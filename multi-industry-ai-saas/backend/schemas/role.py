from typing import Optional, Dict, Any, List, Union
from pydantic import BaseModel, UUID4
from datetime import datetime

# 权限基础模型
class PermissionBase(BaseModel):
    name: str
    code: str
    description: Optional[str] = None
    module: str
    action: str
    resource: str
    is_system_permission: bool = False

# 创建权限请求模型
class PermissionCreate(PermissionBase):
    pass

# 更新权限请求模型
class PermissionUpdate(PermissionBase):
    pass

# 权限响应模型
class Permission(PermissionBase):
    id: UUID4
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        model_config = {"from_attributes": True}

# 角色基础模型
class RoleBase(BaseModel):
    name: str
    code: str
    description: Optional[str] = None
    status: str = "active"
    is_system_role: bool = False

# 创建角色请求模型
class RoleCreate(RoleBase):
    project_id: UUID4
    permissions: Optional[Dict[str, Any]] = None

# 更新角色请求模型
class RoleUpdate(BaseModel):
    name: Optional[str] = None
    code: Optional[str] = None
    description: Optional[str] = None
    status: Optional[str] = None
    permissions: Optional[Dict[str, Any]] = None

# 角色响应模型
class Role(RoleBase):
    id: UUID4
    project_id: UUID4
    permissions: Optional[Dict[str, Any]] = None
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        model_config = {"from_attributes": True}

# 角色列表响应模型
class RoleList(BaseModel):
    items: List[Role]
    total: int
    page: int
    size: int
    pages: int

# 角色权限关联基础模型
class RolePermissionBase(BaseModel):
    role_id: UUID4
    permission_id: UUID4

# 创建角色权限关联请求模型
class RolePermissionCreate(RolePermissionBase):
    pass

# 角色权限关联响应模型
class RolePermission(RolePermissionBase):
    id: UUID4
    created_at: datetime

    class Config:
        model_config = {"from_attributes": True}

# 角色权限批量更新请求模型
class RolePermissionBulkUpdate(BaseModel):
    role_id: UUID4
    permission_ids: List[UUID4]
