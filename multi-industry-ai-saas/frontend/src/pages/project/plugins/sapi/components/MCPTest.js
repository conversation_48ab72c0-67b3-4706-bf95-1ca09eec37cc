import React, { useState, useEffect, useCallback } from 'react';
import { Card, Typography, Select, Button, Space, Input, Tabs, Spin, message, Divider, Table, Tag, Form, Collapse, Alert, Steps, List, Modal, Radio } from 'antd';
import { SendOutlined, ReloadOutlined, CopyOutlined, ApiOutlined, CodeOutlined, HistoryOutlined, CheckCircleOutlined, CloseCircleOutlined, LinkOutlined, ToolOutlined, DisconnectOutlined, KeyOutlined, EyeOutlined, InfoCircleOutlined } from '@ant-design/icons';
import SyntaxHighlighter from 'react-syntax-highlighter';
import { vs2015 } from 'react-syntax-highlighter/dist/esm/styles/hljs';
import ReactJson from 'react-json-view';
import { getAvailableServers } from '../services/mcpService';
import { getProjectId } from '../../../../../services/api/httpClient';

const { Title, Paragraph, Text } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;
const { TextArea } = Input;
const { Panel } = Collapse;
const { Password } = Input;

/**
 * MCP测试组件 - 直接与MCP服务端通信 (StreamHTTP focused)
 */
const MCPTest = () => {
  const [form] = Form.useForm();
  const [tokenForm] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [testLoading, setTestLoading] = useState(false);
  const [servers, setServers] = useState([]);
  const [selectedServer, setSelectedServer] = useState(null);
  const [tools, setTools] = useState([]);
  const [selectedTool, setSelectedTool] = useState(null);
  const [rawTestResult, setRawTestResult] = useState(null);
  const [jsonTestResult, setJsonTestResult] = useState(null);
  const [isStreaming, setIsStreaming] = useState(false);
  const [streamedMessages, setStreamedMessages] = useState([]);

  const [testHistory, setTestHistory] = useState([]);
  const [activeTab, setActiveTab] = useState('1');

  // MCP会话状态
  const [sessionId, setSessionId] = useState(null);
  const [serverInfo, setServerInfo] = useState(null);
  const [capabilities, setCapabilities] = useState(null);
  const [apiToken, setApiToken] = useState("");
  const [mcpServerUrl, setMcpServerUrl] = useState("");

  // For EventSource
  const [eventSource, setEventSource] = useState(null);

  // 生成UUID
  const generateUUID = useCallback(() => {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }, []);

  const [testSteps, setTestSteps] = useState([
    { title: '连接认证', status: 'process', description: '输入JWT并初始化会话', icon: <KeyOutlined /> },
    { title: '获取工具', status: 'wait', description: '请求工具列表', icon: <ToolOutlined /> },
    { title: '调用工具', status: 'wait', description: '执行选定工具', icon: <SendOutlined /> },
    { title: '关闭会话', status: 'wait', description: '发送shutdown请求', icon: <DisconnectOutlined /> }
  ]);
  const [currentStep, setCurrentStep] = useState(0);

  // 加载服务器列表
  useEffect(() => {
    const fetchServers = async () => {
      setLoading(true);
      try {
        const response = await getAvailableServers();
        if (response.success && response.data) {
          setServers(response.data);
          if (response.data.length > 0) {
            setSelectedServer(response.data[0].id);
          }
        } else {
          message.error('获取服务器列表失败: ' + (response.error || '未知错误'));
        }
      } catch (error) {
        console.error('获取服务器列表失败:', error);
        message.error('获取服务器列表失败: ' + error.message);
      } finally {
        setLoading(false);
      }
    };
    fetchServers();
  }, []);

  // Cleanup EventSource on component unmount or when sessionId changes
  useEffect(() => {
    return () => {
      if (eventSource) {
        console.log("Cleaning up EventSource connection.");
        eventSource.close();
        setEventSource(null);
      }
    };
  }, [eventSource]);

  // Helper to send requests via fetch
  const sendMCPRequest = useCallback(async (
    mcpMethod,
    params = {},
    options = {} // { isInitialization: false, explicitToken: null, explicitSessionId: null }
  ) => {
    const {
      isInitialization = false,
      explicitToken = null,
      explicitSessionId = null
    } = options;

    if (!selectedServer && !isInitialization) {
      message.error('请选择一个MCP服务器实例');
      throw new Error('未选择服务器');
    }
    // apiToken from state is the fallback if explicitToken is not provided
    // sessionId from state is the fallback if explicitSessionId is not provided

    const tokenToUse = explicitToken !== null ? explicitToken : apiToken;
    const currentSessionId = explicitSessionId !== null ? explicitSessionId : sessionId;

    const serverTargetId = selectedServer;
    const requestUrl = `${window.location.origin}/mcp/servers/${serverTargetId}/mcp`;

    const requestId = generateUUID();
    const payload = {
        jsonrpc: "2.0",
      id: requestId,
      method: mcpMethod,
      params: params,
    };

    const headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };
    if (tokenToUse) {
      headers['Authorization'] = `Bearer ${tokenToUse}`;
    }
    if (currentSessionId && !isInitialization) {
      headers['mcp-session-id'] = currentSessionId;
    }

    console.log(`发送 MCP 请求 (${mcpMethod}) 到 ${requestUrl}:`, payload, "Headers:", headers);
    setTestLoading(true);
    setRawTestResult(null);
    setJsonTestResult(null);

    try {
      const response = await fetch(requestUrl, {
        method: 'POST',
        headers: headers,
        body: JSON.stringify(payload),
      });

      const responseText = await response.text();
      setRawTestResult(responseText);
      let responseData;
      try {
        responseData = JSON.parse(responseText);
        setJsonTestResult(responseData);
      } catch (e) {
        console.warn("响应不是有效的JSON:", responseText);
        setJsonTestResult({ error: { message: "响应不是有效的JSON", data: responseText } });
      }

      console.log(`MCP 响应 (${mcpMethod}):`, responseData || responseText);

      if (!response.ok) {
        const errorMsg = responseData?.error?.message || responseData?.detail || response.statusText || `HTTP error ${response.status}`;
        message.error(`MCP请求 ${mcpMethod} 失败: ${errorMsg}`);
        if (responseData) {
             setTestHistory(prev => [{ request: payload, response: responseData, timestamp: new Date(), type: mcpMethod }, ...prev].slice(0, 20));
        } else {
             setTestHistory(prev => [{ request: payload, response: {error: {message: errorMsg, details: responseText }}, timestamp: new Date(), type: mcpMethod }, ...prev].slice(0, 20));
        }
        throw new Error(errorMsg);
      }

      if (responseData?.error) {
        message.error(`MCP逻辑错误 (${mcpMethod}): ${responseData.error.message}`);
      }

      setTestHistory(prev => [{ request: payload, response: responseData || { raw: responseText} , timestamp: new Date(), type: mcpMethod }, ...prev].slice(0, 20));
      return responseData;

    } catch (error) {
      console.error(`MCP请求 ${mcpMethod} 捕获错误:`, error);
      setRawTestResult(prev => prev ? prev + `\nError: ${error.message}` : `Error: ${error.message}`);
      setTestHistory(prev => [{ request: payload, response: { error: { message: error.message, details: error.stack } } , timestamp: new Date(), type: mcpMethod }, ...prev].slice(0, 20));
      throw error;
    } finally {
      setTestLoading(false);
    }
  }, [selectedServer, apiToken, sessionId, generateUUID]);

  // 处理连接到MCP服务器 (初始化)
  const handleConnect = async (values) => {
    if (!selectedServer) {
      message.error('请选择服务器');
      return;
    }
    const tokenFromForm = values.apiToken || "";
    setApiToken(tokenFromForm); // Update state for future renders and other handlers

    // Reset previous session state
    if (eventSource) {
        eventSource.close();
        setEventSource(null);
    }
    setSessionId(null);
    setTools([]);
    setSelectedTool(null);
    setServerInfo(null);
    setCapabilities(null);
    setStreamedMessages([]);
    setIsStreaming(false);

    setTestSteps(steps => steps.map((step, index) => ({ ...step, status: index === 0 ? 'process' : 'wait' })));
    setCurrentStep(0);

    const serverDetails = servers.find(s => s.id === selectedServer);
    if (serverDetails) {
        const proxyUrl = `${window.location.origin}/mcp/servers/${selectedServer}/mcp`;
        setMcpServerUrl(proxyUrl);
    }

    try {
      const initParams = {
        protocolVersion: "2025-03-26",
        clientInfo: { name: "MCPTestUI", version: "1.0.0" },
        capabilities: { tools: { listChanged: true } }
      };
      const initData = await sendMCPRequest("initialize", initParams, {
        isInitialization: true,
        explicitToken: tokenFromForm
      });

      if (initData.error) throw new Error(initData.error.message || '初始化响应错误');
      if (!initData.result || !initData.result.sessionId) throw new Error('初始化响应中缺少sessionId');

      const newSessionId = initData.result.sessionId;
      setSessionId(newSessionId);
      setServerInfo(initData.result.serverInfo || { name: serverDetails?.name || "Unknown Server" });
      setCapabilities(initData.result.capabilities || {});

      message.success(`会话初始化成功: ${newSessionId}`);

      await sendMCPRequest("notifications/initialized", {}, {
        isInitialization: false,
        explicitToken: tokenFromForm,
        explicitSessionId: newSessionId
      });
      console.log("notifications/initialized 已发送");

      // 获取流式事件路径，默认为 /mcp
      const streamPath = initData.result?.streamhttp_event_stream_path || "/mcp";
      if (streamPath) {
          const serverTargetId = selectedServer;
          const eventSourceUrl = `${window.location.origin}/mcp/servers/${serverTargetId}${streamPath.startsWith('/') ? streamPath : '/' + streamPath}`;

          console.log("尝试连接到 SSE (StreamHTTP GET) URL:", eventSourceUrl);

          // 准备 EventSource 连接
          try {
              // 关闭现有连接
              if (eventSource) {
                  eventSource.close();
              }

              // 创建 URL 对象，用于添加查询参数
              const url = new URL(eventSourceUrl);

              // 添加会话ID和认证信息作为查询参数，因为某些浏览器不支持在 EventSource 中设置自定义头
              if (newSessionId) {
                  url.searchParams.append('sessionId', newSessionId);
              }
              if (tokenFromForm) {
                  url.searchParams.append('token', tokenFromForm);
              }

              console.log("Connecting to EventSource:", url.toString());
              setIsStreaming(true);
              setStreamedMessages([]);

              // 创建 EventSource 选项
              const eventSourceOptions = {
                  withCredentials: true // 允许发送凭证（如 cookies）
              };

              // 创建 EventSource 实例
              const newEventSource = new EventSource(url.toString(), eventSourceOptions);

              // 设置事件处理程序
              newEventSource.onopen = (event) => {
                  console.log("EventSource connection opened:", event);
                  message.success("MCP事件流已连接");
                  setStreamedMessages(prev => [...prev, {type: 'connection', content: "事件流已连接", timestamp: new Date()}]);
              };

              newEventSource.onerror = (event) => {
                  console.error("EventSource error:", event);
                  message.error("MCP事件流连接错误");
                  setStreamedMessages(prev => [...prev, {type: 'error', content: "事件流连接错误", timestamp: new Date()}]);

                  // 如果连接关闭，更新状态
                  if (newEventSource.readyState === EventSource.CLOSED) {
                      setIsStreaming(false);
                      setEventSource(null);
                  }
              };

              // 处理消息事件
              newEventSource.onmessage = (event) => {
                  console.log("EventSource message:", event);

                  try {
                      const jsonData = JSON.parse(event.data);
                      console.log("Parsed SSE default event:", jsonData);

                      // 处理符合MCP协议标准的通知
                      if (jsonData.method && jsonData.method.startsWith("notifications/") && jsonData.params) {
                          // 提取通知类型
                          const notificationType = jsonData.method.split("/")[1];

                          // 处理工具调用通知
                          if (notificationType === "toolChunk" || notificationType === "toolStatus" || notificationType === "toolError") {
                              // 检查是否有content数组
                              if (jsonData.params.content && Array.isArray(jsonData.params.content)) {
                                  // 将通知添加到消息列表
                                  setStreamedMessages(prev => [
                                      ...prev,
                                      {
                                          type: notificationType,
                                          content: jsonData.params,
                                          timestamp: new Date()
                                      }
                                  ].slice(-100));
                              } else {
                                  // 兼容旧格式，没有content数组
                                  setStreamedMessages(prev => [
                                      ...prev,
                                      {
                                          type: notificationType,
                                          content: jsonData.params,
                                          timestamp: new Date(),
                                          note: "缺少content数组"
                                      }
                                  ].slice(-100));
                              }
                          } else {
                              // 其他类型的通知
                              setStreamedMessages(prev => [
                                  ...prev,
                                  {
                                      type: "message",
                                      content: jsonData,
                                      timestamp: new Date()
                                  }
                              ].slice(-100));
                          }
                      } else {
                          // 非标准格式，直接添加
                          setStreamedMessages(prev => [
                              ...prev,
                              {
                                  type: "message",
                                  content: jsonData,
                                  timestamp: new Date()
                              }
                          ].slice(-100));
                      }
                  } catch (e) {
                      console.warn("Failed to parse SSE data JSON:", event.data, e);
                      setStreamedMessages(prev => [...prev, {type: 'parse_error', content: event.data, timestamp: new Date()}].slice(-100));
                  }
              };

              // 处理特定类型的事件
              newEventSource.addEventListener('toolChunk', (event) => {
                  try {
                      const jsonData = JSON.parse(event.data);
                      console.log("Parsed SSE toolChunk event:", jsonData);
                      setStreamedMessages(prev => [
                          ...prev,
                          {
                              type: 'toolChunk',
                              content: jsonData,
                              timestamp: new Date()
                          }
                      ].slice(-100));
                  } catch (e) {
                      console.warn("Failed to parse SSE toolChunk JSON:", event.data, e);
                  }
              });

              newEventSource.addEventListener('toolStatus', (event) => {
                  try {
                      const jsonData = JSON.parse(event.data);
                      console.log("Parsed SSE toolStatus event:", jsonData);
                      setStreamedMessages(prev => [
                          ...prev,
                          {
                              type: 'toolStatus',
                              content: jsonData,
                              timestamp: new Date()
                          }
                      ].slice(-100));
                  } catch (e) {
                      console.warn("Failed to parse SSE toolStatus JSON:", event.data, e);
                  }
              });

              newEventSource.addEventListener('toolError', (event) => {
                  try {
                      const jsonData = JSON.parse(event.data);
                      console.log("Parsed SSE toolError event:", jsonData);
                      setStreamedMessages(prev => [
                          ...prev,
                          {
                              type: 'toolError',
                              content: jsonData,
                              timestamp: new Date()
                          }
                      ].slice(-100));
                  } catch (e) {
                      console.warn("Failed to parse SSE toolError JSON:", event.data, e);
                  }
              });

              // 保存 EventSource 实例
              setEventSource(newEventSource);

          } catch (err) {
              console.error("Error setting up EventSource:", err);
              message.error("MCP事件流连接失败: " + err.message);
              setIsStreaming(false);
          }
      } else {
          console.warn("StreamHTTP Event Stream Path not provided in initialize response.");
      }

      setTestSteps(steps => steps.map((step, index) => {
        if (index === 0) return { ...step, status: 'finish', description: `会话ID: ${newSessionId.substring(0,8)}...` };
        if (index === 1) return { ...step, status: 'process' };
        return step;
      }));
      setCurrentStep(1);

      } catch (error) {
      console.error('连接MCP服务器失败:', error);
      message.error('连接MCP服务器失败: ' + (error.message || '未知错误'));
      setTestSteps(steps => steps.map((step, index) => (index === 0 ? { ...step, status: 'error', description: error.message } : step)));
    }
  };

  // 处理获取工具列表
  const handleGetTools = async () => {
    if (!sessionId) {
      message.error('请先初始化会话');
      return;
    }
    setTestSteps(steps => steps.map((step, index) => (index === 1 ? { ...step, status: 'process', description: '正在获取工具...' } : step)));
    try {
      const toolsData = await sendMCPRequest("tools/list", {});
      if (toolsData.error) throw new Error(toolsData.error.message || '获取工具列表失败');

      const toolsList = toolsData.result?.tools;
      if (!Array.isArray(toolsList)) {
        console.warn("tools/list 响应格式不正确，tools 字段不是数组:", toolsData.result);
        setTools([]);
        throw new Error("工具列表响应格式不正确");
      }
      setTools(toolsList);
      message.success(`获取到 ${toolsList.length} 个工具`);

      if (toolsList.length > 0) {
        handleToolChange(toolsList[0].name); // Pre-select first tool and set its args
      } else {
        setSelectedTool(null); // No tools, clear selection
        form.resetFields(); // Reset form if no tools
      }

      setTestSteps(steps => steps.map((step, index) => {
        if (index === 1) return { ...step, status: 'finish', description: `获取到 ${toolsList.length} 个工具`};
        // Step 2 will be set to 'process' when user clicks 'Next'
        return step;
      }));
      setCurrentStep(1); // Stay on Step 1 to show the tool list and 'Next' button
    } catch (error) {
      console.error('获取工具列表失败:', error);
      message.error('获取工具列表失败: ' + (error.message || '未知错误'));
      setTestSteps(steps => steps.map((step, index) => (index === 1 ? { ...step, status: 'error', description: error.message } : step)));
    }
  };

  const handleToolChange = (toolName) => {
    setSelectedTool(toolName);
    const currentTool = tools.find(t => t.name === toolName);
    const newCallArgs = {};
    const individualArgs = {};

    if (currentTool?.parameters?.properties) {
      Object.entries(currentTool.parameters.properties).forEach(([key, prop]) => {
        if (prop.hasOwnProperty('default')) {
          individualArgs[key] = prop.default;
          newCallArgs[key] = prop.default;
        } else {
          // Set a sensible default based on type, or undefined to clear
          // For simplicity, we'll set to undefined for now, antd form should handle placeholders
          individualArgs[key] = undefined;
        }
      });
    }

    // Update the main JSON blob for callArgs
    form.setFieldsValue({ callArgs: JSON.stringify(newCallArgs, null, 2) });

    // Update dynamic fields under 'arguments' namespace
    // Ensure 'arguments' is an object, even if empty
    form.setFieldsValue({ arguments: individualArgs });
  };

  // 处理调用工具
  const handleCallTool = async (values) => {
    if (!sessionId) {
      message.error('请先初始化会话');
      return;
    }
    if (!selectedTool) {
      message.error('请选择要调用的工具');
      return;
    }
    setTestSteps(steps => steps.map((step, index) => (index === 2 ? { ...step, status: 'process' } : step)));
    try {
      let parsedArgs = {};
      try {
        parsedArgs = JSON.parse(values.callArgs || '{}');
      } catch (e) {
        message.error('工具参数不是有效的JSON');
        setTestSteps(steps => steps.map((step, index) => (index === 2 ? { ...step, status: 'error', description: '参数JSON无效' } : step)));
        return;
      }

      const callParams = {
        name: selectedTool,
        arguments: parsedArgs,
        stream_mode: true, // Always request stream mode from UI for simplicity, server can ignore if not streamable
      };
      const callData = await sendMCPRequest("tools/call", callParams);
      if (callData.error) {
        throw new Error(callData.error.message || '调用工具失败');
      }

      // 检查MCP协议标准的响应格式
      if (callData.result?.status === "tool_stream_started") {
        // 旧格式，兼容处理
        message.success(`工具 '${selectedTool}' 流式调用已启动。请在事件流区域查看结果。`);
      } else if (callData.result?.content && Array.isArray(callData.result.content)) {
        // 符合MCP协议标准的响应格式
        const isError = callData.result.isError === true;
        const contentItems = callData.result.content;

        if (isError) {
          message.error(`工具 '${selectedTool}' 调用失败: ${contentItems[0]?.text || '未知错误'}`);
        } else {
          message.success(`工具 '${selectedTool}' 调用成功。`);

          // 如果是非流式响应，显示内容
          if (contentItems.length > 0) {
            console.log(`工具 '${selectedTool}' 返回内容:`, contentItems);
          }
        }
      } else {
        // 其他格式，尝试兼容处理
        message.success(`工具 '${selectedTool}' 调用成功 (非标准响应格式)。`);
        console.log(`工具 '${selectedTool}' 返回数据:`, callData.result);
      }

      setTestSteps(steps => steps.map((step, index) => {
        if (index === 2) return { ...step, status: 'finish', description: `工具 ${selectedTool} 调用完成` };
        if (index === 3) return { ...step, status: 'process' };
        return step;
      }));
      setCurrentStep(3);

    } catch (error) {
      console.error('调用工具失败:', error);
      message.error('调用工具失败: ' + (error.message || '未知错误'));
      setTestSteps(steps => steps.map((step, index) => (index === 2 ? { ...step, status: 'error', description: error.message } : step)));
    }
  };

  // 处理关闭会话
  const handleCloseSession = async () => {
    if (!sessionId) {
    }
     setTestSteps(steps => steps.map((step, index) => (index === 3 ? { ...step, status: 'process' } : step)));

    if (sessionId) {
        try {
          await sendMCPRequest("shutdown", {});
          message.success('关闭会话请求已发送');
        } catch (error) {
          console.error('关闭会话失败:', error);
          message.error('关闭会话失败: ' + (error.message || '未知错误'));
        }
    }

    if (eventSource) {
      eventSource.close();
      setEventSource(null);
    }
      setSessionId(null);
      setTools([]);
      setSelectedTool(null);
    setServerInfo(null);
    setCapabilities(null);
    setApiToken("");
    tokenForm.resetFields();
    form.resetFields();
    setStreamedMessages([]);
    setIsStreaming(false);

      setTestSteps(steps => steps.map((step, index) => {
        if (index === 3) return { ...step, status: 'finish', description: '会话已关闭并重置' };
        if (index === 0) return { ...step, status: 'process', description: '输入JWT并初始化会话' };
          return { ...step, status: 'wait' };
        }));
        setCurrentStep(0);
    message.info("测试客户端已重置。");
  };

  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text).then(() => {
        message.success('已复制到剪贴板');
    }, (err) => {
        message.error('复制失败');
      console.error('复制错误:', err);
    });
  };

  const getCurrentTool = () => {
    return tools.find(t => t.name === selectedTool) || null;
  };

  const refreshServers = async () => {
    setLoading(true);
    try {
      const response = await getAvailableServers();
       if (response.success && response.data) {
        setServers(response.data);
        if (response.data.length > 0 && !selectedServer) {
          setSelectedServer(response.data[0].id);
        }
           message.success("服务器列表已刷新");
      } else {
          message.error('刷新服务器列表失败: ' + (response.error || '未知错误'));
      }
    } catch (error) {
      console.error('刷新服务器列表失败:', error);
      message.error('刷新服务器列表失败: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const renderParameterForm = () => {
    const currentTool = getCurrentTool();
    if (!currentTool || !currentTool.parameters || !currentTool.parameters.properties) {
      return <Paragraph>此工具没有定义参数，或参数格式未知。请使用JSON格式输入参数。</Paragraph>;
    }

    const properties = currentTool.parameters.properties;
      return (
      <Form form={form} layout="vertical" onFinish={handleCallTool}>
        {Object.entries(properties).map(([key, prop]) => (
            <Form.Item
            key={key}
            label={`${key} (${prop.type})`}
            name={['arguments', key]}
            rules={currentTool.parameters.required?.includes(key) ? [{ required: true, message: `参数 ${key} 是必需的` }] : []}
            tooltip={prop.description}
          >
            {prop.type === 'string' && <Input placeholder={prop.description} />}
            {prop.type === 'integer' && <Input type="number" placeholder={prop.description} />}
            {prop.type === 'boolean' && <Radio.Group><Radio value={true}>True</Radio><Radio value={false}>False</Radio></Radio.Group>}
            {prop.type === 'object' && <TextArea rows={3} placeholder={`JSON 对象 for ${key}`} />}
            {prop.type === 'array' && <TextArea rows={3} placeholder={`JSON 数组 for ${key}`} />}
            </Form.Item>
        ))}
        <Form.Item label="完整调用参数 (JSON)" name="callArgs" initialValue="{}" rules={[{ required: true, message: '请输入JSON格式的工具参数' }]}>
          <TextArea rows={6} placeholder='{\n  "paramName": "paramValue"\n}' />
        </Form.Item>
        <Form.Item>
          <Button type="primary" htmlType="submit" icon={<SendOutlined />} loading={testLoading} disabled={!sessionId || !selectedTool}>
            调用工具
          </Button>
        </Form.Item>
      </Form>
    );
  };

  const renderTestResult = () => {
    if (testLoading && !isStreaming) return <Spin tip="加载中..." />;
    if (!rawTestResult && streamedMessages.length === 0) return <Paragraph type="secondary">暂无结果</Paragraph>;

    return (
        <>
            {rawTestResult && (
                 <Tabs defaultActiveKey="parsed" size="small">
                    <TabPane tab="解析后 (JSON)" key="parsed">
                        {jsonTestResult ? (
          <ReactJson
                                src={jsonTestResult}
                                theme="rjv-default"
                                collapsed={false}
            displayDataTypes={false}
                                enableClipboard={copy => {
                                    copyToClipboard(JSON.stringify(copy.src, null, 2));
                                }}
                            />
                        ) : <Text type="warning">无法解析为JSON</Text>}
                    </TabPane>
                    <TabPane tab="原始响应" key="raw">
                        <SyntaxHighlighter language="json" style={vs2015} wrapLines={true} showLineNumbers={true}>
                            {String(rawTestResult)}
                        </SyntaxHighlighter>
                    </TabPane>
                </Tabs>
            )}
            {isStreaming && <Paragraph strong>MCP 事件流 (StreamHTTP GET):</Paragraph>}
            {streamedMessages.length > 0 && (
                <List
                    size="small"
                    bordered
                    dataSource={streamedMessages.slice().reverse()}
                    renderItem={item => (
                        <List.Item>
                            <Text type="secondary" style={{fontSize: '0.8em', marginRight: '8px'}}>[{new Date(item.timestamp).toLocaleTimeString()}]</Text>
                            <Tag color={item.type === 'parse_error' ? "error" : "blue"}>{item.type}</Tag>
                            {typeof item.content === 'string' ?
                                <Text code style={{whiteSpace: 'pre-wrap'}}>{item.content}</Text> :
                                <>
                                    {item.note && <Tag color="orange">{item.note}</Tag>}
                                    {/* 特殊处理MCP协议标准的content数组 */}
                                    {item.content && item.content.content && Array.isArray(item.content.content) ? (
                                        <div>
                                            <div style={{marginBottom: '4px'}}>
                                                {item.content.toolName && <Tag color="blue">工具: {item.content.toolName}</Tag>}
                                                {item.content.toolCallId && <Tag color="purple">调用ID: {item.content.toolCallId.substring(0, 8)}...</Tag>}
                                                {item.content.status && <Tag color="green">状态: {item.content.status}</Tag>}
                                            </div>
                                            {item.content.content.map((contentItem, idx) => (
                                                <div key={idx} style={{marginBottom: '4px', border: '1px solid #f0f0f0', padding: '4px', borderRadius: '4px'}}>
                                                    <Tag color="cyan">{contentItem.type || 'unknown'}</Tag>
                                                    {contentItem.type === 'text' ? (
                                                        <Text code style={{whiteSpace: 'pre-wrap'}}>{contentItem.text}</Text>
                                                    ) : (
                                                        <ReactJson src={contentItem} collapsed={1} name={false} displayDataTypes={false} enableClipboard={false} theme="rjv-default" />
                                                    )}
                                                </div>
                                            ))}
                                        </div>
                                    ) : (
                                        <ReactJson src={item.content} collapsed={1} name={false} displayDataTypes={false} enableClipboard={false} theme="rjv-default" />
                                    )}
                                </>
                            }
                        </List.Item>
                    )}
                    style={{maxHeight: '300px', overflowY: 'auto', marginTop: '10px', background: '#f9f9f9'}}
                />
            )}
             {isStreaming && <Spin size="small" tip="事件流监听中..." style={{marginTop: '5px'}}/>}
        </>
    );
  };

  const historyColumns = [
    { title: '时间', dataIndex: 'timestamp', key: 'timestamp', render: ts => new Date(ts).toLocaleString(), width: 180 },
    { title: '类型', dataIndex: 'type', key: 'type', width: 150, render: type => <Tag>{type}</Tag> },
    { title: '请求', dataIndex: 'request', key: 'request', render: req => <ReactJson src={req} collapsed={1} name={false} theme="rjv-default" displayDataTypes={false} /> },
    { title: '响应', dataIndex: 'response', key: 'response', render: res => <ReactJson src={res} collapsed={1} name={false} theme="rjv-default" displayDataTypes={false} /> },
  ];

  const renderHistory = () => {
    return (
      <Table
        columns={historyColumns}
        dataSource={testHistory}
        rowKey="timestamp"
        pagination={{ pageSize: 5 }}
        size="small"
        scroll={{ y: 400 }}
      />
    );
  };

  const pythonStreamHttpExample = `
import requests
import json
import uuid

# --- 配置 ---
BASE_URL = "${mcpServerUrl || 'http://localhost:8000/mcp/servers/YOUR_SERVER_ID/mcp'}" # 替换为您的MCP代理URL
JWT_TOKEN = "${apiToken || 'YOUR_JWT_TOKEN'}" # 替换为您的JWT令牌
MCP_SESSION_ID = None # 将在初始化后设置

# --- 辅助函数 ---
def generate_uuid():
    return str(uuid.uuid4())

def send_mcp_request(method, params=None, session_id_header=None):
    url = BASE_URL
    request_id = generate_uuid()
    payload = {
    "jsonrpc": "2.0",
        "id": request_id,
        "method": method,
        "params": params if params is not None else {}
    }

    headers = {
        "Content-Type": "application/json",
        "Accept": "application/json",
        "Authorization": f"Bearer {JWT_TOKEN}"
    }
    if session_id_header:
        headers["mcp-session-id"] = session_id_header

    print(f"--> {method} (ID: {request_id})")
    # print(f"    Payload: {json.dumps(payload)}")
    # print(f"    Headers: {headers}")

    try:
        response = requests.post(url, json=payload, headers=headers)
        response.raise_for_status() # 如果HTTP状态码是4xx/5xx，则抛出异常
        response_data = response.json()
        print(f"<-- Response (ID: {request_id}):")
        print(json.dumps(response_data, indent=2))
        return response_data
    except requests.exceptions.HTTPError as http_err:
        print(f"HTTP error occurred: {http_err}")
        print(f"Response body: {response.text}")
    except Exception as err:
        print(f"Other error occurred: {err}")
    return None

def stream_mcp_events(session_id_to_use, stream_path="/mcp"):
    if not session_id_to_use:
        print("错误: SSE流需要会话ID")
        return

    # 假设代理的 /mcp GET 端点可以基于 Authorization (JWT) 和 mcp-session-id header 建立事件流
    # 注意: requests库本身不直接处理EventSource，这是一个简化的示例，可能需要使用 sseclient-py 或其他库
    # 或者直接使用 fetch API 的 ReadableStream (如果是在支持的环境中)

    stream_url = BASE_URL # GET 请求也发送到同一个 /mcp 路径
    if stream_path and stream_path != "/mcp": # If initialize returned a specific path
        # This logic needs to correctly reconstruct the full stream URL if different from base
        # For now, assume GET is on BASE_URL (/mcp)
        pass

    print(f"\n--- 开始监听MCP事件流 (mcp-session-id: {session_id_to_use}) ---")
    print(f"    GET {stream_url}")

headers = {
        "Accept": "text/event-stream",
        "Authorization": f"Bearer {JWT_TOKEN}",
        "mcp-session-id": session_id_to_use
    }

    try:
        # 使用 requests.get(stream=True) 来模拟流式接收
        with requests.get(stream_url, headers=headers, stream=True, timeout=300) as r:
            r.raise_for_status()
            print(f"    SSE 连接成功，状态码: {r.status_code}")
            for line_bytes in r.iter_lines():
                if line_bytes:
                    line = line_bytes.decode('utf-8')
                    print(f"    Raw SSE Line: {line}")
                    # 简单的SSE解析 (生产代码应使用更健壮的解析器)
                    if line.startswith("event:"):
                        current_event_type = line.split(":", 1)[1].strip()
                        print(f"    Event Type: {current_event_type}")
                    elif line.startswith("data:"):
                        current_event_data_str = line.split(":", 1)[1].strip()
                        try:
                            current_event_data_json = json.loads(current_event_data_str)
                            print(f"    Event Data: {json.dumps(current_event_data_json, indent=2)}")
                        except json.JSONDecodeError:
                            print(f"    Event Data (not JSON): {current_event_data_str}")
                    elif line.strip() == "": # 空行表示事件结束
                        print("    --- End of Event ---")

    except requests.exceptions.RequestException as e:
        print(f"    SSE 连接或流式传输错误: {e}")
    except KeyboardInterrupt:
        print("\n    用户中断了事件流监听。")
    finally:
        print("--- 事件流监听结束 ---")


# --- 主逻辑 ---
if __name__ == "__main__":
    # 1. 初始化
    init_params = {
        "protocolVersion": "2025-03-26",
        "clientInfo": {"name": "Python MCPTest Client", "version": "1.0"},
        "capabilities": {}
    }
    init_response = send_mcp_request("initialize", init_params)

    if init_response and init_response.get("result") and init_response["result"].get("sessionId"):
        MCP_SESSION_ID = init_response["result"]["sessionId"]
        print(f"\n会话初始化成功，Session ID: {MCP_SESSION_ID}")

        # (可选) 启动一个线程/异步任务来监听事件流
        # import threading
        # stream_thread = threading.Thread(target=stream_mcp_events, args=(MCP_SESSION_ID,))
        # stream_thread.daemon = True # 允许主程序退出时线程也退出
        # stream_thread.start()
        # print("\n(事件流监听已在后台启动，如果服务器发送事件，将会打印)")


        # 2. 发送 initialized 通知 (StreamHTTP不需要sessionId在params中)
        send_mcp_request("notifications/initialized", params={}, session_id_header=MCP_SESSION_ID)

        # 3. 获取工具列表 (StreamHTTP不需要sessionId在params中)
        tools_response = send_mcp_request("tools/list", params={}, session_id_header=MCP_SESSION_ID)

        available_tools = []
        if tools_response and tools_response.get("result") and tools_response["result"].get("tools"):
            available_tools = tools_response["result"]["tools"]
            print(f"\n获取到 {len(available_tools)} 个工具。")
            if available_tools:
                print("可用工具:")
                for tool in available_tools:
                    print(f"  - {tool.get('name')}")

        # 4. 调用工具 (示例 - 替换为实际工具名和参数)
        if available_tools:
            tool_to_call = "${selectedTool || (tools.length > 0 ? tools[0].name : 'YOUR_TOOL_NAME')}" # 工具名
            tool_args_str = form.getFieldValue('callArgs') || '{}';
            tool_arguments = {}
            try:
                tool_arguments = json.loads(tool_args_str)
            except Exception:
                print(f"警告: 前端工具参数不是有效的JSON: {tool_args_str}，使用空参数。")


            print(f"\n准备调用工具: {tool_to_call}，参数: {tool_arguments}")
            call_params = {
                "name": tool_to_call,
                "arguments": tool_arguments,
                "stream_mode": True # 请求流式处理
            }
            # 工具调用后，结果会通过上面启动的 stream_mcp_events 打印（如果工具流式返回）
            # POST的直接响应只是一个确认
            tool_call_ack = send_mcp_request("tools/call", call_params, session_id_header=MCP_SESSION_ID)
            if tool_call_ack and tool_call_ack.get("result", {}).get("status") == "tool_stream_started":
                print(f"工具 '{tool_to_call}' 流式调用已启动。事件将通过GET流接收。")
                print("请等待事件流输出 (可能需要几秒钟或更长时间，取决于工具执行)...")
                # 在实际应用中，你可能需要等待 stream_thread 完成或提供一种方式来优雅地停止它。
                # for _ in range(10): # 等待一小段时间让流式事件发生
                #    time.sleep(1)
                #    if not stream_thread.is_alive(): break
            elif tool_call_ack and "result" in tool_call_ack:
                 print(f"工具 '{tool_to_call}' 非流式调用成功。")
            else:
                print(f"工具 '{tool_to_call}' 调用可能失败或响应格式未知。")


        # 5. 关闭会话 (StreamHTTP不需要sessionId在params中)
        input("\n按回车键关闭会话并退出...") # 等待用户操作，以便观察流式输出
        send_mcp_request("shutdown", params={}, session_id_header=MCP_SESSION_ID)
        print("\n会话关闭请求已发送。")

        # if stream_thread.is_alive():
        #    print("等待事件流线程结束...")
        #    # stream_thread.join(timeout=5) # 等待线程结束
    else:
        print("\n会话初始化失败，请检查URL、JWT令牌和服务器状态。")

`;

  const renderConnectForm = () => (
    <Form form={tokenForm} layout="vertical" onFinish={handleConnect} initialValues={{ apiToken: apiToken }}>
        <Form.Item
        label="选择MCP服务器实例"
        required
        tooltip="这些是已在系统中注册并可用的MCP服务器实例。"
      >
        <Space>
          <Select
            value={selectedServer}
            onChange={setSelectedServer}
            style={{ width: 300 }}
            loading={loading}
            placeholder="请选择一个服务器"
            disabled={testLoading}
          >
            {servers.map(s => <Option key={s.id} value={s.id}>{s.name} (ID: ...{s.id.slice(-6)})</Option>)}
          </Select>
          <Button icon={<ReloadOutlined />} onClick={refreshServers} loading={loading} disabled={testLoading}/>
        </Space>
        </Form.Item>
        <Form.Item
        label="后端API认证令牌 (JWT)"
          name="apiToken"
        rules={[{ required: true, message: '请输入JWT令牌以通过代理认证' }]}
        tooltip="此令牌用于向主后端API代理验证您的身份，然后代理会将请求安全地转发到选定的MCP服务器实例。"
      >
        <Input.Password placeholder="粘贴您的JWT令牌" style={{ maxWidth: 600 }} disabled={testLoading}/>
        </Form.Item>
        <Form.Item>
        <Button type="primary" htmlType="submit" icon={<ApiOutlined />} loading={testLoading || (currentStep === 0 && testSteps[0].status === 'process' && testLoading)} disabled={!selectedServer || (testLoading && currentStep !== 0 )}>
          {sessionId ? '重新初始化会话' : '初始化会话'}
          </Button>
        {sessionId && (
          <Button danger onClick={handleCloseSession} style={{ marginLeft: 8 }} icon={<DisconnectOutlined />} loading={testLoading && currentStep === 3}>
            关闭会话并重置
          </Button>
        )}
        </Form.Item>
      {mcpServerUrl && sessionId && (
        <Paragraph copyable={{ text: mcpServerUrl }}>
            当前会话代理URL: <Text code>{mcpServerUrl}</Text>
        </Paragraph>
      )}
      </Form>
    );

  const renderToolsList = () => {
    if (!sessionId) return <Alert message="请先初始化会话以获取工具列表。" type="info" showIcon/>;
    if (testLoading && currentStep === 1 && testSteps[1].status === 'process') return <Spin tip="正在获取工具列表..." />;
    if (tools.length === 0 && testSteps[1].status === 'finish') return <Alert message="此服务器当前没有可用的工具。" type="warning" showIcon />;
    if (tools.length === 0 && testSteps[1].status !== 'process') return <Paragraph type="secondary">点击下方按钮获取工具列表。</Paragraph>;

    return (
        <Space direction="vertical" style={{ width: '100%' }}>
        <Select
          showSearch
          value={selectedTool}
          onChange={handleToolChange}
          style={{ width: '100%' }}
          placeholder="选择一个工具"
          disabled={!tools.length}
          filterOption={(input, option) =>
            option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
          }
        >
          {tools.map(tool => (
            <Option key={tool.name} value={tool.name}>{tool.name}</Option>
          ))}
        </Select>
        {selectedTool && getCurrentTool() && (
          <Card size="small" title={`工具详情: ${selectedTool}`} extra={<Tag color={getCurrentTool()?.supports_streaming ? "green" : "orange"}>{getCurrentTool()?.supports_streaming ? "支持流式" : "不支持流式"}</Tag>}>
            <Paragraph ellipsis={{ rows: 3, expandable: true, symbol: '更多' }}>
              {getCurrentTool().description || "无描述"}
            </Paragraph>
            <Collapse ghost size="small">
              <Panel header="参数 Schema (JSON)" key="1">
                <ReactJson src={getCurrentTool().parameters || {}} name={false} collapsed={true} displayDataTypes={false} theme="rjv-default"/>
              </Panel>
              <Panel header="完整 Schema (JSON)" key="2">
                 <ReactJson src={getCurrentTool()} name={false} collapsed={true} displayDataTypes={false} theme="rjv-default"/>
              </Panel>
            </Collapse>
          </Card>
        )}
          </Space>
    );
  };

  const renderStepContent = (stepIndex) => {
    switch (stepIndex) {
      case 0:
        return (
          <>
            {renderConnectForm()}
            {serverInfo && (
              <Card size="small" title="服务器信息" style={{marginTop: 16}}>
                <ReactJson src={{serverInfo, capabilities}} name={false} collapsed={true} displayDataTypes={false} theme="rjv-default"/>
              </Card>
            )}
          </>
        );
      case 1:
        return (
          <>
                        <Button
                onClick={handleGetTools}
                icon={<ReloadOutlined />}
                loading={testLoading && currentStep === 1 && testSteps[1].status === 'process'}
                disabled={!sessionId || (testLoading && currentStep !== 1)}
                style={{marginBottom: 16}}
            >
              {tools.length > 0 ? '刷新工具列表' : '获取工具列表'}
                        </Button>
            {renderToolsList()}
            {tools.length > 0 && selectedTool && (
                  <Button
                    type="primary"
                style={{ marginTop: 16 }}
                    onClick={() => {
                  setTestSteps(steps => steps.map((s, i) => {
                    if (i === 1 && s.status === 'finish') return { ...s }; // Keep step 1 as finish
                    if (i === 2) return { ...s, status: 'process', description: `为 '${selectedTool}' 配置参数` };
                    return s;
                  }));
                      setCurrentStep(2);
                    }}
                  >
                下一步：为工具 '{selectedTool}' 配置参数
                  </Button>
            )}
          </>
        );
      case 2:
        return (
          <>
            {!tools.length && testSteps[1].status === 'finish' ? (
                <Alert message="没有可调用的工具。请返回上一步或检查服务器配置。" type="warning" showIcon />
            ) : (
                 renderParameterForm()
            )}
          </>
        );
      case 3:
        return (
          <Paragraph>
            所有操作已完成或已重置。您可以关闭此会话，或返回第一步以相同的JWT令牌或新的令牌开始新的测试会话。
            <br />
            <Button danger onClick={handleCloseSession} icon={<DisconnectOutlined />} style={{marginTop: 16}} loading={testLoading && currentStep === 3}>
                {sessionId ? "确认关闭当前会话并重置" : "重置测试客户端"}
            </Button>
          </Paragraph>
        );
      default:
        return <Paragraph>未知步骤</Paragraph>;
    }
  };

  return (
    <Card title={<><InfoCircleOutlined style={{marginRight: 8}} />MCP (StreamHTTP) 测试客户端</>} bordered={false}>
      <Paragraph type="secondary">
        此页面允许您直接与通过主后端代理的MCP服务器实例进行交互。它遵循MCP的 StreamHTTP 通信模式。
        您需要一个有效的JWT令牌才能通过代理向MCP服务器发送请求。
      </Paragraph>
      <Divider />

      <Steps current={currentStep} size="small" style={{marginBottom: 24}}>
        {testSteps.map((step, index) => (
          <Steps.Step key={index} title={step.title} status={step.status} icon={step.icon} description={step.description}/>
        ))}
      </Steps>

      <div style={{ padding: '20px', background: '#f9f9f9', borderRadius: '4px', minHeight: '200px' }}>
        {renderStepContent(currentStep)}
              </div>

      <Divider />

          <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="测试结果" key="1" icon={<CodeOutlined />}>
              {renderTestResult()}
            </TabPane>
        <TabPane tab="请求历史" key="2" icon={<HistoryOutlined />}>
              {renderHistory()}
            </TabPane>
        <TabPane tab="Python 客户端示例 (StreamHTTP)" key="3" icon={<CodeOutlined />}>
          <Paragraph copyable={{ text: pythonStreamHttpExample }}>
            <SyntaxHighlighter language="python" style={vs2015} wrapLines={true} showLineNumbers={true}>
              {pythonStreamHttpExample.trim()}
            </SyntaxHighlighter>
          </Paragraph>
        </TabPane>
          </Tabs>
    </Card>
  );
};

export default MCPTest;
