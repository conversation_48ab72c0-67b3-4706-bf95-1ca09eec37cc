import React, { useState, useEffect } from 'react';
import {
  Modal, Form, Input, Select, DatePicker, Button, Table, InputNumber,
  Space, message, Spin, Divider, Row, Col, Typography, Card, Tag
} from 'antd';
import {
  PlusOutlined, MinusCircleOutlined, SearchOutlined,
  DeleteOutlined, SaveOutlined
} from '@ant-design/icons';
import dayjs from 'dayjs';
import debounce from 'lodash/debounce';
import apiService from '../../../../services/api';

const { Option } = Select;
const { Text } = Typography;

/**
 * 新增到货组件
 */
const NewArrival = ({ visible, onCancel, onSuccess, currentStoreId }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [suppliers, setSuppliers] = useState([]);
  const [products, setProducts] = useState([]);
  const [searchingSuppliers, setSearchingSuppliers] = useState(false);
  const [searchingProducts, setSearchingProducts] = useState(false);
  const [selectedSupplier, setSelectedSupplier] = useState(null);
  const [arrivalItems, setArrivalItems] = useState([]);

  // 初始化表单
  useEffect(() => {
    if (visible) {
      form.resetFields();
      form.setFieldsValue({
        arrival_type: 'third_party',
        arrival_date: dayjs(),
        items: [{}]
      });
      setArrivalItems([]);
    }
  }, [visible, form]);

  // 搜索供应商
  const searchSuppliers = debounce(async (keyword) => {
    if (!keyword || keyword.length < 2) {
      return;
    }

    setSearchingSuppliers(true);
    try {
      const response = await apiService.project.store_operations.getSuppliers({ keyword });
      if (response && response.success) {
        setSuppliers(response.items || []);
      } else {
        message.error(response?.message || '获取供应商列表失败');
      }
    } catch (error) {
      console.error('获取供应商列表失败:', error);
    } finally {
      setSearchingSuppliers(false);
    }
  }, 500);

  // 搜索产品
  const searchProducts = debounce(async (keyword) => {
    if (!keyword || keyword.length < 2) {
      return;
    }

    setSearchingProducts(true);
    try {
      const params = { keyword };
      if (selectedSupplier) {
        params.supplier_id = selectedSupplier;
      }

      const response = await apiService.project.store_operations.getProducts(params);
      if (response && response.success) {
        setProducts(response.items || []);
      } else {
        message.error(response?.message || '获取产品列表失败');
      }
    } catch (error) {
      console.error('获取产品列表失败:', error);
    } finally {
      setSearchingProducts(false);
    }
  }, 500);

  // 处理供应商选择
  const handleSupplierChange = (value) => {
    setSelectedSupplier(value);
    // 清空产品列表
    setProducts([]);
  };

  // 处理产品选择
  const handleProductSelect = (value, option, fieldKey) => {
    const product = products.find(p => p.id === value);
    if (product) {
      const items = form.getFieldValue('items') || [];
      items[fieldKey] = {
        ...items[fieldKey],
        product_id: value,
        product_name: product.name,
        product_code: product.code,
        specification: product.specification,
        unit: product.unit,
        price: product.price,
        quantity: items[fieldKey]?.quantity || 1
      };
      form.setFieldsValue({ items });

      // 更新金额
      updateItemAmount(fieldKey, items[fieldKey].quantity, product.price);
    }
  };

  // 更新商品金额
  const updateItemAmount = (index, quantity, price) => {
    const items = form.getFieldValue('items') || [];
    const amount = (quantity || 0) * (price || 0);
    items[index] = {
      ...items[index],
      amount
    };
    form.setFieldsValue({ items });
  };

  // 处理数量变化
  const handleQuantityChange = (value, fieldKey) => {
    const items = form.getFieldValue('items') || [];
    const price = items[fieldKey]?.price || 0;
    updateItemAmount(fieldKey, value, price);
  };

  // 处理价格变化
  const handlePriceChange = (value, fieldKey) => {
    const items = form.getFieldValue('items') || [];
    const quantity = items[fieldKey]?.quantity || 0;
    updateItemAmount(fieldKey, quantity, value);
  };

  // 提交表单
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();

      if (!values.items || values.items.length === 0) {
        message.warning('请添加至少一个商品');
        return;
      }

      setLoading(true);

      // 准备提交数据
      const arrivalData = {
        store_id: currentStoreId,
        arrival_type: values.arrival_type,
        supplier_id: values.supplier_id,
        arrival_date: values.arrival_date.format('YYYY-MM-DD'),
        notes: values.notes,
        items: values.items.map(item => ({
          product_id: item.product_id,
          product_name: item.product_name,
          product_code: item.product_code,
          specification: item.specification,
          unit: item.unit,
          expected_quantity: item.quantity,
          actual_quantity: item.quantity,
          price: item.price,
          amount: item.amount,
          is_confirmed: true
        }))
      };

      // 调用API创建到货确认
      const response = await apiService.project.store_operations.createArrival(arrivalData);

      if (response && response.success) {
        message.success('创建到货确认成功');
        onSuccess();
      } else {
        message.error(response?.message || '创建到货确认失败');
      }
    } catch (error) {
      console.error('创建到货确认失败:', error);
      message.error('创建到货确认失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      title="新增到货"
      open={visible}
      onCancel={onCancel}
      width={800}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          取消
        </Button>,
        <Button key="submit" type="primary" loading={loading} onClick={handleSubmit}>
          提交
        </Button>
      ]}
    >
      <Spin spinning={loading}>
        <Form
          form={form}
          layout="vertical"
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="arrival_type"
                label="到货类型"
                rules={[{ required: true, message: '请选择到货类型' }]}
              >
                <Select>
                  <Option value="market">市场采购</Option>
                  <Option value="warehouse">仓库配货</Option>
                  <Option value="third_party">三方配送</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="arrival_date"
                label="到货日期"
                rules={[{ required: true, message: '请选择到货日期' }]}
              >
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="supplier_id"
            label="供应商"
            rules={[{ required: true, message: '请选择供应商' }]}
          >
            <Select
              showSearch
              placeholder="搜索供应商"
              filterOption={false}
              onSearch={searchSuppliers}
              onChange={handleSupplierChange}
              loading={searchingSuppliers}
              notFoundContent={searchingSuppliers ? <Spin size="small" /> : null}
            >
              {suppliers.map(supplier => (
                <Option key={supplier.id} value={supplier.id}>
                  {supplier.name} {supplier.code ? `(${supplier.code})` : ''}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Divider orientation="left">商品明细</Divider>

          <Form.List name="items">
            {(fields, { add, remove }) => (
              <>
                {fields.map(({ key, name, ...restField }) => (
                  <Row key={key} gutter={8} style={{ marginBottom: 16 }}>
                    <Col span={8}>
                      <Form.Item
                        {...restField}
                        name={[name, 'product_id']}
                        rules={[{ required: true, message: '请选择商品' }]}
                      >
                        <Select
                          showSearch
                          placeholder="搜索商品"
                          filterOption={false}
                          onSearch={searchProducts}
                          onChange={(value, option) => handleProductSelect(value, option, name)}
                          loading={searchingProducts}
                          notFoundContent={searchingProducts ? <Spin size="small" /> : null}
                        >
                          {products.map(product => (
                            <Option key={product.id} value={product.id}>
                              {product.name} {product.code ? `(${product.code})` : ''}
                            </Option>
                          ))}
                        </Select>
                      </Form.Item>
                    </Col>
                    <Col span={4}>
                      <Form.Item
                        {...restField}
                        name={[name, 'specification']}
                      >
                        <Input placeholder="规格" disabled />
                      </Form.Item>
                    </Col>
                    <Col span={3}>
                      <Form.Item
                        {...restField}
                        name={[name, 'quantity']}
                        rules={[{ required: true, message: '请输入数量' }]}
                      >
                        <InputNumber
                          placeholder="数量"
                          min={1}
                          style={{ width: '100%' }}
                          onChange={(value) => handleQuantityChange(value, name)}
                        />
                      </Form.Item>
                    </Col>
                    <Col span={2}>
                      <Form.Item
                        {...restField}
                        name={[name, 'unit']}
                      >
                        <Input placeholder="单位" disabled />
                      </Form.Item>
                    </Col>
                    <Col span={3}>
                      <Form.Item
                        {...restField}
                        name={[name, 'price']}
                        rules={[{ required: true, message: '请输入单价' }]}
                      >
                        <InputNumber
                          placeholder="单价"
                          min={0}
                          step={0.01}
                          style={{ width: '100%' }}
                          onChange={(value) => handlePriceChange(value, name)}
                        />
                      </Form.Item>
                    </Col>
                    <Col span={3}>
                      <Form.Item
                        {...restField}
                        name={[name, 'amount']}
                      >
                        <InputNumber
                          placeholder="金额"
                          disabled
                          style={{ width: '100%' }}
                        />
                      </Form.Item>
                    </Col>
                    <Col span={1}>
                      <Button
                        type="link"
                        danger
                        icon={<DeleteOutlined />}
                        onClick={() => remove(name)}
                      />
                    </Col>
                  </Row>
                ))}
                <Form.Item>
                  <Button
                    type="dashed"
                    onClick={() => add()}
                    block
                    icon={<PlusOutlined />}
                  >
                    添加商品
                  </Button>
                </Form.Item>
              </>
            )}
          </Form.List>

          <Form.Item name="notes" label="备注">
            <Input.TextArea rows={3} placeholder="请输入备注信息" />
          </Form.Item>
        </Form>
      </Spin>
    </Modal>
  );
};

export default NewArrival;
