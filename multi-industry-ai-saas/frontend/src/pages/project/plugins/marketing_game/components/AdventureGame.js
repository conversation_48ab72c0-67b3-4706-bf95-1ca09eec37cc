import React, { useState, useEffect, useRef } from 'react';
import { useParams } from 'react-router-dom';
import {
  Card,
  Button,
  message,
  Spin,
  Typography,
  Row,
  Col,
  Modal,
  Divider,
  List,
  Avatar,
  Tag,
  Result,
  Space,
  Statistic,
  Switch,
  Progress,
  Steps
} from 'antd';
import {
  UserOutlined,
  HistoryOutlined,
  ShareAltOutlined,
  SmileOutlined,
  MehOutlined,
  LoadingOutlined,
  SoundOutlined,
  RightOutlined,
  StarOutlined,
  FlagFilled,
  TrophyOutlined
} from '@ant-design/icons';
import apiService from '../service/api/marketingGame';
import './AdventureGame.css';

const { Title, Paragraph, Text } = Typography;
const { Step } = Steps;

// 音效资源
const AUDIO = {
  CLICK: '/assets/audio/click.mp3',
  WIN: '/assets/audio/win.mp3',
  LOSE: '/assets/audio/lose.mp3',
  LEVEL_UP: '/assets/audio/level-up.mp3',
  GAME_OVER: '/assets/audio/game-over.mp3'
};

// 默认背景图片
const DEFAULT_BACKGROUNDS = [
  '/assets/images/backgrounds/bg1.jpg',
  '/assets/images/backgrounds/bg2.jpg',
  '/assets/images/backgrounds/bg3.jpg',
  '/assets/images/backgrounds/bg4.jpg',
  '/assets/images/backgrounds/bg5.jpg'
];

// 默认奖品图标
const DEFAULT_PRIZE_ICONS = {
  first: '/assets/images/prizes/first-prize.png',
  second: '/assets/images/prizes/second-prize.png',
  third: '/assets/images/prizes/third-prize.png',
  lucky: '/assets/images/prizes/lucky-prize.png',
  participation: '/assets/images/prizes/participation-prize.png',
  none: '/assets/images/prizes/no-prize.png'
};

// 改进音频播放函数，添加错误捕获
const playAudio = (audioPath, volume = 0.5) => {
  try {
    const audio = new Audio(audioPath);
    audio.volume = volume;
    // 静默处理音频播放失败，避免中断主流程
    audio.play().catch(error => {
      console.warn('音频播放失败:', error);
      // 失败不中断，继续返回音频对象
    });
    return audio;
  } catch (error) {
    console.warn('创建音频对象失败:', error);
    // 返回一个模拟对象，使调用方代码不出错
    return { pause: () => {} };
  }
};

// 示例关卡数据
const DEFAULT_LEVELS = [
  {
    id: 1,
    name: '初级挑战',
    description: '完成简单的问答题，获得进入下一关的资格',
    type: 'quiz',
    questions: [
      {
        id: 'q1',
        text: '以下哪个是我们品牌的标志颜色？',
        options: ['红色', '蓝色', '绿色', '黄色'],
        answer: 0
      },
      {
        id: 'q2',
        text: '我们的旗舰店位于哪个城市？',
        options: ['北京', '上海', '广州', '深圳'],
        answer: 1
      }
    ],
    required_score: 1,
    reward: {
      points: 10,
      description: '获得10积分'
    }
  },
  {
    id: 2,
    name: '中级挑战',
    description: '完成产品知识测试，展示你对我们产品的了解',
    type: 'quiz',
    questions: [
      {
        id: 'q3',
        text: '我们的明星产品有哪些特点？',
        options: ['价格实惠', '质量上乘', '设计新颖', '以上都是'],
        answer: 3
      },
      {
        id: 'q4',
        text: '我们的产品保修期是多久？',
        options: ['6个月', '1年', '2年', '3年'],
        answer: 2
      },
      {
        id: 'q5',
        text: '以下哪项不是我们提供的服务？',
        options: ['免费安装', '上门维修', '终身质保', '7天无理由退换'],
        answer: 2
      }
    ],
    required_score: 2,
    reward: {
      points: 20,
      description: '获得20积分'
    }
  },
  {
    id: 3,
    name: '高级挑战',
    description: '完成最终挑战，赢取大奖！',
    type: 'quiz',
    questions: [
      {
        id: 'q6',
        text: '我们的品牌创立于哪一年？',
        options: ['1998年', '2005年', '2010年', '2015年'],
        answer: 1
      },
      {
        id: 'q7',
        text: '以下哪个不是我们的合作伙伴？',
        options: ['阿里巴巴', '腾讯', '百度', '字节跳动'],
        answer: 3
      },
      {
        id: 'q8',
        text: '我们的企业使命是什么？',
        options: ['创造价值', '服务客户', '引领行业', '以上都是'],
        answer: 3
      },
      {
        id: 'q9',
        text: '我们最新推出的产品系列名称是？',
        options: ['未来星', '智慧家', '创新者', '领航者'],
        answer: 1
      }
    ],
    required_score: 3,
    reward: {
      points: 50,
      description: '获得50积分和抽奖机会'
    }
  }
];

const AdventureGame = () => {
  const { gameId } = useParams();
  const [game, setGame] = useState(null);
  const [prizes, setPrizes] = useState([]);
  const [records, setRecords] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showRules, setShowRules] = useState(false);
  const [showRecords, setShowRecords] = useState(false);
  const [showPrizes, setShowPrizes] = useState(false);
  const [soundEnabled, setSoundEnabled] = useState(true);
  const [currentLevel, setCurrentLevel] = useState(0);
  const [levels, setLevels] = useState([]);
  const [gameStarted, setGameStarted] = useState(false);
  const [gameCompleted, setGameCompleted] = useState(false);
  const [currentQuestion, setCurrentQuestion] = useState(0);
  const [score, setScore] = useState(0);
  const [totalScore, setTotalScore] = useState(0);
  const [selectedAnswer, setSelectedAnswer] = useState(null);
  const [answerSubmitted, setAnswerSubmitted] = useState(false);
  const [showResult, setShowResult] = useState(false);
  const [winningPrize, setWinningPrize] = useState(null);

  const audioRef = useRef(null);

  // 将fetchGameData提升到组件作用域
  const fetchGameData = React.useCallback(async () => {
    try {
      setLoading(true);

      // 获取游戏详情
      const gameResponse = await apiService.getGameDetail(gameId);
      if (!gameResponse.success) {
        message.error(gameResponse.message || '获取游戏详情失败');
        setLoading(false);
        return;
      }

      // 确保game.remaining_draws有默认值
      const gameData = {
        ...gameResponse.data,
        remaining_draws: gameResponse.data.remaining_draws ||
                       gameResponse.data.max_draws_per_user ||
                       0,
        // 添加默认配置
        settings: {
          ...gameResponse.data.settings,
          enable_receipt_verification:
            gameResponse.data.settings?.enable_receipt_verification !== undefined
              ? gameResponse.data.settings.enable_receipt_verification
              : gameResponse.data.min_consumption > 0
        }
      };

      setGame(gameData);

      // 获取奖品列表
      const prizesResponse = await apiService.getPrizes(gameId);
      if (!prizesResponse.success) {
        message.error(prizesResponse.message || '获取奖品列表失败');
        setLoading(false);
        return;
      }

      // 确保至少有一个默认奖品（未中奖）
      let prizesList = prizesResponse.data || [];
      if (!prizesList.some(p => p.is_default)) {
        prizesList.push({
          id: 'default',
          name: '谢谢参与',
          description: '下次再来',
          image_url: DEFAULT_PRIZE_ICONS.none,
          probability: 0,
          is_default: true,
          remaining: 999999
        });
      }

      setPrizes(prizesList);

      // 获取最近中奖记录
      const recordsResponse = await apiService.getRecords(gameId, { page: 1, page_size: 10 });
      if (recordsResponse.success) {
        setRecords(recordsResponse.data?.items || []);
      }

      // 设置关卡数据
      // 在实际应用中，这应该从游戏配置或API获取
      setLevels(gameData.settings?.levels || DEFAULT_LEVELS);

      setLoading(false);
    } catch (error) {
      console.error('获取游戏数据失败:', error);
      message.error('获取游戏数据失败');
      setLoading(false);
    }
  }, [gameId]);

  useEffect(() => {
    fetchGameData();
  }, [fetchGameData]);

  // 开始游戏
  const startGame = () => {
    if (soundEnabled) {
      playAudio(AUDIO.CLICK, 0.3);
    }

    if (!game.remaining_draws || game.remaining_draws <= 0) {
      message.warning('您的参与次数已用完');
      return;
    }

    // 重置游戏状态
    setGameStarted(true);
    setGameCompleted(false);
    setCurrentLevel(0);
    setCurrentQuestion(0);
    setScore(0);
    setTotalScore(0);
    setSelectedAnswer(null);
    setAnswerSubmitted(false);
  };

  // 处理答案选择
  const handleAnswerSelect = (answerIndex) => {
    if (answerSubmitted) return;

    setSelectedAnswer(answerIndex);
  };

  // 提交答案
  const submitAnswer = () => {
    if (selectedAnswer === null || answerSubmitted) return;

    const currentLevelData = levels[currentLevel];
    const currentQuestionData = currentLevelData.questions[currentQuestion];
    const isCorrect = selectedAnswer === currentQuestionData.answer;

    if (isCorrect) {
      setScore(score + 1);
      if (soundEnabled) {
        playAudio(AUDIO.WIN, 0.3);
      }
    } else {
      if (soundEnabled) {
        playAudio(AUDIO.LOSE, 0.3);
      }
    }

    setAnswerSubmitted(true);

    // 延迟后进入下一题或下一关
    setTimeout(() => {
      if (currentQuestion < currentLevelData.questions.length - 1) {
        // 进入下一题
        setCurrentQuestion(currentQuestion + 1);
        setSelectedAnswer(null);
        setAnswerSubmitted(false);
      } else {
        // 当前关卡结束，检查是否通过
        const passed = score >= currentLevelData.required_score;

        if (passed) {
          // 添加关卡奖励到总分
          const newTotalScore = totalScore + currentLevelData.reward.points;
          setTotalScore(newTotalScore);

          if (currentLevel < levels.length - 1) {
            // 进入下一关
            if (soundEnabled) {
              playAudio(AUDIO.LEVEL_UP, 0.4);
            }
            message.success(`恭喜通过${currentLevelData.name}！${currentLevelData.reward.description}`);
            setCurrentLevel(currentLevel + 1);
            setCurrentQuestion(0);
            setScore(0);
            setSelectedAnswer(null);
            setAnswerSubmitted(false);
          } else {
            // 通关
            if (soundEnabled) {
              playAudio(AUDIO.LEVEL_UP, 0.5);
            }
            message.success(`恭喜通过所有关卡！总得分：${newTotalScore}`);
            completeGame(newTotalScore);
          }
        } else {
          // 未通过关卡
          if (soundEnabled) {
            playAudio(AUDIO.GAME_OVER, 0.4);
          }
          message.error(`很遗憾，未能通过${currentLevelData.name}。需要得分：${currentLevelData.required_score}，您的得分：${score}`);

          // 游戏结束，但没有完全通关
          setGameStarted(false);
          setGameCompleted(true);

          // 更新剩余次数
          setGame(prev => ({
            ...prev,
            remaining_draws: prev.remaining_draws - 1
          }));
        }
      }
    }, 1500);
  };

  // 完成游戏并抽奖
  const completeGame = async (finalScore) => {
    try {
      // 确保用户ID存在
      const userId = localStorage.getItem('user_id') || `user_${Date.now()}_${Math.floor(Math.random() * 100000)}`;
      if (!localStorage.getItem('user_id')) {
        localStorage.setItem('user_id', userId);
      }

      const deviceId = localStorage.getItem('device_id') || `device_${Date.now()}_${Math.floor(Math.random() * 100000)}`;
      if (!localStorage.getItem('device_id')) {
        localStorage.setItem('device_id', deviceId);
      }

      // 调用API进行抽奖，确保提供participant_id
      const response = await apiService.createRecord(gameId, {
        participant_id: userId, // 确保participant_id有值
        participant_name: localStorage.getItem('user_name') || '匿名用户',
        device_id: deviceId,
        score: finalScore, // 传递最终得分
        timestamp: Date.now()
      });

      // 检查响应和响应数据
      if (!response || !response.success || !response.data) {
        throw new Error(response?.message || '抽奖失败，请重试');
      }

      const result = response.data;

      // 确保结果包含必要的 prize_id
      if (!result || !result.prize_id) {
        throw new Error('抽奖结果异常，请重试');
      }

      // 找到中奖项
      const prize = prizes.find(p => p.id === result.prize_id) ||
                   prizes.find(p => p.is_default);

      setWinningPrize(prize);
      setGameStarted(false);
      setGameCompleted(true);
      setShowResult(true);

      // 更新剩余次数
      setGame(prev => ({
        ...prev,
        remaining_draws: prev.remaining_draws - 1
      }));

      // 更新游戏数据（剩余次数等）
      fetchGameData();
    } catch (error) {
      console.error('完成游戏失败:', error);
      message.error(error.message || '完成游戏失败，请重试');
      setGameStarted(false);
      setGameCompleted(true);
    }
  };

  // 清除音频
  useEffect(() => {
    return () => {
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current = null;
      }
    };
  }, []);

  if (loading) {
    return (
      <div className="adventure-game-loading">
        <Spin size="large" indicator={<LoadingOutlined className="loading-icon" spin />} />
        <p className="loading-text">精彩闯关游戏即将开始...</p>
      </div>
    );
  }

  // 获取背景图片
  const backgroundImage = game.background_image || DEFAULT_BACKGROUNDS[2];

  // 格式化剩余参与次数显示
  const remainingDraws = game.remaining_draws !== undefined ? game.remaining_draws : '0';

  // 渲染游戏界面
  return (
    <div className="adventure-game-container" style={{ backgroundImage: `url(${backgroundImage})` }}>
      <div className="adventure-game-header">
        <img
          src={game.logo_image || '/assets/images/logo-default.png'}
          alt="Logo"
          className="game-logo"
        />
        <Title level={2} className="game-title">{game.name || '闯关游戏'}</Title>
        <Paragraph className="game-description">{game.description || '完成挑战赢大奖'}</Paragraph>
      </div>

      <div className="adventure-game-content">
        <div className="settings-bar">
          <div className="settings-item">
            <span className="settings-label">音效</span>
            <Switch
              checked={soundEnabled}
              onChange={setSoundEnabled}
              size="small"
              checkedChildren={<SoundOutlined />}
              unCheckedChildren={<SoundOutlined />}
            />
          </div>
        </div>

        {!gameStarted && !gameCompleted ? (
          // 游戏开始前的界面
          <div className="game-start-screen">
            <Card className="game-info-card">
              <Title level={3}>闯关挑战</Title>
              <Paragraph>
                完成所有关卡挑战，赢取丰厚奖品！
              </Paragraph>

              <div className="level-preview">
                <Steps direction="vertical" current={-1}>
                  {levels.map((level, index) => (
                    <Step
                      key={level.id}
                      title={level.name}
                      description={level.description}
                      icon={index === levels.length - 1 ? <FlagFilled /> : <StarOutlined />}
                    />
                  ))}
                </Steps>
              </div>

              <Button
                type="primary"
                size="large"
                icon={<RightOutlined />}
                onClick={startGame}
                disabled={!remainingDraws || remainingDraws <= 0}
                className="start-button"
              >
                开始挑战 (剩余{remainingDraws}次)
              </Button>
            </Card>
          </div>
        ) : gameStarted && !gameCompleted ? (
          // 游戏进行中的界面
          <div className="game-play-screen">
            <Card className="game-level-card">
              <div className="level-header">
                <div className="level-info">
                  <Title level={4}>{levels[currentLevel].name}</Title>
                  <Progress
                    percent={Math.round((currentQuestion + 1) / levels[currentLevel].questions.length * 100)}
                    size="small"
                    status="active"
                    format={() => `${currentQuestion + 1}/${levels[currentLevel].questions.length}`}
                  />
                </div>
                <div className="level-score">
                  <Statistic
                    title="当前得分"
                    value={score}
                    suffix={`/${levels[currentLevel].required_score}`}
                  />
                </div>
              </div>

              <div className="question-container">
                <div className="question">
                  <Title level={4} className="question-text">
                    {levels[currentLevel].questions[currentQuestion].text}
                  </Title>
                </div>

                <div className="options">
                  {levels[currentLevel].questions[currentQuestion].options.map((option, index) => (
                    <Button
                      key={index}
                      className={`option-button ${selectedAnswer === index ? 'selected' : ''} ${
                        answerSubmitted
                          ? index === levels[currentLevel].questions[currentQuestion].answer
                            ? 'correct'
                            : selectedAnswer === index
                              ? 'incorrect'
                              : ''
                          : ''
                      }`}
                      onClick={() => handleAnswerSelect(index)}
                      disabled={answerSubmitted}
                    >
                      {option}
                    </Button>
                  ))}
                </div>
              </div>

              <div className="question-actions">
                <Button
                  type="primary"
                  onClick={submitAnswer}
                  disabled={selectedAnswer === null || answerSubmitted}
                >
                  {answerSubmitted ? '请稍候...' : '提交答案'}
                </Button>
              </div>
            </Card>

            <div className="game-progress">
              <Steps current={currentLevel} size="small">
                {levels.map((level) => (
                  <Step key={level.id} title={level.name} />
                ))}
              </Steps>
            </div>
          </div>
        ) : (
          // 游戏结束的界面
          <div className="game-end-screen">
            <Result
              status={totalScore > 0 ? "success" : "warning"}
              title={totalScore > 0 ? "恭喜完成挑战！" : "挑战失败"}
              subTitle={
                totalScore > 0
                  ? `您获得了 ${totalScore} 积分`
                  : "再接再厉，下次一定能成功！"
              }
              extra={[
                <Button
                  type="primary"
                  key="restart"
                  onClick={startGame}
                  disabled={!remainingDraws || remainingDraws <= 0}
                >
                  再次挑战 (剩余{remainingDraws}次)
                </Button>
              ]}
            />
          </div>
        )}

        <div className="action-buttons">
          <Button
            icon={<TrophyOutlined />}
            onClick={() => setShowPrizes(true)}
            className="action-button"
          >
            奖品设置
          </Button>
          <Button
            icon={<HistoryOutlined />}
            onClick={() => setShowRecords(true)}
            className="action-button"
          >
            挑战记录
          </Button>
          <Button
            icon={<UserOutlined />}
            onClick={() => setShowRules(true)}
            className="action-button"
          >
            活动规则
          </Button>
        </div>
      </div>

      <div className="adventure-game-footer">
        <p>© {new Date().getFullYear()} {game.name || '闯关游戏'} - 版权所有</p>
        <div className="footer-links">
          <a href="/privacy" className="footer-link">隐私政策</a>
          <a href="/terms" className="footer-link">使用条款</a>
          <a href="/contact" className="footer-link">联系我们</a>
        </div>
      </div>

      {/* 活动规则弹窗 */}
      <Modal
        title="活动规则"
        open={showRules}
        onCancel={() => setShowRules(false)}
        footer={null}
      >
        <Paragraph>
          <pre style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-word' }}>
            {game.rules || '暂无活动规则'}
          </pre>
        </Paragraph>
        <Divider />
        <Row gutter={16}>
          <Col span={12}>
            <Statistic title="最低消费" value={`¥${game.min_consumption || 0}`} />
          </Col>
          <Col span={12}>
            <Statistic title="每人参与次数" value={game.max_draws_per_user || 0} />
          </Col>
        </Row>
      </Modal>

      {/* 奖品设置弹窗 */}
      <Modal
        title="奖品设置"
        open={showPrizes}
        onCancel={() => setShowPrizes(false)}
        footer={null}
        width={700}
      >
        <List
          itemLayout="horizontal"
          dataSource={prizes.filter(prize => !prize.is_default)}
          renderItem={item => (
            <List.Item>
              <List.Item.Meta
                avatar={<Avatar src={item.image_url || DEFAULT_PRIZE_ICONS.none} size={64} />}
                title={item.name}
                description={item.description}
              />
              <div>
                <Tag color="blue">概率: {item.probability}%</Tag>
                <Tag color="green">剩余: {item.remaining}</Tag>
              </div>
            </List.Item>
          )}
        />
      </Modal>

      {/* 中奖记录弹窗 */}
      <Modal
        title="挑战记录"
        open={showRecords}
        onCancel={() => setShowRecords(false)}
        footer={null}
      >
        {records.length > 0 ? (
          <List
            itemLayout="horizontal"
            dataSource={records}
            renderItem={item => (
              <List.Item>
                <List.Item.Meta
                  avatar={<Avatar icon={<UserOutlined />} />}
                  title={item.user_name}
                  description={`获得 ${item.prize_name}`}
                />
                <div>{item.created_at}</div>
              </List.Item>
            )}
          />
        ) : (
          <div style={{ textAlign: 'center', padding: '20px 0' }}>
            <p>暂无挑战记录</p>
          </div>
        )}
      </Modal>

      {/* 抽奖结果弹窗 */}
      <Modal
        title="挑战结果"
        open={showResult}
        onCancel={() => setShowResult(false)}
        footer={[
          <Button key="back" onClick={() => setShowResult(false)}>
            关闭
          </Button>,
          <Button
            key="share"
            type="primary"
            icon={<ShareAltOutlined />}
            onClick={() => {
              message.success('分享成功');
              setShowResult(false);
            }}
          >
            分享
          </Button>
        ]}
        width={500}
      >
        {winningPrize && (
          <Result
            icon={winningPrize.is_default ? <MehOutlined /> : <SmileOutlined />}
            status={winningPrize.is_default ? 'warning' : 'success'}
            title={winningPrize.is_default ? '很遗憾，未中奖' : '恭喜您，中奖了！'}
            subTitle={winningPrize.description}
            extra={
              !winningPrize.is_default && (
                <div className="prize-result">
                  <img src={winningPrize.image_url || DEFAULT_PRIZE_ICONS.none} alt={winningPrize.name} className="prize-result-image" />
                  <div className="prize-result-info">
                    <Title level={4} className="prize-result-title">{winningPrize.name}</Title>
                    <Paragraph className="prize-result-description">{winningPrize.description}</Paragraph>
                  </div>
                </div>
              )
            }
          />
        )}

        <Divider />

        <Space direction="vertical" style={{ width: '100%', textAlign: 'center' }}>
          <Text>剩余参与次数: {remainingDraws}</Text>
          {remainingDraws > 0 && (
            <Button
              type="primary"
              onClick={() => {
                setShowResult(false);
                setTimeout(() => {
                  startGame();
                }, 500);
              }}
            >
              再次挑战
            </Button>
          )}
        </Space>
      </Modal>
    </div>
  );
}

export default AdventureGame;