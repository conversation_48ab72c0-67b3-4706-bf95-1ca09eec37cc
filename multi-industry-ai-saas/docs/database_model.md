# 零售AI SaaS系统数据库模型设计

## 1. 概述

零售AI SaaS系统数据库设计采用多租户架构，同时支持服务商模式，实现多级分销能力。本文档详细描述系统的数据库模型设计，包括核心实体、关系和表结构。

### 1.1 设计原则

1. **多租户支持**：所有业务数据表都包含租户标识
2. **服务商模式**：支持服务商创建和管理多个租户
3. **数据隔离**：确保不同租户和服务商之间的数据安全隔离
4. **可扩展性**：支持未来功能扩展和业务变化
5. **性能优化**：针对高频查询进行索引优化

### 1.2 数据隔离策略

系统采用混合数据隔离策略：

1. **Schema隔离**：每个租户使用独立的数据库Schema
   - 用于存储租户的业务数据
   - 提供最高级别的数据隔离

2. **行级隔离**：在共享表中通过标识字段区分数据
   - 用于系统级配置和共享数据
   - 通过`service_provider_id`和`tenant_id`字段实现隔离

## 2. 核心实体关系

### 2.1 系统级实体关系

```
系统管理员(SystemAdmin) ──┐
                          │
服务商(ServiceProvider) ──┼── 订阅计划(SubscriptionPlan)
     │                    │
     └─── 租户(Tenant) ───┘
           │
           └─── 项目(Project) ─── 用户(User)
                                    │
                                    └─── 角色(Role) ─── 权限(Permission)
```

### 2.2 业务级实体关系

```
项目(Project) ─┬─ 门店(Store) ─┬─ 销售(Sales)
               │               └─ 库存(Inventory)
               │
               ├─ 商品(Product) ─┬─ 分类(Category)
               │                 └─ 品牌(Brand)
               │
               ├─ 供应商(Supplier) ─── 采购单(PurchaseOrder)
               │
               └─ 仓库(Warehouse) ─── 库存调拨(StockTransfer)
```

## 3. 系统级数据模型

### 3.1 服务商模型

#### 3.1.1 服务商表(service_providers)

存储系统中的服务商信息，服务商可以创建和管理多个租户。

```sql
CREATE TABLE service_providers (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    company_name VARCHAR(200) NOT NULL,
    business_license VARCHAR(100),
    contact_person VARCHAR(100) NOT NULL,
    contact_email VARCHAR(100) NOT NULL,
    contact_phone VARCHAR(20) NOT NULL,
    address TEXT,
    logo_url VARCHAR(255),
    website VARCHAR(255),
    status VARCHAR(20) NOT NULL DEFAULT 'active', -- active, suspended, inactive
    commission_rate DECIMAL(5, 2) DEFAULT 0, -- 分成比例
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    settings JSONB DEFAULT '{}'::jsonb, -- 服务商特定设置
    branding_settings JSONB DEFAULT '{}'::jsonb, -- OEM品牌设置
    api_key VARCHAR(100), -- 服务商API密钥
    api_secret VARCHAR(255) -- 服务商API密钥
);

-- 索引
CREATE INDEX idx_service_provider_status ON service_providers(status);
CREATE INDEX idx_service_provider_created_at ON service_providers(created_at);
```

#### 3.1.2 服务商管理员表(service_provider_admins)

存储服务商的管理员账户信息。

```sql
CREATE TABLE service_provider_admins (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    service_provider_id UUID NOT NULL REFERENCES service_providers(id) ON DELETE CASCADE,
    username VARCHAR(50) NOT NULL,
    email VARCHAR(100) NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(100),
    phone VARCHAR(20),
    status VARCHAR(20) NOT NULL DEFAULT 'active', -- active, inactive
    is_primary BOOLEAN DEFAULT FALSE, -- 是否为主管理员
    last_login_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    permissions JSONB DEFAULT '{}'::jsonb -- 管理员权限设置
);

-- 索引
CREATE UNIQUE INDEX idx_sp_admin_username ON service_provider_admins(username);
CREATE UNIQUE INDEX idx_sp_admin_email ON service_provider_admins(email);
CREATE INDEX idx_sp_admin_service_provider ON service_provider_admins(service_provider_id);
```

#### 3.1.3 服务商配置表(service_provider_configs)

存储服务商的配置信息，包括自定义域名、品牌设置等。

```sql
CREATE TABLE service_provider_configs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    service_provider_id UUID NOT NULL REFERENCES service_providers(id) ON DELETE CASCADE,
    config_key VARCHAR(100) NOT NULL,
    config_value TEXT,
    config_group VARCHAR(50) NOT NULL DEFAULT 'general',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,

    UNIQUE(service_provider_id, config_key)
);

-- 索引
CREATE INDEX idx_sp_config_group ON service_provider_configs(service_provider_id, config_group);
```

### 3.2 租户模型

#### 3.2.1 租户表(tenants)

存储系统中的租户信息，每个租户关联到一个服务商。

```sql
CREATE TABLE tenants (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    service_provider_id UUID NOT NULL REFERENCES service_providers(id),
    name VARCHAR(100) NOT NULL,
    domain_prefix VARCHAR(50) UNIQUE,
    custom_domain VARCHAR(255) UNIQUE,
    contact_person VARCHAR(100) NOT NULL,
    contact_email VARCHAR(100) NOT NULL,
    contact_phone VARCHAR(20) NOT NULL,
    address TEXT,
    logo_url VARCHAR(255),
    status VARCHAR(20) NOT NULL DEFAULT 'active', -- active, trial, suspended, inactive
    trial_ends_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    settings JSONB DEFAULT '{}'::jsonb, -- 租户特定设置
    industry_type VARCHAR(50) DEFAULT 'retail', -- 行业类型
    db_schema VARCHAR(50) NOT NULL, -- 租户数据库Schema名称
    max_projects INTEGER DEFAULT 1, -- 最大项目数
    max_users INTEGER DEFAULT 10, -- 最大用户数
    storage_quota BIGINT DEFAULT 536870912 -- 512MB in bytes
);

-- 索引
CREATE INDEX idx_tenant_service_provider ON tenants(service_provider_id);
CREATE INDEX idx_tenant_status ON tenants(status);
CREATE INDEX idx_tenant_created_at ON tenants(created_at);
```

#### 3.2.2 租户管理员表(tenant_admins)

存储租户的管理员账户信息。

```sql
CREATE TABLE tenant_admins (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    username VARCHAR(50) NOT NULL,
    email VARCHAR(100) NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(100),
    phone VARCHAR(20),
    status VARCHAR(20) NOT NULL DEFAULT 'active', -- active, inactive
    is_primary BOOLEAN DEFAULT FALSE, -- 是否为主管理员
    last_login_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    permissions JSONB DEFAULT '{}'::jsonb -- 管理员权限设置
);

-- 索引
CREATE UNIQUE INDEX idx_tenant_admin_username ON tenant_admins(username);
CREATE UNIQUE INDEX idx_tenant_admin_email ON tenant_admins(email);
CREATE INDEX idx_tenant_admin_tenant ON tenant_admins(tenant_id);
```

### 3.3 订阅与计费模型

#### 3.3.1 订阅计划表(subscription_plans)

存储系统提供的订阅计划信息。

```sql
CREATE TABLE subscription_plans (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(100) NOT NULL,
    code VARCHAR(50) NOT NULL UNIQUE,
    description TEXT,
    is_public BOOLEAN DEFAULT TRUE, -- 是否公开可订阅
    price DECIMAL(10, 2) NOT NULL,
    billing_cycle VARCHAR(20) NOT NULL DEFAULT 'monthly', -- monthly, quarterly, yearly
    max_projects INTEGER,
    max_users_per_tenant INTEGER,
    max_stores_per_project INTEGER,
    storage_quota BIGINT, -- in bytes
    features JSONB DEFAULT '{}'::jsonb, -- 包含的功能
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE
);

-- 索引
CREATE INDEX idx_subscription_plan_active ON subscription_plans(is_active);
CREATE INDEX idx_subscription_plan_public ON subscription_plans(is_public);
```

#### 3.3.2 服务商订阅计划表(service_provider_subscription_plans)

服务商可以创建自定义的订阅计划提供给其租户。

```sql
CREATE TABLE service_provider_subscription_plans (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    service_provider_id UUID NOT NULL REFERENCES service_providers(id) ON DELETE CASCADE,
    base_plan_id UUID REFERENCES subscription_plans(id), -- 基于系统计划的自定义计划
    name VARCHAR(100) NOT NULL,
    code VARCHAR(50) NOT NULL,
    description TEXT,
    price DECIMAL(10, 2) NOT NULL,
    billing_cycle VARCHAR(20) NOT NULL DEFAULT 'monthly', -- monthly, quarterly, yearly
    max_projects INTEGER,
    max_users_per_tenant INTEGER,
    max_stores_per_project INTEGER,
    storage_quota BIGINT, -- in bytes
    features JSONB DEFAULT '{}'::jsonb, -- 包含的功能
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,

    UNIQUE(service_provider_id, code)
);

-- 索引
CREATE INDEX idx_sp_plan_provider ON service_provider_subscription_plans(service_provider_id);
CREATE INDEX idx_sp_plan_active ON service_provider_subscription_plans(is_active);
```

#### 3.3.3 租户订阅表(tenant_subscriptions)

存储租户的订阅信息。

```sql
CREATE TABLE tenant_subscriptions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    plan_id UUID REFERENCES subscription_plans(id),
    service_provider_plan_id UUID REFERENCES service_provider_subscription_plans(id),
    status VARCHAR(20) NOT NULL DEFAULT 'active', -- active, pending, canceled, expired
    start_date DATE NOT NULL,
    end_date DATE,
    auto_renew BOOLEAN DEFAULT TRUE,
    price DECIMAL(10, 2) NOT NULL, -- 实际订阅价格
    billing_cycle VARCHAR(20) NOT NULL DEFAULT 'monthly', -- monthly, quarterly, yearly
    last_billing_date DATE,
    next_billing_date DATE,
    payment_method VARCHAR(50),
    payment_details JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,

    CHECK (
        (plan_id IS NOT NULL AND service_provider_plan_id IS NULL) OR
        (plan_id IS NULL AND service_provider_plan_id IS NOT NULL)
    )
);

-- 索引
CREATE UNIQUE INDEX idx_tenant_active_subscription ON tenant_subscriptions(tenant_id) WHERE status = 'active';
CREATE INDEX idx_subscription_status ON tenant_subscriptions(status);
CREATE INDEX idx_subscription_end_date ON tenant_subscriptions(end_date);
```

#### 3.3.4 账单表(invoices)

存储租户的账单信息。

```sql
CREATE TABLE invoices (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    subscription_id UUID REFERENCES tenant_subscriptions(id),
    invoice_number VARCHAR(50) NOT NULL UNIQUE,
    amount DECIMAL(10, 2) NOT NULL,
    tax_amount DECIMAL(10, 2) DEFAULT 0,
    total_amount DECIMAL(10, 2) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'pending', -- pending, paid, overdue, canceled
    issue_date DATE NOT NULL,
    due_date DATE NOT NULL,
    paid_date DATE,
    billing_period_start DATE,
    billing_period_end DATE,
    description TEXT,
    payment_method VARCHAR(50),
    payment_details JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_invoice_tenant ON invoices(tenant_id);
CREATE INDEX idx_invoice_status ON invoices(status);
CREATE INDEX idx_invoice_due_date ON invoices(due_date);
```

#### 3.3.5 服务商佣金表(service_provider_commissions)

记录服务商获得的佣金信息。

```sql
CREATE TABLE service_provider_commissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    service_provider_id UUID NOT NULL REFERENCES service_providers(id) ON DELETE CASCADE,
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    invoice_id UUID REFERENCES invoices(id),
    amount DECIMAL(10, 2) NOT NULL,
    commission_rate DECIMAL(5, 2) NOT NULL,
    commission_amount DECIMAL(10, 2) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'pending', -- pending, paid, canceled
    calculation_date DATE NOT NULL,
    payment_date DATE,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_commission_provider ON service_provider_commissions(service_provider_id);
CREATE INDEX idx_commission_status ON service_provider_commissions(status);
CREATE INDEX idx_commission_calculation_date ON service_provider_commissions(calculation_date);
```

### 3.4 项目模型

#### 3.4.1 项目表(projects)

存储租户创建的项目信息。

```sql
CREATE TABLE projects (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    logo_url VARCHAR(255),
    status VARCHAR(20) NOT NULL DEFAULT 'active', -- active, archived, inactive
    created_by UUID NOT NULL, -- 创建者ID
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    settings JSONB DEFAULT '{}'::jsonb, -- 项目特定设置
    storage_quota BIGINT, -- 项目存储配额，为NULL则使用租户配额
    user_quota INTEGER, -- 项目用户配额，为NULL则使用租户配额
    industry_type VARCHAR(50), -- 项目行业类型，为NULL则使用租户设置
    default_language VARCHAR(10) DEFAULT 'zh-CN' -- 项目默认语言
);

-- 索引
CREATE INDEX idx_project_tenant ON projects(tenant_id);
CREATE INDEX idx_project_status ON projects(status);
CREATE INDEX idx_project_created_at ON projects(created_at);
```

#### 3.4.2 项目成员表(project_members)

存储项目的成员信息，关联用户和项目。

```sql
CREATE TABLE project_members (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    project_id UUID NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    role_id UUID NOT NULL REFERENCES roles(id),
    status VARCHAR(20) NOT NULL DEFAULT 'active', -- active, inactive
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    invited_by UUID, -- 邀请人ID
    permissions JSONB DEFAULT '{}'::jsonb, -- 成员特定权限
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,

    UNIQUE(project_id, user_id)
);

-- 索引
CREATE INDEX idx_project_member_project ON project_members(project_id);
CREATE INDEX idx_project_member_user ON project_members(user_id);
CREATE INDEX idx_project_member_role ON project_members(role_id);
```

#### 3.4.3 项目配置表(project_configs)

存储项目的配置信息。

```sql
CREATE TABLE project_configs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    project_id UUID NOT NULL REFERENCES projects(id) ON DELETE CASCADE,
    config_key VARCHAR(100) NOT NULL,
    config_value TEXT,
    config_group VARCHAR(50) NOT NULL DEFAULT 'general',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,

    UNIQUE(project_id, config_key)
);

-- 索引
CREATE INDEX idx_project_config_group ON project_configs(project_id, config_group);
```

### 3.5 用户与权限模型

#### 3.5.1 用户表(users)

存储系统中的用户信息。

```sql
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    username VARCHAR(50) NOT NULL,
    email VARCHAR(100) NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(100),
    phone VARCHAR(20),
    avatar_url VARCHAR(255),
    status VARCHAR(20) NOT NULL DEFAULT 'active', -- active, inactive, locked
    email_verified BOOLEAN DEFAULT FALSE,
    phone_verified BOOLEAN DEFAULT FALSE,
    last_login_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    settings JSONB DEFAULT '{}'::jsonb, -- 用户特定设置
    default_project_id UUID, -- 默认项目ID

    UNIQUE(tenant_id, username),
    UNIQUE(tenant_id, email)
);

-- 索引
CREATE INDEX idx_user_tenant ON users(tenant_id);
CREATE INDEX idx_user_status ON users(status);
CREATE INDEX idx_user_email ON users(email);
```

#### 3.5.2 角色表(roles)

存储系统中的角色信息。

```sql
CREATE TABLE roles (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    name VARCHAR(50) NOT NULL,
    description TEXT,
    is_system BOOLEAN DEFAULT FALSE, -- 是否为系统预定义角色
    permissions JSONB DEFAULT '{}'::jsonb, -- 角色权限设置
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,

    UNIQUE(tenant_id, name)
);

-- 索引
CREATE INDEX idx_role_tenant ON roles(tenant_id);
CREATE INDEX idx_role_system ON roles(is_system);
```

#### 3.5.3 权限表(permissions)

存储系统中的权限定义。

```sql
CREATE TABLE permissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    code VARCHAR(100) NOT NULL UNIQUE,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    group_name VARCHAR(50) NOT NULL,
    is_system BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_permission_group ON permissions(group_name);
CREATE INDEX idx_permission_system ON permissions(is_system);
```

#### 3.5.4 角色权限关联表(role_permissions)

存储角色与权限的关联关系。

```sql
CREATE TABLE role_permissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    role_id UUID NOT NULL REFERENCES roles(id) ON DELETE CASCADE,
    permission_id UUID NOT NULL REFERENCES permissions(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,

    UNIQUE(role_id, permission_id)
);

-- 索引
CREATE INDEX idx_role_permission_role ON role_permissions(role_id);
CREATE INDEX idx_role_permission_permission ON role_permissions(permission_id);
```

#### 3.5.5 用户会话表(user_sessions)

存储用户的会话信息。

```sql
CREATE TABLE user_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    session_token VARCHAR(255) NOT NULL UNIQUE,
    refresh_token VARCHAR(255) UNIQUE,
    ip_address VARCHAR(45),
    user_agent TEXT,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_activity_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_user_session_user ON user_sessions(user_id);
CREATE INDEX idx_user_session_expires ON user_sessions(expires_at);
```

### 3.6 存储管理模型

#### 3.6.1 文件存储表(storage_files)

存储系统中的文件信息。

```sql
CREATE TABLE storage_files (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    path VARCHAR(500) NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    size BIGINT NOT NULL, -- 文件大小（字节）
    extension VARCHAR(20),
    storage_provider VARCHAR(50) DEFAULT 'local', -- local, s3, oss, etc.
    storage_path VARCHAR(500) NOT NULL, -- 存储路径
    is_public BOOLEAN DEFAULT FALSE,
    access_url VARCHAR(1000), -- 公开访问地址
    uploaded_by UUID NOT NULL REFERENCES users(id),
    folder_path VARCHAR(500) DEFAULT '/',
    status VARCHAR(20) DEFAULT 'active', -- active, deleted
    metadata JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_storage_file_tenant ON storage_files(tenant_id);
CREATE INDEX idx_storage_file_project ON storage_files(project_id);
CREATE INDEX idx_storage_file_path ON storage_files(path);
CREATE INDEX idx_storage_file_folder ON storage_files(folder_path);
CREATE INDEX idx_storage_file_status ON storage_files(status);
```

#### 3.6.2 文件共享表(file_shares)

存储文件的共享信息。

```sql
CREATE TABLE file_shares (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    file_id UUID NOT NULL REFERENCES storage_files(id) ON DELETE CASCADE,
    share_token VARCHAR(100) NOT NULL UNIQUE,
    created_by UUID NOT NULL REFERENCES users(id),
    expires_at TIMESTAMP WITH TIME ZONE,
    password VARCHAR(100),
    access_count INTEGER DEFAULT 0,
    last_accessed_at TIMESTAMP WITH TIME ZONE,
    status VARCHAR(20) DEFAULT 'active', -- active, expired, revoked
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_file_share_file ON file_shares(file_id);
CREATE INDEX idx_file_share_token ON file_shares(share_token);
CREATE INDEX idx_file_share_status ON file_shares(status);
```

#### 3.6.3 存储配额表(storage_quotas)

记录租户和项目的存储配额使用情况。

```sql
CREATE TABLE storage_quotas (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    total_quota BIGINT NOT NULL, -- 总配额（字节）
    used_quota BIGINT NOT NULL DEFAULT 0, -- 已使用配额（字节）
    last_updated TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,

    UNIQUE(tenant_id, project_id)
);

-- 索引
CREATE INDEX idx_storage_quota_tenant ON storage_quotas(tenant_id);
CREATE INDEX idx_storage_quota_project ON storage_quotas(project_id);
```

### 3.7 插件管理模型

#### 3.7.1 插件表(plugins)

存储系统中的插件信息。

```sql
CREATE TABLE plugins (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    code VARCHAR(100) NOT NULL UNIQUE,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    version VARCHAR(20) NOT NULL,
    author VARCHAR(100),
    website VARCHAR(255),
    icon_url VARCHAR(255),
    is_system BOOLEAN DEFAULT FALSE,
    is_active BOOLEAN DEFAULT TRUE,
    requires_subscription BOOLEAN DEFAULT FALSE,
    min_subscription_plan VARCHAR(50),
    installation_path VARCHAR(255),
    entry_point VARCHAR(255),
    settings_schema JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_plugin_active ON plugins(is_active);
CREATE INDEX idx_plugin_system ON plugins(is_system);
```

#### 3.7.2 租户插件表(tenant_plugins)

存储租户安装的插件信息。

```sql
CREATE TABLE tenant_plugins (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    plugin_id UUID NOT NULL REFERENCES plugins(id) ON DELETE CASCADE,
    status VARCHAR(20) NOT NULL DEFAULT 'active', -- active, disabled, uninstalled
    version VARCHAR(20) NOT NULL,
    installed_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    installed_by UUID REFERENCES users(id),
    last_updated_at TIMESTAMP WITH TIME ZONE,
    settings JSONB DEFAULT '{}'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,

    UNIQUE(tenant_id, plugin_id)
);

-- 索引
CREATE INDEX idx_tenant_plugin_tenant ON tenant_plugins(tenant_id);
CREATE INDEX idx_tenant_plugin_status ON tenant_plugins(status);
```

#### 3.7.3 插件版本表(plugin_versions)

存储插件的版本信息。

```sql
CREATE TABLE plugin_versions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    plugin_id UUID NOT NULL REFERENCES plugins(id) ON DELETE CASCADE,
    version VARCHAR(20) NOT NULL,
    release_notes TEXT,
    download_url VARCHAR(255),
    checksum VARCHAR(100),
    min_system_version VARCHAR(20),
    is_compatible BOOLEAN DEFAULT TRUE,
    is_latest BOOLEAN DEFAULT FALSE,
    released_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,

    UNIQUE(plugin_id, version)
);

-- 索引
CREATE INDEX idx_plugin_version_plugin ON plugin_versions(plugin_id);
CREATE INDEX idx_plugin_version_latest ON plugin_versions(is_latest);
```

## 4. 业务数据模型

业务数据模型存储在租户的独立Schema中，每个租户都有自己的业务数据表。以下是主要的业务数据表结构。

### 4.1 零售基础模型

#### 4.1.1 门店表(stores)

存储租户的门店信息。

```sql
CREATE TABLE {tenant_schema}.stores (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    project_id UUID NOT NULL,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(50),
    address TEXT,
    contact_person VARCHAR(100),
    contact_phone VARCHAR(20),
    area DECIMAL(10, 2), -- 门店面积
    category_id UUID, -- 门店分类
    area_id UUID, -- 门店区域
    manager_id UUID, -- 店长
    status VARCHAR(20) DEFAULT 'active', -- active, closed, suspended
    opening_hours JSONB, -- 营业时间
    location JSONB, -- 地理位置（经纬度）
    created_by UUID NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_store_project ON {tenant_schema}.stores(project_id);
CREATE INDEX idx_store_status ON {tenant_schema}.stores(status);
CREATE INDEX idx_store_category ON {tenant_schema}.stores(category_id);
CREATE INDEX idx_store_area ON {tenant_schema}.stores(area_id);
```

#### 4.1.2 门店分类表(store_categories)

存储门店的分类信息。

```sql
CREATE TABLE {tenant_schema}.store_categories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    project_id UUID NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,

    UNIQUE(project_id, name)
);

-- 索引
CREATE INDEX idx_store_category_project ON {tenant_schema}.store_categories(project_id);
```

#### 4.1.3 门店区域表(store_areas)

存储门店的区域信息。

```sql
CREATE TABLE {tenant_schema}.store_areas (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    project_id UUID NOT NULL,
    name VARCHAR(100) NOT NULL,
    parent_id UUID REFERENCES {tenant_schema}.store_areas(id),
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,

    UNIQUE(project_id, name, parent_id)
);

-- 索引
CREATE INDEX idx_store_area_project ON {tenant_schema}.store_areas(project_id);
CREATE INDEX idx_store_area_parent ON {tenant_schema}.store_areas(parent_id);
```

### 4.2 商品管理模型

#### 4.2.1 商品表(products)

存储租户的商品信息。

```sql
CREATE TABLE {tenant_schema}.products (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    project_id UUID NOT NULL,
    name VARCHAR(200) NOT NULL,
    sku VARCHAR(50),
    barcode VARCHAR(50),
    specification VARCHAR(100),
    brand_id UUID,
    category_id UUID,
    description TEXT,
    cost_price DECIMAL(10, 2),
    retail_price DECIMAL(10, 2),
    wholesale_price DECIMAL(10, 2),
    unit VARCHAR(20),
    weight DECIMAL(10, 3),
    volume DECIMAL(10, 3),
    images JSONB DEFAULT '[]'::jsonb,
    attributes JSONB DEFAULT '{}'::jsonb,
    status VARCHAR(20) DEFAULT 'active', -- active, inactive, discontinued
    created_by UUID NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_product_project ON {tenant_schema}.products(project_id);
CREATE INDEX idx_product_sku ON {tenant_schema}.products(sku);
CREATE INDEX idx_product_barcode ON {tenant_schema}.products(barcode);
CREATE INDEX idx_product_category ON {tenant_schema}.products(category_id);
CREATE INDEX idx_product_brand ON {tenant_schema}.products(brand_id);
CREATE INDEX idx_product_status ON {tenant_schema}.products(status);
```

#### 4.2.2 商品分类表(product_categories)

存储商品的分类信息。

```sql
CREATE TABLE {tenant_schema}.product_categories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    project_id UUID NOT NULL,
    name VARCHAR(100) NOT NULL,
    parent_id UUID REFERENCES {tenant_schema}.product_categories(id),
    description TEXT,
    image_url VARCHAR(255),
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_product_category_project ON {tenant_schema}.product_categories(project_id);
CREATE INDEX idx_product_category_parent ON {tenant_schema}.product_categories(parent_id);
```

#### 4.2.3 品牌表(brands)

存储商品的品牌信息。

```sql
CREATE TABLE {tenant_schema}.brands (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    project_id UUID NOT NULL,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    logo_url VARCHAR(255),
    website VARCHAR(255),
    country_of_origin VARCHAR(50),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,

    UNIQUE(project_id, name)
);

-- 索引
CREATE INDEX idx_brand_project ON {tenant_schema}.brands(project_id);
```

### 4.3 库存管理模型

#### 4.3.1 库存表(inventories)

存储商品的库存信息。

```sql
CREATE TABLE {tenant_schema}.inventories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    project_id UUID NOT NULL,
    product_id UUID NOT NULL,
    store_id UUID,
    warehouse_id UUID,
    quantity INTEGER NOT NULL DEFAULT 0,
    min_quantity INTEGER DEFAULT 5,
    max_quantity INTEGER DEFAULT 100,
    last_count_date TIMESTAMP WITH TIME ZONE,
    last_updated_by UUID,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,

    UNIQUE(project_id, product_id, store_id, warehouse_id)
);

-- 索引
CREATE INDEX idx_inventory_project ON {tenant_schema}.inventories(project_id);
CREATE INDEX idx_inventory_product ON {tenant_schema}.inventories(product_id);
CREATE INDEX idx_inventory_store ON {tenant_schema}.inventories(store_id);
CREATE INDEX idx_inventory_warehouse ON {tenant_schema}.inventories(warehouse_id);
```

#### 4.3.2 仓库表(warehouses)

存储仓库信息。

```sql
CREATE TABLE {tenant_schema}.warehouses (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    project_id UUID NOT NULL,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(50),
    address TEXT,
    manager_id UUID,
    contact_phone VARCHAR(20),
    area DECIMAL(10, 2),
    status VARCHAR(20) DEFAULT 'active', -- active, inactive
    created_by UUID NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_warehouse_project ON {tenant_schema}.warehouses(project_id);
CREATE INDEX idx_warehouse_status ON {tenant_schema}.warehouses(status);
```

### 4.4 销售管理模型

#### 4.4.1 销售记录表(sales)

存储商品的销售记录。

```sql
CREATE TABLE {tenant_schema}.sales (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    project_id UUID NOT NULL,
    store_id UUID NOT NULL,
    product_id UUID NOT NULL,
    quantity INTEGER NOT NULL,
    unit_price DECIMAL(10, 2) NOT NULL,
    total_price DECIMAL(10, 2) NOT NULL,
    discount_amount DECIMAL(10, 2) DEFAULT 0,
    payment_method VARCHAR(50),
    sale_date DATE NOT NULL,
    sale_time TIME NOT NULL,
    created_by UUID NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 索引
CREATE INDEX idx_sale_project ON {tenant_schema}.sales(project_id);
CREATE INDEX idx_sale_store ON {tenant_schema}.sales(store_id);
CREATE INDEX idx_sale_product ON {tenant_schema}.sales(product_id);
CREATE INDEX idx_sale_date ON {tenant_schema}.sales(sale_date);
```

#### 4.4.2 日销售汇总表(daily_sales)

存储门店的日销售汇总数据。

```sql
CREATE TABLE {tenant_schema}.daily_sales (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    project_id UUID NOT NULL,
    store_id UUID NOT NULL,
    report_date DATE NOT NULL,
    total_sales DECIMAL(12, 2) NOT NULL,
    online_sales DECIMAL(12, 2) DEFAULT 0,
    offline_sales DECIMAL(12, 2) DEFAULT 0,
    customer_count INTEGER DEFAULT 0,
    transaction_count INTEGER DEFAULT 0,
    status VARCHAR(20) DEFAULT 'submitted', -- pending, submitted, approved, rejected
    notes TEXT,
    created_by UUID NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,

    UNIQUE(project_id, store_id, report_date)
);

-- 索引
CREATE INDEX idx_daily_sale_project ON {tenant_schema}.daily_sales(project_id);
CREATE INDEX idx_daily_sale_store ON {tenant_schema}.daily_sales(store_id);
CREATE INDEX idx_daily_sale_date ON {tenant_schema}.daily_sales(report_date);
CREATE INDEX idx_daily_sale_status ON {tenant_schema}.daily_sales(status);
```

## 5. 数据迁移与管理

### 5.1 数据迁移策略

在SaaS化改造过程中，需要将现有的单体部署数据迁移到多租户架构中。主要的数据迁移策略如下：

1. **创建服务商和租户**
   - 为每个现有客户创建服务商记录
   - 为每个服务商创建默认租户
   - 设置租户的订阅计划和配额

2. **创建租户Schema**
   - 为每个租户创建独立的数据库Schema
   - 在Schema中创建必要的业务数据表

3. **数据迁移**
   - 将现有用户数据迁移到租户管理员和用户表
   - 将现有业务数据迁移到租户Schema中的相应表
   - 将现有配置数据迁移到租户和项目配置表

4. **数据验证**
   - 执行数据一致性检查
   - 验证数据关系完整性
   - 测试数据访问性能

### 5.2 数据备份与恢复

系统将实现定期备份和恢复机制，主要包括：

1. **定期备份**
   - 每日增量备份
   - 每周全量备份
   - 跨区域备份存储

2. **恢复机制**
   - 支持租户级恢复
   - 支持项目级恢复
   - 支持指定表恢复

3. **灾难恢复计划**
   - 定期灾难恢复演练
   - 跨区域备份切换
   - 数据一致性检查

### 5.3 数据安全策略

为保障多租户环境下的数据安全，系统将实施以下安全策略：

1. **数据加密**
   - 敏感数据字段加密存储
   - 传输过程加密(TLS)
   - 备份数据加密

2. **访问控制**
   - 基于角色的访问控制(RBAC)
   - 最小权限原则
   - 多因素认证

3. **审计日志**
   - 记录所有数据操作
   - 定期审计日志分析
   - 异常行为检测

## 6. 结论

本文档详细描述了零售AI SaaS系统的数据库模型设计，包括系统级数据模型和业务数据模型。该设计支持多租户架构，并实现了服务商模式，使系统能够支持多级分销能力。

数据库模型设计遵循了数据隔离、可扩展性和性能优化的原则，确保系统能够安全、高效地运行。同时，该设计也考虑了数据迁移、备份和恢复策略，为系统的可靠性和安全性提供了保障。

随着系统的发展，数据库模型将不断扩展和优化，以支持新的功能和业务需求。
