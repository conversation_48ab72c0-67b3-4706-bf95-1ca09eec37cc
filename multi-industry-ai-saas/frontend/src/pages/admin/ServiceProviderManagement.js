import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Table, 
  Button, 
  Space, 
  Tooltip, 
  Tag, 
  Typography, 
  Modal, 
  Form, 
  Input, 
  Select, 
  Divider,
  Popconfirm,
  message,
  Spin,
  Alert,
  Row,
  Col,
  Statistic,
  Upload
} from 'antd';
import { 
  CloudServerOutlined, 
  PlusOutlined, 
  EditOutlined, 
  DeleteOutlined, 
  EyeOutlined, 
  SettingOutlined,
  UploadOutlined,
  BankOutlined,
  TeamOutlined,
  AppstoreOutlined,
  CloudOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import apiService from '../../services/api';

const { Title, Paragraph, Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;

const ServiceProviderManagement = () => {
  const navigate = useNavigate();
  
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [serviceProviders, setServiceProviders] = useState([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [modalTitle, setModalTitle] = useState('');
  const [editingProvider, setEditingProvider] = useState(null);
  const [form] = Form.useForm();
  const [statistics, setStatistics] = useState({
    total: 0,
    active: 0,
    inactive: 0,
    tenants: 0,
    users: 0
  });
  
  // 加载服务商数据
  useEffect(() => {
    fetchServiceProviders();
  }, []);
  
  // 获取服务商列表
  const fetchServiceProviders = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await apiService.getServiceProviders();
      setServiceProviders(response);
      
      // 计算统计数据
      const stats = {
        total: response.length,
        active: response.filter(p => p.status === 'active').length,
        inactive: response.filter(p => p.status === 'inactive').length,
        tenants: response.reduce((sum, p) => sum + (p.tenant_count || 0), 0),
        users: response.reduce((sum, p) => sum + (p.user_count || 0), 0)
      };
      
      setStatistics(stats);
    } catch (error) {
      console.error('获取服务商列表失败:', error);
      setError('获取服务商列表失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };
  
  // 打开创建服务商模态框
  const showCreateModal = () => {
    setModalTitle('创建服务商');
    setEditingProvider(null);
    form.resetFields();
    setModalVisible(true);
  };
  
  // 打开编辑服务商模态框
  const showEditModal = (provider) => {
    setModalTitle('编辑服务商');
    setEditingProvider(provider);
    form.setFieldsValue({
      name: provider.name,
      code: provider.code,
      description: provider.description,
      contact_name: provider.contact_name,
      contact_email: provider.contact_email,
      contact_phone: provider.contact_phone,
      status: provider.status,
      max_tenants: provider.max_tenants,
      max_users_per_tenant: provider.max_users_per_tenant,
      settings: provider.settings ? JSON.stringify(provider.settings) : '{}'
    });
    setModalVisible(true);
  };
  
  // 关闭模态框
  const handleCancel = () => {
    setModalVisible(false);
  };
  
  // 提交表单
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      
      // 解析JSON设置
      try {
        values.settings = values.settings ? JSON.parse(values.settings) : {};
      } catch (e) {
        message.error('设置格式不正确，请输入有效的JSON');
        return;
      }
      
      setLoading(true);
      
      if (editingProvider) {
        // 更新服务商
        await apiService.updateServiceProvider(editingProvider.id, values);
        message.success('服务商更新成功');
      } else {
        // 创建服务商
        await apiService.createServiceProvider(values);
        message.success('服务商创建成功');
      }
      
      setModalVisible(false);
      fetchServiceProviders();
    } catch (error) {
      console.error('提交表单失败:', error);
      message.error('操作失败: ' + (error.response?.data?.detail || '未知错误'));
    } finally {
      setLoading(false);
    }
  };
  
  // 删除服务商
  const handleDelete = async (id) => {
    try {
      setLoading(true);
      await apiService.deleteServiceProvider(id);
      message.success('服务商删除成功');
      fetchServiceProviders();
    } catch (error) {
      console.error('删除服务商失败:', error);
      message.error('删除失败: ' + (error.response?.data?.detail || '未知错误'));
    } finally {
      setLoading(false);
    }
  };
  
  // 获取状态标签
  const getStatusTag = (status) => {
    switch (status) {
      case 'active':
        return <Tag color="success">活跃</Tag>;
      case 'inactive':
        return <Tag color="error">停用</Tag>;
      case 'pending':
        return <Tag color="warning">待审核</Tag>;
      default:
        return <Tag>{status}</Tag>;
    }
  };
  
  // 表格列定义
  const columns = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <Space>
          {text}
          {record.is_verified && <Tag color="blue">已认证</Tag>}
        </Space>
      ),
    },
    {
      title: '代码',
      dataIndex: 'code',
      key: 'code',
    },
    {
      title: '联系人',
      dataIndex: 'contact_name',
      key: 'contact_name',
    },
    {
      title: '联系邮箱',
      dataIndex: 'contact_email',
      key: 'contact_email',
    },
    {
      title: '联系电话',
      dataIndex: 'contact_phone',
      key: 'contact_phone',
    },
    {
      title: '租户数',
      dataIndex: 'tenant_count',
      key: 'tenant_count',
      render: (text, record) => (
        <span>{text || 0} / {record.max_tenants || '不限'}</span>
      ),
    },
    {
      title: '用户数',
      dataIndex: 'user_count',
      key: 'user_count',
      render: (text) => text || 0,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (text) => getStatusTag(text),
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (text) => new Date(text).toLocaleString(),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button 
              type="text" 
              icon={<EyeOutlined />} 
              onClick={() => navigate(`/admin/service-providers/${record.id}`)} 
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button 
              type="text" 
              icon={<EditOutlined />} 
              onClick={() => showEditModal(record)} 
            />
          </Tooltip>
          <Tooltip title="设置">
            <Button 
              type="text" 
              icon={<SettingOutlined />} 
              onClick={() => navigate(`/admin/service-providers/${record.id}/settings`)} 
            />
          </Tooltip>
          <Popconfirm
            title="确定要删除此服务商吗？"
            description="删除后无法恢复，且会影响其下所有租户。"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button 
              type="text" 
              danger 
              icon={<DeleteOutlined />} 
            />
          </Popconfirm>
        </Space>
      ),
    },
  ];
  
  // 错误提示
  const errorAlert = error ? (
    <Alert
      message="错误"
      description={error}
      type="error"
      showIcon
      style={{ marginBottom: 16 }}
    />
  ) : null;
  
  return (
    <div className="service-provider-management">
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
        <div>
          <Title level={2}><CloudServerOutlined /> 服务商管理</Title>
          <Paragraph>管理系统中的服务商，包括创建、编辑和删除服务商。</Paragraph>
        </div>
        <Button 
          type="primary" 
          icon={<PlusOutlined />} 
          onClick={showCreateModal}
        >
          创建服务商
        </Button>
      </div>
      
      {errorAlert}
      
      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={4}>
          <Card>
            <Statistic
              title="服务商总数"
              value={statistics.total}
              prefix={<CloudServerOutlined />}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="活跃服务商"
              value={statistics.active}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="停用服务商"
              value={statistics.inactive}
              valueStyle={{ color: '#cf1322' }}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="租户总数"
              value={statistics.tenants}
              prefix={<BankOutlined />}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="用户总数"
              value={statistics.users}
              prefix={<TeamOutlined />}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Button 
              type="primary" 
              icon={<CloudOutlined />} 
              onClick={() => fetchServiceProviders()}
              loading={loading}
              block
            >
              刷新数据
            </Button>
          </Card>
        </Col>
      </Row>
      
      <Spin spinning={loading}>
        <Card>
          <Table 
            columns={columns} 
            dataSource={serviceProviders} 
            rowKey="id" 
            pagination={{ 
              pageSize: 10,
              showSizeChanger: true,
              showTotal: (total) => `共 ${total} 条记录`
            }}
          />
        </Card>
      </Spin>
      
      {/* 创建/编辑服务商模态框 */}
      <Modal
        title={modalTitle}
        open={modalVisible}
        onOk={handleSubmit}
        onCancel={handleCancel}
        width={800}
        confirmLoading={loading}
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="服务商名称"
                rules={[{ required: true, message: '请输入服务商名称' }]}
              >
                <Input placeholder="请输入服务商名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="code"
                label="服务商代码"
                rules={[{ required: true, message: '请输入服务商代码' }]}
              >
                <Input placeholder="请输入服务商代码" />
              </Form.Item>
            </Col>
          </Row>
          
          <Form.Item
            name="description"
            label="描述"
          >
            <TextArea rows={4} placeholder="请输入服务商描述" />
          </Form.Item>
          
          <Divider>联系信息</Divider>
          
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="contact_name"
                label="联系人"
                rules={[{ required: true, message: '请输入联系人' }]}
              >
                <Input placeholder="请输入联系人" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="contact_email"
                label="联系邮箱"
                rules={[
                  { required: true, message: '请输入联系邮箱' },
                  { type: 'email', message: '请输入有效的邮箱地址' }
                ]}
              >
                <Input placeholder="请输入联系邮箱" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="contact_phone"
                label="联系电话"
                rules={[{ required: true, message: '请输入联系电话' }]}
              >
                <Input placeholder="请输入联系电话" />
              </Form.Item>
            </Col>
          </Row>
          
          <Divider>配置信息</Divider>
          
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="status"
                label="状态"
                initialValue="active"
                rules={[{ required: true, message: '请选择状态' }]}
              >
                <Select placeholder="请选择状态">
                  <Option value="active">活跃</Option>
                  <Option value="inactive">停用</Option>
                  <Option value="pending">待审核</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="max_tenants"
                label="最大租户数"
                tooltip="设置为0表示不限制"
                initialValue={10}
              >
                <Input type="number" min={0} placeholder="请输入最大租户数" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="max_users_per_tenant"
                label="每租户最大用户数"
                tooltip="设置为0表示不限制"
                initialValue={50}
              >
                <Input type="number" min={0} placeholder="请输入每租户最大用户数" />
              </Form.Item>
            </Col>
          </Row>
          
          <Form.Item
            name="settings"
            label="设置"
            tooltip="JSON格式的设置"
            initialValue="{}"
          >
            <TextArea rows={4} placeholder='{"theme": "light", "logo_url": "https://example.com/logo.png"}' />
          </Form.Item>
          
          <Divider>品牌信息</Divider>
          
          <Form.Item
            name="logo"
            label="Logo"
          >
            <Upload
              name="logo"
              listType="picture"
              maxCount={1}
              beforeUpload={() => false}
            >
              <Button icon={<UploadOutlined />}>上传Logo</Button>
            </Upload>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default ServiceProviderManagement;
