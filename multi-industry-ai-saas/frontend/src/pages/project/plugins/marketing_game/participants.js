import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import {
  Card,
  Breadcrumb,
  Button,
  Table,
  Spin,
  message,
  Input,
  Space,
  Tag,
  Typography,
  Modal,
  Form,
  InputNumber,
  Tooltip,
  Statistic,
  Row,
  Col,
  Divider
} from 'antd';
import {
  ArrowLeftOutlined,
  SearchOutlined,
  UserAddOutlined,
  ExportOutlined,
  ReloadOutlined,
  EyeOutlined,
  EditOutlined,
  DeleteOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import dayjs from "dayjs";
import apiService from './service/api/marketingGame';

const { Title, Text } = Typography;
const { confirm } = Modal;

const ParticipantManagement = () => {
  const { gameId } = useParams();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [game, setGame] = useState(null);
  const [participants, setParticipants] = useState([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });
  const [searchText, setSearchText] = useState('');
  const [modalVisible, setModalVisible] = useState(false);
  const [currentParticipant, setCurrentParticipant] = useState(null);
  const [form] = Form.useForm();
  const [stats, setStats] = useState({
    totalParticipants: 0,
    totalDraws: 0,
    totalWins: 0,
    averageConsumption: 0
  });

  // 获取游戏详情
  const fetchGameDetail = async () => {
    try {
      const response = await apiService.getGameDetail(gameId);
      if (response.success) {
        setGame(response.data);
      } else {
        message.error(response.message || '获取游戏详情失败');
      }
    } catch (error) {
      console.error('获取游戏详情失败:', error);
      message.error('获取游戏详情失败');
    }
  };

  // 获取参与者列表
  const fetchParticipants = async (page = 1, pageSize = 10, search = '') => {
    setLoading(true);
    try {
      const params = {
        page,
        page_size: pageSize,
        search
      };

      const response = await apiService.getParticipants(gameId, params);
      if (response.success) {
        // 处理不同的响应格式
        let participantsData = [];
        let totalCount = 0;

        if (Array.isArray(response.data)) {
          // 如果是数组，直接使用
          participantsData = response.data;
          totalCount = response.data.length;
        } else if (response.data && typeof response.data === 'object') {
          // 如果是对象，尝试获取items属性
          participantsData = response.data.items || response.data.results || [];
          totalCount = response.data.total || response.data.count || participantsData.length;
        }

        // 确保每个参与者对象都有必要的属性
        const formattedParticipants = participantsData.map(p => ({
          ...p,
          id: p.id || p.participant_id || `temp_${Math.random().toString(36).substr(2, 9)}`,
          user_name: p.user_name || p.name || '匿名用户',
          draw_count: p.draw_count || p.draws_count || 0,
          win_count: p.win_count || p.wins_count || 0,
          consumption_amount: p.consumption_amount || p.total_consumption || 0,
          created_at: p.created_at || new Date().toISOString()
        }));

        setParticipants(formattedParticipants);
        setPagination({
          current: page,
          pageSize,
          total: totalCount
        });

        // 计算统计数据
        const totalParticipants = totalCount;
        const totalDraws = formattedParticipants.reduce((sum, p) => sum + (p.draw_count || 0), 0);
        const totalWins = formattedParticipants.reduce((sum, p) => sum + (p.win_count || 0), 0);
        const totalConsumption = formattedParticipants.reduce((sum, p) => sum + (p.consumption_amount || 0), 0);
        const averageConsumption = totalParticipants > 0 ? totalConsumption / totalParticipants : 0;

        setStats({
          totalParticipants,
          totalDraws,
          totalWins,
          averageConsumption
        });
      } else {
        message.error(response.message || '获取参与者列表失败');
      }
    } catch (error) {
      console.error('获取参与者列表失败:', error);
      message.error('获取参与者列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 初始加载
  useEffect(() => {
    const fetchData = async () => {
      await fetchGameDetail();
      await fetchParticipants();
    };

    if (gameId) {
      fetchData();
    }
  }, [gameId]);

  // 处理表格变化
  const handleTableChange = (pagination) => {
    fetchParticipants(pagination.current, pagination.pageSize, searchText);
  };

  // 处理搜索
  const handleSearch = () => {
    fetchParticipants(1, pagination.pageSize, searchText);
  };

  // 处理重置
  const handleReset = () => {
    setSearchText('');
    fetchParticipants(1, pagination.pageSize, '');
  };

  // 处理添加参与者
  const handleAddParticipant = () => {
    setCurrentParticipant(null);
    form.resetFields();
    setModalVisible(true);
  };

  // 处理编辑参与者
  const handleEditParticipant = (participant) => {
    setCurrentParticipant(participant);
    form.setFieldsValue({
      user_name: participant.user_name,
      phone: participant.phone,
      consumption_amount: participant.consumption_amount,
      draw_count: participant.draw_count,
      win_count: participant.win_count
    });
    setModalVisible(true);
  };

  // 处理删除参与者
  const handleDeleteParticipant = (participant) => {
    confirm({
      title: '确认删除',
      icon: <ExclamationCircleOutlined />,
      content: `确定要删除参与者 ${participant.user_name} 吗？`,
      onOk: async () => {
        try {
          // 在实际应用中，这里应该调用API删除参与者
          // const response = await apiService.project.plugin.marketingGame.deleteParticipant(gameId, participant.id);

          // 模拟删除成功
          message.success('删除成功');
          fetchParticipants(pagination.current, pagination.pageSize, searchText);
        } catch (error) {
          console.error('删除参与者失败:', error);
          message.error('删除参与者失败');
        }
      }
    });
  };

  // 处理表单提交
  const handleFormSubmit = async (values) => {
    try {
      if (currentParticipant) {
        // 编辑参与者
        // const response = await apiService.project.plugin.marketingGame.updateParticipant(gameId, currentParticipant.id, values);

        // 模拟更新成功
        message.success('更新成功');
      } else {
        // 添加参与者
        const response = await apiService.createParticipant(gameId, values);
        if (response.success) {
          message.success('添加成功');
        } else {
          message.error(response.message || '添加失败');
        }
      }

      setModalVisible(false);
      fetchParticipants(pagination.current, pagination.pageSize, searchText);
    } catch (error) {
      console.error('提交表单失败:', error);
      message.error('操作失败');
    }
  };

  // 导出参与者数据
  const handleExport = () => {
    // 在实际应用中，这里应该调用API导出参与者数据
    message.success('导出成功');
  };

  // 表格列定义
  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80
    },
    {
      title: '用户名',
      dataIndex: 'user_name',
      key: 'user_name',
      render: (text) => <a>{text}</a>
    },
    {
      title: '手机号',
      dataIndex: 'phone',
      key: 'phone'
    },
    {
      title: '消费金额',
      dataIndex: 'consumption_amount',
      key: 'consumption_amount',
      render: (amount) => `¥${amount ? amount.toFixed(2) : '0.00'}`
    },
    {
      title: '参与次数',
      dataIndex: 'draw_count',
      key: 'draw_count'
    },
    {
      title: '中奖次数',
      dataIndex: 'win_count',
      key: 'win_count'
    },
    {
      title: '中奖率',
      key: 'win_rate',
      render: (_, record) => {
        if (!record.draw_count) return '0.00%';
        const rate = record.draw_count > 0 ? (record.win_count / record.draw_count) * 100 : 0;
        return `${rate.toFixed(2)}%`;
      }
    },
    {
      title: '参与时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (time) => dayjs(time).format('YYYY-MM-DD HH:mm:ss')
    },
    {
      title: '操作',
      key: 'action',
      width: 180,
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => {
                // 查看参与者详情
                Modal.info({
                  title: '参与者详情',
                  content: (
                    <div>
                      <p><strong>用户名:</strong> {record.user_name}</p>
                      <p><strong>手机号:</strong> {record.phone}</p>
                      <p><strong>消费金额:</strong> ¥{record.consumption_amount ? record.consumption_amount.toFixed(2) : '0.00'}</p>
                      <p><strong>参与次数:</strong> {record.draw_count}</p>
                      <p><strong>中奖次数:</strong> {record.win_count}</p>
                      <p><strong>参与时间:</strong> {dayjs(record.created_at).format('YYYY-MM-DD HH:mm:ss')}</p>
                    </div>
                  ),
                  width: 500
                });
              }}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEditParticipant(record)}
            />
          </Tooltip>
          <Tooltip title="删除">
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
              onClick={() => handleDeleteParticipant(record)}
            />
          </Tooltip>
        </Space>
      )
    }
  ];

  return (
    <div className="participant-management">
      <Card>
        <Breadcrumb style={{ marginBottom: 16 }}>
          <Breadcrumb.Item>
            <Link to="/project/plugin/marketing_game">营销游戏</Link>
          </Breadcrumb.Item>
          <Breadcrumb.Item>
            <Link to={`/project/plugin/marketing_game/detail/${gameId}`}>游戏详情</Link>
          </Breadcrumb.Item>
          <Breadcrumb.Item>参与者管理</Breadcrumb.Item>
        </Breadcrumb>

        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
          <Space>
            <Button
              icon={<ArrowLeftOutlined />}
              onClick={() => navigate('/project/plugins/marketing_game')}
            >
              返回
            </Button>
            <Title level={4} style={{ margin: 0 }}>
              参与者管理
              {game && (
                <Text type="secondary" style={{ fontSize: 14, marginLeft: 8 }}>
                  ({game.name})
                </Text>
              )}
            </Title>
          </Space>

          <Space>
            <Input
              placeholder="搜索用户名或手机号"
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              onPressEnter={handleSearch}
              style={{ width: 200 }}
              prefix={<SearchOutlined />}
            />
            <Button onClick={handleSearch}>搜索</Button>
            <Button icon={<ReloadOutlined />} onClick={handleReset}>重置</Button>
            <Button type="primary" icon={<UserAddOutlined />} onClick={handleAddParticipant}>
              添加参与者
            </Button>
            <Button icon={<ExportOutlined />} onClick={handleExport}>
              导出
            </Button>
          </Space>
        </div>

        <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
          <Col span={6}>
            <Card>
              <Statistic title="参与人数" value={stats.totalParticipants} />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic title="参与次数" value={stats.totalDraws} />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic title="中奖次数" value={stats.totalWins} />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="平均消费"
                value={stats.averageConsumption}
                precision={2}
                prefix="¥"
              />
            </Card>
          </Col>
        </Row>

        <Table
          columns={columns}
          dataSource={participants}
          rowKey="id"
          pagination={pagination}
          loading={loading}
          onChange={handleTableChange}
        />
      </Card>

      {/* 添加/编辑参与者弹窗 */}
      <Modal
        title={currentParticipant ? '编辑参与者' : '添加参与者'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleFormSubmit}
        >
          <Form.Item
            name="user_name"
            label="用户名"
            rules={[{ required: true, message: '请输入用户名' }]}
          >
            <Input placeholder="请输入用户名" />
          </Form.Item>

          <Form.Item
            name="phone"
            label="手机号"
            rules={[
              { required: true, message: '请输入手机号' },
              { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号' }
            ]}
          >
            <Input placeholder="请输入手机号" />
          </Form.Item>

          <Form.Item
            name="consumption_amount"
            label="消费金额"
            rules={[{ required: true, message: '请输入消费金额' }]}
          >
            <InputNumber
              min={0}
              precision={2}
              style={{ width: '100%' }}
              placeholder="请输入消费金额"
              prefix="¥"
            />
          </Form.Item>

          {currentParticipant && (
            <>
              <Form.Item
                name="draw_count"
                label="参与次数"
                rules={[{ required: true, message: '请输入参与次数' }]}
              >
                <InputNumber min={0} style={{ width: '100%' }} placeholder="请输入参与次数" />
              </Form.Item>

              <Form.Item
                name="win_count"
                label="中奖次数"
                rules={[{ required: true, message: '请输入中奖次数' }]}
              >
                <InputNumber min={0} style={{ width: '100%' }} placeholder="请输入中奖次数" />
              </Form.Item>
            </>
          )}

          <Form.Item>
            <div style={{ textAlign: 'right' }}>
              <Button style={{ marginRight: 8 }} onClick={() => setModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                确定
              </Button>
            </div>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default ParticipantManagement;
