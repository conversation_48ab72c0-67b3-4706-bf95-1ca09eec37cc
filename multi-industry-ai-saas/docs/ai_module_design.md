# AI 模块设计与实现

## 1. 概述

AI 模块是 retail-ai-saas 系统的核心组件，为整个系统提供智能分析、推荐和决策支持能力。本文档整理了 AI 模块的设计思路、架构和实现计划。

## 2. 设计目标

- **多租户支持**：支持多租户架构，每个租户可以有独立的 AI 配置和资源
- **多行业适配**：支持不同行业（零售、餐饮等）的特定 AI 能力和知识库
- **多供应商集成**：支持多种 AI 模型和服务提供商，实现智能路由和负载均衡
- **统一接口**：提供统一的 API 接口，简化系统其他部分的集成
- **可扩展性**：支持通过插件机制扩展 AI 能力
- **安全与合规**：确保数据安全和合规使用 AI 服务

## 3. 核心架构

### 3.1 数据模型

```
AIProvider (AI供应商)
├── id: UUID
├── name: String (openai, azure, anthropic, etc.)
├── description: String
├── api_base_url: String
├── auth_type: String (api_key, oauth, etc.)
├── status: String (active, inactive)
├── created_at: DateTime
└── updated_at: DateTime

AIModel (AI模型)
├── id: UUID
├── provider_id: UUID (外键关联AIProvider)
├── name: String (gpt-4, claude-3, etc.)
├── type: String (chat, embedding, vision, audio)
├── capabilities: JSON (支持的功能，如function_call, vision, etc.)
├── context_window: Integer (上下文窗口大小)
├── token_limit: Integer (最大token数)
├── created_at: DateTime
└── updated_at: DateTime

AIConfig (AI配置)
├── id: UUID
├── tenant_id: UUID (可选，为空表示系统级配置)
├── project_id: UUID (可选，为空表示租户级配置)
├── provider_id: UUID (外键关联AIProvider)
├── model_id: UUID (外键关联AIModel)
├── api_key: String (加密存储)
├── api_endpoint: String (可选，覆盖默认endpoint)
├── proxy_url: String (可选，HTTP/SOCKS5代理)
├── temperature: Float
├── max_tokens: Integer
├── priority: Integer (路由优先级)
├── weight: Integer (负载均衡权重)
├── status: String (active, inactive)
├── created_at: DateTime
└── updated_at: DateTime

AIUsage (AI使用记录)
├── id: UUID
├── tenant_id: UUID
├── project_id: UUID
├── user_id: UUID
├── config_id: UUID (外键关联AIConfig)
├── model_id: UUID (外键关联AIModel)
├── request_type: String (chat, vision, embedding, audio)
├── prompt_tokens: Integer
├── completion_tokens: Integer
├── total_tokens: Integer
├── cost: Float
├── duration_ms: Integer (请求耗时)
├── status: String (success, failed)
├── error_message: String (可选)
├── created_at: DateTime
└── metadata: JSON (请求的元数据)

AIPromptTemplate (AI提示词模板)
├── id: UUID
├── tenant_id: UUID (可选，为空表示系统级模板)
├── project_id: UUID (可选，为空表示租户级模板)
├── industry_type: String (retail, restaurant, etc.)
├── name: String
├── description: String
├── template_type: String (chat, vision)
├── system_message: Text
├── user_message_template: Text
├── variables: JSON (模板变量定义)
├── created_at: DateTime
└── updated_at: DateTime

AIKnowledgeBase (AI知识库)
├── id: UUID
├── tenant_id: UUID
├── project_id: UUID (可选)
├── name: String
├── description: String
├── industry_type: String (retail, restaurant, etc.)
├── embedding_model_id: UUID (外键关联AIModel)
├── status: String (active, building, inactive)
├── created_at: DateTime
└── updated_at: DateTime

AIKnowledgeDocument (AI知识文档)
├── id: UUID
├── knowledge_base_id: UUID (外键关联AIKnowledgeBase)
├── title: String
├── content: Text
├── file_path: String (可选，文件路径)
├── metadata: JSON
├── embedding_status: String (pending, completed, failed)
├── created_at: DateTime
└── updated_at: DateTime

AIQuota (AI配额)
├── id: UUID
├── tenant_id: UUID (可选，为空表示系统默认配额)
├── project_id: UUID (可选，为空表示租户默认配额)
├── user_id: UUID (可选，为空表示项目默认配额)
├── model_type: String (chat, vision, embedding, audio)
├── daily_limit: Integer
├── monthly_limit: Integer
├── rate_limit: Integer (每分钟请求数)
├── created_at: DateTime
└── updated_at: DateTime
```

### 3.2 服务层

1. **AIProviderService**：供应商管理服务
   - 管理不同的 AI 供应商（OpenAI、Azure、Anthropic 等）
   - 处理供应商特定的认证和 API 调用
   - 实现供应商特定的请求转换和响应解析

2. **AIModelService**：模型管理服务
   - 管理不同供应商的模型
   - 处理模型能力和限制
   - 提供模型选择和推荐

3. **AIConfigService**：配置管理服务
   - 管理 AI 配置的 CRUD 操作
   - 实现配置的多级继承（系统 -> 租户 -> 项目 -> 用户）
   - 处理配置的加密和解密

4. **AIRoutingService**：路由服务
   - 基于优先级、权重、可用性和成本进行智能路由
   - 实现负载均衡和故障转移
   - 处理请求限流和队列

5. **AIUsageService**：使用记录和配额管理
   - 记录 AI 使用情况
   - 实现配额限制和计费
   - 提供使用统计和报告

6. **AIPromptService**：提示词模板管理
   - 管理行业特定的提示词模板
   - 支持模板变量替换
   - 实现模板的多级继承

7. **AIKnowledgeService**：知识库管理
   - 管理知识库和文档
   - 处理文档嵌入和检索
   - 支持知识库增强的 AI 对话

8. **AIMonitoringService**：监控服务
   - 监控 AI 服务的性能和可用性
   - 收集和分析使用数据
   - 提供警报和通知

### 3.3 API 层

1. **管理 API**：
   - `/api/project/{project_id}/ai/providers` - 供应商管理
   - `/api/project/{project_id}/ai/models` - 模型管理
   - `/api/project/{project_id}/ai/configs` - 配置管理
   - `/api/project/{project_id}/ai/templates` - 提示词模板管理
   - `/api/project/{project_id}/ai/knowledge` - 知识库管理
   - `/api/project/{project_id}/ai/quotas` - 配额管理
   - `/api/project/{project_id}/ai/usage` - 使用记录和统计

2. **功能 API**：
   - `/api/v1/project/{project_id}/ai/chat` - 聊天模型接口
   - `/api/v1/project/{project_id}/ai/vision` - 视觉模型接口
   - `/api/v1/project/{project_id}/ai/embedding` - 嵌入模型接口
   - `/api/v1/project/{project_id}/ai/audio` - 音频模型接口
   - `/api/v1/project/{project_id}/ai/recommendation` - 推荐接口
   - `/api/v1/project/{project_id}/ai/knowledge/query` - 知识库查询接口

## 4. 多租户与多行业支持

### 4.1 多租户支持

1. **配置隔离**：
   - 每个租户可以有自己的 AI 供应商配置
   - 租户可以选择使用系统默认配置或自定义配置
   - 项目级配置可以覆盖租户级配置
   - 用户级配置可以覆盖项目级配置

2. **资源管理**：
   - 按租户、项目和用户设置使用限额
   - 支持按时间段（每日、每月）和请求类型设置限额
   - 支持请求速率限制（RPM）
   - 支持按使用量计费和预付费模式

3. **数据隔离**：
   - 确保租户之间的数据完全隔离
   - 知识库和提示词模板也实现租户隔离
   - 缓存机制考虑租户隔离

### 4.2 多行业支持

1. **行业特定提示词**：
   - 为不同行业（零售、餐饮、酒店等）提供特定的提示词模板
   - 模板包含行业特定的术语和知识
   - 支持模板变量和条件逻辑

2. **行业适配器**：
   - 为不同行业提供特定的数据处理和结果解析逻辑
   - 支持行业特定的 AI 能力（如零售的商品识别、餐饮的菜单分析）
   - 提供行业特定的 AI 助手角色

3. **行业知识库**：
   - 支持行业特定的知识库集成
   - 预置行业通用知识
   - 允许租户上传和管理自己的知识库

## 5. 高级功能

### 5.1 智能路由与负载均衡

1. **多供应商路由**：
   - 基于模型可用性、成本、性能和功能需求进行智能路由
   - 支持故障转移和自动重试
   - 实现请求分流和负载均衡

2. **模型回退**：
   - 当首选模型不可用或超出配额时，自动回退到替代模型
   - 基于模型能力和相似性进行智能回退
   - 支持自定义回退策略

3. **成本优化**：
   - 基于成本和性能需求进行模型选择
   - 实现请求批处理和合并
   - 提供成本预估和优化建议

### 5.2 知识库增强

1. **文档处理**：
   - 支持多种文档格式（PDF、Word、Markdown 等）
   - 自动提取和处理文档内容
   - 支持文档分块和嵌入

2. **检索增强生成（RAG）**：
   - 基于用户查询检索相关知识
   - 将检索到的知识融入 AI 提示
   - 支持多种检索策略（语义搜索、混合搜索等）

3. **知识库管理**：
   - 支持知识库的创建、更新和删除
   - 提供知识库健康检查和优化
   - 支持知识库版本控制

### 5.3 监控与分析

1. **使用分析**：
   - 提供详细的使用统计和趋势分析
   - 支持按租户、项目、用户和模型的使用分析
   - 提供成本分析和预测

2. **性能监控**：
   - 监控 AI 请求的延迟和成功率
   - 提供供应商和模型的性能比较
   - 支持自定义性能指标

3. **异常检测**：
   - 检测异常使用模式和潜在滥用
   - 提供安全警报和通知
   - 支持自动响应和缓解措施

## 6. 安全与合规

1. **数据保护**：
   - 加密存储 API 密钥和敏感数据
   - 实现数据脱敏和过滤机制
   - 支持数据留存策略

2. **访问控制**：
   - 基于角色的访问控制（RBAC）
   - 支持细粒度的权限管理
   - 提供审计日志和访问记录

3. **合规支持**：
   - 支持数据本地化和区域限制
   - 提供合规报告和文档
   - 实现内容过滤和审核

## 7. 插件系统

1. **插件架构**：
   - 提供插件接口和生命周期管理
   - 支持动态加载和卸载插件
   - 实现插件隔离和安全检查

2. **内置插件**：
   - 内容审核插件
   - 数据分析插件
   - 行业特定插件（零售分析、餐饮推荐等）

3. **第三方集成**：
   - 支持与第三方服务的集成
   - 提供 Webhook 和事件系统
   - 支持自定义插件开发

## 8. 实施计划

### 8.1 第一阶段：基础架构

- 实现数据模型和核心服务
- 支持基本的 AI 供应商（OpenAI、Azure）
- 实现多租户配置和使用记录

### 8.2 第二阶段：高级路由和监控

- 实现智能路由和负载均衡
- 添加详细的监控和分析
- 支持更多 AI 供应商

### 8.3 第三阶段：知识库和行业适配

- 实现知识库功能
- 添加行业特定提示词和适配器
- 实现 RAG 功能

### 8.4 第四阶段：插件系统和高级功能

- 实现插件架构
- 添加内置插件
- 支持第三方集成

## 9. 参考资源

- [one-hub](https://github.com/MartialBE/one-hub) - OpenAI 接口管理 & 分发系统
- [retail-ai-system](https://github.com/example/retail-ai-system) - 原始零售 AI 系统
