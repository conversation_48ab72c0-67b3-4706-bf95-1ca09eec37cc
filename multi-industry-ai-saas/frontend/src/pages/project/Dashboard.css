.project-dashboard {
  padding: 24px;
}

.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.dashboard-title {
  flex: 1;
}

.loading-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 400px;
  gap: 16px;
}

.error-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 400px;
  gap: 16px;
}

.stat-cards {
  margin-bottom: 24px;
}

.stat-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
  font-size: 13px;
}

.stat-compare {
  color: #8c8c8c;
}

.chart-section {
  margin-bottom: 24px;
}

.chart-card {
  height: 100%;
}

.activity-section {
  margin-bottom: 24px;
}

.task-card, .activity-card {
  height: 100%;
}

.task-stats {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.task-stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.divider {
  height: 1px;
  background-color: #f0f0f0;
  width: 100%;
}

.upcoming-tasks {
  margin-top: 16px;
}

.store-section {
  margin-bottom: 24px;
}

.store-detail-card {
  text-align: center;
  height: 100%;
}

.store-name {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 16px;
  color: #1a365d;
}

.store-sales {
  margin-bottom: 16px;
}

.store-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.store-stats {
  display: flex;
  gap: 16px;
}

.activity-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.activity-time {
  color: #8c8c8c;
  font-size: 13px;
}

@media (max-width: 768px) {
  .project-dashboard {
    padding: 16px;
  }

  .dashboard-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .store-item, .activity-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .store-stats {
    width: 100%;
    justify-content: space-between;
  }

  .activity-time {
    width: 100%;
    text-align: right;
  }

  .task-stats {
    margin-top: 16px;
  }
}
