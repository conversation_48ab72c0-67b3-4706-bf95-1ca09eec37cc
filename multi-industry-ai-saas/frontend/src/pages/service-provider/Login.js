import React, { useState, useEffect } from 'react';
import { Form, Input, Button, Checkbox, Card, Typography, Alert, Divider, message } from 'antd';
import { UserOutlined, LockOutlined, CloudServerOutlined, WechatOutlined, DingdingOutlined } from '@ant-design/icons';
import { Link, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import httpClient from '../../services/api/httpClient';
import '../Login.css';

const { Title, Text } = Typography;

const ServiceProviderLogin = () => {
  const { login, loading } = useAuth();
  const [form] = Form.useForm();
  const location = useLocation();
  const [error, setError] = useState('');

  // 检查URL中是否有token参数（三方登录回调）
  useEffect(() => {
    const urlParams = new URLSearchParams(location.search);
    const token = urlParams.get('token');
    const loginError = urlParams.get('login_error');
    
    if (token) {
      localStorage.setItem('token', token);
      httpClient.get('/auth/me')
        .then(response => {
          message.success('登录成功');
          window.location.href = '/service-provider';
        })
        .catch(error => {
          console.error('获取用户信息失败:', error);
          message.error('登录失败，请重试');
          localStorage.removeItem('token');
        });
    } else if (loginError) {
      // 如果有登录错误，显示错误信息
      message.error(decodeURIComponent(loginError));
      // 清理URL中的错误参数
      const newUrl = window.location.pathname;
      window.history.replaceState({}, document.title, newUrl);
    }
  }, [location]);

  // 处理登录
  const handleLogin = async (values) => {
    try {
      setError('');
      console.log('服务商登录尝试:', values.username, values.password);
      // 服务商登录时，不传递特殊参数，只使用用户名和密码
      await login(values.username, values.password);
    } catch (error) {
      console.error('登录失败:', error);
      setError('登录失败，请检查用户名和密码');
    }
  };

  // 处理第三方登录
  const handleThirdPartyLogin = async (type) => {
    try {
      const projectId = '93289212-7943-48ab-8092-e8eb7f663677'; // 根据实际情况调整
      const currentUrl = window.location.href;
      
      let loginUrl = '';
      if (type === '钉钉') {
        loginUrl = `/auth/dingtalk/login?project_id=${projectId}&redirect_url=${encodeURIComponent(currentUrl)}`;
      } else if (type === '微信') {
        loginUrl = `/auth/wechat/login?project_id=${projectId}&redirect_url=${encodeURIComponent(currentUrl)}`;
      }
      
      if (loginUrl) {
        const response = await httpClient.get(loginUrl);
        if (response && response.auth_url) {
          window.location.href = response.auth_url;
        } else {
          message.error('获取登录链接失败');
        }
      }
    } catch (error) {
      console.error(`${type}登录失败:`, error);
      if (error.response && error.response.data && error.response.data.detail) {
        message.error(error.response.data.detail);
      } else {
        message.error(`${type}登录失败，请稍后重试`);
      }
    }
  };

  return (
    <div className="login-container service-provider-login">
      <Card className="login-card">
        <div className="login-header">
          <CloudServerOutlined style={{ fontSize: 48, color: '#1890ff', marginBottom: 16 }} />
          <Title level={2} className="login-title">服务商管理系统</Title>
          <Text type="secondary">多行业AI SaaS系统 - 服务商管理平台</Text>
        </div>

        {error && (
          <Alert
            message={error}
            type="error"
            showIcon
            style={{ marginBottom: 24 }}
          />
        )}

        <Form
          form={form}
          name="service_provider_login"
          initialValues={{ remember: true }}
          onFinish={handleLogin}
          size="large"
          layout="vertical"
        >
          <Form.Item
            name="username"
            label="用户名"
            rules={[{ required: true, message: '请输入用户名' }]}
          >
            <Input prefix={<UserOutlined />} placeholder="请输入用户名" />
          </Form.Item>

          <Form.Item
            name="password"
            label="密码"
            rules={[{ required: true, message: '请输入密码' }]}
          >
            <Input.Password prefix={<LockOutlined />} placeholder="请输入密码" />
          </Form.Item>

          <Form.Item>
            <div className="login-options">
              <Form.Item name="remember" valuePropName="checked" noStyle>
                <Checkbox>记住我</Checkbox>
              </Form.Item>
              <Link to="/forgot-password">忘记密码</Link>
            </div>
          </Form.Item>

          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              className="login-button"
            >
              登录
            </Button>
          </Form.Item>
        </Form>

        <Divider plain>其他登录方式</Divider>

        <div className="third-party-login">
          <Button
            icon={<WechatOutlined />}
            shape="circle"
            size="large"
            onClick={() => handleThirdPartyLogin('微信')}
          />
          <Button
            icon={<DingdingOutlined />}
            shape="circle"
            size="large"
            onClick={() => handleThirdPartyLogin('钉钉')}
          />
        </div>

        <div className="login-footer">
          <Text type="secondary">提示：服务商管理员请联系系统管理员获取账号</Text>
          <div style={{ marginTop: 8 }}>
            <Link to="/admin/login">超级管理员登录</Link>
            <Divider type="vertical" />
            <Link to="/login">普通用户登录</Link>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default ServiceProviderLogin;
