import React from 'react';
import { useAuth } from '../../contexts/AuthContext';

/**
 * 基于角色的内容控制组件
 * 根据用户角色显示或隐藏内容
 * @param {Object} props - 组件属性
 * @param {Array|string} props.roles - 允许访问的角色列表或单个角色
 * @param {React.ReactNode} props.children - 子组件
 * @param {React.ReactNode} props.fallback - 当用户没有权限时显示的内容
 */
const RoleBasedContent = ({ roles, children, fallback = null }) => {
  const { user, hasRole } = useAuth();

  // 检查用户是否有权限
  const hasPermission = () => {
    if (!user) return false;

    // 使用 AuthContext 提供的 hasRole 方法
    if (Array.isArray(roles)) {
      return roles.some(role => hasRole(role));
    }

    return hasRole(roles);
  };

  return hasPermission() ? children : fallback;
};

export default RoleBasedContent;
