import api from '../httpClient';
import { getProjectId } from '../httpClient';

/**
 * 项目-角色管理API
 */
const role = {
  /**
   * 获取角色列表
   * @param {Object} params - 查询参数
   * @returns {Promise} - 角色列表
   */
  getList: async (params = {}) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.get(`/project/${projectId}/roles`, { params });
  },

  /**
   * 获取角色详情
   * @param {string} id - 角色ID
   * @returns {Promise} - 角色详情
   */
  getDetail: async (id) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.get(`/project/${projectId}/roles/${id}`);
  },

  /**
   * 创建角色
   * @param {Object} data - 角色数据
   * @returns {Promise} - 创建结果
   */
  create: async (data) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.post(`/project/${projectId}/roles`, data);
  },

  /**
   * 更新角色
   * @param {string} id - 角色ID
   * @param {Object} data - 角色数据
   * @returns {Promise} - 更新结果
   */
  update: async (id, data) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.put(`/project/${projectId}/roles/${id}`, data);
  },

  /**
   * 删除角色
   * @param {string} id - 角色ID
   * @returns {Promise} - 删除结果
   */
  delete: async (id) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.delete(`/project/${projectId}/roles/${id}`);
  },

  /**
   * 获取角色权限
   * @param {string} id - 角色ID
   * @returns {Promise} - 角色权限
   */
  getPermissions: async (id) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.get(`/project/${projectId}/roles/${id}/permissions`);
  },

  /**
   * 更新角色权限
   * @param {string} id - 角色ID
   * @param {Object} data - 权限数据
   * @returns {Promise} - 更新结果
   */
  updatePermissions: async (id, data) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.put(`/project/${projectId}/roles/${id}/permissions`, data);
  },

  /**
   * 获取角色用户列表
   * @param {string} id - 角色ID
   * @param {Object} params - 查询参数
   * @returns {Promise} - 用户列表
   */
  getUsers: async (id, params = {}) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.get(`/project/${projectId}/roles/${id}/users`, { params });
  },

  /**
   * 添加角色用户
   * @param {string} id - 角色ID
   * @param {Object} data - 用户数据
   * @returns {Promise} - 添加结果
   */
  addUser: async (id, data) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.post(`/project/${projectId}/roles/${id}/users`, data);
  },

  /**
   * 移除角色用户
   * @param {string} id - 角色ID
   * @param {string} userId - 用户ID
   * @returns {Promise} - 移除结果
   */
  removeUser: async (id, userId) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.delete(`/project/${projectId}/roles/${id}/users/${userId}`);
  }
};

export default role;
