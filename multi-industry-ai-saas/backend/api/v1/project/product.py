#!/usr/bin/env python
# -*- coding: utf-8 -*-

from fastapi import APIRouter, Depends, HTTPException, status, Query, Path, UploadFile, File, Form, Body
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import func, and_, or_, select
from typing import List, Optional, Dict, Any
from datetime import datetime
import uuid
import os
import shutil
import pandas as pd
import io
import traceback
import logging

from db.database import get_db
from core.auth import get_current_user
from api.deps import get_current_project
from models.user import User
from models.product import Product, ProductBrand, ProductCategory
from models.inventory import InventoryItem
from schemas.product import (
    ProductCreate, ProductUpdate, ProductResponse, ProductListResponse,
    ProductBrandCreate, ProductBrandUpdate, ProductBrandResponse, ProductBrandListResponse,
    ProductCategoryCreate, ProductCategoryUpdate, ProductCategoryResponse, ProductCategoryListResponse,
    InventoryItemResponse, InventoryItemListResponse
)

logger = logging.getLogger(__name__)
router = APIRouter()

# --- 静态路由优先 ---
@router.get("/brands", response_model=ProductBrandListResponse)
async def get_brands(
    project_id: uuid.UUID = Path(...),
    page: int = Query(1, ge=1),
    page_size: int = Query(10, ge=1, le=100),
    search: Optional[str] = None,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """获取品牌列表"""
    try:
        # 计算分页偏移量
        offset = (page - 1) * page_size

        # 构建查询条件
        query = select(ProductBrand).filter(
            ProductBrand.project_id == project_id
        )

        # 应用过滤条件
        if search:
            query = query.filter(
                ProductBrand.name.ilike(f"%{search}%")
            )

        # 获取总数
        count_stmt = select(func.count()).select_from(query.subquery())
        total = (await db.execute(count_stmt)).scalar()

        # 获取分页数据
        brands_result = await db.execute(query.order_by(ProductBrand.created_at.desc()).offset(offset).limit(page_size))
        brands = brands_result.scalars().all()

        # 转换为响应格式
        brands_list = []
        for brand in brands:
            brands_list.append({
                "id": brand.id,
                "name": brand.name,
                "project_id": brand.project_id,
                "created_at": brand.created_at,
                "updated_at": brand.updated_at
            })

        return {
            "success": True,
            "data": brands_list,
            "total": total,
            "page": page,
            "page_size": page_size,
            "pages": (total + page_size - 1) // page_size
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取品牌列表失败: {str(e)}"
        )

@router.post("/brands", response_model=ProductBrandResponse, status_code=status.HTTP_201_CREATED)
async def create_brand(
    brand: ProductBrandCreate,
    project_id: uuid.UUID = Path(...),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """创建品牌"""
    try:
        # 检查品牌名称是否已存在
        query = select(ProductBrand).filter(
            ProductBrand.project_id == project_id,
            ProductBrand.name == brand.name
        )

        existing_brand = await db.execute(query)
        existing_brand = existing_brand.scalars().first()

        if existing_brand:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="品牌名称已存在"
            )

        # 创建品牌
        new_brand = ProductBrand(
            id=uuid.uuid4(),
            tenant_id=current_user.tenant_id,
            project_id=project_id,
            name=brand.name,
            description=brand.description,
            logo_url=brand.logo_url,
            created_by=current_user.id,
            updated_by=current_user.id
        )

        db.add(new_brand)
        await db.commit()
        await db.refresh(new_brand)

        return {
            "success": True,
            "message": "品牌创建成功",
            "data": new_brand
        }
    except HTTPException:
        await db.rollback()
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建品牌失败: {str(e)}"
        )

@router.put("/brands/{brand_id}", response_model=ProductBrandResponse)
async def update_brand(
    brand: ProductBrandUpdate,
    project_id: uuid.UUID = Path(...),
    brand_id: uuid.UUID = Path(...),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """更新品牌"""
    try:
        # 查询品牌
        query = select(ProductBrand).filter(
            ProductBrand.id == brand_id,
            ProductBrand.project_id == project_id
        )

        db_brand_result = await db.execute(query)
        db_brand = db_brand_result.scalars().first()

        if not db_brand:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="品牌不存在"
            )

        # 如果更新名称，检查是否与其他品牌冲突
        if brand.name and brand.name != db_brand.name:
            query = select(ProductBrand).filter(
                ProductBrand.project_id == project_id,
                ProductBrand.name == brand.name,
                ProductBrand.id != brand_id
            )

            existing_brand = await db.execute(query)
            existing_brand = existing_brand.scalars().first()

            if existing_brand:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="品牌名称已存在"
                )

        # 更新品牌
        update_data = brand.dict(exclude_unset=True)
        update_data["updated_by"] = current_user.id
        update_data["updated_at"] = datetime.now()

        for key, value in update_data.items():
            setattr(db_brand, key, value)

        await db.commit()
        await db.refresh(db_brand)

        return {
            "success": True,
            "message": "品牌更新成功",
            "data": db_brand
        }
    except HTTPException:
        await db.rollback()
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新品牌失败: {str(e)}"
        )

@router.delete("/brands/{brand_id}", response_model=Dict[str, Any])
async def delete_brand(
    project_id: uuid.UUID = Path(...),
    brand_id: uuid.UUID = Path(...),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """删除品牌"""
    try:
        # 查询品牌
        query = select(ProductBrand).filter(
            ProductBrand.id == brand_id,
            ProductBrand.project_id == project_id
        )

        db_brand_result = await db.execute(query)
        db_brand = db_brand_result.scalars().first()

        if not db_brand:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="品牌不存在"
            )

        # 检查品牌是否有关联的产品
        query = select(func.count(Product.id)).filter(
            Product.brand_id == brand_id
        )

        products_count = await db.execute(query)
        products_count = products_count.scalar()

        if products_count > 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无法删除品牌，因为存在关联的产品"
            )

        # 删除品牌
        await db.delete(db_brand)
        await db.commit()

        return {
            "success": True,
            "message": "品牌删除成功"
        }
    except HTTPException:
        await db.rollback()
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除品牌失败: {str(e)}"
        )

@router.get("/categories", response_model=ProductCategoryListResponse)
async def get_categories(
    project_id: uuid.UUID = Path(...),
    page: int = Query(1, ge=1),
    page_size: int = Query(10, ge=1, le=100),
    search: Optional[str] = None,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """获取分类列表"""
    try:
        # 计算分页偏移量
        offset = (page - 1) * page_size

        # 构建查询条件
        query = select(ProductCategory).filter(
            ProductCategory.project_id == project_id
        )

        # 应用过滤条件
        if search:
            query = query.filter(
                ProductCategory.name.ilike(f"%{search}%")
            )

        # 获取总数
        count_stmt = select(func.count()).select_from(query.subquery())
        total = (await db.execute(count_stmt)).scalar()

        # 获取分页数据
        categories_result = await db.execute(query.order_by(ProductCategory.created_at.desc()).offset(offset).limit(page_size))
        categories = categories_result.scalars().all()

        # 转换为响应格式
        categories_list = []
        for category in categories:
            category_dict = {
                "id": category.id,
                "tenant_id": category.tenant_id,
                "project_id": category.project_id,
                "name": category.name,
                "parent_id": category.parent_id,
                "description": category.description,
                "created_at": category.created_at,
                "updated_at": category.updated_at,
                "created_by": category.created_by,
                "updated_by": category.updated_by,
                "parent_name": None
            }

            # 查询父分类名称
            if category.parent_id:
                parent_query = select(ProductCategory).filter(
                    ProductCategory.id == category.parent_id
                )
                parent_result = await db.execute(parent_query)
                parent_category = parent_result.scalars().first()
                if parent_category:
                    category_dict["parent_name"] = parent_category.name

            categories_list.append(category_dict)

        return {
            "success": True,
            "data": categories_list,
            "total": total,
            "page": page,
            "page_size": page_size,
            "pages": (total + page_size - 1) // page_size
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取分类列表失败: {str(e)}"
        )

@router.post("/categories", response_model=ProductCategoryResponse, status_code=status.HTTP_201_CREATED)
async def create_category(
    category: ProductCategoryCreate,
    project_id: uuid.UUID = Path(...),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """创建分类"""
    try:
        # 检查分类名称是否已存在
        query = select(ProductCategory).filter(
            ProductCategory.project_id == project_id,
            ProductCategory.name == category.name,
            ProductCategory.parent_id == category.parent_id
        )

        existing_category = await db.execute(query)
        existing_category = existing_category.scalars().first()

        if existing_category:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="分类名称已存在"
            )

        # 如果指定了父分类，检查父分类是否存在
        if category.parent_id:
            query = select(ProductCategory).filter(
                ProductCategory.id == category.parent_id,
                ProductCategory.project_id == project_id
            )
            parent_category = await db.execute(query)
            parent_category = parent_category.scalars().first()

            if not parent_category:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="父分类不存在"
                )

        # 创建分类
        new_category = ProductCategory(
            id=uuid.uuid4(),
            tenant_id=current_user.tenant_id,
            project_id=project_id,
            name=category.name,
            parent_id=category.parent_id,
            description=category.description,
            created_by=current_user.id,
            updated_by=current_user.id
        )

        db.add(new_category)
        await db.commit()
        await db.refresh(new_category)

        # 查询父分类名称
        parent_name = None
        if new_category.parent_id:
            query = select(ProductCategory).filter(
                ProductCategory.id == new_category.parent_id
            )
            parent_category = await db.execute(query)
            parent_category = parent_category.scalars().first()
            if parent_category:
                parent_name = parent_category.name

        # 转换为响应格式
        category_dict = {
            "id": new_category.id,
            "tenant_id": new_category.tenant_id,
            "project_id": new_category.project_id,
            "name": new_category.name,
            "parent_id": new_category.parent_id,
            "description": new_category.description,
            "created_at": new_category.created_at,
            "updated_at": new_category.updated_at,
            "created_by": new_category.created_by,
            "updated_by": new_category.updated_by,
            "parent_name": parent_name
        }

        return {
            "success": True,
            "message": "分类创建成功",
            "data": category_dict
        }
    except HTTPException:
        await db.rollback()
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建分类失败: {str(e)}"
        )

@router.put("/categories/{category_id}", response_model=ProductCategoryResponse)
async def update_category(
    category: ProductCategoryUpdate,
    project_id: uuid.UUID = Path(...),
    category_id: uuid.UUID = Path(...),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """更新分类"""
    try:
        # 查询分类
        query = select(ProductCategory).filter(
            ProductCategory.id == category_id,
            ProductCategory.project_id == project_id
        )

        db_category_result = await db.execute(query)
        db_category = db_category_result.scalars().first()

        if not db_category:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="分类不存在"
            )

        # 如果更新名称，检查是否与其他分类冲突
        if category.name and category.name != db_category.name:
            query = select(ProductCategory).filter(
                ProductCategory.project_id == project_id,
                ProductCategory.name == category.name,
                ProductCategory.parent_id == (category.parent_id or db_category.parent_id),
                ProductCategory.id != category_id
            )

            existing_category = await db.execute(query)
            existing_category = existing_category.scalars().first()

            if existing_category:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="分类名称已存在"
                )

        # 如果更新父分类，检查父分类是否存在
        if category.parent_id and category.parent_id != db_category.parent_id:
            # 检查是否将分类设置为自己的子分类
            if category.parent_id == category_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="不能将分类设置为自己的子分类"
                )

            query = select(ProductCategory).filter(
                ProductCategory.id == category.parent_id,
                ProductCategory.project_id == project_id
            )
            parent_category = await db.execute(query)
            parent_category = parent_category.scalars().first()

            if not parent_category:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="父分类不存在"
                )

            # 检查是否形成循环引用
            current_parent_id = parent_category.parent_id
            while current_parent_id:
                if current_parent_id == category_id:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail="不能形成分类的循环引用"
                    )

                query = select(ProductCategory).filter(
                    ProductCategory.id == current_parent_id
                )
                current_parent_id = await db.execute(query)
                current_parent_id = current_parent_id.scalars().first()

        # 更新分类
        update_data = category.dict(exclude_unset=True)
        update_data["updated_by"] = current_user.id
        update_data["updated_at"] = datetime.now()

        for key, value in update_data.items():
            setattr(db_category, key, value)

        await db.commit()
        await db.refresh(db_category)

        # 查询父分类名称
        parent_name = None
        if db_category.parent_id:
            query = select(ProductCategory).filter(
                ProductCategory.id == db_category.parent_id
            )
            parent_category = await db.execute(query)
            parent_category = parent_category.scalars().first()
            if parent_category:
                parent_name = parent_category.name

        # 转换为响应格式
        category_dict = {
            "id": db_category.id,
            "tenant_id": db_category.tenant_id,
            "project_id": db_category.project_id,
            "name": db_category.name,
            "parent_id": db_category.parent_id,
            "description": db_category.description,
            "created_at": db_category.created_at,
            "updated_at": db_category.updated_at,
            "created_by": db_category.created_by,
            "updated_by": db_category.updated_by,
            "parent_name": parent_name
        }

        return {
            "success": True,
            "message": "分类更新成功",
            "data": category_dict
        }
    except HTTPException:
        await db.rollback()
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新分类失败: {str(e)}"
        )

@router.delete("/categories/{category_id}", response_model=Dict[str, Any])
async def delete_category(
    project_id: uuid.UUID = Path(...),
    category_id: uuid.UUID = Path(...),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """删除分类"""
    try:
        # 查询分类
        query = select(ProductCategory).filter(
            ProductCategory.id == category_id,
            ProductCategory.project_id == project_id
        )

        db_category_result = await db.execute(query)
        db_category = db_category_result.scalars().first()

        if not db_category:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="分类不存在"
            )

        # 检查分类是否有关联的产品
        query = select(func.count(Product.id)).filter(
            Product.category_id == category_id
        )

        products_count = await db.execute(query)
        products_count = products_count.scalar()

        if products_count > 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无法删除分类，因为存在关联的产品"
            )

        # 检查分类是否有子分类
        query = select(ProductCategory).filter(
            ProductCategory.parent_id == category_id
        )

        sub_categories_count = await db.execute(query.count())
        sub_categories_count = sub_categories_count.scalar()

        if sub_categories_count > 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无法删除分类，因为存在子分类"
            )

        # 删除分类
        await db.delete(db_category)
        await db.commit()

        return {
            "success": True,
            "message": "分类删除成功"
        }
    except HTTPException:
        await db.rollback()
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除分类失败: {str(e)}"
        )

@router.get("/merge-suggestions", response_model=Dict[str, Any])
async def get_product_merge_suggestions(
    similarity_threshold: float = Query(0.8, ge=0.1, le=1.0, description="相似度阈值"),
    project_id: uuid.UUID = Path(...),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """获取商品合并建议"""
    try:
        from services.product_service import ProductService
        
        suggestions = await ProductService.get_merge_suggestions(
            db=db,
            project_id=project_id,
            similarity_threshold=similarity_threshold
        )
        
        return {
            "success": True,
            "message": f"找到 {len(suggestions)} 组相似商品",
            "data": suggestions,
            "total": len(suggestions)
        }
        
    except Exception as e:
        logger.error(f"获取商品合并建议失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取商品合并建议失败: {str(e)}"
        )

@router.post("/merge", response_model=Dict[str, Any])
async def merge_products(
    merge_request: Dict[str, Any],
    project_id: uuid.UUID = Path(...),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """合并商品"""
    try:
        from services.product_service import ProductService
        
        primary_product_id = merge_request.get("primary_product_id")
        merge_product_ids = merge_request.get("merge_product_ids", [])
        
        if not primary_product_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="缺少主商品ID"
            )
        
        if not merge_product_ids:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="缺少要合并的商品ID列表"
            )
        
        # 验证商品是否属于当前项目
        primary_product = await db.get(Product, uuid.UUID(primary_product_id))
        if not primary_product or primary_product.project_id != project_id:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="主商品不存在或不属于当前项目"
            )
        
        # 转换合并商品ID
        merge_uuids = []
        for product_id in merge_product_ids:
            try:
                merge_uuids.append(uuid.UUID(product_id))
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"无效的商品ID: {product_id}"
                )
        
        # 执行合并
        result = await ProductService.merge_products(
            db=db,
            primary_product_id=uuid.UUID(primary_product_id),
            merge_product_ids=merge_uuids,
            user_id=current_user.id
        )
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"合并商品失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"合并商品失败: {str(e)}"
        )

@router.post("/batch-import", response_model=Dict[str, Any])
async def batch_import_products(
    project_id: uuid.UUID = Path(...),
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """批量导入产品"""
    try:
        # 检查文件类型
        if not file.filename.endswith(('.xlsx', '.xls')):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="只支持Excel文件(.xlsx, .xls)"
            )

        # 读取Excel文件
        contents = await file.read()
        df = pd.read_excel(io.BytesIO(contents))

        # 验证必要的列
        required_columns = ['name', 'sku']
        for column in required_columns:
            if column not in df.columns:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Excel文件缺少必要的列: {column}"
                )

        # 处理导入数据
        success_count = 0
        error_count = 0
        errors = []

        for index, row in df.iterrows():
            try:
                # 检查必要字段
                if pd.isna(row['name']) or pd.isna(row['sku']):
                    errors.append({
                        "row": index + 2,  # Excel行号从1开始，标题行为1
                        "error": "产品名称和SKU不能为空"
                    })
                    error_count += 1
                    continue

                # 检查SKU是否已存在
                query = select(Product).filter(
                    Product.project_id == project_id,
                    Product.sku == row['sku']
                )

                existing_product = await db.execute(query)
                existing_product = existing_product.scalars().first()

                if existing_product:
                    errors.append({
                        "row": index + 2,
                        "error": f"SKU '{row['sku']}' 已存在"
                    })
                    error_count += 1
                    continue

                # 处理品牌
                brand_id = None
                if 'brand' in df.columns and not pd.isna(row['brand']):
                    query = select(ProductBrand).filter(
                        ProductBrand.project_id == project_id,
                        ProductBrand.name == row['brand']
                    )
                    brand_result = await db.execute(query)
                    brand = brand_result.scalars().first()

                    if brand:
                        brand_id = brand.id
                    else:
                        # 创建新品牌
                        new_brand = ProductBrand(
                            id=uuid.uuid4(),
                            tenant_id=current_user.tenant_id,
                            project_id=project_id,
                            name=row['brand'],
                            created_by=current_user.id,
                            updated_by=current_user.id
                        )
                        db.add(new_brand)
                        await db.flush()
                        brand_id = new_brand.id

                # 处理分类
                category_id = None
                if 'category' in df.columns and not pd.isna(row['category']):
                    query = select(ProductCategory).filter(
                        ProductCategory.project_id == project_id,
                        ProductCategory.name == row['category']
                    )
                    category_result = await db.execute(query)
                    category = category_result.scalars().first()

                    if category:
                        category_id = category.id
                    else:
                        # 创建新分类
                        new_category = ProductCategory(
                            id=uuid.uuid4(),
                            tenant_id=current_user.tenant_id,
                            project_id=project_id,
                            name=row['category'],
                            created_by=current_user.id,
                            updated_by=current_user.id
                        )
                        db.add(new_category)
                        await db.flush()
                        category_id = new_category.id

                # 创建产品
                new_product = Product(
                    id=uuid.uuid4(),
                    tenant_id=current_user.tenant_id,
                    project_id=project_id,
                    name=row['name'],
                    sku=row['sku'],
                    barcode=row['barcode'] if 'barcode' in df.columns and not pd.isna(row['barcode']) else None,
                    brand_id=brand_id,
                    category_id=category_id,
                    description=row['description'] if 'description' in df.columns and not pd.isna(row['description']) else None,
                    specification=row['specification'] if 'specification' in df.columns and not pd.isna(row['specification']) else None,
                    unit=row.get('unit', '公斤'),
                    cost_price=row['cost_price'] if 'cost_price' in df.columns and not pd.isna(row['cost_price']) else None,
                    retail_price=row['retail_price'] if 'retail_price' in df.columns and not pd.isna(row['retail_price']) else None,
                    is_active=True,
                    is_variant=bool(row['specification']) if 'specification' in df.columns and not pd.isna(row['specification']) else False,
                    created_by=current_user.id,
                    updated_by=current_user.id
                )

                db.add(new_product)
                success_count += 1

            except Exception as e:
                errors.append({
                    "row": index + 2,
                    "error": str(e)
                })
                error_count += 1

        # 提交事务
        await db.commit()

        return {
            "success": True,
            "message": f"批量导入完成，成功: {success_count}，失败: {error_count}",
            "data": {
                "success_count": success_count,
                "error_count": error_count,
                "errors": errors
            }
        }
    except HTTPException:
        await db.rollback()
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"批量导入产品失败: {str(e)}"
        )

@router.get("/export", response_model=Dict[str, Any])
async def export_products(
    project_id: uuid.UUID = Path(...),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """导出产品列表"""
    # 注意：此处应该返回文件下载，但为了简化，我们只返回成功消息
    # 实际实现应该使用StreamingResponse返回Excel文件
    return {
        "success": True,
        "message": "产品导出功能已触发，请在系统通知中查看导出结果"
    }

# --- 动态路由在后 ---
@router.get("", response_model=ProductListResponse)
async def get_products(
    project_id: uuid.UUID = Path(...),
    page: int = Query(1, ge=1),
    page_size: int = Query(10, ge=1, le=1000),
    search: Optional[str] = None,
    brand_id: Optional[uuid.UUID] = None,
    category_id: Optional[uuid.UUID] = None,
    is_active: Optional[bool] = None,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """获取产品列表"""
    try:
        # 计算分页偏移量
        offset = (page - 1) * page_size

        # 构建查询条件
        query = select(
            Product,
            ProductBrand.name.label("brand_name"),
            ProductCategory.name.label("category_name")
        ).outerjoin(
            ProductBrand, Product.brand_id == ProductBrand.id
        ).outerjoin(
            ProductCategory, Product.category_id == ProductCategory.id
        ).filter(
            Product.project_id == project_id
        )

        # 应用过滤条件
        if search:
            query = query.filter(
                or_(
                    Product.name.ilike(f"%{search}%"),
                    Product.sku.ilike(f"%{search}%"),
                    Product.barcode.ilike(f"%{search}%")
                )
            )

        if brand_id:
            query = query.filter(Product.brand_id == brand_id)

        if category_id:
            query = query.filter(Product.category_id == category_id)

        if is_active is not None:
            query = query.filter(Product.is_active == is_active)

        # 获取总数
        count_stmt = select(func.count()).select_from(query.subquery())
        total = (await db.execute(count_stmt)).scalar()

        # 获取分页数据
        products_result = await db.execute(query.order_by(Product.created_at.desc()).offset(offset).limit(page_size))
        products_data = products_result.all()

        # 转换为响应格式
        products = []
        for product, brand_name, category_name in products_data:
            product_dict = {
                "id": product.id,
                "tenant_id": product.tenant_id,
                "project_id": product.project_id,
                "product_group_id": str(product.product_group_id) if product.product_group_id else None,  # 商品分组ID
                "name": product.name,
                "sku": product.sku,
                "barcode": product.barcode,
                "brand_id": product.brand_id,
                "category_id": product.category_id,
                "description": product.description,
                "specification": product.specification,
                "unit": product.unit,
                "cost_price": product.cost_price,
                "retail_price": product.retail_price,
                "image_url": product.image_url,
                "is_active": product.is_active,
                "is_variant": product.is_variant,  # 是否为变体商品
                "display_name": product.display_name,  # 显示名称（包含规格）
                "created_at": product.created_at,
                "updated_at": product.updated_at,
                "created_by": product.created_by,
                "updated_by": product.updated_by,
                "brand_name": brand_name,
                "category_name": category_name
            }
            products.append(product_dict)

        return {
            "success": True,
            "data": products,
            "total": total,
            "page": page,
            "page_size": page_size,
            "pages": (total + page_size - 1) // page_size
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取产品列表失败: {str(e)}"
        )

@router.get("/{product_id}", response_model=ProductResponse)
async def get_product(
    project_id: uuid.UUID = Path(...),
    product_id: uuid.UUID = Path(...),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """获取产品详情"""
    try:
        # 查询产品
        query = select(
            Product,
            ProductBrand.name.label("brand_name"),
            ProductCategory.name.label("category_name")
        ).outerjoin(
            ProductBrand, Product.brand_id == ProductBrand.id
        ).outerjoin(
            ProductCategory, Product.category_id == ProductCategory.id
        ).filter(
            Product.id == product_id,
            Product.project_id == project_id
        )

        product_result = await db.execute(query)
        product_data = product_result.first()

        if not product_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="产品不存在"
            )

        product, brand_name, category_name = product_data

        # 转换为响应格式
        product_dict = {
            "id": product.id,
            "tenant_id": product.tenant_id,
            "project_id": product.project_id,
            "product_group_id": str(product.product_group_id) if product.product_group_id else None,  # 商品分组ID
            "name": product.name,
            "sku": product.sku,
            "barcode": product.barcode,
            "brand_id": product.brand_id,
            "category_id": product.category_id,
            "description": product.description,
            "specification": product.specification,
            "unit": product.unit,
            "cost_price": product.cost_price,
            "retail_price": product.retail_price,
            "image_url": product.image_url,
            "is_active": product.is_active,
            "is_variant": product.is_variant,  # 是否为变体商品
            "display_name": product.display_name,  # 显示名称（包含规格）
            "created_at": product.created_at,
            "updated_at": product.updated_at,
            "created_by": product.created_by,
            "updated_by": product.updated_by,
            "brand_name": brand_name,
            "category_name": category_name
        }

        return {
            "success": True,
            "data": product_dict
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取产品详情失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取产品详情失败: {str(e)}"
        )

@router.post("", response_model=ProductResponse, status_code=status.HTTP_201_CREATED)
async def create_product(
    product: ProductCreate,
    current_user: User = Depends(get_current_user),
    project_id: uuid.UUID = Path(...),
    db: AsyncSession = Depends(get_db)
):
    """创建产品"""
    try:
        # 检查SKU是否已存在
        query = select(Product).filter(
            Product.project_id == project_id,
            Product.sku == product.sku
        )

        existing_product = await db.execute(query)
        existing_product = existing_product.scalars().first()

        if existing_product:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="SKU已存在"
            )

        # 创建产品
        new_product = Product(
            id=uuid.uuid4(),
            tenant_id=current_user.tenant_id,
            project_id=project_id,
            name=product.name,
            sku=product.sku,
            barcode=product.barcode,
            brand_id=product.brand_id,
            category_id=product.category_id,
            description=product.description,
            specification=product.specification,
            unit=getattr(product, 'unit', None) or '公斤',
            cost_price=product.cost_price,
            retail_price=product.retail_price,
            image_url=product.image_url,
            is_active=product.is_active,
            is_variant=bool(product.specification),  # 如果有规格说明则标记为变体商品
            created_by=current_user.id,
            updated_by=current_user.id
        )

        db.add(new_product)
        await db.flush()
        await db.refresh(new_product)
        
        # 设置product_group_id为自己的id（新商品的默认行为）
        new_product.product_group_id = new_product.id
        await db.commit()
        await db.refresh(new_product)

        # 查询品牌和分类名称
        brand_name = None
        category_name = None

        if new_product.brand_id:
            brand = await db.execute(select(ProductBrand).filter(ProductBrand.id == new_product.brand_id))
            brand = brand.scalars().first()
            if brand:
                brand_name = brand.name

        if new_product.category_id:
            category = await db.execute(select(ProductCategory).filter(ProductCategory.id == new_product.category_id))
            category = category.scalars().first()
            if category:
                category_name = category.name

        # 转换为响应格式
        product_dict = {
            "id": new_product.id,
            "tenant_id": new_product.tenant_id,
            "project_id": new_product.project_id,
            "product_group_id": str(new_product.product_group_id) if new_product.product_group_id else None,  # 商品分组ID
            "name": new_product.name,
            "sku": new_product.sku,
            "barcode": new_product.barcode,
            "brand_id": new_product.brand_id,
            "category_id": new_product.category_id,
            "description": new_product.description,
            "specification": new_product.specification,
            "unit": new_product.unit,
            "cost_price": new_product.cost_price,
            "retail_price": new_product.retail_price,
            "image_url": new_product.image_url,
            "is_active": new_product.is_active,
            "is_variant": new_product.is_variant,  # 是否为变体商品
            "display_name": new_product.display_name,  # 显示名称（包含规格）
            "created_at": new_product.created_at,
            "updated_at": new_product.updated_at,
            "created_by": new_product.created_by,
            "updated_by": new_product.updated_by,
            "brand_name": brand_name,
            "category_name": category_name
        }

        return {
            "success": True,
            "message": "产品创建成功",
            "data": product_dict
        }
    except HTTPException:
        await db.rollback()
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建产品失败: {str(e)}"
        )

@router.put("/{product_id}", response_model=ProductResponse)
async def update_product(
    product: ProductUpdate,
    project_id: uuid.UUID = Path(...),
    product_id: uuid.UUID = Path(...),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """更新产品"""
    try:
        # 查询产品
        query = select(Product).filter(
            Product.id == product_id,
            Product.project_id == project_id
        )

        result = await db.execute(query)
        db_product = result.scalars().first()
        if not db_product:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="产品不存在"
            )

        # 只在 sku 有值且变更时才做唯一性校验
        if product.sku is not None and product.sku != db_product.sku:
            query = select(Product).filter(
                Product.project_id == project_id,
                Product.sku == product.sku,
                Product.id != product_id
            )
            existing_product = await db.execute(query)
            existing_product = existing_product.scalars().first()
            if existing_product:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="SKU已存在"
                )

        # 更新产品
        update_data = product.dict(exclude_unset=True)
        update_data["updated_by"] = current_user.id
        update_data["updated_at"] = datetime.now()
        if "unit" not in update_data:
            update_data["unit"] = db_product.unit or "公斤"
        for key, value in update_data.items():
            setattr(db_product, key, value)

        await db.commit()
        await db.refresh(db_product)

        # 查询品牌和分类名称
        brand_name = None
        category_name = None

        if db_product.brand_id:
            brand = await db.execute(select(ProductBrand).filter(ProductBrand.id == db_product.brand_id))
            brand = brand.scalars().first()
            if brand:
                brand_name = brand.name

        if db_product.category_id:
            category = await db.execute(select(ProductCategory).filter(ProductCategory.id == db_product.category_id))
            category = category.scalars().first()
            if category:
                category_name = category.name

        # 转换为响应格式
        product_dict = {
            "id": db_product.id,
            "tenant_id": db_product.tenant_id,
            "project_id": db_product.project_id,
            "name": db_product.name,
            "sku": db_product.sku,
            "barcode": db_product.barcode,
            "brand_id": db_product.brand_id,
            "category_id": db_product.category_id,
            "description": db_product.description,
            "specification": db_product.specification,
            "unit": db_product.unit,
            "cost_price": db_product.cost_price,
            "retail_price": db_product.retail_price,
            "image_url": db_product.image_url,
            "is_active": db_product.is_active,
            "created_at": db_product.created_at,
            "updated_at": db_product.updated_at,
            "created_by": db_product.created_by,
            "updated_by": db_product.updated_by,
            "brand_name": brand_name,
            "category_name": category_name
        }

        return {
            "success": True,
            "message": "产品更新成功",
            "data": product_dict
        }
    except HTTPException:
        await db.rollback()
        raise
    except Exception as e:
        print(traceback.format_exc())
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新产品失败: {str(e)}"
        )

@router.delete("/{product_id}", response_model=Dict[str, Any])
async def delete_product(
    project_id: uuid.UUID = Path(...),
    product_id: uuid.UUID = Path(...),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """删除产品"""
    try:
        # 查询产品
        query = select(Product).filter(
            Product.id == product_id,
            Product.project_id == project_id
        )

        db_product_result = await db.execute(query)
        db_product = db_product_result.scalars().first()

        if not db_product:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="产品不存在"
            )

        # 检查产品是否有关联的库存
        query = select(func.count(InventoryItem.id)).filter(
            InventoryItem.product_id == product_id
        )

        inventory_items_count = await db.execute(query)
        inventory_items_count = inventory_items_count.scalar()

        if inventory_items_count > 0:
            # 软删除，将产品标记为非活动状态
            db_product.is_active = False
            db_product.updated_by = current_user.id
            db_product.updated_at = datetime.now()
            await db.commit()

            return {
                "success": True,
                "message": "产品已标记为非活动状态，因为存在关联的库存记录"
            }
        else:
            # 硬删除
            await db.delete(db_product)
            await db.commit()

            return {
                "success": True,
                "message": "产品删除成功"
            }
    except HTTPException:
        await db.rollback()
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除产品失败: {str(e)}"
        )

@router.delete("/batch", response_model=Dict[str, Any])
async def batch_delete_products(
    project_id: uuid.UUID = Path(...),
    product_ids: List[uuid.UUID] = Body(..., description="要删除的产品ID列表"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """批量删除产品"""
    try:
        if not product_ids:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="请至少选择一个产品进行删除"
            )

        # 查询要删除的产品
        query = select(Product).filter(
            Product.id.in_(product_ids),
            Product.project_id == project_id
        )
        
        result = await db.execute(query)
        products_to_delete = result.scalars().all()
        
        if not products_to_delete:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="未找到要删除的产品"
            )

        # 检查是否有产品的ID不存在
        found_ids = {p.id for p in products_to_delete}
        missing_ids = set(product_ids) - found_ids
        if missing_ids:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"以下产品ID不存在：{list(missing_ids)}"
            )

        success_count = 0
        soft_delete_count = 0
        errors = []

        for product in products_to_delete:
            try:
                # 检查产品是否有关联的库存
                inventory_query = select(func.count(InventoryItem.id)).filter(
                    InventoryItem.product_id == product.id
                )
                inventory_count = await db.execute(inventory_query)
                inventory_count = inventory_count.scalar()

                if inventory_count > 0:
                    # 软删除：标记为非活动状态
                    product.is_active = False
                    product.updated_by = current_user.id
                    product.updated_at = datetime.now()
                    soft_delete_count += 1
                else:
                    # 硬删除
                    await db.delete(product)
                    success_count += 1

            except Exception as e:
                errors.append(f"删除产品 {product.name} 失败：{str(e)}")

        await db.commit()

        # 准备响应消息
        messages = []
        if success_count > 0:
            messages.append(f"成功删除 {success_count} 个产品")
        if soft_delete_count > 0:
            messages.append(f"{soft_delete_count} 个产品因有库存记录已标记为非活动状态")
        if errors:
            messages.append(f"失败 {len(errors)} 个：{'; '.join(errors)}")

        return {
            "success": True,
            "message": "； ".join(messages),
            "data": {
                "total_requested": len(product_ids),
                "success_count": success_count,
                "soft_delete_count": soft_delete_count,
                "error_count": len(errors),
                "errors": errors
            }
        }

    except HTTPException:
        await db.rollback()
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"批量删除产品失败: {str(e)}"
        )

@router.get("/variants/{product_group_id}", response_model=Dict[str, Any])
async def get_product_variants(
    project_id: uuid.UUID = Path(...),
    product_group_id: uuid.UUID = Path(...),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """获取商品的所有变体"""
    try:
        # 查询商品组下的所有变体
        query = select(
            Product,
            ProductBrand.name.label("brand_name"),
            ProductCategory.name.label("category_name")
        ).outerjoin(
            ProductBrand, Product.brand_id == ProductBrand.id
        ).outerjoin(
            ProductCategory, Product.category_id == ProductCategory.id
        ).filter(
            Product.project_id == project_id,
            Product.product_group_id == product_group_id,
            Product.is_active == True
        ).order_by(Product.specification)

        products_result = await db.execute(query)
        products_data = products_result.all()

        if not products_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="商品组不存在或无变体"
            )

        # 转换为响应格式
        variants = []
        for product, brand_name, category_name in products_data:
            variant_dict = {
                "id": product.id,
                "name": product.name,
                "sku": product.sku,
                "specification": product.specification,
                "unit": product.unit,
                "cost_price": product.cost_price,
                "retail_price": product.retail_price,
                "is_variant": product.is_variant,
                "display_name": product.display_name,
                "brand_name": brand_name,
                "category_name": category_name,
                "created_at": product.created_at
            }
            variants.append(variant_dict)

        return {
            "success": True,
            "message": f"找到 {len(variants)} 个商品变体",
            "data": variants,
            "total": len(variants),
            "product_group_id": str(product_group_id)
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取商品变体失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取商品变体失败: {str(e)}"
        )

@router.post("/create-variant", response_model=ProductResponse, status_code=status.HTTP_201_CREATED)
async def create_product_variant(
    variant_data: Dict[str, Any],
    project_id: uuid.UUID = Path(...),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """基于现有商品创建变体"""
    try:
        base_product_id = variant_data.get("base_product_id")
        new_specification = variant_data.get("specification")
        new_unit = variant_data.get("unit")
        new_price = variant_data.get("cost_price")

        if not base_product_id or not new_specification:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="缺少必要参数: base_product_id 和 specification"
            )

        # 获取基础商品
        base_product = await db.get(Product, uuid.UUID(base_product_id))
        if not base_product or base_product.project_id != project_id:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="基础商品不存在"
            )

        # 使用ProductService创建变体
        from services.product_service import ProductService
        
        variant_product = await ProductService._create_variant_product(
            db=db,
            base_product=base_product,
            new_specification=new_specification,
            new_unit=new_unit,
            new_price=new_price,
            user_id=current_user.id
        )

        # 查询品牌和分类名称
        brand_name = None
        category_name = None

        if variant_product.brand_id:
            brand = await db.execute(select(ProductBrand).filter(ProductBrand.id == variant_product.brand_id))
            brand = brand.scalars().first()
            if brand:
                brand_name = brand.name

        if variant_product.category_id:
            category = await db.execute(select(ProductCategory).filter(ProductCategory.id == variant_product.category_id))
            category = category.scalars().first()
            if category:
                category_name = category.name

        # 转换为响应格式
        product_dict = {
            "id": variant_product.id,
            "tenant_id": variant_product.tenant_id,
            "project_id": variant_product.project_id,
            "product_group_id": str(variant_product.product_group_id) if variant_product.product_group_id else None,
            "name": variant_product.name,
            "sku": variant_product.sku,
            "specification": variant_product.specification,
            "unit": variant_product.unit,
            "cost_price": variant_product.cost_price,
            "retail_price": variant_product.retail_price,
            "is_active": variant_product.is_active,
            "is_variant": variant_product.is_variant,
            "display_name": variant_product.display_name,
            "created_at": variant_product.created_at,
            "updated_at": variant_product.updated_at,
            "brand_name": brand_name,
            "category_name": category_name
        }

        return {
            "success": True,
            "message": "商品变体创建成功",
            "data": product_dict
        }

    except HTTPException:
        await db.rollback()
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"创建商品变体失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建商品变体失败: {str(e)}"
        )
