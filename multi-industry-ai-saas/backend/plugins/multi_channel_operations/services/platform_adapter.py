#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
全渠道运营助手 - 平台适配器服务
"""

import logging
import asyncio
from typing import Dict, List, Optional, Any, Union
from abc import ABC, abstractmethod
import httpx
import json
from datetime import datetime

logger = logging.getLogger(__name__)


class BasePlatformAdapter(ABC):
    """平台适配器基类"""
    
    def __init__(self, platform_code: str, config: Dict[str, Any]):
        self.platform_code = platform_code
        self.config = config
        self.api_credentials = config.get('api_credentials', {})
        self.platform_config = config.get('platform_config', {})
        
    @abstractmethod
    async def test_connection(self) -> Dict[str, Any]:
        """测试API连接"""
        pass
        
    @abstractmethod
    async def get_products(self, **kwargs) -> List[Dict[str, Any]]:
        """获取商品列表"""
        pass
        
    @abstractmethod
    async def create_product(self, product_data: Dict[str, Any]) -> Dict[str, Any]:
        """创建商品"""
        pass
        
    @abstractmethod
    async def update_product(self, external_id: str, product_data: Dict[str, Any]) -> Dict[str, Any]:
        """更新商品"""
        pass
        
    @abstractmethod
    async def delete_product(self, external_id: str) -> bool:
        """删除商品"""
        pass
        
    @abstractmethod
    async def update_price(self, external_id: str, price: float) -> bool:
        """更新价格"""
        pass
        
    @abstractmethod
    async def update_stock(self, external_id: str, stock: int) -> bool:
        """更新库存"""
        pass
        
    @abstractmethod
    async def get_orders(self, **kwargs) -> List[Dict[str, Any]]:
        """获取订单列表"""
        pass


class MeituanAdapter(BasePlatformAdapter):
    """美团平台适配器"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__("meituan", config)
        self.base_url = "https://openapi.meituan.com"
        self.app_id = self.api_credentials.get('app_id')
        self.app_secret = self.api_credentials.get('app_secret')
        self.access_token = self.api_credentials.get('access_token')
        
    async def test_connection(self) -> Dict[str, Any]:
        """测试美团API连接"""
        try:
            async with httpx.AsyncClient() as client:
                headers = {
                    'Authorization': f'Bearer {self.access_token}',
                    'Content-Type': 'application/json'
                }
                
                # 调用美团店铺信息接口测试连接
                response = await client.get(
                    f"{self.base_url}/api/v1/shop/info",
                    headers=headers,
                    timeout=30
                )
                
                if response.status_code == 200:
                    data = response.json()
                    return {
                        'success': True,
                        'message': '连接成功',
                        'shop_info': data.get('data', {})
                    }
                else:
                    return {
                        'success': False,
                        'message': f'连接失败: {response.status_code}',
                        'error': response.text
                    }
                    
        except Exception as e:
            logger.error(f"美团API连接测试失败: {e}")
            return {
                'success': False,
                'message': f'连接异常: {str(e)}'
            }
    
    async def get_products(self, **kwargs) -> List[Dict[str, Any]]:
        """获取美团商品列表"""
        try:
            async with httpx.AsyncClient() as client:
                headers = {
                    'Authorization': f'Bearer {self.access_token}',
                    'Content-Type': 'application/json'
                }
                
                params = {
                    'page': kwargs.get('page', 1),
                    'limit': kwargs.get('limit', 50)
                }
                
                response = await client.get(
                    f"{self.base_url}/api/v1/products",
                    headers=headers,
                    params=params,
                    timeout=30
                )
                
                if response.status_code == 200:
                    data = response.json()
                    return data.get('data', {}).get('products', [])
                else:
                    logger.error(f"获取美团商品失败: {response.status_code} - {response.text}")
                    return []
                    
        except Exception as e:
            logger.error(f"获取美团商品异常: {e}")
            return []
    
    async def create_product(self, product_data: Dict[str, Any]) -> Dict[str, Any]:
        """创建美团商品"""
        try:
            async with httpx.AsyncClient() as client:
                headers = {
                    'Authorization': f'Bearer {self.access_token}',
                    'Content-Type': 'application/json'
                }
                
                # 转换为美团API格式
                meituan_data = self._convert_to_meituan_format(product_data)
                
                response = await client.post(
                    f"{self.base_url}/api/v1/products",
                    headers=headers,
                    json=meituan_data,
                    timeout=30
                )
                
                if response.status_code == 200:
                    data = response.json()
                    return {
                        'success': True,
                        'external_id': data.get('data', {}).get('product_id'),
                        'data': data.get('data', {})
                    }
                else:
                    return {
                        'success': False,
                        'error': f'创建失败: {response.status_code} - {response.text}'
                    }
                    
        except Exception as e:
            logger.error(f"创建美团商品异常: {e}")
            return {
                'success': False,
                'error': f'创建异常: {str(e)}'
            }
    
    async def update_product(self, external_id: str, product_data: Dict[str, Any]) -> Dict[str, Any]:
        """更新美团商品"""
        try:
            async with httpx.AsyncClient() as client:
                headers = {
                    'Authorization': f'Bearer {self.access_token}',
                    'Content-Type': 'application/json'
                }
                
                meituan_data = self._convert_to_meituan_format(product_data)
                
                response = await client.put(
                    f"{self.base_url}/api/v1/products/{external_id}",
                    headers=headers,
                    json=meituan_data,
                    timeout=30
                )
                
                return {
                    'success': response.status_code == 200,
                    'data': response.json() if response.status_code == 200 else None,
                    'error': response.text if response.status_code != 200 else None
                }
                
        except Exception as e:
            logger.error(f"更新美团商品异常: {e}")
            return {
                'success': False,
                'error': f'更新异常: {str(e)}'
            }
    
    async def delete_product(self, external_id: str) -> bool:
        """删除美团商品"""
        try:
            async with httpx.AsyncClient() as client:
                headers = {
                    'Authorization': f'Bearer {self.access_token}',
                    'Content-Type': 'application/json'
                }
                
                response = await client.delete(
                    f"{self.base_url}/api/v1/products/{external_id}",
                    headers=headers,
                    timeout=30
                )
                
                return response.status_code == 200
                
        except Exception as e:
            logger.error(f"删除美团商品异常: {e}")
            return False
    
    async def update_price(self, external_id: str, price: float) -> bool:
        """更新美团商品价格"""
        try:
            async with httpx.AsyncClient() as client:
                headers = {
                    'Authorization': f'Bearer {self.access_token}',
                    'Content-Type': 'application/json'
                }
                
                data = {'price': price}
                
                response = await client.patch(
                    f"{self.base_url}/api/v1/products/{external_id}/price",
                    headers=headers,
                    json=data,
                    timeout=30
                )
                
                return response.status_code == 200
                
        except Exception as e:
            logger.error(f"更新美团商品价格异常: {e}")
            return False
    
    async def update_stock(self, external_id: str, stock: int) -> bool:
        """更新美团商品库存"""
        try:
            async with httpx.AsyncClient() as client:
                headers = {
                    'Authorization': f'Bearer {self.access_token}',
                    'Content-Type': 'application/json'
                }
                
                data = {'stock': stock}
                
                response = await client.patch(
                    f"{self.base_url}/api/v1/products/{external_id}/stock",
                    headers=headers,
                    json=data,
                    timeout=30
                )
                
                return response.status_code == 200
                
        except Exception as e:
            logger.error(f"更新美团商品库存异常: {e}")
            return False
    
    async def get_orders(self, **kwargs) -> List[Dict[str, Any]]:
        """获取美团订单列表"""
        try:
            async with httpx.AsyncClient() as client:
                headers = {
                    'Authorization': f'Bearer {self.access_token}',
                    'Content-Type': 'application/json'
                }
                
                params = {
                    'start_date': kwargs.get('start_date'),
                    'end_date': kwargs.get('end_date'),
                    'page': kwargs.get('page', 1),
                    'limit': kwargs.get('limit', 50)
                }
                
                response = await client.get(
                    f"{self.base_url}/api/v1/orders",
                    headers=headers,
                    params=params,
                    timeout=30
                )
                
                if response.status_code == 200:
                    data = response.json()
                    return data.get('data', {}).get('orders', [])
                else:
                    logger.error(f"获取美团订单失败: {response.status_code} - {response.text}")
                    return []
                    
        except Exception as e:
            logger.error(f"获取美团订单异常: {e}")
            return []
    
    def _convert_to_meituan_format(self, product_data: Dict[str, Any]) -> Dict[str, Any]:
        """转换为美团API格式"""
        return {
            'name': product_data.get('name'),
            'description': product_data.get('description'),
            'price': product_data.get('price'),
            'stock': product_data.get('stock'),
            'category_id': product_data.get('category_id'),
            'images': product_data.get('images', []),
            'specifications': product_data.get('specifications', [])
        }


class DouyinAdapter(BasePlatformAdapter):
    """抖音平台适配器"""

    def __init__(self, config: Dict[str, Any]):
        super().__init__("douyin", config)
        self.base_url = "https://openapi.douyin.com"
        self.app_id = self.api_credentials.get('app_id')
        self.app_secret = self.api_credentials.get('app_secret')
        self.access_token = self.api_credentials.get('access_token')

    async def test_connection(self) -> Dict[str, Any]:
        """测试抖音API连接"""
        try:
            async with httpx.AsyncClient() as client:
                headers = {
                    'Authorization': f'Bearer {self.access_token}',
                    'Content-Type': 'application/json'
                }

                response = await client.get(
                    f"{self.base_url}/api/v1/shop/info",
                    headers=headers,
                    timeout=30
                )

                if response.status_code == 200:
                    data = response.json()
                    return {
                        'success': True,
                        'message': '连接成功',
                        'shop_info': data.get('data', {})
                    }
                else:
                    return {
                        'success': False,
                        'message': f'连接失败: {response.status_code}',
                        'error': response.text
                    }

        except Exception as e:
            logger.error(f"抖音API连接测试失败: {e}")
            return {
                'success': False,
                'message': f'连接异常: {str(e)}'
            }

    async def get_products(self, **kwargs) -> List[Dict[str, Any]]:
        """获取抖音商品列表"""
        try:
            async with httpx.AsyncClient() as client:
                headers = {
                    'Authorization': f'Bearer {self.access_token}',
                    'Content-Type': 'application/json'
                }

                params = {
                    'page': kwargs.get('page', 1),
                    'size': kwargs.get('limit', 50)
                }

                response = await client.get(
                    f"{self.base_url}/api/v1/product/list",
                    headers=headers,
                    params=params,
                    timeout=30
                )

                if response.status_code == 200:
                    data = response.json()
                    return data.get('data', {}).get('list', [])
                else:
                    logger.error(f"获取抖音商品失败: {response.status_code} - {response.text}")
                    return []

        except Exception as e:
            logger.error(f"获取抖音商品异常: {e}")
            return []

    async def create_product(self, product_data: Dict[str, Any]) -> Dict[str, Any]:
        """创建抖音商品"""
        try:
            async with httpx.AsyncClient() as client:
                headers = {
                    'Authorization': f'Bearer {self.access_token}',
                    'Content-Type': 'application/json'
                }

                douyin_data = self._convert_to_douyin_format(product_data)

                response = await client.post(
                    f"{self.base_url}/api/v1/product/create",
                    headers=headers,
                    json=douyin_data,
                    timeout=30
                )

                if response.status_code == 200:
                    data = response.json()
                    return {
                        'success': True,
                        'external_id': data.get('data', {}).get('product_id'),
                        'data': data.get('data', {})
                    }
                else:
                    return {
                        'success': False,
                        'error': f'创建失败: {response.status_code} - {response.text}'
                    }

        except Exception as e:
            logger.error(f"创建抖音商品异常: {e}")
            return {
                'success': False,
                'error': f'创建异常: {str(e)}'
            }

    async def update_product(self, external_id: str, product_data: Dict[str, Any]) -> Dict[str, Any]:
        """更新抖音商品"""
        try:
            async with httpx.AsyncClient() as client:
                headers = {
                    'Authorization': f'Bearer {self.access_token}',
                    'Content-Type': 'application/json'
                }

                douyin_data = self._convert_to_douyin_format(product_data)
                douyin_data['product_id'] = external_id

                response = await client.post(
                    f"{self.base_url}/api/v1/product/update",
                    headers=headers,
                    json=douyin_data,
                    timeout=30
                )

                return {
                    'success': response.status_code == 200,
                    'data': response.json() if response.status_code == 200 else None,
                    'error': response.text if response.status_code != 200 else None
                }

        except Exception as e:
            logger.error(f"更新抖音商品异常: {e}")
            return {
                'success': False,
                'error': f'更新异常: {str(e)}'
            }

    async def delete_product(self, external_id: str) -> bool:
        """删除抖音商品"""
        try:
            async with httpx.AsyncClient() as client:
                headers = {
                    'Authorization': f'Bearer {self.access_token}',
                    'Content-Type': 'application/json'
                }

                data = {'product_id': external_id}

                response = await client.post(
                    f"{self.base_url}/api/v1/product/delete",
                    headers=headers,
                    json=data,
                    timeout=30
                )

                return response.status_code == 200

        except Exception as e:
            logger.error(f"删除抖音商品异常: {e}")
            return False

    async def update_price(self, external_id: str, price: float) -> bool:
        """更新抖音商品价格"""
        try:
            async with httpx.AsyncClient() as client:
                headers = {
                    'Authorization': f'Bearer {self.access_token}',
                    'Content-Type': 'application/json'
                }

                data = {
                    'product_id': external_id,
                    'price': int(price * 100)  # 抖音价格以分为单位
                }

                response = await client.post(
                    f"{self.base_url}/api/v1/product/price/update",
                    headers=headers,
                    json=data,
                    timeout=30
                )

                return response.status_code == 200

        except Exception as e:
            logger.error(f"更新抖音商品价格异常: {e}")
            return False

    async def update_stock(self, external_id: str, stock: int) -> bool:
        """更新抖音商品库存"""
        try:
            async with httpx.AsyncClient() as client:
                headers = {
                    'Authorization': f'Bearer {self.access_token}',
                    'Content-Type': 'application/json'
                }

                data = {
                    'product_id': external_id,
                    'stock_num': stock
                }

                response = await client.post(
                    f"{self.base_url}/api/v1/product/stock/update",
                    headers=headers,
                    json=data,
                    timeout=30
                )

                return response.status_code == 200

        except Exception as e:
            logger.error(f"更新抖音商品库存异常: {e}")
            return False

    async def get_orders(self, **kwargs) -> List[Dict[str, Any]]:
        """获取抖音订单列表"""
        try:
            async with httpx.AsyncClient() as client:
                headers = {
                    'Authorization': f'Bearer {self.access_token}',
                    'Content-Type': 'application/json'
                }

                params = {
                    'start_time': kwargs.get('start_date'),
                    'end_time': kwargs.get('end_date'),
                    'page': kwargs.get('page', 1),
                    'size': kwargs.get('limit', 50)
                }

                response = await client.get(
                    f"{self.base_url}/api/v1/order/list",
                    headers=headers,
                    params=params,
                    timeout=30
                )

                if response.status_code == 200:
                    data = response.json()
                    return data.get('data', {}).get('list', [])
                else:
                    logger.error(f"获取抖音订单失败: {response.status_code} - {response.text}")
                    return []

        except Exception as e:
            logger.error(f"获取抖音订单异常: {e}")
            return []

    def _convert_to_douyin_format(self, product_data: Dict[str, Any]) -> Dict[str, Any]:
        """转换为抖音API格式"""
        return {
            'name': product_data.get('name'),
            'description': product_data.get('description'),
            'price': int(product_data.get('price', 0) * 100),  # 转换为分
            'stock_num': product_data.get('stock'),
            'category_id': product_data.get('category_id'),
            'pic': product_data.get('images', []),
            'spec_info': product_data.get('specifications', [])
        }


class ElemeAdapter(BasePlatformAdapter):
    """饿了么平台适配器"""

    def __init__(self, config: Dict[str, Any]):
        super().__init__("eleme", config)
        self.base_url = "https://open-api.shop.ele.me"
        self.app_key = self.api_credentials.get('app_key')
        self.app_secret = self.api_credentials.get('app_secret')
        self.access_token = self.api_credentials.get('access_token')

    async def test_connection(self) -> Dict[str, Any]:
        """测试饿了么API连接"""
        try:
            async with httpx.AsyncClient() as client:
                headers = {
                    'Authorization': f'Bearer {self.access_token}',
                    'Content-Type': 'application/json'
                }

                response = await client.get(
                    f"{self.base_url}/api/v1/shop",
                    headers=headers,
                    timeout=30
                )

                if response.status_code == 200:
                    data = response.json()
                    return {
                        'success': True,
                        'message': '连接成功',
                        'shop_info': data
                    }
                else:
                    return {
                        'success': False,
                        'message': f'连接失败: {response.status_code}',
                        'error': response.text
                    }

        except Exception as e:
            logger.error(f"饿了么API连接测试失败: {e}")
            return {
                'success': False,
                'message': f'连接异常: {str(e)}'
            }

    async def get_products(self, **kwargs) -> List[Dict[str, Any]]:
        """获取饿了么商品列表"""
        try:
            async with httpx.AsyncClient() as client:
                headers = {
                    'Authorization': f'Bearer {self.access_token}',
                    'Content-Type': 'application/json'
                }

                params = {
                    'offset': (kwargs.get('page', 1) - 1) * kwargs.get('limit', 50),
                    'limit': kwargs.get('limit', 50)
                }

                response = await client.get(
                    f"{self.base_url}/api/v1/products",
                    headers=headers,
                    params=params,
                    timeout=30
                )

                if response.status_code == 200:
                    data = response.json()
                    return data if isinstance(data, list) else []
                else:
                    logger.error(f"获取饿了么商品失败: {response.status_code} - {response.text}")
                    return []

        except Exception as e:
            logger.error(f"获取饿了么商品异常: {e}")
            return []

    async def create_product(self, product_data: Dict[str, Any]) -> Dict[str, Any]:
        """创建饿了么商品"""
        try:
            async with httpx.AsyncClient() as client:
                headers = {
                    'Authorization': f'Bearer {self.access_token}',
                    'Content-Type': 'application/json'
                }

                eleme_data = self._convert_to_eleme_format(product_data)

                response = await client.post(
                    f"{self.base_url}/api/v1/products",
                    headers=headers,
                    json=eleme_data,
                    timeout=30
                )

                if response.status_code == 200:
                    data = response.json()
                    return {
                        'success': True,
                        'external_id': data.get('id'),
                        'data': data
                    }
                else:
                    return {
                        'success': False,
                        'error': f'创建失败: {response.status_code} - {response.text}'
                    }

        except Exception as e:
            logger.error(f"创建饿了么商品异常: {e}")
            return {
                'success': False,
                'error': f'创建异常: {str(e)}'
            }

    async def update_product(self, external_id: str, product_data: Dict[str, Any]) -> Dict[str, Any]:
        """更新饿了么商品"""
        try:
            async with httpx.AsyncClient() as client:
                headers = {
                    'Authorization': f'Bearer {self.access_token}',
                    'Content-Type': 'application/json'
                }

                eleme_data = self._convert_to_eleme_format(product_data)

                response = await client.put(
                    f"{self.base_url}/api/v1/products/{external_id}",
                    headers=headers,
                    json=eleme_data,
                    timeout=30
                )

                return {
                    'success': response.status_code == 200,
                    'data': response.json() if response.status_code == 200 else None,
                    'error': response.text if response.status_code != 200 else None
                }

        except Exception as e:
            logger.error(f"更新饿了么商品异常: {e}")
            return {
                'success': False,
                'error': f'更新异常: {str(e)}'
            }

    async def delete_product(self, external_id: str) -> bool:
        """删除饿了么商品"""
        try:
            async with httpx.AsyncClient() as client:
                headers = {
                    'Authorization': f'Bearer {self.access_token}',
                    'Content-Type': 'application/json'
                }

                response = await client.delete(
                    f"{self.base_url}/api/v1/products/{external_id}",
                    headers=headers,
                    timeout=30
                )

                return response.status_code == 200

        except Exception as e:
            logger.error(f"删除饿了么商品异常: {e}")
            return False

    async def update_price(self, external_id: str, price: float) -> bool:
        """更新饿了么商品价格"""
        try:
            async with httpx.AsyncClient() as client:
                headers = {
                    'Authorization': f'Bearer {self.access_token}',
                    'Content-Type': 'application/json'
                }

                data = {'price': price}

                response = await client.patch(
                    f"{self.base_url}/api/v1/products/{external_id}",
                    headers=headers,
                    json=data,
                    timeout=30
                )

                return response.status_code == 200

        except Exception as e:
            logger.error(f"更新饿了么商品价格异常: {e}")
            return False

    async def update_stock(self, external_id: str, stock: int) -> bool:
        """更新饿了么商品库存"""
        try:
            async with httpx.AsyncClient() as client:
                headers = {
                    'Authorization': f'Bearer {self.access_token}',
                    'Content-Type': 'application/json'
                }

                data = {'stock': stock}

                response = await client.patch(
                    f"{self.base_url}/api/v1/products/{external_id}",
                    headers=headers,
                    json=data,
                    timeout=30
                )

                return response.status_code == 200

        except Exception as e:
            logger.error(f"更新饿了么商品库存异常: {e}")
            return False

    async def get_orders(self, **kwargs) -> List[Dict[str, Any]]:
        """获取饿了么订单列表"""
        try:
            async with httpx.AsyncClient() as client:
                headers = {
                    'Authorization': f'Bearer {self.access_token}',
                    'Content-Type': 'application/json'
                }

                params = {
                    'since': kwargs.get('start_date'),
                    'until': kwargs.get('end_date'),
                    'offset': (kwargs.get('page', 1) - 1) * kwargs.get('limit', 50),
                    'limit': kwargs.get('limit', 50)
                }

                response = await client.get(
                    f"{self.base_url}/api/v1/orders",
                    headers=headers,
                    params=params,
                    timeout=30
                )

                if response.status_code == 200:
                    data = response.json()
                    return data if isinstance(data, list) else []
                else:
                    logger.error(f"获取饿了么订单失败: {response.status_code} - {response.text}")
                    return []

        except Exception as e:
            logger.error(f"获取饿了么订单异常: {e}")
            return []

    def _convert_to_eleme_format(self, product_data: Dict[str, Any]) -> Dict[str, Any]:
        """转换为饿了么API格式"""
        return {
            'name': product_data.get('name'),
            'description': product_data.get('description'),
            'price': product_data.get('price'),
            'stock': product_data.get('stock'),
            'category_id': product_data.get('category_id'),
            'image_url': product_data.get('images', [None])[0],
            'specifications': product_data.get('specifications', [])
        }


class PlatformAdapterManager:
    """平台适配器管理器"""

    def __init__(self):
        self.adapters: Dict[str, BasePlatformAdapter] = {}
        self.adapter_classes = {
            'meituan': MeituanAdapter,
            'douyin': DouyinAdapter,
            'eleme': ElemeAdapter,
        }

    def register_adapter(self, platform_code: str, adapter_class):
        """注册平台适配器"""
        self.adapter_classes[platform_code] = adapter_class
        logger.info(f"注册平台适配器: {platform_code}")

    def get_adapter(self, platform_code: str, config: Dict[str, Any]) -> Optional[BasePlatformAdapter]:
        """获取平台适配器实例"""
        try:
            # 如果已经创建过实例，直接返回
            adapter_key = f"{platform_code}_{hash(str(config))}"
            if adapter_key in self.adapters:
                return self.adapters[adapter_key]

            # 创建新的适配器实例
            if platform_code in self.adapter_classes:
                adapter_class = self.adapter_classes[platform_code]
                adapter = adapter_class(config)
                self.adapters[adapter_key] = adapter
                return adapter
            else:
                logger.error(f"不支持的平台: {platform_code}")
                return None

        except Exception as e:
            logger.error(f"创建平台适配器失败: {platform_code} - {e}")
            return None

    def get_supported_platforms(self) -> List[Dict[str, Any]]:
        """获取支持的平台列表"""
        return [
            {
                'code': 'meituan',
                'name': '美团',
                'description': '美团外卖/团购平台',
                'required_credentials': ['app_id', 'app_secret', 'access_token'],
                'features': ['商品管理', '订单管理', '价格同步', '库存同步']
            },
            {
                'code': 'douyin',
                'name': '抖音',
                'description': '抖音团购/电商平台',
                'required_credentials': ['app_id', 'app_secret', 'access_token'],
                'features': ['商品管理', '订单管理', '价格同步', '库存同步']
            },
            {
                'code': 'eleme',
                'name': '饿了么',
                'description': '饿了么外卖平台',
                'required_credentials': ['app_key', 'app_secret', 'access_token'],
                'features': ['商品管理', '订单管理', '价格同步', '库存同步']
            }
        ]

    async def test_all_connections(self, configs: Dict[str, Dict[str, Any]]) -> Dict[str, Dict[str, Any]]:
        """测试所有平台连接"""
        results = {}

        for platform_code, config in configs.items():
            adapter = self.get_adapter(platform_code, config)
            if adapter:
                try:
                    result = await adapter.test_connection()
                    results[platform_code] = result
                except Exception as e:
                    results[platform_code] = {
                        'success': False,
                        'message': f'测试异常: {str(e)}'
                    }
            else:
                results[platform_code] = {
                    'success': False,
                    'message': '不支持的平台'
                }

        return results

    async def batch_sync_products(self,
                                  platform_configs: Dict[str, Dict[str, Any]],
                                  products: List[Dict[str, Any]]) -> Dict[str, List[Dict[str, Any]]]:
        """批量同步商品到多个平台"""
        results = {}

        for platform_code, config in platform_configs.items():
            adapter = self.get_adapter(platform_code, config)
            if not adapter:
                results[platform_code] = [{'error': '不支持的平台'}]
                continue

            platform_results = []
            for product in products:
                try:
                    result = await adapter.create_product(product)
                    platform_results.append(result)
                except Exception as e:
                    platform_results.append({
                        'success': False,
                        'error': f'同步异常: {str(e)}'
                    })

            results[platform_code] = platform_results

        return results

    async def batch_update_prices(self,
                                  platform_configs: Dict[str, Dict[str, Any]],
                                  price_updates: List[Dict[str, Any]]) -> Dict[str, List[bool]]:
        """批量更新价格到多个平台"""
        results = {}

        for platform_code, config in platform_configs.items():
            adapter = self.get_adapter(platform_code, config)
            if not adapter:
                results[platform_code] = [False] * len(price_updates)
                continue

            platform_results = []
            for update in price_updates:
                try:
                    success = await adapter.update_price(
                        update['external_id'],
                        update['price']
                    )
                    platform_results.append(success)
                except Exception as e:
                    logger.error(f"更新价格失败: {platform_code} - {e}")
                    platform_results.append(False)

            results[platform_code] = platform_results

        return results

    def clear_cache(self):
        """清除适配器缓存"""
        self.adapters.clear()
        logger.info("已清除平台适配器缓存")
