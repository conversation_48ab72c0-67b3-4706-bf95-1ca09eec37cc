#!/usr/bin/env python
# -*- coding: utf-8 -*-

from sqlalchemy import Column, String, Text, Boolean, ForeignKey, DateTime, JSON, Integer
from sqlalchemy.dialects.postgresql import UUID, ARRAY
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid

from db.database import Base

class MCPTool(Base):
    """MCP工具模型"""
    __tablename__ = "mcp_tools"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=True)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id"), nullable=True)
    name = Column(String(100), nullable=False, comment="工具名称")
    display_name = Column(String(100), nullable=False, comment="显示名称")
    description = Column(Text, nullable=True, comment="工具描述")
    server_url = Column(String(500), nullable=False, comment="MCP服务器URL")
    server_type = Column(String(50), nullable=False, default="sse", comment="服务器类型: streamhttp, sse, stdio")
    auth_type = Column(String(50), nullable=False, default="none", comment="认证类型: none, api_key, oauth, custom")
    auth_config = Column(JSON, nullable=True, comment="认证配置")
    capabilities = Column(JSON, nullable=True, comment="工具能力配置")
    parameters_schema = Column(JSON, nullable=True, comment="参数Schema")
    response_schema = Column(JSON, nullable=True, comment="响应Schema")
    timeout = Column(Integer, nullable=False, default=30, comment="超时时间(秒)")
    retry_count = Column(Integer, nullable=False, default=3, comment="重试次数")
    is_enabled = Column(Boolean, nullable=False, default=True, comment="是否启用")
    is_builtin = Column(Boolean, nullable=False, default=False, comment="是否内置工具")
    tags = Column(ARRAY(String), nullable=True, comment="标签")
    version = Column(String(20), nullable=True, comment="版本")
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    created_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)
    updated_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)

    # 关系
    tenant = relationship("Tenant", backref="mcp_tools")
    project = relationship("Project", backref="mcp_tools")
    creator = relationship("User", foreign_keys=[created_by])
    updater = relationship("User", foreign_keys=[updated_by])

    def __repr__(self):
        return f"<MCPTool(id={self.id}, name='{self.name}', server_url='{self.server_url}')>"


class MCPToolUsage(Base):
    """MCP工具使用记录模型"""
    __tablename__ = "mcp_tool_usages"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=True)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id"), nullable=False)
    tool_id = Column(UUID(as_uuid=True), ForeignKey("mcp_tools.id"), nullable=False)

    session_id = Column(String(100), nullable=True, comment="会话ID")
    request_data = Column(JSON, nullable=True, comment="请求数据")
    response_data = Column(JSON, nullable=True, comment="响应数据")
    status = Column(String(20), nullable=False, default="pending", comment="状态: pending, success, failed, timeout")
    error_message = Column(Text, nullable=True, comment="错误信息")
    execution_time = Column(Integer, nullable=True, comment="执行时间(毫秒)")
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    created_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)

    # 关系
    tenant = relationship("Tenant", backref="mcp_tool_usages")
    project = relationship("Project", backref="mcp_tool_usages")
    tool = relationship("MCPTool", backref="usages")

    creator = relationship("User", foreign_keys=[created_by])

    def __repr__(self):
        return f"<MCPToolUsage(id={self.id}, tool_id={self.tool_id}, status='{self.status}')>" 