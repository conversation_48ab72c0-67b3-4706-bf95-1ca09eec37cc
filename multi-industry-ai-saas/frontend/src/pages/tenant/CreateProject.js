import React, { useState, useEffect } from 'react';
import { Form, Input, Button, Card, Select, InputNumber, Typography, message, Spin } from 'antd';
import { ArrowLeftOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import api from '../../services/api';
import './CreateProject.css';

const { Title, Text } = Typography;
const { TextArea } = Input;
const { Option } = Select;

const CreateProject = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [industries, setIndustries] = useState([]);
  const [loadingIndustries, setLoadingIndustries] = useState(true);

  // 获取行业列表
  useEffect(() => {
    const fetchIndustries = async () => {
      try {
        setLoadingIndustries(true);
        const response = await api.getIndustries();

        // 处理API响应，确保我们有一个行业数组
        let industriesData = [];
        if (Array.isArray(response)) {
          industriesData = response;
        } else if (response && Array.isArray(response.items)) {
          industriesData = response.items;
        } else if (response && typeof response === 'object') {
          // 如果响应是一个对象，尝试提取行业数据
          console.log('API响应格式:', response);
          industriesData = response.industries || response.data || [];
        }

        // 如果没有获取到行业数据，添加默认行业
        if (industriesData.length === 0) {
          industriesData = [
            { id: '1', code: 'retail', name: '零售业' },
            { id: '2', code: 'restaurant', name: '餐饮业' },
            { id: '3', code: 'hotel', name: '酒店业' }
          ];
        }

        setIndustries(industriesData);
      } catch (error) {
        console.error('获取行业列表失败:', error);
        message.error('获取行业列表失败');

        // 设置默认行业
        setIndustries([
          { id: '1', code: 'retail', name: '零售业' },
          { id: '2', code: 'restaurant', name: '餐饮业' },
          { id: '3', code: 'hotel', name: '酒店业' }
        ]);
      } finally {
        setLoadingIndustries(false);
      }
    };

    fetchIndustries();
  }, []);

  // 处理表单提交
  const handleSubmit = async (values) => {
    try {
      setLoading(true);

      // 创建项目
      const response = await api.createProject({
        ...values,
        tenant_id: user?.tenant_id
      });

      // 获取创建的项目ID
      const projectId = response.id || response.project_id;

      if (projectId) {
        // 切换到项目上下文
        await api.switchProject(projectId);

        // 初始化项目数据
        try {
          const initResult = await api.initializeProject();
          console.log('项目初始化结果:', initResult);
        } catch (initError) {
          console.error('项目初始化失败:', initError);
          // 初始化失败不阻止项目创建成功
        }
      }

      message.success('项目创建成功');

      // 返回项目选择页面
      navigate('/tenant/projects');
    } catch (error) {
      console.error('创建项目失败:', error);
      message.error('创建项目失败: ' + (error.response?.data?.detail || '未知错误'));
    } finally {
      setLoading(false);
    }
  };

  // 返回项目选择页面
  const handleBack = () => {
    navigate('/tenant/projects');
  };

  return (
    <div className="create-project-container">
      <Card className="create-project-card">
        <div className="create-project-header">
          <Button
            type="link"
            icon={<ArrowLeftOutlined />}
            onClick={handleBack}
            className="back-button"
          >
            返回项目列表
          </Button>
          <Title level={2}>创建新项目</Title>
          <Text type="secondary">创建一个新的项目，设置项目基本信息</Text>
        </div>

        {loadingIndustries ? (
          <div className="loading-container">
            <Spin size="large" />
          </div>
        ) : (
          <Form
            form={form}
            layout="vertical"
            onFinish={handleSubmit}
            initialValues={{
              industry_type: 'retail',
              max_users: 5,
              max_storage_gb: 1,
              plugins: []
            }}
          >
            <Form.Item
              name="name"
              label="项目名称"
              rules={[
                { required: true, message: '请输入项目名称' },
                { max: 100, message: '项目名称不能超过100个字符' }
              ]}
            >
              <Input placeholder="请输入项目名称" />
            </Form.Item>

            <Form.Item
              name="industry_type"
              label="行业类型"
              rules={[{ required: true, message: '请选择行业类型' }]}
            >
              <Select placeholder="请选择行业类型">
                {industries.map(industry => (
                  <Option key={industry.code} value={industry.code}>
                    {industry.name}
                  </Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item
              name="description"
              label="项目描述"
            >
              <TextArea
                placeholder="请输入项目描述"
                autoSize={{ minRows: 3, maxRows: 6 }}
              />
            </Form.Item>

            <Form.Item
              name="max_users"
              label="最大用户数"
              rules={[{ required: true, message: '请输入最大用户数' }]}
            >
              <InputNumber min={1} max={100} style={{ width: '100%' }} />
            </Form.Item>

            <Form.Item
              name="max_storage_gb"
              label="最大存储空间(GB)"
              rules={[{ required: true, message: '请输入最大存储空间' }]}
            >
              <InputNumber min={1} max={100} style={{ width: '100%' }} />
            </Form.Item>

            <Form.Item
              name="plugins"
              label="启用插件"
            >
              <Select
                mode="multiple"
                placeholder="请选择要启用的插件"
                optionLabelProp="label"
              >
                <Option value="marketing" label="营销插件">
                  营销插件 - 支持多渠道营销活动管理
                </Option>
                <Option value="analytics" label="数据分析插件">
                  数据分析插件 - 提供高级数据分析和可视化
                </Option>
                <Option value="inventory" label="库存管理插件">
                  库存管理插件 - 高级库存管理和预测
                </Option>
                <Option value="crm" label="客户关系管理插件">
                  客户关系管理插件 - 客户数据管理和分析
                </Option>
              </Select>
            </Form.Item>

            <Form.Item>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                className="submit-button"
              >
                创建项目
              </Button>
            </Form.Item>
          </Form>
        )}
      </Card>
    </div>
  );
};

export default CreateProject;
