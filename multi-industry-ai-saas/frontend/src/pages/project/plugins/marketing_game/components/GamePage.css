/* 游戏页面样式 - 现代化UI设计 */
.game-page {
  min-height: 100vh;
  position: relative;
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  overflow: hidden;
  color: #fff;
}

.game-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.4);
  z-index: 1;
}

.game-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23ffffff' fill-opacity='0.05' fill-rule='evenodd'/%3E%3C/svg%3E");
  z-index: 2;
}

.game-page-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  min-height: 80vh;
  position: relative;
  z-index: 10;
}

.game-page-container.desktop {
  padding: 30px;
}

.game-page-container.mobile {
  padding: 15px;
  max-width: 100%;
}

/* 磨砂玻璃效果 */
.glassmorphism {
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.18);
  box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease;
}

.glassmorphism:hover {
  box-shadow: 0 12px 36px 0 rgba(0, 0, 0, 0.3);
  transform: translateY(-3px);
}

/* 游戏内容包装器 */
.game-content-wrapper {
  width: 100%;
  padding: 30px;
  margin-bottom: 30px;
  animation: fadeIn 0.8s ease-out;
}

/* 游戏标题区域 */
.game-header {
  margin-bottom: 30px;
  text-align: center;
  position: relative;
}

.game-title {
  margin-bottom: 15px !important;
  color: #fff !important;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.title-icon {
  margin-right: 10px;
  color: gold;
  animation: pulse 2s infinite ease-in-out;
}

.game-description {
  font-size: 16px;
  opacity: 0.8;
  max-width: 80%;
  margin: 0 auto;
  display: block;
  color: #fff;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
}

/* 加载状态 */
.game-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background: linear-gradient(135deg, #6e8efb 0%, #a777e3 100%);
  position: relative;
}

.game-loading-card {
  padding: 40px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.loading-icon {
  font-size: 60px;
  color: #fff;
  margin-bottom: 20px;
  filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.6));
}

.game-loading-text {
  margin-top: 16px;
  font-size: 18px;
  font-weight: 500;
  color: #fff;
  text-align: center;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* 闪光效果 */
.shimmer-effect {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    to right,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0) 100%
  );
  animation: shimmer 2s infinite;
  transform: skewX(-20deg);
}

/* 错误状态 */
.game-error {
  max-width: 600px;
  margin: 0 auto;
  text-align: center;
  padding: 30px;
}

.game-result {
  padding: 40px 20px;
  width: 100%;
  max-width: 500px;
}

.game-result .ant-result-title {
  color: #fff !important;
  font-size: 28px;
  font-weight: 600;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.game-result .ant-result-subtitle {
  color: rgba(255, 255, 255, 0.8) !important;
  font-size: 16px;
  margin-top: 10px;
}

/* 3D按钮效果 */
.game-button-3d {
  transform-style: preserve-3d;
  transition: all 0.3s ease;
  border: none;
  padding: 0 30px;
  height: 46px;
  font-size: 16px;
  font-weight: 600;
  letter-spacing: 0.5px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1), 0 5px 10px rgba(0, 0, 0, 0.05);
}

.game-button-3d:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 20px rgba(0, 0, 0, 0.15), 0 8px 15px rgba(0, 0, 0, 0.1);
}

.game-button-3d:active {
  transform: translateY(-1px);
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
}

/* 页脚 */
.game-footer {
  margin-top: 20px;
  padding: 15px 20px;
  width: 100%;
  text-align: center;
}

.game-footer .ant-typography {
  color: rgba(255, 255, 255, 0.7) !important;
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-150%) skewX(-20deg);
  }
  100% {
    transform: translateX(150%) skewX(-20deg);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes float {
  0% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    transform: translateY(0px);
  }
}

/* 响应式样式 */
@media (max-width: 768px) {
  .game-page {
    padding: 10px;
  }
  
  .game-content-wrapper {
    padding: 20px;
  }
  
  .game-title {
    font-size: 24px !important;
  }
  
  .game-description {
    font-size: 14px;
    max-width: 100%;
  }
  
  .game-result {
    padding: 30px 15px;
  }
  
  .game-result .ant-result-title {
    font-size: 22px;
  }
  
  .game-result .ant-result-subtitle {
    font-size: 14px;
  }
}

/* 暗黑模式支持 */
@media (prefers-color-scheme: dark) {
  .game-page {
    background-color: #121212;
  }
  
  .glassmorphism {
    background: rgba(30, 30, 30, 0.6);
  }
}
