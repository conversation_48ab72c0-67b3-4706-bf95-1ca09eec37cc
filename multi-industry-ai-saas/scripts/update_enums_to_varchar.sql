-- 使用事务确保操作的原子性
BEGIN;

-- 1. 解除列与旧 ENUM 类型的依赖
ALTER TABLE public.sales_reports
    ALTER COLUMN status DROP DEFAULT;

-- 2. 将列类型转换为 VARCHAR
ALTER TABLE public.sales_reports
    ALTER COLUMN status TYPE VARCHAR(50),
    ALTER COLUMN report_type TYPE VARCHAR(50);

-- 3. 现在可以安全地删除旧的 ENUM 类型了
DROP TYPE IF EXISTS public.sales_report_status;
DROP TYPE IF EXISTS public.sales_report_type;

-- 4. （可选）为 VARCHAR 列重新设置默认值（由应用层处理通常更好）
-- ALTER TABLE public.sales_reports
--     ALTER COLUMN status SET DEFAULT 'draft';

-- 提交事务
COMMIT;

\echo '成功将 status 和 report_type 列转换为 VARCHAR，并删除了旧的 ENUM 类型。' 