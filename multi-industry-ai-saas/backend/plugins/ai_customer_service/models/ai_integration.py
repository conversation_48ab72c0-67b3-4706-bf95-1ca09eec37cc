#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
AI客服集成配置模型
"""

import uuid
from datetime import datetime
from sqlalchemy import Column, String, Float, Integer, Boolean, DateTime, ForeignKey, JSON, Text
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func

from db.database import Base

class AICustomerServiceIntegration(Base):
    """AI客服集成配置模型"""
    __tablename__ = "ai_customer_service_integrations"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id", ondelete="CASCADE"), nullable=False, comment="项目ID")
    
    # AI模型配置 - 修改为UUID类型以确保数据一致性
    ai_model_id = Column(UUID(as_uuid=True), nullable=True, comment="聊天模型ID")
    vision_model_id = Column(UUID(as_uuid=True), nullable=True, comment="视觉模型ID")
    audio_model_id = Column(UUID(as_uuid=True), nullable=True, comment="语音模型ID")
    embedding_model_id = Column(UUID(as_uuid=True), nullable=True, comment="嵌入模型ID")
    
    # 知识库配置
    knowledge_base_ids = Column(JSON, default=list, comment="知识库ID列表")
    
    # 参数配置
    temperature = Column(Float, default=0.7, comment="聊天模型温度参数")
    vision_temperature = Column(Float, default=0.7, comment="视觉模型温度参数")
    max_tokens = Column(Integer, default=2000, comment="最大token数")
    confidence_threshold = Column(Float, default=0.8, comment="置信度阈值")
    
    # 功能开关
    enable_context_memory = Column(Boolean, default=True, comment="启用上下文记忆")
    enable_knowledge_search = Column(Boolean, default=True, comment="启用知识库搜索")
    fallback_to_human = Column(Boolean, default=True, comment="回退到人工客服")
    
    # MCP工具配置
    enable_mcp_tools = Column(Boolean, default=True, comment="启用MCP工具")
    mcp_server_ids = Column(JSON, default=list, comment="MCP服务器ID列表")
    mcp_tool_ids = Column(JSON, default=list, comment="MCP工具ID列表")
    
    # 元数据
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), comment="更新时间")
    
    # 关联关系
    project = relationship("Project", backref="ai_customer_service_integrations")
    
    def to_dict(self):
        """转换为字典"""
        return {
            "id": str(self.id),
            "project_id": str(self.project_id),
            "ai_model_id": str(self.ai_model_id) if self.ai_model_id else None,
            "vision_model_id": str(self.vision_model_id) if self.vision_model_id else None,
            "audio_model_id": str(self.audio_model_id) if self.audio_model_id else None,
            "embedding_model_id": str(self.embedding_model_id) if self.embedding_model_id else None,
            "knowledge_base_ids": self.knowledge_base_ids or [],
            "temperature": self.temperature,
            "vision_temperature": self.vision_temperature,
            "max_tokens": self.max_tokens,
            "confidence_threshold": self.confidence_threshold,
            "enable_context_memory": self.enable_context_memory,
            "enable_knowledge_search": self.enable_knowledge_search,
            "fallback_to_human": self.fallback_to_human,
            "enable_mcp_tools": self.enable_mcp_tools,
            "mcp_server_ids": self.mcp_server_ids or [],
            "mcp_tool_ids": self.mcp_tool_ids or [],
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }
    
    def __repr__(self):
        return f"<AICustomerServiceIntegration(id={self.id}, project_id={self.project_id})>" 