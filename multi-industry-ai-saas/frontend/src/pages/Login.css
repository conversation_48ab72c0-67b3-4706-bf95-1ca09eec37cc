.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #f0f2f5 0%, #e6f7ff 100%);
  padding: 20px;
}

.login-content {
  display: flex;
  width: 100%;
  max-width: 1000px;
  height: 600px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.login-left {
  flex: 1;
  background: linear-gradient(135deg, #1a365d 0%, #2d5a8e 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
  color: white;
  position: relative;
  overflow: hidden;
}

.login-left::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('../assets/login-bg.svg') no-repeat center center;
  background-size: cover;
  opacity: 0.1;
}

.login-left-content {
  position: relative;
  z-index: 1;
  max-width: 400px;
}

.login-slogan {
  color: white !important;
  margin-bottom: 24px !important;
  font-size: 28px !important;
}

.login-description {
  color: rgba(255, 255, 255, 0.8);
  font-size: 16px;
  line-height: 1.6;
}

.login-card {
  width: 400px;
  border: none;
  border-radius: 0;
  box-shadow: none;
  background: white;
  display: flex;
  flex-direction: column;
  padding: 40px;
}

.login-header {
  text-align: center;
  margin-bottom: 32px;
}

.login-title {
  margin-bottom: 8px !important;
  color: #1a365d;
  font-weight: 600;
}

.login-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.forgot-password {
  color: #1890ff;
}

.login-button {
  width: 100%;
  height: 44px;
  font-size: 16px;
  border-radius: 4px;
}

.other-login-options {
  margin-top: 24px;
  text-align: center;
}

.third-party-login {
  display: flex;
  justify-content: center;
  margin: 16px 0;
}

.wechat-login-btn {
  background-color: #07c160;
  color: white;
  margin: 0 8px;
}

.wechat-login-btn:hover {
  background-color: #06ad56;
  color: white;
}

.dingtalk-login-btn {
  background-color: #1890ff;
  color: white;
  margin: 0 8px;
}

.dingtalk-login-btn:hover {
  background-color: #096dd9;
  color: white;
}

.login-footer {
  margin-top: auto;
  text-align: center;
  padding-top: 24px;
}

/* 输入框样式 */
.login-container .ant-input-affix-wrapper {
  height: 44px;
  border-radius: 4px;
}

.login-container .ant-input-affix-wrapper .ant-input {
  height: 42px;
}

.login-container .ant-select {
  width: 100%;
}

.login-container .ant-form-item-label > label {
  font-weight: 500;
}

/* 动画效果 */
.login-content {
  animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .login-content {
    flex-direction: column;
    height: auto;
    max-width: 400px;
  }

  .login-left {
    padding: 30px;
    min-height: 200px;
  }

  .login-card {
    width: 100%;
  }
}
