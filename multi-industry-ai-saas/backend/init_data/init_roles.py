"""
初始化角色数据
"""
from uuid import UUID
from sqlalchemy.orm import Session
from models.role import Role, Permission, RolePermission

def init_permissions(db: Session):
    """初始化权限数据"""
    permissions = [
        # 系统权限
        {
            "name": "所有权限",
            "code": "all",
            "description": "系统所有权限",
            "module": "system",
            "action": "all",
            "resource": "all",
            "is_system_permission": True
        },
        
        # 门店权限
        {
            "name": "门店查看",
            "code": "store_view",
            "description": "查看门店信息",
            "module": "store",
            "action": "view",
            "resource": "store",
            "is_system_permission": True
        },
        {
            "name": "门店编辑",
            "code": "store_edit",
            "description": "编辑门店信息",
            "module": "store",
            "action": "edit",
            "resource": "store",
            "is_system_permission": True
        },
        {
            "name": "门店报表",
            "code": "store_report",
            "description": "查看门店报表",
            "module": "store",
            "action": "report",
            "resource": "store",
            "is_system_permission": True
        },
        {
            "name": "门店收银",
            "code": "store_cashier",
            "description": "门店收银操作",
            "module": "store",
            "action": "cashier",
            "resource": "store",
            "is_system_permission": True
        },
        {
            "name": "门店人员管理",
            "code": "store_staff_manage",
            "description": "管理门店人员",
            "module": "store",
            "action": "manage",
            "resource": "staff",
            "is_system_permission": True
        },
        
        # 运营权限
        {
            "name": "运营查看",
            "code": "operation_view",
            "description": "查看运营信息",
            "module": "operation",
            "action": "view",
            "resource": "operation",
            "is_system_permission": True
        },
        {
            "name": "运营编辑",
            "code": "operation_edit",
            "description": "编辑运营信息",
            "module": "operation",
            "action": "edit",
            "resource": "operation",
            "is_system_permission": True
        },
        {
            "name": "运营报表",
            "code": "operation_report",
            "description": "查看运营报表",
            "module": "operation",
            "action": "report",
            "resource": "operation",
            "is_system_permission": True
        },
        {
            "name": "渠道管理",
            "code": "operation_channel_manage",
            "description": "管理销售渠道",
            "module": "operation",
            "action": "manage",
            "resource": "channel",
            "is_system_permission": True
        },
        
        # 采购权限
        {
            "name": "采购查看",
            "code": "purchase_view",
            "description": "查看采购信息",
            "module": "purchase",
            "action": "view",
            "resource": "purchase",
            "is_system_permission": True
        },
        {
            "name": "采购编辑",
            "code": "purchase_edit",
            "description": "编辑采购信息",
            "module": "purchase",
            "action": "edit",
            "resource": "purchase",
            "is_system_permission": True
        },
        {
            "name": "采购报表",
            "code": "purchase_report",
            "description": "查看采购报表",
            "module": "purchase",
            "action": "report",
            "resource": "purchase",
            "is_system_permission": True
        },
        {
            "name": "采购审批",
            "code": "purchase_approve",
            "description": "审批采购单",
            "module": "purchase",
            "action": "approve",
            "resource": "purchase",
            "is_system_permission": True
        },
        {
            "name": "有限采购编辑",
            "code": "purchase_edit_limited",
            "description": "有限的采购编辑权限",
            "module": "purchase",
            "action": "edit_limited",
            "resource": "purchase",
            "is_system_permission": True
        },
        {
            "name": "采购人员管理",
            "code": "purchase_staff_manage",
            "description": "管理采购人员",
            "module": "purchase",
            "action": "manage",
            "resource": "staff",
            "is_system_permission": True
        },
        
        # 财务权限
        {
            "name": "财务查看",
            "code": "finance_view",
            "description": "查看财务信息",
            "module": "finance",
            "action": "view",
            "resource": "finance",
            "is_system_permission": True
        },
        {
            "name": "财务编辑",
            "code": "finance_edit",
            "description": "编辑财务信息",
            "module": "finance",
            "action": "edit",
            "resource": "finance",
            "is_system_permission": True
        },
        {
            "name": "财务报表",
            "code": "finance_report",
            "description": "查看财务报表",
            "module": "finance",
            "action": "report",
            "resource": "finance",
            "is_system_permission": True
        },
        {
            "name": "财务审批",
            "code": "finance_approve",
            "description": "审批财务单据",
            "module": "finance",
            "action": "approve",
            "resource": "finance",
            "is_system_permission": True
        },
        {
            "name": "出纳操作",
            "code": "finance_cashier",
            "description": "执行出纳操作",
            "module": "finance",
            "action": "cashier",
            "resource": "finance",
            "is_system_permission": True
        },
        {
            "name": "财务人员管理",
            "code": "finance_staff_manage",
            "description": "管理财务人员",
            "module": "finance",
            "action": "manage",
            "resource": "staff",
            "is_system_permission": True
        },
        
        # 库存权限
        {
            "name": "库存查看",
            "code": "inventory_view",
            "description": "查看库存信息",
            "module": "inventory",
            "action": "view",
            "resource": "inventory",
            "is_system_permission": True
        },
        {
            "name": "库存编辑",
            "code": "inventory_edit",
            "description": "编辑库存信息",
            "module": "inventory",
            "action": "edit",
            "resource": "inventory",
            "is_system_permission": True
        },
        {
            "name": "库存盘点",
            "code": "inventory_check",
            "description": "执行库存盘点",
            "module": "inventory",
            "action": "check",
            "resource": "inventory",
            "is_system_permission": True
        },
        {
            "name": "库存审批",
            "code": "inventory_approve",
            "description": "审批库存操作",
            "module": "inventory",
            "action": "approve",
            "resource": "inventory",
            "is_system_permission": True
        },
        {
            "name": "库存人员管理",
            "code": "inventory_staff_manage",
            "description": "管理库存人员",
            "module": "inventory",
            "action": "manage",
            "resource": "staff",
            "is_system_permission": True
        },
        
        # 人事权限
        {
            "name": "人事查看",
            "code": "hr_view",
            "description": "查看人事信息",
            "module": "hr",
            "action": "view",
            "resource": "hr",
            "is_system_permission": True
        },
        {
            "name": "人事编辑",
            "code": "hr_edit",
            "description": "编辑人事信息",
            "module": "hr",
            "action": "edit",
            "resource": "hr",
            "is_system_permission": True
        },
        {
            "name": "人事审批",
            "code": "hr_approve",
            "description": "审批人事操作",
            "module": "hr",
            "action": "approve",
            "resource": "hr",
            "is_system_permission": True
        },
        {
            "name": "人事人员管理",
            "code": "hr_staff_manage",
            "description": "管理人事人员",
            "module": "hr",
            "action": "manage",
            "resource": "staff",
            "is_system_permission": True
        },
        
        # 物流权限
        {
            "name": "物流查看",
            "code": "logistics_view",
            "description": "查看物流信息",
            "module": "logistics",
            "action": "view",
            "resource": "logistics",
            "is_system_permission": True
        },
        {
            "name": "物流编辑",
            "code": "logistics_edit",
            "description": "编辑物流信息",
            "module": "logistics",
            "action": "edit",
            "resource": "logistics",
            "is_system_permission": True
        },
        {
            "name": "物流执行",
            "code": "logistics_execute",
            "description": "执行物流操作",
            "module": "logistics",
            "action": "execute",
            "resource": "logistics",
            "is_system_permission": True
        },
        {
            "name": "物流审批",
            "code": "logistics_approve",
            "description": "审批物流操作",
            "module": "logistics",
            "action": "approve",
            "resource": "logistics",
            "is_system_permission": True
        },
        {
            "name": "物流人员管理",
            "code": "logistics_staff_manage",
            "description": "管理物流人员",
            "module": "logistics",
            "action": "manage",
            "resource": "staff",
            "is_system_permission": True
        }
    ]
    
    # 检查权限是否已存在，不存在则创建
    for perm_data in permissions:
        perm = db.query(Permission).filter(Permission.code == perm_data["code"]).first()
        if not perm:
            perm = Permission(**perm_data)
            db.add(perm)
    
    db.commit()
    return db.query(Permission).all()

def init_roles(db: Session, project_id: UUID):
    """初始化角色数据"""
    # 确保权限已初始化
    permissions = {p.code: p for p in init_permissions(db)}
    
    roles = [
        # 系统角色
        {
            "name": "项目管理员",
            "code": "project_admin",
            "description": "项目管理员，拥有项目内的所有权限",
            "permissions": {"view": True, "create": True, "update": True, "delete": True},
            "status": "active",
            "is_system_role": True,
            "category": "系统",
            "permission_codes": ["all"]
        },
        
        # 门店角色
        {
            "name": "门店店长",
            "code": "store_admin",
            "description": "门店店长，负责管理门店相关业务",
            "permissions": {"view": True, "create": True, "update": True, "delete": False},
            "status": "active",
            "is_system_role": True,
            "category": "门店",
            "permission_codes": ["store_view", "store_edit", "store_report", "store_staff_manage"]
        },
        {
            "name": "收银员",
            "code": "store_cashier",
            "description": "收银员，负责门店收银业务",
            "permissions": {"view": True, "create": False, "update": False, "delete": False},
            "status": "active",
            "is_system_role": True,
            "category": "门店",
            "permission_codes": ["store_view", "store_cashier"]
        },
        {
            "name": "店助",
            "code": "store_assistant",
            "description": "店助，协助门店日常运营",
            "permissions": {"view": True, "create": False, "update": False, "delete": False},
            "status": "active",
            "is_system_role": True,
            "category": "门店",
            "permission_codes": ["store_view", "store_report"]
        },
        {
            "name": "普通店员",
            "code": "store_staff",
            "description": "普通店员，执行基本门店任务",
            "permissions": {"view": True, "create": False, "update": False, "delete": False},
            "status": "active",
            "is_system_role": True,
            "category": "门店",
            "permission_codes": ["store_view"]
        },
        
        # 财务角色
        {
            "name": "财务主管",
            "code": "finance_manager",
            "description": "财务主管，负责全面财务管理",
            "permissions": {"view": True, "create": True, "update": True, "delete": False},
            "status": "active",
            "is_system_role": True,
            "category": "财务",
            "permission_codes": ["finance_view", "finance_edit", "finance_approve", "finance_staff_manage"]
        },
        {
            "name": "出纳",
            "code": "finance_cashier",
            "description": "出纳，负责资金收付",
            "permissions": {"view": True, "create": True, "update": False, "delete": False},
            "status": "active",
            "is_system_role": True,
            "category": "财务",
            "permission_codes": ["finance_view", "finance_cashier"]
        },
        {
            "name": "财务助理",
            "code": "finance_assistant",
            "description": "财务助理，协助财务日常工作",
            "permissions": {"view": True, "create": False, "update": False, "delete": False},
            "status": "active",
            "is_system_role": True,
            "category": "财务",
            "permission_codes": ["finance_view", "finance_report"]
        },
        
        # 采购角色
        {
            "name": "采购主管",
            "code": "purchase_manager",
            "description": "采购主管，负责全面采购管理",
            "permissions": {"view": True, "create": True, "update": True, "delete": False},
            "status": "active",
            "is_system_role": True,
            "category": "采购",
            "permission_codes": ["purchase_view", "purchase_edit", "purchase_approve", "purchase_staff_manage"]
        },
        {
            "name": "采购助理",
            "code": "purchase_assistant",
            "description": "采购助理，协助采购日常工作",
            "permissions": {"view": True, "create": False, "update": False, "delete": False},
            "status": "active",
            "is_system_role": True,
            "category": "采购",
            "permission_codes": ["purchase_view", "purchase_report"]
        },
        {
            "name": "专项采购",
            "code": "purchase_specialist",
            "description": "专项采购，负责特定品类采购",
            "permissions": {"view": True, "create": True, "update": True, "delete": False},
            "status": "active",
            "is_system_role": True,
            "category": "采购",
            "permission_codes": ["purchase_view", "purchase_edit_limited"]
        },
        
        # 运营角色
        {
            "name": "渠道运营",
            "code": "operation_channel",
            "description": "渠道运营，负责渠道管理和拓展",
            "permissions": {"view": True, "create": True, "update": True, "delete": False},
            "status": "active",
            "is_system_role": True,
            "category": "运营",
            "permission_codes": ["operation_view", "operation_channel_manage"]
        },
        {
            "name": "运营助理",
            "code": "operation_assistant",
            "description": "运营助理（区域运营），负责区域运营支持",
            "permissions": {"view": True, "create": False, "update": False, "delete": False},
            "status": "active",
            "is_system_role": True,
            "category": "运营",
            "permission_codes": ["operation_view", "operation_report"]
        },
        
        # 人事角色
        {
            "name": "人事主管",
            "code": "hr_manager",
            "description": "人事主管，负责全面人事管理",
            "permissions": {"view": True, "create": True, "update": True, "delete": False},
            "status": "active",
            "is_system_role": True,
            "category": "人事",
            "permission_codes": ["hr_view", "hr_edit", "hr_approve", "hr_staff_manage"]
        },
        {
            "name": "人事专员",
            "code": "hr_specialist",
            "description": "人事专员，负责日常人事工作",
            "permissions": {"view": True, "create": True, "update": True, "delete": False},
            "status": "active",
            "is_system_role": True,
            "category": "人事",
            "permission_codes": ["hr_view", "hr_edit"]
        },
        
        # 物流角色
        {
            "name": "物流主管",
            "code": "logistics_manager",
            "description": "物流主管，负责全面物流管理",
            "permissions": {"view": True, "create": True, "update": True, "delete": False},
            "status": "active",
            "is_system_role": True,
            "category": "物流",
            "permission_codes": ["logistics_view", "logistics_edit", "logistics_approve", "logistics_staff_manage"]
        },
        {
            "name": "物流员",
            "code": "logistics_staff",
            "description": "物流员，负责日常物流工作",
            "permissions": {"view": True, "create": True, "update": False, "delete": False},
            "status": "active",
            "is_system_role": True,
            "category": "物流",
            "permission_codes": ["logistics_view", "logistics_execute"]
        },
        
        # 仓储角色
        {
            "name": "仓库主管",
            "code": "inventory_manager",
            "description": "仓库主管，负责全面仓储管理",
            "permissions": {"view": True, "create": True, "update": True, "delete": False},
            "status": "active",
            "is_system_role": True,
            "category": "仓储",
            "permission_codes": ["inventory_view", "inventory_edit", "inventory_approve", "inventory_staff_manage"]
        },
        {
            "name": "仓管员",
            "code": "inventory_staff",
            "description": "仓管员，负责日常仓库和库存管理",
            "permissions": {"view": True, "create": True, "update": True, "delete": False},
            "status": "active",
            "is_system_role": True,
            "category": "仓储",
            "permission_codes": ["inventory_view", "inventory_edit", "inventory_check"]
        }
    ]
    
    # 检查角色是否已存在，不存在则创建
    created_roles = []
    for role_data in roles:
        permission_codes = role_data.pop("permission_codes")
        role = db.query(Role).filter(
            Role.project_id == project_id,
            Role.code == role_data["code"]
        ).first()
        
        if not role:
            role = Role(project_id=project_id, **role_data)
            db.add(role)
            db.flush()  # 获取角色ID
            created_roles.append(role)
            
            # 创建角色权限关联
            for perm_code in permission_codes:
                if perm_code in permissions:
                    role_perm = RolePermission(
                        role_id=role.id,
                        permission_id=permissions[perm_code].id
                    )
                    db.add(role_perm)
    
    db.commit()
    return created_roles
