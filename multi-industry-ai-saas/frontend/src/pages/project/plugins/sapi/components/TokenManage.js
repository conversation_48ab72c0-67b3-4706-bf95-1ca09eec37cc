import React, { useState, useEffect } from 'react';
import { Card, Typography, Button, Table, Space, Tag, Modal, Form, Input, Select, Tabs, Spin, message, Tooltip, Popconfirm, Badge, Drawer, Descriptions, InputNumber, Switch, Collapse } from 'antd';
import { PlusOutlined, ReloadOutlined, DeleteOutlined, EyeOutlined, CheckOutlined, CloseOutlined, KeyOutlined, CopyOutlined } from '@ant-design/icons';
import { getTokenList, getTokenApplications, approveTokenApplication, rejectTokenApplication, updateTokenStatus, deleteToken, createToken, getTokenDetail, getTokenApplicationDetail } from '../services/tokenService';
import { getServerList, createMcpToken } from '../services/mcpService';
import { getProjectId } from '../../../../../services/api/httpClient';
import { formatDateTime } from '../../../../../utils/dateTimeUtils';

const { Title, Paragraph, Text } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;
const { TextArea } = Input;
const { Panel } = Collapse;

/**
 * JWT令牌管理组件
 *
 * 包含令牌管理和申请审批功能，支持JWT+OAuth2.1认证
 */
const TokenManage = () => {
  const projectId = getProjectId();
  const [tokenForm] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [tokens, setTokens] = useState([]);
  const [applications, setApplications] = useState([]);
  const [activeTab, setActiveTab] = useState('1');
  const [detailVisible, setDetailVisible] = useState(false);
  const [detailItem, setDetailItem] = useState(null);
  const [rejectModalVisible, setRejectModalVisible] = useState(false);
  const [createTokenModalVisible, setCreateTokenModalVisible] = useState(false);
  const [rejectForm] = Form.useForm();
  const [currentApplicationId, setCurrentApplicationId] = useState(null);
  const [mcpServers, setMcpServers] = useState([]);
  const [loadingServers, setLoadingServers] = useState(false);

  // 加载令牌列表
  useEffect(() => {
    if (activeTab === '1') {
      fetchTokens();
    } else {
      fetchApplications();
    }
  }, [activeTab]);

  // 加载MCP服务器列表
  useEffect(() => {
    fetchMCPServers(projectId);
  }, [projectId]);

  // 获取MCP服务器列表
  const fetchMCPServers = async (pid) => {
    const projectId = pid;
    console.log('fetchMCPServers called, projectId:', projectId);
    if (!projectId) return;
    setLoadingServers(true);
    try {
      const response = await getServerList({}, projectId);
      console.log('getServerList response:', response);
      if (response.success) {
        setMcpServers(response.data || []);
      } else {
        message.error('获取MCP服务器列表失败');
      }
    } catch (error) {
      console.error('获取MCP服务器列表失败:', error);
      message.error('获取MCP服务器列表失败');
    } finally {
      setLoadingServers(false);
    }
  };

  // 获取令牌列表
  const fetchTokens = async () => {
    setLoading(true);
    try {
      const response = await getTokenList();
      if (response.success) {
        setTokens(response.data);
      } else {
        message.error('获取令牌列表失败');
      }
    } catch (error) {
      console.error('获取令牌列表失败:', error);
      message.error('获取令牌列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取申请列表
  const fetchApplications = async () => {
    setLoading(true);
    try {
      const response = await getTokenApplications();
      if (response.success) {
        setApplications(response.data);
      } else {
        message.error('获取申请列表失败');
      }
    } catch (error) {
      console.error('获取申请列表失败:', error);
      message.error('获取申请列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 处理令牌状态更新
  const handleUpdateStatus = async (tokenId, isActive) => {
    try {
      const response = await updateTokenStatus(tokenId, { is_active: isActive });
      if (response.success) {
        message.success(`令牌${isActive ? '启用' : '禁用'}成功`);
        fetchTokens();
      } else {
        message.error(`令牌${isActive ? '启用' : '禁用'}失败`);
      }
    } catch (error) {
      console.error(`令牌${isActive ? '启用' : '禁用'}失败:`, error);
      message.error(`令牌${isActive ? '启用' : '禁用'}失败`);
    }
  };

  // 处理令牌删除
  const handleDeleteToken = async (tokenId) => {
    try {
      const response = await deleteToken(tokenId);
      if (response.success) {
        message.success('令牌删除成功');
        fetchTokens();
      } else {
        message.error('令牌删除失败');
      }
    } catch (error) {
      console.error('令牌删除失败:', error);
      message.error('令牌删除失败');
    }
  };

  // 处理申请审批
  const handleApproveApplication = async (applicationId) => {
    try {
      const response = await approveTokenApplication(applicationId, { status: 'approved', project_id: projectId });
      if (response.success) {
        message.success('申请审批通过');
        fetchApplications();

        // 显示令牌信息
        Modal.success({
          title: '申请已审批通过',
          content: (
            <div>
              <p>JWT令牌已生成，请妥善保管：</p>
              <div style={{ background: '#f5f5f5', padding: '10px', borderRadius: '4px', marginBottom: '10px', display: 'flex', alignItems: 'center' }}>
                <Text code style={{ flex: 1, marginRight: '8px' }}>{response.data.token}</Text>
                <Button
                  icon={<CopyOutlined />}
                  size="small"
                  onClick={() => copyToClipboard(response.data.token)}
                >
                  复制
                </Button>
              </div>
              <p>令牌信息：</p>
              <ul>
                <li><strong>名称：</strong> {response.data.name}</li>
                <li><strong>类型：</strong> {response.data.token_type || 'mcp'}</li>
                <li><strong>权限：</strong> {response.data.permissions}</li>
                <li><strong>过期时间：</strong> {formatDateTime(response.data.expires_at, 'YYYY-MM-DD HH:mm:ss')}</li>
                {response.data.server_permissions && (
                  <li>
                    <strong>服务器权限：</strong>
                    <ul>
                      {Object.entries(response.data.server_permissions).map(([serverId, permissions]) => (
                        <li key={serverId}>
                          {serverId}: {permissions.join(', ')}
                        </li>
                      ))}
                    </ul>
                  </li>
                )}
              </ul>
              <p>使用方式：</p>
              <ul>
                <li>在HTTP请求头中添加：<code>Authorization: Bearer {'{your_token}'}</code></li>
                <li>令牌使用过程中如有问题，请联系系统管理员</li>
              </ul>
            </div>
          ),
          okText: '确定'
        });
      } else {
        message.error('申请审批失败');
      }
    } catch (error) {
      console.error('申请审批失败:', error);
      message.error('申请审批失败');
    }
  };

  // 显示拒绝申请模态框
  const showRejectModal = (applicationId) => {
    setCurrentApplicationId(applicationId);
    rejectForm.resetFields();
    setRejectModalVisible(true);
  };

  // 处理申请拒绝
  const handleRejectApplication = async () => {
    try {
      const values = await rejectForm.validateFields();
      const response = await rejectTokenApplication(currentApplicationId, { reason: values.reason, project_id: projectId });
      if (response.success) {
        message.success('申请已拒绝');
        setRejectModalVisible(false);
        fetchApplications();
      } else {
        message.error('申请拒绝失败');
      }
    } catch (error) {
      console.error('申请拒绝失败:', error);
      message.error('申请拒绝失败');
    }
  };

  // 查看详情
  const showDetail = async (item, type) => {
    if (type === 'token') {
      try {
        // 获取完整的令牌详情
        const response = await getTokenDetail(item.id);
        if (response.success) {
          setDetailItem({ ...response.data, type });
        } else {
          setDetailItem({ ...item, type });
          message.error('获取令牌详情失败');
        }
      } catch (error) {
        console.error('获取令牌详情失败:', error);
        setDetailItem({ ...item, type });
        message.error('获取令牌详情失败');
      }
    } else {
      try {
        // 新增：获取完整的令牌申请详情
        const response = await getTokenApplicationDetail(item.id);
        if (response.success) {
          setDetailItem({ ...response.data, type });
        } else {
          setDetailItem({ ...item, type });
          message.error('获取申请详情失败');
        }
      } catch (error) {
        setDetailItem({ ...item, type });
        message.error('获取申请详情失败');
      }
    }
    setDetailVisible(true);
  };

  // 复制到剪贴板
  const copyToClipboard = (text) => {
    // 检查是否支持 Clipboard API
    if (navigator.clipboard && navigator.clipboard.writeText) {
      navigator.clipboard.writeText(text).then(
        () => {
          message.success('已复制到剪贴板');
        },
        (err) => {
          console.error('使用 Clipboard API 复制失败:', err);
          fallbackCopyTextToClipboard(text);
        }
      );
    } else {
      // 使用 fallback 方法
      fallbackCopyTextToClipboard(text);
    }
  };

  // Fallback 复制方法（兼容旧浏览器）
  const fallbackCopyTextToClipboard = (text) => {
    const textArea = document.createElement("textarea");
    textArea.value = text;
    
    // 避免在页面上显示
    textArea.style.position = "fixed";
    textArea.style.top = "0";
    textArea.style.left = "0";
    textArea.style.width = "2em";
    textArea.style.height = "2em";
    textArea.style.padding = "0";
    textArea.style.border = "none";
    textArea.style.outline = "none";
    textArea.style.boxShadow = "none";
    textArea.style.background = "transparent";
    
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    
    try {
      const successful = document.execCommand('copy');
      if (successful) {
        message.success('已复制到剪贴板');
      } else {
        message.error('复制失败，请手动复制');
      }
    } catch (err) {
      console.error('Fallback 复制方法失败:', err);
      message.error('复制失败，请手动复制');
    }
    
    document.body.removeChild(textArea);
  };

  // 显示创建令牌模态框
  const showCreateTokenModal = () => {
    tokenForm.resetFields();
    tokenForm.setFieldsValue({
      permissions: 'read',
      expires_days: 30
    });
    setCreateTokenModalVisible(true);
  };

  // 处理创建令牌
  const handleCreateToken = async () => {
    try {
      const values = await tokenForm.validateFields();

      // 处理服务器权限映射
      let serverPermissions = {};
      if (values.serverPermissions) {
        Object.keys(values.serverPermissions).forEach(serverId => {
          if (values.serverPermissions[serverId]) {
            serverPermissions[serverId] = [values.permissions];
          }
        });
      }

      // 构建请求数据
      const requestData = {
        ...values,
        project_id: projectId,
        server_permissions: Object.keys(serverPermissions).length > 0 ? serverPermissions : undefined,
        default_permissions: [values.permissions]
      };

      let response;
      if (values.token_type === 'mcp') {
        // 取第一个已授权服务器为 server_id，或让用户选择
        const enabledServerIds = Object.keys(serverPermissions).filter(sid => serverPermissions[sid]);
        if (!enabledServerIds.length) {
          message.error('请至少选择一个授权的MCP服务器');
          return;
        }
        const server_id = enabledServerIds[0];
        response = await createMcpToken({
          server_id,
          permissions: [values.permissions],
          server_permissions: requestData.server_permissions,
          default_permissions: requestData.default_permissions,
          expires_hours: values.expires_hours || 24 * 30
        });
        if (response.access_token) {
          Modal.success({
            title: 'MCP令牌创建成功',
            content: (
              <div>
                <p>JWT令牌已生成，请妥善保管：</p>
                <div style={{ background: '#f5f5f5', padding: '10px', borderRadius: '4px', marginBottom: '10px', display: 'flex', alignItems: 'center' }}>
                  <Text code style={{ flex: 1, marginRight: '8px' }}>{response.access_token}</Text>
                  <Button
                    icon={<CopyOutlined />}
                    size="small"
                    onClick={() => copyToClipboard(response.access_token)}
                  >
                    复制
                  </Button>
                </div>
                <p>令牌信息：</p>
                <ul>
                  <li><strong>类型：</strong> MCP</li>
                  <li><strong>权限：</strong> {values.permissions}</li>
                  <li><strong>过期时间：</strong> {response.expires_in ? `${Math.floor(response.expires_in/3600)} 小时` : '未设置'}</li>
                  <li><strong>服务器ID：</strong> {server_id}</li>
                </ul>
              </div>
            ),
            okText: '确定'
          });
          setCreateTokenModalVisible(false);
          fetchTokens();
        } else {
          message.error('MCP令牌创建失败');
        }
      } else {
        response = await createToken(requestData);
        if (response.success) {
          message.success('令牌创建成功');
          setCreateTokenModalVisible(false);
          fetchTokens();
          // 显示令牌信息
          Modal.success({
            title: '令牌创建成功',
            content: (
              <div>
                <p>令牌已生成，请妥善保管：</p>
                <div style={{ background: '#f5f5f5', padding: '10px', borderRadius: '4px', marginBottom: '10px', display: 'flex', alignItems: 'center' }}>
                  <Text code style={{ flex: 1, marginRight: '8px' }}>{response.data.token}</Text>
                  <Button
                    icon={<CopyOutlined />}
                    size="small"
                    onClick={() => copyToClipboard(response.data.token)}
                  >
                    复制
                  </Button>
                </div>
                <p>令牌信息：</p>
                <ul>
                  <li><strong>名称：</strong> {response.data.name}</li>
                  <li><strong>权限：</strong> {response.data.permissions}</li>
                  <li><strong>过期时间：</strong> {response.data.expires_at ? formatDateTime(response.data.expires_at, 'YYYY-MM-DD HH:mm:ss') : '永不过期'}</li>
                </ul>
              </div>
            ),
            okText: '确定'
          });
        } else {
          message.error('令牌创建失败');
        }
      }
    } catch (error) {
      console.error('令牌创建失败:', error);
      message.error('令牌创建失败');
    }
  };

  // 令牌列表列定义
  const tokenColumns = [
    {
      title: '状态',
      key: 'status',
      render: (_, record) => {
        const statusMap = {
          active: { status: 'success', text: '启用' },
          inactive: { status: 'default', text: '禁用' },
          revoked: { status: 'error', text: '已撤销' }
        };
        const { status, text } = statusMap[record.status] || statusMap.inactive;
        return <Badge status={status} text={text} />;
      },
      width: 80,
    },
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      render: text => <Text strong>{text}</Text>
    },
    {
      title: '类型',
      dataIndex: 'token_type',
      key: 'token_type',
      render: type => {
        const typeColors = {
          access: 'blue',
          refresh: 'green',
          mcp: 'purple'
        };
        return <Tag color={typeColors[type] || 'default'}>{type || 'access'}</Tag>;
      }
    },
    {
      title: '令牌预览',
      key: 'token_preview',
      render: (_, record) => (
        record.token_preview ? (
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <Text code>{record.token_preview}</Text>
          </div>
        ) : (
          <Text type="secondary">无权查看</Text>
        )
      ),
    },
    {
      title: '权限',
      dataIndex: 'permissions',
      key: 'permissions',
      render: permissions => {
        const permissionColors = {
          read: 'blue',
          write: 'green',
          read_write: 'purple'
        };
        return <Tag color={permissionColors[permissions] || 'default'}>{permissions}</Tag>;
      }
    },
    {
      title: '过期时间',
      dataIndex: 'expires_at',
      key: 'expires_at',
      render: text => text ? formatDateTime(text, 'YYYY-MM-DD') : '永不过期'
    },
    {
      title: '用户',
      dataIndex: 'user_name',
      key: 'user_name',
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: text => formatDateTime(text, 'YYYY-MM-DD HH:mm')
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space>
          <Tooltip title="查看详情">
            <Button
              icon={<EyeOutlined />}
              size="small"
              onClick={() => showDetail(record, 'token')}
            />
          </Tooltip>
          <Tooltip title={record.status === 'active' ? '禁用' : '启用'}>
            <Button
              icon={record.status === 'active' ? <CloseOutlined /> : <CheckOutlined />}
              size="small"
              onClick={() => handleUpdateStatus(record.id, record.status !== 'active')}
            />
          </Tooltip>
          <Tooltip title="删除">
            <Popconfirm
              title="确定要删除此令牌吗？"
              onConfirm={() => handleDeleteToken(record.id)}
              okText="确定"
              cancelText="取消"
            >
              <Button
                icon={<DeleteOutlined />}
                size="small"
                danger
              />
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ];

  // 申请列表列定义
  const applicationColumns = [
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: status => {
        const statusColors = {
          pending: 'processing',
          approved: 'success',
          rejected: 'error'
        };
        const statusTexts = {
          pending: '待审批',
          approved: '已通过',
          rejected: '已拒绝'
        };
        return (
          <Badge
            status={statusColors[status]}
            text={statusTexts[status]}
          />
        );
      },
      width: 100,
    },
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      render: text => <Text strong>{text}</Text>
    },
    {
      title: '类型',
      dataIndex: 'token_type',
      key: 'token_type',
      render: type => {
        const typeColors = {
          access: 'blue',
          refresh: 'green',
          mcp: 'purple'
        };
        return <Tag color={typeColors[type] || 'default'}>{type || 'mcp'}</Tag>;
      }
    },
    {
      title: '申请人',
      dataIndex: 'applicant_name',
      key: 'applicant_name',
    },
    {
      title: '权限',
      dataIndex: 'permissions',
      key: 'permissions',
      render: permissions => {
        const permissionColors = {
          read: 'blue',
          write: 'green',
          read_write: 'purple'
        };
        return <Tag color={permissionColors[permissions] || 'default'}>{permissions}</Tag>;
      }
    },
    {
      title: '过期时间',
      dataIndex: 'expires_at',
      key: 'expires_at',
      render: text => text ? formatDateTime(text, 'YYYY-MM-DD') : '未设置'
    },
    {
      title: '申请时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: text => formatDateTime(text, 'YYYY-MM-DD HH:mm')
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => {
        if (record.status === 'pending') {
          return (
            <Space>
              <Tooltip title="查看详情">
                <Button
                  icon={<EyeOutlined />}
                  size="small"
                  onClick={() => showDetail(record, 'application')}
                />
              </Tooltip>
              <Tooltip title="通过">
                <Button
                  icon={<CheckOutlined />}
                  size="small"
                  type="primary"
                  onClick={() => handleApproveApplication(record.id)}
                />
              </Tooltip>
              <Tooltip title="拒绝">
                <Button
                  icon={<CloseOutlined />}
                  size="small"
                  danger
                  onClick={() => showRejectModal(record.id)}
                />
              </Tooltip>
            </Space>
          );
        } else {
          return (
            <Space>
              <Tooltip title="查看详情">
                <Button
                  icon={<EyeOutlined />}
                  size="small"
                  onClick={() => showDetail(record, 'application')}
                />
              </Tooltip>
            </Space>
          );
        }
      },
    },
  ];

  // 渲染令牌详情
  const renderTokenDetail = () => {
    if (!detailItem) return null;

    return (
      <Descriptions bordered column={1}>
        <Descriptions.Item label="ID">{detailItem.id}</Descriptions.Item>
        <Descriptions.Item label="名称">{detailItem.name}</Descriptions.Item>
        <Descriptions.Item label="描述">{detailItem.description}</Descriptions.Item>
        {detailItem.token && (
          <Descriptions.Item label="令牌">
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <Text code>{detailItem.token}</Text>
              <Button
                icon={<CopyOutlined />}
                type="text"
                size="small"
                onClick={() => copyToClipboard(detailItem.token)}
              />
            </div>
          </Descriptions.Item>
        )}
        <Descriptions.Item label="令牌类型">
          {(() => {
            const typeColors = {
              access: 'blue',
              refresh: 'green',
              mcp: 'purple'
            };
            return <Tag color={typeColors[detailItem.token_type] || 'default'}>{detailItem.token_type || 'access'}</Tag>;
          })()}
        </Descriptions.Item>
        <Descriptions.Item label="权限">
          {(() => {
            const permissionColors = {
              read: 'blue',
              write: 'green',
              read_write: 'purple'
            };
            return <Tag color={permissionColors[detailItem.permissions] || 'default'}>{detailItem.permissions}</Tag>;
          })()}
        </Descriptions.Item>
        <Descriptions.Item label="状态">
          {(() => {
            const statusMap = {
              active: { status: 'success', text: '启用' },
              inactive: { status: 'default', text: '禁用' },
              revoked: { status: 'error', text: '已撤销' }
            };
            const { status, text } = statusMap[detailItem.status] || statusMap.inactive;
            return <Badge status={status} text={text} />;
          })()}
        </Descriptions.Item>
        <Descriptions.Item label="服务器权限">
          {detailItem.server_permissions ? (
            <ul style={{ margin: 0, paddingLeft: 20 }}>
              {Object.entries(detailItem.server_permissions).map(([serverId, permissions]) => (
                <li key={serverId}>
                  {serverId}: {permissions.join(', ')}
                </li>
              ))}
            </ul>
          ) : (
            <Text type="secondary">未指定</Text>
          )}
        </Descriptions.Item>
        <Descriptions.Item label="默认权限">
          {detailItem.default_permissions ? (
            <div>
              {detailItem.default_permissions.map(permission => (
                <Tag key={permission} color={
                  permission === 'read' ? 'blue' :
                  permission === 'write' ? 'green' :
                  permission === 'read_write' ? 'purple' : 'default'
                }>
                  {permission}
                </Tag>
              ))}
            </div>
          ) : (
            <Text type="secondary">未指定</Text>
          )}
        </Descriptions.Item>
        <Descriptions.Item label="项目ID">
          {detailItem.project_id || <Text type="secondary">未指定</Text>}
        </Descriptions.Item>
        <Descriptions.Item label="过期时间">
          {detailItem.expires_at ? formatDateTime(detailItem.expires_at, 'YYYY-MM-DD HH:mm:ss') : '永不过期'}
        </Descriptions.Item>
        <Descriptions.Item label="用户">{detailItem.user_name}</Descriptions.Item>
        <Descriptions.Item label="创建时间">{formatDateTime(detailItem.created_at, 'YYYY-MM-DD HH:mm:ss')}</Descriptions.Item>
        <Descriptions.Item label="最后使用时间">
          {detailItem.last_used ? formatDateTime(detailItem.last_used, 'YYYY-MM-DD HH:mm:ss') : '从未使用'}
        </Descriptions.Item>
      </Descriptions>
    );
  };

  // 渲染申请详情
  const renderApplicationDetail = () => {
    if (!detailItem) return null;

    return (
      <Descriptions bordered column={1}>
        <Descriptions.Item label="ID">{detailItem.id}</Descriptions.Item>
        <Descriptions.Item label="名称">{detailItem.name}</Descriptions.Item>
        <Descriptions.Item label="描述">{detailItem.description}</Descriptions.Item>
        <Descriptions.Item label="申请目的">{detailItem.purpose}</Descriptions.Item>
        <Descriptions.Item label="令牌类型">
          {(() => {
            const typeColors = {
              access: 'blue',
              refresh: 'green',
              mcp: 'purple'
            };
            return <Tag color={typeColors[detailItem.token_type] || 'default'}>{detailItem.token_type || 'mcp'}</Tag>;
          })()}
        </Descriptions.Item>
        <Descriptions.Item label="权限">
          {(() => {
            const permissionColors = {
              read: 'blue',
              write: 'green',
              read_write: 'purple'
            };
            return <Tag color={permissionColors[detailItem.permissions] || 'default'}>{detailItem.permissions}</Tag>;
          })()}
        </Descriptions.Item>
        <Descriptions.Item label="状态">
          {(() => {
            const statusColors = {
              pending: 'processing',
              approved: 'success',
              rejected: 'error'
            };
            const statusTexts = {
              pending: '待审批',
              approved: '已通过',
              rejected: '已拒绝'
            };
            return (
              <Badge
                status={statusColors[detailItem.status]}
                text={statusTexts[detailItem.status]}
              />
            );
          })()}
        </Descriptions.Item>
        <Descriptions.Item label="服务器权限">
          {detailItem.server_permissions ? (
            <ul style={{ margin: 0, paddingLeft: 20 }}>
              {Object.entries(detailItem.server_permissions).map(([serverId, permissions]) => (
                <li key={serverId}>
                  {serverId}: {permissions.join(', ')}
                </li>
              ))}
            </ul>
          ) : (
            <Text type="secondary">未指定</Text>
          )}
        </Descriptions.Item>
        <Descriptions.Item label="默认权限">
          {detailItem.default_permissions ? (
            <div>
              {detailItem.default_permissions.map(permission => (
                <Tag key={permission} color={
                  permission === 'read' ? 'blue' :
                  permission === 'write' ? 'green' :
                  permission === 'read_write' ? 'purple' : 'default'
                }>
                  {permission}
                </Tag>
              ))}
            </div>
          ) : (
            <Text type="secondary">未指定</Text>
          )}
        </Descriptions.Item>
        <Descriptions.Item label="项目ID">
          {detailItem.project_id || <Text type="secondary">未指定</Text>}
        </Descriptions.Item>
        <Descriptions.Item label="过期时间">
          {detailItem.expires_at ? formatDateTime(detailItem.expires_at, 'YYYY-MM-DD HH:mm:ss') : '未设置'}
        </Descriptions.Item>
        <Descriptions.Item label="申请人">{detailItem.applicant_name}</Descriptions.Item>
        <Descriptions.Item label="申请时间">{formatDateTime(detailItem.created_at, 'YYYY-MM-DD HH:mm:ss')}</Descriptions.Item>
        {detailItem.status === 'approved' && (
          <>
            <Descriptions.Item label="审批人">{detailItem.approver_name}</Descriptions.Item>
            <Descriptions.Item label="审批时间">{formatDateTime(detailItem.approved_at, 'YYYY-MM-DD HH:mm:ss')}</Descriptions.Item>
            {detailItem.token && (
              <Descriptions.Item label="令牌">
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <Text code style={{ maxWidth: '300px', overflow: 'hidden', textOverflow: 'ellipsis' }}>
                    {detailItem.token}
                  </Text>
                  <Button
                    icon={<CopyOutlined />}
                    type="text"
                    size="small"
                    onClick={() => copyToClipboard(detailItem.token)}
                  />
                </div>
              </Descriptions.Item>
            )}
          </>
        )}
        {detailItem.status === 'rejected' && (
          <>
            <Descriptions.Item label="审批人">{detailItem.approver_name}</Descriptions.Item>
            <Descriptions.Item label="审批时间">{formatDateTime(detailItem.approved_at, 'YYYY-MM-DD HH:mm:ss')}</Descriptions.Item>
            <Descriptions.Item label="拒绝原因">{detailItem.rejection_reason}</Descriptions.Item>
          </>
        )}
      </Descriptions>
    );
  };

  return (
    <Card>
      <Title level={4}>JWT令牌管理</Title>
      <Paragraph>
        管理JWT令牌和审批令牌申请。JWT令牌用于访问API和MCP服务器，支持OAuth2.1认证，可以设置不同的权限、过期时间和服务器访问权限。
      </Paragraph>

      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane
          tab={<span><KeyOutlined /> 令牌列表</span>}
          key="1"
        >
          <div style={{ marginBottom: 16 }}>
            <Space>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={showCreateTokenModal}
              >
                新增令牌
              </Button>
              <Button
                icon={<ReloadOutlined />}
                onClick={fetchTokens}
              >
                刷新列表
              </Button>
            </Space>
          </div>

          <Spin spinning={loading}>
            <Table
              dataSource={tokens}
              columns={tokenColumns}
              rowKey="id"
              pagination={{ pageSize: 10 }}
            />
          </Spin>
        </TabPane>

        <TabPane
          tab={<span><CheckOutlined /> 申请审批</span>}
          key="2"
        >
          <div style={{ marginBottom: 16 }}>
            <Space>
              <Button
                icon={<ReloadOutlined />}
                onClick={fetchApplications}
              >
                刷新列表
              </Button>
            </Space>
          </div>

          <Spin spinning={loading}>
            <Table
              dataSource={applications}
              columns={applicationColumns}
              rowKey="id"
              pagination={{ pageSize: 10 }}
            />
          </Spin>
        </TabPane>
      </Tabs>

      {/* 详情抽屉 */}
      <Drawer
        title={detailItem?.type === 'token' ? '令牌详情' : '申请详情'}
        placement="right"
        width={600}
        onClose={() => setDetailVisible(false)}
        open={detailVisible}
      >
        {detailItem?.type === 'token' ? renderTokenDetail() : renderApplicationDetail()}
      </Drawer>

      {/* 拒绝申请模态框 */}
      <Modal
        title="拒绝申请"
        open={rejectModalVisible}
        onOk={handleRejectApplication}
        onCancel={() => setRejectModalVisible(false)}
      >
        <Form
          form={rejectForm}
          layout="vertical"
        >
          <Form.Item
            name="reason"
            label="拒绝原因"
            rules={[{ required: true, message: '请输入拒绝原因' }]}
          >
            <TextArea rows={4} placeholder="请输入拒绝原因" />
          </Form.Item>
        </Form>
      </Modal>

      {/* 创建令牌模态框 */}
      <Modal
        title="创建JWT令牌"
        open={createTokenModalVisible}
        onOk={handleCreateToken}
        onCancel={() => setCreateTokenModalVisible(false)}
        width={600}
      >
        <Form
          form={tokenForm}
          layout="vertical"
          initialValues={{
            token_type: 'mcp',
            permissions: 'read',
            expires_hours: 24 * 30, // 默认30天
          }}
        >
          <Form.Item
            name="name"
            label="令牌名称"
            rules={[{ required: true, message: '请输入令牌名称' }]}
          >
            <Input placeholder="请输入令牌名称" />
          </Form.Item>

          <Form.Item
            name="description"
            label="令牌描述"
          >
            <TextArea rows={3} placeholder="请输入令牌描述" />
          </Form.Item>

          <Form.Item
            name="token_type"
            label="令牌类型"
            rules={[{ required: true, message: '请选择令牌类型' }]}
            tooltip="MCP令牌用于访问MCP服务器，Access令牌用于访问REST API"
          >
            <Select>
              <Option value="mcp">MCP令牌</Option>
              <Option value="access">Access令牌</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="permissions"
            label="权限"
            rules={[{ required: true, message: '请选择权限' }]}
          >
            <Select>
              <Option value="read">只读</Option>
              <Option value="write">只写</Option>
              <Option value="read_write">读写</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="expires_hours"
            label="有效期（小时）"
            tooltip="令牌的有效期，单位为小时，默认为720小时（30天）"
            rules={[{ required: true, message: '请输入过期时间' }]}
          >
            <InputNumber
              style={{ width: '100%' }}
              min={1}
              max={8760} // 最长一年
              placeholder="令牌有效期（小时）"
            />
          </Form.Item>

          <Collapse ghost>
            <Panel header="高级设置" key="advanced">
              <Form.Item
                label="服务器权限"
                tooltip="选择此令牌可以访问的MCP服务器"
              >
                <Table
                  dataSource={mcpServers}
                  rowKey="id"
                  size="small"
                  loading={loadingServers}
                  pagination={false}
                  columns={[
                    {
                      title: '服务器名称',
                      dataIndex: 'name',
                      key: 'name',
                    },
                    {
                      title: '状态',
                      dataIndex: 'status',
                      key: 'status',
                      render: (status) => (
                        <Tag color={status === 'running' ? 'green' : 'red'}>
                          {status === 'running' ? '运行中' : '已停止'}
                        </Tag>
                      ),
                    },
                    {
                      title: '授权',
                      key: 'auth',
                      render: (_, record) => (
                        <Form.Item
                          name={['serverPermissions', record.id]}
                          valuePropName="checked"
                          noStyle
                        >
                          <Switch
                            checkedChildren="已授权"
                            unCheckedChildren="未授权"
                            defaultChecked={false}
                          />
                        </Form.Item>
                      ),
                    },
                  ]}
                />
              </Form.Item>
            </Panel>
          </Collapse>
        </Form>
      </Modal>
    </Card>
  );
};

export default TokenManage;
