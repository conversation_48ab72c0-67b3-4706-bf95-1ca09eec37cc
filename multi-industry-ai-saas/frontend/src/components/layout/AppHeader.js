import React, { useState, useEffect } from 'react';
import { Layout, Menu, Dropdown, Button, Space, Avatar, Typography, Image, Badge, message, Progress, Select } from 'antd';
import { 
  UserOutlined, 
  LogoutOutlined, 
  SettingOutlined, 
  BellOutlined, 
  AppstoreOutlined, 
  AppstoreAddOutlined, 
  <PERSON>Outlined, 
  FolderOutlined, 
  FileImageOutlined, 
  HddOutlined,
  ProjectOutlined,
  TeamOutlined,
  ShopOutlined,
  HomeOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { useTenant } from '../../contexts/TenantContext';
import { useIndustry } from '../../contexts/IndustryContext';
import * as apiService from '../../services/api';
import * as notificationService from '../../services/notificationService';
import { getStorageUsage, getFileTypeStats } from '../../services/storageService';

const { Header } = Layout;
const { Text, Title } = Typography;
const { Option } = Select;

const AppHeader = () => {
  const { user, logout } = useAuth();
  const { 
    currentTenant, 
    currentServiceProvider, 
    currentProject, 
    tenants, 
    projects, 
    switchTenant, 
    switchProject 
  } = useTenant();
  const { getIndustryName } = useIndustry();
  
  const navigate = useNavigate();
  
  const [logoUrl, setLogoUrl] = useState(null);
  const [settings, setSettings] = useState(null);
  const [loading, setLoading] = useState(true);
  const [notifications, setNotifications] = useState([]);
  const [notificationCount, setNotificationCount] = useState(0);
  const [systemTheme, setSystemTheme] = useState('light');
  const [primaryColor, setPrimaryColor] = useState('#1890ff');
  const [storageUsage, setStorageUsage] = useState(null);
  
  // 加载设置
  useEffect(() => {
    const loadSettings = async () => {
      try {
        setLoading(true);
        const themeSettings = await apiService.getThemeSettings();
        setSettings(themeSettings);
        setSystemTheme(themeSettings.theme || 'light');
        setPrimaryColor(themeSettings.primaryColor || '#1890ff');
        
        // 如果有租户，加载租户Logo
        if (currentTenant) {
          setLogoUrl(currentTenant.branding?.logo || null);
        } else if (currentServiceProvider) {
          setLogoUrl(currentServiceProvider.branding?.logo || null);
        }
        
        // 加载存储使用情况
        if (currentTenant) {
          const usage = await getStorageUsage();
          setStorageUsage(usage);
        }
      } catch (error) {
        console.error('加载设置失败:', error);
      } finally {
        setLoading(false);
      }
    };
    
    loadSettings();
    
    // 设置定时器，定期获取通知
    const notificationTimer = setInterval(fetchNotifications, 30000);
    
    // 组件卸载时清除定时器
    return () => {
      clearInterval(notificationTimer);
    };
  }, [currentTenant, currentServiceProvider]);
  
  // 获取通知
  const fetchNotifications = async () => {
    try {
      const response = await notificationService.getNotifications();
      setNotifications(response.notifications || []);
      setNotificationCount(response.unread_count || 0);
    } catch (error) {
      console.error('获取通知失败:', error);
    }
  };
  
  // 处理通知点击
  const handleNotificationClick = async (notification) => {
    try {
      // 标记通知为已读
      await notificationService.markAsRead(notification.id);
      
      // 刷新通知列表
      fetchNotifications();
      
      // 如果有链接，导航到相应页面
      if (notification.link) {
        navigate(notification.link);
      }
    } catch (error) {
      console.error('处理通知失败:', error);
      message.error('处理通知失败');
    }
  };
  
  // 处理租户切换
  const handleTenantChange = async (tenantId) => {
    try {
      await switchTenant(tenantId);
    } catch (error) {
      console.error('切换租户失败:', error);
      message.error('切换租户失败');
    }
  };
  
  // 处理项目切换
  const handleProjectChange = async (projectId) => {
    try {
      await switchProject(projectId);
    } catch (error) {
      console.error('切换项目失败:', error);
      message.error('切换项目失败');
    }
  };
  
  // 通知菜单
  const notificationMenu = {
    items: notifications.slice(0, 5).map((notification, index) => ({
      key: notification.id,
      label: (
        <div style={{ maxWidth: 300 }}>
          <div style={{ fontWeight: notification.read ? 'normal' : 'bold' }}>
            {notification.title}
          </div>
          <div style={{ fontSize: '12px', color: 'rgba(0, 0, 0, 0.45)' }}>
            {notification.content}
          </div>
          <div style={{ fontSize: '12px', color: 'rgba(0, 0, 0, 0.45)', marginTop: 4 }}>
            {new Date(notification.created_at).toLocaleString()}
          </div>
        </div>
      ),
      onClick: () => handleNotificationClick(notification),
    })).concat([
      {
        key: 'all',
        label: <div style={{ textAlign: 'center' }}>查看全部通知</div>,
        onClick: () => navigate('/notifications'),
      },
    ]),
  };
  
  // 用户菜单
  const userMenu = {
    items: [
      {
        key: 'profile',
        icon: <UserOutlined />,
        label: '个人中心',
        onClick: () => navigate('/profile'),
      },
      {
        key: 'settings',
        icon: <SettingOutlined />,
        label: '设置',
        onClick: () => {
          if (user.is_system_admin) {
            navigate('/admin/settings');
          } else if (user.is_service_provider_admin) {
            navigate('/service-provider/settings');
          } else if (user.is_tenant_admin) {
            navigate('/tenant/settings');
          } else {
            navigate('/profile');
          }
        },
      },
      {
        type: 'divider',
      },
      {
        key: 'logout',
        icon: <LogoutOutlined />,
        label: '退出登录',
        onClick: logout,
      },
    ],
  };
  
  // 存储菜单
  const storageMenu = {
    items: [
      {
        key: 'storage',
        label: (
          <div>
            <div style={{ marginBottom: 8 }}>
              存储使用情况
            </div>
            {storageUsage && (
              <>
                <Progress 
                  percent={Math.round((storageUsage.used / storageUsage.total) * 100)} 
                  size="small" 
                  status={storageUsage.used / storageUsage.total > 0.9 ? "exception" : "normal"}
                />
                <div style={{ fontSize: '12px', marginTop: 4 }}>
                  已使用 {(storageUsage.used / 1024 / 1024).toFixed(2)} MB / {(storageUsage.total / 1024 / 1024).toFixed(2)} MB
                </div>
              </>
            )}
          </div>
        ),
      },
      {
        key: 'manage',
        label: '管理存储',
        onClick: () => navigate('/admin/storage'),
      },
    ],
  };
  
  return (
    <Header className="app-header" style={{ 
      display: 'flex', 
      alignItems: 'center', 
      justifyContent: 'space-between', 
      padding: '0 24px', 
      background: systemTheme === 'dark' ? '#001529' : '#fff' 
    }}>
      <div className="logo" style={{ display: 'flex', alignItems: 'center' }}>
        {logoUrl ? (
          <Image
            src={logoUrl}
            alt="Logo"
            preview={false}
            height={32}
            style={{ marginRight: '16px' }}
          />
        ) : (
          <AppstoreAddOutlined style={{ fontSize: '24px', marginRight: '16px', color: primaryColor }} />
        )}
        <Title level={4} style={{ margin: 0, color: systemTheme === 'dark' ? '#fff' : 'inherit' }}>
          {currentTenant?.name || currentServiceProvider?.name || '多行业AI SaaS系统'}
        </Title>
      </div>
      
      <div style={{ display: 'flex', alignItems: 'center' }}>
        {/* 行业类型显示 */}
        {currentTenant && (
          <div style={{ marginRight: 16 }}>
            <Text style={{ color: systemTheme === 'dark' ? '#fff' : 'inherit' }}>
              行业: {getIndustryName()}
            </Text>
          </div>
        )}
        
        {/* 租户选择器 */}
        {(user?.is_system_admin || user?.is_service_provider_admin) && tenants.length > 0 && (
          <Select
            placeholder="选择租户"
            style={{ width: 200, marginRight: 16 }}
            value={currentTenant?.id}
            onChange={handleTenantChange}
            loading={loading}
          >
            {tenants.map(tenant => (
              <Option key={tenant.id} value={tenant.id}>
                {tenant.name}
              </Option>
            ))}
          </Select>
        )}
        
        {/* 项目选择器 */}
        {currentTenant && projects.length > 0 && (
          <Select
            placeholder="选择项目"
            style={{ width: 200, marginRight: 16 }}
            value={currentProject?.id}
            onChange={handleProjectChange}
            loading={loading}
          >
            {projects.map(project => (
              <Option key={project.id} value={project.id}>
                {project.name}
              </Option>
            ))}
          </Select>
        )}
        
        <Space size="middle">
          {/* 存储使用情况 */}
          {currentTenant && (
            <Dropdown menu={storageMenu} placement="bottomRight">
              <Button type="text" icon={<CloudOutlined />} />
            </Dropdown>
          )}
          
          {/* 通知中心 */}
          <Dropdown menu={notificationMenu} placement="bottomRight">
            <Badge count={notificationCount} overflowCount={99}>
              <Button type="text" icon={<BellOutlined />} />
            </Badge>
          </Dropdown>
          
          {/* 用户菜单 */}
          <Dropdown menu={userMenu} placement="bottomRight">
            <Space>
              <Avatar icon={<UserOutlined />} src={user?.avatar_url} />
              <span style={{ color: systemTheme === 'dark' ? '#fff' : 'inherit' }}>
                {user?.full_name || user?.username}
              </span>
            </Space>
          </Dropdown>
        </Space>
      </div>
    </Header>
  );
};

export default AppHeader;
