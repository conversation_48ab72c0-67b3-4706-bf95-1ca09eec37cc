#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试数据库迁移和初始化功能
"""

import asyncio
import logging
from sqlalchemy.ext.asyncio import AsyncSession
from db.database import get_db
from db.migrations import (
    create_tables,
    initialize_system_data,
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def test_initialization():
    """测试数据库初始化"""
    try:
        logger.info("开始测试数据库初始化...")
        
        # 创建表
        logger.info("创建数据库表...")
        await create_tables()
        
        # 初始化系统数据
        logger.info("初始化系统数据...")
        async for db in get_db():
            await initialize_system_data(db)
            break
        
        logger.info("数据库初始化测试完成！")
        
        # 验证数据
        logger.info("验证初始化数据...")
        await verify_initialization_data()
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        raise


async def verify_initialization_data():
    """验证初始化数据"""
    from sqlalchemy import text
    
    async for db in get_db():
        try:
            # 验证行业数据
            result = await db.execute(text("SELECT COUNT(*) FROM industries"))
            industry_count = result.scalar()
            logger.info(f"行业数据数量: {industry_count}")
            
            # 验证系统角色
            result = await db.execute(text("SELECT COUNT(*) FROM roles WHERE is_system_role = TRUE"))
            role_count = result.scalar()
            logger.info(f"系统角色数量: {role_count}")
            
            # 验证系统管理员
            result = await db.execute(text("SELECT COUNT(*) FROM users WHERE is_system_admin = TRUE"))
            admin_count = result.scalar()
            logger.info(f"系统管理员数量: {admin_count}")
            
            # 验证服务商
            result = await db.execute(text("SELECT COUNT(*) FROM service_providers"))
            provider_count = result.scalar()
            logger.info(f"服务商数量: {provider_count}")
            
            # 验证服务商管理员
            result = await db.execute(text("SELECT COUNT(*) FROM users WHERE is_service_provider_admin = TRUE"))
            provider_admin_count = result.scalar()
            logger.info(f"服务商管理员数量: {provider_admin_count}")
            
            # 验证租户
            result = await db.execute(text("SELECT COUNT(*) FROM tenants"))
            tenant_count = result.scalar()
            logger.info(f"租户数量: {tenant_count}")
            
            # 验证租户管理员
            result = await db.execute(text("SELECT COUNT(*) FROM users WHERE is_tenant_admin = TRUE"))
            tenant_admin_count = result.scalar()
            logger.info(f"租户管理员数量: {tenant_admin_count}")
            
            # 显示详细信息
            logger.info("=== 详细信息 ===")
            
            # 显示用户信息
            result = await db.execute(text("""
                SELECT username, email, full_name, role, is_system_admin, is_service_provider_admin, is_tenant_admin
                FROM users
                ORDER BY created_at
            """))
            users = result.fetchall()
            logger.info("用户列表:")
            for user in users:
                logger.info(f"  - {user.username} ({user.email}) - {user.full_name} - 角色: {user.role}")
            
            # 显示服务商信息
            result = await db.execute(text("""
                SELECT name, company_name, contact_person, status
                FROM service_providers
            """))
            providers = result.fetchall()
            logger.info("服务商列表:")
            for provider in providers:
                logger.info(f"  - {provider.name} ({provider.company_name}) - 联系人: {provider.contact_person}")
            
            # 显示租户信息
            result = await db.execute(text("""
                SELECT name, industry_type, schema_name, status
                FROM tenants
            """))
            tenants = result.fetchall()
            logger.info("租户列表:")
            for tenant in tenants:
                logger.info(f"  - {tenant.name} - 行业: {tenant.industry_type} - Schema: {tenant.schema_name}")
            
        except Exception as e:
            logger.error(f"验证数据失败: {e}")
            raise
        finally:
            break


if __name__ == "__main__":
    asyncio.run(test_initialization()) 