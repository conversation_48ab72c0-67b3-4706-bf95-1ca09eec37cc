import api from '../httpClient';
import { getProjectId } from '../httpClient';

const isValidUUID = (id) => /^[0-9a-fA-F-]{36}$/.test(id);

/**
 * 项目-销售上报API
 */
const salesReport = {
  /**
   * 获取销售上报列表
   * @param {Object} params - 查询参数
   * @returns {Promise} - 销售上报列表
   */
  getList: async (params) => {
    const projectId = getProjectId();
    // 只在 store_id 合法时传递
    const query = { ...params };
    if (query.store_id && !isValidUUID(query.store_id)) {
      delete query.store_id;
    }
    return api.get(`/project/${projectId}/sales-reports`, { params: query });
  },

  /**
   * 获取销售上报详情
   * @param {string} id - 销售上报ID
   * @returns {Promise} - 销售上报详情
   */
  getDetail: async (id) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.get(`/project/${projectId}/sales-reports/${id}`);
  },

  /**
   * 创建销售上报
   * @param {Object} data - 销售上报数据
   * @returns {Promise} - 创建结果
   */
  create: async (data) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.post(`/project/${projectId}/sales-reports`, data);
  },

  /**
   * 更新销售上报
   * @param {string} id - 销售上报ID
   * @param {Object} data - 销售上报数据
   * @returns {Promise} - 更新结果
   */
  update: async (id, data) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.put(`/project/${projectId}/sales-reports/${id}`, data);
  },

  /**
   * 更新销售上报状态
   * @param {string} id - 销售上报ID
   * @param {Object} data - 状态数据
   * @returns {Promise} - 更新结果
   */
  updateStatus: async (id, data) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.put(`/project/${projectId}/sales-reports/${id}/status`, data);
  },

  /**
   * 删除销售上报
   * @param {string} id - 销售上报ID
   * @returns {Promise} - 删除结果
   */
  delete: async (id) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.delete(`/project/${projectId}/sales-reports/${id}`);
  },

  /**
   * 添加销售上报项
   * @param {string} reportId - 销售上报ID
   * @param {Object} data - 上报项数据
   * @returns {Promise} - 添加结果
   */
  addItem: async (reportId, data) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.post(`/project/${projectId}/sales-reports/${reportId}/items`, data);
  },

  /**
   * 更新销售上报项
   * @param {string} reportId - 销售上报ID
   * @param {string} itemId - 上报项ID
   * @param {Object} data - 上报项数据
   * @returns {Promise} - 更新结果
   */
  updateItem: async (reportId, itemId, data) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.put(`/project/${projectId}/sales-reports/${reportId}/items/${itemId}`, data);
  },

  /**
   * 删除销售上报项
   * @param {string} reportId - 销售上报ID
   * @param {string} itemId - 上报项ID
   * @returns {Promise} - 删除结果
   */
  deleteItem: async (reportId, itemId) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.delete(`/project/${projectId}/sales-reports/${reportId}/items/${itemId}`);
  },

  /**
   * 获取销售统计数据
   * @param {Object} params - 查询参数
   * @param {string} params.start_date - 开始日期
   * @param {string} params.end_date - 结束日期
   * @param {string} [params.store_id] - 门店ID
   * @param {string} [params.channel_id] - 渠道ID
   * @param {string} [params.group_by] - 分组方式: month, day
   * @returns {Promise} - 销售统计数据
   */
  getStatistics: async (params = {}) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }

    // 确保projectId不是'current'字符串，这会导致后端错误
    if (projectId === 'current') {
      console.warn('使用了"current"作为项目ID，这可能导致后端错误。尝试从localStorage获取实际项目ID');
      const storedProjectId = localStorage.getItem('project_id');
      if (storedProjectId && isValidUUID(storedProjectId)) {
        return api.get(`/project/${storedProjectId}/sales-reports/statistics`, { params });
      }
      throw new Error('无法获取有效的项目ID');
    }

    return api.get(`/project/${projectId}/sales-reports/statistics`, { params });
  }
};

export default salesReport;
