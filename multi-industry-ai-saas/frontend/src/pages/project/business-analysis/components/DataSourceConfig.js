import React, { useState, useEffect } from 'react';
import {
  Card, Tabs, Button, Space, message, Spin, Alert, 
  Form, Input, Switch, Select, Table, Tag, Modal, 
  Tooltip, Progress, Descriptions, Divider, Row, Col,
  List, Avatar, Badge, Typography, Popconfirm
} from 'antd';
import {
  DatabaseOutlined, ApiOutlined, RobotOutlined, 
  AppstoreOutlined, CloudOutlined, SettingOutlined,
  PlusOutlined, EditOutlined, DeleteOutlined,
  CheckCircleOutlined, ExclamationCircleOutlined,
  SyncOutlined, InfoCircleOutlined, LinkOutlined
} from '@ant-design/icons';
import { api } from '../../../../services/api';

const { TabPane } = Tabs;
const { Option } = Select;
const { TextArea } = Input;
const { Text, Title } = Typography;

/**
 * 数据源配置组件
 * 管理经营分析的各种数据源：采集接口、插件、AI等
 */
const DataSourceConfig = () => {
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('api');
  const [dataSources, setDataSources] = useState({
    api: [],
    plugins: [],
    ai: [],
    external: []
  });
  const [modalVisible, setModalVisible] = useState(false);
  const [editingSource, setEditingSource] = useState(null);
  const [form] = Form.useForm();
  const [saving, setSaving] = useState(false);
  const [testing, setTesting] = useState(false);
  const [syncing, setSyncing] = useState(false);

  useEffect(() => {
    fetchDataSources();
  }, []);

  // 获取数据源配置
  const fetchDataSources = async () => {
    try {
      setLoading(true);
      const projectId = localStorage.getItem('project_id');
      if (!projectId) {
        throw new Error('未找到项目ID');
      }
      const response = await api.get(`/project/${projectId}/business-analysis/datasources`);
      setDataSources(response.data || {
        api: [],
        plugins: [],
        ai: [],
        external: []
      });
    } catch (error) {
      console.error('获取数据源配置失败:', error);
      message.error('获取数据源配置失败');
    } finally {
      setLoading(false);
    }
  };

  // 保存数据源配置
  const handleSaveDataSource = async (sourceData) => {
    try {
      setSaving(true);
      const projectId = localStorage.getItem('project_id');
      if (!projectId) {
        throw new Error('未找到项目ID');
      }
      
      if (editingSource) {
        await api.put(`/project/${projectId}/business-analysis/datasources/${editingSource.id}`, sourceData);
        message.success('数据源更新成功');
      } else {
        await api.post(`/project/${projectId}/business-analysis/datasources`, sourceData);
        message.success('数据源创建成功');
      }
      
      setModalVisible(false);
      setEditingSource(null);
      fetchDataSources();
    } catch (error) {
      console.error('保存数据源失败:', error);
      message.error('保存数据源失败');
    } finally {
      setSaving(false);
    }
  };

  // 删除数据源
  const handleDeleteDataSource = async (sourceId) => {
    try {
      const projectId = localStorage.getItem('project_id');
      if (!projectId) {
        throw new Error('未找到项目ID');
      }
      await api.delete(`/project/${projectId}/business-analysis/datasources/${sourceId}`);
      message.success('数据源删除成功');
      fetchDataSources();
    } catch (error) {
      console.error('删除数据源失败:', error);
      message.error('删除数据源失败');
    }
  };

  // 测试数据源连接
  const handleTestConnection = async (source) => {
    try {
      setTesting(true);
      const projectId = localStorage.getItem('project_id');
      if (!projectId) {
        throw new Error('未找到项目ID');
      }
      const response = await api.post(`/project/${projectId}/business-analysis/datasources/${source.id}/test`);
      if (response.success) {
        message.success('连接测试成功');
      } else {
        message.error('连接测试失败');
      }
    } catch (error) {
      console.error('测试连接失败:', error);
      message.error('测试连接失败');
    } finally {
      setTesting(false);
    }
  };

  // 同步数据源数据
  const handleSyncData = async (source) => {
    try {
      setSyncing(true);
      const projectId = localStorage.getItem('project_id');
      if (!projectId) {
        throw new Error('未找到项目ID');
      }
      await api.post(`/project/${projectId}/business-analysis/datasources/${source.id}/sync`);
      message.success('数据同步已启动');
    } catch (error) {
      console.error('数据同步失败:', error);
      message.error('数据同步失败');
    } finally {
      setSyncing(false);
    }
  };

  // 渲染API接口数据源
  const renderApiDataSources = () => {
    const columns = [
      {
        title: '接口名称',
        dataIndex: 'name',
        key: 'name',
        render: (text, record) => (
          <Space>
            <ApiOutlined />
            <div>
              <div>{text}</div>
              <Text type="secondary" style={{ fontSize: '12px' }}>
                {record.url}
              </Text>
            </div>
          </Space>
        )
      },
      {
        title: '数据类型',
        dataIndex: 'dataType',
        key: 'dataType',
        render: (type) => {
          const typeMap = {
            'sales': { text: '销售数据', color: 'blue' },
            'inventory': { text: '库存数据', color: 'green' },
            'customer': { text: '客户数据', color: 'orange' },
            'product': { text: '商品数据', color: 'purple' },
            'financial': { text: '财务数据', color: 'red' }
          };
          const config = typeMap[type] || { text: type, color: 'default' };
          return <Tag color={config.color}>{config.text}</Tag>;
        }
      },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        render: (status) => (
          <Badge 
            status={status === 'active' ? 'success' : 'error'} 
            text={status === 'active' ? '正常' : '异常'} 
          />
        )
      },
      {
        title: '最后同步',
        dataIndex: 'lastSync',
        key: 'lastSync',
        render: (time) => time ? new Date(time).toLocaleString() : '未同步'
      },
      {
        title: '操作',
        key: 'actions',
        render: (_, record) => (
          <Space>
            <Tooltip title="测试连接">
              <Button 
                icon={<LinkOutlined />} 
                size="small"
                onClick={() => handleTestConnection(record)}
              />
            </Tooltip>
            <Tooltip title="同步数据">
              <Button 
                icon={<SyncOutlined />} 
                size="small"
                onClick={() => handleSyncData(record)}
              />
            </Tooltip>
            <Tooltip title="编辑">
              <Button 
                icon={<EditOutlined />} 
                size="small"
                onClick={() => {
                  setEditingSource(record);
                  form.setFieldsValue(record);
                  setModalVisible(true);
                }}
              />
            </Tooltip>
            <Popconfirm
              title="确定删除此数据源吗？"
              onConfirm={() => handleDeleteDataSource(record.id)}
            >
              <Button 
                icon={<DeleteOutlined />} 
                size="small" 
                danger
              />
            </Popconfirm>
          </Space>
        )
      }
    ];

    return (
      <div>
        <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between' }}>
          <div>
            <Title level={4}>API接口数据源</Title>
            <Text type="secondary">配置外部API接口，自动采集业务数据</Text>
          </div>
          <Button 
            type="primary" 
            icon={<PlusOutlined />}
            onClick={() => {
              setActiveTab('api');
              setEditingSource(null);
              form.resetFields();
              setModalVisible(true);
            }}
          >
            添加API接口
          </Button>
        </div>
        
        <Table
          dataSource={dataSources.api || []}
          columns={columns}
          rowKey="id"
          pagination={{ pageSize: 10 }}
        />
      </div>
    );
  };

  // 渲染插件数据源
  const renderPluginDataSources = () => {
    const availablePlugins = [
      {
        id: 'dingtalk',
        name: '钉钉插件',
        description: '从钉钉获取组织架构、用户活跃度等数据',
        icon: <AppstoreOutlined style={{ color: '#1890ff' }} />,
        status: 'available'
      },
      {
        id: 'wechat',
        name: '微信插件',
        description: '从微信小程序/公众号获取用户行为数据',
        icon: <AppstoreOutlined style={{ color: '#52c41a' }} />,
        status: 'available'
      },
      {
        id: 'erp',
        name: 'ERP系统',
        description: '从ERP系统同步商品、库存、订单数据',
        icon: <DatabaseOutlined style={{ color: '#722ed1' }} />,
        status: 'available'
      },
      {
        id: 'crm',
        name: 'CRM系统',
        description: '从CRM系统获取客户关系和销售数据',
        icon: <DatabaseOutlined style={{ color: '#fa8c16' }} />,
        status: 'available'
      }
    ];

    return (
      <div>
        <div style={{ marginBottom: 16 }}>
          <Title level={4}>插件数据源</Title>
          <Text type="secondary">启用已安装的插件作为数据源</Text>
        </div>

        <Row gutter={[16, 16]}>
          {availablePlugins.map(plugin => {
            const isEnabled = (dataSources.plugins || []).some(p => p.pluginId === plugin.id);
            return (
              <Col xs={24} sm={12} lg={8} key={plugin.id}>
                <Card
                  hoverable
                  actions={[
                    <Button
                      type={isEnabled ? 'default' : 'primary'}
                      size="small"
                      onClick={() => {
                        if (isEnabled) {
                          // 禁用插件
                          const pluginToDelete = (dataSources.plugins || []).find(p => p.pluginId === plugin.id);
                          if (pluginToDelete) {
                            handleDeleteDataSource(pluginToDelete.id);
                          }
                        } else {
                          // 启用插件
                          handleSaveDataSource({
                            name: plugin.name,
                            pluginId: plugin.id,
                            description: plugin.description,
                            enabled: true
                          });
                        }
                      }}
                    >
                      {isEnabled ? '禁用' : '启用'}
                    </Button>,
                    <Button
                      icon={<SettingOutlined />}
                      size="small"
                      disabled={!isEnabled}
                      onClick={() => {
                        const pluginSource = (dataSources.plugins || []).find(p => p.pluginId === plugin.id);
                        if (pluginSource) {
                          setEditingSource(pluginSource);
                          form.setFieldsValue(pluginSource);
                          setModalVisible(true);
                        }
                      }}
                    >
                      配置
                    </Button>
                  ]}
                >
                  <Card.Meta
                    avatar={plugin.icon}
                    title={
                      <Space>
                        {plugin.name}
                        {isEnabled && <Badge status="success" />}
                      </Space>
                    }
                    description={plugin.description}
                  />
                </Card>
              </Col>
            );
          })}
        </Row>
      </div>
    );
  };

  // 渲染AI数据源
  const renderAiDataSources = () => {
    const aiSources = [
      {
        id: 'market_analysis',
        name: '市场分析AI',
        description: '基于市场数据进行竞品分析和趋势预测',
        type: 'analysis',
        status: 'active'
      },
      {
        id: 'customer_insight',
        name: '客户洞察AI',
        description: '分析客户行为，提供个性化推荐',
        type: 'insight',
        status: 'active'
      },
      {
        id: 'sales_forecast',
        name: '销售预测AI',
        description: '基于历史数据预测销售趋势',
        type: 'forecast',
        status: 'active'
      }
    ];

    return (
      <div>
        <div style={{ marginBottom: 16 }}>
          <Title level={4}>AI智能数据源</Title>
          <Text type="secondary">配置AI模型进行数据分析和预测</Text>
        </div>

        <List
          dataSource={aiSources}
          renderItem={item => (
            <List.Item
              actions={[
                <Switch 
                  checked={(dataSources.ai || []).some(ai => ai.aiId === item.id)}
                  onChange={(checked) => {
                    if (checked) {
                      handleSaveDataSource({
                        name: item.name,
                        aiId: item.id,
                        description: item.description,
                        type: item.type,
                        enabled: true
                      });
                    } else {
                      const aiSource = (dataSources.ai || []).find(ai => ai.aiId === item.id);
                      if (aiSource) {
                        handleDeleteDataSource(aiSource.id);
                      }
                    }
                  }}
                />,
                <Button 
                  icon={<SettingOutlined />} 
                  size="small"
                  disabled={!(dataSources.ai || []).some(ai => ai.aiId === item.id)}
                >
                  配置
                </Button>
              ]}
            >
              <List.Item.Meta
                avatar={<RobotOutlined style={{ fontSize: '24px', color: '#1890ff' }} />}
                title={item.name}
                description={item.description}
              />
            </List.Item>
          )}
        />
      </div>
    );
  };

  // 渲染外部数据源
  const renderExternalDataSources = () => {
    const columns = [
      {
        title: '数据源名称',
        dataIndex: 'name',
        key: 'name',
        render: (text, record) => (
          <Space>
            <CloudOutlined />
            <div>
              <div>{text}</div>
              <Text type="secondary" style={{ fontSize: '12px' }}>
                {record.provider}
              </Text>
            </div>
          </Space>
        )
      },
      {
        title: '数据类型',
        dataIndex: 'dataType',
        key: 'dataType',
        render: (type) => <Tag>{type}</Tag>
      },
      {
        title: '更新频率',
        dataIndex: 'frequency',
        key: 'frequency',
        render: (freq) => {
          const freqMap = {
            'realtime': '实时',
            'hourly': '每小时',
            'daily': '每日',
            'weekly': '每周'
          };
          return freqMap[freq] || freq;
        }
      },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        render: (status) => (
          <Badge 
            status={status === 'active' ? 'success' : 'error'} 
            text={status === 'active' ? '正常' : '异常'} 
          />
        )
      },
      {
        title: '操作',
        key: 'actions',
        render: (_, record) => (
          <Space>
            <Button 
              icon={<SyncOutlined />} 
              size="small"
              onClick={() => handleSyncData(record)}
            >
              同步
            </Button>
            <Button 
              icon={<EditOutlined />} 
              size="small"
              onClick={() => {
                setEditingSource(record);
                form.setFieldsValue(record);
                setModalVisible(true);
              }}
            />
            <Popconfirm
              title="确定删除此数据源吗？"
              onConfirm={() => handleDeleteDataSource(record.id)}
            >
              <Button 
                icon={<DeleteOutlined />} 
                size="small" 
                danger
              />
            </Popconfirm>
          </Space>
        )
      }
    ];

    return (
      <div>
        <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between' }}>
          <div>
            <Title level={4}>外部数据源</Title>
            <Text type="secondary">配置第三方数据提供商，如市场数据、行业报告等</Text>
          </div>
          <Button 
            type="primary" 
            icon={<PlusOutlined />}
            onClick={() => {
              setActiveTab('external');
              setEditingSource(null);
              form.resetFields();
              setModalVisible(true);
            }}
          >
            添加外部数据源
          </Button>
        </div>
        
        <Table
          dataSource={dataSources.external || []}
          columns={columns}
          rowKey="id"
          pagination={{ pageSize: 10 }}
        />
      </div>
    );
  };

  // 渲染配置表单
  const renderConfigForm = () => {
    const getFormFields = () => {
      switch (activeTab) {
        case 'api':
          return (
            <>
              <Form.Item name="name" label="接口名称" rules={[{ required: true }]}>
                <Input placeholder="请输入接口名称" />
              </Form.Item>
              <Form.Item name="url" label="接口地址" rules={[{ required: true }]}>
                <Input placeholder="https://api.example.com/data" />
              </Form.Item>
              <Form.Item name="method" label="请求方法" rules={[{ required: true }]}>
                <Select>
                  <Option value="GET">GET</Option>
                  <Option value="POST">POST</Option>
                </Select>
              </Form.Item>
              <Form.Item name="dataType" label="数据类型" rules={[{ required: true }]}>
                <Select>
                  <Option value="sales">销售数据</Option>
                  <Option value="inventory">库存数据</Option>
                  <Option value="customer">客户数据</Option>
                  <Option value="product">商品数据</Option>
                  <Option value="financial">财务数据</Option>
                </Select>
              </Form.Item>
              <Form.Item name="headers" label="请求头">
                <TextArea placeholder="JSON格式的请求头" rows={3} />
              </Form.Item>
              <Form.Item name="frequency" label="同步频率" rules={[{ required: true }]}>
                <Select>
                  <Option value="realtime">实时</Option>
                  <Option value="hourly">每小时</Option>
                  <Option value="daily">每日</Option>
                  <Option value="weekly">每周</Option>
                </Select>
              </Form.Item>
            </>
          );
        case 'external':
          return (
            <>
              <Form.Item name="name" label="数据源名称" rules={[{ required: true }]}>
                <Input placeholder="请输入数据源名称" />
              </Form.Item>
              <Form.Item name="provider" label="数据提供商" rules={[{ required: true }]}>
                <Input placeholder="如：艾瑞咨询、易观等" />
              </Form.Item>
              <Form.Item name="dataType" label="数据类型" rules={[{ required: true }]}>
                <Input placeholder="如：行业报告、市场数据等" />
              </Form.Item>
              <Form.Item name="accessKey" label="访问密钥">
                <Input.Password placeholder="API访问密钥" />
              </Form.Item>
              <Form.Item name="frequency" label="更新频率" rules={[{ required: true }]}>
                <Select>
                  <Option value="daily">每日</Option>
                  <Option value="weekly">每周</Option>
                  <Option value="monthly">每月</Option>
                </Select>
              </Form.Item>
            </>
          );
        default:
          return (
            <>
              <Form.Item name="name" label="名称" rules={[{ required: true }]}>
                <Input placeholder="请输入名称" />
              </Form.Item>
              <Form.Item name="description" label="描述">
                <TextArea placeholder="请输入描述" rows={3} />
              </Form.Item>
            </>
          );
      }
    };

    return (
      <Modal
        title={`${editingSource ? '编辑' : '添加'}数据源`}
        visible={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          setEditingSource(null);
          form.resetFields();
        }}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSaveDataSource}
        >
          {getFormFields()}
          
          <Form.Item name="enabled" label="启用状态" valuePropName="checked">
            <Switch />
          </Form.Item>
          
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                保存
              </Button>
              <Button onClick={() => setModalVisible(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    );
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>加载数据源配置...</div>
      </div>
    );
  }

  return (
    <div className="datasource-config">
      <Alert
        message="数据源配置"
        description="配置各种数据源以支持经营分析功能。系统将自动从这些数据源采集数据，并通过AI进行智能分析。"
        type="info"
        showIcon
        style={{ marginBottom: 24 }}
      />

      <Tabs 
        activeKey={activeTab} 
        onChange={setActiveTab}
        size="large"
      >
        <TabPane 
          tab={
            <Space>
              <ApiOutlined />
              API接口
              <Badge count={dataSources.api?.length || 0} />
            </Space>
          } 
          key="api"
        >
          {renderApiDataSources()}
        </TabPane>
        
        <TabPane 
          tab={
            <Space>
              <AppstoreOutlined />
              插件数据源
              <Badge count={dataSources.plugins?.length || 0} />
            </Space>
          } 
          key="plugins"
        >
          {renderPluginDataSources()}
        </TabPane>
        
        <TabPane 
          tab={
            <Space>
              <RobotOutlined />
              AI数据源
              <Badge count={dataSources.ai?.length || 0} />
            </Space>
          } 
          key="ai"
        >
          {renderAiDataSources()}
        </TabPane>
        
        <TabPane 
          tab={
            <Space>
              <CloudOutlined />
              外部数据源
              <Badge count={dataSources.external?.length || 0} />
            </Space>
          } 
          key="external"
        >
          {renderExternalDataSources()}
        </TabPane>
      </Tabs>

      {renderConfigForm()}
    </div>
  );
};

export default DataSourceConfig; 