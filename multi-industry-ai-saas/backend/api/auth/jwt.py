#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
JWT认证模块

提供JWT令牌的生成、验证和管理功能
"""

import os
import jwt
import uuid
import logging
from typing import Dict, Any, Optional, List, Union, Set
from datetime import datetime, timedelta
from dataclasses import dataclass

from fastapi import Depends, HTTPException, status
from fastapi.security import HTT<PERSON><PERSON>earer, HTTPAuthorizationCredentials
from pydantic import BaseModel

from core.config import settings

logger = logging.getLogger(__name__)

# JWT配置
JWT_SECRET_KEY = settings.SECRET_KEY  # 使用统一的SECRET_KEY
JWT_ALGORITHM = settings.ALGORITHM  # 使用统一的算法
ACCESS_TOKEN_EXPIRE_MINUTES = 60 * 24 * 30  # 30天
REFRESH_TOKEN_EXPIRE_MINUTES = 60 * 24 * 90  # 90天

# JWT认证依赖
oauth2_scheme = HTTPBearer(auto_error=False)


@dataclass
class JWTPayload:
    """JWT负载"""
    sub: str  # 用户ID
    token_id: str  # 令牌ID
    token_type: str  # 令牌类型：access, refresh, mcp
    project_id: Optional[str] = None  # 项目ID
    permissions: Optional[List[str]] = None  # 权限列表
    server_permissions: Optional[Dict[str, List[str]]] = None  # 服务器权限映射
    default_permissions: Optional[List[str]] = None  # 默认权限
    exp: Optional[int] = None  # 过期时间
    iat: Optional[int] = None  # 签发时间
    jti: Optional[str] = None  # JWT ID


def create_jwt_token(
    subject: str,
    token_id: str,
    token_type: str = "access",
    project_id: Optional[str] = None,
    permissions: Optional[List[str]] = None,
    server_permissions: Optional[Dict[str, List[str]]] = None,
    default_permissions: Optional[List[str]] = None,
    expires_delta: Optional[timedelta] = None,
) -> str:
    """
    创建JWT令牌
    
    Args:
        subject: 用户ID
        token_id: 令牌ID
        token_type: 令牌类型：access, refresh, mcp
        project_id: 项目ID
        permissions: 权限列表
        server_permissions: 服务器权限映射
        default_permissions: 默认权限
        expires_delta: 过期时间
        
    Returns:
        str: JWT令牌
    """
    if expires_delta is None:
        if token_type == "refresh":
            expires_delta = timedelta(minutes=REFRESH_TOKEN_EXPIRE_MINUTES)
        else:
            expires_delta = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    
    expire = datetime.utcnow() + expires_delta
    
    payload = {
        "sub": subject,
        "token_id": token_id,
        "token_type": token_type,
        "exp": expire.timestamp(),
        "iat": datetime.utcnow().timestamp(),
        "jti": str(uuid.uuid4()),
    }
    
    if project_id:
        payload["project_id"] = project_id
    
    if permissions:
        payload["permissions"] = permissions
    
    if server_permissions:
        payload["server_permissions"] = server_permissions
    
    if default_permissions:
        payload["default_permissions"] = default_permissions
    
    return jwt.encode(payload, JWT_SECRET_KEY, algorithm=JWT_ALGORITHM)


def decode_jwt_token(token: str) -> JWTPayload:
    """
    解码JWT令牌
    
    Args:
        token: JWT令牌
        
    Returns:
        JWTPayload: JWT负载
        
    Raises:
        HTTPException: 如果令牌无效
    """
    try:
        payload = jwt.decode(token, JWT_SECRET_KEY, algorithms=[JWT_ALGORITHM])
        
        return JWTPayload(
            sub=payload.get("sub"),
            token_id=payload.get("token_id"),
            token_type=payload.get("token_type"),
            project_id=payload.get("project_id"),
            permissions=payload.get("permissions"),
            server_permissions=payload.get("server_permissions"),
            default_permissions=payload.get("default_permissions"),
            exp=payload.get("exp"),
            iat=payload.get("iat"),
            jti=payload.get("jti"),
        )
    except jwt.PyJWTError as e:
        logger.error(f"JWT解码失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的认证令牌",
            headers={"WWW-Authenticate": "Bearer"},
        )


async def get_jwt_token(
    authorization: Optional[HTTPAuthorizationCredentials] = Depends(oauth2_scheme),
) -> Optional[str]:
    """
    从请求头中获取JWT令牌
    
    Args:
        authorization: 认证凭证
        
    Returns:
        Optional[str]: JWT令牌，如果不存在则返回None
    """
    if authorization is None:
        return None
    return authorization.credentials


async def verify_jwt_token(
    token: Optional[str] = Depends(get_jwt_token),
    required_permissions: Optional[List[str]] = None,
    required_server_id: Optional[str] = None,
) -> Optional[JWTPayload]:
    """
    验证JWT令牌
    
    Args:
        token: JWT令牌
        required_permissions: 所需权限列表
        required_server_id: 所需服务器ID
        
    Returns:
        Optional[JWTPayload]: JWT负载，如果验证失败则返回None
        
    Raises:
        HTTPException: 如果令牌无效或权限不足
    """
    if token is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="未提供认证令牌",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    try:
        # 解码JWT令牌
        payload = decode_jwt_token(token)
        
        # 检查令牌类型
        if payload.token_type not in ["access", "mcp"]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"令牌类型 {payload.token_type} 不允许访问此资源",
            )
        
        # 检查权限
        if required_permissions:
            has_permission = False
            
            # 检查服务器特定权限
            if required_server_id and payload.server_permissions:
                server_perms = payload.server_permissions.get(required_server_id, [])
                if any(perm in server_perms for perm in required_permissions):
                    has_permission = True
            
            # 检查默认权限
            if not has_permission and payload.default_permissions:
                if any(perm in payload.default_permissions for perm in required_permissions):
                    has_permission = True
            
            if not has_permission:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"权限不足，需要权限: {', '.join(required_permissions)}",
                )
        
        return payload
    except Exception as e:
        logger.error(f"JWT令牌验证失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的认证令牌",
            headers={"WWW-Authenticate": "Bearer"},
        )
