#!/usr/bin/env python
# -*- coding: utf-8 -*-

from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any, Union
from datetime import datetime
from enum import Enum

class DataSourceType(str, Enum):
    """数据源类型"""
    API = "api"
    PLUGIN = "plugins"
    AI = "ai"
    EXTERNAL = "external"

class DataSourceStatus(str, Enum):
    """数据源状态"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    ERROR = "error"

class DataSourceCreate(BaseModel):
    """创建数据源"""
    name: str = Field(..., description="数据源名称")
    type: DataSourceType = Field(..., description="数据源类型")
    description: Optional[str] = Field(None, description="描述")
    url: Optional[str] = Field(None, description="API地址")
    method: Optional[str] = Field("GET", description="请求方法")
    headers: Optional[Dict[str, Any]] = Field(None, description="请求头")
    data_type: Optional[str] = Field(None, description="数据类型")
    frequency: Optional[str] = Field("daily", description="同步频率")
    enabled: bool = Field(True, description="是否启用")
    plugin_id: Optional[str] = Field(None, description="插件ID")
    ai_id: Optional[str] = Field(None, description="AI模型ID")
    provider: Optional[str] = Field(None, description="数据提供商")
    access_key: Optional[str] = Field(None, description="访问密钥")

class DataSourceUpdate(BaseModel):
    """更新数据源"""
    name: Optional[str] = Field(None, description="数据源名称")
    description: Optional[str] = Field(None, description="描述")
    url: Optional[str] = Field(None, description="API地址")
    method: Optional[str] = Field(None, description="请求方法")
    headers: Optional[Dict[str, Any]] = Field(None, description="请求头")
    data_type: Optional[str] = Field(None, description="数据类型")
    frequency: Optional[str] = Field(None, description="同步频率")
    enabled: Optional[bool] = Field(None, description="是否启用")
    provider: Optional[str] = Field(None, description="数据提供商")
    access_key: Optional[str] = Field(None, description="访问密钥")

class DataSourceResponse(BaseModel):
    """数据源响应"""
    id: str = Field(..., description="数据源ID")
    name: str = Field(..., description="数据源名称")
    type: DataSourceType = Field(..., description="数据源类型")
    description: Optional[str] = Field(None, description="描述")
    url: Optional[str] = Field(None, description="API地址")
    method: Optional[str] = Field(None, description="请求方法")
    data_type: Optional[str] = Field(None, description="数据类型")
    frequency: Optional[str] = Field(None, description="同步频率")
    status: DataSourceStatus = Field(..., description="状态")
    enabled: bool = Field(..., description="是否启用")
    last_sync: Optional[datetime] = Field(None, description="最后同步时间")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

class AnalysisInsightType(str, Enum):
    """洞察类型"""
    OPPORTUNITY = "opportunity"
    WARNING = "warning"
    SUGGESTION = "suggestion"

class AnalysisInsightPriority(str, Enum):
    """洞察优先级"""
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"

class AnalysisInsight(BaseModel):
    """分析洞察"""
    type: AnalysisInsightType = Field(..., description="洞察类型")
    priority: AnalysisInsightPriority = Field(..., description="优先级")
    title: str = Field(..., description="标题")
    content: str = Field(..., description="内容")
    suggestion: Optional[str] = Field(None, description="建议")
    created_at: Optional[datetime] = Field(None, description="创建时间")

class ProductAnalysisData(BaseModel):
    """商品分析数据"""
    total_products: int = Field(..., description="商品总数")
    total_categories: int = Field(..., description="分类总数")
    top_products: List[Dict[str, Any]] = Field(..., description="热销商品")
    category_distribution: List[Dict[str, Any]] = Field(..., description="分类分布")
    price_analysis: Dict[str, Any] = Field(..., description="价格分析")
    inventory_status: Dict[str, Any] = Field(..., description="库存状态")

class CustomerAnalysisData(BaseModel):
    """客户分析数据"""
    total_customers: int = Field(..., description="客户总数")
    new_customers_this_month: int = Field(..., description="本月新客户")
    customer_segments: List[Dict[str, Any]] = Field(..., description="客户分群")
    customer_lifecycle: Dict[str, Any] = Field(..., description="客户生命周期")
    geographic_distribution: List[Dict[str, Any]] = Field(..., description="地域分布")
    age_distribution: List[Dict[str, Any]] = Field(..., description="年龄分布")
    retention_metrics: Dict[str, Any] = Field(..., description="留存指标")

class ChannelAnalysisData(BaseModel):
    """渠道分析数据"""
    total_channels: int = Field(..., description="渠道总数")
    channel_performance: List[Dict[str, Any]] = Field(..., description="渠道表现")
    channel_types: Dict[str, int] = Field(..., description="渠道类型统计")
    revenue_by_channel: List[Dict[str, Any]] = Field(..., description="渠道收入")
    conversion_rates: List[Dict[str, Any]] = Field(..., description="转化率")
    customer_acquisition_cost: List[Dict[str, Any]] = Field(..., description="获客成本")

class BusinessOverviewData(BaseModel):
    """经营概览数据"""
    revenue: float = Field(..., description="总收入")
    revenue_trend: float = Field(..., description="收入趋势")
    orders: int = Field(..., description="订单数")
    orders_trend: float = Field(..., description="订单趋势")
    customers: int = Field(..., description="客户数")
    customer_trend: float = Field(..., description="客户趋势")
    products: int = Field(..., description="商品数")
    channels: int = Field(..., description="渠道数")
    channel_trend: float = Field(..., description="渠道趋势")
    ai_suggestions: int = Field(..., description="AI建议数")
    analysis_date: datetime = Field(..., description="分析日期")

class BusinessAnalysisResponse(BaseModel):
    """经营分析响应"""
    success: bool = Field(..., description="是否成功")
    data: Union[
        BusinessOverviewData,
        List[AnalysisInsight],
        ProductAnalysisData,
        CustomerAnalysisData,
        ChannelAnalysisData,
        Dict[str, Any]
    ] = Field(..., description="数据")
    message: Optional[str] = Field(None, description="消息")

class DataSourceTestResult(BaseModel):
    """数据源测试结果"""
    status: str = Field(..., description="连接状态")
    response_time: Optional[str] = Field(None, description="响应时间")
    last_test: datetime = Field(..., description="测试时间")
    error_message: Optional[str] = Field(None, description="错误信息")

class DataSourceSyncResult(BaseModel):
    """数据源同步结果"""
    sync_id: str = Field(..., description="同步ID")
    status: str = Field(..., description="同步状态")
    started_at: datetime = Field(..., description="开始时间")
    completed_at: Optional[datetime] = Field(None, description="完成时间")
    records_synced: Optional[int] = Field(None, description="同步记录数")
    error_message: Optional[str] = Field(None, description="错误信息") 