version: '3.3'

services:
  # 后端API服务
  retail-ai-saas-backend:
    build:
      context: ../backend
      dockerfile: ${DOCKERFILE_BACKEND:-../docker/backend/Dockerfile}
    container_name: retail-ai-saas-backend
    restart: always
    volumes:
      - ${BACKEND_VOLUME:-../backend:/app}
      - ../uploads:/app/uploads
      - /var/run/docker.sock:/var/run/docker.sock
    environment:
      - DEBUG=${DEBUG:-false}
      - POSTGRES_SERVER=retail-ai-saas-db
      - POSTGRES_USER=${POSTGRES_USER:-postgres}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-postgres}
      - POSTGRES_DB=${POSTGRES_DB:-retail_ai_saas}
      - POSTGRES_PORT=5432
      - REDIS_HOST=retail-ai-saas-redis
      - REDIS_PORT=6379
      - REDIS_DB=0
      - SECRET_KEY=${SECRET_KEY:-dev_secret_key_change_in_production}
      - ALLOWED_HOSTS=${ALLOWED_HOSTS:-*}
      - CORS_ORIGINS=${CORS_ORIGINS:-http://localhost:3000,http://localhost:8080,https://saas.houshanai.com,http://**************:3001,http://**************}
      - STORAGE_LOCAL_PATH=/app/uploads
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - LOGURU_LEVEL=${LOGURU_LEVEL:-INFO}
      - UVICORN_LOG_LEVEL=debug
      - MCP_CONTAINER_INSTANCES_PATH=/app/mcp_servers
      - MCP_TARGET_API_BASE_URL=http://retail-ai-saas-backend:8000
      - DOCKER_ENV=true
    ports:
      - "${BACKEND_PORT:-8001}:8000"
    depends_on:
      - retail-ai-saas-db
      - retail-ai-saas-redis
    networks:
      - retail-ai-saas-network
    command: ${BACKEND_COMMAND:-uvicorn main:app --host 0.0.0.0 --port 8000}

  # 前端服务
  retail-ai-saas-frontend:
    build:
      context: ../frontend
      dockerfile: ${DOCKERFILE_FRONTEND:-../docker/frontend/Dockerfile}
    container_name: retail-ai-saas-frontend
    restart: always
    volumes:
      - ${FRONTEND_VOLUME:-../frontend:/app}
      - /app/node_modules
    ports:
      - "${FRONTEND_PORT:-3001}:3000"
    environment:
      - NODE_ENV=production
      - REACT_APP_API_URL=/api
      - WDS_SOCKET_HOST=0.0.0.0
      - WDS_SOCKET_PORT=0
      - WDS_SOCKET_PATH=auto
      - WDS_SOCKET=false
      - FAST_REFRESH=false
      - DANGEROUSLY_DISABLE_HOST_CHECK=${DANGEROUSLY_DISABLE_HOST_CHECK:-true}
    depends_on:
      - retail-ai-saas-backend
    networks:
      - retail-ai-saas-network
    command: ${FRONTEND_COMMAND:-sh -c "npm run build && npx serve -s build -l 3000"}

  # 数据库服务
  retail-ai-saas-db:
    image: pgvector/pgvector:pg14
    container_name: retail-ai-saas-db
    restart: always
    volumes:
      - postgres_data:/var/lib/postgresql/data
    environment:
      - POSTGRES_USER=${POSTGRES_USER:-postgres}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-postgres}
      - POSTGRES_DB=${POSTGRES_DB:-retail_ai_saas}
    ports:
      - "${DB_PORT:-5433}:5432"
    networks:
      - retail-ai-saas-network

  # Redis缓存服务
  retail-ai-saas-redis:
    image: redis:7-alpine
    container_name: retail-ai-saas-redis
    restart: always
    volumes:
      - redis_data:/data
    ports:
      - "${REDIS_PORT:-6380}:6379"
    networks:
      - retail-ai-saas-network

  # Nginx服务
  retail-ai-saas-nginx:
    image: nginx:1.23-alpine
    container_name: retail-ai-saas-nginx
    restart: always
    volumes:
      - ../docker/nginx/nginx.conf:/etc/nginx/conf.d/default.conf
      - ../uploads:/var/www/uploads
    ports:
      - "${NGINX_PORT:-8080}:80"
    depends_on:
      - retail-ai-saas-backend
      - retail-ai-saas-frontend
    networks:
      - retail-ai-saas-network

volumes:
  postgres_data:
  redis_data:

networks:
  retail-ai-saas-network:
    driver: bridge
