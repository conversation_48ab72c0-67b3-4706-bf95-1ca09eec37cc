import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tooltip,
  Tag,
  Typography,
  Modal,
  Form,
  Input,
  Select,
  Divider,
  Popconfirm,
  message,
  Spin,
  Alert,
  Row,
  Col,
  Statistic,
  Upload,
  InputNumber,
  Tabs,
  List,
  Avatar,
  Badge
} from 'antd';
import {
  ProjectOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  SettingOutlined,
  UploadOutlined,
  TeamOutlined,
  CloudOutlined,
  ApartmentOutlined,
  UserAddOutlined,
  UserDeleteOutlined,
  AppstoreOutlined,
  AppstoreAddOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { useTenant } from '../../contexts/TenantContext';
import apiService from '../../services/api';

const { Title, Paragraph, Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;
const { TabPane } = Tabs;

const ProjectManagement = () => {
  const navigate = useNavigate();
  const { currentTenant, fetchProjects } = useTenant();

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [projects, setProjects] = useState([]);
  const [industries, setIndustries] = useState([]);
  const [users, setUsers] = useState([]);
  const [plugins, setPlugins] = useState([]);
  const [modalOpen, setModalOpen] = useState(false);
  const [modalTitle, setModalTitle] = useState('');
  const [editingProject, setEditingProject] = useState(null);
  const [form] = Form.useForm();
  const [statistics, setStatistics] = useState({
    total: 0,
    active: 0,
    inactive: 0,
    users: 0
  });
  const [activeTab, setActiveTab] = useState('1');
  const [userModalOpen, setUserModalOpen] = useState(false);
  const [selectedProject, setSelectedProject] = useState(null);
  const [projectUsers, setProjectUsers] = useState([]);
  const [userForm] = Form.useForm();

  // 加载项目数据
  useEffect(() => {
    if (currentTenant) {
      fetchProjectData();
      fetchIndustries();
      fetchUsers();
      fetchPlugins();
    }
  }, [currentTenant]);

  // 获取项目列表
  const fetchProjectData = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await apiService.getProjects(currentTenant?.id);
      setProjects(response);

      // 计算统计数据
      const stats = {
        total: response.length,
        active: response.filter(p => p.status === 'active').length,
        inactive: response.filter(p => p.status !== 'active').length,
        users: response.reduce((sum, p) => sum + (p.user_count || 0), 0)
      };

      setStatistics(stats);
    } catch (error) {
      console.error('获取项目列表失败:', error);
      setError('获取项目列表失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 获取行业列表
  const fetchIndustries = async () => {
    try {
      const response = await apiService.getIndustries({ is_active: true });
      setIndustries(response);
    } catch (error) {
      console.error('获取行业列表失败:', error);
      message.error('获取行业列表失败');
    }
  };

  // 获取用户列表
  const fetchUsers = async () => {
    try {
      const response = await apiService.getUsers({ tenant_id: currentTenant?.id });
      setUsers(response);
    } catch (error) {
      console.error('获取用户列表失败:', error);
      message.error('获取用户列表失败');
    }
  };

  // 获取插件列表
  const fetchPlugins = async () => {
    try {
      // 这里应该调用API获取插件列表
      // 为了演示，使用模拟数据
      const mockPlugins = [
        { id: 'dingtalk', name: '钉钉集成', description: '集成钉钉通知和登录', icon: 'https://img.alicdn.com/tfs/TB1pTXtcQP2gK0jSZPxXXacQpXa-128-128.png', status: 'active' },
        { id: 'wechat', name: '微信集成', description: '集成微信通知和登录', icon: 'https://res.wx.qq.com/a/wx_fed/assets/res/NTI4MWU5.ico', status: 'active' },
        { id: 'saip', name: 'SAIP插件', description: '集成SAIP API调用', icon: '/plugins/saip/icon.png', status: 'active' },
        { id: 'mcp', name: 'MCP插件', description: '集成MCP大模型调用', icon: '/plugins/mcp/icon.png', status: 'active' },
        { id: 'storage', name: '高级存储', description: '提供高级存储功能', icon: '/plugins/storage/icon.png', status: 'active' },
      ];
      setPlugins(mockPlugins);
    } catch (error) {
      console.error('获取插件列表失败:', error);
      message.error('获取插件列表失败');
    }
  };

  // 获取项目用户
  const fetchProjectUsers = async (projectId) => {
    try {
      setLoading(true);
      const response = await apiService.getProjectUsers(projectId);
      setProjectUsers(response);
    } catch (error) {
      console.error('获取项目用户失败:', error);
      message.error('获取项目用户失败');
    } finally {
      setLoading(false);
    }
  };

  // 打开创建项目模态框
  const showCreateModal = () => {
    setModalTitle('创建项目');
    setEditingProject(null);
    form.resetFields();
    form.setFieldsValue({
      industry_type: currentTenant?.industry_type,
      max_users: 5,
      max_storage_gb: 1,
      plugins: []
    });
    setModalOpen(true);
  };

  // 打开编辑项目模态框
  const showEditModal = (project) => {
    setModalTitle('编辑项目');
    setEditingProject(project);

    form.setFieldsValue({
      name: project.name,
      description: project.description,
      industry_type: project.industry_type,
      status: project.status,
      max_users: project.max_users,
      max_storage_gb: project.max_storage_gb,
      plugins: project.plugins || [],
      settings: project.settings ? JSON.stringify(project.settings) : '{}'
    });

    setModalOpen(true);
  };

  // 关闭模态框
  const handleCancel = () => {
    setModalOpen(false);
  };

  // 提交表单
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();

      // 解析JSON设置
      try {
        values.settings = values.settings ? JSON.parse(values.settings) : {};
      } catch (e) {
        message.error('设置格式不正确，请输入有效的JSON');
        return;
      }

      setLoading(true);

      if (editingProject) {
        // 更新项目
        await apiService.updateProject(editingProject.id, values);
        message.success('项目更新成功');
      } else {
        // 创建项目
        values.tenant_id = currentTenant.id;
        await apiService.createProject(values);
        message.success('项目创建成功');
      }

      setModalOpen(false);
      fetchProjectData();

      // 刷新上下文中的项目列表
      if (fetchProjects) {
        fetchProjects(currentTenant.id);
      }
    } catch (error) {
      console.error('提交表单失败:', error);
      message.error('操作失败: ' + (error.response?.data?.detail || '未知错误'));
    } finally {
      setLoading(false);
    }
  };

  // 删除项目
  const handleDelete = async (id) => {
    try {
      setLoading(true);
      await apiService.deleteProject(id);
      message.success('项目删除成功');
      fetchProjectData();

      // 刷新上下文中的项目列表
      if (fetchProjects) {
        fetchProjects(currentTenant.id);
      }
    } catch (error) {
      console.error('删除项目失败:', error);
      message.error('删除失败: ' + (error.response?.data?.detail || '未知错误'));
    } finally {
      setLoading(false);
    }
  };

  // 打开用户管理模态框
  const showUserModal = (project) => {
    setSelectedProject(project);
    fetchProjectUsers(project.id);
    setUserModalOpen(true);
  };

  // 关闭用户管理模态框
  const handleUserModalCancel = () => {
    setUserModalOpen(false);
    setSelectedProject(null);
    setProjectUsers([]);
  };

  // 添加项目用户
  const handleAddUser = async () => {
    try {
      const values = await userForm.validateFields();
      setLoading(true);

      await apiService.addProjectUser(selectedProject.id, values);
      message.success('用户添加成功');

      // 刷新项目用户列表
      fetchProjectUsers(selectedProject.id);
      userForm.resetFields();
    } catch (error) {
      console.error('添加用户失败:', error);
      message.error('添加用户失败: ' + (error.response?.data?.detail || '未知错误'));
    } finally {
      setLoading(false);
    }
  };

  // 移除项目用户
  const handleRemoveUser = async (userId) => {
    try {
      setLoading(true);
      await apiService.removeProjectUser(selectedProject.id, userId);
      message.success('用户移除成功');

      // 刷新项目用户列表
      fetchProjectUsers(selectedProject.id);
    } catch (error) {
      console.error('移除用户失败:', error);
      message.error('移除用户失败: ' + (error.response?.data?.detail || '未知错误'));
    } finally {
      setLoading(false);
    }
  };

  // 获取状态标签
  const getStatusTag = (status) => {
    switch (status) {
      case 'active':
        return <Tag color="success">活跃</Tag>;
      case 'inactive':
        return <Tag color="error">停用</Tag>;
      case 'pending':
        return <Tag color="warning">待审核</Tag>;
      case 'archived':
        return <Tag color="default">已归档</Tag>;
      default:
        return <Tag>{status}</Tag>;
    }
  };

  // 获取行业名称
  const getIndustryName = (code) => {
    const industry = industries.find(i => i.code === code);
    return industry ? industry.name : code;
  };

  // 获取用户角色名称
  const getRoleName = (role) => {
    const roleNames = {
      'project_admin': '项目管理员',
      'project_member': '项目成员',
      'project_viewer': '项目查看者'
    };
    return roleNames[role] || role;
  };

  // 表格列定义
  const columns = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '行业',
      dataIndex: 'industry_type',
      key: 'industry_type',
      render: (text) => getIndustryName(text),
    },
    {
      title: '用户数',
      dataIndex: 'user_count',
      key: 'user_count',
      render: (text, record) => (
        <span>{text || 0} / {record.max_users || '不限'}</span>
      ),
    },
    {
      title: '存储空间',
      dataIndex: 'storage_usage',
      key: 'storage_usage',
      render: (text, record) => (
        <span>
          {text ? `${(text / 1024 / 1024 / 1024).toFixed(2)} GB` : '0 GB'} / {record.max_storage_gb ? `${record.max_storage_gb} GB` : '不限'}
        </span>
      ),
    },
    {
      title: '插件数',
      dataIndex: 'plugins',
      key: 'plugins',
      render: (plugins) => (plugins ? plugins.length : 0),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (text) => getStatusTag(text),
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (text) => new Date(text).toLocaleString(),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => navigate(`/tenant/projects/${record.id}`)}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => showEditModal(record)}
            />
          </Tooltip>
          <Tooltip title="用户管理">
            <Button
              type="text"
              icon={<TeamOutlined />}
              onClick={() => showUserModal(record)}
            />
          </Tooltip>
          <Tooltip title="设置">
            <Button
              type="text"
              icon={<SettingOutlined />}
              onClick={() => navigate(`/tenant/projects/${record.id}/settings`)}
            />
          </Tooltip>
          <Popconfirm
            title="确定要删除此项目吗？"
            description="删除后无法恢复，且会删除项目下所有数据。"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
            />
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 错误提示
  const errorAlert = error ? (
    <Alert
      message="错误"
      description={error}
      type="error"
      showIcon
      style={{ marginBottom: 16 }}
    />
  ) : null;

  return (
    <div className="project-management">
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
        <div>
          <Title level={2}><ProjectOutlined /> 项目管理</Title>
          <Paragraph>
            {currentTenant ? `${currentTenant.name} - ` : ''}
            管理租户下的项目，包括创建、编辑和删除项目。
          </Paragraph>
        </div>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={showCreateModal}
          disabled={!currentTenant}
        >
          创建项目
        </Button>
      </div>

      {errorAlert}

      {!currentTenant && (
        <Alert
          message="未选择租户"
          description="请先选择一个租户，然后再管理项目。"
          type="warning"
          showIcon
          style={{ marginBottom: 16 }}
        />
      )}

      {currentTenant && (
        <>
          {/* 统计卡片 */}
          <Row gutter={16} style={{ marginBottom: 24 }}>
            <Col span={6}>
              <Card>
                <Statistic
                  title="项目总数"
                  value={statistics.total}
                  prefix={<ProjectOutlined />}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title="活跃项目"
                  value={statistics.active}
                  valueStyle={{ color: '#3f8600' }}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title="非活跃项目"
                  value={statistics.inactive}
                  valueStyle={{ color: '#cf1322' }}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title="用户总数"
                  value={statistics.users}
                  prefix={<TeamOutlined />}
                />
              </Card>
            </Col>
          </Row>

          <Tabs activeKey={activeTab} onChange={setActiveTab}>
            <TabPane tab="项目列表" key="1">
              <Spin spinning={loading}>
                <Card>
                  <Table
                    columns={columns}
                    dataSource={projects}
                    rowKey="id"
                    pagination={{
                      pageSize: 10,
                      showSizeChanger: true,
                      showTotal: (total) => `共 ${total} 条记录`
                    }}
                  />
                </Card>
              </Spin>
            </TabPane>
            <TabPane tab="可用插件" key="2">
              <Spin spinning={loading}>
                <Card>
                  <List
                    grid={{ gutter: 16, column: 4 }}
                    dataSource={plugins}
                    renderItem={item => (
                      <List.Item>
                        <Card
                          hoverable
                          cover={
                            <div style={{
                              height: 120,
                              display: 'flex',
                              justifyContent: 'center',
                              alignItems: 'center',
                              padding: 16
                            }}>
                              {item.icon ? (
                                <img
                                  alt={item.name}
                                  src={item.icon}
                                  style={{ maxHeight: '100%', maxWidth: '100%' }}
                                />
                              ) : (
                                <AppstoreAddOutlined style={{ fontSize: 64, color: '#1890ff' }} />
                              )}
                            </div>
                          }
                          actions={[
                            <Tooltip title="查看详情">
                              <EyeOutlined key="view" onClick={() => navigate(`/marketplace/plugins/${item.id}`)} />
                            </Tooltip>,
                            <Tooltip title="安装">
                              <AppstoreAddOutlined key="install" />
                            </Tooltip>,
                          ]}
                        >
                          <Card.Meta
                            title={
                              <Space>
                                {item.name}
                                {item.status === 'active' && <Badge status="success" />}
                              </Space>
                            }
                            description={item.description}
                          />
                        </Card>
                      </List.Item>
                    )}
                  />
                </Card>
              </Spin>
            </TabPane>
          </Tabs>
        </>
      )}

      {/* 创建/编辑项目模态框 */}
      <Modal
        title={modalTitle}
        open={modalOpen}
        onOk={handleSubmit}
        onCancel={handleCancel}
        width={800}
        confirmLoading={loading}
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="项目名称"
                rules={[{ required: true, message: '请输入项目名称' }]}
              >
                <Input placeholder="请输入项目名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="industry_type"
                label="行业类型"
                rules={[{ required: true, message: '请选择行业类型' }]}
              >
                <Select placeholder="请选择行业类型">
                  {industries.map(industry => (
                    <Option key={industry.code} value={industry.code}>
                      {industry.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="description"
            label="描述"
          >
            <TextArea rows={4} placeholder="请输入项目描述" />
          </Form.Item>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="status"
                label="状态"
                initialValue="active"
                rules={[{ required: true, message: '请选择状态' }]}
              >
                <Select placeholder="请选择状态">
                  <Option value="active">活跃</Option>
                  <Option value="inactive">停用</Option>
                  <Option value="pending">待审核</Option>
                  <Option value="archived">已归档</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="max_users"
                label="最大用户数"
                tooltip="设置为0表示不限制"
                initialValue={5}
              >
                <InputNumber min={0} style={{ width: '100%' }} placeholder="请输入最大用户数" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="max_storage_gb"
                label="最大存储空间(GB)"
                tooltip="设置为0表示不限制"
                initialValue={1}
              >
                <InputNumber min={0} style={{ width: '100%' }} placeholder="请输入最大存储空间" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="plugins"
            label="启用插件"
          >
            <Select
              mode="multiple"
              placeholder="请选择插件"
              style={{ width: '100%' }}
              optionLabelProp="label"
            >
              {plugins.map(plugin => (
                <Option key={plugin.id} value={plugin.id} label={plugin.name}>
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    {plugin.icon ? (
                      <Avatar size="small" src={plugin.icon} style={{ marginRight: 8 }} />
                    ) : (
                      <AppstoreOutlined style={{ marginRight: 8 }} />
                    )}
                    {plugin.name}
                  </div>
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="settings"
            label="设置"
            tooltip="JSON格式的设置"
            initialValue="{}"
          >
            <TextArea rows={4} placeholder='{"theme": "light", "default_view": "dashboard"}' />
          </Form.Item>
        </Form>
      </Modal>

      {/* 项目用户管理模态框 */}
      <Modal
        title={`用户管理 - ${selectedProject?.name || ''}`}
        open={userModalOpen}
        onCancel={handleUserModalCancel}
        footer={null}
        width={800}
      >
        <Tabs defaultActiveKey="1">
          <TabPane tab="项目用户" key="1">
            <Spin spinning={loading}>
              <Table
                columns={[
                  {
                    title: '用户名',
                    dataIndex: 'username',
                    key: 'username',
                    render: (_, record) => record.user?.username || '',
                  },
                  {
                    title: '姓名',
                    dataIndex: 'full_name',
                    key: 'full_name',
                    render: (_, record) => record.user?.full_name || '',
                  },
                  {
                    title: '邮箱',
                    dataIndex: 'email',
                    key: 'email',
                    render: (_, record) => record.user?.email || '',
                  },
                  {
                    title: '角色',
                    dataIndex: 'role',
                    key: 'role',
                    render: (text) => getRoleName(text),
                  },
                  {
                    title: '操作',
                    key: 'action',
                    render: (_, record) => (
                      <Popconfirm
                        title="确定要移除此用户吗？"
                        onConfirm={() => handleRemoveUser(record.user_id)}
                        okText="确定"
                        cancelText="取消"
                      >
                        <Button
                          type="text"
                          danger
                          icon={<UserDeleteOutlined />}
                        >
                          移除
                        </Button>
                      </Popconfirm>
                    ),
                  },
                ]}
                dataSource={projectUsers}
                rowKey="id"
                pagination={false}
              />
            </Spin>
          </TabPane>
          <TabPane tab="添加用户" key="2">
            <Form
              form={userForm}
              layout="vertical"
              onFinish={handleAddUser}
            >
              <Form.Item
                name="user_id"
                label="用户"
                rules={[{ required: true, message: '请选择用户' }]}
              >
                <Select
                  placeholder="请选择用户"
                  showSearch
                  optionFilterProp="children"
                  filterOption={(input, option) =>
                    option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                  }
                >
                  {users.map(user => (
                    <Option key={user.id} value={user.id}>
                      {user.username} ({user.full_name || user.email})
                    </Option>
                  ))}
                </Select>
              </Form.Item>
              <Form.Item
                name="role"
                label="角色"
                initialValue="project_member"
                rules={[{ required: true, message: '请选择角色' }]}
              >
                <Select placeholder="请选择角色">
                  <Option value="project_admin">项目管理员</Option>
                  <Option value="project_member">项目成员</Option>
                  <Option value="project_viewer">项目查看者</Option>
                </Select>
              </Form.Item>
              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  icon={<UserAddOutlined />}
                  loading={loading}
                >
                  添加用户
                </Button>
              </Form.Item>
            </Form>
          </TabPane>
        </Tabs>
      </Modal>
    </div>
  );
};

export default ProjectManagement;
