#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
客户分析模块

提供客户行为分析、满意度统计、客服质量评估等功能。
"""

import logging
import uuid
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, or_, desc

from ..models.customer_service import (
    CustomerServiceSession,
    CustomerServiceMessage,
    CustomerServiceAnalytics,
    UserProfile,
    ConversationLog
)

logger = logging.getLogger(__name__)

class CustomerAnalytics:
    """客户分析服务"""
    
    def __init__(self):
        self.analysis_cache = {}
        self.cache_ttl = 300  # 5分钟缓存
        
    async def analyze_customer_behavior(
        self,
        db: AsyncSession,
        project_id: uuid.UUID,
        platform_user_id: Optional[str] = None,
        platform_type: Optional[str] = None,
        date_range: Optional[Tuple[datetime, datetime]] = None
    ) -> Dict[str, Any]:
        """
        分析客户行为
        
        Args:
            db: 数据库会话
            project_id: 项目ID
            platform_user_id: 平台用户ID
            platform_type: 平台类型
            date_range: 日期范围
            
        Returns:
            Dict[str, Any]: 客户行为分析结果
        """
        try:
            # 构建查询条件
            conditions = [CustomerServiceSession.project_id == project_id]
            
            if platform_user_id:
                conditions.append(CustomerServiceSession.platform_user_id == platform_user_id)
                
            if platform_type:
                conditions.append(CustomerServiceSession.platform_type == platform_type)
                
            if date_range:
                start_date, end_date = date_range
                conditions.append(CustomerServiceSession.started_at >= start_date)
                conditions.append(CustomerServiceSession.started_at <= end_date)
            
            # 查询会话数据
            session_query = select(CustomerServiceSession).where(and_(*conditions))
            session_result = await db.execute(session_query)
            sessions = session_result.scalars().all()
            
            if not sessions:
                return self._empty_behavior_analysis()
            
            # 分析会话模式
            session_analysis = await self._analyze_session_patterns(sessions)
            
            # 分析消息模式
            message_analysis = await self._analyze_message_patterns(db, sessions)
            
            # 分析时间模式
            time_analysis = await self._analyze_time_patterns(sessions)
            
            # 分析满意度
            satisfaction_analysis = await self._analyze_satisfaction(sessions)
            
            # 生成客户画像
            customer_profile = await self._generate_customer_profile(
                db, sessions, platform_user_id, platform_type
            )
            
            return {
                "summary": {
                    "total_sessions": len(sessions),
                    "total_messages": sum(s.message_count for s in sessions),
                    "avg_session_duration": session_analysis["avg_duration"],
                    "avg_satisfaction": satisfaction_analysis["avg_score"]
                },
                "session_patterns": session_analysis,
                "message_patterns": message_analysis,
                "time_patterns": time_analysis,
                "satisfaction": satisfaction_analysis,
                "customer_profile": customer_profile,
                "insights": await self._generate_insights(sessions, message_analysis)
            }
            
        except Exception as e:
            logger.error(f"客户行为分析失败: {e}")
            return self._empty_behavior_analysis()
            
    async def _analyze_session_patterns(self, sessions: List) -> Dict[str, Any]:
        """分析会话模式"""
        try:
            if not sessions:
                return {"avg_duration": 0, "status_distribution": {}}
            
            # 计算平均会话时长
            durations = []
            status_counts = {}
            
            for session in sessions:
                # 计算会话时长
                if session.ended_at:
                    duration = (session.ended_at - session.started_at).total_seconds()
                    durations.append(duration)
                else:
                    # 使用最后活动时间
                    duration = (session.last_activity_at - session.started_at).total_seconds()
                    durations.append(duration)
                
                # 统计状态分布
                status = session.status
                status_counts[status] = status_counts.get(status, 0) + 1
            
            avg_duration = sum(durations) / len(durations) if durations else 0
            
            return {
                "avg_duration": avg_duration,
                "min_duration": min(durations) if durations else 0,
                "max_duration": max(durations) if durations else 0,
                "status_distribution": status_counts,
                "total_sessions": len(sessions)
            }
            
        except Exception as e:
            logger.error(f"分析会话模式失败: {e}")
            return {"avg_duration": 0, "status_distribution": {}}
            
    async def _analyze_message_patterns(
        self, 
        db: AsyncSession, 
        sessions: List
    ) -> Dict[str, Any]:
        """分析消息模式"""
        try:
            if not sessions:
                return {"avg_messages_per_session": 0, "message_types": {}}
            
            session_ids = [session.id for session in sessions]
            
            # 查询消息数据
            message_query = select(CustomerServiceMessage).where(
                CustomerServiceMessage.session_id.in_(session_ids)
            )
            
            message_result = await db.execute(message_query)
            messages = message_result.scalars().all()
            
            if not messages:
                return {"avg_messages_per_session": 0, "message_types": {}}
            
            # 分析消息类型分布
            type_counts = {}
            user_messages = 0
            ai_messages = 0
            
            for message in messages:
                msg_type = message.message_type
                type_counts[msg_type] = type_counts.get(msg_type, 0) + 1
                
                if message.is_from_user:
                    user_messages += 1
                else:
                    ai_messages += 1
            
            avg_messages_per_session = len(messages) / len(sessions)
            
            return {
                "total_messages": len(messages),
                "avg_messages_per_session": avg_messages_per_session,
                "user_messages": user_messages,
                "ai_messages": ai_messages,
                "message_types": type_counts,
                "user_ai_ratio": user_messages / max(ai_messages, 1)
            }
            
        except Exception as e:
            logger.error(f"分析消息模式失败: {e}")
            return {"avg_messages_per_session": 0, "message_types": {}}
            
    async def _analyze_time_patterns(self, sessions: List) -> Dict[str, Any]:
        """分析时间模式"""
        try:
            if not sessions:
                return {"peak_hours": [], "day_distribution": {}}
            
            hour_counts = {}
            day_counts = {}
            
            for session in sessions:
                # 分析小时分布
                hour = session.started_at.hour
                hour_counts[hour] = hour_counts.get(hour, 0) + 1
                
                # 分析星期分布
                day = session.started_at.strftime('%A')
                day_counts[day] = day_counts.get(day, 0) + 1
            
            # 找出高峰时段
            peak_hours = sorted(hour_counts.items(), key=lambda x: x[1], reverse=True)[:3]
            
            return {
                "hour_distribution": hour_counts,
                "day_distribution": day_counts,
                "peak_hours": [{"hour": h, "count": c} for h, c in peak_hours],
                "most_active_day": max(day_counts.items(), key=lambda x: x[1])[0] if day_counts else None
            }
            
        except Exception as e:
            logger.error(f"分析时间模式失败: {e}")
            return {"peak_hours": [], "day_distribution": {}}
            
    async def _analyze_satisfaction(self, sessions: List) -> Dict[str, Any]:
        """分析满意度"""
        try:
            if not sessions:
                return {"avg_score": 0, "score_distribution": {}}
            
            scores = []
            score_counts = {}
            
            for session in sessions:
                if session.satisfaction_score is not None:
                    score = session.satisfaction_score
                    scores.append(score)
                    
                    # 分组统计
                    if score >= 4.5:
                        category = "excellent"
                    elif score >= 3.5:
                        category = "good"
                    elif score >= 2.5:
                        category = "average"
                    else:
                        category = "poor"
                        
                    score_counts[category] = score_counts.get(category, 0) + 1
            
            avg_score = sum(scores) / len(scores) if scores else 0
            
            return {
                "avg_score": avg_score,
                "total_rated": len(scores),
                "total_sessions": len(sessions),
                "rating_rate": len(scores) / len(sessions) if sessions else 0,
                "score_distribution": score_counts,
                "trend": "stable"  # 可以添加趋势分析
            }
            
        except Exception as e:
            logger.error(f"分析满意度失败: {e}")
            return {"avg_score": 0, "score_distribution": {}}
            
    async def _generate_customer_profile(
        self,
        db: AsyncSession,
        sessions: List,
        platform_user_id: Optional[str],
        platform_type: Optional[str]
    ) -> Dict[str, Any]:
        """生成客户画像"""
        try:
            if not sessions or not platform_user_id:
                return {}
            
            # 基础信息
            first_session = min(sessions, key=lambda s: s.started_at)
            last_session = max(sessions, key=lambda s: s.started_at)
            
            # 行为特征
            total_messages = sum(s.message_count for s in sessions)
            avg_messages = total_messages / len(sessions)
            
            # 活跃度分析
            session_dates = [s.started_at.date() for s in sessions]
            unique_days = len(set(session_dates))
            
            # 问题类型分析（基于会话标题或元数据）
            common_topics = []
            for session in sessions:
                if session.title:
                    common_topics.append(session.title)
            
            # 客户类型判断
            customer_type = "new"
            if len(sessions) > 10:
                customer_type = "loyal"
            elif len(sessions) > 3:
                customer_type = "regular"
            
            return {
                "platform_user_id": platform_user_id,
                "platform_type": platform_type,
                "customer_type": customer_type,
                "first_interaction": first_session.started_at.isoformat(),
                "last_interaction": last_session.started_at.isoformat(),
                "total_sessions": len(sessions),
                "total_messages": total_messages,
                "avg_messages_per_session": avg_messages,
                "active_days": unique_days,
                "engagement_level": self._calculate_engagement_level(sessions),
                "common_topics": list(set(common_topics))[:5],
                "preferred_contact_times": await self._get_preferred_times(sessions)
            }
            
        except Exception as e:
            logger.error(f"生成客户画像失败: {e}")
            return {}
            
    def _calculate_engagement_level(self, sessions: List) -> str:
        """计算参与度等级"""
        try:
            if not sessions:
                return "low"
            
            # 基于会话数量和频率计算
            session_count = len(sessions)
            
            if session_count >= 20:
                return "high"
            elif session_count >= 5:
                return "medium"
            else:
                return "low"
                
        except Exception as e:
            logger.error(f"计算参与度失败: {e}")
            return "low"
            
    async def _get_preferred_times(self, sessions: List) -> List[Dict[str, Any]]:
        """获取偏好联系时间"""
        try:
            hour_counts = {}
            
            for session in sessions:
                hour = session.started_at.hour
                hour_counts[hour] = hour_counts.get(hour, 0) + 1
            
            # 返回前3个高频时段
            top_hours = sorted(hour_counts.items(), key=lambda x: x[1], reverse=True)[:3]
            
            return [
                {
                    "hour": hour,
                    "count": count,
                    "percentage": count / len(sessions) * 100
                }
                for hour, count in top_hours
            ]
            
        except Exception as e:
            logger.error(f"获取偏好时间失败: {e}")
            return []
            
    async def _generate_insights(
        self, 
        sessions: List, 
        message_analysis: Dict[str, Any]
    ) -> List[str]:
        """生成分析洞察"""
        try:
            insights = []
            
            # 会话数量洞察
            session_count = len(sessions)
            if session_count > 10:
                insights.append(f"该客户是活跃用户，共有{session_count}次对话记录")
            elif session_count > 3:
                insights.append(f"该客户是常规用户，有{session_count}次对话记录")
            else:
                insights.append(f"该客户是新用户，仅有{session_count}次对话记录")
            
            # 消息模式洞察
            if message_analysis.get("user_ai_ratio", 0) > 2:
                insights.append("客户倾向于发送较多消息，可能需要更详细的回复")
            
            # 满意度洞察
            avg_satisfaction = sum(s.satisfaction_score for s in sessions if s.satisfaction_score) / len([s for s in sessions if s.satisfaction_score]) if any(s.satisfaction_score for s in sessions) else 0
            
            if avg_satisfaction > 4:
                insights.append("客户满意度较高，服务质量良好")
            elif avg_satisfaction < 3:
                insights.append("客户满意度偏低，需要改进服务质量")
            
            return insights
            
        except Exception as e:
            logger.error(f"生成洞察失败: {e}")
            return []
            
    def _empty_behavior_analysis(self) -> Dict[str, Any]:
        """返回空的行为分析结果"""
        return {
            "summary": {
                "total_sessions": 0,
                "total_messages": 0,
                "avg_session_duration": 0,
                "avg_satisfaction": 0
            },
            "session_patterns": {"avg_duration": 0, "status_distribution": {}},
            "message_patterns": {"avg_messages_per_session": 0, "message_types": {}},
            "time_patterns": {"peak_hours": [], "day_distribution": {}},
            "satisfaction": {"avg_score": 0, "score_distribution": {}},
            "customer_profile": {},
            "insights": []
        }
        
    async def generate_analytics_report(
        self,
        db: AsyncSession,
        project_id: uuid.UUID,
        date_range: Tuple[datetime, datetime],
        platform_type: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        生成分析报告
        
        Args:
            db: 数据库会话
            project_id: 项目ID
            date_range: 日期范围
            platform_type: 平台类型
            
        Returns:
            Dict[str, Any]: 分析报告
        """
        try:
            start_date, end_date = date_range
            
            # 构建查询条件
            conditions = [
                CustomerServiceSession.project_id == project_id,
                CustomerServiceSession.started_at >= start_date,
                CustomerServiceSession.started_at <= end_date
            ]
            
            if platform_type:
                conditions.append(CustomerServiceSession.platform_type == platform_type)
            
            # 查询会话统计
            session_stats = await self._get_session_statistics(db, conditions)
            
            # 查询消息统计
            message_stats = await self._get_message_statistics(db, conditions)
            
            # 查询满意度统计
            satisfaction_stats = await self._get_satisfaction_statistics(db, conditions)
            
            # 查询平台分布
            platform_stats = await self._get_platform_statistics(db, conditions)
            
            # 生成趋势数据
            trend_data = await self._get_trend_data(db, project_id, start_date, end_date)
            
            return {
                "period": {
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat(),
                    "days": (end_date - start_date).days + 1
                },
                "session_statistics": session_stats,
                "message_statistics": message_stats,
                "satisfaction_statistics": satisfaction_stats,
                "platform_statistics": platform_stats,
                "trend_data": trend_data,
                "generated_at": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"生成分析报告失败: {e}")
            return {}
            
    async def _get_session_statistics(
        self, 
        db: AsyncSession, 
        conditions: List
    ) -> Dict[str, Any]:
        """获取会话统计"""
        try:
            # 总会话数
            total_query = select(func.count(CustomerServiceSession.id)).where(and_(*conditions))
            total_result = await db.execute(total_query)
            total_sessions = total_result.scalar()
            
            # 状态分布
            status_query = select(
                CustomerServiceSession.status,
                func.count(CustomerServiceSession.id)
            ).where(and_(*conditions)).group_by(CustomerServiceSession.status)
            
            status_result = await db.execute(status_query)
            status_distribution = dict(status_result.all())
            
            return {
                "total_sessions": total_sessions,
                "status_distribution": status_distribution
            }
            
        except Exception as e:
            logger.error(f"获取会话统计失败: {e}")
            return {"total_sessions": 0, "status_distribution": {}}
            
    async def _get_message_statistics(
        self, 
        db: AsyncSession, 
        conditions: List
    ) -> Dict[str, Any]:
        """获取消息统计"""
        try:
            # 通过会话ID查询消息
            session_query = select(CustomerServiceSession.id).where(and_(*conditions))
            session_result = await db.execute(session_query)
            session_ids = [row[0] for row in session_result.all()]
            
            if not session_ids:
                return {"total_messages": 0, "user_messages": 0, "ai_messages": 0}
            
            # 总消息数
            total_msg_query = select(func.count(CustomerServiceMessage.id)).where(
                CustomerServiceMessage.session_id.in_(session_ids)
            )
            total_msg_result = await db.execute(total_msg_query)
            total_messages = total_msg_result.scalar()
            
            # 用户消息数
            user_msg_query = select(func.count(CustomerServiceMessage.id)).where(
                and_(
                    CustomerServiceMessage.session_id.in_(session_ids),
                    CustomerServiceMessage.is_from_user.is_(True)
                )
            )
            user_msg_result = await db.execute(user_msg_query)
            user_messages = user_msg_result.scalar()
            
            ai_messages = total_messages - user_messages
            
            return {
                "total_messages": total_messages,
                "user_messages": user_messages,
                "ai_messages": ai_messages,
                "avg_messages_per_session": total_messages / len(session_ids) if session_ids else 0
            }
            
        except Exception as e:
            logger.error(f"获取消息统计失败: {e}")
            return {"total_messages": 0, "user_messages": 0, "ai_messages": 0}
            
    async def _get_satisfaction_statistics(
        self, 
        db: AsyncSession, 
        conditions: List
    ) -> Dict[str, Any]:
        """获取满意度统计"""
        try:
            # 平均满意度
            avg_query = select(func.avg(CustomerServiceSession.satisfaction_score)).where(
                and_(*conditions, CustomerServiceSession.satisfaction_score.isnot(None))
            )
            avg_result = await db.execute(avg_query)
            avg_satisfaction = avg_result.scalar() or 0
            
            # 评分数量
            count_query = select(func.count(CustomerServiceSession.satisfaction_score)).where(
                and_(*conditions, CustomerServiceSession.satisfaction_score.isnot(None))
            )
            count_result = await db.execute(count_query)
            rated_count = count_result.scalar()
            
            return {
                "avg_satisfaction": float(avg_satisfaction),
                "rated_sessions": rated_count,
                "satisfaction_rate": rated_count  # 需要除以总会话数
            }
            
        except Exception as e:
            logger.error(f"获取满意度统计失败: {e}")
            return {"avg_satisfaction": 0, "rated_sessions": 0}
            
    async def _get_platform_statistics(
        self, 
        db: AsyncSession, 
        conditions: List
    ) -> Dict[str, Any]:
        """获取平台统计"""
        try:
            platform_query = select(
                CustomerServiceSession.platform_type,
                func.count(CustomerServiceSession.id)
            ).where(and_(*conditions)).group_by(CustomerServiceSession.platform_type)
            
            platform_result = await db.execute(platform_query)
            platform_distribution = dict(platform_result.all())
            
            return {
                "platform_distribution": platform_distribution
            }
            
        except Exception as e:
            logger.error(f"获取平台统计失败: {e}")
            return {"platform_distribution": {}}
            
    async def _get_trend_data(
        self,
        db: AsyncSession,
        project_id: uuid.UUID,
        start_date: datetime,
        end_date: datetime
    ) -> List[Dict[str, Any]]:
        """获取趋势数据"""
        try:
            # 按天统计会话数量
            daily_query = select(
                func.date(CustomerServiceSession.started_at).label('date'),
                func.count(CustomerServiceSession.id).label('sessions')
            ).where(
                and_(
                    CustomerServiceSession.project_id == project_id,
                    CustomerServiceSession.started_at >= start_date,
                    CustomerServiceSession.started_at <= end_date
                )
            ).group_by(func.date(CustomerServiceSession.started_at)).order_by('date')
            
            daily_result = await db.execute(daily_query)
            daily_data = daily_result.all()
            
            return [
                {
                    "date": row.date.isoformat(),
                    "sessions": row.sessions
                }
                for row in daily_data
            ]
            
        except Exception as e:
            logger.error(f"获取趋势数据失败: {e}")
            return []
