import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Modal,
  Form,
  Input,
  Select,
  InputNumber,
  Switch,
  message,
  Popconfirm,
  Tooltip,
  Badge,
  Typography,
  Row,
  Col,
  Divider,
  Progress,
  Upload,
  Checkbox,
  Image,
  Tabs,
  Descriptions,
  Steps,
  Timeline,
  Rate,
  Drawer,
  Alert,
  Spin,
  Empty,
  Dropdown,
  Menu,
  Statistic
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SyncOutlined,
  UploadOutlined,
  DownloadOutlined,
  EyeOutlined,
  SettingOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ExclamationCircleOutlined,
  ReloadOutlined,
  PictureOutlined,
  ShoppingOutlined,
  ClockCircleOutlined,
  BulbOutlined,
  LinkOutlined,
  UnlinkOutlined,
  ThunderboltOutlined,
  StarOutlined,
  DollarOutlined
} from '@ant-design/icons';
import { useParams } from 'react-router-dom';
import multiChannelService from '../services/multiChannelService';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;
const { TabPane } = Tabs;
const { Step } = Steps;

const ProductManagementNew = () => {
  const { projectId } = useParams();
  const [loading, setLoading] = useState(false);
  const [products, setProducts] = useState([]);
  const [channels, setChannels] = useState([]);
  const [mappings, setMappings] = useState([]);
  const [categories, setCategories] = useState([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [selectedProduct, setSelectedProduct] = useState(null);
  
  // 模态框状态
  const [productDetailVisible, setProductDetailVisible] = useState(false);
  const [channelSyncVisible, setChannelSyncVisible] = useState(false);
  const [imageGenerationVisible, setImageGenerationVisible] = useState(false);
  const [batchOperationVisible, setBatchOperationVisible] = useState(false);
  const [aiOptimizeVisible, setAiOptimizeVisible] = useState(false);
  
  // 表单
  const [syncForm] = Form.useForm();
  const [imageForm] = Form.useForm();
  const [batchForm] = Form.useForm();
  const [aiForm] = Form.useForm();

  useEffect(() => {
    loadData();
  }, [projectId]);

  const loadData = async () => {
    try {
      setLoading(true);
      await Promise.all([
        loadProducts(),
        loadChannels(),
        loadMappings(),
        loadCategories()
      ]);
    } catch (error) {
      console.error('加载数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadProducts = async () => {
    try {
      const response = await multiChannelService.getProducts(projectId);
      setProducts(response.data || []);
    } catch (error) {
      console.error('获取商品列表失败:', error);
    }
  };

  const loadChannels = async () => {
    try {
      const response = await multiChannelService.getChannels(projectId);
      setChannels(response.data || []);
    } catch (error) {
      console.error('获取渠道列表失败:', error);
    }
  };

  const loadMappings = async () => {
    try {
      const response = await multiChannelService.getProductMappings(projectId);
      setMappings(response.data || []);
    } catch (error) {
      console.error('获取商品映射失败:', error);
    }
  };

  const loadCategories = async () => {
    try {
      const response = await multiChannelService.getCategories(projectId);
      setCategories(response.data || []);
    } catch (error) {
      console.error('获取类目列表失败:', error);
    }
  };

  // 查看商品详情
  const handleViewProduct = (product) => {
    setSelectedProduct(product);
    setProductDetailVisible(true);
  };

  // 一键生成AI图片
  const handleGenerateImage = (product) => {
    setSelectedProduct(product);
    setImageGenerationVisible(true);
    imageForm.setFieldsValue({
      product_id: product.id,
      prompt: `高质量${product.name}商品图片，专业摄影，白色背景`
    });
  };

  // 多渠道同步
  const handleMultiChannelSync = (product) => {
    setSelectedProduct(product);
    setChannelSyncVisible(true);
    syncForm.setFieldsValue({
      product_id: product.id
    });
  };

  // AI优化商品信息
  const handleAIOptimize = (product) => {
    setSelectedProduct(product);
    setAiOptimizeVisible(true);
    aiForm.setFieldsValue({
      product_id: product.id,
      current_title: product.name,
      current_description: product.description
    });
  };

  // 执行AI图片生成
  const handleImageGeneration = async (values) => {
    try {
      setLoading(true);
      const response = await multiChannelService.generateProductImage(projectId, values);
      if (response.success) {
        message.success('AI图片生成成功');
        setImageGenerationVisible(false);
        // 可以在这里显示生成的图片选择界面
      } else {
        message.error(response.error || 'AI图片生成失败');
      }
    } catch (error) {
      console.error('AI图片生成失败:', error);
      message.error('AI图片生成失败');
    } finally {
      setLoading(false);
    }
  };

  // 执行多渠道同步
  const handleChannelSync = async (values) => {
    try {
      setLoading(true);
      const response = await multiChannelService.multiChannelSync(projectId, values);
      if (response.success) {
        message.success(`已同步到${response.success_channels}/${response.total_channels}个渠道`);
        setChannelSyncVisible(false);
        loadMappings();
      } else {
        message.error('多渠道同步失败');
      }
    } catch (error) {
      console.error('多渠道同步失败:', error);
      message.error('多渠道同步失败');
    } finally {
      setLoading(false);
    }
  };

  // 执行AI优化
  const handleAIOptimization = async (values) => {
    try {
      setLoading(true);
      const response = await multiChannelService.optimizeProductWithAI(projectId, values);
      if (response.success) {
        message.success('AI优化完成');
        setAiOptimizeVisible(false);
        loadProducts();
      } else {
        message.error('AI优化失败');
      }
    } catch (error) {
      console.error('AI优化失败:', error);
      message.error('AI优化失败');
    } finally {
      setLoading(false);
    }
  };

  // 批量操作
  const handleBatchOperation = async (values) => {
    try {
      setLoading(true);
      const response = await multiChannelService.batchOperation(projectId, {
        product_ids: selectedRowKeys,
        operation_type: values.operation_type,
        operation_params: values
      });
      if (response.success) {
        message.success('批量操作已启动');
        setBatchOperationVisible(false);
        setSelectedRowKeys([]);
        loadProducts();
      } else {
        message.error('批量操作失败');
      }
    } catch (error) {
      console.error('批量操作失败:', error);
      message.error('批量操作失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取商品在各渠道的同步状态
  const getProductChannelStatus = (productId) => {
    const productMappings = mappings.filter(m => m.product_id === productId);
    const statusCount = {
      success: productMappings.filter(m => m.sync_status === 'success').length,
      failed: productMappings.filter(m => m.sync_status === 'failed').length,
      pending: productMappings.filter(m => m.sync_status === 'pending').length,
      syncing: productMappings.filter(m => m.sync_status === 'syncing').length
    };
    return { mappings: productMappings, statusCount };
  };

  // 渲染渠道同步状态
  const renderChannelStatus = (productId) => {
    const { statusCount } = getProductChannelStatus(productId);
    const total = Object.values(statusCount).reduce((a, b) => a + b, 0);
    
    if (total === 0) {
      return <Tag color="default">未同步</Tag>;
    }

    return (
      <Space size={4}>
        {statusCount.success > 0 && (
          <Tag color="success" size="small">{statusCount.success}成功</Tag>
        )}
        {statusCount.failed > 0 && (
          <Tag color="error" size="small">{statusCount.failed}失败</Tag>
        )}
        {statusCount.syncing > 0 && (
          <Tag color="processing" size="small">{statusCount.syncing}同步中</Tag>
        )}
        {statusCount.pending > 0 && (
          <Tag color="default" size="small">{statusCount.pending}待同步</Tag>
        )}
      </Space>
    );
  };

  // 渲染商品操作按钮
  const renderProductActions = (product) => {
    const { statusCount } = getProductChannelStatus(product.id);
    const hasChannels = Object.values(statusCount).reduce((a, b) => a + b, 0) > 0;

    return (
      <Space size="small">
        <Tooltip title="查看详情">
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => handleViewProduct(product)}
          />
        </Tooltip>
        
        <Tooltip title="AI生成图片">
          <Button
            type="text"
            icon={<PictureOutlined />}
            onClick={() => handleGenerateImage(product)}
          />
        </Tooltip>
        
        <Tooltip title="多渠道同步">
          <Button
            type="text"
            icon={<SyncOutlined />}
            onClick={() => handleMultiChannelSync(product)}
          />
        </Tooltip>
        
        <Tooltip title="AI优化">
          <Button
            type="text"
            icon={<BulbOutlined />}
            onClick={() => handleAIOptimize(product)}
          />
        </Tooltip>
        
        <Dropdown
          overlay={
            <Menu>
              <Menu.Item key="edit" icon={<EditOutlined />}>
                编辑商品
              </Menu.Item>
              <Menu.Item key="copy" icon={<PlusOutlined />}>
                复制商品
              </Menu.Item>
              <Menu.Divider />
              <Menu.Item key="delete" icon={<DeleteOutlined />} danger>
                删除商品
              </Menu.Item>
            </Menu>
          }
        >
          <Button type="text" icon={<SettingOutlined />} />
        </Dropdown>
      </Space>
    );
  };

  const columns = [
    {
      title: '商品信息',
      dataIndex: 'name',
      key: 'name',
      width: 300,
      render: (name, record) => (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Image
            width={60}
            height={60}
            src={record.main_image || '/placeholder.png'}
            style={{ marginRight: 12, borderRadius: 4 }}
            fallback="/placeholder.png"
          />
          <div>
            <div>
              <Text strong>{name}</Text>
              {record.is_featured && (
                <StarOutlined style={{ color: '#faad14', marginLeft: 4 }} />
              )}
            </div>
            <div style={{ marginTop: 4 }}>
              <Text type="secondary" style={{ fontSize: 12 }}>
                SKU: {record.sku}
              </Text>
            </div>
            <div style={{ marginTop: 2 }}>
              <Tag size="small">{record.category_name}</Tag>
            </div>
          </div>
        </div>
      )
    },
    {
      title: '价格库存',
      dataIndex: 'base_price',
      key: 'price_stock',
      width: 120,
      render: (basePrice, record) => (
        <div>
          <div>
            <DollarOutlined style={{ marginRight: 4 }} />
            <Text strong>¥{basePrice}</Text>
          </div>
          <div style={{ marginTop: 4 }}>
            <Text type="secondary" style={{ fontSize: 12 }}>
              库存: {record.stock_quantity}
            </Text>
          </div>
        </div>
      )
    },
    {
      title: '渠道状态',
      dataIndex: 'id',
      key: 'channel_status',
      width: 200,
      render: (productId) => renderChannelStatus(productId)
    },
    {
      title: '最后更新',
      dataIndex: 'updated_at',
      key: 'updated_at',
      width: 120,
      render: (time) => (
        <Text type="secondary" style={{ fontSize: 12 }}>
          {new Date(time).toLocaleDateString()}
        </Text>
      )
    },
    {
      title: '操作',
      key: 'actions',
      width: 200,
      render: (_, record) => renderProductActions(record)
    }
  ];

  const rowSelection = {
    selectedRowKeys,
    onChange: setSelectedRowKeys,
    getCheckboxProps: (record) => ({
      disabled: record.status === 'disabled'
    })
  };

  return (
    <div>
      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="商品总数"
              value={products.length}
              prefix={<ShoppingOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="已同步商品"
              value={mappings.filter(m => m.sync_status === 'success').length}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="同步失败"
              value={mappings.filter(m => m.sync_status === 'failed').length}
              prefix={<ExclamationCircleOutlined />}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="活跃渠道"
              value={channels.filter(c => c.is_active).length}
              prefix={<LinkOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 主表格 */}
      <Card
        title={
          <Space>
            <ShoppingOutlined />
            <span>商品管理</span>
            <Badge count={products.length} />
          </Space>
        }
        extra={
          <Space>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {/* 新建商品 */}}
            >
              新建商品
            </Button>
            <Button
              icon={<ReloadOutlined />}
              onClick={loadData}
            >
              刷新
            </Button>
          </Space>
        }
      >
        {/* 批量操作工具栏 */}
        {selectedRowKeys.length > 0 && (
          <Alert
            message={
              <Space>
                <span>已选择 {selectedRowKeys.length} 个商品</span>
                <Button
                  size="small"
                  onClick={() => setBatchOperationVisible(true)}
                >
                  批量操作
                </Button>
                <Button
                  size="small"
                  onClick={() => setSelectedRowKeys([])}
                >
                  取消选择
                </Button>
              </Space>
            }
            type="info"
            style={{ marginBottom: 16 }}
          />
        )}

        <Table
          columns={columns}
          dataSource={products}
          rowKey="id"
          loading={loading}
          rowSelection={rowSelection}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => 
              `第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`
          }}
        />
      </Card>

      {/* 商品详情抽屉 */}
      <Drawer
        title="商品详情"
        width={800}
        open={productDetailVisible}
        onClose={() => setProductDetailVisible(false)}
      >
        {selectedProduct && (
          <Tabs defaultActiveKey="basic">
            <TabPane tab="基本信息" key="basic">
              <Descriptions bordered>
                <Descriptions.Item label="商品名称" span={3}>
                  {selectedProduct.name}
                </Descriptions.Item>
                <Descriptions.Item label="SKU" span={2}>
                  {selectedProduct.sku}
                </Descriptions.Item>
                <Descriptions.Item label="类目">
                  {selectedProduct.category_name}
                </Descriptions.Item>
                <Descriptions.Item label="基础价格" span={2}>
                  ¥{selectedProduct.base_price}
                </Descriptions.Item>
                <Descriptions.Item label="库存数量">
                  {selectedProduct.stock_quantity}
                </Descriptions.Item>
                <Descriptions.Item label="商品描述" span={3}>
                  {selectedProduct.description}
                </Descriptions.Item>
              </Descriptions>
            </TabPane>

            <TabPane tab="渠道同步" key="channels">
              <div>
                {getProductChannelStatus(selectedProduct.id).mappings.map(mapping => (
                  <Card key={mapping.id} size="small" style={{ marginBottom: 8 }}>
                    <Row>
                      <Col span={8}>
                        <Text strong>{channels.find(c => c.id === mapping.channel_id)?.custom_name}</Text>
                      </Col>
                      <Col span={8}>
                        <Tag color={mapping.sync_status === 'success' ? 'success' : 'error'}>
                          {mapping.sync_status}
                        </Tag>
                      </Col>
                      <Col span={8}>
                        <Text type="secondary">
                          {mapping.last_sync_at ? new Date(mapping.last_sync_at).toLocaleString() : '未同步'}
                        </Text>
                      </Col>
                    </Row>
                  </Card>
                ))}
              </div>
            </TabPane>

            <TabPane tab="AI优化记录" key="ai">
              <Timeline>
                <Timeline.Item color="green">
                  <Text>AI图片生成完成</Text>
                  <div><Text type="secondary">2024-01-15 14:30</Text></div>
                </Timeline.Item>
                <Timeline.Item color="blue">
                  <Text>商品标题优化</Text>
                  <div><Text type="secondary">2024-01-14 10:20</Text></div>
                </Timeline.Item>
              </Timeline>
            </TabPane>
          </Tabs>
        )}
      </Drawer>

      {/* AI图片生成模态框 */}
      <Modal
        title="AI图片生成"
        open={imageGenerationVisible}
        onCancel={() => setImageGenerationVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={imageForm}
          layout="vertical"
          onFinish={handleImageGeneration}
        >
          <Alert
            message="AI图片生成"
            description="为商品生成专业的商品图片，可以选择不同的风格和平台定制。"
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />

          <Form.Item name="product_id" hidden>
            <Input />
          </Form.Item>

          <Form.Item
            name="prompt"
            label="生成提示词"
            rules={[{ required: true, message: '请输入生成提示词' }]}
          >
            <Input.TextArea
              rows={3}
              placeholder="描述您想要生成的图片，例如：高质量美食摄影，白色背景，专业灯光"
            />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="style"
                label="图片风格"
                initialValue="natural"
              >
                <Select>
                  <Option value="natural">自然风格</Option>
                  <Option value="vivid">鲜艳风格</Option>
                  <Option value="photorealistic">写实风格</Option>
                  <Option value="artistic">艺术风格</Option>
                  <Option value="minimalist">简约风格</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="channel_id"
                label="目标渠道（可选）"
              >
                <Select placeholder="选择特定渠道">
                  {channels.map(channel => (
                    <Option key={channel.id} value={channel.id}>
                      {channel.custom_name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <div style={{ textAlign: 'right', marginTop: 24 }}>
            <Space>
              <Button onClick={() => setImageGenerationVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit" loading={loading}>
                生成图片
              </Button>
            </Space>
          </div>
        </Form>
      </Modal>

      {/* 多渠道同步模态框 */}
      <Modal
        title="多渠道同步"
        open={channelSyncVisible}
        onCancel={() => setChannelSyncVisible(false)}
        footer={null}
        width={700}
      >
        <Form
          form={syncForm}
          layout="vertical"
          onFinish={handleChannelSync}
        >
          <Alert
            message="多渠道同步"
            description="将商品同步到多个渠道，支持选择同步内容和渠道特定配置。"
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />

          <Form.Item name="product_id" hidden>
            <Input />
          </Form.Item>

          <Form.Item
            name="channel_ids"
            label="目标渠道"
            rules={[{ required: true, message: '请选择至少一个渠道' }]}
          >
            <Checkbox.Group style={{ width: '100%' }}>
              <Row gutter={[16, 8]}>
                {channels.map(channel => (
                  <Col span={8} key={channel.id}>
                    <Checkbox value={channel.id}>
                      {channel.custom_name}
                    </Checkbox>
                  </Col>
                ))}
              </Row>
            </Checkbox.Group>
          </Form.Item>

          <Form.Item
            name="sync_options"
            label="同步内容"
            initialValue={['basic_info', 'price', 'stock', 'images']}
          >
            <Checkbox.Group>
              <Row gutter={[16, 8]}>
                <Col span={8}>
                  <Checkbox value="basic_info">基本信息</Checkbox>
                </Col>
                <Col span={8}>
                  <Checkbox value="price">价格</Checkbox>
                </Col>
                <Col span={8}>
                  <Checkbox value="stock">库存</Checkbox>
                </Col>
                <Col span={8}>
                  <Checkbox value="images">图片</Checkbox>
                </Col>
                <Col span={8}>
                  <Checkbox value="description">描述</Checkbox>
                </Col>
                <Col span={8}>
                  <Checkbox value="category">分类</Checkbox>
                </Col>
              </Row>
            </Checkbox.Group>
          </Form.Item>

          <div style={{ textAlign: 'right', marginTop: 24 }}>
            <Space>
              <Button onClick={() => setChannelSyncVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit" loading={loading}>
                开始同步
              </Button>
            </Space>
          </div>
        </Form>
      </Modal>

      {/* AI优化模态框 */}
      <Modal
        title="AI智能优化"
        open={aiOptimizeVisible}
        onCancel={() => setAiOptimizeVisible(false)}
        footer={null}
        width={800}
      >
        <Form
          form={aiForm}
          layout="vertical"
          onFinish={handleAIOptimization}
        >
          <Alert
            message="AI智能优化"
            description="使用AI技术优化商品标题、描述等信息，提升商品在各平台的表现。"
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />

          <Form.Item name="product_id" hidden>
            <Input />
          </Form.Item>

          <Form.Item
            name="optimization_type"
            label="优化类型"
            rules={[{ required: true, message: '请选择优化类型' }]}
          >
            <Checkbox.Group>
              <Row gutter={[16, 8]}>
                <Col span={8}>
                  <Checkbox value="title">商品标题</Checkbox>
                </Col>
                <Col span={8}>
                  <Checkbox value="description">商品描述</Checkbox>
                </Col>
                <Col span={8}>
                  <Checkbox value="keywords">关键词</Checkbox>
                </Col>
                <Col span={8}>
                  <Checkbox value="category">类目推荐</Checkbox>
                </Col>
                <Col span={8}>
                  <Checkbox value="pricing">定价建议</Checkbox>
                </Col>
                <Col span={8}>
                  <Checkbox value="tags">标签优化</Checkbox>
                </Col>
              </Row>
            </Checkbox.Group>
          </Form.Item>

          <Form.Item
            name="target_platform"
            label="目标平台（可选）"
          >
            <Select placeholder="选择特定平台进行优化">
              {channels.map(channel => (
                <Option key={channel.id} value={channel.id}>
                  {channel.custom_name}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="current_title"
            label="当前标题"
          >
            <Input placeholder="当前商品标题" />
          </Form.Item>

          <Form.Item
            name="current_description"
            label="当前描述"
          >
            <Input.TextArea rows={4} placeholder="当前商品描述" />
          </Form.Item>

          <div style={{ textAlign: 'right', marginTop: 24 }}>
            <Space>
              <Button onClick={() => setAiOptimizeVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit" loading={loading}>
                开始优化
              </Button>
            </Space>
          </div>
        </Form>
      </Modal>

      {/* 批量操作模态框 */}
      <Modal
        title="批量操作"
        open={batchOperationVisible}
        onCancel={() => setBatchOperationVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={batchForm}
          layout="vertical"
          onFinish={handleBatchOperation}
        >
          <Alert
            message={`已选择 ${selectedRowKeys.length} 个商品`}
            description="选择要执行的批量操作类型。"
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />

          <Form.Item
            name="operation_type"
            label="操作类型"
            rules={[{ required: true, message: '请选择操作类型' }]}
          >
            <Select placeholder="选择批量操作类型">
              <Option value="sync">批量同步</Option>
              <Option value="price_update">批量调价</Option>
              <Option value="stock_update">批量更新库存</Option>
              <Option value="status_update">批量上下架</Option>
              <Option value="category_update">批量更新类目</Option>
              <Option value="ai_optimize">批量AI优化</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="target_channels"
            label="目标渠道"
          >
            <Checkbox.Group style={{ width: '100%' }}>
              <Row gutter={[16, 8]}>
                {channels.map(channel => (
                  <Col span={8} key={channel.id}>
                    <Checkbox value={channel.id}>
                      {channel.custom_name}
                    </Checkbox>
                  </Col>
                ))}
              </Row>
            </Checkbox.Group>
          </Form.Item>

          <div style={{ textAlign: 'right', marginTop: 24 }}>
            <Space>
              <Button onClick={() => setBatchOperationVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit" loading={loading}>
                执行操作
              </Button>
            </Space>
          </div>
        </Form>
      </Modal>
    </div>
  );
};

export default ProductManagementNew;
