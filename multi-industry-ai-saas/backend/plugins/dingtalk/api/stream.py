#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
钉钉Stream模式连接管理API
提供Stream连接的启动、停止、状态查询等功能
"""

import logging
import uuid
from typing import Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_

from db.database import get_db
from models.user import User
from models.project import Project
from models.tenant import Tenant
from core.auth import get_current_user
from api.deps import get_current_project, get_current_tenant
from ..models.models import DingTalkWebhook
from ..services.stream_service import get_stream_manager, start_stream_service, stop_stream_service

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("/stream/start")
async def start_stream_connections(
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: AsyncSession = Depends(get_db)
):
    """启动所有Stream模式连接"""
    try:
        # 启动Stream服务
        await start_stream_service(db)
        
        return {
            "success": True,
            "message": "Stream服务启动成功"
        }
        
    except Exception as e:
        logger.error(f"启动Stream服务失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"启动Stream服务失败: {str(e)}"
        )


@router.post("/stream/stop")
async def stop_stream_connections(
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: AsyncSession = Depends(get_db)
):
    """停止所有Stream模式连接"""
    try:
        # 停止Stream服务
        await stop_stream_service()
        
        return {
            "success": True,
            "message": "Stream服务停止成功"
        }
        
    except Exception as e:
        logger.error(f"停止Stream服务失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"停止Stream服务失败: {str(e)}"
        )


@router.post("/stream/restart/{webhook_id}")
async def restart_stream_connection(
    webhook_id: str,
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: AsyncSession = Depends(get_db)
):
    """重启指定Webhook的Stream连接"""
    try:
        # 查找Webhook
        query = select(DingTalkWebhook).where(
            and_(
                DingTalkWebhook.id == webhook_id,
                DingTalkWebhook.tenant_id == current_tenant.id,
                DingTalkWebhook.project_id == current_project.id,
                DingTalkWebhook.subscription_mode == 'stream'
            )
        )
        
        result = await db.execute(query)
        webhook = result.scalar_one_or_none()
        
        if not webhook:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Stream模式Webhook不存在"
            )
        
        # 获取Stream管理器
        stream_manager = await get_stream_manager(db)
        
        # 断开现有连接
        if str(webhook.id) in stream_manager.connections:
            connection = stream_manager.connections[str(webhook.id)]
            await connection.disconnect()
            del stream_manager.connections[str(webhook.id)]
        
        # 重新创建连接
        await stream_manager._create_connection(webhook)
        
        return {
            "success": True,
            "message": f"Stream连接重启成功: {webhook.name}"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"重启Stream连接失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"重启Stream连接失败: {str(e)}"
        )


@router.get("/stream/status")
async def get_stream_status(
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: AsyncSession = Depends(get_db)
):
    """获取Stream连接状态"""
    try:
        # 查询所有Stream模式的Webhook
        query = select(DingTalkWebhook).where(
            and_(
                DingTalkWebhook.tenant_id == current_tenant.id,
                DingTalkWebhook.project_id == current_project.id,
                DingTalkWebhook.subscription_mode == 'stream'
            )
        )
        
        result = await db.execute(query)
        webhooks = result.scalars().all()
        
        # 获取Stream管理器
        try:
            stream_manager = await get_stream_manager(db)
            connections = stream_manager.connections
        except:
            connections = {}
        
        # 构建状态信息
        status_info = []
        for webhook in webhooks:
            webhook_id = str(webhook.id)
            connection = connections.get(webhook_id)
            
            status_info.append({
                "webhook_id": webhook_id,
                "webhook_name": webhook.name,
                "enabled": webhook.enabled,
                "stream_status": webhook.stream_status,
                "is_connected": connection.is_connected if connection else False,
                "reconnect_attempts": connection.reconnect_attempts if connection else 0,
                "last_heartbeat": connection.last_heartbeat.isoformat() if connection and connection.last_heartbeat else None
            })
        
        return {
            "success": True,
            "data": {
                "total_stream_webhooks": len(webhooks),
                "active_connections": len([s for s in status_info if s["is_connected"]]),
                "connections": status_info
            }
        }
        
    except Exception as e:
        logger.error(f"获取Stream状态失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取Stream状态失败: {str(e)}"
        )


@router.get("/stream/health")
async def check_stream_health(
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: AsyncSession = Depends(get_db)
):
    """检查Stream服务健康状态"""
    try:
        # 获取Stream管理器
        try:
            stream_manager = await get_stream_manager(db)
            is_running = stream_manager.is_running
            total_connections = len(stream_manager.connections)
            active_connections = len([c for c in stream_manager.connections.values() if c.is_connected])
        except:
            is_running = False
            total_connections = 0
            active_connections = 0
        
        health_status = "healthy" if is_running and active_connections > 0 else "unhealthy"
        
        return {
            "success": True,
            "data": {
                "status": health_status,
                "is_running": is_running,
                "total_connections": total_connections,
                "active_connections": active_connections,
                "timestamp": "2024-01-01T00:00:00Z"  # 实际应该使用当前时间
            }
        }
        
    except Exception as e:
        logger.error(f"检查Stream健康状态失败: {e}")
        return {
            "success": False,
            "data": {
                "status": "error",
                "error": str(e)
            }
        } 