#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
AI 配置模型
"""

import uuid
from datetime import datetime
from sqlalchemy import Column, String, Integer, Float, Boolean, ForeignKey, DateTime, Text, func
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship

from db.database import Base

class AIConfig(Base):
    """AI 配置模型"""
    __tablename__ = "ai_configs"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id", ondelete="CASCADE"), nullable=True, comment="租户 ID，为空表示系统级配置")
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id", ondelete="CASCADE"), nullable=True, comment="项目 ID，为空表示租户级配置")
    provider_id = Column(UUID(as_uuid=True), ForeignKey("ai_providers.id", ondelete="CASCADE"), nullable=False, comment="提供商 ID")
    model_id = Column(UUID(as_uuid=True), ForeignKey("ai_models.id", ondelete="CASCADE"), nullable=False, comment="模型 ID")
    name = Column(String(100), nullable=False, comment="配置名称")
    description = Column(Text, nullable=True, comment="描述")
    api_key = Column(String(255), nullable=False, comment="API 密钥，加密存储")
    api_endpoint = Column(String(255), nullable=True, comment="API 端点，可选，覆盖默认端点")
    proxy_url = Column(String(255), nullable=True, comment="代理 URL，可选")
    temperature = Column(Float, nullable=True, default=0.7, comment="温度参数")
    max_tokens = Column(Integer, nullable=True, comment="最大 token 数")
    top_p = Column(Float, nullable=True, default=1.0, comment="Top P 参数")
    frequency_penalty = Column(Float, nullable=True, default=0.0, comment="频率惩罚")
    presence_penalty = Column(Float, nullable=True, default=0.0, comment="存在惩罚")
    is_default = Column(Boolean, nullable=False, default=False, comment="是否为默认配置")
    priority = Column(Integer, nullable=False, default=0, comment="路由优先级")
    weight = Column(Integer, nullable=False, default=1, comment="负载均衡权重")
    status = Column(String(20), nullable=False, default="active", comment="状态：active, inactive")
    config = Column(JSONB, nullable=True, comment="其他配置参数")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), onupdate=func.now(), comment="更新时间")

    # 关系
    provider = relationship("AIProvider", back_populates="configs")
    model = relationship("AIModel", back_populates="configs")
    tenant = relationship("Tenant", back_populates="ai_configs")
    project = relationship("Project", back_populates="ai_configs")
    usages = relationship("AIUsage", back_populates="config", cascade="all, delete-orphan")

    def __repr__(self):
        # 避免访问可能导致DetachedInstanceError的关联属性
        return f"<AIConfig(id={self.id}, name='{self.name}')>"
