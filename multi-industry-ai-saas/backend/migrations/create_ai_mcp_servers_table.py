#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
创建AI助手MCP服务器表

Revision ID: create_ai_mcp_servers_table
Revises: 
Create Date: 2024-12-19 10:00:00.000000
"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers
revision = 'create_ai_mcp_servers_table'
down_revision = None
branch_labels = None
depends_on = None

def upgrade():
    """创建AI助手MCP服务器表"""
    op.create_table(
        'ai_mcp_servers',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True),
        sa.Column('name', sa.String(100), nullable=False),
        sa.<PERSON>umn('description', sa.Text, nullable=True),
        sa.Column('project_id', postgresql.UUID(as_uuid=True), sa.<PERSON>ey('projects.id'), nullable=False),
        
        # 传输协议配置
        sa.Column('transport_type', sa.String(20), nullable=False),  # sse, streamhttp, stdio
        sa.Column('transport_types', sa.JSON, default=list),  # 支持的传输类型列表
        
        # 部署模式
        sa.Column('deployment_mode', sa.String(20), default='external'),  # external, internal, local, docker, npm
        sa.Column('source', sa.String(20), default='manual'),  # manual, sapi, import
        
        # 连接配置
        sa.Column('config', sa.JSON, default=dict),  # 连接配置（URL、认证等）
        
        # 状态信息
        sa.Column('enabled', sa.Boolean, default=True),
        sa.Column('status', sa.String(20), default='unknown'),  # unknown, connected, disconnected, error
        sa.Column('last_connected_at', sa.DateTime, nullable=True),
        
        # 工具信息
        sa.Column('tools_count', sa.Integer, default=0),
        sa.Column('available_tools', sa.JSON, default=list),  # 缓存的工具列表
        
        # 元数据
        sa.Column('created_at', sa.DateTime, default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime, default=sa.func.now(), onupdate=sa.func.now()),
        sa.Column('created_by_id', postgresql.UUID(as_uuid=True), sa.ForeignKey('users.id'), nullable=True),
        
        # SAPI插件关联（如果是从SAPI添加的）
        sa.Column('sapi_server_id', postgresql.UUID(as_uuid=True), nullable=True),  # 关联的SAPI服务器ID
    )
    
    # 创建索引
    op.create_index('idx_ai_mcp_servers_project_id', 'ai_mcp_servers', ['project_id'])
    op.create_index('idx_ai_mcp_servers_sapi_server_id', 'ai_mcp_servers', ['sapi_server_id'])
    op.create_index('idx_ai_mcp_servers_status', 'ai_mcp_servers', ['status'])
    op.create_index('idx_ai_mcp_servers_enabled', 'ai_mcp_servers', ['enabled'])

def downgrade():
    """删除AI助手MCP服务器表"""
    op.drop_index('idx_ai_mcp_servers_enabled', 'ai_mcp_servers')
    op.drop_index('idx_ai_mcp_servers_status', 'ai_mcp_servers')
    op.drop_index('idx_ai_mcp_servers_sapi_server_id', 'ai_mcp_servers')
    op.drop_index('idx_ai_mcp_servers_project_id', 'ai_mcp_servers')
    op.drop_table('ai_mcp_servers') 