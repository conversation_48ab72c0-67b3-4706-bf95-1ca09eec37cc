#!/usr/bin/env python
# -*- coding: utf-8 -*-

from typing import List, Optional, Dict, Any, Union
from pydantic import BaseModel, Field, validator
from datetime import datetime
import uuid
from enum import Enum

# 报损状态枚举
class LossStatus(str, Enum):
    PENDING = "pending"
    APPROVED = "approved"
    REJECTED = "rejected"
    WITHDRAWN = "withdrawn"

# 报损原因枚举
class LossReason(str, Enum):
    EXPIRED = "expired"  # 过期
    DAMAGED = "damaged"  # 损坏
    LOST = "lost"        # 丢失
    QUALITY = "quality"  # 质量问题
    OTHER = "other"      # 其他

# 基础报损模型
class LossBase(BaseModel):
    store_id: uuid.UUID
    product_id: uuid.UUID
    quantity: float
    reason: str
    notes: Optional[str] = None
    product_unit: Optional[str] = None

# 创建报损请求模型
class LossCreate(LossBase):
    pass

# 更新报损请求模型
class LossUpdate(BaseModel):
    quantity: Optional[float] = None
    reason: Optional[str] = None
    notes: Optional[str] = None
    product_unit: Optional[str] = None

# 报损状态更新请求模型
class LossStatusUpdate(BaseModel):
    status: LossStatus
    reject_reason: Optional[str] = None

    @validator('reject_reason')
    def validate_reject_reason(cls, v, values):
        if values.get('status') == LossStatus.REJECTED and not v:
            raise ValueError('拒绝时必须提供拒绝原因')
        return v

# 报损响应模型
class LossResponse(LossBase):
    id: uuid.UUID
    project_id: uuid.UUID
    unit_price: float
    total_amount: float
    status: str
    images: Optional[List[Dict[str, Any]]] = []
    images_size: Optional[int] = 0
    created_by: uuid.UUID
    created_at: datetime
    updated_at: datetime
    approved_by: Optional[uuid.UUID] = None
    approved_at: Optional[datetime] = None
    reject_reason: Optional[str] = None
    
    # 关联信息
    store_name: Optional[str] = None
    product_name: Optional[str] = None
    product_code: Optional[str] = None
    creator_name: Optional[str] = None
    approver_name: Optional[str] = None

    model_config = {"from_attributes": True}

# 报损列表响应模型
class LossListResponse(BaseModel):
    items: List[LossResponse]
    total: int
    page: int
    size: int
    pages: int
