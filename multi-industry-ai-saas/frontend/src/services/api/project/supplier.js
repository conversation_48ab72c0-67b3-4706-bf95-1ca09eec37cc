import api from '../httpClient';
import { getProjectId } from '../httpClient';

/**
 * 项目-供应商管理API
 */
const supplier = {
  /**
   * 获取供应商列表
   * @param {Object} params - 查询参数
   * @returns {Promise} - 供应商列表
   */
  getList: async (params = {}) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.get(`/project/${projectId}/suppliers`, { params });
  },

  /**
   * 获取供应商详情
   * @param {string} id - 供应商ID
   * @returns {Promise} - 供应商详情
   */
  getDetail: async (id) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.get(`/project/${projectId}/suppliers/${id}`);
  },

  /**
   * 创建供应商
   * @param {Object} data - 供应商数据
   * @returns {Promise} - 创建结果
   */
  create: async (data) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.post(`/project/${projectId}/suppliers`, data);
  },

  /**
   * 更新供应商
   * @param {string} id - 供应商ID
   * @param {Object} data - 供应商数据
   * @returns {Promise} - 更新结果
   */
  update: async (id, data) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.put(`/project/${projectId}/suppliers/${id}`, data);
  },

  /**
   * 删除供应商
   * @param {string} id - 供应商ID
   * @returns {Promise} - 删除结果
   */
  delete: async (id) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.delete(`/project/${projectId}/suppliers/${id}`);
  },

  /**
   * 获取供应商产品列表
   * @param {string} id - 供应商ID
   * @param {Object} params - 查询参数
   * @returns {Promise} - 供应商产品列表
   */
  getProducts: async (id, params = {}) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.get(`/project/${projectId}/suppliers/${id}/products`, { params });
  },

  /**
   * 获取供应商采购订单列表
   * @param {string} id - 供应商ID
   * @param {Object} params - 查询参数
   * @returns {Promise} - 供应商采购订单列表
   */
  getPurchaseOrders: async (id, params = {}) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.get(`/project/${projectId}/suppliers/${id}/purchase-orders`, { params });
  },

  /**
   * 获取供应商评估数据
   * @param {string} id - 供应商ID
   * @param {Object} params - 查询参数
   * @returns {Promise} - 供应商评估数据
   */
  getEvaluation: async (id, params = {}) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.get(`/project/${projectId}/suppliers/${id}/evaluation`, { params });
  },

  /**
   * 添加供应商评估
   * @param {string} id - 供应商ID
   * @param {Object} data - 评估数据
   * @returns {Promise} - 添加结果
   */
  addEvaluation: async (id, data) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.post(`/project/${projectId}/suppliers/${id}/evaluation`, data);
  },

  /**
   * 批量导入供应商
   * @param {FormData} formData - 包含供应商数据的表单
   * @returns {Promise} - 导入结果
   */
  batchImport: async (formData) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.post(`/project/${projectId}/suppliers/batch-import`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
  },

  /**
   * 导出供应商列表
   * @param {Object} params - 查询参数
   * @returns {Promise} - 导出结果
   */
  export: async (params = {}) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.get(`/project/${projectId}/suppliers/export`, { 
      params,
      responseType: 'blob'
    });
  }
};

export default supplier;
