import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Avatar,
  Typography,
  Form,
  Input,
  Button,
  message,
  Tabs,
  List,
  Tag,
  Divider,
  Spin,
  Upload,
  Alert,
  Popconfirm,
  Modal,
  Table
} from 'antd';
import {
  UserOutlined,
  MailOutlined,
  PhoneOutlined,
  TeamOutlined,
  LockOutlined,
  UploadOutlined,
  EditOutlined,
  SaveOutlined,
  CloseOutlined,
  SecurityScanOutlined,
  CheckCircleOutlined,
  WechatOutlined,
  DingdingOutlined,
  LinkOutlined
} from '@ant-design/icons';
import { useAuth } from '../../../contexts/AuthContext';
import userApi from '../../../services/api/project/userProfit';
import './Profile.css';
import { useLocation } from 'react-router-dom';

const { Title } = Typography;
const { TabPane } = Tabs;

const UserProfile = () => {
  const { user } = useAuth();
  const location = useLocation();
  const [profileForm] = Form.useForm();
  const [passwordForm] = Form.useForm();

  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [editing, setEditing] = useState(false);
  const [userProfile, setUserProfile] = useState(null);
  const [activities, setActivities] = useState([]);
  const [avatarUrl, setAvatarUrl] = useState(null);
  const [activeTab, setActiveTab] = useState('profile');
  const [thirdPartyAccounts, setThirdPartyAccounts] = useState([]);

  // 获取平台名称
  const getPlatformName = (platform) => {
    const nameMap = {
      wechat: '微信',
      dingtalk: '钉钉',
      feishu: '飞书',
      wecom: '企业微信'
    };
    return nameMap[platform] || platform;
  };

  // 加载第三方账号列表
  const loadThirdPartyAccounts = async () => {
    try {
      const response = await userApi.getThirdPartyAccounts();
      if (response && response.data) {
        setThirdPartyAccounts(response.data);
      }
    } catch (error) {
      console.error('加载第三方账号列表失败:', error);
    }
  };

  // 监听URL参数，处理绑定回调
  useEffect(() => {
    const urlParams = new URLSearchParams(location.search);
    const bindSuccess = urlParams.get('bind_success');
    const bindError = urlParams.get('bind_error');
    const platform = urlParams.get('platform');

    if (bindSuccess === 'true') {
      message.success(`${getPlatformName(platform)}账号绑定成功！`);
      // 刷新第三方账号列表
      loadThirdPartyAccounts();
      // 清理URL参数
      const newUrl = window.location.pathname;
      window.history.replaceState({}, document.title, newUrl);
      // 切换到第三方账号标签页
      setActiveTab('third-party');
    } else if (bindError) {
      const errorMessages = {
        'invalid_state': '无效的授权状态',
        'project_mismatch': '项目不匹配',
        'user_not_found': '用户不存在',
        'callback_failed': '绑定回调处理失败'
      };
      message.error(`绑定失败：${errorMessages[bindError] || '未知错误'}`);
      // 清理URL参数
      const newUrl = window.location.pathname;
      window.history.replaceState({}, document.title, newUrl);
    }

    // 监听来自绑定弹窗的消息
    const handleMessage = (event) => {
      // 验证消息来源（可以根据需要添加更严格的验证）
      if (event.data && typeof event.data === 'object') {
        if (event.data.type === 'BIND_SUCCESS') {
          const { platform, data } = event.data;
          message.success(`${getPlatformName(platform)}账号绑定成功！`);
          // 刷新第三方账号列表
          loadThirdPartyAccounts();
          // 切换到第三方账号标签页
          setActiveTab('third-party');
        } else if (event.data.type === 'BIND_ERROR') {
          const { error, message: errorMessage } = event.data;
          const errorMessages = {
            'missing_code': '缺少授权码',
            'invalid_state': '无效的授权状态',
            'project_mismatch': '项目不匹配',
            'user_not_found': '用户不存在',
            'platform_unknown': '无法确定平台类型或获取用户信息',
            'callback_failed': '绑定回调处理失败'
          };
          message.error(`绑定失败：${errorMessages[error] || errorMessage || '未知错误'}`);
        }
      }
    };

    // 添加事件监听器
    window.addEventListener('message', handleMessage);

    // 清理函数
    return () => {
      window.removeEventListener('message', handleMessage);
    };
  }, [location]);

  // 加载用户资料
  useEffect(() => {
    const loadUserProfile = async () => {
      try {
        setLoading(true);
        // 调用获取用户资料的API
        const response = await userApi.getProfile();

        if (response) {
          const profileData = response.data;
          setUserProfile(profileData);
          setActivities(profileData.activities || []);
          setThirdPartyAccounts(profileData.third_party_accounts || []);

          if (profileData.avatar_url) {
            setAvatarUrl(profileData.avatar_url);
          }

          // 设置表单初始值
          profileForm.setFieldsValue({
            username: profileData.username,
            email: profileData.email,
            phone: profileData.phone || '',
            department: profileData.department || ''
          });
        } else {
          // 使用模拟数据
          const mockData = {
            id: user?.id || '1',
            username: user?.username || '用户名',
            email: user?.email || '<EMAIL>',
            phone: '***********',
            department: '技术部',
            role: user?.role || 'user',
            avatar_url: user?.avatar_url,
            created_at: new Date().toISOString(),
            last_login: new Date().toISOString(),
            activities: [
              { id: 1, type: 'login', description: '登录系统', created_at: new Date().toISOString() },
              { id: 2, type: 'update', description: '更新个人资料', created_at: new Date(Date.now() - ********).toISOString() }
            ],
            third_party_accounts: []
          };

          setUserProfile(mockData);
          setActivities(mockData.activities);
          setThirdPartyAccounts(mockData.third_party_accounts);

          if (mockData.avatar_url) {
            setAvatarUrl(mockData.avatar_url);
          }

          // 设置表单初始值
          profileForm.setFieldsValue({
            username: mockData.username,
            email: mockData.email,
            phone: mockData.phone || '',
            department: mockData.department || ''
          });
        }
      } catch (error) {
        console.error('加载用户资料失败:', error);
        message.error('加载用户资料失败');
      } finally {
        setLoading(false);
      }
    };

    loadUserProfile();
  }, [user, profileForm]);

  // 处理头像上传
  const handleAvatarUpload = (info) => {
    if (info.file.status === 'done') {
      message.success('头像上传成功');
      setAvatarUrl(info.file.response.url || URL.createObjectURL(info.file.originFileObj));
    } else if (info.file.status === 'error') {
      message.error('头像上传失败');
    }
  };

  // 保存个人资料
  const handleProfileSave = async (values) => {
    try {
      setSaving(true);
      // 调用更新用户资料的API
      const response = await userApi.updateProfile(values);

      if (response) {
        message.success('个人资料保存成功');
        setEditing(false);

        // 更新本地用户资料
        setUserProfile(prev => ({
          ...prev,
          ...values
        }));
      } else {
        message.error('保存个人资料失败');
      }
    } catch (error) {
      console.error('保存个人资料失败:', error);
      message.error('保存个人资料失败');
    } finally {
      setSaving(false);
    }
  };

  // 修改密码
  const handlePasswordChange = async (values) => {
    try {
      setSaving(true);
      // 调用修改密码的API
      const response = await userApi.changePassword(values);

      if (response) {
        message.success('密码修改成功');
        passwordForm.resetFields();
      } else {
        message.error('修改密码失败');
      }
    } catch (error) {
      console.error('修改密码失败:', error);
      message.error('修改密码失败');
    } finally {
      setSaving(false);
    }
  };

  // 绑定第三方账号
  const handleBindThirdParty = async (platform) => {
    Modal.confirm({
      title: `绑定${getPlatformName(platform)}账号`,
      content: `确定要绑定${getPlatformName(platform)}账号吗？绑定后您可以使用${getPlatformName(platform)}账号直接登录系统。`,
      okText: '确定绑定',
      cancelText: '取消',
      onOk: async () => {
        try {
          message.info(`正在获取${getPlatformName(platform)}授权链接...`);
          
          // 统一使用项目级别的绑定接口
          const response = await userApi.getThirdPartyAuthUrl(platform);
          if (response && response.data && response.data.auth_url) {
            // 在新窗口打开授权链接
            const authWindow = window.open(
              response.data.auth_url,
              'auth_window',
              'width=600,height=700,scrollbars=yes,resizable=yes'
            );
            
            // 检查弹窗是否被阻止
            if (!authWindow) {
              message.error('弹窗被阻止，请允许弹窗后重试');
              return;
            }
            
            // 监听授权窗口关闭（作为备用机制）
            const checkClosed = setInterval(() => {
              if (authWindow.closed) {
                clearInterval(checkClosed);
                // 如果窗口关闭但没有收到postMessage，提示用户检查绑定状态
                setTimeout(() => {
                  message.info('授权窗口已关闭，正在检查绑定状态...');
                  loadThirdPartyAccounts();
                }, 500);
              }
            }, 1000);
            
            // 设置超时清理
            setTimeout(() => {
              if (checkClosed) {
                clearInterval(checkClosed);
              }
            }, 300000); // 5分钟超时
            
          } else {
            message.error('获取授权链接失败');
          }
        } catch (error) {
          console.error(`获取${platform}授权链接失败:`, error);
          message.error(`获取${getPlatformName(platform)}授权链接失败`);
        }
      }
    });
  };

  // 解绑第三方账号
  const handleUnbindThirdParty = async (accountId, platform) => {
    try {
      setSaving(true);
      const response = await userApi.unbindThirdParty(accountId);
      if (response) {
        message.success(`解绑${getPlatformName(platform)}成功`);
        setThirdPartyAccounts(prev => prev.filter(account => account.id !== accountId));
      } else {
        message.error('解绑失败');
      }
    } catch (error) {
      console.error('解绑第三方账号失败:', error);
      message.error('解绑失败');
    } finally {
      setSaving(false);
    }
  };

  // 格式化日期时间
  const formatDateTime = (dateTimeStr) => {
    if (!dateTimeStr) return '未知';
    return new Date(dateTimeStr).toLocaleString('zh-CN');
  };

  // 获取角色名称
  const getRoleName = (role) => {
    const roleMap = {
      admin: '系统管理员',
      system_admin: '系统管理员',
      super_admin: '超级管理员',
      service_provider_admin: '服务商管理员',
      tenant_admin: '租户管理员',
      project_admin: '项目管理员',
      user: '普通用户'
    };
    return roleMap[role] || '未知角色';
  };

  // 获取角色颜色
  const getRoleColor = (role) => {
    const colorMap = {
      admin: 'red',
      system_admin: 'red',
      super_admin: 'red',
      service_provider_admin: 'volcano',
      tenant_admin: 'orange',
      project_admin: 'gold',
      user: 'blue'
    };
    return colorMap[role] || 'default';
  };

  // 安全设置相关函数
  const [securityModalVisible, setSecurityModalVisible] = useState(false);
  const [securityModalType, setSecurityModalType] = useState('');
  const [loginHistory, setLoginHistory] = useState([]);
  const [loginDevices, setLoginDevices] = useState([]);
  const [securityLoading, setSecurityLoading] = useState(false);

  // 查看登录历史
  const handleViewLoginHistory = async () => {
    try {
      setSecurityLoading(true);
      setSecurityModalType('login_history');
      setSecurityModalVisible(true);
      
      const response = await userApi.getLoginHistory({ limit: 100 });
      if (response && response.data) {
        setLoginHistory(response.data.login_history || []);
      }
    } catch (error) {
      console.error('获取登录历史失败:', error);
      message.error('获取登录历史失败');
    } finally {
      setSecurityLoading(false);
    }
  };

  // 查看登录设备
  const handleViewLoginDevices = async () => {
    try {
      setSecurityLoading(true);
      setSecurityModalType('login_devices');
      setSecurityModalVisible(true);
      
      const response = await userApi.getLoginDevices();
      if (response && response.data) {
        setLoginDevices(response.data.devices || []);
      }
    } catch (error) {
      console.error('获取登录设备失败:', error);
      message.error('获取登录设备失败');
    } finally {
      setSecurityLoading(false);
    }
  };

  // 注销设备
  const handleLogoutDevice = async (deviceId) => {
    try {
      const response = await userApi.logoutDevice(deviceId);
      if (response) {
        message.success('设备注销成功');
        // 重新获取设备列表
        handleViewLoginDevices();
      }
    } catch (error) {
      console.error('注销设备失败:', error);
      message.error('注销设备失败');
    }
  };

  // 锁定账号
  const handleLockAccount = async () => {
    Modal.confirm({
      title: '确认锁定账号',
      content: '锁定账号后，您将需要联系管理员解锁。确定要继续吗？',
      okText: '确定锁定',
      cancelText: '取消',
      okType: 'danger',
      onOk: async () => {
        try {
          const response = await userApi.lockAccount();
          if (response) {
            message.success('账号锁定成功');
            // 可以考虑退出登录
          }
        } catch (error) {
          console.error('锁定账号失败:', error);
          message.error('锁定账号失败');
        }
      }
    });
  };

  // 获取活动类型图标
  const getActivityIcon = (type) => {
    const iconMap = {
      login: <UserOutlined />,
      update: <EditOutlined />,
      create: <CheckCircleOutlined />
    };
    return iconMap[type] || <UserOutlined />;
  };

  // 获取平台图标
  const getPlatformIcon = (platform) => {
    const iconMap = {
      wechat: <WechatOutlined style={{ color: '#07C160' }} />,
      dingtalk: <DingdingOutlined style={{ color: '#1890ff' }} />,
      feishu: <LinkOutlined style={{ color: '#3370ff' }} />,
      wecom: <WechatOutlined style={{ color: '#2A8CE5' }} />
    };
    return iconMap[platform] || <LinkOutlined />;
  };

  if (loading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
        <Spin size="large" tip="加载中..." />
      </div>
    );
  }

  return (
    <div className="user-profile-container">
      <Row gutter={[24, 24]}>
        <Col xs={24} md={8}>
          <Card>
            <div style={{ textAlign: 'center', marginBottom: '20px' }}>
              <Avatar
                size={100}
                icon={<UserOutlined />}
                src={avatarUrl}
                style={{ backgroundColor: '#1890ff' }}
              />
              <div style={{ marginTop: '16px' }}>
                <Upload
                  name="avatar"
                  showUploadList={false}
                  action="/api/v1/user/avatar"
                  onChange={handleAvatarUpload}
                >
                  <Button icon={<UploadOutlined />}>更换头像</Button>
                </Upload>
              </div>
            </div>

            <div style={{ textAlign: 'center' }}>
              <Title level={3}>{userProfile?.username}</Title>
              <Tag color={getRoleColor(userProfile?.role)}>{getRoleName(userProfile?.role)}</Tag>
            </div>

            <Divider />

            <div>
              <p><MailOutlined /> 邮箱: {userProfile?.email}</p>
              <p><PhoneOutlined /> 电话: {userProfile?.phone}</p>
              <p><TeamOutlined /> 部门: {userProfile?.department}</p>
            </div>

            <Divider />

            <div>
              <p>账号创建时间: {formatDateTime(userProfile?.created_at)}</p>
              <p>上次登录时间: {formatDateTime(userProfile?.last_login_at || userProfile?.last_login)}</p>
            </div>
          </Card>
        </Col>

        <Col xs={24} md={16}>
          <Card>
            <Tabs activeKey={activeTab} onChange={setActiveTab}>
              <TabPane
                tab={
                  <span>
                    <UserOutlined />
                    个人资料
                  </span>
                }
                key="profile"
              >
                <div style={{ display: 'flex', justifyContent: 'flex-end', marginBottom: '16px' }}>
                  {!editing ? (
                    <Button
                      type="primary"
                      icon={<EditOutlined />}
                      onClick={() => setEditing(true)}
                    >
                      编辑资料
                    </Button>
                  ) : (
                    <>
                      <Button
                        icon={<CloseOutlined />}
                        style={{ marginRight: '8px' }}
                        onClick={() => {
                          setEditing(false);
                          profileForm.setFieldsValue(userProfile);
                        }}
                      >
                        取消
                      </Button>
                      <Button
                        type="primary"
                        icon={<SaveOutlined />}
                        onClick={() => profileForm.submit()}
                        loading={saving}
                      >
                        保存
                      </Button>
                    </>
                  )}
                </div>

                <Form
                  form={profileForm}
                  layout="vertical"
                  disabled={!editing}
                  onFinish={handleProfileSave}
                >
                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item
                        name="username"
                        label="用户名"
                        rules={[{ required: true, message: '请输入用户名' }]}
                      >
                        <Input prefix={<UserOutlined />} placeholder="用户名" />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        name="email"
                        label="邮箱"
                        rules={[
                          { required: true, message: '请输入邮箱' },
                          { type: 'email', message: '请输入有效的邮箱地址' }
                        ]}
                      >
                        <Input prefix={<MailOutlined />} placeholder="邮箱" />
                      </Form.Item>
                    </Col>
                  </Row>

                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item
                        name="phone"
                        label="电话"
                        rules={[{ pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号码' }]}
                      >
                        <Input prefix={<PhoneOutlined />} placeholder="电话" />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        name="department"
                        label="部门"
                      >
                        <Input prefix={<TeamOutlined />} placeholder="部门" />
                      </Form.Item>
                    </Col>
                  </Row>
                </Form>
              </TabPane>

              <TabPane
                tab={
                  <span>
                    <LockOutlined />
                    修改密码
                  </span>
                }
                key="password"
              >
                <Form
                  form={passwordForm}
                  layout="vertical"
                  onFinish={handlePasswordChange}
                >
                  <Form.Item
                    name="currentPassword"
                    label="当前密码"
                    rules={[{ required: true, message: '请输入当前密码' }]}
                  >
                    <Input.Password prefix={<LockOutlined />} placeholder="当前密码" />
                  </Form.Item>

                  <Form.Item
                    name="newPassword"
                    label="新密码"
                    rules={[
                      { required: true, message: '请输入新密码' },
                      { min: 8, message: '密码长度不能少于8个字符' }
                    ]}
                  >
                    <Input.Password prefix={<LockOutlined />} placeholder="新密码" />
                  </Form.Item>

                  <Form.Item
                    name="confirmPassword"
                    label="确认新密码"
                    dependencies={['newPassword']}
                    rules={[
                      { required: true, message: '请确认新密码' },
                      ({ getFieldValue }) => ({
                        validator(_, value) {
                          if (!value || getFieldValue('newPassword') === value) {
                            return Promise.resolve();
                          }
                          return Promise.reject(new Error('两次输入的密码不一致'));
                        },
                      }),
                    ]}
                  >
                    <Input.Password prefix={<LockOutlined />} placeholder="确认新密码" />
                  </Form.Item>

                  <Form.Item>
                    <Button type="primary" htmlType="submit" loading={saving}>
                      修改密码
                    </Button>
                  </Form.Item>
                </Form>
              </TabPane>

              <TabPane
                tab={
                  <span>
                    <LinkOutlined />
                    第三方账号
                  </span>
                }
                key="third-party"
              >
                <div style={{ marginBottom: 24 }}>
                  <Alert
                    message="第三方账号绑定"
                    description="绑定第三方账号后，您可以使用这些账号直接登录系统，无需输入用户名和密码。"
                    type="info"
                    showIcon
                  />
                </div>

                <List
                  itemLayout="horizontal"
                  dataSource={[
                    { key: 'wechat', name: '微信', icon: <WechatOutlined style={{ fontSize: 24, color: '#07C160' }} /> },
                    { key: 'dingtalk', name: '钉钉', icon: <DingdingOutlined style={{ fontSize: 24, color: '#1890ff' }} /> },
                    { key: 'wecom', name: '企业微信', icon: <WechatOutlined style={{ fontSize: 24, color: '#2A8CE5' }} /> },
                    { key: 'feishu', name: '飞书', icon: <LinkOutlined style={{ fontSize: 24, color: '#3370ff' }} /> }
                  ]}
                  renderItem={item => {
                    // 查找是否已绑定
                    const boundAccount = thirdPartyAccounts.find(account => account.platform === item.key);

                    return (
                      <List.Item
                        actions={[
                          boundAccount ? (
                            <Popconfirm
                              title="确定要解绑此账号吗？"
                              onConfirm={() => handleUnbindThirdParty(boundAccount.id, item.key)}
                              okText="确定"
                              cancelText="取消"
                            >
                              <Button
                                danger
                                loading={saving}
                              >
                                解绑
                              </Button>
                            </Popconfirm>
                          ) : (
                            <Button
                              type="primary"
                              onClick={() => handleBindThirdParty(item.key)}
                            >
                              绑定
                            </Button>
                          )
                        ]}
                      >
                        <List.Item.Meta
                          avatar={item.icon}
                          title={item.name}
                          description={
                            boundAccount ?
                            `已绑定账号: ${boundAccount.platform_username || '未知用户名'}` :
                            `未绑定${item.name}账号`
                          }
                        />
                      </List.Item>
                    );
                  }}
                />
              </TabPane>

              <TabPane
                tab={
                  <span>
                    <SecurityScanOutlined />
                    安全设置
                  </span>
                }
                key="security"
              >
                <List
                  itemLayout="horizontal"
                  dataSource={[
                    {
                      title: '登录设备管理',
                      description: '查看并管理已登录的设备',
                      actions: [<Button onClick={handleViewLoginDevices}>查看设备</Button>]
                    },
                    {
                      title: '登录历史',
                      description: '查看账号的登录历史记录',
                      actions: [<Button onClick={handleViewLoginHistory}>查看历史</Button>]
                    },
                    {
                      title: '账号锁定',
                      description: '临时锁定账号，防止未授权访问',
                      actions: [<Button danger onClick={handleLockAccount}>锁定账号</Button>]
                    }
                  ]}
                  renderItem={item => (
                    <List.Item actions={item.actions}>
                      <List.Item.Meta
                        title={item.title}
                        description={item.description}
                      />
                    </List.Item>
                  )}
                />
              </TabPane>

              <TabPane
                tab={
                  <span>
                    <UserOutlined />
                    活动记录
                  </span>
                }
                key="activity"
              >
                <List
                  itemLayout="horizontal"
                  dataSource={activities}
                  renderItem={item => (
                    <List.Item>
                      <List.Item.Meta
                        avatar={
                          <Avatar icon={getActivityIcon(item.type)} style={{ backgroundColor: '#1890ff' }} />
                        }
                        title={item.description}
                        description={formatDateTime(item.created_at)}
                      />
                    </List.Item>
                  )}
                />
              </TabPane>
            </Tabs>
          </Card>
        </Col>
      </Row>

      {/* 安全设置模态框 */}
      <Modal
        title={
          securityModalType === 'login_history' ? '登录历史' :
          securityModalType === 'login_devices' ? '登录设备管理' : '安全设置'
        }
        visible={securityModalVisible}
        onCancel={() => setSecurityModalVisible(false)}
        footer={null}
        width={800}
      >
        {securityLoading ? (
          <div style={{ textAlign: 'center', padding: '50px' }}>
            <Spin size="large" tip="加载中..." />
          </div>
        ) : (
          <>
            {securityModalType === 'login_history' && (
              <Table
                dataSource={loginHistory}
                columns={[
                  {
                    title: '登录时间',
                    dataIndex: 'login_time',
                    key: 'login_time',
                    render: (text) => formatDateTime(text)
                  },
                  {
                    title: '登录类型',
                    dataIndex: 'login_type',
                    key: 'login_type',
                    render: (type) => {
                      const typeMap = {
                        login: '账密登录',
                        third_party_login: '第三方登录',
                        logout: '退出登录'
                      };
                      return <Tag color={type === 'logout' ? 'red' : 'blue'}>{typeMap[type] || type}</Tag>;
                    }
                  },
                  {
                    title: 'IP地址',
                    dataIndex: 'ip_address',
                    key: 'ip_address'
                  },
                  {
                    title: '设备信息',
                    dataIndex: 'device_info',
                    key: 'device_info',
                    ellipsis: true
                  },
                  {
                    title: '地区',
                    dataIndex: 'location',
                    key: 'location'
                  }
                ]}
                rowKey="id"
                pagination={{ pageSize: 10 }}
              />
            )}

            {securityModalType === 'login_devices' && (
              <Table
                dataSource={loginDevices}
                columns={[
                  {
                    title: '设备名称',
                    dataIndex: 'device_name',
                    key: 'device_name'
                  },
                  {
                    title: '设备类型',
                    dataIndex: 'device_type',
                    key: 'device_type',
                    render: (type) => <Tag>{type}</Tag>
                  },
                  {
                    title: 'IP地址',
                    dataIndex: 'ip_address',
                    key: 'ip_address'
                  },
                  {
                    title: '最后登录',
                    dataIndex: 'last_login',
                    key: 'last_login',
                    render: (text) => formatDateTime(text)
                  },
                  {
                    title: '状态',
                    dataIndex: 'status',
                    key: 'status',
                    render: (status) => (
                      <Tag color={status === '在线' ? 'green' : 'default'}>{status}</Tag>
                    )
                  },
                  {
                    title: '操作',
                    key: 'action',
                    render: (_, record) => (
                      <Button
                        size="small"
                        danger
                        onClick={() => handleLogoutDevice(record.id)}
                        disabled={record.is_current}
                      >
                        {record.is_current ? '当前设备' : '注销'}
                      </Button>
                    )
                  }
                ]}
                rowKey="id"
                pagination={{ pageSize: 10 }}
              />
            )}
          </>
        )}
      </Modal>
    </div>
  );
};

export default UserProfile;
