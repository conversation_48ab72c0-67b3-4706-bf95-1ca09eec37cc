import os
import uuid
import shutil
import logging
import mimetypes
from typing import Dict, Any, Optional, List, Tuple
from fastapi import UploadFile, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import and_, or_, func, desc, cast, String
from datetime import datetime, timedelta, timezone
import aiofiles
from pathlib import Path
import secrets
import hashlib
from PIL import Image
import io

from core.config import settings
from models.storage import StorageFile, StorageFolder, StorageQuota, FileShare
from models.space_settings import SpaceSettings, ModuleSpaceSettings
from models.user import User
from models.project import Project
from models.tenant import Tenant

# 配置日志
logger = logging.getLogger(__name__)

# 文件上传配置
UPLOAD_DIR = Path(settings.UPLOAD_DIR)
MAX_FILE_SIZE = settings.MAX_FILE_SIZE  # 默认10MB
THUMBNAIL_SIZE = (200, 200)  # 缩略图尺寸
ALLOWED_IMAGE_TYPES = ["image/jpeg", "image/png", "image/gif", "image/webp", "image/svg+xml"]
ALLOWED_DOCUMENT_TYPES = [
    "application/pdf",
    "application/msword",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    "application/vnd.ms-excel",
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    "application/vnd.ms-powerpoint",
    "application/vnd.openxmlformats-officedocument.presentationml.presentation",
    "text/plain",
    "text/csv",
    "text/markdown"
]

class StorageService:
    """项目空间存储服务"""

    @staticmethod
    async def get_file_category(mime_type: str) -> str:
        """根据MIME类型确定文件类别"""
        if mime_type in ALLOWED_IMAGE_TYPES:
            return "image"
        elif mime_type in ALLOWED_DOCUMENT_TYPES:
            return "document"
        else:
            return "other"

    @staticmethod
    async def get_file_extension(filename: str) -> str:
        """获取文件扩展名"""
        return os.path.splitext(filename)[1].lower().lstrip(".")

    @staticmethod
    async def is_preview_supported(mime_type: str) -> bool:
        """检查文件是否支持预览"""
        if mime_type in ALLOWED_IMAGE_TYPES:
            return True
        elif mime_type == "application/pdf":
            return True
        elif mime_type in ["text/plain", "text/csv", "text/markdown"]:
            return True
        return False

    @staticmethod
    async def generate_thumbnail(file_path: Path, mime_type: str) -> Optional[Path]:
        """生成缩略图"""
        try:
            if mime_type not in ALLOWED_IMAGE_TYPES:
                return None

            # 创建缩略图目录
            thumbnail_dir = UPLOAD_DIR / "thumbnails"
            thumbnail_dir.mkdir(parents=True, exist_ok=True)

            # 生成缩略图文件名
            thumbnail_name = f"{file_path.stem}_thumb{file_path.suffix}"
            thumbnail_path = thumbnail_dir / thumbnail_name

            # 生成缩略图
            with Image.open(file_path) as img:
                img.thumbnail(THUMBNAIL_SIZE)
                img.save(thumbnail_path)

            return thumbnail_path
        except Exception as e:
            logger.error(f"生成缩略图失败: {e}")
            return None

    @staticmethod
    async def check_space_settings(
        db: AsyncSession,
        project_id: uuid.UUID,
        file_size: int,
        file_extension: str,
        module_code: Optional[str] = None
    ) -> Tuple[bool, str]:
        """检查空间设置约束"""
        try:
            # 获取项目空间设置
            space_settings_query = select(SpaceSettings).where(
                SpaceSettings.project_id == project_id
            )
            result = await db.execute(space_settings_query)
            space_settings = result.scalar_one_or_none()

            # 如果没有空间设置，返回默认限制
            if not space_settings:
                if file_size > MAX_FILE_SIZE:
                    return False, f"文件大小不能超过{MAX_FILE_SIZE/1024/1024}MB"
                return True, ""

            # 检查模块设置（如果提供了模块代码）
            if module_code:
                module_query = select(ModuleSpaceSettings).where(
                    ModuleSpaceSettings.space_settings_id == space_settings.id,
                    ModuleSpaceSettings.module_code == module_code
                )
                result = await db.execute(module_query)
                module_settings = result.scalar_one_or_none()

                if module_settings:
                    # 使用模块设置
                    max_file_size = module_settings.max_file_size or space_settings.max_file_size
                    allowed_extensions = module_settings.allowed_extensions or space_settings.allowed_extensions
                else:
                    # 使用全局设置
                    max_file_size = space_settings.max_file_size
                    allowed_extensions = space_settings.allowed_extensions
            else:
                # 使用全局设置
                max_file_size = space_settings.max_file_size
                allowed_extensions = space_settings.allowed_extensions

            # 检查文件大小
            if file_size > max_file_size:
                return False, f"文件大小不能超过{max_file_size/1024/1024}MB"

            # 检查文件扩展名
            if allowed_extensions and file_extension.lower() not in [ext.lower() for ext in allowed_extensions]:
                return False, f"不支持的文件类型: {file_extension}"

            return True, ""

        except Exception as e:
            logger.error(f"检查空间设置失败: {e}")
            return False, f"检查空间设置失败: {str(e)}"

    @staticmethod
    async def check_quota(
        db: AsyncSession,
        project_id: uuid.UUID,
        user_id: uuid.UUID,
        file_size: int
    ) -> Tuple[bool, str]:
        """检查存储配额"""
        try:
            # 检查项目配额
            project_quota_query = select(StorageQuota).where(
                StorageQuota.project_id == project_id,
                StorageQuota.user_id.is_(None)
            )
            result = await db.execute(project_quota_query)
            project_quota = result.scalar_one_or_none()

            if project_quota:
                if not project_quota.has_sufficient_space(file_size):
                    return False, "项目存储空间不足，请联系管理员"

            # 检查用户配额（从项目级存储设置获取限制）
            space_settings_query = select(SpaceSettings).where(
                SpaceSettings.project_id == project_id
            )
            result = await db.execute(space_settings_query)
            space_settings = result.scalar_one_or_none()

            if space_settings and space_settings.max_storage_per_user:
                # 检查用户已使用的存储空间
                user_used_query = select(func.sum(StorageFile.size)).where(
                    StorageFile.project_id == project_id,
                    StorageFile.uploaded_by == user_id,
                    StorageFile.status == "active"
                )
                result = await db.execute(user_used_query)
                user_used = result.scalar_one() or 0

                if (user_used + file_size) > space_settings.max_storage_per_user:
                    return False, f"您的存储空间不足，已使用{user_used/1024/1024:.2f}MB，限制为{space_settings.max_storage_per_user/1024/1024:.2f}MB"

            # 检查项目总存储空间限制
            if space_settings and space_settings.max_storage_per_project:
                # 检查项目已使用的存储空间
                project_used_query = select(func.sum(StorageFile.size)).where(
                    StorageFile.project_id == project_id,
                    StorageFile.status == "active"
                )
                result = await db.execute(project_used_query)
                project_used = result.scalar_one() or 0

                if (project_used + file_size) > space_settings.max_storage_per_project:
                    return False, f"项目存储空间不足，已使用{project_used/1024/1024/1024:.2f}GB，限制为{space_settings.max_storage_per_project/1024/1024/1024:.2f}GB"

            return True, ""
        except Exception as e:
            logger.error(f"检查存储配额失败: {e}")
            return False, f"检查存储配额失败: {str(e)}"

    @staticmethod
    async def update_quota(
        db: AsyncSession,
        project_id: uuid.UUID,
        user_id: uuid.UUID,
        file_size: int,
        category: str,
        operation: str = "add"
    ) -> bool:
        """更新存储配额"""
        try:
            # 更新项目配额
            project_quota_query = select(StorageQuota).where(
                StorageQuota.project_id == project_id,
                StorageQuota.user_id.is_(None)
            )
            result = await db.execute(project_quota_query)
            project_quota = result.scalar_one_or_none()

            if project_quota:
                project_quota.update_category_usage(category, file_size, operation)

            # 更新用户配额
            user_quota_query = select(StorageQuota).where(
                StorageQuota.project_id == project_id,
                StorageQuota.user_id == user_id
            )
            result = await db.execute(user_quota_query)
            user_quota = result.scalar_one_or_none()

            if user_quota:
                user_quota.update_category_usage(category, file_size, operation)

            await db.commit()
            return True
        except Exception as e:
            logger.error(f"更新存储配额失败: {e}")
            await db.rollback()
            return False

    @staticmethod
    async def ensure_folder_exists(
        db: AsyncSession,
        project_id: uuid.UUID,
        tenant_id: Optional[uuid.UUID],
        user_id: uuid.UUID,
        folder_path: str
    ) -> Optional[uuid.UUID]:
        """确保文件夹存在，如果不存在则创建"""
        try:
            # 规范化路径
            folder_path = folder_path.strip("/")
            if not folder_path:
                # 根目录
                root_folder_query = select(StorageFolder).where(
                    StorageFolder.project_id == project_id,
                    StorageFolder.parent_id.is_(None),
                    StorageFolder.name == "root"
                )
                result = await db.execute(root_folder_query)
                root_folder = result.scalar_one_or_none()

                if not root_folder:
                    # 创建根目录
                    root_folder = StorageFolder(
                        id=uuid.uuid4(),
                        tenant_id=tenant_id,
                        project_id=project_id,
                        parent_id=None,
                        name="root",
                        path="/",
                        created_by=user_id
                    )
                    db.add(root_folder)
                    await db.commit()

                return root_folder.id

            # 分割路径
            parts = folder_path.split("/")
            current_path = ""
            parent_id = None

            # 查找或创建每一级目录
            for i, part in enumerate(parts):
                current_path += f"/{part}" if i > 0 else part

                folder_query = select(StorageFolder).where(
                    StorageFolder.project_id == project_id,
                    StorageFolder.parent_id == parent_id,
                    StorageFolder.name == part
                )
                result = await db.execute(folder_query)
                folder = result.scalar_one_or_none()

                if not folder:
                    # 创建文件夹
                    folder = StorageFolder(
                        id=uuid.uuid4(),
                        tenant_id=tenant_id,
                        project_id=project_id,
                        parent_id=parent_id,
                        name=part,
                        path=current_path,
                        created_by=user_id
                    )
                    db.add(folder)
                    await db.commit()

                parent_id = folder.id

            return parent_id
        except Exception as e:
            logger.error(f"确保文件夹存在失败: {e}")
            await db.rollback()
            return None

    @staticmethod
    async def upload_file(
        db: AsyncSession,
        file: UploadFile,
        project_id: uuid.UUID,
        tenant_id: Optional[uuid.UUID],
        user_id: uuid.UUID,
        folder_path: str = "/",
        description: Optional[str] = None,
        is_public: bool = False,
        module_code: Optional[str] = None
    ) -> Dict[str, Any]:
        """上传文件到项目空间"""
        try:
            # 读取文件内容
            file_content = await file.read()
            file_size = len(file_content)

            # 重置文件位置
            await file.seek(0)

            # 获取文件信息
            original_filename = file.filename
            mime_type = file.content_type or mimetypes.guess_type(original_filename)[0] or "application/octet-stream"
            extension = await StorageService.get_file_extension(original_filename)
            category = await StorageService.get_file_category(mime_type)
            preview_available = await StorageService.is_preview_supported(mime_type)

            # 检查空间设置约束
            space_valid, space_message = await StorageService.check_space_settings(
                db, project_id, file_size, extension, module_code
            )
            if not space_valid:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=space_message
                )

            # 检查存储配额
            has_quota, message = await StorageService.check_quota(db, project_id, user_id, file_size)
            if not has_quota:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=message
                )

            # 确保文件夹存在
            folder_id = await StorageService.ensure_folder_exists(db, project_id, tenant_id, user_id, folder_path)
            if not folder_id:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="创建文件夹失败"
                )

            # 构建文件存储路径
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            safe_filename = f"{timestamp}_{uuid.uuid4().hex[:8]}_{original_filename}"

            # 创建项目存储目录
            project_dir = UPLOAD_DIR / str(project_id)
            project_dir.mkdir(parents=True, exist_ok=True)

            # 构建文件路径
            file_path = project_dir / safe_filename
            storage_path = f"/{project_id}/{safe_filename}"

            # 保存文件
            async with aiofiles.open(file_path, "wb") as f:
                await f.write(file_content)

            # 生成缩略图
            thumbnail_path = None
            if category == "image":
                thumbnail_path = await StorageService.generate_thumbnail(file_path, mime_type)
                thumbnail_storage_path = f"/thumbnails/{file_path.stem}_thumb{file_path.suffix}" if thumbnail_path else None
            else:
                thumbnail_storage_path = None

            # 构建访问URL
            access_url = f"/api/v1/project/{project_id}/space/files/{safe_filename}" if is_public else None

            # 创建文件记录
            file_id = uuid.uuid4()
            storage_file = StorageFile(
                id=file_id,
                tenant_id=tenant_id,
                project_id=project_id,
                folder_id=folder_id,
                name=original_filename,
                original_name=original_filename,
                path=str(file_path),
                mime_type=mime_type,
                size=file_size,
                extension=extension,
                storage_provider="local",
                storage_path=storage_path,
                is_public=is_public,
                access_url=access_url,
                uploaded_by=user_id,
                status="active",
                file_metadata={
                    "description": description,
                    "category": category
                },
                thumbnail_path=thumbnail_storage_path,
                preview_available=preview_available
            )

            db.add(storage_file)

            # 更新存储配额
            await StorageService.update_quota(db, project_id, user_id, file_size, category, "add")

            await db.commit()

            # 构建响应数据
            file_data = {
                "id": str(file_id),
                "name": original_filename,
                "size": file_size,
                "mime_type": mime_type,
                "extension": extension,
                "category": category,
                "preview_available": preview_available,
                "thumbnail_url": f"/api/v1/project/{project_id}/space/files/{file_id}/thumbnail" if thumbnail_storage_path else None,
                "download_url": f"/api/v1/project/{project_id}/space/files/{file_id}/download",
                "created_at": datetime.now().isoformat()
            }

            return {
                "success": True,
                "message": "文件上传成功",
                "data": file_data
            }
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"文件上传失败: {e}")
            await db.rollback()
            # 如果文件已保存，但后续处理失败，则删除文件
            if 'file_path' in locals() and os.path.exists(file_path):
                os.remove(file_path)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"文件上传失败: {str(e)}"
            )

    @staticmethod
    async def get_file(
        db: AsyncSession,
        file_id: uuid.UUID,
        project_id: Optional[uuid.UUID] = None
    ) -> Optional[StorageFile]:
        """获取文件信息"""
        try:
            # 构建查询条件
            conditions = [
                StorageFile.id == file_id,
                StorageFile.status == "active"
            ]

            # 如果提供了项目ID，则添加项目ID条件
            if project_id:
                conditions.append(StorageFile.project_id == project_id)

            query = select(StorageFile).where(and_(*conditions))
            result = await db.execute(query)
            file = result.scalar_one_or_none()

            if file:
                logger.info(f"获取文件成功: file_id={file_id}, path={file.path}")
            else:
                logger.warning(f"文件不存在: file_id={file_id}")

            return file
        except Exception as e:
            logger.error(f"获取文件信息失败: {e}")
            return None

    @staticmethod
    async def get_file_by_id(
        db: AsyncSession,
        file_id: uuid.UUID,
        project_id: uuid.UUID
    ) -> Optional[Dict[str, Any]]:
        """根据文件ID获取文件详细信息（包含文件路径等信息）"""
        try:
            file = await StorageService.get_file(db, file_id, project_id)
            if not file:
                logger.warning(f"文件不存在: file_id={file_id}, project_id={project_id}")
                return None

            # 检查文件物理路径是否存在
            file_path = Path(file.path)
            if not file_path.exists():
                logger.error(f"文件物理路径不存在: {file_path}")
                return None

            return {
                "id": str(file.id),
                "name": file.name,
                "original_name": file.original_name,
                "storage_path": str(file_path),
                "size": file.size,
                "mime_type": file.mime_type,
                "extension": file.extension,
                "project_id": str(file.project_id),
                "folder_id": str(file.folder_id) if file.folder_id else None,
                "is_public": file.is_public,
                "preview_available": file.preview_available,
                "thumbnail_path": file.thumbnail_path,
                "created_at": file.created_at.isoformat() if file.created_at else None,
                "updated_at": file.updated_at.isoformat() if file.updated_at else None
            }
        except Exception as e:
            logger.error(f"根据ID获取文件信息失败: {e}")
            return None

    @staticmethod
    async def download_file(
        db: AsyncSession,
        file_id: uuid.UUID,
        project_id: Optional[uuid.UUID] = None
    ) -> Optional[Dict[str, Any]]:
        """下载文件"""
        try:
            file = await StorageService.get_file(db, file_id, project_id)
            if not file:
                logger.error(f"文件不存在，无法下载: file_id={file_id}")
                return None

            file_path = Path(file.path)
            logger.info(f"文件路径: {file_path}")

            if not file_path.exists():
                logger.error(f"文件物理路径不存在: {file_path}")
                return None

            return {
                "file_path": file_path,
                "file_name": file.original_name,
                "mime_type": file.mime_type,
                "size": file.size
            }
        except Exception as e:
            logger.error(f"下载文件失败: {e}")
            return None

    @staticmethod
    async def get_thumbnail(
        db: AsyncSession,
        file_id: uuid.UUID,
        project_id: Optional[uuid.UUID] = None
    ) -> Optional[Dict[str, Any]]:
        """获取文件缩略图"""
        try:
            file = await StorageService.get_file(db, file_id, project_id)
            if not file:
                logger.error(f"文件不存在，无法获取缩略图: file_id={file_id}")
                return None

            if not file.thumbnail_path:
                logger.error(f"文件没有缩略图: file_id={file_id}")
                return None

            # 确保路径格式正确
            thumbnail_path = UPLOAD_DIR / file.thumbnail_path.lstrip("/")
            logger.info(f"缩略图路径: {thumbnail_path}")

            if not thumbnail_path.exists():
                logger.error(f"缩略图文件不存在: {thumbnail_path}")
                return None

            return {
                "file_path": thumbnail_path,
                "file_name": f"thumbnail_{file.original_name}",
                "mime_type": "image/jpeg",  # 缩略图通常是JPEG格式
                "size": thumbnail_path.stat().st_size
            }
        except Exception as e:
            logger.error(f"获取缩略图失败: {e}")
            return None

    @staticmethod
    async def list_files(
        db: AsyncSession,
        project_id: uuid.UUID,
        folder_id: Optional[uuid.UUID] = None,
        folder_path: Optional[str] = None,
        page: int = 1,
        page_size: int = 20,
        search: Optional[str] = None,
        file_type: Optional[str] = None,
        sort_by: str = "created_at",
        sort_order: str = "desc"
    ) -> Dict[str, Any]:
        """列出文件夹中的文件"""
        try:
            # 构建查询条件
            conditions = [
                StorageFile.project_id == project_id,
                StorageFile.status == "active"
            ]

            # 如果指定了文件夹ID，则查询该文件夹下的文件
            if folder_id:
                conditions.append(StorageFile.folder_id == folder_id)

            # 如果指定了文件夹路径，则先查询文件夹ID
            elif folder_path is not None:
                folder_id = await StorageService.ensure_folder_exists(
                    db, project_id, None, None, folder_path
                )
                if folder_id:
                    conditions.append(StorageFile.folder_id == folder_id)

            # 如果指定了搜索关键词，则搜索文件名
            if search:
                conditions.append(StorageFile.name.ilike(f"%{search}%"))

            # 如果指定了文件类型，则过滤文件类型
            if file_type:
                if file_type == "image":
                    conditions.append(StorageFile.mime_type.in_(ALLOWED_IMAGE_TYPES))
                elif file_type == "document":
                    conditions.append(StorageFile.mime_type.in_(ALLOWED_DOCUMENT_TYPES))
                elif file_type == "other":
                    conditions.append(
                        and_(
                            ~StorageFile.mime_type.in_(ALLOWED_IMAGE_TYPES),
                            ~StorageFile.mime_type.in_(ALLOWED_DOCUMENT_TYPES)
                        )
                    )

            # 构建排序条件
            if sort_by == "name":
                order_by = StorageFile.name.asc() if sort_order == "asc" else StorageFile.name.desc()
            elif sort_by == "size":
                order_by = StorageFile.size.asc() if sort_order == "asc" else StorageFile.size.desc()
            else:  # created_at
                order_by = StorageFile.created_at.asc() if sort_order == "asc" else StorageFile.created_at.desc()

            # 计算分页参数
            offset = (page - 1) * page_size

            # 查询文件总数
            count_query = select(func.count()).select_from(StorageFile).where(and_(*conditions))
            result = await db.execute(count_query)
            total_count = result.scalar_one()

            # 查询文件列表
            query = (
                select(
                    StorageFile,
                    User.full_name.label("uploader_name")
                )
                .outerjoin(User, StorageFile.uploaded_by == User.id)
                .where(and_(*conditions))
                .order_by(order_by)
                .offset(offset)
                .limit(page_size)
            )

            result = await db.execute(query)
            files = result.all()

            # 构建文件列表
            file_list = []
            for file, uploader_name in files:
                file_list.append({
                    "id": str(file.id),
                    "name": file.name,
                    "original_name": file.original_name,
                    "size": file.size,
                    "mime_type": file.mime_type,
                    "extension": file.extension,
                    "is_public": file.is_public,
                    "preview_available": file.preview_available,
                    "thumbnail_url": f"/api/v1/project/{project_id}/space/files/{file.id}/thumbnail" if file.thumbnail_path else None,
                    "download_url": f"/api/v1/project/{project_id}/space/files/{file.id}/download",
                    "uploaded_by": str(file.uploaded_by),
                    "uploader_name": uploader_name,
                    "created_at": file.created_at.isoformat() if file.created_at else None,
                    "updated_at": file.updated_at.isoformat() if file.updated_at else None
                })

            # 查询当前文件夹信息
            current_folder = None
            if folder_id:
                folder_query = select(StorageFolder).where(
                    StorageFolder.id == folder_id,
                    StorageFolder.project_id == project_id
                )
                result = await db.execute(folder_query)
                folder = result.scalar_one_or_none()
                if folder:
                    current_folder = {
                        "id": str(folder.id),
                        "name": folder.name,
                        "path": folder.path,
                        "parent_id": str(folder.parent_id) if folder.parent_id else None
                    }

            # 查询子文件夹
            subfolders_query = select(StorageFolder).where(
                StorageFolder.project_id == project_id,
                StorageFolder.parent_id == folder_id,
                StorageFolder.status == "active"
            ).order_by(StorageFolder.name)

            result = await db.execute(subfolders_query)
            subfolders = result.scalars().all()

            # 构建子文件夹列表
            subfolder_list = []
            for folder in subfolders:
                subfolder_list.append({
                    "id": str(folder.id),
                    "name": folder.name,
                    "path": folder.path,
                    "parent_id": str(folder.parent_id) if folder.parent_id else None,
                    "created_by": str(folder.created_by),
                    "created_at": folder.created_at.isoformat() if folder.created_at else None
                })

            # 构建响应数据
            return {
                "success": True,
                "data": {
                    "files": file_list,
                    "folders": subfolder_list,
                    "current_folder": current_folder,
                    "pagination": {
                        "page": page,
                        "page_size": page_size,
                        "total": total_count,
                        "pages": (total_count + page_size - 1) // page_size
                    }
                }
            }
        except Exception as e:
            logger.error(f"列出文件失败: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"列出文件失败: {str(e)}"
            )

    @staticmethod
    async def create_folder(
        db: AsyncSession,
        project_id: uuid.UUID,
        tenant_id: Optional[uuid.UUID],
        user_id: uuid.UUID,
        folder_name: str,
        parent_id: Optional[uuid.UUID] = None
    ) -> Dict[str, Any]:
        """创建文件夹"""
        try:
            # 检查父文件夹是否存在
            if parent_id:
                parent_query = select(StorageFolder).where(
                    StorageFolder.id == parent_id,
                    StorageFolder.project_id == project_id,
                    StorageFolder.status == "active"
                )
                result = await db.execute(parent_query)
                parent_folder = result.scalar_one_or_none()

                if not parent_folder:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail="父文件夹不存在"
                    )

                # 构建文件夹路径
                folder_path = f"{parent_folder.path}/{folder_name}" if parent_folder.path != "/" else f"/{folder_name}"
            else:
                # 根目录
                folder_path = f"/{folder_name}"

            # 检查同名文件夹是否已存在
            existing_query = select(StorageFolder).where(
                StorageFolder.project_id == project_id,
                StorageFolder.parent_id == parent_id,
                StorageFolder.name == folder_name,
                StorageFolder.status == "active"
            )
            result = await db.execute(existing_query)
            existing_folder = result.scalar_one_or_none()

            if existing_folder:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="同名文件夹已存在"
                )

            # 创建文件夹
            folder_id = uuid.uuid4()
            folder = StorageFolder(
                id=folder_id,
                tenant_id=tenant_id,
                project_id=project_id,
                parent_id=parent_id,
                name=folder_name,
                path=folder_path,
                created_by=user_id,
                status="active"
            )

            db.add(folder)
            await db.commit()

            return {
                "success": True,
                "message": "文件夹创建成功",
                "data": {
                    "id": str(folder_id),
                    "name": folder_name,
                    "path": folder_path,
                    "parent_id": str(parent_id) if parent_id else None,
                    "created_at": folder.created_at.isoformat() if folder.created_at else None
                }
            }
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"创建文件夹失败: {e}")
            await db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"创建文件夹失败: {str(e)}"
            )

    @staticmethod
    async def delete_file(
        db: AsyncSession,
        file_id: uuid.UUID,
        project_id: uuid.UUID,
        user_id: uuid.UUID,
        permanent: bool = False
    ) -> Dict[str, Any]:
        """删除文件"""
        try:
            # 查询文件 - 如果是永久删除，允许删除已标记为deleted的文件
            if permanent:
                file_query = select(StorageFile).where(
                    StorageFile.id == file_id,
                    StorageFile.project_id == project_id,
                    StorageFile.status == "deleted"
                )
            else:
                file_query = select(StorageFile).where(
                    StorageFile.id == file_id,
                    StorageFile.project_id == project_id,
                    StorageFile.status == "active"
                )
            
            result = await db.execute(file_query)
            file = result.scalar_one_or_none()

            if not file:
                if permanent:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail="文件不存在或已被彻底删除"
                    )
                else:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail="文件不存在"
                    )

            # 获取文件类别
            category = file.file_metadata.get("category", "other") if file.file_metadata else "other"

            if permanent:
                # 永久删除文件
                file_path = Path(file.path)
                if file_path.exists():
                    os.remove(file_path)

                # 删除缩略图
                if file.thumbnail_path:
                    thumbnail_path = Path(file.thumbnail_path)
                    if thumbnail_path.exists():
                        os.remove(thumbnail_path)

                # 永久删除时不需要更新配额，因为软删除时已经减过了

                # 删除文件记录
                await db.delete(file)
            else:
                # 软删除文件
                file.status = "deleted"
                file.deleted_at = datetime.now()

                # 即使是软删除，也应该更新存储配额，因为软删除的文件不应计入使用空间
                await StorageService.update_quota(db, project_id, file.uploaded_by, file.size, category, "subtract")

            await db.commit()

            return {
                "success": True,
                "message": "文件删除成功",
                "data": {
                    "id": str(file_id),
                    "permanent": permanent
                }
            }
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"删除文件失败: {e}")
            await db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"删除文件失败: {str(e)}"
            )

    @staticmethod
    async def create_file_share(
        db: AsyncSession,
        file_id: uuid.UUID,
        project_id: uuid.UUID,
        user_id: uuid.UUID,
        expires_in_days: Optional[int] = None,
        password: Optional[str] = None
    ) -> Dict[str, Any]:
        """创建文件分享链接"""
        try:
            # 查询文件
            file_query = select(StorageFile).where(
                StorageFile.id == file_id,
                StorageFile.project_id == project_id,
                StorageFile.status == "active"
            )
            result = await db.execute(file_query)
            file = result.scalar_one_or_none()

            if not file:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="文件不存在"
                )

            # 生成分享令牌
            share_token = secrets.token_hex(16)

            # 计算过期时间
            expires_at = None
            if expires_in_days:
                # 使用带时区的日期时间，避免时区比较问题
                expires_at = datetime.now(tz=timezone.utc) + timedelta(days=expires_in_days)

            # 处理密码
            hashed_password = None
            if password:
                hashed_password = hashlib.sha256(password.encode()).hexdigest()

            # 创建分享记录
            share_id = uuid.uuid4()
            share = FileShare(
                id=share_id,
                file_id=file_id,
                share_token=share_token,
                created_by=user_id,
                expires_at=expires_at,
                password=hashed_password,
                status="active"
            )

            db.add(share)
            await db.commit()

            # 构建分享链接 - 使用正确的URL格式
            share_url = f"/space/share/{share_token}"

            return {
                "success": True,
                "message": "文件分享创建成功",
                "data": {
                    "id": str(share_id),
                    "file_id": str(file_id),
                    "file_name": file.name,
                    "share_token": share_token,
                    "share_url": share_url,
                    "expires_at": expires_at.isoformat() if expires_at else None,
                    "has_password": password is not None,
                    "created_at": share.created_at.isoformat() if share.created_at else None
                }
            }
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"创建文件分享失败: {e}")
            await db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"创建文件分享失败: {str(e)}"
            )

    @staticmethod
    async def get_shared_file(
        db: AsyncSession,
        share_token: str,
        password: Optional[str] = None
    ) -> Dict[str, Any]:
        """获取分享文件信息"""
        try:
            # 查询分享记录
            share_query = select(FileShare).where(
                FileShare.share_token == share_token,
                FileShare.status == "active"
            )
            result = await db.execute(share_query)
            share = result.scalar_one_or_none()

            if not share:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="分享链接不存在或已失效"
                )

            # 检查是否过期
            if share.expires_at and share.expires_at < datetime.now(share.expires_at.tzinfo):
                share.status = "expired"
                await db.commit()
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="分享链接已过期"
                )

            # 检查密码
            if share.password:
                if not password:
                    return {
                        "success": False,
                        "message": "此分享需要密码",
                        "data": {
                            "requires_password": True,
                            "file_id": None,
                            "file_info": None
                        }
                    }

                hashed_input = hashlib.sha256(password.encode()).hexdigest()
                if hashed_input != share.password:
                    raise HTTPException(
                        status_code=status.HTTP_403_FORBIDDEN,
                        detail="密码错误"
                    )

            # 查询文件信息
            file_query = select(StorageFile).where(
                StorageFile.id == share.file_id,
                StorageFile.status == "active"
            )
            result = await db.execute(file_query)
            file = result.scalar_one_or_none()

            if not file:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="文件不存在或已删除"
                )

            # 更新访问计数和最后访问时间
            share.access_count += 1
            share.last_accessed_at = datetime.now(tz=timezone.utc)
            await db.commit()

            # 构建文件信息
            file_info = {
                "id": str(file.id),
                "name": file.name,
                "original_name": file.original_name,
                "size": file.size,
                "mime_type": file.mime_type,
                "extension": file.extension,
                "preview_available": file.preview_available,
                "thumbnail_url": f"/api/v1/space/share/{share_token}/thumbnail" if file.thumbnail_path else None,
                "download_url": f"/api/v1/space/share/{share_token}/download",
                "created_at": file.created_at.isoformat() if file.created_at else None
            }

            return {
                "success": True,
                "message": "获取分享文件成功",
                "data": {
                    "requires_password": False,
                    "file_id": str(file.id),
                    "file_info": file_info,
                    "share_info": {
                        "created_by": str(share.created_by),
                        "expires_at": share.expires_at.isoformat() if share.expires_at else None,
                        "access_count": share.access_count,
                        "last_accessed_at": share.last_accessed_at.isoformat() if share.last_accessed_at else None
                    }
                }
            }
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"获取分享文件失败: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"获取分享文件失败: {str(e)}"
            )

    @staticmethod
    async def get_storage_usage(
        db: AsyncSession,
        project_id: uuid.UUID,
        user_id: Optional[uuid.UUID] = None
    ) -> Dict[str, Any]:
        """获取存储使用情况"""
        try:
            # 查询配额信息
            if user_id:
                # 查询用户配额
                quota_query = select(StorageQuota).where(
                    StorageQuota.project_id == project_id,
                    StorageQuota.user_id == user_id
                )
            else:
                # 查询项目配额
                quota_query = select(StorageQuota).where(
                    StorageQuota.project_id == project_id,
                    StorageQuota.user_id.is_(None)
                )

            result = await db.execute(quota_query)
            quota = result.scalar_one_or_none()

            if not quota:
                # 如果配额不存在，返回默认值
                return {
                    "success": True,
                    "data": {
                        "total": 0,
                        "used": 0,
                        "available": 0,
                        "percentage": 0,
                        "by_category": {
                            "image": 0,
                            "document": 0,
                            "other": 0
                        }
                    }
                }

            # 重新计算实际使用的存储空间
            # 只计算状态为active的文件
            if user_id:
                # 按类别统计用户文件大小
                image_size_query = select(func.sum(StorageFile.size)).select_from(StorageFile).where(
                    StorageFile.project_id == project_id,
                    StorageFile.uploaded_by == user_id,
                    StorageFile.status == "active",
                    cast(StorageFile.file_metadata['category'], String) == 'image'
                )
                document_size_query = select(func.sum(StorageFile.size)).select_from(StorageFile).where(
                    StorageFile.project_id == project_id,
                    StorageFile.uploaded_by == user_id,
                    StorageFile.status == "active",
                    cast(StorageFile.file_metadata['category'], String) == 'document'
                )
                other_size_query = select(func.sum(StorageFile.size)).select_from(StorageFile).where(
                    StorageFile.project_id == project_id,
                    StorageFile.uploaded_by == user_id,
                    StorageFile.status == "active",
                    cast(StorageFile.file_metadata['category'], String) == 'other'
                )
            else:
                # 按类别统计项目文件大小
                image_size_query = select(func.sum(StorageFile.size)).select_from(StorageFile).where(
                    StorageFile.project_id == project_id,
                    StorageFile.status == "active",
                    cast(StorageFile.file_metadata['category'], String) == 'image'
                )
                document_size_query = select(func.sum(StorageFile.size)).select_from(StorageFile).where(
                    StorageFile.project_id == project_id,
                    StorageFile.status == "active",
                    cast(StorageFile.file_metadata['category'], String) == 'document'
                )
                other_size_query = select(func.sum(StorageFile.size)).select_from(StorageFile).where(
                    StorageFile.project_id == project_id,
                    StorageFile.status == "active",
                    cast(StorageFile.file_metadata['category'], String) == 'other'
                )

            # 执行查询并获取结果
            result = await db.execute(image_size_query)
            image_size = result.scalar_one() or 0

            result = await db.execute(document_size_query)
            document_size = result.scalar_one() or 0

            result = await db.execute(other_size_query)
            other_size = result.scalar_one() or 0

            # 计算总使用空间
            total_used = image_size + document_size + other_size

            # 更新配额记录以确保数据准确
            quota.image_quota = image_size
            quota.document_quota = document_size
            quota.other_quota = other_size
            quota.used_quota = total_used
            quota.last_updated = datetime.now(tz=timezone.utc)

            await db.commit()

            # 计算可用空间和使用百分比
            available = max(0, quota.total_quota - total_used)
            percentage = (total_used / quota.total_quota * 100) if quota.total_quota > 0 else 100

            # 查询文件数量
            if user_id:
                count_query = select(func.count()).select_from(StorageFile).where(
                    StorageFile.project_id == project_id,
                    StorageFile.uploaded_by == user_id,
                    StorageFile.status == "active"
                )
            else:
                count_query = select(func.count()).select_from(StorageFile).where(
                    StorageFile.project_id == project_id,
                    StorageFile.status == "active"
                )

            result = await db.execute(count_query)
            file_count = result.scalar_one()

            return {
                "success": True,
                "data": {
                    "total": quota.total_quota,
                    "used": total_used,
                    "available": available,
                    "percentage": round(percentage, 2),
                    "files_count": file_count,
                    "by_category": {
                        "image": image_size,
                        "document": document_size,
                        "other": other_size
                    }
                }
            }
        except Exception as e:
            logger.error(f"获取存储使用情况失败: {e}")
            await db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"获取存储使用情况失败: {str(e)}"
            )