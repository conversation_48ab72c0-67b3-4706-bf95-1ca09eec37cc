## 全局配置
global:
  checkNewVersion: true
  sendAnonymousUsage: false

## 核心配置 - v3.3新增，确保向后兼容
core:
  defaultRuleSyntax: v2  # 保持v2语法兼容性，可以逐步迁移到v3

## 入口点配置
entryPoints:
  web:
    address: ":80"
    # HTTP重定向到HTTPS（可选）
    # http:
    #   redirections:
    #     entrypoint:
    #       to: websecure
    #       scheme: https
  websecure:
    address: ":443"
    # 启用TLS支持
    http:
      tls: {}

## API和仪表板配置
api:
  dashboard: true
  insecure: true  # 仅在开发环境使用，生产环境建议设置为false并配置认证

## 提供方配置
providers:
  docker:
    endpoint: "unix:///var/run/docker.sock"
    exposedByDefault: false
    network: docker_retail-ai-saas-network
    # 监听Docker事件以动态更新配置
    watch: true
  file:
    directory: "/etc/traefik/dynamic"
    watch: true

## 日志配置
log:
  level: "INFO"
  format: "common"
  # 可选：输出到文件
  # filePath: "/var/log/traefik/traefik.log"

## 访问日志配置
accessLog:
  format: "common"
  # 可选：输出到文件
  # filePath: "/var/log/traefik/access.log"
  # 过滤敏感信息
  fields:
    headers:
      defaultMode: "keep"
      names:
        Authorization: "drop"
        Cookie: "drop"

## 健康检查配置
ping:
  entryPoint: "web"

## 可选：启用v3.3的新观测性功能（实验性）
# experimental:
#   otlpLogs: true

## 可选：OpenTelemetry支持（v3.3新功能）
# metrics:
#   otlp: {}
# tracing:
#   otlp: {}
# log:
#   otlp: {}
# accesslog:
#   otlp: {}
