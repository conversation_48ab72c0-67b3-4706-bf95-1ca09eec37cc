import React, { useState, useEffect } from 'react';
import { Card, Typography, Button, Table, Space, Tag, Modal, Form, Input, Select, Switch, Tabs, Spin, message, Tooltip, Popconfirm, Badge, Drawer, Descriptions, Alert, Row, Col } from 'antd';
import { PlusOutlined, ReloadOutlined, DeleteOutlined, EditOutlined, EyeOutlined, LinkOutlined, ApiOutlined, ToolOutlined, SyncOutlined, DisconnectOutlined, SearchOutlined } from '@ant-design/icons';
import ReactJson from 'react-json-view';
import { getEndpointList, createEndpoint, updateEndpoint, deleteEndpoint, syncEndpoints } from '../services/endpointService';
import { getMappingList, createMapping, updateMapping, deleteMapping } from '../services/mappingService';
import { getAvailableServers, getServerTools } from '../services/mcpService';

const { Title, Paragraph, Text } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;
const { TextArea } = Input;

/**
 * API端点和工具映射管理组件
 *
 * 合并了API端点管理和工具映射管理功能
 */
const ApiEndpointToolMappingManage = () => {
  // 端点相关状态
  const [endpointForm] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [endpoints, setEndpoints] = useState([]);
  const [endpointModalVisible, setEndpointModalVisible] = useState(false);
  const [endpointModalTitle, setEndpointModalTitle] = useState('创建API端点');
  const [editingEndpoint, setEditingEndpoint] = useState(null);
  const [detailVisible, setDetailVisible] = useState(false);
  const [detailEndpoint, setDetailEndpoint] = useState(null);
  const [syncLoading, setSyncLoading] = useState(false);

  // 映射相关状态
  const [mappingForm] = Form.useForm();
  const [mappings, setMappings] = useState([]);
  const [mappingModalVisible, setMappingModalVisible] = useState(false);
  const [mappingModalTitle, setMappingModalTitle] = useState('创建工具映射');
  const [editingMapping, setEditingMapping] = useState(null);
  const [selectedEndpoint, setSelectedEndpoint] = useState(null);

  // 分页和搜索状态
  const [pagination, setPagination] = useState({ current: 1, pageSize: 10, total: 0 });
  const [searchText, setSearchText] = useState('');

  // 服务器相关状态
  const [servers, setServers] = useState([]);
  const [serverTools, setServerTools] = useState({});
  const [toolsLoading, setToolsLoading] = useState(false);

  // 详情抽屉状态
  const [detailTab, setDetailTab] = useState('1');

  // 加载数据
  useEffect(() => {
    // 初始加载时，明确指定分页参数
    fetchEndpoints({
      page: 1,
      page_size: 10
    });
    fetchMappings({
      page: 1,
      page_size: 100 // 修改为后端允许的最大值
    });
    fetchServers();
  }, []);

  // 获取端点列表
  const fetchEndpoints = async (params = {}) => {
    setLoading(true);
    try {
      // 使用传入的参数优先，然后使用当前分页状态
      const { current, pageSize } = pagination;
      const queryParams = {
        page: params.page || current || 1,
        page_size: params.page_size || params.pageSize || pageSize || 10
      };

      // 只有当搜索关键词不为空时才添加搜索参数
      const searchValue = params.search !== undefined ? params.search : searchText;
      if (searchValue) {
        queryParams.search = searchValue;
      }

      console.log('获取端点列表参数:', queryParams);
      const response = await getEndpointList(queryParams);

      if (response.success) {
        setEndpoints(response.data || []);

        // 更新分页信息，确保使用有效的值
        const responsePage = parseInt(response.page) || queryParams.page;
        const responsePageSize = parseInt(response.page_size) || queryParams.page_size;
        const responseTotal = parseInt(response.total) || 0;

        setPagination({
          current: responsePage,
          pageSize: responsePageSize,
          total: responseTotal
        });

        console.log('端点列表分页信息:', {
          current: responsePage,
          pageSize: responsePageSize,
          total: responseTotal
        });
      } else {
        message.error('获取API端点列表失败');
      }
    } catch (error) {
      console.error('获取API端点列表失败:', error);
      message.error('获取API端点列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取映射列表
  const fetchMappings = async (params = {}) => {
    try {
      // 注意：这里不设置loading状态，因为映射列表只是辅助数据
      const { current, pageSize } = pagination;
      const queryParams = {
        page: params.page || current || 1,
        page_size: params.page_size || params.pageSize || pageSize || 10
      };

      // 只有当搜索关键词不为空时才添加搜索参数
      const searchValue = params.search !== undefined ? params.search : searchText;
      if (searchValue) {
        queryParams.search = searchValue;
      }

      console.log('获取映射列表参数:', queryParams);
      const response = await getMappingList(queryParams);

      if (response.success) {
        setMappings(response.data || []);

        // 映射列表不更新分页信息，因为分页是基于端点列表的
        console.log('映射列表获取成功，数据条数:', response.data?.length || 0);
      } else {
        message.error('获取工具映射列表失败');
      }
    } catch (error) {
      console.error('获取工具映射列表失败:', error);
      message.error('获取工具映射列表失败');
    }
  };

  // 处理表格分页、筛选、排序变化
  const handleTableChange = (paginationParams, filters, sorter) => {
    console.log('表格分页变化:', paginationParams);

    // 直接使用新的分页参数获取数据，不要先更新状态
    fetchEndpoints({
      page: paginationParams.current,
      page_size: paginationParams.pageSize,
      search: searchText
    });

    // 同时获取映射列表数据（用于显示端点的映射信息）
    fetchMappings({
      page: 1, // 映射列表总是从第一页开始获取，因为我们需要所有映射数据来显示端点的映射状态
      page_size: 100, // 修改为后端允许的最大值
      search: searchText
    });
  };

  // 处理搜索
  const handleSearch = (value) => {
    console.log('搜索关键词:', value);

    // 更新搜索文本
    setSearchText(value);

    // 搜索端点列表，重置到第一页
    fetchEndpoints({
      page: 1,
      page_size: pagination.pageSize,
      search: value
    });

    // 同时获取映射列表，用于显示端点的映射信息
    fetchMappings({
      page: 1,
      page_size: 100, // 修改为后端允许的最大值
      search: value
    });
  };

  // 获取服务器列表
  const fetchServers = async () => {
    try {
      const response = await getAvailableServers();
      if (response.success) {
        setServers(response.data);
      } else {
        message.error('获取服务器列表失败');
      }
    } catch (error) {
      console.error('获取服务器列表失败:', error);
      message.error('获取服务器列表失败');
    }
  };

  // 同步API端点
  const handleSyncEndpoints = async () => {
    setSyncLoading(true);
    try {
      const response = await syncEndpoints();
      if (response.success) {
        message.success('API端点同步成功');
        // 同步成功后，重新获取端点列表，保持当前分页状态
        fetchEndpoints({
          page: pagination.current,
          page_size: pagination.pageSize,
          search: searchText
        });
      } else {
        message.error('API端点同步失败: ' + (response.message || '未知错误'));
      }
    } catch (error) {
      console.error('API端点同步失败:', error);
      message.error('API端点同步失败: ' + (error.message || '未知错误'));
    } finally {
      setSyncLoading(false);
    }
  };

  // 打开创建端点模态框
  const showCreateEndpointModal = () => {
    setEndpointModalTitle('创建API端点');
    setEditingEndpoint(null);
    endpointForm.resetFields();
    setEndpointModalVisible(true);
  };

  // 打开编辑端点模态框
  const showEditEndpointModal = (endpoint) => {
    setEndpointModalTitle('编辑API端点');
    setEditingEndpoint(endpoint);

    // 设置表单初始值
    endpointForm.setFieldsValue({
      name: endpoint.name,
      description: endpoint.description,
      endpoint_path: endpoint.endpoint_path,
      method: endpoint.method,
      enabled: endpoint.enabled,
      parameters: endpoint.parameters ? JSON.stringify(endpoint.parameters, null, 2) : '{}',
      response_template: endpoint.response_template || ''
    });

    setEndpointModalVisible(true);
  };

  // 处理端点表单提交
  const handleEndpointSubmit = async (values) => {
    try {
      // 解析参数
      let parameters = {};
      try {
        parameters = JSON.parse(values.parameters);
      } catch (error) {
        message.error('参数格式错误，请输入有效的JSON');
        return;
      }

      // 构建端点配置
      const endpointConfig = {
        ...values,
        parameters
      };

      if (editingEndpoint) {
        // 更新端点
        const response = await updateEndpoint(editingEndpoint.id, endpointConfig);
        if (response.success) {
          message.success('API端点更新成功');
          setEndpointModalVisible(false);
          // 更新端点后，重新获取端点列表，保持当前分页状态
          fetchEndpoints({
            page: pagination.current,
            page_size: pagination.pageSize,
            search: searchText
          });
        } else {
          message.error('API端点更新失败: ' + (response.message || '未知错误'));
        }
      } else {
        // 创建端点
        const response = await createEndpoint(endpointConfig);
        if (response.success) {
          message.success('API端点创建成功');
          setEndpointModalVisible(false);
          // 创建端点后，重新获取端点列表，保持当前分页状态
          fetchEndpoints({
            page: pagination.current,
            page_size: pagination.pageSize,
            search: searchText
          });
        } else {
          message.error('API端点创建失败: ' + (response.message || '未知错误'));
        }
      }
    } catch (error) {
      console.error('提交表单失败:', error);
      message.error('提交表单失败: ' + (error.message || '未知错误'));
    }
  };

  // 处理删除端点
  const handleDeleteEndpoint = async (endpointId) => {
    try {
      const response = await deleteEndpoint(endpointId);
      if (response.success) {
        message.success('API端点删除成功');
        // 删除端点后，重新获取端点列表，保持当前分页状态
        fetchEndpoints({
          page: pagination.current,
          page_size: pagination.pageSize,
          search: searchText
        });

        // 同时获取映射列表
        fetchMappings({
          page: 1,
          page_size: 100, // 修改为后端允许的最大值
          search: searchText
        });
      } else {
        message.error('API端点删除失败: ' + (response.message || '未知错误'));
      }
    } catch (error) {
      console.error('删除API端点失败:', error);
      message.error('删除API端点删除失败: ' + (error.message || '未知错误'));
    }
  };

  // 查看端点详情
  const showEndpointDetail = (endpoint) => {
    setDetailEndpoint(endpoint);
    setDetailVisible(true);
    setDetailTab('1');

    // 查找该端点的映射
    const endpointMappings = mappings.filter(m => m.endpoint_id === endpoint.id);
    setDetailEndpoint({
      ...endpoint,
      mappings: endpointMappings
    });
  };

  // 打开创建映射模态框
  const showCreateMappingModal = (endpoint = null) => {
    setMappingModalTitle('创建工具映射');
    setEditingMapping(null);
    mappingForm.resetFields();

    if (endpoint) {
      setSelectedEndpoint(endpoint.id);
      mappingForm.setFieldsValue({
        endpoint_id: endpoint.id
      });
    } else {
      setSelectedEndpoint(null);
    }

    setMappingModalVisible(true);
  };

  // 打开编辑映射模态框
  const showEditMappingModal = (mapping) => {
    setMappingModalTitle('编辑工具映射');
    setEditingMapping(mapping);
    setSelectedEndpoint(mapping.endpoint_id);

    // 设置表单初始值
    mappingForm.setFieldsValue({
      endpoint_id: mapping.endpoint_id,
      server_id: mapping.server_id,
      tool_name: mapping.tool_name,
      tool_description: mapping.tool_description || '',
      parameter_mapping: mapping.parameter_mapping ? JSON.stringify(mapping.parameter_mapping, null, 2) : '{}',
      response_mapping: mapping.response_mapping ? JSON.stringify(mapping.response_mapping, null, 2) : '{}',
      supports_streaming: mapping.supports_streaming,
      supports_media: mapping.supports_media,
      enabled: mapping.enabled
    });

    // 加载服务器工具
    if (mapping.server_id) {
      fetchServerTools(mapping.server_id);
    }

    setMappingModalVisible(true);
  };

  // 处理映射表单提交
  const handleMappingSubmit = async (values) => {
    try {
      // 解析参数映射和响应映射
      let parameter_mapping = {};
      let response_mapping = {};

      try {
        parameter_mapping = JSON.parse(values.parameter_mapping);
      } catch (error) {
        message.error('参数映射格式错误，请输入有效的JSON');
        return;
      }

      try {
        response_mapping = JSON.parse(values.response_mapping);
      } catch (error) {
        message.error('响应映射格式错误，请输入有效的JSON');
        return;
      }

      // 构建映射配置
      const mappingConfig = {
        ...values,
        parameter_mapping,
        response_mapping
      };

      if (editingMapping) {
        // 更新映射
        const response = await updateMapping(editingMapping.id, mappingConfig);
        if (response.success) {
          message.success('工具映射更新成功');
          setMappingModalVisible(false);
          // 更新映射后，重新获取映射列表
          fetchMappings({
            page: 1,
            page_size: 100, // 修改为后端允许的最大值
            search: searchText
          });
        } else {
          message.error('工具映射更新失败: ' + (response.message || '未知错误'));
        }
      } else {
        // 创建映射
        const response = await createMapping(mappingConfig);
        if (response.success) {
          message.success('工具映射创建成功');
          setMappingModalVisible(false);
          // 创建映射后，重新获取映射列表
          fetchMappings({
            page: 1,
            page_size: 100, // 修改为后端允许的最大值
            search: searchText
          });
        } else {
          message.error('工具映射创建失败: ' + (response.message || '未知错误'));
        }
      }
    } catch (error) {
      console.error('提交表单失败:', error);
      message.error('提交表单失败: ' + (error.message || '未知错误'));
    }
  };

  // 处理删除映射
  const handleDeleteMapping = async (mappingId) => {
    try {
      const response = await deleteMapping(mappingId);
      if (response.success) {
        message.success('工具映射删除成功');
        // 删除映射后，重新获取映射列表
        fetchMappings({
          page: 1,
          page_size: 100, // 修改为后端允许的最大值
          search: searchText
        });
      } else {
        message.error('工具映射删除失败: ' + (response.message || '未知错误'));
      }
    } catch (error) {
      console.error('删除工具映射失败:', error);
      message.error('删除工具映射失败: ' + (error.message || '未知错误'));
    }
  };

  // 获取服务器工具列表
  const fetchServerTools = async (serverId) => {
    setToolsLoading(true);
    try {
      const response = await getServerTools(serverId);
      if (response.success) {
        setServerTools({
          ...serverTools,
          [serverId]: response.data
        });
      }
    } catch (error) {
      console.error('获取服务器工具失败:', error);
    } finally {
      setToolsLoading(false);
    }
  };

  // 处理服务器选择变化
  const handleServerChange = (serverId) => {
    if (serverId && !serverTools[serverId]) {
      fetchServerTools(serverId);
    }
  };

  // 处理端点选择变化
  const handleEndpointChange = (endpointId) => {
    setSelectedEndpoint(endpointId);

    // 根据选择的端点自动生成工具名称
    const endpoint = endpoints.find(e => e.id === endpointId);
    if (endpoint) {
      // 提取端点路径中的最后一部分作为工具名称
      const path_parts = endpoint.endpoint_path.split('/').filter(p => p);
      // 如果路径以api开头，则去掉api部分
      if (path_parts && path_parts[0] === 'api') {
        path_parts.shift();
      }
      const tool_name = path_parts[path_parts.length - 1] || endpoint.name.toLowerCase().replace(/\s+/g, '_');

      mappingForm.setFieldsValue({
        tool_name,
        tool_description: endpoint.description || ''
      });
    }
  };

  // 自动生成参数映射
  const generateParameterMapping = () => {
    if (!selectedEndpoint) {
      message.warning('请先选择API端点');
      return;
    }

    const endpoint = endpoints.find(e => e.id === selectedEndpoint);
    if (!endpoint || !endpoint.parameters || typeof endpoint.parameters !== 'object') {
      message.warning('所选端点数据无效或缺少参数定义 (endpoint.parameters)');
      mappingForm.setFieldsValue({
        parameter_mapping: JSON.stringify({}, null, 2)
      });
      return;
    }

    const parameterMapping = {};
    let paramsFound = false;

    // 1. 处理 openapi_parameters (路径、查询、头部等)
    if (endpoint.parameters.openapi_parameters && typeof endpoint.parameters.openapi_parameters === 'object') {
      Object.keys(endpoint.parameters.openapi_parameters).forEach(paramName => {
        // 确保参数定义本身是对象并且包含 schema (即使 schema 可能是空的，但参数名是存在的)
        const paramDef = endpoint.parameters.openapi_parameters[paramName];
        if (paramDef && typeof paramDef === 'object' && paramDef.name === paramName) { // paramDef.name 应该等于 paramName
            parameterMapping[paramName] = paramName;
            paramsFound = true;
        }
      });
    }

    // 2. 处理 requestBody
    if (endpoint.parameters.requestBody &&
        typeof endpoint.parameters.requestBody === 'object' &&
        endpoint.parameters.requestBody.content &&
        typeof endpoint.parameters.requestBody.content === 'object') {

      const rbContent = endpoint.parameters.requestBody.content;
      let requestBodySchema = null;

      // 按优先级查找 schema: application/json, then multipart/form-data, then first available
      if (rbContent['application/json'] && rbContent['application/json'].schema) {
        requestBodySchema = rbContent['application/json'].schema;
      } else if (rbContent['multipart/form-data'] && rbContent['multipart/form-data'].schema) {
        requestBodySchema = rbContent['multipart/form-data'].schema;
      } else {
        for (const mediaType in rbContent) {
          if (rbContent[mediaType] && rbContent[mediaType].schema) {
            requestBodySchema = rbContent[mediaType].schema;
            break;
          }
        }
      }

      if (requestBodySchema && requestBodySchema.properties && typeof requestBodySchema.properties === 'object') {
        Object.keys(requestBodySchema.properties).forEach(bodyParamName => {
          parameterMapping[bodyParamName] = bodyParamName;
          paramsFound = true;
        });
      }
    }

    mappingForm.setFieldsValue({
      parameter_mapping: JSON.stringify(parameterMapping, null, 2)
    });

    if (paramsFound && Object.keys(parameterMapping).length > 0) {
      message.success(`已自动生成 ${Object.keys(parameterMapping).length} 个同名参数映射。您可以编辑此JSON进行调整。`);
    } else {
      message.info('所选端点没有可自动映射的参数 (请检查openapi_parameters或requestBody的定义)，参数映射已设置为空。');
    }
  };

  // 渲染HTTP方法标签
  const renderMethodTag = (method) => {
    const methodColors = {
      GET: 'green',
      POST: 'blue',
      PUT: 'orange',
      DELETE: 'red',
      PATCH: 'purple'
    };

    return <Tag color={methodColors[method] || 'default'}>{method}</Tag>;
  };

  // 表格列定义
  const columns = [
    {
      title: '状态',
      key: 'enabled',
      render: (_, record) => (
        <Badge
          status={record.enabled ? 'success' : 'default'}
          text={record.enabled ? '启用' : '禁用'}
        />
      ),
      width: 80,
    },
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <Space>
          <Text strong>{text}</Text>
          {record.mappings && record.mappings.length > 0 && (
            <Tag color="blue" icon={<LinkOutlined />}>已映射</Tag>
          )}
        </Space>
      )
    },
    {
      title: '方法',
      dataIndex: 'method',
      key: 'method',
      render: method => renderMethodTag(method),
      width: 80,
    },
    {
      title: '路径',
      dataIndex: 'endpoint_path',
      key: 'endpoint_path',
      ellipsis: true,
    },
    {
      title: '映射工具',
      key: 'mappings',
      render: (_, record) => {
        // 查找该端点的映射
        const endpointMappings = mappings.filter(m => m.endpoint_id === record.id);

        if (endpointMappings.length === 0) {
          return (
            <Button
              type="dashed"
              size="small"
              icon={<LinkOutlined />}
              onClick={() => showCreateMappingModal(record)}
            >
              添加映射
            </Button>
          );
        }

        return (
          <Space>
            {endpointMappings.map(mapping => (
              <Tag
                key={mapping.id}
                color="blue"
                icon={<ToolOutlined />}
                style={{ cursor: 'pointer' }}
                onClick={() => showEditMappingModal(mapping)}
              >
                {mapping.tool_name}
              </Tag>
            ))}
          </Space>
        );
      },
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space>
          <Tooltip title="查看详情">
            <Button
              icon={<EyeOutlined />}
              size="small"
              onClick={() => showEndpointDetail(record)}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button
              icon={<EditOutlined />}
              size="small"
              onClick={() => showEditEndpointModal(record)}
            />
          </Tooltip>
          <Tooltip title="删除">
            <Popconfirm
              title="确定要删除此API端点吗？"
              onConfirm={() => handleDeleteEndpoint(record.id)}
              okText="确定"
              cancelText="取消"
            >
              <Button
                icon={<DeleteOutlined />}
                size="small"
                danger
              />
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <Card>
      <Title level={4}>API端点和工具映射管理</Title>
      <Paragraph>
        管理API端点和工具映射，包括创建、编辑和删除端点，以及将端点映射到MCP工具。通过映射，可以将系统API转换为MCP工具，供大语言模型使用。
      </Paragraph>

      <div style={{ marginBottom: 16 }}>
        <Row gutter={[16, 16]} align="middle">
          <Col xs={24} sm={24} md={16} lg={16}>
            <Space>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={showCreateEndpointModal}
              >
                创建API端点
              </Button>
              <Button
                icon={<LinkOutlined />}
                onClick={() => showCreateMappingModal()}
              >
                创建工具映射
              </Button>
              <Button
                icon={<ReloadOutlined />}
                onClick={() => {
                  // 刷新时使用当前分页状态
                  fetchEndpoints({
                    page: pagination.current,
                    page_size: pagination.pageSize,
                    search: searchText
                  });
                  fetchMappings({
                    page: 1,
                    page_size: 100, // 修改为后端允许的最大值
                    search: searchText
                  });
                }}
              >
                刷新列表
              </Button>
              <Button
                icon={<SyncOutlined />}
                loading={syncLoading}
                onClick={handleSyncEndpoints}
              >
                同步系统API
              </Button>
            </Space>
          </Col>
          <Col xs={24} sm={24} md={8} lg={8}>
            <Input.Search
              placeholder="搜索映射名称、描述或端点"
              allowClear
              enterButton={<SearchOutlined />}
              onSearch={handleSearch}
              style={{ width: '100%' }}
            />
          </Col>
        </Row>
      </div>

      <Spin spinning={loading}>
        <Table
          dataSource={endpoints}
          columns={columns}
          rowKey="id"
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            pageSizeOptions: ['10', '20', '50', '100'],
            showTotal: (total) => `共 ${total} 条记录`
          }}
          onChange={handleTableChange}
        />
      </Spin>

      {/* 创建/编辑端点模态框 */}
      <Modal
        title={endpointModalTitle}
        open={endpointModalVisible}
        onCancel={() => setEndpointModalVisible(false)}
        footer={null}
        width={700}
      >
        <Form
          form={endpointForm}
          layout="vertical"
          onFinish={handleEndpointSubmit}
          initialValues={{
            method: 'GET',
            enabled: true,
            parameters: '{}',
            response_template: ''
          }}
        >
          <Form.Item
            name="name"
            label="端点名称"
            rules={[{ required: true, message: '请输入端点名称' }]}
          >
            <Input placeholder="请输入端点名称" />
          </Form.Item>

          <Form.Item
            name="description"
            label="端点描述"
          >
            <TextArea
              placeholder="请输入端点描述"
              rows={2}
            />
          </Form.Item>

          <Form.Item
            name="endpoint_path"
            label="端点路径"
            rules={[{ required: true, message: '请输入端点路径' }]}
            tooltip="API端点的路径，例如: /api/v1/project/users"
          >
            <Input
              placeholder="请输入端点路径"
              addonBefore={<LinkOutlined />}
            />
          </Form.Item>

          <Form.Item
            name="method"
            label="HTTP方法"
            rules={[{ required: true, message: '请选择HTTP方法' }]}
          >
            <Select>
              <Option value="GET">GET</Option>
              <Option value="POST">POST</Option>
              <Option value="PUT">PUT</Option>
              <Option value="DELETE">DELETE</Option>
              <Option value="PATCH">PATCH</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="enabled"
            label="启用状态"
            valuePropName="checked"
            tooltip="是否启用此API端点"
          >
            <Switch />
          </Form.Item>

          <Form.Item
            name="parameters"
            label="参数定义"
            tooltip="JSON格式的参数定义，包含参数名称、类型、描述和是否必填"
          >
            <TextArea
              placeholder="请输入JSON格式的参数定义"
              rows={5}
            />
          </Form.Item>

          <Form.Item
            name="response_template"
            label="响应模板"
            tooltip="用于格式化API响应的模板，支持变量替换"
          >
            <TextArea
              placeholder="请输入响应模板"
              rows={3}
            />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                {editingEndpoint ? '更新' : '创建'}
              </Button>
              <Button onClick={() => setEndpointModalVisible(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 创建/编辑映射模态框 */}
      <Modal
        title={mappingModalTitle}
        open={mappingModalVisible}
        onCancel={() => setMappingModalVisible(false)}
        footer={null}
        width={700}
      >
        <Form
          form={mappingForm}
          layout="vertical"
          onFinish={handleMappingSubmit}
          initialValues={{
            parameter_mapping: '{}',
            response_mapping: '{}',
            supports_streaming: true,
            supports_media: false,
            enabled: true
          }}
        >
          <Form.Item
            name="endpoint_id"
            label="API端点"
            rules={[{ required: true, message: '请选择API端点' }]}
          >
            <Select
              placeholder="请选择API端点"
              onChange={handleEndpointChange}
              showSearch
              optionFilterProp="children"
              disabled={!!selectedEndpoint && !!editingMapping}
            >
              {endpoints.map(endpoint => (
                <Option key={endpoint.id} value={endpoint.id}>
                  <Space>
                    {renderMethodTag(endpoint.method)}
                    {endpoint.name} - {endpoint.endpoint_path}
                  </Space>
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="server_id"
            label="MCP服务器"
            rules={[{ required: true, message: '请选择MCP服务器' }]}
          >
            <Select
              placeholder="请选择MCP服务器"
              onChange={handleServerChange}
              showSearch
              optionFilterProp="children"
            >
              {servers.map(server => (
                <Option key={server.id} value={server.id}>
                  {server.name}
                  <Badge
                    status={server.status === 'running' ? 'success' : 'default'}
                    text={server.status === 'running' ? '运行中' : '已停止'}
                    style={{ marginLeft: 8 }}
                  />
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="tool_name"
            label="工具名称"
            rules={[{ required: true, message: '请输入工具名称' }]}
            tooltip="MCP工具的名称，建议使用小写字母和下划线"
          >
            <Input placeholder="请输入工具名称" />
          </Form.Item>

          <Form.Item
            name="tool_description"
            label="工具描述"
            tooltip="工具的描述，如果不填写则使用API端点的描述"
          >
            <TextArea
              placeholder="请输入工具描述"
              rows={2}
            />
          </Form.Item>

          <div style={{ marginBottom: 16 }}>
            <Button
              type="dashed"
              onClick={generateParameterMapping}
              icon={<SyncOutlined />}
            >
              自动生成参数映射
            </Button>
          </div>

          <Form.Item
            name="parameter_mapping"
            label="参数映射"
            tooltip="JSON格式的参数映射，用于将API参数映射到MCP工具参数"
          >
            <TextArea
              placeholder="请输入JSON格式的参数映射"
              rows={5}
            />
          </Form.Item>

          <Form.Item
            name="response_mapping"
            label="响应映射"
            tooltip="JSON格式的响应映射，用于将API响应映射到MCP工具响应"
          >
            <TextArea
              placeholder="请输入JSON格式的响应映射"
              rows={5}
            />
          </Form.Item>

          <Form.Item
            name="supports_streaming"
            label="支持流式响应"
            valuePropName="checked"
            tooltip="是否支持流式响应"
          >
            <Switch />
          </Form.Item>

          <Form.Item
            name="supports_media"
            label="支持媒体"
            valuePropName="checked"
            tooltip="是否支持图片等媒体类型"
          >
            <Switch />
          </Form.Item>

          <Form.Item
            name="enabled"
            label="启用状态"
            valuePropName="checked"
            tooltip="是否启用此工具映射"
          >
            <Switch />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                {editingMapping ? '更新' : '创建'}
              </Button>
              <Button onClick={() => setMappingModalVisible(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 端点详情抽屉 */}
      <Drawer
        title={detailEndpoint?.name}
        placement="right"
        width={700}
        onClose={() => setDetailVisible(false)}
        open={detailVisible}
      >
        {detailEndpoint && (
          <Tabs activeKey={detailTab} onChange={setDetailTab}>
            <TabPane
              tab={<span><ApiOutlined /> 基本信息</span>}
              key="1"
            >
              <Descriptions bordered column={1}>
                <Descriptions.Item label="ID">{detailEndpoint.id}</Descriptions.Item>
                <Descriptions.Item label="名称">{detailEndpoint.name}</Descriptions.Item>
                <Descriptions.Item label="描述">{detailEndpoint.description}</Descriptions.Item>
                <Descriptions.Item label="路径">{detailEndpoint.endpoint_path}</Descriptions.Item>
                <Descriptions.Item label="方法">
                  {renderMethodTag(detailEndpoint.method)}
                </Descriptions.Item>
                <Descriptions.Item label="状态">
                  <Badge
                    status={detailEndpoint.enabled ? 'success' : 'default'}
                    text={detailEndpoint.enabled ? '启用' : '禁用'}
                  />
                </Descriptions.Item>
                <Descriptions.Item label="创建时间">{new Date(detailEndpoint.created_at).toLocaleString()}</Descriptions.Item>
                <Descriptions.Item label="更新时间">{new Date(detailEndpoint.updated_at).toLocaleString()}</Descriptions.Item>
              </Descriptions>

              {detailEndpoint.parameters && Object.keys(detailEndpoint.parameters).length > 0 && (
                <div style={{ marginTop: 16 }}>
                  <Title level={5}>参数定义</Title>
                  <ReactJson
                    src={detailEndpoint.parameters}
                    name={false}
                    collapsed={1}
                    displayDataTypes={false}
                  />
                </div>
              )}

              {detailEndpoint.response_template && (
                <div style={{ marginTop: 16 }}>
                  <Title level={5}>响应模板</Title>
                  <Card size="small">
                    <pre style={{ margin: 0 }}>{detailEndpoint.response_template}</pre>
                  </Card>
                </div>
              )}
            </TabPane>

            <TabPane
              tab={<span><LinkOutlined /> 工具映射</span>}
              key="2"
            >
              {detailEndpoint.mappings && detailEndpoint.mappings.length > 0 ? (
                <Table
                  dataSource={detailEndpoint.mappings}
                  rowKey="id"
                  pagination={false}
                  columns={[
                    {
                      title: '工具名称',
                      dataIndex: 'tool_name',
                      key: 'tool_name',
                      render: text => <Text strong>{text}</Text>
                    },
                    {
                      title: '服务器',
                      dataIndex: 'server_name',
                      key: 'server_name',
                    },
                    {
                      title: '状态',
                      key: 'enabled',
                      render: (_, record) => (
                        <Badge
                          status={record.enabled ? 'success' : 'default'}
                          text={record.enabled ? '启用' : '禁用'}
                        />
                      ),
                    },
                    {
                      title: 'Input Schema',
                      dataIndex: 'input_schema',
                      key: 'input_schema',
                      render: (schema) => (
                        schema ? (
                          <ReactJson
                            src={schema}
                            name={false}
                            collapsed={1}
                            displayDataTypes={false}
                            enableClipboard={false}
                            style={{ maxHeight: '150px', overflowY: 'auto', fontSize: '12px' }}
                          />
                        ) : (
                          <Text type="secondary">N/A</Text>
                        )
                      ),
                    },
                    {
                      title: '操作',
                      key: 'action',
                      render: (_, record) => (
                        <Space>
                          <Button
                            icon={<EditOutlined />}
                            size="small"
                            onClick={() => {
                              setDetailVisible(false);
                              showEditMappingModal(record);
                            }}
                          >
                            编辑
                          </Button>
                          <Popconfirm
                            title="确定要删除此工具映射吗？"
                            onConfirm={() => {
                              handleDeleteMapping(record.id);
                              setDetailVisible(false);
                            }}
                            okText="确定"
                            cancelText="取消"
                          >
                            <Button
                              icon={<DisconnectOutlined />}
                              size="small"
                              danger
                            >
                              解除映射
                            </Button>
                          </Popconfirm>
                        </Space>
                      ),
                    }
                  ]}
                />
              ) : (
                <div style={{ textAlign: 'center', padding: '20px 0' }}>
                  <Alert
                    message="暂无工具映射"
                    description={
                      <div>
                        <p>此API端点尚未映射到任何MCP工具</p>
                        <Button
                          type="primary"
                          icon={<LinkOutlined />}
                          onClick={() => {
                            setDetailVisible(false);
                            showCreateMappingModal(detailEndpoint);
                          }}
                        >
                          创建映射
                        </Button>
                      </div>
                    }
                    type="info"
                    showIcon
                  />
                </div>
              )}
            </TabPane>
          </Tabs>
        )}
      </Drawer>
    </Card>
  );
};

export default ApiEndpointToolMappingManage;
