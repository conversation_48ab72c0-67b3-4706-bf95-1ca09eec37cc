import React, { useState } from 'react';
import { Layout, Menu, Button, Avatar, Dropdown, Badge, Space, Divider } from 'antd';
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  DashboardOutlined,
  TeamOutlined,
  SettingOutlined,
  UserOutlined,
  LogoutOutlined,
  BellOutlined,
  AppstoreOutlined,
  ShopOutlined,
  BankOutlined,
  CloudServerOutlined,
  CloudOutlined,
} from '@ant-design/icons';
import { useNavigate, useLocation } from 'react-router-dom';
import './AppLayout.css';

const { Header, Sider, Content, Footer } = Layout;

const AppLayout = ({ children }) => {
  const [collapsed, setCollapsed] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();

  // 从localStorage获取用户信息
  const userString = localStorage.getItem('user_data');
  const user = userString ? JSON.parse(userString) : null;

  // 处理注销
  const handleLogout = () => {
    // 获取用户类型，用于决定登出后导航到哪个登录页面
    const userType = localStorage.getItem('user_type');

    // 清除所有认证相关的本地存储
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    localStorage.removeItem('user_data');
    localStorage.removeItem('user_type');
    localStorage.removeItem('isLoggedIn');

    // 根据用户类型导航到相应的登录页面
    if (userType === 'admin') {
      window.location.href = '/admin/login';
    } else if (userType === 'service_provider') {
      window.location.href = '/service-provider/login';
    } else {
      window.location.href = '/login';
    }
  };

  // 用户下拉菜单
  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人中心',
      onClick: () => navigate('/user/profile')
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '个人设置',
      onClick: () => navigate('/user/settings')
    },
    {
      type: 'divider'
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      onClick: handleLogout
    }
  ];

  // 获取菜单项
  const getMenuItems = () => {
    const items = [];

    // 系统管理员菜单
    if (user?.is_system_admin || user?.is_super_admin) {
      items.push(
        { key: '/admin/dashboard', icon: <DashboardOutlined />, label: '系统仪表盘' },
        { key: '/admin/service-providers', icon: <CloudServerOutlined />, label: '服务商管理' },
        { key: '/admin/tenants', icon: <BankOutlined />, label: '租户管理' },
        { key: '/admin/users', icon: <TeamOutlined />, label: '用户管理' },
        { key: '/admin/industries', icon: <AppstoreOutlined />, label: '行业管理' },
        { key: '/admin/settings', icon: <SettingOutlined />, label: '系统设置' },
        { key: '/admin/storage', icon: <CloudOutlined />, label: '存储管理' }
      );
    }

    // 服务商管理员菜单
    else if (user?.is_service_provider_admin) {
      items.push(
        { key: '/service-provider/dashboard', icon: <DashboardOutlined />, label: '服务商仪表盘' },
        { key: '/service-provider/tenants', icon: <BankOutlined />, label: '租户管理' },
        { key: '/service-provider/plugins', icon: <AppstoreOutlined />, label: '插件管理' },
        {
          key: '/service-provider/subscription',
          icon: <ShopOutlined />,
          label: '订阅计划',
          children: [
            { key: '/service-provider/subscription/plans', label: '计划管理' },
            { key: '/service-provider/subscription/orders', label: '订单管理' }
          ]
        },
        {
          key: '/service-provider/finance',
          icon: <BankOutlined />,
          label: '财务管理',
          children: [
            { key: '/service-provider/finance/income', label: '收入统计' },
            { key: '/service-provider/finance/settlement', label: '结算记录' }
          ]
        },
        { key: '/service-provider/settings', icon: <SettingOutlined />, label: '服务商设置' }
      );
    }

    // 租户管理员菜单
    else if (user?.is_tenant_admin) {
      items.push(
        { key: '/tenant/dashboard', icon: <DashboardOutlined />, label: '租户仪表盘' },
        { key: '/tenant/projects', icon: <AppstoreOutlined />, label: '项目管理' },
        { key: '/tenant/settings', icon: <SettingOutlined />, label: '租户设置' }
      );
    }

    // 普通用户菜单（默认零售业）
    else {
      items.push(
        { key: '/retail/dashboard', icon: <DashboardOutlined />, label: '零售仪表盘' },
        { key: '/retail/products', icon: <AppstoreOutlined />, label: '商品管理' },
        { key: '/retail/inventory', icon: <ShopOutlined />, label: '库存管理' }
      );
    }

    return items;
  };

  return (
    <Layout style={{ minHeight: '100vh' }}>
      <Sider trigger={null} collapsible collapsed={collapsed} theme="light">
        <div className="logo">
          {collapsed ? 'RAIS' : 'Retail AI SaaS'}
        </div>
        <Menu
          theme="light"
          mode="inline"
          selectedKeys={[location.pathname]}
          items={getMenuItems()}
          onClick={({ key }) => navigate(key)}
        />
      </Sider>
      <Layout>
        <Header className="app-header">
          <Button
            type="text"
            icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
            onClick={() => setCollapsed(!collapsed)}
            className="trigger-button"
          />
          <div className="header-right">
            <Space>
              <Badge count={5} size="small">
                <Button
                  type="text"
                  icon={<BellOutlined />}
                  onClick={() => navigate('/notifications')}
                />
              </Badge>
              <Dropdown menu={{ items: userMenuItems }} placement="bottomRight">
                <Space className="user-dropdown">
                  <Avatar icon={<UserOutlined />} />
                  {user?.username || '用户'}
                </Space>
              </Dropdown>
            </Space>
          </div>
        </Header>
        <Content className="app-content">
          {children}
        </Content>
        <Footer className="app-footer">
          Retail AI SaaS © {new Date().getFullYear()} - 多租户多行业SaaS平台
        </Footer>
      </Layout>
    </Layout>
  );
};

export default AppLayout;
