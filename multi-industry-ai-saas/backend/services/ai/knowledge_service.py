#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
AI 知识库服务
"""

import logging
import uuid
import json
import time
from typing import List, Optional, Dict, Any, Tuple, Union
from sqlalchemy import select, func, and_, or_, desc, text, bindparam
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import joinedload

from models.ai import AIKnowledgeBase, AIKnowledgeDocument, AIKnowledgeChunk, AIModel
from models.project import Project
from models.tenant import Tenant
from schemas.ai import (
    AIKnowledgeBaseCreate,
    AIKnowledgeBaseUpdate,
    AIKnowledgeDocumentCreate,
    AIKnowledgeDocumentUpdate,
    AIKnowledgeChunkCreate,
    AIKnowledgeQueryRequest,
    AIKnowledgeQueryResult,
)
from services.ai import AIEmbeddingService

logger = logging.getLogger(__name__)

class AIKnowledgeService:
    """AI 知识库服务"""

    @staticmethod
    async def get_knowledge_bases(
        db: AsyncSession,
        skip: int = 0,
        limit: int = 100,
        tenant_id: Optional[uuid.UUID] = None,
        project_id: Optional[uuid.UUID] = None,
        industry_type: Optional[str] = None,
        status: Optional[str] = None,
    ) -> Tuple[List[AIKnowledgeBase], int]:
        """
        获取 AI 知识库列表
        """
        query = select(AIKnowledgeBase).options(
            joinedload(AIKnowledgeBase.embedding_model)
        )

        # 构建查询条件
        conditions = []

        if tenant_id:
            conditions.append(AIKnowledgeBase.tenant_id == tenant_id)

        if project_id:
            conditions.append(AIKnowledgeBase.project_id == project_id)

        if industry_type:
            conditions.append(AIKnowledgeBase.industry_type == industry_type)

        if status:
            conditions.append(AIKnowledgeBase.status == status)

        if conditions:
            query = query.where(and_(*conditions))

        # 获取总数
        count_query = select(func.count()).select_from(query.subquery())
        total = await db.scalar(count_query)

        # 获取分页数据
        query = query.order_by(desc(AIKnowledgeBase.created_at)).offset(skip).limit(limit)
        result = await db.execute(query)
        knowledge_bases = result.scalars().all()

        return knowledge_bases, total

    @staticmethod
    async def get_knowledge_base(
        db: AsyncSession,
        knowledge_base_id: uuid.UUID,
    ) -> Optional[AIKnowledgeBase]:
        """
        获取 AI 知识库详情
        """
        query = select(AIKnowledgeBase).options(
            joinedload(AIKnowledgeBase.embedding_model)
        ).where(AIKnowledgeBase.id == knowledge_base_id)

        result = await db.execute(query)
        return result.scalars().first()

    @staticmethod
    async def create_knowledge_base(
        db: AsyncSession,
        knowledge_base_data: AIKnowledgeBaseCreate,
    ) -> AIKnowledgeBase:
        """
        创建 AI 知识库
        """
        # 检查租户是否存在
        tenant_query = select(Tenant).where(Tenant.id == knowledge_base_data.tenant_id)
        tenant_result = await db.execute(tenant_query)
        tenant = tenant_result.scalars().first()

        if not tenant:
            raise ValueError(f"租户 ID '{knowledge_base_data.tenant_id}' 不存在")

        # 检查项目是否存在
        if knowledge_base_data.project_id:
            project_query = select(Project).where(Project.id == knowledge_base_data.project_id)
            project_result = await db.execute(project_query)
            project = project_result.scalars().first()

            if not project:
                raise ValueError(f"项目 ID '{knowledge_base_data.project_id}' 不存在")

            # 检查项目是否属于该租户
            if project.tenant_id != knowledge_base_data.tenant_id:
                raise ValueError(f"项目 '{project.name}' 不属于租户 ID '{knowledge_base_data.tenant_id}'")

        # 检查嵌入模型是否存在
        if knowledge_base_data.embedding_model_id:
            model_query = select(AIModel).where(
                AIModel.id == knowledge_base_data.embedding_model_id,
                AIModel.model_type == "embedding"
            )
            model_result = await db.execute(model_query)
            model = model_result.scalars().first()

            if not model:
                raise ValueError(f"嵌入模型 ID '{knowledge_base_data.embedding_model_id}' 不存在或不是嵌入模型")

        # 创建知识库
        knowledge_base = AIKnowledgeBase(**knowledge_base_data.dict())
        db.add(knowledge_base)
        await db.commit()
        await db.refresh(knowledge_base)

        logger.info(f"创建 AI 知识库成功: {knowledge_base.id}")
        return knowledge_base

    @staticmethod
    async def update_knowledge_base(
        db: AsyncSession,
        knowledge_base_id: uuid.UUID,
        knowledge_base_data: AIKnowledgeBaseUpdate,
    ) -> Optional[AIKnowledgeBase]:
        """
        更新 AI 知识库
        """
        # 获取知识库
        knowledge_base = await AIKnowledgeService.get_knowledge_base(db, knowledge_base_id)
        if not knowledge_base:
            return None

        # 检查嵌入模型是否存在
        if knowledge_base_data.embedding_model_id:
            model_query = select(AIModel).where(
                AIModel.id == knowledge_base_data.embedding_model_id,
                AIModel.model_type == "embedding"
            )
            model_result = await db.execute(model_query)
            model = model_result.scalars().first()

            if not model:
                raise ValueError(f"嵌入模型 ID '{knowledge_base_data.embedding_model_id}' 不存在或不是嵌入模型")

        # 更新知识库
        update_data = knowledge_base_data.dict(exclude_unset=True)
        for key, value in update_data.items():
            setattr(knowledge_base, key, value)

        await db.commit()
        await db.refresh(knowledge_base)

        logger.info(f"更新 AI 知识库成功: {knowledge_base.id}")
        return knowledge_base

    @staticmethod
    async def delete_knowledge_base(
        db: AsyncSession,
        knowledge_base_id: uuid.UUID,
    ) -> bool:
        """
        删除 AI 知识库
        """
        # 获取知识库
        knowledge_base = await AIKnowledgeService.get_knowledge_base(db, knowledge_base_id)
        if not knowledge_base:
            return False

        # 删除知识库
        await db.delete(knowledge_base)
        await db.commit()

        logger.info(f"删除 AI 知识库成功: {knowledge_base.id}")
        return True

    @staticmethod
    async def get_documents(
        db: AsyncSession,
        knowledge_base_id: uuid.UUID,
        skip: int = 0,
        limit: int = 100,
        embedding_status: Optional[str] = None,
    ) -> Tuple[List[AIKnowledgeDocument], int]:
        """
        获取 AI 知识文档列表
        """
        query = select(AIKnowledgeDocument).where(
            AIKnowledgeDocument.knowledge_base_id == knowledge_base_id
        )

        if embedding_status:
            query = query.where(AIKnowledgeDocument.embedding_status == embedding_status)

        # 获取总数
        count_query = select(func.count()).select_from(query.subquery())
        total = await db.scalar(count_query)

        # 获取分页数据
        query = query.order_by(desc(AIKnowledgeDocument.created_at)).offset(skip).limit(limit)
        result = await db.execute(query)
        documents = result.scalars().all()

        return documents, total

    @staticmethod
    async def get_document(
        db: AsyncSession,
        document_id: uuid.UUID,
    ) -> Optional[AIKnowledgeDocument]:
        """
        获取 AI 知识文档详情
        """
        query = select(AIKnowledgeDocument).where(AIKnowledgeDocument.id == document_id)
        result = await db.execute(query)
        return result.scalars().first()

    @staticmethod
    async def create_document(
        db: AsyncSession,
        document_data: AIKnowledgeDocumentCreate,
    ) -> AIKnowledgeDocument:
        """
        创建 AI 知识文档
        """
        # 检查知识库是否存在
        knowledge_base = await AIKnowledgeService.get_knowledge_base(db, document_data.knowledge_base_id)
        if not knowledge_base:
            raise ValueError(f"知识库 ID '{document_data.knowledge_base_id}' 不存在")

        # 创建文档
        document = AIKnowledgeDocument(**document_data.dict())
        db.add(document)

        # 更新知识库文档数量
        knowledge_base.document_count += 1

        await db.commit()
        await db.refresh(document)

        logger.info(f"创建 AI 知识文档成功: {document.id}")
        return document

    @staticmethod
    async def update_document(
        db: AsyncSession,
        document_id: uuid.UUID,
        document_data: AIKnowledgeDocumentUpdate,
    ) -> Optional[AIKnowledgeDocument]:
        """
        更新 AI 知识文档
        """
        # 获取文档
        document = await AIKnowledgeService.get_document(db, document_id)
        if not document:
            return None

        # 更新文档
        update_data = document_data.dict(exclude_unset=True)
        for key, value in update_data.items():
            setattr(document, key, value)

        await db.commit()
        await db.refresh(document)

        logger.info(f"更新 AI 知识文档成功: {document.id}")
        return document

    @staticmethod
    async def delete_document(
        db: AsyncSession,
        document_id: uuid.UUID,
    ) -> bool:
        """
        删除 AI 知识文档
        """
        # 获取文档
        document = await AIKnowledgeService.get_document(db, document_id)
        if not document:
            return False

        # 获取知识库
        knowledge_base = await AIKnowledgeService.get_knowledge_base(db, document.knowledge_base_id)
        if knowledge_base:
            # 更新知识库文档数量和文本块数量
            knowledge_base.document_count = max(0, knowledge_base.document_count - 1)
            knowledge_base.chunk_count = max(0, knowledge_base.chunk_count - document.chunk_count)

        # 删除文档
        await db.delete(document)
        await db.commit()

        logger.info(f"删除 AI 知识文档成功: {document.id}")
        return True

    @staticmethod
    async def get_chunks(
        db: AsyncSession,
        document_id: uuid.UUID,
        skip: int = 0,
        limit: int = 100,
    ) -> Tuple[List[AIKnowledgeChunk], int]:
        """
        获取 AI 知识文本块列表
        """
        query = select(AIKnowledgeChunk).where(
            AIKnowledgeChunk.document_id == document_id
        )

        # 获取总数
        count_query = select(func.count()).select_from(query.subquery())
        total = await db.scalar(count_query)

        # 获取分页数据
        query = query.order_by(AIKnowledgeChunk.chunk_index).offset(skip).limit(limit)
        result = await db.execute(query)
        chunks = result.scalars().all()

        return chunks, total

    @staticmethod
    async def create_chunk(
        db: AsyncSession,
        chunk_data: AIKnowledgeChunkCreate,
    ) -> AIKnowledgeChunk:
        """
        创建 AI 知识文本块
        """
        # 检查文档是否存在
        document = await AIKnowledgeService.get_document(db, chunk_data.document_id)
        if not document:
            raise ValueError(f"文档 ID '{chunk_data.document_id}' 不存在")

        # 检查知识库是否存在
        knowledge_base = await AIKnowledgeService.get_knowledge_base(db, chunk_data.knowledge_base_id)
        if not knowledge_base:
            raise ValueError(f"知识库 ID '{chunk_data.knowledge_base_id}' 不存在")

        # 创建文本块
        chunk = AIKnowledgeChunk(**chunk_data.dict())
        db.add(chunk)

        # 更新文档文本块数量
        document.chunk_count += 1

        # 更新知识库文本块数量
        knowledge_base.chunk_count += 1

        await db.commit()
        await db.refresh(chunk)

        logger.info(f"创建 AI 知识文本块成功: {chunk.id}")
        return chunk

    @staticmethod
    async def delete_chunk(
        db: AsyncSession,
        chunk_id: uuid.UUID,
    ) -> bool:
        """
        删除 AI 知识文本块
        """
        # 获取文本块
        query = select(AIKnowledgeChunk).where(AIKnowledgeChunk.id == chunk_id)
        result = await db.execute(query)
        chunk = result.scalars().first()

        if not chunk:
            return False

        # 获取文档
        document = await AIKnowledgeService.get_document(db, chunk.document_id)
        if document:
            # 更新文档文本块数量
            document.chunk_count = max(0, document.chunk_count - 1)

        # 获取知识库
        knowledge_base = await AIKnowledgeService.get_knowledge_base(db, chunk.knowledge_base_id)
        if knowledge_base:
            # 更新知识库文本块数量
            knowledge_base.chunk_count = max(0, knowledge_base.chunk_count - 1)

        # 删除文本块
        await db.delete(chunk)
        await db.commit()

        logger.info(f"删除 AI 知识文本块成功: {chunk.id}")
        return True

    @staticmethod
    async def process_document(
        db: AsyncSession,
        document_id: uuid.UUID,
        user_id: uuid.UUID,
        chunk_size: int = 1000,
        chunk_overlap: int = 200,
    ) -> bool:
        """
        处理文档，生成文本块和嵌入向量
        """
        # 获取文档
        document = await AIKnowledgeService.get_document(db, document_id)
        if not document:
            raise ValueError(f"文档 ID '{document_id}' 不存在")

        # 获取知识库
        knowledge_base = await AIKnowledgeService.get_knowledge_base(db, document.knowledge_base_id)
        if not knowledge_base:
            raise ValueError(f"知识库 ID '{document.knowledge_base_id}' 不存在")

        # 检查嵌入模型
        if not knowledge_base.embedding_model_id:
            raise ValueError(f"知识库 '{knowledge_base.name}' 未配置嵌入模型")

        try:
            # 更新文档状态
            document.embedding_status = "processing"
            await db.commit()

            # 获取文档内容
            content = document.content

            # 如果文档内容为空，但有文件ID（存储在metadata中），则尝试从存储服务获取文件内容
            file_id = None
            if document.request_metadata and "file_id" in document.request_metadata:
                file_id = document.request_metadata["file_id"]
            
            if not content and file_id:
                from services.storage_service import StorageService

                # 获取文件信息
                file = await StorageService.get_file(
                    db=db,
                    file_id=uuid.UUID(file_id),
                    project_id=knowledge_base.project_id
                )

                if file:
                    # 获取文件内容
                    file_info = await StorageService.download_file(
                        db=db,
                        file_id=uuid.UUID(file_id),
                        project_id=knowledge_base.project_id
                    )

                    if file_info:
                        # 根据文件类型处理内容
                        import os
                        from pathlib import Path

                        file_path = Path(file_info["file_path"])
                        mime_type = file.mime_type

                        # 文本文件直接读取
                        if mime_type.startswith("text/"):
                            with open(file_path, "r", encoding="utf-8") as f:
                                content = f.read()
                        # PDF文件使用pypdf解析
                        elif mime_type == "application/pdf":
                            try:
                                from pypdf import PdfReader
                                with open(file_path, "rb") as f:
                                    pdf_reader = PdfReader(f)
                                    content = ""
                                    for page in pdf_reader.pages:
                                        content += page.extract_text() + "\n"
                            except ImportError:
                                logger.warning("pypdf not installed, cannot process PDF")
                                content = ""
                        # Word文档使用python-docx解析
                        elif mime_type in ["application/msword", "application/vnd.openxmlformats-officedocument.wordprocessingml.document"]:
                            try:
                                import docx
                                doc = docx.Document(file_path)
                                content = "\n".join([para.text for para in doc.paragraphs])
                            except ImportError:
                                logger.warning("python-docx not installed, cannot process Word documents")
                                content = ""
                        # Excel文件使用pandas解析（统一处理.xls和.xlsx）
                        elif mime_type in [
                            "application/vnd.ms-excel", 
                            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                        ]:
                            try:
                                import pandas as pd
                                
                                content = ""
                                
                                # 使用pandas读取Excel文件，自动处理.xls和.xlsx
                                # 读取所有工作表
                                excel_data = pd.read_excel(file_path, sheet_name=None)  # sheet_name=None读取所有工作表
                                
                                for sheet_name, df in excel_data.items():
                                    content += f"\n=== {sheet_name} ===\n"
                                    
                                    # 添加列标题
                                    if not df.empty:
                                        headers = [str(col) for col in df.columns if col != "Unnamed"]
                                        if headers:
                                            content += "\t".join(headers) + "\n"
                                    
                                    # 将DataFrame转换为文本，逐行处理
                                    for _, row in df.iterrows():
                                        row_values = []
                                        for value in row:
                                            if pd.notna(value):  # 跳过NaN值
                                                row_values.append(str(value).strip())
                                        
                                        if row_values:  # 只添加非空行
                                            content += "\t".join(row_values) + "\n"
                                
                            except ImportError:
                                logger.warning("pandas未安装，无法处理Excel文件")
                                content = ""
                            except Exception as e:
                                logger.warning(f"pandas处理Excel文件失败: {e}")
                                content = ""

                        # 更新文档内容
                        if content:
                            document.content = content
                            await db.commit()

            if not content:
                raise ValueError(f"文档 '{document.title}' 没有内容，且无法从文件中提取内容")

            # 分割文本
            chunks = AIKnowledgeService._split_text(content, chunk_size, chunk_overlap)

            # 删除现有的文本块
            delete_query = text(f"""
                DELETE FROM ai_knowledge_chunks
                WHERE document_id = '{document_id}'
            """)
            await db.execute(delete_query)

            # 重置文档和知识库的文本块数量
            old_chunk_count = document.chunk_count
            document.chunk_count = 0
            knowledge_base.chunk_count -= old_chunk_count

            # 创建新的文本块
            for i, chunk_text in enumerate(chunks):
                # 创建文本块
                chunk_data = AIKnowledgeChunkCreate(
                    knowledge_base_id=knowledge_base.id,
                    document_id=document.id,
                    content=chunk_text,
                    chunk_index=i,
                    token_count=len(chunk_text.split()),  # 简单估算
                    chunk_metadata={
                        "document_title": document.title,
                        "source": document.source_url or document.file_path,
                    }
                )

                # 生成嵌入向量
                embedding_result = await AIEmbeddingService.create_embedding(
                    db=db,
                    project_id=knowledge_base.project_id or knowledge_base.tenant_id,  # 使用项目 ID 或租户 ID
                    user_id=user_id,
                    input=chunk_text,
                    model_id=knowledge_base.embedding_model_id,
                )

                # 提取嵌入向量
                embedding_data = embedding_result.get("data", [])
                if embedding_data:
                    embedding_vector = embedding_data[0].get("embedding", [])
                    chunk_data.embedding = embedding_vector

                # 创建文本块
                await AIKnowledgeService.create_chunk(db, chunk_data)

            # 更新文档状态
            document.embedding_status = "completed"
            await db.commit()

            logger.info(f"处理文档成功: {document.id}, 生成 {len(chunks)} 个文本块")
            return True

        except Exception as e:
            # 更新文档状态
            document.embedding_status = "failed"
            document.error_message = str(e)
            await db.commit()

            logger.error(f"处理文档失败: {document.id}, 错误: {e}")
            raise ValueError(f"处理文档失败: {str(e)}")

    @staticmethod
    def _split_text(text: str, chunk_size: int = 1000, chunk_overlap: int = 200) -> List[str]:
        """
        分割文本为多个文本块
        """
        # 简单按段落分割
        paragraphs = text.split("\n\n")
        chunks = []
        current_chunk = ""

        for paragraph in paragraphs:
            # 如果段落太长，进一步分割
            if len(paragraph) > chunk_size:
                sentences = paragraph.split(". ")
                for sentence in sentences:
                    if len(current_chunk) + len(sentence) + 2 <= chunk_size:
                        current_chunk += sentence + ". "
                    else:
                        if current_chunk:
                            chunks.append(current_chunk.strip())
                        current_chunk = sentence + ". "
            else:
                if len(current_chunk) + len(paragraph) + 2 <= chunk_size:
                    current_chunk += paragraph + "\n\n"
                else:
                    if current_chunk:
                        chunks.append(current_chunk.strip())
                    current_chunk = paragraph + "\n\n"

        if current_chunk:
            chunks.append(current_chunk.strip())

        # 添加重叠
        if chunk_overlap > 0 and len(chunks) > 1:
            overlapped_chunks = []
            for i in range(len(chunks)):
                if i == 0:
                    overlapped_chunks.append(chunks[i])
                else:
                    prev_chunk = chunks[i-1]
                    current_chunk = chunks[i]

                    # 获取上一个块的末尾
                    overlap_text = prev_chunk[-chunk_overlap:] if len(prev_chunk) > chunk_overlap else prev_chunk

                    # 添加重叠
                    overlapped_chunks.append(overlap_text + current_chunk)

            return overlapped_chunks

        return chunks

    @staticmethod
    async def query_knowledge_base(
        db: AsyncSession,
        query_data: AIKnowledgeQueryRequest,
        user_id: uuid.UUID,
    ) -> List[AIKnowledgeQueryResult]:
        """
        查询知识库
        """
        # 获取知识库
        knowledge_base = await AIKnowledgeService.get_knowledge_base(db, query_data.knowledge_base_id)
        if not knowledge_base:
            raise ValueError(f"知识库 ID '{query_data.knowledge_base_id}' 不存在")

        # 检查嵌入模型
        if not knowledge_base.embedding_model_id:
            raise ValueError(f"知识库 '{knowledge_base.name}' 未配置嵌入模型")

        # 生成查询嵌入向量
        embedding_result = await AIEmbeddingService.create_embedding(
            db=db,
            project_id=knowledge_base.project_id or knowledge_base.tenant_id,  # 使用项目 ID 或租户 ID
            user_id=user_id,
            input=query_data.query,
            model_id=knowledge_base.embedding_model_id,
        )

        # 提取嵌入向量
        if not embedding_result.get("success"):
            logger.error(f"生成查询嵌入向量失败: {embedding_result.get('message')}")
            raise ValueError("生成查询嵌入向量失败")
        
        # 从正确的数据结构中提取向量
        embedding_data = embedding_result.get("data", [])
        if not embedding_data:
            logger.error("嵌入服务返回的数据为空")
            raise ValueError("生成查询嵌入向量失败")
        
        # 获取第一个向量（对于单个查询）
        first_embedding = embedding_data[0] if isinstance(embedding_data, list) else embedding_data
        query_embedding = first_embedding.get("embedding", [])
        
        if not query_embedding:
            logger.error("提取的嵌入向量为空")
            raise ValueError("生成查询嵌入向量失败")

        # 将向量转换为pgvector格式的字符串
        # pgvector需要向量格式为'[1,2,3,...]'的字符串
        query_embedding_str = '[' + ','.join(map(str, query_embedding)) + ']'

        # 构建向量搜索查询
        # 注意：这里使用 PostgreSQL 的向量搜索功能，需要安装 pgvector 扩展
        # 使用位置参数避免参数绑定冲突
        vector_query = text("""
            SELECT
                c.id,
                c.document_id,
                d.title as document_title,
                c.content,
                c.chunk_metadata,
                c.page_number,
                1 - (c.embedding <=> $1::vector) as similarity
            FROM
                ai_knowledge_chunks c
            JOIN
                ai_knowledge_documents d ON c.document_id = d.id
            WHERE
                c.knowledge_base_id = $2
                AND c.embedding IS NOT NULL
            ORDER BY
                c.embedding <=> $1::vector
            LIMIT $3
        """)

        # 构建最终SQL查询
        final_sql = vector_query.text.replace('$1', f"'{query_embedding_str}'::vector")
        final_sql = final_sql.replace('$2', f"'{query_data.knowledge_base_id}'")
        final_sql = final_sql.replace('$3', str(query_data.top_k))
        
        # 执行查询
        result = await db.execute(text(final_sql))

        # 处理结果
        rows = result.mappings().all()
        query_results = []

        for row in rows:
            similarity = row["similarity"]

            # 过滤低相似度的结果
            if similarity < query_data.similarity_threshold:
                continue

            query_result = AIKnowledgeQueryResult(
                chunk_id=row["id"],
                document_id=row["document_id"],
                document_title=row["document_title"],
                content=row["content"],
                similarity=similarity,
                chunk_metadata=row["chunk_metadata"],
                page_number=row["page_number"],
            )

            query_results.append(query_result)

        return query_results

    @staticmethod
    async def search_knowledge(
        db: AsyncSession,
        query: str,
        project_id: Optional[uuid.UUID] = None,
        tenant_id: Optional[uuid.UUID] = None,
        knowledge_base_ids: Optional[List[uuid.UUID]] = None,
        top_k: int = 5,
        similarity_threshold: float = 0.7,
        user_id: Optional[uuid.UUID] = None,
        include_metadata: bool = True
    ) -> List[Dict[str, Any]]:
        """
        跨知识库搜索知识
        
        Args:
            db: 数据库会话
            query: 搜索查询
            project_id: 项目ID
            tenant_id: 租户ID
            knowledge_base_ids: 指定知识库ID列表
            top_k: 返回结果数量
            similarity_threshold: 相似度阈值
            user_id: 用户ID
            include_metadata: 是否包含元数据
            
        Returns:
            搜索结果列表
        """
        try:
            # 获取可搜索的知识库
            available_kbs = []
            
            if knowledge_base_ids:
                # 使用指定的知识库
                for kb_id in knowledge_base_ids:
                    kb = await AIKnowledgeService.get_knowledge_base(db, kb_id)
                    if kb and kb.status == "active":
                        available_kbs.append(kb)
            else:
                # 获取项目或租户的所有活跃知识库
                conditions = {"status": "active"}
                if project_id:
                    conditions["project_id"] = project_id
                elif tenant_id:
                    conditions["tenant_id"] = tenant_id
                
                kbs, _ = await AIKnowledgeService.get_knowledge_bases(db, **conditions)
                available_kbs = kbs
            
            if not available_kbs:
                logger.warning(f"没有找到可用的知识库: project_id={project_id}, tenant_id={tenant_id}")
                return []
            
            all_results = []
            
            # 搜索每个知识库
            for kb in available_kbs:
                try:
                    if not kb.embedding_model_id:
                        logger.warning(f"知识库 {kb.id} 未配置嵌入模型，跳过搜索")
                        continue
                    
                    # 构建查询请求
                    from schemas.ai.knowledge import AIKnowledgeQueryRequest
                    query_request = AIKnowledgeQueryRequest(
                        knowledge_base_id=kb.id,
                        query=query,
                        top_k=top_k,
                        similarity_threshold=similarity_threshold
                    )
                    
                    # 查询知识库
                    kb_results = await AIKnowledgeService.query_knowledge_base(
                        db=db,
                        query_data=query_request,
                        user_id=user_id or uuid.uuid4()  # 如果没有用户ID，使用临时ID
                    )
                    
                    # 处理结果
                    for result in kb_results:
                        result_dict = {
                            "content": result.content,
                            "similarity": result.similarity,
                            "document_title": result.document_title,
                            "document_id": str(result.document_id),
                            "chunk_id": str(result.chunk_id),
                            "knowledge_base_id": str(kb.id),
                            "knowledge_base_name": kb.name
                        }
                        
                        if include_metadata:
                            result_dict.update({
                                "chunk_metadata": result.chunk_metadata,
                                "page_number": result.page_number,
                                "knowledge_base_type": kb.industry_type,
                                "embedding_model": kb.embedding_model.name if kb.embedding_model else None
                            })
                        
                        all_results.append(result_dict)
                        
                except Exception as e:
                    logger.error(f"搜索知识库 {kb.id} 失败: {e}")
                    continue
            
            # 按相似度排序并限制结果数量
            all_results.sort(key=lambda x: x["similarity"], reverse=True)
            return all_results[:top_k]
            
        except Exception as e:
            logger.error(f"知识库搜索失败: {e}")
            return []
    
    @staticmethod
    async def enhanced_search_with_reranking(
        db: AsyncSession,
        query: str,
        project_id: Optional[uuid.UUID] = None,
        tenant_id: Optional[uuid.UUID] = None,
        top_k: int = 5,
        similarity_threshold: float = 0.7,
        user_id: Optional[uuid.UUID] = None,
        enable_semantic_reranking: bool = True,
        query_expansion: bool = True
    ) -> Dict[str, Any]:
        """
        增强搜索功能，包含查询扩展和重排序
        
        Args:
            db: 数据库会话
            query: 搜索查询
            project_id: 项目ID
            tenant_id: 租户ID
            top_k: 返回结果数量
            similarity_threshold: 相似度阈值
            user_id: 用户ID
            enable_semantic_reranking: 启用语义重排序
            query_expansion: 启用查询扩展
            
        Returns:
            增强搜索结果
        """
        try:
            search_metadata = {
                "original_query": query,
                "expanded_queries": [],
                "search_time": time.time(),
                "techniques_used": []
            }
            
            # 查询扩展
            expanded_queries = [query]  # 原始查询
            if query_expansion:
                search_metadata["techniques_used"].append("query_expansion")
                # 简单的查询扩展：添加同义词、相关词
                expanded_queries.extend(await AIKnowledgeService._expand_query(query))
                search_metadata["expanded_queries"] = expanded_queries[1:]  # 不包含原始查询
            
            # 执行多查询搜索
            all_results = []
            for exp_query in expanded_queries:
                results = await AIKnowledgeService.search_knowledge(
                    db=db,
                    query=exp_query,
                    project_id=project_id,
                    tenant_id=tenant_id,
                    top_k=top_k * 2,  # 获取更多结果用于重排序
                    similarity_threshold=similarity_threshold * 0.8,  # 降低阈值获取更多候选
                    user_id=user_id,
                    include_metadata=True
                )
                all_results.extend(results)
            
            # 去重（基于chunk_id）
            unique_results = {}
            for result in all_results:
                chunk_id = result["chunk_id"]
                if chunk_id not in unique_results or result["similarity"] > unique_results[chunk_id]["similarity"]:
                    unique_results[chunk_id] = result
            
            deduplicated_results = list(unique_results.values())
            
            # 语义重排序
            if enable_semantic_reranking and len(deduplicated_results) > 1:
                search_metadata["techniques_used"].append("semantic_reranking")
                deduplicated_results = await AIKnowledgeService._semantic_rerank(
                    query, deduplicated_results
                )
            
            # 最终排序和截取
            final_results = sorted(deduplicated_results, key=lambda x: x["similarity"], reverse=True)[:top_k]
            
            # 计算搜索时间
            search_metadata["search_time"] = time.time() - search_metadata["search_time"]
            search_metadata["total_candidates"] = len(all_results)
            search_metadata["after_deduplication"] = len(deduplicated_results)
            search_metadata["final_results"] = len(final_results)
            
            return {
                "results": final_results,
                "metadata": search_metadata,
                "success": True
            }
            
        except Exception as e:
            logger.error(f"增强搜索失败: {e}")
            return {
                "results": [],
                "metadata": {"error": str(e)},
                "success": False
            }
    
    @staticmethod
    async def _expand_query(query: str) -> List[str]:
        """
        查询扩展：生成相关查询
        """
        try:
            expanded = []
            
            # 简单的同义词替换和相关词添加
            synonyms_map = {
                "问题": ["疑问", "困惑", "难题"],
                "产品": ["服务", "商品", "方案"],
                "价格": ["费用", "成本", "收费"],
                "使用": ["操作", "应用", "运行"],
                "设置": ["配置", "设定", "调整"],
                "错误": ["故障", "异常", "问题"],
                "帮助": ["协助", "支持", "指导"]
            }
            
            query_words = query.split()
            for word in query_words:
                if word in synonyms_map:
                    for synonym in synonyms_map[word]:
                        expanded_query = query.replace(word, synonym)
                        if expanded_query != query:
                            expanded.append(expanded_query)
            
            # 添加相关主题查询
            if "安装" in query:
                expanded.append(query + " 步骤")
                expanded.append(query + " 教程")
            
            if "错误" in query or "问题" in query:
                expanded.append(query + " 解决方案")
                expanded.append(query + " 故障排除")
            
            return expanded[:3]  # 限制扩展查询数量
            
        except Exception as e:
            logger.error(f"查询扩展失败: {e}")
            return []
    
    @staticmethod
    async def _semantic_rerank(query: str, results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        语义重排序：基于语义相关性重新排序结果
        """
        try:
            # 简单的基于关键词匹配的重排序
            query_words = set(query.lower().split())
            
            for result in results:
                content = result["content"].lower()
                title = result.get("document_title", "").lower()
                
                # 计算关键词匹配分数
                content_matches = sum(1 for word in query_words if word in content)
                title_matches = sum(1 for word in query_words if word in title)
                
                # 调整相似度分数
                keyword_boost = (content_matches * 0.1 + title_matches * 0.2)
                result["similarity"] += keyword_boost
                
                # 添加额外的语义特征
                result["keyword_matches"] = content_matches + title_matches
                result["title_relevance"] = title_matches > 0
            
            return results
            
        except Exception as e:
            logger.error(f"语义重排序失败: {e}")
            return results
    
    @staticmethod
    async def get_knowledge_statistics(
        db: AsyncSession,
        project_id: Optional[uuid.UUID] = None,
        tenant_id: Optional[uuid.UUID] = None
    ) -> Dict[str, Any]:
        """
        获取知识库统计信息
        """
        try:
            # 获取知识库列表
            conditions = {}
            if project_id:
                conditions["project_id"] = project_id
            elif tenant_id:
                conditions["tenant_id"] = tenant_id
                
            knowledge_bases, total_kbs = await AIKnowledgeService.get_knowledge_bases(db, **conditions)
            
            stats = {
                "total_knowledge_bases": total_kbs,
                "active_knowledge_bases": len([kb for kb in knowledge_bases if kb.status == "active"]),
                "total_documents": 0,
                "total_chunks": 0,
                "total_tokens": 0,
                "embedding_models_used": set(),
                "industry_types": {},
                "knowledge_base_details": []
            }
            
            for kb in knowledge_bases:
                stats["total_documents"] += kb.document_count or 0
                stats["total_chunks"] += kb.chunk_count or 0
                stats["total_tokens"] += kb.token_count or 0
                
                if kb.embedding_model:
                    stats["embedding_models_used"].add(kb.embedding_model.name)
                
                industry = kb.industry_type or "未分类"
                stats["industry_types"][industry] = stats["industry_types"].get(industry, 0) + 1
                
                stats["knowledge_base_details"].append({
                    "id": str(kb.id),
                    "name": kb.name,
                    "status": kb.status,
                    "document_count": kb.document_count or 0,
                    "chunk_count": kb.chunk_count or 0,
                    "industry_type": kb.industry_type,
                    "created_at": kb.created_at.isoformat() if kb.created_at else None
                })
            
            stats["embedding_models_used"] = list(stats["embedding_models_used"])
            
            return stats
            
        except Exception as e:
            logger.error(f"获取知识库统计失败: {e}")
            return {}
