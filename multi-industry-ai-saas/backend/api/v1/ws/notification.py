#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
WebSocket通知API
用于实时推送通知
"""

import logging
import uuid
import json
from datetime import datetime
from typing import Dict, Any, Optional
from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Depends, Query, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from db.database import get_db
from core.auth import get_current_user_ws
from models.user import User
from services.redis_manager import RedisManager

# 配置日志
logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter()

@router.websocket("/notifications")
async def websocket_notifications(
    websocket: WebSocket,
    user_id: str = Query(...),
    project_id: str = Query(...),
    token: str = Query(...),
    db: AsyncSession = Depends(get_db)
):
    """
    WebSocket通知端点

    Args:
        websocket: WebSocket连接
        user_id: 用户ID
        project_id: 项目ID
        token: 认证令牌
        db: 数据库会话
    """
    # 验证用户身份
    try:
        logger.info(f"WebSocket连接请求: user_id={user_id}, project_id={project_id}")

        # 验证令牌
        current_user = await get_current_user_ws(token, db)

        # 检查用户ID是否匹配
        if str(current_user.id) != user_id:
            logger.warning(f"WebSocket认证失败: 用户ID不匹配 (token_user_id={current_user.id}, request_user_id={user_id})")
            await websocket.close(code=status.WS_1008_POLICY_VIOLATION)
            return

        # 检查用户是否有权限访问项目
        # 这里可以添加更多的权限检查逻辑
        logger.info(f"WebSocket认证成功: user_id={user_id}, project_id={project_id}")

    except HTTPException as he:
        logger.warning(f"WebSocket认证失败 (HTTP异常): {he.detail}")
        try:
            await websocket.close(code=status.WS_1008_POLICY_VIOLATION)
        except Exception:
            pass  # 忽略关闭连接时的错误
        return
    except Exception as e:
        logger.error(f"WebSocket认证失败 (未预期的错误): {str(e)}")
        try:
            await websocket.close(code=status.WS_1011_INTERNAL_ERROR)
        except Exception:
            pass  # 忽略关闭连接时的错误
        return

    # 接受WebSocket连接
    await websocket.accept()

    try:
        # 注册WebSocket连接
        logger.info(f"注册WebSocket连接: user_id={user_id}, project_id={project_id}")
        redis_manager = await RedisManager.get_instance()
        await redis_manager.register_websocket(user_id, project_id, websocket)

        # 发送连接成功消息
        await websocket.send_text(json.dumps({
            "type": "connection_established",
            "message": "WebSocket连接已建立",
            "timestamp": datetime.now().isoformat()
        }))

        # 保持连接直到客户端断开
        while True:
            # 等待客户端消息，主要是为了检测断开连接
            try:
                data = await websocket.receive_text()
                logger.debug(f"收到WebSocket消息: {data[:100]}...")  # 只记录前100个字符

                # 处理客户端消息
                try:
                    message = json.loads(data)
                    message_type = message.get("type")

                    # 处理心跳消息
                    if message_type == "ping":
                        await websocket.send_text(json.dumps({
                            "type": "pong",
                            "timestamp": datetime.now().isoformat()
                        }))

                    # 处理确认消息
                    elif message_type == "ack":
                        notification_id = message.get("notification_id")
                        if notification_id:
                            logger.debug(f"客户端确认收到通知: {notification_id}")
                            # 这里可以添加更新通知状态的逻辑

                except json.JSONDecodeError:
                    logger.warning(f"无法解析WebSocket消息: {data[:100]}...")

            except WebSocketDisconnect:
                logger.info(f"WebSocket连接断开: 用户 {user_id}, 项目 {project_id}")
                break

    except WebSocketDisconnect:
        logger.info(f"WebSocket连接断开: 用户 {user_id}, 项目 {project_id}")
    except Exception as e:
        logger.error(f"WebSocket连接错误: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
    finally:
        # 注销WebSocket连接
        try:
            logger.info(f"注销WebSocket连接: user_id={user_id}, project_id={project_id}")
            redis_manager = await RedisManager.get_instance()
            await redis_manager.unregister_websocket(user_id, project_id)
        except Exception as e:
            logger.error(f"注销WebSocket连接失败: {str(e)}")
