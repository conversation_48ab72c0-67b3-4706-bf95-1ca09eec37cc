# 路径优化解决方案

## 方案概述
通过修改路由规则，使用路径参数而不是子域名来区分不同的MCP服务器。

## 当前架构 vs 优化架构

### 当前架构（基于子域名）
```
mcp-{server_id}.saas.houshanai.com/mcp
```

### 优化架构（基于路径）
```
saas.houshanai.com/mcp/{server_id}
```

## 实施方案

### 1. 修改Traefik路由配置

修改 `traefik/dynamic/services.yml`：

```yaml
http:
  routers:
    # 新增：MCP路径路由
    mcp-path-router:
      rule: "PathPrefix(`/mcp/`)"
      service: mcp-dynamic-service
      middlewares:
        - mcp-auth@file
        - sse-no-buffer@file
        - mcp-path-middleware@file
      priority: 250
      entryPoints:
        - web
        - websecure

  middlewares:
    # 新增：MCP路径中间件
    mcp-path-middleware:
      replacePath:
        path: "/mcp"
      headers:
        customRequestHeaders:
          X-MCP-Server-ID: ""  # 从路径提取
```

### 2. 修改MCP服务器适配器

修改 `mcp_server_adapter.py` 中的容器标签：

```python
# 移除基于Host的路由
# 改为基于路径的路由
traefik_labels = {
    "traefik.enable": "true",
    f"traefik.http.routers.mcp-{server_id}.rule": f"PathPrefix(`/mcp/{server_id}`)",
    f"traefik.http.routers.mcp-{server_id}.priority": "300",
    f"traefik.http.services.mcp-{server_id}.loadbalancer.server.port": str(port),
    f"traefik.http.routers.mcp-{server_id}.middlewares": "mcp-auth@file,sse-no-buffer@file"
}
```

### 3. 客户端使用方式

```json
{
  "mcpServers": {
    "my-mcp-server": {
      "command": "npx",
      "args": [
        "@modelcontextprotocol/server-fetch",
        "http://saas.houshanai.com/mcp/38be5e4a-7d0c-4ca7-ad50-bc69bd06e20d"
      ]
    }
  }
}
```

## 优势与劣势

### ✅ 优势
- 无需DNS配置
- 使用单一域名
- 路径清晰明确
- 易于理解和调试

### ❌ 劣势
- 需要修改现有代码
- 路径可能较长
- 需要重新部署所有MCP服务器
- 可能影响现有客户端

## 实施复杂度
- **代码修改**：中等
- **部署影响**：需要重启服务
- **客户端影响**：需要更新配置 