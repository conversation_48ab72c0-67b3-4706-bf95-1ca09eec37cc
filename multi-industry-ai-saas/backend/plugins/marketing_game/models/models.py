from sqlalchemy import Column, Integer, String, Float, <PERSON><PERSON><PERSON>, DateTime, ForeignKey, Text, JSON, Enum
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from sqlalchemy.dialects.postgresql import UUID
import enum
import uuid
from db.database import Base

class GameType(str, enum.Enum):
    """游戏类型枚举"""
    LUCKY_DRAW = "lucky_draw"  # 随机抽奖
    SCRATCH_CARD = "scratch_card"  # 刮刮卡
    WHEEL_OF_FORTUNE = "wheel_of_fortune"  # 幸运大转盘
    ADVENTURE_GAME = "adventure_game"  # 闯关游戏

class GameStatus(str, enum.Enum):
    """游戏状态枚举"""
    DRAFT = "draft"  # 草稿
    PUBLISHED = "published"  # 已发布
    ENDED = "ended"  # 已结束
    CANCELLED = "cancelled"  # 已取消

class PrizeType(str, enum.Enum):
    """奖品类型枚举"""
    PHYSICAL = "physical"  # 实物奖品
    COUPON = "coupon"  # 优惠券
    POINTS = "points"  # 积分
    VIRTUAL = "virtual"  # 虚拟物品

class MarketingType(str, enum.Enum):
    """营销类型枚举"""
    OPENING_PROMOTION = "opening_promotion"  # 开业营销
    ANNIVERSARY = "anniversary"  # 周年庆
    HOLIDAY_MARKETING = "holiday_marketing"  # 节假日营销
    CUSTOM_FESTIVAL = "custom_festival"  # 自造节
    DAILY_ACTIVITY = "daily_activity"  # 日常活动
    MEMBERSHIP_PROMOTION = "membership_promotion"  # 会员促销

class MarketingGame(Base):
    """营销游戏模型"""
    __tablename__ = "marketing_games"

    id = Column(Integer, primary_key=True, index=True)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id", ondelete="CASCADE"), nullable=False)
    name = Column(String(100), nullable=False, comment="游戏名称")
    title = Column(String(255), nullable=True, comment="游戏标题")
    description = Column(Text, nullable=True, comment="游戏描述")
    cover_image = Column(Text, nullable=True, comment="封面图片URL")
    game_type = Column(String(50), nullable=False, comment="游戏类型")
    marketing_type = Column(String(50), nullable=False, comment="营销类型")
    start_time = Column(DateTime, nullable=False, comment="开始时间")
    end_time = Column(DateTime, nullable=False, comment="结束时间")
    status = Column(String(20), default=GameStatus.DRAFT, comment="游戏状态")
    rules = Column(Text, nullable=True, comment="游戏规则")
    min_consumption = Column(Float, default=0, comment="最低消费金额")
    max_draws_per_user = Column(Integer, default=1, comment="每用户最大抽奖次数")
    settings = Column(JSON, nullable=True, comment="游戏设置")
    theme_style = Column(String(50), default="classic", comment="游戏主题风格")
    background_music_url = Column(String(500), nullable=True, comment="背景音乐URL")
    effect_sound_enabled = Column(Boolean, default=True, comment="是否启用音效")
    max_participants = Column(Integer, default=0, comment="最大参与人数,0表示无限制")
    require_phone = Column(Boolean, default=False, comment="是否需要手机号验证")
    require_receipt = Column(Boolean, default=False, comment="是否需要小票验证")
    require_membership = Column(Boolean, default=False, comment="是否需要会员身份")
    wechat_share_title = Column(String(255), nullable=True, comment="微信分享标题")
    wechat_share_description = Column(Text, nullable=True, comment="微信分享描述")
    wechat_share_image = Column(Text, nullable=True, comment="微信分享图片URL")
    enable_wechat_share = Column(Boolean, default=True, comment="是否启用微信分享")
    ai_assistant_enabled = Column(Boolean, default=False, comment="是否启用AI助手")
    ai_welcome_message = Column(Text, nullable=True, comment="AI欢迎消息")
    ai_help_prompts = Column(JSON, default=list, comment="AI帮助提示列表")
    ai_personality = Column(String(100), default="friendly", comment="AI助手性格(friendly/professional/playful)")
    use_enhanced_ui = Column(Boolean, default=False, comment="是否使用增强版界面")
    background_music = Column(Text, nullable=True, comment="背景音乐URL")
    sound_effects = Column(JSON, default=dict, comment="音效设置")
    animation_settings = Column(JSON, default=dict, comment="动画设置")
    enable_analytics = Column(Boolean, default=True, comment="是否启用数据分析")
    track_user_behavior = Column(Boolean, default=True, comment="是否追踪用户行为")
    generate_reports = Column(Boolean, default=True, comment="是否生成报告")
    marketing_goals = Column(JSON, default=dict, comment="营销目标设置")
    target_audience = Column(JSON, default=dict, comment="目标受众设置")
    promotion_strategy = Column(JSON, default=dict, comment="推广策略")
    enable_social_sharing = Column(Boolean, default=True, comment="是否启用社交分享")
    enable_friend_invitation = Column(Boolean, default=False, comment="是否启用好友邀请")
    invitation_rewards = Column(JSON, default=dict, comment="邀请奖励设置")
    location_restriction = Column(Boolean, default=False, comment="是否限制地理位置")
    allowed_locations = Column(JSON, default=list, comment="允许的地理位置列表")
    location_radius = Column(Float, default=0, comment="位置半径限制(米)")
    total_participants = Column(Integer, default=0, comment="总参与人数")
    total_draws = Column(Integer, default=0, comment="总抽奖次数")
    total_prizes_given = Column(Integer, default=0, comment="总发放奖品数")
    total_cost = Column(Float, default=0, comment="总成本")
    store_ids = Column(JSON, default=list, comment="关联门店ID列表")
    is_global = Column(Boolean, default=False, comment="是否全局游戏(所有门店)")
    created_at = Column(DateTime, server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now(), comment="更新时间")
    created_by = Column(UUID(as_uuid=True), nullable=True, comment="创建者ID")
    extra_data = Column(JSON, default=dict, comment="扩展数据")

    # 关系
    prizes = relationship("GamePrize", back_populates="game", cascade="all, delete-orphan")
    participants = relationship("GameParticipant", back_populates="game", cascade="all, delete-orphan")
    records = relationship("GameRecord", back_populates="game", cascade="all, delete-orphan")
    project = relationship("Project", foreign_keys=[project_id], backref="marketing_games")

class GamePrize(Base):
    """游戏奖品模型"""
    __tablename__ = "game_prizes"

    id = Column(Integer, primary_key=True, index=True)
    game_id = Column(Integer, ForeignKey("marketing_games.id", ondelete="CASCADE"), nullable=False)
    name = Column(String(100), nullable=False, comment="奖品名称")
    description = Column(Text, nullable=True, comment="奖品描述")
    prize_type = Column(String(20), nullable=False, comment="奖品类型")
    value = Column(Float, default=0, comment="奖品价值")
    quantity = Column(Integer, default=0, comment="奖品数量")
    remaining = Column(Integer, default=0, comment="剩余数量")
    probability = Column(Float, default=0, comment="中奖概率")
    image_url = Column(String(255), nullable=True, comment="奖品图片")
    is_default = Column(Boolean, default=False, comment="是否默认奖品")
    effective_date = Column(DateTime, nullable=True, comment="奖品生效日期")
    expiry_date = Column(DateTime, nullable=True, comment="奖品失效日期")
    effective_time_start = Column(String(8), nullable=True, comment="每日生效时间（HH:MM:SS）")
    effective_time_end = Column(String(8), nullable=True, comment="每日失效时间（HH:MM:SS）")
    special_conditions = Column(JSON, nullable=True, comment="特殊中奖条件")
    daily_limit = Column(Integer, default=0, comment="每日限量（0为不限制）")
    hourly_limit = Column(Integer, default=0, comment="每小时限量（0为不限制）")
    user_limit = Column(Integer, default=0, comment="每用户限量（0为不限制）")
    min_days_since_game_start = Column(Integer, default=0, comment="游戏开始后第几天才可中奖")
    max_days_since_game_start = Column(Integer, default=0, comment="游戏开始后第几天停止中奖（0为不限制）")
    weight_factor = Column(Float, default=1.0, comment="权重因子（影响实际中奖概率）")
    is_guaranteed = Column(Boolean, default=False, comment="是否保底奖品")
    guaranteed_condition = Column(JSON, nullable=True, comment="保底触发条件")
    animation_style = Column(String(50), default="default", comment="中奖动画样式")
    sound_effect_url = Column(String(500), nullable=True, comment="中奖音效URL")
    created_at = Column(DateTime, server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now(), comment="更新时间")

    # 关系
    game = relationship("MarketingGame", back_populates="prizes")
    records = relationship("GameRecord", back_populates="prize")

class GameParticipant(Base):
    """游戏参与者模型"""
    __tablename__ = "game_participants"

    id = Column(Integer, primary_key=True, index=True)
    game_id = Column(Integer, ForeignKey("marketing_games.id", ondelete="CASCADE"), nullable=False)
    user_id = Column(Integer, nullable=True, comment="用户ID")
    member_id = Column(Integer, nullable=True, comment="会员ID")
    name = Column(String(50), nullable=True, comment="参与者姓名")
    phone = Column(String(20), nullable=True, comment="参与者电话")
    draws_count = Column(Integer, default=0, comment="抽奖次数")
    is_winner = Column(Boolean, default=False, comment="是否中奖")
    is_fixed_winner = Column(Boolean, default=False, comment="是否内定中奖")
    total_consumption = Column(Float, default=0, comment="总消费金额")
    last_draw_time = Column(DateTime, nullable=True, comment="最后抽奖时间")
    ip_address = Column(String(50), nullable=True, comment="IP地址")
    user_agent = Column(Text, nullable=True, comment="用户代理")
    created_at = Column(DateTime, server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now(), comment="更新时间")

    # 关系
    game = relationship("MarketingGame", back_populates="participants")
    records = relationship("GameRecord", back_populates="participant")

class GameRecord(Base):
    """游戏记录模型"""
    __tablename__ = "game_records"

    id = Column(Integer, primary_key=True, index=True)
    game_id = Column(Integer, ForeignKey("marketing_games.id", ondelete="CASCADE"), nullable=False)
    participant_id = Column(Integer, ForeignKey("game_participants.id", ondelete="CASCADE"), nullable=False)
    prize_id = Column(Integer, ForeignKey("game_prizes.id", ondelete="SET NULL"), nullable=True)
    receipt_id = Column(String(100), nullable=True, comment="小票ID")
    consumption_amount = Column(Float, default=0, comment="消费金额")
    is_win = Column(Boolean, default=False, comment="是否中奖")
    draw_time = Column(DateTime, server_default=func.now(), comment="抽奖时间")
    claim_status = Column(String(20), default="unclaimed", comment="领取状态")
    claim_time = Column(DateTime, nullable=True, comment="领取时间")
    draw_method = Column(String(20), default="random", comment="抽奖方式(random/guaranteed/fixed)")
    probability_used = Column(Float, nullable=True, comment="实际使用的中奖概率")
    random_number = Column(Float, nullable=True, comment="抽奖随机数")
    ip_address = Column(String(50), nullable=True, comment="抽奖IP地址")
    device_info = Column(JSON, nullable=True, comment="设备信息")
    location_info = Column(JSON, nullable=True, comment="位置信息")
    remark = Column(Text, nullable=True, comment="备注")
    created_at = Column(DateTime, server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime, server_default=func.now(), onupdate=func.now(), comment="更新时间")

    # 关系
    game = relationship("MarketingGame", back_populates="records")
    participant = relationship("GameParticipant", back_populates="records")
    prize = relationship("GamePrize", back_populates="records")
