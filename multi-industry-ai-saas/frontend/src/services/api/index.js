import api, {
  getUserRole,
  isSuperAdmin,
  isServiceProviderAdmin,
  isTenantAdmin,
  isProjectAdmin,
  getProjectId,
  getTenantId,
  getServiceProviderId,
  setProjectId,
  setTenantId,
  setServiceProviderId
} from './httpClient';

import auth from './auth';
import admin from './admin';
import serviceProvider from './serviceProvider';
import tenant from './tenant';
import project from './project';

/**
 * API服务
 */
const apiService = {
  // 基础工具
  getUserRole,
  isSuperAdmin,
  isServiceProviderAdmin,
  isTenantAdmin,
  isProjectAdmin,
  getProjectId,
  getTenantId,
  getServiceProviderId,
  setProjectId,
  setTenantId,
  setServiceProviderId,

  // 认证服务
  auth,

  // 管理员服务（最顶层超级管理员）
  admin,

  // 服务商服务
  serviceProvider,

  // 租户服务
  tenant,

  // 项目服务
  project
};

export {
  // 基础工具
  api,
  getUserRole,
  isSuperAdmin,
  isServiceProviderAdmin,
  isTenantAdmin,
  isProjectAdmin,
  getProjectId,
  getTenantId,
  getServiceProviderId,
  setProjectId,
  setTenantId,
  setServiceProviderId,

  // 认证服务
  auth,

  // 管理员服务（最顶层超级管理员）
  admin,

  // 服务商服务
  serviceProvider,

  // 租户服务
  tenant,

  // 项目服务
  project
};

export default apiService;
