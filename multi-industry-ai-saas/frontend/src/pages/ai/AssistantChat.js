import React, { useState, useEffect, useRef, useCallback } from 'react';
import { 
  Layout, Input, Button, Avatar, Typography, Space, 
  Upload, message, Spin, Tooltip, Drawer, Switch, Slider,
  Tag, Modal, Alert, Card
} from 'antd';
import { 
  SendOutlined, PaperClipOutlined, SettingOutlined, 
  RobotOutlined, UserOutlined, EyeOutlined, SoundOutlined,
  AudioOutlined, StopOutlined, PlayCircleOutlined, PauseCircleOutlined,
  DownloadOutlined, FullscreenOutlined, FullscreenExitOutlined, ArrowLeftOutlined,
  CodeOutlined, ToolOutlined, VideoCameraOutlined, GlobalOutlined,
  FileOutlined, PictureOutlined, AudioMutedOutlined, ClearOutlined,
  CloudServerOutlined
} from '@ant-design/icons';
import { useParams, useNavigate } from 'react-router-dom';
import { useProject } from '../../contexts/ProjectContext';
import apiService from '../../services/api';

const { Header, Content, Footer } = Layout;
const { TextArea } = Input;
const { Text, Title } = Typography;

/**
 * AI助手聊天对话页面
 * 支持多模态交互和MCP工具调用
 */
const AIAssistantChat = () => {
  const { assistantId } = useParams();
  const navigate = useNavigate();
  const { currentProject } = useProject();
  const [assistant, setAssistant] = useState(null);
  const [messages, setMessages] = useState([]);
  const [inputValue, setInputValue] = useState('');
  const [loading, setLoading] = useState(false);
  const [sending, setSending] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [settingsVisible, setSettingsVisible] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [fontSize, setFontSize] = useState(14);
  const [theme, setTheme] = useState('light');
  const [autoScroll, setAutoScroll] = useState(true);
  const [showTyping, setShowTyping] = useState(false);
  const [mcpTools, setMcpTools] = useState([]);
  const [mcpServers, setMcpServers] = useState([]);
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewImage, setPreviewImage] = useState('');
  const [recordingTime, setRecordingTime] = useState(0);
  const [audioPlaying, setAudioPlaying] = useState(null);
  const [currentThreadId, setCurrentThreadId] = useState(null);
  
  const messagesEndRef = useRef(null);
  const inputRef = useRef(null);
  const mediaRecorderRef = useRef(null);
  const audioChunksRef = useRef([]);
  const chatContainerRef = useRef(null);
  const recordingTimerRef = useRef(null);
  const audioRef = useRef(null);

  // 检测设备类型
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkDevice = () => {
      const width = window.innerWidth;
      const userAgent = navigator.userAgent;
      
      if (width <= 768 || /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent)) {
        setIsMobile(true);
      } else {
        setIsMobile(false);
      }
    };

    checkDevice();
    window.addEventListener('resize', checkDevice);
    return () => window.removeEventListener('resize', checkDevice);
  }, []);

  // 获取助手信息
  const fetchAssistant = useCallback(async () => {
    try {
      setLoading(true);
      const response = await apiService.project.ai.aiAssistants.get(currentProject.id, assistantId);
      setAssistant(response.data);
      
      // 尝试恢复对话历史
      const conversationKey = `conversation_${assistantId}`;
      const savedConversation = localStorage.getItem(conversationKey);
      
      if (savedConversation) {
        try {
          const { messages: savedMessages, threadId, lastUpdated } = JSON.parse(savedConversation);
          
          // 检查是否是最近的对话（24小时内）
          const lastUpdate = new Date(lastUpdated);
          const now = new Date();
          const hoursDiff = (now - lastUpdate) / (1000 * 60 * 60);
          
          if (hoursDiff < 24 && savedMessages && savedMessages.length > 0) {
            setMessages(savedMessages);
            setCurrentThreadId(threadId);
            console.log('恢复了对话历史:', savedMessages.length, '条消息');
          } else {
            // 清除过期的对话历史
            localStorage.removeItem(conversationKey);
            // 添加欢迎消息
            if (response.data.welcome_message) {
              setMessages([{
                id: Date.now(),
                type: 'assistant',
                content: response.data.welcome_message,
                timestamp: new Date()
              }]);
            }
          }
        } catch (e) {
          console.error('恢复对话历史失败:', e);
          // 添加欢迎消息
          if (response.data.welcome_message) {
            setMessages([{
              id: Date.now(),
              type: 'assistant',
              content: response.data.welcome_message,
              timestamp: new Date()
            }]);
          }
        }
      } else {
        // 添加欢迎消息
        if (response.data.welcome_message) {
          setMessages([{
            id: Date.now(),
            type: 'assistant',
            content: response.data.welcome_message,
            timestamp: new Date()
          }]);
        }
      }
    } catch (error) {
      console.error('获取助手信息失败:', error);
      message.error('助手不存在或已被删除');
    } finally {
      setLoading(false);
    }
  }, [currentProject?.id, assistantId]);

  const fetchMcpTools = useCallback(async () => {
    try {
      // 获取MCP服务器列表和工具
      const serversResponse = await apiService.project.ai.mcpServers.list(currentProject.id);
      const servers = serversResponse.data || serversResponse || [];
      
      // 保存服务器信息
      setMcpServers(servers);
      
      let allTools = [];
      for (const server of servers) {
        try {
          const tools = await apiService.project.ai.mcpServers.getTools(currentProject.id, server.id);
          const toolsArray = Array.isArray(tools) ? tools : (tools.data || []);
          // 为每个工具添加服务器信息
          const serverTools = toolsArray.map(tool => ({
            ...tool,
            serverId: server.id,
            serverName: server.name,
            display_name: tool.name || tool.display_name,
            id: `${server.id}_${tool.name}` // 创建唯一ID
          }));
          allTools = [...allTools, ...serverTools];
        } catch (toolError) {
          console.warn(`获取服务器 ${server.name} 工具失败:`, toolError);
        }
      }
      
      setMcpTools(allTools);
    } catch (error) {
      console.error('获取MCP工具失败:', error);
    }
  }, [currentProject?.id]);

  // 获取助手信息
  useEffect(() => {
    const fetchData = async () => {
      if (assistantId && currentProject?.id) {
        await fetchAssistant();
        await fetchMcpTools();
      }
    };
    fetchData();
  }, [assistantId, currentProject?.id, fetchAssistant, fetchMcpTools]);

  // 自动滚动到底部
  useEffect(() => {
    if (autoScroll && messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages, autoScroll]);

  // 发送消息
  const sendMessage = async (content, type = 'text', file = null, toolCall = null) => {
    if (!content.trim() && !file && !toolCall) return;

    const userMessage = {
      id: Date.now(),
      type: 'user',
      content: content || '',
      file: file,
      toolCall: toolCall,
      contentType: type,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setSending(true);
    setShowTyping(true);

    try {
      // 构建请求数据
      const requestData = {
        assistant_id: assistantId,
        content: content,
        content_type: type,
        file_ids: file ? [file.name] : [],
        tool_call: toolCall,
        thread_id: currentThreadId // 使用当前线程ID
      };

      const response = await apiService.project.ai.aiAssistants.chat(currentProject.id, assistantId, requestData);

      // 根据后端返回的数据结构提取响应内容
      const responseData = response.data || response;
      const assistantReply = responseData.response || responseData.content || '';
      
      const assistantMessage = {
        id: Date.now() + 1,
        type: 'assistant',
        content: assistantReply,
        toolResults: responseData.toolResults,
        timestamp: new Date(),
        thread_id: responseData.thread_id // 保存线程ID
      };
      
      setMessages(prev => [...prev, assistantMessage]);
      setShowTyping(false);
      
      // 更新当前线程ID
      if (responseData.thread_id && !currentThreadId) {
        setCurrentThreadId(responseData.thread_id);
      }
      
      // 保存对话历史到localStorage
      const conversationKey = `conversation_${assistantId}`;
      const updatedMessages = [...messages, userMessage, assistantMessage];
      localStorage.setItem(conversationKey, JSON.stringify({
        messages: updatedMessages,
        threadId: responseData.thread_id || currentThreadId,
        lastUpdated: new Date().toISOString()
      }));
      
      // 如果支持语音输出，自动播放
      if (assistant?.capabilities?.supports_text_to_speech && !isMuted) {
        speakText(assistantMessage.content);
      }
    } catch (error) {
      console.error('发送消息失败:', error);
      message.error('发送失败，请重试');
      setShowTyping(false);
      
      // 移除用户消息，因为发送失败
      setMessages(prev => prev.slice(0, -1));
    } finally {
      setSending(false);
    }
  };

  // 调用MCP工具
  const callMcpTool = async (tool, parameters = {}) => {
    try {
      let response;
      
      // 根据工具类型选择不同的API调用方式
      if (tool.serverId) {
        // 通过MCP服务器API调用工具
        response = await apiService.project.ai.mcpServers.executeTools(currentProject.id, tool.serverId, {
          tool_name: tool.name,
          parameters,
          session_id: `session_${Date.now()}`
        });
      } else {
        // 使用传统MCP工具API
        response = await apiService.project.ai.mcpTools.execute(currentProject.id, tool.id, {
          parameters,
          session_id: `session_${Date.now()}`
        });
      }
      
      if (response.success || response.data) {
        await sendMessage(`调用工具 ${tool.display_name}`, 'tool', null, {
          tool: tool.display_name,
          parameters,
          result: response.data || response.result,
          serverName: tool.serverName
        });
      } else {
        message.error(`工具调用失败: ${response.message || '未知错误'}`);
      }
    } catch (error) {
      console.error('调用MCP工具失败:', error);
      message.error('工具调用失败');
    }
  };

  // 语音转文字
  const startRecording = async () => {
    if (!assistant?.capabilities?.supports_speech_to_text) {
      message.warning('当前助手不支持语音输入');
      return;
    }

    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const mediaRecorder = new MediaRecorder(stream);
      mediaRecorderRef.current = mediaRecorder;
      audioChunksRef.current = [];

      mediaRecorder.ondataavailable = (event) => {
        audioChunksRef.current.push(event.data);
      };

      mediaRecorder.onstop = async () => {
        const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/wav' });
        const audioFile = new File([audioBlob], 'voice.wav', { type: 'audio/wav' });
        
        // 调用语音转文字API
        try {
          const response = await apiService.project.ai.aiAssistants.speechToText(currentProject.id, assistantId, audioFile);
          const transcribedText = response.data.text;
          await sendMessage(transcribedText, 'audio', audioFile);
        } catch (error) {
          console.error('语音转文字失败:', error);
          // 如果语音转文字失败，直接发送音频文件
          await sendMessage('语音消息', 'audio', audioFile);
        }
        
        stream.getTracks().forEach(track => track.stop());
        clearInterval(recordingTimerRef.current);
        setRecordingTime(0);
      };

      mediaRecorder.start();
      setIsRecording(true);
      
      // 开始计时
      recordingTimerRef.current = setInterval(() => {
        setRecordingTime(prev => prev + 1);
      }, 1000);
    } catch (error) {
      console.error('录音失败:', error);
      message.error('无法访问麦克风');
    }
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
    }
  };

  // 文字转语音
  const speakText = (text) => {
    if (!assistant?.capabilities?.supports_text_to_speech || isMuted) return;
    
    const utterance = new SpeechSynthesisUtterance(text);
    utterance.lang = 'zh-CN';
    utterance.rate = 0.9;
    speechSynthesis.speak(utterance);
  };

  // 播放音频
  const playAudio = (audioUrl, messageId) => {
    if (audioPlaying === messageId) {
      audioRef.current?.pause();
      setAudioPlaying(null);
    } else {
      if (audioRef.current) {
        audioRef.current.src = audioUrl;
        audioRef.current.play();
        setAudioPlaying(messageId);
        
        audioRef.current.onended = () => {
          setAudioPlaying(null);
        };
      }
    }
  };

  // 处理文件上传
  const handleFileUpload = (file) => {
    if (!assistant?.capabilities?.supports_file_upload) {
      message.warning('当前助手不支持文件上传');
      return false;
    }

    const maxSize = (assistant.capabilities.max_file_size || 10) * 1024 * 1024; // MB to bytes
    if (file.size > maxSize) {
      message.error(`文件大小不能超过 ${assistant.capabilities.max_file_size || 10}MB`);
      return false;
    }

    const fileType = file.type.split('/')[0];
    const fileExt = file.name.split('.').pop().toLowerCase();

    if (fileType === 'image') {
      if (!assistant.capabilities.supports_vision) {
        message.warning('当前助手不支持图像处理');
        return false;
      }
      const supportedFormats = assistant.capabilities.supported_image_formats || ['jpg', 'jpeg', 'png', 'gif', 'webp'];
      if (!supportedFormats.includes(fileExt)) {
        message.error(`不支持的图像格式，支持格式：${supportedFormats.join(', ')}`);
        return false;
      }
      sendMessage(`上传了图片：${file.name}`, 'image', file);
    } else if (fileType === 'audio') {
      if (!assistant.capabilities.supports_audio_input) {
        message.warning('当前助手不支持音频处理');
        return false;
      }
      const supportedFormats = assistant.capabilities.supported_audio_formats || ['mp3', 'wav', 'ogg', 'm4a'];
      if (!supportedFormats.includes(fileExt)) {
        message.error(`不支持的音频格式，支持格式：${supportedFormats.join(', ')}`);
        return false;
      }
      sendMessage(`上传了音频：${file.name}`, 'audio', file);
    } else if (fileType === 'video') {
      if (!assistant.capabilities.supports_video_understanding) {
        message.warning('当前助手不支持视频处理');
        return false;
      }
      sendMessage(`上传了视频：${file.name}`, 'video', file);
    } else {
      sendMessage(`上传了文件：${file.name}`, 'file', file);
    }

    return false; // 阻止默认上传
  };

  // 全屏切换
  const toggleFullscreen = () => {
    if (!isFullscreen) {
      if (chatContainerRef.current?.requestFullscreen) {
        chatContainerRef.current.requestFullscreen();
      }
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen();
      }
    }
    setIsFullscreen(!isFullscreen);
  };

  // 清空对话
  const clearMessages = () => {
    const welcomeMessages = assistant?.welcome_message ? [{
      id: Date.now(),
      type: 'assistant',
      content: assistant.welcome_message,
      timestamp: new Date()
    }] : [];
    
    setMessages(welcomeMessages);
    setCurrentThreadId(null);
    
    // 清除保存的对话历史
    const conversationKey = `conversation_${assistantId}`;
    localStorage.removeItem(conversationKey);
  };

  // 渲染能力标签
  const renderCapabilityTags = (capabilities) => {
    if (!capabilities) return null;
    
    const tags = [];
    
    if (capabilities.supports_vision) {
      tags.push(<Tag key="vision" color="blue" icon={<EyeOutlined />} size="small">视觉</Tag>);
    }
    if (capabilities.supports_audio_input || capabilities.supports_audio_output) {
      tags.push(<Tag key="audio" color="green" icon={<AudioOutlined />} size="small">音频</Tag>);
    }
    if (capabilities.supports_speech_to_text || capabilities.supports_text_to_speech) {
      tags.push(<Tag key="speech" color="orange" icon={<SoundOutlined />} size="small">语音</Tag>);
    }
    if (capabilities.supports_file_upload) {
      tags.push(<Tag key="file" color="purple" icon={<FileOutlined />} size="small">文件</Tag>);
    }
    if (capabilities.supports_web_search) {
      tags.push(<Tag key="web" color="cyan" icon={<GlobalOutlined />} size="small">搜索</Tag>);
    }
    if (capabilities.supports_code_execution) {
      tags.push(<Tag key="code" color="red" icon={<CodeOutlined />} size="small">代码</Tag>);
    }
    if (capabilities.supports_memory) {
      tags.push(<Tag key="memory" color="gold" icon={<ToolOutlined />} size="small">记忆</Tag>);
    }
    if (capabilities.supports_thinking) {
      tags.push(<Tag key="thinking" color="magenta" icon={<ToolOutlined />} size="small">思考</Tag>);
    }
    
    return tags;
  };

  // 渲染消息
  const renderMessage = (msg) => {
    const isUser = msg.type === 'user';
    
    return (
      <div
        key={msg.id}
        className={`message ${isUser ? 'user-message' : 'assistant-message'}`}
        style={{ 
          fontSize: `${fontSize}px`, 
          marginBottom: '16px',
          display: 'flex',
          justifyContent: isUser ? 'flex-end' : 'flex-start'
        }}
      >
        <div style={{ 
          display: 'flex', 
          alignItems: 'flex-start', 
          gap: '8px',
          flexDirection: isUser ? 'row-reverse' : 'row',
          maxWidth: '70%'
        }}>
          <Avatar
            size={isMobile ? 32 : 40}
            icon={isUser ? <UserOutlined /> : <RobotOutlined />}
            src={isUser ? null : assistant?.avatar}
            style={{ backgroundColor: isUser ? '#1890ff' : '#52c41a' }}
          />
          <div style={{ flex: 1 }}>
            <div style={{
              background: isUser ? '#1890ff' : 'white',
              color: isUser ? 'white' : 'black',
              padding: '12px 16px',
              borderRadius: '12px',
              boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
              wordWrap: 'break-word'
            }}>
              {msg.file && (
                <div style={{ marginBottom: '8px' }}>
                  {msg.file.type.startsWith('image/') ? (
                    <div style={{ position: 'relative' }}>
                      <img
                        src={URL.createObjectURL(msg.file)}
                        alt={msg.file.name}
                        style={{ 
                          maxWidth: '200px', 
                          maxHeight: '200px', 
                          borderRadius: '8px',
                          cursor: 'pointer'
                        }}
                        onClick={() => {
                          setPreviewImage(URL.createObjectURL(msg.file));
                          setPreviewVisible(true);
                        }}
                      />
                      <div style={{ 
                        position: 'absolute', 
                        top: 4, 
                        right: 4, 
                        background: 'rgba(0,0,0,0.5)', 
                        borderRadius: '4px',
                        padding: '2px 6px'
                      }}>
                        <EyeOutlined style={{ color: 'white', fontSize: '12px' }} />
                      </div>
                    </div>
                  ) : msg.file.type.startsWith('audio/') ? (
                    <div style={{ 
                      display: 'flex', 
                      alignItems: 'center', 
                      gap: '8px',
                      padding: '8px',
                      background: 'rgba(0,0,0,0.05)',
                      borderRadius: '8px'
                    }}>
                      <Button
                        type="text"
                        size="small"
                        icon={audioPlaying === msg.id ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
                        onClick={() => playAudio(URL.createObjectURL(msg.file), msg.id)}
                      />
                      <span style={{ fontSize: '12px' }}>{msg.file.name}</span>
                      <span style={{ fontSize: '10px', color: '#666' }}>
                        {(msg.file.size / 1024).toFixed(1)}KB
                      </span>
                    </div>
                  ) : msg.file.type.startsWith('video/') ? (
                    <video
                      controls
                      style={{ 
                        maxWidth: '300px', 
                        maxHeight: '200px', 
                        borderRadius: '8px'
                      }}
                    >
                      <source src={URL.createObjectURL(msg.file)} type={msg.file.type} />
                      您的浏览器不支持视频播放
                    </video>
                  ) : (
                    <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                      <FileOutlined style={{ fontSize: '24px' }} />
                      <div>
                        <div style={{ fontWeight: 'bold' }}>{msg.file.name}</div>
                        <div style={{ fontSize: '12px', opacity: 0.7 }}>
                          {(msg.file.size / 1024).toFixed(1)}KB
                        </div>
                      </div>
                      <Button
                        type="text"
                        size="small"
                        icon={<DownloadOutlined />}
                        onClick={() => {
                          const url = URL.createObjectURL(msg.file);
                          const a = document.createElement('a');
                          a.href = url;
                          a.download = msg.file.name;
                          a.click();
                          URL.revokeObjectURL(url);
                        }}
                      />
                    </div>
                  )}
                </div>
              )}
              
              {msg.toolCall && (
                <div style={{ marginBottom: '8px' }}>
                  <Tag icon={<ToolOutlined />} color="purple">
                    调用工具: {msg.toolCall.tool}
                  </Tag>
                </div>
              )}
              
              {msg.toolResults && (
                <div style={{ marginBottom: '8px' }}>
                  <Alert
                    message="工具执行结果"
                    description={
                      <pre style={{ fontSize: '12px', margin: 0 }}>
                        {JSON.stringify(msg.toolResults, null, 2)}
                      </pre>
                    }
                    type="info"
                    size="small"
                  />
                </div>
              )}
              
              {msg.content && <div>{msg.content}</div>}
            </div>
            <div style={{ fontSize: '12px', color: '#999', marginTop: '4px' }}>
              {new Date(msg.timestamp).toLocaleTimeString()}
            </div>
          </div>
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>加载中...</div>
      </div>
    );
  }

  if (!assistant) {
    return (
      <div style={{ display: 'flex', flexDirection: 'column', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <Title level={3}>助手不存在</Title>
        <Text>请检查链接是否正确</Text>
      </div>
    );
  }

  return (
    <Layout style={{ height: '100vh' }} ref={chatContainerRef}>
      <Header style={{ 
        background: 'linear-gradient(135deg, #1890ff 0%, #722ed1 100%)',
        padding: '0 16px',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
          <Button
            type="text"
            icon={<ArrowLeftOutlined />}
            onClick={() => navigate('/project/ai/assistants')}
            style={{ color: 'white' }}
          >
            返回
          </Button>
          <Avatar
            size={isMobile ? 32 : 40}
            icon={<RobotOutlined />}
            src={assistant.avatar}
            style={{ backgroundColor: '#52c41a' }}
          />
          <div>
            <Title level={isMobile ? 5 : 4} style={{ margin: 0, color: 'white' }}>
              {assistant.name}
            </Title>
            {!isMobile && (
              <div style={{ display: 'flex', gap: '4px', marginTop: '4px' }}>
                {renderCapabilityTags(assistant.capabilities)}
              </div>
            )}
          </div>
        </div>
        
        <div>
          <Space>
            {assistant.capabilities?.supports_text_to_speech && (
              <Tooltip title={isMuted ? '开启语音' : '关闭语音'}>
                <Button
                  type="text"
                  icon={isMuted ? <AudioMutedOutlined /> : <AudioOutlined />}
                  onClick={() => setIsMuted(!isMuted)}
                  style={{ color: 'white' }}
                />
              </Tooltip>
            )}
            
            <Tooltip title="设置">
              <Button
                type="text"
                icon={<SettingOutlined />}
                onClick={() => setSettingsVisible(true)}
                style={{ color: 'white' }}
              />
            </Tooltip>
            
            {!isMobile && (
              <Tooltip title={isFullscreen ? '退出全屏' : '全屏'}>
                <Button
                  type="text"
                  icon={isFullscreen ? <FullscreenExitOutlined /> : <FullscreenOutlined />}
                  onClick={toggleFullscreen}
                  style={{ color: 'white' }}
                />
              </Tooltip>
            )}
          </Space>
        </div>
      </Header>

      <Content style={{ padding: '16px', overflow: 'auto', background: '#f5f5f5' }}>
        <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
          {messages.length === 0 && (
            <div style={{ 
              textAlign: 'center', 
              padding: '40px 20px',
              color: '#666'
            }}>
              <RobotOutlined style={{ fontSize: '48px', marginBottom: '16px' }} />
              <div style={{ fontSize: '16px', marginBottom: '8px' }}>
                欢迎与 {assistant.name} 对话
              </div>
              <div style={{ fontSize: '14px' }}>
                {assistant.description || '我是您的AI助手，有什么可以帮助您的吗？'}
              </div>
              {assistant.capabilities && (
                <div style={{ marginTop: '16px' }}>
                  <div style={{ fontSize: '12px', marginBottom: '8px' }}>支持的功能：</div>
                  <div style={{ display: 'flex', justifyContent: 'center', flexWrap: 'wrap', gap: '4px' }}>
                    {renderCapabilityTags(assistant.capabilities)}
                  </div>
                </div>
              )}
              {(assistant.mcp_tools?.length > 0 || assistant.mcp_server_ids?.length > 0) && (
                <div style={{ marginTop: '16px' }}>
                  <div style={{ fontSize: '12px', marginBottom: '8px' }}>可用MCP工具：</div>
                  <div style={{ display: 'flex', justifyContent: 'center', flexWrap: 'wrap', gap: '4px' }}>
                    {/* 显示传统MCP工具 */}
                    {assistant.mcp_tools?.slice(0, 2).map(toolId => {
                      const tool = mcpTools.find(t => t.id === toolId);
                      return tool ? (
                        <Tag key={toolId} size="small" color="processing">
                          {tool.display_name}
                        </Tag>
                      ) : null;
                    })}
                    
                    {/* 显示MCP服务器工具 */}
                    {assistant.mcp_server_ids?.slice(0, 2).map(serverId => {
                      // 从mcpServers数组中查找服务器信息
                      const server = mcpServers?.find(s => s.id === serverId);
                      const serverName = server ? server.name : `MCP服务器 ${serverId.slice(0, 8)}`;
                      const toolCount = server ? (server.available_tools?.length || 0) : 0;
                      return (
                        <Tag key={serverId} size="small" color="blue">
                          {serverName} ({toolCount}个工具)
                        </Tag>
                      );
                    })}
                    
                    {(assistant.mcp_tools?.length > 2 || assistant.mcp_server_ids?.length > 2) && (
                      <Tag size="small">+更多</Tag>
                    )}
                  </div>
                </div>
              )}
            </div>
          )}
          
          {messages.map(renderMessage)}
          
          {showTyping && (
            <div style={{ display: 'flex', alignItems: 'flex-start', gap: '8px', marginBottom: '16px' }}>
              <Avatar
                size={isMobile ? 32 : 40}
                icon={<RobotOutlined />}
                src={assistant.avatar}
                style={{ backgroundColor: '#52c41a' }}
              />
              <div style={{
                background: 'white',
                padding: '12px 16px',
                borderRadius: '12px',
                boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)'
              }}>
                <div style={{ display: 'flex', gap: '4px' }}>
                  <span style={{ animation: 'typing 1.4s infinite ease-in-out' }}>●</span>
                  <span style={{ animation: 'typing 1.4s infinite ease-in-out 0.16s' }}>●</span>
                  <span style={{ animation: 'typing 1.4s infinite ease-in-out 0.32s' }}>●</span>
                </div>
              </div>
            </div>
          )}
          
          <div ref={messagesEndRef} />
        </div>
      </Content>

      <Footer style={{ background: 'white', borderTop: '1px solid #f0f0f0', padding: '16px' }}>
        <div style={{ maxWidth: '1200px', margin: '0 auto' }}>
          {isRecording && (
            <div style={{ 
              background: '#fff2f0', 
              border: '1px solid #ffccc7',
              borderRadius: '8px',
              padding: '8px 12px',
              marginBottom: '8px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between'
            }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <div style={{ 
                  width: '8px', 
                  height: '8px', 
                  borderRadius: '50%', 
                  background: '#ff4d4f',
                  animation: 'pulse 1s infinite'
                }} />
                <span style={{ fontSize: '14px' }}>正在录音... {recordingTime}s</span>
              </div>
              <Button 
                size="small" 
                danger 
                icon={<StopOutlined />}
                onClick={stopRecording}
              >
                停止
              </Button>
            </div>
          )}
          
          <div style={{
            display: 'flex',
            alignItems: 'flex-end',
            gap: '8px',
            background: '#fafafa',
            borderRadius: '12px',
            padding: '8px',
            border: '1px solid #d9d9d9'
          }}>
            <TextArea
              ref={inputRef}
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              placeholder={isRecording ? "正在录音中..." : "输入消息..."}
              autoSize={{ minRows: 1, maxRows: isMobile ? 3 : 4 }}
              onPressEnter={(e) => {
                if (!e.shiftKey) {
                  e.preventDefault();
                  sendMessage(inputValue);
                }
              }}
              disabled={sending || isRecording}
              style={{ 
                border: 'none',
                background: 'transparent',
                boxShadow: 'none',
                resize: 'none',
                fontSize: `${fontSize}px`
              }}
            />
            
            <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
              {assistant.capabilities?.supports_file_upload && (
                <Upload
                  beforeUpload={handleFileUpload}
                  showUploadList={false}
                  multiple={false}
                >
                  <Tooltip title="上传文件">
                    <Button
                      type="text"
                      icon={<PaperClipOutlined />}
                      size={isMobile ? 'small' : 'middle'}
                    />
                  </Tooltip>
                </Upload>
              )}
              
              {assistant.capabilities?.supports_vision && (
                <Upload
                  beforeUpload={handleFileUpload}
                  showUploadList={false}
                  accept="image/*"
                >
                  <Tooltip title="上传图片">
                    <Button
                      type="text"
                      icon={<PictureOutlined />}
                      size={isMobile ? 'small' : 'middle'}
                    />
                  </Tooltip>
                </Upload>
              )}
              
              {assistant.capabilities?.supports_video_understanding && (
                <Upload
                  beforeUpload={handleFileUpload}
                  showUploadList={false}
                  accept="video/*"
                >
                  <Tooltip title="上传视频">
                    <Button
                      type="text"
                      icon={<VideoCameraOutlined />}
                      size={isMobile ? 'small' : 'middle'}
                    />
                  </Tooltip>
                </Upload>
              )}
              
              {assistant.capabilities?.supports_speech_to_text && (
                <Tooltip title={isRecording ? '停止录音' : '语音输入'}>
                  <Button
                    type="text"
                    icon={isRecording ? <StopOutlined /> : <AudioOutlined />}
                    size={isMobile ? 'small' : 'middle'}
                    danger={isRecording}
                    onClick={isRecording ? stopRecording : startRecording}
                  />
                </Tooltip>
              )}
              
              {((assistant.mcp_tools?.length > 0) || (assistant.mcp_server_ids?.length > 0) || (mcpTools.length > 0)) && (
                <Tooltip title="MCP工具">
                  <Button
                    type="text"
                    icon={<ToolOutlined />}
                    size={isMobile ? 'small' : 'middle'}
                    onClick={() => {
                      // 收集所有可用的MCP工具
                      let allAvailableTools = [];
                      
                      // 1. 传统MCP工具
                      if (assistant.mcp_tools) {
                        assistant.mcp_tools.forEach(toolId => {
                          const tool = mcpTools.find(t => t.id === toolId);
                          if (tool) {
                            allAvailableTools.push({
                              ...tool,
                              source: 'traditional',
                              sourceLabel: '传统MCP工具'
                            });
                          }
                        });
                      }
                      
                      // 2. MCP服务器工具
                      if (assistant.mcp_server_ids) {
                        assistant.mcp_server_ids.forEach(serverId => {
                          const serverTools = mcpTools.filter(t => t.serverId === serverId);
                          serverTools.forEach(tool => {
                            allAvailableTools.push({
                              ...tool,
                              source: 'server',
                              sourceLabel: `来自${tool.serverName}`
                            });
                          });
                        });
                      } else if (!assistant.mcp_tools || assistant.mcp_tools.length === 0) {
                        // 3. 如果没有明确配置，显示所有可用工具
                        mcpTools.forEach(tool => {
                          allAvailableTools.push({
                            ...tool,
                            source: 'available',
                            sourceLabel: `来自${tool.serverName || 'MCP服务器'}`
                          });
                        });
                      }
                      
                      Modal.info({
                        title: `可用MCP工具 (${allAvailableTools.length}个)`,
                        content: (
                          <div>
                            {allAvailableTools.length > 0 ? (
                              allAvailableTools.map((tool, index) => (
                                <Card key={tool.id || index} size="small" style={{ marginBottom: 8 }}>
                                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                                    <div style={{ flex: 1 }}>
                                      <div style={{ fontWeight: 'bold', display: 'flex', alignItems: 'center', gap: '8px' }}>
                                        <CloudServerOutlined style={{ color: '#1890ff' }} />
                                        {tool.display_name || tool.name}
                                      </div>
                                      <div style={{ fontSize: '12px', color: '#666', marginTop: '4px' }}>
                                        {tool.description || '暂无描述'}
                                      </div>
                                      <div style={{ fontSize: '11px', color: '#999', marginTop: '2px' }}>
                                        {tool.sourceLabel}
                                      </div>
                                    </div>
                                    <Button
                                      size="small"
                                      type="primary"
                                      onClick={() => {
                                        Modal.destroyAll(); // 关闭当前模态框
                                        callMcpTool(tool);
                                      }}
                                    >
                                      调用
                                    </Button>
                                  </div>
                                </Card>
                              ))
                            ) : (
                              <div style={{ textAlign: 'center', color: '#999', padding: '20px' }}>
                                <CloudServerOutlined style={{ fontSize: '24px', marginBottom: '8px' }} />
                                <div>暂无可用的MCP工具</div>
                              </div>
                            )}
                          </div>
                        ),
                        width: 600
                      });
                    }}
                  />
                </Tooltip>
              )}
              
              <Button
                type="primary"
                icon={<SendOutlined />}
                onClick={() => sendMessage(inputValue)}
                loading={sending}
                disabled={(!inputValue.trim() && !sending) || isRecording}
                size={isMobile ? 'small' : 'middle'}
              />
            </div>
          </div>
        </div>
      </Footer>

      {/* 图片预览模态框 */}
      <Modal
        open={previewVisible}
        title="图片预览"
        footer={null}
        onCancel={() => setPreviewVisible(false)}
        width="80%"
        style={{ maxWidth: '800px' }}
      >
        <img
          src={previewImage}
          style={{ width: '100%', height: 'auto' }}
          alt="预览"
        />
      </Modal>

      {/* 音频播放器 */}
      <audio ref={audioRef} style={{ display: 'none' }} />

      {/* 设置抽屉 */}
      <Drawer
        title="聊天设置"
        placement="right"
        onClose={() => setSettingsVisible(false)}
        open={settingsVisible}
        width={isMobile ? '100%' : 400}
      >
        <Space direction="vertical" style={{ width: '100%' }} size="large">
          <div>
            <Text strong>字体大小</Text>
            <Slider
              min={12}
              max={20}
              value={fontSize}
              onChange={setFontSize}
              marks={{ 12: '小', 16: '中', 20: '大' }}
            />
          </div>
          
          <div>
            <Text strong>主题模式</Text>
            <div style={{ marginTop: 8 }}>
              <Switch
                checked={theme === 'dark'}
                onChange={(checked) => setTheme(checked ? 'dark' : 'light')}
                checkedChildren="深色"
                unCheckedChildren="浅色"
              />
            </div>
          </div>
          
          <div>
            <Text strong>自动滚动</Text>
            <div style={{ marginTop: 8 }}>
              <Switch
                checked={autoScroll}
                onChange={setAutoScroll}
                checkedChildren="开启"
                unCheckedChildren="关闭"
              />
            </div>
          </div>
          
          {assistant.capabilities && (
            <div>
              <Text strong>助手能力</Text>
              <div style={{ marginTop: 8 }}>
                <Alert
                  message="支持的功能"
                  description={
                    <div style={{ marginTop: 8 }}>
                      {renderCapabilityTags(assistant.capabilities)}
                    </div>
                  }
                  type="info"
                  showIcon
                />
              </div>
            </div>
          )}
          
          <Button
            type="primary"
            danger
            icon={<ClearOutlined />}
            onClick={clearMessages}
            block
          >
            清空对话
          </Button>
        </Space>
      </Drawer>

      {/* 添加CSS动画样式 */}
      <style>
        {`
          @keyframes typing {
            0%, 80%, 100% {
              transform: scale(0.8);
              opacity: 0.5;
            }
            40% {
              transform: scale(1);
              opacity: 1;
            }
          }
          
          @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
          }
        `}
      </style>
    </Layout>
  );
};

export default AIAssistantChat; 