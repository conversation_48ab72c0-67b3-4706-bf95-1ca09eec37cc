#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import json
import time
import logging
import asyncio
import redis
import jwt
from typing import Dict, Any, Optional

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 获取Redis客户端
def get_redis_client():
    redis_url = os.environ.get("REDIS_URL", "redis://retail-ai-saas-redis-dev:6379/0")
    logger.critical(f"CRITICAL_LOG: Attempting get_redis_client()")
    logger.info(f"Connecting to Redis at: {redis_url}")
    try:
        client = redis.from_url(redis_url)
        client.ping()  # 测试连接
        logger.info("Successfully connected to Redis.")
        logger.critical(f"CRITICAL_LOG: Successfully connected to Redis and pinged.")
        return client
    except Exception as e:
        logger.error(f"Redis连接失败: {e}")
        return None

# 全局Redis客户端
redis_client = None
try:
    redis_client = get_redis_client()
    logger.info(f"Redis连接成功: {redis_client}")
except Exception as e:
    logger.warning(f"Redis连接失败: {str(e)}")

def register_server(server_id: str, host: Optional[str] = None, port: int = 0) -> bool:
    """
    将MCP服务器注册到Redis

    使用格式: mcp:server:<server_id> = {host, port, status, last_heartbeat}
    同时创建 v1:servers:<server_id>，确保前端API能找到服务器

    Args:
        server_id: 服务器ID
        host: 主机名或IP地址，如果为None则使用环境变量MCP_SERVER_HOST或默认localhost
        port: 端口号

    Returns:
        bool: 是否注册成功
    """
    try:
        logger.critical(f"CRITICAL_LOG: Attempting register_server for ID: {server_id}, Host: {host}, Port: {port}")
        # 如果未提供host，从环境变量获取
        if not host:
            host = os.environ.get("MCP_SERVER_HOST", server_id)  # 默认使用server_id作为主机名

        # 确保有Redis客户端
        global redis_client
        if not redis_client:
            logger.info("Redis客户端未初始化，尝试初始化...")
            logger.critical("CRITICAL_LOG: Redis client is None in register_server, calling get_redis_client()")
            redis_client = get_redis_client()
            logger.critical(f"CRITICAL_LOG: Redis client initialized in register_server: {redis_client}")

        # 服务器信息
        server_info = {
            "host": host,
            "port": port,
            "status": "running",
            "last_heartbeat": int(time.time())
        }

        # 使用两个键格式注册，确保兼容性
        # 1. 使用mcp:server:<server_id>格式（原始格式）
        redis_client.hset(f"mcp:server:{server_id}", mapping=server_info)

        # 2. 使用v1:servers:<server_id>格式（前端API查询格式）
        redis_client.hset(f"v1:servers:{server_id}", mapping=server_info)

        logger.critical(f"CRITICAL_LOG: Server registration HSET calls successful for {server_id}")
        logger.info(f"服务器注册成功: {server_id} @ {host}:{port}")
        logger.info(f"Redis键注册信息:\nv1格式键: v1:servers:{server_id}\nmcp格式键: mcp:server:{server_id}")
        return True
    except Exception as e:
        logger.error(f"服务器注册失败: {str(e)}")
        return False

async def heartbeat(server_id: str, host: Optional[str] = None, port: int = 0):
    """
    定期向Redis发送心跳，更新服务器状态

    同时更新两种键格式

    Args:
        server_id: 服务器ID
        host: 主机名或IP地址
        port: 端口号
    """
    try:
        # 如果未提供host，从环境变量获取
        if not host:
            host = os.environ.get("MCP_SERVER_HOST", server_id)  # 默认使用server_id作为主机名

        # 确保有Redis客户端
        global redis_client
        if not redis_client:
            redis_client = get_redis_client()

        while True:
            try:
                # 更新心跳时间
                server_info = {
                    "host": host,
                    "port": port,
                    "status": "running",
                    "last_heartbeat": int(time.time())
                }

                # 使用两个键格式更新，确保兼容性
                # 1. 更新mcp:server:<server_id>格式
                redis_client.hset(f"mcp:server:{server_id}", mapping=server_info)

                # 2. 更新v1:servers:<server_id>格式
                redis_client.hset(f"v1:servers:{server_id}", mapping=server_info)

                logger.debug(f"服务器心跳更新: {server_id} @ {host}:{port}")
            except Exception as e:
                logger.warning(f"更新服务器心跳失败: {str(e)}")
                # 尝试重新连接Redis
                try:
                    redis_client = get_redis_client()
                except Exception as re:
                    logger.error(f"重新连接Redis失败: {str(re)}")

            # 等待下一次心跳
            await asyncio.sleep(30)  # 每30秒发送一次心跳
    except Exception as e:
        logger.error(f"心跳任务异常: {str(e)}")

# 系统JWT令牌相关

# 令牌缓存
token_cache = {}

def get_secret_key() -> str:
    """
    获取SECRET_KEY，用于JWT签名验证
    
    优先从环境变量获取，如果没有则返回一个默认值
    
    Returns:
        str: SECRET_KEY
    """
    # 优先从环境变量获取
    secret_key = os.environ.get("SECRET_KEY")
    if secret_key:
        logger.info("从环境变量获取SECRET_KEY成功")
        return secret_key
    
    # 如果没有环境变量，返回默认值
    # 警告：这仅用于开发环境，生产环境应该设置SECRET_KEY环境变量
    logger.warning("环境变量中未找到SECRET_KEY，使用默认值（仅用于开发环境）")
    return "retail-ai-saas-default-secret-key-for-development-only"

async def get_system_jwt_token(plugin_token: str, token_data: Dict[str, Any] = None) -> Dict[str, Any]:
    """
    获取系统JWT令牌

    通过API将插件JWT令牌转换为系统JWT令牌

    Args:
        plugin_token: 插件JWT令牌
        token_data: 令牌数据，如用户ID、项目ID等

    Returns:
        Dict[str, Any]: 包含系统令牌的字典 {"success": bool, "token": str, "error": str}
    """
    try:
        import httpx

        # API基础URL
        api_base_url = os.environ.get("API_BASE_URL", "http://retail-ai-saas-backend-dev:8000")

        # 准备请求数据
        request_data = {
            "plugin_token": plugin_token
        }

        # 如果有额外的令牌数据，添加到请求中
        if token_data:
            if "user_id" in token_data:
                request_data["user_id"] = token_data["user_id"]
            if "project_id" in token_data:
                request_data["project_id"] = token_data["project_id"]
            if "tenant_id" in token_data:
                request_data["tenant_id"] = token_data["tenant_id"]

        # 获取系统令牌
        system_token = os.environ.get("SYSTEM_TOKEN")
        
        # 如果有系统令牌，直接使用
        if system_token:
            logger.info("使用环境变量中的系统令牌")
            return {"success": True, "token": system_token}
        
        # 否则通过API获取
        # 发送请求
        url = f"{api_base_url}/api/v1/auth/convert-token"
        headers = {
            "Content-Type": "application/json"
        }

        logger.info(f"发送请求到 {url}")
        async with httpx.AsyncClient() as client:
            response = await client.post(url, json=request_data, headers=headers)
            logger.info(f"API响应: {response.status_code}")

        # 检查响应
        if response.status_code != 200:
            logger.error(f"获取系统令牌失败: {response.status_code} - {response.text}")
            return {"success": False, "error": f"获取系统令牌失败: {response.status_code} - {response.text}"}

        # 解析响应
        result = response.json()

        # 检查结果
        if "access_token" not in result:
            logger.error(f"系统令牌格式无效: {result}")
            return {"success": False, "error": "系统令牌格式无效"}

        # 返回系统令牌
        return {"success": True, "token": result["access_token"]}
    except Exception as e:
        logger.error(f"获取系统令牌异常: {str(e)}")
        return {"success": False, "error": f"获取系统令牌异常: {str(e)}"}

async def get_system_jwt_token_with_cache(plugin_token: str, token_data: Dict[str, Any] = None) -> Dict[str, Any]:
    """
    获取系统JWT令牌，使用缓存

    如果缓存中有有效的令牌，直接返回，否则获取新令牌

    Args:
        plugin_token: 插件JWT令牌
        token_data: 令牌数据，如用户ID、项目ID等

    Returns:
        Dict[str, Any]: 包含系统令牌的字典 {"success": bool, "token": str, "error": str}
    """
    global token_cache

    # 缓存键
    cache_key = plugin_token

    # 检查缓存
    if cache_key in token_cache:
        # 有缓存的令牌，返回
        logger.debug(f"使用缓存的系统令牌: {cache_key}")
        return {"success": True, "token": token_cache[cache_key]}

    # 获取新令牌
    result = await get_system_jwt_token(plugin_token, token_data)
    if result["success"]:
        # 缓存令牌
        token_cache[cache_key] = result["token"]

    return result

def create_jwt_token(data: Dict[str, Any], expires_delta: Optional[int] = None) -> str:
    """
    创建JWT令牌
    
    Args:
        data: 令牌数据
        expires_delta: 过期时间（秒）
        
    Returns:
        str: JWT令牌
    """
    to_encode = data.copy()
    
    # 设置过期时间
    if expires_delta:
        expire = int(time.time()) + expires_delta
    else:
        # 默认30天过期
        expire = int(time.time()) + 30 * 24 * 60 * 60
    
    to_encode.update({"exp": expire})
    
    # 获取SECRET_KEY
    secret_key = get_secret_key()
    
    # 创建令牌
    encoded_jwt = jwt.encode(to_encode, secret_key, algorithm="HS256")
    return encoded_jwt

def verify_jwt_token(token: str) -> Dict[str, Any]:
    """
    验证JWT令牌
    
    Args:
        token: JWT令牌
        
    Returns:
        Dict[str, Any]: 令牌数据
    """
    try:
        # 获取SECRET_KEY
        secret_key = get_secret_key()
        
        # 验证令牌
        payload = jwt.decode(token, secret_key, algorithms=["HS256"])
        return {"success": True, "data": payload}
    except jwt.ExpiredSignatureError:
        logger.error("令牌已过期")
        return {"success": False, "error": "令牌已过期"}
    except jwt.InvalidTokenError:
        logger.error("无效的令牌")
        return {"success": False, "error": "无效的令牌"}
    except Exception as e:
        logger.error(f"验证令牌异常: {str(e)}")
        return {"success": False, "error": f"验证令牌异常: {str(e)}"} 