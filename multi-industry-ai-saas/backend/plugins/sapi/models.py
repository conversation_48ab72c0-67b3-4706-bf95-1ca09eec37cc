#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
智能助手集成平台(SAPI)数据模型
"""
import uuid
from datetime import datetime
from sqlalchemy import Column, String, Boolean, Float, DateTime, ForeignKey, JSON, Text, Integer
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID

from db.database import Base

# 智能助手集成平台配置模型
class SAPIConfig(Base):
    __tablename__ = "sapi_configs"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(100), nullable=False)
    description = Column(Text, nullable=True)
    api_key = Column(String(200), nullable=True)  # 大模型API密钥
    model_name = Column(String(100), nullable=False)  # 使用的模型名称
    enabled = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)
    created_by_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)

    # 关系
    created_by = relationship("User", foreign_keys=[created_by_id])
    endpoints = relationship("SAPIEndpoint", back_populates="config")

# API端点模型
class SAPIEndpoint(Base):
    __tablename__ = "sapi_endpoints"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(100), nullable=False)
    description = Column(Text, nullable=True)
    endpoint_path = Column(String(200), nullable=False)  # API路径
    method = Column(String(10), nullable=False)  # GET, POST, PUT, DELETE
    enabled = Column(Boolean, default=True)
    requires_auth = Column(Boolean, default=True)  # 是否需要认证
    allowed_roles = Column(String(200), nullable=True)  # 允许的角色，逗号分隔
    parameters = Column(JSON, default=dict)  # 参数定义
    response_template = Column(Text, nullable=True)  # 响应模板
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)
    config_id = Column(UUID(as_uuid=True), ForeignKey("sapi_configs.id"), nullable=False)

    # 关系
    config = relationship("SAPIConfig", back_populates="endpoints")
    usage_logs = relationship("SAPIUsageLog", back_populates="endpoint")
    mappings = relationship(
        "APIMCPToolMapping",
        back_populates="endpoint",
        cascade="all, delete-orphan"
    )

# 使用日志模型
class SAPIUsageLog(Base):
    __tablename__ = "sapi_usage_logs"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)
    endpoint_id = Column(UUID(as_uuid=True), ForeignKey("sapi_endpoints.id"), nullable=False)
    request_data = Column(JSON, nullable=True)
    response_data = Column(JSON, nullable=True)
    status_code = Column(Integer, nullable=False)
    execution_time = Column(Float, nullable=False)  # 执行时间(ms)
    timestamp = Column(DateTime, default=datetime.now)
    token_id = Column(UUID(as_uuid=True), ForeignKey("sapi_tokens.id"), nullable=True)

    # 关系
    user = relationship("User", foreign_keys=[user_id])
    endpoint = relationship("SAPIEndpoint", back_populates="usage_logs")
    token = relationship("SAPIToken", back_populates="usage_logs")

# API令牌模型
class SAPIToken(Base):
    __tablename__ = "sapi_tokens"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(100), nullable=False)
    description = Column(Text, nullable=True)
    token = Column(String(1000), unique=True, nullable=False)  # 令牌值 (JWT可能较长)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)  # 关联用户
    permissions = Column(String(50), default="read")  # read, write, read_write
    server_permissions = Column(JSON, nullable=True)  # 服务器权限映射 {server_id: [permissions]}
    default_permissions = Column(JSON, nullable=True)  # 默认权限列表
    token_type = Column(String(20), default="access")  # access, refresh, mcp
    status = Column(String(20), default="active")  # active, inactive, revoked
    project_id = Column(UUID(as_uuid=True), nullable=True)  # 关联项目
    created_at = Column(DateTime, default=datetime.now)
    last_used = Column(DateTime, nullable=True)
    expires_at = Column(DateTime, nullable=True)  # 过期时间，如果为空则永不过期

    # 关系
    user = relationship("User", foreign_keys=[user_id])
    usage_logs = relationship("SAPIUsageLog", back_populates="token")

# API令牌申请模型
class SAPITokenApplication(Base):
    __tablename__ = "sapi_token_applications"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(100), nullable=False)
    description = Column(Text, nullable=True)
    purpose = Column(Text, nullable=False)  # 申请目的
    requested_permissions = Column(String(50), default="read")  # 申请的权限
    server_permissions = Column(JSON, nullable=True)  # 服务器权限映射 {server_id: [permissions]}
    default_permissions = Column(JSON, nullable=True)  # 默认权限列表
    token_type = Column(String(20), default="mcp")  # access, refresh, mcp
    status = Column(String(20), default="pending")  # pending, approved, rejected
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)  # 申请人
    project_id = Column(UUID(as_uuid=True), nullable=True)  # 关联项目
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, nullable=True)
    token = Column(String(1000), nullable=True)  # 批准后生成的令牌值
    rejection_reason = Column(Text, nullable=True)  # 拒绝原因
    expires_at = Column(DateTime, nullable=True)  # 过期时间

    # 关系
    user = relationship("User", foreign_keys=[user_id])

# MCP服务器模型
class MCPServer(Base):
    __tablename__ = "sapi_mcp_servers"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(100), nullable=False)
    description = Column(Text, nullable=True)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id"), nullable=False)
    transport_types = Column(JSON, default=lambda: ["streamhttp"])  # 支持的传输类型列表: stdio, sse, streamhttp
    deployment_mode = Column(String(20), default="local")  # 部署模式: local, docker, npm
    install_dir = Column(String(200), nullable=True)  # 安装目录
    command = Column(String(100), nullable=True)  # 启动命令
    args = Column(Text, nullable=True)  # 命令参数，JSON格式
    env = Column(Text, nullable=True)  # 环境变量，JSON格式
    docker_image = Column(String(200), nullable=True)  # Docker镜像
    docker_tag = Column(String(50), default="latest")  # Docker标签
    npm_package = Column(String(200), nullable=True)  # NPM包名
    npm_version = Column(String(50), default="latest")  # NPM版本
    simulation = Column(Boolean, default=False)  # 是否为模拟模式
    use_system_ai = Column(Boolean, default=False)  # 是否使用系统AI
    enabled = Column(Boolean, default=True)  # 是否启用
    supports_media = Column(Boolean, default=True)  # 是否支持媒体（图片等）
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)
    created_by_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)

    # 关系
    created_by = relationship("User", foreign_keys=[created_by_id])
    processes = relationship("MCPServerProcess", back_populates="server")
    mappings = relationship("APIMCPToolMapping", back_populates="server")
    project = relationship("Project")

# MCP服务器进程模型
class MCPServerProcess(Base):
    __tablename__ = "sapi_mcp_server_processes"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    server_id = Column(UUID(as_uuid=True), ForeignKey("sapi_mcp_servers.id"), nullable=False)
    process_id = Column(Integer, nullable=True)  # 进程ID
    status = Column(String(20), default="stopped")  # running, stopped, error
    started_at = Column(DateTime, nullable=True)
    stopped_at = Column(DateTime, nullable=True)
    logs = Column(Text, nullable=True)  # 进程日志
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    # 关系
    server = relationship("MCPServer", back_populates="processes")

# API到MCP工具的映射模型
class APIMCPToolMapping(Base):
    __tablename__ = "sapi_api_mcp_tool_mappings"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    endpoint_id = Column(UUID(as_uuid=True), ForeignKey("sapi_endpoints.id"), nullable=False)
    server_id = Column(UUID(as_uuid=True), ForeignKey("sapi_mcp_servers.id"), nullable=False)
    tool_name = Column(String(100), nullable=False)  # MCP工具名称
    tool_description = Column(Text, nullable=True)  # 工具描述，覆盖端点描述
    parameter_mapping = Column(JSON, default=dict)  # 参数映射
    response_mapping = Column(JSON, default=dict)  # 响应映射
    input_schema = Column(JSON, nullable=True)  # MCP工具的 inputSchema，预生成
    tool_schema = Column(JSON, nullable=True)  # 工具模式，覆盖自动生成的模式. DEPRECATED or to be reviewed if different from input_schema.
    supports_streaming = Column(Boolean, default=True)  # 是否支持流式响应
    supports_media = Column(Boolean, default=False)  # 是否支持媒体（图片等）
    enabled = Column(Boolean, default=True)  # 是否启用
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)
    created_by_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)

    # 关系
    endpoint = relationship("SAPIEndpoint", back_populates="mappings")
    server = relationship("MCPServer", back_populates="mappings")
    created_by = relationship("User", foreign_keys=[created_by_id])
