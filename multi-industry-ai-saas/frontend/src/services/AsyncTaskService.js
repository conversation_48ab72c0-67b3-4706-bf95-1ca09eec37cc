import { api } from './api';
import { getProjectId } from './api';

// 导入WebSocket服务
const getWebSocketService = () => {
  try {
    const NotificationWebSocket = require('./websocket/NotificationWebSocket').default;
    return new NotificationWebSocket();
  } catch (error) {
    console.warn('WebSocket服务不可用，将使用轮询模式:', error);
    return null;
  }
};

class AsyncTaskService {
  constructor() {
    this.pollingIntervals = new Map(); // 存储轮询定时器
    this.defaultPollingInterval = 10000; // 默认轮询间隔10秒，减少服务器压力
    this.maxPollingTime = 900000; // 增加最大轮询时间到15分钟，适应分批处理
    this.webSocketService = null;
    this.taskCallbacks = new Map(); // 存储任务回调
    this.initWebSocket();
  }

  /**
   * 初始化WebSocket连接
   */
  async initWebSocket() {
    try {
      this.webSocketService = getWebSocketService();
      if (this.webSocketService) {
        // 监听WebSocket任务状态更新
        this.webSocketService.onMessage((message) => {
          if (message.type === 'task_update' && message.task_id) {
            this.handleWebSocketTaskUpdate(message);
          }
        });
        
        console.log('AsyncTaskService: WebSocket服务已初始化');
      }
    } catch (error) {
      console.warn('AsyncTaskService: WebSocket初始化失败，使用轮询模式:', error);
    }
  }

  /**
   * 处理WebSocket任务状态更新
   */
  handleWebSocketTaskUpdate(message) {
    const { task_id, status, progress, result } = message;
    const callbacks = this.taskCallbacks.get(task_id);
    
    if (callbacks) {
      const { onProgress, onComplete, onError } = callbacks;
      
      // 构建任务更新数据
      const taskUpdate = {
        status,
        progress: progress || 0,
        message: this.getStatusMessage(status),
        task: message
      };
      
      // 触发进度回调
      if (onProgress) {
        onProgress(taskUpdate);
      }
      
      // 处理完成状态
      if (status === 'success') {
        // 停止轮询
        this.stopPolling(task_id);
        this.taskCallbacks.delete(task_id);
        
        if (onComplete) {
          onComplete(result || { success: true });
        }
      } else if (['failed', 'cancelled'].includes(status)) {
        // 停止轮询
        this.stopPolling(task_id);
        this.taskCallbacks.delete(task_id);
        
        if (onError) {
          onError(new Error(message.error_message || '任务执行失败'));
        }
      }
    }
  }

  /**
   * 获取当前项目ID
   */
  getCurrentProjectId() {
    return getProjectId();
  }

  /**
   * 混合模式执行任务
   * 先尝试同步执行，超时后自动转为异步
   */
  async executeHybridTask({
    syncAction,
    asyncAction,
    syncTimeout = 5000,
    onProgress = () => {},
    onComplete = () => {},
    onError = () => {},
    onFallbackToAsync = () => {}
  }) {
    try {
      // 如果syncAction为null，直接跳过同步处理，转为异步模式
      if (!syncAction) {
        onProgress({ 
          status: 'fallback_to_async', 
          message: '使用AI智能处理模式...',
          progress: 30 
        });
        
        onFallbackToAsync();
        
        // 直接执行异步任务
        const asyncResult = await asyncAction();
        console.log('executeHybridTask: 异步任务执行结果:', asyncResult);
        
        if (asyncResult.data && asyncResult.data.task_id) {
          console.log('executeHybridTask: 获取到任务ID:', asyncResult.data.task_id);
          // 开始监控异步任务
          this.monitorTask(asyncResult.data.task_id, {
            onProgress,
            onComplete,
            onError
          });
        } else {
          console.error('executeHybridTask: 未找到任务ID在异步结果中:', asyncResult);
        }
        
        return asyncResult;
      }

      // 先尝试同步执行
      onProgress({ 
        status: 'sync_trying', 
        message: '正在快速处理...',
        progress: 10 
      });

      const syncPromise = syncAction();
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('SYNC_TIMEOUT')), syncTimeout);
      });

      try {
        const result = await Promise.race([syncPromise, timeoutPromise]);
        
        // 同步执行成功
        onProgress({ 
          status: 'sync_completed', 
          message: '处理完成',
          progress: 100 
        });
        onComplete(result);
        return result;
        
      } catch (error) {
        if (error.message === 'SYNC_TIMEOUT' || error.message === 'IMAGE_FILE_NEEDS_AI') {
          // 同步超时或图片需要AI处理，转为异步模式
          const message = error.message === 'IMAGE_FILE_NEEDS_AI' 
            ? '图片文件正在使用AI智能识别处理...' 
            : '处理时间较长，已转为后台处理模式...';
            
          onProgress({ 
            status: 'fallback_to_async', 
            message,
            progress: 30 
          });
          
          onFallbackToAsync();
          
          // 执行异步任务
          const asyncResult = await asyncAction();
          console.log('executeHybridTask(fallback): 异步任务执行结果:', asyncResult);
          
          if (asyncResult.data && asyncResult.data.task_id) {
            console.log('executeHybridTask(fallback): 获取到任务ID:', asyncResult.data.task_id);
            // 开始监控异步任务
            this.monitorTask(asyncResult.data.task_id, {
              onProgress,
              onComplete,
              onError
            });
          } else {
            console.error('executeHybridTask(fallback): 未找到任务ID在异步结果中:', asyncResult);
          }
          
          return asyncResult;
        } else {
          throw error; // 其他错误继续抛出
        }
      }
    } catch (error) {
      onError(error);
      throw error;
    }
  }

  /**
   * 开始轮询任务状态（兼容旧接口）
   */
  startPolling(taskId, onUpdate, interval = null) {
    if (interval) {
      this.defaultPollingInterval = interval;
    }
    
    this.monitorTask(taskId, {
      onProgress: onUpdate,
      onComplete: (result) => {
        onUpdate({
          status: 'success',
          progress: 100,
          result,
          is_completed: true
        });
      },
      onError: (error) => {
        onUpdate({
          status: 'failed',
          progress: 0,
          error_message: error.message,
          is_completed: true
        });
      }
    });
  }

  /**
   * 智能监控任务进度 - 结合WebSocket和轮询
   */
  async monitorTask(taskId, { onProgress, onComplete, onError }) {
    console.log('AsyncTaskService.monitorTask 开始监控任务:', taskId);
    
    // 验证任务ID格式
    if (!taskId || typeof taskId !== 'string') {
      console.error('AsyncTaskService.monitorTask 任务ID无效:', taskId);
      onError(new Error('任务ID无效'));
      return;
    }
    
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    if (!uuidRegex.test(taskId)) {
      console.error('AsyncTaskService.monitorTask 任务ID格式错误:', taskId);
      onError(new Error(`任务ID格式错误: ${taskId}`));
      return;
    }
    
    // 存储回调以便WebSocket使用
    this.taskCallbacks.set(taskId, { onProgress, onComplete, onError });
    
    // 优先尝试WebSocket监控
    if (this.webSocketService && this.webSocketService.isConnected()) {
      console.log('AsyncTaskService: 使用WebSocket+轮询混合模式监控任务:', taskId);
      // WebSocket已连接，使用混合模式：WebSocket为主，轮询为辅
      this.startHybridMonitoring(taskId, { onProgress, onComplete, onError });
    } else {
      console.log('AsyncTaskService: 使用纯轮询模式监控任务:', taskId);
      // WebSocket不可用，使用纯轮询模式
      this.startPollingOnlyMonitoring(taskId, { onProgress, onComplete, onError });
    }
  }

  /**
   * 混合监控模式：WebSocket + 智能轮询
   */
  async startHybridMonitoring(taskId, { onProgress, onComplete, onError }) {
    const startTime = Date.now();
    let lastStatus = null;
    let retryCount = 0;
    const maxRetries = 3;
    let pollingInterval = this.defaultPollingInterval;
    
    // 智能轮询间隔调整 - 优化版，减少轮询频次
    const adjustPollingInterval = (status, duration) => {
      if (status === 'running') {
        // 任务运行中，根据运行时间调整间隔
        if (duration < 30000) { // 30秒内
          return 5000; // 5秒（降低频次）
        } else if (duration < 120000) { // 2分钟内
          return 12000; // 12秒（降低频次）
        } else { // 超过2分钟
          return 20000; // 20秒（降低频次）
        }
      }
      return this.defaultPollingInterval;
    };

    const poll = async () => {
      try {
        const projectId = this.getCurrentProjectId();
        if (!projectId) {
          throw new Error('未找到项目ID');
        }
        
        const url = `/project/${projectId}/tasks/${taskId}`;
        const response = await api.get(url);
        
        retryCount = 0; // 重置重试计数
        
        if (response && response.success && response.data) {
          const task = response.data;
          const currentTime = Date.now();
          const duration = currentTime - startTime;
          
          // 状态变化时触发进度回调
          if (task.status !== lastStatus) {
            lastStatus = task.status;
            
            onProgress({
              status: task.status,
              progress: task.progress || 0,
              message: this.getStatusMessage(task.status, task.task_name),
              task: task,
              duration: Math.round(duration / 1000)
            });
          }

          // 任务完成
          if (task.is_completed) {
            this.stopPolling(taskId);
            this.taskCallbacks.delete(taskId);
            
            if (task.status === 'success') {
              let resultData = task.result;
              
              if (typeof resultData === 'string') {
                try {
                  resultData = JSON.parse(resultData);
                } catch (error) {
                  console.warn('解析任务结果JSON失败:', error, resultData);
                }
              }
              
              if (!resultData) {
                resultData = { success: false, message: '任务结果为空' };
              }
              
              console.log('AsyncTaskService: 任务成功完成，结果数据:', resultData);
              onComplete(resultData);
            } else {
              const errorMessage = task.error_message || '任务执行失败';
              console.error('AsyncTaskService: 任务失败:', errorMessage);
              onError(new Error(errorMessage));
            }
            return;
          }

          // 检查是否超时
          if (duration > this.maxPollingTime) {
            this.stopPolling(taskId);
            this.taskCallbacks.delete(taskId);
            onError(new Error('任务监控超时（15分钟）'));
            return;
          }

          // 调整轮询间隔
          pollingInterval = adjustPollingInterval(task.status, duration);
          
          // 继续轮询
          const timeoutId = setTimeout(poll, pollingInterval);
          this.pollingIntervals.set(taskId, timeoutId);
          
        } else {
          throw new Error('获取任务状态失败');
        }
      } catch (error) {
        console.error('AsyncTaskService.monitorTask 错误:', error, '任务ID:', taskId, '重试次数:', retryCount);
        
        const isRetryableError = this.isRetryableError(error);
        
        if (isRetryableError && retryCount < maxRetries) {
          retryCount++;
          const retryDelay = Math.min(2000 * Math.pow(2, retryCount - 1), 10000);
          
          console.log(`AsyncTaskService: 第${retryCount}次重试，${retryDelay}ms后重试...`);
          
          const timeoutId = setTimeout(poll, retryDelay);
          this.pollingIntervals.set(taskId, timeoutId);
        } else {
          this.stopPolling(taskId);
          this.taskCallbacks.delete(taskId);
          onError(error);
        }
      }
    };

    // 开始轮询
    poll();
  }

  /**
   * 纯轮询监控模式
   */
  async startPollingOnlyMonitoring(taskId, { onProgress, onComplete, onError }) {
    // 与startHybridMonitoring相同的逻辑，但不依赖WebSocket
    return this.startHybridMonitoring(taskId, { onProgress, onComplete, onError });
  }

  /**
   * 判断是否是可重试的错误
   */
  isRetryableError(error) {
    if (!error) return false;
    
    const retryableStatusCodes = [502, 503, 504, 500];
    const retryableMessages = [
      'network error',
      'timeout',
      'bad gateway',
      'service unavailable',
      'gateway timeout',
      'request failed with status code 502',
      'request failed with status code 503',
      'request failed with status code 504',
      'request failed with status code 500'
    ];
    
    // 检查状态码
    if (error.response && retryableStatusCodes.includes(error.response.status)) {
      return true;
    }
    
    // 检查错误消息
    const errorMessage = (error.message || '').toLowerCase();
    return retryableMessages.some(msg => errorMessage.includes(msg));
  }

  /**
   * 停止轮询
   */
  stopPolling(taskId) {
    const timeoutId = this.pollingIntervals.get(taskId);
    if (timeoutId) {
      clearTimeout(timeoutId);
      this.pollingIntervals.delete(taskId);
    }
  }

  /**
   * 获取状态消息
   */
  getStatusMessage(status, taskName) {
    const messages = {
      pending: '任务排队中...',
      running: '正在处理中...',
      success: '处理完成',
      failed: '处理失败',
      cancelled: '任务已取消'
    };
    return messages[status] || '未知状态';
  }

  /**
   * 取消任务
   */
  async cancelTask(taskId) {
    this.stopPolling(taskId);
    
    try {
      const projectId = this.getCurrentProjectId();
      if (!projectId) {
        throw new Error('未找到项目ID');
      }
      
      const response = await api.post(`/project/${projectId}/tasks/${taskId}/cancel`);
      return response;
    } catch (error) {
      console.error('取消任务失败:', error);
      throw error;
    }
  }

  /**
   * 清理所有轮询
   */
  cleanup() {
    this.pollingIntervals.forEach(timeoutId => clearTimeout(timeoutId));
    this.pollingIntervals.clear();
  }

  /**
   * 获取任务状态
   */
  async getTaskStatus(taskId) {
    const maxRetries = 3;
    let retryCount = 0;
    
    const attemptGetStatus = async () => {
      try {
        const projectId = this.getCurrentProjectId();
        if (!projectId) {
          throw new Error('未找到项目ID');
        }
        
        const response = await api.get(`/project/${projectId}/tasks/${taskId}`);
        
        if (response && response.success && response.data) {
          return response.data;
        } else {
          throw new Error('获取任务状态失败');
        }
      } catch (error) {
        console.error('获取任务状态失败:', error, '重试次数:', retryCount);
        
        // 检查是否是可重试的错误
        const isRetryableError = this.isRetryableError(error);
        
        if (isRetryableError && retryCount < maxRetries) {
          retryCount++;
          const retryDelay = Math.min(1000 * Math.pow(2, retryCount - 1), 5000); // 指数退避，最大5秒
          
          console.log(`getTaskStatus 第${retryCount}次重试，${retryDelay}ms后重试...`);
          
          // 延迟后重试
          await new Promise(resolve => setTimeout(resolve, retryDelay));
          return attemptGetStatus();
        } else {
          throw error;
        }
      }
    };
    
    return attemptGetStatus();
  }

  // ... existing methods ...
}

const asyncTaskService = new AsyncTaskService();
export default asyncTaskService; 