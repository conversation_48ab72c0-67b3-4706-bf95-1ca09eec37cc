#!/bin/sh

echo 'start.sh execution started.' > /app/startup_debug.log

# Determine Python command
if command -v python3.10 > /dev/null 2>&1; then
    PYTHON_CMD="python3.10"
elif command -v python3 > /dev/null 2>&1; then
    PYTHON_CMD="python3"
elif command -v python > /dev/null 2>&1; then
    PYTHON_CMD="python"
else
    echo 'Error: Python interpreter not found.' >> /app/startup_debug.log
    exit 1
fi
echo "Using Python command: $PYTHON_CMD" >> /app/startup_debug.log

# Install dependencies
if [ -f "requirements.txt" ]; then
    echo 'Installing dependencies...' >> /app/startup_debug.log
    $PYTHON_CMD -m pip install --no-cache-dir -r requirements.txt >> /app/pip_install.log 2>&1
    if [ $? -ne 0 ]; then
        echo 'pip install failed. Check /app/pip_install.log' >> /app/startup_debug.log
        # Optional: exit 1
    else:
        echo 'Dependencies installed successfully.' >> /app/startup_debug.log
    fi
else
    echo 'requirements.txt not found, skipping pip install.' >> /app/startup_debug.log
fi

echo 'Configuring and starting server...' >> /app/startup_debug.log
# PRIMARY_TRANSPORT_TYPE is determined by Python and baked into this script logic
echo "Primary transport type for this instance: streamhttp" >> /app/startup_debug.log

if [ "streamhttp" = "stdio" ]; then
    echo 'Starting stdio server...' >> /app/startup_debug.log
    exec $PYTHON_CMD main.py
# ELIF for SSE is removed. SSE type is now handled as streamhttp or an invalid type earlier.
else  # streamhttp or default (covers cases where sse might have been passed and converted to streamhttp)
    echo 'Starting StreamHTTP server...' >> /app/startup_debug.log
    # multi_protocol.py, when handling SSE, will still run an HTTP server.
    # It requires a port. We will assign one dynamically if not provided.
    if [ -z "$PORT" ]; then
        echo "PORT environment variable not set for StreamHTTP/default mode, attempting to find a dynamic port..." >> /app/startup_debug.log
        PORT_CANDIDATE=$($PYTHON_CMD -c 'import socket; s=socket.socket(); s.bind(("", 0)); print(s.getsockname()[1]); s.close()')
        if [ -n "$PORT_CANDIDATE" ]; then
            export PORT="$PORT_CANDIDATE"
            echo "Dynamically assigned PORT=$PORT for StreamHTTP/default mode." >> /app/startup_debug.log
        else:
            echo "Failed to assign dynamic port for StreamHTTP/default mode. main.py might use a default or fail." >> /app/startup_debug.log
            # Fallback to a default if dynamic assignment fails, or let main.py handle it
            export PORT="8000" # Default fallback
            echo "Using default PORT=$PORT for StreamHTTP/default mode after dynamic assignment failure." >> /app/startup_debug.log
        fi
    else
        echo "Using environment PORT=$PORT for StreamHTTP/default mode." >> /app/startup_debug.log
        export PORT # Ensure it's exported
    fi
    exec $PYTHON_CMD main.py --http-port=$PORT
fi

echo "main.py exited with code: $?" >> /app/startup_debug.log
echo 'start.sh finished.' >> /app/startup_debug.log
