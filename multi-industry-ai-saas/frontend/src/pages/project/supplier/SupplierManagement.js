import React, { useState, useEffect, useRef } from 'react';
import {
  Card,
  Table,
  Button,
  Input,
  Space,
  message,
  Modal,
  Form,
  Row,
  Col,
  Typography,
  Tooltip,
  Popconfirm,
  Tabs,
  Tag,
  Divider,
  Upload
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  ReloadOutlined,
  EditOutlined,
  DeleteOutlined,
  ExportOutlined,
  ImportOutlined,
  EyeOutlined,
  MailOutlined,
  PhoneOutlined,
  HomeOutlined,
  UserOutlined,
  UploadOutlined,
  DownloadOutlined
} from '@ant-design/icons';
import { project } from '../../../services/api';
import { useNavigate } from 'react-router-dom';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;

const SupplierManagement = () => {
  const [loading, setLoading] = useState(false);
  const [suppliers, setSuppliers] = useState([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total) => `共 ${total} 条`
  });
  const [total, setTotal] = useState(0);
  const [searchText, setSearchText] = useState('');
  const [modalVisible, setModalVisible] = useState(false);
  const [editingSupplier, setEditingSupplier] = useState(null);
  const [submitting, setSubmitting] = useState(false);
  const [importModalVisible, setImportModalVisible] = useState(false);
  const [importFile, setImportFile] = useState(null);
  const [importing, setImporting] = useState(false);

  const [form] = Form.useForm();

  const navigate = useNavigate();
  const searchInputRef = useRef(null);

  // 初始化数据
  useEffect(() => {
    fetchSuppliers();
  }, []);

  // 监听搜索和分页变化
  useEffect(() => {
    fetchSuppliers();
  }, [searchText, pagination.current, pagination.pageSize]);

  // 获取供应商列表
  const fetchSuppliers = async () => {
    setLoading(true);
    try {
      const response = await project.supplier.getList({
        page: pagination.current,
        page_size: pagination.pageSize,
        search: searchText
      });

      if (response && response.success) {
        setSuppliers(response.data || []);
        setTotal(response.total || 0);
      } else {
        message.error(response?.message || '获取供应商列表失败');
      }
    } catch (error) {
      console.error('获取供应商失败:', error);
      message.error('获取供应商列表失败: ' + (error.response?.data?.message || '服务器错误'));
    } finally {
      setLoading(false);
    }
  };

  // 处理表格分页变化
  const handleTableChange = (pagination) => {
    setPagination(pagination);
  };

  // 处理搜索
  const handleSearch = (value) => {
    setSearchText(value);
    setPagination({ ...pagination, current: 1 });
  };

  // 打开新增供应商模态框
  const handleAddSupplier = () => {
    setEditingSupplier(null);
    form.resetFields();
    setModalVisible(true);
  };

  // 打开编辑供应商模态框
  const handleEditSupplier = (record) => {
    setEditingSupplier(record);
    form.setFieldsValue({
      name: record.name,
      contact_person: record.contact_person,
      contact_phone: record.contact_phone,
      email: record.email,
      address: record.address
    });
    setModalVisible(true);
  };

  // 处理删除供应商
  const handleDeleteSupplier = async (id) => {
    try {
      const response = await project.supplier.delete(id);
      if (response && response.success) {
        message.success('供应商删除成功');
        fetchSuppliers();
      } else {
        message.error(response?.message || '删除供应商失败');
      }
    } catch (error) {
      console.error('删除供应商失败:', error);
      message.error('删除供应商失败: ' + (error.response?.data?.message || '服务器错误'));
    }
  };

  // 处理表单提交
  const handleFormSubmit = async () => {
    try {
      const values = await form.validateFields();
      setSubmitting(true);

      if (editingSupplier) {
        // 更新供应商
        const response = await project.supplier.update(editingSupplier.id, values);

        if (response && response.success) {
          message.success('供应商更新成功');
          setModalVisible(false);
          fetchSuppliers();
        } else {
          message.error(response?.message || '更新供应商失败');
        }
      } else {
        // 创建供应商
        const response = await project.supplier.create(values);

        if (response && response.success) {
          message.success('供应商创建成功');
          setModalVisible(false);
          fetchSuppliers();
        } else {
          message.error(response?.message || '创建供应商失败');
        }
      }
    } catch (error) {
      console.error('提交表单失败:', error);
      message.error('操作失败: ' + (error.response?.data?.message || '表单验证失败'));
    } finally {
      setSubmitting(false);
    }
  };

  // 处理导入供应商
  const handleImport = async () => {
    if (!importFile) {
      message.error('请选择要导入的文件');
      return;
    }

    setImporting(true);
    try {
      const formData = new FormData();
      formData.append('file', importFile);

      const response = await project.supplier.batchImport(formData);

      if (response && response.success) {
        message.success('供应商导入成功');
        setImportModalVisible(false);
        setImportFile(null);
        fetchSuppliers();
      } else {
        message.error(response?.message || '导入供应商失败');
      }
    } catch (error) {
      console.error('导入供应商失败:', error);
      message.error('导入供应商失败: ' + (error.response?.data?.message || '服务器错误'));
    } finally {
      setImporting(false);
    }
  };

  // 处理导出供应商
  const handleExport = async () => {
    try {
      const response = await project.supplier.export({
        search: searchText
      });

      // 创建下载链接
      const url = window.URL.createObjectURL(new Blob([response]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `供应商列表_${new Date().toLocaleDateString()}.xlsx`);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      message.success('供应商导出成功');
    } catch (error) {
      console.error('导出供应商失败:', error);
      message.error('导出供应商失败: ' + (error.response?.data?.message || '服务器错误'));
    }
  };

  // 查看供应商详情
  const handleViewSupplier = (id) => {
    navigate(`/project/purchase/suppliers/detail/${id}`);
  };

  // 供应商列表列定义
  const columns = [
    {
      title: '供应商名称',
      dataIndex: 'name',
      key: 'name',
      ellipsis: true,
      render: (text, record) => (
        <a onClick={() => handleViewSupplier(record.id)}>{text}</a>
      )
    },
    {
      title: '联系人',
      dataIndex: 'contact_person',
      key: 'contact_person',
      width: 120
    },
    {
      title: '联系电话',
      dataIndex: 'contact_phone',
      key: 'contact_phone',
      width: 150
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      key: 'email',
      width: 180,
      ellipsis: true
    },
    {
      title: '地址',
      dataIndex: 'address',
      key: 'address',
      ellipsis: true
    },
    {
      title: '操作',
      key: 'action',
      width: 180,
      render: (_, record) => (
        <Space size="small">
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => handleViewSupplier(record.id)}
          >
            查看
          </Button>
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => handleEditSupplier(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除此供应商吗？"
            onConfirm={() => handleDeleteSupplier(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];
  return (
    <div className="supplier-management-page">
      <Card>
        <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 16 }}>
          <Title level={4}>供应商管理</Title>
          <Space>
            <Input
              placeholder="搜索供应商"
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              onPressEnter={() => fetchSuppliers()}
              style={{ width: 200 }}
              prefix={<SearchOutlined />}
              ref={searchInputRef}
            />
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAddSupplier}
            >
              新增供应商
            </Button>
            <Button
              icon={<ImportOutlined />}
              onClick={() => setImportModalVisible(true)}
            >
              导入
            </Button>
            <Button
              icon={<ExportOutlined />}
              onClick={handleExport}
            >
              导出
            </Button>
            <Button
              icon={<ReloadOutlined />}
              onClick={() => {
                setSearchText('');
                setPagination({ ...pagination, current: 1 });
                fetchSuppliers();
              }}
            >
              刷新
            </Button>
          </Space>
        </div>

        <Table
          columns={columns}
          dataSource={suppliers}
          rowKey="id"
          pagination={{
            ...pagination,
            total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条`
          }}
          loading={loading}
          onChange={handleTableChange}
        />
      </Card>

      {/* 供应商编辑/新增模态框 */}
      <Modal
        title={editingSupplier ? '编辑供应商' : '添加供应商'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={[
          <Button key="cancel" onClick={() => setModalVisible(false)}>
            取消
          </Button>,
          <Button
            key="submit"
            type="primary"
            loading={submitting}
            onClick={handleFormSubmit}
          >
            保存
          </Button>,
        ]}
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Form.Item
            name="name"
            label="供应商名称"
            rules={[{ required: true, message: '请输入供应商名称' }]}
          >
            <Input placeholder="请输入供应商名称" />
          </Form.Item>

          <Form.Item
            name="contact_person"
            label="联系人"
          >
            <Input placeholder="请输入联系人姓名" prefix={<UserOutlined />} />
          </Form.Item>

          <Form.Item
            name="contact_phone"
            label="联系电话"
          >
            <Input placeholder="请输入联系电话" prefix={<PhoneOutlined />} />
          </Form.Item>

          <Form.Item
            name="email"
            label="电子邮箱"
            rules={[
              { type: 'email', message: '请输入有效的电子邮箱地址' }
            ]}
          >
            <Input placeholder="请输入电子邮箱" prefix={<MailOutlined />} />
          </Form.Item>

          <Form.Item
            name="address"
            label="地址"
          >
            <Input placeholder="请输入地址" prefix={<HomeOutlined />} />
          </Form.Item>

          <Form.Item
            name="description"
            label="描述"
          >
            <Input.TextArea rows={4} placeholder="请输入供应商描述" />
          </Form.Item>
        </Form>
      </Modal>

      {/* 导入供应商模态框 */}
      <Modal
        title="导入供应商"
        open={importModalVisible}
        onCancel={() => {
          setImportModalVisible(false);
          setImportFile(null);
        }}
        footer={[
          <Button
            key="cancel"
            onClick={() => {
              setImportModalVisible(false);
              setImportFile(null);
            }}
          >
            取消
          </Button>,
          <Button
            key="submit"
            type="primary"
            loading={importing}
            onClick={handleImport}
            disabled={!importFile}
          >
            导入
          </Button>,
        ]}
      >
        <Paragraph>
          请上传Excel文件，文件中应包含以下列：供应商名称、联系人、联系电话、电子邮箱、地址。
        </Paragraph>

        <Upload
          beforeUpload={(file) => {
            const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                           file.type === 'application/vnd.ms-excel';
            if (!isExcel) {
              message.error('只能上传Excel文件!');
              return Upload.LIST_IGNORE;
            }
            setImportFile(file);
            return false;
          }}
          onRemove={() => {
            setImportFile(null);
          }}
          fileList={importFile ? [importFile] : []}
          maxCount={1}
        >
          <Button icon={<UploadOutlined />}>选择文件</Button>
        </Upload>

        <div style={{ marginTop: 16 }}>
          <Button
            type="link"
            icon={<DownloadOutlined />}
            onClick={() => {
              // 下载模板
              window.open('/templates/supplier_import_template.xlsx');
            }}
          >
            下载导入模板
          </Button>
        </div>
      </Modal>
    </div>
  );
};

export default SupplierManagement;