.service-provider-dashboard {
  padding: 24px;
}

.dashboard-header {
  margin-bottom: 24px;
}

.stats-row {
  margin-bottom: 16px;
}

.stat-card {
  height: 100%;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
  transition: all 0.3s;
}

.stat-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.stat-footer {
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid #f0f0f0;
}

.revenue-card {
  background: linear-gradient(135deg, #fff8f8 0%, #fff 100%);
}

.tenants-card, .activities-card, .quick-actions-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
}

.action-button {
  height: 60px;
  border-radius: 6px;
  font-size: 16px;
  transition: all 0.3s;
}

.action-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .service-provider-dashboard {
    padding: 16px;
  }
  
  .stat-card {
    margin-bottom: 16px;
  }
}
