#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
为 sapi_mcp_servers 表添加部署模式相关列的迁移脚本
"""

import logging
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession

logger = logging.getLogger(__name__)

async def migrate(db: AsyncSession) -> bool:
    """
    为 sapi_mcp_servers 表添加部署模式相关列
    
    Args:
        db: 数据库会话
        
    Returns:
        bool: 迁移是否成功
    """
    try:
        # 检查表是否存在
        result = await db.execute(text("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables
                WHERE table_name = 'sapi_mcp_servers'
            );
        """))
        
        table_exists = result.scalar()
        
        if not table_exists:
            logger.warning("sapi_mcp_servers 表不存在，无法添加部署模式相关列")
            return True
            
        # 检查 deployment_mode 列是否存在
        result = await db.execute(text("""
            SELECT EXISTS (
                SELECT FROM information_schema.columns
                WHERE table_name = 'sapi_mcp_servers' AND column_name = 'deployment_mode'
            );
        """))
        
        deployment_mode_exists = result.scalar()
        
        if not deployment_mode_exists:
            # 添加 deployment_mode 列
            await db.execute(text("""
                ALTER TABLE sapi_mcp_servers
                ADD COLUMN deployment_mode VARCHAR(20) DEFAULT 'local';
            """))
            
            await db.commit()
            logger.info("成功为 sapi_mcp_servers 表添加 deployment_mode 列")
        else:
            logger.info("sapi_mcp_servers 表已有 deployment_mode 列，无需添加")
            
        # 检查 docker_image 列是否存在
        result = await db.execute(text("""
            SELECT EXISTS (
                SELECT FROM information_schema.columns
                WHERE table_name = 'sapi_mcp_servers' AND column_name = 'docker_image'
            );
        """))
        
        docker_image_exists = result.scalar()
        
        if not docker_image_exists:
            # 添加 docker_image 列
            await db.execute(text("""
                ALTER TABLE sapi_mcp_servers
                ADD COLUMN docker_image VARCHAR(200);
            """))
            
            await db.commit()
            logger.info("成功为 sapi_mcp_servers 表添加 docker_image 列")
        else:
            logger.info("sapi_mcp_servers 表已有 docker_image 列，无需添加")
            
        # 检查 docker_tag 列是否存在
        result = await db.execute(text("""
            SELECT EXISTS (
                SELECT FROM information_schema.columns
                WHERE table_name = 'sapi_mcp_servers' AND column_name = 'docker_tag'
            );
        """))
        
        docker_tag_exists = result.scalar()
        
        if not docker_tag_exists:
            # 添加 docker_tag 列
            await db.execute(text("""
                ALTER TABLE sapi_mcp_servers
                ADD COLUMN docker_tag VARCHAR(50) DEFAULT 'latest';
            """))
            
            await db.commit()
            logger.info("成功为 sapi_mcp_servers 表添加 docker_tag 列")
        else:
            logger.info("sapi_mcp_servers 表已有 docker_tag 列，无需添加")
            
        # 检查 npm_package 列是否存在
        result = await db.execute(text("""
            SELECT EXISTS (
                SELECT FROM information_schema.columns
                WHERE table_name = 'sapi_mcp_servers' AND column_name = 'npm_package'
            );
        """))
        
        npm_package_exists = result.scalar()
        
        if not npm_package_exists:
            # 添加 npm_package 列
            await db.execute(text("""
                ALTER TABLE sapi_mcp_servers
                ADD COLUMN npm_package VARCHAR(200);
            """))
            
            await db.commit()
            logger.info("成功为 sapi_mcp_servers 表添加 npm_package 列")
        else:
            logger.info("sapi_mcp_servers 表已有 npm_package 列，无需添加")
            
        # 检查 npm_version 列是否存在
        result = await db.execute(text("""
            SELECT EXISTS (
                SELECT FROM information_schema.columns
                WHERE table_name = 'sapi_mcp_servers' AND column_name = 'npm_version'
            );
        """))
        
        npm_version_exists = result.scalar()
        
        if not npm_version_exists:
            # 添加 npm_version 列
            await db.execute(text("""
                ALTER TABLE sapi_mcp_servers
                ADD COLUMN npm_version VARCHAR(50) DEFAULT 'latest';
            """))
            
            await db.commit()
            logger.info("成功为 sapi_mcp_servers 表添加 npm_version 列")
        else:
            logger.info("sapi_mcp_servers 表已有 npm_version 列，无需添加")
            
        return True
            
    except Exception as e:
        await db.rollback()
        logger.error(f"为 sapi_mcp_servers 表添加部署模式相关列失败: {str(e)}")
        raise
