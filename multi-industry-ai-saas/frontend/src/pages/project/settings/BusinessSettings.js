import React, { useState, useEffect } from 'react';
import {
  Card,
  Tabs,
  Button,
  Form,
  Input,
  Select,
  Switch,
  message,
  Table,
  Space,
  Popconfirm,
  Typography,
  Row,
  Col,
  Divider,
  Upload,
  InputNumber,
  Tag
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SaveOutlined,
  CloseOutlined,
  UploadOutlined,
  DownloadOutlined
} from '@ant-design/icons';
import apiService from '../../../services/api';
import { useNavigate } from 'react-router-dom';

const { Title, Text } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;

/**
 * 业务设置组件
 */
const BusinessSettings = () => {
  const [activeTab, setActiveTab] = useState('workflow');
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  // 处理标签页切换
  const handleTabChange = (key) => {
    setActiveTab(key);
  };

  // 渲染工作流配置标签页
  const renderWorkflowTab = () => {
    return (
      <WorkflowSettings />
    );
  };

  // 渲染空间设置标签页
  const renderSpaceTab = () => {
    return (
      <SpaceSettings />
    );
  };

  return (
    <Card title="业务设置">
      <Tabs activeKey={activeTab} onChange={handleTabChange}>
        <TabPane tab="工作流配置" key="workflow">
          {renderWorkflowTab()}
        </TabPane>
        <TabPane tab="空间设置" key="space">
          {renderSpaceTab()}
        </TabPane>
      </Tabs>
    </Card>
  );
};

/**
 * 工作流设置组件
 */
const WorkflowSettings = () => {
  const [workflows, setWorkflows] = useState([]);
  const [loading, setLoading] = useState(false);
  const [editingWorkflow, setEditingWorkflow] = useState(null);
  const [form] = Form.useForm();
  const navigate = useNavigate(); //

  // 获取工作流列表
  const fetchWorkflows = async () => {
    setLoading(true);
    try {
      const response = await apiService.project.businessSettings.getWorkflows();
      if (response && response.items) {
        setWorkflows(response.items);
      }
    } catch (error) {
      console.error('获取工作流列表失败:', error);
      message.error('获取工作流列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 初始化
  useEffect(() => {
    fetchWorkflows();
  }, []);

  // 处理编辑工作流
  const handleEdit = (workflow) => {
    setEditingWorkflow(workflow);
    form.setFieldsValue({
      name: workflow.name,
      description: workflow.description,
      is_default: workflow.is_default,
      is_active: workflow.is_active
    });
  };

  // 处理保存工作流
  const handleSave = async () => {
    try {
      const values = await form.validateFields();
      const response = await apiService.project.businessSettings.updateWorkflow(
        editingWorkflow.id,
        values
      );
      if (response && response.success) {
        message.success('工作流更新成功');
        setEditingWorkflow(null);
        fetchWorkflows();
      } else {
        message.error('工作流更新失败');
      }
    } catch (error) {
      console.error('更新工作流失败:', error);
      message.error('更新工作流失败');
    }
  };

  // 处理取消编辑
  const handleCancel = () => {
    setEditingWorkflow(null);
    form.resetFields();
  };

  // 表格列定义
  const columns = [
    {
      title: '工作流名称',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <span>
          {text}
          {record.is_default && <Tag color="blue" style={{ marginLeft: 8 }}>默认</Tag>}
        </span>
      )
    },
    {
      title: '工作流类型',
      dataIndex: 'type',
      key: 'type',
      render: (text) => {
        const typeMap = {
          'loss_report': '报损工作流',
          'purchase_order': '采购工作流',
          'inventory_check': '盘点工作流'
        };
        return typeMap[text] || text;
      }
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description'
    },
    {
      title: '状态',
      dataIndex: 'is_active',
      key: 'is_active',
      render: (active) => (
        <Tag color={active ? 'green' : 'red'}>
          {active ? '启用' : '禁用'}
        </Tag>
      )
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="small">
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Button
            type="link"
            onClick={() => navigate(`/project/settings/workflow/${record.id}`)}
          >
            配置
          </Button>
        </Space>
      )
    }
  ];

  return (
    <div>
      <div style={{ marginBottom: 16 }}>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => navigate('/project/settings/workflow/create')}
        >
          创建工作流
        </Button>
      </div>

      <Table
        columns={columns}
        dataSource={workflows}
        rowKey="id"
        loading={loading}
        pagination={{ pageSize: 10 }}
      />

      {editingWorkflow && (
        <Card
          title="编辑工作流"
          style={{ marginTop: 16 }}
          extra={
            <Space>
              <Button
                type="primary"
                icon={<SaveOutlined />}
                onClick={handleSave}
              >
                保存
              </Button>
              <Button
                icon={<CloseOutlined />}
                onClick={handleCancel}
              >
                取消
              </Button>
            </Space>
          }
        >
          <Form
            form={form}
            layout="vertical"
            initialValues={{
              name: editingWorkflow.name,
              description: editingWorkflow.description,
              is_default: editingWorkflow.is_default,
              is_active: editingWorkflow.is_active
            }}
          >
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="name"
                  label="工作流名称"
                  rules={[{ required: true, message: '请输入工作流名称' }]}
                >
                  <Input placeholder="请输入工作流名称" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="description"
                  label="描述"
                >
                  <Input placeholder="请输入描述" />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="is_default"
                  label="默认工作流"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="is_active"
                  label="启用状态"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
              </Col>
            </Row>
          </Form>
        </Card>
      )}
    </div>
  );
};

/**
 * 空间设置组件
 */
const SpaceSettings = () => {
  const [settings, setSettings] = useState(null);
  const [moduleSettings, setModuleSettings] = useState([]);
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();
  const [moduleForm] = Form.useForm();
  const [editingModule, setEditingModule] = useState(null);

  // 获取空间设置
  const fetchSettings = async () => {
    setLoading(true);
    try {
      const response = await apiService.project.businessSettings.getSpaceSettings();
      if (response && response.data) {
        setSettings(response.data);
        setModuleSettings(response.data.module_settings || []);
        form.setFieldsValue({
          max_file_size: response.data.max_file_size / (1024 * 1024), // 转换为MB
          allowed_extensions: response.data.allowed_extensions,
          max_storage_per_user: response.data.max_storage_per_user / (1024 * 1024), // 转换为MB
          max_storage_per_project: response.data.max_storage_per_project / (1024 * 1024 * 1024) // 转换为GB
        });
      }
    } catch (error) {
      console.error('获取空间设置失败:', error);
      message.error('获取空间设置失败');
    } finally {
      setLoading(false);
    }
  };

  // 初始化
  useEffect(() => {
    fetchSettings();
  }, []);

  // 处理保存设置
  const handleSave = async () => {
    try {
      const values = await form.validateFields();
      const data = {
        max_file_size: values.max_file_size * 1024 * 1024, // 转换为字节
        allowed_extensions: values.allowed_extensions,
        max_storage_per_user: values.max_storage_per_user * 1024 * 1024, // 转换为字节
        max_storage_per_project: values.max_storage_per_project * 1024 * 1024 * 1024 // 转换为字节
      };
      
      const response = await apiService.project.businessSettings.updateSpaceSettings(data);
      if (response && response.success) {
        message.success('空间设置更新成功');
        fetchSettings();
      } else {
        message.error('空间设置更新失败');
      }
    } catch (error) {
      console.error('更新空间设置失败:', error);
      message.error('更新空间设置失败');
    }
  };

  // 处理编辑模块设置
  const handleEditModule = (module) => {
    setEditingModule(module);
    moduleForm.setFieldsValue({
      module_name: module.module_name,
      max_file_size: module.max_file_size ? module.max_file_size / (1024 * 1024) : null, // 转换为MB
      allowed_extensions: module.allowed_extensions
    });
  };

  // 处理保存模块设置
  const handleSaveModule = async () => {
    try {
      const values = await moduleForm.validateFields();
      const data = {
        module_name: values.module_name,
        max_file_size: values.max_file_size ? values.max_file_size * 1024 * 1024 : null, // 转换为字节
        allowed_extensions: values.allowed_extensions
      };
      
      const response = await apiService.project.businessSettings.updateModuleSpaceSettings(
        editingModule.id,
        data
      );
      if (response && response.success) {
        message.success('模块空间设置更新成功');
        setEditingModule(null);
        fetchSettings();
      } else {
        message.error('模块空间设置更新失败');
      }
    } catch (error) {
      console.error('更新模块空间设置失败:', error);
      message.error('更新模块空间设置失败');
    }
  };

  // 处理取消编辑模块
  const handleCancelModule = () => {
    setEditingModule(null);
    moduleForm.resetFields();
  };

  // 处理删除模块设置
  const handleDeleteModule = async (id) => {
    try {
      const response = await apiService.project.businessSettings.deleteModuleSpaceSettings(id);
      if (response && response.success) {
        message.success('模块空间设置删除成功');
        fetchSettings();
      } else {
        message.error('模块空间设置删除失败');
      }
    } catch (error) {
      console.error('删除模块空间设置失败:', error);
      message.error('删除模块空间设置失败');
    }
  };

  // 处理创建模块设置
  const handleCreateModule = () => {
    setEditingModule({
      id: 'new',
      module_code: '',
      module_name: '',
      max_file_size: null,
      allowed_extensions: []
    });
    moduleForm.resetFields();
  };

  // 处理保存新模块设置
  const handleSaveNewModule = async () => {
    try {
      const values = await moduleForm.validateFields();
      const data = {
        module_code: values.module_code,
        module_name: values.module_name,
        max_file_size: values.max_file_size ? values.max_file_size * 1024 * 1024 : null, // 转换为字节
        allowed_extensions: values.allowed_extensions
      };
      
      const response = await apiService.project.businessSettings.createModuleSpaceSettings(data);
      if (response && response.success) {
        message.success('模块空间设置创建成功');
        setEditingModule(null);
        fetchSettings();
      } else {
        message.error('模块空间设置创建失败');
      }
    } catch (error) {
      console.error('创建模块空间设置失败:', error);
      message.error('创建模块空间设置失败');
    }
  };

  // 模块设置表格列定义
  const moduleColumns = [
    {
      title: '模块名称',
      dataIndex: 'module_name',
      key: 'module_name'
    },
    {
      title: '模块代码',
      dataIndex: 'module_code',
      key: 'module_code'
    },
    {
      title: '最大文件大小',
      dataIndex: 'max_file_size',
      key: 'max_file_size',
      render: (size) => size ? `${(size / (1024 * 1024)).toFixed(2)} MB` : '使用全局设置'
    },
    {
      title: '允许的文件类型',
      dataIndex: 'allowed_extensions',
      key: 'allowed_extensions',
      render: (extensions) => extensions ? extensions.join(', ') : '使用全局设置'
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="small">
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEditModule(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除此模块设置吗?"
            onConfirm={() => handleDeleteModule(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      )
    }
  ];

  return (
    <div>
      <Card title="全局空间设置">
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSave}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="max_file_size"
                label="最大文件大小 (MB)"
                rules={[{ required: true, message: '请输入最大文件大小' }]}
              >
                <InputNumber min={1} max={1000} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="allowed_extensions"
                label="允许的文件类型"
                rules={[{ required: true, message: '请选择允许的文件类型' }]}
              >
                <Select
                  mode="tags"
                  style={{ width: '100%' }}
                  placeholder="请输入文件扩展名，如jpg, pdf"
                  tokenSeparators={[',']}
                >
                  <Option value="jpg">jpg</Option>
                  <Option value="jpeg">jpeg</Option>
                  <Option value="png">png</Option>
                  <Option value="gif">gif</Option>
                  <Option value="pdf">pdf</Option>
                  <Option value="doc">doc</Option>
                  <Option value="docx">docx</Option>
                  <Option value="xls">xls</Option>
                  <Option value="xlsx">xlsx</Option>
                  <Option value="ppt">ppt</Option>
                  <Option value="pptx">pptx</Option>
                  <Option value="txt">txt</Option>
                  <Option value="zip">zip</Option>
                  <Option value="rar">rar</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="max_storage_per_user"
                label="每用户最大存储空间 (MB)"
                rules={[{ required: true, message: '请输入每用户最大存储空间' }]}
              >
                <InputNumber min={1} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="max_storage_per_project"
                label="每项目最大存储空间 (GB)"
                rules={[{ required: true, message: '请输入每项目最大存储空间' }]}
              >
                <InputNumber min={1} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>
          <Form.Item>
            <Button type="primary" htmlType="submit" loading={loading}>
              保存设置
            </Button>
          </Form.Item>
        </Form>
      </Card>

      <Divider orientation="left">模块空间设置</Divider>

      <div style={{ marginBottom: 16 }}>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={handleCreateModule}
        >
          添加模块设置
        </Button>
      </div>

      <Table
        columns={moduleColumns}
        dataSource={moduleSettings}
        rowKey="id"
        loading={loading}
        pagination={{ pageSize: 10 }}
      />

      {editingModule && (
        <Card
          title={editingModule.id === 'new' ? "添加模块设置" : "编辑模块设置"}
          style={{ marginTop: 16 }}
          extra={
            <Space>
              <Button
                type="primary"
                icon={<SaveOutlined />}
                onClick={editingModule.id === 'new' ? handleSaveNewModule : handleSaveModule}
              >
                保存
              </Button>
              <Button
                icon={<CloseOutlined />}
                onClick={handleCancelModule}
              >
                取消
              </Button>
            </Space>
          }
        >
          <Form
            form={moduleForm}
            layout="vertical"
            initialValues={
              editingModule.id !== 'new' ? {
                module_name: editingModule.module_name,
                max_file_size: editingModule.max_file_size ? editingModule.max_file_size / (1024 * 1024) : null,
                allowed_extensions: editingModule.allowed_extensions
              } : {}
            }
          >
            {editingModule.id === 'new' && (
              <Form.Item
                name="module_code"
                label="模块代码"
                rules={[{ required: true, message: '请输入模块代码' }]}
              >
                <Input placeholder="请输入模块代码，如loss_report" />
              </Form.Item>
            )}
            <Form.Item
              name="module_name"
              label="模块名称"
              rules={[{ required: true, message: '请输入模块名称' }]}
            >
              <Input placeholder="请输入模块名称，如报损申请" />
            </Form.Item>
            <Form.Item
              name="max_file_size"
              label="最大文件大小 (MB)"
              extra="留空则使用全局设置"
            >
              <InputNumber min={1} max={1000} style={{ width: '100%' }} />
            </Form.Item>
            <Form.Item
              name="allowed_extensions"
              label="允许的文件类型"
              extra="留空则使用全局设置"
            >
              <Select
                mode="tags"
                style={{ width: '100%' }}
                placeholder="请输入文件扩展名，如jpg, pdf"
                tokenSeparators={[',']}
                allowClear
              >
                <Option value="jpg">jpg</Option>
                <Option value="jpeg">jpeg</Option>
                <Option value="png">png</Option>
                <Option value="gif">gif</Option>
                <Option value="pdf">pdf</Option>
                <Option value="doc">doc</Option>
                <Option value="docx">docx</Option>
                <Option value="xls">xls</Option>
                <Option value="xlsx">xlsx</Option>
                <Option value="ppt">ppt</Option>
                <Option value="pptx">pptx</Option>
                <Option value="txt">txt</Option>
                <Option value="zip">zip</Option>
                <Option value="rar">rar</Option>
              </Select>
            </Form.Item>
          </Form>
        </Card>
      )}
    </div>
  );
};

export default BusinessSettings;
