#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试插件安装功能

此脚本用于测试租户级别的插件安装功能是否正常工作。
"""

import asyncio
import sys
import os
import logging
import json
from sqlalchemy import select, and_
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from db.database import get_engine
from models.plugin import Plugin, TenantPlugin
from models.tenant import Tenant
from models.user import User

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

async def test_plugin_install():
    """测试插件安装功能"""
    try:
        # 获取数据库引擎
        engine = await get_engine()
        
        # 创建会话
        async_session = sessionmaker(
            engine, class_=AsyncSession, expire_on_commit=False
        )
        
        async with async_session() as db:
            # 1. 查询可用插件
            logger.info("=== 查询可用插件 ===")
            plugin_query = select(Plugin).where(Plugin.is_active == True)
            result = await db.execute(plugin_query)
            plugins = result.scalars().all()
            
            logger.info(f"找到 {len(plugins)} 个可用插件:")
            for plugin in plugins:
                logger.info(f"  - {plugin.name} ({plugin.code}) - 价格: {plugin.price}")
            
            # 2. 查询第一个租户
            logger.info("\n=== 查询租户信息 ===")
            tenant_query = select(Tenant).limit(1)
            result = await db.execute(tenant_query)
            tenant = result.scalar_one_or_none()
            
            if not tenant:
                logger.error("没有找到租户")
                return
            
            logger.info(f"使用租户: {tenant.name} ({tenant.id})")
            
            # 3. 查询钉钉机器人插件
            dingtalk_plugin_query = select(Plugin).where(Plugin.code == "dingtalk_robot")
            result = await db.execute(dingtalk_plugin_query)
            dingtalk_plugin = result.scalar_one_or_none()
            
            if not dingtalk_plugin:
                logger.error("钉钉机器人插件未找到")
                return
            
            logger.info(f"钉钉机器人插件: {dingtalk_plugin.name} ({dingtalk_plugin.id})")
            
            # 4. 检查插件是否已安装
            logger.info("\n=== 检查插件安装状态 ===")
            tenant_plugin_query = select(TenantPlugin).where(
                and_(
                    TenantPlugin.tenant_id == tenant.id,
                    TenantPlugin.plugin_id == dingtalk_plugin.id
                )
            )
            result = await db.execute(tenant_plugin_query)
            tenant_plugin = result.scalar_one_or_none()
            
            if tenant_plugin:
                logger.info(f"插件已安装，状态: {tenant_plugin.status}")
                if tenant_plugin.status != "active":
                    logger.info("激活插件...")
                    tenant_plugin.status = "active"
                    await db.commit()
                    logger.info("插件已激活")
            else:
                logger.info("插件未安装，正在安装...")
                
                # 5. 安装插件
                new_tenant_plugin = TenantPlugin(
                    tenant_id=tenant.id,
                    plugin_id=dingtalk_plugin.id,
                    status="active",
                    version=dingtalk_plugin.version,
                    settings={}
                )
                db.add(new_tenant_plugin)
                await db.commit()
                logger.info("插件安装成功")
            
            # 6. 查询已安装的插件
            logger.info("\n=== 查询已安装的插件 ===")
            installed_query = select(
                TenantPlugin,
                Plugin
            ).join(
                Plugin,
                TenantPlugin.plugin_id == Plugin.id
            ).where(
                and_(
                    TenantPlugin.tenant_id == tenant.id,
                    TenantPlugin.status == "active"
                )
            )
            result = await db.execute(installed_query)
            installed_plugins = result.all()
            
            logger.info(f"租户 {tenant.name} 已安装 {len(installed_plugins)} 个插件:")
            for tenant_plugin, plugin in installed_plugins:
                logger.info(f"  - {plugin.name} ({plugin.code}) - 版本: {tenant_plugin.version}")
            
            # 7. 测试插件市场API响应格式
            logger.info("\n=== 测试插件市场API响应格式 ===")
            installed_plugin_ids = {str(tp.plugin_id) for tp, p in installed_plugins}
            
            market_response = {
                "success": True,
                "data": {
                    "plugins": [
                        {
                            "id": str(plugin.id),
                            "code": plugin.code,
                            "name": plugin.name,
                            "description": plugin.description,
                            "version": plugin.version,
                            "author": plugin.author,
                            "website": plugin.website,
                            "icon_url": plugin.icon_url,
                            "category": plugin.category,
                            "price": plugin.price,
                            "billing_cycle": plugin.billing_cycle,
                            "is_system": plugin.is_system,
                            "requires_subscription": plugin.requires_subscription,
                            "features": plugin.features,
                            "is_installed": str(plugin.id) in installed_plugin_ids,
                            "created_at": plugin.created_at.isoformat() if plugin.created_at else None
                        }
                        for plugin in plugins
                    ],
                    "pagination": {
                        "page": 1,
                        "page_size": 20,
                        "total": len(plugins),
                        "pages": 1
                    }
                }
            }
            
            logger.info("插件市场API响应示例:")
            logger.info(json.dumps(market_response, indent=2, ensure_ascii=False))
            
            logger.info("\n=== 测试完成 ===")
    
    except Exception as e:
        logger.error(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_plugin_install()) 