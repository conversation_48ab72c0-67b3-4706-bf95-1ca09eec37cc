#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
智能助手集成平台(SAPI)请求和响应模型
"""
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
from datetime import datetime
import uuid

# 配置相关模型
class ConfigCreate(BaseModel):
    name: str
    description: Optional[str] = None
    model_name: str
    api_key: str
    enabled: bool = True

class ConfigUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    model_name: Optional[str] = None
    api_key: Optional[str] = None
    enabled: Optional[bool] = None

class ConfigResponse(BaseModel):
    id: uuid.UUID
    name: str
    description: Optional[str] = None
    model_name: str
    enabled: bool
    created_at: datetime
    updated_at: Optional[datetime] = None

# 端点相关模型
class EndpointCreate(BaseModel):
    name: str
    description: Optional[str] = None
    endpoint_path: str
    method: str
    requires_auth: bool = True
    allowed_roles: Optional[str] = None
    parameters: Dict[str, Any] = {}
    response_template: Optional[str] = None
    config_id: uuid.UUID

class EndpointUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    endpoint_path: Optional[str] = None
    method: Optional[str] = None
    requires_auth: Optional[bool] = None
    allowed_roles: Optional[str] = None
    parameters: Optional[Dict[str, Any]] = None
    response_template: Optional[str] = None
    enabled: Optional[bool] = None

class EndpointResponse(BaseModel):
    id: uuid.UUID
    name: str
    description: Optional[str] = None
    endpoint_path: str
    method: str
    requires_auth: bool
    allowed_roles: Optional[str] = None
    parameters: Dict[str, Any]
    response_template: Optional[str] = None
    enabled: bool
    created_at: datetime
    updated_at: Optional[datetime] = None

# 令牌相关模型
class TokenCreate(BaseModel):
    name: str
    description: Optional[str] = None
    permissions: str = "read"  # read, write, read_write
    expires_days: Optional[int] = None  # 过期天数，如果为空则永不过期

class TokenApplyRequest(BaseModel):
    name: str
    description: str
    purpose: str  # 申请目的
    requested_permissions: str = "read"  # 申请的权限

class TokenApprovalRequest(BaseModel):
    status: str  # approved, rejected
    token: Optional[str] = None  # 如果批准，可以指定令牌值，否则自动生成

class TokenResponse(BaseModel):
    id: uuid.UUID
    name: str
    description: Optional[str] = None
    token: str
    permissions: str
    status: str
    created_at: datetime
    last_used: Optional[datetime] = None
    expires_at: Optional[datetime] = None
    user_name: Optional[str] = None

# MCP服务器相关模型
class MCPServerCreate(BaseModel):
    name: str
    description: Optional[str] = None
    command: str
    args: Dict[str, Any] = {}
    env: Dict[str, Any] = {}
    enabled: bool = True

class MCPServerUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    command: Optional[str] = None
    args: Optional[Dict[str, Any]] = None
    env: Optional[Dict[str, Any]] = None
    enabled: Optional[bool] = None

class MCPServerResponse(BaseModel):
    id: uuid.UUID
    name: str
    description: Optional[str] = None
    command: str
    args: Dict[str, Any]
    env: Dict[str, Any]
    enabled: bool
    created_at: datetime
    updated_at: Optional[datetime] = None
    status: str
    started_at: Optional[datetime] = None
    stopped_at: Optional[datetime] = None

# API到MCP映射相关模型
class APIMCPMappingCreate(BaseModel):
    endpoint_id: uuid.UUID
    server_id: uuid.UUID
    tool_name: str
    parameter_mapping: Dict[str, str] = {}
    response_mapping: Dict[str, str] = {}

class APIMCPMappingUpdate(BaseModel):
    tool_name: Optional[str] = None
    parameter_mapping: Optional[Dict[str, str]] = None
    response_mapping: Optional[Dict[str, str]] = None

class APIMCPMappingResponse(BaseModel):
    id: uuid.UUID
    endpoint_id: uuid.UUID
    server_id: uuid.UUID
    tool_name: str
    parameter_mapping: Dict[str, str]
    response_mapping: Dict[str, str]
    created_at: datetime
    updated_at: Optional[datetime] = None
    endpoint: Optional[Dict[str, Any]] = None
    server: Optional[Dict[str, Any]] = None
