# Traefik v3.3 配置文档

## 🏗️ 架构概述

本配置将Traefik v3.3作为主入口网关，负责接收所有外部流量并根据路径进行智能分发。**重要**：Traefik与主项目nginx协同工作，避免端口冲突。

```
外部流量 (80/443) → Traefik v3.3 → 路由分发
                        ├── /mcp/* → MCP服务 (直接处理)
                        ├── /sp/* → 服务商系统 (预留)
                        ├── /api/* → 主项目Nginx容器
                        ├── /uploads/* → 主项目Nginx容器
                        ├── /static/* → 主项目Nginx容器
                        ├── /health → 主项目Nginx容器
                        └── /* → 主项目Nginx容器 (默认)
```

### 🔌 端口分配

| 服务 | 主机端口 | 容器端口 | 说明 |
|------|----------|----------|------|
| Traefik | 80, 443 | 80, 443 | 主入口网关 |
| Traefik Dashboard | 8090 | 8080 | 管理界面 |
| 主项目Nginx | 8080 | 80 | 内部处理主项目请求 |
| MCP服务 | 3100 | 3100 | MCP功能 |
| 服务商系统 | 8083 | 8083 | 预留 |

## 📁 文件结构

```
traefik/
├── docker-compose.yml      # Docker Compose配置
├── traefik.yml            # Traefik主配置文件
├── dynamic/
│   └── services.yml       # 动态路由配置
├── upgrade-to-v3.3.sh     # 升级脚本
└── README.md              # 本文档
```

## 🚀 快速开始

### 前置条件

1. **确保主项目已启动**：
```bash
cd multi-industry-ai-saas/docker
docker-compose up -d
```

2. **验证主项目网络**：
```bash
docker network ls | grep retail-ai-saas-network
```

### 启动Traefik

```bash
# 进入traefik目录
cd multi-industry-ai-saas/traefik/

# 启动Traefik
docker-compose up -d

# 查看日志
docker-compose logs -f traefik

# 检查状态
docker-compose ps
```

### 升级到v3.3

```bash
# 运行升级脚本
chmod +x upgrade-to-v3.3.sh
./upgrade-to-v3.3.sh
```

## 🛣️ 路由配置详解

### 路由优先级

| 路由 | 规则 | 优先级 | 目标服务 | 说明 |
|------|------|--------|----------|------|
| mcp-router | `/mcp/*` | 100 | MCP服务 | Traefik直接处理MCP请求 |
| sp-router | `/sp/*` | 90 | 服务商系统 | 预留后期配置 |
| health-router | `/health` | 85 | 主项目Nginx | 健康检查 |
| api-router | `/api/*` | 80 | 主项目Nginx | API请求转发 |
| uploads-router | `/uploads/*` | 75 | 主项目Nginx | 文件上传 |
| static-router | `/static/*`, `/media/*`, `/assets/*` | 70 | 主项目Nginx | 静态资源 |
| main-router | `/*` | 1 | 主项目Nginx | 默认兜底路由 |

### 中间件功能

- **strip-mcp-prefix**: 移除`/mcp`前缀
- **strip-sp-prefix**: 移除`/sp`前缀  
- **api-headers**: 为API请求添加安全头
- **static-headers**: 为静态资源添加缓存头
- **sse-no-buffer**: SSE连接优化

## 🔧 配置说明

### 网络连接

Traefik通过Docker网络连接到主项目服务：

```yaml
# Traefik连接到主项目网络
networks:
  retail-ai-saas-network:
    external: true  # 使用主项目已创建的网络
```

### 服务地址配置

| 服务 | 地址 | 说明 |
|------|------|------|
| 主项目Nginx | `http://retail-ai-saas-nginx:80` | 通过容器名连接 |
| MCP服务 | `http://**************:3100` | 外部服务地址 |
| 服务商系统 | `http://**************:8083` | 预留配置 |

## 🔍 监控和调试

### 仪表板访问

- URL: http://localhost:8090
- 功能: 查看路由、服务状态、中间件等

### 测试连接

```bash
# 测试主项目连接
curl http://localhost/health

# 测试API连接
curl http://localhost/api/v1/health

# 测试MCP连接（如果MCP服务已启动）
curl http://localhost/mcp/health

# 查看Traefik仪表板
curl http://localhost:8090/api/rawdata
```

### 日志查看

```bash
# Traefik日志
docker-compose logs -f traefik

# 主项目nginx日志
cd ../docker && docker-compose logs -f nginx

# 检查网络连接
docker exec traefik ping retail-ai-saas-nginx
```

## 🚨 故障排除

### 常见问题

1. **无法连接到主项目nginx**
   ```bash
   # 检查网络连接
   docker network inspect retail-ai-saas-network
   
   # 确保主项目已启动
   cd ../docker && docker-compose ps
   ```

2. **端口冲突**
   ```bash
   # 检查端口占用
   netstat -tulpn | grep :80
   netstat -tulpn | grep :8090
   ```

3. **路由不匹配**
   ```bash
   # 查看路由状态
   curl http://localhost:8090/api/http/routers
   ```

### 启动顺序

正确的启动顺序：
1. 启动主项目：`cd docker && docker-compose up -d`
2. 启动Traefik：`cd traefik && docker-compose up -d`

### 回滚步骤

```bash
# 停止Traefik
docker-compose down

# 使用备份快速回滚
cp backup-YYYYMMDD-HHMMSS/* .
docker-compose up -d
```

## 📈 性能优化

### 开发环境建议

1. **保持主项目nginx运行**：避免频繁重启
2. **使用健康检查**：确保服务可用性
3. **监控日志**：及时发现问题

### 生产环境建议

1. **启用HTTPS**：配置SSL证书
2. **添加认证**：保护Traefik仪表板
3. **配置监控**：集成观测性工具

## 🔄 部署流程

### 开发环境部署

```bash
# 1. 启动主项目
cd multi-industry-ai-saas/docker
docker-compose up -d

# 2. 等待服务就绪
sleep 30

# 3. 启动Traefik
cd ../traefik
docker-compose up -d

# 4. 验证服务
curl http://localhost/health
curl http://localhost:8090/ping
```

### 生产环境部署

```bash
# 1. 配置环境变量
export NODE_ENV=production
export DEBUG=false

# 2. 启动主项目
cd docker && docker-compose up -d

# 3. 配置HTTPS（生产环境）
# 编辑 traefik.yml 启用TLS

# 4. 启动Traefik
cd ../traefik && docker-compose up -d
```

## 📞 支持

如有问题，请检查：
1. 主项目是否正常运行
2. Docker网络连接是否正常
3. 端口是否有冲突
4. 配置文件语法是否正确 