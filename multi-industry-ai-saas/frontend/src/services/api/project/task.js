import api from '../httpClient';

const taskAPI = {
  // 获取任务列表（支持筛选）
  getList: (params = {}) => {
    const {
      status,
      task_type,
      task_module,
      start_date,
      end_date,
      search,
      limit = 50,
      offset = 0
    } = params;
    
    const queryParams = new URLSearchParams();
    if (status) queryParams.append('status', status);
    if (task_type) queryParams.append('task_type', task_type);
    if (task_module) queryParams.append('task_module', task_module);
    if (start_date) queryParams.append('start_date', start_date);
    if (end_date) queryParams.append('end_date', end_date);
    if (search) queryParams.append('search', search);
    queryParams.append('limit', limit);
    queryParams.append('offset', offset);
    
    return api.get(`/tasks?${queryParams.toString()}`);
  },

  // 获取任务详情
  getDetail: (taskId) => {
    return api.get(`/tasks/${taskId}`);
  },

  // 取消任务
  cancel: (taskId) => {
    return api.post(`/tasks/${taskId}/cancel`);
  },

  // 删除任务
  delete: (taskId) => {
    return api.delete(`/tasks/${taskId}`);
  },

  // 批量删除任务
  batchDelete: (taskIds) => {
    return api.post('/tasks/batch-delete', {
      task_ids: taskIds
    });
  },

  // 获取任务统计信息
  getStats: () => {
    return api.get('/tasks/stats');
  }
};

export default taskAPI; 