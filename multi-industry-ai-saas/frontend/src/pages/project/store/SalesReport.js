import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  DatePicker,
  Select,
  Input,
  Tag,
  message,
  Typography,
  Row,
  Col,
  Tabs,
  Statistic,
  Popconfirm,
  Empty
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  DownloadOutlined,
  Pie<PERSON><PERSON>Outlined,
  Bar<PERSON><PERSON>Outlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import dayjs from 'dayjs';
import apiService from '../../../services/api';
import { Column, Pie } from '@ant-design/plots';
import dateTimeUtils from '../../../utils/dateTimeUtils';
import SalesMonthlyReport from './SalesMonthlyReport';

const { Title, Text } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;
const { TabPane } = Tabs;

/**
 * 销售上报组件
 * 用于查看和管理门店销售上报，包括渠道销售和充值/售卡
 */
const SalesReport = () => {
  // 状态定义
  const [loading, setLoading] = useState(false);
  const [reports, setReports] = useState([]);
  const [stores, setStores] = useState([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const [filters, setFilters] = useState({
    store_id: null,
    dateRange: [dayjs().subtract(7, 'days'), dayjs()],
    report_type: 'all',
    status: 'all',
    search: ''
  });
  const [activeTab, setActiveTab] = useState('list');
  const [statistics, setStatistics] = useState({
    total_sales: 0,
    total_orders: 0,
    total_recharge_amount: 0,
    total_recharge_orders: 0,
    grand_total_amount: 0,
    grand_total_orders: 0,
  });
  const [channelChartData, setChannelChartData] = useState([]);
  const [paymentMethodChartData, setPaymentMethodChartData] = useState([]);

  const navigate = useNavigate();

  // 报告类型文本映射
  const reportTypeTexts = {
    shift: '班次',
    daily: '日报',
    weekly: '周报',
    monthly: '月报',
    recharge: '充值/售卡'
  };

  // 状态文本映射
  const statusTexts = {
    draft: '草稿',
    submitted: '已提交',
    approved: '已审核',
    rejected: '已拒绝'
  };

  // 状态颜色映射
  const statusColors = {
    draft: 'default',
    submitted: 'processing',
    approved: 'success',
    rejected: 'error'
  };

  // 获取门店列表
  const fetchStores = async () => {
    try {
      const response = await apiService.project.store.getList();
      if (response && response.items) {
        setStores(response.items);
      }
    } catch (error) {
      console.error('获取门店列表失败:', error);
      message.error('获取门店列表失败');
    }
  };

  // 获取销售上报列表
  const fetchReports = async (page = 1, pageSize = 10) => {
    setLoading(true);
    try {
      const params = {
        page: page,
        limit: pageSize,
        store_id: filters.store_id || undefined,
        start_date: filters.dateRange?.[0]?.format('YYYY-MM-DD'),
        end_date: filters.dateRange?.[1]?.format('YYYY-MM-DD'),
        report_type: filters.report_type === 'all' ? undefined : filters.report_type,
        status: filters.status === 'all' ? undefined : filters.status,
        search: filters.search || undefined
      };

      const response = await apiService.project.salesReport.getList(params);
      if (response && response.items) {
        setReports(response.items);
        setPagination({
          current: response.page,
          pageSize: response.limit,
          total: response.total,
        });
        // 异步获取统计数据
        fetchStatistics(params);
      } else {
        setReports([]);
        setPagination(prev => ({ ...prev, total: 0 }));
        resetStatistics();
      }
    } catch (error) {
      console.error('获取销售上报列表失败:', error);
      message.error('获取销售上报列表失败');
      setReports([]);
      resetStatistics();
    } finally {
      setLoading(false);
    }
  };

  // 获取统计数据
  const fetchStatistics = async (params) => {
    try {
      const statsResponse = await apiService.project.salesReport.getStatistics(params);
      if (statsResponse) {
        setStatistics({
          total_sales: statsResponse.total_sales || 0,
          total_orders: statsResponse.total_orders || 0,
          total_recharge_amount: statsResponse.total_recharge_amount || 0,
          total_recharge_orders: statsResponse.total_recharge_orders || 0,
          grand_total_amount: statsResponse.grand_total_amount || 0,
          grand_total_orders: statsResponse.grand_total_orders || 0,
        });
        setChannelChartData(statsResponse.channel_summary || []);
        setPaymentMethodChartData(statsResponse.payment_method_summary || []);
      }
    } catch (error) {
      console.error('获取统计数据失败:', error);
      // Don't show a message, as this is a background task
    }
  };

  // 初始化
  useEffect(() => {
    // 初始化日期时间工具
    dateTimeUtils.initDateTimeUtils();
    fetchStores();
  }, []);

  // 当筛选条件变化时，重新获取数据
  useEffect(() => {
    fetchReports(1, pagination.pageSize);
  }, [filters]);

  // 重置统计数据
  const resetStatistics = () => {
    setStatistics({
      total_sales: 0,
      total_orders: 0,
      total_recharge_amount: 0,
      total_recharge_orders: 0,
      grand_total_amount: 0,
      grand_total_orders: 0,
    });
    setChannelChartData([]);
    setPaymentMethodChartData([]);
  };

  // 处理表格变化（分页、排序、筛选）
  const handleTableChange = (pagination, tableFilters, sorter) => {
    fetchReports(pagination.current, pagination.pageSize);
  };
  
  // 处理筛选条件变化
  const handleFilterChange = (key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleDelete = async (id) => {
    try {
      const response = await apiService.project.salesReport.delete(id);
      if (response) {
        message.success('删除成功');
        fetchReports(pagination.current, pagination.pageSize);
      } else {
        message.error(response?.message || '删除失败');
      }
    } catch (error) {
      console.error('删除销售上报失败:', error);
      message.error('删除失败');
    }
  };

  // 表格列定义
  const columns = [
    {
      title: '门店',
      dataIndex: ['store', 'name'],
      key: 'store_name',
    },
    {
      title: '上报日期',
      dataIndex: 'report_date',
      key: 'report_date',
      sorter: true,
      render: (text) => dateTimeUtils.formatDate(text),
    },
    {
      title: '报告类型',
      dataIndex: 'report_type',
      key: 'report_type',
      render: (text) => reportTypeTexts[text] || text,
    },
    {
      title: '销售总额',
      dataIndex: 'total_sales',
      key: 'total_sales',
      render: (text) => `¥${parseFloat(text || 0).toFixed(2)}`,
      sorter: (a, b) => a.total_sales - b.total_sales,
    },
    {
      title: '销售笔数',
      dataIndex: 'total_orders',
      key: 'total_orders',
       sorter: (a, b) => a.total_orders - b.total_orders,
    },
    {
      title: '充值/售卡总额',
      dataIndex: 'total_recharge_amount',
      key: 'total_recharge_amount',
      render: (text) => `¥${parseFloat(text || 0).toFixed(2)}`,
      sorter: (a, b) => a.total_recharge_amount - b.total_recharge_amount,
    },
    {
      title: '充值/售卡笔数',
      dataIndex: 'total_recharge_orders',
      key: 'total_recharge_orders',
       sorter: (a, b) => a.total_recharge_orders - b.total_recharge_orders
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (text) => (
        <Tag color={statusColors[text] || 'default'}>
          {statusTexts[text] || text}
        </Tag>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      sorter: true,
      render: (text) => dateTimeUtils.formatDateTime(text),
    },
    {
      title: '最后更新',
      dataIndex: 'updated_at',
      key: 'updated_at',
      render: (text) => dateTimeUtils.formatDateTime(text),
    },
    {
      title: '操作',
      key: 'action',
      width: '15%',
      render: (text, record) => (
        <Space size="small">
          <Button
            type="link"
            icon={<EyeOutlined />}
            onClick={() => navigate(`/project/store/sales-report/detail/${record.id}`)}
          >
            查看
          </Button>
          {record.status === 'draft' && (
            <Button
              type="link"
              icon={<EditOutlined />}
              onClick={() => navigate(`/project/store/sales-report/edit/${record.id}`)}
            >
              编辑
            </Button>
          )}
          {record.status === 'draft' && (
            <Popconfirm
              title="确定删除吗？"
              onConfirm={() => handleDelete(record.id)}
            >
              <Button type="link" danger icon={<DeleteOutlined />}>
                删除
              </Button>
            </Popconfirm>
          )}
        </Space>
      ),
    },
  ];

  const renderStatisticsCards = () => (
    <Row gutter={16}>
      <Col span={4}>
        <Card>
          <Statistic title="销售总额" value={statistics.total_sales} precision={2} prefix="¥" />
        </Card>
      </Col>
      <Col span={4}>
        <Card>
          <Statistic title="销售总笔数" value={statistics.total_orders} />
        </Card>
      </Col>
      <Col span={4}>
        <Card>
          <Statistic title="充值/售卡总额" value={statistics.total_recharge_amount} precision={2} prefix="¥" />
        </Card>
      </Col>
      <Col span={4}>
        <Card>
          <Statistic title="充值/售卡总笔数" value={statistics.total_recharge_orders} />
        </Card>
      </Col>
      <Col span={4}>
        <Card>
          <Statistic title="合计总额" value={statistics.grand_total_amount} precision={2} prefix="¥" />
        </Card>
      </Col>
      <Col span={4}>
        <Card>
          <Statistic title="合计总笔数" value={statistics.grand_total_orders} />
        </Card>
      </Col>
    </Row>
  );

  const renderChannelSalesChart = () => {
    const config = {
      data: channelChartData,
      xField: 'total_sales',
      yField: 'channel_name',
      seriesField: 'channel_name',
      legend: { position: 'top-left' },
      tooltip: {
        formatter: (datum) => ({ name: '销售额', value: `¥${datum.total_sales.toFixed(2)}` }),
      },
    };
    return <Column {...config} />;
  };

  const renderPaymentMethodChart = () => {
    const config = {
      appendPadding: 10,
      data: paymentMethodChartData,
      angleField: 'total_amount',
      colorField: 'payment_method_name',
      radius: 0.8,
      label: {
        type: 'inner',
        offset: '-50%',
        content: '{value}',
        style: {
          textAlign: 'center',
          fontSize: 14,
        },
      },
      interactions: [{ type: 'element-active' }],
      tooltip: {
        formatter: (datum) => ({ name: datum.payment_method_name, value: `¥${datum.total_amount.toFixed(2)}` }),
      },
    };
    return <Pie {...config} />;
  };
  
  return (
    <Card>
      <Title level={4}>销售上报管理</Title>
      
      <Card style={{ marginBottom: 16 }}>
        <Row gutter={16} align="bottom">
          <Col span={6}>
            <Text>门店</Text>
            <Select
              style={{ width: '100%' }}
              placeholder="所有门店"
              allowClear
              onChange={(value) => handleFilterChange('store_id', value)}
              value={filters.store_id}
            >
              {stores.map(store => (
                <Option key={store.id} value={store.id}>{store.name}</Option>
              ))}
            </Select>
          </Col>
          <Col span={6}>
            <Text>上报日期</Text>
            <RangePicker
              style={{ width: '100%' }}
              value={filters.dateRange}
              onChange={(dates) => handleFilterChange('dateRange', dates)}
            />
          </Col>
          <Col span={4}>
            <Text>报告类型</Text>
            <Select
              style={{ width: '100%' }}
              value={filters.report_type}
              onChange={(value) => handleFilterChange('report_type', value)}
            >
              <Option value="all">所有类型</Option>
              <Option value="SHIFT">班次</Option>
              <Option value="DAILY">日报</Option>
            </Select>
          </Col>
          <Col span={4}>
            <Text>状态</Text>
             <Select
              style={{ width: '100%' }}
              value={filters.status}
              onChange={(value) => handleFilterChange('status', value)}
            >
              <Option value="all">所有状态</Option>
              <Option value="draft">草稿</Option>
              <Option value="submitted">已提交</Option>
              <Option value="approved">已审核</Option>
              <Option value="rejected">已拒绝</Option>
            </Select>
          </Col>
           <Col span={4}>
            <Text>搜索</Text>
            <Input.Search
              placeholder="搜索..."
              onSearch={(value) => handleFilterChange('search', value)}
              onChange={(e) => handleFilterChange('search', e.target.value)}
              value={filters.search}
              allowClear
            />
          </Col>
        </Row>
      </Card>

      <Tabs activeKey={activeTab} onChange={setActiveTab} tabBarExtraContent={
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => navigate('/project/store/sales-report/create')}
        >
          新建上报
        </Button>
      }>
        <TabPane tab="上报列表" key="list">
          <Table
            columns={columns}
            dataSource={reports}
            rowKey="id"
            loading={loading}
            pagination={pagination}
            onChange={handleTableChange}
          />
        </TabPane>
        <TabPane tab="统计图表" key="charts">
          {renderStatisticsCards()}
          <Row gutter={16} style={{ marginTop: 24 }}>
            <Col span={12}>
              <Card title="渠道销售分布">
                {channelChartData.length > 0 ? renderChannelSalesChart() : <Empty description="暂无渠道数据"/>}
              </Card>
            </Col>
            <Col span={12}>
              <Card title="支付方式分布">
                 {paymentMethodChartData.length > 0 ? renderPaymentMethodChart() : <Empty description="暂无支付方式数据"/>}
              </Card>
            </Col>
          </Row>
        </TabPane>
        <TabPane tab="月度报告" key="monthly">
            <SalesMonthlyReport filters={{store_id: filters.store_id}} />
        </TabPane>
      </Tabs>
    </Card>
  );
};

export default SalesReport;
