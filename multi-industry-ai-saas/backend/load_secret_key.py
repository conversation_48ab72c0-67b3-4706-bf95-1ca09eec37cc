#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
加载SECRET_KEY工具

用于从环境变量或配置文件中加载SECRET_KEY，确保系统中所有组件使用相同的密钥
"""

import os
import logging
from pathlib import Path
import json

# 配置日志
logger = logging.getLogger(__name__)

def load_secret_key() -> str:
    """
    加载SECRET_KEY
    
    优先从环境变量获取，如果环境变量中没有，则从配置文件中获取
    如果都没有，则生成一个新的SECRET_KEY并保存到配置文件中
    
    Returns:
        str: SECRET_KEY
    """
    # 优先从环境变量获取
    secret_key = os.environ.get("SECRET_KEY")
    if secret_key:
        logger.info("从环境变量加载SECRET_KEY成功")
        return secret_key
    
    # 从配置文件获取
    config_dir = Path.home() / ".retail-ai-saas"
    config_file = config_dir / "config.json"
    
    if config_file.exists():
        try:
            with open(config_file, "r") as f:
                config = json.load(f)
                secret_key = config.get("SECRET_KEY")
                if secret_key:
                    logger.info("从配置文件加载SECRET_KEY成功")
                    return secret_key
        except Exception as e:
            logger.error(f"从配置文件加载SECRET_KEY失败: {str(e)}")
    
    # 生成新的SECRET_KEY
    import secrets
    secret_key = secrets.token_hex(32)
    logger.warning(f"生成新的SECRET_KEY: {secret_key[:5]}...")
    
    # 保存到配置文件
    try:
        config_dir.mkdir(exist_ok=True)
        
        config = {}
        if config_file.exists():
            try:
                with open(config_file, "r") as f:
                    config = json.load(f)
            except:
                pass
        
        config["SECRET_KEY"] = secret_key
        
        with open(config_file, "w") as f:
            json.dump(config, f, indent=2)
        
        logger.info("SECRET_KEY已保存到配置文件")
    except Exception as e:
        logger.error(f"保存SECRET_KEY到配置文件失败: {str(e)}")
    
    return secret_key

# 如果直接运行此文件，则打印SECRET_KEY
if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    secret_key = load_secret_key()
    print(f"SECRET_KEY: {secret_key}") 