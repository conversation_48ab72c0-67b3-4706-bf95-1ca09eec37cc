#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
AI 智能体客服插件数据库迁移脚本
"""

import logging
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession

from db.database import get_async_engine
from .models.customer_service import (
    CustomerServiceSession,
    CustomerServiceMessage,
    CustomerServiceTicket,
    CustomerServiceAnalytics,
    CustomerServiceConfig,
    PlatformAccount,
    MarketingAction,
    ConversationLog,
    UserProfile,
    KnowledgeCache
)

logger = logging.getLogger(__name__)

async def create_tables():
    """创建插件所需的数据库表"""
    try:
        logger.info("正在创建 AI 智能体客服插件数据库表...")
        
        # 获取数据库引擎
        engine = get_async_engine()
        
        # 创建所有表
        async with engine.begin() as conn:
            # 导入Base并创建表
            from db.database import Base
            await conn.run_sync(Base.metadata.create_all)
            
            # 创建索引（如果需要额外的索引）
            await _create_additional_indexes(conn)
            
        logger.info("AI 智能体客服插件数据库表创建完成")
        
    except Exception as e:
        logger.error(f"创建数据库表失败: {e}")
        raise

async def _create_additional_indexes(conn):
    """创建额外的索引"""
    try:
        # 为客服会话表创建复合索引
        await conn.execute(text("""
            CREATE INDEX IF NOT EXISTS idx_cs_session_platform_user 
            ON ai_cs_sessions (platform_type, platform_user_id, status);
        """))
        
        # 为客服消息表创建时间索引
        await conn.execute(text("""
            CREATE INDEX IF NOT EXISTS idx_cs_message_created_at 
            ON ai_cs_messages (created_at DESC);
        """))
        
        # 为工单表创建状态和优先级索引
        await conn.execute(text("""
            CREATE INDEX IF NOT EXISTS idx_cs_ticket_status_priority 
            ON ai_cs_tickets (status, priority, created_at DESC);
        """))
        
        # 为营销动作表创建状态索引
        await conn.execute(text("""
            CREATE INDEX IF NOT EXISTS idx_cs_marketing_status 
            ON ai_cs_marketing_actions (status, created_at DESC);
        """))
        
        # 为用户画像表创建平台用户索引
        await conn.execute(text("""
            CREATE INDEX IF NOT EXISTS idx_cs_user_profile_platform 
            ON ai_cs_user_profiles (platform_type, platform_user_id);
        """))
        
        logger.info("额外索引创建完成")
        
    except Exception as e:
        logger.error(f"创建额外索引失败: {e}")
        # 不抛出异常，因为索引创建失败不应该阻止插件初始化

async def drop_tables():
    """删除插件的数据库表"""
    try:
        logger.info("正在删除 AI 智能体客服插件数据库表...")
        
        # 获取数据库引擎
        engine = get_async_engine()
        
        # 删除表（按依赖关系逆序）
        async with engine.begin() as conn:
            # 删除表的SQL语句
            tables_to_drop = [
                "ai_cs_knowledge_cache",
                "ai_cs_user_profiles", 
                "ai_cs_conversation_logs",
                "ai_cs_marketing_actions",
                "ai_cs_platform_accounts",
                "ai_cs_configs",
                "ai_cs_analytics",
                "ai_cs_messages",
                "ai_cs_tickets",
                "ai_cs_sessions"
            ]
            
            for table_name in tables_to_drop:
                try:
                    await conn.execute(text(f"DROP TABLE IF EXISTS {table_name} CASCADE;"))
                    logger.info(f"删除表 {table_name}")
                except Exception as e:
                    logger.warning(f"删除表 {table_name} 失败: {e}")
        
        logger.info("AI 智能体客服插件数据库表删除完成")
        
    except Exception as e:
        logger.error(f"删除数据库表失败: {e}")
        raise

async def migrate_data():
    """数据迁移（如果需要）"""
    try:
        logger.info("正在执行数据迁移...")
        
        # 这里可以添加数据迁移逻辑
        # 例如：从旧版本迁移数据、初始化默认配置等
        
        await _initialize_default_configs()
        
        logger.info("数据迁移完成")
        
    except Exception as e:
        logger.error(f"数据迁移失败: {e}")
        raise

async def _initialize_default_configs():
    """初始化默认配置"""
    try:
        from db.database import get_db
        
        # 这里需要实际的数据库会话
        # 暂时跳过，实际使用时需要实现
        
        logger.info("默认配置初始化完成")
        
    except Exception as e:
        logger.error(f"初始化默认配置失败: {e}")

async def check_table_exists(table_name: str) -> bool:
    """检查表是否存在"""
    try:
        engine = get_async_engine()
        
        async with engine.begin() as conn:
            result = await conn.execute(text("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_name = :table_name
                );
            """), {"table_name": table_name})
            
            return result.scalar()
            
    except Exception as e:
        logger.error(f"检查表存在性失败: {e}")
        return False

async def get_plugin_version() -> str:
    """获取插件数据库版本"""
    try:
        engine = get_async_engine()
        
        async with engine.begin() as conn:
            # 检查是否有版本配置表
            version_exists = await conn.execute(text("""
                SELECT EXISTS (
                    SELECT FROM information_schema.tables 
                    WHERE table_name = 'ai_cs_configs'
                );
            """))
            
            if not version_exists.scalar():
                return "0.0.0"
            
            # 查询版本信息
            result = await conn.execute(text("""
                SELECT config_value 
                FROM ai_cs_configs 
                WHERE config_key = 'plugin_version'
                LIMIT 1;
            """))
            
            version_row = result.fetchone()
            if version_row:
                return version_row[0].get("version", "0.0.0")
            else:
                return "0.0.0"
                
    except Exception as e:
        logger.error(f"获取插件版本失败: {e}")
        return "0.0.0"

async def update_plugin_version(version: str):
    """更新插件数据库版本"""
    try:
        engine = get_async_engine()
        
        async with engine.begin() as conn:
            # 更新或插入版本信息
            await conn.execute(text("""
                INSERT INTO ai_cs_configs (
                    id, tenant_id, project_id, config_key, config_value, 
                    description, created_at, updated_at
                ) VALUES (
                    gen_random_uuid(), gen_random_uuid(), gen_random_uuid(),
                    'plugin_version', :config_value, 'AI智能体客服插件版本',
                    NOW(), NOW()
                ) ON CONFLICT (tenant_id, project_id, config_key) 
                DO UPDATE SET 
                    config_value = :config_value,
                    updated_at = NOW();
            """), {
                "config_value": {"version": version, "updated_at": "2025-01-01T00:00:00Z"}
            })
            
        logger.info(f"插件版本更新为: {version}")
        
    except Exception as e:
        logger.error(f"更新插件版本失败: {e}")

async def cleanup_old_data(days: int = 90):
    """清理旧数据"""
    try:
        logger.info(f"正在清理 {days} 天前的旧数据...")
        
        engine = get_async_engine()
        
        async with engine.begin() as conn:
            # 清理过期的知识库缓存
            result = await conn.execute(text("""
                DELETE FROM ai_cs_knowledge_cache 
                WHERE expires_at < NOW() - INTERVAL ':days days';
            """), {"days": days})
            
            cache_deleted = result.rowcount
            logger.info(f"清理了 {cache_deleted} 条过期缓存")
            
            # 清理旧的对话日志
            result = await conn.execute(text("""
                DELETE FROM ai_cs_conversation_logs 
                WHERE created_at < NOW() - INTERVAL ':days days';
            """), {"days": days})
            
            logs_deleted = result.rowcount
            logger.info(f"清理了 {logs_deleted} 条旧对话日志")
            
            # 清理已完成的营销动作
            result = await conn.execute(text("""
                DELETE FROM ai_cs_marketing_actions 
                WHERE status IN ('converted', 'failed') 
                AND created_at < NOW() - INTERVAL ':days days';
            """), {"days": days})
            
            actions_deleted = result.rowcount
            logger.info(f"清理了 {actions_deleted} 条旧营销动作")
        
        logger.info("旧数据清理完成")
        
    except Exception as e:
        logger.error(f"清理旧数据失败: {e}")

# 迁移脚本映射
MIGRATION_SCRIPTS = {
    "1.0.0": create_tables,
    "1.0.1": migrate_data,
    # 可以添加更多版本的迁移脚本
}

async def run_migrations(target_version: str = "1.0.0"):
    """运行数据库迁移"""
    try:
        current_version = await get_plugin_version()
        logger.info(f"当前版本: {current_version}, 目标版本: {target_version}")
        
        # 如果版本相同，跳过迁移
        if current_version == target_version:
            logger.info("数据库版本已是最新，跳过迁移")
            return
        
        # 执行迁移脚本
        for version, migration_func in MIGRATION_SCRIPTS.items():
            if version <= target_version and version > current_version:
                logger.info(f"执行迁移脚本: {version}")
                await migration_func()
        
        # 更新版本
        await update_plugin_version(target_version)
        
        logger.info("数据库迁移完成")
        
    except Exception as e:
        logger.error(f"数据库迁移失败: {e}")
        raise
