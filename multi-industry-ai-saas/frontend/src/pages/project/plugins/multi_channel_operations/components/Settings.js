import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Input,
  Select,
  Switch,
  Button,
  Space,
  message,
  Typography,
  Row,
  Col,
  Divider,
  Al<PERSON>,
  Slider,
  InputNumber,
  Checkbox
} from 'antd';
import {
  SaveOutlined,
  ReloadOutlined,
  SettingOutlined
} from '@ant-design/icons';
import { useParams } from 'react-router-dom';
import multiChannelService from '../services/multiChannelService';

const { Title, Text } = Typography;
const { Option } = Select;

const Settings = () => {
  const { projectId } = useParams();
  const [loading, setLoading] = useState(false);
  const [settings, setSettings] = useState({});
  const [form] = Form.useForm();

  useEffect(() => {
    loadSettings();
  }, [projectId]);

  const loadSettings = async () => {
    try {
      setLoading(true);
      const response = await multiChannelService.getPluginSettings(projectId);
      const settingsData = response.data || {};
      setSettings(settingsData);
      form.setFieldsValue(settingsData);
    } catch (error) {
      console.error('加载设置失败:', error);
      message.error('加载设置失败');
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async (values) => {
    try {
      setLoading(true);
      await multiChannelService.updatePluginSettings(projectId, values);
      message.success('设置保存成功');
      setSettings(values);
    } catch (error) {
      console.error('保存设置失败:', error);
      message.error('保存设置失败');
    } finally {
      setLoading(false);
    }
  };

  const handleReset = async () => {
    try {
      setLoading(true);
      await multiChannelService.resetPluginSettings(projectId);
      message.success('设置已重置为默认值');
      loadSettings();
    } catch (error) {
      console.error('重置设置失败:', error);
      message.error('重置设置失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>

      <Form
        form={form}
        layout="vertical"
        onFinish={handleSave}
        initialValues={settings}
      >
        {/* 基础设置 */}
        <Card title="基础设置" style={{ marginBottom: 24 }}>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="enable_auto_pricing"
                label="启用自动定价"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
              <Text type="secondary" style={{ fontSize: 12 }}>
                基于AI分析自动调整商品价格
              </Text>
            </Col>
            
            <Col span={12}>
              <Form.Item
                name="enable_competitor_monitoring"
                label="启用竞品监控"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
              <Text type="secondary" style={{ fontSize: 12 }}>
                监控竞争对手的价格和商品信息
              </Text>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="enable_auto_listing"
                label="启用自动上下架"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
              <Text type="secondary" style={{ fontSize: 12 }}>
                根据库存和销售情况自动上下架商品
              </Text>
            </Col>
            
            <Col span={12}>
              <Form.Item
                name="ai_assistant_integration"
                label="AI助手集成"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
              <Text type="secondary" style={{ fontSize: 12 }}>
                启用AI助手进行智能运营建议
              </Text>
            </Col>
          </Row>
        </Card>

        {/* 定价策略设置 */}
        <Card title="定价策略设置" style={{ marginBottom: 24 }}>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="price_adjustment_threshold"
                label="价格调整阈值 (%)"
              >
                <Slider
                  min={1}
                  max={50}
                  marks={{
                    1: '1%',
                    10: '10%',
                    25: '25%',
                    50: '50%'
                  }}
                />
              </Form.Item>
              <Text type="secondary" style={{ fontSize: 12 }}>
                价格调整的最大幅度百分比
              </Text>
            </Col>
            
            <Col span={12}>
              <Form.Item
                name="min_profit_margin"
                label="最低利润率 (%)"
              >
                <InputNumber
                  style={{ width: '100%' }}
                  min={0}
                  max={100}
                  precision={1}
                />
              </Form.Item>
              <Text type="secondary" style={{ fontSize: 12 }}>
                自动定价时保证的最低利润率
              </Text>
            </Col>
          </Row>

          <Form.Item
            name="pricing_strategy"
            label="默认定价策略"
          >
            <Select placeholder="选择默认定价策略">
              <Option value="competitive">竞争定价</Option>
              <Option value="cost_plus">成本加成</Option>
              <Option value="value_based">价值定价</Option>
              <Option value="dynamic">动态定价</Option>
            </Select>
          </Form.Item>
        </Card>

        {/* 监控设置 */}
        <Card title="监控设置" style={{ marginBottom: 24 }}>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="monitoring_frequency"
                label="监控频率"
              >
                <Select placeholder="选择监控频率">
                  <Option value="realtime">实时</Option>
                  <Option value="hourly">每小时</Option>
                  <Option value="daily">每天</Option>
                  <Option value="weekly">每周</Option>
                </Select>
              </Form.Item>
            </Col>
            
            <Col span={12}>
              <Form.Item
                name="data_retention_days"
                label="数据保留天数"
              >
                <InputNumber
                  style={{ width: '100%' }}
                  min={7}
                  max={365}
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="supported_platforms"
            label="支持的平台"
          >
            <Checkbox.Group>
              <Row gutter={[16, 16]}>
                <Col span={8}>
                  <Checkbox value="meituan_takeout">美团外卖</Checkbox>
                </Col>
                <Col span={8}>
                  <Checkbox value="meituan_group_buy">美团团购</Checkbox>
                </Col>
                <Col span={8}>
                  <Checkbox value="meituan_flash">美团闪购</Checkbox>
                </Col>
                <Col span={8}>
                  <Checkbox value="douyin_group_buy">抖音团购</Checkbox>
                </Col>
                <Col span={8}>
                  <Checkbox value="douyin_impulse">抖音随心团</Checkbox>
                </Col>
                <Col span={8}>
                  <Checkbox value="eleme_retail">饿了么零售</Checkbox>
                </Col>
                <Col span={8}>
                  <Checkbox value="eleme_takeout">饿了么外卖</Checkbox>
                </Col>
                <Col span={8}>
                  <Checkbox value="jd_takeout">京东外卖</Checkbox>
                </Col>
                <Col span={8}>
                  <Checkbox value="private_group_buy">私域团购</Checkbox>
                </Col>
              </Row>
            </Checkbox.Group>
          </Form.Item>
        </Card>

        {/* 通知设置 */}
        <Card title="通知设置" style={{ marginBottom: 24 }}>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name={['notification_settings', 'price_change_alert']}
                label="价格变动提醒"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
            
            <Col span={8}>
              <Form.Item
                name={['notification_settings', 'competitor_alert']}
                label="竞品动态提醒"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
            
            <Col span={8}>
              <Form.Item
                name={['notification_settings', 'inventory_alert']}
                label="库存预警"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name={['notification_settings', 'automation_alert']}
                label="自动化执行通知"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
            
            <Col span={8}>
              <Form.Item
                name={['notification_settings', 'error_alert']}
                label="错误提醒"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
            
            <Col span={8}>
              <Form.Item
                name={['notification_settings', 'daily_report']}
                label="每日报告"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
          </Row>
        </Card>

        {/* 高级设置 */}
        <Card title="高级设置" style={{ marginBottom: 24 }}>
          <Alert
            message="高级设置"
            description="这些设置会影响插件的核心功能，请谨慎修改"
            type="warning"
            showIcon
            style={{ marginBottom: 16 }}
          />

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="api_timeout"
                label="API超时时间 (秒)"
              >
                <InputNumber
                  style={{ width: '100%' }}
                  min={5}
                  max={300}
                />
              </Form.Item>
            </Col>
            
            <Col span={12}>
              <Form.Item
                name="max_concurrent_requests"
                label="最大并发请求数"
              >
                <InputNumber
                  style={{ width: '100%' }}
                  min={1}
                  max={50}
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="cache_duration"
                label="缓存时长 (分钟)"
              >
                <InputNumber
                  style={{ width: '100%' }}
                  min={1}
                  max={1440}
                />
              </Form.Item>
            </Col>
            
            <Col span={12}>
              <Form.Item
                name="log_level"
                label="日志级别"
              >
                <Select placeholder="选择日志级别">
                  <Option value="debug">调试</Option>
                  <Option value="info">信息</Option>
                  <Option value="warning">警告</Option>
                  <Option value="error">错误</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
        </Card>

        {/* 操作按钮 */}
        <Card>
          <div style={{ textAlign: 'right' }}>
            <Space>
              <Button
                icon={<ReloadOutlined />}
                onClick={handleReset}
                loading={loading}
              >
                重置为默认
              </Button>
              <Button
                type="primary"
                icon={<SaveOutlined />}
                htmlType="submit"
                loading={loading}
              >
                保存设置
              </Button>
            </Space>
          </div>
        </Card>
      </Form>
    </div>
  );
};

export default Settings;
