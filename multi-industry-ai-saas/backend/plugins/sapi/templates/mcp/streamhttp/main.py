#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
MCP сервер - StreamHTTP Протокол (V2 - использует обновленный mcp.base)
"""

import os
import json
import logging
import asyncio
import sys
import uuid
from typing import Dict, Any, Optional, Union, List

# Импорт из mcp.base
from mcp.base import (
    MCPServer,
    SERVER_NAME,
    SERVER_VERSION,
    ErrorCode, # Import ErrorCode
    get_mcp_server_id,
    start_heartbeat as start_mcp_base_heartbeat_func,
    MCPSession
)
# 从load_tools.py导入load_tools函数
from load_tools import load_tools
# Утилиты для регистрации в Redis
from utils import register_server, heartbeat as redis_registration_heartbeat, parsed_env_sdk

# Настройка логирования
logger = logging.getLogger(__name__)

# FastAPI и Pydantic
from fastapi import FastAPI, Request as FastAPIRequest, Response as FastAPIResponse, HTTPException
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
from pydantic import BaseModel, Field

# SSE支持
try:
    from sse_starlette.sse import EventSourceResponse
    SSE_AVAILABLE = True
except ImportError:
    logger.warning("[StreamHTTP] sse_starlette not installed. GET /mcp for SSE streams will not be available.")
    SSE_AVAILABLE = False
    EventSourceResponse = None # type: ignore

# --- Глобальный экземпляр MCPServer ---
SERVER_ID = get_mcp_server_id()
mcp_server = MCPServer(server_id=SERVER_ID)

# Создание FastAPI приложения
app = FastAPI(title=f"MCP Server (StreamHTTP) - {SERVER_ID}", version=SERVER_VERSION)

# CORS Middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"], allow_credentials=True, allow_methods=["*"], allow_headers=["*"],
)

# Модель для POST-запросов
class MCPRequestModelStreamHTTP(BaseModel):
    id: Optional[Union[str, int]] = None
    method: str = Field(...)
    params: Dict[str, Any] = Field(default_factory=dict)

# Событие Startup
@app.on_event("startup")
async def startup_event_streamhttp():
    logger.info(f"--- [StreamHTTP {SERVER_ID}] Событие Startup ---")
    host_for_registration = parsed_env_sdk.get("MCP_SERVER_HOST") or parsed_env_sdk.get("MCP_SERVICE_NAME") or f"mcp-server-{SERVER_ID}"
    port_str = os.environ.get("PORT")
    if not port_str:
        logger.critical(f"[StreamHTTP {SERVER_ID}] Переменная окружения PORT не установлена.")
        return
    try:
        current_port = int(port_str)
    except ValueError:
        logger.critical(f"[StreamHTTP {SERVER_ID}] Неверное значение PORT: '{port_str}'.")
        return

    logger.info(f"[StreamHTTP {SERVER_ID}] Регистрация в Redis: хост={host_for_registration}, порт={current_port}...")
    try:
        # 添加元数据以支持Traefik路由
        traefik_mcp_domain_suffix = os.environ.get("TRAEFIK_MCP_DOMAIN_SUFFIX", "localhost")
        mcp_instance_hostname = f"mcp-{SERVER_ID}.{traefik_mcp_domain_suffix}"

        registration_data = {
            "server_id": SERVER_ID,
            "host": host_for_registration,
            "port": current_port,
            "port_http": current_port,
            "port_streamhttp": current_port,
            "metadata": {
                "traefik_hostname": mcp_instance_hostname,
                "protocols": ["streamhttp"],
                "version": SERVER_VERSION
            }
        }

        if register_server(**registration_data):
            logger.info(f"[StreamHTTP {SERVER_ID}] Сервер успешно зарегистрирован/обновлен в Redis.")
            asyncio.create_task(redis_registration_heartbeat(**registration_data))
            logger.info(f"[StreamHTTP {SERVER_ID}] Запущена задача Redis heartbeat.")
        else:
            logger.error(f"[StreamHTTP {SERVER_ID}] Ошибка регистрации в Redis.")
    except Exception as e:
        logger.error(f"[StreamHTTP {SERVER_ID}] Исключение при регистрации или запуске heartbeat: {e}", exc_info=True)

    # 先初始化服务器（包括预加载工具）
    await mcp_server.initialize_server()

    # logger.info(f"[StreamHTTP {SERVER_ID}] Загрузка инструментов...")
    # # 获取系统令牌
    # system_token = os.environ.get("SYSTEM_TOKEN")
    # if system_token:
    #     logger.info(f"[StreamHTTP {SERVER_ID}] 使用系统令牌获取工具列表")
    # else:
    #     logger.warning(f"[StreamHTTP {SERVER_ID}] 未找到系统令牌，工具获取可能失败")

    # # 不使用MCPSession，直接传递None并让load_tools内部处理系统令牌
    # tools = await load_tools(None)  # load_tools内部会处理None并查找环境变量中的系统令牌

    # # 注册工具到mcp_server
    # for tool in tools:
    #     tool_name = tool.get("name")
    #     if not tool_name:
    #         logger.warning(f"工具缺少name字段: {tool}")
    #         continue

    #     # 创建工具函数
    #     async def tool_func(session, params, tool_metadata=tool, stream_mode=False):
    #         logger.info(f"执行工具: {tool_metadata.get('name')}")
    #         logger.info(f"工具参数: {params}")
    #         logger.info(f"流模式: {stream_mode}")
    #         session.last_activity_time = asyncio.get_event_loop().time()
    #         return {
    #             "result": f"工具 {tool_metadata.get('name')} 执行成功",
    #             "metadata": tool_metadata,
    #             "stream_mode": stream_mode
    #         }
    #     mcp_server.register_tool(tool_name, tool_func)
    #     logger.info(f"工具 {tool_name} 注册成功")

    # logger.info(f"[StreamHTTP {SERVER_ID}] 成功注册 {len(tools)} 个工具")
    logger.info(f"[StreamHTTP {SERVER_ID}] MCPServer tool initialization complete. Tools loaded: {len(mcp_server.get_tool_schemas()) if hasattr(mcp_server, 'get_tool_schemas') else 'N/A (check MCPServer)'}.")

    logger.info(f"[StreamHTTP {SERVER_ID}] Запуск фоновой задачи очистки сессий mcp.base...")
    asyncio.create_task(start_mcp_base_heartbeat_func(mcp_server))
    logger.info(f"[StreamHTTP {SERVER_ID}] Сервер '{SERVER_NAME}' полностью запущен и инициализирован на порту {current_port}.")

# POST /mcp - основной эндпоинт для обработки команд
@app.post("/mcp")
@app.post("/message")  # 添加标准MCP路径
async def mcp_streamhttp_handler(request_data: MCPRequestModelStreamHTTP, http_request: FastAPIRequest):
    logger.debug(f"[StreamHTTP {SERVER_ID}] POST /mcp: id={request_data.id}, method={request_data.method}")
    headers = dict(http_request.headers)

    # 记录所有传入的头部信息，用于诊断
    logger.info(f"[StreamHTTP POST /mcp INCOMING_HEADERS for method {request_data.method}, id {request_data.id}]: {json.dumps(headers)}")

    # 检查是否为Traefik直接路由（带有鉴权头部）
    auth_type = headers.get("x-mcp-auth-type")
    if auth_type in ["direct_access", "sapi_converted"]:
        logger.info(f"[StreamHTTP POST /mcp] 检测到Traefik直接路由，鉴权类型: {auth_type}")
        # 从Traefik鉴权头部获取会话信息
        user_id = headers.get("x-mcp-user-id")
        project_id = headers.get("x-mcp-project-id")
        system_token = headers.get("x-mcp-system-token")
        permissions_str = headers.get("x-mcp-permissions", "[]")
        
        try:
            permissions = json.loads(permissions_str) if permissions_str else []
        except json.JSONDecodeError:
            permissions = []
        
        # 获取会话ID - 优先使用客户端提供的会话ID
        MCP_SESSION_ID_HEADER = "mcp-session-id"
        session_id_from_header = headers.get(MCP_SESSION_ID_HEADER.lower()) or headers.get(MCP_SESSION_ID_HEADER)
        
        # 如果是初始化请求或没有提供会话ID，则根据鉴权类型处理
        if request_data.method == "initialize" or not session_id_from_header:
            if auth_type == "direct_access":
                session_id_from_header = headers.get("x-mcp-session-id") or str(uuid.uuid4())
            else:
                # SAPI转换的情况，生成新的会话ID
                session_id_from_header = str(uuid.uuid4())
                logger.info(f"[StreamHTTP POST /mcp] 为SAPI转换创建新会话: {session_id_from_header}")
        else:
            logger.info(f"[StreamHTTP POST /mcp] 使用客户端提供的会话ID: {session_id_from_header}")
    else:
        # 标准会话ID头部处理
    MCP_SESSION_ID_HEADER = "mcp-session-id"
    session_id_from_header = headers.get(MCP_SESSION_ID_HEADER.lower()) or headers.get(MCP_SESSION_ID_HEADER)

    response_json: Dict[str, Any]

    if request_data.method == "initialize":
        # 检查是否为Traefik直接路由的初始化
        if auth_type in ["direct_access", "sapi_converted"]:
            # 直接使用Traefik提供的鉴权信息创建会话
            logger.info(f"[StreamHTTP POST /mcp] Traefik直接路由初始化，鉴权类型: {auth_type}")
            
            # 创建会话信息
            if auth_type == "direct_access" and session_id_from_header:
                # 直接访问模式，使用现有会话ID
                new_session_id = session_id_from_header
            else:
                # SAPI转换模式，使用生成的会话ID
                new_session_id = session_id_from_header
            
            # 构造初始化响应
            response_json = {
                "jsonrpc": "2.0",
                "id": request_data.id,
                "result": {
                    "sessionId": new_session_id,
                    "protocolVersion": "2024-11-05",
                    "serverInfo": {
                        "name": SERVER_NAME,
                        "version": SERVER_VERSION,
                        "serverId": SERVER_ID
                    },
                    "capabilities": mcp_server.get_server_capabilities(new_session_id),
                    "authType": auth_type,
                    "directAccess": True
                }
            }
            
            # 创建并存储会话
            session = MCPSession(
                session_id=new_session_id,
                user_id=user_id,
                project_id=project_id,
                system_token=system_token,
                permissions=permissions,
                server_id=SERVER_ID,
                client_info=request_data.params.get("clientInfo"),
                is_initialized=True,
                internal_message_queue=asyncio.Queue()
            )
            mcp_server.session_store[new_session_id] = session
            logger.info(f"[StreamHTTP POST /mcp] Traefik直接路由会话已创建: {new_session_id}")
        else:
            # 标准初始化流程
            logger.info(f"[StreamHTTP {SERVER_ID}] 处理标准初始化请求: {request_data.id}")
        response_json = await mcp_server.handle_initialize(
            request_id=request_data.id,
            params=request_data.params,
            headers=headers
        )
        # 如果初始化成功，发送initialized通知
        if "result" in response_json and "sessionId" in response_json["result"]:
            session_id = response_json["result"]["sessionId"]
            logger.info(f"[StreamHTTP {SERVER_ID}] 初始化成功，会话ID: {session_id}")
            try:
                await mcp_server.handle_request(
                    method="notifications/initialized",
                    params={},
                    request_id=None,
                    headers=headers,
                    session_id_from_transport=session_id
                )
                logger.info(f"[StreamHTTP {SERVER_ID}] 已发送initialized通知给会话 {session_id}")
            except Exception as e_noti:
                logger.warning(f"[StreamHTTP {SERVER_ID}] 发送initialized通知时出错: {e_noti}")
    elif session_id_from_header:
        # 对于Traefik直接路由的非初始化请求，如果会话不存在则创建
        if auth_type in ["direct_access", "sapi_converted"]:
            existing_session = mcp_server.get_session(session_id_from_header)
            if not existing_session:
                logger.info(f"[StreamHTTP POST /mcp] Traefik直接路由会话不存在，创建新会话: {session_id_from_header}")
                # 创建并存储会话
                session = MCPSession(
                    session_id=session_id_from_header,
                    user_id=user_id,
                    project_id=project_id,
                    system_token=system_token,
                    permissions=permissions,
                    server_id=SERVER_ID,
                    client_info=None,
                    is_initialized=True,
                    internal_message_queue=asyncio.Queue()
                )
                mcp_server.session_store[session_id_from_header] = session
                logger.info(f"[StreamHTTP POST /mcp] Traefik直接路由会话已创建: {session_id_from_header}")
        
        logger.info(f"[StreamHTTP {SERVER_ID}] 处理方法 {request_data.method} 的请求，会话ID: {session_id_from_header}")

        # 记录详细的请求参数
        if request_data.method == "tools/call":
            tool_name = request_data.params.get("name", "未知工具")
            tool_args = request_data.params.get("arguments", {})
            logger.info(f"[StreamHTTP {SERVER_ID}] 工具调用请求: 工具名称={tool_name}, 参数={json.dumps(tool_args)}")

        # 调用处理请求
        try:
            response_json = await mcp_server.handle_request(
                method=request_data.method,
                params=request_data.params,
                request_id=request_data.id,
                headers=headers,
                session_id_from_transport=session_id_from_header
            )

            # 详细记录API响应
            if request_data.method == "tools/call":
                if "error" in response_json:
                    logger.error(f"[StreamHTTP {SERVER_ID}] 工具调用失败: {json.dumps(response_json['error'])}")
                else:
                    logger.info(f"[StreamHTTP {SERVER_ID}] 工具调用成功: 结果类型={type(response_json.get('result'))}")
                    logger.debug(f"[StreamHTTP {SERVER_ID}] 工具调用详细结果: {json.dumps(response_json)}")
            else:
                logger.debug(f"[StreamHTTP {SERVER_ID}] 方法 {request_data.method} 的响应: {json.dumps(response_json)}")
        except Exception as e:
            logger.error(f"[StreamHTTP {SERVER_ID}] 处理请求时出错: {e}", exc_info=True)
            response_json = {
                "jsonrpc": "2.0",
                "id": request_data.id,
                "error": {
                    "code": ErrorCode.GENERIC_ERROR.value,
                    "message": f"处理请求时出错: {str(e)}"
                }
            }
    else:
        # 非初始化请求缺少会话ID
        logger.warning(f"[StreamHTTP {SERVER_ID}] 非初始化请求缺少 {MCP_SESSION_ID_HEADER} 头部")
        response_json = {
            "jsonrpc": "2.0",
            "id": request_data.id,
            "error": {
                "code": ErrorCode.SESSION_NOT_FOUND.value,
                "message": f"非初始化请求缺少 {MCP_SESSION_ID_HEADER} 头部"
            }
        }

    # 将MCP错误代码映射到HTTP状态码
    http_status_code = 200
    if "error" in response_json and response_json["error"]:
        mcp_error_code = response_json["error"].get("code")
        if mcp_error_code == ErrorCode.AUTHENTICATION_FAILED.value:
            http_status_code = 401
        elif mcp_error_code == ErrorCode.AUTHORIZATION_FAILED.value:
            http_status_code = 403
        elif mcp_error_code == ErrorCode.INVALID_REQUEST.value:
            http_status_code = 400
        elif mcp_error_code == ErrorCode.SESSION_NOT_FOUND.value:
            http_status_code = 404
        elif mcp_error_code == ErrorCode.METHOD_NOT_FOUND.value:
            http_status_code = 405
        elif mcp_error_code in [ErrorCode.INITIALIZATION_FAILED.value,
                                ErrorCode.TOOL_NOT_FOUND.value,
                                ErrorCode.TOOL_EXECUTION_ERROR.value,
                                ErrorCode.GENERIC_ERROR.value,
                                ErrorCode.SESSION_NOT_INITIALIZED.value]:
            http_status_code = 500

    # 如果是成功的初始化调用，将会话ID添加到响应头部
    final_headers = {}
    if request_data.method == "initialize" and "result" in response_json and "sessionId" in response_json["result"]:
        final_headers[MCP_SESSION_ID_HEADER] = response_json["result"]["sessionId"]

    logger.debug(f"[StreamHTTP {SERVER_ID}] 最终响应状态码: {http_status_code}, 头部: {final_headers}")
    return FastAPIResponse(content=json.dumps(response_json),
                           media_type="application/json",
                           status_code=http_status_code,
                           headers=final_headers)

# 添加GET /mcp端点以支持流式工具调用
@app.get("/mcp")
@app.get("/message")  # 添加标准MCP路径
async def mcp_get_endpoint_sse(http_request: FastAPIRequest):
    if not SSE_AVAILABLE:
        logger.warning(f"[StreamHTTP {SERVER_ID}] GET /mcp 被调用但SSE不可用 (sse_starlette缺失).")
        return FastAPIResponse("此服务器实例不支持SSE流式传输。", status_code=501, media_type="text/plain")

    headers = dict(http_request.headers)
    logger.info(f"[StreamHTTP GET /mcp INCOMING_HEADERS]: {json.dumps(headers)}")

    # 标准化头部键访问（不区分大小写）
    MCP_SESSION_ID_HEADER = "mcp-session-id"
    session_id = headers.get(MCP_SESSION_ID_HEADER.lower()) or headers.get(MCP_SESSION_ID_HEADER)

    if not session_id:
        logger.warning(f"[StreamHTTP {SERVER_ID} GET /mcp] 缺少 {MCP_SESSION_ID_HEADER} 头部.")
        return FastAPIResponse(f"缺少 {MCP_SESSION_ID_HEADER} 头部", status_code=400, media_type="text/plain")

    mcp_session = mcp_server.get_session(session_id)
    if not mcp_session:
        logger.warning(f"[StreamHTTP {SERVER_ID} GET /mcp] 未找到会话ID: {session_id}")
        return FastAPIResponse("未找到会话", status_code=404, media_type="text/plain")

    if not mcp_session.is_initialized:
        logger.warning(f"[StreamHTTP {SERVER_ID} GET /mcp] 会话 {session_id} 未完全初始化，无法进行SSE流式传输.")
        return FastAPIResponse("会话未初始化，无法进行SSE流式传输。", status_code=409, media_type="text/plain")

    logger.info(f"[StreamHTTP {SERVER_ID} GET /mcp] 客户端已连接SSE流式传输. 会话ID: {session_id}")

    async def event_generator():
        try:
            # 确保会话有消息队列
            if not mcp_session.internal_message_queue:
                mcp_session.internal_message_queue = asyncio.Queue()
                logger.info(f"[StreamHTTP {SERVER_ID} SSE Gen {session_id}] 为会话创建了新的消息队列")

            while True:
                try:
                    # 等待来自会话内部队列的消息
                    message = await asyncio.wait_for(
                        mcp_session.internal_message_queue.get(),
                        timeout=mcp_session.heartbeat_interval * 0.8
                    )

                    # 检查是否为关闭队列的哨兵
                    if message is MCPSession._SENTINEL_CLOSE_QUEUE_ or message is None:
                        logger.info(f"[StreamHTTP {SERVER_ID} SSE Gen {session_id}] 收到哨兵或队列为空，关闭流.")
                        break

                    if isinstance(message, dict):
                        event_type = message.get("method", "message")
                        if event_type.startswith("notifications/"):
                            event_type = event_type.split("/", 1)[1]

                        logger.debug(f"[StreamHTTP {SERVER_ID} SSE Gen {session_id}] 发送事件: {event_type}")
                        yield {
                            "event": event_type,
                            "data": json.dumps(message)
                        }
                        mcp_session.internal_message_queue.task_done()
                    else:
                        logger.warning(f"[StreamHTTP {SERVER_ID} SSE Gen {session_id}] 从队列收到非字典消息: {type(message)}. 跳过.")
                        mcp_session.internal_message_queue.task_done()

                except asyncio.TimeoutError:
                    # 发送ping事件以保持连接活跃
                    logger.debug(f"[StreamHTTP {SERVER_ID} SSE Gen {session_id}] 发送ping事件")
                    yield {
                        "event": "ping",
                        "data": json.dumps({"timestamp": asyncio.get_event_loop().time()})
                    }
                except Exception as e:
                    logger.error(f"[StreamHTTP {SERVER_ID} SSE Gen {session_id}] 事件生成器中出错: {e}", exc_info=True)
                    break

            logger.info(f"[StreamHTTP {SERVER_ID} SSE Gen {session_id}] 事件生成器已完成.")
        except Exception as e:
            logger.error(f"[StreamHTTP {SERVER_ID} SSE Gen {session_id}] 事件生成器外层异常: {e}", exc_info=True)

    return EventSourceResponse(
        event_generator(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "X-Accel-Buffering": "no"
        }
    )

# Эндпоинт для проверки состояния
@app.get("/health")
async def health_check_streamhttp():
    redis_ok = False
    try:
        from utils import get_redis_client
        redis = await get_redis_client()
        await redis.ping()
        redis_ok = True
    except Exception as e:
        logger.warning(f"[StreamHTTP {SERVER_ID} Health] Ошибка проверки Redis: {e}")

    return {
        "status": "ok", "protocol": "streamhttp", "server_id": SERVER_ID,
        "server_name": SERVER_NAME, "version": SERVER_VERSION,
        "redis_connected": redis_ok
    }

# Главная функция для запуска
if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    logger.info(f"--- [StreamHTTP MCP Server V2 ({SERVER_ID})] Запуск через __main__ ---")
    port_str = os.environ.get("PORT")
    if not port_str:
        logger.critical("Переменная окружения PORT не установлена. Невозможно запустить сервер.")
        sys.exit(1)
    try:
        port = int(port_str)
    except ValueError:
        logger.critical(f"Неверное значение PORT: '{port_str}'. Должно быть числом.")
        sys.exit(1)

    logger.info(f"Запуск Uvicorn для StreamHTTP MCP сервера ({SERVER_ID}) на хосте 0.0.0.0, порт {port}")
    uvicorn.run(app, host="0.0.0.0", port=port, log_config=None)
