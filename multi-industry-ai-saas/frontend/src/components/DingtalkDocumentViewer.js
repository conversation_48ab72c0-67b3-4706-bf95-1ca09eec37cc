import React, { useState, useEffect } from 'react';
import { 
  Mo<PERSON>, 
  Card, 
  Spin, 
  Space, 
  Tag, 
  Button, 
  message, 
  Typography,
  Divider,
  Empty,
  Tooltip,
  Row,
  Col,
  Breadcrumb
} from 'antd';
import { 
  FileTextOutlined, 
  FolderOutlined, 
  SyncOutlined, 
  EyeOutlined,
  DownloadOutlined,
  ShareAltOutlined,
  LinkOutlined
} from '@ant-design/icons';
import ReactMarkdown from 'react-markdown';
import { Prism as SyntaxHighlighter } from 'react-syntax-highlighter';
import { tomorrow } from 'react-syntax-highlighter/dist/esm/styles/prism';

const { Title, Text, Paragraph } = Typography;

/**
 * 钉钉文档查看器组件
 * 提供文档查看、同步、下载等功能，保持插件独立性
 */
const DingtalkDocumentViewer = ({
  visible,
  onClose,
  documentId,
  spaceId,
  mode = 'viewer', // viewer, selector, embedded
  height = 600,
  showActions = true,
  onDocumentSelect = null, // 选择文档回调
  onSync = null, // 同步回调
  title = '钉钉文档查看器'
}) => {
  const [loading, setLoading] = useState(false);
  const [document, setDocument] = useState(null);
  const [spaces, setSpaces] = useState([]);
  const [documents, setDocuments] = useState([]);
  const [selectedSpace, setSelectedSpace] = useState(spaceId);
  const [selectedDocument, setSelectedDocument] = useState(documentId);
  const [currentView, setCurrentView] = useState('spaces'); // spaces, documents, content

  useEffect(() => {
    if (visible) {
      loadInitialData();
    }
  }, [visible, spaceId, documentId]);

  const loadInitialData = async () => {
    setLoading(true);
    try {
      await loadSpaces();
      
      if (spaceId) {
        setSelectedSpace(spaceId);
        await loadDocuments(spaceId);
        setCurrentView('documents');
        
        if (documentId) {
          await loadDocumentContent(documentId);
          setCurrentView('content');
        }
      }
    } catch (error) {
      console.error('加载初始数据失败:', error);
      message.error('加载数据失败');
    } finally {
      setLoading(false);
    }
  };

  const loadSpaces = async () => {
    try {
      // 模拟API调用 - 实际项目中需要实现钉钉文档API
      const mockSpaces = [
        {
          id: '1',
          dingtalk_space_id: 'space_1',
          space_name: '产品文档',
          space_description: '产品相关的文档和说明',
          doc_count: 15,
          sync_status: 'synced',
          auto_sync: true,
          last_sync_at: new Date().toISOString()
        },
        {
          id: '2',
          dingtalk_space_id: 'space_2',
          space_name: '技术文档',
          space_description: '开发和技术相关文档',
          doc_count: 28,
          sync_status: 'synced',
          auto_sync: true,
          last_sync_at: new Date().toISOString()
        },
        {
          id: '3',
          dingtalk_space_id: 'space_3',
          space_name: '运营手册',
          space_description: '运营流程和规范文档',
          doc_count: 12,
          sync_status: 'pending',
          auto_sync: false,
          last_sync_at: null
        }
      ];
      
      setSpaces(mockSpaces);
    } catch (error) {
      console.error('获取知识库列表失败:', error);
      throw error;
    }
  };

  const loadDocuments = async (spaceId) => {
    try {
      // 模拟API调用
      const mockDocuments = [
        {
          id: '1',
          dingtalk_doc_id: 'doc_1',
          title: '产品需求文档',
          content: '# 产品需求文档\n\n## 概述\n这是一个产品需求文档示例...\n\n## 功能列表\n- 功能1\n- 功能2\n- 功能3',
          content_format: 'markdown',
          document_type: 'requirement',
          sync_status: 'synced',
          sync_direction: 'from_dingtalk',
          last_sync_at: new Date().toISOString(),
          tags: ['产品', '需求'],
          created_at: new Date().toISOString()
        },
        {
          id: '2',
          dingtalk_doc_id: 'doc_2',
          title: 'API接口文档',
          content: '# API接口文档\n\n## 接口列表\n\n### 用户相关\n```javascript\n// 获取用户信息\nGET /api/user/:id\n```',
          content_format: 'markdown',
          document_type: 'api',
          sync_status: 'synced',
          sync_direction: 'bidirectional',
          last_sync_at: new Date().toISOString(),
          tags: ['API', '技术'],
          created_at: new Date().toISOString()
        }
      ];
      
      setDocuments(mockDocuments);
    } catch (error) {
      console.error('获取文档列表失败:', error);
      throw error;
    }
  };

  const loadDocumentContent = async (docId) => {
    try {
      // 从已加载的文档中查找
      const foundDoc = documents.find(doc => doc.id === docId || doc.dingtalk_doc_id === docId);
      if (foundDoc) {
        setDocument(foundDoc);
        setSelectedDocument(docId);
      } else {
        // 如果没找到，模拟API调用获取文档内容
        const mockDoc = {
          id: docId,
          dingtalk_doc_id: docId,
          title: '文档标题',
          content: '# 文档内容\n\n这是文档的具体内容...',
          content_format: 'markdown',
          sync_status: 'synced',
          last_sync_at: new Date().toISOString()
        };
        setDocument(mockDoc);
        setSelectedDocument(docId);
      }
    } catch (error) {
      console.error('获取文档内容失败:', error);
      throw error;
    }
  };

  const handleSpaceSelect = async (space) => {
    setSelectedSpace(space.dingtalk_space_id);
    setCurrentView('documents');
    await loadDocuments(space.dingtalk_space_id);
  };

  const handleDocumentSelect = async (doc) => {
    if (mode === 'selector' && onDocumentSelect) {
      onDocumentSelect(doc);
      onClose();
      return;
    }
    
    setSelectedDocument(doc.id);
    setDocument(doc);
    setCurrentView('content');
  };

  const handleSync = async () => {
    if (onSync) {
      setLoading(true);
      try {
        await onSync(selectedSpace || selectedDocument);
        message.success('同步成功');
        // 重新加载数据
        if (currentView === 'documents') {
          await loadDocuments(selectedSpace);
        } else if (currentView === 'content') {
          await loadDocumentContent(selectedDocument);
        }
      } catch (error) {
        message.error('同步失败');
      } finally {
        setLoading(false);
      }
    }
  };

  const getSyncStatusColor = (status) => {
    const colors = {
      'synced': 'green',
      'pending': 'orange',
      'failed': 'red',
      'draft': 'blue'
    };
    return colors[status] || 'default';
  };

  const getSyncStatusText = (status) => {
    const texts = {
      'synced': '已同步',
      'pending': '待同步',
      'failed': '同步失败',
      'draft': '草稿'
    };
    return texts[status] || status;
  };

  const renderBreadcrumb = () => (
    <Breadcrumb style={{ marginBottom: 16 }}>
      <Breadcrumb.Item 
        href="#" 
        onClick={() => setCurrentView('spaces')}
      >
        <FolderOutlined />
        <span>知识库</span>
      </Breadcrumb.Item>
      {selectedSpace && (
        <Breadcrumb.Item 
          href="#" 
          onClick={() => setCurrentView('documents')}
        >
          {spaces.find(s => s.dingtalk_space_id === selectedSpace)?.space_name || '文档列表'}
        </Breadcrumb.Item>
      )}
      {document && currentView === 'content' && (
        <Breadcrumb.Item>{document.title}</Breadcrumb.Item>
      )}
    </Breadcrumb>
  );

  const renderSpacesList = () => (
    <div>
      <Title level={4}>选择知识库</Title>
      <Row gutter={[16, 16]}>
        {spaces.map(space => (
          <Col span={8} key={space.id}>
            <Card
              hoverable
              onClick={() => handleSpaceSelect(space)}
              style={{ cursor: 'pointer' }}
            >
              <Space direction="vertical" style={{ width: '100%' }}>
                <Space>
                  <FolderOutlined style={{ fontSize: 20, color: '#1890ff' }} />
                  <Text strong>{space.space_name}</Text>
                </Space>
                <Text type="secondary" ellipsis>
                  {space.space_description}
                </Text>
                <Space>
                  <Tag color="blue">{space.doc_count} 个文档</Tag>
                  <Tag color={getSyncStatusColor(space.sync_status)}>
                    {getSyncStatusText(space.sync_status)}
                  </Tag>
                </Space>
              </Space>
            </Card>
          </Col>
        ))}
      </Row>
    </div>
  );

  const renderDocumentsList = () => (
    <div>
      <Space style={{ marginBottom: 16, width: '100%', justifyContent: 'space-between' }}>
        <Title level={4}>文档列表</Title>
        {showActions && (
          <Space>
            <Button 
              icon={<SyncOutlined />} 
              onClick={handleSync}
              loading={loading}
            >
              同步文档
            </Button>
          </Space>
        )}
      </Space>
      
      {documents.length === 0 ? (
        <Empty description="暂无文档" />
      ) : (
        <Row gutter={[16, 16]}>
          {documents.map(doc => (
            <Col span={12} key={doc.id}>
              <Card
                hoverable
                onClick={() => handleDocumentSelect(doc)}
                style={{ cursor: 'pointer' }}
                actions={mode !== 'selector' && showActions ? [
                  <Tooltip key="view" title="查看">
                    <EyeOutlined />
                  </Tooltip>,
                  <Tooltip key="download" title="下载">
                    <DownloadOutlined />
                  </Tooltip>,
                  <Tooltip key="share" title="分享">
                    <ShareAltOutlined />
                  </Tooltip>
                ] : undefined}
              >
                <Space direction="vertical" style={{ width: '100%' }}>
                  <Space>
                    <FileTextOutlined style={{ fontSize: 16, color: '#52c41a' }} />
                    <Text strong ellipsis>{doc.title}</Text>
                  </Space>
                  <Space wrap>
                    <Tag color="blue">{doc.content_format}</Tag>
                    <Tag color={getSyncStatusColor(doc.sync_status)}>
                      {getSyncStatusText(doc.sync_status)}
                    </Tag>
                    {doc.tags && doc.tags.map(tag => (
                      <Tag key={tag} color="default">{tag}</Tag>
                    ))}
                  </Space>
                  <Text type="secondary" style={{ fontSize: 12 }}>
                    最后同步: {doc.last_sync_at ? new Date(doc.last_sync_at).toLocaleString() : '未同步'}
                  </Text>
                </Space>
              </Card>
            </Col>
          ))}
        </Row>
      )}
    </div>
  );

  const renderDocumentContent = () => (
    <div>
      <Space style={{ marginBottom: 16, width: '100%', justifyContent: 'space-between' }}>
        <Space direction="vertical" size={0}>
          <Title level={4}>{document.title}</Title>
          <Space>
            <Tag color="blue">{document.content_format}</Tag>
            <Tag color={getSyncStatusColor(document.sync_status)}>
              {getSyncStatusText(document.sync_status)}
            </Tag>
            {document.tags && document.tags.map(tag => (
              <Tag key={tag} color="default">{tag}</Tag>
            ))}
          </Space>
        </Space>
        
        {showActions && (
          <Space>
            <Button icon={<SyncOutlined />} onClick={handleSync} loading={loading}>
              同步
            </Button>
            <Button icon={<DownloadOutlined />}>
              下载
            </Button>
            <Button icon={<LinkOutlined />}>
              获取链接
            </Button>
          </Space>
        )}
      </Space>

      <Divider />

      <div style={{ 
        maxHeight: height - 200, 
        overflow: 'auto',
        padding: '16px',
        backgroundColor: '#fafafa',
        borderRadius: '6px'
      }}>
        {document.content_format === 'markdown' ? (
          <ReactMarkdown
            components={{
              code({node, inline, className, children, ...props}) {
                const match = /language-(\w+)/.exec(className || '');
                return !inline && match ? (
                  <SyntaxHighlighter
                    style={tomorrow}
                    language={match[1]}
                    PreTag="div"
                    {...props}
                  >
                    {String(children).replace(/\n$/, '')}
                  </SyntaxHighlighter>
                ) : (
                  <code className={className} {...props}>
                    {children}
                  </code>
                );
              }
            }}
          >
            {document.content}
          </ReactMarkdown>
        ) : (
          <div dangerouslySetInnerHTML={{ __html: document.content }} />
        )}
      </div>

      <Divider />
      
      <Space direction="vertical" size={0}>
        <Text type="secondary">
          最后同步时间: {document.last_sync_at ? new Date(document.last_sync_at).toLocaleString() : '未同步'}
        </Text>
        <Text type="secondary">
          创建时间: {document.created_at ? new Date(document.created_at).toLocaleString() : '-'}
        </Text>
      </Space>
    </div>
  );

  const renderContent = () => {
    if (loading) {
      return (
        <div style={{ textAlign: 'center', padding: '50px 0' }}>
          <Spin size="large" />
          <div style={{ marginTop: 16 }}>加载中...</div>
        </div>
      );
    }

    switch (currentView) {
      case 'spaces':
        return renderSpacesList();
      case 'documents':
        return renderDocumentsList();
      case 'content':
        return renderDocumentContent();
      default:
        return renderSpacesList();
    }
  };

  if (mode === 'embedded') {
    return (
      <Card title={title} style={{ height }}>
        {renderBreadcrumb()}
        {renderContent()}
      </Card>
    );
  }

  return (
    <Modal
      title={title}
      visible={visible}
      onCancel={onClose}
      width={1000}
      footer={null}
      style={{ top: 20 }}
      bodyStyle={{ height, overflow: 'auto' }}
    >
      {renderBreadcrumb()}
      {renderContent()}
    </Modal>
  );
};

export default DingtalkDocumentViewer; 