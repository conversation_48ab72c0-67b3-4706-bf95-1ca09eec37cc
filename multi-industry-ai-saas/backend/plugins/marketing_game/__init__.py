#!/usr/bin/env python
# -*- coding: utf-8 -*-

import logging
from fastapi import APIRouter, FastAPI
from sqlalchemy.engine import Engine
from sqlalchemy.orm import sessionmaker

# 导入API路由
from .api.router import router as marketing_game_router
from .adventure_game import router as adventure_game_router

# 导入数据库初始化
from .db_init import initialize_plugin, uninstall_plugin, get_plugin_status

# 插件版本
__version__ = "1.2.0"

# 插件信息
plugin_info = {
    "name": "营销游戏插件",
    "code": "marketing_game",
    "version": __version__,
    "description": "提供多种营销游戏活动，帮助客户线下互动，增强用户粘性。支持随机抽奖、幸运大转盘、刮刮卡、闯关游戏等多种游戏类型，支持奖品设置、限制抽奖次数、内定中奖人、自动识别小票等功能。",
    "author": "Retail AI Team",
    "category": "marketing",
    "icon": "gift",
    "price": 0,  # 免费插件
    "is_free": True,
    "requires_setup": False,
    "dependencies": [
        {"name": "pytesseract", "version": ">=0.3.10", "optional": True},
        {"name": "opencv-python-headless", "version": ">=4.5.0", "optional": True},
        {"name": "numpy", "version": ">=1.20.0", "required": True},
        {"name": "pillow", "version": ">=9.0.0", "required": True}
    ],
    "tables": [
        "marketing_game_settings",
        "marketing_game_activities",
        "marketing_game_prizes",
        "marketing_game_participants",
        "marketing_game_records"
    ],
    "features": [
        "🎲 随机抽奖 - 支持概率设置和奖品配置",
        "🎡 幸运大转盘 - 炫酷转盘动画效果",
        "🃏 刮刮卡游戏 - 刮开涂层揭晓奖品",
        "🗺️ 闯关游戏 - 多关卡挑战模式",
        "🎁 奖品管理 - 灵活的奖品配置系统",
        "📊 中奖率控制 - 精确的概率算法",
        "👥 参与者管理 - 用户游戏记录统计",
        "🎯 内定中奖 - 支持指定用户中奖",
        "📱 小票识别 - AI识别小票信息",
        "📈 数据统计 - 详细的游戏数据分析"
    ],
    "permissions": {
        "user": [
            "参与游戏",
            "查看游戏记录",
            "上传小票"
        ],
        "admin": [
            "创建游戏活动",
            "管理奖品",
            "查看统计数据",
            "设置中奖率",
            "管理参与者"
        ],
        "super_admin": [
            "插件配置",
            "系统设置"
        ]
    }
}

# 初始化日志
logger = logging.getLogger(__name__)

async def initialize(app: FastAPI = None, engine: Engine = None, db_session: sessionmaker = None):
    """
    插件初始化函数

    Args:
        app: FastAPI应用实例
        engine: 数据库引擎
        db_session: 数据库会话工厂
    """
    try:
        logger.info("正在初始化营销游戏插件...")

        # 初始化数据库
        success = await initialize_plugin()

        if success:
            logger.info("营销游戏插件数据库初始化成功")
        else:
            logger.warning("营销游戏插件数据库初始化失败")

        # 注册路由
        if app:
            # 注册营销游戏路由
            app.include_router(marketing_game_router, prefix="/plugins")
            # 注册闯关游戏路由
            app.include_router(adventure_game_router, prefix="/plugins")
            logger.info("营销游戏插件路由注册成功")

        logger.info(f"营销游戏插件 v{__version__} 初始化完成")
        logger.info("支持功能：随机抽奖、幸运大转盘、刮刮卡、闯关游戏、奖品管理、数据统计")
        return True
    except Exception as e:
        logger.error(f"营销游戏插件初始化失败: {str(e)}")
        return False

async def install(tenant_id: str, project_id: str = None):
    """
    安装插件到指定租户

    Args:
        tenant_id: 租户ID
        project_id: 项目ID（可选）
    """
    try:
        logger.info(f"正在为租户 {tenant_id} 安装营销游戏插件...")
        success = await initialize_plugin(tenant_id, project_id)
        if success:
            logger.info(f"营销游戏插件安装成功，租户ID: {tenant_id}")
        return success
    except Exception as e:
        logger.error(f"营销游戏插件安装失败: {str(e)}")
        return False

async def uninstall(tenant_id: str, project_id: str = None):
    """
    从指定租户卸载插件

    Args:
        tenant_id: 租户ID
        project_id: 项目ID（可选）
    """
    try:
        logger.info(f"正在为租户 {tenant_id} 卸载营销游戏插件...")
        success = await uninstall_plugin(tenant_id, project_id)
        if success:
            logger.info(f"营销游戏插件卸载成功，租户ID: {tenant_id}")
        return success
    except Exception as e:
        logger.error(f"营销游戏插件卸载失败: {str(e)}")
        return False

async def get_status(tenant_id: str):
    """
    获取插件在指定租户的安装状态

    Args:
        tenant_id: 租户ID
    """
    try:
        return await get_plugin_status(tenant_id)
    except Exception as e:
        logger.error(f"获取营销游戏插件状态失败: {str(e)}")
        return {"installed": False, "plugin": None, "tenant_plugin": None}

# 导出主要功能
__all__ = [
    "plugin_info",
    "initialize", 
    "install",
    "uninstall", 
    "get_status",
    "marketing_game_router",
    "adventure_game_router"
]
