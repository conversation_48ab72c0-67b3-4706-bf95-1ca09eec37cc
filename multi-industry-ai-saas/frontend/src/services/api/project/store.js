import api from '../httpClient';
import { getProjectId } from '../httpClient';

/**
 * 项目-门店管理API
 */
const store = {
  /**
   * 获取门店列表
   * @param {Object} params - 查询参数
   * @returns {Promise} - 门店列表
   */
  getList: async (params = {}) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.get(`/project/${projectId}/basic/stores`, { params });
  },

  /**
   * 获取门店详情
   * @param {string} id - 门店ID
   * @returns {Promise} - 门店详情
   */
  getDetail: async (id) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.get(`/project/${projectId}/basic/stores/${id}`);
  },

  /**
   * 创建门店
   * @param {Object} data - 门店数据
   * @returns {Promise} - 创建结果
   */
  create: async (data) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.post(`/project/${projectId}/basic/stores`, data);
  },

  /**
   * 更新门店
   * @param {string} id - 门店ID
   * @param {Object} data - 门店数据
   * @returns {Promise} - 更新结果
   */
  update: async (id, data) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.put(`/project/${projectId}/basic/stores/${id}`, data);
  },

  /**
   * 删除门店
   * @param {string} id - 门店ID
   * @returns {Promise} - 删除结果
   */
  delete: async (id) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.delete(`/project/${projectId}/basic/stores/${id}`);
  },

  /**
   * 地址解析获取经纬度
   * @param {Object} data - 地址数据
   * @param {string} data.address - 地址
   * @returns {Promise} - 解析结果
   */
  geocode: async (data) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.post(`/project/${projectId}/basic/stores/geocode`, data);
  },

  /**
   * 获取门店任务列表
   * @param {string} storeId - 门店ID
   * @param {Object} params - 查询参数
   * @returns {Promise} - 门店任务列表
   */
  getTasks: async (storeId, params = {}) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.get(`/project/${projectId}/stores/${storeId}/tasks`, { params });
  },

  /**
   * 创建门店任务
   * @param {string} storeId - 门店ID
   * @param {Object} data - 任务数据
   * @returns {Promise} - 创建结果
   */
  createTask: async (storeId, data) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.post(`/project/${projectId}/stores/${storeId}/tasks`, data);
  },

  /**
   * 更新门店任务
   * @param {string} storeId - 门店ID
   * @param {string} taskId - 任务ID
   * @param {Object} data - 任务数据
   * @returns {Promise} - 更新结果
   */
  updateTask: async (storeId, taskId, data) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.put(`/project/${projectId}/stores/${storeId}/tasks/${taskId}`, data);
  },

  /**
   * 删除门店任务
   * @param {string} storeId - 门店ID
   * @param {string} taskId - 任务ID
   * @returns {Promise} - 删除结果
   */
  deleteTask: async (storeId, taskId) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.delete(`/project/${projectId}/stores/${storeId}/tasks/${taskId}`);
  },

  /**
   * 获取门店例行任务列表
   * @param {string} storeId - 门店ID
   * @param {Object} params - 查询参数
   * @returns {Promise} - 门店例行任务列表
   */
  getRoutineTasks: async (storeId, params = {}) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.get(`/project/${projectId}/stores/${storeId}/routine-tasks`, { params });
  },

  /**
   * 创建门店例行任务
   * @param {string} storeId - 门店ID
   * @param {Object} data - 例行任务数据
   * @returns {Promise} - 创建结果
   */
  createRoutineTask: async (storeId, data) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.post(`/project/${projectId}/stores/${storeId}/routine-tasks`, data);
  },

  /**
   * 更新门店例行任务
   * @param {string} storeId - 门店ID
   * @param {string} taskId - 例行任务ID
   * @param {Object} data - 例行任务数据
   * @returns {Promise} - 更新结果
   */
  updateRoutineTask: async (storeId, taskId, data) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.put(`/project/${projectId}/stores/${storeId}/routine-tasks/${taskId}`, data);
  },

  /**
   * 删除门店例行任务
   * @param {string} storeId - 门店ID
   * @param {string} taskId - 例行任务ID
   * @returns {Promise} - 删除结果
   */
  deleteRoutineTask: async (storeId, taskId) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.delete(`/project/${projectId}/stores/${storeId}/routine-tasks/${taskId}`);
  },

  /**
   * 获取门店周边调研列表
   * @param {string} storeId - 门店ID
   * @param {Object} params - 查询参数
   * @returns {Promise} - 门店周边调研列表
   */
  getAreaSurveys: async (storeId, params = {}) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.get(`/project/${projectId}/stores/${storeId}/area-surveys`, { params });
  },

  /**
   * 创建门店周边调研
   * @param {string} storeId - 门店ID
   * @param {Object} data - 周边调研数据
   * @returns {Promise} - 创建结果
   */
  createAreaSurvey: async (storeId, data) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.post(`/project/${projectId}/stores/${storeId}/area-surveys`, data);
  },

  /**
   * 更新门店周边调研
   * @param {string} storeId - 门店ID
   * @param {string} surveyId - 周边调研ID
   * @param {Object} data - 周边调研数据
   * @returns {Promise} - 更新结果
   */
  updateAreaSurvey: async (storeId, surveyId, data) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.put(`/project/${projectId}/stores/${storeId}/area-surveys/${surveyId}`, data);
  },

  /**
   * 删除门店周边调研
   * @param {string} storeId - 门店ID
   * @param {string} surveyId - 周边调研ID
   * @returns {Promise} - 删除结果
   */
  deleteAreaSurvey: async (storeId, surveyId) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.delete(`/project/${projectId}/stores/${storeId}/area-surveys/${surveyId}`);
  },

  /**
   * 获取门店周边调研统计数据
   * @param {string} storeId - 门店ID
   * @returns {Promise} - 周边调研统计数据
   */
  getAreaSurveyStats: async (storeId) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.get(`/project/${projectId}/stores/${storeId}/area-surveys/stats`);
  },

  /**
   * 获取门店周边调研详情
   * @param {string} storeId - 门店ID
   * @param {string} surveyId - 周边调研ID
   * @returns {Promise} - 周边调研详情
   */
  getAreaSurveyDetail: async (storeId, surveyId) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.get(`/project/${projectId}/stores/${storeId}/area-surveys/${surveyId}`);
  },

  /**
   * 使用AI分析周边调研
   * @param {string} storeId - 门店ID
   * @param {string} surveyId - 周边调研ID
   * @returns {Promise} - AI分析结果
   */
  analyzeAreaSurvey: async (storeId, surveyId) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.post(`/project/${projectId}/stores/${storeId}/area-surveys/${surveyId}/ai-analysis`);
  },

  /**
   * 获取门店竞对调研列表 (兼容旧版API)
   * @deprecated 请使用 getAreaSurveys 代替
   */
  getCompetitorSurveys: async (storeId, params = {}) => {
    return await store.getAreaSurveys(storeId, params);
  },

  /**
   * 创建门店竞对调研 (兼容旧版API)
   * @deprecated 请使用 createAreaSurvey 代替
   */
  createCompetitorSurvey: async (storeId, data) => {
    return await store.createAreaSurvey(storeId, data);
  },

  /**
   * 更新门店竞对调研 (兼容旧版API)
   * @deprecated 请使用 updateAreaSurvey 代替
   */
  updateCompetitorSurvey: async (storeId, surveyId, data) => {
    return await store.updateAreaSurvey(storeId, surveyId, data);
  },

  /**
   * 删除门店竞对调研 (兼容旧版API)
   * @deprecated 请使用 deleteAreaSurvey 代替
   */
  deleteCompetitorSurvey: async (storeId, surveyId) => {
    return await store.deleteAreaSurvey(storeId, surveyId);
  },

  getCategories: async () => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.get(`/project/${projectId}/basic/stores/store-categories`);
  },

  /**
   * 创建分类
   * @param {Object} data - 分类数据
   * @returns {Promise} - 创建结果
   */
  createCategory: async (data) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.post(`/project/${projectId}/basic/stores/store-categories`, data);
  },

  /**
   * 更新分类
   * @param {string} id - 分类ID
   * @param {Object} data - 分类数据
   * @returns {Promise} - 更新结果
   */
  updateCategory: async (id, data) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.put(`/project/${projectId}/basic/stores/store-categories/${id}`, data);
  },

  /**
   * 删除分类
   * @param {string} id - 分类ID
   * @returns {Promise} - 删除结果
   */
  deleteCategory: async (id) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.delete(`/project/${projectId}/basic/stores/store-categories/${id}`);
  },

  getRegions: async () => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.get(`/project/${projectId}/basic/stores/store-regions`);
  },

  /**
   * 创建区域
   * @param {Object} data - 区域数据
   * @returns {Promise} - 创建结果
   */
  createRegion: async (data) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.post(`/project/${projectId}/basic/stores/store-regions`, data);
  },

  /**
   * 更新区域
   * @param {string} id - 区域ID
   * @param {Object} data - 区域数据
   * @returns {Promise} - 更新结果
   */
  updateRegion: async (id, data) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.put(`/project/${projectId}/basic/stores/store-regions/${id}`, data);
  },

  /**
   * 删除区域
   * @param {string} id - 区域ID
   * @returns {Promise} - 删除结果
   */
  deleteRegion: async (id) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.delete(`/project/${projectId}/basic/stores/store-regions/${id}`);
  }
};

export default store;
