#!/usr/bin/env python
# -*- coding: utf-8 -*-

import asyncio
import json
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import openai
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import desc, func

from models.tenant import Tenant
from models.project import Project
from ..models.models import MarketingGame, GameRecord, GameParticipant, GameAnalytics
from core.config import settings

logger = logging.getLogger(__name__)

class MarketingGameAIAssistant:
    """营销游戏AI助手"""
    
    def __init__(self):
        self.openai_client = None
        if hasattr(settings, 'OPENAI_API_KEY') and settings.OPENAI_API_KEY:
            openai.api_key = settings.OPENAI_API_KEY
            self.openai_client = openai
    
    async def get_welcome_message(self, game: MarketingGame, user_data: Dict = None) -> str:
        """获取AI欢迎消息"""
        try:
            if game.ai_welcome_message:
                return self._personalize_message(game.ai_welcome_message, user_data)
            
            # 根据游戏类型和营销类型生成默认欢迎消息
            welcome_templates = {
                "opening_promotion": "🎉 欢迎参与我们的开业庆典活动！让我来帮您了解这个精彩的游戏！",
                "anniversary": "🎂 感谢您参与我们的周年庆活动！我是您的专属游戏助手，有什么问题尽管问我！",
                "holiday_marketing": "🎄 节日快乐！我来为您介绍这个有趣的节日游戏，祝您好运连连！",
                "custom_festival": "🎊 欢迎来到我们的特别活动！我会全程陪伴您，让您玩得开心！",
                "daily_activity": "😊 您好！欢迎参与我们的日常活动，我来帮您了解游戏规则！",
                "membership_promotion": "💎 尊贵的会员，欢迎您！让我为您介绍这个专属游戏！"
            }
            
            return welcome_templates.get(
                game.marketing_type, 
                "🎮 欢迎来到我们的营销游戏！我是您的AI助手，有任何问题都可以问我！"
            )
        except Exception as e:
            logger.error(f"获取欢迎消息失败: {e}")
            return "您好！欢迎参与游戏，我是您的AI助手！"
    
    async def get_game_suggestions(self, game_id: str, db: AsyncSession) -> List[Dict]:
        """获取游戏建议"""
        try:
            # 获取游戏数据
            game_query = select(MarketingGame).where(MarketingGame.id == game_id)
            result = await db.execute(game_query)
            game = result.scalar_one_or_none()
            
            if not game:
                return []
            
            # 获取游戏统计数据
            analytics = await self._get_game_analytics(game_id, db)
            suggestions = []
            
            # 基于数据分析提供建议
            if analytics['win_rate'] < 5:
                suggestions.append({
                    "type": "optimization",
                    "title": "提高中奖率建议",
                    "message": "当前中奖率较低，建议适当提高中奖概率或增加安慰奖，提升用户体验。",
                    "action": "adjust_prize_probability",
                    "priority": "high"
                })
            
            if analytics['participation_rate'] < 20:
                suggestions.append({
                    "type": "promotion",
                    "title": "提升参与度建议", 
                    "message": "参与度偏低，建议优化宣传文案或增加社交分享奖励机制。",
                    "action": "improve_promotion",
                    "priority": "medium"
                })
            
            if analytics['bounce_rate'] > 60:
                suggestions.append({
                    "type": "ux",
                    "title": "用户体验优化",
                    "message": "用户跳出率较高，建议简化游戏流程或优化界面设计。",
                    "action": "optimize_ux",
                    "priority": "high"
                })
            
            # 基于营销类型的建议
            marketing_suggestions = await self._get_marketing_type_suggestions(game)
            suggestions.extend(marketing_suggestions)
            
            return suggestions
            
        except Exception as e:
            logger.error(f"获取游戏建议失败: {e}")
            return []
    
    async def answer_user_question(self, question: str, game: MarketingGame, context: Dict = None) -> str:
        """回答用户问题"""
        try:
            # 常见问题快速回答
            quick_answers = await self._get_quick_answers(question, game)
            if quick_answers:
                return quick_answers
            
            # 如果没有OpenAI API，使用预设回答
            if not self.openai_client:
                return await self._get_default_answer(question, game)
            
            # 使用OpenAI生成回答
            return await self._generate_ai_answer(question, game, context)
            
        except Exception as e:
            logger.error(f"回答用户问题失败: {e}")
            return "抱歉，我暂时无法回答这个问题。请稍后再试或联系客服。"
    
    async def get_personalized_tips(self, user_id: str, game_id: str, db: AsyncSession) -> List[str]:
        """获取个性化提示"""
        try:
            # 获取用户游戏记录
            records_query = select(GameRecord).where(
                GameRecord.game_id == game_id,
                GameRecord.participant_id == user_id
            ).order_by(desc(GameRecord.created_at)).limit(10)
            
            result = await db.execute(records_query)
            records = result.scalars().all()
            
            tips = []
            
            if not records:
                tips.append("🎯 第一次玩？别紧张，仔细阅读规则，好运会眷顾有准备的人！")
                tips.append("💡 小贴士：保持良好心态，每次抽奖都是新的机会！")
            else:
                # 基于历史记录分析
                win_count = sum(1 for r in records if r.is_win)
                total_plays = len(records)
                
                if win_count == 0:
                    tips.append("🍀 别灰心！运气总会来的，坚持就是胜利！")
                elif win_count / total_plays > 0.5:
                    tips.append("🎉 您的运气真不错！继续保持这份好运气！")
                else:
                    tips.append("😊 您已经有过中奖经历，好运还会再来的！")
                
                # 分析游戏时间
                recent_record = records[0]
                if recent_record.created_at.date() == datetime.now().date():
                    tips.append("⏰ 今天已经玩过了，记得明天再来试试运气！")
            
            return tips
            
        except Exception as e:
            logger.error(f"获取个性化提示失败: {e}")
            return ["🎮 享受游戏过程，好运自然来！"]
    
    async def analyze_game_performance(self, game_id: str, db: AsyncSession) -> Dict:
        """分析游戏表现"""
        try:
            # 获取基础数据
            analytics = await self._get_game_analytics(game_id, db)
            
            # AI分析
            analysis = {
                "overall_score": self._calculate_overall_score(analytics),
                "strengths": [],
                "weaknesses": [], 
                "recommendations": [],
                "predicted_trends": {}
            }
            
            # 分析优势
            if analytics['win_rate'] > 10:
                analysis["strengths"].append("中奖率适中，用户体验良好")
            if analytics['participation_rate'] > 30:
                analysis["strengths"].append("参与度较高，活动吸引力强")
            if analytics['return_rate'] > 40:
                analysis["strengths"].append("用户回访率高，活动粘性强")
            
            # 分析不足
            if analytics['bounce_rate'] > 50:
                analysis["weaknesses"].append("跳出率偏高，需要优化用户体验")
            if analytics['share_rate'] < 10:
                analysis["weaknesses"].append("分享率较低，社交传播效果有限")
            if analytics['conversion_rate'] < 5:
                analysis["weaknesses"].append("转化率偏低，需要优化转化路径")
            
            # 生成建议
            analysis["recommendations"] = await self._generate_recommendations(analytics)
            
            # 预测趋势
            analysis["predicted_trends"] = await self._predict_trends(analytics)
            
            return analysis
            
        except Exception as e:
            logger.error(f"分析游戏表现失败: {e}")
            return {"overall_score": 0, "error": str(e)}
    
    async def get_optimization_suggestions(self, game_id: str, db: AsyncSession) -> List[Dict]:
        """获取优化建议"""
        try:
            analytics = await self._get_game_analytics(game_id, db)
            suggestions = []
            
            # 参与度优化
            if analytics['participation_rate'] < 30:
                suggestions.append({
                    "category": "participation",
                    "title": "提升参与度",
                    "description": "当前参与度较低，建议优化活动入口和宣传策略",
                    "actions": [
                        "优化活动页面设计，提高视觉吸引力",
                        "增加多渠道推广，扩大活动曝光",
                        "设置新手引导，降低参与门槛",
                        "增加参与奖励，提高参与动机"
                    ],
                    "expected_improvement": "参与度预计提升20-40%"
                })
            
            # 中奖体验优化
            if analytics['win_rate'] < 8:
                suggestions.append({
                    "category": "winning_experience",
                    "title": "优化中奖体验",
                    "description": "中奖率偏低可能影响用户体验",
                    "actions": [
                        "适当提高小奖概率，保证基础体验",
                        "增加安慰奖，减少用户挫败感",
                        "优化中奖动画和音效",
                        "设置连续参与奖励机制"
                    ],
                    "expected_improvement": "用户满意度预计提升30%"
                })
            
            # 社交传播优化
            if analytics['share_rate'] < 15:
                suggestions.append({
                    "category": "social_sharing",
                    "title": "增强社交传播",
                    "description": "分享率较低，限制了活动的自然传播",
                    "actions": [
                        "优化分享素材，增加吸引力",
                        "设置分享奖励机制",
                        "增加好友助力功能",
                        "定制个性化分享内容"
                    ],
                    "expected_improvement": "分享率预计提升50-100%"
                })
            
            return suggestions
            
        except Exception as e:
            logger.error(f"获取优化建议失败: {e}")
            return []
    
    async def _get_game_analytics(self, game_id: str, db: AsyncSession) -> Dict:
        """获取游戏分析数据"""
        try:
            # 查询游戏记录
            records_query = select(GameRecord).where(GameRecord.game_id == game_id)
            result = await db.execute(records_query)
            records = result.scalars().all()
            
            # 查询参与者
            participants_query = select(GameParticipant).where(GameParticipant.game_id == game_id)
            result = await db.execute(participants_query)
            participants = result.scalars().all()
            
            total_records = len(records)
            total_participants = len(participants)
            winners = sum(1 for r in records if r.is_win)
            
            analytics = {
                "total_participants": total_participants,
                "total_plays": total_records,
                "winners_count": winners,
                "win_rate": (winners / total_records * 100) if total_records > 0 else 0,
                "participation_rate": 0,  # 需要更多数据计算
                "bounce_rate": 0,  # 需要更多数据计算
                "return_rate": 0,  # 需要更多数据计算
                "share_rate": 0,  # 需要更多数据计算
                "conversion_rate": 0,  # 需要更多数据计算
                "average_session_duration": 0  # 需要更多数据计算
            }
            
            # 计算更详细的指标
            if participants:
                total_draws = sum(p.draw_count for p in participants)
                total_wins = sum(p.win_count for p in participants)
                
                analytics["average_plays_per_user"] = total_draws / total_participants if total_participants > 0 else 0
                analytics["average_wins_per_user"] = total_wins / total_participants if total_participants > 0 else 0
                
                # 计算回访率
                return_users = sum(1 for p in participants if p.participation_count > 1)
                analytics["return_rate"] = (return_users / total_participants * 100) if total_participants > 0 else 0
                
                # 计算分享率
                sharing_users = sum(1 for p in participants if p.share_count > 0)
                analytics["share_rate"] = (sharing_users / total_participants * 100) if total_participants > 0 else 0
            
            return analytics
            
        except Exception as e:
            logger.error(f"获取游戏分析数据失败: {e}")
            return {}
    
    async def _get_quick_answers(self, question: str, game: MarketingGame) -> Optional[str]:
        """获取常见问题的快速回答"""
        question_lower = question.lower()
        
        # 规则相关
        if any(keyword in question_lower for keyword in ["规则", "怎么玩", "如何参与"]):
            return f"🎮 {game.name}的参与很简单！{game.description or '按照页面提示操作即可参与抽奖。'}"
        
        # 奖品相关
        if any(keyword in question_lower for keyword in ["奖品", "能得到什么", "有什么奖"]):
            return "🎁 我们准备了丰富的奖品！具体奖品信息请查看奖品列表，祝您好运！"
        
        # 次数相关
        if any(keyword in question_lower for keyword in ["次数", "几次", "多少次"]):
            max_draws = game.max_draws_per_user or 1
            return f"🎯 每人最多可以参与{max_draws}次，请珍惜每次机会！"
        
        # 时间相关
        if any(keyword in question_lower for keyword in ["时间", "什么时候", "期限"]):
            return f"⏰ 活动时间：{game.start_time.strftime('%Y-%m-%d %H:%M')} 至 {game.end_time.strftime('%Y-%m-%d %H:%M')}"
        
        # 技术问题
        if any(keyword in question_lower for keyword in ["卡", "慢", "打不开", "错误"]):
            return "🔧 遇到技术问题？请尝试刷新页面或检查网络连接。如果问题持续，请联系客服。"
        
        return None
    
    async def _get_default_answer(self, question: str, game: MarketingGame) -> str:
        """获取默认回答"""
        personality_responses = {
            "friendly": "😊 我来帮您解答！",
            "professional": "我很乐意为您提供帮助。",
            "playful": "🎈 哈哈，让我想想这个问题！"
        }
        
        prefix = personality_responses.get(game.ai_personality, "😊 我来帮您解答！")
        
        return f"{prefix}关于您的问题，建议您查看游戏说明或联系客服获取更详细的信息。有其他问题随时问我！"
    
    async def _generate_ai_answer(self, question: str, game: MarketingGame, context: Dict = None) -> str:
        """使用AI生成回答"""
        try:
            prompt = f"""
            你是一个友好的营销游戏AI助手，正在帮助用户了解游戏"{game.name}"。
            
            游戏信息：
            - 游戏类型：{game.game_type}
            - 营销类型：{game.marketing_type}
            - 游戏描述：{game.description}
            - 最大抽奖次数：{game.max_draws_per_user}
            
            用户问题：{question}
            
            请用{game.ai_personality}的语气回答，保持简洁友好，不超过100字。
            """
            
            response = await self.openai_client.ChatCompletion.acreate(
                model="gpt-3.5-turbo",
                messages=[{"role": "user", "content": prompt}],
                max_tokens=200,
                temperature=0.7
            )
            
            return response.choices[0].message.content.strip()
            
        except Exception as e:
            logger.error(f"AI生成回答失败: {e}")
            return await self._get_default_answer(question, game)
    
    async def _get_marketing_type_suggestions(self, game: MarketingGame) -> List[Dict]:
        """根据营销类型获取建议"""
        suggestions = []
        
        if game.marketing_type == "opening_promotion":
            suggestions.append({
                "type": "opening",
                "title": "开业营销建议",
                "message": "建议增加首次体验奖励，吸引更多新客户关注。",
                "action": "add_first_time_bonus",
                "priority": "medium"
            })
        
        elif game.marketing_type == "anniversary":
            suggestions.append({
                "type": "anniversary",
                "title": "周年庆优化",
                "message": "可以设置限时大奖，营造紧迫感和特殊感。",
                "action": "add_limited_grand_prize",
                "priority": "medium"
            })
        
        elif game.marketing_type == "holiday_marketing":
            suggestions.append({
                "type": "holiday",
                "title": "节日营销建议",
                "message": "建议使用节日主题元素，增强节日氛围。",
                "action": "enhance_holiday_theme",
                "priority": "low"
            })
        
        return suggestions
    
    def _personalize_message(self, message: str, user_data: Dict = None) -> str:
        """个性化消息"""
        if not user_data:
            return message
        
        # 替换个性化变量
        personalizations = {
            "{user_name}": user_data.get("name", "朋友"),
            "{time_of_day}": self._get_time_greeting(),
            "{weather}": user_data.get("weather", ""),
        }
        
        for placeholder, value in personalizations.items():
            message = message.replace(placeholder, value)
        
        return message
    
    def _get_time_greeting(self) -> str:
        """根据时间获取问候语"""
        hour = datetime.now().hour
        if hour < 6:
            return "深夜"
        elif hour < 12:
            return "上午"
        elif hour < 18:
            return "下午"
        else:
            return "晚上"
    
    def _calculate_overall_score(self, analytics: Dict) -> float:
        """计算总体评分"""
        try:
            weights = {
                "win_rate": 0.2,
                "participation_rate": 0.25,
                "return_rate": 0.2,
                "share_rate": 0.15,
                "conversion_rate": 0.2
            }
            
            score = 0
            for metric, weight in weights.items():
                value = analytics.get(metric, 0)
                # 将指标标准化到0-100分
                normalized_value = min(100, max(0, value))
                score += normalized_value * weight
            
            return round(score, 1)
            
        except Exception as e:
            logger.error(f"计算总体评分失败: {e}")
            return 0
    
    async def _generate_recommendations(self, analytics: Dict) -> List[str]:
        """生成建议"""
        recommendations = []
        
        if analytics.get("win_rate", 0) < 5:
            recommendations.append("适当提高中奖率，增强用户体验")
        
        if analytics.get("participation_rate", 0) < 20:
            recommendations.append("优化推广策略，提升活动曝光度")
        
        if analytics.get("return_rate", 0) < 30:
            recommendations.append("增加用户粘性机制，如连续签到奖励")
        
        if analytics.get("share_rate", 0) < 10:
            recommendations.append("设置分享激励，促进社交传播")
        
        return recommendations
    
    async def _predict_trends(self, analytics: Dict) -> Dict:
        """预测趋势"""
        # 简单的趋势预测逻辑
        return {
            "participation_trend": "稳定" if analytics.get("participation_rate", 0) > 20 else "需提升",
            "engagement_trend": "良好" if analytics.get("return_rate", 0) > 30 else "待改善",
            "viral_potential": "高" if analytics.get("share_rate", 0) > 15 else "中等"
        } 