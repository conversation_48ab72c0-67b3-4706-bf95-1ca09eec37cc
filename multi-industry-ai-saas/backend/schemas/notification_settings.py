from typing import List, Optional, Dict, Any, Union
from pydantic import BaseModel, UUID4, Field
from datetime import datetime

# 通知设置基础模型
class NotificationSettingsBase(BaseModel):
    """通知设置基础模型"""
    # 普通通知设置
    normal_frequency: str = "realtime"  # realtime, hourly, daily
    
    # 高优先级通知设置
    high_priority_frequency: str = "realtime"  # realtime, hourly, daily
    
    # 通知方式设置
    email_notifications: bool = True
    push_notifications: bool = True
    sms_notifications: bool = False
    sound_enabled: bool = True
    
    # 通知类型设置
    system_notifications: bool = True
    task_notifications: bool = True
    inventory_notifications: bool = True
    order_notifications: bool = True
    security_notifications: bool = True
    
    # 工作流通知设置
    workflow_notifications: Dict[str, Any] = Field(default_factory=dict)


class NotificationSettingsCreate(NotificationSettingsBase):
    """创建通知设置模型"""
    pass


class NotificationSettingsUpdate(BaseModel):
    """更新通知设置模型"""
    # 普通通知设置
    normal_frequency: Optional[str] = None
    
    # 高优先级通知设置
    high_priority_frequency: Optional[str] = None
    
    # 通知方式设置
    email_notifications: Optional[bool] = None
    push_notifications: Optional[bool] = None
    sms_notifications: Optional[bool] = None
    sound_enabled: Optional[bool] = None
    
    # 通知类型设置
    system_notifications: Optional[bool] = None
    task_notifications: Optional[bool] = None
    inventory_notifications: Optional[bool] = None
    order_notifications: Optional[bool] = None
    security_notifications: Optional[bool] = None
    
    # 工作流通知设置
    workflow_notifications: Optional[Dict[str, Any]] = None


class NotificationSettingsInDB(NotificationSettingsBase):
    """数据库通知设置模型"""
    id: UUID4
    tenant_id: Optional[UUID4] = None
    project_id: UUID4
    user_id: UUID4
    created_at: datetime
    updated_at: datetime

    class Config:
        model_config = {"from_attributes": True}


class NotificationSettingsResponse(BaseModel):
    """通知设置响应模型"""
    success: bool
    message: Optional[str] = None
    data: Optional[NotificationSettingsInDB] = None


# 工作流通知设置模型
class WorkflowNotificationSettings(BaseModel):
    """工作流通知设置模型"""
    enabled: bool = True
    notify_type: str = "roles"  # roles, users
    notify_roles: List[str] = []
    notify_users: List[UUID4] = []
    title: str
    template: str


class WorkflowNotificationSettingsUpdate(BaseModel):
    """更新工作流通知设置模型"""
    enabled: Optional[bool] = None
    notify_type: Optional[str] = None
    notify_roles: Optional[List[str]] = None
    notify_users: Optional[List[UUID4]] = None
    title: Optional[str] = None
    template: Optional[str] = None
