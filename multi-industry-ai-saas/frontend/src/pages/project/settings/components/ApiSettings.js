import React, { useState, useEffect } from 'react';
import { Form, Input, Button, message, Spin, Alert, Divider, Row, Col, Card } from 'antd';
import { SaveOutlined, ReloadOutlined } from '@ant-design/icons';
import apiService from '../../../../services/api';

const ApiSettings = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    fetchSettings();
  }, []);

  const fetchSettings = async () => {
    setLoading(true);
    try {
      const settings = await apiService.project.settings.getApiSettings();
      form.setFieldsValue(settings);
    } catch (error) {
      console.error('获取接口设置失败:', error);
      message.error('获取接口设置失败');
    } finally {
      setLoading(false);
    }
  };

  const onFinish = async (values) => {
    setLoading(true);
    try {
      await apiService.project.settings.updateApiSettings(values);
      message.success('接口设置保存成功！');
    } catch (error) {
      console.error('保存接口设置失败:', error);
      message.error('保存接口设置失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Spin spinning={loading}>
      <Form form={form} layout="vertical" onFinish={onFinish}>
        <Alert
          message="第三方接口设置"
          description="在此处配置用于获取外部数据（如天气、地图信息）的第三方服务API密钥。"
          type="info"
          showIcon
          style={{ marginBottom: 24 }}
        />
        
        <Card title="天气服务 (和风天气)" style={{ marginBottom: 24 }}>
            <Row gutter={24}>
              <Col span={12}>
                <Form.Item
                  label="API Key"
                  name={['weather', 'apiKey']}
                  tooltip="用于获取天气数据"
                >
                  <Input.Password placeholder="请输入和风天气API Key" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="API Host"
                  name={['weather', 'apiHost']}
                  tooltip="和风天气API的请求地址，例如 https://devapi.qweather.com"
                >
                  <Input placeholder="请输入API Host" />
                </Form.Item>
              </Col>
            </Row>
        </Card>

        <Card title="地图服务 (高德地图)" style={{ marginBottom: 24 }}>
            <Row gutter={24}>
                <Col span={12}>
                    <Form.Item
                    name={['gaode', 'apiKey']}
                    label="Web服务API Key"
                    rules={[{ required: false, message: '如需使用高德地图，请输入Web服务API Key' }]}
                    >
                    <Input.Password placeholder="请输入高德地图Web服务API Key" />
                    </Form.Item>
                </Col>
                <Col span={12}>
                    <Form.Item
                    name={['gaode', 'apiSecret']}
                    label="API Secret (如有)"
                    >
                    <Input.Password placeholder="请输入高德地图API Secret" />
                    </Form.Item>
                </Col>
            </Row>
        </Card>
        
        <Form.Item>
          <Button type="primary" htmlType="submit" icon={<SaveOutlined />} loading={loading}>
            保存设置
          </Button>
          <Button style={{ marginLeft: 8 }} icon={<ReloadOutlined />} onClick={() => fetchSettings()}>
            重新加载
          </Button>
        </Form.Item>
      </Form>
    </Spin>
  );
};

export default ApiSettings; 