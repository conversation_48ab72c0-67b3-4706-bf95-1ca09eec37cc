from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query, Path, UploadFile, File, Form, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import func, and_, or_
import uuid
from datetime import datetime, timedelta
import pandas as pd
import io
import json

from db.database import get_db
from models.inventory import InventoryItem, InventoryHistory, InventoryTransfer, InventoryTransferItem, InventoryCheck, InventoryCheckItem
from models.product import Product
from models.warehouse import Warehouse
from models.user import User
from schemas.inventory import (
    InventoryItemCreate, InventoryItemUpdate, InventoryItemResponse, InventoryItemListResponse,
    InventoryHistoryListResponse, InventoryTransferCreate, InventoryTransferUpdate,
    InventoryTransferResponse, InventoryTransferListResponse, InventoryCheckCreate,
    InventoryCheckUpdate, InventoryCheckResponse, InventoryCheckListResponse,
    InventoryImportResponse, InventoryImportConfirm, InventoryImportConfirmResponse
)
from api.deps import get_current_user, get_current_project_id, get_current_tenant_id

router = APIRouter()

# 库存项管理API
@router.get("/items", response_model=InventoryItemListResponse)
async def get_inventory_items(
    warehouse_id: Optional[uuid.UUID] = None,
    category_id: Optional[uuid.UUID] = None,
    is_low_stock: Optional[bool] = None,
    is_slow_moving: Optional[bool] = None,
    search: Optional[str] = None,
    skip: int = 0,
    limit: int = 100,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    project_id: uuid.UUID = Depends(get_current_project_id),
    tenant_id: Optional[uuid.UUID] = Depends(get_current_tenant_id)
):
    """
    获取库存项列表
    """
    try:
        # 构建查询条件
        conditions = [
            InventoryItem.project_id == project_id
        ]

        if warehouse_id:
            conditions.append(InventoryItem.warehouse_id == warehouse_id)

        if is_low_stock:
            conditions.append(InventoryItem.quantity < InventoryItem.min_quantity)

        if is_slow_moving:
            conditions.append(InventoryItem.is_slow_moving == True)

        if search:
            # 联合查询产品名称和编码
            conditions.append(
                or_(
                    Product.name.ilike(f"%{search}%"),
                    Product.sku.ilike(f"%{search}%")
                )
            )

        if category_id:
            conditions.append(Product.category_id == category_id)

        # 构建查询
        query = (
            select(
                InventoryItem,
                Product.name.label("product_name"),
                Product.sku.label("product_sku"),
                Warehouse.name.label("warehouse_name")
            )
            .join(Product, InventoryItem.product_id == Product.id)
            .join(Warehouse, InventoryItem.warehouse_id == Warehouse.id)
            .where(and_(*conditions))
            .order_by(Product.name)
            .offset(skip)
            .limit(limit)
        )

        # 执行查询
        result = await db.execute(query)
        items = result.all()

        # 获取总数
        count_query = select(func.count()).select_from(
            select(InventoryItem)
            .join(Product, InventoryItem.product_id == Product.id)
            .where(and_(*conditions))
            .subquery()
        )
        result = await db.execute(count_query)
        total = result.scalar_one()

        # 构建响应数据
        inventory_items = []
        for item in items:
            inventory_item = item.InventoryItem
            inventory_item_dict = {
                **inventory_item.__dict__,
                "product_name": item.product_name,
                "product_sku": item.product_sku,
                "warehouse_name": item.warehouse_name
            }
            inventory_items.append(inventory_item_dict)

        return {
            "success": True,
            "items": inventory_items,
            "total": total,
            "page": skip // limit + 1 if limit > 0 else 1,
            "size": limit,
            "pages": (total + limit - 1) // limit if limit > 0 else 1
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取库存项列表失败: {str(e)}"
        )


@router.get("/items/{item_id}", response_model=InventoryItemResponse)
async def get_inventory_item(
    item_id: uuid.UUID = Path(...),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    project_id: uuid.UUID = Depends(get_current_project_id)
):
    """
    获取单个库存项详情
    """
    try:
        # 构建查询
        query = (
            select(
                InventoryItem,
                Product.name.label("product_name"),
                Product.sku.label("product_sku"),
                Warehouse.name.label("warehouse_name")
            )
            .join(Product, InventoryItem.product_id == Product.id)
            .join(Warehouse, InventoryItem.warehouse_id == Warehouse.id)
            .where(
                InventoryItem.id == item_id,
                InventoryItem.project_id == project_id
            )
        )

        # 执行查询
        result = await db.execute(query)
        item = result.first()

        if not item:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="库存项不存在"
            )

        # 构建响应数据
        inventory_item = item.InventoryItem
        inventory_item_dict = {
            **inventory_item.__dict__,
            "product_name": item.product_name,
            "product_sku": item.product_sku,
            "warehouse_name": item.warehouse_name
        }

        return {
            "success": True,
            "data": inventory_item_dict
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取库存项详情失败: {str(e)}"
        )


@router.post("/items", response_model=InventoryItemResponse)
async def create_inventory_item(
    item: InventoryItemCreate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    project_id: uuid.UUID = Depends(get_current_project_id),
    tenant_id: Optional[uuid.UUID] = Depends(get_current_tenant_id)
):
    """
    创建库存项
    """
    try:
        # 检查产品是否存在
        product_query = select(Product).where(
            Product.id == item.product_id,
            Product.project_id == project_id
        )
        result = await db.execute(product_query)
        product = result.scalar_one_or_none()

        if not product:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="产品不存在"
            )

        # 检查仓库是否存在
        warehouse_query = select(Warehouse).where(
            Warehouse.id == item.warehouse_id,
            Warehouse.project_id == project_id
        )
        result = await db.execute(warehouse_query)
        warehouse = result.scalar_one_or_none()

        if not warehouse:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="仓库不存在"
            )

        # 检查是否已存在相同的库存项
        existing_query = select(InventoryItem).where(
            InventoryItem.product_id == item.product_id,
            InventoryItem.warehouse_id == item.warehouse_id,
            InventoryItem.project_id == project_id
        )
        result = await db.execute(existing_query)
        existing_item = result.scalar_one_or_none()

        if existing_item:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="该产品在此仓库中已有库存记录"
            )

        # 创建新库存项
        new_item = InventoryItem(
            id=uuid.uuid4(),
            tenant_id=tenant_id,
            project_id=project_id,
            product_id=item.product_id,
            warehouse_id=item.warehouse_id,
            quantity=item.quantity,
            min_quantity=item.min_quantity,
            max_quantity=item.max_quantity,
            is_slow_moving=item.is_slow_moving,
            last_movement_date=datetime.now() if item.quantity > 0 else None,
            created_by=current_user.id,
            updated_by=current_user.id
        )

        db.add(new_item)
        await db.commit()
        await db.refresh(new_item)

        # 如果初始库存大于0，创建历史记录
        if item.quantity > 0:
            history = InventoryHistory(
                id=uuid.uuid4(),
                inventory_item_id=new_item.id,
                tenant_id=tenant_id,
                project_id=project_id,
                action_type="add",
                previous_quantity=0,
                new_quantity=item.quantity,
                change_amount=item.quantity,
                notes="初始库存",
                created_by=current_user.id
            )
            db.add(history)
            await db.commit()

        # 构建响应数据
        query = (
            select(
                InventoryItem,
                Product.name.label("product_name"),
                Product.sku.label("product_sku"),
                Warehouse.name.label("warehouse_name")
            )
            .join(Product, InventoryItem.product_id == Product.id)
            .join(Warehouse, InventoryItem.warehouse_id == Warehouse.id)
            .where(InventoryItem.id == new_item.id)
        )

        result = await db.execute(query)
        item = result.first()

        inventory_item = item.InventoryItem
        inventory_item_dict = {
            **inventory_item.__dict__,
            "product_name": item.product_name,
            "product_sku": item.product_sku,
            "warehouse_name": item.warehouse_name
        }

        return {
            "success": True,
            "message": "库存项创建成功",
            "data": inventory_item_dict
        }
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建库存项失败: {str(e)}"
        )


@router.put("/items/{item_id}", response_model=InventoryItemResponse)
async def update_inventory_item(
    item_id: uuid.UUID,
    item_update: InventoryItemUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    project_id: uuid.UUID = Depends(get_current_project_id)
):
    """
    更新库存项
    """
    try:
        # 查询库存项
        query = select(InventoryItem).where(
            InventoryItem.id == item_id,
            InventoryItem.project_id == project_id
        )
        result = await db.execute(query)
        existing_item = result.scalar_one_or_none()

        if not existing_item:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="库存项不存在"
            )

        # 检查是否更新了数量
        quantity_changed = item_update.quantity is not None and item_update.quantity != existing_item.quantity
        old_quantity = existing_item.quantity

        # 更新字段
        update_data = item_update.dict(exclude_unset=True)

        for key, value in update_data.items():
            setattr(existing_item, key, value)

        existing_item.updated_by = current_user.id
        existing_item.updated_at = datetime.now()

        # 如果数量变更，更新最后移动日期
        if quantity_changed:
            existing_item.last_movement_date = datetime.now()

        await db.commit()
        await db.refresh(existing_item)

        # 如果数量变更，创建历史记录
        if quantity_changed:
            history = InventoryHistory(
                id=uuid.uuid4(),
                inventory_item_id=existing_item.id,
                tenant_id=existing_item.tenant_id,
                project_id=existing_item.project_id,
                action_type="update",
                previous_quantity=old_quantity,
                new_quantity=existing_item.quantity,
                change_amount=existing_item.quantity - old_quantity,
                notes="手动更新库存",
                created_by=current_user.id
            )
            db.add(history)
            await db.commit()

        # 构建响应数据
        query = (
            select(
                InventoryItem,
                Product.name.label("product_name"),
                Product.sku.label("product_sku"),
                Warehouse.name.label("warehouse_name")
            )
            .join(Product, InventoryItem.product_id == Product.id)
            .join(Warehouse, InventoryItem.warehouse_id == Warehouse.id)
            .where(InventoryItem.id == item_id)
        )

        result = await db.execute(query)
        item = result.first()

        inventory_item = item.InventoryItem
        inventory_item_dict = {
            **inventory_item.__dict__,
            "product_name": item.product_name,
            "product_sku": item.product_sku,
            "warehouse_name": item.warehouse_name
        }

        return {
            "success": True,
            "message": "库存项更新成功",
            "data": inventory_item_dict
        }
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新库存项失败: {str(e)}"
        )


@router.delete("/items/{item_id}", response_model=InventoryItemResponse)
async def delete_inventory_item(
    item_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    project_id: uuid.UUID = Depends(get_current_project_id)
):
    """
    删除库存项
    """
    try:
        # 查询库存项
        query = select(InventoryItem).where(
            InventoryItem.id == item_id,
            InventoryItem.project_id == project_id
        )
        result = await db.execute(query)
        item = result.scalar_one_or_none()

        if not item:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="库存项不存在"
            )

        # 删除库存项
        await db.delete(item)
        await db.commit()

        return {
            "success": True,
            "message": "库存项删除成功"
        }
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除库存项失败: {str(e)}"
        )


@router.get("/items/{item_id}/history", response_model=InventoryHistoryListResponse)
async def get_inventory_history(
    item_id: uuid.UUID,
    skip: int = 0,
    limit: int = 100,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    project_id: uuid.UUID = Depends(get_current_project_id)
):
    """
    获取库存项历史记录
    """
    try:
        # 检查库存项是否存在
        item_query = select(InventoryItem).where(
            InventoryItem.id == item_id,
            InventoryItem.project_id == project_id
        )
        result = await db.execute(item_query)
        item = result.scalar_one_or_none()

        if not item:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="库存项不存在"
            )

        # 查询历史记录
        query = (
            select(
                InventoryHistory,
                User.name.label("operator_name")
            )
            .outerjoin(User, InventoryHistory.created_by == User.id)
            .where(InventoryHistory.inventory_item_id == item_id)
            .order_by(InventoryHistory.created_at.desc())
            .offset(skip)
            .limit(limit)
        )

        result = await db.execute(query)
        history_items = result.all()

        # 获取总数
        count_query = select(func.count()).where(
            InventoryHistory.inventory_item_id == item_id
        )
        result = await db.execute(count_query)
        total = result.scalar_one()

        # 构建响应数据
        history_list = []
        for history in history_items:
            history_item = history.InventoryHistory
            history_dict = {
                **history_item.__dict__,
                "operator_name": history.operator_name
            }
            history_list.append(history_dict)

        return {
            "success": True,
            "history": history_list,
            "total": total,
            "page": skip // limit + 1 if limit > 0 else 1,
            "size": limit,
            "pages": (total + limit - 1) // limit if limit > 0 else 1
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取库存历史记录失败: {str(e)}"
        )


# 库存导入相关API
@router.post("/upload", response_model=InventoryImportResponse)
async def upload_inventory_sheet(
    file: UploadFile = File(...),
    warehouse_id: uuid.UUID = Form(...),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    project_id: uuid.UUID = Depends(get_current_project_id),
    tenant_id: Optional[uuid.UUID] = Depends(get_current_tenant_id)
):
    """
    上传库存表格
    """
    try:
        # 检查仓库是否存在
        warehouse_query = select(Warehouse).where(
            Warehouse.id == warehouse_id,
            Warehouse.project_id == project_id
        )
        result = await db.execute(warehouse_query)
        warehouse = result.scalar_one_or_none()

        if not warehouse:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="仓库不存在"
            )

        # 读取Excel文件
        content = await file.read()

        if file.filename.endswith('.xlsx') or file.filename.endswith('.xls'):
            df = pd.read_excel(io.BytesIO(content))
        elif file.filename.endswith('.csv'):
            df = pd.read_csv(io.BytesIO(content))
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="不支持的文件格式，请上传.xlsx、.xls或.csv文件"
            )

        # 检查必要的列
        required_columns = ['商品名称', '商品编码', '当前库存', '最低库存', '单位']
        missing_columns = [col for col in required_columns if col not in df.columns]

        if missing_columns:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"缺少必要的列: {', '.join(missing_columns)}"
            )

        # 获取所有产品
        product_query = select(Product).where(Product.project_id == project_id)
        result = await db.execute(product_query)
        products = {p.sku: p for p in result.scalars().all()}

        # 获取现有库存项
        inventory_query = select(InventoryItem).where(
            InventoryItem.warehouse_id == warehouse_id,
            InventoryItem.project_id == project_id
        )
        result = await db.execute(inventory_query)
        existing_items = {item.product_id: item for item in result.scalars().all()}

        # 处理数据
        preview_items = []
        new_count = 0
        update_count = 0
        error_count = 0

        for _, row in df.iterrows():
            try:
                item = {
                    'name': str(row['商品名称']),
                    'code': str(row['商品编码']),
                    'category': str(row.get('分类', '')),
                    'current_stock': int(row['当前库存']),
                    'min_stock': int(row['最低库存']),
                    'unit': str(row['单位'])
                }

                # 检查产品是否存在
                product = products.get(item['code'])

                if not product:
                    item['status'] = 'error'
                    item['error_message'] = '商品编码不存在'
                    error_count += 1
                else:
                    # 检查是否已有库存记录
                    if product.id in existing_items:
                        item['status'] = 'update'
                        update_count += 1
                    else:
                        item['status'] = 'new'
                        new_count += 1

                preview_items.append(item)
            except Exception as e:
                preview_items.append({
                    'name': str(row.get('商品名称', '')),
                    'code': str(row.get('商品编码', '')),
                    'category': str(row.get('分类', '')),
                    'current_stock': 0,
                    'min_stock': 0,
                    'unit': str(row.get('单位', '')),
                    'status': 'error',
                    'error_message': str(e)
                })
                error_count += 1

        # 保存预览数据到会话
        session_data = {
            'warehouse_id': str(warehouse_id),
            'preview_items': preview_items,
            'products': {sku: str(p.id) for sku, p in products.items()},
            'existing_items': {str(pid): str(item.id) for pid, item in existing_items.items()}
        }

        # 在实际应用中，应该使用Redis或其他缓存存储会话数据
        # 这里简化处理，将数据存储在数据库中的临时表或配置中
        # 创建或更新临时导入配置
        import_config_query = select(SystemConfig).where(
            SystemConfig.project_id == project_id,
            SystemConfig.key == f"inventory_import_{current_user.id}"
        )
        result = await db.execute(import_config_query)
        import_config = result.scalar_one_or_none()

        if import_config:
            import_config.value = json.dumps(session_data)
            import_config.updated_at = datetime.now()
        else:
            from models.system_config import SystemConfig
            import_config = SystemConfig(
                id=uuid.uuid4(),
                tenant_id=tenant_id,
                project_id=project_id,
                key=f"inventory_import_{current_user.id}",
                value=json.dumps(session_data),
                created_by=current_user.id
            )
            db.add(import_config)

        await db.commit()

        # 返回预览数据
        return {
            "success": True,
            "message": "库存表上传成功",
            "data": {
                "preview": preview_items[:100],  # 限制预览数量
                "total": len(preview_items),
                "new_items": new_count,
                "updated_items": update_count,
                "errors": error_count
            }
        }
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"上传库存表失败: {str(e)}"
        )


@router.post("/confirm-import", response_model=InventoryImportConfirmResponse)
async def confirm_inventory_import(
    data: InventoryImportConfirm,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    project_id: uuid.UUID = Depends(get_current_project_id),
    tenant_id: Optional[uuid.UUID] = Depends(get_current_tenant_id)
):
    """
    确认导入库存表
    """
    try:
        # 获取导入配置
        from models.system_config import SystemConfig
        import_config_query = select(SystemConfig).where(
            SystemConfig.project_id == project_id,
            SystemConfig.key == f"inventory_import_{current_user.id}"
        )
        result = await db.execute(import_config_query)
        import_config = result.scalar_one_or_none()

        if not import_config:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="没有找到导入数据，请先上传库存表"
            )

        # 解析导入数据
        session_data = json.loads(import_config.value)

        # 检查仓库ID是否匹配
        if str(data.warehouse_id) != session_data['warehouse_id']:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="仓库ID不匹配，请重新上传库存表"
            )

        # 获取产品和现有库存项映射
        products_map = {sku: uuid.UUID(pid) for sku, pid in session_data['products'].items()}
        existing_items_map = {uuid.UUID(pid): uuid.UUID(iid) for pid, iid in session_data['existing_items'].items()}

        # 处理导入数据
        imported_count = 0
        now = datetime.now()

        for item_data in session_data['preview_items']:
            if item_data['status'] == 'error':
                continue

            product = products_map.get(item_data['code'])
            if not product:
                continue

            if item_data['status'] == 'update':
                # 更新现有库存项
                inventory_id = existing_items_map.get(product)
                if not inventory_id:
                    continue

                # 查询现有库存项
                item_query = select(InventoryItem).where(InventoryItem.id == inventory_id)
                result = await db.execute(item_query)
                inventory_item = result.scalar_one_or_none()

                if not inventory_item:
                    continue

                # 记录旧数量
                old_quantity = inventory_item.quantity
                new_quantity = item_data['current_stock']

                # 更新库存项
                inventory_item.quantity = new_quantity
                inventory_item.min_quantity = item_data['min_stock']
                inventory_item.updated_by = current_user.id
                inventory_item.updated_at = now

                if old_quantity != new_quantity:
                    inventory_item.last_movement_date = now

                # 创建历史记录
                if old_quantity != new_quantity:
                    history = InventoryHistory(
                        id=uuid.uuid4(),
                        inventory_item_id=inventory_item.id,
                        tenant_id=tenant_id,
                        project_id=project_id,
                        action_type="import",
                        previous_quantity=old_quantity,
                        new_quantity=new_quantity,
                        change_amount=new_quantity - old_quantity,
                        notes="导入更新",
                        created_by=current_user.id
                    )
                    db.add(history)

                imported_count += 1

            elif item_data['status'] == 'new':
                # 创建新库存项
                new_item = InventoryItem(
                    id=uuid.uuid4(),
                    tenant_id=tenant_id,
                    project_id=project_id,
                    product_id=product,
                    warehouse_id=uuid.UUID(session_data['warehouse_id']),
                    quantity=item_data['current_stock'],
                    min_quantity=item_data['min_stock'],
                    is_slow_moving=False,
                    last_movement_date=now if item_data['current_stock'] > 0 else None,
                    created_by=current_user.id,
                    updated_by=current_user.id
                )

                db.add(new_item)

                # 创建历史记录
                if item_data['current_stock'] > 0:
                    history = InventoryHistory(
                        id=uuid.uuid4(),
                        inventory_item_id=new_item.id,
                        tenant_id=tenant_id,
                        project_id=project_id,
                        action_type="import",
                        previous_quantity=0,
                        new_quantity=item_data['current_stock'],
                        change_amount=item_data['current_stock'],
                        notes="导入新增",
                        created_by=current_user.id
                    )
                    db.add(history)

                imported_count += 1

        # 提交事务
        await db.commit()

        # 删除导入配置
        await db.delete(import_config)
        await db.commit()

        return {
            "success": True,
            "message": "库存导入成功",
            "imported_count": imported_count
        }
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"确认导入库存表失败: {str(e)}"
        )


@router.post("/cancel-import", response_model=dict)
async def cancel_inventory_import(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    project_id: uuid.UUID = Depends(get_current_project_id)
):
    """
    取消导入库存表
    """
    try:
        # 获取导入配置
        from models.system_config import SystemConfig
        import_config_query = select(SystemConfig).where(
            SystemConfig.project_id == project_id,
            SystemConfig.key == f"inventory_import_{current_user.id}"
        )
        result = await db.execute(import_config_query)
        import_config = result.scalar_one_or_none()

        if import_config:
            # 删除导入配置
            await db.delete(import_config)
            await db.commit()

        return {
            "success": True,
            "message": "已取消导入"
        }
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"取消导入库存表失败: {str(e)}"
        )


@router.get("/template", response_model=None)
async def get_inventory_template():
    """
    获取库存表模板
    """
    try:
        # 创建Excel文件
        df = pd.DataFrame({
            '商品名称': ['示例商品1', '示例商品2'],
            '商品编码': ['SKU001', 'SKU002'],
            '分类': ['食品', '饮料'],
            '当前库存': [100, 50],
            '最低库存': [20, 10],
            '单位': ['个', '箱']
        })

        # 创建内存中的Excel文件
        output = io.BytesIO()
        df.to_excel(output, sheet_name='库存模板', index=False)

        # 返回Excel文件
        from fastapi.responses import Response
        output.seek(0)

        headers = {
            'Content-Disposition': 'attachment; filename="inventory_template.xlsx"'
        }

        return Response(
            content=output.getvalue(),
            media_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            headers=headers
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取库存表模板失败: {str(e)}"
        )
