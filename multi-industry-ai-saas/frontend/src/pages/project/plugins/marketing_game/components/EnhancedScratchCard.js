import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  Button,
  message,
  Modal,
  Typography,
  Card,
  Row,
  Col,
  Statistic,
  Badge,
  Progress,
  Space,
  Tag,
  Tooltip,
  Alert,
  Spin,
  Empty,
  Avatar,
  Divider,
  Switch,
  Select,
  Form,
  Input,
  notification,
  Slider
} from 'antd';
import {
  TrophyOutlined,
  GiftOutlined,
  SoundOutlined,
  SoundFilled,
  PlayCircleOutlined,
  StarOutlined,
  FireOutlined,
  ThunderboltOutlined,
  HeartOutlined,
  SmileOutlined,
  CrownOutlined,
  SettingOutlined,
  HistoryOutlined,
  UserOutlined,
  ReloadOutlined,
  ClockCircleOutlined,
  RedoOutlined,
  CheckCircleOutlined,
  EyeOutlined
} from '@ant-design/icons';
import './EnhancedScratchCard.css';
import marketingGameApi from '../service/api/marketingGame';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;

// 主题配置
const THEMES = {
  classic: {
    name: '经典风格',
    primaryColor: '#1890ff',
    secondaryColor: '#52c41a',
    bgGradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    scratchColor: '#c0c0c0',
    cardBg: 'rgba(255, 255, 255, 0.95)'
  },
  gold: {
    name: '黄金风格',
    primaryColor: '#faad14',
    secondaryColor: '#ff7a45',
    bgGradient: 'linear-gradient(135deg, #ffd700 0%, #ff8c00 100%)',
    scratchColor: '#ffd700',
    cardBg: 'rgba(255, 215, 0, 0.2)'
  },
  neon: {
    name: '霓虹风格',
    primaryColor: '#00BFFF',
    secondaryColor: '#39FF14',
    bgGradient: 'linear-gradient(135deg, #0c0c0c 0%, #1a1a1a 100%)',
    scratchColor: '#333',
    cardBg: 'rgba(0, 0, 0, 0.8)'
  },
  rainbow: {
    name: '彩虹风格',
    primaryColor: '#ff6b6b',
    secondaryColor: '#4ecdc4',
    bgGradient: 'linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%)',
    scratchColor: '#ff6b6b',
    cardBg: 'rgba(255, 255, 255, 0.9)'
  }
};

// 刮奖图案
const SCRATCH_PATTERNS = {
  silver: { name: '银色涂层', pattern: 'silver' },
  gold: { name: '金色涂层', pattern: 'gold' },
  rainbow: { name: '彩虹涂层', pattern: 'rainbow' },
  dots: { name: '圆点图案', pattern: 'dots' },
  lines: { name: '线条图案', pattern: 'lines' }
};

const EnhancedScratchCard = ({ gameId, embedded = false }) => {
  // 基础状态
  const [loading, setLoading] = useState(true);
  const [game, setGame] = useState(null);
  const [prizes, setPrizes] = useState([]);
  const [scratching, setScratching] = useState(false);
  const [canScratch, setCanScratch] = useState(true);
  const [remainingDraws, setRemainingDraws] = useState(0);
  
  // 界面状态
  const [currentTheme, setCurrentTheme] = useState('classic');
  const [soundEnabled, setSoundEnabled] = useState(true);
  const [scratchPattern, setScratchPattern] = useState('silver');
  const [brushSize, setBrushSize] = useState(20);
  const [showStats, setShowStats] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  
  // 游戏状态
  const [winningPrize, setWinningPrize] = useState(null);
  const [showResult, setShowResult] = useState(false);
  const [totalSpins, setTotalSpins] = useState(0);
  const [winCount, setWinCount] = useState(0);
  const [lastWinTime, setLastWinTime] = useState(null);
  const [winStreak, setWinStreak] = useState(0);
  
  // 刮卡状态
  const [isScratching, setIsScratching] = useState(false);
  const [scratchProgress, setScratchProgress] = useState(0);
  const [cardRevealed, setCardRevealed] = useState(false);
  const [hasStartedScratching, setHasStartedScratching] = useState(false);
  
  // 验证状态
  const [needVerification, setNeedVerification] = useState(false);
  const [verified, setVerified] = useState(false);
  
  // 引用
  const canvasRef = useRef(null);
  const overlayCanvasRef = useRef(null);
  const audioRef = useRef(null);
  
  // 音频引用
  const scratchSoundRef = useRef(null);
  const winSoundRef = useRef(null);
  const loseSoundRef = useRef(null);
  const clickSoundRef = useRef(null);

  // 初始化音频
  useEffect(() => {
    if (soundEnabled) {
      try {
        scratchSoundRef.current = new Audio('/sounds/scratch.mp3');
        winSoundRef.current = new Audio('/sounds/win-celebration.mp3');
        loseSoundRef.current = new Audio('/sounds/try-again.mp3');
        clickSoundRef.current = new Audio('/sounds/click.mp3');
        
        [scratchSoundRef, winSoundRef, loseSoundRef, clickSoundRef].forEach(ref => {
          if (ref.current) {
            ref.current.volume = 0.3;
            ref.current.preload = 'auto';
          }
        });
      } catch (error) {
        console.log('音频加载失败:', error);
      }
    }
  }, [soundEnabled]);

  // 播放音效
  const playSound = useCallback((type) => {
    if (!soundEnabled) return;
    
    try {
      let audio = null;
      switch (type) {
        case 'scratch':
          audio = scratchSoundRef.current;
          break;
        case 'win':
          audio = winSoundRef.current;
          break;
        case 'lose':
          audio = loseSoundRef.current;
          break;
        case 'click':
          audio = clickSoundRef.current;
          break;
        default:
          return;
      }
      
      if (audio) {
        audio.currentTime = 0;
        audio.play().catch(e => console.log('音频播放失败:', e));
      }
    } catch (error) {
      console.log('播放音效失败:', error);
    }
  }, [soundEnabled]);

  // 获取游戏数据
  const fetchGameData = useCallback(async () => {
    try {
      setLoading(true);
      
      const gameResponse = await marketingGameApi.getGameDetail(gameId);
      if (!gameResponse.success) {
        message.error('获取游戏详情失败');
        return;
      }
      
      const gameData = gameResponse.data;
      setGame(gameData);
      setRemainingDraws(gameData.remaining_draws || 0);
      
      const prizesResponse = await marketingGameApi.getGamePrizes(gameId);
      if (prizesResponse.success) {
        setPrizes(prizesResponse.data || []);
      }
      
      await fetchUserStats();
      
      const needVerify = gameData.min_consumption > 0 || gameData.settings?.enable_receipt_verification;
      setNeedVerification(needVerify);
      
    } catch (error) {
      console.error('获取游戏数据失败:', error);
      message.error('获取游戏数据失败');
    } finally {
      setLoading(false);
    }
  }, [gameId]);

  // 获取用户统计
  const fetchUserStats = useCallback(async () => {
    try {
      const response = await marketingGameApi.getUserStats(gameId);
      if (response.success) {
        const stats = response.data;
        setTotalSpins(stats.total_spins || 0);
        setWinCount(stats.win_count || 0);
        setLastWinTime(stats.last_win_time);
        setWinStreak(stats.win_streak || 0);
      }
    } catch (error) {
      console.error('获取用户统计失败:', error);
    }
  }, [gameId]);

  // 初始化
  useEffect(() => {
    fetchGameData();
  }, [fetchGameData]);

  // 初始化画布
  useEffect(() => {
    if (canvasRef.current && overlayCanvasRef.current) {
      initCanvas();
    }
  }, [prizes, currentTheme, scratchPattern]);

  // 初始化画布
  const initCanvas = () => {
    const canvas = canvasRef.current;
    const overlayCanvas = overlayCanvasRef.current;
    
    if (!canvas || !overlayCanvas) return;
    
    const ctx = canvas.getContext('2d');
    const overlayCtx = overlayCanvas.getContext('2d');
    
    // 设置画布尺寸
    const width = 400;
    const height = 300;
    
    canvas.width = width;
    canvas.height = height;
    overlayCanvas.width = width;
    overlayCanvas.height = height;
    
    // 绘制背景内容
    drawBackground(ctx, width, height);
    
    // 绘制涂层
    drawScratchLayer(overlayCtx, width, height);
    
    // 重置状态
    setScratchProgress(0);
    setCardRevealed(false);
    setHasStartedScratching(false);
  };

  // 绘制背景内容
  const drawBackground = (ctx, width, height) => {
    const theme = THEMES[currentTheme];
    
    // 绘制背景渐变
    const gradient = ctx.createLinearGradient(0, 0, width, height);
    gradient.addColorStop(0, theme.primaryColor);
    gradient.addColorStop(1, theme.secondaryColor);
    
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, width, height);
    
    // 绘制装饰边框
    ctx.strokeStyle = '#fff';
    ctx.lineWidth = 4;
    ctx.strokeRect(10, 10, width - 20, height - 20);
    
    // 绘制奖品信息（如果已抽奖）
    if (winningPrize) {
      drawPrizeInfo(ctx, width, height);
    } else {
      drawPlaceholder(ctx, width, height);
    }
  };

  // 绘制奖品信息
  const drawPrizeInfo = (ctx, width, height) => {
    ctx.fillStyle = '#fff';
    ctx.font = 'bold 24px Arial';
    ctx.textAlign = 'center';
    
    if (winningPrize) {
      ctx.fillText('恭喜中奖！', width / 2, height / 2 - 40);
      ctx.font = 'bold 18px Arial';
      ctx.fillText(winningPrize.name, width / 2, height / 2);
      ctx.font = '14px Arial';
      ctx.fillText(`价值：¥${winningPrize.value}`, width / 2, height / 2 + 30);
    } else {
      ctx.fillText('很遗憾，未中奖', width / 2, height / 2);
      ctx.font = '14px Arial';
      ctx.fillText('谢谢参与！', width / 2, height / 2 + 30);
    }
  };

  // 绘制占位符
  const drawPlaceholder = (ctx, width, height) => {
    ctx.fillStyle = '#fff';
    ctx.font = 'bold 20px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('刮开此区域', width / 2, height / 2 - 20);
    ctx.font = '16px Arial';
    ctx.fillText('查看中奖结果', width / 2, height / 2 + 10);
  };

  // 绘制刮奖涂层
  const drawScratchLayer = (ctx, width, height) => {
    const theme = THEMES[currentTheme];
    
    switch (scratchPattern) {
      case 'silver':
        drawSilverLayer(ctx, width, height);
        break;
      case 'gold':
        drawGoldLayer(ctx, width, height);
        break;
      case 'rainbow':
        drawRainbowLayer(ctx, width, height);
        break;
      case 'dots':
        drawDotsLayer(ctx, width, height);
        break;
      case 'lines':
        drawLinesLayer(ctx, width, height);
        break;
      default:
        drawSilverLayer(ctx, width, height);
    }
  };

  // 银色涂层
  const drawSilverLayer = (ctx, width, height) => {
    const gradient = ctx.createLinearGradient(0, 0, width, height);
    gradient.addColorStop(0, '#e8e8e8');
    gradient.addColorStop(0.5, '#c0c0c0');
    gradient.addColorStop(1, '#a0a0a0');
    
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, width, height);
  };

  // 金色涂层
  const drawGoldLayer = (ctx, width, height) => {
    const gradient = ctx.createLinearGradient(0, 0, width, height);
    gradient.addColorStop(0, '#ffd700');
    gradient.addColorStop(0.5, '#ffb347');
    gradient.addColorStop(1, '#ff8c00');
    
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, width, height);
  };

  // 彩虹涂层
  const drawRainbowLayer = (ctx, width, height) => {
    const gradient = ctx.createLinearGradient(0, 0, width, 0);
    gradient.addColorStop(0, '#ff0000');
    gradient.addColorStop(0.16, '#ff8000');
    gradient.addColorStop(0.33, '#ffff00');
    gradient.addColorStop(0.5, '#00ff00');
    gradient.addColorStop(0.66, '#0080ff');
    gradient.addColorStop(0.83, '#8000ff');
    gradient.addColorStop(1, '#ff0080');
    
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, width, height);
  };

  // 圆点图案
  const drawDotsLayer = (ctx, width, height) => {
    ctx.fillStyle = '#c0c0c0';
    ctx.fillRect(0, 0, width, height);
    
    ctx.fillStyle = '#a0a0a0';
    for (let x = 10; x < width; x += 20) {
      for (let y = 10; y < height; y += 20) {
        ctx.beginPath();
        ctx.arc(x, y, 5, 0, Math.PI * 2);
        ctx.fill();
      }
    }
  };

  // 线条图案
  const drawLinesLayer = (ctx, width, height) => {
    ctx.fillStyle = '#c0c0c0';
    ctx.fillRect(0, 0, width, height);
    
    ctx.strokeStyle = '#a0a0a0';
    ctx.lineWidth = 2;
    for (let x = 0; x < width; x += 15) {
      ctx.beginPath();
      ctx.moveTo(x, 0);
      ctx.lineTo(x, height);
      ctx.stroke();
    }
  };

  // 开始刮奖
  const handleStartScratch = async () => {
    if (scratching || !canScratch) return;
    
    if (remainingDraws <= 0) {
      message.warning('您今日的抽奖次数已用完');
      return;
    }
    
    if (needVerification && !verified) {
      message.warning('请先完成身份验证');
      setShowSettings(true);
      return;
    }
    
    playSound('click');
    
    try {
      setScratching(true);
      setCanScratch(false);
      
      // 调用抽奖API
      const response = await marketingGameApi.drawPrize(gameId);
      
      if (response.success) {
        const result = response.data;
        
        if (result.prize) {
          setWinningPrize(result.prize);
          setWinCount(prev => prev + 1);
          setWinStreak(prev => prev + 1);
          setLastWinTime(new Date());
        } else {
          setWinningPrize(null);
          setWinStreak(0);
        }
        
        setTotalSpins(prev => prev + 1);
        setRemainingDraws(prev => prev - 1);
        
        // 重新绘制背景
        const canvas = canvasRef.current;
        if (canvas) {
          const ctx = canvas.getContext('2d');
          drawBackground(ctx, canvas.width, canvas.height);
        }
        
        await fetchUserStats();
        
      } else {
        message.error(response.message || '抽奖失败');
        playSound('lose');
      }
      
    } catch (error) {
      console.error('抽奖失败:', error);
      message.error('抽奖失败，请重试');
      playSound('lose');
    }
  };

  // 处理刮奖事件
  const handleScratch = useCallback((e) => {
    if (!scratching || cardRevealed) return;
    
    const overlayCanvas = overlayCanvasRef.current;
    if (!overlayCanvas) return;
    
    const rect = overlayCanvas.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;
    
    const ctx = overlayCanvas.getContext('2d');
    
    // 设置刮除模式
    ctx.globalCompositeOperation = 'destination-out';
    ctx.beginPath();
    ctx.arc(x, y, brushSize, 0, Math.PI * 2);
    ctx.fill();
    
    if (!hasStartedScratching) {
      setHasStartedScratching(true);
      playSound('scratch');
    }
    
    // 计算刮除进度
    const imageData = ctx.getImageData(0, 0, overlayCanvas.width, overlayCanvas.height);
    const pixels = imageData.data;
    let transparentPixels = 0;
    
    for (let i = 3; i < pixels.length; i += 4) {
      if (pixels[i] === 0) {
        transparentPixels++;
      }
    }
    
    const progress = (transparentPixels / (pixels.length / 4)) * 100;
    setScratchProgress(progress);
    
    // 如果刮除超过30%，显示结果
    if (progress > 30 && !cardRevealed) {
      setCardRevealed(true);
      setScratching(false);
      
      if (winningPrize) {
        playSound('win');
        notification.success({
          message: '🎉 恭喜中奖！',
          description: `您获得了：${winningPrize.name}`,
          duration: 5,
          placement: 'topRight'
        });
      } else {
        playSound('lose');
        notification.info({
          message: '很遗憾',
          description: '本次未中奖，再试一次吧！',
          duration: 3,
          placement: 'topRight'
        });
      }
      
      setTimeout(() => {
        setShowResult(true);
        setCanScratch(true);
      }, 1000);
    }
  }, [scratching, cardRevealed, brushSize, hasStartedScratching, winningPrize, playSound]);

  // 重新开始
  const handleRestart = () => {
    setWinningPrize(null);
    setCardRevealed(false);
    setHasStartedScratching(false);
    setScratchProgress(0);
    setShowResult(false);
    initCanvas();
  };

  // 揭示全部
  const handleRevealAll = () => {
    const overlayCanvas = overlayCanvasRef.current;
    if (!overlayCanvas) return;
    
    const ctx = overlayCanvas.getContext('2d');
    ctx.clearRect(0, 0, overlayCanvas.width, overlayCanvas.height);
    
    setCardRevealed(true);
    setScratchProgress(100);
    
    if (!hasStartedScratching) {
      handleStartScratch();
    }
  };

  // 渲染统计信息
  const renderStats = () => (
    <Row gutter={16}>
      <Col span={6}>
        <Card className="stat-card">
          <Statistic
            title="总刮奖次数"
            value={totalSpins}
            prefix={<PlayCircleOutlined />}
            valueStyle={{ color: THEMES[currentTheme].primaryColor }}
          />
        </Card>
      </Col>
      <Col span={6}>
        <Card className="stat-card">
          <Statistic
            title="中奖次数"
            value={winCount}
            prefix={<TrophyOutlined />}
            valueStyle={{ color: '#52c41a' }}
          />
        </Card>
      </Col>
      <Col span={6}>
        <Card className="stat-card">
          <Statistic
            title="中奖率"
            value={totalSpins > 0 ? ((winCount / totalSpins) * 100).toFixed(1) : 0}
            suffix="%"
            prefix={<StarOutlined />}
            valueStyle={{ color: '#faad14' }}
          />
        </Card>
      </Col>
      <Col span={6}>
        <Card className="stat-card">
          <Statistic
            title="连胜纪录"
            value={winStreak}
            prefix={<FireOutlined />}
            valueStyle={{ color: '#f5222d' }}
          />
        </Card>
      </Col>
    </Row>
  );

  // 渲染设置面板
  const renderSettings = () => (
    <Space direction="vertical" style={{ width: '100%' }}>
      <Card title="主题设置">
        <Select
          value={currentTheme}
          onChange={setCurrentTheme}
          style={{ width: '100%' }}
        >
          {Object.entries(THEMES).map(([key, theme]) => (
            <Option key={key} value={key}>
              {theme.name}
            </Option>
          ))}
        </Select>
      </Card>
      
      <Card title="涂层样式">
        <Select
          value={scratchPattern}
          onChange={setScratchPattern}
          style={{ width: '100%' }}
        >
          {Object.entries(SCRATCH_PATTERNS).map(([key, pattern]) => (
            <Option key={key} value={key}>
              {pattern.name}
            </Option>
          ))}
        </Select>
      </Card>
      
      <Card title="刮奖设置">
        <Space direction="vertical" style={{ width: '100%' }}>
          <div>
            <Text>画笔大小：</Text>
            <Slider
              value={brushSize}
              onChange={setBrushSize}
              min={10}
              max={50}
              style={{ width: '100%' }}
              tooltip={{ formatter: value => `${value}px` }}
            />
          </div>
        </Space>
      </Card>
      
      <Card title="音效设置">
        <Space>
          <Text>音效开关：</Text>
          <Switch
            checked={soundEnabled}
            onChange={setSoundEnabled}
            checkedChildren={<SoundFilled />}
            unCheckedChildren={<SoundOutlined />}
          />
        </Space>
      </Card>
      
      {needVerification && (
        <Card title="身份验证">
          <Space direction="vertical" style={{ width: '100%' }}>
            <Alert
              message="需要验证"
              description="参与刮奖需要完成身份验证"
              type="warning"
              showIcon
            />
            <Button 
              type="primary" 
              onClick={() => setVerified(true)}
            >
              完成验证
            </Button>
          </Space>
        </Card>
      )}
    </Space>
  );

  if (loading) {
    return (
      <div className="scratch-loading">
        <Spin size="large" />
        <Title level={4} style={{ marginTop: 16 }}>
          加载游戏中...
        </Title>
      </div>
    );
  }

  if (!game || !prizes.length) {
    return (
      <Empty
        description="游戏暂未配置或奖品为空"
        image={Empty.PRESENTED_IMAGE_SIMPLE}
      />
    );
  }

  const theme = THEMES[currentTheme];

  return (
    <div className={`enhanced-scratch-card theme-${currentTheme}`} style={{ background: theme.bgGradient }}>
      {/* 游戏头部 */}
      <div className="game-header">
        <Card className="header-card" style={{ background: theme.cardBg }}>
          <Row justify="space-between" align="middle">
            <Col>
              <Title level={3} style={{ margin: 0 }}>
                <GiftOutlined style={{ marginRight: 8, color: theme.primaryColor }} />
                {game.name}
              </Title>
              <Text>{game.description}</Text>
            </Col>
            <Col>
              <Space>
                <Badge count={remainingDraws} showZero>
                  <Button
                    type="primary"
                    size="large"
                    onClick={handleStartScratch}
                    loading={scratching}
                    disabled={!canScratch || remainingDraws <= 0 || hasStartedScratching}
                    className="scratch-button"
                    style={{ backgroundColor: theme.primaryColor, borderColor: theme.primaryColor }}
                  >
                    {scratching ? '抽奖中...' : hasStartedScratching ? '刮奖中...' : '开始刮奖'}
                  </Button>
                </Badge>
                <Tooltip title="游戏统计">
                  <Button
                    icon={<HistoryOutlined />}
                    onClick={() => setShowStats(!showStats)}
                  />
                </Tooltip>
                <Tooltip title="游戏设置">
                  <Button
                    icon={<SettingOutlined />}
                    onClick={() => setShowSettings(!showSettings)}
                  />
                </Tooltip>
              </Space>
            </Col>
          </Row>
        </Card>
      </div>

      {/* 统计信息 */}
      {showStats && (
        <div className="stats-section">
          {renderStats()}
        </div>
      )}

      {/* 主游戏区域 */}
      <div className="main-game-area">
        <Row gutter={24}>
          <Col span={showSettings ? 16 : 24}>
            <Card className="scratch-card" style={{ background: theme.cardBg }}>
              <div className="scratch-container">
                <div className="canvas-container">
                  <canvas
                    ref={canvasRef}
                    className="background-canvas"
                  />
                  <canvas
                    ref={overlayCanvasRef}
                    className="overlay-canvas"
                    onMouseMove={isScratching ? handleScratch : undefined}
                    onMouseDown={() => setIsScratching(true)}
                    onMouseUp={() => setIsScratching(false)}
                    onMouseLeave={() => setIsScratching(false)}
                    style={{ cursor: hasStartedScratching ? 'grabbing' : 'grab' }}
                  />
                </div>
                
                {/* 刮奖进度 */}
                <div className="scratch-progress">
                  <Progress
                    percent={Math.round(scratchProgress)}
                    strokeColor={theme.primaryColor}
                    format={(percent) => `已刮开 ${percent}%`}
                  />
                </div>
                
                {/* 操作按钮 */}
                <div className="scratch-controls">
                  <Space>
                    {hasStartedScratching && !cardRevealed && (
                      <Button
                        icon={<EyeOutlined />}
                        onClick={handleRevealAll}
                        type="default"
                      >
                        揭示全部
                      </Button>
                    )}
                    {cardRevealed && (
                      <Button
                        icon={<RedoOutlined />}
                        onClick={handleRestart}
                        type="primary"
                        disabled={remainingDraws <= 0}
                      >
                        再来一次
                      </Button>
                    )}
                  </Space>
                </div>
              </div>
              
              {/* 奖品展示 */}
              <div className="prizes-display">
                <Title level={5}>
                  <TrophyOutlined style={{ color: theme.primaryColor }} /> 丰厚奖品等你来拿
                </Title>
                <Row gutter={8}>
                  {prizes.slice(0, 6).map((prize, index) => (
                    <Col span={4} key={prize.id}>
                      <Card size="small" className="prize-card">
                        <Avatar
                          src={prize.image_url}
                          icon={<GiftOutlined />}
                          size={32}
                        />
                        <Text className="prize-name">{prize.name}</Text>
                      </Card>
                    </Col>
                  ))}
                </Row>
              </div>
            </Card>
          </Col>

          {/* 设置面板 */}
          {showSettings && (
            <Col span={8}>
              <Card title="游戏设置" className="settings-card" style={{ background: theme.cardBg }}>
                {renderSettings()}
              </Card>
            </Col>
          )}
        </Row>
      </div>

      {/* 结果模态框 */}
      <Modal
        open={showResult}
        onCancel={() => setShowResult(false)}
        footer={null}
        centered
        className="result-modal"
        width={480}
      >
        <div className="result-content">
          {winningPrize ? (
            <div className="win-result">
              <div className="celebration-icon">
                <CrownOutlined style={{ fontSize: 64, color: '#faad14' }} />
              </div>
              <Title level={2} style={{ color: '#52c41a', margin: '16px 0' }}>
                🎉 恭喜中奖！
              </Title>
              <Card className="prize-info-card">
                <Avatar
                  src={winningPrize.image_url}
                  icon={<GiftOutlined />}
                  size={80}
                />
                <Title level={4} style={{ margin: '16px 0 8px' }}>
                  {winningPrize.name}
                </Title>
                <Text type="secondary">{winningPrize.description}</Text>
                {winningPrize.value > 0 && (
                  <Tag color="gold" style={{ marginTop: 8 }}>
                    价值 ¥{winningPrize.value}
                  </Tag>
                )}
              </Card>
            </div>
          ) : (
            <div className="lose-result">
              <div className="comfort-icon">
                <SmileOutlined style={{ fontSize: 64, color: '#1890ff' }} />
              </div>
              <Title level={3} style={{ margin: '16px 0' }}>
                再接再厉！
              </Title>
              <Paragraph style={{ textAlign: 'center' }}>
                很遗憾本次未中奖，不要气馁，下次一定会有好运气的！
              </Paragraph>
            </div>
          )}
          
          <div className="result-actions">
            <Space size="large">
              <Button onClick={() => setShowResult(false)}>
                关闭
              </Button>
              {remainingDraws > 0 && (
                <Button
                  type="primary"
                  icon={<ReloadOutlined />}
                  onClick={() => {
                    setShowResult(false);
                    setTimeout(handleRestart, 500);
                  }}
                >
                  再来一次
                </Button>
              )}
            </Space>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default EnhancedScratchCard; 