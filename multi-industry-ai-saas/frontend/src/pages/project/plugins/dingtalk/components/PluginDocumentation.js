import React, { useState } from 'react';
import {
  Card, Tabs, Typography, Alert, Button, Space, Tag, Divider,
  Steps, Collapse, Table, Row, Col, List, Image, Descriptions
} from 'antd';
import {
  InfoCircleOutlined, CheckCircleOutlined, RobotOutlined, TeamOutlined,
  FileTextOutlined, AuditOutlined, CalendarOutlined, SettingOutlined,
  CodeOutlined, ApiOutlined, ExperimentOutlined, ShopOutlined,
  DingdingOutlined, WarningOutlined, BookOutlined, BugOutlined,
  SafetyOutlined, ThunderboltOutlined, LinkOutlined, UserOutlined
} from '@ant-design/icons';

const { Title, Paragraph, Text } = Typography;
const { TabPane } = Tabs;
const { Step } = Steps;
const { Panel } = Collapse;

/**
 * 钉钉插件完整文档组件
 * 整合插件介绍、配置指南、API文档、使用示例和故障排除
 */
const PluginDocumentation = () => {
  const [activeTab, setActiveTab] = useState('overview');

  // 核心功能列表
  const coreFeatures = [
    {
      icon: <RobotOutlined style={{ color: '#1890ff' }} />,
      title: 'AI智能对话',
      description: '集成AI大模型，支持智能客服、知识库检索、多模态对话等功能'
    },
    {
      icon: <ApiOutlined style={{ color: '#52c41a' }} />,
      title: '事件订阅管理',
      description: '统一管理机器人通知和事件订阅，支持多种事件类型和实时处理'
    },
    {
      icon: <TeamOutlined style={{ color: '#722ed1' }} />,
      title: '组织架构管理',
      description: '完整的企业组织架构同步，支持多级部门结构和用户管理'
    },
    {
      icon: <FileTextOutlined style={{ color: '#fa8c16' }} />,
      title: '文档知识库管理',
      description: '双向同步钉钉知识库，支持多种同步策略和文档上传'
    },
    {
      icon: <CalendarOutlined style={{ color: '#eb2f96' }} />,
      title: '待办任务管理',
      description: '项目任务与钉钉待办的双向同步，支持任务创建、更新、完成等操作'
    },
    {
      icon: <AuditOutlined style={{ color: '#13c2c2' }} />,
      title: '审批流程管理',
      description: '可视化审批模板设计，支持复杂表单组件和完整的审批流程管理'
    },
    {
      icon: <UserOutlined style={{ color: '#f5222d' }} />,
      title: '用户管理',
      description: '智能unionId管理，自动用户绑定，支持用户信息同步和权限控制'
    }
  ];

  // 业务审批类型
  const businessApprovalTypes = [
    {
      key: 'store_loss',
      name: '门店报损',
      description: '处理门店商品报损申请的审批流程',
      fields: [
        { name: '门店名称', required: true, type: 'string' },
        { name: '产品名称', required: true, type: 'string' },
        { name: '产品编码', required: true, type: 'string' },
        { name: '报损数量', required: true, type: 'number' },
        { name: '单价', required: true, type: 'decimal' },
        { name: '报损原因', required: true, type: 'enum' },
        { name: '报损说明', required: false, type: 'text' },
        { name: '报损凭证', required: false, type: 'file' }
      ]
    },
    {
      key: 'sales_report',
      name: '销售业绩上报',
      description: '处理门店销售业绩数据上报的审批流程',
      fields: [
        { name: '门店名称', required: true, type: 'string' },
        { name: '报告日期', required: true, type: 'date' },
        { name: '总销售额', required: true, type: 'decimal' },
        { name: '客流量', required: true, type: 'number' },
        { name: '客单价', required: true, type: 'decimal' },
        { name: '热销产品', required: false, type: 'text' },
        { name: '业绩总结', required: false, type: 'text' }
      ]
    },
    {
      key: 'expense_reimbursement',
      name: '费用报销',
      description: '处理门店日常费用报销申请的审批流程',
      fields: [
        { name: '门店名称', required: true, type: 'string' },
        { name: '费用日期', required: true, type: 'date' },
        { name: '费用类型', required: true, type: 'enum' },
        { name: '报销金额', required: true, type: 'decimal' },
        { name: '费用说明', required: true, type: 'text' },
        { name: '供应商/商家', required: false, type: 'string' },
        { name: '发票凭证', required: true, type: 'file' }
      ]
    }
  ];

  // 配置步骤
  const configSteps = [
    {
      title: '注册钉钉开放平台账号',
      description: '访问钉钉开放平台并注册开发者账号'
    },
    {
      title: '创建企业内部应用',
      description: '在钉钉开放平台创建企业内部应用'
    },
    {
      title: '配置应用权限',
      description: '为应用配置必要的API权限'
    },
    {
      title: '获取应用凭证',
      description: '获取AppKey和AppSecret'
    },
    {
      title: '配置到系统',
      description: '在系统设置中配置钉钉应用信息'
    }
  ];

  // 必需权限
  const requiredPermissions = [
    { permission: '通讯录管理', description: '读取企业通讯录信息，同步组织架构' },
    { permission: '审批', description: '创建和管理审批流程' },
    { permission: '待办任务', description: '创建和管理待办任务' },
    { permission: '钉盘', description: '访问和同步钉钉文档' },
    { permission: '群管理', description: '管理企业群聊' }
  ];

  // API接口列表
  const apiEndpoints = [
    {
      method: 'GET',
      path: '/api/v1/project/{project_id}/plugin/dingtalk/webhooks/subscriptions',
      description: '获取Webhook列表',
      params: [],
      response: '{ success: boolean, data: array }'
    },
    {
      method: 'POST',
      path: '/api/v1/project/{project_id}/plugin/dingtalk/webhooks/subscriptions',
      description: '创建Webhook',
      params: ['webhook_data: object'],
      response: '{ success: boolean, data: object }'
    },
    {
      method: 'GET',
      path: '/api/v1/project/{project_id}/plugin/dingtalk/admin/organization-status',
      description: '获取组织架构状态',
      params: [],
      response: '{ success: boolean, data: object }'
    },
    {
      method: 'GET',
      path: '/api/v1/project/{project_id}/plugin/dingtalk/admin/spaces',
      description: '获取知识库列表',
      params: [],
      response: '{ success: boolean, data: array }'
    },
    {
      method: 'POST',
      path: '/api/v1/project/{project_id}/plugin/dingtalk/ai/chat-json',
      description: 'AI聊天对话',
      params: ['message: string', 'context?: object'],
      response: '{ success: boolean, response: string }'
    },
    {
      method: 'GET',
      path: '/api/v1/project/{project_id}/plugin/dingtalk/approval/templates/list',
      description: '获取审批模板列表',
      params: [],
      response: '{ success: boolean, data: array }'
    },
    {
      method: 'POST',
      path: '/api/v1/project/{project_id}/plugin/dingtalk/approval/templates',
      description: '创建/更新审批模板',
      params: ['template_data: object'],
      response: '{ success: boolean, process_code?: string }'
    }
  ];

  // 渲染功能概览
  const renderOverview = () => (
    <div>
      {/* 插件概述 */}
      <Card style={{ marginBottom: 24 }}>
        <div style={{ textAlign: 'center', marginBottom: 32 }}>
          <Space direction="vertical" size="large">
            <div style={{ fontSize: 64, color: '#1890ff' }}>
              <DingdingOutlined />
            </div>
            <Title level={2}>钉钉深度集成插件</Title>
            <Paragraph style={{ fontSize: 16, color: '#666' }}>
              全面集成钉钉开放平台能力，实现企业办公系统与钉钉的深度融合。
              支持AI智能对话、事件订阅管理、组织架构同步、文档管理、待办任务、审批流程等核心功能，
              采用基于角色的权限控制，为不同用户提供差异化功能体验。
            </Paragraph>
          </Space>
        </div>

        <Alert
          message="重要提示"
          description="使用本插件需要先在钉钉开放平台创建企业内部应用并获得相应权限。请确保您拥有企业管理员权限以完成配置。"
          type="info"
          showIcon
          style={{ marginBottom: 24 }}
        />
      </Card>

      {/* 核心功能 */}
      <Card title="核心功能" style={{ marginBottom: 24 }}>
        <Row gutter={[24, 24]}>
          {coreFeatures.map((feature, index) => (
            <Col xs={24} sm={12} lg={8} key={index}>
              <Card size="small" hoverable>
                <Space direction="vertical" style={{ width: '100%' }}>
                  <div style={{ fontSize: 32, textAlign: 'center' }}>
                    {feature.icon}
                  </div>
                  <Title level={5} style={{ textAlign: 'center', margin: 0 }}>
                    {feature.title}
                  </Title>
                  <Paragraph style={{ textAlign: 'center', margin: 0 }}>
                    {feature.description}
                  </Paragraph>
                </Space>
              </Card>
            </Col>
          ))}
        </Row>
      </Card>

      {/* 业务审批场景 */}
      <Card title="业务审批场景" style={{ marginBottom: 24 }}>
        <Alert
          message="钉钉业务审批深度集成"
          description="基于钉钉官方审批流程文档，深度集成钉钉审批功能到门店管理业务中，实现从审批发起到数据录入的全程自动化。"
          type="success"
          style={{ marginBottom: 16 }}
        />
        <List
          dataSource={businessApprovalTypes}
          renderItem={item => (
            <List.Item>
              <List.Item.Meta
                title={<Space><Tag color="blue">{item.name}</Tag></Space>}
                description={item.description}
              />
            </List.Item>
          )}
        />
      </Card>

      {/* 系统架构 */}
      <Card title="系统架构">
        <Steps direction="horizontal" current={-1}>
          <Step title="钉钉插件界面" description="用户发起审批" />
          <Step title="后端API" description="创建审批实例" />
          <Step title="钉钉处理" description="审批流程处理" />
          <Step title="状态回调" description="审批状态变更" />
          <Step title="业务集成" description="创建业务记录" />
        </Steps>
        <Divider />
        <Paragraph>
          整个流程实现了从用户在钉钉插件界面发起审批，到系统自动创建对应业务记录的完整闭环，
          确保了审批流程与业务系统的深度集成。
        </Paragraph>
      </Card>
    </div>
  );

  // 渲染配置指南
  const renderConfigGuide = () => (
    <div>
      <Alert
        message="配置指南"
        description="详细的钉钉插件配置步骤，帮助您快速完成插件的配置和部署。"
        type="info"
        style={{ marginBottom: 24 }}
      />

      {/* 配置步骤 */}
      <Card title="配置步骤" style={{ marginBottom: 24 }}>
        <Steps direction="vertical" current={-1}>
          {configSteps.map((step, index) => (
            <Step
              key={index}
              title={step.title}
              description={step.description}
            />
          ))}
        </Steps>
      </Card>

      {/* 详细配置说明 */}
      <Card title="详细配置说明">
        <Collapse>
          <Panel header="1. 钉钉开放平台准备" key="1">
            <Space direction="vertical" style={{ width: '100%' }}>
              <Title level={5}>步骤说明：</Title>
              <List
                size="small"
                dataSource={[
                  '访问钉钉开放平台：https://open.dingtalk.com',
                  '使用企业管理员钉钉账号登录',
                  '完成开发者认证（需要提供身份证等信息）',
                  '创建开发组织（如果还没有的话）'
                ]}
                renderItem={item => (
                  <List.Item>
                    <CheckCircleOutlined style={{ color: '#52c41a' }} /> {item}
                  </List.Item>
                )}
              />
            </Space>
          </Panel>

          <Panel header="2. 创建企业内部应用" key="2">
            <Space direction="vertical" style={{ width: '100%' }}>
              <Title level={5}>创建步骤：</Title>
              <List
                size="small"
                dataSource={[
                  '进入"应用开发" → "企业内部开发" → "小程序"',
                  '点击"创建应用"，选择"小程序"类型',
                  '填写应用基本信息（应用名称、应用描述等）',
                  '上传应用图标（建议使用系统相关的图标）',
                  '设置开发环境和发布环境配置'
                ]}
                renderItem={item => (
                  <List.Item>
                    <CheckCircleOutlined style={{ color: '#52c41a' }} /> {item}
                  </List.Item>
                )}
              />
            </Space>
          </Panel>

          <Panel header="3. 配置应用权限" key="3">
            <Space direction="vertical" style={{ width: '100%' }}>
              <Title level={5}>必需权限列表：</Title>
              <List
                size="small"
                dataSource={requiredPermissions}
                renderItem={item => (
                  <List.Item>
                    <Space>
                      <Tag color="blue">{item.permission}</Tag>
                      <Text>{item.description}</Text>
                    </Space>
                  </List.Item>
                )}
              />
              <Alert
                message="权限申请提示"
                description="部分权限需要企业管理员审批，请确保在申请时详细说明权限用途。"
                type="warning"
                showIcon
              />
            </Space>
          </Panel>

          <Panel header="4. 系统配置" key="4">
            <Space direction="vertical" style={{ width: '100%' }}>
              <Title level={5}>配置路径：</Title>
              <Paragraph>
                <Text code>项目设置 → 系统设置 → 第三方登录 → 钉钉配置</Text>
              </Paragraph>
              
              <Title level={5}>配置项说明：</Title>
              <Descriptions column={1} bordered size="small">
                <Descriptions.Item label="AppKey">
                  钉钉应用的AppKey，从钉钉开放平台获取
                </Descriptions.Item>
                <Descriptions.Item label="AppSecret">
                  钉钉应用的AppSecret，用于API调用认证
                </Descriptions.Item>
                <Descriptions.Item label="回调地址">
                  系统域名 + /api/auth/dingtalk/callback
                </Descriptions.Item>
              </Descriptions>
            </Space>
          </Panel>
        </Collapse>
      </Card>
    </div>
  );

  // 渲染事件订阅模式文档
  const renderEventSubscription = () => (
    <div>
      <Alert
        message="事件订阅模式选择"
        description="钉钉开放平台支持两种事件订阅模式，请根据您的部署环境选择合适的模式。"
        type="info"
        showIcon
        style={{ marginBottom: 24 }}
      />

      <Title level={3}>
        <SettingOutlined /> HTTP推送模式配置指引
      </Title>
      
      <Alert
        message="重要：钉钉开放平台配置"
        description="在创建事件订阅前，需要先在钉钉开放平台配置事件订阅参数。"
        type="warning"
        showIcon
        style={{ marginBottom: 24 }}
      />

      <Card title="配置步骤" style={{ marginBottom: 24 }}>
        <Steps direction="vertical" size="small">
          <Step
            title="登录钉钉开放平台"
            description={
              <div>
                <p>访问 <a href="https://open-dev.dingtalk.com/" target="_blank" rel="noopener noreferrer">钉钉开放平台</a> 并选择您的应用</p>
              </div>
            }
            status="process"
          />
          <Step
            title="配置事件订阅"
            description={
              <div>
                <p>在应用管理页面，找到"事件订阅"功能，选择"HTTP推送模式"</p>
              </div>
            }
            status="process"
          />
          <Step
            title="填写请求网址"
            description={
              <div>
                <p><strong>请求网址填写：</strong></p>
                <Text code style={{ display: 'block', margin: '8px 0', padding: '8px', background: '#f5f5f5' }}>
                  https://your-domain.com/api/v1/project/{'{projectId}'}/plugin/dingtalk/webhook/events
                </Text>
                <p><Text type="secondary">将 your-domain.com 替换为您的实际域名，{'{projectId}'} 替换为当前项目ID</Text></p>
                <p><Text type="warning">⚠️ 必须是公网可访问的HTTPS地址</Text></p>
              </div>
            }
            status="process"
          />
          <Step
            title="生成加密参数"
            description={
              <div>
                <p><strong>Token：</strong>随机生成一个字符串（建议16-32位）</p>
                <p><strong>AES Key：</strong>随机生成43位字符串</p>
                <p><Text type="secondary">这两个参数需要复制到下面的应用配置中</Text></p>
              </div>
            }
            status="process"
          />
          <Step
            title="选择事件类型"
            description={
              <div>
                <p>根据需要选择要订阅的事件类型，如：</p>
                <ul>
                  <li>审批实例状态变更 (bpms_instance_change)</li>
                  <li>审批任务状态变更 (bpms_task_change)</li>
                  <li>用户信息变更 (user_modify_org)</li>
                </ul>
              </div>
            }
            status="process"
          />
          <Step
            title="在应用中创建事件订阅"
            description={
              <div>
                <p>使用钉钉开放平台的Token和AES Key在本应用中创建事件订阅配置</p>
              </div>
            }
            status="process"
          />
        </Steps>
      </Card>

      <Card title="配置参数对照表" style={{ marginBottom: 24 }}>
        <Table
          size="small"
          columns={[
            { title: '钉钉开放平台', dataIndex: 'dingtalk', key: 'dingtalk' },
            { title: '应用配置字段', dataIndex: 'app', key: 'app' },
            { title: '说明', dataIndex: 'description', key: 'description' }
          ]}
          dataSource={[
            {
              key: '1',
              dingtalk: '请求网址',
              app: '回调URL（自动填充）',
              description: '钉钉推送事件的接收地址'
            },
            {
              key: '2',
              dingtalk: 'Token',
              app: '签名Token',
              description: '用于验证请求来源的签名密钥'
            },
            {
              key: '3',
              dingtalk: 'AES Key',
              app: 'AES加密密钥',
              description: '用于解密钉钉推送数据的43位密钥'
            }
          ]}
          pagination={false}
        />
      </Card>

      <Alert
        message="开发建议"
        description={
          <div>
            <p><strong>当前推荐：</strong>使用HTTP推送模式，因为：</p>
            <ul>
              <li>实时性好，延迟低</li>
              <li>实现简单，维护成本低</li>
              <li>与现有架构兼容性好</li>
              <li>钉钉官方推荐的标准模式</li>
            </ul>
          </div>
        }
        type="success"
        showIcon
        style={{ marginTop: 24 }}
      />
    </div>
  );

  // 渲染API文档
  const renderApiDoc = () => (
    <div>
      <Alert
        message="API接口文档"
        description="钉钉业务审批系统提供的核心API接口说明，包括请求参数、响应格式和使用示例。"
        type="info"
        style={{ marginBottom: 24 }}
      />

      <Table
        dataSource={apiEndpoints}
        columns={[
          {
            title: '方法',
            dataIndex: 'method',
            key: 'method',
            width: 80,
            render: (method) => (
              <Tag color={method === 'GET' ? 'blue' : method === 'POST' ? 'green' : 'orange'}>
                {method}
              </Tag>
            )
          },
          {
            title: '路径',
            dataIndex: 'path',
            key: 'path',
            render: (path) => <code>{path}</code>
          },
          {
            title: '说明',
            dataIndex: 'description',
            key: 'description'
          },
          {
            title: '参数',
            dataIndex: 'params',
            key: 'params',
            render: (params) => (
              <div>
                {params.map((param, index) => (
                  <div key={index}><code>{param}</code></div>
                ))}
              </div>
            )
          },
          {
            title: '响应',
            dataIndex: 'response',
            key: 'response',
            render: (response) => <code>{response}</code>
          }
        ]}
        pagination={false}
        size="small"
      />
    </div>
  );

  // 渲染使用示例
  const renderExamples = () => (
    <div>
      <Alert
        message="使用示例"
        description="完整的业务审批流程示例，展示如何创建模板、发起审批、处理回调等操作。"
        type="success"
        style={{ marginBottom: 24 }}
      />

      <Collapse defaultActiveKey={['1']}>
        <Panel header="1. 创建审批模板" key="1" extra={<CodeOutlined />}>
          <Paragraph>
            首先需要为每种业务类型创建对应的审批模板：
          </Paragraph>
          <pre style={{ background: '#f5f5f5', padding: 16, borderRadius: 4 }}>
{`// Python 后端示例
from plugins.dingtalk.examples.business_approval_example import BusinessApprovalExample

# 初始化示例
example = BusinessApprovalExample(db, project_id, dingtalk_api)

# 创建门店报损审批模板
await example.create_store_loss_template()

# 创建销售业绩上报模板
await example.create_sales_report_template()

# 创建费用报销模板
await example.create_expense_reimbursement_template()`}
          </pre>
        </Panel>

        <Panel header="2. 发起审批申请" key="2" extra={<ExperimentOutlined />}>
          <Paragraph>
            用户在前端界面填写表单，系统自动创建钉钉审批实例：
          </Paragraph>
          <pre style={{ background: '#f5f5f5', padding: 16, borderRadius: 4 }}>
{`// 前端 JavaScript 示例
const createApproval = async (formData) => {
  const response = await apiService.post(
    \`/project/\${projectId}/plugin/dingtalk/approval/templates\`,
    {
      type: 'store_loss',
      name: '门店报损申请',
      form_data: formData,
      approvers: [
        { user_ids: ['manager001'], mode: 'and' },
        { user_ids: ['admin001'], mode: 'and' }
      ]
    }
  );
  
  if (response.success) {
    message.success('审批申请创建成功');
  }
};`}
          </pre>
        </Panel>

        <Panel header="3. 业务数据集成" key="3" extra={<SettingOutlined />}>
          <Paragraph>
            审批通过后，系统自动创建对应的业务记录：
          </Paragraph>
          <pre style={{ background: '#f5f5f5', padding: 16, borderRadius: 4 }}>
{`// 业务集成示例
from plugins.dingtalk.services.business_integration import (
    integrate_store_loss,
    integrate_sales_report,
    integrate_expense_reimbursement
)

# 根据审批类型调用对应的集成函数
async def integrate_business_data(instance):
    if instance.template_type == 'store_loss':
        result = await integrate_store_loss(db, project_id, instance, 'sync')
    elif instance.template_type == 'sales_report':
        result = await integrate_sales_report(db, project_id, instance, 'sync')
    elif instance.template_type == 'expense_reimbursement':
        result = await integrate_expense_reimbursement(db, project_id, instance, 'sync')
    
    return result`}
          </pre>
        </Panel>
      </Collapse>
    </div>
  );

  // 渲染故障排除
  const renderTroubleshooting = () => (
    <div>
      <Alert
        message="故障排除指南"
        description="常见问题和解决方案，帮助快速定位和解决集成过程中遇到的问题。"
        type="warning"
        style={{ marginBottom: 24 }}
      />

      <Collapse>
        <Panel header="钉钉配置问题" key="1" extra={<WarningOutlined />}>
          <div>
            <Title level={5}>问题现象：</Title>
            <ul>
              <li>无法获取钉钉访问令牌</li>
              <li>创建审批模板失败</li>
              <li>用户登录失败</li>
            </ul>
            
            <Title level={5}>解决方案：</Title>
            <ol>
              <li>检查钉钉应用配置是否正确：app_key、app_secret</li>
              <li>确认钉钉应用权限包含：审批流程管理、通讯录读取</li>
              <li>验证回调地址设置是否正确</li>
              <li>检查网络连接和防火墙设置</li>
            </ol>
          </div>
        </Panel>

        <Panel header="审批模板创建失败" key="2" extra={<BugOutlined />}>
          <div>
            <Title level={5}>问题现象：</Title>
            <ul>
              <li>模板创建API返回错误</li>
              <li>表单组件配置不正确</li>
              <li>审批流程节点设置错误</li>
            </ul>
            
            <Title level={5}>解决方案：</Title>
            <ol>
              <li>检查模板数据格式是否符合钉钉规范</li>
              <li>验证表单组件类型和属性配置</li>
              <li>确认审批人用户ID是否存在且有效</li>
              <li>查看详细错误日志进行问题定位</li>
            </ol>
          </div>
        </Panel>

        <Panel header="业务数据集成问题" key="3" extra={<ExperimentOutlined />}>
          <div>
            <Title level={5}>问题现象：</Title>
            <ul>
              <li>审批通过但业务记录未创建</li>
              <li>数据同步不及时或不完整</li>
              <li>重复创建业务记录</li>
            </ul>
            
            <Title level={5}>解决方案：</Title>
            <ol>
              <li>检查回调处理逻辑是否正常执行</li>
              <li>验证业务模型和数据库连接</li>
              <li>确认表单数据解析和映射逻辑</li>
              <li>添加重复检查和事务处理机制</li>
            </ol>
          </div>
        </Panel>
      </Collapse>
    </div>
  );

  return (
    <Card 
      title={
        <Space>
          <BookOutlined />
          钉钉插件文档
        </Space>
      }
    >
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane 
          tab={<span><InfoCircleOutlined />功能概览</span>} 
          key="overview"
        >
          {renderOverview()}
        </TabPane>
        
        <TabPane 
          tab={<span><SettingOutlined />配置指南</span>} 
          key="config"
        >
          {renderConfigGuide()}
        </TabPane>
        
        <TabPane 
          tab={<span><SettingOutlined />事件订阅</span>} 
          key="events"
        >
          {renderEventSubscription()}
        </TabPane>
        
        <TabPane 
          tab={<span><ApiOutlined />API文档</span>} 
          key="api"
        >
          {renderApiDoc()}
        </TabPane>
        
        <TabPane 
          tab={<span><CodeOutlined />使用示例</span>} 
          key="examples"
        >
          {renderExamples()}
        </TabPane>
        
        <TabPane 
          tab={<span><BugOutlined />故障排除</span>} 
          key="troubleshooting"
        >
          {renderTroubleshooting()}
        </TabPane>
      </Tabs>
    </Card>
  );
};

export default PluginDocumentation; 