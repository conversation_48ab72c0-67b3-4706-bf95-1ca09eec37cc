import pytest
from httpx import Client
from sqlalchemy.orm import Session
from fastapi import status
import uuid
from datetime import date

from models.user import User
from models.project import Project
from models.store import Store
from models.sales_management import SalesChannel
from services.sales_report import SalesReportService
from schemas.sales_report import SalesReportCreate, ChannelDetailCreate, PaymentMethodDetailCreate

@pytest.fixture
def setup_sales_data(db: Session, current_project: Project, current_user: User):
    """Fixture to set up necessary data for sales report tests."""
    store = Store(id=uuid.uuid4(), name="测试门店", project_id=current_project.id)
    db.add(store)
    
    sales_channel = SalesChannel(id=uuid.uuid4(), name="线上渠道", project_id=current_project.id)
    db.add(sales_channel)
    
    db.commit()
    db.refresh(store)
    db.refresh(sales_channel)
    
    report_create_data = {
        "store_id": store.id,
        "report_date": date.today(),
        "report_type": "daily",
        "notes": "这是一个集成测试报告",
        "channel_details": [
            {
                "sales_channel_id": sales_channel.id,
                "payment_methods_details": [
                    {
                        "payment_method_id": uuid.uuid4(),
                        "amount": 100.0,
                        "orders": 10
                    }
                ]
            }
        ]
    }
    
    # We need to run the async service function in a sync context
    import asyncio
    report = asyncio.run(SalesReportService.create_report(
        db=db,
        project_id=current_project.id,
        user_id=current_user.id,
        report_data=SalesReportCreate.model_validate(report_create_data)
    ))
    
    return {
        "project": current_project,
        "store": store,
        "sales_channel": sales_channel,
        "report": report
    }

def test_get_sales_reports_list_success(
    client: Client,
    get_auth_headers: dict,
    setup_sales_data: dict
):
    """
    Test getting a list of sales reports, ensuring the ORM to Pydantic conversion works.
    This test will fail if the validation issue persists.
    """
    project_id = setup_sales_data["project"].id
    headers = get_auth_headers
    
    response = client.get(
        f"/api/v1/project/{project_id}/sales-reports",
        headers=headers
    )
    
    assert response.status_code == status.HTTP_200_OK
    response_data = response.json()
    
    assert response_data["total"] >= 1
    assert len(response_data["items"]) >= 1
    
    first_report = response_data["items"][0]
    assert "id" in first_report
    assert "store_name" in first_report
    assert "creator_name" in first_report
    assert first_report["id"] == str(setup_sales_data["report"].id)
    assert first_report["notes"] == "这是一个集成测试报告"
    assert len(first_report["items"]) == 0
    assert len(first_report["channel_details"]) > 0
    
    first_channel = first_report["channel_details"][0]
    assert first_channel["total_sales"] == 100.0
    assert first_channel["total_orders"] == 10 