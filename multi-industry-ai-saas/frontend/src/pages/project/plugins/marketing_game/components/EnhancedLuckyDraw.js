import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  Button,
  message,
  Modal,
  Typography,
  Card,
  Row,
  Col,
  Statistic,
  Badge,
  Progress,
  Space,
  Tag,
  Tooltip,
  Alert,
  Spin,
  Empty,
  Avatar,
  List,
  Divider,
  Switch,
  Select,
  Form,
  Input,
  notification,
  Image,
  Timeline
} from 'antd';
import {
  TrophyOutlined,
  GiftOutlined,
  SoundOutlined,
  SoundFilled,
  PlayCircleOutlined,
  StarOutlined,
  FireOutlined,
  ThunderboltOutlined,
  HeartOutlined,
  SmileOutlined,
  CrownOutlined,
  SettingOutlined,
  HistoryOutlined,
  UserOutlined,
  ReloadOutlined,
  ClockCircleOutlined,
  RocketOutlined,
  DiamondOutlined,
  CheckCircleOutlined
} from '@ant-design/icons';
import './EnhancedLuckyDraw.css';
import marketingGameApi from '../service/api/marketingGame';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;

// 主题配置
const THEMES = {
  classic: {
    name: '经典风格',
    primaryColor: '#1890ff',
    secondaryColor: '#52c41a',
    bgGradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    cardBg: 'rgba(255, 255, 255, 0.95)',
    textColor: '#333'
  },
  neon: {
    name: '霓虹风格',
    primaryColor: '#00BFFF',
    secondaryColor: '#39FF14',
    bgGradient: 'linear-gradient(135deg, #0c0c0c 0%, #1a1a1a 100%)',
    cardBg: 'rgba(0, 0, 0, 0.8)',
    textColor: '#fff'
  },
  gradient: {
    name: '渐变风格',
    primaryColor: '#ff6b6b',
    secondaryColor: '#4ecdc4',
    bgGradient: 'linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%)',
    cardBg: 'rgba(255, 255, 255, 0.9)',
    textColor: '#333'
  },
  dark: {
    name: '暗黑风格',
    primaryColor: '#722ed1',
    secondaryColor: '#eb2f96',
    bgGradient: 'linear-gradient(135deg, #434343 0%, #000000 100%)',
    cardBg: 'rgba(0, 0, 0, 0.8)',
    textColor: '#fff'
  }
};

// 抽奖动画类型
const DRAW_ANIMATIONS = {
  cards: { name: '翻卡效果', icon: <CrownOutlined /> },
  roulette: { name: '轮盘效果', icon: <ThunderboltOutlined /> },
  shake: { name: '摇奖效果', icon: <RocketOutlined /> },
  flash: { name: '闪光效果', icon: <StarOutlined /> }
};

const EnhancedLuckyDraw = ({ gameId, embedded = false }) => {
  // 基础状态
  const [loading, setLoading] = useState(true);
  const [game, setGame] = useState(null);
  const [prizes, setPrizes] = useState([]);
  const [drawing, setDrawing] = useState(false);
  const [canDraw, setCanDraw] = useState(true);
  const [remainingDraws, setRemainingDraws] = useState(0);
  
  // 界面状态
  const [currentTheme, setCurrentTheme] = useState('classic');
  const [soundEnabled, setSoundEnabled] = useState(true);
  const [animationType, setAnimationType] = useState('cards');
  const [showStats, setShowStats] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [showHistory, setShowHistory] = useState(false);
  
  // 游戏状态
  const [winningPrize, setWinningPrize] = useState(null);
  const [showResult, setShowResult] = useState(false);
  const [totalSpins, setTotalSpins] = useState(0);
  const [winCount, setWinCount] = useState(0);
  const [lastWinTime, setLastWinTime] = useState(null);
  const [winStreak, setWinStreak] = useState(0);
  const [drawHistory, setDrawHistory] = useState([]);
  
  // 动画状态
  const [isAnimating, setIsAnimating] = useState(false);
  const [selectedCards, setSelectedCards] = useState([]);
  const [showCards, setShowCards] = useState(true);
  
  // 验证状态
  const [needVerification, setNeedVerification] = useState(false);
  const [verified, setVerified] = useState(false);
  
  // 引用
  const audioRef = useRef(null);
  const drawContainerRef = useRef(null);
  
  // 音频引用
  const spinSoundRef = useRef(null);
  const winSoundRef = useRef(null);
  const loseSoundRef = useRef(null);
  const clickSoundRef = useRef(null);

  // 初始化音频
  useEffect(() => {
    if (soundEnabled) {
      try {
        spinSoundRef.current = new Audio('/sounds/draw-start.mp3');
        winSoundRef.current = new Audio('/sounds/win-celebration.mp3');
        loseSoundRef.current = new Audio('/sounds/try-again.mp3');
        clickSoundRef.current = new Audio('/sounds/click.mp3');
        
        [spinSoundRef, winSoundRef, loseSoundRef, clickSoundRef].forEach(ref => {
          if (ref.current) {
            ref.current.volume = 0.3;
            ref.current.preload = 'auto';
          }
        });
      } catch (error) {
        console.log('音频加载失败:', error);
      }
    }
  }, [soundEnabled]);

  // 播放音效
  const playSound = useCallback((type) => {
    if (!soundEnabled) return;
    
    try {
      let audio = null;
      switch (type) {
        case 'draw':
          audio = spinSoundRef.current;
          break;
        case 'win':
          audio = winSoundRef.current;
          break;
        case 'lose':
          audio = loseSoundRef.current;
          break;
        case 'click':
          audio = clickSoundRef.current;
          break;
        default:
          return;
      }
      
      if (audio) {
        audio.currentTime = 0;
        audio.play().catch(e => console.log('音频播放失败:', e));
      }
    } catch (error) {
      console.log('播放音效失败:', error);
    }
  }, [soundEnabled]);

  // 获取游戏数据
  const fetchGameData = useCallback(async () => {
    try {
      setLoading(true);
      
      const gameResponse = await marketingGameApi.getGameDetail(gameId);
      if (!gameResponse.success) {
        message.error('获取游戏详情失败');
        return;
      }
      
      const gameData = gameResponse.data;
      setGame(gameData);
      setRemainingDraws(gameData.remaining_draws || 0);
      
      const prizesResponse = await marketingGameApi.getGamePrizes(gameId);
      if (prizesResponse.success) {
        setPrizes(prizesResponse.data || []);
      }
      
      await fetchUserStats();
      
      const needVerify = gameData.min_consumption > 0 || gameData.settings?.enable_receipt_verification;
      setNeedVerification(needVerify);
      
    } catch (error) {
      console.error('获取游戏数据失败:', error);
      message.error('获取游戏数据失败');
    } finally {
      setLoading(false);
    }
  }, [gameId]);

  // 获取用户统计
  const fetchUserStats = useCallback(async () => {
    try {
      const response = await marketingGameApi.getUserStats(gameId);
      if (response.success) {
        const stats = response.data;
        setTotalSpins(stats.total_spins || 0);
        setWinCount(stats.win_count || 0);
        setLastWinTime(stats.last_win_time);
        setWinStreak(stats.win_streak || 0);
      }
    } catch (error) {
      console.error('获取用户统计失败:', error);
    }
  }, [gameId]);

  // 初始化
  useEffect(() => {
    fetchGameData();
  }, [fetchGameData]);

  // 执行抽奖
  const handleDraw = async () => {
    if (drawing || !canDraw) return;
    
    if (remainingDraws <= 0) {
      message.warning('您今日的抽奖次数已用完');
      return;
    }
    
    if (needVerification && !verified) {
      message.warning('请先完成身份验证');
      setShowSettings(true);
      return;
    }
    
    playSound('click');
    
    try {
      setDrawing(true);
      setCanDraw(false);
      setIsAnimating(true);
      
      playSound('draw');
      
      // 执行抽奖动画
      await performDrawAnimation();
      
      // 调用抽奖API
      const response = await marketingGameApi.drawPrize(gameId);
      
      if (response.success) {
        const result = response.data;
        
        // 添加到历史记录
        const newRecord = {
          id: Date.now(),
          time: new Date(),
          prize: result.prize,
          isWin: !!result.prize
        };
        setDrawHistory(prev => [newRecord, ...prev.slice(0, 9)]);
        
        if (result.prize) {
          setWinningPrize(result.prize);
          setWinCount(prev => prev + 1);
          setWinStreak(prev => prev + 1);
          setLastWinTime(new Date());
          playSound('win');
          
          triggerWinAnimation(result.prize);
          
          notification.success({
            message: '🎉 恭喜中奖！',
            description: `您获得了：${result.prize.name}`,
            duration: 5,
            placement: 'topRight'
          });
        } else {
          setWinStreak(0);
          playSound('lose');
          
          notification.info({
            message: '很遗憾',
            description: '本次未中奖，再试一次吧！',
            duration: 3,
            placement: 'topRight'
          });
        }
        
        setTotalSpins(prev => prev + 1);
        setRemainingDraws(prev => prev - 1);
        setShowResult(true);
        
        await fetchUserStats();
        
      } else {
        message.error(response.message || '抽奖失败');
        playSound('lose');
      }
      
    } catch (error) {
      console.error('抽奖失败:', error);
      message.error('抽奖失败，请重试');
      playSound('lose');
    } finally {
      setDrawing(false);
      setIsAnimating(false);
      setTimeout(() => setCanDraw(true), 1000);
    }
  };

  // 执行抽奖动画
  const performDrawAnimation = () => {
    return new Promise((resolve) => {
      switch (animationType) {
        case 'cards':
          performCardAnimation(resolve);
          break;
        case 'roulette':
          performRouletteAnimation(resolve);
          break;
        case 'shake':
          performShakeAnimation(resolve);
          break;
        case 'flash':
          performFlashAnimation(resolve);
          break;
        default:
          setTimeout(resolve, 2000);
      }
    });
  };

  // 翻卡动画
  const performCardAnimation = (resolve) => {
    const cardCount = Math.min(prizes.length, 6);
    const cards = Array.from({ length: cardCount }, (_, i) => i);
    
    setSelectedCards([]);
    setShowCards(true);
    
    let currentCard = 0;
    const interval = setInterval(() => {
      setSelectedCards(prev => [...prev, currentCard]);
      currentCard++;
      
      if (currentCard >= cardCount) {
        clearInterval(interval);
        setTimeout(() => {
          setShowCards(false);
          setTimeout(resolve, 500);
        }, 1000);
      }
    }, 300);
  };

  // 轮盘动画
  const performRouletteAnimation = (resolve) => {
    const container = drawContainerRef.current;
    if (!container) {
      setTimeout(resolve, 2000);
      return;
    }
    
    container.style.transform = 'rotate(0deg)';
    container.style.transition = 'transform 2s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
    
    setTimeout(() => {
      const randomRotation = 360 * 3 + Math.random() * 360;
      container.style.transform = `rotate(${randomRotation}deg)`;
    }, 100);
    
    setTimeout(() => {
      container.style.transition = '';
      container.style.transform = 'rotate(0deg)';
      resolve();
    }, 2500);
  };

  // 摇奖动画
  const performShakeAnimation = (resolve) => {
    const container = drawContainerRef.current;
    if (!container) {
      setTimeout(resolve, 2000);
      return;
    }
    
    container.classList.add('shake-animation');
    setTimeout(() => {
      container.classList.remove('shake-animation');
      resolve();
    }, 2000);
  };

  // 闪光动画
  const performFlashAnimation = (resolve) => {
    const container = drawContainerRef.current;
    if (!container) {
      setTimeout(resolve, 2000);
      return;
    }
    
    container.classList.add('flash-animation');
    setTimeout(() => {
      container.classList.remove('flash-animation');
      resolve();
    }, 2000);
  };

  // 触发中奖动画
  const triggerWinAnimation = (prize) => {
    if (navigator.vibrate) {
      navigator.vibrate([200, 100, 200]);
    }
    
    // 创建庆祝粒子效果
    createCelebrationEffect();
  };

  // 创建庆祝效果
  const createCelebrationEffect = () => {
    const container = document.createElement('div');
    container.className = 'celebration-container';
    document.body.appendChild(container);
    
    for (let i = 0; i < 50; i++) {
      const particle = document.createElement('div');
      particle.className = 'celebration-particle';
      particle.style.left = Math.random() * window.innerWidth + 'px';
      particle.style.top = Math.random() * window.innerHeight + 'px';
      particle.style.animationDelay = Math.random() * 2 + 's';
      container.appendChild(particle);
    }
    
    setTimeout(() => {
      document.body.removeChild(container);
    }, 3000);
  };

  // 渲染抽奖区域
  const renderDrawArea = () => {
    const theme = THEMES[currentTheme];
    
    return (
      <div className="draw-area" ref={drawContainerRef}>
        {animationType === 'cards' && showCards && (
          <div className="cards-container">
            {Array.from({ length: Math.min(prizes.length, 6) }, (_, i) => (
              <div 
                key={i} 
                className={`draw-card ${selectedCards.includes(i) ? 'selected' : ''}`}
                style={{ animationDelay: `${i * 0.1}s` }}
              >
                <div className="card-front">
                  <GiftOutlined style={{ fontSize: '48px', color: theme.primaryColor }} />
                  <div>神秘奖品</div>
                </div>
                <div className="card-back">
                  {prizes[i] && (
                    <>
                      <img src={prizes[i].image_url || '/placeholder.png'} alt="" />
                      <div>{prizes[i].name}</div>
                    </>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
        
        {animationType === 'roulette' && (
          <div className="roulette-container">
            <div className="roulette-wheel">
              {prizes.slice(0, 8).map((prize, index) => (
                <div 
                  key={index} 
                  className="roulette-sector"
                  style={{
                    transform: `rotate(${index * 45}deg)`,
                    backgroundColor: `hsl(${index * 45}, 70%, 60%)`
                  }}
                >
                  <div className="sector-content">
                    <img src={prize.image_url || '/placeholder.png'} alt="" />
                    <span>{prize.name}</span>
                  </div>
                </div>
              ))}
            </div>
            <div className="roulette-pointer">
              <ThunderboltOutlined style={{ fontSize: '24px', color: theme.primaryColor }} />
            </div>
          </div>
        )}
        
        {(animationType === 'shake' || animationType === 'flash') && (
          <div className="simple-draw-container">
            <div className="draw-box">
              <RocketOutlined style={{ fontSize: '64px', color: theme.primaryColor }} />
              <div style={{ marginTop: 16, fontSize: '18px', fontWeight: 'bold' }}>
                {drawing ? '抽奖中...' : '点击开始抽奖'}
              </div>
            </div>
          </div>
        )}
      </div>
    );
  };

  // 渲染统计信息
  const renderStats = () => (
    <Row gutter={16}>
      <Col span={6}>
        <Card className="stat-card">
          <Statistic
            title="总抽奖次数"
            value={totalSpins}
            prefix={<PlayCircleOutlined />}
            valueStyle={{ color: THEMES[currentTheme].primaryColor }}
          />
        </Card>
      </Col>
      <Col span={6}>
        <Card className="stat-card">
          <Statistic
            title="中奖次数"
            value={winCount}
            prefix={<TrophyOutlined />}
            valueStyle={{ color: '#52c41a' }}
          />
        </Card>
      </Col>
      <Col span={6}>
        <Card className="stat-card">
          <Statistic
            title="中奖率"
            value={totalSpins > 0 ? ((winCount / totalSpins) * 100).toFixed(1) : 0}
            suffix="%"
            prefix={<StarOutlined />}
            valueStyle={{ color: '#faad14' }}
          />
        </Card>
      </Col>
      <Col span={6}>
        <Card className="stat-card">
          <Statistic
            title="连胜纪录"
            value={winStreak}
            prefix={<FireOutlined />}
            valueStyle={{ color: '#f5222d' }}
          />
        </Card>
      </Col>
    </Row>
  );

  // 渲染设置面板
  const renderSettings = () => (
    <Space direction="vertical" style={{ width: '100%' }}>
      <Card title="主题设置">
        <Select
          value={currentTheme}
          onChange={setCurrentTheme}
          style={{ width: '100%' }}
        >
          {Object.entries(THEMES).map(([key, theme]) => (
            <Option key={key} value={key}>
              {theme.name}
            </Option>
          ))}
        </Select>
      </Card>
      
      <Card title="动画效果">
        <Select
          value={animationType}
          onChange={setAnimationType}
          style={{ width: '100%' }}
        >
          {Object.entries(DRAW_ANIMATIONS).map(([key, animation]) => (
            <Option key={key} value={key}>
              {animation.icon} {animation.name}
            </Option>
          ))}
        </Select>
      </Card>
      
      <Card title="音效设置">
        <Space>
          <Text>音效开关：</Text>
          <Switch
            checked={soundEnabled}
            onChange={setSoundEnabled}
            checkedChildren={<SoundFilled />}
            unCheckedChildren={<SoundOutlined />}
          />
        </Space>
      </Card>
      
      {needVerification && (
        <Card title="身份验证">
          <Space direction="vertical" style={{ width: '100%' }}>
            <Alert
              message="需要验证"
              description="参与抽奖需要完成身份验证"
              type="warning"
              showIcon
            />
            <Button 
              type="primary" 
              onClick={() => setVerified(true)}
            >
              完成验证
            </Button>
          </Space>
        </Card>
      )}
    </Space>
  );

  // 渲染历史记录
  const renderHistory = () => (
    <Card title="抽奖历史" style={{ maxHeight: 400, overflow: 'auto' }}>
      {drawHistory.length > 0 ? (
        <Timeline>
          {drawHistory.map((record) => (
            <Timeline.Item
              key={record.id}
              dot={record.isWin ? <TrophyOutlined style={{ color: '#52c41a' }} /> : <ClockCircleOutlined />}
              color={record.isWin ? 'green' : 'gray'}
            >
              <div>
                <div style={{ fontWeight: 'bold' }}>
                  {record.isWin ? `中奖：${record.prize.name}` : '未中奖'}
                </div>
                <div style={{ color: '#666', fontSize: '12px' }}>
                  {record.time.toLocaleString()}
                </div>
              </div>
            </Timeline.Item>
          ))}
        </Timeline>
      ) : (
        <Empty description="暂无抽奖记录" />
      )}
    </Card>
  );

  if (loading) {
    return (
      <div className="draw-loading">
        <Spin size="large" />
        <Title level={4} style={{ marginTop: 16 }}>
          加载游戏中...
        </Title>
      </div>
    );
  }

  if (!game || !prizes.length) {
    return (
      <Empty
        description="游戏暂未配置或奖品为空"
        image={Empty.PRESENTED_IMAGE_SIMPLE}
      />
    );
  }

  const theme = THEMES[currentTheme];

  return (
    <div className={`enhanced-lucky-draw theme-${currentTheme}`} style={{ background: theme.bgGradient }}>
      {/* 游戏头部 */}
      <div className="game-header">
        <Card className="header-card" style={{ background: theme.cardBg, color: theme.textColor }}>
          <Row justify="space-between" align="middle">
            <Col>
              <Title level={3} style={{ margin: 0, color: theme.textColor }}>
                <GiftOutlined style={{ marginRight: 8, color: theme.primaryColor }} />
                {game.name}
              </Title>
              <Text style={{ color: theme.textColor }}>{game.description}</Text>
            </Col>
            <Col>
              <Space>
                <Badge count={remainingDraws} showZero>
                  <Button
                    type="primary"
                    size="large"
                    onClick={handleDraw}
                    loading={drawing}
                    disabled={!canDraw || remainingDraws <= 0}
                    className="draw-button"
                    style={{ backgroundColor: theme.primaryColor, borderColor: theme.primaryColor }}
                  >
                    {drawing ? '抽奖中...' : '开始抽奖'}
                  </Button>
                </Badge>
                <Tooltip title="游戏统计">
                  <Button
                    icon={<HistoryOutlined />}
                    onClick={() => setShowStats(!showStats)}
                  />
                </Tooltip>
                <Tooltip title="游戏设置">
                  <Button
                    icon={<SettingOutlined />}
                    onClick={() => setShowSettings(!showSettings)}
                  />
                </Tooltip>
                <Tooltip title="抽奖历史">
                  <Button
                    icon={<ClockCircleOutlined />}
                    onClick={() => setShowHistory(!showHistory)}
                  />
                </Tooltip>
              </Space>
            </Col>
          </Row>
        </Card>
      </div>

      {/* 统计信息 */}
      {showStats && (
        <div className="stats-section">
          {renderStats()}
        </div>
      )}

      {/* 主游戏区域 */}
      <div className="main-game-area">
        <Row gutter={24}>
          <Col span={showSettings || showHistory ? 16 : 24}>
            <Card className="draw-card" style={{ background: theme.cardBg }}>
              {renderDrawArea()}
              
              {/* 奖品展示 */}
              <div className="prizes-display">
                <Title level={5} style={{ color: theme.textColor }}>
                  <TrophyOutlined style={{ color: theme.primaryColor }} /> 丰厚奖品等你来拿
                </Title>
                <Row gutter={8}>
                  {prizes.slice(0, 6).map((prize, index) => (
                    <Col span={4} key={prize.id}>
                      <Card size="small" className="prize-card">
                        <Avatar
                          src={prize.image_url}
                          icon={<GiftOutlined />}
                          size={32}
                        />
                        <Text className="prize-name">{prize.name}</Text>
                      </Card>
                    </Col>
                  ))}
                </Row>
              </div>
            </Card>
          </Col>

          {/* 侧边栏 */}
          {(showSettings || showHistory) && (
            <Col span={8}>
              {showSettings && (
                <Card title="游戏设置" className="settings-card" style={{ background: theme.cardBg }}>
                  {renderSettings()}
                </Card>
              )}
              {showHistory && renderHistory()}
            </Col>
          )}
        </Row>
      </div>

      {/* 中奖结果模态框 */}
      <Modal
        open={showResult}
        onCancel={() => setShowResult(false)}
        footer={null}
        centered
        className="result-modal"
        width={480}
      >
        <div className="result-content">
          {winningPrize ? (
            <div className="win-result">
              <div className="celebration-icon">
                <CrownOutlined style={{ fontSize: 64, color: '#faad14' }} />
              </div>
              <Title level={2} style={{ color: '#52c41a', margin: '16px 0' }}>
                🎉 恭喜中奖！
              </Title>
              <Card className="prize-info-card">
                <Avatar
                  src={winningPrize.image_url}
                  icon={<GiftOutlined />}
                  size={80}
                />
                <Title level={4} style={{ margin: '16px 0 8px' }}>
                  {winningPrize.name}
                </Title>
                <Text type="secondary">{winningPrize.description}</Text>
                {winningPrize.value > 0 && (
                  <Tag color="gold" style={{ marginTop: 8 }}>
                    价值 ¥{winningPrize.value}
                  </Tag>
                )}
              </Card>
            </div>
          ) : (
            <div className="lose-result">
              <div className="comfort-icon">
                <SmileOutlined style={{ fontSize: 64, color: '#1890ff' }} />
              </div>
              <Title level={3} style={{ margin: '16px 0' }}>
                再接再厉！
              </Title>
              <Paragraph style={{ textAlign: 'center' }}>
                很遗憾本次未中奖，不要气馁，下次一定会有好运气的！
              </Paragraph>
            </div>
          )}
          
          <div className="result-actions">
            <Space size="large">
              <Button onClick={() => setShowResult(false)}>
                关闭
              </Button>
              {remainingDraws > 0 && (
                <Button
                  type="primary"
                  icon={<ReloadOutlined />}
                  onClick={() => {
                    setShowResult(false);
                    setTimeout(handleDraw, 500);
                  }}
                >
                  再来一次
                </Button>
              )}
            </Space>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default EnhancedLuckyDraw; 