import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Input,
  Button,
  Select,
  Radio,
  message,
  Space,
  Divider,
  Typography,
  Row,
  Col,
  Spin,
  Alert
} from 'antd';
import {
  SendOutlined,
  UserOutlined,
  TeamOutlined,
  BellOutlined,
  InfoCircleOutlined,
  ArrowLeftOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import apiService from '../../../services/api';
import api from '../../../services/api/project/notifications';

const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;
const { Option } = Select;

/**
 * 创建通知组件
 */
const NotificationCreate = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [users, setUsers] = useState([]);
  const [roles, setRoles] = useState([]);
  const [notifyType, setNotifyType] = useState('user');
  const navigate = useNavigate();

  // 获取用户列表
  const fetchUsers = async () => {
    try {
      setLoading(true);
      console.log('开始获取用户列表');

      // 使用apiService获取用户列表，确保带上认证信息
      const response = await apiService.project.user.getList();
      console.log('用户列表原始响应:', response);

      // 处理特殊的响应格式 - 数组中包含对象，每个对象有success和data字段
      if (Array.isArray(response)) {
        const userList = response
          .filter(item => item.success && item.data)
          .map(item => {
            const userData = item.data;
            return {
              id: userData.id,
              username: userData.username,
              name: userData.name,
              full_name: userData.name, // 使用name作为full_name
              email: userData.email,
              role: userData.role,
              role_name: userData.role_name
            };
          });

        console.log('处理后的用户列表:', userList);
        setUsers(userList);
      } else if (response && response.data) {
        // 处理标准响应格式
        let userList = [];
        if (Array.isArray(response.data)) {
          userList = response.data;
        } else if (response.data.items && Array.isArray(response.data.items)) {
          userList = response.data.items;
        } else if (response.data.data && Array.isArray(response.data.data)) {
          userList = response.data.data;
        }

        // 确保每个用户对象都有id属性
        userList = userList.map(user => {
          // 如果用户对象没有id属性，但有_id属性，使用_id作为id
          if (!user.id && user._id) {
            return { ...user, id: user._id };
          }
          return user;
        }).filter(user => user.id); // 过滤掉没有id的用户

        console.log('处理后的用户列表:', userList);
        setUsers(userList);
      } else {
        console.warn('用户列表响应格式不正确:', response);
        setUsers([]);
      }
    } catch (error) {
      console.error('获取用户列表失败:', error);
      message.error('获取用户列表失败');
      setUsers([]);
    } finally {
      setLoading(false);
    }
  };

  // 获取角色列表
  const fetchRoles = async () => {
    try {
      setLoading(true);
      console.log('开始获取角色列表');

      // 使用apiService获取角色列表，确保带上认证信息
      const response = await apiService.project.role.getList();
      console.log('角色列表原始响应:', response);

      // 检查响应格式，兼容不同的返回格式
      if (response && typeof response === 'object') {
        if (response.success !== false) {
          let roleList = [];
          
          // 处理包含items数组的响应格式
          if (response.items && Array.isArray(response.items)) {
            roleList = response.items.map(role => ({
              id: role.id,
              name: role.name,
              code: role.code,
              description: role.description
            }));
          } else if (response.data) {
            // 处理标准响应格式
            if (Array.isArray(response.data)) {
              roleList = response.data;
            } else if (response.data.items && Array.isArray(response.data.items)) {
              roleList = response.data.items;
            } else if (response.data.data && Array.isArray(response.data.data)) {
              roleList = response.data.data;
            }
          } else if (Array.isArray(response)) {
            roleList = response;
          }

          // 确保每个角色对象都有id属性
          roleList = roleList.map(role => {
            // 如果角色对象没有id属性，但有_id属性，使用_id作为id
            if (!role.id && role._id) {
              return { ...role, id: role._id };
            }
            return role;
          }).filter(role => role.id); // 过滤掉没有id的角色

          console.log('处理后的角色列表:', roleList);
          setRoles(roleList);
        } else {
          console.error('获取角色列表失败:', response.message);
          message.error(response.message || '获取角色列表失败');
          setRoles([]);
        }
      } else {
        console.warn('角色列表响应格式不正确:', response);
        setRoles([]);
      }
    } catch (error) {
      console.error('获取角色列表失败:', error);
      message.error('获取角色列表失败');
      setRoles([]);
    } finally {
      setLoading(false);
    }
  };

  // 初始化
  useEffect(() => {
    fetchUsers();
    fetchRoles();
  }, []);

  // 处理通知类型变更
  const handleNotifyTypeChange = (e) => {
    setNotifyType(e.target.value);
    form.setFieldsValue({
      user_ids: [],
      role_ids: []
    });
  };

  // 处理表单提交
  const handleSubmit = async (values) => {
    setLoading(true);
    try {
      // 构建通知数据
      const notificationData = {
        title: values.title,
        content: values.content,
        type: values.type,
        priority: values.priority,
        target_url: values.target_url || null
      };

      // 根据通知类型设置接收者
      if (notifyType === 'user') {
        // 发送给指定用户
        console.log('按用户发送通知，用户ID列表:', values.user_ids);
        for (const userId of values.user_ids) {
          console.log(`发送通知给用户 ${userId}`);
          try {
            // 使用正确的API方法名
            const response = await api.createNotification({
              ...notificationData,
              user_id: userId
            });
            console.log(`发送通知给用户 ${userId} 成功:`, response);
          } catch (userError) {
            console.error(`发送通知给用户 ${userId} 失败:`, userError);
            message.warning(`发送通知给用户 ${userId} 失败`);
          }
        }
      } else if (notifyType === 'role') {
        // 发送给指定角色的所有用户
        for (const roleId of values.role_ids) {
          console.log(`获取角色 ${roleId} 的用户列表`);
          try {
            // 使用apiService获取角色用户列表，确保带上认证信息
            const roleUsers = await apiService.project.role.getUsers(roleId);
            console.log(`角色 ${roleId} 用户列表原始响应:`, roleUsers);

            // 处理不同的响应格式
            let userIds = [];

            // 处理数组响应格式 - 每个元素是包含success和data的对象
            if (Array.isArray(roleUsers)) {
              userIds = roleUsers
                .filter(item => item.success && item.data)
                .map(item => item.data.id);
            }
            // 处理标准响应格式
            else if (roleUsers && roleUsers.data) {
              if (Array.isArray(roleUsers.data)) {
                userIds = roleUsers.data.map(user => user.id);
              } else if (roleUsers.data.items && Array.isArray(roleUsers.data.items)) {
                userIds = roleUsers.data.items.map(user => user.id);
              } else if (roleUsers.data.data && Array.isArray(roleUsers.data.data)) {
                userIds = roleUsers.data.data.map(user => user.id);
              }
            }

            console.log(`角色 ${roleId} 的用户ID列表:`, userIds);

            // 为每个用户发送通知
            for (const userId of userIds) {
              // 使用正确的API方法名
              await api.createNotification({
                ...notificationData,
                user_id: userId
              });
            }
          } catch (roleError) {
            console.error(`获取角色 ${roleId} 用户列表失败:`, roleError);
            message.warning(`无法获取角色 ${roleId} 的用户列表`);
          }
        }
      }

      message.success('通知创建成功');

      // 记录操作日志 - 使用通知API自动记录，不需要单独调用

      // 返回通知中心
      navigate('/project/notifications');
    } catch (error) {
      console.error('创建通知失败:', error);
      message.error('创建通知失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card
      title={
        <Space>
          <BellOutlined />
          <span>创建通知</span>
        </Space>
      }
      extra={
        <Button
          type="link"
          icon={<ArrowLeftOutlined />}
          onClick={() => navigate('/project/notifications')}
        >
          返回通知中心
        </Button>
      }
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        initialValues={{
          type: 'system',
          priority: 'normal',
          notify_type: 'user'
        }}
      >
        <Divider orientation="left">通知内容</Divider>
        <Row gutter={16}>
          <Col span={24}>
            <Form.Item
              name="title"
              label="通知标题"
              rules={[{ required: true, message: '请输入通知标题' }]}
            >
              <Input placeholder="请输入通知标题" maxLength={100} />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={24}>
            <Form.Item
              name="content"
              label="通知内容"
              rules={[{ required: true, message: '请输入通知内容' }]}
            >
              <TextArea
                placeholder="请输入通知内容"
                autoSize={{ minRows: 4, maxRows: 8 }}
                maxLength={500}
                showCount
              />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="type"
              label="通知类型"
              rules={[{ required: true, message: '请选择通知类型' }]}
            >
              <Select placeholder="请选择通知类型">
                <Option value="system">系统通知</Option>
                <Option value="task">任务通知</Option>
                <Option value="inventory">库存通知</Option>
                <Option value="order">订单通知</Option>
                <Option value="security">安全通知</Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="priority"
              label="优先级"
              rules={[{ required: true, message: '请选择优先级' }]}
            >
              <Radio.Group>
                <Radio.Button value="normal">普通</Radio.Button>
                <Radio.Button value="high">高</Radio.Button>
              </Radio.Group>
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={24}>
            <Form.Item
              name="target_url"
              label="目标链接"
              tooltip="点击通知后跳转的链接，可选"
            >
              <Input placeholder="请输入目标链接，例如: /project/tasks/123" />
            </Form.Item>
          </Col>
        </Row>

        <Divider orientation="left">接收者设置</Divider>
        <Row gutter={16}>
          <Col span={24}>
            <Form.Item
              name="notify_type"
              label="通知方式"
              rules={[{ required: true, message: '请选择通知方式' }]}
            >
              <Radio.Group onChange={handleNotifyTypeChange}>
                <Radio.Button value="user">
                  <UserOutlined /> 按用户通知
                </Radio.Button>
                <Radio.Button value="role">
                  <TeamOutlined /> 按角色通知
                </Radio.Button>
              </Radio.Group>
            </Form.Item>
          </Col>
        </Row>

        {notifyType === 'user' ? (
          <Form.Item
            name="user_ids"
            label="选择用户"
            rules={[{ required: true, message: '请选择至少一个用户' }]}
          >
            <Select
              mode="multiple"
              placeholder="请选择用户"
              loading={users.length === 0}
              optionFilterProp="children"
              showSearch
            >
              {users && users.length > 0 ? (
                users.map(user => (
                  <Option key={user.id} value={user.id}>
                    {user.full_name || user.username || user.name || user.email || `用户${user.id}`}
                  </Option>
                ))
              ) : (
                <Option disabled>暂无用户数据</Option>
              )}
            </Select>
          </Form.Item>
        ) : (
          <Form.Item
            name="role_ids"
            label="选择角色"
            rules={[{ required: true, message: '请选择至少一个角色' }]}
          >
            <Select
              mode="multiple"
              placeholder="请选择角色"
              loading={roles.length === 0}
              optionFilterProp="children"
            >
              {roles && roles.length > 0 ? (
                roles.map(role => (
                  <Option key={role.id} value={role.id}>
                    {role.name || role.title || role.role_name || `角色${role.id}`}
                  </Option>
                ))
              ) : (
                <Option disabled>暂无角色数据</Option>
              )}
            </Select>
          </Form.Item>
        )}

        <Form.Item>
          <Button
            type="primary"
            htmlType="submit"
            icon={<SendOutlined />}
            loading={loading}
          >
            发送通知
          </Button>
        </Form.Item>
      </Form>
    </Card>
  );
};

export default NotificationCreate;
