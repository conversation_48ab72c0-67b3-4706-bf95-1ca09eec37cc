#!/usr/bin/env python
# -*- coding: utf-8 -*-

from typing import List, Optional, Tuple, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import or_, and_, func, desc, extract
from datetime import datetime, timedelta, date, timezone
import uuid
import logging
from collections import defaultdict

from models.routine_task import RoutineTask, TaskComment
from models.user import User
from models.store import Store
from schemas.task import TaskCreate, TaskUpdate, TaskCommentCreate, TaskStatItem, TaskStatusStats, TaskTimeStatItem

# 设置日志记录器
logger = logging.getLogger(__name__)

class TaskService:
    """任务服务类"""

    @staticmethod
    async def get_tasks(
        db: AsyncSession,
        project_id: uuid.UUID,
        status: Optional[str] = None,
        priority: Optional[str] = None,
        category: Optional[str] = None,
        assignee_id: Optional[uuid.UUID] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        search: Optional[str] = None,
        skip: int = 0,
        limit: int = 100
    ) -> Tuple[List[Dict[str, Any]], int]:
        """获取任务列表"""
        try:
            # 构建查询
            query = select(RoutineTask).where(RoutineTask.project_id == project_id)

            # 应用过滤条件
            if status:
                query = query.where(RoutineTask.status == status)

            if priority:
                query = query.where(RoutineTask.priority == priority)

            if category:
                # 处理category参数，确保与前端传递的值匹配
                # 前端传递'store'时，匹配数据库中的'store'或'门店'
                if category.lower() == 'store':
                    query = query.where(or_(
                        RoutineTask.category == 'store',
                        RoutineTask.category == '门店'
                    ))
                else:
                    query = query.where(RoutineTask.category == category)

            if assignee_id:
                # 查询分配给该用户的任务或由该用户创建的任务
                query = query.where(
                    or_(
                        RoutineTask.assignee_ids.contains([str(assignee_id)]),
                        RoutineTask.created_by == assignee_id
                    )
                )

            # 日期过滤逻辑
            if start_date:
                # 如果有开始日期，过滤出开始日期大于等于指定日期的任务
                query = query.where(RoutineTask.start_date >= start_date)

            if end_date:
                # 如果有结束日期，过滤出截止日期小于等于指定日期的任务
                # 或者没有设置截止日期的任务（这样可以包含那些没有设置截止日期的任务）
                query = query.where(
                    or_(
                        RoutineTask.due_date <= end_date,
                        RoutineTask.due_date == None
                    )
                )

            if search:
                search_term = f"%{search}%"
                query = query.where(
                    or_(
                        RoutineTask.title.ilike(search_term),
                        RoutineTask.description.ilike(search_term)
                    )
                )

            # 计算总数
            count_query = select(func.count()).select_from(query.subquery())
            total = await db.scalar(count_query)

            # 应用分页
            query = query.order_by(desc(RoutineTask.created_at)).offset(skip).limit(limit)

            # 执行查询
            result = await db.execute(query)
            tasks = result.scalars().all()

            # 获取关联的用户和门店信息
            task_list = []
            for task in tasks:
                task_dict = {
                    "id": str(task.id),
                    "project_id": str(task.project_id),
                    "store_id": str(task.store_id) if task.store_id else None,
                    "store_ids": task.store_ids if hasattr(task, 'store_ids') else [],
                    "is_all_stores": task.is_all_stores if hasattr(task, 'is_all_stores') else False,
                    "title": task.title,
                    "description": task.description,
                    "task_type": task.tags[0] if task.tags and len(task.tags) > 0 else None,  # 从 tags 中获取第一个元素作为 task_type
                    "category": task.category,
                    "priority": task.priority,
                    "start_date": task.start_date,
                    "due_date": task.due_date,
                    "assignee_ids": task.assignee_ids,
                    "attachments": task.attachments,
                    "tags": task.tags,  # 添加 tags 字段
                    "status": task.status,
                    "completion_date": task.completed_date,  # 修正字段名
                    "completion_note": task.custom_fields.get("completion_note") if task.custom_fields and "completion_note" in task.custom_fields else None,  # 从 custom_fields 中获取
                    "created_by": str(task.created_by) if task.created_by else None,
                    "updated_by": str(task.updated_by) if task.updated_by else None,
                    "created_at": task.created_at,
                    "updated_at": task.updated_at,

                    # 从 custom_fields 中提取字段
                    "checklist": task.custom_fields.get("checklist") if task.custom_fields and "checklist" in task.custom_fields else None,
                    "supervisor_ids": task.custom_fields.get("supervisor_ids") if task.custom_fields and "supervisor_ids" in task.custom_fields else None,
                    "reminder": task.custom_fields.get("reminder") if task.custom_fields and "reminder" in task.custom_fields else None,

                    # 构建 recurrence 字段
                    "recurrence": {
                        "pattern": task.recurrence_pattern,
                        "interval": task.recurrence_interval,
                        "end_date": task.recurrence_end_date,
                        "count": task.recurrence_count
                    } if task.is_recurring else None
                }

                # 获取单个门店信息（向后兼容）
                if task.store_id:
                    store_query = select(Store).where(Store.id == task.store_id)
                    store_result = await db.execute(store_query)
                    store = store_result.scalar_one_or_none()
                    if store:
                        task_dict["store_name"] = store.name

                # 获取多个门店信息
                if hasattr(task, 'store_ids') and task.store_ids:
                    store_names = []
                    for store_id_str in task.store_ids:
                        try:
                            store_id = uuid.UUID(store_id_str)
                            store_query = select(Store).where(Store.id == store_id)
                            store_result = await db.execute(store_query)
                            store = store_result.scalar_one_or_none()
                            if store:
                                store_names.append(store.name)
                        except (ValueError, TypeError):
                            continue
                    task_dict["store_names"] = store_names

                # 获取负责人信息
                if task.assignee_ids:
                    assignee_names = []
                    for assignee_id in task.assignee_ids:
                        user_query = select(User).where(User.id == assignee_id)
                        user_result = await db.execute(user_query)
                        user = user_result.scalar_one_or_none()
                        if user:
                            assignee_names.append(user.full_name or user.username)
                    task_dict["assignee_names"] = assignee_names

                task_list.append(task_dict)

            return task_list, total
        except Exception as e:
            logger.error(f"获取任务列表失败: {str(e)}")
            raise e

    @staticmethod
    async def get_task_by_id(
        db: AsyncSession,
        project_id: uuid.UUID,
        task_id: uuid.UUID
    ) -> Optional[Dict[str, Any]]:
        """根据ID获取任务"""
        try:
            # 构建查询
            query = select(RoutineTask).where(
                and_(
                    RoutineTask.id == task_id,
                    RoutineTask.project_id == project_id
                )
            )

            # 执行查询
            result = await db.execute(query)
            task = result.scalar_one_or_none()

            if not task:
                return None

            # 构建任务字典
            task_dict = {
                "id": str(task.id),
                "project_id": str(task.project_id),
                "store_id": str(task.store_id) if task.store_id else None,
                "store_ids": task.store_ids if hasattr(task, 'store_ids') else [],
                "is_all_stores": task.is_all_stores if hasattr(task, 'is_all_stores') else False,
                "title": task.title,
                "description": task.description,
                "task_type": task.tags[0] if task.tags and len(task.tags) > 0 else None,  # 从 tags 中获取第一个元素作为 task_type
                "category": task.category,
                "priority": task.priority,
                "start_date": task.start_date,
                "due_date": task.due_date,
                "assignee_ids": task.assignee_ids,
                "attachments": task.attachments,
                "tags": task.tags,  # 添加 tags 字段
                "status": task.status,
                "completion_date": task.completed_date,  # 修正字段名
                "completion_note": task.custom_fields.get("completion_note") if task.custom_fields and "completion_note" in task.custom_fields else None,  # 从 custom_fields 中获取
                "created_by": str(task.created_by) if task.created_by else None,
                "updated_by": str(task.updated_by) if task.updated_by else None,
                "created_at": task.created_at,
                "updated_at": task.updated_at,

                # 从 custom_fields 中提取字段
                "checklist": task.custom_fields.get("checklist") if task.custom_fields and "checklist" in task.custom_fields else None,
                "supervisor_ids": task.custom_fields.get("supervisor_ids") if task.custom_fields and "supervisor_ids" in task.custom_fields else None,
                "reminder": task.custom_fields.get("reminder") if task.custom_fields and "reminder" in task.custom_fields else None,

                # 构建 recurrence 字段
                "recurrence": {
                    "pattern": task.recurrence_pattern,
                    "interval": task.recurrence_interval,
                    "end_date": task.recurrence_end_date,
                    "count": task.recurrence_count
                } if task.is_recurring else None
            }

            # 获取单个门店信息（向后兼容）
            if task.store_id:
                store_query = select(Store).where(Store.id == task.store_id)
                store_result = await db.execute(store_query)
                store = store_result.scalar_one_or_none()
                if store:
                    task_dict["store_name"] = store.name

            # 获取多个门店信息
            if hasattr(task, 'store_ids') and task.store_ids:
                store_names = []
                for store_id_str in task.store_ids:
                    try:
                        store_id = uuid.UUID(store_id_str)
                        store_query = select(Store).where(Store.id == store_id)
                        store_result = await db.execute(store_query)
                        store = store_result.scalar_one_or_none()
                        if store:
                            store_names.append(store.name)
                    except (ValueError, TypeError):
                        continue
                task_dict["store_names"] = store_names

            # 获取负责人信息
            if task.assignee_ids:
                assignee_names = []
                for assignee_id in task.assignee_ids:
                    user_query = select(User).where(User.id == assignee_id)
                    user_result = await db.execute(user_query)
                    user = user_result.scalar_one_or_none()
                    if user:
                        assignee_names.append(user.full_name or user.username)
                task_dict["assignee_names"] = assignee_names

            return task_dict
        except Exception as e:
            logger.error(f"获取任务详情失败: {str(e)}")
            raise e

    @staticmethod
    async def create_task(
        db: AsyncSession,
        project_id: uuid.UUID,
        task_data: TaskCreate,
        created_by: uuid.UUID
    ) -> Dict[str, Any]:
        """创建任务"""
        try:
            # 处理门店信息
            store_id = None
            store_ids = []
            is_all_stores = False

            # 如果指定了 is_all_stores
            if hasattr(task_data, 'is_all_stores') and task_data.is_all_stores:
                is_all_stores = True
            # 如果指定了 store_ids
            elif hasattr(task_data, 'store_ids') and task_data.store_ids:
                store_ids = [str(store_id) for store_id in task_data.store_ids]
                # 如果只有一个门店，也设置 store_id 以保持向后兼容
                if len(store_ids) == 1:
                    store_id = task_data.store_ids[0]
            # 如果指定了单个 store_id
            elif task_data.store_id:
                store_id = task_data.store_id
                store_ids = [str(task_data.store_id)]

            # 创建任务
            # 将 task_type 转换为 tags
            tags = []
            if hasattr(task_data, 'task_type') and task_data.task_type:
                tags = [task_data.task_type]

            # 创建任务
            task_kwargs = {
                "project_id": project_id,
                "store_id": store_id,
                "store_ids": store_ids,
                "is_all_stores": is_all_stores,
                "title": task_data.title,
                "description": task_data.description,
                "tags": tags,  # 使用 tags 字段替代 task_type
                "category": task_data.category,
                "priority": task_data.priority,
                "start_date": task_data.start_date,
                "due_date": task_data.due_date,
                "assignee_ids": task_data.assignee_ids,
                "attachments": task_data.attachments,
                "status": task_data.status,
                "created_by": created_by,
                "updated_by": created_by
            }

            # 处理重复规则
            if hasattr(task_data, 'recurrence') and task_data.recurrence:
                # 如果有重复规则，设置 is_recurring 为 True
                task_kwargs["is_recurring"] = True

                # 从 recurrence 字典中提取相关字段
                if 'pattern' in task_data.recurrence:
                    task_kwargs["recurrence_pattern"] = task_data.recurrence['pattern']
                if 'interval' in task_data.recurrence:
                    task_kwargs["recurrence_interval"] = task_data.recurrence['interval']
                if 'end_date' in task_data.recurrence:
                    task_kwargs["recurrence_end_date"] = task_data.recurrence['end_date']
                if 'count' in task_data.recurrence:
                    task_kwargs["recurrence_count"] = task_data.recurrence['count']

            # 将 checklist 和 supervisor_ids 存储在 custom_fields 中
            custom_fields = {}
            if hasattr(task_data, 'checklist') and task_data.checklist:
                custom_fields["checklist"] = task_data.checklist
            if hasattr(task_data, 'supervisor_ids') and task_data.supervisor_ids:
                custom_fields["supervisor_ids"] = [str(sid) for sid in task_data.supervisor_ids]
            if hasattr(task_data, 'reminder') and task_data.reminder:
                custom_fields["reminder"] = task_data.reminder

            if custom_fields:
                task_kwargs["custom_fields"] = custom_fields

            # 确保所有 UUID 类型的字段都被转换为字符串
            if task_kwargs.get("assignee_ids"):
                task_kwargs["assignee_ids"] = [str(uid) for uid in task_kwargs["assignee_ids"]]

            # 确保 store_ids 中的所有 UUID 都被转换为字符串
            if task_kwargs.get("store_ids"):
                task_kwargs["store_ids"] = [str(sid) for sid in task_kwargs["store_ids"]]

            # 创建任务
            task = RoutineTask(**task_kwargs)

            db.add(task)
            await db.commit()
            await db.refresh(task)

            # 获取任务详情
            task_dict = await TaskService.get_task_by_id(
                db=db,
                project_id=project_id,
                task_id=task.id
            )

            return task_dict
        except Exception as e:
            await db.rollback()
            logger.error(f"创建任务失败: {str(e)}")
            raise e

    @staticmethod
    async def update_task(
        db: AsyncSession,
        project_id: uuid.UUID,
        task_id: uuid.UUID,
        task_data: TaskUpdate,
        updated_by: uuid.UUID
    ) -> Dict[str, Any]:
        """更新任务"""
        try:
            # 获取任务
            query = select(RoutineTask).where(
                and_(
                    RoutineTask.id == task_id,
                    RoutineTask.project_id == project_id
                )
            )
            result = await db.execute(query)
            task = result.scalar_one_or_none()

            if not task:
                raise ValueError("任务不存在")

            # 处理门店信息
            task_data_dict = task_data.dict(exclude_unset=True)

            # 如果更新了 is_all_stores
            if 'is_all_stores' in task_data_dict and task_data_dict['is_all_stores']:
                task.is_all_stores = True
                task.store_ids = []
                task.store_id = None
            # 如果更新了 store_ids
            elif 'store_ids' in task_data_dict and task_data_dict['store_ids']:
                store_ids = [str(sid) for sid in task_data_dict['store_ids']]
                task.store_ids = store_ids
                task.is_all_stores = False
                # 如果只有一个门店，也设置 store_id 以保持向后兼容
                if len(store_ids) == 1:
                    task.store_id = task_data_dict['store_ids'][0]
                else:
                    task.store_id = None
            # 如果更新了单个 store_id
            elif 'store_id' in task_data_dict and task_data_dict['store_id']:
                task.store_id = task_data_dict['store_id']
                task.store_ids = [str(task_data_dict['store_id'])]
                task.is_all_stores = False

            # 移除已处理的字段
            for field in ['store_id', 'store_ids', 'is_all_stores']:
                if field in task_data_dict:
                    del task_data_dict[field]

            # 更新其他任务字段
            for key, value in task_data_dict.items():
                # 处理 UUID 类型的字段
                if key == "assignee_ids" and value:
                    value = [str(uid) for uid in value]
                # 处理 tags 字段
                elif key == "task_type" and value:
                    # 如果更新了 task_type，将其转换为 tags
                    task.tags = [value]
                    continue

                setattr(task, key, value)

            # 更新更新人和更新时间
            task.updated_by = updated_by
            task.updated_at = datetime.utcnow()

            await db.commit()
            await db.refresh(task)

            # 获取任务详情
            task_dict = await TaskService.get_task_by_id(
                db=db,
                project_id=project_id,
                task_id=task.id
            )

            return task_dict
        except Exception as e:
            await db.rollback()
            logger.error(f"更新任务失败: {str(e)}")
            raise e

    @staticmethod
    async def update_task_status(
        db: AsyncSession,
        project_id: uuid.UUID,
        task_id: uuid.UUID,
        status: str,
        updated_by: uuid.UUID
    ) -> Dict[str, Any]:
        """更新任务状态"""
        try:
            # 获取任务
            query = select(RoutineTask).where(
                and_(
                    RoutineTask.id == task_id,
                    RoutineTask.project_id == project_id
                )
            )
            result = await db.execute(query)
            task = result.scalar_one_or_none()

            if not task:
                raise ValueError("任务不存在")

            # 更新任务状态
            task.status = status

            # 如果状态为已完成，设置完成日期
            if status == "completed" and not task.completed_date:
                task.completed_date = datetime.utcnow()

            # 更新更新人和更新时间
            task.updated_by = updated_by
            task.updated_at = datetime.utcnow()

            await db.commit()
            await db.refresh(task)

            # 获取任务详情
            task_dict = await TaskService.get_task_by_id(
                db=db,
                project_id=project_id,
                task_id=task.id
            )

            return task_dict
        except Exception as e:
            await db.rollback()
            logger.error(f"更新任务状态失败: {str(e)}")
            raise e

    @staticmethod
    async def delete_task(
        db: AsyncSession,
        project_id: uuid.UUID,
        task_id: uuid.UUID
    ) -> None:
        """删除任务"""
        try:
            # 获取任务
            query = select(RoutineTask).where(
                and_(
                    RoutineTask.id == task_id,
                    RoutineTask.project_id == project_id
                )
            )
            result = await db.execute(query)
            task = result.scalar_one_or_none()

            if not task:
                raise ValueError("任务不存在")

            # 删除任务
            await db.delete(task)
            await db.commit()
        except Exception as e:
            await db.rollback()
            logger.error(f"删除任务失败: {str(e)}")
            raise e

    @staticmethod
    async def get_task_comments(
        db: AsyncSession,
        task_id: uuid.UUID,
        skip: int = 0,
        limit: int = 100
    ) -> Tuple[List[Dict[str, Any]], int]:
        """获取任务评论列表"""
        try:
            # 构建查询
            query = select(TaskComment).where(TaskComment.task_id == task_id)

            # 计算总数
            count_query = select(func.count()).select_from(query.subquery())
            total = await db.scalar(count_query)

            # 应用分页
            query = query.order_by(desc(TaskComment.created_at)).offset(skip).limit(limit)

            # 执行查询
            result = await db.execute(query)
            comments = result.scalars().all()

            # 获取关联的用户信息
            comment_list = []
            for comment in comments:
                comment_dict = {
                    "id": str(comment.id),
                    "task_id": str(comment.task_id),
                    "content": comment.content,
                    "attachments": comment.attachments,
                    "created_by": str(comment.created_by) if comment.created_by else None,
                    "created_at": comment.created_at,
                    "updated_at": comment.updated_at
                }

                # 获取创建人信息
                if comment.created_by:
                    user_query = select(User).where(User.id == comment.created_by)
                    user_result = await db.execute(user_query)
                    user = user_result.scalar_one_or_none()
                    if user:
                        comment_dict["created_by_name"] = user.full_name or user.username

                comment_list.append(comment_dict)

            return comment_list, total
        except Exception as e:
            logger.error(f"获取任务评论列表失败: {str(e)}")
            raise e

    @staticmethod
    async def get_comment_by_id(
        db: AsyncSession,
        comment_id: uuid.UUID
    ) -> Optional[TaskComment]:
        """根据ID获取评论"""
        try:
            # 构建查询
            query = select(TaskComment).where(TaskComment.id == comment_id)

            # 执行查询
            result = await db.execute(query)
            comment = result.scalar_one_or_none()

            return comment
        except Exception as e:
            logger.error(f"获取评论详情失败: {str(e)}")
            raise e

    @staticmethod
    async def create_task_comment(
        db: AsyncSession,
        task_id: uuid.UUID,
        comment_data: TaskCommentCreate,
        created_by: uuid.UUID
    ) -> Dict[str, Any]:
        """创建任务评论"""
        try:
            # 创建评论
            comment = TaskComment(
                task_id=task_id,
                content=comment_data.content,
                attachments=comment_data.attachments,
                created_by=created_by
            )

            db.add(comment)
            await db.commit()
            await db.refresh(comment)

            # 构建评论字典
            comment_dict = {
                "id": str(comment.id),
                "task_id": str(comment.task_id),
                "content": comment.content,
                "attachments": comment.attachments,
                "created_by": str(comment.created_by) if comment.created_by else None,
                "created_at": comment.created_at,
                "updated_at": comment.updated_at
            }

            # 获取创建人信息
            if comment.created_by:
                user_query = select(User).where(User.id == comment.created_by)
                user_result = await db.execute(user_query)
                user = user_result.scalar_one_or_none()
                if user:
                    comment_dict["created_by_name"] = user.full_name or user.username

            return comment_dict
        except Exception as e:
            await db.rollback()
            logger.error(f"创建任务评论失败: {str(e)}")
            raise e

    @staticmethod
    async def delete_task_comment(
        db: AsyncSession,
        comment_id: uuid.UUID
    ) -> None:
        """删除任务评论"""
        try:
            # 获取评论
            query = select(TaskComment).where(TaskComment.id == comment_id)
            result = await db.execute(query)
            comment = result.scalar_one_or_none()

            if not comment:
                raise ValueError("评论不存在")

            # 删除评论
            await db.delete(comment)
            await db.commit()
        except Exception as e:
            await db.rollback()
            logger.error(f"删除任务评论失败: {str(e)}")
            raise e

    @staticmethod
    async def get_task_stats(
        db: AsyncSession,
        project_id: uuid.UUID,
        period: str = "month",
        category: Optional[str] = None,
        store_id: Optional[uuid.UUID] = None
    ) -> Dict[str, Any]:
        """获取任务统计数据"""
        try:
            # 根据统计周期设置日期范围
            start_date = None
            if period == "week":
                # 使用不带时区信息的日期，避免与数据库中的日期比较时出现问题
                start_date = datetime.now() - timedelta(days=7)
            elif period == "month":
                start_date = datetime.now() - timedelta(days=30)
            elif period == "year":
                start_date = datetime.now() - timedelta(days=365)

            # 构建基础查询
            query = select(RoutineTask).where(RoutineTask.project_id == project_id)

            # 应用日期过滤
            if start_date:
                # 确保使用不带时区信息的日期进行比较
                query = query.where(RoutineTask.created_at >= start_date)

            # 应用类别过滤
            if category:
                query = query.where(RoutineTask.category == category)

            # 应用门店过滤
            if store_id:
                query = query.where(
                    or_(
                        RoutineTask.store_id == store_id,
                        RoutineTask.store_ids.contains([str(store_id)]),
                        RoutineTask.is_all_stores == True
                    )
                )

            # 执行查询
            result = await db.execute(query)
            tasks = result.scalars().all()

            # 初始化统计数据
            status_stats = {
                "total": len(tasks),
                "pending": 0,
                "in_progress": 0,
                "completed": 0,
                "overdue": 0,
                "cancelled": 0
            }

            store_stats = defaultdict(int)
            category_stats = defaultdict(int)
            type_stats = defaultdict(int)
            priority_stats = defaultdict(int)
            time_stats = defaultdict(int)

            # 获取所有门店信息，用于名称映射
            store_query = select(Store)
            store_result = await db.execute(store_query)
            # 确保使用正确的格式存储门店ID和名称
            stores = {}
            for store in store_result.scalars().all():
                # 将UUID转换为字符串，确保格式一致
                store_id_str = str(store.id)
                stores[store_id_str] = store.name

            # 处理每个任务
            for task in tasks:
                # 状态统计
                status = task.status
                if status in status_stats:
                    status_stats[status] += 1

                # 门店统计
                if task.is_all_stores:
                    store_stats["所有门店"] += 1
                elif task.store_id:
                    # 确保使用正确的格式获取门店名称
                    store_id_str = str(task.store_id)
                    store_name = stores.get(store_id_str, "未知门店")
                    store_stats[store_name] += 1
                elif task.store_ids and isinstance(task.store_ids, list):
                    for store_id in task.store_ids:
                        # 确保使用正确的格式获取门店名称
                        store_id_str = str(store_id)
                        store_name = stores.get(store_id_str, "未知门店")
                        store_stats[store_name] += 1

                # 类别统计
                category_name = task.category
                category_stats[category_name] += 1

                # 任务类型统计
                task_type = task.tags[0] if task.tags and len(task.tags) > 0 else "未分类"
                type_stats[task_type] += 1

                # 优先级统计
                priority = task.priority
                priority_stats[priority] += 1

                # 时间统计
                if task.created_at:
                    date_key = task.created_at.strftime("%Y-%m-%d")
                    time_stats[date_key] += 1

            # 转换为响应格式
            status_stats_response = TaskStatusStats(
                total=status_stats["total"],
                pending=status_stats.get("pending", 0),
                in_progress=status_stats.get("in_progress", 0),
                completed=status_stats.get("completed", 0),
                overdue=status_stats.get("overdue", 0),
                cancelled=status_stats.get("cancelled", 0)
            )

            # 转换门店统计
            store_stats_response = [
                TaskStatItem(name=name, value=value)
                for name, value in sorted(store_stats.items(), key=lambda x: x[1], reverse=True)
            ]

            # 转换类别统计
            category_stats_response = [
                TaskStatItem(name=name, value=value)
                for name, value in sorted(category_stats.items(), key=lambda x: x[1], reverse=True)
            ]

            # 转换任务类型统计
            type_stats_response = [
                TaskStatItem(name=name, value=value)
                for name, value in sorted(type_stats.items(), key=lambda x: x[1], reverse=True)
            ]

            # 转换优先级统计
            priority_stats_response = [
                TaskStatItem(name=name, value=value)
                for name, value in sorted(priority_stats.items(), key=lambda x: x[1], reverse=True)
            ]

            # 转换时间统计
            time_stats_response = [
                TaskTimeStatItem(date=date, count=count)
                for date, count in sorted(time_stats.items())
            ]

            return {
                "status_stats": status_stats_response,
                "store_stats": store_stats_response,
                "category_stats": category_stats_response,
                "type_stats": type_stats_response,
                "priority_stats": priority_stats_response,
                "time_stats": time_stats_response
            }
        except Exception as e:
            logger.error(f"获取任务统计数据失败: {str(e)}")
            raise e
