API 命名规则
文件命名：
不使用  Service 后缀，例如 user.js 而不是 userService.js
使用小驼峰命名法，例如 serviceProvider.js
方法命名：
使用语义化的方法名，如 getList、getInfo、create、update、delete 等
对于特定的操作，可以使用更具体的名称，如 addUser、removeUser 等
参数命名：
使用语义化的参数名，如  tenantId、 projectId、 userId 等
对于查询参数，使用  params 作为参数名
返回值：
所有方法都返回 Promise
使用 async/await 语法
错误处理：
在方法内部进行参数验证，如果参数无效，抛出错误
使用 try/catch 捕获 API 调用错误
注释：
使用 JSDoc 格式的注释
包含方法描述、参数说明和返回值说明
导出：
默认导出整个对象
在 index.js 中导出所有模块
层次结构：
 admin - 最顶层超级管理员 API
 serviceProvider - 服务商 API
 tenant - 租户 API
 project - 项目 API
ID 设置：
在调用 API 前，确保设置了相应的 ID
使用 setTenantId、setProjectId、setServiceProviderId 等方法设置 ID
不使用旧的 API 结构：
不使用 api.js 中的方法
不使用 superAdmin 模块
这些规则将帮助我们在编写代码时保持一致性，并避免使用已废弃的 API 结构。