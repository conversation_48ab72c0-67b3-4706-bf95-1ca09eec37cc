from sqlalchemy import Colum<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Foreign<PERSON>ey, JSON, Text, Integer, UniqueConstraint
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid

from db.database import Base

class SystemConfig(Base):
    """系统配置模型"""
    __tablename__ = "system_configs"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id"), nullable=False)
    config_type = Column(String(50), nullable=False)  # theme, notification, security, general, third_party_login, etc.
    config_key = Column(String(100), nullable=False)
    config_value = Column(JSON, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 关联关系
    project = relationship("Project", back_populates="system_configs")

    __table_args__ = (
        UniqueConstraint('project_id', 'config_type', 'config_key', name='uq_config_per_project'),
    )

    def __repr__(self):
        return f"<SystemConfig(id={self.id}, project_id={self.project_id}, type='{self.config_type}', key='{self.config_key}')>"
