#!/usr/bin/env python
# -*- coding: utf-8 -*-

from sqlalchemy import Column, String, Integer, Float, Text, Boolean, ForeignKey, DateTime, UniqueConstraint, Date
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid

from db.database import Base

class MonthlyInventory(Base):
    """月度盘点记录模型"""
    __tablename__ = "monthly_inventories"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=True)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id"), nullable=False)
    store_id = Column(UUID(as_uuid=True), ForeignKey("stores.id"), nullable=False)
    product_id = Column(UUID(as_uuid=True), ForeignKey("products.id"), nullable=False)
    month = Column(Date, nullable=False, comment="盘点月份")
    inventory_quantity = Column(Float, nullable=False, default=0, comment="库存量")
    actual_quantity = Column(Float, nullable=False, default=0, comment="实际量")
    system_quantity = Column(Float, nullable=True, comment="第三方ERP系统库存量")
    unit = Column(String(20), nullable=True, comment="单位")
    remark = Column(Text, nullable=True, comment="备注")
    status = Column(String(20), default="draft", nullable=False, comment="状态: draft, submitted, approved, rejected")
    submitted_at = Column(DateTime(timezone=True), nullable=True, comment="提交时间")
    audited_at = Column(DateTime(timezone=True), nullable=True, comment="审核时间")
    audit_remark = Column(Text, nullable=True, comment="审核备注")
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    created_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)
    updated_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)
    audited_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)

    # 关系
    tenant = relationship("Tenant", backref="monthly_inventories")
    project = relationship("Project", backref="monthly_inventories")
    store = relationship("Store", backref="monthly_inventories")
    product = relationship("Product", backref="monthly_inventories")
    creator = relationship("User", foreign_keys=[created_by])
    updater = relationship("User", foreign_keys=[updated_by])
    auditor = relationship("User", foreign_keys=[audited_by])

    __table_args__ = (
        UniqueConstraint('project_id', 'store_id', 'product_id', 'month', name='uq_monthly_inventory_per_store_product_month'),
    )

    @property
    def difference(self):
        """计算差异"""
        return self.actual_quantity - self.inventory_quantity

    @property
    def difference_rate(self):
        """计算差异率"""
        if self.inventory_quantity == 0:
            return None
        return (self.actual_quantity - self.inventory_quantity) / self.inventory_quantity * 100

    @property
    def system_difference(self):
        """计算与系统库存的差异"""
        if self.system_quantity is None:
            return None
        return self.actual_quantity - self.system_quantity

    @property
    def system_difference_rate(self):
        """计算与系统库存的差异率"""
        if self.system_quantity is None or self.system_quantity == 0:
            return None
        return (self.actual_quantity - self.system_quantity) / self.system_quantity * 100

    @property
    def difference_amount(self):
        """计算差异金额（需要商品价格）"""
        if hasattr(self, 'product') and self.product and hasattr(self.product, 'cost_price') and self.product.cost_price:
            return self.difference * self.product.cost_price
        return 0

    def __repr__(self):
        return f"<MonthlyInventory(id={self.id}, store_id='{self.store_id}', product_id='{self.product_id}', month='{self.month}')>"


class MonthlyInventorySummary(Base):
    """月度盘点汇总模型"""
    __tablename__ = "monthly_inventory_summaries"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=True)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id"), nullable=False)
    store_id = Column(UUID(as_uuid=True), ForeignKey("stores.id"), nullable=False)
    month = Column(Date, nullable=False, comment="盘点月份")
    total_items = Column(Integer, default=0, comment="总盘点商品数")
    completed_items = Column(Integer, default=0, comment="已完成盘点商品数")
    diff_items = Column(Integer, default=0, comment="有差异商品数")
    total_diff_amount = Column(Float, default=0, comment="总差异金额")
    status = Column(String(20), default="draft", nullable=False, comment="状态: draft, submitted, approved, rejected")
    submitted_at = Column(DateTime(timezone=True), nullable=True, comment="提交时间")
    audited_at = Column(DateTime(timezone=True), nullable=True, comment="审核时间")
    audit_remark = Column(Text, nullable=True, comment="审核备注")
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    created_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)
    updated_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)
    audited_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)

    # 关系
    tenant = relationship("Tenant", backref="monthly_inventory_summaries")
    project = relationship("Project", backref="monthly_inventory_summaries")
    store = relationship("Store", backref="monthly_inventory_summaries")
    creator = relationship("User", foreign_keys=[created_by])
    updater = relationship("User", foreign_keys=[updated_by])
    auditor = relationship("User", foreign_keys=[audited_by])

    __table_args__ = (
        UniqueConstraint('project_id', 'store_id', 'month', name='uq_monthly_inventory_summary_per_store_month'),
    )

    @property
    def completion_rate(self):
        """计算完成率"""
        if self.total_items == 0:
            return 0
        return self.completed_items / self.total_items * 100

    @property
    def diff_rate(self):
        """计算差异率"""
        if self.total_items == 0:
            return 0
        return self.diff_items / self.total_items * 100

    def __repr__(self):
        return f"<MonthlyInventorySummary(id={self.id}, store_id='{self.store_id}', month='{self.month}')>"


class MonthlyInventoryUpload(Base):
    """月度盘点上传记录模型"""
    __tablename__ = "monthly_inventory_uploads"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=True)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id"), nullable=False)
    store_id = Column(UUID(as_uuid=True), ForeignKey("stores.id"), nullable=False)
    month = Column(Date, nullable=False, comment="盘点月份")
    filename = Column(String(255), nullable=False, comment="文件名")
    file_path = Column(String(500), nullable=False, comment="文件路径")
    total_rows = Column(Integer, default=0, comment="总行数")
    success_rows = Column(Integer, default=0, comment="成功行数")
    error_rows = Column(Integer, default=0, comment="错误行数")
    error_details = Column(Text, nullable=True, comment="错误详情")
    status = Column(String(20), default="processing", nullable=False, comment="状态: processing, completed, failed")
    remark = Column(Text, nullable=True, comment="备注")
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    created_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)

    # 关系
    tenant = relationship("Tenant", backref="monthly_inventory_uploads")
    project = relationship("Project", backref="monthly_inventory_uploads")
    store = relationship("Store", backref="monthly_inventory_uploads")
    creator = relationship("User", foreign_keys=[created_by])

    def __repr__(self):
        return f"<MonthlyInventoryUpload(id={self.id}, filename='{self.filename}', status='{self.status}')>" 