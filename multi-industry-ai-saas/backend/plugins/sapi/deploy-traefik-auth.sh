#!/bin/bash

# Traefik鉴权微服务部署脚本
# 用于启动SAPI插件的Traefik鉴权优化

set -e

echo "🚀 开始部署SAPI Traefik鉴权微服务..."

# 检查Docker和Docker Compose
if ! command -v docker &> /dev/null; then
    echo "❌ Docker未安装，请先安装Docker"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose未安装，请先安装Docker Compose"
    exit 1
fi

# 设置工作目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

echo "📁 工作目录: $SCRIPT_DIR"

# 检查必要文件
REQUIRED_FILES=(
    "Dockerfile.traefik-auth"
    "docker-compose.traefik-auth.yml"
    "services/traefik_auth_service.py"
)

for file in "${REQUIRED_FILES[@]}"; do
    if [[ ! -f "$file" ]]; then
        echo "❌ 缺少必要文件: $file"
        exit 1
    fi
done

echo "✅ 所有必要文件检查通过"

# 检查环境变量文件
if [[ ! -f "../../.env" ]]; then
    echo "⚠️  未找到.env文件，将使用默认配置"
    echo "请确保以下环境变量已设置："
    echo "  - DATABASE_URL"
    echo "  - REDIS_URL"
    echo "  - SECRET_KEY"
    echo "  - JWT_SECRET_KEY"
    echo "  - SAPI_SECRET_KEY"
fi

# 创建网络（如果不存在）
echo "🌐 检查Docker网络..."
if ! docker network ls | grep -q "retail-ai-saas-network"; then
    echo "创建Docker网络: retail-ai-saas-network"
    docker network create retail-ai-saas-network
else
    echo "✅ Docker网络已存在"
fi

# 构建镜像
echo "🔨 构建Traefik鉴权微服务镜像..."
docker-compose -f docker-compose.traefik-auth.yml build

# 启动服务
echo "🚀 启动Traefik鉴权微服务..."
docker-compose -f docker-compose.traefik-auth.yml up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 健康检查
echo "🔍 检查服务健康状态..."
MAX_RETRIES=30
RETRY_COUNT=0

while [[ $RETRY_COUNT -lt $MAX_RETRIES ]]; do
    if curl -f http://localhost:8001/health > /dev/null 2>&1; then
        echo "✅ Traefik鉴权微服务启动成功！"
        break
    else
        echo "⏳ 等待服务启动... ($((RETRY_COUNT + 1))/$MAX_RETRIES)"
        sleep 2
        ((RETRY_COUNT++))
    fi
done

if [[ $RETRY_COUNT -eq $MAX_RETRIES ]]; then
    echo "❌ 服务启动超时，请检查日志"
    docker-compose -f docker-compose.traefik-auth.yml logs
    exit 1
fi

# 显示服务状态
echo "📊 服务状态:"
docker-compose -f docker-compose.traefik-auth.yml ps

# 显示服务信息
echo ""
echo "🎉 部署完成！"
echo ""
echo "📋 服务信息:"
echo "  - 鉴权服务地址: http://localhost:8001"
echo "  - 健康检查: http://localhost:8001/health"
echo "  - 服务指标: http://localhost:8001/metrics"
echo ""
echo "🔧 管理命令:"
echo "  - 查看日志: docker-compose -f docker-compose.traefik-auth.yml logs -f"
echo "  - 停止服务: docker-compose -f docker-compose.traefik-auth.yml down"
echo "  - 重启服务: docker-compose -f docker-compose.traefik-auth.yml restart"
echo ""
echo "📖 使用说明:"
echo "  1. 确保Traefik配置已更新（services.yml中的mcp-auth中间件）"
echo "  2. MCP服务器将自动使用Traefik直接路由进行鉴权"
echo "  3. 支持SAPI令牌验证和直接访问令牌"
echo ""
echo "✨ Traefik鉴权优化已启用，MCP服务性能将显著提升！" 