import api from '../httpClient';
import { getServiceProviderId } from '../httpClient';

/**
 * 服务商API
 */
const serviceProvider = {
  /**
   * 获取服务商列表
   * @param {Object} params - 查询参数
   * @returns {Promise} - 服务商列表
   */
  getList: async (params = {}) => {
    return api.get('/admin/service-providers', { params });
  },

  /**
   * 获取服务商信息
   * @param {string} id - 服务商ID
   * @returns {Promise} - 服务商信息
   */
  getInfo: async (id) => {
    const serviceProviderId = id || getServiceProviderId();
    if (!serviceProviderId) {
      throw new Error('未指定服务商ID');
    }
    return api.get(`/admin/service-providers/${serviceProviderId}`);
  },

  /**
   * 创建服务商
   * @param {Object} data - 服务商数据
   * @returns {Promise} - 创建结果
   */
  create: async (data) => {
    return api.post('/admin/service-providers', data);
  },

  /**
   * 更新服务商
   * @param {string} id - 服务商ID
   * @param {Object} data - 服务商数据
   * @returns {Promise} - 更新结果
   */
  update: async (id, data) => {
    const serviceProviderId = id || getServiceProviderId();
    if (!serviceProviderId) {
      throw new Error('未指定服务商ID');
    }
    return api.put(`/admin/service-providers/${serviceProviderId}`, data);
  },

  /**
   * 删除服务商
   * @param {string} id - 服务商ID
   * @returns {Promise} - 删除结果
   */
  delete: async (id) => {
    const serviceProviderId = id || getServiceProviderId();
    if (!serviceProviderId) {
      throw new Error('未指定服务商ID');
    }
    return api.delete(`/admin/service-providers/${serviceProviderId}`);
  }
};

export default serviceProvider;
