import React, { useEffect, useState } from 'react';
import {
  Form,
  Input,
  Button,
  Switch,
  message,
  Card,
  Row,
  Col,
  Tooltip,
  Select,
  Radio,
  Divider,
  Alert,
  Typography
} from 'antd';
import { CopyOutlined, InfoCircleOutlined } from '@ant-design/icons';
import dingtalkApi from '../service/dingtalkApi';

const { Paragraph, Text } = Typography;

// ==================== AI集成标签页组件 ====================
const AIIntegration = ({ settings, onUpdateSettings, aiAssistants }) => {
  const [form] = Form.useForm();
  const [formValues, setFormValues] = useState({});

  useEffect(() => {
    const initialValues = {
      dingtalk_ai_auth_method: 'none',
      dingtalk_api_key_auth_type: 'custom_header',
      ...settings,
    };
    form.setFieldsValue(initialValues);
    setFormValues(initialValues);
  }, [settings, form]);

  const handleValuesChange = (changedValues, allValues) => {
    setFormValues(allValues);
  };

  const handleCopyToClipboard = (text) => {
    if (navigator.clipboard && navigator.clipboard.writeText) {
      navigator.clipboard.writeText(text).then(() => {
        message.success('已成功复制到剪贴板');
      });
    } else {
      message.error('复制功能不可用，请手动复制或在HTTPS环境下操作');
    }
  };

  const generateYaml = () => {
    const finalDomain = formValues.domain || 'your-saas-domain.com';
    const passthroughUrl = `/public/plugin/dingtalk/ai-assistant/passthrough`;
    
    // 基础YAML结构
    const yamlBase = {
      openapi: '3.0.1',
      info: {
        title: 'AI助理通用直通',
        description: '将用户输入直接转发到后端服务进行处理，并以一次性JSON格式返回结果。',
        version: '1.0.0',
      },
      servers: [{ url: `https://${finalDomain}` }],
      paths: {
        [passthroughUrl]: {
          post: {
            summary: 'AI助理直通模式',
            description: '接收钉钉AI助理的请求，调用后端AI大模型服务，并以JSON对象一次性返回结果。',
            operationId: 'invokeAIAssistant',
            requestBody: {
              description: '钉钉回调时发送的请求体',
              required: true,
              content: {
                'application/json': {
                  schema: {
                    type: 'object',
                    properties: {
                      corpId: { type: 'string', description: '企业ID，用于识别项目', 'x-dingtalk-context': { property: 'currentOrg', format: 'corpId' } },
                      threadId: { type: 'string', description: '会话ID，用于维持多轮对话', 'x-dingtalk-context': { property: 'currentConversation', format: 'threadId' } },
                      senderUserId: { type: 'string', description: '发送者员工ID', 'x-dingtalk-context': { property: 'currentUser', format: 'userId' } },
                      msgContent: { type: 'object', description: '用户输入的原始消息内容', 'x-dingtalk-context': { property: 'currentInput', format: 'raw' } },
                    },
                  },
                },
              },
            },
            responses: {
              '200': {
                description: '成功，以JSON对象返回',
                content: {
                  'application/json': {
                    schema: {
                      type: 'object',
                      properties: {
                        text: {
                          type: 'object',
                          properties: {
                            content: {
                              type: 'string',
                              description: 'AI响应的文本内容.'
                            }
                          },
                          required: ['content']
                        }
                      },
                      required: ['text']
                    }
                  },
                },
              },
            },
          },
        },
      },
      components: {}, // 初始化components
    };

    // 根据鉴权方式动态添加 securitySchemes 和 security
    const authMethod = formValues.dingtalk_ai_auth_method;
    if (authMethod === 'api_key') {
      const headerName = formValues.dingtalk_custom_header_name || 'X-Api-Key';
      yamlBase.components.securitySchemes = {
        apiKeyAuth: { type: 'apiKey', in: 'header', name: headerName },
      };
      yamlBase.paths[passthroughUrl].post.security = [{ apiKeyAuth: [] }];
    } else if (authMethod === 'oauth') {
      const authUrl = `https://${finalDomain}/api/v1/plugin/dingtalk/oauth/authorize`;
      const tokenUrl = `https://${finalDomain}/api/v1/plugin/dingtalk/oauth/token`;
      yamlBase.components.securitySchemes = {
        oauth2Auth: {
          type: 'oauth2',
          flows: {
            authorizationCode: {
              authorizationUrl: authUrl,
              tokenUrl: tokenUrl,
              scopes: { read: 'Grants read access', write: 'Grants write access' },
            },
          },
        },
      };
      yamlBase.paths[passthroughUrl].post.security = [{ oauth2Auth: ['read', 'write'] }];
    }

    // 使用 safeDump 转换成格式规范的YAML字符串
    try {
        const yaml = require('js-yaml');
        return yaml.dump(yamlBase, { indent: 2, noRefs: true });
    } catch (e) {
        console.error("需要 js-yaml 库来生成YAML。请运行 'npm install js-yaml'");
        return "请安装 js-yaml 库以预览YAML";
    }
  };

  const projectId = localStorage.getItem('currentProjectId');
  const domain = formValues.domain || 'your-saas-domain.com';
  const redirectUrl = `https://${domain}/public/plugin/dingtalk/oauth/callback`;
  const authUrl = `https://${domain}/public/plugin/dingtalk/oauth/authorize`;
  const tokenUrl = `https://${domain}/public/plugin/dingtalk/oauth/token`;


  return (
    <Card title="AI助理直通模式配置" style={{ marginBottom: 24 }}>
      <Form form={form} layout="vertical" onFinish={onUpdateSettings} onValuesChange={handleValuesChange}>
        <Form.Item label="启用AI助理直通模式" name="ai_passthrough_enabled" valuePropName="checked">
          <Switch />
        </Form.Item>

        {formValues.ai_passthrough_enabled && (
          <>
            <Form.Item label="系统域名" name="domain" rules={[{ required: true }]} tooltip="用于自动生成OAuth地址和YAML，请确保公网可访问">
              <Input placeholder="your-saas-domain.com" />
            </Form.Item>

            <Card size="small" title={<Text strong>第一步：配置接口鉴权 (提供给钉钉)</Text>} style={{ marginBottom: '24px' }}>
              <Form.Item label="鉴权方式" name="dingtalk_ai_auth_method" help='请选择一种鉴权方式，并将下方生成的配置信息填写到钉钉AI助理的技能配置后台。'>
                <Select>
                  <Select.Option value="none">无 (仅使用基础签名验证)</Select.Option>
                  <Select.Option value="api_key">API密钥</Select.Option>
                  <Select.Option value="oauth">OAuth 2.0</Select.Option>
                </Select>
              </Form.Item>

              {formValues.dingtalk_ai_auth_method === 'none' && (
                  <Alert message="当前模式仅依赖第二步中回填的凭证进行基础签名验证。" type="info" showIcon />
              )}
              
              {formValues.dingtalk_ai_auth_method === 'api_key' && (
                <Card size="small" style={{ backgroundColor: '#f9f9f9' }}>
                    <Alert type="info" showIcon style={{ marginBottom: 16}} message={(
                        <div>
                           <Paragraph>请在下方自定义您的`Header 名称`和`API 密钥`，然后将它们复制并填写到钉钉后台对应的输入框中。</Paragraph>
                        </div>
                    )}/>
                  <Form.Item label="Header名称 (复制到钉钉)" name="dingtalk_custom_header_name" rules={[{ required: true }]}><Input placeholder="例如：X-Api-Key" /></Form.Item>
                  <Form.Item label="API密钥 (复制到钉钉)" name="dingtalk_custom_api_key" rules={[{ required: true }]}><Input.Password placeholder="请输入自定义的API密钥" /></Form.Item>
                </Card>
              )}

              {formValues.dingtalk_ai_auth_method === 'oauth' && (
                <Card size="small" style={{ backgroundColor: '#f9f9f9' }}>
                    <Alert type="info" showIcon style={{ marginBottom: 16}} message={(
                        <div>
                            <Paragraph>请将以下由本系统生成的地址复制到钉钉后台"OAuth 2.0"的对应配置项中。</Paragraph>
                        </div>
                    )}/>
                    <Form.Item label="Authorization URL (复制到钉钉)">
                        <Input readOnly value={authUrl} addonAfter={<Button size="small" icon={<CopyOutlined />} onClick={() => handleCopyToClipboard(authUrl)} />} />
                    </Form.Item>
                     <Form.Item label="Token URL (复制到钉钉)">
                        <Input readOnly value={tokenUrl} addonAfter={<Button size="small" icon={<CopyOutlined />} onClick={() => handleCopyToClipboard(tokenUrl)} />} />
                    </Form.Item>
                     <Form.Item label="Redirect URL (复制到钉钉)">
                        <Input readOnly value={redirectUrl} addonAfter={<Button size="small" icon={<CopyOutlined />} onClick={() => handleCopyToClipboard(redirectUrl)} />} />
                    </Form.Item>
                </Card>
              )}
            </Card>

            <Card size="small" title={<Text strong>第二步：回填钉钉凭证 (从钉钉获取)</Text>} style={{ marginBottom: '24px', border: '1px solid #1890ff' }}>
              <Alert
                type="info" showIcon message="为什么需要回填？"
                description='在钉钉后台完成技能配置后，钉钉会生成一套凭证。请将它们复制到此处，以便我们的后端服务在收到钉钉请求时，能够验证其合法性并识别其归属于您的项目。'
                style={{ marginBottom: 16, border: 'none', backgroundColor: 'transparent' }}
              />
              <Row gutter={24}>
                <Col span={12}><Form.Item label="Assistant ID (从钉钉获取)" name="dingtalk_ai_assistant_id" rules={[{ required: true }]}><Input /></Form.Item></Col>
                <Col span={12}><Form.Item label="Client ID (从钉钉获取)" name="dingtalk_ai_client_id" rules={[{ required: true }]}><Input /></Form.Item></Col>
              </Row>
              <Form.Item label="Client Secret (从钉钉获取)" name="dingtalk_ai_client_secret" rules={[{ required:true }]}><Input.Password /></Form.Item>
            </Card>

            <Form.Item name="passthrough_ai_assistant_id" label="选择绑定的系统AI助手" rules={[{ required: true }]}>
              <Select placeholder="请选择一个系统AI助手">
                {aiAssistants && aiAssistants.map(assistant => <Select.Option key={assistant.id} value={assistant.id}>{assistant.name}</Select.Option>)}
              </Select>
            </Form.Item>
            
            <Divider>直通模式自定义技能配置 (YAML)</Divider>
            <div style={{ position: 'relative', backgroundColor: '#f5f5f5', padding: '16px', borderRadius: '4px' }}>
              <Tooltip title="复制YAML"><Button icon={<CopyOutlined />} style={{ position: 'absolute', top: 8, right: 8, zIndex: 1 }} onClick={() => handleCopyToClipboard(generateYaml())} /></Tooltip>
              <pre style={{ margin: 0, whiteSpace: 'pre-wrap', wordBreak: 'break-all' }}><code>{generateYaml()}</code></pre>
            </div>
          </>
        )}
        <Form.Item style={{ marginTop: 24 }}><Button type="primary" htmlType="submit">保存AI集成设置</Button></Form.Item>
      </Form>
    </Card>
  );
};

export default AIIntegration; 