from sqlalchemy import Column, String, Boolean, DateTime, ForeignKey, JSON, Text, Integer, UniqueConstraint, Float
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid

from db.database import Base

class InventoryItem(Base):
    """库存项模型"""
    __tablename__ = "inventory_items"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=True)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id"), nullable=False)
    product_id = Column(UUID(as_uuid=True), ForeignKey("products.id"), nullable=False)
    warehouse_id = Column(UUID(as_uuid=True), ForeignKey("warehouses.id"), nullable=False)
    quantity = Column(Integer, default=0)
    min_quantity = Column(Integer, default=0)
    max_quantity = Column(Integer, nullable=True)
    is_slow_moving = Column(Boolean, default=False)  # 是否滞销
    last_movement_date = Column(DateTime(timezone=True), nullable=True)  # 最后一次移动日期
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    created_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)
    updated_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)

    # 关系
    product = relationship("Product", back_populates="inventory_items")
    warehouse = relationship("Warehouse", back_populates="inventory_items")
    tenant = relationship("Tenant", back_populates="inventory_items")
    project = relationship("Project", back_populates="inventory_items")
    history = relationship("InventoryHistory", back_populates="inventory_item", cascade="all, delete-orphan")

    __table_args__ = (
        UniqueConstraint('project_id', 'product_id', 'warehouse_id', name='uq_inventory_item_per_product_warehouse'),
        {'extend_existing': True}
    )

    def __repr__(self):
        return f"<InventoryItem(id={self.id}, product_id='{self.product_id}', quantity={self.quantity})>"


class InventoryHistory(Base):
    """库存历史记录模型"""
    __tablename__ = "inventory_history"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    inventory_item_id = Column(UUID(as_uuid=True), ForeignKey("inventory_items.id"), nullable=False)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=True)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id"), nullable=False)
    action_type = Column(String(20), nullable=False)  # add, update, delete, transfer_in, transfer_out, import
    previous_quantity = Column(Integer, nullable=False)
    new_quantity = Column(Integer, nullable=False)
    change_amount = Column(Integer, nullable=False)
    reference_id = Column(UUID(as_uuid=True), nullable=True)  # 关联的单据ID，如采购单、调拨单等
    reference_type = Column(String(50), nullable=True)  # 关联的单据类型
    notes = Column(Text, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    created_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)

    # 关系
    inventory_item = relationship("InventoryItem", back_populates="history")
    tenant = relationship("Tenant", backref="inventory_history")
    project = relationship("Project", backref="inventory_history")
    operator = relationship("User", foreign_keys=[created_by])

    def __repr__(self):
        return f"<InventoryHistory(id={self.id}, action_type='{self.action_type}', change_amount={self.change_amount})>"

    __table_args__ = (
        {'extend_existing': True}
    )


class InventoryTransfer(Base):
    """库存调拨单模型"""
    __tablename__ = "inventory_transfers"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=True)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id"), nullable=False)
    transfer_number = Column(String(50), nullable=False)
    source_warehouse_id = Column(UUID(as_uuid=True), ForeignKey("warehouses.id"), nullable=False)
    target_warehouse_id = Column(UUID(as_uuid=True), ForeignKey("warehouses.id"), nullable=False)
    status = Column(String(20), default="draft", nullable=False)  # draft, confirmed, completed, cancelled
    transfer_date = Column(DateTime(timezone=True), nullable=False)
    notes = Column(Text, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    created_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)
    updated_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)

    # 关系
    source_warehouse = relationship("Warehouse", foreign_keys=[source_warehouse_id])
    target_warehouse = relationship("Warehouse", foreign_keys=[target_warehouse_id])
    tenant = relationship("Tenant", backref="inventory_transfers")
    project = relationship("Project", backref="inventory_transfers")
    items = relationship("InventoryTransferItem", back_populates="transfer", cascade="all, delete-orphan")

    __table_args__ = (
        UniqueConstraint('project_id', 'transfer_number', name='uq_transfer_number_per_project'),
        {'extend_existing': True}
    )

    def __repr__(self):
        return f"<InventoryTransfer(id={self.id}, transfer_number='{self.transfer_number}', status='{self.status}')>"


class InventoryTransferItem(Base):
    """库存调拨单明细模型"""
    __tablename__ = "inventory_transfer_items"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    transfer_id = Column(UUID(as_uuid=True), ForeignKey("inventory_transfers.id"), nullable=False)
    product_id = Column(UUID(as_uuid=True), ForeignKey("products.id"), nullable=False)
    quantity = Column(Integer, nullable=False)
    notes = Column(Text, nullable=True)

    # 关系
    transfer = relationship("InventoryTransfer", back_populates="items")
    product = relationship("Product")

    def __repr__(self):
        return f"<InventoryTransferItem(id={self.id}, product_id='{self.product_id}', quantity={self.quantity})>"


class InventoryCheck(Base):
    """库存盘点单模型"""
    __tablename__ = "inventory_checks"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=True)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id"), nullable=False)
    check_number = Column(String(50), nullable=False)
    warehouse_id = Column(UUID(as_uuid=True), ForeignKey("warehouses.id"), nullable=False)
    status = Column(String(20), default="draft", nullable=False)  # draft, in_progress, completed, cancelled
    check_date = Column(DateTime(timezone=True), nullable=False)
    notes = Column(Text, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    created_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)
    updated_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)

    # 关系
    warehouse = relationship("Warehouse")
    tenant = relationship("Tenant", backref="inventory_checks")
    project = relationship("Project", backref="inventory_checks")
    items = relationship("InventoryCheckItem", back_populates="check", cascade="all, delete-orphan")

    __table_args__ = (
        UniqueConstraint('project_id', 'check_number', name='uq_check_number_per_project'),
        {'extend_existing': True}
    )

    def __repr__(self):
        return f"<InventoryCheck(id={self.id}, check_number='{self.check_number}', status='{self.status}')>"


class InventoryCheckItem(Base):
    """库存盘点单明细模型"""
    __tablename__ = "inventory_check_items"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    check_id = Column(UUID(as_uuid=True), ForeignKey("inventory_checks.id"), nullable=False)
    product_id = Column(UUID(as_uuid=True), ForeignKey("products.id"), nullable=False)
    system_quantity = Column(Integer, nullable=False)  # 系统库存数量
    actual_quantity = Column(Integer, nullable=False)  # 实际盘点数量
    difference = Column(Integer, nullable=False)  # 差异数量
    notes = Column(Text, nullable=True)

    # 关系
    check = relationship("InventoryCheck", back_populates="items")
    product = relationship("Product")

    def __repr__(self):
        return f"<InventoryCheckItem(id={self.id}, product_id='{self.product_id}', difference={self.difference})>"
