# MCP服务器增强功能说明

## 概述

本MCP服务器已经过优化，新增了对multipart/form-data、图片处理和流式服务的完整支持。这些功能使得MCP服务器能够处理更复杂的工具调用场景，包括文件上传、图片分析和实时数据流处理。

## 新增功能

### 1. Multipart/Form-Data支持

#### 功能特性
- **自动检测**：根据工具元数据和参数类型自动判断是否需要使用multipart/form-data
- **多种文件格式**：支持base64编码、URL、本地文件路径等多种文件输入格式
- **智能处理**：自动处理文件参数和普通表单字段的分离

#### 使用示例
```python
# 工具定义中指定支持multipart/form-data
TOOL_METADATA = {
    "file_upload": {
        "name": "file_upload",
        "description": "文件上传工具",
        "inputSchema": {
            "type": "object",
            "properties": {
                "files": {
                    "type": "array",
                    "items": {
                        "type": "string",
                        "format": "binary"  # 标记为文件类型
                    }
                }
            }
        },
        "content_type": "multipart/form-data"  # 显式指定
    }
}
```

#### 支持的文件输入格式
1. **Base64数据URL**：`data:image/jpeg;base64,/9j/4AAQ...`
2. **HTTP URL**：`https://example.com/image.jpg`
3. **本地文件路径**：`/path/to/file.jpg`
4. **原始字节数据**：直接的bytes对象

### 2. 图片和媒体处理

#### 智能内容识别
- **自动检测**：智能识别响应中的图片、音频、视频等媒体内容
- **格式转换**：自动将不同格式的媒体内容转换为MCP标准格式
- **元数据提取**：自动提取文件类型、大小等元数据信息

#### 支持的媒体类型
- **图片**：JPEG、PNG、GIF、WebP等
- **音频**：MP3、WAV、AAC等
- **视频**：MP4、WebM、AVI等
- **文档**：PDF、DOC、TXT等

#### 响应格式示例
```json
{
  "content": [
    {
      "type": "image",
      "data": "data:image/jpeg;base64,/9j/4AAQ...",
      "mimeType": "image/jpeg"
    },
    {
      "type": "text",
      "text": "图片分析结果..."
    }
  ],
  "isError": false
}
```

### 3. 流式服务支持

#### 功能特性
- **实时数据流**：支持SSE（Server-Sent Events）和NDJSON格式
- **分块传输**：支持大文件的分块传输和处理
- **进度跟踪**：实时报告处理进度和状态
- **错误处理**：完善的流式错误处理和恢复机制

#### 流式通知类型
1. **开始通知**：`notifications/toolStreamStart`
2. **数据块通知**：`notifications/toolStreamChunk`
3. **完成通知**：`notifications/toolStreamComplete`
4. **错误通知**：`notifications/toolStreamError`

#### 流式工具示例
```python
async def streaming_tool(session: MCPSession, params: Dict[str, Any]) -> None:
    """流式工具示例"""
    if not session.internal_message_queue:
        return
    
    # 发送开始通知
    start_notification = {
        "jsonrpc": "2.0",
        "method": "notifications/toolStreamStart",
        "params": {
            "toolName": "streaming_tool",
            "content": [{"type": "text", "text": "开始处理..."}]
        }
    }
    await session.internal_message_queue.put(start_notification)
    
    # 发送数据块
    for i in range(10):
        chunk_notification = {
            "jsonrpc": "2.0",
            "method": "notifications/toolStreamChunk",
            "params": {
                "toolName": "streaming_tool",
                "chunkIndex": i + 1,
                "content": [{"type": "text", "text": f"数据块 {i + 1}"}]
            }
        }
        await session.internal_message_queue.put(chunk_notification)
        await asyncio.sleep(0.5)
```

## 示例工具

### 1. 图片分析工具
```bash
# 调用示例
curl -X POST http://localhost:5000/mcp \
  -H "Content-Type: application/json" \
  -H "mcp-session-id: your-session-id" \
  -d '{
    "jsonrpc": "2.0",
    "id": 1,
    "method": "tools/call",
    "params": {
      "name": "image_analysis",
      "arguments": {
        "image": "data:image/jpeg;base64,/9j/4AAQ...",
        "analysis_type": "objects"
      }
    }
  }'
```

### 2. 文件上传工具
```bash
# 支持multipart/form-data的文件上传
curl -X POST http://localhost:5000/mcp \
  -H "mcp-session-id: your-session-id" \
  -F 'request={"jsonrpc":"2.0","id":1,"method":"tools/call","params":{"name":"file_upload","arguments":{"description":"测试文件"}}}' \
  -F 'files=@/path/to/file1.jpg' \
  -F 'files=@/path/to/file2.png'
```

### 3. 流式数据处理
```bash
# 启动流式处理（需要SSE客户端接收流式响应）
curl -X POST http://localhost:5000/mcp \
  -H "Content-Type: application/json" \
  -H "mcp-session-id: your-session-id" \
  -d '{
    "jsonrpc": "2.0",
    "id": 1,
    "method": "tools/call",
    "params": {
      "name": "streaming_processor",
      "arguments": {
        "data_source": "sample",
        "total_chunks": 10,
        "stream_mode": true
      }
    }
  }'

# 同时连接SSE端点接收流式数据
curl -N -H "mcp-session-id: your-session-id" \
  http://localhost:5000/mcp
```

## 配置说明

### 环境变量
```bash
# 文件处理相关
MCP_MAX_FILE_SIZE=10485760  # 最大文件大小（10MB）
MCP_TEMP_DIR=/tmp/mcp       # 临时文件目录

# 流式处理相关
MCP_STREAM_CHUNK_SIZE=8192  # 流式块大小
MCP_STREAM_TIMEOUT=30       # 流式超时时间

# 媒体处理相关
MCP_SUPPORTED_IMAGE_FORMATS=jpg,png,gif,webp
MCP_SUPPORTED_AUDIO_FORMATS=mp3,wav,aac
MCP_SUPPORTED_VIDEO_FORMATS=mp4,webm,avi
```

### 依赖包
```bash
# 安装额外依赖
pip install aiofiles python-magic pillow

# 可选：用于更好的文件类型检测
# Ubuntu/Debian
sudo apt-get install libmagic1

# macOS
brew install libmagic
```

## 性能优化

### 1. 文件处理优化
- **内存管理**：大文件采用流式处理，避免内存溢出
- **缓存机制**：常用文件类型检测结果缓存
- **并发处理**：支持多文件并发上传和处理

### 2. 流式处理优化
- **背压控制**：自动调节数据流速度，防止队列溢出
- **连接管理**：优化SSE连接的生命周期管理
- **错误恢复**：支持流式处理中断后的恢复机制

### 3. 媒体处理优化
- **格式转换**：支持常见媒体格式的高效转换
- **压缩优化**：智能压缩算法，平衡质量和大小
- **批量处理**：支持多媒体文件的批量处理

## 安全考虑

### 1. 文件安全
- **类型验证**：严格验证上传文件的类型和格式
- **大小限制**：限制单个文件和总文件大小
- **路径安全**：防止路径遍历攻击

### 2. 内容安全
- **恶意内容检测**：检测和过滤恶意文件内容
- **权限控制**：基于用户权限控制文件访问
- **审计日志**：记录所有文件操作的审计日志

### 3. 流式安全
- **连接限制**：限制单个用户的并发流式连接数
- **速率限制**：防止流式数据的滥用
- **资源保护**：防止流式处理消耗过多系统资源

## 故障排除

### 常见问题

1. **文件上传失败**
   ```bash
   # 检查文件大小和格式
   # 确认Content-Type设置正确
   # 验证multipart/form-data格式
   ```

2. **流式连接中断**
   ```bash
   # 检查网络连接稳定性
   # 确认SSE客户端实现正确
   # 查看服务器日志中的错误信息
   ```

3. **图片处理失败**
   ```bash
   # 验证图片格式是否支持
   # 检查base64编码是否正确
   # 确认图片文件未损坏
   ```

### 调试技巧

1. **启用详细日志**
   ```python
   import logging
   logging.getLogger('mcp').setLevel(logging.DEBUG)
   ```

2. **监控资源使用**
   ```bash
   # 监控内存使用
   ps aux | grep mcp
   
   # 监控文件描述符
   lsof -p <mcp_pid>
   ```

3. **测试工具**
   ```bash
   # 使用curl测试multipart上传
   # 使用SSE客户端测试流式连接
   # 使用图片工具验证格式转换
   ```

## 更新日志

### v2.1.0 (当前版本)
- ✅ 新增multipart/form-data支持
- ✅ 增强图片和媒体处理功能
- ✅ 完善流式服务支持
- ✅ 优化性能和安全性
- ✅ 添加详细的示例和文档

### 计划功能
- 🔄 WebSocket支持
- 🔄 更多媒体格式支持
- 🔄 分布式流式处理
- �� AI模型集成
- 🔄 实时协作功能 