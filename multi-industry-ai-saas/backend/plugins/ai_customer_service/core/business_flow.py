#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
业务流转处理器

基于意图识别，将客户消息转入相应的业务流程，并通知相关人员或角色。
专注于业务流转，而非客户消息的WebSocket推送。
"""

import logging
import uuid
from typing import Dict, Any, Optional, List
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession

from services.notification_service import NotificationService
from services.task import TaskService
from schemas.notification import NotificationCreate
from schemas.task import TaskCreate

logger = logging.getLogger(__name__)

class BusinessFlowHandler:
    """业务流转处理器"""
    
    def __init__(self):
        # 意图到业务流的映射
        self.intent_flow_mapping = {
            "complaint": {
                "flow_type": "complaint_handling",
                "target_roles": ["customer_service_manager", "operations_manager"],
                "priority": "high",
                "sla_hours": 2,
                "escalation_rules": {
                    "level_1": {"roles": ["customer_service_manager"], "timeout_hours": 1},
                    "level_2": {"roles": ["operations_manager"], "timeout_hours": 2},
                    "level_3": {"roles": ["general_manager"], "timeout_hours": 4}
                }
            },
            "partnership": {
                "flow_type": "partnership_inquiry",
                "target_roles": ["business_development", "partnership_manager"],
                "priority": "medium",
                "sla_hours": 24,
                "escalation_rules": {
                    "level_1": {"roles": ["business_development"], "timeout_hours": 8},
                    "level_2": {"roles": ["partnership_manager"], "timeout_hours": 24}
                }
            },
            "franchise": {
                "flow_type": "franchise_inquiry",
                "target_roles": ["franchise_manager", "regional_manager"],
                "priority": "medium",
                "sla_hours": 12,
                "escalation_rules": {
                    "level_1": {"roles": ["franchise_manager"], "timeout_hours": 4},
                    "level_2": {"roles": ["regional_manager"], "timeout_hours": 12}
                }
            },
            "investment": {
                "flow_type": "investment_inquiry",
                "target_roles": ["investment_manager", "finance_director"],
                "priority": "high",
                "sla_hours": 4,
                "escalation_rules": {
                    "level_1": {"roles": ["investment_manager"], "timeout_hours": 2},
                    "level_2": {"roles": ["finance_director"], "timeout_hours": 4}
                }
            },
            "technical_support": {
                "flow_type": "technical_support",
                "target_roles": ["technical_support", "senior_engineer"],
                "priority": "medium",
                "sla_hours": 8,
                "escalation_rules": {
                    "level_1": {"roles": ["technical_support"], "timeout_hours": 2},
                    "level_2": {"roles": ["senior_engineer"], "timeout_hours": 8}
                }
            },
            "sales_inquiry": {
                "flow_type": "sales_inquiry",
                "target_roles": ["sales_representative", "sales_manager"],
                "priority": "medium",
                "sla_hours": 6,
                "escalation_rules": {
                    "level_1": {"roles": ["sales_representative"], "timeout_hours": 2},
                    "level_2": {"roles": ["sales_manager"], "timeout_hours": 6}
                }
            },
            "refund": {
                "flow_type": "refund_request",
                "target_roles": ["finance_team", "customer_service_manager"],
                "priority": "high",
                "sla_hours": 4,
                "escalation_rules": {
                    "level_1": {"roles": ["finance_team"], "timeout_hours": 2},
                    "level_2": {"roles": ["customer_service_manager"], "timeout_hours": 4}
                }
            }
        }
        
        # 关键词到意图的映射（增强意图识别）
        self.keyword_intent_mapping = {
            "投诉": "complaint",
            "不满": "complaint", 
            "问题": "complaint",
            "退款": "refund",
            "退钱": "refund",
            "合作": "partnership",
            "代理": "partnership",
            "加盟": "franchise",
            "招商": "franchise",
            "投资": "investment",
            "融资": "investment",
            "技术": "technical_support",
            "故障": "technical_support",
            "销售": "sales_inquiry",
            "购买": "sales_inquiry",
            "价格": "sales_inquiry"
        }
        
    async def process_business_flow(
        self,
        db: AsyncSession,
        session_id: str,
        message: str,
        intent: str,
        customer_info: Dict[str, Any],
        project_id: uuid.UUID,
        platform_type: str = "",
        platform_user_id: str = ""
    ) -> Dict[str, Any]:
        """
        处理业务流转
        
        Args:
            db: 数据库会话
            session_id: 会话ID
            message: 用户消息
            intent: 识别的意图
            customer_info: 客户信息
            project_id: 项目ID
            platform_type: 平台类型
            platform_user_id: 平台用户ID
            
        Returns:
            Dict[str, Any]: 业务流转结果
        """
        try:
            # 增强意图识别
            enhanced_intent = await self._enhance_intent_recognition(message, intent)
            
            # 获取业务流配置
            flow_config = self.intent_flow_mapping.get(enhanced_intent)
            
            if not flow_config:
                # 没有匹配的业务流，返回默认处理
                return {
                    "flow_triggered": False,
                    "intent": enhanced_intent,
                    "message": "消息已记录，无需特殊业务流转"
                }
            
            # 创建业务流转记录
            flow_result = await self._create_business_flow(
                db=db,
                session_id=session_id,
                message=message,
                intent=enhanced_intent,
                flow_config=flow_config,
                customer_info=customer_info,
                project_id=project_id,
                platform_type=platform_type,
                platform_user_id=platform_user_id
            )
            
            # 通知相关人员
            await self._notify_stakeholders(
                db=db,
                flow_result=flow_result,
                flow_config=flow_config,
                project_id=project_id
            )
            
            # 创建任务
            await self._create_business_tasks(
                db=db,
                flow_result=flow_result,
                flow_config=flow_config,
                project_id=project_id
            )
            
            return {
                "flow_triggered": True,
                "intent": enhanced_intent,
                "flow_type": flow_config["flow_type"],
                "flow_id": flow_result["flow_id"],
                "target_roles": flow_config["target_roles"],
                "priority": flow_config["priority"],
                "sla_hours": flow_config["sla_hours"],
                "message": f"已触发{flow_config['flow_type']}业务流程"
            }
            
        except Exception as e:
            logger.error(f"处理业务流转失败: {e}")
            return {
                "flow_triggered": False,
                "error": str(e),
                "message": "业务流转处理失败"
            }
            
    async def _enhance_intent_recognition(self, message: str, original_intent: str) -> str:
        """增强意图识别"""
        try:
            # 基于关键词增强意图识别
            message_lower = message.lower()
            
            for keyword, intent in self.keyword_intent_mapping.items():
                if keyword in message_lower:
                    # 如果关键词匹配的意图优先级更高，则使用关键词意图
                    if intent in ["complaint", "refund", "investment"] and original_intent not in ["complaint", "refund", "investment"]:
                        return intent
                    elif intent == original_intent:
                        return intent
                        
            return original_intent
            
        except Exception as e:
            logger.error(f"增强意图识别失败: {e}")
            return original_intent
            
    async def _create_business_flow(
        self,
        db: AsyncSession,
        session_id: str,
        message: str,
        intent: str,
        flow_config: Dict[str, Any],
        customer_info: Dict[str, Any],
        project_id: uuid.UUID,
        platform_type: str,
        platform_user_id: str
    ) -> Dict[str, Any]:
        """创建业务流转记录"""
        try:
            flow_id = str(uuid.uuid4())
            
            # 这里应该创建业务流转记录到数据库
            # 暂时返回模拟数据
            
            flow_record = {
                "flow_id": flow_id,
                "session_id": session_id,
                "flow_type": flow_config["flow_type"],
                "intent": intent,
                "message": message,
                "customer_info": customer_info,
                "platform_type": platform_type,
                "platform_user_id": platform_user_id,
                "status": "initiated",
                "priority": flow_config["priority"],
                "sla_deadline": datetime.utcnow().timestamp() + flow_config["sla_hours"] * 3600,
                "created_at": datetime.utcnow().isoformat()
            }
            
            logger.info(f"创建业务流转记录: {flow_id}, 类型: {flow_config['flow_type']}")
            
            return flow_record
            
        except Exception as e:
            logger.error(f"创建业务流转记录失败: {e}")
            raise
            
    async def _notify_stakeholders(
        self,
        db: AsyncSession,
        flow_result: Dict[str, Any],
        flow_config: Dict[str, Any],
        project_id: uuid.UUID
    ):
        """通知相关利益相关者"""
        try:
            flow_type = flow_config["flow_type"]
            target_type = flow_config.get("target_type", "roles")
            priority = flow_config["priority"]
            
            # 构建通知内容
            notification_title = self._get_notification_title(flow_type, priority)
            notification_content = self._get_notification_content(flow_result, flow_config)
            
            target_users = []
            
            if target_type == "roles":
                # 按角色通知
                target_roles = flow_config.get("target_roles", [])
                target_users = await self._get_users_by_roles(db, target_roles, project_id)
                logger.info(f"按角色通知: {target_roles}, 找到 {len(target_users)} 个用户")
            elif target_type == "users":
                # 按用户通知
                target_user_ids = flow_config.get("target_users", [])
                target_users = [uuid.UUID(user_id) for user_id in target_user_ids if user_id]
                logger.info(f"按用户通知: {len(target_users)} 个指定用户")
            else:
                # 混合模式：同时支持角色和用户
                target_roles = flow_config.get("target_roles", [])
                target_user_ids = flow_config.get("target_users", [])
                
                # 获取角色用户
                role_users = await self._get_users_by_roles(db, target_roles, project_id)
                
                # 获取指定用户
                specific_users = [uuid.UUID(user_id) for user_id in target_user_ids if user_id]
                
                # 合并并去重
                target_users = list(set(role_users + specific_users))
                logger.info(f"混合通知: 角色用户 {len(role_users)} 个, 指定用户 {len(specific_users)} 个, 总计 {len(target_users)} 个")
            
            # 发送通知给每个目标用户
            for user_id in target_users:
                notification_data = NotificationCreate(
                    title=notification_title,
                    content=notification_content,
                    type="business_flow",
                    priority=priority,
                    target_url=f"/customer-service/flows/{flow_result['flow_id']}"
                )
                
                await NotificationService.create_notification(
                    db=db,
                    user_id=user_id,
                    project_id=project_id,
                    notification_data=notification_data
                )
                
            logger.info(f"已通知 {len(target_users)} 个相关人员关于业务流转: {flow_result['flow_id']}")
            
        except Exception as e:
            logger.error(f"通知利益相关者失败: {e}")
            
    async def _create_business_tasks(
        self,
        db: AsyncSession,
        flow_result: Dict[str, Any],
        flow_config: Dict[str, Any],
        project_id: uuid.UUID
    ):
        """创建业务任务"""
        try:
            flow_type = flow_config["flow_type"]
            target_roles = flow_config["target_roles"]
            
            # 获取主要负责人
            primary_assignees = await self._get_users_by_roles(db, target_roles[:1], project_id)
            
            # 创建任务
            task_data = TaskCreate(
                title=f"处理{self._get_flow_type_name(flow_type)}: {flow_result['session_id']}",
                description=f"""
                业务流转详情：
                - 流转ID：{flow_result['flow_id']}
                - 类型：{flow_type}
                - 客户消息：{flow_result['message'][:200]}...
                - 客户信息：{flow_result['customer_info']}
                - 平台：{flow_result['platform_type']}
                - 优先级：{flow_config['priority']}
                - SLA：{flow_config['sla_hours']}小时
                
                请及时处理并更新进度。
                """,
                category="business_flow",
                priority=flow_config["priority"],
                status="pending",
                due_date=datetime.fromtimestamp(flow_result["sla_deadline"]),
                assignee_ids=primary_assignees,
                attachments=[]
            )
            
            await TaskService.create_task(
                db=db,
                project_id=project_id,
                task_data=task_data,
                created_by=uuid.uuid4()  # 系统创建
            )
            
            logger.info(f"为业务流转 {flow_result['flow_id']} 创建了任务")
            
        except Exception as e:
            logger.error(f"创建业务任务失败: {e}")
            
    async def _get_users_by_roles(
        self,
        db: AsyncSession,
        roles: List[str],
        project_id: uuid.UUID
    ) -> List[uuid.UUID]:
        """根据角色获取用户列表"""
        try:
            from sqlalchemy import select
            from models.role import Role
            from models.project_user import ProjectUser
            from models.user import User
            
            target_users = []
            
            for role_code in roles:
                # 查询项目中的角色
                role_result = await db.execute(
                    select(Role).where(
                        Role.project_id == project_id,
                        Role.code == role_code
                    )
                )
                role = role_result.scalar_one_or_none()
                
                if role:
                    # 查询该角色下的所有用户
                    user_result = await db.execute(
                        select(ProjectUser.user_id).where(
                            ProjectUser.project_id == project_id,
                            ProjectUser.role_id == role.id
                        )
                    )
                    user_ids = [row[0] for row in user_result.fetchall()]
                    target_users.extend(user_ids)
                    logger.info(f"角色 {role_code} 找到 {len(user_ids)} 个用户")
                else:
                    logger.warning(f"项目 {project_id} 中未找到角色: {role_code}")
                    
            return list(set(target_users))  # 去重
            
        except Exception as e:
            logger.error(f"根据角色获取用户失败: {e}")
            return []
            
    def _get_notification_title(self, flow_type: str, priority: str) -> str:
        """获取通知标题"""
        priority_prefix = "🔴 紧急" if priority == "high" else "🟡 重要" if priority == "medium" else "🟢 普通"
        
        flow_names = {
            "complaint_handling": "客户投诉处理",
            "partnership_inquiry": "合作伙伴咨询",
            "franchise_inquiry": "加盟咨询",
            "investment_inquiry": "投资咨询",
            "technical_support": "技术支持",
            "sales_inquiry": "销售咨询",
            "refund_request": "退款申请"
        }
        
        flow_name = flow_names.get(flow_type, "业务流转")
        return f"{priority_prefix} {flow_name}通知"
        
    def _get_notification_content(self, flow_result: Dict[str, Any], flow_config: Dict[str, Any]) -> str:
        """获取通知内容"""
        return f"""
        收到新的{self._get_flow_type_name(flow_config['flow_type'])}：
        
        📋 流转ID：{flow_result['flow_id']}
        👤 客户：{flow_result['customer_info'].get('name', '未知')}
        📱 平台：{flow_result['platform_type']}
        💬 消息：{flow_result['message'][:100]}...
        ⏰ SLA：{flow_config['sla_hours']}小时内处理
        🎯 优先级：{flow_config['priority']}
        
        请及时查看并处理。
        """
        
    def _get_flow_type_name(self, flow_type: str) -> str:
        """获取业务流类型名称"""
        flow_names = {
            "complaint_handling": "客户投诉",
            "partnership_inquiry": "合作咨询",
            "franchise_inquiry": "加盟咨询", 
            "investment_inquiry": "投资咨询",
            "technical_support": "技术支持",
            "sales_inquiry": "销售咨询",
            "refund_request": "退款申请"
        }
        return flow_names.get(flow_type, "业务流转")
