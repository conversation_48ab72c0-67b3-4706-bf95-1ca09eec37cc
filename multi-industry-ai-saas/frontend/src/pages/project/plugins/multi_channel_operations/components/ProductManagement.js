import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Modal,
  Form,
  Input,
  Select,
  InputNumber,
  Switch,
  message,
  Popconfirm,
  Tooltip,
  Badge,
  Typography,
  Row,
  Col,
  Divider,
  Progress,
  Upload,
  Checkbox
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SyncOutlined,
  UploadOutlined,
  DownloadOutlined,
  ShoppingOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  PictureOutlined
} from '@ant-design/icons';
import { useParams } from 'react-router-dom';
import multiChannelService from '../services/multiChannelService';

const { Title, Text } = Typography;
const { Option } = Select;

const ProductManagement = () => {
  const { projectId } = useParams();
  const [loading, setLoading] = useState(false);
  const [products, setProducts] = useState([]);
  const [channels, setChannels] = useState([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [batchModalVisible, setBatchModalVisible] = useState(false);
  const [multiChannelModalVisible, setMultiChannelModalVisible] = useState(false);
  const [imageGenerationModalVisible, setImageGenerationModalVisible] = useState(false);
  const [editingProduct, setEditingProduct] = useState(null);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [form] = Form.useForm();
  const [batchForm] = Form.useForm();
  const [multiChannelForm] = Form.useForm();
  const [imageForm] = Form.useForm();

  useEffect(() => {
    loadProducts();
    loadChannels();
  }, [projectId]);

  const loadProducts = async () => {
    try {
      setLoading(true);
      const response = await multiChannelService.getProductMappings(projectId);
      setProducts(response.data || []);
    } catch (error) {
      console.error('加载商品列表失败:', error);
      message.error('加载商品列表失败');
    } finally {
      setLoading(false);
    }
  };

  const loadChannels = async () => {
    try {
      const response = await multiChannelService.getChannels(projectId);
      setChannels(response.data || []);
    } catch (error) {
      console.error('加载渠道列表失败:', error);
    }
  };

  const handleAdd = () => {
    setEditingProduct(null);
    form.resetFields();
    setModalVisible(true);
  };

  const handleEdit = (record) => {
    setEditingProduct(record);
    form.setFieldsValue({
      ...record,
      channel_product_data: record.channel_product_data || {}
    });
    setModalVisible(true);
  };

  const handleDelete = async (id) => {
    try {
      await multiChannelService.deleteProductMapping(projectId, id);
      message.success('删除成功');
      loadProducts();
    } catch (error) {
      console.error('删除失败:', error);
      message.error('删除失败');
    }
  };

  const handleSubmit = async (values) => {
    try {
      if (editingProduct) {
        await multiChannelService.updateProductMapping(projectId, editingProduct.id, values);
        message.success('更新成功');
      } else {
        await multiChannelService.createProductMapping(projectId, values);
        message.success('创建成功');
      }
      setModalVisible(false);
      loadProducts();
    } catch (error) {
      console.error('保存失败:', error);
      message.error('保存失败');
    }
  };

  const handleSync = async (id) => {
    try {
      setLoading(true);
      await multiChannelService.syncProduct(projectId, id);
      message.success('同步请求已发送');
      loadProducts();
    } catch (error) {
      console.error('同步失败:', error);
      message.error('同步失败');
    } finally {
      setLoading(false);
    }
  };

  const handleBatchSync = async (values) => {
    try {
      setLoading(true);
      await multiChannelService.batchSyncProducts(projectId, {
        product_ids: selectedRowKeys,
        ...values
      });
      message.success('批量同步成功');
      setBatchModalVisible(false);
      setSelectedRowKeys([]);
      loadProducts();
    } catch (error) {
      console.error('批量同步失败:', error);
      message.error('批量同步失败');
    } finally {
      setLoading(false);
    }
  };

  const handleBatchList = async () => {
    try {
      setLoading(true);
      await multiChannelService.batchListProducts(projectId, {
        product_ids: selectedRowKeys
      });
      message.success('批量上架成功');
      setSelectedRowKeys([]);
      loadProducts();
    } catch (error) {
      console.error('批量上架失败:', error);
      message.error('批量上架失败');
    } finally {
      setLoading(false);
    }
  };

  const handleBatchUnlist = async () => {
    try {
      setLoading(true);
      await multiChannelService.batchUnlistProducts(projectId, {
        product_ids: selectedRowKeys
      });
      message.success('批量下架成功');
      setSelectedRowKeys([]);
      loadProducts();
    } catch (error) {
      console.error('批量下架失败:', error);
      message.error('批量下架失败');
    } finally {
      setLoading(false);
    }
  };

  // 一品多渠道同步
  const handleMultiChannelSync = async (values) => {
    try {
      setLoading(true);
      await multiChannelService.multiChannelSync(projectId, {
        product_id: values.product_id,
        channel_ids: values.channel_ids,
        sync_options: values.sync_options
      });
      message.success('一品多渠道同步成功');
      setMultiChannelModalVisible(false);
      loadProducts();
    } catch (error) {
      console.error('一品多渠道同步失败:', error);
      message.error('一品多渠道同步失败');
    } finally {
      setLoading(false);
    }
  };

  // AI图片生成
  const handleGenerateImage = async (values) => {
    try {
      setLoading(true);
      const response = await multiChannelService.generateProductImage(projectId, {
        product_id: values.product_id,
        prompt: values.prompt,
        style: values.style,
        channel_id: values.channel_id
      });

      if (response.success) {
        message.success('AI图片生成成功');
        setImageGenerationModalVisible(false);
        // 可以在这里显示生成的图片
      } else {
        message.error(response.error || 'AI图片生成失败');
      }
    } catch (error) {
      console.error('AI图片生成失败:', error);
      message.error('AI图片生成失败');
    } finally {
      setLoading(false);
    }
  };

  // 批量价格更新
  const handleBatchPriceUpdate = async (values) => {
    try {
      setLoading(true);
      const mappings = selectedRowKeys.map(id => ({
        mapping_id: id,
        price: values.price,
        price_strategy: values.price_strategy
      }));

      await multiChannelService.batchUpdatePrices(projectId, { mappings });
      message.success('批量价格更新成功');
      setSelectedRowKeys([]);
      loadProducts();
    } catch (error) {
      console.error('批量价格更新失败:', error);
      message.error('批量价格更新失败');
    } finally {
      setLoading(false);
    }
  };

  const getSyncStatusTag = (status) => {
    const statusMap = {
      'synced': { color: 'success', icon: <CheckCircleOutlined />, text: '已同步' },
      'pending': { color: 'processing', icon: <ClockCircleOutlined />, text: '待同步' },
      'failed': { color: 'error', icon: <ExclamationCircleOutlined />, text: '同步失败' },
      'partial': { color: 'warning', icon: <ExclamationCircleOutlined />, text: '部分同步' }
    };
    
    const config = statusMap[status] || { color: 'default', text: status };
    return (
      <Tag color={config.color} icon={config.icon}>
        {config.text}
      </Tag>
    );
  };

  const getChannelName = (channelId) => {
    const channel = channels.find(c => c.id === channelId);
    return channel ? channel.custom_name : '未知渠道';
  };

  const columns = [
    {
      title: '商品信息',
      dataIndex: 'product_name',
      key: 'product_name',
      render: (text, record) => (
        <div>
          <div style={{ fontWeight: 'bold' }}>{text || record.product_id}</div>
          <Text type="secondary" style={{ fontSize: 12 }}>
            ID: {record.product_id}
          </Text>
        </div>
      )
    },
    {
      title: '渠道',
      dataIndex: 'channel_id',
      key: 'channel_id',
      render: (channelId) => getChannelName(channelId)
    },
    {
      title: '外部商品ID',
      dataIndex: 'external_product_id',
      key: 'external_product_id',
      render: (text) => text || <Text type="secondary">未映射</Text>
    },
    {
      title: '同步状态',
      dataIndex: 'sync_status',
      key: 'sync_status',
      render: (status) => getSyncStatusTag(status)
    },
    {
      title: '最后同步',
      dataIndex: 'last_sync_at',
      key: 'last_sync_at',
      render: (text) => text ? new Date(text).toLocaleString() : '从未同步'
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Tooltip title="编辑">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          
          <Tooltip title="同步">
            <Button
              type="text"
              icon={<SyncOutlined />}
              onClick={() => handleSync(record.id)}
            />
          </Tooltip>
          
          <Popconfirm
            title="确定要删除这个商品映射吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除">
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      )
    }
  ];

  const rowSelection = {
    selectedRowKeys,
    onChange: setSelectedRowKeys,
    selections: [
      Table.SELECTION_ALL,
      Table.SELECTION_INVERT,
      Table.SELECTION_NONE,
    ],
  };

  // 商品库数据
  const [productLibrary, setProductLibrary] = useState([]);
  const [productSpecs, setProductSpecs] = useState([]);

  // 加载商品库数据
  const loadProductLibrary = async () => {
    try {
      // 模拟商品库数据
      const mockProducts = [
        {
          id: 'prod1',
          name: '招牌牛肉面',
          category: '主食',
          base_price: 28.00,
          cost_price: 15.00,
          description: '精选优质牛肉，手工拉面',
          images: ['/static/products/beef_noodle.jpg'],
          specs: [
            { id: 'spec1', name: '小份', price: 28.00, stock: 100 },
            { id: 'spec2', name: '大份', price: 35.00, stock: 80 },
            { id: 'spec3', name: '特大份', price: 42.00, stock: 50 }
          ]
        },
        {
          id: 'prod2',
          name: '麻辣香锅',
          category: '主食',
          base_price: 32.00,
          cost_price: 18.00,
          description: '川味麻辣，香气扑鼻',
          images: ['/static/products/spicy_pot.jpg'],
          specs: [
            { id: 'spec4', name: '微辣', price: 32.00, stock: 120 },
            { id: 'spec5', name: '中辣', price: 32.00, stock: 90 },
            { id: 'spec6', name: '特辣', price: 35.00, stock: 60 }
          ]
        },
        {
          id: 'prod3',
          name: '鲜榨橙汁',
          category: '饮品',
          base_price: 15.00,
          cost_price: 8.00,
          description: '新鲜橙子现榨，维C丰富',
          images: ['/static/products/orange_juice.jpg'],
          specs: [
            { id: 'spec7', name: '中杯', price: 15.00, stock: 200 },
            { id: 'spec8', name: '大杯', price: 20.00, stock: 150 }
          ]
        }
      ];
      setProductLibrary(mockProducts);
    } catch (error) {
      console.error('加载商品库失败:', error);
    }
  };

  useEffect(() => {
    loadProducts();
    loadChannels();
    loadProductLibrary();
  }, [projectId]);

  return (
    <div>

      <Card>
        <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between' }}>
          <Space>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAdd}
            >
              添加商品映射
            </Button>
            <Button
              icon={<SyncOutlined />}
              onClick={() => setBatchModalVisible(true)}
              disabled={selectedRowKeys.length === 0}
            >
              批量同步
            </Button>
            <Button
              icon={<UploadOutlined />}
              onClick={handleBatchList}
              disabled={selectedRowKeys.length === 0}
            >
              批量上架
            </Button>
            <Button
              icon={<DownloadOutlined />}
              onClick={handleBatchUnlist}
              disabled={selectedRowKeys.length === 0}
            >
              批量下架
            </Button>
            <Button
              type="dashed"
              onClick={() => setMultiChannelModalVisible(true)}
            >
              一品多渠道
            </Button>
            <Button
              type="dashed"
              icon={<PictureOutlined />}
              onClick={() => setImageGenerationModalVisible(true)}
            >
              AI图片生成
            </Button>
          </Space>
          
          <Space>
            <Text type="secondary">
              已选择 {selectedRowKeys.length} 项
            </Text>
            <Button
              icon={<SyncOutlined />}
              onClick={loadProducts}
            >
              刷新
            </Button>
          </Space>
        </div>

        <Table
          columns={columns}
          dataSource={products}
          rowKey="id"
          loading={loading}
          rowSelection={rowSelection}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`
          }}
        />
      </Card>

      {/* 商品映射模态框 */}
      <Modal
        title={editingProduct ? '编辑商品映射' : '添加商品映射'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item
            name="product_id"
            label="选择商品"
            rules={[{ required: true, message: '请选择商品' }]}
          >
            <Select
              placeholder="从商品库选择商品"
              showSearch
              filterOption={(input, option) =>
                option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
              }
              onChange={(value) => {
                const product = productLibrary.find(p => p.id === value);
                setSelectedProduct(product);
                form.setFieldsValue({
                  'channel_product_data.name': product?.name,
                  'channel_product_data.description': product?.description,
                  'channel_product_data.price': product?.base_price
                });
              }}
            >
              {productLibrary.map(product => (
                <Option key={product.id} value={product.id}>
                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                    <span>{product.name}</span>
                    <span style={{ color: '#999' }}>¥{product.base_price}</span>
                  </div>
                </Option>
              ))}
            </Select>
          </Form.Item>

          {selectedProduct && (
            <Card size="small" style={{ marginBottom: 16 }}>
              <Row gutter={16}>
                <Col span={8}>
                  <div style={{ textAlign: 'center' }}>
                    <img
                      src={selectedProduct.images[0]}
                      alt={selectedProduct.name}
                      style={{ width: 80, height: 80, objectFit: 'cover', borderRadius: 4 }}
                      onError={(e) => {
                        e.target.src = '/static/placeholder.png';
                      }}
                    />
                  </div>
                </Col>
                <Col span={16}>
                  <div>
                    <Text strong>{selectedProduct.name}</Text>
                    <div style={{ marginTop: 4 }}>
                      <Text type="secondary">{selectedProduct.description}</Text>
                    </div>
                    <div style={{ marginTop: 8 }}>
                      <Tag color="blue">基础价格: ¥{selectedProduct.base_price}</Tag>
                      <Tag color="green">成本价格: ¥{selectedProduct.cost_price}</Tag>
                    </div>
                  </div>
                </Col>
              </Row>
            </Card>
          )}

          {selectedProduct && selectedProduct.specs && selectedProduct.specs.length > 0 && (
            <Form.Item
              name="selected_specs"
              label="选择规格"
            >
              <Checkbox.Group style={{ width: '100%' }}>
                <Row gutter={[16, 8]}>
                  {selectedProduct.specs.map(spec => (
                    <Col span={8} key={spec.id}>
                      <Checkbox value={spec.id}>
                        <div>
                          <div>{spec.name}</div>
                          <div style={{ fontSize: 12, color: '#999' }}>
                            ¥{spec.price} | 库存:{spec.stock}
                          </div>
                        </div>
                      </Checkbox>
                    </Col>
                  ))}
                </Row>
              </Checkbox.Group>
            </Form.Item>
          )}

          <Form.Item
            name="channel_id"
            label="目标渠道"
            rules={[{ required: true, message: '请选择渠道' }]}
          >
            <Select placeholder="选择要映射的渠道">
              {channels.map(channel => (
                <Option key={channel.id} value={channel.id}>
                  {channel.custom_name}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="external_product_id"
            label="外部商品ID"
          >
            <Input placeholder="渠道平台的商品ID（可选）" />
          </Form.Item>

          <Divider>渠道商品信息</Divider>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name={['channel_product_data', 'name']}
                label="渠道商品名称"
              >
                <Input placeholder="在渠道平台显示的商品名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name={['channel_product_data', 'price']}
                label="渠道价格"
              >
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="在渠道的销售价格"
                  min={0}
                  precision={2}
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name={['channel_product_data', 'description']}
            label="渠道商品描述"
          >
            <Input.TextArea rows={3} placeholder="在渠道平台的商品描述" />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name={['channel_product_data', 'category']}
                label="渠道分类"
              >
                <Input placeholder="渠道平台的商品分类" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name={['channel_product_data', 'stock']}
                label="库存数量"
              >
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="库存数量"
                  min={0}
                />
              </Form.Item>
            </Col>
          </Row>

          <div style={{ textAlign: 'right', marginTop: 24 }}>
            <Space>
              <Button onClick={() => setModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                {editingProduct ? '更新' : '创建'}
              </Button>
            </Space>
          </div>
        </Form>
      </Modal>

      {/* 批量同步模态框 */}
      <Modal
        title="批量同步商品"
        open={batchModalVisible}
        onCancel={() => setBatchModalVisible(false)}
        footer={null}
        width={500}
      >
        <Form
          form={batchForm}
          layout="vertical"
          onFinish={handleBatchSync}
        >
          <div style={{ marginBottom: 16 }}>
            <Text>已选择 {selectedRowKeys.length} 个商品进行同步</Text>
          </div>

          <Form.Item
            name="sync_type"
            label="同步类型"
            rules={[{ required: true, message: '请选择同步类型' }]}
          >
            <Select placeholder="选择同步类型">
              <Option value="basic_info">基本信息</Option>
              <Option value="price">价格</Option>
              <Option value="stock">库存</Option>
              <Option value="all">全部信息</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="force_update"
            label="强制更新"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>

          <div style={{ textAlign: 'right', marginTop: 24 }}>
            <Space>
              <Button onClick={() => setBatchModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                开始同步
              </Button>
            </Space>
          </div>
        </Form>
      </Modal>

      {/* 一品多渠道同步模态框 */}
      <Modal
        title="一品多渠道同步"
        open={multiChannelModalVisible}
        onCancel={() => setMultiChannelModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={multiChannelForm}
          layout="vertical"
          onFinish={handleMultiChannelSync}
        >
          <Form.Item
            name="product_id"
            label="选择商品"
            rules={[{ required: true, message: '请选择商品' }]}
          >
            <Select
              placeholder="从商品库选择商品"
              showSearch
              filterOption={(input, option) =>
                option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
              }
            >
              {productLibrary.map(product => (
                <Option key={product.id} value={product.id}>
                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                    <span>{product.name}</span>
                    <span style={{ color: '#999' }}>¥{product.base_price}</span>
                  </div>
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="channel_ids"
            label="目标渠道"
            rules={[{ required: true, message: '请选择至少一个渠道' }]}
          >
            <Checkbox.Group style={{ width: '100%' }}>
              <Row gutter={[16, 8]}>
                {channels.map(channel => (
                  <Col span={8} key={channel.id}>
                    <Checkbox value={channel.id}>
                      {channel.custom_name}
                    </Checkbox>
                  </Col>
                ))}
              </Row>
            </Checkbox.Group>
          </Form.Item>

          <Form.Item
            name="sync_options"
            label="同步选项"
          >
            <Checkbox.Group>
              <Row gutter={[16, 8]}>
                <Col span={8}>
                  <Checkbox value="basic_info">基本信息</Checkbox>
                </Col>
                <Col span={8}>
                  <Checkbox value="price">价格</Checkbox>
                </Col>
                <Col span={8}>
                  <Checkbox value="stock">库存</Checkbox>
                </Col>
                <Col span={8}>
                  <Checkbox value="images">图片</Checkbox>
                </Col>
                <Col span={8}>
                  <Checkbox value="description">描述</Checkbox>
                </Col>
                <Col span={8}>
                  <Checkbox value="category">分类</Checkbox>
                </Col>
              </Row>
            </Checkbox.Group>
          </Form.Item>

          <div style={{ textAlign: 'right', marginTop: 24 }}>
            <Space>
              <Button onClick={() => setMultiChannelModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                开始同步
              </Button>
            </Space>
          </div>
        </Form>
      </Modal>

      {/* AI图片生成模态框 */}
      <Modal
        title="AI图片生成"
        open={imageGenerationModalVisible}
        onCancel={() => setImageGenerationModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={imageForm}
          layout="vertical"
          onFinish={handleGenerateImage}
        >
          <Form.Item
            name="product_id"
            label="选择商品"
            rules={[{ required: true, message: '请选择商品' }]}
          >
            <Select
              placeholder="从商品库选择商品"
              showSearch
              filterOption={(input, option) =>
                option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
              }
            >
              {productLibrary.map(product => (
                <Option key={product.id} value={product.id}>
                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                    <span>{product.name}</span>
                    <span style={{ color: '#999' }}>{product.category}</span>
                  </div>
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="channel_id"
            label="目标渠道（可选）"
          >
            <Select placeholder="选择特定渠道以生成定制化图片">
              {channels.map(channel => (
                <Option key={channel.id} value={channel.id}>
                  {channel.custom_name}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="prompt"
            label="生成提示词"
            rules={[{ required: true, message: '请输入生成提示词' }]}
          >
            <Input.TextArea
              rows={3}
              placeholder="描述您想要生成的图片，例如：高质量美食摄影，白色背景，专业灯光"
            />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="style"
                label="图片风格"
                initialValue="natural"
              >
                <Select>
                  <Option value="natural">自然风格</Option>
                  <Option value="vivid">鲜艳风格</Option>
                  <Option value="photorealistic">写实风格</Option>
                  <Option value="artistic">艺术风格</Option>
                  <Option value="minimalist">简约风格</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="model"
                label="AI模型"
                initialValue="dall-e-3"
              >
                <Select>
                  <Option value="dall-e-3">DALL-E 3</Option>
                  <Option value="midjourney">Midjourney</Option>
                  <Option value="stable-diffusion">Stable Diffusion</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <div style={{ textAlign: 'right', marginTop: 24 }}>
            <Space>
              <Button onClick={() => setImageGenerationModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                生成图片
              </Button>
            </Space>
          </div>
        </Form>
      </Modal>
    </div>
  );
};

export default ProductManagement;
