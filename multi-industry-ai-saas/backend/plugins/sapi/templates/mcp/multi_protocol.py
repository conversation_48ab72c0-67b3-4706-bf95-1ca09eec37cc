#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
MCP Server - Multi-Protocol Support V2 (using updated mcp.base)
"""

import os
import json
import logging
import asyncio
import sys
import uuid
import argparse
from fastapi import Request as FastAPIRequest, HTTPException
# import httpx # Retained: Potentially used by mcp.base or utils
from typing import Dict, Any, List, Optional, Union
from pydantic import BaseModel, Field

from utils import register_server, heartbeat as redis_registration_heartbeat
# from load_tools import load_tools # Assumed to be part of MCPServer now

from mcp.base import (
    MCPServer,
    SERVER_VERSION,
    ErrorCode,
    MCPSession,
    # authenticate_request, # Assumed internal to MCPServer or mcp_server.handle_request
    PROTOCOL_VERSION,
)

from utils import parsed_env_sdk, get_redis_client

logger = logging.getLogger(__name__)

SERVER_NAME = "MCP Server"

def get_mcp_server_id() -> str:
    server_id = os.environ.get("MCP_SERVER_ID")
    if server_id:
        return server_id
    try:
        current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        dir_name = os.path.basename(current_dir)
        try:
            uuid.UUID(dir_name)
            return dir_name
        except ValueError:
            pass
    except Exception as e:
        logger.warning(f"Failed to get server ID from directory name: {e}")
    return str(uuid.uuid4())

SERVER_ID = get_mcp_server_id()
mcp_server = MCPServer(server_id=SERVER_ID)

# 导入工具并准备注册 (如果tools.py存在且包含AVAILABLE_TOOLS)
try:
    import tools as mcp_tools # MODIFIED: Changed from relative to absolute import
    if hasattr(mcp_tools, 'AVAILABLE_TOOLS') and isinstance(mcp_tools.AVAILABLE_TOOLS, dict):
        logger.info(f"[MCP Server Startup] Found {len(mcp_tools.AVAILABLE_TOOLS)} tools in tools.py to register.")
    else:
        mcp_tools = None # tools.py exists but no AVAILABLE_TOOLS or wrong format
        logger.warning("[MCP Server Startup] tools.py found, but no AVAILABLE_TOOLS dictionary or incorrect format.")
except ImportError:
    mcp_tools = None # tools.py not found or import error
    logger.warning("[MCP Server Startup] tools.py not found or could not be imported. No Python tools will be pre-registered.")

# --- StreamHTTP Server ---
async def start_streamhttp_server(port: int) -> None:
    logger.info(f"[StreamHTTP] Attempting to start server on port {port}")
    from fastapi import FastAPI, Response as FastAPIResponse
    from fastapi.middleware.cors import CORSMiddleware
    import uvicorn

    app_streamhttp = FastAPI(title=f"MCP Server (StreamHTTP) - {SERVER_ID}", version=SERVER_VERSION)
    app_streamhttp.add_middleware(
        CORSMiddleware,
        allow_origins=["*"], allow_credentials=True, allow_methods=["*"], allow_headers=["*"],
    )

    # Standard headers for MCP session and project/user context
    MCP_SESSION_ID_HEADER = "mcp-session-id"
    MCP_PROJECT_ID_HEADER = "x-mcp-project-id" # Example, adjust as per actual client used
    MCP_USER_ID_HEADER = "x-mcp-user-id"       # Example, adjust as per actual client used

    @app_streamhttp.options("/mcp")
    async def options_mcp_endpoint():
        return FastAPIResponse(
            content="",
            status_code=204,
            headers={
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
                "Access-Control-Allow-Headers": f"Content-Type, Accept, Authorization, {MCP_SESSION_ID_HEADER}, {MCP_PROJECT_ID_HEADER}, {MCP_USER_ID_HEADER}", # Add MCP headers
                "Access-Control-Max-Age": "86400",
            }
    )

    class MCPRequestModel(BaseModel):
        id: Optional[Union[str, int]] = None
        method: str
        params: Dict[str, Any] = Field(default_factory=dict)

    @app_streamhttp.on_event("startup")
    async def streamhttp_startup():
        logger.info(f"[StreamHTTP] Startup event for server {SERVER_ID} on port {port}.")
        host_for_registration = parsed_env_sdk.get("MCP_SERVER_HOST") or \
                                parsed_env_sdk.get("MCP_SERVICE_NAME") or \
                                f"mcp-server-{SERVER_ID}"
        logger.info(f"[StreamHTTP] Registration hostname: {host_for_registration}")

        # 注册预定义的Python工具
        if mcp_tools and mcp_tools.AVAILABLE_TOOLS:
            for tool_name, tool_func in mcp_tools.AVAILABLE_TOOLS.items():
                mcp_server.register_tool(tool_name, tool_func)
            logger.info(f"[StreamHTTP Startup] Successfully registered {len(mcp_tools.AVAILABLE_TOOLS)} Python tools.")
        else:
            logger.info("[StreamHTTP Startup] No Python tools were pre-registered (mcp_tools not available or empty).")

        try:
            internal_http_port = port
            # 确保所有协议端口都被正确注册
            # 添加元数据以支持Traefik路由
            traefik_mcp_domain_suffix = os.environ.get("TRAEFIK_MCP_DOMAIN_SUFFIX", "localhost")
            mcp_instance_hostname = f"mcp-{SERVER_ID}.{traefik_mcp_domain_suffix}"
            streamhttp_endpoint_path = "/mcp" # Define the standard endpoint path

            registration_data = {
                "server_id": SERVER_ID,
                "host": host_for_registration,
                "port": internal_http_port, # General port, could be the same as port_streamhttp for this mode
                "port_http": internal_http_port,
                "port_streamhttp": internal_http_port,
                # "port_stdio": None, # Explicitly not setting if this instance is purely StreamHTTP
                "metadata": {
                    "streamhttp_endpoint": streamhttp_endpoint_path, # Moved into metadata
                    "traefik_hostname": mcp_instance_hostname,
                    "protocols": ["streamhttp"],
                    "version": SERVER_VERSION,
                    "stream_capable": True
                }
            }
            if await register_server(**registration_data):
                logger.info(f"[StreamHTTP] Server {SERVER_ID} registered to Redis "
                            f"({host_for_registration}, HTTP:{internal_http_port}).")
                asyncio.create_task(redis_registration_heartbeat(**registration_data))
            else:
                logger.error(f"[StreamHTTP] Failed to register server {SERVER_ID} to Redis.")
        except Exception as e:
            logger.error(f"[StreamHTTP] Error during Redis registration: {e}", exc_info=True)

        await mcp_server.initialize_server()
        asyncio.create_task(mcp_server.start_heartbeat())
        logger.info(f"[StreamHTTP {SERVER_ID}] Session cleanup task (MCPServer.start_heartbeat) started.")

    @app_streamhttp.post("/mcp")
    async def mcp_post_endpoint(request_data: MCPRequestModel, http_request: FastAPIRequest):
        logger.debug(f"[StreamHTTP] POST /mcp: id={request_data.id}, method={request_data.method}")
        headers = dict(http_request.headers)

        # Log all incoming headers for diagnostics
        logger.info(f"[StreamHTTP POST /mcp INCOMING_HEADERS for method {request_data.method}, id {request_data.id}]: {json.dumps(headers)}")

        # 检查是否为Traefik直接路由（带有鉴权头部）
        auth_type = headers.get("x-mcp-auth-type")
        if auth_type in ["direct_access", "sapi_converted"]:
            logger.info(f"[StreamHTTP POST /mcp] 检测到Traefik直接路由，鉴权类型: {auth_type}")
            # 从Traefik鉴权头部获取会话信息
            user_id = headers.get("x-mcp-user-id")
            project_id = headers.get("x-mcp-project-id")
            system_token = headers.get("x-mcp-system-token")
            permissions_str = headers.get("x-mcp-permissions", "[]")
            
            try:
                permissions = json.loads(permissions_str) if permissions_str else []
            except json.JSONDecodeError:
                permissions = []
            
            # 获取会话ID - 优先使用客户端提供的会话ID
            session_id_from_header = headers.get(MCP_SESSION_ID_HEADER.lower()) or headers.get(MCP_SESSION_ID_HEADER)
            
            # 如果是初始化请求或没有提供会话ID，则根据鉴权类型处理
            if request_data.method == "initialize" or not session_id_from_header:
                if auth_type == "direct_access":
                    session_id_from_header = headers.get("x-mcp-session-id") or str(uuid.uuid4())
                else:
                    # SAPI转换的情况，生成新的会话ID
                    session_id_from_header = str(uuid.uuid4())
                    logger.info(f"[StreamHTTP POST /mcp] 为SAPI转换创建新会话: {session_id_from_header}")
            else:
                logger.info(f"[StreamHTTP POST /mcp] 使用客户端提供的会话ID: {session_id_from_header}")
        else:
            # 标准会话ID头部处理
            session_id_from_header = headers.get(MCP_SESSION_ID_HEADER.lower()) or headers.get(MCP_SESSION_ID_HEADER)

        response_json: Dict[str, Any]

        if request_data.method == "initialize":
            # 检查是否为Traefik直接路由的初始化
            if auth_type in ["direct_access", "sapi_converted"]:
                # 直接使用Traefik提供的鉴权信息创建会话
                logger.info(f"[StreamHTTP POST /mcp] Traefik直接路由初始化，鉴权类型: {auth_type}")
                
                # 创建会话信息
                if auth_type == "direct_access" and session_id_from_header:
                    # 直接访问模式，使用现有会话ID
                    new_session_id = session_id_from_header
                else:
                    # SAPI转换模式，生成新会话ID
                    new_session_id = str(uuid.uuid4())
                
                # 构造初始化响应
                response_json = {
                    "jsonrpc": "2.0",
                    "id": request_data.id,
                    "result": {
                        "sessionId": new_session_id,
                        "protocolVersion": PROTOCOL_VERSION,
                        "serverInfo": {
                            "name": SERVER_NAME,
                            "version": SERVER_VERSION,
                            "serverId": SERVER_ID
                        },
                        "capabilities": mcp_server.get_server_capabilities(new_session_id),
                        "authType": auth_type,
                        "directAccess": True
                    }
                }
                
                # 创建并存储会话
                from mcp.base import MCPSession
                session = MCPSession(
                    session_id=new_session_id,
                    user_id=user_id,
                    project_id=project_id,
                    system_token=system_token,
                    permissions=permissions,
                    server_id=SERVER_ID,
                    client_info=request_data.params.get("clientInfo"),
                    is_initialized=True,
                    internal_message_queue=asyncio.Queue()
                )
                mcp_server.session_store[new_session_id] = session
                logger.info(f"[StreamHTTP POST /mcp] Traefik直接路由会话已创建: {new_session_id}")
            else:
                # 标准初始化流程
                response_json = await mcp_server.handle_initialize(
                    request_id=request_data.id,
                    params=request_data.params,
                    headers=headers # Pass all headers for potential auth/context
                )
            # Ensure the response_json from handle_initialize contains the session_id under result.sessionId
            # if "result" in response_json and "sessionId" in response_json["result"]:
            #     logger.info(f"[StreamHTTP] Initialize successful, session_id: {response_json['result']['sessionId']}")
            # else:
            #     logger.warning(f"[StreamHTTP] Initialize response does not contain session_id in result: {response_json}")

        elif session_id_from_header:
            # 对于Traefik直接路由的非初始化请求，如果会话不存在则创建
            if auth_type in ["direct_access", "sapi_converted"]:
                existing_session = mcp_server.get_session(session_id_from_header)
                if not existing_session:
                    logger.info(f"[StreamHTTP POST /mcp] Traefik直接路由会话不存在，创建新会话: {session_id_from_header}")
                    # 创建并存储会话
                    from mcp.base import MCPSession
                    session = MCPSession(
                        session_id=session_id_from_header,
                        user_id=user_id,
                        project_id=project_id,
                        system_token=system_token,
                        permissions=permissions,
                        server_id=SERVER_ID,
                        client_info=None,
                        is_initialized=True,
                        internal_message_queue=asyncio.Queue()
                    )
                    mcp_server.session_store[session_id_from_header] = session
                    logger.info(f"[StreamHTTP POST /mcp] Traefik直接路由会话已创建: {session_id_from_header}")
            
            # 记录详细的请求参数
            if request_data.method == "tools/call":
                tool_name = request_data.params.get("name", "未知工具")
                tool_args = request_data.params.get("arguments", {})
                logger.info(f"[StreamHTTP {SERVER_ID}] 工具调用请求: 工具名称={tool_name}, 参数={json.dumps(tool_args)}")

            # 调用处理请求
            try:
                response_json = await mcp_server.handle_request(
                    method=request_data.method,
                    params=request_data.params,
                    request_id=request_data.id,
                    headers=headers,
                    session_id_from_transport=session_id_from_header
                )

                # 详细记录API响应
                if request_data.method == "tools/call":
                    if "error" in response_json:
                        logger.error(f"[StreamHTTP {SERVER_ID}] 工具调用失败: {json.dumps(response_json['error'])}")
                    else:
                        logger.info(f"[StreamHTTP {SERVER_ID}] 工具调用成功: 结果类型={type(response_json.get('result'))}")
                        logger.debug(f"[StreamHTTP {SERVER_ID}] 工具调用详细结果: {json.dumps(response_json, ensure_ascii=False)}")
                else:
                    logger.debug(f"[StreamHTTP {SERVER_ID}] 方法 {request_data.method} 的响应: {json.dumps(response_json, ensure_ascii=False)}")
            except Exception as e:
                logger.error(f"[StreamHTTP {SERVER_ID}] 处理请求时出错: {e}", exc_info=True)
                response_json = {
                    "jsonrpc": "2.0",
                    "id": request_data.id,
                    "error": {
                        "code": ErrorCode.GENERIC_ERROR.value,
                        "message": f"处理请求时出错: {str(e)}"
                    }
                }
        else:
            # No session ID for a non-initialize request
            response_json = {
                "jsonrpc": "2.0",
                "id": request_data.id,
                "error": {
                    "code": ErrorCode.SESSION_NOT_FOUND.value,
                    "message": f"Missing {MCP_SESSION_ID_HEADER} header for non-initialize request.",
                },
            }

        http_status_code = 200
        if "error" in response_json and response_json["error"]:
            mcp_error_code = response_json["error"].get("code")
            if mcp_error_code == ErrorCode.AUTHENTICATION_FAILED.value: http_status_code = 401
            elif mcp_error_code == ErrorCode.AUTHORIZATION_FAILED.value: http_status_code = 403
            elif mcp_error_code == ErrorCode.INVALID_REQUEST.value: http_status_code = 400
            elif mcp_error_code == ErrorCode.SESSION_NOT_FOUND.value:
                http_status_code = 404
            elif mcp_error_code == ErrorCode.METHOD_NOT_FOUND.value: # Handle method not found
                http_status_code = 405
            elif mcp_error_code in [ErrorCode.INITIALIZATION_FAILED.value,
                                    ErrorCode.TOOL_EXECUTION_ERROR.value,
                                    ErrorCode.GENERIC_ERROR.value,
                                    ErrorCode.SESSION_NOT_INITIALIZED.value]:
                http_status_code = 500

        # Add session ID to response headers if available and it's an initialize call that succeeded
        # This is more of a confirmation, the client should primarily use the one in the JSON body.
        final_headers = {}
        if request_data.method == "initialize" and "result" in response_json and "sessionId" in response_json["result"]:
            final_headers[MCP_SESSION_ID_HEADER] = response_json["result"]["sessionId"]

        return FastAPIResponse(content=json.dumps(response_json), media_type="application/json", status_code=http_status_code, headers=final_headers)

    # SSE Support for StreamHTTP
    # Make sure sse_starlette is installed: pip install sse-starlette
    try:
        from sse_starlette.sse import EventSourceResponse
        SSE_AVAILABLE = True
    except ImportError:
        logger.warning("[StreamHTTP] sse_starlette not installed. GET /mcp for SSE streams will not be available.")
        SSE_AVAILABLE = False
        EventSourceResponse = None # type: ignore

    @app_streamhttp.get("/mcp")
    @app_streamhttp.get("/message")  # 添加标准MCP路径
    async def mcp_get_endpoint_sse(http_request: FastAPIRequest):
        if not SSE_AVAILABLE:
            logger.warning(f"[StreamHTTP {SERVER_ID}] GET /mcp 被调用但SSE不可用 (sse_starlette缺失).")
            return FastAPIResponse("此服务器实例不支持SSE流式传输。", status_code=501, media_type="text/plain")

        headers = dict(http_request.headers)
        logger.info(f"[StreamHTTP {SERVER_ID} GET /mcp INCOMING_HEADERS]: {json.dumps(headers)}")

        session_id = headers.get(MCP_SESSION_ID_HEADER.lower()) or headers.get(MCP_SESSION_ID_HEADER)

        if not session_id:
            logger.warning(f"[StreamHTTP {SERVER_ID} GET /mcp] 缺少 {MCP_SESSION_ID_HEADER} 头部.")
            return FastAPIResponse(f"缺少 {MCP_SESSION_ID_HEADER} 头部", status_code=400, media_type="text/plain")

        mcp_session = mcp_server.get_session(session_id)
        if not mcp_session:
            logger.warning(f"[StreamHTTP {SERVER_ID} GET /mcp] 未找到会话ID: {session_id}")
            return FastAPIResponse("未找到会话", status_code=404, media_type="text/plain")

        if not mcp_session.is_initialized:
            logger.warning(f"[StreamHTTP {SERVER_ID} GET /mcp] 会话 {session_id} 未完全初始化，无法进行SSE流式传输.")
            return FastAPIResponse("会话未初始化，无法进行SSE流式传输。", status_code=409, media_type="text/plain")

        logger.info(f"[StreamHTTP {SERVER_ID} GET /mcp] 客户端已连接SSE流式传输. 会话ID: {session_id}")

        async def event_generator():
            try:
                # 确保会话有消息队列
                if not mcp_session.internal_message_queue:
                    mcp_session.internal_message_queue = asyncio.Queue()
                    logger.info(f"[StreamHTTP {SERVER_ID} SSE Gen {session_id}] 为会话创建了新的消息队列")

                while True:
                    try:
                        # 等待来自会话内部队列的消息
                        message = await asyncio.wait_for(
                            mcp_session.internal_message_queue.get(),
                            timeout=mcp_session.heartbeat_interval * 0.8
                        )

                        # 检查是否为关闭队列的哨兵
                        if message is MCPSession._SENTINEL_CLOSE_QUEUE_ or message is None:
                            logger.info(f"[StreamHTTP {SERVER_ID} SSE Gen {session_id}] 收到哨兵或队列为空，关闭流.")
                            break

                        if isinstance(message, dict):
                            event_type = message.get("method", "message")
                            if event_type.startswith("notifications/"):
                                event_type = event_type.split("/", 1)[1]

                            logger.debug(f"[StreamHTTP {SERVER_ID} SSE Gen {session_id}] 发送事件: {event_type}")
                            yield {
                                "event": event_type,
                                "data": json.dumps(message)
                            }
                            mcp_session.internal_message_queue.task_done()
                        else:
                            logger.warning(f"[StreamHTTP {SERVER_ID} SSE Gen {session_id}] 从队列收到非字典消息: {type(message)}. 跳过.")
                            mcp_session.internal_message_queue.task_done()

                    except asyncio.TimeoutError:
                        # 发送ping事件以保持连接活跃
                        logger.debug(f"[StreamHTTP {SERVER_ID} SSE Gen {session_id}] 发送ping事件")
                        yield {
                            "event": "ping",
                            "data": json.dumps({"timestamp": asyncio.get_event_loop().time()})
                        }
                    except Exception as e:
                        logger.error(f"[StreamHTTP {SERVER_ID} SSE Gen {session_id}] 事件生成器中出错: {e}", exc_info=True)
                        break

                logger.info(f"[StreamHTTP {SERVER_ID} SSE Gen {session_id}] 事件生成器已完成.")
            except Exception as e:
                logger.error(f"[StreamHTTP {SERVER_ID} SSE Gen {session_id}] 事件生成器外层异常: {e}", exc_info=True)

        return EventSourceResponse(
            event_generator(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "X-Accel-Buffering": "no"
            }
        )

    @app_streamhttp.get("/health")
    async def health_check():
        redis_ok, redis_error = False, None
        try:
            redis = await get_redis_client()
            await redis.ping()
            redis_ok = True
        except Exception as e:
            redis_error = str(e)
            logger.error(f"[StreamHTTP Health Check] Redis ping failed: {redis_error}", exc_info=True)

        response = {
            "status": "ok", "protocol": "streamhttp", "server_id": SERVER_ID,
            "server_name": SERVER_NAME, "version": SERVER_VERSION, "redis_connected": redis_ok
        }
        if redis_error: response["redis_error"] = redis_error
        return response

    logger.info(f"[StreamHTTP] Starting Uvicorn server {SERVER_ID} on 0.0.0.0:{port}...")
    config = uvicorn.Config(app_streamhttp, host="0.0.0.0", port=port, log_config=None)
    server = uvicorn.Server(config)
    await server.serve()
    logger.info(f"[StreamHTTP] Uvicorn server {SERVER_ID} stopped.")


# --- SSE Server ---
# Entire block for SSE server, including:
# - sse_session_queues dictionary
# - _SENTINEL_CLOSE_QUEUE_ constant
# - MCPRequestModelSSE Pydantic model
# - event_generator_v2 async function
# - start_sse_server async function and all its inner functions/routes:
#   - sse_startup
#   - health_check_sse
#   - mcp_sse_message_connect (@app_sse.get("/message"))
#   - mcp_sse_post (@app_sse.post("/mcp"))
#   - sse_client_close_session (@app_sse.post("/mcp/close"))
# IS REMOVED.


# --- STDIO Server ---
async def start_stdio_server() -> None:
    logger.info(f"[STDIO] Starting STDIO server")
    host_for_registration = parsed_env_sdk.get("MCP_SERVER_HOST") or \
                            parsed_env_sdk.get("MCP_SERVICE_NAME") or \
                            f"mcp-server-{SERVER_ID}"
    logger.info(f"[STDIO] Registration hostname: {host_for_registration}")

    # port_for_registration=0 as STDIO doesn't listen on a network port itself.
    # host_port is for registering associated HTTP/SSE ports if this STDIO service
    # is part of a larger deployment where other components handle network traffic.
    port_for_registration = 0
    host_port = 0
    if os.environ.get("DOCKER_ENV") == "true":
        env_host_port = os.environ.get("HOST_PORT")
        if env_host_port:
            try:
                host_port = int(env_host_port)
                logger.info(f"[STDIO] Using HOST_PORT from environment: {host_port}")
            except ValueError:
                logger.warning(f"[STDIO] Invalid HOST_PORT environment variable: {env_host_port}")
    else: # Try reading from .env file if HOST_PORT env var is not set
        try:
            if os.path.exists("/app/.env"):
                with open("/app/.env", "r") as f_env:
                    for line_in_file in f_env:
                        if line_in_file.startswith("export HOST_PORT="):
                            env_host_port_file = line_in_file.strip().split("=", 1)[1]
                            try:
                                host_port = int(env_host_port_file)
                                logger.info(f"[STDIO] Using HOST_PORT from /app/.env: {host_port}")
                                break # Found and parsed HOST_PORT, exit loop
                            except ValueError:
                                logger.warning(f"[STDIO] Invalid HOST_PORT in /app/.env: {env_host_port_file}")
        except Exception as e: # This except is for the try block wrapping file operations
            logger.warning(f"[STDIO] Failed to read HOST_PORT from /app/.env: {e}")

    try:
        # 确保所有协议端口都被正确注册
        # 添加元数据以支持Traefik路由
        traefik_mcp_domain_suffix = os.environ.get("TRAEFIK_MCP_DOMAIN_SUFFIX", "localhost")
        mcp_instance_hostname = f"mcp-{SERVER_ID}.{traefik_mcp_domain_suffix}"

        registration_data = {
            "server_id": SERVER_ID,
            "host": host_for_registration,
            "port": port_for_registration, # STDIO本身不基于网络端口
            "port_http": host_port, # 关联的HTTP端口
            "port_streamhttp": host_port, # 关联的StreamHTTP端口
            "port_stdio": 1, # 标记为支持STDIO
            "metadata": {
                "traefik_hostname": mcp_instance_hostname,
                "protocols": ["stdio"], # Removed "sse" if it was here
                "version": SERVER_VERSION
            }
        }
        if await register_server(**registration_data):
            logger.info(f"[STDIO] Server {SERVER_ID} (stdio) registered to Redis.")
            asyncio.create_task(redis_registration_heartbeat(**registration_data))
        else:
            logger.error(f"[STDIO] Failed to register server {SERVER_ID} (stdio) to Redis.")
    except Exception as e:
        logger.error(f"[STDIO] Error during Redis registration for STDIO: {e}", exc_info=True)

    await mcp_server.initialize_server()
    logger.info(f"[STDIO] Server {SERVER_ID} initialized. Waiting for input...")
    asyncio.create_task(mcp_server.start_heartbeat())
    logger.info(f"[STDIO {SERVER_ID}] Session cleanup task (MCPServer.start_heartbeat) started.")

    current_stdio_session_id: Optional[str] = None
    line_count = 0

    while True:
        raw_line = "" # Initialize for error reporting
        req_id_for_error = None # Initialize for error reporting
        try:
            raw_line = await asyncio.get_event_loop().run_in_executor(None, sys.stdin.readline)
            line_count += 1
            if not raw_line:
                logger.info(f"[STDIO] EOF received (after {line_count-1} lines), exiting.")
                break

            request_json_str = raw_line.strip()
            if not request_json_str:
                logger.debug("[STDIO] Received empty line, skipping.")
                continue

            logger.debug(f"[STDIO] Raw input line #{line_count}: '{request_json_str}'")
            request_json = json.loads(request_json_str)
            logger.debug(f"[STDIO] <<< {request_json}")

            req_id_for_error = request_json.get("id") # Capture early for error reporting
            method = request_json.get("method")
            params = request_json.get("params", {})

            headers_for_auth = params.pop("headers_for_auth", {})
            if not isinstance(headers_for_auth, dict):
                logger.warning("[STDIO] 'headers_for_auth' in params is not a dict, using empty dict.")
                headers_for_auth = {}

            response_data = None
            if not method:
                 logger.error(f"[STDIO] Received request without 'method' field: {request_json_str}")
                 response_data = {"jsonrpc": "2.0", "id": req_id_for_error, "error": {"code": ErrorCode.INVALID_REQUEST.value, "message": "Missing 'method' in request."}}
            elif method == "initialize":
                response_data = await mcp_server.handle_initialize(
                    request_id=req_id_for_error, params=params, headers=headers_for_auth
                )
                if "result" in response_data:
                    current_stdio_session_id = response_data["result"].get("sessionId") or response_data["result"].get("session_id")
                    logger.info(f"[STDIO] Session {current_stdio_session_id} initialized.")
            elif method == "tools/refresh":
                response_data = await mcp_server.handle_request(
                    method="tools/refresh", params=params, request_id=req_id_for_error,
                    headers=headers_for_auth,
                    session_id_from_transport=params.get("sessionId", current_stdio_session_id)
                )
            else:
                session_id_to_use = params.get("sessionId", current_stdio_session_id)
                response_data = await mcp_server.handle_request(
                    method=method, params=params, request_id=req_id_for_error,
                    headers=headers_for_auth, session_id_from_transport=session_id_to_use
                )

            json_response_str = json.dumps(response_data)
            logger.debug(f"[STDIO] >>> {json_response_str}")
            print(json_response_str, flush=True)

            if method == "shutdown" and "result" in response_data:
                logger.info("[STDIO] Shutdown command processed successfully, exiting.")
                break
        except json.JSONDecodeError as e_json:
            logger.error(f"[STDIO] JSON parse error: {e_json}. Input: '{raw_line.strip()}'")
            err_resp_str = json.dumps({"jsonrpc": "2.0", "id": None, "error": {"code": -32700, "message": f"Parse error: {e_json}"}})
            print(err_resp_str, flush=True)
        except Exception as e:
            logger.error(f"[STDIO] Error processing request (id: {req_id_for_error}): {e}", exc_info=True)
            err_resp_str = json.dumps({"jsonrpc": "2.0", "id": req_id_for_error, "error": {"code": ErrorCode.GENERIC_ERROR.value, "message": f"Server error: {e}"}})
            print(err_resp_str, flush=True)

    logger.info("[STDIO] STDIO processing loop finished.")


# --- Main Application Logic ---
async def main():
    # Basic logging config, can be overridden by Uvicorn's log_config if not None
    logging.basicConfig(
        level=os.environ.get("LOG_LEVEL", "INFO").upper(),
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[logging.StreamHandler(sys.stdout)] # Ensure logs go to stdout for Docker
    )

    parser = argparse.ArgumentParser(description="MCP Server V2")
    parser.add_argument(
        "--transport",
        type=str,
        choices=["streamhttp", "stdio"], # Removed 'sse'
        default=os.environ.get("MCP_TRANSPORT", "streamhttp"),
        help="Communication transport to use (default: streamhttp)",
    )
    parser.add_argument("--http-port", type=int, default=int(os.environ.get("MCP_HTTP_PORT", "5000")), help="Port for StreamHTTP server")
    # Removed --sse-port as SSE is now part of StreamHTTP on the same port

    # Add other arguments like --tools-dir, --auth-mode etc. if needed
    # parser.add_argument("--tools-dir", type=str, default=os.environ.get("TOOLS_DIR"), help="Directory to load tools from")

    args = parser.parse_args()

    logger.info(f"--- [MultiProtocol MCP Server V2 ({SERVER_ID})] Starting --- ")
    logger.info(f"Selected transport: {args.transport}")

    # Load tools, initialize mcp_server based on args if necessary
    # Example: if args.tools_dir: mcp_server.load_tools_from_directory(args.tools_dir)

    if args.transport == "streamhttp":
        logger.info(f"Using StreamHTTP transport on port {args.http_port}")
        await start_streamhttp_server(port=args.http_port)
    elif args.transport == "stdio":
        await start_stdio_server()
    else:
        logger.error(f"Unsupported transport type: {args.transport}")
        sys.exit(1)

# --- MCP Router (Conceptual / Optional Integration Point) ---
# This router is kept separate and is not directly used by the `main` execution flow above.
# It can be used if embedding MCP logic into a larger, custom FastAPI application.
async def mcp_router(method: str, path: str, request: FastAPIRequest, body: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    logger.info(f"mcp_router: {method} {path}")

    if method == "OPTIONS":
        logger.info(f"Handling OPTIONS preflight request for path: {path}")
        return {
            "status_code": 204,
            "headers": {
                "Access-Control-Allow-Origin": "*", # Restrict in production
                "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
                "Access-Control-Allow-Headers": "Content-Type, Accept, Authorization, X-Mcp-Project-Id, X-Mcp-User-Id",
                "Access-Control-Max-Age": "86400",
            },
            "content": "" # No content for 204
        }

    # Note: Real SSE streaming cannot be fully handled by returning a simple dict here.
    # This is a placeholder or for a system where another component handles the EventSourceResponse.
    # if path == "/sse" and method == "GET": # Typically /message for SSE data stream # Block removed
    #     logger.warning("mcp_router /sse GET is a stub; full SSE requires EventSourceResponse.")
    #     return {
    #         "status_code": 501, "headers": {"Content-Type": "application/json"},
    #         "content": json.dumps({"jsonrpc":"2.0", "id":None, "error": {"code": -32000, "message": "SSE GET /sse endpoint is conceptual in mcp_router."}})
    #     }

    if path == "/mcp" and method == "GET":
        query_params = dict(request.query_params)
        params_json: Dict[str, Any] = {}
        req_id_get = None
        try:
            if "params" in query_params:
                params_json_str = query_params.pop("params")
                params_json = json.loads(params_json_str)
            for k, v in query_params.items(): # Merge remaining raw query params
                if k not in params_json: params_json[k] = v

            req_id_get = params_json.get("id") # Get id from parsed params
            mcp_method_get = params_json.pop("method", "initialize") # Default to initialize if not specified

            handler_func = mcp_server.handle_initialize if mcp_method_get == "initialize" else mcp_server.handle_request

            response_content = await handler_func(
                request_id=req_id_get,
                method=mcp_method_get, # Needed if using handle_request
                params=params_json,
                headers=dict(request.headers),
                session_id_from_transport=params_json.get("sessionId") # If applicable for GET
            )
            return {"status_code": 200, "headers": {"Content-Type": "application/json"}, "content": json.dumps(response_content)}
        except json.JSONDecodeError as e:
            logger.warning(f"GET /mcp: Query 'params' JSON decode error: {e}")
            return {"status_code": 400, "headers": {"Content-Type": "application/json"}, "content": json.dumps({"jsonrpc":"2.0", "id":req_id_get, "error": {"code": -32700, "message": f"Invalid JSON in 'params' query: {e}"}})}
        except Exception as e:
            logger.error(f"GET /mcp: Error processing request: {e}", exc_info=True)
            return {"status_code": 500, "headers": {"Content-Type": "application/json"}, "content": json.dumps({"jsonrpc": "2.0", "id": req_id_get, "error": {"code": -32000, "message": f"Server error: {e}"}})}

    if path == "/mcp" and method == "POST":
        if not body:
            return {"status_code": 400, "headers": {"Content-Type": "application/json"}, "content": json.dumps({"jsonrpc":"2.0", "id":None, "error": {"code": -32600, "message": "Invalid Request: Missing body"}})}

        mcp_method_post = body.get("method")
        params_post = body.get("params", {})
        req_id_post = body.get("id")

        if not mcp_method_post:
            return {"status_code": 400, "headers": {"Content-Type": "application/json"}, "content": json.dumps({"jsonrpc":"2.0", "id":req_id_post, "error": {"code": -32600, "message": "Invalid Request: Missing 'method'"}})}

        session_id_post = params_post.get("sessionId") or params_post.get("session_id")

        try:
            handler_func = mcp_server.handle_initialize if mcp_method_post == "initialize" else mcp_server.handle_request
            response_content = await handler_func(
                request_id=req_id_post,
                method=mcp_method_post, # Needed if using handle_request
                params=params_post,
                headers=dict(request.headers),
                session_id_from_transport=session_id_post
            )

            http_status = 200
            if "error" in response_content and response_content["error"]:
                err_code = response_content["error"].get("code")
                if err_code == ErrorCode.AUTHENTICATION_FAILED.value: http_status = 401
                elif err_code == ErrorCode.AUTHORIZATION_FAILED.value: http_status = 403
                elif err_code == ErrorCode.INVALID_REQUEST.value: http_status = 400
                elif err_code == ErrorCode.SESSION_NOT_FOUND.value: http_status = 404
                elif err_code in [ErrorCode.INITIALIZATION_FAILED.value, ErrorCode.TOOL_EXECUTION_ERROR.value, ErrorCode.GENERIC_ERROR.value, ErrorCode.SESSION_NOT_INITIALIZED.value]: http_status = 500

            return {"status_code": http_status, "headers": {"Content-Type": "application/json"}, "content": json.dumps(response_content)}
        except Exception as e:
            logger.error(f"POST /mcp: Error processing request: {e}", exc_info=True)
            return {"status_code": 500, "headers": {"Content-Type": "application/json"}, "content": json.dumps({"jsonrpc": "2.0", "id": req_id_post, "error": {"code": -32000, "message": f"Server error: {e}"}})}

    return {"status_code": 404, "headers": {"Content-Type": "application/json"}, "content": json.dumps({"jsonrpc":"2.0", "id":None, "error": {"code": -32601, "message": f"Method/Path not found: {method} {path}"}})}

if __name__ == "__main__":
    # Ensure basicConfig is called only if no handlers are already configured (e.g., by Uvicorn).
    # However, main() now configures logging itself.
    # if not logging.getLogger().hasHandlers():
    #     logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

    logger.info(f"--- [MultiProtocol MCP Server V2 ({SERVER_ID})] Starting --- ")
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info(f"--- [MultiProtocol MCP Server V2 ({SERVER_ID})] User interrupted (KeyboardInterrupt) ---")
    except Exception as e_run:
        logger.error(f"--- [MultiProtocol MCP Server V2 ({SERVER_ID})] Critical asyncio error in main: {e_run} ---", exc_info=True)
    finally:
        logger.info(f"--- [MultiProtocol MCP Server V2 ({SERVER_ID})] Exiting __main__ ---")