#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
AI 提供商 Schema
"""

from typing import Optional, List, Dict, Any
from datetime import datetime
import uuid
from pydantic import BaseModel, Field, validator

class AIProviderBase(BaseModel):
    """AI 提供商基础 Schema"""
    name: str = Field(..., description="提供商名称，如 openai, azure, anthropic 等")
    display_name: str = Field(..., description="显示名称")
    description: Optional[str] = Field(None, description="描述")
    api_base_url: Optional[str] = Field(None, description="API 基础 URL")
    auth_type: str = Field("api_key", description="认证类型：api_key, oauth 等")
    icon_url: Optional[str] = Field(None, description="图标 URL")
    status: str = Field("active", description="状态：active, inactive")
    is_builtin: bool = Field(False, description="是否内置提供商")
    config_schema: Optional[Dict[str, Any]] = Field(None, description="配置模式")

class AIProviderCreate(AIProviderBase):
    """创建 AI 提供商 Schema"""
    pass

class AIProviderUpdate(BaseModel):
    """更新 AI 提供商 Schema"""
    display_name: Optional[str] = Field(None, description="显示名称")
    description: Optional[str] = Field(None, description="描述")
    api_base_url: Optional[str] = Field(None, description="API 基础 URL")
    auth_type: Optional[str] = Field(None, description="认证类型：api_key, oauth 等")
    icon_url: Optional[str] = Field(None, description="图标 URL")
    status: Optional[str] = Field(None, description="状态：active, inactive")
    config_schema: Optional[Dict[str, Any]] = Field(None, description="配置模式")

class AIProviderInDB(AIProviderBase):
    """数据库中的 AI 提供商 Schema"""
    id: uuid.UUID
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        model_config = {"from_attributes": True}

class AIProviderResponse(BaseModel):
    """AI 提供商响应 Schema"""
    success: bool = True
    message: Optional[str] = None
    data: Optional[AIProviderInDB] = None

class AIProviderListResponse(BaseModel):
    """AI 提供商列表响应 Schema"""
    success: bool = True
    message: Optional[str] = None
    data: List[AIProviderInDB] = []
    total: int = 0
    page: int = 1
    page_size: int = 20
