#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
MCP服务器路由

提供MCP服务器的管理功能，包括创建、启动、停止和删除服务器
"""

import logging
import uuid
import json as _json
import subprocess
import os
import shutil
from datetime import datetime
from typing import Dict, Any, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, Body, Request
from fastapi.responses import JSONResponse
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import func, and_, select as sql_select

from db.database import get_db
from models.user import User
from core.auth import get_current_user

from .. import router
from ..models import MCPServer
from ..mcp_server_adapter import mcp_server_adapter
from ..mcp_proxy import mcp_proxy

# 导入MCP工具相关的模块（用于一键集成功能）
try:
    from models.mcp_tool import MCPTool
    from services.mcp_tool import MCPToolService
    from schemas.mcp_tool import MCPToolCreate
    MCP_TOOL_AVAILABLE = True
except ImportError:
    MCP_TOOL_AVAILABLE = False

# 导入AI助手MCP服务器模型
try:
    from models.ai_mcp_server import AIMCPServer
    AI_MCP_SERVER_AVAILABLE = True
except ImportError:
    AI_MCP_SERVER_AVAILABLE = False

logger = logging.getLogger(__name__)

# 获取MCP服务器列表
@router.get("/servers", response_model=Dict[str, Any])
async def get_server_list(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
    page: int = Query(1, ge=1),
    page_size: int = Query(10, ge=1, le=100)
):
    """获取MCP服务器列表"""
    try:
        # 计算偏移量
        offset = (page - 1) * page_size

        # 查询服务器总数
        result = await db.execute(select(func.count()).select_from(MCPServer))
        total = result.scalar()

        # 查询服务器列表
        result = await db.execute(select(MCPServer).offset(offset).limit(page_size))
        servers = result.scalars().all()

        # 转换为字典列表
        server_list = []
        for server in servers:
            server_dict = {
                "id": str(server.id),
                "name": server.name,
                "description": server.description,
                "transport_types": server.transport_types if hasattr(server, 'transport_types') else [server.transport_type],
                "deployment_mode": server.deployment_mode if hasattr(server, 'deployment_mode') else "local",
                "install_dir": server.install_dir,
                "simulation": server.simulation,
                "use_system_ai": server.use_system_ai,
                "supports_media": server.supports_media,
                "enabled": server.enabled,
                "created_at": server.created_at.isoformat(),
                "updated_at": server.updated_at.isoformat(),
                "created_by_id": str(server.created_by_id) if server.created_by_id else None
            }

            # 获取服务器的真实状态
            try:
                # 首先检查内存中的状态（快速检查）
                if str(server.id) in mcp_server_adapter.running_servers:
                    server_dict["status"] = "running"
                else:
                    # 查询最新的进程记录
                    from ..models import MCPServerProcess
                    process_result = await db.execute(
                        select(MCPServerProcess)
                        .where(MCPServerProcess.server_id == server.id)
                        .order_by(MCPServerProcess.created_at.desc())
                        .limit(1)
                    )
                    process = process_result.scalars().first()

                    if process and process.status == "running" and not process.stopped_at:
                        # 进程记录显示正在运行，但内存中没有记录
                        # 尝试通过 get_server_status 获取真实状态
                        status_result = await mcp_server_adapter.get_server_status(str(server.id))
                        if status_result.get("status") == "running":
                            # 服务器实际上在运行，更新内存中的状态
                            mcp_server_adapter.running_servers[str(server.id)] = {
                                "status": "running",
                                "started_at": process.started_at.isoformat() if process.started_at else datetime.now().isoformat(),
                                "use_system_ai": server.use_system_ai
                            }
                            server_dict["status"] = "running"
                        else:
                            # 服务器实际上已停止，更新进程记录
                            process.status = "stopped"
                            process.stopped_at = datetime.now()
                            await db.commit()
                            server_dict["status"] = "stopped"
                    else:
                        server_dict["status"] = "stopped"
            except Exception as status_error:
                logger.warning(f"获取服务器 {server.id} 状态失败: {str(status_error)}")
                # 如果获取状态失败，使用内存中的状态作为备份
                server_dict["status"] = "running" if str(server.id) in mcp_server_adapter.running_servers else "stopped"

            server_list.append(server_dict)

        return {
            "success": True,
            "message": "获取MCP服务器列表成功",
            "data": server_list,
            "total": total,
            "page": page,
            "page_size": page_size
        }
    except Exception as e:
        logger.error(f"获取MCP服务器列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取MCP服务器列表失败: {str(e)}")

# 获取可用的MCP服务器列表
@router.get("/servers/available", response_model=Dict[str, Any])
async def get_available_servers(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """获取可用的MCP服务器列表"""
    try:
        # 查询所有服务器，不再仅限于启用的服务器
        result = await db.execute(select(MCPServer))
        servers = result.scalars().all()

        # 转换为字典列表
        server_list = []
        for server in servers:
            server_dict = {
                "id": str(server.id),
                "name": server.name,
                "description": server.description,
                "transport_types": server.transport_types if hasattr(server, 'transport_types') else [server.transport_type],
                "deployment_mode": server.deployment_mode if hasattr(server, 'deployment_mode') else "local",
                "supports_media": server.supports_media
            }

            # 获取服务器的真实状态
            try:
                # 首先检查内存中的状态（快速检查）
                if str(server.id) in mcp_server_adapter.running_servers:
                    server_dict["status"] = "running"
                else:
                    # 查询最新的进程记录
                    from ..models import MCPServerProcess
                    process_result = await db.execute(
                        select(MCPServerProcess)
                        .where(MCPServerProcess.server_id == server.id)
                        .order_by(MCPServerProcess.created_at.desc())
                        .limit(1)
                    )
                    process = process_result.scalars().first()

                    if process and process.status == "running" and not process.stopped_at:
                        # 进程记录显示正在运行，但内存中没有记录
                        # 尝试通过 get_server_status 获取真实状态
                        status_result = await mcp_server_adapter.get_server_status(str(server.id))
                        if status_result.get("status") == "running":
                            # 服务器实际上在运行，更新内存中的状态
                            mcp_server_adapter.running_servers[str(server.id)] = {
                                "status": "running",
                                "started_at": process.started_at.isoformat() if process.started_at else datetime.now().isoformat(),
                                "use_system_ai": server.use_system_ai
                            }
                            server_dict["status"] = "running"
                        else:
                            # 服务器实际上已停止，更新进程记录
                            process.status = "stopped"
                            process.stopped_at = datetime.now()
                            await db.commit()
                            server_dict["status"] = "stopped"
                    else:
                        server_dict["status"] = "stopped"
            except Exception as status_error:
                logger.warning(f"获取服务器 {server.id} 状态失败: {str(status_error)}")
                # 如果获取状态失败，使用内存中的状态作为备份
                server_dict["status"] = "running" if str(server.id) in mcp_server_adapter.running_servers else "stopped"

            server_list.append(server_dict)

        return {
            "success": True,
            "message": "获取可用MCP服务器列表成功",
            "data": server_list
        }
    except Exception as e:
        logger.error(f"获取可用MCP服务器列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取可用MCP服务器列表失败: {str(e)}")

# 创建MCP服务器
@router.post("/servers", response_model=Dict[str, Any])
async def create_server(
    project_id: uuid.UUID,
    server_data_payload: Dict[str, Any] = Body(...),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """创建MCP服务器"""
    try:
        server_config_dict = server_data_payload.copy()

        logger.info(f"Received project_id '{str(project_id)}' from path for MCP server creation.")
        server_config_dict["project_id"] = str(project_id)

        # 生成服务器ID
        server_id_uuid = uuid.uuid4()
        server_config_dict["id"] = str(server_id_uuid)

        # 设置安装目录
        install_dir = server_config_dict.get("install_dir")
        if not install_dir:
            install_dir = f"mcp_servers/{str(server_id_uuid)}"
            server_config_dict["install_dir"] = install_dir

        # 添加认证令牌，用于获取系统令牌
        server_config_dict["auth_header"] = current_user.get_auth_header() if hasattr(current_user, 'get_auth_header') else None

        # 创建服务器
        success, message, created_server_id = await mcp_server_adapter.create_server(server_config_dict)

        if not success:
            raise HTTPException(status_code=400, detail=message)

        # 根据部署模式设置默认命令
        deployment_mode = server_config_dict.get("deployment_mode", "local")
        default_command = "python main.py"  # 默认命令

        if deployment_mode == "docker":
            default_command = "docker run -d --name mcp-server"
        elif deployment_mode == "npm":
            default_command = "npx mcp-server"

        # 创建数据库记录
        server_db_record = MCPServer(
            id=server_id_uuid,
            name=server_config_dict.get("name"),
            description=server_config_dict.get("description"),
            project_id=project_id,
            transport_types=server_config_dict.get("transport_types", ["streamhttp"]),
            deployment_mode=deployment_mode,
            install_dir=server_config_dict.get("install_dir"),
            simulation=server_config_dict.get("simulation", False),
            use_system_ai=server_config_dict.get("use_system_ai", False),
            supports_media=server_config_dict.get("supports_media", True),
            command=server_config_dict.get("command") or default_command,
            args=_json.dumps(server_config_dict.get("args")) if server_config_dict.get("args") else None,
            env=_json.dumps(server_config_dict.get("env")) if server_config_dict.get("env") else None,
            created_by_id=current_user.id
        )

        db.add(server_db_record)
        await db.commit()

        return {
            "success": True,
            "message": "创建MCP服务器成功",
            "data": {
                "id": str(server_db_record.id),
                "name": server_db_record.name,
                "project_id": str(server_db_record.project_id),
                "description": server_db_record.description,
                "transport_types": server_db_record.transport_types if hasattr(server_db_record, 'transport_types') else [server_db_record.transport_type if hasattr(server_db_record, 'transport_type') else "streamhttp"],
                "install_dir": server_db_record.install_dir,
                "simulation": server_db_record.simulation,
                "use_system_ai": server_db_record.use_system_ai,
                "supports_media": server_db_record.supports_media,
                "created_at": server_db_record.created_at.isoformat(),
                "updated_at": server_db_record.updated_at.isoformat(),
                "created_by_id": str(server_db_record.created_by_id) if server_db_record.created_by_id else None
            }
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建MCP服务器失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"创建MCP服务器失败: {str(e)}")

# 更新MCP服务器
@router.put("/servers/{server_id}", response_model=Dict[str, Any])
async def update_server(
    server_id: str,
    server_data: Dict[str, Any],
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """更新MCP服务器"""
    try:
        # 查询服务器
        result = await db.execute(select(MCPServer).where(MCPServer.id == uuid.UUID(server_id)))
        server = result.scalars().first()
        if not server:
            raise HTTPException(status_code=404, detail="MCP服务器不存在")

        # 更新服务器
        server.name = server_data.get("name", server.name)
        server.description = server_data.get("description", server.description)
        server.transport_types = server_data.get("transport_types", server.transport_types if hasattr(server, 'transport_types') else [server.transport_type])
        server.deployment_mode = server_data.get("deployment_mode", server.deployment_mode if hasattr(server, 'deployment_mode') else "local")
        server.docker_image = server_data.get("docker_image", server.docker_image if hasattr(server, 'docker_image') else None)
        server.docker_tag = server_data.get("docker_tag", server.docker_tag if hasattr(server, 'docker_tag') else "latest")
        server.npm_package = server_data.get("npm_package", server.npm_package if hasattr(server, 'npm_package') else None)
        server.npm_version = server_data.get("npm_version", server.npm_version if hasattr(server, 'npm_version') else "latest")
        server.simulation = server_data.get("simulation", server.simulation)
        server.use_system_ai = server_data.get("use_system_ai", server.use_system_ai)
        server.supports_media = server_data.get("supports_media", server.supports_media)
        server.env = server_data.get("env", server.env)

        await db.commit()

        # 检查是否更新了通信方式
        transport_types_changed = False
        if "transport_types" in server_data and server_data["transport_types"] != server.transport_types:
            transport_types_changed = True
            logger.info(f"通信方式已更新: {server.transport_types} -> {server_data['transport_types']}")

        # 更新适配器中的服务器配置
        mcp_server_adapter.register_server({
            "id": server_id,
            "name": server.name,
            "description": server.description,
            "transport_types": server.transport_types if hasattr(server, 'transport_types') else [server.transport_type],
            "deployment_mode": server.deployment_mode if hasattr(server, 'deployment_mode') else "local",
            "docker_image": server.docker_image if hasattr(server, 'docker_image') else None,
            "docker_tag": server.docker_tag if hasattr(server, 'docker_tag') else "latest",
            "npm_package": server.npm_package if hasattr(server, 'npm_package') else None,
            "npm_version": server.npm_version if hasattr(server, 'npm_version') else "latest",
            "install_dir": server.install_dir,
            "simulation": server.simulation,
            "use_system_ai": server.use_system_ai,
            "supports_media": server.supports_media,
            "env": server.env
        })

        # 如果通信方式已更新，需要重新生成服务器模板文件
        if transport_types_changed:
            logger.info(f"通信方式已更新，重新生成服务器模板文件: {server_id}")
            # 获取主要通信方式
            primary_transport_type = server.transport_types[0] if server.transport_types else "streamhttp"

            # 重新生成服务器模板文件
            install_dir = server.install_dir
            if os.path.exists(install_dir):
                # 备份原有的 main.py 文件
                if os.path.exists(f"{install_dir}/main.py"):
                    backup_file = f"{install_dir}/main.py.bak"
                    shutil.copy(f"{install_dir}/main.py", backup_file)
                    logger.info(f"已备份原有的 main.py 文件: {backup_file}")

                # 获取模板目录
                template_dir = mcp_server_adapter.server_templates.get(primary_transport_type)
                if template_dir and template_dir.exists():
                    # 复制模板文件到服务器目录
                    for file in template_dir.glob("*"):
                        if file.is_file():
                            shutil.copy(file, os.path.join(install_dir, file.name))
                            logger.info(f"已复制模板文件: {file.name} 到 {install_dir}")
                else:
                    logger.warning(f"模板目录不存在: {primary_transport_type}")

        return {
            "success": True,
            "message": "更新MCP服务器成功" + (" 并重新生成了服务器模板文件" if transport_types_changed else ""),
            "data": {
                "id": str(server.id),
                "name": server.name,
                "description": server.description,
                "transport_types": server.transport_types if hasattr(server, 'transport_types') else [server.transport_type],
                "deployment_mode": server.deployment_mode if hasattr(server, 'deployment_mode') else "local",
                "docker_image": server.docker_image if hasattr(server, 'docker_image') else None,
                "docker_tag": server.docker_tag if hasattr(server, 'docker_tag') else "latest",
                "npm_package": server.npm_package if hasattr(server, 'npm_package') else None,
                "npm_version": server.npm_version if hasattr(server, 'npm_version') else "latest",
                "install_dir": server.install_dir,
                "simulation": server.simulation,
                "use_system_ai": server.use_system_ai,
                "supports_media": server.supports_media,
                "created_at": server.created_at.isoformat(),
                "updated_at": server.updated_at.isoformat(),
                "created_by_id": str(server.created_by_id) if server.created_by_id else None
            }
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新MCP服务器失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"更新MCP服务器失败: {str(e)}")

# 删除MCP服务器
@router.delete("/servers/{server_id}", response_model=Dict[str, Any])
async def delete_server(
    server_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """删除MCP服务器"""
    try:
        # 查询服务器
        result = await db.execute(select(MCPServer).where(MCPServer.id == uuid.UUID(server_id)))
        server = result.scalars().first()
        if not server:
            raise HTTPException(status_code=404, detail="MCP服务器不存在")

        # 如果服务器正在运行，先停止
        if server_id in mcp_server_adapter.running_servers:
            await mcp_server_adapter.stop_server(server_id)

        # 检查是否有 Docker 容器，如果有则强制删除
        try:
            # 获取容器名称
            container_name = f"mcp-server-{server_id}"

            # 检查容器是否存在
            process = subprocess.run(
                ["docker", "ps", "-a", "--filter", f"name={container_name}", "--format", "{{.ID}}"],
                capture_output=True,
                text=True
            )

            if process.stdout.strip():
                # 容器存在，强制删除
                logger.info(f"强制删除 Docker 容器: {container_name}")
                subprocess.run(
                    ["docker", "rm", "-f", container_name],
                    capture_output=True,
                    text=True
                )
                logger.info(f"Docker 容器已删除: {container_name}")
        except Exception as e:
            logger.warning(f"删除 Docker 容器失败: {str(e)}")
            # 继续执行，不要阻止流程

        # 查询并删除与该服务器关联的所有进程记录
        from ..models import MCPServerProcess
        process_result = await db.execute(
            select(MCPServerProcess).where(MCPServerProcess.server_id == uuid.UUID(server_id))
        )
        processes = process_result.scalars().all()

        # 删除所有关联的进程记录
        process_count = 0
        for process in processes:
            await db.delete(process)
            process_count += 1

        logger.info(f"删除了 {process_count} 个关联的进程记录")

        # 查询并删除与该服务器关联的所有工具映射
        from ..models import APIMCPToolMapping
        result = await db.execute(
            select(APIMCPToolMapping).where(APIMCPToolMapping.server_id == uuid.UUID(server_id))
        )
        mappings = result.scalars().all()

        # 记录删除的映射数量
        mapping_count = len(mappings)

        # 删除所有关联的映射
        for mapping in mappings:
            await db.delete(mapping)

        # 删除服务器目录
        install_dir = f"/app/mcp_servers/{server_id}"
        if os.path.exists(install_dir):
            try:
                logger.info(f"删除服务器目录: {install_dir}")
                shutil.rmtree(install_dir)
                logger.info(f"服务器目录已删除: {install_dir}")
            except Exception as e:
                logger.warning(f"删除服务器目录失败: {str(e)}")
                # 继续执行，不要阻止流程

        # 删除服务器
        await db.delete(server)
        await db.commit()

        # 从适配器中移除服务器
        mcp_server_adapter.unregister_server(server_id)

        return {
            "success": True,
            "message": f"删除MCP服务器成功，同时删除了 {process_count} 个进程记录和 {mapping_count} 个工具映射"
        }
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"删除MCP服务器失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除MCP服务器失败: {str(e)}")

# 启动MCP服务器
@router.post("/servers/{server_id}/start", response_model=Dict[str, Any])
async def start_server(
    server_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """启动MCP服务器"""
    try:
        # 查询服务器
        result = await db.execute(select(MCPServer).where(MCPServer.id == uuid.UUID(server_id)))
        server = result.scalars().first()
        if not server:
            raise HTTPException(status_code=404, detail="MCP服务器不存在")

        # 确保服务器已注册到适配器
        server_config = {
            "id": str(server.id),
            "name": server.name,
            "description": server.description,
            "transport_types": server.transport_types,
            "deployment_mode": server.deployment_mode,
            "install_dir": server.install_dir,
            "simulation": server.simulation,
            "use_system_ai": server.use_system_ai,
            "supports_media": server.supports_media,
            "env": server.env,
            "project_id": str(server.project_id) if server.project_id else None,
            # 添加认证令牌，用于获取系统令牌
            "auth_header": current_user.get_auth_header() if hasattr(current_user, 'get_auth_header') else None
        }
        mcp_server_adapter.register_server(server_config)

        # 调用适配器启动服务器
        raw_response = await mcp_server_adapter.start_server(server_id)

        if not isinstance(raw_response, tuple) or not (2 <= len(raw_response) <= 3):
            logger.error(f"MCP server adapter start_server returned an unexpected response format: {raw_response}")
            raise HTTPException(status_code=500, detail="Failed to start MCP server due to adapter error.")

        success = raw_response[0]
        message = raw_response[1] # Assuming message is always the second element
        server_data = raw_response[2] if len(raw_response) == 3 else {} # Default to empty dict if not provided

        if not success:
            # If not successful, the second element (message) is likely the error message.
            raise HTTPException(status_code=500, detail=str(message))

        # 更新数据库中的服务器信息（例如端口、状态）
        # 注意：server_data可能包含由适配器动态分配的端口等信息
        update_data = {"status": "running"}
        if server_data and server_data.get("port"):
            update_data["port"] = server_data.get("port") # 假设适配器返回主端口
        if server_data and server_data.get("host"):
            update_data["host"] = server_data.get("host")

        # 从 server_data 中提取元数据，特别是 streamhttp_endpoint
        metadata_from_adapter = server_data.get("metadata", {})
        if isinstance(metadata_from_adapter, dict) and metadata_from_adapter.get("streamhttp_endpoint"):
            # 如果我们想在主 Server 模型的 metadata 字段中也存储它
            # current_metadata = server.metadata or {} # Corrected from db_server
            # current_metadata.update(metadata_from_adapter) # 合并
            # update_data["metadata"] = current_metadata
            pass # 暂时不直接更新数据库的metadata，依赖MCP实例自己的注册

        # 更新已获取的 server 实例的属性
        for key, value in update_data.items():
            if hasattr(server, key):
                setattr(server, key, value)
            else:
                logger.warning(f"MCPServer model does not have attribute {key}. Skipping update for this field for server {server.id}.")

        updated_db_server = None # Initialize
        try:
            await db.commit()
            await db.refresh(server)
            updated_db_server = server # The server instance is now updated and refreshed
        except Exception as e_db_update:
            await db.rollback()
            logger.error(f"Database update failed for server {server.id} after successful start: {e_db_update}")
            # The server process started, but DB state might be inconsistent.
            # Original code logged an error but didn't raise HTTP 500 here.

        if not updated_db_server:
            # This means the db.commit() or db.refresh() failed.
            logger.error(f"服务器 {server.id} 启动成功，但更新数据库状态失败。")

        # 移除对 mcp_proxy.register_server 的调用，因为MCP实例会自行注册
        # # 向MCP代理注册或更新服务器信息 (冗余，但保持以防某些旧逻辑依赖)
        # # 代理应该从Redis读取权威信息，这里可以考虑移除
        # try:
        #     # 确保传递给代理的端口是 streamhttp 使用的端口
        #     # 这个信息应该由MCP实例启动后自行注册到Redis的元数据中更为可靠
        #     # 此处传递的 port 可能不准确或不完整
        #     main_port_for_proxy = server_data.get("port") # 假设这是主监听端口
        #     if main_port_for_proxy:
        #         await mcp_proxy.register_server(
        #             server_id=str(db_server.id),
        #             host=server_data.get("host", "localhost"),
        #             port=int(main_port_for_proxy),
        #             metadata=metadata_from_adapter # 传递适配器返回的元数据
        #         )
        #     else:
        #         logger.warning(f"服务器 {server_id} 启动，但没有明确的主端口信息传递给代理注册。")
        # except Exception as e_proxy_reg:
        #     logger.error(f"服务器 {server_id} 启动成功，但向MCP代理注册/更新信息失败: {e_proxy_reg}")

        response_data = {
                "server_id": str(server.id),
                "name": server.name,
            "status": "running",  # Status should be running at this point
            "port": None, # Default to None
            "host": None  # Default to None
        }

        # Attempt to get port and host from server_data, which was used to populate update_data
        # These keys would have been added to update_data if they existed in server_data
        if server_data and server_data.get("port"):
            response_data["port"] = server_data.get("port")
        if server_data and server_data.get("host"):
            response_data["host"] = server_data.get("host")

        return {
            "success": True,
            "message": f"服务器 {server.name} (ID: {server.id}) 启动成功",
            "data": response_data
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"启动MCP服务器失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"启动MCP服务器失败: {str(e)}")

# 停止MCP服务器
@router.post("/servers/{server_id}/stop", response_model=Dict[str, Any])
async def stop_server(
    server_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """停止MCP服务器"""
    try:
        # 查询服务器
        result = await db.execute(select(MCPServer).where(MCPServer.id == uuid.UUID(server_id)))
        server = result.scalars().first()
        if not server:
            raise HTTPException(status_code=404, detail="MCP服务器不存在")

        # 停止服务器
        success, message = await mcp_server_adapter.stop_server(server_id)

        if not success:
            raise HTTPException(status_code=400, detail=message)

        # 从MCP代理中注销服务器
        await mcp_proxy.unregister_server(server_id)
        logger.info(f"已从MCP代理注销服务器: {server_id}")

        # 更新数据库中的进程记录
        from ..models import MCPServerProcess
        process_result = await db.execute(
            select(MCPServerProcess)
            .where(MCPServerProcess.server_id == server.id)
            .where(MCPServerProcess.status == "running")
            .where(MCPServerProcess.stopped_at.is_(None))
            .order_by(MCPServerProcess.created_at.desc())
        )
        process = process_result.scalars().first()

        if process:
            # 更新进程状态
            process.status = "stopped"
            process.stopped_at = datetime.now()
            await db.commit()
            logger.info(f"已更新服务器进程记录状态为已停止: {server_id}")

        # 强制刷新服务器状态缓存
        await mcp_server_adapter.update_server_status(server_id, "stopped")
        logger.info(f"已强制更新服务器状态缓存: {server_id}")

        return {
            "success": True,
            "message": "停止MCP服务器成功"
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"停止MCP服务器失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"停止MCP服务器失败: {str(e)}")

# 获取MCP服务器状态
@router.get("/servers/{server_id}/status", response_model=Dict[str, Any])
async def get_server_status(
    server_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """获取MCP服务器状态"""
    try:
        # 查询服务器
        result = await db.execute(select(MCPServer).where(MCPServer.id == uuid.UUID(server_id)))
        server = result.scalars().first()
        if not server:
            raise HTTPException(status_code=404, detail="MCP服务器不存在")

        # 确保服务器已注册到适配器
        server_config = {
            "id": str(server.id),
            "name": server.name,
            "description": server.description,
            "transport_types": server.transport_types if hasattr(server, 'transport_types') else [server.transport_type],
            "deployment_mode": server.deployment_mode if hasattr(server, 'deployment_mode') else "local",
            "install_dir": server.install_dir,
            "simulation": server.simulation,
            "use_system_ai": server.use_system_ai,
            "supports_media": server.supports_media,
            "env": server.env,
            "project_id": str(server.project_id) if server.project_id else None
        }
        mcp_server_adapter.register_server(server_config)

        # 查询最新的进程记录
        from ..models import MCPServerProcess
        process_result = await db.execute(
            select(MCPServerProcess)
            .where(MCPServerProcess.server_id == server.id)
            .order_by(MCPServerProcess.created_at.desc())
            .limit(1)
        )
        process = process_result.scalars().first()

        # 如果有进程记录且状态为运行中，但内存中没有记录，则更新内存中的状态
        if process and process.status == "running" and not process.stopped_at and str(server.id) not in mcp_server_adapter.running_servers:
            mcp_server_adapter.running_servers[str(server.id)] = {
                "status": "running",
                "started_at": process.started_at.isoformat() if process.started_at else datetime.now().isoformat(),
                "use_system_ai": server.use_system_ai,
                "deployment_mode": server.deployment_mode if hasattr(server, 'deployment_mode') else "local"
            }
            logger.info(f"从数据库恢复服务器运行状态: {server_id}")

        # 获取服务器状态
        status = await mcp_server_adapter.get_server_status(server_id)

        # 如果状态为运行中，但数据库中没有记录或记录状态不是运行中，则更新数据库
        if status.get("status") == "running" and (not process or process.status != "running" or process.stopped_at):
            # 创建新的进程记录
            new_process = MCPServerProcess(
                server_id=server.id,
                status="running",
                started_at=datetime.now(),
                process_id=0,  # 未知PID
                logs=f"端口: {status.get('port', 3000)}"
            )
            db.add(new_process)
            await db.commit()
            logger.info(f"更新服务器进程记录: {server_id}")

        return {
            "success": True,
            "message": "获取MCP服务器状态成功",
            "data": status
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取MCP服务器状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取MCP服务器状态失败: {str(e)}")

# 获取MCP服务器工具列表
@router.get("/servers/{server_id}/tools", response_model=List[Dict[str, Any]])
async def get_server_tools(
    server_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """获取MCP服务器工具列表"""
    try:
        # 检查服务器是否存在
        from ..models import MCPServer, APIMCPToolMapping, SAPIEndpoint
        from sqlalchemy.orm import joinedload

        # 首先，确保服务器存在
        server_result = await db.execute(select(MCPServer).where(MCPServer.id == uuid.UUID(server_id)))
        server = server_result.scalar_one_or_none()
        if not server:
            raise HTTPException(status_code=404, detail=f"MCP服务器 {server_id} 不存在")

        # 查询与该服务器关联的所有启用的工具映射
        # 预加载 APIMCPToolMapping.endpoint 以访问 SAPIEndpoint.parameters (虽然我们现在直接用 mapping.input_schema)
        mappings_result = await db.execute(
            select(APIMCPToolMapping)
            .options(joinedload(APIMCPToolMapping.endpoint)) # Keep endpoint for other metadata if needed
            .where(
                APIMCPToolMapping.server_id == uuid.UUID(server_id),
                APIMCPToolMapping.enabled == True
            )
        )
        mappings = mappings_result.scalars().all()

        tools = []
        for mapping in mappings:
            endpoint = mapping.endpoint
            if not endpoint or not endpoint.enabled:
                logger.warning(f"Skipping mapping '{mapping.tool_name}' because its SAPIEndpoint is missing or disabled.")
                continue

            # 使用预先生成的 input_schema
            # 如果 mapping.input_schema 不存在或为空，则提供一个默认的空 schema
            input_schema_to_use = mapping.input_schema if mapping.input_schema else {"type": "object", "properties": {}, "required": []}

            tool_entry = {
                "id": str(mapping.id), # 使用映射的ID作为工具的唯一标识
                "name": mapping.tool_name,
                "description": mapping.tool_description or endpoint.description or endpoint.name,
                # "parameters": parameters, # DEPRECATED: We now use inputSchema directly from mapping
                "inputSchema": input_schema_to_use,
                "metadata": {
                    "endpoint": {
                        "id": str(endpoint.id),
                        "path": endpoint.endpoint_path, # 规范化后的路径
                        "method": endpoint.method.upper(),
                        # SAPIEndpoint.parameters 存储了原始的参数定义，可用于调试或特定场景
                        "sapi_parameters": endpoint.parameters
                    },
                    "mapping_id": str(mapping.id), # MCP Server可能会用到
                    "supports_streaming": mapping.supports_streaming, # 从映射中获取
                    "supports_media": mapping.supports_media # 从映射中获取
                }
            }
            tools.append(tool_entry)

        logger.info(f"为服务器 {server_id} 检索到 {len(tools)} 个工具")
        return tools

    except HTTPException: # 直接抛出已知的HTTP异常
        raise
    except Exception as e:
        logger.error(f"获取MCP服务器工具列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取MCP服务器工具列表失败: {str(e)}")

# 获取MCP客户端配置
@router.get("/servers/{server_id}/client-config", response_model=Dict[str, Any])
async def get_client_config(
    server_id: str,
    config_type: str = Query("python", description="配置类型，支持python、javascript、curl"),
    transport_types: str = Query("streamhttp", description="通信方式，支持stdio、sse、streamhttp，多个用逗号分隔"),
    use_token: bool = Query(True, description="是否使用令牌"),
    token: Optional[str] = Query(None, description="API令牌"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """获取MCP客户端配置"""
    try:
        # 查询服务器
        result = await db.execute(sql_select(MCPServer).where(MCPServer.id == uuid.UUID(server_id)))
        server = result.scalars().first()
        if not server:
            raise HTTPException(status_code=404, detail="MCP服务器不存在")

        # 获取服务器工具列表 - 直接使用/servers/{server_id}/tools接口获取工具列表
        tools = []  # 默认为空列表
        try:
            # 直接调用已有的工具列表接口
            tools_response = await get_server_tools(server_id, current_user, db)
            if tools_response.get("success", False):
                tools = tools_response.get("data", [])
                logger.info(f"成功获取到 {len(tools)} 个工具")
            else:
                logger.warning(f"获取工具列表失败: {tools_response.get('message', '未知错误')}")
        except Exception as e:
            logger.warning(f"获取服务器工具列表失败: {str(e)}")
            # 记录详细的异常信息
            import traceback
            logger.error(f"详细异常信息: {traceback.format_exc()}")

        # 获取服务器配置
        server_config = mcp_server_adapter.servers.get(server_id, {})

        # 确保工具列表是有效的列表
        if not isinstance(tools, list):
            logger.warning(f"工具列表不是有效的列表类型: {type(tools)}")
            tools = []

        # 构建客户端配置
        client_config = {
            "server": {
                "id": server_id,
                "name": server.name,
                "description": server.description,
                "transport_types": server.transport_types if hasattr(server, 'transport_types') else [server.transport_type],
                "deployment_mode": server.deployment_mode if hasattr(server, 'deployment_mode') else "local"
            },
            "tools": tools,  # 已确保是列表类型
            "connection": {
                "url": f"http://localhost:3000/mcp",
                "timeout": 30000,
                "retry_strategy": "exponential"
            },
            "auth": {
                "type": "bearer" if use_token else "none",
                "header": "Authorization"
            },
            "debug": {
                "log_level": "info",
                "debug_mode": False
            }
        }

        # 解析通信方式，支持多个通信方式
        transport_types_list = transport_types.split(",") if transport_types else ["streamhttp"]

        # 根据配置类型生成示例代码
        if config_type == "python":
            client_config["python"] = {
                "installation": "pip install mcp-client",
                "code": generate_python_client_code(server, tools, transport_types_list, use_token, token)
            }
        elif config_type == "javascript":
            client_config["javascript"] = {
                "installation": "npm install mcp-client",
                "code": generate_javascript_client_code(server, tools, transport_types_list, use_token, token)
            }
        elif config_type == "curl":
            client_config["curl"] = {
                "code": generate_curl_client_code(server, tools, transport_types_list, use_token, token)
            }

        return {
            "success": True,
            "message": "获取MCP客户端配置成功",
            "data": client_config
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取MCP客户端配置失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取MCP客户端配置失败: {str(e)}")

# MCP会话存储
mcp_sessions = {}

# 连接到MCP服务器 - 使用两种路径格式，确保兼容性
@router.post("/servers/{server_id}/connect", response_model=Dict[str, Any])
@router.post("/servers/connect/{server_id}", response_model=Dict[str, Any])
async def connect_to_mcp_server(
    server_id: str,
    request_data: Dict[str, Any] = Body(default={}),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """连接到MCP服务器并获取会话ID"""
    # 从请求体中获取API令牌
    api_token = ""
    if request_data and "api_token" in request_data:
        api_token = request_data.get("api_token", "")
    try:
        try:
            # 查询服务器
            server_uuid = uuid.UUID(server_id)
            result = await db.execute(sql_select(MCPServer).where(MCPServer.id == server_uuid))
            server = result.scalars().first()
            if not server:
                raise HTTPException(status_code=404, detail="MCP服务器不存在")
        except ValueError:
            # UUID 格式错误
            logger.error(f"无效的服务器ID格式: {server_id}")
            raise HTTPException(status_code=400, detail="无效的服务器ID格式")

        # 获取服务器配置
        server_config = {
            "id": str(server.id),
            "name": server.name,
            "env": _json.loads(server.env) if server.env else {},
            "install_dir": f"mcp_servers/{server_id}",  # 使用相对路径，与创建服务器时保持一致
            "deployment_mode": server.deployment_mode if hasattr(server, 'deployment_mode') else "local",
            "transport_types": server.transport_types if hasattr(server, 'transport_types') else ["streamhttp"],
            "project_id": str(server.project_id) if server.project_id else None
        }

        # 直接尝试连接，不依赖状态检查
        # 这样即使状态显示为停止，但实际上服务器在运行，也能成功连接
        import httpx
        import uuid as uuid_lib

        # 获取服务器信息
        process_info = mcp_server_adapter.processes.get(server_id, {})

        # 获取部署模式
        deployment_mode = server.deployment_mode if hasattr(server, 'deployment_mode') else "local"

        # 获取端口，优先使用进程信息中的端口，如果没有则尝试从Docker容器获取，最后从环境变量获取，默认为8000
        port = process_info.get("port")
        if not port:
            # 尝试从Docker容器获取端口映射
            try:
                container_name = f"mcp-server-{server_id}"
                docker_ps_cmd = ["docker", "ps", "--filter", f"name={container_name}", "--format", "{{.Ports}}"]
                docker_ps_process = subprocess.run(
                    docker_ps_cmd,
                    capture_output=True,
                    text=True
                )
                if docker_ps_process.returncode == 0 and docker_ps_process.stdout.strip():
                    # 解析端口映射，格式类似于 0.0.0.0:39043->39043/tcp
                    port_mapping = docker_ps_process.stdout.strip()
                    logger.info(f"Docker容器端口映射: {port_mapping}")
                    if "->" in port_mapping:
                        host_port = port_mapping.split("->")[0].split(":")[-1]
                        try:
                            port = int(host_port)
                            logger.info(f"从Docker容器获取到端口: {port}")
                        except ValueError:
                            logger.warning(f"无法解析Docker容器端口: {host_port}")
            except Exception as e:
                logger.warning(f"获取Docker容器端口映射失败: {str(e)}")

            # 如果仍然没有获取到端口，从环境变量获取
            if not port:
                env = server_config.get("env", {})
                port = env.get("PORT", 8000)

        # 设置请求头
        headers = {
            "Content-Type": "application/json"
        }

        # 如果提供了API令牌，使用它
        if api_token:
            headers["Authorization"] = f"Bearer {api_token}"
        # 否则，尝试使用服务器配置中的API令牌
        else:
            env = server_config.get("env", {})
            api_key = env.get("API_KEY", "")
            if api_key:
                headers["Authorization"] = f"Bearer {api_key}"

        # 尝试多种可能的主机名
        hosts_to_try = ["localhost", "127.0.0.1", "0.0.0.0", "**********"]

        # 如果是Docker模式，添加更多可能的主机名
        if deployment_mode == "docker":
            # 获取容器名称
            container_name = process_info.get("container_name")
            if container_name:
                # 添加容器名称作为可能的主机名
                hosts_to_try.insert(0, container_name)

                # 添加Docker网络中的主机名
                hosts_to_try.insert(0, "host.docker.internal")  # macOS/Windows Docker
                hosts_to_try.insert(0, "docker.for.mac.localhost")  # 旧版macOS Docker
                hosts_to_try.insert(0, "docker.for.win.localhost")  # 旧版Windows Docker

                # 尝试获取Docker网关IP
                try:
                    import socket
                    s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
                    s.connect(("*******", 80))
                    local_ip = s.getsockname()[0]
                    s.close()
                    # 添加本地IP
                    hosts_to_try.insert(0, local_ip)

                    # 尝试获取Docker网关IP (通常是本地IP的前三段加.1)
                    ip_parts = local_ip.split('.')
                    if len(ip_parts) == 4:
                        gateway_ip = f"{ip_parts[0]}.{ip_parts[1]}.{ip_parts[2]}.1"
                        hosts_to_try.insert(0, gateway_ip)
                except:
                    pass

        # 记录尝试的主机名和端口
        logger.info(f"尝试连接的主机名: {hosts_to_try}, 端口: {port}")

        # 尝试直接连接到服务器
        connection_success = False
        connection_error = None

        for host in hosts_to_try:
            try:
                async with httpx.AsyncClient() as client:
                    # 尝试健康检查
                    logger.info(f"尝试连接到 {host}:{port}/health")
                    health_response = await client.get(
                        f"http://{host}:{port}/health",
                        timeout=5  # 增加超时时间
                    )

                    if health_response.status_code == 200:
                        # 服务器实际上在运行，更新状态
                        logger.info(f"成功连接到 {host}:{port}/health")
                        mcp_server_adapter.running_servers[server_id] = {
                            "status": "running",
                            "started_at": datetime.now().isoformat(),
                            "port": port,
                            "host": host
                        }
                        connection_success = True
                        break
                    else:
                        logger.warning(f"连接到 {host}:{port}/health 返回状态码: {health_response.status_code}")
            except Exception as e:
                logger.warning(f"连接到 {host}:{port}/health 失败: {str(e)}")
                connection_error = str(e)
                continue

        # 如果直接连接失败，尝试使用标准状态检查
        if not connection_success:
            # 尝试标准状态检查
            status = await mcp_server_adapter.get_server_status(server_id)
            if status.get("status") == "running":
                connection_success = True
                logger.info(f"标准状态检查显示服务器正在运行")
            else:
                logger.warning(f"标准状态检查显示服务器未运行: {status.get('status')}")

        # 如果所有连接尝试都失败
        if not connection_success:
            return {
                "success": False,
                "message": "无法连接到MCP服务器",
                "data": {
                    "error": f"服务器未运行或无法访问 (最后错误: {connection_error})"
                }
            }

        # 获取服务器信息 - 使用之前已经确定的端口
        # 不要重新获取端口，因为之前已经确定了正确的端口

        # 设置请求头
        headers = {
            "Content-Type": "application/json"
        }

        # 如果提供了API令牌，使用它
        if api_token:
            headers["Authorization"] = f"Bearer {api_token}"
        # 否则，尝试使用服务器配置中的API令牌
        else:
            env = server_config.get("env", {})
            api_key = env.get("API_KEY", "")
            if api_key:
                headers["Authorization"] = f"Bearer {api_key}"

        # 添加用户ID和项目ID
        # 这里需要手动添加，因为这个请求不经过MCP代理
        headers["X-User-ID"] = str(current_user.id)
        if server.project_id:
            headers["X-Project-ID"] = str(server.project_id)

        # 尝试多种可能的主机名
        hosts_to_try = ["localhost", "127.0.0.1", "0.0.0.0", "**********"]

        # 按照MCP协议标准流程进行连接
        import httpx
        import uuid as uuid_lib

        for host in hosts_to_try:
            try:
                async with httpx.AsyncClient() as client:
                    # 初始化连接
                    init_request = {
                        "jsonrpc": "2.0",
                        "id": str(uuid_lib.uuid4()),
                        "method": "initialize",
                        "params": {
                            "protocolVersion": "2025-03-26",
                            "capabilities": {
                                "tools": {
                                    "listChanged": True
                                }
                            }
                        }
                    }

                    init_response = await client.post(
                        f"http://{host}:{port}/mcp",
                        json=init_request,
                        headers=headers,
                        timeout=5
                    )

                    if init_response.status_code != 200:
                        continue

                    init_data = init_response.json()
                    if "error" in init_data:
                        return {
                            "success": False,
                            "message": "连接MCP服务器失败",
                            "data": {
                                "error": init_data.get("error", {}).get("message", "未知错误")
                            }
                        }

                    # 尝试从不同的字段获取会话ID
                    session_id = init_data.get("result", {}).get("sessionId")
                    if not session_id:
                        # 尝试从session_id字段获取
                        session_id = init_data.get("result", {}).get("session_id")

                    if not session_id:
                        logger.error(f"未获取到会话ID，返回数据: {init_data}")
                        return {
                            "success": False,
                            "message": "连接MCP服务器失败",
                            "data": {
                                "error": "未获取到会话ID"
                            }
                        }

                    # 发送初始化完成通知 - 按照MCP协议标准，使用notifications/initialized
                    init_notification = {
                        "jsonrpc": "2.0",
                        "method": "notifications/initialized",
                        "params": {}
                    }

                    # 发送通知，不需要等待响应
                    try:
                        await client.post(
                            f"http://{host}:{port}/mcp",
                            json=init_notification,
                            headers=headers,
                            timeout=5
                        )
                        logger.info(f"已发送initialized通知到MCP服务器: {server_id}")
                    except Exception as e:
                        logger.warning(f"发送initialized通知失败: {str(e)}")
                        # 继续执行，不要因为通知失败而中断流程

                    # 保存会话信息
                    mcp_sessions[session_id] = {
                        "server_id": server_id,
                        "host": host,
                        "port": port,
                        "headers": headers,
                        "created_at": datetime.now().isoformat(),
                        "server_info": init_data.get("result", {}).get("serverInfo", {})
                    }

                    # 返回连接成功信息
                    response_data = {
                        "server_id": str(server.id),
                        "name": server.name,
                        "status": "running",  # Status should be running at this point
                        "port": None, # Default to None
                        "host": None  # Default to None
                    }

                    # Attempt to get port and host from server_data, which was used to populate update_data
                    # These keys would have been added to update_data if they existed in server_data
                    if server_data and server_data.get("port"):
                        response_data["port"] = server_data.get("port")
                    if server_data and server_data.get("host"):
                        response_data["host"] = server_data.get("host")

                    return {
                        "success": True,
                        "message": "连接MCP服务器成功",
                        "data": response_data
                    }
            except Exception as e:
                logger.warning(f"尝试连接到 {host}:{port} 失败: {str(e)}")
                continue

        # 如果所有连接尝试都失败
        return {
            "success": False,
            "message": "无法连接到MCP服务器",
            "data": {
                "error": "所有连接尝试均失败"
            }
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"连接MCP服务器失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"连接MCP服务器失败: {str(e)}")


# 生成Python客户端代码
def generate_python_client_code(server, tools, transport_types, use_token, token):
    """生成Python客户端代码"""
    try:
        # 使用第一个通信方式作为主要通信方式
        primary_transport_type = transport_types[0] if transport_types and len(transport_types) > 0 else "streamhttp"

        # 确保 transport_types 是字符串形式
        transport_types_str = str(transport_types)

        # 认证代码
        if use_token:
            auth_code = f'client.set_auth_token("{token or "YOUR_API_TOKEN"}")'
        else:
            auth_code = '# 无需认证'

        code = """from mcp_client import MCPClient

# 创建MCP客户端
client = MCPClient(
    server_url="http://localhost:3000/mcp",
    transport_types={transport_types},
    primary_transport_type="{primary_transport_type}",
    timeout=30000
)

# 设置认证信息
{auth_code}

# 获取可用工具列表
tools = client.list_tools()
print(f"可用工具: {{tools}}")

# 示例：调用工具
{tool_example}
"""

        # 工具示例
        if tools and len(tools) > 0:
            try:
                tool = tools[0]
                tool_name = tool.get("name", "example_tool")

                # 构建参数
                params = {}
                if "parameters" in tool and "properties" in tool["parameters"]:
                    for param_name, param_schema in tool["parameters"]["properties"].items():
                        # 根据类型生成示例值
                        if param_schema.get("type") == "string":
                            params[param_name] = f"example_{param_name}"
                        elif param_schema.get("type") == "number" or param_schema.get("type") == "integer":
                            params[param_name] = 123
                        elif param_schema.get("type") == "boolean":
                            params[param_name] = True
                        elif param_schema.get("type") == "array":
                            params[param_name] = ["item1", "item2"]
                        elif param_schema.get("type") == "object":
                            params[param_name] = {"key": "value"}

                tool_example = f"""result = client.execute_tool(
    tool_name="{tool_name}",
    parameters={params}
)
print("执行结果:", result)"""
            except Exception as e:
                logger.warning(f"生成工具示例失败: {str(e)}")
                tool_example = """# 示例：调用工具
result = client.execute_tool(
    tool_name="example_tool",
    parameters={"param1": "value1", "param2": "value2"}
)
print("执行结果:", result)"""
        else:
            tool_example = """# 示例：调用工具
result = client.execute_tool(
    tool_name="example_tool",
    parameters={"param1": "value1", "param2": "value2"}
)
print("执行结果:", result)"""

        return code.format(
            transport_types=transport_types_str,
            primary_transport_type=primary_transport_type,
            auth_code=auth_code,
            tool_example=tool_example
        )
    except Exception as e:
        logger.error(f"生成Python客户端代码失败: {str(e)}")
        # 返回一个简单的错误处理版本
        return """from mcp_client import MCPClient

# 创建MCP客户端
client = MCPClient(
    server_url="http://localhost:3000/mcp",
    transport_types=["streamhttp"],
    primary_transport_type="streamhttp",
    timeout=30000
)

# 设置认证信息
client.set_auth_token("YOUR_API_TOKEN")

# 获取可用工具列表
tools = client.list_tools()
print(f"可用工具: {tools}")

# 示例：调用工具
result = client.execute_tool(
    tool_name="example_tool",
    parameters={"param1": "value1", "param2": "value2"}
)
print("执行结果:", result)
"""

# 生成JavaScript客户端代码
def generate_javascript_client_code(server, tools, transport_types, use_token, token):
    """生成JavaScript客户端代码"""
    try:
        # 使用第一个通信方式作为主要通信方式
        primary_transport_type = transport_types[0] if transport_types and len(transport_types) > 0 else "streamhttp"

        # 确保 transport_types 是字符串形式
        transport_types_str = str(transport_types)

        code = """import { MCPClient } from 'mcp-client';

// 创建MCP客户端
const client = new MCPClient({
  serverUrl: 'http://localhost:3000/mcp',
  transportTypes: {transport_types},
  primaryTransportType: '{primary_transport_type}',
  timeout: 30000
});

// 设置认证信息
{auth_code}

// 获取可用工具列表
client.listTools()
  .then(tools => {
    console.log('可用工具:', tools);

    // 示例：调用工具
    {tool_example}
  })
  .catch(error => {
    console.error('获取工具列表失败:', error);
  });
"""

        # 认证代码
        if use_token:
            auth_code = f'client.setAuthToken("{token or "YOUR_API_TOKEN"}");'
        else:
            auth_code = '// 无需认证'

        # 工具示例
        if tools and len(tools) > 0:
            try:
                tool = tools[0]
                tool_name = tool.get("name", "example_tool")

                # 构建参数
                params = {}
                if "parameters" in tool and "properties" in tool["parameters"]:
                    for param_name, param_schema in tool["parameters"]["properties"].items():
                        # 根据类型生成示例值
                        if param_schema.get("type") == "string":
                            params[param_name] = f"example_{param_name}"
                        elif param_schema.get("type") == "number" or param_schema.get("type") == "integer":
                            params[param_name] = 123
                        elif param_schema.get("type") == "boolean":
                            params[param_name] = True
                        elif param_schema.get("type") == "array":
                            params[param_name] = ["item1", "item2"]
                        elif param_schema.get("type") == "object":
                            params[param_name] = {"key": "value"}

                tool_example = f"""return client.executeTool('{tool_name}', {params});\n  }})\n  .then(result => {{\n    console.log('执行结果:', result);\n  }})\n  .catch(error => {{\n    console.error('执行工具失败:', error);\n  }}}});"""
            except Exception as e:
                logger.warning(f"生成工具示例失败: {str(e)}")
                tool_example = """return client.executeTool('example_tool', {\n      param1: 'value1',\n      param2: 'value2'\n    });\n  }})\n  .then(result => {{\n    console.log('执行结果:', result);\n  }})\n  .catch(error => {{\n    console.error('执行工具失败:', error);\n  }}}});"""
        else:
            tool_example = """return client.executeTool('example_tool', {\n      param1: 'value1',\n      param2: 'value2'\n    });\n  }})\n  .then(result => {{\n    console.log('执行结果:', result);\n  }})\n  .catch(error => {{\n    console.error('执行工具失败:', error);\n  }}}});"""

        return code.format(
            transport_types=transport_types_str,
            primary_transport_type=primary_transport_type,
            auth_code=auth_code,
            tool_example=tool_example
        )
    except Exception as e:
        logger.error(f"生成JavaScript客户端代码失败: {str(e)}")
        # 返回一个简单的错误处理版本
        return """import { MCPClient } from 'mcp-client';

// 创建MCP客户端
const client = new MCPClient({
  serverUrl: 'http://localhost:3000/mcp',
  transportTypes: ["streamhttp"],
  primaryTransportType: "streamhttp",
  timeout: 30000
});

// 设置认证信息
client.setAuthToken("YOUR_API_TOKEN");

// 获取可用工具列表
client.listTools()
  .then(tools => {
    console.log('可用工具:', tools);

    // 示例：调用工具
    return client.executeTool('example_tool', {
      param1: 'value1',
      param2: 'value2'
    });
  })
  .then(result => {
    console.log('执行结果:', result);
  })
  .catch(error => {
    console.error('执行工具失败:', error);
  });
"""

# 生成cURL客户端代码
def generate_curl_client_code(server, tools, transport_types, use_token, token):
    """生成cURL客户端代码"""
    try:
        # 使用第一个通信方式作为主要通信方式
        primary_transport_type = transport_types[0] if transport_types and len(transport_types) > 0 else "streamhttp"

        # 认证头
        auth_header = f'-H "Authorization: Bearer {token or "YOUR_API_TOKEN"}"' if use_token else ''

        # 工具示例
        if tools and len(tools) > 0:
            try:
                tool = tools[0]
                tool_name = tool.get("name", "example_tool")

                # 构建参数
                params = {}
                if "parameters" in tool and "properties" in tool["parameters"]:
                    for param_name, param_schema in tool["parameters"]["properties"].items():
                        # 根据类型生成示例值
                        if param_schema.get("type") == "string":
                            params[param_name] = f"example_{param_name}"
                        elif param_schema.get("type") == "number" or param_schema.get("type") == "integer":
                            params[param_name] = 123
                        elif param_schema.get("type") == "boolean":
                            params[param_name] = True
                        elif param_schema.get("type") == "array":
                            params[param_name] = ["item1", "item2"]
                        elif param_schema.get("type") == "object":
                            params[param_name] = {"key": "value"}

                # 获取工具列表
                list_tools_command = f"""# 获取工具列表
curl -X POST "http://localhost:3000/mcp" \\
  -H "Content-Type: application/json" \\
  {auth_header} \\
  -d '{{"jsonrpc": "2.0", "id": "1", "method": "tools/list"}}'
"""

                # 执行工具
                execute_tool_command = f"""# 执行工具
curl -X POST "http://localhost:3000/mcp" \\
  -H "Content-Type: application/json" \\
  {auth_header} \\
  -d '{{"jsonrpc": "2.0", "id": "2", "method": "tools/call", "params": {{"name": "{tool_name}", "arguments": {params}}}}}'
"""

                return list_tools_command + "\n" + execute_tool_command
            except Exception as e:
                logger.warning(f"生成工具示例失败: {str(e)}")
        # 如果没有工具或出错，使用默认示例
        list_tools_command = f"""# 获取工具列表
curl -X POST "http://localhost:3000/mcp" \\
  -H "Content-Type: application/json" \\
  {auth_header} \\
  -d '{{"jsonrpc": "2.0", "id": "1", "method": "tools/list"}}'
"""

        # 执行工具
        execute_tool_command = f"""# 执行工具
curl -X POST "http://localhost:3000/mcp" \\
  -H "Content-Type: application/json" \\
  {auth_header} \\
  -d '{{"jsonrpc": "2.0", "id": "2", "method": "tools/call", "params": {{"name": "example_tool", "arguments": {{"param1": "value1", "param2": "value2"}}}}}}'
"""

        return list_tools_command + "\n" + execute_tool_command
    except Exception as e:
        logger.error(f"生成cURL客户端代码失败: {str(e)}")
        # 返回一个简单的错误处理版本
        return """# 获取工具列表
curl -X POST "http://localhost:3000/mcp" \\
  -H "Content-Type: application/json" \\
  -H "Authorization: Bearer YOUR_API_TOKEN" \\
  -d '{"jsonrpc": "2.0", "id": "1", "method": "tools/list"}'

# 执行工具
curl -X POST "http://localhost:3000/mcp" \\
  -H "Content-Type: application/json" \\
  -H "Authorization: Bearer YOUR_API_TOKEN" \\
  -d '{"jsonrpc": "2.0", "id": "2", "method": "tools/call", "params": {"name": "example_tool", "arguments": {"param1": "value1", "param2": "value2"}}}'
"""


# 一键添加MCP服务器到AI助手
@router.post("/servers/{server_id}/add-to-ai-assistant", response_model=Dict[str, Any])
async def add_mcp_server_to_ai_assistant(
    project_id: uuid.UUID,
    server_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """一键添加MCP服务器到AI助手系统"""
    try:
        if not AI_MCP_SERVER_AVAILABLE:
            raise HTTPException(
                status_code=503, 
                detail="AI助手MCP服务器模块不可用，无法添加到AI助手"
            )

        # 查询SAPI服务器
        result = await db.execute(select(MCPServer).where(MCPServer.id == uuid.UUID(server_id)))
        sapi_server = result.scalars().first()
        if not sapi_server:
            raise HTTPException(status_code=404, detail="SAPI MCP服务器不存在")

        # 检查服务器是否已经添加到AI助手
        existing_ai_server = await db.execute(
            select(AIMCPServer).where(
                and_(
                    AIMCPServer.sapi_server_id == uuid.UUID(server_id),
                    AIMCPServer.project_id == project_id
                )
            )
        )
        existing_ai_server = existing_ai_server.scalars().first()
        
        if existing_ai_server:
            return {
                "success": True,
                "message": "SAPI MCP服务器已经添加到AI助手",
                "data": {
                    "ai_server_id": str(existing_ai_server.id),
                    "already_exists": True
                }
            }

        # 获取服务器的主要传输协议（使用第一个作为主要协议）
        primary_transport_type = sapi_server.transport_types[0] if sapi_server.transport_types and len(sapi_server.transport_types) > 0 else "streamhttp"
        
        # 构建内部连接配置
        # 对于SAPI服务器，我们使用内部协议连接
        config = {
            "sapi_server_id": server_id,
            "internal_connection": True,
            "proxy_url": f"http://localhost:3000/mcp/proxy/{server_id}",  # 通过MCP代理连接
            "auth_type": "internal",  # 内部认证
            "headers": {
                "X-SAPI-Server-ID": server_id,
                "X-Internal-Request": "true"
            }
        }

        # 创建AI助手MCP服务器记录
        ai_server = AIMCPServer(
            name=f"SAPI-{sapi_server.name}",
            description=f"由SAPI插件提供的MCP服务器: {sapi_server.description or sapi_server.name}",
            project_id=project_id,
            transport_type=primary_transport_type,
            transport_types=sapi_server.transport_types or [primary_transport_type],
            deployment_mode="internal",  # 标记为内部服务器
            source="sapi",  # 来源为SAPI插件
            config=config,
            enabled=sapi_server.enabled,
            status="unknown",
            tools_count=0,
            available_tools=[],
            created_by_id=current_user.id,
            sapi_server_id=uuid.UUID(server_id)
        )
        
        db.add(ai_server)
        await db.commit()
        await db.refresh(ai_server)

        # 尝试获取服务器的工具列表
        tools_count = 0
        try:
            # 调用 get_server_tools 获取服务器的具体工具
            server_tools = await get_server_tools(server_id, current_user, db)
            
            if server_tools and len(server_tools) > 0:
                # 更新工具信息
                ai_server.available_tools = server_tools
                ai_server.tools_count = len(server_tools)
                tools_count = len(server_tools)
                await db.commit()
                logger.info(f"为AI助手MCP服务器 {ai_server.id} 获取到 {tools_count} 个工具")
                        
        except Exception as tools_error:
            logger.warning(f"获取SAPI服务器工具列表失败，但AI助手MCP服务器已创建: {str(tools_error)}")

        return {
            "success": True,
            "message": f"SAPI MCP服务器已成功添加到AI助手，包含 {tools_count} 个工具",
            "data": {
                "ai_server_id": str(ai_server.id),
                "name": ai_server.name,
                "description": ai_server.description,
                "transport_type": ai_server.transport_type,
                "transport_types": ai_server.transport_types,
                "deployment_mode": ai_server.deployment_mode,
                "source": ai_server.source,
                "enabled": ai_server.enabled,
                "tools_count": ai_server.tools_count,
                "created_at": ai_server.created_at.isoformat(),
                "sapi_server_id": str(ai_server.sapi_server_id),
                "already_exists": False
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"添加MCP服务器到AI助手失败: {str(e)}")
        import traceback
        traceback.print_exc()
        raise HTTPException(
            status_code=500, 
            detail=f"添加MCP服务器到AI助手失败: {str(e)}"
        )




