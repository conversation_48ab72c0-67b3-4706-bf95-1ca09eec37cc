#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
MCP服务器增强功能测试脚本
测试multipart/form-data、图片处理和流式服务功能
"""

import asyncio
import httpx
import json
import base64
import io
import time
from typing import Dict, Any, List
from PIL import Image

# 测试配置
MCP_SERVER_URL = "http://localhost:5000"
TEST_SESSION_ID = "test-session-enhanced-features"

class MCPEnhancedTester:
    def __init__(self, server_url: str, session_id: str):
        self.server_url = server_url
        self.session_id = session_id
        self.session = None
    
    async def __aenter__(self):
        self.session = httpx.AsyncClient(timeout=30.0)
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.aclose()
    
    def create_test_image(self) -> str:
        """创建一个测试图片的base64编码"""
        # 创建一个简单的测试图片
        img = Image.new('RGB', (100, 100), color='red')
        buffer = io.BytesIO()
        img.save(buffer, format='JPEG')
        buffer.seek(0)
        
        # 转换为base64
        img_data = base64.b64encode(buffer.getvalue()).decode('utf-8')
        return f"data:image/jpeg;base64,{img_data}"
    
    async def test_mcp_initialization(self) -> bool:
        """测试MCP初始化"""
        print("🔄 测试MCP初始化...")
        
        try:
            payload = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "initialize",
                "params": {
                    "protocolVersion": "2024-11-05",
                    "capabilities": {
                        "tools": {}
                    },
                    "clientInfo": {
                        "name": "enhanced-features-tester",
                        "version": "1.0.0"
                    }
                }
            }
            
            response = await self.session.post(
                f"{self.server_url}/mcp",
                headers={
                    "Content-Type": "application/json",
                    "mcp-session-id": self.session_id
                },
                json=payload
            )
            if response.status_code == 200:
                result = response.json()
                print(f"✅ MCP初始化成功: {result.get('result', {}).get('serverInfo', {}).get('name', 'Unknown')}")
                return True
            else:
                print(f"❌ MCP初始化失败: HTTP {response.status_code}")
                return False
        
        except Exception as e:
            print(f"❌ MCP初始化异常: {e}")
            return False
    
    async def test_tools_list(self) -> bool:
        """测试工具列表获取"""
        print("🔄 测试工具列表获取...")
        
        try:
            payload = {
                "jsonrpc": "2.0",
                "id": 2,
                "method": "tools/list"
            }
            
            response = await self.session.post(
                f"{self.server_url}/mcp",
                headers={
                    "Content-Type": "application/json",
                    "mcp-session-id": self.session_id
                },
                json=payload
            )
            if response.status_code == 200:
                result = response.json()
                tools = result.get('result', {}).get('tools', [])
                print(f"✅ 获取到 {len(tools)} 个工具:")
                for tool in tools:
                    print(f"   - {tool.get('name')}: {tool.get('description', 'No description')}")
                return len(tools) > 0
            else:
                print(f"❌ 工具列表获取失败: HTTP {response.status_code}")
                return False
        
        except Exception as e:
            print(f"❌ 工具列表获取异常: {e}")
            return False
    
    async def test_image_analysis(self) -> bool:
        """测试图片分析工具"""
        print("🔄 测试图片分析工具...")
        
        try:
            test_image = self.create_test_image()
            
            payload = {
                "jsonrpc": "2.0",
                "id": 3,
                "method": "tools/call",
                "params": {
                    "name": "image_analysis",
                    "arguments": {
                        "image": test_image,
                        "analysis_type": "objects"
                    }
                }
            }
            
            response = await self.session.post(
                f"{self.server_url}/mcp",
                headers={
                    "Content-Type": "application/json",
                    "mcp-session-id": self.session_id
                },
                json=payload
            )
            if response.status_code == 200:
                result = response.json()
                content = result.get('result', {}).get('content', [])
                if content and not result.get('result', {}).get('isError', False):
                    print("✅ 图片分析成功:")
                    for item in content:
                        if item.get('type') == 'text':
                            print(f"   结果: {item.get('text', '')[:100]}...")
                    return True
                else:
                    print(f"❌ 图片分析失败: {result}")
                    return False
            else:
                print(f"❌ 图片分析请求失败: HTTP {response.status_code}")
                return False
        
        except Exception as e:
            print(f"❌ 图片分析异常: {e}")
            return False
    
    async def test_file_upload(self) -> bool:
        """测试文件上传工具（multipart/form-data）"""
        print("🔄 测试文件上传工具...")
        
        try:
            # 创建测试文件数据
            test_files = [
                self.create_test_image(),
                "data:text/plain;base64," + base64.b64encode("测试文本文件内容".encode('utf-8')).decode('utf-8')
            ]
            
            payload = {
                "jsonrpc": "2.0",
                "id": 4,
                "method": "tools/call",
                "params": {
                    "name": "file_upload",
                    "arguments": {
                        "files": test_files,
                        "description": "测试文件上传",
                        "tags": ["test", "multipart"]
                    }
                }
            }
            
            response = await self.session.post(
                f"{self.server_url}/mcp",
                headers={
                    "Content-Type": "application/json",
                    "mcp-session-id": self.session_id
                },
                json=payload
            )
            if response.status_code == 200:
                result = response.json()
                content = result.get('result', {}).get('content', [])
                if content and not result.get('result', {}).get('isError', False):
                    print("✅ 文件上传成功:")
                    for item in content:
                        if item.get('type') == 'text':
                            print(f"   结果: {item.get('text', '')[:100]}...")
                    return True
                else:
                    print(f"❌ 文件上传失败: {result}")
                    return False
            else:
                print(f"❌ 文件上传请求失败: HTTP {response.status_code}")
                return False
        
        except Exception as e:
            print(f"❌ 文件上传异常: {e}")
            return False
    
    async def test_multimedia_converter(self) -> bool:
        """测试多媒体转换工具"""
        print("🔄 测试多媒体转换工具...")
        
        try:
            test_image = self.create_test_image()
            
            payload = {
                "jsonrpc": "2.0",
                "id": 5,
                "method": "tools/call",
                "params": {
                    "name": "multimedia_converter",
                    "arguments": {
                        "input_file": test_image,
                        "output_format": "png",
                        "quality": 90
                    }
                }
            }
            
            async with self.session.post(
                f"{self.server_url}/mcp",
                headers={
                    "Content-Type": "application/json",
                    "mcp-session-id": self.session_id
                },
                json=payload
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    content = result.get('result', {}).get('content', [])
                    if content and not result.get('result', {}).get('isError', False):
                        print("✅ 多媒体转换成功:")
                        for item in content:
                            if item.get('type') == 'text':
                                conversion_result = json.loads(item.get('text', '{}'))
                                print(f"   输出格式: {conversion_result.get('output_format', 'Unknown')}")
                                print(f"   压缩比: {conversion_result.get('compression_ratio', 'Unknown')}")
                            elif item.get('type') == 'image':
                                print(f"   转换后图片: {item.get('mimeType', 'Unknown')} ({len(item.get('data', ''))} 字符)")
                        return True
                    else:
                        print(f"❌ 多媒体转换失败: {result}")
                        return False
                else:
                    print(f"❌ 多媒体转换请求失败: HTTP {response.status}")
                    return False
        
        except Exception as e:
            print(f"❌ 多媒体转换异常: {e}")
            return False
    
    async def test_streaming_processor(self) -> bool:
        """测试流式数据处理工具"""
        print("🔄 测试流式数据处理工具...")
        
        try:
            # 启动流式处理
            payload = {
                "jsonrpc": "2.0",
                "id": 6,
                "method": "tools/call",
                "params": {
                    "name": "streaming_processor",
                    "arguments": {
                        "data_source": "test",
                        "chunk_size": 50,
                        "total_chunks": 5
                    }
                }
            }
            
            # 发送流式处理请求
            response = await self.session.post(
                f"{self.server_url}/mcp",
                headers={
                    "Content-Type": "application/json",
                    "mcp-session-id": self.session_id
                },
                json=payload
            )
            if response.status_code == 200:
                result = response.json()
                print(f"✅ 流式处理启动成功: {result.get('result', {})}")
                
                # 连接SSE端点接收流式数据
                await self.test_sse_connection()
                return True
            else:
                print(f"❌ 流式处理启动失败: HTTP {response.status_code}")
                return False
        
        except Exception as e:
            print(f"❌ 流式处理异常: {e}")
            return False
    
    async def test_sse_connection(self) -> bool:
        """测试SSE连接接收流式数据"""
        print("🔄 测试SSE连接...")
        
        try:
            timeout = httpx.Timeout(total=30)
            async with self.session.stream(
                "GET",
                f"{self.server_url}/mcp",
                headers={
                    "Accept": "text/event-stream",
                    "mcp-session-id": self.session_id
                },
                timeout=timeout
            ) as response:
                if response.status_code == 200:
                    print("✅ SSE连接建立成功，接收流式数据:")
                    
                    chunk_count = 0
                    async for line in response.aiter_lines():
                        line = line.strip()
                        if line.startswith('data: '):
                            data = line[6:]  # 移除"data: "前缀
                            if data == '[DONE]':
                                break
                            
                            try:
                                event_data = json.loads(data)
                                method = event_data.get('method', '')
                                if 'toolStream' in method:
                                    chunk_count += 1
                                    print(f"   📦 接收到流式事件 {chunk_count}: {method}")
                                    
                                    # 限制接收数量，避免测试时间过长
                                    if chunk_count >= 10:
                                        break
                            except json.JSONDecodeError:
                                print(f"   📄 接收到文本数据: {data[:50]}...")
                    
                    print(f"✅ SSE测试完成，共接收 {chunk_count} 个事件")
                    return chunk_count > 0
                else:
                    print(f"❌ SSE连接失败: HTTP {response.status_code}")
                    return False
        
        except asyncio.TimeoutError:
            print("⚠️ SSE连接超时（这可能是正常的）")
            return True
        except Exception as e:
            print(f"❌ SSE连接异常: {e}")
            return False
    
    async def run_all_tests(self) -> Dict[str, bool]:
        """运行所有测试"""
        print("🚀 开始MCP服务器增强功能测试\n")
        
        results = {}
        
        # 基础功能测试
        results['initialization'] = await self.test_mcp_initialization()
        await asyncio.sleep(1)
        
        results['tools_list'] = await self.test_tools_list()
        await asyncio.sleep(1)
        
        # 图片处理测试
        results['image_analysis'] = await self.test_image_analysis()
        await asyncio.sleep(1)
        
        # 文件上传测试
        results['file_upload'] = await self.test_file_upload()
        await asyncio.sleep(1)
        
        # 多媒体转换测试
        results['multimedia_converter'] = await self.test_multimedia_converter()
        await asyncio.sleep(1)
        
        # 流式处理测试
        results['streaming_processor'] = await self.test_streaming_processor()
        
        return results

def print_test_summary(results: Dict[str, bool]):
    """打印测试结果摘要"""
    print("\n" + "="*60)
    print("📊 测试结果摘要")
    print("="*60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name.replace('_', ' ').title():<25} {status}")
        if result:
            passed += 1
    
    print("-"*60)
    print(f"总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试都通过了！MCP服务器增强功能工作正常。")
    else:
        print("⚠️ 部分测试失败，请检查服务器配置和日志。")
    
    print("="*60)

async def main():
    """主函数"""
    print("MCP服务器增强功能测试工具")
    print(f"服务器地址: {MCP_SERVER_URL}")
    print(f"测试会话ID: {TEST_SESSION_ID}")
    print()
    
    async with MCPEnhancedTester(MCP_SERVER_URL, TEST_SESSION_ID) as tester:
        results = await tester.run_all_tests()
        print_test_summary(results)

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⚠️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc() 