from fastapi import APIRouter, Depends, HTTPException, status, Form, Query, Path
from fastapi.responses import FileResponse, JSONResponse
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Optional
import uuid

from db.database import get_db
from services.storage_service import StorageService


router = APIRouter()

@router.get("/{share_token}")
async def get_shared_file(
    share_token: str,
    password: Optional[str] = None,
    db: AsyncSession = Depends(get_db)
):
    """
    获取分享文件信息
    """
    result = await StorageService.get_shared_file(
        db=db,
        share_token=share_token,
        password=password
    )
    return result

@router.post("/{share_token}/verify")
async def verify_share_password(
    share_token: str,
    password: str = Form(...),
    db: AsyncSession = Depends(get_db)
):
    """
    验证分享密码
    """
    result = await StorageService.get_shared_file(
        db=db,
        share_token=share_token,
        password=password
    )
    return result

@router.get("/{share_token}/download")
async def download_shared_file(
    share_token: str,
    password: Optional[str] = Query(None),
    db: AsyncSession = Depends(get_db)
):
    """
    下载分享文件
    """
    # 先验证分享
    share_result = await StorageService.get_shared_file(
        db=db,
        share_token=share_token,
        password=password
    )
    
    if not share_result["success"] or share_result["data"]["requires_password"]:
        return JSONResponse(
            status_code=status.HTTP_403_FORBIDDEN,
            content=share_result
        )
    
    # 获取文件信息
    file_id = uuid.UUID(share_result["data"]["file_id"])
    file_info = share_result["data"]["file_info"]
    
    # 查询文件
    file_query = await StorageService.get_file(db, file_id, None)
    if not file_query:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="文件不存在"
        )
    
    # 返回文件
    return FileResponse(
        path=file_query.path,
        filename=file_query.original_name,
        media_type=file_query.mime_type
    )

@router.get("/{share_token}/thumbnail")
async def get_shared_file_thumbnail(
    share_token: str,
    password: Optional[str] = Query(None),
    db: AsyncSession = Depends(get_db)
):
    """
    获取分享文件缩略图
    """
    # 先验证分享
    share_result = await StorageService.get_shared_file(
        db=db,
        share_token=share_token,
        password=password
    )
    
    if not share_result["success"] or share_result["data"]["requires_password"]:
        return JSONResponse(
            status_code=status.HTTP_403_FORBIDDEN,
            content=share_result
        )
    
    # 获取文件信息
    file_id = uuid.UUID(share_result["data"]["file_id"])
    
    # 获取缩略图
    thumbnail_info = await StorageService.get_thumbnail(db, file_id, None)
    if not thumbnail_info:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="缩略图不存在"
        )
    
    # 返回缩略图
    return FileResponse(
        path=thumbnail_info["file_path"],
        filename=thumbnail_info["file_name"],
        media_type=thumbnail_info["mime_type"]
    )
