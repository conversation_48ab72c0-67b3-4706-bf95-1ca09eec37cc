#!/usr/bin/env python3
"""
Enhanced 2025 Purchase Order Task Executor
基于2025年最新视觉AI技术的采购订单任务执行器
集成多模态AI、智能表格解析和高精度OCR
"""

import asyncio
import json
import logging
import time
from typing import Dict, Any, List, Optional
from uuid import UUID
from sqlalchemy.ext.asyncio import AsyncSession

from db.database import AsyncSessionLocal
from models.storage import StorageFile
from models.task import AsyncTask
from services.enhanced_table_processing_service import Enhanced2025TableProcessingService
from schemas.table_processing import TableProcessingRequest

logger = logging.getLogger(__name__)

class Enhanced2025PurchaseOrderTaskExecutor:
    """2025年增强采购订单任务执行器 - 基于最新视觉AI技术"""
    
    def __init__(self):
        self.processing_service = Enhanced2025TableProcessingService()
        self.processing_stats = {
            'total_processed': 0,
            'success_count': 0,
            'error_count': 0,
            'avg_processing_time': 0.0
        }
    
    async def execute_task(self, task: AsyncTask, test_mode: bool = False) -> Dict[str, Any]:
        """执行2025年增强采购订单任务"""
        
        start_time = time.time()
        
        try:
            logger.info(f"开始执行2025年增强采购订单任务: {task.id}")
            
            # 获取文件信息
            async with AsyncSessionLocal() as session:
                file_upload = await session.get(StorageFile, UUID(task.input_data.get("file_id")))
                if not file_upload:
                    raise ValueError(f"文件未找到: {task.input_data.get('file_id')}")
            
            # 创建处理请求 - 使用2025年增强AI模式
            request = TableProcessingRequest(
                file_id=UUID(task.input_data.get("file_id")),
                user_id=task.user_id,
                project_id=task.project_id,
                processing_mode="enhanced_2025_ai"  # 指定使用2025年增强AI
            )
            
            # 使用2025年增强AI处理表格
            result = await self.processing_service.process_table_with_enhanced_ai(request, file_upload)
            
            processing_time = time.time() - start_time
            
            # 更新处理统计
            self._update_processing_stats(processing_time, result.get('success', False))
            
            # 即使数据验证未完全通过，只要有提取到数据就继续处理
            if result.get('success') or (result.get('data') and self._has_valid_data(result['data'])):
                # 获取项目门店列表用于匹配
                async with AsyncSessionLocal() as session:
                    stores = await self._get_project_stores(session, task.project_id)
                
                # 转换为前端格式
                frontend_data = self._convert_to_frontend_format_2025(result['data'], stores)
                
                # 添加2025年特有的元数据
                metadata = result.get('metadata', {})
                metadata.update({
                    'executor_version': '2025.1',
                    'processing_time': processing_time,
                    'ai_technology': '2025_enhanced_vision',
                    'processing_stats': self.processing_stats
                })
                
                return {
                    'success': True,
                    'data': frontend_data,
                    'metadata': metadata,
                    'message': '2025年增强AI采购订单处理完成'
                }
            else:
                error_msg = result.get('error', '2025年增强AI处理失败')
                return {
                    'success': False,
                    'error': error_msg,
                    'data': self._get_empty_frontend_data_2025(),
                    'metadata': {
                        'executor_version': '2025.1',
                        'processing_time': processing_time,
                        'errors': [error_msg]
                    }
                }
                
        except Exception as e:
            processing_time = time.time() - start_time
            self._update_processing_stats(processing_time, False)
            
            logger.error(f"2025年增强版采购订单任务执行失败: {str(e)}")
            import traceback
            traceback.print_exc()
            
            return {
                'success': False,
                'error': str(e),
                'data': self._get_empty_frontend_data_2025(),
                'metadata': {
                    'executor_version': '2025.1',
                    'processing_time': processing_time,
                    'error_details': str(e)
                }
            }
    
    def _has_valid_data(self, data: Dict[str, Any]) -> bool:
        """检查是否有有效数据"""
        if not isinstance(data, dict):
            return False
        
        products = data.get('products', [])
        distribution = data.get('distribution', {})
        
        return len(products) > 0 or len(distribution) > 0
    
    def _update_processing_stats(self, processing_time: float, success: bool):
        """更新处理统计"""
        self.processing_stats['total_processed'] += 1
        if success:
            self.processing_stats['success_count'] += 1
        else:
            self.processing_stats['error_count'] += 1
        
        # 更新平均处理时间
        total_time = (self.processing_stats['avg_processing_time'] * 
                     (self.processing_stats['total_processed'] - 1) + processing_time)
        self.processing_stats['avg_processing_time'] = total_time / self.processing_stats['total_processed']
    
    async def _get_project_stores(self, db, project_id: UUID) -> List[Dict[str, Any]]:
        """获取项目门店列表"""
        try:
            from models.store import Store
            from sqlalchemy import select
            
            stmt = select(Store).where(Store.project_id == project_id)
            result = await db.execute(stmt)
            stores = result.scalars().all()
            
            return [
                {
                    'id': str(store.id),
                    'name': store.name,
                    'code': store.code
                }
                for store in stores
            ]
        except Exception as e:
            logger.error(f"获取门店列表失败: {e}")
            return []
    
    def _match_store_by_name_2025(self, store_name: str, stores: List[Dict[str, Any]]) -> Optional[str]:
        """2025年增强门店名称匹配"""
        if not store_name or not stores:
            return None
        
        store_name = store_name.strip()
        
        # 精确匹配
        for store in stores:
            if store['name'] == store_name:
                return store['id']
        
        # 智能模糊匹配（考虑常见变体）
        for store in stores:
            store_variants = [
                store['name'],
                store['name'].replace('店', ''),
                store['name'].replace('分店', ''),
                store.get('code', '')
            ]
            
            for variant in store_variants:
                if variant and (store_name in variant or variant in store_name):
                    return store['id']
        
        return None
    
    def _convert_to_frontend_format_2025(self, data: Dict[str, Any], stores: List[Dict[str, Any]] = None) -> Dict[str, Any]:
        """转换为2025年前端格式 - 修复数据丢失问题"""
        try:
            products = data.get('products', [])
            distribution = data.get('distribution', {})

            logger.info(f"2025年格式转换开始 - 原始商品数: {len(products)}, 原始门店数: {len(distribution)}")

            # 调试：打印原始数据结构
            if products:
                logger.info(f"第一个商品示例: {products[0]}")
            if distribution:
                first_store = list(distribution.keys())[0]
                logger.info(f"第一个门店示例: {first_store} -> {len(distribution[first_store])} 商品")

            # ===== 2025年增强采购单处理 =====
            purchase_items = []

            for product in products:
                if not product or not isinstance(product, dict) or not product.get('name'):
                    logger.warning(f"跳过无效商品: {product}")
                    continue

                # 计算该商品在所有门店的总数量
                total_quantity = 0
                stores_with_product = []

                product_name = product.get('name', '').strip()
                product_spec = product.get('specification', '1').strip()

                for store_name, store_products in distribution.items():
                    if not isinstance(store_products, list):
                        continue

                    for item in store_products:
                        if not isinstance(item, dict):
                            continue

                        item_name = item.get('product_name', '').strip()
                        item_spec = item.get('specification', '1').strip()

                        # 更宽松的匹配条件
                        if (item_name == product_name or
                            (item_name and product_name and item_name in product_name) or
                            (item_name and product_name and product_name in item_name)):

                            quantity = self._safe_float(item.get('quantity', 0))
                            if quantity > 0:
                                total_quantity += quantity
                                if store_name not in stores_with_product:
                                    stores_with_product.append(store_name)

                # 即使没有分拨数据，也要包含商品信息
                spec_number = self._safe_float(product.get('specification', '1'))
                if spec_number <= 0:
                    spec_number = 1

                unit_price = self._safe_float(product.get('unit_price', 0))
                total_amount = spec_number * total_quantity * unit_price

                purchase_item = {
                    'product_name': product_name,
                    'product_specification': product.get('specification', '1'),
                    'product_unit': product.get('unit', ''),
                    'product_code': '',
                    'quantity': total_quantity,
                    'unit_price': unit_price,
                    'total_amount': total_amount,
                    'notes': f"分布门店: {', '.join(sorted(stores_with_product))}" if stores_with_product else "无分拨数据",
                    'suggested_category': product.get('suggested_category', ''),
                    'suggested_brand': product.get('suggested_brand', ''),
                    'ai_confidence': 0.95
                }
                purchase_items.append(purchase_item)

            logger.info(f"采购单处理完成: {len(purchase_items)} 个商品")
            
            # ===== 2025年增强分拨单处理 =====
            distribution_destinations = []

            for store_name, store_products in distribution.items():
                if not store_name or not isinstance(store_products, list):
                    logger.warning(f"跳过无效门店: {store_name}")
                    continue

                store_items = []
                store_total = 0

                logger.info(f"处理门店 {store_name}: {len(store_products)} 个商品")

                for item in store_products:
                    if not isinstance(item, dict) or not item.get('product_name'):
                        logger.warning(f"跳过无效商品项: {item}")
                        continue

                    # 2025年增强计算
                    specification = item.get('specification', '1')
                    quantity = self._safe_float(item.get('quantity', 0))
                    unit_price = self._safe_float(item.get('unit_price', 0))
                    spec_number = self._safe_float(specification)

                    if spec_number <= 0:
                        spec_number = 1

                    if quantity > 0:  # 只处理有数量的商品
                        subtotal = spec_number * quantity * unit_price
                        store_total += subtotal

                        store_item = {
                            'product_name': item.get('product_name', ''),
                            'specification': specification,
                            'unit': item.get('unit', ''),
                            'quantity': quantity,
                            'unit_price': unit_price,
                            'total_amount': subtotal,
                            'suggested_category': item.get('suggested_category', ''),
                            'suggested_brand': item.get('suggested_brand', ''),
                            'ai_confidence': 0.95
                        }
                        store_items.append(store_item)

                if store_items:
                    # 2025年增强门店匹配
                    matched_store_id = self._match_store_by_name_2025(store_name, stores) if stores else None

                    distribution_destinations.append({
                        'type': 'store',
                        'target_name': store_name,
                        'target_id': matched_store_id,
                        'items': store_items,
                        'total_amount': store_total,
                        'ai_confidence': 0.95
                    })

                    logger.info(f"门店 {store_name} 处理完成: {len(store_items)} 个有效商品")
                else:
                    logger.warning(f"门店 {store_name} 没有有效商品")

            # 计算2025年增强统计信息
            total_stores = len(distribution_destinations)
            total_products = len(purchase_items)
            total_amount = sum(item.get('total_amount', 0) for item in purchase_items)

            logger.info(f"2025年转换完成 - 采购商品数: {total_products}, 分拨门店数: {total_stores}, 总金额: {total_amount}")

            # 计算2025年增强识别质量
            recognition_quality = self._calculate_recognition_quality_2025(
                purchase_items, distribution_destinations, data
            )
            
            return {
                'preview': {
                    'purchase_items': purchase_items,
                    'distribution_destinations': distribution_destinations
                },
                'statistics': {
                    'total_stores': total_stores,
                    'total_products': total_products,
                    'total_amount': total_amount,
                    'ai_version': '2025.1'
                },
                'recognition_quality': recognition_quality
            }
            
        except Exception as e:
            logger.error(f"2025年格式转换失败: {e}")
            return self._get_empty_frontend_data_2025()
    
    def _calculate_recognition_quality_2025(
        self, 
        products: List[Dict], 
        distribution: List[Dict], 
        raw_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """计算2025年增强识别质量"""
        
        try:
            # 2025年增强商品识别率评估
            products_with_complete_info = sum(
                1 for p in products 
                if (p.get('product_specification') and p['product_specification'] != '1' and 
                    p.get('suggested_category') and p.get('unit_price', 0) > 0)
            )
            product_recognition_rate = min(100, (products_with_complete_info / len(products) * 100) if products else 0)
            
            # 2025年增强门店识别率评估
            real_store_names = sum(
                1 for dest in distribution
                if (dest.get('target_name') and not dest['target_name'].startswith('门店') and 
                    len(dest['target_name']) > 1 and dest.get('ai_confidence', 0) > 0.8)
            )
            store_recognition_rate = min(100, (real_store_names / len(distribution) * 100) if distribution else 0)
            
            # 2025年数据完整性评估
            metadata = raw_data.get('metadata', {})
            quality_assessment = metadata.get('quality_assessment', {})
            completeness_score = quality_assessment.get('completeness_score', 0.5)
            
            # 2025年综合质量等级
            avg_score = (product_recognition_rate/100 + store_recognition_rate/100 + completeness_score) / 3
            
            if avg_score >= 0.9:
                data_completeness = 'excellent'
            elif avg_score >= 0.8:
                data_completeness = 'high'
            elif avg_score >= 0.6:
                data_completeness = 'medium'
            else:
                data_completeness = 'low'
            
            return {
                'product_recognition_rate': round(product_recognition_rate, 1),
                'store_recognition_rate': round(store_recognition_rate, 1),
                'data_completeness': data_completeness,
                'completeness_score': round(completeness_score, 3),
                'ai_version': '2025.1',
                'quality_grade': quality_assessment.get('quality_grade', 'fair')
            }
            
        except Exception as e:
            logger.error(f"2025年识别质量计算失败: {e}")
            return {
                'product_recognition_rate': 0,
                'store_recognition_rate': 0,
                'data_completeness': 'poor',
                'completeness_score': 0.0,
                'ai_version': '2025.1',
                'quality_grade': 'poor'
            }
    
    def _get_empty_frontend_data_2025(self) -> Dict[str, Any]:
        """获取2025年空前端数据结构"""
        return {
            'preview': {
                'purchase_items': [],
                'distribution_destinations': []
            },
            'statistics': {
                'total_stores': 0,
                'total_products': 0,
                'total_amount': 0,
                'ai_version': '2025.1'
            },
            'recognition_quality': {
                'product_recognition_rate': 0,
                'store_recognition_rate': 0,
                'data_completeness': 'poor',
                'completeness_score': 0.0,
                'ai_version': '2025.1',
                'quality_grade': 'poor'
            }
        }
    
    def _safe_float(self, value: Any) -> float:
        """安全转换为浮点数"""
        try:
            if isinstance(value, (int, float)):
                return float(value)
            elif isinstance(value, str):
                # 移除非数字字符
                import re
                cleaned = re.sub(r'[^\d.-]', '', value)
                return float(cleaned) if cleaned else 0.0
            else:
                return 0.0
        except (ValueError, TypeError):
            return 0.0


# 创建全局实例
enhanced_2025_purchase_order_executor = Enhanced2025PurchaseOrderTaskExecutor()
