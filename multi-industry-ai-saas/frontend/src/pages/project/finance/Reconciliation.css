.reconciliation-container {
  padding: 16px;
}

.reconciliation-container .ant-card {
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.reconciliation-container .gradient-card {
  background: linear-gradient(to right, #f6f9fc, #f0f5fa);
  border: none;
}

.reconciliation-container .ant-statistic-title {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65);
}

.reconciliation-container .ant-statistic-content {
  font-size: 20px;
  font-weight: 500;
}

.reconciliation-container .ant-table-small {
  border: 1px solid #f0f0f0;
  border-radius: 4px;
}

.reconciliation-container .ant-alert {
  margin-bottom: 16px;
}

.reconciliation-container .ant-upload-drag {
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  cursor: pointer;
  transition: border-color 0.3s;
}

.reconciliation-container .ant-upload-drag:hover {
  border-color: #1890ff;
}

.reconciliation-container .ant-upload-drag-icon {
  margin-bottom: 8px;
  color: #1890ff;
  font-size: 48px;
}

.reconciliation-container .ant-upload-text {
  margin: 0 0 4px;
  color: rgba(0, 0, 0, 0.85);
  font-size: 16px;
}

.reconciliation-container .ant-upload-hint {
  color: rgba(0, 0, 0, 0.45);
  font-size: 14px;
}
