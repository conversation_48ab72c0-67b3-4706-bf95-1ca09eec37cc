#!/usr/bin/env python
# -*- coding: utf-8 -*-

from typing import List, Optional, Dict, Any, Union
from fastapi import APIRouter, Depends, HTTPException, Query, Path, Body, status, UploadFile, File, Form
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import func, and_, or_, desc, asc
import uuid
from datetime import datetime, timezone
import logging
import pandas as pd
import io
import json

from db.database import get_db
from models.user import User
from models.project import Project
from models.store import Store
from models.warehouse import Warehouse
from models.product import Product
from models.inventory_transfer import StoreInventoryTransfer, StoreInventoryTransferItem
from schemas.inventory_transfer import (
    InventoryTransferCreate, InventoryTransferUpdate,
    InventoryTransferResponse, InventoryTransferListResponse,
    UploadTransferSheetResponse
)
from services.storage_service import StorageService
from api.deps import get_current_user, get_current_project_id, get_current_tenant_id

# 配置日志
logger = logging.getLogger(__name__)

router = APIRouter()

# 生成调拨单号
async def generate_transfer_number(db: AsyncSession, prefix: str = "TR") -> str:
    """生成调拨单号"""
    today = datetime.now().strftime("%Y%m%d")
    # 查询今天的最大单号
    result = await db.execute(
        select(func.max(StoreInventoryTransfer.transfer_number))
        .where(StoreInventoryTransfer.transfer_number.like(f"{prefix}{today}%"))
    )
    max_number = result.scalar_one_or_none()

    if max_number:
        # 提取序号部分并加1
        seq = int(max_number[len(prefix) + 8:]) + 1
    else:
        seq = 1

    # 生成新单号：前缀 + 日期 + 4位序号
    return f"{prefix}{today}{seq:04d}"

@router.get("/", response_model=InventoryTransferListResponse)
async def get_inventory_transfers(
    transfer_type: Optional[str] = None,
    status: Optional[str] = None,
    source_warehouse_id: Optional[uuid.UUID] = None,
    destination_id: Optional[uuid.UUID] = None,
    keyword: Optional[str] = None,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    page: int = Query(1, ge=1),
    page_size: int = Query(10, ge=1, le=100),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    project_id: uuid.UUID = Depends(get_current_project_id)
):
    """获取库存调拨列表"""
    try:
        # 计算分页偏移量
        skip = (page - 1) * page_size

        # 构建查询条件
        conditions = [StoreInventoryTransfer.project_id == project_id]

        if transfer_type:
            conditions.append(StoreInventoryTransfer.transfer_type == transfer_type)

        if status:
            conditions.append(StoreInventoryTransfer.status == status)

        if source_warehouse_id:
            conditions.append(StoreInventoryTransfer.source_warehouse_id == source_warehouse_id)

        if destination_id:
            conditions.append(StoreInventoryTransfer.destination_id == destination_id)

        if keyword:
            conditions.append(
                or_(
                    StoreInventoryTransfer.transfer_number.ilike(f"%{keyword}%"),
                    StoreInventoryTransfer.destination_name.ilike(f"%{keyword}%")
                )
            )

        if start_date:
            conditions.append(StoreInventoryTransfer.transfer_date >= start_date)

        if end_date:
            conditions.append(StoreInventoryTransfer.transfer_date <= end_date)

        # 查询总数
        count_query = select(func.count()).select_from(StoreInventoryTransfer).where(*conditions)
        result = await db.execute(count_query)
        total = result.scalar_one()

        # 查询数据
        query = (
            select(
                StoreInventoryTransfer,
                Warehouse.name.label("source_warehouse_name"),
                Store.name.label("destination_store_name")
            )
            .outerjoin(Warehouse, StoreInventoryTransfer.source_warehouse_id == Warehouse.id)
            .outerjoin(Store, StoreInventoryTransfer.destination_id == Store.id)
            .where(*conditions)
            .order_by(desc(StoreInventoryTransfer.created_at))
            .offset(skip)
            .limit(page_size)
        )

        result = await db.execute(query)
        rows = result.all()

        # 处理结果
        items = []
        for row in rows:
            transfer = row[0]
            transfer.source_warehouse_name = row[1]

            # 根据调拨类型设置目标名称
            if transfer.transfer_type == "store" and row[2]:
                transfer.destination_name = row[2]

            items.append(transfer)

        # 计算总页数
        pages = (total + page_size - 1) // page_size if total > 0 else 0

        return {
            "success": True,
            "message": "获取库存调拨列表成功",
            "items": items,
            "total": total,
            "page": page,
            "page_size": page_size,
            "pages": pages
        }
    except Exception as e:
        logger.error(f"获取库存调拨列表失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取库存调拨列表失败: {str(e)}"
        )

@router.get("/{transfer_id}", response_model=InventoryTransferResponse)
async def get_inventory_transfer(
    transfer_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    project_id: uuid.UUID = Depends(get_current_project_id)
):
    """获取库存调拨详情"""
    try:
        # 查询调拨
        query = (
            select(
                StoreInventoryTransfer,
                Warehouse.name.label("source_warehouse_name"),
                Store.name.label("destination_store_name")
            )
            .outerjoin(Warehouse, StoreInventoryTransfer.source_warehouse_id == Warehouse.id)
            .outerjoin(Store, StoreInventoryTransfer.destination_id == Store.id)
            .where(
                StoreInventoryTransfer.id == transfer_id,
                StoreInventoryTransfer.project_id == project_id
            )
        )

        result = await db.execute(query)
        row = result.first()

        if not row:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="库存调拨不存在"
            )

        transfer = row[0]
        transfer.source_warehouse_name = row[1]

        # 根据调拨类型设置目标名称
        if transfer.transfer_type == "store" and row[2]:
            transfer.destination_name = row[2]

        # 查询明细
        items_query = select(StoreInventoryTransferItem).where(StoreInventoryTransferItem.transfer_id == transfer.id)
        items_result = await db.execute(items_query)
        transfer.items = items_result.scalars().all()

        return {
            "success": True,
            "message": "获取库存调拨详情成功",
            "data": transfer
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取库存调拨详情失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取库存调拨详情失败: {str(e)}"
        )


@router.post("/", response_model=InventoryTransferResponse, status_code=status.HTTP_201_CREATED)
async def create_inventory_transfer(
    data: InventoryTransferCreate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    project_id: uuid.UUID = Depends(get_current_project_id),
    tenant_id: Optional[uuid.UUID] = Depends(get_current_tenant_id)
):
    """创建库存调拨"""
    try:
        # 验证调拨类型
        if data.transfer_type not in ["store", "third_party"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无效的调拨类型，必须是 'store' 或 'third_party'"
            )

        # 验证源仓库
        source_query = select(Warehouse).where(
            Warehouse.id == data.source_warehouse_id,
            Warehouse.project_id == project_id
        )
        result = await db.execute(source_query)
        source_warehouse = result.scalar_one_or_none()

        if not source_warehouse:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="源仓库不存在"
            )

        # 验证目标门店（如果是门店配货）
        if data.transfer_type == "store":
            if not data.destination_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="门店配货必须指定目标门店"
                )

            destination_query = select(Store).where(
                Store.id == data.destination_id,
                Store.project_id == project_id
            )
            result = await db.execute(destination_query)
            destination_store = result.scalar_one_or_none()

            if not destination_store:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="目标门店不存在"
                )

        # 验证三方调拨目标名称
        if data.transfer_type == "third_party" and not data.destination_name:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="三方调拨必须指定目标名称"
            )

        # 验证商品明细
        if not data.items or len(data.items) == 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="调拨明细不能为空"
            )

        # 生成调拨单号
        transfer_number = await generate_transfer_number(db)

        # 计算总金额和总数量
        total_amount = sum(item.amount or 0 for item in data.items)
        total_items = len(data.items)

        # 创建调拨单
        transfer = StoreInventoryTransfer(
            tenant_id=tenant_id,
            project_id=project_id,
            transfer_number=transfer_number,
            transfer_type=data.transfer_type,
            source_warehouse_id=data.source_warehouse_id,
            destination_id=data.destination_id,
            destination_name=data.destination_name,
            transfer_date=data.transfer_date,
            status="pending",
            payment_method=data.payment_method,
            total_amount=total_amount,
            total_items=total_items,
            notes=data.notes,
            created_by=current_user.id,
            updated_by=current_user.id
        )

        db.add(transfer)
        await db.flush()

        # 创建调拨明细
        for item_data in data.items:
            item = StoreInventoryTransferItem(
                transfer_id=transfer.id,
                product_id=item_data.product_id,
                product_name=item_data.product_name,
                product_code=item_data.product_code,
                specification=item_data.specification,
                unit=item_data.unit,
                quantity=item_data.quantity,
                price=item_data.price,
                amount=item_data.amount,
                notes=item_data.notes
            )
            db.add(item)

        await db.commit()

        # 查询创建后的完整数据
        query = (
            select(
                StoreInventoryTransfer,
                Warehouse.name.label("source_warehouse_name"),
                Store.name.label("destination_store_name")
            )
            .outerjoin(Warehouse, StoreInventoryTransfer.source_warehouse_id == Warehouse.id)
            .outerjoin(Store, StoreInventoryTransfer.destination_id == Store.id)
            .where(StoreInventoryTransfer.id == transfer.id)
        )

        result = await db.execute(query)
        row = result.first()

        transfer = row[0]
        transfer.source_warehouse_name = row[1]

        # 根据调拨类型设置目标名称
        if transfer.transfer_type == "store" and row[2]:
            transfer.destination_name = row[2]

        # 查询明细
        items_query = select(StoreInventoryTransferItem).where(StoreInventoryTransferItem.transfer_id == transfer.id)
        items_result = await db.execute(items_query)
        transfer.items = items_result.scalars().all()

        return {
            "success": True,
            "message": "创建库存调拨成功",
            "data": transfer
        }
    except HTTPException:
        await db.rollback()
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"创建库存调拨失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建库存调拨失败: {str(e)}"
        )


@router.post("/upload", response_model=UploadTransferSheetResponse)
async def upload_transfer_sheet(
    file: UploadFile = File(...),
    source_warehouse_id: uuid.UUID = Form(...),
    destination_id: Optional[uuid.UUID] = Form(None),
    destination_name: Optional[str] = Form(None),
    transfer_type: str = Form(...),
    payment_method: Optional[str] = Form(None),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    project_id: uuid.UUID = Depends(get_current_project_id),
    tenant_id: Optional[uuid.UUID] = Depends(get_current_tenant_id)
):
    """上传调拨表格"""
    try:
        # 验证调拨类型
        if transfer_type not in ["store", "third_party"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无效的调拨类型，必须是 'store' 或 'third_party'"
            )

        # 验证源仓库
        source_query = select(Warehouse).where(
            Warehouse.id == source_warehouse_id,
            Warehouse.project_id == project_id
        )
        result = await db.execute(source_query)
        source_warehouse = result.scalar_one_or_none()

        if not source_warehouse:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="源仓库不存在"
            )

        # 验证目标门店（如果是门店配货）
        destination_stores = {}
        if transfer_type == "store":
            if not destination_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="门店配货必须指定目标门店"
                )

            # 查询所有门店，用于多门店处理
            stores_query = select(Store).where(Store.project_id == project_id)
            stores_result = await db.execute(stores_query)
            stores = stores_result.scalars().all()

            for store in stores:
                destination_stores[store.name] = store.id

            # 验证指定的目标门店
            if destination_id and destination_id not in [store.id for store in stores]:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="目标门店不存在"
                )

        # 验证三方调拨目标名称
        if transfer_type == "third_party" and not destination_name:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="三方调拨必须指定目标名称"
            )

        # 读取Excel文件
        content = await file.read()

        try:
            df = pd.read_excel(io.BytesIO(content))
        except Exception:
            try:
                df = pd.read_csv(io.BytesIO(content))
            except Exception:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="无法解析文件，请确保文件格式正确（Excel或CSV）"
                )

        # 验证表格格式
        required_columns = ["商品名称", "数量"]
        for col in required_columns:
            if col not in df.columns:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"表格缺少必要的列：{col}"
                )

        # 查询所有产品，用于匹配
        products_query = select(Product).where(Product.project_id == project_id)
        products_result = await db.execute(products_query)
        products = products_result.scalars().all()

        # 创建产品名称到产品ID的映射
        product_map = {}
        for product in products:
            product_map[product.name] = {
                "id": product.id,
                "code": product.code,
                "specification": product.specification,
                "unit": product.unit,
                "price": product.price
            }

        # 处理表格数据
        transfers_data = {}

        # 检查是否有门店列（用于多门店处理）
        has_store_column = "门店" in df.columns

        for _, row in df.iterrows():
            product_name = row["商品名称"]
            quantity = row["数量"]

            # 跳过无效行
            if pd.isna(product_name) or pd.isna(quantity) or quantity <= 0:
                continue

            # 获取产品信息
            product_info = product_map.get(product_name)
            if not product_info:
                continue

            # 获取价格（如果表格中有）
            price = row.get("单价", product_info["price"])
            if pd.isna(price):
                price = product_info["price"]

            # 计算金额
            amount = price * quantity

            # 获取目标门店（如果表格中有）
            target_store_id = destination_id
            target_store_name = destination_name

            if has_store_column and transfer_type == "store":
                store_name = row["门店"]
                if not pd.isna(store_name) and store_name in destination_stores:
                    target_store_id = destination_stores[store_name]
                    target_store_name = store_name

            # 为每个目标创建一个调拨单
            target_key = str(target_store_id) if target_store_id else target_store_name

            if target_key not in transfers_data:
                transfers_data[target_key] = {
                    "destination_id": target_store_id,
                    "destination_name": target_store_name,
                    "items": []
                }

            # 添加商品到调拨单
            transfers_data[target_key]["items"].append({
                "product_id": product_info["id"],
                "product_name": product_name,
                "product_code": product_info["code"],
                "specification": product_info["specification"],
                "unit": product_info["unit"],
                "quantity": int(quantity),
                "price": float(price),
                "amount": float(amount)
            })

        # 验证是否有有效数据
        if not transfers_data:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="表格中没有有效的调拨数据"
            )

        # 创建调拨单
        created_transfers = []

        for target_key, transfer_data in transfers_data.items():
            # 生成调拨单号
            transfer_number = await generate_transfer_number(db)

            # 计算总金额和总数量
            items = transfer_data["items"]
            total_amount = sum(item["amount"] for item in items)
            total_items = len(items)

            # 创建调拨单
            transfer = StoreInventoryTransfer(
                tenant_id=tenant_id,
                project_id=project_id,
                transfer_number=transfer_number,
                transfer_type=transfer_type,
                source_warehouse_id=source_warehouse_id,
                destination_id=transfer_data["destination_id"],
                destination_name=transfer_data["destination_name"],
                transfer_date=datetime.now(timezone.utc),
                status="pending",
                payment_method=payment_method,
                total_amount=total_amount,
                total_items=total_items,
                notes=f"通过表格上传创建",
                created_by=current_user.id,
                updated_by=current_user.id
            )

            db.add(transfer)
            await db.flush()

            # 创建调拨单明细
            for item_data in items:
                item = StoreInventoryTransferItem(
                    transfer_id=transfer.id,
                    product_id=item_data["product_id"],
                    product_name=item_data["product_name"],
                    product_code=item_data["product_code"],
                    specification=item_data["specification"],
                    unit=item_data["unit"],
                    quantity=item_data["quantity"],
                    price=item_data["price"],
                    amount=item_data["amount"]
                )
                db.add(item)

            created_transfers.append({
                "id": str(transfer.id),
                "transfer_number": transfer_number,
                "destination_name": transfer_data["destination_name"] or "未指定",
                "total_items": total_items,
                "total_amount": total_amount
            })

        await db.commit()

        return {
            "success": True,
            "message": f"成功创建 {len(created_transfers)} 个调拨单",
            "data": {
                "transfers": created_transfers
            }
        }
    except HTTPException:
        await db.rollback()
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"上传调拨表格失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"上传调拨表格失败: {str(e)}"
        )


@router.put("/{transfer_id}", response_model=InventoryTransferResponse)
async def update_inventory_transfer(
    transfer_id: uuid.UUID,
    data: InventoryTransferUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    project_id: uuid.UUID = Depends(get_current_project_id)
):
    """更新库存调拨"""
    try:
        # 查询调拨单
        query = select(StoreInventoryTransfer).where(
            StoreInventoryTransfer.id == transfer_id,
            StoreInventoryTransfer.project_id == project_id
        )
        result = await db.execute(query)
        transfer = result.scalar_one_or_none()

        if not transfer:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="库存调拨不存在"
            )

        # 只有待处理状态的调拨单可以更新
        if transfer.status != "pending":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="只有待处理状态的调拨单可以更新"
            )

        # 更新字段
        if data.status is not None:
            transfer.status = data.status

        if data.notes is not None:
            transfer.notes = data.notes

        transfer.updated_by = current_user.id
        transfer.updated_at = datetime.now(timezone.utc)

        await db.commit()

        # 查询更新后的完整数据
        query = (
            select(
                StoreInventoryTransfer,
                Warehouse.name.label("source_warehouse_name"),
                Store.name.label("destination_store_name")
            )
            .outerjoin(Warehouse, StoreInventoryTransfer.source_warehouse_id == Warehouse.id)
            .outerjoin(Store, StoreInventoryTransfer.destination_id == Store.id)
            .where(StoreInventoryTransfer.id == transfer_id)
        )

        result = await db.execute(query)
        row = result.first()

        transfer = row[0]
        transfer.source_warehouse_name = row[1]

        # 根据调拨类型设置目标名称
        if transfer.transfer_type == "store" and row[2]:
            transfer.destination_name = row[2]

        # 查询明细
        items_query = select(StoreInventoryTransferItem).where(StoreInventoryTransferItem.transfer_id == transfer.id)
        items_result = await db.execute(items_query)
        transfer.items = items_result.scalars().all()

        return {
            "success": True,
            "message": "更新库存调拨成功",
            "data": transfer
        }
    except HTTPException:
        await db.rollback()
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"更新库存调拨失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新库存调拨失败: {str(e)}"
        )


@router.post("/{transfer_id}/process", response_model=InventoryTransferResponse)
async def process_inventory_transfer(
    transfer_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    project_id: uuid.UUID = Depends(get_current_project_id)
):
    """处理库存调拨"""
    try:
        # 查询调拨单
        query = select(StoreInventoryTransfer).where(
            StoreInventoryTransfer.id == transfer_id,
            StoreInventoryTransfer.project_id == project_id
        )
        result = await db.execute(query)
        transfer = result.scalar_one_or_none()

        if not transfer:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="库存调拨不存在"
            )

        # 只有待处理状态的调拨单可以处理
        if transfer.status != "pending":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="只有待处理状态的调拨单可以处理"
            )

        # 更新状态
        transfer.status = "processing"
        transfer.updated_by = current_user.id
        transfer.updated_at = datetime.now(timezone.utc)

        await db.commit()

        # 查询更新后的完整数据
        query = (
            select(
                StoreInventoryTransfer,
                Warehouse.name.label("source_warehouse_name"),
                Store.name.label("destination_store_name")
            )
            .outerjoin(Warehouse, StoreInventoryTransfer.source_warehouse_id == Warehouse.id)
            .outerjoin(Store, StoreInventoryTransfer.destination_id == Store.id)
            .where(StoreInventoryTransfer.id == transfer_id)
        )

        result = await db.execute(query)
        row = result.first()

        transfer = row[0]
        transfer.source_warehouse_name = row[1]

        # 根据调拨类型设置目标名称
        if transfer.transfer_type == "store" and row[2]:
            transfer.destination_name = row[2]

        # 查询明细
        items_query = select(StoreInventoryTransferItem).where(StoreInventoryTransferItem.transfer_id == transfer.id)
        items_result = await db.execute(items_query)
        transfer.items = items_result.scalars().all()

        return {
            "success": True,
            "message": "处理库存调拨成功",
            "data": transfer
        }
    except HTTPException:
        await db.rollback()
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"处理库存调拨失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"处理库存调拨失败: {str(e)}"
        )
