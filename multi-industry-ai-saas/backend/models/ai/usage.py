#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
AI 使用记录模型
"""

import uuid
from datetime import datetime
from sqlalchemy import Column, String, Integer, Float, ForeignKey, DateTime, Text, func
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship

from db.database import Base

class AIUsage(Base):
    """AI 使用记录模型"""
    __tablename__ = "ai_usages"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id", ondelete="CASCADE"), nullable=False, comment="租户 ID")
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id", ondelete="CASCADE"), nullable=False, comment="项目 ID")
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id", ondelete="CASCADE"), nullable=False, comment="用户 ID")
    config_id = Column(UUID(as_uuid=True), ForeignKey("ai_configs.id", ondelete="SET NULL"), nullable=True, comment="配置 ID")
    model_id = Column(UUID(as_uuid=True), ForeignKey("ai_models.id", ondelete="SET NULL"), nullable=True, comment="模型 ID")
    request_type = Column(String(20), nullable=False, comment="请求类型：chat, vision, embedding, audio")
    prompt_tokens = Column(Integer, nullable=False, default=0, comment="提示 token 数")
    completion_tokens = Column(Integer, nullable=False, default=0, comment="完成 token 数")
    total_tokens = Column(Integer, nullable=False, default=0, comment="总 token 数")
    cost = Column(Float, nullable=False, default=0, comment="成本（美元）")
    duration_ms = Column(Integer, nullable=False, default=0, comment="请求耗时（毫秒）")
    status = Column(String(20), nullable=False, default="success", comment="状态：success, failed")
    error_message = Column(Text, nullable=True, comment="错误信息")
    request_metadata = Column(JSONB, nullable=True, comment="请求元数据")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")

    # 关系
    tenant = relationship("Tenant", back_populates="ai_usages")
    project = relationship("Project", back_populates="ai_usages")
    user = relationship("User", back_populates="ai_usages")
    config = relationship("AIConfig", back_populates="usages")
    model = relationship("AIModel", back_populates="usages")

    def __repr__(self):
        return f"<AIUsage(id={self.id}, user_id={self.user_id}, model='{self.model.name if self.model else None}')>"
