#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试AI识别功能
"""

print("DEBUG: test_ai_recognition.py script execution started")
import asyncio
import sys
import os
import uuid
import json
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).resolve().parent))

from sqlalchemy.ext.asyncio import AsyncSession
from db.database import get_db, AsyncSessionLocal
# Ensure services.table_processing_service can be imported
try:
    from services.table_processing_service import TableProcessingService
    print("DEBUG: Successfully imported TableProcessingService")
except ImportError as e:
    print(f"DEBUG: FAILED to import TableProcessingService: {e}")
    sys.exit(1)
from schemas.table_processing import TableProcessingRequest
from datetime import datetime, timezone

async def test_ai_recognition():
    print("DEBUG: test_ai_recognition() function started")
    """测试AI识别功能"""
    
    # 使用指定的图片文件（从数据库查询到的最新记录）
    file_id = "d09595ab-5c4b-4231-bfd0-dc30e697d1b3"
    project_id = "*************-48ab-8092-e8eb7f663677"
    user_id = "04a15201-2024-4d45-b434-a0bb4ff40c34"
    
    # Default to True, set to False if any critical step fails
    test_passed_status = True
    
    async with AsyncSessionLocal() as db:
        try:
            print("开始测试AI识别...")
            print(f"文件ID: {file_id}")
            print(f"项目ID: {project_id}")
            print(f"用户ID: {user_id}")
            print("-" * 50)
            
            # 首先检查文件是否存在
            try:
                # 直接查询数据库中的文件记录
                from models.storage import StorageFile
                from sqlalchemy import select
                
                stmt = select(StorageFile).where(
                    StorageFile.id == uuid.UUID(file_id),
                    StorageFile.project_id == uuid.UUID(project_id)
                )
                result = await db.execute(stmt)
                file_info = result.scalar_one_or_none()
                
                if file_info:
                    print(f"文件名: {file_info.name}")
                    print(f"原始文件名: {file_info.original_name}")
                    print(f"文件路径: {file_info.storage_path}")
                    print(f"MIME类型: {file_info.mime_type}")
                    print(f"文件大小: {file_info.size}")
                    
                    # 检查文件物理路径是否存在
                    # 数据库中的路径是相对路径，需要拼接完整路径
                    base_upload_path = "/app/uploads"
                    physical_path = base_upload_path + file_info.storage_path
                    print(f"数据库路径: {file_info.storage_path}")
                    print(f"完整物理路径: {physical_path}")
                    print(f"文件存在: {os.path.exists(physical_path)}")
                    
                    if not os.path.exists(physical_path):
                        print("❌ 物理文件不存在")
                        return test_passed_status
                else:
                    print("❌ 数据库中文件记录不存在")
                    return test_passed_status
                    
            except Exception as e:
                print(f"❌ 获取文件信息失败: {e}")
                import traceback
                traceback.print_exc()
                return test_passed_status
            
            print("-" * 50)
            
            # 直接调用表格处理服务
            table_request = TableProcessingRequest(
                file_id=file_id,
                processing_mode="ai_only",
                custom_prompt="""你的任务是：从采购分拨单图片中，提取【采购总单】的关键信息。
请为图片中的【每一个独立商品行】提取一个汇总条目。
【采购总单】意味着每一条记录代表一个独立的商品及其【总的采购信息】，请【不要】包含任何按门店细分的数量或门店名称字段。

对于每一个独立商品行，请提取以下信息：
1.  "单位"
2.  "规格"
3.  "单价(单规格价)" (指单个规格单位的价格，请确保为数字)
4.  "总数" (指该独立商品行的【总采购数量】。请优先从图片中该商品行对应的"合计"、"总计"或类似汇总列直接读取此数值。此字段代表该商品的总量，【不要】返回单个门店的数量。)
5.  "总金额" (指该独立商品行的【总采购金额】。可由"单价(单规格价)" × "总数" 计算得出，或直接从图片中的总金额列读取。请返回数字。)
6.  "建议分类" (可选)
7.  "建议品牌" (可选)

请将提取的结果整理成一个JSON对象。
该JSON对象的【键】必须是【商品名称】（例如："东魁杨梅A果"）。
每个【键】对应的【值】必须是一个包含该商品上述详细信息的JSON对象。

例如:
{
  "东魁杨梅A果": {
    "单位": "公斤",
    "规格": "1.9",
    "单价(单规格价)": 54,
    "总数": 42, // 示例：这是图片中"东魁杨梅A果 1.9公斤"这一整行的总合计数量
    "总金额": 2268, // 示例：可由 54 * 42 计算
    "建议分类": "水果",
    "建议品牌": "无"
  },
  "另一个商品名称": {
    "单位": "...",
    "规格": "...",
    "单价(单规格价)": 0, // 数字
    "总数": 0, // 数字，来自图片中该商品行的总计
    "总金额": 0, // 数字
    "建议分类": "...",
    "建议品牌": "..."
  }
  // ... 更多独立商品行 ...
}

请【特别注意】：
- JSON的顶层直接是一个以商品名称为键的对象。
- 结果中的每一条记录都必须是【一个独立商品的总览】。
- 如果图片中同时存在商品行总计和各门店分拨数量，你本次任务【只需提取商品行总计】作为"总数"。
- 所有数值类型的字段（单价、总数、总金额）必须返回数字。如果无法识别或图片中不存在，可以返回null。
- 严格区分每一行商品，即使商品名称相似，只要规格、等级、产地等有细微差异，都视为不同商品分别列出。
""",
                use_default_vision_model=True,
                vision_model_id=None,
                vision_temperature=0.3,
                enable_validation=True,
                enable_correction=True
            )
            
            print("调用表格处理服务...")
            print("请求参数:")
            print(f"  - file_id: {table_request.file_id}")
            print(f"  - processing_mode: {table_request.processing_mode}")
            print(f"  - use_default_vision_model: {table_request.use_default_vision_model}")
            print(f"  - vision_temperature: {table_request.vision_temperature}")
            print(f"  - enable_validation: {table_request.enable_validation}")
            print(f"  - enable_correction: {table_request.enable_correction}")
            print("-" * 50)
            
            result = await TableProcessingService.process_table(
                db=db,
                request=table_request,
                project_id=uuid.UUID(project_id),
                tenant_id=None,
                user_id=uuid.UUID(user_id)
            )
            
            print("处理结果:")
            print(f"成功: {result.success}")
            print(f"处理方法: {result.processing_method}")
            if result.errors:
                print(f"错误: {result.errors}")
            if result.warnings:
                print(f"警告: {result.warnings}")
            
            if result.success and result.extracted_data:
                print(f"提取数据类型: {type(result.extracted_data)}")
                if isinstance(result.extracted_data, dict):
                    print(f"数据键: {list(result.extracted_data.keys())}")
                    if "data" in result.extracted_data:
                        print(f"数据行数: {len(result.extracted_data['data'])}")
                        if result.extracted_data['data']:
                            print(f"第一行数据: {result.extracted_data['data'][0]}")
                    if "distribution_targets" in result.extracted_data:
                        print(f"分拨目标: {result.extracted_data['distribution_targets']}")
                    if "suppliers" in result.extracted_data:
                        print(f"供应商: {result.extracted_data['suppliers']}")
                    if "categories" in result.extracted_data:
                        print(f"分类: {result.extracted_data['categories']}")
                elif isinstance(result.extracted_data, list):
                    print(f"数据行数: {len(result.extracted_data)}")
                    if result.extracted_data:
                        print(f"第一行数据: {result.extracted_data[0]}")
                
            print("\n" + "=" * 50)
            print("完整结果:")
            print(json.dumps({
                "success": result.success,
                "processing_method": result.processing_method,
                "extracted_data": result.extracted_data,
                "errors": result.errors,
                "warnings": result.warnings
            }, ensure_ascii=False, indent=2))
            
            if not result.success:
                test_passed_status = False
            
        except Exception as e:
            print(f"测试失败: {e}")
            import traceback
            traceback.print_exc()
            
            # 详细分析错误
            print("\n" + "=" * 50)
            print("错误详细分析:")
            print(f"错误类型: {type(e).__name__}")
            print(f"错误消息: {str(e)}")
            
            # 如果是转换错误，进一步分析
            if "转换AI结果格式失败" in str(e):
                print("这是AI结果格式转换错误，可能的原因:")
                print("1. AI返回的JSON格式不正确")
                print("2. json-repair修复失败")
                print("3. 提取的数据为空或格式不符合预期")
                print("4. 图片质量问题导致AI无法正确识别")
                
            # 如果是ValueError且包含"未提取到有效数据"
            if isinstance(e, ValueError) and "未提取到有效数据" in str(e):
                print("这是数据提取失败错误，可能的原因:")
                print("1. 图片中没有可识别的表格数据")
                print("2. 表格格式与预期不符")
                print("3. AI模型无法理解图片内容")
                print("4. 图片分辨率过低或模糊")

            test_passed_status = False

        finally:
            print("DEBUG: test_ai_recognition() function finished")
            return test_passed_status

async def main():
    print("DEBUG: main() function started")
    all_tests_passed = True
    try:
        recognition_test_result = await test_ai_recognition()
        if not recognition_test_result:
            all_tests_passed = False
        
        # Do not run the second test for now
        # print("DEBUG: Skipping test_ai_vision_service_directly()")
        # vision_test_result = await test_ai_vision_service_directly()
        # if not vision_test_result:
        #     all_tests_passed = False
        
    except Exception as e:
        print(f"DEBUG: Exception in main function: {e}")
        import traceback
        traceback.print_exc()
        all_tests_passed = False
    finally:
        print("DEBUG: main() function finished")
        if all_tests_passed:
            print("DEBUG: All tests passed. Exiting with 0.")
            sys.exit(0)
        else:
            print("DEBUG: Some tests failed. Exiting with 1.")
            sys.exit(1)

if __name__ == "__main__":
    print("DEBUG: __main__ block started")
    asyncio.run(main())
    # This line likely won't be reached due to sys.exit() in main()
    print("DEBUG: __main__ block finished (asyncio.run completed)")

# Comment out or remove test_ai_vision_service_directly to isolate issues
# async def test_ai_vision_service_directly():
#     """直接测试AI视觉服务"""
#     print("\n" + "=" * 50)
#     print("直接测试AI视觉服务...")
#     
#     try:
#         from services.ai.vision_service import AIVisionService
#         
#         # 查找对应的图片文件
#         async with AsyncSessionLocal() as db:
#             from models.storage import StorageFile
#             from sqlalchemy import select
#             
#             file_id = "d09595ab-5c4b-4231-bfd0-dc30e697d1b3"
#             project_id = "*************-48ab-8092-e8eb7f663677"
#             
#             stmt = select(StorageFile).where(
#                 StorageFile.id == uuid.UUID(file_id),
#                 StorageFile.project_id == uuid.UUID(project_id)
#             )
#             result = await db.execute(stmt)
#             file_info = result.scalar_one_or_none()
#             
#             if not file_info:
#                 print("❌ 文件信息不存在")
#                 return
#                 
#             # 拼接完整路径
#             base_upload_path = "/app/uploads"
#             file_path = base_upload_path + file_info.storage_path
#             print(f"数据库路径: {file_info.storage_path}")
#             print(f"完整图片路径: {file_path}")
#             print(f"文件存在: {os.path.exists(file_path)}")
#             
#             if os.path.exists(file_path):
#                 # 获取文件大小
#                 file_size = os.path.getsize(file_path)
#                 print(f"文件大小: {file_size} bytes ({file_size/1024:.2f} KB)")
#                 
#                 # 测试AI识别 - 读取图片文件
#                 with open(file_path, 'rb') as f:
#                     image_data = f.read()
#                 
#                 prompt = """请分析这个采购分拨单表格，提取商品信息和分拨数据。
# 
# **必须严格按照以下JSON格式返回**：
# {
#     "columns": ["商品名称", "单位", "规格", "单价", "合计", "供应商", "分类", "门店1", "门店2"],
#     "data": [
#         {
#             "商品名称": "商品名称",
#             "单位": "单位",
#             "规格": "规格",
#             "单价": 0,
#             "合计": 0,
#             "供应商": "供应商",
#             "分类": "分类",
#             "门店1": 0,
#             "门店2": 0
#         }
#     ],
#     "distribution_targets": ["门店1", "门店2"],
#     "suppliers": ["供应商1"],
#     "categories": ["分类1"]
# }"""
#                 
#                 print("调用AI视觉识别...")
#                 try:
#                     result = await AIVisionService.analyze_image(
#                         db=db,
#                         project_id=uuid.UUID(project_id),
#                         user_id=uuid.UUID("04a15201-2024-4d45-b434-a0bb4ff40c34"),
#                         image_data=image_data,
#                         prompt=prompt,
#                         model_id=None,  # 使用默认模型
#                         temperature=0.3
#                     )
#                     
#                     print("AI识别原始结果:")
#                     print(result)
#                     
#                     # 尝试解析JSON
#                     try:
#                         import json
#                         parsed = json.loads(result)
#                         print("JSON解析成功:")
#                         print(json.dumps(parsed, ensure_ascii=False, indent=2))
#                     except json.JSONDecodeError as je:
#                         print(f"JSON解析失败: {je}")
#                         
#                         # 尝试使用json-repair修复
#                         try:
#                             import json_repair
#                             repaired = json_repair.repair_json(result)
#                             print("json-repair修复结果:")
#                             print(repaired)
#                             
#                             parsed_repaired = json.loads(repaired)
#                             print("修复后JSON解析成功:")
#                             print(json.dumps(parsed_repaired, ensure_ascii=False, indent=2))
#                         except Exception as repair_error:
#                             print(f"json-repair修复失败: {repair_error}")
#                             
#                 except Exception as ai_error:
#                     print(f"AI识别失败: {ai_error}")
#                     import traceback
#                     traceback.print_exc()
#             else:
#                 print("❌ 图片文件不存在")
#         
#     except Exception as e:
#         print(f"直接测试AI视觉服务失败: {e}")
#         import traceback
#         traceback.print_exc() 