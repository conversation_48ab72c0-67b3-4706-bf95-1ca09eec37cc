#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
添加系统AI整合设置表的迁移脚本
"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import UUID

# 修订版本信息
revision = 'add_system_ai_integration'
down_revision = None  # 应该设置为上一个迁移的revision
branch_labels = None
depends_on = None

def upgrade():
    """创建system_ai_integrations表"""
    
    # 创建系统AI整合设置表
    op.create_table(
        'system_ai_integrations',
        sa.Column('id', UUID(as_uuid=True), primary_key=True),
        sa.Column('tenant_id', UUID(as_uuid=True), 
                 sa.ForeignKey('tenants.id', ondelete='CASCADE'), 
                 nullable=True, comment='租户 ID，空表示全局设置'),
        sa.Column('project_id', UUID(as_uuid=True), 
                 sa.ForeignKey('projects.id', ondelete='CASCADE'), 
                 nullable=True, comment='项目 ID，空表示租户级设置'),
        
        # 默认模型配置
        sa.Column('default_chat_model_id', UUID(as_uuid=True), 
                 sa.ForeignKey('ai_models.id', ondelete='SET NULL'), 
                 nullable=True, comment='默认聊天模型ID'),
        sa.Column('default_vision_model_id', UUID(as_uuid=True), 
                 sa.ForeignKey('ai_models.id', ondelete='SET NULL'), 
                 nullable=True, comment='默认视觉模型ID'),
        sa.Column('default_audio_model_id', UUID(as_uuid=True), 
                 sa.ForeignKey('ai_models.id', ondelete='SET NULL'), 
                 nullable=True, comment='默认语音模型ID'),
        sa.Column('default_embedding_model_id', UUID(as_uuid=True), 
                 sa.ForeignKey('ai_models.id', ondelete='SET NULL'), 
                 nullable=True, comment='默认嵌入模型ID'),
        
        # 模型配置参数
        sa.Column('chat_temperature', sa.Float, nullable=False, default=0.7, 
                 comment='聊天模型温度参数'),
        sa.Column('chat_max_tokens', sa.Integer, nullable=True, 
                 comment='聊天模型最大token数'),
        sa.Column('vision_temperature', sa.Float, nullable=False, default=0.7, 
                 comment='视觉模型温度参数'),
        sa.Column('vision_max_tokens', sa.Integer, nullable=True, 
                 comment='视觉模型最大token数'),
        
        # 服务可用性控制
        sa.Column('chat_enabled', sa.Boolean, nullable=False, default=True, 
                 comment='启用聊天功能'),
        sa.Column('vision_enabled', sa.Boolean, nullable=False, default=True, 
                 comment='启用视觉功能'),
        sa.Column('audio_enabled', sa.Boolean, nullable=False, default=True, 
                 comment='启用语音功能'),
        sa.Column('embedding_enabled', sa.Boolean, nullable=False, default=True, 
                 comment='启用嵌入功能'),
        
        # 通用配置
        sa.Column('enable_fallback', sa.Boolean, nullable=False, default=True, 
                 comment='启用模型回退机制'),
        sa.Column('request_timeout', sa.Integer, nullable=False, default=30, 
                 comment='请求超时时间(秒)'),
        sa.Column('max_retries', sa.Integer, nullable=False, default=3, 
                 comment='最大重试次数'),
        
        # 元数据
        sa.Column('created_at', sa.DateTime, nullable=False, 
                 server_default=sa.func.now(), comment='创建时间'),
        sa.Column('updated_at', sa.DateTime, nullable=False, 
                 server_default=sa.func.now(), comment='更新时间'),
        
        comment='系统AI整合设置表'
    )
    
    # 创建索引
    op.create_index('idx_system_ai_integrations_tenant_project', 
                   'system_ai_integrations', 
                   ['tenant_id', 'project_id'])
    
    # 添加约束：确保tenant_id和project_id组合的唯一性
    op.create_unique_constraint('uq_system_ai_integrations_tenant_project',
                               'system_ai_integrations',
                               ['tenant_id', 'project_id'])

def downgrade():
    """删除system_ai_integrations表"""
    
    # 删除索引
    op.drop_index('idx_system_ai_integrations_tenant_project', 
                 'system_ai_integrations')
    
    # 删除表
    op.drop_table('system_ai_integrations') 