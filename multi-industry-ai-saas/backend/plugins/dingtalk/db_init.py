#!/usr/bin/env python
# -*- coding: utf-8 -*-

import logging
import asyncio
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import and_
import uuid
from datetime import datetime
from sqlalchemy.orm import selectinload

from db.database import get_db
from models.tenant import Tenant
from models.project import Project
from models.plugin import Plugin, TenantPlugin
from .models.models import DingTalkSettings, DingTalkWebhook
from models.user import User

# 初始化日志
logger = logging.getLogger(__name__)

# 插件信息
PLUGIN_INFO = {
    "code": "dingtalk_deep_integration",
    "name": "钉钉深度集成",
    "description": "钉钉深度集成插件，提供组织架构同步、文档集成、待办任务管理等全面功能",
    "version": "2.0.0",
    "type": "marketplace",
    "category": "ai_agent",
    "icon_url": "/static/plugins/dingtalk/icon.png",
    "price": 0,
    "author": "系统团队",
    "homepage": "https://open.dingtalk.com",
    "features": [
        "组织架构同步",
        "钉钉文档集成",
        "待办任务管理",
        "智能机器人对话",
        "消息推送与通知",
        "群聊管理",
        "用户身份映射",
        "权限管理",
        "数据同步",
        "第三方登录"
    ]
}

async def initialize_plugin(tenant_id=None, project_id=None):
    """初始化钉钉机器人插件数据库"""
    logger.info("初始化钉钉机器人插件数据库")

    async for db in get_db():
        try:
            # 检查插件是否已注册
            plugin_query = select(Plugin).where(Plugin.code == PLUGIN_INFO["code"])
            result = await db.execute(plugin_query)
            plugin = result.scalar_one_or_none()

            if not plugin:
                # 注册插件
                plugin = Plugin(
                    id=uuid.uuid4(),
                    code=PLUGIN_INFO["code"],
                    name=PLUGIN_INFO["name"],
                    description=PLUGIN_INFO["description"],
                    version=PLUGIN_INFO["version"],
                    type=PLUGIN_INFO["type"],
                    category=PLUGIN_INFO["category"],
                    icon_url=PLUGIN_INFO["icon_url"],
                    price=PLUGIN_INFO["price"],
                    is_system=False,
                    is_active=True,
                    author=PLUGIN_INFO["author"],
                    homepage=PLUGIN_INFO["homepage"],
                    installation_path="plugins.dingtalk",
                    entry_point="initialize",
                    created_at=datetime.now(),
                    settings_schema={
                        "properties": {
                            "enable_dingtalk": {
                                "type": "boolean",
                                "title": "启用钉钉通知",
                                "default": True
                            },
                            "notification_level": {
                                "type": "string",
                                "title": "通知级别",
                                "enum": ["all", "high_only", "custom"],
                                "enumNames": ["所有通知", "仅高优先级", "自定义"],
                                "default": "all"
                            },
                            "retry_count": {
                                "type": "integer",
                                "title": "重试次数",
                                "minimum": 0,
                                "maximum": 10,
                                "default": 3
                            },
                            "retry_interval": {
                                "type": "integer",
                                "title": "重试间隔(秒)",
                                "minimum": 10,
                                "maximum": 300,
                                "default": 60
                            },
                            "default_template": {
                                "type": "string",
                                "title": "默认消息模板",
                                "format": "textarea",
                                "default": "### {{title}}\n\n{{content}}\n\n> 来自系统通知 - {{time}}"
                            }
                        }
                    },
                    features=PLUGIN_INFO["features"]
                )
                db.add(plugin)
                await db.commit()
                logger.info(f"钉钉机器人插件注册成功: {plugin.id}")
            else:
                # 更新插件信息
                plugin.name = PLUGIN_INFO["name"]
                plugin.description = PLUGIN_INFO["description"]
                plugin.version = PLUGIN_INFO["version"]
                plugin.type = PLUGIN_INFO["type"]
                plugin.category = PLUGIN_INFO["category"]
                plugin.icon_url = PLUGIN_INFO["icon_url"]
                plugin.price = PLUGIN_INFO["price"]
                plugin.author = PLUGIN_INFO["author"]
                plugin.homepage = PLUGIN_INFO["homepage"]
                plugin.installation_path = "plugins.dingtalk"
                plugin.entry_point = "initialize"
                plugin.is_active = True
                plugin.last_updated_at = datetime.now()

                await db.commit()
                logger.info(f"更新了钉钉机器人插件: {plugin.id}")

            # 如果指定了租户和项目，则为其初始化插件设置
            if tenant_id and project_id:
                await initialize_tenant_plugin(db, tenant_id, project_id, plugin.id)

        except Exception as e:
            logger.error(f"初始化钉钉机器人插件失败: {str(e)}")
            await db.rollback()
            raise
        finally:
            await db.close()

async def initialize_tenant_plugin(db: AsyncSession, tenant_id: uuid.UUID, project_id: uuid.UUID, plugin_id: uuid.UUID):
    """为指定租户和项目初始化插件设置"""
    try:
        # 检查租户插件是否已安装
        tenant_plugin_query = select(TenantPlugin).where(
            and_(
                TenantPlugin.tenant_id == tenant_id,
                TenantPlugin.plugin_id == plugin_id
            )
        )
        result = await db.execute(tenant_plugin_query)
        tenant_plugin = result.scalar_one_or_none()

        if not tenant_plugin:
            # 安装插件
            plugin_query = select(Plugin).where(Plugin.id == plugin_id)
            result = await db.execute(plugin_query)
            plugin = result.scalar_one_or_none()

            if plugin:
                tenant_plugin = TenantPlugin(
                    id=uuid.uuid4(),
                    tenant_id=tenant_id,
                    plugin_id=plugin_id,
                    status="active",
                    version=plugin.version,
                    settings={}
                )
                db.add(tenant_plugin)
                await db.commit()
                logger.info(f"租户 {tenant_id} 安装钉钉机器人插件成功")

        # 检查项目插件设置是否已初始化
        settings_query = select(DingTalkSettings).where(
            and_(
                DingTalkSettings.tenant_id == tenant_id,
                DingTalkSettings.project_id == project_id
            )
        )
        result = await db.execute(settings_query)
        settings = result.scalar_one_or_none()

        if not settings:
            # 初始化插件设置
            settings = DingTalkSettings(
                id=uuid.uuid4(),
                tenant_id=tenant_id,
                project_id=project_id,
                enable_dingtalk=True,
                notification_level="all",
                retry_count=3,
                retry_interval=60,
                default_template="### {{title}}\n\n{{content}}\n\n> 来自系统通知 - {{time}}"
            )
            db.add(settings)
            await db.commit()
            logger.info(f"项目 {project_id} 初始化钉钉机器人插件设置成功")

        # 创建默认Webhook
        webhook_query = select(DingTalkWebhook).where(
            and_(
                DingTalkWebhook.tenant_id == tenant_id,
                DingTalkWebhook.project_id == project_id
            )
        )
        result = await db.execute(webhook_query)
        webhook = result.scalar_one_or_none()

        if not webhook:
            # 获取租户管理员 - 修复懒加载问题
            tenant_query = select(Tenant).options(selectinload(Tenant.users)).where(Tenant.id == tenant_id)
            result = await db.execute(tenant_query)
            tenant = result.scalar_one_or_none()

            admin_id = None
            if tenant:
                # 直接查询租户的用户
                user_query = select(User).where(User.tenant_id == tenant_id).limit(1)
                user_result = await db.execute(user_query)
                admin_user = user_result.scalar_one_or_none()
                if admin_user:
                    admin_id = admin_user.id

            if admin_id:
                webhook = DingTalkWebhook(
                    id=uuid.uuid4(),
                    tenant_id=tenant_id,
                    project_id=project_id,
                    name="默认机器人",
                    webhook_url="",  # 需要用户配置
                    enabled=False,  # 默认禁用，等待用户配置
                    message_template="### {{title}}\n\n{{content}}\n\n> 来自系统通知 - {{time}}",
                    notification_types=["all"],
                    created_by=admin_id
                )
                db.add(webhook)
                await db.commit()
                logger.info(f"项目 {project_id} 创建默认钉钉机器人Webhook成功")

    except Exception as e:
        logger.error(f"初始化租户插件失败: {str(e)}")
        await db.rollback()
        raise

async def install_plugin():
    """
    安装钉钉深度集成插件到系统
    """
    try:
        logger.info("开始安装钉钉深度集成插件...")
        await initialize_plugin()
        logger.info("钉钉深度集成插件安装成功！")
        return {
            "success": True,
            "message": "钉钉深度集成插件安装成功",
            "version": PLUGIN_INFO["version"],
            "features": PLUGIN_INFO["features"]
        }
    except Exception as e:
        logger.error(f"钉钉深度集成插件安装失败: {str(e)}")
        return {
            "success": False,
            "message": f"安装失败: {str(e)}"
        }


async def uninstall_plugin():
    """
    卸载钉钉深度集成插件
    """
    try:
        logger.info("开始卸载钉钉深度集成插件...")
        
        async for db in get_db():
            # 查找插件
            plugin_query = select(Plugin).where(Plugin.code == PLUGIN_INFO["code"])
            result = await db.execute(plugin_query)
            plugin = result.scalar_one_or_none()
            
            if plugin:
                # 删除所有租户的插件安装记录
                tenant_plugins_query = select(TenantPlugin).where(TenantPlugin.plugin_id == plugin.id)
                tenant_plugins_result = await db.execute(tenant_plugins_query)
                tenant_plugins = tenant_plugins_result.scalars().all()
                
                for tenant_plugin in tenant_plugins:
                    # 清理每个租户的插件数据
                    await cleanup_tenant_plugin_data(db, tenant_plugin.tenant_id, plugin.id)
                    await db.delete(tenant_plugin)
                
                # 删除插件记录
                await db.delete(plugin)
                await db.commit()
                
                logger.info(f"插件 {PLUGIN_INFO['name']} 卸载成功")
            else:
                logger.warning(f"插件 {PLUGIN_INFO['name']} 不存在")
            
            await db.close()
            
        return {
            "success": True,
            "message": "钉钉深度集成插件卸载成功，所有相关数据已清理"
        }
                
    except Exception as e:
        logger.error(f"钉钉深度集成插件卸载失败: {str(e)}")
        return {
            "success": False,
            "message": f"卸载失败: {str(e)}"
        }


async def cleanup_tenant_plugin_data(db: AsyncSession, tenant_id: uuid.UUID, plugin_id: uuid.UUID):
    """
    清理租户的钉钉深度集成插件数据
    
    Args:
        db: 数据库会话
        tenant_id: 租户ID
        plugin_id: 插件ID
    """
    try:
        from .models.models import (
            DingTalkSettings, DingTalkWebhook, DingTalkUserMapping, 
            DingTalkGroup, DingTalkNotificationLog, DingTalkDepartment,
            DingTalkTodoTask, DingTalkDocument, DingTalkSpace
        )
        
        # 删除钉钉设置
        settings_query = select(DingTalkSettings).where(DingTalkSettings.tenant_id == tenant_id)
        settings_result = await db.execute(settings_query)
        settings = settings_result.scalars().all()
        for setting in settings:
            await db.delete(setting)
        
        # 删除钉钉Webhook
        webhooks_query = select(DingTalkWebhook).where(DingTalkWebhook.tenant_id == tenant_id)
        webhooks_result = await db.execute(webhooks_query)
        webhooks = webhooks_result.scalars().all()
        for webhook in webhooks:
            await db.delete(webhook)
        
        # 删除用户映射
        user_mappings_query = select(DingTalkUserMapping).where(DingTalkUserMapping.tenant_id == tenant_id)
        user_mappings_result = await db.execute(user_mappings_query)
        user_mappings = user_mappings_result.scalars().all()
        for mapping in user_mappings:
            await db.delete(mapping)
        
        # 删除部门信息
        departments_query = select(DingTalkDepartment).where(DingTalkDepartment.tenant_id == tenant_id)
        departments_result = await db.execute(departments_query)
        departments = departments_result.scalars().all()
        for dept in departments:
            await db.delete(dept)
        
        # 删除待办任务
        tasks_query = select(DingTalkTodoTask).where(DingTalkTodoTask.tenant_id == tenant_id)
        tasks_result = await db.execute(tasks_query)
        tasks = tasks_result.scalars().all()
        for task in tasks:
            await db.delete(task)
        
        # 删除文档记录
        documents_query = select(DingTalkDocument).where(DingTalkDocument.tenant_id == tenant_id)
        documents_result = await db.execute(documents_query)
        documents = documents_result.scalars().all()
        for doc in documents:
            await db.delete(doc)
        
        # 删除知识库记录
        spaces_query = select(DingTalkSpace).where(DingTalkSpace.tenant_id == tenant_id)
        spaces_result = await db.execute(spaces_query)
        spaces = spaces_result.scalars().all()
        for space in spaces:
            await db.delete(space)
        
        # 删除群组信息
        groups_query = select(DingTalkGroup).where(DingTalkGroup.tenant_id == tenant_id)
        groups_result = await db.execute(groups_query)
        groups = groups_result.scalars().all()
        for group in groups:
            await db.delete(group)
        
        # 删除通知日志
        logs_query = select(DingTalkNotificationLog).where(DingTalkNotificationLog.tenant_id == tenant_id)
        logs_result = await db.execute(logs_query)
        logs = logs_result.scalars().all()
        for log in logs:
            await db.delete(log)
        
        await db.commit()
        logger.info(f"租户 {tenant_id} 的钉钉深度集成插件数据清理成功")
        
    except Exception as e:
        logger.error(f"清理租户 {tenant_id} 的钉钉深度集成插件数据失败: {str(e)}")
        await db.rollback()
        raise


if __name__ == "__main__":
    import sys
    if len(sys.argv) > 1:
        if sys.argv[1] == "install":
            asyncio.run(install_plugin())
        elif sys.argv[1] == "uninstall":
            asyncio.run(uninstall_plugin())
        else:
            print("Usage: python db_init.py [install|uninstall]")
    else:
        asyncio.run(initialize_plugin())
