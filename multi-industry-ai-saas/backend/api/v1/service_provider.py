#!/usr/bin/env python
# -*- coding: utf-8 -*-

import logging
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query, Path
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import update, delete
from pydantic import BaseModel, Field
import uuid
from datetime import datetime

from db.database import get_db
from models.core import ServiceProvider
from models.user import User
from core.auth import get_current_system_admin, get_current_service_provider_admin

# 配置日志
logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter()


# 服务商模型
class ServiceProviderBase(BaseModel):
    name: str
    company_name: str
    contact_person: str
    contact_email: str
    contact_phone: str
    commission_rate: Optional[int] = 15
    supported_industries: Optional[List[str]] = []
    branding: Optional[dict] = {}


class ServiceProviderCreate(ServiceProviderBase):
    pass


class ServiceProviderUpdate(BaseModel):
    name: Optional[str] = None
    company_name: Optional[str] = None
    contact_person: Optional[str] = None
    contact_email: Optional[str] = None
    contact_phone: Optional[str] = None
    status: Optional[str] = None
    commission_rate: Optional[int] = None
    supported_industries: Optional[List[str]] = None
    branding: Optional[dict] = None


class ServiceProviderResponse(ServiceProviderBase):
    id: uuid.UUID
    status: str
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True


@router.post("/service-providers", response_model=ServiceProviderResponse, status_code=status.HTTP_201_CREATED)
async def create_service_provider(
    service_provider: ServiceProviderCreate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_system_admin),
):
    """
    创建服务商（系统管理员权限）
    """
    try:
        # 创建服务商
        db_service_provider = ServiceProvider(
            id=uuid.uuid4(),
            name=service_provider.name,
            company_name=service_provider.company_name,
            contact_person=service_provider.contact_person,
            contact_email=service_provider.contact_email,
            contact_phone=service_provider.contact_phone,
            commission_rate=service_provider.commission_rate,
            supported_industries=service_provider.supported_industries,
            branding=service_provider.branding,
        )
        
        db.add(db_service_provider)
        await db.commit()
        await db.refresh(db_service_provider)
        
        logger.info(f"服务商创建成功: {db_service_provider.id}")
        return db_service_provider
    except Exception as e:
        await db.rollback()
        logger.error(f"创建服务商失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建服务商失败: {str(e)}",
        )


@router.get("/service-providers", response_model=List[ServiceProviderResponse])
async def get_service_providers(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    status: Optional[str] = Query(None),
    db: AsyncSession = Depends(get_db),
   # current_user: User = Depends(get_current_system_admin),
):
    """
    获取服务商列表（系统管理员权限）
    """
    try:
        # 构建查询
        query = select(ServiceProvider)
        
        # 应用过滤条件
        if status:
            query = query.where(ServiceProvider.status == status)
        
        # 应用分页
        query = query.offset(skip).limit(limit)
        
        # 执行查询
        result = await db.execute(query)
        service_providers = result.scalars().all()
        
        return service_providers
    except Exception as e:
        logger.error(f"获取服务商列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取服务商列表失败: {str(e)}",
        )


@router.get("/service-providers/{service_provider_id}", response_model=ServiceProviderResponse)
async def get_service_provider(
    service_provider_id: uuid.UUID = Path(...),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_service_provider_admin),
):
    """
    获取服务商详情（系统管理员或服务商管理员权限）
    """
    try:
        # 检查权限
        if not current_user.is_system_admin and current_user.service_provider_id != service_provider_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有权限访问此服务商",
            )
        
        # 查询服务商
        result = await db.execute(select(ServiceProvider).where(ServiceProvider.id == service_provider_id))
        service_provider = result.scalars().first()
        
        if not service_provider:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="服务商不存在",
            )
        
        return service_provider
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取服务商详情失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取服务商详情失败: {str(e)}",
        )


@router.put("/service-providers/{service_provider_id}", response_model=ServiceProviderResponse)
async def update_service_provider(
    service_provider_id: uuid.UUID = Path(...),
    service_provider_update: ServiceProviderUpdate = None,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_service_provider_admin),
):
    """
    更新服务商信息（系统管理员或服务商管理员权限）
    """
    try:
        # 检查权限
        if not current_user.is_system_admin and current_user.service_provider_id != service_provider_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有权限更新此服务商",
            )
        
        # 查询服务商
        result = await db.execute(select(ServiceProvider).where(ServiceProvider.id == service_provider_id))
        db_service_provider = result.scalars().first()
        
        if not db_service_provider:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="服务商不存在",
            )
        
        # 更新服务商信息
        update_data = service_provider_update.dict(exclude_unset=True)
        
        if update_data:
            await db.execute(
                update(ServiceProvider)
                .where(ServiceProvider.id == service_provider_id)
                .values(**update_data)
            )
            await db.commit()
        
        # 重新查询服务商
        result = await db.execute(select(ServiceProvider).where(ServiceProvider.id == service_provider_id))
        updated_service_provider = result.scalars().first()
        
        return updated_service_provider
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"更新服务商失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新服务商失败: {str(e)}",
        )


@router.delete("/service-providers/{service_provider_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_service_provider(
    service_provider_id: uuid.UUID = Path(...),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_system_admin),
):
    """
    删除服务商（系统管理员权限）
    """
    try:
        # 查询服务商
        result = await db.execute(select(ServiceProvider).where(ServiceProvider.id == service_provider_id))
        db_service_provider = result.scalars().first()
        
        if not db_service_provider:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="服务商不存在",
            )
        
        # 删除服务商
        await db.execute(delete(ServiceProvider).where(ServiceProvider.id == service_provider_id))
        await db.commit()
        
        logger.info(f"服务商删除成功: {service_provider_id}")
        return None
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"删除服务商失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除服务商失败: {str(e)}",
        )
