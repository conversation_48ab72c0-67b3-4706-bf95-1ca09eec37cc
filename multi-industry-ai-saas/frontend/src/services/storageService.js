/**
 * 存储服务
 * 处理文件上传、下载、共享等功能
 */

import apiService from './api';

const storageService = {
  // 获取存储使用情况
  getStorageUsage: async () => {
    try {
      return await apiService.getStorageUsage();
    } catch (error) {
      console.error('获取存储使用情况失败:', error);
      throw error;
    }
  },

  // 获取存储配额
  getStorageQuota: async () => {
    try {
      return await apiService.getStorageQuota();
    } catch (error) {
      console.error('获取存储配额失败:', error);
      throw error;
    }
  },

  // 上传文件
  uploadFile: async (file, folder = '', onProgress) => {
    try {
      const formData = new FormData();
      formData.append('file', file);
      if (folder) {
        formData.append('folder', folder);
      }

      return await apiService.uploadFile(formData, {
        onUploadProgress: (progressEvent) => {
          if (onProgress) {
            const percentCompleted = Math.round(
              (progressEvent.loaded * 100) / progressEvent.total
            );
            onProgress(percentCompleted);
          }
        },
      });
    } catch (error) {
      console.error('文件上传失败:', error);
      throw error;
    }
  },

  // 获取文件列表
  getFiles: async (params = {}) => {
    try {
      return await apiService.getFiles(params);
    } catch (error) {
      console.error('获取文件列表失败:', error);
      throw error;
    }
  },

  // 获取单个文件信息
  getFile: async (fileId) => {
    try {
      return await apiService.getFile(fileId);
    } catch (error) {
      console.error('获取文件信息失败:', error);
      throw error;
    }
  },

  // 删除文件
  deleteFile: async (fileId) => {
    try {
      return await apiService.deleteFile(fileId);
    } catch (error) {
      console.error('删除文件失败:', error);
      throw error;
    }
  },

  // 分享文件
  shareFile: async (fileId, shareOptions) => {
    try {
      return await apiService.shareFile(fileId, shareOptions);
    } catch (error) {
      console.error('分享文件失败:', error);
      throw error;
    }
  },

  // 获取共享文件
  getSharedFile: async (shareToken) => {
    try {
      return await apiService.getSharedFile(shareToken);
    } catch (error) {
      console.error('获取共享文件失败:', error);
      throw error;
    }
  },
};

export default storageService;
