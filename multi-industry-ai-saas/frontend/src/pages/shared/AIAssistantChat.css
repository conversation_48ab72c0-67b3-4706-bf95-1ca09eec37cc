/* AI助手分享页面样式 */
.ai-chat-layout {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.ai-chat-layout.dark {
  background: #141414;
}

/* 头部样式 */
.chat-header {
  background: linear-gradient(135deg, #1890ff 0%, #722ed1 100%);
  padding: 0 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 100;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
  max-width: 1200px;
  margin: 0 auto;
}

.assistant-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.assistant-details {
  display: flex;
  flex-direction: column;
}

.header-actions {
  display: flex;
  align-items: center;
}

/* 内容区域 */
.chat-content {
  flex: 1;
  padding: 0;
  overflow: hidden;
  background: transparent;
}

.messages-container {
  height: 100%;
  overflow-y: auto;
  padding: 16px;
  max-width: 1200px;
  margin: 0 auto;
  scroll-behavior: smooth;
}

/* 消息样式 */
.message {
  display: flex;
  margin-bottom: 16px;
  animation: fadeInUp 0.3s ease-out;
}

.message.user-message {
  flex-direction: row-reverse;
}

.message-avatar {
  flex-shrink: 0;
  margin: 0 8px;
}

.message-content {
  max-width: 70%;
  display: flex;
  flex-direction: column;
}

.user-message .message-content {
  align-items: flex-end;
}

.message-bubble {
  background: white;
  border-radius: 12px;
  padding: 12px 16px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  word-wrap: break-word;
  position: relative;
}

.user-message .message-bubble {
  background: #1890ff;
  color: white;
}

.dark .message-bubble {
  background: #262626;
  color: #fff;
}

.dark .user-message .message-bubble {
  background: #1890ff;
}

.message-bubble::before {
  content: '';
  position: absolute;
  top: 12px;
  width: 0;
  height: 0;
  border: 6px solid transparent;
}

.assistant-message .message-bubble::before {
  left: -12px;
  border-right-color: white;
}

.user-message .message-bubble::before {
  right: -12px;
  border-left-color: #1890ff;
}

.dark .assistant-message .message-bubble::before {
  border-right-color: #262626;
}

.message-text {
  line-height: 1.6;
}

.message-file {
  margin-bottom: 8px;
}

.file-info {
  display: flex;
  align-items: center;
  padding: 8px;
  background: rgba(0, 0, 0, 0.05);
  border-radius: 8px;
}

.message-time {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
  padding: 0 4px;
}

/* 输入正在输入指示器 */
.typing-indicator {
  margin-bottom: 16px;
}

.typing-dots {
  display: flex;
  gap: 4px;
  align-items: center;
}

.typing-dots span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #999;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-dots span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 底部输入区域 */
.chat-footer {
  background: white;
  border-top: 1px solid #f0f0f0;
  padding: 16px;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);
}

.dark .chat-footer {
  background: #1f1f1f;
  border-top-color: #303030;
}

.input-container {
  max-width: 1200px;
  margin: 0 auto;
}

.input-wrapper {
  display: flex;
  align-items: flex-end;
  gap: 8px;
  background: #fafafa;
  border-radius: 12px;
  padding: 8px;
  border: 1px solid #d9d9d9;
}

.dark .input-wrapper {
  background: #262626;
  border-color: #434343;
}

.input-wrapper .ant-input {
  border: none;
  background: transparent;
  box-shadow: none;
  resize: none;
  padding: 8px 12px;
}

.input-wrapper .ant-input:focus {
  border: none;
  box-shadow: none;
}

.input-actions {
  display: flex;
  align-items: center;
  gap: 4px;
  flex-shrink: 0;
}

/* 移动端适配 */
.ai-chat-layout.mobile .chat-header {
  padding: 0 12px;
}

.ai-chat-layout.mobile .messages-container {
  padding: 12px;
}

.ai-chat-layout.mobile .message-content {
  max-width: 85%;
}

.ai-chat-layout.mobile .chat-footer {
  padding: 12px;
}

.ai-chat-layout.mobile .input-wrapper {
  padding: 6px;
}

/* 平板适配 */
.ai-chat-layout.tablet .message-content {
  max-width: 75%;
}

/* 加载和错误状态 */
.loading-container,
.error-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100vh;
  text-align: center;
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 全屏模式 */
.ai-chat-layout:-webkit-full-screen {
  background: white;
}

.ai-chat-layout:-moz-full-screen {
  background: white;
}

.ai-chat-layout:fullscreen {
  background: white;
}

/* 滚动条样式 */
.messages-container::-webkit-scrollbar {
  width: 6px;
}

.messages-container::-webkit-scrollbar-track {
  background: transparent;
}

.messages-container::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.messages-container::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* 深色模式滚动条 */
.dark .messages-container::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
}

.dark .messages-container::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* 响应式断点 */
@media (max-width: 768px) {
  .assistant-details {
    display: none;
  }
  
  .header-actions .ant-btn {
    padding: 4px 8px;
  }
  
  .message-bubble {
    padding: 8px 12px;
    font-size: 14px;
  }
  
  .input-wrapper {
    border-radius: 8px;
  }
}

@media (max-width: 480px) {
  .chat-header {
    padding: 0 8px;
  }
  
  .messages-container {
    padding: 8px;
  }
  
  .chat-footer {
    padding: 8px;
  }
  
  .message {
    margin-bottom: 12px;
  }
  
  .message-content {
    max-width: 90%;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .message-bubble {
    border: 2px solid #000;
  }
  
  .user-message .message-bubble {
    border-color: #fff;
  }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
  .message {
    animation: none;
  }
  
  .messages-container {
    scroll-behavior: auto;
  }
  
  .typing-dots span {
    animation: none;
  }
} 