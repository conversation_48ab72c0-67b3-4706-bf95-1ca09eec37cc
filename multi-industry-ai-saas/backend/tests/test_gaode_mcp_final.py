#!/usr/bin/env python3
"""
高德MCP最终修复版本测试
在保持SSE连接活跃的同时发送MCP请求
"""
import asyncio
import httpx
import uuid
import json
import time

async def test_gaode_mcp_concurrent_requests():
    """在保持SSE连接活跃的同时并发发送MCP请求"""
    url = "https://mcp.amap.com/sse?key=94ed64a48ce623e65b18134befb65750"
    
    print("=== 高德MCP最终修复测试 ===")
    
    async with httpx.AsyncClient() as client:
        print("1. 建立SSE连接...")
        
        sse_headers = {
            "Accept": "text/event-stream",
            "Cache-Control": "no-cache",
            "Connection": "keep-alive"
        }
        
        try:
            # 使用流式连接保持SSE活跃
            async with client.stream('GET', url, headers=sse_headers, timeout=60) as sse_response:
                if sse_response.status_code != 200:
                    print(f"❌ SSE连接失败，状态码: {sse_response.status_code}")
                    return False
                
                print(f"✅ SSE连接成功，状态码: {sse_response.status_code}")
                
                message_endpoint = None
                session_id = None
                mcp_completed = False
                tools = []
                
                # 存储MCP响应
                mcp_responses = {}
                
                async def sse_listener():
                    """监听SSE事件并解析MCP响应"""
                    nonlocal message_endpoint, session_id, mcp_responses
                    
                    buffer = ""
                    try:
                        async for chunk in sse_response.aiter_text():
                            buffer += chunk
                            lines = buffer.split('\n')
                            buffer = lines[-1]
                            
                            for line in lines[:-1]:
                                line = line.strip()
                                if line:
                                    print(f"   SSE: {line}")
                                    
                                    # 解析端点信息
                                    if line.startswith("data:") and "/mcp/message" in line:
                                        endpoint_path = line[5:].strip()
                                        if endpoint_path.startswith("/mcp/message"):
                                            message_endpoint = f"https://mcp.amap.com{endpoint_path}"
                                            
                                            if "sessionId=" in endpoint_path:
                                                session_id = endpoint_path.split("sessionId=")[1].split("&")[0]
                                            
                                            print(f"✅ 端点: {message_endpoint}")
                                            print(f"   Session: {session_id}")
                                    
                                    # 解析MCP响应
                                    elif line.startswith("data:") and line != "data:" and "{" in line:
                                        try:
                                            response_text = line[5:].strip()
                                            response_data = json.loads(response_text)
                                            if "id" in response_data:
                                                request_id = response_data["id"]
                                                mcp_responses[request_id] = response_data
                                                print(f"   📦 MCP响应: {request_id}")
                                        except Exception as e:
                                            print(f"   ⚠️  解析响应失败: {e}")
                    except Exception as e:
                        print(f"   ❌ SSE监听异常: {e}")
                
                async def send_mcp_requests():
                    """在获取到端点后发送MCP请求"""
                    nonlocal mcp_completed, tools
                    
                    # 等待获取到端点
                    max_wait = 10
                    wait_time = 0
                    while not message_endpoint and wait_time < max_wait:
                        await asyncio.sleep(0.5)
                        wait_time += 0.5
                    
                    if not message_endpoint:
                        print("❌ 未能获取到消息端点")
                        return
                    
                    print("\n2. 开始MCP通信序列...")
                    
                    headers = {"Content-Type": "application/json"}
                    
                    try:
                        # 1. 初始化请求
                        init_request = {
                            "jsonrpc": "2.0",
                            "id": str(uuid.uuid4()),
                            "method": "initialize",
                            "params": {
                                "protocolVersion": "2024-11-05",
                                "capabilities": {"tools": {}, "resources": {}, "prompts": {}},
                                "clientInfo": {"name": "Concurrent Test", "version": "1.0.0"}
                            }
                        }
                        
                        print("   发送初始化请求...")
                        init_response = await client.post(
                            message_endpoint, 
                            json=init_request, 
                            headers=headers, 
                            timeout=15
                        )
                        
                        print(f"   初始化: {init_response.status_code}")
                        if init_response.text:
                            print(f"   响应: {init_response.text[:200]}...")
                        
                        # 2. initialized通知
                        print("   发送initialized通知...")
                        initialized_notification = {
                            "jsonrpc": "2.0",
                            "method": "notifications/initialized"
                        }
                        
                        notify_response = await client.post(
                            message_endpoint, 
                            json=initialized_notification, 
                            headers=headers, 
                            timeout=10
                        )
                        print(f"   通知: {notify_response.status_code}")
                        
                        # 等待处理
                        await asyncio.sleep(2)
                        
                        # 3. 获取工具列表  
                        print("   获取工具列表...")
                        tools_request = {
                            "jsonrpc": "2.0",
                            "id": str(uuid.uuid4()),
                            "method": "tools/list",
                            "params": {}
                        }
                        
                        tools_response = await client.post(
                            message_endpoint, 
                            json=tools_request, 
                            headers=headers, 
                            timeout=15
                        )
                        
                        print(f"   工具: {tools_response.status_code}")
                        if tools_response.status_code == 200:
                            try:
                                # 高德MCP使用SSE方式返回，HTTP响应可能为空
                                if tools_response.text:
                                    tools_data = tools_response.json()
                                    if "result" in tools_data and "tools" in tools_data["result"]:
                                        tools = tools_data["result"]["tools"]
                                        print(f"   🎉 HTTP获取到 {len(tools)} 个工具!")
                                    else:
                                        print(f"   📄 HTTP工具响应: {tools_response.text[:200]}...")
                                else:
                                    print("   📡 等待SSE返回工具列表...")
                                    # 等待SSE响应中的工具数据
                                    request_id = tools_request["id"]
                                    wait_time = 0
                                    max_wait = 10
                                    while wait_time < max_wait:
                                        if request_id in mcp_responses:
                                            response_data = mcp_responses[request_id]
                                            if "result" in response_data and "tools" in response_data["result"]:
                                                tools = response_data["result"]["tools"]
                                                print(f"   🎉 SSE获取到 {len(tools)} 个工具!")
                                            break
                                        await asyncio.sleep(0.5)
                                        wait_time += 0.5
                            except Exception as e:
                                print(f"   ❌ 解析工具失败: {e}")
                                print(f"   📄 原始响应: {tools_response.text[:200]}...")
                        
                        mcp_completed = True
                        print("   ✅ MCP通信序列完成")
                        
                    except Exception as e:
                        print(f"   ❌ MCP请求失败: {e}")
                        import traceback
                        traceback.print_exc()
                
                # 启动并发任务
                print("   启动并发任务...")
                
                sse_task = asyncio.create_task(sse_listener())
                mcp_task = asyncio.create_task(send_mcp_requests())
                
                # 等待MCP任务完成或超时
                try:
                    await asyncio.wait_for(mcp_task, timeout=30)
                except asyncio.TimeoutError:
                    print("❌ MCP任务超时")
                
                # 取消SSE任务
                sse_task.cancel()
                
                # 检查结果
                print(f"\n3. 最终结果:")
                print(f"   MCP完成: {mcp_completed}")
                print(f"   工具数量: {len(tools)}")
                print(f"   Session ID: {session_id}")
                
                if len(tools) > 0:
                    print(f"\n🎉 成功！获取到 {len(tools)} 个工具:")
                    for i, tool in enumerate(tools[:3], 1):
                        name = tool.get('name', '未知工具')
                        desc = tool.get('description', '无描述')
                        print(f"   {i}. {name}: {desc}")
                    return True
                else:
                    print("❌ 未获取到工具")
                    return False
                    
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            import traceback
            traceback.print_exc()
            return False

if __name__ == "__main__":
    result = asyncio.run(test_gaode_mcp_concurrent_requests())
    if result:
        print("\n🎉 高德MCP最终修复测试成功!")
    else:
        print("\n❌ 高德MCP最终修复测试失败!") 