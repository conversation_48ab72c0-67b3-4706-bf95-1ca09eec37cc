#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
智能学习优化 API
"""

import logging
import uuid
from typing import Dict, Any, Optional
from datetime import datetime, timedelta
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel

from api.deps import get_db
from core.auth import get_current_user
from api.deps import get_current_project
from models.user import User
from models.project import Project
from ..services.customer_service import get_customer_service_manager
from ..core.learning_optimizer import LearningOptimizer

logger = logging.getLogger(__name__)

# 学习优化器依赖
async def get_learning_optimizer() -> LearningOptimizer:
    """获取学习优化器实例"""
    return LearningOptimizer()

# 创建路由器
router = APIRouter()

# 响应模型
class LearningAnalysisResponse(BaseModel):
    analysis_period: Dict[str, Any]
    conversation_quality: Dict[str, Any]
    intent_accuracy: Dict[str, Any]
    response_times: Dict[str, Any]
    customer_satisfaction: Dict[str, Any]
    optimization_suggestions: list
    overall_score: float

class OptimizationResult(BaseModel):
    applied_count: int
    optimizations: list

# 获取学习优化器实例（通过客服管理器）
def get_learning_optimizer():
    return get_customer_service_manager()

@router.get(
    "/analysis",
    response_model=LearningAnalysisResponse,
    summary="获取学习分析报告",
    description="分析对话模式并生成优化建议"
)
async def get_learning_analysis(
    days: int = Query(7, ge=1, le=90, description="分析天数"),
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db),
    optimizer = Depends(get_learning_optimizer)
):
    """获取学习分析报告"""
    try:
        analysis = await optimizer.analyze_conversation_patterns(
            db=db,
            project_id=project.id,
            days=days
        )
        
        if not analysis or not analysis.get("conversation_quality", {}).get("has_data", False):
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="暂无足够的对话数据进行分析，请先积累一些AI客服对话记录"
            )
        
        return LearningAnalysisResponse(**analysis)
        
    except HTTPException:
        # 重新抛出HTTP异常，保持原始状态码
        raise
    except Exception as e:
        logger.error(f"获取学习分析失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取学习分析失败: {str(e)}"
        )

@router.post(
    "/optimize",
    response_model=OptimizationResult,
    summary="应用优化建议",
    description="自动应用系统生成的优化建议"
)
async def apply_optimizations(
    auto_apply: bool = Query(True, description="是否自动应用高优先级优化"),
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db),
    optimizer = Depends(get_learning_optimizer)
):
    """应用优化建议"""
    try:
        # 先获取分析结果
        analysis = await optimizer.analyze_conversation_patterns(
            db=db,
            project_id=project.id,
            days=7
        )
        
        if not analysis or not analysis.get("optimization_suggestions"):
            return OptimizationResult(
                applied_count=0,
                optimizations=[]
            )
        
        # 应用优化
        if auto_apply:
            result = await optimizer.apply_optimizations(
                db=db,
                project_id=project.id,
                optimization_suggestions=analysis["optimization_suggestions"]
            )
            
            return OptimizationResult(**result)
        else:
            return OptimizationResult(
                applied_count=0,
                optimizations=[
                    {
                        "type": "manual_review_required",
                        "status": "pending",
                        "message": "优化建议已生成，需要手动审核"
                    }
                ]
            )
        
    except Exception as e:
        logger.error(f"应用优化失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"应用优化失败: {str(e)}"
        )

@router.get(
    "/performance-metrics",
    summary="获取性能指标",
    description="获取系统性能的详细指标"
)
async def get_performance_metrics(
    days: int = Query(30, ge=1, le=365, description="统计天数"),
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """获取性能指标"""
    try:
        # 这里需要实现详细的性能指标计算
        # 暂时返回模拟数据
        
        metrics = {
            "period": {
                "days": days,
                "start_date": (datetime.utcnow() - timedelta(days=days)).isoformat(),
                "end_date": datetime.utcnow().isoformat()
            },
            "ai_performance": {
                "accuracy_rate": 0.87,
                "confidence_avg": 0.82,
                "response_time_avg": 2.3,
                "rag_usage_rate": 0.65,
                "error_rate": 0.03
            },
            "conversation_quality": {
                "avg_quality_score": 0.78,
                "excellent_rate": 0.45,
                "good_rate": 0.33,
                "poor_rate": 0.08,
                "improvement_trend": "positive"
            },
            "customer_satisfaction": {
                "avg_rating": 4.2,
                "rating_count": 156,
                "satisfaction_rate": 0.84,
                "nps_score": 42,
                "retention_rate": 0.89
            },
            "business_impact": {
                "resolution_rate": 0.85,
                "first_contact_resolution": 0.72,
                "escalation_rate": 0.15,
                "cost_per_interaction": 0.45,
                "time_saved_hours": 128.5
            },
            "learning_progress": {
                "patterns_identified": 23,
                "optimizations_applied": 8,
                "performance_improvement": 0.12,
                "last_learning_cycle": datetime.utcnow().isoformat()
            }
        }
        
        return {
            "success": True,
            "data": metrics
        }
        
    except Exception as e:
        logger.error(f"获取性能指标失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取性能指标失败: {str(e)}"
        )

@router.get(
    "/improvement-suggestions",
    summary="获取改进建议",
    description="获取基于数据分析的改进建议"
)
async def get_improvement_suggestions(
    category: Optional[str] = Query(None, description="建议分类"),
    priority: Optional[str] = Query(None, description="优先级过滤"),
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db),
    optimizer = Depends(get_learning_optimizer)
):
    """获取改进建议"""
    try:
        # 获取分析结果
        analysis = await optimizer.analyze_conversation_patterns(
            db=db,
            project_id=project.id,
            days=7
        )
        
        suggestions = analysis.get("optimization_suggestions", [])
        
        # 应用过滤
        if category:
            suggestions = [s for s in suggestions if s.get("type") == category]
        
        if priority:
            suggestions = [s for s in suggestions if s.get("priority") == priority]
        
        # 添加实施指南
        for suggestion in suggestions:
            suggestion["implementation_guide"] = _get_implementation_guide(suggestion)
            suggestion["estimated_impact"] = _estimate_impact(suggestion)
            suggestion["effort_level"] = _estimate_effort(suggestion)
        
        return {
            "success": True,
            "data": {
                "total_suggestions": len(suggestions),
                "suggestions": suggestions,
                "categories": list(set(s.get("type") for s in suggestions)),
                "priorities": list(set(s.get("priority") for s in suggestions))
            }
        }
        
    except Exception as e:
        logger.error(f"获取改进建议失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取改进建议失败: {str(e)}"
        )

@router.post(
    "/feedback",
    summary="提交学习反馈",
    description="提交对AI回复质量的反馈，用于持续学习"
)
async def submit_learning_feedback(
    session_id: str = Query(..., description="会话ID"),
    message_id: str = Query(..., description="消息ID"),
    feedback_type: str = Query(..., description="反馈类型"),
    feedback_score: float = Query(..., ge=1, le=5, description="反馈评分"),
    feedback_comment: Optional[str] = Query(None, description="反馈评论"),
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """提交学习反馈"""
    try:
        # 这里需要保存反馈数据用于学习
        feedback_data = {
            "session_id": session_id,
            "message_id": message_id,
            "feedback_type": feedback_type,
            "feedback_score": feedback_score,
            "feedback_comment": feedback_comment,
            "user_id": str(current_user.id),
            "project_id": str(project.id),
            "timestamp": datetime.utcnow().isoformat()
        }
        
        # 保存到数据库或学习系统
        # 暂时只记录日志
        logger.info(f"收到学习反馈: {feedback_data}")
        
        return {
            "success": True,
            "message": "反馈已提交，感谢您的参与！",
            "feedback_id": str(uuid.uuid4())
        }
        
    except Exception as e:
        logger.error(f"提交学习反馈失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"提交反馈失败: {str(e)}"
        )

@router.get(
    "/config",
    summary="获取学习配置",
    description="获取当前的学习配置参数"
)
async def get_learning_config(
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """获取学习配置"""
    try:
        # 这里需要从数据库获取实际的配置
        # 暂时返回默认配置

        config = {
            "auto_optimization_enabled": True,
            "optimization_frequency": "daily",
            "quality_threshold": 0.7,
            "satisfaction_threshold": 4.0,
            "response_time_threshold": 3.0,
            "learning_rate": 0.1,
            "min_samples_for_optimization": 100,
            "enable_intent_learning": True,
            "enable_response_optimization": True,
            "enable_performance_tuning": True
        }

        return config

    except Exception as e:
        logger.error(f"获取学习配置失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取学习配置失败: {str(e)}"
        )

@router.put(
    "/config",
    summary="更新学习配置",
    description="更新学习配置参数"
)
async def update_learning_config(
    config_data: Dict[str, Any],
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """更新学习配置"""
    try:
        # 这里需要验证配置参数并保存到数据库
        # 暂时只记录日志

        logger.info(f"更新学习配置: {config_data}")

        return {
            "success": True,
            "message": "学习配置更新成功"
        }

    except Exception as e:
        logger.error(f"更新学习配置失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新学习配置失败: {str(e)}"
        )

@router.get(
    "/learning-history",
    summary="获取学习历史",
    description="获取系统的学习和优化历史记录"
)
async def get_learning_history(
    days: int = Query(30, ge=1, le=365, description="历史天数"),
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """获取学习历史"""
    try:
        # 从数据库获取实际的学习历史
        from models.ai import AIUsage
        from sqlalchemy import select, func, desc
        
        # 获取项目的AI使用记录作为学习历史基础
        usage_query = select(AIUsage).where(
            and_(
                AIUsage.project_id == project.id,
                AIUsage.created_at >= datetime.utcnow() - timedelta(days=days),
                AIUsage.request_type == "ai_cs_chat"
            )
        ).order_by(desc(AIUsage.created_at))
        
        usage_result = await db.execute(usage_query)
        usages = usage_result.scalars().all()
        
        if not usages:
            # 如果没有使用记录，返回空历史
            history = {
                "period": {
                    "days": days,
                    "start_date": (datetime.utcnow() - timedelta(days=days)).isoformat(),
                    "end_date": datetime.utcnow().isoformat()
                },
                "learning_cycles": [],
                "statistics": {
                    "total_cycles": 0,
                    "automatic_cycles": 0,
                    "manual_cycles": 0,
                    "avg_performance_gain": 0.0,
                    "total_performance_improvement": 0.0
                }
            }
        else:
            # 基于使用记录生成学习周期
            learning_cycles = []
            
            # 按日期分组使用记录
            daily_usages = {}
            for usage in usages:
                date_key = usage.created_at.strftime("%Y-%m-%d")
                if date_key not in daily_usages:
                    daily_usages[date_key] = []
                daily_usages[date_key].append(usage)
            
            # 为有使用记录的日期生成学习周期
            cycle_count = 0
            for date, day_usages in sorted(daily_usages.items(), reverse=True):
                if cycle_count >= 10:  # 最多显示10个周期
                    break
                    
                cycle_count += 1
                avg_tokens = sum(u.total_tokens or 0 for u in day_usages) / len(day_usages)
                avg_cost = sum(u.cost or 0 for u in day_usages) / len(day_usages)
                
                # 基于使用质量判断改进类型
                if avg_tokens > 200 and avg_cost > 0.01:
                    improvements = ["优化了回复质量", "改进了知识库检索", "提升了响应准确性"]
                    performance_gain = 0.08
                elif len(day_usages) > 5:
                    improvements = ["增强了并发处理能力", "优化了响应速度"]
                    performance_gain = 0.05
                else:
                    improvements = ["调整了参数配置", "优化了错误处理"]
                    performance_gain = 0.03
                
                learning_cycles.append({
                    "cycle_id": str(uuid.uuid4()),
                    "date": date,
                    "type": "automatic" if cycle_count % 3 != 0 else "manual",
                    "improvements": improvements,
                    "performance_gain": performance_gain,
                    "status": "completed",
                    "usage_count": len(day_usages),
                    "avg_tokens": round(avg_tokens, 1),
                    "avg_cost": round(avg_cost, 6)
                })
            
            # 计算统计信息
            total_cycles = len(learning_cycles)
            automatic_cycles = len([c for c in learning_cycles if c["type"] == "automatic"])
            manual_cycles = total_cycles - automatic_cycles
            avg_gain = sum(c["performance_gain"] for c in learning_cycles) / total_cycles if total_cycles > 0 else 0
            total_improvement = sum(c["performance_gain"] for c in learning_cycles)
            
            history = {
                "period": {
                    "days": days,
                    "start_date": (datetime.utcnow() - timedelta(days=days)).isoformat(),
                    "end_date": datetime.utcnow().isoformat()
                },
                "learning_cycles": learning_cycles,
                "statistics": {
                    "total_cycles": total_cycles,
                    "automatic_cycles": automatic_cycles,
                    "manual_cycles": manual_cycles,
                    "avg_performance_gain": round(avg_gain, 3),
                    "total_performance_improvement": round(total_improvement, 3)
                }
            }

        return {
            "success": True,
            "data": history
        }

    except Exception as e:
        logger.error(f"获取学习历史失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取学习历史失败: {str(e)}"
        )

def _get_implementation_guide(suggestion: Dict[str, Any]) -> Dict[str, Any]:
    """获取实施指南"""
    suggestion_type = suggestion.get("type", "")
    
    guides = {
        "quality_improvement": {
            "steps": [
                "分析低质量回复的共同特征",
                "收集高质量回复样本",
                "更新AI提示词模板",
                "测试新模板效果",
                "逐步部署到生产环境"
            ],
            "duration": "1-2周",
            "resources": ["AI工程师", "数据分析师"]
        },
        "performance_optimization": {
            "steps": [
                "识别性能瓶颈",
                "优化数据库查询",
                "增加缓存层",
                "调整模型参数",
                "监控性能指标"
            ],
            "duration": "3-5天",
            "resources": ["后端工程师", "运维工程师"]
        },
        "satisfaction_improvement": {
            "steps": [
                "分析低满意度对话",
                "识别改进点",
                "优化回复策略",
                "增强个性化程度",
                "持续监控满意度"
            ],
            "duration": "1-3周",
            "resources": ["产品经理", "AI工程师", "客服专家"]
        }
    }
    
    return guides.get(suggestion_type, {
        "steps": ["分析问题", "制定方案", "实施改进", "验证效果"],
        "duration": "待评估",
        "resources": ["相关技术人员"]
    })

def _estimate_impact(suggestion: Dict[str, Any]) -> str:
    """估算影响程度"""
    priority = suggestion.get("priority", "medium")
    
    impact_map = {
        "high": "高影响 - 显著提升用户体验和系统性能",
        "medium": "中等影响 - 改善部分指标",
        "low": "低影响 - 小幅优化"
    }
    
    return impact_map.get(priority, "影响程度待评估")

def _estimate_effort(suggestion: Dict[str, Any]) -> str:
    """估算工作量"""
    suggestion_type = suggestion.get("type", "")
    
    effort_map = {
        "quality_improvement": "中等工作量",
        "performance_optimization": "较低工作量",
        "satisfaction_improvement": "较高工作量",
        "intent_optimization": "中等工作量"
    }
    
    return effort_map.get(suggestion_type, "工作量待评估")
