#!/usr/bin/env python
# -*- coding: utf-8 -*-

from typing import List, Optional, Union
from fastapi import APIRouter, Depends, HTTPException, Query, Path, Body, status
from sqlalchemy.ext.asyncio import AsyncSession
from datetime import datetime, date
import uuid
import logging

from db.database import get_db
from models.project import Project
from models.user import User
from models.marketing_activity import MarketingActivity
from schemas.marketing_activity import (
    MarketingActivityCreate,
    MarketingActivityUpdate,
    MarketingActivityStatusUpdate,
    MarketingActivityResponse,
    MarketingActivityListResponse,
    MarketingActivityStatsResponse
)
from services.marketing_activity import MarketingActivityService
from api.deps import get_current_user, get_current_project
from core.auth import get_current_active_user

router = APIRouter()
logger = logging.getLogger(__name__)

@router.get(
    "/marketing-activities",
    response_model=MarketingActivityListResponse,
    summary="获取营销活动列表",
    description="获取项目下的营销活动列表，支持分页、筛选和搜索"
)
async def get_marketing_activities(
    project_id: uuid.UUID = Path(..., description="项目ID"),
    skip: int = Query(0, description="跳过记录数"),
    limit: int = Query(10, description="返回记录数"),
    status: Optional[str] = Query(None, description="活动状态"),
    type: Optional[str] = Query(None, description="活动类型"),
    store_id: Optional[uuid.UUID] = Query(None, description="门店ID"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    start_date: Optional[date] = Query(None, description="开始日期"),
    end_date: Optional[date] = Query(None, description="结束日期"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    project: Project = Depends(get_current_project)
):
    """
    获取营销活动列表
    """

    # 获取营销活动列表
    activities, total = await MarketingActivityService.get_marketing_activities(
        db=db,
        project_id=project_id,
        skip=skip,
        limit=limit,
        status=status,
        type=type,
        store_id=store_id,
        search=search,
        start_date=start_date,
        end_date=end_date
    )

    # 计算总页数
    pages = (total + limit - 1) // limit if limit > 0 else 1

    return {
        "items": activities,
        "total": total,
        "page": skip // limit + 1 if limit > 0 else 1,
        "size": limit,
        "pages": pages
    }

@router.get(
    "/marketing-activities/{activity_id}",
    response_model=MarketingActivityResponse,
    summary="获取营销活动详情",
    description="获取项目下指定营销活动的详细信息"
)
async def get_marketing_activity(
    project_id: uuid.UUID = Path(..., description="项目ID"),
    activity_id: uuid.UUID = Path(..., description="活动ID"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    project: Project = Depends(get_current_project)
):
    """
    获取营销活动详情
    """

    # 获取营销活动详情
    activity = await MarketingActivityService.get_marketing_activity(
        db=db,
        project_id=project_id,
        activity_id=activity_id
    )

    if not activity:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="营销活动不存在"
        )

    return activity

@router.post(
    "/marketing-activities",
    response_model=MarketingActivityResponse,
    status_code=status.HTTP_201_CREATED,
    summary="创建营销活动",
    description="在项目下创建新的营销活动"
)
async def create_marketing_activity(
    project_id: uuid.UUID = Path(..., description="项目ID"),
    activity_data: MarketingActivityCreate = Body(..., description="活动数据"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    project: Project = Depends(get_current_project)
):
    """
    创建营销活动
    """

    # 创建营销活动
    activity = await MarketingActivityService.create_marketing_activity(
        db=db,
        activity_data=activity_data.dict(),
        project_id=project_id,
        user_id=current_user.id
    )

    # 获取完整的活动信息（包括关联数据）
    activity_data = await MarketingActivityService.get_marketing_activity(
        db=db,
        project_id=project_id,
        activity_id=activity.id
    )

    return activity_data

@router.put(
    "/marketing-activities/{activity_id}",
    response_model=MarketingActivityResponse,
    summary="更新营销活动",
    description="更新项目下指定营销活动的信息"
)
async def update_marketing_activity(
    project_id: uuid.UUID = Path(..., description="项目ID"),
    activity_id: uuid.UUID = Path(..., description="活动ID"),
    activity_data: MarketingActivityUpdate = Body(..., description="活动数据"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    project: Project = Depends(get_current_project)
):
    """
    更新营销活动
    """

    # 更新营销活动
    activity = await MarketingActivityService.update_marketing_activity(
        db=db,
        project_id=project_id,
        activity_id=activity_id,
        activity_data=activity_data.dict(exclude_unset=True),
        user_id=current_user.id
    )

    if not activity:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="营销活动不存在"
        )

    # 获取完整的活动信息（包括关联数据）
    activity_data = await MarketingActivityService.get_marketing_activity(
        db=db,
        project_id=project_id,
        activity_id=activity.id
    )

    return activity_data

@router.patch(
    "/marketing-activities/{activity_id}/status",
    response_model=MarketingActivityResponse,
    summary="更新营销活动状态",
    description="更新项目下指定营销活动的状态"
)
async def update_marketing_activity_status(
    project_id: uuid.UUID = Path(..., description="项目ID"),
    activity_id: uuid.UUID = Path(..., description="活动ID"),
    status_data: MarketingActivityStatusUpdate = Body(..., description="状态数据"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    project: Project = Depends(get_current_project)
):
    """
    更新营销活动状态
    """

    # 更新营销活动状态
    activity = await MarketingActivityService.update_marketing_activity_status(
        db=db,
        project_id=project_id,
        activity_id=activity_id,
        status=status_data.status,
        user_id=current_user.id
    )

    if not activity:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="营销活动不存在"
        )

    # 获取完整的活动信息（包括关联数据）
    activity_data = await MarketingActivityService.get_marketing_activity(
        db=db,
        project_id=project_id,
        activity_id=activity.id
    )

    return activity_data

@router.delete(
    "/marketing-activities/{activity_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="删除营销活动",
    description="删除项目下指定的营销活动"
)
async def delete_marketing_activity(
    project_id: uuid.UUID = Path(..., description="项目ID"),
    activity_id: uuid.UUID = Path(..., description="活动ID"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    project: Project = Depends(get_current_project)
):
    """
    删除营销活动
    """

    # 删除营销活动
    success = await MarketingActivityService.delete_marketing_activity(
        db=db,
        project_id=project_id,
        activity_id=activity_id
    )

    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="营销活动不存在"
        )

@router.get(
    "/marketing-activities-stats",
    response_model=MarketingActivityStatsResponse,
    summary="获取营销活动统计数据",
    description="获取项目下营销活动的统计数据"
)
async def get_marketing_activity_stats(
    project_id: uuid.UUID = Path(..., description="项目ID"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_active_user),
    project: Project = Depends(get_current_project)
):
    """
    获取营销活动统计数据
    """

    # 获取营销活动统计数据
    stats = await MarketingActivityService.get_marketing_activity_stats(
        db=db,
        project_id=project_id
    )

    return stats
