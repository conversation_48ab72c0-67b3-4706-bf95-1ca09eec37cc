import React, { useState, useEffect } from 'react';
import {
  Card, Table, Button, Select, DatePicker, Row, Col, Statistic,
  Tabs, Space, Form, Input, Modal, message, Spin, Empty, Tag, Tooltip,
  InputNumber, Upload
} from 'antd';
import {
  PlusOutlined, SyncOutlined, EditOutlined, DeleteOutlined,
  UploadOutlined, SearchOutlined, FileTextOutlined, PieChartOutlined
} from '@ant-design/icons';
import dayjs from 'dayjs';
import apiService from '../../../../services/api';
import { getProjectId } from '../../../../services/api/httpClient';
import './styles.css';

const { TabPane } = Tabs;
const { Option } = Select;
const { RangePicker } = DatePicker;
const { TextArea } = Input;

/**
 * 日常费用组件
 */
const DailyExpenses = ({ stores, currentStoreId, onStoreChange, loading: storeLoading }) => {
  const [activeTab, setActiveTab] = useState('list');
  const [loading, setLoading] = useState(false);
  const [expenseList, setExpenseList] = useState([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });
  const [filters, setFilters] = useState({
    expense_type: 'all',
    date_range: [dayjs().startOf('month'), dayjs()]
  });
  const [modalVisible, setModalVisible] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [currentExpense, setCurrentExpense] = useState(null);
  const [form] = Form.useForm();
  const [fileList, setFileList] = useState([]);
  const [uploadLoading, setUploadLoading] = useState(false);
  const [expenseStats, setExpenseStats] = useState({
    total: 0,
    misc: 0,
    material: 0,
    electricity: 0,
    internet: 0,
    other: 0
  });

  // 获取费用列表
  const fetchExpenseList = async (params = {}) => {
    if (!currentStoreId) {
      return;
    }

    setLoading(true);
    try {
      // 准备查询参数
      const queryParams = {
        ...params,
        store_id: currentStoreId
      };

      // 处理日期范围
      if (filters.date_range && filters.date_range.length === 2) {
        queryParams.start_date = filters.date_range[0].format('YYYY-MM-DD');
        queryParams.end_date = filters.date_range[1].format('YYYY-MM-DD');
      }

      // 处理费用类型
      if (filters.expense_type && filters.expense_type !== 'all') {
        queryParams.expense_type = filters.expense_type;
      }

      // 调用API获取费用列表
      const response = await apiService.project.store_operations.getExpenseList(queryParams);

      if (response && response.success) {
        setExpenseList(response.items || []);
        setPagination({
          ...pagination,
          current: params.page || pagination.current,
          total: response.total || 0
        });

        // 获取费用统计
        fetchExpenseStats();
      } else {
        message.error(response?.message || '获取费用列表失败');
      }
    } catch (error) {
      console.error('获取费用列表失败:', error);
      message.error('获取费用列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取费用统计
  const fetchExpenseStats = async () => {
    if (!currentStoreId) {
      return;
    }

    try {
      // 准备查询参数
      const queryParams = {
        store_id: currentStoreId
      };

      // 处理日期范围
      if (filters.date_range && filters.date_range.length === 2) {
        queryParams.start_date = filters.date_range[0].format('YYYY-MM-DD');
        queryParams.end_date = filters.date_range[1].format('YYYY-MM-DD');
      }

      // 调用API获取费用统计
      const response = await apiService.project.store_operations.getExpenseStatistics(queryParams);

      if (response && response.success && response.data) {
        const { total, by_type } = response.data;

        // 更新状态
        setExpenseStats({
          total: total || 0,
          misc: by_type?.misc || 0,
          material: by_type?.material || 0,
          electricity: by_type?.electricity || 0,
          internet: by_type?.internet || 0,
          other: by_type?.other || 0
        });
      }
    } catch (error) {
      console.error('获取费用统计失败:', error);
      // 不显示错误消息，避免干扰用户
    }
  };

  // 处理表格变化
  const handleTableChange = (pagination, filters, sorter) => {
    fetchExpenseList({
      page: pagination.current,
      pageSize: pagination.pageSize,
      ...filters
    });
  };

  // 处理筛选变化
  const handleFilterChange = (key, value) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);

    // 重置分页
    setPagination({
      ...pagination,
      current: 1
    });

    fetchExpenseList({
      page: 1,
      pageSize: pagination.pageSize,
      ...newFilters
    });
  };

  // 处理新增费用
  const handleAdd = () => {
    form.resetFields();
    setFileList([]);
    setCurrentExpense(null);
    setModalVisible(true);
  };

  // 处理编辑费用
  const handleEdit = (record) => {
    setCurrentExpense(record);
    form.setFieldsValue({
      expense_type: record.expense_type,
      expense_date: dayjs(record.expense_date),
      amount: record.amount,
      description: record.description
    });

    // 如果有凭据，设置文件列表
    if (record.has_receipt && record.receipt_url) {
      setFileList([
        {
          uid: '-1',
          name: '凭据.jpg',
          status: 'done',
          url: record.receipt_url
        }
      ]);
    } else {
      setFileList([]);
    }

    setModalVisible(true);
  };

  // 处理删除费用
  const handleDelete = (record) => {
    Modal.confirm({
      title: '删除费用',
      content: '确定要删除此费用记录吗？',
      onOk: async () => {
        try {
          setLoading(true);

          // 调用API删除费用
          const response = await apiService.project.store_operations.deleteExpense(record.id);

          if (response && response.success) {
            message.success('删除费用成功');
            fetchExpenseList();
          } else {
            message.error(response?.message || '删除费用失败');
          }
        } catch (error) {
          console.error('删除费用失败:', error);
          message.error('删除费用失败');
        } finally {
          setLoading(false);
        }
      }
    });
  };

  // 处理表单提交
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      // 处理文件上传
      let receiptUrl = null;
      let hasReceipt = false;

      if (fileList.length > 0) {
        // 如果是已上传的文件，直接使用URL
        if (fileList[0].url) {
          receiptUrl = fileList[0].url;
          hasReceipt = true;
        }
        // 否则需要上传文件
        else if (fileList[0].originFileObj) {
          const formData = new FormData();
          formData.append('file', fileList[0].originFileObj);

          // 上传文件
          const uploadResponse = await apiService.project.store_operations.uploadExpenseReceipt(formData);

          if (uploadResponse && uploadResponse.success && uploadResponse.data) {
            receiptUrl = uploadResponse.data.receipt_url;
            hasReceipt = true;
          } else {
            message.error(uploadResponse?.message || '上传凭据失败');
            setLoading(false);
            return;
          }
        }
      }

      // 准备提交数据
      const expenseData = {
        ...values,
        store_id: currentStoreId,
        expense_date: values.expense_date.format('YYYY-MM-DD'),
        receipt_url: receiptUrl,
        has_receipt: hasReceipt
      };

      let response;

      if (currentExpense) {
        // 编辑费用
        response = await apiService.project.store_operations.updateExpense(
          currentExpense.id,
          expenseData
        );
      } else {
        // 新增费用
        response = await apiService.project.store_operations.createExpense(
          expenseData
        );
      }

      if (response && response.success) {
        message.success(currentExpense ? '编辑费用成功' : '新增费用成功');
        setModalVisible(false);
        fetchExpenseList();
      } else {
        message.error(response?.message || '提交费用失败');
      }
    } catch (error) {
      console.error('提交费用失败:', error);
      message.error('提交费用失败');
    } finally {
      setLoading(false);
    }
  };

  // 处理文件上传
  const handleUpload = async (file) => {
    try {
      setUploadLoading(true);

      // 检查文件类型
      const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'application/pdf'];
      if (!allowedTypes.includes(file.type)) {
        message.error('不支持的文件类型，只允许上传图片或PDF文件');
        setUploadLoading(false);
        return false;
      }

      // 检查文件大小
      const fileSizeLimit = 2 * 1024 * 1024; // 2MB
      if (file.size > fileSizeLimit) {
        message.error('文件大小不能超过2MB');
        setUploadLoading(false);
        return false;
      }

      // 在实际上传时，我们会在表单提交时处理文件上传
      // 这里只是验证文件
      setUploadLoading(false);
      return false; // 阻止默认上传行为
    } catch (error) {
      console.error('上传凭据失败:', error);
      message.error('上传凭据失败');
      setUploadLoading(false);
      return false;
    }
  };

  // 初始化
  useEffect(() => {
    if (currentStoreId) {
      fetchExpenseList();
    }
  }, [currentStoreId]);

  // 表格列定义
  const columns = [
    {
      title: '费用编号',
      dataIndex: 'expense_number',
      key: 'expense_number',
    },
    {
      title: '费用类型',
      dataIndex: 'expense_type',
      key: 'expense_type',
      render: (text) => {
        const typeMap = {
          misc: '杂费',
          material: '物料费',
          electricity: '电费',
          internet: '网费',
          other: '其他'
        };
        return typeMap[text] || text;
      }
    },
    {
      title: '费用日期',
      dataIndex: 'expense_date',
      key: 'expense_date',
    },
    {
      title: '金额',
      dataIndex: 'amount',
      key: 'amount',
      render: (text) => `¥${text.toFixed(2)}`
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },
    {
      title: '凭据',
      dataIndex: 'has_receipt',
      key: 'has_receipt',
      render: (has_receipt, record) => {
        if (has_receipt && record.receipt_url) {
          return (
            <Button
              type="link"
              size="small"
              icon={<FileTextOutlined />}
              onClick={() => window.open(record.receipt_url)}
            >
              查看凭据
            </Button>
          );
        }
        return <Tag color="red">无凭据</Tag>;
      }
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record)}
          >
            编辑
          </Button>
          <Button
            type="link"
            size="small"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record)}
          >
            删除
          </Button>
        </Space>
      ),
    },
  ];

  // 渲染搜索表单
  const renderSearchForm = () => {
    return (
      <div style={{ marginBottom: 16 }}>
        <Row gutter={16}>
          <Col span={6}>
            <Select
              placeholder="选择门店"
              style={{ width: '100%' }}
              value={currentStoreId}
              onChange={onStoreChange}
              loading={storeLoading}
            >
              {stores.map(store => (
                <Option key={store.id} value={store.id}>{store.name}</Option>
              ))}
            </Select>
          </Col>
          <Col span={6}>
            <Select
              placeholder="费用类型"
              style={{ width: '100%' }}
              value={filters.expense_type}
              onChange={(value) => handleFilterChange('expense_type', value)}
            >
              <Option value="all">全部类型</Option>
              <Option value="misc">杂费</Option>
              <Option value="material">物料费</Option>
              <Option value="electricity">电费</Option>
              <Option value="internet">网费</Option>
              <Option value="other">其他</Option>
            </Select>
          </Col>
          <Col span={8}>
            <RangePicker
              value={filters.date_range}
              onChange={(dates) => handleFilterChange('date_range', dates)}
              style={{ width: '100%' }}
            />
          </Col>
          <Col span={4}>
            <Space>
              <Button
                type="primary"
                icon={<SearchOutlined />}
                onClick={() => fetchExpenseList()}
              >
                搜索
              </Button>
              <Button
                icon={<SyncOutlined />}
                onClick={() => fetchExpenseList()}
              >
                刷新
              </Button>
            </Space>
          </Col>
        </Row>
      </div>
    );
  };

  // 渲染费用统计
  const renderExpenseStats = () => {
    return (
      <div style={{ marginBottom: 16 }}>
        <Row gutter={16}>
          <Col span={4}>
            <Card>
              <Statistic
                title="总费用"
                value={expenseStats.total}
                precision={2}
                prefix="¥"
                valueStyle={{ color: '#cf1322' }}
              />
            </Card>
          </Col>
          <Col span={4}>
            <Card>
              <Statistic
                title="杂费"
                value={expenseStats.misc}
                precision={2}
                prefix="¥"
              />
            </Card>
          </Col>
          <Col span={4}>
            <Card>
              <Statistic
                title="物料费"
                value={expenseStats.material}
                precision={2}
                prefix="¥"
              />
            </Card>
          </Col>
          <Col span={4}>
            <Card>
              <Statistic
                title="电费"
                value={expenseStats.electricity}
                precision={2}
                prefix="¥"
              />
            </Card>
          </Col>
          <Col span={4}>
            <Card>
              <Statistic
                title="网费"
                value={expenseStats.internet}
                precision={2}
                prefix="¥"
              />
            </Card>
          </Col>
          <Col span={4}>
            <Card>
              <Statistic
                title="其他"
                value={expenseStats.other}
                precision={2}
                prefix="¥"
              />
            </Card>
          </Col>
        </Row>
      </div>
    );
  };

  return (
    <div className="daily-expenses">
      {renderSearchForm()}

      {renderExpenseStats()}

      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="费用列表" key="list">
          <div style={{ marginBottom: 16 }}>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAdd}
            >
              新增费用
            </Button>
          </div>

          <Table
            columns={columns}
            dataSource={expenseList}
            rowKey="id"
            pagination={{
              ...pagination,
              showSizeChanger: true,
              showTotal: (total) => `共 ${total} 条记录`
            }}
            onChange={handleTableChange}
            loading={loading}
          />
        </TabPane>
        <TabPane tab="费用统计" key="stats">
          <Card title="费用类型分布">
            <Empty description="暂无统计数据" />
          </Card>
        </TabPane>
      </Tabs>

      {/* 费用表单模态框 */}
      <Modal
        title={currentExpense ? '编辑费用' : '新增费用'}
        open={modalVisible}
        onOk={handleSubmit}
        onCancel={() => setModalVisible(false)}
        confirmLoading={loading}
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Form.Item
            name="expense_type"
            label="费用类型"
            rules={[{ required: true, message: '请选择费用类型' }]}
          >
            <Select placeholder="请选择费用类型">
              <Option value="misc">杂费</Option>
              <Option value="material">物料费</Option>
              <Option value="electricity">电费</Option>
              <Option value="internet">网费</Option>
              <Option value="other">其他</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="expense_date"
            label="费用日期"
            rules={[{ required: true, message: '请选择费用日期' }]}
          >
            <DatePicker style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            name="amount"
            label="金额"
            rules={[{ required: true, message: '请输入金额' }]}
          >
            <InputNumber
              style={{ width: '100%' }}
              min={0}
              precision={2}
              step={0.01}
              prefix="¥"
            />
          </Form.Item>

          <Form.Item
            name="description"
            label="描述"
            rules={[{ required: true, message: '请输入描述' }]}
          >
            <TextArea rows={4} />
          </Form.Item>

          <Form.Item
            label="费用凭据"
            extra="支持 jpg, png, pdf 格式，单个文件不超过 2MB"
          >
            <Upload
              name="receipt"
              listType="picture"
              fileList={fileList}
              beforeUpload={handleUpload}
              onChange={({ fileList }) => setFileList(fileList)}
              maxCount={1}
            >
              <Button icon={<UploadOutlined />} loading={uploadLoading}>
                上传凭据
              </Button>
            </Upload>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default DailyExpenses;
