import React, { useState, useEffect } from 'react';
import dayjs from 'dayjs';
import {
  Card,
  Form,
  Select,
  InputNumber,
  Button,
  Row,
  Col,
  Statistic,
  Divider,
  Table,
  message,
  Upload,
  Tabs,
  Modal,
  Progress,
  Space,
  Tag,
  Typography,
  DatePicker,
} from 'antd';
import {
    CalculatorOutlined,
    SaveOutlined,
    InboxOutlined,
    DownloadOutlined,
    UploadOutlined,
    CheckCircleOutlined,
    ExclamationCircleOutlined,
} from '@ant-design/icons';
import FileSpaceSelector from '../../../../components/FileSpaceSelector';

const { Option } = Select;
const { TabPane } = Tabs;
const { Dragger } = Upload;
const { Text } = Typography;

// Mock data for existing targets
const mockTargets = [
    {
        key: 'store-001',
        storeId: 'store-001',
        storeName: '总店',
        lastYearSales: 320000,
        growthRate: 15,
        monthlyTarget: 368000,
        dailyTarget: 12267,
    },
    {
        key: 'store-002',
        storeId: 'store-002',
        storeName: '滨江店',
        lastYearSales: 250000,
        growthRate: 20,
        monthlyTarget: 300000,
        dailyTarget: 10000,
    }
];

const StoreTargetManagement = ({ stores }) => {
  const [form] = Form.useForm();
  const [calculatedTarget, setCalculatedTarget] = useState(null);
  const [targets, setTargets] = useState(mockTargets);
  const [loading, setLoading] = useState(false);
  const [fileList, setFileList] = useState([]);
  const [uploading, setUploading] = useState(false);
  const [isSpaceModalVisible, setIsSpaceModalVisible] = useState(false);
  const [selectedMonth, setSelectedMonth] = useState(dayjs().format('YYYY-MM')); // 新增月份筛选状态
  const [progressData, setProgressData] = useState([]); // 真实进度数据

  // 获取真实进度数据
  const fetchProgressData = async () => {
    try {
      const [year, month] = selectedMonth.split('-');
      // 这里应该调用真实的API获取进度数据
      // const response = await apiService.project.storeOperations.getTargetProgress(year, month);

      // 暂时使用模拟数据，实际部署时需要替换为真实API调用
      const mockProgressData = targets.map(target => {
        const currentDate = dayjs(selectedMonth);
        const currentDay = currentDate.date();
        const daysInMonth = currentDate.daysInMonth();
        const timeProgress = (currentDay / daysInMonth) * 100;

        // 模拟实际销售额（实际应从API获取）
        const actualSales = target.monthlyTarget * (0.6 + Math.random() * 0.8);
        const targetProgress = (actualSales / target.monthlyTarget) * 100;

        return {
          storeId: target.storeId,
          storeName: target.storeName,
          monthlyTarget: target.monthlyTarget,
          actualSales: actualSales,
          targetProgress: targetProgress,
          timeProgress: timeProgress,
          isOnTrack: targetProgress >= timeProgress
        };
      });

      setProgressData(mockProgressData);
    } catch (error) {
      console.error('获取进度数据失败:', error);
      message.error('获取进度数据失败');
    }
  };

  // 监听月份变化，重新获取数据
  useEffect(() => {
    if (targets.length > 0) {
      fetchProgressData();
    }
  }, [selectedMonth, targets]);

  const handleCalculate = () => {
    form.validateFields(['lastYearSales', 'growthRate']).then(values => {
      const { lastYearSales, growthRate } = values;
      if(!lastYearSales || !growthRate) {
        message.error('请输入销售额和增长率');
        return;
      }
      const monthlyTarget = lastYearSales * (1 + growthRate / 100);
      const dailyTarget = monthlyTarget / 30; // Assume 30 days for simplicity
      setCalculatedTarget({
        monthlyTarget: monthlyTarget.toFixed(2),
        dailyTarget: dailyTarget.toFixed(2),
      });
    });
  };

  const handleSave = () => {
    form.validateFields().then(values => {
        if (!calculatedTarget) {
            message.error('请先计算目标');
            return;
        }

        // 验证必填字段
        if (!values.storeId) {
            message.error('请选择门店');
            return;
        }
        if (!values.lastYearSales) {
            message.error('请输入去年同期月销售额');
            return;
        }
        if (!values.growthRate) {
            message.error('请输入期望增长率');
            return;
        }

        setLoading(true);
        console.log('Saving target:', { ...values, ...calculatedTarget });
        // Mock API call
        setTimeout(() => {
            const newTarget = {
                key: values.storeId,
                storeId: values.storeId,
                storeName: stores.find(s => s.id === values.storeId)?.name || '未知门店',
                ...values,
                monthlyTarget: parseFloat(calculatedTarget.monthlyTarget),
                dailyTarget: parseFloat(calculatedTarget.dailyTarget),
            };

            const newTargets = targets.filter(t => t.storeId !== newTarget.storeId);
            newTargets.push(newTarget);

            setTargets(newTargets.sort((a,b) => a.storeName.localeCompare(b.storeName)));
            setLoading(false);
            message.success('目标保存成功');
            form.resetFields();
            setCalculatedTarget(null);
        }, 500);
    }).catch(errorInfo => {
        console.error('表单验证失败:', errorInfo);
        message.error('请检查表单填写是否完整');
    });
  };

  const handleUpload = async (options) => {
    const { file } = options;
    setUploading(true);
    
    try {
      //
      // 此处应调用apiService.project.storeOperations.uploadTargets(file)
      // 由于后端尚未完全实现，暂时模拟
      await new Promise(resolve => setTimeout(resolve, 1500));
      setFileList([]);
      message.success(`${file.name} 文件上传成功并处理完毕.`);
    } catch (error) {
      message.error(`${file.name} 文件上传失败.`);
    } finally {
      setUploading(false);
    }
  };

  const handleSpaceFileSelect = async (selectedFile) => {
    if (!selectedFile) {
      setIsSpaceModalVisible(false);
      return;
    }
    
    setIsSpaceModalVisible(false);
    message.info(`已选择文件: ${selectedFile.name}，开始处理...`);
    
    // 模拟从文件空间获取文件并上传
    setUploading(true);
    try {
      // 此处应调用apiService下载文件，然后调用上传接口
      await new Promise(resolve => setTimeout(resolve, 2000));
      message.success(`文件 ${selectedFile.name} 处理成功！`);
    } catch (error) {
      message.error(`处理文件 ${selectedFile.name} 失败。`);
    } finally {
      setUploading(false);
    }
  };

  const handleDownloadTemplate = () => {
    message.info('正在准备模板，请稍候...');
    // 此处应生成并下载一个CSV模板
  };

  const draggerProps = {
    name: 'file',
    multiple: false,
    fileList,
    beforeUpload: (file) => {
      // 阻止自动上传，手动处理
      handleUpload({ file });
      return false;
    },
    onChange: (info) => {
        setFileList([info.fileList[info.fileList.length - 1]]);
    },
    accept: ".csv,.xlsx,.xls",
  };
  
  const columns = [
      { title: '门店名称', dataIndex: 'storeName', key: 'storeName' },
      { title: '去年同期月销售 (元)', dataIndex: 'lastYearSales', key: 'lastYearSales', render: (val) => val.toLocaleString() },
      { title: '期望增长率 (%)', dataIndex: 'growthRate', key: 'growthRate' },
      { title: '月度目标 (元)', dataIndex: 'monthlyTarget', key: 'monthlyTarget', render: (val) => <span style={{color: '#1890ff', fontWeight: 'bold'}}>{val.toLocaleString()}</span> },
      { title: '日均目标 (元)', dataIndex: 'dailyTarget', key: 'dailyTarget', render: (val) => val.toLocaleString() },
      { title: '操作', key: 'action', render: (_, record) => (
          <Button type="link" onClick={() => {
              form.setFieldsValue(record);
              setCalculatedTarget(null);
          }}>编辑</Button>
      )},
  ];

  return (
    <div>
      <Row gutter={24}>
        <Col span={12}>
          <Card title="手动设置销售目标">
            <Form form={form} layout="vertical" onValuesChange={() => setCalculatedTarget(null)}>
              <Form.Item name="storeId" label="选择门店" rules={[{ required: true, message: '请选择门店' }]}>
                <Select placeholder="请选择门店" allowClear>
                    {(stores || []).map(store => (
                        <Option key={store.id} value={store.id}>{store.name}</Option>
                    ))}
                </Select>
              </Form.Item>
              <Form.Item name="lastYearSales" label="去年同期月销售额 (元)" rules={[{ required: true, message: '请输入销售额' }]}>
                <InputNumber style={{ width: '100%' }} placeholder="例如: 300000" min={0} />
              </Form.Item>
              <Form.Item name="growthRate" label="期望增长率 (%)" rules={[{ required: true, message: '请输入增长率' }]}>
                <InputNumber style={{ width: '100%' }} placeholder="例如: 15" min={0} />
              </Form.Item>
              <Form.Item>
                <Button type="dashed" icon={<CalculatorOutlined />} onClick={handleCalculate} block>
                  计算目标
                </Button>
              </Form.Item>

              {calculatedTarget && (
                <div>
                  <Divider>计算结果</Divider>
                  <Row gutter={16}>
                    <Col span={12}>
                        <Statistic title="月度销售目标" value={calculatedTarget.monthlyTarget} prefix="¥" />
                    </Col>
                    <Col span={12}>
                        <Statistic title="日均销售目标" value={calculatedTarget.dailyTarget} prefix="¥" />
                    </Col>
                  </Row>
                  <Divider />
                  <Button type="primary" icon={<SaveOutlined />} onClick={handleSave} loading={loading} block>
                    保存目标
                  </Button>
                </div>
              )}
            </Form>
          </Card>
        </Col>
        <Col span={12}>
            <Card title="智能上传销售目标">
                <Tabs defaultActiveKey="upload">
                    <TabPane tab="本地上传" key="upload">
                        <Dragger {...draggerProps} disabled={uploading}>
                            <p className="ant-upload-drag-icon">
                                <InboxOutlined />
                            </p>
                            <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
                            <p className="ant-upload-hint">
                                支持上传包含门店ID、去年同期销售额、期望增长率等信息的文件 (CSV, XLSX, XLS)。
                            </p>
                        </Dragger>
                    </TabPane>
                    <TabPane tab="从项目空间选择" key="2">
                        <div style={{ padding: '20px', textAlign: 'center' }}>
                            <Button 
                              type="primary" 
                              icon={<InboxOutlined />}
                              onClick={() => setIsSpaceModalVisible(true)}
                              loading={uploading}
                            >
                              选择项目文件
                            </Button>
                            <p style={{ marginTop: '10px', color: '#888' }}>从当前项目的共享空间选择已有的目标文件进行上传。</p>
                        </div>
                    </TabPane>
                </Tabs>
                <Divider />
                <Button 
                    icon={<DownloadOutlined />}
                    onClick={handleDownloadTemplate}
                    block
                >
                    下载模板
                </Button>
            </Card>
        </Col>
      </Row>
      <Row style={{ marginTop: 24 }}>
        <Col span={24}>
            <Card title="门店目标总览">
                <Table
                    columns={columns}
                    dataSource={targets}
                    pagination={{ pageSize: 5 }}
                    rowKey="key"
                    loading={loading}
                />
            </Card>
        </Col>
      </Row>

      {/* 目标进度与时间进度展示板块 */}
      <Row style={{ marginTop: 24 }}>
        <Col span={24}>
          <Card
            title="目标进度与时间进度分析"
            extra={
              <Space>
                <span>选择月份:</span>
                <DatePicker.MonthPicker
                  value={dayjs(selectedMonth)}
                  onChange={(date) => setSelectedMonth(date.format('YYYY-MM'))}
                  format="YYYY-MM"
                  allowClear={false}
                />
              </Space>
            }
          >
            <Row gutter={[24, 24]}>
              {progressData.map((progress, index) => {
                const progressDiff = progress.targetProgress - progress.timeProgress;

                return (
                  <Col xs={24} sm={12} lg={8} key={progress.storeId}>
                    <Card
                      size="small"
                      title={progress.storeName}
                      extra={
                        <Tag color={progress.isOnTrack ? 'green' : 'red'}>
                          {progress.isOnTrack ? '进度正常' : '需要关注'}
                        </Tag>
                      }
                    >
                      <Space direction="vertical" style={{ width: '100%' }} size="middle">
                        {/* 目标完成进度 */}
                        <div>
                          <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 8 }}>
                            <Text strong>目标完成进度</Text>
                            <Text strong style={{ color: progress.targetProgress >= 100 ? '#52c41a' : '#1890ff' }}>
                              {progress.targetProgress.toFixed(1)}%
                            </Text>
                          </div>
                          <Progress
                            percent={Math.min(progress.targetProgress, 100)}
                            status={progress.targetProgress >= 100 ? 'success' : 'active'}
                            strokeColor={progress.targetProgress >= 100 ? '#52c41a' : '#1890ff'}
                          />
                          <div style={{ marginTop: 4, fontSize: 12, color: '#666' }}>
                            实际销售: ¥{progress.actualSales.toLocaleString()} / 目标: ¥{progress.monthlyTarget.toLocaleString()}
                          </div>
                        </div>

                        {/* 时间进度 */}
                        <div>
                          <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 8 }}>
                            <Text strong>时间进度</Text>
                            <Text strong style={{ color: '#722ed1' }}>
                              {progress.timeProgress.toFixed(1)}%
                            </Text>
                          </div>
                          <Progress
                            percent={progress.timeProgress}
                            strokeColor="#722ed1"
                          />
                          <div style={{ marginTop: 4, fontSize: 12, color: '#666' }}>
                            {selectedMonth} 月份时间进度
                          </div>
                        </div>

                        {/* 进度对比分析 */}
                        <div style={{
                          padding: 12,
                          backgroundColor: progress.isOnTrack ? '#f6ffed' : '#fff2e8',
                          border: `1px solid ${progress.isOnTrack ? '#b7eb8f' : '#ffbb96'}`,
                          borderRadius: 6
                        }}>
                          <div style={{ display: 'flex', alignItems: 'center', marginBottom: 4 }}>
                            {progress.isOnTrack ? (
                              <CheckCircleOutlined style={{ color: '#52c41a', marginRight: 8 }} />
                            ) : (
                              <ExclamationCircleOutlined style={{ color: '#fa8c16', marginRight: 8 }} />
                            )}
                            <Text strong style={{ fontSize: 12 }}>
                              {progress.isOnTrack ? '进度超前' : '进度滞后'} {Math.abs(progressDiff).toFixed(1)}%
                            </Text>
                          </div>
                          <Text style={{ fontSize: 12, color: '#666' }}>
                            {progress.isOnTrack
                              ? `当前销售进度超前时间进度，预计能够完成月度目标`
                              : `当前销售进度落后时间进度，需要加强销售力度`
                            }
                          </Text>
                        </div>

                        {/* 预测完成度 */}
                        <div>
                          <Text strong style={{ fontSize: 12, color: '#666' }}>预测月度完成度: </Text>
                          <Text strong style={{
                            color: (progress.targetProgress / progress.timeProgress * 100) >= 100 ? '#52c41a' : '#f5222d',
                            fontSize: 12
                          }}>
                            {progress.timeProgress > 0 ? (progress.targetProgress / progress.timeProgress * 100).toFixed(1) : 0}%
                          </Text>
                        </div>
                      </Space>
                    </Card>
                  </Col>
                );
              })}
            </Row>

            {progressData.length === 0 && (
              <div style={{ textAlign: 'center', padding: '40px', color: '#999' }}>
                <Text type="secondary">暂无门店目标数据，请先设置门店销售目标</Text>
              </div>
            )}
          </Card>
        </Col>
      </Row>

      <FileSpaceSelector
        onFileSelect={handleSpaceFileSelect}
        accept={['.xlsx', '.xls', '.csv']}
        description="从项目空间选择销售目标文件"
        visible={isSpaceModalVisible}
        onCancel={() => setIsSpaceModalVisible(false)}
      />
    </div>
  );
};

export default StoreTargetManagement; 