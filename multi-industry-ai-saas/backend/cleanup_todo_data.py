#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
清理钉钉待办任务测试数据
"""

import asyncio
import os
import sys
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from db.database import AsyncSessionLocal
from sqlalchemy import text

async def cleanup_todo_data():
    """清理待办任务测试数据"""
    db = AsyncSessionLocal()
    try:
        print("🧹 开始清理待办任务测试数据...")
        
        # 清理待办任务数据
        result1 = await db.execute(text('DELETE FROM dingtalk_todo_tasks'))
        print(f"✅ 清理待办任务记录: {result1.rowcount} 条")
        
        # 清理相关事件日志
        result2 = await db.execute(text("DELETE FROM dingtalk_event_logs WHERE event_type LIKE '%todo%'"))
        print(f"✅ 清理事件日志记录: {result2.rowcount} 条")
        
        await db.commit()
        print("🎉 待办任务数据清理完成")
        
    except Exception as e:
        print(f"❌ 清理失败: {e}")
        await db.rollback()
    finally:
        await db.close()

if __name__ == "__main__":
    asyncio.run(cleanup_todo_data()) 