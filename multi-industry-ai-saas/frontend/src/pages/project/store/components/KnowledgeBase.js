import React, { useState, useEffect } from 'react';
import {
  Card,
  List,
  Tag,
  Typography,
  Space,
  Spin,
  Empty,
  Divider,
  Button,
  Modal,
  Row,
  Col,
  Input,
  Select,
  message,
  Tabs,
  Avatar
} from 'antd';
import {
  BookOutlined,
  FileTextOutlined,
  TagOutlined,
  UserOutlined,
  CalendarOutlined,
  SearchOutlined,
  SyncOutlined,
  DownloadOutlined,
  EyeOutlined
} from '@ant-design/icons';
import apiService from '../../../../services/api';
import dayjs from 'dayjs';

const { Title, Text, Paragraph } = Typography;
const { Search } = Input;
const { Option } = Select;
const { TabPane } = Tabs;

/**
 * 知识库组件
 * 显示门店可以查看的知识库内容
 */
const KnowledgeBase = () => {
  const [documents, setDocuments] = useState([]);
  const [loading, setLoading] = useState(false);
  const [detailVisible, setDetailVisible] = useState(false);
  const [currentDocument, setCurrentDocument] = useState(null);
  const [searchText, setSearchText] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('all');
  const [categories, setCategories] = useState([]);
  const [activeTab, setActiveTab] = useState('list');

  // 获取知识库文档列表
  useEffect(() => {
    fetchDocuments();
    fetchCategories();
  }, []);

  // 获取知识库文档
  const fetchDocuments = async () => {
    setLoading(true);
    try {
      const response = await apiService.project.knowledgeBase.getList();
      
      if (response && response.items) {
        setDocuments(response.items);
      } else {
        message.error('获取知识库文档失败');
      }
    } catch (error) {
      console.error('获取知识库文档失败:', error);
      message.error('获取知识库文档失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  // 获取知识库分类
  const fetchCategories = async () => {
    try {
      const response = await apiService.project.knowledgeBase.getCategories();
      
      if (response && response.items) {
        setCategories(response.items);
      }
    } catch (error) {
      console.error('获取知识库分类失败:', error);
    }
  };

  // 查看文档详情
  const viewDocumentDetail = (document) => {
    setCurrentDocument(document);
    setDetailVisible(true);
  };

  // 下载文档
  const downloadDocument = (document) => {
    if (document.file_url) {
      window.open(document.file_url, '_blank');
    } else {
      message.error('文档没有可下载的文件');
    }
  };

  // 筛选文档
  const filteredDocuments = documents.filter(document => {
    // 搜索文本筛选
    const matchesSearch = searchText ? 
      (document.title && document.title.toLowerCase().includes(searchText.toLowerCase())) || 
      (document.content && document.content.toLowerCase().includes(searchText.toLowerCase())) : 
      true;
    
    // 分类筛选
    const matchesCategory = categoryFilter === 'all' ? true : document.category_id === categoryFilter;
    
    return matchesSearch && matchesCategory;
  });

  // 渲染列表视图
  const renderListView = () => {
    return (
      <List
        itemLayout="vertical"
        dataSource={filteredDocuments}
        renderItem={document => (
          <List.Item
            key={document.id}
            actions={[
              <Button type="link" icon={<EyeOutlined />} onClick={() => viewDocumentDetail(document)}>查看详情</Button>,
              document.file_url && <Button type="link" icon={<DownloadOutlined />} onClick={() => downloadDocument(document)}>下载</Button>
            ]}
          >
            <List.Item.Meta
              avatar={<Avatar icon={<FileTextOutlined />} />}
              title={document.title}
              description={
                <Space>
                  {document.category_name && <Tag color="blue">{document.category_name}</Tag>}
                  <CalendarOutlined /> {dayjs(document.created_at).format('YYYY-MM-DD')}
                  {document.author && <><UserOutlined /> {document.author}</>}
                </Space>
              }
            />
            <div style={{ marginTop: 8 }}>
              <Paragraph ellipsis={{ rows: 2 }}>
                {document.content || '暂无内容描述'}
              </Paragraph>
            </div>
          </List.Item>
        )}
      />
    );
  };

  // 渲染卡片视图
  const renderCardView = () => {
    return (
      <List
        grid={{ gutter: 16, xs: 1, sm: 2, md: 3, lg: 4, xl: 4, xxl: 6 }}
        dataSource={filteredDocuments}
        renderItem={document => (
          <List.Item>
            <Card
              hoverable
              actions={[
                <Button type="link" icon={<EyeOutlined />} onClick={() => viewDocumentDetail(document)}>查看</Button>,
                document.file_url && <Button type="link" icon={<DownloadOutlined />} onClick={() => downloadDocument(document)}>下载</Button>
              ]}
            >
              <Card.Meta
                avatar={<Avatar icon={<FileTextOutlined />} />}
                title={document.title}
                description={
                  <Space direction="vertical" style={{ width: '100%' }}>
                    {document.category_name && <Tag color="blue">{document.category_name}</Tag>}
                    <Text type="secondary" ellipsis={{ tooltip: document.content }}>
                      {document.content || '暂无内容描述'}
                    </Text>
                    <div>
                      <CalendarOutlined /> {dayjs(document.created_at).format('YYYY-MM-DD')}
                    </div>
                  </Space>
                }
              />
            </Card>
          </List.Item>
        )}
      />
    );
  };

  return (
    <div className="knowledge-base-container">
      <div className="knowledge-base-header" style={{ marginBottom: 16 }}>
        <Row gutter={16} align="middle">
          <Col xs={24} sm={8} md={6} lg={6}>
            <Search
              placeholder="搜索文档标题或内容"
              value={searchText}
              onChange={e => setSearchText(e.target.value)}
              style={{ width: '100%' }}
            />
          </Col>
          <Col xs={12} sm={8} md={4} lg={4}>
            <Select
              placeholder="文档分类"
              style={{ width: '100%' }}
              value={categoryFilter}
              onChange={value => setCategoryFilter(value)}
            >
              <Option value="all">全部分类</Option>
              {categories.map(category => (
                <Option key={category.id} value={category.id}>{category.name}</Option>
              ))}
            </Select>
          </Col>
          <Col xs={12} sm={8} md={4} lg={4}>
            <Button 
              type="primary" 
              icon={<SyncOutlined />} 
              onClick={fetchDocuments}
            >
              刷新
            </Button>
          </Col>
        </Row>
      </div>

      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="列表视图" key="list">
          <Spin spinning={loading}>
            {filteredDocuments.length > 0 ? renderListView() : <Empty description="暂无知识库文档" />}
          </Spin>
        </TabPane>
        <TabPane tab="卡片视图" key="card">
          <Spin spinning={loading}>
            {filteredDocuments.length > 0 ? renderCardView() : <Empty description="暂无知识库文档" />}
          </Spin>
        </TabPane>
      </Tabs>

      {/* 文档详情弹窗 */}
      <Modal
        title="文档详情"
        open={detailVisible}
        onCancel={() => setDetailVisible(false)}
        footer={[
          currentDocument?.file_url && (
            <Button 
              key="download" 
              type="primary" 
              icon={<DownloadOutlined />} 
              onClick={() => downloadDocument(currentDocument)}
            >
              下载文档
            </Button>
          ),
          <Button key="close" onClick={() => setDetailVisible(false)}>
            关闭
          </Button>
        ]}
        width={700}
      >
        {currentDocument && (
          <div className="document-detail">
            <Title level={4}>{currentDocument.title}</Title>
            <div style={{ marginBottom: 16 }}>
              <Space wrap>
                {currentDocument.category_name && <Tag color="blue">{currentDocument.category_name}</Tag>}
                {currentDocument.tags && currentDocument.tags.map(tag => (
                  <Tag key={tag}>{tag}</Tag>
                ))}
              </Space>
            </div>
            <div style={{ marginBottom: 16, color: '#888' }}>
              <Space>
                {currentDocument.author && <span><UserOutlined /> 作者: {currentDocument.author}</span>}
                <span><CalendarOutlined /> 创建时间: {dayjs(currentDocument.created_at).format('YYYY-MM-DD')}</span>
              </Space>
            </div>
            <Divider />
            <div className="document-content" style={{ whiteSpace: 'pre-wrap' }}>
              {currentDocument.content || '暂无内容'}
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default KnowledgeBase;
