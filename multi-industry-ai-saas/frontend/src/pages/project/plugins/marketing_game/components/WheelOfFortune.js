import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  Button,
  message,
  Modal,
  Typography,
  List,
  Avatar,
  Tag,
  Divider,
  Input,
  Form,
  Switch,
  Result,
  Empty
} from 'antd';
import {
  TrophyOutlined,
  HistoryOutlined,
  UserOutlined,
  SoundOutlined,
  LoadingOutlined,
  GiftOutlined,
  SmileOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import marketingGameApi from '../service/api/marketingGame';
import './WheelOfFortune.css';

const { Title, Paragraph } = Typography;

// 默认奖品图标
const DEFAULT_PRIZE_ICONS = {
  none: 'https://img.icons8.com/color/96/000000/gift--v1.png',
  gift: 'https://img.icons8.com/color/96/000000/gift--v1.png',
  coupon: 'https://img.icons8.com/color/96/000000/coupon--v1.png',
  points: 'https://img.icons8.com/color/96/000000/prize--v1.png',
  product: 'https://img.icons8.com/color/96/000000/shopping-bag--v1.png',
  thanks: 'https://img.icons8.com/color/96/000000/sad--v1.png'
};



/**
 * 大转盘组件
 * 完全重写的新版本，支持多种主题风格
 */
const WheelOfFortune = ({ gameId }) => {
  // 状态管理
  const [loading, setLoading] = useState(true);
  const [game, setGame] = useState(null);
  const [prizes, setPrizes] = useState([]);
  const [spinning, setSpinning] = useState(false);
  const [winningPrize, setWinningPrize] = useState(null);
  const [showResult, setShowResult] = useState(false);
  const [remainingDraws, setRemainingDraws] = useState(0);
  const [soundEnabled, setSoundEnabled] = useState(true);
  const [showPrizes, setShowPrizes] = useState(false);
  const [showRecords, setShowRecords] = useState(false);
  const [showRules, setShowRules] = useState(false);
  const [records, setRecords] = useState([]);
  const [receiptVerified, setReceiptVerified] = useState(false);
  const [receiptCode, setReceiptCode] = useState('');
  const [receiptAmount, setReceiptAmount] = useState('');
  const [verifying, setVerifying] = useState(false);
  const [phoneVerified, setPhoneVerified] = useState(false);
  const [phone, setPhone] = useState('');
  const [verifyingPhone, setVerifyingPhone] = useState(false);
  const [showVerification, setShowVerification] = useState(false);
  const [showClaimPrize, setShowClaimPrize] = useState(false);
  const [claimPhone, setClaimPhone] = useState('');
  const [claimingPrize, setClaimingPrize] = useState(false);

  // 引用
  const wheelRef = useRef(null);
  const spinSound = useRef(null);
  const winSound = useRef(null);
  const loseSound = useRef(null);

  // 初始化音频
  useEffect(() => {
    try {
      spinSound.current = new Audio();
      winSound.current = new Audio();
      loseSound.current = new Audio();

      // 设置音频源 - 使用插件内的相对路径
      try {
        if (spinSound.current) {
          spinSound.current.src = '../assets/audio/wheel-spin.mp3';
          // 预加载音频
          spinSound.current.load();
        }
        if (winSound.current) {
          winSound.current.src = '../assets/audio/win.mp3';
          winSound.current.load();
        }
        if (loseSound.current) {
          loseSound.current.src = '../assets/audio/lose.mp3';
          loseSound.current.load();
        }
      } catch (error) {
        console.error('Error loading audio files:', error);
        // 音频加载失败不影响游戏主体功能
      }
    } catch (error) {
      console.error('Error initializing audio:', error);
    }
  }, []);

  // 主题样式配置
  const themeStyles = {
    classic: {
      colors: [
        '#FF9800', // 橙色
        '#2196F3', // 蓝色
        '#4CAF50', // 绿色
        '#E91E63', // 粉色
        '#673AB7', // 紫色
        '#00BCD4', // 青色
        '#FFC107', // 黄色
        '#607D8B'  // 蓝灰色
      ],
      borderColor: '#ff5722',
      shadowColor: '#ffeb3b',
      centerBgColor: 'white',
      fontColor: '#333',
      buttonBgColor: '#ff5722'
    },
    apple: {
      colors: ['#5AC8FA', '#34C759', '#FF9500', '#FF2D55', '#AF52DE', '#007AFF', '#5856D6', '#FF3B30'],
      borderColor: '#007AFF',
      shadowColor: '#5AC8FA',
      centerBgColor: 'white',
      fontColor: '#333',
      buttonBgColor: '#007AFF'
    },
    cartoon: {
      colors: ['#FF6B6B', '#4ECDC4', '#FFE66D', '#6699CC', '#FF9A76', '#88D8B0', '#FF6F91', '#C5D86D'],
      borderColor: '#FF6B6B',
      shadowColor: '#FFE66D',
      centerBgColor: 'white',
      fontColor: '#333',
      buttonBgColor: '#FF6B6B'
    },
    business: {
      colors: ['#2C3E50', '#3498DB', '#1ABC9C', '#E74C3C', '#9B59B6', '#F1C40F', '#16A085', '#C0392B'],
      borderColor: '#34495E',
      shadowColor: '#BDC3C7',
      centerBgColor: '#ECF0F1',
      fontColor: '#2C3E50',
      buttonBgColor: '#3498DB'
    },
    ghibli: {
      colors: ['#1A7A89', '#D4A017', '#7CAE7A', '#E86A92', '#7C90A0', '#FFB067', '#9B8BB4', '#5B8E7D'],
      borderColor: '#1A7A89',
      shadowColor: '#D4A017',
      centerBgColor: 'white',
      fontColor: '#333',
      buttonBgColor: '#1A7A89'
    }
  };

  // 获取当前主题样式
  const getCurrentTheme = (themeStyle = 'classic') => {
    return themeStyles[themeStyle] || themeStyles.classic;
  };

  // 将fetchGameData提升到组件作用域
  const fetchGameData = useCallback(async () => {
    try {
      setLoading(true);

      // 检查是否有项目ID，如果没有则使用公开API
      const projectId = localStorage.getItem('project_id');
      const isPublicAccess = !projectId;

      let gameResponse, prizesResponse;

      if (isPublicAccess) {
        // 外部分享页面，使用公开API
        gameResponse = await marketingGameApi.getPublicGameDetail(gameId);
        prizesResponse = await marketingGameApi.getPublicGamePrizes(gameId);
      } else {
        // 内部用户，使用普通API
        gameResponse = await marketingGameApi.getGameDetail(gameId);
        prizesResponse = await marketingGameApi.getPrizes(gameId);
      }

      if (!gameResponse.success) {
        message.error(gameResponse.message || '获取游戏详情失败');
        setLoading(false);
        return;
      }

      // 确保game.remaining_draws有默认值
      const gameData = {
        ...gameResponse.data,
        remaining_draws: gameResponse.data.remaining_draws !== undefined ? gameResponse.data.remaining_draws :
                         (gameResponse.data.max_draws_per_user !== undefined ? gameResponse.data.max_draws_per_user : 0),
        // 添加默认配置
        settings: {
          ...gameResponse.data.settings,
          enable_receipt_verification:
            gameResponse.data.settings?.enable_receipt_verification !== undefined
              ? gameResponse.data.settings.enable_receipt_verification
              : gameResponse.data.min_consumption > 0,
          theme_style: gameResponse.data.settings?.theme_style || 'classic'
        }
      };

      console.log("Game settings:", gameData.settings);
      console.log("Public access mode:", isPublicAccess);

      // 检查是否需要验证
      if (gameData.settings) {
        if (gameData.settings.enable_phone_verification === true) {
          console.log("Phone verification enabled");
        }
        if (gameData.settings.enable_receipt_verification === true) {
          console.log("Receipt verification enabled");
        }
      }

      setGame(gameData);
      setRemainingDraws(gameData.remaining_draws);

      // 获取奖品列表
      if (!prizesResponse.success) {
        message.error(prizesResponse.message || '获取奖品列表失败');
        setLoading(false);
        return;
      }

      // 获取原始奖品列表
      let originalPrizes = prizesResponse.data || [];
      console.log("原始奖品列表:", originalPrizes);

      // 过滤掉重复的奖品（可能由于之前的bug导致）
      const uniquePrizes = [];
      const seenIds = new Set();

      for (const prize of originalPrizes) {
        // 如果是默认奖品但ID不同，只保留一个
        if (prize.is_default) {
          if (!seenIds.has('default')) {
            uniquePrizes.push({
              ...prize,
              id: 'default' // 统一默认奖品ID
            });
            seenIds.add('default');
          }
        } else if (!seenIds.has(prize.id)) {
          uniquePrizes.push(prize);
          seenIds.add(prize.id);
        }
      }

      originalPrizes = uniquePrizes;
      console.log("去重后奖品列表:", originalPrizes);

      // 检查是否已经有默认奖品（谢谢参与）
      const hasDefaultPrize = originalPrizes.some(prize => prize.is_default);

      // 如果没有默认奖品，添加一个
      if (!hasDefaultPrize) {
        const defaultPrize = {
          id: 'default',
          name: '谢谢参与',
          image_url: DEFAULT_PRIZE_ICONS.thanks,
          probability: 0,
          is_default: true
        };
        originalPrizes.push(defaultPrize);
      }

      // 使用原始奖品列表，根据概率分配扇形大小
      // 计算总概率，确保概率总和为100%
      const totalProbability = originalPrizes.reduce((sum, prize) => sum + (prize.probability || 0), 0);

      // 如果总概率为0，则平均分配
      const normalizedPrizes = originalPrizes.map(prize => {
        const normalizedProbability = totalProbability > 0
          ? prize.probability
          : 100 / originalPrizes.length;

        return {
          ...prize,
          normalizedProbability
        };
      });

      // 确保至少有一个奖品
      if (normalizedPrizes.length === 0) {
        normalizedPrizes.push({
          id: 'default',
          name: '谢谢参与',
          image_url: DEFAULT_PRIZE_ICONS.thanks,
          probability: 100,
          normalizedProbability: 100,
          is_default: true
        });
      }

      // 最终奖品列表就是归一化后的原始奖品列表
      let finalPrizes = normalizedPrizes;

      console.log("使用原始奖品列表，根据概率分配扇形大小:", finalPrizes);

      console.log("最终奖品列表:", finalPrizes);
      setPrizes(finalPrizes);

      // 获取中奖记录 - 限制最新的5条记录
      const recordsResponse = await marketingGameApi.getDrawRecords(gameId, { limit: 10 });
      if (recordsResponse.success) {
        // 确保按时间倒序排列，最新的记录在前面
        let allRecords = recordsResponse.data || [];

        // 确保记录是数组
        if (!Array.isArray(allRecords)) {
          allRecords = allRecords.items || allRecords.results || [];
        }

        console.log("原始中奖记录数据:", allRecords);

        // 先获取完整的奖品列表，确保有最新数据
        let currentPrizes = [...prizes];
        try {
          const prizesResponse = await marketingGameApi.getPrizes(gameId);
          if (prizesResponse.success) {
            currentPrizes = prizesResponse.data || [];
            // 确保奖品列表是数组
            if (!Array.isArray(currentPrizes)) {
              currentPrizes = currentPrizes.items || currentPrizes.results || [];
            }
            console.log("获取到的最新奖品列表:", currentPrizes);
          }
        } catch (error) {
          console.error("获取奖品列表失败:", error);
        }

        // 确保每条记录都有必要的字段
        const processedRecords = allRecords.map(record => {
          // 处理参与者信息
          let participantInfo = record.participant || {};
          if (!participantInfo.name) {
            participantInfo.name = record.participant_name || record.user_name || '匿名用户';
          }
          if (!participantInfo.phone) {
            participantInfo.phone = record.phone || record.user_phone || '';

            // 如果participant_id是手机号格式，也可以用作phone
            if (!participantInfo.phone && record.participant_id && /^1[3-9]\d{9}$/.test(record.participant_id)) {
              participantInfo.phone = record.participant_id;
            }
          }

          // 处理奖品信息
          let prizeInfo = record.prize || {};

          // 如果有prize_id但没有完整的prize信息，尝试从最新的奖品列表中获取
          if (record.prize_id && (!prizeInfo.name || prizeInfo.name === '未知奖品')) {
            // 尝试多种匹配方式，确保能找到奖品
            let matchingPrize = currentPrizes.find(p => p.id === record.prize_id);

            // 如果没找到，尝试字符串比较
            if (!matchingPrize) {
              matchingPrize = currentPrizes.find(p => String(p.id) === String(record.prize_id));
            }

            // 如果还没找到，尝试数字比较
            if (!matchingPrize) {
              matchingPrize = currentPrizes.find(p => Number(p.id) === Number(record.prize_id));
            }

            // 如果在最新列表中找到了匹配的奖品
            if (matchingPrize) {
              console.log("从最新奖品列表中找到匹配的奖品:", matchingPrize);
              prizeInfo = { ...matchingPrize };
            } else {
              console.log("在最新奖品列表中未找到匹配的奖品，尝试从本地奖品列表中查找");

              // 尝试从本地奖品列表中查找
              matchingPrize = prizes.find(p => p.id === record.prize_id ||
                                          String(p.id) === String(record.prize_id) ||
                                          Number(p.id) === Number(record.prize_id));

              if (matchingPrize) {
                console.log("从本地奖品列表中找到匹配的奖品:", matchingPrize);
                prizeInfo = { ...matchingPrize };
              } else {
                console.log("未找到匹配的奖品，prize_id:", record.prize_id);
              }
            }
          }

          // 确保奖品信息完整
          if (!prizeInfo.name) {
            prizeInfo.name = record.prize_name || (record.prize_id ? `奖品#${record.prize_id}` : '未知奖品');
          }
          if (prizeInfo.is_default === undefined) {
            prizeInfo.is_default = record.is_default || false;
          }
          if (!prizeInfo.image_url) {
            prizeInfo.image_url = record.prize_image || DEFAULT_PRIZE_ICONS[prizeInfo.is_default ? 'thanks' : 'gift'];
          }
          if (prizeInfo.value === undefined) {
            prizeInfo.value = record.prize_value || 0;
          }
          if (!prizeInfo.description) {
            prizeInfo.description = record.prize_description || '';
          }
          if (!prizeInfo.level) {
            prizeInfo.level = record.prize_level || '';
          }

          // 构建完整的记录对象
          const processedRecord = {
            ...record,
            id: record.id || `record_${Math.random()}`,
            created_at: record.created_at || record.draw_time || new Date().toISOString(),
            participant: participantInfo,
            prize: prizeInfo,
            prize_name: prizeInfo.name, // 添加直接的奖品名称字段
            prize_description: prizeInfo.description, // 添加直接的奖品描述字段
            prize_value: prizeInfo.value, // 添加直接的奖品价值字段
            prize_image: prizeInfo.image_url, // 添加直接的奖品图片字段
            prize_level: prizeInfo.level, // 添加直接的奖品等级字段
            // 确保is_win字段存在
            is_win: record.is_win === true ||
                   record.is_winner === true ||
                   record.is_win === 'true' ||
                   record.is_winner === 'true' ||
                   record.status === 'win' ||
                   (prizeInfo && !prizeInfo.is_default),
            // 确保有领取状态
            claim_status: record.claim_status || 'unclaimed',
            claim_time: record.claim_time || null
          };

          console.log("处理后的记录:", processedRecord);
          return processedRecord;
        });

        // 按时间倒序排序并只取前5条
        const sortedRecords = processedRecords
          // 只保留中奖记录
          .filter(record =>
            record.is_win === true ||
            record.is_winner === true ||
            record.is_win === 'true' ||
            record.is_winner === 'true' ||
            record.status === 'win' ||
            (record.prize && !record.prize.is_default)
          )
          // 按时间倒序排序
          .sort((a, b) => new Date(b.created_at || b.draw_time || 0) - new Date(a.created_at || a.draw_time || 0))
          // 只取前5条
          .slice(0, 5);

        console.log("最终排序后的中奖记录:", sortedRecords);
        setRecords(sortedRecords);
      }

      setLoading(false);
    } catch (error) {
      console.error('Error fetching game data:', error);
      message.error('获取游戏数据失败，请稍后再试');
      setLoading(false);
    }
  }, [gameId]);

  // 初始化
  useEffect(() => {
    fetchGameData();
  }, [fetchGameData]);

  // 调整颜色亮度
  const adjustColor = (color, amount) => {
    return '#' + color.replace(/^#/, '').replace(/../g, color => ('0' + Math.min(255, Math.max(0, parseInt(color, 16) + amount)).toString(16)).slice(-2));
  };

  // 判断颜色是否为浅色
  const isLightColor = (color) => {
    const hex = color.replace('#', '');
    const r = parseInt(hex.substr(0, 2), 16);
    const g = parseInt(hex.substr(2, 2), 16);
    const b = parseInt(hex.substr(4, 2), 16);
    const brightness = (r * 299 + g * 587 + b * 114) / 1000;
    return brightness > 155;
  };

  // 格式化为中国时间（东八区）
  const formatToChineseTime = (timeString) => {
    if (!timeString) return '未知时间';

    try {
      // 创建日期对象
      const date = new Date(timeString);

      // 检查日期是否有效
      if (isNaN(date.getTime())) return '无效时间';

      // 使用本地时区格式化（浏览器会自动处理时区）
      // 格式化为YYYY-MM-DD HH:MM:SS格式
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      const hours = String(date.getHours()).padStart(2, '0');
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');

      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    } catch (error) {
      console.error('格式化时间错误:', error, timeString);
      return '时间格式错误';
    }
  };



  // 处理抽奖
  const handleDraw = async () => {
    // 检查是否需要验证
    if (
      (game?.settings?.enable_receipt_verification === true && !receiptVerified) ||
      (game?.settings?.enable_phone_verification === true && !phoneVerified)
    ) {
      setShowVerification(true);
      return;
    }

    try {
      setSpinning(true);
      if (soundEnabled && spinSound.current) {
        try {
          spinSound.current.play().catch(err => console.error('Error playing spin sound:', err));
        } catch (error) {
          console.error('Error playing spin sound:', error);
        }
      }

      // 检查是否有项目ID，决定使用哪个API
      const projectId = localStorage.getItem('project_id');
      const isPublicAccess = !projectId;

      let response;

      if (isPublicAccess) {
        // 外部分享页面，使用公开抽奖API
        const participantData = {
          phone: phone || localStorage.getItem('user_phone') || '',
          name: localStorage.getItem('user_name') || '匿名用户',
          receipt_code: receiptCode || localStorage.getItem('receipt_code') || '',
          receipt_amount: parseFloat(receiptAmount || localStorage.getItem('receipt_amount') || '0')
        };
        
        console.log("使用公开抽奖API，参与者数据:", participantData);
        response = await marketingGameApi.publicDraw(gameId, participantData);
      } else {
        // 内部用户，使用普通抽奖API
        console.log("使用内部抽奖API");
        response = await marketingGameApi.draw(gameId);
      }

      if (!response.success) {
        message.error(response.message || '抽奖失败');
        setSpinning(false);
        return;
      }

      // 确保有奖品数据
      if (!response.data) {
        message.error('抽奖结果异常，请重试');
        setSpinning(false);
        return;
      }

      console.log("抽奖返回数据:", response.data);

      // 获取中奖奖品ID
      let prizeId = response.data.prize_id ||
                   (response.data.prize && response.data.prize.id) ||
                   null;

      // 如果没有prize_id，但有id，可能整个response就是prize对象
      if (!prizeId && response.data.id) {
        prizeId = response.data.id;
      }

      console.log("提取的奖品ID:", prizeId);

      // 如果还是没有找到奖品ID，使用第一个奖品
      if (!prizeId && prizes.length > 0) {
        prizeId = prizes[0].id;
        console.log("使用默认奖品ID:", prizeId);
      }

      if (!prizeId) {
        message.error('抽奖结果异常，无法确定中奖奖品');
        setSpinning(false);
        return;
      }

      // 根据返回的prize_id找到对应的奖品
      let winPrize = prizes.find(p => p.id.toString() === prizeId.toString());

      // 如果找不到对应奖品，尝试重新获取奖品列表
      if (!winPrize) {
        console.log("未找到对应奖品，尝试重新获取奖品列表");
        try {
          const prizesResponse = await marketingGameApi.getPrizes(gameId);
          if (prizesResponse.success) {
            const freshPrizes = prizesResponse.data || [];
            winPrize = freshPrizes.find(p => p.id.toString() === prizeId.toString());

            // 如果还是找不到，使用默认奖品或第一个奖品
            if (!winPrize) {
              winPrize = freshPrizes.find(p => p.is_default) || freshPrizes[0] || {
                id: prizeId,
                name: '未知奖品',
                description: '',
                is_default: false
              };
            }

            // 更新本地奖品列表
            setPrizes(freshPrizes);
          }
        } catch (error) {
          console.error("重新获取奖品列表失败:", error);
        }
      }

      // 如果还是没有找到，使用默认值
      if (!winPrize) {
        winPrize = {
          id: prizeId,
          name: response.data.prize?.name || '未知奖品',
          description: response.data.prize?.description || '',
          image_url: response.data.prize?.image_url || DEFAULT_PRIZE_ICONS.gift,
          is_default: response.data.prize?.is_default || false
        };
      }

      console.log("中奖奖品:", winPrize);

      // 找到中奖奖品在转盘上的位置
      const prizeIndex = prizes.findIndex(p => p.id.toString() === prizeId.toString());
      console.log("奖品索引:", prizeIndex, "奖品ID:", prizeId);

      // 计算旋转角度 - 使用概率加权的角度计算
      // 首先计算每个奖品的角度范围
      let startAngle = 0;
      const prizeAngles = prizes.map(prize => {
        // 根据概率计算角度
        const angle = (prize.normalizedProbability / 100) * 360;
        const currentStartAngle = startAngle;
        startAngle += angle;
        return {
          prize,
          startAngle: currentStartAngle,
          endAngle: startAngle,
          angle
        };
      });

      // 找到中奖奖品的角度范围
      const winPrizeAngle = prizeAngles.find(item => item.prize.id.toString() === prizeId.toString());

      // 如果找不到，使用默认计算方式
      let targetAngle;
      if (!winPrizeAngle) {
        console.log("未找到奖品角度范围，使用默认计算方式");
        const baseAngle = 360 / prizes.length;
        targetAngle = 360 * 5 + (360 - (prizeIndex * baseAngle + baseAngle / 2));
      } else {
        // 计算指针应该指向的角度 - 指向奖品区域的中心
        const midAngle = winPrizeAngle.startAngle + (winPrizeAngle.angle / 2);
        targetAngle = 360 * 5 + (360 - midAngle);
        console.log("使用概率加权角度计算:", winPrizeAngle, "中心角度:", midAngle, "目标角度:", targetAngle);
      }

      // 设置转盘旋转
      if (wheelRef.current) {
        wheelRef.current.style.transform = `rotate(${targetAngle}deg)`;
      }

      // 处理剩余抽奖次数
      let newRemainingDraws;

      // 优先使用API返回的剩余抽奖次数
      if (response.remaining_draws !== undefined) {
        newRemainingDraws = parseInt(response.remaining_draws);
        console.log("API返回的剩余抽奖次数:", newRemainingDraws);
      } else if (response.data && response.data.remaining_draws !== undefined) {
        // 有些API可能在data中返回剩余次数
        newRemainingDraws = parseInt(response.data.remaining_draws);
        console.log("API data中返回的剩余抽奖次数:", newRemainingDraws);
      } else {
        // 兼容旧版API，如果没有返回剩余次数，则自行计算
        newRemainingDraws = Math.max(0, remainingDraws - 1);
        console.log("计算的剩余抽奖次数:", newRemainingDraws);
      }

      // 确保剩余次数是有效数字
      if (isNaN(newRemainingDraws)) {
        console.warn("剩余抽奖次数无效，使用0作为默认值");
        newRemainingDraws = 0;
      }

      // 更新状态
      setRemainingDraws(newRemainingDraws);

      // 同时更新游戏对象中的剩余次数，确保一致性
      setGame(prev => ({
        ...prev,
        remaining_draws: newRemainingDraws
      }));

      // 旋转结束后显示结果
      setTimeout(() => {
        // 确保winPrize有完整的数据
        const completeWinPrize = {
          ...winPrize,
          id: prizeId,
          name: winPrize.name || '未知奖品',
          description: winPrize.description || '',
          value: winPrize.value || 0,
          level: winPrize.level || '',
          image_url: winPrize.image_url || DEFAULT_PRIZE_ICONS[winPrize.is_default ? 'thanks' : 'gift'],
          // 保存抽奖响应数据，以便后续使用
          record_id: response.data.id || response.data.record_id || null
        };

        console.log("完整的中奖奖品数据:", completeWinPrize);

        // 如果是默认奖品但没有is_default标记，添加标记
        if (completeWinPrize.name === '谢谢参与' && !completeWinPrize.is_default) {
          completeWinPrize.is_default = true;
        }

        // 更新奖品列表，减少已中奖品的数量
        if (!completeWinPrize.is_default) {
          setPrizes(prevPrizes =>
            prevPrizes.map(prize => {
              if (prize.id.toString() === completeWinPrize.id.toString() && prize.remaining !== undefined) {
                // 减少奖品数量
                const newRemaining = Math.max(0, prize.remaining - 1);
                console.log(`更新奖品 ${prize.name} 的剩余数量: ${prize.remaining} -> ${newRemaining}`);
                return {
                  ...prize,
                  remaining: newRemaining
                };
              }
              return prize;
            })
          );
        }

        setWinningPrize(completeWinPrize);
        setShowResult(true);
        setSpinning(false);

        // 播放音效
        if (soundEnabled) {
          try {
            if (completeWinPrize.is_default && loseSound.current) {
              loseSound.current.play().catch(err => console.error('Error playing lose sound:', err));
            } else if (winSound.current) {
              winSound.current.play().catch(err => console.error('Error playing win sound:', err));
            }
          } catch (error) {
            console.error('Error playing sound:', error);
          }
        }

        // 不刷新整个游戏数据，只更新中奖记录
        // 手动更新中奖记录，确保UI立即更新

        // 先添加当前中奖记录到本地记录中，确保UI立即更新
        if (!completeWinPrize.is_default) {
          const userPhone = localStorage.getItem('user_phone') || '';
          const currentTime = new Date().toISOString();

          // 获取用户名和消费金额
          const userName = localStorage.getItem('user_name') || '用户';
          const receiptAmountValue = localStorage.getItem('receipt_amount') || '0';
          const amountValue = parseFloat(receiptAmountValue) || 0;

          console.log("创建中奖记录时的消费金额:", amountValue);

          // 创建一个新的中奖记录对象
          const newRecord = {
            id: completeWinPrize.record_id || `temp_${Date.now()}`,
            prize_id: completeWinPrize.id,
            prize: completeWinPrize,
            participant: {
              phone: userPhone,
              name: userName
            },
            participant_name: userName,
            user_name: userName,
            phone: userPhone,
            user_phone: userPhone,
            participant_id: userPhone,
            amount: amountValue,
            receipt_amount: amountValue,
            consumption_amount: amountValue,
            created_at: currentTime,
            draw_time: currentTime,
            is_win: true,
            claim_status: 'unclaimed',
            claim_time: null
          };

          // 添加到记录列表
          setRecords(prevRecords => {
            // 检查是否已存在相同ID的记录
            const exists = prevRecords.some(r => r.id === newRecord.id);
            if (!exists) {
              // 添加新记录并保持最多5条
              return [newRecord, ...prevRecords].slice(0, 5);
            }
            return prevRecords;
          });
        }

        // 然后从服务器获取最新记录
        Promise.all([
          marketingGameApi.getParticipants(gameId),
          marketingGameApi.getDrawRecords(gameId, { limit: 10 })
        ]).then(([participantsResponse, recordsResponse]) => {
          // 处理参与者数据
          let participants = [];
          if (participantsResponse.success) {
            participants = participantsResponse.data || [];
            if (!Array.isArray(participants)) {
              participants = participants.items || participants.results || [];
            }
          }

          if (recordsResponse.success) {
            // 确保按时间倒序排列，最新的记录在前面
            let allRecords = recordsResponse.data || [];

            // 确保记录是数组
            if (!Array.isArray(allRecords)) {
              allRecords = allRecords.items || allRecords.results || [];
            }

            // 确保每条记录都有必要的字段
            const processedRecords = allRecords.map(record => {
              // 处理参与者信息
              let participantInfo = record.participant || {};
              if (!participantInfo.name) {
                participantInfo.name = record.participant_name || record.user_name || '匿名用户';
              }
              if (!participantInfo.phone) {
                participantInfo.phone = record.phone || record.user_phone || '';

                // 如果participant_id是手机号格式，也可以用作phone
                if (!participantInfo.phone && record.participant_id && /^1[3-9]\d{9}$/.test(record.participant_id)) {
                  participantInfo.phone = record.participant_id;
                }
              }

              // 处理奖品信息
              let prizeInfo = record.prize || {};

              // 如果有prize_id但没有完整的prize信息，尝试从prizes列表中获取
              if (record.prize_id && (!prizeInfo.name || prizeInfo.name === '未知奖品')) {
                // 尝试多种匹配方式，确保能找到奖品
                let matchingPrize = prizes.find(p => p.id === record.prize_id);

                // 如果没找到，尝试字符串比较
                if (!matchingPrize) {
                  matchingPrize = prizes.find(p => String(p.id) === String(record.prize_id));
                }

                // 如果还没找到，尝试数字比较
                if (!matchingPrize) {
                  matchingPrize = prizes.find(p => Number(p.id) === Number(record.prize_id));
                }

                if (matchingPrize) {
                  console.log("从prizes列表中找到匹配的奖品:", matchingPrize);
                  prizeInfo = { ...matchingPrize };
                } else {
                  console.log("未找到匹配的奖品，prize_id:", record.prize_id, "可用奖品:", prizes);

                  // 如果还是找不到，尝试从winningPrize中获取
                  if (winningPrize && winningPrize.id && winningPrize.id.toString() === record.prize_id.toString()) {
                    console.log("从当前中奖奖品中找到匹配:", winningPrize);
                    prizeInfo = { ...winningPrize };
                  }
                }
              }

              // 确保奖品信息完整
              if (!prizeInfo.name) {
                prizeInfo.name = record.prize_name || (record.prize_id ? `奖品#${record.prize_id}` : '未知奖品');
              }
              if (prizeInfo.is_default === undefined) {
                prizeInfo.is_default = record.is_default || false;
              }
              if (!prizeInfo.image_url) {
                prizeInfo.image_url = record.prize_image || DEFAULT_PRIZE_ICONS[prizeInfo.is_default ? 'thanks' : 'gift'];
              }
              if (prizeInfo.value === undefined) {
                prizeInfo.value = record.prize_value || 0;
              }
              if (!prizeInfo.description) {
                prizeInfo.description = record.prize_description || '';
              }
              if (!prizeInfo.level) {
                prizeInfo.level = record.prize_level || '';
              }

              // 确保记录ID存在
              if (!record.id && record.record_id) {
                record.id = record.record_id;
              }

              // 确保消费金额存在
              if (!record.amount && !record.receipt_amount && !record.consumption_amount) {
                // 尝试从localStorage获取消费金额
                const storedAmount = localStorage.getItem('receipt_amount');
                if (storedAmount) {
                  const amountValue = parseFloat(storedAmount) || 0;
                  record.amount = amountValue;
                  record.receipt_amount = amountValue;
                  record.consumption_amount = amountValue;
                  console.log(`为记录 ${record.id} 设置消费金额: ${amountValue}`);
                }
              } else {
                // 确保所有消费金额字段都有值
                const amountValue = record.amount || record.receipt_amount || record.consumption_amount || 0;
                record.amount = amountValue;
                record.receipt_amount = amountValue;
                record.consumption_amount = amountValue;
              }

              return {
                ...record,
                id: record.id || `record_${Math.random()}`,
                created_at: record.created_at || record.draw_time || new Date().toISOString(),
                participant: participantInfo,
                prize: prizeInfo,
                // 确保is_win字段存在
                is_win: record.is_win === true ||
                       record.is_winner === true ||
                       record.is_win === 'true' ||
                       record.is_winner === 'true' ||
                       record.status === 'win' ||
                       (prizeInfo && !prizeInfo.is_default),
                // 确保有领取状态
                claim_status: record.claim_status || 'unclaimed',
                claim_time: record.claim_time || null
              };
            });

            // 按时间倒序排序并只取前5条
            const sortedRecords = processedRecords
              // 只保留中奖记录
              .filter(record =>
                record.is_win === true ||
                record.is_winner === true ||
                record.is_win === 'true' ||
                record.is_winner === 'true' ||
                record.status === 'win' ||
                (record.prize && !record.prize.is_default)
              )
              // 按时间倒序排序
              .sort((a, b) => new Date(b.created_at || b.draw_time || 0) - new Date(a.created_at || a.draw_time || 0))
              // 只取前5条
              .slice(0, 5);

            setRecords(sortedRecords);
            console.log("已更新中奖记录:", sortedRecords);
          }
        });

        // 确保剩余抽奖次数一致性
        // 这里不需要重复设置，因为在抽奖开始时已经设置过了
        // 但是为了确保UI显示正确，我们再次检查并确认
        if (response.remaining_draws !== undefined || (response.data && response.data.remaining_draws !== undefined)) {
          const apiRemainingDraws = parseInt(response.remaining_draws !== undefined ?
                                           response.remaining_draws :
                                           response.data.remaining_draws);

          // 只有当API返回的值与当前状态不一致时才更新
          if (!isNaN(apiRemainingDraws) && apiRemainingDraws !== remainingDraws) {
            console.log("更新剩余抽奖次数:", apiRemainingDraws);
            setRemainingDraws(apiRemainingDraws);
            // 更新游戏对象中的剩余次数
            setGame(prev => ({
              ...prev,
              remaining_draws: apiRemainingDraws
            }));
          }
        }
      }, 4000); // 与CSS中的transition时间一致
    } catch (error) {
      console.error('Error during draw:', error);
      message.error('抽奖失败，请稍后再试');
      setSpinning(false);
    }
  };

  // 验证小票
  const handleVerifyReceipt = async () => {
    if (!receiptCode) {
      message.error('请输入小票号码');
      return;
    }

    if (game?.min_consumption > 0 && (!receiptAmount || parseFloat(receiptAmount) < game.min_consumption)) {
      message.error(`小票金额不能低于${game.min_consumption}元`);
      return;
    }

    try {
      setVerifying(true);

      // 调用验证API
      const amountValue = receiptAmount ? parseFloat(receiptAmount) : 0;
      console.log("验证小票时的消费金额:", amountValue);

      const response = await marketingGameApi.verifyReceipt(gameId, {
        receipt_code: receiptCode,
        amount: amountValue,
        consumption_amount: amountValue  // 添加额外的字段，确保后端能接收到
      });

      if (response.success) {
        // 保存小票号到localStorage
        localStorage.setItem('receipt_code', receiptCode);

        // 保存消费金额 - 确保即使是0也保存
        const amountValue = receiptAmount ? parseFloat(receiptAmount) : 0;
        localStorage.setItem('receipt_amount', amountValue.toString());

        // 如果API返回了用户信息，也保存
        if (response.data) {
          if (response.data.phone) {
            localStorage.setItem('user_phone', response.data.phone);
            // 使用手机号后四位作为默认用户名
            const userName = `用户(${response.data.phone.slice(-4)})`;
            localStorage.setItem('user_name', userName);
          }

          if (response.data.participant && response.data.participant.name) {
            localStorage.setItem('user_name', response.data.participant.name);
          }
        }

        message.success('小票验证成功');
        setReceiptVerified(true);

        // 如果设置了自动抽奖，则自动开始抽奖
        if (game?.settings?.auto_draw === true) {
          setShowVerification(false); // 关闭验证弹窗
          handleDraw();
        }
      } else {
        message.error(response.message || '小票验证失败');
      }
    } catch (error) {
      console.error('Error verifying receipt:', error);
      message.error(error.response?.data?.detail || '小票验证失败，请稍后再试');
    } finally {
      setVerifying(false);
    }
  };

  // 验证手机号
  const handleVerifyPhone = async () => {
    if (!phone || !/^1[3-9]\d{9}$/.test(phone)) {
      message.error('请输入有效的手机号码');
      return;
    }

    // 检查是否需要输入消费金额
    if (game?.min_consumption > 0 && (!receiptAmount || parseFloat(receiptAmount) < game.min_consumption)) {
      message.error(`消费金额不能低于${game.min_consumption}元`);
      return;
    }

    try {
      setVerifyingPhone(true);

      // 调用验证API
      const amountValue = receiptAmount ? parseFloat(receiptAmount) : 0;
      console.log("验证手机号时的消费金额:", amountValue);

      const response = await marketingGameApi.verifyPhone(gameId, {
        phone: phone,
        amount: amountValue,
        consumption_amount: amountValue  // 添加额外的字段，确保后端能接收到
      });

      if (response.success) {
        // 保存手机号到localStorage
        localStorage.setItem('user_phone', phone);

        // 保存用户名 - 使用手机号后四位作为默认用户名
        const userName = `用户(${phone.slice(-4)})`;
        localStorage.setItem('user_name', userName);

        // 保存消费金额 - 确保即使是0也保存
        const amountValue = receiptAmount ? parseFloat(receiptAmount) : 0;
        localStorage.setItem('receipt_amount', amountValue.toString());

        // 如果API返回了用户信息，也保存
        if (response.data && response.data.participant) {
          if (response.data.participant.name) {
            localStorage.setItem('user_name', response.data.participant.name);
          }
        }

        message.success('手机号验证成功');
        setPhoneVerified(true);

        // 如果设置了自动抽奖，则自动开始抽奖
        if (game?.settings?.auto_draw === true) {
          setShowVerification(false); // 关闭验证弹窗
          handleDraw();
        }
      } else {
        message.error(response.message || '手机号验证失败');
      }
    } catch (error) {
      console.error('Error verifying phone:', error);
      message.error(error.response?.data?.detail || '手机号验证失败，请稍后再试');
    } finally {
      setVerifyingPhone(false);
    }
  };

  // 渲染加载状态
  if (loading) {
    return (
      <div className="wheel-loading">
        <LoadingOutlined className="loading-icon" />
        <div className="loading-text">加载中，请稍候...</div>
      </div>
    );
  }

  // 获取当前主题
  const themeStyle = game?.settings?.theme_style || 'classic';
  const currentTheme = getCurrentTheme(themeStyle);

  return (
    <div className="wheel-fortune-wrapper">
      <div className="wheel-fortune-header">
        <Title level={3} className="game-title" style={{
          background: 'linear-gradient(45deg, #ff4d4f, #ff7a45, #fa8c16, #ffc53d)',
          WebkitBackgroundClip: 'text',
          WebkitTextFillColor: 'transparent',
          textShadow: '0 2px 4px rgba(0,0,0,0.1)',
          fontWeight: 'bold',
          letterSpacing: '1px',
          textAlign: 'center',
          fontSize: '28px'
        }}>
          {/* 优先使用游戏设置中的名称 */}
          {game?.settings?.game_name || game?.name || '幸运大转盘'}
        </Title>
        <Paragraph className="game-description" style={{
          textAlign: 'center',
          fontSize: '16px',
          color: '#666',
          fontStyle: 'italic',
          margin: '10px auto',
          maxWidth: '80%'
        }}>
          {game?.settings?.game_description || game?.description || '转动大转盘，赢取精美礼品'}
        </Paragraph>
      </div>

      <div className="wheel-fortune-content">
        {/* 音效按钮移到下方 */}

        <div className="wheel-container">
          <div className={`wheel-pointer-${themeStyle}`}></div>
          <div
            className={`wheel-fortune-${themeStyle}${spinning ? ' wheel-spinning' : ''}`}
            ref={wheelRef}
            style={{
              '--prize-count': prizes.length,
            }}
          >
            {/* 计算总角度和起始角度 */}
            {(() => {
              // 计算每个奖品的角度
              let startAngle = 0;
              const prizeAngles = prizes.map(prize => {
                // 根据概率计算角度
                const angle = (prize.normalizedProbability / 100) * 360;
                const currentStartAngle = startAngle;
                startAngle += angle;
                return {
                  prize,
                  startAngle: currentStartAngle,
                  angle
                };
              });

              return prizeAngles.map((item, index) => {
                const { prize, startAngle, angle } = item;
                // 确保所有扇形都有颜色，不会出现白色扇形
                const backgroundColor = currentTheme.colors[index % currentTheme.colors.length];

                // 计算扇形的CSS
                // 使用clip-path创建扇形
                const clipPath = `conic-gradient(from ${startAngle}deg, ${backgroundColor} 0deg, ${backgroundColor} ${angle}deg, transparent ${angle}deg, transparent 360deg)`;

                return (
                  <div
                    key={prize.id || index}
                    className="wheel-item"
                    style={{
                      position: 'absolute',
                      width: '100%',
                      height: '100%',
                      transform: `rotate(${startAngle}deg)`,
                      transformOrigin: 'center',
                      // 使用单一的background属性，避免冲突
                      background: `${clipPath}, radial-gradient(circle at center, ${backgroundColor}, ${adjustColor(backgroundColor, 20)})`
                    }}
                  >
                    <div
                      className="wheel-item-content"
                      style={{
                        position: 'absolute',
                        top: '12%',
                        left: '50%',
                        transform: `translateX(-50%) rotate(${angle / 2}deg)`,
                        // 根据背景色调整文字颜色以确保可读性
                        color: isLightColor(backgroundColor) ? '#333' : '#fff',
                        textAlign: 'center',
                        width: '90px',
                        padding: '4px',
                        borderRadius: '4px',
                        backgroundColor: isLightColor(backgroundColor) ? 'rgba(255,255,255,0.3)' : 'rgba(0,0,0,0.1)'
                      }}
                    >
                      <img
                        src={prize.image_url || DEFAULT_PRIZE_ICONS[prize.is_default ? 'none' : 'gift']}
                        alt={prize.name}
                        className="prize-image"
                        style={{
                          width: '35px',
                          height: '35px',
                          objectFit: 'contain',
                          marginBottom: '5px'
                        }}
                      />
                      <div
                        className="prize-name"
                        style={{
                          fontSize: '12px',
                          fontWeight: 'bold',
                          textShadow: isLightColor(backgroundColor) ? '0 0 2px rgba(255,255,255,0.8)' : '0 0 3px rgba(0,0,0,0.7)',
                          whiteSpace: 'nowrap',
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          maxWidth: '100%',
                          padding: '3px 5px',
                          borderRadius: '3px',
                          backgroundColor: isLightColor(backgroundColor) ? 'rgba(255,255,255,0.6)' : 'rgba(0,0,0,0.3)',
                          border: `1px solid ${isLightColor(backgroundColor) ? 'rgba(0,0,0,0.15)' : 'rgba(255,255,255,0.3)'}`,
                          boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
                        }}
                      >
                        {prize.name || '谢谢参与'}
                      </div>

                      {/* 显示奖品等级 */}
                      {prize.level && (
                        <div
                          className="prize-level"
                          style={{
                            fontSize: '10px',
                            fontWeight: 'bold',
                            color: isLightColor(backgroundColor) ? '#1890ff' : '#bae7ff',
                            marginTop: '3px',
                            backgroundColor: isLightColor(backgroundColor) ? 'rgba(24, 144, 255, 0.15)' : 'rgba(24, 144, 255, 0.35)',
                            padding: '2px 5px',
                            borderRadius: '4px',
                            display: 'inline-block',
                            border: `1px solid ${isLightColor(backgroundColor) ? 'rgba(24, 144, 255, 0.3)' : 'rgba(24, 144, 255, 0.5)'}`,
                            boxShadow: '0 1px 2px rgba(0,0,0,0.1)'
                          }}
                        >
                          等级: {prize.level}
                        </div>
                      )}

                      {prize.value > 0 && (
                        <div
                          className="prize-value"
                          style={{
                            fontSize: '10px',
                            fontWeight: 'bold',
                            color: isLightColor(backgroundColor) ? '#f5222d' : '#ffccc7',
                            marginTop: '3px',
                            backgroundColor: isLightColor(backgroundColor) ? 'rgba(245, 34, 45, 0.1)' : 'rgba(245, 34, 45, 0.2)',
                            padding: '2px 5px',
                            borderRadius: '4px',
                            display: 'inline-block',
                            border: `1px solid ${isLightColor(backgroundColor) ? 'rgba(245, 34, 45, 0.2)' : 'rgba(245, 34, 45, 0.4)'}`,
                            boxShadow: '0 1px 2px rgba(0,0,0,0.1)'
                          }}
                        >
                          价值: ¥{prize.value}
                        </div>
                      )}
                    </div>
                  </div>
                );
              });
            })()}
          </div>

          <div className={`wheel-center-${themeStyle}`}></div>

          <div className="wheel-button-container">
            <Button
              type="primary"
              className={`wheel-button-${themeStyle}`}
              onClick={handleDraw}
              disabled={spinning || remainingDraws <= 0}
              loading={spinning}
            >
              {spinning ? '旋转中' : (remainingDraws > 0 ? '开始' : '次数已用完')}
            </Button>
          </div>
        </div>

        <div className="remaining-draws">
          剩余抽奖次数: <span className="remaining-count">{remainingDraws}</span>
        </div>

        <div className="action-buttons">
          <Button
            icon={<TrophyOutlined />}
            onClick={() => setShowPrizes(true)}
            className="action-button"
          >
            奖品设置
          </Button>
          <Button
            icon={<HistoryOutlined />}
            onClick={() => setShowRecords(true)}
            className="action-button"
          >
            中奖记录
          </Button>
          <Button
            icon={<UserOutlined />}
            onClick={() => setShowRules(true)}
            className="action-button"
          >
            活动规则
          </Button>

          {/* 添加再来一局和领取奖励按钮 */}
          <div style={{
            width: '100%',
            marginTop: '10px',
            padding: '0 10px',
            display: 'flex',
            justifyContent: 'center',
            gap: '10px'
          }}>
            <Button
              type="primary"
              icon={<ReloadOutlined />}
              onClick={() => {
                // 清空手机号和小票号
                localStorage.removeItem('user_phone');
                localStorage.removeItem('receipt_code');
                setPhoneVerified(false);
                setReceiptVerified(false);
                setPhone('');
                setReceiptCode('');
                setReceiptAmount('');
                // 刷新游戏数据
                fetchGameData();
                // 提示用户
                message.success('已重置，可以重新参与游戏');
              }}
            >
              再来一局
            </Button>
            {game?.settings?.enable_phone_verification === true && (
              <Button
                icon={<GiftOutlined />}
                onClick={() => {
                  // 打开领奖弹窗
                  setShowClaimPrize(true);
                }}
              >
                领取奖励
              </Button>
            )}
          </div>
        </div>
      </div>

      <div className="wheel-fortune-footer">
        <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', marginBottom: '10px' }}>
          <span style={{ marginRight: '10px' }}>音效</span>
          <Switch
            checked={soundEnabled}
            onChange={setSoundEnabled}
            size="small"
            checkedChildren={<SoundOutlined />}
            unCheckedChildren={<SoundOutlined />}
          />
        </div>
        <div>© {new Date().getFullYear()} {game?.company_name || '营销游戏'}</div>
      </div>

      {/* 中奖结果弹窗 */}
      <Modal
        open={showResult}
        footer={null}
        onCancel={() => setShowResult(false)}
        width={400}
        centered
        closable={false}
        styles={{ body: { padding: '30px 20px' } }}
      >
        <Result
          icon={
            winningPrize?.is_default ?
            <SmileOutlined style={{ color: '#ffa940' }} /> :
            <GiftOutlined style={{ color: '#ff4d4f' }} />
          }
          status={winningPrize?.is_default ? 'info' : 'success'}
          title={
            <div style={{ color: winningPrize?.is_default ? '#ffa940' : '#52c41a', fontSize: '24px' }}>
              {winningPrize?.is_default ? '谢谢参与' : '恭喜中奖'}
            </div>
          }
          subTitle={
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '20px', fontWeight: 'bold', marginBottom: '12px', color: '#333' }}>
                {winningPrize?.name || '未知奖品'}
              </div>
              {winningPrize?.description && (
                <div style={{ fontSize: '14px', color: '#666', margin: '10px 0' }}>
                  {winningPrize.description}
                </div>
              )}
              {winningPrize?.value > 0 && (
                <div style={{ fontSize: '16px', color: '#ff5722', margin: '10px 0', fontWeight: 'bold' }}>
                  价值: ¥{winningPrize.value}
                </div>
              )}
              {!winningPrize?.is_default && winningPrize?.image_url && (
                <div style={{ margin: '15px 0' }}>
                  <img
                    src={winningPrize.image_url}
                    alt={winningPrize.name}
                    style={{
                      maxWidth: '150px',
                      maxHeight: '150px',
                      objectFit: 'contain',
                      borderRadius: '8px',
                      border: '1px solid #f0f0f0',
                      padding: '5px'
                    }}
                  />
                </div>
              )}
            </div>
          }
          extra={[
            <Button key="close" onClick={() => {
              setShowResult(false);
              // 不刷新数据，保持当前状态
            }}>
              关闭
            </Button>,
            !winningPrize?.is_default && (
              <Button key="claim" type="primary" onClick={async () => {
                try {
                  // 直接使用当前中奖信息进行领取，而不是重新查询
                  if (winningPrize && !winningPrize.is_default) {
                    // 获取当前北京时间的ISO字符串，但不带时区信息
                    const getCurrentBeijingTime = () => {
                      const now = new Date();
                      // 获取UTC时间的毫秒数
                      const utcTime = now.getTime();
                      // 北京时间比UTC早8小时，即+8时区
                      const beijingTime = new Date(utcTime + (8 * 60 * 60 * 1000));
                      // 返回ISO格式的字符串，但移除末尾的Z（表示UTC）
                      return beijingTime.toISOString().replace('Z', '');
                    };

                    // 获取最新的中奖记录，但不依赖它来确定是否可以领奖
                    const recordsResponse = await marketingGameApi.getDrawRecords(gameId, { limit: 10 });
                    let recordId = null;

                    if (recordsResponse.success) {
                      let allRecords = recordsResponse.data || [];
                      if (!Array.isArray(allRecords)) {
                        allRecords = allRecords.items || allRecords.results || [];
                      }

                      // 尝试找到匹配当前中奖奖品的记录
                      const userPhone = localStorage.getItem('user_phone') || '';
                      const latestRecord = allRecords
                        .filter(record => {
                          // 检查是否中奖
                          const isWin = record.is_win === true ||
                                     record.is_winner === true ||
                                     record.is_win === 'true' ||
                                     record.is_winner === 'true' ||
                                     record.status === 'win' ||
                                     (record.prize && !record.prize.is_default);

                          // 检查是否是当前用户
                          const isCurrentUser =
                            (record.participant && record.participant.phone === userPhone) ||
                            record.phone === userPhone ||
                            record.user_phone === userPhone ||
                            record.participant_id === userPhone;

                          // 检查是否未领取
                          const isUnclaimed = record.claim_status !== 'claimed';

                          // 检查是否是当前奖品
                          const isPrizeMatch =
                            (record.prize_id && record.prize_id.toString() === winningPrize.id.toString()) ||
                            (record.prize && record.prize.id && record.prize.id.toString() === winningPrize.id.toString());

                          return isWin && isCurrentUser && isUnclaimed && (isPrizeMatch || !isPrizeMatch); // 不强制匹配奖品，只要是未领取的中奖记录即可
                        })
                        .sort((a, b) => new Date(b.created_at || b.draw_time) - new Date(a.created_at || a.draw_time))
                        [0];

                      if (latestRecord) {
                        recordId = latestRecord.id;
                        console.log("找到匹配的中奖记录:", latestRecord);
                      }
                    }

                    if (!recordId) {
                      // 如果没有找到记录ID，尝试使用响应中的记录ID
                      console.log("未找到匹配的中奖记录，尝试使用响应中的记录ID");
                      // 这里可能需要根据实际情况调整
                      recordId = winningPrize.record_id;
                    }

                    if (recordId) {
                      // 更新领取状态
                      const response = await marketingGameApi.updateRecord(gameId, recordId, {
                        claim_status: 'claimed',
                        claim_time: getCurrentBeijingTime()
                      });

                      if (response.success) {
                        message.success('奖品领取成功！');

                        // 先关闭结果弹窗
                        setShowResult(false);

                        // 成功后再刷新游戏数据
                        fetchGameData();
                      } else {
                        message.error(response.message || '领取失败');
                      }
                    } else {
                      // 如果没有找到记录ID，直接显示成功消息
                      message.success('奖品已记录，请在中奖记录中查看！');

                      // 关闭结果弹窗
                      setShowResult(false);

                      // 刷新游戏数据
                      fetchGameData();
                    }
                  } else {
                    message.warning('非中奖奖品无法领取');
                  }
                } catch (error) {
                  console.error('Error claiming prize:', error);
                  message.error('领取失败，请稍后再试');
                }
              }}>
                领取奖品
              </Button>
            )
          ].filter(Boolean)}
        />
      </Modal>

      {/* 奖品列表弹窗 */}
      <Modal
        title={<div style={{ textAlign: 'center', fontSize: '18px' }}>奖品列表</div>}
        open={showPrizes}
        onCancel={() => setShowPrizes(false)}
        footer={null}
        width={600}
      >
        <List
          itemLayout="horizontal"
          dataSource={prizes.filter(p => {
            // 过滤掉复制的奖品，只显示原始奖品
            return !p._isCopy && !p.id.toString().includes('_copy_');
          })}
          renderItem={item => (
            <List.Item>
              <List.Item.Meta
                avatar={
                  <Avatar
                    src={item.image_url || DEFAULT_PRIZE_ICONS[item.is_default ? 'thanks' : 'gift']}
                    style={{
                      border: item.is_default ? 'none' : '2px solid #1890ff',
                      padding: '2px',
                      backgroundColor: '#f9f9f9'
                    }}
                    size={64}
                  />
                }
                title={
                  <span style={{ fontSize: '16px', color: item.is_default ? '#999' : '#1890ff' }}>
                    {item.name}
                    {item.is_default && <Tag color="default" style={{ marginLeft: '8px' }}>安慰奖</Tag>}
                  </span>
                }
                description={
                  <>
                    {item.description && (
                      <div style={{ margin: '5px 0' }}>{item.description}</div>
                    )}
                    <div style={{ marginTop: '8px' }}>
                      <Tag color="blue">概率: {item.probability || 0}%</Tag>
                      {!item.is_default && (
                        <Tag color="green">剩余: {
                          item.remaining === 0 ? '0' :
                          (item.remaining || item.remaining_quantity || '不限')
                        }</Tag>
                      )}
                      {item.value > 0 && (
                        <Tag color="volcano">价值: ¥{item.value}</Tag>
                      )}
                    </div>
                  </>
                }
              />
            </List.Item>
          )}
          locale={{
            emptyText: <Empty description="暂无奖品" />
          }}
        />
      </Modal>

      {/* 中奖记录弹窗 */}
      <Modal
        title={<div style={{ textAlign: 'center', fontSize: '18px' }}>中奖记录</div>}
        open={showRecords}
        onCancel={() => setShowRecords(false)}
        footer={null}
        width={600}
      >
        <List
          itemLayout="horizontal"
          dataSource={records}
          renderItem={item => (
            <List.Item
              actions={[
                item.claim_status === 'unclaimed' && !item.prize?.is_default && (
                  <Button
                    type="primary"
                    size="small"
                    onClick={async () => {
                      try {
                        // 确保有记录ID
                        if (!item.id) {
                          message.error('记录ID缺失，无法领取');
                          return;
                        }

                        // 获取当前北京时间的ISO字符串，但不带时区信息
                        const getCurrentBeijingTime = () => {
                          const now = new Date();
                          // 获取UTC时间的毫秒数
                          const utcTime = now.getTime();
                          // 北京时间比UTC早8小时，即+8时区
                          const beijingTime = new Date(utcTime + (8 * 60 * 60 * 1000));
                          // 返回ISO格式的字符串，但移除末尾的Z（表示UTC）
                          return beijingTime.toISOString().replace('Z', '');
                        };

                        console.log("尝试领取奖品，记录ID:", item.id);

                        // 更新领取状态
                        const response = await marketingGameApi.updateRecord(gameId, item.id, {
                          claim_status: 'claimed',
                          claim_time: getCurrentBeijingTime()
                        });

                        if (response.success) {
                          message.success('奖品领取成功！');

                          // 先更新本地记录状态
                          const currentTime = getCurrentBeijingTime();
                          setRecords(prevRecords =>
                            prevRecords.map(record =>
                              record.id === item.id
                                ? {
                                    ...record,
                                    claim_status: 'claimed',
                                    claim_time: currentTime
                                  }
                                : record
                            )
                          );

                          // 延迟一段时间后再刷新数据，确保状态更新完成
                          setTimeout(() => {
                            fetchGameData();
                          }, 800);
                        } else {
                          message.error(response.message || '领取失败');
                        }
                      } catch (error) {
                        console.error('Error claiming prize:', error);
                        message.error('领取失败，请稍后再试');
                      }
                    }}
                  >
                    领取奖品
                  </Button>
                )
              ].filter(Boolean)}
            >
              <List.Item.Meta
                avatar={
                  <Avatar
                    src={item.prize?.image_url || DEFAULT_PRIZE_ICONS[item.prize?.is_default ? 'thanks' : 'gift']}
                    style={{
                      border: item.prize?.is_default ? 'none' : '2px solid #52c41a',
                      padding: '2px',
                      backgroundColor: '#f9f9f9'
                    }}
                  />
                }
                title={
                  <span style={{ color: item.prize?.is_default ? '#999' : '#1890ff' }}>
                    奖品: <strong>{item.prize?.name || item.prize_name || (item.prize_id ? `奖品#${item.prize_id}` : '未知奖品')}</strong>
                    <Tag color="success" style={{ marginLeft: '8px' }}>已中奖</Tag>
                    {item.claim_status && (
                      <Tag
                        color={item.claim_status === 'claimed' ? 'green' : 'orange'}
                        style={{ marginLeft: '8px' }}
                      >
                        {item.claim_status === 'claimed' ? '已领取' : '未领取'}
                      </Tag>
                    )}
                  </span>
                }
                description={
                  <>
                    <div>用户: {
                      // 如果有手机号，显示后四位
                      item.participant?.phone ?
                        `${item.participant.name || '用户'}(${item.participant.phone.slice(-4)})` :
                        item.phone ? `用户(${item.phone.slice(-4)})` :
                        item.user_phone ? `用户(${item.user_phone.slice(-4)})` :
                        item.participant_id && /^1[3-9]\d{9}$/.test(item.participant_id) ? `用户(${item.participant_id.slice(-4)})` :
                        item.participant?.name || item.user_name || item.participant_name || '匿名用户'
                    }</div>
                    <div>时间: {formatToChineseTime(item.created_at || item.draw_time)}</div>

                    {/* 奖品信息 */}
                    <div style={{
                      marginTop: '5px',
                      padding: '8px',
                      backgroundColor: '#f9f9f9',
                      borderRadius: '4px',
                      border: '1px solid #f0f0f0',
                      boxShadow: '0 1px 3px rgba(0,0,0,0.05)'
                    }}>
                      <div style={{
                        fontSize: '14px',
                        fontWeight: 'bold',
                        marginBottom: '5px',
                        borderBottom: '1px dashed #e8e8e8',
                        paddingBottom: '5px'
                      }}>
                        奖品详情
                      </div>

                      <div style={{ display: 'flex', flexWrap: 'wrap', gap: '8px' }}>
                        {item.prize?.level &&
                          <div style={{
                            backgroundColor: '#e6f7ff',
                            padding: '2px 8px',
                            borderRadius: '4px',
                            border: '1px solid #91d5ff'
                          }}>
                            等级: <span style={{ fontWeight: 'bold', color: '#1890ff' }}>{item.prize.level}</span>
                          </div>
                        }

                        {(item.prize?.value > 0 || item.prize_value > 0) &&
                          <div style={{
                            backgroundColor: '#fff2e8',
                            padding: '2px 8px',
                            borderRadius: '4px',
                            border: '1px solid #ffbb96'
                          }}>
                            价值: <span style={{ fontWeight: 'bold', color: '#ff4d4f' }}>
                              ¥{((item.prize?.value || item.prize_value || 0)).toFixed(2)}
                            </span>
                          </div>
                        }

                        {/* 显示消费金额 */}
                        {(item.amount > 0 || item.receipt_amount > 0 || item.consumption_amount > 0) &&
                          <div style={{
                            backgroundColor: '#f6ffed',
                            padding: '2px 8px',
                            borderRadius: '4px',
                            border: '1px solid #b7eb8f'
                          }}>
                            消费: <span style={{ fontWeight: 'bold', color: '#52c41a' }}>
                              ¥{((item.amount || item.receipt_amount || item.consumption_amount || 0)).toFixed(2)}
                            </span>
                          </div>
                        }
                      </div>

                      {(item.prize?.description || item.prize_description) &&
                        <div style={{ marginTop: '5px', color: '#666' }}>
                          <div style={{ fontWeight: 'bold', fontSize: '12px', color: '#999' }}>描述:</div>
                          <div style={{ padding: '3px 0' }}>{item.prize?.description || item.prize_description}</div>
                        </div>
                      }
                    </div>

                    {item.claim_time && <div>领取时间: {formatToChineseTime(item.claim_time)}</div>}
                  </>
                }
              />
            </List.Item>
          )}
          locale={{
            emptyText: <Empty description="暂无中奖记录" />
          }}
        />

      </Modal>

      {/* 活动规则弹窗 */}
      <Modal
        title={<div style={{ textAlign: 'center', fontSize: '18px' }}>活动规则</div>}
        open={showRules}
        onCancel={() => setShowRules(false)}
        footer={null}
        width={600}
      >
        <div style={{ padding: '0 10px' }}>
          <Paragraph style={{ fontSize: '15px' }}>
            <strong style={{ color: '#1890ff' }}>活动时间：</strong>
            <span style={{ marginLeft: '8px' }}>
              {game?.start_time && game?.end_time
                ? `${new Date(game.start_time).toLocaleDateString()} 至 ${new Date(game.end_time).toLocaleDateString()}`
                : '长期有效'}
            </span>
          </Paragraph>

          <Paragraph style={{ fontSize: '15px' }}>
            <strong style={{ color: '#1890ff' }}>参与方式：</strong>
            <span style={{ marginLeft: '8px' }}>
              {game?.settings?.enable_receipt_verification === true || game?.min_consumption > 0
                ? `消费满${game?.min_consumption || 0}元，凭小票参与抽奖`
                : game?.settings?.enable_phone_verification === true
                  ? '需验证手机号参与'
                  : '无门槛参与'}
            </span>
          </Paragraph>

          <Paragraph style={{ fontSize: '15px' }}>
            <strong style={{ color: '#1890ff' }}>抽奖次数：</strong>
            <span style={{ marginLeft: '8px' }}>
              每人每天最多可抽奖{game?.max_draws_per_user || '不限'}次
            </span>
          </Paragraph>

          <Divider style={{ margin: '15px 0' }} />

          <Paragraph style={{ fontSize: '15px' }}>
            <strong style={{ color: '#1890ff' }}>奖品设置：</strong>
          </Paragraph>

          <List
            itemLayout="horizontal"
            dataSource={prizes.filter(p => !p._isCopy && !p.id.toString().includes('_copy_'))}
            renderItem={item => (
              <List.Item>
                <List.Item.Meta
                  avatar={
                    <Avatar
                      src={item.image_url || DEFAULT_PRIZE_ICONS[item.is_default ? 'thanks' : 'gift']}
                      style={{
                        border: item.is_default ? 'none' : '2px solid #1890ff',
                        padding: '2px',
                        backgroundColor: '#f9f9f9'
                      }}
                    />
                  }
                  title={
                    <span style={{ color: item.is_default ? '#999' : '#1890ff' }}>
                      {item.name}
                      {item.is_default && <Tag color="default" style={{ marginLeft: '8px' }}>安慰奖</Tag>}
                    </span>
                  }
                  description={
                    <>
                      {item.description && <div>{item.description}</div>}
                      {!item.is_default && item.probability > 0 && (
                        <Tag color="blue" style={{ marginTop: '5px' }}>概率: {item.probability}%</Tag>
                      )}
                    </>
                  }
                />
              </List.Item>
            )}
            locale={{
              emptyText: <Empty description="暂无奖品" />
            }}
          />

          <Divider style={{ margin: '15px 0' }} />

          <Paragraph style={{ fontSize: '15px' }}>
            <strong style={{ color: '#1890ff' }}>注意事项：</strong>
          </Paragraph>

          <Paragraph style={{ fontSize: '14px', margin: '8px 0' }}>
            1. 实物奖品请在中奖后30天内领取，逾期视为自动放弃。
          </Paragraph>

          <Paragraph style={{ fontSize: '14px', margin: '8px 0' }}>
            2. 活动最终解释权归{game?.company_name || '主办方'}所有。
          </Paragraph>

          {game?.rules && (
            <Paragraph style={{ fontSize: '14px', margin: '8px 0' }}>
              3. {game.rules}
            </Paragraph>
          )}
        </div>
      </Modal>

      {/* 验证弹窗 */}
      <Modal
        title={
          <div>
            验证信息
            <div style={{ fontSize: '12px', color: '#999', fontWeight: 'normal', marginTop: '5px' }}>
              提示: 输入完成后可按空格键快速确认
            </div>
          </div>
        }
        open={showVerification}
        onCancel={() => setShowVerification(false)}
        footer={null}
        width={400}
      >
        <div>
          {game?.settings?.enable_phone_verification === true && !phoneVerified && (
            <>
              <Form layout="vertical">
                <Form.Item
                  label="手机号码"
                  required
                  rules={[{ required: true, message: '请输入手机号码' }]}
                >
                  <Input
                    placeholder="请输入手机号码"
                    value={phone}
                    onChange={e => setPhone(e.target.value)}
                    onKeyDown={e => {
                      // 当按下空格键时，触发验证
                      if (e.key === ' ' || e.keyCode === 32) {
                        e.preventDefault(); // 阻止空格输入
                        if (phone && game?.min_consumption > 0 ? receiptAmount >= game.min_consumption : true) {
                          handleVerifyPhone();
                        } else if (!phone) {
                          message.warning('请输入手机号码');
                        } else if (game?.min_consumption > 0 && receiptAmount < game.min_consumption) {
                          message.warning(`消费金额需满${game.min_consumption}元`);
                        }
                      }
                    }}
                  />
                </Form.Item>

                {/* 如果有最低消费金额要求，显示消费金额输入框 */}
                {game?.min_consumption > 0 && (
                  <Form.Item
                    label={`消费金额（最低${game.min_consumption}元）`}
                    required
                    rules={[{ required: true, message: '请输入消费金额' }]}
                  >
                    <Input
                      placeholder="请输入消费金额"
                      value={receiptAmount}
                      onChange={e => setReceiptAmount(e.target.value)}
                      type="number"
                      min={game.min_consumption}
                      addonAfter="元"
                      onKeyDown={e => {
                        // 当按下空格键时，触发验证
                        if (e.key === ' ' || e.keyCode === 32) {
                          e.preventDefault(); // 阻止空格输入
                          if (phone && receiptAmount >= game.min_consumption) {
                            handleVerifyPhone();
                          } else if (!phone) {
                            message.warning('请输入手机号码');
                          } else if (receiptAmount < game.min_consumption) {
                            message.warning(`消费金额需满${game.min_consumption}元`);
                          }
                        }
                      }}
                    />
                  </Form.Item>
                )}
              </Form>

              <div style={{ textAlign: 'right', marginBottom: 16 }}>
                <Button
                  type="primary"
                  onClick={handleVerifyPhone}
                  loading={verifyingPhone}
                >
                  验证手机号
                </Button>
              </div>

              <Divider />
            </>
          )}

          {game?.settings?.enable_receipt_verification === true && !receiptVerified && (
            <Form layout="vertical">
              <Form.Item
                label="小票号码"
                required
                rules={[{ required: true, message: '请输入小票号码' }]}
              >
                <Input
                  placeholder="请输入小票号码"
                  value={receiptCode}
                  onChange={e => setReceiptCode(e.target.value)}
                  onKeyDown={e => {
                    // 当按下空格键时，触发验证
                    if (e.key === ' ' || e.keyCode === 32) {
                      e.preventDefault(); // 阻止空格输入
                      if (receiptCode && game?.min_consumption > 0 ? receiptAmount >= game.min_consumption : true) {
                        handleVerifyReceipt();
                      } else if (!receiptCode) {
                        message.warning('请输入小票号码');
                      } else if (game?.min_consumption > 0 && receiptAmount < game.min_consumption) {
                        message.warning(`消费金额需满${game.min_consumption}元`);
                      }
                    }
                  }}
                />
              </Form.Item>

              {game?.min_consumption > 0 && (
                <Form.Item
                  label={`消费金额（最低${game.min_consumption}元）`}
                  required
                  rules={[{ required: true, message: '请输入消费金额' }]}
                >
                  <Input
                    placeholder="请输入消费金额"
                    value={receiptAmount}
                    onChange={e => setReceiptAmount(e.target.value)}
                    type="number"
                    min={game.min_consumption}
                    addonAfter="元"
                    onKeyDown={e => {
                      // 当按下空格键时，触发验证
                      if (e.key === ' ' || e.keyCode === 32) {
                        e.preventDefault(); // 阻止空格输入
                        if (receiptCode && receiptAmount >= game.min_consumption) {
                          handleVerifyReceipt();
                        } else if (!receiptCode) {
                          message.warning('请输入小票号码');
                        } else if (receiptAmount < game.min_consumption) {
                          message.warning(`消费金额需满${game.min_consumption}元`);
                        }
                      }
                    }}
                  />
                </Form.Item>
              )}

              <div style={{ textAlign: 'right' }}>
                <Button
                  type="primary"
                  onClick={handleVerifyReceipt}
                  loading={verifying}
                >
                  验证小票
                </Button>
              </div>
            </Form>
          )}

          {((game?.settings?.enable_phone_verification === true && phoneVerified) ||
            game?.settings?.enable_phone_verification !== true) &&
           ((game?.settings?.enable_receipt_verification === true && receiptVerified) ||
            game?.settings?.enable_receipt_verification !== true) && (
            <Result
              status="success"
              title="验证成功"
              subTitle="您已完成所有验证，可以开始抽奖了"
              extra={[
                <Button
                  key="draw"
                  type="primary"
                  onClick={() => {
                    setShowVerification(false);
                    handleDraw();
                  }}
                >
                  开始抽奖
                </Button>
              ]}
            />
          )}
        </div>
      </Modal>

      {/* 领取奖励弹窗 */}
      <Modal
        title="领取奖励"
        open={showClaimPrize}
        onCancel={() => setShowClaimPrize(false)}
        footer={null}
        width={400}
      >
        <Form layout="vertical">
          <Form.Item
            label="手机号码"
            required
            rules={[{ required: true, message: '请输入手机号码' }]}
          >
            <Input
              placeholder="请输入您参与游戏时使用的手机号码"
              value={claimPhone}
              onChange={e => setClaimPhone(e.target.value)}
            />
          </Form.Item>

          <div style={{ textAlign: 'center', marginTop: '20px' }}>
            <Button
              type="primary"
              icon={<GiftOutlined />}
              loading={claimingPrize}
              onClick={async () => {
                if (!claimPhone || !/^1[3-9]\d{9}$/.test(claimPhone)) {
                  message.error('请输入有效的手机号码');
                  return;
                }

                try {
                  setClaimingPrize(true);

                  // 1. 先获取所有参与者
                  const participantsResponse = await marketingGameApi.getParticipants(gameId);
                  let participants = [];

                  if (participantsResponse.success) {
                    participants = participantsResponse.data || [];
                    if (!Array.isArray(participants)) {
                      participants = participants.items || participants.results || [];
                    }
                  }

                  console.log("获取到的参与者列表:", participants);

                  // 2. 查找手机号匹配的参与者
                  const matchingParticipants = participants.filter(p =>
                    p.phone === claimPhone ||
                    p.name === `用户${claimPhone.slice(-4)}` ||
                    p.participant_id === claimPhone
                  );

                  console.log("匹配的参与者:", matchingParticipants);

                  // 3. 获取所有中奖记录
                  const recordsResponse = await marketingGameApi.getDrawRecords(gameId);
                  let allRecords = [];

                  if (recordsResponse.success) {
                    allRecords = recordsResponse.data || [];
                    if (!Array.isArray(allRecords)) {
                      allRecords = allRecords.items || allRecords.results || [];
                    }
                  }

                  console.log("获取到的所有记录:", allRecords);

                  // 4. 过滤出该手机号的中奖记录
                  let winRecords = [];

                  // 4.1 先通过参与者ID匹配
                  if (matchingParticipants.length > 0) {
                    const participantIds = matchingParticipants.map(p => p.id);
                    winRecords = allRecords.filter(record => {
                      // 检查是否中奖
                      const isWin = record.is_win === true ||
                                   record.is_winner === true ||
                                   record.is_win === 'true' ||
                                   record.is_winner === 'true' ||
                                   record.status === 'win' ||
                                   (record.prize && !record.prize.is_default);

                      // 检查是否匹配参与者ID
                      const matchesParticipant = participantIds.includes(record.participant_id);

                      return isWin && matchesParticipant;
                    });
                  }

                  // 4.2 如果通过参与者ID没有找到，尝试直接通过手机号匹配
                  if (winRecords.length === 0) {
                    winRecords = allRecords.filter(record => {
                      // 检查是否中奖
                      const isWin = record.is_win === true ||
                                   record.is_winner === true ||
                                   record.is_win === 'true' ||
                                   record.is_winner === 'true' ||
                                   record.status === 'win' ||
                                   (record.prize && !record.prize.is_default);

                      // 兼容多种手机号字段
                      const matchesPhone =
                        // 如果record.participant存在且有phone字段
                        (record.participant && record.participant.phone === claimPhone) ||
                        // 如果record直接有phone字段
                        record.phone === claimPhone ||
                        // 如果record有user_phone字段
                        record.user_phone === claimPhone ||
                        // 如果record.participant_id是手机号
                        record.participant_id === claimPhone ||
                        // 如果record.user_id是手机号
                        record.user_id === claimPhone;

                      return isWin && matchesPhone;
                    });
                  }

                  console.log("找到的中奖记录:", winRecords);

                  if (winRecords.length > 0) {
                    // 处理记录，确保所有必要字段都存在
                    const processedRecords = winRecords.map(record => {
                      // 处理参与者信息
                      let participantInfo = record.participant || {};
                      if (!participantInfo.name) {
                        participantInfo.name = record.participant_name || `用户${claimPhone.slice(-4)}`;
                      }
                      if (!participantInfo.phone) {
                        participantInfo.phone = claimPhone;
                      }

                      // 处理奖品信息
                      let prizeInfo = record.prize || {};

                      // 如果有prize_id但没有完整的prize信息，尝试从prizes列表中获取
                      if (record.prize_id && (!prizeInfo.name || prizeInfo.name === '未知奖品')) {
                        // 尝试多种匹配方式，确保能找到奖品
                        let matchingPrize = prizes.find(p => p.id === record.prize_id);

                        // 如果没找到，尝试字符串比较
                        if (!matchingPrize) {
                          matchingPrize = prizes.find(p => String(p.id) === String(record.prize_id));
                        }

                        // 如果还没找到，尝试数字比较
                        if (!matchingPrize) {
                          matchingPrize = prizes.find(p => Number(p.id) === Number(record.prize_id));
                        }

                        if (matchingPrize) {
                          console.log("从prizes列表中找到匹配的奖品:", matchingPrize);
                          prizeInfo = { ...matchingPrize };
                        } else {
                          console.log("未找到匹配的奖品，prize_id:", record.prize_id, "可用奖品:", prizes);
                        }
                      }

                      if (!prizeInfo.name) {
                        prizeInfo.name = record.prize_name || '未知奖品';
                      }
                      if (prizeInfo.is_default === undefined) {
                        prizeInfo.is_default = record.is_default || false;
                      }
                      if (!prizeInfo.image_url) {
                        prizeInfo.image_url = record.prize_image || DEFAULT_PRIZE_ICONS[prizeInfo.is_default ? 'thanks' : 'gift'];
                      }
                      if (prizeInfo.value === undefined) {
                        prizeInfo.value = record.prize_value || 0;
                      }
                      if (!prizeInfo.description) {
                        prizeInfo.description = record.prize_description || '';
                      }

                      return {
                        ...record,
                        id: record.id || `record_${Math.random()}`,
                        created_at: record.created_at || record.draw_time || new Date().toISOString(),
                        participant: participantInfo,
                        prize: prizeInfo,
                        is_win: true, // 确保标记为中奖
                        claim_status: record.claim_status || 'unclaimed' // 确保有领取状态
                      };
                    });

                    message.success('查询成功，您有中奖记录！');

                    // 不更新全局中奖记录列表，而是显示一个临时的用户中奖记录弹窗
                    // 创建一个新的Modal来显示用户的中奖记录
                    Modal.success({
                      title: '您的中奖记录',
                      width: 600,
                      content: (
                        <List
                          itemLayout="horizontal"
                          dataSource={processedRecords
                            .sort((a, b) => new Date(b.created_at || b.draw_time) - new Date(a.created_at || a.draw_time))
                          }
                          renderItem={item => (
                            <List.Item
                              actions={[
                                item.claim_status === 'unclaimed' && !item.prize?.is_default && (
                                  <Button
                                    type="primary"
                                    size="small"
                                    onClick={async () => {
                                      try {
                                        // 更新领取状态
                                        const response = await marketingGameApi.updateRecord(gameId, item.id, {
                                          claim_status: 'claimed',
                                          claim_time: new Date().toISOString()
                                        });

                                        if (response.success) {
                                          message.success('奖品领取成功！');

                                          // 刷新中奖记录列表
                                          fetchGameData();

                                          // 关闭当前弹窗
                                          Modal.destroyAll();
                                        } else {
                                          message.error(response.message || '领取失败');
                                        }
                                      } catch (error) {
                                        console.error('Error claiming prize:', error);
                                        message.error('领取失败，请稍后再试');
                                      }
                                    }}
                                  >
                                    领取奖品
                                  </Button>
                                )
                              ].filter(Boolean)}
                            >
                              <List.Item.Meta
                                avatar={
                                  <Avatar
                                    src={item.prize?.image_url || DEFAULT_PRIZE_ICONS[item.prize?.is_default ? 'thanks' : 'gift']}
                                    style={{
                                      border: item.prize?.is_default ? 'none' : '2px solid #52c41a',
                                      padding: '2px',
                                      backgroundColor: '#f9f9f9'
                                    }}
                                  />
                                }
                                title={
                                  <span style={{ color: item.prize?.is_default ? '#999' : '#1890ff' }}>
                                    奖品: <strong>{item.prize?.name || '未知奖品'}</strong>
                                    <Tag color="success" style={{ marginLeft: '8px' }}>已中奖</Tag>
                                    {item.claim_status && (
                                      <Tag
                                        color={item.claim_status === 'claimed' ? 'green' : 'orange'}
                                        style={{ marginLeft: '8px' }}
                                      >
                                        {item.claim_status === 'claimed' ? '已领取' : '未领取'}
                                      </Tag>
                                    )}
                                  </span>
                                }
                                description={
                                  <>
                                    <div>用户: {
                                      // 如果有手机号，显示后四位
                                      item.participant?.phone ?
                                        `${item.participant.name || '用户'}(${item.participant.phone.slice(-4)})` :
                                        (item.participant?.name || item.user_name || '匿名用户')
                                    }</div>
                                    <div>时间: {formatToChineseTime(item.created_at || item.draw_time)}</div>
                                    {item.prize?.description && <div>描述: {item.prize.description}</div>}
                                    {item.prize?.value > 0 && <div>价值: ¥{(item.prize.value || 0).toFixed(2)}</div>}
                                    {item.claim_time && <div>领取时间: {formatToChineseTime(item.claim_time)}</div>}
                                  </>
                                }
                              />
                            </List.Item>
                          )}
                          locale={{
                            emptyText: <Empty description="暂无中奖记录" />
                          }}
                        />
                      ),
                      onOk() {
                        // 关闭领奖弹窗
                        setShowClaimPrize(false);
                      },
                    });

                    // 关闭领奖弹窗
                    setShowClaimPrize(false);
                  } else {
                    message.warning('未找到该手机号的中奖记录');
                  }
                } catch (error) {
                  console.error('Error claiming prize:', error);
                  message.error('查询失败，请稍后再试');
                } finally {
                  setClaimingPrize(false);
                }
              }}
            >
              查询中奖记录
            </Button>
          </div>
        </Form>
      </Modal>
    </div>
  );
};

export default WheelOfFortune;
