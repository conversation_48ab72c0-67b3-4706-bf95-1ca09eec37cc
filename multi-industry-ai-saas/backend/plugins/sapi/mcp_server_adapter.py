#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
MCP服务器适配器

负责管理MCP服务器的生命周期，包括启动、停止和状态监控
支持最新的MCP协议规范，包括stdio、SSE和streamhttp通信方式
"""

import os
import json
import logging
import subprocess
import requests
import asyncio
import shutil
import tempfile
import time
import uuid
import socket # <--- 添加此行
import jwt    # <--- 添加此行，用于生成系统令牌
from datetime import datetime, timedelta # MODIFIED: Ensure timedelta is imported
from typing import Dict, Any, List, Tuple, Optional, Union, Callable
from pathlib import Path
import docker # Added
import io       # Added
import tarfile  # Added
from jose import jwt as jose_jwt # MODIFIED: Use jose.jwt for consistency

logger = logging.getLogger(__name__)

class MCPServerAdapter:
    """
    MCP服务器适配器

    支持最新的MCP协议规范，包括:
    - stdio: 标准输入输出通信
    - SSE: Server-Sent Events通信
    - streamhttp: 流式HTTP通信
    - 支持图片等媒体类型
    """

    def __init__(self):
        """初始化适配器"""
        self.servers = {}  # 服务器ID -> 服务器配置
        self.running_servers = {}  # 服务器ID -> 运行状态
        self.processes = {}  # 服务器ID -> 进程对象

        # 使用新的模板目录结构
        templates_dir = Path(__file__).parent / "templates"
        self.server_templates = {
            "stdio": templates_dir / "mcp" / "stdio",
            "streamhttp": templates_dir / "mcp" / "streamhttp"
            # No longer an explicit "sse" template type
        }

    def register_server(self, server_config: Dict[str, Any]) -> bool:
        """
        注册服务器

        Args:
            server_config: 服务器配置

        Returns:
            bool: 是否注册成功
        """
        server_id = server_config.get("id")
        if not server_id:
            logger.error("服务器配置中缺少ID")
            return False

        self.servers[server_id] = server_config
        logger.info(f"注册服务器: {server_id}")
        return True

    def unregister_server(self, server_id: str) -> bool:
        """
        注销服务器

        Args:
            server_id: 服务器ID

        Returns:
            bool: 是否注销成功
        """
        if server_id in self.servers:
            del self.servers[server_id]
            logger.info(f"注销服务器: {server_id}")
            return True
        return False

    async def create_server(self, server_config: Dict[str, Any]) -> Tuple[bool, str, Optional[str]]:
        """
        创建MCP服务器

        Args:
            server_config: 服务器配置，包含以下字段:
                - name: 服务器名称
                - description: 服务器描述
                - transport_types: 通信方式列表 (stdio, sse, streamhttp)
                - api_endpoints: API端点列表或映射配置
                - env: 环境变量

        Returns:
            Tuple[bool, str, Optional[str]]: (成功标志, 日志, 服务器ID)
        """
        try:
            # 生成服务器ID
            server_id = server_config.get("id")
            if not server_id:
                return False, "服务器配置中缺少ID", None

            # 获取通信方式
            transport_types = server_config.get("transport_types", ["streamhttp"])
            if not isinstance(transport_types, list):
                transport_types = [transport_types]
            if "sse" in transport_types: # If "sse" is still lingering in config, treat as streamhttp
                transport_types = [tt if tt != "sse" else "streamhttp" for tt in transport_types]
                if "streamhttp" not in transport_types: # ensure streamhttp is present if sse was the only one
                     transport_types.append("streamhttp")
                # Remove duplicates if any introduced
                seen = set()
                transport_types = [x for x in transport_types if not (x in seen or seen.add(x))]
                server_config["transport_types"] = transport_types # Update server_config as well

            # 使用第一个通信方式作为主要通信方式
            primary_transport_type = transport_types[0] if transport_types else "streamhttp"
            if primary_transport_type not in ["stdio", "streamhttp"]: # Removed "sse" from valid primary types
                return False, f"不支持的主要通信方式: {primary_transport_type}, 必须是 'stdio' 或 'streamhttp'", None

            # 创建服务器目录
            install_dir = os.path.abspath(f"mcp_servers/{server_id}")
            os.makedirs(install_dir, exist_ok=True)

            # 获取模板目录
            template_dir = self.server_templates.get(primary_transport_type)
            if not template_dir.exists():
                # 创建模板目录
                os.makedirs(template_dir, exist_ok=True)

            # 检查模板文件是否存在，如果不存在，就创建模板文件
            main_py = template_dir / "main.py"
            if not main_py.exists():
                # 创建模板文件
                await self._create_template_files(primary_transport_type, template_dir)

            # 创建 mcp 目录和 base.py 文件
            mcp_dir = os.path.join(install_dir, "mcp")
            os.makedirs(mcp_dir, exist_ok=True)

            # 创建 __init__.py 文件，使 mcp 成为一个 Python 包
            init_py = os.path.join(mcp_dir, "__init__.py")
            with open(init_py, "w") as f:
                f.write("# MCP 包\n")

            # 复制 base.py 文件
            base_py = os.path.join(mcp_dir, "base.py")
            base_template = Path(__file__).parent / "templates" / "mcp" / "base.py"
            if base_template.exists():
                shutil.copy(base_template, base_py)
                logger.info(f"已复制 base.py 文件: {base_template} -> {base_py}")
            else:
                # 如果模板文件不存在，记录错误
                logger.error(f"找不到 base.py 模板文件: {base_template}")
                # 尝试从其他位置复制
                alternative_paths = [
                    Path(__file__).parent / "templates" / "mcp" / "base" / "base.py",
                    Path(__file__).parent / "templates" / "base.py"
                ]
                for alt_path in alternative_paths:
                    if alt_path.exists():
                        logger.info(f"使用替代模板: {alt_path}")
                        shutil.copy(alt_path, base_py)
                        logger.info(f"已复制 base.py 文件: {alt_path} -> {base_py}")
                        break
                else:
                    # 如果所有路径都失败，抛出异常
                    error_msg = f"无法找到 base.py 模板文件: {base_template} 或任何替代路径"
                    logger.error(error_msg)
                    raise FileNotFoundError(error_msg)

            # 复制 load_tools.py
            load_tools_py = os.path.join(install_dir, "load_tools.py")
            load_tools_template = Path(__file__).parent / "templates" / "mcp" / "load_tools.py"
            if load_tools_template.exists():
                shutil.copy(load_tools_template, load_tools_py)
            else:
                # 如果模板文件不存在，使用内置模板
                with open(load_tools_py, "w") as f:
                    f.write(self._get_load_tools_template())

            # 创建 main.py - 使用多协议模板
            main_py = os.path.join(install_dir, "main.py")
            multi_protocol_template = Path(__file__).parent / "templates" / "mcp" / "multi_protocol.py"
            if multi_protocol_template.exists():
                shutil.copy(multi_protocol_template, main_py)
            else:
                # 如果多协议模板不存在，使用主要通信方式的模板
                if primary_transport_type == "stdio":
                    with open(main_py, "w") as f:
                        f.write(self._get_stdio_template())
                elif primary_transport_type == "streamhttp":
                    # If SSE is chosen, multi_protocol.py should handle it or default.
                    # For safety, ensure multi_protocol.py is copied.
                    # shutil.copy(multi_protocol_template, main_py) # Already handled by the general case below
                    # Fallback to streamhttp template if multi_protocol.py doesn't explicitly handle 'sse' as primary
                    # This specific SSE template logic is now removed in favor of multi_protocol.py handling
                    # For now, let multi_protocol.py decide. If it was an explicit SSE server type,
                    # this would point to a dedicated SSE main.py.
                    # Since we are using multi_protocol.py for StreamHTTP (which includes SSE for events),
                    # this explicit SSE server type is less relevant.
                    # We are removing dedicated SSE server type selection.
                    # Defaulting to streamhttp's logic if SSE is somehow still selected.
                    logger.warning(f"SSE was selected as primary transport, but dedicated SSE template is removed. Relying on multi_protocol.py or defaulting to streamhttp logic.")
                    # The warning for SSE selection should be removed as SSE is no longer a valid primary_transport_type here.
                    # The logic will now fall into the 'else' for streamhttp.
                    with open(main_py, "w") as f:
                        f.write(self._get_streamhttp_template())
                else:  # streamhttp or default
                    with open(main_py, "w") as f:
                        f.write(self._get_streamhttp_template())

            # 复制 requirements.txt
            requirements_txt = os.path.join(install_dir, "requirements.txt")
            requirements_template = Path(__file__).parent / "templates" / "mcp" / "requirements.txt"
            if requirements_template.exists():
                shutil.copy(requirements_template, requirements_txt)
            else:
                # 如果模板文件不存在，创建一个简单的版本
                with open(requirements_txt, "w") as f:
                    f.write("fastapi>=0.95.0\n")
                    f.write("uvicorn>=0.21.1\n")
                    f.write("pydantic>=2.0.0\n")
                    f.write("requests>=2.28.2\n")
                    f.write("python-multipart>=0.0.5\n")
                    f.write("aiofiles>=0.8.0\n")
                    f.write("redis>=4.5.5\n")
                    f.write("sse-starlette>=1.6.1\n")  # 支持 SSE 协议 (StreamHTTP uses this for event stream part)
                    # Removed python-jose here as it's not directly used by the simplest server templates
                    # It will be in the main requirements.txt if needed by the mcp instance code itself.

            # 创建配置文件
            config_file = os.path.join(install_dir, "config.json")
            with open(config_file, "w") as f:
                json.dump({
                    "name": server_config.get("name", "MCP Server"),
                    "description": server_config.get("description", ""),
                    "transport_types": transport_types,
                    "env": server_config.get("env", {})
                }, f, indent=2)

            # 创建启动脚本
            start_script = os.path.join(install_dir, "start.sh")
            # Determine primary_transport_type from server_config for use in the script
            primary_transport_type_in_script = server_config.get("transport_types", ["streamhttp"])[0]

            start_script_content = f"""#!/bin/sh

echo 'start.sh execution started.' > /app/startup_debug.log

# Determine Python command
if command -v python3.10 > /dev/null 2>&1; then
    PYTHON_CMD="python3.10"
elif command -v python3 > /dev/null 2>&1; then
    PYTHON_CMD="python3"
elif command -v python > /dev/null 2>&1; then
    PYTHON_CMD="python"
else
    echo 'Error: Python interpreter not found.' >> /app/startup_debug.log
    exit 1
fi
echo "Using Python command: $PYTHON_CMD" >> /app/startup_debug.log

# Install dependencies
if [ -f "requirements.txt" ]; then
    echo 'Installing dependencies...' >> /app/startup_debug.log
    $PYTHON_CMD -m pip install --no-cache-dir -r requirements.txt >> /app/pip_install.log 2>&1
    if [ $? -ne 0 ]; then
        echo 'pip install failed. Check /app/pip_install.log' >> /app/startup_debug.log
        # Optional: exit 1
    else:
        echo 'Dependencies installed successfully.' >> /app/startup_debug.log
    fi
else
    echo 'requirements.txt not found, skipping pip install.' >> /app/startup_debug.log
fi

echo 'Configuring and starting server...' >> /app/startup_debug.log
# PRIMARY_TRANSPORT_TYPE is determined by Python and baked into this script logic
echo "Primary transport type for this instance: {primary_transport_type_in_script}" >> /app/startup_debug.log

if [ "{primary_transport_type_in_script}" = "stdio" ]; then
    echo 'Starting stdio server...' >> /app/startup_debug.log
    exec $PYTHON_CMD main.py
# ELIF for SSE is removed. SSE type is now handled as streamhttp or an invalid type earlier.
else  # streamhttp or default (covers cases where sse might have been passed and converted to streamhttp)
    echo 'Starting StreamHTTP server...' >> /app/startup_debug.log
    # multi_protocol.py, when handling SSE, will still run an HTTP server.
    # It requires a port. We will assign one dynamically if not provided.
    if [ -z "$PORT" ]; then
        echo "PORT environment variable not set for StreamHTTP/default mode, attempting to find a dynamic port..." >> /app/startup_debug.log
        PORT_CANDIDATE=$($PYTHON_CMD -c 'import socket; s=socket.socket(); s.bind(("", 0)); print(s.getsockname()[1]); s.close()')
        if [ -n "$PORT_CANDIDATE" ]; then
            export PORT="$PORT_CANDIDATE"
            echo "Dynamically assigned PORT=$PORT for StreamHTTP/default mode." >> /app/startup_debug.log
        else:
            echo "Failed to assign dynamic port for StreamHTTP/default mode. main.py might use a default or fail." >> /app/startup_debug.log
            # Fallback to a default if dynamic assignment fails, or let main.py handle it
            export PORT="8000" # Default fallback
            echo "Using default PORT=$PORT for StreamHTTP/default mode after dynamic assignment failure." >> /app/startup_debug.log
        fi
    else
        echo "Using environment PORT=$PORT for StreamHTTP/default mode." >> /app/startup_debug.log
        export PORT # Ensure it's exported
    fi
    exec $PYTHON_CMD main.py --http-port=$PORT
fi

echo "main.py exited with code: $?" >> /app/startup_debug.log
echo 'start.sh finished.' >> /app/startup_debug.log
"""
            with open(start_script, "w", newline='\n') as f: # CORRECTED: newline='\n' (single backslash)
                f.write(start_script_content)

            # 创建停止脚本
            stop_script = os.path.join(install_dir, "stop.sh")
            with open(stop_script, "w", newline='\n') as f: # CORRECTED: newline='\n' (single backslash)
                f.write("#!/bin/sh\n\n")
                f.write("# 停止MCP服务器\n")
                f.write("echo \"Stopping MCP server...\"\n\n")

                # 检查PID文件
                f.write("if [ -f \"pid.txt\" ]; then\n")
                f.write("    pid=$(cat pid.txt)\n")
                f.write("    if ps -p $pid > /dev/null; then\n")
                f.write("        echo \"Killing process $pid\"\n")
                f.write("        kill $pid\n")
                f.write("        rm pid.txt\n")
                f.write("    else\n")
                f.write("        echo \"Process $pid is not running\"\n")
                f.write("        rm pid.txt\n")
                f.write("    fi\n")
                f.write("else\n")
                f.write("    echo \"No pid.txt file found\"\n")
                f.write("fi\n\n")

                # 查找并杀死所有Python进程
                f.write("# 查找并杀死所有Python进程\n")
                f.write("echo \"Looking for Python processes...\"\n")
                f.write("pkill -f \"python.*main.py\" || echo \"No Python processes found\"\n\n")

                f.write("echo \"MCP server stopped\"\n")

            # 设置脚本权限
            os.chmod(start_script, 0o755)
            os.chmod(stop_script, 0o755)

            # ---> 新增：创建实例专属的 Dockerfile <----
            dockerfile_path = os.path.join(install_dir, "Dockerfile")
            with open(dockerfile_path, "w") as f:
                # 使用适合的Python基础镜像
                f.write("FROM python:3.10-slim\n\n")
                # 设置工作目录
                f.write("WORKDIR /app\n\n")
                # 复制所有实例文件到镜像中
                f.write("COPY . /app\n\n")
                # 添加调试命令，查看 COPY 后的文件列表和权限
                f.write("RUN ls -la /app\n\n")
                # 修改 RUN 命令以包含 curl 安装 和 apt 缓存清理
                f.write("RUN apt-get update && apt-get install -y curl && \
")
                f.write("    chmod +x /app/start.sh && \
")
                f.write("    python3.10 -m pip install --no-cache-dir -r requirements.txt && \
")
                f.write("    apt-get clean && rm -rf /var/lib/apt/lists/*\n\n")
                # 设置容器启动时执行的命令
                f.write('CMD ["/app/start.sh"]\n') # Reverted CMD
            logger.info(f"为实例 {server_id} 创建 Dockerfile 于: {dockerfile_path} (含调试步骤和curl安装, CMD REVERTED TO /app/start.sh)")
            # ---> Dockerfile 创建结束 <----

            # 注册服务器
            server_config["install_dir"] = install_dir
            self.register_server(server_config)

            # 复制 utils.py
            utils_py = os.path.join(install_dir, "utils.py")
            utils_template = Path(__file__).parent / "templates" / "mcp" / "utils.py"
            if utils_template.exists():
                shutil.copy(utils_template, utils_py)

            # 复制 tools.py (新增)
            tools_py_path = os.path.join(install_dir, "tools.py")
            tools_template_path = Path(__file__).parent / "templates" / "mcp" / "tools.py"
            if tools_template_path.exists():
                shutil.copy(tools_template_path, tools_py_path)
                logger.info(f"已复制 tools.py 文件: {tools_template_path} -> {tools_py_path}")
            else:
                logger.warning(f"找不到 tools.py 模板文件: {tools_template_path}, MCP实例可能无法加载自定义Python工具。")

            return True, f"MCP服务器创建成功: {server_id}", server_id
        except Exception as e:
            logger.error(f"创建MCP服务器失败: {str(e)}")
            return False, f"创建MCP服务器失败: {str(e)}", None

    async def _create_template_files(self, transport_type: str, template_dir: Path) -> None:
        """
        创建模板文件

        Args:
            transport_type: 主要通信方式
            template_dir: 模板目录
        """
        # 创建 mcp 目录和 base.py 文件
        mcp_dir = template_dir / "mcp"
        if not mcp_dir.exists():
            mcp_dir.mkdir(parents=True, exist_ok=True)

        # 创建 __init__.py 文件，使 mcp 成为一个 Python 包
        init_py = mcp_dir / "__init__.py"
        if not init_py.exists():
            with open(init_py, "w") as f:
                f.write("# MCP 包\n")

        # 复制 base.py 文件
        base_py = mcp_dir / "base.py"
        base_template = Path(__file__).parent / "templates" / "mcp" / "base.py"
        if base_template.exists():
            import shutil
            shutil.copy(base_template, base_py)
        else:
            # 如果模板文件不存在，记录错误
            logger.error(f"找不到 base.py 模板文件: {base_template}")
            # 尝试从其他位置复制
            alternative_paths = [
                Path(__file__).parent / "templates" / "mcp" / "base" / "base.py",
                Path(__file__).parent / "templates" / "base.py"
            ]
            for alt_path in alternative_paths:
                if alt_path.exists():
                    logger.info(f"使用替代模板: {alt_path}")
                    import shutil
                    shutil.copy(alt_path, base_py)
                    break
            else:
                # 如果所有路径都失败，抛出异常
                raise FileNotFoundError(f"无法找到 base.py 模板文件: {base_template} 或任何替代路径")

        # 复制load_tools.py
        load_tools_py = template_dir / "load_tools.py"
        load_tools_template = Path(__file__).parent / "templates" / "load_tools.py"
        if load_tools_template.exists():
            import shutil
            shutil.copy(load_tools_template, load_tools_py)
        else:
            # 如果模板文件不存在，创建一个简单的版本
            with open(load_tools_py, "w") as f:
                f.write(self._get_load_tools_template())

        # 复制utils.py
        utils_py = template_dir / "utils.py"
        utils_template = Path(__file__).parent / "templates" / "utils.py" # Corrected path for utils.py template
        if utils_template.exists():
            import shutil
            shutil.copy(utils_template, utils_py)
        else:
            # Fallback for utils.py if not in templates/utils.py but in templates/mcp/utils.py
            utils_template_alt = Path(__file__).parent / "templates" / "mcp" / "utils.py"
            if utils_template_alt.exists():
                import shutil
                shutil.copy(utils_template_alt, utils_py)
                logger.info(f"Copied utils.py from alternative path: {utils_template_alt} to {utils_py}")
            else:
                logger.warning(f"utils.py template not found at {utils_template} or {utils_template_alt}")

        # 复制 tools.py (新增)
        tools_py_path_template_target = template_dir / "tools.py" # Target path within the template structure being built
        tools_template_source = Path(__file__).parent / "templates" / "mcp" / "tools.py" # Source of the actual tools.py template
        if tools_template_source.exists():
            import shutil
            shutil.copy(tools_template_source, tools_py_path_template_target)
            logger.info(f"已复制 tools.py 模板文件到模板目录: {tools_template_source} -> {tools_py_path_template_target}")
        else:
            logger.warning(f"找不到 tools.py 模板源文件: {tools_template_source}，无法将其复制到模板目录。")

        # 创建main.py
        main_py = template_dir / "main.py"

        # 根据通信方式选择模板
        if transport_type == "stdio":
            # 复制stdio模板
            stdio_template = Path(__file__).parent / "templates" / "mcp" / "stdio" / "main.py"
            if stdio_template.exists():
                import shutil
                shutil.copy(stdio_template, main_py)
            else:
                # 如果模板文件不存在，使用内置模板
                with open(main_py, "w") as f:
                    f.write(self._get_stdio_template())
        elif transport_type == "streamhttp":
            # SSE is no longer a distinct server template type.
            # Fallback to streamhttp template logic, as multi_protocol.py handles StreamHTTP (which includes SSE for events).
            logger.warning("Dedicated SSE template type is removed. Defaulting to StreamHTTP template logic in _create_template_files.")
            streamhttp_template = Path(__file__).parent / "templates" / "mcp" / "streamhttp" / "main.py" # Should be multi_protocol.py
            actual_main_template = Path(__file__).parent / "templates" / "mcp" / "multi_protocol.py"
            if actual_main_template.exists():
                import shutil
                shutil.copy(actual_main_template, main_py)
                logger.info(f"Copied multi_protocol.py for SSE transport type to {main_py}") # This log is now misleading
            elif streamhttp_template.exists(): # Fallback if multi_protocol.py is missing for some reason
                import shutil
                shutil.copy(streamhttp_template, main_py)
                logger.warning(f"Copied streamhttp/main.py (fallback) for SSE transport type to {main_py}") # Misleading
            else:
                with open(main_py, "w") as f:
                    f.write(self._get_streamhttp_template()) # Further fallback to inline template
        else:  # streamhttp
            # 复制streamhttp模板 (should be multi_protocol.py)
            # streamhttp_template = Path(__file__).parent / "templates" / "mcp" / "streamhttp" / "main.py"
            actual_main_template = Path(__file__).parent / "templates" / "mcp" / "multi_protocol.py"
            if actual_main_template.exists():
                import shutil
                shutil.copy(actual_main_template, main_py)
            else:
                # 如果模板文件不存在，使用内置模板
                with open(main_py, "w") as f:
                    f.write(self._get_streamhttp_template()) # This should ideally be the multi_protocol content

        # 创建requirements.txt
        requirements_txt = template_dir / "requirements.txt"
        requirements_template = Path(__file__).parent / "templates" / "mcp" / "requirements.txt"
        if requirements_template.exists():
            import shutil
            shutil.copy(requirements_template, requirements_txt)
        else:
            # 如果模板文件不存在，创建一个简单的版本
            with open(requirements_txt, "w") as f:
                f.write("fastapi>=0.95.0\n")
                f.write("uvicorn>=0.21.1\n")
                f.write("pydantic>=2.0.0\n")
                f.write("requests>=2.28.2\n")
                f.write("python-multipart>=0.0.5\n")
                f.write("aiofiles>=0.8.0\n")
                f.write("redis>=4.5.5\n")
                # sse-starlette is always needed for streamhttp's event stream part
                f.write("sse-starlette>=1.6.1\n")

    def _get_stdio_template(self) -> str:
        """获取stdio模板"""
        template_path = os.path.join(os.path.dirname(__file__), "templates", "mcp", "stdio", "main.py")
        with open(template_path, "r") as f:
            return f.read()

    def _get_sse_template(self) -> str:
        """获取SSE模板 (DEPRECATED - Use multi_protocol.py or streamhttp)"""
        # This method should be removed.
        logger.error("_get_sse_template is deprecated and should no longer be called. Raising NotImplementedError.")
        raise NotImplementedError("_get_sse_template is deprecated and removed.")

    def _get_base_template(self) -> str:
        """获取base模板"""
        template_path = os.path.join(os.path.dirname(__file__), "templates", "mcp", "base", "main.py")
        with open(template_path, "r") as f:
            return f.read()

    def _get_load_tools_template(self) -> str:
        """获取load_tools模板"""
        template_path = os.path.join(os.path.dirname(__file__), "templates", "load_tools.py") # Corrected path
        try:
            with open(template_path, "r") as f:
                return f.read()
        except FileNotFoundError:
            logger.error(f"找不到load_tools模板文件: {template_path}")
            return ""

    def _get_streamhttp_template(self) -> str:
        """获取streamhttp模板"""
        template_path = os.path.join(os.path.dirname(__file__), "templates", "mcp", "streamhttp", "main.py")
        with open(template_path, "r") as f:
            return f.read()

    def _check_process_exists(self, process_id: int) -> bool:
        """
        检查进程是否存在

        Args:
            process_id: 进程ID

        Returns:
            bool: 进程是否存在
        """
        try:
            os.kill(process_id, 0)
            return True
        except OSError:
            return False

    def _sync_docker_build_and_log(self, client: docker.DockerClient, tar_buffer: io.BytesIO, image_tag: str, logger_instance: logging.Logger) -> Tuple[bool, List[str]]:
        """Synchronous helper to build Docker image and capture logs."""
        # sourcery skip: assign-if ट्रू, boolean-িক্যাল-identity, remove-redundant-boolean
        logger_instance.info(f"SYNC_HELPER: Starting build for {image_tag}")
        build_success = False
        error_messages = []
        final_log_lines_for_error = []

        try:
            resp_generator = client.api.build(
                fileobj=tar_buffer,
                custom_context=True,
                encoding='gzip',
                tag=image_tag,
                rm=True,
                forcerm=True,
                dockerfile='Dockerfile'
            )

            for log_chunk in resp_generator:
                lines = log_chunk.decode('utf-8').splitlines()
                for line in lines:
                    try:
                        log_entry = json.loads(line)
                        if 'stream' in log_entry:
                            log_msg = log_entry['stream'].strip()
                            if log_msg:
                                logger_instance.info(f"[BUILD_LOG_SYNC] {log_msg}")
                                final_log_lines_for_error.append(log_msg)
                        if 'errorDetail' in log_entry:
                            err_msg = log_entry['errorDetail']['message']
                            logger_instance.error(f"[BUILD_ERROR_SYNC] {err_msg}")
                            error_messages.append(err_msg)
                            final_log_lines_for_error.append(f"ERROR: {err_msg}")
                        # Consider successful tagging as a positive sign but not final proof
                        if 'status' in log_entry and 'Successfully tagged' in log_entry['status']:
                            # build_success = True # Tentative success
                            pass # Don't set to true yet, wait for image_get
                    except json.JSONDecodeError:
                        logger_instance.warning(f"SYNC_HELPER:无法解析的构建日志行: {line}")
                        final_log_lines_for_error.append(line)

            # After iterating all logs, verify image existence
            try:
                client.images.get(image_tag) # This is a synchronous call
                logger_instance.info(f"SYNC_HELPER:成功构建并找到 Docker 镜像: {image_tag}")
                build_success = True # Confirmed success
            except docker.errors.ImageNotFound:
                logger_instance.error(f"SYNC_HELPER:构建 Docker 镜像失败: {image_tag} (镜像构建后未找到)")
                if not error_messages: # If no specific error was caught from logs, add a generic one
                    error_messages.append(f"镜像 {image_tag} 构建后未找到.")
                logger_instance.error(f"SYNC_HELPER:最后相关的构建日志: {' --- '.join(final_log_lines_for_error[-5:])}")
                build_success = False
            except docker.errors.APIError as e_img_get:
                logger_instance.error(f"SYNC_HELPER:检查镜像 {image_tag} 时发生API错误: {e_img_get}")
                error_messages.append(f"检查镜像时发生API错误: {str(e_img_get)}")
                build_success = False

        except docker.errors.BuildError as e_build:
            logger_instance.error(f"SYNC_HELPER:Docker 构建 API 错误 (BuildError): {e_build}")
            error_messages.append(f"BuildError: {str(e_build)}")
            if hasattr(e_build, 'build_log') and e_build.build_log:
                for log_line_dict in e_build.build_log:
                    if 'error' in log_line_dict:
                        detailed_error = log_line_dict['error']
                        logger_instance.error(f"[DOCKER_BUILD_FAIL_LOG_BuildError_SYNC] {detailed_error}")
                        error_messages.append(detailed_error)
                    elif 'stream' in log_line_dict:
                        logger_instance.info(f"[DOCKER_BUILD_FAIL_LOG_BuildError_SYNC] {log_line_dict['stream'].strip()}")
            else:
                logger_instance.error(f"SYNC_HELPER:BuildError 没有详细的 build_log: {e_build.msg}")
            build_success = False
        except docker.errors.APIError as e_api:
            logger_instance.error(f"SYNC_HELPER:Docker API 通用错误: {e_api}")
            error_messages.append(f"APIError: {str(e_api)}")
            build_success = False
        except Exception as e_exc:
            logger_instance.error(f"SYNC_HELPER:构建 Docker 镜像时发生未知错误: {e_exc}", exc_info=True)
            error_messages.append(f"未知构建错误: {str(e_exc)}")
            build_success = False

        logger_instance.info(f"SYNC_HELPER: Build process for {image_tag} finished. Success: {build_success}")
        return build_success, error_messages

    async def start_server(self, server_id: str, auth_header_override: Optional[str] = None) -> Tuple[bool, str]:
        """
        启动服务器

        Args:
            server_id: 服务器ID
            auth_header_override: 覆盖服务器配置中的auth_header

        Returns:
            Tuple[bool, str]: (成功标志, 日志)
        """
        if server_id not in self.servers:
            return False, f"服务器不存在: {server_id}"

        server_config = self.servers[server_id]

        # 检查是否是模拟安装
        if server_config.get("simulation", False) and server_config.get("use_system_ai", False):
            # 使用系统AI模块
            self.running_servers[server_id] = {
                "status": "running",
                "started_at": datetime.now().isoformat(),
                "use_system_ai": True
            }
            logger.info(f"启动服务器(使用系统AI模块): {server_id}")
            return True, f"服务器启动成功(使用系统AI模块): {server_id}"

        # 如果不是模拟安装或不使用系统AI模块，根据部署模式启动服务器
        try:
            install_dir = server_config.get("install_dir")
            if not install_dir or not os.path.exists(install_dir):
                return False, f"服务器安装目录不存在: {install_dir}"

            # 获取部署模式
            deployment_mode = server_config.get("deployment_mode", "local")

            # 准备环境变量，特别是 API_BASE_URL
            env_vars_from_config = server_config.get("env") # 首先直接获取
            if isinstance(env_vars_from_config, str):
                try:
                    env_vars_for_instance = json.loads(env_vars_from_config)
                except json.JSONDecodeError:
                    logger.warning(f"无法解析服务器 {server_id} 的现有环境变量字符串: {env_vars_from_config}")
                    env_vars_for_instance = {} # 初始化为空字典
            elif isinstance(env_vars_from_config, dict):
                env_vars_for_instance = env_vars_from_config # 如果已经是字典，直接使用
            else:
                # 包括 env_vars_from_config 是 None 的情况，或其他非str、非dict类型
                if env_vars_from_config is not None: # Log if it was something unexpected but not None
                    logger.warning(f"服务器 {server_id} 的 env 字段类型不是预期的字符串或字典: {type(env_vars_from_config)}，将初始化为空环境变量。")
                env_vars_for_instance = {} # 确保初始化为空字典

            # 确保 API_BASE_URL 指向正确的后端服务
            # 通过 MCP_TARGET_API_BASE_URL 环境变量配置，在不同环境下自动适配正确的后端容器名
            # 生产环境: retail-ai-saas-backend:8000，开发环境: retail-ai-saas-backend-dev:8000
            correct_api_base_url = os.environ.get("MCP_TARGET_API_BASE_URL", "http://retail-ai-saas-backend:8000")
            env_vars_for_instance["API_BASE_URL"] = correct_api_base_url
            logger.info(f"为MCP服务器实例 {server_id} 设置 API_BASE_URL 为: {correct_api_base_url}")
            
            # 传递SECRET_KEY环境变量，确保JWT签名验证一致
            # This block correctly sources SECRET_KEY into env_vars_for_instance["SECRET_KEY"]
            # from the main backend's environment or falls back to a default.
            try:
                secret_key = os.environ.get("SECRET_KEY")
                if secret_key:
                    env_vars_for_instance["SECRET_KEY"] = secret_key
                    logger.info(f"为MCP服务器实例 {server_id} 传递 SECRET_KEY 环境变量")
                else:
                    default_key = "retail-ai-saas-default-secret-key-for-development-only"
                    env_vars_for_instance["SECRET_KEY"] = default_key
                    logger.warning(f"未找到 SECRET_KEY，MCP实例将使用默认开发密钥。") # Clarified warning
            except Exception as e:
                logger.error(f"处理 SECRET_KEY 时出错: {str(e)}")
                default_key = "retail-ai-saas-default-secret-key-for-development-only"
                env_vars_for_instance["SECRET_KEY"] = default_key
                logger.warning(f"处理 SECRET_KEY 出错，MCP实例将使用默认开发密钥。") # Clarified warning
                
            # --- REPLACED SYSTEM_TOKEN LOGIC ---
            # 获取项目ID，用于SYSTEM_TOKEN的payload
            project_id_for_token = str(server_config.get("project_id", "system"))
            current_secret_key_for_signing = env_vars_for_instance.get("SECRET_KEY")

            if not current_secret_key_for_signing:
                logger.error(f"CRITICAL: 为服务器 {server_id} 签名SYSTEM_TOKEN所需的SECRET_KEY缺失。工具加载将失败。")
                system_token = "CRITICAL_ERROR_SECRET_KEY_MISSING_FOR_SYSTEM_TOKEN_SIGNING"
            elif current_secret_key_for_signing == "retail-ai-saas-default-secret-key-for-development-only":
                logger.warning(f"警告: 为服务器 {server_id} 生成SYSTEM_TOKEN时使用的是默认开发密钥。")
                # Proceed with default key for development if explicitly set.
                try:
                    system_token_payload = {
                        "sub": f"mcp_system_instance_{server_id}", 
                        "user_id": "mcp_system_user",
                        "project_id": project_id_for_token,
                        "type": "system_instance_token",
                        "scope": "tools:read", 
                        "exp": datetime.utcnow() + timedelta(hours=24), # Longer expiry for dev default key token
                        "iat": datetime.utcnow()
                    }
                    system_token = jose_jwt.encode(
                        system_token_payload,
                        current_secret_key_for_signing,
                        algorithm="HS256"
                    )
                    logger.info(f"为MCP服务器实例 {server_id} 使用默认开发SECRET_KEY动态生成了内部SYSTEM_TOKEN。")
                except Exception as e_jwt_gen:
                    logger.error(f"为MCP服务器实例 {server_id} (使用默认密钥)动态生成内部SYSTEM_TOKEN失败: {e_jwt_gen}", exc_info=True)
                    system_token = f"ERROR_GENERATING_SYSTEM_TOKEN_WITH_DEFAULT_KEY_{server_id}"
            else:
                try:
                    system_token_payload = {
                        "sub": f"mcp_system_instance_{server_id}", 
                        "user_id": "mcp_system_user", 
                        "project_id": project_id_for_token,
                        "type": "system_instance_token", # To distinguish from other system/user tokens
                        "scope": "tools:read", # Scope needed by load_tools.py
                        "exp": datetime.utcnow() + timedelta(hours=1), # Token valid for 1 hour
                        "iat": datetime.utcnow()
                    }
                    system_token = jose_jwt.encode(
                        system_token_payload,
                        current_secret_key_for_signing,
                        algorithm="HS256"
                    )
                    logger.info(f"为MCP服务器实例 {server_id} 使用当前SECRET_KEY动态生成了内部SYSTEM_TOKEN。")
                except Exception as e_jwt_gen:
                    logger.error(f"为MCP服务器实例 {server_id} 动态生成内部SYSTEM_TOKEN失败: {e_jwt_gen}", exc_info=True)
                    system_token = f"ERROR_GENERATING_SYSTEM_TOKEN_{server_id}"
            
            env_vars_for_instance["SYSTEM_TOKEN"] = system_token
            logger.info(f"为MCP服务器实例 {server_id} 设置SYSTEM_TOKEN环境变量。")
            # --- END OF REPLACED SYSTEM_TOKEN LOGIC ---

            # 根据部署模式启动服务器
            if deployment_mode == "docker":
                try: # <<<< Outer try for the entire Docker logic path >>>>
                # Docker 部署模式
                    # 检查是否在 Docker 容器内运行
                    in_docker = os.path.exists('/.dockerenv')
                    docker_socket_exists = os.path.exists('/var/run/docker.sock')

                    # 改进的Docker可用性检查
                    docker_available = False

                    if in_docker:
                        # 在Docker容器内部，检查是否挂载了Docker socket
                        if docker_socket_exists:
                            logger.info("在Docker容器内运行，已挂载Docker socket")
                            # 尝试直接调用docker命令来验证
                            try:
                                docker_test = subprocess.run(
                                    ["docker", "version"],
                                    capture_output=True,
                                    text=True,
                                    timeout=10
                                )
                                if docker_test.returncode == 0:
                                    logger.info("Docker命令验证成功，可以使用Docker")
                                    docker_available = True
                                else:
                                    logger.warning(f"Docker命令验证失败，返回码: {docker_test.returncode}")
                                    logger.warning(f"错误输出: {docker_test.stderr}")
                                    # 即使验证失败，如果socket存在，我们仍然尝试使用Python Docker客户端
                                    try:
                                        client = docker.from_env()
                                        client.ping()
                                        logger.info("Python Docker客户端验证成功")
                                        docker_available = True
                                    except Exception as e:
                                        logger.warning(f"Python Docker客户端验证失败: {str(e)}")
                                        docker_available = False
                            except subprocess.TimeoutExpired:
                                logger.warning("Docker命令验证超时")
                                # 尝试使用Python Docker客户端
                                try:
                                    client = docker.from_env()
                                    client.ping()
                                    logger.info("Python Docker客户端验证成功")
                                    docker_available = True
                                except Exception as e:
                                    logger.warning(f"Python Docker客户端验证失败: {str(e)}")
                                    docker_available = False
                            except Exception as e:
                                logger.warning(f"Docker命令验证异常: {str(e)}")
                                # 尝试使用Python Docker客户端
                                try:
                                    client = docker.from_env()
                                    client.ping()
                                    logger.info("Python Docker客户端验证成功")
                                    docker_available = True
                                except Exception as e2:
                                    logger.warning(f"Python Docker客户端验证失败: {str(e2)}")
                                    docker_available = False
                        else:
                            logger.warning("在Docker容器内运行，但没有挂载Docker socket，无法使用Docker")
                            docker_available = False
                    else:
                        # 不在Docker容器内部，检查Docker客户端是否可用
                        # 首先尝试which命令
                        try:
                            docker_check = subprocess.run(
                                ["which", "docker"],
                                capture_output=True,
                                text=True,
                                timeout=5
                            )
                            if docker_check.returncode == 0:
                                logger.info("which docker 检查通过")
                                # 进一步验证docker命令是否真的可用
                                docker_test = subprocess.run(
                                    ["docker", "version"],
                                    capture_output=True,
                                    text=True,
                                    timeout=10
                                )
                                if docker_test.returncode == 0:
                                    logger.info("Docker命令验证成功")
                                    docker_available = True
                                else:
                                    logger.warning(f"Docker命令验证失败: {docker_test.stderr}")
                                    docker_available = False
                            else:
                                logger.warning("which docker 检查失败，尝试直接调用docker命令")
                                # which命令失败，尝试直接调用docker
                                docker_test = subprocess.run(
                                    ["docker", "version"],
                                    capture_output=True,
                                    text=True,
                                    timeout=10
                                )
                                if docker_test.returncode == 0:
                                    logger.info("直接调用Docker命令成功")
                                    docker_available = True
                                else:
                                    logger.warning("直接调用Docker命令也失败")
                                    docker_available = False
                        except Exception as e:
                            logger.warning(f"Docker可用性检查异常: {str(e)}")
                            # 最后尝试使用Python Docker客户端
                            try:
                                client = docker.from_env()
                                client.ping()
                                logger.info("Python Docker客户端验证成功")
                                docker_available = True
                            except Exception as e2:
                                logger.warning(f"Python Docker客户端验证失败: {str(e2)}")
                                docker_available = False

                    # 检查是否可以使用 Docker
                    if not docker_available:
                       logger.error("Docker 命令不可用，无法继续 Docker 部署模式。")
                       return False, "Docker 命令不可用"
                    else:
                        logger.info("Docker 命令可用，继续 Docker 部署模式。")

                    # 使用 env_vars_for_instance 替代旧的 env
                    s = socket.socket()
                    s.bind(('', 0))
                    port = s.getsockname()[1]
                    s.close()

                    # 更新环境变量中的端口
                    env_vars_for_instance["PORT"] = str(port)

                    # 构建 Docker 运行命令
                    container_name = f"mcp-server-{server_id}"
                    
                    # 动态检测网络名称，Docker Compose会添加项目前缀
                    network_name = "retail-ai-saas-network"  # 默认值
                    try:
                        # 尝试获取所有网络并找到匹配的
                        networks_output = subprocess.run(
                            ["docker", "network", "ls", "--format", "{{.Name}}"],
                            capture_output=True,
                            text=True
                        )
                        if networks_output.returncode == 0:
                            networks = networks_output.stdout.strip().split('\n')
                            # 查找包含 retail-ai-saas-network 的网络
                            for network in networks:
                                if 'retail-ai-saas-network' in network:
                                    network_name = network
                                    logger.info(f"检测到网络: {network_name}")
                                    break
                            else:
                                # 如果没找到，尝试查找包含 retail-ai-saas 的网络
                                for network in networks:
                                    if 'retail-ai-saas' in network and 'default' in network:
                                        network_name = network
                                        logger.info(f"使用默认网络: {network_name}")
                                        break
                    except Exception as e:
                        logger.warning(f"检测网络名称失败，使用默认值: {e}")
                    
                    logger.info(f"最终使用的网络名称: {network_name}")

                    # 基本 Docker 命令
                    docker_base_cmd = ["docker"]

                    # 如果在 Docker 容器内运行，需要特殊处理
                    if in_docker:
                        logger.info("在 Docker 容器内运行，使用挂载的 Docker socket")

                        # 检查是否有 DOCKER_HOST 环境变量
                        docker_host = os.environ.get('DOCKER_HOST', '')
                        if docker_host:
                            docker_base_cmd.extend(["--host", docker_host])

                        # 获取当前容器的网络
                        try:
                            # 获取当前容器ID
                            with open('/proc/self/cgroup', 'r') as f:
                                for line in f:
                                    if 'docker' in line:
                                        current_container_id = line.split('/')[-1].strip()
                                        break
                                else:
                                    current_container_id = None

                            if current_container_id:
                                # 获取当前容器的网络
                                inspect_cmd = docker_base_cmd + ["inspect", "--format", "{{json .NetworkSettings.Networks}}", current_container_id]
                                inspect_process = subprocess.run(
                                    inspect_cmd,
                                    capture_output=True,
                                    text=True
                                )

                                if inspect_process.returncode == 0:
                                    networks = json.loads(inspect_process.stdout)
                                    if networks:
                                        # 检查是否包含我们期望的网络
                                        target_networks = [net for net in networks.keys() if 'retail-ai-saas-network' in net]
                                        if target_networks:
                                            # 使用找到的网络名称
                                            network_name = target_networks[0]
                                            logger.info(f"确认使用检测到的网络: {network_name}")
                                        else:
                                            # 查找包含 retail-ai-saas 的任何网络
                                            fallback_networks = [net for net in networks.keys() if 'retail-ai-saas' in net]
                                            if fallback_networks:
                                                network_name = fallback_networks[0]
                                                logger.info(f"使用备选网络: {network_name}")
                                            else:
                                                # 使用第一个网络作为最后备选
                                                detected_network = list(networks.keys())[0]
                                                logger.warning(f"未找到retail-ai-saas相关网络，使用检测到的网络: {detected_network}")
                                                network_name = detected_network
                        except Exception as e:
                            logger.warning(f"获取当前容器网络失败: {str(e)}，将使用默认网络名称")

                    # 获取安装目录的绝对路径
                    abs_install_dir = os.path.abspath(install_dir)
                    logger.info(f"安装目录绝对路径: {abs_install_dir}")

                    # 检查当前工作目录
                    cwd = os.getcwd()
                    logger.info(f"当前工作目录: {cwd}")

                    # 检查是否在Docker容器中运行
                    in_container = os.path.exists('/.dockerenv')
                    logger.info(f"是否在Docker容器中运行: {in_container}")

                    # 如果在Docker容器中运行，使用相对路径
                    if in_container:
                        # 使用相对路径
                        rel_install_dir = os.path.relpath(abs_install_dir, '/app')
                        mount_path = f"/app/{rel_install_dir}"
                        logger.info(f"在Docker容器中运行，使用相对路径: {mount_path}")
                    else:
                        # 使用绝对路径作为挂载路径
                        mount_path = abs_install_dir
                        logger.info(f"不在Docker容器中运行，使用绝对路径: {mount_path}")

                    # ---> 修改：读取 MCP_HOST_INSTANCES_PATH 并解析 <----
                    mcp_container_instances_path = os.environ.get("MCP_CONTAINER_INSTANCES_PATH") # Corrected var name
                    logger.critical(f"CRITICAL_LOG: 读取到 MCP_CONTAINER_INSTANCES_PATH 环境变量值为: {mcp_container_instances_path}")

                    if not mcp_container_instances_path:
                        logger.error("CRITICAL_ERROR: MCP_CONTAINER_INSTANCES_PATH 环境变量未设置或为空! Docker 构建将失败。")
                        return False, "MCP_CONTAINER_INSTANCES_PATH 环境变量缺失"

                    # 构建特定实例在容器内的完整路径
                    # install_dir 来自 server_config.get("install_dir")，它通常是 'mcp_servers/<server_id>'
                    # 或在create_server中被设置为 os.path.abspath(f"mcp_servers/{server_id}") 这在容器内会解析为 /app/mcp_servers/<server_id>
                    if os.path.isabs(install_dir) and install_dir.startswith('/app/'):
                        container_instance_path = install_dir
                    else: # More robustly handle if install_dir is just <server_id> or mcp_servers/<server_id>
                        if install_dir.startswith('mcp_servers/'):
                             # Assuming mcp_container_instances_path is /app/mcp_servers, and install_dir is mcp_servers/uuid
                             # We want /app/mcp_servers/uuid
                             # A safer way is to take the basename (uuid) from install_dir
                             instance_id_only = os.path.basename(install_dir)
                             container_instance_path = os.path.join(mcp_container_instances_path, instance_id_only)
                        elif server_id in install_dir: # if install_dir is just the server_id or contains it
                             instance_id_only = server_id
                             container_instance_path = os.path.join(mcp_container_instances_path, instance_id_only)
                        else:
                             logger.error(f"无法从 install_dir '{install_dir}' 和 server_id '{server_id}' 推断出实例路径")
                             return False, "无法确定容器内实例路径"


                    container_instance_path = container_instance_path.replace('\\\\', '/') # Ensure correct slashes
                    logger.info(f"INFO_LOG: MCP 服务实例在容器内的路径 (用于打包): {container_instance_path}")

                    if not os.path.isdir(container_instance_path):
                        logger.error(f"CRITICAL_ERROR: 容器内实例路径 {container_instance_path} 不是一个目录或不存在！")
                        files_in_app_mcp_servers = []
                        if os.path.exists(mcp_container_instances_path):
                            files_in_app_mcp_servers = os.listdir(mcp_container_instances_path)
                        logger.error(f"DEBUG: 内容 /app/mcp_servers/: {files_in_app_mcp_servers}")
                        return False, f"容器内实例路径无效: {container_instance_path}"

                    # Docker 客户端初始化
                    try:
                        # client = docker.from_env() # Original sync call
                        client = await asyncio.to_thread(docker.from_env)
                    except docker.errors.DockerException as e:
                        logger.error(f"初始化 Docker 客户端失败: {e}. 请确保 Docker正在运行且socket已正确挂载。")
                        return False, f"Docker客户端初始化失败: {str(e)}"
                    except Exception as e_init_client: # Catch any other init errors
                        logger.error(f"初始化 Docker 客户端时发生未知错误: {e_init_client}", exc_info=True)
                        return False, f"Docker客户端初始化时未知错误: {e_init_client}"


                    # ---> 新增：在启动前强制移除同名旧容器 --- >
                    try:
                        # old_container = client.containers.get(container_name) # Original sync call
                        old_container = await asyncio.to_thread(client.containers.get, container_name)
                        logger.info(f"尝试移除可能存在的旧容器: {container_name}")
                        # old_container.remove(force=True) # Original sync call
                        await asyncio.to_thread(old_container.remove, force=True)
                        logger.info(f"成功移除旧容器: {container_name}")
                    except docker.errors.NotFound:
                        logger.info(f"旧容器 {container_name} 不存在，无需移除。")
                    except docker.errors.APIError as e:
                        logger.warning(f"移除旧容器 {container_name} 时遇到API错误: {e} (这可能不是严重问题，将继续尝试构建)")
                    except Exception as e_rm_cont: # Catch other potential errors during removal
                        logger.warning(f"移除旧容器 {container_name} 时发生其他错误: {e_rm_cont}", exc_info=True)
                    # <--- 移除旧容器结束 --->

                    # ---> 新增：构建实例专属的 Docker 镜像 (使用 tar 流) <----
                    image_tag = f"mcp-server-image:{server_id}"
                    logger.info(f"开始构建 Docker 镜像: {image_tag} 从容器内路径: {container_instance_path}")

                    build_success = False
                    build_error_messages = []
                    try:
                        tar_buffer = io.BytesIO()
                        # It's important that tarfile operations, especially if heavy, are also non-blocking.
                        # For simplicity here, assuming tar creation is fast enough or also wrapped if it becomes a bottleneck.
                        # However, true async tar libraries are rare. This part remains synchronous for now.
                        with tarfile.open(fileobj=tar_buffer, mode='w:gz') as tar:
                            tar.add(container_instance_path, arcname='.')

                        tar_buffer.seek(0)

                        # ---- DEBUGGING: Save tar stream to file ----
                        debug_tar_path = f"/tmp/mcp_context_{server_id}.tar.gz"
                        try:
                            with open(debug_tar_path, "wb") as debug_f:
                                debug_f.write(tar_buffer.getvalue())
                            logger.info(f"DEBUG: Saved build context tar stream to {debug_tar_path}")
                            # Rewind again is crucial if getvalue() potentially moved the pointer (though it usually doesn't for BytesIO)
                            # Let's seek again just to be safe *after* saving.
                            tar_buffer.seek(0)
                        except Exception as e_debug_tar:
                            logger.warning(f"DEBUG: Failed to save debug tar stream: {e_debug_tar}")
                        # ---- END DEBUGGING ----

                        logger.info(f"准备通过流式传输构建上下文，大小约: {len(tar_buffer.getvalue()) / 1024:.2f} KB")

                        # Call the synchronous helper function using asyncio.to_thread
                        build_success, build_error_messages = await asyncio.to_thread(
                            self._sync_docker_build_and_log,
                            client,          # Docker client
                            tar_buffer,      # BytesIO object with tar.gz data
                            image_tag,       # Image tag
                            logger           # Pass the logger instance for use in the sync helper
                        )

                        if not build_success:
                            # Error logging is now primarily handled within _sync_docker_build_and_log
                            # and detailed error messages are in build_error_messages
                            logger.error(f"最终判定构建 Docker 镜像失败: {image_tag}. 错误: {'; '.join(build_error_messages)}")
                            return False, f"构建 Docker 镜像失败: {server_id}. Details: {'; '.join(build_error_messages)}"

                    except tarfile.TarError as e_tar:
                        logger.error(f"创建 tar 包失败: {e_tar}")
                        return False, f"创建构建上下文失败 (tar): {server_id}"
                    # BuildError and APIError from client.api.build are now handled inside _sync_docker_build_and_log
                    except Exception as e_exc: # Catch other unexpected errors during tar or pre-build phase
                        logger.error(f"构建 Docker 镜像的准备阶段发生未知错误: {e_exc}", exc_info=True)
                        return False, f"构建 Docker 镜像准备阶段未知错误: {server_id}"
                    # ---> 镜像构建结束 <----

                    # 使用 Docker SDK 运行容器
                    try:
                        parsed_env_sdk = {}
                        idx = 0
                        # 使用 env_vars_for_instance 构建 parsed_env_sdk，而不是 env_list
                        # 因为 env_list 是为 subprocess.Popen 格式化的，而 SDK 需要字典
                        parsed_env_sdk.update(env_vars_for_instance) # 直接使用准备好的字典

                        # 从 server_config 获取 project_id 并添加到环境变量
                        project_id_for_env = server_config.get("project_id")

                        # 确保关键的动态环境变量也在 parsed_env_sdk 中
                        parsed_env_sdk["PORT"] = str(port) # 确保是字符串
                        parsed_env_sdk["MCP_SERVER_ID"] = server_id
                        parsed_env_sdk["MCP_SERVER_NAME"] = server_config.get('name', 'MCP Server')
                        parsed_env_sdk["MCP_SERVICE_NAME"] = f"mcp-server-{server_id}"
                        # 从server_config获取transport_types，确保是逗号分隔的字符串
                        transport_types = server_config.get("transport_types", ["streamhttp"])
                        if not isinstance(transport_types, list):
                            transport_types = [transport_types]
                        parsed_env_sdk["MCP_TRANSPORT_TYPES"] = ",".join(transport_types) # 确保是逗号分隔字符串
                        parsed_env_sdk["PYTHONUNBUFFERED"] = "1"
                        
                        # 传递Redis相关环境变量
                        parsed_env_sdk["REDIS_HOST"] = os.environ.get("REDIS_HOST", "redis")
                        parsed_env_sdk["REDIS_PORT"] = os.environ.get("REDIS_PORT", "6379")
                        parsed_env_sdk["REDIS_DB"] = os.environ.get("REDIS_DB", "0")
                        # 如果有完整的REDIS_URL，也传递它
                        if "REDIS_URL" in os.environ:
                            parsed_env_sdk["REDIS_URL"] = os.environ["REDIS_URL"]
                        logger.info(f"为MCP容器 {server_id} 设置Redis连接: REDIS_HOST={parsed_env_sdk['REDIS_HOST']}, REDIS_PORT={parsed_env_sdk['REDIS_PORT']}, REDIS_DB={parsed_env_sdk['REDIS_DB']}")
                        
                        # 传递Traefik域名后缀配置
                        traefik_domain_suffix = os.environ.get("TRAEFIK_MCP_DOMAIN_SUFFIX", "localhost")
                        parsed_env_sdk["TRAEFIK_MCP_DOMAIN_SUFFIX"] = traefik_domain_suffix
                        logger.info(f"为MCP容器 {server_id} 设置Traefik域名后缀: {traefik_domain_suffix}")
                        
                        if project_id_for_env:
                            parsed_env_sdk["PROJECT_ID"] = str(project_id_for_env)
                            logger.info(f"Adding PROJECT_ID={str(project_id_for_env)} to parsed_env_sdk for Docker container {server_id}")
                        else:
                            logger.warning(f"PROJECT_ID not found in server_config for {server_id}. MCP server might not load project-specific tools correctly if it relies on this env var.")

                        logger.info(f"准备使用以下环境变量运行容器 (SDK): {parsed_env_sdk}")

                        # 创建Traefik标签，实现自动服务发现
                        # 注意：设置较低优先级，让文件配置的鉴权路由优先
                        # 获取域名后缀配置
                        traefik_domain_suffix = os.environ.get("TRAEFIK_MCP_DOMAIN_SUFFIX", "localhost")
                        traefik_labels = {
                            "traefik.enable": "true",
                            f"traefik.http.routers.mcp-{server_id}.rule": f"Host(`mcp-{server_id}.{traefik_domain_suffix}`)",
                            f"traefik.http.routers.mcp-{server_id}.priority": "300",  # 最高优先级，确保优于所有其他路由
                            f"traefik.http.services.mcp-{server_id}.loadbalancer.server.port": str(port),
                            # 添加鉴权中间件到Docker路由
                            f"traefik.http.routers.mcp-{server_id}.middlewares": "mcp-auth@file,sse-no-buffer@file"
                        }
                        
                        logger.info(f"为容器添加Traefik标签: {traefik_labels}")

                        # container = client.containers.run( # Original sync call
                        container = await asyncio.to_thread(
                            client.containers.run,
                            image=image_tag,
                            detach=True,
                            name=container_name,
                            network=network_name,
                            hostname=container_name,  # 新增，确保主机名与注册一致
                            ports={f'{port}/tcp': None}, # Let Docker assign a random host port
                            environment=parsed_env_sdk,
                            labels=traefik_labels, # 添加Traefik标签
                            restart_policy={"Name": "unless-stopped"}
                        )
                        logger.info(f"Docker 容器已启动，ID: {container.id}，内部端口: {port}")

                        # 获取 Docker 分配的实际主机端口
                        await asyncio.to_thread(container.reload)
                        host_port_mapping = container.attrs['NetworkSettings']['Ports'].get(f'{port}/tcp')
                        actual_host_port = None
                        if host_port_mapping and isinstance(host_port_mapping, list) and len(host_port_mapping) > 0:
                            actual_host_port_str = host_port_mapping[0]['HostPort']
                            if actual_host_port_str:
                                actual_host_port = int(actual_host_port_str)
                                logger.info(f"容器 {container.id} (服务 {server_id}) 监听于主机端口 {actual_host_port} (内部端口 {port})")

                                # 将主机端口作为环境变量传递给容器
                                try:
                                    # 使用docker exec命令设置环境变量
                                    env_cmd = docker_base_cmd + ["exec", container_name, "sh", "-c", f"echo 'export HOST_PORT={actual_host_port}' >> /app/.env"]
                                    env_process = subprocess.run(env_cmd, capture_output=True, text=True)
                                    if env_process.returncode == 0:
                                        logger.info(f"成功将主机端口 {actual_host_port} 写入容器 {container_name} 的环境文件")
                                    else:
                                        logger.error(f"无法将主机端口写入容器环境文件: {env_process.stderr}")
                                except Exception as e:
                                    logger.error(f"设置容器环境变量失败: {str(e)}")
                            else:
                                logger.error(f"无法从映射中获取 HostPort 字符串: {host_port_mapping}")
                        else:
                            logger.error(f"无法为容器 {container.id} (内部端口 {port}) 找到端口映射。映射数据: {container.attrs['NetworkSettings']['Ports']}")

                        if actual_host_port is None:
                            logger.error(f"无法确定容器 {container.id} 的主机端口 (内部端口 {port})。正在停止并移除容器。")
                            try:
                                await asyncio.to_thread(container.stop)
                                await asyncio.to_thread(container.remove)
                                logger.info(f"已停止并移除无法确定端口的容器 {container.id}")
                            except Exception as e_stop_rm:
                                logger.error(f"停止/移除无法确定端口的容器 {container.id} 时出错: {e_stop_rm}")
                            try:
                                logger.warning(f"尝试移除相关镜像: {image_tag}")
                                await asyncio.to_thread(client.images.remove, image=image_tag, force=True)
                            except Exception as e_img_rm:
                                logger.error(f"移除镜像 {image_tag} 失败: {e_img_rm}")
                            return False, f"无法确定服务 {server_id} 的主机端口"

                        # 首先初始化 running_servers 条目
                        self.running_servers[server_id] = {
                            "status": "starting", # 初始状态
                            "started_at": datetime.now().isoformat(),
                            "use_system_ai": server_config.get("use_system_ai", False),
                            "deployment_mode": "docker",
                            "port": port, # 使用内部端口进行健康检查
                            "host_port": actual_host_port # 外部端口用于外部访问
                        }

                        self.processes[server_id] = {
                            "type": "docker",
                            "container_id": container.id,
                            "container_name": container_name,
                            "port": port, # 内部端口用于容器间通信
                            "host_port": actual_host_port, # 外部端口用于外部访问
                            "internal_port": port # 保留内部端口信息以供参考
                            }
                        # self.running_servers[server_id]["port"] = port # 已在上面初始化时设置
                        # self.running_servers[server_id]["deployment_mode"] = "docker" # 已在上面初始化时设置
                        self.running_servers[server_id]["status"] = "running" # 更新状态为 running

                        logger.info(f"Docker 部署模式成功完成，服务器 {server_id} 已启动") # Added log
                        return True, f"服务器启动成功 (Docker): {server_id}" # Crucial return statement for success path

                    except docker.errors.APIError as e_run_api:
                        logger.error(f"使用 Docker SDK 启动容器失败: {e_run_api}")
                        try:
                            logger.warning(f"容器启动失败，尝试移除已构建的镜像: {image_tag}")
                            # client.images.remove(image=image_tag, force=True) # Original sync call
                            await asyncio.to_thread(client.images.remove, image=image_tag, force=True)
                        except Exception as e_img_rm:
                            logger.error(f"移除镜像 {image_tag} 失败: {e_img_rm}")
                        return False, f"启动 Docker 容器失败 (SDK): {server_id}"
                    except Exception as e_run_exc:
                        logger.error(f"使用 Docker SDK 启动容器时发生未知错误: {e_run_exc}", exc_info=True)
                        try:
                            logger.warning(f"容器启动未知错误，尝试移除已构建的镜像: {image_tag}")
                            # client.images.remove(image=image_tag, force=True) # Original sync call
                            await asyncio.to_thread(client.images.remove, image=image_tag, force=True)
                        except Exception as e_img_rm:
                            logger.error(f"移除镜像 {image_tag} 失败: {e_img_rm}")
                        return False, f"启动 Docker 容器时未知错误 (SDK): {server_id}"
                except Exception as e_docker_main: # Catch-all for the *entire* docker deployment mode operations
                    logger.error(f"Docker 部署模式执行期间发生意外总错误: {e_docker_main}", exc_info=True)
                    # Fallback or specific error return for docker mode failure
                    # Original code might have attempted a fallback to local mode here.
                    # For now, we ensure it returns an error to prevent SyntaxError with following elif.
                    return False, f"Docker 部署模式失败: {str(e_docker_main)}"

            elif deployment_mode == "npm":
                # NPM 部署模式
                npm_package = server_config.get("npm_package", "mcp-server")
                npm_version = server_config.get("npm_version", "latest")

                # 构建 NPM 运行命令
                npm_cmd = [
                    "cd", install_dir, "&&",
                    "npm", "install", f"{npm_package}@{npm_version}", "&&",
                    "npx", npm_package
                ]

                # 运行 NPM 命令
                logger.info(f"启动 NPM 包: {' '.join(npm_cmd)}")
                process = subprocess.Popen(
                    " ".join(npm_cmd),
                    shell=True,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True
                )

                # 保存进程对象
                self.processes[server_id] = {
                    "type": "npm",
                    "process": process
                }
            else:
                # 本地部署模式
                start_script = os.path.join(install_dir, "start.sh")
                if not os.path.exists(start_script):
                    return False, f"启动脚本不存在: {start_script}"

                # 执行启动脚本
                process = subprocess.Popen(
                    [start_script],
                    cwd=install_dir,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True
                )

                # 保存进程对象
                self.processes[server_id] = {
                    "type": "local",
                    "process": process
                }

            # 更新服务器状态
            self.running_servers[server_id] = {
                "status": "running",
                "started_at": datetime.now().isoformat(),
                "use_system_ai": False,
                "deployment_mode": deployment_mode
            }

            logger.info(f"启动服务器: {server_id}")
            return True, f"服务器启动成功: {server_id}"
        except Exception as e:
            logger.error(f"启动服务器失败: {str(e)}")
            return False, f"启动服务器失败: {str(e)}"

    async def stop_server(self, server_id: str) -> Tuple[bool, str]:
        """
        停止服务器

        Args:
            server_id: 服务器ID

        Returns:
            Tuple[bool, str]: (成功标志, 日志)
        """
        if server_id not in self.servers:
            return False, f"服务器不存在: {server_id}"

        if server_id not in self.running_servers:
            return False, f"服务器未运行: {server_id}"

        server_config = self.servers[server_id]

        # 检查是否是模拟安装
        if server_config.get("simulation", False) and server_config.get("use_system_ai", False):
            # 使用系统AI模块
            del self.running_servers[server_id]
            logger.info(f"停止服务器(使用系统AI模块): {server_id}")
            return True, f"服务器停止成功(使用系统AI模块): {server_id}"

        # 如果不是模拟安装或不使用系统AI模块，根据部署模式停止服务器
        try:
            # 获取部署模式
            deployment_mode = server_config.get("deployment_mode", "local")

            # 如果有进程对象，根据部署模式停止进程
            if server_id in self.processes:
                process_info = self.processes[server_id]
                process_type = process_info.get("type", "local")

                if process_type == "docker":
                    # Docker 部署模式
                    try:
                        # 检查是否在 Docker 容器内运行
                        in_docker = os.path.exists('/.dockerenv')
                        docker_socket_exists = os.path.exists('/var/run/docker.sock')

                        # 在Docker容器内部，即使没有Docker客户端，只要挂载了Docker socket，就可以使用Docker
                        docker_available = True

                        if in_docker:
                            # 在Docker容器内部，检查是否挂载了Docker socket
                            if docker_socket_exists:
                                logger.info("在Docker容器内运行，已挂载Docker socket，可以使用Docker")
                                docker_available = True
                            else:
                                logger.warning("在Docker容器内运行，但没有挂载Docker socket，无法使用Docker")
                                docker_available = False
                        else:
                            # 不在Docker容器内部，检查Docker客户端是否可用
                            docker_check = subprocess.run(
                                ["which", "docker"],
                                capture_output=True,
                                text=True
                            )
                            docker_available = docker_check.returncode == 0
                            if not docker_available:
                                logger.warning("Docker客户端不可用，无法使用Docker")

                        # 检查是否可以使用 Docker
                        if not docker_available:
                            logger.warning("Docker 命令不可用，无法停止 Docker 容器")
                        elif in_docker and not docker_socket_exists: # This implies docker_available is true
                            logger.warning("在 Docker 容器内运行，但没有挂载 Docker socket，无法停止 Docker 容器")
                        else:
                            container_name = process_info.get("container_name")
                            if container_name:
                                # 基本 Docker 命令
                                docker_base_cmd = ["docker"]

                                # 如果在 Docker 容器内运行，需要特殊处理
                                if in_docker:
                                    logger.info("在 Docker 容器内运行，使用挂载的 Docker socket")

                                    # 检查是否有 DOCKER_HOST 环境变量
                                    docker_host = os.environ.get('DOCKER_HOST', '')
                                    if docker_host:
                                        docker_base_cmd.extend(["--host", docker_host])

                                # 停止 Docker 容器
                                logger.info(f"停止 Docker 容器: {container_name}")

                                # 首先检查容器是否存在
                                inspect_cmd = docker_base_cmd + ["inspect", container_name]
                                logger.info(f"检查容器是否存在: {' '.join(inspect_cmd)}")
                                inspect_process = subprocess.run(
                                    inspect_cmd,
                                    capture_output=True,
                                    text=True
                                )

                                if inspect_process.returncode != 0:
                                    logger.warning(f"容器 {container_name} 不存在，无需停止")
                                else:
                                    # 容器存在，尝试停止
                                    stop_cmd = docker_base_cmd + ["stop", container_name]
                                    logger.info(f"执行命令: {' '.join(stop_cmd)}")
                                    stop_process = subprocess.run(
                                        stop_cmd,
                                        capture_output=True,
                                        text=True
                                    )
                                    logger.info(f"停止结果: {stop_process.stdout}, 错误: {stop_process.stderr}")

                                # 无论容器是否存在或停止是否成功，都尝试强制删除容器
                                logger.info(f"尝试强制删除 Docker 容器: {container_name}")
                                rm_cmd = docker_base_cmd + ["rm", "-f", container_name]
                                logger.info(f"执行命令: {' '.join(rm_cmd)}")
                                rm_process = subprocess.run(
                                    rm_cmd,
                                    capture_output=True,
                                    text=True
                                )
                                logger.info(f"删除结果: {rm_process.stdout}, 错误: {rm_process.stderr}")

                                if rm_process.returncode != 0:
                                    logger.warning(f"强制删除 Docker 容器失败: {rm_process.stderr}")

                                    # 尝试使用 docker kill 命令强制终止容器
                                    logger.info(f"尝试使用 kill 命令强制终止容器: {container_name}")
                                    kill_cmd = docker_base_cmd + ["kill", container_name]
                                    logger.info(f"执行命令: {' '.join(kill_cmd)}")
                                    kill_process = subprocess.run(
                                        kill_cmd,
                                        capture_output=True,
                                        text=True
                                    )
                                    logger.info(f"终止结果: {kill_process.stdout}, 错误: {kill_process.stderr}")

                                    # 再次尝试删除容器
                                    logger.info(f"再次尝试删除容器: {container_name}")
                                    rm_cmd = docker_base_cmd + ["rm", "-f", container_name]
                                    rm_process = subprocess.run(
                                        rm_cmd,
                                        capture_output=True,
                                        text=True
                                    )
                                    logger.info(f"删除结果: {rm_process.stdout}, 错误: {rm_process.stderr}")

                                # 即使删除失败，也继续执行，不要阻止流程
                    except Exception as e:
                        logger.error(f"停止 Docker 容器失败: {str(e)}")
                elif process_type == "npm":
                    # NPM 部署模式
                    process = process_info.get("process")
                    if process and process.poll() is None:  # 进程仍在运行
                        process.terminate()
                        try:
                            process.wait(timeout=5)  # 等待进程终止
                        except subprocess.TimeoutExpired:
                            process.kill()  # 如果超时，强制终止
                else:
                    # 本地部署模式
                    process = process_info.get("process")
                    if process and process.poll() is None:  # 进程仍在运行
                        process.terminate()
                        try:
                            process.wait(timeout=5)  # 等待进程终止
                        except subprocess.TimeoutExpired:
                            process.kill()  # 如果超时，强制终止

                # 移除进程对象
                del self.processes[server_id]

            # 执行停止脚本（作为备份方案）
            install_dir = server_config.get("install_dir")
            if install_dir and os.path.exists(install_dir):
                stop_script = os.path.join(install_dir, "stop.sh")
                if os.path.exists(stop_script):
                    # 执行停止脚本
                    logger.info(f"执行停止脚本: {stop_script}")
                    process = subprocess.run(
                        [stop_script],
                        cwd=install_dir,
                        capture_output=True,
                        text=True
                    )

                    if process.returncode != 0:
                        logger.warning(f"停止脚本执行失败: {process.stderr}")

            # 无论脚本执行结果如何，都从运行列表中移除服务器
            if server_id in self.running_servers:
                del self.running_servers[server_id]

            # 更新数据库中的进程记录
            try:
                from .models import MCPServerProcess
                from db.database import AsyncSessionLocal
                import asyncio

                # 使用异步会话
                async def update_process_record():
                    async with AsyncSessionLocal() as session:
                        # 查询最新的进程记录
                        from sqlalchemy import desc
                        from sqlalchemy.future import select

                        result = await session.execute(
                            select(MCPServerProcess).filter(
                                MCPServerProcess.server_id == server_id,
                                MCPServerProcess.status == "running"
                            ).order_by(desc(MCPServerProcess.created_at))
                        )
                        process = result.scalars().first()

                        if process:
                            # 更新进程状态
                            process.status = "stopped"
                            process.stopped_at = datetime.now()
                            await session.commit()
                            logger.info(f"更新进程记录成功: {process.id}")

                # 执行异步函数
                try:
                    asyncio.run(update_process_record())
                except Exception as e:
                    logger.warning(f"更新进程记录失败: {str(e)}")

            except Exception as e:
                logger.warning(f"更新进程记录失败: {str(e)}")

            logger.info(f"停止服务器: {server_id}")
            return True, f"服务器停止成功: {server_id}"
        except Exception as e:
            logger.error(f"停止服务器失败: {str(e)}")
            # 即使出错，也尝试从运行列表中移除服务器
            if server_id in self.running_servers:
                del self.running_servers[server_id]
            return False, f"停止服务器失败: {str(e)}"

    async def update_server_status(self, server_id: str, status: str) -> bool:
        """
        强制更新服务器状态

        Args:
            server_id: 服务器ID
            status: 新状态 (running, stopped, error)

        Returns:
            bool: 是否更新成功
        """
        try:
            # 更新内存中的状态
            if status == "running":
                if server_id not in self.running_servers:
                    self.running_servers[server_id] = {
                        "status": "running",
                        "started_at": datetime.now().isoformat(),
                        "use_system_ai": self.servers.get(server_id, {}).get("use_system_ai", False),
                        "deployment_mode": self.servers.get(server_id, {}).get("deployment_mode", "local")
                    }
            else:
                # 如果状态不是running，从运行列表中移除
                if server_id in self.running_servers:
                    del self.running_servers[server_id]

            logger.info(f"强制更新服务器状态: {server_id} -> {status}")
            return True
        except Exception as e:
            logger.error(f"更新服务器状态失败: {str(e)}")
            return False

    async def get_server_status(self, server_id: str) -> Dict[str, Any]:
        """
        获取服务器状态

        Args:
            server_id: 服务器ID

        Returns:
            Dict[str, Any]: 服务器状态
        """
        # 如果服务器不在内存中，尝试从数据库加载
        if server_id not in self.servers:
            try:
                # 从数据库中获取服务器信息
                from .models import MCPServer, MCPServerProcess
                from db.database import get_db
                from sqlalchemy.future import select

                # 使用异步会话
                async for session in get_db():
                    try:
                        # 查询服务器
                        result = await session.execute(select(MCPServer).filter(MCPServer.id == server_id))
                        server = result.scalars().first()
                        if server:
                            # 注册服务器到适配器
                            server_config = {
                                "id": str(server.id),
                                "name": server.name,
                                "env": json.loads(server.env) if server.env else {},
                                "install_dir": f"mcp_servers/{server_id}",
                                "simulation": server.simulation,
                                "use_system_ai": server.use_system_ai,
                                "deployment_mode": server.deployment_mode if hasattr(server, 'deployment_mode') else "local",
                                "transport_types": server.transport_types if hasattr(server, 'transport_types') else [server.transport_type]
                            }
                            self.register_server(server_config)
                            logger.info(f"从数据库加载服务器: {server_id}")

                            # 查询最新的进程记录
                            result = await session.execute(
                                select(MCPServerProcess)
                                .filter(MCPServerProcess.server_id == server_id)
                                .order_by(MCPServerProcess.created_at.desc())
                            )
                            process = result.scalars().first()

                            if process and process.status == "running" and not process.stopped_at:
                                # 如果有正在运行的进程记录，将服务器添加到运行列表
                                self.running_servers[server_id] = {
                                    "status": "running",
                                    "started_at": process.started_at.isoformat() if process.started_at else datetime.now().isoformat(),
                                    "use_system_ai": server.use_system_ai,
                                    "deployment_mode": server.deployment_mode if hasattr(server, 'deployment_mode') else "local"
                                }
                                logger.info(f"从数据库恢复服务器运行状态: {server_id}")
                        else:
                            return {"status": "not_found", "message": f"服务器不存在: {server_id}"}
                    except Exception as e:
                        logger.error(f"查询服务器失败: {str(e)}")
                        return {"status": "error", "message": f"查询服务器失败: {str(e)}"}
            except Exception as e:
                logger.error(f"从数据库加载服务器失败: {str(e)}")
                return {"status": "error", "message": f"服务器加载失败: {str(e)}"}

        # 检查服务器是否实际在运行
        server_config = self.servers[server_id]
        install_dir = server_config.get("install_dir")

        # 如果服务器在运行列表中，先检查进程是否仍然存在
        if server_id in self.running_servers:
            process_info = self.processes.get(server_id, {})
            process_type = process_info.get("type", "local")

            if process_type == "docker":
                # Docker 部署模式，检查容器是否仍在运行
                container_name = process_info.get("container_name")
                if container_name:
                    try:
                        # 检查是否在 Docker 容器内运行
                        in_docker = os.path.exists('/.dockerenv')
                        docker_socket_exists = os.path.exists('/var/run/docker.sock')

                        # 在Docker容器内部，即使没有Docker客户端，只要挂载了Docker socket，就可以使用Docker
                        docker_available = True

                        if in_docker:
                            # 在Docker容器内部，检查是否挂载了Docker socket
                            if docker_socket_exists:
                                logger.info("在Docker容器内运行，已挂载Docker socket，可以使用Docker")
                                docker_available = True
                            else:
                                logger.warning("在Docker容器内运行，但没有挂载Docker socket，无法使用Docker")
                                docker_available = False
                        else:
                            # 不在Docker容器内部，检查Docker客户端是否可用
                            docker_check = subprocess.run(
                                ["which", "docker"],
                                capture_output=True,
                                text=True
                            )
                            docker_available = docker_check.returncode == 0
                            if not docker_available:
                                logger.warning("Docker客户端不可用，无法使用Docker")

                        if docker_available:
                            # 基本 Docker 命令
                            docker_base_cmd = ["docker"]

                            # 如果在 Docker 容器内运行，需要特殊处理
                            if in_docker:
                                # 检查是否有 DOCKER_HOST 环境变量
                                docker_host = os.environ.get('DOCKER_HOST', '')
                                if docker_host:
                                    docker_base_cmd.extend(["--host", docker_host])

                            # 使用 docker ps 命令检查容器状态，更可靠
                            ps_cmd = docker_base_cmd + ["ps", "-a", "--filter", f"name={container_name}", "--format", "{{.Status}}"]
                            logger.info(f"执行命令: {' '.join(ps_cmd)}")
                            inspect_process = subprocess.run(
                                ps_cmd,
                                capture_output=True,
                                text=True
                            )

                            if inspect_process.returncode == 0:
                                container_status = inspect_process.stdout.strip()
                                # docker ps 的状态输出通常以 "Up" 开头表示容器正在运行
                                if container_status and "Up" in container_status:
                                    # 容器正在运行，尝试健康检查
                                    try:
                                        # 尝试多种可能的主机名和端口
                                        hosts_to_try = ["localhost", "127.0.0.1", "0.0.0.0", "**********",
                                                       container_name, "host.docker.internal"]
                                        
                                        # 动态获取容器的内部端口
                                        ports_to_try = []
                                        try:
                                            # 从Docker容器信息中获取暴露的端口
                                            inspect_cmd = ["docker", "inspect", container_name, "--format", "{{json .Config.ExposedPorts}}"]
                                            inspect_result = subprocess.run(inspect_cmd, capture_output=True, text=True)
                                            if inspect_result.returncode == 0:
                                                import json
                                                exposed_ports = json.loads(inspect_result.stdout.strip())
                                                for port_spec in exposed_ports.keys():
                                                    if "/tcp" in port_spec:
                                                        internal_port = int(port_spec.split("/")[0])
                                                        ports_to_try.append(internal_port)
                                                        logger.info(f"从容器配置获取到内部端口: {internal_port}")
                                        except Exception as e:
                                            logger.warning(f"无法从容器配置获取端口: {str(e)}")
                                        
                                        # 如果无法从容器配置获取端口，使用存储的端口信息
                                        if not ports_to_try:
                                            port = process_info.get("port")
                                            if not port:
                                                port = env.get("PORT", 8000)
                                            ports_to_try = [port]
                                            
                                        # 添加常用的MCP服务器端口
                                        for common_port in [42665, 3000, 8000]:
                                            if common_port not in ports_to_try:
                                                ports_to_try.append(common_port)

                                        # 记录端口信息
                                        logger.info(f"使用端口: {ports_to_try}")

                                        # 尝试获取本地IP和Docker网关IP
                                        try:
                                            import socket
                                            s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
                                            s.connect(("*******", 80))
                                            local_ip = s.getsockname()[0]
                                            s.close()
                                            # 添加本地IP
                                            hosts_to_try.append(local_ip)

                                            # 尝试获取Docker网关IP
                                            ip_parts = local_ip.split('.')
                                            if len(ip_parts) == 4:
                                                gateway_ip = f"{ip_parts[0]}.{ip_parts[1]}.{ip_parts[2]}.1"
                                                hosts_to_try.append(gateway_ip)
                                        except:
                                            pass

                                        # 优先尝试容器名称和内部端口的组合
                                        success = False
                                        for host in hosts_to_try:
                                            for port_to_check in ports_to_try:
                                                try:
                                                    logger.info(f"尝试健康检查URL: http://{host}:{port_to_check}/health")
                                                    response = requests.get(f"http://{host}:{port_to_check}/health", timeout=2)
                                                    if response.status_code == 200:
                                                        # 健康检查成功
                                                        logger.info(f"健康检查成功: {response.json()}")
                                                        return {
                                                            "status": "running",
                                                            "message": f"服务器正在运行(Docker容器): {server_id}",
                                                            "started_at": self.running_servers[server_id].get("started_at"),
                                                            "use_system_ai": server_config.get("use_system_ai", False),
                                                            "deployment_mode": server_config.get("deployment_mode", "docker"),
                                                            "container_status": container_status,
                                                            "health": response.json()
                                                        }
                                                except Exception as e:
                                                    logger.warning(f"健康检查失败: {str(e)}")
                                                    continue
                                    except Exception as health_error:
                                        logger.warning(f"健康检查失败: {str(health_error)}")

                                    # 即使健康检查失败，但容器在运行，也返回运行状态
                                    logger.info(f"容器正在运行，但健康检查失败，仍然返回运行状态")
                                    return {
                                        "status": "running",
                                        "message": f"服务器正在运行(Docker容器): {server_id}",
                                        "started_at": self.running_servers[server_id].get("started_at"),
                                        "use_system_ai": server_config.get("use_system_ai", False),
                                        "deployment_mode": server_config.get("deployment_mode", "docker"),
                                        "container_status": container_status
                                    }
                        else:
                            logger.warning("Docker命令不可用，无法检查容器状态")
                    except Exception as e:
                        logger.warning(f"检查Docker容器状态失败: {str(e)}")
            elif process_type == "local":
                # 本地部署模式，检查进程是否仍在运行
                process_id = process_info.get("process_id")
                if process_id and self._check_process_exists(process_id):
                    # 进程仍在运行，保持状态不变
                    return {
                        "status": "running",
                        "message": f"服务器正在运行(本地进程): {server_id}",
                        "started_at": self.running_servers[server_id].get("started_at"),
                        "use_system_ai": server_config.get("use_system_ai", False),
                        "deployment_mode": server_config.get("deployment_mode", "local")
                    }

        # 如果服务器不在运行列表中或进程不存在，尝试检查实际状态
        # 尝试通过HTTP请求检查服务器状态
        if server_id not in self.running_servers:
            try:
                # 尝试多种可能的主机名和端口
                hosts_to_try = ["localhost", "127.0.0.1", "0.0.0.0", "**********"]

                # 获取进程信息中的端口
                process_info = self.processes.get(server_id, {})
                port = process_info.get("port")
                if port:
                    ports_to_try = [port]
                else:
                    # 获取所有Docker容器的端口映射
                    try:
                        docker_ps_cmd = ["docker", "ps", "--format", "{{.Ports}}"]
                        docker_ps_process = subprocess.run(
                            docker_ps_cmd,
                            capture_output=True,
                            text=True
                        )
                        if docker_ps_process.returncode == 0:
                            # 解析端口映射
                            ports = []
                            for line in docker_ps_process.stdout.strip().split("\n"):
                                if line:
                                    # 格式类似于 0.0.0.0:53691->53691/tcp
                                    for port_mapping in line.split(","):
                                        if "->" in port_mapping:
                                            host_port = port_mapping.split("->")[0].split(":")[-1]
                                            try:
                                                ports.append(int(host_port))
                                            except ValueError:
                                                pass
                            if ports:
                                ports_to_try = ports
                                logger.info(f"从Docker容器中获取到的端口: {ports}")
                            else:
                                ports_to_try = [3000, 3001, 8000, 8080]
                        else:
                            ports_to_try = [3000, 3001, 8000, 8080]
                    except Exception as e:
                        logger.warning(f"获取Docker容器端口映射失败: {str(e)}")
                        ports_to_try = [3000, 3001, 8000, 8080]

                logger.info(f"尝试端口: {ports_to_try}")

                for host in hosts_to_try:
                    for port in ports_to_try:
                        try:
                            response = requests.get(f"http://{host}:{port}/health", timeout=1)
                            if response.status_code == 200:
                                # 服务器实际上在运行，更新内存中的状态
                                self.running_servers[server_id] = {
                                    "status": "running",
                                    "started_at": datetime.now().isoformat(),
                                    "use_system_ai": server_config.get("use_system_ai", False),
                                    "deployment_mode": server_config.get("deployment_mode", "local"),
                                    "port": port
                                }

                                # 更新数据库中的进程记录
                                try:
                                    from .models import MCPServerProcess
                                    from db.database import SessionLocal

                                    session = SessionLocal()
                                    try:
                                        # 创建新的进程记录
                                        process = MCPServerProcess(
                                            server_id=server_id,
                                            status="running",
                                            started_at=datetime.now(),
                                            process_id=0,  # 未知PID
                                            logs=f"端口: {port}"
                                        )
                                        session.add(process)
                                        session.commit()
                                    finally:
                                        session.close()
                                except Exception as e:
                                    logger.warning(f"更新进程记录失败: {str(e)}")

                                return {
                                    "status": "running",
                                    "message": f"服务器正在运行: {server_id}",
                                    "started_at": datetime.now().isoformat(),
                                    "use_system_ai": server_config.get("use_system_ai", False),
                                    "deployment_mode": server_config.get("deployment_mode", "local"),
                                    "health": response.json()
                                }
                        except:
                            continue

                # 如果无法确认服务器在运行，返回停止状态
                return {"status": "stopped", "message": f"服务器未运行: {server_id}"}
            except Exception as e:
                logger.warning(f"检查服务器实际状态失败: {str(e)}")
                return {"status": "error", "message": f"检查服务器状态失败: {str(e)}"}

        # 服务器在运行列表中，获取配置和运行信息
        server_config = self.servers[server_id]
        running_info = self.running_servers[server_id]
        deployment_mode = server_config.get("deployment_mode", "local")

        # 检查是否是模拟安装
        if server_config.get("simulation", False) and server_config.get("use_system_ai", False):
            # 使用系统AI模块
            return {
                "status": "running",
                "message": f"服务器正在运行(使用系统AI模块): {server_id}",
                "started_at": running_info.get("started_at"),
                "use_system_ai": True,
                "deployment_mode": deployment_mode
            }

        # 如果不是模拟安装或不使用系统AI模块，尝试检查服务器状态
        try:
            # 检查进程信息
            process_info = self.processes.get(server_id, {})
            process_type = process_info.get("type", "local")

            # 如果是 Docker 部署模式，检查容器状态
            if process_type == "docker":
                container_name = process_info.get("container_name")
                if container_name:
                    try:
                        # 检查是否在 Docker 容器内运行
                        in_docker = os.path.exists('/.dockerenv')
                        docker_socket_exists = os.path.exists('/var/run/docker.sock')

                        # 检查 Docker 客户端是否可用
                        docker_check = subprocess.run(
                            ["which", "docker"],
                            capture_output=True,
                            text=True
                        )

                        docker_available = docker_check.returncode == 0

                        # 如果 Docker 客户端不可用，尝试安装
                        if not docker_available and (not in_docker or docker_socket_exists):
                            logger.info("Docker 客户端不可用，尝试安装...")
                            try:
                                # 尝试安装 Docker 客户端
                                # 首先尝试使用 apt-get
                                install_docker_cmd = [
                                    "apt-get", "update", "&&",
                                    "apt-get", "install", "-y", "docker.io"
                                ]
                                install_process = subprocess.run(
                                    " ".join(install_docker_cmd),
                                    shell=True,
                                    capture_output=True,
                                    text=True
                                )

                                if install_process.returncode == 0:
                                    logger.info("Docker 客户端安装成功")
                                    docker_available = True
                                else:
                                    logger.warning(f"使用 apt-get 安装 Docker 客户端失败: {install_process.stderr}")

                                    # 尝试使用 Python 的 urllib 库下载 Docker 客户端
                                    logger.info("尝试使用 Python 的 urllib 库下载 Docker 客户端...")
                                    try:
                                        import urllib.request

                                        # 下载 Docker 客户端
                                        docker_client_url = "https://download.docker.com/linux/static/stable/x86_64/docker-20.10.9.tgz"
                                        docker_client_file = "/tmp/docker.tgz"

                                        # 下载 Docker 客户端
                                        urllib.request.urlretrieve(docker_client_url, docker_client_file)

                                        # 解压 Docker 客户端
                                        extract_cmd = f"tar -xzf {docker_client_file} -C /tmp"
                                        extract_process = subprocess.run(
                                            extract_cmd,
                                            shell=True,
                                            capture_output=True,
                                            text=True
                                        )

                                        if extract_process.returncode == 0:
                                            # 移动 Docker 客户端到 /usr/local/bin
                                            move_cmd = "cp /tmp/docker/docker /usr/local/bin/"
                                            move_process = subprocess.run(
                                                move_cmd,
                                                shell=True,
                                                capture_output=True,
                                                text=True
                                            )

                                            if move_process.returncode == 0:
                                                # 设置 Docker 客户端权限
                                                chmod_cmd = "chmod +x /usr/local/bin/docker"
                                                chmod_process = subprocess.run(
                                                    chmod_cmd,
                                                    shell=True,
                                                    capture_output=True,
                                                    text=True
                                                )

                                                if chmod_process.returncode == 0:
                                                    logger.info("Docker 客户端安装成功")
                                                    docker_available = True
                                                else:
                                                    logger.warning(f"设置 Docker 客户端权限失败: {chmod_process.stderr}")
                                            else:
                                                logger.warning(f"移动 Docker 客户端失败: {move_process.stderr}")
                                        else:
                                            logger.warning(f"解压 Docker 客户端失败: {extract_process.stderr}")
                                    except Exception as e:
                                        logger.warning(f"使用 Python 的 urllib 库下载 Docker 客户端失败: {str(e)}")
                            except Exception as e:
                                logger.warning(f"Docker 客户端安装失败: {str(e)}")

                        if not docker_available:
                            logger.warning("Docker 命令不可用，无法检查容器状态")
                        elif in_docker and not docker_socket_exists:
                            logger.warning("在 Docker 容器内运行，但没有挂载 Docker socket，无法检查容器状态")
                        else:
                            # 基本 Docker 命令
                            docker_base_cmd = ["docker"]

                            # 如果在 Docker 容器内运行，需要特殊处理
                            if in_docker:
                                # 检查是否有 DOCKER_HOST 环境变量
                                docker_host = os.environ.get('DOCKER_HOST', '')
                                if docker_host:
                                    docker_base_cmd.extend(["--host", docker_host])

                            # 检查容器状态 - 使用 docker ps 命令，更可靠
                            ps_cmd = docker_base_cmd + ["ps", "-a", "--filter", f"name={container_name}", "--format", "{{.Status}}"]
                            logger.info(f"执行命令: {' '.join(ps_cmd)}")
                            inspect_process = subprocess.run(
                                ps_cmd,
                                capture_output=True,
                                text=True
                            )
                            logger.info(f"容器状态检查结果: {inspect_process.stdout}, 错误: {inspect_process.stderr}")

                            if inspect_process.returncode == 0:
                                container_status = inspect_process.stdout.strip()
                                # docker ps 的状态输出通常以 "Up" 开头表示容器正在运行
                                if container_status and "Up" in container_status:
                                    # 容器正在运行，尝试获取健康状态
                                    port = process_info.get("port", 3000)

                                    # 尝试多种可能的主机名
                                    hosts_to_try = ["localhost", "127.0.0.1", "0.0.0.0", "**********", container_name]

                                    for host in hosts_to_try:
                                        try:
                                            response = requests.get(f"http://{host}:{port}/health", timeout=1)
                                            if response.status_code == 200:
                                                return {
                                                    "status": "running",
                                                    "message": f"服务器正在运行(Docker容器): {server_id}",
                                                    "started_at": running_info.get("started_at"),
                                                    "use_system_ai": False,
                                                    "deployment_mode": deployment_mode,
                                                    "container_status": container_status,
                                                    "health": response.json()
                                                }
                                        except:
                                            continue

                                    # 如果无法获取健康状态，但容器正在运行
                                    return {
                                        "status": "running",
                                        "message": f"服务器正在运行(Docker容器): {server_id}",
                                        "started_at": running_info.get("started_at"),
                                        "use_system_ai": False,
                                        "deployment_mode": deployment_mode,
                                        "container_status": container_status
                                    }
                                else:
                                    # 容器不在运行状态
                                    return {
                                        "status": "error",
                                        "message": f"Docker容器状态异常: {container_status}",
                                        "started_at": running_info.get("started_at"),
                                        "use_system_ai": False,
                                        "deployment_mode": deployment_mode,
                                        "container_status": container_status
                                    }
                            else:
                                # 无法获取容器状态
                                logger.warning(f"无法获取Docker容器状态: {inspect_process.stderr}")
                    except Exception as e:
                        logger.error(f"检查Docker容器状态失败: {str(e)}")

            # 如果不是 Docker 部署模式或无法获取容器状态，尝试通过 HTTP 请求检查服务器状态
            install_dir = server_config.get("install_dir")
            if not install_dir or not os.path.exists(install_dir):
                return {"status": "error", "message": f"服务器安装目录不存在: {install_dir}"}

            # 尝试通过HTTP请求检查服务器状态
            env_str = server_config.get("env", "{}")
            if env_str is None:
                env_str = "{}"
            try:
                env = json.loads(env_str)
            except:
                logger.warning(f"无法解析环境变量: {env_str}")
                env = {}

            # 获取端口，优先使用进程信息中的端口，如果没有则从环境变量获取，默认为8000
            port = process_info.get("port")
            if not port:
                port = env.get("PORT", 8000)

            # 尝试多种可能的主机名
            hosts_to_try = ["localhost", "127.0.0.1", "0.0.0.0", "**********"]

            for host in hosts_to_try:
                try:
                    response = requests.get(f"http://{host}:{port}/health", timeout=1)
                    if response.status_code == 200:
                        return {
                            "status": "running",
                            "message": f"服务器正在运行: {server_id}",
                            "started_at": running_info.get("started_at"),
                            "use_system_ai": False,
                            "deployment_mode": deployment_mode,
                            "health": response.json()
                        }
                except:
                    continue

            # 如果所有连接都失败，但服务器在运行列表中，则返回未知状态
            return {
                "status": "unknown",
                "message": f"服务器状态未知: {server_id}",
                "started_at": running_info.get("started_at"),
                "use_system_ai": False,
                "deployment_mode": deployment_mode
            }
        except Exception as e:
            logger.error(f"获取服务器状态失败: {str(e)}")
            return {"status": "error", "message": f"获取服务器状态失败: {str(e)}"}

    async def process_mcp_request(self, server_id: str, request: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理MCP请求

        Args:
            server_id: 服务器ID
            request: MCP请求

        Returns:
            Dict[str, Any]: MCP响应
        """
        if server_id not in self.servers:
            return {"error": f"服务器不存在: {server_id}", "status": "error"}

        if server_id not in self.running_servers:
            return {"error": f"服务器未运行: {server_id}", "status": "error"}

        server_config = self.servers[server_id]

        # 检查是否使用系统AI模块
        if server_config.get("use_system_ai", False):
            # 检查是否是视觉请求
            is_vision_request = False
            for msg in request.get("messages", []):
                if msg.get("role") == "user" and isinstance(msg.get("content"), list):
                    for item in msg.get("content", []):
                        if item.get("type") == "image_url":
                            is_vision_request = True
                            break

            if is_vision_request:
                # 处理视觉请求
                from .system_ai_adapter import system_ai_adapter
                return await system_ai_adapter.process_mcp_vision_request(request)
            else:
                # 处理普通请求
                from .system_ai_adapter import system_ai_adapter
                return await system_ai_adapter.process_mcp_request(request)

        # 如果不使用系统AI模块，返回错误
        return {
            "error": "服务器不支持直接处理请求，请使用HTTP API",
            "status": "error"
        }

    async def get_server_tools(self, server_id: str) -> Dict[str, Any]:
        """
        获取服务器工具列表

        Args:
            server_id: 服务器ID

        Returns:
            Dict[str, Any]: 工具列表
        """
        # 如果服务器不在内存中，尝试从数据库加载
        if server_id not in self.servers:
            try:
                # 从数据库中获取服务器信息
                from .models import MCPServer
                from db.database import get_db
                from sqlalchemy.future import select

                # 使用异步会话，但在同步上下文中运行
                # 这里我们需要使用一个临时的异步函数来获取会话
                async def get_server():
                    async for session in get_db():
                        try:
                            result = await session.execute(select(MCPServer).filter(MCPServer.id == server_id))
                            server = result.scalars().first()
                            return server
                        except Exception as e:
                            logger.error(f"查询服务器失败: {str(e)}")
                            return None

                # 运行异步函数
                server = asyncio.run(get_server())
                try:
                    # 使用已获取的服务器对象
                    if server:
                        # 注册服务器到适配器
                        server_config = {
                            "id": str(server.id),
                            "name": server.name,
                            "env": json.loads(server.env) if server.env else {},
                            "install_dir": f"mcp_servers/{server_id}",
                            "simulation": server.simulation,
                            "use_system_ai": server.use_system_ai,
                            "deployment_mode": server.deployment_mode if hasattr(server, 'deployment_mode') else "local",
                            "transport_types": server.transport_types if hasattr(server, 'transport_types') else [server.transport_type]
                        }
                        self.register_server(server_config)
                        logger.info(f"从数据库加载服务器: {server_id}")
                    else:
                        return {"success": False, "message": f"服务器不存在: {server_id}", "data": []}
                finally:
                    pass  # 异步会话会自动关闭
            except Exception as e:
                logger.error(f"从数据库加载服务器失败: {str(e)}")
                return {"success": False, "message": f"服务器加载失败: {str(e)}", "data": []}

        server_config = self.servers[server_id]

        # 检查是否是模拟安装
        simulation_mode = server_config.get("simulation", False)

        if simulation_mode:
            # 如果在模拟模式下运行，直接从数据库中获取API端点并转换为工具
            logger.info(f"服务器 {server_id} 在模拟模式下运行，从数据库中获取API端点")

            try:
                # 从数据库中获取API端点和API到MCP工具的映射
                from .models import SAPIEndpoint, APIMCPToolMapping
                from db.database import AsyncSessionLocal
                import asyncio

                # 使用异步会话
                async def get_mappings_and_tools():
                    async with AsyncSessionLocal() as session:
                        # 首先检查是否有API到MCP工具的映射
                        logger.info(f"检查服务器 {server_id} 的API到MCP工具映射")
                        try:
                            # 使用字符串形式的 UUID 进行查询
                            from sqlalchemy.future import select
                            result = await session.execute(
                                select(APIMCPToolMapping).filter(
                                    APIMCPToolMapping.server_id == uuid.UUID(server_id)
                                )
                            )
                            mappings = result.scalars().all()
                            logger.info(f"查询到 {len(mappings)} 个映射")
                        except Exception as e:
                            logger.error(f"查询映射失败: {str(e)}")
                            mappings = []

                        tools = []

                        if mappings:
                            # 如果有映射，使用映射创建工具
                            logger.info(f"找到 {len(mappings)} 个API到MCP工具映射")
                            for mapping in mappings:
                                try:
                                    # 获取端点
                                    result = await session.execute(
                                        select(SAPIEndpoint).filter(SAPIEndpoint.id == mapping.endpoint_id)
                                    )
                                    endpoint = result.scalar_one_or_none()
                                    if not endpoint:
                                        logger.warning(f"找不到映射 {mapping.id} 对应的API端点")
                                        continue
                                except Exception as e:
                                    logger.error(f"获取端点失败: {str(e)}")
                                    continue

                                # 创建参数模式 - 使用最新的MCP协议格式
                                parameters = {
                                    "type": "object",
                                    "properties": {},
                                    "required": []
                                }

                                if endpoint.parameters:
                                    for param_name, param_info in endpoint.parameters.items():
                                        # 使用参数映射中的名称，如果有的话
                                        mcp_param_name = mapping.parameter_mapping.get(param_name, param_name) if mapping.parameter_mapping else param_name

                                        # 处理不同类型的参数
                                        param_type = param_info.get("type", "string")
                                        param_schema = {
                                            "type": param_type,
                                            "description": param_info.get("description", param_name)
                                        }

                                        # 添加额外的类型信息
                                        if param_type == "array":
                                            param_schema["items"] = param_info.get("items", {"type": "string"})
                                        elif param_type == "object":
                                            param_schema["properties"] = param_info.get("properties", {})
                                            param_schema["additionalProperties"] = param_info.get("additionalProperties", True)
                                        elif param_type == "number" or param_type == "integer":
                                            if "minimum" in param_info:
                                                param_schema["minimum"] = param_info["minimum"]
                                            if "maximum" in param_info:
                                                param_schema["maximum"] = param_info["maximum"]
                                        elif param_type == "string":
                                            if "format" in param_info:
                                                param_schema["format"] = param_info["format"]
                                            if "enum" in param_info:
                                                param_schema["enum"] = param_info["enum"]

                                        parameters["properties"][mcp_param_name] = param_schema

                                        # 如果参数是必填的，添加到required列表
                                        if param_info.get("required", False):
                                            parameters["required"].append(mcp_param_name)

                                # 创建工具 - 使用最新的MCP协议格式
                                tool = {
                                    "name": mapping.tool_name,
                                    "description": endpoint.description or endpoint.name,
                                    "parameters": parameters,
                                    "metadata": {
                                        "endpoint": {
                                            "path": endpoint.endpoint_path,
                                            "method": endpoint.method
                                        },
                                        "mapping_id": str(mapping.id)
                                    }
                                }

                                # 添加响应模板，如果有的话
                                if endpoint.response_template:
                                    tool["metadata"]["response_template"] = endpoint.response_template

                                tools.append(tool)
                        else:
                            # 如果没有映射，直接使用API端点创建工具
                            logger.info(f"没有找到API到MCP工具映射，直接使用API端点创建工具")
                            try:
                                result = await session.execute(
                                    select(SAPIEndpoint).filter(SAPIEndpoint.enabled == True)
                                )
                                endpoints = result.scalars().all()
                                logger.info(f"查询到 {len(endpoints)} 个API端点")
                            except Exception as e:
                                logger.error(f"查询API端点失败: {str(e)}")
                                endpoints = []

                            for endpoint in endpoints:
                                try:
                                    # 提取端点路径中的最后一部分作为工具名称
                                    path_parts = endpoint.endpoint_path.strip('/').split('/')
                                    # 如果路径以/api/开头，则去掉api部分
                                    if path_parts and path_parts[0] == 'api':
                                        path_parts = path_parts[1:]
                                    tool_name = path_parts[-1] if path_parts else endpoint.name.lower().replace(' ', '_')
                                except Exception as e:
                                    logger.error(f"处理API端点失败: {str(e)}")
                                    continue

                                # 创建参数模式 - 使用最新的MCP协议格式
                                parameters = {
                                    "type": "object",
                                    "properties": {},
                                    "required": []
                                }

                                if endpoint.parameters:
                                    for param_name, param_info in endpoint.parameters.items():
                                        # 处理不同类型的参数
                                        param_type = param_info.get("type", "string")
                                        param_schema = {
                                            "type": param_type,
                                            "description": param_info.get("description", param_name)
                                        }

                                        # 添加额外的类型信息
                                        if param_type == "array":
                                            param_schema["items"] = param_info.get("items", {"type": "string"})
                                        elif param_type == "object":
                                            param_schema["properties"] = param_info.get("properties", {})
                                            param_schema["additionalProperties"] = param_info.get("additionalProperties", True)
                                        elif param_type == "number" or param_type == "integer":
                                            if "minimum" in param_info:
                                                param_schema["minimum"] = param_info["minimum"]
                                            if "maximum" in param_info:
                                                param_schema["maximum"] = param_info["maximum"]
                                        elif param_type == "string":
                                            if "format" in param_info:
                                                param_schema["format"] = param_info["format"]
                                            if "enum" in param_info:
                                                param_schema["enum"] = param_info["enum"]

                                        parameters["properties"][param_name] = param_schema

                                        # 如果参数是必填的，添加到required列表
                                        if param_info.get("required", False):
                                            parameters["required"].append(param_name)

                                # 创建工具 - 使用最新的MCP协议格式
                                tool = {
                                    "name": tool_name,
                                    "description": endpoint.description or endpoint.name,
                                    "parameters": parameters,
                                    "metadata": {
                                        "endpoint": {
                                            "path": endpoint.endpoint_path,
                                            "method": endpoint.method
                                        }
                                    }
                                }

                                # 添加响应模板，如果有的话
                                if endpoint.response_template:
                                    tool["metadata"]["response_template"] = endpoint.response_template

                                tools.append(tool)

                        return tools

                # 执行异步函数
                try:
                    tools = asyncio.run(get_mappings_and_tools())
                except Exception as e:
                    logger.error(f"获取映射和工具失败: {str(e)}")
                    tools = []

                # 如果没有找到任何工具，返回空数组
                if not tools:
                    logger.warning(f"没有找到任何API端点或映射，返回空工具列表")
                    return {"success": True, "data": []}

                return {"success": True, "data": tools}
            except Exception as e:
                logger.error(f"从数据库中获取API端点或映射失败: {str(e)}")
                # 如果出错，返回空数组
                return {"success": False, "message": f"获取工具列表失败: {str(e)}", "data": []}

        # 如果不是模拟安装，尝试从服务器获取工具列表
        try:
            # 检查进程信息
            process_info = self.processes.get(server_id, {})
            process_type = process_info.get("type", "local")

            # 获取端口，优先使用进程信息中的端口
            port = process_info.get("port", 3000)

            # 如果进程信息中没有端口，从环境变量中获取
            if not port:
                env_str = server_config.get("env", "{}")
                if env_str is None:
                    env_str = "{}"
                try:
                    env = json.loads(env_str)
                except:
                    logger.warning(f"无法解析环境变量: {env_str}")
                    env = {}
                port = env.get("PORT", 3000)

            # 获取通信方式
            transport_types = server_config.get("transport_types", ["streamhttp"])
            if not isinstance(transport_types, list):
                transport_types = [transport_types]
            primary_transport_type = transport_types[0] if transport_types else "streamhttp"

            # 如果是 Docker 部署模式，检查容器状态
            if process_type == "docker":
                container_name = process_info.get("container_name")
                if container_name:
                    try:
                        # 检查是否在 Docker 容器内运行
                        in_docker = os.path.exists('/.dockerenv')
                        docker_socket_exists = os.path.exists('/var/run/docker.sock')

                        # 检查 Docker 客户端是否可用
                        docker_check = subprocess.run(
                            ["which", "docker"],
                            capture_output=True,
                            text=True
                        )

                        docker_available = docker_check.returncode == 0

                        # 如果 Docker 客户端不可用，尝试安装
                        if not docker_available and (not in_docker or docker_socket_exists):
                            logger.info("Docker 客户端不可用，尝试安装...")
                            try:
                                # 尝试安装 Docker 客户端
                                # 首先尝试使用 apt-get
                                install_docker_cmd = [
                                    "apt-get", "update", "&&",
                                    "apt-get", "install", "-y", "docker.io"
                                ]
                                install_process = subprocess.run(
                                    " ".join(install_docker_cmd),
                                    shell=True,
                                    capture_output=True,
                                    text=True
                                )

                                if install_process.returncode == 0:
                                    logger.info("Docker 客户端安装成功")
                                    docker_available = True
                                else:
                                    logger.warning(f"使用 apt-get 安装 Docker 客户端失败: {install_process.stderr}")

                                    # 尝试使用 Python 的 urllib 库下载 Docker 客户端
                                    logger.info("尝试使用 Python 的 urllib 库下载 Docker 客户端...")
                                    try:
                                        import urllib.request

                                        # 下载 Docker 客户端
                                        docker_client_url = "https://download.docker.com/linux/static/stable/x86_64/docker-20.10.9.tgz"
                                        docker_client_file = "/tmp/docker.tgz"

                                        # 下载 Docker 客户端
                                        urllib.request.urlretrieve(docker_client_url, docker_client_file)

                                        # 解压 Docker 客户端
                                        extract_cmd = f"tar -xzf {docker_client_file} -C /tmp"
                                        extract_process = subprocess.run(
                                            extract_cmd,
                                            shell=True,
                                            capture_output=True,
                                            text=True
                                        )

                                        if extract_process.returncode == 0:
                                            # 移动 Docker 客户端到 /usr/local/bin
                                            move_cmd = "cp /tmp/docker/docker /usr/local/bin/"
                                            move_process = subprocess.run(
                                                move_cmd,
                                                shell=True,
                                                capture_output=True,
                                                text=True
                                            )

                                            if move_process.returncode == 0:
                                                # 设置 Docker 客户端权限
                                                chmod_cmd = "chmod +x /usr/local/bin/docker"
                                                chmod_process = subprocess.run(
                                                    chmod_cmd,
                                                    shell=True,
                                                    capture_output=True,
                                                    text=True
                                                )

                                                if chmod_process.returncode == 0:
                                                    logger.info("Docker 客户端安装成功")
                                                    docker_available = True
                                                else:
                                                    logger.warning(f"设置 Docker 客户端权限失败: {chmod_process.stderr}")
                                            else:
                                                logger.warning(f"移动 Docker 客户端失败: {move_process.stderr}")
                                        else:
                                            logger.warning(f"解压 Docker 客户端失败: {extract_process.stderr}")
                                    except Exception as e:
                                        logger.warning(f"使用 Python 的 urllib 库下载 Docker 客户端失败: {str(e)}")
                            except Exception as e:
                                logger.warning(f"Docker 客户端安装失败: {str(e)}")

                        if docker_available and (not in_docker or docker_socket_exists):
                            # 基本 Docker 命令
                            docker_base_cmd = ["docker"]

                            # 如果在 Docker 容器内运行，需要特殊处理
                            if in_docker:
                                # 检查是否有 DOCKER_HOST 环境变量
                                docker_host = os.environ.get('DOCKER_HOST', '')
                                if docker_host:
                                    docker_base_cmd.extend(["--host", docker_host])

                            # 检查容器状态
                            inspect_cmd = docker_base_cmd + ["inspect", "--format", "{{.State.Status}}", container_name]
                            inspect_process = subprocess.run(
                                inspect_cmd,
                                capture_output=True,
                                text=True
                            )

                            if inspect_process.returncode == 0:
                                container_status = inspect_process.stdout.strip()
                                if container_status != "running":
                                    logger.warning(f"Docker容器状态异常: {container_status}")
                                    return {"success": False, "message": f"Docker容器状态异常: {container_status}", "data": []}
                    except Exception as e:
                        logger.error(f"检查Docker容器状态失败: {str(e)}")

            # 尝试多种可能的主机名
            hosts_to_try = ["localhost", "127.0.0.1", "0.0.0.0", "**********"]

            # 如果是 Docker 部署模式，尝试使用容器名称
            if process_type == "docker":
                container_name = process_info.get("container_name")
                if container_name:
                    hosts_to_try.insert(0, container_name)

            response = None
            success = False

            for host in hosts_to_try:
                try:
                    logger.info(f"尝试连接到{host}:{port}/mcp")
                    # 从配置中获取API令牌
                    env_str = server_config.get("env", "{}")
                    if env_str is None:
                        env_str = "{}"
                    try:
                        env = json.loads(env_str)
                    except:
                        logger.warning(f"无法解析环境变量: {env_str}")
                        env = {}

                    api_key = env.get("API_KEY", "")
                    headers = {
                        "Content-Type": "application/json"
                    }
                    if api_key:
                        headers["Authorization"] = f"Bearer {api_key}"

                    # 构建JSON-RPC请求
                    jsonrpc_request = {
                        "jsonrpc": "2.0",
                        "id": str(uuid.uuid4()),
                        "method": "tools/list"
                    }

                    response = requests.post(
                        f"http://{host}:{port}/mcp",
                        json=jsonrpc_request,
                        headers=headers,
                        timeout=5  # 缩短单个连接的超时时间，以便快速尝试多个主机
                    )
                    logger.info(f"成功连接到{host}:{port}/mcp")
                    success = True
                    break  # 成功连接后跳出循环
                except Exception as e:
                    logger.warning(f"连接到{host}:{port}/mcp失败: {str(e)}")

            if not success:
                # 所有连接方式都失败
                logger.error("无法连接到MCP服务器，请检查服务器是否正在运行以及网络配置")
                return {"success": False, "message": "无法连接到MCP服务器", "data": []}

            if response.status_code == 200:
                # 成功获取工具列表
                response_data = response.json()

                # 检查响应格式
                if "result" in response_data and "tools" in response_data["result"]:
                    # 标准MCP JSON-RPC格式
                    tools = response_data["result"].get("tools", [])
                elif "tools" in response_data:
                    # 旧版MCP格式
                    tools = response_data.get("tools", [])
                else:
                    # 可能是自定义格式
                    tools = response_data

                logger.info(f"从服务器获取到的工具列表: {tools}")
                return {"success": True, "data": tools}
            else:
                # 返回空数组
                logger.warning(f"服务器返回错误状态码: {response.status_code}")
                return {"success": False, "message": f"服务器返回错误状态码: {response.status_code}", "data": []}
        except Exception as e:
            # 连接失败，返回空数组
            logger.warning(f"连接服务器失败: {str(e)}")
            return {"success": False, "message": f"连接服务器失败: {str(e)}", "data": []}

# 创建全局适配器实例
mcp_server_adapter = MCPServerAdapter()
