import React, { useState, useEffect } from 'react';
import {
  Form, Input, Switch, Button, Space, Divider,
  Tabs, message, Alert
} from 'antd';
import {
  SaveOutlined, ReloadOutlined, WechatOutlined,
  DingdingOutlined
} from '@ant-design/icons';
import { project } from '../../../../services';

// 不再使用 TabPane，改用 items 属性

/**
 * 三方登录配置组件
 * 用于配置企业微信、钉钉、飞书、微信等第三方登录
 */
// 接收form属性，但实际上我们使用内部的表单实例
const ThirdPartyLogin = () => {
  const [activeTab, setActiveTab] = useState('wechat_work');
  const [loading, setLoading] = useState(false);
  const [wechatWorkForm] = Form.useForm();
  const [dingTalkForm] = Form.useForm();
  const [feishuForm] = Form.useForm();
  const [wechatForm] = Form.useForm();

  // 修复hooks调用位置
  const wechatWorkEnabled = Form.useWatch('enabled', wechatWorkForm);
  const dingTalkEnabled = Form.useWatch('enabled', dingTalkForm);
  const feishuEnabled = Form.useWatch('enabled', feishuForm);
  const wechatEnabled = Form.useWatch('enabled', wechatForm);

  // 初始化表单数据
  useEffect(() => {
    fetchSettings();
  }, []);

  // 获取设置数据
  const fetchSettings = async () => {
    try {
      setLoading(true);

      // 从API获取第三方登录设置
      try {
        const settings = await project.settings.getThirdPartyLogin();

        // 确保各个配置对象存在
        if (settings && typeof settings === 'object') {
          const wechatWorkConfig = settings.wechat_work || {};
          const dingtalkConfig = settings.dingtalk || {};
          const feishuConfig = settings.feishu || {};
          const wechatConfig = settings.wechat || {};

          wechatWorkForm.setFieldsValue(wechatWorkConfig);
          dingTalkForm.setFieldsValue(dingtalkConfig);
          feishuForm.setFieldsValue(feishuConfig);
          wechatForm.setFieldsValue(wechatConfig);
        }
      } catch (apiError) {
        console.error('API调用失败，使用默认设置:', apiError);

        // API调用失败时使用默认设置
        const defaultSettings = {
          wechat_work: {
            enabled: false,
            corp_id: '',
            agent_id: '',
            secret: '',
            redirect_uri: ''
          },
          dingtalk: {
            enabled: false,
            app_key: '',
            app_secret: '',
            corp_id: '',
            redirect_uri: ''
          },
          feishu: {
            enabled: false,
            app_id: '',
            app_secret: '',
            redirect_uri: ''
          },
          wechat: {
            enabled: false,
            app_id: '',
            app_secret: '',
            redirect_uri: ''
          }
        };

        wechatWorkForm.setFieldsValue(defaultSettings.wechat_work);
        dingTalkForm.setFieldsValue(defaultSettings.dingtalk);
        feishuForm.setFieldsValue(defaultSettings.feishu);
        wechatForm.setFieldsValue(defaultSettings.wechat);
      }

      setLoading(false);
    } catch (error) {
      console.error('获取设置失败:', error);
      message.error('获取设置失败');
      setLoading(false);
    }
  };

  // 保存企业微信设置
  const saveWechatWorkSettings = async () => {
    try {
      const values = await wechatWorkForm.validateFields();
      setLoading(true);

      // 如果禁用了登录，则清空其他字段的验证
      if (!values.enabled) {
        // 创建一个新的对象，只保留enabled字段
        const disabledValues = { enabled: false };

        // 调用API保存设置
        await project.settings.updateThirdPartyLogin({
          wechat_work: disabledValues
        });
      } else {
        // 调用API保存设置
        await project.settings.updateThirdPartyLogin({
          wechat_work: values
        });
      }

      message.success('企业微信设置保存成功');
      setLoading(false);
    } catch (error) {
      console.error('保存企业微信设置失败:', error);
      message.error('保存企业微信设置失败: ' + (error.response?.data?.message || error.message));
      setLoading(false);
    }
  };

  // 保存钉钉设置
  const saveDingTalkSettings = async () => {
    try {
      const values = await dingTalkForm.validateFields();
      setLoading(true);

      // 如果禁用了登录，则清空其他字段的验证
      if (!values.enabled) {
        // 创建一个新的对象，只保留enabled字段
        const disabledValues = { enabled: false };

        // 调用API保存设置
        await project.settings.updateThirdPartyLogin({
          dingtalk: disabledValues
        });
      } else {
        // 调用API保存设置
        await project.settings.updateThirdPartyLogin({
          dingtalk: values
        });
      }

      message.success('钉钉设置保存成功');
      setLoading(false);
    } catch (error) {
      console.error('保存钉钉设置失败:', error);
      message.error('保存钉钉设置失败: ' + (error.response?.data?.message || error.message));
      setLoading(false);
    }
  };

  // 保存飞书设置
  const saveFeishuSettings = async () => {
    try {
      const values = await feishuForm.validateFields();
      setLoading(true);

      // 如果禁用了登录，则清空其他字段的验证
      if (!values.enabled) {
        // 创建一个新的对象，只保留enabled字段
        const disabledValues = { enabled: false };

        // 调用API保存设置
        await project.settings.updateThirdPartyLogin({
          feishu: disabledValues
        });
      } else {
        // 调用API保存设置
        await project.settings.updateThirdPartyLogin({
          feishu: values
        });
      }

      message.success('飞书设置保存成功');
      setLoading(false);
    } catch (error) {
      console.error('保存飞书设置失败:', error);
      message.error('保存飞书设置失败: ' + (error.response?.data?.message || error.message));
      setLoading(false);
    }
  };

  // 保存微信设置
  const saveWechatSettings = async () => {
    try {
      const values = await wechatForm.validateFields();
      setLoading(true);

      // 如果禁用了登录，则清空其他字段的验证
      if (!values.enabled) {
        // 创建一个新的对象，只保留enabled字段
        const disabledValues = { enabled: false };

        // 调用API保存设置
        await project.settings.updateThirdPartyLogin({
          wechat: disabledValues
        });
      } else {
        // 调用API保存设置
        await project.settings.updateThirdPartyLogin({
          wechat: values
        });
      }

      message.success('微信设置保存成功');
      setLoading(false);
    } catch (error) {
      console.error('保存微信设置失败:', error);
      message.error('保存微信设置失败: ' + (error.response?.data?.message || error.message));
      setLoading(false);
    }
  };

  // 重置设置
  const resetSettings = () => {
    switch (activeTab) {
      case 'wechat_work':
        wechatWorkForm.resetFields();
        message.info('企业微信设置已重置');
        break;
      case 'dingtalk':
        dingTalkForm.resetFields();
        message.info('钉钉设置已重置');
        break;
      case 'feishu':
        feishuForm.resetFields();
        message.info('飞书设置已重置');
        break;
      case 'wechat':
        wechatForm.resetFields();
        message.info('微信设置已重置');
        break;
      default:
        break;
    }
  };

  // 企业微信设置表单
  const renderWechatWorkSettings = (enabled) => {
    return (
      <Form
        form={wechatWorkForm}
        layout="vertical"
        className="settings-form"
        name="wechatWorkForm"
        initialValues={{
          enabled: false,
          corp_id: '',
          agent_id: '',
          secret: '',
          redirect_uri: ''
        }}
      >
        <Alert
          message="企业微信登录配置"
          description="配置企业微信应用信息，启用后用户可以使用企业微信扫码登录系统。"
          type="info"
          showIcon
          style={{ marginBottom: 24 }}
        />

        <Form.Item
          name="enabled"
          label="启用企业微信登录"
          valuePropName="checked"
        >
          <Switch onChange={(checked) => {
            const formFields = ['corp_id', 'agent_id', 'secret', 'redirect_uri'];
            if (!checked) {
              const resetValues = {};
              formFields.forEach(field => {
                resetValues[field] = '';
              });
              wechatWorkForm.setFieldsValue(resetValues);
            }
          }} />
        </Form.Item>

        <Form.Item
          name="corp_id"
          label="企业ID (CorpID)"
          rules={[{ required: true, message: '请输入企业ID' }]}
          tooltip="企业微信后台，我的企业 -> 企业信息 -> 企业ID"
        >
          <Input placeholder="请输入企业ID" disabled={!enabled} />
        </Form.Item>

        <Form.Item
          name="agent_id"
          label="应用ID (AgentID)"
          rules={[{ required: true, message: '请输入应用ID' }]}
          tooltip="企业微信后台，应用管理 -> 自建应用 -> 应用详情页 -> AgentID"
        >
          <Input placeholder="请输入应用ID" disabled={!enabled} />
        </Form.Item>

        <Form.Item
          name="secret"
          label="应用密钥 (Secret)"
          rules={[{ required: true, message: '请输入应用密钥' }]}
          tooltip="企业微信后台，应用管理 -> 自建应用 -> 应用详情页 -> Secret"
        >
          <Input.Password placeholder="请输入应用密钥" disabled={!enabled} />
        </Form.Item>

        <Form.Item
          name="redirect_uri"
          label="授权回调地址"
          rules={[{ required: true, message: '请输入授权回调地址' }]}
          tooltip="企业微信扫码登录后的回调地址，需要与企业微信后台配置的一致"
        >
          <Input placeholder="请输入授权回调地址" disabled={!enabled} />
        </Form.Item>

        <Divider />

        <Form.Item>
          <Space>
            <Button
              type="primary"
              icon={<SaveOutlined />}
              onClick={saveWechatWorkSettings}
              loading={loading}
            >
              保存设置
            </Button>
            <Button
              icon={<ReloadOutlined />}
              onClick={resetSettings}
            >
              重置
            </Button>
          </Space>
        </Form.Item>
      </Form>
    );
  };

  // 钉钉设置表单
  const renderDingTalkSettings = (enabled) => {
    return (
      <Form
        form={dingTalkForm}
        layout="vertical"
        className="settings-form"
        name="dingTalkForm"
        initialValues={{
          enabled: false,
          app_key: '',
          app_secret: '',
          corp_id: '',
          redirect_uri: ''
        }}
      >
        <Alert
          message="钉钉登录配置"
          description="配置钉钉应用信息，启用后用户可以使用钉钉扫码登录系统。"
          type="info"
          showIcon
          style={{ marginBottom: 24 }}
        />
        <Form.Item
          name="enabled"
          label="启用钉钉登录"
          valuePropName="checked"
        >
          <Switch onChange={(checked) => {
            const formFields = ['app_key', 'app_secret', 'corp_id', 'redirect_uri'];
            if (!checked) {
              const resetValues = {};
              formFields.forEach(field => {
                resetValues[field] = '';
              });
              dingTalkForm.setFieldsValue(resetValues);
            }
          }} />
        </Form.Item>
        <Form.Item
          name="app_key"
          label="应用AppKey"
          rules={[{ required: true, message: '请输入应用AppKey' }]}
          tooltip="钉钉开放平台，应用开发 -> 应用详情 -> 应用信息 -> AppKey"
        >
          <Input placeholder="请输入应用AppKey" disabled={!enabled} />
        </Form.Item>
        <Form.Item
          name="app_secret"
          label="应用AppSecret"
          rules={[{ required: true, message: '请输入应用AppSecret' }]}
          tooltip="钉钉开放平台，应用开发 -> 应用详情 -> 应用信息 -> AppSecret"
        >
          <Input.Password placeholder="请输入应用AppSecret" disabled={!enabled} />
        </Form.Item>
        <Form.Item
          name="corp_id"
          label="企业CorpID"
          rules={[{ required: true, message: '请输入企业CorpID' }]}
          tooltip="钉钉管理后台，企业信息 -> 企业ID (CorpID)，用于事件订阅和API调用"
        >
          <Input placeholder="请输入企业CorpID" disabled={!enabled} />
        </Form.Item>
        <Form.Item
          name="redirect_uri"
          label="授权回调地址"
          rules={[{ required: true, message: '请输入授权回调地址' }]}
          tooltip="钉钉扫码登录后的回调地址，需要与钉钉开放平台配置的一致"
        >
          <Input placeholder="请输入授权回调地址" disabled={!enabled} />
        </Form.Item>
        <Divider />
        <Form.Item>
          <Space>
            <Button
              type="primary"
              icon={<SaveOutlined />}
              onClick={saveDingTalkSettings}
              loading={loading}
            >
              保存设置
            </Button>
            <Button
              icon={<ReloadOutlined />}
              onClick={resetSettings}
            >
              重置
            </Button>
          </Space>
        </Form.Item>
      </Form>
    );
  };

  // 飞书设置表单
  const renderFeishuSettings = (enabled) => {
    return (
      <Form
        form={feishuForm}
        layout="vertical"
        className="settings-form"
        name="feishuForm"
        initialValues={{
          enabled: false,
          app_id: '',
          app_secret: '',
          redirect_uri: ''
        }}
      >
        <Alert
          message="飞书登录配置"
          description="配置飞书应用信息，启用后用户可以使用飞书扫码登录系统。"
          type="info"
          showIcon
          style={{ marginBottom: 24 }}
        />
        <Form.Item
          name="enabled"
          label="启用飞书登录"
          valuePropName="checked"
        >
          <Switch onChange={(checked) => {
            const formFields = ['app_id', 'app_secret', 'redirect_uri'];
            if (!checked) {
              // 如果禁用登录，清空其他字段的值
              const resetValues = {};
              formFields.forEach(field => {
                resetValues[field] = '';
              });
              feishuForm.setFieldsValue(resetValues);
            }
          }} />
        </Form.Item>

        <Form.Item
          name="app_id"
          label="应用AppID"
          rules={[{ required: true, message: '请输入应用AppID' }]}
          tooltip="飞书开放平台，开发者后台 -> 应用详情 -> 凭证与基础信息 -> App ID"
        >
          <Input placeholder="请输入应用AppID" disabled={!enabled} />
        </Form.Item>

        <Form.Item
          name="app_secret"
          label="应用AppSecret"
          rules={[{ required: true, message: '请输入应用AppSecret' }]}
          tooltip="飞书开放平台，开发者后台 -> 应用详情 -> 凭证与基础信息 -> App Secret"
        >
          <Input.Password placeholder="请输入应用AppSecret" disabled={!enabled} />
        </Form.Item>

        <Form.Item
          name="redirect_uri"
          label="授权回调地址"
          rules={[{ required: true, message: '请输入授权回调地址' }]}
          tooltip="飞书扫码登录后的回调地址，需要与飞书开放平台配置的一致"
        >
          <Input placeholder="请输入授权回调地址" disabled={!enabled} />
        </Form.Item>

        <Divider />

        <Form.Item>
          <Space>
            <Button
              type="primary"
              icon={<SaveOutlined />}
              onClick={saveFeishuSettings}
              loading={loading}
            >
              保存设置
            </Button>
            <Button
              icon={<ReloadOutlined />}
              onClick={resetSettings}
            >
              重置
            </Button>
          </Space>
        </Form.Item>
      </Form>
    );
  };

  // 微信设置表单
  const renderWechatSettings = (enabled) => {
    return (
      <Form
        form={wechatForm}
        layout="vertical"
        className="settings-form"
        name="wechatForm"
        initialValues={{
          enabled: false,
          app_id: '',
          app_secret: '',
          redirect_uri: ''
        }}
      >
        <Alert
          message="微信登录配置"
          description="配置微信公众平台应用信息，启用后用户可以使用微信扫码登录系统。"
          type="info"
          showIcon
          style={{ marginBottom: 24 }}
        />

        <Form.Item
          name="enabled"
          label="启用微信登录"
          valuePropName="checked"
        >
          <Switch onChange={(checked) => {
            // 当开关状态改变时，更新表单字段的禁用状态
            const formFields = ['app_id', 'app_secret', 'redirect_uri'];
            if (!checked) {
              // 如果禁用登录，清空其他字段的值
              const resetValues = {};
              formFields.forEach(field => {
                resetValues[field] = '';
              });
              wechatForm.setFieldsValue(resetValues);
            }
          }} />
        </Form.Item>

        <Form.Item
          name="app_id"
          label="应用AppID"
          rules={[{ required: true, message: '请输入应用AppID' }]}
          tooltip="微信公众平台，开发 -> 基本配置 -> 开发者ID(AppID)"
        >
          <Input placeholder="请输入应用AppID" disabled={!enabled} />
        </Form.Item>

        <Form.Item
          name="app_secret"
          label="应用AppSecret"
          rules={[{ required: true, message: '请输入应用AppSecret' }]}
          tooltip="微信公众平台，开发 -> 基本配置 -> 开发者密码(AppSecret)"
        >
          <Input.Password placeholder="请输入应用AppSecret" disabled={!enabled} />
        </Form.Item>

        <Form.Item
          name="redirect_uri"
          label="授权回调地址"
          rules={[{ required: true, message: '请输入授权回调地址' }]}
          tooltip="微信扫码登录后的回调地址，需要与微信公众平台配置的一致"
        >
          <Input placeholder="请输入授权回调地址" disabled={!enabled} />
        </Form.Item>

        <Divider />

        <Form.Item>
          <Space>
            <Button
              type="primary"
              icon={<SaveOutlined />}
              onClick={saveWechatSettings}
              loading={loading}
            >
              保存设置
            </Button>
            <Button
              icon={<ReloadOutlined />}
              onClick={resetSettings}
            >
              重置
            </Button>
          </Space>
        </Form.Item>
      </Form>
    );
  };

  // 定义 Tabs 的 items
  const tabItems = [
    {
      key: 'wechat_work',
      label: (
        <span>
          <WechatOutlined style={{ color: '#07C160' }} />
          企业微信
        </span>
      ),
      children: renderWechatWorkSettings(wechatWorkEnabled)
    },
    {
      key: 'dingtalk',
      label: (
        <span>
          <DingdingOutlined style={{ color: '#1890ff' }} />
          钉钉
        </span>
      ),
      children: renderDingTalkSettings(dingTalkEnabled)
    },
    {
      key: 'feishu',
      label: (
        <span>
          <img
            src="/images/feishu-icon.svg"
            alt="飞书"
            style={{ width: 14, height: 14, marginRight: 8 }}
          />
          飞书
        </span>
      ),
      children: renderFeishuSettings(feishuEnabled)
    },
    {
      key: 'wechat',
      label: (
        <span>
          <WechatOutlined style={{ color: '#07C160' }} />
          微信
        </span>
      ),
      children: renderWechatSettings(wechatEnabled)
    }
  ];

  return (
    <div className="third-party-login">
      <Tabs activeKey={activeTab} onChange={setActiveTab} items={tabItems} />
    </div>
  );
};

export default ThirdPartyLogin;
