#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
任务调度服务
用于管理和调度定时任务
"""

import logging
import uuid
import asyncio
from typing import Dict, Any, List, Optional, Union
from datetime import datetime, timedelta

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import and_, or_, desc

from models.routine_task import RoutineTask
from models.notification import Notification
from models.notification_settings import NotificationSettings
from schemas.task import TaskCreate
from .redis_manager import RedisManager
from .notification_service import NotificationService

# 配置日志
logger = logging.getLogger(__name__)

class TaskScheduler:
    """任务调度服务，提供任务的调度和执行功能"""

    _instance = None
    _is_running = False

    @classmethod
    def get_instance(cls):
        """获取TaskScheduler单例实例"""
        if cls._instance is None:
            cls._instance = TaskScheduler()
        return cls._instance

    @classmethod
    async def schedule_task(
        cls,
        db: AsyncSession,
        task: RoutineTask
    ) -> bool:
        """
        调度任务

        Args:
            db: 数据库会话
            task: 任务对象

        Returns:
            操作是否成功
        """
        try:
            # 获取Redis管理器实例
            redis_manager = await RedisManager.get_instance()

            # 如果任务有开始时间，添加到执行队列
            if task.start_date:
                execution_time = int(task.start_date.timestamp())
                await redis_manager.schedule_task(
                    f"tasks:scheduled:{task.project_id}",
                    str(task.id),
                    execution_time
                )

                # 如果任务有截止时间，添加到提醒队列
                if task.due_date:
                    # 提前24小时提醒
                    reminder_time = task.due_date - timedelta(hours=24)
                    if reminder_time > datetime.now():
                        await redis_manager.schedule_task(
                            f"tasks:reminders:{task.project_id}",
                            str(task.id),
                            int(reminder_time.timestamp())
                        )

            # 如果是周期性任务，计算下次执行时间
            if task.is_recurring and task.recurrence_pattern:
                next_execution = cls._calculate_next_execution(
                    task.start_date or datetime.now(),
                    task.recurrence_pattern,
                    task.recurrence_interval or 1
                )

                if next_execution:
                    # 添加到周期性任务队列
                    await redis_manager.schedule_task(
                        f"tasks:recurring:{task.project_id}",
                        str(task.id),
                        int(next_execution.timestamp())
                    )

            return True
        except Exception as e:
            logger.error(f"调度任务失败: {str(e)}")
            return False

    @classmethod
    def _calculate_next_execution(
        cls,
        base_time: datetime,
        pattern: str,
        interval: int = 1
    ) -> Optional[datetime]:
        """
        计算下次执行时间

        Args:
            base_time: 基准时间
            pattern: 重复模式 (daily, weekly, monthly)
            interval: 重复间隔

        Returns:
            下次执行时间
        """
        now = datetime.now()

        if pattern == 'daily':
            next_time = base_time + timedelta(days=interval)
        elif pattern == 'weekly':
            next_time = base_time + timedelta(weeks=interval)
        elif pattern == 'monthly':
            # 简单处理月份，可能需要更复杂的逻辑处理月末等情况
            year = base_time.year + ((base_time.month - 1 + interval) // 12)
            month = ((base_time.month - 1 + interval) % 12) + 1
            day = min(base_time.day, cls._get_days_in_month(year, month))
            next_time = datetime(year, month, day, base_time.hour, base_time.minute, base_time.second)
        else:
            logger.warning(f"未知的重复模式: {pattern}")
            return None

        # 确保下次执行时间在当前时间之后
        if next_time <= now:
            return cls._calculate_next_execution(next_time, pattern, interval)

        return next_time

    @staticmethod
    def _get_days_in_month(year: int, month: int) -> int:
        """获取指定月份的天数"""
        if month == 2:
            if (year % 4 == 0 and year % 100 != 0) or (year % 400 == 0):
                return 29
            return 28
        elif month in [4, 6, 9, 11]:
            return 30
        else:
            return 31

    @classmethod
    async def start_scheduler(cls, db_factory):
        """
        启动任务调度器

        Args:
            db_factory: 数据库会话工厂函数
        """
        if cls._is_running:
            logger.warning("任务调度器已经在运行")
            return

        cls._is_running = True
        logger.info("任务调度器已启动")

        try:
            # 启动任务执行循环
            asyncio.create_task(cls._task_execution_loop(db_factory))

            # 启动任务提醒循环
            asyncio.create_task(cls._task_reminder_loop(db_factory))

            # 启动周期性任务循环
            asyncio.create_task(cls._recurring_task_loop(db_factory))

        except Exception as e:
            cls._is_running = False
            logger.error(f"启动任务调度器失败: {str(e)}")
            raise

    @classmethod
    async def _task_execution_loop(cls, db_factory, interval_seconds: int = 60):
        """
        任务执行循环

        Args:
            db_factory: 数据库会话工厂函数
            interval_seconds: 检查间隔（秒）
        """
        redis_manager = await RedisManager.get_instance()

        while cls._is_running:
            try:
                # 获取数据库会话
                async with db_factory() as db:
                    # 获取所有项目ID
                    from models.project import Project
                    result = await db.execute(select(Project.id))
                    project_ids = [str(project_id) for project_id, in result.all()]

                    # 检查每个项目的到期任务
                    for project_id in project_ids:
                        key = f"tasks:scheduled:{project_id}"
                        due_tasks = await redis_manager.get_due_tasks(key)

                        for task_id in due_tasks:
                            # 执行任务
                            await cls._execute_task(db, uuid.UUID(task_id))

            except Exception as e:
                logger.error(f"任务执行循环出错: {str(e)}")

            # 等待下一次检查
            await asyncio.sleep(interval_seconds)

    @classmethod
    async def _task_reminder_loop(cls, db_factory, interval_seconds: int = 300):
        """
        任务提醒循环

        Args:
            db_factory: 数据库会话工厂函数
            interval_seconds: 检查间隔（秒）
        """
        redis_manager = await RedisManager.get_instance()

        while cls._is_running:
            try:
                # 获取数据库会话
                async with db_factory() as db:
                    # 获取所有项目ID
                    from models.project import Project
                    result = await db.execute(select(Project.id))
                    project_ids = [str(project_id) for project_id, in result.all()]

                    # 检查每个项目的提醒任务
                    for project_id in project_ids:
                        key = f"tasks:reminders:{project_id}"
                        due_reminders = await redis_manager.get_due_tasks(key)

                        for task_id in due_reminders:
                            # 发送任务提醒
                            await cls._send_task_reminder(db, uuid.UUID(task_id))

            except Exception as e:
                logger.error(f"任务提醒循环出错: {str(e)}")

            # 等待下一次检查
            await asyncio.sleep(interval_seconds)

    @classmethod
    async def _recurring_task_loop(cls, db_factory, interval_seconds: int = 300):
        """
        周期性任务循环

        Args:
            db_factory: 数据库会话工厂函数
            interval_seconds: 检查间隔（秒）
        """
        redis_manager = await RedisManager.get_instance()

        while cls._is_running:
            try:
                # 获取数据库会话
                async with db_factory() as db:
                    # 获取所有项目ID
                    from models.project import Project
                    result = await db.execute(select(Project.id))
                    project_ids = [str(project_id) for project_id, in result.all()]

                    # 检查每个项目的周期性任务
                    for project_id in project_ids:
                        key = f"tasks:recurring:{project_id}"
                        due_recurring = await redis_manager.get_due_tasks(key)

                        for task_id in due_recurring:
                            # 处理周期性任务
                            await cls._handle_recurring_task(db, uuid.UUID(task_id))

            except Exception as e:
                logger.error(f"周期性任务循环出错: {str(e)}")

            # 等待下一次检查
            await asyncio.sleep(interval_seconds)

    @classmethod
    async def _execute_task(cls, db: AsyncSession, task_id: uuid.UUID):
        """
        执行任务

        Args:
            db: 数据库会话
            task_id: 任务ID
        """
        try:
            # 查询任务
            result = await db.execute(select(RoutineTask).where(RoutineTask.id == task_id))
            task = result.scalars().first()

            if not task:
                logger.warning(f"任务 {task_id} 不存在")
                return

            # 更新任务状态
            task.status = 'in_progress'
            await db.commit()

            # 这里可以添加实际的任务执行逻辑
            # 例如调用外部API、执行特定操作等

            # 发送通知给任务负责人
            for assignee_id in task.assignee_ids:
                try:
                    # 检查用户通知设置
                    query = select(NotificationSettings).where(
                        and_(
                            NotificationSettings.project_id == task.project_id,
                            NotificationSettings.user_id == assignee_id
                        )
                    )
                    result = await db.execute(query)
                    settings = result.scalar_one_or_none()

                    # 如果用户禁用了任务通知，则跳过
                    if settings and not settings.task_notifications:
                        logger.info(f"用户 {assignee_id} 已禁用任务通知，跳过发送")
                        continue

                    notification_data = NotificationCreate(
                        user_id=assignee_id,
                        title=f"任务已开始: {task.title}",
                        content=f"您负责的任务 '{task.title}' 已开始执行。",
                        type="task",
                        priority=task.priority,
                        target_url=f"/project/tasks/{task.id}"
                    )

                    await NotificationService.create_notification(
                        db=db,
                        user_id=assignee_id,
                        project_id=task.project_id,
                        notification_data=notification_data
                    )
                except Exception as e:
                    logger.error(f"发送任务通知失败: {str(e)}")

            # 更新任务状态为进行中
            # 注意：实际应用中，任务可能需要人工完成，所以不自动标记为已完成
            await db.commit()

        except Exception as e:
            await db.rollback()
            logger.error(f"执行任务 {task_id} 失败: {str(e)}")

    @classmethod
    async def _send_task_reminder(cls, db: AsyncSession, task_id: uuid.UUID):
        """
        发送任务提醒

        Args:
            db: 数据库会话
            task_id: 任务ID
        """
        try:
            # 查询任务
            result = await db.execute(select(RoutineTask).where(RoutineTask.id == task_id))
            task = result.scalars().first()

            if not task:
                logger.warning(f"任务 {task_id} 不存在")
                return

            # 如果任务已完成或已取消，不发送提醒
            if task.status in ['completed', 'cancelled']:
                return

            # 发送提醒通知给任务负责人
            for assignee_id in task.assignee_ids:
                try:
                    # 检查用户通知设置
                    query = select(NotificationSettings).where(
                        and_(
                            NotificationSettings.project_id == task.project_id,
                            NotificationSettings.user_id == assignee_id
                        )
                    )
                    result = await db.execute(query)
                    settings = result.scalar_one_or_none()

                    # 如果用户禁用了任务通知，则跳过
                    if settings and not settings.task_notifications:
                        logger.info(f"用户 {assignee_id} 已禁用任务通知，跳过发送")
                        continue

                    notification_data = NotificationCreate(
                        user_id=assignee_id,
                        title=f"任务即将到期: {task.title}",
                        content=f"您负责的任务 '{task.title}' 将在 {task.due_date.strftime('%Y-%m-%d %H:%M')} 到期，请及时处理。",
                        type="task",
                        priority="high",  # 提醒通知使用高优先级
                        target_url=f"/project/tasks/{task.id}"
                    )

                    await NotificationService.create_notification(
                        db=db,
                        user_id=assignee_id,
                        project_id=task.project_id,
                        notification_data=notification_data
                    )
                except Exception as e:
                    logger.error(f"发送任务提醒通知失败: {str(e)}")

        except Exception as e:
            logger.error(f"发送任务 {task_id} 提醒失败: {str(e)}")

    @classmethod
    async def _handle_recurring_task(cls, db: AsyncSession, task_id: uuid.UUID):
        """
        处理周期性任务

        Args:
            db: 数据库会话
            task_id: 任务ID
        """
        try:
            # 查询任务
            result = await db.execute(select(RoutineTask).where(RoutineTask.id == task_id))
            task = result.scalars().first()

            if not task:
                logger.warning(f"任务 {task_id} 不存在")
                return

            # 如果任务不是周期性任务，直接返回
            if not task.is_recurring:
                return

            # 计算下次执行时间
            next_execution = cls._calculate_next_execution(
                task.start_date or datetime.now(),
                task.recurrence_pattern,
                task.recurrence_interval or 1
            )

            if next_execution:
                # 检查是否超过结束日期
                if task.recurrence_end_date and next_execution > task.recurrence_end_date:
                    logger.info(f"周期性任务 {task_id} 已达到结束日期")
                    return

                # 创建新任务实例
                new_task = RoutineTask(
                    id=uuid.uuid4(),
                    title=task.title,
                    description=task.description,
                    category=task.category,
                    priority=task.priority,
                    status="pending",
                    start_date=next_execution,
                    due_date=task.due_date + (next_execution - task.start_date) if task.due_date else None,
                    project_id=task.project_id,
                    store_id=task.store_id,
                    store_ids=task.store_ids,
                    is_all_stores=task.is_all_stores,
                    assignee_ids=task.assignee_ids,
                    tags=task.tags,
                    attachments=task.attachments,
                    created_by=task.created_by,
                    updated_by=task.created_by,
                    created_at=datetime.now(),
                    updated_at=datetime.now()
                )

                db.add(new_task)
                await db.commit()
                await db.refresh(new_task)

                # 调度新任务
                await cls.schedule_task(db, new_task)

                # 通知任务负责人
                for assignee_id in task.assignee_ids:
                    try:
                        # 检查用户通知设置
                        query = select(NotificationSettings).where(
                            and_(
                                NotificationSettings.project_id == task.project_id,
                                NotificationSettings.user_id == assignee_id
                            )
                        )
                        result = await db.execute(query)
                        settings = result.scalar_one_or_none()

                        # 如果用户禁用了任务通知，则跳过
                        if settings and not settings.task_notifications:
                            logger.info(f"用户 {assignee_id} 已禁用任务通知，跳过发送")
                            continue

                        notification_data = NotificationCreate(
                            user_id=assignee_id,
                            title=f"周期性任务已创建: {task.title}",
                            content=f"系统已为您创建新的周期性任务 '{task.title}'，计划开始时间为 {next_execution.strftime('%Y-%m-%d %H:%M')}。",
                            type="task",
                            priority=task.priority,
                            target_url=f"/project/tasks/{new_task.id}"
                        )

                        await NotificationService.create_notification(
                            db=db,
                            user_id=assignee_id,
                            project_id=task.project_id,
                            notification_data=notification_data
                        )
                    except Exception as e:
                        logger.error(f"发送周期性任务通知失败: {str(e)}")

        except Exception as e:
            await db.rollback()
            logger.error(f"处理周期性任务 {task_id} 失败: {str(e)}")
