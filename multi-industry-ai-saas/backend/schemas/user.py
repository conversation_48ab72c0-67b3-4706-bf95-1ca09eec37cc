from typing import Optional, Dict, Any, List, Union
from pydantic import BaseModel, UUID4, EmailStr
from datetime import datetime
import uuid

# 用户基础模型
class UserBase(BaseModel):
    username: str
    email: EmailStr
    full_name: Optional[str] = None
    phone: Optional[str] = None
    is_active: bool = True

# 创建用户请求模型
class UserCreate(UserBase):
    password: str

# 更新用户请求模型
class UserUpdate(BaseModel):
    username: Optional[str] = None
    email: Optional[EmailStr] = None
    full_name: Optional[str] = None
    phone: Optional[str] = None
    is_active: Optional[bool] = None
    password: Optional[str] = None

# 用户响应模型
class User(UserBase):
    id: UUID4
    is_superuser: bool = False  # 兼容性字段，映射到 is_system_admin
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        model_config = {"from_attributes": True}

# 用户列表响应模型
class UserList(BaseModel):
    items: List[User]
    total: int
    page: int
    size: int
    pages: int

# 项目用户关联基础模型
class ProjectUserBase(BaseModel):
    is_admin: bool = False
    status: str = "active"

# 创建项目用户关联请求模型
class ProjectUserCreate(ProjectUserBase):
    project_id: UUID4
    user_id: UUID4
    role_id: Optional[UUID4] = None
    settings: Optional[Dict[str, Any]] = None

# 更新项目用户关联请求模型
class ProjectUserUpdate(ProjectUserBase):
    role_id: Optional[UUID4] = None
    settings: Optional[Dict[str, Any]] = None

# 项目用户关联响应模型
class ProjectUser(ProjectUserBase):
    id: UUID4
    project_id: UUID4
    user_id: UUID4
    role_id: Optional[UUID4] = None
    settings: Optional[Dict[str, Any]] = None
    created_at: datetime
    updated_at: Optional[datetime] = None
    user: Optional[User] = None

    class Config:
        model_config = {"from_attributes": True}

# 项目用户列表响应模型
class ProjectUserList(BaseModel):
    items: List[ProjectUser]
    total: int
    page: int
    size: int
    pages: int

# 用户活动基础模型
class UserActivityBase(BaseModel):
    """用户活动基础模型"""
    activity_type: str
    description: str
    created_at: datetime

    class Config:
        model_config = {"from_attributes": True}

# 第三方账号基础模型
class ThirdPartyAccountBase(BaseModel):
    """第三方账号基础模型"""
    platform: str
    platform_user_id: str
    platform_username: Optional[str] = None
    avatar_url: Optional[str] = None

    class Config:
        model_config = {"from_attributes": True}

# 创建第三方账号模型
class ThirdPartyAccountCreate(ThirdPartyAccountBase):
    """创建第三方账号模型"""
    user_id: UUID4

# 第三方账号响应模型
class ThirdPartyAccountResponse(ThirdPartyAccountBase):
    """第三方账号响应模型"""
    id: UUID4
    user_id: UUID4
    created_at: datetime
    updated_at: datetime

# 用户个人资料更新模型
class UserProfileUpdate(BaseModel):
    """更新用户个人资料模型"""
    username: Optional[str] = None
    email: Optional[EmailStr] = None
    phone: Optional[str] = None
    department: Optional[str] = None

# 用户个人资料响应模型
class UserProfileResponse(BaseModel):
    """用户个人资料响应模型"""
    success: bool
    data: Dict[str, Any]

# 修改密码模型
class PasswordChange(BaseModel):
    """修改密码模型"""
    currentPassword: str
    newPassword: str
    confirmPassword: str
