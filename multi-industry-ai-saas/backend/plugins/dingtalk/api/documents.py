#!/usr/bin/env python
# -*- coding: utf-8 -*-

from fastapi import APIRouter, Depends, HTTPException, Query, Body
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Dict, Any, Optional
import uuid

from api.deps import get_db
from core.auth import get_current_user
from ..services.document_service import DocumentService
from ..services.dingtalk_api_service import DingTalkAPIService
# DingTalkConfig 已删除，统一使用 DingTalkSettings

router = APIRouter(prefix="/documents", tags=["钉钉文档管理"])

async def get_document_service(
    db: AsyncSession = Depends(get_db),
    current_user = Depends(get_current_user)
) -> DocumentService:
    """获取文档服务实例"""
    project_id = str(current_user.get("project_id"))
    
    # 获取钉钉配置
    from sqlalchemy import select
    # 使用统一设置服务获取配置
    from ..services.unified_settings_service import UnifiedSettingsService
    from models.project import Project
    
    # 获取tenant_id
    project_query = select(Project).where(Project.id == uuid.UUID(project_id))
    result = await db.execute(project_query)
    project = result.scalar_one_or_none()
    
    if not project:
        return None
        
    settings_service = UnifiedSettingsService(db, str(project.tenant_id), project_id)
    config = await settings_service.get_config_for_api()
    
    if not config or not config.get("has_config"):
        raise HTTPException(status_code=400, detail="钉钉配置不存在，请先配置钉钉应用")
    
    dingtalk_service = DingTalkAPIService(
        app_key=config.get("app_key"),
        app_secret=config.get("app_secret")
    )
    
    return DocumentService(db, project_id, dingtalk_service)

@router.get("/workspaces", summary="获取工作空间列表")
async def get_workspaces(
    document_service: DocumentService = Depends(get_document_service)
):
    """
    获取钉钉工作空间列表
    
    工作空间是钉钉知识库的顶级容器，每个企业可以有多个工作空间
    """
    try:
        result = await document_service.get_workspaces()
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取工作空间失败: {str(e)}")

@router.get("/spaces", summary="获取知识库列表")
async def get_spaces(
    workspace_id: Optional[str] = Query(None, description="工作空间ID，不传则获取所有知识库"),
    document_service: DocumentService = Depends(get_document_service)
):
    """
    获取钉钉知识库列表
    
    知识库是工作空间下的文档集合，用于组织和管理相关文档
    """
    try:
        result = await document_service.get_spaces(workspace_id)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取知识库失败: {str(e)}")

@router.get("/documents", summary="获取文档列表")
async def get_documents(
    space_id: str = Query(..., description="知识库ID"),
    workspace_id: Optional[str] = Query(None, description="工作空间ID"),
    document_service: DocumentService = Depends(get_document_service)
):
    """
    获取指定知识库的文档列表
    """
    try:
        result = await document_service.get_documents(space_id, workspace_id)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取文档列表失败: {str(e)}")

@router.get("/documents/{doc_id}/content", summary="获取文档内容")
async def get_document_content(
    doc_id: str,
    space_id: str = Query(..., description="知识库ID"),
    workspace_id: Optional[str] = Query(None, description="工作空间ID"),
    document_service: DocumentService = Depends(get_document_service)
):
    """
    获取指定文档的详细内容
    """
    try:
        result = await document_service.get_document_content(doc_id, space_id, workspace_id)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取文档内容失败: {str(e)}")

@router.post("/documents", summary="创建文档")
async def create_document(
    document_data: Dict[str, Any] = Body(..., description="文档数据"),
    document_service: DocumentService = Depends(get_document_service)
):
    """
    在钉钉知识库中创建新文档
    
    参数说明：
    - space_id: 知识库ID（必填）
    - workspace_id: 工作空间ID（可选）
    - name: 文档标题（必填）
    - content: 文档内容（可选）
    - type: 文档类型，如 'doc'（可选，默认为'doc'）
    - parent_doc_id: 父文档ID（可选，用于创建子文档）
    """
    try:
        # 验证必填字段
        if not document_data.get('space_id'):
            raise HTTPException(status_code=400, detail="knowledge库ID不能为空")
        if not document_data.get('name'):
            raise HTTPException(status_code=400, detail="文档标题不能为空")
        
        result = await document_service.create_document(document_data)
        return result
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建文档失败: {str(e)}")

@router.put("/documents/{doc_id}", summary="更新文档")
async def update_document(
    doc_id: str,
    document_data: Dict[str, Any] = Body(..., description="更新的文档数据"),
    document_service: DocumentService = Depends(get_document_service)
):
    """
    更新钉钉文档内容
    
    参数说明：
    - name: 新的文档标题（可选）
    - content: 新的文档内容（可选）
    """
    try:
        result = await document_service.update_document(doc_id, document_data)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新文档失败: {str(e)}")

@router.post("/knowledge-bases/upload", summary="批量上传知识库到钉钉")
async def upload_knowledge_bases_to_dingtalk(
    upload_data: Dict[str, Any] = Body(..., description="上传配置"),
    document_service: DocumentService = Depends(get_document_service)
):
    """
    批量上传系统知识库到钉钉知识库
    
    参数说明：
    - knowledge_base_ids: 要上传的知识库ID列表（必填）
    - target_space_id: 目标钉钉知识库ID（必填）
    - target_workspace_id: 目标钉钉工作空间ID（可选）
    - upload_mode: 上传模式，可选值：
        * 'full': 完整上传（删除旧内容，上传新内容）
        * 'incremental': 增量上传（只上传新增或修改的内容）
        * 'update_only': 仅更新（只更新已存在的文档）
    - include_metadata: 是否包含元数据（标签、创建时间等）
    - auto_sync: 是否启用自动同步
    """
    try:
        # 验证必填字段
        if not upload_data.get('knowledge_base_ids'):
            raise HTTPException(status_code=400, detail="知识库ID列表不能为空")
        if not upload_data.get('target_space_id'):
            raise HTTPException(status_code=400, detail="目标知识库ID不能为空")
        
        # 设置默认值
        upload_data.setdefault('upload_mode', 'incremental')
        upload_data.setdefault('include_metadata', True)
        upload_data.setdefault('auto_sync', False)
        
        result = await document_service.upload_knowledge_bases_to_dingtalk(upload_data)
        return result
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"批量上传知识库失败: {str(e)}")

@router.get("/groups/{group_id}/files", summary="获取群文件列表")
async def get_group_files(
    group_id: str,
    cursor: int = Query(0, description="分页游标"),
    size: int = Query(20, description="每页大小，最大100"),
    document_service: DocumentService = Depends(get_document_service)
):
    """
    获取钉钉群组文件列表
    
    用于获取群组中共享的文件，与知识库文档是不同的概念
    """
    try:
        if size > 100:
            size = 100
        
        result = await document_service.get_group_files(group_id)
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取群文件失败: {str(e)}")

@router.post("/sync-all", summary="全量同步所有文档")
async def sync_all_documents(
    document_service: DocumentService = Depends(get_document_service)
):
    """
    执行完整的文档同步流程
    
    该接口将：
    1. 获取所有工作空间
    2. 获取每个工作空间下的知识库
    3. 获取每个知识库下的文档
    4. 获取每个文档的详细内容
    5. 同步到本地数据库
    
    注意：此操作可能耗时较长，建议在后台执行
    """
    try:
        result = await document_service.sync_all_documents()
        return result
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"全量同步文档失败: {str(e)}")

@router.get("/local/documents", summary="获取本地同步的文档列表")
async def get_local_documents(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页大小"),
    space_id: Optional[str] = Query(None, description="筛选知识库ID"),
    sync_status: Optional[str] = Query(None, description="筛选同步状态"),
    document_service: DocumentService = Depends(get_document_service)
):
    """
    获取本地数据库中的文档记录
    
    可用于查看同步状态、管理本地缓存等
    """
    try:
        from sqlalchemy import select, func
        from ..models.models import DingTalkDocument
        
        # 构建查询条件
        stmt = select(DingTalkDocument).where(
            DingTalkDocument.project_id == uuid.UUID(document_service.project_id)
        )
        
        if space_id:
            stmt = stmt.where(DingTalkDocument.dingtalk_space_id == space_id)
        
        if sync_status:
            stmt = stmt.where(DingTalkDocument.sync_status == sync_status)
        
        # 统计总数
        count_stmt = select(func.count()).select_from(stmt.subquery())
        total_result = await document_service.db.execute(count_stmt)
        total = total_result.scalar()
        
        # 分页查询
        stmt = stmt.offset((page - 1) * page_size).limit(page_size)
        stmt = stmt.order_by(DingTalkDocument.updated_at.desc())
        
        result = await document_service.db.execute(stmt)
        documents = result.scalars().all()
        
        # 转换为字典格式
        document_list = []
        for doc in documents:
            document_list.append({
                "id": str(doc.id),
                "dingtalk_doc_id": doc.dingtalk_doc_id,
                "dingtalk_space_id": doc.dingtalk_space_id,
                "dingtalk_workspace_id": doc.dingtalk_workspace_id,
                "title": doc.title,
                "document_type": doc.document_type,
                "sync_direction": doc.sync_direction,
                "sync_status": doc.sync_status,
                "last_sync_at": doc.last_sync_at.isoformat() if doc.last_sync_at else None,
                "created_at": doc.created_at.isoformat(),
                "updated_at": doc.updated_at.isoformat()
            })
        
        return {
            "success": True,
            "data": {
                "documents": document_list,
                "total": total,
                "page": page,
                "page_size": page_size,
                "total_pages": (total + page_size - 1) // page_size
            }
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取本地文档失败: {str(e)}")

@router.delete("/local/documents/{doc_id}", summary="删除本地文档记录")
async def delete_local_document(
    doc_id: str,
    also_delete_from_dingtalk: bool = Query(False, description="是否同时从钉钉删除"),
    document_service: DocumentService = Depends(get_document_service)
):
    """
    删除本地文档记录
    
    可选择是否同时从钉钉知识库中删除对应文档
    """
    try:
        from sqlalchemy import select, delete
        from ..models.models import DingTalkDocument
        
        # 查找文档
        stmt = select(DingTalkDocument).where(
            DingTalkDocument.id == uuid.UUID(doc_id),
            DingTalkDocument.project_id == uuid.UUID(document_service.project_id)
        )
        result = await document_service.db.execute(stmt)
        document = result.scalar_one_or_none()
        
        if not document:
            raise HTTPException(status_code=404, detail="文档不存在")
        
        # 如果需要同时从钉钉删除
        if also_delete_from_dingtalk:
            dingtalk_result = await document_service.dingtalk_service.delete_document(
                document.dingtalk_doc_id,
                document.dingtalk_space_id,
                document.dingtalk_workspace_id
            )
            
            if not dingtalk_result.get('success'):
                return {
                    "success": False,
                    "message": f"从钉钉删除文档失败: {dingtalk_result.get('message')}"
                }
        
        # 删除本地记录
        delete_stmt = delete(DingTalkDocument).where(DingTalkDocument.id == uuid.UUID(doc_id))
        await document_service.db.execute(delete_stmt)
        await document_service.db.commit()
        
        return {
            "success": True,
            "message": "文档删除成功"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        await document_service.db.rollback()
        raise HTTPException(status_code=500, detail=f"删除文档失败: {str(e)}") 