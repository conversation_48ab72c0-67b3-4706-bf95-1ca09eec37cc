from typing import Optional
from fastapi import APIRouter, Depends, Query, Path, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession

from db.database import get_db
from api.deps import get_current_user
from models.user import User
from schemas.inventory import InventoryAlertResponse, InventoryAlertSummaryResponse
from services.inventory_alert import InventoryAlertService

router = APIRouter(
    prefix="/inventory/alerts",
    tags=["inventory"],
    responses={404: {"description": "Not found"}},
)


@router.get("")
async def get_inventory_alerts(
    project_id: str = Path(..., description="项目ID"),
    warehouse_id: Optional[str] = Query(None, description="仓库ID"),
    alert_type: Optional[str] = Query(None, description="预警类型: low, high, slow_moving"),
    product_category_id: Optional[str] = Query(None, description="产品类别ID"),
    skip: int = Query(0, description="分页起始位置"),
    limit: int = Query(20, description="每页数量"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """获取库存预警列表"""
    try:
        alerts = await InventoryAlertService.get_inventory_alerts(
            db=db,
            project_id=project_id,
            warehouse_id=warehouse_id,
            alert_type=alert_type,
            product_category_id=product_category_id,
            skip=skip,
            limit=limit
        )
        return alerts
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取库存预警失败: {str(e)}")


@router.get("/summary")
async def get_inventory_alert_summary(
    project_id: str = Path(..., description="项目ID"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """获取库存预警摘要"""
    try:
        summary = await InventoryAlertService.get_inventory_alert_summary(
            db=db,
            project_id=project_id
        )
        return {"success": True, "data": summary}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取库存预警摘要失败: {str(e)}")
