#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
采购分拨单任务执行器
"""

import logging
import uuid
from typing import List, Dict, Any, Optional # Ensure Optional is imported
import json
import asyncio

from sqlalchemy.ext.asyncio import AsyncSession

# Removed problematic import of TaskStatusEnum and TaskResultSchema
# from schemas.task import TaskStatusEnum, TaskResultSchema 
from services.task import TaskService # Corrected import path
from .base_task_executor import BaseTaskExecutor
# from utils.file_utils import get_file_content_as_base64 # Assuming this util is used - Commented out due to ModuleNotFoundError and not directly used in test path

# Newly added and crucial import that was part of the problematic edit
from services.table_processing_service import TableProcessingService, TableProcessingRequest, TableProcessingResult
from models.task import AsyncTask
from schemas.task_execution import TaskExecutionResult

logger = logging.getLogger(__name__)

class PurchaseOrderTaskExecutor(BaseTaskExecutor):
    def __init__(self, task: AsyncTask):
        super().__init__(task)
        self.input_data = self.task.input_data or {}
        self.file_id = uuid.UUID(self.input_data["file_id"])
        self.upload_type = self.input_data["upload_type"]
        self.warehouse_id = self.input_data.get("warehouse_id")
        # ... other relevant parameters from input_data ...

    async def execute(self, db: AsyncSession) -> TaskExecutionResult:
        """
        Executes the AI processing and returns a standardized result.
        """
        try:
            # (rest of the logic from the original execute method)
            project_id = self.project_id
            user_id = self.user_id
            file_id = uuid.UUID(self.input_data["file_id"])
            task_params = self.input_data.get("task_params", {})
            
            logger.info(f"开始执行采购分拨单AI处理，任务参数: {task_params}")
            base64_image_string = ""

            result_data = await self._execute_ai_processing(
                db=db,
                project_id=project_id,
                user_id=user_id,
                file_id=file_id,
                task_params=task_params,
                base64_image_string=base64_image_string
            )
            
            logger.info(f"Task {self.task_id} business logic successful.")
            return TaskExecutionResult(
                success=True,
                data={
                    "message": "AI processing completed.",
                    "result": result_data
                }
            )
        except Exception as e:
            error_message = f"AI处理失败: {str(e)}"
            logger.error(f"Error executing task {self.task_id}: {error_message}", exc_info=True)
            return TaskExecutionResult(
                success=False,
                data={"error": error_message}
            )

    async def _execute_ai_processing(
        self,
        db: AsyncSession,
        project_id: str,
        user_id: str,
        file_id: uuid.UUID,
        task_params: Dict[str, Any],
        base64_image_string: str
    ) -> Dict[str, Any]:
        """
        Orchestrates the two-phase AI processing for purchase/distribution slips.
        """
        logger.info(f"Executing PurchaseOrderTaskExecutor for task {self.task_id}")
        
        # Phase 1: Get Purchase Order Summary
        purchase_summary_items = await self._execute_phase1_summary_extraction(db)

        # Phase 2: Get Distribution Details
        distribution_details = None
        # Phase 2 is relevant for allocation slips and combined upload types
        if self.upload_type in ["allocation_slip", "both"]:
             distribution_details = await self._execute_phase2_distribution_extraction(db, purchase_summary_items)
        
        # Final Step: Convert to final format
        final_result_data = self._convert_ai_result_to_purchase_format(
            purchase_summary_items=purchase_summary_items,
            distribution_details_by_store=distribution_details,
        )

        return final_result_data

    async def _execute_phase1_summary_extraction(self, db: AsyncSession) -> List[Dict[str, Any]]:
        """
        Execute Phase 1: Extract purchase order summary with enhanced error handling
        """
        logger.info("开始AI处理阶段1: 提取采购单汇总信息")
        
        # 增强的AI提示，明确要求提取规格信息
        ai_prompt_phase1 = '''
分析采购分拨单表格，提取商品基本信息。

表格结构分析：
- 第一列包含商品信息，通常包括：商品名称、单位、规格、单价、合计等
- 后续列为各门店分拨数量

请仔细提取每个商品的完整信息，按以下JSON格式返回：

{
  "商品名1": {
    "商品名称": "商品名1",
    "单位": "单位（如公斤、个、盒等）",
    "规格": "规格描述（如重量、尺寸等，如果没有则为空字符串）", 
    "单价": 数字,
    "总数": 数字,
    "总金额": 数字
  },
  "商品名2": {...}
}

重要要求：
1. 仔细识别商品的规格信息，不要遗漏
2. 规格可能在商品名称下方或同一行，请仔细查找
3. 单位通常是公斤、个、盒、袋等
4. 不要将门店名称作为商品属性
5. 总数为该商品在所有门店的合计数量
6. 严格JSON格式，数字类型不加引号
7. 如果某个字段找不到，用合理的默认值（规格用空字符串，数字用0）'''

        # Enhanced retry logic with exponential backoff
        max_retries = 3
        base_delay = 2
        
        for attempt in range(max_retries):
            try:
                logger.info(f"阶段1尝试 {attempt + 1}/{max_retries}")
                
                # Create request with enhanced error handling
                table_request = TableProcessingRequest(
                    file_id=self.file_id,
                    processing_mode="ai_only",
                    vision_temperature=0.1,
                    custom_prompt=ai_prompt_phase1,
                    vision_max_tokens=8192,  # 增加token限制以处理更复杂的表格
                    max_retries=1  # Let our logic handle retries
                )
                
                result_phase1 = await TableProcessingService.process_table(
                    db=db,
                    request=table_request,
                    project_id=self.project_id,
                    tenant_id=self.tenant_id,
                    user_id=self.user_id
                )
                
                if not result_phase1.success or not result_phase1.extracted_data:
                    raise ValueError(f"AI处理阶段1失败: {result_phase1.errors or '未提取到数据'}")

                # Convert AI result format to list format
                extracted_data = result_phase1.extracted_data
                if isinstance(extracted_data, dict):
                    phase1_items = []
                    for product_name, product_data in extracted_data.items():
                        if isinstance(product_data, dict):
                            product_data["商品名称"] = product_data.get("商品名称", product_name)
                            # 确保规格字段存在
                            if "规格" not in product_data:
                                product_data["规格"] = ""
                            phase1_items.append(product_data)
                        else:
                            logger.warning(f"商品数据格式异常: {product_name} -> {product_data}")
                    
                    if not phase1_items:
                        raise ValueError("转换后的商品列表为空")
                    
                    logger.info(f"AI处理阶段1成功，提取到 {len(phase1_items)} 个商品。")
                    return phase1_items
                    
                elif isinstance(extracted_data, list):
                    if not extracted_data:
                        raise ValueError("AI返回了空的商品列表")
                    
                    # 确保每个商品都有规格字段
                    for item in extracted_data:
                        if isinstance(item, dict) and "规格" not in item:
                            item["规格"] = ""
                    
                    logger.info(f"AI处理阶段1成功，提取到 {len(extracted_data)} 个商品。")
                    return extracted_data
                else:
                    raise ValueError(f"AI返回了不支持的数据格式: {type(extracted_data)}")
                    
            except Exception as e:
                error_msg = str(e)
                logger.error(f"阶段1尝试 {attempt + 1} 失败: {error_msg}")
                
                # Check if it's a network/service error that can be retried
                retryable_errors = [
                    "502", "503", "504", "timeout", "connection", 
                    "RequestTimeOut", "Bad Gateway", "Service Unavailable",
                    "Gateway Timeout", "httpx.RequestError", "Request timed out",
                    "状态码: 500", "status_code=500"
                ]
                
                is_retryable = any(err_type in error_msg for err_type in retryable_errors)
                
                if attempt < max_retries - 1 and is_retryable:
                    delay = base_delay * (2 ** attempt)  # Exponential backoff
                    logger.info(f"网络/服务错误，{delay}秒后重试...")
                    await asyncio.sleep(delay)
                    continue
                else:
                    # Final attempt failed or non-retryable error
                    raise ValueError(f"AI处理阶段1在 {max_retries} 次尝试后失败: {error_msg}")
        
        # This should never be reached due to the raise in the except block
        raise ValueError("AI处理阶段1意外终止")

    async def _execute_phase2_distribution_extraction(self, db: AsyncSession, purchase_summary_items: List[Dict[str, Any]]) -> Optional[Dict[str, List[Dict[str, Any]]]]:
        """
        Execute Phase 2: Extract distribution details with intelligent store recognition
        """
        logger.info("开始AI处理阶段2: 提取门店分拨详情")
        
        # 改进的AI提示，强调识别真实门店名称
        ai_prompt_phase2 = '''
分析采购分拨单表格，精确识别所有真实门店名称和分拨信息。

表格结构分析：
- 第1列：商品信息（商品名称、单位、规格、单价等）
- 第2列及之后：真实门店名称作为列标题
- 数据行：每行一个商品，与门店列交叉处是分拨数量

重要：请使用表格中显示的真实门店名称，不要使用"门店A"、"门店B"等通用名称！

真实门店名称示例：万达、南塘等

重要提醒：
- 门店名称通常是2-4个字的简短中文名称（如"万达"、"南塘"）
- 不要把商品名称误认为门店名称（商品名称通常较长，如"东魁杨梅A果"、"布鲁克斯樱桃"）
- 不要把商品属性误认为门店名称（如"单位"、"规格"、"单价"、"合计"）

请仔细观察表格列标题，识别真实的门店名称，提取每个门店的商品分拨数据，直接返回JSON格式：

{
  "万达": [
    {"商品名称": "商品1", "数量": 10, "单价": 5.0, "规格": "规格描述"}
  ],
  "南塘": [
    {"商品名称": "商品2", "数量": 8, "单价": 3.0, "规格": "规格描述"}
  ]
}

关键识别要求：
1. 必须使用表格中显示的真实门店名称，不要编造或简化
2. 门店名称通常是中文，可能包含地名、商场名、区域名等
3. 仔细区分商品属性列（如商品名称、单位、规格、单价、合计）和门店列
4. 只包含数量>0的商品分拨记录
5. 数量为0、空白、"-"或null的不要包含
6. 真实门店名称作为JSON的顶级键
7. 每个商品记录包含：商品名称、数量、单价、规格
8. 直接返回JSON，不要markdown代码块包装
9. 如果某个门店没有任何商品分拨，则不要包含该门店
10. 预期门店数量通常在10-30个之间，请确保全面识别所有真实门店'''

        max_retries = 3
        
        for attempt in range(max_retries):
            try:
                logger.info(f"阶段2尝试 {attempt + 1}/{max_retries}")
                
                table_request = TableProcessingRequest(
                    file_id=self.file_id,
                    processing_mode="ai_only",
                    vision_temperature=0.05,
                    custom_prompt=ai_prompt_phase2,
                    vision_max_tokens=16384
                )
                
                result_phase2 = await TableProcessingService.process_table(
                    db=db,
                    request=table_request,
                    project_id=self.project_id,
                    tenant_id=self.tenant_id,
                    user_id=self.user_id
                )
                
                if not result_phase2.success:
                    raise ValueError(f"AI处理阶段2失败: {result_phase2.errors or '未能提取到分拨数据'}")
                
                # 尝试从多个数据源获取结果
                raw_ai_data = None
                
                # 方法1：从AI原始返回内容中解析门店分拨格式
                if result_phase2.ai_processing_info and "raw_ai_assistant_content" in result_phase2.ai_processing_info:
                    raw_content = result_phase2.ai_processing_info["raw_ai_assistant_content"]
                    logger.info(f"AI原始返回内容长度: {len(raw_content)}")
                    logger.info(f"AI原始返回内容: {raw_content}")
                    
                    # 增强的JSON解析策略，专门处理门店分拨格式
                    import json
                    import re
                    
                    # 首先尝试直接解析整个内容
                    try:
                        raw_ai_data = json.loads(raw_content.strip())
                        logger.info("直接JSON解析成功")
                        
                        # 验证是否为门店分拨格式
                        if isinstance(raw_ai_data, dict):
                            # 检查是否所有值都是列表（门店分拨格式的特征）
                            all_values_are_lists = all(isinstance(v, list) for v in raw_ai_data.values())
                            if all_values_are_lists and raw_ai_data:
                                logger.info("识别为门店分拨格式，直接使用")
                                logger.info(f"门店分拨格式包含门店: {list(raw_ai_data.keys())}")
                                # 直接使用这个格式，不需要进一步处理
                            else:
                                logger.info(f"不是门店分拨格式，继续其他解析方法。数据类型检查: {[(k, type(v)) for k, v in raw_ai_data.items()]}")
                                raw_ai_data = None
                        
                    except json.JSONDecodeError:
                        # 移除可能的markdown代码块标记
                        cleaned_content = raw_content.strip()
                        if cleaned_content.startswith('```json'):
                            cleaned_content = cleaned_content[7:]
                        if cleaned_content.startswith('```'):
                            cleaned_content = cleaned_content[3:]
                        if cleaned_content.endswith('```'):
                            cleaned_content = cleaned_content[:-3]
                        
                        try:
                            raw_ai_data = json.loads(cleaned_content.strip())
                            logger.info("清理markdown后JSON解析成功")
                            
                            # 验证门店分拨格式
                            if isinstance(raw_ai_data, dict):
                                all_values_are_lists = all(isinstance(v, list) for v in raw_ai_data.values())
                                if all_values_are_lists and raw_ai_data:
                                    logger.info("识别为门店分拨格式")
                                else:
                                    raw_ai_data = None
                                    
                        except json.JSONDecodeError:
                            # 寻找第一个完整的JSON对象
                            json_start = cleaned_content.find('{')
                            if json_start != -1:
                                # 使用括号匹配找到完整JSON
                                bracket_count = 0
                                json_end = json_start
                                
                                for i, char in enumerate(cleaned_content[json_start:], json_start):
                                    if char == '{':
                                        bracket_count += 1
                                    elif char == '}':
                                        bracket_count -= 1
                                        if bracket_count == 0:
                                            json_end = i + 1
                                            break
                                
                                json_str = cleaned_content[json_start:json_end]
                                try:
                                    raw_ai_data = json.loads(json_str)
                                    logger.info("括号匹配JSON解析成功")
                                    
                                    # 验证门店分拨格式
                                    if isinstance(raw_ai_data, dict):
                                        all_values_are_lists = all(isinstance(v, list) for v in raw_ai_data.values())
                                        if all_values_are_lists and raw_ai_data:
                                            logger.info("识别为门店分拨格式")
                                        else:
                                            raw_ai_data = None
                                            
                                except json.JSONDecodeError as e:
                                    logger.warning(f"JSON解析失败: {e}")
                
                # 方法2：如果原始解析失败，尝试从extracted_data中恢复门店分拨格式
                if not raw_ai_data and result_phase2.extracted_data:
                    logger.info("尝试从extracted_data中恢复门店分拨格式")
                    extracted = result_phase2.extracted_data
                    
                    # 如果extracted_data是列表格式，尝试重构为门店分拨格式
                    if isinstance(extracted, list) and extracted:
                        # 检查第一行是否包含门店列
                        first_row = extracted[0]
                        if isinstance(first_row, dict):
                            # 查找门店列（值为字符串形式的JSON列表）
                            store_columns = {}
                            for key, value in first_row.items():
                                if isinstance(value, str) and value.startswith('[') and value.endswith(']'):
                                    try:
                                        # 尝试解析字符串形式的JSON列表
                                        parsed_list = json.loads(value)
                                        if isinstance(parsed_list, list):
                                            store_columns[key] = parsed_list
                                    except json.JSONDecodeError:
                                        continue
                            
                            if store_columns:
                                logger.info(f"从extracted_data中恢复了门店分拨格式: {list(store_columns.keys())}")
                                raw_ai_data = store_columns
                                # 验证恢复的门店分拨格式
                                all_values_are_lists = all(isinstance(v, list) for v in store_columns.values())
                                if all_values_are_lists:
                                    logger.info("恢复的数据确认为门店分拨格式")
                                else:
                                    logger.warning("恢复的数据格式验证失败")
                    
                    # 如果还是没有，使用原始extracted_data
                    if not raw_ai_data:
                        raw_ai_data = extracted
                        logger.info("使用extracted_data作为备选")
                
                if not raw_ai_data:
                    raise ValueError("无法获取有效的AI返回数据")
                
                # 验证和转换数据格式
                distribution_details = {}
                
                if isinstance(raw_ai_data, dict):
                    # 首先检查是否是标准的门店分拨格式（所有值都是列表）
                    all_values_are_lists = all(isinstance(v, list) for v in raw_ai_data.values())
                    
                    if all_values_are_lists:
                        logger.info("检测到标准门店分拨格式，直接处理")
                        # 这是标准的门店分拨格式，直接处理
                        for store_name, store_data in raw_ai_data.items():
                            valid_items = []
                            for item in store_data:
                                if isinstance(item, dict) and item.get("商品名称") and item.get("数量"):
                                    # 确保数量和单价是数字
                                    try:
                                        quantity = float(item["数量"])
                                        price = float(item.get("单价", 0))
                                        if quantity > 0:
                                            valid_items.append({
                                                "商品名称": str(item["商品名称"]),
                                                "数量": quantity,
                                                "单价": price,
                                                "规格": str(item.get("规格", ""))
                                            })
                                    except (ValueError, TypeError):
                                        continue
                            
                            if valid_items:
                                distribution_details[store_name] = valid_items
                                logger.info(f"门店 '{store_name}': {len(valid_items)} 个商品")
                    else:
                        logger.info("不是标准门店分拨格式，尝试其他处理方式")
                        # 不是标准格式，可能是表格行格式，继续原有逻辑
                
                elif isinstance(raw_ai_data, list):
                    # 如果是列表格式，尝试智能转换
                    logger.info("AI返回列表格式，尝试智能转换为门店分拨格式")
                    
                    # 分析列表中的数据结构，寻找门店信息
                    for item in raw_ai_data[:5]:  # 只检查前5个
                        if isinstance(item, dict):
                            logger.info(f"列表项示例字段: {list(item.keys())}")
                            break
                    
                    # 智能识别门店列：排除已知的商品属性字段
                    known_product_fields = {
                        "商品名称", "product_name", "name", "商品编码", "product_code", "code", "sku",
                        "单位", "product_unit", "unit", "规格", "product_specification", "specification", "spec",
                        "单价", "unit_price", "price", "总数", "quantity", "总金额", "total_amount", "amount",
                        "合计", "小计", "总计", "备注", "notes", "remark", "描述", "description"
                    }
                    
                    # 从数据中智能识别门店列
                    potential_stores = set()
                    for item in raw_ai_data:
                        if isinstance(item, dict):
                            for key, value in item.items():
                                # 如果键名不是已知的商品属性，且值>0，可能是门店
                                if key.lower() not in known_product_fields and key not in known_product_fields:
                                    if value and str(value).strip() not in ["", "-", "0", "null", "None"]:
                                        try:
                                            if float(value) > 0:
                                                potential_stores.add(key)
                                        except (ValueError, TypeError):
                                            pass
                    
                    logger.info(f"智能识别到潜在门店: {list(potential_stores)}")
                    
                    # 根据识别的门店构建分拨数据
                    for store_name in potential_stores:
                        store_items = []
                        for item in raw_ai_data:
                            if isinstance(item, dict) and item.get("商品名称"):
                                quantity = item.get(store_name, 0)
                                try:
                                    quantity_num = float(quantity)
                                    if quantity_num > 0:
                                        store_items.append({
                                            "商品名称": str(item["商品名称"]),
                                            "数量": quantity_num,
                                            "单价": float(item.get("单价", 0)),
                                            "规格": str(item.get("规格", ""))
                                        })
                                except (ValueError, TypeError):
                                    continue
                        
                        if store_items:
                            distribution_details[store_name] = store_items
                            logger.info(f"智能为门店 '{store_name}' 构建了 {len(store_items)} 个商品")
                
                if not distribution_details:
                    raise ValueError("无法从AI返回数据中提取有效的门店分拨信息")
                
                # 智能验证门店数量是否合理
                store_count = len(distribution_details)
                if store_count < 3:
                    logger.warning(f"识别到的门店数量过少({store_count}个)，可能存在识别问题")
                    if attempt < max_retries - 1:
                        raise ValueError(f"门店识别数量不足，仅识别到{store_count}个门店")
                
                logger.info(f"AI处理阶段2成功，智能提取到 {len(distribution_details)} 个门店的分拨数据")
                logger.info(f"门店列表: {list(distribution_details.keys())}")
                return distribution_details
                
            except Exception as e:
                error_msg = str(e)
                logger.error(f"阶段2尝试 {attempt + 1} 失败: {error_msg}")
                
                if attempt < max_retries - 1:
                    delay = 2 * (attempt + 1)  # 递增延迟
                    logger.info(f"重试前等待{delay}秒...")
                    await asyncio.sleep(delay)
                    continue
                else:
                    # 最终失败，提供简化的默认数据
                    logger.warning("阶段2所有尝试失败，生成默认分拨数据")
                    default_distribution = {}
                    if purchase_summary_items:
                        # 从商品汇总创建单个默认门店
                        default_items = []
                        for item in purchase_summary_items[:10]:  # 限制数量
                            if item.get("商品名称"):
                                default_items.append({
                                    "商品名称": str(item["商品名称"]),
                                    "数量": float(item.get("总数", 1)),
                                    "单价": float(item.get("单价", 0)),
                                    "规格": str(item.get("规格", ""))
                                })
                        
                        if default_items:
                            default_distribution["默认门店"] = default_items
                    
                    return default_distribution if default_distribution else None
    
    def _aggregate_purchase_order_items(self, items: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Aggregates items for a purchase order, summing quantities for identical products
        and filtering out total/sub-total rows.
        """
        logger.info(f"Aggregating {len(items)} items for purchase order view.")
        
        aggregated_items: Dict[str, Dict[str, Any]] = {}
        
        # Keywords to identify and filter out summary rows that should not be treated as items.
        summary_keywords = ['合计', '小计', '总计', '总额', '共计']

        for item in items:
            # AI model might use different keys, handle common variations.
            product_name = item.get("商品名称") or item.get("product_name")

            # 1. Filter out summary/total rows or rows without a name
            if not product_name or any(keyword in product_name for keyword in summary_keywords):
                logger.debug(f"Skipping summary/invalid row: {item}")
                continue

            # 2. Create a unique key for aggregation. Use spec if available.
            specification = item.get("规格") or item.get("product_specification", "")
            item_key = f"{product_name}_{specification}".strip()

            # 3. Aggregate items
            if item_key not in aggregated_items:
                # New item, initialize it.
                aggregated_items[item_key] = item.copy()
                # Standardize some key fields for consistency
                aggregated_items[item_key]["product_name"] = product_name
                aggregated_items[item_key]["product_specification"] = specification
                
                # Ensure numeric fields are correctly typed and initialized.
                quantity = item.get("总数") or item.get("quantity")
                total_amount = item.get("总金额") or item.get("total_amount")
                aggregated_items[item_key]["quantity"] = float(quantity) if quantity is not None else 0
                aggregated_items[item_key]["total_amount"] = float(total_amount) if total_amount is not None else 0
            else:
                # Existing item, sum up the numeric fields.
                quantity = item.get("总数") or item.get("quantity")
                total_amount = item.get("总金额") or item.get("total_amount")

                aggregated_items[item_key]["quantity"] += float(quantity) if quantity is not None else 0
                aggregated_items[item_key]["total_amount"] += float(total_amount) if total_amount is not None else 0

        final_items = list(aggregated_items.values())
        logger.info(f"Aggregation complete. Resulted in {len(final_items)} unique items.")
        return final_items

    def _convert_ai_result_to_purchase_format(
        self,
        purchase_summary_items: List[Dict[str, Any]],
        distribution_details_by_store: Optional[Dict[str, List[Dict[str, Any]]]] = None,
    ) -> Dict[str, Any]:
        logger.info(f"Entering _convert_ai_result_to_purchase_format for task_id: {self.task_id}")
        logger.info(f"Received purchase_summary_items: {len(purchase_summary_items)} items.")
        logger.debug(f"purchase_summary_items (first 2 items or 500 chars): {str(purchase_summary_items[:2])[:500]}")

        # For pure 'purchase_order', we need an aggregated summary view.
        # For 'allocation_slip' and 'both', the summary items are the totals to be allocated, so we don't aggregate them.
        if self.upload_type == "purchase_order":
            logger.info(f"Upload type is '{self.upload_type}', applying aggregation to purchase summary items.")
            processed_items = self._aggregate_purchase_order_items(purchase_summary_items)
        else:
            logger.info(f"Upload type is '{self.upload_type}', using raw purchase summary items without aggregation.")
            processed_items = purchase_summary_items

        if distribution_details_by_store is not None:
            logger.info(f"Received distribution_details_by_store with {len(distribution_details_by_store)} stores. Keys: {list(distribution_details_by_store.keys())}")
            logger.debug(f"distribution_details_by_store content (first 500 chars): {str(distribution_details_by_store)[:500]}")
        else:
            logger.info("Received distribution_details_by_store as None.")

        # 标准化商品数据格式，确保前端能正确识别
        standardized_items = []
        for item in processed_items:
            # 提取规格信息，确保不为空
            specification = item.get("规格") or item.get("product_specification", "")
            if not specification or specification.strip() == "":
                # 如果规格为空，尝试从商品名称中提取
                product_name = item.get("商品名称") or item.get("product_name", "")
                # 这里可以添加更复杂的规格提取逻辑
                specification = ""
            
            standardized_item = {
                # 兼容前后端不同字段名
                "product_name": item.get("商品名称") or item.get("product_name", ""),
                "product_code": item.get("商品编码") or item.get("product_code", ""),
                "product_specification": specification,
                "product_unit": item.get("单位") or item.get("product_unit", ""),
                "quantity": float(item.get("总数") or item.get("quantity", 0)),
                "unit_price": float(item.get("单价") or item.get("unit_price", 0)),
                "total_amount": float(item.get("总金额") or item.get("total_amount", 0)),
                
                # 可选字段
                "supplier_id": item.get("supplier_id"),
                "category_id": item.get("category_id"),
                "brand_id": item.get("brand_id"),
                
                # 保留原始字段以确保兼容性
                "商品名称": item.get("商品名称") or item.get("product_name", ""),
                "规格": specification,
                "单位": item.get("单位") or item.get("product_unit", ""),
                "总数": float(item.get("总数") or item.get("quantity", 0)),
                "单价": float(item.get("单价") or item.get("unit_price", 0)),
                "总金额": float(item.get("总金额") or item.get("total_amount", 0)),
            }
            
            # 计算缺失的总金额
            if not standardized_item["total_amount"] and standardized_item["quantity"] and standardized_item["unit_price"]:
                standardized_item["total_amount"] = standardized_item["quantity"] * standardized_item["unit_price"]
                standardized_item["总金额"] = standardized_item["total_amount"]
            
            standardized_items.append(standardized_item)

        final_result = {
            "upload_type": self.upload_type,
            "warehouse_id": str(self.warehouse_id) if self.warehouse_id else None,
            # 前端兼容：提供多个字段名
            "items": standardized_items,
            "purchase_items": standardized_items,  # 前端期望的字段名
            "purchase_summary": standardized_items,  # 兼容字段
            "distribution_destinations": [], # Will be populated below
            # 统计信息
            "stats": {
                "total_items": len(standardized_items),
                "total_quantity": sum(item["quantity"] for item in standardized_items),
                "total_amount": sum(item["total_amount"] for item in standardized_items),
                "total_stores": len(distribution_details_by_store) if distribution_details_by_store else 0
            }
        }

        # 构建分拨目标 (distribution_destinations)
        distribution_destinations: List[Dict[str, Any]] = []
        if distribution_details_by_store:
            logger.info(f"Processing distribution_details_by_store for distribution_destinations. Store count: {len(distribution_details_by_store)}")
            for store_name, store_items in distribution_details_by_store.items():
                logger.debug(f"Processing store: {store_name}, with {len(store_items)} items.")
                
                current_store_distributions = []
                store_total_allocated_quantity = 0
                store_total_allocated_amount = 0.0

                for dist_item in store_items:
                    # Extract item information from distribution data
                    item_name = dist_item.get("商品名称") or dist_item.get("product_name")
                    item_quantity = dist_item.get("数量") or dist_item.get("quantity", 0)
                    item_unit_price = dist_item.get("单价") or dist_item.get("unit_price", 0)
                    
                    # Skip items with no quantity
                    if not item_quantity or float(item_quantity) <= 0:
                        continue
                    
                    # Find matching item in purchase summary for additional details
                    matched_summary_item = next((
                        psi for psi in standardized_items 
                        if (psi.get("商品名称") or psi.get("product_name")) == item_name
                    ), None)

                    # Use matched item data if available, otherwise use distribution data
                    item_spec = ""
                    item_unit = "公斤"  # Default unit
                    if matched_summary_item:
                        item_spec = matched_summary_item.get("规格") or matched_summary_item.get("product_specification", "")
                        item_unit = matched_summary_item.get("单位") or matched_summary_item.get("product_unit", "公斤")
                        if not item_unit_price:
                            item_unit_price = matched_summary_item.get("单价") or matched_summary_item.get("unit_price", 0)
                    
                    # 如果分拨数据中有规格信息，优先使用
                    if dist_item.get("规格"):
                        item_spec = str(dist_item.get("规格"))

                    quantity = float(item_quantity)
                    unit_price = float(item_unit_price) if item_unit_price else 0
                    total_amount = quantity * unit_price

                    current_item_data = {
                        "product_name": item_name,
                        "product_code": matched_summary_item.get("product_code") if matched_summary_item else None,
                        "product_specification": item_spec,
                        "product_unit": item_unit,
                        "quantity": quantity,
                        "unit_price": unit_price,
                        "total_amount": total_amount,
                        "supplier_id": matched_summary_item.get("supplier_id") if matched_summary_item else None,
                        "category_id": matched_summary_item.get("category_id") if matched_summary_item else None,
                        "brand_id": matched_summary_item.get("brand_id") if matched_summary_item else None,
                    }
                    current_store_distributions.append(current_item_data)
                    store_total_allocated_quantity += quantity
                    store_total_allocated_amount += total_amount

                # Only add stores with actual distributions
                if current_store_distributions:
                    distribution_destinations.append({
                        "store_name": store_name,
                        "target_name": store_name,
                        "matched_name": store_name,
                        "confidence": 1.0,
                        "type": "store",
                        "target_id": None,
                        "items": current_store_distributions,
                        "total_quantity": store_total_allocated_quantity,
                        "total_amount": store_total_allocated_amount,
                        "task_id": str(self.task_id)
                    })
                    logger.info(f"Store '{store_name}': {len(current_store_distributions)} items, total quantity: {store_total_allocated_quantity}")
                else:
                    logger.warning(f"Store '{store_name}' had no valid distributions after processing")
        
        if not distribution_destinations:
            logger.warning("No distribution destinations were created. This might indicate issues with AI extraction.")

        final_result["distribution_destinations"] = distribution_destinations
        
        # 添加前端期望的preview格式
        final_result["preview"] = {
            "purchase_items": standardized_items,
            "distribution_details": {store["store_name"]: store["items"] for store in distribution_destinations}
        }
        
        # Log the final distribution_destinations before returning
        if distribution_destinations:
            logger.info(f"_convert_ai_result_to_purchase_format: Populated distribution_destinations with {len(distribution_destinations)} entries.")
            logger.debug(f"Final distribution_destinations (first entry or 500 chars): {str(distribution_destinations[0])[:500] if distribution_destinations else '[]'}")
        else:
            logger.info("_convert_ai_result_to_purchase_format: distribution_destinations is empty.")
            logger.debug(f"Debug: purchase_summary_items count: {len(standardized_items)}")
            logger.debug(f"Debug: distribution_details_by_store was {'None' if distribution_details_by_store is None else 'Not None with ' + str(len(distribution_details_by_store)) + ' keys'}")

        return final_result 