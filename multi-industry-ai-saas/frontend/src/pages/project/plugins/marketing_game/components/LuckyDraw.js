import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  Button,
  message as antMessage,
  Modal,
  Typography,
  Spin,
  List,
  Avatar,
  Tag,
  Divider,
  Input,
  Form,
  Switch,
  Result,
  Empty
} from 'antd';
import {
  TrophyOutlined,
  HistoryOutlined,
  UserOutlined,
  SoundOutlined,
  LoadingOutlined,
  GiftOutlined,
  SmileOutlined,
  CloseCircleOutlined
} from '@ant-design/icons';
import marketingGameApi from '../service/api/marketingGame';
import './LuckyDraw.css';

const { Title, Paragraph, Text } = Typography;

// 格式化为中国时间（东八区）
const formatToChineseTime = (timeString) => {
  if (!timeString) return '未知时间';

  // 创建日期对象
  const date = new Date(timeString);

  // 检查日期是否有效
  if (isNaN(date.getTime())) return '无效时间';

  // 转换为东八区时间
  const options = {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false,
    timeZone: 'Asia/Shanghai'
  };

  return new Date(date).toLocaleString('zh-CN', options);
};

// 默认奖品图标
const DEFAULT_PRIZE_ICONS = {
  none: 'https://img.icons8.com/color/96/000000/gift--v1.png',
  coupon: 'https://img.icons8.com/color/96/000000/coupon--v1.png',
  points: 'https://img.icons8.com/color/96/000000/prize--v1.png',
  product: 'https://img.icons8.com/color/96/000000/shopping-bag--v1.png',
  thanks: 'https://img.icons8.com/color/96/000000/sad--v1.png'
};

// 主题背景图片
const THEME_BACKGROUNDS = {
  classic: 'https://img.freepik.com/free-vector/abstract-colorful-shapes-background_23-2148769631.jpg',
  apple: 'https://img.freepik.com/free-vector/gradient-white-monochrome-background_23-2149011361.jpg',
  cartoon: 'https://img.freepik.com/free-vector/flat-abstract-colorful-decorative-background_23-2148874272.jpg',
  business: 'https://img.freepik.com/free-vector/white-abstract-background_23-2148810113.jpg',
  ghibli: 'https://img.freepik.com/free-vector/watercolor-nature-background-with-leaves_52683-61481.jpg'
};

/**
 * 随机抽奖组件
 * 完全重写的新版本，支持多种主题风格
 */
const LuckyDraw = ({ gameId }) => {
  // 状态管理
  const [loading, setLoading] = useState(true);
  const [game, setGame] = useState(null);
  const [prizes, setPrizes] = useState([]);
  const [spinning, setSpinning] = useState(false);
  const [winningPrize, setWinningPrize] = useState(null);
  const [showResult, setShowResult] = useState(false);
  const [remainingDraws, setRemainingDraws] = useState(0);
  const [soundEnabled, setSoundEnabled] = useState(true);
  const [showPrizes, setShowPrizes] = useState(false);
  const [showRecords, setShowRecords] = useState(false);
  const [showRules, setShowRules] = useState(false);
  const [records, setRecords] = useState([]);
  const [receiptVerified, setReceiptVerified] = useState(false);
  const [receiptCode, setReceiptCode] = useState('');
  const [receiptAmount, setReceiptAmount] = useState('');
  const [verifying, setVerifying] = useState(false);
  const [phoneVerified, setPhoneVerified] = useState(false);
  const [phone, setPhone] = useState('');
  const [verifyingPhone, setVerifyingPhone] = useState(false);
  const [showVerification, setShowVerification] = useState(false);

  // 引用
  const wheelRef = useRef(null);
  const spinSound = useRef(new Audio('/sounds/wheel-spin.mp3'));
  const winSound = useRef(new Audio('/sounds/win.mp3'));
  const loseSound = useRef(new Audio('/sounds/lose.mp3'));

  // 主题样式配置
  const themeStyles = {
    classic: {
      colors: [
        '#FF9800', // 橙色
        '#2196F3', // 蓝色
        '#4CAF50', // 绿色
        '#E91E63', // 粉色
        '#673AB7', // 紫色
        '#00BCD4', // 青色
        '#FFC107', // 黄色
        '#607D8B'  // 蓝灰色
      ],
      borderColor: '#ff5722',
      shadowColor: '#ffeb3b',
      centerBgColor: 'white',
      fontColor: '#333',
      buttonBgColor: '#ff5722'
    },
    apple: {
      colors: ['#5AC8FA', '#34C759', '#FF9500', '#FF2D55', '#AF52DE', '#007AFF', '#5856D6', '#FF3B30'],
      borderColor: '#007AFF',
      shadowColor: '#5AC8FA',
      centerBgColor: 'white',
      fontColor: '#333',
      buttonBgColor: '#007AFF'
    },
    cartoon: {
      colors: ['#FF6B6B', '#4ECDC4', '#FFE66D', '#6699CC', '#FF9A76', '#88D8B0', '#FF6F91', '#C5D86D'],
      borderColor: '#FF6B6B',
      shadowColor: '#FFE66D',
      centerBgColor: 'white',
      fontColor: '#333',
      buttonBgColor: '#FF6B6B'
    },
    business: {
      colors: ['#2C3E50', '#3498DB', '#1ABC9C', '#E74C3C', '#9B59B6', '#F1C40F', '#16A085', '#C0392B'],
      borderColor: '#34495E',
      shadowColor: '#BDC3C7',
      centerBgColor: '#ECF0F1',
      fontColor: '#2C3E50',
      buttonBgColor: '#3498DB'
    },
    ghibli: {
      colors: ['#1A7A89', '#D4A017', '#7CAE7A', '#E86A92', '#7C90A0', '#FFB067', '#9B8BB4', '#5B8E7D'],
      borderColor: '#1A7A89',
      shadowColor: '#D4A017',
      centerBgColor: 'white',
      fontColor: '#333',
      buttonBgColor: '#1A7A89'
    }
  };

  // 获取当前主题样式
  const getCurrentTheme = (themeStyle = 'classic') => {
    return themeStyles[themeStyle] || themeStyles.classic;
  };

  // 将fetchGameData提升到组件作用域
  const fetchGameData = useCallback(async () => {
    try {
      setLoading(true);

      // 获取游戏详情
      const gameResponse = await marketingGameApi.getGameDetail(gameId);
      if (!gameResponse.success) {
        antMessage.error(gameResponse.message || '获取游戏详情失败');
        setLoading(false);
        return;
      }

      // 确保game.remaining_draws有默认值
      const gameData = {
        ...gameResponse.data,
        remaining_draws: gameResponse.data.remaining_draws ||
                         gameResponse.data.max_draws_per_user ||
                         0,
        // 添加默认配置
        settings: {
          ...gameResponse.data.settings,
          enable_receipt_verification:
            gameResponse.data.settings?.enable_receipt_verification !== undefined
              ? gameResponse.data.settings.enable_receipt_verification
              : gameResponse.data.min_consumption > 0,
          theme_style: gameResponse.data.settings?.theme_style || 'classic'
        }
      };

      setGame(gameData);
      setRemainingDraws(gameData.remaining_draws);

      // 获取奖品列表
      const prizesResponse = await marketingGameApi.getPrizes(gameId);
      if (!prizesResponse.success) {
        antMessage.error(prizesResponse.message || '获取奖品列表失败');
        setLoading(false);
        return;
      }

      // 使用原始奖品列表，不进行填充
      let prizeList = prizesResponse.data || [];

      // 如果没有奖品，添加一个默认奖品
      if (prizeList.length === 0) {
        const defaultPrize = {
          id: 'default',
          name: '谢谢参与',
          image_url: DEFAULT_PRIZE_ICONS.thanks,
          probability: 100,
          is_default: true
        };
        prizeList.push(defaultPrize);
      }

      console.log("使用原始奖品列表，不进行填充:", prizeList);

      setPrizes(prizeList);

      // 获取中奖记录
      const recordsResponse = await marketingGameApi.getDrawRecords(gameId);
      if (recordsResponse.success) {
        // 只显示最新的5条记录，并按时间倒序排序
        const sortedRecords = (recordsResponse.data || [])
          .sort((a, b) => new Date(b.created_at || b.draw_time) - new Date(a.created_at || a.draw_time))
          .slice(0, 5);
        setRecords(sortedRecords);
      }

      setLoading(false);
    } catch (error) {
      console.error('Error fetching game data:', error);
      antMessage.error('获取游戏数据失败，请稍后再试');
      setLoading(false);
    }
  }, [gameId]);

  // 初始化
  useEffect(() => {
    fetchGameData();
  }, [fetchGameData]);

  // 调整颜色亮度
  const adjustColor = (color, amount) => {
    return '#' + color.replace(/^#/, '').replace(/../g, color => ('0' + Math.min(255, Math.max(0, parseInt(color, 16) + amount)).toString(16)).slice(-2));
  };

  // 判断颜色是否为浅色
  const isLightColor = (color) => {
    const hex = color.replace('#', '');
    const r = parseInt(hex.substr(0, 2), 16);
    const g = parseInt(hex.substr(2, 2), 16);
    const b = parseInt(hex.substr(4, 2), 16);
    const brightness = (r * 299 + g * 587 + b * 114) / 1000;
    return brightness > 155;
  };

  // 处理抽奖
  const handleDraw = async () => {
    // 检查是否需要验证
    if (game?.settings?.enable_receipt_verification && !receiptVerified) {
      setShowVerification(true);
      return;
    }

    if (game?.settings?.enable_phone_verification && !phoneVerified) {
      setShowVerification(true);
      return;
    }

    try {
      setSpinning(true);
      if (soundEnabled) {
        spinSound.current.play();
      }

      // 调用抽奖API
      const response = await marketingGameApi.draw(gameId);
      if (!response.success) {
        antMessage.error(response.message || '抽奖失败');
        setSpinning(false);
        return;
      }

      const winPrize = response.data;

      // 找到中奖奖品在转盘上的位置
      const prizeIndex = prizes.findIndex(p => p.id === winPrize.id);

      // 计算旋转角度
      const baseAngle = 360 / prizes.length;
      const targetAngle = 360 * 5 + (360 - (prizeIndex * baseAngle + baseAngle / 2));

      // 设置转盘旋转
      if (wheelRef.current) {
        wheelRef.current.style.transform = `rotate(${targetAngle}deg)`;
      }

      // 更新剩余抽奖次数
      setRemainingDraws(prev => Math.max(0, prev - 1));

      // 旋转结束后显示结果
      setTimeout(() => {
        setWinningPrize(winPrize);
        setShowResult(true);
        setSpinning(false);

        // 播放音效
        if (soundEnabled) {
          if (winPrize.is_default) {
            loseSound.current.play();
          } else {
            winSound.current.play();
          }
        }

        // 不刷新整个游戏数据，只更新中奖记录
        // 手动更新中奖记录，确保UI立即更新
        marketingGameApi.getDrawRecords(gameId, { limit: 10 }).then(recordsResponse => {
          if (recordsResponse.success) {
            // 确保按时间倒序排列，最新的记录在前面
            let allRecords = recordsResponse.data || [];

            // 确保记录是数组
            if (!Array.isArray(allRecords)) {
              allRecords = allRecords.items || allRecords.results || [];
            }

            // 确保每条记录都有必要的字段
            const processedRecords = allRecords.map(record => ({
              ...record,
              id: record.id || `record_${Math.random()}`,
              created_at: record.created_at || record.draw_time || new Date().toISOString(),
              participant: record.participant || {
                name: record.participant_name || '匿名用户',
                phone: record.phone || ''
              },
              prize: record.prize || {
                name: record.prize_name || '未知奖品',
                is_default: record.is_default || false,
                image_url: record.prize_image || DEFAULT_PRIZE_ICONS.gift,
                value: record.prize_value || 0
              }
            }));

            // 按时间倒序排序并只取前5条
            const sortedRecords = processedRecords
              .sort((a, b) => new Date(b.created_at || 0) - new Date(a.created_at || 0))
              .slice(0, 5);

            setRecords(sortedRecords);
            console.log("已更新中奖记录:", sortedRecords);
          }
        });
      }, 4000); // 与CSS中的transition时间一致
    } catch (error) {
      console.error('Error during draw:', error);
      antMessage.error('抽奖失败，请稍后再试');
      setSpinning(false);
    }
  };

  // 验证小票
  const handleVerifyReceipt = async () => {
    if (!receiptCode) {
      antMessage.error('请输入小票号码');
      return;
    }

    if (game?.min_consumption > 0 && (!receiptAmount || parseFloat(receiptAmount) < game.min_consumption)) {
      antMessage.error(`小票金额不能低于${game.min_consumption}元`);
      return;
    }

    try {
      setVerifying(true);

      // 调用验证API
      const response = await marketingGameApi.verifyReceipt(gameId, {
        receipt_code: receiptCode,
        amount: receiptAmount
      });

      if (response.success) {
        antMessage.success('小票验证成功');
        setReceiptVerified(true);

        // 如果设置了自动抽奖，则自动开始抽奖
        if (game?.settings?.auto_draw) {
          handleDraw();
        }
      } else {
        antMessage.error(response.message || '小票验证失败');
      }
    } catch (error) {
      console.error('Error verifying receipt:', error);
      antMessage.error('小票验证失败，请稍后再试');
    } finally {
      setVerifying(false);
    }
  };

  // 验证手机号
  const handleVerifyPhone = async () => {
    if (!phone || !/^1[3-9]\d{9}$/.test(phone)) {
      antMessage.error('请输入有效的手机号码');
      return;
    }

    try {
      setVerifyingPhone(true);

      // 调用验证API
      const response = await marketingGameApi.verifyPhone(gameId, {
        phone: phone
      });

      if (response.success) {
        antMessage.success('手机号验证成功');
        setPhoneVerified(true);

        // 如果设置了自动抽奖，则自动开始抽奖
        if (game?.settings?.auto_draw) {
          handleDraw();
        }
      } else {
        antMessage.error(response.message || '手机号验证失败');
      }
    } catch (error) {
      console.error('Error verifying phone:', error);
      antMessage.error('手机号验证失败，请稍后再试');
    } finally {
      setVerifyingPhone(false);
    }
  };

  // 渲染加载状态
  if (loading) {
    return (
      <div className="lucky-draw-loading">
        <LoadingOutlined className="loading-icon" />
        <div className="loading-text">加载中，请稍候...</div>
      </div>
    );
  }

  // 获取当前主题
  const themeStyle = game?.settings?.theme_style || 'classic';
  const currentTheme = getCurrentTheme(themeStyle);
  const backgroundImage = THEME_BACKGROUNDS[themeStyle] || THEME_BACKGROUNDS.classic;

  return (
    <div
      className="lucky-draw-container"
      style={{ backgroundImage: `url(${backgroundImage})` }}
    >
      <div className="lucky-draw-header">
        <Title level={2} className="game-title">{game?.name || '幸运抽奖'}</Title>
        <Paragraph className="game-description">{game?.description || '参与抽奖，赢取精美礼品'}</Paragraph>
      </div>

      <div className="lucky-draw-content">
        <div className="settings-bar">
          <div className="settings-item">
            <span className="settings-label">音效</span>
            <Switch
              checked={soundEnabled}
              onChange={setSoundEnabled}
              size="small"
              checkedChildren={<SoundOutlined />}
              unCheckedChildren={<SoundOutlined />}
            />
          </div>
        </div>

        <div className="wheel-container">
          <div
            className={`pointer-${themeStyle}`}
          ></div>
          <div
            className={`wheel-${themeStyle}`}
            ref={wheelRef}
            style={{
              '--prize-count': prizes.length,
            }}
          >
            {prizes.map((prize, index) => {
              const angle = (360 / prizes.length) * index;
              // 确保所有扇形都有颜色，不会出现白色扇形
              const backgroundColor = currentTheme.colors[index % currentTheme.colors.length];

              return (
                <div
                  key={prize.id}
                  className="wheel-item"
                  style={{
                    transform: `rotate(${angle}deg)`,
                    backgroundColor,
                    // 添加一个渐变效果，使颜色更加丰富
                    background: `radial-gradient(circle at bottom right, ${backgroundColor}, ${adjustColor(backgroundColor, 20)})`
                  }}
                >
                  <div
                    className="wheel-item-content"
                    style={{
                      // 根据背景色调整文字颜色以确保可读性
                      color: isLightColor(backgroundColor) ? '#333' : '#fff'
                    }}
                  >
                    <img
                      src={prize.image_url || DEFAULT_PRIZE_ICONS.none}
                      alt={prize.name}
                      className="prize-image"
                    />
                    <div className="prize-name">{prize.name}</div>
                  </div>
                </div>
              );
            })}
          </div>

          <div className={`center-${themeStyle}`}></div>

          <div className="wheel-center">
            <Button
              type="primary"
              className={`button-${themeStyle}`}
              onClick={handleDraw}
              disabled={spinning || !remainingDraws || remainingDraws <= 0}
              loading={spinning}
            >
              GO
            </Button>
          </div>
        </div>

        <div className="remaining-draws">
          剩余抽奖次数: <span className="remaining-count">{remainingDraws}</span>
        </div>

        <div className="action-buttons">
          <Button
            icon={<TrophyOutlined />}
            onClick={() => setShowPrizes(true)}
            className="action-button"
          >
            奖品设置
          </Button>
          <Button
            icon={<HistoryOutlined />}
            onClick={() => setShowRecords(true)}
            className="action-button"
          >
            中奖记录
          </Button>
          <Button
            icon={<UserOutlined />}
            onClick={() => setShowRules(true)}
            className="action-button"
          >
            活动规则
          </Button>
        </div>
      </div>

      <div className="lucky-draw-footer">
        <div>© {new Date().getFullYear()} {game?.company_name || '营销游戏'} 版权所有</div>
        <div className="footer-links">
          <a href="#" className="footer-link">隐私政策</a>
          <a href="#" className="footer-link">用户协议</a>
        </div>
      </div>

      {/* 中奖结果弹窗 */}
      <Modal
        open={showResult}
        footer={null}
        onCancel={() => setShowResult(false)}
        width={400}
        centered
        closable={false}
        styles={{ body: { padding: '30px 20px' } }}
      >
        <Result
          icon={winningPrize?.is_default ? <SmileOutlined /> : <GiftOutlined />}
          status={winningPrize?.is_default ? 'info' : 'success'}
          title={winningPrize?.is_default ? '谢谢参与' : '恭喜中奖'}
          subTitle={winningPrize?.name}
          extra={[
            <Button key="close" onClick={() => setShowResult(false)}>
              关闭
            </Button>,
            !winningPrize?.is_default && (
              <Button key="claim" type="primary" onClick={() => setShowResult(false)}>
                领取奖品
              </Button>
            )
          ].filter(Boolean)}
        />
      </Modal>

      {/* 奖品列表弹窗 */}
      <Modal
        title="奖品列表"
        open={showPrizes}
        onCancel={() => setShowPrizes(false)}
        footer={null}
        width={600}
      >
        <List
          itemLayout="horizontal"
          dataSource={prizes.filter(p => !p.is_default)}
          renderItem={item => (
            <List.Item>
              <List.Item.Meta
                avatar={<Avatar src={item.image_url || DEFAULT_PRIZE_ICONS.none} />}
                title={item.name}
                description={
                  <>
                    <div>{item.description}</div>
                    <div>
                      <Tag color="blue">概率: {(item.probability || 0).toFixed(2)}%</Tag>
                      <Tag color="green">剩余: {item.remaining || item.remaining_quantity || '不限'}</Tag>
                    </div>
                  </>
                }
              />
            </List.Item>
          )}
          locale={{
            emptyText: <Empty description="暂无奖品" />
          }}
        />
      </Modal>

      {/* 中奖记录弹窗 */}
      <Modal
        title="中奖记录"
        open={showRecords}
        onCancel={() => setShowRecords(false)}
        footer={null}
        width={600}
      >
        <List
          itemLayout="horizontal"
          dataSource={records.filter(r => !r.prize?.is_default)}
          renderItem={item => (
            <List.Item>
              <List.Item.Meta
                avatar={<Avatar src={item.prize?.image_url || DEFAULT_PRIZE_ICONS.none} />}
                title={item.prize?.name}
                description={
                  <>
                    <div>用户: {
                      // 如果有手机号，显示后四位
                      item.participant?.phone ?
                        `${item.participant.name || '用户'}(${item.participant.phone.slice(-4)})` :
                        (item.participant?.name || item.user_name || '匿名用户')
                    }</div>
                    <div>时间: {formatToChineseTime(item.created_at || item.draw_time)}</div>
                    {item.prize?.description && <div>描述: {item.prize.description}</div>}
                    {item.prize?.value > 0 && <div>价值: ¥{(item.prize.value || 0).toFixed(2)}</div>}
                  </>
                }
              />
            </List.Item>
          )}
          locale={{
            emptyText: <Empty description="暂无中奖记录" />
          }}
        />
      </Modal>

      {/* 活动规则弹窗 */}
      <Modal
        title="活动规则"
        open={showRules}
        onCancel={() => setShowRules(false)}
        footer={null}
        width={600}
      >
        <div>
          <Paragraph>
            <strong>活动时间：</strong>
            {game?.start_time && game?.end_time
              ? `${new Date(game.start_time).toLocaleDateString()} 至 ${new Date(game.end_time).toLocaleDateString()}`
              : '长期有效'}
          </Paragraph>

          <Paragraph>
            <strong>参与方式：</strong>
            {game?.settings?.enable_receipt_verification
              ? `消费满${game?.min_consumption || 0}元，凭小票参与抽奖`
              : '无门槛参与'}
          </Paragraph>

          <Paragraph>
            <strong>抽奖次数：</strong>
            每人每天最多可抽奖{game?.max_draws_per_user || '不限'}次
          </Paragraph>

          <Divider />

          <Paragraph>
            <strong>奖品设置：</strong>
          </Paragraph>

          <List
            itemLayout="horizontal"
            dataSource={prizes.filter(p => !p.is_default)}
            renderItem={item => (
              <List.Item>
                <List.Item.Meta
                  avatar={<Avatar src={item.image_url || DEFAULT_PRIZE_ICONS.none} />}
                  title={item.name}
                  description={item.description}
                />
              </List.Item>
            )}
            locale={{
              emptyText: <Empty description="暂无奖品" />
            }}
          />

          <Divider />

          <Paragraph>
            <strong>注意事项：</strong>
          </Paragraph>

          <Paragraph>
            1. 实物奖品请在中奖后30天内领取，逾期视为自动放弃。
          </Paragraph>

          <Paragraph>
            2. 活动最终解释权归{game?.company_name || '主办方'}所有。
          </Paragraph>
        </div>
      </Modal>

      {/* 验证弹窗 */}
      <Modal
        title="验证信息"
        open={showVerification}
        onCancel={() => setShowVerification(false)}
        footer={null}
        width={400}
      >
        <div>
          {game?.settings?.enable_phone_verification && !phoneVerified && (
            <>
              <Form layout="vertical">
                <Form.Item
                  label="手机号码"
                  required
                  rules={[{ required: true, message: '请输入手机号码' }]}
                >
                  <Input
                    placeholder="请输入手机号码"
                    value={phone}
                    onChange={e => setPhone(e.target.value)}
                  />
                </Form.Item>
              </Form>

              <div style={{ textAlign: 'right', marginBottom: 16 }}>
                <Button
                  type="primary"
                  onClick={handleVerifyPhone}
                  loading={verifyingPhone}
                >
                  验证手机号
                </Button>
              </div>

              <Divider />
            </>
          )}

          {game?.settings?.enable_receipt_verification && !receiptVerified && (
            <Form layout="vertical">
              <Form.Item
                label="小票号码"
                required
                rules={[{ required: true, message: '请输入小票号码' }]}
              >
                <Input
                  placeholder="请输入小票号码"
                  value={receiptCode}
                  onChange={e => setReceiptCode(e.target.value)}
                />
              </Form.Item>

              {game?.min_consumption > 0 && (
                <Form.Item
                  label={`消费金额（最低${game.min_consumption}元）`}
                  required
                  rules={[{ required: true, message: '请输入消费金额' }]}
                >
                  <Input
                    placeholder="请输入消费金额"
                    value={receiptAmount}
                    onChange={e => setReceiptAmount(e.target.value)}
                    type="number"
                    min={game.min_consumption}
                    addonAfter="元"
                  />
                </Form.Item>
              )}

              <div style={{ textAlign: 'right' }}>
                <Button
                  type="primary"
                  onClick={handleVerifyReceipt}
                  loading={verifying}
                >
                  验证小票
                </Button>
              </div>
            </Form>
          )}

          {phoneVerified && receiptVerified && (
            <Result
              status="success"
              title="验证成功"
              subTitle="您已完成所有验证，可以开始抽奖了"
              extra={[
                <Button
                  key="draw"
                  type="primary"
                  onClick={() => {
                    setShowVerification(false);
                    handleDraw();
                  }}
                >
                  开始抽奖
                </Button>
              ]}
            />
          )}
        </div>
      </Modal>
    </div>
  );
};

export default LuckyDraw;
