import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Modal,
  Form,
  Input,
  Select,
  DatePicker,
  InputNumber,
  message,
  Typography,
  Row,
  Col,
  Statistic,
  Progress,
  Alert,
  Tooltip,
  Badge,
  Descriptions,
  Steps
} from 'antd';
import {
  GiftOutlined,
  CalendarOutlined,
  DollarOutlined,
  TrophyOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  EyeOutlined,
  FormOutlined,
  SyncOutlined
} from '@ant-design/icons';
import { useParams } from 'react-router-dom';
import multiChannelService from '../services/multiChannelService';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;
const { RangePicker } = DatePicker;
const { Step } = Steps;

const PlatformActivities = () => {
  const { projectId } = useParams();
  const [loading, setLoading] = useState(false);
  const [activities, setActivities] = useState([]);
  const [channels, setChannels] = useState([]);
  const [selectedActivity, setSelectedActivity] = useState(null);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [applyModalVisible, setApplyModalVisible] = useState(false);
  const [selectedChannel, setSelectedChannel] = useState(null);
  const [form] = Form.useForm();

  useEffect(() => {
    loadData();
  }, [projectId]);

  const loadData = async () => {
    try {
      setLoading(true);
      await Promise.all([
        loadActivities(),
        loadChannels()
      ]);
    } catch (error) {
      console.error('加载数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadActivities = async () => {
    try {
      const response = await multiChannelService.getPlatformActivities(projectId);
      setActivities(response.data || []);
    } catch (error) {
      console.error('获取平台活动失败:', error);
    }
  };

  const loadChannels = async () => {
    try {
      const response = await multiChannelService.getChannels(projectId);
      setChannels(response.data || []);
    } catch (error) {
      console.error('获取渠道列表失败:', error);
    }
  };

  const handleViewDetail = (activity) => {
    setSelectedActivity(activity);
    setDetailModalVisible(true);
  };

  const handleApplyActivity = (activity) => {
    setSelectedActivity(activity);
    setApplyModalVisible(true);
    form.resetFields();
  };

  const handleSubmitApplication = async (values) => {
    try {
      await multiChannelService.applyPlatformActivity(projectId, {
        activity_id: selectedActivity.id,
        ...values
      });
      message.success('报名申请已提交');
      setApplyModalVisible(false);
      loadActivities();
    } catch (error) {
      console.error('报名失败:', error);
      message.error('报名失败');
    }
  };

  const handleSyncActivities = async (channelId) => {
    try {
      setLoading(true);
      await multiChannelService.syncPlatformActivities(projectId, channelId);
      message.success('同步成功');
      loadActivities();
    } catch (error) {
      console.error('同步失败:', error);
      message.error('同步失败');
    } finally {
      setLoading(false);
    }
  };

  const getActivityTypeTag = (type) => {
    const typeMap = {
      'subsidy_1v1': { color: 'red', text: '1:1补贴' },
      'single_product': { color: 'orange', text: '单品补贴' },
      'full_reduction': { color: 'blue', text: '满减活动' },
      'discount': { color: 'green', text: '折扣活动' },
      'new_user': { color: 'purple', text: '新用户活动' },
      'flash_sale': { color: 'volcano', text: '限时抢购' }
    };
    const config = typeMap[type] || { color: 'default', text: type };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  const getStatusTag = (status) => {
    const statusMap = {
      'upcoming': { color: 'blue', icon: <ClockCircleOutlined />, text: '即将开始' },
      'ongoing': { color: 'green', icon: <CheckCircleOutlined />, text: '进行中' },
      'ended': { color: 'default', icon: <ExclamationCircleOutlined />, text: '已结束' },
      'applied': { color: 'orange', icon: <FormOutlined />, text: '已报名' },
      'approved': { color: 'success', icon: <CheckCircleOutlined />, text: '已通过' },
      'rejected': { color: 'error', icon: <ExclamationCircleOutlined />, text: '已拒绝' }
    };
    const config = statusMap[status] || { color: 'default', text: status };
    return <Tag color={config.color} icon={config.icon}>{config.text}</Tag>;
  };

  const getChannelName = (channelId) => {
    const channel = channels.find(c => c.id === channelId);
    return channel ? channel.custom_name : '未知渠道';
  };

  const calculateProgress = (startTime, endTime) => {
    const now = new Date();
    const start = new Date(startTime);
    const end = new Date(endTime);
    
    if (now < start) return 0;
    if (now > end) return 100;
    
    const total = end - start;
    const elapsed = now - start;
    return Math.round((elapsed / total) * 100);
  };

  const columns = [
    {
      title: '活动名称',
      dataIndex: 'name',
      key: 'name',
      render: (name, record) => (
        <div>
          <Text strong>{name}</Text>
          <div style={{ marginTop: 4 }}>
            {getActivityTypeTag(record.activity_type)}
            <Tag color="blue" style={{ marginLeft: 4 }}>
              {getChannelName(record.channel_id)}
            </Tag>
          </div>
        </div>
      )
    },
    {
      title: '活动时间',
      dataIndex: 'start_time',
      key: 'start_time',
      render: (startTime, record) => (
        <div>
          <div>
            <CalendarOutlined style={{ marginRight: 4 }} />
            <Text>{new Date(startTime).toLocaleDateString()}</Text>
          </div>
          <div style={{ marginTop: 4 }}>
            <Text type="secondary">
              至 {new Date(record.end_time).toLocaleDateString()}
            </Text>
          </div>
          {record.status === 'ongoing' && (
            <Progress
              percent={calculateProgress(startTime, record.end_time)}
              size="small"
              style={{ marginTop: 4 }}
            />
          )}
        </div>
      )
    },
    {
      title: '补贴信息',
      dataIndex: 'subsidy_info',
      key: 'subsidy_info',
      render: (subsidyInfo, record) => (
        <div>
          {subsidyInfo?.rate && (
            <div>
              <DollarOutlined style={{ marginRight: 4 }} />
              <Text>补贴比例: {subsidyInfo.rate}%</Text>
            </div>
          )}
          {subsidyInfo?.max_amount && (
            <div style={{ marginTop: 4 }}>
              <Text type="secondary">
                最高: ¥{subsidyInfo.max_amount}
              </Text>
            </div>
          )}
        </div>
      )
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => getStatusTag(status)
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => handleViewDetail(record)}
            />
          </Tooltip>
          {record.status === 'upcoming' && (
            <Tooltip title="报名参加">
              <Button
                type="text"
                icon={<FormOutlined />}
                onClick={() => handleApplyActivity(record)}
              />
            </Tooltip>
          )}
        </Space>
      )
    }
  ];

  const renderActivityStats = () => {
    const stats = {
      total: activities.length,
      ongoing: activities.filter(a => a.status === 'ongoing').length,
      applied: activities.filter(a => a.status === 'applied').length,
      approved: activities.filter(a => a.status === 'approved').length
    };

    return (
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总活动数"
              value={stats.total}
              prefix={<GiftOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="进行中"
              value={stats.ongoing}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="已报名"
              value={stats.applied}
              prefix={<FormOutlined />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="已通过"
              value={stats.approved}
              prefix={<TrophyOutlined />}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
      </Row>
    );
  };

  return (
    <div>
      {renderActivityStats()}

      <Card
        title={
          <Space>
            <GiftOutlined />
            <span>平台活动管理</span>
            <Badge count={activities.length} />
          </Space>
        }
        extra={
          <Space>
            <Select
              placeholder="选择渠道同步活动"
              style={{ width: 200 }}
              onChange={(channelId) => handleSyncActivities(channelId)}
            >
              {channels.map(channel => (
                <Option key={channel.id} value={channel.id}>
                  <SyncOutlined style={{ marginRight: 4 }} />
                  {channel.custom_name}
                </Option>
              ))}
            </Select>
          </Space>
        }
      >
        <Alert
          message="平台活动说明"
          description="这里展示各个平台的官方活动和补贴政策，您可以查看活动详情并报名参加。不同平台的活动类型和补贴方式可能不同，请仔细阅读活动规则。"
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />

        <Table
          columns={columns}
          dataSource={activities}
          rowKey="id"
          loading={loading}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`
          }}
        />
      </Card>

      {/* 活动详情模态框 */}
      <Modal
        title="活动详情"
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={null}
        width={800}
      >
        {selectedActivity && (
          <div>
            <Descriptions title={selectedActivity.name} bordered>
              <Descriptions.Item label="活动类型" span={2}>
                {getActivityTypeTag(selectedActivity.activity_type)}
              </Descriptions.Item>
              <Descriptions.Item label="状态">
                {getStatusTag(selectedActivity.status)}
              </Descriptions.Item>
              <Descriptions.Item label="所属渠道" span={3}>
                <Tag color="blue">{getChannelName(selectedActivity.channel_id)}</Tag>
              </Descriptions.Item>
              <Descriptions.Item label="活动时间" span={3}>
                {new Date(selectedActivity.start_time).toLocaleString()} 至{' '}
                {new Date(selectedActivity.end_time).toLocaleString()}
              </Descriptions.Item>
              {selectedActivity.subsidy_info && (
                <>
                  <Descriptions.Item label="补贴比例">
                    {selectedActivity.subsidy_info.rate}%
                  </Descriptions.Item>
                  <Descriptions.Item label="最高补贴">
                    ¥{selectedActivity.subsidy_info.max_amount}
                  </Descriptions.Item>
                  <Descriptions.Item label="最低消费">
                    ¥{selectedActivity.subsidy_info.min_order || 0}
                  </Descriptions.Item>
                </>
              )}
            </Descriptions>

            <div style={{ marginTop: 24 }}>
              <Title level={5}>活动描述</Title>
              <Paragraph>{selectedActivity.description}</Paragraph>
            </div>

            {selectedActivity.rules && (
              <div style={{ marginTop: 16 }}>
                <Title level={5}>参与规则</Title>
                <Paragraph>{selectedActivity.rules}</Paragraph>
              </div>
            )}
          </div>
        )}
      </Modal>

      {/* 报名申请模态框 */}
      <Modal
        title="活动报名"
        open={applyModalVisible}
        onCancel={() => setApplyModalVisible(false)}
        footer={null}
        width={600}
      >
        {selectedActivity && (
          <div>
            <Alert
              message={`报名参加：${selectedActivity.name}`}
              description="请填写报名信息，提交后等待平台审核。"
              type="info"
              showIcon
              style={{ marginBottom: 24 }}
            />

            <Form
              form={form}
              layout="vertical"
              onFinish={handleSubmitApplication}
            >
              <Form.Item
                name="products"
                label="参与商品"
                rules={[{ required: true, message: '请选择参与商品' }]}
              >
                <Select
                  mode="multiple"
                  placeholder="选择要参与活动的商品"
                  showSearch
                  filterOption={(input, option) =>
                    option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                  }
                >
                  {/* 这里需要加载商品列表 */}
                </Select>
              </Form.Item>

              <Form.Item
                name="expected_sales"
                label="预期销量"
              >
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="输入预期销量"
                  min={1}
                />
              </Form.Item>

              <Form.Item
                name="remarks"
                label="备注信息"
              >
                <Input.TextArea
                  rows={3}
                  placeholder="输入备注信息（可选）"
                />
              </Form.Item>

              <div style={{ textAlign: 'right', marginTop: 24 }}>
                <Space>
                  <Button onClick={() => setApplyModalVisible(false)}>
                    取消
                  </Button>
                  <Button type="primary" htmlType="submit">
                    提交报名
                  </Button>
                </Space>
              </div>
            </Form>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default PlatformActivities;
