import asyncio
from typing import List, Dict, Any, Optional, Union
from sqlalchemy import and_, select, func, update
from uuid import UUID
from sqlalchemy.orm import selectinload, joinedload

from models.store import Store, StoreCategory, StoreRegion, ProjectUserStore
from schemas.store import StoreCreate, StoreUpdate, StoreCategoryCreate, StoreCategoryUpdate, StoreRegionCreate, StoreRegionUpdate
from models.project_user import ProjectUser

class StoreService:
    @staticmethod
    async def get_store_by_id(db, store_id: Union[UUID, str]) -> Optional[Store]:
        """根据ID获取门店"""
        result = await db.execute(
            select(Store)
            .where(Store.id == store_id)
            .options(
                selectinload(Store.category),
                selectinload(Store.region).selectinload(StoreRegion.parent),
                selectinload(Store.project),
            )
        )
        store = result.scalars().first()
        
        # 处理区域的full_name，避免序列化时的异步访问问题
        if store and store.region:
            # 预计算full_name并将其设置为实例属性
            if store.region.parent:
                full_name = f"{store.region.parent.name}-{store.region.name}"
            else:
                full_name = store.region.name
            
            # 将full_name设置为实例属性，避免在序列化时调用property
            store.region._full_name = full_name
        
        return store

    @staticmethod
    async def get_store_by_code(db, project_id: Union[UUID, str], code: str) -> Optional[Store]:
        """根据编码获取门店"""
        result = await db.execute(select(Store).where(and_(Store.project_id == project_id, Store.code == code)))
        return result.scalars().first()

    @staticmethod
    async def get_stores(
        db,
        project_id: Union[UUID, str],
        skip: int = 0,
        limit: int = 100,
        category_id: Optional[Union[UUID, str]] = None,
        region_id: Optional[Union[UUID, str]] = None,
        status: Optional[str] = None
    ) -> List[Store]:
        """获取门店列表"""
        stmt = select(Store).where(Store.project_id == project_id)
        if category_id:
            stmt = stmt.where(Store.category_id == category_id)
        if region_id:
            stmt = stmt.where(Store.region_id == region_id)
        if status:
            stmt = stmt.where(Store.status == status)

        # 预加载关联对象，包括区域的父级关系
        stmt = stmt.options(
            selectinload(Store.category),
            selectinload(Store.region).selectinload(StoreRegion.parent),
        ).offset(skip).limit(limit)

        result = await db.execute(stmt)
        stores = result.scalars().all()

        # 处理区域的full_name，避免序列化时的异步访问问题
        for store in stores:
            if store.region:
                # 预计算full_name并将其设置为实例属性
                if store.region.parent:
                    full_name = f"{store.region.parent.name}-{store.region.name}"
                else:
                    full_name = store.region.name
                
                # 将full_name设置为实例属性，避免在序列化时调用property
                store.region._full_name = full_name

        return stores

    @staticmethod
    async def count_stores(
        db,
        project_id: Union[UUID, str],
        category_id: Optional[Union[UUID, str]] = None,
        region_id: Optional[Union[UUID, str]] = None,
        status: Optional[str] = None
    ) -> int:
        stmt = select(func.count()).select_from(Store).where(Store.project_id == project_id)
        if category_id:
            stmt = stmt.where(Store.category_id == category_id)
        if region_id:
            stmt = stmt.where(Store.region_id == region_id)
        if status:
            stmt = stmt.where(Store.status == status)
        result = await db.execute(stmt)
        return result.scalar_one()

    @staticmethod
    async def get_user_stores(
        db,
        project_id: Union[UUID, str],
        user_id: Union[UUID, str],
        skip: int = 0,
        limit: int = 100,
        category_id: Optional[Union[UUID, str]] = None,
        region_id: Optional[Union[UUID, str]] = None,
        status: Optional[str] = None
    ) -> List[Store]:
        """获取用户关联的门店列表"""
        # 首先获取用户在项目中的ProjectUser记录
        project_user_query = select(ProjectUser).where(
            ProjectUser.user_id == user_id,
            ProjectUser.project_id == project_id
        )
        project_user_result = await db.execute(project_user_query)
        project_user = project_user_result.scalars().first()

        if not project_user:
            return []

        # 查询用户关联的门店
        stmt = select(Store).join(
            ProjectUserStore,
            ProjectUserStore.store_id == Store.id
        ).where(
            Store.project_id == project_id,
            ProjectUserStore.project_user_id == project_user.id
        )

        # 添加过滤条件
        if category_id:
            stmt = stmt.where(Store.category_id == category_id)
        if region_id:
            stmt = stmt.where(Store.region_id == region_id)
        if status:
            stmt = stmt.where(Store.status == status)

        # 预加载关联对象，包括区域的父级关系
        stmt = stmt.options(
            selectinload(Store.category),
            selectinload(Store.region).selectinload(StoreRegion.parent),
        ).offset(skip).limit(limit)

        result = await db.execute(stmt)
        stores = result.scalars().all()

        # 处理区域的full_name，避免序列化时的异步访问问题
        for store in stores:
            if store.region:
                # 预计算full_name并将其设置为实例属性
                if store.region.parent:
                    full_name = f"{store.region.parent.name}-{store.region.name}"
                else:
                    full_name = store.region.name
                
                # 将full_name设置为实例属性，避免在序列化时调用property
                store.region._full_name = full_name

        return stores

    @staticmethod
    async def count_user_stores(
        db,
        project_id: Union[UUID, str],
        user_id: Union[UUID, str],
        category_id: Optional[Union[UUID, str]] = None,
        region_id: Optional[Union[UUID, str]] = None,
        status: Optional[str] = None
    ) -> int:
        """计算用户关联的门店数量"""
        # 首先获取用户在项目中的ProjectUser记录
        project_user_query = select(ProjectUser).where(
            ProjectUser.user_id == user_id,
            ProjectUser.project_id == project_id
        )
        project_user_result = await db.execute(project_user_query)
        project_user = project_user_result.scalars().first()

        if not project_user:
            return 0

        # 查询用户关联的门店数量
        stmt = select(func.count()).select_from(Store).join(
            ProjectUserStore,
            ProjectUserStore.store_id == Store.id
        ).where(
            Store.project_id == project_id,
            ProjectUserStore.project_user_id == project_user.id
        )

        # 添加过滤条件
        if category_id:
            stmt = stmt.where(Store.category_id == category_id)
        if region_id:
            stmt = stmt.where(Store.region_id == region_id)
        if status:
            stmt = stmt.where(Store.status == status)

        result = await db.execute(stmt)
        return result.scalar_one()

    @staticmethod
    async def create_store(db, store: StoreCreate) -> Store:
        """创建门店"""
        db_store = Store(
            project_id=store.project_id,
            category_id=store.category_id,
            region_id=store.region_id,
            name=store.name,
            code=store.code,
            address=store.address,
            phone=store.phone,
            latitude=store.latitude,
            longitude=store.longitude,
            business_hours=store.business_hours,
            status=store.status,
            settings=store.settings
        )
        db.add(db_store)
        await db.commit()
        await db.refresh(db_store)
        
        # 预加载关系以避免序列化时的懒加载问题
        from sqlalchemy.orm import selectinload
        result = await db.execute(
            select(Store)
            .options(selectinload(Store.category), selectinload(Store.region).selectinload(StoreRegion.parent))
            .where(Store.id == db_store.id)
        )
        store = result.scalar_one()
        
        # 处理区域的full_name，避免序列化时的异步访问问题
        if store.region:
            if store.region.parent:
                full_name = f"{store.region.parent.name}-{store.region.name}"
            else:
                full_name = store.region.name
            store.region._full_name = full_name
        
        return store

    @staticmethod
    async def update_store(db, store_id: Union[UUID, str], store_update: StoreUpdate) -> Optional[Store]:
        """更新门店"""
        db_store = await StoreService.get_store_by_id(db, store_id)
        if db_store:
            update_data = store_update.dict(exclude_unset=True)
            update_data.pop('manager', None)
            for key, value in update_data.items():
                setattr(db_store, key, value)
            await db.commit()
            await db.refresh(db_store)
            
            # 预加载关系以避免序列化时的懒加载问题
            result = await db.execute(
                select(Store)
                .options(selectinload(Store.category), selectinload(Store.region).selectinload(StoreRegion.parent))
                .where(Store.id == db_store.id)
            )
            store = result.scalar_one()
            
            # 处理区域的full_name，避免序列化时的异步访问问题
            if store.region:
                if store.region.parent:
                    full_name = f"{store.region.parent.name}-{store.region.name}"
                else:
                    full_name = store.region.name
                store.region._full_name = full_name
            
            return store
        return db_store

    @staticmethod
    async def delete_store(db, store_id: Union[UUID, str]) -> bool:
        """删除门店"""
        db_store = await StoreService.get_store_by_id(db, store_id)
        if db_store:
            await db.delete(db_store)
            await db.commit()
            return True
        return False


class StoreCategoryService:
    @staticmethod
    async def get_category_by_id(db, category_id: Union[UUID, str]) -> Optional[StoreCategory]:
        """根据ID获取门店分类"""
        result = await db.execute(select(StoreCategory).where(StoreCategory.id == category_id))
        return result.scalars().first()

    @staticmethod
    async def get_category_by_code(db, project_id: Union[UUID, str], code: str) -> Optional[StoreCategory]:
        """根据编码获取门店分类"""
        result = await db.execute(select(StoreCategory).where(and_(StoreCategory.project_id == project_id, StoreCategory.code == code)))
        return result.scalars().first()

    @staticmethod
    async def get_categories(db, project_id: Union[UUID, str], skip: int = 0, limit: int = 100) -> List[StoreCategory]:
        """获取门店分类列表"""
        stmt = select(StoreCategory).where(StoreCategory.project_id == project_id).offset(skip).limit(limit)
        result = await db.execute(stmt)
        return result.scalars().all()

    @staticmethod
    async def count_categories(db, project_id: Union[UUID, str]) -> int:
        stmt = select(func.count()).select_from(StoreCategory).where(StoreCategory.project_id == project_id)
        result = await db.execute(stmt)
        return result.scalar_one()

    @staticmethod
    async def create_category(db, project_id, category: StoreCategoryCreate) -> StoreCategory:
        """创建门店分类"""
        db_category = StoreCategory(
            project_id=project_id,
            name=category.name,
            code=category.code,
            description=category.description
        )
        db.add(db_category)
        await db.commit()
        await db.refresh(db_category)
        return db_category

    @staticmethod
    async def update_category(db, category_id: Union[UUID, str], category_update: StoreCategoryUpdate) -> Optional[StoreCategory]:
        """更新门店分类"""
        db_category = await StoreCategoryService.get_category_by_id(db, category_id)
        if db_category:
            update_data = category_update.dict(exclude_unset=True)
            for key, value in update_data.items():
                setattr(db_category, key, value)
            await db.commit()
            await db.refresh(db_category)
        return db_category

    @staticmethod
    async def delete_category(db, category_id: Union[UUID, str]) -> bool:
        """删除门店分类"""
        db_category = await StoreCategoryService.get_category_by_id(db, category_id)
        if db_category:
            # 检查是否有门店使用该分类
            stmt = select(Store).where(Store.category_id == category_id)
            result = await db.execute(stmt)
            store_count = len(result.scalars().all())
            if store_count > 0:
                return False
            await db.delete(db_category)
            await db.commit()
            return True
        return False


class StoreRegionService:
    @staticmethod
    async def get_region_by_id(db, region_id: Union[UUID, str]) -> Optional[StoreRegion]:
        """根据ID获取门店区域"""
        result = await db.execute(
            select(StoreRegion)
            .where(StoreRegion.id == region_id)
            .options(selectinload(StoreRegion.parent))
        )
        region = result.scalars().first()
        
        # 处理full_name，避免序列化时的异步访问问题
        if region:
            # 预计算full_name并将其设置为实例属性
            if region.parent:
                full_name = f"{region.parent.name}-{region.name}"
            else:
                full_name = region.name
            
            # 将full_name设置为实例属性，避免在序列化时调用property
            region._full_name = full_name
        
        return region

    @staticmethod
    async def get_region_by_code(db, project_id: Union[UUID, str], code: str) -> Optional[StoreRegion]:
        """根据编码获取门店区域"""
        result = await db.execute(select(StoreRegion).where(and_(StoreRegion.project_id == project_id, StoreRegion.code == code)))
        return result.scalars().first()

    @staticmethod
    async def get_regions(db, project_id: Union[UUID, str], skip: int = 0, limit: int = 100) -> List[StoreRegion]:
        """获取门店区域列表"""
        stmt = (
            select(StoreRegion)
            .where(StoreRegion.project_id == project_id)
            .offset(skip)
            .limit(limit)
        )
        result = await db.execute(stmt)
        return result.scalars().all()

    @staticmethod
    async def get_child_regions(db, region_id: Union[UUID, str]) -> List[StoreRegion]:
        """获取指定区域的所有子区域"""
        stmt = select(StoreRegion).where(StoreRegion.parent_id == region_id)
        result = await db.execute(stmt)
        return result.scalars().all()

    @staticmethod
    async def count_regions(db, project_id: Union[UUID, str]) -> int:
        stmt = select(func.count()).select_from(StoreRegion).where(StoreRegion.project_id == project_id)
        result = await db.execute(stmt)
        return result.scalar_one()

    @staticmethod
    async def create_region(db, project_id: Union[UUID, str], region: StoreRegionCreate) -> StoreRegion:
        """创建门店区域"""
        # 确保使用传入的 project_id，而不是 region.project_id
        db_region = StoreRegion(
            project_id=project_id,
            parent_id=region.parent_id,
            name=region.name,
            code=region.code,
            description=region.description
        )
        db.add(db_region)
        await db.commit()
        await db.refresh(db_region)
        return db_region

    @staticmethod
    async def update_region(db, project_id: Union[UUID, str], region_id: Union[UUID, str], region_update: StoreRegionUpdate) -> Optional[StoreRegion]:
        """更新门店区域"""
        db_region = await StoreRegionService.get_region_by_id(db, region_id)
        if db_region and str(db_region.project_id) == str(project_id):
            update_data = region_update.dict(exclude_unset=True)
            for key, value in update_data.items():
                setattr(db_region, key, value)
            await db.commit()
            await db.refresh(db_region)
        return db_region

    @staticmethod
    async def delete_region(db, region_id: Union[UUID, str]) -> bool:
        """删除门店区域"""
        db_region = await StoreRegionService.get_region_by_id(db, region_id)
        if db_region:
            # 检查是否有门店使用该区域
            stmt = select(Store).where(Store.region_id == region_id)
            result = await db.execute(stmt)
            store_count = len(result.scalars().all())
            if store_count > 0:
                return False
            await db.delete(db_region)
            await db.commit()
            return True
        return False


class ProjectUserStoreService:
    @staticmethod
    async def set_manager(db, store_id, project_user_id):
        # 先将该门店下所有负责人标记为 False
        await db.execute(
            update(ProjectUserStore)
            .where(ProjectUserStore.store_id == store_id)
            .values(is_manager=False)
        )
        # 再将当前 project_user_id 标记为 True
        await db.execute(
            update(ProjectUserStore)
            .where(
                ProjectUserStore.store_id == store_id,
                ProjectUserStore.project_user_id == project_user_id
            )
            .values(is_manager=True)
        )
        await db.commit()

    @staticmethod
    async def get_manager_user(db, store_id):
        result = await db.execute(
            select(ProjectUserStore)
            .options(selectinload(ProjectUserStore.project_user).selectinload(ProjectUser.user))
            .where(ProjectUserStore.store_id == store_id, ProjectUserStore.is_manager == True)
        )
        pus = result.scalars().first()
        if pus and pus.project_user and pus.project_user.user:
            return pus.project_user.user
        return None

    @staticmethod
    async def get_manager_project_user_id(db, store_id) -> Optional[UUID]:
        result = await db.execute(
            select(ProjectUserStore.project_user_id)
            .where(ProjectUserStore.store_id == store_id, ProjectUserStore.is_manager == True)
        )
        return result.scalars().first()
