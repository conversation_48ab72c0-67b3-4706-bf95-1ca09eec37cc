import api from '../httpClient';
import { getProjectId } from '../httpClient';

/**
 * 项目-报损管理API
 */
const loss = {
  /**
   * 获取报损记录列表
   * @param {Object} params - 查询参数
   * @returns {Promise} - 报损记录列表
   */
  getList: async (params = {}) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.get(`/project/${projectId}/losses`, { params });
  },

  /**
   * 获取报损记录详情
   * @param {string} id - 报损记录ID
   * @returns {Promise} - 报损记录详情
   */
  getDetail: async (id) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.get(`/project/${projectId}/losses/${id}`);
  },

  /**
   * 创建报损记录
   * @param {Object} data - 报损记录数据
   * @param {Array} images - 报损图片
   * @returns {Promise} - 创建结果
   */
  create: async (data, images = []) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }

    const formData = new FormData();
    formData.append('store_id', data.store_id);
    formData.append('product_id', data.product_id);
    formData.append('quantity', data.quantity);
    formData.append('reason', data.reason);

    if (data.notes) {
      formData.append('notes', data.notes);
    }

    // 添加图片
    if (images && images.length > 0) {
      images.forEach(image => {
        formData.append('images', image);
      });
    }

    return api.post(`/project/${projectId}/losses`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
  },

  /**
   * 更新报损记录
   * @param {string} id - 报损记录ID
   * @param {Object} data - 报损记录数据
   * @param {Array} images - 新增报损图片
   * @returns {Promise} - 更新结果
   */
  update: async (id, data, images = []) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }

    const formData = new FormData();

    if (data.quantity !== undefined) {
      formData.append('quantity', data.quantity);
    }

    if (data.reason !== undefined) {
      formData.append('reason', data.reason);
    }

    if (data.notes !== undefined) {
      formData.append('notes', data.notes);
    }

    // 添加图片
    if (images && images.length > 0) {
      images.forEach(image => {
        formData.append('images', image);
      });
    }

    return api.put(`/project/${projectId}/losses/${id}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
  },

  /**
   * 更新报损记录状态
   * @param {string} id - 报损记录ID
   * @param {Object} data - 状态数据
   * @returns {Promise} - 更新结果
   */
  updateStatus: async (id, data) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.put(`/project/${projectId}/losses/${id}/status`, data);
  },

  /**
   * 获取报损统计数据
   * @param {Object} params - 查询参数
   * @returns {Promise} - 报损统计数据
   */
  getStatistics: async (params = {}) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.get(`/project/${projectId}/losses/statistics`, { params });
  }
};

export default loss;
