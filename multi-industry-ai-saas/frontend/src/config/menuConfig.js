import React from 'react';
import { Link } from 'react-router-dom';
import {
  DashboardOutlined,
  ShopOutlined,
  BarChartOutlined,
  ScheduleOutlined,
  FileTextOutlined,
  ExceptionOutlined,
  FundOutlined,
  ShoppingOutlined,
  ShoppingCartOutlined,
  TeamOutlined,
  SettingOutlined,
  BankOutlined,
  CloudOutlined,
  AppstoreOutlined,
  RobotOutlined,
  UserOutlined,
  BellOutlined,
  DatabaseOutlined,
  ToolOutlined,
  ApartmentOutlined,
  CloudServerOutlined,
  HddOutlined,
  WarningOutlined,
  SwapOutlined,
  CheckCircleOutlined,
  SolutionOutlined,
  PartitionOutlined,
  HistoryOutlined,
  FormOutlined,
  ApiOutlined,
  MessageOutlined,
  AppstoreAddOutlined,
  GiftOutlined,
  AccountBookOutlined,
  RiseOutlined,
  DingdingOutlined,
} from '@ant-design/icons';

/**
 * 获取项目菜单配置
 * @param {Object} params 参数
 * @param {string} params.industryType 行业类型
 * @param {Function} params.hasPermission 权限检查函数
 * @returns {Array} 菜单配置
 */
export const getProjectMenuItems = ({ industryType, hasPermission, navigate }) => {
  // 基础菜单项（所有行业通用）
  const baseMenuItems = [
    // 项目管理 - 只有项目管理员可见
    {
      key: 'project_management',
      icon: <AppstoreOutlined />,
      label: '项目管理',
      children: [
        {
          key: '/project/dashboard',
          icon: <DashboardOutlined />,
          label: <Link to="/project/dashboard">项目仪表盘</Link>,
        },
        {
          key: '/project/task',
          icon: <ScheduleOutlined />,
          label: <Link to="/project/task">任务管理</Link>,
        },
        {
          key: '/project/business-analysis',
          icon: <RiseOutlined />,
          label: <Link to="/project/business-analysis">经营分析</Link>,
        },
      ],
      role: ['project_admin', 'tenant_admin'] // 只有项目管理员和租户管理员可见
    },

  ];

  // 零售行业特定菜单
  if (industryType === 'retail') {
    // 定义所有可能的菜单项
    const menuItems = [
      ...baseMenuItems,
      // 门店管理 - 门店管理员和项目管理员可见
      {
        key: 'store',
        icon: <ShopOutlined />,
        label: '门店管理',
        children: [
          {
            key: '/project/store/dashboard',
            icon: <DashboardOutlined />,
            label: <Link to="/project/store/dashboard">门店仪表盘</Link>,
          },
          {
            key: '/project/store/super-dashboard',
            icon: <BarChartOutlined />,
            label: <Link to="/project/store/super-dashboard">门店大盘</Link>,
          },
          {
            key: '/project/store/routine-tasks',
            icon: <ScheduleOutlined />,
            label: <Link to="/project/store/routine-tasks">例行任务</Link>,
          },
          {
            key: '/project/store/daily-operations',
            icon: <ShopOutlined />,
            label: <Link to="/project/store/daily-operations">日常店务</Link>,
          },
          {
            key: '/project/store/sales-report',
            icon: <FileTextOutlined />,
            label: <Link to="/project/store/sales-report">销售上报</Link>,
          },
          {
            key: '/project/store/loss-report',
            icon: <ExceptionOutlined />,
            label: <Link to="/project/store/loss-report">损耗上报</Link>,
          },
        ],
        role: ['project_admin', 'store_admin', 'tenant_admin', 'store_manager', 'store_staff'] // 包含所有门店相关角色
      },
      // 运营管理 - 运营人员和项目管理员可见
      {
        key: 'operation',
        icon: <FundOutlined />,
        label: '运营管理',
        children: [
          {
            key: '/project/operation/dashboard',
            icon: <DashboardOutlined />,
            label: <Link to="/project/operation/dashboard">运营仪表盘</Link>,
          },
          {
            key: '/project/operation/super-dashboard',
            icon: <BarChartOutlined />,
            label: <Link to="/project/operation/super-dashboard">运营大盘</Link>,
          },
          {
            key: '/project/operation/channel-management',
            icon: <PartitionOutlined />,
            label: <Link to="/project/operation/channel-management">渠道管理</Link>,
          },
          {
            key: '/project/operation/store-operation',
            icon: <ShopOutlined />,
            label: <Link to="/project/operation/store-operation">门店运营</Link>,
          },
        ],
        role: ['project_admin', 'operation_admin', 'operation_manager', 'operation_staff', 'tenant_admin']
      },
      // 采购管理 - 采购人员和项目管理员可见
      {
        key: 'purchase',
        icon: <ShoppingOutlined />,
        label: '采购管理',
        children: [
          {
            key: '/project/purchase/dashboard',
            icon: <DashboardOutlined />,
            label: <Link to="/project/purchase/dashboard">采购仪表盘</Link>,
          },
          {
            key: '/project/purchase/orders',
            icon: <FormOutlined />,
            label: <Link to="/project/purchase/orders">采购分拨</Link>,
          },
          {
            key: '/project/purchase/products',
            icon: <ShoppingOutlined />,
            label: <Link to="/project/purchase/products">商品管理</Link>,
          },
          {
            key: '/project/purchase/suppliers',
            icon: <BankOutlined />,
            label: <Link to="/project/purchase/suppliers">供应商管理</Link>,
          },
          {
            key: '/project/purchase/loss-approval',
            icon: <SolutionOutlined />,
            label: <Link to="/project/purchase/loss-approval">损耗审批</Link>,
          },
        ],
        role: ['project_admin', 'purchase_admin', 'purchase_manager', 'purchase_staff', 'tenant_admin']
      },
      // 财务管理 - 财务人员和项目管理员可见
      {
        key: 'finance',
        icon: <BankOutlined />,
        label: '财务管理',
        children: [
          {
            key: '/project/finance/dashboard',
            icon: <DashboardOutlined />,
            label: <Link to="/project/finance/dashboard">财务仪表盘</Link>,
          },
          {
            key: '/project/finance/data-analysis',
            icon: <BarChartOutlined />,
            label: <Link to="/project/finance/data-analysis">数据分析</Link>,
          },
          {
            key: '/project/finance/business-center',
            icon: <AppstoreOutlined />,
            label: <Link to="/project/finance/business-center">业务中心</Link>,
          },
          {
            key: '/project/finance/reconciliation',
            icon: <AccountBookOutlined />,
            label: <Link to="/project/finance/reconciliation">财务对账</Link>,
          },
        ],
        role: ['project_admin', 'finance_admin', 'finance_manager', 'finance_staff', 'tenant_admin']
      },
      // 仓储管理 - 仓库人员和项目管理员可见
      {
        key: 'inventory',
        icon: <HddOutlined />,
        label: '仓储管理',
        children: [
          {
            key: '/project/inventory/dashboard',
            icon: <DashboardOutlined />,
            label: <Link to="/project/inventory/dashboard">仓储仪表盘</Link>,
          },
          {
            key: '/project/inventory/management',
            icon: <DatabaseOutlined />,
            label: <Link to="/project/inventory/management">库存管理</Link>,
          },
          {
            key: '/project/inventory/alert',
            icon: <WarningOutlined />,
            label: <Link to="/project/inventory/alert">库存预警</Link>,
          },
          {
            key: '/project/inventory/warehouse',
            icon: <BankOutlined />,
            label: <Link to="/project/inventory/warehouse">仓库管理</Link>,
          },
          {
            key: '/project/inventory/transfer',
            icon: <SwapOutlined />,
            label: <Link to="/project/inventory/transfer">库存调拨</Link>,
          },
          {
            key: '/project/inventory/check',
            icon: <CheckCircleOutlined />,
            label: <Link to="/project/inventory/check">库存盘点</Link>,
          },
        ],
        role: ['project_admin', 'inventory_admin', 'inventory_manager', 'inventory_staff', 'tenant_admin']
      },
      // 插件管理
      {
        key: 'plugins',
        icon: <AppstoreOutlined />,
        label: '插件管理',
        children: [
          {
            key: '/project/plugin/market',
            icon: <AppstoreAddOutlined />,
            label: <Link to="/project/plugin/market">插件市场</Link>,
          },
          {
            key: '/project/plugin/dingtalk',
            icon: <DingdingOutlined />,
            label: <Link to="/project/plugin/dingtalk">钉钉深度集成</Link>,
          },
          {
            key: '/project/plugin/marketing_game',
            icon: <GiftOutlined />,
            label: <Link to="/project/plugin/marketing_game">营销游戏</Link>,
          },
          {
            key: '/project/plugin/sapi',
            icon: <ApiOutlined />,
            label: <Link to="/project/plugin/sapi">智能助手集成平台</Link>,
          },
        ],
        role: ['project_admin', 'tenant_admin']
      },
      // 系统设置 - 项目管理员可见
      {
        key: 'settings',
        icon: <SettingOutlined />,
        label: '系统设置',
        children: [
          {
            key: '/project/settings/users',
            icon: <TeamOutlined />,
            label: <Link to="/project/settings/users">用户管理</Link>,
          },
          {
            key: '/project/settings/roles',
            icon: <UserOutlined />,
            label: <Link to="/project/settings/roles">角色管理</Link>,
          },
          {
            key: '/project/settings/system',
            icon: <SettingOutlined />,
            label: <Link to="/project/settings/system">系统设置</Link>,
          },
          {
            key: '/project/settings/basic',
            icon: <ToolOutlined />,
            label: <Link to="/project/settings/basic">基础设置</Link>,
          },
          {
            key: '/project/settings/business',
            icon: <ApartmentOutlined />,
            label: <Link to="/project/settings/business">业务设置</Link>,
          },
          {
            key: '/project/settings/operation-logs',
            icon: <HistoryOutlined />,
            label: <Link to="/project/settings/operation-logs">操作日志</Link>,
          },
          {
            key: '/project/ai/assistants',
            icon: <RobotOutlined />,
            label: <Link to="/project/ai/assistants">AI 助手</Link>,
          },
        ],
        role: ['project_admin', 'tenant_admin']
      },
    ];

    // 过滤出用户有权限访问的菜单项
    const filteredMenuItems = menuItems.filter(item => {
      if (!item.role) return true;
      return hasPermission(item.role);
    });

    return filteredMenuItems;
  }

  // 餐饮行业特定菜单
  if (industryType === 'restaurant') {
    // 这里可以添加餐饮行业特定的菜单项
    return baseMenuItems;
  }

  // 默认返回基础菜单
  return baseMenuItems;
};
