import api from '../httpClient';

/**
 * 行业API
 */
const industry = {
  /**
   * 获取行业列表
   * @param {Object} params - 查询参数
   * @returns {Promise} - 行业列表
   */
  getList: async (params = {}) => {
    return api.get('/industry/industries', { params });
  },

  /**
   * 获取行业信息
   * @param {string} id - 行业ID
   * @returns {Promise} - 行业信息
   */
  getInfo: async (id) => {
    if (!id) {
      throw new Error('未指定行业ID');
    }
    return api.get(`/industry/industries/${id}`);
  },

  /**
   * 根据行业代码获取行业信息
   * @param {string} code - 行业代码
   * @returns {Promise} - 行业信息
   */
  getInfoByCode: async (code) => {
    if (!code) {
      throw new Error('未指定行业代码');
    }
    return api.get(`/industry/industries/code/${code}`);
  },

  /**
   * 获取行业配置
   * @param {string} industryType - 行业类型
   * @returns {Promise} - 行业配置
   */
  getConfig: async (industryType) => {
    if (!industryType) {
      throw new Error('未指定行业类型');
    }
    return api.get(`/industry/industries/config/${industryType}`);
  },

  /**
   * 创建行业
   * @param {Object} data - 行业数据
   * @returns {Promise} - 创建结果
   */
  create: async (data) => {
    return api.post('/industry/industries', data);
  },

  /**
   * 更新行业
   * @param {string} id - 行业ID
   * @param {Object} data - 行业数据
   * @returns {Promise} - 更新结果
   */
  update: async (id, data) => {
    if (!id) {
      throw new Error('未指定行业ID');
    }
    return api.put(`/industry/industries/${id}`, data);
  },

  /**
   * 删除行业
   * @param {string} id - 行业ID
   * @returns {Promise} - 删除结果
   */
  delete: async (id) => {
    if (!id) {
      throw new Error('未指定行业ID');
    }
    return api.delete(`/industry/industries/${id}`);
  }
};

export default industry;
