#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
工具函数

提供MCP服务器使用的通用工具函数
"""

import os
import asyncio
import time
import logging
import json
import httpx
from typing import Dict, Any, Optional, TYPE_CHECKING

logger = logging.getLogger(__name__)

# 添加重试配置
REDIS_CONNECTION_RETRY_COUNT = 5
REDIS_CONNECTION_RETRY_DELAY = 2  # 重试延迟，秒
REDIS_OPERATION_TIMEOUT = 5  # Redis 操作超时时间，秒

# Global redis client variable, to be initialized by get_redis_client
if TYPE_CHECKING:
    import redis.asyncio as redis_module_for_typing # Use a distinct name for clarity during type checking

_redis_client: Optional['redis_module_for_typing.Redis'] = None

try:
    import redis.asyncio as redis_module
    # _redis_client is initialized as None and then set by get_redis_client
except ImportError:
    redis_module = None # type: ignore
    # _redis_client is already None if import fails
    logger.warning("redis library not found. MCP server registration and heartbeat will not function.")

# 解析所有以 MCP_ 和 PROJECT_ 开头的环境变量，供主机名注册等用
parsed_env_sdk = {k: v for k, v in os.environ.items() if k.startswith("MCP_") or k.startswith("PROJECT_")}
if "MCP_SERVICE_NAME" in os.environ:
    parsed_env_sdk["MCP_SERVICE_NAME"] = os.environ["MCP_SERVICE_NAME"]
if "MCP_SERVER_HOST" in os.environ:
    parsed_env_sdk["MCP_SERVER_HOST"] = os.environ["MCP_SERVER_HOST"]

async def get_redis_client(retry_count=REDIS_CONNECTION_RETRY_COUNT):
    """Initializes and returns an ASYNCHRONOUS Redis client with retries."""
    global _redis_client
    # If client is already initialized and connected, return it
    if _redis_client:
        try:
            await asyncio.wait_for(_redis_client.ping(), timeout=REDIS_OPERATION_TIMEOUT / 2) # Quick ping
            logger.debug("Returning existing connected Redis client.")
            return _redis_client
        except Exception as e:
            logger.warning(f"Existing Redis client failed ping, attempting to reconnect: {e}")
            _redis_client = None # Reset to force reconnection

    # 动态确定Redis URL
    # 优先使用环境变量，然后根据Docker环境检测服务名
    redis_url = os.environ.get("REDIS_URL")
    if not redis_url:
        redis_host = os.environ.get("REDIS_HOST", "redis")  # 默认使用Docker服务名
        redis_port = os.environ.get("REDIS_PORT", "6379")
        redis_db = os.environ.get("REDIS_DB", "0")
        redis_url = f"redis://{redis_host}:{redis_port}/{redis_db}"
        
    logger.info(f"Attempting to connect to Redis at: {redis_url}")

    if redis_module is None:
        logger.error("Cannot create Redis client because the redis library is not imported.")
        print("CRITICAL_LOG: Cannot create Redis client because the redis library is not imported.")
        raise ImportError("Please install the redis library: pip install redis")

    # 遍历尝试连接
    for attempt in range(1, retry_count + 1):
        try:
            client = redis_module.Redis.from_url(redis_url, decode_responses=True)
            # Test connection asynchronously with timeout
            await asyncio.wait_for(client.ping(), timeout=REDIS_OPERATION_TIMEOUT)
            logger.info(f"Successfully connected to Redis on attempt {attempt}.")
            _redis_client = client # Store the successfully connected client globally
            return _redis_client
        except asyncio.TimeoutError:
            logger.error(f"Redis ping timed out on attempt {attempt}/{retry_count}")
            print(f"CRITICAL_LOG: Redis ping timed out on attempt {attempt}/{retry_count}")
            if attempt == retry_count:
                raise
        except redis_module.ConnectionError as e:
            logger.error(f"Failed to connect to Redis at {redis_url} on attempt {attempt}/{retry_count}: {e}")
            print(f"CRITICAL_LOG: Failed to connect to Redis at {redis_url} on attempt {attempt}/{retry_count}: {e}")
            if attempt == retry_count:
                raise
        except Exception as e:
            logger.error(f"Unexpected error in get_redis_client on attempt {attempt}/{retry_count}: {e}", exc_info=True)
            print(f"CRITICAL_LOG: Unexpected error in get_redis_client on attempt {attempt}/{retry_count}: {e}")
            if attempt == retry_count:
                raise
            
        # 如果不是最后一次尝试，等待一段时间后再次尝试
        if attempt < retry_count:
            wait_time = REDIS_CONNECTION_RETRY_DELAY * attempt  # 随着尝试次数增加等待时间
            logger.info(f"Retrying Redis connection in {wait_time} seconds...")
            print(f"CRITICAL_LOG: Retrying Redis connection in {wait_time} seconds...")
            await asyncio.sleep(wait_time)

    # 如果所有尝试都失败，抛出异常
    raise Exception(f"Failed to connect to Redis after {retry_count} attempts")

async def register_server(server_id: str, host: Optional[str] = None, port: int = 0,
                port_http: Optional[int] = None, port_streamhttp: Optional[int] = None,
                port_stdio: Optional[int] = None, metadata: Optional[Dict[str, Any]] = None) -> bool:
    logger.info(f"Attempting to register server ID: {server_id}, Host: {host}, Port: {port}, StreamHTTP Port: {port_streamhttp}")
    """
    将MCP服务器注册到Redis

    双重注册机制:
    1. 使用格式: mcp:server:<server_id> = {host, port, port_http, port_streamhttp, port_sse, port_stdio, status, last_heartbeat} (哈希表格式)
    2. 使用格式: v1:servers:<server_id> = JSON字符串 (字符串格式，供前端API使用)

    Args:
        server_id: 服务器ID
        host: 主机名或IP地址，如果为None则使用环境变量MCP_SERVER_HOST或默认localhost
        port: 通用端口号（向后兼容）
        port_http: HTTP协议端口号
        port_streamhttp: StreamHTTP协议端口号
        port_sse: SSE协议端口号
        port_stdio: STDIO协议标记（1表示支持）
        metadata: 包含其他元数据的字典 (例如 streamhttp_endpoint, stream_capable)

    Returns:
        bool: 是否注册成功
    """
    # 重试计数器
    retry_count = 0
    max_retries = 3
    
    # 如果未提供host，从环境变量获取
    if not host:
        host = os.environ.get("MCP_SERVER_HOST", os.environ.get("MCP_SERVICE_NAME", "localhost"))
        logger.info(f"Resolved host for registration: {host}")
    
    # 特别处理端口信息
    if not port and "PORT" in os.environ:
        try:
            port = int(os.environ["PORT"])
            logger.info(f"Using PORT from environment: {port}")
            print(f"CRITICAL_LOG: Using PORT from environment: {port}")
        except (ValueError, TypeError):
            logger.warning(f"Invalid PORT in environment: {os.environ.get('PORT')}")
    
    # 如果没有指定任何端口，记录警告
    if not port and not port_streamhttp and not port_http and not port_stdio:
        logger.warning(f"No primary port specified for registration of server {server_id}. Server may not be reachable.")

    while retry_count < max_retries:
        try:
            # 确保有Redis客户端
            current_redis_client = await get_redis_client()
            if not current_redis_client:
                logger.error("Failed to get Redis client for registration.")
                raise Exception("Redis client unavailable")

            # 服务器信息
            server_info_to_register: Dict[str, Any] = {
                "host": host,
                "port": port, # General port, could be the same as port_streamhttp
                "status": "running",
                "last_heartbeat": int(time.time())
            }

            # 添加特定协议端口信息
            if port_http is not None:
                server_info_to_register["port_http"] = port_http
            if port_streamhttp is not None:
                server_info_to_register["port_streamhttp"] = port_streamhttp
            elif port:  # Fallback if only general port is given
                server_info_to_register["port_streamhttp"] = port
            if port_stdio is not None:
                server_info_to_register["port_stdio"] = port_stdio
            
            # Merge additional metadata if provided
            if metadata:
                server_info_to_register.update(metadata) # Directly update, or merge under a 'metadata' key if preferred
                # If merging under a 'metadata' sub-key:
                # server_info_to_register.setdefault("metadata", {}).update(metadata)
                # For simplicity, direct update is used here. Adjust if Redis schema expects nested metadata.

            # Filter out None values from server_info before registration
            # server_info_final = {k: v for k, v in server_info_to_register.items() if v is not None}
            # Convert all values to string for redis hset, as it expects Dict[str, str] essentially
            # Though redis.asyncio might handle int/bool conversion, explicit is safer for wider compatibility.
            server_info_final_str_values: Dict[str, str] = {}
            for k, v in server_info_to_register.items():
                if v is not None:
                    server_info_final_str_values[k] = str(v)

            # 使用超时保护 Redis 操作
            try:
                # 1. 使用mcp:server:<server_id>格式（原始格式，哈希表）
                await asyncio.wait_for(
                    current_redis_client.hset(f"mcp:server:{server_id}", mapping=server_info_final_str_values),
                    timeout=REDIS_OPERATION_TIMEOUT
                )

                # 2. 使用v1:servers:<server_id>格式（前端API查询格式）
                await asyncio.wait_for(
                    current_redis_client.hset(f"v1:servers:{server_id}", mapping=server_info_final_str_values),
                    timeout=REDIS_OPERATION_TIMEOUT
                )
                
                # 确保键设置了过期时间 (1小时)，这样如果服务器崩溃，过期的键会自动清理
                await asyncio.wait_for(
                    current_redis_client.expire(f"mcp:server:{server_id}", 3600),
                    timeout=REDIS_OPERATION_TIMEOUT
                )
                await asyncio.wait_for(
                    current_redis_client.expire(f"v1:servers:{server_id}", 3600),
                    timeout=REDIS_OPERATION_TIMEOUT
                )
                
                # 额外的 Redis 健康检查验证注册的数据
                check_data = await asyncio.wait_for(
                    current_redis_client.hgetall(f"mcp:server:{server_id}"),
                    timeout=REDIS_OPERATION_TIMEOUT
                )
                
                if not check_data:
                    logger.warning(f"Registration verification failed for {server_id} - no data found after HSET.")
                    raise Exception("Redis registration could not be verified (key not found or empty after set)")
                    
                logger.info(f"Registration verified for {server_id} - Redis data: {check_data}")
                
            except asyncio.TimeoutError:
                logger.error(f"Redis HSET/EXPIRE operation timed out during registration for {server_id}")
                raise
                
            logger.info(f"Server {server_id} registered successfully to Redis with info: {server_info_final_str_values}")
            return True
            
        except Exception as e:
            retry_count += 1
            logger.error(f"Server registration for {server_id} failed (attempt {retry_count}/{max_retries}): {str(e)}", exc_info=True)
            
            if retry_count < max_retries:
                wait_time = REDIS_CONNECTION_RETRY_DELAY * retry_count
                logger.info(f"Retrying registration in {wait_time} seconds...")
                await asyncio.sleep(wait_time)
            else:
                logger.critical(f"Server {server_id} registration finally FAILED after {max_retries} attempts.")
                return False
    return False # Should be unreachable if loop completes, but as a safeguard

async def heartbeat(server_id: str, host: Optional[str] = None, port: int = 0,
                port_http: Optional[int] = None, port_streamhttp: Optional[int] = None,
                port_stdio: Optional[int] = None, metadata: Optional[Dict[str, Any]] = None,
                interval_seconds: int = 30):
    """
    定期向Redis发送心跳，更新服务器状态

    同时更新两种键格式和存储方式

    Args:
        server_id: 服务器ID
        host: 主机名或IP地址
        port: 通用端口号（向后兼容）
        port_http: HTTP协议端口号
        port_streamhttp: StreamHTTP协议端口号
        port_sse: SSE协议端口号
        port_stdio: STDIO协议标记（1表示支持）
        metadata: 包含其他元数据的字典 (例如 streamhttp_endpoint, stream_capable)
        interval_seconds: 心跳间隔秒数
    """
    # 如果未提供host，从环境变量获取
    if not host:
        host = os.environ.get("MCP_SERVER_HOST", os.environ.get("MCP_SERVICE_NAME", "localhost"))
        logger.info(f"Heartbeat for {server_id} using resolved host: {host}")

    # 确保端口信息
    if not port and "PORT" in os.environ:
        try:
            port = int(os.environ["PORT"])
        except (ValueError, TypeError):
            logger.warning(f"Invalid PORT in environment for heartbeat: {os.environ.get('PORT')}")

    # 确保有Redis客户端
    # global redis # No longer need global redis, use _redis_client via get_redis_client
    # if redis is None: # Use get_redis_client for robust connection handling
    #     try:
    #         redis = await get_redis_client()
    #         logger.info(f"Redis client initialized for heartbeat of {server_id}")
    #     except Exception as e:
    #         logger.error(f"Failed to initialize Redis client for heartbeat of {server_id}: {e}")
            # Continue, will attempt connection in loop

    # 最大故障容忍次数，超过这个数字会增加心跳间隔
    max_consecutive_failures = 3
    consecutive_failures = 0
    initial_heartbeat_interval = interval_seconds 
    
    try:
        while True:
            try:
                # 检查Redis连接是否有效
                current_redis_client = await get_redis_client() # Get or ensure client is valid
                if not current_redis_client:
                    logger.error(f"Heartbeat for {server_id}: Failed to get Redis client. Skipping this beat.")
                    await asyncio.sleep(initial_heartbeat_interval) # Wait before retrying get_redis_client
                    continue
                    
                # 更新心跳时间
                server_info_to_update: Dict[str, Any] = {
                    "host": host,
                    "port": port,
                    "status": "running",
                    "last_heartbeat": int(time.time())
                }

                # 添加特定协议端口信息
                if port_http is not None:
                    server_info_to_update["port_http"] = port_http
                if port_streamhttp is not None:
                    server_info_to_update["port_streamhttp"] = port_streamhttp
                elif port:
                    server_info_to_update["port_streamhttp"] = port
                # if port_sse is not None: # Removed SSE specific port
                #     server_info_to_update["port_sse"] = port_sse
                if port_stdio is not None:
                    server_info_to_update["port_stdio"] = port_stdio

                # Merge additional metadata if provided
                if metadata:
                    server_info_to_update.update(metadata)
                    # If merging under a 'metadata' sub-key:
                    # server_info_to_update.setdefault("metadata", {}).update(metadata)

                # server_info_final = {k: v for k, v in server_info_to_update.items() if v is not None}
                server_info_final_str_values: Dict[str, str] = {}
                for k, v in server_info_to_update.items():
                    if v is not None:
                        server_info_final_str_values[k] = str(v)

                # 使用超时保护 Redis 操作
                try:
                    # 1. 更新mcp:server:<server_id>格式（哈希表）
                    await asyncio.wait_for(
                        current_redis_client.hset(f"mcp:server:{server_id}", mapping=server_info_final_str_values),
                        timeout=REDIS_OPERATION_TIMEOUT
                    )

                    # 2. 更新v1:servers:<server_id>格式
                    await asyncio.wait_for(
                        current_redis_client.hset(f"v1:servers:{server_id}", mapping=server_info_final_str_values),
                        timeout=REDIS_OPERATION_TIMEOUT
                    )
                    
                    # 续期键的过期时间
                    await asyncio.wait_for(
                        current_redis_client.expire(f"mcp:server:{server_id}", 3600),
                        timeout=REDIS_OPERATION_TIMEOUT
                    )
                    await asyncio.wait_for(
                        current_redis_client.expire(f"v1:servers:{server_id}", 3600),
                        timeout=REDIS_OPERATION_TIMEOUT
                    )
                
                except asyncio.TimeoutError:
                    logger.error(f"Redis HSET/EXPIRE operation timed out during heartbeat for {server_id}")
                    raise # Raise to trigger failure count and retry logic
                
                if consecutive_failures > 0:
                    consecutive_failures = 0
                    initial_heartbeat_interval = initial_heartbeat_interval
                    logger.info(f"Heartbeat for {server_id} recovered. Reset interval to {initial_heartbeat_interval}s")
                
                logger.debug(f"Server heartbeat updated for {server_id} with info: {server_info_final_str_values}")
                
            except Exception as e: # Catches Redis connection errors from get_redis_client or op errors
                consecutive_failures += 1
                logger.warning(f"Failed to update server heartbeat for {server_id} (failure #{consecutive_failures}): {str(e)}")
                
                if consecutive_failures >= max_consecutive_failures:
                    new_interval = min(initial_heartbeat_interval * (2 ** (consecutive_failures - max_consecutive_failures + 1)), 300) # Exponential backoff up to 5 mins
                    if new_interval > initial_heartbeat_interval:
                        initial_heartbeat_interval = new_interval
                        logger.warning(f"Heartbeat for {server_id}: Consecutive failures reached {consecutive_failures}. Increased interval to {initial_heartbeat_interval}s")
            
            await asyncio.sleep(initial_heartbeat_interval)
    except asyncio.CancelledError:
        logger.info(f"Heartbeat task for {server_id} cancelled.")
        raise
    except Exception as e:
        logger.error(f"心跳任务异常: {str(e)}")
        # 尝试重启心跳任务
        logger.info(f"尝试重启心跳任务...")
        asyncio.create_task(heartbeat(server_id, host, port, port_http, port_streamhttp, port_stdio, metadata, interval_seconds))

# 从插件令牌获取项目JWT令牌
async def get_system_jwt_token(sapi_token: str, token_data: Optional[Dict[str, Any]] = None, base_url: Optional[str] = None) -> Dict[str, Any]:
    """
    通过SAPI插件的JWT令牌获取主系统的JWT令牌

    Args:
        sapi_token: SAPI插件的JWT令牌
        token_data: 已验证的令牌数据，包含用户ID等信息
        base_url: API基础URL，默认从环境变量获取

    Returns:
        Dict[str, Any]: 包含系统令牌和状态的字典
        {
            "success": bool,  # 是否成功获取
            "token": str,     # 系统JWT令牌，获取失败时为None
            "error": str      # 错误信息，成功时为None
        }
    """
    try:
        # 验证输入参数
        if not sapi_token:
            return {
                "success": False,
                "token": None,
                "error": "未提供SAPI令牌"
            }

        # 确保有令牌数据，尤其是用户ID
        if not token_data or not token_data.get("user_id"):
            return {
                "success": False,
                "token": None,
                "error": "缺少令牌数据或用户ID"
            }

        if not base_url:
            base_url = os.environ.get("API_BASE_URL", "http://localhost:3001")

        # 构建请求
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {sapi_token}" # Assuming SAPI token is passed in Authorization for this internal call too
        }

        # 准备请求体，包含用户ID
        request_body = {
            "plugin_token": sapi_token, # Keep this if the endpoint expects it
            "user_id": token_data.get("user_id"),
            "tenant_id": token_data.get("tenant_id"),
            "project_id": token_data.get("project_id")
        }

        # 调用系统令牌转换接口
        token_url = f"{base_url}/api/v1/auth/convert-token"
        logger.info(f"Attempting to get system JWT token from {token_url} for user {token_data.get('user_id')}")

        async with httpx.AsyncClient(timeout=10.0) as client: # Added timeout
            response = await client.post(
                token_url,
                json=request_body,
                headers=headers
            )

        if response.status_code != 200:
            logger.error(f"Failed to get system token: {response.status_code} - {response.text}")
            return {
                "success": False,
                "token": None,
                "error": f"API error: {response.status_code} - {response.text}" # Include response text for better debugging
            }

        result = response.json()
        if not result.get("success", False):
            logger.error(f"API call successful, but failed to get system token: {result.get('message', 'Unknown error from API')}")
            return {
                "success": False,
                "token": None,
                "error": result.get("message", "Failed to get system token from API")
            }

        system_token = result.get("token")
        if not system_token:
            logger.error("API response successful but system token is missing in the response.")
            return {
                "success": False,
                "token": None,
                "error": "System token missing in API response"
            }

        logger.info(f"Successfully obtained system JWT token for user {token_data.get('user_id')}")
        return {
            "success": True,
            "token": system_token,
            "error": None
        }
    except httpx.RequestError as e:
        logger.error(f"HTTP request error while getting system token: {e}", exc_info=True)
        return {"success": False, "token": None, "error": f"HTTP request error: {str(e)}"}
    except json.JSONDecodeError as e:
        logger.error(f"JSON decode error from convert-token endpoint: {e}. Response: {response.text if 'response' in locals() else 'N/A'}", exc_info=True)
        return {"success": False, "token": None, "error": "Invalid JSON response from API"}
    except Exception as e:
        logger.error(f"Unexpected error while getting system token: {e}", exc_info=True)
        return {
            "success": False,
            "token": None,
            "error": f"Unexpected error: {str(e)}"
        }

# 令牌缓存，用于存储SAPI令牌到系统令牌的映射
# 格式: {cache_key: {"system_token": str, "expires_at": float}}
token_cache: Dict[str, Dict[str, Any]] = {}
# Cache TTL in seconds (e.g., 50 minutes, slightly less than typical 1-hour token expiry)
TOKEN_CACHE_TTL = 50 * 60

async def get_system_jwt_token_with_cache(sapi_token: str, token_data: Optional[Dict[str, Any]] = None, base_url: Optional[str] = None) -> Dict[str, Any]:
    """
    通过SAPI插件的JWT令牌获取主系统的JWT令牌，带缓存功能
    """
    if not sapi_token or not token_data or not token_data.get("user_id"):
        return {
            "success": False,
            "token": None,
            "error": "Missing SAPI token or user_id for system token retrieval."
        }

    # Using a more robust cache key
    cache_key = f"sapi_to_system_token:{token_data['user_id']}:{sapi_token[:16]}" # Include part of SAPI token for uniqueness

    current_time = time.time()
    cached_entry = token_cache.get(cache_key)

    if cached_entry and current_time < cached_entry["expires_at"]:
        logger.info(f"Returning system token from cache for user {token_data['user_id']}.")
        return {
            "success": True,
            "token": cached_entry["system_token"],
            "error": None,
            "from_cache": True
        }

    if cached_entry: # Expired entry
        logger.info(f"System token cache expired for user {token_data['user_id']}. Refreshing.")
        del token_cache[cache_key]

    logger.info(f"System token not in cache or expired for user {token_data['user_id']}. Fetching new token.")
    result = await get_system_jwt_token(sapi_token, token_data, base_url)

    if result["success"] and result["token"]:
        token_cache[cache_key] = {
            "system_token": result["token"],
            "expires_at": current_time + TOKEN_CACHE_TTL
        }
        logger.info(f"System token for user {token_data['user_id']} cached successfully.")
    else:
        logger.warning(f"Failed to fetch new system token for user {token_data['user_id']}. Not caching. Error: {result.get('error')}")

    result["from_cache"] = False # Explicitly state it's not from cache if fetched
    return result

# Example of how these might be called from a main.py in the MCP server
if __name__ == '__main__':
    # This is for demonstration if utils.py were run directly.
    # In a real MCP server, main.py would import and use these functions.

    # Configure logging for direct execution
    logging.basicConfig(stream=sys.stdout, level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    logger.info("utils.py executed directly (for testing purposes).")

    # Mock environment variables (these should be set in the actual MCP server environment)
    os.environ["MCP_SERVER_ID"] = "test-server-001"
    os.environ["MCP_SERVER_HOST"] = "mcp-test-server-001" # Or "localhost" for local test
    os.environ["PORT"] = "8080"
    os.environ["REDIS_URL"] = "redis://localhost:6379/0" # Ensure Redis is running for this test

    async def test_registration_and_heartbeat():
        logger.info("--- Testing Server Registration ---")
        if _redis_client is None:
            logger.error("Redis library not imported, skipping registration/heartbeat test.")
            return

        # Initial registration
        # In a real app, these would be read from env by register_server itself
        # or passed if a specific instance needs to override env vars.
        server_id = os.environ["MCP_SERVER_ID"]
        host = os.environ["MCP_SERVER_HOST"]
        port_str = os.environ["PORT"]
        port = int(port_str) if port_str else 0

        initial_reg_success = await register_server(server_id, host, port)
        if initial_reg_success:
            logger.info(f"Test: Initial ASYNC registration for {server_id} successful.")

            # Start heartbeat task
            logger.info("Test: Starting heartbeat task (will run for a short period)...")
            # Run heartbeat for a few cycles then stop it for the test
            heartbeat_task_handle = asyncio.create_task(heartbeat(server_id, host, port, interval_seconds=10))

            await asyncio.sleep(35) # Let it run for ~3 heartbeats
            heartbeat_task_handle.cancel()
            try:
                await heartbeat_task_handle
            except asyncio.CancelledError:
                logger.info("Test: Heartbeat task cancelled as expected.")
            logger.info("Test: Heartbeat test finished.")
        else:
            logger.error("Test: Initial registration failed. Aborting heartbeat test.")

    async def test_get_system_token():
        logger.info("--- Testing System Token Retrieval (mocked) ---")
        # This part would require a running API endpoint for /api/v1/auth/convert-token
        # For a unit test, you'd mock httpx.AsyncClient
        # For now, this will likely fail unless a compatible API is running.

        # Mock SAPI token and data
        mock_sapi_token = "dummy-sapi-token-for-testing"
        mock_token_data = {
            "user_id": "test-user-123",
            "project_id": "test-project-456",
            "tenant_id": "test-tenant-789"
        }
        os.environ["API_BASE_URL"] = "http://localhost:3001" # Ensure your API mock or real service is here

        logger.info("Attempting to get system token (round 1 - no cache)...")
        result1 = await get_system_jwt_token_with_cache(mock_sapi_token, mock_token_data)
        if result1["success"]:
            logger.info(f"Token 1 (No Cache): {result1['token'][:20]}...")
        else:
            logger.error(f"Token 1 (No Cache) Failed: {result1['error']}")

        logger.info("Attempting to get system token (round 2 - should be cached)...")
        result2 = await get_system_jwt_token_with_cache(mock_sapi_token, mock_token_data)
        if result2["success"] and result2.get("from_cache"):
            logger.info(f"Token 2 (From Cache): {result2['token'][:20]}... Success!")
        elif result2["success"]:
            logger.warning(f"Token 2 (Not From Cache, but expected): {result2['token'][:20]}...")
        else:
            logger.error(f"Token 2 (From Cache) Failed: {result2['error']}")


    async def main_test_suite():
        await test_registration_and_heartbeat()
        # await test_get_system_token() # Uncomment to test token retrieval

    if os.environ.get("RUN_UTILS_TESTS") == "true": # Run tests if this env var is set
        asyncio.run(main_test_suite())
    else:
        logger.info("Skipping direct execution tests for utils.py. Set RUN_UTILS_TESTS=true to run them.")

# Example of how to integrate into your MCP server's main.py:
#
# At the start of your MCP server (e.g., in an on_startup event or main function):
#
# import os
# import asyncio
# from utils import register_server, heartbeat # Assuming utils.py is in PYTHONPATH
#
# async def startup_event():
#     logger.info("MCP Server starting up...")
#     # These should be read from the environment where the MCP server is running
#     server_id = os.environ.get("MCP_SERVER_ID")
#     host = os.environ.get("MCP_SERVER_HOST") # Or construct as needed, e.g. f"mcp-server-{server_id}"
#     port_str = os.environ.get("PORT")
#
#     if not server_id or not host or not port_str:
#         logger.error("MCP_SERVER_ID, MCP_SERVER_HOST, or PORT not set in environment. Cannot register.")
#         return
#
#     try:
#         port = int(port_str)
#     except ValueError:
#         logger.error(f"Invalid PORT: {port_str}. Cannot register.")
#         return
#
#     # Perform initial registration
#     if await register_server(server_id, host, port):
#         logger.info(f"Server {server_id} registered successfully.")
#         # Start the heartbeat task
#         asyncio.create_task(heartbeat(server_id, host, port))
#         logger.info(f"Heartbeat task for {server_id} started.")
#     else:
#         logger.error(f"Initial server registration for {server_id} failed.")
#
# if __name__ == "__main__": # In your actual MCP server's main.py
#     # ... your FastAPI app setup ...
#     # app.add_event_handler("startup", startup_event)
#     # uvicorn.run(app, host="0.0.0.0", port=int(os.environ.get("PORT", 8000)))
#     #
#     # Or, if not using FastAPI events, call directly before starting uvicorn:
#     # loop = asyncio.get_event_loop()
#     # loop.run_until_complete(startup_event()) # Run registration before server starts fully listening
#     # Then start uvicorn
#     pass