from sqlalchemy import Column, String, <PERSON>olean, DateTime, ForeignKey, JSON, Text, Integer, UniqueConstraint, Float
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship, backref
from sqlalchemy.sql import func
import uuid

from db.database import Base

class Store(Base):
    """门店模型"""
    __tablename__ = "stores"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id"), nullable=False)
    category_id = Column(UUID(as_uuid=True), ForeignKey("store_categories.id"), nullable=True)
    region_id = Column(UUID(as_uuid=True), ForeignKey("store_regions.id"), nullable=True)
    name = Column(String(100), nullable=False)
    code = Column(String(50), nullable=False)
    address = Column(String(200), nullable=True)
    phone = Column(String(20), nullable=True)
    manager = Column(String(50), nullable=True)
    latitude = Column(Float, nullable=True)
    longitude = Column(Float, nullable=True)
    business_hours = Column(String(100), nullable=True)  # 营业时间，如：09:00-22:00
    status = Column(String(20), default="active", nullable=False)  # active, inactive, closed
    settings = Column(JSON, default=dict)  # 门店设置
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 关联关系
    project = relationship("Project", backref="stores")
    category = relationship("StoreCategory", back_populates="stores")
    region = relationship("StoreRegion", back_populates="stores")
    sales_reports = relationship("SalesReport", back_populates="store")
    financial_reconciliations = relationship("FinancialReconciliation", back_populates="store")
    routine_tasks = relationship("RoutineTask", back_populates="store", foreign_keys="[RoutineTask.store_id]")

    __table_args__ = (
        UniqueConstraint('project_id', 'code', name='uq_store_code_per_project'),
    )

    def __repr__(self):
        return f"<Store(id={self.id}, name='{self.name}', code='{self.code}')>"


class StoreCategory(Base):
    """门店分类模型"""
    __tablename__ = "store_categories"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id"), nullable=False)
    name = Column(String(50), nullable=False)
    code = Column(String(50), nullable=False)
    description = Column(Text, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 关联关系
    project = relationship("Project", backref="store_categories")
    stores = relationship("Store", back_populates="category")

    __table_args__ = (
        UniqueConstraint('project_id', 'code', name='uq_store_category_code_per_project'),
    )

    def __repr__(self):
        return f"<StoreCategory(id={self.id}, name='{self.name}', code='{self.code}')>"


class StoreRegion(Base):
    """门店区域模型"""
    __tablename__ = "store_regions"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id"), nullable=False)
    parent_id = Column(UUID(as_uuid=True), ForeignKey("store_regions.id"), nullable=True)
    name = Column(String(50), nullable=False)
    code = Column(String(50), nullable=False)
    description = Column(Text, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 关联关系
    project = relationship("Project", backref="store_regions")
    stores = relationship("Store", back_populates="region")
    parent = relationship("StoreRegion", remote_side=[id], backref=backref("children", lazy="selectin"), lazy="selectin")

    __table_args__ = (
        UniqueConstraint('project_id', 'code', name='uq_store_region_code_per_project'),
    )

    def __repr__(self):
        return f"<StoreRegion(id={self.id}, name='{self.name}', code='{self.code}')>"

    @property
    def full_name(self):
        """获取完整区域名称（包含父级区域）"""
        if self.parent:
            return f"{self.parent.name}-{self.name}"
        return self.name


class ProjectUserStore(Base):
    __tablename__ = "project_user_stores"
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    project_user_id = Column(UUID(as_uuid=True), ForeignKey("project_users.id", ondelete="CASCADE"), nullable=False)
    store_id = Column(UUID(as_uuid=True), ForeignKey("stores.id", ondelete="CASCADE"), nullable=False)
    is_manager = Column(Boolean, default=False)

    project_user = relationship("ProjectUser", back_populates="project_user_stores")
    store = relationship("Store")
