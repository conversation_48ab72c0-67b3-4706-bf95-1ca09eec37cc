import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Modal,
  Form,
  Input,
  Select,
  Switch,
  Space,
  message,
  Tabs,
  Tag,
  Tooltip,
  Popconfirm,
  Row,
  Col,
  Statistic,
  Timeline,
  Alert,
  Divider,
  Radio
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  ReloadOutlined,
  SettingOutlined,
  BellOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  ClockCircleOutlined,
  RobotOutlined,
  ApiOutlined,
  LinkOutlined
} from '@ant-design/icons';
import dingtalkApi from '../service/dingtalkApi';

const { Option } = Select;
const { TextArea } = Input;
const { TabPane } = Tabs;

const WebhookManagement = () => {
  const [loading, setLoading] = useState(false);
  const [webhooks, setWebhooks] = useState([]);
  const [eventLogs, setEventLogs] = useState([]);
  const [statistics, setStatistics] = useState({});
  const [modalVisible, setModalVisible] = useState(false);
  const [editingWebhook, setEditingWebhook] = useState(null);
  const [logModalVisible, setLogModalVisible] = useState(false);
  const [selectedWebhook, setSelectedWebhook] = useState(null);
  const [webhookType, setWebhookType] = useState('event');
  const [subscriptionMode, setSubscriptionMode] = useState('http');
  const [form] = Form.useForm();

  // 支持的事件类型
  const eventTypes = [
    { value: 'bpms_task_change', label: '审批任务状态变更', description: '审批任务被创建、完成、拒绝时触发' },
    { value: 'bpms_instance_change', label: '审批实例状态变更', description: '审批实例状态发生变化时触发' },
    { value: 'todo_task_change', label: '待办任务变更', description: '待办任务被创建、完成、删除时触发' },
    { value: 'user_add_org', label: '用户加入企业', description: '新用户加入企业时触发' },
    { value: 'user_modify_org', label: '用户信息变更', description: '用户信息被修改时触发' },
    { value: 'user_leave_org', label: '用户离开企业', description: '用户离开企业时触发' },
    { value: 'org_dept_create', label: '部门创建', description: '新部门被创建时触发' },
    { value: 'org_dept_modify', label: '部门信息变更', description: '部门信息被修改时触发' },
    { value: 'org_dept_remove', label: '部门删除', description: '部门被删除时触发' },
    { value: 'chat_add_member', label: '群成员增加', description: '群聊新增成员时触发' },
    { value: 'chat_remove_member', label: '群成员减少', description: '群聊成员离开时触发' },
    { value: 'chat_disband', label: '群解散', description: '群聊被解散时触发' }
  ];

  useEffect(() => {
    loadWebhooks();
    loadEventLogs();
    loadStatistics();
  }, []);

  const loadWebhooks = async () => {
    try {
      setLoading(true);
      const response = await dingtalkApi.getWebhooks();
      if (response.success) {
        setWebhooks(response.data || []);
      }
    } catch (error) {
      console.error('加载Webhook失败:', error);
      message.error('加载Webhook失败');
    } finally {
      setLoading(false);
    }
  };

  const loadEventLogs = async () => {
    try {
      const response = await dingtalkApi.getEventLogs();
      if (response.success) {
        setEventLogs(response.data || []);
      }
    } catch (error) {
      console.error('加载事件日志失败:', error);
    }
  };

  const loadStatistics = async () => {
    try {
      const response = await dingtalkApi.getEventStatistics();
      if (response.success) {
        setStatistics(response.data || {});
      }
    } catch (error) {
      console.error('加载统计信息失败:', error);
    }
  };

  const handleCreateWebhook = () => {
    setEditingWebhook(null);
    setWebhookType('event');
    setSubscriptionMode('http');
    form.resetFields();
    
    // 自动填充回调URL - 修复路径问题
    const pathParts = window.location.pathname.split('/');
    const projectIndex = pathParts.indexOf('project');
    const projectId = projectIndex !== -1 && projectIndex + 1 < pathParts.length ? pathParts[projectIndex + 1] : '';
    
    if (projectId) {
      const defaultCallbackUrl = `/api/v1/project/${projectId}/plugin/dingtalk/webhook/events`;
      form.setFieldsValue({
        callback_url: defaultCallbackUrl,
        enabled: true
      });
    } else {
      // 如果无法获取项目ID，显示模板格式
      form.setFieldsValue({
        callback_url: '/api/v1/project/{projectId}/plugin/dingtalk/webhook/events',
        enabled: true
      });
    }
    
    setModalVisible(true);
  };

  const handleEditWebhook = (webhook) => {
    setEditingWebhook(webhook);
    setWebhookType(webhook.webhook_type || 'event');
    setSubscriptionMode(webhook.subscription_mode || 'http');
    form.setFieldsValue({
      ...webhook,
      event_types: webhook.event_types || []
    });
    setModalVisible(true);
  };

  const handleDeleteWebhook = async (id) => {
    try {
      const response = await dingtalkApi.deleteWebhook(id);
      if (response.success) {
        message.success('删除成功');
        loadWebhooks();
      } else {
        message.error(response.message || '删除失败');
      }
    } catch (error) {
      console.error('删除Webhook失败:', error);
      message.error('删除失败');
    }
  };

  const handleToggleWebhook = async (id, enabled) => {
    try {
      const response = await dingtalkApi.updateWebhook(id, { enabled });
      if (response.success) {
        message.success(enabled ? '启用成功' : '禁用成功');
        loadWebhooks();
      } else {
        message.error(response.message || '操作失败');
      }
    } catch (error) {
      console.error('切换Webhook状态失败:', error);
      message.error('操作失败');
    }
  };

  const handleSubmit = async (values) => {
    try {
      setLoading(true);
      const data = {
        ...values,
        webhook_type: webhookType,
        subscription_mode: subscriptionMode
      };

      let response;
      if (editingWebhook) {
        response = await dingtalkApi.updateWebhook(editingWebhook.id, data);
      } else {
        response = await dingtalkApi.createWebhook(data);
      }

      if (response.success) {
        message.success(editingWebhook ? '更新成功' : '创建成功');
        setModalVisible(false);
        loadWebhooks();
      } else {
        message.error(response.message || '操作失败');
      }
    } catch (error) {
      console.error('保存Webhook失败:', error);
      message.error('保存失败');
    } finally {
      setLoading(false);
    }
  };

  const handleViewLogs = (webhook) => {
    setSelectedWebhook(webhook);
    setLogModalVisible(true);
  };

  const webhookColumns = [
    {
      title: 'Webhook名称',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <Space>
          {record.webhook_type === 'robot' ? <RobotOutlined /> : <ApiOutlined />}
          <span>{text}</span>
          {record.enabled ? (
            <Tag color="green">已启用</Tag>
          ) : (
            <Tag color="red">已禁用</Tag>
          )}
        </Space>
      )
    },
    {
      title: '类型',
      dataIndex: 'webhook_type',
      key: 'webhook_type',
      render: (type) => (
        <Tag color={type === 'robot' ? 'blue' : 'purple'}>
          {type === 'robot' ? '机器人通知' : '事件订阅'}
        </Tag>
      )
    },
    {
      title: 'URL/事件',
      key: 'url_or_events',
      render: (_, record) => {
        if (record.webhook_type === 'robot') {
          return (
            <Tooltip title={record.webhook_url}>
              <span style={{ maxWidth: 200, overflow: 'hidden', textOverflow: 'ellipsis' }}>
                {record.webhook_url}
              </span>
            </Tooltip>
          );
        } else {
          return (
            <Space wrap>
              {(record.event_types || []).map(type => {
                const eventType = eventTypes.find(et => et.value === type);
                return (
                  <Tag key={type} size="small">
                    {eventType ? eventType.label : type}
                  </Tag>
                );
              })}
            </Space>
          );
        }
      }
    },
    {
      title: '状态',
      dataIndex: 'enabled',
      key: 'enabled',
      render: (enabled, record) => (
        <Switch
          checked={enabled}
          onChange={(checked) => handleToggleWebhook(record.id, checked)}
          checkedChildren="启用"
          unCheckedChildren="禁用"
        />
      )
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (time) => time ? new Date(time).toLocaleString() : '-'
    },
    {
      title: '最后触发',
      dataIndex: 'last_triggered_at',
      key: 'last_triggered_at',
      render: (time) => time ? new Date(time).toLocaleString() : '未触发'
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEditWebhook(record)}
          >
            编辑
          </Button>
          <Button
            type="link"
            icon={<EyeOutlined />}
            onClick={() => handleViewLogs(record)}
          >
            日志
          </Button>
          <Popconfirm
            title="确定要删除这个Webhook吗？"
            onConfirm={() => handleDeleteWebhook(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      )
    }
  ];

  const eventLogColumns = [
    {
      title: '事件类型',
      dataIndex: 'event_type',
      key: 'event_type',
      render: (type) => {
        const eventType = eventTypes.find(et => et.value === type);
        return eventType ? eventType.label : type;
      }
    },
    {
      title: '处理状态',
      dataIndex: 'processed',
      key: 'processed',
      render: (processed) => (
        <Tag color={processed ? 'green' : 'orange'}>
          {processed ? '已处理' : '待处理'}
        </Tag>
      )
    },
    {
      title: '时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (time) => time ? new Date(time).toLocaleString() : '-'
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record) => (
        <Button
          type="link"
          icon={<EyeOutlined />}
          onClick={() => {
            Modal.info({
              title: '事件详情',
              width: 800,
              content: (
                <div>
                  <p><strong>事件类型:</strong> {record.event_type}</p>
                  <p><strong>签名:</strong> {record.signature}</p>
                  <p><strong>时间戳:</strong> {record.timestamp}</p>
                  <p><strong>事件数据:</strong></p>
                  <pre style={{ background: '#f5f5f5', padding: 10, borderRadius: 4 }}>
                    {JSON.stringify(record.event_data, null, 2)}
                  </pre>
                  {record.process_result && (
                    <>
                      <p><strong>处理结果:</strong></p>
                      <pre style={{ background: '#f5f5f5', padding: 10, borderRadius: 4 }}>
                        {JSON.stringify(record.process_result, null, 2)}
                      </pre>
                    </>
                  )}
                </div>
              )
            });
          }}
        >
          详情
        </Button>
      )
    }
  ];

  return (
    <div>
      <Card title="Webhook管理" style={{ marginBottom: 16 }}>
        <Row gutter={16} style={{ marginBottom: 16 }}>
          <Col span={6}>
            <Statistic
              title="总Webhook数"
              value={statistics.total_subscriptions || 0}
              prefix={<SettingOutlined />}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="活跃Webhook"
              value={statistics.active_subscriptions || 0}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#3f8600' }}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="今日事件"
              value={statistics.today_events || 0}
              prefix={<BellOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="待处理事件"
              value={statistics.pending_events || 0}
              prefix={<ExclamationCircleOutlined />}
              valueStyle={{ color: '#cf1322' }}
            />
          </Col>
        </Row>

        <div style={{ marginBottom: 16 }}>
          <Space>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleCreateWebhook}
            >
              创建Webhook
            </Button>
            <Button
              icon={<ReloadOutlined />}
              onClick={() => {
                loadWebhooks();
                loadEventLogs();
                loadStatistics();
              }}
            >
              刷新
            </Button>
          </Space>
        </div>

        <Tabs defaultActiveKey="webhooks">
          <TabPane tab="Webhook列表" key="webhooks">
            <Table
              columns={webhookColumns}
              dataSource={webhooks}
              rowKey="id"
              loading={loading}
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条记录`
              }}
            />
          </TabPane>
          <TabPane tab="事件日志" key="logs">
            <Table
              columns={eventLogColumns}
              dataSource={eventLogs}
              rowKey="id"
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条记录`
              }}
            />
          </TabPane>
        </Tabs>
      </Card>

      {/* 创建/编辑Webhook模态框 */}
      <Modal
        title={editingWebhook ? '编辑Webhook' : '创建Webhook'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={800}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item
            name="name"
            label="Webhook名称"
            rules={[{ required: true, message: '请输入Webhook名称' }]}
          >
            <Input placeholder="请输入Webhook名称" />
          </Form.Item>

          <Form.Item label="Webhook类型">
            <Radio.Group
              value={webhookType}
              onChange={(e) => setWebhookType(e.target.value)}
              disabled={!!editingWebhook}
            >
              <Radio.Button value="robot">
                <RobotOutlined /> 机器人通知
              </Radio.Button>
              <Radio.Button value="event">
                <ApiOutlined /> 事件订阅
              </Radio.Button>
            </Radio.Group>
          </Form.Item>

          {webhookType === 'robot' && (
            <>
              <Form.Item
                name="webhook_url"
                label="机器人Webhook URL"
                rules={[{ required: true, message: '请输入机器人Webhook URL' }]}
              >
                <Input placeholder="https://oapi.dingtalk.com/robot/send?access_token=..." />
              </Form.Item>

              <Form.Item
                name="secret"
                label="机器人签名密钥"
                rules={[{ required: true, message: '请输入机器人签名密钥' }]}
                extra="钉钉机器人的签名密钥，用于验证消息来源"
              >
                <Input.Password placeholder="请输入机器人签名密钥" />
              </Form.Item>
            </>
          )}

          {webhookType === 'event' && (
            <>
              <Alert
                message="事件订阅配置"
                description="请先在钉钉开放平台配置事件订阅，然后在此处填写对应的参数。详细配置指引请查看插件文档。"
                type="info"
                showIcon
                style={{ marginBottom: 16 }}
              />

              <Form.Item label="订阅模式">
                <Radio.Group
                  value={subscriptionMode}
                  onChange={(e) => setSubscriptionMode(e.target.value)}
                >
                  <Radio.Button value="http">
                    <ApiOutlined /> HTTP推送模式
                  </Radio.Button>
                  <Radio.Button value="stream">
                    <LinkOutlined /> Stream模式
                  </Radio.Button>
                </Radio.Group>
                <div style={{ marginTop: 8, fontSize: '12px', color: '#666' }}>
                  HTTP推送模式需要公网回调地址，Stream模式使用WebSocket长连接
                </div>
              </Form.Item>

              <Alert
                message={
                  subscriptionMode === 'http' 
                    ? "HTTP推送模式：钉钉服务器主动向您的应用推送事件数据，需要配置AES密钥和Token进行加解密。"
                    : "Stream模式：通过WebSocket长连接接收事件，无需公网回调地址，推荐使用。"
                }
                type={subscriptionMode === 'stream' ? 'success' : 'info'}
                showIcon
                style={{ marginBottom: 16 }}
              />

              <Form.Item
                name="callback_url"
                label="回调URL"
                rules={[{ required: true, message: '请输入回调URL' }]}
                extra="此URL已自动填充，对应钉钉开放平台的'请求网址'配置"
              >
                <Input 
                  placeholder="请输入事件回调URL" 
                  disabled={!editingWebhook} // 新建时禁用编辑，编辑时允许修改
                />
              </Form.Item>

              {subscriptionMode === 'http' && (
                <>
                  <Divider orientation="left">HTTP推送模式配置</Divider>
                  
                  <Form.Item
                    name="aes_key"
                    label="AES加密密钥"
                    rules={[
                      { required: true, message: '请输入AES加密密钥' },
                      { len: 43, message: 'AES密钥必须为43位字符' }
                    ]}
                    extra="从钉钉开放平台复制43位AES加密密钥"
                  >
                    <Input.Password placeholder="请输入43位AES加密密钥" />
                  </Form.Item>

                  <Form.Item
                    name="token"
                    label="签名Token"
                    rules={[{ required: true, message: '请输入签名Token' }]}
                    extra="从钉钉开放平台复制签名Token"
                  >
                    <Input.Password placeholder="请输入签名Token" />
                  </Form.Item>
                </>
              )}

              {subscriptionMode === 'stream' && (
                <React.Fragment>
                  <Divider orientation="left">Stream模式配置</Divider>
                  
                  <Form.Item
                    name="stream_endpoint"
                    label="Stream连接端点"
                    extra="Stream模式的WebSocket连接地址，留空将使用默认配置"
                  >
                    <Input placeholder="wss://your-stream-endpoint" />
                  </Form.Item>
                </React.Fragment>
              )}

              <Divider orientation="left">事件类型选择</Divider>

              <Form.Item
                name="event_types"
                label="订阅事件类型"
                rules={[{ required: true, message: '请选择要订阅的事件类型' }]}
                extra="选择需要接收的钉钉事件类型，建议根据实际业务需求选择"
              >
                <Select
                  mode="multiple"
                  placeholder="请选择要订阅的事件类型"
                  optionLabelProp="label"
                >
                  {eventTypes.map(type => (
                    <Option key={type.value} value={type.value} label={type.label}>
                      <div>
                        <div>{type.label}</div>
                        <div style={{ fontSize: '12px', color: '#999' }}>
                          {type.description}
                        </div>
                      </div>
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </>
          )}

          <Form.Item
            name="description"
            label="描述"
          >
            <TextArea rows={3} placeholder="请输入描述信息" />
          </Form.Item>

          <Form.Item
            name="enabled"
            label="启用状态"
            valuePropName="checked"
            initialValue={true}
          >
            <Switch checkedChildren="启用" unCheckedChildren="禁用" />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit" loading={loading}>
                {editingWebhook ? '更新' : '创建'}
              </Button>
              <Button onClick={() => setModalVisible(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 日志详情模态框 */}
      <Modal
        title={`${selectedWebhook?.name} - 事件日志`}
        open={logModalVisible}
        onCancel={() => setLogModalVisible(false)}
        footer={null}
        width={1000}
      >
        <Table
          columns={eventLogColumns}
          dataSource={eventLogs.filter(log => 
            selectedWebhook?.event_types?.includes(log.event_type)
          )}
          rowKey="id"
          size="small"
          pagination={{
            pageSize: 5,
            showSizeChanger: false
          }}
        />
      </Modal>
    </div>
  );
};

export default WebhookManagement; 