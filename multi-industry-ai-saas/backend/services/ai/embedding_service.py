#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
AI 嵌入服务
处理文本嵌入、向量管理和RAG相关功能
"""

import logging
import uuid
import json
import time
import httpx
from typing import List, Optional, Dict, Any, Union, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func, text, delete
from sqlalchemy.orm import selectinload
from datetime import datetime

from models.ai import AIConfig, AIModel, AIProvider
from models.project import Project
from models.user import User
from services.ai import AIConfigService, AIUsageService
from schemas.ai import AIUsageCreate
from models.ai.embedding import AIEmbedding
from models.ai.model import AIModel
from models.ai.knowledge import AIKnowledgeBase, AIKnowledgeDocument, AIKnowledgeChunk
from schemas.ai.embedding import (
    AIEmbeddingCreate,
    AIEmbeddingQuery
)
# from services.ai.core_service import AICoreService  # 暂时注释掉，待实现

logger = logging.getLogger(__name__)

class AIEmbeddingService:
    """AI 嵌入服务"""

    @staticmethod
    async def get_embedding_models(
        db: AsyncSession,
        skip: int = 0,
        limit: int = 100,
        status: str = "active",
    ) -> Tuple[List[AIModel], int]:
        """
        获取嵌入模型列表
        """
        try:
            # 查询嵌入模型
            query = select(AIModel).where(
                and_(
                    AIModel.model_type == "embedding",
                    AIModel.status == status,
                )
            )

            # 获取总数
            count_query = select(func.count()).select_from(query.subquery())
            total = await db.scalar(count_query)

            # 获取分页数据
            query = query.order_by(AIModel.created_at).offset(skip).limit(limit)
            result = await db.execute(query)
            models = result.scalars().all()

            return models, total

        except Exception as e:
            logger.error(f"获取嵌入模型列表失败: {e}")
            return [], 0

    @staticmethod
    async def get_embedding_model(
        db: AsyncSession,
        model_id: uuid.UUID,
    ) -> Optional[AIModel]:
        """
        获取嵌入模型详情
        """
        try:
            query = select(AIModel).where(
                and_(
                    AIModel.id == model_id,
                    AIModel.model_type == "embedding",
                )
            )
            result = await db.execute(query)
            return result.scalars().first()

        except Exception as e:
            logger.error(f"获取嵌入模型详情失败: {e}")
            return None

    @staticmethod
    async def create_embedding(
        db: AsyncSession,
        project_id: uuid.UUID,
        user_id: uuid.UUID,
        input: Union[str, List[str]],
        model_id: uuid.UUID,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        创建文本嵌入
        """
        try:
            # 获取模型信息
            model = await AIEmbeddingService.get_embedding_model(db, model_id)
            if not model:
                return {
                    "success": False,
                    "message": f"嵌入模型 ID '{model_id}' 不存在",
                }

            # 获取模型配置 - 从系统整合设置中获取默认配置
            from services.ai.system_integration_service import SystemAIIntegrationService
            
            system_config = await SystemAIIntegrationService.get_integration_config(
                db=db,
                project_id=project_id
            )

            if not system_config or not system_config.embedding_enabled:
                return {
                    "success": False,
                    "message": "项目未启用嵌入功能，请在AI助手系统整合中启用嵌入功能",
                }

            # 获取模型的提供商配置
            provider_query = select(AIProvider).where(AIProvider.id == model.provider_id)
            provider_result = await db.execute(provider_query)
            provider = provider_result.scalars().first()
            
            if not provider:
                return {
                    "success": False,
                    "message": f"模型提供商不存在",
                }

            # 获取该提供商的有效配置
            config_query = select(AIConfig).where(
                and_(
                    AIConfig.provider_id == provider.id,
                    AIConfig.project_id == project_id,
                    AIConfig.status == "active"
                )
            ).order_by(AIConfig.created_at.desc())
            
            config_result = await db.execute(config_query)
            ai_config = config_result.scalars().first()
            
            if not ai_config:
                return {
                    "success": False,
                    "message": f"项目未配置{provider.display_name}提供商，请在AI助手设置中配置API密钥",
                }

            # 解密API密钥
            from core.security import decrypt_text
            decrypted_api_key = decrypt_text(ai_config.api_key)
            
            # 调用AI服务生成嵌入向量
            embedding_data = await AIEmbeddingService._call_embedding_api(
                model=model,
                provider=provider,
                config=ai_config.config or {},
                api_key=decrypted_api_key,
                input=input,
            )

            if not embedding_data.get("success"):
                return {
                    "success": False,
                    "message": f"生成嵌入向量失败: {embedding_data.get('message', '未知错误')}",
                }

            # 获取项目的租户ID
            from models.project import Project
            project_query = select(Project).where(Project.id == project_id)
            project_result = await db.execute(project_query)
            project_obj = project_result.scalars().first()
            
            # 记录使用情况
            await AIUsageService.create_usage(
                db=db,
                usage_data=AIUsageCreate(
                    tenant_id=project_obj.tenant_id if project_obj else None,
                    project_id=project_id,
                    user_id=user_id,
                    config_id=ai_config.id,
                    model_id=model_id,
                    request_type="embedding",
                    prompt_tokens=len(str(input).split()) if isinstance(input, str) else sum(len(s.split()) for s in input),
                    completion_tokens=0,
                    total_tokens=len(str(input).split()) if isinstance(input, str) else sum(len(s.split()) for s in input),
                    cost=embedding_data.get("cost", 0.0),
                    request_metadata={
                        "model_name": model.name,
                        "provider_name": provider.name,
                        "input_type": "text",
                        "dimensions": embedding_data.get("dimensions", 1536),
                        "metadata": metadata,
                    },
                ),
            )

            # 保存嵌入记录（可选）
            if metadata and metadata.get("save_to_db", False):
                for i, embedding_vector in enumerate(embedding_data["data"]):
                    text_input = input[i] if isinstance(input, list) else input
                    embedding_record = AIEmbedding(
                        project_id=project_id,
                        model_id=model_id,
                        input_text=text_input,
                        embedding_vector=embedding_vector["embedding"],
                        dimensions=len(embedding_vector["embedding"]),
                        embedding_metadata=metadata,
                        token_count=len(text_input.split()),
                        created_by=user_id,
                    )
                    db.add(embedding_record)

                await db.commit()

            return {
                "success": True,
                "message": "生成嵌入向量成功",
                "data": embedding_data["data"],
                "model": {
                    "id": str(model.id),
                    "name": model.name,
                    "provider": provider.name if provider else None,
                },
            }

        except Exception as e:
            logger.error(f"创建嵌入向量失败: {e}")
            return {
                "success": False,
                "message": f"创建嵌入向量失败: {str(e)}",
            }

    @staticmethod
    async def _call_embedding_api(
        model: AIModel,
        provider: AIProvider,
        config: Dict[str, Any],
        api_key: str,
        input: Union[str, List[str]],
    ) -> Dict[str, Any]:
        """
        调用嵌入API
        """
        try:
            # 根据提供商构建请求数据
            if provider.name == "alibaba_bailian":
                # 阿里云百炼的请求格式
                request_data = {
                    "model": model.name,
                    "input": {
                        "texts": input if isinstance(input, list) else [input]
                    }
                }
            else:
                # OpenAI格式的请求数据
                request_data = {
                    "model": model.name,
                    "input": input,
                    "encoding_format": "float",  # 标准格式
                }

            # 根据提供商构建请求头
            if provider.name == "alibaba_bailian":
                # 阿里云百炼使用API-KEY认证
                headers = {
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {api_key}",
                }
            else:
                # 默认使用OpenAI格式
                headers = {
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {api_key}",
                }
            
            # 根据模型提供商获取API基础URL
            api_base = config.get("api_base") or config.get("base_url") or provider.api_base_url
            
            # 根据不同提供商设置正确的端点
            if provider.name == "alibaba_bailian":
                # 阿里云百炼的嵌入端点
                api_base = api_base.rstrip("/") + "/services/embeddings/text-embedding/text-embedding"
            else:
                # 默认使用OpenAI格式的端点
                if not api_base.endswith("/embeddings"):
                    api_base = api_base.rstrip("/") + "/embeddings"

            # 发送请求
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.post(
                    api_base,
                    json=request_data,
                    headers=headers,
                )

                if response.status_code == 200:
                    data = response.json()
                    
                    # 根据提供商处理响应格式
                    if provider.name == "alibaba_bailian":
                        # 阿里云百炼的响应格式
                        if data.get("output") and data["output"].get("embeddings"):
                            embeddings_data = []
                            for i, embedding in enumerate(data["output"]["embeddings"]):
                                embeddings_data.append({
                                    "embedding": embedding["embedding"],
                                    "index": i
                                })
                            
                            return {
                                "success": True,
                                "data": embeddings_data,
                                "dimensions": len(embeddings_data[0]["embedding"]) if embeddings_data else 1536,
                                "usage": data.get("usage", {}),
                            }
                        else:
                            return {
                                "success": False,
                                "message": f"阿里云百炼API响应格式错误: {data}",
                            }
                    else:
                        # OpenAI格式的响应
                        return {
                            "success": True,
                            "data": data.get("data", []),
                            "dimensions": len(data["data"][0]["embedding"]) if data.get("data") else 1536,
                            "usage": data.get("usage", {}),
                        }
                else:
                    error_info = response.text
                    logger.error(f"嵌入API调用失败: {response.status_code}, {error_info}")
                    return {
                        "success": False,
                        "message": f"API调用失败: {response.status_code} - {error_info}",
                    }

        except Exception as e:
            logger.error(f"调用嵌入API异常: {e}")
            return {
                "success": False,
                "message": f"调用嵌入API异常: {str(e)}",
            }

    @staticmethod
    async def search_similar_embeddings(
        db: AsyncSession,
        query_vector: List[float],
        project_id: Optional[uuid.UUID] = None,
        tenant_id: Optional[uuid.UUID] = None,
        knowledge_base_ids: Optional[List[uuid.UUID]] = None,
        top_k: int = 5,
        similarity_threshold: float = 0.7,
        metadata_filter: Optional[Dict[str, Any]] = None,
    ) -> List[Dict[str, Any]]:
        """
        搜索相似的嵌入向量
        """
        try:
            # 构建查询条件
            conditions = []
            
            if project_id:
                conditions.append(AIKnowledgeChunk.project_id == project_id)
            elif tenant_id:
                conditions.append(AIKnowledgeChunk.tenant_id == tenant_id)
                
            if knowledge_base_ids:
                conditions.append(AIKnowledgeChunk.knowledge_base_id.in_(knowledge_base_ids))

            # 使用向量相似度搜索
            # 这里使用余弦相似度，实际使用时可能需要配置不同的距离函数
            query_vector_str = f"[{','.join(map(str, query_vector))}]"
            
            # 构建SQL查询（使用余弦相似度）
            similarity_sql = f"""
                SELECT 
                    akc.*,
                    akd.title as document_title,
                    akb.name as knowledge_base_name,
                    (
                        (akc.embedding <-> '{query_vector_str}'::vector) * -1 + 1
                    ) as similarity_score
                FROM ai_knowledge_chunks akc
                LEFT JOIN ai_knowledge_documents akd ON akc.document_id = akd.id
                LEFT JOIN ai_knowledge_bases akb ON akc.knowledge_base_id = akb.id
                WHERE akc.embedding IS NOT NULL
            """
            
            # 添加过滤条件
            if project_id:
                similarity_sql += f" AND akc.project_id = '{project_id}'"
            elif tenant_id:
                similarity_sql += f" AND akc.tenant_id = '{tenant_id}'"
                
            if knowledge_base_ids:
                kb_ids_str = "','".join(str(kb_id) for kb_id in knowledge_base_ids)
                similarity_sql += f" AND akc.knowledge_base_id IN ('{kb_ids_str}')"

            # 添加相似度阈值和排序
            similarity_sql += f"""
                HAVING (
                    (akc.embedding <-> '{query_vector_str}'::vector) * -1 + 1
                ) >= {similarity_threshold}
                ORDER BY akc.embedding <-> '{query_vector_str}'::vector
                LIMIT {top_k}
            """

            result = await db.execute(text(similarity_sql))
            rows = result.fetchall()

            # 转换结果
            similar_chunks = []
            for row in rows:
                chunk_dict = dict(row._mapping)
                similar_chunks.append({
                    "id": str(chunk_dict["id"]),
                    "content": chunk_dict["content"],
                    "similarity_score": float(chunk_dict["similarity_score"]),
                    "chunk_index": chunk_dict["chunk_index"],
                    "token_count": chunk_dict["token_count"],
                    "document_title": chunk_dict["document_title"],
                    "knowledge_base_name": chunk_dict["knowledge_base_name"],
                    "metadata": chunk_dict.get("chunk_metadata", {})
                })

            logger.info(f"搜索相似嵌入向量完成，找到 {len(similar_chunks)} 个结果")
            return similar_chunks

        except Exception as e:
            logger.error(f"搜索相似嵌入向量失败: {e}")
            return []

    @staticmethod
    async def cleanup_orphaned_embeddings(
        db: AsyncSession,
        project_id: Optional[uuid.UUID] = None,
        tenant_id: Optional[uuid.UUID] = None,
    ) -> Dict[str, Any]:
        """
        清理孤立的嵌入记录（没有对应文档的嵌入记录）
        """
        try:
            # 查找孤立的knowledge_chunks（没有对应文档的）
            orphaned_chunks_query = text("""
                SELECT akc.id
                FROM ai_knowledge_chunks akc
                LEFT JOIN ai_knowledge_documents akd ON akc.document_id = akd.id
                WHERE akd.id IS NULL
            """)
            
            if project_id:
                orphaned_chunks_query = text("""
                    SELECT akc.id
                    FROM ai_knowledge_chunks akc
                    LEFT JOIN ai_knowledge_documents akd ON akc.document_id = akd.id
                    WHERE akd.id IS NULL AND akc.knowledge_base_id IN (
                        SELECT id FROM ai_knowledge_bases WHERE project_id = :project_id
                    )
                """)
            elif tenant_id:
                orphaned_chunks_query = text("""
                    SELECT akc.id
                    FROM ai_knowledge_chunks akc
                    LEFT JOIN ai_knowledge_documents akd ON akc.document_id = akd.id
                    WHERE akd.id IS NULL AND akc.knowledge_base_id IN (
                        SELECT id FROM ai_knowledge_bases WHERE tenant_id = :tenant_id
                    )
                """)

            # 执行查询
            params = {}
            if project_id:
                params["project_id"] = str(project_id)
            elif tenant_id:
                params["tenant_id"] = str(tenant_id)
                
            result = await db.execute(orphaned_chunks_query, params)
            orphaned_chunk_ids = [row[0] for row in result.fetchall()]

            if not orphaned_chunk_ids:
                return {
                    "success": True,
                    "message": "没有发现孤立的向量记录",
                    "deleted_count": 0
                }

            # 删除孤立的chunks
            delete_query = delete(AIKnowledgeChunk).where(
                AIKnowledgeChunk.id.in_(orphaned_chunk_ids)
            )
            
            delete_result = await db.execute(delete_query)
            deleted_count = delete_result.rowcount
            
            await db.commit()

            logger.info(f"成功清理孤立向量记录: {deleted_count} 条")
            return {
                "success": True,
                "message": f"成功清理孤立向量记录: {deleted_count} 条",
                "deleted_count": deleted_count
            }

        except Exception as e:
            await db.rollback()
            logger.error(f"清理孤立向量记录失败: {e}")
            return {
                "success": False,
                "message": f"清理孤立向量记录失败: {str(e)}",
                "deleted_count": 0
            }

    @staticmethod
    async def rebuild_vector_index(
        db: AsyncSession,
        project_id: Optional[uuid.UUID] = None,
        tenant_id: Optional[uuid.UUID] = None,
        knowledge_base_ids: Optional[List[uuid.UUID]] = None,
    ) -> Dict[str, Any]:
        """
        重建向量索引，重新生成所有文档的嵌入向量
        """
        try:
            # 构建查询条件
            conditions = [AIKnowledgeDocument.embedding_status.in_(["completed", "failed"])]
            
            if project_id:
                conditions.append(AIKnowledgeDocument.knowledge_base_id.in_(
                    select(AIKnowledgeBase.id).where(AIKnowledgeBase.project_id == project_id)
                ))
            elif tenant_id:
                conditions.append(AIKnowledgeDocument.knowledge_base_id.in_(
                    select(AIKnowledgeBase.id).where(AIKnowledgeBase.tenant_id == tenant_id)
                ))
                
            if knowledge_base_ids:
                conditions.append(AIKnowledgeDocument.knowledge_base_id.in_(knowledge_base_ids))

            # 查询需要重建索引的文档
            query = select(AIKnowledgeDocument).where(and_(*conditions))
            result = await db.execute(query)
            documents = result.scalars().all()

            if not documents:
                return {
                    "success": True,
                    "message": "没有需要重建索引的文档",
                    "processed_count": 0
                }

            processed_count = 0
            failed_count = 0

            for document in documents:
                try:
                    # 删除现有的chunks
                    delete_chunks_query = delete(AIKnowledgeChunk).where(
                        AIKnowledgeChunk.document_id == document.id
                    )
                    await db.execute(delete_chunks_query)

                    # 重置文档状态为pending，触发重新处理
                    document.embedding_status = "pending"
                    document.error_message = None
                    document.chunk_count = 0

                    processed_count += 1

                except Exception as e:
                    logger.error(f"重建文档 {document.id} 索引失败: {e}")
                    failed_count += 1

            await db.commit()

            logger.info(f"重建向量索引完成，处理 {processed_count} 个文档，失败 {failed_count} 个")
            return {
                "success": True,
                "message": f"重建向量索引完成，处理 {processed_count} 个文档，失败 {failed_count} 个",
                "processed_count": processed_count,
                "failed_count": failed_count
            }

        except Exception as e:
            await db.rollback()
            logger.error(f"重建向量索引失败: {e}")
            return {
                "success": False,
                "message": f"重建向量索引失败: {str(e)}",
                "processed_count": 0
            }

    @staticmethod
    async def optimize_vector_storage(
        db: AsyncSession,
        project_id: Optional[uuid.UUID] = None,
        tenant_id: Optional[uuid.UUID] = None,
    ) -> Dict[str, Any]:
        """
        优化向量存储：去重、压缩等
        """
        try:
            # 1. 查找重复的向量
            duplicate_query = text("""
                SELECT content, COUNT(*) as count, array_agg(id) as chunk_ids
                FROM ai_knowledge_chunks akc
                WHERE akc.embedding IS NOT NULL
                GROUP BY content
                HAVING COUNT(*) > 1
            """)
            
            # 添加过滤条件
            if project_id:
                duplicate_query = text("""
                    SELECT content, COUNT(*) as count, array_agg(id) as chunk_ids
                    FROM ai_knowledge_chunks akc
                    WHERE akc.embedding IS NOT NULL 
                    AND akc.knowledge_base_id IN (
                        SELECT id FROM ai_knowledge_bases WHERE project_id = :project_id
                    )
                    GROUP BY content
                    HAVING COUNT(*) > 1
                """)
            elif tenant_id:
                duplicate_query = text("""
                    SELECT content, COUNT(*) as count, array_agg(id) as chunk_ids
                    FROM ai_knowledge_chunks akc
                    WHERE akc.embedding IS NOT NULL 
                    AND akc.knowledge_base_id IN (
                        SELECT id FROM ai_knowledge_bases WHERE tenant_id = :tenant_id
                    )
                    GROUP BY content
                    HAVING COUNT(*) > 1
                """)

            params = {}
            if project_id:
                params["project_id"] = str(project_id)
            elif tenant_id:
                params["tenant_id"] = str(tenant_id)

            result = await db.execute(duplicate_query, params)
            duplicates = result.fetchall()

            deduped_count = 0
            
            # 删除重复项（保留第一个）
            for row in duplicates:
                chunk_ids = row[2]  # array_agg(id)
                if len(chunk_ids) > 1:
                    # 删除除第一个外的所有重复项
                    ids_to_delete = chunk_ids[1:]
                    delete_query = delete(AIKnowledgeChunk).where(
                        AIKnowledgeChunk.id.in_(ids_to_delete)
                    )
                    delete_result = await db.execute(delete_query)
                    deduped_count += delete_result.rowcount

            # 2. 重建向量索引以优化存储
            await db.execute(text("REINDEX INDEX idx_knowledge_chunks_embedding;"))

            # 3. 清理空的嵌入向量
            empty_embedding_query = delete(AIKnowledgeChunk).where(
                or_(
                    AIKnowledgeChunk.embedding.is_(None),
                    func.array_length(AIKnowledgeChunk.embedding, 1) == 0
                )
            )
            empty_result = await db.execute(empty_embedding_query)
            empty_cleaned = empty_result.rowcount

            await db.commit()

            logger.info(f"向量存储优化完成，去重 {deduped_count} 条，清理空向量 {empty_cleaned} 条")
            return {
                "success": True,
                "message": f"向量存储优化完成，去重 {deduped_count} 条，清理空向量 {empty_cleaned} 条",
                "deduped_count": deduped_count,
                "empty_cleaned_count": empty_cleaned,
                "processed_count": deduped_count + empty_cleaned
            }

        except Exception as e:
            await db.rollback()
            logger.error(f"优化向量存储失败: {e}")
            return {
                "success": False,
                "message": f"优化向量存储失败: {str(e)}",
                "processed_count": 0
            }

    @staticmethod
    async def create_vector_index(
        db: AsyncSession,
        knowledge_base_id: uuid.UUID,
        user_id: uuid.UUID,
        force_rebuild: bool = False,
    ) -> Dict[str, Any]:
        """
        为指定知识库创建向量索引
        """
        try:
            # 获取知识库信息
            knowledge_base_query = select(AIKnowledgeBase).where(
                AIKnowledgeBase.id == knowledge_base_id
            )
            result = await db.execute(knowledge_base_query)
            knowledge_base = result.scalars().first()

            if not knowledge_base:
                return {
                    "success": False,
                    "message": f"知识库 ID '{knowledge_base_id}' 不存在"
                }

            # 从系统整合设置中获取默认嵌入模型
            from services.ai.system_integration_service import SystemAIIntegrationService
            
            system_config = await SystemAIIntegrationService.get_integration_config(
                db=db,
                project_id=knowledge_base.project_id
            )

            if not system_config or not system_config.embedding_enabled:
                return {
                    "success": False,
                    "message": "项目未启用嵌入功能，请在AI助手系统整合中启用嵌入功能"
                }

            # 获取默认嵌入模型
            default_embedding_model_id = system_config.default_embedding_model_id
            if not default_embedding_model_id:
                return {
                    "success": False,
                    "message": "项目未配置默认嵌入模型，请在AI助手系统整合中选择默认嵌入模型"
                }

            # 验证嵌入模型是否存在
            embedding_model = await AIEmbeddingService.get_embedding_model(db, default_embedding_model_id)
            if not embedding_model:
                return {
                    "success": False,
                    "message": f"默认嵌入模型不存在或已禁用，请在AI助手系统整合中重新选择"
                }

            # 更新知识库的嵌入模型配置（如果还没有配置的话）
            if not knowledge_base.embedding_model_id:
                knowledge_base.embedding_model_id = default_embedding_model_id
                await db.commit()

            # 获取待处理的文档
            conditions = [AIKnowledgeDocument.knowledge_base_id == knowledge_base_id]
            
            if force_rebuild:
                # 强制重建：处理所有文档
                conditions.append(AIKnowledgeDocument.embedding_status.in_(
                    ["pending", "processing", "completed", "failed"]
                ))
            else:
                # 正常模式：只处理未处理的文档
                conditions.append(AIKnowledgeDocument.embedding_status.in_(
                    ["pending", "failed"]
                ))

            documents_query = select(AIKnowledgeDocument).where(and_(*conditions))
            result = await db.execute(documents_query)
            documents = result.scalars().all()

            if not documents:
                # 检查是否有文档但没有嵌入向量的情况
                all_docs_query = select(AIKnowledgeDocument).where(
                    AIKnowledgeDocument.knowledge_base_id == knowledge_base_id
                )
                all_docs_result = await db.execute(all_docs_query)
                all_docs = all_docs_result.scalars().all()
                
                if all_docs and not force_rebuild:
                    return {
                        "success": True,
                        "message": f"知识库中的 {len(all_docs)} 个文档已处理完成。如需重新生成向量，请选择'强制重建'选项",
                        "processed_count": 0,
                        "total_documents": len(all_docs)
                    }
                else:
                    return {
                        "success": True,
                        "message": "知识库中没有文档需要处理",
                        "processed_count": 0,
                        "total_documents": len(all_docs) if all_docs else 0
                    }

            # 更新知识库状态
            knowledge_base.status = "building"
            await db.commit()

            processed_count = 0
            failed_count = 0

            # 导入文档处理服务
            from services.ai.knowledge_service import AIKnowledgeService

            for document in documents:
                try:
                    # 如果是强制重建，先清理现有chunks
                    if force_rebuild:
                        delete_chunks = delete(AIKnowledgeChunk).where(
                            AIKnowledgeChunk.document_id == document.id
                        )
                        await db.execute(delete_chunks)
                        document.chunk_count = 0

                    # 处理文档生成向量
                    success = await AIKnowledgeService.process_document(
                        db=db,
                        document_id=document.id,
                        user_id=user_id,
                        chunk_size=1000,
                        chunk_overlap=200,
                    )

                    if success:
                        processed_count += 1
                    else:
                        failed_count += 1

                except Exception as e:
                    logger.error(f"处理文档 {document.id} 失败: {e}")
                    failed_count += 1

            # 更新知识库状态和统计
            knowledge_base.status = "active"
            
            # 重新计算文档和chunk数量
            doc_count_query = select(func.count(AIKnowledgeDocument.id)).where(
                AIKnowledgeDocument.knowledge_base_id == knowledge_base_id
            )
            doc_count = await db.scalar(doc_count_query) or 0
            
            chunk_count_query = select(func.count(AIKnowledgeChunk.id)).where(
                AIKnowledgeChunk.knowledge_base_id == knowledge_base_id
            )
            chunk_count = await db.scalar(chunk_count_query) or 0
            
            knowledge_base.document_count = doc_count
            knowledge_base.chunk_count = chunk_count
            
            await db.commit()

            logger.info(f"知识库 {knowledge_base_id} 索引创建完成，处理 {processed_count} 个文档，失败 {failed_count} 个")
            return {
                "success": True,
                "message": f"索引创建完成，处理 {processed_count} 个文档，失败 {failed_count} 个",
                "processed_count": processed_count,
                "failed_count": failed_count,
                "total_documents": doc_count,
                "total_chunks": chunk_count
            }

        except Exception as e:
            # 恢复知识库状态
            try:
                knowledge_base.status = "active"
                await db.commit()
            except:
                pass
                
            logger.error(f"创建向量索引失败: {e}")
            return {
                "success": False,
                "message": f"创建向量索引失败: {str(e)}",
                "processed_count": 0
            }

    @staticmethod
    async def get_vector_statistics(
        db: AsyncSession,
        project_id: Optional[uuid.UUID] = None,
        tenant_id: Optional[uuid.UUID] = None,
        knowledge_base_id: Optional[uuid.UUID] = None,
    ) -> Dict[str, Any]:
        """
        获取向量统计信息
        """
        try:
            # 构建基础查询条件
            kb_conditions = []
            chunk_conditions = []
            doc_conditions = []

            if knowledge_base_id:
                kb_conditions.append(AIKnowledgeBase.id == knowledge_base_id)
                chunk_conditions.append(AIKnowledgeChunk.knowledge_base_id == knowledge_base_id)
                doc_conditions.append(AIKnowledgeDocument.knowledge_base_id == knowledge_base_id)
            elif project_id:
                kb_conditions.append(AIKnowledgeBase.project_id == project_id)
                chunk_conditions.append(AIKnowledgeChunk.knowledge_base_id.in_(
                    select(AIKnowledgeBase.id).where(AIKnowledgeBase.project_id == project_id)
                ))
                doc_conditions.append(AIKnowledgeDocument.knowledge_base_id.in_(
                    select(AIKnowledgeBase.id).where(AIKnowledgeBase.project_id == project_id)
                ))
            elif tenant_id:
                kb_conditions.append(AIKnowledgeBase.tenant_id == tenant_id)
                chunk_conditions.append(AIKnowledgeChunk.knowledge_base_id.in_(
                    select(AIKnowledgeBase.id).where(AIKnowledgeBase.tenant_id == tenant_id)
                ))
                doc_conditions.append(AIKnowledgeDocument.knowledge_base_id.in_(
                    select(AIKnowledgeBase.id).where(AIKnowledgeBase.tenant_id == tenant_id)
                ))

            # 1. 知识库统计
            kb_total_query = select(func.count(AIKnowledgeBase.id)).where(and_(*kb_conditions))
            kb_total = await db.scalar(kb_total_query) or 0

            kb_active_query = select(func.count(AIKnowledgeBase.id)).where(
                and_(AIKnowledgeBase.status == "active", *kb_conditions)
            )
            kb_active = await db.scalar(kb_active_query) or 0

            # 2. 文档统计
            doc_total_query = select(func.count(AIKnowledgeDocument.id)).where(and_(*doc_conditions))
            doc_total = await db.scalar(doc_total_query) or 0

            doc_completed_query = select(func.count(AIKnowledgeDocument.id)).where(
                and_(AIKnowledgeDocument.embedding_status == "completed", *doc_conditions)
            )
            doc_completed = await db.scalar(doc_completed_query) or 0

            doc_pending_query = select(func.count(AIKnowledgeDocument.id)).where(
                and_(AIKnowledgeDocument.embedding_status == "pending", *doc_conditions)
            )
            doc_pending = await db.scalar(doc_pending_query) or 0

            doc_failed_query = select(func.count(AIKnowledgeDocument.id)).where(
                and_(AIKnowledgeDocument.embedding_status == "failed", *doc_conditions)
            )
            doc_failed = await db.scalar(doc_failed_query) or 0

            # 3. 向量块统计
            chunk_total_query = select(func.count(AIKnowledgeChunk.id)).where(and_(*chunk_conditions))
            chunk_total = await db.scalar(chunk_total_query) or 0

            chunk_with_embedding_query = select(func.count(AIKnowledgeChunk.id)).where(
                and_(AIKnowledgeChunk.embedding.is_not(None), *chunk_conditions)
            )
            chunk_with_embedding = await db.scalar(chunk_with_embedding_query) or 0

            # 4. 存储使用统计
            # 估算向量存储大小（假设每个向量1536维，每维4字节）
            estimated_vector_size = chunk_with_embedding * 1536 * 4  # bytes

            return {
                "knowledge_bases": {
                    "total": kb_total,
                    "active": kb_active,
                    "building": kb_total - kb_active
                },
                "documents": {
                    "total": doc_total,
                    "completed": doc_completed,
                    "pending": doc_pending,
                    "failed": doc_failed,
                    "completion_rate": round(doc_completed / doc_total * 100, 2) if doc_total > 0 else 0
                },
                "vectors": {
                    "total_chunks": chunk_total,
                    "indexed_chunks": chunk_with_embedding,
                    "index_rate": round(chunk_with_embedding / chunk_total * 100, 2) if chunk_total > 0 else 0,
                    "estimated_storage_size": estimated_vector_size,
                    "estimated_storage_size_mb": round(estimated_vector_size / 1024 / 1024, 2)
                }
            }

        except Exception as e:
            logger.error(f"获取向量统计信息失败: {e}")
            return {
                "knowledge_bases": {"total": 0, "active": 0, "building": 0},
                "documents": {"total": 0, "completed": 0, "pending": 0, "failed": 0, "completion_rate": 0},
                "vectors": {"total_chunks": 0, "indexed_chunks": 0, "index_rate": 0, "estimated_storage_size": 0, "estimated_storage_size_mb": 0}
            }
