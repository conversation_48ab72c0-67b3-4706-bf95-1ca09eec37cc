import React, { useState, useEffect } from 'react';
import {
  Form,
  Input,
  Select,
  DatePicker,
  InputNumber,
  Switch,
  Button,
  Card,
  Row,
  Col,
  Upload,
  message,
  Tabs,
  Space,
  Divider,
  Tag,
  Tooltip,
  ColorPicker,
  Slider,
  Alert,
  Modal,
  List,
  Avatar,
  Typography,
  Checkbox
} from 'antd';
import {
  PlusOutlined,
  DeleteOutlined,
  InfoCircleOutlined,
  ShopOutlined,
  RobotOutlined,
  BarChartOutlined,
  ShareAltOutlined,
  EnvironmentOutlined,
  WechatOutlined,
  SoundOutlined,
  BgColorsOutlined,
  SettingOutlined,
  BulbOutlined,
  GiftOutlined
} from '@ant-design/icons';
import dayjs from 'dayjs';
import { gameTypeOptions } from '../utils/constants';
import storeApi from '../../../../../services/api/storeApi';
import aiApi from '../../../../../services/api/aiApi';

const { Option } = Select;
const { RangePicker } = DatePicker;
const { TabPane } = Tabs;
const { TextArea } = Input;
const { Title, Text, Paragraph } = Typography;

// 营销类型选项
const marketingTypeOptions = [
  { label: '开业营销', value: 'opening_promotion', color: '#f50', icon: '🎉' },
  { label: '周年庆', value: 'anniversary', color: '#faad14', icon: '🎂' },
  { label: '节假日营销', value: 'holiday_marketing', color: '#52c41a', icon: '🎄' },
  { label: '自造节', value: 'custom_festival', color: '#1890ff', icon: '🎊' },
  { label: '日常活动', value: 'daily_activity', color: '#722ed1', icon: '😊' },
  { label: '会员促销', value: 'membership_promotion', color: '#eb2f96', icon: '💎' }
];

// AI助手性格选项
const aiPersonalityOptions = [
  { label: '友好型', value: 'friendly', description: '温馨友好，像朋友一样陪伴用户' },
  { label: '专业型', value: 'professional', description: '专业严谨，提供权威的游戏指导' },
  { label: '活泼型', value: 'playful', description: '活泼有趣，为游戏增添更多乐趣' }
];

// 主题样式选项
const themeOptions = [
  { label: '经典风格', value: 'classic', color: '#1890ff' },
  { label: '黄金风格', value: 'gold', color: '#faad14' },
  { label: '霓虹风格', value: 'neon', color: '#00BFFF' },
  { label: '彩虹风格', value: 'rainbow', color: '#ff6b6b' },
  { label: '简约风格', value: 'minimal', color: '#000000' },
  { label: '渐变风格', value: 'gradient', color: '#667eea' }
];

const EnhancedGameForm = ({ 
  game, 
  visible, 
  onCancel, 
  onSubmit, 
  loading = false 
}) => {
  const [form] = Form.useForm();
  const [activeTab, setActiveTab] = useState('basic');
  const [stores, setStores] = useState([]);
  const [loadingStores, setLoadingStores] = useState(false);
  const [aiPrompts, setAiPrompts] = useState([]);
  const [coverImage, setCoverImage] = useState(null);
  const [shareImage, setShareImage] = useState(null);
  const [backgroundMusic, setBackgroundMusic] = useState(null);
  const [selectedStores, setSelectedStores] = useState([]);
  const [aiPreview, setAiPreview] = useState(false);
  const [previewMessage, setPreviewMessage] = useState('');

  useEffect(() => {
    if (visible) {
      fetchStores();
      if (game) {
        initializeForm();
      } else {
        resetForm();
      }
    }
  }, [visible, game]);

  const fetchStores = async () => {
    try {
      setLoadingStores(true);
      const response = await storeApi.getStores();
      if (response.success) {
        setStores(response.data || []);
      }
    } catch (error) {
      console.error('获取门店列表失败:', error);
      message.error('获取门店列表失败');
    } finally {
      setLoadingStores(false);
    }
  };

  const initializeForm = () => {
    form.setFieldsValue({
      ...game,
      time_range: [dayjs(game.start_time), dayjs(game.end_time)],
      store_ids: game.store_ids || [],
      ai_help_prompts: game.ai_help_prompts || [],
      sound_effects: game.sound_effects || {},
      animation_settings: game.animation_settings || {},
      marketing_goals: game.marketing_goals || {},
      target_audience: game.target_audience || {},
      promotion_strategy: game.promotion_strategy || {}
    });
    
    setSelectedStores(game.store_ids || []);
    setAiPrompts(game.ai_help_prompts || []);
    setCoverImage(game.cover_image);
    setShareImage(game.wechat_share_image);
    setBackgroundMusic(game.background_music);
  };

  const resetForm = () => {
    form.resetFields();
    setSelectedStores([]);
    setAiPrompts([]);
    setCoverImage(null);
    setShareImage(null);
    setBackgroundMusic(null);
    
    // 设置默认值
    form.setFieldsValue({
      game_type: 'lucky_draw',
      marketing_type: 'daily_activity',
      max_draws_per_user: 1,
      use_enhanced_ui: true,
      theme_style: 'classic',
      ai_personality: 'friendly',
      enable_analytics: true,
      track_user_behavior: true,
      enable_social_sharing: true,
      enable_wechat_share: true,
      location_restriction: false,
      is_global: true
    });
  };

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      
      // 处理时间范围
      if (values.time_range) {
        values.start_time = values.time_range[0].toISOString();
        values.end_time = values.time_range[1].toISOString();
        delete values.time_range;
      }

      // 处理门店关联
      values.store_ids = selectedStores;
      values.ai_help_prompts = aiPrompts;

      // 处理图片上传
      if (coverImage) values.cover_image = coverImage;
      if (shareImage) values.wechat_share_image = shareImage;
      if (backgroundMusic) values.background_music = backgroundMusic;

      onSubmit(values);
    } catch (error) {
      console.error('表单验证失败:', error);
      message.error('请检查表单填写是否完整');
    }
  };

  const handleImageUpload = (type) => {
    return {
      beforeUpload: (file) => {
        const isImage = file.type.startsWith('image/');
        const isLt2M = file.size / 1024 / 1024 < 2;

        if (!isImage) {
          message.error('只能上传图片文件!');
          return false;
        }
        if (!isLt2M) {
          message.error('图片大小不能超过 2MB!');
          return false;
        }

        // 创建预览URL
        const reader = new FileReader();
        reader.onload = (e) => {
          if (type === 'cover') {
            setCoverImage(e.target.result);
          } else if (type === 'share') {
            setShareImage(e.target.result);
          }
        };
        reader.readAsDataURL(file);

        return false; // 阻止自动上传
      }
    };
  };

  const handleAudioUpload = {
    beforeUpload: (file) => {
      const isAudio = file.type.startsWith('audio/');
      const isLt5M = file.size / 1024 / 1024 < 5;

      if (!isAudio) {
        message.error('只能上传音频文件!');
        return false;
      }
      if (!isLt5M) {
        message.error('音频大小不能超过 5MB!');
        return false;
      }

      // 创建音频URL
      const reader = new FileReader();
      reader.onload = (e) => {
        setBackgroundMusic(e.target.result);
      };
      reader.readAsDataURL(file);

      return false;
    }
  };

  const addAiPrompt = () => {
    const newPrompts = [...aiPrompts, { question: '', answer: '' }];
    setAiPrompts(newPrompts);
  };

  const removeAiPrompt = (index) => {
    const newPrompts = aiPrompts.filter((_, i) => i !== index);
    setAiPrompts(newPrompts);
  };

  const updateAiPrompt = (index, field, value) => {
    const newPrompts = [...aiPrompts];
    newPrompts[index][field] = value;
    setAiPrompts(newPrompts);
  };

  const previewAiAssistant = async () => {
    const values = form.getFieldsValue();
    if (!values.ai_welcome_message) {
      message.warning('请先设置AI欢迎消息');
      return;
    }

    try {
      // 模拟AI助手预览
      const mockMessage = `${values.ai_welcome_message}\n\n我是您的${aiPersonalityOptions.find(p => p.value === values.ai_personality)?.label || ''}AI助手，有什么问题可以问我！`;
      setPreviewMessage(mockMessage);
      setAiPreview(true);
    } catch (error) {
      console.error('预览AI助手失败:', error);
      message.error('预览失败');
    }
  };

  const getSelectedStoresDisplay = () => {
    if (selectedStores.length === 0) return '未选择门店';
    const selectedStoreNames = stores
      .filter(store => selectedStores.includes(store.id))
      .map(store => store.name);
    return selectedStoreNames.join(', ');
  };

  return (
    <Modal
      title={game ? '编辑营销游戏' : '创建营销游戏'}
      open={visible}
      onCancel={onCancel}
      footer={null}
      width={1000}
      destroyOnClose
      className="enhanced-game-form-modal"
    >
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
      >
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          {/* 基本信息 */}
          <TabPane tab="基本信息" key="basic">
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  label="游戏名称"
                  name="name"
                  rules={[{ required: true, message: '请输入游戏名称' }]}
                >
                  <Input placeholder="请输入游戏名称" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="游戏标题"
                  name="title"
                >
                  <Input placeholder="显示在页面上的标题" />
                </Form.Item>
              </Col>
            </Row>

            <Form.Item
              label="游戏描述"
              name="description"
            >
              <TextArea 
                rows={3} 
                placeholder="请输入游戏描述，介绍游戏玩法和奖品"
              />
            </Form.Item>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  label="游戏类型"
                  name="game_type"
                  rules={[{ required: true, message: '请选择游戏类型' }]}
                >
                  <Select placeholder="请选择游戏类型">
                    {gameTypeOptions.map(option => (
                      <Option key={option.value} value={option.value}>
                        {option.label}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="营销类型"
                  name="marketing_type"
                  rules={[{ required: true, message: '请选择营销类型' }]}
                >
                  <Select placeholder="请选择营销类型">
                    {marketingTypeOptions.map(option => (
                      <Option key={option.value} value={option.value}>
                        <Space>
                          <span>{option.icon}</span>
                          <span>{option.label}</span>
                          <Tag color={option.color} style={{ marginLeft: 8 }}>
                            {option.value}
                          </Tag>
                        </Space>
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
            </Row>

            <Form.Item
              label="活动时间"
              name="time_range"
              rules={[{ required: true, message: '请选择活动时间' }]}
            >
              <RangePicker 
                showTime
                style={{ width: '100%' }}
                placeholder={['开始时间', '结束时间']}
              />
            </Form.Item>

            <Row gutter={16}>
              <Col span={8}>
                <Form.Item
                  label="每人最大参与次数"
                  name="max_draws_per_user"
                  rules={[{ required: true, message: '请输入参与次数' }]}
                >
                  <InputNumber
                    min={1}
                    max={100}
                    style={{ width: '100%' }}
                    placeholder="每人最多可参与次数"
                  />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  label="最大参与人数"
                  name="max_participants"
                >
                  <InputNumber
                    min={0}
                    style={{ width: '100%' }}
                    placeholder="0表示无限制"
                  />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  label="最小消费金额"
                  name="min_consumption"
                >
                  <InputNumber
                    min={0}
                    precision={2}
                    style={{ width: '100%' }}
                    placeholder="参与所需最小消费"
                    addonAfter="元"
                  />
                </Form.Item>
              </Col>
            </Row>

            <Card title="封面图片" size="small">
              <Upload
                {...handleImageUpload('cover')}
                listType="picture-card"
                showUploadList={false}
              >
                {coverImage ? (
                  <img src={coverImage} alt="封面" style={{ width: '100%' }} />
                ) : (
                  <div>
                    <PlusOutlined />
                    <div style={{ marginTop: 8 }}>上传封面</div>
                  </div>
                )}
              </Upload>
              <Text type="secondary">建议尺寸：750x400px，支持JPG、PNG格式</Text>
            </Card>
          </TabPane>

          {/* 门店关联 */}
          <TabPane 
            tab={
              <span>
                <ShopOutlined />
                门店关联
              </span>
            } 
            key="stores"
          >
            <Alert
              message="门店关联设置"
              description="设置游戏适用的门店范围，可以选择全局游戏或指定特定门店。"
              type="info"
              showIcon
              style={{ marginBottom: 16 }}
            />

            <Form.Item
              label="全局游戏"
              name="is_global"
              valuePropName="checked"
            >
              <Switch 
                checkedChildren="全部门店"
                unCheckedChildren="指定门店"
                onChange={(checked) => {
                  if (checked) {
                    setSelectedStores([]);
                  }
                }}
              />
            </Form.Item>

            {!form.getFieldValue('is_global') && (
              <Form.Item label="选择门店">
                <Select
                  mode="multiple"
                  placeholder="请选择适用门店"
                  loading={loadingStores}
                  value={selectedStores}
                  onChange={setSelectedStores}
                  style={{ width: '100%' }}
                >
                  {stores.map(store => (
                    <Option key={store.id} value={store.id}>
                      <Space>
                        <ShopOutlined />
                        {store.name}
                        <Text type="secondary">({store.address})</Text>
                      </Space>
                    </Option>
                  ))}
                </Select>
                <div style={{ marginTop: 8 }}>
                  <Text type="secondary">
                    已选择：{getSelectedStoresDisplay()}
                  </Text>
                </div>
              </Form.Item>
            )}

            <Divider />

            <Title level={5}>地理位置限制</Title>
            <Form.Item
              label="启用位置限制"
              name="location_restriction"
              valuePropName="checked"
            >
              <Switch 
                checkedChildren="启用"
                unCheckedChildren="禁用"
              />
            </Form.Item>

            {form.getFieldValue('location_restriction') && (
              <>
                <Form.Item
                  label="允许的地理位置"
                  name="allowed_locations"
                >
                  <Select
                    mode="tags"
                    placeholder="输入允许的地理位置（如：北京市朝阳区）"
                    style={{ width: '100%' }}
                  />
                </Form.Item>
                <Form.Item
                  label="位置半径限制"
                  name="location_radius"
                >
                  <InputNumber
                    min={0}
                    max={10000}
                    style={{ width: '100%' }}
                    placeholder="限制半径（米）"
                    addonAfter="米"
                  />
                </Form.Item>
              </>
            )}
          </TabPane>

          {/* AI助手设置 */}
          <TabPane 
            tab={
              <span>
                <RobotOutlined />
                AI助手
              </span>
            } 
            key="ai"
          >
            <Alert
              message="AI助手功能"
              description="AI助手可以为用户提供游戏指导、回答常见问题，提升用户体验。"
              type="info"
              showIcon
              style={{ marginBottom: 16 }}
            />

            <Form.Item
              label="启用AI助手"
              name="ai_assistant_enabled"
              valuePropName="checked"
            >
              <Switch 
                checkedChildren="启用"
                unCheckedChildren="禁用"
              />
            </Form.Item>

            {form.getFieldValue('ai_assistant_enabled') && (
              <>
                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      label="AI助手性格"
                      name="ai_personality"
                    >
                      <Select placeholder="选择AI助手性格">
                        {aiPersonalityOptions.map(option => (
                          <Option key={option.value} value={option.value}>
                            <div>
                              <div>{option.label}</div>
                              <Text type="secondary" style={{ fontSize: '12px' }}>
                                {option.description}
                              </Text>
                            </div>
                          </Option>
                        ))}
                      </Select>
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item>
                      <Button 
                        type="primary" 
                        icon={<BulbOutlined />}
                        onClick={previewAiAssistant}
                        style={{ marginTop: 30 }}
                      >
                        预览AI助手
                      </Button>
                    </Form.Item>
                  </Col>
                </Row>

                <Form.Item
                  label="AI欢迎消息"
                  name="ai_welcome_message"
                >
                  <TextArea
                    rows={3}
                    placeholder="设置AI助手的欢迎消息，可使用变量如 {user_name}、{time_of_day} 等"
                  />
                </Form.Item>

                <div style={{ marginBottom: 16 }}>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 8 }}>
                    <Text strong>AI帮助提示</Text>
                    <Button 
                      type="dashed" 
                      icon={<PlusOutlined />}
                      onClick={addAiPrompt}
                    >
                      添加提示
                    </Button>
                  </div>
                  
                  {aiPrompts.map((prompt, index) => (
                    <Card 
                      key={index} 
                      size="small" 
                      style={{ marginBottom: 8 }}
                      extra={
                        <Button 
                          type="text" 
                          danger 
                          icon={<DeleteOutlined />}
                          onClick={() => removeAiPrompt(index)}
                        />
                      }
                    >
                      <Row gutter={8}>
                        <Col span={10}>
                          <Input
                            placeholder="常见问题"
                            value={prompt.question}
                            onChange={(e) => updateAiPrompt(index, 'question', e.target.value)}
                          />
                        </Col>
                        <Col span={14}>
                          <Input
                            placeholder="AI回答"
                            value={prompt.answer}
                            onChange={(e) => updateAiPrompt(index, 'answer', e.target.value)}
                          />
                        </Col>
                      </Row>
                    </Card>
                  ))}
                </div>
              </>
            )}
          </TabPane>

          {/* 微信分享设置 */}
          <TabPane 
            tab={
              <span>
                <WechatOutlined />
                微信分享
              </span>
            } 
            key="wechat"
          >
            <Form.Item
              label="启用微信分享"
              name="enable_wechat_share"
              valuePropName="checked"
            >
              <Switch 
                checkedChildren="启用"
                unCheckedChildren="禁用"
              />
            </Form.Item>

            {form.getFieldValue('enable_wechat_share') && (
              <>
                <Form.Item
                  label="分享标题"
                  name="wechat_share_title"
                >
                  <Input placeholder="微信分享时显示的标题" />
                </Form.Item>

                <Form.Item
                  label="分享描述"
                  name="wechat_share_description"
                >
                  <TextArea 
                    rows={2}
                    placeholder="微信分享时显示的描述"
                  />
                </Form.Item>

                <Form.Item label="分享图片">
                  <Upload
                    {...handleImageUpload('share')}
                    listType="picture-card"
                    showUploadList={false}
                  >
                    {shareImage ? (
                      <img src={shareImage} alt="分享图片" style={{ width: '100%' }} />
                    ) : (
                      <div>
                        <PlusOutlined />
                        <div style={{ marginTop: 8 }}>上传分享图片</div>
                      </div>
                    )}
                  </Upload>
                  <Text type="secondary">建议尺寸：500x500px，支持JPG、PNG格式</Text>
                </Form.Item>

                <Divider />

                <Title level={5}>社交功能</Title>
                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      label="启用社交分享"
                      name="enable_social_sharing"
                      valuePropName="checked"
                    >
                      <Switch />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      label="启用好友邀请"
                      name="enable_friend_invitation"
                      valuePropName="checked"
                    >
                      <Switch />
                    </Form.Item>
                  </Col>
                </Row>
              </>
            )}
          </TabPane>

          {/* 验证设置 */}
          <TabPane tab="验证设置" key="verification">
            <Alert
              message="参与验证"
              description="设置用户参与游戏时需要通过的验证条件。"
              type="info"
              showIcon
              style={{ marginBottom: 16 }}
            />

            <Row gutter={16}>
              <Col span={8}>
                <Form.Item
                  label="手机号验证"
                  name="require_phone"
                  valuePropName="checked"
                >
                  <Switch 
                    checkedChildren="必须"
                    unCheckedChildren="可选"
                  />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  label="小票验证"
                  name="require_receipt"
                  valuePropName="checked"
                >
                  <Switch 
                    checkedChildren="必须"
                    unCheckedChildren="可选"
                  />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  label="会员身份"
                  name="require_membership"
                  valuePropName="checked"
                >
                  <Switch 
                    checkedChildren="必须"
                    unCheckedChildren="可选"
                  />
                </Form.Item>
              </Col>
            </Row>
          </TabPane>

          {/* 外观设置 */}
          <TabPane 
            tab={
              <span>
                <BgColorsOutlined />
                外观设置
              </span>
            } 
            key="appearance"
          >
            <Form.Item
              label="使用增强版界面"
              name="use_enhanced_ui"
              valuePropName="checked"
            >
              <Switch 
                checkedChildren="增强版"
                unCheckedChildren="经典版"
              />
            </Form.Item>

            <Form.Item
              label="主题风格"
              name="theme_style"
            >
              <Select placeholder="选择主题风格">
                {themeOptions.map(theme => (
                  <Option key={theme.value} value={theme.value}>
                    <Space>
                      <div 
                        style={{ 
                          width: 16, 
                          height: 16, 
                          backgroundColor: theme.color,
                          borderRadius: 2 
                        }} 
                      />
                      {theme.label}
                    </Space>
                  </Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item label="背景音乐">
              <Upload
                {...handleAudioUpload}
                showUploadList={false}
              >
                <Button icon={<SoundOutlined />}>
                  {backgroundMusic ? '重新上传' : '上传背景音乐'}
                </Button>
              </Upload>
              {backgroundMusic && (
                <div style={{ marginTop: 8 }}>
                  <Text type="secondary">已上传背景音乐</Text>
                  <Button 
                    type="link" 
                    size="small"
                    onClick={() => setBackgroundMusic(null)}
                  >
                    删除
                  </Button>
                </div>
              )}
            </Form.Item>
          </TabPane>

          {/* 数据分析 */}
          <TabPane 
            tab={
              <span>
                <BarChartOutlined />
                数据分析
              </span>
            } 
            key="analytics"
          >
            <Alert
              message="数据分析功能"
              description="启用数据分析可以帮助您了解游戏效果，优化营销策略。"
              type="info"
              showIcon
              style={{ marginBottom: 16 }}
            />

            <Row gutter={16}>
              <Col span={8}>
                <Form.Item
                  label="启用数据分析"
                  name="enable_analytics"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  label="追踪用户行为"
                  name="track_user_behavior"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  label="生成报告"
                  name="generate_reports"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
              </Col>
            </Row>
          </TabPane>
        </Tabs>

        <Divider />

        <div style={{ textAlign: 'right' }}>
          <Space>
            <Button onClick={onCancel}>
              取消
            </Button>
            <Button 
              type="primary" 
              htmlType="submit"
              loading={loading}
              icon={<GiftOutlined />}
            >
              {game ? '更新游戏' : '创建游戏'}
            </Button>
          </Space>
        </div>
      </Form>

      {/* AI助手预览模态框 */}
      <Modal
        title="AI助手预览"
        open={aiPreview}
        onCancel={() => setAiPreview(false)}
        footer={[
          <Button key="close" onClick={() => setAiPreview(false)}>
            关闭
          </Button>
        ]}
      >
        <div style={{ 
          background: '#f6f8fa', 
          padding: 16, 
          borderRadius: 8,
          minHeight: 120
        }}>
          <div style={{ display: 'flex', alignItems: 'flex-start' }}>
            <Avatar 
              icon={<RobotOutlined />} 
              style={{ backgroundColor: '#1890ff', marginRight: 12 }} 
            />
            <div style={{ flex: 1 }}>
              <div style={{ 
                background: 'white', 
                padding: 12, 
                borderRadius: 8,
                boxShadow: '0 1px 3px rgba(0,0,0,0.1)'
              }}>
                <Paragraph style={{ margin: 0, whiteSpace: 'pre-wrap' }}>
                  {previewMessage}
                </Paragraph>
              </div>
            </div>
          </div>
        </div>
      </Modal>
    </Modal>
  );
};

export default EnhancedGameForm; 