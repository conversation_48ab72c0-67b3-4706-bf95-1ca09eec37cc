import api from '../httpClient';

/**
 * 租户插件市场API
 */
const plugins = {
  /**
   * 获取插件列表
   * @param {Object} params - 查询参数
   * @returns {Promise} - 插件列表
   */
  getPlugins: async (params = {}) => {
    return api.get('/tenant/plugins/market', { params });
  },

  /**
   * 获取插件详情
   * @param {string} pluginId - 插件ID
   * @returns {Promise} - 插件详情
   */
  getPluginDetail: async (pluginId) => {
    return api.get(`/tenant/plugins/market/${pluginId}`);
  },

  /**
   * 安装插件
   * @param {string} pluginId - 插件ID
   * @returns {Promise} - 安装结果
   */
  installPlugin: async (pluginId) => {
    return api.post(`/tenant/plugins/market/${pluginId}/install`);
  },

  /**
   * 卸载插件
   * @param {string} pluginId - 插件ID
   * @returns {Promise} - 卸载结果
   */
  uninstallPlugin: async (pluginId) => {
    return api.post(`/tenant/plugins/market/${pluginId}/uninstall`);
  },

  /**
   * 购买插件
   * @param {string} pluginId - 插件ID
   * @param {Object} data - 购买数据
   * @returns {Promise} - 购买结果
   */
  purchasePlugin: async (pluginId, data) => {
    return api.post(`/tenant/plugins/${pluginId}/purchase`, data);
  },

  /**
   * 获取插件订单列表
   * @param {Object} params - 查询参数
   * @returns {Promise} - 订单列表
   */
  getOrders: async (params = {}) => {
    return api.get('/tenant/plugins/orders', { params });
  },

  /**
   * 获取插件订单详情
   * @param {string} orderId - 订单ID
   * @returns {Promise} - 订单详情
   */
  getOrderDetail: async (orderId) => {
    return api.get(`/tenant/plugins/orders/${orderId}`);
  }
};

export default plugins;
