fastapi>=0.100.0
uvicorn[standard]==0.22.0
websockets>=11.0.3
pydantic>=2.0.0
pydantic-settings>=2.0.0
sqlalchemy==2.0.12
asyncpg==0.27.0
psycopg2-binary==2.9.6
alembic==1.10.4
python-jose==3.3.0
passlib==1.7.4
python-multipart==0.0.6
email-validator==2.0.0
aiofiles==23.1.0
redis==4.5.5
aio-pika==9.0.5
jinja2==3.1.2
pillow==9.5.0
python-dotenv==1.0.0
httpx==0.24.0
tenacity==8.2.2
bcrypt==4.0.1
pytz==2023.3
boto3==1.26.133
pyyaml==6.0
PyJWT>=2.0.0
docker
json-repair>=0.46.0
sse_starlette>=2.3.4
cryptography

# 向量数据库支持
pgvector

# 文档处理（使用pandas统一处理Excel，更轻量的PDF处理）
pandas>=2.0.0
openpyxl>=3.1.0  # pandas读取Excel文件的引擎
python-docx>=1.1.0
pypdf>=4.0.0

# 基础数学计算（用于向量相似度计算，比numpy+scipy更轻量）
# pgvector已提供向量运算，只需要基础数学函数
