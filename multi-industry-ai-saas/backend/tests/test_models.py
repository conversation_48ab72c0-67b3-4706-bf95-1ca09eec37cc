import pytest
import sys
import os
from uuid import uuid4
from sqlalchemy.orm import Session

# 添加项目根目录到 Python 路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 使用相对导入避免重复导入模型
from db.session import SessionLocal

@pytest.fixture
def db_session():
    """创建测试数据库会话"""
    session = SessionLocal()
    try:
        yield session
    finally:
        session.close()

def test_user_model(db_session: Session):
    """测试用户模型"""
    # 导入模型
    from models.user import User

    # 创建测试用户
    user = User(
        username="testuser",
        email="<EMAIL>",
        hashed_password="hashed_password",
        full_name="Test User",
        phone="13800138000",
        is_active=True,
        is_superuser=False
    )

    db_session.add(user)
    db_session.commit()
    db_session.refresh(user)

    # 验证用户创建成功
    assert user.id is not None
    assert user.username == "testuser"
    assert user.email == "<EMAIL>"
    assert user.hashed_password == "hashed_password"
    assert user.full_name == "Test User"
    assert user.phone == "13800138000"
    assert user.is_active is True
    assert user.is_superuser is False
    assert user.created_at is not None

    # 清理测试数据
    db_session.delete(user)
    db_session.commit()

def test_role_model(db_session: Session):
    """测试角色模型"""
    # 导入模型
    from models.role import Role

    # 创建测试项目
    project_id = uuid4()

    # 创建测试角色
    role = Role(
        project_id=project_id,
        name="测试角色",
        code="test_role",
        description="这是一个测试角色",
        permissions={"view": True, "create": True, "update": False, "delete": False},
        is_system_role=False
    )

    db_session.add(role)
    db_session.commit()
    db_session.refresh(role)

    # 验证角色创建成功
    assert role.id is not None
    assert role.project_id == project_id
    assert role.name == "测试角色"
    assert role.code == "test_role"
    assert role.description == "这是一个测试角色"
    assert role.permissions == {"view": True, "create": True, "update": False, "delete": False}
    assert role.is_system_role is False
    assert role.created_at is not None

    # 清理测试数据
    db_session.delete(role)
    db_session.commit()

def test_permission_model(db_session: Session):
    """测试权限模型"""
    # 导入模型
    from models.role import Permission

    # 创建测试权限
    permission = Permission(
        name="查看用户",
        code="user:view",
        description="查看用户列表和详情",
        module="user",
        action="view",
        resource="user",
        is_system_permission=True
    )

    db_session.add(permission)
    db_session.commit()
    db_session.refresh(permission)

    # 验证权限创建成功
    assert permission.id is not None
    assert permission.name == "查看用户"
    assert permission.code == "user:view"
    assert permission.description == "查看用户列表和详情"
    assert permission.module == "user"
    assert permission.action == "view"
    assert permission.resource == "user"
    assert permission.is_system_permission is True
    assert permission.created_at is not None

    # 清理测试数据
    db_session.delete(permission)
    db_session.commit()

def test_role_permission_model(db_session: Session):
    """测试角色权限关联模型"""
    # 导入模型
    from models.role import Role, Permission, RolePermission

    # 创建测试项目
    project_id = uuid4()

    # 创建测试角色
    role = Role(
        project_id=project_id,
        name="测试角色",
        code="test_role",
        description="这是一个测试角色",
        permissions={"view": True, "create": True, "update": False, "delete": False},
        is_system_role=False
    )

    # 创建测试权限
    permission = Permission(
        name="查看用户",
        code="user:view",
        description="查看用户列表和详情",
        module="user",
        action="view",
        resource="user",
        is_system_permission=True
    )

    db_session.add(role)
    db_session.add(permission)
    db_session.commit()
    db_session.refresh(role)
    db_session.refresh(permission)

    # 创建角色权限关联
    role_permission = RolePermission(
        role_id=role.id,
        permission_id=permission.id
    )

    db_session.add(role_permission)
    db_session.commit()
    db_session.refresh(role_permission)

    # 验证角色权限关联创建成功
    assert role_permission.id is not None
    assert role_permission.role_id == role.id
    assert role_permission.permission_id == permission.id
    assert role_permission.created_at is not None

    # 清理测试数据
    db_session.delete(role_permission)
    db_session.delete(role)
    db_session.delete(permission)
    db_session.commit()

def test_store_model(db_session: Session):
    """测试门店模型"""
    # 导入模型
    from models.store import Store, StoreCategory, StoreRegion

    # 创建测试项目
    project_id = uuid4()

    # 创建测试门店分类
    category = StoreCategory(
        project_id=project_id,
        name="旗舰店",
        code="flagship",
        description="面积大于500平米的大型门店"
    )

    # 创建测试门店区域
    region = StoreRegion(
        project_id=project_id,
        name="华北区",
        code="north",
        description="包括北京、天津、河北等地区"
    )

    db_session.add(category)
    db_session.add(region)
    db_session.commit()
    db_session.refresh(category)
    db_session.refresh(region)

    # 创建测试门店
    store = Store(
        project_id=project_id,
        category_id=category.id,
        region_id=region.id,
        name="北京西单店",
        code="BJ001",
        address="北京市西城区西单北大街120号",
        phone="010-12345678",
        manager="张三",
        latitude=39.9123,
        longitude=116.3789,
        business_hours="09:00-22:00",
        status="active",
        settings={"has_parking": True, "floor_area": 600}
    )

    db_session.add(store)
    db_session.commit()
    db_session.refresh(store)

    # 验证门店创建成功
    assert store.id is not None
    assert store.project_id == project_id
    assert store.category_id == category.id
    assert store.region_id == region.id
    assert store.name == "北京西单店"
    assert store.code == "BJ001"
    assert store.address == "北京市西城区西单北大街120号"
    assert store.phone == "010-12345678"
    assert store.manager == "张三"
    assert store.latitude == 39.9123
    assert store.longitude == 116.3789
    assert store.business_hours == "09:00-22:00"
    assert store.status == "active"
    assert store.settings == {"has_parking": True, "floor_area": 600}
    assert store.created_at is not None

    # 清理测试数据
    db_session.delete(store)
    db_session.delete(category)
    db_session.delete(region)
    db_session.commit()

def test_warehouse_model(db_session: Session):
    """测试仓库模型"""
    # 导入模型
    from models.warehouse import Warehouse, WarehouseSetting

    # 创建测试项目
    project_id = uuid4()

    # 创建测试仓库
    warehouse = Warehouse(
        project_id=project_id,
        name="北京总仓",
        code="WH001",
        type="central",
        address="北京市大兴区经济开发区科创十三街18号院",
        phone="010-87654321",
        manager="李四",
        capacity=10000,
        capacity_unit="sqm",
        business_hours="09:00-18:00",
        status="active",
        settings={"has_cold_storage": True, "floors": 3}
    )

    db_session.add(warehouse)
    db_session.commit()
    db_session.refresh(warehouse)

    # 验证仓库创建成功
    assert warehouse.id is not None
    assert warehouse.project_id == project_id
    assert warehouse.name == "北京总仓"
    assert warehouse.code == "WH001"
    assert warehouse.type == "central"
    assert warehouse.address == "北京市大兴区经济开发区科创十三街18号院"
    assert warehouse.phone == "010-87654321"
    assert warehouse.manager == "李四"
    assert warehouse.capacity == 10000
    assert warehouse.capacity_unit == "sqm"
    assert warehouse.business_hours == "09:00-18:00"
    assert warehouse.status == "active"
    assert warehouse.settings == {"has_cold_storage": True, "floors": 3}
    assert warehouse.created_at is not None

    # 创建测试仓库设置
    warehouse_setting = WarehouseSetting(
        project_id=project_id,
        warehouse_mode="multi",
        default_business_hours_start="09:00",
        default_business_hours_end="18:00",
        enable_front_warehouse=True,
        enable_cross_warehouse_transfer=True,
        inventory_warning_threshold=10,
        auto_replenishment=True,
        default_capacity_unit="sqm"
    )

    db_session.add(warehouse_setting)
    db_session.commit()
    db_session.refresh(warehouse_setting)

    # 验证仓库设置创建成功
    assert warehouse_setting.id is not None
    assert warehouse_setting.project_id == project_id
    assert warehouse_setting.warehouse_mode == "multi"
    assert warehouse_setting.default_business_hours_start == "09:00"
    assert warehouse_setting.default_business_hours_end == "18:00"
    assert warehouse_setting.enable_front_warehouse is True
    assert warehouse_setting.enable_cross_warehouse_transfer is True
    assert warehouse_setting.inventory_warning_threshold == 10
    assert warehouse_setting.auto_replenishment is True
    assert warehouse_setting.default_capacity_unit == "sqm"
    assert warehouse_setting.created_at is not None

    # 清理测试数据
    db_session.delete(warehouse)
    db_session.delete(warehouse_setting)
    db_session.commit()

def test_system_config_model(db_session: Session):
    """测试系统配置模型"""
    # 导入模型
    from models.system_config import SystemConfig

    # 创建测试项目
    project_id = uuid4()

    # 创建测试系统配置
    system_config = SystemConfig(
        project_id=project_id,
        config_type="theme",
        config_key="primaryColor",
        config_value={"value": "#1890ff"}
    )

    db_session.add(system_config)
    db_session.commit()
    db_session.refresh(system_config)

    # 验证系统配置创建成功
    assert system_config.id is not None
    assert system_config.project_id == project_id
    assert system_config.config_type == "theme"
    assert system_config.config_key == "primaryColor"
    assert system_config.config_value == {"value": "#1890ff"}
    assert system_config.created_at is not None

    # 清理测试数据
    db_session.delete(system_config)
    db_session.commit()
