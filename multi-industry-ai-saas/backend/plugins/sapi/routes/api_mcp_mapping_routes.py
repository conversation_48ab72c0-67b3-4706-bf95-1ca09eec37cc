#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
API到MCP映射路由

负责管理API到MCP工具的映射
"""

import json
import logging
import uuid
from datetime import datetime
from typing import Dict, Any, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Body, Query, Path
from sqlalchemy import and_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from pydantic import BaseModel

from db.database import get_db
from models.user import User
from core.auth import get_current_user

from ..models import SAPIEndpoint, MCPServer, APIMCPToolMapping
from .. import router

logger = logging.getLogger(__name__)

# 请求模型
class APIMCPMappingCreate(BaseModel):
    endpoint_id: uuid.UUID
    server_id: uuid.UUID
    tool_name: str
    parameter_mapping: Optional[Dict[str, str]] = {}
    response_mapping: Optional[Dict[str, str]] = {}

class APIMCPMappingUpdate(BaseModel):
    tool_name: Optional[str] = None
    parameter_mapping: Optional[Dict[str, str]] = None
    response_mapping: Optional[Dict[str, str]] = None

# 推送工具列表到MCP服务器
async def push_tools_to_mcp_server(server_id: str, current_user: User, db: AsyncSession):
    """
    推送工具列表到MCP服务器

    Args:
        server_id: 服务器ID
        current_user: 当前用户
        db: 数据库会话
    """
    try:
        # 获取服务器
        result = await db.execute(select(MCPServer).where(MCPServer.id == server_id))
        server = result.scalars().first()
        if not server:
            logger.warning(f"找不到MCP服务器: {server_id}")
            return

        # 获取服务器的工具列表
        from ..mcp_server_adapter import mcp_server_adapter

        # 注册服务器到适配器
        server_config = {
            "id": str(server.id),
            "name": server.name,
            "env": json.loads(server.env) if server.env else {},
            "install_dir": f"mcp_servers/{server_id}",
            "simulation": True
        }
        mcp_server_adapter.register_server(server_config)

        # 获取工具列表
        tools_response = await mcp_server_adapter.get_server_tools(str(server.id))

        logger.info(f"推送工具列表到MCP服务器: {tools_response}")

        return tools_response
    except Exception as e:
        logger.error(f"推送工具列表到MCP服务器失败: {str(e)}")
        return {"success": False, "message": f"推送工具列表失败: {str(e)}"}

# 获取API到MCP工具的映射列表
@router.get("/api-mcp-mappings", response_model=Dict[str, Any])
async def get_api_mcp_mappings(
    server_id: Optional[str] = None,
    endpoint_id: Optional[str] = None,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """获取API到MCP工具的映射列表"""
    try:
        # 构建查询
        query = select(APIMCPToolMapping)

        # 应用过滤条件
        if server_id:
            query = query.where(APIMCPToolMapping.server_id == server_id)
        if endpoint_id:
            query = query.where(APIMCPToolMapping.endpoint_id == endpoint_id)

        # 执行查询
        result = await db.execute(query)
        mappings = result.scalars().all()

        # 构建响应
        result = []
        for mapping in mappings:
            # 获取端点和服务器信息
            endpoint_result = await db.execute(select(SAPIEndpoint).where(SAPIEndpoint.id == mapping.endpoint_id))
            endpoint = endpoint_result.scalars().first()
            server_result = await db.execute(select(MCPServer).where(MCPServer.id == mapping.server_id))
            server = server_result.scalars().first()

            result.append({
                "id": str(mapping.id),
                "endpoint_id": str(mapping.endpoint_id),
                "server_id": str(mapping.server_id),
                "tool_name": mapping.tool_name,
                "parameter_mapping": mapping.parameter_mapping,
                "response_mapping": mapping.response_mapping,
                "created_at": mapping.created_at.isoformat() if mapping.created_at else None,
                "updated_at": mapping.updated_at.isoformat() if mapping.updated_at else None,
                "endpoint": {
                    "name": endpoint.name,
                    "endpoint_path": endpoint.endpoint_path,
                    "method": endpoint.method
                } if endpoint else None,
                "server": {
                    "name": server.name
                } if server else None
            })

        return {
            "success": True,
            "message": "获取API到MCP工具的映射列表成功",
            "data": result
        }
    except Exception as e:
        logger.error(f"获取API到MCP工具的映射列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取API到MCP工具的映射列表失败: {str(e)}")

# 获取API到MCP工具的映射详情
@router.get("/api-mcp-mappings/{mapping_id}", response_model=Dict[str, Any])
async def get_api_mcp_mapping(
    mapping_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """获取API到MCP工具的映射详情"""
    try:
        # 查询映射
        result = await db.execute(select(APIMCPToolMapping).where(APIMCPToolMapping.id == mapping_id))
        mapping = result.scalars().first()
        if not mapping:
            raise HTTPException(status_code=404, detail="API到MCP工具的映射不存在")

        # 获取端点和服务器信息
        endpoint_result = await db.execute(select(SAPIEndpoint).where(SAPIEndpoint.id == mapping.endpoint_id))
        endpoint = endpoint_result.scalars().first()
        server_result = await db.execute(select(MCPServer).where(MCPServer.id == mapping.server_id))
        server = server_result.scalars().first()

        # 构建响应
        result = {
            "id": str(mapping.id),
            "endpoint_id": str(mapping.endpoint_id),
            "server_id": str(mapping.server_id),
            "tool_name": mapping.tool_name,
            "parameter_mapping": mapping.parameter_mapping,
            "response_mapping": mapping.response_mapping,
            "created_at": mapping.created_at.isoformat() if mapping.created_at else None,
            "updated_at": mapping.updated_at.isoformat() if mapping.updated_at else None,
            "endpoint": {
                "name": endpoint.name,
                "endpoint_path": endpoint.endpoint_path,
                "method": endpoint.method,
                "parameters": endpoint.parameters
            } if endpoint else None,
            "server": {
                "name": server.name
            } if server else None
        }

        return {
            "success": True,
            "message": "获取API到MCP工具的映射详情成功",
            "data": result
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取API到MCP工具的映射详情失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取API到MCP工具的映射详情失败: {str(e)}")

# 创建API到MCP工具的映射
@router.post("/api-mcp-mappings", response_model=Dict[str, Any])
async def create_api_mcp_mapping(
    mapping_data: APIMCPMappingCreate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """创建API到MCP工具的映射"""
    try:
        # 检查端点是否存在
        result = await db.execute(select(SAPIEndpoint).where(SAPIEndpoint.id == mapping_data.endpoint_id))
        endpoint = result.scalars().first()
        if not endpoint:
            raise HTTPException(status_code=404, detail="API端点不存在")

        # 检查服务器是否存在
        result = await db.execute(select(MCPServer).where(MCPServer.id == mapping_data.server_id))
        server = result.scalars().first()
        if not server:
            raise HTTPException(status_code=404, detail="MCP服务器不存在")

        # 检查映射是否已存在
        result = await db.execute(
            select(APIMCPToolMapping).where(
                and_(
                    APIMCPToolMapping.endpoint_id == mapping_data.endpoint_id,
                    APIMCPToolMapping.server_id == mapping_data.server_id
                )
            )
        )
        existing_mapping = result.scalars().first()

        if existing_mapping:
            raise HTTPException(status_code=400, detail="API到MCP工具的映射已存在")

        # 创建新映射
        new_mapping = APIMCPToolMapping(
            id=uuid.uuid4(),
            endpoint_id=endpoint.id,
            server_id=server.id,
            tool_name=mapping_data.tool_name,
            parameter_mapping=mapping_data.parameter_mapping,
            response_mapping=mapping_data.response_mapping,
            created_by_id=current_user.id,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )

        db.add(new_mapping)
        await db.commit()
        await db.refresh(new_mapping)

        # 推送工具列表到MCP服务器
        await push_tools_to_mcp_server(str(server.id), current_user, db)

        return {
            "success": True,
            "message": "API到MCP工具的映射创建成功",
            "data": {
                "id": str(new_mapping.id),
                "endpoint_id": str(new_mapping.endpoint_id),
                "server_id": str(new_mapping.server_id),
                "tool_name": new_mapping.tool_name
            }
        }
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"创建API到MCP工具的映射失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"创建API到MCP工具的映射失败: {str(e)}")

# 更新API到MCP工具的映射
@router.put("/api-mcp-mappings/{mapping_id}", response_model=Dict[str, Any])
async def update_api_mcp_mapping(
    mapping_id: str,
    mapping_data: APIMCPMappingUpdate,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """更新API到MCP工具的映射"""
    try:
        # 查询映射
        result = await db.execute(select(APIMCPToolMapping).where(APIMCPToolMapping.id == mapping_id))
        mapping = result.scalars().first()
        if not mapping:
            raise HTTPException(status_code=404, detail="API到MCP工具的映射不存在")

        # 更新其他字段
        if mapping_data.tool_name is not None:
            mapping.tool_name = mapping_data.tool_name
        if mapping_data.parameter_mapping is not None:
            mapping.parameter_mapping = mapping_data.parameter_mapping
        if mapping_data.response_mapping is not None:
            mapping.response_mapping = mapping_data.response_mapping

        mapping.updated_at = datetime.now()

        await db.commit()
        await db.refresh(mapping)

        # 推送工具列表到MCP服务器
        await push_tools_to_mcp_server(str(mapping.server_id), current_user, db)

        return {
            "success": True,
            "message": "API到MCP工具的映射更新成功",
            "data": {
                "id": str(mapping.id),
                "endpoint_id": str(mapping.endpoint_id),
                "server_id": str(mapping.server_id),
                "tool_name": mapping.tool_name
            }
        }
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"更新API到MCP工具的映射失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"更新API到MCP工具的映射失败: {str(e)}")

# 删除API到MCP工具的映射
@router.delete("/api-mcp-mappings/{mapping_id}", response_model=Dict[str, Any])
async def delete_api_mcp_mapping(
    mapping_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """删除API到MCP工具的映射"""
    try:
        # 查询映射
        result = await db.execute(select(APIMCPToolMapping).where(APIMCPToolMapping.id == mapping_id))
        mapping = result.scalars().first()
        if not mapping:
            raise HTTPException(status_code=404, detail="API到MCP工具的映射不存在")

        # 保存服务器ID，用于后续推送工具列表
        server_id = str(mapping.server_id)

        # 删除映射
        await db.delete(mapping)
        await db.commit()

        # 推送工具列表到MCP服务器
        await push_tools_to_mcp_server(server_id, current_user, db)

        return {
            "success": True,
            "message": "API到MCP工具的映射删除成功"
        }
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"删除API到MCP工具的映射失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除API到MCP工具的映射失败: {str(e)}")

# 自动映射API到MCP工具
@router.post("/api-mcp-mappings/auto-map/{server_id}", response_model=Dict[str, Any])
async def auto_map_api_to_mcp(
    server_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """自动映射API到MCP工具"""
    try:
        # 检查服务器是否存在
        result = await db.execute(select(MCPServer).where(MCPServer.id == server_id))
        server = result.scalars().first()
        if not server:
            raise HTTPException(status_code=404, detail="MCP服务器不存在")

        # 获取所有API端点
        result = await db.execute(select(SAPIEndpoint).where(SAPIEndpoint.enabled == True))
        endpoints = result.scalars().all()

        # 获取已存在的映射
        result = await db.execute(
            select(APIMCPToolMapping).where(
                APIMCPToolMapping.server_id == server_id
            )
        )
        existing_mappings = result.scalars().all()

        # 提取已映射的端点ID
        existing_endpoint_ids = [str(mapping.endpoint_id) for mapping in existing_mappings]

        # 直接从端点创建工具和映射
        created_count = 0
        for endpoint in endpoints:
            # 跳过已映射的端点
            if str(endpoint.id) in existing_endpoint_ids:
                continue

            # 提取端点路径中的最后一部分作为工具名称
            path_parts = endpoint.endpoint_path.strip('/').split('/')
            # 如果路径以/api/开头，则去掉api部分
            if path_parts and path_parts[0] == 'api':
                path_parts = path_parts[1:]
            tool_name = path_parts[-1] if path_parts else endpoint.name.lower().replace(' ', '_')

            # 记录日志
            logger.info(f"从端点 {endpoint.endpoint_path} 创建工具: {tool_name}")

            # 自动生成参数映射
            parameter_mapping = {}
            if endpoint.parameters:
                for param_name, param_info in endpoint.parameters.items():
                    # 将API参数名称映射为相同的MCP工具参数名称
                    parameter_mapping[param_name] = param_name

            # 自动生成响应映射
            # 如果有响应模板，使用模板中的字段
            response_mapping = {}
            if endpoint.response_template:
                try:
                    template = json.loads(endpoint.response_template)
                    for field_name in template.keys():
                        # 将API响应字段映射为相同的MCP工具响应字段
                        response_mapping[field_name] = field_name
                except:
                    pass

            # 创建映射
            new_mapping = APIMCPToolMapping(
                id=uuid.uuid4(),
                endpoint_id=endpoint.id,
                server_id=server.id,
                tool_name=tool_name,
                parameter_mapping=parameter_mapping,
                response_mapping=response_mapping,
                created_by_id=current_user.id,
                created_at=datetime.now(),
                updated_at=datetime.now()
            )

            db.add(new_mapping)
            created_count += 1
            # 添加到已映射列表，避免重复映射
            existing_endpoint_ids.append(str(endpoint.id))

        await db.commit()

        # 如果成功创建了映射，尝试推送工具列表到MCP服务器
        if created_count > 0:
            await push_tools_to_mcp_server(server_id, current_user, db)

        return {
            "success": True,
            "message": f"自动映射API到MCP工具成功，创建了 {created_count} 个映射",
            "data": {
                "created_count": created_count
            }
        }
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"自动映射API到MCP工具失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"自动映射API到MCP工具失败: {str(e)}")
