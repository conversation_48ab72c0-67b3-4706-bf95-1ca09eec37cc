#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
AI 智能体客服嵌入代码生成器

提供嵌入代码生成和管理功能
"""

import logging
from typing import Dict, Any, Optional
from fastapi import APIRouter, Request, HTTPException, status, Query, Path
from fastapi.responses import HTMLResponse, JSONResponse
from pydantic import BaseModel

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter()

class EmbedConfigRequest(BaseModel):
    widget_id: str
    theme: str = "light"
    size: str = "medium"
    position: str = "bottom-right"
    auto_open: bool = False
    custom_domain: Optional[str] = None

@router.get(
    "/embed-code/{widget_id}",
    response_class=HTMLResponse,
    summary="生成嵌入代码",
    description="生成可嵌入网站的JavaScript代码"
)
async def generate_embed_code(
    request: Request,
    widget_id: str = Path(..., description="组件ID"),
    theme: str = Query("light", description="主题"),
    size: str = Query("medium", description="尺寸"),
    position: str = Query("bottom-right", description="位置"),
    domain: Optional[str] = Query(None, description="自定义域名")
):
    """生成嵌入代码"""
    try:
        base_url = domain or str(request.base_url).rstrip("/")
        
        embed_code = f"""
<!-- AI智能客服组件 -->
<script>
(function() {{
    var config = {{
        widgetId: '{widget_id}',
        theme: '{theme}',
        size: '{size}',
        position: '{position}',
        apiBaseUrl: '{base_url}'
    }};
    
    var script = document.createElement('script');
    script.src = '{base_url}/static/ai-customer-service/widget.js';
    script.onload = function() {{
        if (window.AICustomerService) {{
            window.AICustomerService.init(config);
        }}
    }};
    document.head.appendChild(script);
    
    var link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = '{base_url}/static/ai-customer-service/widget.css';
    document.head.appendChild(link);
}})();
</script>"""
        
        return HTMLResponse(content=embed_code, media_type="text/html")
        
    except Exception as e:
        logger.error(f"生成嵌入代码失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="生成嵌入代码失败"
        )

@router.post(
    "/embed-config",
    summary="保存嵌入配置",
    description="保存组件的嵌入配置"
)
async def save_embed_config(
    config_request: EmbedConfigRequest
):
    """保存嵌入配置"""
    try:
        # 这里可以保存配置到数据库
        # 暂时返回成功响应
        
        return JSONResponse(content={
            "success": True,
            "message": "嵌入配置保存成功",
            "config": config_request.dict()
        })
        
    except Exception as e:
        logger.error(f"保存嵌入配置失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="保存嵌入配置失败"
        )

@router.get(
    "/embed-preview/{widget_id}",
    response_class=HTMLResponse,
    summary="预览嵌入效果",
    description="预览组件在网页中的嵌入效果"
)
async def preview_embed(
    request: Request,
    widget_id: str = Path(..., description="组件ID"),
    theme: str = Query("light", description="主题"),
    size: str = Query("medium", description="尺寸")
):
    """预览嵌入效果"""
    try:
        base_url = str(request.base_url).rstrip("/")
        
        preview_html = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI客服组件预览</title>
    <style>
        body {{
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }}
        .preview-container {{
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 40px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }}
        .preview-header {{
            text-align: center;
            margin-bottom: 40px;
        }}
        .preview-content {{
            min-height: 500px;
            border: 2px dashed #d9d9d9;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #999;
            font-size: 18px;
        }}
    </style>
</head>
<body>
    <div class="preview-container">
        <div class="preview-header">
            <h1>AI智能客服组件预览</h1>
            <p>组件ID: {widget_id} | 主题: {theme} | 尺寸: {size}</p>
        </div>
        <div class="preview-content">
            <div>
                <p>这是您的网站内容区域</p>
                <p>AI客服组件将显示在页面右下角</p>
            </div>
        </div>
    </div>
    
    <!-- AI客服组件嵌入代码 -->
    <script>
    (function() {{
        var config = {{
            widgetId: '{widget_id}',
            theme: '{theme}',
            size: '{size}',
            position: 'bottom-right',
            apiBaseUrl: '{base_url}'
        }};
        
        // 模拟组件加载
        setTimeout(function() {{
            var widget = document.createElement('div');
            widget.style.cssText = `
                position: fixed;
                bottom: 20px;
                right: 20px;
                width: 60px;
                height: 60px;
                background: #1890ff;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                color: white;
                font-size: 24px;
                cursor: pointer;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 9999;
            `;
            widget.innerHTML = '💬';
            widget.title = 'AI智能客服';
            
            widget.onclick = function() {{
                alert('这是AI客服组件预览\\n实际使用时会打开聊天窗口');
            }};
            
            document.body.appendChild(widget);
        }}, 1000);
    }})();
    </script>
</body>
</html>"""
        
        return HTMLResponse(content=preview_html)
        
    except Exception as e:
        logger.error(f"预览嵌入效果失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="预览嵌入效果失败"
        )

@router.get(
    "/embed-stats/{widget_id}",
    summary="获取嵌入统计",
    description="获取组件的嵌入使用统计"
)
async def get_embed_stats(
    widget_id: str = Path(..., description="组件ID"),
    days: int = Query(7, ge=1, le=90, description="统计天数")
):
    """获取嵌入统计"""
    try:
        # 这里需要从数据库获取实际统计数据
        # 暂时返回模拟数据
        
        stats = {
            "widget_id": widget_id,
            "period_days": days,
            "total_embeds": 15,
            "active_embeds": 12,
            "total_sessions": 234,
            "total_messages": 1567,
            "domains": [
                {"domain": "example.com", "sessions": 89, "messages": 567},
                {"domain": "demo.com", "sessions": 67, "messages": 445},
                {"domain": "test.com", "sessions": 45, "messages": 234},
                {"domain": "sample.com", "sessions": 33, "messages": 321}
            ],
            "daily_stats": [
                {"date": "2025-01-01", "sessions": 32, "messages": 189},
                {"date": "2025-01-02", "sessions": 28, "messages": 156},
                {"date": "2025-01-03", "sessions": 35, "messages": 234},
                {"date": "2025-01-04", "sessions": 41, "messages": 267},
                {"date": "2025-01-05", "sessions": 38, "messages": 245},
                {"date": "2025-01-06", "sessions": 33, "messages": 198},
                {"date": "2025-01-07", "sessions": 27, "messages": 278}
            ]
        }
        
        return JSONResponse(content={
            "success": True,
            "data": stats
        })
        
    except Exception as e:
        logger.error(f"获取嵌入统计失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取嵌入统计失败"
        )
