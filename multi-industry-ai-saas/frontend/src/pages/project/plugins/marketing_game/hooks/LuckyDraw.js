import React, { useState, useEffect, useRef } from 'react';
import { useParams } from 'react-router-dom';
import {
  Card,
  Button,
  message,
  App,
  Spin,
  Typography,
  Row,
  Col,
  Modal,
  Image,
  Divider,
  List,
  Avatar,
  Tag,
  Result,
  Space,
  Input,
  Form,
  Statistic,
  Upload,
  Tooltip,
  Switch
} from 'antd';
import {
  GiftOutlined,
  TrophyOutlined,
  UserOutlined,
  HistoryOutlined,
  ShareAltOutlined,
  QrcodeOutlined,
  SmileOutlined,
  MehOutlined,
  LoadingOutlined,
  SoundOutlined,
  PictureOutlined,
  UploadOutlined
} from '@ant-design/icons';
import apiService from '../service/api/marketingGame';
import './LuckyDraw.css';

const { Title, Paragraph, Text } = Typography;

// 音效资源
const AUDIO = {
  SPIN: '/assets/audio/wheel-spin.mp3',
  WIN: '/assets/audio/win.mp3',
  LOSE: '/assets/audio/lose.mp3',
  CLICK: '/assets/audio/click.mp3'
};

// 默认背景图片
const DEFAULT_BACKGROUNDS = [
  '/assets/images/backgrounds/bg1.jpg',
  '/assets/images/backgrounds/bg2.jpg',
  '/assets/images/backgrounds/bg3.jpg',
  '/assets/images/backgrounds/bg4.jpg',
  '/assets/images/backgrounds/bg5.jpg'
];

// 默认奖品图标
const DEFAULT_PRIZE_ICONS = {
  first: '/assets/images/prizes/first-prize.png',
  second: '/assets/images/prizes/second-prize.png',
  third: '/assets/images/prizes/third-prize.png',
  lucky: '/assets/images/prizes/lucky-prize.png',
  participation: '/assets/images/prizes/participation-prize.png',
  none: '/assets/images/prizes/no-prize.png'
};

// 奖品颜色方案（更加和谐的配色）
const PRIZE_COLORS = [
  '#FF9800', // 橙色
  '#2196F3', // 蓝色
  '#4CAF50', // 绿色
  '#E91E63', // 粉色
  '#673AB7', // 紫色
  '#00BCD4', // 青色
  '#FFC107', // 黄色
  '#607D8B'  // 蓝灰色
];

// 调整颜色亮度的辅助函数
const adjustColor = (color, amount) => {
  return '#' + color.replace(/^#/, '').replace(/../g, color =>
    ('0' + Math.min(255, Math.max(0, parseInt(color, 16) + amount)).toString(16)).substr(-2)
  );
};

// 判断颜色是否为亮色
const isLightColor = (color) => {
  const hex = color.replace('#', '');
  const r = parseInt(hex.substr(0, 2), 16);
  const g = parseInt(hex.substr(2, 2), 16);
  const b = parseInt(hex.substr(4, 2), 16);
  const brightness = ((r * 299) + (g * 587) + (b * 114)) / 1000;
  return brightness > 155;
};

// 改进音频播放函数，添加错误捕获
const playAudio = (audioPath, volume = 0.5) => {
  try {
    const audio = new Audio(audioPath);
    audio.volume = volume;
    // 静默处理音频播放失败，避免中断主流程
    audio.play().catch(error => {
      console.warn('音频播放失败:', error);
      // 失败不中断，继续返回音频对象
    });
    return audio;
  } catch (error) {
    console.warn('创建音频对象失败:', error);
    // 返回一个模拟对象，使调用方代码不出错
    return { pause: () => {} };
  }
};

const LuckyDraw = () => {
  const { gameId } = useParams();
  const [game, setGame] = useState(null);
  const [prizes, setPrizes] = useState([]);
  const [records, setRecords] = useState([]);
  const [loading, setLoading] = useState(true);
  const [spinning, setSpinning] = useState(false);
  const [showRules, setShowRules] = useState(false);
  const [showRecords, setShowRecords] = useState(false);
  const [showPrizes, setShowPrizes] = useState(false);
  const [showReceiptUpload, setShowReceiptUpload] = useState(false);
  const [receiptFile, setReceiptFile] = useState(null);
  const [uploadLoading, setUploadLoading] = useState(false);
  const [winningPrize, setWinningPrize] = useState(null);
  const [showResult, setShowResult] = useState(false);
  const [soundEnabled, setSoundEnabled] = useState(true);
  const [form] = Form.useForm();
  const { message: antMessage, notification } = App.useApp();

  const wheelRef = useRef(null);
  const spinningTimeRef = useRef(null);
  const spinAudioRef = useRef(null);

  // 主题样式配置
  const themeStyles = {
    classic: {
      colors: PRIZE_COLORS,
      borderColor: '#ff5722',
      shadowColor: '#ffeb3b',
      centerBgColor: 'white',
      fontColor: '#333',
      buttonBgColor: '#ff5722'
    },
    apple: {
      colors: ['#5AC8FA', '#34C759', '#FF9500', '#FF2D55', '#AF52DE', '#007AFF', '#5856D6', '#FF3B30'],
      borderColor: '#007AFF',
      shadowColor: '#5AC8FA',
      centerBgColor: 'white',
      fontColor: '#333',
      buttonBgColor: '#007AFF'
    },
    cartoon: {
      colors: ['#FF6B6B', '#4ECDC4', '#FFE66D', '#6699CC', '#FF9A76', '#88D8B0', '#FF6F91', '#C5D86D'],
      borderColor: '#FF6B6B',
      shadowColor: '#FFE66D',
      centerBgColor: 'white',
      fontColor: '#333',
      buttonBgColor: '#FF6B6B'
    },
    business: {
      colors: ['#2C3E50', '#3498DB', '#1ABC9C', '#E74C3C', '#9B59B6', '#F1C40F', '#16A085', '#C0392B'],
      borderColor: '#34495E',
      shadowColor: '#BDC3C7',
      centerBgColor: '#ECF0F1',
      fontColor: '#2C3E50',
      buttonBgColor: '#3498DB'
    },
    ghibli: {
      colors: ['#1A7A89', '#D4A017', '#7CAE7A', '#E86A92', '#7C90A0', '#FFB067', '#9B8BB4', '#5B8E7D'],
      borderColor: '#1A7A89',
      shadowColor: '#D4A017',
      centerBgColor: 'white',
      fontColor: '#333',
      buttonBgColor: '#1A7A89'
    }
  };

  // 确保用户ID存在
  useEffect(() => {
    if (!localStorage.getItem('user_id')) {
      const tempUserId = `user_${Date.now()}_${Math.floor(Math.random() * 100000)}`;
      localStorage.setItem('user_id', tempUserId);
      console.log('创建临时用户ID:', tempUserId);
    }

    if (!localStorage.getItem('device_id')) {
      const tempDeviceId = `device_${Date.now()}_${Math.floor(Math.random() * 100000)}`;
      localStorage.setItem('device_id', tempDeviceId);
    }
  }, []);

  // 获取当前主题样式
  const getCurrentTheme = (themeStyle = 'classic') => {
    return themeStyles[themeStyle] || themeStyles.classic;
  };

  // 将fetchGameData提升到组件作用域
  const fetchGameData = React.useCallback(async () => {
    try {
      setLoading(true);

      // 获取游戏详情
      const gameResponse = await apiService.getGameDetail(gameId);
      if (!gameResponse.success) {
        antMessage.error(gameResponse.message || '获取游戏详情失败');
        setLoading(false);
        return;
      }

      // 确保game.remaining_draws有默认值
      const gameData = {
        ...gameResponse.data,
        remaining_draws: gameResponse.data.remaining_draws ||
                         gameResponse.data.max_draws_per_user ||
                         0,
        // 添加默认配置
        settings: {
          ...gameResponse.data.settings,
          enable_receipt_verification:
            gameResponse.data.settings?.enable_receipt_verification !== undefined
              ? gameResponse.data.settings.enable_receipt_verification
              : gameResponse.data.min_consumption > 0,
          theme_style: gameResponse.data.settings?.theme_style || 'classic'
        }
      };

      setGame(gameData);

      // 获取奖品列表
      const prizesResponse = await apiService.getPrizes(gameId);
      if (!prizesResponse.success) {
        antMessage.error(prizesResponse.message || '获取奖品列表失败');
        setLoading(false);
        return;
      }

      // 确保至少有一个默认奖品（未中奖）
      let prizesList = prizesResponse.data || [];
      if (!prizesList.some(p => p.is_default)) {
        prizesList.push({
          id: 'default',
          name: '谢谢参与',
          description: '下次再来',
          image_url: DEFAULT_PRIZE_ICONS.none,
          probability: 0,
          is_default: true,
          remaining: 999999
        });
      }

      setPrizes(prizesList);

      // 设置转盘分割线变量
      if (wheelRef.current) {
        wheelRef.current.style.setProperty('--prize-count', prizesList.length);
      }

      // 获取最近中奖记录
      const recordsResponse = await apiService.getRecords(gameId, { page: 1, page_size: 10 });
      if (recordsResponse.success) {
        setRecords(recordsResponse.data?.items || []);
      }

      setLoading(false);
    } catch (error) {
      console.error('获取游戏数据失败:', error);
      antMessage.error('获取游戏数据失败');
      setLoading(false);
    }
  }, [gameId, antMessage]);

  useEffect(() => {
    fetchGameData();
  }, [fetchGameData]);

  // 处理抽奖
  const handleDraw = () => {
    if (soundEnabled) {
      playAudio(AUDIO.CLICK, 0.3);
    }

    if (!game.remaining_draws || game.remaining_draws <= 0) {
      antMessage.warning('您的抽奖次数已用完');
      return;
    }

    // 检查是否需要小票验证
    const needsReceipt = game.min_consumption > 0 &&
                        game.settings?.enable_receipt_verification !== false &&
                        !game.receipt_verified;

    if (needsReceipt) {
      setShowReceiptUpload(true);
      return;
    }

    startSpinningWheel();
  };

  // 修复startSpinningWheel函数中的API调用
  const startSpinningWheel = async () => {
    if (spinning) return;

    setSpinning(true);

    try {
      // 播放旋转音效
      if (soundEnabled) {
        spinAudioRef.current = playAudio(AUDIO.SPIN, 0.4);
      }

      // 确保转盘在初始位置
      if (wheelRef.current) {
        wheelRef.current.style.transition = 'none';
        wheelRef.current.style.transform = 'rotate(0deg)';

        // 强制重绘以确保样式应用
        void wheelRef.current.offsetWidth;

        // 恢复动画
        wheelRef.current.style.transition = 'transform 4s cubic-bezier(0.17, 0.67, 0.83, 0.67)';
      }

      // 确保用户ID存在
      const userId = localStorage.getItem('user_id') || `user_${Date.now()}_${Math.floor(Math.random() * 100000)}`;
      if (!localStorage.getItem('user_id')) {
        localStorage.setItem('user_id', userId);
      }

      const deviceId = localStorage.getItem('device_id') || `device_${Date.now()}_${Math.floor(Math.random() * 100000)}`;
      if (!localStorage.getItem('device_id')) {
        localStorage.setItem('device_id', deviceId);
      }

      // 确保使用整数类型的participant_id
      // 生成一个基于时间戳的唯一整数ID
      const numericUserId = parseInt(Date.now().toString().slice(-8)) + Math.floor(Math.random() * 1000);

      // 先创建参与者记录
      const participantResponse = await apiService.createParticipant(gameId, {
        name: userId || '匿名用户',
        phone: localStorage.getItem('user_phone') || '',
        is_fixed_winner: false
      });

      if (!participantResponse.success) {
        throw new Error(participantResponse.message || '创建参与者失败');
      }

      // 使用返回的参与者ID进行抽奖
      const participantId = participantResponse.data.id;

      // 调用API进行抽奖，确保提供participant_id
      const response = await apiService.createRecord(gameId, {
        participant_id: participantId, // 使用创建的参与者ID
        device_id: deviceId,
        timestamp: Date.now()
      });

      // 检查响应和响应数据
      if (!response || !response.success || !response.data) {
        throw new Error(response?.message || '抽奖失败，请重试');
      }

      const result = response.data;

      // 确保结果包含必要的 prize_id
      if (!result || !result.prize_id) {
        throw new Error('抽奖结果异常，请重试');
      }

      // 找到中奖项索引
      const winIndex = prizes.findIndex(prize => prize.id === result.prize_id);

      if (winIndex === -1) {
        // 如果找不到匹配的奖品，使用默认奖品（通常是未中奖项）
        console.warn('未找到匹配的奖品ID:', result.prize_id);
        // 尝试找到默认奖品
        const defaultPrizeIndex = prizes.findIndex(prize => prize.is_default);
        if (defaultPrizeIndex === -1) {
          throw new Error('未找到匹配的奖品');
        }
        // 使用默认奖品
        result.prize_id = prizes[defaultPrizeIndex].id;
      }

      // 重新查找索引（防止上面的逻辑修改了 prize_id）
      const finalWinIndex = prizes.findIndex(prize => prize.id === result.prize_id);

      // 计算旋转角度
      // 要旋转到的目标扇形区域中心的角度
      const segmentAngle = 360 / prizes.length;
      const targetAngle = segmentAngle * finalWinIndex;

      // 计算目标角度（多转几圈再到目标位置）
      // 添加720度（2圈）+ 目标角度 + 随机偏移使每次看起来不同
      const randomOffset = Math.random() * (segmentAngle * 0.5);
      const finalAngle = 720 + 360 - targetAngle - segmentAngle / 2 + randomOffset;

      // 设置转盘旋转
      if (wheelRef.current) {
        wheelRef.current.style.transform = `rotate(${finalAngle}deg)`;
      }

      // 等待动画完成后显示结果
      spinningTimeRef.current = setTimeout(() => {
        if (soundEnabled && spinAudioRef.current) {
          spinAudioRef.current.pause();
        }

        if (soundEnabled) {
          // 使用适当的奖品判断条件
          const isWinning = result.is_winning ||
                           !prizes.find(p => p.id === result.prize_id)?.is_default;
          playAudio(isWinning ? AUDIO.WIN : AUDIO.LOSE, 0.4);
        }

        setWinningPrize(result);
        setSpinning(false);
        setShowResult(true);

        // 更新游戏数据（剩余次数等）
        fetchGameData();
      }, 4200); // 略大于动画时间以确保动画完成
    } catch (error) {
      console.error('抽奖失败:', error);
      antMessage.error(error.message || '抽奖失败，请重试');
      setSpinning(false);
    }
  };

  // 处理小票上传
  const handleReceiptUpload = async (values) => {
    if (!receiptFile) {
      antMessage.error('请上传小票图片');
      return;
    }

    try {
      setUploadLoading(true);

      // 在实际应用中，这里应该调用API上传小票
      const formData = new FormData();
      formData.append('receipt', receiptFile);
      formData.append('amount', values.amount);
      formData.append('phone', values.phone);
      const response = await apiService.recognizeReceipt(gameId, formData);

      if (response.success) {
        setUploadLoading(false);
        setShowReceiptUpload(false);

        // 更新游戏状态
        setGame(prev => ({
          ...prev,
          receipt_verified: true
        }));

        antMessage.success('小票验证成功，可以开始抽奖了！');

        // 自动开始抽奖
        startSpinningWheel();
      } else {
        throw new Error(response.message || '小票验证失败');
      }
    } catch (error) {
      console.error('小票上传失败:', error);
      antMessage.error(error.message || '小票验证失败，请重试');
      setUploadLoading(false);
    }
  };

  // 处理文件上传
  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      setReceiptFile(file);
    }
  };

  // 跳过小票验证（仅开发环境使用）
  const skipReceiptVerification = () => {
    setShowReceiptUpload(false);
    setGame(prev => ({
      ...prev,
      receipt_verified: true
    }));
    startSpinningWheel();
  };

  // 清除定时器
  useEffect(() => {
    return () => {
      if (spinningTimeRef.current) {
        clearTimeout(spinningTimeRef.current);
      }

      if (spinAudioRef.current) {
        spinAudioRef.current.pause();
      }
    };
  }, []);

  if (loading) {
    return (
      <div className="lucky-draw-loading">
        <Spin size="large" indicator={<LoadingOutlined className="loading-icon" spin />} />
        <p className="loading-text">精彩抽奖即将开始...</p>
      </div>
    );
  }

  // 获取背景图片
  const backgroundImage = game.background_image || DEFAULT_BACKGROUNDS[0];

  // 格式化剩余抽奖次数显示
  const remainingDraws = game.remaining_draws !== undefined ? game.remaining_draws : '0';

  return (
    <div className="lucky-draw-container" style={{ backgroundImage: `url(${backgroundImage})` }}>
      <div className="lucky-draw-header">
        <img
          src={game.logo_image || '/assets/images/logo-default.png'}
          alt="Logo"
          className="game-logo"
        />
        <Title level={2} className="game-title">{game.name || '幸运抽奖'}</Title>
        <Paragraph className="game-description">{game.description || '参与抽奖赢大奖'}</Paragraph>
      </div>

      <div className="lucky-draw-content">
        <div className="settings-bar">
          <div className="settings-item">
            <span className="settings-label">音效</span>
            <Switch
              checked={soundEnabled}
              onChange={setSoundEnabled}
              size="small"
              checkedChildren={<SoundOutlined />}
              unCheckedChildren={<SoundOutlined />}
            />
          </div>
        </div>

        <div className="wheel-container">
          {/* 获取当前主题 */}
          {(() => {
            const themeStyle = game?.settings?.theme_style || 'classic';
            const currentTheme = getCurrentTheme(themeStyle);

            return (
              <>
                <div
                  className="wheel-pointer"
                  style={{ backgroundColor: currentTheme.borderColor }}
                ></div>
                <div
                  className="wheel"
                  ref={wheelRef}
                  style={{
                    '--prize-count': prizes.length,
                    borderColor: currentTheme.borderColor,
                    boxShadow: `0 0 0 12px ${currentTheme.shadowColor}, 0 0 40px rgba(0, 0, 0, 0.3)`
                  }}
                >
                  {prizes.map((prize, index) => {
                    const angle = (360 / prizes.length) * index;
                    // 确保所有扇形都有颜色，不会出现白色扇形
                    const backgroundColor = currentTheme.colors[index % currentTheme.colors.length];

                    return (
                      <div
                        key={prize.id}
                        className="wheel-item"
                        style={{
                          transform: `rotate(${angle}deg)`,
                          backgroundColor,
                          // 添加一个渐变效果，使颜色更加丰富
                          background: `radial-gradient(circle at bottom right, ${backgroundColor}, ${adjustColor(backgroundColor, 20)})`
                        }}
                      >
                        <div
                          className="wheel-item-content"
                          style={{
                            // 根据背景色调整文字颜色以确保可读性
                            color: isLightColor(backgroundColor) ? '#333' : '#fff'
                          }}
                        >
                          <img
                            src={prize.image_url || DEFAULT_PRIZE_ICONS.none}
                            alt={prize.name}
                            className="prize-image"
                          />
                          <div className="prize-name">{prize.name}</div>
                        </div>
                      </div>
                    );
                  })}
                </div>

                <div
                  className="wheel-center"
                  style={{ backgroundColor: currentTheme.centerBgColor }}
                >
                  <Button
                    type="primary"
                    className="draw-button"
                    onClick={handleDraw}
                    disabled={spinning || !remainingDraws || remainingDraws <= 0}
                    loading={spinning}
                    style={{
                      backgroundColor: currentTheme.buttonBgColor,
                      borderColor: currentTheme.buttonBgColor
                    }}
                  >
                    {spinning ? 'GO' : 'GO'}
                  </Button>
                </div>
              </>
            );
          })()}
        </div>

        <div className="remaining-draws">
          剩余抽奖次数: <span className="remaining-count">{remainingDraws}</span>
        </div>

        <div className="action-buttons">
          <Button
            icon={<TrophyOutlined />}
            onClick={() => setShowPrizes(true)}
            className="action-button"
          >
            奖品设置
          </Button>
          <Button
            icon={<HistoryOutlined />}
            onClick={() => setShowRecords(true)}
            className="action-button"
          >
            中奖记录
          </Button>
          <Button
            icon={<UserOutlined />}
            onClick={() => setShowRules(true)}
            className="action-button"
          >
            活动规则
          </Button>
        </div>
      </div>

      <div className="lucky-draw-footer">
        <p>© {new Date().getFullYear()} {game.name || '营销游戏'} - 版权所有</p>
        <div className="footer-links">
          <a href="#" className="footer-link">隐私政策</a>
          <a href="#" className="footer-link">使用条款</a>
          <a href="#" className="footer-link">联系我们</a>
        </div>
      </div>

      {/* 活动规则弹窗 */}
      <Modal
        title="活动规则"
        open={showRules}
        onCancel={() => setShowRules(false)}
        footer={null}
      >
        <Paragraph>
          <pre style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-word' }}>
            {game.rules || '暂无活动规则'}
          </pre>
        </Paragraph>
        <Divider />
        <Row gutter={16}>
          <Col span={12}>
            <Statistic title="最低消费" value={`¥${game.min_consumption || 0}`} />
          </Col>
          <Col span={12}>
            <Statistic title="每人抽奖次数" value={game.max_draws_per_user || 0} />
          </Col>
        </Row>
      </Modal>

      {/* 奖品设置弹窗 */}
      <Modal
        title="奖品设置"
        open={showPrizes}
        onCancel={() => setShowPrizes(false)}
        footer={null}
        width={700}
      >
        <List
          itemLayout="horizontal"
          dataSource={prizes.filter(prize => !prize.is_default)}
          renderItem={item => (
            <List.Item>
              <List.Item.Meta
                avatar={<Avatar src={item.image_url || DEFAULT_PRIZE_ICONS.none} size={64} />}
                title={item.name}
                description={item.description}
              />
              <div>
                <Tag color="blue">概率: {item.probability}%</Tag>
                <Tag color="green">剩余: {item.remaining}</Tag>
              </div>
            </List.Item>
          )}
        />
      </Modal>

      {/* 中奖记录弹窗 */}
      <Modal
        title="中奖记录"
        open={showRecords}
        onCancel={() => setShowRecords(false)}
        footer={null}
      >
        {records.length > 0 ? (
          <List
            itemLayout="horizontal"
            dataSource={records}
            renderItem={item => (
              <List.Item>
                <List.Item.Meta
                  avatar={<Avatar icon={<UserOutlined />} />}
                  title={item.user_name}
                  description={`获得 ${item.prize_name}`}
                />
                <div>{item.created_at}</div>
              </List.Item>
            )}
          />
        ) : (
          <div style={{ textAlign: 'center', padding: '20px 0' }}>
            <p>暂无中奖记录</p>
          </div>
        )}
      </Modal>

      {/* 小票上传弹窗 */}
      <Modal
        title="小票验证"
        open={showReceiptUpload}
        onCancel={() => setShowReceiptUpload(false)}
        footer={null}
      >
        <Form form={form} onFinish={handleReceiptUpload} layout="vertical">
          <Form.Item
            name="receipt"
            label="上传小票"
            rules={[{ required: true, message: '请上传小票图片' }]}
          >
            <Input
              type="file"
              accept="image/*"
              onChange={handleFileChange}
            />
          </Form.Item>

          <Form.Item
            name="amount"
            label="消费金额"
            rules={[{ required: true, message: '请输入消费金额' }]}
          >
            <Input prefix="¥" placeholder="请输入消费金额" />
          </Form.Item>

          <Form.Item
            name="phone"
            label="手机号码"
            rules={[{ required: true, message: '请输入手机号码' }]}
          >
            <Input placeholder="请输入手机号码" />
          </Form.Item>

          <Row gutter={16}>
            <Col span={process.env.NODE_ENV === 'development' ? 12 : 24}>
              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={uploadLoading}
                  block
                >
                  验证并抽奖
                </Button>
              </Form.Item>
            </Col>

            {/* 仅在开发环境显示跳过按钮 */}
            {process.env.NODE_ENV === 'development' && (
              <Col span={12}>
                <Form.Item>
                  <Button onClick={skipReceiptVerification} block>
                    跳过验证
                  </Button>
                </Form.Item>
              </Col>
            )}
          </Row>
        </Form>
      </Modal>

      {/* 抽奖结果弹窗 */}
      <Modal
        title="抽奖结果"
        open={showResult}
        onCancel={() => setShowResult(false)}
        footer={[
          <Button key="back" onClick={() => setShowResult(false)}>
            关闭
          </Button>,
          <Button
            key="share"
            type="primary"
            icon={<ShareAltOutlined />}
            onClick={() => {
              antMessage.success('分享成功');
              setShowResult(false);
            }}
          >
            分享
          </Button>
        ]}
        width={500}
      >
        {winningPrize && (
          <Result
            icon={winningPrize.is_default ? <MehOutlined /> : <SmileOutlined />}
            status={winningPrize.is_default ? 'warning' : 'success'}
            title={winningPrize.is_default ? '很遗憾，未中奖' : '恭喜您，中奖了！'}
            subTitle={winningPrize.description}
            extra={
              !winningPrize.is_default && (
                <div className="prize-result">
                  <img
                    src={winningPrize.image_url || DEFAULT_PRIZE_ICONS.none}
                    alt={winningPrize.name}
                    className="prize-result-image"
                  />
                  <div className="prize-result-info">
                    <Title level={4} className="prize-result-title">{winningPrize.name}</Title>
                    <Paragraph className="prize-result-description">{winningPrize.description}</Paragraph>
                  </div>
                </div>
              )
            }
          />
        )}

        <Divider />

        <Space direction="vertical" style={{ width: '100%', textAlign: 'center' }}>
          <Text>剩余抽奖次数: {game.remaining_draws > 0 ? game.remaining_draws : 0}</Text>
          {(game.remaining_draws > 0) && (
            <Button
              type="primary"
              onClick={() => {
                setShowResult(false);
                setTimeout(() => {
                  startSpinningWheel();
                }, 500);
              }}
            >
              继续抽奖
            </Button>
          )}
        </Space>
      </Modal>
    </div>
  );
};

// 包装LuckyDraw组件，确保App context可用
const LuckyDrawWithApp = () => (
  <App>
    <LuckyDraw />
  </App>
);

export default LuckyDrawWithApp;
