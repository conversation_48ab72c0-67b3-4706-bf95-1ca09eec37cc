#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
全渠道运营助手 - 通知服务
"""

import logging
import asyncio
from typing import Dict, List, Optional, Any
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_

from ..models.operation_log import NotificationLog

logger = logging.getLogger(__name__)


class NotificationService:
    """通知服务 - 集成项目通知系统"""
    
    def __init__(self):
        self.notification_channels = {
            'system': '系统通知',
            'email': '邮件通知',
            'sms': '短信通知',
            'webhook': 'Webhook通知',
            'dingtalk': '钉钉通知',
            'wechat': '微信通知'
        }
    
    async def send_notification(self,
                                db: AsyncSession,
                                project_id: str,
                                notification_type: str,
                                title: str,
                                message: str,
                                source_type: str,
                                source_id: Optional[str] = None,
                                recipient_users: Optional[List[str]] = None,
                                channels: Optional[List[str]] = None) -> bool:
        """发送通知"""
        try:
            # 创建通知记录
            notification = NotificationLog(
                project_id=project_id,
                notification_type=notification_type,
                title=title,
                message=message,
                source_type=source_type,
                source_id=source_id,
                notification_channels=channels or ['system'],
                recipient_users=recipient_users or [],
                send_status="pending"
            )
            
            db.add(notification)
            await db.commit()
            
            # 异步发送通知
            asyncio.create_task(self._send_notification_async(
                db, str(notification.id), channels or ['system']
            ))
            
            return True
            
        except Exception as e:
            logger.error(f"发送通知失败: {e}")
            return False
    
    async def send_ai_task_completion_notification(self,
                                                   db: AsyncSession,
                                                   project_id: str,
                                                   task_id: str,
                                                   task_name: str,
                                                   status: str,
                                                   result_summary: str,
                                                   user_id: Optional[str] = None) -> bool:
        """发送AI任务完成通知"""
        try:
            title = f"AI任务完成通知"
            
            if status == "completed":
                message = f"✅ AI任务「{task_name}」已成功完成\n\n📋 执行结果：\n{result_summary}"
                notification_type = "info"
            else:
                message = f"❌ AI任务「{task_name}」执行失败\n\n❗ 错误信息：\n{result_summary}"
                notification_type = "error"
            
            recipient_users = [user_id] if user_id else []
            
            return await self.send_notification(
                db=db,
                project_id=project_id,
                notification_type=notification_type,
                title=title,
                message=message,
                source_type="ai_agent",
                source_id=task_id,
                recipient_users=recipient_users,
                channels=["system", "email"]
            )
            
        except Exception as e:
            logger.error(f"发送AI任务完成通知失败: {e}")
            return False
    
    async def send_automation_rule_notification(self,
                                                db: AsyncSession,
                                                project_id: str,
                                                rule_id: str,
                                                rule_name: str,
                                                execution_result: Dict[str, Any],
                                                user_id: Optional[str] = None) -> bool:
        """发送自动化规则执行通知"""
        try:
            success_items = execution_result.get('success_items', 0)
            failed_items = execution_result.get('failed_items', 0)
            total_items = success_items + failed_items
            
            title = f"自动化规则执行通知"
            
            if failed_items == 0:
                message = f"✅ 自动化规则「{rule_name}」执行成功\n\n📊 执行统计：\n- 总计：{total_items}项\n- 成功：{success_items}项\n- 失败：{failed_items}项"
                notification_type = "info"
            else:
                message = f"⚠️ 自动化规则「{rule_name}」执行完成（部分失败）\n\n📊 执行统计：\n- 总计：{total_items}项\n- 成功：{success_items}项\n- 失败：{failed_items}项"
                notification_type = "warning"
            
            return await self.send_notification(
                db=db,
                project_id=project_id,
                notification_type=notification_type,
                title=title,
                message=message,
                source_type="automation",
                source_id=rule_id,
                recipient_users=[user_id] if user_id else [],
                channels=["system"]
            )
            
        except Exception as e:
            logger.error(f"发送自动化规则通知失败: {e}")
            return False
    
    async def send_competitor_alert_notification(self,
                                                 db: AsyncSession,
                                                 project_id: str,
                                                 alert_id: str,
                                                 alert_type: str,
                                                 competitor_name: str,
                                                 product_name: str,
                                                 alert_data: Dict[str, Any]) -> bool:
        """发送竞品预警通知"""
        try:
            title = f"竞品监控预警"
            
            if alert_type == "price_drop":
                old_price = alert_data.get('old_price', 0)
                new_price = alert_data.get('new_price', 0)
                message = f"📉 竞品价格下降预警\n\n🏪 竞争对手：{competitor_name}\n📦 商品：{product_name}\n💰 价格变化：¥{old_price} → ¥{new_price}\n📊 降幅：{((old_price - new_price) / old_price * 100):.1f}%"
                notification_type = "warning"
            elif alert_type == "price_rise":
                old_price = alert_data.get('old_price', 0)
                new_price = alert_data.get('new_price', 0)
                message = f"📈 竞品价格上涨提醒\n\n🏪 竞争对手：{competitor_name}\n📦 商品：{product_name}\n💰 价格变化：¥{old_price} → ¥{new_price}\n📊 涨幅：{((new_price - old_price) / old_price * 100):.1f}%"
                notification_type = "info"
            elif alert_type == "stock_out":
                message = f"📦 竞品缺货提醒\n\n🏪 竞争对手：{competitor_name}\n📦 商品：{product_name}\n⚠️ 状态：商品已缺货"
                notification_type = "info"
            else:
                message = f"🔔 竞品监控提醒\n\n🏪 竞争对手：{competitor_name}\n📦 商品：{product_name}\n📋 类型：{alert_type}"
                notification_type = "info"
            
            return await self.send_notification(
                db=db,
                project_id=project_id,
                notification_type=notification_type,
                title=title,
                message=message,
                source_type="competitor",
                source_id=alert_id,
                channels=["system", "email"]
            )
            
        except Exception as e:
            logger.error(f"发送竞品预警通知失败: {e}")
            return False
    
    async def send_system_health_notification(self,
                                              db: AsyncSession,
                                              project_id: str,
                                              check_id: str,
                                              check_name: str,
                                              status: str,
                                              issues: List[Dict[str, Any]],
                                              recommendations: List[str]) -> bool:
        """发送系统健康检查通知"""
        try:
            title = f"系统健康检查报告"
            
            if status == "healthy":
                message = f"✅ 系统健康检查正常\n\n🔍 检查项目：{check_name}\n✨ 状态：系统运行正常"
                notification_type = "info"
            elif status == "warning":
                issues_text = "\n".join([f"- {issue.get('description', 'Unknown')}" for issue in issues[:3]])
                message = f"⚠️ 系统健康检查发现问题\n\n🔍 检查项目：{check_name}\n❗ 发现问题：\n{issues_text}"
                if len(issues) > 3:
                    message += f"\n... 还有{len(issues) - 3}个问题"
                notification_type = "warning"
            else:
                issues_text = "\n".join([f"- {issue.get('description', 'Unknown')}" for issue in issues[:3]])
                message = f"❌ 系统健康检查发现严重问题\n\n🔍 检查项目：{check_name}\n🚨 严重问题：\n{issues_text}"
                if len(issues) > 3:
                    message += f"\n... 还有{len(issues) - 3}个问题"
                notification_type = "error"
            
            if recommendations:
                rec_text = "\n".join([f"- {rec}" for rec in recommendations[:3]])
                message += f"\n\n💡 建议措施：\n{rec_text}"
            
            return await self.send_notification(
                db=db,
                project_id=project_id,
                notification_type=notification_type,
                title=title,
                message=message,
                source_type="system",
                source_id=check_id,
                channels=["system", "email"]
            )
            
        except Exception as e:
            logger.error(f"发送系统健康通知失败: {e}")
            return False
    
    async def _send_notification_async(self,
                                       db: AsyncSession,
                                       notification_id: str,
                                       channels: List[str]):
        """异步发送通知"""
        try:
            # 查找通知记录
            stmt = select(NotificationLog).where(NotificationLog.id == notification_id)
            result = await db.execute(stmt)
            notification = result.scalar_one_or_none()
            
            if not notification:
                return
            
            send_results = {}
            
            # 发送到各个渠道
            for channel in channels:
                try:
                    if channel == "system":
                        # 系统通知（存储到数据库即可）
                        send_results[channel] = {"success": True, "message": "系统通知已记录"}
                    elif channel == "email":
                        # 邮件通知
                        result = await self._send_email_notification(notification)
                        send_results[channel] = result
                    elif channel == "webhook":
                        # Webhook通知
                        result = await self._send_webhook_notification(notification)
                        send_results[channel] = result
                    else:
                        send_results[channel] = {"success": False, "message": f"不支持的通知渠道: {channel}"}
                        
                except Exception as e:
                    send_results[channel] = {"success": False, "message": str(e)}
            
            # 更新发送状态
            success_count = sum(1 for r in send_results.values() if r.get("success"))
            notification.send_status = "sent" if success_count > 0 else "failed"
            notification.send_details = send_results
            
            await db.commit()
            
        except Exception as e:
            logger.error(f"异步发送通知失败: {e}")
    
    async def _send_email_notification(self, notification: NotificationLog) -> Dict[str, Any]:
        """发送邮件通知"""
        try:
            # 这里需要集成实际的邮件发送服务
            # 暂时返回模拟结果
            await asyncio.sleep(0.1)  # 模拟发送时间
            
            return {
                "success": True,
                "message": "邮件发送成功"
            }
            
        except Exception as e:
            return {
                "success": False,
                "message": f"邮件发送失败: {str(e)}"
            }
    
    async def _send_webhook_notification(self, notification: NotificationLog) -> Dict[str, Any]:
        """发送Webhook通知"""
        try:
            # 这里需要实现实际的Webhook发送逻辑
            # 暂时返回模拟结果
            await asyncio.sleep(0.1)  # 模拟发送时间
            
            return {
                "success": True,
                "message": "Webhook发送成功"
            }
            
        except Exception as e:
            return {
                "success": False,
                "message": f"Webhook发送失败: {str(e)}"
            }
    
    async def get_notifications(self,
                                db: AsyncSession,
                                project_id: str,
                                notification_type: Optional[str] = None,
                                is_read: Optional[bool] = None,
                                limit: int = 50) -> List[Dict[str, Any]]:
        """获取通知列表"""
        try:
            conditions = [NotificationLog.project_id == project_id]
            
            if notification_type:
                conditions.append(NotificationLog.notification_type == notification_type)
            if is_read is not None:
                conditions.append(NotificationLog.is_read == is_read)
            
            stmt = select(NotificationLog).where(
                and_(*conditions)
            ).order_by(NotificationLog.created_at.desc()).limit(limit)
            
            result = await db.execute(stmt)
            notifications = result.scalars().all()
            
            return [
                {
                    'id': str(notif.id),
                    'notification_type': notif.notification_type,
                    'title': notif.title,
                    'message': notif.message,
                    'source_type': notif.source_type,
                    'source_id': notif.source_id,
                    'is_read': notif.is_read,
                    'is_handled': notif.is_handled,
                    'send_status': notif.send_status,
                    'created_at': notif.created_at.isoformat()
                }
                for notif in notifications
            ]
            
        except Exception as e:
            logger.error(f"获取通知列表失败: {e}")
            return []
    
    async def mark_notification_read(self,
                                     db: AsyncSession,
                                     notification_id: str,
                                     project_id: str) -> bool:
        """标记通知为已读"""
        try:
            stmt = select(NotificationLog).where(
                and_(
                    NotificationLog.id == notification_id,
                    NotificationLog.project_id == project_id
                )
            )
            result = await db.execute(stmt)
            notification = result.scalar_one_or_none()
            
            if notification:
                notification.is_read = True
                notification.read_at = datetime.utcnow()
                await db.commit()
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"标记通知已读失败: {e}")
            return False
