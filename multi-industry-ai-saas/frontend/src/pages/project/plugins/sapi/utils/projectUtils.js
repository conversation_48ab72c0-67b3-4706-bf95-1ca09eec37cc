/**
 * 获取当前项目ID
 *
 * 从localStorage中获取项目ID
 * @returns {string} 项目ID
 */
export const getCurrentProjectId = () => {
  return localStorage.getItem('project_id') || '';
};

/**
 * 构建API路径
 *
 * 将项目ID插入到API路径中
 * @param {string} path API路径
 * @returns {string} 包含项目ID的API路径
 */
export const buildApiPath = (path) => {
  const projectId = getCurrentProjectId();
  return path.replace('{project_id}', projectId);
};
