import api from '../httpClient';
import { getServiceProviderId } from '../httpClient';

/**
 * 服务商-租户管理API
 */
const tenant = {
  /**
   * 获取租户列表
   * @param {Object} params - 查询参数
   * @returns {Promise} - 租户列表
   */
  getList: async (params = {}) => {
    const serviceProviderId = getServiceProviderId();
    if (!serviceProviderId) {
      throw new Error('未指定服务商ID');
    }
    return api.get('/service-provider/tenants', { params });
  },

  /**
   * 获取租户详情
   * @param {string} id - 租户ID
   * @returns {Promise} - 租户详情
   */
  getDetail: async (id) => {
    const serviceProviderId = getServiceProviderId();
    if (!serviceProviderId) {
      throw new Error('未指定服务商ID');
    }
    return api.get(`/service-provider/tenants/${id}`);
  },

  /**
   * 创建租户
   * @param {Object} data - 租户数据
   * @returns {Promise} - 创建结果
   */
  create: async (data) => {
    const serviceProviderId = getServiceProviderId();
    if (!serviceProviderId) {
      throw new Error('未指定服务商ID');
    }
    return api.post('/service-provider/tenants', data);
  },

  /**
   * 更新租户
   * @param {string} id - 租户ID
   * @param {Object} data - 租户数据
   * @returns {Promise} - 更新结果
   */
  update: async (id, data) => {
    const serviceProviderId = getServiceProviderId();
    if (!serviceProviderId) {
      throw new Error('未指定服务商ID');
    }
    return api.put(`/service-provider/tenants/${id}`, data);
  },

  /**
   * 删除租户
   * @param {string} id - 租户ID
   * @returns {Promise} - 删除结果
   */
  delete: async (id) => {
    const serviceProviderId = getServiceProviderId();
    if (!serviceProviderId) {
      throw new Error('未指定服务商ID');
    }
    return api.delete(`/service-provider/tenants/${id}`);
  },

  /**
   * 获取租户项目列表
   * @param {string} tenantId - 租户ID
   * @param {Object} params - 查询参数
   * @returns {Promise} - 项目列表
   */
  getProjects: async (tenantId, params = {}) => {
    const serviceProviderId = getServiceProviderId();
    if (!serviceProviderId) {
      throw new Error('未指定服务商ID');
    }
    return api.get(`/service-provider/tenants/${tenantId}/projects`, { params });
  },

  /**
   * 获取租户用户列表
   * @param {string} tenantId - 租户ID
   * @param {Object} params - 查询参数
   * @returns {Promise} - 用户列表
   */
  getUsers: async (tenantId, params = {}) => {
    const serviceProviderId = getServiceProviderId();
    if (!serviceProviderId) {
      throw new Error('未指定服务商ID');
    }
    return api.get(`/service-provider/tenants/${tenantId}/users`, { params });
  },

  /**
   * 获取租户统计信息
   * @param {string} tenantId - 租户ID
   * @returns {Promise} - 统计信息
   */
  getStats: async (tenantId) => {
    const serviceProviderId = getServiceProviderId();
    if (!serviceProviderId) {
      throw new Error('未指定服务商ID');
    }
    return api.get(`/service-provider/tenants/${tenantId}/stats`);
  }
};

export default tenant;
