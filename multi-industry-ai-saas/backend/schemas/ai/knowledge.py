#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
AI 知识库 Schema
"""

from typing import Optional, List, Dict, Any, Union
from datetime import datetime
import uuid
from pydantic import BaseModel, Field, validator

class AIKnowledgeBaseBase(BaseModel):
    """AI 知识库基础 Schema"""
    tenant_id: uuid.UUID = Field(..., description="租户 ID")
    project_id: Optional[uuid.UUID] = Field(None, description="项目 ID，为空表示租户级知识库")
    name: str = Field(..., description="知识库名称")
    description: Optional[str] = Field(None, description="描述")
    industry_type: Optional[str] = Field(None, description="行业类型：retail, restaurant 等")
    embedding_model_id: Optional[uuid.UUID] = Field(None, description="嵌入模型 ID")
    config: Optional[Dict[str, Any]] = Field(None, description="配置参数")

class AIKnowledgeBaseCreate(BaseModel):
    """创建 AI 知识库 Schema - 前端创建时不需要提供tenant_id和project_id"""
    name: str = Field(..., description="知识库名称")
    description: Optional[str] = Field(None, description="描述")
    industry_type: Optional[str] = Field(None, description="行业类型：retail, restaurant 等")
    embedding_model_id: Optional[uuid.UUID] = Field(None, description="嵌入模型 ID")
    status: Optional[str] = Field("active", description="状态：active, building, inactive")
    config: Optional[Dict[str, Any]] = Field(None, description="配置参数")
    
    # 这些字段由后端API自动设置
    tenant_id: Optional[uuid.UUID] = Field(None, description="租户 ID - 由后端自动设置")
    project_id: Optional[uuid.UUID] = Field(None, description="项目 ID - 由后端自动设置")

class AIKnowledgeBaseUpdate(BaseModel):
    """更新 AI 知识库 Schema"""
    name: Optional[str] = Field(None, description="知识库名称")
    description: Optional[str] = Field(None, description="描述")
    industry_type: Optional[str] = Field(None, description="行业类型：retail, restaurant 等")
    embedding_model_id: Optional[uuid.UUID] = Field(None, description="嵌入模型 ID")
    status: Optional[str] = Field(None, description="状态：active, building, inactive")
    config: Optional[Dict[str, Any]] = Field(None, description="配置参数")

class AIKnowledgeBaseInDB(AIKnowledgeBaseBase):
    """数据库中的 AI 知识库 Schema"""
    id: uuid.UUID
    status: str = Field(..., description="状态：active, building, inactive")
    document_count: int = Field(..., description="文档数量")
    chunk_count: int = Field(..., description="文本块数量")
    created_at: datetime
    updated_at: Optional[datetime] = None

    # 关联数据
    embedding_model_name: Optional[str] = None

    class Config:
        from_attributes = True

class AIKnowledgeBaseResponse(BaseModel):
    """AI 知识库响应 Schema"""
    success: bool = True
    message: Optional[str] = None
    data: Optional[AIKnowledgeBaseInDB] = None

class AIKnowledgeBaseListResponse(BaseModel):
    """AI 知识库列表响应 Schema"""
    success: bool = True
    message: Optional[str] = None
    data: List[AIKnowledgeBaseInDB] = []
    total: int = 0
    page: int = 1
    page_size: int = 20

class AIKnowledgeDocumentBase(BaseModel):
    """AI 知识文档基础 Schema"""
    knowledge_base_id: uuid.UUID = Field(..., description="知识库 ID")
    title: str = Field(..., description="文档标题")
    content: Optional[str] = Field(None, description="文档内容")
    file_path: Optional[str] = Field(None, description="文件路径")
    file_type: Optional[str] = Field(None, description="文件类型：pdf, docx, txt 等")
    file_size: Optional[int] = Field(None, description="文件大小（字节）")
    source_url: Optional[str] = Field(None, description="来源 URL")
    request_metadata: Optional[Dict[str, Any]] = Field(None, description="请求元数据")

class AIKnowledgeDocumentCreate(AIKnowledgeDocumentBase):
    """创建 AI 知识文档 Schema"""
    pass

class AIKnowledgeDocumentUpdate(BaseModel):
    """更新 AI 知识文档 Schema"""
    title: Optional[str] = Field(None, description="文档标题")
    content: Optional[str] = Field(None, description="文档内容")
    file_path: Optional[str] = Field(None, description="文件路径")
    source_url: Optional[str] = Field(None, description="来源 URL")
    embedding_status: Optional[str] = Field(None, description="嵌入状态：pending, processing, completed, failed")
    request_metadata: Optional[Dict[str, Any]] = Field(None, description="请求元数据")

class AIKnowledgeDocumentInDB(AIKnowledgeDocumentBase):
    """数据库中的 AI 知识文档 Schema"""
    id: uuid.UUID
    chunk_count: int = Field(..., description="文本块数量")
    embedding_status: str = Field(..., description="嵌入状态：pending, processing, completed, failed")
    error_message: Optional[str] = Field(None, description="错误信息")
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

class AIKnowledgeDocumentResponse(BaseModel):
    """AI 知识文档响应 Schema"""
    success: bool = True
    message: Optional[str] = None
    data: Optional[AIKnowledgeDocumentInDB] = None

class AIKnowledgeDocumentListResponse(BaseModel):
    """AI 知识文档列表响应 Schema"""
    success: bool = True
    message: Optional[str] = None
    data: List[AIKnowledgeDocumentInDB] = []
    total: int = 0
    page: int = 1
    page_size: int = 20

class AIKnowledgeChunkBase(BaseModel):
    """AI 知识文本块基础 Schema"""
    knowledge_base_id: uuid.UUID = Field(..., description="知识库 ID")
    document_id: uuid.UUID = Field(..., description="文档 ID")
    content: str = Field(..., description="文本块内容")
    chunk_metadata: Optional[Dict[str, Any]] = Field(None, description="文本块元数据")
    page_number: Optional[int] = Field(None, description="页码")
    chunk_index: int = Field(..., description="文本块索引")
    token_count: Optional[int] = Field(None, description="Token 数量")

class AIKnowledgeChunkCreate(AIKnowledgeChunkBase):
    """创建 AI 知识文本块 Schema"""
    embedding: Optional[List[float]] = Field(None, description="嵌入向量")

class AIKnowledgeChunkInDB(AIKnowledgeChunkBase):
    """数据库中的 AI 知识文本块 Schema"""
    id: uuid.UUID
    embedding: Optional[List[float]] = Field(None, description="嵌入向量")
    created_at: datetime

    class Config:
        from_attributes = True

class AIKnowledgeChunkResponse(BaseModel):
    """AI 知识文本块响应 Schema"""
    success: bool = True
    message: Optional[str] = None
    data: Optional[AIKnowledgeChunkInDB] = None

class AIKnowledgeChunkListResponse(BaseModel):
    """AI 知识文本块列表响应 Schema"""
    success: bool = True
    message: Optional[str] = None
    data: List[AIKnowledgeChunkInDB] = []
    total: int = 0
    page: int = 1
    page_size: int = 20

class AIKnowledgeQueryRequest(BaseModel):
    """AI 知识库查询请求 Schema"""
    query: str = Field(..., description="查询文本")
    knowledge_base_id: uuid.UUID = Field(..., description="知识库 ID")
    top_k: int = Field(5, description="返回的结果数量")
    similarity_threshold: float = Field(0.7, description="相似度阈值")
    filter: Optional[Dict[str, Any]] = Field(None, description="过滤条件")

class AIKnowledgeQueryResult(BaseModel):
    """AI 知识库查询结果 Schema"""
    chunk_id: uuid.UUID = Field(..., description="文本块 ID")
    document_id: uuid.UUID = Field(..., description="文档 ID")
    document_title: str = Field(..., description="文档标题")
    content: str = Field(..., description="文本块内容")
    similarity: float = Field(..., description="相似度")
    chunk_metadata: Optional[Dict[str, Any]] = Field(None, description="文本块元数据")
    page_number: Optional[int] = Field(None, description="页码")

class AIKnowledgeQueryResponse(BaseModel):
    """AI 知识库查询响应 Schema"""
    success: bool = True
    message: Optional[str] = None
    data: List[AIKnowledgeQueryResult] = []
