# 钉钉插件权限配置指南

## 概述
钉钉插件需要特定的API权限才能正常工作。本文档详细说明了所需权限、申请流程和常见问题的解决方案。

## 必需权限列表

### 🔑 基础权限
- **通讯录个人信息读权限** - 获取用户基本信息
- **通讯录部门信息读权限** - 获取部门结构

### 📋 审批相关权限
- **审批模板权限** - 创建和管理审批模板  
- **审批实例权限** - 创建和管理审批实例
- **智能工作流权限** - 新版审批API（v1.0/workflow）

### 📚 知识库相关权限（重要更新）

#### 工作空间权限（v2.0 API）
- **Wiki.Workspace.Read** - 知识库工作空间读权限 ✅
- **Wiki.Workspace.Write** - 知识库工作空间写权限 ✅  

#### 知识库文档权限（后备API）
- **Document.WorkspaceDocument.Read** - 知识库文档读权限 ✅
- **Document.WorkspaceDocument.Write** - 知识库文档写权限 ✅

#### 知识库节点权限
- **Wiki.Node.Read** - 知识库节点读权限 ✅

### ❌ 已废弃/不存在的权限
- ~~Document.Workspace.Read~~ - 此权限不存在，请勿申请

## API版本对照表

| 功能 | 旧版API | 新版API | 权限要求 | 状态 |
|------|---------|---------|----------|------|
| 获取工作空间 | ❌ v1.0/doc/workspaces | ✅ v2.0/wiki/workspaces | Wiki.Workspace.Read | 已修复 |
| 获取知识库列表 | ❌ v1.0/doc/spaces | ✅ v2.0/wiki/spaces | Wiki.Workspace.Read | 已修复 |
| 审批模板 | ❌ topapi/process/save | ✅ v1.0/workflow/processCentres/schemas | 审批模板权限 | 已修复 |

## 权限申请流程

### 1. 进入钉钉开发者后台
访问：https://open-dev.dingtalk.com/

### 2. 选择应用
在"我的应用"中选择您的钉钉应用

### 3. 权限管理
1. 点击左侧菜单"权限管理"
2. 点击"申请权限"按钮
3. 搜索并勾选所需权限：
   - `Wiki.Workspace.Read`
   - `Wiki.Workspace.Write`  
   - `Document.WorkspaceDocument.Read`
   - `Document.WorkspaceDocument.Write`
   - `Wiki.Node.Read`

### 4. 提交审核
填写权限申请理由并提交审核，通常1-3个工作日内审核完成。

## 📊 故障排除指南

### 问题1：获取工作空间列表失败

#### 错误信息
```
ERROR: 没有调用该接口的权限，接口权限申请参考...
HTTP 403 Forbidden
```

#### 解决方案
1. **确认API版本**：确保使用v2.0 API
   ```
   GET /v2.0/wiki/workspaces
   ```

2. **确认权限申请**：检查是否已申请`Wiki.Workspace.Read`权限
   
3. **确认认证方式**：使用请求头认证
   ```http
   x-acs-dingtalk-access-token: {access_token}
   ```

#### 代码示例
```python
# ✅ 正确的v2.0 API调用
url = "https://api.dingtalk.com/v2.0/wiki/workspaces"
headers = {
    "x-acs-dingtalk-access-token": access_token,
    "Content-Type": "application/json"
}
params = {
    "maxResults": 30,
    "orderBy": "VIEW_TIME_DESC",
    "withPermissionRole": False,
    "operatorId": user_id
}
```

### 问题2：获取知识库列表失败

#### 错误信息
```
API请求失败: HTTP 404
```

#### 解决方案
1. **API端点确认**：v2.0/wiki/spaces可能不存在，使用后备方案
   
2. **权限检查**：确认已申请相关权限
   
3. **参数验证**：确保workspaceId有效

#### 代码示例
```python
# 主API（v2.0）
url = "https://api.dingtalk.com/v2.0/wiki/spaces"

# 后备API（v1.0）
fallback_url = "https://api.dingtalk.com/v1.0/doc/spaces"
```

### 问题3：审批模板创建失败

#### 错误信息
```
ERROR: Missing required arguments:saveProcessRequest.form_component_list
```

#### 解决方案
1. **API升级**：使用新版v1.0审批API
   ```
   POST /v1.0/workflow/processCentres/schemas
   ```

2. **数据结构修正**：移除旧版包装器
   ```python
   # ❌ 旧版数据结构
   {
       "saveProcessRequest": {
           "form_component_list": [...]
       }
   }
   
   # ✅ 新版数据结构
   {
       "formComponents": [...]
   }
   ```

## 🔧 技术实现要点

### 1. 认证方式统一
所有新版API统一使用请求头认证：
```http
x-acs-dingtalk-access-token: {access_token}
```

### 2. 参数命名规范
新版API采用驼峰命名：
- `maxResults` (不是 max_results)
- `operatorId` (不是 operator_id)
- `workspaceId` (不是 workspace_id)

### 3. 错误处理增强
```python
async def handle_api_error(response):
    if response.status_code == 403:
        # 权限错误，提供具体权限名称
        return "需要申请Wiki.Workspace.Read权限"
    elif response.status_code == 404:
        # API不存在，使用后备方案
        return "API不存在，使用后备API"
    else:
        # 其他错误
        return f"API错误: {response.status_code}"
```

## 📝 测试验证

### 使用测试脚本
运行测试脚本验证API修复：
```bash
cd backend
python test_dingtalk_v2_api.py
```

### 检查日志
查看详细API调用日志：
```bash
tail -f logs/dingtalk.log
```

## 🚀 最新修复状态

### ✅ 已修复问题
1. **知识库工作空间API** - 升级到v2.0/wiki/workspaces
2. **权限名称错误** - 修正为正确的Wiki.Workspace.Read
3. **认证方式** - 统一使用请求头认证
4. **审批模板API** - 升级到v1.0/workflow新版API
5. **数据结构** - 符合钉钉官方API标准

### 🔄 持续改进
- API版本兼容性处理
- 错误提示优化
- 权限申请指导完善

## 📞 技术支持

如果权限申请或API调用仍有问题，请：

1. **查看钉钉官方文档**：https://open.dingtalk.com/document/
2. **权限申请参考**：https://open.dingtalk.com/document/orgapp-server/add-api-permission
3. **API调试工具**：https://open-dev.dingtalk.com/apiExplorer

---

**最后更新**：2024年（基于钉钉官方v2.0 API文档） 