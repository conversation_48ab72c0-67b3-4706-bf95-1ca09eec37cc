#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
项目级 AI 配置 API
"""

import logging
import uuid
from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, Query, Path, status
from sqlalchemy.ext.asyncio import AsyncSession

from db.database import get_db
from models.project import Project
from core.auth import get_current_user
from api.deps import get_current_project
from services.ai import AIConfigService, AISettingsService
from schemas.ai import (
    AIConfigCreate,
    AIConfigUpdate,
    AIConfigResponse,
    AIConfigListResponse,
    AISettingsBase,
    AISettingsUpdate,
    AISettingsResponse,
)
from models.user import User

logger = logging.getLogger(__name__)
router = APIRouter()

@router.get("/ai/configs", response_model=AIConfigListResponse)
async def list_configs(
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db),
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    provider_id: Optional[uuid.UUID] = Query(None),
    model_id: Optional[uuid.UUID] = Query(None),
    status: Optional[str] = Query(None),
):
    """
    获取 AI 配置列表

    注意：只返回当前项目的配置和系统级配置
    """
    try:
        logger.info(f"开始获取项目 {project.id} 的AI配置列表")
        
        configs, total = await AIConfigService.get_configs(
            db=db,
            skip=skip,
            limit=limit,
            project_id=project.id,
            provider_id=provider_id,
            model_id=model_id,
            status=status,
        )

        logger.info(f"获取到 {len(configs)} 个配置，总计 {total} 个")

        # 手动构建响应数据，确保数据完整性
        config_data = []
        for config in configs:
            try:
                logger.debug(f"处理配置 {config.name}: id={config.id}, model_id={config.model_id}, provider_id={config.provider_id}")
                
                # 基础配置信息
                config_dict = {
                    "id": str(config.id),
                    "tenant_id": str(config.tenant_id) if config.tenant_id else None,
                    "project_id": str(config.project_id) if config.project_id else None,
                    "provider_id": str(config.provider_id),
                    "model_id": str(config.model_id),
                    "name": config.name,
                    "description": config.description,
                    "api_key": config.api_key,
                    "api_endpoint": config.api_endpoint,
                    "proxy_url": config.proxy_url,
                    "temperature": config.temperature,
                    "max_tokens": config.max_tokens,
                    "top_p": config.top_p,
                    "frequency_penalty": config.frequency_penalty,
                    "presence_penalty": config.presence_penalty,
                    "is_default": config.is_default,
                    "priority": config.priority,
                    "weight": config.weight,
                    "status": config.status,
                    "config": config.config,
                    "created_at": config.created_at.isoformat() if config.created_at else None,
                    "updated_at": config.updated_at.isoformat() if config.updated_at else None,
                    "provider": None,
                    "model": None,
                }
                
                # 确保提供商信息
                if hasattr(config, 'provider') and config.provider:
                    config_dict["provider"] = {
                        "id": str(config.provider.id),
                        "name": config.provider.name,
                        "display_name": config.provider.display_name,
                        "description": config.provider.description,
                    }
                    logger.debug(f"配置 {config.name} 已加载提供商: {config.provider.name}")
                else:
                    logger.warning(f"配置 {config.name} 缺少提供商，尝试手动查询")
                    # 手动查询提供商
                    from services.ai.provider_service import AIProviderService
                    provider = await AIProviderService.get_provider(db, config.provider_id)
                    if provider:
                        config_dict["provider"] = {
                            "id": str(provider.id),
                            "name": provider.name,
                            "display_name": provider.display_name,
                            "description": provider.description,
                        }
                        logger.debug(f"手动查询提供商成功: {provider.name}")
                    else:
                        logger.error(f"无法找到提供商: {config.provider_id}")
                
                # 确保模型信息
                if hasattr(config, 'model') and config.model:
                    config_dict["model"] = {
                        "id": str(config.model.id),
                        "name": config.model.name,
                        "display_name": config.model.display_name,
                        "description": config.model.description,
                        "model_type": config.model.model_type,
                        "context_window": config.model.context_window,
                        "token_limit": config.model.token_limit,
                        "capabilities": config.model.capabilities,
                        "input_price_per_1k_tokens": config.model.input_price_per_1k_tokens,
                        "output_price_per_1k_tokens": config.model.output_price_per_1k_tokens,
                    }
                    logger.debug(f"配置 {config.name} 已加载模型: {config.model.name}")
                else:
                    logger.warning(f"配置 {config.name} 缺少模型，尝试手动查询")
                    # 手动查询模型
                    from services.ai.model_service import AIModelService
                    model = await AIModelService.get_model(db, config.model_id)
                    if model:
                        config_dict["model"] = {
                            "id": str(model.id),
                            "name": model.name,
                            "display_name": model.display_name,
                            "description": model.description,
                            "model_type": model.model_type,
                            "context_window": model.context_window,
                            "token_limit": model.token_limit,
                            "capabilities": model.capabilities,
                            "input_price_per_1k_tokens": model.input_price_per_1k_tokens,
                            "output_price_per_1k_tokens": model.output_price_per_1k_tokens,
                        }
                        logger.debug(f"手动查询模型成功: {model.name}")
                    else:
                        logger.error(f"无法找到模型: {config.model_id}")
                
                config_data.append(config_dict)
                logger.debug(f"配置 {config.name} 处理完成")
                
            except Exception as e:
                logger.error(f"处理配置 {config.id if hasattr(config, 'id') else 'unknown'} 时出错: {e}")
                # 添加错误配置的基础信息
                config_data.append({
                    "id": str(config.id) if hasattr(config, 'id') else None,
                    "name": config.name if hasattr(config, 'name') else "Unknown",
                    "error": f"配置处理失败: {str(e)}"
                })

        logger.info(f"成功处理 {len(config_data)} 个配置")

        return {
            "success": True,
            "message": "获取 AI 配置列表成功",
            "data": config_data,
            "total": total,
            "page": skip // limit + 1,
            "page_size": limit,
        }
    except Exception as e:
        logger.error(f"获取 AI 配置列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取 AI 配置列表失败: {str(e)}",
        )

@router.get("/ai/configs/{config_id}", response_model=AIConfigResponse)
async def get_config(
    config_id: uuid.UUID = Path(...),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db),
):
    """
    获取 AI 配置详情
    """
    try:
        config = await AIConfigService.get_config(db=db, config_id=config_id)

        if not config:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"AI 配置 ID '{config_id}' 不存在",
            )

        # 检查是否有权限访问该配置
        if config.project_id and config.project_id != project.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问该 AI 配置",
            )

        # 手动构建响应数据以避免序列化问题
        config_response_data = {
            "id": str(config.id),
            "tenant_id": str(config.tenant_id) if config.tenant_id else None,
            "project_id": str(config.project_id) if config.project_id else None,
            "provider_id": str(config.provider_id),
            "model_id": str(config.model_id),
            "name": config.name,
            "description": config.description,
            "api_key": config.api_key,
            "api_endpoint": config.api_endpoint,
            "proxy_url": config.proxy_url,
            "temperature": config.temperature,
            "max_tokens": config.max_tokens,
            "top_p": config.top_p,
            "frequency_penalty": config.frequency_penalty,
            "presence_penalty": config.presence_penalty,
            "is_default": config.is_default,
            "priority": config.priority,
            "weight": config.weight,
            "status": config.status,
            "config": config.config,
            "created_at": config.created_at.isoformat() if config.created_at else None,
            "updated_at": config.updated_at.isoformat() if config.updated_at else None,
            "provider": None,
            "model": None,
        }
        
        # 添加提供商信息
        if hasattr(config, 'provider') and config.provider:
            config_response_data["provider"] = {
                "id": str(config.provider.id),
                "name": config.provider.name,
                "display_name": config.provider.display_name,
                "description": config.provider.description,
            }
        
        # 添加模型信息
        if hasattr(config, 'model') and config.model:
            config_response_data["model"] = {
                "id": str(config.model.id),
                "name": config.model.name,
                "display_name": config.model.display_name,
                "description": config.model.description,
                "model_type": config.model.model_type,
                "context_window": config.model.context_window,
                "token_limit": config.model.token_limit,
                "capabilities": config.model.capabilities,
                "input_price_per_1k_tokens": config.model.input_price_per_1k_tokens,
                "output_price_per_1k_tokens": config.model.output_price_per_1k_tokens,
            }

        return {
            "success": True,
            "message": "获取 AI 配置详情成功",
            "data": config_response_data,
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取 AI 配置详情失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取 AI 配置详情失败: {str(e)}",
        )

@router.post("/ai/configs", response_model=AIConfigResponse, status_code=status.HTTP_201_CREATED)
async def create_config(
    config_data: AIConfigCreate,
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    创建 AI 配置

    注意：只有项目管理员才能创建 AI 配置
    """
    try:
        # 检查权限
        if not current_user.is_system_admin and not current_user.is_tenant_admin:
            # 检查是否为项目管理员
            if not any(pu.is_admin for pu in project.project_users if pu.user_id == current_user.id):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="只有项目管理员才能创建 AI 配置",
                )

        # 设置项目 ID
        config_data.project_id = project.id
        config_data.tenant_id = project.tenant_id

        config = await AIConfigService.create_config(db=db, config_data=config_data)

        # 手动构建响应数据以避免序列化问题
        config_data = {
            "id": str(config.id),
            "tenant_id": str(config.tenant_id) if config.tenant_id else None,
            "project_id": str(config.project_id) if config.project_id else None,
            "provider_id": str(config.provider_id),
            "model_id": str(config.model_id),
            "name": config.name,
            "description": config.description,
            "api_key": config.api_key,
            "api_endpoint": config.api_endpoint,
            "proxy_url": config.proxy_url,
            "temperature": config.temperature,
            "max_tokens": config.max_tokens,
            "top_p": config.top_p,
            "frequency_penalty": config.frequency_penalty,
            "presence_penalty": config.presence_penalty,
            "is_default": config.is_default,
            "priority": config.priority,
            "weight": config.weight,
            "status": config.status,
            "config": config.config,
            "created_at": config.created_at.isoformat() if config.created_at else None,
            "updated_at": config.updated_at.isoformat() if config.updated_at else None,
            "provider": None,
            "model": None,
        }
        
        # 添加提供商信息
        if hasattr(config, 'provider') and config.provider:
            config_data["provider"] = {
                "id": str(config.provider.id),
                "name": config.provider.name,
                "display_name": config.provider.display_name,
                "description": config.provider.description,
            }
        
        # 添加模型信息
        if hasattr(config, 'model') and config.model:
            config_data["model"] = {
                "id": str(config.model.id),
                "name": config.model.name,
                "display_name": config.model.display_name,
                "description": config.model.description,
                "model_type": config.model.model_type,
                "context_window": config.model.context_window,
                "token_limit": config.model.token_limit,
                "capabilities": config.model.capabilities,
                "input_price_per_1k_tokens": config.model.input_price_per_1k_tokens,
                "output_price_per_1k_tokens": config.model.output_price_per_1k_tokens,
            }

        return {
            "success": True,
            "message": "创建 AI 配置成功",
            "data": config_data,
        }
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建 AI 配置失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建 AI 配置失败: {str(e)}",
        )

@router.put("/ai/configs/{config_id}", response_model=AIConfigResponse)
async def update_config(
    config_id: uuid.UUID,
    config_data: AIConfigUpdate,
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    更新 AI 配置

    注意：只有项目管理员才能更新 AI 配置，且只能更新当前项目的配置
    """
    try:
        # 获取配置
        config = await AIConfigService.get_config(db=db, config_id=config_id)

        if not config:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"AI 配置 ID '{config_id}' 不存在",
            )

        # 检查是否有权限更新该配置
        if config.project_id != project.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权更新该 AI 配置",
            )

        # 检查权限
        if not current_user.is_system_admin and not current_user.is_tenant_admin:
            # 检查是否为项目管理员
            if not any(pu.is_admin for pu in project.project_users if pu.user_id == current_user.id):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="只有项目管理员才能更新 AI 配置",
                )

        config = await AIConfigService.update_config(
            db=db,
            config_id=config_id,
            config_data=config_data,
        )

        # 手动构建响应数据以避免序列化问题
        config_response_data = {
            "id": str(config.id),
            "tenant_id": str(config.tenant_id) if config.tenant_id else None,
            "project_id": str(config.project_id) if config.project_id else None,
            "provider_id": str(config.provider_id),
            "model_id": str(config.model_id),
            "name": config.name,
            "description": config.description,
            "api_key": config.api_key,
            "api_endpoint": config.api_endpoint,
            "proxy_url": config.proxy_url,
            "temperature": config.temperature,
            "max_tokens": config.max_tokens,
            "top_p": config.top_p,
            "frequency_penalty": config.frequency_penalty,
            "presence_penalty": config.presence_penalty,
            "is_default": config.is_default,
            "priority": config.priority,
            "weight": config.weight,
            "status": config.status,
            "config": config.config,
            "created_at": config.created_at.isoformat() if config.created_at else None,
            "updated_at": config.updated_at.isoformat() if config.updated_at else None,
            "provider": None,
            "model": None,
        }
        
        # 添加提供商信息
        if hasattr(config, 'provider') and config.provider:
            config_response_data["provider"] = {
                "id": str(config.provider.id),
                "name": config.provider.name,
                "display_name": config.provider.display_name,
                "description": config.provider.description,
            }
        
        # 添加模型信息
        if hasattr(config, 'model') and config.model:
            config_response_data["model"] = {
                "id": str(config.model.id),
                "name": config.model.name,
                "display_name": config.model.display_name,
                "description": config.model.description,
                "model_type": config.model.model_type,
                "context_window": config.model.context_window,
                "token_limit": config.model.token_limit,
                "capabilities": config.model.capabilities,
                "input_price_per_1k_tokens": config.model.input_price_per_1k_tokens,
                "output_price_per_1k_tokens": config.model.output_price_per_1k_tokens,
            }

        return {
            "success": True,
            "message": "更新 AI 配置成功",
            "data": config_response_data,
        }
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新 AI 配置失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新 AI 配置失败: {str(e)}",
        )

@router.delete("/ai/configs/{config_id}", response_model=AIConfigResponse)
async def delete_config(
    config_id: uuid.UUID = Path(...),
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    删除 AI 配置

    注意：只有项目管理员才能删除 AI 配置，且只能删除当前项目的配置
    """
    try:
        # 获取配置
        config = await AIConfigService.get_config(db=db, config_id=config_id)

        if not config:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"AI 配置 ID '{config_id}' 不存在",
            )

        # 检查是否有权限删除该配置
        if config.project_id != project.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权删除该 AI 配置",
            )

        # 检查权限
        if not current_user.is_system_admin and not current_user.is_tenant_admin:
            # 检查是否为项目管理员
            if not any(pu.is_admin for pu in project.project_users if pu.user_id == current_user.id):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="只有项目管理员才能删除 AI 配置",
                )

        await AIConfigService.delete_config(db=db, config_id=config_id)

        return {
            "success": True,
            "message": "删除 AI 配置成功",
        }
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除 AI 配置失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除 AI 配置失败: {str(e)}",
        )

@router.get("/ai/settings", response_model=AISettingsResponse)
async def get_ai_settings(
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db),
):
    """
    获取 AI 设置
    """
    try:
        settings = await AISettingsService.get_settings(db=db, project_id=project.id)

        return {
            "success": True,
            "message": "获取 AI 设置成功",
            "data": settings,
        }
    except Exception as e:
        logger.error(f"获取 AI 设置失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取 AI 设置失败: {str(e)}",
        )

@router.put("/ai/settings", response_model=AISettingsResponse)
async def update_ai_settings(
    settings_data: AISettingsUpdate,
    project: Project = Depends(get_current_project),
    current_user = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    更新 AI 设置

    注意：只有项目管理员才能更新 AI 设置
    """
    try:
        # 检查权限
        if not current_user.get("is_system_admin") and not current_user.get("is_tenant_admin"):
            # 检查是否为项目管理员
            if not any(pu.is_admin for pu in project.project_users if pu.user_id == current_user.get("id")):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="只有项目管理员才能更新 AI 设置",
                )

        # 获取当前设置
        current_settings = await AISettingsService.get_settings(db=db, project_id=project.id)

        # 更新设置
        update_data = settings_data.dict(exclude_unset=True)
        for key, value in update_data.items():
            if value is not None:
                current_settings[key] = value

        # 保存设置
        success = await AISettingsService.update_settings(
            db=db,
            project_id=project.id,
            settings=current_settings,
        )

        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="更新 AI 设置失败",
            )

        return {
            "success": True,
            "message": "更新 AI 设置成功",
            "data": current_settings,
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新 AI 设置失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新 AI 设置失败: {str(e)}",
        )

@router.post("/ai/configs/{config_id}/test")
async def test_config(
    config_id: uuid.UUID = Path(...),
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    测试 AI 配置连接
    """
    try:
        # 获取配置
        config = await AIConfigService.get_config(db=db, config_id=config_id)

        if not config:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"AI 配置 ID '{config_id}' 不存在",
            )

        # 检查是否有权限测试该配置
        if config.project_id != project.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权测试该 AI 配置",
            )

        # 测试配置连接
        import time
        import httpx
        
        start_time = time.time()
        
        try:
            # 构建测试请求
            headers = {
                "Authorization": f"Bearer {config.api_key}",
                "Content-Type": "application/json"
            }
            
            # 根据不同提供商构建不同的测试请求
            test_data = {
                "model": config.model_name or "gpt-3.5-turbo",
                "messages": [{"role": "user", "content": "Hello, this is a test message."}],
                "max_tokens": 10,
                "temperature": 0.1
            }
            
            endpoint = config.api_endpoint or "https://api.openai.com/v1/chat/completions"
            
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.post(
                    endpoint,
                    headers=headers,
                    json=test_data,
                    proxies=config.proxy_url if config.proxy_url else None
                )
                
                response_time = int((time.time() - start_time) * 1000)
                
                if response.status_code == 200:
                    response_data = response.json()
                    
                    return {
                        "success": True,
                        "message": "配置测试成功",
                        "data": {
                            "status": "success",
                            "response_time": response_time,
                            "model_info": {
                                "name": config.model_name,
                                "provider": config.provider_name
                            },
                            "test_message": response_data.get("choices", [{}])[0].get("message", {}).get("content", "测试成功")
                        }
                    }
                else:
                    return {
                        "success": False,
                        "message": f"配置测试失败: HTTP {response.status_code}",
                        "data": {
                            "status": "failed",
                            "response_time": response_time,
                            "error": response.text
                        }
                    }
                    
        except httpx.TimeoutException:
            return {
                "success": False,
                "message": "配置测试超时",
                "data": {
                    "status": "timeout",
                    "response_time": int((time.time() - start_time) * 1000),
                    "error": "请求超时"
                }
            }
        except Exception as test_error:
            return {
                "success": False,
                "message": f"配置测试失败: {str(test_error)}",
                "data": {
                    "status": "error",
                    "response_time": int((time.time() - start_time) * 1000),
                    "error": str(test_error)
                }
            }
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"测试 AI 配置失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"测试 AI 配置失败: {str(e)}",
        )
