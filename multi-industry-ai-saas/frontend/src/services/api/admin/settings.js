import api from '../httpClient';

/**
 * 设置API
 */
const settings = {
  /**
   * 获取系统设置
   * @returns {Promise} - 系统设置
   */
  getSystem: async () => {
    return api.get('/admin/settings/system');
  },

  /**
   * 更新系统设置
   * @param {Object} data - 系统设置数据
   * @returns {Promise} - 更新结果
   */
  updateSystem: async (data) => {
    return api.put('/admin/settings/system', data);
  },

  /**
   * 获取安全设置
   * @returns {Promise} - 安全设置
   */
  getSecurity: async () => {
    return api.get('/admin/settings/security');
  },

  /**
   * 更新安全设置
   * @param {Object} data - 安全设置数据
   * @returns {Promise} - 更新结果
   */
  updateSecurity: async (data) => {
    return api.put('/admin/settings/security', data);
  },

  /**
   * 获取通知设置
   * @returns {Promise} - 通知设置
   */
  getNotification: async () => {
    return api.get('/admin/settings/notification');
  },

  /**
   * 更新通知设置
   * @param {Object} data - 通知设置数据
   * @returns {Promise} - 更新结果
   */
  updateNotification: async (data) => {
    return api.put('/admin/settings/notification', data);
  },

  /**
   * 获取第三方登录设置
   * @returns {Promise} - 第三方登录设置
   */
  getThirdPartyLogin: async () => {
    return api.get('/admin/settings/third-party-login');
  },

  /**
   * 更新第三方登录设置
   * @param {Object} data - 第三方登录设置数据
   * @returns {Promise} - 更新结果
   */
  updateThirdPartyLogin: async (data) => {
    return api.put('/admin/settings/third-party-login', data);
  }
};

export default settings;
