import hmac
import hashlib
import base64
import logging

logger = logging.getLogger(__name__)

def verify_dingtalk_signature(timestamp: str, client_secret: str, signature: str) -> bool:
    """
    根据钉钉官方文档验证回调请求的签名。
    https://open.dingtalk.com/document/orgapp/configure-the-server-api-for-the-robot

    Args:
        timestamp (str): 请求头中的 'x-dingtalk-timestamp'。
        client_secret (str): 钉钉应用或机器人的 Client Secret (AppSecret)。
        signature (str): 请求头中的 'x-dingtalk-signature'。

    Returns:
        bool: 签名是否有效。
    """
    if not timestamp or not signature:
        logger.warning("签名验证失败：缺少时间戳或签名。")
        return False

    string_to_sign = f"{timestamp}\n{client_secret}"
    
    try:
        # 使用HMAC-SHA256算法计算签名
        mac = hmac.new(
            client_secret.encode('utf-8'),
            string_to_sign.encode('utf-8'),
            hashlib.sha256
        ).digest()
        
        # 将计算出的签名进行Base64编码
        computed_signature = base64.b64encode(mac).decode('utf-8')
        
        logger.debug(f"签名验证: 时间戳='{timestamp}', 计算出的签名='{computed_signature}', 收到的签名='{signature}'")

        # 比较计算出的签名和请求中的签名是否相等
        return computed_signature == signature
    except Exception as e:
        logger.error(f"签名计算过程中出现异常: {e}", exc_info=True)
        return False 