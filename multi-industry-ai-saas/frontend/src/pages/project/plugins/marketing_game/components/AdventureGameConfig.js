import React, { useState, useEffect } from 'react';
import {
  Form,
  Input,
  Button,
  Card,
  Space,
  Typography,
  Divider,
  Select,
  InputNumber,
  Collapse,
  Radio,
  Empty,
  message
} from 'antd';
import {
  PlusOutlined,
  DeleteOutlined,
  QuestionCircleOutlined,
  TrophyOutlined,
  FlagOutlined
} from '@ant-design/icons';

const { Paragraph } = Typography;
const { Panel } = Collapse;
const { Option } = Select;
const { TextArea } = Input;

/**
 * 闯关游戏配置组件
 */
const AdventureGameConfig = ({ form, initialValues }) => {
  const [levels, setLevels] = useState(
    initialValues?.settings?.levels || [
      {
        id: 1,
        name: '初级挑战',
        description: '完成简单的问答题，获得进入下一关的资格',
        type: 'quiz',
        questions: [
          {
            id: 'q1',
            text: '以下哪个是我们品牌的标志颜色？',
            options: ['红色', '蓝色', '绿色', '黄色'],
            answer: 0
          },
          {
            id: 'q2',
            text: '我们的旗舰店位于哪个城市？',
            options: ['北京', '上海', '广州', '深圳'],
            answer: 1
          }
        ],
        required_score: 1,
        reward: {
          points: 10,
          description: '获得10积分'
        }
      }
    ]
  );

  // 当表单值变化时更新状态
  useEffect(() => {
    const settings = form.getFieldValue('settings') || {};
    if (settings.levels) {
      setLevels(settings.levels);
    }
  }, [form]);

  // 更新表单值
  const updateFormSettings = (newLevels) => {
    const settings = form.getFieldValue('settings') || {};
    form.setFieldsValue({
      settings: {
        ...settings,
        levels: newLevels
      }
    });
    setLevels(newLevels);
  };

  // 添加新关卡
  const addLevel = () => {
    const newLevel = {
      id: levels.length + 1,
      name: `第${levels.length + 1}关`,
      description: '完成挑战，获得奖励',
      type: 'quiz',
      questions: [
        {
          id: `q${Date.now()}`,
          text: '请输入问题内容',
          options: ['选项1', '选项2', '选项3', '选项4'],
          answer: 0
        }
      ],
      required_score: 1,
      reward: {
        points: 10,
        description: '获得10积分'
      }
    };

    const newLevels = [...levels, newLevel];
    updateFormSettings(newLevels);
  };

  // 删除关卡
  const removeLevel = (index) => {
    const newLevels = [...levels];
    newLevels.splice(index, 1);

    // 更新关卡ID
    newLevels.forEach((level, idx) => {
      level.id = idx + 1;
    });

    updateFormSettings(newLevels);
  };

  // 添加问题
  const addQuestion = (levelIndex) => {
    const newLevels = [...levels];
    const newQuestion = {
      id: `q${Date.now()}`,
      text: '请输入问题内容',
      options: ['选项1', '选项2', '选项3', '选项4'],
      answer: 0
    };

    newLevels[levelIndex].questions.push(newQuestion);
    updateFormSettings(newLevels);
  };

  // 删除问题
  const removeQuestion = (levelIndex, questionIndex) => {
    const newLevels = [...levels];
    newLevels[levelIndex].questions.splice(questionIndex, 1);
    updateFormSettings(newLevels);
  };

  // 更新关卡信息
  const updateLevel = (index, field, value) => {
    const newLevels = [...levels];
    newLevels[index][field] = value;
    updateFormSettings(newLevels);
  };

  // 更新问题信息
  const updateQuestion = (levelIndex, questionIndex, field, value) => {
    const newLevels = [...levels];
    newLevels[levelIndex].questions[questionIndex][field] = value;
    updateFormSettings(newLevels);
  };

  // 更新选项
  const updateOption = (levelIndex, questionIndex, optionIndex, value) => {
    const newLevels = [...levels];
    newLevels[levelIndex].questions[questionIndex].options[optionIndex] = value;
    updateFormSettings(newLevels);
  };

  // 更新奖励
  const updateReward = (levelIndex, field, value) => {
    const newLevels = [...levels];
    newLevels[levelIndex].reward[field] = value;
    updateFormSettings(newLevels);
  };

  return (
    <div className="adventure-game-config">
      <Paragraph>
        配置闯关游戏的关卡、问题和奖励。每个关卡可以包含多个问题，玩家需要回答正确一定数量的问题才能通过关卡。
      </Paragraph>

      <Collapse defaultActiveKey={['0']} accordion>
        {levels.map((level, levelIndex) => (
          <Panel
            key={levelIndex}
            header={
              <Space>
                <FlagOutlined />
                <span>{level.name || `第${levelIndex + 1}关`}</span>
              </Space>
            }
            extra={
              <Button
                danger
                icon={<DeleteOutlined />}
                onClick={(e) => {
                  e.stopPropagation();
                  removeLevel(levelIndex);
                }}
                size="small"
              >
                删除关卡
              </Button>
            }
          >
            <Space direction="vertical" style={{ width: '100%' }}>
              <Card title="关卡设置" size="small">
                <Form.Item label="关卡名称">
                  <Input
                    value={level.name}
                    onChange={(e) => updateLevel(levelIndex, 'name', e.target.value)}
                    placeholder="输入关卡名称"
                  />
                </Form.Item>

                <Form.Item label="关卡描述">
                  <TextArea
                    value={level.description}
                    onChange={(e) => updateLevel(levelIndex, 'description', e.target.value)}
                    placeholder="输入关卡描述"
                    rows={2}
                  />
                </Form.Item>

                <Form.Item label="关卡类型">
                  <Select
                    value={level.type}
                    onChange={(value) => updateLevel(levelIndex, 'type', value)}
                    style={{ width: '100%' }}
                  >
                    <Option value="quiz">问答题</Option>
                    <Option value="puzzle">拼图</Option>
                    <Option value="memory">记忆游戏</Option>
                  </Select>
                </Form.Item>

                <Form.Item label="通关所需分数">
                  <InputNumber
                    min={1}
                    max={level.questions?.length || 1}
                    value={level.required_score}
                    onChange={(value) => updateLevel(levelIndex, 'required_score', value)}
                    style={{ width: '100%' }}
                  />
                </Form.Item>
              </Card>

              <Card
                title={
                  <Space>
                    <QuestionCircleOutlined />
                    <span>问题列表</span>
                  </Space>
                }
                size="small"
                extra={
                  <Button
                    type="primary"
                    icon={<PlusOutlined />}
                    onClick={() => addQuestion(levelIndex)}
                    size="small"
                  >
                    添加问题
                  </Button>
                }
              >
                {level.questions?.map((question, questionIndex) => (
                  <Card
                    key={question.id}
                    title={`问题 ${questionIndex + 1}`}
                    size="small"
                    style={{ marginBottom: '16px' }}
                    extra={
                      <Button
                        danger
                        icon={<DeleteOutlined />}
                        onClick={() => removeQuestion(levelIndex, questionIndex)}
                        size="small"
                        disabled={level.questions.length <= 1}
                      >
                        删除
                      </Button>
                    }
                  >
                    <Form.Item label="问题内容">
                      <TextArea
                        value={question.text}
                        onChange={(e) => updateQuestion(levelIndex, questionIndex, 'text', e.target.value)}
                        placeholder="输入问题内容"
                        rows={2}
                      />
                    </Form.Item>

                    <Divider orientation="left">选项</Divider>

                    {question.options?.map((option, optionIndex) => (
                      <Form.Item
                        key={optionIndex}
                        label={`选项 ${optionIndex + 1}`}
                        labelCol={{ span: 4 }}
                        wrapperCol={{ span: 20 }}
                      >
                        <Input
                          value={option}
                          onChange={(e) => updateOption(levelIndex, questionIndex, optionIndex, e.target.value)}
                          placeholder={`输入选项 ${optionIndex + 1}`}
                          addonAfter={
                            <Radio
                              checked={question.answer === optionIndex}
                              onChange={() => updateQuestion(levelIndex, questionIndex, 'answer', optionIndex)}
                            >
                              正确答案
                            </Radio>
                          }
                        />
                      </Form.Item>
                    ))}
                  </Card>
                ))}

                {(!level.questions || level.questions.length === 0) && (
                  <Empty description="暂无问题" />
                )}
              </Card>

              <Card
                title={
                  <Space>
                    <TrophyOutlined />
                    <span>关卡奖励</span>
                  </Space>
                }
                size="small"
              >
                <Form.Item label="积分奖励">
                  <InputNumber
                    min={0}
                    value={level.reward?.points}
                    onChange={(value) => updateReward(levelIndex, 'points', value)}
                    style={{ width: '100%' }}
                  />
                </Form.Item>

                <Form.Item label="奖励描述">
                  <Input
                    value={level.reward?.description}
                    onChange={(e) => updateReward(levelIndex, 'description', e.target.value)}
                    placeholder="输入奖励描述"
                  />
                </Form.Item>
              </Card>
            </Space>
          </Panel>
        ))}
      </Collapse>

      <div style={{ marginTop: '16px', textAlign: 'center' }}>
        <Button
          type="dashed"
          onClick={addLevel}
          icon={<PlusOutlined />}
          style={{ width: '60%' }}
        >
          添加关卡
        </Button>
      </div>
    </div>
  );
};

export default AdventureGameConfig;
