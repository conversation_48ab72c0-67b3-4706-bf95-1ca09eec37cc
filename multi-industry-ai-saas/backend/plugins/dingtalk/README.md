# 钉钉机器人插件

## 架构说明

### 设计原则

钉钉机器人插件是一个**智能消息推送工具**，集成了AI功能。它的职责是：

1. **利用现有的钉钉账号绑定**：读取项目级别的`ThirdPartyAccount`表中的钉钉绑定信息
2. **提供智能消息推送功能**：通过钉钉API发送工作通知、群消息、单聊消息等
3. **AI功能增强**：集成系统AI模块，提供智能对话、自动回复等功能
4. **存储插件特有信息**：在`DingTalkUserMapping`中存储钉钉部门、角色等扩展信息

### 数据模型

#### 1. 第三方账号绑定（通用）
- **表名**：`third_party_accounts`
- **用途**：存储用户与第三方平台的绑定关系
- **管理方式**：通过项目级别的用户资料页面进行绑定/解绑
- **API路径**：`/api/project/{project_id}/users/bind/{platform}`

#### 2. 钉钉用户映射（插件特有）
- **表名**：`dingtalk_user_mappings`
- **用途**：存储钉钉插件特有的用户信息（部门、角色、详细信息等）
- **管理方式**：通过插件API同步钉钉用户详细信息
- **API路径**：`/api/v1/tenant/plugins/dingtalk/auth/sync-info`

### 功能特性

#### 1. 基础消息功能
- **工作通知**：发送个人工作通知
- **群组消息**：发送群聊消息
- **交互式卡片**：发送富文本卡片消息
- **Webhook消息**：支持机器人Webhook推送

#### 2. AI智能功能
- **智能对话**：与AI助手进行自然语言对话
- **自动回复**：基于AI的智能自动回复
- **上下文记忆**：维护对话历史和上下文
- **工具调用**：AI可以调用钉钉相关工具发送消息

#### 3. AI工具集
- **钉钉消息发送工具**：AI可以主动发送钉钉消息
- **用户信息查询工具**：AI可以查询钉钉用户信息
- **群组信息查询工具**：AI可以获取群组详情

### 工作流程

#### 1. 用户绑定钉钉账号
```
用户 -> 项目用户资料页面 -> 点击"绑定钉钉" -> 钉钉授权 -> 回调处理 -> 创建ThirdPartyAccount记录
```

#### 2. 插件同步用户信息
```
用户 -> 钉钉插件管理页面 -> 点击"同步信息" -> 调用钉钉API -> 创建/更新DingTalkUserMapping记录
```

#### 3. AI智能对话
```
用户 -> 钉钉插件AI聊天 -> 发送消息 -> AI助手处理 -> 生成回复 -> 发送到钉钉
```

#### 4. AI工具调用
```
用户提问 -> AI助手分析 -> 调用钉钉工具 -> 执行操作（发送消息/查询信息） -> 返回结果
```

### API接口

#### 用户信息管理
- `GET /auth/user-info` - 获取用户的钉钉绑定信息和插件扩展信息
- `POST /auth/sync-info` - 同步钉钉用户信息到插件扩展表
- `DELETE /auth/clear-mapping` - 清除钉钉插件用户映射（不影响账号绑定）

#### AI聊天功能
- `POST /ai/chat` - 与AI助手进行对话
- `POST /ai/send-to-dingtalk` - 将AI回复发送到钉钉
- `GET /ai/assistants` - 获取可用的AI助手列表
- `GET /ai/threads` - 获取用户的对话线程列表

#### 消息推送
- `POST /webhook/send` - 发送钉钉消息
- `GET /webhook/list` - 获取Webhook配置列表
- `POST /webhook/create` - 创建Webhook配置

### AI集成说明

#### 1. AI助手配置
插件会自动创建"钉钉智能助手"，具有以下特性：
- 专为钉钉场景优化的提示词
- 集成钉钉相关工具
- 支持上下文对话
- 可以主动发送消息到钉钉

#### 2. AI工具能力
AI助手可以调用以下钉钉工具：
- **发送消息**：向指定用户或群组发送消息
- **查询用户**：获取钉钉用户的详细信息
- **查询群组**：获取群组信息和成员列表

#### 3. 使用场景
- **智能客服**：自动回答常见问题
- **工作助手**：协助处理日常工作任务
- **信息查询**：快速查找用户和群组信息
- **消息推送**：智能化的消息分发

### 配置要求

1. **系统配置**：在项目设置中配置钉钉应用的AppKey和AppSecret
2. **插件安装**：在租户级别安装钉钉机器人插件
3. **用户绑定**：用户在个人资料页面绑定钉钉账号
4. **AI配置**：配置AI提供商和模型（可选）
5. **信息同步**：用户在插件页面同步详细信息（可选）

### 优势

1. **职责分离**：账号绑定和插件功能分离，避免重复
2. **统一管理**：所有第三方账号绑定在一个地方管理
3. **AI增强**：集成先进的AI功能，提供智能化体验
4. **扩展性强**：插件可以存储特有信息，不影响通用绑定
5. **数据一致性**：避免重复存储相同的绑定信息
6. **工具生态**：AI可以调用丰富的钉钉工具

### 注意事项

1. 用户必须先在项目用户资料页面绑定钉钉账号
2. AI功能需要配置有效的AI提供商和模型
3. 插件的用户映射是可选的，主要用于存储扩展信息
4. 解绑钉钉账号会影响所有使用该绑定的插件
5. 插件卸载不会影响用户的第三方账号绑定
6. AI工具调用需要相应的钉钉权限 