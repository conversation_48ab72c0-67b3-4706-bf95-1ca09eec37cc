#!/usr/bin/env python
# -*- coding: utf-8 -*-

from typing import List, Optional, Dict, Any, Union
from datetime import datetime
from uuid import UUID
from pydantic import BaseModel, Field

# 产品数据模型
class ProductData(BaseModel):
    id: Optional[UUID] = None
    name: str
    specification: Optional[str] = None
    price: float
    competitor_price: float
    price_difference: Optional[float] = None
    price_difference_percentage: Optional[float] = None
    notes: Optional[str] = None

# 促销活动数据模型
class PromotionData(BaseModel):
    promotion_name: str
    promotion_type: str
    promotion_discount: Optional[float] = None
    promotion_start_date: Optional[str] = None
    promotion_end_date: Optional[str] = None
    promotion_description: Optional[str] = None

# 基础周边调研模型
class AreaSurveyBase(BaseModel):
    title: str
    description: Optional[str] = None
    survey_type: str = "price"
    status: str = "draft"
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    target_store_name: str
    target_store_address: Optional[str] = None
    target_store_distance: Optional[float] = None
    target_store_type: Optional[str] = None
    survey_data: Optional[Dict[str, Any]] = None
    product_id: Optional[UUID] = None
    product_data: Optional[Dict[str, Any]] = None
    promotion_data: Optional[Dict[str, Any]] = None
    conclusion: Optional[str] = None
    recommendations: Optional[str] = None
    attachments: Optional[List[str]] = None
    tags: Optional[List[str]] = None
    rating: Optional[int] = None

# 创建周边调研请求模型
class AreaSurveyCreate(AreaSurveyBase):
    store_id: UUID
    project_id: Optional[UUID] = None  # 可选，会从路径中获取

# 更新周边调研请求模型
class AreaSurveyUpdate(BaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    survey_type: Optional[str] = None
    status: Optional[str] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    target_store_name: Optional[str] = None
    target_store_address: Optional[str] = None
    target_store_distance: Optional[float] = None
    target_store_type: Optional[str] = None
    survey_data: Optional[Dict[str, Any]] = None
    conclusion: Optional[str] = None
    recommendations: Optional[str] = None
    attachments: Optional[List[str]] = None
    tags: Optional[List[str]] = None
    rating: Optional[int] = None

# 周边调研状态更新请求模型
class AreaSurveyStatusUpdate(BaseModel):
    status: str

# 周边调研响应模型
class AreaSurveyResponse(AreaSurveyBase):
    id: UUID
    project_id: UUID
    store_id: UUID
    created_at: datetime
    updated_at: Optional[datetime] = None
    completed_date: Optional[datetime] = None
    created_by: Optional[UUID] = None
    updated_by: Optional[UUID] = None
    is_ai_analyzed: bool = False
    ai_analysis: Optional[Dict[str, Any]] = None

    # 额外信息
    store_name: Optional[str] = None
    creator_name: Optional[str] = None
    updater_name: Optional[str] = None

    # 产品和促销活动信息
    product_name: Optional[str] = None
    product_spec: Optional[str] = None
    product_price: Optional[float] = None
    competitor_price: Optional[float] = None
    price_difference: Optional[float] = None
    price_difference_percentage: Optional[float] = None

    promotion_name: Optional[str] = None
    promotion_type: Optional[str] = None
    promotion_discount: Optional[float] = None
    promotion_period: Optional[str] = None

    class Config:
        from_attributes = True

# 周边调研列表响应模型
class AreaSurveyListResponse(BaseModel):
    items: List[AreaSurveyResponse]
    total: int
    page: int
    page_size: int
    pages: int

# 简化的周边调研响应模型（用于统计数据中的最近调研列表）
class SimplifiedAreaSurveyResponse(BaseModel):
    id: UUID
    project_id: UUID
    store_id: UUID
    title: str
    status: str
    survey_type: str
    created_at: datetime
    target_store_name: Optional[str] = None
    store_name: Optional[str] = None
    creator_name: Optional[str] = None
    created_by: Optional[UUID] = None
    updated_by: Optional[UUID] = None
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True

# 周边调研统计响应模型
class AreaSurveyStatsResponse(BaseModel):
    total: int = 0
    draft: int = 0
    in_progress: int = 0
    completed: int = 0
    cancelled: int = 0
    by_type: Dict[str, int] = Field(default_factory=dict)
    by_rating: Dict[str, int] = Field(default_factory=dict)
    recent_surveys: List[SimplifiedAreaSurveyResponse] = Field(default_factory=list)

# AI分析请求模型
class AreaSurveyAIAnalysisRequest(BaseModel):
    survey_id: UUID

# AI分析响应模型
class AreaSurveyAIAnalysisResponse(BaseModel):
    survey_id: UUID
    analysis: Dict[str, Any]
    recommendations: List[str]
    insights: List[str]
    comparison: Optional[Dict[str, Any]] = None
