import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Button, 
  Space, 
  Tag, 
  Typography, 
  Descriptions, 
  Table, 
  Divider, 
  message, 
  Spin, 
  Empty,
  Tabs,
  Modal,
  Form,
  Input,
  DatePicker,
  Select,
  Row,
  Col,
  Statistic
} from 'antd';
import { 
  ArrowLeftOutlined, 
  EditOutlined, 
  DeleteOutlined, 
  PrinterOutlined, 
  FileTextOutlined, 
  DollarOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ShoppingOutlined,
  HomeOutlined
} from '@ant-design/icons';
import { useParams, useNavigate } from 'react-router-dom';
import dayjs from "dayjs";
import apiService from '../../../services/api';

const { Title, Text } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;

// 订单状态映射
const orderStatusMap = {
  'draft': { color: 'default', text: '草稿' },
  'confirmed': { color: 'processing', text: '已确认' },
  'received': { color: 'success', text: '已收货' },
  'cancelled': { color: 'error', text: '已取消' }
};

// 支付状态映射
const paymentStatusMap = {
  'unpaid': { color: 'default', text: '未支付' },
  'partial': { color: 'warning', text: '部分支付' },
  'paid': { color: 'success', text: '已支付' }
};

const PurchaseOrderDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  
  // 状态管理
  const [loading, setLoading] = useState(true);
  const [order, setOrder] = useState(null);
  const [activeTab, setActiveTab] = useState('1');
  const [statusModalVisible, setStatusModalVisible] = useState(false);
  const [paymentModalVisible, setPaymentModalVisible] = useState(false);
  const [deleteModalVisible, setDeleteModalVisible] = useState(false);
  const [statusForm] = Form.useForm();
  const [paymentForm] = Form.useForm();

  // 获取采购订单详情
  const fetchOrderDetail = async () => {
    setLoading(true);
    try {
      const response = await apiService.project.purchaseOrder.getDetail(id);
      if (response) {
        setOrder(response);
      } else {
        message.error('获取采购订单详情失败');
      }
    } catch (error) {
      console.error('获取采购订单详情失败:', error);
      message.error('获取采购订单详情失败: ' + (error.response?.data?.detail || error.message));
    } finally {
      setLoading(false);
    }
  };

  // 返回列表
  const handleBack = () => {
    navigate('/project/purchase/orders');
  };

  // 编辑订单
  const handleEdit = () => {
    navigate(`/project/purchase/orders/edit/${id}`);
  };

  // 删除订单
  const handleDelete = async () => {
    setLoading(true);
    try {
      await apiService.project.purchaseOrder.delete(id);
      message.success('采购订单删除成功');
      navigate('/project/purchase/orders');
    } catch (error) {
      console.error('删除采购订单失败:', error);
      message.error('删除采购订单失败: ' + (error.response?.data?.detail || error.message));
    } finally {
      setLoading(false);
    }
  };

  // 更新订单状态
  const handleUpdateStatus = async () => {
    try {
      const values = await statusForm.validateFields();
      setLoading(true);
      
      await apiService.project.purchaseOrder.updateStatus(id, values);
      
      message.success('订单状态更新成功');
      setStatusModalVisible(false);
      fetchOrderDetail();
    } catch (error) {
      console.error('更新订单状态失败:', error);
      if (error.errorFields) {
        // 表单验证错误
        return;
      }
      message.error('更新订单状态失败: ' + (error.response?.data?.detail || error.message));
    } finally {
      setLoading(false);
    }
  };

  // 更新支付状态
  const handleUpdatePayment = async () => {
    try {
      const values = await paymentForm.validateFields();
      setLoading(true);
      
      await apiService.project.purchaseOrder.updatePayment(id, values);
      
      message.success('支付状态更新成功');
      setPaymentModalVisible(false);
      fetchOrderDetail();
    } catch (error) {
      console.error('更新支付状态失败:', error);
      if (error.errorFields) {
        // 表单验证错误
        return;
      }
      message.error('更新支付状态失败: ' + (error.response?.data?.detail || error.message));
    } finally {
      setLoading(false);
    }
  };

  // 打印订单
  const handlePrint = () => {
    window.print();
  };

  // 初始化
  useEffect(() => {
    if (id) {
      fetchOrderDetail();
    }
  }, [id]);

  // 订单项表格列定义
  const itemColumns = [
    {
      title: '产品名称',
      dataIndex: 'product_name',
      key: 'product_name',
    },
    {
      title: '产品编码',
      dataIndex: 'product_code',
      key: 'product_code',
    },
    {
      title: '供应商',
      dataIndex: 'supplier_name',
      key: 'supplier_name',
      render: (text) => text || '未指定'
    },
    {
      title: '规格',
      dataIndex: 'product_specification',
      key: 'product_specification',
    },
    {
      title: '单位',
      dataIndex: 'product_unit',
      key: 'product_unit',
    },
    {
      title: '数量',
      dataIndex: 'quantity',
      key: 'quantity',
    },
    {
      title: '单价',
      dataIndex: 'unit_price',
      key: 'unit_price',
      render: (text) => `¥${parseFloat(text || 0).toFixed(2)}`,
    },
    {
      title: '折扣率',
      dataIndex: 'discount_rate',
      key: 'discount_rate',
      render: (text) => `${text}%`,
    },
    {
      title: '税率',
      dataIndex: 'tax_rate',
      key: 'tax_rate',
      render: (text) => `${text}%`,
    },
    {
      title: '总金额',
      dataIndex: 'total_amount',
      key: 'total_amount',
      render: (text) => `¥${parseFloat(text || 0).toFixed(2)}`,
    },
    {
      title: '已收货数量',
      dataIndex: 'received_quantity',
      key: 'received_quantity',
    },
  ];

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
      </div>
    );
  }

  if (!order) {
    return (
      <Empty 
        description="采购订单不存在或已被删除" 
        style={{ margin: '50px 0' }}
      >
        <Button type="primary" onClick={handleBack}>返回列表</Button>
      </Empty>
    );
  }

  return (
    <div className="purchase-order-detail-page">
      <Card>
        <div style={{ marginBottom: 16 }}>
          <Space>
            <Button icon={<ArrowLeftOutlined />} onClick={handleBack}>返回</Button>
            {order.status === 'draft' && (
              <>
                <Button type="primary" icon={<EditOutlined />} onClick={handleEdit}>编辑</Button>
                <Button danger icon={<DeleteOutlined />} onClick={() => setDeleteModalVisible(true)}>删除</Button>
              </>
            )}
            <Button icon={<FileTextOutlined />} onClick={() => {
              statusForm.setFieldsValue({
                status: order.status,
                actual_delivery_date: order.actual_delivery_date ? dayjs(order.actual_delivery_date) : null,
                notes: order.notes
              });
              setStatusModalVisible(true);
            }}>更新状态</Button>
            <Button icon={<DollarOutlined />} onClick={() => {
              paymentForm.setFieldsValue({
                payment_status: order.payment_status,
                payment_method: order.payment_method,
                payment_date: order.payment_date ? dayjs(order.payment_date) : null,
                notes: order.notes
              });
              setPaymentModalVisible(true);
            }}>更新支付</Button>
            <Button icon={<PrinterOutlined />} onClick={handlePrint}>打印</Button>
          </Space>
        </div>

        <div className="order-header" style={{ marginBottom: 24 }}>
          <Title level={4}>采购订单详情</Title>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Text strong>订单编号: {order.order_number}</Text>
            <Space>
              <Tag color={orderStatusMap[order.status]?.color || 'default'}>
                {orderStatusMap[order.status]?.text || order.status}
              </Tag>
              <Tag color={paymentStatusMap[order.payment_status]?.color || 'default'}>
                {paymentStatusMap[order.payment_status]?.text || order.payment_status}
              </Tag>
            </Space>
          </div>
        </div>

        <Row gutter={16} style={{ marginBottom: 24 }}>
          <Col span={12}>
            <Card>
              <Statistic 
                title="仓库" 
                value={order.warehouse_name || '未指定'} 
                prefix={<HomeOutlined />} 
                valueStyle={{ fontSize: '16px' }}
              />
            </Card>
          </Col>
          <Col span={12}>
            <Card>
              <Statistic 
                title="总金额" 
                value={parseFloat(order.final_amount || 0).toFixed(2)} 
                prefix="¥" 
                precision={2}
              />
            </Card>
          </Col>
        </Row>

        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab="基本信息" key="1">
            <Descriptions bordered column={2}>
              <Descriptions.Item label="订单日期">
                {order.order_date ? dayjs(order.order_date).format('YYYY-MM-DD') : '-'}
              </Descriptions.Item>
              <Descriptions.Item label="预计交货日期">
                {order.expected_delivery_date ? dayjs(order.expected_delivery_date).format('YYYY-MM-DD') : '-'}
              </Descriptions.Item>
              <Descriptions.Item label="实际交货日期">
                {order.actual_delivery_date ? dayjs(order.actual_delivery_date).format('YYYY-MM-DD') : '-'}
              </Descriptions.Item>
              <Descriptions.Item label="支付方式">
                {order.payment_method || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="支付日期">
                {order.payment_date ? dayjs(order.payment_date).format('YYYY-MM-DD') : '-'}
              </Descriptions.Item>
              <Descriptions.Item label="创建人">
                {order.creator_name || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="创建时间">
                {order.created_at ? dayjs(order.created_at).format('YYYY-MM-DD HH:mm:ss') : '-'}
              </Descriptions.Item>
              <Descriptions.Item label="更新时间">
                {order.updated_at ? dayjs(order.updated_at).format('YYYY-MM-DD HH:mm:ss') : '-'}
              </Descriptions.Item>
              <Descriptions.Item label="备注" span={2}>
                {order.notes || '-'}
              </Descriptions.Item>
            </Descriptions>
            
            <Divider orientation="left">金额信息</Divider>
            
            <Descriptions bordered column={2}>
              <Descriptions.Item label="商品总金额">¥{parseFloat(order.total_amount || 0).toFixed(2)}</Descriptions.Item>
              <Descriptions.Item label="折扣金额">¥{parseFloat(order.discount_amount || 0).toFixed(2)}</Descriptions.Item>
              <Descriptions.Item label="税额">¥{parseFloat(order.tax_amount || 0).toFixed(2)}</Descriptions.Item>
              <Descriptions.Item label="运费">¥{parseFloat(order.shipping_fee || 0).toFixed(2)}</Descriptions.Item>
              <Descriptions.Item label="最终金额" span={2}>
                <Text strong style={{ fontSize: '16px', color: '#f50' }}>
                  ¥{parseFloat(order.final_amount || 0).toFixed(2)}
                </Text>
              </Descriptions.Item>
            </Descriptions>
          </TabPane>
          
          <TabPane tab="订单项" key="2">
            <Table
              columns={itemColumns}
              dataSource={order.items}
              rowKey="id"
              pagination={false}
              summary={() => (
                <Table.Summary fixed>
                  <Table.Summary.Row>
                    <Table.Summary.Cell index={0} colSpan={5}><Text strong>合计</Text></Table.Summary.Cell>
                    <Table.Summary.Cell index={1} colSpan={3}></Table.Summary.Cell>
                    <Table.Summary.Cell index={2}>
                      <Text strong>¥{parseFloat(order.total_amount || 0).toFixed(2)}</Text>
                    </Table.Summary.Cell>
                    <Table.Summary.Cell index={3}></Table.Summary.Cell>
                  </Table.Summary.Row>
                </Table.Summary>
              )}
            />
          </TabPane>
        </Tabs>
      </Card>

      {/* 更新订单状态弹窗 */}
      <Modal
        title="更新订单状态"
        open={statusModalVisible}
        onOk={handleUpdateStatus}
        onCancel={() => setStatusModalVisible(false)}
        confirmLoading={loading}
      >
        <Form
          form={statusForm}
          layout="vertical"
        >
          <Form.Item
            name="status"
            label="订单状态"
            rules={[{ required: true, message: '请选择订单状态' }]}
          >
            <Select>
              <Option value="draft">草稿</Option>
              <Option value="confirmed">已确认</Option>
              <Option value="received">已收货</Option>
              <Option value="cancelled">已取消</Option>
            </Select>
          </Form.Item>
          
          <Form.Item
            name="actual_delivery_date"
            label="实际交货日期"
          >
            <DatePicker style={{ width: '100%' }} />
          </Form.Item>
          
          <Form.Item
            name="notes"
            label="备注"
          >
            <Input.TextArea rows={4} />
          </Form.Item>
        </Form>
      </Modal>

      {/* 更新支付状态弹窗 */}
      <Modal
        title="更新支付状态"
        open={paymentModalVisible}
        onOk={handleUpdatePayment}
        onCancel={() => setPaymentModalVisible(false)}
        confirmLoading={loading}
      >
        <Form
          form={paymentForm}
          layout="vertical"
        >
          <Form.Item
            name="payment_status"
            label="支付状态"
            rules={[{ required: true, message: '请选择支付状态' }]}
          >
            <Select>
              <Option value="unpaid">未支付</Option>
              <Option value="partial">部分支付</Option>
              <Option value="paid">已支付</Option>
            </Select>
          </Form.Item>
          
          <Form.Item
            name="payment_method"
            label="支付方式"
          >
            <Select allowClear>
              <Option value="cash">现金</Option>
              <Option value="bank_transfer">银行转账</Option>
              <Option value="credit_card">信用卡</Option>
              <Option value="alipay">支付宝</Option>
              <Option value="wechat">微信支付</Option>
              <Option value="other">其他</Option>
            </Select>
          </Form.Item>
          
          <Form.Item
            name="payment_date"
            label="支付日期"
          >
            <DatePicker style={{ width: '100%' }} />
          </Form.Item>
          
          <Form.Item
            name="notes"
            label="备注"
          >
            <Input.TextArea rows={4} />
          </Form.Item>
        </Form>
      </Modal>

      {/* 删除确认弹窗 */}
      <Modal
        title="删除采购订单"
        open={deleteModalVisible}
        onOk={handleDelete}
        onCancel={() => setDeleteModalVisible(false)}
        confirmLoading={loading}
      >
        <p>确定要删除此采购订单吗？此操作不可恢复。</p>
        <div>
          <p><strong>订单编号:</strong> {order.order_number}</p>
          <p><strong>供应商:</strong> {order.supplier_name}</p>
          <p><strong>总金额:</strong> ¥{parseFloat(order.final_amount || 0).toFixed(2)}</p>
        </div>
      </Modal>
    </div>
  );
};

export default PurchaseOrderDetail;
