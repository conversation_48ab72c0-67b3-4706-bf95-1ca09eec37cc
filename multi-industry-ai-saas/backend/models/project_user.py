from sqlalchemy import Column, <PERSON>, <PERSON><PERSON><PERSON>, DateTime, ForeignKey, JSON, Text, Integer, UniqueConstraint
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid

from db.database import Base

class ProjectUser(Base):
    """项目用户关联模型"""
    __tablename__ = "project_users"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id"), nullable=False)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    role_id = Column(UUID(as_uuid=True), ForeignKey("roles.id"), nullable=True)
    is_admin = Column(Boolean, default=False)  # 是否项目管理员
    status = Column(String(20), default="active", nullable=False)  # active, inactive, suspended
    settings = Column(JSON, default=dict)  # 用户在项目中的设置
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 关联关系
    project = relationship("Project", back_populates="project_users")
    user = relationship("User", back_populates="project_users")
    role = relationship("Role", back_populates="project_users")
    project_user_stores = relationship("ProjectUserStore", back_populates="project_user", cascade="all, delete-orphan")

    __table_args__ = (
        UniqueConstraint('project_id', 'user_id', name='uq_project_user'),
    )

    def __repr__(self):
        return f"<ProjectUser(id={self.id}, project_id={self.project_id}, user_id={self.user_id})>"
