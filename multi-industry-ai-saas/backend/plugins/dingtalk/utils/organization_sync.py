#!/usr/bin/env python
# -*- coding: utf-8 -*-

import logging
import uuid
import asyncio
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, update, delete, func
from sqlalchemy.orm import selectinload

from ..utils.dingtalk_api import DingTalkAPI
from ..models.models import (
    DingTalkDepartment,
    DingTalkUserMapping, 
    DingTalkSettings
)
from models.user import User, ThirdPartyAccount
from models.project_user import ProjectUser
from models.role import Role
from models.store import Store, ProjectUserStore
from models.project import Project

class DingTalkOrganizationSyncService:
    """钉钉组织架构同步服务 - 插件内部存储版"""
    
    def __init__(self, dingtalk_api: DingTalkAPI, db: AsyncSession):
        self.dingtalk_api = dingtalk_api
        self.db = db
        self.logger = logging.getLogger(__name__)

    async def sync_organization(self, tenant_id: uuid.UUID, project_id: uuid.UUID, 
                              sync_options: dict = None) -> dict:
        """
        同步钉钉组织架构到插件内部存储
        
        Args:
            tenant_id: 租户ID
            project_id: 项目ID
            sync_options: 同步选项
                - sync_type: 同步类型 (full, incremental, specific_dept)
                - target_dept_ids: 目标部门ID列表（specific_dept模式）
                - include_sub_depts: 是否包含子部门
                - sync_inactive_users: 是否同步非活跃用户
        
        Returns:
            dict: 同步结果
        """
        self.logger.info("🔄 开始同步钉钉组织架构到插件内部存储")
        
        sync_options = sync_options or {}
        sync_type = sync_options.get("sync_type", "full")
        
        result = {
            "success": True,
            "sync_type": sync_type,
            "departments_synced": 0,
            "users_synced": 0,
            "existing_users_updated": 0,
            "new_users_added": 0,
            "total_dingtalk_users": 0,
            "unmatched_users": 0,
            "errors": [],
            "details": {}
        }
        
        try:
            # 1. 同步部门结构
            dept_result = await self._sync_departments(tenant_id, project_id, sync_options)
            result["departments_synced"] = dept_result.get("synced_count", 0)
            result["details"]["departments"] = dept_result
            
            # 2. 同步用户信息
            user_result = await self._sync_users(tenant_id, project_id, sync_options)
            result["users_synced"] = user_result.get("synced_count", 0)
            result["existing_users_updated"] = user_result.get("updated_count", 0)
            result["new_users_added"] = user_result.get("new_count", 0)
            result["total_dingtalk_users"] = user_result.get("total_count", 0)
            result["unmatched_users"] = user_result.get("unmatched_users", 0)
            result["details"]["users"] = user_result
            
            # 收集错误
            result["errors"].extend(dept_result.get("errors", []))
            result["errors"].extend(user_result.get("errors", []))
            
            if result["errors"]:
                result["success"] = False
                
            await self.db.commit()
            
            self.logger.info(f"✅ 组织架构同步完成")
            self.logger.info(f"   📁 部门：{result['departments_synced']}个")
            self.logger.info(f"   👥 用户：{result['users_synced']}个（新增{result['new_users_added']}，更新{result['existing_users_updated']}，{result['unmatched_users']}个未绑定）")
            
            return result
            
        except Exception as e:
            await self.db.rollback()
            error_msg = f"组织架构同步失败: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            result["success"] = False
            result["errors"].append(error_msg)
        return result
    
    async def full_sync_with_user_creation(self, tenant_id: uuid.UUID, project_id: uuid.UUID, 
                                         sync_options: dict = None) -> dict:
        """
        完整同步组织架构并创建项目用户
        
        Args:
            tenant_id: 租户ID
            project_id: 项目ID
            sync_options: 同步选项
                - create_project_users: 是否创建项目用户
                - default_role_id: 默认角色ID
                - default_store_id: 默认门店ID
        
        Returns:
            dict: 同步结果
        """
        self.logger.info("🔄 开始完整组织架构同步（包含用户创建）")
        
        sync_options = sync_options or {}
        create_project_users = sync_options.get("create_project_users", False)
        
        try:
            # 1. 执行基础组织架构同步
            base_result = await self.sync_organization(tenant_id, project_id, sync_options)
            
            result = {
                "success": base_result["success"],
                "message": "组织架构同步完成",
                "data": {
                    "organization_sync": base_result,
                    "project_users_created": 0,
                    "project_users_errors": []
                }
            }
            
            return result
            
        except Exception as e:
            error_msg = f"完整组织架构同步失败: {str(e)}"
            self.logger.error(error_msg)
            return {
                "success": False,
                "message": error_msg,
                "data": {"errors": [error_msg]}
            }
    
    async def _sync_departments(self, tenant_id: uuid.UUID, project_id: uuid.UUID, 
                              sync_options: dict) -> dict:
        """同步部门结构到插件内部存储"""
        self.logger.info("📁 开始同步部门结构")
        
        result = {
            "success": True,
            "synced_count": 0,
            "updated_count": 0,
            "new_count": 0,
            "errors": []
        }
        
        try:
            sync_type = sync_options.get("sync_type", "full")
            target_dept_ids = sync_options.get("target_dept_ids", [])
            include_sub_depts = sync_options.get("include_sub_depts", True)
            
            # 根据同步类型获取部门
            if sync_type == "specific_dept" and target_dept_ids:
                self.logger.info(f"📋 按部门同步，目标部门: {target_dept_ids}")
                departments = []
                
                # 获取指定部门
                for dept_id in target_dept_ids:
                    try:
                        dept_detail = await self.dingtalk_api.get_department_detail(int(dept_id))
                        if dept_detail:
                            departments.append(dept_detail)
                    except Exception as e:
                        self.logger.warning(f"获取部门{dept_id}详情失败: {str(e)}")
                
                # 如果包含子部门，递归获取
                if include_sub_depts:
                    all_sub_depts = []
                    for dept_id in target_dept_ids:
                        try:
                            sub_depts = await self.dingtalk_api.get_department_list(int(dept_id))
                            if sub_depts:
                                all_sub_depts.extend(sub_depts)
                        except Exception as e:
                            self.logger.warning(f"获取部门{dept_id}子部门失败: {str(e)}")
                    departments.extend(all_sub_depts)
                
                self.logger.info(f"📋 按部门同步获取到 {len(departments)} 个部门")
            else:
                # 全量同步或增量同步
                departments = await self.dingtalk_api.get_all_departments()
                if not departments:
                    self.logger.warning("未获取到钉钉部门数据")
                    return result
                
                self.logger.info(f"📋 {sync_type}同步获取到 {len(departments)} 个钉钉部门")
            
            # 构建部门路径映射
            dept_path_map = self._build_department_paths(departments)
            
            # 获取现有部门
            existing_query = select(DingTalkDepartment).where(
                DingTalkDepartment.tenant_id == tenant_id,
                DingTalkDepartment.project_id == project_id
            )
            existing_result = await self.db.execute(existing_query)
            existing_depts = {dept.dept_id: dept for dept in existing_result.scalars().all()}
            
            for dept_data in departments:
                try:
                    dept_id = str(dept_data.get("dept_id", dept_data.get("id")))
                    name = dept_data.get("name", "")
                    parent_id = dept_data.get("parent_id", dept_data.get("parentid"))
                    
                    if parent_id:
                        parent_id = str(parent_id)
                    
                    dept_path = dept_path_map.get(dept_id, name)
                    level = self._calculate_department_level(dept_data, departments)
                    
                    if dept_id in existing_depts:
                        # 更新现有部门
                        existing_dept = existing_depts[dept_id]
                        existing_dept.name = name
                        existing_dept.parent_id = parent_id
                        existing_dept.dept_path = dept_path
                        existing_dept.level = level
                        existing_dept.updated_at = datetime.now()
                        result["updated_count"] += 1
                    else:
                        # 创建新部门
                        new_dept = DingTalkDepartment(
                            id=uuid.uuid4(),
                            tenant_id=tenant_id,
                            project_id=project_id,
                            dept_id=dept_id,
                            name=name,
                            parent_id=parent_id,
                            dept_path=dept_path,
                            level=level,
                            order=dept_data.get("order", 0),
                            dept_manager_userid_list=dept_data.get("dept_manager_userid_list", []),
                            dept_hiding=dept_data.get("dept_hiding", False),
                            outer_dept=dept_data.get("outer_dept", False),
                            source_identifier=dept_data.get("source_identifier"),
                            created_at=datetime.now(),
                            updated_at=datetime.now()
                        )
                        self.db.add(new_dept)
                        result["new_count"] += 1
                    
                    result["synced_count"] += 1

                except Exception as e:
                    error_msg = f"处理部门数据 {dept_data.get('id')} 失败: {str(e)}"
                    self.logger.error(error_msg, exc_info=True)
                    result["errors"].append(error_msg)
        
        except Exception as e:
            error_msg = f"同步部门信息失败: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            result["errors"].append(error_msg)
            result["success"] = False

        self.logger.info(f"📁 部门同步完成：新增{result['new_count']}，更新{result['updated_count']}，总计{result['synced_count']}")
        return result

    async def _sync_users(self, tenant_id: uuid.UUID, project_id: uuid.UUID, 
                         sync_options: dict) -> dict:
        """同步用户到插件内部存储，并关联系统用户"""
        self.logger.info("👥 开始同步用户信息")

        result = {
            "success": True,
            "total_count": 0,
            "synced_count": 0,
            "updated_count": 0,
            "new_count": 0,
            "unmatched_users": 0,
            "errors": []
        }

        try:
            # 获取所有部门的用户
            all_users_data = await self.dingtalk_api.get_all_users()
            if not all_users_data:
                self.logger.warning("未获取到钉钉用户数据")
                return result
            
            result["total_count"] = len(all_users_data)
            self.logger.info(f"📋 从钉钉获取到 {len(all_users_data)} 个用户")

            # 关键步骤：获取系统中所有钉钉的第三方绑定记录
            stmt = (
                select(ThirdPartyAccount)
                .where(
                    ThirdPartyAccount.tenant_id == tenant_id,
                    ThirdPartyAccount.provider == 'dingtalk'
                )
            )
            tp_result = await self.db.execute(stmt)
            # 使用 unionid 作为 key
            third_party_accounts = {tp.platform_user_id: tp for tp in tp_result.scalars().all()}
            self.logger.info(f"🔍 在系统中查询到 {len(third_party_accounts)} 个已绑定的钉钉账号")

            # 获取现有用户映射
            existing_query = select(DingTalkUserMapping).where(
                DingTalkUserMapping.tenant_id == tenant_id,
                DingTalkUserMapping.project_id == project_id
            )
            existing_result = await self.db.execute(existing_query)
            # 使用钉钉 userid 作为 key
            existing_users_map = {user.dingtalk_user_id: user for user in existing_result.scalars().all()}

            for user_data in all_users_data:
                try:
                    userid = user_data.get("userid")
                    unionid = user_data.get("unionid")
                    
                    if not userid or not unionid:
                        self.logger.warning(f"跳过不完整的用户数据: {user_data}")
                        continue

                    # 检查此钉钉用户是否已在系统中绑定
                    matched_user_id = None
                    if unionid in third_party_accounts:
                        matched_user_id = third_party_accounts[unionid].user_id
                        self.logger.info(f"✅ 匹配成功: 钉钉用户 {user_data.get('name')} (UnionID: {unionid}) -> 系统用户ID: {matched_user_id}")
                    else:
                        result["unmatched_users"] += 1
                        self.logger.debug(f"ℹ️ 未匹配: 钉钉用户 {user_data.get('name')} (UnionID: {unionid}) 在系统中无绑定记录")

                    if userid in existing_users_map:
                        # 更新现有用户
                        existing_user = existing_users_map[userid]
                        existing_user.user_id = matched_user_id # 更新或设置系统用户ID
                        existing_user.dingtalk_union_id = unionid
                        existing_user.dingtalk_name = user_data.get("name")
                        existing_user.dingtalk_mobile = user_data.get("mobile")
                        existing_user.dingtalk_email = user_data.get("email")
                        existing_user.dingtalk_avatar = user_data.get("avatar")
                        existing_user.dingtalk_job_number = user_data.get("job_number")
                        existing_user.dingtalk_title = user_data.get("title")
                        existing_user.is_active = user_data.get("active", True)
                        existing_user.updated_at = datetime.now()
                        result["updated_count"] += 1
                    else:
                        # 创建新用户
                        new_user = DingTalkUserMapping(
                            id=uuid.uuid4(),
                            tenant_id=tenant_id,
                            project_id=project_id,
                            user_id=matched_user_id, # 设置系统用户ID（如果匹配到）
                            dingtalk_user_id=userid,
                            dingtalk_union_id=unionid,
                            dingtalk_name=user_data.get("name"),
                            dingtalk_mobile=user_data.get("mobile"),
                            dingtalk_email=user_data.get("email"),
                            dingtalk_avatar=user_data.get("avatar"),
                            dingtalk_job_number=user_data.get("job_number"),
                            dingtalk_title=user_data.get("title"),
                            is_active=user_data.get("active", True),
                        )
                        self.db.add(new_user)
                        result["new_count"] += 1

                    result["synced_count"] += 1
                
                except Exception as e:
                    error_msg = f"处理用户数据 {user_data.get('userid')} 失败: {str(e)}"
                    self.logger.error(error_msg, exc_info=True)
                    result["errors"].append(error_msg)
        
        except Exception as e:
            error_msg = f"同步用户信息失败: {str(e)}"
            self.logger.error(error_msg, exc_info=True)
            result["errors"].append(error_msg)
            result["success"] = False

        return result

    async def get_organization_status(self, tenant_id: uuid.UUID, project_id: uuid.UUID) -> dict:
        """获取组织架构同步状态和统计信息"""
        stats_query = select(
            func.count(DingTalkUserMapping.id),
            func.count(func.distinct(DingTalkUserMapping.user_id))
        ).where(
            DingTalkUserMapping.tenant_id == tenant_id,
            DingTalkUserMapping.project_id == project_id
        )
        total_users, linked_users = (await self.db.execute(stats_query)).one()

        dept_query = select(func.count(DingTalkDepartment.id)).where(
            DingTalkDepartment.tenant_id == tenant_id,
            DingTalkDepartment.project_id == project_id
        )
        total_departments = (await self.db.execute(dept_query)).scalar_one_or_none() or 0
        
        # 获取最近同步的10个部门
        recent_depts_query = select(DingTalkDepartment).where(
            DingTalkDepartment.tenant_id == tenant_id,
            DingTalkDepartment.project_id == project_id
        ).order_by(DingTalkDepartment.updated_at.desc()).limit(10)
        
        recent_departments = (await self.db.execute(recent_depts_query)).scalars().all()
        
        return {
            "statistics": {
                "total_departments": total_departments,
                "total_users": total_users,
                "linked_users": linked_users,
                "unlinked_users": total_users - linked_users,
                "link_rate": f"{(linked_users / total_users * 100) if total_users > 0 else 0:.2f}%"
            },
            "recent_departments": [
                {
                    "id": dept.id, "name": dept.name, "dept_id": dept.dept_id, 
                    "parent_id": dept.parent_id, "updated_at": dept.updated_at
                } for dept in recent_departments
            ]
        }
        
    async def get_plugin_organization_tree(self, tenant_id: uuid.UUID, project_id: uuid.UUID) -> dict:
        """获取插件视角的组织架构树"""
        # 获取所有部门
        dept_query = select(DingTalkDepartment).where(
            DingTalkDepartment.tenant_id == tenant_id,
            DingTalkDepartment.project_id == project_id
        )
        departments = (await self.db.execute(dept_query)).scalars().all()

        # 获取所有用户
        user_query = select(DingTalkUserMapping).where(
            DingTalkUserMapping.tenant_id == tenant_id,
            DingTalkUserMapping.project_id == project_id
        )
        users = (await self.db.execute(user_query)).scalars().all()

        tree = self._build_organization_tree(departments, users)
        
        # 获取所有部门（用于前端构建完整树）
        all_departments = [
            {
                "id": dept.dept_id,
                "key": dept.dept_id,
                "title": dept.name,
                "value": dept.dept_id,
                "parent_id": dept.parent_id,
            }
            for dept in departments
        ]
        
        return {
            "tree": tree,
            "users": [
                {
                    "dingtalk_user_id": user.dingtalk_user_id,
                    "dingtalk_name": user.dingtalk_name,
                    "dingtalk_avatar": user.dingtalk_avatar,
                    "title": user.dingtalk_title,
                    "is_active": user.is_active,
                    "user_id": user.user_id, # 关联的系统用户ID
                    "dept_ids": user.dingtalk_dept_id, # 使用单个部门ID代替
                }
                for user in users
            ],
            "allDepartments": all_departments, # 新增，返回所有部门
        }

    def _build_department_paths(self, departments: List[Dict[str, Any]]) -> Dict[str, str]:
        """构建部门ID到完整路径的映射"""
        dept_map = {str(d.get("dept_id", d.get("id"))): d for d in departments}
        path_cache = {}

        def build_path(dept_id: str) -> str:
            if dept_id in path_cache:
                return path_cache[dept_id]
            
            dept = dept_map.get(dept_id)
            if not dept:
                return ""
            
            name = dept.get("name", "")
            parent_id = dept.get("parent_id", dept.get("parentid"))
            if parent_id and str(parent_id) != "1": # 钉钉根部门ID通常是1
                parent_path = build_path(str(parent_id))
                path = f"{parent_path} / {name}" if parent_path else name
            else:
                path = name
            
            path_cache[dept_id] = path
            return path

        for dept_id in dept_map.keys():
            build_path(dept_id)
        
        return path_cache

    def _calculate_department_level(self, dept: Dict[str, Any], all_departments: List[Dict[str, Any]]) -> int:
        """计算部门层级"""
        dept_map = {str(d.get("dept_id", d.get("id"))): d for d in all_departments}
        
        def get_level(dept_id: str, visited: set = None) -> int:
            if visited is None:
                visited = set()
            if dept_id in visited:
                return 1 # 环路检测
            visited.add(dept_id)

            current_dept = dept_map.get(str(dept_id))
            if not current_dept or not current_dept.get("parent_id") or str(current_dept.get("parent_id")) == "1":
                return 1
            
            parent_id = str(current_dept.get("parent_id"))
            return 1 + get_level(parent_id, visited)

        dept_id = str(dept.get("dept_id", dept.get("id")))
        return get_level(dept_id)

    def _build_organization_tree(self, departments: List[DingTalkDepartment], 
                                users: List[DingTalkUserMapping]) -> List[dict]:
        """构建组织架构树"""
        dept_map = {dept.dept_id: {
            "key": dept.dept_id,
            "value": dept.dept_id,
            "title": dept.name,
            "children": [],
            "users": []
        } for dept in departments}

        user_map = {}
        for user in users:
            # A user might be associated with a single department in this model
            if user.dingtalk_dept_id:
                if user.dingtalk_dept_id not in user_map:
                    user_map[user.dingtalk_dept_id] = []
                user_map[user.dingtalk_dept_id].append({
                    "key": user.dingtalk_user_id,
                    "title": user.dingtalk_name,
                    "avatar": user.dingtalk_avatar,
                    "is_user": True,
                    "user_id": user.user_id,
                    "is_active": user.is_active,
                })

        for dept_id, user_list in user_map.items():
            if dept_id in dept_map:
                dept_map[dept_id]["users"].extend(user_list)

        tree = []
        for dept in departments:
            if dept.parent_id and dept.parent_id in dept_map:
                parent_node = dept_map[dept.parent_id]
                parent_node["children"].append(dept_map[dept.dept_id])
            # 钉钉的根部门ID通常是'1'
            elif not dept.parent_id or str(dept.parent_id) == "1":
                tree.append(dept_map[dept.dept_id])
        
        # 处理没有父部门的孤儿部门
        all_parent_ids = {d.parent_id for d in departments if d.parent_id}
        for dept in departments:
            if dept.dept_id not in all_parent_ids and str(dept.parent_id) != "1":
                 # 确保它不在树中
                if not any(d['key'] == dept.dept_id for d in tree):
                    # 如果它的父ID不为1但父节点不存在，也视为顶层
                    if dept.parent_id and dept.parent_id not in dept_map:
                         tree.append(dept_map[dept.dept_id])


        return tree

    def _generate_username(self, mobile: str, email: str, userid: str) -> str:
        """
        生成唯一的系统用户名
        - 优先使用手机号
        - 其次使用邮箱前缀
        - 最后使用钉钉userid
        """
        if mobile:
            return mobile
        if email:
            return email.split('@')[0]
        return f"ding_{userid}"

    async def create_project_users_from_dingtalk(
        self, 
        tenant_id: uuid.UUID, 
        project_id: uuid.UUID, 
        dingtalk_user_ids: List[str],
        role_id: str,
        store_id: Optional[str] = None,
        created_by: uuid.UUID = None
    ) -> Dict[str, Any]:
        """从钉钉用户创建项目用户"""
        
        self.logger.info(f"准备从钉钉创建项目用户: {dingtalk_user_ids}")
        
        result = {
            "success": True,
            "created_count": 0,
            "skipped_count": 0,
            "failed_count": 0,
            "details": [],
            "errors": []
        }
        
        if not dingtalk_user_ids:
            result["success"] = False
            result["errors"].append("未提供任何钉钉用户ID")
            return result

        # 检查角色是否存在
        role = await self.db.get(Role, role_id)
        if not role:
            result["success"] = False
            result["errors"].append(f"角色ID '{role_id}' 不存在")
            return result
        
        # 检查门店是否存在（如果提供）
        store = None
        if store_id:
            store = await self.db.get(Store, store_id)
            if not store:
                result["success"] = False
                result["errors"].append(f"门店ID '{store_id}' 不存在")
                return result

        # 获取钉钉用户映射信息
        user_mapping_query = select(DingTalkUserMapping).where(
            DingTalkUserMapping.tenant_id == tenant_id,
            DingTalkUserMapping.project_id == project_id,
            DingTalkUserMapping.dingtalk_user_id.in_(dingtalk_user_ids)
        )
        user_mappings_result = await self.db.execute(user_mapping_query)
        user_mappings = user_mappings_result.scalars().all()

        if not user_mappings:
            result["success"] = False
            result["errors"].append("找不到任何匹配的钉钉用户映射记录")
            return result

        project_name_query = select(Project.name).where(Project.id == project_id)
        project_name = (await self.db.execute(project_name_query)).scalar_one_or_none() or "您的项目"

        for mapping in user_mappings:
            try:
                # 检查是否已关联系统用户
                if mapping.user_id:
                    self.logger.info(f"用户 {mapping.dingtalk_name} (DingTalk ID: {mapping.dingtalk_user_id}) 已关联系统用户 {mapping.user_id}，跳过创建")
                    result["skipped_count"] += 1
                    result["details"].append({
                        "dingtalk_user_id": mapping.dingtalk_user_id,
                        "status": "skipped",
                        "reason": "已存在关联的系统用户"
                    })
                    continue

                # 检查手机号或邮箱是否已在系统中存在
                if mapping.dingtalk_mobile:
                    existing_user_query = select(User).where(User.phone == mapping.dingtalk_mobile)
                    existing_user = (await self.db.execute(existing_user_query)).scalar_one_or_none()
                    if existing_user:
                        self.logger.warning(f"手机号 {mapping.dingtalk_mobile} 已被用户 {existing_user.username} 使用，跳过创建")
                        result["failed_count"] += 1
                        result["errors"].append(f"用户 {mapping.dingtalk_name} 的手机号已被占用")
                        result["details"].append({
                            "dingtalk_user_id": mapping.dingtalk_user_id,
                            "status": "failed",
                            "reason": "手机号已存在"
                        })
                        continue
                
                # 生成用户名和初始密码
                username = self._generate_username(mapping.dingtalk_mobile, mapping.dingtalk_email, mapping.dingtalk_user_id)
                initial_password = str(uuid.uuid4()).split('-')[0]
                
                # 创建系统用户
                new_user = User(
                    username=username,
                    email=mapping.dingtalk_email,
                    phone=mapping.dingtalk_mobile,
                    full_name=mapping.dingtalk_name,
                    password_hash=User.get_password_hash(initial_password),
                    tenant_id=tenant_id,
                    status="active",
                )
                self.db.add(new_user)
                await self.db.flush() # 刷新以获取 new_user.id

                # 创建项目用户关联
                new_project_user = ProjectUser(
                    user_id=new_user.id,
                    project_id=project_id,
                    role_id=role.id,
                )
                self.db.add(new_project_user)
                await self.db.flush() # 刷新以获取 new_project_user.id
                
                # 关联门店（如果提供）
                if store:
                    project_user_store = ProjectUserStore(
                        project_user_id=new_project_user.id,
                        store_id=store.id
                    )
                    self.db.add(project_user_store)

                # 更新钉钉用户映射表，关联新创建的用户ID
                mapping.user_id = new_user.id
                
                notification_status: str
                notification_error: Optional[str] = None
                
                # 发送通知
                try:
                    await self._send_account_creation_notification(
                        dingtalk_user_id=mapping.dingtalk_user_id,
                        username=username,
                        initial_password=initial_password,
                        project_name=project_name,
                        role_name=role.name,
                        store_name=store.name if store else None
                    )
                    notification_status = "sent_success"
                except Exception as notify_exc:
                    error_str = f"发送通知给 {username} 失败: {notify_exc}"
                    self.logger.error(error_str, exc_info=True)
                    result["errors"].append(error_str)
                    notification_status = "sent_failed"
                    notification_error = str(notify_exc)

                self.logger.info(f"✅ 成功创建用户: {username} (源: {mapping.dingtalk_name})")
                result["created_count"] += 1
                result["details"].append({
                    "dingtalk_user_id": mapping.dingtalk_user_id,
                    "system_user_id": str(new_user.id),
                    "username": username,
                    "status": "created",
                    "initial_password": initial_password,
                    "notification_status": notification_status,
                    "notification_error": notification_error,
                })

            except Exception as e:
                self.logger.error(f"为钉钉用户 {mapping.dingtalk_name} 创建系统用户失败: {e}", exc_info=True)
                result["failed_count"] += 1
                result["errors"].append(f"创建用户 {mapping.dingtalk_name} 失败: {e}")
                result["details"].append({
                    "dingtalk_user_id": mapping.dingtalk_user_id,
                    "status": "failed",
                    "reason": str(e)
                })
        
        if result["failed_count"] > 0:
            result["success"] = False
        
        self.logger.info(
            f"项目用户创建完成: "
            f"成功{result['created_count']}, "
            f"跳过{result['skipped_count']}, "
            f"失败{result['failed_count']}"
        )
        return result

    async def _send_account_creation_notification(
        self,
        dingtalk_user_id: str,
        username: str,
        initial_password: str,
        project_name: str,
        role_name: str,
        store_name: Optional[str] = None
    ):
        """发送账户创建成功的钉钉工作通知"""
        self.logger.info(f"准备向钉钉用户 {dingtalk_user_id} 发送账户创建工作通知")
        try:
            store_info = f"门店：{store_name}\n" if store_name else ""
            
            markdown_text = (
                f"### 欢迎加入 {project_name}！\n\n"
                f"您的系统账号已成功创建。\n\n"
                f"--- \n\n"
                f"**登录账号：** {username}\n\n"
                f"**初始密码：** `{initial_password}`\n\n"
                f"**项目角色：** {role_name}\n\n"
                f"{store_info}"
                f"--- \n\n"
                f"请尽快登录系统并修改您的初始密码。如有任何问题，请联系管理员。"
                f"登录地址：http://45.207.210.252:3001/"
            )
            
            msg = {
                "msgtype": "markdown",
                "markdown": {
                    "title": "账号创建成功",
                    "text": markdown_text
                }
            }

            await self.dingtalk_api.send_work_notification(
                userid_list=[dingtalk_user_id],
                msg=msg
            )
            self.logger.info(f"✅ 成功向钉钉用户 {dingtalk_user_id} 发送工作通知")
        except Exception as e:
            self.logger.error(
                f"通过钉钉工作通知向用户 {dingtalk_user_id} 发送消息失败: {e}",
                exc_info=True
            )
            raise e


# 保持向后兼容的别名
OrganizationSyncService = DingTalkOrganizationSyncService