from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File, Form, Query, Path, Request, Body
from fastapi.responses import FileResponse, JSONResponse
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Optional, List, Dict, Any
import uuid
from datetime import datetime
import logging
import os
from pathlib import Path
from sqlalchemy import select, desc, func

from db.database import get_db
from models.storage import StorageFile, StorageFolder, StorageQuota, FileShare
from models.user import User
from models.project import Project
from models.operation_log import OperationLog
from services.storage_service import StorageService
from api.deps import get_current_user, get_current_project

# 配置日志
logger = logging.getLogger(__name__)

router = APIRouter()

@router.post("/upload")
async def upload_file(
    file: UploadFile = File(...),
    folder_path: str = Form("/"),
    description: Optional[str] = Form(None),
    is_public: bool = Form(False),
    module_code: Optional[str] = Form(None),
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """
    上传文件到项目空间
    """
    try:
        logger.info(f"开始上传文件: user_id={current_user.id}, project_id={current_project.id}, filename={file.filename}")
        
        result = await StorageService.upload_file(
            db=db,
            file=file,
            project_id=current_project.id,
            tenant_id=current_project.tenant_id,
            user_id=current_user.id,
            folder_path=folder_path,
            description=description,
            is_public=is_public,
            module_code=module_code
        )
        
        logger.info(f"文件上传成功: user_id={current_user.id}, project_id={current_project.id}, file_id={result['data']['id']}")
        return result
    except Exception as e:
        logger.error(f"上传文件失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"上传文件失败: {str(e)}"
        )

@router.get("/files")
async def list_files(
    folder_id: Optional[uuid.UUID] = None,
    folder_path: Optional[str] = None,
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    search: Optional[str] = None,
    file_type: Optional[str] = None,
    sort_by: str = Query("created_at", regex="^(name|size|created_at)$"),
    sort_order: str = Query("desc", regex="^(asc|desc)$"),
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """
    列出文件夹中的文件
    """
    try:
        logger.info(f"开始获取文件列表: project_id={current_project.id}, folder_id={folder_id}, folder_path={folder_path}")

        result = await StorageService.list_files(
            db=db,
            project_id=current_project.id,
            folder_id=folder_id,
            folder_path=folder_path,
            page=page,
            page_size=page_size,
            search=search,
            file_type=file_type,
            sort_by=sort_by,
            sort_order=sort_order
        )

        logger.info(f"获取文件列表成功: project_id={current_project.id}, count={result.get('data', {}).get('total', 0)}")
        return result
    except Exception as e:
        logger.error(f"获取文件列表失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取文件列表失败: {str(e)}"
        )

@router.get("/files/{file_id}")
async def get_file_info(
    file_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """
    获取文件信息
    """
    try:
        logger.info(f"开始获取文件信息: file_id={file_id}, project_id={current_project.id}")

        file = await StorageService.get_file(db, file_id, current_project.id)
        if not file:
            logger.error(f"文件不存在: file_id={file_id}, project_id={current_project.id}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="文件不存在"
            )

        logger.info(f"获取文件信息成功: file_id={file_id}, name={file.name}")

        return {
            "success": True,
            "data": {
                "id": str(file.id),
                "name": file.name,
                "original_name": file.original_name,
                "size": file.size,
                "mime_type": file.mime_type,
                "extension": file.extension,
                "is_public": file.is_public,
                "preview_available": file.preview_available,
                "thumbnail_url": f"/api/v1/project/{current_project.id}/space/files/{file.id}/thumbnail" if file.thumbnail_path else None,
                "download_url": f"/api/v1/project/{current_project.id}/space/files/{file.id}/download",
                "uploaded_by": str(file.uploaded_by),
                "created_at": file.created_at.isoformat() if file.created_at else None,
                "updated_at": file.updated_at.isoformat() if file.updated_at else None
            }
        }
    except Exception as e:
        if not isinstance(e, HTTPException):
            logger.error(f"获取文件信息失败: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"获取文件信息失败: {str(e)}"
            )
        raise e

@router.get("/files/{file_id}/download", dependencies=[])
async def download_file(
    file_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
    request: Request = None
):
    """
    下载文件 - 公开访问，不需要认证
    """
    try:
        logger.info(f"开始处理文件下载请求: file_id={file_id}")

        # 从路径中提取项目ID
        project_id = None
        path_parts = request.url.path.split('/')
        for i, part in enumerate(path_parts):
            if part == "project" and i + 1 < len(path_parts):
                try:
                    project_id = uuid.UUID(path_parts[i + 1])
                    break
                except (ValueError, IndexError):
                    pass

        # 不再需要检查 current_project_id

        if not project_id:
            logger.error(f"无法确定项目ID: file_id={file_id}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无法确定项目ID"
            )

        # 获取文件信息
        file_info = await StorageService.download_file(db, file_id, project_id)
        if not file_info:
            logger.error(f"文件不存在: file_id={file_id}, project_id={project_id}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="文件不存在"
            )

        # 记录操作日志（匿名访问）
        try:
            log = OperationLog(
                id=uuid.uuid4(),
                project_id=project_id,
                module="space",
                action="download",
                resource_type="file",
                resource_id=file_id,
                description=f"匿名下载文件: {file_info['file_name']}",
                details={"file_name": file_info["file_name"], "file_size": file_info.get("file_size", 0)},
                ip_address=request.client.host if request and request.client else None,
                user_agent=request.headers.get("user-agent") if request else None
            )
            db.add(log)
            await db.commit()
        except Exception as log_error:
            logger.error(f"记录操作日志失败: {str(log_error)}")
            await db.rollback()
            # 继续处理下载，不因日志记录失败而中断

        logger.info(f"准备返回文件: path={file_info['file_path']}, name={file_info['file_name']}")

        # 返回文件，不需要认证
        return FileResponse(
            path=file_info["file_path"],
            filename=file_info["file_name"],
            media_type=file_info["mime_type"]
        )
    except Exception as e:
        logger.error(f"下载文件时发生错误: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"下载文件失败: {str(e)}"
        )

@router.get("/files/{file_id}/thumbnail", dependencies=[])
async def get_file_thumbnail(
    file_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
    request: Request = None
):
    """
    获取文件缩略图 - 公开访问，不需要认证
    """
    try:
        logger.info(f"开始获取文件缩略图: file_id={file_id}")

        # 从路径中提取项目ID
        project_id = None
        path_parts = request.url.path.split('/')
        for i, part in enumerate(path_parts):
            if part == "project" and i + 1 < len(path_parts):
                try:
                    project_id = uuid.UUID(path_parts[i + 1])
                    break
                except (ValueError, IndexError):
                    pass

        # 不再需要检查 current_project_id

        if not project_id:
            logger.error(f"无法确定项目ID: file_id={file_id}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无法确定项目ID"
            )

        thumbnail_info = await StorageService.get_thumbnail(db, file_id, project_id)
        if not thumbnail_info:
            logger.error(f"缩略图不存在: file_id={file_id}, project_id={project_id}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="缩略图不存在"
            )

        # 记录操作日志（匿名访问）
        try:
            log = OperationLog(
                id=uuid.uuid4(),
                project_id=project_id,
                module="space",
                action="view",
                resource_type="file_thumbnail",
                resource_id=file_id,
                description=f"匿名查看文件缩略图: {thumbnail_info['file_name']}",
                details={"file_name": thumbnail_info["file_name"]},
                ip_address=request.client.host if request and request.client else None,
                user_agent=request.headers.get("user-agent") if request else None
            )
            db.add(log)
            await db.commit()
        except Exception as log_error:
            logger.error(f"记录操作日志失败: {str(log_error)}")
            await db.rollback()
            # 继续处理缩略图，不因日志记录失败而中断

        logger.info(f"准备返回缩略图: path={thumbnail_info['file_path']}, name={thumbnail_info['file_name']}")

        # 返回缩略图，不需要认证
        return FileResponse(
            path=thumbnail_info["file_path"],
            filename=thumbnail_info["file_name"],
            media_type=thumbnail_info["mime_type"]
        )
    except Exception as e:
        if not isinstance(e, HTTPException):
            logger.error(f"获取文件缩略图失败: {str(e)}", exc_info=True)
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"获取文件缩略图失败: {str(e)}"
            )
        raise e

@router.post("/folders")
async def create_folder(
    folder_name: str = Form(...),
    parent_id: Optional[uuid.UUID] = Form(None),
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """
    创建文件夹
    """
    try:
        logger.info(f"开始创建文件夹: folder_name={folder_name}, parent_id={parent_id}, project_id={current_project.id}")

        result = await StorageService.create_folder(
            db=db,
            project_id=current_project.id,
            tenant_id=current_project.tenant_id,
            user_id=current_user.id,
            folder_name=folder_name,
            parent_id=parent_id
        )

        logger.info(f"创建文件夹成功: folder_name={folder_name}, project_id={current_project.id}")
        return result
    except Exception as e:
        logger.error(f"创建文件夹失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建文件夹失败: {str(e)}"
        )

@router.delete("/files/{file_id}")
async def delete_file(
    file_id: uuid.UUID,
    permanent: bool = Query(False),
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """
    删除文件
    """
    try:
        logger.info(f"开始删除文件: file_id={file_id}, permanent={permanent}, project_id={current_project.id}")

        result = await StorageService.delete_file(
            db=db,
            file_id=file_id,
            project_id=current_project.id,
            user_id=current_user.id,
            permanent=permanent
        )

        logger.info(f"删除文件成功: file_id={file_id}, project_id={current_project.id}")
        return result
    except Exception as e:
        logger.error(f"删除文件失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除文件失败: {str(e)}"
        )

@router.post("/files/{file_id}/share")
async def create_file_share(
    file_id: uuid.UUID,
    expires_in_days: Optional[int] = Form(None),
    password: Optional[str] = Form(None),
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """
    创建文件分享链接
    """
    try:
        logger.info(f"开始创建文件分享链接: file_id={file_id}, expires_in_days={expires_in_days}, project_id={current_project.id}")

        result = await StorageService.create_file_share(
            db=db,
            file_id=file_id,
            project_id=current_project.id,
            user_id=current_user.id,
            expires_in_days=expires_in_days,
            password=password
        )

        logger.info(f"创建文件分享链接成功: file_id={file_id}, project_id={current_project.id}")
        return result
    except Exception as e:
        logger.error(f"创建文件分享链接失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建文件分享链接失败: {str(e)}"
        )

@router.get("/usage")
async def get_storage_usage(
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """
    获取存储使用情况
    """
    try:
        logger.info(f"开始获取存储使用情况: user_id={current_user.id}, project_id={current_project.id}")

        result = await StorageService.get_storage_usage(
            db=db,
            project_id=current_project.id,
            user_id=None  # 获取项目级配额而不是用户级配额
        )

        logger.info(f"获取存储使用情况成功: user_id={current_user.id}, project_id={current_project.id}")
        return result
    except Exception as e:
        logger.error(f"获取存储使用情况失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取存储使用情况失败: {str(e)}"
        )

@router.post("/files/{file_id}/ai-recognize")
async def ai_recognize_file(
    file_id: uuid.UUID,
    prompt: Optional[str] = Body(None, description="自定义识别提示"),
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """
    AI识图功能 - 对图片进行AI识别和总结
    """
    try:
        logger.info(f"开始AI识图: file_id={file_id}, project_id={current_project.id}")

        # 获取文件信息
        file = await StorageService.get_file(db, file_id, current_project.id)
        if not file:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="文件不存在"
            )

        # 检查文件是否为图片
        if not file.mime_type.startswith('image/'):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="只支持图片文件的AI识别"
            )

        # 检查文件物理路径是否存在
        file_path = Path(file.path)
        if not file_path.exists():
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="文件物理路径不存在"
            )

        # 读取图片文件并转换为base64
        with open(file_path, 'rb') as f:
            image_data = f.read()
        
        import base64
        image_base64 = base64.b64encode(image_data).decode('utf-8')

        # 构建AI识别提示词
        if not prompt:
            prompt = """请分析这张图片，并提供详细的识别和总结：

1. **图片内容描述**：描述图片中的主要内容、物体、人物、场景等
2. **技术信息**：如果可见，描述图片的技术特征（颜色、构图、风格等）
3. **文字识别**：如果图片中包含文字，请准确识别并提取
4. **用途分析**：分析这张图片可能的用途或场景
5. **关键信息提取**：提取图片中的关键信息点

请用中文回答，内容要详细、准确、有条理。"""

        # 调用AI视觉服务
        from services.ai.vision_service import AIVisionService
        
        ai_result = await AIVisionService.analyze_image(
            db=db,
            project_id=current_project.id,
            user_id=current_user.id,
            image_data=image_base64,
            prompt=prompt,
            temperature=0.3
        )

        # 解析AI响应内容
        ai_content = ""
        
        # OpenAI/Azure/OpenRouter/阿里巴巴百炼格式
        if "choices" in ai_result and ai_result["choices"]:
            choice = ai_result["choices"][0]
            if "message" in choice and "content" in choice["message"]:
                ai_content = choice["message"]["content"]
        # Anthropic格式
        elif "content" in ai_result and ai_result["content"]:
            if isinstance(ai_result["content"], list) and ai_result["content"]:
                ai_content = ai_result["content"][0].get("text", "")
            elif isinstance(ai_result["content"], str):
                ai_content = ai_result["content"]

        if not ai_content:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="AI识别失败：未获取到有效内容"
            )

        # 保存识别结果到文件元数据
        if not file.file_metadata:
            file.file_metadata = {}
        
        file.file_metadata['ai_recognition'] = {
            'content': ai_content,
            'prompt': prompt,
            'recognized_at': datetime.now().isoformat(),
            'recognized_by': str(current_user.id)
        }
        
        # 标记该字段已修改
        from sqlalchemy.orm.attributes import flag_modified
        flag_modified(file, 'file_metadata')
        
        await db.commit()

        logger.info(f"AI识图成功: file_id={file_id}, content_length={len(ai_content)}")

        return {
            "success": True,
            "message": "AI识图完成",
            "data": {
                "file_id": str(file_id),
                "file_name": file.name,
                "recognition_content": ai_content,
                "prompt_used": prompt,
                "recognized_at": datetime.now().isoformat()
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"AI识图失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"AI识图失败: {str(e)}"
        )

@router.get("/recycle-bin")
async def get_deleted_files(
    page: int = Query(1, ge=1),
    limit: int = Query(20, ge=1, le=100),
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """
    获取回收站文件列表
    """
    try:
        logger.info(f"获取回收站文件列表: user_id={current_user.id}, project_id={current_project.id}")
        
        # 计算偏移量
        offset = (page - 1) * limit
        
        # 查询已删除的文件
        files_query = select(StorageFile).where(
            StorageFile.project_id == current_project.id,
            StorageFile.status == "deleted"
        ).order_by(desc(StorageFile.deleted_at)).offset(offset).limit(limit)
        
        result = await db.execute(files_query)
        files = result.scalars().all()
        
        # 查询总数
        count_query = select(func.count()).select_from(StorageFile).where(
            StorageFile.project_id == current_project.id,
            StorageFile.status == "deleted"
        )
        result = await db.execute(count_query)
        total = result.scalar_one()
        
        # 构建响应数据
        files_data = []
        for file in files:
            file_data = {
                "id": str(file.id),
                "name": file.name,
                "original_name": file.original_name,
                "size": file.size,
                "mime_type": file.mime_type,
                "extension": file.extension,
                "category": file.file_metadata.get("category") if file.file_metadata else "other",
                "uploaded_at": file.uploaded_at.isoformat() if file.uploaded_at else None,
                "deleted_at": file.deleted_at.isoformat() if file.deleted_at else None,
                "uploaded_by": str(file.uploaded_by) if file.uploaded_by else None,
                "folder_id": str(file.folder_id) if file.folder_id else None
            }
            files_data.append(file_data)
        
        return {
            "success": True,
            "data": {
                "files": files_data,
                "pagination": {
                    "current_page": page,
                    "page_size": limit,
                    "total": total,
                    "total_pages": (total + limit - 1) // limit
                }
            }
        }
        
    except Exception as e:
        logger.error(f"获取回收站文件列表失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取回收站文件列表失败: {str(e)}"
        )

@router.post("/recycle-bin/{file_id}/restore")
async def restore_file(
    file_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """
    从回收站还原文件
    """
    try:
        logger.info(f"还原文件: file_id={file_id}, user_id={current_user.id}, project_id={current_project.id}")
        
        # 查询已删除的文件
        file_query = select(StorageFile).where(
            StorageFile.id == file_id,
            StorageFile.project_id == current_project.id,
            StorageFile.status == "deleted"
        )
        result = await db.execute(file_query)
        file = result.scalar_one_or_none()
        
        if not file:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="文件不存在或已被彻底删除"
            )
        
        # 检查存储配额是否允许还原
        has_quota, message = await StorageService.check_quota(
            db, current_project.id, current_user.id, file.size
        )
        if not has_quota:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"还原失败: {message}"
            )
        
        # 还原文件
        file.status = "active"
        file.deleted_at = None
        
        # 获取文件类别
        category = file.file_metadata.get("category", "other") if file.file_metadata else "other"
        
        # 更新存储配额
        await StorageService.update_quota(
            db, current_project.id, file.uploaded_by, file.size, category, "add"
        )
        
        await db.commit()
        
        logger.info(f"文件还原成功: file_id={file_id}")
        return {
            "success": True,
            "message": "文件还原成功",
            "data": {
                "id": str(file.id),
                "name": file.name
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"还原文件失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"还原文件失败: {str(e)}"
        )

@router.delete("/recycle-bin/{file_id}/permanent")
async def permanent_delete_file(
    file_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """
    从回收站彻底删除文件
    """
    try:
        logger.info(f"彻底删除文件: file_id={file_id}, user_id={current_user.id}, project_id={current_project.id}")
        
        result = await StorageService.delete_file(
            db=db,
            file_id=file_id,
            project_id=current_project.id,
            user_id=current_user.id,
            permanent=True
        )
        
        logger.info(f"文件彻底删除成功: file_id={file_id}")
        return result
        
    except Exception as e:
        logger.error(f"彻底删除文件失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"彻底删除文件失败: {str(e)}"
        )

@router.delete("/recycle-bin/clear")
async def clear_recycle_bin(
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """
    清空回收站
    """
    try:
        logger.info(f"清空回收站: user_id={current_user.id}, project_id={current_project.id}")
        
        # 查询所有已删除的文件
        files_query = select(StorageFile).where(
            StorageFile.project_id == current_project.id,
            StorageFile.status == "deleted"
        )
        result = await db.execute(files_query)
        files = result.scalars().all()
        
        deleted_count = 0
        freed_space = 0
        
        for file in files:
            try:
                # 删除物理文件
                file_path = Path(file.path)
                if file_path.exists():
                    os.remove(file_path)
                
                # 删除缩略图
                if file.thumbnail_path:
                    # 使用storage service中的路径处理方式
                    thumbnail_path = Path(file.thumbnail_path)
                    if thumbnail_path.exists():
                        os.remove(thumbnail_path)
                
                freed_space += file.size
                deleted_count += 1
                
                # 删除数据库记录
                await db.delete(file)
                
            except Exception as e:
                logger.warning(f"删除文件失败: file_id={file.id}, error={str(e)}")
                continue
        
        await db.commit()
        
        logger.info(f"回收站清空完成: 删除了{deleted_count}个文件，释放了{freed_space}字节")
        return {
            "success": True,
            "message": f"回收站清空完成，删除了{deleted_count}个文件",
            "data": {
                "deleted_count": deleted_count,
                "freed_space": freed_space
            }
        }
        
    except Exception as e:
        await db.rollback()
        logger.error(f"清空回收站失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"清空回收站失败: {str(e)}"
        )
