#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
通知服务
用于创建、发送和管理通知
"""

import logging
import uuid
from typing import Dict, Any, List, Optional, Union
from datetime import datetime

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import and_, or_, desc
from db.database import Base

from models.notification import Notification
from models.user import User
from models.notification_settings import NotificationSettings
from schemas.notification import NotificationCreate
from .redis_manager import RedisManager

# 配置日志
logger = logging.getLogger(__name__)

class NotificationService:
    """通知服务，提供通知的创建、发送和管理功能"""

    @staticmethod
    async def create_notification(
        db: AsyncSession,
        user_id: uuid.UUID,
        project_id: uuid.UUID,
        notification_data: NotificationCreate,
        publish: bool = True
    ) -> Notification:
        """
        创建通知并保存到数据库

        Args:
            db: 数据库会话
            user_id: 用户ID
            project_id: 项目ID
            notification_data: 通知数据
            publish: 是否发布到Redis

        Returns:
            创建的通知对象
        """
        try:
            # 检查用户通知设置
            query = select(NotificationSettings).where(
                and_(
                    NotificationSettings.project_id == project_id,
                    NotificationSettings.user_id == user_id
                )
            )
            result = await db.execute(query)
            settings = result.scalar_one_or_none()

            # 根据通知类型检查用户是否启用了该类型的通知
            if settings:
                notification_type = notification_data.type

                # 检查用户是否启用了该类型的通知
                if notification_type == "system" and not settings.system_notifications:
                    logger.info(f"用户 {user_id} 已禁用系统通知，跳过创建")
                    return None
                elif notification_type == "task" and not settings.task_notifications:
                    logger.info(f"用户 {user_id} 已禁用任务通知，跳过创建")
                    return None
                elif notification_type == "inventory" and not settings.inventory_notifications:
                    logger.info(f"用户 {user_id} 已禁用库存通知，跳过创建")
                    return None
                elif notification_type == "order" and not settings.order_notifications:
                    logger.info(f"用户 {user_id} 已禁用订单通知，跳过创建")
                    return None
                elif notification_type == "security" and not settings.security_notifications:
                    logger.info(f"用户 {user_id} 已禁用安全通知，跳过创建")
                    return None

            # 创建通知对象
            notification = Notification(
                id=uuid.uuid4(),
                user_id=user_id,
                project_id=project_id,
                title=notification_data.title,
                content=notification_data.content,
                type=notification_data.type,
                priority=notification_data.priority,
                target_url=notification_data.target_url,
                created_at=datetime.now(),
                is_read=False
            )

            # 保存到数据库
            db.add(notification)
            await db.commit()
            await db.refresh(notification)

            # 如果需要，发布到Redis
            if publish:
                await NotificationService.publish_notification(notification)

            return notification
        except Exception as e:
            await db.rollback()
            logger.error(f"创建通知失败: {str(e)}")
            raise

    @staticmethod
    async def publish_notification(notification: Notification):
        """
        发布通知到Redis

        Args:
            notification: 通知对象
        """
        try:
            # 获取用户通知设置
            async with AsyncSession() as db:
                query = select(NotificationSettings).where(
                    and_(
                        NotificationSettings.project_id == notification.project_id,
                        NotificationSettings.user_id == notification.user_id
                    )
                )
                result = await db.execute(query)
                settings = result.scalar_one_or_none()

                # 检查是否应该实时发送通知
                should_publish_realtime = True

                if settings:
                    # 根据通知优先级和频率设置决定是否实时发送
                    if notification.priority == "high":
                        if settings.high_priority_frequency != "realtime":
                            should_publish_realtime = False
                            logger.info(f"用户 {notification.user_id} 的高优先级通知频率设置为 {settings.high_priority_frequency}，跳过实时发送")
                    else:
                        if settings.normal_frequency != "realtime":
                            should_publish_realtime = False
                            logger.info(f"用户 {notification.user_id} 的普通通知频率设置为 {settings.normal_frequency}，跳过实时发送")

                    # 检查是否启用了推送通知
                    if not settings.push_notifications:
                        should_publish_realtime = False
                        logger.info(f"用户 {notification.user_id} 已禁用推送通知，跳过实时发送")

                if not should_publish_realtime:
                    return

            # 获取Redis管理器实例
            redis_manager = await RedisManager.get_instance()

            # 准备通知数据
            notification_dict = {
                "id": str(notification.id),
                "user_id": str(notification.user_id),
                "project_id": str(notification.project_id),
                "title": notification.title,
                "content": notification.content,
                "type": notification.type,
                "priority": notification.priority,
                "target_url": notification.target_url,
                "is_read": notification.is_read,
                "created_at": notification.created_at.isoformat()
            }

            # 发布到用户通道
            await redis_manager.publish_notification(
                f"user:{notification.user_id}:notifications",
                notification_dict
            )

            # 发布到项目通道
            await redis_manager.publish_notification(
                f"project:{notification.project_id}:notifications",
                notification_dict
            )

            logger.debug(f"通知 {notification.id} 已发布")

        except Exception as e:
            logger.error(f"发布通知失败: {str(e)}")
            # 这里我们不抛出异常，因为发布失败不应该影响主流程
            # 客户端仍然可以通过轮询获取通知

    @staticmethod
    async def get_notifications(
        db: AsyncSession,
        user_id: uuid.UUID,
        project_id: uuid.UUID,
        page: int = 1,
        page_size: int = 50,
        filter_type: Optional[str] = None,
        priority: Optional[str] = None,
        is_read: Optional[bool] = None
    ) -> Dict[str, Any]:
        """
        获取用户通知列表

        Args:
            db: 数据库会话
            user_id: 用户ID
            project_id: 项目ID
            page: 页码
            page_size: 每页大小
            filter_type: 通知类型过滤
            priority: 优先级过滤
            is_read: 是否已读过滤

        Returns:
            通知列表和分页信息
        """
        try:
            # 计算偏移量
            offset = (page - 1) * page_size

            # 构建查询
            query = select(Notification).where(
                and_(
                    Notification.user_id == user_id,
                    Notification.project_id == project_id
                )
            )

            # 应用过滤条件
            if filter_type:
                query = query.where(Notification.type == filter_type)
            if priority:
                query = query.where(Notification.priority == priority)
            if is_read is not None:
                query = query.where(Notification.is_read == is_read)

            # 按创建时间降序排序
            query = query.order_by(desc(Notification.created_at))

            # 执行查询获取总数
            count_query = select(Notification).where(
                and_(
                    Notification.user_id == user_id,
                    Notification.project_id == project_id
                )
            )
            if filter_type:
                count_query = count_query.where(Notification.type == filter_type)
            if priority:
                count_query = count_query.where(Notification.priority == priority)
            if is_read is not None:
                count_query = count_query.where(Notification.is_read == is_read)

            result = await db.execute(count_query)
            total = len(result.scalars().all())

            # 应用分页
            query = query.offset(offset).limit(page_size)

            # 执行查询
            result = await db.execute(query)
            notifications = result.scalars().all()

            # 计算总页数
            pages = (total + page_size - 1) // page_size

            return {
                "items": notifications,
                "total": total,
                "page": page,
                "page_size": page_size,
                "pages": pages
            }
        except Exception as e:
            logger.error(f"获取通知列表失败: {str(e)}")
            raise

    @staticmethod
    async def mark_as_read(
        db: AsyncSession,
        notification_id: uuid.UUID,
        user_id: uuid.UUID
    ) -> bool:
        """
        将通知标记为已读

        Args:
            db: 数据库会话
            notification_id: 通知ID
            user_id: 用户ID

        Returns:
            操作是否成功
        """
        try:
            # 查询通知
            query = select(Notification).where(
                and_(
                    Notification.id == notification_id,
                    Notification.user_id == user_id
                )
            )
            result = await db.execute(query)
            notification = result.scalars().first()

            if not notification:
                logger.warning(f"通知 {notification_id} 不存在或不属于用户 {user_id}")
                return False

            # 标记为已读
            notification.is_read = True
            notification.read_at = datetime.now()

            await db.commit()
            return True
        except Exception as e:
            await db.rollback()
            logger.error(f"标记通知为已读失败: {str(e)}")
            raise

    @staticmethod
    async def send_store_notification(
        db: AsyncSession,
        project_id: uuid.UUID,
        store_id: uuid.UUID,
        notification_data: Dict[str, Any],
        sender_id: Optional[uuid.UUID] = None
    ) -> List[Notification]:
        """
        发送门店通知

        Args:
            db: 数据库会话
            project_id: 项目ID
            store_id: 门店ID
            notification_data: 通知数据
            sender_id: 发送者ID

        Returns:
            创建的通知列表
        """
        try:
            # 查询门店关联的用户
            from models.store import ProjectUserStore
            from models.project_user import ProjectUser
            from sqlalchemy import select, join

            # 查询与门店关联的项目用户
            query = select(ProjectUser).join(
                ProjectUserStore,
                ProjectUserStore.project_user_id == ProjectUser.id
            ).where(
                and_(
                    ProjectUser.project_id == project_id,
                    ProjectUserStore.store_id == store_id
                )
            )

            result = await db.execute(query)
            project_users = result.scalars().all()

            if not project_users:
                logger.warning(f"门店 {store_id} 没有关联的用户，无法发送通知")
                return []

            # 创建通知
            notifications = []
            for project_user in project_users:
                # 创建通知数据
                notification_create = NotificationCreate(
                    user_id=project_user.user_id,
                    title=notification_data.get("title", "门店通知"),
                    content=notification_data.get("content", ""),
                    type=notification_data.get("type", "store"),
                    priority=notification_data.get("priority", "normal"),
                    target_url=notification_data.get("target_url")
                )

                # 创建通知
                notification = await NotificationService.create_notification(
                    db=db,
                    user_id=store_user.user_id,
                    project_id=project_id,
                    notification_data=notification_create,
                    publish=True
                )

                if notification:
                    notifications.append(notification)

            return notifications
        except Exception as e:
            logger.error(f"发送门店通知失败: {str(e)}")
            raise
