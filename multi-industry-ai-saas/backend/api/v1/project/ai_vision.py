#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
项目级 AI 视觉 API
"""

import logging
import uuid
import base64
from typing import Optional, List, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query, Path, status, UploadFile, File, Form
from sqlalchemy.ext.asyncio import AsyncSession

from db.database import get_db
from models.project import Project
from core.auth import get_current_user
from api.deps import get_current_project
from services.ai import AIVisionService
from schemas.ai import (
    AIVisionRequest,
    AIVisionResponse,
    AIVisionErrorResponse,
)

logger = logging.getLogger(__name__)
router = APIRouter()

@router.post("/ai/vision", response_model=AIVisionResponse)
async def analyze_image(
    request: AIVisionRequest,
    project: Project = Depends(get_current_project),
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    分析图像
    
    使用 AI 模型分析图像内容。
    """
    try:
        # 发送视觉请求
        result = await AIVisionService.analyze_image(
            db=db,
            project_id=project.id,
            user_id=current_user.id,
            image_data=request.image_base64,
            prompt=request.prompt,
            model_id=request.model_id,
            config_id=request.config_id,
            temperature=request.temperature,
            max_tokens=request.max_tokens,
            additional_messages=request.additional_messages,
        )
        
        return result
    except ValueError as e:
        logger.error(f"AI 视觉请求失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )
    except Exception as e:
        logger.error(f"AI 视觉请求失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"AI 视觉请求失败: {str(e)}",
        )

@router.post("/ai/vision/upload", response_model=AIVisionResponse)
async def analyze_uploaded_image(
    file: UploadFile = File(...),
    prompt: str = Form(...),
    model_id: Optional[uuid.UUID] = Form(None),
    config_id: Optional[uuid.UUID] = Form(None),
    temperature: Optional[float] = Form(None),
    max_tokens: Optional[int] = Form(None),
    project: Project = Depends(get_current_project),
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    分析上传的图像
    
    上传图像并使用 AI 模型分析内容。
    """
    try:
        # 读取上传的图像文件
        image_data = await file.read()
        
        # 发送视觉请求
        result = await AIVisionService.analyze_image(
            db=db,
            project_id=project.id,
            user_id=current_user.id,
            image_data=image_data,
            prompt=prompt,
            model_id=model_id,
            config_id=config_id,
            temperature=temperature,
            max_tokens=max_tokens,
            additional_messages=None,
        )
        
        return result
    except ValueError as e:
        logger.error(f"AI 视觉请求失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )
    except Exception as e:
        logger.error(f"AI 视觉请求失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"AI 视觉请求失败: {str(e)}",
        )
