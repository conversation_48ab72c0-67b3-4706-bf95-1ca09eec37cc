#!/usr/bin/env python3
"""
测试AI助手调用高德MCP工具的完整流程
"""
import asyncio
import httpx
import uuid
import json
import time

async def test_ai_assistant_mcp_tool_call():
    """测试AI助手调用高德MCP工具"""
    print("=== 测试AI助手调用高德MCP工具 ===")
    
    # 高德MCP配置
    gaode_config = {
        "url": "https://mcp.amap.com/sse?key=94ed64a48ce623e65b18134befb65750",
        "headers": {}
    }
    
    async with httpx.AsyncClient() as client:
        print("1. 建立SSE连接并初始化MCP...")
        
        sse_headers = {
            "Accept": "text/event-stream",
            "Cache-Control": "no-cache",
            "Connection": "keep-alive"
        }
        
        try:
            async with client.stream('GET', gaode_config["url"], headers=sse_headers, timeout=60) as sse_response:
                if sse_response.status_code != 200:
                    print(f"❌ SSE连接失败，状态码: {sse_response.status_code}")
                    return False
                
                print(f"✅ SSE连接成功，状态码: {sse_response.status_code}")
                
                message_endpoint = None
                session_id = None
                tools = []
                server_info = {}
                mcp_responses = {}
                
                async def sse_listener():
                    """监听SSE事件"""
                    nonlocal message_endpoint, session_id, mcp_responses
                    
                    buffer = ""
                    try:
                        async for chunk in sse_response.aiter_text():
                            buffer += chunk
                            lines = buffer.split('\n')
                            buffer = lines[-1]
                            
                            for line in lines[:-1]:
                                line = line.strip()
                                if line:
                                    # 解析端点信息
                                    if line.startswith("data:") and "/mcp/message" in line:
                                        endpoint_path = line[5:].strip()
                                        if endpoint_path.startswith("/mcp/message"):
                                            message_endpoint = f"https://mcp.amap.com{endpoint_path}"
                                            if "sessionId=" in endpoint_path:
                                                session_id = endpoint_path.split("sessionId=")[1].split("&")[0]
                                            print(f"   获取到消息端点: {message_endpoint}")
                                    
                                    # 解析MCP响应
                                    elif line.startswith("data:") and line != "data:" and "{" in line:
                                        try:
                                            response_text = line[5:].strip()
                                            response_data = json.loads(response_text)
                                            if "id" in response_data:
                                                request_id = response_data["id"]
                                                mcp_responses[request_id] = response_data
                                        except:
                                            pass
                    except:
                        pass
                
                async def initialize_and_get_tools():
                    """初始化MCP并获取工具列表"""
                    nonlocal tools, server_info
                    
                    # 等待获取端点
                    wait_time = 0
                    while not message_endpoint and wait_time < 10:
                        await asyncio.sleep(0.5)
                        wait_time += 0.5
                    
                    if not message_endpoint:
                        print("❌ 未能获取到消息端点")
                        return False
                    
                    headers = {"Content-Type": "application/json"}
                    
                    try:
                        # 1. 初始化
                        init_request = {
                            "jsonrpc": "2.0",
                            "id": str(uuid.uuid4()),
                            "method": "initialize",
                            "params": {
                                "protocolVersion": "2024-11-05",
                                "capabilities": {"tools": {}},
                                "clientInfo": {"name": "AI Assistant", "version": "1.0.0"}
                            }
                        }
                        
                        await client.post(message_endpoint, json=init_request, headers=headers, timeout=15)
                        
                        # 等待初始化响应
                        request_id = init_request["id"]
                        wait_time = 0
                        while wait_time < 10:
                            if request_id in mcp_responses:
                                init_data = mcp_responses.pop(request_id)
                                if "result" in init_data:
                                    server_info.update(init_data["result"].get("serverInfo", {}))
                                    print(f"   ✅ 服务器初始化成功: {server_info.get('name', 'Unknown')}")
                                break
                            await asyncio.sleep(0.5)
                            wait_time += 0.5
                        
                        # 2. 发送initialized通知
                        initialized_notification = {
                            "jsonrpc": "2.0",
                            "method": "notifications/initialized"
                        }
                        await client.post(message_endpoint, json=initialized_notification, headers=headers, timeout=10)
                        await asyncio.sleep(1)
                        
                        # 3. 获取工具列表
                        tools_request = {
                            "jsonrpc": "2.0",
                            "id": str(uuid.uuid4()),
                            "method": "tools/list",
                            "params": {}
                        }
                        
                        await client.post(message_endpoint, json=tools_request, headers=headers, timeout=15)
                        
                        # 等待工具响应
                        request_id = tools_request["id"]
                        wait_time = 0
                        while wait_time < 10:
                            if request_id in mcp_responses:
                                tools_data = mcp_responses.pop(request_id)
                                if "result" in tools_data and "tools" in tools_data["result"]:
                                    tools.extend(tools_data["result"]["tools"])
                                    print(f"   ✅ 获取到 {len(tools)} 个工具")
                                break
                            await asyncio.sleep(0.5)
                            wait_time += 0.5
                        
                        return len(tools) > 0
                        
                    except Exception as e:
                        print(f"   ❌ 初始化失败: {e}")
                        return False
                
                # 启动并发任务
                sse_task = asyncio.create_task(sse_listener())
                init_task = asyncio.create_task(initialize_and_get_tools())
                
                # 等待初始化完成
                init_success = await asyncio.wait_for(init_task, timeout=30)
                
                if not init_success:
                    sse_task.cancel()
                    return False
                
                print(f"\n2. 测试工具调用...")
                
                # 测试几个不同的工具
                test_cases = [
                    {
                        "name": "maps_weather",
                        "description": "查询天气",
                        "arguments": {"city": "北京"}
                    },
                    {
                        "name": "maps_text_search", 
                        "description": "搜索POI",
                        "arguments": {"keywords": "北京大学", "city": "北京"}
                    },
                    {
                        "name": "maps_geo",
                        "description": "地址转坐标",
                        "arguments": {"address": "北京市朝阳区", "city": "北京"}
                    }
                ]
                
                successful_calls = 0
                
                for i, test_case in enumerate(test_cases, 1):
                    tool_name = test_case["name"]
                    
                    # 检查工具是否存在
                    tool_found = False
                    for tool in tools:
                        if tool.get("name") == tool_name:
                            tool_found = True
                            break
                    
                    if not tool_found:
                        print(f"   {i}. ⚠️  工具 {tool_name} 不存在，跳过")
                        continue
                    
                    print(f"   {i}. 测试工具: {tool_name} ({test_case['description']})")
                    
                    try:
                        # 构建工具调用请求
                        tool_request = {
                            "jsonrpc": "2.0",
                            "id": str(uuid.uuid4()),
                            "method": "tools/call",
                            "params": {
                                "name": tool_name,
                                "arguments": test_case["arguments"]
                            }
                        }
                        
                        # 发送工具调用请求
                        headers = {"Content-Type": "application/json"}
                        call_response = await client.post(
                            message_endpoint,
                            json=tool_request,
                            headers=headers,
                            timeout=30
                        )
                        
                        print(f"      HTTP状态码: {call_response.status_code}")
                        
                        # 等待SSE响应
                        request_id = tool_request["id"]
                        tool_result = None
                        wait_time = 0
                        
                        while wait_time < 15:
                            if request_id in mcp_responses:
                                tool_result = mcp_responses.pop(request_id)
                                break
                            await asyncio.sleep(0.5)
                            wait_time += 0.5
                        
                        if tool_result:
                            if "result" in tool_result:
                                result_data = tool_result["result"]
                                print(f"      ✅ 调用成功")
                                
                                # 显示部分结果
                                if isinstance(result_data, dict):
                                    if "content" in result_data:
                                        content = result_data["content"]
                                        if isinstance(content, list) and len(content) > 0:
                                            first_content = content[0]
                                            if isinstance(first_content, dict) and "text" in first_content:
                                                text_content = first_content["text"]
                                                if len(text_content) > 200:
                                                    text_content = text_content[:200] + "..."
                                                print(f"      结果: {text_content}")
                                            else:
                                                print(f"      结果: {str(first_content)[:200]}...")
                                        else:
                                            print(f"      结果: {str(result_data)[:200]}...")
                                    else:
                                        print(f"      结果: {str(result_data)[:200]}...")
                                else:
                                    print(f"      结果: {str(result_data)[:200]}...")
                                
                                successful_calls += 1
                                
                            elif "error" in tool_result:
                                error_info = tool_result["error"]
                                print(f"      ❌ 调用失败: {error_info.get('message', '未知错误')}")
                            else:
                                print(f"      ⚠️  响应格式异常: {tool_result}")
                        else:
                            print(f"      ❌ 未收到响应")
                        
                        # 等待一下再调用下一个工具
                        await asyncio.sleep(2)
                        
                    except Exception as e:
                        print(f"      ❌ 调用异常: {e}")
                
                # 取消SSE任务
                sse_task.cancel()
                
                print(f"\n3. 测试结果总结:")
                print(f"   总工具数: {len(tools)}")
                print(f"   测试用例: {len(test_cases)}")
                print(f"   成功调用: {successful_calls}")
                print(f"   成功率: {successful_calls/len(test_cases)*100:.1f}%")
                
                if successful_calls > 0:
                    print(f"\n🎉 AI助手MCP工具调用测试成功!")
                    print(f"✅ 高德MCP服务器连接正常")
                    print(f"✅ 工具列表获取成功")
                    print(f"✅ 工具调用功能正常")
                    print(f"✅ SSE协议处理正确")
                    return True
                else:
                    print(f"\n❌ 所有工具调用都失败了")
                    return False
                    
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            import traceback
            traceback.print_exc()
            return False

if __name__ == "__main__":
    result = asyncio.run(test_ai_assistant_mcp_tool_call())
    if result:
        print("\n🎉 AI助手MCP工具调用测试完全成功!")
        print("✅ 可以在生产环境中使用高德MCP工具")
    else:
        print("\n❌ AI助手MCP工具调用测试失败!")
        print("需要进一步调试工具调用功能") 