import React, { useState, useEffect } from 'react';
import {
  Card, Table, Button, Space, Modal, Form, Input, Select, 
  message, Tabs, Row, Col, Statistic, DatePicker, Tag, 
  Divider, Alert, Typography, Tooltip, Progress, Empty,
  Popconfirm, InputNumber, Badge, Upload
} from 'antd';
import {
  CheckCircleOutlined, CloseCircleOutlined, EditOutlined,
  EyeOutlined, SearchOutlined, FilterOutlined, ReloadOutlined,
  BarChartOutlined, ExclamationCircleOutlined, FileExcelOutlined,
  DownloadOutlined, AuditOutlined, RollbackOutlined, UnorderedListOutlined,
  PieChartOutlined, UploadOutlined, CloudUploadOutlined
} from '@ant-design/icons';
import dayjs from 'dayjs';
import dateTimeUtils from '../../../../utils/dateTimeUtils';
import apiService from '../../../../services/api';

const { TabPane } = Tabs;
const { Option } = Select;
const { TextArea } = Input;
const { Title, Text } = Typography;
const { RangePicker } = DatePicker;

/**
 * 盘点审核组件
 * 财务管理业务中心用于审核门店提交的盘点数据
 * 按月份、门店汇总显示，支持查看详细商品列表
 */
const InventoryAudit = () => {
  const [activeTab, setActiveTab] = useState('pending');
  const [auditList, setAuditList] = useState([]);
  const [storeList, setStoreList] = useState([]);
  const [auditLoading, setAuditLoading] = useState(false);
  const [auditModalVisible, setAuditModalVisible] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [itemListModalVisible, setItemListModalVisible] = useState(false);
  const [batchAuditModalVisible, setBatchAuditModalVisible] = useState(false);
  const [uploadModalVisible, setUploadModalVisible] = useState(false);
  const [selectedAudit, setSelectedAudit] = useState(null);
  const [selectedItems, setSelectedItems] = useState([]);
  const [itemList, setItemList] = useState([]);
  const [submitting, setSubmitting] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [filters, setFilters] = useState({
    store_id: null,
    month: null,
    status: null,
    date_range: null
  });
  const [statistics, setStatistics] = useState({
    pending: 0,
    approved: 0,
    rejected: 0,
    total: 0
  });

  const [auditForm] = Form.useForm();
  const [batchAuditForm] = Form.useForm();
  const [uploadForm] = Form.useForm();

  // 初始化时间工具
  useEffect(() => {
    dateTimeUtils.initDateTimeUtils();
  }, []);

  // 获取审核列表（按门店月份汇总）
  const fetchAuditList = async () => {
    setAuditLoading(true);
    try {
      const params = {
        status: activeTab === 'all' ? null : activeTab,
        store_id: filters.store_id,
        month: filters.month,
        start_date: filters.date_range?.[0]?.format('YYYY-MM-DD'),
        end_date: filters.date_range?.[1]?.format('YYYY-MM-DD'),
        group_by: 'store_month' // 按门店月份分组
      };
      
      const response = await apiService.project.finance.getInventoryAuditList(params);
      if (response && response.success) {
        setAuditList(response.data.items || []);
        setStatistics(response.data.statistics || {});
      }
    } catch (error) {
      console.error('获取审核列表失败:', error);
      message.error('获取审核列表失败');
    } finally {
      setAuditLoading(false);
    }
  };

  // 获取门店列表
  const fetchStoreList = async () => {
    try {
      const response = await apiService.project.store.getList({ page_size: 1000 });
      if (response && response.items) {
        setStoreList(response.items);
      }
    } catch (error) {
      console.error('获取门店列表失败:', error);
      message.error('获取门店列表失败');
    }
  };

  // 获取盘点明细列表
  const fetchItemList = async (storeId, month) => {
    try {
      const response = await apiService.project.finance.getInventoryAuditItems({
        store_id: storeId,
        month: month
      });
      if (response && response.success) {
        setItemList(response.data.items || []);
      }
    } catch (error) {
      console.error('获取盘点明细失败:', error);
      message.error('获取盘点明细失败');
    }
  };

  // 初始化数据
  useEffect(() => {
    fetchAuditList();
  }, [activeTab, filters]);

  useEffect(() => {
    fetchStoreList();
  }, []);

  // 处理审核
  const handleAudit = (record, action) => {
    setSelectedAudit(record);
    auditForm.resetFields();
    auditForm.setFieldsValue({
      action: action,
      audit_remark: ''
    });
    setAuditModalVisible(true);
  };

  // 提交审核
  const handleSubmitAudit = async () => {
    try {
      const values = await auditForm.validateFields();
      setSubmitting(true);

      const data = {
        store_id: selectedAudit.store_id,
        month: selectedAudit.month,
        action: values.action,
        audit_remark: values.audit_remark
      };

      const response = await apiService.project.finance.auditInventoryByStoreMonth(data);
      if (response && response.success) {
        message.success('审核完成');
        setAuditModalVisible(false);
        fetchAuditList();
      }
    } catch (error) {
      console.error('审核失败:', error);
      message.error('审核失败');
    } finally {
      setSubmitting(false);
    }
  };

  // 查看详情
  const handleViewDetail = (record) => {
    setSelectedAudit(record);
    setDetailModalVisible(true);
  };

  // 查看商品明细
  const handleViewItems = async (record) => {
    setSelectedAudit(record);
    await fetchItemList(record.store_id, record.month);
    setItemListModalVisible(true);
  };

  // 批量审核
  const handleBatchAudit = () => {
    if (selectedItems.length === 0) {
      message.error('请选择要审核的记录');
      return;
    }
    batchAuditForm.resetFields();
    setBatchAuditModalVisible(true);
  };

  // 提交批量审核
  const handleSubmitBatchAudit = async () => {
    try {
      const values = await batchAuditForm.validateFields();
      setSubmitting(true);

      const data = {
        items: selectedItems.map(item => ({
          store_id: item.store_id,
          month: item.month
        })),
        action: values.action,
        audit_remark: values.audit_remark
      };

      const response = await apiService.project.finance.batchAuditInventory(data);
      if (response && response.success) {
        message.success(`成功审核${selectedItems.length}条记录`);
        setBatchAuditModalVisible(false);
        setSelectedItems([]);
        fetchAuditList();
      }
    } catch (error) {
      console.error('批量审核失败:', error);
      message.error('批量审核失败');
    } finally {
      setSubmitting(false);
    }
  };

  // 导出数据
  const handleExport = () => {
    // 这里可以实现导出功能
    message.info('导出功能待实现');
  };

  // 上传系统库存
  const handleUploadSystemInventory = () => {
    uploadForm.resetFields();
    setUploadModalVisible(true);
  };

  // 处理文件上传
  const handleFileUpload = async (file, values) => {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('store_id', values.store_id);
    formData.append('month', values.month);
    formData.append('erp_system', values.erp_system || 'unknown');

    try {
      setUploading(true);
      const response = await apiService.project.finance.uploadSystemInventory(formData);
      if (response && response.success) {
        message.success('系统库存上传成功，差异量已自动计算');
        setUploadModalVisible(false);
        fetchAuditList(); // 刷新列表以显示更新的差异量
        return true;
      }
    } catch (error) {
      console.error('上传系统库存失败:', error);
      message.error('上传系统库存失败: ' + (error.message || '未知错误'));
      return false;
    } finally {
      setUploading(false);
    }
  };

  // 提交上传
  const handleSubmitUpload = async () => {
    try {
      const values = await uploadForm.validateFields();
      const fileList = uploadForm.getFieldValue('file');
      
      if (!fileList || fileList.length === 0) {
        message.error('请选择要上传的文件');
        return;
      }

      const file = fileList[0].originFileObj || fileList[0];
      await handleFileUpload(file, values);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  // 审核列表表格列（按门店月份汇总）
  const auditColumns = [
    {
      title: '门店名称',
      dataIndex: 'store_name',
      key: 'store_name',
      width: 150,
      fixed: 'left'
    },
    {
      title: '盘点月份',
      dataIndex: 'month',
      key: 'month',
      width: 120,
      render: (text) => text ? dayjs(text).format('YYYY年MM月') : '-'
    },
    {
      title: '盘点商品数',
      dataIndex: 'total_items',
      key: 'total_items',
      width: 120,
      render: (text) => (
        <Badge count={text} style={{ backgroundColor: '#1890ff' }} />
      )
    },
    {
      title: '有差异商品数',
      dataIndex: 'diff_items',
      key: 'diff_items',
      width: 130,
      render: (text) => (
        <Badge count={text} style={{ backgroundColor: text > 0 ? '#ff4d4f' : '#52c41a' }} />
      )
    },
    {
      title: '总差异金额',
      dataIndex: 'total_diff_amount',
      key: 'total_diff_amount',
      width: 120,
      render: (text) => {
        const amount = parseFloat(text) || 0;
        return (
          <span style={{ color: amount === 0 ? '#52c41a' : amount > 0 ? '#1890ff' : '#ff4d4f' }}>
            {amount > 0 ? '+' : ''}¥{Math.abs(amount).toFixed(2)}
          </span>
        );
      }
    },
    {
      title: '盘点完成率',
      key: 'completion_rate',
      width: 120,
      render: (_, record) => {
        const rate = record.completed_items / record.total_items * 100;
        return (
          <Progress 
            percent={rate} 
            size="small" 
            status={rate === 100 ? 'success' : 'active'}
          />
        );
      }
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status) => {
        const statusMap = {
          'pending': { color: 'processing', text: '待审核' },
          'approved': { color: 'success', text: '已通过' },
          'rejected': { color: 'error', text: '已驳回' }
        };
        const config = statusMap[status] || { color: 'default', text: status };
        return <Tag color={config.color}>{config.text}</Tag>;
      }
    },
    {
      title: '提交时间',
      dataIndex: 'submitted_at',
      key: 'submitted_at',
      width: 150,
      render: (text) => text ? dayjs(text).format('YYYY-MM-DD HH:mm') : '-'
    },
    {
      title: '审核时间',
      dataIndex: 'audited_at',
      key: 'audited_at',
      width: 150,
      render: (text) => text ? dayjs(text).format('YYYY-MM-DD HH:mm') : '-'
    },
    {
      title: '操作',
      key: 'action',
      width: 280,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => handleViewDetail(record)}
          >
            查看汇总
          </Button>
          <Button
            type="text"
            icon={<UnorderedListOutlined />}
            onClick={() => handleViewItems(record)}
          >
            查看明细
          </Button>
          {record.status === 'pending' && (
            <>
              <Button
                type="text"
                icon={<CheckCircleOutlined />}
                onClick={() => handleAudit(record, 'approve')}
                style={{ color: '#52c41a' }}
              >
                通过
              </Button>
              <Button
                type="text"
                icon={<CloseCircleOutlined />}
                onClick={() => handleAudit(record, 'reject')}
                style={{ color: '#ff4d4f' }}
              >
                驳回
              </Button>
            </>
          )}
        </Space>
      )
    }
  ];

  // 商品明细表格列
  const itemColumns = [
    {
      title: '商品编号',
      dataIndex: 'product_code',
      key: 'product_code',
      width: 120
    },
    {
      title: '商品名称',
      dataIndex: 'product_name',
      key: 'product_name',
      width: 200,
      ellipsis: true
    },
    {
      title: '规格',
      dataIndex: 'specification',
      key: 'specification',
      width: 120
    },
    {
      title: '单位',
      dataIndex: 'unit',
      key: 'unit',
      width: 80
    },
    {
      title: '分类',
      dataIndex: 'category_name',
      key: 'category_name',
      width: 120
    },
    {
      title: '库存量',
      dataIndex: 'inventory_quantity',
      key: 'inventory_quantity',
      width: 100,
      render: (text) => text || 0
    },
    {
      title: '实际量',
      dataIndex: 'actual_quantity',
      key: 'actual_quantity',
      width: 100,
      render: (text) => text || 0
    },
    {
      title: '差异',
      key: 'difference',
      width: 100,
      render: (_, record) => {
        const diff = (record.actual_quantity || 0) - (record.inventory_quantity || 0);
        return (
          <span style={{ color: diff === 0 ? '#52c41a' : diff > 0 ? '#1890ff' : '#ff4d4f' }}>
            {diff > 0 ? '+' : ''}{diff}
          </span>
        );
      }
    },
    {
      title: '差异率',
      key: 'difference_rate',
      width: 100,
      render: (_, record) => {
        const inventory = record.inventory_quantity || 0;
        const actual = record.actual_quantity || 0;
        if (inventory === 0) return '-';
        const rate = ((actual - inventory) / inventory * 100).toFixed(2);
        return (
          <span style={{ color: rate == 0 ? '#52c41a' : rate > 0 ? '#1890ff' : '#ff4d4f' }}>
            {rate > 0 ? '+' : ''}{rate}%
          </span>
        );
      }
    },
    {
      title: '备注',
      dataIndex: 'remark',
      key: 'remark',
      width: 150,
      ellipsis: true
    }
  ];

  // 渲染统计卡片
  const renderStatistics = () => (
    <Row gutter={16} style={{ marginBottom: 16 }}>
      <Col span={6}>
        <Card>
          <Statistic
            title="总门店数"
            value={statistics.total}
            prefix={<BarChartOutlined />}
          />
        </Card>
      </Col>
      <Col span={6}>
        <Card>
          <Statistic
            title="待审核"
            value={statistics.pending}
            prefix={<ExclamationCircleOutlined />}
            valueStyle={{ color: '#faad14' }}
          />
        </Card>
      </Col>
      <Col span={6}>
        <Card>
          <Statistic
            title="已通过"
            value={statistics.approved}
            prefix={<CheckCircleOutlined />}
            valueStyle={{ color: '#52c41a' }}
          />
        </Card>
      </Col>
      <Col span={6}>
        <Card>
          <Statistic
            title="已驳回"
            value={statistics.rejected}
            prefix={<CloseCircleOutlined />}
            valueStyle={{ color: '#ff4d4f' }}
          />
        </Card>
      </Col>
    </Row>
  );

  // 渲染商品明细的分类统计
  const renderItemCategoryStatistics = () => {
    // 按分类汇总商品明细数据
    const categoryStats = {};
    
    itemList.forEach(item => {
      const categoryName = item.category_name || '未分类';
      if (!categoryStats[categoryName]) {
        categoryStats[categoryName] = {
          categoryName,
          totalCount: 0,
          totalInventory: 0,
          totalActual: 0,
          totalDifference: 0,
          items: []
        };
      }
      
      const stats = categoryStats[categoryName];
      stats.totalCount += 1;
      stats.totalInventory += item.inventory_quantity || 0;
      stats.totalActual += item.actual_quantity || 0;
      stats.totalDifference += (item.actual_quantity || 0) - (item.inventory_quantity || 0);
      stats.items.push(item);
    });

    const categoryStatsArray = Object.values(categoryStats);

    const categoryColumns = [
      {
        title: '分类名称',
        dataIndex: 'categoryName',
        key: 'categoryName',
        width: 150
      },
      {
        title: '商品数量',
        dataIndex: 'totalCount',
        key: 'totalCount',
        width: 100
      },
      {
        title: '库存总量',
        dataIndex: 'totalInventory',
        key: 'totalInventory',
        width: 120,
        render: (value) => value.toFixed(2)
      },
      {
        title: '实际总量',
        dataIndex: 'totalActual',
        key: 'totalActual',
        width: 120,
        render: (value) => value.toFixed(2)
      },
      {
        title: '差异总量',
        dataIndex: 'totalDifference',
        key: 'totalDifference',
        width: 120,
        render: (value) => (
          <span style={{ color: value === 0 ? '#52c41a' : value > 0 ? '#1890ff' : '#ff4d4f' }}>
            {value > 0 ? '+' : ''}{value.toFixed(2)}
          </span>
        )
      },
      {
        title: '差异率',
        key: 'differenceRate',
        width: 100,
        render: (_, record) => {
          const rate = record.totalInventory > 0 ? 
            (record.totalDifference / record.totalInventory * 100) : 0;
          return (
            <span style={{ color: rate === 0 ? '#52c41a' : rate > 0 ? '#1890ff' : '#ff4d4f' }}>
              {rate > 0 ? '+' : ''}{rate.toFixed(2)}%
            </span>
          );
        }
      }
    ];

    return (
      <div>
        <Row gutter={16} style={{ marginBottom: 16 }}>
          <Col span={6}>
            <Card size="small">
              <Statistic
                title="分类总数"
                value={categoryStatsArray.length}
                prefix={<PieChartOutlined />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card size="small">
              <Statistic
                title="总库存量"
                value={categoryStatsArray.reduce((sum, cat) => sum + cat.totalInventory, 0).toFixed(2)}
                prefix={<BarChartOutlined />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card size="small">
              <Statistic
                title="总实际量"
                value={categoryStatsArray.reduce((sum, cat) => sum + cat.totalActual, 0).toFixed(2)}
                prefix={<BarChartOutlined />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card size="small">
              <Statistic
                title="总差异量"
                value={categoryStatsArray.reduce((sum, cat) => sum + cat.totalDifference, 0).toFixed(2)}
                prefix={<BarChartOutlined />}
                valueStyle={{ 
                  color: categoryStatsArray.reduce((sum, cat) => sum + cat.totalDifference, 0) >= 0 ? 
                    '#1890ff' : '#ff4d4f' 
                }}
              />
            </Card>
          </Col>
        </Row>

        <Table
          columns={categoryColumns}
          dataSource={categoryStatsArray}
          rowKey="categoryName"
          pagination={false}
          size="small"
          scroll={{ y: 300 }}
        />
      </div>
    );
  };

  // 渲染筛选器
  const renderFilters = () => (
    <Card style={{ marginBottom: 16 }}>
      <Row gutter={16} align="middle">
        <Col span={4}>
          <Select
            placeholder="选择门店"
            style={{ width: '100%' }}
            value={filters.store_id}
            onChange={(value) => setFilters({ ...filters, store_id: value })}
            allowClear
          >
            {storeList.map(store => (
              <Option key={store.id} value={store.id}>{store.name}</Option>
            ))}
          </Select>
        </Col>
        <Col span={4}>
          <DatePicker
            picker="month"
            placeholder="选择月份"
            style={{ width: '100%' }}
            value={filters.month ? dayjs(filters.month) : null}
            onChange={(date) => setFilters({ ...filters, month: date ? date.format('YYYY-MM') : null })}
          />
        </Col>
        <Col span={6}>
          <RangePicker
            placeholder={['开始日期', '结束日期']}
            style={{ width: '100%' }}
            value={filters.date_range}
            onChange={(dates) => setFilters({ ...filters, date_range: dates })}
          />
        </Col>
        <Col span={10}>
          <Space>
            <Button
              type="primary"
              icon={<CheckCircleOutlined />}
              onClick={handleBatchAudit}
              disabled={selectedItems.length === 0}
            >
              批量通过
            </Button>
            <Button
              type="primary"
              icon={<CloudUploadOutlined />}
              onClick={handleUploadSystemInventory}
              style={{ backgroundColor: '#722ed1', borderColor: '#722ed1' }}
            >
              上传系统库存
            </Button>
            <Button
              icon={<DownloadOutlined />}
              onClick={handleExport}
            >
              导出数据
            </Button>
            <Button
              icon={<ReloadOutlined />}
              onClick={fetchAuditList}
            >
              刷新
            </Button>
          </Space>
        </Col>
      </Row>
    </Card>
  );

  return (
    <div className="inventory-audit">
      <Alert
        message="盘点审核说明"
        description="对门店提交的月度盘点数据进行审核，以门店月份为单位进行汇总显示。可以查看汇总信息和详细商品明细，支持批量审核操作。"
        type="info"
        showIcon
        style={{ marginBottom: 16 }}
      />

      {renderFilters()}
      {renderStatistics()}

      <Card>
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab="待审核" key="pending">
            <Table
              columns={auditColumns}
              dataSource={auditList}
              rowKey={(record) => `${record.store_id}-${record.month}`}
              loading={auditLoading}
              scroll={{ x: 1800 }}
              rowSelection={{
                selectedRowKeys: selectedItems.map(item => `${item.store_id}-${item.month}`),
                onChange: (selectedRowKeys, selectedRows) => {
                  setSelectedItems(selectedRows);
                }
              }}
              pagination={{
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条记录`
              }}
            />
          </TabPane>

          <TabPane tab="已通过" key="approved">
            <Table
              columns={auditColumns}
              dataSource={auditList}
              rowKey={(record) => `${record.store_id}-${record.month}`}
              loading={auditLoading}
              scroll={{ x: 1800 }}
              pagination={{
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条记录`
              }}
            />
          </TabPane>

          <TabPane tab="已驳回" key="rejected">
            <Table
              columns={auditColumns}
              dataSource={auditList}
              rowKey={(record) => `${record.store_id}-${record.month}`}
              loading={auditLoading}
              scroll={{ x: 1800 }}
              pagination={{
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条记录`
              }}
            />
          </TabPane>

          <TabPane tab="全部" key="all">
            <Table
              columns={auditColumns}
              dataSource={auditList}
              rowKey={(record) => `${record.store_id}-${record.month}`}
              loading={auditLoading}
              scroll={{ x: 1800 }}
              pagination={{
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条记录`
              }}
            />
          </TabPane>
        </Tabs>
      </Card>

      {/* 审核模态框 */}
      <Modal
        title="盘点审核"
        open={auditModalVisible}
        onOk={handleSubmitAudit}
        onCancel={() => setAuditModalVisible(false)}
        confirmLoading={submitting}
        width={600}
      >
        {selectedAudit && (
          <div>
            <div style={{ marginBottom: 16, padding: 16, backgroundColor: '#f5f5f5', borderRadius: 4 }}>
              <Row gutter={16}>
                <Col span={12}>
                  <div><strong>门店：</strong>{selectedAudit.store_name}</div>
                </Col>
                <Col span={12}>
                  <div><strong>月份：</strong>{dayjs(selectedAudit.month).format('YYYY年MM月')}</div>
                </Col>
              </Row>
              <Divider style={{ margin: '8px 0' }} />
              <Row gutter={16}>
                <Col span={8}>
                  <div><strong>盘点商品数：</strong>{selectedAudit.total_items}</div>
                </Col>
                <Col span={8}>
                  <div><strong>有差异商品：</strong>{selectedAudit.diff_items}</div>
                </Col>
                <Col span={8}>
                  <div>
                    <strong>总差异金额：</strong>
                    <span style={{ 
                      color: selectedAudit.total_diff_amount === 0 ? '#52c41a' : '#ff4d4f' 
                    }}>
                      ¥{Math.abs(selectedAudit.total_diff_amount || 0).toFixed(2)}
                    </span>
                  </div>
                </Col>
              </Row>
            </div>

            <Form
              form={auditForm}
              layout="vertical"
            >
              <Form.Item
                name="action"
                label="审核结果"
                rules={[{ required: true, message: '请选择审核结果' }]}
              >
                <Select placeholder="请选择审核结果">
                  <Option value="approve">通过</Option>
                  <Option value="reject">驳回</Option>
                </Select>
              </Form.Item>

              <Form.Item
                name="audit_remark"
                label="审核备注"
                rules={[{ required: true, message: '请输入审核备注' }]}
              >
                <TextArea
                  rows={4}
                  placeholder="请输入审核备注"
                />
              </Form.Item>
            </Form>
          </div>
        )}
      </Modal>

      {/* 汇总详情查看模态框 */}
      <Modal
        title="盘点汇总详情"
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setDetailModalVisible(false)}>
            关闭
          </Button>
        ]}
        width={700}
      >
        {selectedAudit && (
          <div>
            <Row gutter={16}>
              <Col span={12}>
                <div><strong>门店名称：</strong>{selectedAudit.store_name}</div>
              </Col>
              <Col span={12}>
                <div><strong>盘点月份：</strong>{dayjs(selectedAudit.month).format('YYYY年MM月')}</div>
              </Col>
            </Row>
            <Divider />
            <Row gutter={16}>
              <Col span={8}>
                <div><strong>盘点商品数：</strong>{selectedAudit.total_items}</div>
              </Col>
              <Col span={8}>
                <div><strong>已完成数：</strong>{selectedAudit.completed_items}</div>
              </Col>
              <Col span={8}>
                <div><strong>完成率：</strong>{((selectedAudit.completed_items / selectedAudit.total_items) * 100).toFixed(1)}%</div>
              </Col>
            </Row>
            <Divider />
            <Row gutter={16}>
              <Col span={8}>
                <div><strong>有差异商品：</strong>{selectedAudit.diff_items}</div>
              </Col>
              <Col span={8}>
                <div><strong>差异率：</strong>{((selectedAudit.diff_items / selectedAudit.total_items) * 100).toFixed(1)}%</div>
              </Col>
              <Col span={8}>
                <div>
                  <strong>总差异金额：</strong>
                  <span style={{ 
                    color: selectedAudit.total_diff_amount === 0 ? '#52c41a' : '#ff4d4f' 
                  }}>
                    ¥{Math.abs(selectedAudit.total_diff_amount || 0).toFixed(2)}
                  </span>
                </div>
              </Col>
            </Row>
            <Divider />
            <Row gutter={16}>
              <Col span={12}>
                <div><strong>提交时间：</strong>{dayjs(selectedAudit.submitted_at).format('YYYY-MM-DD HH:mm:ss')}</div>
              </Col>
              <Col span={12}>
                <div><strong>审核时间：</strong>{selectedAudit.audited_at ? dayjs(selectedAudit.audited_at).format('YYYY-MM-DD HH:mm:ss') : '-'}</div>
              </Col>
            </Row>
            {selectedAudit.audit_remark && (
              <>
                <Divider />
                <div><strong>审核备注：</strong>{selectedAudit.audit_remark}</div>
              </>
            )}
          </div>
        )}
      </Modal>

      {/* 商品明细模态框 */}
      <Modal
        title={`商品明细 - ${selectedAudit?.store_name} - ${selectedAudit ? dayjs(selectedAudit.month).format('YYYY年MM月') : ''}`}
        open={itemListModalVisible}
        onCancel={() => setItemListModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setItemListModalVisible(false)}>
            关闭
          </Button>
        ]}
        width={1200}
        style={{ top: 20 }}
      >
        <Tabs defaultActiveKey="list">
          <TabPane tab="商品列表" key="list">
            <Table
              columns={itemColumns}
              dataSource={itemList}
              rowKey="id"
              scroll={{ y: 400 }}
              pagination={{
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条记录`
              }}
            />
          </TabPane>
          <TabPane tab="分类汇总" key="category">
            {renderItemCategoryStatistics()}
          </TabPane>
        </Tabs>
      </Modal>

      {/* 批量审核模态框 */}
      <Modal
        title="批量审核"
        open={batchAuditModalVisible}
        onOk={handleSubmitBatchAudit}
        onCancel={() => setBatchAuditModalVisible(false)}
        confirmLoading={submitting}
        width={600}
      >
        <div style={{ marginBottom: 16 }}>
          <Alert
            message={`已选择 ${selectedItems.length} 条记录进行批量审核`}
            type="info"
            showIcon
          />
        </div>

        <Form
          form={batchAuditForm}
          layout="vertical"
        >
          <Form.Item
            name="action"
            label="审核结果"
            rules={[{ required: true, message: '请选择审核结果' }]}
          >
            <Select placeholder="请选择审核结果">
              <Option value="approve">通过</Option>
              <Option value="reject">驳回</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="audit_remark"
            label="审核备注"
            rules={[{ required: true, message: '请输入审核备注' }]}
          >
            <TextArea
              rows={4}
              placeholder="请输入审核备注"
            />
          </Form.Item>
        </Form>
      </Modal>

      {/* 上传系统库存模态框 */}
      <Modal
        title="上传第三方ERP系统库存"
        open={uploadModalVisible}
        onOk={handleSubmitUpload}
        onCancel={() => setUploadModalVisible(false)}
        confirmLoading={uploading}
        width={600}
      >
        <Alert
          message="上传说明"
          description="上传第三方ERP系统的库存数据，系统将自动计算与门店实际盘点的差异量。支持Excel格式文件，请确保文件包含商品编号、库存数量等必要字段。"
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />

        <Form
          form={uploadForm}
          layout="vertical"
        >
          <Form.Item
            name="store_id"
            label="选择门店"
            rules={[{ required: true, message: '请选择门店' }]}
          >
            <Select placeholder="请选择门店" showSearch optionFilterProp="children">
              {storeList.map(store => (
                <Option key={store.id} value={store.id}>
                  {store.name}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="month"
            label="盘点月份"
            rules={[{ required: true, message: '请选择盘点月份' }]}
          >
            <DatePicker
              picker="month"
              placeholder="选择盘点月份"
              style={{ width: '100%' }}
              format="YYYY-MM"
            />
          </Form.Item>

          <Form.Item
            name="erp_system"
            label="ERP系统类型"
            initialValue="unknown"
          >
            <Select placeholder="请选择ERP系统类型">
              <Option value="sap">SAP</Option>
              <Option value="oracle">Oracle ERP</Option>
              <Option value="kingdee">金蝶</Option>
              <Option value="yonyou">用友</Option>
              <Option value="custom">自定义系统</Option>
              <Option value="unknown">其他</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="file"
            label="选择文件"
            rules={[{ required: true, message: '请选择要上传的文件' }]}
          >
            <Upload
              beforeUpload={() => false} // 阻止自动上传
              accept=".xlsx,.xls"
              maxCount={1}
              listType="text"
            >
              <Button icon={<UploadOutlined />}>选择Excel文件</Button>
            </Upload>
          </Form.Item>

          <Alert
            message="文件格式要求"
            description={
              <div>
                <p>Excel文件应包含以下列：</p>
                <ul style={{ marginBottom: 0, paddingLeft: 20 }}>
                  <li>商品编号 - 必填</li>
                  <li>商品名称 - 推荐</li>
                  <li>系统库存数量 - 必填</li>
                  <li>单位 - 推荐</li>
                  <li>规格 - 推荐</li>
                  <li>分类 - 推荐</li>
                </ul>
                <p style={{ marginTop: 8, marginBottom: 0, fontSize: '12px', color: '#666' }}>
                  注：商品编号和系统库存数量为必填项，其他字段有助于更准确地匹配商品信息
                </p>
              </div>
            }
            type="warning"
            showIcon
          />
        </Form>
      </Modal>
    </div>
  );
};

export default InventoryAudit; 