import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Card,
  Descriptions,
  Button,
  Space,
  Spin,
  message,
  Typography,
  Row,
  Col,
  Image,
  Tag,
  Breadcrumb
} from 'antd';
import {
  ArrowLeftOutlined,
  EditOutlined,
  DeleteOutlined,
  ShoppingOutlined,
  TagOutlined,
  BarcodeOutlined,
  DollarOutlined
} from '@ant-design/icons';
import productAPI from '../../../services/api/project/product';
import { useProject } from '../../../contexts/ProjectContext';

const { Title } = Typography;

const ProductDetail = () => {
  const { productId } = useParams();
  const { currentProject } = useProject();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [product, setProduct] = useState(null);

  // 获取产品详情
  useEffect(() => {
    if (currentProject?.id && productId) {
      fetchProductDetail();
    }
  }, [currentProject?.id, productId]);

  const fetchProductDetail = async () => {
    try {
      setLoading(true);
      const response = await productAPI.getProduct(currentProject.id, productId);
      
      if (response && response.success) {
        setProduct(response.data);
      } else {
        message.error(response?.message || '获取产品详情失败');
      }
    } catch (error) {
      console.error('获取产品详情失败:', error);
      message.error('获取产品详情失败: ' + (error.response?.data?.detail || error.message || '服务器错误'));
    } finally {
      setLoading(false);
    }
  };

  // 处理编辑产品
  const handleEdit = () => {
    navigate(`/project/purchase/products`, { state: { editProduct: product } });
  };

  // 处理删除产品
  const handleDelete = async () => {
    try {
      const response = await productAPI.deleteProduct(currentProject.id, productId);
      
      if (response && response.success) {
        message.success('产品删除成功');
        navigate(`/project/purchase/products`);
      } else {
        message.error(response?.message || '删除产品失败');
      }
    } catch (error) {
      console.error('删除产品失败:', error);
      message.error('删除产品失败: ' + (error.response?.data?.detail || error.message || '服务器错误'));
    }
  };

  // 返回产品列表
  const handleBack = () => {
    navigate(`/project/purchase/products`);
  };

  if (loading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
        <Spin size="large" tip="加载中..." />
      </div>
    );
  }

  if (!product) {
    return (
      <Card>
        <div style={{ textAlign: 'center' }}>
          <Title level={4}>未找到产品信息</Title>
          <Button type="primary" onClick={handleBack}>返回产品列表</Button>
        </div>
      </Card>
    );
  }

  return (
    <div className="product-detail-page">
      <Breadcrumb style={{ marginBottom: 16 }}>
        <Breadcrumb.Item>
          <a onClick={handleBack}>产品管理</a>
        </Breadcrumb.Item>
        <Breadcrumb.Item>产品详情</Breadcrumb.Item>
      </Breadcrumb>

      <Card
        title={
          <Space>
            <Button icon={<ArrowLeftOutlined />} onClick={handleBack}>返回</Button>
            <Title level={4} style={{ margin: 0 }}>{product.display_name || product.name}</Title>
            {product.is_active ? 
              <Tag color="green">在售</Tag> : 
              <Tag color="red">已下架</Tag>
            }
            {product.is_variant && (
              <Tag color="blue">多规格</Tag>
            )}
          </Space>
        }
        extra={
          <Space>
            <Button icon={<EditOutlined />} onClick={handleEdit}>编辑</Button>
            <Button icon={<DeleteOutlined />} danger onClick={handleDelete}>删除</Button>
          </Space>
        }
      >
        <Row gutter={24}>
          <Col span={8}>
            <Card>
              <div style={{ textAlign: 'center' }}>
                {product.image_url ? (
                  <Image
                    src={product.image_url}
                    alt={product.name}
                    style={{ maxWidth: '100%', maxHeight: 300 }}
                  />
                ) : (
                  <div style={{ 
                    width: '100%', 
                    height: 200, 
                    background: '#f0f0f0', 
                    display: 'flex', 
                    justifyContent: 'center', 
                    alignItems: 'center' 
                  }}>
                    <ShoppingOutlined style={{ fontSize: 64, color: '#d9d9d9' }} />
                  </div>
                )}
              </div>
            </Card>
          </Col>
          
          <Col span={16}>
            <Descriptions title="基本信息" bordered column={2}>
              <Descriptions.Item label="产品名称" span={2}>{product.name}</Descriptions.Item>
              <Descriptions.Item label="显示名称" span={2}>{product.display_name || '-'}</Descriptions.Item>
              <Descriptions.Item label="SKU">
                <Space>
                  <BarcodeOutlined />
                  {product.sku}
                </Space>
              </Descriptions.Item>
              <Descriptions.Item label="条码">
                {product.barcode || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="品牌">
                <Space>
                  <TagOutlined />
                  {product.brand_name || '-'}
                </Space>
              </Descriptions.Item>
              <Descriptions.Item label="分类">
                {product.category_name || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="规格" span={1}>
                {product.specification || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="单位" span={1}>
                {product.unit || '公斤'}
              </Descriptions.Item>
              <Descriptions.Item label="成本价" span={1}>
                <Space>
                  <DollarOutlined />
                  {product.cost_price ? `¥${parseFloat(product.cost_price).toFixed(2)}` : '-'}
                </Space>
              </Descriptions.Item>
              <Descriptions.Item label="零售价" span={1}>
                <Space>
                  <DollarOutlined />
                  {product.retail_price ? `¥${parseFloat(product.retail_price).toFixed(2)}` : '-'}
                </Space>
              </Descriptions.Item>
              <Descriptions.Item label="商品组ID" span={2}>
                {product.product_group_id || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="是否多规格" span={1}>
                {product.is_variant ? <Tag color="blue">是</Tag> : <Tag>否</Tag>}
              </Descriptions.Item>
              <Descriptions.Item label="状态" span={1}>
                {product.is_active ? <Tag color="green">在售</Tag> : <Tag color="red">已下架</Tag>}
              </Descriptions.Item>
              <Descriptions.Item label="创建时间" span={2}>
                {product.created_at ? new Date(product.created_at).toLocaleString() : '-'}
              </Descriptions.Item>
              <Descriptions.Item label="更新时间" span={2}>
                {product.updated_at ? new Date(product.updated_at).toLocaleString() : '-'}
              </Descriptions.Item>
              <Descriptions.Item label="描述" span={2}>
                {product.description || '-'}
              </Descriptions.Item>
            </Descriptions>
          </Col>
        </Row>
      </Card>
    </div>
  );
};

export default ProductDetail;