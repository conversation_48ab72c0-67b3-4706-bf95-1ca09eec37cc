-- 钉钉插件完整数据库表结构创建脚本

-- 创建钉钉Webhook配置表
CREATE TABLE IF NOT EXISTS dingtalk_webhooks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL,
    project_id UUID NOT NULL,
    
    -- 基本信息
    name VARCHAR(100) NOT NULL,
    webhook_type VARCHAR(32) DEFAULT 'robot',
    
    -- Webhook配置
    webhook_url VARCHAR(512),
    callback_url VARCHAR(512),
    secret VARCHAR(256),
    enabled BOOLEAN DEFAULT true,
    
    -- HTTP推送模式配置
    subscription_mode VARCHAR(32) DEFAULT 'http',
    aes_key VARCHAR(256),
    token VARCHAR(256),
    
    -- Stream模式配置
    stream_endpoint VARCHAR(512),
    stream_status VARCHAR(32) DEFAULT 'disconnected',
    
    -- 机器人通知配置
    message_template TEXT,
    notification_types TEXT[],
    target_users TEXT[],
    target_groups TEXT[],
    
    -- 事件订阅配置
    event_types JSONB,
    
    -- 描述和状态
    description TEXT,
    last_triggered_at TIMESTAMP WITH TIME ZONE,
    created_by <PERSON><PERSON><PERSON>,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 更新现有的dingtalk_event_logs表结构
ALTER TABLE dingtalk_event_logs 
ADD COLUMN IF NOT EXISTS webhook_id UUID,
ADD COLUMN IF NOT EXISTS processing_time INTEGER,
ADD COLUMN IF NOT EXISTS retry_count INTEGER DEFAULT 0,
ADD COLUMN IF NOT EXISTS error_details TEXT;

-- 创建钉钉用户映射表
CREATE TABLE IF NOT EXISTS dingtalk_user_mappings (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL,
    project_id UUID NOT NULL,
    user_id UUID NOT NULL,
    dingtalk_user_id VARCHAR(100) NOT NULL,
    dingtalk_union_id VARCHAR(100),
    dingtalk_name VARCHAR(100),
    dingtalk_mobile VARCHAR(20),
    dingtalk_email VARCHAR(100),
    dingtalk_department VARCHAR(200),
    dingtalk_position VARCHAR(100),
    dingtalk_avatar VARCHAR(500),
    dingtalk_dept_id VARCHAR(50),
    dingtalk_dept_name VARCHAR(200),
    dingtalk_dept_path VARCHAR(500),
    dingtalk_job_number VARCHAR(50),
    dingtalk_title VARCHAR(100),
    dingtalk_work_place VARCHAR(200),
    dingtalk_remark VARCHAR(500),
    dingtalk_order_in_dept INTEGER,
    dingtalk_extension JSONB,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建钉钉部门表
CREATE TABLE IF NOT EXISTS dingtalk_departments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL,
    project_id UUID NOT NULL,
    dept_id VARCHAR(50) NOT NULL,
    name VARCHAR(200) NOT NULL,
    parent_id VARCHAR(50),
    dept_path VARCHAR(500),
    level INTEGER DEFAULT 1,
    "order" INTEGER DEFAULT 0,
    member_count INTEGER DEFAULT 0,
    dept_manager_userid_list TEXT[],
    dept_hiding BOOLEAN DEFAULT false,
    outer_dept BOOLEAN DEFAULT false,
    source_identifier VARCHAR(100),
    ext JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建钉钉配置表
CREATE TABLE IF NOT EXISTS dingtalk_configs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL,
    project_id UUID NOT NULL,
    app_key VARCHAR(128) NOT NULL,
    app_secret VARCHAR(256) NOT NULL,
    webhook_url VARCHAR(512),
    enabled BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建钉钉访问令牌表
CREATE TABLE IF NOT EXISTS dingtalk_access_tokens (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    project_id UUID NOT NULL,
    access_token VARCHAR(512) NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_dingtalk_webhooks_tenant_project ON dingtalk_webhooks(tenant_id, project_id);
CREATE INDEX IF NOT EXISTS idx_dingtalk_webhooks_subscription_mode ON dingtalk_webhooks(subscription_mode);
CREATE INDEX IF NOT EXISTS idx_dingtalk_webhooks_stream_status ON dingtalk_webhooks(stream_status) WHERE subscription_mode = 'stream';
CREATE INDEX IF NOT EXISTS idx_dingtalk_webhooks_enabled ON dingtalk_webhooks(enabled);

CREATE INDEX IF NOT EXISTS idx_dingtalk_event_logs_webhook_id ON dingtalk_event_logs(webhook_id);
CREATE INDEX IF NOT EXISTS idx_dingtalk_event_logs_event_type_created ON dingtalk_event_logs(event_type, created_at);
CREATE INDEX IF NOT EXISTS idx_dingtalk_event_logs_processed ON dingtalk_event_logs(processed, created_at);

CREATE INDEX IF NOT EXISTS idx_dingtalk_user_mappings_tenant_project_user ON dingtalk_user_mappings(tenant_id, project_id, user_id);
CREATE INDEX IF NOT EXISTS idx_dingtalk_user_mappings_dingtalk_user_id ON dingtalk_user_mappings(dingtalk_user_id);
CREATE INDEX IF NOT EXISTS idx_dingtalk_user_mappings_dingtalk_union_id ON dingtalk_user_mappings(dingtalk_union_id);

CREATE INDEX IF NOT EXISTS idx_dingtalk_departments_tenant_project_dept ON dingtalk_departments(tenant_id, project_id, dept_id);
CREATE INDEX IF NOT EXISTS idx_dingtalk_departments_parent_id ON dingtalk_departments(parent_id);

CREATE INDEX IF NOT EXISTS idx_dingtalk_configs_tenant_project ON dingtalk_configs(tenant_id, project_id);
CREATE INDEX IF NOT EXISTS idx_dingtalk_access_tokens_project ON dingtalk_access_tokens(project_id);
CREATE INDEX IF NOT EXISTS idx_dingtalk_access_tokens_expires ON dingtalk_access_tokens(expires_at);

-- 添加唯一约束
ALTER TABLE dingtalk_user_mappings 
ADD CONSTRAINT IF NOT EXISTS uq_dingtalk_user_mapping 
UNIQUE (tenant_id, project_id, user_id);

ALTER TABLE dingtalk_departments 
ADD CONSTRAINT IF NOT EXISTS uq_dingtalk_department 
UNIQUE (tenant_id, project_id, dept_id);

ALTER TABLE dingtalk_configs 
ADD CONSTRAINT IF NOT EXISTS uq_dingtalk_config 
UNIQUE (tenant_id, project_id);

ALTER TABLE dingtalk_access_tokens 
ADD CONSTRAINT IF NOT EXISTS uq_dingtalk_access_token 
UNIQUE (project_id);

-- 添加外键约束（如果相关表存在）
-- 注意：这些约束可能需要根据实际的表结构调整
-- ALTER TABLE dingtalk_webhooks ADD CONSTRAINT fk_dingtalk_webhooks_tenant FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE;
-- ALTER TABLE dingtalk_webhooks ADD CONSTRAINT fk_dingtalk_webhooks_project FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE;

-- 添加注释
COMMENT ON TABLE dingtalk_webhooks IS '钉钉Webhook配置表，支持机器人通知和事件订阅';
COMMENT ON COLUMN dingtalk_webhooks.subscription_mode IS '订阅模式: http为HTTP推送模式，stream为Stream模式';
COMMENT ON COLUMN dingtalk_webhooks.aes_key IS 'HTTP推送模式的AES加密密钥';
COMMENT ON COLUMN dingtalk_webhooks.token IS 'HTTP推送模式的签名Token';
COMMENT ON COLUMN dingtalk_webhooks.stream_endpoint IS 'Stream模式的WebSocket连接端点';
COMMENT ON COLUMN dingtalk_webhooks.stream_status IS 'Stream模式的连接状态';

COMMENT ON TABLE dingtalk_event_logs IS '钉钉事件处理日志表';
COMMENT ON COLUMN dingtalk_event_logs.processing_time IS '处理耗时（毫秒）';
COMMENT ON COLUMN dingtalk_event_logs.retry_count IS '重试次数';
COMMENT ON COLUMN dingtalk_event_logs.error_details IS '错误详情';

COMMENT ON TABLE dingtalk_user_mappings IS '钉钉用户映射表';
COMMENT ON TABLE dingtalk_departments IS '钉钉部门结构表';
COMMENT ON TABLE dingtalk_configs IS '钉钉应用配置表';
COMMENT ON TABLE dingtalk_access_tokens IS '钉钉访问令牌表';

-- 输出执行结果
SELECT '钉钉插件数据库表结构创建完成' as result; 