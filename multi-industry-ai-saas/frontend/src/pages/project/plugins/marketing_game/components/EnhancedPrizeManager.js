import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  Table,
  Form,
  Input,
  InputNumber,
  Select,
  DatePicker,
  TimePicker,
  Modal,
  message,
  Upload,
  Row,
  Col,
  Tabs,
  Tag,
  Tooltip,
  Space,
  Divider,
  Typography,
  Progress,
  Collapse,
  Checkbox
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  UploadOutlined,
  GiftOutlined,
  ClockCircleOutlined,
  CalendarOutlined,
  StarOutlined,
  ThunderboltOutlined,
  FireOutlined,
  CrownOutlined,
  EyeOutlined,
  SaveOutlined
} from '@ant-design/icons';
import dayjs from 'dayjs';
import marketingGameApi from '../service/api/marketingGame';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;
const { TabPane } = Tabs;
const { Panel } = Collapse;

const EnhancedPrizeManager = ({ gameId, visible, onClose, onSuccess }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [prizes, setPrizes] = useState([]);
  const [editingPrize, setEditingPrize] = useState(null);
  const [showPrizeForm, setShowPrizeForm] = useState(false);
  const [activeTab, setActiveTab] = useState('list');
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewPrize, setPreviewPrize] = useState(null);

  // 奖品类型配置
  const PRIZE_TYPES = {
    physical: { label: '实物奖品', color: 'blue', icon: <GiftOutlined /> },
    coupon: { label: '优惠券', color: 'orange', icon: <StarOutlined /> },
    points: { label: '积分', color: 'green', icon: <ThunderboltOutlined /> },
    virtual: { label: '虚拟物品', color: 'purple', icon: <CrownOutlined /> }
  };

  // 动画样式选项
  const ANIMATION_STYLES = [
    { value: 'default', label: '默认动画' },
    { value: 'bounce', label: '弹跳效果' },
    { value: 'fadeIn', label: '淡入效果' },
    { value: 'slideDown', label: '下滑效果' },
    { value: 'zoomIn', label: '缩放效果' },
    { value: 'rotate', label: '旋转效果' },
    { value: 'shake', label: '震动效果' },
    { value: 'pulse', label: '脉冲效果' }
  ];

  // 默认奖品图标 (base64 encoded placeholder)
  const DEFAULT_PRIZE_IMAGE = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjQwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjRjVGNUY1Ii8+CjxwYXRoIGQ9Ik0yMCAzMEMyNS41MjI4IDMwIDMwIDI1LjUyMjggMzAgMjBDMzAgMTQuNDc3MiAyNS41MjI4IDEwIDIwIDEwQzE0LjQ3NzIgMTAgMTAgMTQuNDc3MiAxMCAyMEMxMCAyNS41MjI4IDE0LjQ3NzIgMzAgMjAgMzBaIiBzdHJva2U9IiNDQ0NDQ0MiIHN0cm9rZS13aWR0aD0iMiIvPgo8cGF0aCBkPSJNMjAgMTVWMjVNMTUgMjBIMjUiIHN0cm9rZT0iI0NDQ0NDQyIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiLz4KPC9zdmc+Cg==';

  // 获取奖品列表
  const fetchPrizes = async () => {
    try {
      setLoading(true);
      const response = await marketingGameApi.getPrizes(gameId);
      if (response.success) {
        setPrizes(response.data || []);
      }
    } catch (error) {
      console.error('获取奖品列表失败:', error);
      message.error('获取奖品列表失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (visible && gameId) {
      fetchPrizes();
    }
  }, [visible, gameId]);

  // 保存奖品
  const handleSavePrize = async (values) => {
    try {
      setLoading(true);
      
      // 处理特殊条件数据
      const specialConditions = {};
      if (values.min_user_level) {
        specialConditions.min_user_level = values.min_user_level;
      }
      if (values.min_consumption_amount) {
        specialConditions.min_consumption_amount = values.min_consumption_amount;
      }
      if (values.required_member_type) {
        specialConditions.required_member_type = values.required_member_type;
      }

      // 处理保底条件数据
      const guaranteedCondition = {};
      if (values.is_guaranteed) {
        if (values.consecutive_losses) {
          guaranteedCondition.consecutive_losses = values.consecutive_losses;
        }
        if (values.total_draws) {
          guaranteedCondition.total_draws = values.total_draws;
        }
      }

      const prizeData = {
        ...values,
        special_conditions: Object.keys(specialConditions).length > 0 ? specialConditions : null,
        guaranteed_condition: Object.keys(guaranteedCondition).length > 0 ? guaranteedCondition : null,
        // 处理时间字段
        effective_date: values.effective_date ? values.effective_date.format('YYYY-MM-DD HH:mm:ss') : null,
        expiry_date: values.expiry_date ? values.expiry_date.format('YYYY-MM-DD HH:mm:ss') : null,
        effective_time_start: values.effective_time_start ? values.effective_time_start.format('HH:mm:ss') : null,
        effective_time_end: values.effective_time_end ? values.effective_time_end.format('HH:mm:ss') : null
      };

      let response;
      if (editingPrize) {
        response = await marketingGameApi.updatePrize(gameId, editingPrize.id, prizeData);
      } else {
        response = await marketingGameApi.createPrize(gameId, prizeData);
      }

      if (response.success) {
        message.success(editingPrize ? '奖品更新成功' : '奖品创建成功');
        setShowPrizeForm(false);
        setEditingPrize(null);
        form.resetFields();
        fetchPrizes();
        if (onSuccess) onSuccess();
      } else {
        message.error(response.message || '操作失败');
      }
    } catch (error) {
      console.error('保存奖品失败:', error);
      message.error('保存奖品失败');
    } finally {
      setLoading(false);
    }
  };

  // 删除奖品
  const handleDeletePrize = async (prize) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除奖品"${prize.name}"吗？`,
      onOk: async () => {
        try {
          const response = await marketingGameApi.deletePrize(gameId, prize.id);
          if (response.success) {
            message.success('奖品删除成功');
            fetchPrizes();
          } else {
            message.error(response.message || '删除失败');
          }
        } catch (error) {
          console.error('删除奖品失败:', error);
          message.error('删除奖品失败');
        }
      }
    });
  };

  // 编辑奖品
  const handleEditPrize = (prize) => {
    setEditingPrize(prize);
    
    // 填充表单数据
    const formData = {
      ...prize,
      effective_date: prize.effective_date ? dayjs(prize.effective_date) : null,
      expiry_date: prize.expiry_date ? dayjs(prize.expiry_date) : null,
      effective_time_start: prize.effective_time_start ? dayjs(prize.effective_time_start, 'HH:mm:ss') : null,
      effective_time_end: prize.effective_time_end ? dayjs(prize.effective_time_end, 'HH:mm:ss') : null,
      // 展开特殊条件
      ...(prize.special_conditions || {}),
      // 展开保底条件
      ...(prize.guaranteed_condition || {})
    };
    
    form.setFieldsValue(formData);
    setShowPrizeForm(true);
    setActiveTab('form');
  };

  // 预览奖品
  const handlePreviewPrize = (prize) => {
    setPreviewPrize(prize);
    setPreviewVisible(true);
  };

  // 表格列定义
  const columns = [
    {
      title: '奖品信息',
      key: 'info',
      width: 200,
      render: (_, record) => (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <img
            src={record.image_url || '/placeholder-prize.png'}
            alt={record.name}
            style={{ width: 40, height: 40, borderRadius: 4, marginRight: 12 }}
            onError={(e) => {
              // 首先尝试 placeholder 图片
              if (e.target.src !== '/placeholder-prize.png') {
                e.target.src = '/placeholder-prize.png';
              } else {
                // 如果 placeholder 也失败，使用 base64 编码的默认图片
                e.target.src = DEFAULT_PRIZE_IMAGE;
              }
            }}
          />
          <div>
            <div style={{ fontWeight: 'bold' }}>{record.name}</div>
            <div style={{ fontSize: 12, color: '#666' }}>
              {PRIZE_TYPES[record.prize_type]?.label || record.prize_type}
            </div>
          </div>
        </div>
      )
    },
    {
      title: '数量/概率',
      key: 'quantity',
      width: 120,
      render: (_, record) => (
        <div>
          <div>总量: {record.quantity}</div>
          <div>剩余: <Text type={record.remaining > 0 ? 'success' : 'danger'}>{record.remaining}</Text></div>
          <div>概率: {(record.probability * 100).toFixed(1)}%</div>
        </div>
      )
    },
    {
      title: '时间控制',
      key: 'time',
      width: 150,
      render: (_, record) => (
        <div>
          {record.effective_date && (
            <div style={{ fontSize: 12 }}>
              <CalendarOutlined style={{ marginRight: 4 }} />
              {dayjs(record.effective_date).format('MM-DD')}
            </div>
          )}
          {record.effective_time_start && (
            <div style={{ fontSize: 12 }}>
              <ClockCircleOutlined style={{ marginRight: 4 }} />
              {record.effective_time_start}-{record.effective_time_end}
            </div>
          )}
          {record.min_days_since_game_start > 0 && (
            <Tag size="small" color="blue">第{record.min_days_since_game_start}天生效</Tag>
          )}
        </div>
      )
    },
    {
      title: '限制条件',
      key: 'limits',
      width: 120,
      render: (_, record) => (
        <div>
          {record.daily_limit > 0 && <Tag size="small">日限{record.daily_limit}</Tag>}
          {record.user_limit > 0 && <Tag size="small">人限{record.user_limit}</Tag>}
          {record.is_guaranteed && <Tag size="small" color="gold">保底</Tag>}
          {record.weight_factor !== 1 && (
            <Tag size="small" color="purple">权重{record.weight_factor}</Tag>
          )}
        </div>
      )
    },
    {
      title: '状态',
      dataIndex: 'is_default',
      key: 'status',
      width: 80,
      render: (isDefault, record) => (
        <div>
          {isDefault ? (
            <Tag color="default">默认奖品</Tag>
          ) : (
            <Tag color="success">普通奖品</Tag>
          )}
          {record.remaining <= 0 && <Tag color="error">已售罄</Tag>}
        </div>
      )
    },
    {
      title: '操作',
      key: 'actions',
      width: 120,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="预览">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => handlePreviewPrize(record)}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEditPrize(record)}
            />
          </Tooltip>
          <Tooltip title="删除">
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
              onClick={() => handleDeletePrize(record)}
            />
          </Tooltip>
        </Space>
      )
    }
  ];

  // 渲染奖品表单
  const renderPrizeForm = () => (
    <Form
      form={form}
      layout="vertical"
      onFinish={handleSavePrize}
      initialValues={{
        prize_type: 'physical',
        probability: 0.1,
        quantity: 1,
        weight_factor: 1.0,
        daily_limit: 0,
        hourly_limit: 0,
        user_limit: 0,
        min_days_since_game_start: 0,
        max_days_since_game_start: 0,
        is_guaranteed: false,
        is_default: false,
        animation_style: 'default'
      }}
    >
      <Row gutter={16}>
        <Col span={12}>
          <Form.Item
            label="奖品名称"
            name="name"
            rules={[{ required: true, message: '请输入奖品名称' }]}
          >
            <Input placeholder="请输入奖品名称" />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            label="奖品类型"
            name="prize_type"
            rules={[{ required: true }]}
          >
            <Select>
              {Object.entries(PRIZE_TYPES).map(([key, config]) => (
                <Option key={key} value={key}>
                  {config.icon} {config.label}
                </Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
      </Row>

      <Form.Item label="奖品描述" name="description">
        <Input.TextArea rows={3} placeholder="请输入奖品描述" />
      </Form.Item>

      <Row gutter={16}>
        <Col span={8}>
          <Form.Item
            label="奖品价值"
            name="value"
            rules={[{ required: true, message: '请输入奖品价值' }]}
          >
            <InputNumber
              style={{ width: '100%' }}
              min={0}
              precision={2}
              placeholder="奖品价值"
              addonAfter="元"
            />
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item
            label="奖品数量"
            name="quantity"
            rules={[{ required: true, message: '请输入奖品数量' }]}
          >
            <InputNumber
              style={{ width: '100%' }}
              min={1}
              placeholder="奖品数量"
            />
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item
            label="中奖概率"
            name="probability"
            rules={[{ required: true, message: '请输入中奖概率' }]}
            tooltip="填写0-1之间的小数，如0.1表示10%"
          >
            <InputNumber
              style={{ width: '100%' }}
              min={0}
              max={1}
              step={0.01}
              precision={3}
              placeholder="中奖概率"
            />
          </Form.Item>
        </Col>
      </Row>

      <Divider orientation="left">时间控制设置</Divider>
      
      <Row gutter={16}>
        <Col span={12}>
          <Form.Item label="生效日期" name="effective_date">
            <DatePicker
              style={{ width: '100%' }}
              showTime
              placeholder="选择生效日期"
            />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label="失效日期" name="expiry_date">
            <DatePicker
              style={{ width: '100%' }}
              showTime
              placeholder="选择失效日期"
            />
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={12}>
          <Form.Item label="每日生效时间" name="effective_time_start">
            <TimePicker
              style={{ width: '100%' }}
              placeholder="开始时间"
            />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label="每日失效时间" name="effective_time_end">
            <TimePicker
              style={{ width: '100%' }}
              placeholder="结束时间"
            />
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={12}>
          <Form.Item
            label="游戏开始后第几天生效"
            name="min_days_since_game_start"
            tooltip="0表示游戏开始即可中奖"
          >
            <InputNumber
              style={{ width: '100%' }}
              min={0}
              placeholder="天数"
            />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            label="游戏开始后第几天停止"
            name="max_days_since_game_start"
            tooltip="0表示不限制"
          >
            <InputNumber
              style={{ width: '100%' }}
              min={0}
              placeholder="天数"
            />
          </Form.Item>
        </Col>
      </Row>

      <Divider orientation="left">限量设置</Divider>

      <Row gutter={16}>
        <Col span={8}>
          <Form.Item
            label="每日限量"
            name="daily_limit"
            tooltip="0表示不限制"
          >
            <InputNumber
              style={{ width: '100%' }}
              min={0}
              placeholder="每日限量"
            />
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item
            label="每小时限量"
            name="hourly_limit"
            tooltip="0表示不限制"
          >
            <InputNumber
              style={{ width: '100%' }}
              min={0}
              placeholder="每小时限量"
            />
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item
            label="每用户限量"
            name="user_limit"
            tooltip="0表示不限制"
          >
            <InputNumber
              style={{ width: '100%' }}
              min={0}
              placeholder="每用户限量"
            />
          </Form.Item>
        </Col>
      </Row>

      <Divider orientation="left">高级设置</Divider>

      <Row gutter={16}>
        <Col span={12}>
          <Form.Item
            label="权重因子"
            name="weight_factor"
            tooltip="影响实际中奖概率，1.0为正常，大于1.0增加概率"
          >
            <InputNumber
              style={{ width: '100%' }}
              min={0.1}
              max={10}
              step={0.1}
              precision={1}
            />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item label="中奖动画" name="animation_style">
            <Select>
              {ANIMATION_STYLES.map(style => (
                <Option key={style.value} value={style.value}>
                  {style.label}
                </Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={12}>
          <Form.Item name="is_guaranteed" valuePropName="checked">
            <Checkbox>设为保底奖品</Checkbox>
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item name="is_default" valuePropName="checked">
            <Checkbox>设为默认奖品（未中奖时显示）</Checkbox>
          </Form.Item>
        </Col>
      </Row>

      <Form.Item noStyle shouldUpdate={(prev, curr) => prev.is_guaranteed !== curr.is_guaranteed}>
        {({ getFieldValue }) =>
          getFieldValue('is_guaranteed') ? (
            <Collapse style={{ marginBottom: 16 }}>
              <Panel header="保底触发条件" key="guaranteed">
                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      label="连续未中奖次数"
                      name="consecutive_losses"
                      tooltip="连续未中奖多少次后触发保底"
                    >
                      <InputNumber
                        style={{ width: '100%' }}
                        min={1}
                        placeholder="次数"
                      />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      label="总抽奖次数"
                      name="total_draws"
                      tooltip="用户总抽奖多少次后触发保底"
                    >
                      <InputNumber
                        style={{ width: '100%' }}
                        min={1}
                        placeholder="次数"
                      />
                    </Form.Item>
                  </Col>
                </Row>
              </Panel>
            </Collapse>
          ) : null
        }
      </Form.Item>

      <Collapse style={{ marginBottom: 16 }}>
        <Panel header="特殊中奖条件" key="special">
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                label="最低用户等级"
                name="min_user_level"
                tooltip="只有达到指定等级的用户才能中奖"
              >
                <InputNumber
                  style={{ width: '100%' }}
                  min={1}
                  placeholder="等级"
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label="最低消费金额"
                name="min_consumption_amount"
                tooltip="只有消费达到指定金额的用户才能中奖"
              >
                <InputNumber
                  style={{ width: '100%' }}
                  min={0}
                  precision={2}
                  placeholder="金额"
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label="会员类型要求" name="required_member_type">
                <Select placeholder="选择会员类型">
                  <Option value="normal">普通会员</Option>
                  <Option value="vip">VIP会员</Option>
                  <Option value="svip">超级VIP</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
        </Panel>
      </Collapse>

      <Form.Item label="奖品图片" name="image_url">
        <Upload
          name="image"
          listType="picture-card"
          showUploadList={false}
          action="/api/v1/upload"
          beforeUpload={(file) => {
            const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
            if (!isJpgOrPng) {
              message.error('只能上传 JPG/PNG 文件!');
            }
            const isLt2M = file.size / 1024 / 1024 < 2;
            if (!isLt2M) {
              message.error('图片必须小于 2MB!');
            }
            return isJpgOrPng && isLt2M;
          }}
          onChange={(info) => {
            if (info.file.status === 'done') {
              form.setFieldsValue({
                image_url: info.file.response.url
              });
            }
          }}
        >
          {form.getFieldValue('image_url') ? (
            <img
              src={form.getFieldValue('image_url')}
              alt="奖品"
              style={{ width: '100%', height: '100%', objectFit: 'cover' }}
            />
          ) : (
            <div>
              <UploadOutlined />
              <div style={{ marginTop: 8 }}>上传图片</div>
            </div>
          )}
        </Upload>
      </Form.Item>

      <Form.Item label="中奖音效URL" name="sound_effect_url">
        <Input placeholder="请输入音效文件URL（可选）" />
      </Form.Item>

      <Form.Item>
        <Space>
          <Button type="primary" htmlType="submit" loading={loading} icon={<SaveOutlined />}>
            {editingPrize ? '更新奖品' : '创建奖品'}
          </Button>
          <Button onClick={() => {
            setShowPrizeForm(false);
            setEditingPrize(null);
            form.resetFields();
          }}>
            取消
          </Button>
        </Space>
      </Form.Item>
    </Form>
  );

  // 渲染奖品预览
  const renderPrizePreview = () => {
    if (!previewPrize) return null;

    return (
      <Modal
        title="奖品预览"
        open={previewVisible}
        onCancel={() => setPreviewVisible(false)}
        footer={null}
        width={600}
      >
        <Card>
          <Row gutter={16}>
            <Col span={8}>
              <img
                src={previewPrize.image_url || '/placeholder-prize.png'}
                alt={previewPrize.name}
                style={{ width: '100%', borderRadius: 8 }}
                onError={(e) => {
                  // 首先尝试 placeholder 图片
                  if (e.target.src !== '/placeholder-prize.png') {
                    e.target.src = '/placeholder-prize.png';
                  } else {
                    // 如果 placeholder 也失败，使用 base64 编码的默认图片
                    e.target.src = DEFAULT_PRIZE_IMAGE;
                  }
                }}
              />
            </Col>
            <Col span={16}>
              <Title level={4}>{previewPrize.name}</Title>
              <Paragraph>{previewPrize.description}</Paragraph>
              
              <Space direction="vertical" style={{ width: '100%' }}>
                <div>
                  <Text strong>奖品类型：</Text>
                  <Tag color={PRIZE_TYPES[previewPrize.prize_type]?.color}>
                    {PRIZE_TYPES[previewPrize.prize_type]?.label}
                  </Tag>
                </div>
                
                <div>
                  <Text strong>奖品价值：</Text>
                  <Text type="success">¥{previewPrize.value}</Text>
                </div>
                
                <div>
                  <Text strong>中奖概率：</Text>
                  <Text>{(previewPrize.probability * 100).toFixed(1)}%</Text>
                </div>
                
                <div>
                  <Text strong>库存状态：</Text>
                  <Progress
                    percent={((previewPrize.quantity - previewPrize.remaining) / previewPrize.quantity) * 100}
                    format={() => `${previewPrize.remaining}/${previewPrize.quantity}`}
                  />
                </div>
              </Space>
            </Col>
          </Row>

          {(previewPrize.effective_date || previewPrize.min_days_since_game_start > 0) && (
            <>
              <Divider />
              <Title level={5}>时间控制</Title>
              <Space direction="vertical" style={{ width: '100%' }}>
                {previewPrize.effective_date && (
                  <div>
                    <CalendarOutlined style={{ marginRight: 8 }} />
                    生效时间：{dayjs(previewPrize.effective_date).format('YYYY-MM-DD HH:mm')}
                  </div>
                )}
                {previewPrize.min_days_since_game_start > 0 && (
                  <div>
                    <ClockCircleOutlined style={{ marginRight: 8 }} />
                    游戏开始第{previewPrize.min_days_since_game_start}天后可中奖
                  </div>
                )}
              </Space>
            </>
          )}

          {(previewPrize.daily_limit > 0 || previewPrize.user_limit > 0) && (
            <>
              <Divider />
              <Title level={5}>限制条件</Title>
              <Space>
                {previewPrize.daily_limit > 0 && (
                  <Tag>每日限量{previewPrize.daily_limit}个</Tag>
                )}
                {previewPrize.user_limit > 0 && (
                  <Tag>每人限量{previewPrize.user_limit}个</Tag>
                )}
                {previewPrize.is_guaranteed && (
                  <Tag color="gold">保底奖品</Tag>
                )}
              </Space>
            </>
          )}
        </Card>
      </Modal>
    );
  };

  return (
    <Modal
      title="奖品管理"
      open={visible}
      onCancel={onClose}
      footer={null}
      width={1200}
      style={{ top: 20 }}
    >
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="奖品列表" key="list">
          <div style={{ marginBottom: 16 }}>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {
                setEditingPrize(null);
                form.resetFields();
                setShowPrizeForm(true);
                setActiveTab('form');
              }}
            >
              创建奖品
            </Button>
          </div>
          
          <Table
            columns={columns}
            dataSource={prizes}
            rowKey="id"
            loading={loading}
            scroll={{ x: 1000 }}
            pagination={{
              pageSize: 10,
              showSizeChanger: true
            }}
          />
        </TabPane>
        
        <TabPane tab={editingPrize ? "编辑奖品" : "创建奖品"} key="form">
          {renderPrizeForm()}
        </TabPane>
      </Tabs>
      
      {renderPrizePreview()}
    </Modal>
  );
};

export default EnhancedPrizeManager; 