# 多行业AI SaaS系统API设计

## 1. 概述

本文档详细描述了多行业AI SaaS系统的API设计，包括API架构、认证机制、请求/响应格式、错误处理以及各模块的具体API接口定义。系统API采用RESTful设计风格，支持多租户和多行业场景。

### 1.1 设计原则

1. **RESTful设计**：遵循REST架构风格，使用HTTP方法表达操作语义
2. **多租户支持**：所有API支持多租户隔离
3. **多行业适配**：API设计考虑不同行业的特殊需求
4. **版本控制**：支持API版本管理，确保向后兼容
5. **安全性**：实现严格的认证和授权机制
6. **可扩展性**：支持通过插件机制扩展API功能
7. **文档完备**：提供详细的API文档和示例

### 1.2 API分类

系统API主要分为以下几类：

1. **系统管理API**：面向系统管理员，管理整个SaaS平台
2. **服务商API**：面向服务商，管理其租户和配置
3. **租户管理API**：面向租户管理员，管理租户内的项目和用户
4. **项目API**：面向项目成员，管理项目内的业务数据
5. **行业特定API**：针对不同行业的特定功能
6. **插件API**：提供插件安装、配置和使用的接口
7. **集成API**：用于与第三方系统集成

## 2. API架构

### 2.1 基础URL结构

```
https://{domain}/api/v{version}/{resource}
```

- **domain**：可以是系统域名、服务商自定义域名或租户子域名
- **version**：API版本号，如v1、v2
- **resource**：API资源路径

### 2.2 多租户路由策略

系统支持以下几种租户识别和路由方式：

1. **子域名路由**：`https://{tenant-id}.{domain}/api/v1/{resource}`
2. **路径路由**：`https://{domain}/api/v1/tenants/{tenant-id}/{resource}`
3. **请求头路由**：通过`X-Tenant-ID`请求头识别租户

### 2.3 多行业适配策略

为支持多行业场景，API设计采用以下策略：

1. **行业通用API**：适用于所有行业的核心功能API
2. **行业扩展API**：针对特定行业的扩展API
3. **行业配置参数**：通过配置参数适配不同行业需求
4. **行业插件机制**：通过插件扩展特定行业功能

### 2.4 API版本控制

系统支持以下版本控制策略：

1. **URL路径版本**：`/api/v1/resources`
2. **请求头版本**：`Accept: application/vnd.retail-ai.v1+json`
3. **参数版本**：`/api/resources?version=1`

主要采用URL路径版本作为默认策略，确保API的向后兼容性。

## 3. 认证与授权

### 3.1 认证机制

系统支持多种认证机制：

1. **JWT认证**：基于JSON Web Token的认证
2. **OAuth 2.0**：支持授权码、客户端凭证等授权流程
3. **API密钥认证**：用于服务间通信和第三方集成

#### 3.1.1 JWT认证流程

1. 客户端提交用户名和密码到认证服务
2. 认证服务验证凭据并生成JWT令牌
3. 客户端在后续请求中通过Authorization头提供JWT令牌
4. 服务器验证JWT令牌并处理请求

```
Authorization: Bearer {jwt_token}
```

#### 3.1.2 API密钥认证

用于服务间通信和第三方集成，通过请求头提供API密钥：

```
X-API-Key: {api_key}
```

### 3.2 授权策略

系统采用基于角色的访问控制(RBAC)和基于属性的访问控制(ABAC)相结合的授权策略：

1. **角色权限**：基于用户角色的权限控制
2. **资源权限**：基于资源所有权的权限控制
3. **数据权限**：基于数据属性的权限控制
4. **操作权限**：基于操作类型的权限控制

### 3.3 租户隔离

所有API请求都会进行租户隔离检查，确保用户只能访问其所属租户的数据：

1. 验证用户身份和租户关系
2. 验证资源所属租户
3. 应用租户级权限策略
4. 记录跨租户访问尝试

## 4. 请求与响应

### 4.1 请求格式

#### 4.1.1 HTTP方法

- **GET**：获取资源
- **POST**：创建资源
- **PUT**：完全更新资源
- **PATCH**：部分更新资源
- **DELETE**：删除资源

#### 4.1.2 请求头

常用请求头：

```
Content-Type: application/json
Authorization: Bearer {jwt_token}
X-Tenant-ID: {tenant_id}
X-Project-ID: {project_id}
Accept-Language: zh-CN
```

#### 4.1.3 请求参数

- **路径参数**：`/api/v1/resources/{resource_id}`
- **查询参数**：`/api/v1/resources?limit=10&offset=0`
- **请求体**：JSON格式的数据

### 4.2 响应格式

所有API响应采用统一的JSON格式：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    // 响应数据
  },
  "meta": {
    "total": 100,
    "page": 1,
    "limit": 10
  }
}
```

#### 4.2.1 状态码

系统使用标准HTTP状态码：

- **2xx**：成功
  - 200 OK：请求成功
  - 201 Created：资源创建成功
  - 204 No Content：请求成功但无返回内容
- **4xx**：客户端错误
  - 400 Bad Request：请求参数错误
  - 401 Unauthorized：未认证
  - 403 Forbidden：无权限
  - 404 Not Found：资源不存在
  - 409 Conflict：资源冲突
- **5xx**：服务器错误
  - 500 Internal Server Error：服务器内部错误
  - 503 Service Unavailable：服务不可用

#### 4.2.2 错误响应

错误响应格式：

```json
{
  "code": 400,
  "message": "请求参数错误",
  "errors": [
    {
      "field": "name",
      "message": "名称不能为空"
    }
  ]
}
```

### 4.3 分页与排序

#### 4.3.1 分页参数

```
/api/v1/resources?limit=10&offset=0
/api/v1/resources?page=1&per_page=10
```

#### 4.3.2 排序参数

```
/api/v1/resources?sort=name&order=asc
/api/v1/resources?sort=-created_at,name
```

### 4.4 过滤与搜索

#### 4.4.1 过滤参数

```
/api/v1/resources?status=active
/api/v1/resources?created_at_gte=2023-01-01&created_at_lte=2023-12-31
```

#### 4.4.2 搜索参数

```
/api/v1/resources?q=keyword
/api/v1/resources?search=keyword&search_fields=name,description
```

### 4.5 字段选择

支持选择返回特定字段：

```
/api/v1/resources?fields=id,name,status
```

### 4.6 国际化

支持通过Accept-Language请求头指定响应语言：

```
Accept-Language: zh-CN
Accept-Language: en-US
```

## 5. 项目API

项目API用于项目成员管理项目内的业务数据。不同行业的项目有不同的业务API。

### 5.1 通用项目API

#### 5.1.1 获取项目信息

```
GET /api/v1/project
```

响应示例：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": "550e8400-e29b-41d4-a716-446655440000",
    "name": "广州总店",
    "description": "广州海鲜酒楼总店",
    "logo_url": "https://example.com/logo.png",
    "status": "active",
    "industry_type": "restaurant",
    "settings": {
      "default_language": "zh-CN",
      "timezone": "Asia/Shanghai",
      "currency": "CNY"
    },
    "created_at": "2023-06-01T00:00:00Z"
  }
}
```

#### 5.1.2 获取项目成员

```
GET /api/v1/project/members
```

响应示例：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "id": "550e8400-e29b-41d4-a716-446655440003",
      "username": "admin",
      "full_name": "王海鲜",
      "email": "<EMAIL>",
      "avatar_url": "https://example.com/avatar.png",
      "role": {
        "id": "550e8400-e29b-41d4-a716-446655440006",
        "name": "管理员"
      },
      "status": "active",
      "joined_at": "2023-06-01T00:00:00Z"
    },
    {
      "id": "550e8400-e29b-41d4-a716-446655440004",
      "username": "manager1",
      "full_name": "李经理",
      "email": "<EMAIL>",
      "avatar_url": "https://example.com/avatar2.png",
      "role": {
        "id": "550e8400-e29b-41d4-a716-446655440007",
        "name": "经理"
      },
      "status": "active",
      "joined_at": "2023-06-01T00:00:00Z"
    }
  ],
  "meta": {
    "total": 2,
    "page": 1,
    "per_page": 10
  }
}
```

### 5.2 系统设置API

#### 5.2.1 获取系统配置

```
GET /api/v1/project/settings/{config_type}
```

参数说明：

| 参数名 | 类型 | 必需 | 描述 |
| --- | --- | --- | --- |
| config_type | string | 是 | 配置类型：theme, notification, security, general, third_party_login |

响应示例：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "theme": {
      "mode": "light",
      "primaryColor": "#1890ff",
      "borderRadius": 4,
      "compactMode": false,
      "customFont": false,
      "fontFamily": "default"
    }
  }
}
```

#### 5.2.2 更新系统配置

```
PUT /api/v1/project/settings/{config_type}
```

请求体示例：

```json
{
  "theme": {
    "mode": "dark",
    "primaryColor": "#1890ff",
    "borderRadius": 4,
    "compactMode": true,
    "customFont": false,
    "fontFamily": "default"
  }
}
```

响应示例：

```json
{
  "code": 200,
  "message": "配置更新成功",
  "data": {
    "theme": {
      "mode": "dark",
      "primaryColor": "#1890ff",
      "borderRadius": 4,
      "compactMode": true,
      "customFont": false,
      "fontFamily": "default"
    }
  }
}
```

### 5.3 角色与权限API

#### 5.3.1 获取角色列表

```
GET /api/v1/project/settings/roles
```

响应示例：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "id": "550e8400-e29b-41d4-a716-446655440006",
      "name": "项目管理员",
      "code": "project_admin",
      "description": "项目管理员，拥有项目内的所有权限",
      "is_system_role": true,
      "permissions": {
        "view": true,
        "create": true,
        "update": true,
        "delete": true
      },
      "created_at": "2023-06-01T00:00:00Z"
    },
    {
      "id": "550e8400-e29b-41d4-a716-446655440007",
      "name": "门店店长",
      "code": "store_admin",
      "description": "门店店长，负责管理门店相关业务",
      "is_system_role": true,
      "permissions": {
        "view": true,
        "create": true,
        "update": true,
        "delete": false
      },
      "created_at": "2023-06-01T00:00:00Z"
    }
  ],
  "meta": {
    "total": 2,
    "page": 1,
    "per_page": 10
  }
}
```

#### 5.3.2 创建角色

```
POST /api/v1/project/settings/roles
```

请求体示例：

```json
{
  "name": "财务主管",
  "code": "finance_manager",
  "description": "财务主管，负责全面财务管理",
  "permissions": {
    "view": true,
    "create": true,
    "update": true,
    "delete": false
  },
  "permission_ids": ["finance_view", "finance_edit", "finance_approve", "finance_staff_manage"]
}
```

响应示例：

```json
{
  "code": 201,
  "message": "角色创建成功",
  "data": {
    "id": "550e8400-e29b-41d4-a716-446655440008",
    "name": "财务主管",
    "code": "finance_manager",
    "description": "财务主管，负责全面财务管理",
    "is_system_role": false,
    "permissions": {
      "view": true,
      "create": true,
      "update": true,
      "delete": false
    },
    "created_at": "2023-06-03T00:00:00Z"
  }
}
```

#### 5.3.3 获取权限列表

```
GET /api/v1/project/settings/permissions
```

响应示例：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "id": "550e8400-e29b-41d4-a716-446655440010",
      "name": "查看用户",
      "code": "user:view",
      "description": "查看用户列表和详情",
      "module": "user",
      "action": "view",
      "resource": "user",
      "is_system_permission": true
    },
    {
      "id": "550e8400-e29b-41d4-a716-446655440011",
      "name": "创建用户",
      "code": "user:create",
      "description": "创建新用户",
      "module": "user",
      "action": "create",
      "resource": "user",
      "is_system_permission": true
    }
  ],
  "meta": {
    "total": 2,
    "page": 1,
    "per_page": 10
  }
}
```

### 5.4 基础设置API

#### 5.4.1 门店管理API

##### ******* 获取门店列表

```
GET /api/v1/project/basic/stores
```

响应示例：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "id": "550e8400-e29b-41d4-a716-446655440020",
      "name": "北京西单店",
      "code": "BJ001",
      "address": "北京市西城区西单北大街120号",
      "phone": "010-12345678",
      "manager": "张三",
      "latitude": 39.9123,
      "longitude": 116.3789,
      "business_hours": "09:00-22:00",
      "status": "active",
      "category": {
        "id": "550e8400-e29b-41d4-a716-446655440030",
        "name": "旗舰店"
      },
      "region": {
        "id": "550e8400-e29b-41d4-a716-446655440040",
        "name": "华北区"
      },
      "created_at": "2023-06-01T00:00:00Z"
    }
  ],
  "meta": {
    "total": 1,
    "page": 1,
    "per_page": 10
  }
}
```

##### ******* 创建门店

```
POST /api/v1/project/basic/stores
```

请求体示例：

```json
{
  "name": "上海南京路店",
  "code": "SH001",
  "address": "上海市黄浦区南京东路123号",
  "phone": "021-12345678",
  "manager": "李四",
  "latitude": 31.2304,
  "longitude": 121.4737,
  "business_hours": "09:00-22:00",
  "status": "active",
  "category_id": "550e8400-e29b-41d4-a716-446655440030",
  "region_id": "550e8400-e29b-41d4-a716-446655440041",
  "settings": {
    "has_parking": true,
    "floor_area": 800
  }
}
```

响应示例：

```json
{
  "code": 201,
  "message": "门店创建成功",
  "data": {
    "id": "550e8400-e29b-41d4-a716-446655440021",
    "name": "上海南京路店",
    "code": "SH001",
    "address": "上海市黄浦区南京东路123号",
    "phone": "021-12345678",
    "manager": "李四",
    "latitude": 31.2304,
    "longitude": 121.4737,
    "business_hours": "09:00-22:00",
    "status": "active",
    "category": {
      "id": "550e8400-e29b-41d4-a716-446655440030",
      "name": "旗舰店"
    },
    "region": {
      "id": "550e8400-e29b-41d4-a716-446655440041",
      "name": "华东区"
    },
    "settings": {
      "has_parking": true,
      "floor_area": 800
    },
    "created_at": "2023-06-03T00:00:00Z"
  }
}
```

##### ******* 获取门店分类列表

```
GET /api/v1/project/basic/store-categories
```

响应示例：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "id": "550e8400-e29b-41d4-a716-446655440030",
      "name": "旗舰店",
      "code": "flagship",
      "description": "面积大于500平米的大型门店",
      "created_at": "2023-06-01T00:00:00Z"
    },
    {
      "id": "550e8400-e29b-41d4-a716-446655440031",
      "name": "标准店",
      "code": "standard",
      "description": "面积在200-500平米之间的标准门店",
      "created_at": "2023-06-01T00:00:00Z"
    }
  ],
  "meta": {
    "total": 2,
    "page": 1,
    "per_page": 10
  }
}
```

##### ******* 获取门店区域列表

```
GET /api/v1/project/basic/store-regions
```

响应示例：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "id": "550e8400-e29b-41d4-a716-446655440040",
      "name": "华北区",
      "code": "north",
      "description": "包括北京、天津、河北等地区",
      "created_at": "2023-06-01T00:00:00Z"
    },
    {
      "id": "550e8400-e29b-41d4-a716-446655440041",
      "name": "华东区",
      "code": "east",
      "description": "包括上海、江苏、浙江等地区",
      "created_at": "2023-06-01T00:00:00Z"
    }
  ],
  "meta": {
    "total": 2,
    "page": 1,
    "per_page": 10
  }
}
```

#### 5.4.2 仓库管理API

##### ******* 获取仓库列表

```
GET /api/v1/project/basic/warehouses
```

响应示例：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": [
    {
      "id": "550e8400-e29b-41d4-a716-446655440050",
      "name": "北京总仓",
      "code": "WH001",
      "type": "central",
      "address": "北京市大兴区经济开发区科创十三街18号院",
      "phone": "010-87654321",
      "manager": "李四",
      "capacity": 10000,
      "capacity_unit": "sqm",
      "business_hours": "09:00-18:00",
      "status": "active",
      "settings": {
        "has_cold_storage": true,
        "floors": 3
      },
      "created_at": "2023-06-01T00:00:00Z"
    }
  ],
  "meta": {
    "total": 1,
    "page": 1,
    "per_page": 10
  }
}
```

##### ******* 创建仓库

```
POST /api/v1/project/basic/warehouses
```

请求体示例：

```json
{
  "name": "上海区域仓",
  "code": "WH002",
  "type": "regional",
  "address": "上海市松江区佘山工业区88号",
  "phone": "021-87654321",
  "manager": "王五",
  "capacity": 5000,
  "capacity_unit": "sqm",
  "business_hours": "09:00-18:00",
  "status": "active",
  "settings": {
    "has_cold_storage": true,
    "floors": 2
  }
}
```

响应示例：

```json
{
  "code": 201,
  "message": "仓库创建成功",
  "data": {
    "id": "550e8400-e29b-41d4-a716-446655440051",
    "name": "上海区域仓",
    "code": "WH002",
    "type": "regional",
    "address": "上海市松江区佘山工业区88号",
    "phone": "021-87654321",
    "manager": "王五",
    "capacity": 5000,
    "capacity_unit": "sqm",
    "business_hours": "09:00-18:00",
    "status": "active",
    "settings": {
      "has_cold_storage": true,
      "floors": 2
    },
    "created_at": "2023-06-03T00:00:00Z"
  }
}
```

##### ******* 获取仓库设置

```
GET /api/v1/project/basic/warehouse-settings
```

响应示例：

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "id": "550e8400-e29b-41d4-a716-446655440060",
    "warehouse_mode": "multi",
    "default_business_hours_start": "09:00",
    "default_business_hours_end": "18:00",
    "enable_front_warehouse": true,
    "enable_cross_warehouse_transfer": true,
    "inventory_warning_threshold": 10,
    "auto_replenishment": true,
    "default_capacity_unit": "sqm",
    "created_at": "2023-06-01T00:00:00Z",
    "updated_at": "2023-06-01T00:00:00Z"
  }
}
```

##### ******* 更新仓库设置

```
PUT /api/v1/project/basic/warehouse-settings
```

请求体示例：

```json
{
  "warehouse_mode": "central",
  "default_business_hours_start": "08:00",
  "default_business_hours_end": "20:00",
  "enable_front_warehouse": false,
  "inventory_warning_threshold": 15
}
```

响应示例：

```json
{
  "code": 200,
  "message": "仓库设置更新成功",
  "data": {
    "id": "550e8400-e29b-41d4-a716-446655440060",
    "warehouse_mode": "central",
    "default_business_hours_start": "08:00",
    "default_business_hours_end": "20:00",
    "enable_front_warehouse": false,
    "enable_cross_warehouse_transfer": true,
    "inventory_warning_threshold": 15,
    "auto_replenishment": true,
    "default_capacity_unit": "sqm",
    "updated_at": "2023-06-03T00:00:00Z"
  }
}
```

## 6. 项目初始化API

### 6.1 初始化项目数据

```
POST /api/v1/project/init
```

响应示例：

```json
{
  "code": 200,
  "message": "项目初始化成功",
  "data": {
    "roles_count": 20,
    "stores_count": 7,
    "warehouses_count": 7,
    "configs_count": 25
  }
}
```

## 7. 结论

本文档详细描述了多行业AI SaaS系统的API设计，包括系统管理API、服务商API、租户API、项目API以及行业特定API。该设计支持多租户架构，并实现了服务商模式，同时考虑了不同行业的特定需求。

API设计遵循了RESTful风格，并提供了统一的请求/响应格式、错误处理、分页与排序、过滤与搜索等机制。同时，设计也考虑了API版本控制、认证与授权、租户隔离等安全因素。

通过行业特定API的设计，系统能够支持不同行业的业务需求，如餐饮业的菜品管理、订单处理，零售业的商品管理、库存管理等。同时，插件系统的API设计也使得系统能够通过插件扩展支持更多行业特定功能。

随着系统的发展，API设计将不断扩展和优化，以支持更多行业和功能需求。
