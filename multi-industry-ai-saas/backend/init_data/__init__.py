"""
初始化数据包
"""
from uuid import UUID
from sqlalchemy.orm import Session

from .init_roles import init_roles, init_permissions
from .init_stores import init_stores, init_store_categories, init_store_regions
from .init_warehouses import init_warehouses, init_warehouse_settings
from .init_system_config import init_system_configs
from .init_ai import init_ai_data

def init_project_data(db: Session, project_id: UUID):
    """初始化项目数据"""
    # 初始化角色和权限
    roles = init_roles(db, project_id)
    
    # 初始化门店数据
    stores = init_stores(db, project_id)
    
    # 初始化仓库数据
    warehouses = init_warehouses(db, project_id)
    
    # 初始化系统配置
    configs = init_system_configs(db, project_id)
    
    return {
        "roles": roles,
        "stores": stores,
        "warehouses": warehouses,
        "configs": configs
    }

def init_system_data(db: Session):
    """初始化系统级数据（不依赖项目）"""
    print("开始初始化系统级数据...")
    
    # 初始化AI提供商和模型数据（放在最后，因为依赖其他表结构）
    ai_data = init_ai_data(db)
    
    print("系统级数据初始化完成")
    return {
        "ai_data": ai_data
    }
