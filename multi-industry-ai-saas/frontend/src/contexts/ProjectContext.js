import React, { createContext, useContext, useState, useEffect } from 'react';

// 创建项目上下文
const ProjectContext = createContext();

/**
 * 项目上下文提供者
 * 管理当前项目相关的状态和操作
 */
export const ProjectProvider = ({ children }) => {
  const [currentProject, setCurrentProject] = useState(null);
  const [loading, setLoading] = useState(false);

  // 初始化项目信息
  useEffect(() => {
    const initProject = () => {
      const projectId = localStorage.getItem('project_id');
      const projectName = localStorage.getItem('project_name');
      
      if (projectId) {
        // 从localStorage构建项目对象
        const project = {
          id: projectId,
          name: projectName || '项目',
          industry_type: 'retail', // 默认行业类型
          description: '当前项目',
          created_at: new Date().toISOString()
        };
        
        setCurrentProject(project);
      }
    };

    initProject();

    // 监听localStorage变化
    const handleStorageChange = (e) => {
      if (e.key === 'project_id' || e.key === 'project_name') {
        initProject();
      }
    };

    window.addEventListener('storage', handleStorageChange);
    
    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, []);

  // 切换项目
  const switchProject = (project) => {
    localStorage.setItem('project_id', project.id);
    localStorage.setItem('project_name', project.name);
    setCurrentProject(project);
  };

  // 上下文值
  const contextValue = {
    currentProject,
    loading,
    switchProject,
    setCurrentProject
  };

  return (
    <ProjectContext.Provider value={contextValue}>
      {children}
    </ProjectContext.Provider>
  );
};

/**
 * 使用项目上下文的Hook
 */
export const useProject = () => {
  const context = useContext(ProjectContext);
  if (!context) {
    throw new Error('useProject must be used within a ProjectProvider');
  }
  return context;
};

export default ProjectContext; 