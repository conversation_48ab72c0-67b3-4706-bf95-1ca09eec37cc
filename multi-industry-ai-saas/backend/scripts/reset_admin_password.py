#!/usr/bin/env python
# -*- coding: utf-8 -*-

import asyncio
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from db.database import get_db
from models.user import User

async def reset_admin_password():
    async for db in get_db():
        result = await db.execute(select(User).where(User.username == 'admin'))
        admin = result.scalars().first()
        if not admin:
            print('未找到 admin 用户')
            return
        admin.password_hash = User.get_password_hash('123456')
        await db.commit()
        print('admin 密码已重置为 123456')

if __name__ == '__main__':
    asyncio.run(reset_admin_password()) 