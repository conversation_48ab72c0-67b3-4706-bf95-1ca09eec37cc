import React, { useState, useEffect } from 'react';
import { Card, Row, Col, DatePicker, Select, Button, Space, Divider, Tooltip } from 'antd';
import { SyncOutlined, FilterOutlined, ClearOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';

const { RangePicker } = DatePicker;
const { Option } = Select;

/**
 * 仪表盘过滤器组件
 * 提供日期范围、门店、仓库、供应商等筛选条件
 * @param {Object} props - 组件属性
 * @param {Function} props.onFilterChange - 筛选条件变化回调
 * @param {Array} props.stores - 门店列表
 * @param {Array} props.warehouses - 仓库列表
 * @param {Array} props.suppliers - 供应商列表
 * @param {Array} props.categories - 类别列表
 * @param {boolean} props.showStoreFilter - 是否显示门店筛选
 * @param {boolean} props.showWarehouseFilter - 是否显示仓库筛选
 * @param {boolean} props.showSupplierFilter - 是否显示供应商筛选
 * @param {boolean} props.showCategoryFilter - 是否显示类别筛选
 * @param {number} props.defaultDateRange - 默认日期范围（天数）
 * @param {Function} props.onRefresh - 刷新回调
 * @param {boolean} props.loading - 加载状态
 */
const DashboardFilter = ({
  onFilterChange,
  stores = [],
  warehouses = [],
  suppliers = [],
  categories = [],
  showStoreFilter = false,
  showWarehouseFilter = false,
  showSupplierFilter = false,
  showCategoryFilter = false,
  defaultDateRange = 7,
  onRefresh,
  loading = false
}) => {
  // 状态定义
  const [dateRange, setDateRange] = useState([
    dayjs().subtract(defaultDateRange - 1, 'days'),
    dayjs()
  ]);
  const [selectedStore, setSelectedStore] = useState(null);
  const [selectedWarehouse, setSelectedWarehouse] = useState(null);
  const [selectedSupplier, setSelectedSupplier] = useState(null);
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [expanded, setExpanded] = useState(false);

  // 初始化时触发筛选条件变化
  useEffect(() => {
    handleFilterChange();
  }, []);

  // 处理日期范围变化
  const handleDateRangeChange = (dates) => {
    if (dates && dates.length === 2) {
      setDateRange(dates);
    }
  };

  // 处理门店选择变化
  const handleStoreChange = (value) => {
    setSelectedStore(value);
  };

  // 处理仓库选择变化
  const handleWarehouseChange = (value) => {
    setSelectedWarehouse(value);
  };

  // 处理供应商选择变化
  const handleSupplierChange = (value) => {
    setSelectedSupplier(value);
  };

  // 处理类别选择变化
  const handleCategoryChange = (value) => {
    setSelectedCategory(value);
  };

  // 处理筛选条件变化
  const handleFilterChange = () => {
    if (onFilterChange) {
      onFilterChange({
        dateRange: dateRange ? [
          dateRange[0].format('YYYY-MM-DD'),
          dateRange[1].format('YYYY-MM-DD')
        ] : null,
        storeId: selectedStore,
        warehouseId: selectedWarehouse,
        supplierId: selectedSupplier,
        categoryId: selectedCategory
      });
    }
  };

  // 处理重置筛选条件
  const handleResetFilter = () => {
    setDateRange([
      dayjs().subtract(defaultDateRange - 1, 'days'),
      dayjs()
    ]);
    setSelectedStore(null);
    setSelectedWarehouse(null);
    setSelectedSupplier(null);
    setSelectedCategory(null);
    
    // 触发筛选条件变化
    setTimeout(() => {
      handleFilterChange();
    }, 0);
  };

  // 切换展开/收起状态
  const toggleExpanded = () => {
    setExpanded(!expanded);
  };

  return (
    <Card className="dashboard-filter" size="small">
      <Row gutter={[16, 16]} align="middle">
        <Col xs={24} sm={12} md={8} lg={6}>
          <div className="filter-item">
            <div className="filter-label">日期范围:</div>
            <RangePicker
              value={dateRange}
              onChange={handleDateRangeChange}
              allowClear={false}
              style={{ width: '100%' }}
            />
          </div>
        </Col>
        
        {showStoreFilter && (
          <Col xs={24} sm={12} md={8} lg={6}>
            <div className="filter-item">
              <div className="filter-label">门店:</div>
              <Select
                placeholder="选择门店"
                allowClear
                style={{ width: '100%' }}
                onChange={handleStoreChange}
                value={selectedStore}
              >
                {stores.map(store => (
                  <Option key={store.id} value={store.id}>{store.name}</Option>
                ))}
              </Select>
            </div>
          </Col>
        )}
        
        {expanded && showWarehouseFilter && (
          <Col xs={24} sm={12} md={8} lg={6}>
            <div className="filter-item">
              <div className="filter-label">仓库:</div>
              <Select
                placeholder="选择仓库"
                allowClear
                style={{ width: '100%' }}
                onChange={handleWarehouseChange}
                value={selectedWarehouse}
              >
                {warehouses.map(warehouse => (
                  <Option key={warehouse.id} value={warehouse.id}>{warehouse.name}</Option>
                ))}
              </Select>
            </div>
          </Col>
        )}
        
        {expanded && showSupplierFilter && (
          <Col xs={24} sm={12} md={8} lg={6}>
            <div className="filter-item">
              <div className="filter-label">供应商:</div>
              <Select
                placeholder="选择供应商"
                allowClear
                style={{ width: '100%' }}
                onChange={handleSupplierChange}
                value={selectedSupplier}
              >
                {suppliers.map(supplier => (
                  <Option key={supplier.id} value={supplier.id}>{supplier.name}</Option>
                ))}
              </Select>
            </div>
          </Col>
        )}
        
        {expanded && showCategoryFilter && (
          <Col xs={24} sm={12} md={8} lg={6}>
            <div className="filter-item">
              <div className="filter-label">类别:</div>
              <Select
                placeholder="选择类别"
                allowClear
                style={{ width: '100%' }}
                onChange={handleCategoryChange}
                value={selectedCategory}
              >
                {categories.map(category => (
                  <Option key={category.id} value={category.id}>{category.name}</Option>
                ))}
              </Select>
            </div>
          </Col>
        )}
        
        <Col xs={24} sm={12} md={8} lg={6}>
          <Space>
            <Button
              type="primary"
              icon={<FilterOutlined />}
              onClick={handleFilterChange}
            >
              应用筛选
            </Button>
            <Button
              icon={<ClearOutlined />}
              onClick={handleResetFilter}
            >
              重置
            </Button>
            <Button
              icon={<SyncOutlined />}
              onClick={onRefresh}
              loading={loading}
            >
              刷新
            </Button>
            {(showWarehouseFilter || showSupplierFilter || showCategoryFilter) && (
              <Button
                type="link"
                onClick={toggleExpanded}
              >
                {expanded ? '收起' : '更多筛选'}
              </Button>
            )}
          </Space>
        </Col>
      </Row>
    </Card>
  );
};

export default DashboardFilter;
