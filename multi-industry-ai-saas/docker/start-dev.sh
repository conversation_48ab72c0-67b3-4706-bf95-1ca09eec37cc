#!/bin/bash

# 设置颜色
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 获取脚本所在目录
DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"

echo -e "${GREEN}启动 Retail AI SaaS 开发环境...${NC}"

# 加载环境变量
echo -e "${YELLOW}加载环境变量...${NC}"
set -a
source "$DIR/.env.dev"
set +a

# 构建并启动容器
echo -e "${YELLOW}构建并启动容器...${NC}"
docker-compose -f "$DIR/docker-compose.dev.yml" up --build -d

echo -e "${GREEN}开发环境已启动!${NC}"
echo -e "${YELLOW}访问地址:${NC}"
echo -e "  前端: http://localhost:${FRONTEND_PORT:-3001}"
echo -e "  API: http://localhost:${BACKEND_PORT:-8001}/api/v1/docs"
echo -e "  Nginx: http://localhost:${NGINX_PORT:-8080}"

echo -e "${YELLOW}容器状态:${NC}"
docker-compose -f "$DIR/docker-compose.dev.yml" ps
