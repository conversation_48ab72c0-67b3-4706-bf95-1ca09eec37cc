"""
初始化门店数据
"""
from uuid import UUID
from sqlalchemy.orm import Session
from models.store import Store, StoreCategory, StoreRegion

def init_store_categories(db: Session, project_id: UUID):
    """初始化门店分类数据"""
    categories = []
    
    # 检查分类是否已存在，不存在则创建
    created_categories = []
    for cat_data in categories:
        category = db.query(StoreCategory).filter(
            StoreCategory.project_id == project_id,
            StoreCategory.code == cat_data["code"]
        ).first()
        
        if not category:
            category = StoreCategory(project_id=project_id, **cat_data)
            db.add(category)
            created_categories.append(category)
    
    db.commit()
    return db.query(StoreCategory).filter(StoreCategory.project_id == project_id).all()

def init_store_regions(db: Session, project_id: UUID):
    """初始化门店区域数据"""
    regions = []
    
    # 检查区域是否已存在，不存在则创建
    created_regions = []
    for region_data in regions:
        region = db.query(StoreRegion).filter(
            StoreRegion.project_id == project_id,
            StoreRegion.code == region_data["code"]
        ).first()
        
        if not region:
            region = StoreRegion(project_id=project_id, **region_data)
            db.add(region)
            created_regions.append(region)
    
    db.commit()
    return db.query(StoreRegion).filter(StoreRegion.project_id == project_id).all()

def init_stores(db: Session, project_id: UUID):
    """初始化门店数据"""
    # 确保分类和区域已初始化
    categories = {cat.code: cat for cat in init_store_categories(db, project_id)}
    regions = {region.code: region for region in init_store_regions(db, project_id)}
    
    stores = []
    
    # 检查门店是否已存在，不存在则创建
    created_stores = []
    for store_data in stores:
        category_code = store_data.pop("category_code")
        region_code = store_data.pop("region_code")
        
        store = db.query(Store).filter(
            Store.project_id == project_id,
            Store.code == store_data["code"]
        ).first()
        
        if not store:
            store = Store(
                project_id=project_id,
                category_id=categories[category_code].id if category_code in categories else None,
                region_id=regions[region_code].id if region_code in regions else None,
                **store_data
            )
            db.add(store)
            created_stores.append(store)
    
    db.commit()
    return created_stores
