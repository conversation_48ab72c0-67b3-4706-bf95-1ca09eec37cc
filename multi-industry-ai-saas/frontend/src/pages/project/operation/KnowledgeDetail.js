import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Card,
  Typography,
  Spin,
  Tag,
  Space,
  Divider,
  Button,
  message,
  Row,
  Col,
  Breadcrumb,
  Avatar
} from 'antd';
import {
  ArrowLeftOutlined,
  FileTextOutlined,
  TagOutlined,
  UserOutlined,
  CalendarOutlined,
  DownloadOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined
} from '@ant-design/icons';
import dayjs from 'dayjs';
import apiService from '../../../services/api';

const { Title, Text, Paragraph } = Typography;

/**
 * 知识库文档详情页面
 */
const KnowledgeDetail = () => {
  const { documentId } = useParams();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [document, setDocument] = useState(null);
  const [error, setError] = useState(null);

  // 获取文档详情
  useEffect(() => {
    fetchDocumentDetail();
  }, [documentId]);

  const fetchDocumentDetail = async () => {
    setLoading(true);
    try {
      const response = await apiService.project.knowledgeBase.getDetail(documentId);
      setDocument(response);
    } catch (error) {
      console.error('获取知识库文档详情失败:', error);
      setError('获取知识库文档详情失败，请重试');
      message.error('获取知识库文档详情失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  // 下载文档
  const handleDownload = () => {
    if (document?.file_url) {
      window.open(document.file_url, '_blank');
    } else {
      message.error('文档没有可下载的文件');
    }
  };

  // 编辑文档
  const handleEdit = () => {
    navigate(`/project/operation/knowledge-base/edit/${documentId}`);
  };

  // 删除文档
  const handleDelete = async () => {
    try {
      await apiService.project.knowledgeBase.delete(documentId);
      message.success('文档删除成功');
      navigate('/project/operation/store-operation');
    } catch (error) {
      console.error('删除文档失败:', error);
      message.error('删除文档失败，请重试');
    }
  };

  // 返回列表
  const handleBack = () => {
    navigate('/project/operation/store-operation');
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px 0' }}>
        <Spin size="large" />
      </div>
    );
  }

  if (error) {
    return (
      <div style={{ textAlign: 'center', padding: '50px 0' }}>
        <Text type="danger">{error}</Text>
        <div style={{ marginTop: 16 }}>
          <Button type="primary" onClick={handleBack}>
            返回列表
          </Button>
        </div>
      </div>
    );
  }

  if (!document) {
    return (
      <div style={{ textAlign: 'center', padding: '50px 0' }}>
        <Text>文档不存在或已被删除</Text>
        <div style={{ marginTop: 16 }}>
          <Button type="primary" onClick={handleBack}>
            返回列表
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="knowledge-detail-page">
      <Card>
        <Breadcrumb style={{ marginBottom: 16 }}>
          <Breadcrumb.Item>
            <a onClick={handleBack}>知识库管理</a>
          </Breadcrumb.Item>
          <Breadcrumb.Item>文档详情</Breadcrumb.Item>
        </Breadcrumb>

        <div style={{ marginBottom: 24 }}>
          <Button icon={<ArrowLeftOutlined />} onClick={handleBack}>
            返回列表
          </Button>
        </div>

        <div className="document-header">
          <Row gutter={16} align="middle">
            <Col xs={24} md={16}>
              <Title level={3}>{document.title}</Title>
            </Col>
            <Col xs={24} md={8} style={{ textAlign: 'right' }}>
              <Space>
                {document.file_url && (
                  <Button icon={<DownloadOutlined />} onClick={handleDownload}>
                    下载
                  </Button>
                )}
                <Button icon={<EditOutlined />} onClick={handleEdit}>
                  编辑
                </Button>
                <Button danger icon={<DeleteOutlined />} onClick={handleDelete}>
                  删除
                </Button>
              </Space>
            </Col>
          </Row>
        </div>

        <div className="document-meta" style={{ marginBottom: 24 }}>
          <Space wrap>
            {document.category_name && (
              <Tag color="blue">
                <FileTextOutlined /> {document.category_name}
              </Tag>
            )}
            {document.tags && document.tags.map(tag => (
              <Tag key={tag}>
                <TagOutlined /> {tag}
              </Tag>
            ))}
            <Tag>
              <EyeOutlined /> {document.is_public ? '公开' : '私有'}
            </Tag>
          </Space>
        </div>

        <div className="document-info" style={{ marginBottom: 24, color: '#888' }}>
          <Space wrap>
            {document.author && (
              <span>
                <UserOutlined /> 作者: {document.author}
              </span>
            )}
            <span>
              <CalendarOutlined /> 创建时间: {dayjs(document.created_at).format('YYYY-MM-DD HH:mm')}
            </span>
            {document.updated_at && (
              <span>
                <CalendarOutlined /> 更新时间: {dayjs(document.updated_at).format('YYYY-MM-DD HH:mm')}
              </span>
            )}
          </Space>
        </div>

        {document.description && (
          <div className="document-description" style={{ marginBottom: 24 }}>
            <Card type="inner" title="文档描述">
              <Paragraph>{document.description}</Paragraph>
            </Card>
          </div>
        )}

        <Divider />

        <div className="document-content">
          <Card type="inner" title="文档内容" bodyStyle={{ whiteSpace: 'pre-wrap' }}>
            {document.content || '暂无内容'}
          </Card>
        </div>

        {document.file_url && (
          <div className="document-file" style={{ marginTop: 24 }}>
            <Card type="inner" title="附件">
              <Space>
                <Avatar icon={<FileTextOutlined />} />
                <Text>{document.file_name || '文档附件'}</Text>
                <Button type="link" icon={<DownloadOutlined />} onClick={handleDownload}>
                  下载
                </Button>
              </Space>
            </Card>
          </div>
        )}
      </Card>
    </div>
  );
};

export default KnowledgeDetail;
