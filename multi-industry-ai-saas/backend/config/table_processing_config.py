#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
表格处理配置 - 分级处理策略
"""

from enum import Enum
from typing import Dict, Any, Optional
from dataclasses import dataclass

class ProcessingStrategy(Enum):
    """处理策略枚举"""
    SMALL = "small"      # 小数据量 (≤100行)
    MEDIUM = "medium"    # 中等数据量 (100-500行)
    LARGE = "large"      # 大数据量 (500-2000行)
    XLARGE = "xlarge"    # 超大数据量 (>2000行)

@dataclass
class ProcessingConfig:
    """处理配置"""
    name: str
    max_rows: int
    max_columns: int
    max_tokens: int
    sample_rows: Optional[int] = None
    success_rate: float = 0.95
    description: str = ""

class TableProcessingConfig:
    """表格处理配置类"""
    
    # 分级处理配置
    PROCESSING_CONFIGS = {
        ProcessingStrategy.SMALL: ProcessingConfig(
            name="小型数据处理",
            max_rows=100,
            max_columns=20,
            max_tokens=4096,
            sample_rows=None,  # 完整处理
            success_rate=0.95,
            description="适用于小型表格，完整处理所有数据"
        ),
        ProcessingStrategy.MEDIUM: ProcessingConfig(
            name="中型数据处理",
            max_rows=500,
            max_columns=50,
            max_tokens=8192,
            sample_rows=200,  # 采样处理
            success_rate=0.90,
            description="适用于中型表格，采样200行进行处理"
        ),
        ProcessingStrategy.LARGE: ProcessingConfig(
            name="大型数据处理",
            max_rows=2000,
            max_columns=100,
            max_tokens=16384,
            sample_rows=300,  # 采样处理
            success_rate=0.85,
            description="适用于大型表格，采样300行进行处理"
        ),
        ProcessingStrategy.XLARGE: ProcessingConfig(
            name="超大型数据处理",
            max_rows=10000,
            max_columns=200,
            max_tokens=32768,
            sample_rows=500,  # 采样处理
            success_rate=0.80,
            description="适用于超大型表格，采样500行进行处理"
        )
    }
    
    # AI模型配置
    AI_MODEL_CONFIGS = {
        ProcessingStrategy.SMALL: {
            "model": "gpt-4-vision-preview",
            "max_tokens": 4096,
            "temperature": 0.1,
            "timeout": 60
        },
        ProcessingStrategy.MEDIUM: {
            "model": "gpt-4-vision-preview", 
            "max_tokens": 6144,
            "temperature": 0.1,
            "timeout": 120
        },
        ProcessingStrategy.LARGE: {
            "model": "gpt-4-vision-preview",
            "max_tokens": 8192,  # DashScope qwen-vl最大限制
            "temperature": 0.1,
            "timeout": 180
        },
        ProcessingStrategy.XLARGE: {
            "model": "gpt-4-vision-preview",
            "max_tokens": 8192,  # DashScope qwen-vl最大限制
            "temperature": 0.1,
            "timeout": 240
        }
    }
    
    # 图像处理配置
    IMAGE_CONFIGS = {
        ProcessingStrategy.SMALL: {
            "max_size": (1024, 1024),
            "quality": 95,
            "format": "PNG"
        },
        ProcessingStrategy.MEDIUM: {
            "max_size": (1536, 1536),
            "quality": 90,
            "format": "JPEG"
        },
        ProcessingStrategy.LARGE: {
            "max_size": (2048, 2048),
            "quality": 85,
            "format": "JPEG"
        },
        ProcessingStrategy.XLARGE: {
            "max_size": (2048, 2048),
            "quality": 80,
            "format": "JPEG"
        }
    }
    
    @classmethod
    def get_strategy_for_data_size(cls, rows: int, columns: int) -> ProcessingStrategy:
        """根据数据量选择处理策略"""
        if rows <= 100:
            return ProcessingStrategy.SMALL
        elif rows <= 500:
            return ProcessingStrategy.MEDIUM
        elif rows <= 2000:
            return ProcessingStrategy.LARGE
        else:
            return ProcessingStrategy.XLARGE
    
    @classmethod
    def get_processing_config(cls, strategy: ProcessingStrategy) -> ProcessingConfig:
        """获取处理配置"""
        return cls.PROCESSING_CONFIGS.get(strategy, cls.PROCESSING_CONFIGS[ProcessingStrategy.SMALL])
    
    @classmethod
    def get_optimal_ai_config(cls, strategy: ProcessingStrategy) -> Dict[str, Any]:
        """获取最优AI配置"""
        return cls.AI_MODEL_CONFIGS.get(strategy, cls.AI_MODEL_CONFIGS[ProcessingStrategy.SMALL])
    
    @classmethod
    def get_image_config(cls, strategy: ProcessingStrategy) -> Dict[str, Any]:
        """获取图像处理配置"""
        return cls.IMAGE_CONFIGS.get(strategy, cls.IMAGE_CONFIGS[ProcessingStrategy.SMALL])
    
    @classmethod
    def should_use_sampling(cls, strategy: ProcessingStrategy) -> bool:
        """是否应该使用采样处理"""
        config = cls.get_processing_config(strategy)
        return config.sample_rows is not None
    
    @classmethod
    def get_sample_size(cls, strategy: ProcessingStrategy, total_rows: int) -> int:
        """获取采样大小"""
        config = cls.get_processing_config(strategy)
        if config.sample_rows is None:
            return total_rows
        return min(config.sample_rows, total_rows)
    
    @classmethod
    def estimate_processing_time(cls, strategy: ProcessingStrategy, rows: int) -> int:
        """估算处理时间（秒）"""
        base_times = {
            ProcessingStrategy.SMALL: 30,
            ProcessingStrategy.MEDIUM: 60,
            ProcessingStrategy.LARGE: 120,
            ProcessingStrategy.XLARGE: 180
        }
        return base_times.get(strategy, 60)
    
    @classmethod
    def get_file_size_limits(cls) -> Dict[str, int]:
        """获取文件大小限制"""
        return {
            "max_file_size": 50 * 1024 * 1024,  # 50MB
            "max_image_size": 20 * 1024 * 1024,  # 20MB
            "max_excel_size": 30 * 1024 * 1024,  # 30MB
            "max_csv_size": 10 * 1024 * 1024     # 10MB
        }
    
    @classmethod
    def get_supported_formats(cls) -> Dict[str, list]:
        """获取支持的文件格式"""
        return {
            "images": [".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp"],
            "excel": [".xlsx", ".xls"],
            "csv": [".csv"],
            "all": [".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp", ".xlsx", ".xls", ".csv"]
        }
    
    @classmethod
    def validate_file(cls, file_size: int, file_extension: str) -> tuple[bool, str]:
        """验证文件"""
        limits = cls.get_file_size_limits()
        formats = cls.get_supported_formats()
        
        # 检查文件格式
        if file_extension.lower() not in formats["all"]:
            return False, f"不支持的文件格式: {file_extension}"
        
        # 检查文件大小
        if file_size > limits["max_file_size"]:
            return False, f"文件大小超过限制: {file_size / 1024 / 1024:.1f}MB > {limits['max_file_size'] / 1024 / 1024}MB"
        
        # 特定格式的大小检查
        if file_extension.lower() in formats["images"] and file_size > limits["max_image_size"]:
            return False, f"图片文件大小超过限制: {file_size / 1024 / 1024:.1f}MB > {limits['max_image_size'] / 1024 / 1024}MB"
        
        if file_extension.lower() in formats["excel"] and file_size > limits["max_excel_size"]:
            return False, f"Excel文件大小超过限制: {file_size / 1024 / 1024:.1f}MB > {limits['max_excel_size'] / 1024 / 1024}MB"
        
        if file_extension.lower() in formats["csv"] and file_size > limits["max_csv_size"]:
            return False, f"CSV文件大小超过限制: {file_size / 1024 / 1024:.1f}MB > {limits['max_csv_size'] / 1024 / 1024}MB"
        
        return True, "文件验证通过"

# 全局配置实例
table_processing_config = TableProcessingConfig() 