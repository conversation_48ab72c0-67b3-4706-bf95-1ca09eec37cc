#!/usr/bin/env python
# -*- coding: utf-8 -*-

import logging
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status, Query, Path, File, UploadFile, Form
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from pydantic import parse_obj_as
from datetime import datetime, date, timedelta
import uuid

from db.database import get_db
from core.auth import get_current_user
from api.deps import get_current_project
from models.project import Project
from models.user import User
from models.loss import Loss
from schemas.loss import LossCreate, LossUpdate, LossStatusUpdate, LossResponse, LossListResponse
from services.loss import LossService
from services.file_upload import FileUploadService

# 配置日志
logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter()

@router.get("/statistics", response_model=Dict[str, Any])
async def get_loss_statistics(
    start_date: Optional[date] = Query(None, description="开始日期"),
    end_date: Optional[date] = Query(None, description="结束日期"),
    store_id: Optional[uuid.UUID] = Query(None, description="门店ID"),
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取报损统计数据
    """
    try:
        # 转换日期为datetime
        # 如果没有指定日期范围，使用过去一年的数据
        if not start_date:
            start_datetime = datetime.now() - timedelta(days=365)
        else:
            start_datetime = datetime.combine(start_date, datetime.min.time())

        if not end_date:
            end_datetime = datetime.now()
        else:
            end_datetime = datetime.combine(end_date, datetime.max.time())

        # 获取报损统计数据
        statistics = await LossService.get_loss_statistics(
            db=db,
            project_id=project.id,
            start_date=start_datetime,
            end_date=end_datetime,
            store_id=store_id
        )

        return statistics
    except Exception as e:
        logger.error(f"获取报损统计数据失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取报损统计数据失败: {str(e)}"
        )

@router.get("", response_model=LossListResponse)
async def get_losses(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    store_id: Optional[uuid.UUID] = None,
    product_id: Optional[uuid.UUID] = None,
    status: Optional[str] = None,
    search: Optional[str] = None,
    start_date: Optional[date] = None,
    end_date: Optional[date] = None,
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取报损记录列表
    """
    try:
        # 转换日期为datetime
        start_datetime = datetime.combine(start_date, datetime.min.time()) if start_date else None
        end_datetime = datetime.combine(end_date, datetime.max.time()) if end_date else None

        # 获取报损记录
        losses = await LossService.get_losses(
            db=db,
            project_id=project.id,
            skip=skip,
            limit=limit,
            store_id=store_id,
            product_id=product_id,
            status=status,
            search=search,
            start_date=start_datetime,
            end_date=end_datetime
        )

        # 获取报损记录总数
        total = await LossService.count_losses(
            db=db,
            project_id=project.id,
            store_id=store_id,
            product_id=product_id,
            status=status,
            search=search,
            start_date=start_datetime,
            end_date=end_datetime
        )

        # 构建响应数据
        loss_responses = []
        for loss in losses:
            # 获取报损记录详情
            loss_detail = await LossService.get_loss_with_details(db, loss.id)
            if loss_detail:
                loss_responses.append(loss_detail)

        return {
            "items": parse_obj_as(List[LossResponse], loss_responses),
            "total": total,
            "page": skip // limit + 1 if limit else 1,
            "size": limit,
            "pages": (total + limit - 1) // limit if limit else 1
        }
    except Exception as e:
        logger.error(f"获取报损记录列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取报损记录列表失败: {str(e)}"
        )

@router.post("", response_model=LossResponse, status_code=status.HTTP_201_CREATED)
async def create_loss(
    store_id: uuid.UUID = Form(...),
    product_id: uuid.UUID = Form(...),
    quantity: float = Form(...),
    reason: str = Form(...),
    notes: Optional[str] = Form(None),
    images: List[UploadFile] = File(None),
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    创建报损记录
    """
    try:
        # 创建报损记录
        loss_data = LossCreate(
            store_id=store_id,
            product_id=product_id,
            quantity=quantity,
            reason=reason,
            notes=notes
        )

        loss = await LossService.create_loss(
            db=db,
            loss_data=loss_data,
            project_id=project.id,
            user_id=current_user.id
        )

        # 处理图片上传
        if images:
            uploaded_images = []
            total_size = 0

            for image in images:
                # 使用文件上传服务保存图片
                upload_result = await FileUploadService.save_file(
                    file=image,
                    user_id=current_user.id,
                    project_id=project.id,
                    module="loss",
                    category="image",
                    db=db
                )

                if upload_result:
                    uploaded_images.append({
                        "id": upload_result["id"],
                        "filename": upload_result["filename"],
                        "url": upload_result["url"],
                        "size": upload_result["size"]
                    })
                    total_size += upload_result["size"]

            # 更新报损记录的图片信息
            if uploaded_images:
                await LossService.add_loss_images(
                    db=db,
                    loss_id=loss.id,
                    images=uploaded_images,
                    images_size=total_size
                )

        # 获取报损记录详情
        loss_detail = await LossService.get_loss_with_details(db, loss.id)
        if not loss_detail:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="报损记录不存在"
            )

        return parse_obj_as(LossResponse, loss_detail)
    except ValueError as e:
        logger.error(f"创建报损记录失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"创建报损记录失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建报损记录失败: {str(e)}"
        )

@router.get("/{loss_id}", response_model=LossResponse)
async def get_loss(
    loss_id: uuid.UUID = Path(...),
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取报损记录详情
    """
    try:
        # 获取报损记录
        loss = await LossService.get_loss_by_id(db, loss_id)
        if not loss:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="报损记录不存在"
            )

        # 检查报损记录是否属于当前项目
        if loss.project_id != project.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问此报损记录"
            )

        # 获取报损记录详情
        loss_detail = await LossService.get_loss_with_details(db, loss_id)
        if not loss_detail:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="报损记录不存在"
            )

        return parse_obj_as(LossResponse, loss_detail)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取报损记录详情失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取报损记录详情失败: {str(e)}"
        )

@router.put("/{loss_id}", response_model=LossResponse)
async def update_loss(
    loss_id: uuid.UUID = Path(...),
    quantity: Optional[float] = Form(None),
    reason: Optional[str] = Form(None),
    notes: Optional[str] = Form(None),
    images: List[UploadFile] = File(None),
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    更新报损记录
    """
    try:
        # 获取报损记录
        loss = await LossService.get_loss_by_id(db, loss_id)
        if not loss:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="报损记录不存在"
            )

        # 检查报损记录是否属于当前项目
        if loss.project_id != project.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权修改此报损记录"
            )

        # 检查是否为创建者
        if loss.created_by != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="只有创建者可以修改报损记录"
            )

        # 更新报损记录
        loss_data = LossUpdate(
            quantity=quantity,
            reason=reason,
            notes=notes
        )

        updated_loss = await LossService.update_loss(
            db=db,
            loss_id=loss_id,
            loss_data=loss_data
        )

        if not updated_loss:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="报损记录不存在"
            )

        # 处理图片上传
        if images:
            uploaded_images = []
            total_size = 0

            # 保留原有图片
            if updated_loss.images:
                uploaded_images = updated_loss.images
                total_size = updated_loss.images_size

            for image in images:
                # 使用文件上传服务保存图片
                upload_result = await FileUploadService.save_file(
                    file=image,
                    user_id=current_user.id,
                    project_id=project.id,
                    module="loss",
                    category="image",
                    db=db
                )

                if upload_result:
                    uploaded_images.append({
                        "id": upload_result["id"],
                        "filename": upload_result["filename"],
                        "url": upload_result["url"],
                        "size": upload_result["size"]
                    })
                    total_size += upload_result["size"]

            # 更新报损记录的图片信息
            if uploaded_images:
                await LossService.add_loss_images(
                    db=db,
                    loss_id=loss_id,
                    images=uploaded_images,
                    images_size=total_size
                )

        # 获取报损记录详情
        loss_detail = await LossService.get_loss_with_details(db, loss_id)
        if not loss_detail:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="报损记录不存在"
            )

        return parse_obj_as(LossResponse, loss_detail)
    except ValueError as e:
        logger.error(f"更新报损记录失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新报损记录失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新报损记录失败: {str(e)}"
        )

@router.put("/{loss_id}/status", response_model=LossResponse)
async def update_loss_status(
    loss_id: uuid.UUID = Path(...),
    status_update: LossStatusUpdate = None,
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    更新报损记录状态
    """
    try:
        # 获取报损记录
        loss = await LossService.get_loss_by_id(db, loss_id)
        if not loss:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="报损记录不存在"
            )

        # 检查报损记录是否属于当前项目
        if loss.project_id != project.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权修改此报损记录状态"
            )

        # 检查权限
        new_status = status_update.status
        if new_status == "withdrawn":
            # 撤回操作：只有创建者可以撤回
            if loss.created_by != current_user.id:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="只有创建者可以撤回报损记录"
                )
        elif new_status in ["approved", "rejected"]:
            # 审核操作：只有采购角色或管理员可以审核
            # 检查用户是否有采购角色或管理员角色
            has_permission = False

            # 检查用户角色
            if current_user.is_system_admin or current_user.is_tenant_admin:
                has_permission = True
            else:
                # 检查用户是否有采购角色
                # 这里需要根据实际的角色系统进行修改
                result = await db.execute(
                    select(User).where(
                        User.id == current_user.id
                    )
                )
                user = result.scalars().first()
                if user and hasattr(user, 'role') and user.role in ["purchase", "admin"]:
                    has_permission = True

            if not has_permission:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="没有权限审核报损记录"
                )

        # 更新报损记录状态
        updated_loss = await LossService.update_loss_status(
            db=db,
            loss_id=loss_id,
            status_data=status_update,
            user_id=current_user.id
        )

        if not updated_loss:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="报损记录不存在"
            )

        # 获取报损记录详情
        loss_detail = await LossService.get_loss_with_details(db, loss_id)
        if not loss_detail:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="报损记录不存在"
            )

        return parse_obj_as(LossResponse, loss_detail)
    except ValueError as e:
        logger.error(f"更新报损记录状态失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新报损记录状态失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新报损记录状态失败: {str(e)}"
        )
