.create-project-steps-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 24px;
  background-color: #f0f2f5;
}

.create-project-steps-card {
  width: 100%;
  max-width: 1000px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.steps-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 24px;
  position: relative;
}

.back-button {
  position: absolute;
  left: 0;
  top: 0;
}

.project-steps {
  margin-bottom: 32px;
}

.steps-content {
  min-height: 300px;
  margin-bottom: 24px;
}

.step-content {
  padding: 0 16px;
}

.step-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 24px;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}

/* 套餐卡片样式 */
.plan-cards {
  margin-top: 24px;
}

.plan-card {
  height: 100%;
  transition: all 0.3s;
  display: flex;
  flex-direction: column;
}

.plan-card.selected {
  transform: translateY(-5px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.plan-header {
  padding: 16px;
  color: white;
  border-radius: 4px 4px 0 0;
  text-align: center;
}

.plan-name {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 8px;
}

.plan-price {
  font-size: 16px;
}

.price-number {
  font-size: 24px;
  font-weight: bold;
}

.price-unit {
  font-size: 14px;
  margin-left: 4px;
}

.price-free {
  font-size: 24px;
  font-weight: bold;
}

.plan-features {
  padding: 16px;
  flex-grow: 1;
}

.feature-item {
  margin-bottom: 8px;
  display: flex;
  align-items: center;
}

.feature-item .anticon {
  margin-right: 8px;
  color: #52c41a;
}

/* 支付卡片样式 */
.payment-card {
  margin-top: 24px;
}

.selected-plan-info {
  text-align: center;
  padding: 16px;
}

.payment-methods {
  display: flex;
  flex-direction: column;
}

.payment-methods .ant-radio-wrapper {
  height: 40px;
  line-height: 40px;
  margin-bottom: 8px;
}

.redeem-code-section {
  margin-top: 16px;
  padding: 16px;
  background-color: #f9f9f9;
  border-radius: 4px;
}

.redeem-tip {
  margin-top: 8px;
}

/* 确认卡片样式 */
.confirmation-card {
  margin-top: 24px;
}

.confirmation-item {
  display: flex;
  margin-bottom: 16px;
}

.confirmation-item .label {
  width: 100px;
  color: rgba(0, 0, 0, 0.45);
}

.confirmation-item .value {
  flex: 1;
  font-weight: 500;
}

/* 创建步骤样式 */
.creation-step {
  text-align: center;
}

.creation-progress {
  margin: 32px auto;
  max-width: 600px;
}

.progress-text {
  margin-top: 8px;
  color: rgba(0, 0, 0, 0.45);
}

.creation-illustration {
  margin: 32px auto;
  max-width: 400px;
}

.creation-illustration img {
  width: 100%;
}

/* 创建完成样式 */
.creation-complete {
  text-align: center;
  padding: 32px 0;
}

.success-icon {
  font-size: 72px;
  color: #52c41a;
  margin-bottom: 24px;
}

.action-buttons {
  margin-top: 32px;
}
