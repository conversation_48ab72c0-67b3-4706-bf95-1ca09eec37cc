import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Input,
  Button,
  Select,
  Space,
  Divider,
  message,
  Typography,
  Alert,
  Tabs,
  Table,
  Tag,
  Tooltip,
  Badge
} from 'antd';
import {
  SendOutlined,
  SettingOutlined,
  HistoryOutlined,
  SafetyOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import { getProjectId } from '../../../../services/api/httpClient';
import axios from 'axios';

const { Title, Paragraph, Text } = Typography;
const { TextArea } = Input;
const { TabPane } = Tabs;
const { Option } = Select;

/**
 * 内容审核插件组件
 */
const ContentModerationPlugin = () => {
  const [form] = Form.useForm();
  const [settingsForm] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('moderate');
  const [result, setResult] = useState(null);
  const [settings, setSettings] = useState({
    sensitivity: 'medium',
    filter_mode: 'replace',
    replacement_char: '*'
  });
  const [history, setHistory] = useState([]);
  const projectId = getProjectId();
  
  // 获取插件设置
  const fetchSettings = async () => {
    try {
      const response = await axios.get(`/api/v1/project/${projectId}/ai/plugin/content_moderation/config`);
      if (response.data) {
        setSettings(response.data);
        settingsForm.setFieldsValue(response.data);
      }
    } catch (error) {
      console.error('获取插件设置失败:', error);
      message.error('获取插件设置失败');
    }
  };
  
  // 获取审核历史
  const fetchHistory = async () => {
    try {
      setLoading(true);
      // 模拟获取历史记录
      setTimeout(() => {
        const mockHistory = [
          {
            id: '1',
            content: '这是一个测试内容，包含敏感词1',
            filtered_content: '这是一个测试内容，包含***',
            is_safe: false,
            categories: { '敏感词1': 0.9 },
            created_at: '2023-05-15 10:30:45'
          },
          {
            id: '2',
            content: '这是一个正常的内容',
            filtered_content: '这是一个正常的内容',
            is_safe: true,
            categories: {},
            created_at: '2023-05-15 11:20:15'
          },
          {
            id: '3',
            content: '这个内容包含敏感词2和敏感词3',
            filtered_content: '这个内容包含***和***',
            is_safe: false,
            categories: { '敏感词2': 0.9, '敏感词3': 0.9 },
            created_at: '2023-05-16 09:15:30'
          }
        ];
        setHistory(mockHistory);
        setLoading(false);
      }, 500);
    } catch (error) {
      console.error('获取审核历史失败:', error);
      message.error('获取审核历史失败');
      setLoading(false);
    }
  };
  
  // 提交内容审核
  const handleSubmit = async (values) => {
    try {
      setLoading(true);
      const response = await axios.post(`/api/v1/project/${projectId}/ai/plugin/content_moderation/moderate`, {
        content: values.content,
        context: values.context ? JSON.parse(values.context) : undefined
      });
      
      setResult(response.data);
      
      // 添加到历史记录
      setHistory([
        {
          id: Date.now().toString(),
          content: values.content,
          filtered_content: response.data.filtered_content,
          is_safe: response.data.is_safe,
          categories: response.data.categories,
          created_at: new Date().toLocaleString()
        },
        ...history
      ]);
      
      message.success('内容审核完成');
    } catch (error) {
      console.error('内容审核失败:', error);
      message.error('内容审核失败');
    } finally {
      setLoading(false);
    }
  };
  
  // 保存设置
  const handleSaveSettings = async (values) => {
    try {
      setLoading(true);
      // 模拟保存设置
      setTimeout(() => {
        setSettings(values);
        message.success('设置保存成功');
        setLoading(false);
      }, 500);
    } catch (error) {
      console.error('保存设置失败:', error);
      message.error('保存设置失败');
      setLoading(false);
    }
  };
  
  // 初始化
  useEffect(() => {
    fetchSettings();
    fetchHistory();
  }, []);
  
  // 历史记录表格列
  const historyColumns = [
    {
      title: '原始内容',
      dataIndex: 'content',
      key: 'content',
      ellipsis: true,
      width: 200
    },
    {
      title: '过滤后内容',
      dataIndex: 'filtered_content',
      key: 'filtered_content',
      ellipsis: true,
      width: 200
    },
    {
      title: '审核结果',
      dataIndex: 'is_safe',
      key: 'is_safe',
      render: (isSafe) => (
        isSafe ? (
          <Tag color="green">安全</Tag>
        ) : (
          <Tag color="red">不安全</Tag>
        )
      ),
      width: 100
    },
    {
      title: '敏感类别',
      dataIndex: 'categories',
      key: 'categories',
      render: (categories) => (
        <Space>
          {Object.keys(categories).map(category => (
            <Tag key={category} color="orange">
              {category}
            </Tag>
          ))}
          {Object.keys(categories).length === 0 && (
            <span>-</span>
          )}
        </Space>
      ),
      width: 150
    },
    {
      title: '审核时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 150
    }
  ];
  
  return (
    <div className="content-moderation-plugin">
      <Card
        title={
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Space>
              <SafetyOutlined />
              <span>内容审核</span>
            </Space>
            <Space>
              <Badge count={history.filter(item => !item.is_safe).length} overflowCount={99}>
                <Button
                  icon={<HistoryOutlined />}
                  onClick={() => setActiveTab('history')}
                >
                  审核历史
                </Button>
              </Badge>
              <Button
                icon={<SettingOutlined />}
                onClick={() => setActiveTab('settings')}
              >
                设置
              </Button>
            </Space>
          </div>
        }
      >
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab="内容审核" key="moderate">
            <Form
              form={form}
              layout="vertical"
              onFinish={handleSubmit}
            >
              <Form.Item
                name="content"
                label="待审核内容"
                rules={[{ required: true, message: '请输入待审核内容' }]}
              >
                <TextArea
                  rows={6}
                  placeholder="请输入需要审核的内容"
                />
              </Form.Item>
              
              <Form.Item
                name="context"
                label={
                  <span>
                    上下文信息（可选）
                    <Tooltip title="以 JSON 格式提供上下文信息，例如：{'user_id': '123', 'channel': 'chat'}">
                      <InfoCircleOutlined style={{ marginLeft: 8 }} />
                    </Tooltip>
                  </span>
                }
              >
                <TextArea
                  rows={3}
                  placeholder="请输入 JSON 格式的上下文信息（可选）"
                />
              </Form.Item>
              
              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  icon={<SendOutlined />}
                  loading={loading}
                >
                  提交审核
                </Button>
              </Form.Item>
            </Form>
            
            {result && (
              <div style={{ marginTop: 16 }}>
                <Divider>审核结果</Divider>
                
                <Alert
                  type={result.is_safe ? 'success' : 'error'}
                  message={result.is_safe ? '内容安全' : '内容包含敏感信息'}
                  description={
                    <div>
                      {!result.is_safe && (
                        <div style={{ marginBottom: 8 }}>
                          <Text strong>敏感类别：</Text>
                          <Space>
                            {Object.keys(result.categories).map(category => (
                              <Tag key={category} color="orange">
                                {category}
                              </Tag>
                            ))}
                          </Space>
                        </div>
                      )}
                      <div>
                        <Text strong>过滤后内容：</Text>
                        <Paragraph style={{ marginTop: 8, padding: 8, background: '#f5f5f5', borderRadius: 4 }}>
                          {result.filtered_content}
                        </Paragraph>
                      </div>
                    </div>
                  }
                  showIcon
                />
              </div>
            )}
          </TabPane>
          
          <TabPane tab="审核历史" key="history">
            <Table
              columns={historyColumns}
              dataSource={history}
              rowKey="id"
              loading={loading}
              pagination={{ pageSize: 10 }}
              scroll={{ x: 800 }}
            />
          </TabPane>
          
          <TabPane tab="设置" key="settings">
            <Form
              form={settingsForm}
              layout="vertical"
              initialValues={settings}
              onFinish={handleSaveSettings}
            >
              <Form.Item
                name="sensitivity"
                label="审核敏感度"
                rules={[{ required: true, message: '请选择审核敏感度' }]}
              >
                <Select>
                  <Option value="low">低（仅过滤明显敏感内容）</Option>
                  <Option value="medium">中（默认）</Option>
                  <Option value="high">高（严格过滤）</Option>
                </Select>
              </Form.Item>
              
              <Form.Item
                name="filter_mode"
                label="过滤模式"
                rules={[{ required: true, message: '请选择过滤模式' }]}
              >
                <Select>
                  <Option value="replace">替换敏感词</Option>
                  <Option value="remove">删除敏感词</Option>
                  <Option value="tag">标记敏感词</Option>
                </Select>
              </Form.Item>
              
              <Form.Item
                name="replacement_char"
                label="替换字符"
                rules={[{ required: true, message: '请输入替换字符' }]}
                dependencies={['filter_mode']}
              >
                <Input
                  placeholder="请输入替换字符"
                  disabled={settingsForm.getFieldValue('filter_mode') !== 'replace'}
                />
              </Form.Item>
              
              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={loading}
                >
                  保存设置
                </Button>
              </Form.Item>
            </Form>
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default ContentModerationPlugin;
