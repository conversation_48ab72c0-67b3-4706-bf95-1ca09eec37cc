#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
初始化AI提供商和模型数据
"""

import uuid
from sqlalchemy.orm import Session
from models.ai import AIProvider, AIModel

def init_ai_providers(db: Session):
    """初始化AI提供商数据"""
    
    # 检查是否已经初始化
    existing_providers = db.query(AIProvider).first()
    if existing_providers:
        print("AI提供商数据已存在，跳过初始化")
        return []
    
    # 创建提供商数据
    providers_data = [
        {
            "name": "openai",
            "display_name": "OpenAI",
            "description": "OpenAI GPT系列模型",
            "api_base_url": "https://api.openai.com/v1",
            "status": "active",
            "config_schema": {
                "api_key": {"type": "string", "required": True, "description": "OpenAI API密钥"},
                "organization": {"type": "string", "required": False, "description": "组织ID"},
            }
        },
        {
            "name": "azure",
            "display_name": "Azure OpenAI",
            "description": "Azure OpenAI服务",
            "api_base_url": "https://{resource_name}.openai.azure.com/openai/deployments/{deployment_id}/chat/completions",
            "status": "active",
            "config_schema": {
                "api_key": {"type": "string", "required": True, "description": "Azure API密钥"},
                "resource_name": {"type": "string", "required": True, "description": "Azure资源名称"},
                "deployment_id": {"type": "string", "required": True, "description": "部署ID"},
                "api_version": {"type": "string", "required": False, "description": "API版本", "default": "2023-05-15"},
            }
        },
        {
            "name": "anthropic",
            "display_name": "Anthropic",
            "description": "Anthropic Claude系列模型",
            "api_base_url": "https://api.anthropic.com/v1",
            "status": "active",
            "config_schema": {
                "api_key": {"type": "string", "required": True, "description": "Anthropic API密钥"},
            }
        },
        {
            "name": "alibaba_bailian",
            "display_name": "阿里巴巴百炼",
            "description": "阿里巴巴通义千问系列模型",
            "api_base_url": "https://dashscope.aliyuncs.com/compatible-mode/v1",
            "status": "active",
            "config_schema": {
                "api_key": {"type": "string", "required": True, "description": "DashScope API密钥"},
            }
        },
        {
            "name": "google",
            "display_name": "Google",
            "description": "Google Gemini系列模型",
            "api_base_url": "https://generativelanguage.googleapis.com",
            "status": "active",
            "config_schema": {
                "api_key": {"type": "string", "required": True, "description": "Google API密钥"},
            }
        },
        {
            "name": "openrouter",
            "display_name": "OpenRouter",
            "description": "OpenRouter多模型聚合平台",
            "api_base_url": "https://openrouter.ai/api/v1",
            "status": "active",
            "config_schema": {
                "api_key": {"type": "string", "required": True, "description": "OpenRouter API密钥"},
            }
        },
        {
            "name": "deepseek",
            "display_name": "DeepSeek",
            "description": "DeepSeek官方API",
            "api_base_url": "https://api.deepseek.com/v1",
            "status": "active",
            "config_schema": {
                "api_key": {"type": "string", "required": True, "description": "DeepSeek API密钥"},
            }
        },
    ]
    
    # 创建提供商
    providers = {}
    for provider_data in providers_data:
        provider = AIProvider(
            id=uuid.uuid4(),
            **provider_data
        )
        db.add(provider)
        providers[provider_data["name"]] = provider
    
    db.flush()  # 获取ID
    print(f"创建了 {len(providers)} 个AI提供商")
    return providers

def init_ai_models(db: Session, providers: dict):
    """初始化AI模型数据"""
    
    # 检查是否已经初始化
    existing_models = db.query(AIModel).first()
    if existing_models:
        print("AI模型数据已存在，跳过初始化")
        return []
    
    # 创建模型数据
    models_data = [
        # OpenAI模型
        {
            "provider": "openai",
            "name": "gpt-4o",
            "display_name": "GPT-4o",
            "description": "OpenAI最新的多模态模型",
            "model_type": "chat",
            "max_tokens": 4096,
            "supports_functions": True,
            "supports_vision": True,
            "input_price_per_1k_tokens": 0.005,
            "output_price_per_1k_tokens": 0.015,
            "default_temperature": 0.7,
        },
        {
            "provider": "openai",
            "name": "gpt-4o-mini",
            "display_name": "GPT-4o Mini",
            "description": "OpenAI高性价比模型",
            "model_type": "chat",
            "max_tokens": 16384,
            "supports_functions": True,
            "supports_vision": True,
            "input_price_per_1k_tokens": 0.00015,
            "output_price_per_1k_tokens": 0.0006,
            "default_temperature": 0.7,
        },
        {
            "provider": "openai",
            "name": "gpt-4-turbo",
            "display_name": "GPT-4 Turbo",
            "description": "OpenAI GPT-4 Turbo模型",
            "model_type": "chat",
            "max_tokens": 4096,
            "supports_functions": True,
            "supports_vision": True,
            "input_price_per_1k_tokens": 0.01,
            "output_price_per_1k_tokens": 0.03,
            "default_temperature": 0.7,
        },
        
        # Anthropic模型
        {
            "provider": "anthropic",
            "name": "claude-3-5-sonnet-20241022",
            "display_name": "Claude 3.5 Sonnet",
            "description": "Anthropic最新的Claude 3.5 Sonnet模型",
            "model_type": "chat",
            "max_tokens": 8192,
            "supports_functions": True,
            "supports_vision": True,
            "input_price_per_1k_tokens": 0.003,
            "output_price_per_1k_tokens": 0.015,
            "default_temperature": 0.7,
        },
        {
            "provider": "anthropic",
            "name": "claude-3-5-haiku-20241022",
            "display_name": "Claude 3.5 Haiku",
            "description": "Anthropic高速Claude 3.5 Haiku模型",
            "model_type": "chat",
            "max_tokens": 8192,
            "supports_functions": True,
            "supports_vision": True,
            "input_price_per_1k_tokens": 0.001,
            "output_price_per_1k_tokens": 0.005,
            "default_temperature": 0.7,
        },
        
        # 阿里巴巴百炼模型
        {
            "provider": "alibaba_bailian",
            "name": "qwen-plus",
            "display_name": "通义千问Plus",
            "description": "阿里巴巴通义千问Plus模型",
            "model_type": "chat",
            "max_tokens": 8192,
            "supports_functions": True,
            "supports_vision": False,
            "input_price_per_1k_tokens": 0.0008,
            "output_price_per_1k_tokens": 0.002,
            "default_temperature": 0.7,
        },
        {
            "provider": "alibaba_bailian",
            "name": "qwen-max",
            "display_name": "通义千问Max",
            "description": "阿里巴巴通义千问Max模型",
            "model_type": "chat",
            "max_tokens": 8192,
            "supports_functions": True,
            "supports_vision": False,
            "input_price_per_1k_tokens": 0.02,
            "output_price_per_1k_tokens": 0.06,
            "default_temperature": 0.7,
        },
        {
            "provider": "alibaba_bailian",
            "name": "qwen-vl-plus",
            "display_name": "通义千问VL Plus",
            "description": "阿里巴巴通义千问视觉模型",
            "model_type": "chat",
            "max_tokens": 8192,
            "supports_functions": True,
            "supports_vision": True,
            "input_price_per_1k_tokens": 0.008,
            "output_price_per_1k_tokens": 0.008,
            "default_temperature": 0.7,
        },
        {
            "provider": "alibaba_bailian",
            "name": "qwen-vl-max",
            "display_name": "通义千问VL Max",
            "description": "阿里巴巴2025年最新的多模态视觉大模型，支持高精度图像理解、视频分析和文档解析",
            "model_type": "multimodal",
            "max_tokens": 32768,
            "supports_functions": True,
            "supports_vision": True,
            "input_price_per_1k_tokens": 0.012,
            "output_price_per_1k_tokens": 0.024,
            "default_temperature": 0.7,
        },
        {
            "provider": "alibaba_bailian",
            "name": "qvq-max",
            "display_name": "通义QVQ Max",
            "description": "阿里巴巴2025年最新的推理大模型，专注于复杂逻辑推理、数学计算和科学分析",
            "model_type": "reasoning",
            "max_tokens": 65536,
            "supports_functions": True,
            "supports_vision": False,
            "input_price_per_1k_tokens": 0.025,
            "output_price_per_1k_tokens": 0.05,
            "default_temperature": 0.7,
        },
        {
            "provider": "alibaba_bailian",
            "name": "qwen-omni-turbo-realtime",
            "display_name": "通义千问Omni Turbo实时版",
            "description": "阿里巴巴2025年最新的实时多模态大模型，支持语音、视频、文本的实时交互处理",
            "model_type": "multimodal",
            "max_tokens": 16384,
            "supports_functions": True,
            "supports_vision": True,
            "input_price_per_1k_tokens": 0.004,
            "output_price_per_1k_tokens": 0.008,
            "default_temperature": 0.7,
        },
        
        # Google模型
        {
            "provider": "google",
            "name": "gemini-2.0-flash-exp",
            "display_name": "Gemini 2.0 Flash",
            "description": "Google最新的Gemini 2.0 Flash模型",
            "model_type": "chat",
            "max_tokens": 8192,
            "supports_functions": True,
            "supports_vision": True,
            "input_price_per_1k_tokens": 0.00015,
            "output_price_per_1k_tokens": 0.0006,
            "default_temperature": 0.7,
        },
        {
            "provider": "google",
            "name": "gemini-1.5-pro",
            "display_name": "Gemini 1.5 Pro",
            "description": "Google Gemini 1.5 Pro模型",
            "model_type": "chat",
            "max_tokens": 8192,
            "supports_functions": True,
            "supports_vision": True,
            "input_price_per_1k_tokens": 0.00125,
            "output_price_per_1k_tokens": 0.005,
            "default_temperature": 0.7,
        },
        
        # OpenRouter模型
        {
            "provider": "openrouter",
            "name": "anthropic/claude-3.5-sonnet",
            "display_name": "Claude 3.5 Sonnet (OpenRouter)",
            "description": "通过OpenRouter访问的Claude 3.5 Sonnet",
            "model_type": "chat",
            "max_tokens": 8192,
            "supports_functions": True,
            "supports_vision": True,
            "input_price_per_1k_tokens": 0.003,
            "output_price_per_1k_tokens": 0.015,
            "default_temperature": 0.7,
        },
        {
            "provider": "openrouter",
            "name": "openai/gpt-4o",
            "display_name": "GPT-4o (OpenRouter)",
            "description": "通过OpenRouter访问的GPT-4o",
            "model_type": "chat",
            "max_tokens": 4096,
            "supports_functions": True,
            "supports_vision": True,
            "input_price_per_1k_tokens": 0.005,
            "output_price_per_1k_tokens": 0.015,
            "default_temperature": 0.7,
        },
        {
            "provider": "openrouter",
            "name": "google/gemini-2.0-flash-exp:free",
            "display_name": "Gemini 2.0 Flash (Free)",
            "description": "通过OpenRouter免费访问的Gemini 2.0 Flash",
            "model_type": "chat",
            "max_tokens": 8192,
            "supports_functions": True,
            "supports_vision": True,
            "input_price_per_1k_tokens": 0.0,
            "output_price_per_1k_tokens": 0.0,
            "default_temperature": 0.7,
        },
        # DeepSeek官方模型
        {
            "provider": "deepseek",
            "name": "deepseek-chat",
            "display_name": "DeepSeek Chat",
            "description": "DeepSeek官方聊天模型",
            "model_type": "chat",
            "max_tokens": 8192,
            "supports_functions": True,
            "supports_vision": False,
            "input_price_per_1k_tokens": 0.00014,
            "output_price_per_1k_tokens": 0.00028,
            "default_temperature": 0.7,
        },
        {
            "provider": "deepseek",
            "name": "deepseek-coder",
            "display_name": "DeepSeek Coder",
            "description": "DeepSeek代码生成专用模型",
            "model_type": "chat",
            "max_tokens": 8192,
            "supports_functions": True,
            "supports_vision": False,
            "input_price_per_1k_tokens": 0.00014,
            "output_price_per_1k_tokens": 0.00028,
            "default_temperature": 0.7,
        },
        {
            "provider": "deepseek",
            "name": "deepseek-reasoner",
            "display_name": "DeepSeek Reasoner",
            "description": "DeepSeek推理专用模型",
            "model_type": "chat",
            "max_tokens": 8192,
            "supports_functions": True,
            "supports_vision": False,
            "input_price_per_1k_tokens": 0.00055,
            "output_price_per_1k_tokens": 0.0019,
            "default_temperature": 0.7,
        },
    ]
    
    # 创建模型
    models = []
    for model_data in models_data:
        provider_name = model_data.pop("provider")
        if provider_name not in providers:
            print(f"警告：提供商 {provider_name} 不存在，跳过模型创建")
            continue
            
        provider = providers[provider_name]
        
        model = AIModel(
            id=uuid.uuid4(),
            provider_id=provider.id,
            status="active",
            **model_data
        )
        db.add(model)
        models.append(model)
    
    print(f"创建了 {len(models)} 个AI模型")
    return models

def init_ai_data(db: Session):
    """初始化所有AI相关数据"""
    print("开始初始化AI数据...")
    
    # 初始化提供商
    providers = init_ai_providers(db)
    
    # 初始化模型
    models = init_ai_models(db, providers)
    
    # 提交事务
    db.commit()
    
    print("AI数据初始化完成")
    return {
        "providers": providers,
        "models": models
    } 