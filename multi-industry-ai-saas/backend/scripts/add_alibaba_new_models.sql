-- 添加阿里云百炼2025年新模型
-- 该脚本用于添加 qwen-vl-max, qvq-max, qwen-omni-turbo-realtime 三个新模型

DO $$
DECLARE
    alibaba_provider_id UUID;
    model_exists INTEGER;
BEGIN
    -- 获取阿里巴巴百炼提供商ID
    SELECT id INTO alibaba_provider_id 
    FROM ai_providers 
    WHERE name = 'alibaba_bailian';

    -- 检查提供商是否存在
    IF alibaba_provider_id IS NULL THEN
        RAISE EXCEPTION '阿里巴巴百炼提供商不存在，请先初始化AI提供商数据';
    END IF;

    -- 检查并添加 qwen-vl-max 模型（多模态视觉模型）
    SELECT COUNT(*) INTO model_exists 
    FROM ai_models 
    WHERE provider_id = alibaba_provider_id AND name = 'qwen-vl-max';
    
    IF model_exists = 0 THEN
        INSERT INTO ai_models (
            id, provider_id, name, display_name, description, model_type,
            capabilities, context_window, token_limit,
            input_price_per_1k_tokens, output_price_per_1k_tokens,
            image_price_per_1k_tokens, average_response_time, rate_limit_per_minute,
            status, is_builtin, created_at, updated_at
        ) VALUES (
            gen_random_uuid(),
            alibaba_provider_id,
            'qwen-vl-max',
            '通义千问VL Max',
            '阿里巴巴2025年最新的多模态视觉大模型，支持高精度图像理解、视频分析和文档解析',
            'multimodal',
            jsonb_build_object(
                'supports_function_calling', true,
                'supports_vision', true,
                'supports_audio_input', false,
                'supports_audio_output', false,
                'supports_speech_to_text', false,
                'supports_text_to_speech', false,
                'supports_system_message', true,
                'supports_streaming', true,
                'supports_reasoning', true,
                'supports_code_generation', true,
                'supports_multilingual', true,
                'supports_document_analysis', true,
                'supports_chart_analysis', true,
                'supports_ocr', true,
                'supports_image_understanding', true,
                'supports_video_understanding', true,
                'supports_grounding', true
            ),
            32768,
            32768,
            0.012,
            0.024,
            0.012,
            3.5,
            60,
            'active',
            true,
            NOW(),
            NOW()
        );
        RAISE NOTICE '添加模型: qwen-vl-max';
    ELSE
        RAISE NOTICE '模型 qwen-vl-max 已存在，跳过';
    END IF;

    -- 检查并添加 qvq-max 模型（推理模型）
    SELECT COUNT(*) INTO model_exists 
    FROM ai_models 
    WHERE provider_id = alibaba_provider_id AND name = 'qvq-max';
    
    IF model_exists = 0 THEN
        INSERT INTO ai_models (
            id, provider_id, name, display_name, description, model_type,
            capabilities, context_window, token_limit,
            input_price_per_1k_tokens, output_price_per_1k_tokens,
            average_response_time, rate_limit_per_minute,
            status, is_builtin, created_at, updated_at
        ) VALUES (
            gen_random_uuid(),
            alibaba_provider_id,
            'qvq-max',
            '通义QVQ Max',
            '阿里巴巴2025年最新的推理大模型，专注于复杂逻辑推理、数学计算和科学分析',
            'reasoning',
            jsonb_build_object(
                'supports_function_calling', true,
                'supports_vision', false,
                'supports_audio_input', false,
                'supports_audio_output', false,
                'supports_speech_to_text', false,
                'supports_text_to_speech', false,
                'supports_system_message', true,
                'supports_streaming', true,
                'supports_reasoning', true,
                'supports_code_generation', true,
                'supports_multilingual', true,
                'supports_thinking', true,
                'supports_mathematical_reasoning', true,
                'supports_logical_reasoning', true,
                'supports_scientific_analysis', true
            ),
            65536,
            65536,
            0.025,
            0.05,
            4.0,
            30,
            'active',
            true,
            NOW(),
            NOW()
        );
        RAISE NOTICE '添加模型: qvq-max';
    ELSE
        RAISE NOTICE '模型 qvq-max 已存在，跳过';
    END IF;

    -- 检查并添加 qwen-omni-turbo-realtime 模型（实时多模态模型）
    SELECT COUNT(*) INTO model_exists 
    FROM ai_models 
    WHERE provider_id = alibaba_provider_id AND name = 'qwen-omni-turbo-realtime';
    
    IF model_exists = 0 THEN
        INSERT INTO ai_models (
            id, provider_id, name, display_name, description, model_type,
            capabilities, context_window, token_limit,
            input_price_per_1k_tokens, output_price_per_1k_tokens,
            image_price_per_1k_tokens, audio_price_per_minute, video_price_per_minute,
            average_response_time, rate_limit_per_minute,
            status, is_builtin, created_at, updated_at
        ) VALUES (
            gen_random_uuid(),
            alibaba_provider_id,
            'qwen-omni-turbo-realtime',
            '通义千问Omni Turbo实时版',
            '阿里巴巴2025年最新的实时多模态大模型，支持语音、视频、文本的实时交互处理',
            'multimodal',
            jsonb_build_object(
                'supports_function_calling', true,
                'supports_vision', true,
                'supports_audio_input', true,
                'supports_audio_output', true,
                'supports_speech_to_text', true,
                'supports_text_to_speech', true,
                'supports_system_message', true,
                'supports_streaming', true,
                'supports_reasoning', false,
                'supports_code_generation', true,
                'supports_multilingual', true,
                'supports_realtime_interaction', true,
                'supports_voice_conversation', true,
                'supports_video_understanding', true,
                'supports_multimodal_fusion', true
            ),
            16384,
            16384,
            0.004,
            0.008,
            0.004,
            0.02,
            0.05,
            1.2,
            120,
            'active',
            true,
            NOW(),
            NOW()
        );
        RAISE NOTICE '添加模型: qwen-omni-turbo-realtime';
    ELSE
        RAISE NOTICE '模型 qwen-omni-turbo-realtime 已存在，跳过';
    END IF;

    RAISE NOTICE '阿里云百炼新模型添加完成！';
END $$; 