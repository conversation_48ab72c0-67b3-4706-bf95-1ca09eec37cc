http:
  routers:
    # MCP 服务器路由 - 转发到主项目nginx，由主项目后端处理MCP服务器管理
    # 注意：降低优先级，让Docker自动发现的MCP实例路由优先
    mcp-router:
      rule: "PathPrefix(`/mcp`)"
      service: main-nginx-service  # 修改：转发到主项目nginx
      middlewares:
        - mcp-headers  # 修改：使用专门的MCP头部中间件
      priority: 50  # 降低优先级，让Docker MCP实例路由（优先级200）优先
      entryPoints:
        - web
        - websecure
      # v3.3新功能：可以为单个路由配置观测性
      # observability:
      #   tracing: true
      #   metrics: true
        
    # MCP实例直接路由（基于Host规则，动态）- 暂时禁用，让Docker自动发现路由生效
    # mcp-instance-router:
    #   rule: "HostRegexp(`mcp-{server_id:[a-z0-9\\\\-]+}.localhost`) || HostRegexp(`mcp-{server_id:[a-z0-9\\\\-]+}.{domain:.+}`)"
    #   service: mcp-dynamic
    #   middlewares:
    #     - mcp-auth  # 新增：鉴权中间件
    #     - sse-no-buffer
    #     - mcp-error-pages  # 新增：错误页面处理
    #   priority: 200
    #   entryPoints:
    #     - web
    #     - websecure

    # 服务商管理系统路由 - 预留后期处理
    sp-router:
      rule: "PathPrefix(`/sp`)"
      service: sp-service
      middlewares:
        - strip-sp-prefix
      priority: 90  # 中等优先级
      entryPoints:
        - web
        - websecure

    # API相关路由 - 转发到主项目nginx
    api-router:
      rule: "PathPrefix(`/api`)"
      service: main-nginx-service
      priority: 80
      middlewares:
        - api-headers
      entryPoints:
        - web
        - websecure

    # 上传文件路由 - 转发到主项目nginx
    uploads-router:
      rule: "PathPrefix(`/uploads`)"
      service: main-nginx-service
      priority: 75
      middlewares:
        - static-headers
      entryPoints:
        - web
        - websecure

    # 静态资源路由 - 转发到主项目nginx  
    static-router:
      rule: "PathPrefix(`/static`, `/media`, `/assets`)"
      service: main-nginx-service
      priority: 70
      middlewares:
        - static-headers
      entryPoints:
        - web
        - websecure

    # 健康检查路由 - 转发到主项目nginx
    health-router:
      rule: "PathPrefix(`/health`)"
      service: main-nginx-service
      priority: 85
      entryPoints:
        - web
        - websecure

    # WebSocket路由 - 支持webpack-dev-server的热重载
    ws-router:
      rule: "PathPrefix(`/ws`) || PathPrefix(`/sockjs-node`) || PathPrefix(`/__webpack_hmr`)"
      service: main-nginx-service
      priority: 95
      middlewares:
        - ws-headers
      entryPoints:
        - web
        - websecure

    # 主项目默认路由 - 转发到主项目nginx（最低优先级，兜底）
    main-router:
      rule: "PathPrefix(`/`)"
      service: main-nginx-service
      priority: 1  # 最低优先级，作为默认路由
      entryPoints:
        - web
        - websecure

  middlewares:
    # MCP鉴权中间件 - 使用ForwardAuth验证SAPI令牌
    mcp-auth:
      forwardAuth:
        address: "http://sapi-traefik-auth:8001/auth/verify"
        trustForwardHeader: true
        authRequestHeaders:
          - "Authorization"
          - "X-MCP-Server-ID"
          - "X-Forwarded-Uri"
        authResponseHeaders:
          - "X-MCP-User-ID"
          - "X-MCP-Project-ID"
          - "X-MCP-Session-ID"
          - "X-MCP-System-Token"
          - "X-MCP-Permissions"
          - "X-MCP-Auth-Type"

    # MCP相关中间件 - 不移除前缀，保留完整路径给主项目处理
    mcp-headers:
      headers:
        customRequestHeaders:
          X-Forwarded-Proto: ""  # 让Traefik自动设置正确的协议
          X-Real-IP: ""
          X-MCP-Request: "true"  # 添加标识，帮助主项目识别MCP请求
        customResponseHeaders:
          X-Content-Type-Options: "nosniff"
          # 支持 SSE 和长连接
          X-Accel-Buffering: "no"
          Cache-Control: "no-cache"

    # 错误页面中间件
    mcp-error-pages:
      errors:
        status:
          - "401-403"
          - "500-599"
        service: mcp-error-service
        query: "/{status}.html"

    # 服务商系统中间件
    strip-sp-prefix:
      stripPrefix:
        prefixes:
          - "/sp"
          
    # API请求中间件
    api-headers:
      headers:
        customRequestHeaders:
          X-Forwarded-Proto: ""  # 让Traefik自动设置正确的协议
          X-Real-IP: ""
        customResponseHeaders:
          X-Content-Type-Options: "nosniff"
          X-Frame-Options: "DENY"

    # 静态资源中间件
    static-headers:
      headers:
        customResponseHeaders:
          Cache-Control: "public, max-age=31536000"
          X-Content-Type-Options: "nosniff"
          
    # 为SSE连接添加特殊中间件
    sse-no-buffer:
      headers:
        customResponseHeaders:
          X-Accel-Buffering: "no"
          Cache-Control: "no-cache"
          Connection: "keep-alive"

    # WebSocket中间件
    ws-headers:
      headers:
        customRequestHeaders:
          X-Forwarded-Proto: ""
          X-Real-IP: ""
          # WebSocket升级头部
          Connection: "Upgrade"
          Upgrade: "websocket"
        customResponseHeaders:
          X-Content-Type-Options: "nosniff"

  services:
    # 主项目Nginx服务 - 连接到nginx容器
    main-nginx-service:
      loadBalancer:
        servers:
          - url: "http://retail-ai-saas-nginx-dev:80"  # 连接到nginx容器的80端口
        healthCheck:
          path: "/health"
          interval: "30s"
          timeout: "10s"

    # 服务商系统服务 - 预留后期配置
    sp-service:
      loadBalancer:
        servers:
          - url: "http://**************:8083"  # 服务商系统地址
        healthCheck:
          path: "/health"
          interval: "30s"
          timeout: "10s"

    # 动态MCP服务 - 通过Docker标签自动发现
    # 注意：实际的服务器列表由Docker自动发现提供
    mcp-dynamic:
      loadBalancer:
        servers: []
        # 健康检查配置
        healthCheck:
          path: "/health"
          interval: "30s"
          timeout: "10s"

    # SAPI Traefik鉴权服务
    sapi-traefik-auth:
      loadBalancer:
        servers:
          - url: "http://sapi-traefik-auth:8001"
        healthCheck:
          path: "/health"
          interval: "30s"
          timeout: "10s"

    # 错误页面服务（复用鉴权服务）
    mcp-error-service:
      loadBalancer:
        servers:
          - url: "http://sapi-traefik-auth:8001"
