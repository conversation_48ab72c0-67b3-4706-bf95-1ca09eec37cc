import api from '../httpClient';
import { getProjectId } from '../httpClient';

/**
 * 获取库存调拨列表
 * @param {Object} params - 查询参数
 * @returns {Promise<Object>} 库存调拨列表
 */
export const getInventoryTransfers = (params = {}) => {
  const projectId = getProjectId();
  return api.request({
    url: `/project/${projectId}/store-inventory-transfers`,
    method: 'GET',
    params
  });
};

/**
 * 获取库存调拨详情
 * @param {string} transferId - 调拨ID
 * @returns {Promise<Object>} 库存调拨详情
 */
export const getInventoryTransfer = (transferId) => {
  const projectId = getProjectId();
  return api.request({
    url: `/project/${projectId}/store-inventory-transfers/${transferId}`,
    method: 'GET'
  });
};

/**
 * 创建库存调拨
 * @param {Object} data - 调拨数据
 * @returns {Promise<Object>} 创建结果
 */
export const createInventoryTransfer = (data) => {
  const projectId = getProjectId();
  return api.request({
    url: `/project/${projectId}/store-inventory-transfers`,
    method: 'POST',
    data
  });
};

/**
 * 更新库存调拨
 * @param {string} transferId - 调拨ID
 * @param {Object} data - 更新数据
 * @returns {Promise<Object>} 更新结果
 */
export const updateInventoryTransfer = (transferId, data) => {
  const projectId = getProjectId();
  return api.request({
    url: `/project/${projectId}/store-inventory-transfers/${transferId}`,
    method: 'PUT',
    data
  });
};

/**
 * 处理库存调拨
 * @param {string} transferId - 调拨ID
 * @returns {Promise<Object>} 处理结果
 */
export const processInventoryTransfer = (transferId) => {
  const projectId = getProjectId();
  return api.request({
    url: `/project/${projectId}/store-inventory-transfers/${transferId}/process`,
    method: 'POST'
  });
};

/**
 * 上传调拨表格
 * @param {FormData} formData - 表单数据
 * @returns {Promise<Object>} 上传结果
 */
export const uploadTransferSheet = (formData) => {
  const projectId = getProjectId();
  return api.request({
    url: `/project/${projectId}/store-inventory-transfers/upload`,
    method: 'POST',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
};

const storeInventoryTransferService = {
  getInventoryTransfers,
  getInventoryTransfer,
  createInventoryTransfer,
  updateInventoryTransfer,
  processInventoryTransfer,
  uploadTransferSheet
};

export default storeInventoryTransferService;
