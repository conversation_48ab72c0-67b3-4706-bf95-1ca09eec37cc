#!/usr/bin/env python
# -*- coding: utf-8 -*-

import logging
import uuid
from typing import List, Optional, Dict, Any, Tuple
from datetime import datetime, date
from sqlalchemy import delete, or_, and_, func, desc, asc, text, case, Float
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.orm import joinedload, selectinload

from models.sales_report import SalesReport, SalesReportItem, SalesReportChannel
from models.store import Store
from models.sales_management import SalesChannel, PaymentMethod
from models.user import User
from schemas.sales_report import SalesReportCreate, SalesReportUpdate, SalesReportStatusUpdate, RechargeSaleBase

logger = logging.getLogger(__name__)

class SalesReportService:
    @staticmethod
    async def get_reports(
        db: AsyncSession,
        project_id: uuid.UUID,
        skip: int = 0,
        limit: int = 100,
        store_id: Optional[uuid.UUID] = None,
        status: Optional[str] = None,
        report_type: Optional[str] = None,
        search: Optional[str] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        sort_by: str = "report_date",
        sort_order: str = "desc"
    ) -> List[SalesReport]:
        """获取销售上报列表"""
        query = select(SalesReport).where(SalesReport.project_id == project_id)

        # 应用过滤条件
        if store_id:
            query = query.where(SalesReport.store_id == store_id)
        if status:
            query = query.where(SalesReport.status == status.upper())
        if report_type:
            query = query.where(SalesReport.report_type == report_type.upper())
        if search:
            query = query.where(SalesReport.notes.ilike(f"%{search}%"))
        if start_date:
            query = query.where(SalesReport.report_date >= start_date)
        if end_date:
            query = query.where(SalesReport.report_date <= end_date)

        # 应用排序
        order_column = getattr(SalesReport, sort_by, SalesReport.report_date)
        if sort_order.lower() == "desc":
            query = query.order_by(desc(order_column))
        else:
            query = query.order_by(asc(order_column))

        # 加载关联数据
        query = query.options(
            joinedload(SalesReport.store),
            selectinload(SalesReport.items),
            joinedload(SalesReport.creator),
            joinedload(SalesReport.updater),
            joinedload(SalesReport.approver),
            selectinload(SalesReport.channel_details).joinedload(SalesReportChannel.sales_channel)
        )

        # 应用分页
        query = query.offset(skip).limit(limit)
        
        result = await db.execute(query)
        return list(result.unique().scalars().all())

    @staticmethod
    async def count_reports(
        db: AsyncSession,
        project_id: uuid.UUID,
        store_id: Optional[uuid.UUID] = None,
        status: Optional[str] = None,
        report_type: Optional[str] = None,
        search: Optional[str] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> int:
        """计算销售上报数量"""
        query = select(func.count(SalesReport.id)).where(SalesReport.project_id == project_id)

        if store_id:
            query = query.where(SalesReport.store_id == store_id)
        if status:
            query = query.where(SalesReport.status == status.upper())
        if report_type:
            query = query.where(SalesReport.report_type == report_type.upper())
        if search:
            query = query.where(SalesReport.notes.ilike(f"%{search}%"))
        if start_date:
            query = query.where(SalesReport.report_date >= start_date)
        if end_date:
            query = query.where(SalesReport.report_date <= end_date)

        result = await db.execute(query)
        return result.scalar_one()

    @staticmethod
    async def get_report_by_id(db: AsyncSession, report_id: uuid.UUID) -> Optional[SalesReport]:
        result = await db.execute(select(SalesReport).where(SalesReport.id == report_id))
        return result.scalars().first()

    @staticmethod
    async def get_report_with_details(db: AsyncSession, report_id: uuid.UUID) -> Optional[SalesReport]:
        """获取带有完整详情的单一报告"""
        query = select(SalesReport).where(SalesReport.id == report_id).options(
            joinedload(SalesReport.store),
            selectinload(SalesReport.items),
            joinedload(SalesReport.creator),
            joinedload(SalesReport.updater),
            joinedload(SalesReport.approver),
            selectinload(SalesReport.channel_details).joinedload(SalesReportChannel.sales_channel)
        )
        result = await db.execute(query)
        return result.scalars().first()

    @staticmethod
    async def create_report(
        db: AsyncSession,
        project_id: uuid.UUID,
        user_id: uuid.UUID,
        report_data: SalesReportCreate
    ) -> SalesReport:
        """创建新的销售报告 (已重构)"""
        
        # 验证: 确保同一天内, 班次和日报不能并存
        existing_report_query = select(SalesReport).where(
            SalesReport.store_id == report_data.store_id,
            func.date(SalesReport.report_date) == report_data.report_date
        )
        existing_reports_result = await db.execute(existing_report_query)
        existing_reports = existing_reports_result.scalars().all()

        if existing_reports:
            is_daily_creating = report_data.report_type == 'daily'
            has_shift_report = any(r.report_type == 'shift' for r in existing_reports)
            has_daily_report = any(r.report_type == 'daily' for r in existing_reports)

            if is_daily_creating and has_shift_report:
                raise ValueError("该日期已存在班次上报，无法创建日报。日报数据由班次上报自动汇总。")
            if not is_daily_creating and has_daily_report:
                raise ValueError("该日期已存在日报，无法再创建班次上报。")
            if is_daily_creating and has_daily_report:
                raise ValueError("该日期已存在日报，无法重复创建。")


        # 计算总计
        total_sales = sum(
            pm.amount 
            for cd in report_data.channel_details 
            for pm in cd.payment_methods_details
        )
        total_orders = sum(
            pm.orders 
            for cd in report_data.channel_details 
            for pm in cd.payment_methods_details
        )
        total_recharge_amount = sum(rs.amount for rs in report_data.recharge_sales_data)
        total_recharge_orders = sum(rs.orders for rs in report_data.recharge_sales_data)
        
        # 创建主报告对象
        db_report = SalesReport(
            project_id=project_id,
            store_id=report_data.store_id,
            reporter_id=user_id,
            report_date=report_data.report_date,
            report_type=report_data.report_type,
            notes=report_data.notes,
            status='draft',
            total_sales=total_sales,
            total_orders=total_orders,
            total_recharge_amount=total_recharge_amount,
            total_recharge_orders=total_recharge_orders,
            recharge_sales_data=[rs.model_dump() for rs in report_data.recharge_sales_data]
        )
        db.add(db_report)
        await db.flush()

        # 创建渠道详情
        for channel_detail_data in report_data.channel_details:
            channel_total_sales = sum(pm.amount for pm in channel_detail_data.payment_methods_details)
            channel_total_orders = sum(pm.orders for pm in channel_detail_data.payment_methods_details)

            db_channel_detail = SalesReportChannel(
                sales_report_id=db_report.id,
                sales_channel_id=channel_detail_data.sales_channel_id,
                total_sales=channel_total_sales,
                total_orders=channel_total_orders,
                payment_methods_details=[pm.model_dump() for pm in channel_detail_data.payment_methods_details]
            )
            db.add(db_channel_detail)

        await db.commit()
        await db.refresh(db_report)
        return db_report

    @staticmethod
    async def update_report(
        db: AsyncSession,
        report_id: uuid.UUID,
        user_id: uuid.UUID,
        report_data: SalesReportUpdate
    ) -> Optional[SalesReport]:
        """更新销售报告 (已重构)"""
        db_report = await db.get(SalesReport, report_id)
        if not db_report:
            return None

        update_data = report_data.model_dump(exclude_unset=True)
        
        # 如果更新了渠道或充值数据，需要重新计算总计
        if 'channel_details' in update_data or 'recharge_sales_data' in update_data:
            
            # 使用新数据或现有数据进行计算
            channel_details_to_calc = report_data.channel_details if 'channel_details' in update_data else (await db.execute(
                select(SalesReportChannel).where(SalesReportChannel.sales_report_id == report_id)
            )).scalars().all()
            
            recharge_data_to_calc = report_data.recharge_sales_data if 'recharge_sales_data' in update_data else db_report.recharge_sales_data

            # 将ORM模型或字典转换为Pydantic模型以便于统一处理
            from schemas.sales_report import ChannelDetailCreate, RechargeSaleCreate
            
            channel_details_for_calc = [
                ChannelDetailCreate.model_validate(cd, from_attributes=True) if isinstance(cd, SalesReportChannel) else cd 
                for cd in channel_details_to_calc
            ]
            
            recharge_data_for_calc = [
                RechargeSaleCreate.model_validate(rs) if isinstance(rs, dict) else rs
                for rs in recharge_data_to_calc
            ]

            db_report.total_sales = sum(pm.amount for cd in channel_details_for_calc for pm in cd.payment_methods_details)
            db_report.total_orders = sum(pm.orders for cd in channel_details_for_calc for pm in cd.payment_methods_details)
            db_report.total_recharge_amount = sum(rs.amount for rs in recharge_data_for_calc)
            db_report.total_recharge_orders = sum(rs.orders for rs in recharge_data_for_calc)

            if 'channel_details' in update_data:
                # 删除旧的渠道详情
                await db.execute(delete(SalesReportChannel).where(SalesReportChannel.sales_report_id == report_id))
                await db.flush()
                # 添加新的渠道详情
                for cd_data in report_data.channel_details:
                    channel_total_sales = sum(pm.amount for pm in cd_data.payment_methods_details)
                    channel_total_orders = sum(pm.orders for pm in cd_data.payment_methods_details)
                    db.add(SalesReportChannel(
                        sales_report_id=report_id,
                        sales_channel_id=cd_data.sales_channel_id,
                        total_sales=channel_total_sales,
                        total_orders=channel_total_orders,
                        payment_methods_details=[pm.model_dump() for pm in cd_data.payment_methods_details]
                    ))

            if 'recharge_sales_data' in update_data:
                 db_report.recharge_sales_data = [rs.model_dump() for rs in report_data.recharge_sales_data]


        # 更新其他字段
        if report_data.report_date:
            db_report.report_date = report_data.report_date
        if report_data.report_type:
            db_report.report_type = report_data.report_type
        if report_data.notes is not None:
            db_report.notes = report_data.notes
        
        db_report.updated_by = user_id

        await db.commit()
        await db.refresh(db_report)
        return db_report

    @staticmethod
    async def update_report_status(db: AsyncSession, report_id: uuid.UUID, user_id: uuid.UUID, status_data: SalesReportStatusUpdate) -> Optional[SalesReport]:
        """更新销售报告状态"""
        db_report = await db.get(SalesReport, report_id)
        if not db_report:
            return None

        db_report.status = status_data.status
        db_report.updated_by = user_id
        db_report.updated_at = datetime.utcnow()

        await db.commit()
        await db.refresh(db_report)
        return db_report

    @staticmethod
    def _process_recharge_sales(
        recharge_sales: Optional[List[RechargeSaleBase]]
    ) -> Tuple[float, float, int, int, Dict[str, float]]:
        """处理充值/售卡数据，返回各项总额和支付方式"""
        if not recharge_sales:
            return 0.0, 0.0, 0, 0, {}

        recharge_amount = 0.0
        card_sales_amount = 0.0
        recharge_count = 0
        card_sales_count = 0
        payment_methods = {}

        for sale in recharge_sales:
            if sale.type == "recharge":
                recharge_amount += sale.amount
                recharge_count += sale.count
            elif sale.type == "card":
                card_sales_amount += sale.amount
                card_sales_count += sale.count
            
            if sale.payment_methods:
                for pm in sale.payment_methods:
                    method_key = pm.get('method') if isinstance(pm, dict) else getattr(pm, 'method', 'unknown')
                    method_amount = pm.get('amount') if isinstance(pm, dict) else getattr(pm, 'amount', 0)
                    payment_methods[method_key] = payment_methods.get(method_key, 0) + method_amount
        
        return recharge_amount, card_sales_amount, recharge_count, card_sales_count, payment_methods

    @staticmethod
    async def aggregate_daily_report(db: AsyncSession, project_id: uuid.UUID, store_id: uuid.UUID, report_date: date, user_id: uuid.UUID):
        """根据当天的所有班次报告，创建或更新日报"""
        start_of_day = datetime.combine(report_date, datetime.min.time())
        end_of_day = datetime.combine(report_date, datetime.max.time())

        # 查找当天的所有班次报告
        stmt = select(SalesReport).where(
            SalesReport.project_id == project_id,
            SalesReport.store_id == store_id,
            SalesReport.report_date >= start_of_day,
            SalesReport.report_date <= end_of_day,
            SalesReport.report_type == 'shift'
        )
        result = await db.execute(stmt)
        shift_reports = result.scalars().all()

        # 查找是否已存在日报
        daily_report_stmt = select(SalesReport).where(
            SalesReport.project_id == project_id,
            SalesReport.store_id == store_id,
            func.date(SalesReport.report_date) == report_date,
            SalesReport.report_type == 'daily'
        )
        existing_daily_report = (await db.execute(daily_report_stmt)).scalars().first()

        if not shift_reports:
            # 如果没有班次报告了，但日报还存在，则删除日报
            if existing_daily_report:
                await db.delete(existing_daily_report)
                await db.commit()
            return

        # 初始化汇总数据
        total_sales, total_orders, total_customers, online_sales, offline_sales = 0, 0, 0, 0, 0
        recharge_amount, card_sales_amount, recharge_count, card_sales_count = 0, 0, 0, 0
        payment_methods, recharge_payment_methods = {}, {}
        
        for report in shift_reports:
            total_sales += report.total_sales
            total_orders += report.total_orders
            total_customers += report.total_customers
            online_sales += report.online_sales
            offline_sales += report.offline_sales
            recharge_amount += report.recharge_amount
            card_sales_amount += report.card_sales_amount
            recharge_count += report.recharge_count
            card_sales_count += report.card_sales_count
            
            for method, amount in (report.payment_methods or {}).items():
                payment_methods[method] = payment_methods.get(method, 0) + amount
            for method, amount in (report.recharge_payment_methods or {}).items():
                recharge_payment_methods[method] = recharge_payment_methods.get(method, 0) + amount

        if existing_daily_report:
            # 更新现有日报
            daily_report = existing_daily_report
            daily_report.updated_by = user_id
            daily_report.updated_at = datetime.utcnow()
        else:
            # 创建新日报
            daily_report = SalesReport(
                project_id=project_id,
                store_id=store_id,
                report_date=start_of_day, # 使用日期开始时间
                report_type='daily',
                status='submitted',
                created_by=user_id,
                updated_by=user_id,
            )
            db.add(daily_report)
        
        # 填充数据
        daily_report.total_sales = total_sales
        daily_report.total_orders = total_orders
        daily_report.total_customers = total_customers
        daily_report.online_sales = online_sales
        daily_report.offline_sales = offline_sales
        daily_report.payment_methods = payment_methods
        daily_report.recharge_amount = recharge_amount
        daily_report.card_sales_amount = card_sales_amount
        daily_report.recharge_count = recharge_count
        daily_report.card_sales_count = card_sales_count
        daily_report.recharge_payment_methods = recharge_payment_methods
        
        await db.commit()

    @staticmethod
    async def get_sales_statistics(
        db: AsyncSession,
        project_id: uuid.UUID,
        start_date: datetime,
        end_date: datetime,
        store_id: Optional[uuid.UUID] = None,
        group_by: str = "month",
        status: Optional[str] = None,
        data_type: str = "sales"
    ) -> List[Dict[str, Any]]:
        """获取销售统计数据，可按销售额或储值额统计"""
        
        # 确定统计的字段
        if data_type == "recharge":
            amount_column = SalesReport.total_recharge_amount
        else: # 默认为 sales
            amount_column = SalesReport.total_sales

        # 格式化日期以进行分组
        if group_by == 'day':
            date_format_str = 'YYYY-MM-DD'
        else: # 默认为 month
            date_format_str = 'YYYY-MM'

        group_by_expression = func.to_char(SalesReport.report_date, date_format_str)

        # 基础查询
        query = select(
            group_by_expression.label("group_key"),
            func.sum(amount_column).label("total_amount"),
            func.avg(amount_column).label("average_amount"),
            func.count(SalesReport.id).label("report_count")
        ).where(
            SalesReport.project_id == project_id,
            SalesReport.report_date.between(start_date, end_date)
        )

        if store_id:
            query = query.where(SalesReport.store_id == store_id)
        
        # 根据状态过滤
        if status:
            query = query.where(SalesReport.status == status.upper())
        else:
            # 默认只统计已发布的
            query = query.where(SalesReport.status == 'PUBLISHED')
        
        query = query.group_by("group_key").order_by("group_key")

        result = await db.execute(query)
        return [row._asdict() for row in result.all()]
