#!/usr/bin/env python
# -*- coding: utf-8 -*-

import uuid
from datetime import datetime
from typing import List, Optional
from sqlalchemy import Column, String, Float, Integer, Boolean, ForeignKey, DateTime, Text, Enum
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from models.core import Base

class PurchaseOrder(Base):
    """采购订单模型"""
    __tablename__ = "purchase_orders"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id", ondelete="SET NULL"), nullable=True, index=True, comment="租户ID")
    order_number = Column(String(50), nullable=False, index=True, comment="订单编号")
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id", ondelete="CASCADE"), nullable=False, index=True, comment="项目ID")
    warehouse_id = Column(UUID(as_uuid=True), ForeignKey("warehouses.id", ondelete="SET NULL"), nullable=True, index=True, comment="仓库ID")
    
    order_date = Column(DateTime, nullable=False, default=datetime.utcnow, comment="订单日期")
    expected_delivery_date = Column(DateTime, nullable=True, comment="预计交货日期")
    actual_delivery_date = Column(DateTime, nullable=True, comment="实际交货日期")
    
    status = Column(
        Enum("draft", "confirmed", "received", "cancelled", name="purchase_order_status"),
        nullable=False,
        default="draft",
        comment="订单状态: draft-草稿, confirmed-已确认, received-已收货, cancelled-已取消"
    )
    
    total_amount = Column(Float, nullable=False, default=0.0, comment="订单总金额")
    discount_amount = Column(Float, nullable=False, default=0.0, comment="折扣金额")
    tax_amount = Column(Float, nullable=False, default=0.0, comment="税额")
    shipping_fee = Column(Float, nullable=False, default=0.0, comment="运费")
    final_amount = Column(Float, nullable=False, default=0.0, comment="最终金额")
    
    payment_status = Column(
        Enum("unpaid", "partial", "paid", name="payment_status"),
        nullable=False,
        default="unpaid",
        comment="支付状态: unpaid-未支付, partial-部分支付, paid-已支付"
    )
    payment_method = Column(String(50), nullable=True, comment="支付方式")
    payment_date = Column(DateTime, nullable=True, comment="支付日期")
    
    notes = Column(Text, nullable=True, comment="备注")
    
    created_by = Column(UUID(as_uuid=True), ForeignKey("users.id", ondelete="SET NULL"), nullable=True, comment="创建人ID")
    updated_by = Column(UUID(as_uuid=True), ForeignKey("users.id", ondelete="SET NULL"), nullable=True, comment="更新人ID")
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")
    
    # 关系
    tenant = relationship("Tenant", back_populates="purchase_orders")
    project = relationship("Project", back_populates="purchase_orders")
    warehouse = relationship("Warehouse", back_populates="purchase_orders")
    creator = relationship("User", foreign_keys=[created_by])
    updater = relationship("User", foreign_keys=[updated_by])
    items = relationship("PurchaseOrderItem", back_populates="purchase_order", cascade="all, delete-orphan")


class PurchaseOrderItem(Base):
    """采购订单项模型"""
    __tablename__ = "purchase_order_items"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id", ondelete="SET NULL"), nullable=True, index=True, comment="租户ID")
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id", ondelete="SET NULL"), nullable=True, index=True, comment="项目ID")
    purchase_order_id = Column(UUID(as_uuid=True), ForeignKey("purchase_orders.id", ondelete="SET NULL"), nullable=True, index=True, comment="采购订单ID")
    product_id = Column(UUID(as_uuid=True), ForeignKey("products.id", ondelete="SET NULL"), nullable=True, index=True, comment="产品ID")
    supplier_id = Column(UUID(as_uuid=True), ForeignKey("suppliers.id", ondelete="SET NULL"), nullable=True, index=True, comment="供应商ID")
    category_id = Column(UUID(as_uuid=True), ForeignKey("product_categories.id", ondelete="SET NULL"), nullable=True, index=True, comment="商品分类ID")
    
    product_name = Column(String(255), nullable=False, comment="产品名称")
    product_code = Column(String(50), nullable=True, comment="产品编码")
    product_unit = Column(String(20), nullable=True, comment="产品单位")
    product_specification = Column(String(255), nullable=True, comment="产品规格")
    
    quantity = Column(Float, nullable=False, default=0.0, comment="数量")
    unit_price = Column(Float, nullable=False, default=0.0, comment="单价")
    discount_rate = Column(Float, nullable=False, default=0.0, comment="折扣率")
    tax_rate = Column(Float, nullable=False, default=0.0, comment="税率")
    total_amount = Column(Float, nullable=False, default=0.0, comment="总金额")
    
    received_quantity = Column(Float, nullable=False, default=0.0, comment="已收货数量")
    
    notes = Column(Text, nullable=True, comment="备注")
    
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")
    
    # 关系
    tenant = relationship("Tenant", back_populates="purchase_order_items")
    project = relationship("Project", back_populates="purchase_order_items")
    purchase_order = relationship("PurchaseOrder", back_populates="items")
    product = relationship("Product")
    supplier = relationship("Supplier", back_populates="purchase_order_items")
    category = relationship("ProductCategory")
