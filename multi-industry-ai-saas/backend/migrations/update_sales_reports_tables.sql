-- Update sales_reports table
DO $$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM pg_attribute WHERE attrelid = 'sales_reports'::regclass AND attname = 'reporter_id') THEN
        ALTER TABLE sales_reports ADD COLUMN reporter_id UUID REFERENCES users(id);
        COMMENT ON COLUMN sales_reports.reporter_id IS '上报人ID';
    END IF;

    -- Add status enum type if not exists
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'sales_report_status') THEN
        CREATE TYPE sales_report_status AS ENUM ('DRAFT', 'PUBLISHED', 'ARCHIVED');
    END IF;

    IF NOT EXISTS(SELECT 1 FROM pg_attribute WHERE attrelid = 'sales_reports'::regclass AND attname = 'status') THEN
        ALTER TABLE sales_reports ADD COLUMN status sales_report_status NOT NULL DEFAULT 'DRAFT';
        COMMENT ON COLUMN sales_reports.status IS '报告状态';
    END IF;

    IF NOT EXISTS(SELECT 1 FROM pg_attribute WHERE attrelid = 'sales_reports'::regclass AND attname = 'online_sales') THEN
        ALTER TABLE sales_reports ADD COLUMN online_sales REAL NOT NULL DEFAULT 0.0;
        COMMENT ON COLUMN sales_reports.online_sales IS '线上销售额';
    END IF;

    IF NOT EXISTS(SELECT 1 FROM pg_attribute WHERE attrelid = 'sales_reports'::regclass AND attname = 'offline_sales') THEN
        ALTER TABLE sales_reports ADD COLUMN offline_sales REAL NOT NULL DEFAULT 0.0;
        COMMENT ON COLUMN sales_reports.offline_sales IS '线下销售额';
    END IF;

    IF NOT EXISTS(SELECT 1 FROM pg_attribute WHERE attrelid = 'sales_reports'::regclass AND attname = 'payment_methods_data') THEN
        ALTER TABLE sales_reports ADD COLUMN payment_methods_data JSONB DEFAULT '[]'::jsonb;
        COMMENT ON COLUMN sales_reports.payment_methods_data IS '支付方式统计';
    END IF;
    
    IF NOT EXISTS(SELECT 1 FROM pg_attribute WHERE attrelid = 'sales_reports'::regclass AND attname = 'recharge_sales_data') THEN
        ALTER TABLE sales_reports ADD COLUMN recharge_sales_data JSONB DEFAULT '[]'::jsonb;
        COMMENT ON COLUMN sales_reports.recharge_sales_data IS '充值/售卡支付方式统计';
    END IF;

    IF NOT EXISTS(SELECT 1 FROM pg_attribute WHERE attrelid = 'sales_reports'::regclass AND attname = 'recharge_amount') THEN
        ALTER TABLE sales_reports ADD COLUMN recharge_amount REAL NOT NULL DEFAULT 0.0;
        COMMENT ON COLUMN sales_reports.recharge_amount IS '会员充值金额';
    END IF;

    IF NOT EXISTS(SELECT 1 FROM pg_attribute WHERE attrelid = 'sales_reports'::regclass AND attname = 'card_sales_amount') THEN
        ALTER TABLE sales_reports ADD COLUMN card_sales_amount REAL NOT NULL DEFAULT 0.0;
        COMMENT ON COLUMN sales_reports.card_sales_amount IS '储值卡销售金额';
    END IF;

    IF NOT EXISTS(SELECT 1 FROM pg_attribute WHERE attrelid = 'sales_reports'::regclass AND attname = 'recharge_count') THEN
        ALTER TABLE sales_reports ADD COLUMN recharge_count INTEGER NOT NULL DEFAULT 0;
        COMMENT ON COLUMN sales_reports.recharge_count IS '会员充值笔数';
    END IF;

    IF NOT EXISTS(SELECT 1 FROM pg_attribute WHERE attrelid = 'sales_reports'::regclass AND attname = 'card_sales_count') THEN
        ALTER TABLE sales_reports ADD COLUMN card_sales_count INTEGER NOT NULL DEFAULT 0;
        COMMENT ON COLUMN sales_reports.card_sales_count IS '储值卡销售数量';
    END IF;

    IF NOT EXISTS(SELECT 1 FROM pg_attribute WHERE attrelid = 'sales_reports'::regclass AND attname = 'reject_reason') THEN
        ALTER TABLE sales_reports ADD COLUMN reject_reason TEXT;
        COMMENT ON COLUMN sales_reports.reject_reason IS '拒绝原因';
    END IF;

    IF NOT EXISTS(SELECT 1 FROM pg_attribute WHERE attrelid = 'sales_reports'::regclass AND attname = 'approved_by') THEN
        ALTER TABLE sales_reports ADD COLUMN approved_by UUID REFERENCES users(id) ON DELETE SET NULL;
        COMMENT ON COLUMN sales_reports.approved_by IS '审核人ID';
    END IF;

    IF NOT EXISTS(SELECT 1 FROM pg_attribute WHERE attrelid = 'sales_reports'::regclass AND attname = 'approved_at') THEN
        ALTER TABLE sales_reports ADD COLUMN approved_at TIMESTAMP;
        COMMENT ON COLUMN sales_reports.approved_at IS '审核时间';
    END IF;

END $$;

-- Update sales_report_channels table
DO $$
BEGIN
    IF NOT EXISTS(SELECT 1 FROM pg_attribute WHERE attrelid = 'sales_report_channels'::regclass AND attname = 'total_customers') THEN
        ALTER TABLE sales_report_channels ADD COLUMN total_customers INTEGER NOT NULL DEFAULT 0;
        COMMENT ON COLUMN sales_report_channels.total_customers IS '该渠道顾客总数';
    END IF;
    
    IF NOT EXISTS(SELECT 1 FROM pg_attribute WHERE attrelid = 'sales_report_channels'::regclass AND attname = 'payment_methods_details') THEN
        ALTER TABLE sales_report_channels ADD COLUMN payment_methods_details JSONB;
        COMMENT ON COLUMN sales_report_channels.payment_methods_details IS '该渠道的支付方式明细';
    END IF;
END $$; 