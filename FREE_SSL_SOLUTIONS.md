# 免费SSL证书解决方案

## 问题描述
Cloudflare的Total TLS功能在某些情况下需要付费方案，我们需要免费的替代方案来解决通配符子域名的SSL证书问题。

## 解决方案概览

### 方案1：使用HTTP连接（推荐，立即可用）
### 方案2：Let's Encrypt通配符证书 + Nginx反向代理
### 方案3：Cloudflare Origin CA证书
### 方案4：修改架构使用路径而非子域名

---

## 方案1：使用HTTP连接（推荐）

### 优势
- ✅ 立即可用，无需等待
- ✅ 完全免费
- ✅ 无需修改现有架构
- ✅ 适合API服务（MCP协议）

### 实施方法

客户端配置使用HTTP而非HTTPS：

```json
{
  "mcpServers": {
    "my-mcp-server": {
      "command": "npx",
      "args": [
        "@modelcontextprotocol/server-fetch",
        "http://mcp-38be5e4a-7d0c-4ca7-ad50-bc69bd06e20d.saas.houshanai.com/mcp"
      ]
    }
  }
}
```

### 安全考虑
- MCP协议本身包含认证机制（Bearer Token）
- 对于API服务，HTTP通常是可接受的
- 如果需要加密，可以在应用层实现

---

## 方案2：Let's Encrypt通配符证书 + Nginx反向代理

### 概述
在服务器上使用Let's Encrypt生成通配符证书，然后配置Nginx反向代理。

### 实施步骤

#### 1. 安装Certbot和Cloudflare插件

```bash
# Ubuntu/Debian
sudo apt update
sudo apt install certbot python3-certbot-dns-cloudflare

# CentOS/RHEL
sudo yum install certbot python3-certbot-dns-cloudflare
```

#### 2. 配置Cloudflare API凭据

```bash
# 创建凭据文件
sudo mkdir -p /etc/letsencrypt
sudo nano /etc/letsencrypt/cloudflare.ini
```

添加内容：
```ini
dns_cloudflare_email = <EMAIL>
dns_cloudflare_api_key = your-global-api-key
```

设置权限：
```bash
sudo chmod 600 /etc/letsencrypt/cloudflare.ini
```

#### 3. 获取通配符证书

```bash
sudo certbot certonly \
  --dns-cloudflare \
  --dns-cloudflare-credentials /etc/letsencrypt/cloudflare.ini \
  -d "*.saas.houshanai.com" \
  -d "saas.houshanai.com"
```

#### 4. 配置Nginx反向代理

```nginx
# /etc/nginx/sites-available/mcp-wildcard
server {
    listen 443 ssl http2;
    server_name *.saas.houshanai.com;
    
    ssl_certificate /etc/letsencrypt/live/saas.houshanai.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/saas.houshanai.com/privkey.pem;
    
    # 提取server_id从域名
    if ($host ~ ^mcp-([^.]+)\.saas\.houshanai\.com$) {
        set $server_id $1;
    }
    
    location /mcp {
        # 代理到对应的MCP容器
        proxy_pass http://localhost:$upstream_port;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # SSE支持
        proxy_buffering off;
        proxy_cache off;
        proxy_set_header Connection '';
        proxy_http_version 1.1;
        chunked_transfer_encoding off;
    }
}
```

#### 5. 自动续期

```bash
# 添加到crontab
sudo crontab -e

# 添加这行（每天2:30检查续期）
30 2 * * * certbot renew --quiet --post-hook "systemctl reload nginx"
```

### 优势
- ✅ 完全免费
- ✅ 真正的SSL证书
- ✅ 自动续期

### 劣势
- ❌ 需要服务器配置
- ❌ 需要Cloudflare API密钥
- ❌ 复杂度较高

---

## 方案3：Cloudflare Origin CA证书

### 概述
使用Cloudflare的Origin CA为服务器生成证书，然后启用"Full"SSL模式。

### 实施步骤

#### 1. 生成Origin CA证书

1. 登录Cloudflare控制台
2. 进入 SSL/TLS → Origin Server
3. 点击"Create Certificate"
4. 选择"Let Cloudflare generate a private key and a CSR"
5. 在Hostnames中添加：
   - `*.saas.houshanai.com`
   - `saas.houshanai.com`
6. 选择证书有效期（最长15年）
7. 点击"Create"

#### 2. 保存证书文件

```bash
# 保存证书
sudo nano /etc/ssl/certs/cloudflare-origin.pem
# 粘贴Origin Certificate内容

# 保存私钥
sudo nano /etc/ssl/private/cloudflare-origin.key
# 粘贴Private Key内容

# 设置权限
sudo chmod 644 /etc/ssl/certs/cloudflare-origin.pem
sudo chmod 600 /etc/ssl/private/cloudflare-origin.key
```

#### 3. 配置Nginx使用Origin证书

```nginx
server {
    listen 443 ssl http2;
    server_name *.saas.houshanai.com;
    
    ssl_certificate /etc/ssl/certs/cloudflare-origin.pem;
    ssl_certificate_key /etc/ssl/private/cloudflare-origin.key;
    
    # 其他配置...
}
```

#### 4. 设置Cloudflare SSL模式

在Cloudflare控制台中：
1. 进入 SSL/TLS → Overview
2. 将SSL/TLS encryption mode设置为"Full"

### 优势
- ✅ 完全免费
- ✅ 长期有效（最长15年）
- ✅ 支持通配符

### 劣势
- ❌ 只在Cloudflare代理模式下工作
- ❌ 需要启用橙色云朵（代理模式）
- ❌ 可能影响性能

---

## 方案4：修改架构使用路径

### 概述
将 `mcp-{server_id}.saas.houshanai.com/mcp` 改为 `saas.houshanai.com/mcp/{server_id}`

### 实施步骤

#### 1. 修改Traefik路由配置

```yaml
# traefik/dynamic/mcp-routing.yml
http:
  routers:
    mcp-path-router:
      rule: "PathPrefix(`/mcp/`)"
      service: mcp-dynamic-service
      middlewares:
        - mcp-auth@file
        - mcp-path-extractor@file
      priority: 100

  middlewares:
    mcp-path-extractor:
      replacePathRegex:
        regex: "^/mcp/([^/]+)(.*)$"
        replacement: "/mcp$2"
      headers:
        customRequestHeaders:
          X-MCP-Server-ID: "{1}"

  services:
    mcp-dynamic-service:
      loadBalancer:
        servers:
          - url: "http://localhost:5000"  # 默认后端
```

#### 2. 修改MCP服务器适配器

```python
# 在mcp_server_adapter.py中修改容器标签
traefik_labels = {
    "traefik.enable": "true",
    f"traefik.http.routers.mcp-{server_id}.rule": f"PathPrefix(`/mcp/{server_id}`)",
    f"traefik.http.routers.mcp-{server_id}.priority": "200",
    f"traefik.http.services.mcp-{server_id}.loadbalancer.server.port": str(port),
    f"traefik.http.routers.mcp-{server_id}.middlewares": "mcp-auth@file"
}
```

#### 3. 客户端使用新URL

```json
{
  "mcpServers": {
    "my-mcp-server": {
      "command": "npx",
      "args": [
        "@modelcontextprotocol/server-fetch",
        "https://saas.houshanai.com/mcp/38be5e4a-7d0c-4ca7-ad50-bc69bd06e20d"
      ]
    }
  }
}
```

### 优势
- ✅ 使用单一域名，SSL证书简单
- ✅ 无需通配符证书
- ✅ 可以使用Cloudflare免费SSL

### 劣势
- ❌ 需要修改现有架构
- ❌ 需要重新部署所有服务
- ❌ 影响现有客户端

---

## 推荐方案

### 立即解决（推荐）：方案1 - HTTP连接
- 最简单、最快速
- 无需任何配置更改
- 适合API服务

### 长期解决：方案2 - Let's Encrypt + Nginx
- 提供真正的SSL加密
- 完全免费
- 专业解决方案

### 如果需要重构：方案4 - 路径架构
- 最彻底的解决方案
- 简化SSL证书管理
- 适合新项目或大规模重构

## 实施建议

1. **立即使用方案1**解决当前问题
2. **并行实施方案2**作为长期解决方案
3. **考虑方案4**用于未来的架构优化

这样可以确保服务的连续性，同时逐步改善SSL支持。 