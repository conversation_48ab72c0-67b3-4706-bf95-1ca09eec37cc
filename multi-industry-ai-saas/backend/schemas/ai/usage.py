#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
AI 使用记录 Schema
"""

from typing import Optional, List, Dict, Any
from datetime import datetime
import uuid
from pydantic import BaseModel, Field, validator

class AIUsageBase(BaseModel):
    """AI 使用记录基础 Schema"""
    tenant_id: uuid.UUID = Field(..., description="租户 ID")
    project_id: uuid.UUID = Field(..., description="项目 ID")
    user_id: uuid.UUID = Field(..., description="用户 ID")
    config_id: Optional[uuid.UUID] = Field(None, description="配置 ID")
    model_id: Optional[uuid.UUID] = Field(None, description="模型 ID")
    request_type: str = Field(..., description="请求类型：chat, vision, embedding, audio")
    prompt_tokens: int = Field(0, description="提示 token 数")
    completion_tokens: int = Field(0, description="完成 token 数")
    total_tokens: int = Field(0, description="总 token 数")
    cost: float = Field(0, description="成本（美元）")
    duration_ms: int = Field(0, description="请求耗时（毫秒）")
    status: str = Field("success", description="状态：success, failed")
    error_message: Optional[str] = Field(None, description="错误信息")
    request_metadata: Optional[Dict[str, Any]] = Field(None, description="请求元数据")

class AIUsageCreate(AIUsageBase):
    """创建 AI 使用记录 Schema"""
    pass

class AIUsageInDB(AIUsageBase):
    """数据库中的 AI 使用记录 Schema"""
    id: uuid.UUID
    created_at: datetime

    # 关联数据
    user_name: Optional[str] = None
    model_name: Optional[str] = None
    config_name: Optional[str] = None

    class Config:
        model_config = {"from_attributes": True}

    # 兼容旧版本
    @property
    def metadata(self) -> Optional[Dict[str, Any]]:
        return self.request_metadata

class AIUsageResponse(BaseModel):
    """AI 使用记录响应 Schema"""
    success: bool = True
    message: Optional[str] = None
    data: Optional[AIUsageInDB] = None

class AIUsageListResponse(BaseModel):
    """AI 使用记录列表响应 Schema"""
    success: bool = True
    message: Optional[str] = None
    data: List[AIUsageInDB] = []
    total: int = 0
    page: int = 1
    page_size: int = 20

class AIUsageStatistics(BaseModel):
    """AI 使用统计 Schema"""
    total_requests: int = 0
    total_tokens: int = 0
    total_cost: float = 0
    success_rate: float = 0
    avg_duration_ms: float = 0
    by_model: Dict[str, Dict[str, Any]] = {}
    by_request_type: Dict[str, Dict[str, Any]] = {}
    by_date: Dict[str, Dict[str, Any]] = {}

class AIUsageSummary(BaseModel):
    """AI 使用摘要统计 Schema"""
    total_tokens: int = 0
    total_cost: float = 0
    current_month_tokens: int = 0
    current_month_cost: float = 0
    total_conversations: int = 0
    current_month_conversations: int = 0
    active_assistants: int = 0
    total_assistants: int = 0
    total_requests: int = 0
    model_stats: List[Dict[str, Any]] = []

class AIUsageStatisticsResponse(BaseModel):
    """AI 使用统计响应 Schema"""
    success: bool = True
    message: Optional[str] = None
    data: AIUsageStatistics

class AIUsageSummaryResponse(BaseModel):
    """AI 使用摘要响应 Schema"""
    success: bool = True
    message: Optional[str] = None
    data: AIUsageSummary
