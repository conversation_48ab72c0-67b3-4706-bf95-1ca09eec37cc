import React, { useState, useEffect } from 'react';
import { Card, Row, Col, DatePicker, Select, Button, Table, Statistic, Spin, Tabs, Space } from 'antd';
import { BarChartOutlined, LineChartOutlined, PieChartOutlined, DownloadOutlined } from '@ant-design/icons';
import { Line, Bar, Pie } from '@ant-design/plots';
import dayjs from "dayjs";
import apiService from '../../../../services/api';
import { logOperation } from '../../../../utils/logUtils';

const { RangePicker } = DatePicker;
const { Option } = Select;
const { TabPane } = Tabs;

/**
 * 储值分析组件
 */
const RechargeAnalysis = () => {
  // 状态
  const [loading, setLoading] = useState(false);
  const [stores, setStores] = useState([]);
  const [selectedStoreId, setSelectedStoreId] = useState(null);
  const [dateRange, setDateRange] = useState([dayjs().subtract(30, 'days'), dayjs()]);
  const [timeGranularity, setTimeGranularity] = useState('day');
  const [rechargeData, setRechargeData] = useState([]);
  const [cardData, setCardData] = useState([]);
  const [paymentData, setPaymentData] = useState([]);
  const [memberLevelData, setMemberLevelData] = useState([]);
  const [cardTypeData, setCardTypeData] = useState([]);

  // 初始化
  useEffect(() => {
    fetchStores();
  }, []);

  // 监听筛选条件变化
  useEffect(() => {
    if (dateRange && dateRange.length === 2) {
      fetchRechargeData();
    }
  }, [selectedStoreId, dateRange, timeGranularity]);

  // 获取门店列表
  const fetchStores = async () => {
    try {
      setLoading(true);
      const response = await apiService.project.store.getList();
      if (response && response.items) {
        setStores(response.items);
      }
    } catch (error) {
      console.error('获取门店列表失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 获取储值数据
  const fetchRechargeData = async () => {
    try {
      setLoading(true);
      const startDate = dateRange[0].format('YYYY-MM-DD');
      const endDate = dateRange[1].format('YYYY-MM-DD');

      // 使用真实API获取数据
      const response = await apiService.project.finance.getRechargeAnalysis({
        start_date: startDate,
        end_date: endDate,
        store_id: selectedStoreId,
        group_by: timeGranularity
      });

      if (response && response.items) {
        // 处理真实数据
        const processedData = processRechargeData(response.items, timeGranularity);
        setRechargeData(processedData.rechargeData);
        setCardData(processedData.cardData);
        setPaymentData(processedData.paymentData);
        setMemberLevelData(processedData.memberLevelData);
        setCardTypeData(processedData.cardTypeData);

        // 记录操作日志
        logOperation({
          module: 'finance',
          action: 'query',
          resourceType: 'recharge_analysis',
          description: `查询储值分析数据，时间范围：${startDate}至${endDate}，时间粒度：${timeGranularity}${selectedStoreId ? '，指定门店' : '，所有门店'}`,
          details: {
            start_date: startDate,
            end_date: endDate,
            store_id: selectedStoreId,
            group_by: timeGranularity
          }
        });
      } else {
        // 如果没有数据，设置空数组
        setRechargeData([]);
        setCardData([]);
        setPaymentData([]);
        setMemberLevelData([]);
        setCardTypeData([]);
      }
    } catch (error) {
      console.error('获取储值数据失败:', error);
      // 出错时设置空数组
      setRechargeData([]);
      setCardData([]);
      setPaymentData([]);
      setMemberLevelData([]);
      setCardTypeData([]);
    } finally {
      setLoading(false);
    }
  };

  // 处理真实充值数据
  const processRechargeData = (items, granularity) => {
    if (!items || items.length === 0) {
      return {
        rechargeData: [],
        cardData: [],
        paymentData: [],
        memberLevelData: [],
        cardTypeData: []
      };
    }

    // 处理充值趋势数据
    const rechargeData = items.map(item => ({
      date: item.date || item.month || item.week,
      recharge_amount: item.recharge_amount || 0,
      recharge_count: item.recharge_count || 0,
      card_sales_amount: item.card_sales_amount || 0,
      card_sales_count: item.card_sales_count || 0,
      total_amount: (item.recharge_amount || 0) + (item.card_sales_amount || 0)
    }));

    // 计算总充值和售卡金额
    const totalRecharge = rechargeData.reduce((sum, item) => sum + item.recharge_amount, 0);
    const totalCardSales = rechargeData.reduce((sum, item) => sum + item.card_sales_amount, 0);

    // 处理支付方式数据
    let paymentData = [];
    if (items[0] && items[0].payment_methods) {
      paymentData = Object.entries(items[0].payment_methods).map(([method_name, amount]) => ({
        method: method_name.toLowerCase().replace(/\s+/g, '_'),
        method_name,
        amount: parseFloat(amount) || 0
      })).filter(item => item.amount > 0);
    } else {
      // 如果没有真实数据，提供一些基本的支付方式
      paymentData = [
        { method: 'wechat', method_name: '微信支付', amount: 0 },
        { method: 'alipay', method_name: '支付宝', amount: 0 },
        { method: 'cash', method_name: '现金', amount: 0 },
        { method: 'card', method_name: '银行卡', amount: 0 },
      ];
    }

    // 处理会员等级数据
    let memberLevelData = [];
    if (items[0] && items[0].member_levels) {
      memberLevelData = Object.entries(items[0].member_levels).map(([level, data]) => ({
        level,
        name: data.name || level,
        amount: parseFloat(data.amount) || 0,
        count: parseInt(data.count) || 0
      })).filter(item => item.amount > 0 || item.count > 0);
    } else {
      // 如果没有真实数据，提供一些基本的会员等级
      memberLevelData = [
        { level: 'normal', name: '普通会员', amount: 0, count: 0 },
        { level: 'silver', name: '银卡会员', amount: 0, count: 0 },
        { level: 'gold', name: '金卡会员', amount: 0, count: 0 },
        { level: 'platinum', name: '白金会员', amount: 0, count: 0 },
      ];
    }

    // 处理储值卡类型数据
    let cardTypeData = [];
    if (items[0] && items[0].card_types) {
      cardTypeData = Object.entries(items[0].card_types).map(([type, data]) => ({
        type,
        name: data.name || type,
        amount: parseFloat(data.amount) || 0,
        count: parseInt(data.count) || 0
      })).filter(item => item.amount > 0 || item.count > 0);
    } else {
      // 如果没有真实数据，提供一些基本的储值卡类型
      cardTypeData = [
        { type: 'gift', name: '礼品卡', amount: 0, count: 0 },
        { type: 'discount', name: '折扣卡', amount: 0, count: 0 },
        { type: 'prepaid', name: '预付卡', amount: 0, count: 0 },
        { type: 'special', name: '特惠卡', amount: 0, count: 0 },
      ];
    }

    // 生成储值类型数据
    const cardData = [
      { type: 'recharge', name: '会员充值', amount: totalRecharge, count: rechargeData.reduce((sum, item) => sum + item.recharge_count, 0) },
      { type: 'card_sales', name: '储值卡销售', amount: totalCardSales, count: rechargeData.reduce((sum, item) => sum + item.card_sales_count, 0) }
    ];

    return {
      rechargeData,
      cardData,
      paymentData,
      memberLevelData,
      cardTypeData
    };
  };

  // 生成模拟数据 - 保留此函数作为备用，当API不可用时使用
  const generateMockData = (startDate, endDate, granularity) => {
    const start = dayjs(startDate);
    const end = dayjs(endDate);
    const diff = end.diff(start, 'days');

    const rechargeData = [];
    const paymentData = [
      { method: 'wechat', method_name: '微信支付', amount: 0 },
      { method: 'alipay', method_name: '支付宝', amount: 0 },
      { method: 'cash', method_name: '现金', amount: 0 },
      { method: 'card', method_name: '银行卡', amount: 0 },
    ];

    // 会员等级数据
    const memberLevelData = [
      { level: 'normal', name: '普通会员', amount: 0, count: 0 },
      { level: 'silver', name: '银卡会员', amount: 0, count: 0 },
      { level: 'gold', name: '金卡会员', amount: 0, count: 0 },
      { level: 'platinum', name: '白金会员', amount: 0, count: 0 },
    ];

    // 储值卡类型数据
    const cardTypeData = [
      { type: 'gift', name: '礼品卡', amount: 0, count: 0 },
      { type: 'discount', name: '折扣卡', amount: 0, count: 0 },
      { type: 'prepaid', name: '预付卡', amount: 0, count: 0 },
      { type: 'special', name: '特惠卡', amount: 0, count: 0 },
    ];

    let totalRecharge = 0;
    let totalCardSales = 0;

    for (let i = 0; i <= diff; i++) {
      const currentDate = dayjs(start).add(i, 'days');
      const dateStr = currentDate.format('YYYY-MM-DD');

      // 随机生成数据
      const rechargeAmount = Math.floor(Math.random() * 10000) + 1000;
      const cardSalesAmount = Math.floor(Math.random() * 5000) + 500;
      const rechargeCount = Math.floor(Math.random() * 50) + 10;
      const cardSalesCount = Math.floor(Math.random() * 20) + 5;

      totalRecharge += rechargeAmount;
      totalCardSales += cardSalesAmount;

      rechargeData.push({
        date: dateStr,
        recharge_amount: rechargeAmount,
        recharge_count: rechargeCount,
        card_sales_amount: cardSalesAmount,
        card_sales_count: cardSalesCount,
        total_amount: rechargeAmount + cardSalesAmount
      });

      // 随机分配支付方式
      paymentData[0].amount += rechargeAmount * 0.4 + cardSalesAmount * 0.5;
      paymentData[1].amount += rechargeAmount * 0.3 + cardSalesAmount * 0.3;
      paymentData[2].amount += rechargeAmount * 0.2 + cardSalesAmount * 0.1;
      paymentData[3].amount += rechargeAmount * 0.1 + cardSalesAmount * 0.1;

      // 随机分配会员等级
      memberLevelData[0].amount += rechargeAmount * 0.3;
      memberLevelData[0].count += rechargeCount * 0.4;
      memberLevelData[1].amount += rechargeAmount * 0.3;
      memberLevelData[1].count += rechargeCount * 0.3;
      memberLevelData[2].amount += rechargeAmount * 0.2;
      memberLevelData[2].count += rechargeCount * 0.2;
      memberLevelData[3].amount += rechargeAmount * 0.2;
      memberLevelData[3].count += rechargeCount * 0.1;

      // 随机分配储值卡类型
      cardTypeData[0].amount += cardSalesAmount * 0.3;
      cardTypeData[0].count += cardSalesCount * 0.3;
      cardTypeData[1].amount += cardSalesAmount * 0.2;
      cardTypeData[1].count += cardSalesCount * 0.2;
      cardTypeData[2].amount += cardSalesAmount * 0.3;
      cardTypeData[2].count += cardSalesCount * 0.3;
      cardTypeData[3].amount += cardSalesAmount * 0.2;
      cardTypeData[3].count += cardSalesCount * 0.2;
    }

    // 四舍五入会员等级和储值卡类型的数据
    memberLevelData.forEach(item => {
      item.amount = Math.round(item.amount);
      item.count = Math.round(item.count);
    });

    cardTypeData.forEach(item => {
      item.amount = Math.round(item.amount);
      item.count = Math.round(item.count);
    });

    const cardData = [
      { type: 'recharge', name: '会员充值', amount: totalRecharge, count: rechargeData.reduce((sum, item) => sum + item.recharge_count, 0) },
      { type: 'card_sales', name: '储值卡销售', amount: totalCardSales, count: rechargeData.reduce((sum, item) => sum + item.card_sales_count, 0) }
    ];

    return {
      rechargeData,
      cardData,
      paymentData,
      memberLevelData,
      cardTypeData
    };
  };

  // 处理门店选择
  const handleStoreChange = (value) => {
    setSelectedStoreId(value);
  };

  // 处理日期范围选择
  const handleDateRangeChange = (dates) => {
    setDateRange(dates);
  };

  // 处理时间粒度选择
  const handleGranularityChange = (value) => {
    setTimeGranularity(value);
  };

  // 导出数据
  const handleExport = () => {
    // 实现导出功能
    console.log('导出数据');
  };

  // 储值趋势图配置
  const rechargeTrendConfig = {
    data: rechargeData,
    xField: 'date',
    yField: 'recharge_amount',
    seriesField: 'type',
    smooth: true,
    legend: {
      position: 'top',
    },
    xAxis: {
      type: 'time',
    },
    tooltip: {
      showMarkers: false,
    },
  };

  // 储值类型分布图配置
  const cardTypeConfig = {
    data: cardData,
    angleField: 'amount',
    colorField: 'name',
    radius: 0.8,
    label: {
      type: 'outer',
      formatter: ({name, percent}) => `${name}: ${(percent * 100).toFixed(2)}%`,
    },
    interactions: [
      {
        type: 'element-active',
      },
    ],
  };

  // 支付方式分布图配置
  const paymentDistributionConfig = {
    data: paymentData,
    angleField: 'amount',
    colorField: 'method_name',
    radius: 0.8,
    label: {
      type: 'outer',
      formatter: ({method_name, percent}) => `${method_name}: ${(percent * 100).toFixed(2)}%`,
    },
    interactions: [
      {
        type: 'element-active',
      },
    ],
  };

  // 会员等级分布图配置
  const memberLevelConfig = {
    data: memberLevelData,
    angleField: 'amount',
    colorField: 'name',
    radius: 0.8,
    label: {
      type: 'outer',
      formatter: ({name, percent}) => `${name}: ${(percent * 100).toFixed(2)}%`,
    },
    interactions: [
      {
        type: 'element-active',
      },
    ],
  };

  // 储值卡类型分布图配置
  const cardTypeDistributionConfig = {
    data: cardTypeData,
    angleField: 'amount',
    colorField: 'name',
    radius: 0.8,
    label: {
      type: 'outer',
      formatter: ({name, percent}) => `${name}: ${(percent * 100).toFixed(2)}%`,
    },
    interactions: [
      {
        type: 'element-active',
      },
    ],
  };

  // 储值数据表格列
  const rechargeColumns = [
    {
      title: '日期',
      dataIndex: 'date',
      key: 'date',
    },
    {
      title: '充值金额(元)',
      dataIndex: 'recharge_amount',
      key: 'recharge_amount',
      render: (text) => text.toFixed(2),
    },
    {
      title: '充值笔数',
      dataIndex: 'recharge_count',
      key: 'recharge_count',
    },
    {
      title: '售卡金额(元)',
      dataIndex: 'card_sales_amount',
      key: 'card_sales_amount',
      render: (text) => text.toFixed(2),
    },
    {
      title: '售卡数量',
      dataIndex: 'card_sales_count',
      key: 'card_sales_count',
    },
    {
      title: '总金额(元)',
      dataIndex: 'total_amount',
      key: 'total_amount',
      render: (text) => text.toFixed(2),
    },
  ];

  return (
    <div className="recharge-analysis-container">
      <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between' }}>
        <Space>
          <Select
            placeholder="选择门店"
            style={{ width: 200 }}
            allowClear
            onChange={handleStoreChange}
            value={selectedStoreId}
          >
            {stores.map(store => (
              <Option key={store.id} value={store.id}>{store.name}</Option>
            ))}
          </Select>
          <RangePicker
            value={dateRange}
            onChange={handleDateRangeChange}
          />
          <Select
            value={timeGranularity}
            onChange={handleGranularityChange}
            style={{ width: 120 }}
          >
            <Option value="day">按日</Option>
            <Option value="week">按周</Option>
            <Option value="month">按月</Option>
          </Select>
        </Space>
        <Button
          icon={<DownloadOutlined />}
          onClick={handleExport}
        >
          导出数据
        </Button>
      </div>

      <Spin spinning={loading}>
        <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
          <Col span={6}>
            <Card>
              <Statistic
                title="总充值金额"
                value={rechargeData.reduce((sum, item) => sum + item.recharge_amount, 0)}
                precision={2}
                suffix="元"
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="总售卡金额"
                value={rechargeData.reduce((sum, item) => sum + item.card_sales_amount, 0)}
                precision={2}
                suffix="元"
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="充值笔数"
                value={rechargeData.reduce((sum, item) => sum + item.recharge_count, 0)}
                suffix="笔"
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="售卡数量"
                value={rechargeData.reduce((sum, item) => sum + item.card_sales_count, 0)}
                suffix="张"
              />
            </Card>
          </Col>
        </Row>

        <Tabs defaultActiveKey="trend">
          <TabPane
            tab={<span><LineChartOutlined />储值趋势</span>}
            key="trend"
          >
            <Row gutter={[16, 16]}>
              <Col span={24}>
                <Card>
                  <Line {...rechargeTrendConfig} />
                </Card>
              </Col>
            </Row>
          </TabPane>
          <TabPane
            tab={<span><PieChartOutlined />会员充值分析</span>}
            key="member"
          >
            <Row gutter={[16, 16]}>
              <Col span={12}>
                <Card title="会员等级分布">
                  <Pie {...memberLevelConfig} />
                </Card>
              </Col>
              <Col span={12}>
                <Card title="会员等级明细">
                  <Table
                    columns={[
                      { title: '会员等级', dataIndex: 'name', key: 'name' },
                      { title: '充值金额(元)', dataIndex: 'amount', key: 'amount', render: (text) => text.toFixed(2) },
                      { title: '充值笔数', dataIndex: 'count', key: 'count' },
                      { title: '平均充值金额(元)', key: 'avg', render: (_, record) => (record.count > 0 ? (record.amount / record.count).toFixed(2) : '0.00') },
                    ]}
                    dataSource={memberLevelData}
                    rowKey="level"
                    pagination={false}
                  />
                </Card>
              </Col>
            </Row>
          </TabPane>
          <TabPane
            tab={<span><PieChartOutlined />储值卡分析</span>}
            key="card"
          >
            <Row gutter={[16, 16]}>
              <Col span={12}>
                <Card title="储值卡类型分布">
                  <Pie {...cardTypeDistributionConfig} />
                </Card>
              </Col>
              <Col span={12}>
                <Card title="储值卡类型明细">
                  <Table
                    columns={[
                      { title: '卡类型', dataIndex: 'name', key: 'name' },
                      { title: '销售金额(元)', dataIndex: 'amount', key: 'amount', render: (text) => text.toFixed(2) },
                      { title: '销售数量', dataIndex: 'count', key: 'count' },
                      { title: '平均单价(元)', key: 'avg', render: (_, record) => (record.count > 0 ? (record.amount / record.count).toFixed(2) : '0.00') },
                    ]}
                    dataSource={cardTypeData}
                    rowKey="type"
                    pagination={false}
                  />
                </Card>
              </Col>
            </Row>
          </TabPane>
          <TabPane
            tab={<span><PieChartOutlined />支付方式分析</span>}
            key="payment"
          >
            <Row gutter={[16, 16]}>
              <Col span={12}>
                <Card title="支付方式分布">
                  <Pie {...paymentDistributionConfig} />
                </Card>
              </Col>
              <Col span={12}>
                <Card title="支付方式明细">
                  <Table
                    columns={[
                      { title: '支付方式', dataIndex: 'method_name', key: 'method_name' },
                      { title: '金额(元)', dataIndex: 'amount', key: 'amount', render: (text) => text.toFixed(2) },
                      { title: '占比', key: 'percentage', render: (_, record) => {
                        const total = paymentData.reduce((sum, item) => sum + item.amount, 0);
                        return total > 0 ? ((record.amount / total) * 100).toFixed(2) + '%' : '0%';
                      }},
                    ]}
                    dataSource={paymentData}
                    rowKey="method"
                    pagination={false}
                  />
                </Card>
              </Col>
            </Row>
          </TabPane>
          <TabPane
            tab={<span><BarChartOutlined />储值明细</span>}
            key="detail"
          >
            <Table
              columns={rechargeColumns}
              dataSource={rechargeData}
              rowKey="date"
              pagination={{ pageSize: 10 }}
            />
          </TabPane>
        </Tabs>
      </Spin>
    </div>
  );
};

export default RechargeAnalysis;
