from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query, Path, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import func, and_, or_
import uuid
from datetime import datetime

from db.database import get_db
from models.workflow import Workflow, WorkflowNode, WorkflowTransition, WorkflowInstance, WorkflowLog
from models.user import User
from schemas.workflow import (
    WorkflowCreate, WorkflowUpdate, WorkflowResponse, WorkflowListResponse,
    WorkflowInstanceCreate, WorkflowInstanceUpdate, WorkflowInstanceResponse, WorkflowInstanceListResponse,
    WorkflowAction, WorkflowLogListResponse
)
from api.deps import get_current_user, get_current_project_id, get_current_tenant_id

router = APIRouter()

# 工作流管理API
@router.get("", response_model=WorkflowListResponse)
async def get_workflows(
    type: Optional[str] = None,
    is_active: Optional[bool] = None,
    is_default: Optional[bool] = None,
    skip: int = 0,
    limit: int = 100,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    project_id: uuid.UUID = Depends(get_current_project_id)
):
    """
    获取工作流列表
    """
    try:
        # 构建查询条件
        conditions = [
            Workflow.project_id == project_id
        ]

        if type:
            conditions.append(Workflow.type == type)

        if is_active is not None:
            conditions.append(Workflow.is_active == is_active)

        if is_default is not None:
            conditions.append(Workflow.is_default == is_default)

        # 构建查询
        query = (
            select(Workflow)
            .where(and_(*conditions))
            .order_by(Workflow.created_at.desc())
            .offset(skip)
            .limit(limit)
        )

        # 执行查询
        result = await db.execute(query)
        workflows = result.scalars().all()

        # 获取总数
        count_query = select(func.count()).select_from(
            select(Workflow)
            .where(and_(*conditions))
            .subquery()
        )
        result = await db.execute(count_query)
        total = result.scalar_one()

        # 获取工作流节点和转换
        workflow_data = []
        for workflow in workflows:
            # 查询节点
            nodes_query = select(WorkflowNode).where(
                WorkflowNode.workflow_id == workflow.id
            ).order_by(WorkflowNode.order)
            result = await db.execute(nodes_query)
            nodes = result.scalars().all()

            # 查询转换
            transitions_query = select(WorkflowTransition).where(
                WorkflowTransition.workflow_id == workflow.id
            )
            result = await db.execute(transitions_query)
            transitions = result.scalars().all()

            # 构建工作流数据
            workflow_dict = {
                **workflow.__dict__,
                "nodes": nodes,
                "transitions": transitions
            }
            workflow_data.append(workflow_dict)

        return {
            "success": True,
            "items": workflow_data,
            "total": total,
            "page": skip // limit + 1 if limit > 0 else 1,
            "size": limit,
            "pages": (total + limit - 1) // limit if limit > 0 else 1
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取工作流列表失败: {str(e)}"
        )


@router.get("/{workflow_id}", response_model=WorkflowResponse)
async def get_workflow(
    workflow_id: uuid.UUID = Path(...),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    project_id: uuid.UUID = Depends(get_current_project_id)
):
    """
    获取单个工作流详情
    """
    try:
        # 查询工作流
        query = select(Workflow).where(
            Workflow.id == workflow_id,
            Workflow.project_id == project_id
        )
        result = await db.execute(query)
        workflow = result.scalar_one_or_none()

        if not workflow:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="工作流不存在"
            )

        # 查询节点
        nodes_query = select(WorkflowNode).where(
            WorkflowNode.workflow_id == workflow.id
        ).order_by(WorkflowNode.order)
        result = await db.execute(nodes_query)
        nodes = result.scalars().all()

        # 查询转换
        transitions_query = select(WorkflowTransition).where(
            WorkflowTransition.workflow_id == workflow.id
        )
        result = await db.execute(transitions_query)
        transitions = result.scalars().all()

        # 构建工作流数据
        workflow_dict = {
            **workflow.__dict__,
            "nodes": nodes,
            "transitions": transitions
        }

        return {
            "success": True,
            "data": workflow_dict
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取工作流详情失败: {str(e)}"
        )


@router.post("", response_model=WorkflowResponse)
async def create_workflow(
    workflow: WorkflowCreate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    project_id: uuid.UUID = Depends(get_current_project_id),
    tenant_id: Optional[uuid.UUID] = Depends(get_current_tenant_id)
):
    """
    创建工作流
    """
    try:
        # 检查工作流代码是否已存在
        query = select(Workflow).where(
            Workflow.code == workflow.code,
            Workflow.project_id == project_id
        )
        result = await db.execute(query)
        existing_workflow = result.scalar_one_or_none()

        if existing_workflow:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="工作流代码已存在"
            )

        # 如果设置为默认，需要将同类型的其他工作流设置为非默认
        if workflow.is_default:
            update_query = (
                select(Workflow)
                .where(
                    Workflow.type == workflow.type,
                    Workflow.project_id == project_id,
                    Workflow.is_default == True
                )
            )
            result = await db.execute(update_query)
            default_workflows = result.scalars().all()

            for default_workflow in default_workflows:
                default_workflow.is_default = False
                default_workflow.updated_by = current_user.id
                default_workflow.updated_at = datetime.now()

        # 创建工作流
        new_workflow = Workflow(
            id=uuid.uuid4(),
            tenant_id=tenant_id,
            project_id=project_id,
            name=workflow.name,
            code=workflow.code,
            description=workflow.description,
            type=workflow.type,
            is_default=workflow.is_default,
            is_active=workflow.is_active,
            config=workflow.config,
            created_by=current_user.id,
            updated_by=current_user.id
        )

        db.add(new_workflow)
        await db.flush()

        # 创建工作流节点
        nodes = []
        for node_data in workflow.nodes:
            node = WorkflowNode(
                id=uuid.uuid4(),
                workflow_id=new_workflow.id,
                name=node_data.name,
                code=node_data.code,
                description=node_data.description,
                type=node_data.type,
                order=node_data.order,
                config=node_data.config
            )
            db.add(node)
            nodes.append(node)

        await db.flush()

        # 创建工作流转换
        transitions = []
        for transition_data in workflow.transitions:
            # 查找源节点和目标节点
            source_node = next((n for n in nodes if n.id == transition_data.source_node_id), None)
            target_node = next((n for n in nodes if n.id == transition_data.target_node_id), None)

            if not source_node or not target_node:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="源节点或目标节点不存在"
                )

            transition = WorkflowTransition(
                id=uuid.uuid4(),
                workflow_id=new_workflow.id,
                source_node_id=source_node.id,
                target_node_id=target_node.id,
                name=transition_data.name,
                condition=transition_data.condition
            )
            db.add(transition)
            transitions.append(transition)

        await db.commit()

        # 构建工作流数据
        workflow_dict = {
            **new_workflow.__dict__,
            "nodes": nodes,
            "transitions": transitions
        }

        return {
            "success": True,
            "message": "工作流创建成功",
            "data": workflow_dict
        }
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建工作流失败: {str(e)}"
        )


@router.put("/{workflow_id}", response_model=WorkflowResponse)
async def update_workflow(
    workflow_id: uuid.UUID,
    workflow_update: WorkflowUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    project_id: uuid.UUID = Depends(get_current_project_id)
):
    """
    更新工作流
    """
    try:
        # 查询工作流
        query = select(Workflow).where(
            Workflow.id == workflow_id,
            Workflow.project_id == project_id
        )
        result = await db.execute(query)
        existing_workflow = result.scalar_one_or_none()

        if not existing_workflow:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="工作流不存在"
            )

        # 如果设置为默认，需要将同类型的其他工作流设置为非默认
        if workflow_update.is_default is not None and workflow_update.is_default and not existing_workflow.is_default:
            update_query = (
                select(Workflow)
                .where(
                    Workflow.type == existing_workflow.type,
                    Workflow.project_id == project_id,
                    Workflow.is_default == True,
                    Workflow.id != workflow_id
                )
            )
            result = await db.execute(update_query)
            default_workflows = result.scalars().all()

            for default_workflow in default_workflows:
                default_workflow.is_default = False
                default_workflow.updated_by = current_user.id
                default_workflow.updated_at = datetime.now()

        # 更新字段
        update_data = workflow_update.dict(exclude_unset=True)

        for key, value in update_data.items():
            setattr(existing_workflow, key, value)

        existing_workflow.updated_by = current_user.id
        existing_workflow.updated_at = datetime.now()

        await db.commit()
        await db.refresh(existing_workflow)

        # 查询节点
        nodes_query = select(WorkflowNode).where(
            WorkflowNode.workflow_id == workflow_id
        ).order_by(WorkflowNode.order)
        result = await db.execute(nodes_query)
        nodes = result.scalars().all()

        # 查询转换
        transitions_query = select(WorkflowTransition).where(
            WorkflowTransition.workflow_id == workflow_id
        )
        result = await db.execute(transitions_query)
        transitions = result.scalars().all()

        # 构建工作流数据
        workflow_dict = {
            **existing_workflow.__dict__,
            "nodes": nodes,
            "transitions": transitions
        }

        return {
            "success": True,
            "message": "工作流更新成功",
            "data": workflow_dict
        }
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新工作流失败: {str(e)}"
        )
