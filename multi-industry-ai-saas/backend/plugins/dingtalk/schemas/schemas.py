from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any, Union

# 重新添加这些模型以修复导入错误并供将来使用
class TextContent(BaseModel):
    content: str

class MsgContent(BaseModel):
    text: Optional[TextContent] = None

class DingTalkAIMessageContent(BaseModel):
    """
    代表从'currentInput:raw'获取的完整消息内容对象。
    """
    text: Optional[TextContent] = None
    msgtype: Optional[str] = Field(None, description="消息类型，如'text', 'file'等。")
    # 根据钉钉文档，还可以包含更多字段，如 'image', 'file' 等

class DingTalkAIPassthroughRequest(BaseModel):
    """
    钉钉AI助理直通回调请求体 (遵循官方最新规范和YAML配置)
    该模型旨在全面覆盖钉钉官方文档中所有可用的上下文参数，以供当前和未来使用。
    """
    corpId: Optional[str] = Field(None, description="企业ID")
    threadId: Optional[str] = Field(None, description="会话ID")
    senderUserId: Optional[str] = Field(None, description="发送者员工ID")
    senderStaffId: Optional[str] = Field(None, description="发送者员工ID（旧版）")
    senderNick: Optional[str] = Field(None, description="发送者昵称")
    isAdmin: Optional[bool] = Field(None, description="是否为管理员")
    sessionWebhook: Optional[str] = Field(None, description="会话Webhook地址")
    sessionWebhookExpiredTime: Optional[int] = Field(None, description="会ahook过期时间")
    robotCode: Optional[str] = Field(None, description="机器人ID")
    msgId: Optional[str] = Field(None, description="消息ID")
    msgContent: Optional[Union[MsgContent, str]] = Field(None, description="用户输入的原始消息内容，可以是对象或字符串")
    # ...其他可能的上下文参数
    # currentOrg
    orgId: Optional[str] = Field(None, alias="currentOrg.corpId")
    # currentConversation
    conversationType: Optional[str] = Field(None, alias="currentConversation.type")
    # currentUser
    currentUserId: Optional[str] = Field(None, alias="currentUser.userId")
    currentUserNick: Optional[str] = Field(None, alias="currentUser.nickName")
    
    class Config:
        populate_by_name = True

class UpdateDingTalkAISettingsRequest(BaseModel):
    """更新钉钉AI助理配置的请求体"""
    dingtalk_ai_client_id: Optional[str] = Field(None, description="钉钉AI助理的Client ID (或 AppKey)")
    dingtalk_ai_client_secret: Optional[str] = Field(None, description="钉钉AI助理的Client Secret (或 AppSecret)")

class DingTalkAISettingsResponse(BaseModel):
    """钉钉AI助理配置的响应体"""
    project_id: str
    dingtalk_ai_client_id: Optional[str]
    dingtalk_ai_client_secret: Optional[str]

    class Config:
        from_attributes = True

class DingTalkAIPassthroughResponse(BaseModel):
    """钉钉AI助理直通回调响应体"""
    message: str
    conversationId: str

class DingTalkAIStreamChoiceDelta(BaseModel):
    content: str 