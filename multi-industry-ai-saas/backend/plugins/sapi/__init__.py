#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
智能助手集成平台(SAPI)插件
"""
from fastapi import APIRouter
import logging

# 设置日志
logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter()

# 导入数据库迁移脚本
from .migrations import create_tables

# 导入MCP服务器适配器
from .mcp_server_adapter import mcp_server_adapter

# 导入系统AI适配器
from .system_ai_adapter import system_ai_adapter

# 导入MCP代理
from .mcp_proxy import mcp_proxy

# 导入模型
from .models import SAPIConfig, SAPIEndpoint, SAPIUsageLog, SAPIToken, SAPITokenApplication, MCPServer, MCPServerProcess, APIMCPToolMapping

# 导入路由模块 - 直接导入文件而不是从routes包导入
# 这样可以避免循环导入问题
import importlib.util
import os
import sys

# 获取当前目录下的routes文件夹
routes_dir = os.path.join(os.path.dirname(__file__), "routes")

# 导入所有路由模块
if os.path.exists(routes_dir):
    for filename in os.listdir(routes_dir):
        if filename.endswith(".py") and filename != "__init__.py":
            module_name = filename[:-3]  # 去掉.py后缀
            module_path = os.path.join(routes_dir, filename)

            # 动态导入模块
            spec = importlib.util.spec_from_file_location(f"plugins.sapi.routes.{module_name}", module_path)
            module = importlib.util.module_from_spec(spec)
            sys.modules[spec.name] = module
            spec.loader.exec_module(module)

            logger.info(f"已导入路由模块: {module_name}")

# 初始化数据库表
async def init_db():
    """初始化SAPI插件数据库表"""
    from db.database import AsyncSessionLocal
    async with AsyncSessionLocal() as db:
        try:
            # 确保数据库表和列存在
            await create_tables(db)
            logger.info("SAPI插件数据库表初始化成功")
        except Exception as e:
            logger.error(f"SAPI插件数据库表初始化失败: {str(e)}")
            import traceback
            traceback.print_exc()
            # 不重新抛出异常，允许应用程序继续启动
            # 但是记录错误，以便后续处理

# 导入MCP路由器
#from .mcp_router import mcp_router
# 注册MCP路由器，不添加额外前缀
# MCP路由器中的路径将直接附加到插件路径后面
# 最终路径形式为: /api/v1/project/{projectId}/plugin/sapi/servers/{serverId}/...
#router.include_router(mcp_router, tags=["MCP协议"])

__all__ = [
    "router",
    "SAPIConfig",
    "SAPIEndpoint",
    "SAPIUsageLog",
    "SAPIToken",
    "SAPITokenApplication",
    "MCPServer",
    "MCPServerProcess",
    "APIMCPToolMapping",
    "mcp_server_adapter",
    "system_ai_adapter",
    "mcp_proxy",
    #"mcp_router",
    "init_db"
]
