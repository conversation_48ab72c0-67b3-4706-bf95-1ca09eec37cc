import api from '../httpClient';

/**
 * 用户API
 */
const user = {
  /**
   * 获取用户列表
   * @param {Object} params - 查询参数
   * @returns {Promise} - 用户列表
   */
  getList: async (params = {}) => {
    return api.get('/admin/users', { params });
  },

  /**
   * 获取用户信息
   * @param {string} id - 用户ID
   * @returns {Promise} - 用户信息
   */
  getInfo: async (id) => {
    if (!id) {
      throw new Error('未指定用户ID');
    }
    return api.get(`/admin/users/${id}`);
  },

  /**
   * 创建用户
   * @param {Object} data - 用户数据
   * @returns {Promise} - 创建结果
   */
  create: async (data) => {
    return api.post('/admin/users', data);
  },

  /**
   * 更新用户
   * @param {string} id - 用户ID
   * @param {Object} data - 用户数据
   * @returns {Promise} - 更新结果
   */
  update: async (id, data) => {
    if (!id) {
      throw new Error('未指定用户ID');
    }
    return api.put(`/admin/users/${id}`, data);
  },

  /**
   * 删除用户
   * @param {string} id - 用户ID
   * @returns {Promise} - 删除结果
   */
  delete: async (id) => {
    if (!id) {
      throw new Error('未指定用户ID');
    }
    return api.delete(`/admin/users/${id}`);
  }
};

export default user;
