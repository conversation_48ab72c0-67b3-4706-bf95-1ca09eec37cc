import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Input,
  Form,
  Select,
  Modal,
  message,
  Typography,
  Popconfirm,
  Row,
  Col,
  DatePicker,
  Statistic,
  Tabs,
  Divider
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  ReloadOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined
} from '@ant-design/icons';
import dayjs from 'dayjs';
import apiService from '../../../services/api';

const { Title } = Typography;
const { Option } = Select;
const { TabPane } = Tabs;
const { RangePicker } = DatePicker;

/**
 * 库存盘点页面
 * 用于管理库存盘点记录
 */
const InventoryCheck = () => {
  const [loading, setLoading] = useState(false);
  const [checkRecords, setCheckRecords] = useState([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });
  const [searchParams, setSearchParams] = useState({
    status: '',
    warehouse_id: '',
    dateRange: null,
    search: ''
  });
  const [warehouses, setWarehouses] = useState([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [currentRecord, setCurrentRecord] = useState(null);
  const [form] = Form.useForm();
  const [searchForm] = Form.useForm();
  const [activeTab, setActiveTab] = useState('1');
  const [stats, setStats] = useState({
    total: 0,
    pending: 0,
    completed: 0,
    abnormal: 0
  });

  // 初始化数据
  useEffect(() => {
    fetchWarehouses();
    fetchCheckRecords();
  }, []);

  // 获取仓库列表
  const fetchWarehouses = async () => {
    try {
      const response = await apiService.project.warehouse.getList();
      if (response && response.success) {
        setWarehouses(response.items || []);
      } else {
        message.error(response?.message || '获取仓库列表失败');
      }
    } catch (error) {
      console.error('获取仓库列表失败:', error);
      message.error('获取仓库列表失败: ' + (error.message || '未知错误'));
    }
  };

  // 获取盘点记录
  const fetchCheckRecords = async (params = {}) => {
    setLoading(true);
    try {
      const queryParams = {
        skip: (pagination.current - 1) * pagination.pageSize,
        limit: pagination.pageSize,
        ...searchParams,
        ...params
      };

      // 处理日期范围
      if (queryParams.dateRange) {
        queryParams.start_date = queryParams.dateRange[0].format('YYYY-MM-DD');
        queryParams.end_date = queryParams.dateRange[1].format('YYYY-MM-DD');
        delete queryParams.dateRange;
      }

      // 过滤掉空字符串参数，避免后端UUID解析错误
      Object.keys(queryParams).forEach(key => {
        if (queryParams[key] === '' || queryParams[key] === null || queryParams[key] === undefined) {
          delete queryParams[key];
        }
      });

      const response = await apiService.project.inventory.getCheckRecords(queryParams);

      if (response && response.success) {
        setCheckRecords(response.items || []);
        setPagination({
          ...pagination,
          total: response.total || 0
        });
        
        // 更新统计数据
        setStats({
          total: response.total || 0,
          pending: response.stats?.pending || 0,
          completed: response.stats?.completed || 0,
          abnormal: response.stats?.abnormal || 0
        });
      } else {
        message.error(response?.message || '获取盘点记录失败');
      }
    } catch (error) {
      console.error('获取盘点记录失败:', error);
      message.error('获取盘点记录失败: ' + (error.message || '未知错误'));
    } finally {
      setLoading(false);
    }
  };

  // 处理搜索
  const handleSearch = (values) => {
    const newParams = { ...searchParams, ...values };
    setSearchParams(newParams);
    setPagination({ ...pagination, current: 1 });
    fetchCheckRecords({ ...newParams, skip: 0 });
  };

  // 处理重置搜索
  const handleReset = () => {
    searchForm.resetFields();
    const newParams = {
      status: '',
      warehouse_id: '',
      dateRange: null,
      search: ''
    };
    setSearchParams(newParams);
    setPagination({ ...pagination, current: 1 });
    fetchCheckRecords({ ...newParams, skip: 0 });
  };

  // 处理表格分页变化
  const handleTableChange = (pagination, filters, sorter) => {
    setPagination(pagination);
    fetchCheckRecords({
      skip: (pagination.current - 1) * pagination.pageSize,
      limit: pagination.pageSize
    });
  };

  // 显示创建盘点记录模态框
  const showCreateModal = () => {
    setCurrentRecord(null);
    form.resetFields();
    form.setFieldsValue({
      check_date: dayjs(),
      status: 'pending'
    });
    setModalVisible(true);
  };

  // 显示编辑盘点记录模态框
  const showEditModal = (record) => {
    setCurrentRecord(record);
    form.setFieldsValue({
      warehouse_id: record.warehouse_id,
      check_number: record.check_number,
      check_date: record.check_date ? dayjs(record.check_date) : dayjs(),
      status: record.status,
      notes: record.notes
    });
    setModalVisible(true);
  };

  // 显示盘点记录详情模态框
  const showDetailModal = (record) => {
    setCurrentRecord(record);
    setDetailModalVisible(true);
  };

  // 处理表单提交
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      try {
        let response;
        if (currentRecord) {
          // 更新盘点记录
          response = await apiService.project.inventory.updateCheckRecord(
            currentRecord.id,
            {
              ...values,
              check_date: values.check_date.format('YYYY-MM-DD')
            }
          );
        } else {
          // 创建盘点记录
          response = await apiService.project.inventory.createCheckRecord({
            ...values,
            check_date: values.check_date.format('YYYY-MM-DD')
          });
        }

        if (response && response.success) {
          message.success(currentRecord ? '盘点记录更新成功' : '盘点记录创建成功');
          setModalVisible(false);
          fetchCheckRecords();
        } else {
          message.error(response?.message || (currentRecord ? '更新盘点记录失败' : '创建盘点记录失败'));
        }
      } catch (error) {
        console.error(currentRecord ? '更新盘点记录失败:' : '创建盘点记录失败:', error);
        message.error((currentRecord ? '更新盘点记录失败: ' : '创建盘点记录失败: ') + (error.message || '未知错误'));
      } finally {
        setLoading(false);
      }
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  // 处理删除盘点记录
  const handleDelete = async (id) => {
    try {
      setLoading(true);
      const response = await apiService.project.inventory.deleteCheckRecord(id);

      if (response && response.success) {
        message.success('盘点记录删除成功');
        fetchCheckRecords();
      } else {
        message.error(response?.message || '删除盘点记录失败');
      }
    } catch (error) {
      console.error('删除盘点记录失败:', error);
      message.error('删除盘点记录失败: ' + (error.message || '未知错误'));
    } finally {
      setLoading(false);
    }
  };

  // 处理确认盘点
  const handleConfirm = async (id) => {
    try {
      setLoading(true);
      const response = await apiService.project.inventory.confirmCheckRecord(id);

      if (response && response.success) {
        message.success('盘点记录确认成功');
        fetchCheckRecords();
      } else {
        message.error(response?.message || '确认盘点记录失败');
      }
    } catch (error) {
      console.error('确认盘点记录失败:', error);
      message.error('确认盘点记录失败: ' + (error.message || '未知错误'));
    } finally {
      setLoading(false);
    }
  };

  // 盘点状态映射
  const statusMap = {
    pending: { text: '待盘点', color: 'warning' },
    completed: { text: '已完成', color: 'success' },
    abnormal: { text: '异常', color: 'error' },
    cancelled: { text: '已取消', color: 'default' }
  };

  // 搜索表单
  const SearchForm = () => (
    <Form
      form={searchForm}
      layout="inline"
      onFinish={handleSearch}
      style={{ marginBottom: 16 }}
    >
      <Form.Item name="status" label="状态">
        <Select placeholder="请选择状态" style={{ width: 120 }} allowClear>
          <Option value="pending">待盘点</Option>
          <Option value="completed">已完成</Option>
          <Option value="abnormal">异常</Option>
          <Option value="cancelled">已取消</Option>
        </Select>
      </Form.Item>

      <Form.Item name="warehouse_id" label="仓库">
        <Select placeholder="请选择仓库" style={{ width: 150 }} allowClear>
          {warehouses.map(warehouse => (
            <Option key={warehouse.id} value={warehouse.id}>{warehouse.name}</Option>
          ))}
        </Select>
      </Form.Item>

      <Form.Item name="dateRange" label="盘点日期">
        <RangePicker style={{ width: 240 }} />
      </Form.Item>

      <Form.Item name="search" label="搜索">
        <Input
          placeholder="盘点单号/备注"
          prefix={<SearchOutlined />}
          style={{ width: 200 }}
        />
      </Form.Item>

      <Form.Item>
        <Button type="primary" htmlType="submit">
          搜索
        </Button>
      </Form.Item>

      <Form.Item>
        <Button onClick={handleReset}>
          重置
        </Button>
      </Form.Item>
    </Form>
  );

  // 表格列定义
  const columns = [
    {
      title: '盘点单号',
      dataIndex: 'check_number',
      key: 'check_number',
      render: (text, record) => (
        <a onClick={() => showDetailModal(record)}>{text}</a>
      )
    },
    {
      title: '仓库',
      dataIndex: 'warehouse_name',
      key: 'warehouse_name'
    },
    {
      title: '盘点日期',
      dataIndex: 'check_date',
      key: 'check_date',
      render: (text) => text ? dayjs(text).format('YYYY-MM-DD') : '-'
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => {
        const { text, color } = statusMap[status] || { text: status, color: 'default' };
        return <Tag color={color}>{text}</Tag>;
      }
    },
    {
      title: '商品数量',
      dataIndex: 'item_count',
      key: 'item_count'
    },
    {
      title: '异常数量',
      dataIndex: 'abnormal_count',
      key: 'abnormal_count',
      render: (text) => text > 0 ? <Tag color="error">{text}</Tag> : text
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="small">
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => showDetailModal(record)}
          />
          
          {record.status === 'pending' && (
            <>
              <Button
                type="text"
                icon={<EditOutlined />}
                onClick={() => showEditModal(record)}
              />
              
              <Button
                type="text"
                icon={<CheckCircleOutlined />}
                onClick={() => handleConfirm(record.id)}
              />
              
              <Popconfirm
                title="确定要删除这个盘点记录吗？"
                onConfirm={() => handleDelete(record.id)}
                okText="确定"
                cancelText="取消"
              >
                <Button
                  type="text"
                  danger
                  icon={<DeleteOutlined />}
                />
              </Popconfirm>
            </>
          )}
        </Space>
      )
    }
  ];

  return (
    <div className="inventory-check-page">
      <Card>
        <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 16 }}>
          <Title level={4}>库存盘点</Title>
          <Space>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={showCreateModal}
            >
              新建盘点
            </Button>
            <Button
              icon={<ReloadOutlined />}
              onClick={() => fetchCheckRecords()}
            >
              刷新
            </Button>
          </Space>
        </div>

        <Row gutter={16} style={{ marginBottom: 16 }}>
          <Col span={6}>
            <Card>
              <Statistic
                title="盘点总数"
                value={stats.total}
                suffix="个"
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="待盘点"
                value={stats.pending}
                suffix="个"
                valueStyle={{ color: '#faad14' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="已完成"
                value={stats.completed}
                suffix="个"
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="异常盘点"
                value={stats.abnormal}
                suffix="个"
                valueStyle={{ color: '#ff4d4f' }}
              />
            </Card>
          </Col>
        </Row>

        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab="盘点记录" key="1">
            <SearchForm />

            <Table
              columns={columns}
              dataSource={checkRecords}
              rowKey="id"
              pagination={pagination}
              onChange={handleTableChange}
              loading={loading}
            />
          </TabPane>
        </Tabs>
      </Card>

      {/* 盘点记录表单模态框 */}
      <Modal
        title={currentRecord ? '编辑盘点记录' : '新建盘点记录'}
        open={modalVisible}
        onOk={handleSubmit}
        onCancel={() => setModalVisible(false)}
        confirmLoading={loading}
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Form.Item
            name="warehouse_id"
            label="仓库"
            rules={[{ required: true, message: '请选择仓库' }]}
          >
            <Select placeholder="请选择仓库">
              {warehouses.map(warehouse => (
                <Option key={warehouse.id} value={warehouse.id}>{warehouse.name}</Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="check_number"
            label="盘点单号"
            rules={[{ required: true, message: '请输入盘点单号' }]}
          >
            <Input placeholder="请输入盘点单号" disabled={!!currentRecord} />
          </Form.Item>

          <Form.Item
            name="check_date"
            label="盘点日期"
            rules={[{ required: true, message: '请选择盘点日期' }]}
          >
            <DatePicker style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            name="status"
            label="状态"
            rules={[{ required: true, message: '请选择状态' }]}
          >
            <Select placeholder="请选择状态">
              <Option value="pending">待盘点</Option>
              <Option value="completed">已完成</Option>
              <Option value="abnormal">异常</Option>
              <Option value="cancelled">已取消</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="notes"
            label="备注"
          >
            <Input.TextArea rows={4} placeholder="请输入备注" />
          </Form.Item>
        </Form>
      </Modal>

      {/* 盘点记录详情模态框 */}
      <Modal
        title="盘点记录详情"
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setDetailModalVisible(false)}>
            关闭
          </Button>
        ]}
        width={800}
      >
        {currentRecord && (
          <>
            <div style={{ marginBottom: 16 }}>
              <Row gutter={16}>
                <Col span={8}>
                  <div style={{ marginBottom: 8 }}>
                    <div style={{ color: 'rgba(0, 0, 0, 0.45)', fontSize: 14, marginBottom: 4 }}>盘点单号</div>
                    <div style={{ fontSize: 14 }}>{currentRecord.check_number}</div>
                  </div>
                </Col>
                <Col span={8}>
                  <div style={{ marginBottom: 8 }}>
                    <div style={{ color: 'rgba(0, 0, 0, 0.45)', fontSize: 14, marginBottom: 4 }}>仓库</div>
                    <div style={{ fontSize: 14 }}>{currentRecord.warehouse_name}</div>
                  </div>
                </Col>
                <Col span={8}>
                  <div style={{ marginBottom: 8 }}>
                    <div style={{ color: 'rgba(0, 0, 0, 0.45)', fontSize: 14, marginBottom: 4 }}>盘点日期</div>
                    <div style={{ fontSize: 14 }}>
                      {currentRecord.check_date ? dayjs(currentRecord.check_date).format('YYYY-MM-DD') : '-'}
                    </div>
                  </div>
                </Col>
              </Row>
              <Row gutter={16} style={{ marginTop: 16 }}>
                <Col span={8}>
                  <div style={{ marginBottom: 8 }}>
                    <div style={{ color: 'rgba(0, 0, 0, 0.45)', fontSize: 14, marginBottom: 4 }}>状态</div>
                    <div style={{ fontSize: 14 }}>
                      <Tag color={statusMap[currentRecord.status]?.color || 'default'}>
                        {statusMap[currentRecord.status]?.text || currentRecord.status}
                      </Tag>
                    </div>
                  </div>
                </Col>
                <Col span={8}>
                  <div style={{ marginBottom: 8 }}>
                    <div style={{ color: 'rgba(0, 0, 0, 0.45)', fontSize: 14, marginBottom: 4 }}>商品数量</div>
                    <div style={{ fontSize: 14 }}>{currentRecord.item_count}</div>
                  </div>
                </Col>
                <Col span={8}>
                  <div style={{ marginBottom: 8 }}>
                    <div style={{ color: 'rgba(0, 0, 0, 0.45)', fontSize: 14, marginBottom: 4 }}>异常数量</div>
                    <div style={{ fontSize: 14 }}>
                      {currentRecord.abnormal_count > 0 ? (
                        <Tag color="error">{currentRecord.abnormal_count}</Tag>
                      ) : (
                        currentRecord.abnormal_count
                      )}
                    </div>
                  </div>
                </Col>
              </Row>
              {currentRecord.notes && (
                <Row style={{ marginTop: 16 }}>
                  <Col span={24}>
                    <div style={{ marginBottom: 8 }}>
                      <div style={{ color: 'rgba(0, 0, 0, 0.45)', fontSize: 14, marginBottom: 4 }}>备注</div>
                      <div style={{ fontSize: 14 }}>{currentRecord.notes}</div>
                    </div>
                  </Col>
                </Row>
              )}
            </div>

            <Divider>盘点商品</Divider>

            {currentRecord.items && currentRecord.items.length > 0 ? (
              <Table
                dataSource={currentRecord.items}
                rowKey="id"
                pagination={false}
                size="small"
                columns={[
                  { title: '商品名称', dataIndex: 'product_name', key: 'product_name' },
                  { title: '商品编码', dataIndex: 'product_code', key: 'product_code' },
                  { title: '规格', dataIndex: 'product_specification', key: 'product_specification' },
                  { title: '单位', dataIndex: 'product_unit', key: 'product_unit' },
                  { title: '系统库存', dataIndex: 'system_quantity', key: 'system_quantity' },
                  { title: '实际库存', dataIndex: 'actual_quantity', key: 'actual_quantity' },
                  {
                    title: '差异',
                    dataIndex: 'difference',
                    key: 'difference',
                    render: (text, record) => {
                      const diff = record.actual_quantity - record.system_quantity;
                      return diff !== 0 ? (
                        <Tag color={diff < 0 ? 'error' : 'warning'}>
                          {diff > 0 ? `+${diff}` : diff}
                        </Tag>
                      ) : (
                        '0'
                      );
                    }
                  },
                  { title: '备注', dataIndex: 'notes', key: 'notes', ellipsis: true }
                ]}
              />
            ) : (
              <div style={{ textAlign: 'center', padding: '20px 0' }}>
                暂无盘点商品数据
              </div>
            )}
          </>
        )}
      </Modal>
    </div>
  );
};

export default InventoryCheck;
