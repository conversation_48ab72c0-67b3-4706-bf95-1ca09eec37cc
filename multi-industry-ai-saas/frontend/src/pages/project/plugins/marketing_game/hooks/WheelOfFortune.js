import React, { useState, useEffect, useRef } from 'react';
import { useParams } from 'react-router-dom';
import {
  Button,
  message,
  Spin,
  Typography,
  Row,
  Col,
  Modal,
  Divider,
  List,
  Avatar,
  Tag,
  Result,
  Space,
  Statistic,
  Card
} from 'antd';
import {
  GiftOutlined,
  TrophyOutlined,
  UserOutlined,
  HistoryOutlined,
  ShareAltOutlined,
  SmileOutlined,
  MehOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import './WheelOfFortune.css';

const { Title, Text, Paragraph } = Typography;

// 转盘颜色配置
const wheelColors = [
  '#FF1744', // 亮红色
  '#FFEA00', // 亮黄色
  '#00E676', // 亮绿色
  '#2979FF', // 亮蓝色
  '#D500F9', // 亮紫色
  '#FF9100', // 亮橙色
  '#00B0FF', // 天蓝色
  '#76FF03'  // 亮绿黄色
];

// 模拟奖品数据
const mockPrizes = [
  { id: 1, name: '一等奖', description: '价值1000元购物券', image: 'https://img.icons8.com/color/96/000000/prize.png', probability: 0.01, remaining: 5 },
  { id: 2, name: '二等奖', description: '价值500元购物券', image: 'https://img.icons8.com/color/96/000000/prize.png', probability: 0.05, remaining: 10 },
  { id: 3, name: '三等奖', description: '价值100元购物券', image: 'https://img.icons8.com/color/96/000000/prize.png', probability: 0.1, remaining: 20 },
  { id: 4, name: '幸运奖', description: '价值50元购物券', image: 'https://img.icons8.com/color/96/000000/prize.png', probability: 0.2, remaining: 50 },
  { id: 5, name: '参与奖', description: '价值10元购物券', image: 'https://img.icons8.com/color/96/000000/prize.png', probability: 0.3, remaining: 100 },
  { id: 6, name: '谢谢参与', description: '下次再来', image: 'https://img.icons8.com/color/96/000000/sad.png', probability: 0.34, remaining: 999 }
];

// 模拟中奖记录
const mockRecords = [
  { id: 1, user_name: '用户1', prize_name: '一等奖', created_at: '2023-05-01 12:30:45' },
  { id: 2, user_name: '用户2', prize_name: '二等奖', created_at: '2023-05-01 13:20:15' },
  { id: 3, user_name: '用户3', prize_name: '三等奖', created_at: '2023-05-01 14:10:22' },
  { id: 4, user_name: '用户4', prize_name: '幸运奖', created_at: '2023-05-01 15:05:33' },
  { id: 5, user_name: '用户5', prize_name: '参与奖', created_at: '2023-05-01 16:15:18' }
];

// 模拟游戏数据
const mockGame = {
  id: 1,
  name: '618购物节幸运大转盘',
  description: '参与618购物节，消费满100元即可参与幸运大转盘，赢取丰厚奖品！',
  rules: `
  1. 活动时间：2023年6月1日至2023年6月18日
  2. 参与方式：消费满100元，凭小票参与幸运大转盘
  3. 每人每天最多参与3次
  4. 奖品数量有限，先到先得
  5. 本活动最终解释权归商家所有
  `,
  start_time: '2023-06-01 00:00:00',
  end_time: '2023-06-18 23:59:59',
  status: 'published',
  min_consumption: 100,
  max_draws_per_user: 3,
  remaining_draws: 3,
  background_image: 'https://img.freepik.com/free-vector/gradient-sale-background_23-2149050986.jpg',
  logo_image: 'https://img.icons8.com/color/96/000000/shop.png'
};

const WheelOfFortune = () => {
  const { gameId } = useParams();
  const [game, setGame] = useState(null);
  const [prizes, setPrizes] = useState([]);
  const [records, setRecords] = useState([]);
  const [loading, setLoading] = useState(true);
  const [spinning, setSpinning] = useState(false);
  const [showRules, setShowRules] = useState(false);
  const [showRecords, setShowRecords] = useState(false);
  const [showPrizes, setShowPrizes] = useState(false);
  const [winningPrize, setWinningPrize] = useState(null);
  const [showResult, setShowResult] = useState(false);

  const wheelRef = useRef(null);
  const spinningTimeRef = useRef(null);

  // 加载游戏数据
  useEffect(() => {
    const fetchGameData = async () => {
      try {
        setLoading(true);
        // 在实际应用中，这里应该调用API获取游戏数据
        // const response = await apiService.project.plugin.marketingGame.getGameDetail(gameId);
        // setGame(response.data);

        // 使用模拟数据
        setTimeout(() => {
          setGame(mockGame);
          setPrizes(mockPrizes);
          setRecords(mockRecords);
          setLoading(false);
        }, 1000);
      } catch (error) {
        console.error('获取游戏数据失败:', error);
        message.error('获取游戏数据失败');
        setLoading(false);
      }
    };

    fetchGameData();
  }, [gameId]);

  // 处理转盘
  const handleSpin = () => {
    if (game.remaining_draws <= 0) {
      message.warning('您的参与次数已用完');
      return;
    }

    if (spinning) return;

    setSpinning(true);

    // 随机选择一个奖品（根据概率）
    const randomValue = Math.random();
    let cumulativeProbability = 0;
    let selectedPrize = null;

    for (const prize of prizes) {
      cumulativeProbability += prize.probability;
      if (randomValue <= cumulativeProbability) {
        selectedPrize = prize;
        break;
      }
    }

    // 如果没有选中任何奖品，默认为"谢谢参与"
    if (!selectedPrize) {
      selectedPrize = prizes[prizes.length - 1];
    }

    // 计算旋转角度
    const prizeIndex = prizes.findIndex(p => p.id === selectedPrize.id);
    const segmentAngle = 360 / prizes.length;
    // 增加旋转圈数，使动画更流畅
    const targetAngle = 360 * 8 + prizeIndex * segmentAngle;

    // 应用CSS动画
    if (wheelRef.current) {
      // 使用更自然的缓动函数
      wheelRef.current.style.transition = 'transform 5s cubic-bezier(0.32, 0.64, 0.45, 1)';
      wheelRef.current.style.transform = `rotate(${targetAngle}deg)`;

      // 添加音效
      const audio = new Audio('/assets/sounds/wheel-spin.mp3');
      audio.volume = 0.5;
      audio.play().catch(e => console.log('音效播放失败:', e));
    }

    // 动画结束后显示结果
    spinningTimeRef.current = setTimeout(() => {
      setSpinning(false);
      setWinningPrize(selectedPrize);
      setShowResult(true);

      // 播放中奖音效
      const winSound = new Audio('/assets/sounds/win-sound.mp3');
      winSound.volume = 0.6;
      winSound.play().catch(e => console.log('中奖音效播放失败:', e));

      // 更新剩余次数
      setGame(prev => ({
        ...prev,
        remaining_draws: prev.remaining_draws - 1
      }));

      // 重置转盘
      if (wheelRef.current) {
        wheelRef.current.style.transition = 'none';
        wheelRef.current.style.transform = 'rotate(0deg)';
      }
    }, 5100);
  };

  // 清除定时器
  useEffect(() => {
    return () => {
      if (spinningTimeRef.current) {
        clearTimeout(spinningTimeRef.current);
      }
    };
  }, []);

  if (loading) {
    return (
      <div className="wheel-fortune-loading">
        <Spin size="large" />
        <p>加载中...</p>
      </div>
    );
  }

  return (
    <div className="wheel-fortune-container" style={{ backgroundImage: `url(${game.background_image})` }}>
      <div className="wheel-fortune-header">
        <img src={game.logo_image} alt="Logo" className="game-logo" />
        <Title level={2} className="game-title">{game.name}</Title>
        <Paragraph className="game-description">{game.description}</Paragraph>
      </div>

      <div className="wheel-fortune-content">
        <Card className="wheel-card">
          <div className="wheel-container">
            <div className="wheel-pointer"></div>
            <div className="wheel" ref={wheelRef}>
              {prizes.map((prize, index) => {
                const angle = (360 / prizes.length) * index;
                // 使用不同颜色
                const colorIndex = index % wheelColors.length;
                const backgroundColor = wheelColors[colorIndex];
                // 文字颜色根据背景色亮度调整
                const textColor = colorIndex === 1 ? '#333' : '#fff';

                return (
                  <div
                    key={prize.id}
                    className="wheel-item"
                    style={{
                      transform: `rotate(${angle}deg) skewY(-${90 - (360 / prizes.length)}deg)`,
                      backgroundColor
                    }}
                  >
                    <div
                      className="wheel-item-content"
                      style={{ color: textColor }}
                    >
                      <img src={prize.image} alt={prize.name} className="prize-image" />
                      <div className="prize-name">{prize.name}</div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </Card>

        <div className="wheel-fortune-actions">
          <Button
            type="primary"
            size="large"
            icon={<GiftOutlined />}
            onClick={handleSpin}
            loading={spinning}
            disabled={spinning || game.remaining_draws <= 0}
            className="spin-button"
          >
            {spinning ? '旋转中...' : `开始旋转 (剩余${game.remaining_draws}次)`}
          </Button>

          <div className="action-buttons">
            <Button icon={<TrophyOutlined />} onClick={() => setShowPrizes(true)}>奖品设置</Button>
            <Button icon={<HistoryOutlined />} onClick={() => setShowRecords(true)}>中奖记录</Button>
            <Button icon={<InfoCircleOutlined />} onClick={() => setShowRules(true)}>活动规则</Button>
          </div>
        </div>
      </div>

      {/* 活动规则弹窗 */}
      <Modal
        title="活动规则"
        open={showRules}
        onCancel={() => setShowRules(false)}
        footer={null}
      >
        <Paragraph>
          <pre>{game.rules}</pre>
        </Paragraph>
        <Divider />
        <Row>
          <Col span={12}>
            <Statistic title="最低消费" value={`¥${game.min_consumption}`} />
          </Col>
          <Col span={12}>
            <Statistic title="每人参与次数" value={game.max_draws_per_user} />
          </Col>
        </Row>
      </Modal>

      {/* 奖品设置弹窗 */}
      <Modal
        title="奖品设置"
        open={showPrizes}
        onCancel={() => setShowPrizes(false)}
        footer={null}
        width={700}
      >
        <List
          itemLayout="horizontal"
          dataSource={prizes.filter(prize => prize.name !== '谢谢参与')}
          renderItem={item => (
            <List.Item>
              <List.Item.Meta
                avatar={<Avatar src={item.image} size={64} />}
                title={item.name}
                description={item.description}
              />
              <div>
                <Tag color="blue">概率: {(item.probability * 100).toFixed(2)}%</Tag>
                <Tag color="green">剩余: {item.remaining}</Tag>
              </div>
            </List.Item>
          )}
        />
      </Modal>

      {/* 中奖记录弹窗 */}
      <Modal
        title="中奖记录"
        open={showRecords}
        onCancel={() => setShowRecords(false)}
        footer={null}
      >
        <List
          itemLayout="horizontal"
          dataSource={records}
          renderItem={item => (
            <List.Item>
              <List.Item.Meta
                avatar={<Avatar icon={<UserOutlined />} />}
                title={item.user_name}
                description={`获得 ${item.prize_name}`}
              />
              <div>{item.created_at}</div>
            </List.Item>
          )}
        />
      </Modal>

      {/* 抽奖结果弹窗 */}
      <Modal
        title="抽奖结果"
        open={showResult}
        onCancel={() => setShowResult(false)}
        footer={[
          <Button key="back" onClick={() => setShowResult(false)}>
            关闭
          </Button>,
          <Button
            key="share"
            type="primary"
            icon={<ShareAltOutlined />}
            onClick={() => {
              message.success('分享成功');
              setShowResult(false);
            }}
          >
            分享
          </Button>
        ]}
        width={500}
      >
        {winningPrize && (
          <Result
            icon={winningPrize.name === '谢谢参与' ? <MehOutlined /> : <SmileOutlined />}
            status={winningPrize.name === '谢谢参与' ? 'warning' : 'success'}
            title={winningPrize.name === '谢谢参与' ? '很遗憾，未中奖' : '恭喜您，中奖了！'}
            subTitle={winningPrize.description}
            extra={
              winningPrize.name !== '谢谢参与' && (
                <div className="prize-result">
                  <img src={winningPrize.image} alt={winningPrize.name} className="prize-result-image" />
                  <div className="prize-result-info">
                    <Title level={4}>{winningPrize.name}</Title>
                    <Paragraph>{winningPrize.description}</Paragraph>
                  </div>
                </div>
              )
            }
          />
        )}

        <Divider />

        <Space direction="vertical" style={{ width: '100%', textAlign: 'center' }}>
          <Text>剩余参与次数: {game.remaining_draws}</Text>
          {game.remaining_draws > 0 && (
            <Button
              type="primary"
              onClick={() => {
                setShowResult(false);
                setTimeout(() => {
                  handleSpin();
                }, 500);
              }}
            >
              继续参与
            </Button>
          )}
        </Space>
      </Modal>
    </div>
  );
};

export default WheelOfFortune;
