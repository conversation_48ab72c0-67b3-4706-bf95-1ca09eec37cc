import React, { useState, useRef, useEffect } from 'react';
import {
  Card,
  Input,
  Button,
  Space,
  Avatar,
  Typography,
  Divider,
  Alert,
  Spin,
  message,
  Tag,
  Tooltip,
  Select
} from 'antd';
import {
  SendOutlined,
  RobotOutlined,
  UserOutlined,
  ReloadOutlined,
  ClockCircleOutlined,
  PictureOutlined,
  DeleteOutlined,
  AudioOutlined,
  StopOutlined,
  AudioMutedOutlined
} from '@ant-design/icons';

const { TextArea } = Input;
const { Text, Paragraph } = Typography;
const { Option } = Select;

// 添加CSS样式
const chatStyles = `
  .chat-messages-container {
    scrollbar-width: thin;
    scrollbar-color: #ccc #f5f5f5;
  }
  
  .chat-messages-container::-webkit-scrollbar {
    width: 8px;
  }
  
  .chat-messages-container::-webkit-scrollbar-track {
    background: #f5f5f5;
    border-radius: 4px;
  }
  
  .chat-messages-container::-webkit-scrollbar-thumb {
    background: #ccc;
    border-radius: 4px;
  }
  
  .chat-messages-container::-webkit-scrollbar-thumb:hover {
    background: #999;
  }
`;

// 注入样式
if (typeof document !== 'undefined') {
  const styleElement = document.createElement('style');
  styleElement.textContent = chatStyles;
  document.head.appendChild(styleElement);
}

const ChatTestComponent = ({ projectId }) => {
  const [messages, setMessages] = useState([
    {
      id: '1',
      type: 'ai',
      content: '您好！我是AI智能客服，有什么可以帮助您的吗？',
      timestamp: new Date(),
      model: 'AI助手'
    }
  ]);
  const [inputValue, setInputValue] = useState('');
  const [loading, setLoading] = useState(false);
  const [sessionId, setSessionId] = useState(null);
  const [uploadedImages, setUploadedImages] = useState([]);
  const [currentModel, setCurrentModel] = useState('AI助手');
  const [uploading, setUploading] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [isProcessingAudio, setIsProcessingAudio] = useState(false);
  const [isLiveAudioConnected, setIsLiveAudioConnected] = useState(false);
  const [audioLevel, setAudioLevel] = useState(0);
  const messagesEndRef = useRef(null);
  const mediaRecorderRef = useRef(null);
  const audioChunksRef = useRef([]);
  const audioContextRef = useRef(null);
  const analyserRef = useRef(null);
  const sourceRef = useRef(null);
  const animationFrameRef = useRef(null);
  const liveAudioWs = useRef(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // 获取项目ID
  const getProjectId = () => {
    // 优先从localStorage获取项目ID
    const projectId = localStorage.getItem('project_id');
    if (projectId && projectId !== 'null' && projectId !== 'undefined') {
      return projectId;
    }

    // 如果localStorage中没有，从用户数据中获取
    const userData = JSON.parse(localStorage.getItem('user_data') || '{}');
    if (userData && userData.project_id) {
      localStorage.setItem('project_id', userData.project_id);
      return userData.project_id;
    }

    // 最后尝试从URL路径解析（作为备用方案）
    const pathParts = window.location.pathname.split('/');
    const projectIndex = pathParts.indexOf('project');
    if (projectIndex !== -1 && pathParts[projectIndex + 1] && pathParts[projectIndex + 1] !== 'plugins') {
      return pathParts[projectIndex + 1];
    }

    return null;
  };

  // 获取系统整合设置
  const fetchIntegrationSettings = async () => {
    try {
      const projectId = getProjectId();
      if (!projectId) return;

      const response = await fetch(`/api/v1/project/${projectId}/plugin/ai-customer-service/integration/settings`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        // 如果有配置的AI模型，获取模型信息
        if (data.ai_model_id) {
          fetchModelInfo(data.ai_model_id);
        }
      }
    } catch (error) {
      console.error('获取系统整合设置失败:', error);
    }
  };

  // 获取AI模型信息
  const fetchModelInfo = async (modelId) => {
    try {
      const projectId = getProjectId();
      if (!projectId) return;

      const response = await fetch(`/api/project/${projectId}/ai/models`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        const model = data.data?.find(m => m.id === modelId);
        if (model) {
          setCurrentModel(model.display_name || model.name || 'AI助手');
        }
      }
    } catch (error) {
      console.error('获取AI模型信息失败:', error);
    }
  };

  // 初始化会话
  const initSession = async () => {
    try {
      const currentProjectId = projectId || getProjectId();
      if (!currentProjectId) {
        console.error('无法获取项目ID');
        return;
      }
      
      const response = await fetch(`/api/v1/project/${currentProjectId}/plugin/ai-customer-service/sessions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          platform_type: 'test',
          platform_user_id: 'test_user_' + Date.now(),
          title: '对话测试会话'
        })
      });

      if (response.ok) {
        const data = await response.json();
        setSessionId(data.session_id);
      }
    } catch (error) {
      console.error('初始化会话失败:', error);
    }
  };

  useEffect(() => {
    const currentProjectId = projectId || getProjectId();
    if (currentProjectId) {
      initSession();
      fetchIntegrationSettings();
    }
  }, [projectId]);

  // 更新初始消息的模型名称
  useEffect(() => {
    if (currentModel !== 'AI助手') {
      setMessages(prevMessages =>
        prevMessages.map(msg =>
          msg.id === '1' ? { ...msg, model: currentModel } : msg
        )
      );
    }
  }, [currentModel]);

  // 上传图片
  const handleImageUpload = async (file) => {
    setUploading(true);
    try {
      const formData = new FormData();
      formData.append('file', file);

      const currentProjectId = projectId || getProjectId();
      if (!currentProjectId) {
        message.error('无法获取项目ID');
        return;
      }
      
      const response = await fetch(`/api/v1/project/${currentProjectId}/plugin/ai-customer-service/upload-image`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: formData
      });

      if (response.ok) {
        const data = await response.json();
        setUploadedImages(prev => [...prev, {
          id: Date.now().toString(),
          url: data.image_url,
          filename: data.filename
        }]);
        message.success('图片上传成功');
      } else {
        message.error('图片上传失败');
      }
    } catch (error) {
      console.error('图片上传失败:', error);
      message.error('图片上传失败');
    } finally {
      setUploading(false);
    }
  };

  // 删除上传的图片
  const removeImage = (imageId) => {
    setUploadedImages(prev => prev.filter(img => img.id !== imageId));
  };

  // 初始化音频分析器
  const initAudioAnalyzer = (stream) => {
    try {
      audioContextRef.current = new (window.AudioContext || window.webkitAudioContext)();
      analyserRef.current = audioContextRef.current.createAnalyser();
      sourceRef.current = audioContextRef.current.createMediaStreamSource(stream);
      
      analyserRef.current.fftSize = 256;
      sourceRef.current.connect(analyserRef.current);
      
      updateAudioLevel();
    } catch (error) {
      console.error('初始化音频分析器失败:', error);
    }
  };

  // 更新音频音量级别
  const updateAudioLevel = () => {
    if (!analyserRef.current) return;
    
    const bufferLength = analyserRef.current.frequencyBinCount;
    const dataArray = new Uint8Array(bufferLength);
    
    const animate = () => {
      if (!isLiveAudioConnected) return;
      
      analyserRef.current.getByteFrequencyData(dataArray);
      const average = dataArray.reduce((sum, value) => sum + value, 0) / bufferLength;
      const normalizedLevel = Math.min(average / 255 * 100, 100);
      
      setAudioLevel(normalizedLevel);
      animationFrameRef.current = requestAnimationFrame(animate);
    };
    
    animate();
  };

  // 开始实时语音连接
  const startLiveAudio = async () => {
    try {
      const currentProjectId = projectId || getProjectId();
      if (!currentProjectId) {
        message.error('无法获取项目ID');
        return;
      }

      // 检查浏览器兼容性
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        message.error('您的浏览器不支持音频录制功能，请使用Chrome、Firefox等现代浏览器');
        return;
      }

      // 获取麦克风权限
      const stream = await navigator.mediaDevices.getUserMedia({ 
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          sampleRate: 16000
        } 
      });

      // 初始化音频分析器
      initAudioAnalyzer(stream);

      // 建立WebSocket连接
      const token = localStorage.getItem('token');
      const wsUrl = `${window.location.protocol === 'https:' ? 'wss:' : 'ws:'}//${window.location.host}/api/v1/project/${currentProjectId}/plugin/ai-customer-service/audio/live-stream?token=${token}`;
      
      const ws = new WebSocket(wsUrl);
      
      ws.onopen = () => {
        console.log('实时语音WebSocket连接已建立');
        setIsLiveAudioConnected(true);
        liveAudioWs.current = ws;
        message.success('实时语音连接已建立');
        
        // 开始发送音频数据
        startAudioStreaming(stream, ws);
      };

      ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          console.log('收到实时语音响应:', data);
          
          if (data.type === 'transcription') {
            // 实时更新输入框文本
            setInputValue(prev => prev + data.text);
          } else if (data.type === 'final_transcription') {
            // 最终转录结果
            setInputValue(data.text);
            message.success('语音识别完成');
          } else if (data.type === 'ai_response') {
            // AI回复
            const newMessage = {
              id: Date.now(),
              type: 'ai',
              content: data.response,
              timestamp: new Date(),
              sources: data.sources || []
            };
            setMessages(prev => [...prev, newMessage]);
          } else if (data.type === 'error') {
            message.error('语音处理错误: ' + data.message);
          }
        } catch (error) {
          console.error('处理语音消息失败:', error);
        }
      };

      ws.onerror = (error) => {
        console.error('实时语音WebSocket错误:', error);
        message.error('实时语音连接错误');
      };

      ws.onclose = () => {
        console.log('实时语音WebSocket连接已关闭');
        setIsLiveAudioConnected(false);
        liveAudioWs.current = null;
        setAudioLevel(0);
        
        // 停止音频流
        stream.getTracks().forEach(track => track.stop());
        
        // 清理音频上下文
        if (audioContextRef.current) {
          audioContextRef.current.close();
          audioContextRef.current = null;
        }
        
        // 清理动画帧
        if (animationFrameRef.current) {
          cancelAnimationFrame(animationFrameRef.current);
          animationFrameRef.current = null;
        }
        
        message.info('实时语音连接已断开');
      };

    } catch (error) {
      console.error('启动实时语音失败:', error);
      if (error.name === 'NotAllowedError') {
        message.error('麦克风权限被拒绝，请在浏览器设置中允许麦克风访问');
      } else if (error.name === 'NotFoundError') {
        message.error('未找到麦克风设备，请检查硬件连接');
      } else {
        message.error('无法访问麦克风: ' + error.message);
      }
    }
  };

  // 开始音频流传输
  const startAudioStreaming = (stream, ws) => {
    const mediaRecorder = new MediaRecorder(stream, {
      mimeType: 'audio/webm;codecs=opus',
      audioBitsPerSecond: 16000
    });

    mediaRecorder.ondataavailable = (event) => {
      if (event.data.size > 0 && ws.readyState === WebSocket.OPEN) {
        // 将音频数据转换为ArrayBuffer并发送
        event.data.arrayBuffer().then(buffer => {
          ws.send(buffer);
        });
      }
    };

    // 每100ms发送一次音频数据
    mediaRecorder.start(100);
    mediaRecorderRef.current = mediaRecorder;
  };

  // 停止实时语音
  const stopLiveAudio = () => {
    if (liveAudioWs.current) {
      liveAudioWs.current.close();
    }
    
    if (mediaRecorderRef.current) {
      mediaRecorderRef.current.stop();
      mediaRecorderRef.current = null;
    }
    
    setIsLiveAudioConnected(false);
    liveAudioWs.current = null;
    setAudioLevel(0);
  };

  // 开始录音 (保留原有录制功能作为备选)
  const startRecording = async () => {
    try {
      // 检查浏览器兼容性
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        message.error('您的浏览器不支持音频录制功能，请使用Chrome、Firefox等现代浏览器');
        return;
      }

      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      
      const mediaRecorder = new MediaRecorder(stream);
      mediaRecorderRef.current = mediaRecorder;
      audioChunksRef.current = [];

      mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
        }
      };

      mediaRecorder.onstop = async () => {
        const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/wav' });
        await processAudio(audioBlob);
        
        // 停止所有音频轨道
        stream.getTracks().forEach(track => track.stop());
      };

      mediaRecorder.start();
      setIsRecording(true);
      message.info('开始录音，再次点击停止');
    } catch (error) {
      console.error('启动录音失败:', error);
      if (error.name === 'NotAllowedError') {
        message.error('麦克风权限被拒绝，请在浏览器设置中允许麦克风访问');
      } else if (error.name === 'NotFoundError') {
        message.error('未找到麦克风设备，请检查硬件连接');
      } else {
        message.error('无法访问麦克风: ' + error.message);
      }
    }
  };

  // 停止录音
  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
    }
  };

  // 处理音频转文字
  const processAudio = async (audioBlob) => {
    setIsProcessingAudio(true);
    try {
      const currentProjectId = projectId || getProjectId();
      if (!currentProjectId) {
        message.error('无法获取项目ID');
        return;
      }

      const formData = new FormData();
      formData.append('audio', audioBlob, 'recording.wav');
      formData.append('language', 'zh'); // 设置为中文

      const response = await fetch(`/api/v1/project/${currentProjectId}/plugin/ai-customer-service/audio/transcribe`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: formData
      });

      if (response.ok) {
        const data = await response.json();
        const transcription = data.transcription || data.text || '';
        
        if (transcription.trim()) {
          setInputValue(transcription);
          message.success('语音转换成功');
        } else {
          message.warning('未识别到语音内容，请重试');
        }
      } else {
        const errorData = await response.json();
        throw new Error(errorData.detail || '语音转换失败');
      }
    } catch (error) {
      console.error('处理音频失败:', error);
      message.error('语音转换失败: ' + error.message);
    } finally {
      setIsProcessingAudio(false);
    }
  };

  // 发送消息
  const sendMessage = async () => {
    if (!inputValue.trim() && uploadedImages.length === 0) return;

    const userMessage = {
      id: Date.now().toString(),
      type: 'user',
      content: inputValue,
      timestamp: new Date(),
      images: uploadedImages.length > 0 ? [...uploadedImages] : null
    };

    setMessages(prev => [...prev, userMessage]);
    const currentInput = inputValue;
    const currentImages = [...uploadedImages];
    setInputValue('');
    setUploadedImages([]);
    setLoading(true);

    try {
      const currentProjectId = projectId || getProjectId();
      if (!currentProjectId) {
        message.error('无法获取项目ID');
        return;
      }
      
      const response = await fetch(`/api/v1/project/${currentProjectId}/plugin/ai-customer-service/chat/enhanced-chat`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          message: currentInput,
          session_id: sessionId,
          platform_type: 'test',
          platform_user_id: 'test_user',
          image_urls: currentImages.map(img => img.url),
          enable_intent_analysis: true,
          enable_knowledge_search: true,
          enable_learning: true
        })
      });

      if (response.ok) {
        const data = await response.json();
        const aiMessage = {
          id: (Date.now() + 1).toString(),
          type: 'ai',
          content: data.response || '抱歉，我暂时无法回答这个问题。',
          timestamp: new Date(),
          model: data.model_used || 'AI',
          confidence: data.confidence,
          response_time: data.response_time,
          // 增强功能数据
          intent_analysis: data.intent_analysis,
          knowledge_sources: data.knowledge_sources,
          business_actions: data.business_actions,
          reply_suggestions: data.reply_suggestions,
          quality_assessment: data.quality_assessment
        };
        setMessages(prev => [...prev, aiMessage]);
      } else {
        message.error('发送消息失败');
      }
    } catch (error) {
      console.error('发送消息失败:', error);
      message.error('发送消息失败');
    } finally {
      setLoading(false);
    }
  };

  // 清空对话
  const clearChat = () => {
    setMessages([
      {
        id: '1',
        type: 'ai',
        content: '您好！我是AI智能客服，有什么可以帮助您的吗？',
        timestamp: new Date(),
        model: currentModel
      }
    ]);
    const projectId = getProjectId();
    if (projectId) {
      initSession();
    }
  };

  // 快速测试问题
  const quickQuestions = [
    '你好，请介绍一下你的功能',
    '我想了解产品价格',
    '如何联系人工客服？',
    '你们的营业时间是什么？',
    '我遇到了技术问题，怎么办？'
  ];

  const handleQuickQuestion = (question) => {
    setInputValue(question);
  };

  const formatTime = (timestamp) => {
    return new Date(timestamp).toLocaleTimeString();
  };

  return (
    <div style={{ height: '600px', display: 'flex', flexDirection: 'column' }}>
      <style dangerouslySetInnerHTML={{__html: `
        .chat-messages-container {
          scrollbar-width: thin;
          scrollbar-color: #ccc #f5f5f5;
        }
        .chat-messages-container::-webkit-scrollbar {
          width: 8px;
        }
        .chat-messages-container::-webkit-scrollbar-track {
          background: #f5f5f5;
          border-radius: 4px;
        }
        .chat-messages-container::-webkit-scrollbar-thumb {
          background: #ccc;
          border-radius: 4px;
        }
        .chat-messages-container::-webkit-scrollbar-thumb:hover {
          background: #999;
        }
      `}} />
      <Alert
        message="对话测试说明"
        description="这是一个测试环境，您可以在这里测试AI客服的对话能力。所有对话数据仅用于测试，不会保存到正式系统中。"
        type="info"
        showIcon
        style={{ marginBottom: 16 }}
      />

      {/* 快速测试问题 */}
      <Card size="small" style={{ marginBottom: 16 }}>
        <Text strong>快速测试问题：</Text>
        <div style={{ marginTop: 8 }}>
          <Space wrap>
            {quickQuestions.map((question, index) => (
              <Button
                key={index}
                size="small"
                onClick={() => handleQuickQuestion(question)}
              >
                {question}
              </Button>
            ))}
          </Space>
        </div>
      </Card>

      {/* 对话区域 */}
      <Card 
        style={{ 
          flex: 1, 
          display: 'flex', 
          flexDirection: 'column',
          overflow: 'hidden'
        }}
        bodyStyle={{ 
          flex: 1, 
          display: 'flex', 
          flexDirection: 'column',
          padding: '16px 16px 0 16px'
        }}
      >
        <div 
          style={{ 
            flex: 1, 
            overflowY: 'auto', 
            marginBottom: 16,
            padding: '0 8px',
            maxHeight: '400px' // 添加最大高度确保滚动
          }}
          className="chat-messages-container"
        >
          {messages.map((msg) => (
            <div key={msg.id} style={{ marginBottom: 16 }}>
              <div style={{ 
                display: 'flex', 
                justifyContent: msg.type === 'user' ? 'flex-end' : 'flex-start',
                alignItems: 'flex-start'
              }}>
                {msg.type === 'ai' && (
                  <Avatar 
                    icon={<RobotOutlined />} 
                    style={{ backgroundColor: '#1890ff', marginRight: 8 }}
                  />
                )}
                
                <div style={{ 
                  maxWidth: '70%',
                  order: msg.type === 'user' ? -1 : 1
                }}>
                  <div style={{
                    background: msg.type === 'user' ? '#1890ff' : '#f5f5f5',
                    color: msg.type === 'user' ? 'white' : 'black',
                    padding: '8px 12px',
                    borderRadius: '8px',
                    marginBottom: '4px'
                  }}>
                    {/* 显示图片 */}
                    {msg.images && msg.images.length > 0 && (
                      <div style={{ marginBottom: '8px' }}>
                        {msg.images.map((image, index) => (
                          <img
                            key={index}
                            src={image.url}
                            alt={image.filename}
                            style={{
                              maxWidth: '200px',
                              maxHeight: '200px',
                              borderRadius: '4px',
                              marginRight: '8px',
                              marginBottom: '4px'
                            }}
                          />
                        ))}
                      </div>
                    )}

                    {msg.content && (
                      <Paragraph style={{
                        margin: 0,
                        color: msg.type === 'user' ? 'white' : 'inherit'
                      }}>
                        {msg.content}
                      </Paragraph>
                    )}
                  </div>
                  
                  <div style={{ 
                    fontSize: '12px', 
                    color: '#999',
                    textAlign: msg.type === 'user' ? 'right' : 'left'
                  }}>
                    <Space size={4}>
                      <ClockCircleOutlined />
                      <span>{formatTime(msg.timestamp)}</span>
                      {msg.model && (
                        <Tag size="small" color="blue">{msg.model}</Tag>
                      )}
                      {msg.confidence && (
                        <Tooltip title="AI置信度">
                          <Tag size="small" color="green">
                            {(msg.confidence * 100).toFixed(0)}%
                          </Tag>
                        </Tooltip>
                      )}
                      {msg.response_time && (
                        <Tooltip title="响应时间">
                          <Tag size="small" color="orange">
                            {msg.response_time}ms
                          </Tag>
                        </Tooltip>
                      )}
                    </Space>

                    {/* 增强功能信息 - 只在AI消息中显示 */}
                    {msg.type === 'ai' && (
                      <div style={{ marginTop: 4 }}>
                        {/* 意图分析 */}
                        {msg.intent_analysis && msg.intent_analysis.primary_intent && (
                          <div style={{ fontSize: '11px', color: '#666' }}>
                            💡 意图: {msg.intent_analysis.primary_intent}
                            {msg.intent_analysis.confidence && typeof msg.intent_analysis.confidence === 'number' && !isNaN(msg.intent_analysis.confidence) && ` (${(msg.intent_analysis.confidence * 100).toFixed(0)}%)`}
                          </div>
                        )}
                        
                        {/* 知识库来源 */}
                        {msg.knowledge_sources && msg.knowledge_sources.length > 0 && (
                          <div style={{ fontSize: '11px', color: '#666' }}>
                            📚 知识库: {msg.knowledge_sources.length}个相关文档
                          </div>
                        )}
                        
                        {/* 业务动作 */}
                        {msg.business_actions && msg.business_actions.length > 0 && (
                          <div style={{ fontSize: '11px', color: '#666' }}>
                            🎯 业务动作: {msg.business_actions.length}个
                          </div>
                        )}
                        
                        {/* 质量评估 */}
                        {msg.quality_assessment && typeof msg.quality_assessment.quality_score === 'number' && !isNaN(msg.quality_assessment.quality_score) && (
                          <div style={{ fontSize: '11px', color: '#666' }}>
                            ⭐ 质量评分: {(msg.quality_assessment.quality_score * 100).toFixed(0)}%
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </div>

                {msg.type === 'user' && (
                  <Avatar 
                    icon={<UserOutlined />} 
                    style={{ backgroundColor: '#52c41a', marginLeft: 8 }}
                  />
                )}
              </div>
            </div>
          ))}
          
          {loading && (
            <div style={{ textAlign: 'center', padding: '16px' }}>
              <Spin size="small" />
              <Text style={{ marginLeft: 8 }}>AI正在思考中...</Text>
            </div>
          )}
          
          <div ref={messagesEndRef} />
        </div>

        <Divider style={{ margin: '8px 0' }} />

        {/* 输入区域 */}
        <div style={{ padding: '0 8px 16px 8px' }}>
          {/* 上传的图片预览 */}
          {uploadedImages.length > 0 && (
            <div style={{ marginBottom: '8px', padding: '8px', background: '#f5f5f5', borderRadius: '4px' }}>
              <Text style={{ fontSize: '12px', color: '#666', marginBottom: '4px', display: 'block' }}>
                已上传的图片：
              </Text>
              <Space wrap>
                {uploadedImages.map((image) => (
                  <div key={image.id} style={{ position: 'relative', display: 'inline-block' }}>
                    <img
                      src={image.url}
                      alt={image.filename}
                      style={{
                        width: '60px',
                        height: '60px',
                        objectFit: 'cover',
                        borderRadius: '4px',
                        border: '1px solid #d9d9d9'
                      }}
                    />
                    <Button
                      type="text"
                      size="small"
                      icon={<DeleteOutlined />}
                      onClick={() => removeImage(image.id)}
                      style={{
                        position: 'absolute',
                        top: '-8px',
                        right: '-8px',
                        background: '#ff4d4f',
                        color: 'white',
                        borderRadius: '50%',
                        width: '20px',
                        height: '20px',
                        padding: 0,
                        minWidth: '20px'
                      }}
                    />
                  </div>
                ))}
              </Space>
            </div>
          )}

          <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
            <Input
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              placeholder="输入消息..."
              onPressEnter={sendMessage}
              disabled={loading}
              style={{ flex: 1 }}
            />
            
            {/* 图片上传按钮 */}
            <input
              type="file"
              accept="image/*"
              style={{ display: 'none' }}
              id="image-upload"
              onChange={(e) => {
                const file = e.target.files[0];
                if (file) {
                  handleImageUpload(file);
                  e.target.value = '';
                }
              }}
            />
            <Button
              icon={<PictureOutlined />}
              onClick={() => document.getElementById('image-upload').click()}
              loading={uploading}
              title="上传图片"
              disabled={loading}
            />
            
            {/* 实时语音按钮 */}
            <Button
              type={isLiveAudioConnected ? "danger" : "primary"}
              icon={isLiveAudioConnected ? <AudioMutedOutlined /> : <AudioOutlined />}
              onClick={isLiveAudioConnected ? stopLiveAudio : startLiveAudio}
              loading={loading}
              style={{
                backgroundColor: isLiveAudioConnected ? '#ff4d4f' : undefined,
                borderColor: isLiveAudioConnected ? '#ff4d4f' : undefined
              }}
              title="实时语音"
            >
              {isLiveAudioConnected ? '结束' : '实时'}
            </Button>
            
            {/* 录制语音按钮 (备选) */}
            <Button
              type={isRecording ? "danger" : "default"}
              icon={isRecording ? <AudioMutedOutlined /> : <AudioOutlined />}
              onClick={isRecording ? stopRecording : startRecording}
              loading={isProcessingAudio}
              disabled={isLiveAudioConnected || loading}
              title="录制语音"
            >
              {isRecording ? '停止' : '录音'}
            </Button>
            
            <Button 
              type="primary" 
              icon={<SendOutlined />}
              onClick={sendMessage}
              loading={loading}
              disabled={(!inputValue.trim() && uploadedImages.length === 0) || loading}
            >
              发送
            </Button>
          </div>
          
          {/* 音频级别指示器 */}
          {isLiveAudioConnected && (
            <div style={{ marginTop: '8px', display: 'flex', alignItems: 'center', gap: '8px' }}>
              <span style={{ fontSize: '12px', color: '#666' }}>音量:</span>
              <div style={{
                width: '100px',
                height: '4px',
                backgroundColor: '#f0f0f0',
                borderRadius: '2px',
                overflow: 'hidden'
              }}>
                <div style={{
                  width: `${audioLevel}%`,
                  height: '100%',
                  backgroundColor: audioLevel > 70 ? '#ff4d4f' : audioLevel > 30 ? '#faad14' : '#52c41a',
                  transition: 'width 0.1s ease'
                }} />
              </div>
              <span style={{ fontSize: '12px', color: '#666' }}>
                {isLiveAudioConnected ? '🔴 实时语音中' : ''}
              </span>
            </div>
          )}
          
          {/* 清空对话按钮 */}
          <div style={{ marginTop: '8px', textAlign: 'right' }}>
            <Button
              size="small"
              icon={<ReloadOutlined />}
              onClick={clearChat}
              disabled={loading}
            >
              清空对话
            </Button>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default ChatTestComponent;
