import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Input,
  DatePicker,
  Form,
  Select,
  Modal,
  message,
  Typography,
  Tabs,
  Statistic,
  Row,
  Col,
  Badge,
  Avatar,
  Tooltip,
  Dropdown,
  Menu,
  Progress,
  Calendar,
  List,
  Radio,
  Upload
} from 'antd';
import {
  SearchOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  ReloadOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ClockCircleOutlined,
  CalendarOutlined,
  MoreOutlined,
  UnorderedListOutlined,
  UploadOutlined,
  SyncOutlined,
  GiftOutlined,
  BookOutlined,
  AimOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import dayjs from 'dayjs';
import apiService from '../../../services/api';
import MarketingActivities from './components/MarketingActivities';
import KnowledgeBase from './components/KnowledgeBase';
import AreaSurvey from './routine-task/AreaSurvey';

const { Title, Text } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;
const { TabPane } = Tabs;
const { TextArea } = Input;
const { Search } = Input;

// 任务状态标签颜色映射
const statusColors = {
  pending: 'default',
  in_progress: 'processing',
  completed: 'success',
  overdue: 'error',
  cancelled: 'warning'
};

// 任务状态文本映射
const statusTexts = {
  pending: '待处理',
  in_progress: '进行中',
  completed: '已完成',
  overdue: '已逾期',
  cancelled: '已取消'
};

// 任务优先级标签颜色映射
const priorityColors = {
  low: 'default',
  medium: 'blue',
  high: 'orange',
  urgent: 'red'
};

// 任务优先级文本映射
const priorityTexts = {
  low: '低',
  medium: '中',
  high: '高',
  urgent: '紧急'
};

// 任务类型映射
const taskTypeTexts = {
  daily: '每日',
  weekly: '每周',
  monthly: '每月',
  custom: '自定义'
};

// 任务类别映射
const categoryTexts = {
  store: '门店',
  operation: '运营',
  marketing: '营销',
  inventory: '库存',
  finance: '财务',
  other: '其他'
};



const RoutineTasks = () => {
  const navigate = useNavigate();

  // 状态管理
  const [loading, setLoading] = useState(false);
  const [loadingUsers, setLoadingUsers] = useState(false);
  const [tasks, setTasks] = useState([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });
  const [searchParams, setSearchParams] = useState({
    status: '',
    task_type: '',
    category: '',
    priority: '',
    store_id: '',
    dateRange: null,
    search: ''
  });
  const [stores, setStores] = useState([]);
  const [users, setUsers] = useState([]);
  const [filteredUsers, setFilteredUsers] = useState([]);
  const [selectedStores, setSelectedStores] = useState([]);
  const [activeTab, setActiveTab] = useState('all');
  const [viewMode, setViewMode] = useState('list'); // list, calendar
  const [deleteModalVisible, setDeleteModalVisible] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [currentTask, setCurrentTask] = useState(null);
  const [fileList, setFileList] = useState([]);
  const [form] = Form.useForm();
  const [taskForm] = Form.useForm();

  // 统计数据
  const [stats, setStats] = useState({
    total: 0,
    pending: 0,
    in_progress: 0,
    completed: 0,
    overdue: 0,
    cancelled: 0
  });

  // 门店统计数据
  const [storeStats, setStoreStats] = useState([]);

  // 获取门店列表
  const fetchStores = async () => {
    try {
      const response = await apiService.project.store.getList();
      if (response && response.items) {
        setStores(response.items);
      }
    } catch (error) {
      console.error('获取门店列表失败:', error);
      message.error('获取门店列表失败');
    }
  };

  // 获取用户列表
  const fetchUsers = async (storeIds = []) => {
    return new Promise(async (resolve) => {
      try {
        setLoadingUsers(true);
        const response = await apiService.project.user.getList();

        if (response && Array.isArray(response)) {
          // 处理返回的数据格式
          const allUsers = response
            .filter(item => item.success && item.data)
            .map(item => {
              const userData = item.data;
              // 确保用户数据包含必要的字段
              return {
                ...userData,
                // 如果没有name，使用username
                name: userData.name || userData.username,
                // 如果有多个门店名称，用逗号连接
                store_name: Array.isArray(userData.store_names) && userData.store_names.length > 0
                  ? userData.store_names.join(', ')
                  : ''
              };
            });

          setUsers(allUsers);

          // 如果选择了特定门店，过滤用户
          if (storeIds && storeIds.length > 0 && !storeIds.includes('all')) {
            // 过滤出选定门店的用户
            const storeUsers = allUsers.filter(user => {
              // 检查用户是否属于选定的门店
              if (Array.isArray(user.store_ids)) {
                return user.store_ids.some(storeId =>
                  storeIds.includes(storeId.toString())
                );
              }
              return false;
            });

            setFilteredUsers(storeUsers.length > 0 ? storeUsers : allUsers);
          } else {
            // 如果选择了"所有门店"或没有选择门店，显示所有用户
            setFilteredUsers(allUsers);
          }
        } else if (response && response.items) {
          // 兼容原有的数据格式
          const allUsers = response.items;
          setUsers(allUsers);
          setFilteredUsers(allUsers);
        } else {
          // 如果没有数据，设置为空数组
          setUsers([]);
          setFilteredUsers([]);
        }
        resolve(true);
      } catch (error) {
        console.error('获取用户列表失败:', error);
        message.error('获取用户列表失败');
        resolve(false);
      } finally {
        setLoadingUsers(false);
      }
    });
  };

  // 处理门店选择变化
  const handleStoreChange = (values) => {
    // 检查是否选择了"所有门店"
    if (values && values.includes('all')) {
      // 如果选择了"所有门店"，移除其他所有选项
      taskForm.setFieldsValue({ store_id: ['all'] });
      setSelectedStores(['all']);
      fetchUsers(); // 加载所有用户
    } else if (values && values.length > 0) {
      // 如果选择了特定门店，加载这些门店的用户
      setSelectedStores(values);
      fetchUsers(values);
    } else {
      // 如果没有选择任何门店，清空用户列表
      setSelectedStores([]);
      setFilteredUsers([]);
    }
  };

  // 获取任务列表
  const fetchTasks = async (params = {}) => {
    setLoading(true);
    try {
      // 准备查询参数
      const queryParams = {
        ...params,
        skip: ((pagination.current - 1) * pagination.pageSize) || 0,
        limit: pagination.pageSize,
        status: activeTab !== 'all' ? activeTab : searchParams.status,
        task_type: searchParams.task_type,
        category: 'store', // 固定为门店类别
        priority: searchParams.priority,
        store_id: searchParams.store_id,
        search: searchParams.search
      };

      // 处理日期范围
      if (searchParams.dateRange && searchParams.dateRange[0] && searchParams.dateRange[1]) {
        queryParams.start_date = searchParams.dateRange[0].format('YYYY-MM-DD');
        queryParams.end_date = searchParams.dateRange[1].format('YYYY-MM-DD');
      }

      // 调用API获取数据
      const response = await apiService.project.routineTask.getList(queryParams);

      if (response && response.items) {
        setTasks(response.items);
        setPagination({
          ...pagination,
          total: response.total || response.items.length
        });

        // 更新统计数据
        if (response.stats) {
          setStats(response.stats);
        } else {
          // 如果API没有返回统计数据，手动计算
          updateStats(response.items);
        }

        // 更新门店统计数据
        if (response.store_stats) {
          setStoreStats(response.store_stats);
        } else {
          // 如果API没有返回门店统计数据，手动计算
          updateStoreStats(response.items);
        }
      } else {
        setTasks([]);
        setPagination({
          ...pagination,
          total: 0
        });
        setStats({
          total: 0,
          pending: 0,
          in_progress: 0,
          completed: 0,
          overdue: 0,
          cancelled: 0
        });
        setStoreStats([]);
      }
    } catch (error) {
      console.error('获取任务列表失败:', error);
      message.error('获取任务列表失败: ' + (error.response?.data?.detail || error.message));
      setTasks([]);
    } finally {
      setLoading(false);
    }
  };

  // 更新统计数据
  const updateStats = (taskList) => {
    const newStats = {
      total: taskList.length,
      pending: taskList.filter(t => t.status === 'pending').length,
      in_progress: taskList.filter(t => t.status === 'in_progress').length,
      completed: taskList.filter(t => t.status === 'completed').length,
      overdue: taskList.filter(t => t.status === 'overdue').length,
      cancelled: taskList.filter(t => t.status === 'cancelled').length
    };

    setStats(newStats);
  };

  // 更新门店统计数据
  const updateStoreStats = (taskList) => {
    // 按门店分组任务
    const storeMap = new Map();

    taskList.forEach(task => {
      // 处理单个门店ID
      if (task.store_id) {
        const storeId = task.store_id;
        const storeName = task.store_name || stores.find(s => String(s.id) === String(storeId))?.name || '未知门店';

        if (!storeMap.has(storeId)) {
          storeMap.set(storeId, {
            store_id: storeId,
            store_name: storeName,
            total: 0,
            completed: 0,
            pending: 0,
            in_progress: 0,
            cancelled: 0,
            overdue: 0,
            completion_rate: 0
          });
        }

        const storeStat = storeMap.get(storeId);
        storeStat.total += 1;

        if (task.status) {
          storeStat[task.status] = (storeStat[task.status] || 0) + 1;
        }
      }
      // 处理多个门店ID
      else if (task.store_ids && Array.isArray(task.store_ids)) {
        task.store_ids.forEach(storeId => {
          const storeName = stores.find(s => String(s.id) === String(storeId))?.name || '未知门店';

          if (!storeMap.has(storeId)) {
            storeMap.set(storeId, {
              store_id: storeId,
              store_name: storeName,
              total: 0,
              completed: 0,
              pending: 0,
              in_progress: 0,
              cancelled: 0,
              overdue: 0,
              completion_rate: 0
            });
          }

          const storeStat = storeMap.get(storeId);
          storeStat.total += 1;

          if (task.status) {
            storeStat[task.status] = (storeStat[task.status] || 0) + 1;
          }
        });
      }
    });

    // 计算完成率
    storeMap.forEach(store => {
      store.completion_rate = store.total > 0 ? Math.round((store.completed / store.total) * 100) : 0;
    });

    setStoreStats(Array.from(storeMap.values()));
  };

  // 处理表格分页变化
  const handleTableChange = (newPagination, filters, sorter) => {
    setPagination(newPagination);
    fetchTasks({
      skip: (newPagination.current - 1) * newPagination.pageSize,
      limit: newPagination.pageSize,
      sort_field: sorter.field,
      sort_order: sorter.order
    });
  };

  // 处理搜索表单提交
  const handleSearch = (values) => {
    const { status, task_type, category, priority, store_id, dateRange, search } = values;
    setSearchParams({ status, task_type, category, priority, store_id, dateRange, search });
    setPagination({ ...pagination, current: 1 });
    fetchTasks();
  };

  // 重置搜索表单
  const handleReset = () => {
    form.resetFields();
    const defaultParams = {
      status: '',
      task_type: '',
      category: '',
      priority: '',
      store_id: '',
      dateRange: null,
      search: ''
    };
    setSearchParams(defaultParams);
    setPagination({ ...pagination, current: 1 });
    fetchTasks();
  };

  // 打开任务表单
  const handleOpenTaskForm = async (task = null) => {
    try {
      let isAdmin = false;
      let isProjectAdmin = false;
      let isStoreUser = false;

      try {
        // 获取当前用户信息
        const currentUser = await apiService.project.user.getCurrentUser();

        // 检查用户是否有权限创建/编辑任务
        isAdmin = currentUser && (currentUser.is_admin || currentUser.is_system_admin || currentUser.is_super_admin);
        isProjectAdmin = currentUser && (currentUser.is_project_admin || currentUser.is_tenant_admin);
        isStoreUser = currentUser && currentUser.roles && currentUser.roles.some(
          role => role.name.includes('门店') || role.name.includes('store')
        );
      } catch (error) {
        console.error('获取当前用户信息失败:', error);
        // 如果获取用户信息失败，默认允许操作，后端会进行权限验证
        isStoreUser = true;
      }

      // 如果是项目管理员，允许创建/编辑任务
      if (isProjectAdmin) {
        isAdmin = true;
      }

      // 如果不是管理员也不是门店部门用户，则不允许创建/编辑任务
      if (!isAdmin && !isStoreUser) {
        message.error('您没有权限创建或编辑门店任务');
        return;
      }

      // 如果是编辑任务，检查是否有权限编辑
      if (task && !isAdmin) {
        // 检查任务是否属于门店部门
        if (task.category !== 'store') {
          message.error('您只能编辑门店部门的任务');
          return;
        }

        try {
          // 获取当前用户信息
          const currentUser = await apiService.project.user.getCurrentUser();

          // 检查任务是否分配给自己
          const isAssignee = currentUser && task.assignee_ids && task.assignee_ids.includes(currentUser.id);
          if (!isAssignee && !isStoreUser) {
            message.error('您只能编辑分配给自己的任务');
            return;
          }
        } catch (error) {
          console.error('获取当前用户信息失败:', error);
          // 如果获取用户信息失败，默认允许操作，后端会进行权限验证
        }
      }

      setCurrentTask(task);
      taskForm.resetFields();

      if (task) {
        // 编辑任务
        const storeIds = task.store_id ?
          (Array.isArray(task.store_id) ? task.store_id : [task.store_id]) :
          [];

        // 如果是所有门店
        if (task.is_all_stores) {
          storeIds.push('all');
        }

        // 加载相关门店的用户
        setSelectedStores(storeIds);
        fetchUsers(storeIds);

        taskForm.setFieldsValue({
          ...task,
          start_date: task.start_date ? dayjs(task.start_date).startOf('day') : null,
          due_date: task.due_date ? dayjs(task.due_date).startOf('day') : null,
          store_id: storeIds,
          assignee_ids: task.assignee_ids || []
        });

        // 设置附件
        if (task.attachments && task.attachments.length > 0) {
          const files = task.attachments.map((url, index) => ({
            uid: `-${index}`,
            name: url.split('/').pop(),
            status: 'done',
            url
          }));
          setFileList(files);
        } else {
          setFileList([]);
        }
      } else {
        // 新建任务
        setSelectedStores([]);
        setFilteredUsers(users); // 显示所有用户

        // 设置表单初始值
        // 使用当前日期，确保只有日期部分
        const currentDate = dayjs().startOf('day');

        taskForm.setFieldsValue({
          category: 'store', // 门店部门只能创建门店任务
          priority: 'medium',
          start_date: currentDate, // 使用当前日期作为开始日期
          assignee_ids: [],
          store_id: []
        });
        setFileList([]);
      }

      setModalVisible(true);
    } catch (error) {
      console.error('获取用户信息失败:', error);
      message.error('获取用户信息失败: ' + (error.message || '未知错误'));
    }
  };

  // 创建任务
  const handleCreate = () => {
    handleOpenTaskForm();
  };

  // 查看任务详情
  const handleView = (id) => {
    navigate(`/project/store/routine-tasks/detail/${id}`);
  };

  // 编辑任务
  const handleEdit = (id) => {
    const task = tasks.find(t => t.id === id);
    if (task) {
      handleOpenTaskForm(task);
    } else {
      message.error('任务不存在');
    }
  };

  // 删除任务
  const handleDelete = async () => {
    if (!currentTask) return;

    setLoading(true);
    try {
      // 调用API删除数据
      await apiService.project.routineTask.delete(currentTask.id);

      message.success('任务删除成功');
      setDeleteModalVisible(false);
      setCurrentTask(null);

      // 重新获取任务列表
      fetchTasks();
    } catch (error) {
      console.error('删除任务失败:', error);
      message.error('删除任务失败: ' + (error.response?.data?.detail || error.message));
    } finally {
      setLoading(false);
    }
  };

  // 提交任务表单
  const handleSubmitTask = async () => {
    try {
      const values = await taskForm.validateFields();
      setLoading(true);

      let isAdmin = false;
      let isProjectAdmin = false;
      let isStoreUser = false;

      try {
        // 获取当前用户信息
        const currentUser = await apiService.project.user.getCurrentUser();

        // 检查用户是否有权限创建/编辑任务
        isAdmin = currentUser && (currentUser.is_admin || currentUser.is_system_admin || currentUser.is_super_admin);
        isProjectAdmin = currentUser && (currentUser.is_project_admin || currentUser.is_tenant_admin);
        isStoreUser = currentUser && currentUser.roles && currentUser.roles.some(
          role => role.name.includes('门店') || role.name.includes('store')
        );
      } catch (error) {
        console.error('获取当前用户信息失败:', error);
        // 如果获取用户信息失败，默认允许操作，后端会进行权限验证
        isStoreUser = true;
      }

      // 如果是项目管理员，允许创建/编辑任务
      if (isProjectAdmin) {
        isAdmin = true;
      }

      // 如果不是管理员也不是门店部门用户，则不允许创建/编辑任务
      if (!isAdmin && !isStoreUser) {
        message.error('您没有权限创建或编辑门店任务');
        setLoading(false);
        return;
      }

      // 确保任务类别为门店
      if (values.category !== 'store') {
        values.category = 'store';
      }

      // 准备提交数据
      const storeIds = values.store_id || [];
      const isAllStores = storeIds.includes('all');

      // 处理门店选择
      let finalStoreIds = [];
      if (isAllStores) {
        // 如果选择了"所有门店"，获取所有门店ID
        finalStoreIds = stores.map(store => store.id);
      } else {
        // 否则使用选择的门店ID，但要过滤掉可能的 'all' 值
        finalStoreIds = storeIds.filter(id => id !== 'all');
      }

      // 确保至少有一个门店ID
      if (finalStoreIds.length === 0) {
        throw new Error('请至少选择一个门店');
      }

      // 确保日期格式正确
      let startDate = null;
      if (values.start_date) {
        // 使用 startOf('day') 确保只有日期部分
        startDate = values.start_date.startOf('day').format('YYYY-MM-DD');
      } else {
        startDate = dayjs().startOf('day').format('YYYY-MM-DD');
      }

      let dueDate = null;
      if (values.due_date) {
        dueDate = values.due_date.startOf('day').format('YYYY-MM-DD');
      }

      // 根据后端API要求，如果是单个门店，store_id应该是字符串而不是数组
      const taskData = {
        ...values,
        store_id: finalStoreIds.length === 1 ? finalStoreIds[0] : finalStoreIds,
        is_all_stores: isAllStores,
        start_date: startDate,
        due_date: dueDate,
        category: 'store', // 强制设置为门店类别
        attachments: fileList.map(file => file.response?.url || file.url),
        tags: values.task_type ? [values.task_type] : ['daily'] // 使用tags字段存储任务类型
      };

      // 删除task_type字段，因为后端模型中没有这个字段
      if (taskData.task_type) {
        delete taskData.task_type;
      }

      // 打印提交的数据，方便调试
      console.log('提交任务数据:', taskData);

      if (currentTask) {
        // 如果是编辑任务，检查是否有权限编辑
        if (!isAdmin) {
          try {
            // 获取当前用户信息
            const currentUser = await apiService.project.user.getCurrentUser();

            // 检查任务是否分配给自己
            const isAssignee = currentUser && currentTask.assignee_ids && currentTask.assignee_ids.includes(currentUser.id);
            if (!isAssignee && !isStoreUser) {
              message.error('您只能编辑分配给自己的任务');
              setLoading(false);
              return;
            }
          } catch (error) {
            console.error('获取当前用户信息失败:', error);
            // 如果获取用户信息失败，默认允许操作，后端会进行权限验证
          }
        }

        // 更新任务
        await apiService.project.routineTask.update(currentTask.id, taskData);
        message.success('任务更新成功');
      } else {
        // 创建任务
        await apiService.project.routineTask.create(taskData);
        message.success('任务创建成功');
      }

      setModalVisible(false);
      fetchTasks();
    } catch (error) {
      console.error('提交任务失败:', error);
      if (error.errorFields) {
        // 表单验证错误
        return;
      }
      // 显示更详细的错误信息
      if (error.response?.data?.detail) {
        if (Array.isArray(error.response.data.detail)) {
          // 如果是数组形式的错误信息
          const errorDetails = error.response.data.detail.map(err => {
            return `${err.loc.join('.')}：${err.msg}`;
          }).join('; ');
          message.error('提交任务失败: ' + errorDetails);
        } else {
          message.error('提交任务失败: ' + error.response.data.detail);
        }
      } else {
        message.error('提交任务失败: ' + error.message);
      }

      // 打印完整错误信息到控制台，方便调试
      console.error('提交任务失败详细信息:', error.response?.data);
    } finally {
      setLoading(false);
    }
  };

  // 更新任务状态
  const handleUpdateStatus = async (id, status) => {
    setLoading(true);
    try {
      // 调用API更新数据
      await apiService.project.routineTask.updateStatus(id, { status });

      message.success('任务状态更新成功');

      // 重新获取任务列表
      fetchTasks();
    } catch (error) {
      console.error('更新任务状态失败:', error);
      message.error('更新任务状态失败: ' + (error.response?.data?.detail || error.message));
    } finally {
      setLoading(false);
    }
  };

  // 处理文件上传前的验证
  const beforeUpload = (file) => {
    const isLt10M = file.size / 1024 / 1024 < 10;
    if (!isLt10M) {
      message.error('文件大小不能超过10MB!');
    }
    return isLt10M;
  };

  // 处理文件上传变化
  const handleFileChange = ({ fileList: newFileList }) => {
    setFileList(newFileList);
  };

  // 处理标签页切换
  const handleTabChange = (key) => {
    setActiveTab(key);

    // 重置分页
    setPagination({ ...pagination, current: 1 });

    // 更新搜索参数
    const newParams = { ...searchParams, status: key === 'all' ? '' : key };
    setSearchParams(newParams);

    // 重新获取数据
    fetchTasks({ status: key === 'all' ? undefined : key });
  };

  // 切换视图模式
  const handleViewModeChange = (mode) => {
    setViewMode(mode);
  };

  // 初始化
  useEffect(() => {
    fetchStores();
    fetchUsers();
    fetchTasks();
  }, []);

  // 搜索表单
  const SearchForm = () => (
    <Form
      layout="inline"
      form={form}
      initialValues={searchParams}
      onFinish={handleSearch}
      style={{ marginBottom: 16 }}
    >
      <Form.Item name="status" label="状态">
        <Select style={{ width: 120 }} allowClear>
          <Option value="">全部</Option>
          <Option value="pending">待处理</Option>
          <Option value="in_progress">进行中</Option>
          <Option value="completed">已完成</Option>
          <Option value="overdue">已逾期</Option>
          <Option value="cancelled">已取消</Option>
        </Select>
      </Form.Item>

      <Form.Item name="task_type" label="任务类型">
        <Select style={{ width: 120 }} allowClear>
          <Option value="">全部</Option>
          <Option value="daily">每日</Option>
          <Option value="weekly">每周</Option>
          <Option value="monthly">每月</Option>
          <Option value="custom">自定义</Option>
        </Select>
      </Form.Item>

      <Form.Item name="category" label="任务类别">
        <Select style={{ width: 120 }} allowClear>
          <Option value="">全部</Option>
          <Option value="store">门店</Option>
          <Option value="operation">运营</Option>
          <Option value="marketing">营销</Option>
          <Option value="inventory">库存</Option>
          <Option value="finance">财务</Option>
          <Option value="other">其他</Option>
        </Select>
      </Form.Item>

      <Form.Item name="priority" label="优先级">
        <Select style={{ width: 120 }} allowClear>
          <Option value="">全部</Option>
          <Option value="low">低</Option>
          <Option value="medium">中</Option>
          <Option value="high">高</Option>
          <Option value="urgent">紧急</Option>
        </Select>
      </Form.Item>

      <Form.Item name="store_id" label="门店">
        <Select style={{ width: 150 }} allowClear>
          <Option value="">全部</Option>
          {stores.map(store => (
            <Option key={store.id} value={store.id}>{store.name}</Option>
          ))}
        </Select>
      </Form.Item>

      <Form.Item name="dateRange" label="日期范围">
        <RangePicker style={{ width: 240 }} />
      </Form.Item>

      <Form.Item name="search" label="搜索">
        <Input
          placeholder="任务标题/描述"
          prefix={<SearchOutlined />}
          style={{ width: 200 }}
        />
      </Form.Item>

      <Form.Item>
        <Button type="primary" htmlType="submit">
          搜索
        </Button>
      </Form.Item>

      <Form.Item>
        <Button onClick={handleReset}>
          重置
        </Button>
      </Form.Item>
    </Form>
  );

  // 任务表单组件
  const TaskForm = () => (
    <Form
      form={taskForm}
      layout="vertical"
    >
      <Form.Item
        name="title"
        label="任务标题"
        rules={[{ required: true, message: '请输入任务标题' }]}
      >
        <Input placeholder="请输入任务标题" />
      </Form.Item>

      <Form.Item
        name="description"
        label="任务描述"
        rules={[{ required: true, message: '请输入任务描述' }]}
      >
        <TextArea rows={4} placeholder="请输入任务描述" />
      </Form.Item>

      <Row gutter={16}>
        <Col span={12}>
          <Form.Item
            name="store_id"
            label="所属门店"
            rules={[{ required: true, message: '请选择门店' }]}
          >
            <Select
              placeholder="请选择门店"
              allowClear
              showSearch
              optionFilterProp="children"
              onChange={(value) => handleStoreChange(value)}
              mode="multiple"
              maxTagCount={3}
            >
              <Option key="all" value="all">所有门店</Option>
              {stores.map(store => (
                <Option key={store.id} value={store.id}>{store.name}</Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            name="category"
            label="任务类别"
            rules={[{ required: true, message: '请选择任务类别' }]}
            initialValue="store"
          >
            <Select placeholder="请选择任务类别" disabled defaultValue="store">
              <Option value="store">门店</Option>
            </Select>
            <div style={{ color: '#999', fontSize: '12px', marginTop: '4px' }}>
              门店管理模块只能创建门店类别的任务
            </div>
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={12}>
          <Form.Item
            name="task_type"
            label="任务类型"
            rules={[{ required: true, message: '请选择任务类型' }]}
          >
            <Select placeholder="请选择任务类型">
              <Option value="daily">每日任务</Option>
              <Option value="weekly">每周任务</Option>
              <Option value="monthly">每月任务</Option>
              <Option value="custom">自定义任务</Option>
            </Select>
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            name="priority"
            label="优先级"
            rules={[{ required: true, message: '请选择优先级' }]}
          >
            <Select placeholder="请选择优先级">
              <Option value="low">低</Option>
              <Option value="medium">中</Option>
              <Option value="high">高</Option>
              <Option value="urgent">紧急</Option>
            </Select>
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={24}>
          <Form.Item
            name="assignee_ids"
            label="指派人"
            rules={[{ required: true, message: '请选择指派人' }]}
          >
            <Select
              mode="multiple"
              placeholder="请选择指派人"
              optionFilterProp="children"
              loading={loadingUsers}
              showSearch
              maxTagCount={3}
            >
              {filteredUsers.map(user => (
                <Option key={user.id} value={user.id}>
                  {user.name || user.username} {user.store_name ? `(${user.store_name})` : ''}
                </Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={12}>
          <Form.Item
            name="start_date"
            label="开始日期"
            rules={[{ required: true, message: '请选择开始日期' }]}
          >
            <DatePicker
              style={{ width: '100%' }}
              format="YYYY-MM-DD"
              placeholder="请选择开始日期"
              showTime={false} // 不显示时间选择器
              onChange={(date) => {
                if (date) {
                  // 确保只有日期部分
                  const dateOnly = date.startOf('day');
                  taskForm.setFieldsValue({ start_date: dateOnly });
                }
              }}
            />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            name="due_date"
            label="截止日期"
          >
            <DatePicker
              style={{ width: '100%' }}
              format="YYYY-MM-DD"
              placeholder="请选择截止日期"
              showTime={false} // 不显示时间选择器
              onChange={(date) => {
                if (date) {
                  // 确保只有日期部分
                  const dateOnly = date.startOf('day');
                  taskForm.setFieldsValue({ due_date: dateOnly });
                }
              }}
            />
          </Form.Item>
        </Col>
      </Row>

      <Form.Item
        name="attachments"
        label="附件"
      >
        <Upload
          listType="picture"
          fileList={fileList}
          onChange={handleFileChange}
          beforeUpload={beforeUpload}
          action="/api/v1/upload"
          multiple
        >
          <Button icon={<UploadOutlined />}>上传附件</Button>
        </Upload>
      </Form.Item>
    </Form>
  );

  // 表格列定义
  const columns = [
    {
      title: '任务标题',
      dataIndex: 'title',
      key: 'title',
      render: (text, record) => (
        <a onClick={() => handleView(record.id)}>{text}</a>
      )
    },
    {
      title: '门店',
      dataIndex: 'store_name',
      key: 'store_name',
      ellipsis: true,
      render: (text, record) => {
        if (record.is_all_stores) {
          return <Tag color="blue">所有门店</Tag>;
        } else if (record.store_names && record.store_names.length > 0) {
          return (
            <Tooltip title={record.store_names.join(', ')}>
              <span>{record.store_names.length > 1 ? `${record.store_names[0]} 等 ${record.store_names.length} 个门店` : text}</span>
            </Tooltip>
          );
        } else {
          return text || '-';
        }
      }
    },
    {
      title: '类型',
      dataIndex: 'task_type',
      key: 'task_type',
      render: (text) => taskTypeTexts[text] || text
    },
    {
      title: '类别',
      dataIndex: 'category',
      key: 'category',
      render: (text) => categoryTexts[text] || text
    },
    {
      title: '优先级',
      dataIndex: 'priority',
      key: 'priority',
      render: (priority) => (
        <Tag color={priorityColors[priority] || 'default'}>
          {priorityTexts[priority] || priority}
        </Tag>
      )
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={statusColors[status] || 'default'}>
          {statusTexts[status] || status}
        </Tag>
      )
    },
    {
      title: '进度',
      dataIndex: 'checklist',
      key: 'progress',
      render: (checklist) => {
        if (!checklist || checklist.length === 0) return '-';
        const completed = checklist.filter(item => item.completed).length;
        const total = checklist.length;
        const percent = Math.round((completed / total) * 100);
        return (
          <Tooltip title={`${completed}/${total}`}>
            <Progress percent={percent} size="small" />
          </Tooltip>
        );
      }
    },
    {
      title: '指派人',
      dataIndex: 'assignee_names',
      key: 'assignees',
      render: (assignees) => {
        if (!assignees || assignees.length === 0) return '-';
        return (
          <Avatar.Group max={{ count: 3, style: { color: '#f56a00', backgroundColor: '#fde3cf' } }}>
            {assignees.map((name, index) => (
              <Tooltip key={index} title={name}>
                <Avatar style={{ backgroundColor: '#' + Math.floor(Math.random()*16777215).toString(16) }}>
                  {name.substring(0, 1)}
                </Avatar>
              </Tooltip>
            ))}
          </Avatar.Group>
        );
      }
    },
    {
      title: '截止日期',
      dataIndex: 'due_date',
      key: 'due_date',
      render: (text) => {
        if (!text) return '-';
        const dueDate = dayjs(text);
        const today = dayjs();
        const isOverdue = dueDate.isBefore(today, 'day');
        const isToday = dueDate.isSame(today, 'day');

        return (
          <span style={{ color: isOverdue ? '#f5222d' : (isToday ? '#faad14' : 'inherit') }}>
            {dueDate.format('YYYY-MM-DD')}
          </span>
        );
      }
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_, record) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => handleView(record.id)}
          >
            查看
          </Button>

          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEdit(record.id)}
          >
            编辑
          </Button>

          <Dropdown overlay={
            <Menu>
              {record.status !== 'in_progress' && (
                <Menu.Item key="start" onClick={() => handleUpdateStatus(record.id, 'in_progress')}>
                  <ClockCircleOutlined /> 开始任务
                </Menu.Item>
              )}

              {record.status !== 'completed' && (
                <Menu.Item key="complete" onClick={() => handleUpdateStatus(record.id, 'completed')}>
                  <CheckCircleOutlined /> 完成任务
                </Menu.Item>
              )}

              {record.status !== 'cancelled' && (
                <Menu.Item key="cancel" onClick={() => handleUpdateStatus(record.id, 'cancelled')}>
                  <CloseCircleOutlined /> 取消任务
                </Menu.Item>
              )}

              <Menu.Divider />

              <Menu.Item key="delete" danger onClick={() => {
                setCurrentTask(record);
                setDeleteModalVisible(true);
              }}>
                <DeleteOutlined /> 删除任务
              </Menu.Item>
            </Menu>
          }>
            <Button
              type="link"
              size="small"
              icon={<MoreOutlined />}
            />
          </Dropdown>
        </Space>
      )
    }
  ];

  // 日历视图单元格渲染
  const calendarCellRender = (value) => {
    const date = value.format('YYYY-MM-DD');
    const dayTasks = tasks.filter(task => {
      const startDate = dayjs(task.start_date).format('YYYY-MM-DD');
      return startDate === date;
    });

    if (dayTasks.length === 0) {
      return null;
    }

    return (
      <ul className="calendar-task-list">
        {dayTasks.map(task => (
          <li key={task.id} onClick={() => handleView(task.id)}>
            <Badge
              color={priorityColors[task.priority] || 'blue'}
              text={
                <Tooltip title={task.title}>
                  <span className="calendar-task-title">{task.title}</span>
                </Tooltip>
              }
            />
          </li>
        ))}
      </ul>
    );
  };

  return (
    <div className="task-management">
      <Card title="门店任务管理" bordered={false}>
        <Tabs activeKey={activeTab} onChange={handleTabChange}>
          <TabPane tab="任务列表" key="all">
            <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between' }}>
              <div>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={handleCreate}
                  style={{ marginRight: 8 }}
                >
                  新建任务
                </Button>
                <Button
                  icon={<SyncOutlined />}
                  onClick={() => fetchTasks()}
                  style={{ marginRight: 8 }}
                >
                  刷新
                </Button>
                <Radio.Group value={viewMode} onChange={(e) => handleViewModeChange(e.target.value)}>
                  <Radio.Button value="list"><UnorderedListOutlined /> 列表</Radio.Button>
                  <Radio.Button value="calendar"><CalendarOutlined /> 日历</Radio.Button>
                </Radio.Group>
              </div>
            </div>

            {/* 统计卡片 */}
            <Row gutter={16} style={{ marginBottom: 16 }}>
              <Col span={4}>
                <Card>
                  <Statistic
                    title="任务总数"
                    value={stats.total}
                    suffix="个"
                  />
                </Card>
              </Col>
              <Col span={4}>
                <Card>
                  <Statistic
                    title="待处理"
                    value={stats.pending}
                    suffix="个"
                    valueStyle={{ color: '#1890ff' }}
                  />
                </Card>
              </Col>
              <Col span={4}>
                <Card>
                  <Statistic
                    title="进行中"
                    value={stats.in_progress}
                    suffix="个"
                    valueStyle={{ color: '#722ed1' }}
                  />
                </Card>
              </Col>
              <Col span={4}>
                <Card>
                  <Statistic
                    title="已完成"
                    value={stats.completed}
                    suffix="个"
                    valueStyle={{ color: '#52c41a' }}
                  />
                </Card>
              </Col>
              <Col span={4}>
                <Card>
                  <Statistic
                    title="已逾期"
                    value={stats.overdue}
                    suffix="个"
                    valueStyle={{ color: '#f5222d' }}
                  />
                </Card>
              </Col>
              <Col span={4}>
                <Card>
                  <Statistic
                    title="已取消"
                    value={stats.cancelled}
                    suffix="个"
                    valueStyle={{ color: '#faad14' }}
                  />
                </Card>
              </Col>
            </Row>

            {/* 筛选条件 */}
            <Card style={{ marginBottom: 16 }}>
              <Form
                layout="vertical"
                form={form}
                initialValues={searchParams}
                onFinish={handleSearch}
              >
                <Row gutter={16}>
                  <Col span={6}>
                    <Form.Item name="status" label="状态">
                      <Select
                        placeholder="选择状态"
                        allowClear
                        style={{ width: '100%' }}
                      >
                        <Option value="">全部</Option>
                        <Option value="pending">待处理</Option>
                        <Option value="in_progress">进行中</Option>
                        <Option value="completed">已完成</Option>
                        <Option value="overdue">已逾期</Option>
                        <Option value="cancelled">已取消</Option>
                      </Select>
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item name="priority" label="优先级">
                      <Select
                        placeholder="选择优先级"
                        allowClear
                        style={{ width: '100%' }}
                      >
                        <Option value="">全部</Option>
                        <Option value="low">低</Option>
                        <Option value="medium">中</Option>
                        <Option value="high">高</Option>
                        <Option value="urgent">紧急</Option>
                      </Select>
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item name="task_type" label="任务类型">
                      <Select
                        placeholder="选择任务类型"
                        allowClear
                        style={{ width: '100%' }}
                      >
                        <Option value="">全部</Option>
                        <Option value="daily">每日</Option>
                        <Option value="weekly">每周</Option>
                        <Option value="monthly">每月</Option>
                        <Option value="custom">自定义</Option>
                      </Select>
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item name="category" label="任务类别">
                      <Select
                        placeholder="选择类别"
                        allowClear
                        style={{ width: '100%' }}
                      >
                        <Option value="">全部</Option>
                        <Option value="store">门店</Option>
                        <Option value="operation">运营</Option>
                        <Option value="marketing">营销</Option>
                        <Option value="inventory">库存</Option>
                        <Option value="finance">财务</Option>
                        <Option value="other">其他</Option>
                      </Select>
                    </Form.Item>
                  </Col>
                </Row>
                <Row gutter={16}>
                  <Col span={6}>
                    <Form.Item name="store_id" label="门店">
                      <Select
                        placeholder="选择门店"
                        allowClear
                        style={{ width: '100%' }}
                      >
                        <Option value="">全部</Option>
                        {stores.map(store => (
                          <Option key={store.id} value={store.id}>{store.name}</Option>
                        ))}
                      </Select>
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item name="dateRange" label="日期范围">
                      <RangePicker style={{ width: '100%' }} />
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item name="search" label="搜索">
                      <Input
                        placeholder="任务标题/描述"
                        prefix={<SearchOutlined />}
                        style={{ width: '100%' }}
                      />
                    </Form.Item>
                  </Col>
                </Row>
                <Row>
                  <Col span={24} style={{ textAlign: 'right' }}>
                    <Button
                      type="primary"
                      htmlType="submit"
                      style={{ marginRight: 8 }}
                    >
                      搜索
                    </Button>
                    <Button onClick={handleReset}>
                      重置
                    </Button>
                  </Col>
                </Row>
              </Form>
            </Card>

            {viewMode === 'list' ? (
              <Table
                columns={columns}
                dataSource={tasks}
                rowKey="id"
                pagination={{
                  ...pagination,
                  showSizeChanger: true,
                  showTotal: (total) => `共 ${total} 条记录`,
                  pageSizeOptions: ['10', '20', '50']
                }}
                onChange={handleTableChange}
                loading={loading}
                scroll={{ x: 1200 }}
              />
            ) : (
              <Calendar
                dateCellRender={calendarCellRender}
                loading={loading}
              />
            )}
          </TabPane>
          <TabPane tab="任务统计" key="stats">
            <div style={{ padding: '20px 0' }}>
              <Row gutter={16}>
                {storeStats.map(store => (
                  <Col span={8} key={store.store_id} style={{ marginBottom: 16 }}>
                    <Card title={store.store_name}>
                      <Statistic
                        title="完成率"
                        value={store.completion_rate}
                        suffix="%"
                        valueStyle={{ color: store.completion_rate > 80 ? '#52c41a' : (store.completion_rate > 50 ? '#faad14' : '#f5222d') }}
                      />
                      <div style={{ marginTop: 16 }}>
                        <Progress percent={store.completion_rate} status={store.completion_rate === 100 ? 'success' : 'active'} />
                      </div>
                      <div style={{ marginTop: 16 }}>
                        <Row gutter={8}>
                          <Col span={8}>
                            <Statistic title="总任务" value={store.total} valueStyle={{ fontSize: 14 }} />
                          </Col>
                          <Col span={8}>
                            <Statistic title="已完成" value={store.completed} valueStyle={{ fontSize: 14, color: '#52c41a' }} />
                          </Col>
                          <Col span={8}>
                            <Statistic title="进行中" value={store.in_progress} valueStyle={{ fontSize: 14, color: '#1890ff' }} />
                          </Col>
                        </Row>
                      </div>
                    </Card>
                  </Col>
                ))}
              </Row>
            </div>
          </TabPane>
          <TabPane tab={<span><AimOutlined /> 周边调研</span>} key="area-survey">
            <AreaSurvey />
          </TabPane>
          <TabPane tab={<span><GiftOutlined /> 营销活动</span>} key="marketing">
            <MarketingActivities />
          </TabPane>
          <TabPane tab={<span><BookOutlined /> 知识库</span>} key="knowledge">
            <KnowledgeBase />
          </TabPane>
        </Tabs>
      </Card>

      {/* 删除确认弹窗 */}
      <Modal
        title="删除任务"
        open={deleteModalVisible}
        onOk={handleDelete}
        onCancel={() => {
          setDeleteModalVisible(false);
          setCurrentTask(null);
        }}
        confirmLoading={loading}
      >
        <p>确定要删除此任务吗？此操作不可恢复。</p>
        {currentTask && (
          <div>
            <p><strong>任务标题:</strong> {currentTask.title}</p>
            <p><strong>门店:</strong> {currentTask.store_name}</p>
            <p><strong>截止日期:</strong> {currentTask.due_date ? dayjs(currentTask.due_date).format('YYYY-MM-DD') : '无'}</p>
          </div>
        )}
      </Modal>

      {/* 任务表单弹窗 */}
      <Modal
        title={currentTask ? '编辑任务' : '新建任务'}
        open={modalVisible}
        onOk={handleSubmitTask}
        onCancel={() => setModalVisible(false)}
        width={700}
        confirmLoading={loading}
      >
        <TaskForm />
      </Modal>
    </div>
  );
};

export default RoutineTasks;
