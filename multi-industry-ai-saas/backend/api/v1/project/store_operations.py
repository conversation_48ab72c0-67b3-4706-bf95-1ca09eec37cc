#!/usr/bin/env python
# -*- coding: utf-8 -*-

from typing import List, Optional, Dict, Any, Union
from fastapi import APIRouter, Depends, HTTPException, Query, Path, Body, status, UploadFile, File, Form
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import func, and_, or_, desc, asc, case
import uuid
from datetime import datetime, timedelta, timezone
import logging
import pandas as pd
import io

from db.database import get_db
from models.user import User
from models.project import Project
from models.project_user import ProjectUser
from models.store import Store, ProjectUserStore
from models.supplier import Supplier, SupplierProduct
from models.warehouse import Warehouse
from models.product import Product
from models.store_operations import ArrivalConfirmation, ArrivalConfirmationItem, DailyExpense, StoreSalesTarget
from models.purchase_order import PurchaseOrder
from models.inventory import InventoryTransfer
from schemas.store_operations import (
    ArrivalConfirmationCreate, ArrivalConfirmationUpdate, ArrivalConfirmationConfirm,
    ArrivalConfirmationResponse, ArrivalConfirmationListResponse, ArrivalCostSummaryResponse,
    ArrivalConfirmationInDB,
    DailyExpenseCreate, DailyExpenseUpdate, DailyExpenseResponse, DailyExpenseListResponse,
    ExpenseStatisticsResponse,
    StoreSalesTargetCreate, StoreSalesTargetUpdate, StoreSalesTargetResponse, 
    StoreSalesTargetListResponse, StoreSalesTargetInDB
)
from services.storage_service import StorageService
from api.deps import get_current_user, get_current_project_id, get_current_tenant_id

# 配置日志
logger = logging.getLogger(__name__)

router = APIRouter()

# 生成单号
async def generate_arrival_number(db: AsyncSession, prefix: str = "AR") -> str:
    """生成到货单号"""
    today = datetime.now().strftime("%Y%m%d")
    try:
        # 查询今天的最大单号
        result = await db.execute(
            select(func.max(ArrivalConfirmation.arrival_number))
            .where(ArrivalConfirmation.arrival_number.like(f"{prefix}{today}%"))
        )
        max_number = result.scalar_one_or_none()

        if max_number:
            # 提取序号部分并加1
            seq = int(max_number[len(prefix) + 8:]) + 1
        else:
            seq = 1

        # 生成新单号：前缀 + 日期 + 4位序号
        return f"{prefix}{today}{seq:04d}"
    except Exception as e:
        logger.error(f"生成到货单号失败: {str(e)}", exc_info=True)
        # 如果出错，使用时间戳作为备用方案
        timestamp = int(datetime.now().timestamp())
        return f"{prefix}{today}{timestamp % 10000:04d}"

async def generate_expense_number(db: AsyncSession, prefix: str = "EX") -> str:
    """生成费用单号"""
    today = datetime.now().strftime("%Y%m%d")
    try:
        # 查询今天的最大单号
        result = await db.execute(
            select(func.max(DailyExpense.expense_number))
            .where(DailyExpense.expense_number.like(f"{prefix}{today}%"))
        )
        max_number = result.scalar_one_or_none()

        if max_number:
            # 提取序号部分并加1
            seq = int(max_number[len(prefix) + 8:]) + 1
        else:
            seq = 1

        # 生成新单号：前缀 + 日期 + 4位序号
        return f"{prefix}{today}{seq:04d}"
    except Exception as e:
        logger.error(f"生成费用单号失败: {str(e)}", exc_info=True)
        # 如果出错，使用时间戳作为备用方案
        timestamp = int(datetime.now().timestamp())
        return f"{prefix}{today}{timestamp % 10000:04d}"

# 检查用户是否有权限访问门店
async def check_store_permission(db: AsyncSession, user_id: uuid.UUID, project_id: uuid.UUID, store_id: uuid.UUID) -> bool:
    """检查用户是否有权限访问门店"""
    try:
        # 查询用户在项目中的角色
        query = select(ProjectUser).where(
            ProjectUser.user_id == user_id,
            ProjectUser.project_id == project_id
        )
        result = await db.execute(query)
        project_user = result.scalars().first()

        if not project_user:
            return False

        # 如果是项目管理员，有权限访问所有门店
        if project_user.is_admin:
            return True

        # 查询用户关联的门店
        query = select(ProjectUserStore).where(
            ProjectUserStore.project_user_id == project_user.id,
            ProjectUserStore.store_id == store_id
        )
        result = await db.execute(query)
        user_store = result.scalars().first()

        return user_store is not None
    except Exception as e:
        logger.error(f"检查门店权限失败: {str(e)}", exc_info=True)
        # 如果出现异常，默认没有权限
        return False

# 到货确认API
@router.get("/arrivals", response_model=ArrivalConfirmationListResponse)
async def get_arrival_confirmations(
    store_id: Optional[uuid.UUID] = None,
    arrival_type: Optional[str] = None,
    status_filter: Optional[str] = None,  # 重命名参数，避免与导入的status模块冲突
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    page: int = Query(1, ge=1),
    page_size: int = Query(10, ge=1, le=100),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    project_id: uuid.UUID = Depends(get_current_project_id)
):
    """
    获取到货确认列表
    
    Args:
        store_id: 门店ID过滤
        arrival_type: 到货类型过滤
        status_filter: 状态过滤
        start_date: 开始日期
        end_date: 结束日期
        page: 页码
        page_size: 每页大小
        db: 数据库会话
        current_user: 当前用户
        project_id: 项目ID
    
    Returns:
        到货确认列表响应
    """
    try:
        logger.info(f"获取到货确认列表: 项目={project_id}, 门店={store_id}, 状态={status_filter}, 日期范围={start_date}~{end_date}")
        
        # 计算分页偏移量
        skip = (page - 1) * page_size

        # 构建查询条件
        conditions = [ArrivalConfirmation.project_id == project_id]

        # 简化权限检查逻辑 - 与财务管理API保持一致
        try:
            query = select(ProjectUser).where(
                ProjectUser.user_id == current_user.id,
                ProjectUser.project_id == project_id
            )
            result = await db.execute(query)
            project_user = result.scalars().first()
            
            # 如果用户是项目成员或超级管理员，允许访问
            if not project_user and not getattr(current_user, 'is_superuser', False):
                logger.warning(f"用户 {current_user.id} 不是项目成员")
                # 返回空列表而不是抛出异常
                return {
                    "success": True,
                    "message": "获取到货确认列表成功",
                    "items": [],
                    "total": 0,
                    "page": page,
                    "page_size": page_size,
                    "pages": 0,
                    "statistics": {"total": 0, "pending": 0, "confirmed": 0, "rejected": 0}
                }
            
            is_admin = (project_user and project_user.is_admin) or getattr(current_user, 'is_superuser', False)
            logger.info(f"用户权限检查: is_admin={is_admin}, is_superuser={getattr(current_user, 'is_superuser', False)}")
            
        except Exception as e:
            logger.error(f"检查用户权限失败: {str(e)}", exc_info=True)
            # 如果权限检查失败，为了保持与财务管理API一致的行为，允许继续执行
            is_admin = True
            logger.info("权限检查失败，默认允许访问")

        # 如果指定了门店ID且不是管理员，需要检查门店权限
        if store_id and not is_admin:
            has_permission = await check_store_permission(db, current_user.id, project_id, store_id)
            if not has_permission:
                logger.warning(f"用户 {current_user.id} 无权限访问门店 {store_id}")
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="没有权限访问该门店"
                )

        # 应用门店筛选条件
        if store_id:
            conditions.append(ArrivalConfirmation.store_id == store_id)
        elif not is_admin:
            # 非管理员只能查看关联的门店，但如果没有关联门店，返回空列表而不是抛出异常
            try:
                query = (
                    select(ProjectUserStore.store_id)
                    .join(ProjectUser, ProjectUserStore.project_user_id == ProjectUser.id)
                    .where(ProjectUser.user_id == current_user.id, ProjectUser.project_id == project_id)
                )
                result = await db.execute(query)
                store_ids = [row[0] for row in result]

                if not store_ids:
                    logger.info(f"用户 {current_user.id} 没有关联任何门店")
                    return {
                        "success": True,
                        "message": "获取到货确认列表成功",
                        "items": [],
                        "total": 0,
                        "page": page,
                        "page_size": page_size,
                        "pages": 0,
                        "statistics": {"total": 0, "pending": 0, "confirmed": 0, "rejected": 0}
                    }
                conditions.append(ArrivalConfirmation.store_id.in_(store_ids))
                logger.info(f"用户关联门店: {store_ids}")
            except Exception as e:
                logger.error(f"获取用户关联门店失败: {str(e)}", exc_info=True)
                # 如果出现异常，返回空列表
                return {
                    "success": True,
                    "message": "获取到货确认列表成功",
                    "items": [],
                    "total": 0,
                    "page": page,
                    "page_size": page_size,
                    "pages": 0,
                    "statistics": {"total": 0, "pending": 0, "confirmed": 0, "rejected": 0}
                }

        # 应用其他筛选条件
        if arrival_type:
            conditions.append(ArrivalConfirmation.arrival_type == arrival_type)

        if status_filter and status_filter != 'all':
            conditions.append(ArrivalConfirmation.status == status_filter)

        # 日期筛选
        if start_date:
            # 如果是日期字符串，确保从当天开始
            if isinstance(start_date, str):
                try:
                    start_date = datetime.strptime(start_date, '%Y-%m-%d')
                except ValueError:
                    try:
                        start_date = datetime.fromisoformat(start_date.replace('Z', '+00:00'))
                    except ValueError:
                        logger.warning(f"无法解析开始日期: {start_date}")
                        start_date = None
            
            if start_date:
                # 设置为当天的开始时间 (00:00:00)
                start_date = start_date.replace(hour=0, minute=0, second=0, microsecond=0)
                conditions.append(ArrivalConfirmation.arrival_date >= start_date)

        if end_date:
            # 如果是日期字符串，确保到当天结束
            if isinstance(end_date, str):
                try:
                    end_date = datetime.strptime(end_date, '%Y-%m-%d')
                except ValueError:
                    try:
                        end_date = datetime.fromisoformat(end_date.replace('Z', '+00:00'))
                    except ValueError:
                        logger.warning(f"无法解析结束日期: {end_date}")
                        end_date = None
            
            if end_date:
                # 设置为当天的结束时间 (23:59:59.999999)
                end_date = end_date.replace(hour=23, minute=59, second=59, microsecond=999999)
                conditions.append(ArrivalConfirmation.arrival_date <= end_date)

        # 使用*conditions而不是and_(*conditions)
        logger.info(f"查询条件数量: {len(conditions)}")

        try:
            # 查询总数
            count_query = select(func.count()).select_from(ArrivalConfirmation).where(*conditions)
            result = await db.execute(count_query)
            total = result.scalar_one()
            
            logger.info(f"到货确认总数查询结果: {total}")

            # 查询数据
            query = (
                select(
                    ArrivalConfirmation,
                    Store.name.label("store_name"),
                    Supplier.name.label("supplier_name"),
                    Warehouse.name.label("warehouse_name"),
                    PurchaseOrder.order_number.label("purchase_order_number"),
                    PurchaseOrder.id.label("purchase_order_id_ref")
                )
                .outerjoin(Store, ArrivalConfirmation.store_id == Store.id)
                .outerjoin(Supplier, ArrivalConfirmation.supplier_id == Supplier.id)
                .outerjoin(Warehouse, ArrivalConfirmation.warehouse_id == Warehouse.id)
                .outerjoin(PurchaseOrder, ArrivalConfirmation.purchase_order_id == PurchaseOrder.id)
                .where(*conditions)
                .order_by(ArrivalConfirmation.arrival_date.desc())
                .offset(skip)
                .limit(page_size)
            )

            result = await db.execute(query)
            rows = result.all()

            # 计算分页信息
            pages = (total + page_size - 1) // page_size

            # 构建返回数据
            items = []
            for row in rows:
                arrival = row[0]
                store_name = row[1]
                supplier_name = row[2]
                warehouse_name = row[3]
                purchase_order_number = row[4] if len(row) > 4 else None
                purchase_order_id_ref = row[5] if len(row) > 5 else None

                # 创建字典而不是直接修改ORM对象
                arrival_dict = {
                    "id": str(arrival.id),
                    "tenant_id": str(arrival.tenant_id) if arrival.tenant_id else None,
                    "project_id": str(arrival.project_id),
                    "store_id": str(arrival.store_id),
                    "arrival_number": arrival.arrival_number,
                    "arrival_type": arrival.arrival_type,
                    "supplier_id": str(arrival.supplier_id) if arrival.supplier_id else None,
                    "warehouse_id": str(arrival.warehouse_id) if arrival.warehouse_id else None,
                    "purchase_order_id": str(arrival.purchase_order_id) if arrival.purchase_order_id else None,
                    "purchase_order_number": purchase_order_number,
                    "arrival_date": arrival.arrival_date.isoformat() if arrival.arrival_date else None,
                    "status": arrival.status,
                    "total_amount": float(arrival.total_amount) if arrival.total_amount else 0,
                    "total_items": arrival.total_items,
                    "notes": arrival.notes,
                    "created_at": arrival.created_at.isoformat() if arrival.created_at else None,
                    "updated_at": arrival.updated_at.isoformat() if arrival.updated_at else None,
                    "created_by": str(arrival.created_by) if arrival.created_by else None,
                    "updated_by": str(arrival.updated_by) if arrival.updated_by else None,
                    "store_name": store_name,
                    "supplier_name": supplier_name,
                    "warehouse_name": warehouse_name
                }
                items.append(arrival_dict)

            logger.info(f"成功构建返回数据: items={len(items)}")

            # 使用基础条件计算统计信息（不包含分页）
            stats_stmt = select(
                func.count().label('total'),
                func.sum(
                    case((ArrivalConfirmation.status == 'pending', 1), else_=0)
                ).label('pending'),
                func.sum(
                    case((ArrivalConfirmation.status == 'confirmed', 1), else_=0)
                ).label('confirmed'),
                func.sum(
                    case((ArrivalConfirmation.status == 'rejected', 1), else_=0)
                ).label('rejected')
            ).where(*conditions)
            
            stats_result = await db.execute(stats_stmt)
            stats_row = stats_result.first()
            
            statistics = {
                "total": int(stats_row.total or 0),
                "pending": int(stats_row.pending or 0),
                "confirmed": int(stats_row.confirmed or 0),
                "rejected": int(stats_row.rejected or 0)
            }

            response_data = {
                "success": True,
                "message": "获取到货确认列表成功",
                "items": items,
                "total": total,
                "page": page,
                "page_size": page_size,
                "pages": pages,
                "statistics": statistics
            }

            logger.info(f"API响应: total={total}, items={len(items)}, statistics={statistics}")
            return response_data
            
        except Exception as e:
            logger.error(f"查询到货确认列表失败: {str(e)}", exc_info=True)
            # 如果出现异常，返回空列表
            return {
                "success": True,
                "message": "获取到货确认列表成功",
                "items": [],
                "total": 0,
                "page": page,
                "page_size": page_size,
                "pages": 0,
                "statistics": {"total": 0, "pending": 0, "confirmed": 0, "rejected": 0}
            }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取到货确认列表失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取到货确认列表失败: {str(e)}"
        )


@router.get("/arrivals/{arrival_id}", response_model=ArrivalConfirmationResponse)
async def get_arrival_confirmation(
    arrival_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    project_id: uuid.UUID = Depends(get_current_project_id)
):
    """获取到货确认详情"""
    try:
        # 查询到货确认
        query = (
            select(
                ArrivalConfirmation,
                Store.name.label("store_name"),
                Supplier.name.label("supplier_name"),
                Warehouse.name.label("warehouse_name"),
                PurchaseOrder.order_number.label("purchase_order_number")
            )
            .outerjoin(Store, ArrivalConfirmation.store_id == Store.id)
            .outerjoin(Supplier, ArrivalConfirmation.supplier_id == Supplier.id)
            .outerjoin(Warehouse, ArrivalConfirmation.warehouse_id == Warehouse.id)
            .outerjoin(PurchaseOrder, ArrivalConfirmation.purchase_order_id == PurchaseOrder.id)
            .where(
                ArrivalConfirmation.id == arrival_id,
                ArrivalConfirmation.project_id == project_id
            )
        )

        result = await db.execute(query)
        row = result.first()

        if not row:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="到货确认不存在"
            )

        arrival = row[0]
        store_name = row[1]
        supplier_name = row[2]
        warehouse_name = row[3]
        purchase_order_number = row[4] if len(row) > 4 else None

        # 检查用户是否有权限访问该门店
        has_permission = await check_store_permission(db, current_user.id, project_id, arrival.store_id)
        if not has_permission:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有权限访问该门店"
            )

        # 创建字典而不是直接修改ORM对象
        arrival_dict = {
            "id": str(arrival.id),
            "tenant_id": str(arrival.tenant_id) if arrival.tenant_id else None,
            "project_id": str(arrival.project_id),
            "store_id": str(arrival.store_id),
            "arrival_number": arrival.arrival_number,
            "arrival_type": arrival.arrival_type,
            "supplier_id": str(arrival.supplier_id) if arrival.supplier_id else None,
            "warehouse_id": str(arrival.warehouse_id) if arrival.warehouse_id else None,
            "purchase_order_id": str(arrival.purchase_order_id) if arrival.purchase_order_id else None,
            "purchase_order_number": purchase_order_number,
            "arrival_date": arrival.arrival_date.isoformat() if arrival.arrival_date else None,
            "status": arrival.status,
            "total_amount": float(arrival.total_amount) if arrival.total_amount else 0,
            "total_items": arrival.total_items,
            "notes": arrival.notes,
            "created_at": arrival.created_at.isoformat() if arrival.created_at else None,
            "updated_at": arrival.updated_at.isoformat() if arrival.updated_at else None,
            "created_by": str(arrival.created_by) if arrival.created_by else None,
            "updated_by": str(arrival.updated_by) if arrival.updated_by else None,
            "store_name": store_name,
            "supplier_name": supplier_name,
            "warehouse_name": warehouse_name,
            "items": []  # 默认为空列表
        }

        try:
            # 查询明细
            items_query = select(ArrivalConfirmationItem).where(ArrivalConfirmationItem.arrival_id == arrival.id)
            items_result = await db.execute(items_query)
            arrival_items = items_result.scalars().all()

            # 将明细转换为字典
            for item in arrival_items:
                item_dict = {
                    "id": str(item.id),
                    "arrival_id": str(item.arrival_id),
                    "product_id": str(item.product_id) if item.product_id else None,
                    "product_name": item.product_name,
                    "product_code": item.product_code,
                    "specification": item.specification,
                    "unit": item.unit,
                    "expected_quantity": float(item.expected_quantity) if item.expected_quantity else 0,
                    "actual_quantity": float(item.actual_quantity) if item.actual_quantity else 0,
                    "price": float(item.price) if item.price else 0,
                    "amount": float(item.amount) if item.amount else 0,
                    "is_confirmed": item.is_confirmed,
                    "notes": item.notes
                }
                arrival_dict["items"].append(item_dict)
        except Exception as e:
            logger.error(f"获取到货确认明细失败: {str(e)}", exc_info=True)
            # 已经默认为空列表，不需要额外处理

        return {
            "success": True,
            "message": "获取到货确认详情成功",
            "data": arrival_dict
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取到货确认详情失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取到货确认详情失败: {str(e)}"
        )


@router.post("/arrivals", response_model=ArrivalConfirmationResponse, status_code=status.HTTP_201_CREATED)
async def create_arrival_confirmation(
    data: ArrivalConfirmationCreate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    project_id: uuid.UUID = Depends(get_current_project_id),
    tenant_id: Optional[uuid.UUID] = Depends(get_current_tenant_id)
):
    """创建到货确认"""
    try:
        # 检查用户是否有权限访问该门店
        has_permission = await check_store_permission(db, current_user.id, project_id, data.store_id)
        if not has_permission:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有权限访问该门店"
            )

        # 生成到货单号
        arrival_number = await generate_arrival_number(db)

        # 计算总金额和总数量
        total_amount = sum(item.amount for item in data.items)
        total_items = len(data.items)

        # 创建到货确认
        arrival = ArrivalConfirmation(
            tenant_id=tenant_id,
            project_id=project_id,
            store_id=data.store_id,
            arrival_number=arrival_number,
            arrival_type=data.arrival_type,
            supplier_id=data.supplier_id,
            warehouse_id=data.warehouse_id,
            arrival_date=data.arrival_date,
            status="pending",
            total_amount=total_amount,
            total_items=total_items,
            notes=data.notes,
            created_by=current_user.id,
            updated_by=current_user.id
        )

        db.add(arrival)
        await db.flush()

        # 创建到货确认明细
        for item_data in data.items:
            item = ArrivalConfirmationItem(
                arrival_id=arrival.id,
                product_id=item_data.product_id,
                product_name=item_data.product_name,
                product_code=item_data.product_code,
                specification=item_data.specification,
                unit=item_data.unit,
                expected_quantity=item_data.expected_quantity,
                actual_quantity=item_data.actual_quantity,
                price=item_data.price,
                amount=item_data.amount,
                is_confirmed=item_data.is_confirmed,
                notes=item_data.notes
            )
            db.add(item)

        await db.commit()

        # 查询创建后的完整数据
        query = (
            select(
                ArrivalConfirmation,
                Store.name.label("store_name"),
                Supplier.name.label("supplier_name"),
                Warehouse.name.label("warehouse_name")
            )
            .outerjoin(Store, ArrivalConfirmation.store_id == Store.id)
            .outerjoin(Supplier, ArrivalConfirmation.supplier_id == Supplier.id)
            .outerjoin(Warehouse, ArrivalConfirmation.warehouse_id == Warehouse.id)
            .where(ArrivalConfirmation.id == arrival.id)
        )

        result = await db.execute(query)
        row = result.first()

        arrival = row[0]
        store_name = row[1]
        supplier_name = row[2]
        warehouse_name = row[3]

        # 创建字典而不是直接修改ORM对象
        arrival_dict = {
            "id": str(arrival.id),
            "tenant_id": str(arrival.tenant_id) if arrival.tenant_id else None,
            "project_id": str(arrival.project_id),
            "store_id": str(arrival.store_id),
            "arrival_number": arrival.arrival_number,
            "arrival_type": arrival.arrival_type,
            "supplier_id": str(arrival.supplier_id) if arrival.supplier_id else None,
            "warehouse_id": str(arrival.warehouse_id) if arrival.warehouse_id else None,
            "arrival_date": arrival.arrival_date.isoformat() if arrival.arrival_date else None,
            "status": arrival.status,
            "total_amount": float(arrival.total_amount) if arrival.total_amount else 0,
            "total_items": arrival.total_items,
            "notes": arrival.notes,
            "created_at": arrival.created_at.isoformat() if arrival.created_at else None,
            "updated_at": arrival.updated_at.isoformat() if arrival.updated_at else None,
            "created_by": str(arrival.created_by) if arrival.created_by else None,
            "updated_by": str(arrival.updated_by) if arrival.updated_by else None,
            "store_name": store_name,
            "supplier_name": supplier_name,
            "warehouse_name": warehouse_name,
            "items": []  # 默认为空列表
        }

        try:
            # 查询明细
            items_query = select(ArrivalConfirmationItem).where(ArrivalConfirmationItem.arrival_id == arrival.id)
            items_result = await db.execute(items_query)
            arrival_items = items_result.scalars().all()

            # 将明细转换为字典
            for item in arrival_items:
                item_dict = {
                    "id": str(item.id),
                    "arrival_id": str(item.arrival_id),
                    "product_id": str(item.product_id) if item.product_id else None,
                    "product_name": item.product_name,
                    "product_code": item.product_code,
                    "specification": item.specification,
                    "unit": item.unit,
                    "expected_quantity": float(item.expected_quantity) if item.expected_quantity else 0,
                    "actual_quantity": float(item.actual_quantity) if item.actual_quantity else 0,
                    "price": float(item.price) if item.price else 0,
                    "amount": float(item.amount) if item.amount else 0,
                    "is_confirmed": item.is_confirmed,
                    "notes": item.notes
                }
                arrival_dict["items"].append(item_dict)
        except Exception as e:
            logger.error(f"获取到货确认明细失败: {str(e)}", exc_info=True)
            # 已经默认为空列表，不需要额外处理

        return {
            "success": True,
            "message": "创建到货确认成功",
            "data": arrival_dict
        }
    except HTTPException:
        await db.rollback()
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"创建到货确认失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建到货确认失败: {str(e)}"
        )


@router.get("/suppliers", response_model=Dict[str, Any])
async def get_suppliers_for_arrival(
    keyword: Optional[str] = None,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    project_id: uuid.UUID = Depends(get_current_project_id)
):
    """获取供应商列表，用于到货确认"""
    try:
        # 构建查询条件
        conditions = [Supplier.project_id == project_id]

        if keyword:
            conditions.append(
                or_(
                    Supplier.name.ilike(f"%{keyword}%"),
                    Supplier.contact_person.ilike(f"%{keyword}%")
                )
            )

        # 查询供应商
        query = (
            select(Supplier)
            .where(*conditions)
            .order_by(Supplier.name)
            .limit(20)  # 限制返回数量
        )

        result = await db.execute(query)
        suppliers = result.scalars().all()

        # 转换为响应格式
        supplier_list = []
        for supplier in suppliers:
            supplier_list.append({
                "id": str(supplier.id),
                "name": supplier.name,
                "code": "",  # 供应商没有code字段，返回空字符串
                "contact_person": supplier.contact_person,
                "contact_phone": supplier.contact_phone
            })

        return {
            "success": True,
            "message": "获取供应商列表成功",
            "items": supplier_list
        }
    except Exception as e:
        logger.error(f"获取供应商列表失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取供应商列表失败: {str(e)}"
        )


@router.get("/products", response_model=Dict[str, Any])
async def get_products_for_arrival(
    keyword: Optional[str] = None,
    supplier_id: Optional[uuid.UUID] = None,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    project_id: uuid.UUID = Depends(get_current_project_id)
):
    """获取产品列表，用于到货确认"""
    try:
        # 构建查询条件
        conditions = [Product.project_id == project_id]

        if keyword:
            conditions.append(
                or_(
                    Product.name.ilike(f"%{keyword}%"),
                    Product.sku.ilike(f"%{keyword}%"),
                    Product.barcode.ilike(f"%{keyword}%")
                )
            )

        # 查询产品
        query = (
            select(Product)
            .where(*conditions)
            .order_by(Product.name)
            .limit(20)  # 限制返回数量
        )

        result = await db.execute(query)
        products = result.scalars().all()

        # 如果指定了供应商，查询供应商产品关联
        supplier_products = {}
        if supplier_id:
            sp_query = (
                select(SupplierProduct)
                .where(
                    SupplierProduct.supplier_id == supplier_id,
                    SupplierProduct.project_id == project_id
                )
            )
            sp_result = await db.execute(sp_query)
            sp_items = sp_result.scalars().all()

            for sp in sp_items:
                supplier_products[str(sp.product_id)] = {
                    "price": sp.price,
                    "unit": sp.unit
                }

        # 转换为响应格式
        product_list = []
        for product in products:
            product_data = {
                "id": str(product.id),
                "name": product.name,
                "code": product.sku,  # 使用sku作为code
                "barcode": product.barcode,
                "specification": product.specification,
                "unit": product.unit,
                "price": product.cost_price or 0  # 使用成本价，如果为空则默认为0
            }

            # 如果有供应商产品信息，使用供应商产品的价格和单位
            if str(product.id) in supplier_products:
                sp_info = supplier_products[str(product.id)]
                product_data["price"] = sp_info["price"]
                if sp_info["unit"]:
                    product_data["unit"] = sp_info["unit"]

            product_list.append(product_data)

        return {
            "success": True,
            "message": "获取产品列表成功",
            "items": product_list
        }
    except Exception as e:
        logger.error(f"获取产品列表失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取产品列表失败: {str(e)}"
        )


@router.post("/arrivals/{arrival_id}/confirm", response_model=ArrivalConfirmationResponse)
async def confirm_arrival(
    arrival_id: uuid.UUID,
    data: ArrivalConfirmationConfirm,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    project_id: uuid.UUID = Depends(get_current_project_id)
):
    """确认到货"""
    try:
        # 查询到货确认
        query = select(ArrivalConfirmation).where(
            ArrivalConfirmation.id == arrival_id,
            ArrivalConfirmation.project_id == project_id
        )

        result = await db.execute(query)
        arrival = result.scalar_one_or_none()

        if not arrival:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="到货确认不存在"
            )

        # 检查状态
        if arrival.status != "pending":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"当前状态({arrival.status})不允许确认操作"
            )

        # 检查用户是否有权限访问该门店
        has_permission = await check_store_permission(db, current_user.id, project_id, arrival.store_id)
        if not has_permission:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有权限访问该门店"
            )

        # 更新到货确认状态
        arrival.status = "confirmed"
        arrival.notes = data.notes or arrival.notes
        arrival.updated_by = current_user.id
        arrival.updated_at = datetime.now(timezone.utc)

        # 更新明细
        total_amount = 0
        confirmed_items = 0

        for item_data in data.items:
            item_id = item_data.get("id")
            actual_quantity = item_data.get("actual_quantity")
            is_confirmed = item_data.get("is_confirmed", True)

            # 查询明细
            item_query = select(ArrivalConfirmationItem).where(
                ArrivalConfirmationItem.id == item_id,
                ArrivalConfirmationItem.arrival_id == arrival_id
            )
            item_result = await db.execute(item_query)
            item = item_result.scalar_one_or_none()

            if item:
                item.actual_quantity = actual_quantity
                item.is_confirmed = is_confirmed
                item.amount = actual_quantity * item.price

                if is_confirmed:
                    total_amount += item.amount
                    confirmed_items += 1

        # 更新总金额和总数量
        arrival.total_amount = total_amount
        arrival.total_items = confirmed_items

        await db.commit()

        # 查询更新后的完整数据
        query = (
            select(
                ArrivalConfirmation,
                Store.name.label("store_name"),
                Supplier.name.label("supplier_name"),
                Warehouse.name.label("warehouse_name")
            )
            .outerjoin(Store, ArrivalConfirmation.store_id == Store.id)
            .outerjoin(Supplier, ArrivalConfirmation.supplier_id == Supplier.id)
            .outerjoin(Warehouse, ArrivalConfirmation.warehouse_id == Warehouse.id)
            .where(ArrivalConfirmation.id == arrival.id)
        )

        result = await db.execute(query)
        row = result.first()

        arrival = row[0]
        store_name = row[1]
        supplier_name = row[2]
        warehouse_name = row[3]

        # 创建字典而不是直接修改ORM对象
        arrival_dict = {
            "id": str(arrival.id),
            "tenant_id": str(arrival.tenant_id) if arrival.tenant_id else None,
            "project_id": str(arrival.project_id),
            "store_id": str(arrival.store_id),
            "arrival_number": arrival.arrival_number,
            "arrival_type": arrival.arrival_type,
            "supplier_id": str(arrival.supplier_id) if arrival.supplier_id else None,
            "warehouse_id": str(arrival.warehouse_id) if arrival.warehouse_id else None,
            "arrival_date": arrival.arrival_date.isoformat() if arrival.arrival_date else None,
            "status": arrival.status,
            "total_amount": float(arrival.total_amount) if arrival.total_amount else 0,
            "total_items": arrival.total_items,
            "notes": arrival.notes,
            "created_at": arrival.created_at.isoformat() if arrival.created_at else None,
            "updated_at": arrival.updated_at.isoformat() if arrival.updated_at else None,
            "created_by": str(arrival.created_by) if arrival.created_by else None,
            "updated_by": str(arrival.updated_by) if arrival.updated_by else None,
            "store_name": store_name,
            "supplier_name": supplier_name,
            "warehouse_name": warehouse_name,
            "items": []  # 默认为空列表
        }

        try:
            # 查询明细
            items_query = select(ArrivalConfirmationItem).where(ArrivalConfirmationItem.arrival_id == arrival.id)
            items_result = await db.execute(items_query)
            arrival_items = items_result.scalars().all()

            # 将明细转换为字典
            for item in arrival_items:
                item_dict = {
                    "id": str(item.id),
                    "arrival_id": str(item.arrival_id),
                    "product_id": str(item.product_id) if item.product_id else None,
                    "product_name": item.product_name,
                    "product_code": item.product_code,
                    "specification": item.specification,
                    "unit": item.unit,
                    "expected_quantity": float(item.expected_quantity) if item.expected_quantity else 0,
                    "actual_quantity": float(item.actual_quantity) if item.actual_quantity else 0,
                    "price": float(item.price) if item.price else 0,
                    "amount": float(item.amount) if item.amount else 0,
                    "is_confirmed": item.is_confirmed,
                    "notes": item.notes
                }
                arrival_dict["items"].append(item_dict)
        except Exception as e:
            logger.error(f"获取到货确认明细失败: {str(e)}", exc_info=True)
            # 已经默认为空列表，不需要额外处理

        return {
            "success": True,
            "message": "确认到货成功",
            "data": arrival_dict
        }
    except HTTPException:
        await db.rollback()
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"确认到货失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"确认到货失败: {str(e)}"
        )


@router.post("/arrivals/{arrival_id}/reject", response_model=ArrivalConfirmationResponse)
async def reject_arrival(
    arrival_id: uuid.UUID,
    notes: Optional[str] = Body(None),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    project_id: uuid.UUID = Depends(get_current_project_id)
):
    """拒绝到货"""
    try:
        # 查询到货确认
        query = select(ArrivalConfirmation).where(
            ArrivalConfirmation.id == arrival_id,
            ArrivalConfirmation.project_id == project_id
        )

        result = await db.execute(query)
        arrival = result.scalar_one_or_none()

        if not arrival:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="到货确认不存在"
            )

        # 检查状态
        if arrival.status != "pending":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"当前状态({arrival.status})不允许拒绝操作"
            )

        # 检查用户是否有权限访问该门店
        has_permission = await check_store_permission(db, current_user.id, project_id, arrival.store_id)
        if not has_permission:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有权限访问该门店"
            )

        # 更新到货确认状态
        arrival.status = "rejected"
        if notes:
            arrival.notes = notes
        arrival.updated_by = current_user.id
        arrival.updated_at = datetime.now(timezone.utc)

        await db.commit()

        # 查询更新后的完整数据
        query = (
            select(
                ArrivalConfirmation,
                Store.name.label("store_name"),
                Supplier.name.label("supplier_name"),
                Warehouse.name.label("warehouse_name")
            )
            .outerjoin(Store, ArrivalConfirmation.store_id == Store.id)
            .outerjoin(Supplier, ArrivalConfirmation.supplier_id == Supplier.id)
            .outerjoin(Warehouse, ArrivalConfirmation.warehouse_id == Warehouse.id)
            .where(ArrivalConfirmation.id == arrival.id)
        )

        result = await db.execute(query)
        row = result.first()

        arrival = row[0]
        arrival.store_name = row[1]
        arrival.supplier_name = row[2]
        arrival.warehouse_name = row[3]

        # 查询明细
        items_query = select(ArrivalConfirmationItem).where(ArrivalConfirmationItem.arrival_id == arrival.id)
        items_result = await db.execute(items_query)
        arrival.items = items_result.scalars().all()

        return {
            "success": True,
            "message": "拒绝到货成功",
            "data": arrival
        }
    except HTTPException:
        await db.rollback()
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"拒绝到货失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"拒绝到货失败: {str(e)}"
        )


@router.get("/arrivals-cost-summary", response_model=ArrivalCostSummaryResponse)
async def get_arrival_cost_summary(
    store_id: Optional[uuid.UUID] = None,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    project_id: uuid.UUID = Depends(get_current_project_id)
):
    """获取到货成本汇总"""
    try:
        # 构建查询条件
        conditions = [
            ArrivalConfirmation.project_id == project_id,
            ArrivalConfirmation.status == "confirmed"  # 只统计已确认的到货
        ]

        # 保存基础条件（用于统计查询）
        base_conditions = conditions.copy()

        # 简化权限检查逻辑 - 与财务管理API保持一致
        try:
            query = select(ProjectUser).where(
                ProjectUser.user_id == current_user.id,
                ProjectUser.project_id == project_id
            )
            result = await db.execute(query)
            project_user = result.scalars().first()
            
            # 如果用户是项目成员或超级管理员，允许访问
            if not project_user and not getattr(current_user, 'is_superuser', False):
                logger.warning(f"用户 {current_user.id} 不是项目成员")
                # 返回空数据而不是抛出异常
                return {
                    "success": True,
                    "message": "获取到货成本汇总成功",
                    "data": {
                        "daily": {
                            "market": 0,
                            "warehouse": 0,
                            "third_party": 0,
                            "total": 0
                        },
                        "monthly": {
                            "market": 0,
                            "warehouse": 0,
                            "third_party": 0,
                            "total": 0
                        }
                    }
                }
            
            is_admin = (project_user and project_user.is_admin) or getattr(current_user, 'is_superuser', False)
            logger.info(f"用户权限检查: is_admin={is_admin}, is_superuser={getattr(current_user, 'is_superuser', False)}")
            
        except Exception as e:
            logger.error(f"检查用户权限失败: {str(e)}", exc_info=True)
            # 如果权限检查失败，为了保持与财务管理API一致的行为，允许继续执行
            is_admin = True
            logger.info("权限检查失败，默认允许访问")

        # 应用门店筛选条件
        if store_id:
            conditions.append(ArrivalConfirmation.store_id == store_id)
            base_conditions.append(ArrivalConfirmation.store_id == store_id)
        elif not is_admin:
            # 非管理员只能查看关联的门店，但如果没有关联门店，返回空数据而不是抛出异常
            try:
                query = (
                    select(ProjectUserStore.store_id)
                    .join(ProjectUser, ProjectUserStore.project_user_id == ProjectUser.id)
                    .where(ProjectUser.user_id == current_user.id, ProjectUser.project_id == project_id)
                )
                result = await db.execute(query)
                store_ids = [row[0] for row in result]

                if not store_ids:
                    logger.info(f"用户 {current_user.id} 没有关联任何门店")
                    return {
                        "success": True,
                        "message": "获取到货成本汇总成功",
                        "data": {
                            "daily": {
                                "market": 0,
                                "warehouse": 0,
                                "third_party": 0,
                                "total": 0
                            },
                            "monthly": {
                                "market": 0,
                                "warehouse": 0,
                                "third_party": 0,
                                "total": 0
                            }
                        }
                    }
                store_condition = ArrivalConfirmation.store_id.in_(store_ids)
                conditions.append(store_condition)
                base_conditions.append(store_condition)
            except Exception as e:
                logger.error(f"获取用户关联门店失败: {str(e)}", exc_info=True)
                # 如果出现异常，返回空数据
                return {
                    "success": True,
                    "message": "获取到货成本汇总成功",
                    "data": {
                        "daily": {
                            "market": 0,
                            "warehouse": 0,
                            "third_party": 0,
                            "total": 0
                        },
                        "monthly": {
                            "market": 0,
                            "warehouse": 0,
                            "third_party": 0,
                            "total": 0
                        }
                    }
                }

        # 应用日期筛选条件
        if start_date:
            conditions.append(ArrivalConfirmation.arrival_date >= start_date)

        if end_date:
            conditions.append(ArrivalConfirmation.arrival_date <= end_date)

        # 获取当前日期
        today = datetime.now().date()
        first_day_of_month = datetime(today.year, today.month, 1)

        # 查询日汇总数据
        daily_conditions = conditions.copy()
        daily_conditions.append(func.date(ArrivalConfirmation.arrival_date) == today)

        daily_query = (
            select(
                ArrivalConfirmation.arrival_type,
                func.sum(ArrivalConfirmation.total_amount).label("total_amount")
            )
            .where(*daily_conditions)
            .group_by(ArrivalConfirmation.arrival_type)
        )

        daily_result = await db.execute(daily_query)
        daily_rows = daily_result.all()

        # 处理日汇总数据
        daily_summary = {
            "market": 0,
            "warehouse": 0,
            "third_party": 0,
            "total": 0
        }

        for row in daily_rows:
            arrival_type = row[0]
            total_amount = float(row[1]) if row[1] else 0

            daily_summary[arrival_type] = total_amount
            daily_summary["total"] += total_amount

        # 查询月汇总数据
        monthly_conditions = conditions.copy()
        monthly_conditions.append(ArrivalConfirmation.arrival_date >= first_day_of_month)

        monthly_query = (
            select(
                ArrivalConfirmation.arrival_type,
                func.sum(ArrivalConfirmation.total_amount).label("total_amount")
            )
            .where(*monthly_conditions)
            .group_by(ArrivalConfirmation.arrival_type)
        )

        monthly_result = await db.execute(monthly_query)
        monthly_rows = monthly_result.all()

        # 处理月汇总数据
        monthly_summary = {
            "market": 0,
            "warehouse": 0,
            "third_party": 0,
            "total": 0
        }

        for row in monthly_rows:
            arrival_type = row[0]
            total_amount = float(row[1]) if row[1] else 0

            monthly_summary[arrival_type] = total_amount
            monthly_summary["total"] += total_amount

        return {
            "success": True,
            "message": "获取到货成本汇总成功",
            "data": {
                "daily": daily_summary,
                "monthly": monthly_summary
            }
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取到货成本汇总失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取到货成本汇总失败: {str(e)}"
        )


# 日常费用API
@router.get("/expenses", response_model=DailyExpenseListResponse)
async def get_daily_expenses(
    store_id: Optional[uuid.UUID] = None,
    expense_type: Optional[str] = None,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    page: int = Query(1, ge=1),
    page_size: int = Query(10, ge=1, le=100),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    project_id: uuid.UUID = Depends(get_current_project_id)
):
    """获取日常费用列表"""
    try:
        # 计算分页偏移量
        skip = (page - 1) * page_size

        # 构建查询条件
        conditions = [DailyExpense.project_id == project_id]

        # 简化权限检查逻辑 - 与财务管理API保持一致
        try:
            query = select(ProjectUser).where(
                ProjectUser.user_id == current_user.id,
                ProjectUser.project_id == project_id
            )
            result = await db.execute(query)
            project_user = result.scalars().first()
            
            # 如果用户是项目成员或超级管理员，允许访问
            if not project_user and not getattr(current_user, 'is_superuser', False):
                logger.warning(f"用户 {current_user.id} 不是项目成员")
                # 返回空列表而不是抛出异常
                return {
                    "success": True,
                    "message": "获取日常费用列表成功",
                    "items": [],
                    "total": 0,
                    "page": page,
                    "page_size": page_size,
                    "pages": 0
                }
            
            is_admin = (project_user and project_user.is_admin) or getattr(current_user, 'is_superuser', False)
            logger.info(f"用户权限检查: is_admin={is_admin}, is_superuser={getattr(current_user, 'is_superuser', False)}")
            
        except Exception as e:
            logger.error(f"检查用户权限失败: {str(e)}", exc_info=True)
            # 如果权限检查失败，为了保持与财务管理API一致的行为，允许继续执行
            is_admin = True
            logger.info("权限检查失败，默认允许访问")

        # 应用门店筛选条件
        if store_id:
            conditions.append(DailyExpense.store_id == store_id)
        elif not is_admin:
            # 非管理员只能查看关联的门店，但如果没有关联门店，返回空列表而不是抛出异常
            try:
                query = (
                    select(ProjectUserStore.store_id)
                    .join(ProjectUser, ProjectUserStore.project_user_id == ProjectUser.id)
                    .where(ProjectUser.user_id == current_user.id, ProjectUser.project_id == project_id)
                )
                result = await db.execute(query)
                store_ids = [row[0] for row in result]

                if not store_ids:
                    logger.info(f"用户 {current_user.id} 没有关联任何门店")
                    return {
                        "success": True,
                        "message": "获取日常费用列表成功",
                        "items": [],
                        "total": 0,
                        "page": page,
                        "page_size": page_size,
                        "pages": 0
                    }
                conditions.append(DailyExpense.store_id.in_(store_ids))
            except Exception as e:
                logger.error(f"获取用户关联门店失败: {str(e)}", exc_info=True)
                # 如果出现异常，返回空列表
                return {
                    "success": True,
                    "message": "获取日常费用列表成功",
                    "items": [],
                    "total": 0,
                    "page": page,
                    "page_size": page_size,
                    "pages": 0
                }

        # 应用其他筛选条件
        if expense_type:
            conditions.append(DailyExpense.expense_type == expense_type)

        if start_date:
            conditions.append(DailyExpense.expense_date >= start_date)

        if end_date:
            conditions.append(DailyExpense.expense_date <= end_date)

        # 查询总数
        count_query = select(func.count()).select_from(DailyExpense).where(*conditions)
        result = await db.execute(count_query)
        total = result.scalar_one()

        # 查询数据
        query = (
            select(
                DailyExpense,
                Store.name.label("store_name")
            )
            .outerjoin(Store, DailyExpense.store_id == Store.id)
            .where(*conditions)
            .order_by(desc(DailyExpense.created_at))
            .offset(skip)
            .limit(page_size)
        )

        result = await db.execute(query)
        rows = result.all()

        # 处理结果
        items = []
        for row in rows:
            expense = row[0]
            expense.store_name = row[1]
            items.append(expense)

        # 计算总页数
        pages = (total + page_size - 1) // page_size

        return {
            "success": True,
            "message": "获取日常费用列表成功",
            "items": items,
            "total": total,
            "page": page,
            "page_size": page_size,
            "pages": pages
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取日常费用列表失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取日常费用列表失败: {str(e)}"
        )


@router.get("/expenses/{expense_id}", response_model=DailyExpenseResponse)
async def get_daily_expense(
    expense_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    project_id: uuid.UUID = Depends(get_current_project_id)
):
    """获取日常费用详情"""
    try:
        # 查询日常费用
        query = (
            select(
                DailyExpense,
                Store.name.label("store_name")
            )
            .outerjoin(Store, DailyExpense.store_id == Store.id)
            .where(
                DailyExpense.id == expense_id,
                DailyExpense.project_id == project_id
            )
        )

        result = await db.execute(query)
        row = result.first()

        if not row:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="日常费用不存在"
            )

        expense = row[0]
        expense.store_name = row[1]

        # 检查用户是否有权限访问该门店
        has_permission = await check_store_permission(db, current_user.id, project_id, expense.store_id)
        if not has_permission:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有权限访问该门店"
            )

        return {
            "success": True,
            "message": "获取日常费用详情成功",
            "data": expense
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取日常费用详情失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取日常费用详情失败: {str(e)}"
        )


@router.post("/expenses", response_model=DailyExpenseResponse, status_code=status.HTTP_201_CREATED)
async def create_daily_expense(
    data: DailyExpenseCreate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    project_id: uuid.UUID = Depends(get_current_project_id),
    tenant_id: Optional[uuid.UUID] = Depends(get_current_tenant_id)
):
    """创建日常费用"""
    try:
        # 检查用户是否有权限访问该门店
        has_permission = await check_store_permission(db, current_user.id, project_id, data.store_id)
        if not has_permission:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有权限访问该门店"
            )

        # 生成费用单号
        expense_number = await generate_expense_number(db)

        # 创建日常费用
        expense = DailyExpense(
            tenant_id=tenant_id,
            project_id=project_id,
            store_id=data.store_id,
            expense_number=expense_number,
            expense_type=data.expense_type,
            expense_date=data.expense_date,
            amount=data.amount,
            description=data.description,
            receipt_url=data.receipt_url,
            has_receipt=data.has_receipt,
            created_by=current_user.id,
            updated_by=current_user.id
        )

        db.add(expense)
        await db.commit()

        # 查询创建后的完整数据
        query = (
            select(
                DailyExpense,
                Store.name.label("store_name")
            )
            .outerjoin(Store, DailyExpense.store_id == Store.id)
            .where(DailyExpense.id == expense.id)
        )

        result = await db.execute(query)
        row = result.first()

        expense = row[0]
        expense.store_name = row[1]

        return {
            "success": True,
            "message": "创建日常费用成功",
            "data": expense
        }
    except HTTPException:
        await db.rollback()
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"创建日常费用失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建日常费用失败: {str(e)}"
        )


@router.put("/expenses/{expense_id}", response_model=DailyExpenseResponse)
async def update_daily_expense(
    expense_id: uuid.UUID,
    data: DailyExpenseUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    project_id: uuid.UUID = Depends(get_current_project_id)
):
    """更新日常费用"""
    try:
        # 查询日常费用
        query = select(DailyExpense).where(
            DailyExpense.id == expense_id,
            DailyExpense.project_id == project_id
        )

        result = await db.execute(query)
        expense = result.scalar_one_or_none()

        if not expense:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="日常费用不存在"
            )

        # 检查用户是否有权限访问该门店
        has_permission = await check_store_permission(db, current_user.id, project_id, expense.store_id)
        if not has_permission:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有权限访问该门店"
            )

        # 更新日常费用
        if data.expense_type is not None:
            expense.expense_type = data.expense_type

        if data.expense_date is not None:
            expense.expense_date = data.expense_date

        if data.amount is not None:
            expense.amount = data.amount

        if data.description is not None:
            expense.description = data.description

        if data.receipt_url is not None:
            expense.receipt_url = data.receipt_url

        if data.has_receipt is not None:
            expense.has_receipt = data.has_receipt

        expense.updated_by = current_user.id
        expense.updated_at = datetime.now(timezone.utc)

        await db.commit()

        # 查询更新后的完整数据
        query = (
            select(
                DailyExpense,
                Store.name.label("store_name")
            )
            .outerjoin(Store, DailyExpense.store_id == Store.id)
            .where(DailyExpense.id == expense.id)
        )

        result = await db.execute(query)
        row = result.first()

        expense = row[0]
        expense.store_name = row[1]

        return {
            "success": True,
            "message": "更新日常费用成功",
            "data": expense
        }
    except HTTPException:
        await db.rollback()
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"更新日常费用失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新日常费用失败: {str(e)}"
        )


@router.delete("/expenses/{expense_id}", response_model=DailyExpenseResponse)
async def delete_daily_expense(
    expense_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    project_id: uuid.UUID = Depends(get_current_project_id)
):
    """删除日常费用"""
    try:
        # 查询日常费用
        query = select(DailyExpense).where(
            DailyExpense.id == expense_id,
            DailyExpense.project_id == project_id
        )

        result = await db.execute(query)
        expense = result.scalar_one_or_none()

        if not expense:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="日常费用不存在"
            )

        # 检查用户是否有权限访问该门店
        has_permission = await check_store_permission(db, current_user.id, project_id, expense.store_id)
        if not has_permission:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有权限访问该门店"
            )

        # 保存费用信息用于返回
        store_id = expense.store_id

        # 删除日常费用
        await db.delete(expense)
        await db.commit()

        # 查询门店信息
        store_query = select(Store).where(Store.id == store_id)
        store_result = await db.execute(store_query)
        store = store_result.scalar_one_or_none()
        store_name = store.name if store else None

        # 构建返回数据
        deleted_expense = DailyExpense(
            id=expense_id,
            tenant_id=expense.tenant_id,
            project_id=project_id,
            store_id=store_id,
            expense_number=expense.expense_number,
            expense_type=expense.expense_type,
            expense_date=expense.expense_date,
            amount=expense.amount,
            description=expense.description,
            receipt_url=expense.receipt_url,
            has_receipt=expense.has_receipt,
            created_by=expense.created_by,
            updated_by=current_user.id,
            created_at=expense.created_at,
            updated_at=datetime.now(timezone.utc)
        )
        deleted_expense.store_name = store_name

        return {
            "success": True,
            "message": "删除日常费用成功",
            "data": deleted_expense
        }
    except HTTPException:
        await db.rollback()
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"删除日常费用失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除日常费用失败: {str(e)}"
        )


@router.get("/expenses-statistics", response_model=ExpenseStatisticsResponse)
async def get_expense_statistics(
    store_id: Optional[uuid.UUID] = None,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    project_id: uuid.UUID = Depends(get_current_project_id)
):
    """获取费用统计"""
    try:
        # 构建查询条件
        conditions = [DailyExpense.project_id == project_id]

        # 简化权限检查逻辑 - 与财务管理API保持一致
        try:
            query = select(ProjectUser).where(
                ProjectUser.user_id == current_user.id,
                ProjectUser.project_id == project_id
            )
            result = await db.execute(query)
            project_user = result.scalars().first()
            
            # 如果用户是项目成员或超级管理员，允许访问
            if not project_user and not getattr(current_user, 'is_superuser', False):
                logger.warning(f"用户 {current_user.id} 不是项目成员")
                # 返回空数据而不是抛出异常
                return {
                    "success": True,
                    "message": "获取费用统计成功",
                    "data": {
                        "total": 0,
                        "by_type": {},
                        "by_month": {}
                    }
                }
            
            is_admin = (project_user and project_user.is_admin) or getattr(current_user, 'is_superuser', False)
            logger.info(f"用户权限检查: is_admin={is_admin}, is_superuser={getattr(current_user, 'is_superuser', False)}")
            
        except Exception as e:
            logger.error(f"检查用户权限失败: {str(e)}", exc_info=True)
            # 如果权限检查失败，为了保持与财务管理API一致的行为，允许继续执行
            is_admin = True
            logger.info("权限检查失败，默认允许访问")

        # 应用门店筛选条件
        if store_id:
            conditions.append(DailyExpense.store_id == store_id)
        elif not is_admin:
            # 非管理员只能查看关联的门店，但如果没有关联门店，返回空数据而不是抛出异常
            try:
                query = (
                    select(ProjectUserStore.store_id)
                    .join(ProjectUser, ProjectUserStore.project_user_id == ProjectUser.id)
                    .where(ProjectUser.user_id == current_user.id, ProjectUser.project_id == project_id)
                )
                result = await db.execute(query)
                store_ids = [row[0] for row in result]

                if not store_ids:
                    logger.info(f"用户 {current_user.id} 没有关联任何门店")
                    return {
                        "success": True,
                        "message": "获取费用统计成功",
                        "data": {
                            "total": 0,
                            "by_type": {},
                            "by_month": {}
                        }
                    }
                conditions.append(DailyExpense.store_id.in_(store_ids))
            except Exception as e:
                logger.error(f"获取用户关联门店失败: {str(e)}", exc_info=True)
                # 如果出现异常，返回空数据
                return {
                    "success": True,
                    "message": "获取费用统计成功",
                    "data": {
                        "total": 0,
                        "by_type": {},
                        "by_month": {}
                    }
                }

        # 应用日期筛选条件
        if start_date:
            conditions.append(DailyExpense.expense_date >= start_date)

        if end_date:
            conditions.append(DailyExpense.expense_date <= end_date)

        # 查询总费用
        total_query = select(func.sum(DailyExpense.amount)).where(*conditions)
        total_result = await db.execute(total_query)
        total_amount = total_result.scalar_one_or_none() or 0

        # 查询按类型分组的费用
        type_query = (
            select(
                DailyExpense.expense_type,
                func.sum(DailyExpense.amount).label("total_amount")
            )
            .where(*conditions)
            .group_by(DailyExpense.expense_type)
        )

        type_result = await db.execute(type_query)
        type_rows = type_result.all()

        # 处理按类型分组的数据
        by_type = {}
        for row in type_rows:
            expense_type = row[0]
            amount = float(row[1]) if row[1] else 0
            by_type[expense_type] = amount

        # 查询按月份分组的费用
        month_expr = func.date_trunc('month', DailyExpense.expense_date)
        month_query = (
            select(
                month_expr.label("month"),
                func.sum(DailyExpense.amount).label("total_amount")
            )
            .where(*conditions)
            .group_by(month_expr)
            .order_by(month_expr)
        )

        month_result = await db.execute(month_query)
        month_rows = month_result.all()

        # 处理按月份分组的数据
        by_month = {}
        for row in month_rows:
            month = row[0].strftime('%Y-%m') if row[0] else 'unknown'
            amount = float(row[1]) if row[1] else 0
            by_month[month] = amount

        return {
            "success": True,
            "message": "获取费用统计成功",
            "data": {
                "total": float(total_amount),
                "by_type": by_type,
                "by_month": by_month
            }
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取费用统计失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取费用统计失败: {str(e)}"
        )


@router.post("/expenses-upload-receipt", response_model=Dict[str, Any])
async def upload_expense_receipt(
    file: UploadFile = File(...),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    project_id: uuid.UUID = Depends(get_current_project_id),
    tenant_id: Optional[uuid.UUID] = Depends(get_current_tenant_id)
):
    """上传费用凭据"""
    try:
        # 检查文件类型
        allowed_types = ["image/jpeg", "image/png", "image/gif", "application/pdf"]
        if file.content_type not in allowed_types:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="不支持的文件类型，只允许上传图片或PDF文件"
            )

        # 检查文件大小
        file_size_limit = 2 * 1024 * 1024  # 2MB
        file_content = await file.read()
        if len(file_content) > file_size_limit:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="文件大小不能超过2MB"
            )

        # 重置文件位置
        await file.seek(0)

        # 使用StorageService上传文件
        result = await StorageService.upload_file(
            db=db,
            file=file,
            project_id=project_id,
            tenant_id=tenant_id,
            user_id=current_user.id,
            folder_path="/expenses/receipts",
            description="费用凭据",
            is_public=False
        )

        if not result or not result.get("success"):
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="上传文件失败"
            )

        file_data = result.get("data", {})

        return {
            "success": True,
            "message": "上传费用凭据成功",
            "data": {
                "receipt_url": file_data.get("download_url"),
                "file_id": file_data.get("id"),
                "file_name": file_data.get("name"),
                "file_size": file_data.get("size")
            }
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"上传费用凭据失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"上传费用凭据失败: {str(e)}"
        )


# 门店销售目标管理

@router.get("/store-targets", response_model=StoreSalesTargetListResponse)
async def get_store_targets(
    store_id: Optional[uuid.UUID] = None,
    target_year: Optional[int] = None,
    target_month: Optional[int] = None,
    page: int = Query(1, ge=1),
    page_size: int = Query(10, ge=1, le=100),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    project_id: uuid.UUID = Depends(get_current_project_id)
):
    """获取门店销售目标列表"""
    try:
        query = select(StoreSalesTarget, Store.name.label("store_name")) \
            .join(Store, StoreSalesTarget.store_id == Store.id) \
            .where(StoreSalesTarget.project_id == project_id)

        if store_id:
            query = query.where(StoreSalesTarget.store_id == store_id)
        if target_year:
            query = query.where(StoreSalesTarget.target_year == target_year)
        if target_month:
            query = query.where(StoreSalesTarget.target_month == target_month)
        
        total_query = select(func.count()).select_from(query.subquery())
        total_result = await db.execute(total_query)
        total = total_result.scalar_one()

        query = query.order_by(desc(StoreSalesTarget.target_year), desc(StoreSalesTarget.target_month), Store.name) \
                     .offset((page - 1) * page_size).limit(page_size)
        
        result = await db.execute(query)
        targets = []
        for target, store_name in result.all():
            target_data = StoreSalesTargetInDB.model_validate(target)
            target_data.store_name = store_name
            targets.append(target_data)

        return StoreSalesTargetListResponse(
            success=True,
            message="获取门店销售目标成功",
            items=targets,
            total=total
        )
    except Exception as e:
        logger.error(f"获取门店销售目标失败: {e}", exc_info=True)
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="服务器内部错误")

@router.post("/store-targets", response_model=StoreSalesTargetResponse)
async def create_or_update_store_target(
    data: StoreSalesTargetCreate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    project_id: uuid.UUID = Depends(get_current_project_id),
    tenant_id: Optional[uuid.UUID] = Depends(get_current_tenant_id)
):
    """创建或更新门店销售目标 (Upsert)"""
    try:
        # 查找现有目标
        query = select(StoreSalesTarget).where(
            StoreSalesTarget.project_id == project_id,
            StoreSalesTarget.store_id == data.store_id,
            StoreSalesTarget.target_year == data.target_year,
            StoreSalesTarget.target_month == data.target_month
        )
        result = await db.execute(query)
        existing_target = result.scalar_one_or_none()

        if existing_target:
            # 更新
            update_data = data.model_dump(exclude_unset=True)
            for key, value in update_data.items():
                setattr(existing_target, key, value)
            existing_target.updated_by = current_user.id
            db.add(existing_target)
            target_to_return = existing_target
            message = "门店销售目标更新成功"
        else:
            # 创建
            new_target = StoreSalesTarget(
                **data.model_dump(),
                project_id=project_id,
                tenant_id=tenant_id,
                created_by=current_user.id,
                updated_by=current_user.id
            )
            db.add(new_target)
            target_to_return = new_target
            message = "门店销售目标创建成功"
        
        await db.commit()
        await db.refresh(target_to_return)

        # 获取门店名称
        store_result = await db.execute(select(Store.name).where(Store.id == target_to_return.store_id))
        store_name = store_result.scalar_one_or_none()

        response_data = StoreSalesTargetInDB.model_validate(target_to_return)
        response_data.store_name = store_name

        return StoreSalesTargetResponse(success=True, message=message, data=response_data)
    except Exception as e:
        await db.rollback()
        logger.error(f"创建/更新门店销售目标失败: {e}", exc_info=True)
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="操作失败")

@router.delete("/store-targets/{target_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_store_target(
    target_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    project_id: uuid.UUID = Depends(get_current_project_id)
):
    """删除门店销售目标"""
    try:
        query = select(StoreSalesTarget).where(
            StoreSalesTarget.id == target_id,
            StoreSalesTarget.project_id == project_id
        )
        result = await db.execute(query)
        target = result.scalar_one_or_none()

        if not target:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="未找到指定的销售目标")

        await db.delete(target)
        await db.commit()
    except Exception as e:
        await db.rollback()
        logger.error(f"删除门店销售目标失败: {e}", exc_info=True)
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="删除失败")

@router.post("/store-targets/upload", response_model=Dict[str, Any])
async def upload_store_targets(
    file: UploadFile = File(...),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    project_id: uuid.UUID = Depends(get_current_project_id),
    tenant_id: Optional[uuid.UUID] = Depends(get_current_tenant_id)
):
    """通过文件上传批量创建/更新门店销售目标"""
    content = await file.read()
    
    try:
        if file.filename.endswith('.csv'):
            df = pd.read_csv(io.BytesIO(content))
        elif file.filename.endswith(('.xls', '.xlsx')):
            df = pd.read_excel(io.BytesIO(content))
        else:
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="不支持的文件类型，请上传CSV或Excel文件")

        required_columns = ['store_id', 'target_year', 'target_month', 'monthly_target']
        if not all(col in df.columns for col in required_columns):
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=f"文件缺少必需的列: {', '.join(required_columns)}")

        success_count = 0
        error_count = 0
        errors = []

        for index, row in df.iterrows():
            try:
                data_dict = {
                    "store_id": row['store_id'],
                    "target_year": int(row['target_year']),
                    "target_month": int(row['target_month']),
                    "monthly_target": float(row['monthly_target']),
                    "last_year_sales": float(row['last_year_sales']) if pd.notna(row.get('last_year_sales')) else None,
                    "growth_rate": float(row['growth_rate']) if pd.notna(row.get('growth_rate')) else None,
                    "daily_target": float(row['daily_target']) if pd.notna(row.get('daily_target')) else None,
                }
                
                target_data = StoreSalesTargetCreate(**data_dict)

                # Upsert logic
                query = select(StoreSalesTarget).where(
                    StoreSalesTarget.project_id == project_id,
                    StoreSalesTarget.store_id == target_data.store_id,
                    StoreSalesTarget.target_year == target_data.target_year,
                    StoreSalesTarget.target_month == target_data.target_month
                )
                result = await db.execute(query)
                existing_target = result.scalar_one_or_none()

                if existing_target:
                    update_data = target_data.model_dump(exclude_unset=True)
                    for key, value in update_data.items():
                        setattr(existing_target, key, value)
                    existing_target.updated_by = current_user.id
                else:
                    new_target = StoreSalesTarget(
                        **target_data.model_dump(),
                        project_id=project_id,
                        tenant_id=tenant_id,
                        created_by=current_user.id,
                        updated_by=current_user.id
                    )
                    db.add(new_target)
                
                success_count += 1
            except Exception as e:
                error_count += 1
                errors.append(f"第 {index + 2} 行处理失败: {e}")

        if error_count == 0:
            await db.commit()
            return {"success": True, "message": f"成功处理 {success_count} 条记录"}
        else:
            await db.rollback()
            return {"success": False, "message": f"处理完成，{success_count}条成功，{error_count}条失败。", "errors": errors}

    except Exception as e:
        await db.rollback()
        logger.error(f"上传门店销售目标文件失败: {e}", exc_info=True)
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"文件处理失败: {e}")
