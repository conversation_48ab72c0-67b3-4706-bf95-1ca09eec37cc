/* 仪表盘容器 */
.dashboard-container {
  padding: 16px;
}

/* 仪表盘标题 */
.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.dashboard-title {
  margin: 0;
}

.dashboard-actions {
  display: flex;
  gap: 8px;
}

/* 仪表盘过滤器 */
.dashboard-filter {
  margin-bottom: 16px;
}

.filter-item {
  display: flex;
  flex-direction: column;
}

.filter-label {
  margin-bottom: 4px;
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
}

/* 统计卡片 */
.statistic-card {
  height: 100%;
}

.statistic-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.statistic-card-title {
  color: rgba(0, 0, 0, 0.45);
  font-size: 14px;
}

.statistic-footer {
  margin-top: 8px;
  font-size: 12px;
}

/* 图表容器 */
.chart-container {
  margin-top: 16px;
  background-color: #fff;
  padding: 16px;
  border-radius: 2px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.chart-title {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
}

.chart-actions {
  display: flex;
  gap: 8px;
}

/* 仪表盘标签页 */
.dashboard-tabs .ant-tabs-nav {
  margin-bottom: 16px;
}

/* AI推荐 */
.ai-recommendations {
  margin-top: 16px;
}

.ai-recommendation-card {
  margin-bottom: 16px;
}

.ai-recommendation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.ai-recommendation-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 响应式调整 */
@media (max-width: 576px) {
  .dashboard-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .dashboard-actions {
    width: 100%;
    justify-content: flex-end;
  }
  
  .chart-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .chart-actions {
    width: 100%;
    justify-content: flex-end;
  }
}

/* 数据表格 */
.dashboard-table {
  margin-top: 16px;
}

/* 加载状态 */
.dashboard-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

/* 空状态 */
.dashboard-empty {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

/* 仪表盘卡片网格 */
.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 16px;
  margin-bottom: 16px;
}

/* 仪表盘内容区域 */
.dashboard-content {
  margin-top: 16px;
}

/* 仪表盘页脚 */
.dashboard-footer {
  margin-top: 24px;
  text-align: center;
  color: rgba(0, 0, 0, 0.45);
}
