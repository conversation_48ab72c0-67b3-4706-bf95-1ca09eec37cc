#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
学习优化服务

提供智能客服的学习优化功能，包括优化历史记录、性能分析等。
"""

import logging
import uuid
import json
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, desc

from ..core.learning_optimizer import LearningOptimizer

logger = logging.getLogger(__name__)

class LearningOptimizationService:
    """学习优化服务"""
    
    def __init__(self):
        self.optimizer = LearningOptimizer()
    
    async def get_optimization_history(
        self,
        project_id: uuid.UUID,
        db: Optional[AsyncSession] = None,
        page: int = 1,
        size: int = 20,
        type_filter: Optional[str] = None,
        date_range: Optional[tuple] = None
    ) -> Dict[str, Any]:
        """获取优化历史记录"""
        try:
            # 模拟优化历史数据
            history_records = [
                {
                    "id": str(uuid.uuid4()),
                    "date": "2025-01-07",
                    "type": "automatic",
                    "improvements": [
                        "优化了产品咨询的回复模板",
                        "改进了意图识别准确性",
                        "调整了响应时间阈值"
                    ],
                    "performance_gain": 0.08,
                    "status": "completed",
                    "metrics": {
                        "accuracy_before": 0.85,
                        "accuracy_after": 0.93,
                        "response_time_before": 2.5,
                        "response_time_after": 1.8
                    }
                },
                {
                    "id": str(uuid.uuid4()),
                    "date": "2025-01-05",
                    "type": "manual",
                    "improvements": [
                        "更新了知识库内容",
                        "添加了新的FAQ条目"
                    ],
                    "performance_gain": 0.05,
                    "status": "completed",
                    "metrics": {
                        "knowledge_coverage_before": 0.78,
                        "knowledge_coverage_after": 0.83,
                        "user_satisfaction_before": 4.2,
                        "user_satisfaction_after": 4.4
                    }
                },
                {
                    "id": str(uuid.uuid4()),
                    "date": "2025-01-03",
                    "type": "automatic",
                    "improvements": [
                        "优化了缓存策略",
                        "改进了错误处理机制"
                    ],
                    "performance_gain": 0.03,
                    "status": "completed",
                    "metrics": {
                        "error_rate_before": 0.12,
                        "error_rate_after": 0.08,
                        "uptime_before": 0.995,
                        "uptime_after": 0.998
                    }
                }
            ]
            
            # 应用过滤器
            filtered_records = history_records
            if type_filter:
                filtered_records = [r for r in filtered_records if r["type"] == type_filter]
            
            if date_range:
                start_date, end_date = date_range
                # 安全的日期比较，处理None值
                if start_date is not None and end_date is not None:
                    # 将字符串日期转换为日期对象进行比较
                    from datetime import datetime
                    start_dt = start_date.date() if isinstance(start_date, datetime) else datetime.strptime(str(start_date), "%Y-%m-%d").date()
                    end_dt = end_date.date() if isinstance(end_date, datetime) else datetime.strptime(str(end_date), "%Y-%m-%d").date()
                    
                    filtered_records = [
                        r for r in filtered_records 
                        if start_dt <= datetime.strptime(r["date"], "%Y-%m-%d").date() <= end_dt
                    ]
                elif start_date is not None:
                    # 只有开始日期
                    start_dt = start_date.date() if isinstance(start_date, datetime) else datetime.strptime(str(start_date), "%Y-%m-%d").date()
                    filtered_records = [
                        r for r in filtered_records 
                        if datetime.strptime(r["date"], "%Y-%m-%d").date() >= start_dt
                    ]
                elif end_date is not None:
                    # 只有结束日期
                    end_dt = end_date.date() if isinstance(end_date, datetime) else datetime.strptime(str(end_date), "%Y-%m-%d").date()
                    filtered_records = [
                        r for r in filtered_records 
                        if datetime.strptime(r["date"], "%Y-%m-%d").date() <= end_dt
                    ]
            
            # 分页
            total = len(filtered_records)
            start_idx = (page - 1) * size
            end_idx = start_idx + size
            paginated_records = filtered_records[start_idx:end_idx]
            
            return {
                "history": paginated_records,
                "total": total,
                "page": page,
                "size": size,
                "total_pages": (total + size - 1) // size
            }
            
        except Exception as e:
            logger.error(f"获取优化历史失败: {e}")
            return {
                "history": [],
                "total": 0,
                "page": page,
                "size": size,
                "total_pages": 0,
                "error": str(e)
            }
    
    async def analyze_performance(
        self,
        project_id: uuid.UUID,
        db: Optional[AsyncSession] = None,
        days: int = 7
    ) -> Dict[str, Any]:
        """分析性能指标"""
        try:
            return await self.optimizer.analyze_conversation_patterns(
                db=db,
                project_id=project_id,
                days=days
            )
        except Exception as e:
            logger.error(f"分析性能指标失败: {e}")
            return {}
    
    async def apply_optimization(
        self,
        project_id: uuid.UUID,
        optimization_type: str,
        db: Optional[AsyncSession] = None
    ) -> Dict[str, Any]:
        """应用优化策略"""
        try:
            # 获取优化建议
            analysis = await self.analyze_performance(project_id, db, days=7)
            suggestions = analysis.get("optimization_suggestions", [])
            
            # 过滤指定类型的优化
            filtered_suggestions = [
                s for s in suggestions 
                if s.get("type") == optimization_type
            ]
            
            if not filtered_suggestions:
                return {
                    "success": False,
                    "message": f"没有找到类型为 {optimization_type} 的优化建议"
                }
            
            # 应用优化
            result = await self.optimizer.apply_optimizations(
                db=db,
                project_id=project_id,
                optimization_suggestions=filtered_suggestions
            )
            
            return {
                "success": True,
                "message": "优化应用成功",
                "result": result
            }
            
        except Exception as e:
            logger.error(f"应用优化失败: {e}")
            return {
                "success": False,
                "message": f"应用优化失败: {str(e)}"
            }
    
    async def get_learning_metrics(
        self,
        project_id: uuid.UUID,
        db: Optional[AsyncSession] = None
    ) -> Dict[str, Any]:
        """获取学习指标"""
        try:
            # 模拟学习指标数据
            metrics = {
                "model_accuracy": 0.91,
                "intent_recognition_rate": 0.89,
                "response_quality_score": 4.3,
                "user_satisfaction": 4.5,
                "average_response_time": 1.8,
                "knowledge_coverage": 0.85,
                "learning_iterations": 156,
                "auto_improvements": 23,
                "manual_improvements": 7,
                "total_conversations_analyzed": 12856,
                "improvement_trends": {
                    "last_7_days": 0.08,
                    "last_30_days": 0.15,
                    "last_90_days": 0.28
                }
            }
            
            return metrics
            
        except Exception as e:
            logger.error(f"获取学习指标失败: {e}")
            return {}

# 全局实例
learning_optimization_service = LearningOptimizationService() 