import asyncio
import json
import uuid
from typing import List, Dict, Any, Optional
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, func
from fastapi import HTTPException

from ..models.models import (
    DingTalkDocument, 
    DingTalkSpace,
    DingTalkGroup
)
from .base_service import DingTalkBaseService
from .dingtalk_api_service import DingTalkAPIService

class DocumentService(DingTalkBaseService):
    """
    钉钉文档管理服务
    基于钉钉知识库基础接口实现完整的文档双向同步
    
    支持的功能：
    1. 工作空间管理
    2. 知识库同步
    3. 文档双向同步
    4. 群文件管理
    """
    
    def __init__(self, db: AsyncSession, project_id: str, dingtalk_service: DingTalkAPIService):
        super().__init__(db, project_id)
        self.dingtalk_service = dingtalk_service
        
    async def get_workspaces(self) -> Dict[str, Any]:
        """
        获取工作空间列表
        对应钉钉接口：获取工作空间列表
        """
        try:
            response = await self.dingtalk_service.get_workspaces()
            
            if response.get('success'):
                workspaces = response['result']['list']
                
                return {
                    "success": True,
                    "data": {
                        "workspaces": workspaces,
                        "total": len(workspaces)
                    }
                }
            else:
                return {
                    "success": False,
                    "message": response.get('message', '获取工作空间失败')
                }
                
        except Exception as e:
            print(f"获取工作空间失败: {str(e)}")
            return {
                "success": False,
                "message": f"获取工作空间失败: {str(e)}"
            }
    
    async def get_spaces(self, workspace_id: str = None) -> Dict[str, Any]:
        """
        获取知识库列表
        对应钉钉接口：获取知识库列表
        """
        try:
            response = await self.dingtalk_service.get_spaces(workspace_id)
            
            if response.get('success'):
                spaces = response['result']['list']
                
                # 同步到本地数据库
                for space_data in spaces:
                    await self._sync_space_to_local(space_data, workspace_id)
                
                return {
                    "success": True,
                    "data": {
                        "spaces": spaces,
                        "total": len(spaces)
                    }
                }
            else:
                return {
                    "success": False,
                    "message": response.get('message', '获取知识库失败')
                }
                
        except Exception as e:
            print(f"获取知识库失败: {str(e)}")
            return {
                "success": False,
                "message": f"获取知识库失败: {str(e)}"
            }
    
    async def _sync_space_to_local(self, space_data: Dict[str, Any], workspace_id: str):
        """同步知识库信息到本地数据库"""
        
        space_id = space_data['space_id']
        
        # 检查知识库是否已存在
        stmt = select(DingTalkSpace).where(
            DingTalkSpace.dingtalk_space_id == space_id,
            DingTalkSpace.project_id == self.project_id
        )
        result = await self.db.execute(stmt)
        existing_space = result.scalar_one_or_none()
        
        if existing_space:
            # 更新现有知识库
            existing_space.space_name = space_data.get('name', '')
            existing_space.space_description = space_data.get('description', '')
            existing_space.doc_count = space_data.get('doc_count', 0)
            existing_space.updated_at = datetime.utcnow()
        else:
            # 创建新知识库
            new_space = DingTalkSpace(
                id=uuid.uuid4(),
                tenant_id=self.project_id,  # 使用project_id作为tenant_id
                project_id=self.project_id,
                dingtalk_space_id=space_id,
                dingtalk_workspace_id=workspace_id or '',
                space_name=space_data.get('name', ''),
                space_description=space_data.get('description', ''),
                doc_count=space_data.get('doc_count', 0),
                created_at=datetime.utcnow()
            )
            self.db.add(new_space)
    
    async def get_documents(self, space_id: str, workspace_id: str = None) -> Dict[str, Any]:
        """
        获取文档列表
        对应钉钉接口：获取文档列表
        """
        try:
            response = await self.dingtalk_service.get_documents(space_id, workspace_id)
            
            if response.get('success'):
                documents = response['result']['list']
                
                # 同步到本地数据库
                for doc_data in documents:
                    await self._sync_document_to_local(doc_data, space_id, workspace_id)
                
                return {
                    "success": True,
                    "data": {
                        "documents": documents,
                        "total": len(documents)
                    }
                }
            else:
                return {
                    "success": False,
                    "message": response.get('message', '获取文档列表失败')
                }
                
        except Exception as e:
            print(f"获取文档列表失败: {str(e)}")
            return {
                "success": False,
                "message": f"获取文档列表失败: {str(e)}"
            }
    
    async def _sync_document_to_local(self, doc_data: Dict[str, Any], space_id: str, workspace_id: str):
        """同步文档信息到本地数据库"""
        
        doc_id = doc_data['doc_id']
        
        # 检查文档是否已存在
        stmt = select(DingTalkDocument).where(
            DingTalkDocument.dingtalk_doc_id == doc_id,
            DingTalkDocument.project_id == self.project_id
        )
        result = await self.db.execute(stmt)
        existing_doc = result.scalar_one_or_none()
        
        if existing_doc:
            # 更新现有文档
            existing_doc.title = doc_data.get('name', '')
            existing_doc.document_type = doc_data.get('type', '')
            existing_doc.dingtalk_updated_at = doc_data.get('modified_time')
            existing_doc.updated_at = datetime.utcnow()
        else:
            # 创建新文档
            new_doc = DingTalkDocument(
                id=uuid.uuid4(),
                tenant_id=self.project_id,  # 使用project_id作为tenant_id
                project_id=self.project_id,
                user_id=self.project_id,  # 临时使用，需要根据实际用户映射
                dingtalk_doc_id=doc_id,
                dingtalk_space_id=space_id,
                dingtalk_workspace_id=workspace_id or '',
                title=doc_data.get('name', ''),
                document_type=doc_data.get('type', ''),
                dingtalk_updated_at=doc_data.get('modified_time'),
                created_at=datetime.utcnow()
            )
            self.db.add(new_doc)
    
    async def get_document_content(self, doc_id: str, space_id: str, workspace_id: str = None) -> Dict[str, Any]:
        """
        获取文档内容
        对应钉钉接口：获取文档内容
        """
        try:
            response = await self.dingtalk_service.get_document_content(doc_id, space_id, workspace_id)
            
            if response.get('success'):
                content_data = response['result']
                
                # 更新本地文档内容
                await self._update_local_document_content(doc_id, content_data)
                
                return {
                    "success": True,
                    "data": content_data
                }
            else:
                return {
                    "success": False,
                    "message": response.get('message', '获取文档内容失败')
                }
                
        except Exception as e:
            print(f"获取文档内容失败: {str(e)}")
            return {
                "success": False,
                "message": f"获取文档内容失败: {str(e)}"
            }
    
    async def _update_local_document_content(self, doc_id: str, content_data: Dict[str, Any]):
        """更新本地文档内容"""
        
        stmt = select(DingTalkDocument).where(
            DingTalkDocument.dingtalk_doc_id == doc_id,
            DingTalkDocument.project_id == self.project_id
        )
        result = await self.db.execute(stmt)
        document = result.scalar_one_or_none()
        
        if document:
            document.content = content_data.get('content', '')
            document.content_format = content_data.get('format', 'markdown')
            document.last_sync_at = datetime.utcnow()
            document.updated_at = datetime.utcnow()
    
    async def create_document(self, document_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        创建文档到钉钉
        对应钉钉接口：创建文档
        """
        try:
            response = await self.dingtalk_service.create_document(document_data)
            
            if response.get('success'):
                doc_result = response['result']
                
                # 保存到本地数据库
                new_doc = DingTalkDocument(
                    id=uuid.uuid4(),
                    tenant_id=self.project_id,
                    project_id=self.project_id,
                    user_id=self.project_id,  # 需要根据实际用户映射
                    dingtalk_doc_id=doc_result['doc_id'],
                    dingtalk_space_id=document_data['space_id'],
                    dingtalk_workspace_id=document_data.get('workspace_id', ''),
                    title=document_data['name'],
                    content=document_data.get('content', ''),
                    content_format=document_data.get('format', 'markdown'),
                    document_type=document_data.get('type', 'doc'),
                    sync_direction='to_dingtalk',
                    sync_status='synced',
                    last_sync_at=datetime.utcnow(),
                    created_at=datetime.utcnow()
                )
                
                self.db.add(new_doc)
                await self.db.commit()
                
                return {
                    "success": True,
                    "data": {
                        "doc_id": doc_result['doc_id'],
                        "local_id": str(new_doc.id)
                    },
                    "message": "文档创建成功"
                }
            else:
                return {
                    "success": False,
                    "message": response.get('message', '创建文档失败')
                }
                
        except Exception as e:
            await self.db.rollback()
            print(f"创建文档失败: {str(e)}")
            return {
                "success": False,
                "message": f"创建文档失败: {str(e)}"
            }
    
    async def update_document(self, doc_id: str, document_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        更新文档内容
        对应钉钉接口：更新文档
        """
        try:
            response = await self.dingtalk_service.update_document(doc_id, document_data)
            
            if response.get('success'):
                # 更新本地文档记录
                stmt = select(DingTalkDocument).where(
                    DingTalkDocument.dingtalk_doc_id == doc_id,
                    DingTalkDocument.project_id == self.project_id
                )
                result = await self.db.execute(stmt)
                document = result.scalar_one_or_none()
                
                if document:
                    document.title = document_data.get('name', document.title)
                    document.content = document_data.get('content', document.content)
                    document.last_sync_at = datetime.utcnow()
                    document.updated_at = datetime.utcnow()
                    
                await self.db.commit()
                
                return {
                    "success": True,
                    "message": "文档更新成功"
                }
            else:
                return {
                    "success": False,
                    "message": response.get('message', '更新文档失败')
                }
                
        except Exception as e:
            await self.db.rollback()
            print(f"更新文档失败: {str(e)}")
            return {
                "success": False,
                "message": f"更新文档失败: {str(e)}"
            }
    
    async def upload_knowledge_bases_to_dingtalk(self, upload_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        批量上传知识库到钉钉
        支持项目知识库和AI知识库的上传
        """
        try:
            knowledge_base_ids = upload_data.get('knowledge_base_ids', [])
            target_space_id = upload_data.get('target_space_id')
            target_workspace_id = upload_data.get('target_workspace_id')
            upload_mode = upload_data.get('upload_mode', 'full')  # full, incremental, update_only
            include_metadata = upload_data.get('include_metadata', True)
            auto_sync = upload_data.get('auto_sync', False)
            
            results = {
                "success_count": 0,
                "error_count": 0,
                "details": []
            }
            
            for kb_id in knowledge_base_ids:
                try:
                    # 获取知识库内容（这里需要调用实际的知识库API）
                    kb_content = await self._get_knowledge_base_content(kb_id)
                    
                    if not kb_content:
                        results["error_count"] += 1
                        results["details"].append({
                            "kb_id": kb_id,
                            "status": "error",
                            "message": "知识库内容为空或不存在"
                        })
                        continue
                    
                    # 根据上传模式处理内容
                    if upload_mode == 'full':
                        # 完整上传：删除旧内容，上传新内容
                        upload_result = await self._upload_full_knowledge_base(
                            kb_content, target_space_id, target_workspace_id, include_metadata
                        )
                    elif upload_mode == 'incremental':
                        # 增量上传：只上传新增或修改的内容
                        upload_result = await self._upload_incremental_knowledge_base(
                            kb_content, target_space_id, target_workspace_id, include_metadata
                        )
                    else:  # update_only
                        # 仅更新：只更新已存在的文档
                        upload_result = await self._update_existing_knowledge_base(
                            kb_content, target_space_id, target_workspace_id, include_metadata
                        )
                    
                    if upload_result.get('success'):
                        results["success_count"] += 1
                        results["details"].append({
                            "kb_id": kb_id,
                            "status": "success",
                            "uploaded_docs": upload_result.get('uploaded_docs', 0),
                            "message": "上传成功"
                        })
                        
                        # 如果启用自动同步，设置同步配置
                        if auto_sync:
                            await self._setup_auto_sync(kb_id, target_space_id, target_workspace_id)
                            
                    else:
                        results["error_count"] += 1
                        results["details"].append({
                            "kb_id": kb_id,
                            "status": "error",
                            "message": upload_result.get('message', '上传失败')
                        })
                        
                except Exception as e:
                    results["error_count"] += 1
                    results["details"].append({
                        "kb_id": kb_id,
                        "status": "error",
                        "message": f"处理失败: {str(e)}"
                    })
            
            return {
                "success": True,
                "data": results,
                "message": f"批量上传完成，成功 {results['success_count']} 个，失败 {results['error_count']} 个"
            }
            
        except Exception as e:
            print(f"批量上传知识库失败: {str(e)}")
            return {
                "success": False,
                "message": f"批量上传知识库失败: {str(e)}"
            }
    
    async def _get_knowledge_base_content(self, kb_id: str) -> Dict[str, Any]:
        """获取知识库内容（需要根据实际的知识库API实现）"""
        # 这里需要调用实际的知识库API
        # 暂时返回模拟数据
        return {
            "id": kb_id,
            "name": f"知识库_{kb_id}",
            "description": "知识库描述",
            "documents": [
                {
                    "id": f"doc_{i}",
                    "title": f"文档_{i}",
                    "content": f"这是文档{i}的内容",
                    "tags": [f"tag{i}"],
                    "created_at": datetime.utcnow().isoformat(),
                    "updated_at": datetime.utcnow().isoformat()
                }
                for i in range(1, 6)  # 模拟5个文档
            ]
        }
    
    async def _upload_full_knowledge_base(self, kb_content: Dict[str, Any], space_id: str, workspace_id: str, include_metadata: bool) -> Dict[str, Any]:
        """完整上传知识库"""
        uploaded_docs = 0
        
        for doc in kb_content.get('documents', []):
            doc_data = {
                "space_id": space_id,
                "workspace_id": workspace_id,
                "name": doc['title'],
                "content": doc['content'],
                "type": "doc",
                "format": "markdown"
            }
            
            if include_metadata:
                doc_data["metadata"] = {
                    "tags": doc.get('tags', []),
                    "source_id": doc['id'],
                    "source_kb": kb_content['id'],
                    "created_at": doc.get('created_at'),
                    "updated_at": doc.get('updated_at')
                }
            
            result = await self.create_document(doc_data)
            if result.get('success'):
                uploaded_docs += 1
        
        return {
            "success": True,
            "uploaded_docs": uploaded_docs
        }
    
    async def _upload_incremental_knowledge_base(self, kb_content: Dict[str, Any], space_id: str, workspace_id: str, include_metadata: bool) -> Dict[str, Any]:
        """增量上传知识库"""
        # 增量上传逻辑（检查本地记录，只上传新的或修改的）
        uploaded_docs = 0
        
        for doc in kb_content.get('documents', []):
            # 检查文档是否已存在
            existing_doc = await self._find_existing_document_by_source(doc['id'], kb_content['id'])
            
            if not existing_doc:
                # 新文档，直接上传
                doc_data = {
                    "space_id": space_id,
                    "workspace_id": workspace_id,
                    "name": doc['title'],
                    "content": doc['content'],
                    "type": "doc",
                    "format": "markdown"
                }
                
                if include_metadata:
                    doc_data["metadata"] = {
                        "tags": doc.get('tags', []),
                        "source_id": doc['id'],
                        "source_kb": kb_content['id']
                    }
                
                result = await self.create_document(doc_data)
                if result.get('success'):
                    uploaded_docs += 1
        
        return {
            "success": True,
            "uploaded_docs": uploaded_docs
        }
    
    async def _update_existing_knowledge_base(self, kb_content: Dict[str, Any], space_id: str, workspace_id: str, include_metadata: bool) -> Dict[str, Any]:
        """仅更新现有知识库"""
        updated_docs = 0
        
        for doc in kb_content.get('documents', []):
            # 查找已存在的文档
            existing_doc = await self._find_existing_document_by_source(doc['id'], kb_content['id'])
            
            if existing_doc:
                # 更新现有文档
                doc_data = {
                    "name": doc['title'],
                    "content": doc['content']
                }
                
                result = await self.update_document(existing_doc.dingtalk_doc_id, doc_data)
                if result.get('success'):
                    updated_docs += 1
        
        return {
            "success": True,
            "uploaded_docs": updated_docs
        }
    
    async def _find_existing_document_by_source(self, source_doc_id: str, source_kb_id: str) -> Optional[DingTalkDocument]:
        """根据源文档ID和知识库ID查找已存在的文档"""
        
        stmt = select(DingTalkDocument).where(
            DingTalkDocument.project_id == self.project_id,
            DingTalkDocument.doc_metadata.contains({"source_id": source_doc_id, "source_kb": source_kb_id})
        )
        result = await self.db.execute(stmt)
        return result.scalar_one_or_none()
    
    async def _setup_auto_sync(self, kb_id: str, space_id: str, workspace_id: str):
        """设置自动同步配置"""
        # 这里可以设置定时任务或者webhook来实现自动同步
        pass
    
    async def get_group_files(self, group_id: str) -> Dict[str, Any]:
        """
        获取群文件列表
        对应钉钉接口：获取群文件列表
        """
        try:
            response = await self.dingtalk_service.get_group_files(group_id)
            
            if response.get('success'):
                files = response['result']['list']
                
                return {
                    "success": True,
                    "data": {
                        "files": files,
                        "total": len(files)
                    }
                }
            else:
                return {
                    "success": False,
                    "message": response.get('message', '获取群文件失败')
                }
                
        except Exception as e:
            print(f"获取群文件失败: {str(e)}")
            return {
                "success": False,
                "message": f"获取群文件失败: {str(e)}"
            }
    
    async def sync_all_documents(self) -> Dict[str, Any]:
        """
        全量同步所有文档
        执行完整的文档同步流程
        """
        try:
            results = {
                "workspaces_synced": 0,
                "spaces_synced": 0,
                "documents_synced": 0,
                "errors": []
            }
            
            # 1. 获取所有工作空间
            workspaces_result = await self.get_workspaces()
            if not workspaces_result.get('success'):
                return {
                    "success": False,
                    "message": "获取工作空间失败",
                    "data": results
                }
            
            workspaces = workspaces_result['data']['workspaces']
            results["workspaces_synced"] = len(workspaces)
            
            # 2. 遍历每个工作空间的知识库
            for workspace in workspaces:
                workspace_id = workspace['workspace_id']
                
                try:
                    # 获取知识库列表
                    spaces_result = await self.get_spaces(workspace_id)
                    if spaces_result.get('success'):
                        spaces = spaces_result['data']['spaces']
                        results["spaces_synced"] += len(spaces)
                        
                        # 3. 遍历每个知识库的文档
                        for space in spaces:
                            space_id = space['space_id']
                            
                            try:
                                docs_result = await self.get_documents(space_id, workspace_id)
                                if docs_result.get('success'):
                                    documents = docs_result['data']['documents']
                                    results["documents_synced"] += len(documents)
                                    
                                    # 4. 获取每个文档的详细内容
                                    for doc in documents:
                                        try:
                                            await self.get_document_content(
                                                doc['doc_id'], space_id, workspace_id
                                            )
                                        except Exception as e:
                                            results["errors"].append(f"同步文档内容失败 {doc['doc_id']}: {str(e)}")
                                else:
                                    results["errors"].append(f"获取知识库文档失败 {space_id}: {docs_result.get('message')}")
                            except Exception as e:
                                results["errors"].append(f"处理知识库失败 {space_id}: {str(e)}")
                    else:
                        results["errors"].append(f"获取工作空间知识库失败 {workspace_id}: {spaces_result.get('message')}")
                except Exception as e:
                    results["errors"].append(f"处理工作空间失败 {workspace_id}: {str(e)}")
            
            await self.db.commit()
            
            return {
                "success": True,
                "data": results,
                "message": f"同步完成：工作空间 {results['workspaces_synced']} 个，知识库 {results['spaces_synced']} 个，文档 {results['documents_synced']} 个"
            }
            
        except Exception as e:
            await self.db.rollback()
            print(f"全量同步文档失败: {str(e)}")
            return {
                "success": False,
                "message": f"全量同步文档失败: {str(e)}",
                "data": results
            } 