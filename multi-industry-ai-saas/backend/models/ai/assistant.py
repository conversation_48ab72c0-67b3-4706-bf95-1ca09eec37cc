#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
AI 助手模型
"""

import uuid
from datetime import datetime
from sqlalchemy import Column, String, Integer, Float, Boolean, ForeignKey, DateTime, Text, func, JSON
from sqlalchemy.dialects.postgresql import UUID, JSONB, ARRAY
from sqlalchemy.orm import relationship

from db.database import Base

class AIAssistant(Base):
    """AI 助手模型"""
    __tablename__ = "ai_assistants"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id", ondelete="CASCADE"), nullable=False, comment="租户 ID")
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id", ondelete="CASCADE"), nullable=True, comment="项目 ID，为空表示租户级助手")
    name = Column(String(100), nullable=False, comment="助手名称")
    description = Column(Text, nullable=True, comment="描述")
    avatar_url = Column(String(255), nullable=True, comment="头像 URL")
    instructions = Column(Text, nullable=False, comment="助手指令")
    model_id = Column(UUID(as_uuid=True), ForeignKey("ai_models.id", ondelete="SET NULL"), nullable=True, comment="模型 ID")
    config_id = Column(UUID(as_uuid=True), ForeignKey("ai_configs.id", ondelete="SET NULL"), nullable=True, comment="配置 ID")
    knowledge_base_ids = Column(ARRAY(UUID(as_uuid=True)), nullable=True, comment="知识库 ID 列表")
    tool_ids = Column(ARRAY(UUID(as_uuid=True)), nullable=True, comment="工具 ID 列表")
    mcp_tools = Column(ARRAY(UUID(as_uuid=True)), nullable=True, comment="MCP工具 ID 列表")
    mcp_server_ids = Column(ARRAY(UUID(as_uuid=True)), nullable=True, comment="MCP服务器 ID 列表")
    capabilities = Column(JSONB, nullable=True, comment="助手能力配置")
    temperature = Column(Float, nullable=True, default=0.7, comment="温度参数")
    max_tokens = Column(Integer, nullable=True, comment="最大 token 数")
    top_p = Column(Float, nullable=True, comment="核心采样参数")
    frequency_penalty = Column(Float, nullable=True, comment="频率惩罚")
    status = Column(String(20), nullable=False, default="active", comment="状态：active, inactive")
    is_public = Column(Boolean, nullable=False, default=False, comment="是否公开")
    assistant_config = Column(JSONB, nullable=True, comment="助手配置")
    welcome_message = Column(Text, nullable=True, comment="欢迎消息")
    published_roles = Column(ARRAY(String), nullable=True, comment="发布到的角色列表")
    created_by = Column(UUID(as_uuid=True), ForeignKey("users.id", ondelete="SET NULL"), nullable=True, comment="创建者 ID")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), onupdate=func.now(), comment="更新时间")

    # 关系
    tenant = relationship("Tenant", back_populates="ai_assistants")
    project = relationship("Project", back_populates="ai_assistants")
    model = relationship("AIModel", foreign_keys=[model_id])
    config = relationship("AIConfig", foreign_keys=[config_id])
    creator = relationship("User", foreign_keys=[created_by])
    threads = relationship("AIAssistantThread", back_populates="assistant", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<AIAssistant(id={self.id}, name='{self.name}')>"

class AIAssistantThread(Base):
    """AI 助手对话线程模型"""
    __tablename__ = "ai_assistant_threads"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    assistant_id = Column(UUID(as_uuid=True), ForeignKey("ai_assistants.id", ondelete="CASCADE"), nullable=False, comment="助手 ID")
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id", ondelete="CASCADE"), nullable=True, comment="内部用户 ID，对于外部平台可以为空")
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id", ondelete="CASCADE"), nullable=True, comment="项目 ID")
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id", ondelete="CASCADE"), nullable=True, comment="租户 ID")
    
    # 外部平台集成字段
    platform = Column(String(50), nullable=True, index=True, comment="平台来源 (e.g., 'dingtalk', 'wechat')")
    conversation_id = Column(String(255), nullable=True, index=True, comment="外部平台的会话ID")
    external_user_id = Column(String(255), nullable=True, index=True, comment="外部平台的用户ID")

    title = Column(String(255), nullable=True, comment="对话标题")
    status = Column(String(20), nullable=False, default="active", comment="状态：active, archived")
    thread_config = Column(JSONB, nullable=True, comment="线程配置")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), onupdate=func.now(), comment="更新时间")
    last_message_at = Column(DateTime(timezone=True), nullable=True, comment="最后消息时间")

    # 关系
    assistant = relationship("AIAssistant", back_populates="threads")
    user = relationship("User", foreign_keys=[user_id])
    project = relationship("Project")
    tenant = relationship("Tenant")
    messages = relationship("AIAssistantMessage", back_populates="thread", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<AIAssistantThread(id={self.id}, assistant_id={self.assistant_id}, user_id={self.user_id})>"

class AIAssistantMessage(Base):
    """AI 助手消息模型"""
    __tablename__ = "ai_assistant_messages"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    thread_id = Column(UUID(as_uuid=True), ForeignKey("ai_assistant_threads.id", ondelete="CASCADE"), nullable=False, comment="线程 ID")
    role = Column(String(20), nullable=False, comment="角色：user, assistant, system, tool")
    content = Column(Text, nullable=True, comment="消息内容")
    content_type = Column(String(20), nullable=False, default="text", comment="内容类型：text, image, file")
    file_ids = Column(ARRAY(UUID(as_uuid=True)), nullable=True, comment="文件 ID 列表")
    parent_id = Column(UUID(as_uuid=True), ForeignKey("ai_assistant_messages.id", ondelete="SET NULL"), nullable=True, comment="父消息 ID")
    message_metadata = Column(JSONB, nullable=True, comment="消息元数据")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")

    # 关系
    thread = relationship("AIAssistantThread", back_populates="messages")
    parent = relationship("AIAssistantMessage", remote_side=[id], backref="children")

    def __repr__(self):
        return f"<AIAssistantMessage(id={self.id}, thread_id={self.thread_id}, role='{self.role}')>"

class AIAssistantTool(Base):
    """AI 助手工具模型"""
    __tablename__ = "ai_assistant_tools"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id", ondelete="CASCADE"), nullable=False, comment="租户 ID")
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id", ondelete="CASCADE"), nullable=True, comment="项目 ID，为空表示租户级工具")
    name = Column(String(100), nullable=False, comment="工具名称")
    description = Column(Text, nullable=True, comment="描述")
    type = Column(String(50), nullable=False, comment="工具类型：function, retrieval, code_interpreter")
    function_name = Column(String(100), nullable=True, comment="函数名称")
    function_description = Column(Text, nullable=True, comment="函数描述")
    parameters_schema = Column(JSONB, nullable=True, comment="参数模式")
    implementation = Column(Text, nullable=True, comment="实现代码")
    is_builtin = Column(Boolean, nullable=False, default=False, comment="是否内置工具")
    status = Column(String(20), nullable=False, default="active", comment="状态：active, inactive")
    created_by = Column(UUID(as_uuid=True), ForeignKey("users.id", ondelete="SET NULL"), nullable=True, comment="创建者 ID")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), onupdate=func.now(), comment="更新时间")

    # 关系
    tenant = relationship("Tenant", back_populates="ai_tools")
    project = relationship("Project", back_populates="ai_tools")
    creator = relationship("User", foreign_keys=[created_by])

    def __repr__(self):
        return f"<AIAssistantTool(id={self.id}, name='{self.name}', type='{self.type}')>"
