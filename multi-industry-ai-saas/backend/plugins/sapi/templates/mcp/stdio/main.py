#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
MCP服务器 - STDIO Протокол (V2 - использует обновленный mcp.base)
"""

import os
import json
import logging
import asyncio
import sys
from typing import Dict, Any, Optional, Union

# Импорт из mcp.base
from mcp.base import (
    MCPServer,
    SERVER_NAME,
    SERVER_VERSION,
    get_mcp_server_id,
    start_heartbeat as start_mcp_base_heartbeat_func,
    MCPSession
)
# 从load_tools.py导入load_tools函数
from load_tools import load_tools
# Утилиты для регистрации в Redis
from utils import register_server, heartbeat as redis_registration_heartbeat, parsed_env_sdk

# Настройка логирования для STDIO (в файл и stderr)
log_file_path = os.environ.get("MCP_LOG_FILE", "mcp_stdio.log")
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file_path, mode='a'),
        logging.StreamHandler(sys.stderr)
    ]
)
logger = logging.getLogger(__name__)

# --- Глобальный экземпляр MCPServer ---
SERVER_ID = get_mcp_server_id()
mcp_server = MCPServer(server_id=SERVER_ID)

# Чтение запроса
async def read_request_stdio() -> Optional[Dict[str, Any]]:
    try:
        line = await asyncio.get_event_loop().run_in_executor(None, sys.stdin.readline)
        if not line:
            logger.info("[STDIO] EOF получен, клиент закрыл stdin.")
            return None
        logger.debug(f"[STDIO] <<< строка: {line.strip()}")
        return json.loads(line)
    except json.JSONDecodeError as e:
        logger.error(f"[STDIO] Ошибка разбора JSON: {e}. Строка: '{line.strip() if 'line' in locals() else "<unavailable>"}'")
        error_response = {
            "jsonrpc": "2.0", "id": None,
            "error": {"code": -32700, "message": "Parse error: Invalid JSON"}
        }
        await write_response_stdio(error_response)
        return None
    except Exception as e:
        logger.error(f"[STDIO] Неожиданная ошибка при чтении запроса: {e}", exc_info=True)
        return None

# Запись ответа
async def write_response_stdio(response: Dict[str, Any]) -> None:
    try:
        json_str = json.dumps(response)
        logger.debug(f"[STDIO] >>> ответ: {json_str}")
        print(json_str, flush=True)
    except Exception as e:
        logger.error(f"[STDIO] Ошибка при записи ответа: {e}", exc_info=True)

# Главный цикл обработки STDIO
async def main_loop_stdio() -> None:
    logger.info(f"--- [STDIO MCP Server V2 ({SERVER_ID})] Запуск основного цикла ---")

    host_for_registration = parsed_env_sdk.get("MCP_SERVER_HOST") or parsed_env_sdk.get("MCP_SERVICE_NAME") or f"mcp-server-{SERVER_ID}"
    port_for_registration = 0 # Для STDIO порт не используется

    logger.info(f"[STDIO {SERVER_ID}] Регистрация в Redis: хост={host_for_registration}, порт={port_for_registration}...")
    try:
        # 添加元数据以支持Traefik路由
        traefik_mcp_domain_suffix = os.environ.get("TRAEFIK_MCP_DOMAIN_SUFFIX", "localhost")
        mcp_instance_hostname = f"mcp-{SERVER_ID}.{traefik_mcp_domain_suffix}"

        registration_data = {
            "server_id": SERVER_ID,
            "host": host_for_registration,
            "port": port_for_registration,
            "port_stdio": 1,  # 标记为支持STDIO
            "metadata": {
                "traefik_hostname": mcp_instance_hostname,
                "protocols": ["stdio"],
                "version": SERVER_VERSION
            }
        }

        if register_server(**registration_data):
            logger.info(f"[STDIO {SERVER_ID}] Сервер (stdio) успешно зарегистрирован/обновлен в Redis.")
            asyncio.create_task(redis_registration_heartbeat(**registration_data))
            logger.info(f"[STDIO {SERVER_ID}] Запущена задача Redis heartbeat.")
        else:
            logger.error(f"[STDIO {SERVER_ID}] Ошибка регистрации STDIO сервера в Redis.")
    except Exception as e:
        logger.error(f"[STDIO {SERVER_ID}] Исключение при регистрации STDIO в Redis: {e}", exc_info=True)

    # 先初始化服务器（包括预加载工具）
    await mcp_server.initialize_server()

    logger.info(f"[STDIO {SERVER_ID}] Загрузка инструментов...")
    # 获取系统令牌
    system_token = os.environ.get("SYSTEM_TOKEN")
    if system_token:
        logger.info(f"[STDIO {SERVER_ID}] 使用系统令牌获取工具列表")
    else:
        logger.warning(f"[STDIO {SERVER_ID}] 未找到系统令牌，工具获取可能失败")

    # 不使用MCPSession，直接传递None并让load_tools内部处理系统令牌
    tools = await load_tools(None)  # load_tools内部会处理None并查找环境变量中的系统令牌

    # 注册工具到mcp_server
    for tool in tools:
        tool_name = tool.get("name")
        if not tool_name:
            logger.warning(f"工具缺少name字段: {tool}")
            continue

        # 创建工具函数
        async def tool_func(session, params, tool_metadata=tool, stream_mode=False):
            """
            工具函数模板

            Args:
                session: MCP会话
                params: 工具参数
                tool_metadata: 工具元数据
                stream_mode: 是否使用流模式

            Returns:
                Dict[str, Any]: 工具执行结果
            """
            logger.info(f"执行工具: {tool_metadata.get('name')}")
            logger.info(f"工具参数: {params}")
            logger.info(f"流模式: {stream_mode}")

            # 更新会话的最后活动时间
            session.last_activity_time = asyncio.get_event_loop().time()

            # 这里应该实现工具的具体逻辑
            # 例如，调用API、执行计算等

            # 返回工具执行结果
            return {
                "result": f"工具 {tool_metadata.get('name')} 执行成功",
                "metadata": tool_metadata,
                "stream_mode": stream_mode
            }

        # 注册工具
        mcp_server.register_tool(tool_name, tool_func)
        logger.info(f"工具 {tool_name} 注册成功")

    logger.info(f"[STDIO {SERVER_ID}] 成功注册 {len(tools)} 个工具")

    logger.info(f"[STDIO {SERVER_ID}] Запуск фоновой задачи очистки сессий mcp.base...")
    asyncio.create_task(start_mcp_base_heartbeat_func(mcp_server))

    logger.info(f"[STDIO {SERVER_ID}] Сервер '{SERVER_NAME}' (STDIO) инициализирован. Ожидание ввода...")

    current_stdio_session_id: Optional[str] = None # Для отслеживания текущей сессии в рамках STDIO

    while True:
        request_json = await read_request_stdio()
        if request_json is None:
            logger.info("[STDIO] read_request_stdio вернул None, завершение цикла.")
            break

        req_id = request_json.get("id")
        method = request_json.get("method")
        params = request_json.get("params", {})

        headers_for_auth = params.pop("headers_for_auth", {})
        if not isinstance(headers_for_auth, dict):
            logger.warning("[STDIO] поле 'headers_for_auth' в params имеет неверный формат, используется пустой dict.")
            headers_for_auth = {}

        actual_params = params

        response_data = None
        try:
            if method == "initialize":
                response_data = await mcp_server.handle_initialize(
                    request_id=req_id,
                    params=actual_params,
                    headers=headers_for_auth
                )
                if "result" in response_data and "session_id" in response_data["result"]:
                    current_stdio_session_id = response_data["result"]["session_id"]
                    logger.info(f"[STDIO] Сессия {current_stdio_session_id} инициализирована.")
            elif method == "tools/refresh":
                session_id_from_params = actual_params.get("sessionId", current_stdio_session_id)
                response_data = await mcp_server.handle_request(
                    method="tools/refresh",
                    params=actual_params,
                    request_id=req_id,
                    headers=headers_for_auth,
                    session_id_from_transport=session_id_from_params
                )
            elif method == "stream/start":
                logger.info(f"[STDIO] 处理流式请求开始: {req_id}")
                session_id_from_params = actual_params.get("sessionId", current_stdio_session_id)
                response_data = await mcp_server.handle_stream_start(
                    method=actual_params.get("method", "unknown"),
                    params=actual_params.get("params", {}),
                    request_id=req_id,
                    headers=headers_for_auth,
                    session_id_from_transport=session_id_from_params
                )
            elif method == "stream/data":
                logger.info(f"[STDIO] 处理流式数据: {req_id}")
                stream_id = actual_params.get("stream_id")
                data = actual_params.get("data")
                if not stream_id:
                    response_data = {
                        "jsonrpc": "2.0",
                        "id": req_id,
                        "error": {"code": -32602, "message": "Missing required parameter 'stream_id'"}
                    }
                else:
                    success = await mcp_server.handle_stream_data(stream_id, data)
                    response_data = {
                        "jsonrpc": "2.0",
                        "id": req_id,
                        "result": {"success": success}
                    }
            elif method == "stream/end":
                logger.info(f"[STDIO] 处理流式请求结束: {req_id}")
                stream_id = actual_params.get("stream_id")
                if not stream_id:
                    response_data = {
                        "jsonrpc": "2.0",
                        "id": req_id,
                        "error": {"code": -32602, "message": "Missing required parameter 'stream_id'"}
                    }
                else:
                    success = await mcp_server.handle_stream_end(stream_id)
                    response_data = {
                        "jsonrpc": "2.0",
                        "id": req_id,
                        "result": {"success": success}
                    }
            elif method == "shutdown":
                session_id_for_shutdown = actual_params.get("sessionId", current_stdio_session_id)
                if not session_id_for_shutdown:
                     logger.warning("[STDIO] Для shutdown не найден sessionId.")

                response_data = await mcp_server.handle_request(
                    method=method,
                    params=actual_params,
                    request_id=req_id,
                    headers=headers_for_auth,
                    session_id_from_transport=session_id_for_shutdown
                )
                await write_response_stdio(response_data)
                if "result" in response_data:
                    logger.info("[STDIO] Команда shutdown успешно обработана, завершение работы.")
                    break
                else:
                    logger.warning(f"[STDIO] Команда shutdown обработана, но результат не указывает на успех: {response_data.get('error')}")
                continue
            else:
                session_id_from_params = actual_params.get("sessionId", current_stdio_session_id)
                if not session_id_from_params:
                    logger.warning(f"[STDIO] Для метода '{method}' не найден sessionId. Попытка без сессии (может привести к ошибке инициализации в base.py).")

                response_data = await mcp_server.handle_request(
                    method=method,
                    params=actual_params,
                    request_id=req_id,
                    headers=headers_for_auth,
                    session_id_from_transport=session_id_from_params
                )

            await write_response_stdio(response_data)

        except Exception as e:
            logger.error(f"[STDIO] Ошибка при обработке запроса (метод: {method}): {e}", exc_info=True)
            error_resp = {"jsonrpc": "2.0", "id": req_id, "error": {"code": -32000, "message": f"Server error: {str(e)}"}}
            await write_response_stdio(error_resp)

    logger.info(f"--- [STDIO MCP Server V2 ({SERVER_ID})] Основной цикл завершен ---")

# Точка входа для STDIO режима
if __name__ == "__main__":
    logger.info(f"--- [STDIO MCP Server V2 ({SERVER_ID})] Инициализация __main__ ---")
    # Проверка важных ENV переменных (API_BASE_URL используется в base.py)
    api_base_url_env = os.environ.get("API_BASE_URL")
    if not api_base_url_env:
        logger.warning("[STDIO __main__] Переменная окружения API_BASE_URL не установлена. Это может повлиять на работу base.py.")

    try:
        asyncio.run(main_loop_stdio())
    except KeyboardInterrupt:
        logger.info(f"--- [STDIO MCP Server V2 ({SERVER_ID})] Прервано пользователем (KeyboardInterrupt) ---")
    except Exception as e_run:
        logger.error(f"--- [STDIO MCP Server V2 ({SERVER_ID})] Критическая ошибка в asyncio.run(main_loop_stdio): {e_run} ---", exc_info=True)
    finally:
        logger.info(f"--- [STDIO MCP Server V2 ({SERVER_ID})] Завершение работы __main__ ---")
