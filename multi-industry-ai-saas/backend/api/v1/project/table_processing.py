#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
项目级通用表格处理 API
"""

import logging
import uuid
from typing import Optional, List
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from db.database import get_db
from models.project import Project
from models.user import User
from core.auth import get_current_user
from api.deps import get_current_project, get_current_tenant_id
from services.table_processing_service import TableProcessingService
from schemas.table_processing import (
    TableProcessingRequest,
    TableProcessingResult,
    TableTemplate
)

logger = logging.getLogger(__name__)
router = APIRouter()

@router.post("/table-processing/process", response_model=TableProcessingResult)
async def process_table(
    request: TableProcessingRequest,
    project: Project = Depends(get_current_project),
    tenant_id: Optional[uuid.UUID] = Depends(get_current_tenant_id),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    通用表格智能处理
    
    支持：
    - 模板智能分流
    - 已知模板规则引擎提取
    - 未知模板AI通用提取
    - 数据校验与修正
    """
    try:
        result = await TableProcessingService.process_table(
            db=db,
            request=request,
            project_id=project.id,
            tenant_id=tenant_id,
            user_id=current_user.id
        )
        
        return result
        
    except Exception as e:
        logger.error(f"表格处理失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"表格处理失败: {str(e)}",
        )

@router.get("/table-processing/templates", response_model=List[TableTemplate])
async def get_available_templates(
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
):
    """
    获取所有可用的表格模板
    """
    try:
        templates = TableProcessingService.get_available_templates()
        return templates
        
    except Exception as e:
        logger.error(f"获取表格模板失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取表格模板失败: {str(e)}",
        )

@router.post("/table-processing/templates")
async def register_custom_template(
    template: TableTemplate,
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
):
    """
    注册自定义表格模板
    """
    try:
        TableProcessingService.register_custom_template(template)
        
        return {
            "success": True,
            "message": f"模板 '{template.template_name}' 注册成功",
            "template_id": template.template_id
        }
        
    except Exception as e:
        logger.error(f"注册表格模板失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"注册表格模板失败: {str(e)}",
        )

@router.post("/table-processing/preview")
async def preview_table_processing(
    request: TableProcessingRequest,
    project: Project = Depends(get_current_project),
    tenant_id: Optional[uuid.UUID] = Depends(get_current_tenant_id),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    预览表格处理结果（不保存）
    """
    try:
        # 设置为预览模式，不进行实际的数据保存
        preview_request = request.model_copy()
        
        result = await TableProcessingService.process_table(
            db=db,
            request=preview_request,
            project_id=project.id,
            tenant_id=tenant_id,
            user_id=current_user.id
        )
        
        # 返回预览结果，限制数据量
        preview_result = result.model_copy()
        if len(preview_result.extracted_data) > 10:
            preview_result.extracted_data = preview_result.extracted_data[:10]
            preview_result.warnings.append({
                "type": "preview_limit",
                "message": "预览模式只显示前10行数据"
            })
        
        return preview_result
        
    except Exception as e:
        logger.error(f"预览表格处理失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"预览表格处理失败: {str(e)}",
        ) 