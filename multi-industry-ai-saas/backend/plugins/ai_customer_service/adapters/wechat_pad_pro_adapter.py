#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
WeChatPadPro集成适配器

基于WeChatPadPro项目的WeChat Pad协议实现个人微信接入
参考：https://github.com/WeChatPadPro
"""

import asyncio
import httpx
import logging
import json
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from datetime import datetime

logger = logging.getLogger(__name__)

@dataclass
class WeChatPadProConfig:
    """WeChatPadPro配置"""
    base_url: str = "http://127.0.0.1:8848"  # WeChatPadPro服务地址
    api_version: str = "v849"  # API版本
    admin_key: str = ""  # 管理密钥
    auth_key: str = ""  # 认证密钥
    webhook_url: str = ""  # Webhook回调地址
    
class WeChatPadProAdapter:
    """WeChatPadPro适配器"""
    
    def __init__(self, config: WeChatPadProConfig):
        self.config = config
        self.session: Optional[httpx.AsyncClient] = None
        self.connected_accounts: Dict[str, Dict] = {}
        
    async def initialize(self):
        """初始化适配器"""
        try:
            self.session = httpx.AsyncClient()
            
            # 测试连接
            await self._test_connection()
            
            # 获取已连接的微信账号
            await self._load_connected_accounts()
            
            logger.info("WeChatPadPro适配器初始化成功")
            
        except Exception as e:
            logger.error(f"WeChatPadPro适配器初始化失败: {e}")
            raise
    
    async def shutdown(self):
        """关闭适配器"""
        if self.session:
            await self.session.aclose()
    
    async def _test_connection(self):
        """测试WeChatPadPro连接"""
        try:
            url = f"{self.config.base_url}/{self.config.api_version}/ping"
            response = await self.session.get(url)
            if response.status_code == 200:
                logger.info("WeChatPadPro连接正常")
                return True
            else:
                raise Exception(f"WeChatPadPro连接失败: HTTP {response.status_code}")
                    
        except Exception as e:
            logger.error(f"WeChatPadPro连接测试失败: {e}")
            raise
    
    async def _load_connected_accounts(self):
        """加载已连接的微信账号"""
        try:
            # 获取在线设备列表
            url = f"{self.config.base_url}/{self.config.api_version}/login/GetLoginDevices"
            params = {"key": self.config.admin_key}
            
            response = await self.session.get(url, params=params)
            if response.status_code == 200:
                data = response.json()
                devices = data.get("data", [])
                
                for device in devices:
                    if device.get("isLogin", False):
                        wxid = device.get("wxid")
                        self.connected_accounts[wxid] = {
                            "wxid": wxid,
                            "nickname": device.get("nickname", ""),
                            "headimg": device.get("headimg", ""),
                            "device_id": device.get("device_id", ""),
                            "login_time": device.get("login_time", ""),
                            "status": "online"
                        }
                
                logger.info(f"已加载{len(self.connected_accounts)}个在线微信账号")
            else:
                logger.warning(f"获取设备列表失败: HTTP {response.status_code}")
                    
        except Exception as e:
            logger.error(f"加载微信账号失败: {e}")
    
    async def get_qr_code(self) -> Dict[str, Any]:
        """获取登录二维码"""
        try:
            url = f"{self.config.base_url}/{self.config.api_version}/login/GetLoginQrCode"
            params = {"key": self.config.admin_key}
            
            response = await self.session.get(url, params=params)
            if response.status_code == 200:
                data = response.json()
                return {
                    "success": True,
                    "qr_code": data.get("qrCode", ""),
                    "qr_id": data.get("qrId", ""),
                    "expires_in": 300  # 5分钟过期
                }
            else:
                return {"success": False, "error": f"HTTP {response.status_code}"}
                    
        except Exception as e:
            logger.error(f"获取二维码失败: {e}")
            return {"success": False, "error": str(e)}
    
    async def check_qr_status(self, qr_id: str) -> Dict[str, Any]:
        """检查二维码状态"""
        try:
            url = f"{self.config.base_url}/{self.config.api_version}/login/CheckLogin"
            params = {
                "key": self.config.admin_key,
                "qrId": qr_id
            }
            
            response = await self.session.get(url, params=params)
            if response.status_code == 200:
                data = response.json()
                status_code = data.get("status", 0)
                
                # 状态码映射
                status_map = {
                    0: "waiting",    # 等待扫码
                    1: "scanned",    # 已扫码，等待确认
                    2: "confirmed",  # 已确认，登录成功
                    3: "expired",    # 二维码过期
                    4: "cancelled"   # 用户取消
                }
                
                return {
                    "success": True,
                    "status": status_map.get(status_code, "unknown"),
                    "wxid": data.get("wxid", ""),
                    "nickname": data.get("nickname", "")
                }
            else:
                return {"success": False, "error": f"HTTP {response.status_code}"}
                    
        except Exception as e:
            logger.error(f"检查二维码状态失败: {e}")
            return {"success": False, "error": str(e)}
    
    async def get_contact_list(self, wxid: str) -> List[Dict[str, Any]]:
        """获取联系人列表"""
        try:
            url = f"{self.config.base_url}/{self.config.api_version}/contact/GetContactList"
            params = {
                "key": self.config.admin_key,
                "wxid": wxid
            }
            
            response = await self.session.get(url, params=params)
            if response.status_code == 200:
                data = response.json()
                return data.get("data", [])
            else:
                logger.error(f"获取联系人列表失败: HTTP {response.status_code}")
                return []
                    
        except Exception as e:
            logger.error(f"获取联系人列表失败: {e}")
            return []
    
    async def get_group_list(self, wxid: str) -> List[Dict[str, Any]]:
        """获取群组列表"""
        try:
            url = f"{self.config.base_url}/{self.config.api_version}/contact/GetChatRoomList"
            params = {
                "key": self.config.admin_key,
                "wxid": wxid
            }
            
            response = await self.session.get(url, params=params)
            if response.status_code == 200:
                data = response.json()
                groups = data.get("data", [])
                
                # 获取每个群的详细信息
                detailed_groups = []
                for group in groups:
                    group_detail = await self._get_group_detail(wxid, group.get("wxid", ""))
                    if group_detail:
                        detailed_groups.append(group_detail)
                
                return detailed_groups
            else:
                logger.error(f"获取群组列表失败: HTTP {response.status_code}")
                return []
                    
        except Exception as e:
            logger.error(f"获取群组列表失败: {e}")
            return []
    
    async def _get_group_detail(self, wxid: str, group_wxid: str) -> Optional[Dict[str, Any]]:
        """获取群组详细信息"""
        try:
            url = f"{self.config.base_url}/{self.config.api_version}/contact/GetChatRoomInfo"
            params = {
                "key": self.config.admin_key,
                "wxid": wxid,
                "chatroom_wxid": group_wxid
            }
            
            response = await self.session.get(url, params=params)
            if response.status_code == 200:
                data = response.json()
                group_info = data.get("data", {})
                
                return {
                    "group_id": group_wxid,
                    "group_name": group_info.get("room_name", ""),
                    "member_count": len(group_info.get("member_list", [])),
                    "owner_wxid": group_info.get("owner_wxid", ""),
                    "create_time": group_info.get("create_time", ""),
                    "announcement": group_info.get("announcement", ""),
                    "members": group_info.get("member_list", [])
                }
            else:
                return None
                    
        except Exception as e:
            logger.error(f"获取群组详情失败: {e}")
            return None
    
    async def send_text_message(self, wxid: str, to_wxid: str, content: str) -> bool:
        """发送文本消息"""
        try:
            url = f"{self.config.base_url}/{self.config.api_version}/message/SendTextMsg"
            data = {
                "key": self.config.admin_key,
                "wxid": wxid,
                "receive_wxid": to_wxid,
                "content": content
            }
            
            response = await self.session.post(url, json=data)
            if response.status_code == 200:
                result = response.json()
                return result.get("success", False)
            else:
                logger.error(f"发送消息失败: HTTP {response.status_code}")
                return False
                    
        except Exception as e:
            logger.error(f"发送消息失败: {e}")
            return False
    
    async def send_image_message(self, wxid: str, to_wxid: str, image_path: str) -> bool:
        """发送图片消息"""
        try:
            url = f"{self.config.base_url}/{self.config.api_version}/message/SendImageMsg"
            data = {
                "key": self.config.admin_key,
                "wxid": wxid,
                "receive_wxid": to_wxid,
                "image_path": image_path
            }
            
            response = await self.session.post(url, json=data)
            if response.status_code == 200:
                result = response.json()
                return result.get("success", False)
            else:
                logger.error(f"发送图片失败: HTTP {response.status_code}")
                return False
                    
        except Exception as e:
            logger.error(f"发送图片失败: {e}")
            return False
    
    async def logout(self, wxid: str) -> bool:
        """登出微信账号"""
        try:
            url = f"{self.config.base_url}/{self.config.api_version}/login/Logout"
            data = {
                "key": self.config.admin_key,
                "wxid": wxid
            }
            
            response = await self.session.post(url, json=data)
            if response.status_code == 200:
                result = response.json()
                success = result.get("success", False)
                
                if success and wxid in self.connected_accounts:
                    del self.connected_accounts[wxid]
                
                return success
            else:
                return False
                    
        except Exception as e:
            logger.error(f"登出失败: {e}")
            return False
    
    def get_webhook_handler(self):
        """获取Webhook处理器（用于接收消息）"""
        async def webhook_handler(request):
            try:
                data = await request.json()
                
                # 处理不同类型的消息
                msg_type = data.get("type", "")
                
                if msg_type == "text":
                    await self._handle_text_message(data)
                elif msg_type == "image":
                    await self._handle_image_message(data)
                elif msg_type == "system":
                    await self._handle_system_message(data)
                
                return {"success": True}
                
            except Exception as e:
                logger.error(f"Webhook处理失败: {e}")
                return {"success": False, "error": str(e)}
        
        return webhook_handler
    
    async def _handle_text_message(self, data: Dict[str, Any]):
        """处理文本消息"""
        # 这里可以集成AI客服逻辑
        logger.info(f"收到文本消息: {data}")
    
    async def _handle_image_message(self, data: Dict[str, Any]):
        """处理图片消息"""
        # 这里可以集成图片识别逻辑
        logger.info(f"收到图片消息: {data}")
    
    async def _handle_system_message(self, data: Dict[str, Any]):
        """处理系统消息"""
        logger.info(f"收到系统消息: {data}")

# 使用示例
async def test_wechat_pad_pro():
    """测试WeChatPadPro集成"""
    config = WeChatPadProConfig(
        base_url="http://127.0.0.1:8848",
        api_version="v849",
        admin_key="your_admin_key"
    )
    
    adapter = WeChatPadProAdapter(config)
    
    try:
        await adapter.initialize()
        
        # 获取二维码
        qr_result = await adapter.get_qr_code()
        print(f"二维码: {qr_result}")
        
        # 检查已连接账号
        print(f"已连接账号: {adapter.connected_accounts}")
        
    finally:
        await adapter.shutdown()

if __name__ == "__main__":
    asyncio.run(test_wechat_pad_pro()) 