import api from '../httpClient';
import { getServiceProviderId } from '../httpClient';

/**
 * 租户API
 */
const tenant = {
  /**
   * 获取租户列表
   * @param {Object} params - 查询参数
   * @returns {Promise} - 租户列表
   */
  getList: async (params = {}) => {
    return api.get('/admin/tenants', { params });
  },

  /**
   * 根据服务商ID获取租户列表
   * @param {string} serviceProviderId - 服务商ID
   * @param {Object} params - 查询参数
   * @returns {Promise} - 租户列表
   */
  getListByServiceProvider: async (serviceProviderId = null, params = {}) => {
    const spId = serviceProviderId || getServiceProviderId();
    if (!spId) {
      throw new Error('未指定服务商ID');
    }
    return api.get('/admin/tenants', {
      params: {
        ...params,
        service_provider_id: spId
      }
    });
  },

  /**
   * 获取租户信息
   * @param {string} id - 租户ID
   * @returns {Promise} - 租户信息
   */
  getInfo: async (id) => {
    if (!id) {
      throw new Error('未指定租户ID');
    }
    return api.get(`/admin/tenants/${id}`);
  },

  /**
   * 创建租户
   * @param {Object} data - 租户数据
   * @returns {Promise} - 创建结果
   */
  create: async (data) => {
    return api.post('/admin/tenants', data);
  },

  /**
   * 更新租户
   * @param {string} id - 租户ID
   * @param {Object} data - 租户数据
   * @returns {Promise} - 更新结果
   */
  update: async (id, data) => {
    if (!id) {
      throw new Error('未指定租户ID');
    }
    return api.put(`/admin/tenants/${id}`, data);
  },

  /**
   * 删除租户
   * @param {string} id - 租户ID
   * @returns {Promise} - 删除结果
   */
  delete: async (id) => {
    if (!id) {
      throw new Error('未指定租户ID');
    }
    return api.delete(`/admin/tenants/${id}`);
  }
};

export default tenant;
