/* 全局样式 */
body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', <PERSON><PERSON>,
    'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
    'Noto Color Emoji';
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f0f2f5;
}

/* 主题颜色 */
:root {
  --primary-color: #1a365d;
  --primary-hover: #2d5a8e;
  --secondary-color: #4a90e2;
  --success-color: #52c41a;
  --warning-color: #faad14;
  --error-color: #f5222d;
  --heading-color: #1a365d;
  --text-color: rgba(0, 0, 0, 0.85);
  --text-color-secondary: rgba(0, 0, 0, 0.45);
  --disabled-color: rgba(0, 0, 0, 0.25);
  --border-color: #d9d9d9;
  --box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

/* 布局样式 */
.app-layout {
  min-height: 100vh;
}

.app-header {
  position: fixed;
  z-index: 1;
  width: 100%;
  display: flex;
  align-items: center;
  padding: 0 24px;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
}

.app-logo {
  height: 32px;
  margin: 16px;
  background: rgba(255, 255, 255, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-weight: bold;
  font-size: 18px;
}

.app-content {
  margin: 24px 16px;
  padding: 24px;
  background: #fff;
  min-height: 280px;
}

.app-footer {
  text-align: center;
  padding: 16px 50px;
}

/* 侧边栏样式 */
.app-sider {
  overflow: auto;
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
  bottom: 0;
}

.app-sider-collapsed .app-logo {
  padding: 0;
}

/* 表单样式 */
.login-form {
  max-width: 400px;
  margin: 0 auto;
}

.login-form-button {
  width: 100%;
  height: 44px;
  font-size: 16px;
  border-radius: 4px;
}

/* 按钮样式 */
.ant-btn {
  border-radius: 4px;
  transition: all 0.3s;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.ant-btn-primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.ant-btn-primary:hover,
.ant-btn-primary:focus {
  background-color: var(--primary-hover);
  border-color: var(--primary-hover);
}

.ant-btn-success {
  background-color: var(--success-color);
  border-color: var(--success-color);
  color: #fff;
}

.ant-btn-success:hover,
.ant-btn-success:focus {
  background-color: #73d13d;
  border-color: #73d13d;
  color: #fff;
}

/* 卡片样式 */
.dashboard-card {
  margin-bottom: 24px;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s;
  box-shadow: var(--box-shadow);
}

.dashboard-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.ant-card {
  border-radius: 8px;
  overflow: hidden;
}

.ant-card-head {
  border-bottom: 1px solid #f0f0f0;
  padding: 16px 24px;
}

.ant-card-head-title {
  color: var(--heading-color);
  font-weight: 500;
}

/* 表格样式 */
.table-operations {
  margin-bottom: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
}

.table-operations > button {
  margin-right: 8px;
  margin-bottom: 8px;
}

.ant-table {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: var(--box-shadow);
}

.ant-table-thead > tr > th {
  background-color: #f9f9f9;
  color: var(--heading-color);
  font-weight: 500;
}

.ant-table-tbody > tr:hover > td {
  background-color: #e6f7ff;
}

.ant-table-row-expand-icon {
  border-radius: 2px;
}

/* 标签页样式 */
.card-container > .ant-tabs-card .ant-tabs-content {
  margin-top: -16px;
}

.card-container > .ant-tabs-card .ant-tabs-content > .ant-tabs-tabpane {
  padding: 16px;
  background: #fff;
}

.card-container > .ant-tabs-card > .ant-tabs-nav::before {
  display: none;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .app-content {
    margin: 24px 0;
  }
}

/* 动画效果 */
.fade-enter {
  opacity: 0;
}

.fade-enter-active {
  opacity: 1;
  transition: opacity 300ms;
}

.fade-exit {
  opacity: 1;
}

.fade-exit-active {
  opacity: 0;
  transition: opacity 300ms;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #555;
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  min-height: 200px;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  min-height: 200px;
  color: rgba(0, 0, 0, 0.25);
}

/* 错误状态 */
.error-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  min-height: 200px;
  color: #ff4d4f;
}

/* 多行文本截断 */
.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* 标签样式 */
.tag-list {
  margin-bottom: 8px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tag-list .ant-tag {
  margin-bottom: 8px;
  border-radius: 4px;
  padding: 0 8px;
  transition: all 0.3s;
}

.ant-tag {
  border-radius: 4px;
  padding: 0 8px;
  margin-right: 8px;
  transition: all 0.3s;
}

.ant-tag:hover {
  opacity: 0.8;
}

.ant-tag-green {
  background-color: #f6ffed;
  border-color: #b7eb8f;
  color: #52c41a;
}

.ant-tag-blue {
  background-color: #e6f7ff;
  border-color: #91d5ff;
  color: #1890ff;
}

.ant-tag-red {
  background-color: #fff1f0;
  border-color: #ffa39e;
  color: #f5222d;
}

.ant-tag-orange {
  background-color: #fff7e6;
  border-color: #ffd591;
  color: #fa8c16;
}

.ant-tag-purple {
  background-color: #f9f0ff;
  border-color: #d3adf7;
  color: #722ed1;
}

/* 上传组件样式 */
.upload-list-inline .ant-upload-list-item {
  float: left;
  width: 200px;
  margin-right: 8px;
}

.upload-list-inline [class*='-upload-list-rtl'] .ant-upload-list-item {
  float: right;
}

/* 图表容器 */
.chart-container {
  height: 400px;
  width: 100%;
}

/* 响应式图表 */
@media (max-width: 768px) {
  .chart-container {
    height: 300px;
  }
}

/* 行业特定样式 */
.retail-theme {
  --primary-color: #1890ff;
}

.restaurant-theme {
  --primary-color: #52c41a;
}

.hotel-theme {
  --primary-color: #722ed1;
}
