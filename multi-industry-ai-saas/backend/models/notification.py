#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
通知模型
"""

import uuid
from datetime import datetime
from sqlalchemy import Column, String, Boolean, ForeignKey, DateTime, Text, JSON, func
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from db.database import Base

class Notification(Base):
    """通知模型"""
    __tablename__ = "notifications"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id", ondelete="CASCADE"), nullable=False, index=True)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id", ondelete="CASCADE"), nullable=False, index=True)

    title = Column(String(255), nullable=False)
    content = Column(Text, nullable=False)

    # 通知类型：system, task, inventory, order, security, ai_recommendation
    type = Column(String(50), nullable=False, default="system")

    # 通知类别：用于分类通知，例如 dashboard_type
    category = Column(String(50), nullable=True)

    # 优先级：normal, high
    priority = Column(String(20), nullable=False, default="normal")

    # 通知内容数据：用于存储结构化数据
    content_data = Column(JSON, nullable=True)

    # 目标URL，点击通知后跳转的地址
    target_url = Column(String(255), nullable=True)

    # 是否已读
    is_read = Column(Boolean, nullable=False, default=False)

    # 创建时间
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow)

    # 读取时间
    read_at = Column(DateTime, nullable=True)

    # 关系
    user = relationship("User", backref="notifications")
    project = relationship("Project", backref="notifications")

    def __repr__(self):
        return f"<Notification(id={self.id}, user_id={self.user_id}, title='{self.title}')>"
