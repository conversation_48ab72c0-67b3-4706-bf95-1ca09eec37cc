#!/usr/bin/env python
# -*- coding: utf-8 -*-

from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query, Path, Body, status
from sqlalchemy.ext.asyncio import AsyncSession
from datetime import datetime
import uuid
import logging

from db.database import get_db
from core.auth import get_current_user
from api.deps import get_current_project
from models.project import Project
from models.user import User
from services.area_survey import AreaSurveyService
from schemas.area_survey import (
    AreaSurveyCreate,
    AreaSurveyUpdate,
    AreaSurveyStatusUpdate,
    AreaSurveyResponse,
    AreaSurveyListResponse,
    AreaSurveyStatsResponse,
    AreaSurveyAIAnalysisRequest,
    AreaSurveyAIAnalysisResponse
)

router = APIRouter()
logger = logging.getLogger(__name__)

# 获取周边调研列表
@router.get("/stores/{store_id}/area-surveys", response_model=AreaSurveyListResponse)
async def get_area_surveys(
    store_id: uuid.UUID = Path(..., description="门店ID"),
    survey_status: Optional[str] = Query(None, description="调研状态"),
    survey_type: Optional[str] = Query(None, description="调研类型"),
    start_date: Optional[datetime] = Query(None, description="开始日期"),
    end_date: Optional[datetime] = Query(None, description="结束日期"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    skip: int = Query(0, ge=0, description="分页偏移量"),
    limit: int = Query(10, ge=1, le=100, description="分页大小"),
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """获取门店周边调研列表"""
    try:
        surveys, total = await AreaSurveyService.get_surveys(
            db=db,
            project_id=project.id,
            store_id=store_id,
            status=survey_status,
            survey_type=survey_type,
            start_date=start_date,
            end_date=end_date,
            search=search,
            skip=skip,
            limit=limit
        )

        return {
            "success": True,
            "message": "获取周边调研列表成功",
            "items": surveys,
            "total": total,
            "page": skip // limit + 1,
            "page_size": limit,
            "pages": (total + limit - 1) // limit
        }
    except Exception as e:
        logger.error(f"获取周边调研列表失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取周边调研列表失败: {str(e)}"
        )

# 获取周边调研统计数据
@router.get("/stores/{store_id}/area-surveys/stats", response_model=AreaSurveyStatsResponse)
async def get_area_survey_stats(
    store_id: uuid.UUID = Path(..., description="门店ID"),
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """获取门店周边调研统计数据"""
    try:
        stats = await AreaSurveyService.get_survey_stats(
            db=db,
            project_id=project.id,
            store_id=store_id
        )

        # 确保每个 recent_survey 都有必要的字段
        for survey in stats.get("recent_surveys", []):
            if "project_id" not in survey:
                survey["project_id"] = project.id
            if "store_id" not in survey:
                survey["store_id"] = store_id

        return {
            "success": True,
            "message": "获取周边调研统计数据成功",
            **stats
        }
    except Exception as e:
        logger.error(f"获取周边调研统计数据失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取周边调研统计数据失败: {str(e)}"
        )

# 获取周边调研详情
@router.get("/stores/{store_id}/area-surveys/{survey_id}", response_model=AreaSurveyResponse)
async def get_area_survey(
    store_id: uuid.UUID = Path(..., description="门店ID"),
    survey_id: uuid.UUID = Path(..., description="调研ID"),
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """获取门店周边调研详情"""
    try:
        survey = await AreaSurveyService.get_survey_by_id(
            db=db,
            project_id=project.id,
            survey_id=survey_id
        )

        return {
            "success": True,
            "message": "获取周边调研详情成功",
            **survey
        }
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"获取周边调研详情失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取周边调研详情失败: {str(e)}"
        )

# 创建周边调研
@router.post("/stores/{store_id}/area-surveys", response_model=AreaSurveyResponse, status_code=status.HTTP_201_CREATED)
async def create_area_survey(
    survey_data: AreaSurveyCreate,
    store_id: uuid.UUID = Path(..., description="门店ID"),
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """创建门店周边调研"""
    try:
        # 设置项目ID和门店ID
        survey_data.project_id = project.id
        survey_data.store_id = store_id

        survey = await AreaSurveyService.create_survey(
            db=db,
            project_id=project.id,
            survey_data=survey_data,
            created_by=current_user.id
        )

        return {
            "success": True,
            "message": "创建周边调研成功",
            **survey
        }
    except Exception as e:
        logger.error(f"创建周边调研失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建周边调研失败: {str(e)}"
        )

# 更新周边调研
@router.put("/stores/{store_id}/area-surveys/{survey_id}", response_model=AreaSurveyResponse)
async def update_area_survey(
    survey_data: AreaSurveyUpdate,
    store_id: uuid.UUID = Path(..., description="门店ID"),
    survey_id: uuid.UUID = Path(..., description="调研ID"),
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """更新门店周边调研"""
    try:
        survey = await AreaSurveyService.update_survey(
            db=db,
            project_id=project.id,
            survey_id=survey_id,
            survey_data=survey_data,
            updated_by=current_user.id
        )

        return {
            "success": True,
            "message": "更新周边调研成功",
            **survey
        }
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"更新周边调研失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新周边调研失败: {str(e)}"
        )

# 更新周边调研状态
@router.patch("/stores/{store_id}/area-surveys/{survey_id}/status", response_model=AreaSurveyResponse)
async def update_area_survey_status(
    status_data: AreaSurveyStatusUpdate,
    store_id: uuid.UUID = Path(..., description="门店ID"),
    survey_id: uuid.UUID = Path(..., description="调研ID"),
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """更新门店周边调研状态"""
    try:
        survey = await AreaSurveyService.update_survey_status(
            db=db,
            project_id=project.id,
            survey_id=survey_id,
            status=status_data.status,
            updated_by=current_user.id
        )

        return {
            "success": True,
            "message": "更新周边调研状态成功",
            **survey
        }
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"更新周边调研状态失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新周边调研状态失败: {str(e)}"
        )

# 删除周边调研
@router.delete("/stores/{store_id}/area-surveys/{survey_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_area_survey(
    store_id: uuid.UUID = Path(..., description="门店ID"),
    survey_id: uuid.UUID = Path(..., description="调研ID"),
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """删除门店周边调研"""
    try:
        await AreaSurveyService.delete_survey(
            db=db,
            project_id=project.id,
            survey_id=survey_id
        )
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"删除周边调研失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除周边调研失败: {str(e)}"
        )



# AI分析周边调研
@router.post("/stores/{store_id}/area-surveys/{survey_id}/ai-analysis", response_model=AreaSurveyResponse)
async def analyze_area_survey_with_ai(
    store_id: uuid.UUID = Path(..., description="门店ID"),
    survey_id: uuid.UUID = Path(..., description="调研ID"),
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """使用AI分析门店周边调研"""
    try:
        survey = await AreaSurveyService.analyze_survey_with_ai(
            db=db,
            project_id=project.id,
            survey_id=survey_id
        )

        return {
            "success": True,
            "message": "AI分析周边调研成功",
            **survey
        }
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"AI分析周边调研失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"AI分析周边调研失败: {str(e)}"
        )

# 兼容旧版API路由 - 获取竞对调研列表
@router.get("/stores/{store_id}/competitor-surveys", response_model=AreaSurveyListResponse)
async def get_competitor_surveys(
    store_id: uuid.UUID = Path(..., description="门店ID"),
    survey_status: Optional[str] = Query(None, description="调研状态"),
    survey_type: Optional[str] = Query(None, description="调研类型"),
    start_date: Optional[datetime] = Query(None, description="开始日期"),
    end_date: Optional[datetime] = Query(None, description="结束日期"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    skip: int = Query(0, ge=0, description="分页偏移量"),
    limit: int = Query(10, ge=1, le=100, description="分页大小"),
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """获取门店竞对调研列表 (兼容旧版API)"""
    return await get_area_surveys(
        store_id=store_id,
        survey_status=survey_status,
        survey_type=survey_type,
        start_date=start_date,
        end_date=end_date,
        search=search,
        skip=skip,
        limit=limit,
        project=project,
        current_user=current_user,
        db=db
    )

# 兼容旧版API路由 - 获取竞对调研详情
@router.get("/stores/{store_id}/competitor-surveys/{survey_id}", response_model=AreaSurveyResponse)
async def get_competitor_survey(
    store_id: uuid.UUID = Path(..., description="门店ID"),
    survey_id: uuid.UUID = Path(..., description="调研ID"),
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """获取门店竞对调研详情 (兼容旧版API)"""
    return await get_area_survey(
        store_id=store_id,
        survey_id=survey_id,
        project=project,
        current_user=current_user,
        db=db
    )

# 兼容旧版API路由 - 创建竞对调研
@router.post("/stores/{store_id}/competitor-surveys", response_model=AreaSurveyResponse, status_code=status.HTTP_201_CREATED)
async def create_competitor_survey(
    survey_data: AreaSurveyCreate,
    store_id: uuid.UUID = Path(..., description="门店ID"),
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """创建门店竞对调研 (兼容旧版API)"""
    return await create_area_survey(
        survey_data=survey_data,
        store_id=store_id,
        project=project,
        current_user=current_user,
        db=db
    )

# 兼容旧版API路由 - 更新竞对调研
@router.put("/stores/{store_id}/competitor-surveys/{survey_id}", response_model=AreaSurveyResponse)
async def update_competitor_survey(
    survey_data: AreaSurveyUpdate,
    store_id: uuid.UUID = Path(..., description="门店ID"),
    survey_id: uuid.UUID = Path(..., description="调研ID"),
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """更新门店竞对调研 (兼容旧版API)"""
    return await update_area_survey(
        survey_data=survey_data,
        store_id=store_id,
        survey_id=survey_id,
        project=project,
        current_user=current_user,
        db=db
    )

# 兼容旧版API路由 - 删除竞对调研
@router.delete("/stores/{store_id}/competitor-surveys/{survey_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_competitor_survey(
    store_id: uuid.UUID = Path(..., description="门店ID"),
    survey_id: uuid.UUID = Path(..., description="调研ID"),
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """删除门店竞对调研 (兼容旧版API)"""
    await delete_area_survey(
        store_id=store_id,
        survey_id=survey_id,
        project=project,
        current_user=current_user,
        db=db
    )