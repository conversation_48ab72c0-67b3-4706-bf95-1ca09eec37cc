#!/usr/bin/env python
# -*- coding: utf-8 -*-

import io
import logging
import pandas as pd
from typing import List, Any, Optional, Dict, Union

logger = logging.getLogger(__name__)

class ExcelService:
    """
    Excel服务，用于生成Excel文件
    """

    @staticmethod
    def generate_excel(
        file_obj: io.BytesIO,
        sheet_name: str,
        headers: List[str],
        data: List[List[Any]],
        title: Optional[str] = None,
        column_widths: Optional[Dict[int, int]] = None
    ) -> None:
        """
        生成Excel文件

        Args:
            file_obj: 文件对象
            sheet_name: 工作表名称
            headers: 表头
            data: 数据
            title: 标题
            column_widths: 列宽
        """
        try:
            # 将数据转换为DataFrame
            df = pd.DataFrame(data, columns=headers)

            # 使用pandas默认引擎生成简洁的Excel文件
            start_row = 2 if title else 0
            
            # 如果有标题，先创建标题行
            if title:
                # 创建包含标题的DataFrame
                title_df = pd.DataFrame([[title] + [''] * (len(headers) - 1)], columns=headers)
                empty_df = pd.DataFrame([[''] * len(headers)], columns=headers)
                data_df = pd.DataFrame(data, columns=headers)
                
                # 合并所有DataFrame
                final_df = pd.concat([title_df, empty_df, data_df], ignore_index=True)
            else:
                # 直接使用数据创建DataFrame
                final_df = pd.DataFrame(data, columns=headers)
            
            # 写入Excel文件
            final_df.to_excel(file_obj, sheet_name=sheet_name, index=False)

        except Exception as e:
            logger.error(f"生成Excel文件失败: {e}")
            raise
