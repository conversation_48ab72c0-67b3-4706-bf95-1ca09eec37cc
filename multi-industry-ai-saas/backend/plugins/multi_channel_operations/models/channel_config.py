#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
渠道配置模型
"""

import uuid
from sqlalchemy import Column, String, Boolean, ForeignKey, UniqueConstraint
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from sqlalchemy import DateTime

from db.database import Base

class MultiChannelConfig(Base):
    """多渠道配置模型"""
    __tablename__ = "multi_channel_configs"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id", ondelete="CASCADE"), nullable=False)
    channel_id = Column(UUID(as_uuid=True), ForeignKey("sales_channels.id", ondelete="CASCADE"), nullable=False)
    
    # 平台配置信息
    platform_config = Column(JSONB, default=dict, comment="平台特定配置")
    
    # API凭证信息（加密存储）
    api_credentials = Column(JSONB, default=dict, comment="API凭证信息")
    
    # 同步设置
    sync_settings = Column(JSONB, default=dict, comment="同步设置")
    
    # 状态
    is_active = Column(Boolean, default=True, comment="是否启用")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 关联关系
    project = relationship("Project")
    channel = relationship("SalesChannel")

    # 唯一约束
    __table_args__ = (
        UniqueConstraint('project_id', 'channel_id', name='uq_project_channel_config'),
    )

    def __repr__(self):
        return f"<MultiChannelConfig(id={self.id}, project_id={self.project_id}, channel_id={self.channel_id})>"

    @property
    def platform_name(self):
        """获取平台名称"""
        if hasattr(self.channel, 'service') and hasattr(self.channel.service, 'platform'):
            return self.channel.service.platform.name
        return "Unknown"

    @property
    def service_name(self):
        """获取服务名称"""
        if hasattr(self.channel, 'service'):
            return self.channel.service.name
        return "Unknown"

    def get_api_credential(self, key: str, default=None):
        """安全获取API凭证"""
        if not self.api_credentials:
            return default
        return self.api_credentials.get(key, default)

    def set_api_credential(self, key: str, value: str):
        """设置API凭证"""
        if not self.api_credentials:
            self.api_credentials = {}
        self.api_credentials[key] = value

    def get_platform_config(self, key: str, default=None):
        """获取平台配置"""
        if not self.platform_config:
            return default
        return self.platform_config.get(key, default)

    def set_platform_config(self, key: str, value):
        """设置平台配置"""
        if not self.platform_config:
            self.platform_config = {}
        self.platform_config[key] = value

    def get_sync_setting(self, key: str, default=None):
        """获取同步设置"""
        if not self.sync_settings:
            return default
        return self.sync_settings.get(key, default)

    def set_sync_setting(self, key: str, value):
        """设置同步设置"""
        if not self.sync_settings:
            self.sync_settings = {}
        self.sync_settings[key] = value

    def is_api_configured(self) -> bool:
        """检查API是否已配置"""
        if not self.api_credentials:
            return False
        
        # 根据不同平台检查必需的凭证
        platform_name = self.platform_name.lower()
        
        if "meituan" in platform_name:
            return bool(self.api_credentials.get("app_id") and self.api_credentials.get("app_secret"))
        elif "douyin" in platform_name:
            return bool(self.api_credentials.get("client_key") and self.api_credentials.get("client_secret"))
        elif "eleme" in platform_name:
            return bool(self.api_credentials.get("app_id") and self.api_credentials.get("app_secret"))
        elif "jd" in platform_name:
            return bool(self.api_credentials.get("app_key") and self.api_credentials.get("app_secret"))
        
        return False

    def to_dict(self):
        """转换为字典"""
        return {
            "id": str(self.id),
            "project_id": str(self.project_id),
            "channel_id": str(self.channel_id),
            "platform_config": self.platform_config,
            "sync_settings": self.sync_settings,
            "is_active": self.is_active,
            "is_api_configured": self.is_api_configured(),
            "platform_name": self.platform_name,
            "service_name": self.service_name,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        }
