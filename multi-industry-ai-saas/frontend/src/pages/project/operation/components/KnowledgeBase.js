import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Table,
  Button,
  Space,
  Tag,
  Input,
  Form,
  Select,
  Modal,
  message,
  Typography,
  Card,
  Divider,
  Upload,
  Tree,
  Tabs,
  Row,
  Col,
  Empty,
  Tooltip
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  EditOutlined,
  DeleteOutlined,
  UploadOutlined,
  DownloadOutlined,
  FileOutlined,
  FolderOutlined,
  EyeOutlined,
  ReloadOutlined,
  ApiOutlined
} from '@ant-design/icons';
import dayjs from "dayjs";
import apiService from '../../../../services/api';
import { logOperation } from '../../../../utils/logUtils';

const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;
const { Option } = Select;
const { TabPane } = Tabs;
const { DirectoryTree } = Tree;

/**
 * 知识库维护组件
 * 用于管理门店相关的知识文档、培训资料等
 */
const KnowledgeBase = ({ stores }) => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('1');
  const [loading, setLoading] = useState(false);
  const [documents, setDocuments] = useState([]);
  const [categories, setCategories] = useState([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });
  const [searchParams, setSearchParams] = useState({
    category_id: '',
    store_id: '',
    search: ''
  });
  const [modalVisible, setModalVisible] = useState(false);
  const [editingDocument, setEditingDocument] = useState(null);
  const [form] = Form.useForm();
  const [searchForm] = Form.useForm();
  const [fileList, setFileList] = useState([]);
  const [treeData, setTreeData] = useState([]);
  const [selectedKeys, setSelectedKeys] = useState([]);
  const [expandedKeys, setExpandedKeys] = useState([]);
  const [newCategoryName, setNewCategoryName] = useState('');
  const [aiKnowledgeBases, setAiKnowledgeBases] = useState([]);
  const [syncModalVisible, setSyncModalVisible] = useState(false);
  const [syncingDocument, setSyncingDocument] = useState(null);
  const [selectedKnowledgeBase, setSelectedKnowledgeBase] = useState(null);

  const isValidUUID = (id) => /^[0-9a-fA-F-]{36}$/.test(id);

  // 获取文档列表
  const fetchDocuments = async (params = {}) => {
    setLoading(true);
    try {
      const { current, pageSize } = pagination;
      const { category_id, store_id, search } = searchParams;
      const queryParams = {
        skip: (current - 1) * pageSize,
        limit: pageSize,
        search,
        ...params
      };
      if (category_id && isValidUUID(category_id)) {
        queryParams.category_id = category_id;
      }
      if (store_id && isValidUUID(store_id)) {
        queryParams.store_id = store_id;
      }
      const response = await apiService.project.knowledgeBase.getList(queryParams);

      if (response) {
        setDocuments(response.items || []);
        setPagination({
          ...pagination,
          current: response.page || 1,
          pageSize: response.size || 10,
          total: response.total || 0
        });
      }
    } catch (error) {
      console.error('获取文档列表失败:', error);
      message.error('获取文档列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取文档分类
  const fetchCategories = async () => {
    try {
      const response = await apiService.project.knowledgeBase.getCategories();
      if (response && response.items) {
        setCategories(response.items);

        // 构建树形结构
        const tree = buildTreeData(response.items);
        setTreeData(tree);

        // 设置默认展开的节点
        if (tree.length > 0) {
          setExpandedKeys([tree[0].key]);
        }
      }
    } catch (error) {
      console.error('获取文档分类失败:', error);
      message.error('获取文档分类失败');
    }
  };

  // 构建树形结构
  const buildTreeData = (categories) => {
    // 找出所有顶级分类
    const rootCategories = categories.filter(cat => !cat.parent_id);

    // 递归构建子分类
    const buildChildren = (parentId) => {
      const children = categories.filter(cat => cat.parent_id === parentId);
      if (children.length === 0) return null;

      return children.map(child => ({
        title: child.name,
        key: child.id,
        icon: <FolderOutlined />,
        children: buildChildren(child.id)
      }));
    };

    // 构建树形数据
    return rootCategories.map(root => ({
      title: root.name,
      key: root.id,
      icon: <FolderOutlined />,
      children: buildChildren(root.id)
    }));
  };

  // 处理表格分页变化
  const handleTableChange = (pagination, filters, sorter) => {
    setPagination(pagination);
    fetchDocuments({
      skip: (pagination.current - 1) * pagination.pageSize,
      limit: pagination.pageSize
    });
  };

  // 处理搜索表单提交
  const handleSearch = (values) => {
    const { category_id, store_id, search } = values;
    setSearchParams({ category_id, store_id, search });
    setPagination({ ...pagination, current: 1 });
    const params = {
      skip: 0,
      category_id,
      search
    };
    if (store_id && isValidUUID(store_id)) {
      params.store_id = store_id;
    }
    fetchDocuments(params);
  };

  // 重置搜索表单
  const handleReset = () => {
    searchForm.resetFields();
    const defaultParams = {
      category_id: '',
      store_id: '',
      search: ''
    };
    setSearchParams(defaultParams);
    setPagination({ ...pagination, current: 1 });
    fetchDocuments({
      skip: 0
    });
  };

  // 打开文档表单
  const handleOpenDocumentForm = (document = null) => {
    setEditingDocument(document);
    setFileList([]);
    form.resetFields();

    if (document) {
      // 编辑文档
      form.setFieldsValue({
        title: document.title,
        description: document.description,
        category_id: document.category_id,
        store_ids: document.store_ids || [],
        is_public: document.is_public
      });

      // 设置文件列表
      if (document.file_url) {
        setFileList([
          {
            uid: '-1',
            name: document.file_name || '文档文件',
            status: 'done',
            url: document.file_url
          }
        ]);
      }
    } else {
      // 创建文档
      form.setFieldsValue({
        is_public: true
      });
    }

    setModalVisible(true);
  };

  // 提交文档表单
  const handleSubmitDocument = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      // 准备提交数据
      const documentData = {
        ...values,
        file_url: values.file_url || (fileList.length > 0 ? (fileList[0].response?.url || fileList[0].url) : null),
        file_name: fileList.length > 0 ? fileList[0].name : null
      };

      let response;
      if (editingDocument) {
        // 更新文档
        response = await apiService.project.knowledgeBase.update(editingDocument.id, documentData);
        message.success('文档更新成功');

        // 记录操作日志
        logOperation({
          module: 'knowledge',
          action: 'update',
          resourceType: 'knowledge_document',
          resourceId: editingDocument.id,
          description: `更新知识库文档：${values.title}`,
          details: {
            before: editingDocument,
            after: documentData
          }
        });
      } else {
        // 创建文档
        response = await apiService.project.knowledgeBase.create(documentData);
        message.success('文档创建成功');

        // 记录操作日志
        logOperation({
          module: 'knowledge',
          action: 'create',
          resourceType: 'knowledge_document',
          resourceId: response?.id,
          description: `创建知识库文档：${values.title}`,
          details: documentData
        });
      }

      setModalVisible(false);
      fetchDocuments();
    } catch (error) {
      console.error('提交文档失败:', error);
      if (error.errorFields) {
        // 表单验证错误
        return;
      }
      message.error('提交文档失败: ' + (error.response?.data?.detail || error.message));
    } finally {
      setLoading(false);
    }
  };

  // 删除文档
  const handleDeleteDocument = async (id) => {
    if (!isValidUUID(id)) {
      message.error('文档ID无效，无法删除');
      return;
    }
    setLoading(true);
    try {
      // 获取文档信息，用于日志记录
      const documentToDelete = documents.find(doc => doc.id === id);

      // 删除文档
      await apiService.project.knowledgeBase.delete(id);
      message.success('文档删除成功');

      // 记录操作日志
      if (documentToDelete) {
        logOperation({
          module: 'knowledge',
          action: 'delete',
          resourceType: 'knowledge_document',
          resourceId: id,
          description: `删除知识库文档：${documentToDelete.title}`,
          details: documentToDelete
        });
      }

      fetchDocuments();
    } catch (error) {
      console.error('删除文档失败:', error);
      message.error('删除文档失败: ' + (error.response?.data?.detail || error.message));
    } finally {
      setLoading(false);
    }
  };

  // 处理标签页切换
  const handleTabChange = (key) => {
    setActiveTab(key);
  };

  // 处理树节点选择
  const handleTreeSelect = (selectedKeys, info) => {
    setSelectedKeys(selectedKeys);
    if (selectedKeys.length > 0) {
      setSearchParams({
        ...searchParams,
        category_id: selectedKeys[0]
      });
      fetchDocuments({
        category_id: selectedKeys[0]
      });
    }
  };

  // 处理树节点展开/收起
  const handleTreeExpand = (expandedKeys) => {
    setExpandedKeys(expandedKeys);
  };

  // 处理添加分类
  const handleAddCategory = async () => {
    if (!newCategoryName) return;

    try {
      setLoading(true);
      const response = await apiService.project.knowledgeBase.createCategory({
        name: newCategoryName
      });

      if (response) {
        message.success('分类创建成功');
        setNewCategoryName('');

        // 更新分类列表
        fetchCategories();

        // 设置当前选中的分类为新创建的分类
        form.setFieldsValue({
          category_id: response.id
        });
      }
    } catch (error) {
      console.error('创建分类失败:', error);
      message.error('创建分类失败: ' + (error.response?.data?.detail || error.message));
    } finally {
      setLoading(false);
    }
  };

  // 获取项目ID
  const getProjectId = () => {
    // 首先尝试从 current_project 获取
    const projectData = localStorage.getItem('current_project');
    if (projectData) {
      try {
        const project = JSON.parse(projectData);
        if (project && project.id) {
          return project.id;
        }
      } catch (e) {
        console.error('解析项目数据失败:', e);
      }
    }

    // 如果上面失败，尝试从 project_id 获取
    const projectId = localStorage.getItem('project_id');
    if (projectId && projectId !== 'null' && projectId !== 'undefined') {
      return projectId;
    }

    // 如果都失败，返回一个默认值，避免发送 "null"
    console.error('无法获取有效的项目ID，请确保已选择项目');
    return 'invalid-project-id'; // 这会导致 400 错误，但比服务器 500 错误更好
  };

  // 文件上传配置
  const projectId = getProjectId();
  const isValidProjectId = projectId !== 'invalid-project-id';

  const uploadProps = {
    name: 'file',
    action: isValidProjectId ? `/api/v1/project/${projectId}/space/upload` : '',
    data: {
      folder_path: '/knowledge-base',
      description: '知识库文档',
      is_public: false
    },
    headers: {
      authorization: 'Bearer ' + localStorage.getItem('token'),
    },
    beforeUpload: (file) => {
      if (!isValidProjectId) {
        message.error('无法上传文件：未找到有效的项目ID，请确保已选择项目');
        return false; // 阻止上传
      }
      return true; // 允许上传
    },
    onChange(info) {
      if (info.file.status === 'done') {
        message.success(`${info.file.name} 上传成功`);
        // 保存文件信息到表单
        if (info.file.response && info.file.response.success) {
          const fileData = info.file.response.data;
          form.setFieldsValue({
            file_id: fileData.id,
            file_url: fileData.download_url
          });
        }
      } else if (info.file.status === 'error') {
        message.error(`${info.file.name} 上传失败: ${info.file.response?.message || '未知错误'}`);
      }
      setFileList(info.fileList.slice(-1)); // 只保留最后一个文件
    },
  };

  // 获取 AI 知识库列表
  const fetchAIKnowledgeBases = async () => {
    try {
      const response = await apiService.project.knowledgeBase.getAvailableAIKnowledgeBases();
      if (response) {
        setAiKnowledgeBases(response);
      }
    } catch (error) {
      console.error('获取 AI 知识库列表失败:', error);
      message.error('获取 AI 知识库列表失败');
    }
  };

  // 处理同步到 AI 知识库
  const handleSyncToAI = async (document) => {
    setSyncingDocument(document);
    setSelectedKnowledgeBase(null);
    setSyncModalVisible(true);

    // 获取 AI 知识库列表
    fetchAIKnowledgeBases();
  };

  // 执行同步到 AI 知识库
  const handleSubmitSync = async () => {
    if (!syncingDocument || !selectedKnowledgeBase) {
      message.error('请选择要同步到的 AI 知识库');
      return;
    }
    if (!isValidUUID(syncingDocument.id)) {
      message.error('文档ID无效，无法同步');
      return;
    }
    try {
      setLoading(true);
      const response = await apiService.project.knowledgeBase.syncToAIKnowledgeBase(
        syncingDocument.id,
        selectedKnowledgeBase
      );

      if (response && response.success) {
        message.success('文档已成功同步到 AI 知识库');
        setSyncModalVisible(false);
      }
    } catch (error) {
      console.error('同步文档到 AI 知识库失败:', error);
      message.error('同步文档到 AI 知识库失败: ' + (error.response?.data?.detail || error.message));
    } finally {
      setLoading(false);
    }
  };

  // 初始化
  useEffect(() => {
    fetchDocuments();
    fetchCategories();
  }, []);

  // 搜索表单
  const SearchForm = () => (
    <Form
      layout="inline"
      form={searchForm}
      initialValues={searchParams}
      onFinish={handleSearch}
      style={{ marginBottom: 16 }}
    >
      <Form.Item name="category_id" label="分类">
        <Select style={{ width: 150 }} allowClear>
          <Option value="">全部</Option>
          {categories.map(category => (
            <Option key={category.id} value={category.id}>{category.name}</Option>
          ))}
        </Select>
      </Form.Item>

      <Form.Item name="store_id" label="门店">
        <Select style={{ width: 150 }} allowClear>
          <Option value="">全部</Option>
          {stores.map(store => (
            <Option key={store.id} value={store.id}>{store.name}</Option>
          ))}
        </Select>
      </Form.Item>

      <Form.Item name="search" label="搜索">
        <Input
          placeholder="文档标题/描述"
          prefix={<SearchOutlined />}
          style={{ width: 200 }}
        />
      </Form.Item>

      <Form.Item>
        <Button type="primary" htmlType="submit">
          搜索
        </Button>
      </Form.Item>

      <Form.Item>
        <Button onClick={handleReset}>
          重置
        </Button>
      </Form.Item>
    </Form>
  );

  // 文档表格列定义
  const columns = [
    {
      title: '文档标题',
      dataIndex: 'title',
      key: 'title',
      render: (text, record) => (
        <a href={record.file_url} target="_blank" rel="noopener noreferrer">
          <Space>
            <FileOutlined />
            {text}
          </Space>
        </a>
      )
    },
    {
      title: '分类',
      dataIndex: 'category_name',
      key: 'category_name'
    },
    {
      title: '适用门店',
      dataIndex: 'store_names',
      key: 'store_names',
      render: (stores) => {
        if (!stores || stores.length === 0) return '全部门店';
        if (stores.length <= 2) return stores.join(', ');
        return (
          <Tooltip title={stores.join(', ')}>
            {`${stores[0]}, ${stores[1]} 等${stores.length}个门店`}
          </Tooltip>
        );
      }
    },
    {
      title: '公开状态',
      dataIndex: 'is_public',
      key: 'is_public',
      render: (isPublic) => (
        <Tag color={isPublic ? 'green' : 'orange'}>
          {isPublic ? '公开' : '仅指定门店'}
        </Tag>
      )
    },
    {
      title: '上传时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (text) => text ? dayjs(text).format('YYYY-MM-DD HH:mm') : '-'
    },
    {
      title: '上传人',
      dataIndex: 'creator_name',
      key: 'creator_name'
    },
    {
      title: '操作',
      key: 'action',
      width: 250,
      render: (_, record) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => navigate(`/project/operation/knowledge-base/${record.id}`)}
          >
            查看详情
          </Button>

          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleOpenDocumentForm(record)}
          >
            编辑
          </Button>

          <Button
            type="link"
            size="small"
            icon={<ApiOutlined />}
            onClick={() => handleSyncToAI(record)}
          >
            同步到AI
          </Button>

          <Button
            type="link"
            danger
            size="small"
            icon={<DeleteOutlined />}
            onClick={() => {
              Modal.confirm({
                title: '确认删除',
                content: `确定要删除文档 "${record.title}" 吗？此操作不可恢复。`,
                okText: '删除',
                okType: 'danger',
                cancelText: '取消',
                onOk: () => handleDeleteDocument(record.id)
              });
            }}
          >
            删除
          </Button>
        </Space>
      )
    }
  ];

  return (
    <div className="knowledge-base">
      <Tabs activeKey={activeTab} onChange={handleTabChange}>
        <TabPane tab="文档列表" key="1">
          <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between' }}>
            <div>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => handleOpenDocumentForm()}
                style={{ marginRight: 8 }}
              >
                上传文档
              </Button>
              <Button
                icon={<ReloadOutlined />}
                onClick={() => fetchDocuments()}
              >
                刷新
              </Button>
            </div>
          </div>

          <SearchForm />

          <Table
            columns={columns}
            dataSource={documents}
            rowKey="id"
            pagination={pagination}
            onChange={handleTableChange}
            loading={loading}
            scroll={{ x: 1200 }}
          />
        </TabPane>

        <TabPane tab="分类浏览" key="2">
          <Row gutter={16}>
            <Col span={6}>
              <Card title="文档分类" style={{ marginBottom: 16 }}>
                {treeData.length > 0 ? (
                  <DirectoryTree
                    treeData={treeData}
                    selectedKeys={selectedKeys}
                    expandedKeys={expandedKeys}
                    onSelect={handleTreeSelect}
                    onExpand={handleTreeExpand}
                  />
                ) : (
                  <Empty description="暂无分类" />
                )}
              </Card>
            </Col>

            <Col span={18}>
              <Card title="文档列表">
                <Table
                  columns={columns}
                  dataSource={documents}
                  rowKey="id"
                  pagination={pagination}
                  onChange={handleTableChange}
                  loading={loading}
                />
              </Card>
            </Col>
          </Row>
        </TabPane>
      </Tabs>

      <Modal
        title={editingDocument ? '编辑文档' : '上传文档'}
        open={modalVisible}
        onOk={handleSubmitDocument}
        onCancel={() => setModalVisible(false)}
        width={700}
        confirmLoading={loading}
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Form.Item
            name="title"
            label="文档标题"
            rules={[{ required: true, message: '请输入文档标题' }]}
          >
            <Input placeholder="请输入文档标题" />
          </Form.Item>

          <Form.Item
            name="description"
            label="文档描述"
          >
            <TextArea rows={4} placeholder="请输入文档描述" />
          </Form.Item>

          <Form.Item
            name="category_id"
            label="文档分类"
            rules={[{ required: true, message: '请选择文档分类' }]}
          >
            <Select
              placeholder="请选择文档分类"
              dropdownRender={menu => (
                <div>
                  {menu}
                  <Divider style={{ margin: '4px 0' }} />
                  <div style={{ display: 'flex', flexWrap: 'nowrap', padding: 8 }}>
                    <Input
                      style={{ flex: 'auto' }}
                      value={newCategoryName}
                      onChange={e => setNewCategoryName(e.target.value)}
                      placeholder="输入新分类名称"
                    />
                    <Button
                      type="primary"
                      style={{ marginLeft: 8 }}
                      onClick={handleAddCategory}
                      disabled={!newCategoryName}
                    >
                      <PlusOutlined /> 添加分类
                    </Button>
                  </div>
                </div>
              )}
            >
              {categories.map(category => (
                <Option key={category.id} value={category.id}>{category.name}</Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="is_public"
            label="公开状态"
            valuePropName="checked"
          >
            <Select>
              <Option value={true}>公开（所有门店可见）</Option>
              <Option value={false}>仅指定门店可见</Option>
            </Select>
          </Form.Item>

          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) => prevValues.is_public !== currentValues.is_public}
          >
            {({ getFieldValue }) => (
              <Form.Item
                name="store_ids"
                label="适用门店"
                dependencies={['is_public']}
                rules={[
                  ({ getFieldValue }) => ({
                    validator(_, value) {
                      if (getFieldValue('is_public') || (value && value.length > 0)) {
                        return Promise.resolve();
                      }
                      return Promise.reject(new Error('请选择至少一个门店'));
                    },
                  }),
                ]}
              >
                <Select
                  mode="multiple"
                  placeholder="请选择适用门店"
                  disabled={getFieldValue('is_public')}
                >
                  {stores.map(store => (
                    <Option key={store.id} value={store.id}>{store.name}</Option>
                  ))}
                </Select>
              </Form.Item>
            )}
          </Form.Item>

          <Form.Item
            label="文档文件"
            rules={[
              {
                validator: (_, value) => {
                  if (editingDocument && editingDocument.file_url && fileList.length === 0) {
                    return Promise.resolve();
                  }
                  if (fileList.length > 0) {
                    return Promise.resolve();
                  }
                  return Promise.reject(new Error('请上传文档文件'));
                },
              },
            ]}
          >
            <Upload {...uploadProps} fileList={fileList}>
              <Button icon={<UploadOutlined />}>选择文件</Button>
            </Upload>
          </Form.Item>

          {/* 隐藏字段 - 存储文件ID */}
          <Form.Item name="file_id" hidden>
            <Input />
          </Form.Item>

          {/* 隐藏字段 - 存储文件URL */}
          <Form.Item name="file_url" hidden>
            <Input />
          </Form.Item>
        </Form>
      </Modal>

      {/* 同步到 AI 知识库模态框 */}
      <Modal
        title="同步到 AI 知识库"
        open={syncModalVisible}
        onOk={handleSubmitSync}
        onCancel={() => setSyncModalVisible(false)}
        confirmLoading={loading}
      >
        <div style={{ marginBottom: 16 }}>
          <p>将文档 <strong>{syncingDocument?.title}</strong> 同步到 AI 知识库，使 AI 助手能够访问此文档内容。</p>
        </div>

        <Form layout="vertical">
          <Form.Item
            label="选择 AI 知识库"
            required
            rules={[{ required: true, message: '请选择 AI 知识库' }]}
          >
            <Select
              placeholder="请选择要同步到的 AI 知识库"
              value={selectedKnowledgeBase}
              onChange={value => setSelectedKnowledgeBase(value)}
              style={{ width: '100%' }}
            >
              {aiKnowledgeBases.map(kb => (
                <Option key={kb.id} value={kb.id}>{kb.name}</Option>
              ))}
            </Select>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default KnowledgeBase;
