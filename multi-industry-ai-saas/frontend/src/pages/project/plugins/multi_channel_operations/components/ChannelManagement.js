import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Modal,
  Form,
  Input,
  Select,
  Switch,
  message,
  Popconfirm,
  Tooltip,
  Badge,
  Typography,
  Row,
  Col,
  Divider
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SyncOutlined,
  ApiOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  SettingOutlined
} from '@ant-design/icons';
import { useParams } from 'react-router-dom';
import multiChannelService from '../services/multiChannelService';

const { Title, Text } = Typography;
const { Option } = Select;

const ChannelManagement = () => {
  const { projectId } = useParams();
  const [loading, setLoading] = useState(false);
  const [channels, setChannels] = useState([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingChannel, setEditingChannel] = useState(null);
  const [form] = Form.useForm();

  // 渠道平台和服务的三层结构数据
  const [platforms, setPlatforms] = useState([]);
  const [services, setServices] = useState([]);
  const [businessModes, setBusinessModes] = useState([]);
  const [stores, setStores] = useState([]);

  // 模拟三层结构数据
  const mockPlatforms = [
    {
      id: '1',
      name: '美团',
      code: 'meituan',
      nature: 'online',
      logo_url: '/static/platforms/meituan.png',
      services: [
        { id: '1-1', name: '美团外卖', code: 'meituan_takeout', platform_id: '1' },
        { id: '1-2', name: '美团团购', code: 'meituan_group_buy', platform_id: '1' },
        { id: '1-3', name: '美团闪购', code: 'meituan_flash', platform_id: '1' }
      ]
    },
    {
      id: '2',
      name: '抖音',
      code: 'douyin',
      nature: 'online',
      logo_url: '/static/platforms/douyin.png',
      services: [
        { id: '2-1', name: '抖音团购', code: 'douyin_group_buy', platform_id: '2' },
        { id: '2-2', name: '抖音随心团', code: 'douyin_impulse', platform_id: '2' }
      ]
    },
    {
      id: '3',
      name: '饿了么',
      code: 'eleme',
      nature: 'online',
      logo_url: '/static/platforms/eleme.png',
      services: [
        { id: '3-1', name: '饿了么外卖', code: 'eleme_takeout', platform_id: '3' },
        { id: '3-2', name: '饿了么零售', code: 'eleme_retail', platform_id: '3' }
      ]
    },
    {
      id: '4',
      name: '线下渠道',
      code: 'offline',
      nature: 'offline',
      logo_url: '/static/platforms/offline.png',
      services: [
        { id: '4-1', name: '线下零售', code: 'offline_retail', platform_id: '4' },
        { id: '4-2', name: '企业客户', code: 'enterprise_client', platform_id: '4' }
      ]
    }
  ];

  const mockBusinessModes = [
    { id: 'bm1', name: '到店消费', code: 'dine_in' },
    { id: 'bm2', name: '配送上门', code: 'delivery' },
    { id: 'bm3', name: '自提', code: 'pickup' },
    { id: 'bm4', name: '企业采购', code: 'enterprise' }
  ];

  const mockStores = [
    { id: 'store1', name: '上海总店', address: '上海市黄浦区南京路100号' },
    { id: 'store2', name: '北京分店', address: '北京市朝阳区建国路200号' },
    { id: 'store3', name: '深圳分店', address: '深圳市南山区科技园300号' }
  ];

  useEffect(() => {
    loadChannels();
    loadPlatformData();
  }, [projectId]);

  const loadChannels = async () => {
    try {
      setLoading(true);
      const response = await multiChannelService.getChannels(projectId);
      setChannels(response.data || []);
    } catch (error) {
      console.error('加载渠道列表失败:', error);
      message.error('加载渠道列表失败');
    } finally {
      setLoading(false);
    }
  };

  const loadPlatformData = async () => {
    try {
      // 加载平台、服务、业务模式和门店数据
      setPlatforms(mockPlatforms);
      setServices(mockPlatforms.flatMap(p => p.services));
      setBusinessModes(mockBusinessModes);
      setStores(mockStores);
    } catch (error) {
      console.error('加载平台数据失败:', error);
    }
  };

  const handleAdd = () => {
    setEditingChannel(null);
    form.resetFields();
    setModalVisible(true);
  };

  const handleEdit = (record) => {
    setEditingChannel(record);
    form.setFieldsValue({
      ...record,
      platform_config: record.platform_config || {},
      sync_settings: record.sync_settings || {}
    });
    setModalVisible(true);
  };

  const handleDelete = async (id) => {
    try {
      await multiChannelService.deleteChannelConfig(projectId, id);
      message.success('删除成功');
      loadChannels();
    } catch (error) {
      console.error('删除失败:', error);
      message.error('删除失败');
    }
  };

  const handleSubmit = async (values) => {
    try {
      if (editingChannel) {
        await multiChannelService.updateChannelConfig(projectId, editingChannel.id, values);
        message.success('更新成功');
      } else {
        await multiChannelService.createChannelConfig(projectId, values);
        message.success('创建成功');
      }
      setModalVisible(false);
      loadChannels();
    } catch (error) {
      console.error('保存失败:', error);
      message.error('保存失败');
    }
  };

  const handleTestConnection = async (record) => {
    try {
      setLoading(true);
      const response = await multiChannelService.testChannelConnection(projectId, record.id);
      if (response.success) {
        message.success('连接测试成功');
      } else {
        message.error(`连接测试失败: ${response.message}`);
      }
    } catch (error) {
      console.error('连接测试失败:', error);
      message.error('连接测试失败');
    } finally {
      setLoading(false);
    }
  };

  const handleSync = async (record) => {
    try {
      setLoading(true);
      await multiChannelService.syncChannelData(projectId, record.id);
      message.success('同步成功');
      loadChannels();
    } catch (error) {
      console.error('同步失败:', error);
      message.error('同步失败');
    } finally {
      setLoading(false);
    }
  };

  const getStatusTag = (record) => {
    if (!record.is_active) {
      return <Tag color="default">已禁用</Tag>;
    }
    if (!record.is_api_configured) {
      return <Tag color="warning" icon={<ExclamationCircleOutlined />}>未配置API</Tag>;
    }
    return <Tag color="success" icon={<CheckCircleOutlined />}>正常</Tag>;
  };

  const getPlatformInfo = (serviceId) => {
    const service = services.find(s => s.id === serviceId);
    if (service) {
      const platform = platforms.find(p => p.id === service.platform_id);
      return {
        platform_name: platform?.name || 'Unknown',
        service_name: service.name,
        platform_code: platform?.code || 'unknown'
      };
    }
    return { platform_name: 'Unknown', service_name: 'Unknown', platform_code: 'unknown' };
  };

  const getSelectedPlatformServices = (platformId) => {
    return services.filter(s => s.platform_id === platformId);
  };

  const columns = [
    {
      title: '渠道名称',
      dataIndex: 'custom_name',
      key: 'custom_name',
      render: (text, record) => (
        <div>
          <div style={{ fontWeight: 'bold' }}>{text}</div>
          <Text type="secondary" style={{ fontSize: 12 }}>
            {record.service_name}
          </Text>
        </div>
      )
    },
    {
      title: '平台/服务',
      key: 'platform_service',
      render: (_, record) => {
        const platformInfo = getPlatformInfo(record.service_id);
        return (
          <div>
            <div style={{ fontWeight: 'bold' }}>{platformInfo.platform_name}</div>
            <Text type="secondary" style={{ fontSize: 12 }}>
              {platformInfo.service_name}
            </Text>
          </div>
        );
      }
    },
    {
      title: '状态',
      key: 'status',
      render: (_, record) => getStatusTag(record)
    },
    {
      title: 'API配置',
      key: 'api_status',
      render: (_, record) => (
        <Badge
          status={record.is_api_configured ? 'success' : 'warning'}
          text={record.is_api_configured ? '已配置' : '未配置'}
        />
      )
    },
    {
      title: '最后同步',
      dataIndex: 'last_sync_at',
      key: 'last_sync_at',
      render: (text) => text ? new Date(text).toLocaleString() : '从未同步'
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Tooltip title="编辑">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          
          <Tooltip title="API配置">
            <Button
              type="text"
              icon={<SettingOutlined />}
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          
          {record.is_api_configured && (
            <>
              <Tooltip title="测试连接">
                <Button
                  type="text"
                  icon={<ApiOutlined />}
                  onClick={() => handleTestConnection(record)}
                />
              </Tooltip>
              
              <Tooltip title="同步数据">
                <Button
                  type="text"
                  icon={<SyncOutlined />}
                  onClick={() => handleSync(record)}
                />
              </Tooltip>
            </>
          )}
          
          <Popconfirm
            title="确定要删除这个渠道配置吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除">
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      )
    }
  ];

  return (
    <div>

      <Card>
        <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between' }}>
          <Space>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAdd}
            >
              添加渠道
            </Button>
            <Button
              icon={<SyncOutlined />}
              onClick={loadChannels}
            >
              刷新
            </Button>
          </Space>
          
          <Space>
            <Text type="secondary">
              共 {channels.length} 个渠道
            </Text>
          </Space>
        </div>

        <Table
          columns={columns}
          dataSource={channels}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`
          }}
        />
      </Card>

      {/* 渠道配置模态框 */}
      <Modal
        title={editingChannel ? '编辑渠道配置' : '添加渠道配置'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={800}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="custom_name"
                label="渠道实例名称"
                rules={[{ required: true, message: '请输入渠道实例名称' }]}
              >
                <Input placeholder="如：上海总店美团外卖" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="store_id"
                label="关联门店"
              >
                <Select placeholder="选择关联门店（可选）" allowClear>
                  {stores.map(store => (
                    <Option key={store.id} value={store.id}>
                      {store.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="platform_id"
                label="渠道平台"
                rules={[{ required: true, message: '请选择渠道平台' }]}
              >
                <Select
                  placeholder="选择平台"
                  onChange={(value) => {
                    form.setFieldsValue({ service_id: undefined });
                  }}
                >
                  {platforms.map(platform => (
                    <Option key={platform.id} value={platform.id}>
                      <Space>
                        <span>{platform.name}</span>
                        <Tag color={platform.nature === 'online' ? 'blue' : 'green'}>
                          {platform.nature === 'online' ? '线上' : '线下'}
                        </Tag>
                      </Space>
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="service_id"
                label="平台服务"
                rules={[{ required: true, message: '请选择平台服务' }]}
              >
                <Select
                  placeholder="选择服务"
                  disabled={!form.getFieldValue('platform_id')}
                >
                  {getSelectedPlatformServices(form.getFieldValue('platform_id')).map(service => (
                    <Option key={service.id} value={service.id}>
                      {service.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="business_mode_id"
                label="业务模式"
                rules={[{ required: true, message: '请选择业务模式' }]}
              >
                <Select placeholder="选择业务模式">
                  {businessModes.map(mode => (
                    <Option key={mode.id} value={mode.id}>
                      {mode.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="description"
            label="描述"
          >
            <Input.TextArea rows={2} placeholder="渠道描述信息" />
          </Form.Item>

          <Divider>API配置</Divider>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name={['api_credentials', 'app_id']}
                label="App ID / Client Key"
              >
                <Input placeholder="应用ID或客户端密钥" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name={['api_credentials', 'app_secret']}
                label="App Secret / Client Secret"
              >
                <Input.Password placeholder="应用密钥或客户端秘钥" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name={['api_credentials', 'access_token']}
                label="Access Token"
              >
                <Input placeholder="访问令牌（如需要）" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name={['api_credentials', 'store_id']}
                label="店铺ID"
              >
                <Input placeholder="平台店铺ID" />
              </Form.Item>
            </Col>
          </Row>

          <Divider>同步设置</Divider>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name={['sync_settings', 'auto_sync_products']}
                label="自动同步商品"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name={['sync_settings', 'auto_sync_orders']}
                label="自动同步订单"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name={['sync_settings', 'auto_sync_inventory']}
                label="自动同步库存"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name={['sync_settings', 'sync_frequency']}
            label="同步频率"
          >
            <Select placeholder="选择同步频率">
              <Option value="realtime">实时同步</Option>
              <Option value="hourly">每小时</Option>
              <Option value="daily">每天</Option>
              <Option value="manual">手动同步</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="is_active"
            label="启用状态"
            valuePropName="checked"
            initialValue={true}
          >
            <Switch />
          </Form.Item>

          <div style={{ textAlign: 'right', marginTop: 24 }}>
            <Space>
              <Button onClick={() => setModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                {editingChannel ? '更新' : '创建'}
              </Button>
            </Space>
          </div>
        </Form>
      </Modal>
    </div>
  );
};

export default ChannelManagement;
