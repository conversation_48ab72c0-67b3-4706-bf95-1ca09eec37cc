import React, { useState, useEffect } from 'react';
import {
  Table,
  Button,
  Space,
  Tag,
  Input,
  DatePicker,
  Form,
  Select,
  Modal,
  message,
  Typography,
  Tooltip,
  Tabs,
  Row,
  Col,
  Statistic,
  Card,
  Divider,
  Badge,
  Popconfirm,
  Upload,
  Avatar,
  Dropdown,
  Menu
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  EditOutlined,
  DeleteOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ClockCircleOutlined,
  SyncOutlined,
  UploadOutlined,
  FileTextOutlined,
  UserOutlined,
  TeamOutlined,
  CalendarOutlined,
  BellOutlined,
  MoreOutlined
} from '@ant-design/icons';
import dayjs from 'dayjs';
import apiService from '../../../../services/api';

const { Title, Text, Paragraph } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;
const { TextArea } = Input;
const { TabPane } = Tabs;

// 任务状态标签颜色映射
const statusColors = {
  pending: 'default',
  in_progress: 'processing',
  completed: 'success',
  overdue: 'error',
  cancelled: 'warning'
};

// 任务状态文本映射
const statusTexts = {
  pending: '待处理',
  in_progress: '进行中',
  completed: '已完成',
  overdue: '已逾期',
  cancelled: '已取消'
};

// 任务优先级标签颜色映射
const priorityColors = {
  low: 'default',
  medium: 'blue',
  high: 'orange',
  urgent: 'red'
};

// 任务优先级文本映射
const priorityTexts = {
  low: '低',
  medium: '中',
  high: '高',
  urgent: '紧急'
};

// 任务类别映射
const categoryTexts = {
  store: '门店',
  operation: '运营',
  marketing: '营销',
  inventory: '库存',
  finance: '财务',
  other: '其他'
};

// 任务类型映射
const taskTypeTexts = {
  display: '陈列任务',
  promotion: '营销推广',
  product_promotion: '单品推广',
  daily: '日常任务',
  weekly: '周期任务',
  monthly: '月度任务',
  other: '其他任务'
};

/**
 * 门店任务管理组件
 * 用于创建、分配和跟踪门店任务
 */
const TaskManagement = ({ stores }) => {
  const [activeTab, setActiveTab] = useState('1');
  const [loading, setLoading] = useState(false);
  const [loadingUsers, setLoadingUsers] = useState(false);
  const [tasks, setTasks] = useState([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });
  const [searchParams, setSearchParams] = useState({
    status: '',
    category: '',
    priority: '',
    store_id: '',
    task_type: '',
    dateRange: null,
    search: ''
  });
  const [modalVisible, setModalVisible] = useState(false);
  const [editingTask, setEditingTask] = useState(null);
  const [form] = Form.useForm();
  const [searchForm] = Form.useForm();
  const [filterForm] = Form.useForm();
  const [fileList, setFileList] = useState([]);
  const [taskStats, setTaskStats] = useState({
    total: 0,
    pending: 0,
    in_progress: 0,
    completed: 0,
    overdue: 0,
    cancelled: 0
  });
  const [storeStats, setStoreStats] = useState([]);
  const [users, setUsers] = useState([]);
  const [filteredUsers, setFilteredUsers] = useState([]);
  const [selectedStores, setSelectedStores] = useState([]);
  const [filters, setFilters] = useState({
    status: '',
    priority: '',
    category: '',
    task_type: '',
    store_id: '',
    dateRange: [dayjs().startOf('month'), dayjs().endOf('month')],
    search: ''
  });

  // 获取任务列表
  const fetchTasks = async (params = {}) => {
    setLoading(true);
    try {
      // 构建查询参数
      const { current, pageSize } = pagination;
      const { status, category, priority, store_id, dateRange, search } = searchParams;

      const queryParams = {
        skip: (current - 1) * pageSize,
        limit: pageSize,
        status,
        category,
        priority,
        store_id,
        search,
        ...params
      };

      // 添加日期范围
      if (dateRange && dateRange[0] && dateRange[1]) {
        queryParams.start_date = dateRange[0].format('YYYY-MM-DD');
        queryParams.end_date = dateRange[1].format('YYYY-MM-DD');
      }

      // 获取任务列表
      const response = await apiService.project.routineTask.getList(queryParams);

      if (response) {
        setTasks(response.items || []);
        setPagination({
          ...pagination,
          current: response.page || 1,
          pageSize: response.size || 10,
          total: response.total || 0
        });

        // 更新任务统计
        updateTaskStats(response.items || []);

        // 更新门店统计
        updateStoreStats(response.items || []);
      }
    } catch (error) {
      console.error('获取任务列表失败:', error);
      message.error('获取任务列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 更新任务统计
  const updateTaskStats = (taskList) => {
    const stats = {
      total: taskList.length,
      pending: 0,
      in_progress: 0,
      completed: 0,
      overdue: 0,
      cancelled: 0
    };

    taskList.forEach(task => {
      if (task.status) {
        stats[task.status] = (stats[task.status] || 0) + 1;
      }
    });

    setTaskStats(stats);
  };

  // 更新门店统计
  const updateStoreStats = (taskList) => {
    // 按门店分组任务
    const storeTaskMap = {};

    taskList.forEach(task => {
      const storeId = task.store_id;
      // 确保类型一致，将 storeId 转换为字符串进行比较
      const storeName = stores.find(s => String(s.id) === String(storeId))?.name || task.store_name || '未知门店';

      if (!storeTaskMap[storeId]) {
        storeTaskMap[storeId] = {
          store_id: storeId,
          store_name: storeName,
          total: 0,
          completed: 0,
          pending: 0,
          in_progress: 0,
          cancelled: 0,
          overdue: 0,
          completion_rate: 0
        };
      }

      storeTaskMap[storeId].total += 1;

      if (task.status === 'completed') {
        storeTaskMap[storeId].completed += 1;
      } else if (task.status === 'pending') {
        storeTaskMap[storeId].pending += 1;
      } else if (task.status === 'in_progress') {
        storeTaskMap[storeId].in_progress += 1;
      } else if (task.status === 'cancelled') {
        storeTaskMap[storeId].cancelled += 1;
      } else if (task.status === 'overdue') {
        storeTaskMap[storeId].overdue += 1;
      }
    });

    // 计算完成率
    Object.values(storeTaskMap).forEach(store => {
      store.completion_rate = store.total > 0
        ? Math.round((store.completed / store.total) * 100)
        : 0;
    });

    setStoreStats(Object.values(storeTaskMap));
  };

  // 处理表格分页变化
  const handleTableChange = (pagination, filters, sorter) => {
    setPagination(pagination);
    fetchTasks({
      skip: (pagination.current - 1) * pagination.pageSize,
      limit: pagination.pageSize
    });
  };

  // 处理搜索表单提交
  const handleSearch = (values) => {
    const { status, category, priority, store_id, dateRange, search } = values;
    setSearchParams({ status, category, priority, store_id, dateRange, search });
    setPagination({ ...pagination, current: 1 });
    fetchTasks({
      skip: 0,
      status,
      category,
      priority,
      store_id,
      search,
      start_date: dateRange && dateRange[0] ? dateRange[0].format('YYYY-MM-DD') : undefined,
      end_date: dateRange && dateRange[1] ? dateRange[1].format('YYYY-MM-DD') : undefined
    });
  };

  // 重置搜索表单
  const handleReset = () => {
    searchForm.resetFields();
    const defaultParams = {
      status: '',
      category: '',
      priority: '',
      store_id: '',
      dateRange: null,
      search: ''
    };
    setSearchParams(defaultParams);
    setPagination({ ...pagination, current: 1 });
    fetchTasks({
      skip: 0
    });
  };

  // 打开任务表单
  const handleOpenTaskForm = (task = null) => {
    setEditingTask(task);
    setFileList([]);
    form.resetFields();

    if (task) {
      // 编辑任务
      const storeIds = task.store_id ?
        (Array.isArray(task.store_id) ? task.store_id : [task.store_id]) :
        [];

      // 如果是所有门店
      if (task.is_all_stores) {
        storeIds.push('all');
      }

      // 加载相关门店的用户
      setSelectedStores(storeIds);
      fetchUsers(storeIds);

      form.setFieldsValue({
        title: task.title,
        description: task.description,
        store_id: storeIds,
        category: task.category || 'operation',
        task_type: task.task_type || 'display',
        priority: task.priority,
        start_date: task.start_date ? dayjs(task.start_date) : null,
        due_date: task.due_date ? dayjs(task.due_date) : null,
        assignee_ids: task.assignee_ids || [],
        supervisor_ids: task.supervisor_ids || [],
        status: task.status
      });

      // 设置附件
      if (task.attachments && task.attachments.length > 0) {
        const files = task.attachments.map((url, index) => ({
          uid: `-${index}`,
          name: url.split('/').pop(),
          status: 'done',
          url
        }));
        setFileList(files);
      }
    } else {
      // 创建任务
      setSelectedStores([]);
      setFilteredUsers(users); // 显示所有用户

      // 创建任务时，强制设置为运营类别
      form.setFieldsValue({
        start_date: dayjs(),
        priority: 'medium',
        category: 'operation', // 固定为运营类别
        task_type: 'display',
        status: 'pending'
      });
    }

    setModalVisible(true);
  };

  // 提交任务表单
  const handleSubmitTask = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      // 准备提交数据
      const storeIds = values.store_id || [];
      const isAllStores = storeIds.includes('all');

      // 处理门店选择
      let finalStoreIds = [];
      if (isAllStores) {
        // 如果选择了"所有门店"，获取所有门店ID
        finalStoreIds = stores.map(store => store.id);
      } else {
        // 否则使用选择的门店ID，但要过滤掉可能的 'all' 值
        finalStoreIds = storeIds.filter(id => id !== 'all');
      }

      // 确保至少有一个门店ID
      if (finalStoreIds.length === 0) {
        throw new Error('请至少选择一个门店');
      }

      // 确保日期格式正确
      let startDate = null;
      if (values.start_date) {
        // 使用 startOf('day') 确保只有日期部分
        startDate = values.start_date.startOf('day').format('YYYY-MM-DD');
      } else {
        startDate = dayjs().startOf('day').format('YYYY-MM-DD');
      }

      let dueDate = null;
      if (values.due_date) {
        dueDate = values.due_date.startOf('day').format('YYYY-MM-DD');
      }

      // 确保任务类别为运营
      // 根据后端API要求，如果是单个门店，store_id应该是字符串而不是数组
      const taskData = {
        ...values,
        store_id: finalStoreIds.length === 1 ? finalStoreIds[0] : finalStoreIds,
        is_all_stores: isAllStores,
        start_date: startDate,
        due_date: dueDate,
        category: 'operation', // 强制设置为运营类别
        tags: values.task_type ? [values.task_type] : ['display'], // 使用tags字段存储任务类型
        attachments: fileList.map(file => file.response?.url || file.url)
      };

      // 删除task_type字段，因为后端模型中没有这个字段
      if (taskData.task_type) {
        delete taskData.task_type;
      }

      // 打印提交的数据，方便调试
      console.log('提交任务数据:', taskData);

      if (editingTask) {
        // 更新任务
        await apiService.project.routineTask.update(editingTask.id, taskData);
        message.success('任务更新成功');
      } else {
        // 创建任务
        await apiService.project.routineTask.create(taskData);
        message.success('任务创建成功');
      }

      setModalVisible(false);
      fetchTasks();
    } catch (error) {
      console.error('提交任务失败:', error);
      if (error.errorFields) {
        // 表单验证错误
        return;
      }
      // 显示更详细的错误信息
      if (error.response?.data?.detail) {
        if (Array.isArray(error.response.data.detail)) {
          // 如果是数组形式的错误信息
          const errorDetails = error.response.data.detail.map(err => {
            return `${err.loc.join('.')}：${err.msg}`;
          }).join('; ');
          message.error('提交任务失败: ' + errorDetails);
        } else {
          message.error('提交任务失败: ' + error.response.data.detail);
        }
      } else {
        message.error('提交任务失败: ' + error.message);
      }

      // 打印完整错误信息到控制台，方便调试
      console.error('提交任务失败详细信息:', error.response?.data);
    } finally {
      setLoading(false);
    }
  };

  // 删除任务
  const handleDeleteTask = async (id) => {
    setLoading(true);
    try {
      await apiService.project.routineTask.delete(id);
      message.success('任务删除成功');
      fetchTasks();
    } catch (error) {
      console.error('删除任务失败:', error);
      message.error('删除任务失败: ' + (error.response?.data?.detail || error.message));
    } finally {
      setLoading(false);
    }
  };

  // 更新任务状态
  const handleUpdateTaskStatus = async (id, status) => {
    setLoading(true);
    try {
      await apiService.project.routineTask.updateStatus(id, { status });
      message.success('任务状态更新成功');
      fetchTasks();
    } catch (error) {
      console.error('更新任务状态失败:', error);
      message.error('更新任务状态失败: ' + (error.response?.data?.detail || error.message));
    } finally {
      setLoading(false);
    }
  };

  // 处理标签页切换
  const handleTabChange = (key) => {
    setActiveTab(key);
  };

  // 文件上传配置
  const uploadProps = {
    name: 'file',
    action: '/api/upload',
    headers: {
      authorization: 'Bearer ' + localStorage.getItem('token'),
    },
    onChange(info) {
      if (info.file.status === 'done') {
        message.success(`${info.file.name} 上传成功`);
      } else if (info.file.status === 'error') {
        message.error(`${info.file.name} 上传失败`);
      }
      setFileList(info.fileList);
    },
  };

  // 获取用户列表
  const fetchUsers = async (storeIds = []) => {
    return new Promise(async (resolve) => {
      try {
        setLoadingUsers(true);
        const response = await apiService.project.user.getList();

        if (response && Array.isArray(response)) {
          // 处理返回的数据格式
          const allUsers = response
            .filter(item => item.success && item.data)
            .map(item => {
              const userData = item.data;
              // 确保用户数据包含必要的字段
              return {
                ...userData,
                // 如果没有name，使用username
                name: userData.name || userData.username,
                // 如果有多个门店名称，用逗号连接
                store_name: Array.isArray(userData.store_names) && userData.store_names.length > 0
                  ? userData.store_names.join(', ')
                  : ''
              };
            });

          setUsers(allUsers);

          // 如果选择了特定门店，过滤用户
          if (storeIds && storeIds.length > 0 && !storeIds.includes('all')) {
            // 过滤出选定门店的用户
            const storeUsers = allUsers.filter(user => {
              // 检查用户是否属于选定的门店
              if (Array.isArray(user.store_ids)) {
                return user.store_ids.some(storeId =>
                  storeIds.includes(storeId.toString())
                );
              }
              return false;
            });

            setFilteredUsers(storeUsers.length > 0 ? storeUsers : allUsers);
          } else {
            // 如果选择了"所有门店"或没有选择门店，显示所有用户
            setFilteredUsers(allUsers);
          }
        } else if (response && response.items) {
          // 兼容原有的数据格式
          const allUsers = response.items;
          setUsers(allUsers);
          setFilteredUsers(allUsers);
        } else {
          // 如果没有数据，设置为空数组
          setUsers([]);
          setFilteredUsers([]);
        }
        resolve(true);
      } catch (error) {
        console.error('获取用户列表失败:', error);
        message.error('获取用户列表失败');
        resolve(false);
      } finally {
        setLoadingUsers(false);
      }
    });
  };

  // 处理门店选择变化
  const handleStoreChange = (values) => {
    // 检查是否选择了"所有门店"
    if (values && values.includes('all')) {
      // 如果选择了"所有门店"，移除其他所有选项
      form.setFieldsValue({ store_id: ['all'] });
      setSelectedStores(['all']);
      fetchUsers(); // 加载所有用户
    } else if (values && values.length > 0) {
      // 如果选择了特定门店，加载这些门店的用户
      setSelectedStores(values);
      fetchUsers(values);
    } else {
      // 如果没有选择任何门店，清空用户列表
      setSelectedStores([]);
      setFilteredUsers([]);
    }
  };

  // 处理文件上传前的验证
  const beforeUpload = (file) => {
    const isLt10M = file.size / 1024 / 1024 < 10;
    if (!isLt10M) {
      message.error('文件大小不能超过10MB!');
    }
    return isLt10M;
  };

  // 处理文件上传变化
  const handleFileChange = ({ fileList: newFileList }) => {
    setFileList(newFileList);
  };

  // 初始化
  useEffect(() => {
    fetchTasks();
    fetchUsers();
  }, []);

  // 搜索表单
  const SearchForm = () => (
    <Form
      layout="inline"
      form={searchForm}
      initialValues={searchParams}
      onFinish={handleSearch}
      style={{ marginBottom: 16 }}
    >
      <Form.Item name="status" label="状态">
        <Select style={{ width: 120 }} allowClear>
          <Option value="">全部</Option>
          <Option value="pending">待处理</Option>
          <Option value="in_progress">进行中</Option>
          <Option value="completed">已完成</Option>
          <Option value="overdue">已逾期</Option>
          <Option value="cancelled">已取消</Option>
        </Select>
      </Form.Item>

      <Form.Item name="category" label="类别">
        <Select style={{ width: 120 }} allowClear>
          <Option value="">全部</Option>
          <Option value="store">门店</Option>
          <Option value="operation">运营</Option>
          <Option value="marketing">营销</Option>
          <Option value="inventory">库存</Option>
          <Option value="finance">财务</Option>
          <Option value="other">其他</Option>
        </Select>
      </Form.Item>

      <Form.Item name="priority" label="优先级">
        <Select style={{ width: 120 }} allowClear>
          <Option value="">全部</Option>
          <Option value="low">低</Option>
          <Option value="medium">中</Option>
          <Option value="high">高</Option>
          <Option value="urgent">紧急</Option>
        </Select>
      </Form.Item>

      <Form.Item name="store_id" label="门店">
        <Select style={{ width: 150 }} allowClear>
          <Option value="">全部</Option>
          {stores.map(store => (
            <Option key={store.id} value={store.id}>{store.name}</Option>
          ))}
        </Select>
      </Form.Item>

      <Form.Item name="dateRange" label="日期范围">
        <RangePicker style={{ width: 240 }} />
      </Form.Item>

      <Form.Item name="search" label="搜索">
        <Input
          placeholder="任务标题/描述"
          prefix={<SearchOutlined />}
          style={{ width: 200 }}
        />
      </Form.Item>

      <Form.Item>
        <Button type="primary" htmlType="submit">
          搜索
        </Button>
      </Form.Item>

      <Form.Item>
        <Button onClick={handleReset}>
          重置
        </Button>
      </Form.Item>
    </Form>
  );

  // 任务表格列定义
  const columns = [
    {
      title: '任务标题',
      dataIndex: 'title',
      key: 'title',
      render: (text, record) => (
        <a onClick={() => handleOpenTaskForm(record)}>{text}</a>
      )
    },
    {
      title: '门店',
      dataIndex: 'store_name',
      key: 'store_name',
      ellipsis: true,
      render: (text, record) => {
        if (record.is_all_stores) {
          return <Tag color="blue">所有门店</Tag>;
        } else if (record.store_names && record.store_names.length > 0) {
          return (
            <Tooltip title={record.store_names.join(', ')}>
              <span>{record.store_names.length > 1 ? `${record.store_names[0]} 等 ${record.store_names.length} 个门店` : text}</span>
            </Tooltip>
          );
        } else {
          return text || '-';
        }
      }
    },
    {
      title: '类别',
      dataIndex: 'category',
      key: 'category',
      render: (text) => categoryTexts[text] || text
    },
    {
      title: '任务类型',
      dataIndex: 'task_type',
      key: 'task_type',
      render: (text) => taskTypeTexts[text] || text
    },
    {
      title: '优先级',
      dataIndex: 'priority',
      key: 'priority',
      render: (priority) => (
        <Tag color={priorityColors[priority] || 'default'}>
          {priorityTexts[priority] || priority}
        </Tag>
      )
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={statusColors[status] || 'default'}>
          {statusTexts[status] || status}
        </Tag>
      )
    },
    {
      title: '开始日期',
      dataIndex: 'start_date',
      key: 'start_date',
      render: (text) => text ? dayjs(text).format('YYYY-MM-DD') : '-'
    },
    {
      title: '截止日期',
      dataIndex: 'due_date',
      key: 'due_date',
      render: (text) => {
        if (!text) return '-';
        const dueDate = dayjs(text);
        const today = dayjs();
        const isOverdue = dueDate.isBefore(today, 'day');
        const isToday = dueDate.isSame(today, 'day');

        return (
          <span style={{ color: isOverdue ? '#f5222d' : (isToday ? '#faad14' : 'inherit') }}>
            {dueDate.format('YYYY-MM-DD')}
          </span>
        );
      }
    },
    {
      title: '指派人',
      dataIndex: 'assignee_names',
      key: 'assignees',
      render: (assignees) => {
        if (!assignees || assignees.length === 0) return '-';
        return (
          <Avatar.Group max={{ count: 3, style: { color: '#f56a00', backgroundColor: '#fde3cf' } }}>
            {assignees.map((name, index) => (
              <Tooltip key={index} title={name}>
                <Avatar style={{ backgroundColor: '#' + Math.floor(Math.random()*16777215).toString(16) }}>
                  {name.substring(0, 1)}
                </Avatar>
              </Tooltip>
            ))}
          </Avatar.Group>
        );
      }
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_, record) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleOpenTaskForm(record)}
          >
            编辑
          </Button>

          <Dropdown overlay={
            <Menu>
              {record.status !== 'in_progress' && (
                <Menu.Item key="start" onClick={() => handleUpdateTaskStatus(record.id, 'in_progress')}>
                  <ClockCircleOutlined /> 开始任务
                </Menu.Item>
              )}

              {record.status !== 'completed' && (
                <Menu.Item key="complete" onClick={() => handleUpdateTaskStatus(record.id, 'completed')}>
                  <CheckCircleOutlined /> 完成任务
                </Menu.Item>
              )}

              {record.status !== 'cancelled' && (
                <Menu.Item key="cancel" onClick={() => handleUpdateTaskStatus(record.id, 'cancelled')}>
                  <CloseCircleOutlined /> 取消任务
                </Menu.Item>
              )}

              <Menu.Divider />

              <Menu.Item key="delete" danger onClick={() => {
                Modal.confirm({
                  title: '确认删除',
                  content: `确定要删除任务 "${record.title}" 吗？此操作不可恢复。`,
                  okText: '删除',
                  okType: 'danger',
                  cancelText: '取消',
                  onOk: () => handleDeleteTask(record.id)
                });
              }}>
                <DeleteOutlined /> 删除任务
              </Menu.Item>
            </Menu>
          }>
            <Button
              type="link"
              size="small"
              icon={<MoreOutlined />}
            />
          </Dropdown>
        </Space>
      )
    }
  ];

  // 任务表单
  const TaskForm = () => (
    <Form
      form={form}
      layout="vertical"
    >
      <Form.Item
        name="title"
        label="任务标题"
        rules={[{ required: true, message: '请输入任务标题' }]}
      >
        <Input placeholder="请输入任务标题" />
      </Form.Item>

      <Form.Item
        name="description"
        label="任务描述"
        rules={[{ required: true, message: '请输入任务描述' }]}
      >
        <TextArea rows={4} placeholder="请输入任务描述" />
      </Form.Item>

      <Row gutter={16}>
        <Col span={12}>
          <Form.Item
            name="store_id"
            label="所属门店"
            rules={[{ required: true, message: '请选择门店' }]}
          >
            <Select
              placeholder="请选择门店"
              allowClear
              showSearch
              optionFilterProp="children"
              onChange={(value) => handleStoreChange(value)}
              mode="multiple"
              maxTagCount={3}
            >
              <Option key="all" value="all">所有门店</Option>
              {stores.map(store => (
                <Option key={store.id} value={store.id}>{store.name}</Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            name="category"
            label="任务类别"
            rules={[{ required: true, message: '请选择任务类别' }]}
            initialValue="operation"
          >
            <Select placeholder="请选择任务类别" disabled defaultValue="operation">
              <Option value="operation">运营</Option>
            </Select>
            <div style={{ color: '#999', fontSize: '12px', marginTop: '4px' }}>
              运营管理模块只能创建运营类别的任务
            </div>
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={12}>
          <Form.Item
            name="task_type"
            label="任务类型"
            rules={[{ required: true, message: '请选择任务类型' }]}
          >
            <Select placeholder="请选择任务类型">
              <Option value="display">陈列任务</Option>
              <Option value="promotion">营销推广</Option>
              <Option value="product_promotion">单品推广</Option>
              <Option value="daily">日常任务</Option>
              <Option value="weekly">周期任务</Option>
              <Option value="monthly">月度任务</Option>
              <Option value="other">其他任务</Option>
            </Select>
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            name="priority"
            label="优先级"
            rules={[{ required: true, message: '请选择优先级' }]}
          >
            <Select placeholder="请选择优先级">
              <Option value="low">低</Option>
              <Option value="medium">中</Option>
              <Option value="high">高</Option>
              <Option value="urgent">紧急</Option>
            </Select>
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={24}>
          <Form.Item
            name="assignee_ids"
            label="指派人"
            rules={[{ required: true, message: '请选择指派人' }]}
          >
            <Select
              mode="multiple"
              placeholder="请选择指派人"
              optionFilterProp="children"
              loading={loadingUsers}
              showSearch
              maxTagCount={3}
            >
              {filteredUsers.map(user => (
                <Option key={user.id} value={user.id}>
                  {user.name || user.username} {user.store_name ? `(${user.store_name})` : ''}
                </Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={12}>
          <Form.Item
            name="start_date"
            label="开始日期"
            rules={[{ required: true, message: '请选择开始日期' }]}
          >
            <DatePicker
              style={{ width: '100%' }}
              format="YYYY-MM-DD"
              placeholder="请选择开始日期"
              showTime={false} // 不显示时间选择器
              onChange={(date) => {
                if (date) {
                  // 确保只有日期部分
                  const dateOnly = date.startOf('day');
                  form.setFieldsValue({ start_date: dateOnly });
                }
              }}
            />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            name="due_date"
            label="截止日期"
          >
            <DatePicker
              style={{ width: '100%' }}
              format="YYYY-MM-DD"
              placeholder="请选择截止日期"
              showTime={false} // 不显示时间选择器
              onChange={(date) => {
                if (date) {
                  // 确保只有日期部分
                  const dateOnly = date.startOf('day');
                  form.setFieldsValue({ due_date: dateOnly });
                }
              }}
            />
          </Form.Item>
        </Col>
      </Row>

      <Form.Item
        name="status"
        label="状态"
        rules={[{ required: true, message: '请选择状态' }]}
      >
        <Select placeholder="请选择状态">
          <Option value="pending">待处理</Option>
          <Option value="in_progress">进行中</Option>
          <Option value="completed">已完成</Option>
          <Option value="cancelled">已取消</Option>
        </Select>
      </Form.Item>

      <Form.Item
        name="attachments"
        label="附件"
      >
        <Upload
          listType="picture"
          fileList={fileList}
          onChange={handleFileChange}
          beforeUpload={beforeUpload}
          action="/api/v1/upload"
          multiple
        >
          <Button icon={<UploadOutlined />}>上传附件</Button>
        </Upload>
      </Form.Item>
    </Form>
  );

  return (
    <div className="task-management">
      <Tabs activeKey={activeTab} onChange={handleTabChange}>
        <TabPane tab="任务列表" key="1">
          <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between' }}>
            <div>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => handleOpenTaskForm()}
                style={{ marginRight: 8 }}
              >
                新建任务
              </Button>
              <Button
                icon={<SyncOutlined />}
                onClick={() => fetchTasks()}
              >
                刷新
              </Button>
            </div>
          </div>

          <Row gutter={16} style={{ marginBottom: 16 }}>
            <Col span={4}>
              <Card>
                <Statistic
                  title="任务总数"
                  value={taskStats.total}
                  suffix="个"
                />
              </Card>
            </Col>
            <Col span={4}>
              <Card>
                <Statistic
                  title="待处理"
                  value={taskStats.pending}
                  suffix="个"
                  valueStyle={{ color: '#1890ff' }}
                />
              </Card>
            </Col>
            <Col span={4}>
              <Card>
                <Statistic
                  title="进行中"
                  value={taskStats.in_progress}
                  suffix="个"
                  valueStyle={{ color: '#722ed1' }}
                />
              </Card>
            </Col>
            <Col span={4}>
              <Card>
                <Statistic
                  title="已完成"
                  value={taskStats.completed}
                  suffix="个"
                  valueStyle={{ color: '#52c41a' }}
                />
              </Card>
            </Col>
            <Col span={4}>
              <Card>
                <Statistic
                  title="已逾期"
                  value={taskStats.overdue}
                  suffix="个"
                  valueStyle={{ color: '#f5222d' }}
                />
              </Card>
            </Col>
            <Col span={4}>
              <Card>
                <Statistic
                  title="已取消"
                  value={taskStats.cancelled}
                  suffix="个"
                  valueStyle={{ color: '#faad14' }}
                />
              </Card>
            </Col>
          </Row>

          <SearchForm />

          <Table
            columns={columns}
            dataSource={tasks}
            rowKey="id"
            pagination={pagination}
            onChange={handleTableChange}
            loading={loading}
            scroll={{ x: 1200 }}
          />
        </TabPane>

        <TabPane tab="门店统计" key="2">
          <Table
            dataSource={storeStats}
            rowKey="store_id"
            pagination={false}
            loading={loading}
            columns={[
              {
                title: '门店',
                dataIndex: 'store_name',
                key: 'store_name',
              },
              {
                title: '任务总数',
                dataIndex: 'total',
                key: 'total',
                sorter: (a, b) => a.total - b.total,
              },
              {
                title: '待处理',
                dataIndex: 'pending',
                key: 'pending',
                sorter: (a, b) => a.pending - b.pending,
              },
              {
                title: '进行中',
                dataIndex: 'in_progress',
                key: 'in_progress',
                sorter: (a, b) => a.in_progress - b.in_progress,
              },
              {
                title: '已完成',
                dataIndex: 'completed',
                key: 'completed',
                sorter: (a, b) => a.completed - b.completed,
              },
              {
                title: '已逾期',
                dataIndex: 'overdue',
                key: 'overdue',
                sorter: (a, b) => a.overdue - b.overdue,
              },
              {
                title: '完成率',
                dataIndex: 'completion_rate',
                key: 'completion_rate',
                render: (text) => `${text}%`,
                sorter: (a, b) => a.completion_rate - b.completion_rate,
              },
            ]}
          />
        </TabPane>
      </Tabs>

      <Modal
        title={editingTask ? '编辑任务' : '新建任务'}
        open={modalVisible}
        onOk={handleSubmitTask}
        onCancel={() => setModalVisible(false)}
        width={700}
        confirmLoading={loading}
      >
        <TaskForm />
      </Modal>
    </div>
  );
};

export default TaskManagement;