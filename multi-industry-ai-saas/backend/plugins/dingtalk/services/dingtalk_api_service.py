import json
import time
import requests
from typing import Dict, Any, List, Optional
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

class DingTalkAPIService:
    """钉钉开放平台API服务 - 基于官方文档实现"""
    
    def __init__(self, app_key: str, app_secret: str):
        self.app_key = app_key
        self.app_secret = app_secret
        self.base_url = "https://oapi.dingtalk.com"
        self._access_token = None
        self._token_expires_at = 0
    
    async def get_access_token(self) -> str:
        """获取企业内部应用的accessToken"""
        
        # 检查token是否过期
        if self._access_token and time.time() < self._token_expires_at:
            return self._access_token
        
        try:
            url = f"{self.base_url}/gettoken"
            params = {
                "appkey": self.app_key,
                "appsecret": self.app_secret
            }
            
            response = requests.get(url, params=params)
            result = response.json()
            
            if result.get("errcode") == 0:
                self._access_token = result["access_token"]
                # 提前5分钟刷新token
                self._token_expires_at = time.time() + result.get("expires_in", 7200) - 300
                return self._access_token
            else:
                raise Exception(f"获取access_token失败: {result.get('errmsg')}")
                
        except Exception as e:
            logger.error(f"获取钉钉access_token失败: {str(e)}")
            raise
    
    async def create_or_update_approval_template(self, template_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        创建或更新审批模板
        对应钉钉文档：https://open.dingtalk.com/document/isvapp/create-or-update-approval-templates-new
        使用正确的v1.0端点和数据结构
        """
        try:
            access_token = await self.get_access_token()
            # 使用正确的API端点和域名 - v1.0 API使用api.dingtalk.com域名
            url = f"https://api.dingtalk.com/v1.0/workflow/processCentres/schemas"
            
            # 使用正确的请求头认证方式
            headers = {
                "x-acs-dingtalk-access-token": access_token,
                "Content-Type": "application/json"
            }
            
            # 构建请求数据 - 严格按照钉钉官方文档的标准结构
            # 参考：https://open.dingtalk.com/document/isvapp/create-or-update-approval-templates-new
            form_components = template_data.get("formComponents", template_data.get("formComponentList", template_data.get("form_component_list", [])))
            
            # 如果前端没有传递表单组件，根据模板类型自动构建
            if not form_components:
                template_type = template_data.get("type", "general")
                logger.warning(f"表单组件列表为空，根据模板类型 {template_type} 添加对应组件")
                
                # 使用 DingTalkAPI 的组件构建方法
                from ..utils.dingtalk_api import DingTalkAPI
                api_builder = DingTalkAPI("", "")  # 临时实例，仅用于组件构建
                
                if template_type == 'store_loss':
                    form_components = api_builder._build_store_loss_components()
                    logger.info(f"构建门店报损组件 {len(form_components)} 个")
                else:
                    # 默认组件
                    form_components = [
                        {
                            "componentType": "TextField",
                            "props": {
                                "componentId": "TextField_title",
                                "label": "申请标题",
                                "required": True,
                                "placeholder": "请输入申请标题"
                            }
                        },
                        {
                            "componentType": "TextareaField",
                            "props": {
                                "componentId": "TextareaField_content",
                                "label": "申请内容",
                                "required": True,
                                "placeholder": "请详细描述申请内容"
                            }
                        }
                    ]
            else:
                logger.info(f"使用前端传递的表单组件，数量: {len(form_components)}")
            
            # 使用官方API文档的完整数据结构
            request_data = {
                "name": template_data["name"],
                "description": template_data.get("description", ""),
                "formComponents": form_components,  # 官方文档字段名
                
                # 添加官方文档要求的processFeatureConfig
                "processFeatureConfig": template_data.get("processFeatureConfig", {
                    "features": []
                }),
                
                # 添加官方文档要求的templateConfig
                "templateConfig": template_data.get("templateConfig", {
                    "hidden": False,
                    "disableSendCard": False
                })
            }
            
            # 如果是更新模板，传入processCode
            if template_data.get("processCode") or template_data.get("process_code"):
                request_data["processCode"] = (
                    template_data.get("processCode") or template_data.get("process_code")
                )
            
            # 添加详细的请求日志
            logger.info(f"创建审批模板请求: {url}")
            logger.info(f"请求数据: {request_data}")
            
            # 使用新的API调用方式 - 请求头认证而非URL参数
            response = requests.post(url, headers=headers, json=request_data)
            result = response.json()
            
            # 添加详细的响应日志
            logger.info(f"钉钉API响应: {result}")
            
            if result.get("errcode") == 0 or "result" in result:
                process_code = result.get("result", {}).get("processCode") if "result" in result else result.get("processCode")
                return {
                    "success": True,
                    "result": {
                        "process_code": process_code
                    }
                }
            else:
                # 详细解析错误信息
                errcode = result.get('errcode')
                errmsg = result.get('errmsg', '未知错误')
                
                # 处理新版API可能返回的错误格式
                if not errcode and 'code' in result:
                    errcode = result.get('code')
                if not errmsg and 'message' in result:
                    errmsg = result.get('message')
                
                error_msg = f"钉钉API错误 - errcode: {errcode}, errmsg: {errmsg}"
                logger.error(error_msg)
                logger.error(f"完整API响应: {result}")
                logger.error(f"请求数据详情: {request_data}")
                return {
                    "success": False,
                    "message": error_msg
                }
                
        except Exception as e:
            logger.error(f"创建审批模板异常: {str(e)}")
            logger.error(f"异常详情: {type(e).__name__}: {str(e)}")
            return {
                "success": False,
                "message": f"创建审批模板失败: {str(e)}"
            }
    
    async def get_approval_instances(self, process_code: str, start_time: datetime = None, 
                                   size: int = 20, cursor: int = 0) -> Dict[str, Any]:
        """
        获取审批实例列表
        对应钉钉文档：查询审批实例接口
        """
        try:
            access_token = await self.get_access_token()
            url = f"{self.base_url}/topapi/processinstance/listids"
            
            # 构建请求数据
            request_data = {
                "process_code": process_code,
                "size": size,
                "cursor": cursor
            }
            
            # 如果指定了开始时间，添加时间过滤
            if start_time:
                # 钉钉API需要毫秒时间戳
                request_data["start_time"] = int(start_time.timestamp() * 1000)
            
            params = {"access_token": access_token}
            response = requests.post(url, params=params, json=request_data)
            result = response.json()
            
            if result.get("errcode") == 0:
                # 获取实例ID列表后，批量获取详细信息
                instance_ids = result["result"]["list"]
                instances = []
                
                for instance_id in instance_ids:
                    instance_detail = await self.get_approval_instance_detail(instance_id)
                    if instance_detail.get("success"):
                        instances.append(instance_detail["result"])
                
                return {
                    "success": True,
                    "result": {
                        "list": instances,
                        "has_more": result["result"]["has_more"]
                    }
                }
            else:
                return {
                    "success": False,
                    "message": f"获取审批实例失败: {result.get('errmsg', '未知错误')}"
                }
                
        except Exception as e:
            logger.error(f"获取审批实例失败: {str(e)}")
            return {
                "success": False,
                "message": f"获取审批实例失败: {str(e)}"
            }
    
    async def get_approval_instance_detail(self, process_instance_id: str) -> Dict[str, Any]:
        """
        获取审批实例详情
        对应钉钉文档：获取单个审批实例详情接口
        """
        try:
            access_token = await self.get_access_token()
            url = f"{self.base_url}/topapi/processinstance/get"
            
            request_data = {
                "process_instance_id": process_instance_id
            }
            
            params = {"access_token": access_token}
            response = requests.post(url, params=params, json=request_data)
            result = response.json()
            
            if result.get("errcode") == 0:
                instance_data = result["result"]["process_instance"]
                
                return {
                    "success": True,
                    "result": {
                        "process_instance_id": instance_data["process_instance_id"],
                        "title": instance_data.get("title", ""),
                        "status": instance_data["status"],
                        "result": instance_data.get("result"),
                        "originator_user_id": instance_data["originator_user_id"],
                        "create_time": instance_data.get("create_time"),
                        "finish_time": instance_data.get("finish_time"),
                        "form_component_values": instance_data.get("form_component_values", []),
                        "operation_records": instance_data.get("operation_records", []),
                        "tasks": instance_data.get("tasks", [])
                    }
                }
            else:
                return {
                    "success": False,
                    "message": f"获取审批实例详情失败: {result.get('errmsg', '未知错误')}"
                }
                
        except Exception as e:
            logger.error(f"获取审批实例详情失败: {str(e)}")
            return {
                "success": False,
                "message": f"获取审批实例详情失败: {str(e)}"
            }
    
    async def create_approval_instance(self, instance_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        创建审批实例
        对应钉钉文档：发起审批实例接口
        """
        try:
            access_token = await self.get_access_token()
            url = f"{self.base_url}/topapi/processinstance/create"
            
            request_data = {
                "process_code": instance_data["process_code"],
                "originator_user_id": instance_data["originator_user_id"],
                "form_component_values": instance_data["form_component_values"],
                "approvers": instance_data.get("approvers", []),
                "cc_list": instance_data.get("cc_list", []),
                "cc_position": instance_data.get("cc_position", "FINISH")
            }
            
            params = {"access_token": access_token}
            response = requests.post(url, params=params, json=request_data)
            result = response.json()
            
            if result.get("errcode") == 0:
                return {
                    "success": True,
                    "result": {
                        "process_instance_id": result["result"]["process_instance_id"]
                    }
                }
            else:
                return {
                    "success": False,
                    "message": f"创建审批实例失败: {result.get('errmsg', '未知错误')}"
                }
                
        except Exception as e:
            logger.error(f"创建审批实例失败: {str(e)}")
            return {
                "success": False,
                "message": f"创建审批实例失败: {str(e)}"
            }
    
    async def create_todo_task(self, task_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        创建流程中心待处理任务
        对应钉钉文档：创建流程中心待处理任务接口
        """
        try:
            access_token = await self.get_access_token()
            url = f"{self.base_url}/topapi/process/workrecord/create"
            
            request_data = {
                "process_instance_id": task_data["process_instance_id"],
                "activity_id": task_data["activity_id"],
                "activity_name": task_data.get("activity_name", ""),
                "activity_type": task_data.get("activity_type", "approval"),
                "userid": task_data["userid"],
                "task_id": task_data["task_id"],
                "url": task_data.get("url", ""),
                "pc_url": task_data.get("pc_url", ""),
                "title": task_data.get("title", ""),
                "create_time": task_data.get("create_time", int(time.time() * 1000))
            }
            
            params = {"access_token": access_token}
            response = requests.post(url, params=params, json=request_data)
            result = response.json()
            
            if result.get("errcode") == 0:
                return {
                    "success": True,
                    "result": {
                        "task_id": result["result"]["task_id"]
                    }
                }
            else:
                return {
                    "success": False,
                    "message": f"创建待办任务失败: {result.get('errmsg', '未知错误')}"
                }
                
        except Exception as e:
            logger.error(f"创建待办任务失败: {str(e)}")
            return {
                "success": False,
                "message": f"创建待办任务失败: {str(e)}"
            }
    
    async def update_task_status(self, task_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        更新流程中心任务状态
        对应钉钉文档：更新流程中心任务状态接口
        """
        try:
            access_token = await self.get_access_token()
            url = f"{self.base_url}/topapi/process/workrecord/update"
            
            request_data = {
                "process_instance_id": task_data["process_instance_id"],
                "task_id": task_data["task_id"],
                "task_status": task_data["task_status"],  # COMPLETED, CANCELED
                "result": task_data.get("result", "agree")  # agree, refuse
            }
            
            params = {"access_token": access_token}
            response = requests.post(url, params=params, json=request_data)
            result = response.json()
            
            if result.get("errcode") == 0:
                return {
                    "success": True,
                    "message": "任务状态更新成功"
                }
            else:
                return {
                    "success": False,
                    "message": f"更新任务状态失败: {result.get('errmsg', '未知错误')}"
                }
                
        except Exception as e:
            logger.error(f"更新任务状态失败: {str(e)}")
            return {
                "success": False,
                "message": f"更新任务状态失败: {str(e)}"
            }
    
    async def update_instance_status(self, instance_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        更新实例状态
        对应钉钉文档：更新实例状态接口
        """
        try:
            access_token = await self.get_access_token()
            url = f"{self.base_url}/topapi/processinstance/update"
            
            request_data = {
                "process_instance_id": instance_data["process_instance_id"],
                "status": instance_data["status"],  # COMPLETED, TERMINATED
                "result": instance_data.get("result", "agree"),  # agree, refuse
                "notifiers": instance_data.get("notifiers", [])
            }
            
            params = {"access_token": access_token}
            response = requests.post(url, params=params, json=request_data)
            result = response.json()
            
            if result.get("errcode") == 0:
                return {
                    "success": True,
                    "message": "实例状态更新成功"
                }
            else:
                return {
                    "success": False,
                    "message": f"更新实例状态失败: {result.get('errmsg', '未知错误')}"
                }
                
        except Exception as e:
            logger.error(f"更新实例状态失败: {str(e)}")
            return {
                "success": False,
                "message": f"更新实例状态失败: {str(e)}"
            }
    
    async def delete_approval_template(self, process_code: str) -> Dict[str, Any]:
        """
        删除审批模板
        对应钉钉文档：删除模板接口
        """
        try:
            access_token = await self.get_access_token()
            url = f"{self.base_url}/topapi/process/delete"
            
            request_data = {
                "process_code": process_code
            }
            
            params = {"access_token": access_token}
            response = requests.post(url, params=params, json=request_data)
            result = response.json()
            
            if result.get("errcode") == 0:
                return {
                    "success": True,
                    "message": "审批模板删除成功"
                }
            else:
                return {
                    "success": False,
                    "message": f"删除审批模板失败: {result.get('errmsg', '未知错误')}"
                }
                
        except Exception as e:
            logger.error(f"删除审批模板失败: {str(e)}")
            return {
                "success": False,
                "message": f"删除审批模板失败: {str(e)}"
            }
    
    async def get_template_by_code(self, process_code: str) -> Dict[str, Any]:
        """
        获取模板code
        对应钉钉文档：获取模板code接口
        """
        try:
            access_token = await self.get_access_token()
            url = f"{self.base_url}/topapi/process/get"
            
            request_data = {
                "process_code": process_code
            }
            
            params = {"access_token": access_token}
            response = requests.post(url, params=params, json=request_data)
            result = response.json()
            
            if result.get("errcode") == 0:
                return {
                    "success": True,
                    "result": result["result"]
                }
            else:
                return {
                    "success": False,
                    "message": f"获取模板信息失败: {result.get('errmsg', '未知错误')}"
                }
                
        except Exception as e:
            logger.error(f"获取模板信息失败: {str(e)}")
            return {
                "success": False,
                "message": f"获取模板信息失败: {str(e)}"
            }
    
    async def get_user_info(self, user_id: str) -> Dict[str, Any]:
        """获取用户信息"""
        try:
            access_token = await self.get_access_token()
            url = f"{self.base_url}/topapi/v2/user/get"
            
            request_data = {
                "userid": user_id
            }
            
            params = {"access_token": access_token}
            response = requests.post(url, params=params, json=request_data)
            result = response.json()
            
            if result.get("errcode") == 0:
                return {
                    "success": True,
                    "result": result["result"]
                }
            else:
                return {
                    "success": False,
                    "message": f"获取用户信息失败: {result.get('errmsg', '未知错误')}"
                }
                
        except Exception as e:
            logger.error(f"获取用户信息失败: {str(e)}")
            return {
                "success": False,
                "message": f"获取用户信息失败: {str(e)}"
            }
    
    # ==================== 知识库文档管理 ====================
    
    async def get_workspaces(self) -> Dict[str, Any]:
        """
        获取工作空间列表
        对应钉钉接口：新版知识库-获取工作空间列表
        需要权限：Wiki.Workspace.Read
        """
        try:
            access_token = await self.get_access_token()
            # 使用新版知识库API
            url = f"https://api.dingtalk.com/v1.0/doc/workspaces"
            
            headers = {
                "x-acs-dingtalk-access-token": access_token,
                "Content-Type": "application/json"
            }
            
            params = {
                "maxResults": 100
            }
            
            logger.info(f"调用新版钉钉工作空间列表API: {url}")
            response = requests.get(url, headers=headers, params=params)
            result = response.json()
            
            logger.info(f"工作空间API响应: {result}")
            
            if response.status_code == 200:
                return {
                    "success": True,
                    "result": {
                        "list": result.get("workspaces", [])
                    }
                }
            elif response.status_code == 403:
                error_msg = f"权限不足，请确保已开通知识库权限 Wiki.Workspace.Read: HTTP {response.status_code}, {result}"
                logger.error(error_msg)
                return {
                    "success": False,
                    "message": error_msg
                }
            else:
                error_msg = f"获取工作空间失败: HTTP {response.status_code}, {result}"
                logger.error(error_msg)
                return {
                    "success": False,
                    "message": error_msg
                }
                
        except Exception as e:
            logger.error(f"获取工作空间异常: {str(e)}")
            return {
                "success": False,
                "message": f"获取工作空间失败: {str(e)}"
            }
    
    async def get_spaces(self, workspace_id: str = None) -> Dict[str, Any]:
        """
        获取知识库列表
        对应钉钉接口：新版知识库-获取团队空间列表
        需要权限：Document.WorkspaceDocument.Write, Wiki.Workspace.Read
        """
        try:
            access_token = await self.get_access_token()
            # 使用新版知识库API
            url = f"https://api.dingtalk.com/v1.0/doc/spaces"
            
            headers = {
                "x-acs-dingtalk-access-token": access_token,
                "Content-Type": "application/json"
            }
            
            params = {
                "maxResults": 100
            }
            
            if workspace_id:
                params["workspaceId"] = workspace_id
            
            logger.info(f"调用新版钉钉知识库列表API: {url}")
            logger.info(f"请求参数: {params}")
            
            response = requests.get(url, headers=headers, params=params)
            result = response.json()
            
            logger.info(f"知识库API响应: {result}")
            
            if response.status_code == 200:
                return {
                    "success": True,
                    "result": {
                        "list": result.get("spaces", [])
                    }
                }
            elif response.status_code == 403:
                error_msg = f"权限不足，请确保已开通知识库权限 Document.WorkspaceDocument.Write 或 Document.WorkspaceDocument.Read: HTTP {response.status_code}, {result}"
                logger.error(error_msg)
                return {
                    "success": False,
                    "message": error_msg
                }
            else:
                error_msg = f"获取知识库失败: HTTP {response.status_code}, {result}"
                logger.error(error_msg)
                return {
                    "success": False,
                    "message": error_msg
                }
                
        except Exception as e:
            logger.error(f"获取知识库异常: {str(e)}")
            return {
                "success": False,
                "message": f"获取知识库失败: {str(e)}"
            }
    
    async def get_documents(self, space_id: str, workspace_id: str = None) -> Dict[str, Any]:
        """
        获取文档列表
        对应钉钉接口：新版知识库-获取文档列表
        需要权限：Document.WorkspaceDocument.Write
        """
        try:
            access_token = await self.get_access_token()
            # 使用新版知识库API
            url = f"https://api.dingtalk.com/v1.0/doc/dentries"
            
            headers = {
                "x-acs-dingtalk-access-token": access_token,
                "Content-Type": "application/json"
            }
            
            params = {
                "spaceId": space_id,
                "maxResults": 100
            }
            
            logger.info(f"调用新版钉钉文档列表API: {url}")
            logger.info(f"请求参数: {params}")
            
            response = requests.get(url, headers=headers, params=params)
            result = response.json()
            
            logger.info(f"文档列表API响应: {result}")
            
            if response.status_code == 200:
                return {
                    "success": True,
                    "result": {
                        "list": result.get("dentries", [])
                    }
                }
            else:
                error_msg = f"获取文档列表失败: HTTP {response.status_code}, {result}"
                logger.error(error_msg)
                return {
                    "success": False,
                    "message": error_msg
                }
                
        except Exception as e:
            logger.error(f"获取文档列表异常: {str(e)}")
            return {
                "success": False,
                "message": f"获取文档列表失败: {str(e)}"
            }
    
    async def get_document_content(self, doc_id: str, space_id: str, workspace_id: str = None) -> Dict[str, Any]:
        """
        获取文档内容
        对应钉钉接口：新版知识库-获取文档内容
        需要权限：Document.WorkspaceDocument.Write
        """
        try:
            access_token = await self.get_access_token()
            # 使用新版知识库API
            url = f"https://api.dingtalk.com/v1.0/doc/dentries/{doc_id}/contents"
            
            headers = {
                "x-acs-dingtalk-access-token": access_token,
                "Content-Type": "application/json"
            }
            
            params = {
                "targetFormat": "markdown"  # 支持markdown、docx等格式
            }
            
            logger.info(f"调用新版钉钉文档内容API: {url}")
            logger.info(f"请求参数: {params}")
            
            response = requests.get(url, headers=headers, params=params)
            result = response.json()
            
            logger.info(f"文档内容API响应: {result}")
            
            if response.status_code == 200:
                return {
                    "success": True,
                    "result": result
                }
            else:
                error_msg = f"获取文档内容失败: HTTP {response.status_code}, {result}"
                logger.error(error_msg)
                return {
                    "success": False,
                    "message": error_msg
                }
                
        except Exception as e:
            logger.error(f"获取文档内容异常: {str(e)}")
            return {
                "success": False,
                "message": f"获取文档内容失败: {str(e)}"
            }
    
    async def create_document(self, document_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        创建文档
        对应钉钉接口：新版知识库-创建文档
        需要权限：Document.WorkspaceDocument.Write
        """
        try:
            access_token = await self.get_access_token()
            # 使用新版知识库API
            url = f"https://api.dingtalk.com/v1.0/doc/dentries"
            
            headers = {
                "x-acs-dingtalk-access-token": access_token,
                "Content-Type": "application/json"
            }
            
            request_data = {
                "spaceId": document_data["space_id"],
                "name": document_data["name"],
                "content": document_data.get("content", ""),
                "dentryType": document_data.get("type", "doc")  # doc, sheet, etc.
            }
            
            if document_data.get("parent_doc_id"):
                request_data["parentUuid"] = document_data["parent_doc_id"]
            
            logger.info(f"调用新版钉钉创建文档API: {url}")
            logger.info(f"请求数据: {request_data}")
            
            response = requests.post(url, headers=headers, json=request_data)
            result = response.json()
            
            logger.info(f"创建文档API响应: {result}")
            
            if response.status_code == 200:
                return {
                    "success": True,
                    "result": {
                        "doc_id": result.get("dentryUuid"),
                        "url": result.get("url")
                    }
                }
            else:
                error_msg = f"创建文档失败: HTTP {response.status_code}, {result}"
                logger.error(error_msg)
                return {
                    "success": False,
                    "message": error_msg
                }
                
        except Exception as e:
            logger.error(f"创建文档异常: {str(e)}")
            return {
                "success": False,
                "message": f"创建文档失败: {str(e)}"
            }
    
    async def update_document(self, doc_id: str, document_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        更新文档
        对应钉钉接口：更新文档
        """
        try:
            access_token = await self.get_access_token()
            url = f"{self.base_url}/topapi/wiki/doc/update"
            
            request_data = {
                "doc_id": doc_id
            }
            
            if document_data.get("name"):
                request_data["name"] = document_data["name"]
            
            if document_data.get("content"):
                request_data["content"] = document_data["content"]
            
            params = {"access_token": access_token}
            response = requests.post(url, params=params, json=request_data)
            result = response.json()
            
            if result.get("errcode") == 0:
                return {
                    "success": True,
                    "message": "文档更新成功"
                }
            else:
                return {
                    "success": False,
                    "message": f"更新文档失败: {result.get('errmsg', '未知错误')}"
                }
                
        except Exception as e:
            logger.error(f"更新文档失败: {str(e)}")
            return {
                "success": False,
                "message": f"更新文档失败: {str(e)}"
            }
    
    async def delete_document(self, doc_id: str, space_id: str, workspace_id: str = None) -> Dict[str, Any]:
        """
        删除文档
        对应钉钉接口：删除文档
        """
        try:
            access_token = await self.get_access_token()
            url = f"{self.base_url}/topapi/wiki/doc/delete"
            
            request_data = {
                "doc_id": doc_id,
                "space_id": space_id
            }
            if workspace_id:
                request_data["workspace_id"] = workspace_id
            
            params = {"access_token": access_token}
            response = requests.post(url, params=params, json=request_data)
            result = response.json()
            
            if result.get("errcode") == 0:
                return {
                    "success": True,
                    "message": "文档删除成功"
                }
            else:
                return {
                    "success": False,
                    "message": f"删除文档失败: {result.get('errmsg', '未知错误')}"
                }
                
        except Exception as e:
            logger.error(f"删除文档失败: {str(e)}")
            return {
                "success": False,
                "message": f"删除文档失败: {str(e)}"
            }
    
    async def get_group_files(self, group_id: str, cursor: int = 0, size: int = 20) -> Dict[str, Any]:
        """
        获取群文件列表
        对应钉钉接口：获取群文件列表
        """
        try:
            access_token = await self.get_access_token()
            url = f"{self.base_url}/topapi/im/chat/scencegroup/file/query"
            
            request_data = {
                "open_conversation_id": group_id,
                "cursor": cursor,
                "size": size
            }
            
            params = {"access_token": access_token}
            response = requests.post(url, params=params, json=request_data)
            result = response.json()
            
            if result.get("errcode") == 0:
                return {
                    "success": True,
                    "result": {
                        "list": result["result"]["file_list"],
                        "has_more": result["result"]["has_more"]
                    }
                }
            else:
                return {
                    "success": False,
                    "message": f"获取群文件失败: {result.get('errmsg', '未知错误')}"
                }
                
        except Exception as e:
            logger.error(f"获取群文件失败: {str(e)}")
            return {
                "success": False,
                "message": f"获取群文件失败: {str(e)}"
            } 