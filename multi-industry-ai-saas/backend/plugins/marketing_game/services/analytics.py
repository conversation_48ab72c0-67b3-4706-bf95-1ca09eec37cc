#!/usr/bin/env python
# -*- coding: utf-8 -*-

import asyncio
import json
import logging
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timedelta, date
from collections import defaultdict, Counter
import pandas as pd
import numpy as np
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import desc, func, and_, or_, text

from ..models.models import (
    MarketingGame, GameRecord, GameParticipant, GamePrize, GameAnalytics,
    MarketingType, GameType, GameStatus
)
from models.store import Store  # 假设有门店模型

logger = logging.getLogger(__name__)

class MarketingGameAnalytics:
    """营销游戏数据分析服务"""
    
    def __init__(self):
        pass
    
    async def generate_game_report(self, game_id: str, db: AsyncSession, 
                                 start_date: Optional[date] = None, 
                                 end_date: Optional[date] = None) -> Dict:
        """生成游戏分析报告"""
        try:
            # 获取游戏基本信息
            game = await self._get_game(game_id, db)
            if not game:
                return {"error": "游戏不存在"}
            
            # 设置时间范围
            if not start_date:
                start_date = game.start_time.date()
            if not end_date:
                end_date = datetime.now().date()
            
            # 生成综合报告
            report = {
                "game_info": await self._get_game_overview(game, db),
                "performance_metrics": await self._get_performance_metrics(game_id, db, start_date, end_date),
                "user_analysis": await self._get_user_analysis(game_id, db, start_date, end_date),
                "prize_analysis": await self._get_prize_analysis(game_id, db, start_date, end_date),
                "temporal_analysis": await self._get_temporal_analysis(game_id, db, start_date, end_date),
                "geographic_analysis": await self._get_geographic_analysis(game_id, db, start_date, end_date),
                "conversion_analysis": await self._get_conversion_analysis(game_id, db, start_date, end_date),
                "roi_analysis": await self._get_roi_analysis(game_id, db, start_date, end_date),
                "recommendations": await self._generate_recommendations(game_id, db),
                "report_metadata": {
                    "generated_at": datetime.now().isoformat(),
                    "date_range": {
                        "start": start_date.isoformat(),
                        "end": end_date.isoformat()
                    },
                    "version": "2.0"
                }
            }
            
            return report
            
        except Exception as e:
            logger.error(f"生成游戏报告失败: {e}")
            return {"error": str(e)}
    
    async def get_real_time_metrics(self, game_id: str, db: AsyncSession) -> Dict:
        """获取实时指标"""
        try:
            now = datetime.now()
            today = now.date()
            
            # 今日数据
            today_records = await self._get_records_by_date(game_id, db, today, today)
            today_participants = await self._get_participants_by_date(game_id, db, today, today)
            
            # 实时指标
            metrics = {
                "current_time": now.isoformat(),
                "today_metrics": {
                    "participants": len(today_participants),
                    "plays": len(today_records),
                    "winners": sum(1 for r in today_records if r.is_win),
                    "win_rate": self._calculate_win_rate(today_records),
                    "conversion_rate": self._calculate_conversion_rate(today_participants, today_records)
                },
                "hourly_data": await self._get_hourly_data(game_id, db, today),
                "recent_winners": await self._get_recent_winners(game_id, db, limit=10),
                "live_feed": await self._get_live_activity_feed(game_id, db, limit=20)
            }
            
            return metrics
            
        except Exception as e:
            logger.error(f"获取实时指标失败: {e}")
            return {"error": str(e)}
    
    async def get_comparison_analysis(self, game_ids: List[str], db: AsyncSession) -> Dict:
        """获取游戏对比分析"""
        try:
            games_data = []
            
            for game_id in game_ids:
                game = await self._get_game(game_id, db)
                if not game:
                    continue
                
                metrics = await self._get_performance_metrics(game_id, db)
                games_data.append({
                    "game_id": game_id,
                    "name": game.name,
                    "marketing_type": game.marketing_type,
                    "game_type": game.game_type,
                    "metrics": metrics
                })
            
            # 对比分析
            comparison = {
                "games_overview": games_data,
                "ranking": self._create_performance_ranking(games_data),
                "best_practices": self._identify_best_practices(games_data),
                "improvement_opportunities": self._identify_improvement_opportunities(games_data)
            }
            
            return comparison
            
        except Exception as e:
            logger.error(f"获取对比分析失败: {e}")
            return {"error": str(e)}
    
    async def get_store_performance(self, project_id: str, db: AsyncSession, 
                                  store_id: Optional[str] = None) -> Dict:
        """获取门店游戏表现分析"""
        try:
            # 查询项目下的所有游戏
            games_query = select(MarketingGame).where(MarketingGame.project_id == project_id)
            if store_id:
                # 过滤特定门店的游戏
                games_query = games_query.where(
                    or_(
                        MarketingGame.is_global == True,
                        MarketingGame.store_ids.contains([store_id])
                    )
                )
            
            result = await db.execute(games_query)
            games = result.scalars().all()
            
            store_analysis = {
                "store_overview": await self._get_store_overview(store_id, db) if store_id else None,
                "games_performance": [],
                "total_metrics": {
                    "total_games": len(games),
                    "total_participants": 0,
                    "total_plays": 0,
                    "total_winners": 0,
                    "total_cost": 0,
                    "total_revenue": 0
                },
                "trends": {},
                "recommendations": []
            }
            
            # 分析每个游戏
            for game in games:
                game_metrics = await self._get_performance_metrics(str(game.id), db)
                store_analysis["games_performance"].append({
                    "game_id": str(game.id),
                    "name": game.name,
                    "marketing_type": game.marketing_type,
                    "metrics": game_metrics
                })
                
                # 累加总指标
                store_analysis["total_metrics"]["total_participants"] += game_metrics.get("total_participants", 0)
                store_analysis["total_metrics"]["total_plays"] += game_metrics.get("total_plays", 0)
                store_analysis["total_metrics"]["total_winners"] += game_metrics.get("total_winners", 0)
                store_analysis["total_metrics"]["total_cost"] += game_metrics.get("total_cost", 0)
            
            return store_analysis
            
        except Exception as e:
            logger.error(f"获取门店表现分析失败: {e}")
            return {"error": str(e)}
    
    async def get_marketing_effectiveness(self, project_id: str, db: AsyncSession,
                                        marketing_type: Optional[str] = None) -> Dict:
        """获取营销活动效果分析"""
        try:
            # 构建查询
            query = select(MarketingGame).where(MarketingGame.project_id == project_id)
            if marketing_type:
                query = query.where(MarketingGame.marketing_type == marketing_type)
            
            result = await db.execute(query)
            games = result.scalars().all()
            
            # 按营销类型分组分析
            marketing_analysis = defaultdict(list)
            
            for game in games:
                metrics = await self._get_performance_metrics(str(game.id), db)
                marketing_analysis[game.marketing_type].append({
                    "game": game,
                    "metrics": metrics
                })
            
            # 生成效果报告
            effectiveness_report = {
                "overview": {
                    "total_campaigns": len(games),
                    "marketing_types": list(marketing_analysis.keys()),
                    "analysis_period": {
                        "start": min(g.start_time for g in games).isoformat() if games else None,
                        "end": max(g.end_time for g in games).isoformat() if games else None
                    }
                },
                "by_marketing_type": {},
                "comparative_analysis": {},
                "success_factors": [],
                "optimization_suggestions": []
            }
            
            # 分析每种营销类型
            for marketing_type, games_data in marketing_analysis.items():
                type_metrics = self._aggregate_marketing_metrics(games_data)
                effectiveness_report["by_marketing_type"][marketing_type] = type_metrics
            
            # 比较分析
            effectiveness_report["comparative_analysis"] = self._compare_marketing_types(
                effectiveness_report["by_marketing_type"]
            )
            
            # 成功因素分析
            effectiveness_report["success_factors"] = self._identify_success_factors(marketing_analysis)
            
            return effectiveness_report
            
        except Exception as e:
            logger.error(f"获取营销效果分析失败: {e}")
            return {"error": str(e)}
    
    async def predict_game_performance(self, game_id: str, db: AsyncSession,
                                     prediction_days: int = 7) -> Dict:
        """预测游戏表现"""
        try:
            # 获取历史数据
            historical_data = await self._get_historical_data(game_id, db, days=14)
            
            if len(historical_data) < 3:
                return {"error": "历史数据不足，无法进行预测"}
            
            # 简单的线性预测（实际可以使用更复杂的ML模型）
            predictions = {
                "prediction_period": prediction_days,
                "predicted_metrics": {},
                "confidence_level": "medium",
                "methodology": "linear_trend_analysis"
            }
            
            # 预测参与人数
            participation_trend = self._calculate_trend(
                [d["participants"] for d in historical_data]
            )
            predictions["predicted_metrics"]["daily_participants"] = self._predict_values(
                participation_trend, prediction_days
            )
            
            # 预测游戏次数
            plays_trend = self._calculate_trend(
                [d["plays"] for d in historical_data]
            )
            predictions["predicted_metrics"]["daily_plays"] = self._predict_values(
                plays_trend, prediction_days
            )
            
            # 预测中奖数
            wins_trend = self._calculate_trend(
                [d["winners"] for d in historical_data]
            )
            predictions["predicted_metrics"]["daily_winners"] = self._predict_values(
                wins_trend, prediction_days
            )
            
            # 预测总指标
            predictions["predicted_totals"] = {
                "total_participants": sum(predictions["predicted_metrics"]["daily_participants"]),
                "total_plays": sum(predictions["predicted_metrics"]["daily_plays"]),
                "total_winners": sum(predictions["predicted_metrics"]["daily_winners"])
            }
            
            return predictions
            
        except Exception as e:
            logger.error(f"预测游戏表现失败: {e}")
            return {"error": str(e)}
    
    async def get_user_journey_analysis(self, game_id: str, db: AsyncSession) -> Dict:
        """获取用户旅程分析"""
        try:
            # 获取所有参与者的详细数据
            participants_query = select(GameParticipant).where(
                GameParticipant.game_id == game_id
            )
            result = await db.execute(participants_query)
            participants = result.scalars().all()
            
            # 获取所有游戏记录
            records_query = select(GameRecord).where(
                GameRecord.game_id == game_id
            ).order_by(GameRecord.created_at)
            result = await db.execute(records_query)
            records = result.scalars().all()
            
            # 用户旅程分析
            journey_analysis = {
                "acquisition": await self._analyze_user_acquisition(participants),
                "engagement": await self._analyze_user_engagement(participants, records),
                "retention": await self._analyze_user_retention(participants),
                "conversion": await self._analyze_user_conversion(participants, records),
                "churn": await self._analyze_user_churn(participants, records),
                "lifecycle_stages": await self._analyze_lifecycle_stages(participants, records),
                "behavioral_segments": await self._segment_users_by_behavior(participants, records)
            }
            
            return journey_analysis
            
        except Exception as e:
            logger.error(f"获取用户旅程分析失败: {e}")
            return {"error": str(e)}
    
    async def _get_game(self, game_id: str, db: AsyncSession) -> Optional[MarketingGame]:
        """获取游戏信息"""
        query = select(MarketingGame).where(MarketingGame.id == game_id)
        result = await db.execute(query)
        return result.scalar_one_or_none()
    
    async def _get_game_overview(self, game: MarketingGame, db: AsyncSession) -> Dict:
        """获取游戏概览"""
        return {
            "id": str(game.id),
            "name": game.name,
            "description": game.description,
            "game_type": game.game_type,
            "marketing_type": game.marketing_type,
            "status": game.status,
            "start_time": game.start_time.isoformat(),
            "end_time": game.end_time.isoformat(),
            "max_draws_per_user": game.max_draws_per_user,
            "is_global": game.is_global,
            "store_ids": game.store_ids or []
        }
    
    async def _get_performance_metrics(self, game_id: str, db: AsyncSession,
                                     start_date: Optional[date] = None,
                                     end_date: Optional[date] = None) -> Dict:
        """获取核心表现指标"""
        try:
            # 查询游戏记录
            records_query = select(GameRecord).where(GameRecord.game_id == game_id)
            if start_date and end_date:
                records_query = records_query.where(
                    and_(
                        GameRecord.created_at >= start_date,
                        GameRecord.created_at <= end_date + timedelta(days=1)
                    )
                )
            
            result = await db.execute(records_query)
            records = result.scalars().all()
            
            # 查询参与者
            participants_query = select(GameParticipant).where(GameParticipant.game_id == game_id)
            if start_date and end_date:
                participants_query = participants_query.where(
                    and_(
                        GameParticipant.created_at >= start_date,
                        GameParticipant.created_at <= end_date + timedelta(days=1)
                    )
                )
            
            result = await db.execute(participants_query)
            participants = result.scalars().all()
            
            # 计算核心指标
            total_records = len(records)
            total_participants = len(participants)
            winners = [r for r in records if r.is_win]
            total_winners = len(winners)
            
            metrics = {
                "total_participants": total_participants,
                "total_plays": total_records,
                "total_winners": total_winners,
                "win_rate": (total_winners / total_records * 100) if total_records > 0 else 0,
                "participation_efficiency": (total_records / total_participants) if total_participants > 0 else 0,
                "average_session_duration": self._calculate_avg_session_duration(records),
                "bounce_rate": self._calculate_bounce_rate(participants),
                "return_rate": self._calculate_return_rate(participants),
                "share_rate": self._calculate_share_rate(participants),
                "conversion_rate": self._calculate_conversion_rate(participants, records),
                "total_cost": sum(r.prize_value for r in winners),
                "cost_per_participant": 0,
                "cost_per_play": 0
            }
            
            # 计算成本效率
            if metrics["total_cost"] > 0:
                metrics["cost_per_participant"] = metrics["total_cost"] / total_participants if total_participants > 0 else 0
                metrics["cost_per_play"] = metrics["total_cost"] / total_records if total_records > 0 else 0
            
            return metrics
            
        except Exception as e:
            logger.error(f"获取表现指标失败: {e}")
            return {}
    
    def _calculate_win_rate(self, records: List[GameRecord]) -> float:
        """计算中奖率"""
        if not records:
            return 0
        winners = sum(1 for r in records if r.is_win)
        return (winners / len(records)) * 100
    
    def _calculate_conversion_rate(self, participants: List[GameParticipant], 
                                 records: List[GameRecord]) -> float:
        """计算转化率"""
        if not participants:
            return 0
        # 简化计算：认为完成游戏的用户为转化用户
        converted_users = len(set(r.participant_id for r in records))
        return (converted_users / len(participants)) * 100
    
    def _calculate_avg_session_duration(self, records: List[GameRecord]) -> float:
        """计算平均会话时长"""
        if not records:
            return 0
        durations = [r.game_duration for r in records if r.game_duration > 0]
        return sum(durations) / len(durations) if durations else 0
    
    def _calculate_bounce_rate(self, participants: List[GameParticipant]) -> float:
        """计算跳出率"""
        if not participants:
            return 0
        # 简化：只参与一次且时长很短的用户认为是跳出用户
        bounce_users = sum(1 for p in participants if p.draw_count <= 1 and p.visit_duration < 30)
        return (bounce_users / len(participants)) * 100
    
    def _calculate_return_rate(self, participants: List[GameParticipant]) -> float:
        """计算回访率"""
        if not participants:
            return 0
        return_users = sum(1 for p in participants if p.participation_count > 1)
        return (return_users / len(participants)) * 100
    
    def _calculate_share_rate(self, participants: List[GameParticipant]) -> float:
        """计算分享率"""
        if not participants:
            return 0
        sharing_users = sum(1 for p in participants if p.share_count > 0)
        return (sharing_users / len(participants)) * 100
    
    async def _get_user_analysis(self, game_id: str, db: AsyncSession,
                               start_date: date, end_date: date) -> Dict:
        """获取用户分析"""
        # 获取参与者数据
        participants_query = select(GameParticipant).where(
            and_(
                GameParticipant.game_id == game_id,
                GameParticipant.created_at >= start_date,
                GameParticipant.created_at <= end_date + timedelta(days=1)
            )
        )
        result = await db.execute(participants_query)
        participants = result.scalars().all()
        
        user_analysis = {
            "demographics": self._analyze_user_demographics(participants),
            "behavior_patterns": self._analyze_behavior_patterns(participants),
            "engagement_levels": self._categorize_engagement_levels(participants),
            "geographic_distribution": self._analyze_geographic_distribution(participants),
            "device_analysis": self._analyze_device_usage(participants),
            "acquisition_channels": self._analyze_acquisition_channels(participants)
        }
        
        return user_analysis
    
    def _analyze_user_demographics(self, participants: List[GameParticipant]) -> Dict:
        """分析用户人口统计学特征"""
        return {
            "total_users": len(participants),
            "verified_users": sum(1 for p in participants if p.is_verified),
            "new_users": sum(1 for p in participants if p.participation_count == 1),
            "returning_users": sum(1 for p in participants if p.participation_count > 1),
            "average_participation": sum(p.participation_count for p in participants) / len(participants) if participants else 0
        }
    
    def _analyze_behavior_patterns(self, participants: List[GameParticipant]) -> Dict:
        """分析用户行为模式"""
        if not participants:
            return {}
        
        return {
            "average_draws": sum(p.draw_count for p in participants) / len(participants),
            "average_wins": sum(p.win_count for p in participants) / len(participants),
            "average_visit_duration": sum(p.visit_duration for p in participants) / len(participants),
            "average_page_views": sum(p.page_views for p in participants) / len(participants),
            "high_engagement_users": sum(1 for p in participants if p.interaction_count > 10),
            "social_sharers": sum(1 for p in participants if p.share_count > 0)
        }
    
    # 更多分析方法...
    async def _get_temporal_analysis(self, game_id: str, db: AsyncSession,
                                   start_date: date, end_date: date) -> Dict:
        """获取时间分析"""
        records_query = select(GameRecord).where(
            and_(
                GameRecord.game_id == game_id,
                GameRecord.created_at >= start_date,
                GameRecord.created_at <= end_date + timedelta(days=1)
            )
        ).order_by(GameRecord.created_at)
        
        result = await db.execute(records_query)
        records = result.scalars().all()
        
        # 按小时、天、周分析
        hourly_data = defaultdict(int)
        daily_data = defaultdict(int)
        weekly_data = defaultdict(int)
        
        for record in records:
            hour = record.created_at.hour
            day = record.created_at.date()
            week = record.created_at.isocalendar()[1]
            
            hourly_data[hour] += 1
            daily_data[day.isoformat()] += 1
            weekly_data[f"Week {week}"] += 1
        
        return {
            "hourly_distribution": dict(hourly_data),
            "daily_distribution": dict(daily_data),
            "weekly_distribution": dict(weekly_data),
            "peak_hours": sorted(hourly_data.items(), key=lambda x: x[1], reverse=True)[:3],
            "peak_days": sorted(daily_data.items(), key=lambda x: x[1], reverse=True)[:5]
        }
    
    # 省略其他分析方法的具体实现... 