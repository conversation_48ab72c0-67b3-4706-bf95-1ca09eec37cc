/**
 * 存储管理页面
 * 系统管理员用于管理存储配额和查看存储使用情况
 */

import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Table, 
  Button, 
  Space, 
  Progress, 
  Typography, 
  Tabs, 
  Statistic, 
  Row, 
  Col, 
  Modal, 
  Form, 
  InputNumber, 
  Select, 
  message,
  Popconfirm,
  Input,
  Divider
} from 'antd';
import { 
  ReloadOutlined, 
  EditOutlined, 
  DeleteOutlined, 
  CloudUploadOutlined, 
  CloudDownloadOutlined,
  PieChartOutlined,
  UserOutlined,
  TeamOutlined,
  AppstoreOutlined
} from '@ant-design/icons';
import apiService from '../../services/api';

const { Title, Paragraph, Text } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;

const StorageManagement = () => {
  const [loading, setLoading] = useState(false);
  const [storageData, setStorageData] = useState({
    total_quota: 0,
    total_used: 0,
    tenant_quotas: [],
    user_quotas: [],
    file_types: [],
  });
  const [editModalVisible, setEditModalVisible] = useState(false);
  const [editingQuota, setEditingQuota] = useState(null);
  const [quotaForm] = Form.useForm();

  // 加载存储数据
  const fetchStorageData = async () => {
    try {
      setLoading(true);
      const data = await apiService.getStorageData();
      setStorageData(data);
    } catch (error) {
      message.error('获取存储数据失败');
      console.error('获取存储数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 初始化数据
  useEffect(() => {
    fetchStorageData();
  }, []);

  // 打开编辑配额模态框
  const showEditQuotaModal = (quota, type) => {
    setEditingQuota({ ...quota, type });
    quotaForm.setFieldsValue({
      quota_mb: Math.round(quota.quota_bytes / (1024 * 1024)),
      can_exceed_quota: quota.can_exceed_quota,
    });
    setEditModalVisible(true);
  };

  // 关闭模态框
  const handleCancel = () => {
    setEditModalVisible(false);
  };

  // 提交配额表单
  const handleQuotaSubmit = async () => {
    try {
      const values = await quotaForm.validateFields();
      const quotaBytes = values.quota_mb * 1024 * 1024;
      
      setLoading(true);
      if (editingQuota.type === 'tenant') {
        await apiService.updateTenantQuota(editingQuota.id, {
          quota_bytes: quotaBytes,
          can_exceed_quota: values.can_exceed_quota,
        });
      } else if (editingQuota.type === 'user') {
        await apiService.updateUserQuota(editingQuota.id, {
          quota_bytes: quotaBytes,
          can_exceed_quota: values.can_exceed_quota,
        });
      }
      
      message.success('配额更新成功');
      setEditModalVisible(false);
      fetchStorageData();
    } catch (error) {
      console.error('更新配额失败:', error);
      message.error('更新配额失败: ' + (error.response?.data?.message || error.message));
    } finally {
      setLoading(false);
    }
  };

  // 删除文件
  const handleDeleteFile = async (fileId) => {
    try {
      setLoading(true);
      await apiService.deleteFile(fileId);
      message.success('文件删除成功');
      fetchStorageData();
    } catch (error) {
      console.error('删除文件失败:', error);
      message.error('删除文件失败: ' + (error.response?.data?.message || error.message));
    } finally {
      setLoading(false);
    }
  };

  // 清理过期文件
  const handleCleanupExpiredFiles = async () => {
    try {
      setLoading(true);
      const result = await apiService.cleanupExpiredFiles();
      message.success(`清理成功，共删除 ${result.deleted_count} 个过期文件，释放 ${(result.freed_bytes / (1024 * 1024)).toFixed(2)} MB 空间`);
      fetchStorageData();
    } catch (error) {
      console.error('清理过期文件失败:', error);
      message.error('清理过期文件失败: ' + (error.response?.data?.message || error.message));
    } finally {
      setLoading(false);
    }
  };

  // 租户配额表格列
  const tenantQuotaColumns = [
    {
      title: '租户名称',
      dataIndex: 'tenant_name',
      key: 'tenant_name',
      sorter: (a, b) => a.tenant_name.localeCompare(b.tenant_name),
    },
    {
      title: '配额',
      dataIndex: 'quota_bytes',
      key: 'quota_bytes',
      render: (bytes) => `${(bytes / (1024 * 1024)).toFixed(2)} MB`,
      sorter: (a, b) => a.quota_bytes - b.quota_bytes,
    },
    {
      title: '已使用',
      dataIndex: 'used_bytes',
      key: 'used_bytes',
      render: (bytes) => `${(bytes / (1024 * 1024)).toFixed(2)} MB`,
      sorter: (a, b) => a.used_bytes - b.used_bytes,
    },
    {
      title: '使用率',
      key: 'usage_percent',
      render: (_, record) => {
        const percent = record.quota_bytes > 0 
          ? Math.round((record.used_bytes / record.quota_bytes) * 100) 
          : 0;
        
        let status = 'normal';
        if (percent >= 90) status = 'exception';
        else if (percent >= 70) status = 'warning';
        
        return <Progress percent={percent} status={status} />;
      },
      sorter: (a, b) => (a.used_bytes / a.quota_bytes) - (b.used_bytes / b.quota_bytes),
    },
    {
      title: '允许超额',
      dataIndex: 'can_exceed_quota',
      key: 'can_exceed_quota',
      render: (canExceed) => canExceed ? '是' : '否',
      filters: [
        { text: '是', value: true },
        { text: '否', value: false },
      ],
      onFilter: (value, record) => record.can_exceed_quota === value,
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Button 
          icon={<EditOutlined />} 
          onClick={() => showEditQuotaModal(record, 'tenant')}
          size="small"
        />
      ),
    },
  ];

  // 用户配额表格列
  const userQuotaColumns = [
    {
      title: '用户名',
      dataIndex: 'username',
      key: 'username',
      sorter: (a, b) => a.username.localeCompare(b.username),
    },
    {
      title: '姓名',
      dataIndex: 'name',
      key: 'name',
      sorter: (a, b) => a.name.localeCompare(b.name),
    },
    {
      title: '租户',
      dataIndex: 'tenant_name',
      key: 'tenant_name',
    },
    {
      title: '配额',
      dataIndex: 'quota_bytes',
      key: 'quota_bytes',
      render: (bytes) => `${(bytes / (1024 * 1024)).toFixed(2)} MB`,
      sorter: (a, b) => a.quota_bytes - b.quota_bytes,
    },
    {
      title: '已使用',
      dataIndex: 'used_bytes',
      key: 'used_bytes',
      render: (bytes) => `${(bytes / (1024 * 1024)).toFixed(2)} MB`,
      sorter: (a, b) => a.used_bytes - b.used_bytes,
    },
    {
      title: '使用率',
      key: 'usage_percent',
      render: (_, record) => {
        const percent = record.quota_bytes > 0 
          ? Math.round((record.used_bytes / record.quota_bytes) * 100) 
          : 0;
        
        let status = 'normal';
        if (percent >= 90) status = 'exception';
        else if (percent >= 70) status = 'warning';
        
        return <Progress percent={percent} status={status} />;
      },
      sorter: (a, b) => (a.used_bytes / a.quota_bytes) - (b.used_bytes / b.quota_bytes),
    },
    {
      title: '允许超额',
      dataIndex: 'can_exceed_quota',
      key: 'can_exceed_quota',
      render: (canExceed) => canExceed ? '是' : '否',
      filters: [
        { text: '是', value: true },
        { text: '否', value: false },
      ],
      onFilter: (value, record) => record.can_exceed_quota === value,
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Button 
          icon={<EditOutlined />} 
          onClick={() => showEditQuotaModal(record, 'user')}
          size="small"
        />
      ),
    },
  ];

  // 文件列表表格列
  const fileColumns = [
    {
      title: '文件名',
      dataIndex: 'name',
      key: 'name',
      sorter: (a, b) => a.name.localeCompare(b.name),
    },
    {
      title: '上传者',
      dataIndex: 'uploader_name',
      key: 'uploader_name',
    },
    {
      title: '租户',
      dataIndex: 'tenant_name',
      key: 'tenant_name',
    },
    {
      title: '大小',
      dataIndex: 'size_bytes',
      key: 'size_bytes',
      render: (bytes) => `${(bytes / (1024 * 1024)).toFixed(2)} MB`,
      sorter: (a, b) => a.size_bytes - b.size_bytes,
    },
    {
      title: '类型',
      dataIndex: 'mime_type',
      key: 'mime_type',
      filters: [
        { text: '图片', value: 'image' },
        { text: '文档', value: 'document' },
        { text: '视频', value: 'video' },
        { text: '音频', value: 'audio' },
        { text: '压缩包', value: 'archive' },
        { text: '其他', value: 'other' },
      ],
      onFilter: (value, record) => record.mime_type.includes(value),
    },
    {
      title: '上传时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (date) => new Date(date).toLocaleString(),
      sorter: (a, b) => new Date(a.created_at) - new Date(b.created_at),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="small">
          <Button 
            icon={<CloudDownloadOutlined />} 
            size="small"
            onClick={() => window.open(record.download_url, '_blank')}
          />
          <Popconfirm
            title="确定要删除此文件吗？"
            onConfirm={() => handleDeleteFile(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button 
              icon={<DeleteOutlined />} 
              danger 
              size="small"
            />
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 计算总体使用率
  const totalUsagePercent = storageData.total_quota > 0 
    ? Math.round((storageData.total_used / storageData.total_quota) * 100) 
    : 0;

  // 设置总体使用率的状态
  let totalUsageStatus = 'normal';
  if (totalUsagePercent >= 90) totalUsageStatus = 'exception';
  else if (totalUsagePercent >= 70) totalUsageStatus = 'warning';

  return (
    <div>
      <Title level={2}>存储管理</Title>
      <Paragraph>
        管理系统存储配额和查看存储使用情况。您可以为租户和用户分配存储配额，查看文件使用情况，以及清理过期文件。
      </Paragraph>
      
      <Card loading={loading} className="dashboard-card">
        <Row gutter={24}>
          <Col span={8}>
            <Statistic 
              title="总存储配额" 
              value={(storageData.total_quota / (1024 * 1024 * 1024)).toFixed(2)} 
              suffix="GB" 
              prefix={<CloudUploadOutlined />} 
            />
          </Col>
          <Col span={8}>
            <Statistic 
              title="已使用存储" 
              value={(storageData.total_used / (1024 * 1024 * 1024)).toFixed(2)} 
              suffix="GB" 
              prefix={<CloudDownloadOutlined />} 
            />
          </Col>
          <Col span={8}>
            <Statistic 
              title="文件总数" 
              value={storageData.file_count || 0} 
              prefix={<AppstoreOutlined />} 
            />
          </Col>
        </Row>
        
        <Divider />
        
        <div style={{ marginBottom: 16 }}>
          <Text strong>总体存储使用率：</Text>
          <Progress percent={totalUsagePercent} status={totalUsageStatus} />
        </div>
        
        <div style={{ textAlign: 'right', marginBottom: 16 }}>
          <Space>
            <Button 
              icon={<ReloadOutlined />} 
              onClick={fetchStorageData}
              loading={loading}
            >
              刷新数据
            </Button>
            <Popconfirm
              title="确定要清理所有过期文件吗？"
              onConfirm={handleCleanupExpiredFiles}
              okText="确定"
              cancelText="取消"
            >
              <Button type="primary">
                清理过期文件
              </Button>
            </Popconfirm>
          </Space>
        </div>
      </Card>
      
      <Tabs defaultActiveKey="tenant_quotas">
        <TabPane 
          tab={<span><TeamOutlined /> 租户配额</span>} 
          key="tenant_quotas"
        >
          <Card loading={loading}>
            <Table 
              columns={tenantQuotaColumns} 
              dataSource={storageData.tenant_quotas} 
              rowKey="id"
              pagination={{ pageSize: 10 }}
            />
          </Card>
        </TabPane>
        
        <TabPane 
          tab={<span><UserOutlined /> 用户配额</span>} 
          key="user_quotas"
        >
          <Card loading={loading}>
            <Table 
              columns={userQuotaColumns} 
              dataSource={storageData.user_quotas} 
              rowKey="id"
              pagination={{ pageSize: 10 }}
            />
          </Card>
        </TabPane>
        
        <TabPane 
          tab={<span><AppstoreOutlined /> 文件列表</span>} 
          key="files"
        >
          <Card loading={loading}>
            <div style={{ marginBottom: 16 }}>
              <Input.Search 
                placeholder="搜索文件名" 
                style={{ width: 300, marginRight: 16 }} 
                onSearch={(value) => console.log(value)} 
              />
              <Select 
                placeholder="按租户筛选" 
                style={{ width: 200 }} 
                allowClear
                onChange={(value) => console.log(value)}
              >
                {storageData.tenant_quotas.map(tenant => (
                  <Option key={tenant.id} value={tenant.id}>{tenant.tenant_name}</Option>
                ))}
              </Select>
            </div>
            
            <Table 
              columns={fileColumns} 
              dataSource={storageData.files || []} 
              rowKey="id"
              pagination={{ pageSize: 10 }}
            />
          </Card>
        </TabPane>
        
        <TabPane 
          tab={<span><PieChartOutlined /> 存储分析</span>} 
          key="analysis"
        >
          <Card loading={loading}>
            <Row gutter={24}>
              <Col span={12}>
                <Card title="按文件类型分布" bordered={false}>
                  <div className="chart-container">
                    {/* 这里可以放置文件类型分布图表 */}
                    <div style={{ textAlign: 'center', padding: '100px 0' }}>
                      文件类型分布图表
                    </div>
                  </div>
                </Card>
              </Col>
              <Col span={12}>
                <Card title="按租户存储分布" bordered={false}>
                  <div className="chart-container">
                    {/* 这里可以放置租户存储分布图表 */}
                    <div style={{ textAlign: 'center', padding: '100px 0' }}>
                      租户存储分布图表
                    </div>
                  </div>
                </Card>
              </Col>
            </Row>
            
            <Row gutter={24} style={{ marginTop: 24 }}>
              <Col span={24}>
                <Card title="存储使用趋势" bordered={false}>
                  <div className="chart-container">
                    {/* 这里可以放置存储使用趋势图表 */}
                    <div style={{ textAlign: 'center', padding: '100px 0' }}>
                      存储使用趋势图表
                    </div>
                  </div>
                </Card>
              </Col>
            </Row>
          </Card>
        </TabPane>
      </Tabs>
      
      <Modal
        title={`编辑${editingQuota?.type === 'tenant' ? '租户' : '用户'}配额`}
        open={editModalVisible}
        onOk={handleQuotaSubmit}
        onCancel={handleCancel}
        confirmLoading={loading}
      >
        {editingQuota && (
          <Form
            form={quotaForm}
            layout="vertical"
          >
            <Form.Item
              label={editingQuota.type === 'tenant' ? '租户名称' : '用户名'}
            >
              <Input 
                value={editingQuota.type === 'tenant' ? editingQuota.tenant_name : editingQuota.username} 
                disabled 
              />
            </Form.Item>
            
            <Form.Item
              name="quota_mb"
              label="配额 (MB)"
              rules={[
                { required: true, message: '请输入配额大小' },
                { type: 'number', min: 1, message: '配额必须大于0' }
              ]}
            >
              <InputNumber style={{ width: '100%' }} />
            </Form.Item>
            
            <Form.Item
              name="can_exceed_quota"
              label="允许超额"
              valuePropName="checked"
            >
              <Select>
                <Option value={true}>是</Option>
                <Option value={false}>否</Option>
              </Select>
            </Form.Item>
            
            <div>
              <Text type="secondary">
                当前使用: {((editingQuota.used_bytes || 0) / (1024 * 1024)).toFixed(2)} MB
              </Text>
            </div>
          </Form>
        )}
      </Modal>
    </div>
  );
};

export default StorageManagement;
