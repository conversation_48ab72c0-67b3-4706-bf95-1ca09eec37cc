from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import and_, or_, func
import uuid
from datetime import datetime

from db.database import get_db
from models.warehouse import Warehouse, WarehouseInbound, WarehouseInboundItem
from models.purchase_order import PurchaseOrder, PurchaseOrderItem
from models.product import Product
from models.user import User
from schemas.warehouse import (
    WarehouseInboundCreate,
    WarehouseInboundUpdate,
    WarehouseInboundResponse,
    WarehouseInboundListResponse
)
from api.deps import get_current_project_id, get_current_tenant_id, get_current_user

router = APIRouter()

@router.get("/{warehouse_id}/inbounds", response_model=WarehouseInboundListResponse)
async def get_inbounds(
    warehouse_id: uuid.UUID,
    skip: int = 0,
    limit: int = 10,
    status: Optional[str] = None,
    inbound_number: Optional[str] = None,
    purchase_order_number: Optional[str] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    project_id: uuid.UUID = Depends(get_current_project_id),
    tenant_id: Optional[uuid.UUID] = Depends(get_current_tenant_id),
    db: AsyncSession = Depends(get_db)
):
    """
    获取入库单列表
    """
    # 验证仓库是否存在
    warehouse = await db.get(Warehouse, warehouse_id)
    if not warehouse or warehouse.project_id != project_id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="仓库不存在或不属于当前项目"
        )
    
    # 构建查询条件
    conditions = [
        WarehouseInbound.project_id == project_id,
        WarehouseInbound.warehouse_id == warehouse_id
    ]
    
    if tenant_id:
        conditions.append(WarehouseInbound.tenant_id == tenant_id)
    
    if status:
        conditions.append(WarehouseInbound.status == status)
    
    if inbound_number:
        conditions.append(WarehouseInbound.inbound_number.ilike(f"%{inbound_number}%"))
    
    if purchase_order_number:
        conditions.append(WarehouseInbound.purchase_order_number.ilike(f"%{purchase_order_number}%"))
    
    if start_date:
        conditions.append(WarehouseInbound.inbound_date >= start_date)
    
    if end_date:
        conditions.append(WarehouseInbound.inbound_date <= end_date)
    
    # 查询总数
    count_query = select(func.count()).select_from(WarehouseInbound).where(and_(*conditions))
    total = await db.scalar(count_query)
    
    # 查询数据
    query = (
        select(WarehouseInbound)
        .where(and_(*conditions))
        .order_by(WarehouseInbound.created_at.desc())
        .offset(skip)
        .limit(limit)
    )
    
    result = await db.execute(query)
    inbounds = result.scalars().all()
    
    return {
        "success": True,
        "message": "获取入库单列表成功",
        "total": total,
        "items": inbounds
    }

@router.post("/{warehouse_id}/inbounds", response_model=WarehouseInboundResponse)
async def create_inbound(
    warehouse_id: uuid.UUID,
    inbound: WarehouseInboundCreate,
    project_id: uuid.UUID = Depends(get_current_project_id),
    tenant_id: Optional[uuid.UUID] = Depends(get_current_tenant_id),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    创建入库单
    """
    # 验证仓库是否存在
    warehouse = await db.get(Warehouse, warehouse_id)
    if not warehouse or warehouse.project_id != project_id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="仓库不存在或不属于当前项目"
        )
    
    # 验证采购单（如果有）
    purchase_order = None
    if inbound.purchase_order_id:
        purchase_order = await db.get(PurchaseOrder, inbound.purchase_order_id)
        if not purchase_order or purchase_order.project_id != project_id:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="采购单不存在或不属于当前项目"
            )
    
    # 创建入库单
    db_inbound = WarehouseInbound(
        tenant_id=tenant_id,
        project_id=project_id,
        warehouse_id=warehouse_id,
        inbound_number=inbound.inbound_number,
        purchase_order_id=inbound.purchase_order_id,
        purchase_order_number=purchase_order.order_number if purchase_order else None,
        inbound_date=inbound.inbound_date,
        status=inbound.status,
        notes=inbound.notes,
        created_by=current_user.id,
        updated_by=current_user.id
    )
    
    db.add(db_inbound)
    await db.flush()
    
    # 创建入库单项
    total_amount = 0.0
    for item in inbound.items:
        db_item = WarehouseInboundItem(
            tenant_id=tenant_id,
            project_id=project_id,
            inbound_id=db_inbound.id,
            purchase_order_item_id=item.purchase_order_item_id,
            product_id=item.product_id,
            product_name=item.product_name,
            product_code=item.product_code,
            product_specification=item.product_specification,
            product_unit=item.product_unit,
            quantity=item.quantity,
            unit_price=item.unit_price,
            total_amount=item.quantity * item.unit_price
        )
        
        db.add(db_item)
        total_amount += item.quantity * item.unit_price
    
    # 更新入库单总金额和商品数量
    db_inbound.total_amount = total_amount
    db_inbound.item_count = len(inbound.items)
    
    await db.commit()
    await db.refresh(db_inbound)
    
    return {
        "success": True,
        "message": "创建入库单成功",
        "data": db_inbound
    }

@router.get("/{warehouse_id}/inbounds/{inbound_id}", response_model=WarehouseInboundResponse)
async def get_inbound(
    warehouse_id: uuid.UUID,
    inbound_id: uuid.UUID,
    project_id: uuid.UUID = Depends(get_current_project_id),
    tenant_id: Optional[uuid.UUID] = Depends(get_current_tenant_id),
    db: AsyncSession = Depends(get_db)
):
    """
    获取入库单详情
    """
    # 验证仓库是否存在
    warehouse = await db.get(Warehouse, warehouse_id)
    if not warehouse or warehouse.project_id != project_id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="仓库不存在或不属于当前项目"
        )
    
    # 获取入库单
    inbound = await db.get(WarehouseInbound, inbound_id)
    if not inbound or inbound.project_id != project_id or inbound.warehouse_id != warehouse_id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="入库单不存在或不属于当前项目/仓库"
        )
    
    # 获取入库单项
    query = select(WarehouseInboundItem).where(WarehouseInboundItem.inbound_id == inbound_id)
    result = await db.execute(query)
    items = result.scalars().all()
    
    # 将入库单项添加到入库单对象
    inbound.items = items
    
    return {
        "success": True,
        "message": "获取入库单详情成功",
        "data": inbound
    }

@router.put("/{warehouse_id}/inbounds/{inbound_id}", response_model=WarehouseInboundResponse)
async def update_inbound(
    warehouse_id: uuid.UUID,
    inbound_id: uuid.UUID,
    inbound_update: WarehouseInboundUpdate,
    project_id: uuid.UUID = Depends(get_current_project_id),
    tenant_id: Optional[uuid.UUID] = Depends(get_current_tenant_id),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    更新入库单
    """
    # 验证仓库是否存在
    warehouse = await db.get(Warehouse, warehouse_id)
    if not warehouse or warehouse.project_id != project_id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="仓库不存在或不属于当前项目"
        )
    
    # 获取入库单
    db_inbound = await db.get(WarehouseInbound, inbound_id)
    if not db_inbound or db_inbound.project_id != project_id or db_inbound.warehouse_id != warehouse_id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="入库单不存在或不属于当前项目/仓库"
        )
    
    # 只有待入库状态的入库单可以更新
    if db_inbound.status != "pending":
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="只有待入库状态的入库单可以更新"
        )
    
    # 更新入库单基本信息
    db_inbound.inbound_date = inbound_update.inbound_date
    db_inbound.status = inbound_update.status
    db_inbound.notes = inbound_update.notes
    db_inbound.updated_by = current_user.id
    db_inbound.updated_at = datetime.now()
    
    # 删除原有入库单项
    query = select(WarehouseInboundItem).where(WarehouseInboundItem.inbound_id == inbound_id)
    result = await db.execute(query)
    old_items = result.scalars().all()
    
    for item in old_items:
        await db.delete(item)
    
    # 创建新的入库单项
    total_amount = 0.0
    for item in inbound_update.items:
        db_item = WarehouseInboundItem(
            tenant_id=tenant_id,
            project_id=project_id,
            inbound_id=db_inbound.id,
            purchase_order_item_id=item.purchase_order_item_id,
            product_id=item.product_id,
            product_name=item.product_name,
            product_code=item.product_code,
            product_specification=item.product_specification,
            product_unit=item.product_unit,
            quantity=item.quantity,
            unit_price=item.unit_price,
            total_amount=item.quantity * item.unit_price
        )
        
        db.add(db_item)
        total_amount += item.quantity * item.unit_price
    
    # 更新入库单总金额和商品数量
    db_inbound.total_amount = total_amount
    db_inbound.item_count = len(inbound_update.items)
    
    await db.commit()
    await db.refresh(db_inbound)
    
    # 获取更新后的入库单项
    query = select(WarehouseInboundItem).where(WarehouseInboundItem.inbound_id == inbound_id)
    result = await db.execute(query)
    items = result.scalars().all()
    
    # 将入库单项添加到入库单对象
    db_inbound.items = items
    
    return {
        "success": True,
        "message": "更新入库单成功",
        "data": db_inbound
    }

@router.delete("/{warehouse_id}/inbounds/{inbound_id}", response_model=WarehouseInboundResponse)
async def delete_inbound(
    warehouse_id: uuid.UUID,
    inbound_id: uuid.UUID,
    project_id: uuid.UUID = Depends(get_current_project_id),
    db: AsyncSession = Depends(get_db)
):
    """
    删除入库单
    """
    # 验证仓库是否存在
    warehouse = await db.get(Warehouse, warehouse_id)
    if not warehouse or warehouse.project_id != project_id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="仓库不存在或不属于当前项目"
        )
    
    # 获取入库单
    db_inbound = await db.get(WarehouseInbound, inbound_id)
    if not db_inbound or db_inbound.project_id != project_id or db_inbound.warehouse_id != warehouse_id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="入库单不存在或不属于当前项目/仓库"
        )
    
    # 只有待入库状态的入库单可以删除
    if db_inbound.status != "pending":
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="只有待入库状态的入库单可以删除"
        )
    
    # 删除入库单项
    query = select(WarehouseInboundItem).where(WarehouseInboundItem.inbound_id == inbound_id)
    result = await db.execute(query)
    items = result.scalars().all()
    
    for item in items:
        await db.delete(item)
    
    # 删除入库单
    await db.delete(db_inbound)
    await db.commit()
    
    return {
        "success": True,
        "message": "删除入库单成功",
        "data": None
    }

@router.post("/{warehouse_id}/inbounds/{inbound_id}/confirm", response_model=WarehouseInboundResponse)
async def confirm_inbound(
    warehouse_id: uuid.UUID,
    inbound_id: uuid.UUID,
    project_id: uuid.UUID = Depends(get_current_project_id),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    确认入库单
    """
    # 验证仓库是否存在
    warehouse = await db.get(Warehouse, warehouse_id)
    if not warehouse or warehouse.project_id != project_id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="仓库不存在或不属于当前项目"
        )
    
    # 获取入库单
    db_inbound = await db.get(WarehouseInbound, inbound_id)
    if not db_inbound or db_inbound.project_id != project_id or db_inbound.warehouse_id != warehouse_id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="入库单不存在或不属于当前项目/仓库"
        )
    
    # 只有待入库状态的入库单可以确认
    if db_inbound.status != "pending":
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="只有待入库状态的入库单可以确认"
        )
    
    # 获取入库单项
    query = select(WarehouseInboundItem).where(WarehouseInboundItem.inbound_id == inbound_id)
    result = await db.execute(query)
    items = result.scalars().all()
    
    # 更新采购单项的已接收数量
    for item in items:
        if item.purchase_order_item_id:
            po_item = await db.get(PurchaseOrderItem, item.purchase_order_item_id)
            if po_item:
                po_item.received_quantity = (po_item.received_quantity or 0) + item.quantity
                
                # 如果采购单项已全部接收，更新状态
                if po_item.received_quantity >= po_item.quantity:
                    po_item.status = "completed"
    
    # 更新入库单状态
    db_inbound.status = "confirmed"
    db_inbound.updated_by = current_user.id
    db_inbound.updated_at = datetime.now()
    
    # 如果有关联采购单，检查是否所有采购单项都已完成
    if db_inbound.purchase_order_id:
        purchase_order = await db.get(PurchaseOrder, db_inbound.purchase_order_id)
        if purchase_order:
            # 获取采购单所有项
            po_items_query = select(PurchaseOrderItem).where(PurchaseOrderItem.purchase_order_id == purchase_order.id)
            po_items_result = await db.execute(po_items_query)
            po_items = po_items_result.scalars().all()
            
            # 检查是否所有项都已完成
            all_completed = all(item.status == "completed" for item in po_items)
            
            if all_completed:
                purchase_order.status = "completed"
    
    await db.commit()
    await db.refresh(db_inbound)
    
    # 将入库单项添加到入库单对象
    db_inbound.items = items
    
    return {
        "success": True,
        "message": "确认入库单成功",
        "data": db_inbound
    }
