#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
异步任务API
"""

import uuid
import logging
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query, Path
from sqlalchemy.ext.asyncio import AsyncSession
from datetime import datetime
from pydantic import BaseModel

from db.database import get_db
from core.auth import get_current_user
from api.deps import get_current_project, get_current_project_id
from models.project import Project
from models.user import User
from services.async_task_service import AsyncTaskService

logger = logging.getLogger(__name__)

router = APIRouter()

class BatchDeleteRequest(BaseModel):
    """批量删除请求"""
    task_ids: List[str]

@router.get("/tasks/{task_id}")
async def get_task_status(
    task_id: uuid.UUID = Path(..., description="任务ID"),
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取任务状态和结果
    """
    try:
        logger.info(f"查询任务状态: task_id={task_id}, user_id={current_user.id}, project_id={project.id}")
        
        task = await AsyncTaskService.get_task(db, task_id)
        
        if not task:
            logger.warning(f"任务不存在: {task_id}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="任务不存在"
            )
        
        logger.info(f"找到任务: {task_id}, 状态: {task.status}, 用户: {task.user_id}, 项目: {task.project_id}")
        
        # 检查权限 - 放宽权限检查，允许项目内的任务访问
        if task.user_id != current_user.id and task.project_id != project.id:
            logger.warning(f"权限不足: 任务用户={task.user_id}, 当前用户={current_user.id}, 任务项目={task.project_id}, 当前项目={project.id}")
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问该任务"
            )
        
        response_data = {
            "id": str(task.id),
            "task_type": task.task_type,
            "task_name": task.task_name,
            "description": task.description,
            "status": task.status,
            "progress": task.progress,
            "created_at": task.created_at.isoformat() if task.created_at else None,
            "started_at": task.started_at.isoformat() if task.started_at else None,
            "completed_at": task.completed_at.isoformat() if task.completed_at else None,
            "updated_at": task.updated_at.isoformat() if task.updated_at else None,
            "error_message": task.error_message,
            "is_completed": task.status in ["success", "failed", "cancelled", "completed"]
        }
        
        # 如果任务完成(成功或失败)，返回结果数据
        if task.status in ["success", "completed"] and task.result_data:
            response_data["result"] = task.result_data
        elif task.status == "failed":
            # 对于失败的任务，提供错误详情
            response_data["result"] = {
                "success": False,
                "error": task.error_message,
                "message": "任务执行失败"
            }
        
        logger.info(f"返回任务状态: {task_id}, 状态: {task.status}")
        
        return {
            "success": True,
            "message": "获取任务信息成功",
            "data": response_data
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取任务状态失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取任务状态失败: {str(e)}"
        )

@router.get("/tasks")
async def get_tasks(
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
    task_status: Optional[str] = Query(None, alias="status", description="任务状态筛选"),
    task_type: Optional[str] = Query(None, description="任务类型筛选"),
    task_module: Optional[str] = Query(None, description="任务模块筛选"),
    start_date: Optional[str] = Query(None, description="开始日期 (YYYY-MM-DD)"),
    end_date: Optional[str] = Query(None, description="结束日期 (YYYY-MM-DD)"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    limit: int = Query(50, ge=1, le=100, description="每页数量"),
    offset: int = Query(0, ge=0, description="偏移量")
):
    """
    获取任务列表（支持筛选）
    """
    try:
        # 解析日期参数
        parsed_start_date = None
        parsed_end_date = None
        
        if start_date:
            try:
                from datetime import timezone
                parsed_start_date = datetime.strptime(start_date, '%Y-%m-%d').replace(tzinfo=timezone.utc)
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"无效的开始日期格式: {start_date}，应为 YYYY-MM-DD"
                )
        
        if end_date:
            try:
                from datetime import timezone
                # 结束日期设置为当天的23:59:59
                parsed_end_date = datetime.strptime(end_date, '%Y-%m-%d').replace(
                    hour=23, minute=59, second=59, tzinfo=timezone.utc
                )
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"无效的结束日期格式: {end_date}，应为 YYYY-MM-DD"
                )
        
        result = await AsyncTaskService.get_user_tasks(
            db=db,
            user_id=current_user.id,
            project_id=project.id,
            status=task_status,
            task_type=task_type,
            task_module=task_module,
            start_date=parsed_start_date,
            end_date=parsed_end_date,
            search=search,
            limit=limit,
            offset=offset
        )
        
        # 转换任务数据为字典格式
        tasks_data = []
        for task in result["tasks"]:
            task_dict = {
                "id": str(task.id),
                "task_type": task.task_type,
                "task_name": task.task_name,
                "description": task.description,
                "status": task.status,
                "progress": task.progress,
                "created_at": task.created_at.isoformat() if task.created_at else None,
                "started_at": task.started_at.isoformat() if task.started_at else None,
                "completed_at": task.completed_at.isoformat() if task.completed_at else None,
                "error_message": task.error_message,
                "duration": task.duration,
                "is_completed": task.is_completed,
                "is_running": task.is_running
            }
            tasks_data.append(task_dict)
        
        return {
            "success": True,
            "data": {
                "tasks": tasks_data,
                "total": result["total"],
                "offset": result["offset"],
                "limit": result["limit"],
                "status_stats": result["status_stats"],
                "available_modules": result.get("available_modules", [])
            }
        }
        
    except Exception as e:
        logger.error(f"获取任务列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取任务列表失败: {str(e)}"
        )

@router.post("/tasks/{task_id}/cancel")
async def cancel_task(
    task_id: uuid.UUID = Path(..., description="任务ID"),
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    取消任务
    """
    try:
        task = await AsyncTaskService.get_task(db, task_id)
        
        if not task:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="任务不存在"
            )
        
        # 检查权限
        if task.user_id != current_user.id and task.project_id != project.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权取消该任务"
            )
        
        success = await AsyncTaskService.cancel_task(db, task_id)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="任务无法取消（可能已完成或失败）"
            )
        
        return {
            "success": True,
            "message": "任务已取消"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"取消任务失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"取消任务失败: {str(e)}"
        )

@router.delete("/tasks/{task_id}")
async def delete_task(
    task_id: uuid.UUID = Path(..., description="任务ID"),
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    删除任务
    """
    try:
        task = await AsyncTaskService.get_task(db, task_id)
        
        if not task:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="任务不存在"
            )
        
        # 检查权限
        if task.user_id != current_user.id and task.project_id != project.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权删除该任务"
            )
        
        success = await AsyncTaskService.delete_task(db, task_id)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="删除任务失败"
            )
        
        return {
            "success": True,
            "message": "任务已删除"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除任务失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除任务失败: {str(e)}"
        )

@router.post("/tasks/batch-delete")
async def batch_delete_tasks(
    request: BatchDeleteRequest,
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    批量删除任务
    """
    try:
        if not request.task_ids:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="任务ID列表不能为空"
            )
        
        # 转换字符串ID为UUID
        task_uuids = []
        for task_id_str in request.task_ids:
            try:
                task_uuids.append(uuid.UUID(task_id_str))
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"无效的任务ID: {task_id_str}"
                )
        
        # 批量删除
        result = await AsyncTaskService.batch_delete_tasks(
            db=db,
            task_ids=task_uuids,
            user_id=current_user.id,
            project_id=project.id
        )
        
        return {
            "success": True,
            "message": f"批量删除完成：成功 {result['deleted']} 个，失败 {result['failed']} 个",
            "data": result
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"批量删除任务失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"批量删除任务失败: {str(e)}"
        ) 