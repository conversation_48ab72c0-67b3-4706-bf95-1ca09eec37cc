.project-selection-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
  min-height: 100vh;
  background-color: #f5f7fa;
}

.project-selection-header {
  text-align: center;
  margin-bottom: 32px;
  position: relative;
}

.demo-link {
  position: absolute;
  top: 0;
  right: 0;
}

.project-selection-actions {
  display: flex;
  justify-content: space-between;
  margin-bottom: 24px;
}

.action-buttons {
  display: flex;
}

.search-input {
  width: 300px;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;
}

.projects-grid {
  margin-top: 16px;
}

.project-card {
  height: 100%;
  display: flex;
  flex-direction: column;
  transition: all 0.3s;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.project-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
}

.project-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.project-card-title {
  display: flex;
  align-items: center;
}

.project-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background-color: #e6f7ff;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 12px;
  font-size: 20px;
  color: #1890ff;
}

.project-name {
  font-weight: 600;
  font-size: 16px;
  margin-bottom: 4px;
}

.project-meta {
  font-size: 12px;
  color: #8c8c8c;
}

.favorite-icon {
  font-size: 18px;
  color: #d9d9d9;
  cursor: pointer;
  transition: all 0.3s;
}

.favorite-icon:hover {
  transform: scale(1.2);
}

.project-description {
  flex: 1;
  margin-bottom: 16px;
  color: #595959;
  font-size: 14px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.project-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
}

.project-stats {
  display: flex;
  gap: 16px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #8c8c8c;
  font-size: 13px;
}

.project-status {
  display: flex;
  align-items: center;
}

@media (max-width: 768px) {
  .project-selection-actions {
    flex-direction: column;
    gap: 16px;
  }

  .search-input {
    width: 100%;
  }
}
