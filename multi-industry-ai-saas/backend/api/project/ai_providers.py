#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
项目级 AI 提供商 API
"""

import logging
import uuid
from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, Query, Path, status
from sqlalchemy.ext.asyncio import AsyncSession

from db.database import get_db
from models.project import Project
from core.auth import get_current_user
from api.deps import get_current_project
from services.ai import AIProviderService
from schemas.ai import (
    AIProviderCreate,
    AIProviderUpdate,
    AIProviderResponse,
    AIProviderListResponse,
)

logger = logging.getLogger(__name__)
router = APIRouter()

@router.get("/ai/providers", response_model=AIProviderListResponse)
async def list_providers(
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db),
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    status: Optional[str] = Query(None),
):
    """
    获取 AI 提供商列表
    """
    try:
        providers, total = await AIProviderService.get_providers(
            db=db,
            skip=skip,
            limit=limit,
            status=status,
        )
        
        return {
            "success": True,
            "message": "获取 AI 提供商列表成功",
            "data": providers,
            "total": total,
            "page": skip // limit + 1,
            "page_size": limit,
        }
    except Exception as e:
        logger.error(f"获取 AI 提供商列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取 AI 提供商列表失败: {str(e)}",
        )

@router.get("/ai/providers/{provider_id}", response_model=AIProviderResponse)
async def get_provider(
    provider_id: uuid.UUID = Path(...),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db),
):
    """
    获取 AI 提供商详情
    """
    try:
        provider = await AIProviderService.get_provider(db=db, provider_id=provider_id)
        
        if not provider:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"AI 提供商 ID '{provider_id}' 不存在",
            )
            
        return {
            "success": True,
            "message": "获取 AI 提供商详情成功",
            "data": provider,
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取 AI 提供商详情失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取 AI 提供商详情失败: {str(e)}",
        )

@router.post("/ai/providers", response_model=AIProviderResponse, status_code=status.HTTP_201_CREATED)
async def create_provider(
    provider_data: AIProviderCreate,
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db),
):
    """
    创建 AI 提供商
    
    注意：只有系统管理员才能创建 AI 提供商
    """
    try:
        # 检查权限
        if not get_current_user.is_system_admin:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="只有系统管理员才能创建 AI 提供商",
            )
            
        provider = await AIProviderService.create_provider(db=db, provider_data=provider_data)
        
        return {
            "success": True,
            "message": "创建 AI 提供商成功",
            "data": provider,
        }
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建 AI 提供商失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建 AI 提供商失败: {str(e)}",
        )

@router.put("/ai/providers/{provider_id}", response_model=AIProviderResponse)
async def update_provider(
    provider_id: uuid.UUID = Path(...),
    provider_data: AIProviderUpdate = None,
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db),
):
    """
    更新 AI 提供商
    
    注意：只有系统管理员才能更新 AI 提供商
    """
    try:
        # 检查权限
        if not get_current_user.is_system_admin:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="只有系统管理员才能更新 AI 提供商",
            )
            
        provider = await AIProviderService.update_provider(
            db=db,
            provider_id=provider_id,
            provider_data=provider_data,
        )
        
        if not provider:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"AI 提供商 ID '{provider_id}' 不存在",
            )
            
        return {
            "success": True,
            "message": "更新 AI 提供商成功",
            "data": provider,
        }
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新 AI 提供商失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新 AI 提供商失败: {str(e)}",
        )

@router.delete("/ai/providers/{provider_id}", response_model=AIProviderResponse)
async def delete_provider(
    provider_id: uuid.UUID = Path(...),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db),
):
    """
    删除 AI 提供商
    
    注意：只有系统管理员才能删除 AI 提供商
    """
    try:
        # 检查权限
        if not get_current_user.is_system_admin:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="只有系统管理员才能删除 AI 提供商",
            )
            
        success = await AIProviderService.delete_provider(db=db, provider_id=provider_id)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"AI 提供商 ID '{provider_id}' 不存在",
            )
            
        return {
            "success": True,
            "message": "删除 AI 提供商成功",
        }
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除 AI 提供商失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除 AI 提供商失败: {str(e)}",
        )
