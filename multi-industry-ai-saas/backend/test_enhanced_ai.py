#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
测试增强版AI识别功能
支持多种表格类型：采购分拨单、库存表、采购单、盘点表
使用真实的文件ID和项目信息
"""

import asyncio
import uuid
import json
from typing import Optional

# 设置环境
import sys
import os
sys.path.append('/app')

from services.table_processing_service import TableProcessingService
from schemas.table_processing import TableProcessingRequest
# Remove direct AIVisionService import if not used directly after changes
# from services.ai.vision_service import AIVisionService 
from db.database import get_db, AsyncSessionLocal

# Import the executor
from tasks.purchase_order_task_executor import PurchaseOrderTaskExecutor

# 使用真实的文件信息（来自test_ai_recognition.py）
TEST_FILE_ID = "d09595ab-5c4b-4231-bfd0-dc30e697d1b3"
TEST_PROJECT_ID = "*************-48ab-8092-e8eb7f663677"
TEST_USER_ID = "04a15201-2024-4d45-b434-a0bb4ff40c34"

async def test_enhanced_ai_extraction():
    """测试增强版AI提取功能 - 现在通过 PurchaseOrderTaskExecutor"""
    print("=== 增强版AI表格识别测试 (通过 PurchaseOrderTaskExecutor) ===")
    print("使用真实文件和项目信息")
    
    async with AsyncSessionLocal() as db:
        base64_image_string = None
        physical_path = None
        try:
            # 首先验证文件是否存在并加载图片为base64
            print("验证文件信息并加载图片...")
            try:
                from models.storage import StorageFile
                from sqlalchemy import select
                
                stmt = select(StorageFile).where(
                    StorageFile.id == uuid.UUID(TEST_FILE_ID),
                    StorageFile.project_id == uuid.UUID(TEST_PROJECT_ID)
                )
                result = await db.execute(stmt)
                file_info = result.scalar_one_or_none()
                
                if file_info:
                    print(f"✓ 文件名: {file_info.name}")
                    base_upload_path = "/app/uploads" # Assuming running in Docker where /app is mapped
                    physical_path = base_upload_path + file_info.storage_path
                    if os.path.exists(physical_path):
                        print(f"✓ 物理文件存在: {physical_path}")
                        with open(physical_path, "rb") as image_file:
                            import base64
                            base64_image_string = base64.b64encode(image_file.read()).decode('utf-8')
                        print(f"✓ 图片已加载并编码为Base64 (长度: {len(base64_image_string)})")
                    else:
                        print(f"❌ 物理文件不存在: {physical_path}")
                        return None
                else:
                    print("❌ 数据库中文件记录不存在")
                    return None
                    
            except Exception as e:
                print(f"❌ 验证文件或加载图片失败: {e}")
                import traceback
                traceback.print_exc()
                return None
            
            if not base64_image_string:
                print("❌ 未能获取图片的Base64编码字符串，测试中止。")
                return None
            
            print("\n" + "=" * 50)
            
            # 创建模拟的AsyncTask对象
            from models.task import AsyncTask
            
            mock_task = AsyncTask(
                id=str(uuid.uuid4()),
                project_id=uuid.UUID(TEST_PROJECT_ID),
                tenant_id=uuid.UUID(TEST_PROJECT_ID),  # 使用project_id作为tenant_id
                user_id=uuid.UUID(TEST_USER_ID),
                task_type="purchase_order_processing",
                input_data={
                    "file_id": TEST_FILE_ID,
                    "upload_type": "allocation_slip",
                    "warehouse_id": None,
                    "task_params": {
                        "use_default_vision_model": True,
                        "vision_temperature": 0.1,
                        "vision_max_tokens": 8192
                    }
                }
            )
            
            # 实例化 PurchaseOrderTaskExecutor
            executor = PurchaseOrderTaskExecutor(mock_task)
            print(f"PurchaseOrderTaskExecutor 已实例化 (Task ID: {mock_task.id})")
            
            # 准备调用 _execute_ai_processing 的参数
            task_params = mock_task.input_data.get("task_params", {})
            
            print(f"测试参数:")
            print(f"- 文件ID (传递给执行器): {TEST_FILE_ID}")
            print(f"- 项目ID: {TEST_PROJECT_ID}")
            print(f"- 用户ID: {TEST_USER_ID}")
            print(f"- 模拟任务参数: {task_params}")
            print()
            
            # 执行 PurchaseOrderTaskExecutor 的核心AI处理方法
            print("开始调用 PurchaseOrderTaskExecutor._execute_ai_processing...")
            
            # Note: _execute_ai_processing is an instance method, so called on `executor`
            # It also requires db, project_id, user_id, file_id, task_params, base64_image_string
            
            final_result_data = await executor._execute_ai_processing(
                db=db,
                project_id=uuid.UUID(TEST_PROJECT_ID),
                user_id=uuid.UUID(TEST_USER_ID),
                file_id=uuid.UUID(TEST_FILE_ID),
                task_params=task_params,
                base64_image_string=base64_image_string
            )
            
            # 显示详细结果
            print("\n" + "=" * 50)
            print("=== PurchaseOrderTaskExecutor 处理结果 ===")
            
            if final_result_data is not None:
                print(f"成功: Executor返回了数据 (类型: {type(final_result_data)})")
                if isinstance(final_result_data, list):
                    print(f"提取到的【门店】数量: {len(final_result_data)}")
                    
                    for store_idx, store_data in enumerate(final_result_data):
                        print(f"\n门店 {store_idx + 1}:")
                        if isinstance(store_data, dict):
                            print(f"  \"store_name\": {repr(store_data.get('store_name'))}")
                            print(f"  \"task_id\": {repr(store_data.get('task_id'))}")
                            print(f"  \"store_total_allocated_quantity\": {repr(store_data.get('store_total_allocated_quantity'))}")
                            print(f"  \"store_total_allocated_amount\": {repr(store_data.get('store_total_allocated_amount'))}")
                            
                            distributions = store_data.get("distributions")
                            if isinstance(distributions, list):
                                print(f"  \"distributions\" (商品数量: {len(distributions)}):")
                                for item_idx, item in enumerate(distributions[:3]):
                                    print(f"    商品 {item_idx + 1}:")
                                    for k, v in item.items():
                                        print(f"      \"{k}\": {repr(v)}")
                                if len(distributions) > 3:
                                    print("      ... (更多商品未显示)")
                            else:
                                print(f"  \"distributions\": {repr(distributions)} (格式非列表)")
                        else:
                            print(f"  门店数据格式非字典: {repr(store_data)}")
                else:
                    print(f"返回的数据不是列表格式: {final_result_data}")
                
                # Basic analysis of the combined result
                print("\n=== 结果数据分析 (门店维度) ===")
                if isinstance(final_result_data, list) and final_result_data:
                    print(f"总共处理了 {len(final_result_data)} 个门店的记录。")

                    all_product_names = []
                    total_allocated_quantity_across_stores = 0
                    total_items_distributed_count = 0

                    for store_data in final_result_data:
                        if isinstance(store_data, dict):
                            store_distributions = store_data.get("distributions")
                            if isinstance(store_distributions, list):
                                total_items_distributed_count += len(store_distributions)
                                for item in store_distributions:
                                    if isinstance(item, dict):
                                        prod_name = item.get("product_name")
                                        if prod_name:
                                            all_product_names.append(str(prod_name))
                                        
                                        alloc_qty = item.get("allocation_quantity")
                                        if alloc_qty is not None:
                                            try:
                                                total_allocated_quantity_across_stores += float(alloc_qty)
                                            except (ValueError, TypeError):
                                                pass
                    
                    unique_product_names = sorted(list(set(filter(None, all_product_names))))
                    print(f"在所有门店的分拨中，总共涉及 {len(unique_product_names)} 个独特商品名称。")
                    if unique_product_names:
                        print(f"  商品名称列表 (部分): {unique_product_names[:10]}{'...' if len(unique_product_names) > 10 else ''}")
                    
                    print(f"所有门店的所有商品 \'allocation_quantity\' 总和: {total_allocated_quantity_across_stores}")
                    print(f"总分拨商品条目数 (所有门店合计): {total_items_distributed_count}")

                else:
                    print("未提取到可分析的数据或数据格式不符 (期望门店列表)。")

            else:
                print("失败: Executor 未返回数据 (None)。")
                # Here, we might not have a `result` object like from TableProcessingService
                # to check for .errors or .warnings. The executor either returns data or raises an exception.

            # For this test structure, we assume success if data is returned.
            # Errors during executor._execute_ai_processing would be caught by the outer try-except.
            return final_result_data # Return the data itself for potential further checks in main_tests
            
        except Exception as e:
            print(f"执行 PurchaseOrderTaskExecutor 测试失败: {e}")
            import traceback
            traceback.print_exc()
            return None # Indicate failure

async def test_different_table_types():
    """测试不同类型的表格识别提示词生成"""
    print("\n" + "=" * 60)
    print("=== 测试多种表格类型的智能提示词生成 ===")
    
    test_prompts = [
        ("采购分拨单", "提取采购分拨单中的商品信息和各门店分拨数量"),
        ("库存表", "提取库存表中的商品编码、名称、库存数量等信息"),
        ("采购单", "提取采购单中的商品信息、采购数量、采购单价等"),
        ("盘点表", "提取盘点表中的商品信息、账面数量、实盘数量、盈亏情况"),
        ("通用表格", "提取表格中的所有数据，保持原始结构")
    ]
    
    async with AsyncSessionLocal() as db:
        try:
            for table_type, prompt in test_prompts:
                print(f"\n--- 测试 {table_type} ---")
                print(f"输入提示词: {prompt}")
                
                # 调用智能提示词生成
                request = TableProcessingRequest(
                    file_id=uuid.UUID(TEST_FILE_ID),
                    custom_prompt=prompt
                )
                
                smart_prompt = await TableProcessingService._generate_smart_prompt(
                    request, {}, ""
                )
                
                print(f"生成的智能提示词:")
                print("-" * 40)
                print(smart_prompt)
                print("-" * 40)
            
            return True
            
        except Exception as e:
            print(f"测试失败: {e}")
            return False

async def compare_with_original():
    """与原版AI识别进行对比测试"""
    print("\n" + "=" * 60)
    print("=== 增强版 vs 原版 AI识别对比 ===")
    
    async with AsyncSessionLocal() as db:
        try:
            # 测试原版方式（使用原有的提示词）
            print("1. 测试原版提示词方式...")
            original_request = TableProcessingRequest(
                file_id=uuid.UUID(TEST_FILE_ID),
                processing_mode="ai_only",
                custom_prompt="""提取表格中所有商品数据，包括所有行和所有门店列。

JSON格式：
{
  "data": [
    {"商品名称": "名称", "单位": "单位", "规格": "规格", "单价": 价格, "合计": 合计, "商品分类": "分类", "门店1": 数量, "门店2": 数量}
  ]
}

提取表格中每一行商品的完整信息，不要遗漏任何数据。""",
                enable_validation=False,
                enable_correction=False
            )
            
            original_result = await TableProcessingService.process_table(
                db=db,
                request=original_request,
                project_id=uuid.UUID(TEST_PROJECT_ID),
                tenant_id=None,
                user_id=uuid.UUID(TEST_USER_ID)
            )
            
            print(f"原版结果: 成功={original_result.success}, 行数={original_result.total_rows}")
            
            # 测试增强版方式
            print("\n2. 测试增强版智能提示词方式...")
            enhanced_request = TableProcessingRequest(
                file_id=uuid.UUID(TEST_FILE_ID),
                processing_mode="ai_only",
                custom_prompt="这是一个采购分拨单，包含商品信息和多个门店的分拨数量",
                enable_validation=True,
                enable_correction=True,
                vision_temperature=0.1
            )
            
            enhanced_result = await TableProcessingService.process_table(
                db=db,
                request=enhanced_request,
                project_id=uuid.UUID(TEST_PROJECT_ID),
                tenant_id=None,
                user_id=uuid.UUID(TEST_USER_ID)
            )
            
            print(f"增强版结果: 成功={enhanced_result.success}, 行数={enhanced_result.total_rows}")
            
            # 对比分析
            print("\n=== 对比分析 ===")
            print(f"数据行数对比: 原版={original_result.total_rows} vs 增强版={enhanced_result.total_rows}")
            print(f"成功率对比: 原版={original_result.success} vs 增强版={enhanced_result.success}")
            
            if enhanced_result.ai_processing_info:
                ai_info = enhanced_result.ai_processing_info
                print(f"增强版特有信息:")
                print(f"  - 表格类型: {ai_info.get('table_type')}")
                print(f"  - 数据质量: {ai_info.get('data_quality', {}).get('valid_rows', 0)}/{ai_info.get('data_quality', {}).get('total_rows', 0)}")
                print(f"  - 处理策略: {ai_info.get('strategy')}")
            
            return True
            
        except Exception as e:
            print(f"对比测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False

async def main_tests():
    """Runs all tests sequentially."""
    print("🚀 开始增强版AI识别功能测试...")
    print("使用真实文件: " + TEST_FILE_ID)
    print("=" * 60)
    
    await test_enhanced_ai_extraction()
    
    # print("\n" + "=" * 60)
    # await test_different_table_types() # Temporarily disable for focused logging
    
    # print("\n" + "=" * 60)
    # await compare_with_original() # Temporarily disable for focused logging

if __name__ == "__main__":
    print("--- Test script starting (focused on test_enhanced_ai_extraction) ---") # Modified log
    try:
        asyncio.run(main_tests()) 
        print("--- Test script finished successfully ---")
    except Exception as e:
        print(f"!!!!!! Unhandled exception in test_enhanced_ai.py: {e} !!!!!!")
        import traceback
        print("!!!!!! Traceback: !!!!!!")
        traceback.print_exc()
        print("!!!!!!!!!!!!!!!!!!!!!!!!!")  