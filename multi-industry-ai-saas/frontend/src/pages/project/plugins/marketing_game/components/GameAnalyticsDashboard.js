import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Progress,
  Table,
  Select,
  DatePicker,
  Button,
  Space,
  Tag,
  Typography,
  Tabs,
  List,
  Avatar,
  Tooltip,
  Alert,
  Empty,
  Spin,
  message,
  Modal,
  Divider
} from 'antd';
import {
  LineChart,
  Line,
  AreaChart,
  Area,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as RechartsTooltip,
  Legend,
  ResponsiveContainer
} from 'recharts';
import {
  TrophyOutlined,
  UserOutlined,
  ShareAltOutlined,
  DollarOutlined,
  RiseOutlined,
  FallOutlined,
  ClockCircleOutlined,
  EnvironmentOutlined,
  MobileOutlined,
  DownloadOutlined,
  ReloadOutlined,
  EyeOutlined,
  HeartOutlined
} from '@ant-design/icons';
import moment from 'moment';
import analyticsApi from '../service/api/analytics';
import { marketingTypeOptions } from '../utils/constants';

const { Title, Text, Paragraph } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;
const { TabPane } = Tabs;

const GameAnalyticsDashboard = ({ gameId, visible, onClose }) => {
  const [loading, setLoading] = useState(false);
  const [dateRange, setDateRange] = useState([
    moment().subtract(7, 'days'),
    moment()
  ]);
  const [activeTab, setActiveTab] = useState('overview');
  const [analyticsData, setAnalyticsData] = useState(null);
  const [realTimeData, setRealTimeData] = useState(null);
  const [reportData, setReportData] = useState(null);
  const [timeGranularity, setTimeGranularity] = useState('day');
  const [refreshInterval, setRefreshInterval] = useState(null);

  useEffect(() => {
    if (visible && gameId) {
      fetchAnalyticsData();
      fetchRealTimeData();
      
      // 设置实时数据刷新
      const interval = setInterval(fetchRealTimeData, 30000); // 30秒刷新一次
      setRefreshInterval(interval);
      
      return () => {
        if (interval) clearInterval(interval);
      };
    }
  }, [visible, gameId, dateRange, timeGranularity]);

  const fetchAnalyticsData = async () => {
    try {
      setLoading(true);
      const response = await analyticsApi.getGameAnalytics(gameId, {
        start_date: dateRange[0].format('YYYY-MM-DD'),
        end_date: dateRange[1].format('YYYY-MM-DD'),
        granularity: timeGranularity
      });
      
      if (response.success) {
        setAnalyticsData(response.data);
      }
    } catch (error) {
      console.error('获取分析数据失败:', error);
      message.error('获取分析数据失败');
    } finally {
      setLoading(false);
    }
  };

  const fetchRealTimeData = async () => {
    try {
      const response = await analyticsApi.getRealTimeMetrics(gameId);
      if (response.success) {
        setRealTimeData(response.data);
      }
    } catch (error) {
      console.error('获取实时数据失败:', error);
    }
  };

  const generateReport = async () => {
    try {
      setLoading(true);
      const response = await analyticsApi.generateGameReport(gameId, {
        start_date: dateRange[0].format('YYYY-MM-DD'),
        end_date: dateRange[1].format('YYYY-MM-DD')
      });
      
      if (response.success) {
        setReportData(response.data);
        message.success('报告生成成功');
      }
    } catch (error) {
      console.error('生成报告失败:', error);
      message.error('生成报告失败');
    } finally {
      setLoading(false);
    }
  };

  const exportReport = async (format = 'excel') => {
    try {
      const response = await analyticsApi.exportReport(gameId, {
        format,
        start_date: dateRange[0].format('YYYY-MM-DD'),
        end_date: dateRange[1].format('YYYY-MM-DD')
      });
      
      if (response.success) {
        // 触发文件下载
        const link = document.createElement('a');
        link.href = response.data.download_url;
        link.download = response.data.filename;
        link.click();
        message.success('报告导出成功');
      }
    } catch (error) {
      console.error('导出报告失败:', error);
      message.error('导出报告失败');
    }
  };

  const getStatisticIcon = (type) => {
    switch (type) {
      case 'participants':
        return <UserOutlined style={{ color: '#1890ff' }} />;
      case 'winners':
        return <TrophyOutlined style={{ color: '#faad14' }} />;
      case 'shares':
        return <ShareAltOutlined style={{ color: '#52c41a' }} />;
      case 'cost':
        return <DollarOutlined style={{ color: '#f5222d' }} />;
      default:
        return <UserOutlined />;
    }
  };

  const getChangeIcon = (change) => {
    if (change > 0) {
      return <RiseOutlined style={{ color: '#52c41a' }} />;
    } else if (change < 0) {
      return <FallOutlined style={{ color: '#f5222d' }} />;
    }
    return null;
  };

  const getChangeColor = (change) => {
    if (change > 0) return '#52c41a';
    if (change < 0) return '#f5222d';
    return '#d9d9d9';
  };

  // 图表颜色配置
  const chartColors = ['#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1', '#13c2c2'];

  // 概览面板
  const renderOverview = () => {
    if (!analyticsData) return <Spin />;

    const { metrics, trends, hourly_data } = analyticsData;

    return (
      <div>
        {/* 核心指标 */}
        <Row gutter={16} style={{ marginBottom: 24 }}>
          <Col span={6}>
            <Card>
              <Statistic
                title="总参与人数"
                value={metrics?.total_participants || 0}
                prefix={getStatisticIcon('participants')}
                suffix={
                  <Space>
                    {getChangeIcon(trends?.participants_change)}
                    <Text style={{ fontSize: '14px', color: getChangeColor(trends?.participants_change) }}>
                      {Math.abs(trends?.participants_change || 0)}%
                    </Text>
                  </Space>
                }
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="总中奖数"
                value={metrics?.total_winners || 0}
                prefix={getStatisticIcon('winners')}
                suffix={
                  <Space>
                    {getChangeIcon(trends?.winners_change)}
                    <Text style={{ fontSize: '14px', color: getChangeColor(trends?.winners_change) }}>
                      {Math.abs(trends?.winners_change || 0)}%
                    </Text>
                  </Space>
                }
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="分享次数"
                value={metrics?.total_shares || 0}
                prefix={getStatisticIcon('shares')}
                suffix={
                  <Space>
                    {getChangeIcon(trends?.shares_change)}
                    <Text style={{ fontSize: '14px', color: getChangeColor(trends?.shares_change) }}>
                      {Math.abs(trends?.shares_change || 0)}%
                    </Text>
                  </Space>
                }
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="总成本"
                value={metrics?.total_cost || 0}
                prefix={getStatisticIcon('cost')}
                precision={2}
                suffix="元"
              />
            </Card>
          </Col>
        </Row>

        {/* 转化率指标 */}
        <Row gutter={16} style={{ marginBottom: 24 }}>
          <Col span={8}>
            <Card>
              <Statistic
                title="中奖率"
                value={metrics?.win_rate || 0}
                precision={1}
                suffix="%"
                valueStyle={{ color: '#faad14' }}
              />
              <Progress 
                percent={metrics?.win_rate || 0} 
                strokeColor="#faad14" 
                showInfo={false}
                size="small"
              />
            </Card>
          </Col>
          <Col span={8}>
            <Card>
              <Statistic
                title="分享率"
                value={metrics?.share_rate || 0}
                precision={1}
                suffix="%"
                valueStyle={{ color: '#52c41a' }}
              />
              <Progress 
                percent={metrics?.share_rate || 0} 
                strokeColor="#52c41a" 
                showInfo={false}
                size="small"
              />
            </Card>
          </Col>
          <Col span={8}>
            <Card>
              <Statistic
                title="回访率"
                value={metrics?.return_rate || 0}
                precision={1}
                suffix="%"
                valueStyle={{ color: '#1890ff' }}
              />
              <Progress 
                percent={metrics?.return_rate || 0} 
                strokeColor="#1890ff" 
                showInfo={false}
                size="small"
              />
            </Card>
          </Col>
        </Row>

        {/* 趋势图 */}
        <Card title="参与趋势" style={{ marginBottom: 24 }}>
          <ResponsiveContainer width="100%" height={300}>
            <AreaChart data={trends?.daily_data || []}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="date" />
              <YAxis />
              <RechartsTooltip />
              <Legend />
              <Area 
                type="monotone" 
                dataKey="participants" 
                stroke="#1890ff" 
                fill="#1890ff" 
                fillOpacity={0.3}
                name="参与人数"
              />
              <Area 
                type="monotone" 
                dataKey="winners" 
                stroke="#faad14" 
                fill="#faad14" 
                fillOpacity={0.3}
                name="中奖人数"
              />
            </AreaChart>
          </ResponsiveContainer>
        </Card>

        {/* 时段分析 */}
        <Card title="时段分析" style={{ marginBottom: 24 }}>
          <ResponsiveContainer width="100%" height={250}>
            <BarChart data={hourly_data || []}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="hour" />
              <YAxis />
              <RechartsTooltip />
              <Bar dataKey="count" fill="#1890ff" name="参与次数" />
            </BarChart>
          </ResponsiveContainer>
        </Card>
      </div>
    );
  };

  // 用户分析面板
  const renderUserAnalysis = () => {
    if (!analyticsData) return <Spin />;

    const { user_analysis, geographic_data, device_data } = analyticsData;

    return (
      <div>
        {/* 用户行为分析 */}
        <Row gutter={16} style={{ marginBottom: 24 }}>
          <Col span={12}>
            <Card title="用户类型分布">
              <ResponsiveContainer width="100%" height={200}>
                <PieChart>
                  <Pie
                    data={user_analysis?.user_types || []}
                    dataKey="value"
                    nameKey="name"
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    label
                  >
                    {(user_analysis?.user_types || []).map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={chartColors[index % chartColors.length]} />
                    ))}
                  </Pie>
                  <RechartsTooltip />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            </Card>
          </Col>
          <Col span={12}>
            <Card title="参与频次分布">
              <ResponsiveContainer width="100%" height={200}>
                <BarChart data={user_analysis?.participation_frequency || []}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="frequency" />
                  <YAxis />
                  <RechartsTooltip />
                  <Bar dataKey="users" fill="#52c41a" name="用户数" />
                </BarChart>
              </ResponsiveContainer>
            </Card>
          </Col>
        </Row>

        {/* 地理分布 */}
        <Card title="地理分布" style={{ marginBottom: 24 }}>
          <Row gutter={16}>
            <Col span={12}>
              <List
                header={<div>热门地区</div>}
                dataSource={geographic_data?.top_locations || []}
                renderItem={(item, index) => (
                  <List.Item>
                    <List.Item.Meta
                      avatar={<Avatar style={{ backgroundColor: chartColors[index % chartColors.length] }}>{index + 1}</Avatar>}
                      title={item.location}
                      description={`${item.count} 人次 (${item.percentage}%)`}
                    />
                  </List.Item>
                )}
              />
            </Col>
            <Col span={12}>
              <ResponsiveContainer width="100%" height={250}>
                <PieChart>
                  <Pie
                    data={geographic_data?.distribution || []}
                    dataKey="count"
                    nameKey="location"
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    label
                  >
                    {(geographic_data?.distribution || []).map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={chartColors[index % chartColors.length]} />
                    ))}
                  </Pie>
                  <RechartsTooltip />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            </Col>
          </Row>
        </Card>

        {/* 设备分析 */}
        <Card title="设备分析">
          <Row gutter={16}>
            <Col span={8}>
              <Statistic
                title="移动端占比"
                value={device_data?.mobile_percentage || 0}
                precision={1}
                suffix="%"
                prefix={<MobileOutlined />}
              />
            </Col>
            <Col span={16}>
              <ResponsiveContainer width="100%" height={150}>
                <BarChart data={device_data?.device_types || []}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="device" />
                  <YAxis />
                  <RechartsTooltip />
                  <Bar dataKey="count" fill="#722ed1" name="用户数" />
                </BarChart>
              </ResponsiveContainer>
            </Col>
          </Row>
        </Card>
      </div>
    );
  };

  // 实时监控面板
  const renderRealTimeMonitoring = () => {
    if (!realTimeData) return <Spin />;

    const { current_online, today_metrics, recent_activities, live_stats } = realTimeData;

    return (
      <div>
        {/* 实时指标 */}
        <Alert
          message="实时数据"
          description={`最后更新时间: ${moment().format('YYYY-MM-DD HH:mm:ss')}`}
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />

        <Row gutter={16} style={{ marginBottom: 24 }}>
          <Col span={6}>
            <Card>
              <Statistic
                title="当前在线"
                value={current_online || 0}
                prefix={<UserOutlined />}
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="今日参与"
                value={today_metrics?.participants || 0}
                prefix={<UserOutlined />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="今日中奖"
                value={today_metrics?.winners || 0}
                prefix={<TrophyOutlined />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="今日分享"
                value={today_metrics?.shares || 0}
                prefix={<ShareAltOutlined />}
              />
            </Card>
          </Col>
        </Row>

        {/* 实时活动流 */}
        <Row gutter={16}>
          <Col span={12}>
            <Card title="最近活动" extra={<EyeOutlined />}>
              <List
                dataSource={recent_activities || []}
                renderItem={(activity) => (
                  <List.Item>
                    <List.Item.Meta
                      avatar={
                        <Avatar 
                          icon={activity.type === 'win' ? <TrophyOutlined /> : <UserOutlined />}
                          style={{ backgroundColor: activity.type === 'win' ? '#faad14' : '#1890ff' }}
                        />
                      }
                      title={activity.user_name || '匿名用户'}
                      description={
                        <Space>
                          <Text type="secondary">{activity.action}</Text>
                          <Text type="secondary">{moment(activity.timestamp).fromNow()}</Text>
                        </Space>
                      }
                    />
                    {activity.prize && (
                      <Tag color="gold">{activity.prize}</Tag>
                    )}
                  </List.Item>
                )}
                size="small"
                style={{ maxHeight: 300, overflowY: 'auto' }}
              />
            </Card>
          </Col>
          <Col span={12}>
            <Card title="实时统计">
              <Row gutter={16}>
                <Col span={12}>
                  <Statistic
                    title="实时转化率"
                    value={live_stats?.conversion_rate || 0}
                    precision={1}
                    suffix="%"
                  />
                  <Progress 
                    percent={live_stats?.conversion_rate || 0}
                    strokeColor="#1890ff"
                    size="small"
                  />
                </Col>
                <Col span={12}>
                  <Statistic
                    title="平均会话时长"
                    value={live_stats?.avg_session_duration || 0}
                    precision={0}
                    suffix="秒"
                    prefix={<ClockCircleOutlined />}
                  />
                </Col>
              </Row>
              <Divider />
              <Row gutter={16}>
                <Col span={12}>
                  <Statistic
                    title="跳出率"
                    value={live_stats?.bounce_rate || 0}
                    precision={1}
                    suffix="%"
                  />
                </Col>
                <Col span={12}>
                  <Statistic
                    title="页面停留"
                    value={live_stats?.avg_page_time || 0}
                    precision={0}
                    suffix="秒"
                  />
                </Col>
              </Row>
            </Card>
          </Col>
        </Row>
      </div>
    );
  };

  return (
    <Modal
      title="游戏数据分析"
      open={visible}
      onCancel={onClose}
      width={1200}
      footer={[
        <Button key="export" icon={<DownloadOutlined />} onClick={() => exportReport()}>
          导出报告
        </Button>,
        <Button key="refresh" icon={<ReloadOutlined />} onClick={fetchAnalyticsData}>
          刷新数据
        </Button>,
        <Button key="close" onClick={onClose}>
          关闭
        </Button>
      ]}
      destroyOnClose
    >
      <div style={{ marginBottom: 16 }}>
        <Space>
          <Text strong>时间范围:</Text>
          <RangePicker
            value={dateRange}
            onChange={setDateRange}
            format="YYYY-MM-DD"
          />
          <Text strong>粒度:</Text>
          <Select value={timeGranularity} onChange={setTimeGranularity} style={{ width: 100 }}>
            <Option value="hour">小时</Option>
            <Option value="day">天</Option>
            <Option value="week">周</Option>
          </Select>
          <Button type="primary" onClick={generateReport} loading={loading}>
            生成报告
          </Button>
        </Space>
      </div>

      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="数据概览" key="overview">
          {renderOverview()}
        </TabPane>
        <TabPane tab="用户分析" key="users">
          {renderUserAnalysis()}
        </TabPane>
        <TabPane tab="实时监控" key="realtime">
          {renderRealTimeMonitoring()}
        </TabPane>
      </Tabs>
    </Modal>
  );
};

export default GameAnalyticsDashboard; 