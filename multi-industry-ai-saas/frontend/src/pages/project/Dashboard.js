import React, { useState, useEffect } from 'react';
import {
  Row, Col, Card, Statistic, Typography, Spin, Empty, List, Tag, Button,
  Avatar, message
} from 'antd';
import {
  ShopOutlined,
  UserOutlined,
  ShoppingCartOutlined,
  DollarOutlined,
  RiseOutlined,
  FallOutlined,
  <PERSON><PERSON><PERSON>Outlined,
  <PERSON><PERSON><PERSON>Outlined,
  <PERSON><PERSON><PERSON>Outlined,
  AppstoreOutlined,
  BellOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import { Line, Pie } from '@ant-design/plots';
import { useAuth } from '../../contexts/AuthContext';
import { project } from '../../services';
import './Dashboard.css';

const { Title, Text } = Typography;

/**
 * 项目仪表盘页面
 * 单租户项目的主仪表盘
 */
const ProjectDashboard = () => {
  useAuth(); // 确保用户已登录
  const [loading, setLoading] = useState(true);
  const [dashboardData, setDashboardData] = useState(null);
  const [currentProject, setCurrentProject] = useState(null);

  // 默认数据结构
  const DEFAULT_DATA = {
    summary: {
      totalSales: 1258963.25,
      totalOrders: 8562,
      totalCustomers: 3254,
      totalStores: 12
    },
    todaySales: {
      value: 25689.50,
      change: 12.5,
      isIncrease: true
    },
    todayOrders: {
      value: 156,
      change: 8.3,
      isIncrease: true
    },
    todayCustomers: {
      value: 98,
      change: -3.2,
      isIncrease: false
    },
    todayVisitors: {
      value: 1245,
      change: 12.8,
      isIncrease: true
    },
    stores: [
      { id: 1, name: '旗舰店', sales: 5680.25, orders: 68 },
      { id: 2, name: '中心店', sales: 3250.75, orders: 42 },
      { id: 3, name: '西区店', sales: 2150.50, orders: 28 },
      { id: 4, name: '东区店', sales: 1499.00, orders: 18 }
    ],
    salesTrend: [
      { date: '2023-06-01', value: 8500 },
      { date: '2023-06-02', value: 9200 },
      { date: '2023-06-03', value: 8700 },
      { date: '2023-06-04', value: 9500 },
      { date: '2023-06-05', value: 10200 },
      { date: '2023-06-06', value: 11500 },
      { date: '2023-06-07', value: 12500 }
    ],
    categoryDistribution: [
      { type: '服装', value: 35 },
      { type: '鞋靴', value: 25 },
      { type: '配饰', value: 15 },
      { type: '美妆', value: 10 },
      { type: '家居', value: 15 }
    ],
    topProducts: [
      { id: 1, name: '时尚连衣裙', sales: 256, price: 299, stock: 120 },
      { id: 2, name: '休闲运动鞋', sales: 198, price: 499, stock: 85 },
      { id: 3, name: '精致手提包', sales: 176, price: 699, stock: 62 },
      { id: 4, name: '保湿面霜套装', sales: 145, price: 399, stock: 93 },
      { id: 5, name: '时尚太阳镜', sales: 132, price: 199, stock: 105 }
    ],
    recentActivities: [
      { id: 1, type: 'order', content: '新订单 #12345 已创建', time: '10分钟前', user: '张三' },
      { id: 2, type: 'inventory', content: '商品"夏季T恤"库存不足', time: '30分钟前', user: '系统' },
      { id: 3, type: 'customer', content: '新客户注册: 王五', time: '1小时前', user: '系统' },
      { id: 4, type: 'promotion', content: '促销活动"夏季大促"已开始', time: '2小时前', user: '赵六' },
      { id: 5, type: 'system', content: '系统更新完成', time: '昨天', user: '管理员' }
    ],
    taskCompletion: {
      total: 24,
      completed: 16,
      pending: 6,
      overdue: 2
    },
    upcomingTasks: [
      { id: 1, title: '618促销活动准备', deadline: '2023-06-10', priority: 'high' },
      { id: 2, title: '新品上架审核', deadline: '2023-06-12', priority: 'medium' },
      { id: 3, title: '库存盘点', deadline: '2023-06-15', priority: 'medium' },
      { id: 4, title: '员工培训', deadline: '2023-06-18', priority: 'low' }
    ]
  };

  // 获取仪表盘数据
  useEffect(() => {
    fetchDashboardData();
  }, []);

  // 获取仪表盘数据
  const fetchDashboardData = async () => {
    try {
      setLoading(true);

      // 获取当前项目信息
      const projectId = localStorage.getItem('project_id');
      if (projectId) {
        try {
          const projectData = await project.getProjectInfo(projectId);
          setCurrentProject(projectData);
        } catch (err) {
          console.error('获取项目信息失败:', err);
        }
      }

      // 模拟获取仪表盘数据
      // 实际项目中应该调用真实的API
      setTimeout(() => {
        setDashboardData(DEFAULT_DATA);
        setLoading(false);
      }, 1000);

    } catch (error) {
      console.error('获取仪表盘数据失败:', error);
      setLoading(false);
    }
  };

  // 渲染变化指标
  const renderChangeIndicator = (change, isIncrease) => {
    const color = isIncrease ? '#52c41a' : '#f5222d';
    const Icon = isIncrease ? RiseOutlined : FallOutlined;

    return (
      <Text style={{ color }}>
        <Icon /> {Math.abs(change)}%
      </Text>
    );
  };

  // 处理刷新
  const handleRefresh = () => {
    message.loading({ content: '正在刷新数据...', key: 'refreshMessage', duration: 0 });
    fetchDashboardData().then(() => {
      message.success({ content: '数据刷新成功！', key: 'refreshMessage', duration: 2 });
    }).catch(() => {
      message.error({ content: '刷新失败，请稍后再试', key: 'refreshMessage', duration: 2 });
    });
  };

  // 销售趋势图配置
  const salesTrendConfig = {
    data: dashboardData?.salesTrend || [],
    xField: 'date',
    yField: 'value',
    seriesField: '',
    smooth: true,
    color: '#1a365d',
    point: {
      size: 4,
      shape: 'circle',
      style: {
        fill: 'white',
        stroke: '#1a365d',
        lineWidth: 2
      }
    },
    meta: {
      value: {
        alias: '销售额',
        formatter: (v) => `¥${v.toLocaleString()}`
      }
    },
    tooltip: {
      formatter: (datum) => {
        return { name: '销售额', value: `¥${datum.value.toLocaleString()}` };
      }
    }
  };

  // 类别分布图配置
  const categoryDistributionConfig = {
    data: dashboardData?.categoryDistribution || [],
    angleField: 'value',
    colorField: 'type',
    radius: 0.8,
    innerRadius: 0.6,
    label: {
      content: ({ name, percentage }) => `${name ?? ''} ${percentage ?? 0}`
    },
    interactions: [{ type: 'pie-legend-active' }, { type: 'element-active' }],
    statistic: {
      title: false,
      content: {
        style: {
          whiteSpace: 'pre-wrap',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          fontSize: '16px'
        },
        content: '类别\n分布'
      }
    }
  };

  // 任务完成率图配置
  const taskCompletionConfig = {
    data: dashboardData?.taskCompletion ? [
      { type: '已完成', value: dashboardData.taskCompletion.completed },
      { type: '进行中', value: dashboardData.taskCompletion.pending },
      { type: '已逾期', value: dashboardData.taskCompletion.overdue }
    ] : [],
    angleField: 'value',
    colorField: 'type',
    radius: 0.8,
    color: ['#52c41a', '#1890ff', '#f5222d'],
    label: {
      content: ({ percentage }) => `${percentage ?? 0}`,
      style: {
        fontSize: 14,
        textAlign: 'center'
      }
    },
    interactions: [{ type: 'element-active' }],
    statistic: {
      title: false,
      content: {
        style: {
          whiteSpace: 'pre-wrap',
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          fontSize: '16px'
        },
        content: '任务\n完成率'
      }
    }
  };

  return (
    <div className="project-dashboard">
      {loading ? (
        <div className="loading-container">
          <Spin size="large" />
          <p>加载中，请稍候...</p>
        </div>
      ) : dashboardData ? (
        <>
          <div className="dashboard-header">
            <div className="dashboard-title">
              <Title level={2}>{currentProject?.name || '项目'} 仪表盘</Title>
              <Text type="secondary">查看项目整体运营情况、销售表现和任务完成情况</Text>
            </div>
            <Button
              type="primary"
              icon={<ReloadOutlined />}
              onClick={handleRefresh}
            >
              刷新数据
            </Button>
          </div>

          {/* 统计卡片 */}
          <Row gutter={[16, 16]} className="stat-cards">
            <Col xs={24} sm={12} md={6}>
              <Card>
                <Statistic
                  title="今日销售额"
                  value={dashboardData.todaySales.value}
                  precision={2}
                  prefix={<DollarOutlined />}
                  suffix="元"
                />
                <div className="stat-footer">
                  {renderChangeIndicator(
                    dashboardData.todaySales.change,
                    dashboardData.todaySales.isIncrease
                  )}
                  <span className="stat-compare">与昨日相比</span>
                </div>
              </Card>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Card>
                <Statistic
                  title="今日订单数"
                  value={dashboardData.todayOrders.value}
                  prefix={<ShoppingCartOutlined />}
                  suffix="单"
                />
                <div className="stat-footer">
                  {renderChangeIndicator(
                    dashboardData.todayOrders.change,
                    dashboardData.todayOrders.isIncrease
                  )}
                  <span className="stat-compare">与昨日相比</span>
                </div>
              </Card>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Card>
                <Statistic
                  title="今日新客户"
                  value={dashboardData.todayCustomers.value}
                  prefix={<UserOutlined />}
                  suffix="人"
                />
                <div className="stat-footer">
                  {renderChangeIndicator(
                    dashboardData.todayCustomers.change,
                    dashboardData.todayCustomers.isIncrease
                  )}
                  <span className="stat-compare">与昨日相比</span>
                </div>
              </Card>
            </Col>
            <Col xs={24} sm={12} md={6}>
              <Card>
                <Statistic
                  title="今日访客数"
                  value={dashboardData.todayVisitors.value}
                  prefix={<ShopOutlined />}
                  suffix="人次"
                />
                <div className="stat-footer">
                  {renderChangeIndicator(
                    dashboardData.todayVisitors.change,
                    dashboardData.todayVisitors.isIncrease
                  )}
                  <span className="stat-compare">与昨日相比</span>
                </div>
              </Card>
            </Col>
          </Row>

          {/* 图表区域 */}
          <Row gutter={[16, 16]} className="chart-section">
            <Col xs={24} lg={16}>
              <Card
                title={
                  <span>
                    <LineChartOutlined style={{ marginRight: 8 }} />
                    销售趋势 (近7天)
                  </span>
                }
                className="chart-card"
              >
                <Line {...salesTrendConfig} />
              </Card>
            </Col>
            <Col xs={24} lg={8}>
              <Card
                title={
                  <span>
                    <PieChartOutlined style={{ marginRight: 8 }} />
                    产品类别分布
                  </span>
                }
                className="chart-card"
              >
                <Pie {...categoryDistributionConfig} />
              </Card>
            </Col>
          </Row>

          {/* 任务和活动区域 */}
          <Row gutter={[16, 16]} className="activity-section">
            <Col xs={24} lg={12}>
              <Card
                title={
                  <span>
                    <AppstoreOutlined style={{ marginRight: 8 }} />
                    任务完成情况
                  </span>
                }
                className="task-card"
              >
                <Row gutter={[16, 16]}>
                  <Col span={12}>
                    <Pie {...taskCompletionConfig} />
                  </Col>
                  <Col span={12}>
                    <div className="task-stats">
                      <div className="task-stat-item">
                        <Text>总任务数</Text>
                        <Title level={4}>{dashboardData.taskCompletion?.total || 0}</Title>
                      </div>
                      <div className="task-stat-item">
                        <Text type="success">已完成</Text>
                        <Title level={4} type="success">{dashboardData.taskCompletion?.completed || 0}</Title>
                      </div>
                      <div className="task-stat-item">
                        <Text type="warning">进行中</Text>
                        <Title level={4} type="warning">{dashboardData.taskCompletion?.pending || 0}</Title>
                      </div>
                      <div className="task-stat-item">
                        <Text type="danger">已逾期</Text>
                        <Title level={4} type="danger">{dashboardData.taskCompletion?.overdue || 0}</Title>
                      </div>
                    </div>
                  </Col>
                </Row>
                <div className="divider" style={{ margin: '16px 0' }} />
                <div className="upcoming-tasks">
                  <Title level={5}>即将到期任务</Title>
                  <List
                    size="small"
                    dataSource={dashboardData.upcomingTasks || []}
                    renderItem={task => (
                      <List.Item
                        actions={[
                          <Tag color={
                            task.priority === 'high' ? 'red' :
                            task.priority === 'medium' ? 'orange' : 'green'
                          }>
                            {task.priority === 'high' ? '高' :
                             task.priority === 'medium' ? '中' : '低'}优先级
                          </Tag>
                        ]}
                      >
                        <List.Item.Meta
                          title={task.title}
                          description={`截止日期: ${task.deadline}`}
                        />
                      </List.Item>
                    )}
                  />
                </div>
              </Card>
            </Col>
            <Col xs={24} lg={12}>
              <Card
                title={
                  <span>
                    <BellOutlined style={{ marginRight: 8 }} />
                    最近活动
                  </span>
                }
                className="activity-card"
              >
                <List
                  itemLayout="horizontal"
                  dataSource={dashboardData.recentActivities || []}
                  renderItem={activity => (
                    <List.Item>
                      <List.Item.Meta
                        avatar={
                          <Avatar icon={
                            activity.type === 'order' ? <ShoppingCartOutlined /> :
                            activity.type === 'inventory' ? <ShopOutlined /> :
                            activity.type === 'customer' ? <UserOutlined /> :
                            activity.type === 'promotion' ? <BarChartOutlined /> :
                            <AppstoreOutlined />
                          } />
                        }
                        title={activity.content}
                        description={
                          <div>
                            <Text type="secondary">{activity.time}</Text>
                            <Text type="secondary" style={{ marginLeft: 8 }}>操作人: {activity.user}</Text>
                          </div>
                        }
                      />
                    </List.Item>
                  )}
                />
              </Card>
            </Col>
          </Row>

          {/* 门店销售排行 */}
          <Row gutter={[16, 16]} className="store-section">
            <Col span={24}>
              <Card
                title={
                  <span>
                    <ShopOutlined style={{ marginRight: 8 }} />
                    门店销售排行
                  </span>
                }
                className="store-card"
              >
                <List
                  grid={{ gutter: 16, xs: 1, sm: 2, md: 4 }}
                  dataSource={dashboardData.stores || []}
                  renderItem={store => (
                    <List.Item>
                      <Card className="store-detail-card">
                        <div className="store-name">
                          <ShopOutlined /> {store.name}
                        </div>
                        <div className="store-sales">
                          <Statistic
                            title="销售额"
                            value={store.sales}
                            precision={2}
                            prefix="¥"
                            valueStyle={{ color: '#1890ff' }}
                          />
                        </div>
                        <div className="store-orders">
                          <Statistic
                            title="订单数"
                            value={store.orders}
                            suffix="单"
                            valueStyle={{ color: '#52c41a' }}
                          />
                        </div>
                      </Card>
                    </List.Item>
                  )}
                />
              </Card>
            </Col>
          </Row>
        </>
      ) : (
        <Empty description="暂无数据" />
      )}
    </div>
  );
};

export default ProjectDashboard;
