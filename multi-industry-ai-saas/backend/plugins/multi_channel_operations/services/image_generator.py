#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
全渠道运营助手 - AI图片生成服务
"""

import logging
import asyncio
import httpx
from typing import Dict, List, Optional, Any
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from ..models.product_mapping import ProductImageGeneration
from ..models.operation_log import MultiChannelOperationLog

logger = logging.getLogger(__name__)


class AIImageGeneratorService:
    """AI图片生成服务"""
    
    def __init__(self):
        self.supported_models = {
            'dall-e-3': {
                'name': 'DALL-E 3',
                'provider': 'openai',
                'max_size': '1024x1024',
                'styles': ['vivid', 'natural']
            },
            'midjourney': {
                'name': 'Midjourney',
                'provider': 'midjourney',
                'max_size': '1024x1024',
                'styles': ['realistic', 'artistic', 'cartoon']
            },
            'stable-diffusion': {
                'name': 'Stable Diffusion',
                'provider': 'stability',
                'max_size': '1024x1024',
                'styles': ['photorealistic', 'artistic', 'anime']
            }
        }
    
    async def generate_product_image(self,
                                     db: AsyncSession,
                                     project_id: str,
                                     product_id: str,
                                     prompt: str,
                                     style: str = "natural",
                                     size: str = "1024x1024",
                                     model: str = "dall-e-3",
                                     channel_id: Optional[str] = None,
                                     user_id: Optional[str] = None) -> Dict[str, Any]:
        """生成商品图片"""
        try:
            # 创建生成记录
            generation = ProductImageGeneration(
                project_id=project_id,
                product_id=product_id,
                channel_id=channel_id,
                prompt=prompt,
                style=style,
                size=size,
                status="generating",
                model_name=model,
                generation_params={
                    'style': style,
                    'size': size,
                    'model': model
                }
            )
            db.add(generation)
            await db.commit()
            
            # 根据模型选择生成方法
            if model == "dall-e-3":
                result = await self._generate_with_dalle(prompt, style, size)
            elif model == "midjourney":
                result = await self._generate_with_midjourney(prompt, style, size)
            elif model == "stable-diffusion":
                result = await self._generate_with_stable_diffusion(prompt, style, size)
            else:
                result = {
                    'success': False,
                    'error': f'不支持的模型: {model}'
                }
            
            # 更新生成记录
            if result.get('success'):
                generation.status = "completed"
                generation.generated_images = result.get('images', [])
                
                # 记录操作日志
                await self._log_operation(
                    db, project_id, "image_generation", "success",
                    f"AI图片生成成功: {len(result.get('images', []))}张图片",
                    product_id, user_id
                )
            else:
                generation.status = "failed"
                generation.error_message = result.get('error', '生成失败')
                
                # 记录操作日志
                await self._log_operation(
                    db, project_id, "image_generation", "failed",
                    f"AI图片生成失败: {result.get('error', '未知错误')}",
                    product_id, user_id
                )
            
            await db.commit()
            
            return {
                'success': result.get('success', False),
                'generation_id': str(generation.id),
                'images': result.get('images', []),
                'error': result.get('error')
            }
            
        except Exception as e:
            logger.error(f"AI图片生成异常: {e}")
            return {
                'success': False,
                'error': f'生成异常: {str(e)}'
            }
    
    async def generate_channel_specific_image(self,
                                              db: AsyncSession,
                                              project_id: str,
                                              product_id: str,
                                              channel_id: str,
                                              base_prompt: str,
                                              user_id: Optional[str] = None) -> Dict[str, Any]:
        """为特定渠道生成定制化商品图片"""
        try:
            # 获取渠道信息来优化提示词
            channel_info = await self._get_channel_info(db, channel_id)
            platform_code = channel_info.get('platform_code', '')
            
            # 根据平台特性优化提示词
            optimized_prompt = await self._optimize_prompt_for_platform(
                base_prompt, platform_code
            )
            
            # 选择适合平台的风格
            style = self._get_platform_preferred_style(platform_code)
            
            # 生成图片
            result = await self.generate_product_image(
                db, project_id, product_id, optimized_prompt,
                style=style, channel_id=channel_id, user_id=user_id
            )
            
            return result
            
        except Exception as e:
            logger.error(f"渠道定制图片生成异常: {e}")
            return {
                'success': False,
                'error': f'生成异常: {str(e)}'
            }
    
    async def batch_generate_images(self,
                                    db: AsyncSession,
                                    project_id: str,
                                    requests: List[Dict[str, Any]],
                                    user_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """批量生成图片"""
        results = []
        
        # 并发生成图片
        tasks = []
        for request in requests:
            task = self.generate_product_image(
                db=db,
                project_id=project_id,
                product_id=request['product_id'],
                prompt=request['prompt'],
                style=request.get('style', 'natural'),
                size=request.get('size', '1024x1024'),
                model=request.get('model', 'dall-e-3'),
                channel_id=request.get('channel_id'),
                user_id=user_id
            )
            tasks.append(task)
        
        # 等待所有任务完成
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理异常结果
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                processed_results.append({
                    'success': False,
                    'error': f'生成异常: {str(result)}',
                    'request_index': i
                })
            else:
                processed_results.append(result)
        
        return processed_results
    
    async def select_image_for_product(self,
                                       db: AsyncSession,
                                       generation_id: str,
                                       image_url: str,
                                       user_id: Optional[str] = None) -> bool:
        """选择图片作为商品主图"""
        try:
            # 查找生成记录
            stmt = select(ProductImageGeneration).where(
                ProductImageGeneration.id == generation_id
            )
            result = await db.execute(stmt)
            generation = result.scalar_one_or_none()
            
            if not generation:
                return False
            
            # 更新选中的图片
            generation.selected_image_url = image_url
            await db.commit()
            
            # 记录操作日志
            await self._log_operation(
                db, generation.project_id, "image_selection", "success",
                f"选择AI生成图片作为商品主图",
                generation.product_id, user_id
            )
            
            return True
            
        except Exception as e:
            logger.error(f"选择图片异常: {e}")
            return False
    
    async def get_generation_history(self,
                                     db: AsyncSession,
                                     project_id: str,
                                     product_id: Optional[str] = None,
                                     limit: int = 50) -> List[Dict[str, Any]]:
        """获取图片生成历史"""
        try:
            stmt = select(ProductImageGeneration).where(
                ProductImageGeneration.project_id == project_id
            )
            
            if product_id:
                stmt = stmt.where(ProductImageGeneration.product_id == product_id)
            
            stmt = stmt.order_by(ProductImageGeneration.created_at.desc()).limit(limit)
            
            result = await db.execute(stmt)
            generations = result.scalars().all()
            
            return [
                {
                    'id': str(gen.id),
                    'product_id': gen.product_id,
                    'channel_id': gen.channel_id,
                    'prompt': gen.prompt,
                    'style': gen.style,
                    'size': gen.size,
                    'model_name': gen.model_name,
                    'status': gen.status,
                    'generated_images': gen.generated_images,
                    'selected_image_url': gen.selected_image_url,
                    'error_message': gen.error_message,
                    'created_at': gen.created_at.isoformat() if gen.created_at else None
                }
                for gen in generations
            ]
            
        except Exception as e:
            logger.error(f"获取生成历史异常: {e}")
            return []
    
    async def _generate_with_dalle(self, prompt: str, style: str, size: str) -> Dict[str, Any]:
        """使用DALL-E生成图片"""
        try:
            # 这里需要实际的OpenAI API调用
            # 暂时返回模拟结果
            await asyncio.sleep(2)  # 模拟生成时间
            
            return {
                'success': True,
                'images': [
                    'https://example.com/generated_image_1.jpg',
                    'https://example.com/generated_image_2.jpg'
                ]
            }
            
        except Exception as e:
            logger.error(f"DALL-E生成失败: {e}")
            return {
                'success': False,
                'error': f'DALL-E生成失败: {str(e)}'
            }
    
    async def _generate_with_midjourney(self, prompt: str, style: str, size: str) -> Dict[str, Any]:
        """使用Midjourney生成图片"""
        try:
            # 这里需要实际的Midjourney API调用
            # 暂时返回模拟结果
            await asyncio.sleep(3)  # 模拟生成时间
            
            return {
                'success': True,
                'images': [
                    'https://example.com/midjourney_image_1.jpg'
                ]
            }
            
        except Exception as e:
            logger.error(f"Midjourney生成失败: {e}")
            return {
                'success': False,
                'error': f'Midjourney生成失败: {str(e)}'
            }
    
    async def _generate_with_stable_diffusion(self, prompt: str, style: str, size: str) -> Dict[str, Any]:
        """使用Stable Diffusion生成图片"""
        try:
            # 这里需要实际的Stability AI API调用
            # 暂时返回模拟结果
            await asyncio.sleep(1.5)  # 模拟生成时间
            
            return {
                'success': True,
                'images': [
                    'https://example.com/stable_diffusion_image_1.jpg',
                    'https://example.com/stable_diffusion_image_2.jpg',
                    'https://example.com/stable_diffusion_image_3.jpg'
                ]
            }
            
        except Exception as e:
            logger.error(f"Stable Diffusion生成失败: {e}")
            return {
                'success': False,
                'error': f'Stable Diffusion生成失败: {str(e)}'
            }
    
    async def _optimize_prompt_for_platform(self, base_prompt: str, platform_code: str) -> str:
        """为特定平台优化提示词"""
        platform_optimizations = {
            'meituan': '美团外卖风格，食物摄影，暖色调，诱人的食物展示',
            'douyin': '抖音风格，年轻时尚，鲜艳色彩，社交媒体友好',
            'eleme': '饿了么风格，简洁清晰，食物特写，蓝色主题'
        }
        
        optimization = platform_optimizations.get(platform_code, '')
        if optimization:
            return f"{base_prompt}, {optimization}"
        return base_prompt
    
    def _get_platform_preferred_style(self, platform_code: str) -> str:
        """获取平台偏好的图片风格"""
        platform_styles = {
            'meituan': 'photorealistic',
            'douyin': 'vivid',
            'eleme': 'natural'
        }
        return platform_styles.get(platform_code, 'natural')
    
    async def _get_channel_info(self, db: AsyncSession, channel_id: str) -> Dict[str, Any]:
        """获取渠道信息"""
        # 这里需要实现从数据库获取渠道信息的逻辑
        # 暂时返回模拟数据
        return {
            'platform_code': 'meituan',
            'platform_name': '美团'
        }
    
    async def _log_operation(self, db: AsyncSession, project_id: str, operation_type: str,
                             status: str, description: str, product_id: str,
                             user_id: Optional[str] = None):
        """记录操作日志"""
        log = MultiChannelOperationLog(
            project_id=project_id,
            operation_type=operation_type,
            operation_name="AI图片生成",
            operation_description=description,
            target_type="product",
            target_id=product_id,
            status=status,
            user_id=user_id,
            operation_source="manual" if user_id else "auto"
        )
        db.add(log)
