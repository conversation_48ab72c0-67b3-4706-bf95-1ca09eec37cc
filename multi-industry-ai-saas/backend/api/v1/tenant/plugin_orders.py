#!/usr/bin/env python
# -*- coding: utf-8 -*-

import logging
import uuid
from typing import Dict, Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query, Path
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, desc, func
from datetime import datetime, timedelta

from db.database import get_db
from models.tenant import Tenant
from models.plugin import Plugin, PluginPurchase
from models.user import User
from core.auth import get_current_user, get_current_tenant

# 配置日志
logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter()

@router.get("/orders", summary="获取插件订单列表")
async def get_plugin_orders(
    status: Optional[str] = Query(None, description="订单状态: active, expired, cancelled"),
    plugin_id: Optional[uuid.UUID] = Query(None, description="插件ID"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(10, ge=1, le=100, description="每页数量"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant)
):
    """
    获取当前租户的插件订单列表
    """
    try:
        # 构建查询条件
        conditions = [PluginPurchase.tenant_id == current_tenant.id]
        
        if status:
            conditions.append(PluginPurchase.status == status)
        
        if plugin_id:
            conditions.append(PluginPurchase.plugin_id == plugin_id)
        
        # 计算总数
        count_query = select(func.count()).select_from(PluginPurchase).where(and_(*conditions))
        result = await db.execute(count_query)
        total = result.scalar_one()
        
        # 查询订单列表
        query = select(PluginPurchase, Plugin).join(
            Plugin, PluginPurchase.plugin_id == Plugin.id
        ).where(
            and_(*conditions)
        ).order_by(
            desc(PluginPurchase.created_at)
        ).offset(
            (page - 1) * page_size
        ).limit(page_size)
        
        result = await db.execute(query)
        orders = result.all()
        
        # 构建响应数据
        order_list = []
        for purchase, plugin in orders:
            order_list.append({
                "id": purchase.id,
                "order_number": purchase.order_number,
                "plugin_id": purchase.plugin_id,
                "plugin_name": plugin.name,
                "plugin_icon": plugin.icon_url,
                "amount": purchase.amount,
                "currency": purchase.currency,
                "billing_cycle": purchase.billing_cycle,
                "duration": purchase.duration,
                "start_date": purchase.start_date,
                "end_date": purchase.end_date,
                "status": purchase.status,
                "payment_status": purchase.payment_status,
                "payment_method": purchase.payment_method,
                "created_at": purchase.created_at
            })
        
        return {
            "success": True,
            "message": "获取插件订单列表成功",
            "data": {
                "items": order_list,
                "total": total,
                "page": page,
                "page_size": page_size,
                "pages": (total + page_size - 1) // page_size
            }
        }
    except Exception as e:
        logger.error(f"获取插件订单列表失败: {str(e)}")
        return {
            "success": False,
            "message": f"获取插件订单列表失败: {str(e)}",
            "data": None
        }

@router.get("/orders/{order_id}", summary="获取插件订单详情")
async def get_plugin_order(
    order_id: uuid.UUID = Path(..., description="订单ID"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant)
):
    """
    获取插件订单详情
    """
    try:
        # 查询订单
        query = select(PluginPurchase, Plugin).join(
            Plugin, PluginPurchase.plugin_id == Plugin.id
        ).where(
            and_(
                PluginPurchase.id == order_id,
                PluginPurchase.tenant_id == current_tenant.id
            )
        )
        
        result = await db.execute(query)
        order_data = result.first()
        
        if not order_data:
            return {
                "success": False,
                "message": "订单不存在或无权访问",
                "data": None
            }
        
        purchase, plugin = order_data
        
        # 查询购买人信息
        from models.user import User
        user_query = select(User).where(User.id == purchase.purchased_by)
        result = await db.execute(user_query)
        user = result.scalar_one_or_none()
        
        # 构建响应数据
        order_detail = {
            "id": purchase.id,
            "order_number": purchase.order_number,
            "tenant_id": purchase.tenant_id,
            "plugin_id": purchase.plugin_id,
            "plugin_name": plugin.name,
            "plugin_description": plugin.description,
            "plugin_icon": plugin.icon_url,
            "plugin_version": plugin.version,
            "amount": purchase.amount,
            "currency": purchase.currency,
            "billing_cycle": purchase.billing_cycle,
            "duration": purchase.duration,
            "start_date": purchase.start_date,
            "end_date": purchase.end_date,
            "status": purchase.status,
            "payment_status": purchase.payment_status,
            "payment_method": purchase.payment_method,
            "purchased_by": {
                "id": user.id if user else None,
                "name": user.username if user else "未知用户"
            },
            "created_at": purchase.created_at,
            "updated_at": purchase.updated_at
        }
        
        # 获取服务商系统中的订单信息
        # 这里需要调用服务商系统的API或直接查询数据库
        # 为简化实现，我们直接查询本地数据库
        from models.order import Order
        order_query = select(Order).where(Order.order_no == purchase.order_number)
        result = await db.execute(order_query)
        order = result.scalar_one_or_none()
        
        if order:
            order_detail["service_provider_order"] = {
                "id": order.id,
                "order_no": order.order_no,
                "status": order.status,
                "payment_method": order.payment_method,
                "payment_time": order.payment_time,
                "created_at": order.created_at
            }
        
        return {
            "success": True,
            "message": "获取插件订单详情成功",
            "data": order_detail
        }
    except Exception as e:
        logger.error(f"获取插件订单详情失败: {str(e)}")
        return {
            "success": False,
            "message": f"获取插件订单详情失败: {str(e)}",
            "data": None
        }
