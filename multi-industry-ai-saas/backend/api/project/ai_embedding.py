"""
AI 嵌入管理 API
"""

import uuid
import time
from typing import Optional, List
from fastapi import APIRouter, Depends, HTTPException, status, Query, Path
from sqlalchemy.ext.asyncio import AsyncSession

from api.deps import get_db, get_current_project
from core.auth import get_current_user
from models.project import Project
from models.user import User
from services.ai.embedding_service import AIEmbeddingService
from schemas.ai.embedding import (
    AIEmbeddingQuery,
    AIEmbeddingSearchResponse,
    AIEmbeddingStatsResponse,
    VectorManagementRequest,
    VectorManagementResponse,
    EmbeddingCreateRequest,
    EmbeddingCreateResponse
)
import logging
from services.ai.system_integration_service import SystemAIIntegrationService

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/ai/embedding", tags=["AI嵌入管理"])


@router.post("/embeddings", response_model=EmbeddingCreateResponse)
async def create_embedding(
    request: EmbeddingCreateRequest,
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    创建文本嵌入（使用AI助手系统配置的默认嵌入模型）
    """
    try:
        # 获取模型（如果指定了model_id使用指定模型，否则使用默认模型）
        if request.model_id:
            model = await AIEmbeddingService.get_embedding_model(db=db, model_id=request.model_id)
            if not model:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"嵌入模型 ID '{request.model_id}' 不存在或未在AI助手系统中配置",
                )
        else:
            # 从系统整合设置中获取默认嵌入模型
            system_config = await SystemAIIntegrationService.get_integration_config(
                db=db,
                project_id=project.id
            )

            if not system_config or not system_config.embedding_enabled:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="项目未启用嵌入功能，请在AI助手系统整合中启用嵌入功能",
                )

            default_embedding_model_id = system_config.default_embedding_model_id
            if not default_embedding_model_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="项目未配置默认嵌入模型，请在AI助手系统整合中选择默认嵌入模型",
                )

            model = await AIEmbeddingService.get_embedding_model(db=db, model_id=default_embedding_model_id)
            if not model:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="默认嵌入模型不存在或已禁用，请在AI助手系统整合中重新选择",
                )

        # 创建嵌入
        result = await AIEmbeddingService.create_embedding(
            db=db,
            project_id=project.id,
            user_id=current_user.id,
            input=request.input,
            model_id=model.id,
            metadata=request.metadata
        )

        if not result.get("success"):
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=result.get("message", "创建嵌入失败"),
            )

        return {
            "success": True,
            "message": "创建嵌入成功",
            "data": result["data"],
            "model": result["model"]
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建嵌入失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建嵌入失败: {str(e)}",
        )


@router.post("/search", response_model=AIEmbeddingSearchResponse)
async def search_embeddings(
    query: AIEmbeddingQuery,
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    搜索相似嵌入向量（使用AI助手系统配置的默认模型）
    """
    try:
        start_time = time.time()

        # 获取查询向量
        if query.model_id:
            model = await AIEmbeddingService.get_embedding_model(db=db, model_id=query.model_id)
            if not model:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"嵌入模型 ID '{query.model_id}' 不存在或未在AI助手系统中配置",
                )
        else:
            # 从系统整合设置中获取默认嵌入模型
            system_config = await SystemAIIntegrationService.get_integration_config(
                db=db,
                project_id=project.id
            )

            if not system_config or not system_config.embedding_enabled:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="项目未启用嵌入功能，请在AI助手系统整合中启用嵌入功能",
                )

            default_embedding_model_id = system_config.default_embedding_model_id
            if not default_embedding_model_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="项目未配置默认嵌入模型，请在AI助手系统整合中选择默认嵌入模型",
                )

            model = await AIEmbeddingService.get_embedding_model(db=db, model_id=default_embedding_model_id)
            if not model:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="默认嵌入模型不存在或已禁用，请在AI助手系统整合中重新选择",
                )

        # 生成查询向量
        embedding_result = await AIEmbeddingService.create_embedding(
            db=db,
            project_id=project.id,
            user_id=current_user.id,
            input=query.query_text,
            model_id=model.id
        )

        if not embedding_result.get("success"):
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="生成查询向量失败",
            )

        query_vector = embedding_result["data"][0]["embedding"]

        # 搜索相似向量
        similar_chunks = await AIEmbeddingService.search_similar_embeddings(
            db=db,
            query_vector=query_vector,
            project_id=project.id,
            knowledge_base_ids=query.knowledge_base_ids,
            top_k=query.top_k,
            similarity_threshold=query.similarity_threshold,
            metadata_filter=query.metadata_filter
        )

        search_time = (time.time() - start_time) * 1000  # 转换为毫秒

        return {
            "success": True,
            "message": "搜索完成",
            "data": similar_chunks,
            "query_text": query.query_text,
            "total_results": len(similar_chunks),
            "search_time_ms": search_time
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"搜索相似嵌入向量失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"搜索相似嵌入向量失败: {str(e)}",
        )


@router.get("/stats", response_model=AIEmbeddingStatsResponse)
async def get_embedding_stats(
    project: Project = Depends(get_current_project),
    knowledge_base_id: Optional[uuid.UUID] = Query(None, description="知识库ID，为空时获取整个项目的统计"),
    db: AsyncSession = Depends(get_db),
):
    """
    获取嵌入统计信息
    """
    try:
        stats = await AIEmbeddingService.get_vector_statistics(
            db=db,
            project_id=project.id,
            knowledge_base_id=knowledge_base_id
        )

        return {
            "success": True,
            "message": "获取统计信息成功",
            "data": stats
        }

    except Exception as e:
        logger.error(f"获取嵌入统计信息失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取嵌入统计信息失败: {str(e)}",
        )


@router.post("/vector-management", response_model=VectorManagementResponse)
async def manage_vectors(
    request: VectorManagementRequest,
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    向量管理操作（清理、重建索引、优化、新建索引等）
    """
    try:
        if request.action == "cleanup":
            # 清理孤立的嵌入记录
            result = await AIEmbeddingService.cleanup_orphaned_embeddings(
                db=db,
                project_id=project.id
            )
            
            # 检查服务层返回的结果
            if not result.get("success"):
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=result.get("message", "向量清理失败"),
                )
            
            return {
                "success": True,
                "message": result.get("message", "向量清理完成"),
                "action": "cleanup",
                "processed_count": result.get("deleted_count", 0),
                "details": {"deleted_embeddings": result.get("deleted_count", 0)}
            }
            
        elif request.action == "reindex":
            # 重建索引：重新生成所有文档的嵌入向量
            result = await AIEmbeddingService.rebuild_vector_index(
                db=db,
                project_id=project.id,
                knowledge_base_ids=request.knowledge_base_ids
            )
            
            # 检查服务层返回的结果
            if not result.get("success"):
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=result.get("message", "重建索引失败"),
                )
            
            return {
                "success": True,
                "message": result.get("message", "重建索引完成"),
                "action": "reindex",
                "processed_count": result.get("processed_count", 0),
                "details": {
                    "processed_documents": result.get("processed_count", 0),
                    "failed_documents": result.get("failed_count", 0)
                }
            }
            
        elif request.action == "optimize":
            # 优化向量存储：去重、压缩等
            result = await AIEmbeddingService.optimize_vector_storage(
                db=db,
                project_id=project.id
            )
            
            # 检查服务层返回的结果
            if not result.get("success"):
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=result.get("message", "向量优化失败"),
                )
            
            return {
                "success": True,
                "message": result.get("message", "向量优化完成"),
                "action": "optimize",
                "processed_count": result.get("processed_count", 0),
                "details": {
                    "deduped_count": result.get("deduped_count", 0),
                    "empty_cleaned_count": result.get("empty_cleaned_count", 0)
                }
            }
            
        elif request.action == "create_index":
            # 新建索引：为指定知识库创建向量索引
            if not request.knowledge_base_ids or len(request.knowledge_base_ids) != 1:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="创建索引操作需要指定单个知识库ID",
                )
            
            knowledge_base_id = request.knowledge_base_ids[0]
            force_rebuild = request.options.get("force_rebuild", False) if request.options else False
            
            result = await AIEmbeddingService.create_vector_index(
                db=db,
                knowledge_base_id=knowledge_base_id,
                user_id=current_user.id,
                force_rebuild=force_rebuild
            )
            
            # 检查服务层返回的结果
            if not result.get("success"):
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=result.get("message", "索引创建失败"),
                )
            
            return {
                "success": True,
                "message": result.get("message", "索引创建完成"),
                "action": "create_index",
                "processed_count": result.get("processed_count", 0),
                "details": {
                    "processed_documents": result.get("processed_count", 0),
                    "failed_documents": result.get("failed_count", 0),
                    "total_documents": result.get("total_documents", 0),
                    "total_chunks": result.get("total_chunks", 0)
                }
            }
            
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"不支持的操作类型: {request.action}。支持的操作：cleanup, reindex, optimize, create_index",
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"向量管理操作失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"向量管理操作失败: {str(e)}",
        )


@router.get("/knowledge-base/{knowledge_base_id}/details", response_model=dict)
async def get_knowledge_base_details(
    knowledge_base_id: uuid.UUID,
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db),
):
    """
    获取知识库的详细信息，包括文档和块的状态
    """
    try:
        # 验证知识库是否属于当前项目
        from sqlalchemy import select, func, and_
        from models.ai.knowledge import AIKnowledgeBase, AIKnowledgeDocument, AIKnowledgeChunk
        
        kb_query = select(AIKnowledgeBase).where(
            and_(
                AIKnowledgeBase.id == knowledge_base_id,
                AIKnowledgeBase.project_id == project.id
            )
        )
        kb_result = await db.execute(kb_query)
        knowledge_base = kb_result.scalars().first()
        
        if not knowledge_base:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="知识库不存在或不属于当前项目",
            )
        
        # 获取文档详细信息
        docs_query = select(AIKnowledgeDocument).where(
            AIKnowledgeDocument.knowledge_base_id == knowledge_base_id
        ).order_by(AIKnowledgeDocument.created_at.desc())
        docs_result = await db.execute(docs_query)
        documents = docs_result.scalars().all()
        
        # 为每个文档获取块信息
        documents_details = []
        for doc in documents:
            # 获取文档的块统计
            chunk_stats_query = select(
                func.count(AIKnowledgeChunk.id).label('total_chunks'),
                func.count(AIKnowledgeChunk.embedding).label('with_embedding'),
                func.sum(AIKnowledgeChunk.token_count).label('total_tokens')
            ).where(AIKnowledgeChunk.document_id == doc.id)
            
            chunk_stats_result = await db.execute(chunk_stats_query)
            chunk_stats = chunk_stats_result.first()
            
            # 获取最近的几个块示例
            chunks_query = select(AIKnowledgeChunk).where(
                AIKnowledgeChunk.document_id == doc.id
            ).order_by(AIKnowledgeChunk.chunk_index).limit(5)
            
            chunks_result = await db.execute(chunks_query)
            chunks = chunks_result.scalars().all()
            
            chunks_info = []
            for chunk in chunks:
                # 安全地检查向量维度
                has_embedding = chunk.embedding is not None
                embedding_dimensions = 0
                if has_embedding:
                    try:
                        # 对于pgvector，可以通过转换为list来获取长度
                        embedding_list = list(chunk.embedding) if chunk.embedding is not None else []
                        embedding_dimensions = len(embedding_list)
                    except Exception:
                        embedding_dimensions = 1024  # 默认维度
                
                chunks_info.append({
                    "id": str(chunk.id),
                    "chunk_index": chunk.chunk_index,
                    "content_preview": chunk.content[:100] + "..." if len(chunk.content) > 100 else chunk.content,
                    "token_count": chunk.token_count,
                    "has_embedding": has_embedding,
                    "embedding_dimensions": embedding_dimensions,
                    "created_at": chunk.created_at.isoformat() if chunk.created_at else None
                })
            
            documents_details.append({
                "id": str(doc.id),
                "title": doc.title,
                "file_path": doc.file_path,
                "file_type": doc.file_type,
                "file_size": doc.file_size,
                "embedding_status": doc.embedding_status,
                "chunk_count": doc.chunk_count,
                "total_chunks": chunk_stats.total_chunks or 0,
                "chunks_with_embedding": chunk_stats.with_embedding or 0,
                "total_tokens": chunk_stats.total_tokens or 0,
                "embedding_success_rate": (chunk_stats.with_embedding / chunk_stats.total_chunks * 100) if chunk_stats.total_chunks else 0,
                "chunks_preview": chunks_info,
                "created_at": doc.created_at.isoformat() if doc.created_at else None,
                "updated_at": doc.updated_at.isoformat() if doc.updated_at else None
            })
        
        # 获取知识库整体统计
        total_chunks_query = select(
            func.count(AIKnowledgeChunk.id).label('total_chunks'),
            func.count(AIKnowledgeChunk.embedding).label('with_embedding'),
            func.sum(AIKnowledgeChunk.token_count).label('total_tokens')
        ).where(AIKnowledgeChunk.knowledge_base_id == knowledge_base_id)
        
        total_stats_result = await db.execute(total_chunks_query)
        total_stats = total_stats_result.first()
        
        return {
            "success": True,
            "data": {
                "knowledge_base": {
                    "id": str(knowledge_base.id),
                    "name": knowledge_base.name,
                    "description": knowledge_base.description,
                    "status": knowledge_base.status,
                    "document_count": knowledge_base.document_count,
                    "chunk_count": knowledge_base.chunk_count,
                    "embedding_model_id": str(knowledge_base.embedding_model_id) if knowledge_base.embedding_model_id else None,
                    "created_at": knowledge_base.created_at.isoformat() if knowledge_base.created_at else None,
                    "updated_at": knowledge_base.updated_at.isoformat() if knowledge_base.updated_at else None
                },
                "statistics": {
                    "total_documents": len(documents),
                    "total_chunks": total_stats.total_chunks or 0,
                    "chunks_with_embedding": total_stats.with_embedding or 0,
                    "total_tokens": total_stats.total_tokens or 0,
                    "embedding_success_rate": (total_stats.with_embedding / total_stats.total_chunks * 100) if total_stats.total_chunks else 0
                },
                "documents": documents_details
            }
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取知识库详细信息失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取知识库详细信息失败: {str(e)}",
        ) 