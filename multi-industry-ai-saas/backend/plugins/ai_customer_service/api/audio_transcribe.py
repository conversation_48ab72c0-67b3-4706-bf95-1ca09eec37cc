#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
AI客服插件 - 语音转录API
"""

import logging
import uuid
import tempfile
import os
from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form, status
from sqlalchemy.ext.asyncio import AsyncSession

from db.database import get_db
from models.project import Project
from models.user import User
from core.auth import get_current_user
from api.deps import get_current_project
from services.ai.audio_service import AIAudioService

logger = logging.getLogger(__name__)
router = APIRouter()

@router.post(
    "/audio/transcribe",
    summary="语音转录",
    description="将音频文件转换为文本"
)
async def transcribe_audio(
    audio: UploadFile = File(..., description="音频文件"),
    language: Optional[str] = Form("zh", description="语言代码，如 zh、en"),
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """语音转录"""
    try:
        # 验证文件类型
        if not audio.content_type or not audio.content_type.startswith('audio/'):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="请上传音频文件"
            )
        
        # 创建临时文件
        with tempfile.NamedTemporaryFile(delete=False, suffix='.wav') as temp_file:
            # 读取上传的音频数据
            audio_data = await audio.read()
            temp_file.write(audio_data)
            temp_file_path = temp_file.name
        
        try:
            # 调用AI音频服务进行转录
            result = await AIAudioService.transcribe_audio(
                db=db,
                project_id=project.id,
                user_id=current_user.id,
                audio_data=audio_data,
                language=language,
                response_format="text"
            )
            
            # 检查转录结果
            if result.get("success"):
                transcription = result.get("transcription", "")
                
                return {
                    "success": True,
                    "transcription": transcription,
                    "text": transcription,  # 兼容性别名
                    "language": language,
                    "duration": result.get("duration"),
                    "model_used": result.get("model_used"),
                    "message": "语音转录成功"
                }
            else:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=result.get("error", "语音转录失败")
                )
                
        finally:
            # 清理临时文件
            try:
                os.unlink(temp_file_path)
            except Exception as e:
                logger.warning(f"清理临时文件失败: {e}")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"语音转录失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"语音转录失败: {str(e)}"
        )

@router.post(
    "/audio/text-to-speech",
    summary="文本转语音",
    description="将文本转换为语音"
)
async def text_to_speech(
    text: str = Form(..., description="要转换的文本"),
    voice: Optional[str] = Form("alloy", description="语音类型"),
    speed: Optional[float] = Form(1.0, description="语音速度"),
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """文本转语音"""
    try:
        # 调用AI音频服务进行文本转语音
        result = await AIAudioService.text_to_speech(
            db=db,
            project_id=project.id,
            user_id=current_user.id,
            text=text,
            voice=voice,
            speed=speed,
            response_format="mp3"
        )
        
        # 检查转换结果
        if result.get("success"):
            return {
                "success": True,
                "audio_url": result.get("audio_url"),
                "audio_data": result.get("audio_data"),
                "duration": result.get("duration"),
                "model_used": result.get("model_used"),
                "message": "文本转语音成功"
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=result.get("error", "文本转语音失败")
            )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"文本转语音失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"文本转语音失败: {str(e)}"
        ) 