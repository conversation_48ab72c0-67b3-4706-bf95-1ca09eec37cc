#!/usr/bin/env python
# -*- coding: utf-8 -*-

from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
import uuid
from datetime import datetime

# 项目模型
class ProjectBase(BaseModel):
    name: str
    description: Optional[str] = None
    industry_type: str
    max_users: Optional[int] = 5
    max_storage_gb: Optional[int] = 1
    settings: Optional[Dict[str, Any]] = {}
    plugins: Optional[List[str]] = []
    code: Optional[str] = None


class ProjectCreate(ProjectBase):
    tenant_id: Optional[uuid.UUID] = None
    code: Optional[str] = None


class ProjectUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    industry_type: Optional[str] = None
    status: Optional[str] = None
    max_users: Optional[int] = None
    max_storage_gb: Optional[int] = None
    settings: Optional[Dict[str, Any]] = None
    plugins: Optional[List[str]] = None
    code: Optional[str] = None


class ProjectResponse(ProjectBase):
    id: uuid.UUID
    tenant_id: uuid.UUID
    status: str
    created_at: datetime
    updated_at: datetime
    code: str

    class Config:
        model_config = {"from_attributes": True}


class ProjectUserBase(BaseModel):
    user_id: uuid.UUID
    role: str


class ProjectUserCreate(ProjectUserBase):
    pass


class ProjectUserResponse(ProjectUserBase):
    id: uuid.UUID
    project_id: uuid.UUID
    created_at: datetime
    updated_at: datetime

    class Config:
        model_config = {"from_attributes": True}


class ProjectSettingsUpdate(BaseModel):
    """项目设置更新模型"""
    theme: Optional[Dict[str, Any]] = None
    notification: Optional[Dict[str, Any]] = None
    security: Optional[Dict[str, Any]] = None
    general: Optional[Dict[str, Any]] = None
    custom: Optional[Dict[str, Any]] = None
    api_settings: Optional[Dict[str, Any]] = None

    # 允许传入未显式声明的字段，防止前端新增设置丢失
    model_config = {
        "extra": "allow"
    }
