#!/usr/bin/env python
# -*- coding: utf-8 -*-

from fastapi import APIRouter

from api.v1.tenant.plugin_market import router as plugin_market_router
from api.v1.tenant.plugin_purchase import router as plugin_purchase_router
from api.v1.tenant.plugin_orders import router as plugin_orders_router
from api.v1.tenant.data_sharing import router as data_sharing_router

# 导入钉钉插件路由
try:
    from plugins.dingtalk.api import router as dingtalk_plugin_router
except ImportError:
    dingtalk_plugin_router = None

# 创建租户路由器
router = APIRouter()

# 注册插件市场路由
router.include_router(plugin_market_router)

# 注册插件购买路由
router.include_router(plugin_purchase_router, prefix="/plugins", tags=["tenant_plugins"])

# 注册插件订单路由
router.include_router(plugin_orders_router, prefix="/plugins", tags=["tenant_plugin_orders"])

# 注册数据共享路由
router.include_router(data_sharing_router, prefix="/data-sharing", tags=["tenant_data_sharing"])

# 注册钉钉插件路由
if dingtalk_plugin_router:
    router.include_router(dingtalk_plugin_router, prefix="/plugins/dingtalk", tags=["tenant_dingtalk_plugin"])
