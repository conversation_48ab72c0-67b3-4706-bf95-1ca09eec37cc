import React, { useState, useEffect, useRef } from 'react';
import {
  Card,
  Button,
  Space,
  Typography,
  Row,
  Col,
  Tabs,
  message,
  Spin,
  Form,
  Input,
  Switch,
  Select,
  Table,
  Modal,
  Tag,
  Badge,
  Alert,
  Tooltip,
  Descriptions,
  List,
  Avatar,
  Empty,
  InputNumber,
  Divider,
  Progress,
  Statistic
} from 'antd';
import {
  PlusOutlined,
  SaveOutlined,
  SyncOutlined,
  DeleteOutlined,
  EditOutlined,
  SendOutlined,
  RobotOutlined,
  InfoCircleOutlined,
  MessageOutlined,
  UserOutlined,
  FileTextOutlined,
  SettingOutlined,
  DatabaseOutlined,
  HistoryOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  TeamOutlined,
  AuditOutlined,
  BellOutlined,
  CalendarOutlined,
  BookOutlined,
  ShopOutlined,
  CheckSquareOutlined,
  FolderOutlined,
  ExperimentOutlined,
  CopyOutlined,
  RedoOutlined
} from '@ant-design/icons';
import dingtalkApi from './service/dingtalkApi';
import AIChat from './components/AIChat';
import UserManagement from './components/UserManagement';
import DocumentManagement from './components/DocumentManagement';
import TodoTaskManagement from './components/TodoTaskManagement';
import OrganizationManagement from './components/OrganizationManagement';
import AIIntegration from './components/AIIntegration';

import { useAuth } from '../../../../contexts/AuthContext';
import RoleBasedContent from '../../../../components/dashboard/RoleBasedContent';
import { api } from '../../../../services/api';
import request from '../../../../services/request';
import UnifiedApprovalManagement from './components/UnifiedApprovalManagement';
import PluginDocumentation from './components/PluginDocumentation';
import WebhookManagement from './components/EventSubscriptionManagement';

const { Paragraph, Text, Link } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;
const { TextArea } = Input;
const { Title } = Typography;

/**
 * 钉钉机器人插件组件
 */
const DingTalkRobot = () => {
  const { user, hasRole } = useAuth();
  const [loading, setLoading] = useState(true);
  const [capabilities, setCapabilities] = useState(null);
  const [settings, setSettings] = useState(null);

  const [activeTab, setActiveTab] = useState('overview');
  const [form] = Form.useForm();

  // 💥 为按需加载概览数据添加独立的状态
  const [statistics, setStatistics] = useState(null);
  const [statisticsLoading, setStatisticsLoading] = useState(false);
  
  // AI相关状态
  const [aiConfigs, setAiConfigs] = useState([]);
  const [aiAssistants, setAiAssistants] = useState([]); 
  const [aiModels, setAiModels] = useState([]);

  useEffect(() => {
    // 初始加载核心数据
    fetchCoreData();
  }, []);

  // 💥 按需加载逻辑：当标签页切换时触发
  useEffect(() => {
    const projectId = localStorage.getItem('project_id');
    if (!projectId) return;

    // 当切换到"概览"页且数据未加载时，加载统计数据
    if (activeTab === 'overview' && !statistics && capabilities?.features.statistics) {
      const fetchStatistics = async () => {
        setStatisticsLoading(true);
        try {
          const statsRes = await api.get(`/project/${projectId}/plugin/dingtalk/admin/statistics`);
          setStatistics(statsRes.data);
        } catch (error) {
          console.error('获取钉钉统计数据失败:', error);
          message.error('获取统计数据失败');
        } finally {
          setStatisticsLoading(false);
        }
      };
      fetchStatistics();
    }
    // 可在此处为其他标签页添加类似的按需加载逻辑
  }, [activeTab, statistics, capabilities]);

  // 💥 重构为只加载核心数据
  const fetchCoreData = async () => {
    try {
      setLoading(true);
      const projectId = localStorage.getItem('project_id');
      if (!projectId) throw new Error('未找到项目ID');
      
      // 1. 获取用户能力（最核心）
      const capabilitiesRes = await api.get(`/project/${projectId}/plugin/dingtalk/admin/capabilities`);
      setCapabilities(capabilitiesRes.data);
      
      // 2. 获取基础设置
      const settingsRes = await api.get(`/project/${projectId}/plugin/dingtalk/settings`);
      setSettings(settingsRes.settings);

      // 3. 获取AI相关配置和助手列表（为AI集成标签页准备）
      try {
        const aiConfigsRes = await request.get(`/project/${projectId}/ai/configs`);
        setAiConfigs(aiConfigsRes.data || []);
        
        const aiModelsRes = await request.get(`/project/${projectId}/ai/models`);
        setAiModels(aiModelsRes.data || []);
        
        const aiAssistantsRes = await request.get(`/project/${projectId}/ai/assistants`);
        // 💥 修正解析逻辑：从返回对象的data字段中获取数组
        setAiAssistants(aiAssistantsRes.data || []); 

        console.log('钉钉插件 - 获取AI助手:', aiAssistantsRes);
      } catch (error) {
        console.error('获取AI数据失败:', error);
        // 不阻塞主流程
      }
      
    } catch (error) {
      console.error('获取钉钉插件核心数据失败:', error);
      message.error('获取核心数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 渲染概览页面 - 💥 加入加载状态
  const renderOverview = () => (
    <div className="dingtalk-overview">
      <Card title="插件状态" className="status-card">
        <Descriptions column={2}>
          <Descriptions.Item label="用户角色">
            <Tag color="blue">{capabilities?.role_name}</Tag>
          </Descriptions.Item>
          <Descriptions.Item label="权限级别">
            <Tag color={capabilities?.role === 'super_admin' ? 'red' : capabilities?.role === 'admin' ? 'orange' : 'green'}>
              {capabilities?.role === 'super_admin' ? '超级管理员' : 
               capabilities?.role === 'admin' ? '管理员' : '普通用户'}
            </Tag>
          </Descriptions.Item>
          <Descriptions.Item label="可用功能" span={2}>
            <Space wrap>
              {capabilities?.features.user_management && <Tag color="green">用户管理</Tag>}
              {capabilities?.features.ai_chat && <Tag color="blue">AI对话</Tag>}
              {capabilities?.features.message_sending && <Tag color="cyan">消息发送</Tag>}
              {capabilities?.features.knowledge_sync && <Tag color="purple">知识库同步</Tag>}
              {capabilities?.features.ai_integration && <Tag color="orange">AI集成</Tag>}
              {capabilities?.features.organization_management && <Tag color="geekblue">组织架构管理</Tag>}
              {capabilities?.features.todo_task_management && <Tag color="volcano">待办任务管理</Tag>}
              {capabilities?.features.approval_management && <Tag color="magenta">审批流程管理</Tag>}
              {capabilities?.features.document_management && <Tag color="lime">文档管理</Tag>}
              {capabilities?.features.plugin_settings && <Tag color="gold">插件设置</Tag>}
              {capabilities?.features.webhook_management && <Tag color="red">事件订阅</Tag>}
            </Space>
          </Descriptions.Item>
        </Descriptions>
        
        <RoleBasedContent roles={['project_admin', 'tenant_admin']}>
          <Divider />
          <Alert
            message="管理员功能"
            description="您拥有管理员权限，可以配置插件设置、管理Webhook、同步知识库、管理组织架构、待办任务和审批流程等。"
            type="info"
            showIcon
          />
        </RoleBasedContent>
      </Card>

      {statisticsLoading ? (
        <div style={{ textAlign: 'center', padding: '40px 0' }}>
          <Spin />
          <p>正在加载统计数据...</p>
        </div>
      ) : statistics ? (
        <>
      <Row gutter={16} style={{ marginTop: 16 }}>
        <Col span={8}>
          <Card>
            <Statistic
              title="钉钉用户"
                  value={statistics.users_count || 0}
              prefix={<UserOutlined />}
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card>
            <Statistic
              title="通知发送数"
                  value={statistics.notifications_sent || 0}
              prefix={<BellOutlined />}
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card>
            <Statistic
              title="AI对话次数"
                  value={statistics.ai_chats_count || 0}
              prefix={<RobotOutlined />}
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={16} style={{ marginTop: 16 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="待办任务"
                  value={statistics.todo_tasks_count || 0}
              prefix={<CalendarOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="审批流程"
                  value={statistics.approval_instances_count || 0}
              prefix={<AuditOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="文档同步"
                  value={statistics.documents_synced || 0}
              prefix={<FileTextOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="组织架构"
                  value={statistics.departments_count || 0}
              prefix={<TeamOutlined />}
            />
          </Card>
        </Col>
      </Row>
        </>
      ) : (
        <Alert
          message="无法加载统计数据"
          description="请检查网络或联系管理员。"
          type="warning"
          showIcon
          style={{ marginTop: 16 }}
        />
      )}
    </div>
  );

  // 渲染AI集成 - 💥 移除内联实现，改为调用独立的组件
  const renderAIIntegration = () => (
    <AIIntegration
      settings={settings}
      onUpdateSettings={handleSettingsUpdate}
      aiAssistants={aiAssistants}
    />
  );

  // 消息发送功能已移除 - 实用性不高，减少代码冗余

  // 渲染基础设置
  const BasicSettings = () => {
    const [form] = Form.useForm();

    // 当 settings 变化时更新表单
    React.useEffect(() => {
      if (settings) {
        form.setFieldsValue(settings);
      }
    }, [settings, form]);

    // 增强的从项目导入配置功能
    const handleImportConfig = async () => {
      try {
        const projectId = localStorage.getItem('project_id');
        const response = await request.get(`/project/${projectId}/settings/third-party-login`);
        
        console.log('配置导入响应:', response);
        
        // request客户端直接返回数据，不需要.data
        const responseData = response.data || response;
        console.log('解析后的响应数据:', responseData);
        
        if (responseData && responseData.dingtalk) {
          const config = responseData.dingtalk;
          console.log('钉钉配置详情:', {
            config,
            enabled: config.enabled,
            has_app_key: !!(config.app_key || config.app_id),
            has_app_secret: !!config.app_secret,
            validation_result: config.enabled && (config.app_key || config.app_id) && config.app_secret
          });
          
          if (config.enabled && (config.app_key || config.app_id) && config.app_secret) {
            // 更新表单值
            const newSettings = {
              ...settings,
              app_key: config.app_key || config.app_id,
              app_secret: config.app_secret,
              corp_id: config.corp_id || '',
              webhook_url: settings?.webhook_url || ''
            };
            setSettings(newSettings);
            form.setFieldsValue(newSettings);
            message.success('已从项目系统设置导入钉钉配置');
          } else if (!config.enabled) {
            message.warning('钉钉配置未启用，请先在系统设置-第三方登录中启用钉钉登录');
          } else {
            message.warning('钉钉配置不完整，请先在系统设置-第三方登录中完善钉钉应用信息');
          }
        } else {
          message.warning('项目系统设置中未找到钉钉配置，请先在系统设置-第三方登录中配置钉钉应用信息');
        }
      } catch (error) {
        console.error('导入系统配置失败:', error);
        message.error(`导入系统配置失败: ${error.message || '未知错误'}`);
      }
    };

    return (
      <div className="basic-settings">
        <Card title="钉钉插件设置">
          <Alert
            message="配置说明"
            description="您可以从项目的系统设置-第三方登录中导入钉钉应用配置，或者手动填写。"
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />

          <Form
            form={form}
            layout="vertical"
            initialValues={settings}
            onFinish={handleSettingsUpdate}
          >
            <Form.Item
              name="app_key"
              label="应用Key"
              rules={[{ required: true, message: '请输入钉钉应用Key' }]}
              extra="钉钉开放平台应用的AppKey"
            >
              <Input placeholder="请输入钉钉应用Key" />
            </Form.Item>

            <Form.Item
              name="app_secret"
              label="应用Secret"
              rules={[{ required: true, message: '请输入钉钉应用Secret' }]}
              extra="钉钉开放平台应用的AppSecret"
            >
              <Input.Password placeholder="请输入钉钉应用Secret" />
            </Form.Item>

            <Form.Item
              name="corp_id"
              label="企业CorpId"
              rules={[{ required: true, message: '请输入钉钉企业CorpId' }]}
              extra="钉钉企业CorpId，用于事件订阅消息解密，可在钉钉开发者后台-企业信息中获取"
            >
              <Input placeholder="请输入钉钉企业CorpId" />
            </Form.Item>

            <Form.Item
              label="AgentID"
              name="agent_id"
              tooltip="应用 AgentID，用于发送工作通知。在钉钉开发者后台-应用详情-应用凭证页面查看。"
            >
              <Input placeholder="请输入应用的 AgentID (可选)" />
            </Form.Item>

            <Form.Item>
              <Space>
                <Button type="primary" htmlType="submit">
                  保存设置
                </Button>
                <Button onClick={handleImportConfig}>
                  从项目导入配置
                </Button>

              </Space>
            </Form.Item>
          </Form>
        </Card>
      </div>
    );
  };

  // 处理设置更新 - 💥 增强以处理所有设置更新，包括AI直通模式
  const handleSettingsUpdate = async (values) => {
    setLoading(true);
    try {
      const projectId = localStorage.getItem('project_id');
      const response = await api.post(`/project/${projectId}/plugin/dingtalk/settings`, values);
      
      if (response.success) {
        message.success(response.message || '设置保存成功');
        // 更新本地状态
        if (response.settings) {
          setSettings(response.settings);
        }
        // 重新获取最新数据以确保同步
        fetchCoreData();
      } else {
        message.error(response.message || '设置保存失败');
      }
    } catch (error) {
      console.error('设置保存失败:', error);
      message.error('设置保存失败');
    } finally {
      setLoading(false);
    }
  };

  // 消息发送处理函数已移除

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>加载钉钉插件数据...</div>
      </div>
    );
  }

  if (!capabilities) {
    return (
      <Alert
        message="权限不足"
        description="您没有访问钉钉插件的权限。"
        type="error"
        showIcon
      />
    );
  }

  return (
    <div className="dingtalk-plugin">
      <Card title="钉钉深度集成插件" className="plugin-header">
        <Tabs activeKey={activeTab} onChange={setActiveTab}>

          
          <TabPane tab={
            <Space>
              <InfoCircleOutlined />
              概览
            </Space>
          } key="overview">
            {renderOverview()}
          </TabPane>
          
          <TabPane tab={
            <Space>
              <SettingOutlined />
              基础设置
            </Space>
          } key="settings">
            <RoleBasedContent 
              roles={['project_admin', 'tenant_admin']}
              fallback={<Alert message="权限不足" description="您没有权限访问此功能。" type="warning" />}
            >
              <BasicSettings />
            </RoleBasedContent>
          </TabPane>
          
          <TabPane 
            tab={
              <Space>
                <RobotOutlined />
                AI集成
              </Space>
            } 
            key="ai"
            disabled={!capabilities?.features.ai_integration}
          >
            <RoleBasedContent 
              roles={['project_admin', 'tenant_admin']}
              fallback={<Alert message="权限不足" description="您没有权限访问AI集成功能。" type="warning" />}
            >
              {renderAIIntegration()}
            </RoleBasedContent>
          </TabPane>
          
          <TabPane 
            tab={
              <Space>
                <UserOutlined />
                用户管理
              </Space>
            } 
            key="user-management"
            disabled={!capabilities?.features.user_management}
          >
            <RoleBasedContent 
              roles={['project_admin', 'tenant_admin']}
              fallback={<Alert message="权限不足" description="您没有权限访问用户管理功能。" type="warning" />}
            >
              <UserManagement />
            </RoleBasedContent>
          </TabPane>

          <TabPane 
            tab={
              <Space>
                <FileTextOutlined />
                文档管理
              </Space>
            } 
            key="document-management"
          >
            <DocumentManagement />
          </TabPane>

          <TabPane 
            tab={
              <Space>
                <CheckCircleOutlined />
                待办任务
              </Space>
            } 
            key="todo-management"
          >
            <RoleBasedContent 
              roles={['project_admin', 'tenant_admin']}
              fallback={<Alert message="权限不足" description="您没有权限访问待办任务功能。" type="warning" />}
            >
              <TodoTaskManagement />
            </RoleBasedContent>
          </TabPane>

          <TabPane 
            tab={
              <Space>
                <TeamOutlined />
                组织架构
              </Space>
            } 
            key="organization-management"
          >
            <RoleBasedContent 
              roles={['project_admin', 'tenant_admin']}
              fallback={<Alert message="权限不足" description="您没有权限访问组织架构功能。" type="warning" />}
            >
              <OrganizationManagement />
            </RoleBasedContent>
          </TabPane>

          {/* 统一审批管理 - 整合业务审批和通用审批功能 */}
          <TabPane 
            tab={
              <Space>
                <AuditOutlined />
                审批管理
              </Space>
            } 
            key="unified-approval"
          >
            <RoleBasedContent 
              roles={['project_admin', 'tenant_admin']}
              fallback={<Alert message="权限不足" description="您没有权限访问审批管理功能。" type="warning" />}
            >
              <UnifiedApprovalManagement />
            </RoleBasedContent>
          </TabPane>

          <TabPane 
            tab={
              <Space>
                <BellOutlined />
                事件订阅
              </Space>
            } 
            key="event-subscription"
          >
            <RoleBasedContent 
              roles={['project_admin', 'tenant_admin']}
              fallback={<Alert message="权限不足" description="您没有权限访问事件订阅功能。" type="warning" />}
            >
                              <WebhookManagement />
            </RoleBasedContent>
          </TabPane>

          <TabPane 
            tab={
              <Space>
                <RobotOutlined />
                AI对话
              </Space>
            } 
            key="ai_chat"
            disabled={!capabilities?.features.ai_chat}
          >
            <RoleBasedContent 
              roles={['project_admin', 'tenant_admin']}
              fallback={<Alert message="权限不足" description="您没有权限访问AI对话功能。" type="warning" />}
            >
              <AIChat />
            </RoleBasedContent>
          </TabPane>
          
          {/* 消息发送功能已移除 - 实用性不高 */}

          {/* 钉钉插件完整文档 - 整合插件介绍和集成文档 */}
          <TabPane
            tab={
              <Space>
                <BookOutlined />
                插件文档
              </Space>
            }
            key="plugin-docs"
          >
            <PluginDocumentation />
          </TabPane>
        </Tabs>
      </Card>

    </div>
  );
};

export default DingTalkRobot;
