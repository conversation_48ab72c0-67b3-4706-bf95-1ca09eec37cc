import React, { useState, useEffect } from 'react';
import { Card, Table, Tag, Button, Space, Select, Input, Row, Col, Statistic, Spin, Alert, Tooltip, Typography } from 'antd';
import { SearchOutlined, ReloadOutlined, WarningOutlined, ArrowUpOutlined, ArrowDownOutlined } from '@ant-design/icons';
import { useParams } from 'react-router-dom';
import apiService from '../../../services/api';
import { formatDateTime } from '../../../utils/dateTimeUtils';

const { Option } = Select;
const { Title } = Typography;

/**
 * 库存预警组件
 */
const InventoryAlert = () => {
  // 状态
  const [loading, setLoading] = useState(false);
  const [alertData, setAlertData] = useState([]);
  const [summary, setSummary] = useState({
    low_stock_count: 0,
    high_stock_count: 0,
    slow_moving_count: 0,
    total_alert_count: 0
  });
  const [warehouses, setWarehouses] = useState([]);
  const [categories, setCategories] = useState([]);
  const [filters, setFilters] = useState({
    warehouse_id: undefined,
    alert_type: undefined,
    product_category_id: undefined,
    search: ''
  });
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });

  // 获取路由参数
  const { projectId } = useParams();

  // 初始化
  useEffect(() => {
    fetchWarehouses();
    fetchCategories();
    fetchAlertSummary();
  }, [projectId]);

  // 监听筛选条件变化
  useEffect(() => {
    fetchAlertData();
  }, [filters, pagination.current, pagination.pageSize, projectId]);

  // 获取仓库列表
  const fetchWarehouses = async () => {
    try {
      const response = await apiService.project.warehouse.getList(projectId);
      if (response && response.items) {
        setWarehouses(response.items);
      }
    } catch (error) {
      console.error('获取仓库列表失败:', error);
    }
  };

  // 获取产品类别列表
  const fetchCategories = async () => {
    try {
      const response = await apiService.project.product.getCategories(projectId);
      if (response && response.items) {
        setCategories(response.items);
      }
    } catch (error) {
      console.error('获取产品类别列表失败:', error);
    }
  };

  // 获取库存预警摘要
  const fetchAlertSummary = async () => {
    try {
      setLoading(true);
      const response = await apiService.project.inventory.getAlertSummary();
      if (response && response.data) {
        setSummary(response.data);
      }
    } catch (error) {
      console.error('获取库存预警摘要失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 获取库存预警数据
  const fetchAlertData = async () => {
    try {
      setLoading(true);
      const params = {
        warehouse_id: filters.warehouse_id,
        alert_type: filters.alert_type,
        product_category_id: filters.product_category_id,
        skip: (pagination.current - 1) * pagination.pageSize,
        limit: pagination.pageSize
      };

      const response = await apiService.project.inventory.getAlerts(null, params);
      if (response) {
        setAlertData(response.items || []);
        setPagination({
          ...pagination,
          total: response.total || 0
        });
      }
    } catch (error) {
      console.error('获取库存预警数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 处理筛选条件变化
  const handleFilterChange = (key, value) => {
    setFilters({
      ...filters,
      [key]: value
    });
    setPagination({
      ...pagination,
      current: 1
    });
  };

  // 处理表格分页变化
  const handleTableChange = (pagination) => {
    setPagination(pagination);
  };

  // 处理刷新
  const handleRefresh = () => {
    fetchAlertSummary();
    fetchAlertData();
  };

  // 表格列定义
  const columns = [
    {
      title: '产品名称',
      dataIndex: 'product_name',
      key: 'product_name',
      render: (text, record) => (
        <Tooltip title={record.product_sku ? `SKU: ${record.product_sku}` : ''}>
          <span>{text}</span>
        </Tooltip>
      )
    },
    {
      title: '仓库',
      dataIndex: 'warehouse_name',
      key: 'warehouse_name'
    },
    {
      title: '当前库存',
      dataIndex: 'quantity',
      key: 'quantity',
      sorter: (a, b) => a.quantity - b.quantity
    },
    {
      title: '最小库存',
      dataIndex: 'min_quantity',
      key: 'min_quantity'
    },
    {
      title: '最大库存',
      dataIndex: 'max_quantity',
      key: 'max_quantity'
    },
    {
      title: '预警类型',
      dataIndex: 'alert_type',
      key: 'alert_type',
      render: (text) => {
        let color = 'default';
        let icon = null;
        let label = '未知';

        if (text === 'low') {
          color = 'error';
          icon = <ArrowDownOutlined />;
          label = '低库存';
        } else if (text === 'high') {
          color = 'warning';
          icon = <ArrowUpOutlined />;
          label = '高库存';
        } else if (text === 'slow_moving') {
          color = 'default';
          icon = <WarningOutlined />;
          label = '滞销';
        }

        return (
          <Tag color={color} icon={icon}>
            {label}
          </Tag>
        );
      }
    },
    {
      title: '更新时间',
      dataIndex: 'updated_at',
      key: 'updated_at',
      render: (text) => formatDateTime(text)
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="small">
          <Button type="link" size="small" onClick={() => handleViewDetail(record)}>
            查看详情
          </Button>
        </Space>
      )
    }
  ];

  // 处理查看详情
  const handleViewDetail = (record) => {
    // 跳转到库存详情页面
    window.open(`/project/${projectId}/inventory/${record.id}`, '_blank');
  };

  return (
    <div className="inventory-alert-container">
      <Card>
        <Title level={4}>库存预警</Title>

        {/* 统计卡片 */}
        <Row gutter={16} style={{ marginBottom: 16 }}>
          <Col span={6}>
            <Card>
              <Statistic
                title="总预警数"
                value={summary.total_alert_count}
                prefix={<WarningOutlined />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="低库存预警"
                value={summary.low_stock_count}
                valueStyle={{ color: '#cf1322' }}
                prefix={<ArrowDownOutlined />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="高库存预警"
                value={summary.high_stock_count}
                valueStyle={{ color: '#faad14' }}
                prefix={<ArrowUpOutlined />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="滞销商品"
                value={summary.slow_moving_count}
                valueStyle={{ color: '#8c8c8c' }}
                prefix={<WarningOutlined />}
              />
            </Card>
          </Col>
        </Row>

        {/* 筛选条件 */}
        <Row gutter={16} style={{ marginBottom: 16 }}>
          <Col span={5}>
            <Select
              placeholder="选择仓库"
              style={{ width: '100%' }}
              allowClear
              onChange={(value) => handleFilterChange('warehouse_id', value)}
              value={filters.warehouse_id}
            >
              {warehouses.map(warehouse => (
                <Option key={warehouse.id} value={warehouse.id}>{warehouse.name}</Option>
              ))}
            </Select>
          </Col>
          <Col span={5}>
            <Select
              placeholder="预警类型"
              style={{ width: '100%' }}
              allowClear
              onChange={(value) => handleFilterChange('alert_type', value)}
              value={filters.alert_type}
            >
              <Option value="low">低库存</Option>
              <Option value="high">高库存</Option>
              <Option value="slow_moving">滞销</Option>
            </Select>
          </Col>
          <Col span={5}>
            <Select
              placeholder="产品类别"
              style={{ width: '100%' }}
              allowClear
              onChange={(value) => handleFilterChange('product_category_id', value)}
              value={filters.product_category_id}
            >
              {categories.map(category => (
                <Option key={category.id} value={category.id}>{category.name}</Option>
              ))}
            </Select>
          </Col>
          <Col span={7}>
            <Input
              placeholder="搜索产品名称"
              prefix={<SearchOutlined />}
              onChange={(e) => handleFilterChange('search', e.target.value)}
              value={filters.search}
              allowClear
            />
          </Col>
          <Col span={2}>
            <Button
              type="primary"
              icon={<ReloadOutlined />}
              onClick={handleRefresh}
            >
              刷新
            </Button>
          </Col>
        </Row>

        {/* 预警表格 */}
        <Spin spinning={loading}>
          {alertData.length > 0 ? (
            <Table
              columns={columns}
              dataSource={alertData}
              rowKey="id"
              pagination={pagination}
              onChange={handleTableChange}
            />
          ) : (
            <Alert
              message="暂无预警数据"
              description="当前没有符合条件的库存预警数据。"
              type="info"
              showIcon
            />
          )}
        </Spin>
      </Card>
    </div>
  );
};

export default InventoryAlert;
