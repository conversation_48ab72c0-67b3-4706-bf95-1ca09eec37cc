import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Modal,
  Form,
  Input,
  Select,
  DatePicker,
  Spin,
  Tabs,
  Row,
  Col,
  Statistic,
  Typography,
  Divider,
  Descriptions,
  Steps,
  message
} from 'antd';
import {
  ExclamationCircleOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  SearchOutlined,
  FileTextOutlined,
  EyeOutlined,
  ReloadOutlined,
  Bar<PERSON>hartOutlined,
  PieChartOutlined,
  CheckOutlined,
  CloseOutlined
} from '@ant-design/icons';
import dayjs from "dayjs";
import apiService from '../../../../services/api';

const { Option } = Select;
const { TextArea } = Input;
const { RangePicker } = DatePicker;
const { Title, Text } = Typography;
const { Step } = Steps;

// 状态标签颜色映射
const statusColors = {
  pending: 'gold',
  approved: 'green',
  rejected: 'red',
  withdrawn: 'default'
};

// 状态文本映射
const statusTexts = {
  pending: '待审核',
  approved: '已通过',
  rejected: '已拒绝',
  withdrawn: '已撤回'
};

// 报损原因映射
const reasonTexts = {
  expired: '过期',
  damaged: '损坏',
  lost: '丢失',
  quality: '质量问题',
  other: '其他'
};

/**
 * 报损审核组件 - 财务部门版本
 * 主要用于月度汇总审核和数据分析
 */
const FinanceLossApproval = () => {
  // 状态
  const [loading, setLoading] = useState(false);
  const [lossRecords, setLossRecords] = useState([]);
  const [lossStatistics, setLossStatistics] = useState({});
  const [stores, setStores] = useState([]);
  const [selectedRecord, setSelectedRecord] = useState(null);
  const [detailVisible, setDetailVisible] = useState(false);
  const [approveVisible, setApproveVisible] = useState(false);
  const [rejectVisible, setRejectVisible] = useState(false);
  const [batchApproveVisible, setBatchApproveVisible] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [form] = Form.useForm();
  const [filterForm] = Form.useForm();
  const [activeTab, setActiveTab] = useState('detail');
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });
  const [searchParams, setSearchParams] = useState({
    status: 'pending',
    search: '',
    dateRange: null,
    storeId: null
  });

  // 初始化
  useEffect(() => {
    fetchStores();
  }, []);

  // 自动监听参数变化刷新报损列表
  useEffect(() => {
    fetchLossRecords();
    fetchLossStatistics();
    // eslint-disable-next-line
  }, [searchParams, pagination.current, pagination.pageSize, activeTab]);

  // 获取门店列表
  const fetchStores = async () => {
    try {
      const response = await apiService.project.store.getList();
      if (response && response.items) {
        setStores(response.items);
      }
    } catch (error) {
      console.error('获取门店列表失败:', error);
      message.error('获取门店列表失败');
    }
  };

  // 获取报损记录
  const fetchLossRecords = async () => {
    setLoading(true);
    try {
      // 构建查询参数
      const params = {
        skip: (pagination.current - 1) * pagination.pageSize,
        limit: pagination.pageSize,
        status: searchParams.status,
        search: searchParams.search,
        start_date: searchParams.dateRange && searchParams.dateRange[0] ? searchParams.dateRange[0].format('YYYY-MM-DD') : undefined,
        end_date: searchParams.dateRange && searchParams.dateRange[1] ? searchParams.dateRange[1].format('YYYY-MM-DD') : undefined,
        store_id: searchParams.storeId
      };

      // 调用API获取数据
      const response = await apiService.project.loss.getList(params);

      if (response && response.items) {
        // 处理数据，根据当前标签页进行不同处理
        let processedItems = response.items;

        // 如果是月度汇总标签页，需要对数据进行分组和汇总
        if (activeTab === 'monthly') {
          // 按月份和门店分组
          const groupedData = {};

          response.items.forEach(item => {
            // 提取月份，如果没有loss_date，则使用created_at
            const date = item.loss_date || item.created_at;
            const month = dayjs(date).format('YYYY-MM');
            const storeId = item.store_id;
            const storeName = item.store_name;

            // 创建分组键
            const key = `${month}-${storeId}`;

            if (!groupedData[key]) {
              groupedData[key] = {
                id: `monthly-${key}`,
                month,
                store_id: storeId,
                store_name: storeName,
                total_amount: 0,
                total_count: 0,
                pending_count: 0,
                approved_count: 0,
                rejected_count: 0,
                status: 'approved' // 默认状态
              };
            }

            // 累加金额和数量
            groupedData[key].total_amount += item.total_amount || 0;
            groupedData[key].total_count += 1;

            // 根据状态累加对应的计数
            if (item.status === 'pending') {
              groupedData[key].pending_count += 1;
              // 如果有待审核的记录，整体状态为待审核
              groupedData[key].status = 'pending';
            } else if (item.status === 'approved') {
              groupedData[key].approved_count += 1;
            } else if (item.status === 'rejected') {
              groupedData[key].rejected_count += 1;
            }
          });

          // 将分组数据转换为数组
          processedItems = Object.values(groupedData);
        }

        setLossRecords(processedItems);
        setPagination({
          ...pagination,
          total: activeTab === 'monthly' ? processedItems.length : response.total
        });
      } else {
        setLossRecords([]);
        setPagination({
          ...pagination,
          total: 0
        });
      }
    } catch (error) {
      console.error('获取报损记录失败:', error);
      message.error('获取报损记录失败: ' + (error.message || '未知错误'));
      setLossRecords([]);
      setPagination({
        ...pagination,
        total: 0
      });
    } finally {
      setLoading(false);
    }
  };

  // 获取报损统计数据
  const fetchLossStatistics = async () => {
    try {
      // 构建查询参数
      const params = {
        start_date: searchParams.dateRange && searchParams.dateRange[0] ? searchParams.dateRange[0].format('YYYY-MM-DD') : undefined,
        end_date: searchParams.dateRange && searchParams.dateRange[1] ? searchParams.dateRange[1].format('YYYY-MM-DD') : undefined,
        store_id: searchParams.storeId
      };

      // 调用API获取数据
      const response = await apiService.project.loss.getStatistics(params);

      if (response) {
        setLossStatistics(response);
      } else {
        // 如果没有数据，设置空的统计数据
        setLossStatistics({
          total_amount: 0,
          total_count: 0,
          pending_amount: 0,
          pending_count: 0,
          approved_amount: 0,
          approved_count: 0,
          rejected_amount: 0,
          rejected_count: 0,
          by_store: [],
          by_reason: [],
          by_month: [],
          by_product: [],
          by_category: []
        });
      }
    } catch (error) {
      console.error('获取报损统计数据失败:', error);
      message.error('获取报损统计数据失败: ' + (error.message || '未知错误'));
      // 设置空的统计数据
      setLossStatistics({
        total_amount: 0,
        total_count: 0,
        pending_amount: 0,
        pending_count: 0,
        approved_amount: 0,
        approved_count: 0,
        rejected_amount: 0,
        rejected_count: 0,
        by_store: [],
        by_reason: [],
        by_month: [],
        by_product: [],
        by_category: []
      });
    }
  };

  // 生成模拟数据
  const generateMockData = () => {
    const items = [];
    let total = 0;

    // 根据当前选择的标签页生成不同的数据
    const isMonthly = activeTab === 'monthly';

    if (isMonthly) {
      // 月度汇总数据 - 使用更可靠的方式生成唯一数据
      // 创建一个包含所有可能的月份和门店组合
      const months = [0, 1, 2, 3, 4, 5].map(m => dayjs().subtract(m, 'months').format('YYYY-MM'));
      const stores = [
        { id: '1', name: '总店' },
        { id: '2', name: '分店1' },
        { id: '3', name: '分店2' }
      ];

      // 为每个月份和门店组合生成一条记录
      months.forEach((month, monthIndex) => {
        stores.forEach((store, storeIndex) => {
          const uniqueId = `monthly-${month}-${store.id}-${monthIndex}-${storeIndex}`;
          const status = (monthIndex + storeIndex) % 3 === 0 ? 'pending' : 'approved';

          items.push({
            id: uniqueId,
            month,
            store_id: store.id,
            store_name: store.name,
            total_amount: Math.floor(Math.random() * 10000) + 1000,
            total_count: Math.floor(Math.random() * 100) + 10,
            pending_count: Math.floor(Math.random() * 10),
            approved_count: Math.floor(Math.random() * 50) + 10,
            rejected_count: Math.floor(Math.random() * 5),
            status,
            created_by: '系统',
            created_at: dayjs().subtract(monthIndex, 'days').format('YYYY-MM-DD HH:mm:ss'),
            updated_at: dayjs().subtract(monthIndex, 'days').format('YYYY-MM-DD HH:mm:ss'),
          });
        });
      });

      // 设置总数为实际生成的记录数
      total = items.length;
    } else {
      // 明细数据 - 保持原来的生成逻辑
      for (let i = 0; i < 10; i++) {
        const id = `loss-${i + 1 + (pagination.current - 1) * pagination.pageSize}`;
        const storeId = i % 3 === 0 ? '1' : (i % 3 === 1 ? '2' : '3');
        const storeName = i % 3 === 0 ? '总店' : (i % 3 === 1 ? '分店1' : '分店2');
        const status = i % 4 === 0 ? 'pending' : (i % 4 === 1 ? 'approved' : (i % 4 === 2 ? 'rejected' : 'withdrawn'));
        const type = i % 2 === 0 ? 'store' : 'purchase';

        items.push({
          id,
          store_id: storeId,
          store_name: storeName,
          product_name: `商品 ${i + 1}`,
          product_code: `P${i + 1}`,
          loss_date: dayjs().subtract(i, 'days').format('YYYY-MM-DD'),
          quantity: Math.floor(Math.random() * 10) + 1,
          unit_price: Math.floor(Math.random() * 100) + 10,
          total_amount: Math.floor(Math.random() * 1000) + 100,
          type,
          status,
          reason: i % 5 === 0 ? 'expired' : (i % 5 === 1 ? 'damaged' : (i % 5 === 2 ? 'lost' : (i % 5 === 3 ? 'quality' : 'other'))),
          creator_name: '张三',
          created_at: dayjs().subtract(i, 'days').format('YYYY-MM-DD HH:mm:ss'),
          updated_at: dayjs().subtract(i, 'days').format('YYYY-MM-DD HH:mm:ss'),
        });
      }

      // 设置总数为实际生成的记录数
      total = items.length;
    }

    return { items, total };
  };

  // 生成模拟统计数据
  const generateMockStatistics = () => {
    return {
      total_amount: 25680.50,
      total_count: 156,
      pending_amount: 3450.20,
      pending_count: 23,
      approved_amount: 20120.30,
      approved_count: 125,
      rejected_amount: 2110.00,
      rejected_count: 8,
      by_store: [
        { store_name: '总店', amount: 12500.50, count: 75 },
        { store_name: '分店1', amount: 8200.00, count: 48 },
        { store_name: '分店2', amount: 4980.00, count: 33 }
      ],
      by_reason: [
        { reason: 'expired', reason_name: '过期', amount: 10240.20, count: 62 },
        { reason: 'damaged', reason_name: '损坏', amount: 8520.10, count: 51 },
        { reason: 'lost', reason_name: '丢失', amount: 3650.00, count: 22 },
        { reason: 'quality', reason_name: '质量问题', amount: 2150.20, count: 13 },
        { reason: 'other', reason_name: '其他', amount: 1120.00, count: 8 }
      ],
      by_month: [
        { month: '2023-01', amount: 4250.30, count: 26 },
        { month: '2023-02', amount: 3850.20, count: 23 },
        { month: '2023-03', amount: 4120.50, count: 25 },
        { month: '2023-04', amount: 4520.80, count: 27 },
        { month: '2023-05', amount: 4780.20, count: 29 },
        { month: '2023-06', amount: 4158.50, count: 26 }
      ],
      // 按商品统计 (TOP5)
      by_product: [
        { product_id: '1', product_name: '鲜牛奶 1L', amount: 3678.90, count: 123 },
        { product_id: '2', product_name: '面包', amount: 2567.89, count: 98 },
        { product_id: '3', product_name: '酸奶 500ml', amount: 1456.78, count: 76 },
        { product_id: '4', product_name: '巧克力', amount: 1345.67, count: 54 },
        { product_id: '5', product_name: '饼干', amount: 1234.56, count: 32 }
      ],
      // 按分类统计
      by_category: [
        { category_id: '1', category_name: '乳制品', amount: 6345.67, count: 234 },
        { category_id: '2', category_name: '烘焙食品', amount: 5876.54, count: 187 },
        { category_id: '3', category_name: '零食', amount: 4654.32, count: 156 },
        { category_id: '4', category_name: '饮料', amount: 3432.10, count: 98 },
        { category_id: '5', category_name: '其他', amount: 2210.98, count: 67 }
      ]
    };
  };

  // 查看详情
  const handleViewDetail = async (record) => {
    try {
      // 检查是否是月度汇总记录
      if (record.id && record.id.toString().startsWith('monthly-')) {
        // 对于月度汇总记录，显示汇总信息而不是调用详情API
        setSelectedRecord({
          ...record,
          is_monthly_summary: true,
          title: `${record.month} ${record.store_name} 报损汇总`,
          summary: {
            total_amount: record.total_amount,
            total_count: record.total_count,
            pending_count: record.pending_count,
            approved_count: record.approved_count,
            rejected_count: record.rejected_count
          }
        });
        setDetailVisible(true);
      } else {
        // 获取详细信息
        const detailData = await apiService.project.loss.getDetail(record.id);
        setSelectedRecord(detailData);
        setDetailVisible(true);
      }
    } catch (error) {
      console.error('获取报损详情失败:', error);
      message.error('获取报损详情失败: ' + (error.message || '未知错误'));
    }
  };

  // 审核通过
  const handleApprove = async () => {
    if (!selectedRecord) return;
    setLoading(true);
    try {
      // 调用API更新报损记录状态
      await apiService.project.loss.updateStatus(selectedRecord.id, {
        status: 'approved'
      });

      message.success('审核通过成功');
      setApproveVisible(false);
      setSelectedRecord(null);
      fetchLossRecords();
      fetchLossStatistics();
    } catch (error) {
      console.error('审核通过失败:', error);
      message.error('审核通过失败: ' + (error.message || '未知错误'));
    } finally {
      setLoading(false);
    }
  };

  // 审核拒绝
  const handleReject = async () => {
    if (!selectedRecord) return;
    try {
      const values = await form.validateFields();
      setLoading(true);

      // 调用API更新报损记录状态
      await apiService.project.loss.updateStatus(selectedRecord.id, {
        status: 'rejected',
        reject_reason: values.rejectReason
      });

      message.success('审核拒绝成功');
      setRejectVisible(false);
      setSelectedRecord(null);
      form.resetFields();
      fetchLossRecords();
      fetchLossStatistics();
    } catch (error) {
      console.error('审核拒绝失败:', error);
      if (error.errorFields) {
        // 表单验证错误
        return;
      }
      message.error('审核拒绝失败: ' + (error.message || '未知错误'));
    } finally {
      setLoading(false);
    }
  };

  // 批量审核通过
  const handleBatchApprove = async () => {
    if (selectedRowKeys.length === 0) return;
    setLoading(true);
    try {
      // 批量处理每个选中的记录
      const promises = selectedRowKeys.map(id =>
        apiService.project.loss.updateStatus(id, { status: 'approved' })
      );

      // 等待所有请求完成
      await Promise.all(promises);

      message.success(`成功批量审核通过 ${selectedRowKeys.length} 条记录`);
      setBatchApproveVisible(false);
      setSelectedRowKeys([]);
      fetchLossRecords();
      fetchLossStatistics();
    } catch (error) {
      console.error('批量审核通过失败:', error);
      message.error('批量审核通过失败: ' + (error.message || '未知错误'));
    } finally {
      setLoading(false);
    }
  };

  // 处理搜索表单提交
  const handleSearch = (values) => {
    const { status, storeId, dateRange, search } = values;
    setSearchParams({ status, storeId, dateRange, search });
    setPagination(prev => ({ ...prev, current: 1 }));
  };

  // 重置搜索表单
  const handleReset = () => {
    filterForm.resetFields();
    const defaultParams = {
      status: 'pending',
      search: '',
      dateRange: null,
      storeId: null
    };
    setSearchParams(defaultParams);
    setPagination(prev => ({ ...prev, current: 1 }));
  };

  // 处理表格分页变化
  const handleTableChange = (pagination, filters, sorter) => {
    setPagination({
      ...pagination,
      current: pagination.current,
      pageSize: pagination.pageSize
    });
  };

  // 处理标签页切换
  const handleTabChange = (key) => {
    setActiveTab(key);
    setSelectedRowKeys([]);
  };

  // 行选择配置
  const rowSelection = {
    selectedRowKeys,
    onChange: (keys) => setSelectedRowKeys(keys),
    getCheckboxProps: (record) => ({
      disabled: record.status !== 'pending',
    }),
  };

  // 搜索表单
  const SearchForm = () => (
    <Form
      layout="inline"
      form={filterForm}
      initialValues={searchParams}
      onFinish={handleSearch}
      style={{ marginBottom: 16 }}
    >
      <Form.Item name="status" label="状态">
        <Select style={{ width: 120 }}>
          <Option value="">全部</Option>
          <Option value="pending">待审核</Option>
          <Option value="approved">已通过</Option>
          <Option value="rejected">已拒绝</Option>
          <Option value="withdrawn">已撤回</Option>
        </Select>
      </Form.Item>

      <Form.Item name="storeId" label="门店">
        <Select style={{ width: 150 }} allowClear placeholder="选择门店">
          <Option value="">全部门店</Option>
          {stores.map(store => (
            <Option key={store.id} value={store.id}>{store.name}</Option>
          ))}
        </Select>
      </Form.Item>

      <Form.Item name="dateRange" label="日期范围">
        <RangePicker
          style={{ width: 240 }}
          defaultValue={[dayjs().startOf('month'), dayjs().endOf('month')]}
          format="YYYY-MM-DD"
        />
      </Form.Item>

      <Form.Item name="search" label="搜索">
        <Input
          placeholder="商品名称/报损原因"
          prefix={<SearchOutlined />}
          style={{ width: 200 }}
        />
      </Form.Item>

      <Form.Item>
        <Button type="primary" htmlType="submit">
          搜索
        </Button>
      </Form.Item>

      <Form.Item>
        <Button onClick={handleReset}>
          重置
        </Button>
      </Form.Item>
    </Form>
  );

  // 月度汇总表格列
  const monthlyColumns = [
    {
      title: '月份',
      dataIndex: 'month',
      key: 'month',
      width: 120,
    },
    {
      title: '门店',
      dataIndex: 'store_name',
      key: 'store_name',
      width: 150,
    },
    {
      title: '报损总金额(元)',
      dataIndex: 'total_amount',
      key: 'total_amount',
      width: 150,
      render: (text) => text.toFixed(2),
    },
    {
      title: '报损总数量',
      dataIndex: 'total_count',
      key: 'total_count',
      width: 120,
    },
    {
      title: '待审核数量',
      dataIndex: 'pending_count',
      key: 'pending_count',
      width: 120,
    },
    {
      title: '已通过数量',
      dataIndex: 'approved_count',
      key: 'approved_count',
      width: 120,
    },
    {
      title: '已拒绝数量',
      dataIndex: 'rejected_count',
      key: 'rejected_count',
      width: 120,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status) => (
        <Tag color={statusColors[status] || 'default'}>
          {statusTexts[status] || status}
        </Tag>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 180,
      render: (_, record) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => handleViewDetail(record)}
          >
            详情
          </Button>

          {record.status === 'pending' && (
            <Button
              type="link"
              size="small"
              icon={<CheckOutlined />}
              style={{ color: '#52c41a' }}
              onClick={() => {
                setSelectedRecord(record);
                setApproveVisible(true);
              }}
            >
              通过
            </Button>
          )}
        </Space>
      ),
    },
  ];

  // 明细表格列
  const detailColumns = [
    {
      title: '门店',
      dataIndex: 'store_name',
      key: 'store_name',
      width: 150,
    },
    {
      title: '商品',
      dataIndex: 'product_name',
      key: 'product_name',
      width: 200,
      render: (text, record) => (
        <span>
          {text}
          <br />
          <Text type="secondary">{record.product_code}</Text>
        </span>
      ),
    },
    {
      title: '数量',
      dataIndex: 'quantity',
      key: 'quantity',
      width: 100,
    },
    {
      title: '金额(元)',
      dataIndex: 'total_amount',
      key: 'total_amount',
      width: 120,
      render: (text) => text.toFixed(2),
    },
    {
      title: '报损原因',
      dataIndex: 'reason',
      key: 'reason',
      width: 120,
      render: (text) => reasonTexts[text] || text,
    },
    {
      title: '报损日期',
      dataIndex: 'loss_date',
      key: 'loss_date',
      width: 120,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status) => (
        <Tag color={statusColors[status] || 'default'}>
          {statusTexts[status] || status}
        </Tag>
      ),
    },
    {
      title: '申请人',
      dataIndex: 'creator_name',
      key: 'creator_name',
      width: 100,
    },
    {
      title: '操作',
      key: 'action',
      width: 180,
      render: (_, record) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => handleViewDetail(record)}
          >
            详情
          </Button>

          {record.status === 'pending' && (
            <>
              <Button
                type="link"
                size="small"
                icon={<CheckOutlined />}
                style={{ color: '#52c41a' }}
                onClick={() => {
                  setSelectedRecord(record);
                  setApproveVisible(true);
                }}
              >
                通过
              </Button>
              <Button
                type="link"
                danger
                size="small"
                icon={<CloseOutlined />}
                onClick={() => {
                  setSelectedRecord(record);
                  form.resetFields();
                  setRejectVisible(true);
                }}
              >
                拒绝
              </Button>
            </>
          )}
        </Space>
      ),
    },
  ];

  // 渲染统计卡片
  const renderStatisticCards = () => (
    <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
      <Col span={6}>
        <Card>
          <Statistic
            title="报损总金额"
            value={lossStatistics.total_amount}
            precision={2}
            suffix="元"
          />
        </Card>
      </Col>
      <Col span={6}>
        <Card>
          <Statistic
            title="报损总数量"
            value={lossStatistics.total_count}
            suffix="件"
          />
        </Card>
      </Col>
      <Col span={6}>
        <Card>
          <Statistic
            title="待审核金额"
            value={lossStatistics.pending_amount}
            precision={2}
            suffix="元"
            valueStyle={{ color: '#faad14' }}
          />
        </Card>
      </Col>
      <Col span={6}>
        <Card>
          <Statistic
            title="待审核数量"
            value={lossStatistics.pending_count}
            suffix="件"
            valueStyle={{ color: '#faad14' }}
          />
        </Card>
      </Col>
    </Row>
  );

  return (
    <div className="loss-approval-container">
      <div style={{ marginBottom: 16 }}>
        <SearchForm />
      </div>

      {lossStatistics && renderStatisticCards()}

      <Tabs
        activeKey={activeTab}
        onChange={handleTabChange}
        items={[
          {
            key: "detail",
            label: "明细数据",
            children: (
              <>
                <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between' }}>
                  <div>
                    <Title level={5}>报损明细数据</Title>
                  </div>
                  <div>
                    {selectedRowKeys.length > 0 && (
                      <Button
                        type="primary"
                        onClick={() => setBatchApproveVisible(true)}
                        style={{ marginRight: 8 }}
                      >
                        批量审核通过 ({selectedRowKeys.length})
                      </Button>
                    )}
                    <Button
                      icon={<ReloadOutlined />}
                      onClick={fetchLossRecords}
                    >
                      刷新
                    </Button>
                  </div>
                </div>

                <Table
                  rowSelection={rowSelection}
                  columns={detailColumns}
                  dataSource={lossRecords}
                  rowKey="id"
                  pagination={pagination}
                  onChange={handleTableChange}
                  loading={loading}
                  scroll={{ x: 1500 }}
                />
              </>
            )
          },
          {
            key: "monthly",
            label: "月度汇总",
            children: (
              <>
                <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between' }}>
                  <div>
                    <Title level={5}>月度报损汇总</Title>
                  </div>
                  <div>
                    <Button
                      icon={<ReloadOutlined />}
                      onClick={fetchLossRecords}
                    >
                      刷新
                    </Button>
                  </div>
                </div>

                {/* 月度汇总统计卡片 */}
                {searchParams.dateRange && searchParams.dateRange[0] && (
                  <Card style={{ marginBottom: 16 }}>
                    <Row gutter={[16, 16]}>
                      <Col span={8}>
                        <Statistic
                          title={`${searchParams.dateRange[0].format('YYYY年MM月')}报损总金额`}
                          value={lossStatistics.total_amount}
                          precision={2}
                          suffix="元"
                        />
                      </Col>
                      <Col span={8}>
                        <Statistic
                          title={`${searchParams.dateRange[0].format('YYYY年MM月')}报损总数量`}
                          value={lossStatistics.total_count}
                          suffix="件"
                        />
                      </Col>
                      <Col span={8}>
                        <Statistic
                          title="待审核数量"
                          value={lossStatistics.pending_count}
                          suffix="件"
                          valueStyle={{ color: '#faad14' }}
                        />
                      </Col>
                    </Row>
                  </Card>
                )}

                <Table
                  columns={monthlyColumns}
                  dataSource={lossRecords}
                  rowKey="id"
                  pagination={pagination}
                  onChange={handleTableChange}
                  loading={loading}
                  scroll={{ x: 1200 }}
                />
              </>
            )
          },
          {
            key: "analysis",
            label: "数据分析",
            children: (
              <>
                <div style={{ marginBottom: 16 }}>
                  <Title level={5}>报损数据分析</Title>
                </div>

                <Row gutter={[16, 16]}>
                  <Col span={12}>
                    <Card title="门店报损分布">
                      <Table
                        columns={[
                          { title: '门店', dataIndex: 'store_name', key: 'store_name' },
                          { title: '报损金额(元)', dataIndex: 'amount', key: 'amount', render: (text) => text.toFixed(2) },
                          { title: '报损数量', dataIndex: 'count', key: 'count' },
                          { title: '占比', key: 'percentage', render: (_, record) => {
                            const total = lossStatistics.by_store.reduce((sum, item) => sum + item.amount, 0);
                            return ((record.amount / total) * 100).toFixed(2) + '%';
                          }},
                        ]}
                        dataSource={lossStatistics.by_store || []}
                        rowKey="store_name"
                        pagination={false}
                      />
                    </Card>
                  </Col>
                  <Col span={12}>
                    <Card title="报损原因分布">
                      <Table
                        columns={[
                          { title: '报损原因', dataIndex: 'reason_name', key: 'reason_name' },
                          { title: '报损金额(元)', dataIndex: 'amount', key: 'amount', render: (text) => text.toFixed(2) },
                          { title: '报损数量', dataIndex: 'count', key: 'count' },
                          { title: '占比', key: 'percentage', render: (_, record) => {
                            const total = lossStatistics.by_reason.reduce((sum, item) => sum + item.amount, 0);
                            return ((record.amount / total) * 100).toFixed(2) + '%';
                          }},
                        ]}
                        dataSource={lossStatistics.by_reason || []}
                        rowKey="reason"
                        pagination={false}
                      />
                    </Card>
                  </Col>
                </Row>

                <div style={{ marginTop: 16 }}>
                  <Card title="月度报损趋势">
                    <Table
                      columns={[
                        { title: '月份', dataIndex: 'month', key: 'month' },
                        { title: '报损金额(元)', dataIndex: 'amount', key: 'amount', render: (text) => text.toFixed(2) },
                        { title: '报损数量', dataIndex: 'count', key: 'count' },
                        { title: '环比', key: 'mom', render: (_, record, index) => {
                          if (index === 0) return '-';
                          const prevAmount = lossStatistics.by_month[index - 1].amount;
                          const change = ((record.amount - prevAmount) / prevAmount) * 100;
                          return (
                            <span style={{ color: change >= 0 ? '#f5222d' : '#52c41a' }}>
                              {change >= 0 ? '+' : ''}{change.toFixed(2)}%
                            </span>
                          );
                        }},
                      ]}
                      dataSource={lossStatistics.by_month || []}
                      rowKey="month"
                      pagination={false}
                    />
                  </Card>
                </div>

                <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
                  <Col span={12}>
                    <Card title="报损TOP5商品">
                      <Table
                        columns={[
                          { title: '排名', key: 'rank', render: (_, __, index) => index + 1 },
                          { title: '商品名称', dataIndex: 'product_name', key: 'product_name' },
                          { title: '报损金额(元)', dataIndex: 'amount', key: 'amount', render: (text) => text.toFixed(2) },
                          { title: '报损数量', dataIndex: 'count', key: 'count' },
                        ]}
                        dataSource={lossStatistics.by_product || []}
                        rowKey="product_id"
                        pagination={false}
                      />
                    </Card>
                  </Col>
                  <Col span={12}>
                    <Card title="商品分类报损分布">
                      <Table
                        columns={[
                          { title: '商品分类', dataIndex: 'category_name', key: 'category_name' },
                          { title: '报损金额(元)', dataIndex: 'amount', key: 'amount', render: (text) => text.toFixed(2) },
                          { title: '报损数量', dataIndex: 'count', key: 'count' },
                          { title: '占比', key: 'percentage', render: (_, record) => {
                            const total = lossStatistics.by_category.reduce((sum, item) => sum + item.amount, 0);
                            return ((record.amount / total) * 100).toFixed(2) + '%';
                          }},
                        ]}
                        dataSource={lossStatistics.by_category || []}
                        rowKey="category_id"
                        pagination={false}
                      />
                    </Card>
                  </Col>
                </Row>
              </>
            )
          }
        ]}
      />

      {/* 详情弹窗 */}
      <Modal
        title="报损详情"
        open={detailVisible}
        onCancel={() => setDetailVisible(false)}
        width={800}
        footer={[
          <Button key="close" onClick={() => setDetailVisible(false)}>
            关闭
          </Button>,
          selectedRecord && selectedRecord.status === 'pending' && (
            <React.Fragment key="action-buttons">
              <Button
                key="approve"
                type="primary"
                onClick={() => {
                  setDetailVisible(false);
                  setApproveVisible(true);
                }}
              >
                审核通过
              </Button>
              <Button
                key="reject"
                danger
                onClick={() => {
                  setDetailVisible(false);
                  form.resetFields();
                  setRejectVisible(true);
                }}
              >
                审核拒绝
              </Button>
            </React.Fragment>
          ),
        ]}
      >
        {selectedRecord && (
          <div>
            {selectedRecord.is_monthly_summary ? (
              // 月度汇总详情
              <div>
                <Descriptions title={selectedRecord.title || "月度汇总信息"} bordered column={2}>
                  <Descriptions.Item label="月份">{selectedRecord.month}</Descriptions.Item>
                  <Descriptions.Item label="门店">{selectedRecord.store_name}</Descriptions.Item>
                  <Descriptions.Item label="报损总金额" span={2}>
                    {selectedRecord.total_amount ? selectedRecord.total_amount.toFixed(2) : '0.00'} 元
                  </Descriptions.Item>
                  <Descriptions.Item label="报损总数量" span={2}>
                    {selectedRecord.total_count || 0} 件
                  </Descriptions.Item>
                </Descriptions>

                <div style={{ marginTop: 24 }}>
                  <Divider orientation="left">状态分布</Divider>
                  <Row gutter={[16, 16]}>
                    <Col span={8}>
                      <Card>
                        <Statistic
                          title="待审核数量"
                          value={selectedRecord.pending_count || 0}
                          suffix="件"
                          valueStyle={{ color: '#faad14' }}
                        />
                      </Card>
                    </Col>
                    <Col span={8}>
                      <Card>
                        <Statistic
                          title="已通过数量"
                          value={selectedRecord.approved_count || 0}
                          suffix="件"
                          valueStyle={{ color: '#52c41a' }}
                        />
                      </Card>
                    </Col>
                    <Col span={8}>
                      <Card>
                        <Statistic
                          title="已拒绝数量"
                          value={selectedRecord.rejected_count || 0}
                          suffix="件"
                          valueStyle={{ color: '#f5222d' }}
                        />
                      </Card>
                    </Col>
                  </Row>
                </div>
              </div>
            ) : (
              // 普通报损详情
              <div>
                <Descriptions title="基本信息" bordered column={2}>
                  <Descriptions.Item label="报损单号">{selectedRecord.id}</Descriptions.Item>
                  <Descriptions.Item label="门店">{selectedRecord.store_name}</Descriptions.Item>
                  <Descriptions.Item label="报损日期">{selectedRecord.loss_date || selectedRecord.month}</Descriptions.Item>
                  <Descriptions.Item label="状态">
                    <Tag color={statusColors[selectedRecord.status] || 'default'}>
                      {statusTexts[selectedRecord.status] || selectedRecord.status}
                    </Tag>
                  </Descriptions.Item>
                  <Descriptions.Item label="报损金额" span={2}>
                    {selectedRecord.total_amount ? selectedRecord.total_amount.toFixed(2) : '0.00'} 元
                  </Descriptions.Item>
                  <Descriptions.Item label="报损数量" span={2}>
                    {selectedRecord.total_count || selectedRecord.quantity || 0} 件
                  </Descriptions.Item>
                  {selectedRecord.reason && (
                    <Descriptions.Item label="报损原因" span={2}>
                      {reasonTexts[selectedRecord.reason] || selectedRecord.reason}
                    </Descriptions.Item>
                  )}
                </Descriptions>

                <div style={{ marginTop: 24 }}>
                  <Divider orientation="left">流转记录</Divider>
                  <Steps
                    progressDot
                    current={
                      selectedRecord.status === 'pending' ? 1 :
                      selectedRecord.status === 'approved' ? 2 :
                      selectedRecord.status === 'rejected' ? 2 : 3
                    }
                    direction="vertical"
                  >
                    <Step
                      title="门店发起"
                      description={
                        <div>
                          <p>操作人: {selectedRecord.creator_name || selectedRecord.created_by || '系统'}</p>
                          <p>操作时间: {selectedRecord.created_at}</p>
                        </div>
                      }
                    />
                    <Step
                      title="采购审核"
                      description={
                        selectedRecord.status === 'pending' ?
                        <div>
                          <p>等待审核中...</p>
                        </div> :
                        <div>
                          <p>操作人: {selectedRecord.purchase_approver || '系统'}</p>
                          <p>操作时间: {dayjs(selectedRecord.created_at).add(1, 'days').format('YYYY-MM-DD HH:mm:ss')}</p>
                          {selectedRecord.status === 'rejected' && (
                            <p>拒绝原因: {selectedRecord.reject_reason || '数据有误'}</p>
                          )}
                        </div>
                      }
                    />
                    <Step
                      title="财务审核"
                      description={
                        selectedRecord.status === 'pending' || selectedRecord.status === 'rejected' ?
                        <div>
                          <p>{selectedRecord.status === 'pending' ? '等待中' : '已终止'}</p>
                        </div> :
                        <div>
                          <p>操作人: {selectedRecord.finance_approver || '系统'}</p>
                          <p>操作时间: {dayjs(selectedRecord.created_at).add(2, 'days').format('YYYY-MM-DD HH:mm:ss')}</p>
                        </div>
                      }
                    />
                    <Step
                      title="完成"
                      description={
                        selectedRecord.status === 'approved' ?
                        <div>
                          <p>报损已入账</p>
                          <p>入账时间: {dayjs(selectedRecord.created_at).add(3, 'days').format('YYYY-MM-DD HH:mm:ss')}</p>
                        </div> :
                        <div>
                          <p>{selectedRecord.status === 'rejected' ? '已拒绝' : '处理中'}</p>
                        </div>
                      }
                    />
                  </Steps>
                </div>
              </div>
            )}
          </div>
        )}
      </Modal>

      {/* 审核通过弹窗 */}
      <Modal
        title="审核通过确认"
        open={approveVisible}
        onCancel={() => setApproveVisible(false)}
        onOk={handleApprove}
        confirmLoading={loading}
      >
        <p>确定要审核通过该报损记录吗？</p>
      </Modal>

      {/* 审核拒绝弹窗 */}
      <Modal
        title="审核拒绝"
        open={rejectVisible}
        onCancel={() => setRejectVisible(false)}
        footer={[
          <Button key="cancel" onClick={() => setRejectVisible(false)}>
            取消
          </Button>,
          <Button key="submit" type="primary" danger onClick={handleReject} loading={loading}>
            确认拒绝
          </Button>,
        ]}
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="rejectReason"
            label="拒绝原因"
            rules={[{ required: true, message: '请输入拒绝原因' }]}
          >
            <TextArea rows={4} placeholder="请输入拒绝原因" />
          </Form.Item>
        </Form>
      </Modal>

      {/* 批量审核通过弹窗 */}
      <Modal
        title="批量审核通过确认"
        open={batchApproveVisible}
        onCancel={() => setBatchApproveVisible(false)}
        onOk={handleBatchApprove}
        confirmLoading={loading}
      >
        <p>确定要批量审核通过选中的 {selectedRowKeys.length} 条报损记录吗？</p>
      </Modal>
    </div>
  );
};

export default FinanceLossApproval;
