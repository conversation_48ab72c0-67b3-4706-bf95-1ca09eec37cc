from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query, Path, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import func, and_, or_
import uuid
from datetime import datetime, timedelta

from db.database import get_db
from models.inventory import InventoryItem, InventoryHistory, InventoryCheck, InventoryCheckItem
from models.product import Product
from models.warehouse import Warehouse
from models.user import User
from schemas.inventory import (
    InventoryCheckCreate, InventoryCheckUpdate, InventoryCheckResponse, InventoryCheckListResponse
)
from api.deps import get_current_user, get_current_project_id, get_current_tenant_id

router = APIRouter()

# 库存盘点单API
@router.get("/checks", response_model=InventoryCheckListResponse)
async def get_inventory_checks(
    warehouse_id: Optional[uuid.UUID] = None,
    status: Optional[str] = None,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    skip: int = 0,
    limit: int = 100,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    project_id: uuid.UUID = Depends(get_current_project_id)
):
    """
    获取库存盘点单列表
    """
    try:
        # 构建查询条件
        conditions = [
            InventoryCheck.project_id == project_id
        ]

        if warehouse_id:
            conditions.append(InventoryCheck.warehouse_id == warehouse_id)

        if status:
            conditions.append(InventoryCheck.status == status)

        if start_date:
            conditions.append(InventoryCheck.check_date >= start_date)

        if end_date:
            conditions.append(InventoryCheck.check_date <= end_date)

        # 构建查询
        query = (
            select(
                InventoryCheck,
                Warehouse.name.label("warehouse_name")
            )
            .join(Warehouse, InventoryCheck.warehouse_id == Warehouse.id)
            .where(and_(*conditions))
            .order_by(InventoryCheck.created_at.desc())
            .offset(skip)
            .limit(limit)
        )

        # 执行查询
        result = await db.execute(query)
        checks_with_warehouse = result.all()

        # 获取总数
        count_query = select(func.count()).select_from(
            select(InventoryCheck)
            .where(and_(*conditions))
            .subquery()
        )
        result = await db.execute(count_query)
        total = result.scalar_one()

        # 构建响应数据
        checks = []
        for check, warehouse_name in checks_with_warehouse:
            # 查询盘点单明细
            items_query = (
                select(
                    InventoryCheckItem,
                    Product.name.label("product_name"),
                    Product.sku.label("product_sku")
                )
                .join(Product, InventoryCheckItem.product_id == Product.id)
                .where(InventoryCheckItem.check_id == check.id)
            )
            result = await db.execute(items_query)
            items = result.all()

            # 构建盘点单明细列表
            check_items = []
            for item, product_name, product_sku in items:
                check_items.append({
                    **item.__dict__,
                    "product_name": product_name,
                    "product_sku": product_sku
                })

            # 构建盘点单数据
            check_dict = {
                **check.__dict__,
                "warehouse_name": warehouse_name,
                "items": check_items
            }
            checks.append(check_dict)

        return {
            "success": True,
            "items": checks,
            "total": total,
            "page": skip // limit + 1 if limit > 0 else 1,
            "size": limit,
            "pages": (total + limit - 1) // limit if limit > 0 else 1
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取库存盘点单列表失败: {str(e)}"
        )


@router.get("/checks/{check_id}", response_model=InventoryCheckResponse)
async def get_inventory_check(
    check_id: uuid.UUID = Path(...),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    project_id: uuid.UUID = Depends(get_current_project_id)
):
    """
    获取单个库存盘点单详情
    """
    try:
        # 查询盘点单
        query = (
            select(
                InventoryCheck,
                Warehouse.name.label("warehouse_name")
            )
            .join(Warehouse, InventoryCheck.warehouse_id == Warehouse.id)
            .where(
                InventoryCheck.id == check_id,
                InventoryCheck.project_id == project_id
            )
        )

        result = await db.execute(query)
        check_with_warehouse = result.first()

        if not check_with_warehouse:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="库存盘点单不存在"
            )

        check, warehouse_name = check_with_warehouse

        # 查询盘点单明细
        items_query = (
            select(
                InventoryCheckItem,
                Product.name.label("product_name"),
                Product.sku.label("product_sku")
            )
            .join(Product, InventoryCheckItem.product_id == Product.id)
            .where(InventoryCheckItem.check_id == check.id)
        )
        result = await db.execute(items_query)
        items = result.all()

        # 构建盘点单明细列表
        check_items = []
        for item, product_name, product_sku in items:
            check_items.append({
                **item.__dict__,
                "product_name": product_name,
                "product_sku": product_sku
            })

        # 构建盘点单数据
        check_dict = {
            **check.__dict__,
            "warehouse_name": warehouse_name,
            "items": check_items
        }

        return {
            "success": True,
            "data": check_dict
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取库存盘点单详情失败: {str(e)}"
        )


@router.post("/checks", response_model=InventoryCheckResponse)
async def create_inventory_check(
    check: InventoryCheckCreate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    project_id: uuid.UUID = Depends(get_current_project_id),
    tenant_id: Optional[uuid.UUID] = Depends(get_current_tenant_id)
):
    """
    创建库存盘点单
    """
    try:
        # 检查仓库是否存在
        warehouse_query = select(Warehouse).where(
            Warehouse.id == check.warehouse_id,
            Warehouse.project_id == project_id
        )
        result = await db.execute(warehouse_query)
        warehouse = result.scalar_one_or_none()

        if not warehouse:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="仓库不存在"
            )

        # 检查产品是否存在
        product_ids = [item.product_id for item in check.items]

        # 查询产品
        products_query = select(Product).where(
            Product.id.in_(product_ids),
            Product.project_id == project_id
        )
        result = await db.execute(products_query)
        products = result.scalars().all()

        if len(products) != len(product_ids):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="部分产品不存在"
            )

        # 生成盘点单号
        now = datetime.now()
        check_number = f"CK{now.strftime('%Y%m%d%H%M%S')}{str(uuid.uuid4())[:8]}"

        # 创建盘点单
        new_check = InventoryCheck(
            id=uuid.uuid4(),
            tenant_id=tenant_id,
            project_id=project_id,
            check_number=check_number,
            warehouse_id=check.warehouse_id,
            status="draft",
            check_date=check.check_date,
            notes=check.notes,
            created_by=current_user.id,
            updated_by=current_user.id
        )

        db.add(new_check)
        await db.flush()

        # 创建盘点单明细
        for item in check.items:
            check_item = InventoryCheckItem(
                id=uuid.uuid4(),
                check_id=new_check.id,
                product_id=item.product_id,
                system_quantity=item.system_quantity,
                actual_quantity=item.actual_quantity,
                difference=item.actual_quantity - item.system_quantity,
                notes=item.notes
            )
            db.add(check_item)

        await db.commit()

        # 查询创建的盘点单
        query = (
            select(
                InventoryCheck,
                Warehouse.name.label("warehouse_name")
            )
            .join(Warehouse, InventoryCheck.warehouse_id == Warehouse.id)
            .where(InventoryCheck.id == new_check.id)
        )

        result = await db.execute(query)
        check_with_warehouse = result.first()

        check, warehouse_name = check_with_warehouse

        # 查询盘点单明细
        items_query = (
            select(
                InventoryCheckItem,
                Product.name.label("product_name"),
                Product.sku.label("product_sku")
            )
            .join(Product, InventoryCheckItem.product_id == Product.id)
            .where(InventoryCheckItem.check_id == check.id)
        )
        result = await db.execute(items_query)
        items = result.all()

        # 构建盘点单明细列表
        check_items = []
        for item, product_name, product_sku in items:
            check_items.append({
                **item.__dict__,
                "product_name": product_name,
                "product_sku": product_sku
            })

        # 构建盘点单数据
        check_dict = {
            **check.__dict__,
            "warehouse_name": warehouse_name,
            "items": check_items
        }

        return {
            "success": True,
            "message": "库存盘点单创建成功",
            "data": check_dict
        }
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建库存盘点单失败: {str(e)}"
        )


@router.put("/checks/{check_id}", response_model=InventoryCheckResponse)
async def update_inventory_check(
    check_id: uuid.UUID,
    check_update: InventoryCheckUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    project_id: uuid.UUID = Depends(get_current_project_id)
):
    """
    更新库存盘点单
    """
    try:
        # 查询盘点单
        query = select(InventoryCheck).where(
            InventoryCheck.id == check_id,
            InventoryCheck.project_id == project_id
        )
        result = await db.execute(query)
        existing_check = result.scalar_one_or_none()

        if not existing_check:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="库存盘点单不存在"
            )

        # 只有草稿状态的盘点单可以更新
        if existing_check.status != "draft":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="只有草稿状态的盘点单可以更新"
            )

        # 更新字段
        update_data = check_update.dict(exclude_unset=True)

        for key, value in update_data.items():
            setattr(existing_check, key, value)

        existing_check.updated_by = current_user.id
        existing_check.updated_at = datetime.now()

        await db.commit()
        await db.refresh(existing_check)

        # 查询更新后的盘点单
        query = (
            select(
                InventoryCheck,
                Warehouse.name.label("warehouse_name")
            )
            .join(Warehouse, InventoryCheck.warehouse_id == Warehouse.id)
            .where(InventoryCheck.id == check_id)
        )

        result = await db.execute(query)
        check_with_warehouse = result.first()

        check, warehouse_name = check_with_warehouse

        # 查询盘点单明细
        items_query = (
            select(
                InventoryCheckItem,
                Product.name.label("product_name"),
                Product.sku.label("product_sku")
            )
            .join(Product, InventoryCheckItem.product_id == Product.id)
            .where(InventoryCheckItem.check_id == check.id)
        )
        result = await db.execute(items_query)
        items = result.all()

        # 构建盘点单明细列表
        check_items = []
        for item, product_name, product_sku in items:
            check_items.append({
                **item.__dict__,
                "product_name": product_name,
                "product_sku": product_sku
            })

        # 构建盘点单数据
        check_dict = {
            **check.__dict__,
            "warehouse_name": warehouse_name,
            "items": check_items
        }

        return {
            "success": True,
            "message": "库存盘点单更新成功",
            "data": check_dict
        }
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新库存盘点单失败: {str(e)}"
        )


@router.post("/checks/{check_id}/confirm", response_model=InventoryCheckResponse)
async def confirm_inventory_check(
    check_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    project_id: uuid.UUID = Depends(get_current_project_id),
    tenant_id: Optional[uuid.UUID] = Depends(get_current_tenant_id)
):
    """
    确认库存盘点单
    """
    try:
        # 查询盘点单
        query = select(InventoryCheck).where(
            InventoryCheck.id == check_id,
            InventoryCheck.project_id == project_id
        )
        result = await db.execute(query)
        check = result.scalar_one_or_none()

        if not check:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="库存盘点单不存在"
            )

        # 只有草稿状态的盘点单可以确认
        if check.status != "draft":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="只有草稿状态的盘点单可以确认"
            )

        # 查询盘点单明细
        items_query = select(InventoryCheckItem).where(
            InventoryCheckItem.check_id == check_id
        )
        result = await db.execute(items_query)
        check_items = result.scalars().all()

        # 获取产品ID列表
        product_ids = [item.product_id for item in check_items]

        # 查询仓库库存
        inventory_query = select(InventoryItem).where(
            InventoryItem.product_id.in_(product_ids),
            InventoryItem.warehouse_id == check.warehouse_id,
            InventoryItem.project_id == project_id
        )
        result = await db.execute(inventory_query)
        inventory_items = {item.product_id: item for item in result.scalars().all()}

        # 更新盘点单状态
        check.status = "completed"
        check.updated_by = current_user.id
        check.updated_at = datetime.now()

        # 处理库存变更
        now = datetime.now()

        for item in check_items:
            inventory_item = inventory_items.get(item.product_id)

            if inventory_item:
                # 记录旧数量
                old_quantity = inventory_item.quantity

                # 如果有差异，更新库存
                if item.difference != 0:
                    # 更新库存数量
                    inventory_item.quantity = item.actual_quantity
                    inventory_item.updated_by = current_user.id
                    inventory_item.updated_at = now
                    inventory_item.last_movement_date = now

                    # 创建库存历史记录
                    history = InventoryHistory(
                        id=uuid.uuid4(),
                        inventory_item_id=inventory_item.id,
                        tenant_id=tenant_id,
                        project_id=project_id,
                        action_type="check",
                        previous_quantity=old_quantity,
                        new_quantity=item.actual_quantity,
                        change_amount=item.difference,
                        reference_id=check.id,
                        reference_type="inventory_check",
                        notes=f"盘点调整: {check.check_number}",
                        created_by=current_user.id
                    )
                    db.add(history)
            else:
                # 如果库存项不存在，但盘点数量大于0，创建新库存项
                if item.actual_quantity > 0:
                    # 查询产品
                    product_query = select(Product).where(Product.id == item.product_id)
                    result = await db.execute(product_query)
                    product = result.scalar_one_or_none()

                    if not product:
                        continue

                    # 创建新库存项
                    new_inventory = InventoryItem(
                        id=uuid.uuid4(),
                        tenant_id=tenant_id,
                        project_id=project_id,
                        product_id=item.product_id,
                        warehouse_id=check.warehouse_id,
                        quantity=item.actual_quantity,
                        min_quantity=0,  # 默认最低库存为0
                        is_slow_moving=False,
                        last_movement_date=now,
                        created_by=current_user.id,
                        updated_by=current_user.id
                    )
                    db.add(new_inventory)
                    await db.flush()

                    # 创建库存历史记录
                    history = InventoryHistory(
                        id=uuid.uuid4(),
                        inventory_item_id=new_inventory.id,
                        tenant_id=tenant_id,
                        project_id=project_id,
                        action_type="check",
                        previous_quantity=0,
                        new_quantity=item.actual_quantity,
                        change_amount=item.actual_quantity,
                        reference_id=check.id,
                        reference_type="inventory_check",
                        notes=f"盘点新增: {check.check_number}",
                        created_by=current_user.id
                    )
                    db.add(history)

        await db.commit()

        # 查询更新后的盘点单
        query = (
            select(
                InventoryCheck,
                Warehouse.name.label("warehouse_name")
            )
            .join(Warehouse, InventoryCheck.warehouse_id == Warehouse.id)
            .where(InventoryCheck.id == check_id)
        )

        result = await db.execute(query)
        check_with_warehouse = result.first()

        check, warehouse_name = check_with_warehouse

        # 查询盘点单明细
        items_query = (
            select(
                InventoryCheckItem,
                Product.name.label("product_name"),
                Product.sku.label("product_sku")
            )
            .join(Product, InventoryCheckItem.product_id == Product.id)
            .where(InventoryCheckItem.check_id == check.id)
        )
        result = await db.execute(items_query)
        items = result.all()

        # 构建盘点单明细列表
        check_items = []
        for item, product_name, product_sku in items:
            check_items.append({
                **item.__dict__,
                "product_name": product_name,
                "product_sku": product_sku
            })

        # 构建盘点单数据
        check_dict = {
            **check.__dict__,
            "warehouse_name": warehouse_name,
            "items": check_items
        }

        return {
            "success": True,
            "message": "库存盘点单确认成功",
            "data": check_dict
        }
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"确认库存盘点单失败: {str(e)}"
        )


@router.post("/checks/{check_id}/cancel", response_model=InventoryCheckResponse)
async def cancel_inventory_check(
    check_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    project_id: uuid.UUID = Depends(get_current_project_id)
):
    """
    取消库存盘点单
    """
    try:
        # 查询盘点单
        query = select(InventoryCheck).where(
            InventoryCheck.id == check_id,
            InventoryCheck.project_id == project_id
        )
        result = await db.execute(query)
        check = result.scalar_one_or_none()

        if not check:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="库存盘点单不存在"
            )

        # 只有草稿状态的盘点单可以取消
        if check.status != "draft":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="只有草稿状态的盘点单可以取消"
            )

        # 更新盘点单状态
        check.status = "cancelled"
        check.updated_by = current_user.id
        check.updated_at = datetime.now()

        await db.commit()

        # 查询更新后的盘点单
        query = (
            select(
                InventoryCheck,
                Warehouse.name.label("warehouse_name")
            )
            .join(Warehouse, InventoryCheck.warehouse_id == Warehouse.id)
            .where(InventoryCheck.id == check_id)
        )

        result = await db.execute(query)
        check_with_warehouse = result.first()

        check, warehouse_name = check_with_warehouse

        # 查询盘点单明细
        items_query = (
            select(
                InventoryCheckItem,
                Product.name.label("product_name"),
                Product.sku.label("product_sku")
            )
            .join(Product, InventoryCheckItem.product_id == Product.id)
            .where(InventoryCheckItem.check_id == check.id)
        )
        result = await db.execute(items_query)
        items = result.all()

        # 构建盘点单明细列表
        check_items = []
        for item, product_name, product_sku in items:
            check_items.append({
                **item.__dict__,
                "product_name": product_name,
                "product_sku": product_sku
            })

        # 构建盘点单数据
        check_dict = {
            **check.__dict__,
            "warehouse_name": warehouse_name,
            "items": check_items
        }

        return {
            "success": True,
            "message": "库存盘点单取消成功",
            "data": check_dict
        }
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"取消库存盘点单失败: {str(e)}"
        )


@router.get("/init-check", response_model=dict)
async def init_inventory_check(
    warehouse_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    project_id: uuid.UUID = Depends(get_current_project_id)
):
    """
    初始化库存盘点单，获取仓库中的所有库存项
    """
    try:
        # 检查仓库是否存在
        warehouse_query = select(Warehouse).where(
            Warehouse.id == warehouse_id,
            Warehouse.project_id == project_id
        )
        result = await db.execute(warehouse_query)
        warehouse = result.scalar_one_or_none()

        if not warehouse:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="仓库不存在"
            )

        # 查询仓库中的所有库存项
        query = (
            select(
                InventoryItem,
                Product.name.label("product_name"),
                Product.sku.label("product_sku"),
                Product.category.label("product_category")
            )
            .join(Product, InventoryItem.product_id == Product.id)
            .where(
                InventoryItem.warehouse_id == warehouse_id,
                InventoryItem.project_id == project_id
            )
            .order_by(Product.name)
        )

        result = await db.execute(query)
        items = result.all()

        # 构建库存项列表
        inventory_items = []
        for item, product_name, product_sku, product_category in items:
            inventory_items.append({
                "product_id": str(item.product_id),
                "product_name": product_name,
                "product_sku": product_sku,
                "product_category": product_category,
                "system_quantity": item.quantity,
                "actual_quantity": item.quantity,  # 默认实际数量等于系统数量
                "difference": 0
            })

        return {
            "success": True,
            "warehouse_id": str(warehouse_id),
            "warehouse_name": warehouse.name,
            "items": inventory_items
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"初始化库存盘点单失败: {str(e)}"
        )
