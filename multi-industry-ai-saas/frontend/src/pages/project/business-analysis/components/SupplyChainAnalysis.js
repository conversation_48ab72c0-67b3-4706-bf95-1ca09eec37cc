import React, { useState, useEffect } from 'react';
import {
  Card, Row, Col, Table, Button, Space, Tag, Progress, 
  Statistic, Alert, Tabs, Select, DatePicker, Input,
  message, Tooltip, Badge, Empty, Spin,
  Timeline, Rate
} from 'antd';
import {
  ApartmentOutlined, SecurityScanOutlined, <PERSON>boltOutlined,
  NodeIndexOutlined, WarningOutlined, CheckCircleOutlined,
  <PERSON><PERSON>hartOutlined, Pie<PERSON>hartOutlined, <PERSON><PERSON>hartOutlined,
  SearchOutlined, FilterOutlined, ExportOutlined,
  RobotOutlined, GlobalOutlined, LinkOutlined,
  SyncOutlined, ClockCircleOutlined, ExclamationCircleOutlined
} from '@ant-design/icons';
import { Line, Column, Pie, Radar } from '@ant-design/plots';
import { api } from '../../../../services/api';

const { Option } = Select;
const { RangePicker } = DatePicker;
const { Search } = Input;
const { TabPane } = Tabs;

/**
 * 供应链分析组件
 * 分析供应链稳定性、风险评估、配送效率等
 */
const SupplyChainAnalysis = () => {
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('stability');
  const [stabilityData, setStabilityData] = useState([]);
  const [riskData, setRiskData] = useState([]);
  const [optimizationData, setOptimizationData] = useState([]);
  const [deliveryData, setDeliveryData] = useState([]);
  const [costEfficiencyData, setCostEfficiencyData] = useState([]);
  const [filters, setFilters] = useState({
    supplier: 'all',
    region: 'all',
    dateRange: null,
    keyword: ''
  });
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    fetchSupplyChainData();
  }, [filters]);

  // 获取供应链分析数据
  const fetchSupplyChainData = async () => {
    try {
      setLoading(true);
      
      const projectId = localStorage.getItem('project_id');
      if (!projectId) {
        throw new Error('未找到项目ID');
      }
      
      const params = {
        supplier: filters.supplier,
        region: filters.region,
        dateRange: filters.dateRange,
        keyword: filters.keyword
      };

      const response = await api.get(`/project/${projectId}/business-analysis/supply-chain-analysis`, { params });
      
      if (response && response.data) {
        setStabilityData(response.data.stability || []);
        setRiskData(response.data.risks || []);
        setOptimizationData(response.data.optimization || []);
        setDeliveryData(response.data.delivery || []);
        setCostEfficiencyData(response.data.costEfficiency || []);
      }
    } catch (error) {
      console.error('获取供应链分析数据失败:', error);
      // 使用模拟数据
      generateMockData();
    } finally {
      setLoading(false);
    }
  };

  // 生成模拟数据
  const generateMockData = () => {
    const mockStabilityData = [
      { 
        supplier: '鲜果供应链', 
        stabilityScore: 92, 
        deliveryConsistency: 95, 
        qualityConsistency: 89, 
        priceStability: 87,
        riskLevel: 'low',
        avgLeadTime: 2.3,
        reliability: 96
      },
      { 
        supplier: '优质农产品', 
        stabilityScore: 85, 
        deliveryConsistency: 88, 
        qualityConsistency: 82, 
        priceStability: 79,
        riskLevel: 'medium',
        avgLeadTime: 3.1,
        reliability: 88
      },
      { 
        supplier: '绿色食品批发', 
        stabilityScore: 78, 
        deliveryConsistency: 75, 
        qualityConsistency: 85, 
        priceStability: 72,
        riskLevel: 'high',
        avgLeadTime: 4.2,
        reliability: 82
      }
    ];

    const mockRiskData = [
      {
        riskType: '供应商单一化',
        level: 'high',
        impact: 8,
        probability: 6,
        description: '过度依赖单一供应商，存在断供风险',
        mitigation: '寻找备用供应商，分散采购风险'
      },
      {
        riskType: '价格波动',
        level: 'medium',
        impact: 6,
        probability: 7,
        description: '受季节性因素影响，价格波动较大',
        mitigation: '建立价格预警机制，签订长期合同'
      },
      {
        riskType: '运输延误',
        level: 'medium',
        impact: 5,
        probability: 4,
        description: '物流配送环节可能出现延误',
        mitigation: '优化配送路线，建立应急配送方案'
      },
      {
        riskType: '质量不稳定',
        level: 'low',
        impact: 7,
        probability: 3,
        description: '部分供应商质量控制不够严格',
        mitigation: '加强质量检验，建立质量反馈机制'
      }
    ];

    const mockDeliveryData = [
      { month: '2024-01', onTimeRate: 95, avgDeliveryTime: 2.5, deliveryCost: 1200 },
      { month: '2024-02', onTimeRate: 92, avgDeliveryTime: 2.8, deliveryCost: 1350 },
      { month: '2024-03', onTimeRate: 96, avgDeliveryTime: 2.3, deliveryCost: 1180 },
      { month: '2024-04', onTimeRate: 94, avgDeliveryTime: 2.6, deliveryCost: 1280 },
      { month: '2024-05', onTimeRate: 98, avgDeliveryTime: 2.1, deliveryCost: 1150 },
      { month: '2024-06', onTimeRate: 97, avgDeliveryTime: 2.4, deliveryCost: 1220 }
    ];

    const mockOptimizationData = [
      {
        category: '库存优化',
        currentStatus: 75,
        targetStatus: 90,
        savings: 12000,
        priority: 'high',
        actions: ['调整安全库存', '优化补货策略', '实施JIT配送']
      },
      {
        category: '采购成本',
        currentStatus: 68,
        targetStatus: 85,
        savings: 8500,
        priority: 'high',
        actions: ['集中采购', '谈判优化', '寻找替代供应商']
      },
      {
        category: '配送效率',
        currentStatus: 82,
        targetStatus: 95,
        savings: 6200,
        priority: 'medium',
        actions: ['路线优化', '配送整合', '技术升级']
      },
      {
        category: '供应商管理',
        currentStatus: 78,
        targetStatus: 92,
        savings: 4800,
        priority: 'medium',
        actions: ['绩效考核', '关系优化', '合作深化']
      }
    ];

    setStabilityData(mockStabilityData);
    setRiskData(mockRiskData);
    setDeliveryData(mockDeliveryData);
    setOptimizationData(mockOptimizationData);
  };

  // 刷新数据
  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchSupplyChainData();
    setRefreshing(false);
    message.success('数据已刷新');
  };

  // 供应链稳定性表格列
  const stabilityColumns = [
    {
      title: '供应商',
      dataIndex: 'supplier',
      key: 'supplier',
      fixed: 'left',
      width: 150,
      render: (name) => (
        <div style={{ fontWeight: 500 }}>{name}</div>
      )
    },
    {
      title: '稳定性评分',
      dataIndex: 'stabilityScore',
      key: 'stabilityScore',
      render: (score) => (
        <div>
          <Progress 
            percent={score} 
            size="small" 
            strokeColor={score >= 90 ? '#52c41a' : score >= 80 ? '#faad14' : '#ff4d4f'}
          />
          <div style={{ fontSize: 12, marginTop: 2 }}>{score}分</div>
        </div>
      )
    },
    {
      title: '交付一致性',
      dataIndex: 'deliveryConsistency',
      key: 'deliveryConsistency',
      render: (rate) => `${rate}%`
    },
    {
      title: '质量一致性',
      dataIndex: 'qualityConsistency',
      key: 'qualityConsistency',
      render: (rate) => `${rate}%`
    },
    {
      title: '价格稳定性',
      dataIndex: 'priceStability',
      key: 'priceStability',
      render: (rate) => `${rate}%`
    },
    {
      title: '风险等级',
      dataIndex: 'riskLevel',
      key: 'riskLevel',
      render: (level) => (
        <Tag color={
          level === 'low' ? 'green' : 
          level === 'medium' ? 'orange' : 'red'
        }>
          {level === 'low' ? '低风险' : level === 'medium' ? '中风险' : '高风险'}
        </Tag>
      )
    },
    {
      title: '平均交货期',
      dataIndex: 'avgLeadTime',
      key: 'avgLeadTime',
      render: (time) => `${time}天`
    },
    {
      title: '可靠性',
      dataIndex: 'reliability',
      key: 'reliability',
      render: (rate) => (
        <Rate disabled value={Math.ceil(rate / 20)} />
      )
    }
  ];

  // 风险评估表格列
  const riskColumns = [
    {
      title: '风险类型',
      dataIndex: 'riskType',
      key: 'riskType',
      width: 120
    },
    {
      title: '风险等级',
      dataIndex: 'level',
      key: 'level',
      render: (level) => (
        <Tag color={
          level === 'low' ? 'green' : 
          level === 'medium' ? 'orange' : 'red'
        }>
          {level === 'low' ? '低' : level === 'medium' ? '中' : '高'}
        </Tag>
      )
    },
    {
      title: '影响程度',
      dataIndex: 'impact',
      key: 'impact',
      render: (impact) => (
        <Progress 
          percent={impact * 10} 
          size="small"
          format={() => `${impact}/10`}
          strokeColor={impact >= 7 ? '#ff4d4f' : impact >= 5 ? '#faad14' : '#52c41a'}
        />
      )
    },
    {
      title: '发生概率',
      dataIndex: 'probability',
      key: 'probability',
      render: (prob) => (
        <Progress 
          percent={prob * 10} 
          size="small"
          format={() => `${prob}/10`}
          strokeColor={prob >= 7 ? '#ff4d4f' : prob >= 5 ? '#faad14' : '#52c41a'}
        />
      )
    },
    {
      title: '风险描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true
    },
    {
      title: '缓解措施',
      dataIndex: 'mitigation',
      key: 'mitigation',
      ellipsis: true,
      render: (text) => (
        <Tooltip title={text}>
          <span style={{ color: '#1890ff' }}>{text}</span>
        </Tooltip>
      )
    }
  ];

  // 渲染供应链稳定性分析
  const renderStabilityAnalysis = () => {
    const stabilityConfig = {
      data: stabilityData.map(item => ({
        name: item.supplier,
        value: item.stabilityScore,
        category: '稳定性评分'
      })),
      xField: 'name',
      yField: 'value',
      height: 300,
      columnStyle: {
        radius: [4, 4, 0, 0],
      },
    };

    return (
      <div>
        <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
          <Col xs={24} sm={6}>
            <Card>
              <Statistic
                title="平均稳定性"
                value={stabilityData.reduce((sum, item) => sum + item.stabilityScore, 0) / stabilityData.length || 0}
                precision={1}
                suffix="分"
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={6}>
            <Card>
              <Statistic
                title="高稳定供应商"
                value={stabilityData.filter(item => item.stabilityScore >= 90).length}
                suffix={`/${stabilityData.length}`}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={6}>
            <Card>
              <Statistic
                title="平均可靠性"
                value={stabilityData.reduce((sum, item) => sum + item.reliability, 0) / stabilityData.length || 0}
                precision={1}
                suffix="%"
                valueStyle={{ color: '#722ed1' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={6}>
            <Card>
              <Statistic
                title="风险供应商"
                value={stabilityData.filter(item => item.riskLevel === 'high').length}
                suffix="个"
                valueStyle={{ color: '#cf1322' }}
              />
            </Card>
          </Col>
        </Row>

        <Row gutter={[16, 16]}>
          <Col xs={24} lg={14}>
            <Card title="供应商稳定性对比">
              <Column {...stabilityConfig} />
            </Card>
          </Col>
          <Col xs={24} lg={10}>
            <Card title="稳定性详情" bodyStyle={{ padding: 0 }}>
              <Table
                columns={stabilityColumns}
                dataSource={stabilityData}
                rowKey="supplier"
                pagination={false}
                size="small"
                scroll={{ x: 800 }}
              />
            </Card>
          </Col>
        </Row>

        <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
          <Col xs={24}>
            <Card title="供应链健康度雷达图">
              <Radar
                data={stabilityData.map(supplier => [
                  { name: supplier.supplier, item: '交付一致性', score: supplier.deliveryConsistency },
                  { name: supplier.supplier, item: '质量一致性', score: supplier.qualityConsistency },
                  { name: supplier.supplier, item: '价格稳定性', score: supplier.priceStability },
                  { name: supplier.supplier, item: '可靠性', score: supplier.reliability },
                ]).flat()}
                xField="item"
                yField="score"
                seriesField="name"
                meta={{
                  score: {
                    alias: '得分',
                    min: 0,
                    max: 100,
                  },
                }}
                xAxis={{
                  line: null,
                  tickLine: null,
                }}
                yAxis={{
                  label: false,
                  grid: {
                    alternateColor: 'rgba(0, 0, 0, 0.04)',
                  },
                }}
                height={400}
              />
            </Card>
          </Col>
        </Row>
      </div>
    );
  };

  // 渲染风险评估
  const renderRiskAssessment = () => (
    <div>
      <Alert
        message="供应链风险评估"
        description="识别和评估供应链中的潜在风险点，提供相应的缓解策略建议。"
        type="warning"
        showIcon
        style={{ marginBottom: 16 }}
      />

      <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="高风险项目"
              value={riskData.filter(risk => risk.level === 'high').length}
              suffix="个"
              valueStyle={{ color: '#cf1322' }}
              prefix={<WarningOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="中风险项目"
              value={riskData.filter(risk => risk.level === 'medium').length}
              suffix="个"
              valueStyle={{ color: '#faad14' }}
              prefix={<ExclamationCircleOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="低风险项目"
              value={riskData.filter(risk => risk.level === 'low').length}
              suffix="个"
              valueStyle={{ color: '#52c41a' }}
              prefix={<CheckCircleOutlined />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="总体风险得分"
              value={riskData.reduce((sum, risk) => sum + risk.impact * risk.probability, 0) / riskData.length || 0}
              precision={1}
              suffix="/100"
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      <Table
        columns={riskColumns}
        dataSource={riskData}
        rowKey="riskType"
        scroll={{ x: 800 }}
        pagination={false}
      />

      <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
        <Col xs={24} lg={12}>
          <Card title="风险分布">
            <Pie
              data={[
                { type: '高风险', value: riskData.filter(r => r.level === 'high').length },
                { type: '中风险', value: riskData.filter(r => r.level === 'medium').length },
                { type: '低风险', value: riskData.filter(r => r.level === 'low').length }
              ]}
              angleField="value"
              colorField="type"
              radius={0.8}
              height={300}
              color={['#ff4d4f', '#faad14', '#52c41a']}
            />
          </Card>
        </Col>
        <Col xs={24} lg={12}>
          <Card title="风险缓解时间线">
            <Timeline>
              {riskData.filter(risk => risk.level === 'high').map((risk, index) => (
                <Timeline.Item 
                  key={index}
                  color={risk.level === 'high' ? 'red' : risk.level === 'medium' ? 'orange' : 'green'}
                >
                  <div style={{ fontWeight: 500 }}>{risk.riskType}</div>
                  <div style={{ fontSize: 12, color: '#666', marginTop: 4 }}>
                    {risk.mitigation}
                  </div>
                </Timeline.Item>
              ))}
            </Timeline>
          </Card>
        </Col>
      </Row>
    </div>
  );

  // 渲染配送效率分析
  const renderDeliveryAnalysis = () => {
    const deliveryConfig = {
      data: deliveryData,
      xField: 'month',
      yField: 'onTimeRate',
      height: 300,
      point: {
        size: 5,
        shape: 'diamond',
      },
    };

    return (
      <div>
        <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
          <Col xs={24} sm={8}>
            <Card>
              <Statistic
                title="平均准时率"
                value={deliveryData.reduce((sum, item) => sum + item.onTimeRate, 0) / deliveryData.length || 0}
                precision={1}
                suffix="%"
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={8}>
            <Card>
              <Statistic
                title="平均配送时间"
                value={deliveryData.reduce((sum, item) => sum + item.avgDeliveryTime, 0) / deliveryData.length || 0}
                precision={1}
                suffix="天"
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={8}>
            <Card>
              <Statistic
                title="月均配送成本"
                value={deliveryData.reduce((sum, item) => sum + item.deliveryCost, 0) / deliveryData.length || 0}
                precision={0}
                prefix="¥"
                valueStyle={{ color: '#722ed1' }}
              />
            </Card>
          </Col>
        </Row>

        <Row gutter={[16, 16]}>
          <Col xs={24} lg={16}>
            <Card title="准时率趋势">
              <Line {...deliveryConfig} />
            </Card>
          </Col>
          <Col xs={24} lg={8}>
            <Card title="配送分析">
              <div style={{ marginBottom: 16 }}>
                <h4><RobotOutlined /> AI分析洞察</h4>
                <ul style={{ margin: 0, paddingLeft: 20 }}>
                  <li>准时率呈上升趋势，供应链稳定性提升</li>
                  <li>5月份表现最佳，可分析成功因素</li>
                  <li>配送成本相对稳定，效率持续优化</li>
                  <li>建议关注配送路线优化</li>
                </ul>
              </div>
              <Alert
                message="优化建议"
                description="建议建立配送效率监控体系，定期评估配送绩效。"
                type="info"
                showIcon
                size="small"
              />
            </Card>
          </Col>
        </Row>

        <Row style={{ marginTop: 16 }}>
          <Col xs={24}>
            <Card title="配送详情">
              <Table
                columns={[
                  { title: '月份', dataIndex: 'month', key: 'month', render: (month) => month.replace('-', '年') + '月' },
                  { title: '准时率', dataIndex: 'onTimeRate', key: 'onTimeRate', render: (rate) => `${rate}%` },
                  { title: '平均配送时间', dataIndex: 'avgDeliveryTime', key: 'avgDeliveryTime', render: (time) => `${time}天` },
                  { title: '配送成本', dataIndex: 'deliveryCost', key: 'deliveryCost', render: (cost) => `¥${cost}` },
                  { 
                    title: '配送效率', 
                    key: 'efficiency',
                    render: (_, record) => {
                      const efficiency = record.onTimeRate / record.avgDeliveryTime;
                      return (
                        <Progress 
                          percent={Math.min(efficiency * 2, 100)} 
                          size="small"
                          format={() => `${efficiency.toFixed(1)}`}
                        />
                      );
                    }
                  }
                ]}
                dataSource={deliveryData}
                rowKey="month"
                pagination={false}
                size="small"
              />
            </Card>
          </Col>
        </Row>
      </div>
    );
  };

  // 渲染优化建议
  const renderOptimizationSuggestions = () => (
    <div>
      <Alert
        message="供应链优化建议"
        description="基于数据分析和AI算法，为供应链各环节提供具体的优化建议和实施方案。"
        type="success"
        showIcon
        style={{ marginBottom: 16 }}
      />

      <Row gutter={[16, 16]}>
        {optimizationData.map((item, index) => (
          <Col xs={24} lg={12} key={index}>
            <Card 
              title={
                <Space>
                  <ThunderboltOutlined />
                  {item.category}
                  <Tag color={item.priority === 'high' ? 'red' : item.priority === 'medium' ? 'orange' : 'green'}>
                    {item.priority === 'high' ? '高优先级' : item.priority === 'medium' ? '中优先级' : '低优先级'}
                  </Tag>
                </Space>
              }
            >
              <div style={{ marginBottom: 16 }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 8 }}>
                  <span>当前状态</span>
                  <span>{item.currentStatus}%</span>
                </div>
                <Progress percent={item.currentStatus} size="small" />
                
                <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 8, marginTop: 8 }}>
                  <span>目标状态</span>
                  <span>{item.targetStatus}%</span>
                </div>
                <Progress percent={item.targetStatus} size="small" strokeColor="#52c41a" />
              </div>

              <div style={{ marginBottom: 16 }}>
                <Statistic
                  title="预期节省"
                  value={item.savings}
                  prefix="¥"
                  valueStyle={{ color: '#52c41a', fontSize: 16 }}
                />
              </div>

              <div>
                <h5>具体行动:</h5>
                <ul style={{ margin: 0, paddingLeft: 20 }}>
                  {item.actions.map((action, idx) => (
                    <li key={idx}>{action}</li>
                  ))}
                </ul>
              </div>
            </Card>
          </Col>
        ))}
      </Row>

      <Row style={{ marginTop: 16 }}>
        <Col xs={24}>
          <Card title="优化路线图">
            <Timeline mode="alternate">
              <Timeline.Item color="blue">
                <div style={{ fontWeight: 500 }}>第一阶段 (1-3个月)</div>
                <div>优化库存管理和采购成本控制</div>
              </Timeline.Item>
              <Timeline.Item color="orange">
                <div style={{ fontWeight: 500 }}>第二阶段 (3-6个月)</div>
                <div>提升配送效率和供应商管理</div>
              </Timeline.Item>
              <Timeline.Item color="green">
                <div style={{ fontWeight: 500 }}>第三阶段 (6-12个月)</div>
                <div>全面数字化转型和智能化升级</div>
              </Timeline.Item>
              <Timeline.Item color="purple">
                <div style={{ fontWeight: 500 }}>持续优化</div>
                <div>建立持续改进机制，定期评估优化</div>
              </Timeline.Item>
            </Timeline>
          </Card>
        </Col>
      </Row>
    </div>
  );

  if (loading && !refreshing) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>加载供应链分析数据...</div>
      </div>
    );
  }

  return (
    <div className="supply-chain-analysis">
      {/* 筛选工具栏 */}
      <Card style={{ marginBottom: 16 }}>
        <Row gutter={[16, 16]} align="middle">
          <Col xs={24} sm={6}>
            <Select
              placeholder="选择供应商"
              style={{ width: '100%' }}
              value={filters.supplier}
              onChange={(value) => setFilters({ ...filters, supplier: value })}
            >
              <Option value="all">全部供应商</Option>
              <Option value="supplier1">鲜果供应链</Option>
              <Option value="supplier2">优质农产品</Option>
              <Option value="supplier3">绿色食品批发</Option>
            </Select>
          </Col>
          <Col xs={24} sm={6}>
            <Select
              placeholder="选择区域"
              style={{ width: '100%' }}
              value={filters.region}
              onChange={(value) => setFilters({ ...filters, region: value })}
            >
              <Option value="all">全部区域</Option>
              <Option value="north">华北地区</Option>
              <Option value="south">华南地区</Option>
              <Option value="east">华东地区</Option>
            </Select>
          </Col>
          <Col xs={24} sm={6}>
            <RangePicker
              style={{ width: '100%' }}
              onChange={(dates) => setFilters({ ...filters, dateRange: dates })}
            />
          </Col>
          <Col xs={24} sm={6}>
            <Space>
              <Button 
                icon={<SyncOutlined spin={refreshing} />} 
                onClick={handleRefresh}
                loading={refreshing}
              >
                刷新
              </Button>
              <Button icon={<ExportOutlined />}>
                导出
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 分析内容Tab */}
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane 
          tab={
            <Space>
              <NodeIndexOutlined />
              稳定性分析
            </Space>
          } 
          key="stability"
        >
          {renderStabilityAnalysis()}
        </TabPane>

        <TabPane 
          tab={
            <Space>
              <SecurityScanOutlined />
              风险评估
            </Space>
          } 
          key="risk"
        >
          {renderRiskAssessment()}
        </TabPane>

        <TabPane 
          tab={
            <Space>
              <ClockCircleOutlined />
              配送效率
            </Space>
          } 
          key="delivery"
        >
          {renderDeliveryAnalysis()}
        </TabPane>

        <TabPane 
          tab={
            <Space>
              <ThunderboltOutlined />
              优化建议
            </Space>
          } 
          key="optimization"
        >
          {renderOptimizationSuggestions()}
        </TabPane>
      </Tabs>
    </div>
  );
};

export default SupplyChainAnalysis; 