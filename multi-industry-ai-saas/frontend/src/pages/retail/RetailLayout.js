import React, { useState } from 'react';
import { Layout, Menu, Button, Avatar, Dropdown, Badge, theme } from 'antd';
import { 
  MenuFoldOutlined, MenuUnfoldOutlined, UserOutlined, BellOutlined, 
  ShopOutlined, DashboardOutlined, BarChartOutlined, ShoppingOutlined,
  TeamOutlined, SettingOutlined, InboxOutlined, AccountBookOutlined,
  AppstoreOutlined, AlertOutlined, ScheduleOutlined, FileTextOutlined,
  SolutionOutlined, CloudServerOutlined, RobotOutlined, BookOutlined
} from '@ant-design/icons';
import { Link, Outlet, useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import './RetailLayout.css';

const { Header, Sider, Content } = Layout;

/**
 * 零售系统布局组件
 * 复刻retail-ai-system的布局和菜单结构
 */
const RetailLayout = () => {
  const [collapsed, setCollapsed] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const { user, logout } = useAuth();
  const { token } = theme.useToken();

  // 获取当前选中的菜单项
  const getSelectedKeys = () => {
    const path = location.pathname;
    return [path];
  };

  // 用户菜单项
  const userMenuItems = [
    {
      key: 'profile',
      label: '个人中心',
      onClick: () => navigate('/profile'),
    },
    {
      key: 'settings',
      label: '个人设置',
      onClick: () => navigate('/settings'),
    },
    {
      key: 'logout',
      label: '退出登录',
      onClick: logout,
    },
  ];

  // 通知菜单项
  const notificationMenuItems = [
    {
      key: 'notifications',
      label: '查看全部通知',
      onClick: () => navigate('/notifications'),
    },
  ];

  // 菜单项配置
  const menuItems = [
    // 门店菜单
    {
      key: 'store',
      icon: <ShopOutlined />,
      label: '门店',
      children: [
        {
          key: '/retail/store/dashboard',
          icon: <DashboardOutlined />,
          label: <Link to="/retail/store/dashboard">门店仪表盘</Link>,
        },
        {
          key: '/retail/store/super-dashboard',
          icon: <BarChartOutlined />,
          label: <Link to="/retail/store/super-dashboard">门店大盘</Link>,
        },
        {
          key: '/retail/store/routine-tasks',
          icon: <ScheduleOutlined />,
          label: <Link to="/retail/store/routine-tasks">例行任务</Link>,
        },
        {
          key: '/retail/store/sales-report',
          icon: <FileTextOutlined />,
          label: <Link to="/retail/store/sales-report">销售上报</Link>,
        },
        {
          key: '/retail/store/loss-report',
          icon: <AlertOutlined />,
          label: <Link to="/retail/store/loss-report">报损记录</Link>,
        },
        {
          key: '/retail/store/knowledge',
          icon: <BookOutlined />,
          label: <Link to="/retail/store/knowledge">团队知识库</Link>,
        },
      ],
    },
    
    // 运营菜单
    {
      key: 'operation',
      icon: <AppstoreOutlined />,
      label: '运营',
      children: [
        {
          key: '/retail/operation/dashboard',
          icon: <DashboardOutlined />,
          label: <Link to="/retail/operation/dashboard">运营仪表盘</Link>,
        },
        {
          key: '/retail/operation/super-dashboard',
          icon: <BarChartOutlined />,
          label: <Link to="/retail/operation/super-dashboard">运营大盘</Link>,
        },
        {
          key: '/retail/operation/store-management',
          icon: <ShopOutlined />,
          label: <Link to="/retail/operation/store-management">门店管理</Link>,
        },
        {
          key: '/retail/operation/channel-management',
          icon: <CloudServerOutlined />,
          label: <Link to="/retail/operation/channel-management">渠道管理</Link>,
        },
      ],
    },
    
    // 采购菜单
    {
      key: 'purchase',
      icon: <ShoppingOutlined />,
      label: '采购',
      children: [
        {
          key: '/retail/purchase/dashboard',
          icon: <DashboardOutlined />,
          label: <Link to="/retail/purchase/dashboard">采购仪表盘</Link>,
        },
        {
          key: '/retail/purchase/orders',
          icon: <FileTextOutlined />,
          label: <Link to="/retail/purchase/orders">采购订单</Link>,
        },
        {
          key: '/retail/purchase/products',
          icon: <AppstoreOutlined />,
          label: <Link to="/retail/purchase/products">商品管理</Link>,
        },
        {
          key: '/retail/purchase/suppliers',
          icon: <TeamOutlined />,
          label: <Link to="/retail/purchase/suppliers">供应商管理</Link>,
        },
        {
          key: '/retail/purchase/loss-approval',
          icon: <SolutionOutlined />,
          label: <Link to="/retail/purchase/loss-approval">报损审批</Link>,
        },
      ],
    },
    
    // 财务菜单
    {
      key: 'finance',
      icon: <AccountBookOutlined />,
      label: '财务',
      children: [
        {
          key: '/retail/finance/dashboard',
          icon: <DashboardOutlined />,
          label: <Link to="/retail/finance/dashboard">财务仪表盘</Link>,
        },
        {
          key: '/retail/finance/sales-analysis',
          icon: <BarChartOutlined />,
          label: <Link to="/retail/finance/sales-analysis">销售分析</Link>,
        },
        {
          key: '/retail/finance/inventory-analysis',
          icon: <InboxOutlined />,
          label: <Link to="/retail/finance/inventory-analysis">库存分析</Link>,
        },
        {
          key: '/retail/finance/loss-analysis',
          icon: <AlertOutlined />,
          label: <Link to="/retail/finance/loss-analysis">报损分析</Link>,
        },
      ],
    },
    
    // 仓储菜单
    {
      key: 'inventory',
      icon: <InboxOutlined />,
      label: '仓储',
      children: [
        {
          key: '/retail/inventory/dashboard',
          icon: <DashboardOutlined />,
          label: <Link to="/retail/inventory/dashboard">仓储仪表盘</Link>,
        },
        {
          key: '/retail/inventory/management',
          icon: <AppstoreOutlined />,
          label: <Link to="/retail/inventory/management">库存管理</Link>,
        },
        {
          key: '/retail/inventory/alert',
          icon: <AlertOutlined />,
          label: <Link to="/retail/inventory/alert">库存预警</Link>,
        },
      ],
    },
    
    // 系统设置菜单
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '系统设置',
      children: [
        {
          key: '/retail/settings/users',
          icon: <TeamOutlined />,
          label: <Link to="/retail/settings/users">用户管理</Link>,
        },
        {
          key: '/retail/settings/roles',
          icon: <SolutionOutlined />,
          label: <Link to="/retail/settings/roles">角色管理</Link>,
        },
        {
          key: '/retail/settings/system',
          icon: <SettingOutlined />,
          label: <Link to="/retail/settings/system">系统配置</Link>,
        },
      ],
    },
    
    // AI功能菜单
    {
      key: 'ai',
      icon: <RobotOutlined />,
      label: 'AI功能',
      children: [
        {
          key: '/retail/ai/assistant',
          icon: <RobotOutlined />,
          label: <Link to="/retail/ai/assistant">AI助手</Link>,
        },
        {
          key: '/retail/ai/analysis',
          icon: <BarChartOutlined />,
          label: <Link to="/retail/ai/analysis">AI分析</Link>,
        },
        {
          key: '/retail/ai/knowledge',
          icon: <BookOutlined />,
          label: <Link to="/retail/ai/knowledge">知识库</Link>,
        },
      ],
    },
  ];

  return (
    <Layout className="retail-layout">
      <Sider 
        trigger={null} 
        collapsible 
        collapsed={collapsed}
        width={220}
        theme="light"
        className="retail-sider"
      >
        <div className="logo">
          {!collapsed && <span className="logo-text">零售AI系统</span>}
        </div>
        <Menu
          theme="light"
          mode="inline"
          selectedKeys={getSelectedKeys()}
          defaultOpenKeys={['store', 'operation', 'purchase', 'finance', 'inventory', 'settings', 'ai']}
          items={menuItems}
          className="retail-menu"
        />
      </Sider>
      <Layout>
        <Header className="retail-header" style={{ background: token.colorBgContainer }}>
          <div className="header-left">
            <Button
              type="text"
              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
              onClick={() => setCollapsed(!collapsed)}
              className="trigger-button"
            />
          </div>
          <div className="header-right">
            <Dropdown menu={{ items: notificationMenuItems }} placement="bottomRight" arrow>
              <Badge count={5} className="notification-badge">
                <Button type="text" icon={<BellOutlined />} className="header-button" />
              </Badge>
            </Dropdown>
            <Dropdown menu={{ items: userMenuItems }} placement="bottomRight" arrow>
              <div className="user-info">
                <Avatar icon={<UserOutlined />} className="user-avatar" />
                {!collapsed && <span className="user-name">{user?.username || '用户'}</span>}
              </div>
            </Dropdown>
          </div>
        </Header>
        <Content className="retail-content">
          <Outlet />
        </Content>
      </Layout>
    </Layout>
  );
};

export default RetailLayout;
