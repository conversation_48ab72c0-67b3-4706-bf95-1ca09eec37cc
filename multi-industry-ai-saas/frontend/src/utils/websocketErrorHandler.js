/**
 * WebSocket错误处理工具
 * 统一处理和过滤WebSocket连接错误
 */

import { isHMRWebSocketError, getWebSocketErrorMessage } from './websocketConfig';

/**
 * WebSocket错误类型
 */
export const WS_ERROR_TYPES = {
  HMR_ERROR: 'hmr_error',           // 热重载错误
  NETWORK_ERROR: 'network_error',   // 网络错误
  AUTH_ERROR: 'auth_error',         // 认证错误
  CORS_ERROR: 'cors_error',         // 跨域错误
  TIMEOUT_ERROR: 'timeout_error',   // 超时错误
  UNKNOWN_ERROR: 'unknown_error'    // 未知错误
};

/**
 * 分析WebSocket错误类型
 * @param {Error} error - 错误对象
 * @param {string} url - WebSocket URL
 * @returns {string} 错误类型
 */
export const analyzeWebSocketError = (error, url) => {
  // 检查是否是热重载错误
  if (isHMRWebSocketError(url)) {
    return WS_ERROR_TYPES.HMR_ERROR;
  }
  
  // 检查网络状态
  if (!navigator.onLine) {
    return WS_ERROR_TYPES.NETWORK_ERROR;
  }
  
  const errorMessage = error.message || '';
  
  // 检查CORS错误
  if (errorMessage.includes('CORS') || errorMessage.includes('cross-origin')) {
    return WS_ERROR_TYPES.CORS_ERROR;
  }
  
  // 检查认证错误
  if (errorMessage.includes('401') || errorMessage.includes('403') || errorMessage.includes('auth')) {
    return WS_ERROR_TYPES.AUTH_ERROR;
  }
  
  // 检查超时错误
  if (errorMessage.includes('timeout') || errorMessage.includes('超时')) {
    return WS_ERROR_TYPES.TIMEOUT_ERROR;
  }
  
  return WS_ERROR_TYPES.UNKNOWN_ERROR;
};

/**
 * WebSocket错误过滤器
 * 决定是否应该显示错误信息给用户
 */
export class WebSocketErrorFilter {
  constructor() {
    this.suppressedErrors = new Set();
    this.errorCounts = new Map();
    this.lastErrorTime = new Map();
    
    // 配置
    this.maxErrorCount = 5; // 相同错误最大显示次数
    this.errorCooldown = 60000; // 错误冷却时间（毫秒）
  }
  
  /**
   * 检查是否应该显示错误
   * @param {Error} error - 错误对象
   * @param {string} url - WebSocket URL
   * @returns {boolean} 是否应该显示错误
   */
  shouldShowError(error, url) {
    const errorType = analyzeWebSocketError(error, url);
    
    // 热重载错误直接忽略
    if (errorType === WS_ERROR_TYPES.HMR_ERROR) {
      return false;
    }
    
    const errorKey = `${errorType}:${url}`;
    const now = Date.now();
    
    // 检查是否在冷却期内
    if (this.lastErrorTime.has(errorKey)) {
      const lastTime = this.lastErrorTime.get(errorKey);
      if (now - lastTime < this.errorCooldown) {
        return false;
      }
    }
    
    // 检查错误次数
    const count = this.errorCounts.get(errorKey) || 0;
    if (count >= this.maxErrorCount) {
      return false;
    }
    
    // 更新计数和时间
    this.errorCounts.set(errorKey, count + 1);
    this.lastErrorTime.set(errorKey, now);
    
    return true;
  }
  
  /**
   * 重置错误统计
   * @param {string} errorKey - 错误键（可选，不传则重置所有）
   */
  resetErrorStats(errorKey = null) {
    if (errorKey) {
      this.errorCounts.delete(errorKey);
      this.lastErrorTime.delete(errorKey);
    } else {
      this.errorCounts.clear();
      this.lastErrorTime.clear();
    }
  }
  
  /**
   * 获取错误统计信息
   * @returns {Object} 错误统计
   */
  getErrorStats() {
    const stats = {};
    for (const [key, count] of this.errorCounts.entries()) {
      stats[key] = {
        count,
        lastTime: this.lastErrorTime.get(key)
      };
    }
    return stats;
  }
}

/**
 * 全局WebSocket错误过滤器实例
 */
export const globalErrorFilter = new WebSocketErrorFilter();

/**
 * 处理WebSocket错误的统一入口
 * @param {Error} error - 错误对象
 * @param {string} url - WebSocket URL
 * @param {Function} showErrorCallback - 显示错误的回调函数
 * @returns {boolean} 是否处理了错误
 */
export const handleWebSocketError = (error, url, showErrorCallback) => {
  console.log('处理WebSocket错误:', { error: error.message, url });
  
  // 检查是否应该显示错误
  if (!globalErrorFilter.shouldShowError(error, url)) {
    console.log('WebSocket错误被过滤，不显示给用户');
    return false;
  }
  
  // 获取用户友好的错误消息
  const userMessage = getWebSocketErrorMessage(error, url);
  
  // 调用显示错误的回调
  if (typeof showErrorCallback === 'function') {
    showErrorCallback(userMessage);
  }
  
  return true;
};

/**
 * 监听全局WebSocket错误（开发环境专用）
 */
export const setupGlobalWebSocketErrorHandler = () => {
  if (process.env.NODE_ENV !== 'development') {
    return;
  }
  
  // 拦截WebSocket构造函数
  const OriginalWebSocket = window.WebSocket;
  
  window.WebSocket = function(url, protocols) {
    const ws = new OriginalWebSocket(url, protocols);
    
    // 为每个WebSocket连接添加错误处理
    const originalOnError = ws.onerror;
    
    ws.onerror = function(event) {
      // 检查是否是热重载相关的错误
      if (isHMRWebSocketError(url)) {
        console.log(`[WebSocket HMR] 连接失败: ${url} (这是正常的开发环境现象)`);
        return;
      }
      
      // 调用原始错误处理器
      if (originalOnError) {
        originalOnError.call(this, event);
      }
    };
    
    return ws;
  };
  
  // 保留原始WebSocket的静态属性
  Object.setPrototypeOf(window.WebSocket, OriginalWebSocket);
  Object.defineProperty(window.WebSocket, 'prototype', {
    value: OriginalWebSocket.prototype,
    writable: false
  });
  
  console.log('[WebSocket ErrorHandler] 全局WebSocket错误处理器已启用');
};

/**
 * 清理WebSocket错误统计（用于页面切换时）
 */
export const cleanupWebSocketErrors = () => {
  globalErrorFilter.resetErrorStats();
  console.log('[WebSocket ErrorHandler] 错误统计已清理');
};

const websocketErrorHandler = {
  WS_ERROR_TYPES,
  analyzeWebSocketError,
  WebSocketErrorFilter,
  globalErrorFilter,
  handleWebSocketError,
  setupGlobalWebSocketErrorHandler,
  cleanupWebSocketErrors
};

export default websocketErrorHandler; 