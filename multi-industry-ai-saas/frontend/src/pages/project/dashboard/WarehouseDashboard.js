import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Typography,
  Table,
  Button,
  Spin,
  Empty,
  Tabs,
  List,
  Tag,
  Space,
  Select,
  Tooltip,
  Badge,
  Progress,
  Divider
} from 'antd';
import {
  InboxOutlined,
  WarningOutlined,
  SyncOutlined,
  <PERSON><PERSON><PERSON>Outlined,
  <PERSON><PERSON><PERSON>Outlined,
  RobotOutlined,
  ShopOutlined,
  AlertOutlined,
  ExclamationCircleOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  LikeOutlined,
  DislikeOutlined
} from '@ant-design/icons';
import { Line, Pie, Column } from '@ant-design/plots';
import dayjs from 'dayjs';
import apiService from '../../../services/api';
import { message } from 'antd';
import { useNavigate } from 'react-router-dom';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;

/**
 * 仓储仪表盘组件
 * 展示库存、预警、周转率等仓储数据
 */
const WarehouseDashboard = () => {
  const navigate = useNavigate();
  
  // 状态定义
  const [loading, setLoading] = useState(true);
  const [dashboardData, setDashboardData] = useState(null);
  const [aiLoading, setAiLoading] = useState(false);
  const [feedbackLoading, setFeedbackLoading] = useState(false);
  const [warehouses, setWarehouses] = useState([]);
  const [selectedWarehouse, setSelectedWarehouse] = useState(null);

  // 初始化加载
  useEffect(() => {
    fetchWarehouses();
    fetchDashboardData();
  }, [selectedWarehouse]);

  // 获取仓库列表
  const fetchWarehouses = async () => {
    try {
      const response = await apiService.project.warehouse.getList();
      setWarehouses(response.items || []);
    } catch (error) {
      console.error('获取仓库列表失败:', error);
    }
  };

  // 获取仪表盘数据
  const fetchDashboardData = async () => {
    setLoading(true);
    try {
      const params = {
        warehouse_id: selectedWarehouse
      };

      const response = await apiService.project.dashboard.getWarehouseDashboard(params);
      setDashboardData(response);
    } catch (error) {
      console.error('获取仓储仪表盘数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 刷新AI推荐
  const refreshAIRecommendations = async () => {
    setAiLoading(true);
    try {
      const response = await apiService.project.dashboard.getAIRecommendations('warehouse');
      if (response && response.recommendations) {
        setDashboardData(prev => ({
          ...prev,
          ai_recommendations: response.recommendations
        }));
      }
    } catch (error) {
      console.error('获取AI推荐失败:', error);
    } finally {
      setAiLoading(false);
    }
  };

  // 处理AI推荐反馈
  const handleRecommendationFeedback = async (recommendationId, isHelpful) => {
    setFeedbackLoading(true);
    try {
      await apiService.project.dashboard.submitAIFeedback({
        recommendation_id: recommendationId,
        is_helpful: isHelpful,
        feedback_type: 'warehouse'
      });

      // 更新本地状态，标记反馈已提交
      setDashboardData(prev => ({
        ...prev,
        ai_recommendations: prev.ai_recommendations.map(item =>
          item.id === recommendationId
            ? { ...item, feedback_submitted: true, is_helpful: isHelpful }
            : item
        )
      }));

      message.success(isHelpful ? '感谢您的反馈！' : '感谢您的反馈，我们会继续改进');
    } catch (error) {
      console.error('提交AI推荐反馈失败:', error);
      message.error('提交反馈失败，请重试');
    } finally {
      setFeedbackLoading(false);
    }
  };

  // 处理仓库选择变化
  const handleWarehouseChange = (value) => {
    setSelectedWarehouse(value);
  };

  // 刷新数据
  const handleRefresh = () => {
    fetchDashboardData();
  };

  // 渲染库存趋势图表
  const renderInventoryTrendChart = () => {
    if (!dashboardData || !dashboardData.inventory_trend || dashboardData.inventory_trend.length === 0) {
      return <Empty description="暂无库存趋势数据" />;
    }

    const config = {
      data: dashboardData.inventory_trend,
      xField: 'date',
      yField: 'value',
      seriesField: 'type',
      smooth: true,
      animation: {
        appear: {
          animation: 'path-in',
          duration: 1000
        }
      },
      xAxis: {
        title: {
          text: '日期'
        }
      },
      yAxis: {
        title: {
          text: '库存量'
        }
      },
      tooltip: {
        formatter: (datum) => {
          return { name: '库存量', value: datum.value };
        }
      }
    };

    return <Line {...config} height={300} />;
  };

  // 渲染类别分布图表
  const renderCategoryDistributionChart = () => {
    if (!dashboardData || !dashboardData.inventory_by_category || dashboardData.inventory_by_category.length === 0) {
      return <Empty description="暂无类别分布数据" />;
    }

    const config = {
      data: dashboardData.inventory_by_category,
      angleField: 'value',
      colorField: 'name',
      radius: 0.8,
      label: {
        type: 'outer',
        content: '{name}: {percentage}%'
      },
      interactions: [{ type: 'element-active' }],
      tooltip: {
        formatter: (datum) => {
          return { name: datum.name, value: `${datum.value} (${datum.percentage.toFixed(1)}%)` };
        }
      }
    };

    return <Pie {...config} height={300} />;
  };

  // 渲染AI推荐
  const renderAIRecommendations = () => {
    if (!dashboardData || !dashboardData.ai_recommendations || dashboardData.ai_recommendations.length === 0) {
      return <Empty description="暂无AI推荐" />;
    }

    return (
      <List
        dataSource={dashboardData.ai_recommendations}
        renderItem={(item) => (
          <List.Item>
            <div style={{ width: '100%' }}>
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'flex-start',
                marginBottom: 12
              }}>
                <div>
                  <Text strong style={{ fontSize: 16, marginRight: 8 }}>{item.title}</Text>
                  <Tag color={
                    item.type === '业务优化' ? 'blue' :
                    item.type === '库存优化' ? 'green' :
                    item.type === '成本控制' ? 'orange' :
                    item.type === '风险预警' ? 'red' : 'default'
                  }>
                    {item.type}
                  </Tag>
                  {item.priority && (
                    <Tag color={
                      item.priority === '高' ? 'red' :
                      item.priority === '中' ? 'orange' : 'green'
                    }>
                      {item.priority}优先级
                    </Tag>
                  )}
                </div>
                <div>
                  {item.feedback_submitted ? (
                    <span>
                      {item.is_helpful ?
                        <Tag icon={<LikeOutlined />} color="success">已标记为有帮助</Tag> :
                        <Tag icon={<DislikeOutlined />} color="default">已提交反馈</Tag>
                      }
                    </span>
                  ) : (
                    <Space>
                      <Button
                        type="primary"
                        size="small"
                        icon={<LikeOutlined />}
                        onClick={() => handleRecommendationFeedback && handleRecommendationFeedback(item.id, true)}
                      >
                        有帮助
                      </Button>
                      <Button
                        size="small"
                        icon={<DislikeOutlined />}
                        onClick={() => handleRecommendationFeedback && handleRecommendationFeedback(item.id, false)}
                      >
                        没有帮助
                      </Button>
                    </Space>
                  )}
                </div>
              </div>

              <Paragraph style={{ margin: '12px 0' }}>{item.content}</Paragraph>

              {item.action_items && item.action_items.length > 0 && (
                <div style={{
                  background: '#f5f5f5',
                  padding: '12px 16px',
                  borderRadius: '4px',
                  marginBottom: 12
                }}>
                  <Text strong>建议行动:</Text>
                  <ul style={{ marginTop: 8, marginBottom: 0, paddingLeft: 20 }}>
                    {item.action_items.map((action, index) => (
                      <li key={index}>{action}</li>
                    ))}
                  </ul>
                </div>
              )}

              {item.detailed_analysis && (
                <div style={{ marginTop: 12 }}>
                  <Divider style={{ margin: '12px 0' }} />
                  <Text strong>详细分析:</Text>
                  <Paragraph style={{ margin: '8px 0 0' }}>{item.detailed_analysis}</Paragraph>
                </div>
              )}
            </div>
          </List.Item>
        )}
        style={{ background: '#fff' }}
      />
    );
  };

  // 渲染库存预警表格
  const renderInventoryAlertsTable = () => {
    if (!dashboardData || !dashboardData.inventory_alerts || dashboardData.inventory_alerts.length === 0) {
      return <Empty description="暂无库存预警数据" />;
    }

    const columns = [
      {
        title: '产品名称',
        dataIndex: 'product_name',
        key: 'product_name',
      },
      {
        title: '产品编码',
        dataIndex: 'product_code',
        key: 'product_code',
      },
      {
        title: '当前库存',
        dataIndex: 'current_quantity',
        key: 'current_quantity',
        sorter: (a, b) => a.current_quantity - b.current_quantity,
      },
      {
        title: '阈值',
        dataIndex: 'threshold_quantity',
        key: 'threshold_quantity',
      },
      {
        title: '仓库',
        dataIndex: 'warehouse_name',
        key: 'warehouse_name',
      },
      {
        title: '预警类型',
        dataIndex: 'alert_type',
        key: 'alert_type',
        render: (text) => {
          let color = 'default';
          let label = text;

          if (text === 'low_stock') {
            color = 'red';
            label = '库存不足';
          } else if (text === 'excess_stock') {
            color = 'orange';
            label = '库存过剩';
          } else if (text === 'slow_moving') {
            color = 'blue';
            label = '滞销商品';
          }

          return <Tag color={color}>{label}</Tag>;
        },
        filters: [
          { text: '库存不足', value: 'low_stock' },
          { text: '库存过剩', value: 'excess_stock' },
          { text: '滞销商品', value: 'slow_moving' }
        ],
        onFilter: (value, record) => record.alert_type === value,
      },
      {
        title: '建议操作',
        dataIndex: 'suggested_action',
        key: 'suggested_action',
      }
    ];

    return (
      <Table
        columns={columns}
        dataSource={dashboardData.inventory_alerts}
        rowKey="id"
        pagination={{ pageSize: 5 }}
      />
    );
  };

  // 渲染仓库库存表格
  const renderWarehouseInventoryTable = () => {
    if (!dashboardData || !dashboardData.inventory_by_warehouse || dashboardData.inventory_by_warehouse.length === 0) {
      return <Empty description="暂无仓库库存数据" />;
    }

    const columns = [
      {
        title: '仓库名称',
        dataIndex: 'name',
        key: 'name',
      },
      {
        title: '库存数量',
        dataIndex: 'quantity',
        key: 'quantity',
        sorter: (a, b) => a.quantity - b.quantity,
      },
      {
        title: '库存价值',
        dataIndex: 'value',
        key: 'value',
        render: (text) => `¥${text.toFixed(2)}`,
        sorter: (a, b) => a.value - b.value,
      }
    ];

    return (
      <Table
        columns={columns}
        dataSource={dashboardData.inventory_by_warehouse}
        rowKey="id"
        pagination={{ pageSize: 5 }}
      />
    );
  };

  return (
    <div className="warehouse-dashboard">
      <Card>
        <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Title level={4} style={{ margin: 0 }}>仓储仪表盘</Title>
          <Space>
            <Select
              placeholder="选择仓库"
              allowClear
              style={{ width: 200 }}
              onChange={handleWarehouseChange}
              value={selectedWarehouse}
            >
              {warehouses.map(warehouse => (
                <Option key={warehouse.id} value={warehouse.id}>{warehouse.name}</Option>
              ))}
            </Select>
            <Button
              icon={<SyncOutlined />}
              onClick={handleRefresh}
              loading={loading}
            >
              刷新数据
            </Button>
          </Space>
        </div>

        <Spin spinning={loading}>
          {dashboardData ? (
            <>
              {/* 统计卡片 */}
              <Row gutter={[16, 16]}>
                <Col xs={24} sm={12} md={6}>
                  <Card>
                    <Statistic
                      title="总库存量"
                      value={dashboardData.total_inventory}
                      prefix={<InboxOutlined />}
                      suffix="件"
                      valueStyle={{ color: '#3f8600' }}
                    />
                  </Card>
                </Col>
                <Col xs={24} sm={12} md={6}>
                  <Card>
                    <Statistic
                      title="库存总价值"
                      value={dashboardData.total_value}
                      precision={2}
                      prefix={<ShopOutlined />}
                      suffix="元"
                      valueStyle={{ color: '#1890ff' }}
                    />
                  </Card>
                </Col>
                <Col xs={24} sm={12} md={6}>
                  <Card>
                    <Statistic
                      title="库存周转率"
                      value={dashboardData.turnover_rate}
                      precision={2}
                      prefix={<SyncOutlined />}
                      suffix="次/月"
                      valueStyle={{ color: '#722ed1' }}
                    />
                  </Card>
                </Col>
                <Col xs={24} sm={12} md={6}>
                  <Card>
                    <Statistic
                      title="库存预警"
                      value={dashboardData.low_stock_count + dashboardData.excess_stock_count + dashboardData.slow_moving_count}
                      prefix={<WarningOutlined />}
                      suffix="项"
                      valueStyle={{ color: '#fa8c16' }}
                    />
                    <div className="statistic-footer">
                      <Space>
                        <Badge color="red" text={`库存不足: ${dashboardData.low_stock_count}`} />
                        <Badge color="orange" text={`库存过剩: ${dashboardData.excess_stock_count}`} />
                        <Badge color="blue" text={`滞销商品: ${dashboardData.slow_moving_count}`} />
                      </Space>
                    </div>
                  </Card>
                </Col>
              </Row>

              {/* 快捷入口 */}
              <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
                <Col xs={24} sm={8} md={6} lg={4}>
                  <Card
                    hoverable
                    onClick={() => navigate('/project/warehouse/inventory')}
                    style={{ textAlign: 'center', cursor: 'pointer' }}
                  >
                    <InboxOutlined style={{ fontSize: 24, color: '#1890ff', marginBottom: 8 }} />
                    <div>库存管理</div>
                  </Card>
                </Col>
                <Col xs={24} sm={8} md={6} lg={4}>
                  <Card
                    hoverable
                    onClick={() => navigate('/project/warehouse/alerts')}
                    style={{ textAlign: 'center', cursor: 'pointer' }}
                  >
                    <WarningOutlined style={{ fontSize: 24, color: '#fa8c16', marginBottom: 8 }} />
                    <div>库存预警</div>
                  </Card>
                </Col>
                <Col xs={24} sm={8} md={6} lg={4}>
                  <Card
                    hoverable
                    onClick={() => navigate('/project/warehouse/reports')}
                    style={{ textAlign: 'center', cursor: 'pointer' }}
                  >
                    <LineChartOutlined style={{ fontSize: 24, color: '#52c41a', marginBottom: 8 }} />
                    <div>库存报表</div>
                  </Card>
                </Col>
                <Col xs={24} sm={8} md={6} lg={4}>
                  <Card
                    hoverable
                    onClick={() => navigate('/project/ai/role-assistants?role=warehouse_manager')}
                    style={{ textAlign: 'center', cursor: 'pointer' }}
                  >
                    <RobotOutlined style={{ fontSize: 24, color: '#f5222d', marginBottom: 8 }} />
                    <div>AI助手</div>
                  </Card>
                </Col>
              </Row>

              {/* 图表区域 */}
              <Tabs defaultActiveKey="1" style={{ marginTop: 16 }}>
                <TabPane
                  tab={<span><LineChartOutlined /> 库存趋势</span>}
                  key="1"
                >
                  <Card>
                    {renderInventoryTrendChart()}
                  </Card>
                </TabPane>
                <TabPane
                  tab={<span><PieChartOutlined /> 类别分布</span>}
                  key="2"
                >
                  <Card>
                    {renderCategoryDistributionChart()}
                  </Card>
                </TabPane>
                <TabPane
                  tab={<span><AlertOutlined /> 库存预警</span>}
                  key="3"
                >
                  <Card>
                    {renderInventoryAlertsTable()}
                  </Card>
                </TabPane>
                <TabPane
                  tab={<span><ShopOutlined /> 仓库库存</span>}
                  key="4"
                >
                  <Card>
                    {renderWarehouseInventoryTable()}
                  </Card>
                </TabPane>
              </Tabs>

              {/* AI推荐 */}
              <Card
                title={
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <span><RobotOutlined /> AI推荐</span>
                    <Button
                      icon={<SyncOutlined />}
                      onClick={refreshAIRecommendations}
                      loading={aiLoading}
                      size="small"
                    >
                      刷新AI推荐
                    </Button>
                  </div>
                }
                style={{ marginTop: 16 }}
              >
                {renderAIRecommendations()}
              </Card>
            </>
          ) : (
            <Empty description="暂无数据" />
          )}
        </Spin>
      </Card>
    </div>
  );
};

export default WarehouseDashboard;
