from typing import Optional, Dict, Any, List
from pydantic import BaseModel, Field
import uuid
from datetime import datetime

# --- 基础定义 Schemas ---

class ChannelPlatformBase(BaseModel):
    name: str
    code: str
    nature: str
    logo_url: Optional[str] = None

class ChannelPlatformCreate(ChannelPlatformBase):
    pass

class ChannelPlatformUpdate(BaseModel):
    name: Optional[str] = None
    code: Optional[str] = None
    nature: Optional[str] = None
    logo_url: Optional[str] = None

class ChannelPlatformResponse(ChannelPlatformBase):
    id: uuid.UUID
    created_at: datetime
    updated_at: datetime

    model_config = {"from_attributes": True}

class PlatformServiceBase(BaseModel):
    name: str
    code: str
    platform_id: uuid.UUID

class PlatformServiceCreate(PlatformServiceBase):
    pass

class PlatformServiceUpdate(BaseModel):
    name: Optional[str] = None
    code: Optional[str] = None
    platform_id: Optional[uuid.UUID] = None

class PlatformServiceResponse(PlatformServiceBase):
    id: uuid.UUID
    created_at: datetime
    updated_at: datetime
    platform: Optional[ChannelPlatformResponse] = None

    model_config = {"from_attributes": True}

class BusinessModeBase(BaseModel):
    name: str
    code: str

class BusinessModeCreate(BusinessModeBase):
    pass

class BusinessModeUpdate(BusinessModeBase):
    pass

class BusinessModeResponse(BusinessModeBase):
    id: uuid.UUID
    project_id: uuid.UUID
    created_at: datetime
    updated_at: datetime

    model_config = {"from_attributes": True}

# --- 支付方式 Schemas (重构后) ---

class PaymentMethodBase(BaseModel):
    name: str = Field(..., description="支付方式名称")
    type: Optional[str] = Field(None, description="支付类型 (e.g., online, cash, third-party)")
    is_active: bool = True
    is_default: bool = False
    config: Optional[Dict[str, Any]] = None

class PaymentMethodCreate(PaymentMethodBase):
    pass

class PaymentMethodUpdate(BaseModel):
    name: Optional[str] = None
    type: Optional[str] = None
    is_active: Optional[bool] = None
    is_default: Optional[bool] = None
    config: Optional[Dict[str, Any]] = None

class PaymentMethodResponse(PaymentMethodBase):
    id: uuid.UUID
    project_id: uuid.UUID
    created_at: datetime
    updated_at: datetime

    model_config = {"from_attributes": True}

class PaymentMethodListResponse(BaseModel):
    items: List[PaymentMethodResponse]
    total: int

# --- 销售渠道实例 Schemas (重构后) ---

class SalesChannelBase(BaseModel):
    custom_name: str = Field(..., description="渠道实例的自定义名称")
    service_id: uuid.UUID = Field(..., description="关联的平台服务ID")
    business_mode_id: uuid.UUID = Field(..., description="关联的业务模式ID")
    is_active: bool = True
    config: Optional[Dict[str, Any]] = None
    description: Optional[str] = None

class SalesChannelCreate(SalesChannelBase):
    payment_method_ids: Optional[List[uuid.UUID]] = Field(None, description="该渠道支持的支付方式ID列表")

class SalesChannelUpdate(BaseModel):
    custom_name: Optional[str] = None
    service_id: Optional[uuid.UUID] = None
    business_mode_id: Optional[uuid.UUID] = None
    is_active: Optional[bool] = None

    config: Optional[Dict[str, Any]] = None
    description: Optional[str] = None
    payment_method_ids: Optional[List[uuid.UUID]] = Field(None, description="更新后该渠道支持的支付方式ID列表")

class SalesChannelResponse(SalesChannelBase):
    id: uuid.UUID
    project_id: uuid.UUID
    created_at: datetime
    updated_at: datetime
    service: PlatformServiceResponse
    business_mode: BusinessModeResponse
    payment_methods: List[PaymentMethodResponse] = []

    model_config = {"from_attributes": True}

class SalesChannelListResponse(BaseModel):
    items: List[SalesChannelResponse]
    total: int

# Channel Overview Schemas
class PlatformSales(BaseModel):
    platform_name: str
    sales: float

class DailyTrendItem(BaseModel):
    date: str
    sales: float

class ChannelOverviewResponse(BaseModel):
    total_sales: float
    total_orders: int
    total_channels: int
    active_channels: int
    platform_sales: List[PlatformSales]
    daily_trend: List[DailyTrendItem]

# --- 渠道模板 Schemas (重构后) ---

class ChannelTemplateInstanceBase(BaseModel):
    service_id: uuid.UUID
    business_mode_id: uuid.UUID

class ChannelTemplateInstanceCreate(ChannelTemplateInstanceBase):
    payment_method_ids: List[uuid.UUID] = []

class ChannelTemplateInstanceUpdate(BaseModel):
    id: Optional[uuid.UUID] = None # 用于标识已有实例
    service_id: Optional[uuid.UUID] = None
    business_mode_id: Optional[uuid.UUID] = None
    payment_method_ids: Optional[List[uuid.UUID]] = None

class ChannelTemplateInstanceResponse(ChannelTemplateInstanceBase):
    id: uuid.UUID
    service: PlatformServiceResponse
    business_mode: BusinessModeResponse
    payment_methods: List[PaymentMethodResponse] = []
    
    model_config = {"from_attributes": True}

class SalesChannelTemplateBase(BaseModel):
    name: str
    description: Optional[str] = None

class SalesChannelTemplateCreate(SalesChannelTemplateBase):
    instances: List[ChannelTemplateInstanceCreate] = []

class SalesChannelTemplateUpdate(SalesChannelTemplateBase):
    name: Optional[str] = None
    instances: List[ChannelTemplateInstanceUpdate] = []

class SalesChannelTemplateResponse(SalesChannelTemplateBase):
    id: uuid.UUID
    project_id: uuid.UUID
    instances: List[ChannelTemplateInstanceResponse] = []
    created_at: datetime
    updated_at: datetime

    model_config = {"from_attributes": True}

class SalesChannelTemplateListResponse(BaseModel):
    items: List[SalesChannelTemplateResponse]
    total: int
