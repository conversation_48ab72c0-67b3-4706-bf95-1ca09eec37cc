#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
MCP代理服务

提供MCP服务器的代理功能，将请求转发到实际的MCP服务器
使用Redis进行服务发现和配置管理
"""

import json as _json
import logging
import os
import time
import asyncio
from typing import Dict, Any, Optional, List, Tuple, AsyncGenerator # Added Tuple and AsyncGenerator
import uuid # 添加uuid导入

import httpx
from fastapi import Request, HTTPException
from fastapi.responses import JSONResponse, Response, StreamingResponse
from redis.asyncio import Redis
from starlette.background import BackgroundTask

# 导入配置
from core.config import settings # Assuming core.config.settings is your Pydantic settings instance

# 配置日志
logger = logging.getLogger(__name__)
# logging.basicConfig(level=logging.INFO if settings.DEBUG else logging.INFO) # Set level based on settings

# Redis键前缀
REDIS_KEY_PREFIX = "mcp:server:"
V1_REDIS_KEY_PREFIX = "v1:servers:" # 前端API查询格式前缀
MCP_SESSION_KEY_PREFIX = "mcp:session:" # 新增会话存储键前缀
MCP_SESSION_INIT_TTL_DEFAULT = 300 # 新增会话初始化TTL默认值 (秒)

# 导入认证相关工具
from .auth import verify_jwt_token as local_verify_jwt_token, TokenType, TokenPermission

# 辅助函数：从payload中获取user_id
def get_user_id_from_payload(payload: Dict[str, Any]) -> Optional[str]:
    """从令牌payload中提取user_id"""
    if not payload:
        return None
    return str(payload.get("user_id", "")) or None

# 辅助函数：从payload中获取project_id
def get_project_id_from_payload(payload: Dict[str, Any]) -> Optional[str]:
    """从令牌payload中提取project_id"""
    if not payload:
        return None
    return str(payload.get("project_id", "")) or None


# 模块级：统一MCP访问权限校验工具 (与你提供的一致)
def verify_mcp_access(token: str, server_id: str) -> Dict[str, Any]:
    """
    本地校验MCP令牌（原SAPI插件令牌）的结构、类型和权限。
    依赖于本地JWT解码和验证函数 (local_verify_jwt_token)。
    返回解码后的payload如果成功，否则抛出HTTPException。
    """
    try:
        payload = local_verify_jwt_token(token) # From .auth module
    except HTTPException as he: # verify_jwt_token throws HTTPException on failure
        logger.error(f"本地JWT令牌解码/验证失败 (HTTPException): {he.detail}", exc_info=settings.DEBUG)
        raise he # Re-throw the HTTPException
    except Exception as e: # Catch other potential errors from jwt.decode
        logger.error(f"本地JWT令牌解码/验证失败 (Generic Exception): {e}", exc_info=settings.DEBUG)
        raise HTTPException(status_code=401, detail=f"令牌无效或解析错误: {str(e)}")

    if payload.get("type") != TokenType.MCP:
        raise HTTPException(status_code=400, detail=f"不支持的令牌类型: {payload.get('type')}, 需要MCP类型")
    if "project_id" not in payload or not payload.get("project_id"):
        raise HTTPException(status_code=400, detail="MCP令牌缺少project_id")
    if "user_id" not in payload or not payload.get("user_id"):
        raise HTTPException(status_code=400, detail="MCP令牌缺少user_id")

    server_permissions_on_token = payload.get("server_permissions", {})
    default_permissions_on_token = payload.get("default_permissions", [])

    if server_id not in server_permissions_on_token:
        logger.warning(f"令牌中未明确包含服务器 {server_id} 的权限，检查是否有默认权限")
        if not default_permissions_on_token:
            raise HTTPException(status_code=403, detail=f"无权访问服务器 {server_id} (令牌中既未指定此服务器权限，也没有默认权限)")
        perms_for_this_server = default_permissions_on_token
    else:
        perms_for_this_server = server_permissions_on_token[server_id]

    if not perms_for_this_server:
        raise HTTPException(status_code=403, detail=f"无权访问服务器 {server_id} (令牌中指定的权限为空)")

    has_valid_permission = any(p in [TokenPermission.READ, TokenPermission.READ_WRITE] for p in perms_for_this_server)
    if not has_valid_permission:
        raise HTTPException(status_code=403, detail=f"对服务器 {server_id} 权限不足 (需要 '{TokenPermission.READ}' 或 '{TokenPermission.READ_WRITE}')")

    logger.info(f"本地MCP令牌对服务器 {server_id} 的权限校验通过。")
    return payload


class MCPProxy:
    """MCP代理服务"""

    def __init__(self, redis_url: Optional[str] = None):
        redis_password_val = None
        if redis_url is None:
            redis_host = settings.REDIS_HOST
            redis_port = settings.REDIS_PORT
            redis_db = settings.REDIS_DB
            redis_password_val = settings.REDIS_PASSWORD
            if redis_password_val:
                self.redis_url = f"redis://:{redis_password_val}@{redis_host}:{redis_port}/{redis_db}"
            else:
                self.redis_url = f"redis://{redis_host}:{redis_port}/{redis_db}"
        else:
            self.redis_url = redis_url
        logger.info(f"MCP代理使用Redis: {self.redis_url.replace(redis_password_val if redis_password_val else '---NEVERMATCH---', '****')}")
        self.redis_client: Optional[Redis] = None

    async def get_redis(self) -> Redis:
        # Your existing get_redis logic, simplified for brevity if always creating new
        # Or use a more robust pooling/connection management if preferred
        try:
            # Re-creating connection each time as per your original code for simplicity here.
            # Consider a shared client or pool for high-load scenarios.
            redis_conn = await Redis.from_url(self.redis_url, decode_responses=True)
            await redis_conn.ping()
            logger.debug("成功连接到Redis并ping通 (new connection)")
            return redis_conn # Return the new connection
        except Exception as e:
            logger.error(f"连接到Redis失败: {e}", exc_info=settings.DEBUG)
            raise # Re-raise to be caught by caller

    # --- 服务注册/注销/获取/列表 (保持你提供的实现，确保 get_server 返回的字典包含 port_http 和 port_sse) ---
    async def register_server(self, server_id: str, host: str = None, port: int = None, metadata: Dict[str, Any] = None) -> bool:
        # ... (Your existing implementation, ensure metadata includes port_http, port_sse)
        # Example: if metadata: metadata.update({"port_http": X, "port_sse": Y})
        # This should be handled by the MCP server instance when it registers itself.
        # The proxy's register_server might be for other purposes or a direct utility.
        # For this proxy to work, the MCP server *must* register its port_http and port_sse.
        try:
            redis = await self.get_redis()
            if not host:
                if os.environ.get("K8S_ENV") == "true": host = f"mcp-server-{server_id}-svc"
                elif os.environ.get("DOCKER_ENV") == "true": host = f"mcp-server-{server_id}"
                else: host = "localhost"
            server_data_to_set = {"id": server_id, "host": host, "status": "running"}
            if port is not None: # Original code had 'port', let's assume it means a default/primary port
                 server_data_to_set["port"] = str(port)

            # Critical: metadata should contain port_http and port_sse if not top-level
            # For example, if your MCP server registration does:
            # metadata = {"port_http": 8000, "port_sse": 8001, "other_meta": "value"}
            if metadata: server_data_to_set["metadata"] = _json.dumps(metadata)

            key_name = f"{REDIS_KEY_PREFIX}{server_id}"
            # ... (rest of your hmset and expire logic) ...
            await redis.hmset(key_name, server_data_to_set)
            await redis.expire(key_name, 86400)
            logger.info(f"已注册/更新MCP服务器 (hash): {server_id} ({host}), 主端口: {port}, 元数据: {metadata is not None}")
            await redis.close() # Close the standalone connection
            return True
        except Exception as e: logger.error(f"注册MCP服务器失败: {str(e)}", exc_info=settings.DEBUG); return False

    async def get_server(self, server_id: str) -> Optional[Dict[str, Any]]:
        # ... (Your existing implementation, ensure it correctly parses port_http, port_sse)
        # ... Ensure port_http and port_sse are retrieved, possibly from metadata if not top-level keys
        try:
            redis = await self.get_redis()
            mcp_key_name = f"{REDIS_KEY_PREFIX}{server_id}"
            server_info_hash = await redis.hgetall(mcp_key_name)
            await redis.close() # Close the standalone connection

            if not server_info_hash: # Try v1 key if mcp key not found
                v1_key_name = f"{V1_REDIS_KEY_PREFIX}{server_id}"
                redis_v1 = await self.get_redis() # New connection for this attempt
                server_info_hash = await redis_v1.hgetall(v1_key_name)
                await redis_v1.close()
                if not server_info_hash:
                    logger.info(f"在Redis中未找到服务器信息: 尝试了 {mcp_key_name} 和 {v1_key_name}")
                    return None
                logger.warning(f"在Redis中找到服务器信息，但使用的是旧格式键: {v1_key_name}")
            else:
                logger.info(f"在Redis中找到服务器信息: {mcp_key_name}")

            processed_info = {}
            for field, value in server_info_hash.items():
                if field in ["port", "port_http", "port_sse"] and value is not None and value != "":
                    try: processed_info[field] = int(value)
                    except ValueError: logger.warning(f"无法将服务器 {server_id} 的端口字段 '{field}' 值 '{value}' 转换为整数。"); processed_info[field] = value
                elif field == "metadata" and isinstance(value, str):
                    try:
                        metadata_dict = _json.loads(value)
                        processed_info[field] = metadata_dict
                        # Attempt to pull port_http and port_sse from metadata if not top-level
                        if "port_http" not in processed_info and "port_http" in metadata_dict:
                            try: processed_info["port_http"] = int(metadata_dict["port_http"])
                            except: logger.warning(f"元数据中的port_http无效: {metadata_dict['port_http']}")
                        if "port_sse" not in processed_info and "port_sse" in metadata_dict:
                            try: processed_info["port_sse"] = int(metadata_dict["port_sse"])
                            except: logger.warning(f"元数据中的port_sse无效: {metadata_dict['port_sse']}")
                    except _json.JSONDecodeError: logger.warning(f"无法解析服务器 {server_id} 的 metadata JSON: {value}"); processed_info[field] = value
                else: processed_info[field] = value
            if 'id' not in processed_info: processed_info['id'] = server_id
            return processed_info
        except Exception as e: logger.error(f"获取MCP服务器 {server_id} 信息失败: {str(e)}", exc_info=settings.DEBUG); return None
    # ... (unregister_server, update_server_status, list_servers as you provided)
    async def unregister_server(self, server_id: str) -> bool:
        try: redis = await self.get_redis(); await redis.delete(f"{REDIS_KEY_PREFIX}{server_id}"); await redis.close(); logger.info(f"已注销MCP服务器: {server_id}"); return True
        except Exception as e: logger.error(f"注销MCP服务器失败: {str(e)}", exc_info=settings.DEBUG); return False

    async def update_server_status(self, server_id: str, status: str) -> bool:
        try:
            redis = await self.get_redis(); key_name = f"{REDIS_KEY_PREFIX}{server_id}"
            if not await redis.exists(key_name): logger.warning(f"尝试更新不存在的MCP服务器状态: {server_id}"); await redis.close(); return False
            await redis.hset(key_name, "status", status); await redis.close(); logger.info(f"已更新MCP服务器状态: {server_id} -> {status}"); return True
        except Exception as e: logger.error(f"更新MCP服务器状态失败: {str(e)}", exc_info=settings.DEBUG); return False

    async def list_servers(self) -> List[Dict[str, Any]]:
        try:
            redis = await self.get_redis(); keys = await redis.keys(f"{REDIS_KEY_PREFIX}*"); servers = []
            for key in keys: # This could be slow with many keys, consider redis.scan_iter
                server_info_hash = await redis.hgetall(key)
                if server_info_hash: # Process as in get_server
                    processed_info = {}; # ... (same processing logic as get_server) ...
                    for field, value in server_info_hash.items():
                        if field in ["port", "port_http", "port_sse"] and value is not None and value != "":
                            try: processed_info[field] = int(value)
                            except ValueError: processed_info[field] = value
                        elif field == "metadata" and isinstance(value, str):
                            try:
                                metadata_dict = _json.loads(value)
                                processed_info[field] = metadata_dict
                                if "port_http" not in processed_info and "port_http" in metadata_dict:
                                    try: processed_info["port_http"] = int(metadata_dict["port_http"])
                                    except: pass
                                if "port_sse" not in processed_info and "port_sse" in metadata_dict:
                                    try: processed_info["port_sse"] = int(metadata_dict["port_sse"])
                                    except: pass
                            except _json.JSONDecodeError: processed_info[field] = value
                        else: processed_info[field] = value
                    actual_server_id = key.split(REDIS_KEY_PREFIX, 1)[1]
                    processed_info['id'] = actual_server_id
                    servers.append(processed_info)
            await redis.close()
            return servers
        except Exception as e: logger.error(f"列出MCP服务器失败: {str(e)}", exc_info=settings.DEBUG); return []

    async def _get_system_token_for_forwarding(self, original_mcp_token: str, original_mcp_payload: Dict[str, Any], server_id: str) -> str:
        """辅助方法：将MCP令牌转换为系统令牌，用于转发请求。"""
        try:
            conversion_payload = {
                "plugin_token": original_mcp_token,
                "user_id": original_mcp_payload.get("user_id"),
                "project_id": original_mcp_payload.get("project_id"),
                "server_id": server_id,
                "server_permissions": original_mcp_payload.get("server_permissions")
            }
            # 使用 settings.API_BASE_URL, 假设这是主API的地址
            # 如果令牌转换是不同的服务，应有专门的配置项
            convert_url = f"{settings.API_BASE_URL}/api/v1/auth/convert-token"
            logger.info(f"[MCP代理-TokenConvert] 令牌转换请求URL: {convert_url}")

            async with httpx.AsyncClient(timeout=settings.DEFAULT_HTTP_TIMEOUT) as client:
                convert_response = await client.post(convert_url, json=conversion_payload)

            if convert_response.status_code == 200:
                system_token_data = convert_response.json()
                system_token = system_token_data.get("access_token") or system_token_data.get("token")
                if not system_token:
                    logger.error(f"[MCP代理-TokenConvert] /convert-token 未返回access_token或token: {system_token_data}")
                    raise HTTPException(status_code=500, detail="系统令牌转换失败 (无access_token或token)")
                logger.info(f"[MCP代理-TokenConvert] 成功获取系统令牌 (前16位): {system_token[:16]}...")
                return system_token
            else:
                error_message = f"系统令牌转换API调用失败: {convert_response.status_code}"
                try:
                    error_detail = convert_response.json()
                    error_message = error_detail.get("detail", error_message)
                except: pass
                logger.error(f"[MCP代理-TokenConvert] /convert-token 转换失败: {convert_response.status_code} - {convert_response.text}")
                raise HTTPException(status_code=convert_response.status_code, detail=error_message)
        except httpx.RequestError as e_req:
            logger.error(f"[MCP代理-TokenConvert] 系统令牌转换时发生HTTP请求错误: {e_req}", exc_info=settings.DEBUG)
            raise HTTPException(status_code=503, detail=f"系统令牌转换服务连接失败: {str(e_req)}")
        except HTTPException:
            raise
        except Exception as e_convert:
            logger.error(f"[MCP代理-TokenConvert] 系统令牌转换过程中发生意外错误: {e_convert}", exc_info=settings.DEBUG)
            raise HTTPException(status_code=500, detail=f"系统令牌转换处理异常: {str(e_convert)}")

    async def _prepare_forward_headers_and_auth(
        self, server_id: str, original_request_headers_obj: Any # Actually starlette.datastructures.Headers
    ) -> Tuple[Dict[str, str], Dict[str, Any]]:
        """准备转发头和验证原始MCP令牌，返回 (转发头, 原始MCP Payload)
        如果请求是针对SSE初始化路径，则允许从查询参数 'token' 或 'auth_token' 获取令牌。
        """
        # --- 增强版头部调试日志 ---
        logger.info(f"[MCP代理-Headers] Headers object type: {type(original_request_headers_obj)}")

        # 尝试所有可能的方式获取 mcp-session-id
        mcp_session_id_lowercase = None
        mcp_session_id_camelcase = None
        mcp_session_id_uppercase = None

        # 方法1: 直接通过get获取不同大小写版本
        if hasattr(original_request_headers_obj, 'get'):
            mcp_session_id_lowercase = original_request_headers_obj.get("mcp-session-id")
            mcp_session_id_camelcase = original_request_headers_obj.get("Mcp-Session-Id")
            mcp_session_id_uppercase = original_request_headers_obj.get("MCP-SESSION-ID")
            logger.info(f"[MCP代理-Headers] Direct get - lowercase: '{mcp_session_id_lowercase}', camelcase: '{mcp_session_id_camelcase}', uppercase: '{mcp_session_id_uppercase}'")

        # 方法2: 将headers转换成字典并检查所有键
        headers_dict = {}
        mcp_session_id_from_dict = None

        if hasattr(original_request_headers_obj, 'items'):
            headers_dict = dict(original_request_headers_obj.items())
            logger.info(f"[MCP代理-Headers] Headers as dict: {headers_dict}")

            # 遍历所有头部，查找任何大小写形式的mcp-session-id
            for key, value in headers_dict.items():
                logger.info(f"[MCP代理-Headers] Checking header key: '{key}', value: '{value}'")
                if key.lower() == "mcp-session-id":
                    mcp_session_id_from_dict = value
                    logger.info(f"[MCP代理-Headers] Found mcp-session-id in dict with key '{key}': '{value}'")
                    break

        # 确定最终要使用的会话ID值
        final_mcp_session_id = mcp_session_id_from_dict or mcp_session_id_camelcase or mcp_session_id_lowercase or mcp_session_id_uppercase

        logger.info(f"[MCP代理-Headers] Final determined mcp-session-id: '{final_mcp_session_id}'")

        # --- 原始逻辑继续 ---
        # 创建要转发的头部字典 - 但不使用原来的大小写转换逻辑，保留原始键
        headers_to_forward = {}

        # 复制所有头部，但排除一些特定头部
        excluded_headers = {'host', 'content-length', 'transfer-encoding', 'connection'}
        for key, value in headers_dict.items():
            if key.lower() not in excluded_headers:
                headers_to_forward[key] = value  # 保留原始大小写的键

        # 明确处理Mcp-Session-Id - 如果找到了有效值，使用标准化的小写键添加到转发头
        if final_mcp_session_id:
            headers_to_forward["mcp-session-id"] = final_mcp_session_id
            logger.info(f"[MCP代理-Headers] Adding mcp-session-id to forwarded headers: '{final_mcp_session_id}'")

        # --- 验证令牌逻辑 ---
        original_mcp_token = None
        # 尝试从 Authorization 头部获取令牌
        auth_header = None
        for key, value in headers_dict.items():
            if key.lower() == "authorization":
                auth_header = value
                break

        if auth_header and auth_header.lower().startswith("bearer "):
            original_mcp_token = auth_header.split(" ", 1)[1]

        if not original_mcp_token:
            x_auth_token = None
            for key, value in headers_dict.items():
                if key.lower() == "x-auth-token-from-query":
                    x_auth_token = value
                    break

            if x_auth_token:
                original_mcp_token = x_auth_token
            logger.info("[MCP代理-Auth] 从 x-auth-token-from-query (来源于查询参数) 获取到令牌用于SSE初始化。")

        if not original_mcp_token:
            logger.warning("[MCP代理-Auth] 未能从 Authorization 头部或 x-auth-token-from-query 获取到认证令牌。")
            raise HTTPException(status_code=401, detail="未提供有效认证令牌")

        logger.debug(f"[MCP代理-Auth] 开始本地MCP令牌校验 (for server_id: {server_id})")
        original_mcp_payload = verify_mcp_access(original_mcp_token, server_id) # 使用模块级函数
        logger.info(f"[MCP代理-Auth] 本地MCP令牌校验通过。 User: {original_mcp_payload.get('user_id')}, Proj: {original_mcp_payload.get('project_id')}")

        system_jwt_for_mcp_instance = await self._get_system_token_for_forwarding(original_mcp_token, original_mcp_payload, server_id)

        # 正确设置转发给MCP实例的头部
        headers_to_forward["x-mcp-system-token"] = system_jwt_for_mcp_instance # 使用正确的头部名称

        # 标准化删除任何形式的authorization头
        for key in list(headers_to_forward.keys()):
            if key.lower() == "authorization":
                del headers_to_forward[key]

        # 添加用户和项目ID头部
        if original_mcp_payload.get("user_id"):
            headers_to_forward["x-mcp-user-id"] = str(original_mcp_payload["user_id"])
        if original_mcp_payload.get("project_id"):
            headers_to_forward["x-mcp-project-id"] = str(original_mcp_payload["project_id"])

        # 添加权限头部
        server_perms_on_token = original_mcp_payload.get("server_permissions", {})
        default_perms_on_token = original_mcp_payload.get("default_permissions", [])
        perms_for_this_server = server_perms_on_token.get(server_id, default_perms_on_token)
        if perms_for_this_server:
            headers_to_forward["x-mcp-permissions"] = _json.dumps(perms_for_this_server)
            logger.info(f"[MCP代理-Auth] 添加 x-mcp-permissions 头: {perms_for_this_server}")

        # 最后记录所有将要发送的头部
        logger.info(f"[MCP代理-PRE_STREAM_HEADERS] Headers to be sent: {headers_to_forward}")

        return headers_to_forward, original_mcp_payload

    async def _get_backend_target_info(
        self, server_id: str, protocol: str
    ) -> Tuple[int, List[str]]: # Removed determined_host as it's part of hosts_to_try logic
        """获取后端目标服务器信息 (port_to_use, hosts_to_try)"""
        server_info = await self.get_server(server_id)
        if not server_info:
            raise HTTPException(status_code=404, detail=f"MCP服务器 {server_id} 不存在或未运行 (Redis中未找到)")
        if server_info.get("status") != "running":
            raise HTTPException(status_code=503, detail=f"MCP服务器 {server_id} 状态非运行: {server_info.get('status')}")

        port_key = "port_http" if protocol == "streamhttp" else "port_sse"
        port_value = server_info.get(port_key)

        if port_value is None or str(port_value).strip() == "": # Check if None or empty string
            msg = f"MCP服务器 {server_id} 配置错误 (无有效 {protocol} 端口: {port_key})"
            logger.error(f"[MCP代理-{protocol.upper()}] {msg}")
            raise HTTPException(status_code=503, detail=msg)
        try:
            port_to_use = int(port_value)
        except (ValueError, TypeError):
            msg = f"MCP服务器 {server_id} 配置错误 ({protocol} 端口 '{port_value}' 不是有效整数)"
            logger.error(f"[MCP代理-{protocol.upper()}] {msg}")
            raise HTTPException(status_code=503, detail=msg)

        host_from_redis = server_info.get("host", f"mcp-server-{server_id}") # Default to container name pattern

        hosts_to_try = []
        # Prefer K8S/Docker service name if applicable
        if os.environ.get("K8S_ENV") == "true":
            hosts_to_try.append(f"mcp-server-{server_id}-svc")
        elif os.environ.get("DOCKER_ENV") == "true":
            hosts_to_try.append(f"mcp-server-{server_id}")

        # Add host from Redis if it's different and not localhost/127.0.0.1 (unless not in K8S/Docker)
        if host_from_redis and host_from_redis not in hosts_to_try:
            if host_from_redis not in ["localhost", "127.0.0.1"] or \
               (os.environ.get("DOCKER_ENV") != "true" and os.environ.get("K8S_ENV") != "true"):
                hosts_to_try.append(host_from_redis)

        # Fallback to localhost/127.0.0.1 if not in K8S/Docker and not already added
        if os.environ.get("DOCKER_ENV") != "true" and os.environ.get("K8S_ENV") != "true":
            if "localhost" not in hosts_to_try: hosts_to_try.append("localhost")
            if "127.0.0.1" not in hosts_to_try: hosts_to_try.append("127.0.0.1")

        if not hosts_to_try: # Absolute fallback if no hosts were determined
            logger.warning(f"[MCP代理-{protocol.upper()}] 未能确定任何有效主机进行尝试 for {server_id}, 将使用 'localhost'.")
            hosts_to_try.append("localhost")

        logger.info(f"[MCP代理-{protocol.upper()}] 目标端口: {port_to_use}, 将按顺序尝试主机: {hosts_to_try}")
        return port_to_use, hosts_to_try


    async def get_session_data(self, session_id: str) -> Optional[Dict[str, Any]]:
        """
        获取会话数据

        Args:
            session_id: 会话ID

        Returns:
            Optional[Dict[str, Any]]: 会话数据，如果会话不存在则返回None
        """
        try:
            redis = await self.get_redis()
            session_key = f"{MCP_SESSION_KEY_PREFIX}{session_id}"

            # 检查会话是否存在
            if not await redis.exists(session_key):
                logger.warning(f"[MCP代理-GetSession] 会话 {session_id} 不存在")
                await redis.close()
                return None

            # 获取会话数据
            session_data_str = await redis.get(session_key)
            await redis.close()

            if not session_data_str:
                logger.warning(f"[MCP代理-GetSession] 会话 {session_id} 存在但数据为空")
                return None

            try:
                session_data = _json.loads(session_data_str)
                return session_data
            except _json.JSONDecodeError:
                logger.error(f"[MCP代理-GetSession] 会话 {session_id} 数据不是有效的JSON: {session_data_str}")
                return None
        except Exception as e:
            logger.error(f"[MCP代理-GetSession] 获取会话 {session_id} 数据时出错: {str(e)}", exc_info=settings.DEBUG)
            return None

    async def terminate_session(self, session_id: str) -> bool:
        """
        终止会话

        Args:
            session_id: 会话ID

        Returns:
            bool: 是否成功终止会话
        """
        try:
            redis = await self.get_redis()
            session_key = f"{MCP_SESSION_KEY_PREFIX}{session_id}"

            # 检查会话是否存在
            if not await redis.exists(session_key):
                logger.warning(f"[MCP代理-TerminateSession] 会话 {session_id} 不存在，无法终止")
                await redis.close()
                return False

            # 删除会话
            await redis.delete(session_key)
            logger.info(f"[MCP代理-TerminateSession] 会话 {session_id} 已终止")
            await redis.close()
            return True
        except Exception as e:
            logger.error(f"[MCP代理-TerminateSession] 终止会话 {session_id} 时出错: {str(e)}", exc_info=settings.DEBUG)
            return False

    async def extend_session_ttl(self, session_id: str, ttl_seconds: int = 3600) -> bool:
        """
        延长会话的TTL时间

        Args:
            session_id: 会话ID
            ttl_seconds: 新的TTL时间（秒）

        Returns:
            bool: 是否成功延长TTL
        """
        try:
            redis = await self.get_redis()
            session_key = f"{MCP_SESSION_KEY_PREFIX}{session_id}"

            # 检查会话是否存在
            if not await redis.exists(session_key):
                logger.warning(f"[MCP代理-ExtendSession] 会话 {session_id} 不存在，无法延长TTL")
                await redis.close()
                return False

            # 获取会话数据
            session_data_str = await redis.get(session_key)
            if not session_data_str:
                logger.warning(f"[MCP代理-ExtendSession] 会话 {session_id} 存在但数据为空")
                await redis.close()
                return False

            # 更新会话的最后活动时间
            try:
                session_data = _json.loads(session_data_str)
                session_data["last_activity_time"] = time.time()
                await redis.set(session_key, _json.dumps(session_data), ex=ttl_seconds)
                logger.info(f"[MCP代理-ExtendSession] 会话 {session_id} TTL已延长至 {ttl_seconds}秒")
                await redis.close()
                return True
            except _json.JSONDecodeError:
                logger.error(f"[MCP代理-ExtendSession] 会话 {session_id} 数据不是有效的JSON: {session_data_str}")
                await redis.close()
                return False
        except Exception as e:
            logger.error(f"[MCP代理-ExtendSession] 延长会话 {session_id} TTL时出错: {str(e)}", exc_info=settings.DEBUG)
            return False

    async def proxy_request_to_streamhttp(
        self, server_id: str, request: Request
    ) -> Tuple[int, Dict[str, str], AsyncGenerator[bytes, None]]: # Modified return type
        """
        代理MCP请求到后端MCP服务器的StreamHTTP端口。
        返回: (status_code, headers, content_generator)
        content_generator 是一个异步生成器，用于流式传输响应体。
        """
        actual_port_to_use, hosts_to_try = await self._get_backend_target_info(server_id, "streamhttp")

        # --- BEGIN IMMEDIATE request.headers CHECK in proxy_request_to_streamhttp ---
        logger.info(f"[MCP代理-StreamHTTP-RAW_REQ_HEADERS] Type of request.headers: {type(request.headers)}")
        mcp_session_from_req_headers_lower = request.headers.get("mcp-session-id")
        mcp_session_from_req_headers_camel = request.headers.get("Mcp-Session-Id")
        logger.info(f"[MCP代理-StreamHTTP-RAW_REQ_HEADERS] Direct get 'mcp-session-id' from request.headers: {mcp_session_from_req_headers_lower}")
        logger.info(f"[MCP代理-StreamHTTP-RAW_REQ_HEADERS] Direct get 'Mcp-Session-Id' from request.headers: {mcp_session_from_req_headers_camel}")
        logger.info(f"[MCP代理-StreamHTTP-RAW_REQ_HEADERS] All request.headers items: {list(request.headers.items())}")
        # --- END IMMEDIATE request.headers CHECK ---

        if actual_port_to_use is None: # Should be caught by _get_backend_target_info raising HTTPException
            logger.error(f"无法确定服务器 {server_id} 的 StreamHTTP 端口 (port_http)。")
            async def error_gen_no_port():
                # Try to parse request body for JSON-RPC id if possible
                req_id = None
                try:
                    content = await request.body() # Read body once
                    if content:
                        json_body = _json.loads(content.decode())
                        req_id = json_body.get("id")
                except: # Ignore parsing errors
                    pass
                yield _json.dumps({
                    "jsonrpc": "2.0",
                    "error": {"code": -32003, "message": "Proxy error: Backend server HTTP port not configured"},
                    "id": req_id
                }).encode()
            return 500, {"Content-Type": "application/json", "X-Proxy-Error": "HTTP Port not found"}, error_gen_no_port()

        request_content = await request.body() # Already read if req_id parsing happened, consider passing content
        # If request_content was already read for req_id, ensure it's available for client.stream
        # This might require passing the content if it's read early.
        # For now, assuming request.body() can be called again or httpx handles it.
        # A safer approach is to read request.body() once at the beginning of the function.
        # Let's assume request.body() can be called multiple times or the first call's result is implicitly reused.
        # If not, this line should use a variable that stored the body if parsed for req_id.

        proxy_headers, _ = await self._prepare_forward_headers_and_auth(server_id, dict(request.headers)) # 不需要使用original_payload

        # Log the mcp-session-id that will be forwarded (obtained from _prepare_forward_headers_and_auth)
        forwarded_mcp_session_id = proxy_headers.get("mcp-session-id")
        logger.info(f"[MCP代理-转发] mcp-session-id 将被转发到 MCP 服务器: {forwarded_mcp_session_id}")

        # --- 添加额外的头部诊断日志 ---
        all_proxy_headers_keys = list(proxy_headers.keys())
        logger.info(f"[MCP代理-转发-头部] 所有将被转发的头部键: {all_proxy_headers_keys}")

        # 特别检查所有与 mcp-session-id 相关的键（不区分大小写）
        session_related_headers = {}
        for key, value in proxy_headers.items():
            if "session" in key.lower():
                session_related_headers[key] = value
        if session_related_headers:
            logger.info(f"[MCP代理-转发-头部] 所有与会话相关的头部: {session_related_headers}")

        # --- BEGIN TEMPORARY DEBUG LOGGING ---
        try:
            if request_content:
                json_body = _json.loads(request_content.decode())
                rpc_method = json_body.get("method")
                # 在 mcp_instance_target_path 定义之后记录，确保 target_path 可用
                # logger.info(f"[MCP代理-DEBUG] Forwarding JSON-RPC method: {rpc_method} to server {server_id} at {mcp_instance_target_path}. Request ID: {json_body.get('id')}")
                logger.info(f"[MCP代理-DEBUG] Raw request content to {server_id}: {request_content.decode()[:500]}...") # Log raw content
                logger.info(f"[MCP代理-DEBUG] JSON-RPC method: {rpc_method}. Request ID: {json_body.get('id')}")
                logger.info(f"[MCP代理-DEBUG] Forwarding Headers to MCP Server for method {rpc_method}: {proxy_headers}")

                # 检查是否是ping请求，如果是，则延长会话TTL
                if rpc_method == "ping":
                    # 从请求头中获取会话ID
                    session_id = None
                    for key, value in request.headers.items():
                        if key.lower() == "mcp-session-id":
                            session_id = value
                            break

                    if session_id:
                        logger.info(f"[MCP代理-Ping] 收到ping请求，会话ID: {session_id}，延长TTL")
                        # 异步延长会话TTL，但不等待结果，避免阻塞请求
                        asyncio.create_task(self.extend_session_ttl(session_id))

                # 检查是否是会话终止请求
                elif rpc_method == "session/terminate":
                    # 从请求头中获取会话ID
                    session_id = None
                    for key, value in request.headers.items():
                        if key.lower() == "mcp-session-id":
                            session_id = value
                            break

                    # 如果请求体中有会话ID，优先使用请求体中的
                    if "sessionId" in json_body.get("params", {}):
                        session_id = json_body["params"]["sessionId"]

                    if session_id:
                        logger.info(f"[MCP代理-SessionTerminate] 收到会话终止请求，会话ID: {session_id}")
                        # 异步终止会话，但不等待结果，避免阻塞请求
                        asyncio.create_task(self.terminate_session(session_id))

                        # 直接返回成功响应，不转发到后端
                        response_json = {
                            "jsonrpc": "2.0",
                            "id": json_body.get("id"),
                            "result": {
                                "success": True,
                                "message": f"Session {session_id} terminated."
                            }
                        }

                        # 返回自定义响应
                        async def custom_response_generator():
                            yield _json.dumps(response_json).encode()

                        return 200, {"Content-Type": "application/json"}, custom_response_generator()
        except Exception as e_log:
            logger.warning(f"[MCP代理-DEBUG] Error logging RPC method/content: {e_log}")
        # --- END TEMPORARY DEBUG LOGGING ---

        last_error = None

        mcp_instance_target_path = "/mcp" # Path on the MCP server instance

        # Adjust Accept header for POST requests to /mcp
        # ---- BEGINNING OF BLOCK TO COMMENT OUT ----
        # if request.method == "POST" and mcp_instance_target_path == "/mcp":
        #     current_accept = proxy_headers.get("accept", "")
        #     # If text/event-stream is present, or application/json is NOT, normalize.
        #     # tools/list should get application/json.
        #     if "text/event-stream" in current_accept.lower() or "application/json" not in current_accept.lower():
        #         new_accept = "application/json"
        #         logger.info(f"[MCP代理-Accept] For POST to {mcp_instance_target_path}, original Accept: '{current_accept}'. Normalizing to '{new_accept}'.")
        #         proxy_headers["accept"] = new_accept
        #     elif not current_accept.lower().strip().startswith("application/json"):
        #         # If application/json is present but not primary, and text/event-stream is not forcing the above condition
        #         # e.g. "text/html, application/json" -> still good to make application/json primary for RPC
        #         logger.info(f"[MCP代理-Accept] For POST to {mcp_instance_target_path}, Accept: '{current_accept}'. Ensuring 'application/json' is effectively primary.")
        #         # This condition might be too broad; the one above is more targeted.
        #         # For now, the above condition handles the main scenarios.
        #         # If 'application/json' is present, and 'text/event-stream' is not, we generally trust it.
        # ---- END OF BLOCK TO COMMENT OUT ----

        for host_to_try in hosts_to_try:
            # Use the common port 'actual_port_to_use' obtained for port_http
            target_url = f"http://{host_to_try}:{actual_port_to_use}{mcp_instance_target_path}"
            if request.url.query: target_url += f"?{request.url.query}"

            logger.info(f"尝试代理 MCP StreamHTTP 请求 for server {server_id} to {target_url}")

            # The timeout for read is set to 3.0 seconds. This might be too short for some MCP operations
            # that take longer to process before sending the first byte of the response.
            # Consider making this configurable or increasing it if long-polling like behavior is expected
            # for some non-streaming JSON-RPC calls.
            # For true streaming responses, read timeout ideally is very long or infinite,
            # but initial connect/header timeout should be shorter.
            # httpx.Timeout(settings.MCP_PROXY_CONNECT_TIMEOUT, read=settings.MCP_PROXY_READ_TIMEOUT) would be better
            # if settings.MCP_PROXY_READ_TIMEOUT is defined and appropriate. For now, using existing 3.0.
            async with httpx.AsyncClient(timeout=httpx.Timeout(settings.MCP_PROXY_CONNECT_TIMEOUT, read=3.0)) as client:
                try:
                    # --- ADDING PRE-STREAM LOGGING ---
                    logger.info(f"[MCP代理-PRE_STREAM_HEADERS] Headers to be sent to {target_url}: {proxy_headers}")
                    # --- END OF ADDING PRE-STREAM LOGGING ---
                    async with client.stream(
                        method=request.method,
                        url=target_url,
                        content=request_content,
                        headers=proxy_headers,
                    ) as backend_response:
                        await backend_response.aread()  # Ensure headers are loaded

                        response_headers_to_forward = {}
                        content_type_from_backend = backend_response.headers.get("Content-Type")
                        if content_type_from_backend:
                            response_headers_to_forward["Content-Type"] = content_type_from_backend

                        # 转发包括mcp-session-id在内的所有重要头部
                        logger.info(f"[MCP代理-RESPONSE_HEADERS] 后端响应头部: {dict(backend_response.headers)}")

                        # 特别处理 mcp-session-id 头部 (不区分大小写)
                        for key, value in backend_response.headers.items():
                            if key.lower() == "mcp-session-id":
                                # 统一使用标准大小写格式
                                response_headers_to_forward["Mcp-Session-Id"] = value
                                logger.info(f"[MCP代理-RESPONSE_HEADERS] 从后端响应提取 mcp-session-id: {value}")
                                break

                        # 转发其他可能需要的头部
                        headers_to_forward = ["Access-Control-Allow-Origin", "Access-Control-Allow-Methods",
                                            "Access-Control-Allow-Headers", "Access-Control-Max-Age"]

                        for header in headers_to_forward:
                            value = backend_response.headers.get(header)
                            if value:
                                response_headers_to_forward[header] = value

                        logger.info(f"[MCP代理-RESPONSE_HEADERS] 将转发给客户端的头部: {response_headers_to_forward}")

                        logger.debug(f"Backend response status for {target_url}: {backend_response.status_code}, "
                                     f"forwarding headers: {response_headers_to_forward}")

                        async def stream_generator():
                            try:
                                async for chunk in backend_response.aiter_bytes():
                                    logger.debug(f"Yielding chunk from {target_url}: {chunk[:100]}...")
                                    yield chunk
                            except Exception as e_stream:
                                logger.error(f"Error during backend response streaming from {target_url}: {e_stream}", exc_info=True)
                                # This error happens mid-stream. The headers are already sent.
                                # Client will experience a broken stream.
                            finally:
                                await backend_response.aclose()
                                logger.debug(f"Backend response stream from {target_url} closed.")

                        # Successfully connected and got response (even if it's an HTTP error status from backend)
                        return backend_response.status_code, response_headers_to_forward, stream_generator()

                except httpx.ConnectError as e_conn:
                    logger.warning(f"HTTPX ConnectError while proxying to {target_url}: {e_conn}")
                    last_error = e_conn # Store error and try next host
                    continue # Try next host
                except httpx.ReadTimeout as e_read: # Explicitly handle ReadTimeout
                    logger.error(f"HTTPX ReadTimeout while proxying to {target_url} (after {client.timeout.read}s): {e_read}", exc_info=True)
                    last_error = e_read
                    # For ReadTimeout, we typically don't retry with other hosts, as it implies the server accepted connection
                    # but didn't respond in time.
                    req_id_timeout = None # Try to get req_id for error response
                    # (Logic to get req_id from request_content if needed)
                    async def error_gen_read_timeout():
                        yield _json.dumps({
                            "jsonrpc": "2.0",
                            "error": {"code": -32004, "message": f"Proxy timeout: Backend server failed to respond in time from {host_to_try}."},
                            "id": req_id_timeout
                        }).encode()
                    return 504, {"Content-Type": "application/json", "X-Proxy-Error": "Read Timeout"}, error_gen_read_timeout()
                except httpx.RequestError as e_req: # Other httpx errors
                    logger.error(f"HTTPX RequestError (non-ConnectError/ReadTimeout) while proxying to {target_url}: {e_req}", exc_info=True)
                    last_error = e_req
                    req_id_req_err = None # Try to get req_id
                    # (Logic to get req_id from request_content if needed)
                    async def error_gen_req_err():
                        yield _json.dumps({
                            "jsonrpc": "2.0",
                            "error": {"code": -32001, "message": f"Proxy request error to backend {host_to_try}: {str(e_req)}"},
                            "id": req_id_req_err
                        }).encode()
                    return 502, {"Content-Type": "application/json", "X-Proxy-Error": "Request failed"}, error_gen_req_err()
                except Exception as e_unexp:
                    logger.error(f"Unexpected error while proxying to {target_url}: {e_unexp}", exc_info=True)
                    last_error = e_unexp
                    req_id_unexp = None # Try to get req_id
                    # (Logic to get req_id from request_content if needed)
                    async def error_gen_unexp(e=e_unexp):
                        yield _json.dumps({
                            "jsonrpc": "2.0",
                            "error": {"code": -32000, "message": f"Unexpected proxy error with {host_to_try}: {str(e)}"},
                            "id": req_id_unexp
                        }).encode()
                    return 500, {"Content-Type": "application/json", "X-Proxy-Error": "Unexpected"}, error_gen_unexp()

        # If all hosts failed
        logger.error(f"All attempts to proxy request for server {server_id} failed. Last error: {last_error}")
        req_id_all_failed = None # Try to get req_id
        # (Logic to get req_id from request_content if needed)
        async def error_gen_all_failed():
            yield _json.dumps({
                "jsonrpc": "2.0",
                "error": {"code": -32002, "message": f"Proxy error: All backend connection attempts failed. Last error: {str(last_error)}"},
                "id": req_id_all_failed
            }).encode()
        return 503, {"Content-Type": "application/json", "X-Proxy-Error": "All backends failed"}, error_gen_all_failed()


    async def proxy_request_to_sse(self, server_id: str, request: Request) -> JSONResponse: # 旧的SSE代理，可能不需要大改，或者也改成新模式
        logger.info(f"[MCP代理-SSE_INIT] 开始处理SSE会话初始化请求: server_id={server_id}, path={request.url.path}")

        try:
            # 提取原始请求头
            original_headers = dict(request.headers)

            # 尝试从查询参数获取token (用于 _prepare_forward_headers_and_auth)
            auth_token_from_query = request.query_params.get("token") or request.query_params.get("auth_token")
            if auth_token_from_query:
                original_headers["x-auth-token-from-query"] = auth_token_from_query
                logger.info(f"[MCP代理-SSE_INIT] 在查询参数中找到令牌，将通过 x-auth-token-from-query 传递。 Token: {auth_token_from_query[:10]}...")

            # 1. 认证和令牌转换
            # 现在 _prepare_forward_headers_and_auth 会检查 x-auth-token-from-query
            forward_headers, user_context = await self._prepare_forward_headers_and_auth(server_id, original_headers)

            system_token_str = None
            auth_header = forward_headers.get("authorization")
            if auth_header and auth_header.lower().startswith("bearer "):
                system_token_str = auth_header.split(" ", 1)[1]

            if not system_token_str:
                logger.error(f"[MCP代理-SSE_INIT] 未能在 'forward_headers' 中找到系统令牌 (Authorization: Bearer ...)。server_id={server_id}")
                raise HTTPException(status_code=500, detail="代理内部错误：无法准备用于SSE会话的系统令牌")

            # 2. 获取MCP服务器信息
            mcp_server_info = await self.get_server(server_id)
            if not mcp_server_info:
                logger.warning(f"[MCP代理-SSE_INIT] 找不到服务器注册信息: server_id={server_id}")
                raise HTTPException(status_code=404, detail=f"MCP服务器 '{server_id}' 未注册或不可用")

            # 获取SSE主机和端口信息
            # 首先尝试从port_sse字段获取端口
            sse_port = mcp_server_info.get("port_sse")
            if not sse_port:
                # 尝试从metadata中获取
                metadata = mcp_server_info.get("metadata", {})
                if isinstance(metadata, dict):
                    sse_port = metadata.get("port_sse")

            # 获取主机信息
            sse_host = None
            # 尝试不同的主机名策略
            if os.environ.get("K8S_ENV") == "true":
                sse_host = f"mcp-server-{server_id}-svc"
            elif os.environ.get("DOCKER_ENV") == "true":
                sse_host = f"mcp-server-{server_id}"
            else:
                # 使用Redis中注册的主机名
                sse_host = mcp_server_info.get("host", "localhost")

            if not sse_host or not sse_port:
                logger.error(f"[MCP代理-SSE_INIT] 服务器 '{server_id}' 注册信息不完整 (缺少sse_host/host或sse_port). Info: {mcp_server_info}")
                raise HTTPException(status_code=500, detail=f"MCP服务器 '{server_id}' 的SSE端点信息配置不完整")

            # TODO: 考虑在此处添加对 sse_host:sse_port 的健康检查

            # 3. 生成 Session ID
            session_id = uuid.uuid4()

            # 4. 存储会话到 Redis
            redis = await self.get_redis()
            session_key = f"{MCP_SESSION_KEY_PREFIX}{str(session_id)}" #确保session_id是字符串

            # 从user_context中获取信息，如果不存在则为None
            user_id_from_context = user_context.get("user_id", get_user_id_from_payload(user_context))
            project_id_from_context = user_context.get("project_id", get_project_id_from_payload(user_context))

            session_data = {
                "system_token": system_token_str,
                "user_id": user_id_from_context,
                "project_id": project_id_from_context,
                "server_id": server_id,
                "original_request_headers": dict(request.headers), # 可选：存储原始请求头以备调试或特定场景
            }
            session_data_to_store = {k: v for k, v in session_data.items() if v is not None}

            session_ttl = getattr(settings, 'MCP_SESSION_INIT_TTL', MCP_SESSION_INIT_TTL_DEFAULT)

            await redis.set(session_key, _json.dumps(session_data_to_store), ex=session_ttl)
            logger.info(f"[MCP代理-SSE_INIT] Session '{session_id}' 已存入Redis，TTL: {session_ttl}s. Data Keys: {list(session_data_to_store.keys())}")

            # 5. 构建给客户端的响应数据，遵循策略B和JSON-RPC 2.0风格
            # 添加健康检查
            sse_health_url = f"http://{sse_host}:{sse_port}/health"
            sse_health_status = "unknown"
            sse_health_message = ""

            try:
                async with httpx.AsyncClient(timeout=httpx.Timeout(5.0)) as health_client:
                    health_resp = await health_client.get(sse_health_url)
                    if health_resp.status_code == 200:
                        sse_health_status = "healthy"
                        try:
                            health_data = health_resp.json()
                            sse_health_message = health_data.get("status", "ok")
                        except:
                            sse_health_message = "ok"
                    else:
                        sse_health_status = "unhealthy"
                        sse_health_message = f"Status code: {health_resp.status_code}"
            except Exception as e_health:
                sse_health_status = "error"
                sse_health_message = str(e_health)
                logger.warning(f"[MCP代理-SSE_INIT] 健康检查失败: {sse_health_url} - {str(e_health)}")

            # 构建SSE连接URL
            # 这里我们提供两种URL格式：
            # 1. 直接URL - 客户端可以直接连接的完整URL
            # 2. 相对路径 - 客户端可以根据自己的基础URL构建完整URL

            # 获取当前请求的主机和协议
            request_host = request.headers.get("host", "localhost")
            request_proto = request.headers.get("x-forwarded-proto", "http")

            # 获取Traefik域名配置
            traefik_mcp_domain_suffix = os.environ.get("TRAEFIK_MCP_DOMAIN_SUFFIX", "localhost")

            # 构建Traefik直接URL (更简洁的方式)
            traefik_direct_url = f"{request_proto}://mcp-{server_id}.{traefik_mcp_domain_suffix}/message?sessionId={session_id}"

            # 构建API代理URL (通过后端API代理)
            api_proxy_url = f"{request_proto}://{request_host}/api/v1/project/{project_id_from_context}/plugin/sapi/servers/{server_id}/sse/message?sessionId={session_id}"

            # 构建相对路径 (简化版)
            relative_sse_path = f"/servers/{server_id}/sse/message?sessionId={session_id}"

            # 检查是否在Docker环境中运行
            is_docker_env = os.environ.get("DOCKER_ENV") == "true"

            result_data = {
                "sessionId": str(session_id),
                "mcpServerId": server_id,
                "mcpRelativeSsePath": relative_sse_path,
                "mcpDirectSseUrl": traefik_direct_url if is_docker_env else api_proxy_url,
                "mcpApiProxyUrl": api_proxy_url,
                "mcpTraefikUrl": traefik_direct_url,
                "sseHealthStatus": sse_health_status,
                "sseHealthMessage": sse_health_message,
                "message": "SSE会话已初始化。请使用提供的URL连接到SSE端点。",
                "ttlSeconds": session_ttl,
                "projectId": project_id_from_context,
                "userId": user_id_from_context,
                "useTraefik": is_docker_env
            }

            # 构建符合 JSON-RPC 2.0 的成功响应结构
            json_rpc_response = {
                "jsonrpc": "2.0",
                "result": result_data,
                "id": None # 假设此GET请求没有显式的JSON-RPC请求ID
            }

            logger.info(f"[MCP代理-SSE_INIT] 成功初始化SSE会话. JSON-RPC Response: {json_rpc_response}")

            return JSONResponse(content=json_rpc_response)

        except HTTPException as he:
            logger.warning(f"[MCP代理-SSE_INIT] 处理SSE会话初始化时发生HTTPException: Status={he.status_code}, Detail={he.detail}", exc_info=settings.DEBUG)
            # 返回JSON-RPC风格的错误
            error_obj = {"code": he.status_code, "message": he.detail}
            # 如果能从请求中获取ID，则填充，否则为None
            # request_id = getattr(request.state, "json_rpc_id", None)
            return JSONResponse(status_code=he.status_code,
                                content={"jsonrpc": "2.0", "error": error_obj, "id": None})
        except Exception as e_top:
            logger.error(f"[MCP代理-SSE_INIT] 处理SSE会话初始化时发生顶层未捕获异常: {type(e_top).__name__} - {str(e_top)}", exc_info=settings.DEBUG)
            error_obj = {"code": -32000, "message": f"代理内部服务器错误: {str(e_top)}"}
            return JSONResponse(status_code=500,
                                content={"jsonrpc": "2.0", "error": error_obj, "id": None})
        # 旧的 finally 块中关于 http_client 的关闭逻辑在此不再需要

    async def proxy_event_stream_request(self, server_id: str, request: Request) -> StreamingResponse:
        logger.info(f"[MCP代理-StreamHTTP-GET] 收到GET事件流请求: server_id={server_id}, path={request.url.path}")
        try:
            # 1. 认证和准备转发头 (大部分与POST类似，但不需要请求体)
            #    _prepare_forward_headers_and_auth 已经处理了从查询参数提取令牌的逻辑 (通过 x-auth-token-from-query)

            # 直接从 Starlette Headers 对象获取 mcp-session-id (大小写不敏感)
            mcp_session_id_value = None
            # 尝试常见的大小写变体
            for header_key in ["mcp-session-id", "Mcp-Session-Id", "MCP-SESSION-ID"]:
                header_value = request.headers.get(header_key)
                if header_value:
                    mcp_session_id_value = header_value
                    logger.info(f"[MCP代理-StreamHTTP-GET] 找到会话ID头部: '{header_key}': '{header_value}'")
                    break

            # 如果从标准头中找不到，检查所有头部，查找包含'session'的键
            if not mcp_session_id_value:
                for key, value in request.headers.items():
                    if "session" in key.lower() and "id" in key.lower():
                        logger.info(f"[MCP代理-StreamHTTP-GET] 从非标准头部找到可能的会话ID: '{key}': '{value}'")
                        mcp_session_id_value = value
                        break

            # 尝试从查询参数中获取会话ID
            if not mcp_session_id_value:
                session_id_from_query = request.query_params.get("sessionId") or request.query_params.get("session_id")
                if session_id_from_query:
                    logger.info(f"[MCP代理-StreamHTTP-GET] 从查询参数中找到会话ID: {session_id_from_query}")
                    mcp_session_id_value = session_id_from_query

            if not mcp_session_id_value:
                logger.warning(f"[MCP代理-StreamHTTP-GET] 请求缺少 'mcp-session-id' 头部和查询参数 (checked case-insensitively). server_id={server_id}")
                async def error_event_stream_no_session(): # Renamed to avoid conflict
                    error_json = _json.dumps({"error": "Missing mcp-session-id header or sessionId query parameter"})
                    sse_event_string = f"event: error\\ndata: {error_json}\\n\\n"
                    yield sse_event_string
                return StreamingResponse(error_event_stream_no_session(), media_type="text/event-stream", status_code=400)

            # 检查会话是否存在，如果不存在，尝试恢复
            session_data = await self.get_session_data(mcp_session_id_value)
            if not session_data:
                logger.warning(f"[MCP代理-StreamHTTP-GET] 会话 {mcp_session_id_value} 不存在或已过期")
                async def error_event_stream_session_expired():
                    error_json = _json.dumps({"error": "Session not found or expired", "code": "SESSION_NOT_FOUND"})
                    sse_event_string = f"event: error\\ndata: {error_json}\\n\\n"
                    yield sse_event_string
                return StreamingResponse(error_event_stream_session_expired(), media_type="text/event-stream", status_code=404)

            # 将原始请求头转换为字典，用于传递给 _prepare_forward_headers_and_auth
            original_headers_dict = dict(request.headers)
            auth_token_from_query = request.query_params.get("token") or request.query_params.get("auth_token")
            if auth_token_from_query:
                original_headers_dict["x-auth-token-from-query"] = auth_token_from_query
                logger.info(f"[MCP代理-StreamHTTP-GET] 在查询参数中找到令牌，将通过 x-auth-token-from-query 传递。")

            headers_to_forward, _ = await self._prepare_forward_headers_and_auth(server_id, original_headers_dict)

            # _prepare_forward_headers_and_auth 会从 original_headers_dict 中处理 mcp-session-id。
            # 为了确保万无一失，我们再次确认 headers_to_forward 中 mcp-session-id 已正确设置。
            # 通常情况下，如果 mcp_session_id_value 有值，_prepare_forward_headers_and_auth 应该已经正确设置了它。
            if mcp_session_id_value and not headers_to_forward.get("mcp-session-id"):
                logger.warning(f"[MCP代理-StreamHTTP-GET] mcp-session-id ('{mcp_session_id_value}') 在 _prepare_forward_headers_and_auth 后丢失，强制重新设置。")
                headers_to_forward["mcp-session-id"] = mcp_session_id_value
            elif not headers_to_forward.get("mcp-session-id") and mcp_session_id_value:
                 # This case implies _prepare_forward_headers_and_auth failed to pick it up but we found it directly
                 headers_to_forward["mcp-session-id"] = mcp_session_id_value


            # 确保 MCP 实例知道我们期望 SSE
            headers_to_forward["accept"] = "text/event-stream"

            # 检查是否有Last-Event-ID头部，用于恢复断开的连接
            last_event_id = request.headers.get("Last-Event-ID")
            if last_event_id:
                logger.info(f"[MCP代理-StreamHTTP-GET] 发现Last-Event-ID头部: {last_event_id}，将用于恢复连接")
                headers_to_forward["Last-Event-ID"] = last_event_id

            # 延长会话TTL
            await self.extend_session_ttl(mcp_session_id_value)
            logger.info(f"[MCP代理-StreamHTTP-GET] 会话 {mcp_session_id_value} TTL已延长")

            # 2. 获取后端目标信息
            port_to_use, hosts_to_try = await self._get_backend_target_info(server_id, "streamhttp") # 使用 port_http

            # 3. 流式代理
            errors_from_attempts = []
            for current_host_to_try in hosts_to_try:
                target_url = f"http://{current_host_to_try}:{port_to_use}/mcp" # MCP实例的GET /mcp端点
                logger.info(f"[MCP代理-StreamHTTP-GET] 尝试连接事件流到: {target_url} with session: {mcp_session_id_value}")
                try:
                    client = httpx.AsyncClient(timeout=None) # No timeout for the connection itself, server manages stream

                    # --- BEGIN Log final headers before streaming GET ---
                    logger.info(f"[MCP代理-StreamHTTP-GET PRE-STREAM] Final headers to be sent to MCP server for SSE GET ({target_url}): {headers_to_forward}")
                    # --- END Log final headers before streaming GET ---

                    async def event_generator():
                        try:
                            async with client.stream("GET", target_url, headers=headers_to_forward, timeout=None) as response:
                                logger.info(f"[MCP代理-StreamHTTP-GET] 连接到 {target_url} 状态: {response.status_code}")
                                if response.status_code != 200:
                                    error_content = await response.aread()
                                    logger.error(f"[MCP代理-StreamHTTP-GET] MCP实例返回错误: {response.status_code}, {error_content.decode(errors='ignore')}")
                                    error_json = _json.dumps({"error": "MCP service error", "status_code": response.status_code, "details": error_content.decode(errors='ignore')})
                                    sse_event_string = f"event: error\\ndata: {error_json}\\n\\n"
                                    yield sse_event_string
                                    return

                                async for chunk in response.aiter_bytes():
                                    yield chunk
                        except httpx.RequestError as e_stream_req:
                            logger.error(f"[MCP代理-StreamHTTP-GET] 流式请求到 {target_url} 错误: {e_stream_req}")
                            error_json = _json.dumps({"error": "Stream connection error", "details": str(e_stream_req)})
                            sse_event_string = f"event: error\\ndata: {error_json}\\n\\n"
                            yield sse_event_string
                        except Exception as e_stream_generic:
                            logger.error(f"[MCP代理-StreamHTTP-GET] 流处理中发生未知错误 for {target_url}: {e_stream_generic}", exc_info=True)
                            error_json = _json.dumps({"error": "Stream processing error", "details": str(e_stream_generic)})
                            sse_event_string = f"event: error\\ndata: {error_json}\\n\\n"
                            yield sse_event_string
                        finally:
                            await client.aclose()
                            logger.info(f"[MCP代理-StreamHTTP-GET] 事件流 for {target_url} (session: {mcp_session_id_value}) 已关闭.")

                    # 准备转发的响应头 (从原始请求中筛选，或设置默认值)
                    response_headers = {
                        "Content-Type": "text/event-stream",
                            "Cache-Control": "no-cache",
                        "Connection": "keep-alive",
                        "X-Accel-Buffering": "no", # Useful for Nginx
                        "Access-Control-Allow-Origin": "*" # Or be more specific
                    }
                    return StreamingResponse(event_generator(), headers=response_headers)

                except httpx.ConnectError as e_conn:
                    logger.warning(f"[MCP代理-StreamHTTP-GET] 连接到 {target_url} 失败: {e_conn}")
                    errors_from_attempts.append(f"Host {current_host_to_try}: {str(e_conn)}")
                    # Don't close client here, it's per attempt
                    continue # Try next host
                except Exception as e_outer_try:
                    logger.error(f"[MCP代理-StreamHTTP-GET] 尝试连接 {target_url} 时发生未知错误: {e_outer_try}", exc_info=True)
                    errors_from_attempts.append(f"Host {current_host_to_try} (Outer): {str(e_outer_try)}")
                    continue # Try next host

            # If all attempts failed
            logger.error(f"[MCP代理-StreamHTTP-GET] 所有连接尝试均失败 for server {server_id}, session {mcp_session_id_value}. Errors: {errors_from_attempts}")
            async def final_error_stream():
                error_json = _json.dumps({"error": "MCP service unavailable (all connection attempts failed)", "details": errors_from_attempts})
                sse_event_string = f"event: error\\ndata: {error_json}\\n\\n"
                yield sse_event_string
            return StreamingResponse(final_error_stream(), media_type="text/event-stream", status_code=503)

        except HTTPException as he:
            logger.warning(f"[MCP代理-StreamHTTP-GET] 处理事件流时发生HTTPException: Status={he.status_code}, Detail={he.detail}", exc_info=settings.DEBUG)
            async def http_exc_event_stream():
                error_json = _json.dumps({"error": he.detail, "status_code": he.status_code})
                sse_event_string = f"event: error\\ndata: {error_json}\\n\\n"
                yield sse_event_string
            return StreamingResponse(http_exc_event_stream(), media_type="text/event-stream", status_code=he.status_code)
        except Exception as e_top:
            logger.error(f"[MCP代理-StreamHTTP-GET] 处理事件流时发生顶层未捕获异常: {str(e_top)}", exc_info=settings.DEBUG)
            async def top_exc_event_stream():
                error_json = _json.dumps({"error": "Proxy internal server error", "details": str(e_top)})
                sse_event_string = f"event: error\\ndata: {error_json}\\n\\n"
                yield sse_event_string
            return StreamingResponse(top_exc_event_stream(), media_type="text/event-stream", status_code=500)

# 创建MCP代理实例 (通常在你的主应用或路由模块中创建和导入)
# 例如，在你的 FastAPI app setup: from .mcp_proxy import mcp_proxy_service
mcp_proxy = MCPProxy() # 名称已更改为mcp_proxy_service，与 mcp_router.py 一致

# --- End of mcp_proxy.py ---