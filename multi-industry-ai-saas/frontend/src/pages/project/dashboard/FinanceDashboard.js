import React, { useState, useEffect, useRef } from 'react';
import {
  Card,
  Row,
  Col,
  Typography,
  Table,
  Button,
  Spin,
  Empty,
  Tabs,
  List,
  Tag,
  Space,
  message,
  Divider
} from 'antd';
import {
  DollarOutlined,
  LineChartOutlined,
  PieChartOutlined,
  RobotOutlined,
  BankOutlined,
  AccountBookOutlined,
  ShopOutlined,
  LikeOutlined,
  DislikeOutlined,
  SyncOutlined
} from '@ant-design/icons';
import { Line, Pie } from '@ant-design/plots';
import dayjs from 'dayjs';
import apiService from '../../../services/api';
import { useAuth } from '../../../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';

// 导入自定义组件
import DashboardFilter from '../../../components/dashboard/DashboardFilter';
import StatisticCard from '../../../components/dashboard/StatisticCard';
import ExportButton from '../../../components/dashboard/ExportButton';
import RoleBasedContent from '../../../components/dashboard/RoleBasedContent';

// 导入服务
import dashboardCache from '../../../services/dashboardCache';
import aiRecommendation from '../../../services/aiRecommendation';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;

/**
 * 财务仪表盘组件
 * 展示收入、成本、利润等财务数据
 */
const FinanceDashboard = () => {
  // 引用
  const dashboardRef = useRef(null);
  const navigate = useNavigate();

  // 获取用户信息
  const { user } = useAuth();

  // 状态定义
  const [loading, setLoading] = useState(true);
  const [dashboardData, setDashboardData] = useState(null);
  const [aiLoading, setAiLoading] = useState(false);
  const [stores, setStores] = useState([]);
  const [categories, setCategories] = useState([]);
  const [filterParams, setFilterParams] = useState({
    dateRange: [
      dayjs().subtract(29, 'days').format('YYYY-MM-DD'),
      dayjs().format('YYYY-MM-DD')
    ],
    storeId: null,
    categoryId: null
  });
  const [feedbackLoading, setFeedbackLoading] = useState(false);

  // 初始化加载
  useEffect(() => {
    fetchStores();
    fetchCategories();
    fetchDashboardData();

    // 组件卸载时清除特定缓存
    return () => {
      dashboardCache.clearCachedData('finance');
    };
  }, []);

  // 获取门店列表
  const fetchStores = async () => {
    try {
      const response = await apiService.project.store.getList();
      setStores(response.items || []);
    } catch (error) {
      console.error('获取门店列表失败:', error);
    }
  };

  // 获取类别列表
  const fetchCategories = async () => {
    try {
      const response = await apiService.project.product.getCategories();
      setCategories(response.items || []);
    } catch (error) {
      console.error('获取类别列表失败:', error);
    }
  };

  // 获取仪表盘数据
  const fetchDashboardData = async () => {
    setLoading(true);
    try {
      const params = {
        start_date: filterParams.dateRange[0],
        end_date: filterParams.dateRange[1],
        store_id: filterParams.storeId,
        category_id: filterParams.categoryId
      };

      // 使用缓存服务获取数据
      const response = await dashboardCache.fetchWithCache(
        'finance',
        params,
        async (p) => await apiService.project.dashboard.getFinanceDashboard(p)
      );

      // 处理 store_profit 数据，确保每个项目都有 orders 字段
      if (response.store_profit && Array.isArray(response.store_profit)) {
        response.store_profit = response.store_profit.map(store => ({
          ...store,
          orders: store.orders || 0 // 如果没有 orders 字段，则添加默认值 0
        }));
      }

      setDashboardData(response);

      // 如果没有AI推荐，则自动获取
      if (!response.ai_recommendations || response.ai_recommendations.length === 0) {
        refreshAIRecommendations(response);
      }
    } catch (error) {
      console.error('获取财务仪表盘数据失败:', error);
      message.error('获取财务仪表盘数据失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  // 刷新AI推荐
  const refreshAIRecommendations = async (data = null) => {
    setAiLoading(true);
    try {
      // 使用增强的AI推荐服务
      const recommendations = await aiRecommendation.getFinanceRecommendations(data || dashboardData);

      if (recommendations && recommendations.length > 0) {
        setDashboardData(prev => ({
          ...prev,
          ai_recommendations: recommendations
        }));
        message.success('AI推荐已更新');
      } else {
        message.info('暂无新的AI推荐');
      }
    } catch (error) {
      console.error('获取AI推荐失败:', error);
      message.error('获取AI推荐失败，请重试');
    } finally {
      setAiLoading(false);
    }
  };

  // 提交AI推荐反馈
  const handleRecommendationFeedback = async (recommendationId, isHelpful, feedback = '') => {
    setFeedbackLoading(true);
    try {
      const result = await aiRecommendation.feedbackRecommendation(recommendationId, isHelpful, feedback);

      if (result.success) {
        message.success(isHelpful ? '感谢您的正面反馈！' : '感谢您的反馈，我们会继续改进');

        // 更新本地数据，标记已反馈
        setDashboardData(prev => ({
          ...prev,
          ai_recommendations: prev.ai_recommendations.map(item =>
            item.id === recommendationId
              ? { ...item, feedback_submitted: true, is_helpful: isHelpful }
              : item
          )
        }));
      } else {
        message.error('提交反馈失败，请重试');
      }
    } catch (error) {
      console.error('提交AI推荐反馈失败:', error);
      message.error('提交反馈失败，请重试');
    } finally {
      setFeedbackLoading(false);
    }
  };

  // 处理筛选条件变化
  const handleFilterChange = (filters) => {
    setFilterParams(filters);
    fetchDashboardData();
  };

  // 刷新数据
  const handleRefresh = () => {
    fetchDashboardData();
  };

  // 导出数据
  const getExportData = () => {
    if (!dashboardData) return [];

    // 根据当前选中的标签页返回不同的导出数据
    return dashboardData.revenue_trend || [];
  };

  // 渲染收入趋势图表
  const renderRevenueTrendChart = () => {
    if (!dashboardData || !dashboardData.revenue_trend || dashboardData.revenue_trend.length === 0) {
      return <Empty description="暂无收入趋势数据" />;
    }

    const config = {
      data: dashboardData.revenue_trend,
      xField: 'date',
      yField: 'value',
      seriesField: 'type',
      smooth: true,
      animation: {
        appear: {
          animation: 'path-in',
          duration: 1000
        }
      },
      xAxis: {
        title: {
          text: '日期'
        }
      },
      yAxis: {
        title: {
          text: '金额 (元)'
        }
      },
      tooltip: {
        formatter: (datum) => {
          return { name: '收入', value: `¥${datum.value.toFixed(2)}` };
        }
      }
    };

    return <Line {...config} height={300} />;
  };

  // 渲染成本趋势图表
  const renderCostTrendChart = () => {
    if (!dashboardData || !dashboardData.cost_trend || dashboardData.cost_trend.length === 0) {
      return <Empty description="暂无成本趋势数据" />;
    }

    const config = {
      data: dashboardData.cost_trend,
      xField: 'date',
      yField: 'value',
      seriesField: 'type',
      smooth: true,
      animation: {
        appear: {
          animation: 'path-in',
          duration: 1000
        }
      },
      xAxis: {
        title: {
          text: '日期'
        }
      },
      yAxis: {
        title: {
          text: '金额 (元)'
        }
      },
      tooltip: {
        formatter: (datum) => {
          return { name: '成本', value: `¥${datum.value.toFixed(2)}` };
        }
      }
    };

    return <Line {...config} height={300} />;
  };

  // 渲染利润趋势图表
  const renderProfitTrendChart = () => {
    if (!dashboardData || !dashboardData.profit_trend || dashboardData.profit_trend.length === 0) {
      return <Empty description="暂无利润趋势数据" />;
    }

    const config = {
      data: dashboardData.profit_trend,
      xField: 'date',
      yField: 'value',
      seriesField: 'type',
      smooth: true,
      animation: {
        appear: {
          animation: 'path-in',
          duration: 1000
        }
      },
      xAxis: {
        title: {
          text: '日期'
        }
      },
      yAxis: {
        title: {
          text: '金额 (元)'
        }
      },
      tooltip: {
        formatter: (datum) => {
          return { name: '利润', value: `¥${datum.value.toFixed(2)}` };
        }
      }
    };

    return <Line {...config} height={300} />;
  };

  // 渲染类别利润分布图表
  const renderCategoryProfitChart = () => {
    if (!dashboardData || !dashboardData.category_profit || dashboardData.category_profit.length === 0) {
      return <Empty description="暂无类别利润数据" />;
    }

    const config = {
      data: dashboardData.category_profit,
      angleField: 'value',
      colorField: 'name',
      radius: 0.8,
      label: {
        type: 'outer',
        content: '{name}: {percentage}%'
      },
      interactions: [{ type: 'element-active' }],
      tooltip: {
        formatter: (datum) => {
          return { name: datum.name, value: `¥${datum.value.toFixed(2)} (${datum.percentage.toFixed(1)}%)` };
        }
      }
    };

    return <Pie {...config} height={300} />;
  };

  // 渲染AI推荐
  const renderAIRecommendations = () => {
    if (!dashboardData || !dashboardData.ai_recommendations || dashboardData.ai_recommendations.length === 0) {
      return <Empty description="暂无AI推荐" />;
    }

    return (
      <List
        dataSource={dashboardData.ai_recommendations}
        renderItem={(item) => (
          <List.Item>
            <div style={{ width: '100%' }}>
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'flex-start',
                marginBottom: 12
              }}>
                <div>
                  <Text strong style={{ fontSize: 16, marginRight: 8 }}>{item.title}</Text>
                  <Tag color={
                    item.type === '业务优化' ? 'blue' :
                    item.type === '销售增长' ? 'green' :
                    item.type === '成本控制' ? 'orange' :
                    item.type === '风险预警' ? 'red' : 'default'
                  }>
                    {item.type}
                  </Tag>
                  {item.priority && (
                    <Tag color={
                      item.priority === '高' ? 'red' :
                      item.priority === '中' ? 'orange' : 'green'
                    }>
                      {item.priority}优先级
                    </Tag>
                  )}
                </div>
                <div>
                  {item.feedback_submitted ? (
                    <span>
                      {item.is_helpful ?
                        <Tag icon={<LikeOutlined />} color="success">已标记为有帮助</Tag> :
                        <Tag icon={<DislikeOutlined />} color="default">已提交反馈</Tag>
                      }
                    </span>
                  ) : (
                    <Space>
                      <Button
                        type="primary"
                        size="small"
                        icon={<LikeOutlined />}
                        onClick={() => handleRecommendationFeedback(item.id, true)}
                        loading={feedbackLoading}
                      >
                        有帮助
                      </Button>
                      <Button
                        size="small"
                        icon={<DislikeOutlined />}
                        onClick={() => handleRecommendationFeedback(item.id, false)}
                        loading={feedbackLoading}
                      >
                        没有帮助
                      </Button>
                    </Space>
                  )}
                </div>
              </div>

              <Paragraph style={{ margin: '12px 0' }}>{item.content}</Paragraph>

              {item.action_items && item.action_items.length > 0 && (
                <div style={{
                  background: '#f5f5f5',
                  padding: '12px 16px',
                  borderRadius: '4px',
                  marginBottom: 12
                }}>
                  <Text strong>建议行动:</Text>
                  <ul style={{ marginTop: 8, marginBottom: 0, paddingLeft: 20 }}>
                    {item.action_items.map((action, index) => (
                      <li key={index}>{action}</li>
                    ))}
                  </ul>
                </div>
              )}

              {/* 基于角色显示更多详细信息 */}
              <RoleBasedContent roles={['project_admin', 'finance_manager']}>
                {item.detailed_analysis && (
                  <div style={{ marginTop: 12 }}>
                    <Divider style={{ margin: '12px 0' }} />
                    <Text strong>详细分析:</Text>
                    <Paragraph style={{ margin: '8px 0 0' }}>{item.detailed_analysis}</Paragraph>
                  </div>
                )}
              </RoleBasedContent>
            </div>
          </List.Item>
        )}
        style={{ background: '#fff' }}
      />
    );
  };

  // 渲染门店利润表格
  const renderStoreProfitTable = () => {
    if (!dashboardData || !dashboardData.store_profit || dashboardData.store_profit.length === 0) {
      return <Empty description="暂无门店利润数据" />;
    }

    const columns = [
      {
        title: '门店名称',
        dataIndex: 'name',
        key: 'name',
      },
      {
        title: '销售额',
        dataIndex: 'sales',
        key: 'sales',
        render: (text) => `¥${text.toFixed(2)}`,
        sorter: (a, b) => a.sales - b.sales,
      },
      {
        title: '利润',
        dataIndex: 'profit',
        key: 'profit',
        render: (text) => `¥${text.toFixed(2)}`,
        sorter: (a, b) => a.profit - b.profit,
      },
      {
        title: '利润率',
        key: 'profit_rate',
        render: (_, record) => {
          const rate = (record.profit / record.sales) * 100;
          return `${rate.toFixed(2)}%`;
        },
        sorter: (a, b) => (a.profit / a.sales) - (b.profit / b.sales),
      }
    ];

    return (
      <Table
        columns={columns}
        dataSource={dashboardData.store_profit}
        rowKey="id"
        pagination={{ pageSize: 5 }}
      />
    );
  };

  return (
    <div className="finance-dashboard" ref={dashboardRef}>
      <Card className="ant-card ant-card-bordered css-dev-only-do-not-override-1m2bkf9">
        <div className="dashboard-header">
          <Title level={4} className="dashboard-title">财务仪表盘</Title>
          <div className="dashboard-actions">
            <ExportButton
              data={getExportData()}
              filename="财务仪表盘数据"
            />
          </div>
        </div>

        <DashboardFilter
          onFilterChange={handleFilterChange}
          stores={stores}
          categories={categories}
          showStoreFilter={true}
          showCategoryFilter={true}
          defaultDateRange={30}
          onRefresh={handleRefresh}
          loading={loading}
        />

        <Spin spinning={loading}>
          {dashboardData ? (
            <div className="dashboard-content">
            {/* 统计卡片 */}
            <Row gutter={[16, 16]}>
              <Col xs={24} sm={12} md={6}>
                <StatisticCard
                  title="本月收入"
                  value={dashboardData.monthly_revenue.value}
                  precision={2}
                  prefix={<DollarOutlined />}
                  suffix="元"
                  valueStyle={{ color: '#3f8600' }}
                  change={dashboardData.monthly_revenue.change}
                  isIncrease={dashboardData.monthly_revenue.is_increase}
                  compareText="与上月相比"
                  tooltip="本月总收入，包括销售收入、充值收入等"
                />
              </Col>
              <Col xs={24} sm={12} md={6}>
                <StatisticCard
                  title="本月成本"
                  value={dashboardData.monthly_cost.value}
                  precision={2}
                  prefix={<BankOutlined />}
                  suffix="元"
                  valueStyle={{ color: dashboardData.monthly_cost.is_increase ? '#cf1322' : '#3f8600' }}
                  change={dashboardData.monthly_cost.change}
                  isIncrease={dashboardData.monthly_cost.is_increase}
                  compareText="与上月相比"
                  tooltip="本月总成本，包括采购成本、运营成本等"
                />
              </Col>
              <Col xs={24} sm={12} md={6}>
                <StatisticCard
                  title="本月利润"
                  value={dashboardData.monthly_profit.value}
                  precision={2}
                  prefix={<AccountBookOutlined />}
                  suffix="元"
                  valueStyle={{ color: '#722ed1' }}
                  change={dashboardData.monthly_profit.change}
                  isIncrease={dashboardData.monthly_profit.is_increase}
                  compareText="与上月相比"
                  tooltip="本月总利润，收入减去成本"
                />
              </Col>
              <Col xs={24} sm={12} md={6}>
                <StatisticCard
                  title="利润率"
                  value={dashboardData.monthly_margin.value}
                  precision={2}
                  prefix={<DollarOutlined />}
                  suffix="%"
                  valueStyle={{ color: '#fa8c16' }}
                  change={dashboardData.monthly_margin.change}
                  isIncrease={dashboardData.monthly_margin.is_increase}
                  compareText="与上月相比"
                  tooltip="利润占收入的百分比"
                />
              </Col>
            </Row>

            {/* 快捷入口 */}
            <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
              <Col xs={24} sm={8} md={6} lg={4}>
                <Card
                  hoverable
                  onClick={() => navigate('/project/finance/reconciliation')}
                  style={{ textAlign: 'center', cursor: 'pointer' }}
                >
                  <AccountBookOutlined style={{ fontSize: 24, color: '#1890ff', marginBottom: 8 }} />
                  <div>财务对账</div>
                </Card>
              </Col>
              <Col xs={24} sm={8} md={6} lg={4}>
                <Card
                  hoverable
                  onClick={() => navigate('/project/finance/data-analysis')}
                  style={{ textAlign: 'center', cursor: 'pointer' }}
                >
                  <LineChartOutlined style={{ fontSize: 24, color: '#52c41a', marginBottom: 8 }} />
                  <div>数据分析</div>
                </Card>
              </Col>
              <Col xs={24} sm={8} md={6} lg={4}>
                <Card
                  hoverable
                  onClick={() => navigate('/project/finance/business-center')}
                  style={{ textAlign: 'center', cursor: 'pointer' }}
                >
                  <BankOutlined style={{ fontSize: 24, color: '#722ed1', marginBottom: 8 }} />
                  <div>业务中心</div>
                </Card>
              </Col>
              <Col xs={24} sm={8} md={6} lg={4}>
                <Card
                  hoverable
                  onClick={() => navigate('/project/ai/role-assistants?role=finance_manager')}
                  style={{ textAlign: 'center', cursor: 'pointer' }}
                >
                  <RobotOutlined style={{ fontSize: 24, color: '#f5222d', marginBottom: 8 }} />
                  <div>AI助手</div>
                </Card>
              </Col>
            </Row>

            {/* 图表区域 */}
            <Tabs defaultActiveKey="1" style={{ marginTop: 16 }} className="dashboard-tabs">
              <TabPane
                tab={<span><LineChartOutlined /> 收入趋势</span>}
                key="1"
              >
                <Card className="chart-container">
                  {renderRevenueTrendChart()}
                </Card>
              </TabPane>
              <TabPane
                tab={<span><LineChartOutlined /> 成本趋势</span>}
                key="2"
              >
                <Card className="chart-container">
                  {renderCostTrendChart()}
                </Card>
              </TabPane>
              <TabPane
                tab={<span><LineChartOutlined /> 利润趋势</span>}
                key="3"
              >
                <Card className="chart-container">
                  {renderProfitTrendChart()}
                </Card>
              </TabPane>
              <TabPane
                tab={<span><PieChartOutlined /> 类别利润</span>}
                key="4"
              >
                <Card className="chart-container">
                  {renderCategoryProfitChart()}
                </Card>
              </TabPane>
              <TabPane
                tab={<span><ShopOutlined /> 门店利润</span>}
                key="5"
              >
                <Card className="chart-container">
                  {renderStoreProfitTable()}
                </Card>
              </TabPane>
            </Tabs>

            {/* AI推荐 */}
            <div style={{ marginTop: 16 }}>
              <Card
                title={
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <span><RobotOutlined /> AI推荐</span>
                    <Button
                      icon={<SyncOutlined />}
                      onClick={refreshAIRecommendations}
                      loading={aiLoading}
                      size="small"
                    >
                      刷新AI推荐
                    </Button>
                  </div>
                }
              >
                {renderAIRecommendations()}
              </Card>
            </div>
          </div>
        ) : (
          <Empty description="暂无数据" />
        )}
      </Spin>
      </Card>
    </div>
  );
};

export default FinanceDashboard;
