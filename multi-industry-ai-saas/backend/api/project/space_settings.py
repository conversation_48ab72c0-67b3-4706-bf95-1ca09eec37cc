from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query, Path, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import func, and_, or_
import uuid
from datetime import datetime

from db.database import get_db
from models.space_settings import SpaceSettings, ModuleSpaceSettings
from models.user import User
from schemas.space_settings import (
    SpaceSettingsCreate, SpaceSettingsUpdate, SpaceSettingsResponse,
    ModuleSpaceSettingsCreate, ModuleSpaceSettingsUpdate, ModuleSpaceSettingsResponse
)
from api.deps import get_current_user, get_current_project_id, get_current_tenant_id

router = APIRouter()

# 空间设置API
@router.get("/space-settings", response_model=SpaceSettingsResponse)
async def get_space_settings(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    project_id: uuid.UUID = Depends(get_current_project_id)
):
    """
    获取空间设置
    """
    try:
        # 查询空间设置
        query = select(SpaceSettings).where(
            SpaceSettings.project_id == project_id
        )
        result = await db.execute(query)
        space_settings = result.scalar_one_or_none()

        if not space_settings:
            # 如果不存在，创建默认设置
            space_settings = SpaceSettings(
                id=uuid.uuid4(),
                project_id=project_id,
                created_by=current_user.id,
                updated_by=current_user.id
            )
            db.add(space_settings)
            await db.commit()
            await db.refresh(space_settings)

        # 查询模块设置
        modules_query = select(ModuleSpaceSettings).where(
            ModuleSpaceSettings.space_settings_id == space_settings.id
        )
        result = await db.execute(modules_query)
        module_settings = result.scalars().all()

        # 构建空间设置数据
        space_settings_dict = {
            **space_settings.__dict__,
            "module_settings": module_settings
        }

        return {
            "success": True,
            "data": space_settings_dict
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取空间设置失败: {str(e)}"
        )


@router.put("/space-settings", response_model=SpaceSettingsResponse)
async def update_space_settings(
    settings_update: SpaceSettingsUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    project_id: uuid.UUID = Depends(get_current_project_id)
):
    """
    更新空间设置
    """
    try:
        # 查询空间设置
        query = select(SpaceSettings).where(
            SpaceSettings.project_id == project_id
        )
        result = await db.execute(query)
        space_settings = result.scalar_one_or_none()

        if not space_settings:
            # 如果不存在，创建默认设置
            space_settings = SpaceSettings(
                id=uuid.uuid4(),
                project_id=project_id,
                created_by=current_user.id,
                updated_by=current_user.id
            )
            db.add(space_settings)
            await db.flush()

        # 更新字段
        update_data = settings_update.dict(exclude_unset=True)

        for key, value in update_data.items():
            setattr(space_settings, key, value)

        space_settings.updated_by = current_user.id
        space_settings.updated_at = datetime.now()

        await db.commit()
        await db.refresh(space_settings)

        # 查询模块设置
        modules_query = select(ModuleSpaceSettings).where(
            ModuleSpaceSettings.space_settings_id == space_settings.id
        )
        result = await db.execute(modules_query)
        module_settings = result.scalars().all()

        # 构建空间设置数据
        space_settings_dict = {
            **space_settings.__dict__,
            "module_settings": module_settings
        }

        return {
            "success": True,
            "message": "空间设置更新成功",
            "data": space_settings_dict
        }
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新空间设置失败: {str(e)}"
        )


@router.post("/space-settings/modules", response_model=ModuleSpaceSettingsResponse)
async def create_module_space_settings(
    module_settings: ModuleSpaceSettingsCreate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    project_id: uuid.UUID = Depends(get_current_project_id)
):
    """
    创建模块空间设置
    """
    try:
        # 查询空间设置
        query = select(SpaceSettings).where(
            SpaceSettings.project_id == project_id
        )
        result = await db.execute(query)
        space_settings = result.scalar_one_or_none()

        if not space_settings:
            # 如果不存在，创建默认设置
            space_settings = SpaceSettings(
                id=uuid.uuid4(),
                project_id=project_id,
                created_by=current_user.id,
                updated_by=current_user.id
            )
            db.add(space_settings)
            await db.flush()

        # 检查模块代码是否已存在
        module_query = select(ModuleSpaceSettings).where(
            ModuleSpaceSettings.space_settings_id == space_settings.id,
            ModuleSpaceSettings.module_code == module_settings.module_code
        )
        result = await db.execute(module_query)
        existing_module = result.scalar_one_or_none()

        if existing_module:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="模块代码已存在"
            )

        # 创建模块设置
        new_module = ModuleSpaceSettings(
            id=uuid.uuid4(),
            space_settings_id=space_settings.id,
            module_code=module_settings.module_code,
            module_name=module_settings.module_name,
            max_file_size=module_settings.max_file_size,
            allowed_extensions=module_settings.allowed_extensions
        )

        db.add(new_module)
        await db.commit()
        await db.refresh(new_module)

        return {
            "success": True,
            "message": "模块空间设置创建成功",
            "data": new_module
        }
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建模块空间设置失败: {str(e)}"
        )


@router.put("/space-settings/modules/{module_id}", response_model=ModuleSpaceSettingsResponse)
async def update_module_space_settings(
    module_id: uuid.UUID,
    module_update: ModuleSpaceSettingsUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    project_id: uuid.UUID = Depends(get_current_project_id)
):
    """
    更新模块空间设置
    """
    try:
        # 查询空间设置
        space_query = select(SpaceSettings).where(
            SpaceSettings.project_id == project_id
        )
        result = await db.execute(space_query)
        space_settings = result.scalar_one_or_none()

        if not space_settings:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="空间设置不存在"
            )

        # 查询模块设置
        module_query = select(ModuleSpaceSettings).where(
            ModuleSpaceSettings.id == module_id,
            ModuleSpaceSettings.space_settings_id == space_settings.id
        )
        result = await db.execute(module_query)
        module_settings = result.scalar_one_or_none()

        if not module_settings:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="模块空间设置不存在"
            )

        # 更新字段
        update_data = module_update.dict(exclude_unset=True)

        for key, value in update_data.items():
            setattr(module_settings, key, value)

        module_settings.updated_at = datetime.now()

        await db.commit()
        await db.refresh(module_settings)

        return {
            "success": True,
            "message": "模块空间设置更新成功",
            "data": module_settings
        }
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新模块空间设置失败: {str(e)}"
        )


@router.delete("/space-settings/modules/{module_id}", response_model=dict)
async def delete_module_space_settings(
    module_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    project_id: uuid.UUID = Depends(get_current_project_id)
):
    """
    删除模块空间设置
    """
    try:
        # 查询空间设置
        space_query = select(SpaceSettings).where(
            SpaceSettings.project_id == project_id
        )
        result = await db.execute(space_query)
        space_settings = result.scalar_one_or_none()

        if not space_settings:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="空间设置不存在"
            )

        # 查询模块设置
        module_query = select(ModuleSpaceSettings).where(
            ModuleSpaceSettings.id == module_id,
            ModuleSpaceSettings.space_settings_id == space_settings.id
        )
        result = await db.execute(module_query)
        module_settings = result.scalar_one_or_none()

        if not module_settings:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="模块空间设置不存在"
            )

        # 删除模块设置
        await db.delete(module_settings)
        await db.commit()

        return {
            "success": True,
            "message": "模块空间设置删除成功"
        }
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除模块空间设置失败: {str(e)}"
        )
