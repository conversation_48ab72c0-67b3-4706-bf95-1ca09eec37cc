import api from '../../../../../../services/api/httpClient';
import axios from 'axios';

/**
 * 营销游戏插件API
 */
const marketingGame = {
  /**
   * 获取营销游戏列表
   * @param {Object} params - 查询参数
   * @returns {Promise} - 营销游戏列表
   */
  getGames: async (params = {}) => {
    const projectId = localStorage.getItem('project_id');
    if (!projectId) {
      console.error('获取项目ID失败');
      return Promise.reject(new Error('获取项目ID失败'));
    }

    try {
      const { ...restParams } = params;
      const response = await api.get(`/project/${projectId}/plugin/marketing-game/games`, { params: restParams });

      // 包装响应，确保有 success 属性
      return {
        success: true,
        data: response,
        message: '获取成功'
      };
    } catch (error) {
      // 如果是404错误，返回空数据
      if (error.response && error.response.status === 404) {
        return {
          success: false,
          data: [],
          message: '营销游戏插件未安装或未正确配置'
        };
      }

      return {
        success: false,
        data: [],
        message: error.message || '获取失败'
      };
    }
  },

  /**
   * 获取营销游戏详情（公开接口，用于分享链接）
   * @param {number} gameId - 游戏ID
   * @returns {Promise} - 营销游戏详情
   */
  getPublicGameDetail: async (gameId) => {
    try {
      // 创建一个不带认证的axios实例用于公开API
      const publicApi = axios.create({
        baseURL: '/api/public',
        timeout: 30000,
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await publicApi.get(`/marketing-game/games/${gameId}`);
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error('获取公开游戏详情失败:', error);
      return {
        success: false,
        message: error.response?.data?.detail || error.message || '获取失败'
      };
    }
  },

  /**
   * 获取营销游戏奖品列表（公开接口，用于分享链接）
   * @param {number} gameId - 游戏ID
   * @returns {Promise} - 奖品列表
   */
  getPublicGamePrizes: async (gameId) => {
    try {
      // 创建一个不带认证的axios实例用于公开API
      const publicApi = axios.create({
        baseURL: '/api/public',
        timeout: 30000,
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await publicApi.get(`/marketing-game/games/${gameId}/prizes`);
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error('获取公开游戏奖品失败:', error);
      return {
        success: false,
        message: error.response?.data?.detail || error.message || '获取失败'
      };
    }
  },

  /**
   * 公开抽奖接口（用于外部分享页面）
   * @param {number} gameId - 游戏ID
   * @param {Object} participantData - 参与者数据
   * @returns {Promise} - 抽奖结果
   */
  publicDraw: async (gameId, participantData) => {
    try {
      // 创建一个不带认证的axios实例用于公开API
      const publicApi = axios.create({
        baseURL: '/api/public',
        timeout: 30000,
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await publicApi.post(`/marketing-game/games/${gameId}/draw`, participantData);
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error('公开抽奖失败:', error);
      return {
        success: false,
        message: error.response?.data?.detail || error.message || '抽奖失败'
      };
    }
  },

  /**
   * 获取营销游戏详情
   * @param {number} gameId - 游戏ID
   * @returns {Promise} - 营销游戏详情
   */
  getGameDetail: async (gameId) => {
    // 从localStorage获取项目ID
    const projectId = localStorage.getItem('project_id');
    if (!projectId) {
      console.error('获取项目ID失败');
      return Promise.reject(new Error('获取项目ID失败'));
    }
    try {
      const response = await api.get(`/project/${projectId}/plugin/marketing-game/games/${gameId}`);
      // 包装响应，确保有 success 属性
      return {
        success: true,
        data: response,
        message: '获取成功'
      };
    } catch (error) {
      return {
        success: false,
        data: null,
        message: error.message || '获取失败'
      };
    }
  },

  /**
   * 创建营销游戏
   * @param {Object} data - 游戏数据
   * @returns {Promise} - 创建结果
   */
  createGame: async (data) => {
    // 从localStorage获取项目ID
    const projectId = localStorage.getItem('project_id');
    if (!projectId) {
      console.error('获取项目ID失败');
      return Promise.reject(new Error('获取项目ID失败'));
    }
    try {
      const response = await api.post(`/project/${projectId}/plugin/marketing-game/games`, data);
      // 包装响应，确保有 success 属性
      return {
        success: true,
        data: response,
        message: '创建成功'
      };
    } catch (error) {
      return {
        success: false,
        message: error.message || '创建失败'
      };
    }
  },

  /**
   * 更新营销游戏
   * @param {number} gameId - 游戏ID
   * @param {Object} data - 游戏数据
   * @returns {Promise} - 更新结果
   */
  updateGame: async (gameId, data) => {
    // 从localStorage获取项目ID
    const projectId = localStorage.getItem('project_id');
    if (!projectId) {
      console.error('获取项目ID失败');
      return Promise.reject(new Error('获取项目ID失败'));
    }
    try {
      const response = await api.put(`/project/${projectId}/plugin/marketing-game/games/${gameId}`, data);
      // 包装响应，确保有 success 属性
      return {
        success: true,
        data: response,
        message: '更新成功'
      };
    } catch (error) {
      return {
        success: false,
        message: error.message || '更新失败'
      };
    }
  },

  /**
   * 删除营销游戏
   * @param {number} gameId - 游戏ID
   * @returns {Promise} - 删除结果
   */
  deleteGame: async (gameId) => {
    // 从localStorage获取项目ID
    const projectId = localStorage.getItem('project_id');
    if (!projectId) {
      console.error('获取项目ID失败');
      return Promise.reject(new Error('获取项目ID失败'));
    }
    try {
      const response = await api.delete(`/project/${projectId}/plugin/marketing-game/games/${gameId}`);
      // 包装响应，确保有 success 属性
      return {
        success: true,
        data: response,
        message: '删除成功'
      };
    } catch (error) {
      return {
        success: false,
        message: error.message || '删除失败'
      };
    }
  },

  /**
   * 获取游戏奖品列表
   * @param {number} gameId - 游戏ID
   * @returns {Promise} - 奖品列表
   */
  getPrizes: async (gameId) => {
    // 从localStorage获取项目ID
    const projectId = localStorage.getItem('project_id');
    if (!projectId) {
      console.error('获取项目ID失败');
      return Promise.reject(new Error('获取项目ID失败'));
    }
    try {
      const response = await api.get(`/project/${projectId}/plugin/marketing-game/games/${gameId}/prizes`);
      // 包装响应，确保有 success 属性
      return {
        success: true,
        data: response,
        message: '获取成功'
      };
    } catch (error) {
      return {
        success: false,
        data: [],
        message: error.message || '获取失败'
      };
    }
  },

  /**
   * 获取游戏奖品列表 (别名，保持向后兼容)
   * @param {number} gameId - 游戏ID
   * @returns {Promise} - 奖品列表
   */
  getGamePrizes: async (gameId) => {
    return marketingGame.getPrizes(gameId);
  },

  /**
   * 创建游戏奖品
   * @param {number} gameId - 游戏ID
   * @param {Object} data - 奖品数据
   * @returns {Promise} - 创建结果
   */
  createPrize: async (gameId, data) => {
    // 从localStorage获取项目ID
    const projectId = localStorage.getItem('project_id');
    if (!projectId) {
      console.error('获取项目ID失败');
      return Promise.reject(new Error('获取项目ID失败'));
    }
    try {
      const response = await api.post(`/project/${projectId}/plugin/marketing-game/games/${gameId}/prizes`, data);
      // 包装响应，确保有 success 属性
      return {
        success: true,
        data: response,
        message: '创建成功'
      };
    } catch (error) {
      return {
        success: false,
        data: null,
        message: error.message || '创建失败'
      };
    }
  },

  /**
   * 更新游戏奖品
   * @param {number} gameId - 游戏ID
   * @param {number} prizeId - 奖品ID
   * @param {Object} data - 奖品数据
   * @returns {Promise} - 更新结果
   */
  updatePrize: async (gameId, prizeId, data) => {
    // 从localStorage获取项目ID
    const projectId = localStorage.getItem('project_id');
    if (!projectId) {
      console.error('获取项目ID失败');
      return Promise.reject(new Error('获取项目ID失败'));
    }
    try {
      const response = await api.put(`/project/${projectId}/plugin/marketing-game/games/${gameId}/prizes/${prizeId}`, data);
      // 包装响应，确保有 success 属性
      return {
        success: true,
        data: response,
        message: '更新成功'
      };
    } catch (error) {
      return {
        success: false,
        data: null,
        message: error.message || '更新失败'
      };
    }
  },

  /**
   * 删除游戏奖品
   * @param {number} gameId - 游戏ID
   * @param {number} prizeId - 奖品ID
   * @returns {Promise} - 删除结果
   */
  deletePrize: async (gameId, prizeId) => {
    // 从localStorage获取项目ID
    const projectId = localStorage.getItem('project_id');
    if (!projectId) {
      console.error('获取项目ID失败');
      return Promise.reject(new Error('获取项目ID失败'));
    }
    try {
      const response = await api.delete(`/project/${projectId}/plugin/marketing-game/games/${gameId}/prizes/${prizeId}`);
      // 包装响应，确保有 success 属性
      return {
        success: true,
        data: response,
        message: '删除成功'
      };
    } catch (error) {
      return {
        success: false,
        data: null,
        message: error.message || '删除失败'
      };
    }
  },

  /**
   * 获取游戏参与者列表
   * @param {number} gameId - 游戏ID
   * @param {Object} params - 查询参数
   * @returns {Promise} - 参与者列表
   */
  getParticipants: async (gameId, params = {}) => {
    // 从localStorage获取项目ID
    const projectId = localStorage.getItem('project_id');
    if (!projectId) {
      console.error('获取项目ID失败');
      return Promise.reject(new Error('获取项目ID失败'));
    }
    try {
      const response = await api.get(`/project/${projectId}/plugin/marketing-game/games/${gameId}/participants`, { params });
      // 包装响应，确保有 success 属性
      return {
        success: true,
        data: response,
        message: '获取成功'
      };
    } catch (error) {
      return {
        success: false,
        data: [],
        message: error.message || '获取失败'
      };
    }
  },

  /**
   * 创建游戏参与者
   * @param {number} gameId - 游戏ID
   * @param {Object} data - 参与者数据
   * @returns {Promise} - 创建结果
   */
  createParticipant: async (gameId, data) => {
    // 从localStorage获取项目ID
    const projectId = localStorage.getItem('project_id');
    if (!projectId) {
      console.error('获取项目ID失败');
      return Promise.reject(new Error('获取项目ID失败'));
    }
    try {
      const response = await api.post(`/project/${projectId}/plugin/marketing-game/games/${gameId}/participants`, data);
      // 包装响应，确保有 success 属性
      return {
        success: true,
        data: response,
        message: '创建成功'
      };
    } catch (error) {
      return {
        success: false,
        data: null,
        message: error.message || '创建失败'
      };
    }
  },

  /**
   * 获取游戏记录列表
   * @param {number} gameId - 游戏ID
   * @param {Object} params - 查询参数
   * @returns {Promise} - 记录列表
   */
  getRecords: async (gameId, params = {}) => {
    // 从localStorage获取项目ID
    const projectId = localStorage.getItem('project_id');
    if (!projectId) {
      console.error('获取项目ID失败');
      return Promise.reject(new Error('获取项目ID失败'));
    }
    try {
      const response = await api.get(`/project/${projectId}/plugin/marketing-game/games/${gameId}/records`, { params });
      // 包装响应，确保有 success 属性
      return {
        success: true,
        data: response,
        message: '获取成功'
      };
    } catch (error) {
      return {
        success: false,
        data: [],
        message: error.message || '获取失败'
      };
    }
  },

  /**
   * 创建游戏记录
   * @param {number} gameId - 游戏ID
   * @param {Object} data - 记录数据
   * @returns {Promise} - 创建结果
   */
  createRecord: async (gameId, data) => {
    // 从localStorage获取项目ID
    const projectId = localStorage.getItem('project_id');
    if (!projectId) {
      console.error('获取项目ID失败');
      return Promise.reject(new Error('获取项目ID失败'));
    }
    try {
      const response = await api.post(`/project/${projectId}/plugin/marketing-game/games/${gameId}/records`, data);
      // 包装响应，确保有 success 属性
      return {
        success: true,
        data: response,
        message: '创建成功'
      };
    } catch (error) {
      return {
        success: false,
        data: null,
        message: error.message || '创建失败'
      };
    }
  },

  /**
   * 识别小票
   * @param {number} gameId - 游戏ID
   * @param {File} file - 小票图片文件
   * @returns {Promise} - 识别结果
   */
  recognizeReceipt: async (gameId, file) => {
    // 从localStorage获取项目ID
    const projectId = localStorage.getItem('project_id');
    if (!projectId) {
      console.error('获取项目ID失败');
      return Promise.reject(new Error('获取项目ID失败'));
    }

    const formData = new FormData();
    formData.append('receipt', file);

    try {
      const response = await api.post(`/project/${projectId}/plugin/marketing-game/games/${gameId}/receipt-recognition`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      // 包装响应，确保有 success 属性
      return {
        success: true,
        data: response,
        message: '识别成功'
      };
    } catch (error) {
      return {
        success: false,
        data: null,
        message: error.message || '识别失败'
      };
    }
  },

  /**
   * 抽奖
   * @param {number} gameId - 游戏ID
   * @returns {Promise} - 抽奖结果
   */
  draw: async (gameId) => {
    // 从localStorage获取项目ID
    const projectId = localStorage.getItem('project_id');
    if (!projectId) {
      console.error('获取项目ID失败');
      return Promise.reject(new Error('获取项目ID失败'));
    }
    try {
      console.log(`开始抽奖流程，游戏ID: ${gameId}`);

      // 0. 先获取奖品列表，检查是否还有可用奖品
      const prizesResponse = await api.get(`/project/${projectId}/plugin/marketing-game/games/${gameId}/prizes`);
      const prizes = prizesResponse || [];

      // 检查是否有非默认奖品且剩余数量大于0
      const hasAvailablePrizes = prizes.some(prize =>
        !prize.is_default &&
        (prize.remaining === undefined || prize.remaining > 0)
      );

      if (!hasAvailablePrizes) {
        console.log('没有可用奖品，只能获得默认奖品');
      }

      // 1. 先创建参与者
      // 获取游戏详情，检查是否需要手机号验证
      const gameDetailResponse = await api.get(`/project/${projectId}/plugin/marketing-game/games/${gameId}`);
      const gameSettings = gameDetailResponse?.settings || {};
      const enablePhoneVerification = gameSettings.enable_phone_verification === true;
      const enableReceiptVerification = gameSettings.enable_receipt_verification === true;
      const maxDrawsPerUser = gameDetailResponse?.max_draws_per_user || 0;

      // 获取用户信息
      const userName = localStorage.getItem('user_name') || '匿名用户';
      const userPhone = localStorage.getItem('user_phone') || '';

      // 如果开启了手机号验证，必须有手机号才能参与
      if (enablePhoneVerification && !userPhone) {
        throw new Error('请先输入手机号才能参与游戏');
      }

      // 根据验证设置确定参与者ID
      let participantId;

      // 优先使用手机号作为参与者ID
      if (userPhone) {
        // 如果有手机号，直接使用手机号作为参与者ID
        participantId = userPhone;
        console.log("使用手机号作为参与者ID:", participantId);
        // 保存到localStorage，确保其他地方可以使用
        localStorage.setItem('user_phone', userPhone);
      } else if (enableReceiptVerification) {
        // 如果开启了小票验证，使用小票号作为参与者ID
        const receiptCode = localStorage.getItem('receipt_code') || '';
        participantId = receiptCode || `user_${Date.now()}`;
        console.log("使用小票号作为参与者ID:", participantId);
      } else {
        // 如果都没有，使用时间戳作为参与者ID
        participantId = `user_${Date.now()}`;
        console.log("使用时间戳作为参与者ID:", participantId);
      }

      // 如果设置了每人参与次数限制，检查是否已达到限制
      if (maxDrawsPerUser > 0) {
        try {
          // 获取所有参与者
          const participantsResponse = await api.get(`/project/${projectId}/plugin/marketing-game/games/${gameId}/participants`);
          const participants = Array.isArray(participantsResponse) ? participantsResponse : [];

          // 查找是否已存在相同手机号的参与者
          let existingParticipant = null;
          if (userPhone) {
            existingParticipant = participants.find(p => p.phone === userPhone);
          }

          if (existingParticipant) {
            console.log("找到已存在的参与者:", existingParticipant);

            // 获取该参与者的历史记录
            const recordsResponse = await api.get(`/project/${projectId}/plugin/marketing-game/games/${gameId}/records`);
            const records = Array.isArray(recordsResponse) ? recordsResponse : [];

            // 过滤出当前参与者的记录
            const participantRecords = records.filter(record =>
              record.participant_id === existingParticipant.id
            );

            console.log("当前参与者历史记录:", participantRecords);

            // 检查是否已达到参与次数限制
            if (participantRecords.length >= maxDrawsPerUser) {
              throw new Error(`您已达到最大参与次数(${maxDrawsPerUser}次)`);
            }

            // 使用已存在的参与者ID
            participantId = existingParticipant.id;
            console.log("使用已存在的参与者ID:", participantId);
          }
        } catch (error) {
          if (error.message.includes('最大参与次数')) {
            throw error; // 重新抛出参与次数限制错误
          }
          console.error("检查参与次数失败:", error);
          // 继续执行，不阻止抽奖
        }
      }

      // 如果没有用户名但有手机号，使用手机号作为用户名
      let participantName = userName;

      // 如果用户名是匿名用户或为空，且有手机号，使用手机号作为用户名
      if ((participantName === '匿名用户' || !participantName) && userPhone) {
        participantName = `用户${userPhone.slice(-4)}`;
      }

      // 如果用户名仍然是匿名用户或为空，且有参与者ID且是手机号格式，使用参与者ID作为用户名
      if ((participantName === '匿名用户' || !participantName) &&
          participantId && /^1[3-9]\d{9}$/.test(participantId)) {
        participantName = `用户${participantId.slice(-4)}`;
      }

      console.log("最终使用的参与者名称:", participantName);

      const participantData = {
        name: participantName,
        phone: userPhone || ((/^1[3-9]\d{9}$/.test(participantId)) ? participantId : ''),
        draws_count: 1, // 设置为1，表示已经抽过一次
        is_winner: false,
        is_fixed_winner: false,
        participant_id: participantId
      };

      console.log('创建参与者数据:', participantData);
      const participantResponse = await api.post(`/project/${projectId}/plugin/marketing-game/games/${gameId}/participants`, participantData);
      console.log('参与者创建响应:', participantResponse);

      if (!participantResponse || !participantResponse.id) {
        throw new Error('创建参与者失败');
      }

      // 2. 随机选择一个奖品
      let selectedPrize = null;
      let isWin = false;

      // 如果有可用奖品，根据概率随机选择
      if (prizes && prizes.length > 0) {
        // 过滤出剩余数量大于0的非默认奖品
        const availablePrizes = prizes.filter(prize =>
          !prize.is_default &&
          (prize.remaining === undefined || prize.remaining > 0)
        );

        console.log('可用奖品:', availablePrizes);

        if (availablePrizes.length > 0) {
          // 计算总概率
          const totalProbability = availablePrizes.reduce((sum, prize) => sum + (prize.probability || 0), 0);

          // 生成随机数 (0-totalProbability)
          const random = Math.random() * totalProbability;
          let cumulativeProbability = 0;

          // 根据概率选择奖品
          for (const prize of availablePrizes) {
            cumulativeProbability += (prize.probability || 0);
            if (random <= cumulativeProbability) {
              selectedPrize = prize;
              isWin = true;
              break;
            }
          }

          // 如果没有选中任何奖品，选择默认奖品
          if (!selectedPrize) {
            const defaultPrize = prizes.find(prize => prize.is_default);
            if (defaultPrize) {
              selectedPrize = defaultPrize;
              isWin = false;
            }
          }
        } else {
          // 如果没有可用奖品，选择默认奖品
          const defaultPrize = prizes.find(prize => prize.is_default);
          if (defaultPrize) {
            selectedPrize = defaultPrize;
            isWin = false;
          }
        }
      }

      console.log('选中的奖品:', selectedPrize, '是否中奖:', isWin);

      // 获取当前北京时间的ISO字符串
      const getCurrentBeijingTimeISO = () => {
        const now = new Date();
        // 获取UTC时间的毫秒数
        const utcTime = now.getTime();
        // 北京时间比UTC早8小时，即+8时区
        const beijingTime = new Date(utcTime + (8 * 60 * 60 * 1000));
        // 返回ISO格式的字符串，但移除末尾的Z（表示UTC）
        return beijingTime.toISOString().replace('Z', '');
      };

      // 创建抽奖记录
      const recordData = {
        participant_id: participantResponse.id,
        prize_id: selectedPrize ? selectedPrize.id : null,
        is_win: isWin,
        claim_status: 'unclaimed',
        created_at: getCurrentBeijingTimeISO(), // 使用北京时间
        consumption_amount: parseFloat(localStorage.getItem('receipt_amount') || '0')
      };

      console.log('创建抽奖记录数据:', recordData);
      const response = await api.post(`/project/${projectId}/plugin/marketing-game/games/${gameId}/records`, recordData);
      console.log('抽奖记录创建响应:', response);

      if (!response) {
        throw new Error('创建抽奖记录失败');
      }

      // 3. 获取游戏详情，获取最新的剩余抽奖次数
      console.log('获取游戏详情以更新剩余抽奖次数');
      const gameResponse = await api.get(`/project/${projectId}/plugin/marketing-game/games/${gameId}`);
      console.log('游戏详情响应:', gameResponse);

      // 计算剩余抽奖次数
      let remainingDraws = 0;

      if (gameResponse) {
        if (gameResponse.remaining_draws !== undefined) {
          // 如果API直接返回了剩余次数，使用它
          remainingDraws = gameResponse.remaining_draws;
        } else if (gameResponse.max_draws_per_user) {
          // 否则根据最大抽奖次数和已使用次数计算
          // 直接获取游戏记录列表，然后过滤出当前参与者的记录
          try {
            const allRecords = await api.get(
              `/project/${projectId}/plugin/marketing-game/games/${gameId}/records`
            );

            // 过滤出当前参与者的记录
            const participantRecords = Array.isArray(allRecords)
              ? allRecords.filter(record => record.participant_id === participantResponse.id)
              : [];

            const usedDraws = participantRecords.length || 1; // 至少算1次（当前这次）
            remainingDraws = Math.max(0, gameResponse.max_draws_per_user - usedDraws);
          } catch (error) {
            console.error('获取抽奖记录失败，使用默认剩余次数:', error);
            // 如果获取记录失败，默认减1
            remainingDraws = Math.max(0, gameResponse.max_draws_per_user - 1);
          }
        }
      }

      console.log('计算的剩余抽奖次数:', remainingDraws);

      // 4. 获取中奖奖品详情
      let prizeDetail = null;
      if (response.prize_id) {
        try {
          const prizeResponse = await api.get(`/project/${projectId}/plugin/marketing-game/games/${gameId}/prizes/${response.prize_id}`);
          prizeDetail = prizeResponse;
        } catch (prizeError) {
          console.error('获取奖品详情失败:', prizeError);
        }
      }

      // 确保返回的数据包含prize_id
      // 检查response中是否有prize_id
      const prize_id = response.prize_id || (response.prize && response.prize.id);

      console.log('最终抽奖结果数据:', {
        response,
        prize_id,
        prizeDetail
      });

      // 包装响应，确保有 success 属性和prize_id
      return {
        success: true,
        data: {
          ...response,
          prize_id: prize_id, // 确保prize_id存在
          prize: prizeDetail || response.prize || null
        },
        message: '抽奖成功',
        remaining_draws: remainingDraws
      };
    } catch (error) {
      console.error('抽奖失败:', error);
      return {
        success: false,
        data: null,
        message: error.response?.data?.detail || error.message || '抽奖失败'
      };
    }
  },

  /**
   * 抽奖 (别名，保持向后兼容)
   * @param {number} gameId - 游戏ID
   * @returns {Promise} - 抽奖结果
   */
  drawPrize: async (gameId) => {
    return marketingGame.draw(gameId);
  },

  /**
   * 获取抽奖记录
   * @param {number} gameId - 游戏ID
   * @param {Object} params - 查询参数
   * @returns {Promise} - 抽奖记录
   */
  getDrawRecords: async (gameId, params = {}) => {
    // 从localStorage获取项目ID
    const projectId = localStorage.getItem('project_id');
    if (!projectId) {
      console.error('获取项目ID失败');
      return Promise.reject(new Error('获取项目ID失败'));
    }
    try {
      const response = await api.get(`/project/${projectId}/plugin/marketing-game/games/${gameId}/records`, { params });
      // 包装响应，确保有 success 属性
      return {
        success: true,
        data: response,
        message: '获取成功'
      };
    } catch (error) {
      return {
        success: false,
        data: [],
        message: error.message || '获取失败'
      };
    }
  },

  /**
   * 验证小票
   * @param {number} gameId - 游戏ID
   * @param {Object} data - 小票数据
   * @returns {Promise} - 验证结果
   */
  verifyReceipt: async (gameId, data) => {
    // 从localStorage获取项目ID
    const projectId = localStorage.getItem('project_id');
    if (!projectId) {
      console.error('获取项目ID失败');
      return Promise.reject(new Error('获取项目ID失败'));
    }
    try {
      const response = await api.post(`/project/${projectId}/plugin/marketing-game/games/${gameId}/verify-receipt`, data);
      // 包装响应，确保有 success 属性
      return {
        success: true,
        data: response,
        message: '验证成功'
      };
    } catch (error) {
      return {
        success: false,
        data: null,
        message: error.message || '验证失败'
      };
    }
  },

  /**
   * 验证手机号
   * @param {number} gameId - 游戏ID
   * @param {Object} data - 手机号数据
   * @returns {Promise} - 验证结果
   */
  verifyPhone: async (gameId, data) => {
    // 从localStorage获取项目ID
    const projectId = localStorage.getItem('project_id');
    if (!projectId) {
      console.error('获取项目ID失败');
      return Promise.reject(new Error('获取项目ID失败'));
    }
    try {
      const response = await api.post(`/project/${projectId}/plugin/marketing-game/games/${gameId}/verify-phone`, data);
      // 包装响应，确保有 success 属性
      return {
        success: true,
        data: response,
        message: '验证成功'
      };
    } catch (error) {
      return {
        success: false,
        data: null,
        message: error.message || '验证失败'
      };
    }
  },

  /**
   * 更新游戏记录
   * @param {number} gameId - 游戏ID
   * @param {number} recordId - 记录ID
   * @param {Object} data - 更新数据
   * @returns {Promise} - 更新结果
   */
  updateRecord: async (gameId, recordId, data) => {
    // 从localStorage获取项目ID
    const projectId = localStorage.getItem('project_id');
    if (!projectId) {
      console.error('获取项目ID失败');
      return Promise.reject(new Error('获取项目ID失败'));
    }
    try {
      const response = await api.put(`/project/${projectId}/plugin/marketing-game/games/${gameId}/records/${recordId}`, data);
      // 包装响应，确保有 success 属性
      return {
        success: true,
        data: response,
        message: '更新成功'
      };
    } catch (error) {
      return {
        success: false,
        data: null,
        message: error.message || '更新失败'
      };
    }
  },

  // 一键初始化营销游戏数据
  initializeGames: async () => {
    try {
      const projectId = localStorage.getItem('project_id');
      if (!projectId) {
        throw new Error('获取项目ID失败');
      }

      const response = await api.post(`/project/${projectId}/plugin/marketing-game/initialize`);
      return {
        success: true,
        data: response.data,
        message: response.data.message || '初始化成功'
      };
    } catch (error) {
      console.error('初始化营销游戏数据失败:', error);
      return {
        success: false,
        message: error.response?.data?.detail || error.message || '初始化失败'
      };
    }
  },

  // 初始化单个游戏数据
  initializeSingleGame: async (gameId) => {
    try {
      const projectId = localStorage.getItem('project_id');
      if (!projectId) {
        throw new Error('获取项目ID失败');
      }

      const response = await api.post(`/project/${projectId}/plugin/marketing-game/games/${gameId}/initialize`);
      return {
        success: true,
        data: response.data,
        message: response.data.message || '游戏初始化成功'
      };
    } catch (error) {
      console.error('初始化单个游戏数据失败:', error);
      return {
        success: false,
        message: error.response?.data?.detail || error.message || '初始化失败'
      };
    }
  },

  // 获取营销游戏统计数据
  getStatistics: async () => {
    try {
      const projectId = localStorage.getItem('project_id');
      if (!projectId) {
        throw new Error('获取项目ID失败');
      }

      const response = await api.get(`/project/${projectId}/plugin/marketing-game/statistics`);
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error('获取统计数据失败:', error);
      return {
        success: false,
        message: error.response?.data?.detail || error.message || '获取统计数据失败'
      };
    }
  },

  /**
   * 获取用户游戏统计
   * @param {number} gameId - 游戏ID
   * @returns {Promise} - 用户统计数据
   */
  getUserStats: async (gameId) => {
    // 从localStorage获取项目ID
    const projectId = localStorage.getItem('project_id');
    if (!projectId) {
      console.error('获取项目ID失败');
      return Promise.reject(new Error('获取项目ID失败'));
    }
    try {
      const response = await api.get(`/project/${projectId}/plugin/marketing-game/games/${gameId}/user-stats`);
      // 包装响应，确保有 success 属性
      return {
        success: true,
        data: response,
        message: '获取成功'
      };
    } catch (error) {
      return {
        success: false,
        data: null,
        message: error.message || '获取失败'
      };
    }
  },

  // 领取奖品（公开接口）
  claimPrize: async (gameId, recordId, participantData) => {
    try {
      const API_BASE_URL = window.location.origin;
      const response = await fetch(`${API_BASE_URL}/api/public/marketing-game/games/${gameId}/claim/${recordId}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(participantData)
      });

      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.detail || '领取奖品失败');
      }

      return data;
    } catch (error) {
      console.error('领取奖品失败:', error);
      return {
        success: false,
        message: error.message || '领取奖品失败'
      };
    }
  },

  // 根据手机号查询中奖记录（公开接口）
  queryPrizesByPhone: async (gameId, phone) => {
    try {
      const API_BASE_URL = window.location.origin;
      const response = await fetch(`${API_BASE_URL}/api/public/marketing-game/games/${gameId}/prizes/query/${phone}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      // 检查响应状态
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      // 检查响应内容类型
      const contentType = response.headers.get('content-type');
      if (!contentType || !contentType.includes('application/json')) {
        const text = await response.text();
        console.error('非JSON响应:', text);
        throw new Error('服务器返回了非JSON格式的响应');
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error('查询中奖记录失败:', error);
      return {
        success: false,
        message: error.message || '查询中奖记录失败'
      };
    }
  },

  // 公开手机号验证接口
  verifyPhonePublic: async (gameId, data) => {
    try {
      const API_BASE_URL = window.location.origin;
      const response = await fetch(`${API_BASE_URL}/api/public/marketing-game/games/${gameId}/verify-phone`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
      });

      const result = await response.json();
      
      if (!response.ok) {
        throw new Error(result.detail || '手机号验证失败');
      }

      return {
        success: true,
        data: result,
        message: '验证成功'
      };
    } catch (error) {
      console.error('手机号验证失败:', error);
      return {
        success: false,
        message: error.message || '手机号验证失败'
      };
    }
  },

  // 公开小票验证接口
  verifyReceiptPublic: async (gameId, data) => {
    try {
      const API_BASE_URL = window.location.origin;
      const response = await fetch(`${API_BASE_URL}/api/public/marketing-game/games/${gameId}/verify-receipt`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
      });

      const result = await response.json();
      
      if (!response.ok) {
        throw new Error(result.detail || '小票验证失败');
      }

      return {
        success: true,
        data: result,
        message: '验证成功'
      };
    } catch (error) {
      console.error('小票验证失败:', error);
      return {
        success: false,
        message: error.message || '小票验证失败'
      };
    }
  }
};

export default marketingGame;
