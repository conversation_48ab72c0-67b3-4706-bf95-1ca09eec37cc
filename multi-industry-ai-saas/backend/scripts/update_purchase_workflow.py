#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
更新采购工作流脚本
- 删除默认采购工作流
- 创建采购分拨直配工作流
"""

import asyncio
import uuid
import sys
import os
import json
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy import text
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker

# 数据库连接配置
DATABASE_URL = os.environ.get("DATABASE_URL", "postgresql+asyncpg://postgres:postgres@db:5432/retail_ai")
engine = create_async_engine(DATABASE_URL)
async_session = sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)


async def delete_default_purchase_workflow(db: AsyncSession, project_id: str, project_name: str):
    """删除默认采购工作流"""
    # 查询默认采购工作流
    query = text("""
        SELECT id FROM workflows
        WHERE project_id = :project_id
        AND type = 'purchase_order'
        AND code = 'default_purchase_workflow'
    """)

    result = await db.execute(query, {"project_id": project_id})
    workflow = result.fetchone()

    if not workflow:
        print(f"项目 {project_name} 不存在默认采购工作流，跳过删除")
        return

    workflow_id = workflow[0]
    print(f"删除项目 {project_name} 的默认采购工作流")

    # 删除工作流节点和转换
    await db.execute(
        text("DELETE FROM workflow_transitions WHERE workflow_id = :workflow_id"),
        {"workflow_id": workflow_id}
    )

    await db.execute(
        text("DELETE FROM workflow_nodes WHERE workflow_id = :workflow_id"),
        {"workflow_id": workflow_id}
    )

    # 删除工作流
    await db.execute(
        text("DELETE FROM workflows WHERE id = :workflow_id"),
        {"workflow_id": workflow_id}
    )

    await db.commit()
    print(f"已删除项目 {project_name} 的默认采购工作流")


async def create_purchase_distribution_workflow(db: AsyncSession, project_id: str, project_name: str, tenant_id: str, admin_id: str):
    """创建采购分拨直配工作流"""
    # 检查是否已存在
    query = text("""
        SELECT id FROM workflows
        WHERE project_id = :project_id
        AND type = 'purchase_order'
        AND code = 'purchase_distribution_workflow'
    """)

    result = await db.execute(query, {"project_id": project_id})
    existing_workflow = result.fetchone()

    if existing_workflow:
        print(f"项目 {project_name} 已存在采购分拨直配工作流，跳过创建")
        return

    print(f"为项目 {project_name} 创建采购分拨直配工作流")

    # 创建工作流
    workflow_id = str(uuid.uuid4())
    now = datetime.utcnow()

    config = {
        "notify_enabled": True,
        "notify_roles": ["store_admin", "finance"],
        "notify_template": "采购专员 {{user_name}} 提交了采购分拨单，供应商: {{supplier_name}}，总金额: {{total_amount}}元，请及时确认到货"
    }

    await db.execute(
        text("""
            INSERT INTO workflows (
                id, tenant_id, project_id, name, code, description, type,
                is_default, is_active, config, created_at, updated_at, created_by, updated_by
            ) VALUES (
                :id, :tenant_id, :project_id, :name, :code, :description, :type,
                :is_default, :is_active, :config, :created_at, :updated_at, :created_by, :updated_by
            )
        """),
        {
            "id": workflow_id,
            "tenant_id": tenant_id,
            "project_id": project_id,
            "name": "采购分拨直配工作流",
            "code": "purchase_distribution_workflow",
            "description": "采购创建采购分拨单，门店到货确认，财务数据审核",
            "type": "purchase_order",
            "is_default": True,
            "is_active": True,
            "config": json.dumps(config),
            "created_at": now,
            "updated_at": now,
            "created_by": admin_id,
            "updated_by": admin_id
        }
    )

    # 创建节点
    start_node_id = str(uuid.uuid4())
    store_confirm_node_id = str(uuid.uuid4())
    finance_review_node_id = str(uuid.uuid4())
    end_node_id = str(uuid.uuid4())

    # 开始节点配置
    start_node_config = {
        "roles": ["purchase"],
        "form_fields": [
            {"name": "supplier_id", "label": "供应商", "type": "select", "required": True},
            {"name": "expected_delivery_date", "label": "预计交付日期", "type": "date", "required": True},
            {"name": "items", "label": "采购项目", "type": "table", "required": True, "columns": [
                {"name": "product_id", "label": "产品", "type": "select", "required": True},
                {"name": "quantity", "label": "数量", "type": "number", "required": True},
                {"name": "price", "label": "单价", "type": "number", "required": True},
                {"name": "amount", "label": "金额", "type": "number", "required": True}
            ]},
            {"name": "distribution", "label": "分拨信息", "type": "table", "required": True, "columns": [
                {"name": "store_id", "label": "门店", "type": "select", "required": True},
                {"name": "product_id", "label": "产品", "type": "select", "required": True},
                {"name": "quantity", "label": "数量", "type": "number", "required": True}
            ]},
            {"name": "total_amount", "label": "总金额", "type": "number", "required": True},
            {"name": "remark", "label": "备注", "type": "textarea", "required": False}
        ],
        "notify": {
            "enabled": True,
            "roles": ["store_admin"],
            "title": "新采购分拨通知",
            "template": "采购专员 {{user_name}} 提交了采购分拨申请，供应商: {{supplier_name}}，总金额: {{total_amount}}元，请注意查收"
        }
    }

    # 门店确认节点配置
    store_confirm_node_config = {
        "roles": ["store_admin"],
        "form_fields": [
            {"name": "arrival_date", "label": "到货日期", "type": "date", "required": True},
            {"name": "items", "label": "到货项目", "type": "table", "required": True, "columns": [
                {"name": "product_id", "label": "产品", "type": "select", "required": True},
                {"name": "expected_quantity", "label": "预期数量", "type": "number", "required": True},
                {"name": "actual_quantity", "label": "实际数量", "type": "number", "required": True},
                {"name": "is_qualified", "label": "是否合格", "type": "checkbox", "required": True}
            ]},
            {"name": "comment", "label": "到货意见", "type": "textarea", "required": False}
        ],
        "notify": {
            "enabled": True,
            "roles": ["purchase", "finance"],
            "title": "门店到货确认通知",
            "template": "门店 {{store_name}} 已确认商品到货，到货日期: {{arrival_date}}，请查看详情"
        }
    }

    # 财务审核节点配置
    finance_review_node_config = {
        "roles": ["finance"],
        "form_fields": [
            {"name": "review_result", "label": "审核结果", "type": "select", "required": True, "options": [
                {"label": "通过", "value": "approved"},
                {"label": "退回", "value": "rejected"}
            ]},
            {"name": "comment", "label": "审核意见", "type": "textarea", "required": False}
        ],
        "notify": {
            "enabled": True,
            "roles": ["purchase", "store_admin"],
            "title": "财务审核通知",
            "template": "财务已{{review_result == 'approved' ? '通过' : '退回'}}采购分拨数据，审核意见: {{comment}}"
        }
    }

    # 结束节点配置
    end_node_config = {
        "notify": {
            "enabled": True,
            "roles": ["purchase", "store_admin", "finance"],
            "title": "采购分拨流程完成通知",
            "template": "采购分拨流程已完成"
        }
    }

    # 插入节点
    await db.execute(
        text("""
            INSERT INTO workflow_nodes (
                id, workflow_id, name, code, description, type,
                "order", config, created_at, updated_at
            ) VALUES (
                :id, :workflow_id, :name, :code, :description, :type,
                :order, :config, :created_at, :updated_at
            )
        """),
        {
            "id": start_node_id,
            "workflow_id": workflow_id,
            "name": "发起采购分拨",
            "code": "start",
            "description": "采购专员发起采购分拨申请",
            "type": "start",
            "order": 1,
            "config": json.dumps(start_node_config),
            "created_at": now,
            "updated_at": now
        }
    )

    await db.execute(
        text("""
            INSERT INTO workflow_nodes (
                id, workflow_id, name, code, description, type,
                "order", config, created_at, updated_at
            ) VALUES (
                :id, :workflow_id, :name, :code, :description, :type,
                :order, :config, :created_at, :updated_at
            )
        """),
        {
            "id": store_confirm_node_id,
            "workflow_id": workflow_id,
            "name": "门店到货确认",
            "code": "store_confirm",
            "description": "门店确认商品到货",
            "type": "approval",
            "order": 2,
            "config": json.dumps(store_confirm_node_config),
            "created_at": now,
            "updated_at": now
        }
    )

    await db.execute(
        text("""
            INSERT INTO workflow_nodes (
                id, workflow_id, name, code, description, type,
                "order", config, created_at, updated_at
            ) VALUES (
                :id, :workflow_id, :name, :code, :description, :type,
                :order, :config, :created_at, :updated_at
            )
        """),
        {
            "id": finance_review_node_id,
            "workflow_id": workflow_id,
            "name": "财务数据审核",
            "code": "finance_review",
            "description": "财务审核采购分拨数据",
            "type": "approval",
            "order": 3,
            "config": json.dumps(finance_review_node_config),
            "created_at": now,
            "updated_at": now
        }
    )

    await db.execute(
        text("""
            INSERT INTO workflow_nodes (
                id, workflow_id, name, code, description, type,
                "order", config, created_at, updated_at
            ) VALUES (
                :id, :workflow_id, :name, :code, :description, :type,
                :order, :config, :created_at, :updated_at
            )
        """),
        {
            "id": end_node_id,
            "workflow_id": workflow_id,
            "name": "完成",
            "code": "end",
            "description": "采购分拨流程完成",
            "type": "end",
            "order": 4,
            "config": json.dumps(end_node_config),
            "created_at": now,
            "updated_at": now
        }
    )

    # 创建转换
    # 提交到门店确认
    await db.execute(
        text("""
            INSERT INTO workflow_transitions (
                id, workflow_id, source_node_id, target_node_id, name,
                condition, created_at, updated_at
            ) VALUES (
                :id, :workflow_id, :source_node_id, :target_node_id, :name,
                :condition, :created_at, :updated_at
            )
        """),
        {
            "id": str(uuid.uuid4()),
            "workflow_id": workflow_id,
            "source_node_id": start_node_id,
            "target_node_id": store_confirm_node_id,
            "name": "提交到门店确认",
            "condition": None,
            "created_at": now,
            "updated_at": now
        }
    )

    # 提交到财务审核
    await db.execute(
        text("""
            INSERT INTO workflow_transitions (
                id, workflow_id, source_node_id, target_node_id, name,
                condition, created_at, updated_at
            ) VALUES (
                :id, :workflow_id, :source_node_id, :target_node_id, :name,
                :condition, :created_at, :updated_at
            )
        """),
        {
            "id": str(uuid.uuid4()),
            "workflow_id": workflow_id,
            "source_node_id": store_confirm_node_id,
            "target_node_id": finance_review_node_id,
            "name": "提交到财务审核",
            "condition": None,
            "created_at": now,
            "updated_at": now
        }
    )

    # 审核通过
    await db.execute(
        text("""
            INSERT INTO workflow_transitions (
                id, workflow_id, source_node_id, target_node_id, name,
                condition, created_at, updated_at
            ) VALUES (
                :id, :workflow_id, :source_node_id, :target_node_id, :name,
                :condition, :created_at, :updated_at
            )
        """),
        {
            "id": str(uuid.uuid4()),
            "workflow_id": workflow_id,
            "source_node_id": finance_review_node_id,
            "target_node_id": end_node_id,
            "name": "审核通过",
            "condition": json.dumps({"field": "review_result", "operator": "==", "value": "approved"}),
            "created_at": now,
            "updated_at": now
        }
    )

    # 退回门店
    await db.execute(
        text("""
            INSERT INTO workflow_transitions (
                id, workflow_id, source_node_id, target_node_id, name,
                condition, created_at, updated_at
            ) VALUES (
                :id, :workflow_id, :source_node_id, :target_node_id, :name,
                :condition, :created_at, :updated_at
            )
        """),
        {
            "id": str(uuid.uuid4()),
            "workflow_id": workflow_id,
            "source_node_id": finance_review_node_id,
            "target_node_id": store_confirm_node_id,
            "name": "退回门店",
            "condition": json.dumps({"field": "review_result", "operator": "==", "value": "rejected"}),
            "created_at": now,
            "updated_at": now
        }
    )

    await db.commit()
    print(f"已为项目 {project_name} 创建采购分拨直配工作流")


async def main():
    """主函数"""
    # 创建数据库会话
    async with async_session() as db:
        try:
            # 获取所有项目
            projects_query = text("""
                SELECT id, name, tenant_id FROM projects
            """)
            result = await db.execute(projects_query)
            projects = result.fetchall()

            if not projects:
                print("没有找到项目，跳过工作流更新")
                return

            # 获取系统管理员用户
            admin_query = text("""
                SELECT id FROM users WHERE is_system_admin = true LIMIT 1
            """)
            result = await db.execute(admin_query)
            admin = result.fetchone()

            if not admin:
                print("没有找到系统管理员用户，跳过工作流更新")
                return

            admin_id = admin[0]

            # 为每个项目更新工作流
            for project in projects:
                project_id = project[0]
                project_name = project[1]
                tenant_id = project[2]

                await delete_default_purchase_workflow(db, project_id, project_name)
                await create_purchase_distribution_workflow(db, project_id, project_name, tenant_id, admin_id)

            print("采购工作流更新完成")
        except Exception as e:
            print(f"更新采购工作流失败: {str(e)}")
            await db.rollback()
            raise


if __name__ == "__main__":
    asyncio.run(main())
