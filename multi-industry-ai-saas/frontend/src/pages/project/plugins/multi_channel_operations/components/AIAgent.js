import React, { useState, useEffect, useRef } from 'react';
import {
  Card,
  Input,
  Button,
  Space,
  List,
  Avatar,
  Typography,
  Tag,
  Divider,
  Row,
  Col,
  Spin,
  Empty,
  message,
  Tooltip,
  Badge,
  Modal,
  Collapse
} from 'antd';
import {
  SendOutlined,
  RobotOutlined,
  UserOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  InfoCircleOutlined,
  BulbOutlined,
  HistoryOutlined
} from '@ant-design/icons';
import { useParams } from 'react-router-dom';
import multiChannelService from '../services/multiChannelService';

const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;
const { Panel } = Collapse;

const AIAgent = () => {
  const { projectId } = useParams();
  const [loading, setLoading] = useState(false);
  const [messages, setMessages] = useState([]);
  const [inputValue, setInputValue] = useState('');
  const [conversationId, setConversationId] = useState(null);
  const [capabilities, setCapabilities] = useState(null);
  const [taskHistory, setTaskHistory] = useState([]);
  const [capabilitiesModalVisible, setCapabilitiesModalVisible] = useState(false);
  const messagesEndRef = useRef(null);

  useEffect(() => {
    loadCapabilities();
    loadTaskHistory();
    // 初始化对话
    addMessage('assistant', '👋 您好！我是全渠道运营AI助手，可以帮您处理商品同步、价格管理、图片生成等任务。请告诉我您需要什么帮助！');
  }, []);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const loadCapabilities = async () => {
    try {
      const response = await multiChannelService.getAICapabilities(projectId);
      if (response.success) {
        setCapabilities(response.data);
      }
    } catch (error) {
      console.error('获取AI能力失败:', error);
    }
  };

  const loadTaskHistory = async () => {
    try {
      const response = await multiChannelService.getAITasks(projectId, {
        limit: 10
      });
      setTaskHistory(response || []);
    } catch (error) {
      console.error('获取任务历史失败:', error);
    }
  };

  const addMessage = (role, content, actions = [], data = {}) => {
    const message = {
      id: Date.now(),
      role,
      content,
      actions,
      data,
      timestamp: new Date().toLocaleTimeString()
    };
    setMessages(prev => [...prev, message]);
  };

  const handleSendMessage = async () => {
    if (!inputValue.trim()) return;

    const userMessage = inputValue.trim();
    setInputValue('');

    // 添加用户消息
    addMessage('user', userMessage);

    try {
      setLoading(true);

      const response = await multiChannelService.chatWithAI(projectId, {
        message: userMessage,
        conversation_id: conversationId
      });

      if (response.success) {
        // 设置对话ID
        if (!conversationId && response.task_id) {
          setConversationId(response.task_id);
        }

        // 添加AI回复
        addMessage('assistant', response.response, response.actions, response.data);

        // 刷新任务历史
        loadTaskHistory();
      } else {
        addMessage('assistant', '抱歉，处理您的请求时出现了问题，请稍后重试。');
      }
    } catch (error) {
      console.error('发送消息失败:', error);
      addMessage('assistant', '网络连接异常，请检查网络后重试。');
    } finally {
      setLoading(false);
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const getActionIcon = (action) => {
    const iconMap = {
      'product_sync': <CheckCircleOutlined style={{ color: '#52c41a' }} />,
      'price_update': <InfoCircleOutlined style={{ color: '#1890ff' }} />,
      'image_generation': <BulbOutlined style={{ color: '#faad14' }} />,
      'channel_analysis': <HistoryOutlined style={{ color: '#722ed1' }} />,
      'automation_setup': <ExclamationCircleOutlined style={{ color: '#f5222d' }} />
    };
    return iconMap[action] || <InfoCircleOutlined />;
  };

  const getActionText = (action) => {
    const textMap = {
      'product_sync': '商品同步',
      'price_update': '价格更新',
      'image_generation': '图片生成',
      'channel_analysis': '渠道分析',
      'automation_setup': '自动化设置',
      'general_help': '帮助说明'
    };
    return textMap[action] || action;
  };

  const getTaskStatusTag = (status) => {
    const statusMap = {
      'pending': <Tag color="default">等待中</Tag>,
      'processing': <Tag color="processing">处理中</Tag>,
      'completed': <Tag color="success">已完成</Tag>,
      'failed': <Tag color="error">失败</Tag>
    };
    return statusMap[status] || <Tag>{status}</Tag>;
  };

  return (
    <div>
      {/* 智能体状态概览 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <div style={{ textAlign: 'center' }}>
              <Avatar 
                size={64} 
                icon={<RobotOutlined />} 
                style={{ 
                  backgroundColor: getStatusColor(agentStatus),
                  marginBottom: 12
                }} 
              />
              <div>
                <Text strong>AI运营智能体</Text>
                <div style={{ marginTop: 4 }}>
                  <Tag color={getStatusColor(agentStatus)}>
                    {getStatusText(agentStatus)}
                  </Tag>
                </div>
                <div style={{ marginTop: 12 }}>
                  {agentStatus === 'stopped' ? (
                    <Button 
                      type="primary" 
                      icon={<PlayCircleOutlined />}
                      onClick={handleStartAgent}
                    >
                      启动
                    </Button>
                  ) : (
                    <Button 
                      danger 
                      icon={<PauseCircleOutlined />}
                      onClick={handleStopAgent}
                    >
                      停止
                    </Button>
                  )}
                </div>
              </div>
            </div>
          </Card>
        </Col>
        
        <Col span={6}>
          <Card>
            <Statistic
              title="总决策次数"
              value={agentMetrics.total_decisions}
              prefix={<ThunderboltOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        
        <Col span={6}>
          <Card>
            <Statistic
              title="成功率"
              value={agentMetrics.success_rate}
              suffix="%"
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        
        <Col span={6}>
          <Card>
            <Statistic
              title="收益影响"
              value={agentMetrics.revenue_impact}
              prefix="¥"
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 智能体能力 */}
      <Card title="智能体能力" style={{ marginBottom: 24 }}>
        <Row gutter={16}>
          <Col span={8}>
            <Card size="small">
              <div style={{ textAlign: 'center' }}>
                <BulbOutlined style={{ fontSize: 24, color: '#1890ff', marginBottom: 8 }} />
                <div>
                  <Text strong>智能定价</Text>
                  <div style={{ marginTop: 4 }}>
                    <Text type="secondary" style={{ fontSize: 12 }}>
                      基于市场数据自动调整价格
                    </Text>
                  </div>
                  <div style={{ marginTop: 8 }}>
                    <Switch 
                      checked={agentConfig.auto_pricing} 
                      size="small"
                      onChange={(checked) => setAgentConfig({...agentConfig, auto_pricing: checked})}
                    />
                  </div>
                </div>
              </div>
            </Card>
          </Col>
          
          <Col span={8}>
            <Card size="small">
              <div style={{ textAlign: 'center' }}>
                <EyeOutlined style={{ fontSize: 24, color: '#52c41a', marginBottom: 8 }} />
                <div>
                  <Text strong>竞品监控</Text>
                  <div style={{ marginTop: 4 }}>
                    <Text type="secondary" style={{ fontSize: 12 }}>
                      实时监控竞品动态
                    </Text>
                  </div>
                  <div style={{ marginTop: 8 }}>
                    <Switch 
                      checked={agentConfig.competitor_monitoring} 
                      size="small"
                      onChange={(checked) => setAgentConfig({...agentConfig, competitor_monitoring: checked})}
                    />
                  </div>
                </div>
              </div>
            </Card>
          </Col>
          
          <Col span={8}>
            <Card size="small">
              <div style={{ textAlign: 'center' }}>
                <ThunderboltOutlined style={{ fontSize: 24, color: '#fa8c16', marginBottom: 8 }} />
                <div>
                  <Text strong>库存管理</Text>
                  <div style={{ marginTop: 4 }}>
                    <Text type="secondary" style={{ fontSize: 12 }}>
                      智能库存预警和补货
                    </Text>
                  </div>
                  <div style={{ marginTop: 8 }}>
                    <Switch 
                      checked={agentConfig.inventory_management} 
                      size="small"
                      onChange={(checked) => setAgentConfig({...agentConfig, inventory_management: checked})}
                    />
                  </div>
                </div>
              </div>
            </Card>
          </Col>
        </Row>
        
        <div style={{ textAlign: 'right', marginTop: 16 }}>
          <Button 
            icon={<SettingOutlined />}
            onClick={() => setConfigModalVisible(true)}
          >
            高级配置
          </Button>
        </div>
      </Card>

      {/* 运行状态和日志 */}
      <Row gutter={16}>
        <Col span={12}>
          <Card title="运行状态" style={{ height: 400 }}>
            {agentStatus === 'running' && (
              <div>
                <Alert
                  message="智能体正在运行"
                  description="AI正在分析市场数据，执行优化策略..."
                  type="success"
                  showIcon
                  style={{ marginBottom: 16 }}
                />
                <div style={{ marginBottom: 16 }}>
                  <Text>当前任务进度</Text>
                  <Progress percent={75} status="active" />
                </div>
              </div>
            )}
            
            {agentStatus === 'stopped' && (
              <Alert
                message="智能体已停止"
                description="点击启动按钮开始AI自动化运营"
                type="info"
                showIcon
              />
            )}
          </Card>
        </Col>
        
        <Col span={12}>
          <Card title="执行日志" style={{ height: 400 }}>
            <List
              size="small"
              dataSource={agentLogs}
              renderItem={item => (
                <List.Item>
                  <List.Item.Meta
                    avatar={getActionIcon(item.result)}
                    title={
                      <Space>
                        <Text strong>{item.action}</Text>
                        <Text type="secondary">- {item.target}</Text>
                      </Space>
                    }
                    description={
                      <div>
                        <div style={{ fontSize: 12 }}>{item.details}</div>
                        <div style={{ marginTop: 4 }}>
                          <Tag color="blue" size="small">{item.impact}</Tag>
                          <Text type="secondary" style={{ fontSize: 11 }}>
                            {new Date(item.timestamp).toLocaleString()}
                          </Text>
                        </div>
                      </div>
                    }
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>
      </Row>

      {/* 配置模态框 */}
      <Modal
        title="AI智能体高级配置"
        open={configModalVisible}
        onCancel={() => setConfigModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleConfigAgent}
          initialValues={agentConfig}
        >
          <Form.Item
            name="decision_frequency"
            label="决策频率"
          >
            <Select placeholder="选择决策频率">
              <Option value="realtime">实时</Option>
              <Option value="hourly">每小时</Option>
              <Option value="daily">每天</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="risk_tolerance"
            label="风险容忍度"
          >
            <Select placeholder="选择风险容忍度">
              <Option value="conservative">保守</Option>
              <Option value="moderate">适中</Option>
              <Option value="aggressive">激进</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="max_price_change"
            label="最大价格变动幅度(%)"
          >
            <InputNumber
              style={{ width: '100%' }}
              min={1}
              max={50}
              placeholder="输入最大价格变动幅度"
            />
          </Form.Item>

          <div style={{ textAlign: 'right', marginTop: 24 }}>
            <Space>
              <Button onClick={() => setConfigModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                保存配置
              </Button>
            </Space>
          </div>
        </Form>
      </Modal>
    </div>
  );
};

export default AIAgent;
