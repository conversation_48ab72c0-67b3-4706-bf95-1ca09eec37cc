import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Card,
  Button,
  Descriptions,
  Spin,
  Empty,
  Tag,
  Space,
  Divider,
  Typography,
  Avatar,
  List,
  message,
  Timeline,
  Row,
  Col,
  Image
} from 'antd';
import {
  ArrowLeftOutlined,
  EditOutlined,
  DeleteOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  StopOutlined,
  UserOutlined,
  FileOutlined,
  CalendarOutlined,
  TagOutlined,
  ShopOutlined
} from '@ant-design/icons';
import dayjs from 'dayjs';
import apiService from '../../../services/api';

const { Title, Paragraph, Text } = Typography;

// 任务类别映射
const categoryTexts = {
  store: '门店',
  operation: '运营',
  marketing: '营销',
  inventory: '库存',
  finance: '财务',
  other: '其他'
};

// 任务状态映射
const statusTexts = {
  pending: '待处理',
  in_progress: '进行中',
  completed: '已完成',
  cancelled: '已取消'
};

// 任务状态颜色映射
const statusColors = {
  pending: 'orange',
  in_progress: 'blue',
  completed: 'green',
  cancelled: 'red'
};

// 任务优先级映射
const priorityTexts = {
  low: '低',
  medium: '中',
  high: '高'
};

// 任务优先级颜色映射
const priorityColors = {
  low: 'green',
  medium: 'blue',
  high: 'red'
};

const RoutineTaskDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();

  const [loading, setLoading] = useState(true);
  const [task, setTask] = useState(null);
  const [comments, setComments] = useState([]);
  const [loadingComments, setLoadingComments] = useState(false);

  // 获取任务详情
  const fetchTaskDetail = async () => {
    setLoading(true);
    try {
      const response = await apiService.project.routineTask.getDetail(id);
      if (response) {
        setTask(response);
      } else {
        message.error('获取任务详情失败');
      }
    } catch (error) {
      console.error('获取任务详情失败:', error);
      message.error('获取任务详情失败: ' + (error.response?.data?.detail || error.message));
    } finally {
      setLoading(false);
    }
  };

  // 获取任务评论
  const fetchTaskComments = async () => {
    setLoadingComments(true);
    try {
      const response = await apiService.project.routineTask.getComments(id);
      if (response && response.items) {
        setComments(response.items);
      } else {
        setComments([]);
      }
    } catch (error) {
      console.error('获取任务评论失败:', error);
      message.error('获取任务评论失败: ' + (error.response?.data?.detail || error.message));
      setComments([]);
    } finally {
      setLoadingComments(false);
    }
  };

  // 初始化加载
  useEffect(() => {
    if (id) {
      fetchTaskDetail();
      fetchTaskComments();
    }
  }, [id]);

  // 返回列表
  const handleBack = () => {
    navigate('/project/store/routine-tasks');
  };

  // 编辑任务
  const handleEdit = () => {
    navigate(`/project/store/routine-tasks/edit/${id}`);
  };

  // 渲染加载状态
  if (loading) {
    return (
      <Card>
        <div style={{ textAlign: 'center', padding: '50px 0' }}>
          <Spin size="large" />
          <p style={{ marginTop: 16 }}>加载中...</p>
        </div>
      </Card>
    );
  }

  // 渲染任务不存在状态
  if (!task) {
    return (
      <Card>
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description="任务不存在或已被删除"
        />
        <div style={{ textAlign: 'center', marginTop: 16 }}>
          <Button type="primary" onClick={handleBack}>
            返回任务列表
          </Button>
        </div>
      </Card>
    );
  }

  // 渲染任务详情
  return (
    <div className="task-detail-page">
      <Card>
        <div style={{ marginBottom: 16 }}>
          <Space>
            <Button icon={<ArrowLeftOutlined />} onClick={handleBack}>返回</Button>
            <Button type="primary" icon={<EditOutlined />} onClick={handleEdit}>编辑</Button>
          </Space>
        </div>

        <Title level={4}>{task.title}</Title>

        <Row gutter={[16, 16]}>
          <Col xs={24} md={16}>
            <Card title="任务详情" bordered={false}>
              <Descriptions column={1} bordered>
                <Descriptions.Item label="任务状态">
                  <Tag color={statusColors[task.status] || 'default'}>
                    {statusTexts[task.status] || task.status}
                  </Tag>
                </Descriptions.Item>
                <Descriptions.Item label="任务类别">
                  <Tag icon={<TagOutlined />}>
                    {categoryTexts[task.category] || task.category}
                  </Tag>
                </Descriptions.Item>
                <Descriptions.Item label="优先级">
                  <Tag color={priorityColors[task.priority] || 'default'}>
                    {priorityTexts[task.priority] || task.priority}
                  </Tag>
                </Descriptions.Item>
                <Descriptions.Item label="开始日期">
                  {task.start_date ? dayjs(task.start_date).format('YYYY-MM-DD') : '未设置'}
                </Descriptions.Item>
                <Descriptions.Item label="截止日期">
                  {task.due_date ? dayjs(task.due_date).format('YYYY-MM-DD') : '未设置'}
                </Descriptions.Item>
                <Descriptions.Item label="完成日期">
                  {task.completed_date ? dayjs(task.completed_date).format('YYYY-MM-DD HH:mm') : '未完成'}
                </Descriptions.Item>
                <Descriptions.Item label="创建时间">
                  {dayjs(task.created_at).format('YYYY-MM-DD HH:mm')}
                </Descriptions.Item>
                <Descriptions.Item label="更新时间">
                  {dayjs(task.updated_at).format('YYYY-MM-DD HH:mm')}
                </Descriptions.Item>
                <Descriptions.Item label="任务描述">
                  <Paragraph>{task.description || '无描述'}</Paragraph>
                </Descriptions.Item>
              </Descriptions>
            </Card>

            {task.attachments && task.attachments.length > 0 && (
              <Card title="附件" style={{ marginTop: 16 }} bordered={false}>
                <List
                  itemLayout="horizontal"
                  dataSource={task.attachments}
                  renderItem={(item, index) => (
                    <List.Item>
                      <List.Item.Meta
                        avatar={<Avatar icon={<FileOutlined />} />}
                        title={<a href={item} target="_blank" rel="noopener noreferrer">{`附件 ${index + 1}`}</a>}
                        description={item.split('/').pop()}
                      />
                    </List.Item>
                  )}
                />
              </Card>
            )}
          </Col>

          <Col xs={24} md={8}>
            <Card title="门店信息" bordered={false}>
              {task.is_all_stores ? (
                <Tag icon={<ShopOutlined />}>所有门店</Tag>
              ) : (
                <List
                  dataSource={task.store_names || []}
                  renderItem={store => (
                    <List.Item>
                      <Tag icon={<ShopOutlined />}>{store}</Tag>
                    </List.Item>
                  )}
                  locale={{ emptyText: '未分配门店' }}
                />
              )}
            </Card>

            <Card title="负责人" style={{ marginTop: 16 }} bordered={false}>
              <List
                dataSource={task.assignee_names || []}
                renderItem={assignee => (
                  <List.Item>
                    <List.Item.Meta
                      avatar={<Avatar icon={<UserOutlined />} />}
                      title={assignee}
                    />
                  </List.Item>
                )}
                locale={{ emptyText: '未分配负责人' }}
              />
            </Card>

            {task.tags && task.tags.length > 0 && (
              <Card title="标签" style={{ marginTop: 16 }} bordered={false}>
                <Space wrap>
                  {task.tags.map((tag, index) => (
                    <Tag key={index} color="blue">{tag}</Tag>
                  ))}
                </Space>
              </Card>
            )}
          </Col>
        </Row>

        <Card title="评论" style={{ marginTop: 16 }} bordered={false} loading={loadingComments}>
          {comments.length > 0 ? (
            <List
              dataSource={comments}
              itemLayout="horizontal"
              renderItem={comment => (
                <List.Item>
                  <List.Item.Meta
                    avatar={<Avatar icon={<UserOutlined />} />}
                    title={
                      <div>
                        <span style={{ fontWeight: 'bold' }}>{comment.creator_name}</span>
                        <span style={{ marginLeft: 8, fontSize: '12px', color: '#999' }}>
                          {dayjs(comment.created_at).format('YYYY-MM-DD HH:mm')}
                        </span>
                      </div>
                    }
                    description={comment.content}
                  />
                </List.Item>
              )}
            />
          ) : (
            <Empty description="暂无评论" />
          )}
        </Card>
      </Card>
    </div>
  );
};

export default RoutineTaskDetail;
