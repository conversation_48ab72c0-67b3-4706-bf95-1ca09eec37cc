#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
AI 模块 Schema
"""

from .provider import (
    AIProviderBase,
    AIProviderCreate,
    AIProviderUpdate,
    AIProviderInDB,
    AIProviderResponse,
    AIProviderListResponse,
)
from .model import (
    AIModelBase,
    AIModelCreate,
    AIModelUpdate,
    AIModelInDB,
    AIModelResponse,
    AIModelListResponse,
)
from .config import (
    AIConfigBase,
    AIConfigCreate,
    AIConfigUpdate,
    AIConfigInDB,
    AIConfigResponse,
    AIConfigListResponse,
)
from .usage import (
    AIUsageBase,
    AIUsageCreate,
    AIUsageInDB,
    AIUsageResponse,
    AIUsageListResponse,
    AIUsageStatistics,
    AIUsageStatisticsResponse,
    AIUsageSummaryResponse,
)
from .chat import (
    AIChatMessage,
    AIFunction,
    AIChatRequest,
    AIChatResponseChoice,
    AIChatResponseUsage,
    AIChatResponse,
    AIChatStreamResponse,
    AIChatErrorResponse,
)
from .embedding import (
    AIEmbeddingRequest,
    AIEmbeddingData,
    AIEmbeddingUsage,
    AIEmbeddingResponse,
    AIEmbeddingErrorResponse,
)
from .vision import (
    AIVisionRequest,
    AIVisionMessageContent,
    AIVisionMessage,
    AIVisionResponseChoice,
    AIVisionResponseUsage,
    AIVisionResponse,
    AIVisionErrorResponse,
)
from .audio import (
    AITranscriptionRequest,
    AITranscriptionResponse,
    AITextToSpeechRequest,
    AITextToSpeechResponse,
    AIAudioErrorResponse,
)
from .knowledge import (
    AIKnowledgeBaseBase,
    AIKnowledgeBaseCreate,
    AIKnowledgeBaseUpdate,
    AIKnowledgeBaseInDB,
    AIKnowledgeBaseResponse,
    AIKnowledgeBaseListResponse,
    AIKnowledgeDocumentBase,
    AIKnowledgeDocumentCreate,
    AIKnowledgeDocumentUpdate,
    AIKnowledgeDocumentInDB,
    AIKnowledgeDocumentResponse,
    AIKnowledgeDocumentListResponse,
    AIKnowledgeChunkBase,
    AIKnowledgeChunkCreate,
    AIKnowledgeChunkInDB,
    AIKnowledgeChunkResponse,
    AIKnowledgeChunkListResponse,
    AIKnowledgeQueryRequest,
    AIKnowledgeQueryResult,
    AIKnowledgeQueryResponse,
)
from .assistant import (
    AIAssistantToolBase,
    AIAssistantToolCreate,
    AIAssistantToolUpdate,
    AIAssistantToolInDB,
    AIAssistantToolResponse,
    AIAssistantToolListResponse,
    AIAssistantBase,
    AIAssistantCreate,
    AIAssistantUpdate,
    AIAssistantInDB,
    AIAssistantResponse,
    AIAssistantListResponse,
    AIAssistantThreadBase,
    AIAssistantThreadCreate,
    AIAssistantThreadUpdate,
    AIAssistantThreadInDB,
    AIAssistantThreadResponse,
    AIAssistantThreadListResponse,
    AIAssistantMessageContent,
    AIAssistantMessageBase,
    AIAssistantMessageCreate,
    AIAssistantMessageInDB,
    AIAssistantMessageResponse,
    AIAssistantMessageListResponse,
    AIAssistantChatRequest,
    AIAssistantChatResponse,
)
from .settings import (
    AISettingsBase,
    AISettingsUpdate,
    AISettingsResponse,
)

__all__ = [
    'AIProviderBase',
    'AIProviderCreate',
    'AIProviderUpdate',
    'AIProviderInDB',
    'AIProviderResponse',
    'AIProviderListResponse',
    'AIModelBase',
    'AIModelCreate',
    'AIModelUpdate',
    'AIModelInDB',
    'AIModelResponse',
    'AIModelListResponse',
    'AIConfigBase',
    'AIConfigCreate',
    'AIConfigUpdate',
    'AIConfigInDB',
    'AIConfigResponse',
    'AIConfigListResponse',
    'AIUsageBase',
    'AIUsageCreate',
    'AIUsageInDB',
    'AIUsageResponse',
    'AIUsageListResponse',
    'AIUsageStatistics',
    'AIUsageStatisticsResponse',
    'AIChatMessage',
    'AIFunction',
    'AIChatRequest',
    'AIChatResponseChoice',
    'AIChatResponseUsage',
    'AIChatResponse',
    'AIChatStreamResponse',
    'AIChatErrorResponse',
    'AIEmbeddingRequest',
    'AIEmbeddingData',
    'AIEmbeddingUsage',
    'AIEmbeddingResponse',
    'AIEmbeddingErrorResponse',
    'AIVisionRequest',
    'AIVisionMessageContent',
    'AIVisionMessage',
    'AIVisionResponseChoice',
    'AIVisionResponseUsage',
    'AIVisionResponse',
    'AIVisionErrorResponse',
    'AITranscriptionRequest',
    'AITranscriptionResponse',
    'AITextToSpeechRequest',
    'AITextToSpeechResponse',
    'AIAudioErrorResponse',
    'AIKnowledgeBaseBase',
    'AIKnowledgeBaseCreate',
    'AIKnowledgeBaseUpdate',
    'AIKnowledgeBaseInDB',
    'AIKnowledgeBaseResponse',
    'AIKnowledgeBaseListResponse',
    'AIKnowledgeDocumentBase',
    'AIKnowledgeDocumentCreate',
    'AIKnowledgeDocumentUpdate',
    'AIKnowledgeDocumentInDB',
    'AIKnowledgeDocumentResponse',
    'AIKnowledgeDocumentListResponse',
    'AIKnowledgeChunkBase',
    'AIKnowledgeChunkCreate',
    'AIKnowledgeChunkInDB',
    'AIKnowledgeChunkResponse',
    'AIKnowledgeChunkListResponse',
    'AIKnowledgeQueryRequest',
    'AIKnowledgeQueryResult',
    'AIKnowledgeQueryResponse',
    'AIAssistantToolBase',
    'AIAssistantToolCreate',
    'AIAssistantToolUpdate',
    'AIAssistantToolInDB',
    'AIAssistantToolResponse',
    'AIAssistantToolListResponse',
    'AIAssistantBase',
    'AIAssistantCreate',
    'AIAssistantUpdate',
    'AIAssistantInDB',
    'AIAssistantResponse',
    'AIAssistantListResponse',
    'AIAssistantThreadBase',
    'AIAssistantThreadCreate',
    'AIAssistantThreadUpdate',
    'AIAssistantThreadInDB',
    'AIAssistantThreadResponse',
    'AIAssistantThreadListResponse',
    'AIAssistantMessageContent',
    'AIAssistantMessageBase',
    'AIAssistantMessageCreate',
    'AIAssistantMessageInDB',
    'AIAssistantMessageResponse',
    'AIAssistantMessageListResponse',
    'AIAssistantChatRequest',
    'AIAssistantChatResponse',
    'AISettingsBase',
    'AISettingsUpdate',
    'AISettingsResponse',
]
