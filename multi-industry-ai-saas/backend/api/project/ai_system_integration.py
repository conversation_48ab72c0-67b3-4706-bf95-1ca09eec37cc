#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
项目级系统 AI 整合设置 API (for /api/project/)
"""

import logging
import uuid
from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from db.database import get_db
from models.project import Project
from models.user import User
from core.auth import get_current_user
from api.deps import get_current_project, get_current_tenant_id
from services.ai.system_integration_service import SystemAIIntegrationService
from schemas.ai.system_integration import (
    SystemAIIntegrationCreate,
    SystemAIIntegrationUpdate,
    SystemAIIntegrationResponse
)

logger = logging.getLogger(__name__)
router = APIRouter()

@router.get("/ai/system-integration")
async def get_system_ai_integration(
    project: Project = Depends(get_current_project),
    tenant_id: Optional[uuid.UUID] = Depends(get_current_tenant_id),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    获取系统AI整合配置
    
    按优先级返回: 项目级 > 租户级 > 全局级配置
    如果没有任何配置，返回默认配置模板
    """
    try:
        config = await SystemAIIntegrationService.get_integration_config(
            db=db,
            project_id=project.id,
            tenant_id=tenant_id
        )
        
        if config:
            return {
                "success": True,
                "message": "获取系统AI整合配置成功",
                "data": config
            }
        else:
            # 返回默认配置结构
            return {
                "success": True,
                "message": "使用默认配置",
                "data": {
                    "id": None,
                    "project_id": project.id,
                    "tenant_id": tenant_id,
                    "default_chat_config_id": None,
                    "default_vision_config_id": None,
                    "default_audio_config_id": None,
                    "default_embedding_config_id": None,
                    "default_chat_model_id": None,
                    "default_vision_model_id": None,
                    "default_audio_model_id": None,
                    "default_embedding_model_id": None,
                    "chat_temperature": 0.7,
                    "chat_max_tokens": None,
                    "vision_temperature": 0.7,
                    "vision_max_tokens": None,
                    "chat_enabled": True,
                    "vision_enabled": True,
                    "audio_enabled": True,
                    "embedding_enabled": True,
                    "enable_fallback": True,
                    "request_timeout": 30,
                    "max_retries": 3,
                    "created_at": None,
                    "updated_at": None
                }
            }
        
    except Exception as e:
        logger.error(f"获取系统AI整合配置失败: {e}")
        # 即使出错也返回默认配置，避免前端报错
        return {
            "success": True,
            "message": "使用默认配置（获取配置时出错）",
            "data": {
                "id": None,
                "project_id": project.id,
                "tenant_id": tenant_id,
                "default_chat_config_id": None,
                "default_vision_config_id": None,
                "default_audio_config_id": None,
                "default_embedding_config_id": None,
                "default_chat_model_id": None,
                "default_vision_model_id": None,
                "default_audio_model_id": None,
                "default_embedding_model_id": None,
                "chat_temperature": 0.7,
                "chat_max_tokens": None,
                "vision_temperature": 0.7,
                "vision_max_tokens": None,
                "chat_enabled": True,
                "vision_enabled": True,
                "audio_enabled": True,
                "embedding_enabled": True,
                "enable_fallback": True,
                "request_timeout": 30,
                "max_retries": 3,
                "created_at": None,
                "updated_at": None
            }
        }

@router.post("/ai/system-integration")
async def create_system_ai_integration(
    config_data: SystemAIIntegrationCreate,
    project: Project = Depends(get_current_project),
    tenant_id: Optional[uuid.UUID] = Depends(get_current_tenant_id),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    创建项目级系统AI整合配置
    """
    try:
        config = await SystemAIIntegrationService.create_integration_config(
            db=db,
            config_data=config_data,
            project_id=project.id,
            tenant_id=tenant_id
        )
        
        return {
            "success": True,
            "message": "创建系统AI整合配置成功",
            "data": config
        }
        
    except ValueError as e:
        logger.error(f"创建系统AI整合配置失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )
    except Exception as e:
        logger.error(f"创建系统AI整合配置失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建系统AI整合配置失败: {str(e)}",
        )

@router.put("/ai/system-integration/{config_id}")
async def update_system_ai_integration(
    config_id: uuid.UUID,
    config_data: SystemAIIntegrationUpdate,
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    更新系统AI整合配置
    """
    try:
        config = await SystemAIIntegrationService.update_integration_config(
            db=db,
            config_id=config_id,
            config_data=config_data
        )
        
        if not config:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"系统AI整合配置 {config_id} 不存在",
            )
        
        return {
            "success": True,
            "message": "更新系统AI整合配置成功",
            "data": config
        }
        
    except HTTPException:
        raise
    except ValueError as e:
        logger.error(f"更新系统AI整合配置失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )
    except Exception as e:
        logger.error(f"更新系统AI整合配置失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新系统AI整合配置失败: {str(e)}",
        ) 