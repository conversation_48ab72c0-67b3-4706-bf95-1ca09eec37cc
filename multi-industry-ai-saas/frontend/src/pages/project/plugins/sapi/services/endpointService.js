import api from '../../../../../services/api/httpClient';
import { getProjectId } from '../../../../../services/api/httpClient';

/**
 * 获取API端点列表
 *
 * @param {Object} params 查询参数
 * @returns {Promise<Object>} 端点列表
 */
export const getEndpointList = async (params = {}) => {
  try {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    // 确保参数名称与后端一致
    const queryParams = {
      page: params.page || 1,
      page_size: params.page_size || params.pageSize || 10
    };

    // 只有当搜索关键词不为空时才添加搜索参数
    if (params.search) {
      queryParams.search = params.search;
    }

    console.log('获取API端点列表参数:', queryParams);
    const response = await api.get(`/project/${projectId}/plugin/sapi/endpoints`, { params: queryParams });
    return response;
  } catch (error) {
    console.error('获取API端点列表失败:', error);
    throw error;
  }
};

/**
 * 创建API端点
 *
 * @param {Object} data 端点数据
 * @returns {Promise<Object>} 创建结果
 */
export const createEndpoint = async (data) => {
  try {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    const response = await api.post(`/project/${projectId}/plugin/sapi/endpoints`, data);
    return response;
  } catch (error) {
    console.error('创建API端点失败:', error);
    throw error;
  }
};

/**
 * 更新API端点
 *
 * @param {string} endpointId 端点ID
 * @param {Object} data 更新数据
 * @returns {Promise<Object>} 更新结果
 */
export const updateEndpoint = async (endpointId, data) => {
  try {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    const response = await api.put(`/project/${projectId}/plugin/sapi/endpoints/${endpointId}`, data);
    return response;
  } catch (error) {
    console.error('更新API端点失败:', error);
    throw error;
  }
};

/**
 * 删除API端点
 *
 * @param {string} endpointId 端点ID
 * @returns {Promise<Object>} 删除结果
 */
export const deleteEndpoint = async (endpointId) => {
  try {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    const response = await api.delete(`/project/${projectId}/plugin/sapi/endpoints/${endpointId}`);
    return response;
  } catch (error) {
    console.error('删除API端点失败:', error);
    throw error;
  }
};

/**
 * 同步系统API端点
 *
 * @returns {Promise<Object>} 同步结果
 */
export const syncEndpoints = async () => {
  try {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    const response = await api.post(`/project/${projectId}/plugin/sapi/endpoints/sync`);
    return response;
  } catch (error) {
    console.error('同步系统API端点失败:', error);
    throw error;
  }
};

/**
 * 获取API端点详情
 *
 * @param {string} endpointId 端点ID
 * @returns {Promise<Object>} 端点详情
 */
export const getEndpointDetail = async (endpointId) => {
  try {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    const response = await api.get(`/project/${projectId}/plugin/sapi/endpoints/${endpointId}`);
    return response;
  } catch (error) {
    console.error('获取API端点详情失败:', error);
    throw error;
  }
};

/**
 * 测试API端点
 *
 * @param {string} endpointId 端点ID
 * @param {Object} data 测试数据
 * @returns {Promise<Object>} 测试结果
 */
export const testEndpoint = async (endpointId, data) => {
  try {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    const response = await api.post(`/project/${projectId}/plugin/sapi/endpoints/${endpointId}/test`, data);
    return response;
  } catch (error) {
    console.error('测试API端点失败:', error);
    throw error;
  }
};
