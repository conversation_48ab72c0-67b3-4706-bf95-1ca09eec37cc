import api from '../httpClient';
import request from '../../../services/request';

/**
 * AI 助手相关 API
 */
const aiAssistants = {
  list: (projectId, params) => request.get(`/project/${projectId}/ai/assistants`, { params }),
  get: (projectId, assistantId) => request.get(`/project/${projectId}/ai/assistants/${assistantId}`),
  create: (projectId, data) => request.post(`/project/${projectId}/ai/assistants`, data),
  update: (projectId, assistantId, data) => request.put(`/project/${projectId}/ai/assistants/${assistantId}`, data),
  delete: (projectId, assistantId) => request.delete(`/project/${projectId}/ai/assistants/${assistantId}`),
  chat: (projectId, assistantId, data) => api.post(`/project/${projectId}/ai/assistant/chat`, data),
  speechToText: (projectId, assistantId, audioFile) => {
    const formData = new FormData();
    formData.append('audio', audioFile);
    formData.append('assistant_id', assistantId);
    return api.post(`/project/${projectId}/ai/assistant/speech-to-text`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
  }
};

/**
 * AI 模型相关 API
 */
const aiModels = {
  list: (projectId, params) => request.get(`/project/${projectId}/ai/models`, { params }),
  get: (projectId, modelId) => request.get(`/project/${projectId}/ai/models/${modelId}`),
  create: (projectId, data) => request.post(`/project/${projectId}/ai/models`, data),
  update: (projectId, modelId, data) => request.put(`/project/${projectId}/ai/models/${modelId}`, data),
  delete: (projectId, modelId) => request.delete(`/project/${projectId}/ai/models/${modelId}`),
  sync: (projectId) => request.post(`/project/${projectId}/ai/models/sync`),
  test: (projectId, modelId, data) => api.post(`/project/${projectId}/ai/models/${modelId}/test`, data)
};

/**
 * AI 配置相关 API
 */
const aiConfigs = {
  list: (projectId, params) => request.get(`/project/${projectId}/ai/configs`, { params }),
  get: (projectId, configId) => request.get(`/project/${projectId}/ai/configs/${configId}`),
  create: (projectId, data) => request.post(`/project/${projectId}/ai/configs`, data),
  update: (projectId, configId, data) => request.put(`/project/${projectId}/ai/configs/${configId}`, data),
  delete: (projectId, configId) => request.delete(`/project/${projectId}/ai/configs/${configId}`),
  test: (projectId, configId) => request.post(`/project/${projectId}/ai/configs/${configId}/test`),
  setDefault: (projectId, configId) => request.put(`/project/${projectId}/ai/configs/${configId}/default`)
};

/**
 * AI 提供商相关 API
 */
const aiProviders = {
  list: (projectId, params) => request.get(`/project/${projectId}/ai/providers`, { params }),
  get: (projectId, providerId) => request.get(`/project/${projectId}/ai/providers/${providerId}`),
  create: (projectId, data) => request.post(`/project/${projectId}/ai/providers`, data),
  update: (projectId, providerId, data) => request.put(`/project/${projectId}/ai/providers/${providerId}`, data),
  delete: (projectId, providerId) => request.delete(`/project/${projectId}/ai/providers/${providerId}`),
  test: (projectId, providerId, data) => api.post(`/project/${projectId}/ai/providers/${providerId}/test`, data)
};

/**
 * 系统AI整合设置相关 API
 */
const systemIntegration = {
  // 获取系统AI整合配置
  get: (projectId) => request.get(`/project/${projectId}/ai/system-integration`),
  
  // 创建系统AI整合配置
  create: (projectId, data) => request.post(`/project/${projectId}/ai/system-integration`, data),
  
  // 更新系统AI整合配置
  update: (projectId, configId, data) => request.put(`/project/${projectId}/ai/system-integration/${configId}`, data),
  
  // 获取有效的默认模型
  getEffectiveModel: (projectId, modelType) => request.get(`/project/${projectId}/ai/effective-model/${modelType}`)
};

/**
 * 通用表格处理相关 API
 */
const tableProcessing = {
  // 处理表格
  process: (projectId, data) => request.post(`/project/${projectId}/table-processing/process`, data),
  
  // 预览表格处理结果
  preview: (projectId, data) => request.post(`/project/${projectId}/table-processing/preview`, data),
  
  // 获取可用模板
  getTemplates: (projectId) => request.get(`/project/${projectId}/table-processing/templates`),
  
  // 注册自定义模板
  registerTemplate: (projectId, template) => request.post(`/project/${projectId}/table-processing/templates`, template)
};

/**
 * MCP 工具相关 API
 */
const mcpTools = {
  list: (projectId, params) => request.get(`/project/${projectId}/mcp-tools`, { params }),
  get: (projectId, toolId) => request.get(`/project/${projectId}/mcp-tools/${toolId}`),
  create: (projectId, data) => request.post(`/project/${projectId}/mcp-tools`, data),
  update: (projectId, toolId, data) => request.put(`/project/${projectId}/mcp-tools/${toolId}`, data),
  delete: (projectId, toolId) => request.delete(`/project/${projectId}/mcp-tools/${toolId}`),
  execute: (projectId, toolId, data) => request.post(`/project/${projectId}/mcp-tools/${toolId}/execute`, data),
  testConnection: (projectId, data) => request.post(`/project/${projectId}/mcp-tools/test-connection`, data),
  getUsageStats: (projectId, params) => request.get(`/project/${projectId}/mcp-tools/usages`, { params })
};

/**
 * MCP 服务器相关 API
 */
const mcpServers = {
  list: (projectId, params) => request.get(`/project/${projectId}/ai/mcp-servers`, { params }),
  get: (projectId, serverId) => request.get(`/project/${projectId}/ai/mcp-servers/${serverId}`),
  create: (projectId, data) => request.post(`/project/${projectId}/ai/mcp-servers`, data),
  update: (projectId, serverId, data) => request.put(`/project/${projectId}/ai/mcp-servers/${serverId}`, data),
  delete: (projectId, serverId) => request.delete(`/project/${projectId}/ai/mcp-servers/${serverId}`),
  test: (projectId, serverId) => request.post(`/project/${projectId}/ai/mcp-servers/${serverId}/test`),
  initialize: (projectId, serverId) => request.post(`/project/${projectId}/ai/mcp-servers/${serverId}/initialize`),
  getTools: (projectId, serverId) => request.get(`/project/${projectId}/ai/mcp-servers/${serverId}/tools`)
};

/**
 * AI 使用统计相关 API
 */
const aiUsage = {
  getStats: (projectId, params) => request.get(`/project/${projectId}/ai/usage/stats`, { params }),
  getHistory: (projectId, params) => request.get(`/project/${projectId}/ai/usage`, { params }),
  getCosts: (projectId, params) => request.get(`/project/${projectId}/ai/usage/stats`, { params }),
  getTokens: (projectId, params) => request.get(`/project/${projectId}/ai/usage/stats`, { params }),
  getConfigStats: (projectId, configId, params) => request.get(`/project/${projectId}/ai/usage/configs/${configId}/stats`, { params })
};

/**
 * AI 设置相关 API
 */
const aiSettings = {
  get: (projectId) => request.get(`/project/${projectId}/ai/settings`),
  update: (projectId, data) => request.put(`/project/${projectId}/ai/settings`, data),
  reset: (projectId) => request.post(`/project/${projectId}/ai/settings/reset`)
};

/**
 * AI 相关 API（兼容旧版本）
 */
const ai = {
  // 助手管理（设置 API - 使用 request，路径为 /api/project/...）
  getAssistants: (projectId, params) => request.get(`/project/${projectId}/ai/assistants`, { params }),
  getAssistant: (projectId, assistantId) => request.get(`/project/${projectId}/ai/assistants/${assistantId}`),
  createAssistant: (projectId, data) => request.post(`/project/${projectId}/ai/assistants`, data),
  updateAssistant: (projectId, assistantId, data) => request.put(`/project/${projectId}/ai/assistants/${assistantId}`, data),
  deleteAssistant: (projectId, assistantId) => request.delete(`/project/${projectId}/ai/assistants/${assistantId}`),

  // 助手工具管理（设置 API - 使用 request，路径为 /api/project/...）
  getTools: (projectId, params) => request.get(`/project/${projectId}/ai/tools`, { params }),
  getTool: (projectId, toolId) => request.get(`/project/${projectId}/ai/tools/${toolId}`),
  createTool: (projectId, data) => request.post(`/project/${projectId}/ai/tools`, data),
  updateTool: (projectId, toolId, data) => request.put(`/project/${projectId}/ai/tools/${toolId}`, data),
  deleteTool: (projectId, toolId) => request.delete(`/project/${projectId}/ai/tools/${toolId}`),
  createBuiltinTools: (projectId) => request.post(`/project/${projectId}/ai/tools/builtin`),

  // 助手对话（业务 API - 使用 api，路径为 /api/v1/project/...）
  getThreads: (projectId, params) => api.get(`/project/${projectId}/ai/assistant/threads`, { params }),
  getThread: (projectId, threadId) => api.get(`/project/${projectId}/ai/assistant/threads/${threadId}`),
  createThread: (projectId, data) => api.post(`/project/${projectId}/ai/assistant/threads`, data),
  updateThread: (projectId, threadId, data) => api.put(`/project/${projectId}/ai/assistant/threads/${threadId}`, data),
  deleteThread: (projectId, threadId) => api.delete(`/project/${projectId}/ai/assistant/threads/${threadId}`),
  getMessages: (projectId, threadId, params) => api.get(`/project/${projectId}/ai/assistant/threads/${threadId}/messages`, { params }),
  chatWithAssistant: (projectId, data) => api.post(`/project/${projectId}/ai/assistant/chat`, data),

  // 使用统计
  getUsageStats: (projectId, params) => aiUsage.getStats(projectId, params),

  // 新版本API结构
  aiAssistants,
  aiModels,
  aiProviders,
  aiConfigs,
  mcpTools,
  mcpServers,
  aiUsage,
  aiSettings,
  systemIntegration,
  tableProcessing,
  
  // 向后兼容的别名
  getSystemIntegration: (projectId) => systemIntegration.get(projectId),
  createSystemIntegration: (projectId, data) => systemIntegration.create(projectId, data),
  updateSystemIntegration: (projectId, configId, data) => systemIntegration.update(projectId, configId, data),
  getEffectiveModel: (projectId, modelType) => systemIntegration.getEffectiveModel(projectId, modelType)
};

export default ai;
