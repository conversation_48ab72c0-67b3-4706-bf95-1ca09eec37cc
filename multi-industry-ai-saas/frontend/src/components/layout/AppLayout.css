/* 布局样式 */
.ant-layout {
  transition: all 0.3s;
}

.ant-layout-sider {
  box-shadow: 2px 0 8px 0 rgba(29, 35, 41, 0.05);
  z-index: 10;
}

/* Logo样式 */
.logo {
  height: 64px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 20px;
  font-weight: bold;
  color: #1a365d;
  border-bottom: 1px solid #f0f0f0;
  overflow: hidden;
  white-space: nowrap;
  transition: all 0.3s;
}

/* 头部样式 */
.app-header {
  padding: 0 16px;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  z-index: 9;
  position: sticky;
  top: 0;
}

.trigger-button {
  font-size: 18px;
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s;
}

.trigger-button:hover {
  background: #f0f0f0;
  color: #1a365d;
}

.header-right {
  display: flex;
  align-items: center;
}

.user-dropdown {
  cursor: pointer;
  padding: 0 8px;
  border-radius: 4px;
  transition: all 0.3s;
}

.user-dropdown:hover {
  background: #f0f0f0;
}

/* 内容区域样式 */
.app-content {
  margin: 24px 16px;
  padding: 24px;
  background: #fff;
  min-height: 280px;
  overflow: auto;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

/* 底部样式 */
.app-footer {
  text-align: center;
  color: rgba(0, 0, 0, 0.45);
  font-size: 14px;
  padding: 16px 50px;
  background: transparent;
}

/* 菜单样式 */
.ant-menu-light {
  border-right: none !important;
}

.ant-menu-item {
  border-radius: 4px;
  margin: 4px 8px !important;
}

.ant-menu-item-selected {
  background-color: #e6f7ff !important;
  color: #1a365d !important;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .app-content {
    margin: 16px 8px;
    padding: 16px;
  }

  .app-footer {
    padding: 12px 20px;
  }
}

/* 动画效果 */
.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.ant-card {
  transition: all 0.3s;
}

.ant-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}
