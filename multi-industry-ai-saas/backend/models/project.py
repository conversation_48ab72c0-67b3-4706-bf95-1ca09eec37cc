from sqlalchemy import Column, <PERSON>, <PERSON>olean, DateTime, ForeignKey, JSON, Text, Integer, UniqueConstraint
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid

from db.database import Base

class Project(Base):
    """项目模型"""
    __tablename__ = "projects"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=False)
    name = Column(String(100), nullable=False)
    code = Column(String(50), nullable=False)
    description = Column(Text, nullable=True)
    industry_type = Column(String(50), nullable=False, default="retail")  # retail, restaurant, hotel, etc.
    logo_url = Column(String(255), nullable=True)
    status = Column(String(20), default="active", nullable=False)  # active, inactive, suspended
    settings = Column(JSON, default=dict)  # 项目设置
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    max_users = Column(Integer, default=10)
    max_storage_gb = Column(Integer, default=500)
    plugins = Column(JSON, default=list)

    # 关联关系
    tenant = relationship("Tenant", back_populates="projects")
    project_users = relationship("ProjectUser", back_populates="project", cascade="all, delete-orphan")
    system_configs = relationship("SystemConfig", back_populates="project", cascade="all, delete-orphan")
    product_brands = relationship("ProductBrand", back_populates="project")
    product_categories = relationship("ProductCategory", back_populates="project")
    products = relationship("Product", back_populates="project")
    inventory_items = relationship("InventoryItem", back_populates="project")
    suppliers = relationship("Supplier", back_populates="project")
    supplier_products = relationship("SupplierProduct", back_populates="project")
    supplier_evaluations = relationship("SupplierEvaluation", back_populates="project")
    purchase_orders = relationship("PurchaseOrder", back_populates="project")
    purchase_order_items = relationship("PurchaseOrderItem", back_populates="project")
    sales_reports = relationship("SalesReport", back_populates="project")
    sales_channels = relationship("SalesChannel", back_populates="project", cascade="all, delete-orphan")
    payment_methods = relationship("PaymentMethod", back_populates="project", cascade="all, delete-orphan")
    financial_reconciliations = relationship("FinancialReconciliation", back_populates="project", cascade="all, delete-orphan")
    reconciliation_uploads = relationship("ReconciliationUpload", back_populates="project", cascade="all, delete-orphan")
    routine_tasks = relationship("RoutineTask", back_populates="project", cascade="all, delete-orphan", foreign_keys="[RoutineTask.project_id]")
    # 知识库相关关系在 KnowledgeCategory 和 KnowledgeDocument 模型中定义
    # 使用延迟加载避免循环导入

    # 营销活动关系
    marketing_activities = relationship("MarketingActivity", back_populates="project", cascade="all, delete-orphan")

    # AI 相关关系
    ai_configs = relationship("AIConfig", back_populates="project", cascade="all, delete-orphan")
    ai_assistants = relationship("AIAssistant", back_populates="project", cascade="all, delete-orphan")
    ai_tools = relationship("AIAssistantTool", back_populates="project", cascade="all, delete-orphan")
    ai_knowledge_bases = relationship("AIKnowledgeBase", back_populates="project", cascade="all, delete-orphan")
    ai_usages = relationship("AIUsage", back_populates="project", cascade="all, delete-orphan")
    
    # 异步任务关系
    async_tasks = relationship("AsyncTask", back_populates="project", cascade="all, delete-orphan")

    __table_args__ = (
        UniqueConstraint('tenant_id', 'code', name='uq_project_code_per_tenant'),
        {'extend_existing': True}
    )

    def __repr__(self):
        return f"<Project(id={self.id}, name='{self.name}', code='{self.code}')>"
