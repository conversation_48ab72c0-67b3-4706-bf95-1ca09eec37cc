#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
MCP令牌路由

提供MCP服务器的令牌管理功能，包括获取MCP令牌、验证MCP令牌等
符合MCP最新草案及协议
"""

import logging
import uuid
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Union

from fastapi import APIRouter, Depends, HTTPException, status, Request, Body, Query, Path
from sqlalchemy import and_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from pydantic import BaseModel, Field

from db.database import get_db
from models.user import User
from core.auth import get_current_user
from core.config import settings

from ..models import SAPIToken, MCPServer
from ..auth import (
    create_jwt_token, verify_jwt_token, TokenType, TokenPermission,
    JWT_MCP_TOKEN_EXPIRE_HOURS
)
from .. import router

# 配置日志
logger = logging.getLogger(__name__)

# MCP令牌请求模型
class MCPTokenRequest(BaseModel):
    """MCP令牌请求模型"""
    project_id: str = Field(..., description="项目ID")
    server_id: str = Field(..., description="服务器ID")
    permissions: Optional[List[str]] = Field(None, description="权限列表")
    expires_hours: Optional[int] = Field(None, description="过期时间（小时）")
    server_permissions: Optional[Dict[str, List[str]]] = Field(None, description="服务器权限映射")
    default_permissions: Optional[List[str]] = Field(None, description="默认权限列表")

# MCP令牌响应模型
class MCPTokenResponse(BaseModel):
    """MCP令牌响应模型"""
    access_token: str = Field(..., description="访问令牌")
    token_type: str = Field("Bearer", description="令牌类型")
    expires_in: int = Field(..., description="过期时间（秒）")
    scope: str = Field(..., description="作用域")
    project_id: str = Field(..., description="项目ID")

# 获取MCP令牌
@router.post("/mcp/token", response_model=MCPTokenResponse)
async def get_mcp_token(
    token_request: MCPTokenRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取MCP令牌

    用于MCP客户端获取访问MCP服务器的令牌
    """
    try:
        # 检查项目ID
        project_id = token_request.project_id

        # 检查服务器ID
        server_id = token_request.server_id

        # 查询服务器
        result = await db.execute(select(MCPServer).where(MCPServer.id == server_id))
        server = result.scalars().first()

        if not server:
            raise HTTPException(status_code=404, detail="服务器不存在")

        # 检查权限
        permissions = token_request.permissions or [TokenPermission.READ]
        for permission in permissions:
            if not TokenPermission.validate_permission(permission):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"无效的权限: {permission}"
                )

        # 检查服务器权限映射
        server_permissions = token_request.server_permissions or {}
        for srv_id, perms in server_permissions.items():
            for perm in perms:
                if not TokenPermission.validate_permission(perm):
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail=f"无效的权限: {perm}"
                    )

        # 检查默认权限
        default_permissions = token_request.default_permissions or [TokenPermission.READ]
        for permission in default_permissions:
            if not TokenPermission.validate_permission(permission):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"无效的权限: {permission}"
                )

        # 设置过期时间
        expires_hours = token_request.expires_hours or JWT_MCP_TOKEN_EXPIRE_HOURS

        # 如果没有指定服务器权限映射，则使用请求的权限
        if not server_permissions:
            server_permissions = {server_id: permissions}

        # 生成MCP令牌
        token_id = str(uuid.uuid4())
        access_token = create_jwt_token(
            subject=str(current_user.id),
            token_type=TokenType.MCP,
            expires_delta=timedelta(hours=expires_hours),
            scopes=["mcp"],
            project_id=project_id,
            server_permissions=server_permissions,
            default_permissions=default_permissions,
            user_id=str(current_user.id),
            jti=token_id
        )

        # 创建令牌记录 - 确保包含所有必要字段
        token_obj = SAPIToken(
            id=uuid.UUID(token_id),
            name=f"MCP令牌 - {server.name}",
            description=f"用于访问MCP服务器 {server.name} 的令牌",
            token=access_token,
            user_id=current_user.id,
            permissions=",".join(default_permissions),
            server_permissions=server_permissions,  # 保存服务器权限映射
            default_permissions=default_permissions,  # 保存默认权限列表
            project_id=uuid.UUID(project_id),  # 保存项目ID
            status="active",
            created_at=datetime.now(),
            expires_at=datetime.now() + timedelta(hours=expires_hours),
            token_type="mcp"
        )

        db.add(token_obj)
        await db.commit()

        # 返回令牌
        return {
            "access_token": access_token,
            "token_type": "Bearer",
            "expires_in": expires_hours * 3600,
            "scope": "mcp",
            "project_id": project_id
        }
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"获取MCP令牌失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取MCP令牌失败: {str(e)}")

# 验证MCP令牌
@router.post("/mcp/token/verify", response_model=Dict[str, Any])
async def verify_mcp_token(
    token: str = Body(..., embed=True),
    db: AsyncSession = Depends(get_db)
):
    """
    验证MCP令牌

    用于验证MCP令牌的有效性
    """
    try:
        # 验证令牌
        payload = verify_jwt_token(token)

        # 检查令牌类型
        if payload.get("type") != TokenType.MCP:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无效的MCP令牌类型"
            )

        # 检查必要的声明
        if "project_id" not in payload:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无效的MCP令牌，缺少项目ID"
            )

        # 更新令牌的最后使用时间
        try:
            token_id = payload.get("jti")
            if token_id:
                # 查询令牌
                result = await db.execute(select(SAPIToken).where(SAPIToken.id == token_id))
                token_obj = result.scalars().first()

                if token_obj:
                    # 更新最后使用时间
                    token_obj.last_used = datetime.now()
                    await db.commit()
        except Exception as e:
            logger.warning(f"更新令牌最后使用时间失败: {str(e)}")

        # 返回令牌信息
        return {
            "valid": True,
            "project_id": payload.get("project_id"),
            "user_id": payload.get("user_id"),
            "server_permissions": payload.get("server_permissions", {}),
            "default_permissions": payload.get("default_permissions", []),
            "expires_at": payload.get("exp")
        }
    except HTTPException as e:
        return {
            "valid": False,
            "error": e.detail
        }
    except Exception as e:
        logger.error(f"验证MCP令牌失败: {str(e)}")
        return {
            "valid": False,
            "error": str(e)
        }

# 撤销MCP令牌
@router.post("/mcp/token/revoke", response_model=Dict[str, Any])
async def revoke_mcp_token(
    token: str = Body(..., embed=True),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    撤销MCP令牌

    用于撤销MCP令牌
    """
    try:
        # 验证令牌
        try:
            payload = verify_jwt_token(token)

            # 检查令牌类型
            if payload.get("type") != TokenType.MCP:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="无效的MCP令牌类型"
                )

            # 获取令牌ID
            token_id = payload.get("jti")
            if not token_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="无效的MCP令牌，缺少令牌ID"
                )

            # 查询令牌
            result = await db.execute(
                select(SAPIToken).where(
                    and_(
                        SAPIToken.id == token_id,
                        SAPIToken.user_id == current_user.id
                    )
                )
            )
            token_obj = result.scalars().first()

            if not token_obj:
                raise HTTPException(status_code=404, detail="令牌不存在")

            # 撤销令牌
            token_obj.status = "revoked"
            await db.commit()

            return {
                "success": True,
                "message": "令牌已撤销"
            }
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"验证令牌失败: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"无效的令牌: {str(e)}"
            )
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"撤销MCP令牌失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"撤销MCP令牌失败: {str(e)}")
