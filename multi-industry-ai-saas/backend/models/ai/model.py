#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
AI 模型模型
"""

import uuid
from datetime import datetime
from sqlalchemy import Column, String, Integer, Float, Boolean, ForeignKey, DateTime, Text, func
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship

from db.database import Base

class AIModel(Base):
    """AI 模型模型"""
    __tablename__ = "ai_models"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    provider_id = Column(UUID(as_uuid=True), ForeignKey("ai_providers.id", ondelete="CASCADE"), nullable=False, comment="提供商 ID")
    name = Column(String(100), nullable=False, comment="模型名称，如 gpt-4, claude-3 等")
    display_name = Column(String(100), nullable=False, comment="显示名称")
    description = Column(Text, nullable=True, comment="描述")
    model_type = Column(String(20), nullable=False, comment="模型类型：chat, embedding, vision, audio, multimodal")
    capabilities = Column(JSONB, nullable=True, comment="模型能力配置")
    context_window = Column(Integer, nullable=True, comment="上下文窗口大小")
    token_limit = Column(Integer, nullable=True, comment="最大 token 数")
    
    # 基础定价
    input_price_per_1k_tokens = Column(Float, nullable=True, comment="每千 token 输入价格（美元）")
    output_price_per_1k_tokens = Column(Float, nullable=True, comment="每千 token 输出价格（美元）")
    
    # 多模态定价
    image_price_per_1k_tokens = Column(Float, nullable=True, comment="每千图像token价格（美元）")
    audio_price_per_minute = Column(Float, nullable=True, comment="每分钟音频价格（美元）")
    video_price_per_minute = Column(Float, nullable=True, comment="每分钟视频价格（美元）")
    
    # 性能参数
    average_response_time = Column(Float, nullable=True, comment="平均响应时间(秒)")
    max_concurrent_requests = Column(Integer, nullable=True, comment="最大并发请求数")
    rate_limit_per_minute = Column(Integer, nullable=True, comment="每分钟请求限制")
    
    status = Column(String(20), nullable=False, default="active", comment="状态：active, inactive, deprecated")
    is_builtin = Column(Boolean, nullable=False, default=False, comment="是否内置模型")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), onupdate=func.now(), comment="更新时间")

    # 关系
    provider = relationship("AIProvider", back_populates="models")
    configs = relationship("AIConfig", back_populates="model", cascade="all, delete-orphan")
    usages = relationship("AIUsage", back_populates="model", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<AIModel(id={self.id}, name='{self.name}', provider_id={self.provider_id})>"

    @property
    def supports_vision(self) -> bool:
        """是否支持视觉能力"""
        if not self.capabilities:
            return False
        return self.capabilities.get("supports_vision", False)

    @property
    def supports_audio(self) -> bool:
        """是否支持音频能力"""
        if not self.capabilities:
            return False
        return (
            self.capabilities.get("supports_audio_input", False) or
            self.capabilities.get("supports_audio_output", False) or
            self.capabilities.get("supports_speech_to_text", False) or
            self.capabilities.get("supports_text_to_speech", False)
        )

    @property
    def supports_multimodal(self) -> bool:
        """是否支持多模态"""
        return self.supports_vision or self.supports_audio

    @property
    def supports_function_calling(self) -> bool:
        """是否支持函数调用"""
        if not self.capabilities:
            return False
        return self.capabilities.get("supports_function_calling", False)

    @property
    def is_multimodal_model(self) -> bool:
        """是否为多模态模型"""
        return self.model_type in ["multimodal", "vision", "audio"] or self.supports_multimodal
