#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
周边调研模型
"""

import uuid
from datetime import datetime
from typing import List, Optional
from sqlalchemy import Column, String, Text, ForeignKey, DateTime, Float, Integer, Boolean, JSON
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship
from sqlalchemy.ext.declarative import declared_attr

from db.database import Base

class AreaSurvey(Base):
    """
    周边调研模型
    用于门店对周边5公里范围的门店进行市场调研
    """
    __tablename__ = "area_surveys"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    title = Column(String(100), nullable=False, comment="调研标题")
    description = Column(Text, nullable=True, comment="调研描述")

    # 调研类型：price(价格调研), promotion(促销活动), product(产品调研), etc.
    survey_type = Column(String(50), nullable=False, default="price", comment="调研类型")

    # 调研状态：draft(草稿), in_progress(进行中), completed(已完成), cancelled(已取消)
    status = Column(String(20), nullable=False, default="draft", comment="调研状态")

    # 调研开始时间
    start_date = Column(DateTime, nullable=True, comment="调研开始时间")

    # 调研结束时间
    end_date = Column(DateTime, nullable=True, comment="调研结束时间")

    # 调研完成时间
    completed_date = Column(DateTime, nullable=True, comment="调研完成时间")

    # 调研创建时间
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")

    # 调研更新时间
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")

    # 调研创建者
    created_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True, comment="创建者ID")

    # 调研更新者
    updated_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True, comment="更新者ID")

    # 调研所属项目
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id"), nullable=False, comment="项目ID")

    # 调研所属门店
    store_id = Column(UUID(as_uuid=True), ForeignKey("stores.id"), nullable=False, comment="门店ID")

    # 调研目标门店
    target_store_name = Column(String(100), nullable=False, comment="目标门店名称")
    target_store_address = Column(String(200), nullable=True, comment="目标门店地址")
    target_store_distance = Column(Float, nullable=True, comment="目标门店距离(公里)")
    target_store_type = Column(String(50), nullable=True, comment="目标门店类型")

    # 调研数据
    survey_data = Column(JSONB, default=dict, comment="调研数据")

    # 产品相关字段 (价格调研)
    product_id = Column(UUID(as_uuid=True), nullable=True, comment="产品ID")
    product_data = Column(JSONB, default=dict, comment="产品数据")

    # 促销活动相关字段
    promotion_data = Column(JSONB, default=dict, comment="促销活动数据")

    # 调研结论
    conclusion = Column(Text, nullable=True, comment="调研结论")

    # 调研建议
    recommendations = Column(Text, nullable=True, comment="调研建议")

    # 调研附件
    attachments = Column(JSONB, default=list, comment="附件列表")

    # 调研标签
    tags = Column(JSONB, default=list, comment="标签列表")

    # 调研评分 (1-5)
    rating = Column(Integer, nullable=True, comment="调研评分")

    # 是否为AI分析
    is_ai_analyzed = Column(Boolean, default=False, comment="是否为AI分析")

    # AI分析结果
    ai_analysis = Column(JSONB, default=dict, comment="AI分析结果")

    # 关系
    project = relationship("Project", foreign_keys=[project_id])
    store = relationship("Store", foreign_keys=[store_id])
    creator = relationship("User", foreign_keys=[created_by])
    updater = relationship("User", foreign_keys=[updated_by])

    def __repr__(self):
        return f"<AreaSurvey {self.title}>"
