import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Tooltip,
  Modal,
  Form,
  Input,
  Select,
  message,
  Drawer,
  Divider,
  Upload,
  Progress,
  Typography,
  Tabs,
  Badge,
  Popconfirm
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  UploadOutlined,
  ReloadOutlined,
  FileTextOutlined,
  QuestionCircleOutlined,
  SyncOutlined,
  SettingOutlined,
  EyeOutlined,
  CloudUploadOutlined,
  DatabaseOutlined
} from '@ant-design/icons';
import { useProject } from '../../contexts/ProjectContext';
import request from '../../services/request';

const { Title, Text } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;
const { TextArea } = Input;

const AIKnowledgeBaseList = () => {
  const { currentProject } = useProject();
  const [loading, setLoading] = useState(false);
  const [knowledgeBases, setKnowledgeBases] = useState([]);
  const [documents, setDocuments] = useState([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });
  const [activeTab, setActiveTab] = useState('bases');
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [currentKnowledgeBase, setCurrentKnowledgeBase] = useState(null);
  const [documentDrawerVisible, setDocumentDrawerVisible] = useState(false);
  const [currentDocument, setCurrentDocument] = useState(null);
  const [uploadVisible, setUploadVisible] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [fileList, setFileList] = useState([]);
  const [selectedKnowledgeBaseId, setSelectedKnowledgeBaseId] = useState(null);
  
  // 向量管理相关状态
  const [embeddingStats, setEmbeddingStats] = useState(null);
  const [vectorManagementVisible, setVectorManagementVisible] = useState(false);
  
  // 知识库详情相关状态
  const [knowledgeBaseDetailsVisible, setKnowledgeBaseDetailsVisible] = useState(false);
  const [knowledgeBaseDetails, setKnowledgeBaseDetails] = useState(null);
  const [detailsLoading, setDetailsLoading] = useState(false);
  
  const [form] = Form.useForm();
  const [documentForm] = Form.useForm();
  const [uploadForm] = Form.useForm();
  const [vectorForm] = Form.useForm();

  // 获取知识库列表
  const fetchKnowledgeBases = async () => {
    setLoading(true);
    try {
      const response = await request.get(`/project/${currentProject.id}/ai/knowledge/bases`);
      if (response.success) {
        setKnowledgeBases(response.data || []);
        setPagination({
          ...pagination,
          total: response.total || 0
        });
      }
    } catch (error) {
      console.error('获取知识库列表失败:', error);
      message.error('获取知识库列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取文档列表
  const fetchDocuments = async (knowledgeBaseId) => {
    if (!knowledgeBaseId) return;
    
    setLoading(true);
    try {
      const response = await request.get(`/project/${currentProject.id}/ai/knowledge/bases/${knowledgeBaseId}/documents`);
      if (response.success) {
        setDocuments(response.data || []);
      }
    } catch (error) {
      console.error('获取文档列表失败:', error);
      message.error('获取文档列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取嵌入模型列表已移除 - 由AI助手系统统一管理

  // 获取嵌入统计信息
  const fetchEmbeddingStats = async () => {
    try {
      const response = await request.get(`/project/${currentProject.id}/ai/embedding/stats`);
      if (response.success) {
        setEmbeddingStats(response.data);
      }
    } catch (error) {
      console.error('获取嵌入统计信息失败:', error);
    }
  };

  // 显示知识库详情
  const showKnowledgeBaseDetails = async (knowledgeBaseId) => {
    setDetailsLoading(true);
    try {
      const response = await request.get(`/project/${currentProject.id}/ai/embedding/knowledge-base/${knowledgeBaseId}/details`);
      if (response.success) {
        setKnowledgeBaseDetails(response.data);
        setKnowledgeBaseDetailsVisible(true);
      } else {
        message.error(response.message || '获取知识库详情失败');
      }
    } catch (error) {
      console.error('获取知识库详情失败:', error);
      message.error('获取知识库详情失败');
    } finally {
      setDetailsLoading(false);
    }
  };

  // 关闭知识库详情
  const closeKnowledgeBaseDetails = () => {
    setKnowledgeBaseDetailsVisible(false);
    setKnowledgeBaseDetails(null);
  };

  // 初始化
  useEffect(() => {
    if (currentProject?.id) {
      fetchKnowledgeBases();
      if (activeTab === 'vectors') {
        fetchEmbeddingStats();
      }
    }
  }, [currentProject?.id, activeTab]);

  // 打开知识库抽屉
  const showKnowledgeBaseDrawer = (knowledgeBase = null) => {
    setCurrentKnowledgeBase(knowledgeBase);
    
    if (knowledgeBase) {
      form.setFieldsValue({
        name: knowledgeBase.name,
        description: knowledgeBase.description,
        industry_type: knowledgeBase.industry_type || 'retail',
        status: knowledgeBase.status || 'active'
      });
    } else {
      form.resetFields();
      form.setFieldsValue({
        industry_type: 'retail',
        status: 'active'
      });
    }
    
    setDrawerVisible(true);
  };

  // 关闭知识库抽屉
  const closeKnowledgeBaseDrawer = () => {
    setDrawerVisible(false);
    setCurrentKnowledgeBase(null);
    form.resetFields();
  };

  // 保存知识库
  const handleSaveKnowledgeBase = async () => {
    try {
      const values = await form.validateFields();
      
      if (currentKnowledgeBase) {
        // 更新知识库
        const response = await request.put(`/project/${currentProject.id}/ai/knowledge/bases/${currentKnowledgeBase.id}`, values);
        if (response.success) {
          message.success('更新知识库成功');
          closeKnowledgeBaseDrawer();
          fetchKnowledgeBases();
        } else {
          message.error(response.message || '更新知识库失败');
        }
      } else {
        // 创建知识库
        const response = await request.post(`/project/${currentProject.id}/ai/knowledge/bases`, values);
        if (response.success) {
          message.success('创建知识库成功');
          closeKnowledgeBaseDrawer();
          fetchKnowledgeBases();
        } else {
          message.error(response.message || '创建知识库失败');
        }
      }
    } catch (error) {
      console.error('保存知识库出错:', error);
    }
  };

  // 删除知识库
  const handleDeleteKnowledgeBase = async (id) => {
    try {
      const response = await request.delete(`/project/${currentProject.id}/ai/knowledge/bases/${id}`);
      if (response.success) {
        message.success('删除知识库成功');
        fetchKnowledgeBases();
      } else {
        message.error(response.message || '删除知识库失败');
      }
    } catch (error) {
      console.error('删除知识库出错:', error);
      message.error('删除知识库失败');
    }
  };

  // 打开文档抽屉
  const showDocumentDrawer = (document = null) => {
    setCurrentDocument(document);
    
    if (document) {
      documentForm.setFieldsValue({
        title: document.title,
        content: document.content,
        status: document.status || 'active'
      });
    } else {
      documentForm.resetFields();
      documentForm.setFieldsValue({
        status: 'active'
      });
    }
    
    setDocumentDrawerVisible(true);
  };

  // 关闭文档抽屉
  const closeDocumentDrawer = () => {
    setDocumentDrawerVisible(false);
    setCurrentDocument(null);
    documentForm.resetFields();
  };

  // 保存文档
  const handleSaveDocument = async () => {
    try {
      const values = await documentForm.validateFields();
      
      if (!selectedKnowledgeBaseId) {
        message.error('请先选择知识库');
        return;
      }
      
      if (currentDocument) {
        // 更新文档
        const response = await request.put(
          `/project/${currentProject.id}/ai/knowledge/bases/${selectedKnowledgeBaseId}/documents/${currentDocument.id}`, 
          values
        );
        if (response.success) {
          message.success('更新文档成功');
          closeDocumentDrawer();
          fetchDocuments(selectedKnowledgeBaseId);
        } else {
          message.error(response.message || '更新文档失败');
        }
      } else {
        // 创建文档
        const response = await request.post(
          `/project/${currentProject.id}/ai/knowledge/bases/${selectedKnowledgeBaseId}/documents`, 
          values
        );
        if (response.success) {
          message.success('创建文档成功');
          closeDocumentDrawer();
          fetchDocuments(selectedKnowledgeBaseId);
        } else {
          message.error(response.message || '创建文档失败');
        }
      }
    } catch (error) {
      console.error('保存文档出错:', error);
    }
  };

  // 删除文档
  const handleDeleteDocument = async (id) => {
    try {
      const response = await request.delete(
        `/project/${currentProject.id}/ai/knowledge/bases/${selectedKnowledgeBaseId}/documents/${id}`
      );
      if (response.success) {
        message.success('删除文档成功');
        fetchDocuments(selectedKnowledgeBaseId);
      } else {
        message.error(response.message || '删除文档失败');
      }
    } catch (error) {
      console.error('删除文档出错:', error);
      message.error('删除文档失败');
    }
  };

  // 打开上传抽屉
  const showUploadDrawer = () => {
    if (!selectedKnowledgeBaseId) {
      message.error('请先选择知识库');
      return;
    }
    
    setUploadVisible(true);
    setFileList([]);
    setUploadProgress(0);
    uploadForm.resetFields();
  };

  // 关闭上传抽屉
  const closeUploadDrawer = () => {
    setUploadVisible(false);
    setFileList([]);
    setUploadProgress(0);
    uploadForm.resetFields();
  };

  // 处理文件上传
  const handleUpload = async () => {
    try {
      if (fileList.length === 0) {
        message.error('请选择要上传的文件');
        return;
      }
      
      // 获取表单值（不验证，因为所有字段都是可选的）
      const values = uploadForm.getFieldsValue();
      
      setLoading(true);
      
             // 逐个上传文件
       for (const file of fileList) {
         const formData = new FormData();
         formData.append('file', file.originFileObj || file);
         // 如果有多个文件且用户输入了标题，为每个文件添加序号
         const title = fileList.length > 1 && values.title 
           ? `${values.title} - ${file.name}` 
           : values.title || file.name;
         formData.append('title', title);
         formData.append('description', values.description || '');
        
        // 模拟上传进度
        let progress = 0;
        const interval = setInterval(() => {
          progress += 10;
          if (progress > 90) {
            clearInterval(interval);
          }
          setUploadProgress(progress);
        }, 300);
        
        const response = await request.post(
          `/project/${currentProject.id}/ai/knowledge/bases/${selectedKnowledgeBaseId}/upload`,
          formData,
          {
            headers: {
              'Content-Type': 'multipart/form-data'
            }
          }
        );
        
        clearInterval(interval);
        setUploadProgress(100);
        
        if (response.success) {
          message.success(`文件 ${file.name} 上传成功`);
        } else {
          message.error(response.message || `文件 ${file.name} 上传失败`);
        }
      }
      
      setTimeout(() => {
        closeUploadDrawer();
        fetchDocuments(selectedKnowledgeBaseId);
      }, 1000);
      
    } catch (error) {
      console.error('文件上传出错:', error);
      message.error('文件上传失败');
      setUploadProgress(0);
    } finally {
      setLoading(false);
    }
  };

  // 处理文件列表变化
  const handleFileChange = ({ fileList }) => {
    setFileList(fileList);
  };

  // 处理知识库选择
  const handleKnowledgeBaseSelect = (knowledgeBaseId) => {
    setSelectedKnowledgeBaseId(knowledgeBaseId);
    fetchDocuments(knowledgeBaseId);
  };

  // 嵌入模型管理已移除 - 由AI助手系统统一管理

  // 向量管理函数
  const showVectorManagementModal = () => {
    setVectorManagementVisible(true);
    vectorForm.resetFields();
    // 确保知识库数据已加载
    if (knowledgeBases.length === 0) {
      fetchKnowledgeBases();
    }
  };

  const closeVectorManagementModal = () => {
    setVectorManagementVisible(false);
    vectorForm.resetFields();
  };

  const handleVectorManagement = async () => {
    try {
      setLoading(true);
      const values = await vectorForm.validateFields();
      
      // 所有向量管理操作都使用统一的API端点
      let requestData;
      
      if (values.action === 'create_index') {
        // 对于创建索引，knowledge_base_ids在单选模式下是字符串
        let knowledge_base_id = values.knowledge_base_ids;
        if (Array.isArray(knowledge_base_id)) {
          knowledge_base_id = knowledge_base_id[0];
        }
        
        requestData = {
          action: 'create_index',
          knowledge_base_ids: [knowledge_base_id], // 后端期望数组格式
          options: {
            force_rebuild: values.options?.force_rebuild || false
          }
        };
      } else {
        // 其他操作使用原有格式
        requestData = values;
      }
      
      const response = await request.post(`/project/${currentProject.id}/ai/embedding/vector-management`, requestData);
      
      if (response.success) {
        message.success(response.message);
        closeVectorManagementModal();
        fetchEmbeddingStats();
        // 如果是在文档页面，刷新文档列表
        if (activeTab === 'documents' && selectedKnowledgeBaseId) {
          fetchDocuments(selectedKnowledgeBaseId);
        }
        // 刷新知识库列表
        fetchKnowledgeBases();
      } else {
        // 处理API返回的错误
        message.error(response.message || '向量管理操作失败');
      }
    } catch (error) {
      console.error('向量管理操作出错:', error);
      
      // 处理不同类型的错误
      if (error.response && error.response.data) {
        const errorData = error.response.data;
        if (errorData.detail) {
          message.error(errorData.detail);
        } else if (errorData.message) {
          message.error(errorData.message);
        } else {
          message.error('向量管理操作失败');
        }
      } else if (error.message) {
        message.error(error.message);
      } else {
        message.error('向量管理操作失败');
      }
    } finally {
      setLoading(false);
    }
  };

  // 知识库表格列定义
  const knowledgeBaseColumns = [
    {
      title: '知识库名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },
    {
      title: '行业',
      dataIndex: 'industry_type',
      key: 'industry_type',
      render: (type) => {
        const industryMap = {
          'retail': '零售',
          'restaurant': '餐饮',
          'hotel': '酒店',
          'education': '教育',
          'healthcare': '医疗',
          'general': '通用'
        };
        return industryMap[type] || type;
      }
    },
    {
      title: '文档数量',
      dataIndex: 'document_count',
      key: 'document_count',
      render: (count) => count || 0
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={status === 'active' ? 'green' : 'default'}>
          {status === 'active' ? '启用' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="查看文档">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => {
                setActiveTab('documents');
                handleKnowledgeBaseSelect(record.id);
              }}
            />
          </Tooltip>
          <Tooltip title="向量详情">
            <Button
              type="text"
              icon={<DatabaseOutlined />}
              onClick={() => showKnowledgeBaseDetails(record.id)}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => showKnowledgeBaseDrawer(record)}
            />
          </Tooltip>
          <Popconfirm
            title="确定要删除这个知识库吗？"
            description="删除后将无法恢复，且所有关联的文档也将被删除。"
            onConfirm={() => handleDeleteKnowledgeBase(record.id)}
            okText="删除"
            cancelText="取消"
          >
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
            />
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 文档表格列定义
  const documentColumns = [
    {
      title: '文档标题',
      dataIndex: 'title',
      key: 'title',
    },
    {
      title: '内容预览',
      dataIndex: 'content',
      key: 'content',
      ellipsis: true,
      render: (content) => content ? content.substring(0, 50) + '...' : '-'
    },
    {
      title: '文件类型',
      dataIndex: 'file_type',
      key: 'file_type',
      render: (type) => type || '-'
    },
    {
      title: '处理状态',
      dataIndex: 'embedding_status',
      key: 'embedding_status',
      render: (status) => {
        const statusMap = {
          pending: { color: 'orange', text: '待处理' },
          processing: { color: 'blue', text: '处理中' },
          completed: { color: 'green', text: '已完成' },
          failed: { color: 'red', text: '失败' }
        };
        const statusInfo = statusMap[status] || { color: 'default', text: status || '未知' };
        return (
          <Tag color={statusInfo.color}>
            {statusInfo.text}
          </Tag>
        );
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={status === 'active' ? 'green' : 'default'}>
          {status === 'active' ? '启用' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="编辑">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => showDocumentDrawer(record)}
            />
          </Tooltip>
          <Popconfirm
            title="确定要删除这个文档吗？"
            description="删除后将无法恢复。"
            onConfirm={() => handleDeleteDocument(record.id)}
            okText="删除"
            cancelText="取消"
          >
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
            />
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 嵌入模型表格列定义已移除 - 由AI助手系统统一管理

  return (
    <div>
      <Card
        variant="outlined"
        title={
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <span>AI 知识库管理</span>
            <Space>
              <span
                style={{
                  cursor: 'pointer',
                  fontWeight: activeTab === 'bases' ? 'bold' : 'normal',
                  borderBottom: activeTab === 'bases' ? '2px solid #1890ff' : 'none',
                  paddingBottom: 8,
                  marginRight: 16
                }}
                onClick={() => setActiveTab('bases')}
              >
                知识库列表
              </span>
              <span
                style={{
                  cursor: 'pointer',
                  fontWeight: activeTab === 'documents' ? 'bold' : 'normal',
                  borderBottom: activeTab === 'documents' ? '2px solid #1890ff' : 'none',
                  paddingBottom: 8
                }}
                onClick={() => setActiveTab('documents')}
              >
                文档管理
                {selectedKnowledgeBaseId && (
                  <Badge 
                    count={knowledgeBases.find(kb => kb.id === selectedKnowledgeBaseId)?.name} 
                    style={{ backgroundColor: '#1890ff', marginLeft: 8 }} 
                  />
                )}
              </span>
              <span
                style={{
                  cursor: 'pointer',
                  fontWeight: activeTab === 'vectors' ? 'bold' : 'normal',
                  borderBottom: activeTab === 'vectors' ? '2px solid #1890ff' : 'none',
                  paddingBottom: 8
                }}
                onClick={() => setActiveTab('vectors')}
              >
                向量管理
              </span>
            </Space>
          </div>
        }
      >
        {activeTab === 'bases' ? (
          <>
            <div style={{ marginBottom: 16 }}>
              <Space>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => showKnowledgeBaseDrawer()}
                >
                  创建知识库
                </Button>
                <Button
                  icon={<ReloadOutlined />}
                  onClick={fetchKnowledgeBases}
                >
                  刷新
                </Button>
              </Space>
            </div>
            <Table
              columns={knowledgeBaseColumns}
              dataSource={knowledgeBases}
              rowKey="id"
              pagination={pagination}
              loading={loading}
            />
          </>
        ) : activeTab === 'documents' ? (
          <>
            <div style={{ marginBottom: 16 }}>
              <Space>
                <Select
                  placeholder="请选择知识库"
                  style={{ width: 200 }}
                  value={selectedKnowledgeBaseId}
                  onChange={handleKnowledgeBaseSelect}
                >
                  {knowledgeBases.map(kb => (
                    <Option key={kb.id} value={kb.id}>{kb.name}</Option>
                  ))}
                </Select>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => showDocumentDrawer()}
                  disabled={!selectedKnowledgeBaseId}
                >
                  添加文档
                </Button>
                <Button
                  type="primary"
                  icon={<CloudUploadOutlined />}
                  onClick={showUploadDrawer}
                  disabled={!selectedKnowledgeBaseId}
                >
                  上传文件
                </Button>
                <Button
                  icon={<ReloadOutlined />}
                  onClick={() => fetchDocuments(selectedKnowledgeBaseId)}
                  disabled={!selectedKnowledgeBaseId}
                >
                  刷新
                </Button>
              </Space>
            </div>
            <Table
              columns={documentColumns}
              dataSource={documents}
              rowKey="id"
              pagination={pagination}
              loading={loading}
              locale={{ emptyText: selectedKnowledgeBaseId ? '暂无文档' : '请先选择知识库' }}
            />
          </>
        ) : (
          <>
            {/* 向量管理页面 */}
            <div style={{ marginBottom: 16 }}>
              <Space>
                <Button
                  type="primary"
                  icon={<SettingOutlined />}
                  onClick={showVectorManagementModal}
                >
                  向量管理
                </Button>
                <Button
                  icon={<ReloadOutlined />}
                  onClick={fetchEmbeddingStats}
                >
                  刷新统计
                </Button>
              </Space>
                              <div style={{ marginTop: 8 }}>
                <Text type="secondary">
                  嵌入模型由AI助手系统统一管理，请在"AI助手 &gt; AI模型与配置"中进行配置
                </Text>
              </div>
            </div>
            
            {/* 统计信息卡片 */}
            {embeddingStats && (
              <div style={{ marginBottom: 16 }}>
                <Space size="large">
                  <Card size="small" style={{ minWidth: 150 }}>
                    <div style={{ textAlign: 'center' }}>
                      <div style={{ fontSize: 24, fontWeight: 'bold', color: '#1890ff' }}>
                        {embeddingStats.knowledge_bases?.total || 0}
                      </div>
                      <div style={{ color: '#666' }}>知识库总数</div>
                      <div style={{ fontSize: 12, color: '#999' }}>
                        活跃: {embeddingStats.knowledge_bases?.active || 0}
                      </div>
                    </div>
                  </Card>
                  <Card size="small" style={{ minWidth: 150 }}>
                    <div style={{ textAlign: 'center' }}>
                      <div style={{ fontSize: 24, fontWeight: 'bold', color: '#52c41a' }}>
                        {embeddingStats.documents?.total || 0}
                      </div>
                      <div style={{ color: '#666' }}>文档总数</div>
                      <div style={{ fontSize: 12, color: '#999' }}>
                        完成率: {embeddingStats.documents?.completion_rate || 0}%
                      </div>
                    </div>
                  </Card>
                  <Card size="small" style={{ minWidth: 150 }}>
                    <div style={{ textAlign: 'center' }}>
                      <div style={{ fontSize: 24, fontWeight: 'bold', color: '#faad14' }}>
                        {embeddingStats.vectors?.indexed_chunks || 0}
                      </div>
                      <div style={{ color: '#666' }}>已索引向量</div>
                      <div style={{ fontSize: 12, color: '#999' }}>
                        索引率: {embeddingStats.vectors?.index_rate || 0}%
                      </div>
                    </div>
                  </Card>
                  <Card size="small" style={{ minWidth: 150 }}>
                    <div style={{ textAlign: 'center' }}>
                      <div style={{ fontSize: 24, fontWeight: 'bold', color: '#722ed1' }}>
                        {embeddingStats.vectors?.estimated_storage_size_mb || 0}
                      </div>
                      <div style={{ color: '#666' }}>存储大小(MB)</div>
                      <div style={{ fontSize: 12, color: '#999' }}>
                        总块数: {embeddingStats.vectors?.total_chunks || 0}
                      </div>
                    </div>
                  </Card>
                </Space>
              </div>
            )}
            
            {/* 向量统计详情 */}
            {embeddingStats && (
              <div style={{ marginBottom: 16 }}>
                <Space direction="vertical" style={{ width: '100%' }} size="large">
                  {/* 文档处理状态分布 */}
                  <Card title="文档处理状态" size="small">
                    <Space size="large">
                      <div style={{ textAlign: 'center' }}>
                        <div style={{ fontSize: 20, fontWeight: 'bold', color: '#52c41a' }}>
                          {embeddingStats.documents?.completed || 0}
                        </div>
                        <div style={{ color: '#666' }}>已完成</div>
                      </div>
                      <div style={{ textAlign: 'center' }}>
                        <div style={{ fontSize: 20, fontWeight: 'bold', color: '#faad14' }}>
                          {embeddingStats.documents?.pending || 0}
                        </div>
                        <div style={{ color: '#666' }}>待处理</div>
                      </div>
                      <div style={{ textAlign: 'center' }}>
                        <div style={{ fontSize: 20, fontWeight: 'bold', color: '#ff4d4f' }}>
                          {embeddingStats.documents?.failed || 0}
                        </div>
                        <div style={{ color: '#666' }}>失败</div>
                      </div>
                    </Space>
                  </Card>

                  {/* 向量索引状态 */}
                  <Card title="向量索引状态" size="small">
                    <Space size="large">
                      <div style={{ textAlign: 'center' }}>
                        <div style={{ fontSize: 20, fontWeight: 'bold', color: '#1890ff' }}>
                          {embeddingStats.vectors?.total_chunks || 0}
                        </div>
                        <div style={{ color: '#666' }}>总文本块</div>
                      </div>
                      <div style={{ textAlign: 'center' }}>
                        <div style={{ fontSize: 20, fontWeight: 'bold', color: '#52c41a' }}>
                          {embeddingStats.vectors?.indexed_chunks || 0}
                        </div>
                        <div style={{ color: '#666' }}>已索引</div>
                      </div>
                      <div style={{ textAlign: 'center' }}>
                        <div style={{ fontSize: 20, fontWeight: 'bold', color: '#722ed1' }}>
                          {embeddingStats.vectors?.estimated_storage_size || 0}
                        </div>
                        <div style={{ color: '#666' }}>存储字节</div>
                      </div>
                    </Space>
                  </Card>
                </Space>
              </div>
            )}
          </>
        )}
      </Card>

      {/* 知识库抽屉 */}
      <Drawer
        title={currentKnowledgeBase ? '编辑知识库' : '创建知识库'}
        width={600}
        onClose={closeKnowledgeBaseDrawer}
        open={drawerVisible}
        styles={{ body: { paddingBottom: 80 } }}
        footer={
          <div style={{ textAlign: 'right' }}>
            <Button onClick={closeKnowledgeBaseDrawer} style={{ marginRight: 8 }}>
              取消
            </Button>
            <Button onClick={handleSaveKnowledgeBase} type="primary" loading={loading}>
              保存
            </Button>
          </div>
        }
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Form.Item
            name="name"
            label="知识库名称"
            rules={[{ required: true, message: '请输入知识库名称' }]}
          >
            <Input placeholder="请输入知识库名称" />
          </Form.Item>

          <Form.Item
            name="description"
            label="描述"
          >
            <TextArea rows={4} placeholder="请输入知识库描述" />
          </Form.Item>

          <Form.Item
            name="industry_type"
            label="行业类型"
            rules={[{ required: true, message: '请选择行业类型' }]}
          >
            <Select placeholder="请选择行业类型">
              <Option value="retail">零售</Option>
              <Option value="restaurant">餐饮</Option>
              <Option value="hotel">酒店</Option>
              <Option value="education">教育</Option>
              <Option value="healthcare">医疗</Option>
              <Option value="general">通用</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="status"
            label="状态"
          >
            <Select>
              <Option value="active">启用</Option>
              <Option value="inactive">禁用</Option>
            </Select>
          </Form.Item>
        </Form>
      </Drawer>

      {/* 文档抽屉 */}
      <Drawer
        title={currentDocument ? '编辑文档' : '添加文档'}
        width={600}
        onClose={closeDocumentDrawer}
        open={documentDrawerVisible}
        styles={{ body: { paddingBottom: 80 } }}
        footer={
          <div style={{ textAlign: 'right' }}>
            <Button onClick={closeDocumentDrawer} style={{ marginRight: 8 }}>
              取消
            </Button>
            <Button onClick={handleSaveDocument} type="primary" loading={loading}>
              保存
            </Button>
          </div>
        }
      >
        <Form
          form={documentForm}
          layout="vertical"
        >
          <Form.Item
            name="title"
            label="文档标题"
            rules={[{ required: true, message: '请输入文档标题' }]}
          >
            <Input placeholder="请输入文档标题" />
          </Form.Item>

          <Form.Item
            name="content"
            label="文档内容"
            rules={[{ required: true, message: '请输入文档内容' }]}
          >
            <TextArea rows={10} placeholder="请输入文档内容" />
          </Form.Item>

          <Form.Item
            name="status"
            label="状态"
          >
            <Select>
              <Option value="active">启用</Option>
              <Option value="inactive">禁用</Option>
            </Select>
          </Form.Item>
        </Form>
      </Drawer>

      {/* 上传抽屉 */}
      <Drawer
        title="上传文件"
        width={500}
        onClose={closeUploadDrawer}
        open={uploadVisible}
        styles={{ body: { paddingBottom: 80 } }}
        footer={
          <div style={{ textAlign: 'right' }}>
            <Button onClick={closeUploadDrawer} style={{ marginRight: 8 }}>
              取消
            </Button>
            <Button onClick={handleUpload} type="primary" loading={loading} disabled={fileList.length === 0}>
              上传
            </Button>
          </div>
        }
      >
        <Form
          form={uploadForm}
          layout="vertical"
        >
          <Form.Item
            name="title"
            label="文档标题"
          >
            <Input placeholder="请输入文档标题（留空时使用文件名）" />
          </Form.Item>

          <Form.Item
            name="description"
            label="文档描述"
          >
            <TextArea rows={3} placeholder="请输入文档描述（可选）" />
          </Form.Item>

          <Form.Item
            label="选择文件"
            required
          >
            <Upload
              fileList={fileList}
              onChange={handleFileChange}
              beforeUpload={() => false}
              multiple
              accept=".pdf,.doc,.docx,.xls,.xlsx,.txt,.md,.csv"
            >
              <Button icon={<UploadOutlined />}>选择文件</Button>
            </Upload>
            <div style={{ marginTop: 16 }}>
              <Text type="secondary">支持的文件格式：PDF, DOC, DOCX, XLS, XLSX, TXT, MD, CSV</Text>
            </div>
          </Form.Item>

          {uploadProgress > 0 && (
            <Form.Item>
              <Progress percent={uploadProgress} status={uploadProgress === 100 ? 'success' : 'active'} />
            </Form.Item>
          )}
        </Form>
      </Drawer>

      {/* 知识库详情对话框 */}
      <Modal
        title={`知识库详情 - ${knowledgeBaseDetails?.knowledge_base?.name || ''}`}
        open={knowledgeBaseDetailsVisible}
        onCancel={closeKnowledgeBaseDetails}
        width={900}
        footer={[
          <Button key="close" onClick={closeKnowledgeBaseDetails}>
            关闭
          </Button>
        ]}
      >
        {knowledgeBaseDetails && (
          <div>
            {/* 知识库基本信息 */}
            <Card size="small" title="基本信息" style={{ marginBottom: 16 }}>
              <div style={{ display: 'grid', gridTemplateColumns: 'repeat(2, 1fr)', gap: 16 }}>
                <div>
                  <Text strong>知识库名称：</Text>
                  <Text>{knowledgeBaseDetails.knowledge_base.name}</Text>
                </div>
                <div>
                  <Text strong>状态：</Text>
                  <Tag color={knowledgeBaseDetails.knowledge_base.status === 'active' ? 'green' : 'default'}>
                    {knowledgeBaseDetails.knowledge_base.status === 'active' ? '启用' : '禁用'}
                  </Tag>
                </div>
                <div>
                  <Text strong>文档数量：</Text>
                  <Text>{knowledgeBaseDetails.knowledge_base.document_count || 0}</Text>
                </div>
                <div>
                  <Text strong>块数量：</Text>
                  <Text>{knowledgeBaseDetails.knowledge_base.chunk_count || 0}</Text>
                </div>
              </div>
              {knowledgeBaseDetails.knowledge_base.description && (
                <div style={{ marginTop: 12 }}>
                  <Text strong>描述：</Text>
                  <div style={{ marginTop: 4 }}>
                    <Text>{knowledgeBaseDetails.knowledge_base.description}</Text>
                  </div>
                </div>
              )}
            </Card>

            {/* 向量统计 */}
            <Card size="small" title="向量统计" style={{ marginBottom: 16 }}>
              <div style={{ display: 'grid', gridTemplateColumns: 'repeat(2, 1fr)', gap: 16 }}>
                <div>
                  <Text strong>总块数：</Text>
                  <Text>{knowledgeBaseDetails.statistics.total_chunks || 0}</Text>
                </div>
                <div>
                  <Text strong>已向量化：</Text>
                  <Text style={{ color: knowledgeBaseDetails.statistics.chunks_with_embedding > 0 ? '#52c41a' : '#f5222d' }}>
                    {knowledgeBaseDetails.statistics.chunks_with_embedding || 0}
                  </Text>
                </div>
                <div>
                  <Text strong>总Token数：</Text>
                  <Text>{(knowledgeBaseDetails.statistics.total_tokens || 0).toLocaleString()}</Text>
                </div>
                <div>
                  <Text strong>向量化率：</Text>
                  <Text style={{ 
                    color: knowledgeBaseDetails.statistics.embedding_success_rate >= 100 ? '#52c41a' :
                           knowledgeBaseDetails.statistics.embedding_success_rate >= 80 ? '#fa8c16' : '#f5222d'
                  }}>
                    {knowledgeBaseDetails.statistics.embedding_success_rate?.toFixed(1) || 0}%
                  </Text>
                </div>
              </div>
            </Card>

            {/* 文档详情 */}
            <Card size="small" title="文档详情">
              <Table
                size="small"
                dataSource={knowledgeBaseDetails.documents}
                pagination={false}
                scroll={{ y: 300 }}
                columns={[
                  {
                    title: '文档标题',
                    dataIndex: 'title',
                    key: 'title',
                    ellipsis: true,
                  },
                                     {
                     title: '文件路径',
                     dataIndex: 'file_path',
                     key: 'file_path',
                     width: 120,
                     ellipsis: true,
                     render: (path) => path ? path.split('/').pop() : '-' // 只显示文件名部分
                   },
                   {
                     title: '文件类型',
                     dataIndex: 'file_type',
                     key: 'file_type',
                     width: 80,
                   },
                  {
                    title: '状态',
                    dataIndex: 'embedding_status',
                    key: 'embedding_status',
                    width: 80,
                    render: (status) => {
                      const statusMap = {
                        pending: { color: 'orange', text: '待处理' },
                        processing: { color: 'blue', text: '处理中' },
                        completed: { color: 'green', text: '已完成' },
                        failed: { color: 'red', text: '失败' }
                      };
                      const statusInfo = statusMap[status] || { color: 'default', text: status || '未知' };
                      return <Tag color={statusInfo.color} size="small">{statusInfo.text}</Tag>;
                    },
                  },
                  {
                    title: '块数',
                    dataIndex: 'total_chunks',
                    key: 'total_chunks',
                    width: 60,
                    render: (count) => count || 0
                  },
                  {
                    title: '向量',
                    dataIndex: 'chunks_with_embedding',
                    key: 'chunks_with_embedding',
                    width: 60,
                    render: (count, record) => (
                      <span style={{ color: count === record.total_chunks && count > 0 ? '#52c41a' : '#f5222d' }}>
                        {count || 0}
                      </span>
                    )
                  },
                  {
                    title: '成功率',
                    key: 'success_rate',
                    width: 80,
                    render: (_, record) => {
                      const rate = record.embedding_success_rate || 0;
                      return (
                        <span style={{ 
                          color: rate >= 100 ? '#52c41a' : rate >= 80 ? '#fa8c16' : '#f5222d'
                        }}>
                          {rate.toFixed(1)}%
                        </span>
                      );
                    }
                  },
                  {
                    title: '块预览',
                    key: 'chunks_preview',
                    ellipsis: true,
                    render: (_, record) => (
                      <div>
                        {record.chunks_preview?.slice(0, 2).map((chunk) => (
                          <div key={chunk.id} style={{ fontSize: 12, color: '#666' }}>
                            <span style={{ 
                              color: chunk.has_embedding ? '#52c41a' : '#f5222d',
                              marginRight: 4
                            }}>
                              #{chunk.chunk_index}
                            </span>
                            {chunk.content_preview}
                          </div>
                        ))}
                        {record.chunks_preview?.length > 2 && (
                          <Text type="secondary" style={{ fontSize: 12 }}>
                            ... 还有 {record.chunks_preview.length - 2} 个块
                          </Text>
                        )}
                      </div>
                    )
                  }
                ]}
              />
            </Card>
          </div>
        )}
      </Modal>

      {/* 向量管理对话框 */}
      <Modal
        title="向量管理"
        open={vectorManagementVisible}
        onOk={handleVectorManagement}
        onCancel={closeVectorManagementModal}
        width={600}
        confirmLoading={loading}
      >
        <Form
          form={vectorForm}
          layout="vertical"
        >
          <Form.Item
            name="action"
            label="操作类型"
            rules={[{ required: true, message: '请选择操作类型' }]}
          >
            <Select placeholder="请选择操作类型">
              <Option value="create_index">新建索引</Option>
              <Option value="cleanup">清理孤立向量</Option>
              <Option value="reindex">重建索引</Option>
              <Option value="optimize">优化向量存储</Option>
            </Select>
          </Form.Item>

          <Form.Item noStyle shouldUpdate>
            {({ getFieldValue }) => {
              const action = getFieldValue('action');
              const isCreateIndex = action === 'create_index';
              
              return (
                <Form.Item
                  name="knowledge_base_ids"
                  label="知识库范围"
                  rules={[
                    ({ getFieldValue }) => ({
                      validator(_, value) {
                        const currentAction = getFieldValue('action');
                        if (currentAction === 'create_index') {
                          // 对于单选模式，value应该是字符串；对于多选模式，value应该是数组
                          if (!value) {
                            return Promise.reject(new Error('创建索引操作需要选择单个知识库'));
                          }
                          // 在单选模式下，value是字符串；在多选模式下，value是数组
                          if (typeof value === 'string' && value.trim() === '') {
                            return Promise.reject(new Error('创建索引操作需要选择单个知识库'));
                          }
                        }
                        return Promise.resolve();
                      },
                    }),
                  ]}
                >
                  <Select 
                    key={action} // 强制重新渲染
                    mode={isCreateIndex ? undefined : 'multiple'}
                    placeholder={
                      isCreateIndex 
                        ? '请选择一个知识库' 
                        : '请选择知识库（留空表示全部）'
                    }
                    allowClear
                    notFoundContent={knowledgeBases.length === 0 ? "暂无知识库" : "无匹配项"}
                    showSearch
                    filterOption={(input, option) =>
                      option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                    }
                  >
                    {knowledgeBases.map(kb => (
                      <Option key={kb.id} value={kb.id}>{kb.name}</Option>
                    ))}
                  </Select>
                </Form.Item>
              );
            }}
          </Form.Item>

          <Form.Item noStyle shouldUpdate>
            {({ getFieldValue }) => {
              const action = getFieldValue('action');
              
              return action === 'create_index' ? (
                <Form.Item
                  name="options"
                  label="选项"
                  valuePropName="checked"
                >
                  <div>
                    <label style={{ display: 'flex', alignItems: 'center' }}>
                      <input 
                        type="checkbox" 
                        onChange={(e) => {
                          const options = vectorForm.getFieldValue('options') || {};
                          vectorForm.setFieldsValue({
                            options: { ...options, force_rebuild: e.target.checked }
                          });
                        }}
                        style={{ marginRight: 8 }}
                      />
                      强制重建（重新处理所有文档）
                    </label>
                  </div>
                </Form.Item>
              ) : null;
            }}
          </Form.Item>

          <div style={{ 
            background: '#f5f5f5', 
            padding: 12, 
            borderRadius: 6, 
            fontSize: 12, 
            color: '#666',
            marginTop: 8 
          }}>
            <div style={{ fontWeight: 'bold', marginBottom: 8, color: '#333' }}>操作说明：</div>
            <p style={{ margin: '4px 0' }}>• <strong>新建索引</strong>：为指定知识库创建向量索引，处理未索引的文档</p>
            <p style={{ margin: '4px 0' }}>• <strong>清理孤立向量</strong>：删除没有对应文档的嵌入记录</p>
            <p style={{ margin: '4px 0' }}>• <strong>重建索引</strong>：重新构建向量索引以提高搜索性能</p>
            <p style={{ margin: '4px 0' }}>• <strong>优化向量存储</strong>：压缩和优化向量数据存储，去重重复内容</p>
          </div>
        </Form>
      </Modal>
    </div>
  );
};

export default AIKnowledgeBaseList;
