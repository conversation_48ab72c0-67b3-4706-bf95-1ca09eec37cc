#!/usr/bin/env python
# -*- coding: utf-8 -*-

import logging
import asyncio
import httpx
import json
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import select, and_

from models.user import ThirdPartyAccount
from models.project import Project
from ..models.models import DingTalkSettings

logger = logging.getLogger(__name__)

class DingTalkTokenManager:
    """钉钉访问凭证管理器"""
    
    def __init__(self):
        self._access_tokens = {}  # 缓存访问令牌
        
    async def get_access_token(self, db: Session, project: Project, user_id: str = None) -> Optional[str]:
        """
        获取钉钉访问令牌
        
        Args:
            db: 数据库会话
            project: 项目对象
            user_id: 用户ID（可选，用于获取用户级别的令牌）
            
        Returns:
            访问令牌字符串，如果获取失败返回None
        """
        try:
            # 1. 首先尝试从项目配置获取应用凭证
            app_key, app_secret = await self._get_app_credentials(db, project)
            
            if app_key and app_secret:
                # 使用应用凭证获取企业访问令牌
                token = await self._get_enterprise_token(app_key, app_secret)
                if token:
                    return token
            
            # 2. 如果指定了用户ID，尝试从用户绑定获取令牌
            if user_id:
                token = await self._get_user_token(db, user_id)
                if token:
                    return token
            
            # 3. 尝试从任意已绑定用户获取令牌
            token = await self._get_any_user_token(db, project)
            if token:
                return token
                
            logger.warning(f"无法获取项目 {project.id} 的钉钉访问令牌")
            return None
            
        except Exception as e:
            logger.error(f"获取钉钉访问令牌失败: {str(e)}")
            return None
    
    async def _get_app_credentials(self, db: Session, project: Project) -> tuple[Optional[str], Optional[str]]:
        """从项目配置获取应用凭证"""
        try:
            # 从系统配置获取钉钉应用凭证
            from services.system_config import SystemConfigService
            configs = await SystemConfigService.get_configs_by_type(db, project.id, "third_party_login")
            config_dict = {config.config_key: config.config_value for config in configs}
            dingtalk_config = config_dict.get("dingtalk", {})
            
            app_key = dingtalk_config.get("app_key")
            app_secret = dingtalk_config.get("app_secret")
            
            return app_key, app_secret
            
        except Exception as e:
            logger.error(f"获取应用凭证失败: {str(e)}")
            return None, None
    
    async def _get_enterprise_token(self, app_key: str, app_secret: str) -> Optional[str]:
        """使用应用凭证获取企业访问令牌"""
        cache_key = f"enterprise_{app_key}"
        
        # 检查缓存
        if cache_key in self._access_tokens:
            token_info = self._access_tokens[cache_key]
            if datetime.now() < token_info['expires_at']:
                return token_info['token']
        
        try:
            async with httpx.AsyncClient() as client:
                url = "https://oapi.dingtalk.com/gettoken"
                params = {
                    "appkey": app_key,
                    "appsecret": app_secret
                }
                
                response = await client.get(url, params=params)
                if response.status_code == 200:
                    data = response.json()
                    if data.get("errcode") == 0:
                        token = data.get("access_token")
                        expires_in = data.get("expires_in", 7200)  # 默认2小时
                        
                        # 缓存令牌
                        self._access_tokens[cache_key] = {
                            'token': token,
                            'expires_at': datetime.now() + timedelta(seconds=expires_in - 300)  # 提前5分钟过期
                        }
                        
                        logger.info(f"成功获取企业访问令牌")
                        return token
                    else:
                        logger.error(f"获取企业访问令牌失败: {data.get('errmsg')}")
                else:
                    logger.error(f"请求企业访问令牌失败: HTTP {response.status_code}")
                        
        except Exception as e:
            logger.error(f"获取企业访问令牌异常: {str(e)}")
            
        return None
    
    async def _get_user_token(self, db: Session, user_id: str) -> Optional[str]:
        """从指定用户的绑定信息获取访问令牌"""
        try:
            # 查询用户的钉钉绑定信息
            query = select(ThirdPartyAccount).where(
                and_(
                    ThirdPartyAccount.user_id == user_id,
                    ThirdPartyAccount.platform == "dingtalk"
                )
            )
            result = db.execute(query)
            account = result.scalar_one_or_none()
            
            if account and account.access_token:
                # 检查令牌是否过期
                if account.token_expires_at and datetime.now() < account.token_expires_at:
                    return account.access_token
                else:
                    # 尝试刷新令牌
                    if account.refresh_token:
                        new_token = await self._refresh_user_token(account.refresh_token)
                        if new_token:
                            # 更新数据库中的令牌
                            account.access_token = new_token['access_token']
                            account.token_expires_at = datetime.now() + timedelta(seconds=new_token['expires_in'])
                            if 'refresh_token' in new_token:
                                account.refresh_token = new_token['refresh_token']
                            db.commit()
                            return new_token['access_token']
            
        except Exception as e:
            logger.error(f"获取用户访问令牌失败: {str(e)}")
            
        return None
    
    async def _get_any_user_token(self, db: Session, project: Project) -> Optional[str]:
        """从项目中任意已绑定用户获取访问令牌"""
        try:
            # 查询项目中所有绑定钉钉的用户
            query = select(ThirdPartyAccount).where(
                and_(
                    ThirdPartyAccount.platform == "dingtalk",
                    ThirdPartyAccount.access_token.isnot(None)
                )
            )
            result = db.execute(query)
            accounts = result.scalars().all()
            
            for account in accounts:
                if account.token_expires_at and datetime.now() < account.token_expires_at:
                    return account.access_token
                elif account.refresh_token:
                    # 尝试刷新令牌
                    new_token = await self._refresh_user_token(account.refresh_token)
                    if new_token:
                        account.access_token = new_token['access_token']
                        account.token_expires_at = datetime.now() + timedelta(seconds=new_token['expires_in'])
                        if 'refresh_token' in new_token:
                            account.refresh_token = new_token['refresh_token']
                        db.commit()
                        return new_token['access_token']
            
        except Exception as e:
            logger.error(f"获取任意用户访问令牌失败: {str(e)}")
            
        return None
    
    async def _refresh_user_token(self, refresh_token: str) -> Optional[Dict[str, Any]]:
        """刷新用户访问令牌"""
        try:
            async with httpx.AsyncClient() as client:
                url = "https://oapi.dingtalk.com/sns/token"
                data = {
                    "refresh_token": refresh_token
                }
                
                response = await client.post(url, json=data)
                if response.status_code == 200:
                    result = response.json()
                    if result.get("errcode") == 0:
                        return {
                            "access_token": result.get("access_token"),
                            "expires_in": result.get("expires_in", 7200),
                            "refresh_token": result.get("refresh_token")
                        }
                    else:
                        logger.error(f"刷新用户访问令牌失败: {result.get('errmsg')}")
                else:
                    logger.error(f"刷新用户访问令牌请求失败: HTTP {response.status_code}")
                    
        except Exception as e:
            logger.error(f"刷新用户访问令牌异常: {str(e)}")
            
        return None
    
    async def get_user_info(self, access_token: str, user_id: str = None) -> Optional[Dict[str, Any]]:
        """获取用户信息"""
        try:
            async with httpx.AsyncClient() as session:
                if user_id:
                    # 获取指定用户信息
                    url = f"https://oapi.dingtalk.com/topapi/v2/user/get"
                    params = {
                        "access_token": access_token
                    }
                    data = {
                        "userid": user_id
                    }
                    
                    response = await session.post(url, params=params, json=data)
                    if response.status_code == 200:
                        result = response.json()
                        if result.get("errcode") == 0:
                            return result.get("result")
                else:
                    # 获取当前用户信息
                    url = f"https://oapi.dingtalk.com/sns/getuserinfo"
                    params = {
                        "access_token": access_token
                    }
                    
                    response = await session.get(url, params=params)
                    if response.status_code == 200:
                        result = response.json()
                        if result.get("errcode") == 0:
                            return result
                                
        except Exception as e:
            logger.error(f"获取用户信息失败: {str(e)}")
            
        return None

# 全局令牌管理器实例
token_manager = DingTalkTokenManager() 