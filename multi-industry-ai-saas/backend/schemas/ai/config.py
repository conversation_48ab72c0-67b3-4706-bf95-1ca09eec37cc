#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
AI 配置 Schema
"""

from typing import Optional, List, Dict, Any
from datetime import datetime
import uuid
from pydantic import BaseModel, Field, validator, computed_field

# 导入模型Schema
from schemas.ai.model import AIModelCapabilities

class AIConfigBase(BaseModel):
    """AI 配置基础 Schema"""
    tenant_id: Optional[uuid.UUID] = Field(None, description="租户 ID，为空表示系统级配置")
    project_id: Optional[uuid.UUID] = Field(None, description="项目 ID，为空表示租户级配置")
    provider_id: uuid.UUID = Field(..., description="提供商 ID")
    model_id: uuid.UUID = Field(..., description="模型 ID")
    name: str = Field(..., description="配置名称")
    description: Optional[str] = Field(None, description="描述")
    api_key: str = Field(..., description="API 密钥，加密存储")
    api_endpoint: Optional[str] = Field(None, description="API 端点，可选，覆盖默认端点")
    proxy_url: Optional[str] = Field(None, description="代理 URL，可选")
    temperature: Optional[float] = Field(0.7, description="温度参数")
    max_tokens: Optional[int] = Field(None, description="最大 token 数")
    top_p: Optional[float] = Field(1.0, description="Top P 参数")
    frequency_penalty: Optional[float] = Field(0.0, description="频率惩罚")
    presence_penalty: Optional[float] = Field(0.0, description="存在惩罚")
    is_default: Optional[bool] = Field(False, description="是否为默认配置")
    priority: int = Field(0, description="路由优先级")
    weight: int = Field(1, description="负载均衡权重")
    status: str = Field("active", description="状态：active, inactive")
    config: Optional[Dict[str, Any]] = Field(None, description="其他配置参数")

class AIConfigCreate(AIConfigBase):
    """创建 AI 配置 Schema"""
    pass

class AIConfigUpdate(BaseModel):
    """更新 AI 配置 Schema"""
    provider_id: Optional[uuid.UUID] = Field(None, description="提供商 ID")
    model_id: Optional[uuid.UUID] = Field(None, description="模型 ID")
    name: Optional[str] = Field(None, description="配置名称")
    description: Optional[str] = Field(None, description="描述")
    api_key: Optional[str] = Field(None, description="API 密钥，加密存储")
    api_endpoint: Optional[str] = Field(None, description="API 端点，可选，覆盖默认端点")
    proxy_url: Optional[str] = Field(None, description="代理 URL，可选")
    temperature: Optional[float] = Field(None, description="温度参数")
    max_tokens: Optional[int] = Field(None, description="最大 token 数")
    top_p: Optional[float] = Field(None, description="Top P 参数")
    frequency_penalty: Optional[float] = Field(None, description="频率惩罚")
    presence_penalty: Optional[float] = Field(None, description="存在惩罚")
    is_default: Optional[bool] = Field(None, description="是否为默认配置")
    priority: Optional[int] = Field(None, description="路由优先级")
    weight: Optional[int] = Field(None, description="负载均衡权重")
    status: Optional[str] = Field(None, description="状态：active, inactive")
    config: Optional[Dict[str, Any]] = Field(None, description="其他配置参数")

# 添加嵌套的模型信息Schema
class AIModelInfo(BaseModel):
    """嵌套的AI模型信息"""
    id: uuid.UUID
    name: str
    display_name: Optional[str] = None
    description: Optional[str] = None
    model_type: str
    context_window: Optional[int] = None
    token_limit: Optional[int] = None
    capabilities: Optional[Dict[str, Any]] = None
    input_price_per_1k_tokens: Optional[float] = None
    output_price_per_1k_tokens: Optional[float] = None
    
    class Config:
        from_attributes = True

class AIProviderInfo(BaseModel):
    """嵌套的AI提供商信息"""
    id: uuid.UUID
    name: str
    display_name: Optional[str] = None
    description: Optional[str] = None
    
    class Config:
        from_attributes = True

class AIConfigInDB(AIConfigBase):
    """数据库中的 AI 配置 Schema"""
    id: uuid.UUID
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    # 关联数据 - 使用嵌套对象
    provider: Optional[AIProviderInfo] = None
    model: Optional[AIModelInfo] = None
    
    # 兼容性字段（从关联对象自动计算）
    @computed_field
    @property
    def provider_name(self) -> Optional[str]:
        return self.provider.name if self.provider else None
    
    @computed_field
    @property 
    def provider_display_name(self) -> Optional[str]:
        return self.provider.display_name if self.provider else None
    
    @computed_field
    @property
    def model_name(self) -> Optional[str]:
        return self.model.name if self.model else None
    
    @computed_field
    @property
    def model_display_name(self) -> Optional[str]:
        return self.model.display_name if self.model else None

    class Config:
        from_attributes = True

class AIConfigResponse(BaseModel):
    """AI 配置响应 Schema"""
    success: bool = True
    message: Optional[str] = None
    data: Optional[Dict[str, Any]] = None  # 使用灵活的字典类型而不是严格的模型

class AIConfigListResponse(BaseModel):
    """AI 配置列表响应 Schema"""
    success: bool = True
    message: Optional[str] = None
    data: List[AIConfigInDB] = []
    total: int = 0
    page: int = 1
    page_size: int = 20
