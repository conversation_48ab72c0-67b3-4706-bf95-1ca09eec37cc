# 服务商管理系统设计文档

## 1. 概述

服务商管理系统是 Retail AI SaaS 平台的核心组成部分，作为管理租户（客户）的主要入口。服务商管理系统允许服务商（平台运营方）管理平台上的租户、项目、套餐、插件等资源，并提供完整的运营和管理功能。

### 1.1 系统定位

- **角色定位**：服务商是 SaaS 平台的运营方，负责管理平台上的租户和项目
- **功能定位**：提供租户管理、项目管理、套餐配置、插件管理、系统设置等功能
- **用户定位**：面向服务商的管理员和运营人员

### 1.2 系统架构

服务商管理系统采用与租户系统相同的技术栈，但有独立的权限控制和数据访问层：

- **前端**：React + Ant Design
- **后端**：FastAPI + SQLAlchemy
- **数据库**：PostgreSQL
- **存储**：支持本地存储和云存储（S3、OSS等）
- **部署**：Docker + Docker Compose

## 2. 功能模块

### 2.1 首页

首页作为服务商管理系统的入口，提供系统概览和关键数据展示：

- **系统授权信息**：显示系统授权状态、有效期、版本等信息
- **实施概况数据**：展示平台实施进度、活跃项目数等数据
- **项目和用户关键数据**：展示项目总数、用户总数、活跃用户数等关键指标
- **项目排行和套餐排行**：展示热门项目和热门套餐的排行榜
- **产品动态信息**：展示产品更新、新功能发布等动态信息
- **更新日志信息**：展示系统更新日志

### 2.2 VIP 功能

VIP 功能模块提供高级功能配置，仅对购买"尊享版"的用户开放：

- **存储容量限制**：限制项目存储容量，配置增购容量收费方案
- **自提点限制**：限制项目自提点数量，配置增购自提点收费方案
- **项目员工限制**：限制项目员工数量，配置增购员工数量收费方案
- **项目商品限制**：限制项目商品数量，配置增购商品数量收费方案
- **项目管理**：设置后台员工可管理项目权限，进入项目后台协助运营
- **版权设置**：设置管理端版权信息
- **我的官网**：DIY 装修平台官网
- **平台样式**：调整平台样式和风格

### 2.3 用户管理

用户管理模块用于系统化管理平台注册用户（租户）：

- **用户管理**：创建和管理平台注册用户，查看用户信息，管理用户项目
- **待审核用户**：审核用户注册申请，查看用户审核记录
- **拒绝用户**：管理已拒绝申请注册用户，重新审核
- **用户分组**：根据平台运营模式管理用户分组
- **回收站**：管理已删除的用户，恢复用户

### 2.4 项目管理

项目管理模块用于系统化管理入驻平台的项目：

- **项目管理**：创建和管理入驻平台的项目，查看项目信息，配置项目功能
- **试用项目**：管理试用项目，查看试用项目信息，配置试用项目功能
- **待创建项目**：管理待创建项目，查看项目信息，联系项目继续完成创建项目操作
- **待审核项目**：审核创建项目申请，查看项目审核记录
- **拒绝项目**：管理已拒绝申请开店的项目，重新审核
- **回收站**：管理已删除的项目，恢复项目

### 2.5 平台管理

平台管理模块用于配置和管理平台的各种资源：

- **项目套餐**：设置项目套餐功能和套餐售卖价格，设置试用项目套餐功能
- **渠道管理**：设置各渠道基础信息和售卖价格，管理小程序版本，管理项目APP，配置第三方平台
- **营销管理**：设置营销活动基础信息和售卖价格，设置营销活动分组
- **插件管理**：设置插件工具基础信息和售卖价格，设置插件工具分组
  - **短信管理**：设置和管理"消息通知"插件中使用的项目相关短信
  - **电子面单**：设置"快递助手"插件中使用的电子面单接口
  - **项目助手管理**：发布和管理项目助手小程序
- **模板管理**：设置项目装修模板基础信息和售卖价格
- **AI管理**：设置项目里的AI模块
- **行业管理**：管理项目的行业

### 2.6 工具

工具模块提供各种辅助功能：

- **短信签名**：设置项目短信签名收费方案，审核独立申请短信签名申请，配置项目独立短信签名
- **优惠码**：添加和管理项目购买套餐或插件时可使用的优惠码，查看优惠码使用记录
- **系统公告**：添加和管理平台系统"最新公告"，业务端首页展示最新公告
- **小程序业务域名**：添加和管理小程序业务域名
- **平台官网**：设置平台官网样式和内容，快速搭建平台官网
- **平台广告**：添加和管理平台广告，业务端首页展示平台广告
- **独立存储**：设置项目的独立存储，合理分配存储空间
- **数据管理**：SQL数据调试，管理数据缓存

### 2.7 订单管理

订单管理模块用于查看和管理平台项目在购买套餐、续费，购买插件时生成的订单。

### 2.8 设置

设置模块提供系统配置和管理功能：

- **系统信息**：查看系统授权详情和系统更新日志，更新系统为最新版本以及修复和回滚系统
- **基础设置**：设置"业务端"、"手机端"、"管理端"、"网站"相关信息
- **用户设置**：设置用户注册登录业务端相关信息和登录限制
- **项目设置**：设置用户创建项目场景相关设置，包括创建项目基础设置和主营类目设置
- **通知设置**：设置用户通知，平台通知，短信接口和邮件接口
  - **用户通知**："用户注册短信"，"找回密码短信"，"套餐到期提醒短信"，"用户注册邮件"，"找回密码邮件"
  - **平台通知**：用户注册提醒短信内容和短信接收员工
- **附件设置**：设置入驻平台的项目使用的图片、音频、视频的格式类型和大小等信息
- **存储设置**：设置入住平台的项目数据存储方式
- **支付设置**：设置入住平台的项目购买项目套餐、续费或购买插件场景的支付配置
- **联系我们**：设置平台联系人信息。可分别设置业务端项目首页，功能模块，套餐购买页面显示的联系人信息
- **员工设置**：添加和管理管理端员工信息和每个员工的权限
- **角色权限**：添加和管理端员工角色的权限配置
- **操作日志**：查看管理端员工的操作日志
