.assistant-chat-container {
  height: calc(100vh - 64px - 48px);
  display: flex;
  flex-direction: column;
}

.chat-card {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.chat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 0 16px 0;
  border-bottom: 1px solid #f0f0f0;
}

.chat-body {
  flex: 1;
  display: flex;
  overflow: hidden;
  position: relative;
}

.threads-sidebar {
  width: 250px;
  border-right: 1px solid #f0f0f0;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.threads-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
}

.thread-item {
  cursor: pointer;
  padding: 8px 16px;
  transition: background-color 0.3s;
}

.thread-item:hover {
  background-color: #f5f5f5;
}

.thread-item.active {
  background-color: #e6f7ff;
}

.thread-title {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 180px;
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  display: flex;
  flex-direction: column;
}

.messages-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.message-item {
  display: flex;
  margin-bottom: 16px;
}

.user-message {
  flex-direction: row-reverse;
}

.message-avatar {
  margin: 0 12px;
}

.message-content {
  max-width: 70%;
  border-radius: 8px;
  padding: 12px;
  background-color: #f5f5f5;
}

.user-message .message-content {
  background-color: #e6f7ff;
}

.message-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.message-body {
  word-break: break-word;
}

.message-body p {
  margin-bottom: 8px;
}

.message-body p:last-child {
  margin-bottom: 0;
}

.message-body pre {
  background-color: #f0f0f0;
  padding: 8px;
  border-radius: 4px;
  overflow-x: auto;
}

.message-body code {
  background-color: #f0f0f0;
  padding: 2px 4px;
  border-radius: 4px;
}

.empty-chat {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  padding: 0 20%;
}

.chat-footer {
  padding: 16px 0 0 0;
  border-top: 1px solid #f0f0f0;
}

.input-container {
  display: flex;
  flex-direction: column;
}

.input-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 8px;
}

.selected-files {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 8px;
}

.selected-file {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  padding: 4px 8px;
  border-radius: 4px;
}

/* 响应式样式 */
@media (max-width: 768px) {
  .threads-sidebar {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    z-index: 10;
    background-color: white;
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);
  }
  
  .message-content {
    max-width: 85%;
  }
}
