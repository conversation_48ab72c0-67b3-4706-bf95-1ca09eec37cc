#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
为 sapi_mcp_servers 表添加 transport_type 列的迁移脚本
"""

import logging
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession

logger = logging.getLogger(__name__)

async def migrate(db: AsyncSession) -> bool:
    """
    为 sapi_mcp_servers 表添加 transport_type 列

    Args:
        db: 数据库会话

    Returns:
        bool: 迁移是否成功
    """
    try:
        # 检查表是否存在
        result = await db.execute(text("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables
                WHERE table_name = 'sapi_mcp_servers'
            );
        """))

        table_exists = result.scalar()

        if not table_exists:
            # 创建表
            await db.execute(text("""
                CREATE TABLE IF NOT EXISTS sapi_mcp_servers (
                    id UUID PRIMARY KEY,
                    name VARCHAR(100) NOT NULL,
                    description TEXT,
                    transport_type VARCHAR(20) DEFAULT 'streamhttp',
                    install_dir VARCHAR(200),
                    command VARCHAR(100),
                    args TEXT,
                    env TEXT,
                    simulation BOOLEAN DEFAULT FALSE,
                    use_system_ai BOOLEAN DEFAULT FALSE,
                    enabled BOOLEAN DEFAULT TRUE,
                    supports_media BOOLEAN DEFAULT TRUE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    created_by_id UUID
                );
            """))

            await db.commit()
            logger.info("成功创建 sapi_mcp_servers 表")
            return True

        # 检查 transport_type 列是否存在
        result = await db.execute(text("""
            SELECT EXISTS (
                SELECT FROM information_schema.columns
                WHERE table_name = 'sapi_mcp_servers' AND column_name = 'transport_type'
            );
        """))

        transport_type_exists = result.scalar()

        if not transport_type_exists:
            # 添加 transport_type 列
            await db.execute(text("""
                ALTER TABLE sapi_mcp_servers
                ADD COLUMN transport_type VARCHAR(20) DEFAULT 'streamhttp';
            """))

            await db.commit()
            logger.info("成功为 sapi_mcp_servers 表添加 transport_type 列")
        else:
            logger.info("sapi_mcp_servers 表已有 transport_type 列，无需添加")

        # 检查 install_dir 列是否存在
        result = await db.execute(text("""
            SELECT EXISTS (
                SELECT FROM information_schema.columns
                WHERE table_name = 'sapi_mcp_servers' AND column_name = 'install_dir'
            );
        """))

        install_dir_exists = result.scalar()

        if not install_dir_exists:
            # 添加 install_dir 列
            await db.execute(text("""
                ALTER TABLE sapi_mcp_servers
                ADD COLUMN install_dir VARCHAR(200);
            """))

            await db.commit()
            logger.info("成功为 sapi_mcp_servers 表添加 install_dir 列")
        else:
            logger.info("sapi_mcp_servers 表已有 install_dir 列，无需添加")

        # 检查 command 列是否存在
        result = await db.execute(text("""
            SELECT EXISTS (
                SELECT FROM information_schema.columns
                WHERE table_name = 'sapi_mcp_servers' AND column_name = 'command'
            );
        """))

        command_exists = result.scalar()

        if not command_exists:
            # 添加 command 列
            await db.execute(text("""
                ALTER TABLE sapi_mcp_servers
                ADD COLUMN command VARCHAR(100);
            """))

            await db.commit()
            logger.info("成功为 sapi_mcp_servers 表添加 command 列")
        else:
            logger.info("sapi_mcp_servers 表已有 command 列，无需添加")

        # 检查 args 列是否存在
        result = await db.execute(text("""
            SELECT EXISTS (
                SELECT FROM information_schema.columns
                WHERE table_name = 'sapi_mcp_servers' AND column_name = 'args'
            );
        """))

        args_exists = result.scalar()

        if not args_exists:
            # 添加 args 列
            await db.execute(text("""
                ALTER TABLE sapi_mcp_servers
                ADD COLUMN args TEXT;
            """))

            await db.commit()
            logger.info("成功为 sapi_mcp_servers 表添加 args 列")
        else:
            logger.info("sapi_mcp_servers 表已有 args 列，无需添加")

        # 检查 env 列是否存在
        result = await db.execute(text("""
            SELECT EXISTS (
                SELECT FROM information_schema.columns
                WHERE table_name = 'sapi_mcp_servers' AND column_name = 'env'
            );
        """))

        env_exists = result.scalar()

        if not env_exists:
            # 添加 env 列
            await db.execute(text("""
                ALTER TABLE sapi_mcp_servers
                ADD COLUMN env TEXT;
            """))

            await db.commit()
            logger.info("成功为 sapi_mcp_servers 表添加 env 列")
        else:
            logger.info("sapi_mcp_servers 表已有 env 列，无需添加")

        # 检查 simulation 列是否存在
        result = await db.execute(text("""
            SELECT EXISTS (
                SELECT FROM information_schema.columns
                WHERE table_name = 'sapi_mcp_servers' AND column_name = 'simulation'
            );
        """))

        simulation_exists = result.scalar()

        if not simulation_exists:
            # 添加 simulation 列
            await db.execute(text("""
                ALTER TABLE sapi_mcp_servers
                ADD COLUMN simulation BOOLEAN DEFAULT FALSE;
            """))

            await db.commit()
            logger.info("成功为 sapi_mcp_servers 表添加 simulation 列")
        else:
            logger.info("sapi_mcp_servers 表已有 simulation 列，无需添加")

        # 检查 use_system_ai 列是否存在
        result = await db.execute(text("""
            SELECT EXISTS (
                SELECT FROM information_schema.columns
                WHERE table_name = 'sapi_mcp_servers' AND column_name = 'use_system_ai'
            );
        """))

        use_system_ai_exists = result.scalar()

        if not use_system_ai_exists:
            # 添加 use_system_ai 列
            await db.execute(text("""
                ALTER TABLE sapi_mcp_servers
                ADD COLUMN use_system_ai BOOLEAN DEFAULT FALSE;
            """))

            await db.commit()
            logger.info("成功为 sapi_mcp_servers 表添加 use_system_ai 列")
        else:
            logger.info("sapi_mcp_servers 表已有 use_system_ai 列，无需添加")

        # 检查 enabled 列是否存在
        result = await db.execute(text("""
            SELECT EXISTS (
                SELECT FROM information_schema.columns
                WHERE table_name = 'sapi_mcp_servers' AND column_name = 'enabled'
            );
        """))

        enabled_exists = result.scalar()

        if not enabled_exists:
            # 添加 enabled 列
            await db.execute(text("""
                ALTER TABLE sapi_mcp_servers
                ADD COLUMN enabled BOOLEAN DEFAULT TRUE;
            """))

            await db.commit()
            logger.info("成功为 sapi_mcp_servers 表添加 enabled 列")
        else:
            logger.info("sapi_mcp_servers 表已有 enabled 列，无需添加")

        # 检查 supports_media 列是否存在
        result = await db.execute(text("""
            SELECT EXISTS (
                SELECT FROM information_schema.columns
                WHERE table_name = 'sapi_mcp_servers' AND column_name = 'supports_media'
            );
        """))

        supports_media_exists = result.scalar()

        if not supports_media_exists:
            # 添加 supports_media 列
            await db.execute(text("""
                ALTER TABLE sapi_mcp_servers
                ADD COLUMN supports_media BOOLEAN DEFAULT TRUE;
            """))

            await db.commit()
            logger.info("成功为 sapi_mcp_servers 表添加 supports_media 列")
        else:
            logger.info("sapi_mcp_servers 表已有 supports_media 列，无需添加")

        # 检查 created_at 列是否存在
        result = await db.execute(text("""
            SELECT EXISTS (
                SELECT FROM information_schema.columns
                WHERE table_name = 'sapi_mcp_servers' AND column_name = 'created_at'
            );
        """))

        created_at_exists = result.scalar()

        if not created_at_exists:
            # 添加 created_at 列
            await db.execute(text("""
                ALTER TABLE sapi_mcp_servers
                ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
            """))

            await db.commit()
            logger.info("成功为 sapi_mcp_servers 表添加 created_at 列")
        else:
            logger.info("sapi_mcp_servers 表已有 created_at 列，无需添加")

        # 检查 updated_at 列是否存在
        result = await db.execute(text("""
            SELECT EXISTS (
                SELECT FROM information_schema.columns
                WHERE table_name = 'sapi_mcp_servers' AND column_name = 'updated_at'
            );
        """))

        updated_at_exists = result.scalar()

        if not updated_at_exists:
            # 添加 updated_at 列
            await db.execute(text("""
                ALTER TABLE sapi_mcp_servers
                ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
            """))

            await db.commit()
            logger.info("成功为 sapi_mcp_servers 表添加 updated_at 列")
        else:
            logger.info("sapi_mcp_servers 表已有 updated_at 列，无需添加")

        # 检查 created_by_id 列是否存在
        result = await db.execute(text("""
            SELECT EXISTS (
                SELECT FROM information_schema.columns
                WHERE table_name = 'sapi_mcp_servers' AND column_name = 'created_by_id'
            );
        """))

        created_by_id_exists = result.scalar()

        if not created_by_id_exists:
            # 添加 created_by_id 列
            await db.execute(text("""
                ALTER TABLE sapi_mcp_servers
                ADD COLUMN created_by_id UUID;
            """))

            await db.commit()
            logger.info("成功为 sapi_mcp_servers 表添加 created_by_id 列")
        else:
            logger.info("sapi_mcp_servers 表已有 created_by_id 列，无需添加")

        return True

    except Exception as e:
        await db.rollback()
        logger.error(f"为 sapi_mcp_servers 表添加 transport_type 列失败: {str(e)}")
        import traceback
        traceback.print_exc()
        # 不重新抛出异常，允许应用程序继续启动
        # 但是记录错误，以便后续处理
        return False
