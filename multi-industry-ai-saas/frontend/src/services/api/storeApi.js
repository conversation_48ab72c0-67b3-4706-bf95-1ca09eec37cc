/**
 * 门店API服务
 */
const storeApi = {
  /**
   * 获取门店列表
   * @param {Object} params - 查询参数
   * @returns {Promise} - 门店列表
   */
  getStores: async (params = {}) => {
    try {
      const projectId = localStorage.getItem('project_id');
      if (!projectId) {
        return {
          success: false,
          message: '获取项目ID失败',
          data: []
        };
      }

      // 模拟门店数据
      const mockStores = [
        {
          id: 1,
          name: '总店',
          address: '北京市朝阳区建国路1号',
          phone: '010-12345678',
          status: 'active'
        },
        {
          id: 2,
          name: '分店A',
          address: '北京市海淀区中关村大街2号',
          phone: '010-87654321',
          status: 'active'
        },
        {
          id: 3,
          name: '分店B',
          address: '北京市西城区西单大街3号',
          phone: '010-11223344',
          status: 'active'
        }
      ];

      return {
        success: true,
        data: mockStores,
        message: '获取成功'
      };
    } catch (error) {
      console.error('获取门店列表失败:', error);
      return {
        success: false,
        data: [],
        message: error.message || '获取失败'
      };
    }
  },

  /**
   * 获取门店详情
   * @param {number} storeId - 门店ID
   * @returns {Promise} - 门店详情
   */
  getStoreDetail: async (storeId) => {
    try {
      const projectId = localStorage.getItem('project_id');
      if (!projectId) {
        return {
          success: false,
          message: '获取项目ID失败',
          data: null
        };
      }

      // 模拟门店详情
      const mockStore = {
        id: storeId,
        name: `门店${storeId}`,
        address: `北京市朝阳区示例地址${storeId}号`,
        phone: `010-1234567${storeId}`,
        status: 'active',
        manager: `店长${storeId}`,
        created_at: new Date().toISOString()
      };

      return {
        success: true,
        data: mockStore,
        message: '获取成功'
      };
    } catch (error) {
      console.error('获取门店详情失败:', error);
      return {
        success: false,
        data: null,
        message: error.message || '获取失败'
      };
    }
  }
};

export default storeApi; 