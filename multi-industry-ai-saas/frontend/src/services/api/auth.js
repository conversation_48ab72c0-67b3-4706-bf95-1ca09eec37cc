import api from './httpClient';

/**
 * 认证相关API服务
 */
const auth = {
  /**
   * 用户登录
   * @param {string} username - 用户名
   * @param {string} password - 密码
   * @returns {Promise} - 登录结果
   */
  login: async (username, password) => {
    const data = { username, password };
    console.log('Login request:', data);

    try {
      const response = await api.post('/auth/login', data);
      console.log('Login response:', response);
      return response;
    } catch (error) {
      console.error('Login error:', error.response?.data || error.message);
      throw error;
    }
  },

  /**
   * 用户注册
   * @param {Object} userData - 用户数据
   * @returns {Promise} - 注册结果
   */
  register: async (userData) => {
    return api.post('/auth/register', userData);
  },

  /**
   * 获取当前用户信息
   * @returns {Promise} - 用户信息
   */
  getCurrentUser: async () => {
    // 获取项目ID（如果存在）
    const projectId = localStorage.getItem('project_id');
    const params = projectId ? { project_id: projectId } : {};

    try {
      console.log('获取当前用户信息，参数:', params);
      const userData = await api.get('/auth/me', { params });
      console.log('获取到的用户信息:', userData);

      // 保存用户数据到localStorage
      localStorage.setItem('user_data', JSON.stringify(userData));
      console.log('保存用户数据到localStorage');

      // 如果返回的用户数据中有项目ID，保存到localStorage
      if (userData.project_id) {
        localStorage.setItem('project_id', userData.project_id);
        console.log('从用户数据中保存项目ID到localStorage:', userData.project_id);
      }

      return userData;
    } catch (error) {
      console.error('获取用户信息失败:', error);
      throw error;
    }
  },

  /**
   * 更新当前用户信息
   * @param {Object} data - 用户数据
   * @returns {Promise} - 更新结果
   */
  updateCurrentUser: async (data) => {
    return api.put('/auth/me', data);
  },

  /**
   * 退出登录
   * @returns {Promise} - 退出结果
   */
  logout: async () => {
    try {
      // 调用退出登录API
      await api.post('/auth/logout');

      // 清除本地存储的用户信息
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      localStorage.removeItem('tenant_id');
      localStorage.removeItem('project_id');
      localStorage.removeItem('service_provider_id');

      return { success: true };
    } catch (error) {
      console.error('Logout error:', error);

      // 即使API调用失败，也清除本地存储
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      localStorage.removeItem('tenant_id');
      localStorage.removeItem('project_id');
      localStorage.removeItem('service_provider_id');

      return { success: true };
    }
  },

  /**
   * 重置密码请求
   * @param {string} email - 用户邮箱
   * @returns {Promise} - 请求结果
   */
  requestPasswordReset: async (email) => {
    return api.post('/auth/request-password-reset', { email });
  },

  /**
   * 重置密码
   * @param {string} token - 重置令牌
   * @param {string} newPassword - 新密码
   * @returns {Promise} - 重置结果
   */
  resetPassword: async (token, newPassword) => {
    return api.post('/auth/reset-password', { token, new_password: newPassword });
  },

  /**
   * 修改密码
   * @param {string} oldPassword - 旧密码
   * @param {string} newPassword - 新密码
   * @returns {Promise} - 修改结果
   */
  changePassword: async (oldPassword, newPassword) => {
    return api.post('/auth/change-password', {
      old_password: oldPassword,
      new_password: newPassword
    });
  }
};

export default auth;
