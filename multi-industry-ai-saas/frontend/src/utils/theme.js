/**
 * 主题工具函数
 * 用于应用和管理系统主题设置
 */

// 应用主题设置
export const applyTheme = (themeSettings) => {
  if (!themeSettings) return;

  const { mode, primaryColor, borderRadius, compactMode, customFont, fontFamily } = themeSettings;

  // 应用主题模式
  applyThemeMode(mode);

  // 应用主题色
  applyPrimaryColor(primaryColor);

  // 应用圆角大小
  applyBorderRadius(borderRadius);

  // 应用紧凑模式
  applyCompactMode(compactMode);

  // 应用自定义字体
  if (customFont && fontFamily) {
    applyFontFamily(fontFamily);
  }

  // 保存主题设置到本地存储
  localStorage.setItem('themeSettings', JSON.stringify(themeSettings));
};

// 应用主题模式
const applyThemeMode = (mode) => {
  const body = document.body;
  const html = document.documentElement;

  // 移除现有的主题类
  body.classList.remove('theme-light', 'theme-dark');
  html.classList.remove('theme-light', 'theme-dark');

  if (mode === 'auto') {
    // 跟随系统
    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
    const themeClass = prefersDark ? 'theme-dark' : 'theme-light';
    body.classList.add(themeClass);
    html.classList.add(themeClass);

    // 监听系统主题变化
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    const themeChangeHandler = (e) => {
      body.classList.remove('theme-light', 'theme-dark');
      html.classList.remove('theme-light', 'theme-dark');
      const newThemeClass = e.matches ? 'theme-dark' : 'theme-light';
      body.classList.add(newThemeClass);
      html.classList.add(newThemeClass);
    };

    // 移除旧的监听器（如果存在）
    try {
      mediaQuery.removeEventListener('change', themeChangeHandler);
    } catch (e) {
      // 忽略错误
    }

    // 添加新的监听器
    mediaQuery.addEventListener('change', themeChangeHandler);
  } else {
    // 手动设置
    const themeClass = `theme-${mode}`;
    body.classList.add(themeClass);
    html.classList.add(themeClass);
  }
};

// 应用主题色
const applyPrimaryColor = (color) => {
  if (!color) return;

  // 设置CSS变量
  document.documentElement.style.setProperty('--primary-color', color);

  // 生成不同深浅的主题色
  const r = parseInt(color.slice(1, 3), 16);
  const g = parseInt(color.slice(3, 5), 16);
  const b = parseInt(color.slice(5, 7), 16);

  // 设置不同深浅的主题色
  for (let i = 1; i <= 9; i++) {
    const factor = i / 10;
    const lighter = `rgba(${r}, ${g}, ${b}, ${factor})`;
    document.documentElement.style.setProperty(`--primary-${i}`, lighter);
  }

  // 设置hover和active状态的颜色
  const darken = (r, g, b, amount) => {
    return `rgb(${Math.max(0, r - amount)}, ${Math.max(0, g - amount)}, ${Math.max(0, b - amount)})`;
  };

  document.documentElement.style.setProperty('--primary-color-hover', darken(r, g, b, 20));
  document.documentElement.style.setProperty('--primary-color-active', darken(r, g, b, 40));
};

// 应用圆角大小
const applyBorderRadius = (radius) => {
  if (radius === undefined) return;

  document.documentElement.style.setProperty('--border-radius', `${radius}px`);

  // 强制更新所有带圆角的元素
  const style = document.createElement('style');
  style.textContent = `
    .ant-btn, .ant-input, .ant-select-selector, .ant-card,
    .ant-modal-content, .ant-table-container, .ant-pagination-item,
    .ant-radio-button-wrapper, .ant-slider, .ant-switch {
      border-radius: ${radius}px !important;
    }
  `;

  // 移除旧的样式（如果存在）
  const oldStyle = document.getElementById('dynamic-border-radius');
  if (oldStyle) {
    oldStyle.remove();
  }

  // 添加新的样式
  style.id = 'dynamic-border-radius';
  document.head.appendChild(style);
};

// 应用紧凑模式
const applyCompactMode = (compact) => {
  const body = document.body;
  const html = document.documentElement;

  if (compact) {
    body.classList.add('compact-mode');
    html.classList.add('compact-mode');

    // 强制更新紧凑模式样式
    const style = document.createElement('style');
    style.textContent = `
      .compact-mode .ant-form-item {
        margin-bottom: 12px !important;
      }
      .compact-mode .ant-table-small {
        font-size: 12px !important;
      }
      .compact-mode .ant-btn {
        padding: 0 8px !important;
        height: 28px !important;
      }
      .compact-mode .ant-input {
        padding: 2px 8px !important;
      }
      .compact-mode .ant-form-item-label {
        padding-bottom: 4px !important;
      }
      .compact-mode .ant-select {
        font-size: 12px !important;
      }
    `;

    // 移除旧的样式（如果存在）
    const oldStyle = document.getElementById('dynamic-compact-mode');
    if (oldStyle) {
      oldStyle.remove();
    }

    // 添加新的样式
    style.id = 'dynamic-compact-mode';
    document.head.appendChild(style);
  } else {
    body.classList.remove('compact-mode');
    html.classList.remove('compact-mode');

    // 移除紧凑模式样式
    const oldStyle = document.getElementById('dynamic-compact-mode');
    if (oldStyle) {
      oldStyle.remove();
    }
  }
};

// 应用字体
const applyFontFamily = (fontFamily) => {
  const fontMap = {
    'default': '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
    'arial': 'Arial, sans-serif',
    'roboto': 'Roboto, sans-serif',
    'pingfang': 'PingFang SC, sans-serif',
    'microsoft': 'Microsoft YaHei, sans-serif'
  };

  const font = fontMap[fontFamily] || fontMap.default;
  document.documentElement.style.setProperty('--font-family', font);

  // 强制更新字体样式
  const style = document.createElement('style');
  style.textContent = `
    body, .ant-typography, .ant-form, .ant-table, .ant-modal, .ant-btn, .ant-input,
    .ant-select, .ant-select-dropdown, .ant-select-item, .ant-select-selection-item {
      font-family: ${font} !important;
    }
  `;

  // 移除旧的样式（如果存在）
  const oldStyle = document.getElementById('dynamic-font-family');
  if (oldStyle) {
    oldStyle.remove();
  }

  // 添加新的样式
  style.id = 'dynamic-font-family';
  document.head.appendChild(style);
};

// 初始化主题
export const initTheme = () => {
  // 从本地存储获取主题设置
  const savedTheme = localStorage.getItem('themeSettings');

  if (savedTheme) {
    try {
      const themeSettings = JSON.parse(savedTheme);
      applyTheme(themeSettings);
    } catch (error) {
      console.error('解析主题设置失败:', error);
      // 应用默认主题
      applyTheme({
        mode: 'light',
        primaryColor: '#1890ff',
        borderRadius: 4,
        compactMode: false,
        customFont: false,
        fontFamily: 'default'
      });
    }
  } else {
    // 应用默认主题
    applyTheme({
      mode: 'light',
      primaryColor: '#1890ff',
      borderRadius: 4,
      compactMode: false,
      customFont: false,
      fontFamily: 'default'
    });
  }
};
