import React, { useState, useEffect } from 'react';
import {
  Card, Table, Button, Select, DatePicker, Row, Col, Statistic,
  Tabs, Space, Form, Input, Modal, message, Spin, Empty, Tag, Tooltip,
  InputNumber, Checkbox
} from 'antd';
import {
  PlusOutlined, SyncOutlined, CheckCircleOutlined,
  CloseCircleOutlined, InfoCircleOutlined, SearchOutlined
} from '@ant-design/icons';
import dayjs from 'dayjs';
import apiService from '../../../../services/api';
import { getProjectId } from '../../../../services/api/httpClient';
import './styles.css';
import NewArrival from './NewArrival';

const { TabPane } = Tabs;
const { Option } = Select;
const { RangePicker } = DatePicker;

/**
 * 到货确认组件
 */
const ArrivalConfirmation = ({ stores, currentStoreId, onStoreChange, loading: storeLoading }) => {
  const [activeTab, setActiveTab] = useState('list');
  const [loading, setLoading] = useState(false);
  const [arrivalList, setArrivalList] = useState([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });
  const [filters, setFilters] = useState({
    arrival_type: 'all',
    date_range: [dayjs().startOf('month'), dayjs()]
  });
  const [modalVisible, setModalVisible] = useState(false);
  const [confirmModalVisible, setConfirmModalVisible] = useState(false);
  const [newArrivalVisible, setNewArrivalVisible] = useState(false);
  const [currentArrival, setCurrentArrival] = useState(null);
  const [form] = Form.useForm();
  const [costSummary, setCostSummary] = useState({
    daily: {
      market: 0,
      warehouse: 0,
      thirdParty: 0,
      total: 0
    },
    monthly: {
      market: 0,
      warehouse: 0,
      thirdParty: 0,
      total: 0
    }
  });

  // 状态变量 - 商品明细
  const [productDetails, setProductDetails] = useState([]);
  const [confirmForm] = Form.useForm();
  const [tableRefreshKey, setTableRefreshKey] = useState(0); // 用于强制表格重新渲染

  // 获取到货列表
  const fetchArrivalList = async (params = {}) => {
    if (!currentStoreId) {
      return;
    }

    setLoading(true);
    try {
      // 准备查询参数
      const queryParams = {
        ...params,
        store_id: currentStoreId
      };

      // 处理日期范围
      if (filters.date_range && filters.date_range.length === 2) {
        queryParams.start_date = filters.date_range[0].format('YYYY-MM-DD');
        queryParams.end_date = filters.date_range[1].format('YYYY-MM-DD');
      }

      // 处理到货类型
      if (filters.arrival_type && filters.arrival_type !== 'all') {
        queryParams.arrival_type = filters.arrival_type;
      }

      // 调用API获取到货列表
      const response = await apiService.project.store_operations.getArrivalList(queryParams);

      if (response && response.success) {
        setArrivalList(response.items || []);
        setPagination({
          ...pagination,
          current: params.page || pagination.current,
          total: response.total || 0
        });

        // 获取成本汇总
        fetchCostSummary();
      } else {
        message.error(response?.message || '获取到货列表失败');
      }
    } catch (error) {
      console.error('获取到货列表失败:', error);
      message.error('获取到货列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取成本汇总
  const fetchCostSummary = async () => {
    if (!currentStoreId) {
      return;
    }

    try {
      // 调用API获取成本汇总
      const response = await apiService.project.store_operations.getArrivalCostSummary({
        store_id: currentStoreId
      });

      if (response && response.success && response.data) {
        // 处理后端返回的数据
        const { daily, monthly } = response.data;

        // 转换字段名称以匹配前端组件
        const dailySummary = {
          market: daily.market || 0,
          warehouse: daily.warehouse || 0,
          thirdParty: daily.third_party || 0,
          total: daily.total || 0
        };

        const monthlySummary = {
          market: monthly.market || 0,
          warehouse: monthly.warehouse || 0,
          thirdParty: monthly.third_party || 0,
          total: monthly.total || 0
        };

        // 更新状态
        setCostSummary({
          daily: dailySummary,
          monthly: monthlySummary
        });
      }
    } catch (error) {
      console.error('获取成本汇总失败:', error);
      // 不显示错误消息，避免干扰用户
    }
  };

  // 处理表格变化
  const handleTableChange = (pagination, filters, sorter) => {
    fetchArrivalList({
      page: pagination.current,
      pageSize: pagination.pageSize,
      ...filters
    });
  };

  // 处理筛选变化
  const handleFilterChange = (key, value) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);

    // 重置分页
    setPagination({
      ...pagination,
      current: 1
    });

    fetchArrivalList({
      page: 1,
      pageSize: pagination.pageSize,
      ...newFilters
    });
  };

  // 处理确认到货
  const handleConfirm = (record) => {
    setCurrentArrival(record);

    // 获取商品明细
    fetchProductDetails(record.id);

    setConfirmModalVisible(true);
  };

  // 获取商品明细
  const fetchProductDetails = async (arrivalId) => {
    try {
      setLoading(true);

      // 调用API获取到货详情
      const response = await apiService.project.store_operations.getArrivalDetail(arrivalId);

      if (response && response.success && response.data) {
        const arrivalDetail = response.data;
        const items = arrivalDetail.items || [];

        // 设置商品明细
        setProductDetails(items);

        // 初始化表单数据
        const initialValues = {};
        items.forEach(item => {
          initialValues[`actual_quantity_${item.id}`] = item.actual_quantity;
          initialValues[`is_confirmed_${item.id}`] = item.is_confirmed;
        });

        confirmForm.setFieldsValue(initialValues);
      } else {
        message.error(response?.message || '获取到货详情失败');
      }
    } catch (error) {
      console.error('获取到货详情失败:', error);
      message.error('获取到货详情失败');
    } finally {
      setLoading(false);
    }
  };

  // 提交确认
  const handleConfirmSubmit = async () => {
    try {
      const values = await confirmForm.validateFields();
      setLoading(true);

      // 处理表单数据
      const confirmedItems = productDetails.map(item => {
        return {
          id: item.id,
          actual_quantity: values[`actual_quantity_${item.id}`],
          is_confirmed: values[`is_confirmed_${item.id}`]
        };
      });

      // 准备提交数据
      const confirmData = {
        items: confirmedItems,
        notes: values.notes
      };

      // 调用API确认到货
      const response = await apiService.project.store_operations.confirmArrival(
        currentArrival.id,
        confirmData
      );

      if (response && response.success) {
        message.success('确认到货成功');
        setConfirmModalVisible(false);
        fetchArrivalList();
      } else {
        message.error(response?.message || '确认到货失败');
      }
    } catch (error) {
      console.error('确认到货失败:', error);
      message.error('确认到货失败');
    } finally {
      setLoading(false);
    }
  };

  // 处理拒绝到货
  const handleReject = (record) => {
    Modal.confirm({
      title: '拒绝到货',
      content: '确定要拒绝此到货记录吗？',
      onOk: async () => {
        try {
          setLoading(true);

          // 调用API拒绝到货
          const response = await apiService.project.store_operations.rejectArrival(
            record.id
          );

          if (response && response.success) {
            message.success('拒绝到货成功');
            fetchArrivalList();
          } else {
            message.error(response?.message || '拒绝到货失败');
          }
        } catch (error) {
          console.error('拒绝到货失败:', error);
          message.error('拒绝到货失败');
        } finally {
          setLoading(false);
        }
      }
    });
  };

  // 处理新增到货
  const handleNewArrivalSuccess = () => {
    setNewArrivalVisible(false);
    fetchArrivalList();
  };

  // 初始化
  useEffect(() => {
    if (currentStoreId) {
      fetchArrivalList();
    }
  }, [currentStoreId]);

  // 表格列定义
  const columns = [
    {
      title: '到货单号',
      dataIndex: 'arrival_number',
      key: 'arrival_number',
    },
    {
      title: '到货类型',
      dataIndex: 'arrival_type',
      key: 'arrival_type',
      render: (text) => {
        const typeMap = {
          market: '市场采购',
          warehouse: '仓库配货',
          third_party: '三方配送'
        };
        return typeMap[text] || text;
      }
    },
    {
      title: '到货日期',
      dataIndex: 'arrival_date',
      key: 'arrival_date',
    },
    {
      title: '供应商',
      dataIndex: 'supplier_name',
      key: 'supplier_name',
    },
    {
      title: '总金额',
      dataIndex: 'total_amount',
      key: 'total_amount',
      render: (text) => `¥${text.toFixed(2)}`
    },
    {
      title: '商品数量',
      dataIndex: 'total_items',
      key: 'total_items',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (text) => {
        const statusMap = {
          pending: <Tag color="blue">待确认</Tag>,
          confirmed: <Tag color="green">已确认</Tag>,
          rejected: <Tag color="red">已拒绝</Tag>
        };
        return statusMap[text] || text;
      }
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="small">
          {record.status === 'pending' && (
            <>
              <Button
                type="link"
                size="small"
                icon={<CheckCircleOutlined />}
                onClick={() => handleConfirm(record)}
              >
                确认
              </Button>
              <Button
                type="link"
                size="small"
                danger
                icon={<CloseCircleOutlined />}
                onClick={() => handleReject(record)}
              >
                拒绝
              </Button>
            </>
          )}
          <Button
            type="link"
            size="small"
            icon={<InfoCircleOutlined />}
            onClick={() => {
              setCurrentArrival(record);
              setModalVisible(true);
            }}
          >
            详情
          </Button>
        </Space>
      ),
    },
  ];

  // 渲染搜索表单
  const renderSearchForm = () => {
    return (
      <div style={{ marginBottom: 16 }}>
        <Row gutter={16}>
          <Col span={6}>
            <Select
              placeholder="选择门店"
              style={{ width: '100%' }}
              value={currentStoreId}
              onChange={onStoreChange}
              loading={storeLoading}
            >
              {stores.map(store => (
                <Option key={store.id} value={store.id}>{store.name}</Option>
              ))}
            </Select>
          </Col>
          <Col span={6}>
            <Select
              placeholder="到货类型"
              style={{ width: '100%' }}
              value={filters.arrival_type}
              onChange={(value) => handleFilterChange('arrival_type', value)}
            >
              <Option value="all">全部类型</Option>
              <Option value="market">市场采购</Option>
              <Option value="warehouse">仓库配货</Option>
              <Option value="third_party">三方配送</Option>
            </Select>
          </Col>
          <Col span={8}>
            <RangePicker
              value={filters.date_range}
              onChange={(dates) => handleFilterChange('date_range', dates)}
              style={{ width: '100%' }}
            />
          </Col>
          <Col span={4}>
            <Space>
              <Button
                type="primary"
                icon={<SearchOutlined />}
                onClick={() => fetchArrivalList()}
              >
                搜索
              </Button>
              <Button
                icon={<SyncOutlined />}
                onClick={() => fetchArrivalList()}
              >
                刷新
              </Button>
            </Space>
          </Col>
        </Row>
      </div>
    );
  };

  // 渲染成本汇总
  const renderCostSummary = () => {
    // 创建自定义的TabBar，在其中添加按钮
    const renderTabBar = (props, DefaultTabBar) => (
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <DefaultTabBar {...props} style={{ flex: 1 }} />
        {currentStoreId && (
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setNewArrivalVisible(true)}
            style={{ marginLeft: '16px' }}
          >
            新增到货
          </Button>
        )}
      </div>
    );

    return (
      <div style={{ marginBottom: 16 }}>
        <Tabs defaultActiveKey="daily" renderTabBar={renderTabBar}>
          <TabPane tab="日成本汇总" key="daily">
            <Row gutter={16}>
              <Col span={6}>
                <Card>
                  <Statistic
                    title="市场采购"
                    value={costSummary.daily.market}
                    precision={2}
                    prefix="¥"
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card>
                  <Statistic
                    title="仓库配货"
                    value={costSummary.daily.warehouse}
                    precision={2}
                    prefix="¥"
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card>
                  <Statistic
                    title="三方配送"
                    value={costSummary.daily.thirdParty}
                    precision={2}
                    prefix="¥"
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card>
                  <Statistic
                    title="总成本"
                    value={costSummary.daily.total}
                    precision={2}
                    prefix="¥"
                    valueStyle={{ color: '#3f8600' }}
                  />
                </Card>
              </Col>
            </Row>
          </TabPane>
          <TabPane tab="月成本汇总" key="monthly">
            <Row gutter={16}>
              <Col span={6}>
                <Card>
                  <Statistic
                    title="市场采购"
                    value={costSummary.monthly.market}
                    precision={2}
                    prefix="¥"
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card>
                  <Statistic
                    title="仓库配货"
                    value={costSummary.monthly.warehouse}
                    precision={2}
                    prefix="¥"
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card>
                  <Statistic
                    title="三方配送"
                    value={costSummary.monthly.thirdParty}
                    precision={2}
                    prefix="¥"
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card>
                  <Statistic
                    title="总成本"
                    value={costSummary.monthly.total}
                    precision={2}
                    prefix="¥"
                    valueStyle={{ color: '#3f8600' }}
                  />
                </Card>
              </Col>
            </Row>
          </TabPane>
        </Tabs>
      </div>
    );
  };

  return (
    <div className="arrival-confirmation">
      {renderSearchForm()}

      {renderCostSummary()}

      <div style={{ marginBottom: '16px' }}>
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab="到货列表" key="list">
            <Table
              columns={columns}
              dataSource={arrivalList}
              rowKey="id"
              pagination={{
                ...pagination,
                showSizeChanger: true,
                showTotal: (total) => `共 ${total} 条记录`
              }}
              onChange={handleTableChange}
              loading={loading}
            />
          </TabPane>
        </Tabs>
      </div>

      {/* 到货详情模态框 */}
      <Modal
        title="到货详情"
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={[
          <Button key="back" onClick={() => setModalVisible(false)}>
            关闭
          </Button>
        ]}
        width={800}
      >
        {currentArrival && (
          <div>
            <p><strong>到货单号:</strong> {currentArrival.arrival_number}</p>
            <p><strong>到货类型:</strong> {
              {
                market: '市场采购',
                warehouse: '仓库配货',
                third_party: '三方配送'
              }[currentArrival.arrival_type]
            }</p>
            <p><strong>到货日期:</strong> {currentArrival.arrival_date}</p>
            <p><strong>供应商:</strong> {currentArrival.supplier_name}</p>
            <p><strong>总金额:</strong> ¥{currentArrival.total_amount.toFixed(2)}</p>
            <p><strong>商品数量:</strong> {currentArrival.total_items}</p>
            <p><strong>状态:</strong> {
              {
                pending: '待确认',
                confirmed: '已确认',
                rejected: '已拒绝'
              }[currentArrival.status]
            }</p>
            <p><strong>创建时间:</strong> {currentArrival.created_at}</p>

            <h3>商品明细</h3>
            <Spin spinning={loading}>
              <Button
                type="primary"
                size="small"
                style={{ marginBottom: 16 }}
                onClick={() => fetchProductDetails(currentArrival.id)}
              >
                加载商品明细
              </Button>
              <Table
                key={tableRefreshKey}
                columns={[
                  { title: '商品名称', dataIndex: 'product_name', key: 'product_name' },
                  { title: '规格', dataIndex: 'specification', key: 'specification' },
                  { title: '单位', dataIndex: 'unit', key: 'unit' },
                  { title: '应到数量', dataIndex: 'expected_quantity', key: 'expected_quantity' },
                  { title: '实收数量', dataIndex: 'actual_quantity', key: 'actual_quantity' },
                  { title: '单价', dataIndex: 'price', key: 'price', render: (text) => `¥${text.toFixed(2)}` },
                  { title: '金额', dataIndex: 'amount', key: 'amount', render: (text) => `¥${text.toFixed(2)}` },
                  {
                    title: '确认状态',
                    dataIndex: 'is_confirmed',
                    key: 'is_confirmed',
                    render: (text) => text ? <Tag color="green">已确认</Tag> : <Tag color="red">未确认</Tag>
                  }
                ]}
                dataSource={productDetails}
                rowKey="id"
                pagination={false}
              />
            </Spin>
          </div>
        )}
      </Modal>

      {/* 确认到货模态框 */}
      <Modal
        title="确认到货"
        open={confirmModalVisible}
        onOk={handleConfirmSubmit}
        onCancel={() => setConfirmModalVisible(false)}
        confirmLoading={loading}
        width={900}
        destroyOnClose={true}
      >
        {currentArrival && (
          <div>
            <p>请确认 <strong>{currentArrival.arrival_number}</strong> 的到货商品：</p>

            <Form form={confirmForm} layout="vertical">
              <div style={{ marginBottom: 16, textAlign: 'right' }}>
                <Space>
                  <Button
                    onClick={() => {
                      // 全选所有商品
                      const values = {};
                      productDetails.forEach(item => {
                        values[`is_confirmed_${item.id}`] = true;
                      });
                      confirmForm.setFieldsValue(values);
                    }}
                  >
                    全选
                  </Button>
                  <Button
                    onClick={() => {
                      // 全不选所有商品
                      const values = {};
                      productDetails.forEach(item => {
                        values[`is_confirmed_${item.id}`] = false;
                      });
                      confirmForm.setFieldsValue(values);
                    }}
                  >
                    全不选
                  </Button>
                  <Button
                    type="primary"
                    ghost
                    onClick={() => {
                      // 将所有实收数量设置为应到数量
                      const values = {};
                      productDetails.forEach(item => {
                        values[`actual_quantity_${item.id}`] = item.expected_quantity;
                      });
                      confirmForm.setFieldsValue(values);
                      // 强制表格重新渲染以更新总计
                      setTableRefreshKey(prev => prev + 1);
                    }}
                  >
                    全部实收=应到
                  </Button>
                </Space>
              </div>
              <Table
                key={tableRefreshKey}
                dataSource={productDetails}
                rowKey="id"
                pagination={false}
                bordered
                columns={[
                  {
                    title: '确认',
                    dataIndex: 'is_confirmed',
                    key: 'is_confirmed',
                    width: 80,
                    render: (_, record) => (
                      <Form.Item
                        name={`is_confirmed_${record.id}`}
                        valuePropName="checked"
                        style={{ marginBottom: 0 }}
                      >
                        <Checkbox />
                      </Form.Item>
                    )
                  },
                  {
                    title: '商品名称',
                    dataIndex: 'product_name',
                    key: 'product_name',
                  },
                  {
                    title: '规格',
                    dataIndex: 'specification',
                    key: 'specification',
                    width: 120,
                  },
                  {
                    title: '单位',
                    dataIndex: 'unit',
                    key: 'unit',
                    width: 80,
                  },
                  {
                    title: '应到数量',
                    dataIndex: 'expected_quantity',
                    key: 'expected_quantity',
                    width: 100,
                  },
                  {
                    title: '实收数量',
                    dataIndex: 'actual_quantity',
                    key: 'actual_quantity',
                    width: 120,
                    render: (_, record) => (
                      <Form.Item
                        name={`actual_quantity_${record.id}`}
                        rules={[{ required: true, message: '请输入实收数量' }]}
                        style={{ marginBottom: 0 }}
                      >
                        <InputNumber
                          min={0}
                          precision={0}
                          style={{ width: '100%' }}
                          onChange={(value) => {
                            // 更新表单中的实收数量
                            const formValues = confirmForm.getFieldsValue();
                            formValues[`actual_quantity_${record.id}`] = value;
                            confirmForm.setFieldsValue(formValues);
                          }}
                        />
                      </Form.Item>
                    )
                  },
                  {
                    title: '差异',
                    key: 'difference',
                    width: 100,
                    render: (_, record) => {
                      const actualQuantity = confirmForm.getFieldValue(`actual_quantity_${record.id}`);
                      const difference = actualQuantity - record.expected_quantity;

                      let color = '';
                      if (difference < 0) {
                        color = '#f5222d'; // 红色
                      } else if (difference > 0) {
                        color = '#52c41a'; // 绿色
                      }

                      return (
                        <span style={{ color }}>
                          {difference === 0 ? '-' : difference > 0 ? `+${difference}` : difference}
                        </span>
                      );
                    }
                  },
                  {
                    title: '单价',
                    dataIndex: 'price',
                    key: 'price',
                    width: 100,
                    render: (text) => `¥${text.toFixed(2)}`
                  },
                  {
                    title: '金额',
                    key: 'amount',
                    width: 120,
                    render: (_, record) => {
                      const actualQuantity = confirmForm.getFieldValue(`actual_quantity_${record.id}`);
                      const amount = actualQuantity * record.price;
                      return `¥${amount.toFixed(2)}`;
                    }
                  }
                ]}
                summary={pageData => {
                  let totalExpected = 0;
                  let totalActual = 0;
                  let totalAmount = 0;

                  pageData.forEach(item => {
                    totalExpected += item.expected_quantity;
                    const actualQuantity = confirmForm.getFieldValue(`actual_quantity_${item.id}`) || 0;
                    totalActual += actualQuantity;
                    totalAmount += actualQuantity * item.price;
                  });

                  const difference = totalActual - totalExpected;

                  return (
                    <Table.Summary.Row>
                      <Table.Summary.Cell index={0} colSpan={4}>总计</Table.Summary.Cell>
                      <Table.Summary.Cell index={1}>{totalExpected}</Table.Summary.Cell>
                      <Table.Summary.Cell index={2}>{totalActual}</Table.Summary.Cell>
                      <Table.Summary.Cell index={3}>
                        <span style={{
                          color: difference < 0 ? '#f5222d' : (difference > 0 ? '#52c41a' : '')
                        }}>
                          {difference === 0 ? '-' : difference > 0 ? `+${difference}` : difference}
                        </span>
                      </Table.Summary.Cell>
                      <Table.Summary.Cell index={4}></Table.Summary.Cell>
                      <Table.Summary.Cell index={5}>¥{totalAmount.toFixed(2)}</Table.Summary.Cell>
                    </Table.Summary.Row>
                  );
                }}
              />

              <div style={{ marginTop: 16 }}>
                <Form.Item name="notes" label="备注">
                  <Input.TextArea rows={2} placeholder="请输入备注信息" />
                </Form.Item>
              </div>
            </Form>
          </div>
        )}
      </Modal>

      {/* 新增到货模态框 */}
      <NewArrival
        visible={newArrivalVisible}
        onCancel={() => setNewArrivalVisible(false)}
        onSuccess={handleNewArrivalSuccess}
        currentStoreId={currentStoreId}
      />
    </div>
  );
};

export default ArrivalConfirmation;
