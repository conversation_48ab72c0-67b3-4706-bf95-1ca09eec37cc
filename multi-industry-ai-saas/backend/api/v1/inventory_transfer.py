from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query, Path, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import func, and_, or_
import uuid
from datetime import datetime, timedelta

from db.database import get_db
from models.inventory import InventoryItem, InventoryHistory, InventoryTransfer, InventoryTransferItem
from models.product import Product
from models.warehouse import Warehouse
from models.user import User
from schemas.inventory import (
    InventoryTransferCreate, InventoryTransferUpdate, InventoryTransferResponse, InventoryTransferListResponse
)
from api.deps import get_current_user, get_current_project_id, get_current_tenant_id

router = APIRouter()

# 库存调拨单API
@router.get("/transfers", response_model=InventoryTransferListResponse)
async def get_inventory_transfers(
    source_warehouse_id: Optional[uuid.UUID] = None,
    target_warehouse_id: Optional[uuid.UUID] = None,
    status: Optional[str] = None,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    skip: int = 0,
    limit: int = 100,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    project_id: uuid.UUID = Depends(get_current_project_id)
):
    """
    获取库存调拨单列表
    """
    try:
        # 构建查询条件
        conditions = [
            InventoryTransfer.project_id == project_id
        ]

        if source_warehouse_id:
            conditions.append(InventoryTransfer.source_warehouse_id == source_warehouse_id)

        if target_warehouse_id:
            conditions.append(InventoryTransfer.target_warehouse_id == target_warehouse_id)

        if status:
            conditions.append(InventoryTransfer.status == status)

        if start_date:
            conditions.append(InventoryTransfer.transfer_date >= start_date)

        if end_date:
            conditions.append(InventoryTransfer.transfer_date <= end_date)

        # 构建查询
        query = (
            select(
                InventoryTransfer,
                Warehouse.name.label("source_warehouse_name")
            )
            .join(Warehouse, InventoryTransfer.source_warehouse_id == Warehouse.id)
            .where(and_(*conditions))
            .order_by(InventoryTransfer.created_at.desc())
            .offset(skip)
            .limit(limit)
        )

        # 执行查询
        result = await db.execute(query)
        transfers_with_source = result.all()

        # 获取目标仓库名称
        transfers = []
        for transfer, source_name in transfers_with_source:
            # 查询目标仓库名称
            target_query = select(Warehouse.name).where(Warehouse.id == transfer.target_warehouse_id)
            result = await db.execute(target_query)
            target_name = result.scalar_one_or_none()

            # 查询调拨单明细
            items_query = (
                select(
                    InventoryTransferItem,
                    Product.name.label("product_name"),
                    Product.sku.label("product_sku")
                )
                .join(Product, InventoryTransferItem.product_id == Product.id)
                .where(InventoryTransferItem.transfer_id == transfer.id)
            )
            result = await db.execute(items_query)
            items = result.all()

            # 构建调拨单明细列表
            transfer_items = []
            for item, product_name, product_sku in items:
                transfer_items.append({
                    **item.__dict__,
                    "product_name": product_name,
                    "product_sku": product_sku
                })

            # 构建调拨单数据
            transfer_dict = {
                **transfer.__dict__,
                "source_warehouse_name": source_name,
                "target_warehouse_name": target_name,
                "items": transfer_items
            }
            transfers.append(transfer_dict)

        # 获取总数
        count_query = select(func.count()).select_from(
            select(InventoryTransfer)
            .where(and_(*conditions))
            .subquery()
        )
        result = await db.execute(count_query)
        total = result.scalar_one()

        return {
            "success": True,
            "items": transfers,
            "total": total,
            "page": skip // limit + 1 if limit > 0 else 1,
            "size": limit,
            "pages": (total + limit - 1) // limit if limit > 0 else 1
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取库存调拨单列表失败: {str(e)}"
        )


@router.get("/transfers/{transfer_id}", response_model=InventoryTransferResponse)
async def get_inventory_transfer(
    transfer_id: uuid.UUID = Path(...),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    project_id: uuid.UUID = Depends(get_current_project_id)
):
    """
    获取单个库存调拨单详情
    """
    try:
        # 查询调拨单
        query = (
            select(
                InventoryTransfer,
                Warehouse.name.label("source_warehouse_name")
            )
            .join(Warehouse, InventoryTransfer.source_warehouse_id == Warehouse.id)
            .where(
                InventoryTransfer.id == transfer_id,
                InventoryTransfer.project_id == project_id
            )
        )

        result = await db.execute(query)
        transfer_with_source = result.first()

        if not transfer_with_source:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="库存调拨单不存在"
            )

        transfer, source_name = transfer_with_source

        # 查询目标仓库名称
        target_query = select(Warehouse.name).where(Warehouse.id == transfer.target_warehouse_id)
        result = await db.execute(target_query)
        target_name = result.scalar_one_or_none()

        # 查询调拨单明细
        items_query = (
            select(
                InventoryTransferItem,
                Product.name.label("product_name"),
                Product.sku.label("product_sku")
            )
            .join(Product, InventoryTransferItem.product_id == Product.id)
            .where(InventoryTransferItem.transfer_id == transfer.id)
        )
        result = await db.execute(items_query)
        items = result.all()

        # 构建调拨单明细列表
        transfer_items = []
        for item, product_name, product_sku in items:
            transfer_items.append({
                **item.__dict__,
                "product_name": product_name,
                "product_sku": product_sku
            })

        # 构建调拨单数据
        transfer_dict = {
            **transfer.__dict__,
            "source_warehouse_name": source_name,
            "target_warehouse_name": target_name,
            "items": transfer_items
        }

        return {
            "success": True,
            "data": transfer_dict
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取库存调拨单详情失败: {str(e)}"
        )


@router.post("/transfers", response_model=InventoryTransferResponse)
async def create_inventory_transfer(
    transfer: InventoryTransferCreate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    project_id: uuid.UUID = Depends(get_current_project_id),
    tenant_id: Optional[uuid.UUID] = Depends(get_current_tenant_id)
):
    """
    创建库存调拨单
    """
    try:
        # 检查源仓库和目标仓库是否存在
        source_query = select(Warehouse).where(
            Warehouse.id == transfer.source_warehouse_id,
            Warehouse.project_id == project_id
        )
        result = await db.execute(source_query)
        source_warehouse = result.scalar_one_or_none()

        if not source_warehouse:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="源仓库不存在"
            )

        target_query = select(Warehouse).where(
            Warehouse.id == transfer.target_warehouse_id,
            Warehouse.project_id == project_id
        )
        result = await db.execute(target_query)
        target_warehouse = result.scalar_one_or_none()

        if not target_warehouse:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="目标仓库不存在"
            )

        if transfer.source_warehouse_id == transfer.target_warehouse_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="源仓库和目标仓库不能相同"
            )

        # 检查产品是否存在，以及源仓库是否有足够的库存
        product_ids = [item.product_id for item in transfer.items]

        # 查询产品
        products_query = select(Product).where(
            Product.id.in_(product_ids),
            Product.project_id == project_id
        )
        result = await db.execute(products_query)
        products = result.scalars().all()

        if len(products) != len(product_ids):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="部分产品不存在"
            )

        # 查询源仓库库存
        inventory_query = select(InventoryItem).where(
            InventoryItem.product_id.in_(product_ids),
            InventoryItem.warehouse_id == transfer.source_warehouse_id,
            InventoryItem.project_id == project_id
        )
        result = await db.execute(inventory_query)
        inventory_items = {item.product_id: item for item in result.scalars().all()}

        # 检查库存是否足够
        insufficient_items = []
        for item in transfer.items:
            inventory = inventory_items.get(item.product_id)
            if not inventory or inventory.quantity < item.quantity:
                product = next((p for p in products if p.id == item.product_id), None)
                insufficient_items.append({
                    "product_id": str(item.product_id),
                    "product_name": product.name if product else "未知产品",
                    "required": item.quantity,
                    "available": inventory.quantity if inventory else 0
                })

        if insufficient_items:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"库存不足: {insufficient_items}"
            )

        # 生成调拨单号
        now = datetime.now()
        transfer_number = f"TR{now.strftime('%Y%m%d%H%M%S')}{str(uuid.uuid4())[:8]}"

        # 创建调拨单
        new_transfer = InventoryTransfer(
            id=uuid.uuid4(),
            tenant_id=tenant_id,
            project_id=project_id,
            transfer_number=transfer_number,
            source_warehouse_id=transfer.source_warehouse_id,
            target_warehouse_id=transfer.target_warehouse_id,
            status="draft",
            transfer_date=transfer.transfer_date,
            notes=transfer.notes,
            created_by=current_user.id,
            updated_by=current_user.id
        )

        db.add(new_transfer)
        await db.flush()

        # 创建调拨单明细
        for item in transfer.items:
            transfer_item = InventoryTransferItem(
                id=uuid.uuid4(),
                transfer_id=new_transfer.id,
                product_id=item.product_id,
                quantity=item.quantity,
                notes=item.notes
            )
            db.add(transfer_item)

        await db.commit()

        # 查询创建的调拨单
        query = (
            select(
                InventoryTransfer,
                Warehouse.name.label("source_warehouse_name")
            )
            .join(Warehouse, InventoryTransfer.source_warehouse_id == Warehouse.id)
            .where(InventoryTransfer.id == new_transfer.id)
        )

        result = await db.execute(query)
        transfer_with_source = result.first()

        transfer, source_name = transfer_with_source

        # 查询目标仓库名称
        target_query = select(Warehouse.name).where(Warehouse.id == transfer.target_warehouse_id)
        result = await db.execute(target_query)
        target_name = result.scalar_one_or_none()

        # 查询调拨单明细
        items_query = (
            select(
                InventoryTransferItem,
                Product.name.label("product_name"),
                Product.sku.label("product_sku")
            )
            .join(Product, InventoryTransferItem.product_id == Product.id)
            .where(InventoryTransferItem.transfer_id == transfer.id)
        )
        result = await db.execute(items_query)
        items = result.all()

        # 构建调拨单明细列表
        transfer_items = []
        for item, product_name, product_sku in items:
            transfer_items.append({
                **item.__dict__,
                "product_name": product_name,
                "product_sku": product_sku
            })

        # 构建调拨单数据
        transfer_dict = {
            **transfer.__dict__,
            "source_warehouse_name": source_name,
            "target_warehouse_name": target_name,
            "items": transfer_items
        }

        return {
            "success": True,
            "message": "库存调拨单创建成功",
            "data": transfer_dict
        }
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建库存调拨单失败: {str(e)}"
        )


@router.put("/transfers/{transfer_id}", response_model=InventoryTransferResponse)
async def update_inventory_transfer(
    transfer_id: uuid.UUID,
    transfer_update: InventoryTransferUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    project_id: uuid.UUID = Depends(get_current_project_id)
):
    """
    更新库存调拨单
    """
    try:
        # 查询调拨单
        query = select(InventoryTransfer).where(
            InventoryTransfer.id == transfer_id,
            InventoryTransfer.project_id == project_id
        )
        result = await db.execute(query)
        existing_transfer = result.scalar_one_or_none()

        if not existing_transfer:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="库存调拨单不存在"
            )

        # 只有草稿状态的调拨单可以更新
        if existing_transfer.status != "draft":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="只有草稿状态的调拨单可以更新"
            )

        # 更新字段
        update_data = transfer_update.dict(exclude_unset=True)

        for key, value in update_data.items():
            setattr(existing_transfer, key, value)

        existing_transfer.updated_by = current_user.id
        existing_transfer.updated_at = datetime.now()

        await db.commit()
        await db.refresh(existing_transfer)

        # 查询更新后的调拨单
        query = (
            select(
                InventoryTransfer,
                Warehouse.name.label("source_warehouse_name")
            )
            .join(Warehouse, InventoryTransfer.source_warehouse_id == Warehouse.id)
            .where(InventoryTransfer.id == transfer_id)
        )

        result = await db.execute(query)
        transfer_with_source = result.first()

        transfer, source_name = transfer_with_source

        # 查询目标仓库名称
        target_query = select(Warehouse.name).where(Warehouse.id == transfer.target_warehouse_id)
        result = await db.execute(target_query)
        target_name = result.scalar_one_or_none()

        # 查询调拨单明细
        items_query = (
            select(
                InventoryTransferItem,
                Product.name.label("product_name"),
                Product.sku.label("product_sku")
            )
            .join(Product, InventoryTransferItem.product_id == Product.id)
            .where(InventoryTransferItem.transfer_id == transfer.id)
        )
        result = await db.execute(items_query)
        items = result.all()

        # 构建调拨单明细列表
        transfer_items = []
        for item, product_name, product_sku in items:
            transfer_items.append({
                **item.__dict__,
                "product_name": product_name,
                "product_sku": product_sku
            })

        # 构建调拨单数据
        transfer_dict = {
            **transfer.__dict__,
            "source_warehouse_name": source_name,
            "target_warehouse_name": target_name,
            "items": transfer_items
        }

        return {
            "success": True,
            "message": "库存调拨单更新成功",
            "data": transfer_dict
        }
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新库存调拨单失败: {str(e)}"
        )


@router.post("/transfers/{transfer_id}/confirm", response_model=InventoryTransferResponse)
async def confirm_inventory_transfer(
    transfer_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    project_id: uuid.UUID = Depends(get_current_project_id),
    tenant_id: Optional[uuid.UUID] = Depends(get_current_tenant_id)
):
    """
    确认库存调拨单
    """
    try:
        # 查询调拨单
        query = select(InventoryTransfer).where(
            InventoryTransfer.id == transfer_id,
            InventoryTransfer.project_id == project_id
        )
        result = await db.execute(query)
        transfer = result.scalar_one_or_none()

        if not transfer:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="库存调拨单不存在"
            )

        # 只有草稿状态的调拨单可以确认
        if transfer.status != "draft":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="只有草稿状态的调拨单可以确认"
            )

        # 查询调拨单明细
        items_query = select(InventoryTransferItem).where(
            InventoryTransferItem.transfer_id == transfer_id
        )
        result = await db.execute(items_query)
        transfer_items = result.scalars().all()

        # 获取产品ID列表
        product_ids = [item.product_id for item in transfer_items]

        # 查询源仓库库存
        source_inventory_query = select(InventoryItem).where(
            InventoryItem.product_id.in_(product_ids),
            InventoryItem.warehouse_id == transfer.source_warehouse_id,
            InventoryItem.project_id == project_id
        )
        result = await db.execute(source_inventory_query)
        source_inventory = {item.product_id: item for item in result.scalars().all()}

        # 查询目标仓库库存
        target_inventory_query = select(InventoryItem).where(
            InventoryItem.product_id.in_(product_ids),
            InventoryItem.warehouse_id == transfer.target_warehouse_id,
            InventoryItem.project_id == project_id
        )
        result = await db.execute(target_inventory_query)
        target_inventory = {item.product_id: item for item in result.scalars().all()}

        # 检查库存是否足够
        insufficient_items = []
        for item in transfer_items:
            inventory = source_inventory.get(item.product_id)
            if not inventory or inventory.quantity < item.quantity:
                product_query = select(Product).where(Product.id == item.product_id)
                result = await db.execute(product_query)
                product = result.scalar_one_or_none()

                insufficient_items.append({
                    "product_id": str(item.product_id),
                    "product_name": product.name if product else "未知产品",
                    "required": item.quantity,
                    "available": inventory.quantity if inventory else 0
                })

        if insufficient_items:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"库存不足: {insufficient_items}"
            )

        # 更新调拨单状态
        transfer.status = "confirmed"
        transfer.updated_by = current_user.id
        transfer.updated_at = datetime.now()

        # 处理库存变更
        now = datetime.now()

        for item in transfer_items:
            # 减少源仓库库存
            source_item = source_inventory[item.product_id]
            old_source_quantity = source_item.quantity
            source_item.quantity -= item.quantity
            source_item.updated_by = current_user.id
            source_item.updated_at = now
            source_item.last_movement_date = now

            # 创建源仓库库存历史记录
            source_history = InventoryHistory(
                id=uuid.uuid4(),
                inventory_item_id=source_item.id,
                tenant_id=tenant_id,
                project_id=project_id,
                action_type="transfer_out",
                previous_quantity=old_source_quantity,
                new_quantity=source_item.quantity,
                change_amount=-item.quantity,
                reference_id=transfer.id,
                reference_type="inventory_transfer",
                notes=f"调拨至仓库: {transfer.target_warehouse_id}",
                created_by=current_user.id
            )
            db.add(source_history)

            # 增加或创建目标仓库库存
            target_item = target_inventory.get(item.product_id)

            if target_item:
                # 更新现有库存
                old_target_quantity = target_item.quantity
                target_item.quantity += item.quantity
                target_item.updated_by = current_user.id
                target_item.updated_at = now
                target_item.last_movement_date = now

                # 创建目标仓库库存历史记录
                target_history = InventoryHistory(
                    id=uuid.uuid4(),
                    inventory_item_id=target_item.id,
                    tenant_id=tenant_id,
                    project_id=project_id,
                    action_type="transfer_in",
                    previous_quantity=old_target_quantity,
                    new_quantity=target_item.quantity,
                    change_amount=item.quantity,
                    reference_id=transfer.id,
                    reference_type="inventory_transfer",
                    notes=f"从仓库调入: {transfer.source_warehouse_id}",
                    created_by=current_user.id
                )
                db.add(target_history)
            else:
                # 创建新库存记录
                new_target_item = InventoryItem(
                    id=uuid.uuid4(),
                    tenant_id=tenant_id,
                    project_id=project_id,
                    product_id=item.product_id,
                    warehouse_id=transfer.target_warehouse_id,
                    quantity=item.quantity,
                    min_quantity=source_item.min_quantity,
                    max_quantity=source_item.max_quantity,
                    is_slow_moving=source_item.is_slow_moving,
                    last_movement_date=now,
                    created_by=current_user.id,
                    updated_by=current_user.id
                )
                db.add(new_target_item)
                await db.flush()

                # 创建目标仓库库存历史记录
                target_history = InventoryHistory(
                    id=uuid.uuid4(),
                    inventory_item_id=new_target_item.id,
                    tenant_id=tenant_id,
                    project_id=project_id,
                    action_type="transfer_in",
                    previous_quantity=0,
                    new_quantity=item.quantity,
                    change_amount=item.quantity,
                    reference_id=transfer.id,
                    reference_type="inventory_transfer",
                    notes=f"从仓库调入: {transfer.source_warehouse_id}",
                    created_by=current_user.id
                )
                db.add(target_history)

        await db.commit()

        # 查询更新后的调拨单
        query = (
            select(
                InventoryTransfer,
                Warehouse.name.label("source_warehouse_name")
            )
            .join(Warehouse, InventoryTransfer.source_warehouse_id == Warehouse.id)
            .where(InventoryTransfer.id == transfer_id)
        )

        result = await db.execute(query)
        transfer_with_source = result.first()

        transfer, source_name = transfer_with_source

        # 查询目标仓库名称
        target_query = select(Warehouse.name).where(Warehouse.id == transfer.target_warehouse_id)
        result = await db.execute(target_query)
        target_name = result.scalar_one_or_none()

        # 查询调拨单明细
        items_query = (
            select(
                InventoryTransferItem,
                Product.name.label("product_name"),
                Product.sku.label("product_sku")
            )
            .join(Product, InventoryTransferItem.product_id == Product.id)
            .where(InventoryTransferItem.transfer_id == transfer.id)
        )
        result = await db.execute(items_query)
        items = result.all()

        # 构建调拨单明细列表
        transfer_items = []
        for item, product_name, product_sku in items:
            transfer_items.append({
                **item.__dict__,
                "product_name": product_name,
                "product_sku": product_sku
            })

        # 构建调拨单数据
        transfer_dict = {
            **transfer.__dict__,
            "source_warehouse_name": source_name,
            "target_warehouse_name": target_name,
            "items": transfer_items
        }

        return {
            "success": True,
            "message": "库存调拨单确认成功",
            "data": transfer_dict
        }
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"确认库存调拨单失败: {str(e)}"
        )


@router.post("/transfers/{transfer_id}/cancel", response_model=InventoryTransferResponse)
async def cancel_inventory_transfer(
    transfer_id: uuid.UUID,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    project_id: uuid.UUID = Depends(get_current_project_id)
):
    """
    取消库存调拨单
    """
    try:
        # 查询调拨单
        query = select(InventoryTransfer).where(
            InventoryTransfer.id == transfer_id,
            InventoryTransfer.project_id == project_id
        )
        result = await db.execute(query)
        transfer = result.scalar_one_or_none()

        if not transfer:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="库存调拨单不存在"
            )

        # 只有草稿状态的调拨单可以取消
        if transfer.status != "draft":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="只有草稿状态的调拨单可以取消"
            )

        # 更新调拨单状态
        transfer.status = "cancelled"
        transfer.updated_by = current_user.id
        transfer.updated_at = datetime.now()

        await db.commit()

        # 查询更新后的调拨单
        query = (
            select(
                InventoryTransfer,
                Warehouse.name.label("source_warehouse_name")
            )
            .join(Warehouse, InventoryTransfer.source_warehouse_id == Warehouse.id)
            .where(InventoryTransfer.id == transfer_id)
        )

        result = await db.execute(query)
        transfer_with_source = result.first()

        transfer, source_name = transfer_with_source

        # 查询目标仓库名称
        target_query = select(Warehouse.name).where(Warehouse.id == transfer.target_warehouse_id)
        result = await db.execute(target_query)
        target_name = result.scalar_one_or_none()

        # 查询调拨单明细
        items_query = (
            select(
                InventoryTransferItem,
                Product.name.label("product_name"),
                Product.sku.label("product_sku")
            )
            .join(Product, InventoryTransferItem.product_id == Product.id)
            .where(InventoryTransferItem.transfer_id == transfer.id)
        )
        result = await db.execute(items_query)
        items = result.all()

        # 构建调拨单明细列表
        transfer_items = []
        for item, product_name, product_sku in items:
            transfer_items.append({
                **item.__dict__,
                "product_name": product_name,
                "product_sku": product_sku
            })

        # 构建调拨单数据
        transfer_dict = {
            **transfer.__dict__,
            "source_warehouse_name": source_name,
            "target_warehouse_name": target_name,
            "items": transfer_items
        }

        return {
            "success": True,
            "message": "库存调拨单取消成功",
            "data": transfer_dict
        }
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"取消库存调拨单失败: {str(e)}"
        )
