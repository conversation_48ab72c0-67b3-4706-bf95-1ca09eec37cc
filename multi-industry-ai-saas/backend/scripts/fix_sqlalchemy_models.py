#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
修复SQLAlchemy模型中的循环引用问题

这个脚本用于修复SQLAlchemy模型中的循环引用问题，主要是通过修改关系定义的方式。
"""

import os
import re
import sys

def fix_relationship_in_file(file_path):
    """
    修复文件中的关系定义
    """
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 修复直接引用类的关系定义
    # 例如：relationship(Project, back_populates="xxx") -> relationship("Project", back_populates="xxx")
    pattern1 = r'relationship\(([A-Za-z0-9_]+),\s*'
    replacement1 = r'relationship("\1", '
    content = re.sub(pattern1, replacement1, content)
    
    # 修复backref，添加foreign_keys参数
    # 例如：relationship("Project", backref="xxx") -> relationship("Project", foreign_keys=[project_id], backref="xxx")
    pattern2 = r'relationship\("([A-Za-z0-9_]+)",\s*backref="([A-Za-z0-9_]+)"'
    
    # 查找外键列
    fk_pattern = r'([a-z_]+)_id\s*=\s*Column\([^)]+ForeignKey\("([a-z_]+)\.id"'
    fk_matches = re.findall(fk_pattern, content, re.IGNORECASE)
    
    # 替换backref
    for match in re.finditer(pattern2, content):
        class_name = match.group(1)
        backref_name = match.group(2)
        
        # 查找对应的外键列
        fk_col = None
        for col, table in fk_matches:
            if table.rstrip('s') == class_name.lower():
                fk_col = col + '_id'
                break
        
        if fk_col:
            replacement = 'relationship("{0}", foreign_keys=[{1}], backref="{2}"'.format(class_name, fk_col, backref_name)
            content = content.replace(match.group(0), replacement)
    
    # 写回文件
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("已修复文件: {0}".format(file_path))

def main():
    """
    主函数
    """
    # 获取models目录路径
    models_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'models')
    
    # 遍历models目录下的所有Python文件
    for filename in os.listdir(models_dir):
        if filename.endswith('.py') and filename != '__init__.py':
            file_path = os.path.join(models_dir, filename)
            fix_relationship_in_file(file_path)
    
    print("所有模型文件修复完成")

if __name__ == '__main__':
    main()
