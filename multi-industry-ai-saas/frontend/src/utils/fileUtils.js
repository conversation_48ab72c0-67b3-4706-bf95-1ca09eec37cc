import React from 'react';
import {
  FileOutlined,
  FileImageOutlined,
  FileWordOutlined,
  FileExcelOutlined,
  FilePdfOutlined,
  FileTextOutlined,
  FileZipOutlined,
  FileUnknownOutlined
} from '@ant-design/icons';

/**
 * 格式化字节大小为人类可读的格式
 * @param {number} bytes - 字节数
 * @param {number} decimals - 小数位数
 * @returns {string} 格式化后的大小
 */
export const formatBytes = (bytes, decimals = 2) => {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];

  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
};

/**
 * 获取文件扩展名
 * @param {string} filename - 文件名
 * @returns {string} 文件扩展名
 */
export const getFileExtension = (filename) => {
  return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2);
};

/**
 * 根据文件名获取对应的图标
 * @param {string} filename - 文件名
 * @returns {React.ReactNode} 文件图标
 */
export const getFileTypeIcon = (filename) => {
  const ext = getFileExtension(filename).toLowerCase();
  
  // 图片文件
  if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'].includes(ext)) {
    return <FileImageOutlined style={{ color: '#1890ff' }} />;
  }
  
  // Word文档
  if (['doc', 'docx', 'rtf', 'odt'].includes(ext)) {
    return <FileWordOutlined style={{ color: '#2b579a' }} />;
  }
  
  // Excel文档
  if (['xls', 'xlsx', 'csv'].includes(ext)) {
    return <FileExcelOutlined style={{ color: '#217346' }} />;
  }
  
  // PDF文档
  if (ext === 'pdf') {
    return <FilePdfOutlined style={{ color: '#f5222d' }} />;
  }
  
  // 文本文件
  if (['txt', 'md', 'json', 'xml', 'html', 'css', 'js'].includes(ext)) {
    return <FileTextOutlined style={{ color: '#722ed1' }} />;
  }
  
  // 压缩文件
  if (['zip', 'rar', '7z', 'tar', 'gz'].includes(ext)) {
    return <FileZipOutlined style={{ color: '#faad14' }} />;
  }
  
  // 默认文件图标
  return <FileOutlined style={{ color: '#8c8c8c' }} />;
};

/**
 * 检查文件是否可预览
 * @param {string} filename - 文件名
 * @returns {boolean} 是否可预览
 */
export const isPreviewable = (filename) => {
  const ext = getFileExtension(filename).toLowerCase();
  return ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'pdf'].includes(ext);
};

/**
 * 获取文件类型分类
 * @param {string} filename - 文件名
 * @returns {string} 文件类型分类
 */
export const getFileCategory = (filename) => {
  const ext = getFileExtension(filename).toLowerCase();
  
  // 图片文件
  if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg'].includes(ext)) {
    return 'image';
  }
  
  // 文档文件
  if (['doc', 'docx', 'pdf', 'txt', 'rtf', 'odt', 'xls', 'xlsx', 'ppt', 'pptx', 'md'].includes(ext)) {
    return 'document';
  }
  
  // 其他文件
  return 'other';
};
