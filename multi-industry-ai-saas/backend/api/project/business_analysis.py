#!/usr/bin/env python
# -*- coding: utf-8 -*-

import logging
logger = logging.getLogger(__name__)
logger.info("api/project/business_analysis.py loaded")

from fastapi import APIRouter, Depends, HTTPException, Query
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
import json
import uuid
from sqlalchemy import select, and_, or_, func

from db.database import get_db
from core.auth import get_current_user
from api.deps import get_current_project
from models.user import User
from models.project import Project
from models.product import Product, ProductCategory
from models.sales_management import SalesChannel
from models.ai import AIAssistant, AIAssistantThread, AIAssistantMessage
from schemas.business_analysis import (
    DataSourceCreate, DataSourceUpdate, DataSourceResponse,
    BusinessAnalysisResponse, AnalysisInsight
)

router = APIRouter()

# 模拟数据源存储（实际应该存储在数据库中）
DATA_SOURCES_STORAGE = {}

@router.get("/overview", response_model=Dict[str, Any])
async def get_business_overview(
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: Session = Depends(get_db)
):
    """获取经营分析概览数据"""
    try:
        # 获取商品数据
        product_result = await db.execute(
            select(func.count(Product.id)).where(Product.project_id == project.id)
        )
        total_products = product_result.scalar() or 0
        
        # 获取销售渠道数据
        channel_result = await db.execute(
            select(func.count(SalesChannel.id)).where(SalesChannel.project_id == project.id)
        )
        total_channels = channel_result.scalar() or 0
        
        # 获取AI助手数据
        ai_result = await db.execute(
            select(func.count(AIAssistant.id)).where(AIAssistant.project_id == project.id)
        )
        total_ai_assistants = ai_result.scalar() or 0
        
        # 模拟销售数据（实际应该从销售模块获取）
        mock_sales_data = {
            "total_revenue": 1250000,
            "revenue_trend": 12.5,
            "total_orders": 3420,
            "orders_trend": 8.3,
            "avg_order_value": 365.5,
            "aov_trend": 4.2
        }
        
        # 模拟客户数据（实际应该从客户模块获取）
        mock_customer_data = {
            "total_customers": 8650,
            "customer_trend": 15.7,
            "active_customers": 5420,
            "retention_rate": 62.7
        }
        
        return {
            "success": True,
            "data": {
                "revenue": mock_sales_data["total_revenue"],
                "revenueTrend": mock_sales_data["revenue_trend"],
                "orders": mock_sales_data["total_orders"],
                "ordersTrend": mock_sales_data["orders_trend"],
                "customers": mock_customer_data["total_customers"],
                "customerTrend": mock_customer_data["customer_trend"],
                "products": total_products,
                "channels": total_channels,
                "channelTrend": 5.2,
                "aiSuggestions": 12,
                "analysis_date": datetime.now().isoformat()
            }
        }
        
    except Exception as e:
        logger.error(f"获取经营分析概览失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取概览数据失败: {str(e)}")

@router.get("/ai-insights", response_model=Dict[str, Any])
async def get_ai_insights(
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: Session = Depends(get_db)
):
    """获取AI智能洞察"""
    try:
        # 模拟AI洞察数据
        insights = [
            {
                "id": "insight_1",
                "type": "opportunity",
                "priority": "high",
                "title": "商品定价优化机会",
                "content": "检测到5款商品的定价明显低于市场平均价格，建议适当调整定价策略。",
                "suggestion": "建议将这些商品价格上调8-12%，预计可增加15%的利润。",
                "created_at": datetime.now().isoformat()
            },
            {
                "id": "insight_2", 
                "type": "warning",
                "priority": "medium",
                "title": "客户流失风险预警",
                "content": "近30天内有23%的高价值客户活跃度下降，存在流失风险。",
                "suggestion": "建议启动客户关怀计划，通过个性化营销活动重新激活这些客户。",
                "created_at": datetime.now().isoformat()
            },
            {
                "id": "insight_3",
                "type": "suggestion", 
                "priority": "low",
                "title": "渠道效率提升建议",
                "content": "线上渠道的转化率比线下渠道高出35%，但流量分配不均。",
                "suggestion": "建议增加线上渠道的推广投入，优化流量分配策略。",
                "created_at": datetime.now().isoformat()
            }
        ]
        
        return {
            "success": True,
            "data": insights
        }
        
    except Exception as e:
        logger.error(f"获取AI洞察失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取AI洞察失败: {str(e)}")

@router.get("/product-analysis", response_model=Dict[str, Any])
async def get_product_analysis_detailed(
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: Session = Depends(get_db),
    category: Optional[str] = Query(None, description="商品分类"),
    keyword: Optional[str] = Query(None, description="关键词")
):
    """获取详细的商品分析数据"""
    try:
        # 获取商品数据
        query = select(Product).where(Product.project_id == project.id)
        if category and category != 'all':
            query = query.where(Product.category_id == category)
        if keyword:
            query = query.where(Product.name.contains(keyword))
        
        product_result = await db.execute(query)
        products = product_result.scalars().all()
        
        # 获取商品分类数据
        category_result = await db.execute(
            select(ProductCategory).where(ProductCategory.project_id == project.id)
        )
        categories = category_result.scalars().all()
        
        # 构建分析数据
        analysis_data = {
            "products": [
                {
                    "id": str(p.id),
                    "name": p.name,
                    "sku": p.sku or "",
                    "price": float(p.retail_price) if p.retail_price else 0,
                    "category": p.category.name if p.category else "未分类",
                    "status": "active" if p.is_active else "inactive",
                    "created_at": p.created_at.isoformat() if p.created_at else None
                }
                for p in products
            ],
            "competitiveness": [
                {
                    "id": str(p.id),
                    "name": p.name,
                    "sku": p.sku or "",
                    "competitivenessScore": min(95, 60 + (hash(str(p.id)) % 35)),
                    "marketPrice": float(p.retail_price) * 1.1 if p.retail_price else 0,
                    "ourPrice": float(p.retail_price) if p.retail_price else 0,
                    "salesTrend": (hash(str(p.id)) % 21) - 10,
                    "aiSuggestion": f"建议优化{p.name}的定价策略"
                }
                for p in products[:20]  # 限制数量
            ],
            "pricing": [
                {
                    "id": str(p.id),
                    "name": p.name,
                    "currentPrice": float(p.retail_price) if p.retail_price else 0,
                    "suggestedPrice": float(p.retail_price) * (0.95 + (hash(str(p.id)) % 10) / 100) if p.retail_price else 0,
                    "expectedImpact": {
                        "sales": (hash(str(p.id)) % 21) - 10,
                        "profit": (hash(str(p.id)) % 15) - 5
                    },
                    "reason": "基于市场分析和竞品对比"
                }
                for p in products[:15]
            ],
            "trends": {
                "data": [
                    {"date": f"2024-{i:02d}", "sales": 1000 + (i * 50) + (hash(f"trend_{i}") % 200)}
                    for i in range(1, 13)
                ]
            },
            "external": []
        }
        
        return {
            "success": True,
            "data": analysis_data
        }
        
    except Exception as e:
        logger.error(f"获取商品分析失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取商品分析失败: {str(e)}")

@router.get("/customer-analysis", response_model=Dict[str, Any])
async def get_customer_analysis_detailed(
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: Session = Depends(get_db),
    segment: Optional[str] = Query(None, description="客户分群"),
    keyword: Optional[str] = Query(None, description="关键词")
):
    """获取详细的客户分析数据"""
    try:
        # 从实际数据源获取客户分析数据
        
        # 获取用户数据（从第三方账号绑定表）
        user_query = select(func.count(User.id)).where(
            User.tenant_id == project.tenant_id
        )
        result = await db.execute(user_query)
        total_users = result.scalar() or 0
        
        # 获取钉钉绑定用户数据
        from models.user import ThirdPartyAccount
        dingtalk_users_query = select(func.count(ThirdPartyAccount.id)).where(
            and_(
                ThirdPartyAccount.platform == "dingtalk",
                ThirdPartyAccount.user_id.in_(
                    select(User.id).where(User.tenant_id == project.tenant_id)
                )
            )
        )
        result = await db.execute(dingtalk_users_query)
        dingtalk_users = result.scalar() or 0
        
        # 获取AI助手使用数据
        try:
            ai_usage_query = select(func.count(AIAssistantMessage.id)).where(
                AIAssistantMessage.thread_id.in_(
                    select(AIAssistantThread.id).where(
                        AIAssistantThread.assistant_id.in_(
                            select(AIAssistant.id).where(AIAssistant.project_id == project.id)
                        )
                    )
                )
            )
            result = await db.execute(ai_usage_query)
            ai_interactions = result.scalar() or 0
        except Exception as e:
            logger.warning(f"获取AI使用数据失败: {str(e)}")
            ai_interactions = 0
        
        # 构建基于真实数据的分析结果
        analysis_data = {
            "customers": [],
            "profile": {
                "customers": [],
                "ageStats": [],  # 需要从用户资料中获取
                "locationStats": []  # 需要从用户资料中获取
            },
            "behavior": {
                "trends": [],  # 需要从用户活动日志中获取
                "features": [
                    {"name": "钉钉绑定率", "score": int((dingtalk_users / max(total_users, 1)) * 100)},
                    {"name": "AI交互活跃度", "score": min(int((ai_interactions / max(total_users, 1)) * 10), 100)},
                    {"name": "系统使用频率", "score": 0},  # 需要从访问日志中计算
                    {"name": "功能使用深度", "score": 0}   # 需要从功能使用统计中计算
                ],
                "timePreferences": [],  # 需要从访问日志中分析
                "channelPreferences": []  # 需要从渠道使用数据中分析
            },
            "value": {
                "segments": [],  # 需要基于用户行为和贡献度分析
                "details": []
            },
            "segments": [],
            "summary": {
                "total_users": total_users,
                "dingtalk_users": dingtalk_users,
                "ai_interactions": ai_interactions,
                "active_users": 0,  # 需要从活动日志中计算
                "new_users_this_month": 0  # 需要从用户创建时间中计算
            }
        }
        
        return {
            "success": True,
            "data": analysis_data,
            "message": "客户分析数据基于实际系统数据生成，部分功能需要更多数据积累"
        }
        
    except Exception as e:
        logger.error(f"获取客户分析失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取客户分析失败: {str(e)}")

@router.get("/channel-analysis", response_model=Dict[str, Any])
async def get_channel_analysis(
    channelType: str = Query("all", description="渠道类型"),
    keyword: str = Query("", description="搜索关键词"),
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: Session = Depends(get_db)
):
    """获取渠道分析数据"""
    try:
        # 模拟渠道分析数据
        channels = [
            {
                "id": "channel_1",
                "name": "旗舰店",
                "type": "实体门店",
                "revenue": 680000,
                "efficiency": 85,
                "status": "excellent"
            },
            {
                "id": "channel_2", 
                "name": "天猫店铺",
                "type": "线上渠道",
                "revenue": 250000,
                "efficiency": 78,
                "status": "good"
            }
        ]
        
        stores = [
            {
                "id": "store_1",
                "store": {
                    "name": "北京旗舰店",
                    "address": "北京市朝阳区xxx路123号",
                    "area": 500,
                    "staff": 15
                },
                "status": "excellent",
                "revenue": 680000,
                "revenueGrowth": 12.5,
                "traffic": 1200,
                "conversionRate": 23.8,
                "efficiency": 85,
                "rating": 4.8
            }
        ]
        
        online = [
            {
                "id": "online_1",
                "platform": "天猫",
                "revenue": 250000,
                "growth": 18.3,
                "traffic": 50000,
                "conversion": 5.2
            }
        ]
        
        performance = [
            {"month": "2024-01", "value": 580000, "metric": "销售额"},
            {"month": "2024-02", "value": 620000, "metric": "销售额"},
            {"month": "2024-03", "value": 680000, "metric": "销售额"}
        ]
        
        comparison = [
            {"channel": "实体门店", "offline": 85, "online": 15},
            {"channel": "电商平台", "offline": 10, "online": 90}
        ]
        
        return {
            "success": True,
            "data": {
                "channels": channels,
                "stores": stores,
                "online": online,
                "performance": performance,
                "comparison": comparison
            }
        }
        
    except Exception as e:
        logger.error(f"获取渠道分析数据失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取渠道分析数据失败: {str(e)}")

@router.get("/datasources", response_model=Dict[str, Any])
async def get_datasources(
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: Session = Depends(get_db)
):
    """获取数据源信息"""
    try:
        # 模拟数据源信息
        datasources = [
            {
                "id": "ds_1",
                "name": "POS系统",
                "type": "实时数据",
                "status": "connected",
                "lastSync": datetime.now().isoformat(),
                "records": 125000
            },
            {
                "id": "ds_2",
                "name": "电商平台API",
                "type": "API接口",
                "status": "connected", 
                "lastSync": datetime.now().isoformat(),
                "records": 85000
            },
            {
                "id": "ds_3",
                "name": "会员系统",
                "type": "数据库",
                "status": "connected",
                "lastSync": datetime.now().isoformat(),
                "records": 45000
            }
        ]
        
        return {
            "success": True,
            "data": {
                "datasources": datasources,
                "total": len(datasources)
            }
        }
        
    except Exception as e:
        logger.error(f"获取数据源信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取数据源信息失败: {str(e)}")

@router.post("/datasources", response_model=Dict[str, Any])
async def create_datasource(
    datasource_data: Dict[str, Any],
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: Session = Depends(get_db)
):
    """创建数据源配置"""
    try:
        project_key = str(project.id)
        if project_key not in DATA_SOURCES_STORAGE:
            DATA_SOURCES_STORAGE[project_key] = {
                "api": [],
                "plugins": [],
                "ai": [],
                "external": []
            }
        
        # 添加ID和时间戳
        datasource_data["id"] = str(uuid.uuid4())
        datasource_data["created_at"] = datetime.now().isoformat()
        datasource_data["updated_at"] = datetime.now().isoformat()
        datasource_data["status"] = "active"
        
        # 根据类型添加到对应的列表
        source_type = datasource_data.get("type", "api")
        if source_type in DATA_SOURCES_STORAGE[project_key]:
            DATA_SOURCES_STORAGE[project_key][source_type].append(datasource_data)
        
        return {
            "success": True,
            "message": "数据源创建成功",
            "data": datasource_data
        }
        
    except Exception as e:
        logger.error(f"创建数据源失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"创建数据源失败: {str(e)}")

@router.put("/datasources/{datasource_id}", response_model=Dict[str, Any])
async def update_datasource(
    datasource_id: str,
    datasource_data: Dict[str, Any],
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: Session = Depends(get_db)
):
    """更新数据源配置"""
    try:
        project_key = str(project.id)
        if project_key not in DATA_SOURCES_STORAGE:
            raise HTTPException(status_code=404, detail="数据源不存在")
        
        # 查找并更新数据源
        updated = False
        for source_type in DATA_SOURCES_STORAGE[project_key]:
            for i, source in enumerate(DATA_SOURCES_STORAGE[project_key][source_type]):
                if source["id"] == datasource_id:
                    datasource_data["id"] = datasource_id
                    datasource_data["updated_at"] = datetime.now().isoformat()
                    datasource_data["created_at"] = source.get("created_at", datetime.now().isoformat())
                    DATA_SOURCES_STORAGE[project_key][source_type][i] = datasource_data
                    updated = True
                    break
            if updated:
                break
        
        if not updated:
            raise HTTPException(status_code=404, detail="数据源不存在")
        
        return {
            "success": True,
            "message": "数据源更新成功",
            "data": datasource_data
        }
        
    except Exception as e:
        logger.error(f"更新数据源失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"更新数据源失败: {str(e)}")

@router.delete("/datasources/{datasource_id}", response_model=Dict[str, Any])
async def delete_datasource(
    datasource_id: str,
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: Session = Depends(get_db)
):
    """删除数据源配置"""
    try:
        project_key = str(project.id)
        if project_key not in DATA_SOURCES_STORAGE:
            raise HTTPException(status_code=404, detail="数据源不存在")
        
        # 查找并删除数据源
        deleted = False
        for source_type in DATA_SOURCES_STORAGE[project_key]:
            for i, source in enumerate(DATA_SOURCES_STORAGE[project_key][source_type]):
                if source["id"] == datasource_id:
                    del DATA_SOURCES_STORAGE[project_key][source_type][i]
                    deleted = True
                    break
            if deleted:
                break
        
        if not deleted:
            raise HTTPException(status_code=404, detail="数据源不存在")
        
        return {
            "success": True,
            "message": "数据源删除成功"
        }
        
    except Exception as e:
        logger.error(f"删除数据源失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除数据源失败: {str(e)}")

@router.post("/datasources/{datasource_id}/test", response_model=Dict[str, Any])
async def test_datasource_connection(
    datasource_id: str,
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: Session = Depends(get_db)
):
    """测试数据源连接"""
    try:
        # 模拟连接测试
        return {
            "success": True,
            "message": "连接测试成功",
            "data": {
                "status": "connected",
                "response_time": "120ms",
                "last_test": datetime.now().isoformat()
            }
        }
        
    except Exception as e:
        logger.error(f"测试数据源连接失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"测试连接失败: {str(e)}")

@router.post("/datasources/{datasource_id}/sync", response_model=Dict[str, Any])
async def sync_datasource_data(
    datasource_id: str,
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: Session = Depends(get_db)
):
    """同步数据源数据"""
    try:
        # 模拟数据同步
        return {
            "success": True,
            "message": "数据同步已启动",
            "data": {
                "sync_id": str(uuid.uuid4()),
                "status": "running",
                "started_at": datetime.now().isoformat()
            }
        }
        
    except Exception as e:
        logger.error(f"同步数据源数据失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"数据同步失败: {str(e)}") 