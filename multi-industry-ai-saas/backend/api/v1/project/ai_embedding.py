#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
项目级 AI 嵌入 API
"""

import logging
import uuid
from typing import Optional, List, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query, Path, status
from sqlalchemy.ext.asyncio import AsyncSession

from db.database import get_db
from models.project import Project
from core.auth import get_current_user
from api.deps import get_current_project
from services.ai import AIEmbeddingService
from schemas.ai import (
    AIEmbeddingRequest,
    AIEmbeddingResponse,
    AIEmbeddingErrorResponse,
)

logger = logging.getLogger(__name__)
router = APIRouter()

@router.post("/ai/embeddings", response_model=AIEmbeddingResponse)
async def create_embedding(
    request: AIEmbeddingRequest,
    project: Project = Depends(get_current_project),
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    创建嵌入
    
    将文本转换为向量表示。
    """
    try:
        # 发送嵌入请求
        result = await AIEmbeddingService.create_embedding(
            db=db,
            project_id=project.id,
            user_id=current_user.id,
            input=request.input,
            model_id=request.model_id,
            config_id=request.config_id,
            dimensions=request.dimensions,
            user_data=request.user_data,
        )
        
        return result
    except ValueError as e:
        logger.error(f"AI 嵌入请求失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )
    except Exception as e:
        logger.error(f"AI 嵌入请求失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"AI 嵌入请求失败: {str(e)}",
        )
