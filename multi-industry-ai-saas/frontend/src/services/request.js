import axios from 'axios';

// 创建axios实例
const request = axios.create({
  baseURL: process.env.REACT_APP_API_URL || '/api',
  timeout: 30000,
});

// 请求拦截器
request.interceptors.request.use(
  (config) => {
    // 从localStorage获取token
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    
    // 从localStorage获取租户ID和项目ID
    const tenantId = localStorage.getItem('tenant_id');
    const projectId = localStorage.getItem('project_id');
    const serviceProviderId = localStorage.getItem('service_provider_id');

    // 如果有租户ID，添加到请求头
    if (tenantId) {
      config.headers['X-Tenant-ID'] = tenantId;
    }

    // 如果有项目ID，添加到请求头
    if (projectId) {
      config.headers['X-Project-ID'] = projectId;
    }

    // 如果有服务商ID，添加到请求头
    if (serviceProviderId) {
      config.headers['X-Service-Provider-ID'] = serviceProviderId;
    }
    
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
request.interceptors.response.use(
  (response) => {
    return response.data;
  },
  (error) => {
    if (error.response) {
      // 处理401未授权错误
      if (error.response.status === 401) {
        // 清除token并跳转到登录页
        localStorage.removeItem('token');
        window.location.href = '/login';
      }
      return Promise.reject(error.response.data);
    }
    return Promise.reject(error);
  }
);

export default request;
