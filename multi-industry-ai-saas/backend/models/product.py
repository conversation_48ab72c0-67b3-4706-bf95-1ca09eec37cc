#!/usr/bin/env python
# -*- coding: utf-8 -*-

from sqlalchemy import Column, String, Integer, Float, Text, Boolean, ForeignKey, DateTime, UniqueConstraint
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid

from db.database import Base

class ProductBrand(Base):
    """产品品牌模型"""
    __tablename__ = "product_brands"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=True)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id"), nullable=False)
    name = Column(String(100), nullable=False)
    description = Column(Text, nullable=True)
    logo_url = Column(String(255), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    created_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)
    updated_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)

    # 关系
    products = relationship("Product", back_populates="brand")
    tenant = relationship("Tenant", back_populates="product_brands")
    project = relationship("Project", back_populates="product_brands")

    __table_args__ = (
        UniqueConstraint('project_id', 'name', name='uq_brand_name_per_project'),
    )

    def __repr__(self):
        return f"<ProductBrand(id={self.id}, name='{self.name}')>"


class ProductCategory(Base):
    """产品分类模型"""
    __tablename__ = "product_categories"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=True)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id"), nullable=False)
    name = Column(String(100), nullable=False)
    parent_id = Column(UUID(as_uuid=True), ForeignKey("product_categories.id"), nullable=True)
    description = Column(Text, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    created_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)
    updated_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)

    # 关系
    products = relationship("Product", back_populates="category")
    parent = relationship("ProductCategory", remote_side=[id], backref="children")
    tenant = relationship("Tenant", back_populates="product_categories")
    project = relationship("Project", back_populates="product_categories")

    __table_args__ = (
        UniqueConstraint('project_id', 'name', 'parent_id', name='uq_category_name_per_parent_project'),
    )

    def __repr__(self):
        return f"<ProductCategory(id={self.id}, name='{self.name}')>"


class Product(Base):
    """产品模型 - 支持多规格架构"""
    __tablename__ = "products"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=True)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id"), nullable=False)
    
    # 商品分组：用于将同一商品的不同规格归类
    product_group_id = Column(UUID(as_uuid=True), nullable=True, comment="商品分组ID，同一商品的不同规格使用相同的group_id")
    
    # 基础信息
    name = Column(String(200), nullable=False, comment="商品名称")
    sku = Column(String(100), nullable=False, comment="商品SKU")
    barcode = Column(String(100), nullable=True, comment="条形码")
    
    # 分类和品牌
    brand_id = Column(UUID(as_uuid=True), ForeignKey("product_brands.id"), nullable=True)
    category_id = Column(UUID(as_uuid=True), ForeignKey("product_categories.id"), nullable=True)
    
    # 商品描述
    description = Column(Text, nullable=True, comment="商品描述")
    
    # 规格相关 - 增强多规格支持
    specification = Column(String(200), nullable=True, comment="规格描述")
    variant_attributes = Column(Text, nullable=True, comment="规格属性JSON，如: {\"weight\": \"1.5kg\", \"color\": \"红色\"}")
    
    # 单位和价格
    unit = Column(String(20), nullable=True, comment="计量单位")
    cost_price = Column(Float, nullable=True, comment="成本价")
    retail_price = Column(Float, nullable=True, comment="零售价")
    
    # 商品图片
    image_url = Column(String(255), nullable=True, comment="商品图片URL")
    
    # 状态
    is_active = Column(Boolean, default=True, comment="是否启用")
    is_variant = Column(Boolean, default=False, comment="是否为变体商品")
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    created_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)
    updated_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)

    # 关系
    brand = relationship("ProductBrand", back_populates="products")
    category = relationship("ProductCategory", back_populates="products")
    tenant = relationship("Tenant", back_populates="products")
    project = relationship("Project", back_populates="products")
    supplier_products = relationship("SupplierProduct", back_populates="product")
    inventory_items = relationship("InventoryItem", back_populates="product")

    __table_args__ = (
        UniqueConstraint('project_id', 'sku', name='uq_product_sku_per_project'),
        # 同一项目下，同一商品分组内的规格应该唯一
        UniqueConstraint('project_id', 'product_group_id', 'specification', name='uq_product_variant_per_group'),
    )

    @property
    def display_name(self):
        """显示名称：商品名称 + 规格"""
        if self.specification:
            return f"{self.name} ({self.specification})"
        return self.name
    
    @property
    def unique_key(self):
        """唯一标识：用于AI识别匹配"""
        if self.specification:
            return f"{self.name}|{self.specification}"
        return self.name

    def __repr__(self):
        return f"<Product(id={self.id}, name='{self.display_name}', sku='{self.sku}')>"
