#!/usr/bin/env python
# -*- coding: utf-8 -*-

import logging
import uuid
from typing import List, Optional, Dict, Any, Tuple
from datetime import datetime, date, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import or_, and_, func, desc, asc, text
from sqlalchemy.orm import joinedload

from models.finance import FinancialReconciliation, ReconciliationUpload
from models.store import Store
from models.sales_management import SalesChannel
from models.sales_report import SalesReport
from schemas.finance import FinancialReconciliationCreate, FinancialReconciliationUpdate

logger = logging.getLogger(__name__)

class FinanceService:
    @staticmethod
    async def create_reconciliation(
        db: AsyncSession,
        reconciliation_data: FinancialReconciliationCreate,
        project_id: uuid.UUID
    ) -> FinancialReconciliation:
        """创建财务对账记录"""
        # 计算差异
        sales_diff = reconciliation_data.reported_sales - reconciliation_data.system_sales
        recharge_diff = reconciliation_data.reported_recharge - reconciliation_data.system_recharge

        # 确定状态
        status = "normal"
        if abs(sales_diff) > reconciliation_data.reported_sales * 0.05 or abs(recharge_diff) > reconciliation_data.reported_recharge * 0.05:
            status = "error"
        elif abs(sales_diff) > reconciliation_data.reported_sales * 0.01 or abs(recharge_diff) > reconciliation_data.reported_recharge * 0.01:
            status = "warning"

        # 创建对账记录
        db_reconciliation = FinancialReconciliation(
            project_id=project_id,
            store_id=reconciliation_data.store_id,
            channel_id=reconciliation_data.channel_id,
            reconciliation_date=reconciliation_data.reconciliation_date,
            reconciliation_type=reconciliation_data.reconciliation_type,
            reported_sales=reconciliation_data.reported_sales,
            system_sales=reconciliation_data.system_sales,
            reported_recharge=reconciliation_data.reported_recharge,
            system_recharge=reconciliation_data.system_recharge,
            sales_diff=sales_diff,
            recharge_diff=recharge_diff,
            status=status,
            details=reconciliation_data.details,
            notes=reconciliation_data.notes
        )

        db.add(db_reconciliation)
        await db.commit()
        await db.refresh(db_reconciliation)

        return db_reconciliation

    @staticmethod
    async def get_reconciliations(
        db: AsyncSession,
        project_id: uuid.UUID,
        skip: int = 0,
        limit: int = 100,
        store_id: Optional[uuid.UUID] = None,
        channel_id: Optional[uuid.UUID] = None,
        reconciliation_type: Optional[str] = None,
        status: Optional[str] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        sort_by: str = "reconciliation_date",
        sort_order: str = "desc"
    ) -> List[FinancialReconciliation]:
        """获取财务对账列表"""
        query = select(FinancialReconciliation).where(FinancialReconciliation.project_id == project_id)

        # 应用过滤条件
        if store_id:
            query = query.where(FinancialReconciliation.store_id == store_id)

        if channel_id:
            query = query.where(FinancialReconciliation.channel_id == channel_id)

        if reconciliation_type:
            query = query.where(FinancialReconciliation.reconciliation_type == reconciliation_type)

        if status:
            query = query.where(FinancialReconciliation.status == status)

        if start_date:
            query = query.where(FinancialReconciliation.reconciliation_date >= start_date)

        if end_date:
            query = query.where(FinancialReconciliation.reconciliation_date <= end_date)

        # 应用排序
        if sort_order.lower() == "asc":
            query = query.order_by(asc(getattr(FinancialReconciliation, sort_by)))
        else:
            query = query.order_by(desc(getattr(FinancialReconciliation, sort_by)))

        # 应用分页
        query = query.offset(skip).limit(limit)

        # 加载关联数据
        query = query.options(
            joinedload(FinancialReconciliation.store),
            joinedload(FinancialReconciliation.channel)
        )

        result = await db.execute(query)
        return result.scalars().all()

    @staticmethod
    async def count_reconciliations(
        db: AsyncSession,
        project_id: uuid.UUID,
        store_id: Optional[uuid.UUID] = None,
        channel_id: Optional[uuid.UUID] = None,
        reconciliation_type: Optional[str] = None,
        status: Optional[str] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> int:
        """计算财务对账总数"""
        query = select(func.count(FinancialReconciliation.id)).where(FinancialReconciliation.project_id == project_id)

        # 应用过滤条件
        if store_id:
            query = query.where(FinancialReconciliation.store_id == store_id)

        if channel_id:
            query = query.where(FinancialReconciliation.channel_id == channel_id)

        if reconciliation_type:
            query = query.where(FinancialReconciliation.reconciliation_type == reconciliation_type)

        if status:
            query = query.where(FinancialReconciliation.status == status)

        if start_date:
            query = query.where(FinancialReconciliation.reconciliation_date >= start_date)

        if end_date:
            query = query.where(FinancialReconciliation.reconciliation_date <= end_date)

        result = await db.execute(query)
        return result.scalar_one()

    @staticmethod
    async def get_reconciliation_by_id(
        db: AsyncSession,
        reconciliation_id: uuid.UUID
    ) -> Optional[FinancialReconciliation]:
        """根据ID获取财务对账"""
        query = select(FinancialReconciliation).where(FinancialReconciliation.id == reconciliation_id)
        query = query.options(
            joinedload(FinancialReconciliation.store),
            joinedload(FinancialReconciliation.channel)
        )

        result = await db.execute(query)
        return result.scalar_one_or_none()

    @staticmethod
    async def update_reconciliation(
        db: AsyncSession,
        reconciliation_id: uuid.UUID,
        reconciliation_data: FinancialReconciliationUpdate
    ) -> Optional[FinancialReconciliation]:
        """更新财务对账"""
        query = select(FinancialReconciliation).where(FinancialReconciliation.id == reconciliation_id)
        result = await db.execute(query)
        db_reconciliation = result.scalar_one_or_none()

        if not db_reconciliation:
            return None

        # 更新财务对账信息
        update_data = reconciliation_data.dict(exclude_unset=True)

        for key, value in update_data.items():
            setattr(db_reconciliation, key, value)

        # 重新计算差异和状态
        if "reported_sales" in update_data or "system_sales" in update_data:
            db_reconciliation.sales_diff = db_reconciliation.reported_sales - db_reconciliation.system_sales

        if "reported_recharge" in update_data or "system_recharge" in update_data:
            db_reconciliation.recharge_diff = db_reconciliation.reported_recharge - db_reconciliation.system_recharge

        if "status" not in update_data:
            # 自动计算状态
            status = "normal"
            if abs(db_reconciliation.sales_diff) > db_reconciliation.reported_sales * 0.05 or abs(db_reconciliation.recharge_diff) > db_reconciliation.reported_recharge * 0.05:
                status = "error"
            elif abs(db_reconciliation.sales_diff) > db_reconciliation.reported_sales * 0.01 or abs(db_reconciliation.recharge_diff) > db_reconciliation.reported_recharge * 0.01:
                status = "warning"

            db_reconciliation.status = status

        db_reconciliation.updated_at = datetime.now()

        await db.commit()
        await db.refresh(db_reconciliation)

        return db_reconciliation

    @staticmethod
    async def delete_reconciliation(
        db: AsyncSession,
        reconciliation_id: uuid.UUID
    ) -> bool:
        """删除财务对账"""
        query = select(FinancialReconciliation).where(FinancialReconciliation.id == reconciliation_id)
        result = await db.execute(query)
        db_reconciliation = result.scalar_one_or_none()

        if not db_reconciliation:
            return False

        await db.delete(db_reconciliation)
        await db.commit()

        return True

    @staticmethod
    async def get_store_reconciliation_detail(
        db: AsyncSession,
        project_id: uuid.UUID,
        store_id: uuid.UUID,
        month: str
    ) -> Dict[str, Any]:
        """获取门店对账明细"""
        import random  # 添加random模块导入
        
        # 解析月份
        year, month_num = map(int, month.split('-'))
        start_date = datetime(year, month_num, 1)

        # 计算月末日期
        if month_num == 12:
            end_date = datetime(year + 1, 1, 1) - timedelta(days=1)
        else:
            end_date = datetime(year, month_num + 1, 1) - timedelta(days=1)

        end_date = datetime.combine(end_date, datetime.max.time())

        # 获取门店信息
        store_query = select(Store).where(Store.id == store_id)
        store_result = await db.execute(store_query)
        store = store_result.scalar_one_or_none()

        if not store:
            return None

        # 获取销售上报数据
        reports_query = select(SalesReport).where(
            SalesReport.project_id == project_id,
            SalesReport.store_id == store_id,
            SalesReport.report_date >= start_date,
            SalesReport.report_date <= end_date
        ).order_by(SalesReport.report_date)

        reports_result = await db.execute(reports_query)
        reports = reports_result.scalars().all()

        # 获取渠道列表
        channels_query = select(SalesChannel).where(SalesChannel.project_id == project_id)
        channels_result = await db.execute(channels_query)
        channels = channels_result.scalars().all()

        # 构建每日销售数据
        daily_sales = []
        channel_data = {}

        for report in reports:
            # 在实际项目中，这里应该从其他数据源获取系统记录的销售额和充值额
            # 这里为了演示，我们假设系统销售额与上报销售额有1%的随机差异
            system_sales = report.total_sales * (1 + random.uniform(-0.01, 0.01))
            system_recharge = report.total_recharge_amount * (1 + random.uniform(-0.01, 0.01)) if hasattr(report, 'total_recharge_amount') else 0

            sales_diff = report.total_sales - system_sales
            recharge_diff = (report.total_recharge_amount if hasattr(report, 'total_recharge_amount') else 0) - system_recharge

            status = "normal"
            if abs(sales_diff) > report.total_sales * 0.05 or abs(recharge_diff) > (report.total_recharge_amount if hasattr(report, 'total_recharge_amount') else 0) * 0.05:
                status = "error"
            elif abs(sales_diff) > report.total_sales * 0.01 or abs(recharge_diff) > (report.total_recharge_amount if hasattr(report, 'total_recharge_amount') else 0) * 0.01:
                status = "warning"

            daily_sales.append({
                "date": report.report_date.strftime("%Y-%m-%d"),
                "reported_sales": float(report.total_sales),
                "system_sales": float(system_sales),
                "sales_diff": float(sales_diff),
                "reported_recharge": float(report.total_recharge_amount if hasattr(report, 'total_recharge_amount') else 0),
                "system_recharge": float(system_recharge),
                "recharge_diff": float(recharge_diff),
                "status": status
            })

            # 处理渠道数据
            if hasattr(report, 'payment_methods') and report.payment_methods:
                for channel_name, amount in report.payment_methods.items():
                    if channel_name not in channel_data:
                        channel_data[channel_name] = {
                            "reported_amount": 0,
                            "system_amount": 0
                        }

                    channel_data[channel_name]["reported_amount"] += float(amount)
                    # 假设系统金额与上报金额有1%的随机差异
                    channel_data[channel_name]["system_amount"] += float(amount) * (1 + random.uniform(-0.01, 0.01))

        # 处理渠道销售数据
        sales_channels = []
        for channel_name, data in channel_data.items():
            diff = data["reported_amount"] - data["system_amount"]
            status = "normal"
            if abs(diff) > data["reported_amount"] * 0.05:
                status = "error"
            elif abs(diff) > data["reported_amount"] * 0.01:
                status = "warning"

            # 查找渠道ID
            channel_id = None
            channel_display_name = channel_name
            for channel in channels:
                # 添加None检查，避免NoneType错误
                channel_name_lower = channel_name.lower() if channel_name else ""
                channel_name_val = channel.name.lower() if channel.name else ""
                channel_code_val = channel.code.lower() if channel.code else ""
                if channel_name_lower and (channel_name_lower == channel_name_val or channel_name_lower == channel_code_val):
                    channel_id = str(channel.id)
                    channel_display_name = channel.name
                    break

            sales_channels.append({
                "channel_id": channel_id or channel_name,
                "channel_name": channel_display_name,
                "reported_amount": round(data["reported_amount"], 2),
                "system_amount": round(data["system_amount"], 2),
                "diff": round(diff, 2),
                "status": status
            })

        # 处理充值数据
        total_member_recharge = sum(float(report.member_recharge_amount if hasattr(report, 'member_recharge_amount') else 0) for report in reports)
        total_card_sales = sum(float(report.card_sales_amount if hasattr(report, 'card_sales_amount') else 0) for report in reports)

        # 同样，假设系统金额与上报金额有1%的随机差异
        system_member_recharge = total_member_recharge * (1 + random.uniform(-0.01, 0.01))
        system_card_sales = total_card_sales * (1 + random.uniform(-0.01, 0.01))

        member_recharge_diff = total_member_recharge - system_member_recharge
        card_sales_diff = total_card_sales - system_card_sales

        member_recharge_status = "normal"
        if abs(member_recharge_diff) > total_member_recharge * 0.05:
            member_recharge_status = "error"
        elif abs(member_recharge_diff) > total_member_recharge * 0.01:
            member_recharge_status = "warning"

        card_sales_status = "normal"
        if abs(card_sales_diff) > total_card_sales * 0.05:
            card_sales_status = "error"
        elif abs(card_sales_diff) > total_card_sales * 0.01:
            card_sales_status = "warning"

        recharge_data = {
            "member_recharge": {
                "reported": round(total_member_recharge, 2),
                "system": round(system_member_recharge, 2),
                "diff": round(member_recharge_diff, 2),
                "status": member_recharge_status
            },
            "card_sales": {
                "reported": round(total_card_sales, 2),
                "system": round(system_card_sales, 2),
                "diff": round(card_sales_diff, 2),
                "status": card_sales_status
            }
        }

        # 构建返回数据
        return {
            "store_id": store_id,
            "store_name": store.name,
            "month": month,
            "daily_sales": daily_sales,
            "sales_channels": sales_channels,
            "recharge_data": recharge_data
        }

    @staticmethod
    async def get_reconciliation_statistics(
        db: AsyncSession,
        project_id: uuid.UUID,
        month: str,
        store_id: Optional[uuid.UUID] = None
    ) -> Dict[str, Any]:
        """获取财务对账统计数据"""
        import random  # 添加random模块导入
        
        # 解析月份
        year, month_num = map(int, month.split('-'))
        start_date = datetime(year, month_num, 1)

        # 计算月末日期
        if month_num == 12:
            end_date = datetime(year + 1, 1, 1) - timedelta(days=1)
        else:
            end_date = datetime(year, month_num + 1, 1) - timedelta(days=1)

        end_date = datetime.combine(end_date, datetime.max.time())

        # 获取门店列表
        stores_query = select(Store).where(Store.project_id == project_id)
        if store_id:
            stores_query = stores_query.where(Store.id == store_id)

        stores_result = await db.execute(stores_query)
        stores = stores_result.scalars().all()

        # 获取渠道列表
        channels_query = select(SalesChannel).where(SalesChannel.project_id == project_id)
        channels_result = await db.execute(channels_query)
        channels = channels_result.scalars().all()

        # 获取销售上报数据
        reports_query = select(SalesReport).where(
            SalesReport.project_id == project_id,
            SalesReport.report_date >= start_date,
            SalesReport.report_date <= end_date
        )

        if store_id:
            reports_query = reports_query.where(SalesReport.store_id == store_id)

        reports_result = await db.execute(reports_query)
        reports = reports_result.scalars().all()

        # 处理门店对账数据
        stores_data = {}
        for store in stores:
            stores_data[str(store.id)] = {
                "id": str(store.id),
                "project_id": str(project_id),
                "store_id": str(store.id),
                "name": store.name,
                "store_name": store.name,  # 添加 store_name 字段，与前端期望一致
                "reported_sales": 0.0,
                "system_sales": 0.0,
                "reported_recharge": 0.0,
                "system_recharge": 0.0,
                "sales_diff": 0.0,
                "recharge_diff": 0.0,
                "status": "normal",
                "reconciliation_date": start_date,
                "reconciliation_type": "monthly",
                "created_at": datetime.now(),  # 使用当前时间
                "updated_at": datetime.now()   # 使用当前时间
            }

        # 处理渠道对账数据
        channels_data = {}
        for channel in channels:
            channels_data[str(channel.id)] = {
                "id": str(channel.id),
                "name": channel.name,
                "reported_amount": 0.0,
                "system_amount": 0.0,
                "diff": 0.0,
                "status": "normal"
            }

        # 累计销售数据
        for report in reports:
            store_id_str = str(report.store_id)
            if store_id_str in stores_data:
                # 累加销售额和充值额
                reported_sales = float(report.total_sales or 0)
                reported_recharge = float(report.total_recharge_amount if hasattr(report, 'total_recharge_amount') else 0)

                # 在实际项目中，这里应该从其他数据源获取系统记录的销售额和充值额
                # 这里为了演示，我们假设系统销售额与上报销售额有1%的随机差异
                system_sales = reported_sales * (1 + random.uniform(-0.01, 0.01))
                system_recharge = reported_recharge * (1 + random.uniform(-0.01, 0.01))

                stores_data[store_id_str]["reported_sales"] += reported_sales
                stores_data[store_id_str]["system_sales"] += system_sales
                stores_data[store_id_str]["reported_recharge"] += reported_recharge
                stores_data[store_id_str]["system_recharge"] += system_recharge

                # 处理渠道数据
                if hasattr(report, 'payment_methods') and report.payment_methods:
                    for channel_name, amount in report.payment_methods.items():
                        # 查找渠道ID
                        channel_id = None
                        for channel in channels:
                            # 添加None检查，避免NoneType错误
                            channel_name_lower = channel_name.lower() if channel_name else ""
                            channel_name_val = channel.name.lower() if channel.name else ""
                            channel_code_val = channel.code.lower() if channel.code else ""
                            if channel_name_lower and (channel_name_lower == channel_name_val or channel_name_lower == channel_code_val):
                                channel_id = str(channel.id)
                                break

                        if channel_id and channel_id in channels_data:
                            channels_data[channel_id]["reported_amount"] += float(amount)
                            # 假设系统金额与上报金额有1%的随机差异
                            channels_data[channel_id]["system_amount"] += float(amount) * (1 + random.uniform(-0.01, 0.01))

        # 计算差异和状态
        for store_id, data in stores_data.items():
            data["sales_diff"] = data["reported_sales"] - data["system_sales"]
            data["recharge_diff"] = data["reported_recharge"] - data["system_recharge"]

            # 计算状态
            if abs(data["sales_diff"]) > data["reported_sales"] * 0.05 or abs(data["recharge_diff"]) > data["reported_recharge"] * 0.05:
                data["status"] = "error"
            elif abs(data["sales_diff"]) > data["reported_sales"] * 0.01 or abs(data["recharge_diff"]) > data["reported_recharge"] * 0.01:
                data["status"] = "warning"

        for channel_id, data in channels_data.items():
            data["diff"] = data["reported_amount"] - data["system_amount"]

            # 计算状态
            if abs(data["diff"]) > data["reported_amount"] * 0.05:
                data["status"] = "error"
            elif abs(data["diff"]) > data["reported_amount"] * 0.01:
                data["status"] = "warning"

        # 计算渠道总计
        total_reported_sales = sum(data["reported_amount"] for data in channels_data.values())
        total_system_sales = sum(data["system_amount"] for data in channels_data.values())
        total_sales_diff = total_reported_sales - total_system_sales

        # 计算充值总计
        total_reported_recharge = sum(data["reported_recharge"] for data in stores_data.values())
        total_system_recharge = sum(data["system_recharge"] for data in stores_data.values())
        total_recharge_diff = total_reported_recharge - total_system_recharge

        # 计算平台状态
        platform_status = "normal"
        if abs(total_sales_diff) > total_reported_sales * 0.05 or abs(total_recharge_diff) > total_reported_recharge * 0.05:
            platform_status = "error"
        elif abs(total_sales_diff) > total_reported_sales * 0.01 or abs(total_recharge_diff) > total_reported_recharge * 0.01:
            platform_status = "warning"

        # 构建返回数据
        return {
            "stores": list(stores_data.values()),
            "platform_data": {
                "total_reported_sales": round(total_reported_sales, 2),
                "total_system_sales": round(total_system_sales, 2),
                "total_sales_diff": round(total_sales_diff, 2),
                "total_reported_recharge": round(total_reported_recharge, 2),
                "total_system_recharge": round(total_system_recharge, 2),
                "total_recharge_diff": round(total_recharge_diff, 2),
                "status": platform_status,
                "channels": [
                    {
                        "channel_id": data["id"],
                        "channel_name": data["name"],
                        "reported_amount": round(data["reported_amount"], 2),
                        "system_amount": round(data["system_amount"], 2),
                        "diff": round(data["diff"], 2),
                        "status": data["status"]
                    }
                    for data in channels_data.values() if data["reported_amount"] > 0
                ]
            }
        }
