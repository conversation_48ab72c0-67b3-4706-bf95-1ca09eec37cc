#!/usr/bin/env python
# -*- coding: utf-8 -*-

import uuid
from typing import Any, Dict
import logging
from abc import ABC, abstractmethod
from sqlalchemy.ext.asyncio import AsyncSession
from models.task import AsyncTask

logger = logging.getLogger(__name__)

class BaseTaskExecutor(ABC):
    def __init__(self, task: AsyncTask):
        self.task = task
        self.task_id = task.id
        self.tenant_id = task.tenant_id
        self.project_id = task.project_id
        self.user_id = task.user_id
        logger.info(f"BaseTaskExecutor initialized for task_id='{self.task_id}'")

    @abstractmethod
    async def execute(self, db: AsyncSession) -> Dict[str, Any]:
        """
        The main execution logic for the task.
        This method must be implemented by subclasses.
        It should perform the task and return a result dictionary.
        """
        raise NotImplementedError("The 'execute' method must be implemented by concrete subclasses.")

    # Keeping _execute_ai_processing as a potential helper method for subclasses,
    # but the primary entry point is now the `execute` method.
    async def _execute_ai_processing(self, *args: Any, **kwargs: Any) -> Any:
        logger.warning("'_execute_ai_processing' is a helper method and might not be suitable as a direct entry point.")
        raise NotImplementedError("'_execute_ai_processing' can be implemented by subclasses if needed.") 