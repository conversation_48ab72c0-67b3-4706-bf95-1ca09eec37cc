import React, { useState, useEffect } from 'react';
import { Form, Input, Button, Checkbox, Card, Typography, message, Divider, Alert } from 'antd';
import { UserOutlined, LockOutlined, WechatOutlined, DingdingOutlined } from '@ant-design/icons';
import { Link, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import httpClient from '../services/api/httpClient';
import './Login.css';

const { Title, Text } = Typography;

const Login = () => {
  const { login, loading } = useAuth();
  const [form] = Form.useForm();
  const location = useLocation();

  const [error, setError] = useState('');

  // 检查URL中是否有token参数（三方登录回调）
  useEffect(() => {
    const urlParams = new URLSearchParams(location.search);
    const token = urlParams.get('token');
    const loginError = urlParams.get('login_error');
    
    if (token) {
      // 如果有token，说明是三方登录回调，直接设置token并跳转
      localStorage.setItem('token', token);
      // 获取用户信息并跳转
      httpClient.get('/auth/me')
        .then(response => {
          message.success('登录成功');
          // 跳转到默认页面
          window.location.href = '/';
        })
        .catch(error => {
          console.error('获取用户信息失败:', error);
          message.error('登录失败，请重试');
          localStorage.removeItem('token');
        });
    } else if (loginError) {
      // 如果有登录错误，显示错误信息
      message.error(decodeURIComponent(loginError));
      // 清理URL中的错误参数
      const newUrl = window.location.pathname;
      window.history.replaceState({}, document.title, newUrl);
    }
  }, [location]);

  // 处理登录
  const handleLogin = async (values) => {
    try {
      setError('');
      console.log('普通登录尝试:', values.username, values.password);
      await login(values.username, values.password);
    } catch (error) {
      console.error('登录失败:', error);
      setError('登录失败，请检查用户名和密码');
    }
  };

  // 处理第三方登录
  const handleThirdPartyLogin = async (type) => {
    try {
      // 这里需要获取项目ID，可以从URL参数或者配置中获取
      // 暂时使用一个默认的项目ID，实际应用中需要根据业务逻辑获取
      const projectId = '93289212-7943-48ab-8092-e8eb7f663677'; // 您可以根据实际情况调整
      const currentUrl = window.location.href;
      
      let loginUrl = '';
      if (type === '钉钉') {
        loginUrl = `/auth/dingtalk/login?project_id=${projectId}&redirect_url=${encodeURIComponent(currentUrl)}`;
      } else if (type === '微信') {
        loginUrl = `/auth/wechat/login?project_id=${projectId}&redirect_url=${encodeURIComponent(currentUrl)}`;
      }
      
      if (loginUrl) {
        // 调用后端接口获取授权URL
        const response = await httpClient.get(loginUrl);
        if (response && response.auth_url) {
          // 重定向到三方登录页面
          window.location.href = response.auth_url;
        } else {
          message.error('获取登录链接失败');
        }
      }
    } catch (error) {
      console.error(`${type}登录失败:`, error);
      if (error.response && error.response.data && error.response.data.detail) {
        message.error(error.response.data.detail);
      } else {
        message.error(`${type}登录失败，请稍后重试`);
      }
    }
  };

  return (
    <div className="login-container">
      <div className="login-content">
        <div className="login-left">
          <div className="login-left-content">
            <Title level={2} className="login-slogan">AI驱动的零售管理平台</Title>
            <Text className="login-description">多场景智能应用，让线上线下一体化，AGI时代</Text>
          </div>
        </div>

        <Card className="login-card">
          <div className="login-header">
            <Title level={3} className="login-title">你好，欢迎登录</Title>
          </div>

          {error && (
            <Alert
              message={error}
              type="error"
              showIcon
              style={{ marginBottom: 24 }}
            />
          )}

          <Form
            form={form}
            name="login"
            initialValues={{ remember: true }}
            onFinish={handleLogin}
            size="large"
          >
            <Form.Item
              name="username"
              rules={[{ required: true, message: '请输入用户名' }]}
            >
              <Input prefix={<UserOutlined />} placeholder="登录账号" />
            </Form.Item>

            <Form.Item
              name="password"
              rules={[{ required: true, message: '请输入密码' }]}
            >
              <Input.Password prefix={<LockOutlined />} placeholder="登录密码" />
            </Form.Item>

            <Form.Item>
              <div className="login-options">
                <Form.Item name="remember" valuePropName="checked" noStyle>
                  <Checkbox>记住密码</Checkbox>
                </Form.Item>
                <Link to="/forgot-password" className="forgot-password">忘记密码？</Link>
              </div>
            </Form.Item>

            <Form.Item>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                className="login-button"
              >
                立即登录
              </Button>
            </Form.Item>
          </Form>

          <div className="other-login-options">
            <Text type="secondary">其他登录方式</Text>
            <div className="third-party-login">
              <Button
                icon={<WechatOutlined />}
                shape="circle"
                size="large"
                className="wechat-login-btn"
                onClick={() => handleThirdPartyLogin('微信')}
              />
              <Button
                icon={<DingdingOutlined />}
                shape="circle"
                size="large"
                className="dingtalk-login-btn"
                onClick={() => handleThirdPartyLogin('钉钉')}
              />
            </div>
          </div>

          <div className="login-footer">
            <Text type="secondary">没有账号？请联系管理员开通</Text>
            <div style={{ marginTop: 8 }}>
              <Link to="/admin/login">超级管理员登录</Link>
              <Divider type="vertical" />
              <Link to="/service-provider/login">服务商登录</Link>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default Login;
