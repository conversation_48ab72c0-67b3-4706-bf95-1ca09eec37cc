#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
OAuth2.1认证路由

提供OAuth2.1标准的认证端点，包括令牌颁发、刷新等功能
符合MCP最新草案及协议
"""

import os
import json
import logging
import uuid
import base64
import hashlib
import secrets
from datetime import datetime, timed<PERSON>ta
from typing import Dict, Any, Optional, List, Union

from fastapi import APIRouter, Depends, HTTPException, status, Request, Response, Form, Cookie, Query, Path
from fastapi.security import OAuth2PasswordRequestForm
from fastapi.responses import JSONResponse, RedirectResponse
from sqlalchemy import and_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from db.database import get_db
from models.user import User
from core.auth import get_current_user
from core.config import settings

from ..models import SAPIToken, MCPServer
from ..auth import (
    create_jwt_token, verify_jwt_token, TokenType, TokenPermission,
    JWT_ACCESS_TOKEN_EXPIRE_MINUTES, JWT_REFRESH_TOKEN_EXPIRE_DAYS, JWT_MCP_TOKEN_EXPIRE_HOURS
)
from .. import router

# 配置日志
logger = logging.getLogger(__name__)

# OAuth2.1配置
OAUTH2_AUTHORIZATION_ENDPOINT = "/api/v1/plugin/sapi/oauth/authorize"
OAUTH2_TOKEN_ENDPOINT = "/api/v1/plugin/sapi/oauth/token"
OAUTH2_METADATA_ENDPOINT = "/api/v1/plugin/sapi/oauth/.well-known/oauth-authorization-server"

# PKCE配置
PKCE_REQUIRED = True  # OAuth2.1要求所有客户端都使用PKCE

# 授权码存储（生产环境应使用Redis等持久化存储）
authorization_codes = {}

# 刷新令牌存储（生产环境应使用数据库存储）
refresh_tokens = {}

# 客户端注册信息（生产环境应使用数据库存储）
registered_clients = {}

# OAuth2.1元数据端点
@router.get("/oauth/.well-known/oauth-authorization-server", response_model=Dict[str, Any])
async def oauth_metadata():
    """OAuth2.1授权服务器元数据端点"""
    base_url = settings.API_BASE_URL or "http://localhost:8000"
    
    return {
        "issuer": base_url,
        "authorization_endpoint": f"{base_url}{OAUTH2_AUTHORIZATION_ENDPOINT}",
        "token_endpoint": f"{base_url}{OAUTH2_TOKEN_ENDPOINT}",
        "jwks_uri": f"{base_url}/api/v1/plugin/sapi/oauth/jwks.json",
        "registration_endpoint": f"{base_url}/api/v1/plugin/sapi/oauth/register",
        "scopes_supported": ["read", "write", "mcp"],
        "response_types_supported": ["code"],
        "response_modes_supported": ["query"],
        "grant_types_supported": ["authorization_code", "refresh_token", "client_credentials"],
        "token_endpoint_auth_methods_supported": ["client_secret_basic", "client_secret_post", "none"],
        "token_endpoint_auth_signing_alg_values_supported": ["RS256"],
        "service_documentation": f"{base_url}/docs",
        "ui_locales_supported": ["zh-CN", "en"],
        "op_policy_uri": f"{base_url}/privacy",
        "op_tos_uri": f"{base_url}/terms",
        "code_challenge_methods_supported": ["S256"],
        "pkce_required": PKCE_REQUIRED
    }

# 客户端注册端点
@router.post("/oauth/register", response_model=Dict[str, Any])
async def register_client(
    client_data: Dict[str, Any],
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """OAuth2.1客户端注册端点"""
    # 检查必要字段
    required_fields = ["client_name", "redirect_uris"]
    for field in required_fields:
        if field not in client_data:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"缺少必要字段: {field}"
            )
    
    # 生成客户端ID和密钥
    client_id = str(uuid.uuid4())
    client_secret = secrets.token_urlsafe(32)
    
    # 创建客户端注册信息
    client_info = {
        "client_id": client_id,
        "client_secret": client_secret,
        "client_name": client_data["client_name"],
        "redirect_uris": client_data["redirect_uris"],
        "grant_types": client_data.get("grant_types", ["authorization_code", "refresh_token"]),
        "response_types": client_data.get("response_types", ["code"]),
        "token_endpoint_auth_method": client_data.get("token_endpoint_auth_method", "client_secret_basic"),
        "scope": client_data.get("scope", "read write"),
        "client_uri": client_data.get("client_uri"),
        "logo_uri": client_data.get("logo_uri"),
        "contacts": client_data.get("contacts", []),
        "tos_uri": client_data.get("tos_uri"),
        "policy_uri": client_data.get("policy_uri"),
        "jwks_uri": client_data.get("jwks_uri"),
        "software_id": client_data.get("software_id"),
        "software_version": client_data.get("software_version"),
        "created_at": datetime.now().isoformat(),
        "created_by": str(current_user.id)
    }
    
    # 存储客户端注册信息
    registered_clients[client_id] = client_info
    
    # 返回客户端信息
    return {
        "client_id": client_id,
        "client_secret": client_secret,
        "client_id_issued_at": int(datetime.now().timestamp()),
        "client_secret_expires_at": 0,  # 永不过期
        **{k: v for k, v in client_info.items() if k not in ["client_id", "client_secret", "created_at", "created_by"]}
    }

# 授权端点
@router.get("/oauth/authorize", response_model=Dict[str, Any])
async def authorize(
    response_type: str = Query(...),
    client_id: str = Query(...),
    redirect_uri: str = Query(...),
    scope: str = Query(...),
    state: str = Query(None),
    code_challenge: str = Query(None),
    code_challenge_method: str = Query(None),
    project_id: str = Path(...),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """OAuth2.1授权端点"""
    # 检查响应类型
    if response_type != "code":
        return RedirectResponse(
            url=f"{redirect_uri}?error=unsupported_response_type&error_description=Only+code+response+type+is+supported&state={state}"
        )
    
    # 检查客户端ID
    if client_id not in registered_clients:
        return RedirectResponse(
            url=f"{redirect_uri}?error=invalid_client&error_description=Unknown+client&state={state}"
        )
    
    # 检查重定向URI
    client_info = registered_clients[client_id]
    if redirect_uri not in client_info["redirect_uris"]:
        return RedirectResponse(
            url=f"{redirect_uri}?error=invalid_request&error_description=Invalid+redirect_uri&state={state}"
        )
    
    # 检查PKCE
    if PKCE_REQUIRED and (not code_challenge or code_challenge_method != "S256"):
        return RedirectResponse(
            url=f"{redirect_uri}?error=invalid_request&error_description=PKCE+is+required&state={state}"
        )
    
    # 检查项目ID
    try:
        # 查询项目是否存在
        # 这里应该有项目模型的查询逻辑
        # 简化处理，假设项目ID有效
        pass
    except Exception as e:
        logger.error(f"查询项目失败: {str(e)}")
        return RedirectResponse(
            url=f"{redirect_uri}?error=server_error&error_description=Failed+to+query+project&state={state}"
        )
    
    # 生成授权码
    authorization_code = secrets.token_urlsafe(32)
    
    # 存储授权码信息
    authorization_codes[authorization_code] = {
        "client_id": client_id,
        "redirect_uri": redirect_uri,
        "scope": scope,
        "user_id": str(current_user.id),
        "project_id": project_id,
        "code_challenge": code_challenge,
        "code_challenge_method": code_challenge_method,
        "expires_at": datetime.now() + timedelta(minutes=10),  # 授权码10分钟有效
    }
    
    # 重定向回客户端
    return RedirectResponse(
        url=f"{redirect_uri}?code={authorization_code}&state={state}"
    )

# 令牌端点
@router.post("/oauth/token", response_model=Dict[str, Any])
async def token(
    request: Request,
    db: AsyncSession = Depends(get_db)
):
    """OAuth2.1令牌端点"""
    # 获取请求表单数据
    form_data = await request.form()
    grant_type = form_data.get("grant_type")
    
    # 根据授权类型处理
    if grant_type == "authorization_code":
        return await handle_authorization_code(form_data, db)
    elif grant_type == "refresh_token":
        return await handle_refresh_token(form_data, db)
    elif grant_type == "client_credentials":
        return await handle_client_credentials(form_data, db)
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"不支持的授权类型: {grant_type}"
        )

# 处理授权码授权
async def handle_authorization_code(form_data, db: AsyncSession):
    """处理授权码授权"""
    code = form_data.get("code")
    redirect_uri = form_data.get("redirect_uri")
    client_id = form_data.get("client_id")
    client_secret = form_data.get("client_secret")
    code_verifier = form_data.get("code_verifier")
    
    # 检查授权码
    if code not in authorization_codes:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="无效的授权码"
        )
    
    # 获取授权码信息
    code_info = authorization_codes[code]
    
    # 检查授权码是否过期
    if datetime.now() > code_info["expires_at"]:
        del authorization_codes[code]
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="授权码已过期"
        )
    
    # 检查客户端ID
    if client_id != code_info["client_id"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="客户端ID不匹配"
        )
    
    # 检查重定向URI
    if redirect_uri != code_info["redirect_uri"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="重定向URI不匹配"
        )
    
    # 检查PKCE
    if PKCE_REQUIRED and code_info.get("code_challenge"):
        if not code_verifier:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="缺少code_verifier"
            )
        
        # 计算code_verifier的哈希
        code_challenge = base64.urlsafe_b64encode(
            hashlib.sha256(code_verifier.encode()).digest()
        ).decode().rstrip("=")
        
        # 检查code_challenge是否匹配
        if code_challenge != code_info["code_challenge"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="code_verifier验证失败"
            )
    
    # 获取用户和项目信息
    user_id = code_info["user_id"]
    project_id = code_info["project_id"]
    scope = code_info["scope"]
    
    # 生成访问令牌
    access_token = create_jwt_token(
        subject=user_id,
        token_type=TokenType.ACCESS,
        scopes=scope.split(),
        project_id=project_id,
        user_id=user_id
    )
    
    # 生成刷新令牌
    refresh_token = create_jwt_token(
        subject=user_id,
        token_type=TokenType.REFRESH,
        scopes=scope.split(),
        project_id=project_id,
        user_id=user_id
    )
    
    # 存储刷新令牌
    refresh_token_id = str(uuid.uuid4())
    refresh_tokens[refresh_token_id] = {
        "token": refresh_token,
        "user_id": user_id,
        "client_id": client_id,
        "scope": scope,
        "project_id": project_id,
        "expires_at": datetime.now() + timedelta(days=JWT_REFRESH_TOKEN_EXPIRE_DAYS)
    }
    
    # 删除使用过的授权码
    del authorization_codes[code]
    
    # 返回令牌
    return {
        "access_token": access_token,
        "token_type": "Bearer",
        "expires_in": JWT_ACCESS_TOKEN_EXPIRE_MINUTES * 60,
        "refresh_token": refresh_token,
        "scope": scope
    }

# 处理刷新令牌
async def handle_refresh_token(form_data, db: AsyncSession):
    """处理刷新令牌"""
    refresh_token = form_data.get("refresh_token")
    client_id = form_data.get("client_id")
    client_secret = form_data.get("client_secret")
    scope = form_data.get("scope")
    
    # 验证刷新令牌
    try:
        payload = verify_jwt_token(refresh_token)
        
        # 检查令牌类型
        if payload.get("type") != TokenType.REFRESH:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无效的刷新令牌类型"
            )
        
        # 获取用户和项目信息
        user_id = payload.get("user_id")
        project_id = payload.get("project_id")
        token_scope = payload.get("scope")
        
        # 如果请求的作用域未指定，使用原始作用域
        if not scope:
            scope = token_scope
        else:
            # 检查请求的作用域是否是原始作用域的子集
            requested_scopes = set(scope.split())
            original_scopes = set(token_scope.split())
            if not requested_scopes.issubset(original_scopes):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="请求的作用域超出原始授权范围"
                )
        
        # 生成新的访问令牌
        access_token = create_jwt_token(
            subject=user_id,
            token_type=TokenType.ACCESS,
            scopes=scope.split(),
            project_id=project_id,
            user_id=user_id
        )
        
        # 生成新的刷新令牌（令牌轮换）
        new_refresh_token = create_jwt_token(
            subject=user_id,
            token_type=TokenType.REFRESH,
            scopes=scope.split(),
            project_id=project_id,
            user_id=user_id
        )
        
        # 返回新令牌
        return {
            "access_token": access_token,
            "token_type": "Bearer",
            "expires_in": JWT_ACCESS_TOKEN_EXPIRE_MINUTES * 60,
            "refresh_token": new_refresh_token,
            "scope": scope
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"刷新令牌失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="无效的刷新令牌"
        )

# 处理客户端凭证授权
async def handle_client_credentials(form_data, db: AsyncSession):
    """处理客户端凭证授权"""
    client_id = form_data.get("client_id")
    client_secret = form_data.get("client_secret")
    scope = form_data.get("scope", "read")
    project_id = form_data.get("project_id")
    
    # 检查客户端ID和密钥
    if client_id not in registered_clients:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的客户端ID"
        )
    
    client_info = registered_clients[client_id]
    if client_info["client_secret"] != client_secret:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的客户端密钥"
        )
    
    # 检查项目ID
    if not project_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="缺少项目ID"
        )
    
    # 生成访问令牌
    access_token = create_jwt_token(
        subject=client_id,
        token_type=TokenType.ACCESS,
        scopes=scope.split(),
        project_id=project_id
    )
    
    # 返回令牌
    return {
        "access_token": access_token,
        "token_type": "Bearer",
        "expires_in": JWT_ACCESS_TOKEN_EXPIRE_MINUTES * 60,
        "scope": scope
    }
