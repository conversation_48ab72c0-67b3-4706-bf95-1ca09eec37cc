#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
业务异常处理模块
"""

class BusinessException(Exception):
    """业务异常基类"""
    
    def __init__(self, message: str, error_code: str = None, details: dict = None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.details = details or {}
    
    def __str__(self):
        return self.message

class ValidationException(BusinessException):
    """数据验证异常"""
    pass

class AuthenticationException(BusinessException):
    """认证异常"""
    pass

class AuthorizationException(BusinessException):
    """授权异常"""
    pass

class ResourceNotFoundException(BusinessException):
    """资源未找到异常"""
    pass

class ResourceConflictException(BusinessException):
    """资源冲突异常"""
    pass

class ServiceUnavailableException(BusinessException):
    """服务不可用异常"""
    pass 