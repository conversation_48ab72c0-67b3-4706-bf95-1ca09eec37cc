#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
项目级 AI 知识库查询 API
"""

import logging
import uuid
from typing import Optional, List, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query, Path, status
from sqlalchemy.ext.asyncio import AsyncSession

from db.database import get_db
from models.project import Project
from core.auth import get_current_user
from api.deps import get_current_project
from services.ai import AIKnowledgeService, AIChatService
from schemas.ai import (
    AIKnowledgeQueryRequest,
    AIKnowledgeQueryResponse,
    AIChatRequest,
    AIChatMessage,
)

logger = logging.getLogger(__name__)
router = APIRouter()

@router.post("/ai/knowledge/query", response_model=AIKnowledgeQueryResponse)
async def query_knowledge_base(
    query_data: AIKnowledgeQueryRequest,
    project: Project = Depends(get_current_project),
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    查询知识库
    
    根据查询文本在知识库中搜索相关内容。
    """
    try:
        # 查询知识库
        results = await AIKnowledgeService.query_knowledge_base(
            db=db,
            query_data=query_data,
            user_id=current_user.id,
        )
        
        return {
            "success": True,
            "message": "查询 AI 知识库成功",
            "data": results,
        }
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )
    except Exception as e:
        logger.error(f"查询 AI 知识库失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"查询 AI 知识库失败: {str(e)}",
        )

@router.post("/ai/knowledge/chat")
async def chat_with_knowledge_base(
    query_data: AIKnowledgeQueryRequest,
    project: Project = Depends(get_current_project),
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
    temperature: Optional[float] = Query(0.7, ge=0, le=2),
    max_tokens: Optional[int] = Query(None),
):
    """
    与知识库对话
    
    根据查询文本在知识库中搜索相关内容，并使用 AI 模型生成回复。
    """
    try:
        # 查询知识库
        results = await AIKnowledgeService.query_knowledge_base(
            db=db,
            query_data=query_data,
            user_id=current_user.id,
        )
        
        if not results:
            return {
                "success": True,
                "message": "未找到相关内容",
                "answer": "抱歉，我没有找到与您问题相关的信息。",
                "sources": [],
            }
            
        # 构建上下文
        context = "以下是与问题相关的信息：\n\n"
        sources = []
        
        for i, result in enumerate(results):
            context += f"[{i+1}] {result.content}\n\n"
            sources.append({
                "document_id": str(result.document_id),
                "document_title": result.document_title,
                "similarity": result.similarity,
                "content": result.content[:200] + "..." if len(result.content) > 200 else result.content,
            })
            
        # 构建提示
        system_message = f"""你是一个基于知识库的 AI 助手。请根据提供的上下文信息回答用户的问题。
如果上下文中没有足够的信息来回答问题，请诚实地说你不知道，不要编造信息。
回答时请引用相关的上下文信息，并在回答中标明引用的来源编号，例如 [1]、[2] 等。
回答应该简洁、准确、有帮助，并且使用中文。"""

        user_message = f"""上下文信息：
{context}

用户问题：{query_data.query}

请根据上下文信息回答我的问题，并在回答中标明引用的来源编号。"""

        # 构建聊天请求
        chat_request = AIChatRequest(
            messages=[
                AIChatMessage(role="system", content=system_message),
                AIChatMessage(role="user", content=user_message),
            ],
            temperature=temperature,
            max_tokens=max_tokens,
        )
        
        # 发送聊天请求
        chat_response = await AIChatService.chat_completion(
            db=db,
            project_id=project.id,
            user_id=current_user.id,
            messages=chat_request.messages,
            temperature=chat_request.temperature,
            max_tokens=chat_request.max_tokens,
        )
        
        # 提取回答
        answer = chat_response.get("choices", [{}])[0].get("message", {}).get("content", "")
        
        return {
            "success": True,
            "message": "查询 AI 知识库成功",
            "answer": answer,
            "sources": sources,
        }
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )
    except Exception as e:
        logger.error(f"查询 AI 知识库失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"查询 AI 知识库失败: {str(e)}",
        )
