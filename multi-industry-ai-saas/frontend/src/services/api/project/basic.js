import api from '../httpClient';
import { getProjectId } from '../httpClient';

/**
 * 项目-基础设置API
 */
const basic = {
  /**
   * 获取仓库列表
   * @param {Object} params - 查询参数
   * @returns {Promise} - 仓库列表
   */
  getWarehouses: async (params = {}) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.get(`/project/${projectId}/basic/warehouses`, { params });
  },

  /**
   * 获取仓库详情
   * @param {string} id - 仓库ID
   * @returns {Promise} - 仓库详情
   */
  getWarehouse: async (id) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.get(`/project/${projectId}/basic/warehouses/${id}`);
  },

  /**
   * 创建仓库
   * @param {Object} data - 仓库数据
   * @returns {Promise} - 创建结果
   */
  createWarehouse: async (data) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.post(`/project/${projectId}/basic/warehouses`, data);
  },

  /**
   * 更新仓库
   * @param {string} id - 仓库ID
   * @param {Object} data - 仓库数据
   * @returns {Promise} - 更新结果
   */
  updateWarehouse: async (id, data) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.put(`/project/${projectId}/basic/warehouses/${id}`, data);
  },

  /**
   * 删除仓库
   * @param {string} id - 仓库ID
   * @returns {Promise} - 删除结果
   */
  deleteWarehouse: async (id) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.delete(`/project/${projectId}/basic/warehouses/${id}`);
  },

  /**
   * 获取门店列表
   * @param {Object} params - 查询参数
   * @returns {Promise} - 门店列表
   */
  getStores: async (params = {}) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.get(`/project/${projectId}/basic/stores`, { params });
  },

  /**
   * 获取门店详情
   * @param {string} id - 门店ID
   * @returns {Promise} - 门店详情
   */
  getStore: async (id) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.get(`/project/${projectId}/basic/stores/${id}`);
  },

  /**
   * 创建门店
   * @param {Object} data - 门店数据
   * @returns {Promise} - 创建结果
   */
  createStore: async (data) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.post(`/project/${projectId}/basic/stores`, data);
  },

  /**
   * 更新门店
   * @param {string} id - 门店ID
   * @param {Object} data - 门店数据
   * @returns {Promise} - 更新结果
   */
  updateStore: async (id, data) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.put(`/project/${projectId}/basic/stores/${id}`, data);
  },

  /**
   * 删除门店
   * @param {string} id - 门店ID
   * @returns {Promise} - 删除结果
   */
  deleteStore: async (id) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.delete(`/project/${projectId}/basic/stores/${id}`);
  }
};

export default basic;
