#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
AI 嵌入 Schema
"""

from typing import Optional, List, Dict, Any, Union
from datetime import datetime
import uuid
from pydantic import BaseModel, Field, validator

class AIEmbeddingRequest(BaseModel):
    """AI 嵌入请求 Schema"""
    input: Union[str, List[str]] = Field(..., description="输入文本，可以是单个字符串或字符串列表")
    model_id: Optional[uuid.UUID] = Field(None, description="模型 ID，如果不指定则使用默认模型")
    config_id: Optional[uuid.UUID] = Field(None, description="配置 ID，如果不指定则使用默认配置")
    dimensions: Optional[int] = Field(None, gt=0, description="嵌入维度，仅部分模型支持")
    user_data: Optional[Dict[str, Any]] = Field(None, description="用户数据，用于日志和监控")

class AIEmbeddingData(BaseModel):
    """AI 嵌入数据 Schema"""
    index: int = Field(..., description="嵌入索引")
    object: str = Field(..., description="对象类型")
    embedding: List[float] = Field(..., description="嵌入向量")

class AIEmbeddingUsage(BaseModel):
    """AI 嵌入使用情况 Schema"""
    prompt_tokens: int = Field(..., description="提示 token 数")
    total_tokens: int = Field(..., description="总 token 数")

class AIEmbeddingResponse(BaseModel):
    """AI 嵌入响应 Schema"""
    object: str = Field(..., description="对象类型")
    data: List[AIEmbeddingData] = Field(..., description="嵌入数据列表")
    model: str = Field(..., description="模型名称")
    usage: Optional[AIEmbeddingUsage] = Field(None, description="使用情况")

class AIEmbeddingErrorResponse(BaseModel):
    """AI 嵌入错误响应 Schema"""
    success: bool = Field(False, description="是否成功")
    message: str = Field(..., description="错误信息")
    error: Optional[Dict[str, Any]] = Field(None, description="错误详情")

# 嵌入模型列表响应已移除 - 由AI助手系统统一管理
# class AIEmbeddingModelListResponse(BaseModel):


# 嵌入记录 Schema
class AIEmbeddingBase(BaseModel):
    """嵌入记录基础Schema"""
    input_text: str = Field(..., description="输入文本")
    embedding_metadata: Optional[Dict[str, Any]] = Field(None, description="元数据")


class AIEmbeddingCreate(AIEmbeddingBase):
    """创建嵌入记录Schema"""
    model_id: uuid.UUID = Field(..., description="模型ID")
    project_id: Optional[uuid.UUID] = Field(None, description="项目ID")
    tenant_id: Optional[uuid.UUID] = Field(None, description="租户ID")


class AIEmbeddingResponse(AIEmbeddingBase):
    """嵌入记录响应Schema"""
    id: uuid.UUID = Field(..., description="嵌入ID")
    model_id: uuid.UUID = Field(..., description="模型ID")
    project_id: Optional[uuid.UUID] = Field(None, description="项目ID")
    tenant_id: Optional[uuid.UUID] = Field(None, description="租户ID")
    embedding_vector: List[float] = Field(..., description="嵌入向量")
    dimensions: int = Field(..., description="向量维度")
    token_count: Optional[int] = Field(None, description="token数量")
    created_at: datetime = Field(..., description="创建时间")
    created_by: Optional[uuid.UUID] = Field(None, description="创建者ID")

    class Config:
        from_attributes = True


# 查询和搜索 Schema
class AIEmbeddingQuery(BaseModel):
    """嵌入查询Schema"""
    query_text: str = Field(..., description="查询文本")
    model_id: Optional[uuid.UUID] = Field(None, description="模型ID")
    knowledge_base_ids: Optional[List[uuid.UUID]] = Field(None, description="知识库ID列表")
    top_k: int = Field(5, description="返回结果数量")
    similarity_threshold: float = Field(0.7, description="相似度阈值")
    metadata_filter: Optional[Dict[str, Any]] = Field(None, description="元数据过滤条件")


class AIEmbeddingSimilarityResult(BaseModel):
    """嵌入相似度搜索结果Schema"""
    id: str = Field(..., description="文本块ID")
    content: str = Field(..., description="文本内容")
    similarity_score: float = Field(..., description="相似度分数")
    chunk_index: int = Field(..., description="文本块索引")
    token_count: int = Field(..., description="token数量")
    document_title: str = Field(..., description="文档标题")
    knowledge_base_name: str = Field(..., description="知识库名称")
    metadata: Dict[str, Any] = Field(..., description="元数据")


class AIEmbeddingSearchResponse(BaseModel):
    """嵌入搜索响应Schema"""
    success: bool = Field(True, description="是否成功")
    message: str = Field("搜索完成", description="响应消息")
    data: List[AIEmbeddingSimilarityResult] = Field(..., description="搜索结果")
    query_text: str = Field(..., description="查询文本")
    total_results: int = Field(..., description="结果总数")
    search_time_ms: float = Field(..., description="搜索耗时(毫秒)")


# 统计信息 Schema
class AIEmbeddingModelStats(BaseModel):
    """嵌入模型统计Schema"""
    model_name: str = Field(..., description="模型名称")
    count: int = Field(..., description="使用次数")


class AIKnowledgeBaseStats(BaseModel):
    """知识库统计Schema"""
    name: str = Field(..., description="知识库名称")
    chunk_count: int = Field(..., description="文本块数量")
    total_tokens: int = Field(..., description="总token数")


class AIEmbeddingStatsResponse(BaseModel):
    """嵌入统计响应Schema"""
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    data: Dict[str, Any] = Field(..., description="统计数据")


# API请求 Schema
class EmbeddingCreateRequest(BaseModel):
    """创建嵌入请求Schema"""
    input: Union[str, List[str]] = Field(..., description="输入文本或文本列表")
    model_id: Optional[uuid.UUID] = Field(None, description="模型ID，为空时使用默认模型")
    metadata: Optional[Dict[str, Any]] = Field(None, description="元数据")


class EmbeddingCreateResponse(BaseModel):
    """创建嵌入响应Schema"""
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="响应消息")
    data: List[Dict[str, Any]] = Field(..., description="嵌入数据")
    model: Dict[str, Any] = Field(..., description="使用的模型信息")


# 向量管理 Schema
class VectorManagementRequest(BaseModel):
    """向量管理请求Schema"""
    action: str = Field(..., description="操作类型：create_index, cleanup, reindex, optimize")
    knowledge_base_ids: Optional[List[uuid.UUID]] = Field(None, description="知识库ID列表")
    options: Optional[Dict[str, Any]] = Field(None, description="操作选项")


class VectorManagementResponse(BaseModel):
    """向量管理响应Schema"""
    success: bool = Field(..., description="操作是否成功")
    message: str = Field(..., description="操作消息")
    action: str = Field(..., description="执行的操作")
    processed_count: int = Field(..., description="处理的项目数量")
    details: Optional[Dict[str, Any]] = Field(None, description="详细信息")
