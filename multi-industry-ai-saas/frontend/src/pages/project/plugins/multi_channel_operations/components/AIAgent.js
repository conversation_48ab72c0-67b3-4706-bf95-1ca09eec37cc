import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Button,
  Switch,
  Progress,
  Timeline,
  Tag,
  Space,
  Typography,
  Alert,
  Statistic,
  Modal,
  Form,
  Select,
  InputNumber,
  message,
  Avatar,
  List
} from 'antd';
import {
  RobotOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  SettingOutlined,
  BulbOutlined,
  <PERSON>boltOutlined,
  EyeOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import { useParams } from 'react-router-dom';

const { Title, Text } = Typography;
const { Option } = Select;

const AIAgent = () => {
  const { projectId } = useParams();
  const [agentStatus, setAgentStatus] = useState('stopped'); // stopped, running, paused
  const [agentConfig, setAgentConfig] = useState({});
  const [configModalVisible, setConfigModalVisible] = useState(false);
  const [agentLogs, setAgentLogs] = useState([]);
  const [agentMetrics, setAgentMetrics] = useState({});
  const [form] = Form.useForm();

  useEffect(() => {
    loadAgentData();
  }, [projectId]);

  const loadAgentData = async () => {
    // 模拟AI智能体数据
    setAgentMetrics({
      total_decisions: 156,
      success_rate: 94.2,
      revenue_impact: 12580,
      time_saved: 48.5
    });

    setAgentLogs([
      {
        id: '1',
        timestamp: new Date().toISOString(),
        action: '价格优化',
        target: '招牌牛肉面',
        result: 'success',
        details: '基于竞品分析，调整价格从28元到26元，预计销量提升15%',
        impact: '+15% 销量'
      },
      {
        id: '2',
        timestamp: new Date(Date.now() - 3600000).toISOString(),
        action: '库存预警',
        target: '麻辣香锅',
        result: 'warning',
        details: '检测到库存不足，已自动下架并通知补货',
        impact: '避免缺货'
      },
      {
        id: '3',
        timestamp: new Date(Date.now() - 7200000).toISOString(),
        action: '竞品监控',
        target: '周边5家店铺',
        result: 'info',
        details: '发现竞品降价活动，建议跟进促销策略',
        impact: '市场洞察'
      }
    ]);
  };

  const handleStartAgent = async () => {
    try {
      setAgentStatus('running');
      message.success('AI运营智能体已启动');
    } catch (error) {
      message.error('启动失败');
    }
  };

  const handleStopAgent = async () => {
    try {
      setAgentStatus('stopped');
      message.success('AI运营智能体已停止');
    } catch (error) {
      message.error('停止失败');
    }
  };

  const handleConfigAgent = async (values) => {
    try {
      setAgentConfig(values);
      setConfigModalVisible(false);
      message.success('配置已保存');
    } catch (error) {
      message.error('配置保存失败');
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'running': return '#52c41a';
      case 'paused': return '#faad14';
      case 'stopped': return '#d9d9d9';
      default: return '#d9d9d9';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'running': return '运行中';
      case 'paused': return '已暂停';
      case 'stopped': return '已停止';
      default: return '未知';
    }
  };

  const getActionIcon = (result) => {
    switch (result) {
      case 'success': return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case 'warning': return <ExclamationCircleOutlined style={{ color: '#faad14' }} />;
      case 'error': return <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />;
      default: return <ClockCircleOutlined style={{ color: '#1890ff' }} />;
    }
  };

  return (
    <div>
      {/* 智能体状态概览 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <div style={{ textAlign: 'center' }}>
              <Avatar 
                size={64} 
                icon={<RobotOutlined />} 
                style={{ 
                  backgroundColor: getStatusColor(agentStatus),
                  marginBottom: 12
                }} 
              />
              <div>
                <Text strong>AI运营智能体</Text>
                <div style={{ marginTop: 4 }}>
                  <Tag color={getStatusColor(agentStatus)}>
                    {getStatusText(agentStatus)}
                  </Tag>
                </div>
                <div style={{ marginTop: 12 }}>
                  {agentStatus === 'stopped' ? (
                    <Button 
                      type="primary" 
                      icon={<PlayCircleOutlined />}
                      onClick={handleStartAgent}
                    >
                      启动
                    </Button>
                  ) : (
                    <Button 
                      danger 
                      icon={<PauseCircleOutlined />}
                      onClick={handleStopAgent}
                    >
                      停止
                    </Button>
                  )}
                </div>
              </div>
            </div>
          </Card>
        </Col>
        
        <Col span={6}>
          <Card>
            <Statistic
              title="总决策次数"
              value={agentMetrics.total_decisions}
              prefix={<ThunderboltOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        
        <Col span={6}>
          <Card>
            <Statistic
              title="成功率"
              value={agentMetrics.success_rate}
              suffix="%"
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        
        <Col span={6}>
          <Card>
            <Statistic
              title="收益影响"
              value={agentMetrics.revenue_impact}
              prefix="¥"
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 智能体能力 */}
      <Card title="智能体能力" style={{ marginBottom: 24 }}>
        <Row gutter={16}>
          <Col span={8}>
            <Card size="small">
              <div style={{ textAlign: 'center' }}>
                <BulbOutlined style={{ fontSize: 24, color: '#1890ff', marginBottom: 8 }} />
                <div>
                  <Text strong>智能定价</Text>
                  <div style={{ marginTop: 4 }}>
                    <Text type="secondary" style={{ fontSize: 12 }}>
                      基于市场数据自动调整价格
                    </Text>
                  </div>
                  <div style={{ marginTop: 8 }}>
                    <Switch 
                      checked={agentConfig.auto_pricing} 
                      size="small"
                      onChange={(checked) => setAgentConfig({...agentConfig, auto_pricing: checked})}
                    />
                  </div>
                </div>
              </div>
            </Card>
          </Col>
          
          <Col span={8}>
            <Card size="small">
              <div style={{ textAlign: 'center' }}>
                <EyeOutlined style={{ fontSize: 24, color: '#52c41a', marginBottom: 8 }} />
                <div>
                  <Text strong>竞品监控</Text>
                  <div style={{ marginTop: 4 }}>
                    <Text type="secondary" style={{ fontSize: 12 }}>
                      实时监控竞品动态
                    </Text>
                  </div>
                  <div style={{ marginTop: 8 }}>
                    <Switch 
                      checked={agentConfig.competitor_monitoring} 
                      size="small"
                      onChange={(checked) => setAgentConfig({...agentConfig, competitor_monitoring: checked})}
                    />
                  </div>
                </div>
              </div>
            </Card>
          </Col>
          
          <Col span={8}>
            <Card size="small">
              <div style={{ textAlign: 'center' }}>
                <ThunderboltOutlined style={{ fontSize: 24, color: '#fa8c16', marginBottom: 8 }} />
                <div>
                  <Text strong>库存管理</Text>
                  <div style={{ marginTop: 4 }}>
                    <Text type="secondary" style={{ fontSize: 12 }}>
                      智能库存预警和补货
                    </Text>
                  </div>
                  <div style={{ marginTop: 8 }}>
                    <Switch 
                      checked={agentConfig.inventory_management} 
                      size="small"
                      onChange={(checked) => setAgentConfig({...agentConfig, inventory_management: checked})}
                    />
                  </div>
                </div>
              </div>
            </Card>
          </Col>
        </Row>
        
        <div style={{ textAlign: 'right', marginTop: 16 }}>
          <Button 
            icon={<SettingOutlined />}
            onClick={() => setConfigModalVisible(true)}
          >
            高级配置
          </Button>
        </div>
      </Card>

      {/* 运行状态和日志 */}
      <Row gutter={16}>
        <Col span={12}>
          <Card title="运行状态" style={{ height: 400 }}>
            {agentStatus === 'running' && (
              <div>
                <Alert
                  message="智能体正在运行"
                  description="AI正在分析市场数据，执行优化策略..."
                  type="success"
                  showIcon
                  style={{ marginBottom: 16 }}
                />
                <div style={{ marginBottom: 16 }}>
                  <Text>当前任务进度</Text>
                  <Progress percent={75} status="active" />
                </div>
              </div>
            )}
            
            {agentStatus === 'stopped' && (
              <Alert
                message="智能体已停止"
                description="点击启动按钮开始AI自动化运营"
                type="info"
                showIcon
              />
            )}
          </Card>
        </Col>
        
        <Col span={12}>
          <Card title="执行日志" style={{ height: 400 }}>
            <List
              size="small"
              dataSource={agentLogs}
              renderItem={item => (
                <List.Item>
                  <List.Item.Meta
                    avatar={getActionIcon(item.result)}
                    title={
                      <Space>
                        <Text strong>{item.action}</Text>
                        <Text type="secondary">- {item.target}</Text>
                      </Space>
                    }
                    description={
                      <div>
                        <div style={{ fontSize: 12 }}>{item.details}</div>
                        <div style={{ marginTop: 4 }}>
                          <Tag color="blue" size="small">{item.impact}</Tag>
                          <Text type="secondary" style={{ fontSize: 11 }}>
                            {new Date(item.timestamp).toLocaleString()}
                          </Text>
                        </div>
                      </div>
                    }
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>
      </Row>

      {/* 配置模态框 */}
      <Modal
        title="AI智能体高级配置"
        open={configModalVisible}
        onCancel={() => setConfigModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleConfigAgent}
          initialValues={agentConfig}
        >
          <Form.Item
            name="decision_frequency"
            label="决策频率"
          >
            <Select placeholder="选择决策频率">
              <Option value="realtime">实时</Option>
              <Option value="hourly">每小时</Option>
              <Option value="daily">每天</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="risk_tolerance"
            label="风险容忍度"
          >
            <Select placeholder="选择风险容忍度">
              <Option value="conservative">保守</Option>
              <Option value="moderate">适中</Option>
              <Option value="aggressive">激进</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="max_price_change"
            label="最大价格变动幅度(%)"
          >
            <InputNumber
              style={{ width: '100%' }}
              min={1}
              max={50}
              placeholder="输入最大价格变动幅度"
            />
          </Form.Item>

          <div style={{ textAlign: 'right', marginTop: 24 }}>
            <Space>
              <Button onClick={() => setConfigModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                保存配置
              </Button>
            </Space>
          </div>
        </Form>
      </Modal>
    </div>
  );
};

export default AIAgent;
