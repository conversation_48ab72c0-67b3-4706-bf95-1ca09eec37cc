import pytest
from uuid import uuid4
from sqlalchemy.orm import Session

from models.user import User
from models.role import Role, Permission, RolePermission
from models.store import Store, StoreCategory, StoreRegion
from models.warehouse import Warehouse, WarehouseSetting
from models.system_config import SystemConfig

from services.system_config import SystemConfigService
from services.store import StoreService, StoreCategoryService, StoreRegionService
from services.warehouse import WarehouseService, WarehouseSettingService
from services.role import RoleService, PermissionService, RolePermissionService

from schemas.system_config import SystemConfigCreate, SystemConfigUpdate
from schemas.store import StoreCreate, StoreUpdate, StoreCategoryCreate, StoreCategoryUpdate, StoreRegionCreate, StoreRegionUpdate
from schemas.warehouse import WarehouseCreate, WarehouseUpdate, WarehouseSettingCreate, WarehouseSettingUpdate
from schemas.role import RoleCreate, RoleUpdate, PermissionCreate, PermissionUpdate, RolePermissionCreate

@pytest.fixture
def db_session():
    """创建测试数据库会话"""
    from db.session import SessionLocal
    session = SessionLocal()
    try:
        yield session
    finally:
        session.close()

def test_system_config_service(db_session: Session):
    """测试系统配置服务"""
    # 创建测试项目ID
    project_id = uuid4()
    
    # 测试创建配置
    config_create = SystemConfigCreate(
        project_id=project_id,
        config_type="theme",
        config_key="primaryColor",
        config_value={"value": "#1890ff"}
    )
    
    config = SystemConfigService.create_config(db_session, config_create)
    
    # 验证配置创建成功
    assert config.id is not None
    assert config.project_id == project_id
    assert config.config_type == "theme"
    assert config.config_key == "primaryColor"
    assert config.config_value == {"value": "#1890ff"}
    
    # 测试获取配置
    retrieved_config = SystemConfigService.get_config_by_type_and_key(
        db_session, project_id, "theme", "primaryColor"
    )
    
    assert retrieved_config is not None
    assert retrieved_config.id == config.id
    
    # 测试更新配置
    config_update = SystemConfigUpdate(
        config_value={"value": "#2f54eb"}
    )
    
    updated_config = SystemConfigService.update_config(
        db_session, project_id, "theme", "primaryColor", config_update
    )
    
    assert updated_config is not None
    assert updated_config.config_value == {"value": "#2f54eb"}
    
    # 测试批量更新配置
    configs = {
        "primaryColor": {"value": "#1890ff"},
        "mode": {"value": "light"},
        "borderRadius": {"value": 4}
    }
    
    bulk_configs = SystemConfigService.bulk_upsert_configs(
        db_session, project_id, "theme", configs
    )
    
    assert len(bulk_configs) == 3
    
    # 测试获取所有配置
    all_configs = SystemConfigService.get_configs_by_type(db_session, project_id, "theme")
    
    assert len(all_configs) == 3
    
    # 测试删除配置
    for config in all_configs:
        success = SystemConfigService.delete_config(
            db_session, project_id, config.config_type, config.config_key
        )
        assert success is True

def test_store_service(db_session: Session):
    """测试门店服务"""
    # 创建测试项目ID
    project_id = uuid4()
    
    # 测试创建门店分类
    category_create = StoreCategoryCreate(
        project_id=project_id,
        name="旗舰店",
        code="flagship",
        description="面积大于500平米的大型门店"
    )
    
    category = StoreCategoryService.create_category(db_session, category_create)
    
    # 验证门店分类创建成功
    assert category.id is not None
    assert category.project_id == project_id
    assert category.name == "旗舰店"
    assert category.code == "flagship"
    
    # 测试创建门店区域
    region_create = StoreRegionCreate(
        project_id=project_id,
        name="华北区",
        code="north",
        description="包括北京、天津、河北等地区"
    )
    
    region = StoreRegionService.create_region(db_session, region_create)
    
    # 验证门店区域创建成功
    assert region.id is not None
    assert region.project_id == project_id
    assert region.name == "华北区"
    assert region.code == "north"
    
    # 测试创建门店
    store_create = StoreCreate(
        project_id=project_id,
        category_id=category.id,
        region_id=region.id,
        name="北京西单店",
        code="BJ001",
        address="北京市西城区西单北大街120号",
        phone="010-12345678",
        manager="张三",
        latitude=39.9123,
        longitude=116.3789,
        business_hours="09:00-22:00",
        status="active",
        settings={"has_parking": True, "floor_area": 600}
    )
    
    store = StoreService.create_store(db_session, store_create)
    
    # 验证门店创建成功
    assert store.id is not None
    assert store.project_id == project_id
    assert store.category_id == category.id
    assert store.region_id == region.id
    assert store.name == "北京西单店"
    assert store.code == "BJ001"
    
    # 测试获取门店
    retrieved_store = StoreService.get_store_by_id(db_session, store.id)
    
    assert retrieved_store is not None
    assert retrieved_store.id == store.id
    
    # 测试更新门店
    store_update = StoreUpdate(
        name="北京西单旗舰店",
        code="BJ001",
        address="北京市西城区西单北大街120号",
        phone="010-12345678",
        manager="李四",
        business_hours="09:00-22:00",
        status="active"
    )
    
    updated_store = StoreService.update_store(db_session, store.id, store_update)
    
    assert updated_store is not None
    assert updated_store.name == "北京西单旗舰店"
    assert updated_store.manager == "李四"
    
    # 测试获取门店列表
    stores = StoreService.get_stores(db_session, project_id)
    
    assert len(stores) == 1
    assert stores[0].id == store.id
    
    # 测试删除门店
    success = StoreService.delete_store(db_session, store.id)
    assert success is True
    
    # 测试删除门店分类和区域
    success = StoreCategoryService.delete_category(db_session, category.id)
    assert success is True
    
    success = StoreRegionService.delete_region(db_session, region.id)
    assert success is True

def test_warehouse_service(db_session: Session):
    """测试仓库服务"""
    # 创建测试项目ID
    project_id = uuid4()
    
    # 测试创建仓库
    warehouse_create = WarehouseCreate(
        project_id=project_id,
        name="北京总仓",
        code="WH001",
        type="central",
        address="北京市大兴区经济开发区科创十三街18号院",
        phone="010-87654321",
        manager="李四",
        capacity=10000,
        capacity_unit="sqm",
        business_hours="09:00-18:00",
        status="active",
        settings={"has_cold_storage": True, "floors": 3}
    )
    
    warehouse = WarehouseService.create_warehouse(db_session, warehouse_create)
    
    # 验证仓库创建成功
    assert warehouse.id is not None
    assert warehouse.project_id == project_id
    assert warehouse.name == "北京总仓"
    assert warehouse.code == "WH001"
    assert warehouse.type == "central"
    
    # 测试获取仓库
    retrieved_warehouse = WarehouseService.get_warehouse_by_id(db_session, warehouse.id)
    
    assert retrieved_warehouse is not None
    assert retrieved_warehouse.id == warehouse.id
    
    # 测试更新仓库
    warehouse_update = WarehouseUpdate(
        name="北京中央仓库",
        code="WH001",
        type="central",
        address="北京市大兴区经济开发区科创十三街18号院",
        phone="010-87654321",
        manager="王五",
        capacity=12000,
        capacity_unit="sqm",
        business_hours="08:00-20:00",
        status="active"
    )
    
    updated_warehouse = WarehouseService.update_warehouse(db_session, warehouse.id, warehouse_update)
    
    assert updated_warehouse is not None
    assert updated_warehouse.name == "北京中央仓库"
    assert updated_warehouse.manager == "王五"
    assert updated_warehouse.capacity == 12000
    assert updated_warehouse.business_hours == "08:00-20:00"
    
    # 测试创建仓库设置
    warehouse_setting_create = WarehouseSettingCreate(
        project_id=project_id,
        warehouse_mode="multi",
        default_business_hours_start="09:00",
        default_business_hours_end="18:00",
        enable_front_warehouse=True,
        enable_cross_warehouse_transfer=True,
        inventory_warning_threshold=10,
        auto_replenishment=True,
        default_capacity_unit="sqm"
    )
    
    warehouse_setting = WarehouseSettingService.create_setting(db_session, warehouse_setting_create)
    
    # 验证仓库设置创建成功
    assert warehouse_setting.id is not None
    assert warehouse_setting.project_id == project_id
    assert warehouse_setting.warehouse_mode == "multi"
    
    # 测试获取仓库设置
    retrieved_setting = WarehouseSettingService.get_setting_by_project_id(db_session, project_id)
    
    assert retrieved_setting is not None
    assert retrieved_setting.id == warehouse_setting.id
    
    # 测试更新仓库设置
    setting_update = WarehouseSettingUpdate(
        warehouse_mode="central",
        default_business_hours_start="08:00",
        default_business_hours_end="20:00",
        enable_front_warehouse=False,
        inventory_warning_threshold=15
    )
    
    updated_setting = WarehouseSettingService.update_setting(db_session, project_id, setting_update)
    
    assert updated_setting is not None
    assert updated_setting.warehouse_mode == "central"
    assert updated_setting.default_business_hours_start == "08:00"
    assert updated_setting.default_business_hours_end == "20:00"
    assert updated_setting.enable_front_warehouse is False
    assert updated_setting.inventory_warning_threshold == 15
    
    # 测试删除仓库和设置
    success = WarehouseService.delete_warehouse(db_session, warehouse.id)
    assert success is True
    
    db_session.delete(warehouse_setting)
    db_session.commit()

def test_role_service(db_session: Session):
    """测试角色服务"""
    # 创建测试项目ID
    project_id = uuid4()
    
    # 测试创建角色
    role_create = RoleCreate(
        project_id=project_id,
        name="门店经理",
        code="store_manager",
        description="负责门店日常管理",
        permissions={"store": {"view": True, "create": True, "update": True, "delete": False}},
        is_system_role=False
    )
    
    role = RoleService.create_role(db_session, role_create)
    
    # 验证角色创建成功
    assert role.id is not None
    assert role.project_id == project_id
    assert role.name == "门店经理"
    assert role.code == "store_manager"
    
    # 测试创建权限
    permission_create = PermissionCreate(
        name="查看门店",
        code="store:view",
        description="查看门店列表和详情",
        module="store",
        action="view",
        resource="store",
        is_system_permission=True
    )
    
    permission = PermissionService.create_permission(db_session, permission_create)
    
    # 验证权限创建成功
    assert permission.id is not None
    assert permission.name == "查看门店"
    assert permission.code == "store:view"
    
    # 测试创建角色权限关联
    role_permission_create = RolePermissionCreate(
        role_id=role.id,
        permission_id=permission.id
    )
    
    role_permission = RolePermissionService.create_role_permission(db_session, role_permission_create)
    
    # 验证角色权限关联创建成功
    assert role_permission.id is not None
    assert role_permission.role_id == role.id
    assert role_permission.permission_id == permission.id
    
    # 测试获取角色权限
    role_permissions = RolePermissionService.get_role_permissions(db_session, role.id)
    
    assert len(role_permissions) == 1
    assert role_permissions[0].id == role_permission.id
    
    # 测试批量更新角色权限
    # 创建另一个权限
    permission_create2 = PermissionCreate(
        name="创建门店",
        code="store:create",
        description="创建新门店",
        module="store",
        action="create",
        resource="store",
        is_system_permission=True
    )
    
    permission2 = PermissionService.create_permission(db_session, permission_create2)
    
    # 批量更新角色权限
    updated_permissions = RolePermissionService.bulk_update_role_permissions(
        db_session, role.id, [permission.id, permission2.id]
    )
    
    assert len(updated_permissions) == 2
    
    # 测试删除角色权限关联
    success = RolePermissionService.delete_role_permission(db_session, role.id, permission.id)
    assert success is True
    
    # 测试删除角色和权限
    db_session.query(RolePermission).filter(RolePermission.role_id == role.id).delete()
    db_session.commit()
    
    success = RoleService.delete_role(db_session, role.id)
    assert success is True
    
    db_session.delete(permission)
    db_session.delete(permission2)
    db_session.commit()
