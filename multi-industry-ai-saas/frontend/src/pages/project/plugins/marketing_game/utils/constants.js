// 游戏类型选项
export const gameTypeOptions = [
  { label: '随机抽奖', value: 'lucky_draw' },
  { label: '刮刮卡', value: 'scratch_card' },
  { label: '幸运大转盘', value: 'wheel_of_fortune' },
  { label: '闯关游戏', value: 'adventure_game' }
];

// 营销类型选项
export const marketingTypeOptions = [
  { label: '开业营销', value: 'opening_promotion', color: '#f50', icon: '🎉', description: '店铺开业庆典活动' },
  { label: '周年庆', value: 'anniversary', color: '#faad14', icon: '🎂', description: '周年庆典活动' },
  { label: '节假日营销', value: 'holiday_marketing', color: '#52c41a', icon: '🎄', description: '节假日主题活动' },
  { label: '自造节', value: 'custom_festival', color: '#1890ff', icon: '🎊', description: '品牌自造节日' },
  { label: '日常活动', value: 'daily_activity', color: '#722ed1', icon: '😊', description: '日常促销活动' },
  { label: '会员促销', value: 'membership_promotion', color: '#eb2f96', icon: '💎', description: '会员专享活动' }
];

// 游戏状态选项
export const gameStatusOptions = [
  { label: '草稿', value: 'draft', color: '#d9d9d9' },
  { label: '已发布', value: 'published', color: '#52c41a' },
  { label: '暂停', value: 'paused', color: '#faad14' },
  { label: '已结束', value: 'ended', color: '#f5222d' },
  { label: '已取消', value: 'cancelled', color: '#8c8c8c' }
];

// AI助手性格选项
export const aiPersonalityOptions = [
  { label: '友好型', value: 'friendly', description: '温馨友好，像朋友一样陪伴用户' },
  { label: '专业型', value: 'professional', description: '专业严谨，提供权威的游戏指导' },
  { label: '活泼型', value: 'playful', description: '活泼有趣，为游戏增添更多乐趣' }
];

// 主题样式选项
export const themeStyleOptions = [
  { label: '经典风格', value: 'classic', color: '#1890ff' },
  { label: '黄金风格', value: 'gold', color: '#faad14' },
  { label: '霓虹风格', value: 'neon', color: '#00BFFF' },
  { label: '彩虹风格', value: 'rainbow', color: '#ff6b6b' },
  { label: '简约风格', value: 'minimal', color: '#000000' },
  { label: '渐变风格', value: 'gradient', color: '#667eea' }
];

// 验证类型选项
export const verificationOptions = [
  { label: '手机号验证', value: 'phone', icon: '📱' },
  { label: '小票验证', value: 'receipt', icon: '🧾' },
  { label: '会员验证', value: 'membership', icon: '💎' },
  { label: '位置验证', value: 'location', icon: '📍' }
];

// 游戏类型文本
export const gameTypeText = {
  lucky_draw: '随机抽奖',
  scratch_card: '刮刮卡',
  wheel_of_fortune: '幸运大转盘',
  adventure_game: '闯关游戏'
};

// 游戏状态颜色
export const gameStatusColors = {
  draft: 'default',
  published: 'green',
  ended: 'blue',
  cancelled: 'red'
};

// 游戏状态文本
export const gameStatusText = {
  draft: '草稿',
  published: '进行中',
  ended: '已结束',
  cancelled: '已取消'
};

// 奖品类型选项
export const prizeTypeOptions = [
  { label: '实物奖品', value: 'physical' },
  { label: '优惠券', value: 'coupon' },
  { label: '积分', value: 'points' },
  { label: '虚拟物品', value: 'virtual' }
];

// 奖品类型文本
export const prizeTypeText = {
  physical: '实物奖品',
  coupon: '优惠券',
  points: '积分',
  virtual: '虚拟物品'
};

// 领取状态文本
export const claimStatusText = {
  unclaimed: '未领取',
  claimed: '已领取',
  expired: '已过期'
};

// 领取状态颜色
export const claimStatusColors = {
  unclaimed: 'warning',
  claimed: 'success',
  expired: 'default'
};
