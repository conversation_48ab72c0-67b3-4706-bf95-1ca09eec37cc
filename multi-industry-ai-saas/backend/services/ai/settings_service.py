#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
AI 设置服务
"""

import logging
import uuid
import json
from typing import Dict, Any, Optional
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from models.project import Project
from models.system_config import SystemConfig

logger = logging.getLogger(__name__)

class AISettingsService:
    """AI 设置服务"""

    @staticmethod
    async def get_settings(
        db: AsyncSession,
        project_id: uuid.UUID,
    ) -> Dict[str, Any]:
        """
        获取 AI 设置

        优先级：项目级设置 > 系统默认设置
        """
        # 获取项目信息
        project_query = select(Project).where(Project.id == project_id)
        project_result = await db.execute(project_query)
        project = project_result.scalars().first()

        if not project:
            logger.error(f"项目 ID '{project_id}' 不存在")
            return {}

        # 获取项目级设置
        project_settings_query = select(SystemConfig).where(
            SystemConfig.project_id == project_id,
            SystemConfig.config_type == "ai",
            SystemConfig.config_key == "ai_settings"
        )
        project_settings_result = await db.execute(project_settings_query)
        project_settings = project_settings_result.scalars().first()

        # 如果存在项目级设置，则返回
        if project_settings and project_settings.config_value:
            try:
                return json.loads(project_settings.config_value)
            except json.JSONDecodeError:
                logger.error(f"项目 ID '{project_id}' 的 AI 设置解析失败")

        # 获取系统默认设置
        system_settings_query = select(SystemConfig).where(
            SystemConfig.project_id.is_(None),
            SystemConfig.config_type == "ai",
            SystemConfig.config_key == "ai_settings"
        )
        system_settings_result = await db.execute(system_settings_query)
        system_settings = system_settings_result.scalars().first()

        # 如果存在系统默认设置，则返回
        if system_settings and system_settings.config_value:
            try:
                return json.loads(system_settings.config_value)
            except json.JSONDecodeError:
                logger.error("系统默认 AI 设置解析失败")

        # 返回默认设置
        return {
            "knowledge_base_enabled": True,
            "tools_enabled": True,
            "max_history_messages": 10,
            "default_system_prompt": "",
            "cache_enabled": True,
            "cache_ttl": 3600,
            "log_level": "info"
        }

    @staticmethod
    async def update_settings(
        db: AsyncSession,
        project_id: uuid.UUID,
        settings: Dict[str, Any],
    ) -> bool:
        """
        更新 AI 设置
        """
        # 获取项目信息
        project_query = select(Project).where(Project.id == project_id)
        project_result = await db.execute(project_query)
        project = project_result.scalars().first()

        if not project:
            logger.error(f"项目 ID '{project_id}' 不存在")
            return False

        # 获取项目级设置
        project_settings_query = select(SystemConfig).where(
            SystemConfig.project_id == project_id,
            SystemConfig.config_type == "ai",
            SystemConfig.config_key == "ai_settings"
        )
        project_settings_result = await db.execute(project_settings_query)
        project_settings = project_settings_result.scalars().first()

        # 如果存在项目级设置，则更新
        if project_settings:
            project_settings.config_value = json.dumps(settings)
            await db.commit()
            return True

        # 如果不存在项目级设置，则创建
        new_settings = SystemConfig(
            project_id=project_id,
            config_type="ai",
            config_key="ai_settings",
            config_value=json.dumps(settings)
        )
        db.add(new_settings)
        await db.commit()
        return True
