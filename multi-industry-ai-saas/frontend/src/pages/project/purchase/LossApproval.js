import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Table, 
  Button, 
  Space, 
  Tag, 
  Input, 
  DatePicker, 
  Form, 
  Select, 
  Modal, 
  message, 
  Descriptions, 
  Image, 
  Typography, 
  Divider,
  Row,
  Col
} from 'antd';
import { 
  SearchOutlined, 
  CheckOutlined, 
  CloseOutlined, 
  EyeOutlined, 
  ReloadOutlined 
} from '@ant-design/icons';
import dayjs from "dayjs";
import apiService from '../../../services/api';

const { Title, Text } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;
const { TextArea } = Input;

// 状态标签颜色映射
const statusColors = {
  pending: 'gold',
  approved: 'green',
  rejected: 'red',
  withdrawn: 'default'
};

// 状态文本映射
const statusTexts = {
  pending: '待审核',
  approved: '已通过',
  rejected: '已拒绝',
  withdrawn: '已撤回'
};

// 报损原因映射
const reasonTexts = {
  expired: '过期',
  damaged: '损坏',
  lost: '丢失',
  quality: '质量问题',
  other: '其他'
};

const LossApproval = () => {
  // 状态管理
  const [loading, setLoading] = useState(false);
  const [lossRecords, setLossRecords] = useState([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });
  const [searchParams, setSearchParams] = useState({
    status: 'pending',
    search: '',
    dateRange: null,
    storeId: null
  });
  const [stores, setStores] = useState([]);
  const [currentRecord, setCurrentRecord] = useState(null);
  const [detailVisible, setDetailVisible] = useState(false);
  const [approveVisible, setApproveVisible] = useState(false);
  const [rejectVisible, setRejectVisible] = useState(false);
  const [form] = Form.useForm();

  // 获取门店列表
  const fetchStores = async () => {
    try {
      const response = await apiService.project.store.getList();
      if (response && response.items) {
        setStores(response.items);
      }
    } catch (error) {
      console.error('获取门店列表失败:', error);
      message.error('获取门店列表失败');
    }
  };

  // 获取报损记录列表
  const fetchLossRecords = async (params = {}) => {
    setLoading(true);
    try {
      const { current, pageSize } = pagination;
      const { status, search, dateRange, storeId } = searchParams;
      
      const queryParams = {
        skip: (current - 1) * pageSize,
        limit: pageSize,
        status,
        search: search || undefined,
        store_id: storeId || undefined,
        ...params
      };
      
      // 添加日期范围
      if (dateRange && dateRange[0] && dateRange[1]) {
        queryParams.start_date = dateRange[0].format('YYYY-MM-DD');
        queryParams.end_date = dateRange[1].format('YYYY-MM-DD');
      }
      
      const response = await apiService.project.loss.getList(queryParams);
      
      if (response) {
        setLossRecords(response.items || []);
        setPagination({
          ...pagination,
          current: response.page || 1,
          pageSize: response.size || 10,
          total: response.total || 0
        });
      }
    } catch (error) {
      console.error('获取报损记录失败:', error);
      message.error('获取报损记录失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取报损记录详情
  const fetchLossDetail = async (id) => {
    setLoading(true);
    try {
      const response = await apiService.project.loss.getDetail(id);
      return response;
    } catch (error) {
      console.error('获取报损记录详情失败:', error);
      message.error('获取报损记录详情失败');
      return null;
    } finally {
      setLoading(false);
    }
  };

  // 审核通过
  const handleApprove = async () => {
    if (!currentRecord) return;
    setLoading(true);
    try {
      await apiService.project.loss.updateStatus(currentRecord.id, { status: 'approved' });
      message.success('审核通过成功');
      setApproveVisible(false);
      setCurrentRecord(null);
      fetchLossRecords();
    } catch (error) {
      console.error('审核通过失败:', error);
      message.error('审核通过失败');
    } finally {
      setLoading(false);
    }
  };

  // 审核拒绝
  const handleReject = async () => {
    if (!currentRecord) return;
    try {
      const values = await form.validateFields();
      setLoading(true);
      
      await apiService.project.loss.updateStatus(currentRecord.id, {
        status: 'rejected',
        reject_reason: values.rejectReason
      });
      
      message.success('审核拒绝成功');
      setRejectVisible(false);
      setCurrentRecord(null);
      form.resetFields();
      fetchLossRecords();
    } catch (error) {
      console.error('审核拒绝失败:', error);
      if (error.errorFields) {
        // 表单验证错误
        return;
      }
      message.error('审核拒绝失败');
    } finally {
      setLoading(false);
    }
  };

  // 初始化
  useEffect(() => {
    fetchStores();
  }, []);

  // 自动监听参数变化刷新报损列表
  useEffect(() => {
    fetchLossRecords();
    // eslint-disable-next-line
  }, [searchParams, pagination.current, pagination.pageSize]);

  // 处理表格分页、排序、筛选变化
  const handleTableChange = (pagination, filters, sorter) => {
    setPagination({
      ...pagination,
      current: pagination.current,
      pageSize: pagination.pageSize
    });
  };

  // 处理搜索表单提交
  const handleSearch = (values) => {
    const { status, search, dateRange, storeId } = values;
    setSearchParams({ status, search, dateRange, storeId });
    setPagination(prev => ({ ...prev, current: 1 }));
  };

  // 重置搜索表单
  const handleReset = () => {
    form.resetFields();
    const defaultParams = {
      status: 'pending',
      search: '',
      dateRange: null,
      storeId: null
    };
    setSearchParams(defaultParams);
    setPagination(prev => ({ ...prev, current: 1 }));
  };

  // 查看详情
  const handleViewDetail = async (record) => {
    const detailData = await fetchLossDetail(record.id);
    if (detailData) {
      setCurrentRecord(detailData);
      setDetailVisible(true);
    }
  };

  // 搜索表单
  const SearchForm = () => (
    <Form
      layout="inline"
      form={form}
      initialValues={searchParams}
      onFinish={handleSearch}
      style={{ marginBottom: 16 }}
    >
      <Form.Item name="status" label="状态">
        <Select style={{ width: 120 }}>
          <Option value="">全部</Option>
          <Option value="pending">待审核</Option>
          <Option value="approved">已通过</Option>
          <Option value="rejected">已拒绝</Option>
          <Option value="withdrawn">已撤回</Option>
        </Select>
      </Form.Item>
      
      <Form.Item name="storeId" label="门店">
        <Select style={{ width: 150 }} allowClear placeholder="选择门店">
          <Option value="">全部门店</Option>
          {stores.map(store => (
            <Option key={store.id} value={store.id}>{store.name}</Option>
          ))}
        </Select>
      </Form.Item>
      
      <Form.Item name="dateRange" label="日期范围">
        <RangePicker style={{ width: 240 }} />
      </Form.Item>
      
      <Form.Item name="search" label="搜索">
        <Input
          placeholder="产品名称/报损原因"
          prefix={<SearchOutlined />}
          style={{ width: 200 }}
        />
      </Form.Item>
      
      <Form.Item>
        <Button type="primary" htmlType="submit">
          搜索
        </Button>
      </Form.Item>
      
      <Form.Item>
        <Button onClick={handleReset}>
          重置
        </Button>
      </Form.Item>
    </Form>
  );

  // 表格列定义
  const columns = [
    {
      title: '门店',
      dataIndex: 'store_name',
      key: 'store_name',
      width: 150,
    },
    {
      title: '产品',
      dataIndex: 'product_name',
      key: 'product_name',
      width: 200,
      render: (text, record) => (
        <span>
          {text}
          <br />
          <Text type="secondary">{record.product_code}</Text>
        </span>
      ),
    },
    {
      title: '数量',
      dataIndex: 'quantity',
      key: 'quantity',
      width: 100,
      render: (text, record) => (
        <span>
          {text} {record.product_unit || '个'}
        </span>
      ),
    },
    {
      title: '金额',
      dataIndex: 'total_amount',
      key: 'total_amount',
      width: 100,
      render: (text) => `¥${text.toFixed(2)}`,
    },
    {
      title: '原因',
      dataIndex: 'reason',
      key: 'reason',
      width: 120,
      render: (text) => reasonTexts[text] || text,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status) => (
        <Tag color={statusColors[status] || 'default'}>
          {statusTexts[status] || status}
        </Tag>
      ),
    },
    {
      title: '申请人',
      dataIndex: 'creator_name',
      key: 'creator_name',
      width: 120,
    },
    {
      title: '申请时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 180,
      render: (text) => dayjs(text).format('YYYY-MM-DD HH:mm'),
    },
    {
      title: '操作',
      key: 'action',
      width: 180,
      render: (_, record) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => handleViewDetail(record)}
          >
            详情
          </Button>
          
          {record.status === 'pending' && (
            <>
              <Button
                type="link"
                size="small"
                icon={<CheckOutlined />}
                style={{ color: '#52c41a' }}
                onClick={async () => {
                  const detailData = await fetchLossDetail(record.id);
                  if (detailData) {
                    setCurrentRecord(detailData);
                    setApproveVisible(true);
                  }
                }}
              >
                通过
              </Button>
              <Button
                type="link"
                danger
                size="small"
                icon={<CloseOutlined />}
                onClick={async () => {
                  const detailData = await fetchLossDetail(record.id);
                  if (detailData) {
                    setCurrentRecord(detailData);
                    form.resetFields();
                    setRejectVisible(true);
                  }
                }}
              >
                拒绝
              </Button>
            </>
          )}
        </Space>
      ),
    },
  ];

  return (
    <div className="loss-approval-page">
      <Card>
        <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 16 }}>
          <Title level={4}>报损审核</Title>
          <Button
            icon={<ReloadOutlined />}
            onClick={() => fetchLossRecords()}
          >
            刷新
          </Button>
        </div>

        <SearchForm />

        <Table
          columns={columns}
          dataSource={lossRecords}
          rowKey="id"
          pagination={pagination}
          onChange={handleTableChange}
          loading={loading}
          scroll={{ x: 1200 }}
        />
      </Card>

      {/* 详情弹窗 */}
      <Modal
        title="报损详情"
        open={detailVisible}
        onCancel={() => setDetailVisible(false)}
        footer={[
          <Button key="close" onClick={() => setDetailVisible(false)}>
            关闭
          </Button>
        ]}
        width={700}
      >
        {currentRecord && (
          <>
            <Descriptions bordered column={2}>
              <Descriptions.Item label="门店" span={2}>{currentRecord.store_name}</Descriptions.Item>
              <Descriptions.Item label="产品名称" span={2}>{currentRecord.product_name}</Descriptions.Item>
              <Descriptions.Item label="产品编码">{currentRecord.product_code}</Descriptions.Item>
              <Descriptions.Item label="单位">{currentRecord.product_unit || '个'}</Descriptions.Item>
              <Descriptions.Item label="数量">{currentRecord.quantity}</Descriptions.Item>
              <Descriptions.Item label="单价">¥{currentRecord.unit_price.toFixed(2)}</Descriptions.Item>
              <Descriptions.Item label="总金额">¥{currentRecord.total_amount.toFixed(2)}</Descriptions.Item>
              <Descriptions.Item label="报损原因">{reasonTexts[currentRecord.reason] || currentRecord.reason}</Descriptions.Item>
              <Descriptions.Item label="状态">
                <Tag color={statusColors[currentRecord.status] || 'default'}>
                  {statusTexts[currentRecord.status] || currentRecord.status}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="申请人">{currentRecord.creator_name}</Descriptions.Item>
              <Descriptions.Item label="申请时间" span={2}>
                {dayjs(currentRecord.created_at).format('YYYY-MM-DD HH:mm:ss')}
              </Descriptions.Item>
              
              {currentRecord.status === 'approved' && (
                <>
                  <Descriptions.Item label="审核人">{currentRecord.approver_name}</Descriptions.Item>
                  <Descriptions.Item label="审核时间">
                    {dayjs(currentRecord.approved_at).format('YYYY-MM-DD HH:mm:ss')}
                  </Descriptions.Item>
                </>
              )}
              
              {currentRecord.status === 'rejected' && (
                <>
                  <Descriptions.Item label="审核人">{currentRecord.approver_name}</Descriptions.Item>
                  <Descriptions.Item label="审核时间">
                    {dayjs(currentRecord.approved_at).format('YYYY-MM-DD HH:mm:ss')}
                  </Descriptions.Item>
                  <Descriptions.Item label="拒绝原因" span={2}>
                    {currentRecord.reject_reason}
                  </Descriptions.Item>
                </>
              )}
              
              {currentRecord.notes && (
                <Descriptions.Item label="备注" span={2}>
                  {currentRecord.notes}
                </Descriptions.Item>
              )}
            </Descriptions>
            
            {currentRecord.images && currentRecord.images.length > 0 && (
              <>
                <Divider orientation="left">报损图片</Divider>
                <Row gutter={[16, 16]}>
                  {currentRecord.images.map((image, index) => (
                    <Col span={8} key={index}>
                      <Image
                        src={image.url}
                        alt={`报损图片${index + 1}`}
                        style={{ width: '100%', height: 150, objectFit: 'cover' }}
                      />
                    </Col>
                  ))}
                </Row>
              </>
            )}
          </>
        )}
      </Modal>

      {/* 审核通过弹窗 */}
      <Modal
        title="审核通过"
        open={approveVisible}
        onOk={handleApprove}
        onCancel={() => setApproveVisible(false)}
        confirmLoading={loading}
      >
        {currentRecord && (
          <div>
            <p>确认通过以下报损申请？</p>
            <Descriptions column={1} bordered size="small">
              <Descriptions.Item label="门店">{currentRecord.store_name}</Descriptions.Item>
              <Descriptions.Item label="产品">{currentRecord.product_name}</Descriptions.Item>
              <Descriptions.Item label="数量">{currentRecord.quantity} {currentRecord.product_unit || '个'}</Descriptions.Item>
              <Descriptions.Item label="金额">¥{currentRecord.total_amount.toFixed(2)}</Descriptions.Item>
              <Descriptions.Item label="原因">{reasonTexts[currentRecord.reason] || currentRecord.reason}</Descriptions.Item>
            </Descriptions>
          </div>
        )}
      </Modal>

      {/* 审核拒绝弹窗 */}
      <Modal
        title="审核拒绝"
        open={rejectVisible}
        onOk={handleReject}
        onCancel={() => setRejectVisible(false)}
        confirmLoading={loading}
      >
        {currentRecord && (
          <div>
            <p>确认拒绝以下报损申请？</p>
            <Descriptions column={1} bordered size="small" style={{ marginBottom: 16 }}>
              <Descriptions.Item label="门店">{currentRecord.store_name}</Descriptions.Item>
              <Descriptions.Item label="产品">{currentRecord.product_name}</Descriptions.Item>
              <Descriptions.Item label="数量">{currentRecord.quantity} {currentRecord.product_unit || '个'}</Descriptions.Item>
              <Descriptions.Item label="金额">¥{currentRecord.total_amount.toFixed(2)}</Descriptions.Item>
              <Descriptions.Item label="原因">{reasonTexts[currentRecord.reason] || currentRecord.reason}</Descriptions.Item>
            </Descriptions>
            
            <Form form={form}>
              <Form.Item
                name="rejectReason"
                label="拒绝原因"
                rules={[{ required: true, message: '请输入拒绝原因' }]}
              >
                <TextArea rows={4} placeholder="请输入拒绝原因" />
              </Form.Item>
            </Form>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default LossApproval;
