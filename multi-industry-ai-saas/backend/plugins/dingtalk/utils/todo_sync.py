#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
钉钉待办任务同步服务
"""

import logging
import uuid
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, update, desc

from models.user import User
from models.base import Base
from ..models.models import DingTalkTodoTask, DingTalkUserMapping
from .dingtalk_api import DingTalkAPI

# 初始化日志
logger = logging.getLogger(__name__)

class TodoTaskSyncService:
    """钉钉待办任务同步服务"""
    
    def __init__(self, dingtalk_api: DingTalkAPI, db: AsyncSession):
        self.dingtalk_api = dingtalk_api
        self.db = db
        self.logger = logger
    
    async def create_todo_task_in_dingtalk(
        self,
        tenant_id: uuid.UUID,
        project_id: uuid.UUID,
        user_id: uuid.UUID,
        task_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        在钉钉中创建待办任务
        
        Args:
            tenant_id: 租户ID
            project_id: 项目ID
            user_id: 用户ID
            task_data: 任务数据
            
        Returns:
            Dict[str, Any]: 创建结果
        """
        try:
            # 获取用户的钉钉映射
            user_mapping = await self._get_user_dingtalk_mapping(tenant_id, project_id, user_id)
            if not user_mapping:
                return {
                    "success": False,
                    "message": "用户未绑定钉钉账号",
                    "error_code": "USER_NOT_MAPPED"
                }
            
            # 构建钉钉任务数据
            dingtalk_task_data = {
                "userid": user_mapping.dingtalk_user_id,
                "title": task_data.get("title", ""),
                "description": task_data.get("description", ""),
                "url": self._build_task_url(project_id, task_data.get("system_task_id")),
                "priority": task_data.get("priority", "normal"),
                "due_date": task_data.get("due_date"),
                "system_task_id": str(task_data.get("system_task_id", "")),
                "note": task_data.get("note", "")
            }
            
            # 在钉钉中创建任务
            dingtalk_result = await self.dingtalk_api.create_todo_task(dingtalk_task_data)
            if not dingtalk_result:
                return {
                    "success": False,
                    "message": "钉钉待办任务创建失败",
                    "error_code": "DINGTALK_CREATE_FAILED"
                }
            
            # 创建本地映射记录
            todo_task = DingTalkTodoTask(
                id=uuid.uuid4(),
                tenant_id=tenant_id,
                project_id=project_id,
                user_id=user_id,
                system_task_id=task_data.get("system_task_id"),
                dingtalk_record_id=dingtalk_result.get("task_id", dingtalk_result.get("record_id", "")),
                dingtalk_user_id=user_mapping.dingtalk_user_id,
                title=task_data.get("title", ""),
                description=task_data.get("description", ""),
                url=dingtalk_task_data["url"],
                priority=task_data.get("priority", "normal"),
                due_date=task_data.get("due_date"),
                status="pending",
                dingtalk_status=0,
                sync_status="synced",
                last_sync_at=datetime.now(),
                form_data=task_data.get("metadata", {}),
                task_metadata=task_data.get("metadata", {}),
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            
            self.db.add(todo_task)
            await self.db.commit()
            
            logger.info(f"成功为用户 {user_id} 创建钉钉待办任务: {task_data.get('title')}")
            
            return {
                "success": True,
                "message": "钉钉待办任务创建成功",
                "data": {
                    "task_id": str(todo_task.id),
                    "dingtalk_task_id": dingtalk_result.get("task_id"),
                    "dingtalk_record_id": dingtalk_result.get("record_id"),  # 兼容旧版
                    "title": task_data.get("title")
                }
            }
            
        except Exception as e:
            logger.error(f"创建钉钉待办任务失败: {str(e)}")
            await self.db.rollback()
            return {
                "success": False,
                "message": f"创建钉钉待办任务失败: {str(e)}",
                "error_code": "SYSTEM_ERROR"
            }
    
    async def update_todo_task_in_dingtalk(
        self,
        task_id: uuid.UUID,
        task_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        在钉钉中更新待办任务
        
        Args:
            task_id: 任务ID
            task_data: 更新的任务数据
            
        Returns:
            Dict[str, Any]: 更新结果
        """
        try:
            # 获取本地任务记录
            query = select(DingTalkTodoTask).where(DingTalkTodoTask.id == task_id)
            result = await self.db.execute(query)
            todo_task = result.scalar_one_or_none()
            
            if not todo_task:
                return {
                    "success": False,
                    "message": "未找到对应的待办任务",
                    "error_code": "TASK_NOT_FOUND"
                }
            
            # 在钉钉中更新任务 - 使用新版API签名
            dingtalk_result = await self.dingtalk_api.update_todo_task(
                user_id=todo_task.dingtalk_user_id,
                task_id=todo_task.dingtalk_record_id,
                task_data=task_data
            )
            
            if not dingtalk_result:
                return {
                    "success": False,
                    "message": "钉钉待办任务更新失败",
                    "error_code": "DINGTALK_UPDATE_FAILED"
                }
            
            # 更新本地记录
            if "title" in task_data:
                todo_task.title = task_data["title"]
            if "description" in task_data:
                todo_task.description = task_data["description"]
            if "priority" in task_data:
                todo_task.priority = task_data["priority"]
            if "due_date" in task_data:
                todo_task.due_date = task_data["due_date"]
            if "url" in task_data:
                todo_task.url = task_data["url"]
            
            todo_task.updated_at = datetime.now()
            todo_task.last_sync_at = datetime.now()
            todo_task.sync_status = "synced"
            
            await self.db.commit()
            
            logger.info(f"成功更新钉钉待办任务: {task_id}")
            
            return {
                "success": True,
                "message": "钉钉待办任务更新成功",
                "data": {
                    "task_id": str(task_id),
                    "title": todo_task.title
                }
            }
            
        except Exception as e:
            logger.error(f"更新钉钉待办任务失败: {str(e)}")
            await self.db.rollback()
            return {
                "success": False,
                "message": f"更新钉钉待办任务失败: {str(e)}",
                "error_code": "SYSTEM_ERROR"
            }
    
    async def complete_todo_task(
        self,
        task_id: uuid.UUID,
        completed_by: str
    ) -> Dict[str, Any]:
        """
        完成待办任务
        
        Args:
            task_id: 任务ID
            completed_by: 完成者
            
        Returns:
            Dict[str, Any]: 完成结果
        """
        try:
            # 获取本地任务记录
            query = select(DingTalkTodoTask).where(DingTalkTodoTask.id == task_id)
            result = await self.db.execute(query)
            todo_task = result.scalar_one_or_none()
            
            if not todo_task:
                return {
                    "success": False,
                    "message": "未找到对应的待办任务",
                    "error_code": "TASK_NOT_FOUND"
                }
            
            # 在钉钉中完成任务 - 使用新版API签名
            dingtalk_result = await self.dingtalk_api.complete_todo_task(
                user_id=todo_task.dingtalk_user_id,
                task_id=todo_task.dingtalk_record_id
            )
            
            if not dingtalk_result:
                return {
                    "success": False,
                    "message": "钉钉待办任务完成失败",
                    "error_code": "DINGTALK_COMPLETE_FAILED"
                }
            
            # 更新本地记录
            todo_task.status = "completed"
            todo_task.dingtalk_status = 1  # 钉钉完成状态
            todo_task.completed_at = datetime.now()
            todo_task.completed_by = completed_by
            todo_task.updated_at = datetime.now()
            todo_task.last_sync_at = datetime.now()
            todo_task.sync_status = "synced"
            
            await self.db.commit()
            
            logger.info(f"成功完成钉钉待办任务: {task_id}")
            
            return {
                "success": True,
                "message": "钉钉待办任务完成成功",
                "data": {
                    "task_id": str(task_id),
                    "title": todo_task.title,
                    "completed_at": todo_task.completed_at.isoformat()
                }
            }
            
        except Exception as e:
            logger.error(f"完成钉钉待办任务失败: {str(e)}")
            await self.db.rollback()
            return {
                "success": False,
                "message": f"完成钉钉待办任务失败: {str(e)}",
                "error_code": "SYSTEM_ERROR"
            }
    
    async def sync_todo_tasks_from_dingtalk(
        self,
        tenant_id: uuid.UUID,
        project_id: uuid.UUID,
        user_id: uuid.UUID,
        dingtalk_user_id: str  # 这应该是unionId
    ) -> Dict[str, Any]:
        """
        从钉钉同步待办任务
        
        Args:
            tenant_id: 租户ID
            project_id: 项目ID
            user_id: 系统用户ID
            dingtalk_user_id: 钉钉用户unionId
        
        Returns:
            Dict[str, Any]: 同步结果
        """
        try:
            self.logger.info(f"🔄 开始同步待办任务，用户unionId: {dingtalk_user_id}")
            
            # 查询钉钉待办任务，按状态分别查询
            all_tasks = []
            total_count = 0
            
            # 分别查询未完成和已完成的任务
            for status in ["NEW", "DONE"]:
                try:
                    self.logger.info(f"📋 查询状态为 {status} 的任务...")
                    
                    # 使用正确的API参数，直接按状态查询
                    tasks_result = await self.dingtalk_api.get_todo_tasks(
                        user_id=dingtalk_user_id,
                        status=status,  # 直接指定状态
                        limit=50
                    )
                    
                    if tasks_result and tasks_result.get('success'):
                        tasks = tasks_result.get('tasks', [])
                        todo_cards = tasks_result.get('todoCards', [])
                        
                        # 优先使用todoCards，如果没有则使用tasks
                        actual_tasks = todo_cards if todo_cards else tasks
                        all_tasks.extend(actual_tasks)
                        total_count += len(actual_tasks)
                        
                        self.logger.info(f"✅ 查询状态 {status} 成功，获得 {len(actual_tasks)} 个任务")
                        self.logger.info(f"   - tasks字段: {len(tasks)} 个")
                        self.logger.info(f"   - todoCards字段: {len(todo_cards)} 个")
                        
                        # 显示任务详情供调试
                        for task in actual_tasks[:3]:  # 只显示前3个
                            task_id = task.get('taskId', 'Unknown')
                            subject = task.get('subject', 'Unknown')
                            created_time = task.get('createdTime', 'Unknown')
                            is_done = task.get('isDone', 'Unknown')
                            self.logger.info(f"  📝 任务: {subject} (ID: {task_id}, 完成: {is_done})")
                    else:
                        error_msg = tasks_result.get('error', 'Unknown') if tasks_result else 'API返回None'
                        self.logger.warning(f"⚠️ 查询状态 {status} 失败: {error_msg}")
                        
                except Exception as e:
                    self.logger.error(f"❌ 查询状态 {status} 异常: {str(e)}")
            
            self.logger.info(f"📊 总共获取到 {total_count} 个待办任务")
            
            if total_count == 0:
                self.logger.warning("⚠️ 没有获取到任何待办任务，可能原因：")
                self.logger.warning("  1. 用户确实没有待办任务")
                self.logger.warning("  2. unionId不正确")
                self.logger.warning("  3. API参数不正确")
                self.logger.warning("  4. 权限问题")
                
                return {
                    "success": True,
                    "message": f"同步完成，但未获取到任何待办任务",
                    "data": {
                        "synced_count": 0,
                        "total_tasks": 0,
                        "api_called": True,
                        "union_id_used": dingtalk_user_id
                    }
                }
            
            # 同步到本地数据库
            synced_count = 0
            
            for task in all_tasks:
                try:
                    task_id = task.get('taskId')
                    if not task_id:
                        continue
                    
                    # 检查任务是否已存在
                    existing_query = select(DingTalkTodoTask).where(
                        and_(
                            DingTalkTodoTask.tenant_id == tenant_id,
                            DingTalkTodoTask.project_id == project_id,
                            DingTalkTodoTask.dingtalk_task_id == task_id
                        )
                    )
                    result = await self.db.execute(existing_query)
                    existing_task = result.scalar_one_or_none()
                    
                    # 解析任务数据
                    subject = task.get('subject', '未知任务')
                    description = task.get('description', '')
                    is_done = task.get('isDone', False)
                    created_time_str = task.get('createdTime')
                    due_time_str = task.get('dueTime')
                    
                    # 解析时间
                    created_time = None
                    due_time = None
                    
                    if created_time_str:
                        try:
                            # 钉钉时间格式通常是毫秒时间戳
                            if isinstance(created_time_str, (int, str)) and str(created_time_str).isdigit():
                                timestamp = int(created_time_str)
                                if timestamp > 1000000000000:  # 毫秒时间戳
                                    timestamp = timestamp / 1000
                                created_time = datetime.fromtimestamp(timestamp)
                        except Exception as e:
                            self.logger.warning(f"解析创建时间失败: {created_time_str}, {e}")
                    
                    if due_time_str:
                        try:
                            if isinstance(due_time_str, (int, str)) and str(due_time_str).isdigit():
                                timestamp = int(due_time_str)
                                if timestamp > 1000000000000:  # 毫秒时间戳
                                    timestamp = timestamp / 1000
                                due_time = datetime.fromtimestamp(timestamp)
                        except Exception as e:
                            self.logger.warning(f"解析截止时间失败: {due_time_str}, {e}")
                    
                    if existing_task:
                        # 更新现有任务
                        existing_task.subject = subject
                        existing_task.description = description
                        existing_task.is_done = is_done
                        existing_task.due_time = due_time
                        existing_task.updated_at = datetime.now()
                        existing_task.raw_data = task
                    else:
                        # 创建新任务
                        new_task = DingTalkTodoTask(
                            id=uuid.uuid4(),
                            tenant_id=tenant_id,
                            project_id=project_id,
                            user_id=user_id,
                            dingtalk_task_id=task_id,
                            dingtalk_record_id=task_id,  # 使用task_id作为record_id
                            dingtalk_user_id=dingtalk_user_id,
                            subject=subject,
                            title=subject,  # 添加title字段
                            description=description,
                            is_done=is_done,
                            created_time=created_time or datetime.now(),
                            due_time=due_time,
                            raw_data=task,
                            status="pending",  # 添加状态
                            dingtalk_status=0,  # 添加钉钉状态
                            sync_status="synced",  # 添加同步状态
                            created_at=datetime.now(),
                            updated_at=datetime.now()
                        )
                        self.db.add(new_task)
                    
                    synced_count += 1
                    
                except Exception as e:
                    self.logger.error(f"同步任务失败 {task.get('taskId', 'Unknown')}: {str(e)}")
            
            # 提交数据库更改
            await self.db.commit()
            
            self.logger.info(f"✅ 待办任务同步完成，同步数量: {synced_count}/{total_count}")
            
            return {
                "success": True,
                "message": f"成功同步 {synced_count} 个待办任务",
                "data": {
                    "synced_count": synced_count,
                    "total_tasks": total_count,
                    "union_id_used": dingtalk_user_id,
                    "api_success": True
                }
            }
            
        except Exception as e:
            self.logger.error(f"❌ 待办任务同步异常: {str(e)}")
            await self.db.rollback()
            return {
                "success": False,
                "message": f"同步失败: {str(e)}",
                "data": {
                    "synced_count": 0,
                    "total_tasks": 0,
                    "error_details": str(e)
                }
            }
    
    def _convert_dingtalk_priority(self, priority: int) -> str:
        """
        转换钉钉优先级到本地格式
        
        Args:
            priority: 钉钉优先级数字
            
        Returns:
            str: 本地优先级字符串
        """
        priority_map = {
            10: "low",
            20: "normal",
            30: "high",
            40: "urgent"
        }
        return priority_map.get(priority, "normal")
    
    def _parse_due_date(self, due_time: str) -> Optional[datetime]:
        """
        解析钉钉返回的截止时间
        
        Args:
            due_time: 钉钉时间字符串
            
        Returns:
            datetime: 解析后的时间
        """
        if not due_time:
            return None
        
        try:
            # 尝试解析ISO格式时间
            if 'T' in due_time:
                return datetime.fromisoformat(due_time.replace('Z', '+00:00'))
            else:
                return datetime.fromisoformat(due_time)
        except Exception as e:
            logger.warning(f"解析钉钉时间失败: {due_time}, {str(e)}")
            return None
    
    def _build_task_url(self, project_id: uuid.UUID, system_task_id: Optional[uuid.UUID]) -> str:
        """构建任务详情URL"""
        base_url = "https://yourdomain.com"  # 替换为实际域名
        if system_task_id:
            return f"{base_url}/project/{project_id}/tasks/{system_task_id}"
        else:
            return f"{base_url}/project/{project_id}/tasks"
    
    def _build_form_items(self, task_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """构建钉钉表单项"""
        form_items = []
        
        # 任务描述
        if task_data.get("description"):
            form_items.append({
                "title": "任务描述",
                "content": task_data["description"]
            })
        
        # 优先级
        if task_data.get("priority"):
            priority_map = {
                "low": "低",
                "normal": "普通", 
                "high": "高",
                "urgent": "紧急"
            }
            form_items.append({
                "title": "优先级",
                "content": priority_map.get(task_data["priority"], task_data["priority"])
            })
        
        # 截止时间
        if task_data.get("due_date"):
            due_date = task_data["due_date"]
            if isinstance(due_date, str):
                form_items.append({
                    "title": "截止时间",
                    "content": due_date
                })
            elif hasattr(due_date, 'strftime'):
                form_items.append({
                    "title": "截止时间",
                    "content": due_date.strftime("%Y-%m-%d %H:%M")
                })
        
        # 其他自定义字段
        metadata = task_data.get("metadata", {})
        for key, value in metadata.items():
            if isinstance(value, (str, int, float)):
                form_items.append({
                    "title": key,
                    "content": str(value)
                })
        
        return form_items
    
    async def _get_user_dingtalk_mapping(
        self,
        tenant_id: uuid.UUID,
        project_id: uuid.UUID,
        user_id: uuid.UUID
    ) -> Optional[DingTalkUserMapping]:
        """获取用户的钉钉映射"""
        query = select(DingTalkUserMapping).where(
            and_(
                DingTalkUserMapping.tenant_id == tenant_id,
                DingTalkUserMapping.project_id == project_id,
                DingTalkUserMapping.user_id == user_id,
                DingTalkUserMapping.is_active == True
            )
        )
        result = await self.db.execute(query)
        return result.scalar_one_or_none()

    async def get_todo_task_from_dingtalk(
        self,
        dingtalk_task_id: str,
        user_id: str
    ) -> Dict[str, Any]:
        """
        从钉钉获取待办任务详情
        
        Args:
            dingtalk_task_id: 钉钉任务ID
            user_id: 用户ID
            
        Returns:
            Dict[str, Any]: 任务详情结果
        """
        try:
            result = await self.dingtalk_api.get_todo_task(dingtalk_task_id, user_id)
            
            if result:
                return {
                    "success": True,
                    "data": result,
                    "message": "获取任务详情成功"
                }
            else:
                return {
                    "success": False,
                    "message": "获取任务详情失败",
                    "error_code": "API_CALL_FAILED"
                }
            
        except Exception as e:
            logger.error(f"获取钉钉待办任务详情异常: {str(e)}")
            return {
                "success": False,
                "message": f"获取任务详情异常: {str(e)}",
                "error_code": "EXCEPTION"
            }
    
    async def delete_todo_task_from_dingtalk(
        self,
        task_id: uuid.UUID
    ) -> Dict[str, Any]:
        """
        从钉钉删除待办任务
        
        Args:
            task_id: 本地任务ID
            
        Returns:
            Dict[str, Any]: 删除结果
        """
        try:
            # 获取本地任务记录
            query = select(DingTalkTodoTask).where(DingTalkTodoTask.id == task_id)
            result = await self.db.execute(query)
            todo_task = result.scalar_one_or_none()
            
            if not todo_task:
                return {
                    "success": False,
                    "message": "未找到对应的待办任务",
                    "error_code": "TASK_NOT_FOUND"
                }
            
            # 获取用户映射
            user_mapping_query = select(DingTalkUserMapping).where(
                DingTalkUserMapping.tenant_id == todo_task.tenant_id,
                DingTalkUserMapping.project_id == todo_task.project_id
            )
            user_mapping_result = await self.db.execute(user_mapping_query)
            user_mapping = user_mapping_result.scalar_one_or_none()
            
            if not user_mapping:
                return {
                    "success": False,
                    "message": "未找到钉钉用户映射",
                    "error_code": "USER_MAPPING_NOT_FOUND"
                }
            
            # 从钉钉删除任务 - 使用新版API
            delete_success = await self.dingtalk_api.delete_todo_task(
                todo_task.dingtalk_task_id,
                user_mapping.dingtalk_user_id
            )
            
            if delete_success:
                # 删除本地记录
                await self.db.delete(todo_task)
                await self.db.commit()
                
                return {
                    "success": True,
                    "message": "任务删除成功",
                    "data": {
                        "task_id": str(task_id),
                        "dingtalk_task_id": todo_task.dingtalk_task_id
                    }
                }
            else:
                return {
                    "success": False,
                    "message": "钉钉任务删除失败",
                    "error_code": "DINGTALK_DELETE_FAILED"
                }
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"删除钉钉待办任务异常: {str(e)}")
            return {
                "success": False,
                "message": f"删除任务异常: {str(e)}",
                "error_code": "EXCEPTION"
            }
    
    def _convert_dingtalk_priority(self, priority: int) -> str:
        """
        转换钉钉优先级为本地格式
        
        Args:
            priority: 钉钉优先级数字
            
        Returns:
            str: 本地优先级
        """
        if priority >= 30:
            return "high"
        elif priority >= 20:
            return "normal"
        else:
            return "low"

    async def get_local_todo_tasks(
        self,
        tenant_id: uuid.UUID,
        project_id: uuid.UUID,
        user_id: Optional[uuid.UUID] = None,
        is_done: Optional[bool] = None,
        limit: int = 20,
        offset: int = 0
    ) -> Dict[str, Any]:
        """获取本地同步的待办任务"""
        try:
            query = select(DingTalkTodoTask).where(
                and_(
                    DingTalkTodoTask.tenant_id == tenant_id,
                    DingTalkTodoTask.project_id == project_id
                )
            )
            
            if user_id:
                query = query.where(DingTalkTodoTask.user_id == user_id)
            
            if is_done is not None:
                query = query.where(DingTalkTodoTask.is_done == is_done)
            
            # 排序和分页
            query = query.order_by(desc(DingTalkTodoTask.created_time)).offset(offset).limit(limit)
            
            result = await self.db.execute(query)
            tasks = result.scalars().all()
            
            tasks_data = []
            for task in tasks:
                tasks_data.append({
                    "id": str(task.id),
                    "dingtalk_task_id": task.dingtalk_task_id,
                    "subject": task.subject,
                    "description": task.description,
                    "is_done": task.is_done,
                    "created_time": task.created_time.isoformat() if task.created_time else None,
                    "due_time": task.due_time.isoformat() if task.due_time else None,
                    "updated_at": task.updated_at.isoformat() if task.updated_at else None
                })
            
            return {
                "success": True,
                "data": {
                    "tasks": tasks_data,
                    "total": len(tasks_data)
                }
            }
            
        except Exception as e:
            self.logger.error(f"获取本地待办任务失败: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "data": {"tasks": [], "total": 0}
            } 