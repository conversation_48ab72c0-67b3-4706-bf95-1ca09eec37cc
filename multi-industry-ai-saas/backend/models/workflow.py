from sqlalchemy import Column, String, Boolean, DateTime, ForeignKey, JSON, Text, Integer, UniqueConstraint
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid

from db.database import Base

class Workflow(Base):
    """工作流模型"""
    __tablename__ = "workflows"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=True)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id"), nullable=False)
    name = Column(String(100), nullable=False)
    code = Column(String(50), nullable=False)
    description = Column(Text, nullable=True)
    type = Column(String(50), nullable=False)  # loss, purchase, etc.
    is_default = Column(Boolean, default=False)
    is_active = Column(Boolean, default=True)
    config = Column(JSON, nullable=False)  # 工作流配置，包含节点和流转规则
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    created_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)
    updated_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)

    # 关系
    tenant = relationship("Tenant", backref="workflows")
    project = relationship("Project", backref="workflows")
    creator = relationship("User", foreign_keys=[created_by])
    updater = relationship("User", foreign_keys=[updated_by])
    nodes = relationship("WorkflowNode", back_populates="workflow", cascade="all, delete-orphan")

    __table_args__ = (
        UniqueConstraint('project_id', 'code', name='uq_workflow_code_per_project'),
    )

    def __repr__(self):
        return f"<Workflow(id={self.id}, name='{self.name}', type='{self.type}')>"


class WorkflowNode(Base):
    """工作流节点模型"""
    __tablename__ = "workflow_nodes"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    workflow_id = Column(UUID(as_uuid=True), ForeignKey("workflows.id"), nullable=False)
    name = Column(String(100), nullable=False)
    code = Column(String(50), nullable=False)
    description = Column(Text, nullable=True)
    type = Column(String(50), nullable=False)  # start, approval, end, etc.
    order = Column(Integer, nullable=False)
    config = Column(JSON, nullable=False)  # 节点配置，包含审批人、条件等
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 关系
    workflow = relationship("Workflow", back_populates="nodes")
    transitions = relationship("WorkflowTransition", foreign_keys="[WorkflowTransition.source_node_id]", back_populates="source_node", cascade="all, delete-orphan")

    __table_args__ = (
        UniqueConstraint('workflow_id', 'code', name='uq_workflow_node_code_per_workflow'),
    )

    def __repr__(self):
        return f"<WorkflowNode(id={self.id}, name='{self.name}', type='{self.type}')>"


class WorkflowTransition(Base):
    """工作流转换模型"""
    __tablename__ = "workflow_transitions"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    workflow_id = Column(UUID(as_uuid=True), ForeignKey("workflows.id"), nullable=False)
    source_node_id = Column(UUID(as_uuid=True), ForeignKey("workflow_nodes.id"), nullable=False)
    target_node_id = Column(UUID(as_uuid=True), ForeignKey("workflow_nodes.id"), nullable=False)
    name = Column(String(100), nullable=False)
    condition = Column(JSON, nullable=True)  # 转换条件
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 关系
    workflow = relationship("Workflow")
    source_node = relationship("WorkflowNode", foreign_keys=[source_node_id], back_populates="transitions")
    target_node = relationship("WorkflowNode", foreign_keys=[target_node_id])

    def __repr__(self):
        return f"<WorkflowTransition(id={self.id}, name='{self.name}')>"


class WorkflowInstance(Base):
    """工作流实例模型"""
    __tablename__ = "workflow_instances"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=True)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id"), nullable=False)
    workflow_id = Column(UUID(as_uuid=True), ForeignKey("workflows.id"), nullable=False)
    reference_id = Column(UUID(as_uuid=True), nullable=False)  # 关联的业务单据ID
    reference_type = Column(String(50), nullable=False)  # 关联的业务单据类型
    current_node_id = Column(UUID(as_uuid=True), ForeignKey("workflow_nodes.id"), nullable=False)
    status = Column(String(20), default="active", nullable=False)  # active, completed, terminated
    data = Column(JSON, nullable=True)  # 工作流实例数据
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    created_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)
    updated_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)

    # 关系
    tenant = relationship("Tenant", backref="workflow_instances")
    project = relationship("Project", backref="workflow_instances")
    workflow = relationship("Workflow")
    current_node = relationship("WorkflowNode")
    creator = relationship("User", foreign_keys=[created_by])
    updater = relationship("User", foreign_keys=[updated_by])
    logs = relationship("WorkflowLog", back_populates="instance", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<WorkflowInstance(id={self.id}, workflow_id='{self.workflow_id}', status='{self.status}')>"


class WorkflowLog(Base):
    """工作流日志模型"""
    __tablename__ = "workflow_logs"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    instance_id = Column(UUID(as_uuid=True), ForeignKey("workflow_instances.id"), nullable=False)
    node_id = Column(UUID(as_uuid=True), ForeignKey("workflow_nodes.id"), nullable=False)
    transition_id = Column(UUID(as_uuid=True), ForeignKey("workflow_transitions.id"), nullable=True)
    action = Column(String(50), nullable=False)  # start, approve, reject, etc.
    comment = Column(Text, nullable=True)
    data = Column(JSON, nullable=True)  # 操作相关数据
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    created_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)

    # 关系
    instance = relationship("WorkflowInstance", back_populates="logs")
    node = relationship("WorkflowNode")
    transition = relationship("WorkflowTransition")
    user = relationship("User", foreign_keys=[created_by])

    def __repr__(self):
        return f"<WorkflowLog(id={self.id}, action='{self.action}')>"
