# 系统初始化说明

## 概述

系统初始化完成后，会自动创建以下默认数据：

## 默认账户

### 1. 系统管理员（超级管理员）
- **用户名**: `admin`
- **邮箱**: `<EMAIL>`
- **密码**: `admin123`
- **角色**: 系统管理员
- **权限**: 拥有系统最高权限，可以管理所有服务商和租户

### 2. 服务商管理员
- **用户名**: `service_admin`
- **邮箱**: `<EMAIL>`
- **密码**: `service123`
- **角色**: 服务商管理员
- **权限**: 可以管理所属服务商下的所有租户

### 3. 租户管理员
- **用户名**: `tenant_admin`
- **邮箱**: `<EMAIL>`
- **密码**: `tenant123`
- **角色**: 租户管理员
- **权限**: 可以管理所属租户的所有数据和用户

## 默认数据

### 行业数据
系统会自动创建以下行业：
- 零售业 (retail)
- 餐饮业 (restaurant)
- 酒店业 (hotel)
- 美容美发业 (beauty)
- 教育培训业 (education)
- 医疗健康业 (healthcare)

### 系统角色
- `system_admin`: 系统管理员
- `service_provider_admin`: 服务商管理员
- `tenant_admin`: 租户管理员

### 默认服务商
- **名称**: Default Service Provider
- **公司名称**: 默认服务商公司
- **联系人**: 系统管理员
- **支持行业**: 所有行业
- **分成比例**: 15%

### 默认租户
- **名称**: Default Tenant
- **行业类型**: 零售业 (retail)
- **最大项目数**: 5
- **最大用户数**: 50
- **最大存储空间**: 10GB
- **时区**: Asia/Shanghai
- **语言**: zh-CN
- **货币**: CNY

## 数据库Schema

### 公共Schema (public)
存储系统级别的数据：
- 用户表 (users)
- 服务商表 (service_providers)
- 租户表 (tenants)
- 行业表 (industries)
- 角色表 (roles)
- 等等...

### 租户Schema
每个租户都有独立的Schema，命名规则：`tenant_{tenant_id_without_hyphens}`

默认租户的Schema包含零售业特定的表：
- 商品表 (products)
- 商品分类表 (product_categories)
- 门店表 (stores)
- 库存表 (inventory)
- 销售表 (sales)
- 销售明细表 (sale_items)

## 初始化命令

### 运行完整初始化
```bash
python -m db.migrations
```

### 测试初始化
```bash
python test_migrations.py
```

## 安全建议

⚠️ **重要**: 在生产环境中，请务必：

1. **修改默认密码**: 立即修改所有默认账户的密码
2. **删除不需要的账户**: 删除不需要的默认账户
3. **配置强密码策略**: 设置密码复杂度要求
4. **启用双因素认证**: 为管理员账户启用2FA
5. **定期审计**: 定期检查用户权限和访问日志

## 故障排除

### 常见问题

1. **初始化失败**
   - 检查数据库连接配置
   - 确保数据库用户有创建表和Schema的权限
   - 查看日志文件获取详细错误信息

2. **Schema创建失败**
   - 确保PostgreSQL版本支持Schema功能
   - 检查数据库用户权限

3. **JSON字段错误**
   - 确保使用PostgreSQL数据库
   - 检查JSON数据格式是否正确

### 日志查看
系统初始化过程中的详细日志会输出到控制台，包括：
- 表创建状态
- 数据插入结果
- 错误信息和堆栈跟踪

## 下一步

初始化完成后，您可以：

1. 登录系统管理后台
2. 创建新的服务商和租户
3. 配置系统参数
4. 导入业务数据
5. 创建业务用户

## 联系支持

如果在初始化过程中遇到问题，请：
1. 查看本文档的故障排除部分
2. 检查系统日志
3. 联系技术支持团队 