#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
MCP Server - Multi-Protocol Support V2 (using updated mcp.base)
"""

import asyncio
import json as _json # Using _json to avoid conflict if a variable is named json
import logging
import os
import uuid
import httpx
from typing import Any, Callable, Coroutine, Dict, List, Optional, Union

from fastapi import WebSocket, HTTPException # HTTPException is needed

# 从 .models 导入 (如果 MCPSession 等在这里定义，或者从其他地方导入)
# 假设 MCPSession 是一个 dataclass 或者 Pydantic model
from pydantic import BaseModel, Field
from enum import Enum
from jose import jwt, JWTError

# --- Constants and Configuration ---
SERVER_VERSION = "1.0.0"
SERVER_NAME = os.environ.get("MCP_SERVER_NAME", "MCP Server")
# MCP协议版本 - 使用时间戳格式，与MCP标准一致
PROTOCOL_VERSION = "2024-11-05"
# API_BASE_URL is set by mcp_server_adapter.py in the instance's environment
API_BASE_URL = os.environ.get("API_BASE_URL", "http://localhost:8000")

logger = logging.getLogger(__name__)
# logger.setLevel(logging.DEBUG) # Uncomment for verbose logging in MCP instance

# --- Enums and Models ---
class ErrorCode(Enum):
    GENERIC_ERROR = 1000
    AUTHENTICATION_FAILED = 1001
    AUTHORIZATION_FAILED = 1002
    INITIALIZATION_FAILED = 1003
    INVALID_REQUEST = 1004
    TOOL_NOT_FOUND = 1005
    TOOL_EXECUTION_ERROR = 1006
    SESSION_NOT_FOUND = 1007 # Added for clarity
    SESSION_NOT_INITIALIZED = 1008 # Added for clarity
    METHOD_NOT_FOUND = 1009 # Added
    API_ERROR = 1010 # Added for API call errors
    NETWORK_ERROR = 1011 # Added for network errors
    JSON_DECODE_ERROR = 1012 # Added for JSON parsing errors

# 改进的JSON序列化函数，确保UTF-8编码
def safe_json_dumps(obj: Any, **kwargs) -> str:
    """安全的JSON序列化，确保UTF-8编码和中文字符正确显示"""
    kwargs.setdefault('ensure_ascii', False)
    kwargs.setdefault('separators', (',', ':'))
    try:
        return _json.dumps(obj, **kwargs)
    except (TypeError, ValueError) as e:
        logger.warning(f"JSON序列化失败: {e}, 使用str()作为备选")
        return str(obj)

# 错误信息安全过滤函数
def sanitize_error_message(error_msg: str, max_length: int = 500) -> str:
    """过滤和截断错误信息，避免暴露敏感信息"""
    if not error_msg:
        return "未知错误"
    
    # 移除可能的敏感信息
    sensitive_patterns = [
        r'Bearer\s+[A-Za-z0-9\-_]+',  # JWT tokens
        r'password["\s]*[:=]["\s]*[^"\s,}]+',  # passwords
        r'secret["\s]*[:=]["\s]*[^"\s,}]+',  # secrets
        r'key["\s]*[:=]["\s]*[^"\s,}]+',  # keys
    ]
    
    import re
    sanitized = error_msg
    for pattern in sensitive_patterns:
        sanitized = re.sub(pattern, '[REDACTED]', sanitized, flags=re.IGNORECASE)
    
    # 截断过长的错误信息
    if len(sanitized) > max_length:
        sanitized = sanitized[:max_length] + "..."
    
    return sanitized

# 从JWT令牌中提取项目ID的函数
def extract_project_id_from_token(token: str) -> Optional[str]:
    """从JWT令牌中提取项目ID"""
    if not token:
        return None
    
    try:
        # 简单解码JWT令牌（不验证签名，因为这里只是提取信息）
        import base64
        parts = token.split('.')
        if len(parts) != 3:
            return None
        
        # 解码payload部分
        payload_part = parts[1]
        # 添加必要的填充
        missing_padding = len(payload_part) % 4
        if missing_padding:
            payload_part += '=' * (4 - missing_padding)
        
        payload_bytes = base64.urlsafe_b64decode(payload_part)
        payload = _json.loads(payload_bytes.decode('utf-8'))
        
        project_id = payload.get('project_id')
        if project_id:
            logger.debug(f"从JWT令牌中提取到项目ID: {project_id}")
            return str(project_id)
        
    except Exception as e:
        logger.warning(f"从JWT令牌中提取项目ID失败: {e}")
    
    return None

class MCPSession(BaseModel):
    _SENTINEL_CLOSE_QUEUE_ = object() # Sentinel for closing SSE queue

    session_id: str
    user_id: Optional[str] = None
    project_id: Optional[str] = None
    system_token: Optional[str] = None
    permissions: List[str] = Field(default_factory=list)
    token_payload: Optional[Dict[str, Any]] = None
    server_id: str
    client_info: Optional[Dict[str, Any]] = None
    last_activity_time: float = Field(default_factory=lambda: asyncio.get_event_loop().time())
    is_initialized: bool = False
    internal_message_queue: Optional[asyncio.Queue] = None # For SSE streaming
    heartbeat_interval: int = 30 # Interval for SSE ping/keepalive, also for queue read timeout

    class Config:
        arbitrary_types_allowed = True # Allow asyncio.Queue

# --- Core Authentication (Modified for initialize flow) ---
async def create_new_session_from_auth_headers(server_id: str, headers: Dict[str, str], client_info: Optional[Dict[str,Any]]=None) -> MCPSession:
    """
    Authenticates and creates a new MCPSession based on headers from Traefik auth middleware.
    This function expects Traefik to have already processed authentication and added the required headers.
    """
    logger.debug(f"[create_new_session] Attempting for server_id: {server_id} using headers: {list(headers.keys())}")

    # Expect Traefik-processed headers (these should be present after auth middleware)
    MCP_SYSTEM_TOKEN_HEADER = "x-mcp-system-token"
    user_id_header = "x-mcp-user-id"
    project_id_header = "x-mcp-project-id"
    permissions_header = "x-mcp-permissions"

    system_token = headers.get(MCP_SYSTEM_TOKEN_HEADER.lower()) or headers.get(MCP_SYSTEM_TOKEN_HEADER)
    user_id = headers.get(user_id_header.lower()) or headers.get(user_id_header)
    project_id = headers.get(project_id_header.lower()) or headers.get(project_id_header)
    permissions_str = headers.get(permissions_header.lower()) or headers.get(permissions_header)

    # Validate required headers from Traefik auth middleware
    if not system_token:
        logger.error(f"[create_new_session] Missing {MCP_SYSTEM_TOKEN_HEADER} header from Traefik auth middleware")
        raise ValueError(f"Missing {MCP_SYSTEM_TOKEN_HEADER} header - authentication failed")

    if not user_id:
        logger.error(f"[create_new_session] Missing {user_id_header} header from Traefik auth middleware")
        raise ValueError(f"Missing {user_id_header} header - authentication failed")

    # Parse permissions
    permissions: List[str] = []
    if permissions_str:
        try:
            permissions = _json.loads(permissions_str)
            if not isinstance(permissions, list):
                logger.warning(f"[create_new_session] Invalid format for permissions: expected list, got {type(permissions)}. Defaulting to [].")
                permissions = []
        except _json.JSONDecodeError:
            logger.warning(f"[create_new_session] Failed to decode permissions: {permissions_str}. Defaulting to [].")
            permissions = []
    else:
        logger.debug(f"[create_new_session] No permissions header from Traefik. Using empty list.")

    new_session_id = str(uuid.uuid4())
    logger.info(f"[create_new_session] New session {new_session_id} created via Traefik auth. User: {user_id}, Proj: {project_id or 'None'}, Perms: {permissions}")

    return MCPSession(
        session_id=new_session_id,
        user_id=user_id,
        project_id=project_id,
        system_token=system_token,
        permissions=permissions,
        token_payload=None,
        server_id=server_id,
        client_info=client_info,
        internal_message_queue=asyncio.Queue()
    )

# --- MCP Server Logic ---
class MCPServer:
    """MCP 服务器基类 V2，管理会话、处理请求、工具"""

    def __init__(self, server_id: str, session_ttl: int = 3600, tool_cache_ttl: int = 300):
        self.server_id = server_id
        self.session_store: Dict[str, MCPSession] = {}
        self.session_ttl = session_ttl # 会话超时时间（秒）
        self.is_initialized_for_session: Dict[str, bool] = {} # 跟踪会话是否已初始化

        # 工具缓存
        self.tool_metadata_cache: Optional[List[Dict[str, Any]]] = None
        self.tool_cache_last_updated: float = 0.0 # 上次更新时间戳
        self.tool_cache_ttl = tool_cache_ttl # 缓存有效期（秒）
        self.tool_refresh_lock = asyncio.Lock() # 防止并发刷新工具

        # 存储已注册的工具函数
        self.tool_functions: Dict[str, Callable[[MCPSession, Dict[str, Any]], Coroutine[Any, Any, Any]]] = {}

        # 回调函数列表
        self.on_initialize_callbacks: List[Callable] = []
        self.on_shutdown_callbacks: List[Callable] = []
        self.on_request_callbacks: List[Callable] = [] # 通用请求回调
        self.on_tool_call_callbacks: List[Callable] = [] # 特定于工具调用的回调

        # 超时和重试配置
        self.timeout_config = {
            "api_request_timeout": float(os.environ.get("MCP_API_REQUEST_TIMEOUT", "30.0")),
            "redis_operation_timeout": float(os.environ.get("MCP_REDIS_OPERATION_TIMEOUT", "10.0")),
            "tool_load_timeout": float(os.environ.get("MCP_TOOL_LOAD_TIMEOUT", "60.0")),
            "health_check_timeout": float(os.environ.get("MCP_HEALTH_CHECK_TIMEOUT", "15.0"))
        }
        
        self.retry_config = {
            "max_retries": int(os.environ.get("MCP_MAX_RETRIES", "3")),
            "retry_delay": float(os.environ.get("MCP_RETRY_DELAY", "2.0")),
            "backoff_multiplier": float(os.environ.get("MCP_BACKOFF_MULTIPLIER", "2.0")),
            "max_retry_delay": float(os.environ.get("MCP_MAX_RETRY_DELAY", "30.0"))
        }
        
        # Redis重连配置
        self.redis_config = {
            "reconnect_attempts": int(os.environ.get("MCP_REDIS_RECONNECT_ATTEMPTS", "5")),
            "reconnect_delay": float(os.environ.get("MCP_REDIS_RECONNECT_DELAY", "5.0")),
            "heartbeat_interval": float(os.environ.get("MCP_REDIS_HEARTBEAT_INTERVAL", "30.0"))
        }
        
        # Redis连接状态跟踪
        self._redis_connection_lost = False
        self._last_redis_check = 0
        self._redis_heartbeat_task = None

        logger.info(f"[MCPServer Base] 服务器实例 {self.server_id} 创建。会话TTL: {self.session_ttl}s, 工具缓存TTL: {self.tool_cache_ttl}s")
        logger.info(f"[MCPServer Base] 超时配置: {self.timeout_config}")
        logger.info(f"[MCPServer Base] 重试配置: {self.retry_config}")
        logger.info(f"[MCPServer Base] Redis配置: {self.redis_config}")

    async def _execute_with_retry(self, operation: Callable, operation_name: str, *args, **kwargs) -> Any:
        """
        执行操作并支持重试机制
        
        Args:
            operation: 要执行的操作函数
            operation_name: 操作名称，用于日志
            *args, **kwargs: 传递给操作函数的参数
            
        Returns:
            操作结果
            
        Raises:
            最后一次尝试的异常
        """
        last_exception = None
        delay = self.retry_config["retry_delay"]
        
        for attempt in range(self.retry_config["max_retries"] + 1):
            try:
                if asyncio.iscoroutinefunction(operation):
                    return await operation(*args, **kwargs)
                else:
                    return operation(*args, **kwargs)
            except Exception as e:
                last_exception = e
                if attempt < self.retry_config["max_retries"]:
                    logger.warning(f"[MCPServer {self.server_id}] {operation_name} 第{attempt + 1}次尝试失败: {str(e)}, {delay:.1f}秒后重试")
                    await asyncio.sleep(delay)
                    delay = min(delay * self.retry_config["backoff_multiplier"], self.retry_config["max_retry_delay"])
                else:
                    logger.error(f"[MCPServer {self.server_id}] {operation_name} 所有重试尝试失败: {str(e)}")
        
        raise last_exception

    async def _execute_with_timeout(self, operation: Callable, timeout_key: str, operation_name: str, *args, **kwargs) -> Any:
        """
        执行操作并支持超时机制
        
        Args:
            operation: 要执行的操作函数
            timeout_key: 超时配置键
            operation_name: 操作名称，用于日志
            *args, **kwargs: 传递给操作函数的参数
            
        Returns:
            操作结果
            
        Raises:
            asyncio.TimeoutError: 操作超时
        """
        timeout = self.timeout_config.get(timeout_key, 30.0)
        try:
            if asyncio.iscoroutinefunction(operation):
                return await asyncio.wait_for(operation(*args, **kwargs), timeout=timeout)
            else:
                return await asyncio.wait_for(asyncio.to_thread(operation, *args, **kwargs), timeout=timeout)
        except asyncio.TimeoutError:
            logger.error(f"[MCPServer {self.server_id}] {operation_name} 操作超时 ({timeout}秒)")
            raise

    async def _execute_with_retry_and_timeout(self, operation: Callable, timeout_key: str, operation_name: str, *args, **kwargs) -> Any:
        """
        执行操作并同时支持重试和超时机制
        
        Args:
            operation: 要执行的操作函数
            timeout_key: 超时配置键
            operation_name: 操作名称，用于日志
            *args, **kwargs: 传递给操作函数的参数
            
        Returns:
            操作结果
        """
        async def timeout_wrapper():
            return await self._execute_with_timeout(operation, timeout_key, operation_name, *args, **kwargs)
        
        return await self._execute_with_retry(timeout_wrapper, operation_name)

    async def _check_redis_connection(self) -> bool:
        """
        检查Redis连接状态
        
        Returns:
            bool: Redis是否连接正常
        """
        try:
            from utils import get_redis_client
            redis_client = await get_redis_client()
            await asyncio.wait_for(redis_client.ping(), timeout=self.timeout_config["redis_operation_timeout"])
            if self._redis_connection_lost:
                logger.info(f"[MCPServer {self.server_id}] Redis连接已恢复")
                self._redis_connection_lost = False
            return True
        except Exception as e:
            if not self._redis_connection_lost:
                logger.error(f"[MCPServer {self.server_id}] Redis连接失败: {str(e)}")
                self._redis_connection_lost = True
            return False

    async def _start_redis_heartbeat(self):
        """
        启动Redis心跳任务，确保服务器在Redis中的注册信息保持最新
        """
        if self._redis_heartbeat_task and not self._redis_heartbeat_task.done():
            logger.warning(f"[MCPServer {self.server_id}] Redis心跳任务已在运行")
            return
        
        try:
            from utils import heartbeat as redis_heartbeat
            
            # 获取服务器配置信息
            host = os.environ.get("MCP_SERVER_HOST", os.environ.get("MCP_SERVICE_NAME", "localhost"))
            port = int(os.environ.get("PORT", "5000"))
            
            # 启动心跳任务
            self._redis_heartbeat_task = asyncio.create_task(
                redis_heartbeat(
                    server_id=self.server_id,
                    host=host,
                    port=port,
                    port_streamhttp=port,
                    metadata={
                        "protocols": ["streamhttp"],
                        "version": SERVER_VERSION,
                        "status": "running"
                    },
                    interval_seconds=int(self.redis_config["heartbeat_interval"])
                )
            )
            logger.info(f"[MCPServer {self.server_id}] Redis心跳任务已启动，间隔: {self.redis_config['heartbeat_interval']}秒")
        except Exception as e:
            logger.error(f"[MCPServer {self.server_id}] 启动Redis心跳任务失败: {str(e)}")

    async def initialize_server(self):
        """异步初始化服务器，例如预加载工具。"""
        logger.info(f"[MCPServer {self.server_id}] 开始异步初始化...")
        
        # 启动Redis心跳
        await self._start_redis_heartbeat()
        
        # 获取系统令牌以进行预加载
        system_token = os.environ.get("SYSTEM_TOKEN")
        if not system_token:
            logger.warning(f"[MCPServer {self.server_id}] 预加载工具: 缺少 SYSTEM_TOKEN 环境变量。")
        await self._preload_tools(system_token=system_token)
        logger.info(f"[MCPServer {self.server_id}] 异步初始化完成。")

    async def start_heartbeat(self, interval_seconds: int = 60):
        """
        启动一个后台任务，定期清理过期的会话。

        Args:
            interval_seconds (int): 清理检查的间隔时间（秒）。
        """
        logger.info(f"[MCPServer {self.server_id}] 心跳任务启动，每 {interval_seconds} 秒检查一次过期会话。")
        while True:
            try:
                await asyncio.sleep(interval_seconds)
                now = asyncio.get_event_loop().time()
                expired_sessions = [
                    session_id for session_id, session in self.session_store.items()
                    if now - session.last_activity_time > self.session_ttl
                ]
                if expired_sessions:
                    logger.info(f"[MCPServer {self.server_id} Heartbeat] 发现 {len(expired_sessions)} 个过期会话: {expired_sessions}")
                    for session_id in expired_sessions:
                        try:
                            session_to_remove = self.session_store.pop(session_id, None)
                            if session_to_remove:
                                logger.info(f"[MCPServer {self.server_id} Heartbeat] 已移除过期会话: {session_id}")
                                if session_to_remove.internal_message_queue:
                                    # Signal SSE stream to close if active
                                    await session_to_remove.internal_message_queue.put(MCPSession._SENTINEL_CLOSE_QUEUE_)
                            # No longer need to manage is_initialized_for_session separately
                        except Exception as e_clean:
                            logger.warning(f"[MCPServer {self.server_id} Heartbeat] 清理会话 {session_id} 时出错: {e_clean}")
                else:
                    logger.debug(f"[MCPServer {self.server_id} Heartbeat] 没有发现过期会话。当前活动会话数: {len(self.session_store)}")

            except asyncio.CancelledError:
                logger.info(f"[MCPServer {self.server_id} Heartbeat] 心跳任务被取消。")
                break
            except Exception as e:
                logger.error(f"[MCPServer {self.server_id} Heartbeat] 心跳任务发生错误: {e}", exc_info=True)
                # 等待一段时间再重试，避免错误循环
                await asyncio.sleep(interval_seconds * 2)

    async def _preload_tools(self, system_token: Optional[str] = None):
        """服务器初始化时预加载工具列表，使用系统令牌"""
        try:
            # 此处现在不需要直接调用 self.refresh_tools(None) 来填充 self.tool_metadata_cache
            # 因为工具的元数据和函数是分开管理的。
            # self.tool_metadata_cache 会在首次调用 get_tools 时按需加载。
            # 预加载的重点应该是确保所有预定义的工具函数被注册。
            # 假设 register_tool 会在服务器启动的其他地方被调用。
            logger.info(f"[MCPServer {self.server_id}] 预加载工具元数据将在首次请求时进行。确保工具函数已注册。")
            # Example: if you have a list of predefined tools:
            # for tool_name, tool_func in PREDEFINED_PYTHON_TOOLS.items():
            #     self.register_tool(tool_name, tool_func)
            # For now, just log that functions should be registered elsewhere.
        except Exception as e:
            logger.error(f"服务器初始化时预加载工具列表(函数注册)失败: {str(e)}")

    async def refresh_tools(self, session: Optional[MCPSession]) -> List[Dict[str, Any]]:
        """
        强制刷新工具列表并更新缓存
        使用会话中保存的令牌信息

        Args:
            session: MCP会话

        Returns:
            List[Dict[str, Any]]: 工具列表
        """
        from load_tools import load_tools
        logger.info(f"[MCPServer {self.server_id}] 开始刷新工具列表: session={session.session_id if session else 'None'}")

        # 使用超时和重试机制加载工具
        try:
            new_tools_result = await self._execute_with_retry_and_timeout(
                load_tools,
                "tool_load_timeout",
                f"加载工具列表 (session: {session.session_id if session else 'None'})",
                session
            )
        except Exception as e:
            logger.error(f"[MCPServer {self.server_id}] 刷新工具列表失败: {str(e)}")
            # 返回空列表而不是抛出异常，保持服务可用性
            return []

        new_tools_list: List[Dict[str, Any]] = []
        if isinstance(new_tools_result, dict) and "tools" in new_tools_result:
            new_tools_list = new_tools_result["tools"]
        elif isinstance(new_tools_result, list):
            new_tools_list = new_tools_result
        # else: new_tools_list remains []

        # Check if tools actually changed before broadcasting
        # Only notify if the cache was previously populated (tool_cache_last_updated > 0) and there's a change.
        if self.tool_cache_last_updated > 0 and self.tool_metadata_cache != new_tools_list:
            logger.info(f"[MCPServer {self.server_id}] Tool list has changed. Notifying connected clients.")
            notification_message = {
                "jsonrpc": "2.0",
                "method": "notifications/tools/list_changed",
                "params": {} # Typically empty params for this notification
            }
            # Create a list of tasks to send notifications concurrently
            notification_tasks = []
            for s_id, s_obj in self.session_store.items():
                if s_obj.is_initialized and s_obj.internal_message_queue:
                    # Create a separate task for each session to avoid blocking
                    # Ensure the message is a deep copy if it's mutable and could be modified,
                    # though here it's a simple dict literal.
                    task = asyncio.create_task(s_obj.internal_message_queue.put(notification_message.copy()))
                    notification_tasks.append(task)
                    logger.debug(f"[MCPServer {self.server_id}] Queued 'tools/list_changed' notification for session {s_id}")

            if notification_tasks:
                # Wait for all put operations to complete, logging any exceptions
                results = await asyncio.gather(*notification_tasks, return_exceptions=True)
                for i, result_exc in enumerate(results):
                    if isinstance(result_exc, Exception):
                        # Log the error with session_id if possible (requires mapping back from task index or storing session_id with task)
                        # For simplicity, logging the exception for now.
                        logger.error(f"[MCPServer {self.server_id}] Error queueing 'tools/list_changed' notification for a session: {result_exc}")
                logger.info(f"[MCPServer {self.server_id}] Finished queueing 'tools/list_changed' notifications for {len(notification_tasks)} sessions.")

        self.tool_metadata_cache = new_tools_list
        self.tool_cache_last_updated = asyncio.get_event_loop().time()
        return new_tools_list

    async def get_tools(self, session: Optional[MCPSession], force_refresh: bool = False) -> List[Dict[str, Any]]:
        """
        获取工具列表，优先使用缓存

        Args:
            session: MCP会话
            force_refresh: 是否强制刷新

        Returns:
            List[Dict[str, Any]]: 工具列表
        """
        # 检查是否需要刷新缓存
        now = asyncio.get_event_loop().time()
        # 检查缓存是否已过期，或者缓存记录的时间戳是否为0（表示从未成功加载过）
        cache_expired = (now - self.tool_cache_last_updated > self.tool_cache_ttl) or (self.tool_cache_last_updated == 0)

        # 有效的缓存条件：缓存不是None（即使是[]也算有效）、未强制刷新、缓存未过期
        # 注意: self.tool_metadata_cache is not None 可以区分从未加载成功（初始为[]，但时间戳为0）和加载成功但列表为空（为[]，时间戳有效）的情况。
        # 我们主要依赖时间戳和force_refresh来判断。
        if not force_refresh and not cache_expired:
            # 如果缓存上次更新时间有效（不为0），则说明加载过（即使结果是空列表），可以返回缓存
            if self.tool_cache_last_updated > 0:
                 logger.info(f"使用工具列表缓存 (上次更新于 {self.tool_cache_last_updated:.2f})，共 {len(self.tool_metadata_cache)} 个工具")
                 return self.tool_metadata_cache
            else:
                # 时间戳为0，表示从未成功加载，需要刷新
                logger.info("工具列表从未成功加载过，需要刷新")
        elif force_refresh:
            logger.info("强制刷新工具列表缓存")
        elif cache_expired:
             logger.info(f"工具列表缓存已过期 (TTL: {self.tool_cache_ttl}s, 上次更新: {self.tool_cache_last_updated:.2f})，需要刷新")

        # 需要刷新缓存
        # logger.info(f"工具列表缓存{'为空' if not self.tool_metadata_cache else '已过期' if cache_expired else '被强制刷新'}") # 旧日志
        return await self.refresh_tools(session)

    def register_tool(self, name: str, func: Callable[[MCPSession, Dict[str, Any]], Coroutine[Any, Any, Any]]):
        """
        注册一个工具及其可调用函数。
        工具的元数据（如描述、参数模式、是否支持流式）从API加载。
        """
        if name in self.tool_functions:
            logger.warning(f"[MCPServer {self.server_id}] 工具 '{name}' 已被注册。将被覆盖。")
        self.tool_functions[name] = func
        logger.info(f"[MCPServer {self.server_id}] 工具函数 '{name}' 已注册。")

    def get_server_capabilities(self, session_id: Optional[str] = None) -> Dict[str, Any]:
        """
        获取服务器能力和工具列表的合并信息
        """
        # 构建服务器能力
        capabilities = {
            "tools": {
                "streaming": True,
                "listChanged": True
            },
            "resources": {
                "subscribe": False,
                "listChanged": False
            },
            "prompts": {
                "listChanged": False
            }
        }
        return capabilities

    def on_initialize(self, func: Callable[[MCPSession, Dict[str, Any]], Coroutine[Any, Any, None]]):
        self.on_initialize_callbacks.append(func)

    def on_shutdown(self, func: Callable[[], Coroutine[Any, Any, None]]):
        self.on_shutdown_callbacks.append(func)

    def on_request(self, func: Callable[[MCPSession, str, Dict[str, Any]], Coroutine[Any, Any, None]]):
        self.on_request_callbacks.append(func)

    async def _execute_callbacks(self, callbacks: List[Callable[..., Coroutine[Any, Any, None]]], *args):
        for callback in callbacks:
            try:
                await callback(*args)
            except Exception as e:
                logger.error(f"Error executing callback {callback.__name__}: {e}", exc_info=True)

    async def handle_initialize(self, request_id: Optional[str], params: Dict[str, Any], headers: Dict[str, str]) -> Dict[str, Any]:
        """
        Handles the 'initialize' request.
        Authenticates the request (if no existing session_id is implied by transport),
        creates a new session, stores it, and returns session info.
        """
        logger.info(f"[MCPServer handle_initialize] Request ID: {request_id}, Params: {params.keys()}")
        client_info = params.get("clientInfo")
        protocol_version_client = params.get("protocolVersion")

        # For StreamHTTP, session_id from header would ideally be checked by the transport layer first.
        # If initialize is called, it implies a new session or re-initialization.
        # We'll assume for now initialize always tries to create a new session state here.

        session: Optional[MCPSession] = None
        try:
            # Create a new session based on authentication headers provided by the proxy/gateway
            # These headers (like X-Mcp-System-Token, X-Mcp-User-Id) are assumed to be trusted.
            session = await create_new_session_from_auth_headers(self.server_id, headers, client_info)
            session.client_info = client_info # Store client info
            session.is_initialized = True # Mark as initialized
            # Ensure queue is created if not already (should be by constructor)
            if session.internal_message_queue is None:
                session.internal_message_queue = asyncio.Queue()

            self.session_store[session.session_id] = session
            logger.info(f"[MCPServer handle_initialize] New session {session.session_id} created and stored. Client: {client_info}")

            # Execute on_initialize callbacks
            await self._execute_callbacks(self.on_initialize_callbacks, session, params)

            return {
                "jsonrpc": "2.0",
                "id": request_id,
                "result": {
                    "sessionId": session.session_id,
                    "protocolVersion": PROTOCOL_VERSION, # Server's protocol version
                    "serverInfo": {
                        "name": SERVER_NAME,
                        "version": SERVER_VERSION,
                        "serverId": self.server_id
                    },
                    "capabilities": self.get_server_capabilities(session.session_id)
                }
            }
        except ValueError as ve: # Catch auth errors from create_new_session
             logger.error(f"[MCPServer handle_initialize] Authentication/Session creation failed: {ve}", exc_info=True)
             return {
                "jsonrpc": "2.0",
                "id": request_id,
                "error": {"code": ErrorCode.AUTHENTICATION_FAILED.value, "message": f"Authentication failed: {str(ve)}"}
            }
        except Exception as e:
            logger.error(f"[MCPServer handle_initialize] Error: {e}", exc_info=True)
            # Attempt to clean up partially created session if any
            if session and session.session_id in self.session_store:
                del self.session_store[session.session_id]

            return {
                "jsonrpc": "2.0",
                "id": request_id,
                "error": {"code": ErrorCode.INITIALIZATION_FAILED.value, "message": f"Initialization failed: {str(e)}"}
            }

    async def handle_notification(self, method: str, params: Dict[str, Any], headers: Dict[str, str], session_id_from_transport: Optional[str] = None) -> None:
        logger.debug(f"[MCPServer handle_notification] Method: {method}, Session via transport: {session_id_from_transport}")

        if not session_id_from_transport:
            logger.warning(f"[MCPServer handle_notification] Received notification without session_id_from_transport. Method: {method}. Ignoring.")
            return

        session = self.get_session(session_id_from_transport)
        if not session:
            logger.warning(f"[MCPServer handle_notification] Session not found for ID: {session_id_from_transport}. Method: {method}. Ignoring.")
            return

        if not session.is_initialized:
            logger.warning(f"[MCPServer handle_notification] Session {session.session_id} not initialized. Method: {method}. Ignoring notification.")
            return

        session.last_activity_time = asyncio.get_event_loop().time()
        logger.info(f"[MCPServer handle_notification] Received for session {session.session_id}. Method: {method}")
        # Here, you might dispatch specific notification handlers if needed
        # For example, client sending 'initialized' notification (though StreamHTTP might not need it)
        if method == "initialized" or method == "notifications/initialized": # Common client notification after getting initialize response
            logger.info(f"[MCPServer] Client acknowledged initialization for session {session.session_id}")
            # Potentially trigger post-initialization logic if any
            pass
        # Handle other notifications like textDocument/didChange etc.

    async def handle_request(self, method: str, params: Dict[str, Any], request_id: Optional[str], headers: Dict[str, str], session_id_from_transport: Optional[str] = None) -> Dict[str, Any]:
        """处理客户端请求（JSON-RPC请求对象）"""
        logger.debug(f"[MCPServer {self.server_id}] 收到请求: Method={method}, RequestID={request_id}, ParamsKeys={list(params.keys()) if params else []}, SessionTransportID={session_id_from_transport}")

        session: Optional[MCPSession] = None
        if session_id_from_transport:
            session = self.get_session(session_id_from_transport)
            if not session:
                logger.warning(f"[MCPServer {self.server_id}] 处理请求 '{method}' 时会话 '{session_id_from_transport}' 未找到或已过期。")
                return self._create_error_response(request_id, ErrorCode.SESSION_NOT_FOUND, f"Session {session_id_from_transport} not found or expired.")
            if not session.is_initialized:
                logger.warning(f"[MCPServer {self.server_id}] 会话 '{session_id_from_transport}' 未初始化，但尝试调用方法 '{method}'.")
                return self._create_error_response(request_id, ErrorCode.SESSION_NOT_INITIALIZED, f"Session {session_id_from_transport} not initialized. Call 'initialize' first.")
        elif method != "initialize": # Non-initialize methods require a session ID from transport
            logger.warning(f"[MCPServer {self.server_id}] 方法 '{method}' 需要会话ID，但传输层未提供。")
            return self._create_error_response(request_id, ErrorCode.SESSION_NOT_FOUND, "Session ID missing from transport for this method.")

        try:
            if method == "initialize":
                # Initialize is handled by multi_protocol.py which calls self.handle_initialize directly
                # This path should ideally not be hit if multi_protocol.py correctly routes initialize
                logger.warning("[MCPServer {self.server_id}] Initialize called via generic handle_request, should be routed to handle_initialize directly.")
                # However, to be safe, let's call the specific handler if it ends up here.
                # Note: handle_initialize now expects headers directly.
                return await self.handle_initialize(request_id=request_id, params=params, headers=headers)

            # All other methods require an initialized session
            if not session: # Should have been caught above, but as a safeguard
                return self._create_error_response(request_id, ErrorCode.SESSION_NOT_FOUND, "Session not found.")

            if method == "tools/list":
                if not session:
                    return self._create_error_response(request_id, ErrorCode.SESSION_NOT_FOUND, "Session not found for tools/list")
                if not session.is_initialized:
                    return self._create_error_response(request_id, ErrorCode.SESSION_NOT_INITIALIZED, "Session not initialized for tools/list")

                force_refresh = params.get("force_refresh", False)
                try:
                    tool_list_raw = await self.get_tools(session, force_refresh=force_refresh)

                    processed_tools_to_return = [] # Initialize a new list for processed tools
                    if tool_list_raw:
                        logger.info(f"[MCPServer tools/list] Raw tools from get_tools: {len(tool_list_raw)} tools. Content: {tool_list_raw}")

                    for tool_data in tool_list_raw:
                            processed_tool = tool_data.copy() # Work on a copy

                            # Ensure inputSchema exists, falling back or creating default
                            if 'inputSchema' not in processed_tool:
                                if 'parameters' in processed_tool: # Fallback to 'parameters'
                                    logger.warning(f"Tool '{processed_tool.get('name')}' using 'parameters' as fallback for 'inputSchema'.")
                                    processed_tool['inputSchema'] = processed_tool['parameters']
                                else: # If neither 'inputSchema' nor 'parameters' exist
                                    logger.warning(f"Tool '{processed_tool.get('name')}' missing 'inputSchema' and 'parameters'. Creating default empty schema.")
                                processed_tool['inputSchema'] = {"type": "object", "properties": {}, "required": []}

                            # Remove the old 'parameters' field if it exists, as 'inputSchema' is standard
                            if 'parameters' in processed_tool:
                                del processed_tool['parameters']

                            processed_tools_to_return.append(processed_tool) # Add the processed tool to the list

                    else:
                        logger.info(f"[MCPServer tools/list] Raw tools from get_tools is empty or None.")

                    logger.info(f"[MCPServer {self.server_id}] '{session.session_id if session else 'UnknownSession'}' 请求工具列表，返回 {len(processed_tools_to_return)} 个工具。Processed from {len(tool_list_raw) if tool_list_raw else 0}")
                    return self._create_success_response(request_id, {"tools": processed_tools_to_return})

                except Exception as e_tools:
                    logger.error(f"[MCPServer {self.server_id}] 处理 'tools/list' 时出错: {e_tools}", exc_info=True)
                    return self._create_error_response(request_id, ErrorCode.GENERIC_ERROR, f"Error processing tools/list: {str(e_tools)}")

            elif method == "tools/refresh":
                if not session:
                    return self._create_error_response(request_id, ErrorCode.SESSION_NOT_FOUND, "Session not found for tools/refresh")
                if not session.is_initialized:
                    return self._create_error_response(request_id, ErrorCode.SESSION_NOT_INITIALIZED, "Session not initialized for tools/refresh")

                # 实际的权限检查逻辑
                # if "tools:refresh" not in session.permissions: # Предполагая, что есть такое разрешение
                #     logger.warning(f"Session {session.session_id} missing 'tools:refresh' permission for tools/refresh.")
                #     return self._create_error_response(request_id, ErrorCode.AUTHORIZATION_FAILED, "Permission denied for tools/refresh")

                try:
                    # refresh_tools now directly returns the list of tools and updates cache
                    newly_refreshed_tools_raw = await self.refresh_tools(session)
                    # Ensure each tool in the list has inputSchema
                    processed_refreshed_tools = []
                    for tool_data in newly_refreshed_tools_raw:
                        processed_tool = tool_data.copy()
                        if 'inputSchema' not in processed_tool: # If backend didn't provide inputSchema directly
                            if 'parameters' in processed_tool: # Fallback to 'parameters'
                                logger.warning(f"Tool '{processed_tool.get('name')}' using 'parameters' as fallback for 'inputSchema'. This may indicate an outdated format from the SAPI endpoint response for this tool.")
                                processed_tool['inputSchema'] = processed_tool['parameters'] # Use parameters as inputSchema
                            else: # If neither 'inputSchema' nor 'parameters' exist
                                logger.warning(f"Tool '{processed_tool.get('name')}' missing 'inputSchema' and 'parameters' fields. Creating a default empty schema.")
                                processed_tool['inputSchema'] = {"type": "object", "properties": {}, "required": []} # Default empty schema

                        # If 'inputSchema' was already present in tool_data, it's used directly.
                        # Now, ensure the potentially confusing 'parameters' field is removed if it exists,
                        # as 'inputSchema' is the standard.
                        if 'parameters' in processed_tool:
                            del processed_tool['parameters']

                        processed_refreshed_tools.append(processed_tool)

                    logger.info(f"工具列表已为会话 {session.session_id} 刷新，返回 {len(processed_refreshed_tools)} 个工具。")
                    response_json = self._create_success_response(request_id, {"tools": processed_refreshed_tools})
                except Exception as e:
                    logger.error(f"Error in tools/refresh for session {session.session_id}: {e}", exc_info=True)
                    response_json = self._create_error_response(request_id, ErrorCode.GENERIC_ERROR, f"Failed to refresh tools: {str(e)}")

            elif method == "tools/call":
                # 首先，检查 request_id 是否存在
                if not request_id:
                    logger.warning(f"[MCPServer {self.server_id}] tools/call missing request_id.")
                    return self._create_error_response(None, ErrorCode.INVALID_REQUEST, "request_id is required for tools/call")

                # 然后，从参数中获取工具名称和参数
                tool_name = params.get("name")
                tool_arguments = params.get("arguments", {}) # Default to empty dict if not provided

                if not tool_name:
                    logger.warning(f"[MCPServer {self.server_id}] tools/call missing 'name' in params.")
                    return self._create_error_response(request_id, ErrorCode.INVALID_REQUEST, "Missing 'name' in tools/call params")

                logger.info(f"[MCPServer {self.server_id}] '{session.session_id}' 请求调用工具: {tool_name}，参数: {tool_arguments}")

                all_tools_metadata = await self.get_tools(session)
                tool_meta = next((t for t in all_tools_metadata if t.get("name") == tool_name), None)

                if not tool_meta:
                    logger.warning(f"[MCPServer {self.server_id}] 工具 '{tool_name}' 在元数据中未找到。")
                    return self._create_error_response(request_id, ErrorCode.TOOL_NOT_FOUND, f"Tool '{tool_name}' not found in metadata.")

                if not tool_meta.get("enabled", True):
                    logger.warning(f"[MCPServer {self.server_id}] 工具 '{tool_name}' 已被禁用。")
                    return self._create_error_response(request_id, ErrorCode.TOOL_NOT_FOUND, f"Tool '{tool_name}' is disabled.")

                actual_tool_func = self.tool_functions.get(tool_name)

                if actual_tool_func:
                    # This is a locally registered Python tool
                    is_streaming_tool = tool_meta.get("supports_streaming", False) # Assuming metadata for local tools also indicates streaming support
                    stream_mode_requested = params.get("stream_mode", False)

                    if is_streaming_tool and stream_mode_requested:
                        logger.info(f"[MCPServer {self.server_id}] 工具 '{tool_name}' (local Python) 支持流式传输，且客户端已请求。开始流式处理。")
                        asyncio.create_task(self._execute_tool_and_stream_results(
                            session, actual_tool_func, tool_name, tool_arguments, request_id
                        ))
                        # Return immediately for streaming start, actual results are sent via SSE
                        response_json = { # Store in response_json to be returned at the end
                            "jsonrpc": "2.0",
                            "id": request_id,
                            "result": {
                                "status": "tool_stream_started",
                            "tool_name": tool_name,
                                "message": f"Streaming for local tool '{tool_name}' started."
                            }
                        }
                    else: # Non-streaming or streaming not requested for local tool
                        logger.info(f"[MCPServer {self.server_id}] 工具 '{tool_name}' (local Python) 将以非流式模式执行。")
                        try:
                            tool_result = await actual_tool_func(session, tool_arguments)
                            logger.debug(f"[MCPServer {self.server_id}] 工具 '{tool_name}' (local Python) 非流式执行结果: {tool_result}")
                            response_json = self._create_success_response(request_id, tool_result) # Store in response_json
                        except Exception as e_tool:
                            logger.error(f"[MCPServer {self.server_id}] 执行工具 '{tool_name}' (local Python,非流式) 时发生错误: {e_tool}", exc_info=True)
                            response_json = self._create_error_response(request_id, ErrorCode.TOOL_EXECUTION_ERROR, f"Error executing local tool '{tool_name}': {str(e_tool)}") # Store in response_json
                else:
                    # No locally registered function found, this implies it's an API-defined tool
                    logger.info(f"[MCPServer {self.server_id}] Tool '{tool_name}' not found in local functions, treating as API-defined tool.")
                    response_json = {} # Initialize response_json for the API tool processing path

                    endpoint_meta = tool_meta.get("metadata", {}).get("endpoint")
                    if not endpoint_meta or not endpoint_meta.get("path") or not endpoint_meta.get("method"):
                        logger.error(f"[MCPServer {self.server_id}] Tool '{tool_name}' metadata is missing endpoint path or method.")
                        response_json = self._create_error_response(request_id, ErrorCode.TOOL_EXECUTION_ERROR, f"Configuration error for API tool '{tool_name}': Missing endpoint details.") # Assign to response_json
                    else: # Added else to ensure response_json is set if the above 'if' is true.
                        api_path_template = endpoint_meta["path"]
                        api_method = endpoint_meta["method"].upper()

                        auth_token = session.system_token
                        if not auth_token:
                            logger.error(f"[MCPServer {self.server_id}] No system_token in session for user {session.user_id} to call API tool '{tool_name}'. This token should be set during session initialization.")
                            response_json = self._create_error_response(request_id, ErrorCode.AUTHENTICATION_FAILED, "Missing token in session for API tool execution.") # Assign to response_json
                        else: # Added else
                            api_url = API_BASE_URL.rstrip('/') + "/" + api_path_template.lstrip('/')

                            if not isinstance(tool_arguments, dict):
                                logger.warning(
                                    f"[MCPServer {self.server_id}] API Tool '{tool_name}' received non-dict arguments (type: {type(tool_arguments)}, value: '{tool_arguments}'). "
                                    f"Defaulting to an empty dictionary before copy."
                                )
                                tool_arguments = {}

                            api_params_for_query_or_body = tool_arguments.copy()

                            project_id_used_in_path = False
                            if "{project_id}" in api_url:
                                effective_project_id = session.project_id
                                
                                # 如果会话中没有项目ID，尝试从系统令牌中提取
                                if not effective_project_id and session.system_token:
                                    effective_project_id = extract_project_id_from_token(session.system_token)
                                    if effective_project_id:
                                        logger.info(f"[MCPServer {self.server_id}] 从系统令牌中提取到项目ID用于API调用: {effective_project_id}")
                                        # 更新会话中的项目ID
                                        session.project_id = effective_project_id
                                
                                if effective_project_id:
                                    api_url = api_url.replace("{project_id}", effective_project_id)
                                    project_id_used_in_path = True
                                    if "project_id" in api_params_for_query_or_body:
                                        api_params_for_query_or_body.pop("project_id", None)
                                        logger.debug(f"Removed 'project_id' from query/body params for tool '{tool_name}' as project_id was used in path.")
                                else:
                                    # 项目ID缺失，但不一定是错误，可能是全局工具
                                    logger.warning(f"[MCPServer {self.server_id}] API tool '{tool_name}' URL '{api_path_template}' contains project_id placeholder, but no project_id available. This may be a global tool or require project context.")
                                    # 不直接报错，而是尝试继续处理，让API端点决定是否需要项目ID
                                    # response_json = self._create_error_response(request_id, ErrorCode.INVALID_REQUEST, "Project ID missing in session for API tool execution.")

                            # Check if response_json was set due to project_id error before proceeding
                            if "error" not in response_json or not response_json.get("error"): # Check if response_json is not already an error response
                                logger.info(f"[MCPServer {self.server_id}] URL after project_id substitution for tool '{tool_name}': {api_url}")

                                raw_sapi_params = tool_meta.get("metadata", {}).get("endpoint", {}).get("sapi_parameters", "MISSING_SAPI_PARAMETERS_KEY")
                                logger.debug(f"Raw sapi_parameters for tool '{tool_name}': {raw_sapi_params}")

                                sapi_endpoint_meta_for_params = {}
                                if isinstance(raw_sapi_params, dict):
                                    sapi_parameters_metadata_value = raw_sapi_params.get("metadata", "MISSING_METADATA_KEY_IN_SAPI_PARAMETERS")
                                    if isinstance(sapi_parameters_metadata_value, dict):
                                        sapi_endpoint_meta_for_params = sapi_parameters_metadata_value
                                    else:
                                        logger.warning(f"Expected dict for sapi_parameters.metadata for tool '{tool_name}', got: {type(sapi_parameters_metadata_value)}. Value: {sapi_parameters_metadata_value}")
                                else:
                                    logger.warning(f"Expected dict for sapi_parameters for tool '{tool_name}', got: {type(raw_sapi_params)}. Value: {raw_sapi_params}")

                                logger.debug(f"Effective sapi_parameters.metadata for tool '{tool_name}': {sapi_endpoint_meta_for_params}")

                                path_params_defined = []
                                if isinstance(sapi_endpoint_meta_for_params, dict):
                                    path_params_from_meta = sapi_endpoint_meta_for_params.get("path_params", [])
                                    if isinstance(path_params_from_meta, list):
                                        path_params_defined = path_params_from_meta
                                    else:
                                        logger.warning(f"Expected list for path_params in sapi_parameters.metadata for tool '{tool_name}', got: {type(path_params_from_meta)}. Value: {path_params_from_meta}. Defaulting to empty list.")

                                logger.debug(f"Defined path parameters for tool '{tool_name}' from metadata: {path_params_defined}. Available arguments for substitution: {list(api_params_for_query_or_body.keys())}")

                                current_api_url_for_path_params = api_url

                                for param_name in path_params_defined:
                                    placeholder = f"{{{param_name}}}"
                                    if placeholder in current_api_url_for_path_params:
                                        param_value = api_params_for_query_or_body.get(param_name)
                                        if param_value is not None:
                                            current_api_url_for_path_params = current_api_url_for_path_params.replace(placeholder, str(param_value))
                                            api_params_for_query_or_body.pop(param_name, None)
                                            logger.info(f"Substituted path parameter '{param_name}' with value '{param_value}' in URL for tool '{tool_name}'. URL is now: {current_api_url_for_path_params}")
                                        else:
                                            tool_input_schema = tool_meta.get("inputSchema", {})
                                            is_required = param_name in tool_input_schema.get("required", [])
                                            if is_required:
                                                logger.error(f"Required path parameter '{param_name}' for tool '{tool_name}' is missing from arguments. Cannot substitute placeholder '{placeholder}'.")
                                                response_json = self._create_error_response(request_id, ErrorCode.INVALID_REQUEST, f"Missing required path parameter for URL: {param_name}") # Assign to response_json
                                                break
                                            else:
                                                logger.warning(f"Optional path parameter '{param_name}' for tool '{tool_name}' not provided or is None. Placeholder '{placeholder}' will remain in the URL: {current_api_url_for_path_params}. This may lead to an error if the API expects this path segment.")

                                # Check if response_json was set due to path param error
                                if "error" not in response_json or not response_json.get("error"):
                                    final_url_for_api_call = current_api_url_for_path_params
                                    logger.info(f"[MCPServer {self.server_id}] Final URL for API tool '{tool_name}' (after all path param substitutions): {final_url_for_api_call}")

                                    headers_for_api_call = {
                                        "Authorization": f"Bearer {auth_token}",
                                        "Content-Type": "application/json",
                                        "Accept": "application/json"
                                    }

                                    cleaned_params = {k: v for k, v in api_params_for_query_or_body.items() if v is not None}
                                    logger.debug(f"Cleaned parameters for query/body (None values removed, path params excluded): {cleaned_params}")

                                    async with httpx.AsyncClient(timeout=30.0) as client:
                                        try:
                                            response_from_api: httpx.Response
                                            request_kwargs = {}

                                            if api_method.upper() in ["GET", "DELETE"]:
                                                defined_query_params = []
                                                if isinstance(raw_sapi_params, dict) and "openapi_parameters" in raw_sapi_params:
                                                    openapi_params_dict = raw_sapi_params.get("openapi_parameters", {})
                                                    if isinstance(openapi_params_dict, dict):
                                                        for param_name_query, param_info in openapi_params_dict.items():
                                                            if isinstance(param_info, dict) and param_info.get("in") == "query":
                                                                defined_query_params.append(param_name_query)
                                                    else:
                                                        logger.warning(f"Expected dict for openapi_parameters in sapi_parameters for tool '{tool_name}', got {type(openapi_params_dict)}. Cannot determine defined query params accurately.")
                                                else:
                                                    logger.debug(f"The 'openapi_parameters' key is missing or sapi_parameters is not a dict for tool '{tool_name}'. Raw sapi_parameters content: {raw_sapi_params}")

                                                if defined_query_params:
                                                    final_query_params = {k: v for k, v in cleaned_params.items() if k in defined_query_params}
                                                    logger.debug(f"Original cleaned_params for GET/DELETE: {cleaned_params}. Defined query params from metadata: {defined_query_params}. Final query_params to be sent: {final_query_params}")
                                                    request_kwargs["params"] = final_query_params
                                                else:
                                                    logger.warning(
                                                        f"Could not determine defined query parameters for tool '{tool_name}' (method {api_method}) using tool_meta.metadata.endpoint.sapi_parameters.openapi_parameters. "
                                                        f"Content of openapi_parameters was: {raw_sapi_params.get('openapi_parameters', 'KEY_MISSING_OR_SAPI_PARAMS_NOT_DICT')}. "
                                                        f"Falling back to sending all non-None cleaned parameters: {cleaned_params}. "
                                                        "This might lead to unexpected API behavior if the SAPI endpoint is strict about query parameters."
                                                    )
                                                    request_kwargs["params"] = cleaned_params

                                            elif api_method.upper() in ["POST", "PUT", "PATCH"]:
                                                # 检查是否需要使用multipart/form-data
                                                content_type = self._determine_content_type(tool_meta, cleaned_params)
                                                
                                                if content_type == "multipart/form-data":
                                                    # 处理multipart/form-data请求
                                                    files_data, form_data = await self._prepare_multipart_data(cleaned_params, tool_meta)
                                                    
                                                    # 更新请求头
                                                    headers_for_api_call = {
                                                        "Authorization": f"Bearer {auth_token}",
                                                        "Accept": "application/json"
                                                        # 不设置Content-Type，让httpx自动处理multipart边界
                                                    }
                                                    
                                                    request_kwargs["data"] = form_data
                                                    if files_data:
                                                        request_kwargs["files"] = files_data
                                                    
                                                    logger.info(f"[MCPServer {self.server_id}] Using multipart/form-data for tool '{tool_name}'. Form fields: {list(form_data.keys())}, Files: {list(files_data.keys()) if files_data else []}")
                                                else:
                                                    # 标准JSON请求
                                                    request_kwargs["json"] = cleaned_params

                                            logger.info(f"[MCPServer {self.server_id}] Attempting to call {api_method.upper()} {final_url_for_api_call} with args: {request_kwargs}")

                                            response_from_api = await client.request(
                                                method=api_method.upper(),
                                                url=final_url_for_api_call,
                                                headers=headers_for_api_call,
                                                **request_kwargs
                                            )

                                            logger.debug(f"[MCPServer {self.server_id}] API tool '{tool_name}' response status: {response_from_api.status_code}, content: {response_from_api.text[:200]}...")

                                            if 200 <= response_from_api.status_code < 300:
                                                try:
                                                    # 检查是否为流式响应
                                                    is_streaming_tool = tool_meta.get("metadata", {}).get("supports_streaming", False)
                                                    stream_mode_requested = params.get("stream_mode", False)
                                                    
                                                    if is_streaming_tool and stream_mode_requested:
                                                        # 处理流式响应
                                                        logger.info(f"[MCPServer {self.server_id}] Tool '{tool_name}' supports streaming and stream mode requested")
                                                        asyncio.create_task(self._handle_streaming_api_response(
                                                            response_from_api, session, tool_name, request_id, tool_meta
                                                        ))
                                                        # 立即返回流式开始响应
                                                        response_json = self._create_success_response(request_id, {
                                                            "status": "stream_started",
                                                            "tool_name": tool_name,
                                                            "message": f"Streaming response started for API tool '{tool_name}'"
                                                        })
                                                    else:
                                                        # 标准非流式响应处理
                                                        api_response_data = response_from_api.json()
                                                        mapped_data = await self._apply_response_mapping(tool_meta, api_response_data, session)

                                                        content_item: Dict[str, Any]
                                                        if isinstance(mapped_data, dict) and mapped_data.get("type") in ["text", "image", "audio", "resource"]:
                                                            content_item = mapped_data
                                                        elif isinstance(mapped_data, str):
                                                            content_item = {"type": "text", "text": mapped_data}
                                                        else:
                                                            try:
                                                                content_item = {"type": "text", "text": safe_json_dumps(mapped_data)}
                                                            except Exception as e_dumps:
                                                                logger.warning(f"[MCPServer {self.server_id}] Failed to serialize mapped_data for tool '{tool_name}': {e_dumps}. Falling back to str().")
                                                                content_item = {"type": "text", "text": str(mapped_data)}

                                                        mcp_compliant_result = {
                                                            "content": [content_item],
                                                            "isError": False
                                                        }
                                                        response_json = self._create_success_response(request_id, mcp_compliant_result)
                                                except _json.JSONDecodeError as e_json:
                                                    error_message = sanitize_error_message(f"API tool '{tool_name}' returned invalid JSON: {str(e_json)}")
                                                    logger.error(f"[MCPServer {self.server_id}] JSON decode error for tool '{tool_name}': {e_json}")
                                                    response_json = self._create_error_response(request_id, ErrorCode.JSON_DECODE_ERROR, error_message)
                                            else: # Non-2xx status code from API
                                                error_message_prefix = f"API tool '{tool_name}' failed with status {response_from_api.status_code}"
                                                error_detail_text = ""
                                                try:
                                                    error_details_from_api = response_from_api.json()
                                                    error_detail_text = f". Details: {safe_json_dumps(error_details_from_api)}"
                                                except _json.JSONDecodeError:
                                                    # 安全地截断响应文本
                                                    response_text = sanitize_error_message(response_from_api.text, max_length=200)
                                                    error_detail_text = f". Response: {response_text}"

                                                full_error_message = sanitize_error_message(error_message_prefix + error_detail_text)
                                                logger.error(f"[MCPServer {self.server_id}] API error for tool '{tool_name}': status={response_from_api.status_code}")
                                                response_json = self._create_error_response(request_id, ErrorCode.API_ERROR, full_error_message)

                                        except httpx.RequestError as e_httpx:
                                            error_message = sanitize_error_message(f"Network error calling API tool '{tool_name}': {str(e_httpx)}")
                                            logger.error(f"[MCPServer {self.server_id}] Network error for tool '{tool_name}': {type(e_httpx).__name__}", exc_info=True)
                                            response_json = self._create_error_response(request_id, ErrorCode.NETWORK_ERROR, error_message)
                                        except Exception as e_api_call: # Catch-all for other errors during API call or response processing
                                            error_message = sanitize_error_message(f"Unexpected error calling API tool '{tool_name}': {str(e_api_call)}")
                                            logger.error(f"[MCPServer {self.server_id}] Unexpected error for tool '{tool_name}': {type(e_api_call).__name__}", exc_info=True)
                                            response_json = self._create_error_response(request_id, ErrorCode.GENERIC_ERROR, error_message)

            elif method == "resources/list":
                if not session:
                    return self._create_error_response(request_id, ErrorCode.SESSION_NOT_FOUND, "Session not found for resources/list")
                if not session.is_initialized:
                    return self._create_error_response(request_id, ErrorCode.SESSION_NOT_INITIALIZED, "Session not initialized for resources/list")

                logger.info(f"[MCPServer {self.server_id}] '{session.session_id}' 请求资源列表")
                # 返回空的资源列表，因为当前实现主要专注于工具
                response_json = self._create_success_response(request_id, {
                    "resources": []
                })

            elif method == "prompts/list":
                if not session:
                    return self._create_error_response(request_id, ErrorCode.SESSION_NOT_FOUND, "Session not found for prompts/list")
                if not session.is_initialized:
                    return self._create_error_response(request_id, ErrorCode.SESSION_NOT_INITIALIZED, "Session not initialized for prompts/list")

                logger.info(f"[MCPServer {self.server_id}] '{session.session_id}' 请求提示词列表")
                # 返回空的提示词列表，因为当前实现主要专注于工具
                response_json = self._create_success_response(request_id, {
                    "prompts": []
                })

            elif method == "ping":
                if session:
                    logger.info(f"[MCPServer {self.server_id}] Received 'ping' for active session {session.session_id}. Request ID: {request_id}")
                    response_json = self._create_success_response(request_id, {
                        "status": "pong",
                        "message": "Session is active.",
                        "session_id": session.session_id,
                        "server_id": self.server_id,
                        "timestamp": asyncio.get_event_loop().time()
                    })
                else:
                    logger.info(f"[MCPServer {self.server_id}] Received 'ping' (no specific session or session not found). Request ID: {request_id}")
                    response_json = self._create_success_response(request_id, {
                        "status": "pong",
                        "message": "Server is active.",
                        "server_id": self.server_id,
                        "protocol_version": PROTOCOL_VERSION,
                        "timestamp": asyncio.get_event_loop().time()
                    })

            elif method == "session/status":
                # ... existing code ...
                pass

            elif method.startswith("notifications/"):
                logger.info(f"[MCPServer {self.server_id}] Received notification-style request: {method} for session {session.session_id}. Acknowledging.")
                # Call handle_notification for any side-effects if needed
                # Note: handle_notification itself does not return a value suitable for a JSON-RPC response to a request.
                # We are calling it here assuming it might perform some state change or logging.
                await self.handle_notification(method, params, headers, session_id_from_transport=session.session_id)

                # Return a generic success for acknowledged notification received as a request
                return self._create_success_response(request_id, {"acknowledged": True, "method_received": method})

            else: # Placeholder for other specific non-tool methods if any.
                logger.warning(f"[MCPServer handle_request] Unknown method '{method}' for session {session.session_id}. Request ID: {request_id}")
                return {"jsonrpc": "2.0", "id": request_id, "error": {"code": ErrorCode.METHOD_NOT_FOUND.value, "message": f"Method '{method}' not found."}}

        except Exception as e:
            logger.error(f"[MCPServer handle_request] Error: {e}", exc_info=True)
            response_json = self._create_error_response(request_id, ErrorCode.GENERIC_ERROR, f"An unexpected error occurred processing request: {str(e)}")

        logger.debug(f"[MCPServer handle_request] FINAL response_json before return: {response_json}") # ADDED DEBUG LOG
        return response_json

    async def _apply_response_mapping(self, tool_meta: Dict[str, Any], raw_response: Any, session: MCPSession) -> Dict[str, Any]:
        """
        Applies response mapping rules to the raw response from an API tool.
        This creates MCP-compliant responses with both structured and unstructured content.

        Returns:
            Dict[str, Any]: A properly formatted MCP tool response with 'content' array, optional 'structuredContent', and 'isError' flag
        """
        tool_name = tool_meta.get("name")
        # mapping_rules = tool_meta.get("response_mapping") # This would come from SAPI definition

        logger.debug(f"Applying response mapping for tool '{tool_name}'. Raw response: {str(raw_response)[:200]}...")

        # --- Tool-specific hardcoded mapping (example for 'me' tool) ---
        # This section is for demonstration or until generic mapping is fully available.
        # In a production system, prefer rule-based mapping from tool_meta or a more structured approach.
        mapped_data = None

        if tool_name == "me": # Assuming "me" is the tool name for fetching current user info
            logger.info(f"Applying specific mapping for 'me' tool (example).")
            if isinstance(raw_response, dict): # Assuming raw_response is the UserResponse from auth.py
                # Prepare user data structure
                user_data = {
                    "id": str(raw_response.get("id")) if raw_response.get("id") else None,
                    "username": raw_response.get("username"),
                    "email": raw_response.get("email"),
                    "fullName": raw_response.get("full_name"),
                    "role": raw_response.get("role"),
                    "status": raw_response.get("status"),
                    # Ensure boolean fields default to False if missing, or handle as per your UserResponse model
                    "isSystemAdmin": raw_response.get("is_system_admin", False),
                    "isSuperAdmin": raw_response.get("is_super_admin", False),
                    "isServiceProviderAdmin": raw_response.get("is_service_provider_admin", False),
                    "isTenantAdmin": raw_response.get("is_tenant_admin", False),
                }

                session_data = {
                    "sessionId": session.session_id,
                    "projectId": str(session.project_id) if session.project_id else None,
                    # Avoid exposing sensitive system_token details directly
                    "clientInfo": session.client_info
                }

                # Create MCP-compliant response with content array
                mapped_data = {
                    "content": [
                        {
                            "type": "text",
                            "text": _json.dumps({
                                "user": user_data,
                                "permissions": session.permissions,
                                "activeSession": session_data
                            })
                        }
                    ],
                    "isError": False
                }
                logger.debug(f"Mapped 'me' tool response to MCP format with content array")
            else:
                logger.warning(f"'me' tool raw response is not a dict: {type(raw_response)}. Cannot apply specific 'me' mapping.")
                # Create a basic error response
                mapped_data = {
                    "content": [
                        {
                            "type": "text",
                            "text": f"Error: Invalid response format from 'me' tool: {type(raw_response)}"
                        }
                    ],
                    "isError": True
                }

        # If no specific mapping was applied, create a generic MCP-compliant response
        if mapped_data is None:
            logger.debug(f"No specific mapping rules for tool '{tool_name}'. Creating generic MCP-compliant response.")

            # 创建MCP标准响应，同时包含结构化和非结构化内容
            mapped_data = await self._create_mcp_compliant_response(raw_response, tool_name)

        return mapped_data

    async def _create_mcp_compliant_response(self, raw_response: Any, tool_name: str) -> Dict[str, Any]:
        """
        创建符合MCP协议标准的响应，同时包含结构化和非结构化内容
        
        Args:
            raw_response: 原始API响应数据
            tool_name: 工具名称
            
        Returns:
            Dict[str, Any]: MCP标准响应格式
        """
        content_items = []
        structured_content = None
        
        try:
            if isinstance(raw_response, dict):
                # 检查是否已经是MCP格式的内容
                if "type" in raw_response and raw_response["type"] in ["text", "image", "audio", "resource"]:
                    content_items.append(raw_response)
                else:
                    # 对于字典类型，提供结构化内容
                    structured_content = raw_response
                    
                    # 同时提供人类可读的文本内容
                    content_items = await self._parse_response_content(raw_response, tool_name)
                    
            elif isinstance(raw_response, list):
                # 对于数组类型，同样提供结构化内容
                structured_content = raw_response
                
                # 处理数组响应的内容项
                for item in raw_response:
                    if isinstance(item, dict) and "type" in item and item["type"] in ["text", "image", "audio", "resource"]:
                        content_items.append(item)
                    else:
                        content_items.extend(await self._parse_response_content(item, tool_name))
                        
            elif isinstance(raw_response, str):
                # 检查是否为特殊格式的字符串
                content_items.extend(await self._parse_string_content(raw_response, tool_name))
                
            else:
                # 其他类型序列化为JSON
                try:
                    json_text = safe_json_dumps(raw_response)
                    content_items.append({
                        "type": "text", 
                        "text": json_text
                    })
                    # 如果是简单数据类型，也提供结构化内容
                    if isinstance(raw_response, (int, float, bool)):
                        structured_content = raw_response
                except Exception as e_dumps:
                    logger.warning(f"Failed to JSON serialize response for tool '{tool_name}': {e_dumps}. Using str() fallback.")
                    content_items.append({
                        "type": "text", 
                        "text": str(raw_response)
                    })

            # 构建符合MCP标准的响应
            mcp_response = {
                "content": content_items if content_items else [{"type": "text", "text": "Empty response"}],
                "isError": False
            }
            
            # 如果有结构化内容，添加到响应中
            if structured_content is not None:
                mcp_response["structuredContent"] = structured_content
                
            return mcp_response
            
        except Exception as e:
            logger.error(f"Error creating MCP compliant response for tool '{tool_name}': {e}")
            return {
                "content": [{"type": "text", "text": f"Error processing response: {str(e)}"}],
                "isError": True
            }

    async def _parse_response_content(self, data: Any, tool_name: str) -> List[Dict[str, Any]]:
        """
        智能解析响应内容，为API响应创建人类可读的文本内容
        
        Args:
            data: 响应数据
            tool_name: 工具名称
            
        Returns:
            List[Dict[str, Any]]: MCP内容项列表
        """
        content_items = []
        
        if isinstance(data, dict):
            # 检查常见的图片字段
            image_fields = ["image", "images", "picture", "pictures", "photo", "photos", "img", "imgs"]
            file_fields = ["file", "files", "attachment", "attachments", "document", "documents"]
            url_fields = ["url", "urls", "link", "links", "href"]
            
            for field in image_fields:
                if field in data:
                    image_content = await self._process_image_content(data[field], field)
                    if image_content:
                        content_items.extend(image_content)
            
            for field in file_fields:
                if field in data:
                    file_content = await self._process_file_content(data[field], field)
                    if file_content:
                        content_items.extend(file_content)
            
            for field in url_fields:
                if field in data:
                    url_content = await self._process_url_content(data[field], field)
                    if url_content:
                        content_items.extend(url_content)
            
            # 检查是否有文本内容
            text_fields = ["text", "content", "message", "description", "result", "output"]
            for field in text_fields:
                if field in data and data[field]:
                    content_items.append({
                        "type": "text",
                        "text": str(data[field])
                    })
            
            # 如果没有找到特殊字段，创建格式化的文本摘要
            if not content_items:
                formatted_text = await self._format_dict_as_text(data, tool_name)
                content_items.append({
                    "type": "text",
                    "text": formatted_text
                })
        
        elif isinstance(data, list):
            # 处理列表数据，创建摘要
            if data:
                summary_text = await self._format_list_as_text(data, tool_name)
                content_items.append({
                    "type": "text", 
                    "text": summary_text
                })
            else:
                content_items.append({
                    "type": "text",
                    "text": "空列表"
                })
        
        else:
            # 其他类型，直接转换为文本
            content_items.append({
                "type": "text",
                "text": str(data)
            })
        
        return content_items

    async def _format_dict_as_text(self, data: dict, tool_name: str) -> str:
        """
        将字典数据格式化为人类可读的文本
        
        Args:
            data: 字典数据
            tool_name: 工具名称
            
        Returns:
            str: 格式化的文本
        """
        try:
            # 对于少量字段的字典，创建摘要格式
            if len(data) <= 10:
                lines = []
                for key, value in data.items():
                    if isinstance(value, (dict, list)):
                        lines.append(f"{key}: {type(value).__name__} ({len(value)} items)" if hasattr(value, '__len__') else f"{key}: {type(value).__name__}")
                    else:
                        lines.append(f"{key}: {value}")
                return "\n".join(lines)
            else:
                # 对于大量字段的字典，提供JSON格式
                return safe_json_dumps(data, indent=2)
        except Exception as e:
            logger.warning(f"Error formatting dict for tool '{tool_name}': {e}")
            return safe_json_dumps(data)

    async def _format_list_as_text(self, data: list, tool_name: str) -> str:
        """
        将列表数据格式化为人类可读的文本摘要
        
        Args:
            data: 列表数据
            tool_name: 工具名称
            
        Returns:
            str: 格式化的文本摘要
        """
        try:
            if not data:
                return "空列表"
            
            # 分析列表内容类型
            first_item = data[0]
            total_count = len(data)
            
            if isinstance(first_item, dict):
                # 如果是字典列表，尝试创建表格样式的摘要
                summary_lines = [f"共 {total_count} 项记录:"]
                
                # 获取常见字段作为摘要字段
                common_fields = self._get_common_summary_fields(first_item)
                
                # 显示前几项的摘要
                max_show = min(5, total_count)
                for i, item in enumerate(data[:max_show]):
                    if isinstance(item, dict):
                        item_summary = []
                        for field in common_fields:
                            if field in item:
                                value = item[field]
                                # 限制显示长度
                                if isinstance(value, str) and len(value) > 50:
                                    value = value[:47] + "..."
                                item_summary.append(f"{field}: {value}")
                        
                        if item_summary:
                            summary_lines.append(f"{i+1}. {', '.join(item_summary)}")
                        else:
                            # 如果没有常见字段，显示JSON
                            summary_lines.append(f"{i+1}. {safe_json_dumps(item)[:100]}...")
                
                if total_count > max_show:
                    summary_lines.append(f"... 还有 {total_count - max_show} 项")
                
                return "\n".join(summary_lines)
            
            else:
                # 非字典列表，直接显示前几项
                summary_lines = [f"列表包含 {total_count} 项:"]
                max_show = min(10, total_count)
                for i, item in enumerate(data[:max_show]):
                    item_str = str(item)
                    if len(item_str) > 100:
                        item_str = item_str[:97] + "..."
                    summary_lines.append(f"{i+1}. {item_str}")
                
                if total_count > max_show:
                    summary_lines.append(f"... 还有 {total_count - max_show} 项")
                
                return "\n".join(summary_lines)
        
        except Exception as e:
            logger.warning(f"Error formatting list for tool '{tool_name}': {e}")
            return f"列表包含 {len(data)} 项: {safe_json_dumps(data)[:200]}..."

    def _get_common_summary_fields(self, item: dict) -> List[str]:
        """
        获取用于摘要显示的常见字段
        
        Args:
            item: 字典项
            
        Returns:
            List[str]: 优先显示的字段列表
        """
        # 定义优先级字段
        priority_fields = [
            'id', 'name', 'title', 'code', 'description', 'status', 'type',
            'full_name', 'display_name', 'label', 'value', 'count', 'total'
        ]
        
        available_fields = []
        item_keys = list(item.keys())
        
        # 首先添加优先级字段
        for field in priority_fields:
            if field in item_keys:
                available_fields.append(field)
        
        # 如果优先级字段不足3个，添加其他字段
        if len(available_fields) < 3:
            for key in item_keys:
                if key not in available_fields and len(available_fields) < 5:
                    # 跳过一些不适合摘要的字段
                    if not key.endswith(('_at', '_time', '_timestamp')) and key not in ['created_at', 'updated_at', 'metadata', 'settings']:
                        available_fields.append(key)
        
        return available_fields[:5]  # 最多显示5个字段

    async def _parse_string_content(self, text: str, tool_name: str) -> List[Dict[str, Any]]:
        """
        解析字符串内容，识别特殊格式
        
        Args:
            text: 文本内容
            tool_name: 工具名称
            
        Returns:
            List[Dict[str, Any]]: MCP内容项列表
        """
        content_items = []
        
        # 检查是否为data URL（base64编码的图片等）
        if text.startswith("data:"):
            try:
                header, data = text.split(',', 1)
                content_type = header.split(';')[0].split(':')[1]
                
                if content_type.startswith("image/"):
                    content_items.append({
                        "type": "image",
                        "data": text,
                        "mimeType": content_type
                    })
                elif content_type.startswith("audio/"):
                    content_items.append({
                        "type": "audio",
                        "data": text,
                        "mimeType": content_type
                    })
                else:
                    content_items.append({
                        "type": "resource",
                        "resource": {
                            "uri": text,
                            "mimeType": content_type,
                            "name": f"{tool_name}_resource"
                        }
                    })
            except Exception as e:
                logger.warning(f"Failed to parse data URL: {e}")
                content_items.append({"type": "text", "text": text})
        
        # 检查是否为HTTP URL
        elif text.startswith(("http://", "https://")):
            # 尝试检测URL指向的内容类型
            try:
                async with httpx.AsyncClient(timeout=5.0) as client:
                    response = await client.head(text)
                    content_type = response.headers.get("content-type", "").lower()
                    
                    if content_type.startswith("image/"):
                        content_items.append({
                            "type": "image",
                            "data": text,
                            "mimeType": content_type
                        })
                    elif content_type.startswith("audio/"):
                        content_items.append({
                            "type": "audio",
                            "data": text,
                            "mimeType": content_type
                        })
                    elif content_type.startswith("video/"):
                        content_items.append({
                            "type": "resource",
                            "resource": {
                                "uri": text,
                                "mimeType": content_type,
                                "name": f"{tool_name}_video"
                            }
                        })
                    else:
                        content_items.append({
                            "type": "resource",
                            "resource": {
                                "uri": text,
                                "mimeType": content_type or "application/octet-stream",
                                "name": f"{tool_name}_resource"
                            }
                        })
            except Exception as e:
                logger.debug(f"Could not determine content type for URL {text}: {e}")
                # 作为普通链接处理
                content_items.append({"type": "text", "text": text})
        
        # 检查是否为JSON字符串
        elif text.strip().startswith(("{", "[")):
            try:
                parsed_json = _json.loads(text)
                # 递归解析JSON内容
                content_items.extend(await self._parse_response_content(parsed_json, tool_name))
            except _json.JSONDecodeError:
                # 不是有效的JSON，作为普通文本处理
                content_items.append({"type": "text", "text": text})
        
        else:
            # 普通文本
            content_items.append({"type": "text", "text": text})
        
        return content_items

    async def _process_image_content(self, image_data: Any, field_name: str) -> List[Dict[str, Any]]:
        """处理图片内容"""
        content_items = []
        
        if isinstance(image_data, list):
            for i, img in enumerate(image_data):
                item = await self._process_single_image(img, f"{field_name}_{i}")
                if item:
                    content_items.append(item)
        else:
            item = await self._process_single_image(image_data, field_name)
            if item:
                content_items.append(item)
        
        return content_items

    async def _process_single_image(self, image_data: Any, field_name: str) -> Optional[Dict[str, Any]]:
        """处理单个图片"""
        try:
            if isinstance(image_data, str):
                if image_data.startswith("data:image/"):
                    # Base64编码的图片
                    header = image_data.split(',')[0]
                    mime_type = header.split(';')[0].split(':')[1]
                    return {
                        "type": "image",
                        "data": image_data,
                        "mimeType": mime_type
                    }
                elif image_data.startswith(("http://", "https://")):
                    # 图片URL
                    return {
                        "type": "image",
                        "data": image_data,
                        "mimeType": "image/jpeg"  # 默认类型
                    }
            elif isinstance(image_data, dict):
                # 结构化图片数据
                if "url" in image_data or "data" in image_data:
                    return {
                        "type": "image",
                        "data": image_data.get("url") or image_data.get("data"),
                        "mimeType": image_data.get("mimeType", "image/jpeg")
                    }
        except Exception as e:
            logger.warning(f"Error processing image data for {field_name}: {e}")
        
        return None

    async def _process_file_content(self, file_data: Any, field_name: str) -> List[Dict[str, Any]]:
        """处理文件内容"""
        content_items = []
        
        if isinstance(file_data, list):
            for i, file_item in enumerate(file_data):
                item = await self._process_single_file(file_item, f"{field_name}_{i}")
                if item:
                    content_items.append(item)
        else:
            item = await self._process_single_file(file_data, field_name)
            if item:
                content_items.append(item)
        
        return content_items

    async def _process_single_file(self, file_data: Any, field_name: str) -> Optional[Dict[str, Any]]:
        """处理单个文件"""
        try:
            if isinstance(file_data, str):
                if file_data.startswith("data:"):
                    # Data URL
                    header = file_data.split(',')[0]
                    mime_type = header.split(';')[0].split(':')[1]
                    return {
                        "type": "resource",
                        "resource": {
                            "uri": file_data,
                            "mimeType": mime_type,
                            "name": field_name
                        }
                    }
                elif file_data.startswith(("http://", "https://")):
                    # 文件URL
                    return {
                        "type": "resource",
                        "resource": {
                            "uri": file_data,
                            "mimeType": "application/octet-stream",
                            "name": field_name
                        }
                    }
            elif isinstance(file_data, dict):
                # 结构化文件数据
                if "url" in file_data or "data" in file_data:
                    return {
                        "type": "resource",
                        "resource": {
                            "uri": file_data.get("url") or file_data.get("data"),
                            "mimeType": file_data.get("mimeType", "application/octet-stream"),
                            "name": file_data.get("name", field_name)
                        }
                    }
        except Exception as e:
            logger.warning(f"Error processing file data for {field_name}: {e}")
        
        return None

    async def _process_url_content(self, url_data: Any, field_name: str) -> List[Dict[str, Any]]:
        """处理URL内容"""
        content_items = []
        
        if isinstance(url_data, list):
            for i, url in enumerate(url_data):
                if isinstance(url, str) and url.startswith(("http://", "https://")):
                    content_items.append({
                        "type": "resource",
                        "resource": {
                            "uri": url,
                            "mimeType": "text/html",
                            "name": f"{field_name}_{i}"
                        }
                    })
        elif isinstance(url_data, str) and url_data.startswith(("http://", "https://")):
            content_items.append({
                "type": "resource",
                "resource": {
                    "uri": url_data,
                    "mimeType": "text/html",
                    "name": field_name
                }
            })
        
        return content_items

    async def _execute_tool_and_stream_results(self, session: MCPSession, tool_function: Callable, tool_name: str, tool_arguments: Dict[str, Any], original_request_id: Optional[str]):
        """
        Executes a tool that is expected to stream results back to the client
        via the session's internal_message_queue.
        """
        logger.info(f"[MCPServer _execute_tool_and_stream_results] Starting execution for tool '{tool_name}'. Session: {session.session_id}, Request ID: {original_request_id}")

        if not session.internal_message_queue:
            logger.error(f"[MCPServer _execute_tool_and_stream_results] FATAL: No internal_message_queue for session {session.session_id} to stream tool '{tool_name}'.")
            return

        try:
            # The tool_function itself is responsible for handling its execution and
            # putting appropriately formatted messages (dicts for EventSourceResponse)
            # onto session.internal_message_queue.
            await tool_function(session, tool_arguments)

            # After the tool function completes, send a final "stream_end" message
            final_mcp_notification = {
                "jsonrpc": "2.0",
                "method": "notifications/toolStatus",
                "params": {
                    "toolCallId": original_request_id,
                    "toolName": tool_name,
                    "status": "completed",
                    "content": [
                        {
                            "type": "text",
                            "text": f"Tool '{tool_name}' stream finished."
                        }
                    ]
                }
            }
            await session.internal_message_queue.put(final_mcp_notification)
            logger.info(f"[MCPServer _execute_tool_and_stream_results] Tool '{tool_name}' execution task completed. Sent toolStatus notification. Session: {session.session_id}")

        except Exception as e_tool_stream:
            logger.error(f"[MCPServer _execute_tool_and_stream_results] Error during streaming execution of tool '{tool_name}': {e_tool_stream}", exc_info=True)
            # Send an error message over the stream
            mcp_error_notification = {
                "jsonrpc": "2.0",
                "method": "notifications/toolError",
                "params": {
                    "toolCallId": original_request_id,
                    "toolName": tool_name,
                    "content": [
                        {
                            "type": "text",
                            "text": f"Error during streaming execution of tool '{tool_name}': {str(e_tool_stream)}"
                        }
                    ],
                    "error": {
                        "message": f"Error during streaming execution of tool '{tool_name}': {str(e_tool_stream)}",
                        "code": ErrorCode.TOOL_EXECUTION_ERROR.value
                    }
                }
            }
            try:
                await session.internal_message_queue.put(mcp_error_notification)
                logger.info(f"[MCPServer _execute_tool_and_stream_results] Sent toolError notification for tool '{tool_name}'. Session: {session.session_id}")
            except Exception as e_put_err:
                logger.error(f"[MCPServer _execute_tool_and_stream_results] Failed to put error message on queue for tool '{tool_name}': {e_put_err}", exc_info=True)

    def get_session(self, session_id: str) -> Optional[MCPSession]:
        """Retrieves a session from the store and updates its last activity time."""
        session = self.session_store.get(session_id)
        if session:
            session.last_activity_time = asyncio.get_event_loop().time()
            logger.debug(f"[MCPServer get_session] Session {session_id} accessed.")
            return session
        logger.warning(f"[MCPServer get_session] Session {session_id} not found.")
        return None

    async def shutdown(self):
        logger.info(f"Shutting down MCP server {self.server_id}...")
        await self._execute_callbacks(self.on_shutdown_callbacks)
        self.session_store.clear()
        logger.info(f"MCP server {self.server_id} shutdown complete.")

    def _create_error_response(self, request_id: Optional[str], error_code: ErrorCode, message: str) -> Dict[str, Any]:
        return {
            "jsonrpc": "2.0",
            "id": request_id,
            "error": {
                "code": error_code.value,
                "message": message
            }
        }

    def _create_success_response(self, request_id: Optional[str], result: Any) -> Dict[str, Any]:
        return {
            "jsonrpc": "2.0",
            "id": request_id,
            "result": result
        }

    def _determine_content_type(self, tool_meta: Dict[str, Any], params: Dict[str, Any]) -> str:
        """
        确定API调用应该使用的Content-Type
        
        Args:
            tool_meta: 工具元数据
            params: 请求参数
            
        Returns:
            str: "application/json" 或 "multipart/form-data"
        """
        # 检查工具元数据中是否指定了multipart/form-data
        endpoint_meta = tool_meta.get("metadata", {}).get("endpoint", {})
        sapi_params = endpoint_meta.get("sapi_parameters", {})
        request_body = sapi_params.get("requestBody", {})
        
        if request_body:
            content = request_body.get("content", {})
            if "multipart/form-data" in content:
                logger.debug(f"Tool '{tool_meta.get('name')}' requires multipart/form-data based on metadata")
                return "multipart/form-data"
        
        # 检查参数中是否包含文件类型的字段
        for key, value in params.items():
            if self._is_file_parameter(key, value, tool_meta):
                logger.debug(f"Tool '{tool_meta.get('name')}' requires multipart/form-data due to file parameter: {key}")
                return "multipart/form-data"
        
        return "application/json"

    def _is_file_parameter(self, param_name: str, param_value: Any, tool_meta: Dict[str, Any]) -> bool:
        """
        检查参数是否为文件类型

        Args:
            param_name: 参数名
            param_value: 参数值
            tool_meta: 工具元数据
            
        Returns:
            bool: 是否为文件参数
        """
        # 检查参数名是否暗示文件类型
        file_param_names = ["image", "images", "file", "files", "attachment", "attachments", "photo", "photos"]
        if any(file_name in param_name.lower() for file_name in file_param_names):
            return True
        
        # 检查工具schema中的参数定义
        input_schema = tool_meta.get("inputSchema", {})
        properties = input_schema.get("properties", {})
        param_schema = properties.get(param_name, {})
        
        # 检查是否为binary格式
        if param_schema.get("format") == "binary":
            return True
        
        # 检查是否为文件数组
        if param_schema.get("type") == "array":
            items = param_schema.get("items", {})
            if items.get("format") == "binary":
                return True
        
        return False

    async def _prepare_multipart_data(self, params: Dict[str, Any], tool_meta: Dict[str, Any]) -> tuple[Dict[str, Any], Dict[str, Any]]:
        """
        准备multipart/form-data请求的数据
        
        Args:
            params: 请求参数
            tool_meta: 工具元数据
            
        Returns:
            tuple: (files_data, form_data)
        """
        files_data = {}
        form_data = {}
        
        for key, value in params.items():
            if self._is_file_parameter(key, value, tool_meta):
                # 处理文件参数
                if isinstance(value, list):
                    # 多个文件
                    for i, file_item in enumerate(value):
                        file_data = await self._process_file_data(file_item, f"{key}_{i}")
                        if file_data:
                            files_data[f"{key}"] = file_data
                else:
                    # 单个文件
                    file_data = await self._process_file_data(value, key)
                    if file_data:
                        files_data[key] = file_data
            else:
                # 普通表单字段
                form_data[key] = str(value) if value is not None else ""
        
        return files_data, form_data

    async def _process_file_data(self, file_input: Any, field_name: str) -> Optional[tuple]:
        """
        处理文件数据，支持多种输入格式
        
        Args:
            file_input: 文件输入（可能是base64字符串、URL、文件路径等）
            field_name: 字段名
            
        Returns:
            Optional[tuple]: (filename, file_content, content_type) 或 None
        """
        try:
            if isinstance(file_input, str):
                # 检查是否为base64编码的图片
                if file_input.startswith("data:"):
                    return await self._process_base64_data(file_input, field_name)
                
                # 检查是否为URL
                elif file_input.startswith(("http://", "https://")):
                    return await self._process_url_data(file_input, field_name)
                
                # 检查是否为文件路径
                elif os.path.exists(file_input):
                    return await self._process_file_path(file_input, field_name)
                
                # 作为普通文本处理
                else:
                    return (f"{field_name}.txt", file_input.encode('utf-8'), "text/plain")
            
            elif isinstance(file_input, bytes):
                # 直接的字节数据
                content_type = self._detect_content_type(file_input)
                extension = self._get_extension_from_content_type(content_type)
                return (f"{field_name}{extension}", file_input, content_type)
            
            else:
                logger.warning(f"Unsupported file input type for {field_name}: {type(file_input)}")
                return None
                
        except Exception as e:
            logger.error(f"Error processing file data for {field_name}: {e}", exc_info=True)
            return None

    async def _process_base64_data(self, data_url: str, field_name: str) -> Optional[tuple]:
        """处理base64编码的数据URL"""
        try:
            # 解析data URL: data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD...
            header, data = data_url.split(',', 1)
            content_type = header.split(';')[0].split(':')[1]
            
            import base64
            file_content = base64.b64decode(data)
            
            extension = self._get_extension_from_content_type(content_type)
            filename = f"{field_name}{extension}"
            
            return (filename, file_content, content_type)
        except Exception as e:
            logger.error(f"Error processing base64 data for {field_name}: {e}")
            return None

    async def _process_url_data(self, url: str, field_name: str) -> Optional[tuple]:
        """处理URL数据"""
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.get(url)
                response.raise_for_status()
                
                content_type = response.headers.get("content-type", "application/octet-stream")
                file_content = response.content
                
                # 尝试从URL获取文件名
                from urllib.parse import urlparse
                parsed_url = urlparse(url)
                filename = os.path.basename(parsed_url.path)
                
                if not filename or '.' not in filename:
                    extension = self._get_extension_from_content_type(content_type)
                    filename = f"{field_name}{extension}"
                
                return (filename, file_content, content_type)
        except Exception as e:
            logger.error(f"Error downloading file from URL {url} for {field_name}: {e}")
            return None

    async def _process_file_path(self, file_path: str, field_name: str) -> Optional[tuple]:
        """处理本地文件路径"""
        try:
            import aiofiles
            async with aiofiles.open(file_path, 'rb') as f:
                file_content = await f.read()
            
            filename = os.path.basename(file_path)
            content_type = self._detect_content_type(file_content, filename)
            
            return (filename, file_content, content_type)
        except Exception as e:
            logger.error(f"Error reading file {file_path} for {field_name}: {e}")
            return None

    def _detect_content_type(self, file_content: bytes, filename: str = None) -> str:
        """检测文件内容类型"""
        try:
            import magic
            return magic.from_buffer(file_content, mime=True)
        except ImportError:
            # 如果没有python-magic，使用简单的文件扩展名检测
            if filename:
                extension = os.path.splitext(filename)[1].lower()
                content_type_map = {
                    '.jpg': 'image/jpeg',
                    '.jpeg': 'image/jpeg',
                    '.png': 'image/png',
                    '.gif': 'image/gif',
                    '.webp': 'image/webp',
                    '.pdf': 'application/pdf',
                    '.txt': 'text/plain',
                    '.json': 'application/json',
                    '.xml': 'application/xml',
                }
                return content_type_map.get(extension, 'application/octet-stream')
            
            # 简单的字节检测
            if file_content.startswith(b'\xff\xd8\xff'):
                return 'image/jpeg'
            elif file_content.startswith(b'\x89PNG\r\n\x1a\n'):
                return 'image/png'
            elif file_content.startswith(b'GIF8'):
                return 'image/gif'
            elif file_content.startswith(b'RIFF') and b'WEBP' in file_content[:12]:
                return 'image/webp'
            else:
                return 'application/octet-stream'
        except Exception:
            return 'application/octet-stream'

    def _get_extension_from_content_type(self, content_type: str) -> str:
        """从content-type获取文件扩展名"""
        content_type_map = {
            'image/jpeg': '.jpg',
            'image/png': '.png',
            'image/gif': '.gif',
            'image/webp': '.webp',
            'application/pdf': '.pdf',
            'text/plain': '.txt',
            'application/json': '.json',
            'application/xml': '.xml',
        }
        return content_type_map.get(content_type, '.bin')

# Global server instance to be created and configured by the specific main.py of the MCP server.
# Example:
# mcp_server = MCPServer(server_id=os.environ.get("MCP_SERVER_ID", "default-mcp-id"))
# mcp_server.register_tool("example.echo", example_tool)

# 全局MCP服务器实例，由main.py创建和配置
mcp_server = None

# WebSocket Handler (Template - can be used by specific server main.py if WebSocket transport is enabled)
# async def websocket_handler(websocket: WebSocket, server_id_from_path: str, mcp_server_instance: MCPServer):
#     await websocket.accept()
#     ws_session_id: Optional[str] = None
#     ws_headers_from_init: Dict[str,str] = {} # Store headers from initialize for subsequent requests on this ws

#     try:
#         while True:
#             data = await websocket.receive_text()
#             logger.debug(f"WS <<< {data}")
#             try:
#                 request_json = _json.loads(data)
#             except _json.JSONDecodeError:
#                 await websocket.send_text(_json.dumps({"jsonrpc": "2.0", "error": {"code": -32700, "message": "Parse error"}, "id": None}))
#                 continue

#             method = request_json.get("method")
#             params = request_json.get("params", {})
#             req_id = request_json.get("id")

#             current_headers_for_call = ws_headers_from_init # Use stored headers for non-init calls

#             if method == "initialize":
#                 # For WebSocket, 'initialize' might carry initial auth headers in its params or rely on upgrade headers
#                 # This example assumes headers might be passed in params for initialize specifically for WS
#                 init_headers = params.pop("headers", {}) if isinstance(params, dict) else {}
#                 # If not in params, it should use actual websocket upgrade headers if available (complex to map reliably here)
#                 # For now, we rely on them being explicitly passed in params for WS init.

#                 response = await mcp_server_instance.handle_initialize(req_id, params, init_headers)
#                 if "result" in response and "session_id" in response["result"]:
#                     ws_session_id = response["result"]["session_id"]
#                     ws_headers_from_init = init_headers # Store headers used for successful init
#                     logger.info(f"WS Connection initialized with session_id: {ws_session_id}")
#                 await websocket.send_text(_json.dumps(response))
#                 logger.debug(f"WS >>> {_json.dumps(response)}")
#             elif ws_session_id: # Only process if WebSocket connection has been initialized
#                 if not ws_headers_from_init: # Safety check
#                     logger.warning("WS session exists but headers from init are missing. This shouldn't happen.")
#                     # Fallback or error
#                     await websocket.send_text(_json.dumps({"jsonrpc": "2.0", "id": req_id, "error": {"code": ErrorCode.AUTHENTICATION_FAILED.value, "message": "Internal state error: missing auth context for session."}}))
#                     continue

#                 # For subsequent calls on an initialized WebSocket, use the session_id and stored headers
#                 response = await mcp_server_instance.handle_request(method, params, req_id, ws_headers_from_init, ws_session_id)
#                 await websocket.send_text(_json.dumps(response))
#                 logger.debug(f"WS >>> {_json.dumps(response)}")
#             else: # Not initialized, and not an initialize request
#                 logger.warning(f"WS request for method '{method}' rejected, session not initialized.")
#                 await websocket.send_text(_json.dumps({
#                     "jsonrpc": "2.0",
#                     "id": req_id,
#                     "error": {"code": ErrorCode.INITIALIZATION_FAILED.value, "message": "WebSocket not initialized. Call 'initialize' first with auth headers."}
#                 }))

#     except Exception as e: # Includes WebSocketDisconnect
#         logger.error(f"WebSocket exception/disconnect for session {ws_session_id}: {e}", exc_info=True)
#     finally:
#         if ws_session_id:
#             if ws_session_id in mcp_server_instance.session_store:
#                 del mcp_server_instance.session_store[ws_session_id]
#             if ws_session_id in mcp_server_instance.is_initialized_for_session:
#                 del mcp_server_instance.is_initialized_for_session[ws_session_id]
#         logger.info(f"WebSocket connection closed for session: {ws_session_id}")
#         try:
#             await websocket.close()
#         except RuntimeError: # Already closed
#             pass
#         except Exception as e_close: # Other close errors
#             logger.warning(f"Error trying to close WebSocket: {e_close}")

# 从utils.py导入心跳检测函数
async def start_heartbeat(server_instance: "MCPServer") -> None:
    """
    启动心跳检测，定期清理过期会话

    Args:
        server_instance: MCP服务器实例
    """
    if not server_instance:
        logger.error("server_instance为空，无法启动心跳检测")
        return

    logger.info(f"启动心跳检测，服务器ID: {server_instance.server_id}")

    # 会话过期时间（秒）
    session_expiry_seconds = 3600  # 1小时

    try:
        while True:
            try:
                # 当前时间
                now = asyncio.get_event_loop().time()

                # 需要删除的会话ID列表
                sessions_to_remove = []

                # 检查所有会话
                for session_id, session in server_instance.session_store.items():
                    # 检查会话是否过期
                    if hasattr(session, 'last_activity_time'):
                        if now - session.last_activity_time > session_expiry_seconds:
                            logger.info(f"会话 {session_id} 已过期，标记为删除")
                            sessions_to_remove.append(session_id)

                # 删除过期会话
                for session_id in sessions_to_remove:
                    if session_id in server_instance.session_store:
                        del server_instance.session_store[session_id]
                    if session_id in server_instance.is_initialized_for_session:
                        del server_instance.is_initialized_for_session[session_id]
                    logger.info(f"会话已过期并被删除: {session_id}")

                # 等待一段时间再次检查
                await asyncio.sleep(60)  # 每分钟检查一次
            except asyncio.CancelledError:
                logger.info("心跳检测任务被取消")
                break
            except Exception as e:
                logger.error(f"心跳检测异常: {str(e)}", exc_info=True)
                await asyncio.sleep(60)  # 发生异常时，等待一段时间再次尝试
    except asyncio.CancelledError:
        logger.info("心跳检测主循环被取消")
    finally:
        logger.info("心跳检测已停止")

# Global server instance to be created and configured by the specific main.py of the MCP server.
# This allows the main.py to import this base.py and create a server instance.
server_instance: Optional[MCPServer] = None