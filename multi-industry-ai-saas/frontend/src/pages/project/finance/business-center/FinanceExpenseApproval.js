import React, { useState, useEffect } from 'react';
import {
  Table,
  Card,
  Button,
  message,
  Tabs,
  Typography,
  Space,
  Tag,
  Modal,
  Form,
  Input,
  DatePicker,
  Select,
  Statistic,
  Row,
  Col,
  Tooltip,
  Descriptions,
  Image
} from 'antd';
import {
  ReloadOutlined,
  SearchOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ExclamationCircleOutlined,
  DollarOutlined,
  PlusOutlined,
  DownloadOutlined
} from '@ant-design/icons';
import apiService from '../../../../services/api';
import dayjs from 'dayjs';

const { Title } = Typography;
const { TabPane } = Tabs;
const { RangePicker } = DatePicker;
const { Option } = Select;
const { confirm } = Modal;

/**
 * 费用审核组件
 * 汇总门店管理日常店务下的日常费用
 */
const FinanceExpenseApproval = () => {
  // 状态变量
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('detail');
  const [expenseList, setExpenseList] = useState([]);
  const [stores, setStores] = useState([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });
  const [filters, setFilters] = useState({
    store_id: null,
    expense_type: null,
    date_range: null,
    status: null
  });
  const [expenseStats, setExpenseStats] = useState({
    total: 0,
    by_store: {},
    by_type: {
      misc: 0,
      material: 0,
      electricity: 0,
      internet: 0,
      other: 0
    },
    by_month: {}
  });
  const [selectedExpense, setSelectedExpense] = useState(null);
  const [detailVisible, setDetailVisible] = useState(false);
  const [markAbnormalVisible, setMarkAbnormalVisible] = useState(false);
  const [addExpenseVisible, setAddExpenseVisible] = useState(false);
  const [form] = Form.useForm();
  const [addExpenseForm] = Form.useForm();

  // 初始化
  useEffect(() => {
    fetchStores();
  }, []);

  // 监听筛选条件变化，刷新数据
  useEffect(() => {
    fetchExpenseList();
    fetchExpenseStatistics();
  }, [filters, pagination.current, pagination.pageSize, activeTab]);

  // 获取门店列表
  const fetchStores = async () => {
    try {
      const response = await apiService.project.store.getList();
      if (response && response.items) {
        setStores(response.items);
      }
    } catch (error) {
      console.error('获取门店列表失败:', error);
      message.error('获取门店列表失败');
    }
  };

  // 获取费用列表
  const fetchExpenseList = async (params = {}) => {
    setLoading(true);
    try {
      // 准备查询参数
      const queryParams = {
        page: params.page || pagination.current,
        page_size: params.pageSize || pagination.pageSize,
        ...filters
      };

      // 处理日期范围
      if (filters.date_range && filters.date_range.length === 2) {
        queryParams.start_date = filters.date_range[0].format('YYYY-MM-DD');
        queryParams.end_date = filters.date_range[1].format('YYYY-MM-DD');
        delete queryParams.date_range;
      }

      // 调用API获取费用列表
      const response = await apiService.project.finance.getExpenseList(queryParams);

      if (response && response.items) {
        setExpenseList(response.items);
        setPagination({
          ...pagination,
          current: params.page || pagination.current,
          total: response.total || 0
        });
      } else {
        message.error(response?.message || '获取费用列表失败');
      }
    } catch (error) {
      console.error('获取费用列表失败:', error);
      message.error('获取费用列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取费用统计
  const fetchExpenseStatistics = async () => {
    try {
      // 准备查询参数
      const queryParams = {};

      // 处理日期范围
      if (filters.date_range && filters.date_range.length === 2) {
        queryParams.start_date = filters.date_range[0].format('YYYY-MM-DD');
        queryParams.end_date = filters.date_range[1].format('YYYY-MM-DD');
      }

      if (filters.store_id) {
        queryParams.store_id = filters.store_id;
      }

      // 调用API获取费用统计
      const response = await apiService.project.finance.getExpenseStatistics(queryParams);

      if (response && response.data) {
        setExpenseStats(response.data);
      }
    } catch (error) {
      console.error('获取费用统计失败:', error);
      message.error('获取费用统计失败');
    }
  };

  // 处理标签页切换
  const handleTabChange = (key) => {
    setActiveTab(key);
  };

  // 处理表格变化
  const handleTableChange = (pagination, filters, sorter) => {
    fetchExpenseList({
      page: pagination.current,
      pageSize: pagination.pageSize,
      ...filters
    });
  };

  // 处理筛选变化
  const handleFilterChange = (key, value) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);

    // 重置分页
    setPagination({
      ...pagination,
      current: 1
    });
  };

  // 查看详情
  const handleViewDetail = (record) => {
    setSelectedExpense(record);
    setDetailVisible(true);
  };

  // 标记异常
  const handleMarkAbnormal = (record) => {
    setSelectedExpense(record);
    form.resetFields();
    setMarkAbnormalVisible(true);
  };

  // 提交标记异常
  const handleMarkAbnormalSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      // 调用API标记异常
      const response = await apiService.project.finance.markExpenseAbnormal(
        selectedExpense.id,
        {
          is_abnormal: true,
          abnormal_reason: values.abnormal_reason
        }
      );

      if (response && response.success) {
        message.success('标记异常成功');
        setMarkAbnormalVisible(false);
        fetchExpenseList();
      } else {
        message.error(response?.message || '标记异常失败');
      }
    } catch (error) {
      console.error('标记异常失败:', error);
      if (error.errorFields) {
        // 表单验证错误
        return;
      }
      message.error('标记异常失败: ' + (error.message || '未知错误'));
    } finally {
      setLoading(false);
    }
  };

  // 新增费用
  const handleAddExpense = () => {
    addExpenseForm.resetFields();
    setAddExpenseVisible(true);
  };

  // 导出费用列表
  const handleExportExpenses = async () => {
    try {
      setLoading(true);

      // 准备查询参数
      const queryParams = { ...filters };

      // 处理日期范围
      if (filters.date_range && filters.date_range.length === 2) {
        queryParams.start_date = filters.date_range[0].format('YYYY-MM-DD');
        queryParams.end_date = filters.date_range[1].format('YYYY-MM-DD');
        delete queryParams.date_range;
      }

      // 调用导出API
      const response = await apiService.project.finance.exportExpenses(queryParams);

      // 创建下载链接
      const url = window.URL.createObjectURL(new Blob([response]));
      const link = document.createElement('a');
      link.href = url;

      // 设置文件名
      const fileName = `费用列表_${dayjs().format('YYYY-MM-DD_HHmmss')}.xlsx`;
      link.setAttribute('download', fileName);

      // 触发下载
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      message.success('导出成功');
    } catch (error) {
      console.error('导出费用列表失败:', error);
      message.error('导出费用列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 提交新增费用
  const handleAddExpenseSubmit = async () => {
    try {
      const values = await addExpenseForm.validateFields();
      setLoading(true);

      // 处理日期
      const expenseData = {
        ...values,
        expense_date: values.expense_date.format('YYYY-MM-DD')
      };

      // 调用API新增费用
      const response = await apiService.project.finance.addStoreExpense(expenseData);

      if (response && response.success) {
        message.success('新增费用成功');
        setAddExpenseVisible(false);
        fetchExpenseList();
        fetchExpenseStatistics();
      } else {
        message.error(response?.message || '新增费用失败');
      }
    } catch (error) {
      console.error('新增费用失败:', error);
      if (error.errorFields) {
        // 表单验证错误
        return;
      }
      message.error('新增费用失败: ' + (error.message || '未知错误'));
    } finally {
      setLoading(false);
    }
  };

  // 渲染统计卡片
  const renderStatisticCards = () => {
    return (
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总费用金额"
              value={expenseStats.total}
              precision={2}
              prefix={<DollarOutlined />}
              suffix="元"
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="杂费"
              value={expenseStats.by_type?.misc || 0}
              precision={2}
              suffix="元"
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="物料费"
              value={expenseStats.by_type?.material || 0}
              precision={2}
              suffix="元"
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="电费"
              value={expenseStats.by_type?.electricity || 0}
              precision={2}
              suffix="元"
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="网费"
              value={expenseStats.by_type?.internet || 0}
              precision={2}
              suffix="元"
            />
          </Card>
        </Col>
      </Row>
    );
  };

  // 筛选表单
  const FilterForm = () => (
    <Form layout="inline" style={{ marginBottom: 16 }}>
      <Form.Item label="门店">
        <Select
          placeholder="选择门店"
          style={{ width: 200 }}
          allowClear
          onChange={(value) => handleFilterChange('store_id', value)}
          value={filters.store_id}
        >
          {stores.map(store => (
            <Option key={store.id} value={store.id}>{store.name}</Option>
          ))}
        </Select>
      </Form.Item>
      <Form.Item label="费用类型">
        <Select
          placeholder="选择费用类型"
          style={{ width: 150 }}
          allowClear
          onChange={(value) => handleFilterChange('expense_type', value)}
          value={filters.expense_type}
        >
          <Option value="misc">杂费</Option>
          <Option value="material">物料费</Option>
          <Option value="electricity">电费</Option>
          <Option value="internet">网费</Option>
          <Option value="other">其他</Option>
        </Select>
      </Form.Item>
      <Form.Item label="日期范围">
        <RangePicker
          onChange={(dates) => handleFilterChange('date_range', dates)}
          value={filters.date_range}
        />
      </Form.Item>
      <Form.Item label="状态">
        <Select
          placeholder="选择状态"
          style={{ width: 150 }}
          allowClear
          onChange={(value) => handleFilterChange('status', value)}
          value={filters.status}
        >
          <Option value="normal">正常</Option>
          <Option value="abnormal">异常</Option>
        </Select>
      </Form.Item>
      <Form.Item>
        <Button
          type="primary"
          icon={<SearchOutlined />}
          onClick={() => fetchExpenseList()}
        >
          查询
        </Button>
      </Form.Item>
    </Form>
  );

  // 表格列定义
  const columns = [
    {
      title: '费用编号',
      dataIndex: 'expense_number',
      key: 'expense_number',
      width: 150,
    },
    {
      title: '门店',
      dataIndex: 'store_name',
      key: 'store_name',
      width: 150,
    },
    {
      title: '费用类型',
      dataIndex: 'expense_type',
      key: 'expense_type',
      width: 120,
      render: (text) => {
        const typeMap = {
          misc: '杂费',
          material: '物料费',
          electricity: '电费',
          internet: '网费',
          other: '其他'
        };
        return typeMap[text] || text;
      }
    },
    {
      title: '费用日期',
      dataIndex: 'expense_date',
      key: 'expense_date',
      width: 120,
      render: (text) => text ? dayjs(text).format('YYYY-MM-DD') : '-'
    },
    {
      title: '金额',
      dataIndex: 'amount',
      key: 'amount',
      width: 120,
      render: (text) => `¥${parseFloat(text).toFixed(2)}`
    },
    {
      title: '状态',
      dataIndex: 'is_abnormal',
      key: 'is_abnormal',
      width: 100,
      render: (text, record) => (
        <Tag color={text ? 'error' : 'success'}>
          {text ? '异常' : '正常'}
        </Tag>
      )
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_, record) => (
        <Space size="small">
          <Button type="link" onClick={() => handleViewDetail(record)}>
            详情
          </Button>
          {!record.is_abnormal && (
            <Button type="link" danger onClick={() => handleMarkAbnormal(record)}>
              标记异常
            </Button>
          )}
        </Space>
      )
    }
  ];

  return (
    <div className="finance-expense-approval">
      <Tabs activeKey={activeTab} onChange={handleTabChange}>
        <TabPane tab="明细数据" key="detail">
          <div style={{ marginBottom: 16 }}>
            <FilterForm />
          </div>

          <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between' }}>
            <div>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={handleAddExpense}
                style={{ marginRight: 8 }}
              >
                新增费用
              </Button>
              <Button
                icon={<DownloadOutlined />}
                onClick={handleExportExpenses}
                style={{ marginRight: 8 }}
              >
                导出Excel
              </Button>
            </div>
            <div>
              <Button
                icon={<ReloadOutlined />}
                onClick={() => {
                  fetchExpenseList();
                  fetchExpenseStatistics();
                }}
              >
                刷新
              </Button>
            </div>
          </div>

          {expenseStats.total > 0 && renderStatisticCards()}

          <Table
            columns={columns}
            dataSource={expenseList}
            rowKey="id"
            pagination={pagination}
            onChange={handleTableChange}
            loading={loading}
            scroll={{ x: 1200 }}
          />
        </TabPane>

        <TabPane tab="门店汇总" key="summary">
          <div style={{ marginBottom: 16 }}>
            <FilterForm />
          </div>

          {/* 门店汇总表格 - 将在后续实现 */}
          <Table
            loading={loading}
            // 门店汇总表格配置
          />
        </TabPane>
      </Tabs>

      {/* 详情弹窗 */}
      <Modal
        title="费用详情"
        open={detailVisible}
        onCancel={() => setDetailVisible(false)}
        footer={[
          <Button key="close" onClick={() => setDetailVisible(false)}>
            关闭
          </Button>,
          !selectedExpense?.is_abnormal && (
            <Button
              key="mark-abnormal"
              type="primary"
              danger
              onClick={() => {
                setDetailVisible(false);
                handleMarkAbnormal(selectedExpense);
              }}
            >
              标记异常
            </Button>
          )
        ]}
        width={700}
      >
        {selectedExpense && (
          <div className="expense-detail">
            <Descriptions bordered column={2}>
              <Descriptions.Item label="费用编号" span={2}>
                {selectedExpense.expense_number}
              </Descriptions.Item>
              <Descriptions.Item label="门店">
                {selectedExpense.store_name}
              </Descriptions.Item>
              <Descriptions.Item label="费用类型">
                {(() => {
                  const typeMap = {
                    misc: '杂费',
                    material: '物料费',
                    electricity: '电费',
                    internet: '网费',
                    other: '其他'
                  };
                  return typeMap[selectedExpense.expense_type] || selectedExpense.expense_type;
                })()}
              </Descriptions.Item>
              <Descriptions.Item label="费用日期">
                {dayjs(selectedExpense.expense_date).format('YYYY-MM-DD')}
              </Descriptions.Item>
              <Descriptions.Item label="金额">
                ¥{parseFloat(selectedExpense.amount).toFixed(2)}
              </Descriptions.Item>
              <Descriptions.Item label="状态" span={2}>
                <Tag color={selectedExpense.is_abnormal ? 'error' : 'success'}>
                  {selectedExpense.is_abnormal ? '异常' : '正常'}
                </Tag>
                {selectedExpense.is_abnormal && selectedExpense.abnormal_reason && (
                  <div style={{ marginTop: 8 }}>
                    <Typography.Text type="danger">异常原因: {selectedExpense.abnormal_reason}</Typography.Text>
                  </div>
                )}
              </Descriptions.Item>
              <Descriptions.Item label="描述" span={2}>
                {selectedExpense.description || '无'}
              </Descriptions.Item>
              <Descriptions.Item label="创建时间" span={2}>
                {dayjs(selectedExpense.created_at).format('YYYY-MM-DD HH:mm:ss')}
              </Descriptions.Item>
            </Descriptions>

            {selectedExpense.has_receipt && selectedExpense.receipt_url && (
              <div style={{ marginTop: 16 }}>
                <Typography.Title level={5}>费用小票</Typography.Title>
                <div className="receipt-image">
                  <Image
                    src={selectedExpense.receipt_url}
                    alt="费用小票"
                    style={{ maxWidth: '100%' }}
                    fallback="data:image/png;base64,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"
                  />
                </div>
              </div>
            )}
          </div>
        )}
      </Modal>

      {/* 标记异常弹窗 */}
      <Modal
        title="标记异常"
        open={markAbnormalVisible}
        onOk={handleMarkAbnormalSubmit}
        onCancel={() => setMarkAbnormalVisible(false)}
        confirmLoading={loading}
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="abnormal_reason"
            label="异常原因"
            rules={[{ required: true, message: '请输入异常原因' }]}
          >
            <Input.TextArea rows={4} placeholder="请输入异常原因" />
          </Form.Item>
        </Form>
      </Modal>

      {/* 新增费用弹窗 */}
      <Modal
        title="新增费用"
        open={addExpenseVisible}
        onOk={handleAddExpenseSubmit}
        onCancel={() => setAddExpenseVisible(false)}
        confirmLoading={loading}
        width={700}
      >
        <Form form={addExpenseForm} layout="vertical">
          <Form.Item
            name="store_id"
            label="门店"
            rules={[{ required: true, message: '请选择门店' }]}
          >
            <Select placeholder="选择门店">
              {stores.map(store => (
                <Option key={store.id} value={store.id}>{store.name}</Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item
            name="expense_type"
            label="费用类型"
            rules={[{ required: true, message: '请选择费用类型' }]}
          >
            <Select placeholder="选择费用类型">
              <Option value="misc">杂费</Option>
              <Option value="material">物料费</Option>
              <Option value="electricity">电费</Option>
              <Option value="internet">网费</Option>
              <Option value="other">其他</Option>
            </Select>
          </Form.Item>
          <Form.Item
            name="expense_date"
            label="费用日期"
            rules={[{ required: true, message: '请选择费用日期' }]}
          >
            <DatePicker style={{ width: '100%' }} />
          </Form.Item>
          <Form.Item
            name="amount"
            label="金额"
            rules={[
              { required: true, message: '请输入金额' },
              { pattern: /^[0-9]+(.[0-9]{1,2})?$/, message: '请输入有效的金额' }
            ]}
          >
            <Input prefix="¥" suffix="元" placeholder="请输入金额" />
          </Form.Item>
          <Form.Item
            name="description"
            label="描述"
          >
            <Input.TextArea rows={4} placeholder="请输入费用描述" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default FinanceExpenseApproval;
