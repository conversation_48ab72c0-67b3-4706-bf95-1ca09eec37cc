#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import secrets
from typing import List, Optional, Dict, Any, Union
from pydantic import AnyHttpUrl
from pydantic.fields import Field
from pydantic_settings import BaseSettings
from pydantic.functional_validators import field_validator


class Settings(BaseSettings):
    # 基本设置
    PROJECT_NAME: str = "多行业AI SaaS系统"
    API_V1_STR: str = "/api/v1"
    API_BASE_URL: str = os.getenv("API_BASE_URL", "http://localhost:8000")
    FRONTEND_URL: str = os.getenv("FRONTEND_URL", "https://saas.houshanai.com")
    DEBUG: bool = os.getenv("DEBUG", "False").lower() in ("true", "1", "t")

    # 安全设置
    SECRET_KEY: str = os.getenv("SECRET_KEY", secrets.token_urlsafe(32))
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 7  # 7天
    ALGORITHM: str = "HS256"

    # 数据库设置
    POSTGRES_SERVER: str = os.getenv("POSTGRES_SERVER", "localhost")
    POSTGRES_USER: str = os.getenv("POSTGRES_USER", "postgres")
    POSTGRES_PASSWORD: str = os.getenv("POSTGRES_PASSWORD", "postgres")
    POSTGRES_DB: str = os.getenv("POSTGRES_DB", "retail_ai_saas")
    POSTGRES_PORT: str = os.getenv("POSTGRES_PORT", "5432")
    DATABASE_URL: Optional[str] = os.getenv("DATABASE_URL")

    # 数据库连接池设置
    DB_POOL_SIZE: int = 20
    DB_MAX_OVERFLOW: int = 10
    SQL_ECHO: bool = DEBUG

    # CORS设置
    CORS_ORIGINS: Union[str, List[str]] = [
        "http://localhost:3001", 
        "http://frontend:3000",
        "https://saas.houshanai.com",  # 添加生产域名
        "http://**************:3001",  # 添加IP访问
        "http://**************",       # 添加不带端口的IP访问
    ]

    # 文件存储设置
    STORAGE_TYPE: str = os.getenv("STORAGE_TYPE", "local")  # local, s3, oss
    STORAGE_LOCAL_PATH: str = os.getenv("STORAGE_LOCAL_PATH", "./uploads")
    STORAGE_S3_BUCKET: Optional[str] = os.getenv("STORAGE_S3_BUCKET")
    STORAGE_S3_REGION: Optional[str] = os.getenv("STORAGE_S3_REGION")
    STORAGE_S3_ACCESS_KEY: Optional[str] = os.getenv("STORAGE_S3_ACCESS_KEY")
    STORAGE_S3_SECRET_KEY: Optional[str] = os.getenv("STORAGE_S3_SECRET_KEY")

    # 缓存设置
    REDIS_HOST: str = os.getenv("REDIS_HOST", "localhost")
    REDIS_PORT: int = int(os.getenv("REDIS_PORT", "6379"))
    REDIS_DB: int = int(os.getenv("REDIS_DB", "0"))
    REDIS_PASSWORD: Optional[str] = os.getenv("REDIS_PASSWORD")

    # 消息队列设置
    RABBITMQ_HOST: str = os.getenv("RABBITMQ_HOST", "localhost")
    RABBITMQ_PORT: int = int(os.getenv("RABBITMQ_PORT", "5672"))
    RABBITMQ_USER: str = os.getenv("RABBITMQ_USER", "guest")
    RABBITMQ_PASSWORD: str = os.getenv("RABBITMQ_PASSWORD", "guest")

    # 邮件设置
    SMTP_HOST: Optional[str] = os.getenv("SMTP_HOST")
    SMTP_PORT: Optional[int] = int(os.getenv("SMTP_PORT", "587"))
    SMTP_USER: Optional[str] = os.getenv("SMTP_USER")
    SMTP_PASSWORD: Optional[str] = os.getenv("SMTP_PASSWORD")
    EMAILS_FROM_NAME: Optional[str] = os.getenv("EMAILS_FROM_NAME", "多行业AI SaaS系统")
    EMAILS_FROM_EMAIL: Optional[str] = os.getenv("EMAILS_FROM_EMAIL")

    # 行业设置
    SUPPORTED_INDUSTRIES: List[str] = [
        "retail",      # 零售业
        "restaurant",  # 餐饮业
        "hotel",       # 酒店业
        "beauty",      # 美容美发业
        "education",   # 教育培训业
        "healthcare",  # 医疗健康业
    ]

    # 插件设置
    PLUGINS_DIR: str = os.getenv("PLUGINS_DIR", "./plugins")
    PLUGINS_ENABLED: bool = os.getenv("PLUGINS_ENABLED", "True").lower() in ("true", "1", "t")

    # 文件上传目录
    UPLOAD_DIR: str = os.getenv("UPLOAD_DIR", "./uploads")

    # 文件上传最大大小（单位：字节），默认10MB
    MAX_FILE_SIZE: int = int(os.getenv("MAX_FILE_SIZE", 10 * 1024 * 1024))

    # HTTP请求超时设置（单位：秒）
    DEFAULT_HTTP_TIMEOUT: float = float(os.getenv("DEFAULT_HTTP_TIMEOUT", "10.0"))
    MCP_PROXY_CONNECT_TIMEOUT: float = float(os.getenv("MCP_PROXY_CONNECT_TIMEOUT", "5.0"))
    MCP_PROXY_READ_TIMEOUT: float = float(os.getenv("MCP_PROXY_READ_TIMEOUT", "8.0"))
    MCP_PROXY_WRITE_TIMEOUT: float = float(os.getenv("MCP_PROXY_WRITE_TIMEOUT", "8.0"))
    MCP_PROXY_TOTAL_TIMEOUT: float = float(os.getenv("MCP_PROXY_TOTAL_TIMEOUT", "10.0"))

    @field_validator("DATABASE_URL", mode="before")
    def assemble_db_connection(cls, v: Optional[str], info) -> Any:
        values = info.data
        if isinstance(v, str) and v:
            return v

        user = values.get("POSTGRES_USER", "")
        password = values.get("POSTGRES_PASSWORD", "")
        host = values.get("POSTGRES_SERVER", "localhost")
        port = values.get("POSTGRES_PORT", "5432")
        db = values.get("POSTGRES_DB", "")

        # 直接构建字符串形式的数据库 URL
        return f"postgresql+asyncpg://{user}:{password}@{host}:{port}/{db}"

    @field_validator("CORS_ORIGINS", mode="before")
    def assemble_cors_origins(cls, v: Union[str, List[str]]) -> List[str]:
        if isinstance(v, str):
            # 支持JSON字符串
            if v.startswith("[") and v.endswith("]"):
                import json
                try:
                    return json.loads(v)
                except Exception:
                    pass
            # 支持逗号分隔字符串
            return [i.strip() for i in v.split(",") if i.strip()]
        elif isinstance(v, list):
            return v
        return []

    model_config = {
        "case_sensitive": True,
        "env_file": ".env"
    }


# 创建设置实例
settings = Settings()
