#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
配置路由

负责管理SAPI配置
"""

import logging
import uuid
from datetime import datetime
from typing import Dict, Any, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Body, Query, Path
from sqlalchemy import and_
from sqlalchemy.orm import Session
from pydantic import BaseModel

from db.database import get_db
from models.user import User
from core.auth import get_current_user

from ..models import SAPIConfig
from .. import router

logger = logging.getLogger(__name__)

# 获取配置列表
@router.get("/configs", response_model=Dict[str, Any])
async def get_configs(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取配置列表"""
    try:
        # 查询所有配置
        configs = db.query(SAPIConfig).all()
        
        # 构建响应
        result = []
        for config in configs:
            result.append({
                "id": str(config.id),
                "name": config.name,
                "description": config.description,
                "model_name": config.model_name,
                "enabled": config.enabled,
                "created_at": config.created_at.isoformat() if config.created_at else None,
                "updated_at": config.updated_at.isoformat() if config.updated_at else None
            })
        
        return {
            "success": True,
            "message": "获取配置列表成功",
            "data": result
        }
    except Exception as e:
        logger.error(f"获取配置列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取配置列表失败: {str(e)}")

# 获取配置详情
@router.get("/configs/{config_id}", response_model=Dict[str, Any])
async def get_config(
    config_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取配置详情"""
    try:
        # 查询配置
        config = db.query(SAPIConfig).filter(SAPIConfig.id == config_id).first()
        if not config:
            raise HTTPException(status_code=404, detail="配置不存在")
        
        # 构建响应
        result = {
            "id": str(config.id),
            "name": config.name,
            "description": config.description,
            "model_name": config.model_name,
            "api_key": config.api_key[:8] + "..." + config.api_key[-8:] if config.api_key else None,  # 只显示部分API密钥
            "enabled": config.enabled,
            "created_at": config.created_at.isoformat() if config.created_at else None,
            "updated_at": config.updated_at.isoformat() if config.updated_at else None
        }
        
        return {
            "success": True,
            "message": "获取配置详情成功",
            "data": result
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取配置详情失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取配置详情失败: {str(e)}")

# 创建配置
@router.post("/configs", response_model=Dict[str, Any])
async def create_config(
    config_data: dict = Body(...),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """创建配置"""
    try:
        # 创建配置
        new_config = SAPIConfig(
            id=uuid.uuid4(),
            name=config_data.get("name"),
            description=config_data.get("description"),
            model_name=config_data.get("model_name"),
            api_key=config_data.get("api_key"),
            enabled=config_data.get("enabled", True),
            created_by_id=current_user.id,
            created_at=datetime.now(),
            updated_at=datetime.now()
        )
        
        db.add(new_config)
        db.commit()
        db.refresh(new_config)
        
        return {
            "success": True,
            "message": "配置创建成功",
            "data": {
                "id": str(new_config.id),
                "name": new_config.name,
                "model_name": new_config.model_name
            }
        }
    except Exception as e:
        db.rollback()
        logger.error(f"创建配置失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"创建配置失败: {str(e)}")

# 更新配置
@router.put("/configs/{config_id}", response_model=Dict[str, Any])
async def update_config(
    config_id: str,
    config_data: dict = Body(...),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """更新配置"""
    try:
        # 查询配置
        config = db.query(SAPIConfig).filter(SAPIConfig.id == config_id).first()
        if not config:
            raise HTTPException(status_code=404, detail="配置不存在")
        
        # 更新字段
        if "name" in config_data:
            config.name = config_data.get("name")
        if "description" in config_data:
            config.description = config_data.get("description")
        if "model_name" in config_data:
            config.model_name = config_data.get("model_name")
        if "api_key" in config_data:
            config.api_key = config_data.get("api_key")
        if "enabled" in config_data:
            config.enabled = config_data.get("enabled")
        
        config.updated_at = datetime.now()
        
        db.commit()
        db.refresh(config)
        
        return {
            "success": True,
            "message": "配置更新成功",
            "data": {
                "id": str(config.id),
                "name": config.name,
                "model_name": config.model_name
            }
        }
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"更新配置失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"更新配置失败: {str(e)}")

# 删除配置
@router.delete("/configs/{config_id}", response_model=Dict[str, Any])
async def delete_config(
    config_id: str,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """删除配置"""
    try:
        # 查询配置
        config = db.query(SAPIConfig).filter(SAPIConfig.id == config_id).first()
        if not config:
            raise HTTPException(status_code=404, detail="配置不存在")
        
        # 删除配置
        db.delete(config)
        db.commit()
        
        return {
            "success": True,
            "message": "配置删除成功"
        }
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"删除配置失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除配置失败: {str(e)}")
