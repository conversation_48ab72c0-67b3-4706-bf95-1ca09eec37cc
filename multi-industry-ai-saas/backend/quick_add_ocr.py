import asyncio
import asyncpg
from core.config import settings

async def main():
    conn = await asyncpg.connect(settings.DATABASE_URL.replace('postgresql+asyncpg://', 'postgresql://'))
    try:
        # 获取阿里巴巴百炼提供商ID
        provider_id = await conn.fetchval("SELECT id FROM ai_providers WHERE name = 'alibaba_bailian'")
        if not provider_id:
            print('阿里巴巴百炼提供商不存在')
            return
        
        # 添加text-embedding-v4嵌入模型
        exists = await conn.fetchval("SELECT COUNT(*) FROM ai_models WHERE provider_id = $1 AND name = 'text-embedding-v4'", provider_id)
        if exists == 0:
            result = await conn.execute("""
                INSERT INTO ai_models (
                    id, provider_id, name, display_name, description, model_type,
                    capabilities, context_window, token_limit,
                    input_price_per_1k_tokens, output_price_per_1k_tokens,
                    image_price_per_1k_tokens, average_response_time, rate_limit_per_minute,
                    status, is_builtin, created_at, updated_at
                ) VALUES (
                    gen_random_uuid(), $1, 'text-embedding-v4', '通义千问文本嵌入V4',
                    '阿里云百炼最新文本嵌入模型，支持高质量文本向量化，适用于语义搜索、文档检索和知识库构建',
                    'embedding',
                    '{"supports_embedding": true, "embedding_dimension": 1024, "max_input_length": 8192}',
                    8192, 8192, 0.0001, 0.0000, 0.0000, 0.5, 300,
                    'active', true, NOW(), NOW()
                )
            """, provider_id)
            print(f'成功添加嵌入模型: text-embedding-v4')
            print('嵌入维度: 1024, 上下文窗口: 8192')
        else:
            print('text-embedding-v4模型已存在')
        
        # 添加qwen-omni-turbo-realtime全模态模型
        exists = await conn.fetchval("SELECT COUNT(*) FROM ai_models WHERE provider_id = $1 AND name = 'qwen-omni-turbo-realtime'", provider_id)
        if exists == 0:
            result = await conn.execute("""
                INSERT INTO ai_models (
                    id, provider_id, name, display_name, description, model_type,
                    capabilities, context_window, token_limit,
                    input_price_per_1k_tokens, output_price_per_1k_tokens,
                    image_price_per_1k_tokens, average_response_time, rate_limit_per_minute,
                    status, is_builtin, created_at, updated_at
                ) VALUES (
                    gen_random_uuid(), $1, 'qwen-omni-turbo-realtime', '通义千问全模态实时版',
                    '阿里云百炼全模态实时模型，支持文本、图像、音频的多模态实时交互，适用于实时对话和多媒体处理',
                    'multimodal',
                    '{"supports_text": true, "supports_vision": true, "supports_audio": true, "supports_realtime": true, "supports_streaming": true}',
                    32768, 30000, 0.005, 0.015, 0.005, 1.0, 100,
                    'active', true, NOW(), NOW()
                )
            """, provider_id)
            print(f'成功添加全模态模型: qwen-omni-turbo-realtime')
            print('支持: 文本+图像+音频实时交互, 上下文窗口: 32768')
        else:
            print('qwen-omni-turbo-realtime模型已存在')
            
        # 检查OCR模型是否已存在
        exists = await conn.fetchval("SELECT COUNT(*) FROM ai_models WHERE provider_id = $1 AND name = 'qwen-vl-ocr-latest'", provider_id)
        if exists == 0:
            # 添加OCR模型
            result = await conn.execute("""
                INSERT INTO ai_models (
                    id, provider_id, name, display_name, description, model_type,
                    capabilities, context_window, token_limit,
                    input_price_per_1k_tokens, output_price_per_1k_tokens,
                    image_price_per_1k_tokens, average_response_time, rate_limit_per_minute,
                    status, is_builtin, created_at, updated_at
                ) VALUES (
                    gen_random_uuid(), $1, 'qwen-vl-ocr-latest', '通义千问VL OCR最新版',
                    '阿里云百炼专业OCR视觉模型，专门优化用于文档和表格识别，支持高精度文字提取和表格数据解析',
                    'vision',
                    '{"supports_vision": true, "supports_ocr": true, "supports_table_extraction": true}',
                    38192, 30000, 0.0008, 0.002, 0.0008, 2.5, 60,
                    'active', true, NOW(), NOW()
                )
            """, provider_id)
            print(f'成功添加OCR模型: qwen-vl-ocr-latest')
            print('上下文窗口: 38192, 支持: OCR+表格提取')
        else:
            print('qwen-vl-ocr-latest模型已存在')
            
        print('\n所有模型检查和添加完成！')
        
    except Exception as e:
        print(f'执行失败: {e}')
        import traceback
        traceback.print_exc()
    finally:
        await conn.close()

if __name__ == "__main__":
    asyncio.run(main()) 