#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
产品服务 - 智能商品匹配和管理
"""

import logging
import uuid
import re
from typing import Optional, List, Dict, Any, Tuple
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, or_, and_, func
from difflib import SequenceMatcher
from datetime import datetime

from models.product import Product, ProductBrand, ProductCategory
from schemas.product import ProductCreate, ProductResponse

logger = logging.getLogger(__name__)

class ProductService:
    """产品服务类"""
    
    # 商品名称相似度阈值
    SIMILARITY_THRESHOLD = 0.8
    
    # 规格相似度阈值
    SPEC_SIMILARITY_THRESHOLD = 0.7
    
    @staticmethod
    async def find_or_create_product(
        db: AsyncSession,
        product_name: str,
        product_specification: Optional[str] = None,
        product_unit: Optional[str] = None,
        product_code: Optional[str] = None,
        unit_price: Optional[float] = None,
        project_id: uuid.UUID = None,
        tenant_id: Optional[uuid.UUID] = None,
        user_id: uuid.UUID = None,
        auto_create: bool = True,
        suggested_category: Optional[str] = None,
        suggested_brand: Optional[str] = None
    ) -> Tuple[Optional[Product], bool, Dict[str, Any]]:
        """
        智能查找或创建商品 - 增强多规格支持
        
        Args:
            db: 数据库会话
            product_name: 商品名称
            product_specification: 商品规格
            product_unit: 商品单位
            product_code: 商品编码
            unit_price: 单价
            project_id: 项目ID
            tenant_id: 租户ID
            user_id: 用户ID
            auto_create: 是否自动创建新商品
            suggested_category: 建议分类
            suggested_brand: 建议品牌
            
        Returns:
            Tuple[Product, bool, Dict]: (商品对象, 是否新创建, 匹配信息)
        """
        try:
            # 标准化商品信息
            normalized_product = ProductService._normalize_product_info(product_name, product_specification)
            
            logger.info(f"智能查找商品: {normalized_product}")
            
            # 1. 精确匹配（名称 + 规格）
            exact_product = await ProductService._find_exact_product(
                db, normalized_product["name"], normalized_product["specification"], project_id
            )
            
            if exact_product:
                logger.info(f"找到精确匹配商品: {exact_product.name} - {exact_product.specification}")
                return exact_product, False, {
                    "match_type": "exact",
                    "match_score": 1.0,
                    "message": "精确匹配现有商品"
                }
            
            # 2. 查找相似商品
            similar_products = await ProductService._find_similar_products(
                db, normalized_product["name"], normalized_product["specification"], project_id
            )
            
            # 3. 检查是否存在同名不同规格的商品
            if similar_products:
                best_match = similar_products[0]
                
                # 如果名称高度相似且规格不同，可能是同一商品的不同规格
                if (best_match["name_similarity"] >= ProductService.SIMILARITY_THRESHOLD and
                    best_match["spec_similarity"] < ProductService.SPEC_SIMILARITY_THRESHOLD):
                    
                    logger.info(f"发现可能的多规格商品: {best_match['product'].name}")
                    
                    # 如果允许自动创建，则创建新规格
                    if auto_create:
                        new_product = await ProductService._create_variant_product(
                            db, 
                            base_product=best_match["product"],
                            new_specification=normalized_product["specification"],
                            new_unit=product_unit,
                            new_price=unit_price,
                            user_id=user_id
                        )
                        
                        return new_product, True, {
                            "match_type": "variant_created",
                            "match_score": best_match["similarity"],
                            "base_product": best_match["product"].name,
                            "message": f"基于 {best_match['product'].name} 创建新规格商品"
                        }
                
                # 如果整体相似度较高，可能是重复商品
                elif best_match["similarity"] >= ProductService.SIMILARITY_THRESHOLD:
                    logger.info(f"发现高度相似商品: {best_match['product'].name}")
                    return best_match["product"], False, {
                        "match_type": "similar",
                        "match_score": best_match["similarity"],
                        "message": "匹配到相似商品"
                    }
            
            # 4. 如果没有找到匹配商品且允许自动创建
            if auto_create:
                new_product = await ProductService._create_new_product(
                    db=db,
                    product_name=normalized_product["name"],
                    product_specification=normalized_product["specification"],
                    product_unit=product_unit,
                    product_code=product_code,
                    unit_price=unit_price,
                    project_id=project_id,
                    tenant_id=tenant_id,
                    user_id=user_id,
                    suggested_category=suggested_category,
                    suggested_brand=suggested_brand
                )
                
                return new_product, True, {
                    "match_type": "created",
                    "match_score": 0.0,
                    "message": "创建新商品"
                }
            
            # 5. 没有找到且不允许创建
            return None, False, {
                "match_type": "not_found",
                "match_score": 0.0,
                "message": "未找到匹配商品"
            }
            
        except Exception as e:
            logger.error(f"智能查找或创建商品失败: {e}")
            return None, False, {
                "match_type": "error",
                "match_score": 0.0,
                "message": f"处理失败: {str(e)}"
            }
    
    @staticmethod
    async def _find_by_code(
        db: AsyncSession,
        product_code: str,
        project_id: uuid.UUID
    ) -> Optional[Product]:
        """通过商品编码查找商品"""
        stmt = select(Product).where(
            and_(
                Product.project_id == project_id,
                Product.sku == product_code,
                Product.is_active == True
            )
        )
        result = await db.execute(stmt)
        return result.scalars().first()
    
    @staticmethod
    async def _find_by_name_and_spec(
        db: AsyncSession,
        product_name: str,
        product_specification: Optional[str],
        project_id: uuid.UUID
    ) -> Optional[Product]:
        """通过商品名称和规格精确查找商品"""
        conditions = [
            Product.project_id == project_id,
            Product.name == product_name,
            Product.is_active == True
        ]
        
        if product_specification:
            conditions.append(Product.specification == product_specification)
        else:
            conditions.append(
                or_(Product.specification.is_(None), Product.specification == "")
            )
        
        stmt = select(Product).where(and_(*conditions))
        result = await db.execute(stmt)
        return result.scalars().first()
    
    @staticmethod
    async def _find_similar_products(
        db: AsyncSession,
        product_name: str,
        product_specification: Optional[str],
        project_id: uuid.UUID,
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """查找相似商品 - 支持多规格分析"""
        try:
            stmt = select(Product).where(
                and_(
                    Product.project_id == project_id,
                    Product.is_active == True
                )
            )
            
            result = await db.execute(stmt)
            products = result.scalars().all()
            
            similar_products = []
            product_name_lower = product_name.lower()
            product_spec_lower = (product_specification or "").lower()
            
            for product in products:
                product_db_name_lower = product.name.lower()
                product_db_spec_lower = (product.specification or "").lower()
                
                # 计算名称相似度
                name_similarity = SequenceMatcher(
                    None, 
                    product_name_lower, 
                    product_db_name_lower
                ).ratio()
                
                # 计算规格相似度
                if product_specification and product.specification:
                    # 两者都有规格
                    spec_similarity = SequenceMatcher(
                        None,
                        product_spec_lower,
                        product_db_spec_lower
                    ).ratio()
                elif not product_specification and not product.specification:
                    # 两者都没有规格
                    spec_similarity = 1.0
                else:
                    # 一个有规格，一个没有
                    spec_similarity = 0.3
                
                # 综合相似度（名称权重更高）
                overall_similarity = name_similarity * 0.8 + spec_similarity * 0.2
                
                # 如果名称相似度很高，即使规格不同也认为可能是多规格商品
                if name_similarity >= ProductService.SIMILARITY_THRESHOLD:
                    overall_similarity = max(overall_similarity, name_similarity * 0.9)
                
                if overall_similarity > 0.3:  # 降低阈值，包含更多可能的相似商品
                    similar_products.append({
                        "product": product,
                        "similarity": overall_similarity,
                        "name_similarity": name_similarity,
                        "spec_similarity": spec_similarity,
                        "match_type": ProductService._determine_match_type(name_similarity, spec_similarity)
                    })
            
            # 按相似度排序
            similar_products.sort(key=lambda x: x["similarity"], reverse=True)
            
            return similar_products[:limit]
            
        except Exception as e:
            logger.error(f"查找相似商品失败: {e}")
            return []
    
    @staticmethod
    def _determine_match_type(name_similarity: float, spec_similarity: float) -> str:
        """确定匹配类型"""
        if name_similarity >= ProductService.SIMILARITY_THRESHOLD and spec_similarity >= ProductService.SPEC_SIMILARITY_THRESHOLD:
            return "high_similarity"
        elif name_similarity >= ProductService.SIMILARITY_THRESHOLD and spec_similarity < ProductService.SPEC_SIMILARITY_THRESHOLD:
            return "multi_variant_candidate"
        elif name_similarity >= 0.6:
            return "moderate_similarity"
        else:
            return "low_similarity"
    
    @staticmethod
    async def _create_new_product(
        db: AsyncSession,
        product_name: str,
        product_specification: Optional[str],
        product_unit: Optional[str],
        product_code: Optional[str],
        unit_price: Optional[float],
        project_id: uuid.UUID,
        tenant_id: Optional[uuid.UUID],
        user_id: uuid.UUID,
        suggested_category: Optional[str] = None,
        suggested_brand: Optional[str] = None
    ) -> Product:
        """创建新商品"""
        try:
            # 智能分类
            category_id, brand_id = await ProductService._smart_categorize(
                db, product_name, project_id, suggested_category, suggested_brand
            )
            
            # 生成SKU
            sku = await ProductService._generate_sku(db, product_name, project_id)
            
            # 创建商品
            product = Product(
                id=uuid.uuid4(),
                tenant_id=tenant_id,
                project_id=project_id,
                name=product_name,
                sku=sku,
                specification=product_specification,
                unit=product_unit,
                cost_price=unit_price,
                retail_price=(unit_price * 1.2 if unit_price else None),
                category_id=category_id,
                brand_id=brand_id,
                description=f"AI智能识别创建 - {product_name}",
                is_active=True,
                is_variant=False,  # 修复：新创建的商品不应该自动标记为多规格
                created_by=user_id,
                updated_by=user_id
            )
            
            db.add(product)
            await db.flush()
            await db.refresh(product)
            
            logger.info(f"创建新商品: {product.display_name} (SKU: {product.sku})")
            return product
            
        except Exception as e:
            logger.error(f"创建新商品失败: {e}")
            await db.rollback()
            raise e
    
    @staticmethod
    async def _generate_sku(
        db: AsyncSession,
        product_name: str,
        project_id: uuid.UUID
    ) -> str:
        """生成商品SKU"""
        # 提取商品名称的首字母或关键字符
        name_chars = re.sub(r'[^\w\u4e00-\u9fff]', '', product_name)
        
        # 生成基础SKU
        if len(name_chars) >= 2:
            if '\u4e00' <= name_chars[0] <= '\u9fff':  # 中文字符
                # 中文商品名，取前2个字符的拼音首字母（简化处理）
                base_sku = f"P{hash(name_chars[:2]) % 1000:03d}"
            else:
                # 英文商品名，取前2个字符
                base_sku = f"P{name_chars[:2].upper()}"
        else:
            base_sku = f"P{hash(product_name) % 1000:03d}"
        
        # 检查SKU是否已存在，如果存在则添加序号
        counter = 1
        sku = base_sku
        
        while True:
            stmt = select(Product).where(
                and_(
                    Product.project_id == project_id,
                    Product.sku == sku
                )
            )
            result = await db.execute(stmt)
            existing = result.scalars().first()
            
            if not existing:
                break
            
            counter += 1
            sku = f"{base_sku}{counter:02d}"
            
            if counter > 99:  # 防止无限循环
                sku = f"P{uuid.uuid4().hex[:6].upper()}"
                break
        
        return sku
    
    @staticmethod
    async def _smart_categorize(
        db: AsyncSession,
        product_name: str,
        project_id: uuid.UUID,
        suggested_category: Optional[str] = None,
        suggested_brand: Optional[str] = None
    ) -> Tuple[Optional[uuid.UUID], Optional[uuid.UUID]]:
        """智能分类和品牌识别 - 优先使用AI提供的信息"""
        category_id = None
        brand_id = None
        
        try:
            # 获取现有分类
            categories_stmt = select(ProductCategory).where(
                ProductCategory.project_id == project_id
            )
            categories_result = await db.execute(categories_stmt)
            categories = categories_result.scalars().all()
            
            # 获取现有品牌
            brands_stmt = select(ProductBrand).where(
                ProductBrand.project_id == project_id
            )
            brands_result = await db.execute(brands_stmt)
            brands = brands_result.scalars().all()
            
            product_name_lower = product_name.lower()
            
            # 1. 优先使用AI或表格提供的分类和品牌
            if suggested_category:
                logger.info(f"使用AI/表格提供的分类: {suggested_category}")
                # 查找完全匹配的分类
                for category in categories:
                    if category.name.lower() == suggested_category.lower():
                        category_id = category.id
                        logger.info(f"找到匹配的现有分类: {category.name}")
                        break
                
                # 如果没有找到完全匹配，查找相似的分类
                if not category_id:
                    from difflib import SequenceMatcher
                    best_match = None
                    best_similarity = 0.0
                    
                    for category in categories:
                        similarity = SequenceMatcher(None, suggested_category.lower(), category.name.lower()).ratio()
                        if similarity > best_similarity and similarity >= 0.8:  # 相似度阈值
                            best_similarity = similarity
                            best_match = category
                    
                    if best_match:
                        category_id = best_match.id
                        logger.info(f"找到相似的现有分类: {best_match.name}, 相似度: {best_similarity:.2f}")
                    else:
                        # 如果没有找到相似的，创建新分类
                        new_category = ProductCategory(
                            id=uuid.uuid4(),
                            project_id=project_id,
                            name=suggested_category,
                            description=f"AI智能识别自动创建 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
                        )
                        db.add(new_category)
                        await db.flush()
                        category_id = new_category.id
                        logger.info(f"创建新分类: {suggested_category}")
            
            if suggested_brand:
                logger.info(f"使用AI/表格提供的品牌: {suggested_brand}")
                # 查找完全匹配的品牌
                for brand in brands:
                    if brand.name.lower() == suggested_brand.lower():
                        brand_id = brand.id
                        logger.info(f"找到匹配的现有品牌: {brand.name}")
                        break
                
                # 如果没有找到完全匹配，查找相似的品牌
                if not brand_id:
                    from difflib import SequenceMatcher
                    best_match = None
                    best_similarity = 0.0
                    
                    for brand in brands:
                        similarity = SequenceMatcher(None, suggested_brand.lower(), brand.name.lower()).ratio()
                        if similarity > best_similarity and similarity >= 0.8:  # 相似度阈值
                            best_similarity = similarity
                            best_match = brand
                    
                    if best_match:
                        brand_id = best_match.id
                        logger.info(f"找到相似的现有品牌: {best_match.name}, 相似度: {best_similarity:.2f}")
                    else:
                        # 如果没有找到相似的，创建新品牌
                        new_brand = ProductBrand(
                            id=uuid.uuid4(),
                            project_id=project_id,
                            name=suggested_brand,
                            description=f"AI智能识别自动创建 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
                        )
                        db.add(new_brand)
                        await db.flush()
                        brand_id = new_brand.id
                        logger.info(f"创建新品牌: {suggested_brand}")
            
            # 2. 如果AI没有提供分类信息，则进行传统的关键词匹配
            if not category_id:
                # 关键词匹配分类
                category_keywords = {
                    "水果": ["苹果", "橙子", "香蕉", "葡萄", "梨", "桃", "李", "杏", "樱桃", "草莓", "蓝莓", "猕猴桃", "芒果", "菠萝", "西瓜", "哈密瓜"],
                    "蔬菜": ["白菜", "萝卜", "胡萝卜", "土豆", "番茄", "黄瓜", "茄子", "辣椒", "洋葱", "大蒜", "生菜", "菠菜", "芹菜", "韭菜", "豆角"],
                    "肉类": ["猪肉", "牛肉", "羊肉", "鸡肉", "鸭肉", "鱼肉", "虾", "蟹", "贝类"],
                    "粮油": ["大米", "面粉", "食用油", "调料", "盐", "糖", "醋", "酱油"],
                    "日用品": ["洗衣粉", "洗洁精", "牙膏", "香皂", "纸巾", "卫生纸"],
                    "饮料": ["水", "果汁", "茶", "咖啡", "汽水", "啤酒"]
                }
                
                for category_name, keywords in category_keywords.items():
                    if any(keyword in product_name_lower for keyword in keywords):
                        # 查找或创建分类
                        for category in categories:
                            if category.name == category_name:
                                category_id = category.id
                                break
                        
                        if not category_id:
                            new_category = ProductCategory(
                                id=uuid.uuid4(),
                                project_id=project_id,
                                name=category_name,
                                description=f"AI智能识别自动创建 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
                            )
                            db.add(new_category)
                            await db.flush()
                            category_id = new_category.id
                            logger.info(f"智能创建分类: {category_name}")
                        break
                
                # 如果还是没有匹配，使用现有分类进行模糊匹配
                if not category_id and categories:
                    best_match = None
                    best_similarity = 0.0
                    
                    for category in categories:
                        similarity = SequenceMatcher(None, product_name_lower, category.name.lower()).ratio()
                        if similarity > best_similarity and similarity > 0.3:
                            best_similarity = similarity
                            best_match = category
                    
                    if best_match:
                        category_id = best_match.id
                        logger.info(f"模糊匹配分类: {best_match.name}, 相似度: {best_similarity:.2f}")
            
            # 3. 品牌识别
            if not brand_id:
                # 从商品名称中提取可能的品牌名
                # 常见的品牌标识词
                brand_indicators = ["牌", "品牌", "®", "™", "有机", "绿色", "天然"]
                
                # 尝试从现有品牌中匹配
                for brand in brands:
                    if brand.name.lower() in product_name_lower:
                        brand_id = brand.id
                        logger.info(f"从商品名称中识别品牌: {brand.name}")
                        break
                
                # 如果没有匹配到现有品牌，尝试提取品牌名
                if not brand_id:
                    # 简单的品牌名提取逻辑（可以进一步优化）
                    words = product_name.split()
                    if len(words) > 1:
                        # 取第一个词作为可能的品牌名
                        potential_brand = words[0]
                        if len(potential_brand) >= 2 and len(potential_brand) <= 10:
                            new_brand = ProductBrand(
                                id=uuid.uuid4(),
                                project_id=project_id,
                                name=potential_brand,
                                description=f"AI智能识别自动创建 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
                            )
                            db.add(new_brand)
                            await db.flush()
                            brand_id = new_brand.id
                            logger.info(f"智能提取品牌: {potential_brand}")
            
            # 4. 如果还是没有分类，创建默认分类
            if not category_id and not categories:
                default_category = ProductCategory(
                    id=uuid.uuid4(),
                    project_id=project_id,
                    name="默认分类",
                    description="AI智能识别自动创建的默认分类"
                )
                db.add(default_category)
                await db.flush()
                category_id = default_category.id
                logger.info("创建默认分类")
            
        except Exception as e:
            logger.error(f"智能分类失败: {e}")
        
        return category_id, brand_id
    
    @staticmethod
    async def get_existing_categories_and_brands(
        db: AsyncSession,
        project_id: uuid.UUID
    ) -> Dict[str, List[str]]:
        """获取现有的分类和品牌列表，用于AI提示"""
        try:
            # 获取分类
            categories_stmt = select(ProductCategory).where(
                ProductCategory.project_id == project_id
            )
            categories_result = await db.execute(categories_stmt)
            categories = categories_result.scalars().all()
            
            # 获取品牌
            brands_stmt = select(ProductBrand).where(
                ProductBrand.project_id == project_id
            )
            brands_result = await db.execute(brands_stmt)
            brands = brands_result.scalars().all()
            
            return {
                "categories": [cat.name for cat in categories],
                "brands": [brand.name for brand in brands]
            }
            
        except Exception as e:
            logger.error(f"获取分类品牌列表失败: {e}")
            return {"categories": [], "brands": []}
    
    @staticmethod
    async def batch_find_or_create_products(
        db: AsyncSession,
        product_items: List[Dict[str, Any]],
        project_id: uuid.UUID,
        tenant_id: Optional[uuid.UUID],
        user_id: uuid.UUID,
        auto_create: bool = True
    ) -> List[Dict[str, Any]]:
        """
        批量查找或创建商品
        
        Args:
            db: 数据库会话
            product_items: 商品信息列表
            project_id: 项目ID
            tenant_id: 租户ID
            user_id: 用户ID
            auto_create: 是否自动创建新商品
            
        Returns:
            List[Dict]: 处理结果列表
        """
        results = []
        
        for item in product_items:
            try:
                # 提取AI识别的分类和品牌信息
                suggested_category = (
                    item.get("suggested_category") or 
                    item.get("product_category") or 
                    item.get("category")
                )
                suggested_brand = (
                    item.get("suggested_brand") or 
                    item.get("product_brand") or 
                    item.get("brand")
                )
                
                logger.info(f"处理商品: {item.get('product_name')}, 分类: {suggested_category}, 品牌: {suggested_brand}")
                
                product, is_new, match_info = await ProductService.find_or_create_product(
                    db=db,
                    product_name=item.get("product_name", ""),
                    product_specification=item.get("product_specification"),
                    product_unit=item.get("product_unit"),
                    product_code=item.get("product_code"),
                    unit_price=item.get("unit_price"),
                    project_id=project_id,
                    tenant_id=tenant_id,
                    user_id=user_id,
                    auto_create=auto_create,
                    suggested_category=suggested_category,
                    suggested_brand=suggested_brand
                )
                
                result = {
                    "original_item": item,
                    "product": product,
                    "is_new": is_new,
                    "match_info": match_info,
                    "success": product is not None
                }
                
                # 如果找到或创建了商品，更新商品信息到原始数据
                if product:
                    item["product_id"] = str(product.id)
                    item["matched_product_name"] = product.name
                    item["matched_product_sku"] = product.sku
                    item["matched_product_unit"] = product.unit
                    item["matched_product_specification"] = product.specification
                    
                    # 添加分类和品牌信息
                    if product.category_id:
                        category_stmt = select(ProductCategory).where(ProductCategory.id == product.category_id)
                        category_result = await db.execute(category_stmt)
                        category = category_result.scalars().first()
                        if category:
                            item["matched_product_category"] = category.name
                    
                    if product.brand_id:
                        brand_stmt = select(ProductBrand).where(ProductBrand.id == product.brand_id)
                        brand_result = await db.execute(brand_stmt)
                        brand = brand_result.scalars().first()
                        if brand:
                            item["matched_product_brand"] = brand.name
                
                results.append(result)
                
            except Exception as e:
                logger.error(f"处理商品失败: {item.get('product_name')}, 错误: {e}")
                results.append({
                    "original_item": item,
                    "product": None,
                    "is_new": False,
                    "match_info": {"error": str(e)},
                    "success": False
                })
        
        return results
    
    @staticmethod
    async def get_merge_suggestions(
        db: AsyncSession,
        project_id: uuid.UUID,
        similarity_threshold: float = 0.8
    ) -> List[Dict[str, Any]]:
        """
        获取商品合并建议
        
        Args:
            db: 数据库会话
            project_id: 项目ID
            similarity_threshold: 相似度阈值
            
        Returns:
            List[Dict]: 合并建议列表
        """
        try:
            # 获取所有活跃商品
            stmt = select(Product).where(
                and_(
                    Product.project_id == project_id,
                    Product.is_active == True
                )
            ).order_by(Product.name)
            
            result = await db.execute(stmt)
            products = result.scalars().all()
            
            merge_suggestions = []
            processed_ids = set()
            
            for i, product1 in enumerate(products):
                if product1.id in processed_ids:
                    continue
                
                similar_group = [product1]
                
                for j, product2 in enumerate(products[i+1:], i+1):
                    if product2.id in processed_ids:
                        continue
                    
                    # 计算相似度
                    name_similarity = SequenceMatcher(
                        None, 
                        product1.name.lower(), 
                        product2.name.lower()
                    ).ratio()
                    
                    spec_similarity = 1.0
                    if product1.specification and product2.specification:
                        spec_similarity = SequenceMatcher(
                            None,
                            product1.specification.lower(),
                            product2.specification.lower()
                        ).ratio()
                    elif product1.specification or product2.specification:
                        spec_similarity = 0.5
                    
                    overall_similarity = name_similarity * 0.7 + spec_similarity * 0.3
                    
                    if overall_similarity >= similarity_threshold:
                        similar_group.append(product2)
                        processed_ids.add(product2.id)
                
                if len(similar_group) > 1:
                    # 标记主商品ID
                    processed_ids.add(product1.id)
                    
                    merge_suggestions.append({
                        "group_id": str(uuid.uuid4()),
                        "primary_product": {
                            "id": str(product1.id),
                            "name": product1.name,
                            "sku": product1.sku,
                            "specification": product1.specification,
                            "unit": product1.unit
                        },
                        "similar_products": [
                            {
                                "id": str(p.id),
                                "name": p.name,
                                "sku": p.sku,
                                "specification": p.specification,
                                "unit": p.unit,
                                "similarity": SequenceMatcher(None, product1.name.lower(), p.name.lower()).ratio()
                            }
                            for p in similar_group[1:]
                        ],
                        "suggestion_type": "merge",
                        "confidence": max(
                            SequenceMatcher(None, product1.name.lower(), p.name.lower()).ratio()
                            for p in similar_group[1:]
                        )
                    })
            
            return merge_suggestions
            
        except Exception as e:
            logger.error(f"获取合并建议失败: {e}")
            return []
    
    @staticmethod
    async def merge_products(
        db: AsyncSession,
        primary_product_id: uuid.UUID,
        merge_product_ids: List[uuid.UUID],
        user_id: uuid.UUID
    ) -> Dict[str, Any]:
        """
        合并商品
        
        Args:
            db: 数据库会话
            primary_product_id: 主商品ID
            merge_product_ids: 要合并的商品ID列表
            user_id: 用户ID
            
        Returns:
            Dict: 合并结果
        """
        try:
            # 获取主商品
            primary_product = await db.get(Product, primary_product_id)
            if not primary_product:
                raise ValueError("主商品不存在")
            
            # 获取要合并的商品
            merge_products = []
            for product_id in merge_product_ids:
                product = await db.get(Product, product_id)
                if product:
                    merge_products.append(product)
            
            if not merge_products:
                raise ValueError("没有找到要合并的商品")
            
            # TODO: 实现商品合并逻辑
            # 1. 更新相关的采购订单项、库存记录等
            # 2. 将被合并商品设为非活跃状态
            # 3. 记录合并历史
            
            # 暂时只是将被合并的商品设为非活跃状态
            for product in merge_products:
                product.is_active = False
                product.updated_by = user_id
                product.description = f"{product.description or ''} [已合并到: {primary_product.name}]"
            
            await db.commit()
            
            return {
                "success": True,
                "message": f"成功合并 {len(merge_products)} 个商品到 {primary_product.name}",
                "primary_product_id": str(primary_product_id),
                "merged_product_ids": [str(pid) for pid in merge_product_ids]
            }
            
        except Exception as e:
            await db.rollback()
            logger.error(f"合并商品失败: {e}")
            return {
                "success": False,
                "message": f"合并商品失败: {str(e)}"
            }
    
    @staticmethod
    def _normalize_product_info(product_name: str, specification: str = "") -> Dict[str, str]:
        """标准化商品信息，便于匹配和去重"""
        import re
        
        # 基础清理
        name = str(product_name).strip()
        spec = str(specification).strip() if specification else ""
        
        # 提取规格信息（如果规格为空，尝试从名称中提取）
        if not spec and name:
            # 常见规格模式匹配
            spec_patterns = [
                r'\(([^)]+)\)',  # 括号内容
                r'（([^）]+)）',  # 中文括号
                r'(\d+\.?\d*[a-zA-Z\u4e00-\u9fa5]+)',  # 数字+单位
            ]
            
            for pattern in spec_patterns:
                matches = re.findall(pattern, name)
                if matches:
                    spec = matches[-1]  # 取最后一个匹配作为规格
                    # 从名称中移除规格部分
                    name = re.sub(pattern, '', name).strip()
                    break
        
        return {
            "name": name,
            "specification": spec,
            "unique_key": f"{name}|{spec}" if spec else name
        }
    
    @staticmethod
    async def _find_exact_product(
        db: AsyncSession, 
        product_name: str, 
        specification: str,
        project_id: uuid.UUID
    ) -> Optional[Product]:
        """精确查找商品（名称+规格）"""
        stmt = select(Product).where(
            and_(
                Product.project_id == project_id,
                Product.name == product_name,
                Product.specification == specification,
                Product.is_active == True
            )
        )
        
        result = await db.execute(stmt)
        return result.scalars().first()
    
    @staticmethod
    async def _create_variant_product(
        db: AsyncSession,
        base_product: Product,
        new_specification: str,
        new_unit: Optional[str],
        new_price: Optional[float],
        user_id: uuid.UUID
    ) -> Product:
        """基于现有商品创建变体（多规格商品）- 增强版"""
        try:
            # 确保基础商品有product_group_id
            if not base_product.product_group_id:
                base_product.product_group_id = base_product.id
                await db.commit()
                await db.refresh(base_product)
            
            # 生成新的SKU
            new_sku = await ProductService._generate_variant_sku(
                db, base_product.sku, new_specification, base_product.project_id
            )
            
            # 创建变体商品
            variant_product = Product(
                id=uuid.uuid4(),
                tenant_id=base_product.tenant_id,
                project_id=base_product.project_id,
                product_group_id=base_product.product_group_id,  # 使用相同的分组ID
                name=base_product.name,  # 保持相同的名称
                sku=new_sku,
                specification=new_specification,  # 新的规格
                unit=new_unit or base_product.unit,
                cost_price=new_price or base_product.cost_price,
                retail_price=(new_price * 1.2 if new_price else base_product.retail_price),
                category_id=base_product.category_id,  # 继承分类
                brand_id=base_product.brand_id,  # 继承品牌
                description=f"基于 {base_product.name} 创建的规格变体 - {new_specification}",
                is_active=True,
                is_variant=True,  # 标记为变体商品
                created_by=user_id,
                updated_by=user_id
            )
            
            db.add(variant_product)
            await db.flush()
            await db.refresh(variant_product)
            
            # 如果基础商品之前不是变体，现在标记为变体
            if not base_product.is_variant:
                base_product.is_variant = True
            
            # 提交事务
            await db.commit()
            await db.refresh(variant_product)
            
            logger.info(f"创建商品变体: {variant_product.display_name} (SKU: {variant_product.sku})")
            return variant_product
            
        except Exception as e:
            logger.error(f"创建变体商品失败: {e}")
            await db.rollback()
            raise e
    
    @staticmethod
    async def _generate_variant_sku(
        db: AsyncSession,
        base_sku: str,
        specification: str,
        project_id: uuid.UUID
    ) -> str:
        """为变体商品生成SKU"""
        import re
        
        # 清理规格信息，只保留数字和字母
        clean_spec = re.sub(r'[^\w]', '', specification)[:10]
        
        # 尝试不同的SKU格式
        for i in range(1, 100):
            if i == 1:
                candidate_sku = f"{base_sku}-{clean_spec}"
            else:
                candidate_sku = f"{base_sku}-{clean_spec}-{i:02d}"
            
            # 检查SKU是否已存在
            stmt = select(Product).where(
                and_(
                    Product.project_id == project_id,
                    Product.sku == candidate_sku
                )
            )
            
            result = await db.execute(stmt)
            existing = result.scalars().first()
            
            if not existing:
                return candidate_sku
        
        # 如果前面的都失败，使用时间戳
        import time
        timestamp = str(int(time.time()))[-6:]
        return f"{base_sku}-{clean_spec}-{timestamp}" 