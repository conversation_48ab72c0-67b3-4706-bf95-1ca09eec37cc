import api from '../httpClient';

// 获取当前项目ID
const getProjectId = () => {
  const projectId = localStorage.getItem('project_id');
  if (projectId) {
    return projectId;
  }

  const userData = JSON.parse(localStorage.getItem('user_data') || '{}');
  if (userData && userData.project_id) {
    localStorage.setItem('project_id', userData.project_id);
    return userData.project_id;
  }

  return null;
};

/**
 * 财务对账API
 */
const financeService = {
  /**
   * 获取财务对账列表
   * @param {Object} params - 查询参数
   * @returns {Promise} - 财务对账列表
   */
  getReconciliations: async (params = {}) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.get(`/project/${projectId}/finance/reconciliation`, { params });
  },

  /**
   * 获取财务对账详情
   * @param {string} id - 财务对账ID
   * @returns {Promise} - 财务对账详情
   */
  getReconciliationDetail: async (id) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.get(`/project/${projectId}/finance/reconciliation/${id}`);
  },

  /**
   * 创建财务对账
   * @param {Object} data - 财务对账数据
   * @returns {Promise} - 创建结果
   */
  createReconciliation: async (data) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.post(`/project/${projectId}/finance/reconciliation`, data);
  },

  /**
   * 更新财务对账
   * @param {string} id - 财务对账ID
   * @param {Object} data - 财务对账数据
   * @returns {Promise} - 更新结果
   */
  updateReconciliation: async (id, data) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.put(`/project/${projectId}/finance/reconciliation/${id}`, data);
  },

  /**
   * 删除财务对账
   * @param {string} id - 财务对账ID
   * @returns {Promise} - 删除结果
   */
  deleteReconciliation: async (id) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.delete(`/project/${projectId}/finance/reconciliation/${id}`);
  },

  /**
   * 获取门店对账明细
   * @param {string} storeId - 门店ID
   * @param {string} month - 月份，格式为YYYY-MM
   * @returns {Promise} - 门店对账明细
   */
  getStoreReconciliationDetail: async (storeId, month) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.get(`/project/${projectId}/finance/reconciliation/store-detail/${storeId}`, {
      params: { month }
    });
  },

  /**
   * 获取财务对账统计数据
   * @param {string} month - 月份，格式为YYYY-MM
   * @param {string} [storeId] - 门店ID
   * @returns {Promise} - 财务对账统计数据
   */
  getReconciliationStatistics: async (month, storeId) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.get(`/project/${projectId}/finance/reconciliation/statistics`, {
      params: { month, store_id: storeId }
    });
  },

  /**
   * 上传对账数据
   * @param {FormData} formData - 包含文件和参数的表单数据
   * @returns {Promise} - 上传结果
   */
  uploadReconciliationData: async (formData) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.post(`/project/${projectId}/finance/reconciliation/upload`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
  },

  /**
   * 获取储值分析数据
   * @param {Object} params - 查询参数
   * @param {string} params.start_date - 开始日期
   * @param {string} params.end_date - 结束日期
   * @param {string} [params.store_id] - 门店ID
   * @param {string} [params.group_by] - 分组方式: month, week, day
   * @returns {Promise} - 储值分析数据
   */
  getRechargeAnalysis: async (params = {}) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    // 使用销售报表统计API，但添加特定参数以获取储值数据
    return api.get(`/project/${projectId}/sales-reports/statistics`, {
      params: {
        ...params,
        data_type: 'recharge'
      }
    });
  },

  /**
   * 获取利润分析数据
   * @param {Object} params - 查询参数
   * @param {string} params.start_date - 开始日期
   * @param {string} params.end_date - 结束日期
   * @param {string} [params.store_id] - 门店ID
   * @param {string} [params.group_by] - 分组方式: month, week, day
   * @returns {Promise} - 利润分析数据
   */
  getProfitAnalysis: async (params = {}) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    // 使用销售报表统计API，但添加特定参数以获取利润数据
    return api.get(`/project/${projectId}/sales-reports/statistics`, {
      params: {
        ...params,
        data_type: 'profit'
      }
    });
  },

  /**
   * 获取费用列表
   * @param {Object} params - 查询参数
   * @returns {Promise} - 费用列表
   */
  getExpenseList: async (params = {}) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.get(`/project/${projectId}/finance/expenses`, { params });
  },

  /**
   * 获取费用统计
   * @param {Object} params - 查询参数
   * @returns {Promise} - 费用统计
   */
  getExpenseStatistics: async (params = {}) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.get(`/project/${projectId}/finance/expenses-statistics`, { params });
  },

  /**
   * 标记费用异常
   * @param {string} expenseId - 费用ID
   * @param {Object} data - 异常数据
   * @returns {Promise} - 标记结果
   */
  markExpenseAbnormal: async (expenseId, data) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.put(`/project/${projectId}/finance/expenses/${expenseId}/mark-abnormal`, data);
  },

  /**
   * 获取采购分析数据
   * @param {Object} params - 查询参数
   * @param {string} params.start_date - 开始日期
   * @param {string} params.end_date - 结束日期
   * @param {string} [params.warehouse_id] - 仓库ID
   * @param {string} [params.supplier_id] - 供应商ID
   * @param {string} [params.report_type] - 报表类型
   * @returns {Promise} - 采购分析数据
   */
  getPurchaseAnalysis: async (params = {}) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.get(`/project/${projectId}/purchase-orders/report`, { params });
  },

  /**
   * 新增门店费用
   * @param {Object} data - 费用数据
   * @returns {Promise} - 创建结果
   */
  addStoreExpense: async (data) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.post(`/project/${projectId}/finance/expenses`, data);
  },

  /**
   * 获取到货列表
   * @param {Object} params - 查询参数
   * @returns {Promise} - 到货列表
   */
  getArrivalList: async (params = {}) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.get(`/project/${projectId}/finance/arrivals`, { params });
  },

  /**
   * 获取到货统计
   * @param {Object} params - 查询参数
   * @returns {Promise} - 到货统计
   */
  getArrivalStatistics: async (params = {}) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.get(`/project/${projectId}/finance/arrivals-statistics`, { params });
  },

  /**
   * 发送到货催办通知
   * @param {string} arrivalId - 到货ID
   * @param {Object} data - 催办数据
   * @returns {Promise} - 发送结果
   */
  sendArrivalReminder: async (arrivalId, data) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.post(`/project/${projectId}/finance/arrivals/${arrivalId}/remind`, data);
  },

  /**
   * 批量发送到货催办通知
   * @returns {Promise} - 发送结果
   */
  batchSendArrivalReminders: async () => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.post(`/project/${projectId}/finance/arrivals/batch-remind`);
  },

  /**
   * 导出费用列表
   * @param {Object} params - 查询参数
   * @returns {Promise} - 导出结果
   */
  exportExpenses: async (params = {}) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.get(`/project/${projectId}/finance/expenses/export`, {
      params,
      responseType: 'blob'
    });
  },

  /**
   * 导出到货列表
   * @param {Object} params - 查询参数
   * @returns {Promise} - 导出结果
   */
  exportArrivals: async (params = {}) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.get(`/project/${projectId}/finance/arrivals/export`, {
      params,
      responseType: 'blob'
    });
  },

  // ========== 盘点审核相关API ==========

  /**
   * 获取盘点审核列表
   * @param {Object} params - 查询参数
   * @returns {Promise} - 盘点审核列表
   */
  getInventoryAuditList: async (params = {}) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.get(`/project/${projectId}/finance/audit`, { params });
  },

  /**
   * 审核盘点数据
   * @param {Object} data - 审核数据
   * @returns {Promise} - 审核结果
   */
  auditInventory: async (data) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.post(`/project/${projectId}/finance/audit/audit`, data);
  },

  /**
   * 按门店月份审核盘点数据
   * @param {Object} data - 审核数据
   * @returns {Promise} - 审核结果
   */
  auditInventoryByStoreMonth: async (data) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.post(`/project/${projectId}/finance/audit/audit-by-store-month`, data);
  },

  /**
   * 获取盘点明细列表
   * @param {Object} params - 查询参数
   * @returns {Promise} - 盘点明细列表
   */
  getInventoryAuditItems: async (params = {}) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.get(`/project/${projectId}/finance/audit/items`, { params });
  },

  /**
   * 编辑盘点数据
   * @param {string} inventoryId - 盘点ID
   * @param {Object} data - 编辑数据
   * @returns {Promise} - 编辑结果
   */
  editInventoryData: async (inventoryId, data) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.put(`/project/${projectId}/finance/audit/${inventoryId}/edit`, data);
  },

  /**
   * 批量审核盘点数据
   * @param {Object} data - 批量审核数据
   * @returns {Promise} - 批量审核结果
   */
  batchAuditInventory: async (data) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.post(`/project/${projectId}/finance/audit/batch-audit`, data);
  },

  /**
   * 导出盘点审核数据
   * @param {Object} params - 查询参数
   * @returns {Promise} - 导出结果
   */
  exportInventoryAudit: async (params = {}) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.get(`/project/${projectId}/finance/audit/export`, {
      params,
      responseType: 'blob'
    });
  },

  /**
   * 上传第三方ERP系统库存
   * @param {FormData} formData - 包含文件和参数的表单数据
   * @returns {Promise} - 上传结果
   */
  uploadSystemInventory: async (formData) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.post(`/project/${projectId}/finance/audit/upload-system-inventory`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
  }
};

export default financeService;
