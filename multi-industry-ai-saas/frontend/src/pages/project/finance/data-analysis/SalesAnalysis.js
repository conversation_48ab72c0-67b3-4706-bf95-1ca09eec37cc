import React, { useState, useEffect } from 'react';
import { Card, Row, Col, DatePicker, Select, Button, Table, Statistic, Spin, Tabs, Space } from 'antd';
import { BarChartOutlined, LineChartOutlined, PieChartOutlined, DownloadOutlined } from '@ant-design/icons';
import { Line, Bar, Pie } from '@ant-design/plots';
import dayjs from "dayjs";
import apiService from '../../../../services/api';
import { logOperation } from '../../../../utils/logUtils';

const { RangePicker } = DatePicker;
const { Option } = Select;
const { TabPane } = Tabs;

/**
 * 销售分析组件
 */
const SalesAnalysis = () => {
  // 状态
  const [loading, setLoading] = useState(false);
  const [stores, setStores] = useState([]);
  const [selectedStoreId, setSelectedStoreId] = useState(null);
  const [dateRange, setDateRange] = useState([dayjs().subtract(30, 'days'), dayjs()]);
  const [timeGranularity, setTimeGranularity] = useState('day');
  const [salesData, setSalesData] = useState([]);
  const [channelData, setChannelData] = useState([]);
  const [paymentData, setPaymentData] = useState([]);

  // 初始化
  useEffect(() => {
    fetchStores();
  }, []);

  // 监听筛选条件变化
  useEffect(() => {
    if (dateRange && dateRange.length === 2) {
      fetchSalesData();
    }
  }, [selectedStoreId, dateRange, timeGranularity]);

  // 获取门店列表
  const fetchStores = async () => {
    try {
      setLoading(true);
      const response = await apiService.project.store.getList();
      if (response && response.items) {
        setStores(response.items);
      }
    } catch (error) {
      console.error('获取门店列表失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 获取销售数据
  const fetchSalesData = async () => {
    try {
      setLoading(true);
      const startDate = dateRange[0].format('YYYY-MM-DD');
      const endDate = dateRange[1].format('YYYY-MM-DD');

      const response = await apiService.project.salesReport.getStatistics({
        store_id: selectedStoreId,
        start_date: startDate,
        end_date: endDate,
        group_by: timeGranularity
      });

      if (response) {
        // 确保 salesData 是数组
        const salesDataArray = Array.isArray(response) ? response : [];
        setSalesData(salesDataArray);

        // 处理渠道数据
        if (Array.isArray(response) && response.length > 0 && response[0].channel_stats) {
          setChannelData(response[0].channel_stats);
        } else {
          setChannelData([]);
        }

        // 处理支付方式数据
        if (Array.isArray(response) && response.length > 0 && response[0].payment_stats) {
          setPaymentData(response[0].payment_stats);
        } else {
          setPaymentData([]);
        }

        // 记录操作日志
        logOperation({
          module: 'finance',
          action: 'query',
          resourceType: 'sales_analysis',
          description: `查询销售分析数据，时间范围：${startDate}至${endDate}，时间粒度：${timeGranularity}${selectedStoreId ? '，指定门店' : '，所有门店'}`,
          details: {
            start_date: startDate,
            end_date: endDate,
            store_id: selectedStoreId,
            group_by: timeGranularity
          }
        });
      }
    } catch (error) {
      console.error('获取销售数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 处理门店选择
  const handleStoreChange = (value) => {
    setSelectedStoreId(value);
  };

  // 处理日期范围选择
  const handleDateRangeChange = (dates) => {
    setDateRange(dates);
  };

  // 处理时间粒度选择
  const handleGranularityChange = (value) => {
    setTimeGranularity(value);
  };

  // 导出数据
  const handleExport = () => {
    // 实现导出功能
    console.log('导出数据');
  };

  // 销售趋势图配置
  const salesTrendConfig = {
    data: salesData,
    xField: timeGranularity,
    yField: 'total_sales',
    seriesField: 'type',
    smooth: true,
    legend: {
      position: 'top',
    },
    xAxis: {
      type: 'time',
    },
    tooltip: {
      showMarkers: false,
    },
  };

  // 渠道分布图配置
  const channelDistributionConfig = {
    data: channelData,
    angleField: 'amount',
    colorField: 'channel_name',
    radius: 0.8,
    label: {
      type: 'outer',
      formatter: ({channel_name, percent}) => `${channel_name}: ${(percent * 100).toFixed(2)}%`,
    },
    interactions: [
      {
        type: 'element-active',
      },
    ],
  };

  // 支付方式分布图配置
  const paymentDistributionConfig = {
    data: paymentData,
    angleField: 'amount',
    colorField: 'method_name',
    radius: 0.8,
    label: {
      type: 'outer',
      formatter: ({method_name, percent}) => `${method_name}: ${(percent * 100).toFixed(2)}%`,
    },
    interactions: [
      {
        type: 'element-active',
      },
    ],
  };

  // 销售数据表格列
  const salesColumns = [
    {
      title: '日期',
      dataIndex: timeGranularity,
      key: timeGranularity,
    },
    {
      title: '销售额(元)',
      dataIndex: 'total_sales',
      key: 'total_sales',
      render: (text) => text.toFixed(2),
    },
    {
      title: '线上销售(元)',
      dataIndex: 'online_sales',
      key: 'online_sales',
      render: (text) => text.toFixed(2),
    },
    {
      title: '线下销售(元)',
      dataIndex: 'offline_sales',
      key: 'offline_sales',
      render: (text) => text.toFixed(2),
    },
    {
      title: '订单数',
      dataIndex: 'total_orders',
      key: 'total_orders',
    },
    {
      title: '客流量',
      dataIndex: 'total_customers',
      key: 'total_customers',
    },
  ];

  return (
    <div className="sales-analysis-container">
      <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between' }}>
        <Space>
          <Select
            placeholder="选择门店"
            style={{ width: 200 }}
            allowClear
            onChange={handleStoreChange}
            value={selectedStoreId}
          >
            {stores.map(store => (
              <Option key={store.id} value={store.id}>{store.name}</Option>
            ))}
          </Select>
          <RangePicker
            value={dateRange}
            onChange={handleDateRangeChange}
          />
          <Select
            value={timeGranularity}
            onChange={handleGranularityChange}
            style={{ width: 120 }}
          >
            <Option value="day">按日</Option>
            <Option value="week">按周</Option>
            <Option value="month">按月</Option>
          </Select>
        </Space>
        <Button
          icon={<DownloadOutlined />}
          onClick={handleExport}
        >
          导出数据
        </Button>
      </div>

      <Spin spinning={loading}>
        <Tabs defaultActiveKey="trend">
          <TabPane
            tab={<span><LineChartOutlined />销售趋势</span>}
            key="trend"
          >
            <Row gutter={[16, 16]}>
              <Col span={24}>
                <Card>
                  <Line {...salesTrendConfig} />
                </Card>
              </Col>
            </Row>
          </TabPane>
          <TabPane
            tab={<span><PieChartOutlined />渠道分布</span>}
            key="channel"
          >
            <Row gutter={[16, 16]}>
              <Col span={12}>
                <Card>
                  <Pie {...channelDistributionConfig} />
                </Card>
              </Col>
              <Col span={12}>
                <Card>
                  <Table
                    columns={[
                      { title: '渠道', dataIndex: 'channel_name', key: 'channel_name' },
                      { title: '销售额(元)', dataIndex: 'amount', key: 'amount', render: (text) => text.toFixed(2) },
                    ]}
                    dataSource={channelData}
                    rowKey="channel_id"
                    pagination={false}
                  />
                </Card>
              </Col>
            </Row>
          </TabPane>
          <TabPane
            tab={<span><BarChartOutlined />销售明细</span>}
            key="detail"
          >
            <Table
              columns={salesColumns}
              dataSource={salesData}
              rowKey={timeGranularity}
              pagination={{ pageSize: 10 }}
            />
          </TabPane>
        </Tabs>
      </Spin>
    </div>
  );
};

export default SalesAnalysis;
