#!/usr/bin/env python
# -*- coding: utf-8 -*-

from typing import List, Optional, Dict, Any, Union
from pydantic import BaseModel, UUID4, Field
from datetime import datetime

# 调拨明细基础模型
class InventoryTransferItemBase(BaseModel):
    product_id: Optional[UUID4] = None
    product_name: str
    product_code: Optional[str] = None
    specification: Optional[str] = None
    unit: Optional[str] = None
    quantity: int
    price: Optional[float] = None
    amount: Optional[float] = None
    notes: Optional[str] = None

# 创建调拨明细请求模型
class InventoryTransferItemCreate(InventoryTransferItemBase):
    pass

# 更新调拨明细请求模型
class InventoryTransferItemUpdate(BaseModel):
    quantity: Optional[int] = None
    notes: Optional[str] = None

# 调拨基础模型
class InventoryTransferBase(BaseModel):
    transfer_type: str  # store, third_party
    source_warehouse_id: UUID4
    destination_id: Optional[UUID4] = None
    destination_name: Optional[str] = None
    transfer_date: datetime
    payment_method: Optional[str] = None
    notes: Optional[str] = None

# 创建调拨请求模型
class InventoryTransferCreate(InventoryTransferBase):
    items: List[InventoryTransferItemCreate]

# 更新调拨请求模型
class InventoryTransferUpdate(BaseModel):
    status: Optional[str] = None  # pending, processing, completed, cancelled
    notes: Optional[str] = None

# 数据库调拨明细模型
class InventoryTransferItemInDB(InventoryTransferItemBase):
    id: UUID4
    transfer_id: UUID4
    
    class Config:
        model_config = {"from_attributes": True}

# 数据库调拨模型
class InventoryTransferInDB(BaseModel):
    id: UUID4
    tenant_id: Optional[UUID4] = None
    project_id: UUID4
    transfer_number: str
    transfer_type: str
    source_warehouse_id: UUID4
    destination_id: Optional[UUID4] = None
    destination_name: Optional[str] = None
    transfer_date: datetime
    status: str
    payment_method: Optional[str] = None
    total_amount: float
    total_items: int
    notes: Optional[str] = None
    created_by: Optional[UUID4] = None
    updated_by: Optional[UUID4] = None
    created_at: datetime
    updated_at: datetime
    
    # 非数据库字段，用于前端展示
    source_warehouse_name: Optional[str] = None
    destination_name: Optional[str] = None
    items: List[InventoryTransferItemInDB] = []
    
    class Config:
        model_config = {"from_attributes": True}

# 调拨响应模型
class InventoryTransferResponse(BaseModel):
    success: bool
    message: Optional[str] = None
    data: Optional[InventoryTransferInDB] = None

# 调拨列表响应模型
class InventoryTransferListResponse(BaseModel):
    success: bool
    message: Optional[str] = None
    items: List[InventoryTransferInDB]
    total: int
    page: Optional[int] = None
    page_size: Optional[int] = None
    pages: Optional[int] = None

# 上传调拨表格响应模型
class UploadTransferSheetResponse(BaseModel):
    success: bool
    message: Optional[str] = None
    data: Optional[Dict[str, Any]] = None
