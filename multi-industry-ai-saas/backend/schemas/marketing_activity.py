#!/usr/bin/env python
# -*- coding: utf-8 -*-

from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field, UUID4
from datetime import datetime

# 营销活动基础模型
class MarketingActivityBase(BaseModel):
    title: str = Field(..., description="活动标题")
    description: Optional[str] = Field(None, description="活动描述")
    type: Optional[str] = Field(None, description="活动类型: promotion-促销活动, discount-折扣活动, event-主题活动, etc.")
    start_date: datetime = Field(..., description="开始日期")
    end_date: datetime = Field(..., description="结束日期")
    discount_rate: Optional[float] = Field(None, description="折扣率")
    image_url: Optional[str] = Field(None, description="活动图片URL")
    target_stores: Optional[List[UUID4]] = Field(None, description="目标门店ID列表")
    is_all_stores: Optional[bool] = Field(False, description="是否适用于所有门店")
    rules: Optional[str] = Field(None, description="活动规则")
    attachments: Optional[List[str]] = Field(None, description="附件列表")
    custom_fields: Optional[Dict[str, Any]] = Field(None, description="自定义字段")

# 创建营销活动请求模型
class MarketingActivityCreate(MarketingActivityBase):
    status: str = Field("pending", description="活动状态: draft-草稿, pending-待开始, active-进行中, completed-已结束, cancelled-已取消")

    class Config:
        json_schema_extra = {
            "example": {
                "title": "夏季促销活动",
                "description": "夏季服装全场8折",
                "type": "discount",
                "start_date": "2023-06-01T00:00:00",
                "end_date": "2023-06-30T23:59:59",
                "discount_rate": 0.8,
                "target_stores": ["123e4567-e89b-12d3-a456-426614174000"],
                "is_all_stores": False,
                "status": "pending"
            }
        }

# 更新营销活动请求模型
class MarketingActivityUpdate(BaseModel):
    title: Optional[str] = Field(None, description="活动标题")
    description: Optional[str] = Field(None, description="活动描述")
    type: Optional[str] = Field(None, description="活动类型")
    start_date: Optional[datetime] = Field(None, description="开始日期")
    end_date: Optional[datetime] = Field(None, description="结束日期")
    status: Optional[str] = Field(None, description="活动状态")
    discount_rate: Optional[float] = Field(None, description="折扣率")
    image_url: Optional[str] = Field(None, description="活动图片URL")
    target_stores: Optional[List[UUID4]] = Field(None, description="目标门店ID列表")
    is_all_stores: Optional[bool] = Field(None, description="是否适用于所有门店")
    rules: Optional[str] = Field(None, description="活动规则")
    attachments: Optional[List[str]] = Field(None, description="附件列表")
    custom_fields: Optional[Dict[str, Any]] = Field(None, description="自定义字段")

    class Config:
        json_schema_extra = {
            "example": {
                "title": "夏季促销活动",
                "description": "夏季服装全场7折",
                "discount_rate": 0.7,
                "status": "active"
            }
        }

# 更新营销活动状态请求模型
class MarketingActivityStatusUpdate(BaseModel):
    status: str = Field(..., description="活动状态: draft-草稿, pending-待开始, active-进行中, completed-已结束, cancelled-已取消")

# 营销活动响应模型
class MarketingActivityResponse(MarketingActivityBase):
    id: UUID4 = Field(..., description="活动ID")
    project_id: UUID4 = Field(..., description="项目ID")
    status: str = Field(..., description="活动状态")
    created_by: Optional[UUID4] = Field(None, description="创建者ID")
    updated_by: Optional[UUID4] = Field(None, description="更新者ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    store_names: Optional[List[str]] = Field(None, description="目标门店名称列表")
    creator_name: Optional[str] = Field(None, description="创建者名称")

    class Config:
        from_attributes = True

# 营销活动列表响应模型
class MarketingActivityListResponse(BaseModel):
    items: List[MarketingActivityResponse] = Field(..., description="活动列表")
    total: int = Field(..., description="总数")
    page: int = Field(1, description="当前页码")
    size: int = Field(10, description="每页数量")
    pages: int = Field(1, description="总页数")

# 营销活动统计响应模型
class MarketingActivityStatsResponse(BaseModel):
    total: int = Field(..., description="总数")
    draft: int = Field(..., description="草稿数")
    pending: int = Field(..., description="待开始数")
    active: int = Field(..., description="进行中数")
    completed: int = Field(..., description="已结束数")
    cancelled: int = Field(..., description="已取消数")
