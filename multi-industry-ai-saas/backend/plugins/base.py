#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
插件基类和接口定义

这个模块定义了插件系统的基础类和接口，所有插件都应该继承自 BasePlugin 类。
"""

import logging
import json
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Union, Set
from fastapi import APIRouter, Depends, HTTPException, Header, Request, status
from fastapi.security import HTTPBearer, HTTPAuthorizationCredentials

# 使用相对导入
try:
    from api.auth.jwt import decode_jwt_token, JWTPayload
except ImportError:
    # 在Docker容器中，路径可能不同
    try:
        from ..api.auth.jwt import decode_jwt_token, JWTPayload
    except ImportError:
        # 如果都失败，尝试直接导入
        try:
            import sys
            import os
            sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
            from api.auth.jwt import decode_jwt_token, JWTPayload
        except ImportError:
            # 如果还是失败，使用本地实现
            import jwt
            from dataclasses import dataclass
            from typing import Dict, List, Optional
            from datetime import datetime

            @dataclass
            class JWTPayload:
                """JWT负载"""
                sub: str  # 用户ID
                token_id: str  # 令牌ID
                token_type: str  # 令牌类型：access, refresh, mcp
                project_id: Optional[str] = None  # 项目ID
                permissions: Optional[List[str]] = None  # 权限列表
                server_permissions: Optional[Dict[str, List[str]]] = None  # 服务器权限映射
                default_permissions: Optional[List[str]] = None  # 默认权限
                exp: Optional[int] = None  # 过期时间
                iat: Optional[int] = None  # 签发时间
                jti: Optional[str] = None  # JWT ID

            def decode_jwt_token(token: str) -> JWTPayload:
                """解码JWT令牌"""
                try:
                    # 使用统一的SECRET_KEY
                    from core.config import settings
                    secret_key = settings.SECRET_KEY
                    payload = jwt.decode(token, secret_key, algorithms=[settings.ALGORITHM])

                    return JWTPayload(
                        sub=payload.get("sub"),
                        token_id=payload.get("token_id"),
                        token_type=payload.get("token_type"),
                        project_id=payload.get("project_id"),
                        permissions=payload.get("permissions"),
                        server_permissions=payload.get("server_permissions"),
                        default_permissions=payload.get("default_permissions"),
                        exp=payload.get("exp"),
                        iat=payload.get("iat"),
                        jti=payload.get("jti"),
                    )
                except Exception as e:
                    raise HTTPException(
                        status_code=status.HTTP_401_UNAUTHORIZED,
                        detail=f"无效的认证令牌: {str(e)}",
                        headers={"WWW-Authenticate": "Bearer"},
                    )

logger = logging.getLogger(__name__)

# JWT认证依赖
oauth2_scheme = HTTPBearer(auto_error=False)

async def get_jwt_token(
    authorization: Optional[HTTPAuthorizationCredentials] = Depends(oauth2_scheme),
) -> Optional[str]:
    """
    从请求头中获取JWT令牌

    Args:
        authorization: 认证凭证

    Returns:
        Optional[str]: JWT令牌，如果不存在则返回None
    """
    if authorization is None:
        return None
    return authorization.credentials

async def verify_jwt_token(
    token: Optional[str] = Depends(get_jwt_token),
    required_permissions: Optional[List[str]] = None,
    required_server_id: Optional[str] = None,
) -> Optional[JWTPayload]:
    """
    验证JWT令牌

    Args:
        token: JWT令牌
        required_permissions: 所需权限列表
        required_server_id: 所需服务器ID

    Returns:
        Optional[JWTPayload]: JWT负载，如果验证失败则返回None

    Raises:
        HTTPException: 如果令牌无效或权限不足
    """
    if token is None:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="未提供认证令牌",
            headers={"WWW-Authenticate": "Bearer"},
        )

    try:
        # 解码JWT令牌
        payload = decode_jwt_token(token)

        # 检查令牌类型
        if payload.token_type not in ["access", "mcp"]:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"令牌类型 {payload.token_type} 不允许访问此资源",
            )

        # 检查权限
        if required_permissions:
            has_permission = False

            # 检查服务器特定权限
            if required_server_id and payload.server_permissions:
                server_perms = payload.server_permissions.get(required_server_id, [])
                if any(perm in server_perms for perm in required_permissions):
                    has_permission = True

            # 检查默认权限
            if not has_permission and payload.default_permissions:
                if any(perm in payload.default_permissions for perm in required_permissions):
                    has_permission = True

            if not has_permission:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"权限不足，需要权限: {', '.join(required_permissions)}",
                )

        return payload
    except Exception as e:
        logger.error(f"JWT令牌验证失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的认证令牌",
            headers={"WWW-Authenticate": "Bearer"},
        )

class BasePlugin(ABC):
    """
    插件基类

    所有插件都应该继承自这个类，并实现必要的方法。
    """

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        初始化插件

        Args:
            config: 插件配置
        """
        self.config = config or {}
        self.router = APIRouter()
        self.jwt_required = True  # 默认需要JWT认证
        self.required_permissions = ["read"]  # 默认需要读权限
        self._initialize_routes()

    @abstractmethod
    def get_id(self) -> str:
        """
        获取插件ID

        Returns:
            str: 插件ID，通常是一个唯一标识符
        """
        pass

    @abstractmethod
    def get_name(self) -> str:
        """
        获取插件名称

        Returns:
            str: 插件名称，用于显示
        """
        pass

    @abstractmethod
    def get_description(self) -> str:
        """
        获取插件描述

        Returns:
            str: 插件描述
        """
        pass

    @abstractmethod
    def get_version(self) -> str:
        """
        获取插件版本

        Returns:
            str: 插件版本，如 "1.0.0"
        """
        pass

    @abstractmethod
    def get_author(self) -> str:
        """
        获取插件作者

        Returns:
            str: 插件作者
        """
        pass

    @abstractmethod
    def get_type(self) -> str:
        """
        获取插件类型

        Returns:
            str: 插件类型，可以是 "marketplace", "ai", "system" 中的一种
        """
        pass

    def get_dependencies(self) -> List[Dict[str, str]]:
        """
        获取插件依赖

        Returns:
            List[Dict[str, str]]: 插件依赖列表，每个依赖包含 id 和 version
        """
        return []

    def get_permissions(self) -> List[str]:
        """
        获取插件所需权限

        Returns:
            List[str]: 插件所需权限列表
        """
        return []

    def get_config_schema(self) -> Dict[str, Any]:
        """
        获取插件配置模式

        Returns:
            Dict[str, Any]: 插件配置模式，通常是一个 JSON Schema
        """
        return {}

    def get_default_config(self) -> Dict[str, Any]:
        """
        获取插件默认配置

        Returns:
            Dict[str, Any]: 插件默认配置
        """
        return {}

    async def initialize(self) -> None:
        """
        初始化插件

        这个方法会在插件加载时调用，可以用于执行一些初始化操作，如注册路由、连接数据库等。
        """
        logger.info(f"插件 {self.get_name()} 初始化")

    def shutdown(self) -> None:
        """
        关闭插件

        在插件卸载时调用，用于释放插件资源
        """
        logger.info(f"插件 {self.get_name()} 关闭")

    def get_info(self) -> Dict[str, Any]:
        """
        获取插件信息

        Returns:
            Dict[str, Any]: 插件信息
        """
        return {
            'id': self.get_id(),
            'name': self.get_name(),
            'description': self.get_description(),
            'version': self.get_version(),
            'author': self.get_author(),
            'type': self.get_type(),
            'dependencies': self.get_dependencies(),
            'permissions': self.get_permissions(),
            'config_schema': self.get_config_schema(),
            'default_config': self.get_default_config()
        }

    def get_router(self) -> APIRouter:
        """
        获取插件路由

        Returns:
            APIRouter: 插件路由
        """
        return self.router

    def get_jwt_dependency(self, permissions: Optional[List[str]] = None) -> Any:
        """
        获取JWT认证依赖

        Args:
            permissions: 所需权限列表，如果为None则使用默认权限

        Returns:
            Any: JWT认证依赖
        """
        if not self.jwt_required:
            # 如果不需要JWT认证，返回一个空依赖
            async def no_auth():
                return None
            return Depends(no_auth)

        # 使用默认权限
        required_perms = permissions or self.required_permissions

        # 创建一个闭包，捕获服务器ID
        async def verify_token_for_server(
            token: Optional[str] = Depends(get_jwt_token),
        ) -> Optional[JWTPayload]:
            return await verify_jwt_token(
                token=token,
                required_permissions=required_perms,
                required_server_id=self.get_id(),
            )

        return Depends(verify_token_for_server)

    def _initialize_routes(self) -> None:
        """
        初始化插件路由

        子类可以重写这个方法来添加自定义路由
        """
        pass


class PluginManager:
    """
    插件管理器

    负责插件的加载、卸载和管理
    """

    def __init__(self):
        """
        初始化插件管理器
        """
        self.plugins: Dict[str, BasePlugin] = {}
        self.plugin_routes: Dict[str, APIRouter] = {}

    async def register_plugin(self, plugin: BasePlugin) -> None:
        """
        注册插件

        Args:
            plugin: 插件实例
        """
        plugin_id = plugin.get_id()
        if plugin_id in self.plugins:
            logger.warning(f"插件 {plugin_id} 已经注册，将被覆盖")

        self.plugins[plugin_id] = plugin
        self.plugin_routes[plugin_id] = plugin.get_router()

        # 初始化插件
        await plugin.initialize()

        logger.info(f"插件 {plugin_id} 注册成功")

    def unregister_plugin(self, plugin_id: str) -> None:
        """
        注销插件

        Args:
            plugin_id: 插件ID
        """
        if plugin_id in self.plugins:
            # 关闭插件
            self.plugins[plugin_id].shutdown()

            # 移除插件
            del self.plugins[plugin_id]
            del self.plugin_routes[plugin_id]

            logger.info(f"插件 {plugin_id} 注销成功")
        else:
            logger.warning(f"插件 {plugin_id} 不存在，无法注销")

    def get_plugin(self, plugin_id: str) -> Optional[BasePlugin]:
        """
        获取插件

        Args:
            plugin_id: 插件ID

        Returns:
            Optional[BasePlugin]: 插件实例，如果不存在则返回 None
        """
        return self.plugins.get(plugin_id)

    def get_all_plugins(self) -> Dict[str, BasePlugin]:
        """
        获取所有插件

        Returns:
            Dict[str, BasePlugin]: 所有插件
        """
        return self.plugins

    def get_plugins_by_type(self, plugin_type: str) -> Dict[str, BasePlugin]:
        """
        获取指定类型的插件

        Args:
            plugin_type: 插件类型

        Returns:
            Dict[str, BasePlugin]: 指定类型的插件
        """
        return {
            plugin_id: plugin
            for plugin_id, plugin in self.plugins.items()
            if plugin.get_type() == plugin_type
        }

    # 注意：插件路由现在直接在 api/v1/project/__init__.py 中注册，不再需要这个函数


# 创建全局插件管理器实例
plugin_manager = PluginManager()
