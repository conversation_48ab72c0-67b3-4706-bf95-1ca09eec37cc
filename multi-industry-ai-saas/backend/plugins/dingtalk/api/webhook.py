#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
钉钉事件订阅和Webhook处理API
支持审批、待办、群聊等事件的订阅和处理
"""

import logging
import json
import hmac
import hashlib
import base64
from typing import Dict, Any, Optional
from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException, Request, status, Body
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, func

from db.database import get_db
from models.user import User
from models.project import Project
from models.tenant import Tenant
from core.auth import get_current_user
from api.deps import get_current_project, get_current_tenant
from utils.json_utils import safe_jsonable_encoder, safe_json_response, repair_json_string
from ..models.models import DingTalkWebhook, DingTalkEventLog
from ..services.webhook_service import WebhookService
from ..utils.dingtalk_api import DingTalkAPI
from ..utils.callback_crypto import DingTalkCallbackCrypto, DingTalkEventValidator, create_crypto_instance
from ..services.stream_service import get_stream_manager
from ..services.unified_settings_service import UnifiedSettingsService

logger = logging.getLogger(__name__)
router = APIRouter()

@router.post("/webhook/events")
async def handle_dingtalk_events(
    request: Request,
    db: AsyncSession = Depends(get_db)
):
    """
    处理钉钉事件回调
    
    支持HTTP推送模式的加密数据解密和签名验证
    钉钉会向此接口推送各种事件，包括：
    - 审批状态变更
    - 待办任务变更
    - 群聊消息
    - 通讯录变更等
    """
    start_time = datetime.utcnow()
    
    try:
        # 获取请求体和头部信息
        body = await request.body()
        headers = dict(request.headers)
        query_params = dict(request.query_params)
        
        # 获取加解密参数（优先从查询参数获取，其次从头部获取）
        msg_signature = (
            query_params.get('signature') or 
            query_params.get('msg_signature') or
            headers.get('signature') or 
            headers.get('msg_signature', '')
        )
        timestamp = (
            query_params.get('timestamp') or 
            query_params.get('timeStamp') or
            headers.get('timestamp') or 
            headers.get('timeStamp', '')
        )
        nonce = (
            query_params.get('nonce') or 
            headers.get('nonce', '')
        )
        
        logger.info(f"收到钉钉事件回调: signature={msg_signature}, timestamp={timestamp}, nonce={nonce}")
        logger.info(f"查询参数: {query_params}")
        logger.info(f"请求头: {headers}")
        logger.info(f"请求体: {body.decode('utf-8') if body else 'empty'}")
        
        # 解析请求体
        try:
            if body:
                request_data = json.loads(body.decode('utf-8'))
            else:
                request_data = {}
        except json.JSONDecodeError as e:
            logger.error(f"无效的JSON数据: {body}, 错误: {e}")
            return {"success": False, "message": "无效的JSON数据"}
        
        # 获取加密消息
        encrypt_msg = request_data.get('encrypt', '')
        
        # 初始化事件数据
        event_data = {}
        decrypted_msg = ""
        webhook_config = None
        
        # 如果有加密消息，尝试解密
        if encrypt_msg:
            logger.info("检测到加密消息，尝试解密...")
            
            # 查找匹配的Webhook配置进行解密
            webhook_configs = await db.execute(
                select(DingTalkWebhook).where(
                    and_(
                        DingTalkWebhook.enabled == True,
                        DingTalkWebhook.subscription_mode == 'http',
                        DingTalkWebhook.aes_key.isnot(None),
                        DingTalkWebhook.token.isnot(None)
                    )
                )
            )
            
            webhook_config = webhook_configs.scalar_one_or_none()
            if not webhook_config:
                logger.error("未找到有效的HTTP推送模式Webhook配置")
                return {"success": False, "message": "未配置事件订阅"}
            
            try:
                # 获取钉钉配置中的app_key作为ownerKey
                # 根据钉钉官方文档：企业级事件推送的OWNER_KEY为应用的Client ID（即appKey）
                from ..services.unified_settings_service import UnifiedSettingsService
                
                # 使用钉钉插件自己的配置服务获取app_key
                settings_service = UnifiedSettingsService(db, str(webhook_config.tenant_id), str(webhook_config.project_id))
                settings = await settings_service.get_settings()
                
                app_key = settings.get("app_key")
                if not app_key:
                    logger.error("钉钉配置中缺少app_key，无法进行解密")
                    raise ValueError("钉钉配置中缺少app_key")
                
                logger.info(f"📋 获取到app_key作为ownerKey: {app_key}, webhook项目ID: {webhook_config.project_id}")
                
                # 创建加解密实例
                # 根据钉钉官方文档：企业级事件推送使用app_key作为ownerKey
                crypto_config = {
                    'token': webhook_config.token,
                    'aes_key': webhook_config.aes_key,
                    'owner_key': app_key  # 使用app_key作为ownerKey
                }
                
                crypto = create_crypto_instance(crypto_config)
                
                # 解密消息
                decrypted_msg = crypto.get_decrypt_msg(msg_signature, timestamp, nonce, encrypt_msg)
                event_data = json.loads(decrypted_msg)
                
                logger.info(f"消息解密成功: {event_data.get('EventType', 'unknown')}")
                
            except Exception as e:
                logger.error(f"消息解密失败: {e}")
                # 如果解密失败，记录详细信息但继续处理
                logger.error(f"解密参数: token存在={bool(webhook_config.token)}, aes_key存在={bool(webhook_config.aes_key)}")
                logger.error(f"签名验证失败，尝试作为非加密事件处理")
                event_data = request_data
                
        else:
            # 没有加密消息，直接使用请求数据
            event_data = request_data
            logger.info("未检测到加密消息，直接处理事件数据")
        
        # 检查是否为URL验证事件（钉钉首次配置时的验证）
        if 'challenge' in event_data:
            challenge = event_data.get('challenge')
            logger.info(f"收到URL验证请求，challenge: {challenge}")
            
            # 如果是加密模式且有配置，返回加密的challenge
            if encrypt_msg and webhook_config:
                try:
                    crypto_config = {
                        'token': webhook_config.token,
                        'aes_key': webhook_config.aes_key,
                        'owner_key': app_key  # 使用上面获取的app_key
                    }
                    crypto = create_crypto_instance(crypto_config)
                    encrypted_response = crypto.get_encrypted_map(challenge)
                    logger.info("返回加密的URL验证响应")
                    return encrypted_response
                except Exception as e:
                    logger.error(f"加密challenge失败: {e}")
                    # fallback到直接返回
                    return {"challenge": challenge}
            else:
                # 非加密模式直接返回challenge
                logger.info("返回非加密的URL验证响应")
                return {"challenge": challenge}
        
        # 验证事件数据结构 - 对于非URL验证事件
        if not event_data.get('EventType') and not event_data.get('challenge'):
            logger.warning(f"事件数据缺少EventType字段，详细分析:")
            logger.warning(f"  - 事件数据类型: {type(event_data)}")
            logger.warning(f"  - 事件数据keys: {list(event_data.keys()) if isinstance(event_data, dict) else 'Not a dict'}")
            logger.warning(f"  - 事件数据内容: {event_data}")
            logger.warning(f"  - 是否有encrypt字段: {'encrypt' in event_data if isinstance(event_data, dict) else False}")
            logger.warning(f"  - 原始请求headers: {dict(request.headers) if request else 'No request'}")
            # 不直接返回错误，而是尝试处理
            event_data['EventType'] = 'unknown'
        
        # 记录事件日志
        event_log = DingTalkEventLog(
            event_type=event_data.get('EventType', 'unknown'),
            event_data=event_data,
            signature=msg_signature,
            timestamp=timestamp,
            status="pending"
        )
        db.add(event_log)
        await db.commit()
        
        # 初始化Webhook服务并处理事件
        webhook_service = WebhookService(db)
        result = await webhook_service.handle_event(event_data, msg_signature, timestamp)
        
        # 更新处理状态
        event_log.status = "processed" if result.get('success', False) else "failed"
        event_log.process_result = result
        await db.commit()
        
        # 返回加密的成功响应（如果是加密模式）
        if encrypt_msg and webhook_config:
            try:
                # 重新获取app_key（因为在不同的代码块中）
                # DingTalkConfig 已删除，统一使用 DingTalkSettings
                
                # 使用统一设置服务获取配置
                from ..services.unified_settings_service import UnifiedSettingsService
                settings_service = UnifiedSettingsService(db, str(webhook_config.tenant_id), str(webhook_config.project_id))
                config = await settings_service.get_config_for_api()
                
                if config and config.get("app_key"):
                    crypto_config = {
                        'token': webhook_config.token,
                        'aes_key': webhook_config.aes_key,
                        'owner_key': config.get("app_key")
                    }
                    crypto = create_crypto_instance(crypto_config)
                    encrypted_response = crypto.get_encrypted_map("success")
                    return encrypted_response
                else:
                    logger.error("加密成功响应失败: 缺少app_key、suite_key或corp_id参数")
                    return {"success": True}
            except Exception as e:
                logger.error(f"加密成功响应失败: {e}")
                return {"success": True}
        
        return {"success": True}
        
    except Exception as e:
        logger.error(f"处理钉钉事件失败: {str(e)}")
        
        # 记录错误日志
        try:
            error_log = DingTalkEventLog(
                event_type='error',
                event_data={'error': str(e), 'headers': dict(request.headers), 'body': body.decode('utf-8') if body else ''},
                signature=headers.get('signature', ''),
                timestamp=headers.get('timestamp', ''),
                status="failed"
            )
            db.add(error_log)
            await db.commit()
        except Exception as log_error:
            logger.error(f"记录错误日志失败: {log_error}")
        
        return {"success": False, "message": f"处理事件失败: {str(e)}"}

@router.post("/webhook/subscribe")
async def subscribe_dingtalk_events(
    subscription_data: Dict[str, Any] = Body(...),
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: AsyncSession = Depends(get_db)
):
    """
    订阅钉钉事件
    
    支持的事件类型：
    - bpms_instance_change: 审批实例状态变更
    - bpms_task_change: 审批任务状态变更
    - todo_task_change: 待办任务变更
    - chat_update_title: 群聊标题变更
    - user_add_org: 用户加入企业
    - user_modify_org: 用户信息变更
    """
    try:
        # 验证订阅数据
        event_types = subscription_data.get('event_types', [])
        callback_url = subscription_data.get('callback_url', '')
        
        if not event_types:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="必须指定要订阅的事件类型"
            )
        
        if not callback_url:
            # 使用默认回调URL
            callback_url = f"/api/v1/project/{current_project.id}/plugin/dingtalk/webhook/events"
        
        # 初始化钉钉API
        from services.system_config import SystemConfigService
        configs = await SystemConfigService.get_configs_by_type(db, current_project.id, "third_party_login")
        config_dict = {config.config_key: config.config_value for config in configs}
        dingtalk_config = config_dict.get("dingtalk", {})
        
        app_key = dingtalk_config.get("app_key")
        app_secret = dingtalk_config.get("app_secret")
        
        if not app_key or not app_secret:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="钉钉配置不完整，无法订阅事件"
            )
        
        dingtalk_api = DingTalkAPI(app_key, app_secret)
        webhook_service = WebhookService(db)
        
        # 创建事件订阅
        result = await webhook_service.create_subscription(
            tenant_id=current_tenant.id,
            project_id=current_project.id,
            event_types=event_types,
            callback_url=callback_url,
            dingtalk_api=dingtalk_api
        )
        
        return {
            "success": True,
            "message": "事件订阅创建成功",
            "data": result
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"订阅钉钉事件失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"订阅事件失败: {str(e)}"
        )

@router.get("/webhooks/subscriptions")
async def get_webhook_subscriptions(
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: AsyncSession = Depends(get_db)
):
    """获取当前项目的事件订阅列表"""
    try:
        query = select(DingTalkWebhook).where(
            and_(
                DingTalkWebhook.tenant_id == current_tenant.id,
                DingTalkWebhook.project_id == current_project.id
            )
        ).order_by(DingTalkWebhook.created_at.desc())
        
        result = await db.execute(query)
        webhooks = result.scalars().all()
        
        webhook_data = []
        for webhook in webhooks:
            webhook_data.append({
                "id": str(webhook.id),
                "name": webhook.name,
                "webhook_type": webhook.webhook_type,
                "webhook_url": webhook.webhook_url,
                "event_types": webhook.event_types,
                "callback_url": webhook.callback_url,
                "enabled": webhook.enabled,
                "secret": webhook.secret,
                "description": webhook.description,
                "created_at": webhook.created_at.isoformat() if webhook.created_at else None,
                "updated_at": webhook.updated_at.isoformat() if webhook.updated_at else None,
                "last_triggered_at": webhook.last_triggered_at.isoformat() if webhook.last_triggered_at else None
            })
        
        return {
            "success": True,
            "data": webhook_data
        }
        
    except Exception as e:
        logger.error(f"获取事件订阅列表失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取订阅列表失败: {str(e)}"
        )

@router.post("/webhooks/subscriptions")
async def create_webhook_subscription(
    subscription_data: Dict[str, Any] = Body(...),
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: AsyncSession = Depends(get_db)
):
    """创建事件订阅"""
    try:
        import uuid
        
        # 验证必需字段
        name = subscription_data.get('name')
        callback_url = subscription_data.get('callback_url')
        event_types = subscription_data.get('event_types', [])
        secret = subscription_data.get('secret')
        enabled = subscription_data.get('enabled', True)
        subscription_mode = subscription_data.get('subscription_mode', 'http')
        
        # Stream模式配置
        stream_endpoint = subscription_data.get('stream_endpoint')
        
        # HTTP推送模式配置
        aes_key = subscription_data.get('aes_key')
        token = subscription_data.get('token')
        
        if not name:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="订阅名称不能为空"
            )
        
        if not event_types:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="必须选择至少一个事件类型"
            )
        
        # 根据订阅模式验证配置
        if subscription_mode == 'stream':
            if not stream_endpoint:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Stream模式需要配置连接端点"
                )
        elif subscription_mode == 'http':
            if not callback_url:
                # 自动设置默认回调URL
                callback_url = f"/api/v1/project/{current_project.id}/plugin/dingtalk/webhook/events"
            if not aes_key or not token:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="HTTP推送模式需要配置AES密钥和签名Token"
                )
            if len(aes_key) != 43:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="AES密钥必须为43位字符"
                )
        
        # 确定Webhook类型
        webhook_type = subscription_data.get('webhook_type', 'event')
        webhook_url = subscription_data.get('webhook_url', '')
        
        # 根据类型验证必需字段
        if webhook_type == 'robot':
            if not webhook_url:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="机器人通知必须提供webhook_url"
                )
            if not secret:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="机器人通知需要配置签名密钥"
                )
        
        # 创建Webhook记录
        webhook = DingTalkWebhook(
            tenant_id=current_tenant.id,
            project_id=current_project.id,
            name=name,
            webhook_type=webhook_type,
            webhook_url=webhook_url,
            callback_url=callback_url,
            secret=secret,
            enabled=enabled,
            subscription_mode=subscription_mode,
            aes_key=aes_key,
            token=token,
            stream_endpoint=stream_endpoint,
            event_types=event_types if webhook_type == 'event' else [],
            created_by=current_user.id,
            description=subscription_data.get('description', '')
        )
        
        db.add(webhook)
        await db.commit()
        await db.refresh(webhook)
        
        # 如果是Stream模式，启动连接
        if webhook.subscription_mode == 'stream' and webhook.enabled:
            try:
                stream_manager = await get_stream_manager(db)
                await stream_manager._create_connection(webhook)
                logger.info(f"Stream模式Webhook连接已启动: {webhook.name}")
            except Exception as e:
                logger.error(f"启动Stream连接失败: {e}")
                # 不抛出异常，允许Webhook创建成功但连接失败
        
        return {
            "success": True,
            "message": f"{'机器人通知' if webhook_type == 'robot' else '事件订阅'}创建成功",
            "data": {
                "id": str(webhook.id),
                "name": webhook.name,
                "webhook_type": webhook.webhook_type,
                "webhook_url": webhook.webhook_url,
                "callback_url": webhook.callback_url,
                "subscription_mode": webhook.subscription_mode,
                "stream_status": webhook.stream_status,
                "event_types": webhook.event_types,
                "enabled": webhook.enabled,
                "description": webhook.description,
                "created_at": webhook.created_at.isoformat() if webhook.created_at else None
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建事件订阅失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建事件订阅失败: {str(e)}"
        )

@router.put("/webhooks/subscriptions/{webhook_id}")
async def update_webhook_subscription(
    webhook_id: str,
    update_data: Dict[str, Any] = Body(...),
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: AsyncSession = Depends(get_db)
):
    """更新事件订阅配置"""
    try:
        query = select(DingTalkWebhook).where(
            and_(
                DingTalkWebhook.id == webhook_id,
                DingTalkWebhook.tenant_id == current_tenant.id,
                DingTalkWebhook.project_id == current_project.id
            )
        )
        
        result = await db.execute(query)
        webhook = result.scalar_one_or_none()
        
        if not webhook:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="事件订阅不存在"
            )
        
        # 更新配置
        if 'name' in update_data:
            webhook.name = update_data['name']
        if 'webhook_url' in update_data:
            webhook.webhook_url = update_data['webhook_url']
        if 'event_types' in update_data:
            webhook.event_types = update_data['event_types']
        if 'enabled' in update_data:
            webhook.enabled = update_data['enabled']
        if 'callback_url' in update_data:
            webhook.callback_url = update_data['callback_url']
        if 'secret' in update_data:
            webhook.secret = update_data['secret']
        if 'description' in update_data:
            webhook.description = update_data['description']
        
        webhook.updated_at = datetime.utcnow()
        await db.commit()
        
        return {
            "success": True,
            "message": "事件订阅更新成功"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新事件订阅失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新订阅失败: {str(e)}"
        )

@router.delete("/webhooks/subscriptions/{webhook_id}")
async def delete_webhook_subscription(
    webhook_id: str,
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: AsyncSession = Depends(get_db)
):
    """删除事件订阅"""
    try:
        query = select(DingTalkWebhook).where(
            and_(
                DingTalkWebhook.id == webhook_id,
                DingTalkWebhook.tenant_id == current_tenant.id,
                DingTalkWebhook.project_id == current_project.id
            )
        )
        
        result = await db.execute(query)
        webhook = result.scalar_one_or_none()
        
        if not webhook:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="事件订阅不存在"
            )
        
        await db.delete(webhook)
        await db.commit()
        
        return {
            "success": True,
            "message": "事件订阅删除成功"
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除事件订阅失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除订阅失败: {str(e)}"
        )

@router.get("/webhooks/events")
async def get_event_logs(
    limit: int = 50,
    event_type: Optional[str] = None,
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """获取事件处理日志"""
    try:
        query = select(DingTalkEventLog)
        
        if event_type:
            query = query.where(DingTalkEventLog.event_type == event_type)
        
        query = query.order_by(DingTalkEventLog.created_at.desc()).limit(limit)
        
        result = await db.execute(query)
        logs = result.scalars().all()
        
        log_data = []
        for log in logs:
            log_data.append({
                "id": str(log.id),
                "event_type": log.event_type,
                "event_data": log.event_data,
                "signature": log.signature,
                "timestamp": log.timestamp,
                "processed": log.status == "processed",
                "process_result": log.process_result,
                "created_at": log.created_at.isoformat() if log.created_at else None
            })
        
        return {
            "success": True,
            "data": log_data
        }
        
    except Exception as e:
        logger.error(f"获取事件日志失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取事件日志失败: {str(e)}"
        )

@router.get("/webhooks/statistics")
async def get_event_statistics(
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: AsyncSession = Depends(get_db)
):
    """获取事件统计信息"""
    try:
        # 获取总订阅数
        total_subscriptions_query = select(func.count(DingTalkWebhook.id)).where(
            and_(
                DingTalkWebhook.tenant_id == current_tenant.id,
                DingTalkWebhook.project_id == current_project.id
            )
        )
        total_subscriptions_result = await db.execute(total_subscriptions_query)
        total_subscriptions = total_subscriptions_result.scalar() or 0
        
        # 获取活跃订阅数
        active_subscriptions_query = select(func.count(DingTalkWebhook.id)).where(
            and_(
                DingTalkWebhook.tenant_id == current_tenant.id,
                DingTalkWebhook.project_id == current_project.id,
                DingTalkWebhook.enabled == True
            )
        )
        active_subscriptions_result = await db.execute(active_subscriptions_query)
        active_subscriptions = active_subscriptions_result.scalar() or 0
        
        # 获取今日事件数
        today = datetime.now().date()
        today_start = datetime.combine(today, datetime.min.time())
        today_end = datetime.combine(today, datetime.max.time())
        
        today_events_query = select(func.count(DingTalkEventLog.id)).where(
            and_(
                DingTalkEventLog.created_at >= today_start,
                DingTalkEventLog.created_at <= today_end
            )
        )
        today_events_result = await db.execute(today_events_query)
        today_events = today_events_result.scalar() or 0
        
        # 获取待处理事件数
        pending_events_query = select(func.count(DingTalkEventLog.id)).where(
            DingTalkEventLog.status == "pending"
        )
        pending_events_result = await db.execute(pending_events_query)
        pending_events = pending_events_result.scalar() or 0
        
        return {
            "success": True,
            "data": {
                "total_subscriptions": total_subscriptions,
                "active_subscriptions": active_subscriptions,
                "today_events": today_events,
                "pending_events": pending_events
            }
        }
        
    except Exception as e:
        logger.error(f"获取事件统计失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取事件统计失败: {str(e)}"
        ) 