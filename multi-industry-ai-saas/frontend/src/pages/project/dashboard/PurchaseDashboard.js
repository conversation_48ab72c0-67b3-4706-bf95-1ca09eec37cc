import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Typography,
  Table,
  Button,
  Spin,
  Empty,
  DatePicker,
  Tabs,
  List,
  Tag,
  Space,
  Select,
  Tooltip,
  Progress,
  Divider
} from 'antd';
import {
  ShoppingCartOutlined,
  DollarOutlined,
  <PERSON>UpOutlined,
  ArrowDownOutlined,
  SyncOutlined,
  <PERSON><PERSON>hartOutlined,
  PieChartOutlined,
  RobotOutlined,
  TeamOutlined,
  FileTextOutlined,
  LikeOutlined,
  DislikeOutlined
} from '@ant-design/icons';
import { Line, Pie, Column } from '@ant-design/plots';
import dayjs from 'dayjs';
import apiService from '../../../services/api';
import { message } from 'antd';
import { useNavigate } from 'react-router-dom';

const { Title, Text, Paragraph } = Typography;
const { RangePicker } = DatePicker;
const { TabPane } = Tabs;
const { Option } = Select;

/**
 * 采购仪表盘组件
 * 展示采购订单、供应商、成本等采购数据
 */
const PurchaseDashboard = () => {
  const navigate = useNavigate();
  
  // 状态定义
  const [loading, setLoading] = useState(true);
  const [dashboardData, setDashboardData] = useState(null);
  const [dateRange, setDateRange] = useState([dayjs().subtract(29, 'days'), dayjs()]);
  const [aiLoading, setAiLoading] = useState(false);
  const [feedbackLoading, setFeedbackLoading] = useState(false);
  const [suppliers, setSuppliers] = useState([]);
  const [selectedSupplier, setSelectedSupplier] = useState(null);

  // 初始化加载
  useEffect(() => {
    fetchSuppliers();
    fetchDashboardData();
  }, [dateRange, selectedSupplier]);

  // 获取供应商列表
  const fetchSuppliers = async () => {
    try {
      const response = await apiService.project.supplier.getList();
      setSuppliers(response.items || []);
    } catch (error) {
      console.error('获取供应商列表失败:', error);
    }
  };

  // 获取仪表盘数据
  const fetchDashboardData = async () => {
    setLoading(true);
    try {
      const params = {
        start_date: dateRange[0].format('YYYY-MM-DD'),
        end_date: dateRange[1].format('YYYY-MM-DD'),
        supplier_id: selectedSupplier
      };

      const response = await apiService.project.dashboard.getPurchaseDashboard(params);
      setDashboardData(response);
    } catch (error) {
      console.error('获取采购仪表盘数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 刷新AI推荐
  const refreshAIRecommendations = async () => {
    setAiLoading(true);
    try {
      const response = await apiService.project.dashboard.getAIRecommendations('purchase');
      if (response && response.recommendations) {
        setDashboardData(prev => ({
          ...prev,
          ai_recommendations: response.recommendations
        }));
      }
    } catch (error) {
      console.error('获取AI推荐失败:', error);
    } finally {
      setAiLoading(false);
    }
  };

  // 处理AI推荐反馈
  const handleRecommendationFeedback = async (recommendationId, isHelpful) => {
    setFeedbackLoading(true);
    try {
      await apiService.project.dashboard.submitAIFeedback({
        recommendation_id: recommendationId,
        is_helpful: isHelpful,
        feedback_type: 'purchase'
      });

      // 更新本地状态，标记反馈已提交
      setDashboardData(prev => ({
        ...prev,
        ai_recommendations: prev.ai_recommendations.map(item =>
          item.id === recommendationId
            ? { ...item, feedback_submitted: true, is_helpful: isHelpful }
            : item
        )
      }));

      message.success(isHelpful ? '感谢您的反馈！' : '感谢您的反馈，我们会继续改进');
    } catch (error) {
      console.error('提交AI推荐反馈失败:', error);
      message.error('提交反馈失败，请重试');
    } finally {
      setFeedbackLoading(false);
    }
  };

  // 处理日期范围变化
  const handleDateRangeChange = (dates) => {
    if (dates && dates.length === 2) {
      setDateRange(dates);
    }
  };

  // 处理供应商选择变化
  const handleSupplierChange = (value) => {
    setSelectedSupplier(value);
  };

  // 刷新数据
  const handleRefresh = () => {
    fetchDashboardData();
  };

  // 渲染采购趋势图表
  const renderPurchaseTrendChart = () => {
    if (!dashboardData || !dashboardData.purchase_trend || dashboardData.purchase_trend.length === 0) {
      return <Empty description="暂无采购趋势数据" />;
    }

    const config = {
      data: dashboardData.purchase_trend,
      xField: 'date',
      yField: 'value',
      seriesField: 'type',
      smooth: true,
      animation: {
        appear: {
          animation: 'path-in',
          duration: 1000
        }
      },
      xAxis: {
        title: {
          text: '日期'
        }
      },
      yAxis: {
        title: {
          text: '金额 (元)'
        }
      },
      tooltip: {
        formatter: (datum) => {
          return { name: '采购金额', value: `¥${datum.value.toFixed(2)}` };
        }
      }
    };

    return <Line {...config} height={300} />;
  };

  // 渲染类别分布图表
  const renderCategoryDistributionChart = () => {
    if (!dashboardData || !dashboardData.category_distribution || dashboardData.category_distribution.length === 0) {
      return <Empty description="暂无类别分布数据" />;
    }

    const config = {
      data: dashboardData.category_distribution,
      angleField: 'value',
      colorField: 'name',
      radius: 0.8,
      label: {
        type: 'outer',
        content: '{name}: {percentage}%'
      },
      legend: {
        position: 'bottom'
      },
      interactions: [{ type: 'element-active' }],
      tooltip: {
        formatter: (datum) => {
          return { name: datum.name, value: `¥${datum.value.toFixed(2)} (${datum.percentage.toFixed(1)}%)` };
        }
      },
      // 移除可能导致错误的配置
      appendPadding: 10,
      pieStyle: {
        lineWidth: 0
      }
    };

    return <Pie {...config} height={300} />;
  };

  // 渲染订单状态图表
  const renderOrderStatusChart = () => {
    if (!dashboardData || !dashboardData.order_status) {
      return <Empty description="暂无订单状态数据" />;
    }

    const { order_status } = dashboardData;
    const data = [
      { type: '草稿', value: order_status.draft },
      { type: '已确认', value: order_status.confirmed },
      { type: '已收货', value: order_status.received },
      { type: '已取消', value: order_status.cancelled }
    ];

    const config = {
      data,
      xField: 'type',
      yField: 'value',
      seriesField: 'type',
      color: ['#1890ff', '#52c41a', '#faad14', '#f5222d'],
      columnStyle: {
        radius: [4, 4, 0, 0],
      },
      label: {
        position: 'top',
        style: {
          fill: '#000000',
          opacity: 0.8,
        },
      },
      xAxis: {
        label: {
          autoHide: true,
          autoRotate: false,
        }
      },
      yAxis: {
        title: {
          text: '订单数量'
        }
      },
      tooltip: {
        formatter: (datum) => {
          return { name: datum.type, value: datum.value };
        }
      }
    };

    return <Column {...config} height={300} />;
  };

  // 渲染AI推荐
  const renderAIRecommendations = () => {
    if (!dashboardData || !dashboardData.ai_recommendations || dashboardData.ai_recommendations.length === 0) {
      return <Empty description="暂无AI推荐" />;
    }

    return (
      <List
        dataSource={dashboardData.ai_recommendations}
        renderItem={(item) => (
          <List.Item>
            <div style={{ width: '100%' }}>
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'flex-start',
                marginBottom: 12
              }}>
                <div>
                  <Text strong style={{ fontSize: 16, marginRight: 8 }}>{item.title}</Text>
                  <Tag color={
                    item.type === '业务优化' ? 'blue' :
                    item.type === '成本控制' ? 'orange' :
                    item.type === '供应商管理' ? 'green' :
                    item.type === '风险预警' ? 'red' : 'default'
                  }>
                    {item.type}
                  </Tag>
                  {item.priority && (
                    <Tag color={
                      item.priority === '高' ? 'red' :
                      item.priority === '中' ? 'orange' : 'green'
                    }>
                      {item.priority}优先级
                    </Tag>
                  )}
                </div>
                <div>
                  {item.feedback_submitted ? (
                    <span>
                      {item.is_helpful ?
                        <Tag icon={<LikeOutlined />} color="success">已标记为有帮助</Tag> :
                        <Tag icon={<DislikeOutlined />} color="default">已提交反馈</Tag>
                      }
                    </span>
                  ) : (
                    <Space>
                      <Button
                        type="primary"
                        size="small"
                        icon={<LikeOutlined />}
                        onClick={() => handleRecommendationFeedback && handleRecommendationFeedback(item.id, true)}
                      >
                        有帮助
                      </Button>
                      <Button
                        size="small"
                        icon={<DislikeOutlined />}
                        onClick={() => handleRecommendationFeedback && handleRecommendationFeedback(item.id, false)}
                      >
                        没有帮助
                      </Button>
                    </Space>
                  )}
                </div>
              </div>

              <Paragraph style={{ margin: '12px 0' }}>{item.content}</Paragraph>

              {item.action_items && item.action_items.length > 0 && (
                <div style={{
                  background: '#f5f5f5',
                  padding: '12px 16px',
                  borderRadius: '4px',
                  marginBottom: 12
                }}>
                  <Text strong>建议行动:</Text>
                  <ul style={{ marginTop: 8, marginBottom: 0, paddingLeft: 20 }}>
                    {item.action_items.map((action, index) => (
                      <li key={index}>{action}</li>
                    ))}
                  </ul>
                </div>
              )}

              {item.detailed_analysis && (
                <div style={{ marginTop: 12 }}>
                  <Divider style={{ margin: '12px 0' }} />
                  <Text strong>详细分析:</Text>
                  <Paragraph style={{ margin: '8px 0 0' }}>{item.detailed_analysis}</Paragraph>
                </div>
              )}
            </div>
          </List.Item>
        )}
        style={{ background: '#fff' }}
      />
    );
  };

  // 渲染供应商性能表格
  const renderSupplierPerformanceTable = () => {
    if (!dashboardData || !dashboardData.supplier_performance || dashboardData.supplier_performance.length === 0) {
      return <Empty description="暂无供应商性能数据" />;
    }

    const columns = [
      {
        title: '供应商名称',
        dataIndex: 'name',
        key: 'name',
      },
      {
        title: '订单数',
        dataIndex: 'total_orders',
        key: 'total_orders',
        sorter: (a, b) => a.total_orders - b.total_orders,
      },
      {
        title: '采购金额',
        dataIndex: 'total_amount',
        key: 'total_amount',
        render: (text) => `¥${text.toFixed(2)}`,
        sorter: (a, b) => a.total_amount - b.total_amount,
      },
      {
        title: '准时交付率',
        dataIndex: 'on_time_delivery_rate',
        key: 'on_time_delivery_rate',
        render: (text) => `${text}%`,
        sorter: (a, b) => a.on_time_delivery_rate - b.on_time_delivery_rate,
      },
      {
        title: '质量评分',
        dataIndex: 'quality_score',
        key: 'quality_score',
        render: (text) => text ? text.toFixed(1) : '-',
        sorter: (a, b) => (a.quality_score || 0) - (b.quality_score || 0),
      },
      {
        title: '平均交付时间',
        dataIndex: 'average_lead_time',
        key: 'average_lead_time',
        render: (text) => text ? `${text}天` : '-',
        sorter: (a, b) => (a.average_lead_time || 0) - (b.average_lead_time || 0),
      }
    ];

    return (
      <Table
        columns={columns}
        dataSource={dashboardData.supplier_performance}
        rowKey="id"
        pagination={{ pageSize: 5 }}
      />
    );
  };

  // 渲染最近订单表格
  const renderRecentOrdersTable = () => {
    if (!dashboardData || !dashboardData.recent_orders || dashboardData.recent_orders.length === 0) {
      return <Empty description="暂无最近订单数据" />;
    }

    const columns = [
      {
        title: '订单编号',
        dataIndex: 'order_number',
        key: 'order_number',
      },
      {
        title: '订单日期',
        dataIndex: 'order_date',
        key: 'order_date',
        render: (text) => dayjs(text).format('YYYY-MM-DD'),
      },
      {
        title: '供应商',
        dataIndex: 'supplier_name',
        key: 'supplier_name',
      },
      {
        title: '金额',
        dataIndex: 'total_amount',
        key: 'total_amount',
        render: (text) => `¥${text.toFixed(2)}`,
      },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        render: (text) => {
          let color = 'default';
          let label = text;

          if (text === 'draft') {
            color = 'default';
            label = '草稿';
          } else if (text === 'confirmed') {
            color = 'processing';
            label = '已确认';
          } else if (text === 'received') {
            color = 'success';
            label = '已收货';
          } else if (text === 'cancelled') {
            color = 'error';
            label = '已取消';
          }

          return <Tag color={color}>{label}</Tag>;
        },
      },
      {
        title: '支付状态',
        dataIndex: 'payment_status',
        key: 'payment_status',
        render: (text) => {
          let color = 'default';
          let label = text;

          if (text === 'unpaid') {
            color = 'warning';
            label = '未支付';
          } else if (text === 'partial') {
            color = 'processing';
            label = '部分支付';
          } else if (text === 'paid') {
            color = 'success';
            label = '已支付';
          }

          return <Tag color={color}>{label}</Tag>;
        },
      }
    ];

    return (
      <Table
        columns={columns}
        dataSource={dashboardData.recent_orders}
        rowKey="id"
        pagination={{ pageSize: 5 }}
      />
    );
  };

  return (
    <div className="purchase-dashboard">
      <Card>
        <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Title level={4} style={{ margin: 0 }}>采购仪表盘</Title>
          <Space>
            <Select
              placeholder="选择供应商"
              allowClear
              style={{ width: 200 }}
              onChange={handleSupplierChange}
              value={selectedSupplier}
            >
              {suppliers.map(supplier => (
                <Option key={supplier.id} value={supplier.id}>{supplier.name}</Option>
              ))}
            </Select>
            <RangePicker
              value={dateRange}
              onChange={handleDateRangeChange}
              allowClear={false}
            />
            <Button
              icon={<SyncOutlined />}
              onClick={handleRefresh}
              loading={loading}
            >
              刷新数据
            </Button>
          </Space>
        </div>

        <Spin spinning={loading}>
          {dashboardData ? (
            <>
              {/* 统计卡片 */}
              <Row gutter={[16, 16]}>
                <Col xs={24} sm={12} md={6}>
                  <Card>
                    <Statistic
                      title="本月采购额"
                      value={dashboardData.monthly_purchase.value}
                      precision={2}
                      prefix={<DollarOutlined />}
                      suffix="元"
                      valueStyle={{ color: '#3f8600' }}
                    />
                    <div className="statistic-footer">
                      {dashboardData.monthly_purchase.is_increase ? (
                        <Text type="success">
                          <ArrowUpOutlined /> {Math.abs(dashboardData.monthly_purchase.change).toFixed(1)}%
                        </Text>
                      ) : (
                        <Text type="danger">
                          <ArrowDownOutlined /> {Math.abs(dashboardData.monthly_purchase.change).toFixed(1)}%
                        </Text>
                      )}
                      <Text type="secondary" style={{ marginLeft: 8 }}>与上月相比</Text>
                    </div>
                  </Card>
                </Col>
                <Col xs={24} sm={12} md={6}>
                  <Card>
                    <Statistic
                      title="本月订单数"
                      value={dashboardData.monthly_orders.value}
                      prefix={<ShoppingCartOutlined />}
                      suffix="单"
                      valueStyle={{ color: '#1890ff' }}
                    />
                    <div className="statistic-footer">
                      {dashboardData.monthly_orders.is_increase ? (
                        <Text type="success">
                          <ArrowUpOutlined /> {Math.abs(dashboardData.monthly_orders.change).toFixed(1)}%
                        </Text>
                      ) : (
                        <Text type="danger">
                          <ArrowDownOutlined /> {Math.abs(dashboardData.monthly_orders.change).toFixed(1)}%
                        </Text>
                      )}
                      <Text type="secondary" style={{ marginLeft: 8 }}>与上月相比</Text>
                    </div>
                  </Card>
                </Col>
                <Col xs={24} sm={12} md={6}>
                  <Card>
                    <Statistic
                      title="平均订单金额"
                      value={dashboardData.average_order_value}
                      precision={2}
                      prefix={<DollarOutlined />}
                      suffix="元"
                      valueStyle={{ color: '#722ed1' }}
                    />
                  </Card>
                </Col>
                <Col xs={24} sm={12} md={6}>
                  <Card>
                    <Statistic
                      title="待处理订单"
                      value={dashboardData.pending_orders}
                      prefix={<FileTextOutlined />}
                      suffix="单"
                      valueStyle={{ color: '#fa8c16' }}
                    />
                  </Card>
                </Col>
              </Row>

              {/* 快捷入口 */}
              <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
                <Col xs={24} sm={8} md={6} lg={4}>
                  <Card
                    hoverable
                    onClick={() => navigate('/project/purchase/orders')}
                    style={{ textAlign: 'center', cursor: 'pointer' }}
                  >
                    <ShoppingCartOutlined style={{ fontSize: 24, color: '#1890ff', marginBottom: 8 }} />
                    <div>采购订单</div>
                  </Card>
                </Col>
                <Col xs={24} sm={8} md={6} lg={4}>
                  <Card
                    hoverable
                    onClick={() => navigate('/project/purchase/suppliers')}
                    style={{ textAlign: 'center', cursor: 'pointer' }}
                  >
                    <TeamOutlined style={{ fontSize: 24, color: '#52c41a', marginBottom: 8 }} />
                    <div>供应商管理</div>
                  </Card>
                </Col>
                <Col xs={24} sm={8} md={6} lg={4}>
                  <Card
                    hoverable
                    onClick={() => navigate('/project/purchase/orders?tab=report')}
                    style={{ textAlign: 'center', cursor: 'pointer' }}
                  >
                    <LineChartOutlined style={{ fontSize: 24, color: '#722ed1', marginBottom: 8 }} />
                    <div>采购报表</div>
                  </Card>
                </Col>
                <Col xs={24} sm={8} md={6} lg={4}>
                  <Card
                    hoverable
                    onClick={() => navigate('/project/ai/role-assistants?role=purchase_manager')}
                    style={{ textAlign: 'center', cursor: 'pointer' }}
                  >
                    <RobotOutlined style={{ fontSize: 24, color: '#f5222d', marginBottom: 8 }} />
                    <div>AI助手</div>
                  </Card>
                </Col>
              </Row>

              {/* 图表区域 */}
              <Tabs defaultActiveKey="1" style={{ marginTop: 16 }}>
                <TabPane
                  tab={<span><LineChartOutlined /> 采购趋势</span>}
                  key="1"
                >
                  <Card>
                    {renderPurchaseTrendChart()}
                  </Card>
                </TabPane>
                <TabPane
                  tab={<span><PieChartOutlined /> 类别分布</span>}
                  key="2"
                >
                  <Card>
                    {renderCategoryDistributionChart()}
                  </Card>
                </TabPane>
                <TabPane
                  tab={<span><FileTextOutlined /> 订单状态</span>}
                  key="3"
                >
                  <Card>
                    {renderOrderStatusChart()}
                  </Card>
                </TabPane>
                <TabPane
                  tab={<span><TeamOutlined /> 供应商性能</span>}
                  key="4"
                >
                  <Card>
                    {renderSupplierPerformanceTable()}
                  </Card>
                </TabPane>
                <TabPane
                  tab={<span><FileTextOutlined /> 最近订单</span>}
                  key="5"
                >
                  <Card>
                    {renderRecentOrdersTable()}
                  </Card>
                </TabPane>
              </Tabs>

              {/* AI推荐 */}
              <Card
                title={
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <span><RobotOutlined /> AI推荐</span>
                    <Button
                      icon={<SyncOutlined />}
                      onClick={refreshAIRecommendations}
                      loading={aiLoading}
                      size="small"
                    >
                      刷新AI推荐
                    </Button>
                  </div>
                }
                style={{ marginTop: 16 }}
              >
                {renderAIRecommendations()}
              </Card>
            </>
          ) : (
            <Empty description="暂无数据" />
          )}
        </Spin>
      </Card>
    </div>
  );
};

export default PurchaseDashboard;
