#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据库扩展初始化
"""

import logging
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession

# 配置日志
logger = logging.getLogger(__name__)

async def init_extensions(db: AsyncSession):
    """
    初始化数据库扩展
    """
    try:
        # 启用 pgvector 扩展
        await db.execute(text("CREATE EXTENSION IF NOT EXISTS vector"))
        await db.commit()
        
        logger.info("数据库扩展初始化成功")
        return True
    except Exception as e:
        await db.rollback()
        logger.error(f"数据库扩展初始化失败: {e}")
        raise 