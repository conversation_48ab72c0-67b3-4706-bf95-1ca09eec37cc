import api from '../httpClient';
import { getProjectId } from '../httpClient';

/**
 * 操作日志API
 */
const operationLogsApi = {
  /**
   * 获取操作日志列表
   * @param {Object} params - 查询参数
   * @returns {Promise} - 返回操作日志列表
   */
  getLogs: async (params = {}) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.get(`/project/${projectId}/operation-logs/logs`, { params });
  },

  /**
   * 获取操作日志模块列表
   * @returns {Promise} - 返回操作日志模块列表
   */
  getModules: async () => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.get(`/project/${projectId}/operation-logs/logs/modules`);
  },

  /**
   * 创建操作日志
   * @param {Object} data - 操作日志数据
   * @returns {Promise} - 返回创建结果
   */
  createLog: async (data) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.post(`/project/${projectId}/operation-logs/logs`, data);
  }
};

export default operationLogsApi;
