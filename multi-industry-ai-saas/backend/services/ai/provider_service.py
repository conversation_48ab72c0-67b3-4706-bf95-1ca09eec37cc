#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
AI 提供商服务
"""

import logging
import uuid
from typing import List, Optional, Dict, Any
from sqlalchemy import select, func
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import joinedload

from models.ai import AIProvider
from schemas.ai import AIProviderCreate, AIProviderUpdate

logger = logging.getLogger(__name__)

class AIProviderService:
    """AI 提供商服务"""

    @staticmethod
    async def get_providers(
        db: AsyncSession,
        skip: int = 0,
        limit: int = 100,
        status: Optional[str] = None,
    ) -> tuple[List[AIProvider], int]:
        """
        获取 AI 提供商列表
        """
        query = select(AIProvider)
        
        if status:
            query = query.where(AIProvider.status == status)
            
        # 获取总数
        count_query = select(func.count()).select_from(query.subquery())
        total = await db.scalar(count_query)
        
        # 获取分页数据
        query = query.offset(skip).limit(limit)
        result = await db.execute(query)
        providers = result.scalars().all()
        
        return providers, total

    @staticmethod
    async def get_provider(
        db: AsyncSession,
        provider_id: uuid.UUID,
    ) -> Optional[AIProvider]:
        """
        获取 AI 提供商详情
        """
        query = select(AIProvider).where(AIProvider.id == provider_id)
        result = await db.execute(query)
        return result.scalars().first()

    @staticmethod
    async def get_provider_by_name(
        db: AsyncSession,
        name: str,
    ) -> Optional[AIProvider]:
        """
        根据名称获取 AI 提供商
        """
        query = select(AIProvider).where(AIProvider.name == name)
        result = await db.execute(query)
        return result.scalars().first()

    @staticmethod
    async def create_provider(
        db: AsyncSession,
        provider_data: AIProviderCreate,
    ) -> AIProvider:
        """
        创建 AI 提供商
        """
        # 检查名称是否已存在
        existing_provider = await AIProviderService.get_provider_by_name(db, provider_data.name)
        if existing_provider:
            raise ValueError(f"提供商名称 '{provider_data.name}' 已存在")
            
        # 创建提供商
        provider = AIProvider(**provider_data.dict())
        db.add(provider)
        await db.commit()
        await db.refresh(provider)
        
        logger.info(f"创建 AI 提供商成功: {provider.id}")
        return provider

    @staticmethod
    async def update_provider(
        db: AsyncSession,
        provider_id: uuid.UUID,
        provider_data: AIProviderUpdate,
    ) -> Optional[AIProvider]:
        """
        更新 AI 提供商
        """
        # 获取提供商
        provider = await AIProviderService.get_provider(db, provider_id)
        if not provider:
            return None
            
        # 更新提供商
        update_data = provider_data.dict(exclude_unset=True)
        for key, value in update_data.items():
            setattr(provider, key, value)
            
        await db.commit()
        await db.refresh(provider)
        
        logger.info(f"更新 AI 提供商成功: {provider.id}")
        return provider

    @staticmethod
    async def delete_provider(
        db: AsyncSession,
        provider_id: uuid.UUID,
    ) -> bool:
        """
        删除 AI 提供商
        """
        # 获取提供商
        provider = await AIProviderService.get_provider(db, provider_id)
        if not provider:
            return False
            
        # 检查是否为内置提供商
        if provider.is_builtin:
            raise ValueError("内置提供商不能删除")
            
        # 删除提供商
        await db.delete(provider)
        await db.commit()
        
        logger.info(f"删除 AI 提供商成功: {provider.id}")
        return True

    @staticmethod
    async def init_builtin_providers(db: AsyncSession) -> None:
        """
        初始化内置 AI 提供商
        """
        builtin_providers = [
            {
                "name": "openai",
                "display_name": "OpenAI",
                "description": "OpenAI API 提供商",
                "api_base_url": "https://api.openai.com/v1",
                "auth_type": "api_key",
                "icon_url": "/static/images/ai/openai.png",
                "status": "active",
                "is_builtin": True,
                "config_schema": {
                    "api_key": {
                        "type": "string",
                        "title": "API Key",
                        "description": "OpenAI API Key",
                        "required": True
                    },
                    "organization_id": {
                        "type": "string",
                        "title": "Organization ID",
                        "description": "OpenAI Organization ID",
                        "required": False
                    }
                }
            },
            {
                "name": "azure",
                "display_name": "Azure OpenAI",
                "description": "Azure OpenAI API 提供商",
                "api_base_url": "https://{resource_name}.openai.azure.com/openai/deployments/{deployment_id}",
                "auth_type": "api_key",
                "icon_url": "/static/images/ai/azure.png",
                "status": "active",
                "is_builtin": True,
                "config_schema": {
                    "api_key": {
                        "type": "string",
                        "title": "API Key",
                        "description": "Azure OpenAI API Key",
                        "required": True
                    },
                    "resource_name": {
                        "type": "string",
                        "title": "Resource Name",
                        "description": "Azure OpenAI Resource Name",
                        "required": True
                    },
                    "deployment_id": {
                        "type": "string",
                        "title": "Deployment ID",
                        "description": "Azure OpenAI Deployment ID",
                        "required": True
                    },
                    "api_version": {
                        "type": "string",
                        "title": "API Version",
                        "description": "Azure OpenAI API Version",
                        "default": "2023-05-15",
                        "required": False
                    }
                }
            },
            {
                "name": "anthropic",
                "display_name": "Anthropic",
                "description": "Anthropic Claude API 提供商",
                "api_base_url": "https://api.anthropic.com/v1",
                "auth_type": "api_key",
                "icon_url": "/static/images/ai/anthropic.png",
                "status": "active",
                "is_builtin": True,
                "config_schema": {
                    "api_key": {
                        "type": "string",
                        "title": "API Key",
                        "description": "Anthropic API Key",
                        "required": True
                    }
                }
            }
        ]
        
        for provider_data in builtin_providers:
            # 检查是否已存在
            existing_provider = await AIProviderService.get_provider_by_name(db, provider_data["name"])
            if not existing_provider:
                # 创建提供商
                provider = AIProvider(**provider_data)
                db.add(provider)
                logger.info(f"创建内置 AI 提供商: {provider_data['name']}")
        
        await db.commit()
