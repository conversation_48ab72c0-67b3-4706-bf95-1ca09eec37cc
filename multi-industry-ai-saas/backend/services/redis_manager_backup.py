#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Redis管理服务
用于处理Redis连接、发布/订阅和任务调度
"""

import json
import logging
import asyncio
from typing import Dict, Any, Optional, List, Union
from datetime import datetime, timedelta
import uuid

import redis.asyncio as redis
from fastapi import WebSocket

from core.config import settings

# 配置日志
logger = logging.getLogger(__name__)

class RedisManager:
    """Redis管理服务，提供Redis连接和操作方法"""
    
    _instance = None
    _redis_client = None
    _pubsub = None
    _active_subscriptions = {}  # 活跃的订阅
    _websocket_connections = {}  # WebSocket连接
    
    @classmethod
    async def get_instance(cls):
        """获取RedisManager单例实例"""
        if cls._instance is None:
            cls._instance = RedisManager()
            await cls._connect()
        return cls._instance
    
    @classmethod
    async def _connect(cls):
        """连接到Redis服务器"""
        try:
            redis_url = f"redis://{settings.REDIS_HOST}:{settings.REDIS_PORT}/{settings.REDIS_DB}"
            logger.info(f"正在连接到Redis: {redis_url}")
            
            cls._redis_client = redis.Redis.from_url(
                redis_url,
                encoding="utf-8",
                decode_responses=True
            )
            
            # 测试连接
            await cls._redis_client.ping()
            logger.info("Redis连接成功")
            
            # 初始化PubSub对象
            cls._pubsub = cls._redis_client.pubsub()
            
        except Exception as e:
            logger.error(f"Redis连接失败: {str(e)}")
            raise
    
    @classmethod
    async def publish_notification(cls, channel: str, notification: Dict[str, Any]):
        """
        发布通知到指定通道
        
        Args:
            channel: 通道名称
            notification: 通知数据
        """
        if not cls._redis_client:
            await cls._connect()
        
        try:
            # 确保通知数据可以被序列化
            if isinstance(notification.get('id'), uuid.UUID):
                notification['id'] = str(notification['id'])
            if isinstance(notification.get('user_id'), uuid.UUID):
                notification['user_id'] = str(notification['user_id'])
            if isinstance(notification.get('project_id'), uuid.UUID):
                notification['project_id'] = str(notification['project_id'])
            if isinstance(notification.get('created_at'), datetime):
                notification['created_at'] = notification['created_at'].isoformat()
            
            # 发布通知
            message = json.dumps(notification)
            await cls._redis_client.publish(channel, message)
            logger.debug(f"通知已发布到通道 {channel}")
            
        except Exception as e:
            logger.error(f"发布通知失败: {str(e)}")
            raise
    
    @classmethod
    async def schedule_task(cls, key: str, task_id: str, execution_time: int):
        """
        调度任务到Redis有序集合
        
        Args:
            key: Redis键名
            task_id: 任务ID
            execution_time: 执行时间戳
        """
        if not cls._redis_client:
            await cls._connect()
        
        try:
            await cls._redis_client.zadd(key, {task_id: execution_time})
            logger.debug(f"任务 {task_id} 已调度到 {key}, 执行时间: {execution_time}")
            return True
        except Exception as e:
            logger.error(f"调度任务失败: {str(e)}")
            raise
    
    @classmethod
    async def get_due_tasks(cls, key: str, max_time: int = None):
        """
        获取到期任务
        
        Args:
            key: Redis键名
            max_time: 最大时间戳，默认为当前时间
            
        Returns:
            到期任务ID列表
        """
        if not cls._redis_client:
            await cls._connect()
        
        try:
            if max_time is None:
                max_time = int(datetime.now().timestamp())
            
            # 获取分数小于等于max_time的所有成员
            tasks = await cls._redis_client.zrangebyscore(key, 0, max_time)
            
            if tasks:
                # 从有序集合中移除这些任务
                await cls._redis_client.zremrangebyscore(key, 0, max_time)
            
            return tasks
        except Exception as e:
            logger.error(f"获取到期任务失败: {str(e)}")
            raise
    
    @classmethod
    async def register_websocket(cls, user_id: str, project_id: str, websocket: WebSocket):
        """
        注册WebSocket连接
        
        Args:
            user_id: 用户ID
            project_id: 项目ID
            websocket: WebSocket连接
        """
        connection_id = f"{user_id}:{project_id}"
        cls._websocket_connections[connection_id] = websocket
        
        # 订阅用户和项目通道
        await cls.subscribe_to_channels([
            f"user:{user_id}:notifications",
            f"project:{project_id}:notifications"
        ], connection_id)
        
        logger.info(f"WebSocket连接已注册: {connection_id}")
    
    @classmethod
    async def unregister_websocket(cls, user_id: str, project_id: str):
        """
        注销WebSocket连接
        
        Args:
            user_id: 用户ID
            project_id: 项目ID
        """
        connection_id = f"{user_id}:{project_id}"
        
        # 取消订阅
        if connection_id in cls._active_subscriptions:
            channels = cls._active_subscriptions[connection_id]
            for channel in channels:
                await cls._pubsub.unsubscribe(channel)
            del cls._active_subscriptions[connection_id]
        
        # 移除WebSocket连接
        if connection_id in cls._websocket_connections:
            del cls._websocket_connections[connection_id]
        
        logger.info(f"WebSocket连接已注销: {connection_id}")
    
    @classmethod
    async def subscribe_to_channels(cls, channels: List[str], connection_id: str):
        """
        订阅通道
        
        Args:
            channels: 通道列表
            connection_id: 连接ID
        """
        if not cls._pubsub:
            if not cls._redis_client:
                await cls._connect()
            cls._pubsub = cls._redis_client.pubsub()
        
        # 记录活跃订阅
        cls._active_subscriptions[connection_id] = channels
        
        # 订阅通道
        for channel in channels:
            await cls._pubsub.subscribe(channel)
        
        # 启动消息处理任务
        asyncio.create_task(cls._process_messages(connection_id))
    
    @classmethod
    async def _process_messages(cls, connection_id: str):
        """
        处理订阅消息
        
        Args:
            connection_id: 连接ID
        """
        try:
            while True:
                message = await cls._pubsub.get_message(ignore_subscribe_messages=True)
                if message and message['type'] == 'message':
                    channel = message['channel']
                    data = message['data']
                    
                    # 检查连接是否仍然活跃
                    if connection_id not in cls._websocket_connections:
                        break
                    
                    # 发送消息到WebSocket
                    websocket = cls._websocket_connections[connection_id]
                    await websocket.send_text(data)
                    
                # 短暂休眠，避免CPU占用过高
                await asyncio.sleep(0.01)
        except Exception as e:
            logger.error(f"处理消息失败: {str(e)}")
            # 出错时取消订阅
            if connection_id in cls._active_subscriptions:
                channels = cls._active_subscriptions[connection_id]
                for channel in channels:
                    await cls._pubsub.unsubscribe(channel)
                del cls._active_subscriptions[connection_id]
