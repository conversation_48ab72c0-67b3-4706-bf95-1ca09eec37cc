#!/usr/bin/env python
# -*- coding: utf-8 -*-

import logging
from typing import List, Dict, Any, Optional, Union
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import and_, or_, not_, update, delete
from fastapi import HTTPException, status
import uuid

from models.tenant_data_sharing import TenantDataSharing, TenantDataSharingLog
from models.tenant import Tenant
from models.user import User
from core.tenant_context import get_tenant_context

# 配置日志
logger = logging.getLogger(__name__)

class TenantDataSharingService:
    """租户数据共享服务"""

    @staticmethod
    async def create_data_sharing(
        db: AsyncSession,
        source_tenant_id: uuid.UUID,
        target_tenant_id: uuid.UUID,
        data_type: str,
        data_id: uuid.UUID,
        access_level: str = "read",
        expiry_days: Optional[int] = None,
        metadata: Optional[Dict[str, Any]] = None,
        created_by: uuid.UUID = None
    ) -> Dict[str, Any]:
        """
        创建租户数据共享

        Args:
            db: 数据库会话
            source_tenant_id: 源租户ID
            target_tenant_id: 目标租户ID
            data_type: 数据类型
            data_id: 数据ID
            access_level: 访问级别，默认为"read"
            expiry_days: 过期天数，为None表示永不过期
            metadata: 元数据
            created_by: 创建者ID

        Returns:
            创建的数据共享记录
        """
        try:
            # 验证源租户和目标租户
            source_tenant_query = select(Tenant).where(Tenant.id == source_tenant_id)
            source_tenant_result = await db.execute(source_tenant_query)
            source_tenant = source_tenant_result.scalar_one_or_none()

            if not source_tenant:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"源租户 {source_tenant_id} 不存在"
                )

            target_tenant_query = select(Tenant).where(Tenant.id == target_tenant_id)
            target_tenant_result = await db.execute(target_tenant_query)
            target_tenant = target_tenant_result.scalar_one_or_none()

            if not target_tenant:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"目标租户 {target_tenant_id} 不存在"
                )

            # 检查是否已存在相同的共享
            existing_query = select(TenantDataSharing).where(
                and_(
                    TenantDataSharing.source_tenant_id == source_tenant_id,
                    TenantDataSharing.target_tenant_id == target_tenant_id,
                    TenantDataSharing.data_type == data_type,
                    TenantDataSharing.data_id == data_id
                )
            )
            existing_result = await db.execute(existing_query)
            existing_sharing = existing_result.scalar_one_or_none()

            if existing_sharing:
                # 如果已存在且状态为active，则返回错误
                if existing_sharing.status == "active":
                    raise HTTPException(
                        status_code=status.HTTP_409_CONFLICT,
                        detail=f"数据已经被共享给该租户"
                    )
                # 如果已存在但状态不是active，则更新状态
                existing_sharing.status = "active"
                existing_sharing.access_level = access_level
                existing_sharing.updated_at = datetime.now()
                existing_sharing.updated_by = created_by

                if expiry_days is not None:
                    existing_sharing.expiry_date = datetime.now() + timedelta(days=expiry_days)
                else:
                    existing_sharing.expiry_date = None

                if metadata:
                    existing_sharing.sharing_metadata = metadata

                await db.commit()

                # 记录日志
                sharing_log = TenantDataSharingLog(
                    sharing_id=existing_sharing.id,
                    action="update",
                    action_by=created_by,
                    details={
                        "access_level": access_level,
                        "expiry_days": expiry_days,
                        "metadata": metadata
                    }
                )
                db.add(sharing_log)
                await db.commit()

                return {
                    "id": str(existing_sharing.id),
                    "source_tenant_id": str(source_tenant_id),
                    "target_tenant_id": str(target_tenant_id),
                    "data_type": data_type,
                    "data_id": str(data_id),
                    "status": "active",
                    "access_level": access_level,
                    "expiry_date": existing_sharing.expiry_date.isoformat() if existing_sharing.expiry_date else None,
                    "created_at": existing_sharing.created_at.isoformat(),
                    "updated_at": existing_sharing.updated_at.isoformat(),
                    "metadata": existing_sharing.sharing_metadata
                }

            # 计算过期时间
            expiry_date = None
            if expiry_days is not None:
                expiry_date = datetime.now() + timedelta(days=expiry_days)

            # 创建新的共享记录
            sharing_id = uuid.uuid4()
            new_sharing = TenantDataSharing(
                id=sharing_id,
                source_tenant_id=source_tenant_id,
                target_tenant_id=target_tenant_id,
                data_type=data_type,
                data_id=data_id,
                status="active",
                access_level=access_level,
                expiry_date=expiry_date,
                created_by=created_by,
                sharing_metadata=metadata or {}
            )

            db.add(new_sharing)

            # 记录日志
            sharing_log = TenantDataSharingLog(
                sharing_id=sharing_id,
                action="create",
                action_by=created_by,
                details={
                    "access_level": access_level,
                    "expiry_days": expiry_days,
                    "metadata": metadata
                }
            )
            db.add(sharing_log)

            await db.commit()

            return {
                "id": str(sharing_id),
                "source_tenant_id": str(source_tenant_id),
                "target_tenant_id": str(target_tenant_id),
                "data_type": data_type,
                "data_id": str(data_id),
                "status": "active",
                "access_level": access_level,
                "expiry_date": expiry_date.isoformat() if expiry_date else None,
                "created_at": datetime.now().isoformat(),
                "updated_at": datetime.now().isoformat(),
                "metadata": metadata or {}
            }

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"创建租户数据共享失败: {e}")
            await db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"创建租户数据共享失败: {str(e)}"
            )

    @staticmethod
    async def get_data_sharing(
        db: AsyncSession,
        sharing_id: uuid.UUID
    ) -> Dict[str, Any]:
        """
        获取租户数据共享详情

        Args:
            db: 数据库会话
            sharing_id: 共享ID

        Returns:
            数据共享记录
        """
        try:
            # 查询共享记录
            query = select(TenantDataSharing).where(TenantDataSharing.id == sharing_id)
            result = await db.execute(query)
            sharing = result.scalar_one_or_none()

            if not sharing:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"数据共享 {sharing_id} 不存在"
                )

            # 检查是否已过期
            if sharing.expiry_date and sharing.expiry_date < datetime.now():
                sharing.status = "inactive"
                await db.commit()

            return {
                "id": str(sharing.id),
                "source_tenant_id": str(sharing.source_tenant_id),
                "target_tenant_id": str(sharing.target_tenant_id),
                "data_type": sharing.data_type,
                "data_id": str(sharing.data_id),
                "status": sharing.status,
                "access_level": sharing.access_level,
                "expiry_date": sharing.expiry_date.isoformat() if sharing.expiry_date else None,
                "created_at": sharing.created_at.isoformat() if sharing.created_at else None,
                "updated_at": sharing.updated_at.isoformat() if sharing.updated_at else None,
                "metadata": sharing.metadata
            }

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"获取租户数据共享失败: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"获取租户数据共享失败: {str(e)}"
            )

    @staticmethod
    async def list_shared_data(
        db: AsyncSession,
        tenant_id: uuid.UUID,
        data_type: Optional[str] = None,
        status: Optional[str] = "active",
        as_source: bool = True,
        skip: int = 0,
        limit: int = 100
    ) -> Dict[str, Any]:
        """
        列出租户共享的数据

        Args:
            db: 数据库会话
            tenant_id: 租户ID
            data_type: 数据类型过滤
            status: 状态过滤
            as_source: 是否作为源租户查询，False表示作为目标租户查询
            skip: 跳过记录数
            limit: 返回记录数

        Returns:
            共享数据列表
        """
        try:
            # 构建查询条件
            conditions = []

            if as_source:
                conditions.append(TenantDataSharing.source_tenant_id == tenant_id)
            else:
                conditions.append(TenantDataSharing.target_tenant_id == tenant_id)

            if data_type:
                conditions.append(TenantDataSharing.data_type == data_type)

            if status:
                conditions.append(TenantDataSharing.status == status)

            # 查询总数
            count_query = select(TenantDataSharing).where(and_(*conditions))
            count_result = await db.execute(count_query)
            total = len(count_result.scalars().all())

            # 查询分页数据
            query = select(TenantDataSharing).where(and_(*conditions)).offset(skip).limit(limit)
            result = await db.execute(query)
            sharings = result.scalars().all()

            # 检查是否有过期的共享
            now = datetime.now()
            items = []
            for sharing in sharings:
                # 如果已过期但状态仍为active，则更新状态
                if sharing.expiry_date and sharing.expiry_date < now and sharing.status == "active":
                    sharing.status = "inactive"
                    await db.commit()

                items.append({
                    "id": str(sharing.id),
                    "source_tenant_id": str(sharing.source_tenant_id),
                    "target_tenant_id": str(sharing.target_tenant_id),
                    "data_type": sharing.data_type,
                    "data_id": str(sharing.data_id),
                    "status": sharing.status,
                    "access_level": sharing.access_level,
                    "expiry_date": sharing.expiry_date.isoformat() if sharing.expiry_date else None,
                    "created_at": sharing.created_at.isoformat() if sharing.created_at else None,
                    "updated_at": sharing.updated_at.isoformat() if sharing.updated_at else None,
                    "metadata": sharing.metadata
                })

            return {
                "items": items,
                "total": total,
                "skip": skip,
                "limit": limit
            }

        except Exception as e:
            logger.error(f"列出租户共享数据失败: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"列出租户共享数据失败: {str(e)}"
            )

    @staticmethod
    async def revoke_data_sharing(
        db: AsyncSession,
        sharing_id: uuid.UUID,
        user_id: uuid.UUID
    ) -> Dict[str, Any]:
        """
        撤销租户数据共享

        Args:
            db: 数据库会话
            sharing_id: 共享ID
            user_id: 操作用户ID

        Returns:
            撤销结果
        """
        try:
            # 查询共享记录
            query = select(TenantDataSharing).where(TenantDataSharing.id == sharing_id)
            result = await db.execute(query)
            sharing = result.scalar_one_or_none()

            if not sharing:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"数据共享 {sharing_id} 不存在"
                )

            # 更新状态
            sharing.status = "revoked"
            sharing.updated_at = datetime.now()
            sharing.updated_by = user_id

            # 记录日志
            sharing_log = TenantDataSharingLog(
                sharing_id=sharing_id,
                action="revoke",
                action_by=user_id,
                details={
                    "previous_status": sharing.status
                }
            )
            db.add(sharing_log)

            await db.commit()

            return {
                "success": True,
                "message": "数据共享已撤销",
                "data": {
                    "id": str(sharing.id),
                    "status": "revoked",
                    "updated_at": datetime.now().isoformat()
                }
            }

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"撤销租户数据共享失败: {e}")
            await db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"撤销租户数据共享失败: {str(e)}"
            )

    @staticmethod
    async def check_data_access(
        db: AsyncSession,
        tenant_id: uuid.UUID,
        data_type: str,
        data_id: uuid.UUID,
        required_access_level: str = "read",
        user_id: Optional[uuid.UUID] = None
    ) -> Dict[str, Any]:
        """
        检查租户是否有权限访问数据

        Args:
            db: 数据库会话
            tenant_id: 租户ID
            data_type: 数据类型
            data_id: 数据ID
            required_access_level: 所需访问级别
            user_id: 用户ID，用于记录访问日志

        Returns:
            访问权限检查结果
        """
        try:
            # 获取租户上下文
            tenant_context = get_tenant_context()

            # 如果是数据所有者，直接返回有权限
            if tenant_context and tenant_context.tenant_id == tenant_id:
                return {
                    "has_access": True,
                    "access_level": "owner",
                    "source": "owner"
                }

            # 查询共享记录
            query = select(TenantDataSharing).where(
                and_(
                    TenantDataSharing.source_tenant_id == tenant_id,
                    TenantDataSharing.target_tenant_id == tenant_context.tenant_id if tenant_context else None,
                    TenantDataSharing.data_type == data_type,
                    TenantDataSharing.data_id == data_id,
                    TenantDataSharing.status == "active"
                )
            )
            result = await db.execute(query)
            sharing = result.scalar_one_or_none()

            if not sharing:
                return {
                    "has_access": False,
                    "message": "无权访问该数据"
                }

            # 检查是否已过期
            if sharing.expiry_date and sharing.expiry_date < datetime.now():
                sharing.status = "inactive"
                await db.commit()
                return {
                    "has_access": False,
                    "message": "数据共享已过期"
                }

            # 检查访问级别
            access_levels = {
                "read": 1,
                "write": 2,
                "admin": 3
            }

            required_level = access_levels.get(required_access_level, 0)
            actual_level = access_levels.get(sharing.access_level, 0)

            if actual_level < required_level:
                return {
                    "has_access": False,
                    "message": f"权限不足，需要 {required_access_level} 权限"
                }

            # 记录访问日志
            if user_id:
                sharing_log = TenantDataSharingLog(
                    sharing_id=sharing.id,
                    action="access",
                    action_by=user_id,
                    details={
                        "access_level": required_access_level
                    }
                )
                db.add(sharing_log)
                await db.commit()

            return {
                "has_access": True,
                "access_level": sharing.access_level,
                "source": "sharing",
                "sharing_id": str(sharing.id)
            }

        except Exception as e:
            logger.error(f"检查数据访问权限失败: {e}")
            return {
                "has_access": False,
                "message": f"检查数据访问权限失败: {str(e)}"
            }
