import request from '../../../services/request';
import { getProjectId } from '../httpClient';

/**
 * 业务设置API
 */
const businessSettingsApi = {
  /**
   * 获取工作流列表
   * @param {Object} params - 查询参数
   * @returns {Promise} - 返回工作流列表
   */
  getWorkflows: async (params = {}) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return request.get(`/project/${projectId}/workflows`, { params });
  },

  /**
   * 获取工作流详情
   * @param {string} id - 工作流ID
   * @returns {Promise} - 返回工作流详情
   */
  getWorkflow: async (id) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return request.get(`/project/${projectId}/workflows/${id}`);
  },

  /**
   * 获取工作流详情（别名）
   * @param {string} id - 工作流ID
   * @returns {Promise} - 返回工作流详情
   */
  getWorkflowDetail: async (id) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return request.get(`/project/${projectId}/workflows/${id}`);
  },

  /**
   * 创建工作流
   * @param {Object} data - 工作流数据
   * @returns {Promise} - 返回创建结果
   */
  createWorkflow: async (data) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return request.post(`/project/${projectId}/workflows`, data);
  },

  /**
   * 更新工作流
   * @param {string} id - 工作流ID
   * @param {Object} data - 工作流数据
   * @returns {Promise} - 返回更新结果
   */
  updateWorkflow: async (id, data) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return request.put(`/project/${projectId}/workflows/${id}`, data);
  },

  /**
   * 获取空间设置
   * @returns {Promise} - 返回空间设置
   */
  getSpaceSettings: async () => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return request.get(`/project/${projectId}/business-settings/space-settings`);
  },

  /**
   * 更新空间设置
   * @param {Object} data - 空间设置数据
   * @returns {Promise} - 返回更新结果
   */
  updateSpaceSettings: async (data) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return request.put(`/project/${projectId}/business-settings/space-settings`, data);
  },

  /**
   * 创建模块空间设置
   * @param {Object} data - 模块空间设置数据
   * @returns {Promise} - 返回创建结果
   */
  createModuleSpaceSettings: async (data) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return request.post(`/project/${projectId}/business-settings/space-settings/modules`, data);
  },

  /**
   * 更新模块空间设置
   * @param {string} id - 模块空间设置ID
   * @param {Object} data - 模块空间设置数据
   * @returns {Promise} - 返回更新结果
   */
  updateModuleSpaceSettings: async (id, data) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return request.put(`/project/${projectId}/business-settings/space-settings/modules/${id}`, data);
  },

  /**
   * 删除模块空间设置
   * @param {string} id - 模块空间设置ID
   * @returns {Promise} - 返回删除结果
   */
  deleteModuleSpaceSettings: async (id) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return request.delete(`/project/${projectId}/business-settings/space-settings/modules/${id}`);
  }
};

export default businessSettingsApi;
