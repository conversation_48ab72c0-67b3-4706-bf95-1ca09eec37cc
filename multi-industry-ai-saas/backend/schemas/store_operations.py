#!/usr/bin/env python
# -*- coding: utf-8 -*-

from typing import List, Optional, Dict, Any, Union
from pydantic import BaseModel, UUID4, Field
from datetime import datetime

# 到货确认明细基础模型
class ArrivalConfirmationItemBase(BaseModel):
    product_id: Optional[UUID4] = None
    product_name: str
    product_code: Optional[str] = None
    specification: Optional[str] = None
    unit: Optional[str] = None
    expected_quantity: int
    actual_quantity: int
    price: float
    amount: float
    is_confirmed: bool = True
    notes: Optional[str] = None

# 创建到货确认明细请求模型
class ArrivalConfirmationItemCreate(ArrivalConfirmationItemBase):
    pass

# 更新到货确认明细请求模型
class ArrivalConfirmationItemUpdate(BaseModel):
    actual_quantity: Optional[int] = None
    is_confirmed: Optional[bool] = None
    notes: Optional[str] = None

# 到货确认基础模型
class ArrivalConfirmationBase(BaseModel):
    store_id: UUID4
    arrival_type: str  # market, warehouse, third_party
    supplier_id: Optional[UUID4] = None
    warehouse_id: Optional[UUID4] = None
    purchase_order_id: Optional[UUID4] = None
    arrival_date: datetime
    notes: Optional[str] = None

# 创建到货确认请求模型
class ArrivalConfirmationCreate(ArrivalConfirmationBase):
    items: List[ArrivalConfirmationItemCreate]

# 更新到货确认请求模型
class ArrivalConfirmationUpdate(BaseModel):
    status: Optional[str] = None  # pending, confirmed, rejected
    notes: Optional[str] = None

# 确认到货请求模型
class ArrivalConfirmationConfirm(BaseModel):
    items: List[Dict[str, Any]]  # 包含 id, actual_quantity, is_confirmed 的字典列表
    notes: Optional[str] = None

# 数据库到货确认明细模型
class ArrivalConfirmationItemInDB(ArrivalConfirmationItemBase):
    id: UUID4
    arrival_id: UUID4
    
    class Config:
        model_config = {"from_attributes": True}

# 数据库到货确认模型
class ArrivalConfirmationInDB(BaseModel):
    id: UUID4
    tenant_id: Optional[UUID4] = None
    project_id: UUID4
    store_id: UUID4
    arrival_number: str
    arrival_type: str
    supplier_id: Optional[UUID4] = None
    warehouse_id: Optional[UUID4] = None
    purchase_order_id: Optional[UUID4] = None
    arrival_date: datetime
    status: str
    total_amount: float
    total_items: int
    notes: Optional[str] = None
    created_by: Optional[UUID4] = None
    updated_by: Optional[UUID4] = None
    created_at: datetime
    updated_at: datetime
    
    # 非数据库字段，用于前端展示
    store_name: Optional[str] = None
    supplier_name: Optional[str] = None
    warehouse_name: Optional[str] = None
    purchase_order_number: Optional[str] = None
    items: List[ArrivalConfirmationItemInDB] = []
    
    class Config:
        model_config = {"from_attributes": True}

# 到货确认响应模型
class ArrivalConfirmationResponse(BaseModel):
    success: bool
    message: Optional[str] = None
    data: Optional[ArrivalConfirmationInDB] = None

# 到货确认列表响应模型
class ArrivalConfirmationListResponse(BaseModel):
    success: bool
    message: Optional[str] = None
    items: List[ArrivalConfirmationInDB]
    total: int
    page: Optional[int] = None
    page_size: Optional[int] = None
    pages: Optional[int] = None
    statistics: Optional[Dict[str, int]] = None  # 添加统计信息字段

# 到货成本汇总响应模型
class ArrivalCostSummaryResponse(BaseModel):
    success: bool
    message: Optional[str] = None
    data: Dict[str, Any]

# 日常费用基础模型
class DailyExpenseBase(BaseModel):
    store_id: UUID4
    expense_type: str  # misc, material, electricity, internet, other
    expense_date: datetime
    amount: float
    description: Optional[str] = None

# 创建日常费用请求模型
class DailyExpenseCreate(DailyExpenseBase):
    receipt_url: Optional[str] = None
    has_receipt: bool = False

# 更新日常费用请求模型
class DailyExpenseUpdate(BaseModel):
    expense_type: Optional[str] = None
    expense_date: Optional[datetime] = None
    amount: Optional[float] = None
    description: Optional[str] = None
    receipt_url: Optional[str] = None
    has_receipt: Optional[bool] = None

# 数据库日常费用模型
class DailyExpenseInDB(BaseModel):
    id: UUID4
    tenant_id: Optional[UUID4] = None
    project_id: UUID4
    store_id: UUID4
    expense_number: str
    expense_type: str
    expense_date: datetime
    amount: float
    description: Optional[str] = None
    receipt_url: Optional[str] = None
    has_receipt: bool
    created_by: Optional[UUID4] = None
    updated_by: Optional[UUID4] = None
    created_at: datetime
    updated_at: datetime
    
    # 非数据库字段，用于前端展示
    store_name: Optional[str] = None
    
    class Config:
        model_config = {"from_attributes": True}

# 日常费用响应模型
class DailyExpenseResponse(BaseModel):
    success: bool
    message: Optional[str] = None
    data: Optional[DailyExpenseInDB] = None

# 日常费用列表响应模型
class DailyExpenseListResponse(BaseModel):
    success: bool
    message: Optional[str] = None
    items: List[DailyExpenseInDB]
    total: int
    page: Optional[int] = None
    page_size: Optional[int] = None
    pages: Optional[int] = None

# 费用统计响应模型
class ExpenseStatisticsResponse(BaseModel):
    success: bool
    message: Optional[str] = None
    data: Dict[str, Any]

# 门店销售目标模型
class StoreSalesTargetBase(BaseModel):
    store_id: UUID4
    target_year: int = Field(..., gt=2020, lt=2100)
    target_month: int = Field(..., ge=1, le=12)
    last_year_sales: Optional[float] = Field(None, ge=0)
    growth_rate: Optional[float] = Field(None, ge=0)
    monthly_target: float = Field(..., ge=0)
    daily_target: Optional[float] = Field(None, ge=0)

class StoreSalesTargetCreate(StoreSalesTargetBase):
    pass

class StoreSalesTargetUpdate(BaseModel):
    last_year_sales: Optional[float] = Field(None, ge=0)
    growth_rate: Optional[float] = Field(None, ge=0)
    monthly_target: Optional[float] = Field(None, ge=0)
    daily_target: Optional[float] = Field(None, ge=0)

class StoreSalesTargetInDB(StoreSalesTargetBase):
    id: UUID4
    project_id: UUID4
    created_at: datetime
    updated_at: datetime
    
    # 用于前端展示
    store_name: Optional[str] = None

    class Config:
        model_config = {"from_attributes": True}

class StoreSalesTargetResponse(BaseModel):
    success: bool
    message: Optional[str] = None
    data: Optional[StoreSalesTargetInDB] = None

class StoreSalesTargetListResponse(BaseModel):
    success: bool
    message: Optional[str] = None
    items: List[StoreSalesTargetInDB]
    total: int
