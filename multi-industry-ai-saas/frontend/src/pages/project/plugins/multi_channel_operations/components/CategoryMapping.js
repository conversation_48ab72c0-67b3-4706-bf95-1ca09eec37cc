import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Modal,
  Form,
  Input,
  Select,
  Tree,
  message,
  Popconfirm,
  Typography,
  Row,
  Col,
  Divider,
  Alert,
  Tooltip,
  Badge
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SyncOutlined,
  LinkOutlined,
  UnlinkOutlined,
  BranchesOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import { useParams } from 'react-router-dom';
import multiChannelService from '../services/multiChannelService';

const { Title, Text } = Typography;
const { Option } = Select;

const CategoryMapping = () => {
  const { projectId } = useParams();
  const [loading, setLoading] = useState(false);
  const [mappings, setMappings] = useState([]);
  const [channels, setChannels] = useState([]);
  const [localCategories, setLocalCategories] = useState([]);
  const [platformCategories, setPlatformCategories] = useState({});
  const [modalVisible, setModalVisible] = useState(false);
  const [editingMapping, setEditingMapping] = useState(null);
  const [selectedChannel, setSelectedChannel] = useState(null);
  const [form] = Form.useForm();

  useEffect(() => {
    loadData();
  }, [projectId]);

  const loadData = async () => {
    try {
      setLoading(true);
      await Promise.all([
        loadMappings(),
        loadChannels(),
        loadLocalCategories()
      ]);
    } catch (error) {
      console.error('加载数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadMappings = async () => {
    try {
      const response = await multiChannelService.getCategoryMappings(projectId);
      setMappings(response.data || []);
    } catch (error) {
      console.error('获取类目映射失败:', error);
    }
  };

  const loadChannels = async () => {
    try {
      const response = await multiChannelService.getChannels(projectId);
      setChannels(response.data || []);
    } catch (error) {
      console.error('获取渠道列表失败:', error);
    }
  };

  const loadLocalCategories = async () => {
    try {
      const response = await multiChannelService.getLocalCategories(projectId);
      setLocalCategories(response.data || []);
    } catch (error) {
      console.error('获取本地类目失败:', error);
    }
  };

  const loadPlatformCategories = async (channelId) => {
    try {
      const response = await multiChannelService.getPlatformCategories(projectId, channelId);
      setPlatformCategories(prev => ({
        ...prev,
        [channelId]: response.data || []
      }));
    } catch (error) {
      console.error('获取平台类目失败:', error);
    }
  };

  const handleCreateMapping = () => {
    setEditingMapping(null);
    setModalVisible(true);
    form.resetFields();
  };

  const handleEditMapping = (record) => {
    setEditingMapping(record);
    setModalVisible(true);
    form.setFieldsValue({
      local_category_id: record.local_category_id,
      channel_id: record.channel_id,
      platform_category_id: record.platform_category_id,
      mapping_rules: record.mapping_rules
    });
    if (record.channel_id) {
      loadPlatformCategories(record.channel_id);
    }
  };

  const handleDeleteMapping = async (id) => {
    try {
      await multiChannelService.deleteCategoryMapping(projectId, id);
      message.success('删除成功');
      loadMappings();
    } catch (error) {
      console.error('删除失败:', error);
      message.error('删除失败');
    }
  };

  const handleSaveMapping = async (values) => {
    try {
      if (editingMapping) {
        await multiChannelService.updateCategoryMapping(projectId, editingMapping.id, values);
        message.success('更新成功');
      } else {
        await multiChannelService.createCategoryMapping(projectId, values);
        message.success('创建成功');
      }
      setModalVisible(false);
      loadMappings();
    } catch (error) {
      console.error('保存失败:', error);
      message.error('保存失败');
    }
  };

  const handleChannelChange = (channelId) => {
    setSelectedChannel(channelId);
    if (channelId && !platformCategories[channelId]) {
      loadPlatformCategories(channelId);
    }
    form.setFieldsValue({ platform_category_id: undefined });
  };

  const handleSyncCategories = async (channelId) => {
    try {
      setLoading(true);
      await multiChannelService.syncPlatformCategories(projectId, channelId);
      message.success('同步成功');
      loadPlatformCategories(channelId);
    } catch (error) {
      console.error('同步失败:', error);
      message.error('同步失败');
    } finally {
      setLoading(false);
    }
  };

  const renderCategoryTree = (categories) => {
    const buildTree = (items, parentId = null) => {
      return items
        .filter(item => item.parent_id === parentId)
        .map(item => ({
          title: item.name,
          key: item.id,
          children: buildTree(items, item.id)
        }));
    };
    return buildTree(categories);
  };

  const getChannelName = (channelId) => {
    const channel = channels.find(c => c.id === channelId);
    return channel ? channel.custom_name : '未知渠道';
  };

  const getLocalCategoryName = (categoryId) => {
    const category = localCategories.find(c => c.id === categoryId);
    return category ? category.name : '未知类目';
  };

  const getMappingStatusTag = (mapping) => {
    if (mapping.is_active && mapping.platform_category_id) {
      return <Tag color="success" icon={<CheckCircleOutlined />}>已映射</Tag>;
    } else if (mapping.is_active) {
      return <Tag color="warning" icon={<ExclamationCircleOutlined />}>待配置</Tag>;
    } else {
      return <Tag color="default">已禁用</Tag>;
    }
  };

  const columns = [
    {
      title: '本地类目',
      dataIndex: 'local_category_id',
      key: 'local_category_id',
      render: (categoryId) => (
        <Space>
          <BranchesOutlined />
          <Text>{getLocalCategoryName(categoryId)}</Text>
        </Space>
      )
    },
    {
      title: '目标渠道',
      dataIndex: 'channel_id',
      key: 'channel_id',
      render: (channelId) => (
        <Tag color="blue">{getChannelName(channelId)}</Tag>
      )
    },
    {
      title: '平台类目',
      dataIndex: 'platform_category_path',
      key: 'platform_category_path',
      render: (path, record) => (
        <div>
          {path ? (
            <Text code>{path}</Text>
          ) : (
            <Text type="secondary">未配置</Text>
          )}
        </div>
      )
    },
    {
      title: '映射状态',
      dataIndex: 'status',
      key: 'status',
      render: (_, record) => getMappingStatusTag(record)
    },
    {
      title: '最后同步',
      dataIndex: 'last_sync_at',
      key: 'last_sync_at',
      render: (time) => (
        <Text type="secondary">
          {time ? new Date(time).toLocaleString() : '从未同步'}
        </Text>
      )
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Tooltip title="编辑映射">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEditMapping(record)}
            />
          </Tooltip>
          <Popconfirm
            title="确定删除这个映射吗？"
            onConfirm={() => handleDeleteMapping(record.id)}
          >
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
            />
          </Popconfirm>
        </Space>
      )
    }
  ];

  return (
    <div>
      <Card
        title={
          <Space>
            <LinkOutlined />
            <span>类目映射管理</span>
            <Badge count={mappings.length} />
          </Space>
        }
        extra={
          <Space>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleCreateMapping}
            >
              新建映射
            </Button>
          </Space>
        }
      >
        <Alert
          message="类目映射说明"
          description="将本地商品类目映射到各个平台的类目体系，确保商品能够正确同步到对应的分类下。不同平台的类目结构可能不同，需要建立准确的映射关系。"
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />

        <Table
          columns={columns}
          dataSource={mappings}
          rowKey="id"
          loading={loading}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`
          }}
        />
      </Card>

      {/* 映射配置模态框 */}
      <Modal
        title={editingMapping ? "编辑类目映射" : "新建类目映射"}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={800}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSaveMapping}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="local_category_id"
                label="本地类目"
                rules={[{ required: true, message: '请选择本地类目' }]}
              >
                <Select
                  placeholder="选择本地类目"
                  showSearch
                  filterOption={(input, option) =>
                    option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                  }
                >
                  {localCategories.map(category => (
                    <Option key={category.id} value={category.id}>
                      {category.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="channel_id"
                label="目标渠道"
                rules={[{ required: true, message: '请选择目标渠道' }]}
              >
                <Select
                  placeholder="选择目标渠道"
                  onChange={handleChannelChange}
                >
                  {channels.map(channel => (
                    <Option key={channel.id} value={channel.id}>
                      {channel.custom_name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          {selectedChannel && (
            <Form.Item
              label={
                <Space>
                  <span>平台类目</span>
                  <Button
                    type="link"
                    size="small"
                    icon={<SyncOutlined />}
                    onClick={() => handleSyncCategories(selectedChannel)}
                  >
                    同步最新类目
                  </Button>
                </Space>
              }
            >
              <Form.Item
                name="platform_category_id"
                noStyle
                rules={[{ required: true, message: '请选择平台类目' }]}
              >
                <Select
                  placeholder="选择平台类目"
                  showSearch
                  filterOption={(input, option) =>
                    option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                  }
                >
                  {(platformCategories[selectedChannel] || []).map(category => (
                    <Option key={category.id} value={category.id}>
                      {category.path || category.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Form.Item>
          )}

          <Form.Item
            name="mapping_rules"
            label="映射规则（可选）"
          >
            <Input.TextArea
              rows={3}
              placeholder="输入特殊的映射规则或备注信息"
            />
          </Form.Item>

          <div style={{ textAlign: 'right', marginTop: 24 }}>
            <Space>
              <Button onClick={() => setModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                {editingMapping ? '更新' : '创建'}
              </Button>
            </Space>
          </div>
        </Form>
      </Modal>
    </div>
  );
};

export default CategoryMapping;
