import api from '../../../../../services/api/httpClient';
import { getProjectId } from '../../../../../services/api/httpClient';
import request from '../../../../../services/api/request';

/**
 * 获取MCP服务器列表
 *
 * @param {Object} params 查询参数
 * @param {string} projectIdParam 项目ID参数
 * @returns {Promise<Object>} 服务器列表
 */
export const getServerList = async (params = {}, projectIdParam) => {
  try {
    const projectId = projectIdParam || getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    const response = await api.get(`/project/${projectId}/plugin/sapi/servers`, { params });
    return response;
  } catch (error) {
    console.error('获取MCP服务器列表失败:', error);
    throw error;
  }
};

/**
 * 获取可用的MCP服务器列表
 *
 * @param {Object} params 查询参数
 * @returns {Promise<Object>} 服务器列表
 */
export const getAvailableServers = async (params = {}) => {
  try {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    const response = await api.get(`/project/${projectId}/plugin/sapi/servers/available`, { params });
    return response;
  } catch (error) {
    console.error('获取可用MCP服务器列表失败:', error);
    throw error;
  }
};

/**
 * 创建MCP服务器
 *
 * @param {Object} data 服务器数据
 * @returns {Promise<Object>} 创建结果
 */
export const createServer = async (data) => {
  try {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    const response = await api.post(`/project/${projectId}/plugin/sapi/servers`, data);
    return response;
  } catch (error) {
    console.error('创建MCP服务器失败:', error);
    throw error;
  }
};

/**
 * 更新MCP服务器
 *
 * @param {string} serverId 服务器ID
 * @param {Object} data 更新数据
 * @returns {Promise<Object>} 更新结果
 */
export const updateServer = async (serverId, data) => {
  try {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    const response = await api.put(`/project/${projectId}/plugin/sapi/servers/${serverId}`, data);
    return response;
  } catch (error) {
    console.error('更新MCP服务器失败:', error);
    throw error;
  }
};

/**
 * 删除MCP服务器
 *
 * @param {string} serverId 服务器ID
 * @returns {Promise<Object>} 删除结果
 */
export const deleteServer = async (serverId) => {
  try {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    const response = await api.delete(`/project/${projectId}/plugin/sapi/servers/${serverId}`);
    return response;
  } catch (error) {
    console.error('删除MCP服务器失败:', error);
    throw error;
  }
};

/**
 * 启动MCP服务器
 *
 * @param {string} serverId 服务器ID
 * @returns {Promise<Object>} 启动结果
 */
export const startServer = async (serverId) => {
  try {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    const response = await api.post(`/project/${projectId}/plugin/sapi/servers/${serverId}/start`);
    return response;
  } catch (error) {
    console.error('启动MCP服务器失败:', error);
    throw error;
  }
};

/**
 * 停止MCP服务器
 *
 * @param {string} serverId 服务器ID
 * @returns {Promise<Object>} 停止结果
 */
export const stopServer = async (serverId) => {
  try {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    const response = await api.post(`/project/${projectId}/plugin/sapi/servers/${serverId}/stop`);
    return response;
  } catch (error) {
    console.error('停止MCP服务器失败:', error);
    throw error;
  }
};

/**
 * 获取MCP服务器状态
 *
 * @param {string} serverId 服务器ID
 * @returns {Promise<Object>} 服务器状态
 */
export const getServerStatus = async (serverId) => {
  try {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    const response = await api.get(`/project/${projectId}/plugin/sapi/servers/${serverId}/status`);
    return response;
  } catch (error) {
    console.error('获取MCP服务器状态失败:', error);
    throw error;
  }
};

/**
 * 获取MCP服务器工具列表
 *
 * @param {string} serverId 服务器ID
 * @returns {Promise<Object>} 工具列表
 */
export const getServerTools = async (serverId) => {
  try {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    const response = await api.get(`/project/${projectId}/plugin/sapi/servers/${serverId}/tools`);
    return response;
  } catch (error) {
    console.error('获取MCP服务器工具列表失败:', error);
    throw error;
  }
};

/**
 * 获取MCP客户端配置
 *
 * @param {string} serverId 服务器ID
 * @param {Object} params 配置参数
 * @returns {Promise<Object>} 客户端配置
 */
export const getClientConfig = async (serverId, params = {}) => {
  try {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    // 确保使用 transport_types 而不是 transport_type
    const updatedParams = { ...params };
    if (updatedParams.transport_type && !updatedParams.transport_types) {
      updatedParams.transport_types = updatedParams.transport_type;
      delete updatedParams.transport_type;
    }
    const response = await api.get(`/project/${projectId}/plugin/sapi/servers/${serverId}/client-config`, { params: updatedParams });
    return response;
  } catch (error) {
    console.error('获取MCP客户端配置失败:', error);
    throw error;
  }
};

/**
 * 连接到MCP服务器
 *
 * @param {string} serverId 服务器ID
 * @param {string} apiToken API令牌
 * @returns {Promise<Object>} 连接结果
 */
export const connectToMCPServer = async (serverId, apiToken = "") => {
  try {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    // 使用新的连接端点，避免路由冲突
    const response = await api.post(`/project/${projectId}/plugin/sapi/servers/connect/${serverId}`, apiToken ? { api_token: apiToken } : {});
    return response;
  } catch (error) {
    console.error('连接MCP服务器失败:', error);
    throw error;
  }
};

/**
 * 获取MCP会话工具列表
 *
 * @param {string} sessionId 会话ID
 * @returns {Promise<Object>} 工具列表
 */
export const getMCPSessionTools = async (sessionId) => {
  try {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    const response = await api.get(`/project/${projectId}/plugin/sapi/sessions/${sessionId}/tools`);
    return response;
  } catch (error) {
    console.error('获取MCP会话工具列表失败:', error);
    throw error;
  }
};

/**
 * 调用MCP工具
 *
 * @param {string} sessionId 会话ID
 * @param {string} toolName 工具名称
 * @param {Object} data 请求数据
 * @returns {Promise<Object>} 调用结果
 */
export const callMCPTool = async (sessionId, toolName, data) => {
  try {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    const response = await api.post(`/project/${projectId}/plugin/sapi/sessions/${sessionId}/tools/${toolName}/call`, data);
    return response;
  } catch (error) {
    console.error('调用MCP工具失败:', error);
    throw error;
  }
};

/**
 * 关闭MCP会话
 *
 * @param {string} sessionId 会话ID
 * @returns {Promise<Object>} 关闭结果
 */
export const closeMCPSession = async (sessionId) => {
  try {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    const response = await api.post(`/project/${projectId}/plugin/sapi/sessions/${sessionId}/close`);
    return response;
  } catch (error) {
    console.error('关闭MCP会话失败:', error);
    throw error;
  }
};

/**
 * 获取MCP服务器列表（用于令牌管理）
 *
 * @param {string} projectId 项目ID
 * @returns {Promise<Object>} 服务器列表
 */
export const getMCPServers = async (projectId) => {
  try {
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    const response = await request.get(`/api/project/${projectId}/plugin/sapi/servers`);
    return {
      success: true,
      data: response.data || []
    };
  } catch (error) {
    console.error('获取MCP服务器列表失败:', error);
    return {
      success: false,
      message: error.message || '获取MCP服务器列表失败'
    };
  }
};

/**
 * 测试API调用 (已弃用)
 *
 * @param {string} serverId 服务器ID
 * @param {string} toolName 工具名称
 * @param {Object} data 请求数据
 * @returns {Promise<Object>} 测试结果
 * @deprecated 请使用新的分步骤API
 */
export const testApiCall = async (serverId, toolName, data) => {
  try {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    const response = await api.post(`/project/${projectId}/plugin/sapi/servers/${serverId}/tools/${toolName}/test`, data);
    return response;
  } catch (error) {
    console.error('测试API调用失败:', error);
    throw error;
  }
};

/**
 * 创建MCP令牌（JWT）
 * @param {Object} data 令牌参数
 * @returns {Promise<Object>} 创建结果
 */
export const createMcpToken = async (data) => {
  try {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    // 强制带上 project_id
    const reqData = { ...data, project_id: projectId };
    const response = await api.post(`/project/${projectId}/plugin/sapi/mcp/token`, reqData);
    return response;
  } catch (error) {
    console.error('创建MCP令牌失败:', error);
    throw error;
  }
};

/**
 * 直接发送MCP请求到服务器
 *
 * @param {string} serverId 服务器ID
 * @param {Object} requestData 请求数据
 * @param {string} token JWT令牌
 * @returns {Promise<Object>} 响应结果
 */
export const sendRequest = async (serverId, requestData, token = "") => {
  try {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }

    // 构建请求URL - 使用MCP专用短路径
    // 注意：这里不使用api实例，因为它会自动添加/api/v1前缀
    // 而MCP路径应该是/v1/servers/{serverId}/mcp（不带/api前缀）
    const url = `${window.location.origin}/v1/servers/${serverId}/mcp`;

    // 构建请求头
    const headers = {
      'Content-Type': 'application/json'
    };

    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    // 使用fetch直接发送请求，避免api实例添加前缀
    const response = await fetch(url, {
      method: 'POST',
      headers: headers,
      body: JSON.stringify(requestData)
    });

    if (!response.ok) {
      throw new Error(`HTTP错误: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('发送MCP请求失败:', error);
    throw error;
  }
};

/**
 * 一键添加MCP服务器到AI助手
 *
 * @param {string} serverId 服务器ID
 * @returns {Promise<Object>} 添加结果
 */
export const addToAIAssistant = async (serverId) => {
  try {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    const response = await api.post(`/project/${projectId}/plugin/sapi/servers/${serverId}/add-to-ai-assistant`);
    return response;
  } catch (error) {
    console.error('添加MCP服务器到AI助手失败:', error);
    throw error;
  }
};
