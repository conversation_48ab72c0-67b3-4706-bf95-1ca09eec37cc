/*!
 * AI智能客服组件 v1.0.0
 * 多行业AI SaaS平台 - 智能客服组件
 * (c) 2025 Multi-Industry AI SaaS Team
 */

(function(window, document) {
    'use strict';

    // AI客服组件类
    class AICustomerService {
        constructor() {
            this.config = {};
            this.isOpen = false;
            this.sessionId = null;
            this.messages = [];
            this.socket = null;
            this.container = null;
            this.chatWindow = null;
            this.messageContainer = null;
            this.inputBox = null;
        }

        // 初始化组件
        init(config = {}) {
            this.config = Object.assign({
                widgetId: 'default',
                theme: 'light',
                size: 'medium',
                position: 'bottom-right',
                apiBaseUrl: '',
                title: 'AI智能客服',
                welcomeMessage: '您好！我是AI智能客服，有什么可以帮助您的吗？',
                themeColor: '#1890ff',
                autoOpen: false,
                showAvatar: true,
                enableFileUpload: false,
                enableVoiceInput: false
            }, config);

            this.sessionId = this.generateSessionId();
            this.createWidget();
            this.setupEventListeners();
            
            if (this.config.autoOpen) {
                this.openChat();
            }

            console.log('AI客服组件初始化完成', this.config);
        }

        // 生成会话ID
        generateSessionId() {
            return 'session_' + Math.random().toString(36).substr(2, 9) + '_' + Date.now();
        }

        // 创建组件UI
        createWidget() {
            // 创建容器
            this.container = document.createElement('div');
            this.container.className = `ai-cs-widget ai-cs-${this.config.theme} ai-cs-${this.config.size} ai-cs-${this.config.position}`;
            this.container.innerHTML = this.getWidgetHTML();

            // 添加样式
            if (!document.getElementById('ai-cs-styles')) {
                const styles = document.createElement('style');
                styles.id = 'ai-cs-styles';
                styles.textContent = this.getWidgetCSS();
                document.head.appendChild(styles);
            }

            document.body.appendChild(this.container);

            // 获取关键元素引用
            this.chatWindow = this.container.querySelector('.ai-cs-chat-window');
            this.messageContainer = this.container.querySelector('.ai-cs-messages');
            this.inputBox = this.container.querySelector('.ai-cs-input');
        }

        // 获取组件HTML
        getWidgetHTML() {
            return `
                <!-- 悬浮按钮 -->
                <div class="ai-cs-floating-button" onclick="window.AICustomerService.toggleChat()">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M12 2C6.48 2 2 6.48 2 12C2 13.54 2.36 15.01 3.01 16.32L1 23L7.68 20.99C8.99 21.64 10.46 22 12 22C17.52 22 22 17.52 22 12C22 6.48 17.52 2 12 2ZM18 *************************************" fill="currentColor"/>
                    </svg>
                </div>

                <!-- 聊天窗口 -->
                <div class="ai-cs-chat-window" style="display: none;">
                    <!-- 头部 -->
                    <div class="ai-cs-header">
                        <div class="ai-cs-title">
                            ${this.config.showAvatar ? '<div class="ai-cs-avatar"></div>' : ''}
                            <span>${this.config.title}</span>
                        </div>
                        <button class="ai-cs-close" onclick="window.AICustomerService.closeChat()">
                            <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                                <path d="M12 4L4 12M4 4L12 12" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                            </svg>
                        </button>
                    </div>

                    <!-- 消息区域 -->
                    <div class="ai-cs-messages">
                        <div class="ai-cs-message ai-cs-message-ai">
                            <div class="ai-cs-message-content">
                                ${this.config.welcomeMessage}
                            </div>
                            <div class="ai-cs-message-time">${this.formatTime(new Date())}</div>
                        </div>
                    </div>

                    <!-- 输入区域 -->
                    <div class="ai-cs-input-area">
                        ${this.config.enableFileUpload ? '<button class="ai-cs-file-btn" title="上传文件">📎</button>' : ''}
                        <input type="text" class="ai-cs-input" placeholder="请输入您的问题..." 
                               onkeypress="if(event.key==='Enter') window.AICustomerService.sendMessage()">
                        <button class="ai-cs-send-btn" onclick="window.AICustomerService.sendMessage()">
                            <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                                <path d="M2 8L14 2L10 8L14 14L2 8Z" fill="currentColor"/>
                            </svg>
                        </button>
                        ${this.config.enableVoiceInput ? '<button class="ai-cs-voice-btn" title="语音输入">🎤</button>' : ''}
                    </div>

                    <!-- 建议快捷回复 -->
                    <div class="ai-cs-suggestions" style="display: none;"></div>
                </div>
            `;
        }

        // 获取组件CSS样式
        getWidgetCSS() {
            return `
                .ai-cs-widget {
                    position: fixed;
                    z-index: 9999;
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
                }

                .ai-cs-bottom-right { bottom: 20px; right: 20px; }
                .ai-cs-bottom-left { bottom: 20px; left: 20px; }
                .ai-cs-top-right { top: 20px; right: 20px; }
                .ai-cs-top-left { top: 20px; left: 20px; }

                .ai-cs-floating-button {
                    width: 60px;
                    height: 60px;
                    background: ${this.config.themeColor};
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    cursor: pointer;
                    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                    transition: all 0.3s ease;
                    color: white;
                }

                .ai-cs-floating-button:hover {
                    transform: scale(1.1);
                    box-shadow: 0 6px 20px rgba(0,0,0,0.25);
                }

                .ai-cs-chat-window {
                    position: absolute;
                    bottom: 80px;
                    right: 0;
                    width: 350px;
                    height: 500px;
                    background: white;
                    border-radius: 12px;
                    box-shadow: 0 8px 32px rgba(0,0,0,0.15);
                    display: flex;
                    flex-direction: column;
                    overflow: hidden;
                    animation: slideInUp 0.3s ease;
                }

                @keyframes slideInUp {
                    from { transform: translateY(20px); opacity: 0; }
                    to { transform: translateY(0); opacity: 1; }
                }

                .ai-cs-header {
                    padding: 16px;
                    background: ${this.config.themeColor};
                    color: white;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                }

                .ai-cs-title {
                    display: flex;
                    align-items: center;
                    gap: 8px;
                    font-weight: 600;
                }

                .ai-cs-avatar {
                    width: 32px;
                    height: 32px;
                    border-radius: 50%;
                    background: rgba(255,255,255,0.2);
                    background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="white"><path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1H5C3.89 1 3 1.89 3 3V21C3 22.11 3.89 23 5 23H19C20.11 23 21 22.11 21 21V9Z"/></svg>');
                    background-size: 20px;
                    background-repeat: no-repeat;
                    background-position: center;
                }

                .ai-cs-close {
                    background: none;
                    border: none;
                    color: white;
                    cursor: pointer;
                    padding: 4px;
                    border-radius: 4px;
                    opacity: 0.8;
                    transition: opacity 0.2s;
                }

                .ai-cs-close:hover { opacity: 1; }

                .ai-cs-messages {
                    flex: 1;
                    padding: 16px;
                    overflow-y: auto;
                    display: flex;
                    flex-direction: column;
                    gap: 12px;
                }

                .ai-cs-message {
                    max-width: 80%;
                    word-wrap: break-word;
                }

                .ai-cs-message-ai {
                    align-self: flex-start;
                }

                .ai-cs-message-user {
                    align-self: flex-end;
                }

                .ai-cs-message-content {
                    padding: 12px 16px;
                    border-radius: 18px;
                    font-size: 14px;
                    line-height: 1.4;
                }

                .ai-cs-message-ai .ai-cs-message-content {
                    background: #f5f5f5;
                    color: #333;
                }

                .ai-cs-message-user .ai-cs-message-content {
                    background: ${this.config.themeColor};
                    color: white;
                }

                .ai-cs-message-time {
                    font-size: 11px;
                    color: #999;
                    margin-top: 4px;
                    text-align: center;
                }

                .ai-cs-input-area {
                    padding: 16px;
                    border-top: 1px solid #eee;
                    display: flex;
                    align-items: center;
                    gap: 8px;
                }

                .ai-cs-input {
                    flex: 1;
                    border: 1px solid #ddd;
                    border-radius: 20px;
                    padding: 8px 16px;
                    outline: none;
                    font-size: 14px;
                }

                .ai-cs-input:focus {
                    border-color: ${this.config.themeColor};
                }

                .ai-cs-send-btn, .ai-cs-file-btn, .ai-cs-voice-btn {
                    width: 36px;
                    height: 36px;
                    border: none;
                    border-radius: 50%;
                    background: ${this.config.themeColor};
                    color: white;
                    cursor: pointer;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    transition: background 0.2s;
                }

                .ai-cs-send-btn:hover, .ai-cs-file-btn:hover, .ai-cs-voice-btn:hover {
                    background: ${this.adjustColor(this.config.themeColor, -20)};
                }

                .ai-cs-suggestions {
                    padding: 8px 16px;
                    border-top: 1px solid #eee;
                    display: flex;
                    flex-wrap: wrap;
                    gap: 8px;
                }

                .ai-cs-suggestion {
                    padding: 6px 12px;
                    background: #f0f0f0;
                    border: none;
                    border-radius: 16px;
                    font-size: 12px;
                    cursor: pointer;
                    transition: background 0.2s;
                }

                .ai-cs-suggestion:hover {
                    background: ${this.config.themeColor};
                    color: white;
                }

                .ai-cs-typing {
                    display: flex;
                    align-items: center;
                    gap: 4px;
                    padding: 12px 16px;
                    color: #666;
                    font-size: 14px;
                }

                .ai-cs-typing-dots {
                    display: flex;
                    gap: 2px;
                }

                .ai-cs-typing-dot {
                    width: 4px;
                    height: 4px;
                    border-radius: 50%;
                    background: #999;
                    animation: typing 1.4s infinite;
                }

                .ai-cs-typing-dot:nth-child(2) { animation-delay: 0.2s; }
                .ai-cs-typing-dot:nth-child(3) { animation-delay: 0.4s; }

                @keyframes typing {
                    0%, 60%, 100% { opacity: 0.3; }
                    30% { opacity: 1; }
                }

                /* 响应式设计 */
                @media (max-width: 480px) {
                    .ai-cs-chat-window {
                        width: 100vw;
                        height: 100vh;
                        bottom: 0;
                        right: 0;
                        border-radius: 0;
                    }
                }

                /* 暗色主题 */
                .ai-cs-dark .ai-cs-chat-window {
                    background: #2d2d2d;
                    color: white;
                }

                .ai-cs-dark .ai-cs-message-ai .ai-cs-message-content {
                    background: #404040;
                    color: white;
                }

                .ai-cs-dark .ai-cs-input {
                    background: #404040;
                    border-color: #555;
                    color: white;
                }

                .ai-cs-dark .ai-cs-input::placeholder {
                    color: #999;
                }
            `;
        }

        // 调整颜色亮度
        adjustColor(color, percent) {
            const num = parseInt(color.replace("#", ""), 16);
            const amt = Math.round(2.55 * percent);
            const R = (num >> 16) + amt;
            const G = (num >> 8 & 0x00FF) + amt;
            const B = (num & 0x0000FF) + amt;
            return "#" + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
                (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
                (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1);
        }

        // 设置事件监听器
        setupEventListeners() {
            // 监听窗口大小变化
            window.addEventListener('resize', () => {
                this.adjustPosition();
            });

            // 监听点击外部关闭
            document.addEventListener('click', (e) => {
                if (this.isOpen && !this.container.contains(e.target)) {
                    // 可选：点击外部关闭聊天窗口
                    // this.closeChat();
                }
            });
        }

        // 切换聊天窗口
        toggleChat() {
            if (this.isOpen) {
                this.closeChat();
            } else {
                this.openChat();
            }
        }

        // 打开聊天窗口
        openChat() {
            this.isOpen = true;
            this.chatWindow.style.display = 'flex';
            this.inputBox.focus();
            
            // 触发打开事件
            this.triggerEvent('open');
        }

        // 关闭聊天窗口
        closeChat() {
            this.isOpen = false;
            this.chatWindow.style.display = 'none';
            
            // 触发关闭事件
            this.triggerEvent('close');
        }

        // 发送消息
        async sendMessage(text = null) {
            const message = text || this.inputBox.value.trim();
            if (!message) return;

            // 清空输入框
            if (!text) this.inputBox.value = '';

            // 添加用户消息
            this.addMessage('user', message);

            // 显示正在输入状态
            this.showTyping();

            try {
                // 发送到服务器
                const response = await this.sendToServer(message);
                
                // 隐藏正在输入状态
                this.hideTyping();

                // 添加AI回复
                if (response.success) {
                    this.addMessage('ai', response.data.message);
                    
                    // 显示建议回复
                    if (response.data.suggestions) {
                        this.showSuggestions(response.data.suggestions);
                    }
                } else {
                    this.addMessage('ai', '抱歉，我遇到了一些问题，请稍后再试。');
                }

            } catch (error) {
                console.error('发送消息失败:', error);
                this.hideTyping();
                this.addMessage('ai', '网络连接失败，请检查网络后重试。');
            }

            // 触发消息发送事件
            this.triggerEvent('message', { message, type: 'user' });
        }

        // 添加消息到界面
        addMessage(type, content, time = null) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `ai-cs-message ai-cs-message-${type}`;
            
            messageDiv.innerHTML = `
                <div class="ai-cs-message-content">${this.formatMessage(content)}</div>
                <div class="ai-cs-message-time">${time || this.formatTime(new Date())}</div>
            `;

            this.messageContainer.appendChild(messageDiv);
            this.scrollToBottom();

            // 保存到消息历史
            this.messages.push({
                type,
                content,
                time: time || new Date().toISOString()
            });
        }

        // 格式化消息内容
        formatMessage(content) {
            // 简单的文本处理，可以扩展支持Markdown等
            return content
                .replace(/\n/g, '<br>')
                .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
                .replace(/\*(.*?)\*/g, '<em>$1</em>');
        }

        // 显示正在输入状态
        showTyping() {
            const typingDiv = document.createElement('div');
            typingDiv.className = 'ai-cs-typing';
            typingDiv.innerHTML = `
                AI正在输入
                <div class="ai-cs-typing-dots">
                    <div class="ai-cs-typing-dot"></div>
                    <div class="ai-cs-typing-dot"></div>
                    <div class="ai-cs-typing-dot"></div>
                </div>
            `;
            
            this.messageContainer.appendChild(typingDiv);
            this.scrollToBottom();
        }

        // 隐藏正在输入状态
        hideTyping() {
            const typing = this.messageContainer.querySelector('.ai-cs-typing');
            if (typing) {
                typing.remove();
            }
        }

        // 显示建议回复
        showSuggestions(suggestions) {
            const suggestionsContainer = this.container.querySelector('.ai-cs-suggestions');
            suggestionsContainer.innerHTML = '';
            
            suggestions.forEach(suggestion => {
                const btn = document.createElement('button');
                btn.className = 'ai-cs-suggestion';
                btn.textContent = suggestion;
                btn.onclick = () => {
                    this.sendMessage(suggestion);
                    suggestionsContainer.style.display = 'none';
                };
                suggestionsContainer.appendChild(btn);
            });

            suggestionsContainer.style.display = 'flex';
        }

        // 发送消息到服务器
        async sendToServer(message) {
            const url = `${this.config.apiBaseUrl}/api/v1/shared/ai-customer-service/chat/${this.config.widgetId}`;
            
            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    message: message,
                    session_id: this.sessionId,
                    user_info: {
                        user_agent: navigator.userAgent,
                        page_url: window.location.href,
                        page_title: document.title
                    },
                    widget_config: this.config
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}`);
            }

            return await response.json();
        }

        // 滚动到底部
        scrollToBottom() {
            this.messageContainer.scrollTop = this.messageContainer.scrollHeight;
        }

        // 调整位置（响应式）
        adjustPosition() {
            // 在移动设备上调整位置
            if (window.innerWidth <= 480 && this.isOpen) {
                this.chatWindow.style.width = '100vw';
                this.chatWindow.style.height = '100vh';
                this.chatWindow.style.bottom = '0';
                this.chatWindow.style.right = '0';
            }
        }

        // 格式化时间
        formatTime(date) {
            return date.toLocaleTimeString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        // 触发自定义事件
        triggerEvent(eventName, data = {}) {
            const event = new CustomEvent(`ai-cs-${eventName}`, {
                detail: { ...data, widget: this }
            });
            window.dispatchEvent(event);
        }

        // 销毁组件
        destroy() {
            if (this.container) {
                this.container.remove();
            }
            
            if (this.socket) {
                this.socket.close();
            }

            // 清理事件监听器
            // 这里可以添加更多清理逻辑
        }

        // 获取会话历史
        getMessages() {
            return this.messages;
        }

        // 设置配置
        setConfig(newConfig) {
            this.config = Object.assign(this.config, newConfig);
            // 可以在这里更新UI以反映新配置
        }

        // 获取配置
        getConfig() {
            return this.config;
        }
    }

    // 创建全局实例
    window.AICustomerService = new AICustomerService();

    // 如果页面已加载完成，立即可用
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            console.log('AI客服组件已准备就绪');
        });
    } else {
        console.log('AI客服组件已准备就绪');
    }

})(window, document); 