#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
项目级 AI 助手管理 API
"""

import logging
import uuid
from typing import Optional, List, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query, Path, status
from sqlalchemy.ext.asyncio import AsyncSession

from db.database import get_db
from models.project import Project
from models.user import User
from core.auth import get_current_user
from api.deps import get_current_project
from services.ai import AIAssistantService
from schemas.ai import (
    AIAssistantCreate,
    AIAssistantUpdate,
    AIAssistantResponse,
    AIAssistantListResponse,
    AIAssistantToolCreate,
    AIAssistantToolUpdate,
    AIAssistantToolResponse,
    AIAssistantToolListResponse,
)

logger = logging.getLogger(__name__)
router = APIRouter()

@router.get("/ai/assistants", response_model=AIAssistantListResponse)
async def list_assistants(
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    assistant_status: Optional[str] = Query(None, alias="status"),
    is_public: Optional[bool] = Query(None),
    created_by: Optional[uuid.UUID] = Query(None),
):
    """
    获取 AI 助手列表
    """
    try:
        assistants, total = await AIAssistantService.get_assistants(
            db=db,
            skip=skip,
            limit=limit,
            tenant_id=project.tenant_id,
            project_id=project.id,
            status=assistant_status,
            is_public=is_public,
            created_by=created_by,
        )

        return {
            "success": True,
            "message": "获取 AI 助手列表成功",
            "data": assistants,
            "total": total,
            "page": skip // limit + 1,
            "page_size": limit,
        }
    except Exception as e:
        logger.error(f"获取 AI 助手列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取 AI 助手列表失败: {str(e)}",
        )

@router.get("/ai/assistants/{assistant_id}", response_model=AIAssistantResponse)
async def get_assistant(
    assistant_id: uuid.UUID = Path(...),
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    获取 AI 助手详情
    """
    try:
        assistant = await AIAssistantService.get_assistant(db=db, assistant_id=assistant_id)

        if not assistant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"AI 助手 ID '{assistant_id}' 不存在",
            )

        # 检查是否有权限访问该助手
        if assistant.project_id != project.id and assistant.tenant_id != project.tenant_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问该 AI 助手",
            )

        return {
            "success": True,
            "message": "获取 AI 助手详情成功",
            "data": assistant,
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取 AI 助手详情失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取 AI 助手详情失败: {str(e)}",
        )

@router.post("/ai/assistants", response_model=AIAssistantResponse, status_code=status.HTTP_201_CREATED)
async def create_assistant(
    assistant_data: AIAssistantCreate,
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    创建 AI 助手

    注意：只有项目管理员才能创建 AI 助手
    """
    try:
        # 检查权限
        if not current_user.is_system_admin and not current_user.is_tenant_admin:
            # 检查是否为项目管理员
            if not any(pu.is_admin for pu in project.project_users if pu.user_id == current_user.id):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="只有项目管理员才能创建 AI 助手",
                )

        # 设置项目 ID 和租户 ID
        assistant_data.project_id = project.id
        assistant_data.tenant_id = project.tenant_id

        assistant = await AIAssistantService.create_assistant(
            db=db,
            assistant_data=assistant_data,
            user_id=current_user.id,
        )

        return {
            "success": True,
            "message": "创建 AI 助手成功",
            "data": assistant,
        }
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建 AI 助手失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建 AI 助手失败: {str(e)}",
        )

@router.put("/ai/assistants/{assistant_id}", response_model=AIAssistantResponse)
async def update_assistant(
    assistant_id: uuid.UUID = Path(...),
    assistant_data: AIAssistantUpdate = None,
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    更新 AI 助手

    注意：只有项目管理员或助手创建者才能更新 AI 助手
    """
    try:
        # 获取助手
        assistant = await AIAssistantService.get_assistant(db=db, assistant_id=assistant_id)

        if not assistant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"AI 助手 ID '{assistant_id}' 不存在",
            )

        # 检查是否有权限更新该助手
        if assistant.project_id != project.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权更新该 AI 助手",
            )

        # 检查权限
        if not current_user.is_system_admin and not current_user.is_tenant_admin:
            # 检查是否为项目管理员或助手创建者
            is_project_admin = any(pu.is_admin for pu in project.project_users if pu.user_id == current_user.id)
            is_creator = assistant.created_by == current_user.id

            if not is_project_admin and not is_creator:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="只有项目管理员或助手创建者才能更新 AI 助手",
                )

        assistant = await AIAssistantService.update_assistant(
            db=db,
            assistant_id=assistant_id,
            assistant_data=assistant_data,
        )

        return {
            "success": True,
            "message": "更新 AI 助手成功",
            "data": assistant,
        }
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新 AI 助手失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新 AI 助手失败: {str(e)}",
        )

@router.delete("/ai/assistants/{assistant_id}", response_model=AIAssistantResponse)
async def delete_assistant(
    assistant_id: uuid.UUID = Path(...),
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    删除 AI 助手

    注意：只有项目管理员或助手创建者才能删除 AI 助手
    """
    try:
        # 获取助手
        assistant = await AIAssistantService.get_assistant(db=db, assistant_id=assistant_id)

        if not assistant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"AI 助手 ID '{assistant_id}' 不存在",
            )

        # 检查是否有权限删除该助手
        if assistant.project_id != project.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权删除该 AI 助手",
            )

        # 检查权限
        if not current_user.is_system_admin and not current_user.is_tenant_admin:
            # 检查是否为项目管理员或助手创建者
            is_project_admin = any(pu.is_admin for pu in project.project_users if pu.user_id == current_user.id)
            is_creator = assistant.created_by == current_user.id

            if not is_project_admin and not is_creator:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="只有项目管理员或助手创建者才能删除 AI 助手",
                )

        success = await AIAssistantService.delete_assistant(db=db, assistant_id=assistant_id)

        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"AI 助手 ID '{assistant_id}' 不存在",
            )

        return {
            "success": True,
            "message": "删除 AI 助手成功",
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除 AI 助手失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除 AI 助手失败: {str(e)}",
        )

@router.get("/ai/tools", response_model=AIAssistantToolListResponse)
async def list_tools(
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    tool_type: Optional[str] = Query(None, alias="type"),
    tool_status: Optional[str] = Query(None, alias="status"),
    created_by: Optional[uuid.UUID] = Query(None),
):
    """
    获取 AI 助手工具列表
    """
    try:
        tools, total = await AIAssistantService.get_tools(
            db=db,
            skip=skip,
            limit=limit,
            tenant_id=project.tenant_id,
            project_id=project.id,
            type=tool_type,
            status=tool_status,
            created_by=created_by,
        )

        return {
            "success": True,
            "message": "获取 AI 助手工具列表成功",
            "data": tools,
            "total": total,
            "page": skip // limit + 1,
            "page_size": limit,
        }
    except Exception as e:
        logger.error(f"获取 AI 助手工具列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取 AI 助手工具列表失败: {str(e)}",
        )

@router.get("/ai/tools/{tool_id}", response_model=AIAssistantToolResponse)
async def get_tool(
    tool_id: uuid.UUID = Path(...),
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    获取 AI 助手工具详情
    """
    try:
        tool = await AIAssistantService.get_tool(db=db, tool_id=tool_id)

        if not tool:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"AI 助手工具 ID '{tool_id}' 不存在",
            )

        # 检查是否有权限访问该工具
        if tool.project_id != project.id and tool.tenant_id != project.tenant_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问该 AI 助手工具",
            )

        return {
            "success": True,
            "message": "获取 AI 助手工具详情成功",
            "data": tool,
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取 AI 助手工具详情失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取 AI 助手工具详情失败: {str(e)}",
        )

@router.post("/ai/tools", response_model=AIAssistantToolResponse, status_code=status.HTTP_201_CREATED)
async def create_tool(
    tool_data: AIAssistantToolCreate,
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    创建 AI 助手工具

    注意：只有项目管理员才能创建 AI 助手工具
    """
    try:
        # 检查权限
        if not current_user.is_system_admin and not current_user.is_tenant_admin:
            # 检查是否为项目管理员
            if not any(pu.is_admin for pu in project.project_users if pu.user_id == current_user.id):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="只有项目管理员才能创建 AI 助手工具",
                )

        # 设置项目 ID 和租户 ID
        tool_data.project_id = project.id
        tool_data.tenant_id = project.tenant_id

        tool = await AIAssistantService.create_tool(
            db=db,
            tool_data=tool_data,
            user_id=current_user.id,
        )

        return {
            "success": True,
            "message": "创建 AI 助手工具成功",
            "data": tool,
        }
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建 AI 助手工具失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建 AI 助手工具失败: {str(e)}",
        )

@router.put("/ai/tools/{tool_id}", response_model=AIAssistantToolResponse)
async def update_tool(
    tool_id: uuid.UUID = Path(...),
    tool_data: AIAssistantToolUpdate = None,
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    更新 AI 助手工具

    注意：只有项目管理员或工具创建者才能更新 AI 助手工具
    """
    try:
        # 获取工具
        tool = await AIAssistantService.get_tool(db=db, tool_id=tool_id)

        if not tool:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"AI 助手工具 ID '{tool_id}' 不存在",
            )

        # 检查是否有权限更新该工具
        if tool.project_id != project.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权更新该 AI 助手工具",
            )

        # 检查权限
        if not current_user.is_system_admin and not current_user.is_tenant_admin:
            # 检查是否为项目管理员或工具创建者
            is_project_admin = any(pu.is_admin for pu in project.project_users if pu.user_id == current_user.id)
            is_creator = tool.created_by == current_user.id

            if not is_project_admin and not is_creator:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="只有项目管理员或工具创建者才能更新 AI 助手工具",
                )

        tool = await AIAssistantService.update_tool(
            db=db,
            tool_id=tool_id,
            tool_data=tool_data,
        )

        return {
            "success": True,
            "message": "更新 AI 助手工具成功",
            "data": tool,
        }
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新 AI 助手工具失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新 AI 助手工具失败: {str(e)}",
        )

@router.delete("/ai/tools/{tool_id}", response_model=AIAssistantToolResponse)
async def delete_tool(
    tool_id: uuid.UUID = Path(...),
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    删除 AI 助手工具

    注意：只有项目管理员或工具创建者才能删除 AI 助手工具
    """
    try:
        # 获取工具
        tool = await AIAssistantService.get_tool(db=db, tool_id=tool_id)

        if not tool:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"AI 助手工具 ID '{tool_id}' 不存在",
            )

        # 检查是否有权限删除该工具
        if tool.project_id != project.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权删除该 AI 助手工具",
            )

        # 检查权限
        if not current_user.is_system_admin and not current_user.is_tenant_admin:
            # 检查是否为项目管理员或工具创建者
            is_project_admin = any(pu.is_admin for pu in project.project_users if pu.user_id == current_user.id)
            is_creator = tool.created_by == current_user.id

            if not is_project_admin and not is_creator:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="只有项目管理员或工具创建者才能删除 AI 助手工具",
                )

        try:
            success = await AIAssistantService.delete_tool(db=db, tool_id=tool_id)
        except ValueError as e:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=str(e),
            )

        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"AI 助手工具 ID '{tool_id}' 不存在",
            )

        return {
            "success": True,
            "message": "删除 AI 助手工具成功",
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除 AI 助手工具失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除 AI 助手工具失败: {str(e)}",
        )
