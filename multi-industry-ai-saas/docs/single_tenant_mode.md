# 单租户模式实现说明

## 1. 单租户模式概述

单租户模式是 Retail AI SaaS 系统的一种特殊部署模式，主要用于初期开发和测试阶段。在这种模式下，系统只包含单个租户的功能，相当于原来的零售 AI 系统，但保留了向多租户架构扩展的能力。

### 1.1 单租户模式的特点

- **简化架构**：不需要服务商层，直接从租户开始
- **直接访问**：用户直接访问租户应用，无需租户选择
- **功能完整**：包含完整的零售业务功能
- **扩展准备**：数据模型和代码结构已为多租户扩展做好准备

### 1.2 单租户与多租户的关系

单租户模式可以看作是多租户架构的一个特例，其中：

- 单租户 = 多租户架构中的一个租户
- 单租户管理员 = 租户管理员
- 单租户用户 = 项目用户
- 单租户项目 = 多租户架构中的项目

## 2. 单租户模式的数据模型

### 2.1 核心数据模型

在单租户模式下，我们仍然保留了多租户架构的核心数据模型，但有一些简化：

- **Tenant**：系统中只有一个租户记录
- **Project**：租户可以创建多个项目
- **User**：所有用户都关联到同一个租户
- **Role**：角色定义在项目级别

### 2.2 数据隔离

虽然是单租户模式，但我们仍然实现了项目级别的数据隔离：

- 所有业务数据都包含 `project_id` 字段
- 通过项目上下文自动过滤数据
- 用户只能访问其所属项目的数据

## 3. 单租户模式的功能模块

### 3.1 用户管理

- 用户注册和登录
- 用户角色分配
- 用户项目关联

### 3.2 项目管理

- 项目创建和配置
- 项目成员管理
- 项目设置管理

### 3.3 基础设置

- 门店管理
  - 门店信息维护
  - 门店分类管理
  - 门店区域管理
- 仓储管理
  - 仓库信息维护
  - 仓库设置管理

### 3.4 系统设置

- 主题设置
- 通知设置
- 安全设置
- 常规设置
- 第三方登录设置

### 3.5 零售业务功能

- 商品管理
- 库存管理
- 采购管理
- 销售管理
- 财务管理
- 报表分析

## 4. 单租户模式的 API 设计

### 4.1 认证 API

- `POST /api/auth/login`：用户登录
- `POST /api/auth/logout`：用户登出
- `POST /api/auth/register`：用户注册

### 4.2 项目 API

- `GET /api/projects`：获取项目列表
- `POST /api/projects`：创建项目
- `GET /api/projects/{project_id}`：获取项目详情
- `PUT /api/projects/{project_id}`：更新项目
- `DELETE /api/projects/{project_id}`：删除项目

### 4.3 用户与角色 API

- `GET /api/project/settings/roles`：获取角色列表
- `POST /api/project/settings/roles`：创建角色
- `GET /api/project/users`：获取用户列表
- `POST /api/project/users`：添加用户到项目

### 4.4 基础设置 API

- `GET /api/project/basic/stores`：获取门店列表
- `POST /api/project/basic/stores`：创建门店
- `GET /api/project/basic/warehouses`：获取仓库列表
- `POST /api/project/basic/warehouses`：创建仓库

### 4.5 系统设置 API

- `GET /api/project/settings/theme`：获取主题设置
- `POST /api/project/settings/theme`：保存主题设置
- `GET /api/project/settings/notification`：获取通知设置
- `POST /api/project/settings/notification`：保存通知设置

## 5. 单租户模式的前端实现

### 5.1 页面结构

- **登录/注册页**：用户认证
- **项目选择页**：选择或创建项目
- **项目主页**：项目仪表板
- **功能模块页**：各业务功能模块
- **设置页**：系统设置和基础设置

### 5.2 导航结构

- **顶部导航**：用户信息、项目切换、通知
- **侧边导航**：功能模块导航
  - 门店
  - 运营
  - 采购
  - 财务
  - 人事
  - 仓库
  - 系统设置

### 5.3 权限控制

- 基于角色的菜单显示
- 基于权限的功能访问控制
- 项目管理员拥有所有权限

## 6. 从单租户到多租户的迁移路径

### 6.1 数据迁移

- 创建服务商数据结构
- 将现有租户关联到服务商
- 调整数据库Schema结构

### 6.2 功能扩展

- 添加服务商管理界面
- 实现租户管理功能
- 添加多租户路由和识别

### 6.3 部署调整

- 调整API路由结构
- 更新认证和授权机制
- 实现多租户数据隔离

## 7. 当前实现状态

目前，我们已经实现了单租户模式的以下功能：

- 基本的用户认证和授权
- 项目创建和管理
- 基础设置（门店管理、仓储管理）
- 系统设置（主题、通知、安全、常规、第三方登录）
- 角色和权限管理

后续将继续实现：

- 完整的零售业务功能
- 更多的系统集成
- 报表和分析功能
- 向多租户架构的迁移
