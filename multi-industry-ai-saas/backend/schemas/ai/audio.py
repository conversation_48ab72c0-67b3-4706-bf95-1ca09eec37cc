#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
AI 音频 Schema
"""

from typing import Optional, List, Dict, Any, Union
from datetime import datetime
import uuid
from pydantic import BaseModel, Field, validator

class AITranscriptionRequest(BaseModel):
    """AI 音频转录请求 Schema"""
    audio_base64: str = Field(..., description="Base64 编码的音频数据")
    model_id: Optional[uuid.UUID] = Field(None, description="模型 ID，如果不指定则使用默认模型")
    config_id: Optional[uuid.UUID] = Field(None, description="配置 ID，如果不指定则使用默认配置")
    language: Optional[str] = Field(None, description="音频语言，如 zh, en 等")
    prompt: Optional[str] = Field(None, description="转录提示词，帮助模型理解特定术语")
    response_format: str = Field("text", description="响应格式：text, json, srt, vtt")
    temperature: Optional[float] = Field(None, ge=0, le=1, description="温度参数，控制随机性")

class AITranscriptionResponse(BaseModel):
    """AI 音频转录响应 Schema"""
    text: str = Field(..., description="转录文本")
    language: Optional[str] = Field(None, description="检测到的语言")
    duration: Optional[float] = Field(None, description="音频时长（秒）")
    segments: Optional[List[Dict[str, Any]]] = Field(None, description="音频分段信息")

class AITextToSpeechRequest(BaseModel):
    """AI 文本转语音请求 Schema"""
    text: str = Field(..., description="要转换为语音的文本")
    model_id: Optional[uuid.UUID] = Field(None, description="模型 ID，如果不指定则使用默认模型")
    config_id: Optional[uuid.UUID] = Field(None, description="配置 ID，如果不指定则使用默认配置")
    voice: str = Field("alloy", description="语音声音：alloy, echo, fable, onyx, nova, shimmer")
    response_format: str = Field("mp3", description="响应格式：mp3, opus, aac, flac")
    speed: float = Field(1.0, ge=0.25, le=4.0, description="语音速度，0.25-4.0")

class AITextToSpeechResponse(BaseModel):
    """AI 文本转语音响应 Schema"""
    audio_base64: str = Field(..., description="Base64 编码的音频数据")
    format: str = Field(..., description="音频格式")

class AIAudioErrorResponse(BaseModel):
    """AI 音频错误响应 Schema"""
    success: bool = Field(False, description="是否成功")
    message: str = Field(..., description="错误信息")
    error: Optional[Dict[str, Any]] = Field(None, description="错误详情")
