import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Form,
  Input,
  Button,
  Select,
  DatePicker,
  Space,
  Tag,
  Typography,
  Drawer,
  Descriptions,
  Tooltip,
  Row,
  Col
} from 'antd';
import {
  SearchOutlined,
  ReloadOutlined,
  EyeOutlined,
  UserOutlined,
  ClockCircleOutlined,
  TagOutlined,
  FileTextOutlined
} from '@ant-design/icons';
import apiService from '../../../services/api';
import dayjs from "dayjs";

const { Title, Text } = Typography;
const { Option } = Select;
const { RangePicker } = DatePicker;

/**
 * 操作日志组件
 */
const OperationLogs = () => {
  const [logs, setLogs] = useState([]);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [filters, setFilters] = useState({});
  const [modules, setModules] = useState([]);
  const [actions, setActions] = useState([]);
  const [resourceTypes, setResourceTypes] = useState([]);
  const [detailVisible, setDetailVisible] = useState(false);
  const [currentLog, setCurrentLog] = useState(null);
  const [form] = Form.useForm();

  // 获取操作日志列表
  const fetchLogs = async (params = {}) => {
    setLoading(true);
    try {
      const queryParams = {
        page: page,
        size: pageSize,
        ...filters,
        ...params
      };
      
      const response = await apiService.project.operationLogs.getLogs(queryParams);
      if (response && response.items) {
        setLogs(response.items);
        setTotal(response.total || 0);
      }
    } catch (error) {
      console.error('获取操作日志失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 获取模块列表
  const fetchModules = async () => {
    try {
      const response = await apiService.project.operationLogs.getModules();
      if (response && response.success) {
        setModules(response.modules || []);
        setActions(response.actions || []);
        setResourceTypes(response.resource_types || []);
      }
    } catch (error) {
      console.error('获取模块列表失败:', error);
    }
  };

  // 初始化
  useEffect(() => {
    fetchLogs();
    fetchModules();
  }, [page, pageSize]);

  // 处理搜索
  const handleSearch = (values) => {
    const searchParams = { ...values };
    
    // 处理日期范围
    if (values.dateRange && values.dateRange.length === 2) {
      searchParams.start_date = values.dateRange[0].format('YYYY-MM-DD HH:mm:ss');
      searchParams.end_date = values.dateRange[1].format('YYYY-MM-DD HH:mm:ss');
      delete searchParams.dateRange;
    }
    
    setFilters(searchParams);
    setPage(1);
    fetchLogs({ ...searchParams, page: 1 });
  };

  // 处理重置
  const handleReset = () => {
    form.resetFields();
    setFilters({});
    setPage(1);
    fetchLogs({ page: 1 });
  };

  // 处理查看详情
  const handleViewDetail = (log) => {
    setCurrentLog(log);
    setDetailVisible(true);
  };

  // 处理分页变化
  const handleTableChange = (pagination) => {
    setPage(pagination.current);
    setPageSize(pagination.pageSize);
  };

  // 获取操作类型颜色
  const getActionColor = (action) => {
    const colorMap = {
      create: 'green',
      update: 'blue',
      delete: 'red',
      login: 'purple',
      logout: 'orange',
      approve: 'cyan',
      reject: 'magenta'
    };
    return colorMap[action] || 'default';
  };

  // 表格列定义
  const columns = [
    {
      title: '操作时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (text) => dayjs(text).format('YYYY-MM-DD HH:mm:ss')
    },
    {
      title: '操作人',
      dataIndex: 'user_name',
      key: 'user_name',
      render: (text) => text || '未知用户'
    },
    {
      title: '模块',
      dataIndex: 'module',
      key: 'module',
      render: (text) => {
        const moduleMap = {
          user: '用户管理',
          role: '角色管理',
          store: '门店管理',
          warehouse: '仓库管理',
          product: '产品管理',
          supplier: '供应商管理',
          inventory: '库存管理',
          purchase: '采购管理',
          sales: '销售管理',
          system: '系统设置'
        };
        return moduleMap[text] || text;
      }
    },
    {
      title: '操作类型',
      dataIndex: 'action',
      key: 'action',
      render: (text) => {
        const actionMap = {
          create: '创建',
          update: '更新',
          delete: '删除',
          login: '登录',
          logout: '登出',
          approve: '审批',
          reject: '拒绝'
        };
        return (
          <Tag color={getActionColor(text)}>
            {actionMap[text] || text}
          </Tag>
        );
      }
    },
    {
      title: '资源类型',
      dataIndex: 'resource_type',
      key: 'resource_type',
      render: (text) => {
        const resourceMap = {
          user: '用户',
          role: '角色',
          store: '门店',
          warehouse: '仓库',
          product: '产品',
          supplier: '供应商',
          inventory: '库存',
          purchase_order: '采购单',
          sales_report: '销售报表',
          system_config: '系统配置'
        };
        return resourceMap[text] || text;
      }
    },
    {
      title: '操作描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Button
          type="link"
          icon={<EyeOutlined />}
          onClick={() => handleViewDetail(record)}
        >
          详情
        </Button>
      )
    }
  ];

  return (
    <Card title="操作日志">
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSearch}
      >
        <Row gutter={16}>
          <Col span={6}>
            <Form.Item name="module" label="模块">
              <Select placeholder="请选择模块" allowClear>
                {modules.map(module => (
                  <Option key={module} value={module}>
                    {module === 'user' ? '用户管理' :
                     module === 'role' ? '角色管理' :
                     module === 'store' ? '门店管理' :
                     module === 'warehouse' ? '仓库管理' :
                     module === 'product' ? '产品管理' :
                     module === 'supplier' ? '供应商管理' :
                     module === 'inventory' ? '库存管理' :
                     module === 'purchase' ? '采购管理' :
                     module === 'sales' ? '销售管理' :
                     module === 'system' ? '系统设置' :
                     module}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item name="action" label="操作类型">
              <Select placeholder="请选择操作类型" allowClear>
                {actions.map(action => (
                  <Option key={action} value={action}>
                    {action === 'create' ? '创建' :
                     action === 'update' ? '更新' :
                     action === 'delete' ? '删除' :
                     action === 'login' ? '登录' :
                     action === 'logout' ? '登出' :
                     action === 'approve' ? '审批' :
                     action === 'reject' ? '拒绝' :
                     action}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item name="resource_type" label="资源类型">
              <Select placeholder="请选择资源类型" allowClear>
                {resourceTypes.map(type => (
                  <Option key={type} value={type}>
                    {type === 'user' ? '用户' :
                     type === 'role' ? '角色' :
                     type === 'store' ? '门店' :
                     type === 'warehouse' ? '仓库' :
                     type === 'product' ? '产品' :
                     type === 'supplier' ? '供应商' :
                     type === 'inventory' ? '库存' :
                     type === 'purchase_order' ? '采购单' :
                     type === 'sales_report' ? '销售报表' :
                     type === 'system_config' ? '系统配置' :
                     type}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item name="search" label="关键词搜索">
              <Input placeholder="请输入关键词" />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item name="dateRange" label="操作时间范围">
              <RangePicker
                showTime
                style={{ width: '100%' }}
                format="YYYY-MM-DD HH:mm:ss"
              />
            </Form.Item>
          </Col>
          <Col span={12} style={{ textAlign: 'right', marginTop: 29 }}>
            <Space>
              <Button type="primary" htmlType="submit" icon={<SearchOutlined />}>
                搜索
              </Button>
              <Button onClick={handleReset} icon={<ReloadOutlined />}>
                重置
              </Button>
            </Space>
          </Col>
        </Row>
      </Form>

      <Table
        columns={columns}
        dataSource={logs}
        rowKey="id"
        loading={loading}
        pagination={{
          current: page,
          pageSize: pageSize,
          total: total,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `共 ${total} 条记录`
        }}
        onChange={handleTableChange}
      />

      <Drawer
        title="操作日志详情"
        width={600}
        placement="right"
        onClose={() => setDetailVisible(false)}
        visible={detailVisible}
      >
        {currentLog && (
          <>
            <Descriptions bordered column={1}>
              <Descriptions.Item label="操作时间">
                <ClockCircleOutlined style={{ marginRight: 8 }} />
                {dayjs(currentLog.created_at).format('YYYY-MM-DD HH:mm:ss')}
              </Descriptions.Item>
              <Descriptions.Item label="操作人">
                <UserOutlined style={{ marginRight: 8 }} />
                {currentLog.user_name || '未知用户'}
              </Descriptions.Item>
              <Descriptions.Item label="IP地址">
                {currentLog.ip_address || '未知'}
              </Descriptions.Item>
              <Descriptions.Item label="用户代理">
                <div style={{ wordBreak: 'break-all' }}>
                  {currentLog.user_agent || '未知'}
                </div>
              </Descriptions.Item>
              <Descriptions.Item label="模块">
                <TagOutlined style={{ marginRight: 8 }} />
                {currentLog.module === 'user' ? '用户管理' :
                 currentLog.module === 'role' ? '角色管理' :
                 currentLog.module === 'store' ? '门店管理' :
                 currentLog.module === 'warehouse' ? '仓库管理' :
                 currentLog.module === 'product' ? '产品管理' :
                 currentLog.module === 'supplier' ? '供应商管理' :
                 currentLog.module === 'inventory' ? '库存管理' :
                 currentLog.module === 'purchase' ? '采购管理' :
                 currentLog.module === 'sales' ? '销售管理' :
                 currentLog.module === 'system' ? '系统设置' :
                 currentLog.module}
              </Descriptions.Item>
              <Descriptions.Item label="操作类型">
                <Tag color={getActionColor(currentLog.action)}>
                  {currentLog.action === 'create' ? '创建' :
                   currentLog.action === 'update' ? '更新' :
                   currentLog.action === 'delete' ? '删除' :
                   currentLog.action === 'login' ? '登录' :
                   currentLog.action === 'logout' ? '登出' :
                   currentLog.action === 'approve' ? '审批' :
                   currentLog.action === 'reject' ? '拒绝' :
                   currentLog.action}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="资源类型">
                {currentLog.resource_type === 'user' ? '用户' :
                 currentLog.resource_type === 'role' ? '角色' :
                 currentLog.resource_type === 'store' ? '门店' :
                 currentLog.resource_type === 'warehouse' ? '仓库' :
                 currentLog.resource_type === 'product' ? '产品' :
                 currentLog.resource_type === 'supplier' ? '供应商' :
                 currentLog.resource_type === 'inventory' ? '库存' :
                 currentLog.resource_type === 'purchase_order' ? '采购单' :
                 currentLog.resource_type === 'sales_report' ? '销售报表' :
                 currentLog.resource_type === 'system_config' ? '系统配置' :
                 currentLog.resource_type}
              </Descriptions.Item>
              <Descriptions.Item label="资源ID">
                {currentLog.resource_id || '无'}
              </Descriptions.Item>
              <Descriptions.Item label="操作描述">
                <FileTextOutlined style={{ marginRight: 8 }} />
                {currentLog.description}
              </Descriptions.Item>
            </Descriptions>

            {currentLog.details && (
              <div style={{ marginTop: 16 }}>
                <Title level={5}>操作详情</Title>
                <pre style={{ 
                  backgroundColor: '#f5f5f5', 
                  padding: 16, 
                  borderRadius: 4,
                  maxHeight: 300,
                  overflow: 'auto'
                }}>
                  {JSON.stringify(currentLog.details, null, 2)}
                </pre>
              </div>
            )}
          </>
        )}
      </Drawer>
    </Card>
  );
};

export default OperationLogs;
