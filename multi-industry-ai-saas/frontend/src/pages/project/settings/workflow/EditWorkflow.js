import React, { useState, useEffect, useCallback } from 'react';
import {
  Card, Button, Form, Input, Select, Space, 
  Modal, message, Row, Col, Tag,
  Empty, Spin, Typography, Alert, Tabs, Slider
} from 'antd';
import {
  ArrowLeftOutlined, SaveOutlined, PlusOutlined,
  RobotOutlined, EyeOutlined, BranchesOutlined,
  CheckCircleOutlined, ExclamationCircleOutlined, FileTextOutlined, LineChartOutlined,
  PlayCircleOutlined, ThunderboltOutlined, UserOutlined,
  ClockCircleOutlined, MailOutlined, AuditOutlined,
  ApiOutlined, DatabaseOutlined, SecurityScanOutlined,
  NotificationOutlined, FormOutlined, FileSearchOutlined
} from '@ant-design/icons';
import { useNavigate, useParams } from 'react-router-dom';
import ReactFlow, { 
  Controls, 
  Background, 
  MiniMap,
  MarkerType,
  addEdge,
  useNodesState,
  useEdgesState
} from 'reactflow';
import 'reactflow/dist/style.css';
import apiService from '../../../../services/api';

const { Option } = Select;
const { TextArea } = Input;
const { Title } = Typography;

const EditWorkflow = () => {
  const navigate = useNavigate();
  const { id } = useParams();
  
  // 状态管理
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [workflow, setWorkflow] = useState(null);
  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);
  const [nodeModalVisible, setNodeModalVisible] = useState(false);
  const [aiModels, setAiModels] = useState([]);
  const [selectedAiNode, setSelectedAiNode] = useState(null);
  const [aiNodeModalVisible, setAiNodeModalVisible] = useState(false);
  const [selectedNode, setSelectedNode] = useState(null);
  const [workflowData, setWorkflowData] = useState({});
  const [nodeForm] = Form.useForm();

  // 节点类型定义，包含AI节点
  const nodeTypes = [
    { value: 'start', label: '开始节点', icon: <PlayCircleOutlined />, color: 'green' },
    { value: 'end', label: '结束节点', icon: <CheckCircleOutlined />, color: 'red' },
    { value: 'approval', label: '审批节点', icon: <AuditOutlined />, color: 'blue' },
    { value: 'notification', label: '通知节点', icon: <NotificationOutlined />, color: 'orange' },
    { value: 'condition', label: '条件节点', icon: <BranchesOutlined />, color: 'cyan' },
    { value: 'action', label: '执行节点', icon: <ThunderboltOutlined />, color: 'purple' },
    { value: 'human_task', label: '人工任务', icon: <UserOutlined />, color: 'magenta' },
    { value: 'timer', label: '定时节点', icon: <ClockCircleOutlined />, color: 'gold' },
    { value: 'email', label: '邮件节点', icon: <MailOutlined />, color: 'lime' },
    { value: 'api_call', label: 'API调用', icon: <ApiOutlined />, color: 'volcano' },
    { value: 'data_process', label: '数据处理', icon: <DatabaseOutlined />, color: 'geekblue' },
    { value: 'file_process', label: '文件处理', icon: <FileSearchOutlined />, color: 'cyan' },
    { value: 'form_fill', label: '表单填写', icon: <FormOutlined />, color: 'blue' },
    { value: 'ai_vision', label: 'AI视觉识别', icon: <EyeOutlined />, color: 'purple' },
    { value: 'ai_analysis', label: 'AI数据分析', icon: <LineChartOutlined />, color: 'purple' },
    { value: 'ai_evaluation', label: 'AI评估', icon: <SecurityScanOutlined />, color: 'purple' }
  ];

  // AI节点类型配置
  const aiNodeTypes = [
    {
      type: 'ai_vision',
      name: 'AI视觉分析',
      icon: <EyeOutlined />,
      description: '使用计算机视觉模型分析图像',
      defaultConfig: {
        model_type: 'vision',
        input_format: 'image',
        output_format: 'json',
        confidence_threshold: 0.8
      }
    },
    {
      type: 'ai_multimodal',
      name: 'AI多模态分析',
      icon: <RobotOutlined />,
      description: '结合多种数据源进行AI分析',
      defaultConfig: {
        model_type: 'multimodal',
        input_sources: ['text', 'image', 'structured_data'],
        fusion_strategy: 'late_fusion',
        confidence_threshold: 0.85
      }
    },
    {
      type: 'ai_nlp',
      name: 'AI文本分析',
      icon: <FileTextOutlined />,
      description: '自然语言处理和文本分析',
      defaultConfig: {
        model_type: 'nlp',
        tasks: ['sentiment', 'classification', 'extraction'],
        language: 'zh-cn'
      }
    },
    {
      type: 'ai_prediction',
      name: 'AI预测分析',
      icon: <LineChartOutlined />,
      description: '基于历史数据进行预测',
      defaultConfig: {
        model_type: 'prediction',
        prediction_horizon: '7d',
        confidence_interval: 0.95
      }
    }
  ];

  // 预设工作流模板
  const workflowTemplates = [
    {
      id: 'loss_workflow_ai',
      name: '智能报损工作流',
      description: '结合AI视觉检测的自动化报损流程',
      category: 'inventory',
      aiNodes: ['ai_vision', 'ai_multimodal'],
      estimatedTime: '30分钟',
      automationLevel: '85%'
    },
    {
      id: 'purchase_approval_ai',
      name: '智能采购审批',
      description: '基于AI分析的采购决策支持',
      category: 'purchase',
      aiNodes: ['ai_prediction', 'ai_nlp'],
      estimatedTime: '45分钟',
      automationLevel: '70%'
    },
    {
      id: 'quality_inspection_ai',
      name: 'AI质量检测',
      description: '自动化商品质量检测流程',
      category: 'quality',
      aiNodes: ['ai_vision', 'ai_multimodal'],
      estimatedTime: '20分钟',
      automationLevel: '90%'
    }
  ];

  // 初始化
  useEffect(() => {
    if (id) {
      fetchWorkflowDetail();
      fetchAiModels();
    }
  }, [id]);

  // 获取工作流详情
  const fetchWorkflowDetail = async () => {
    setLoading(true);
    try {
      const response = await apiService.project.businessSettings.getWorkflowDetail(id);
      console.log('工作流详情API响应:', response);
      
      // 后端返回格式: { success: true, data: {...} }
      if (response && response.success && response.data) {
        const workflowData = response.data;
        setWorkflow(workflowData);
        
        // 处理节点数据，确保包含位置信息
        const processedNodes = (workflowData.nodes || []).map((node, index) => ({
          ...node,
          position: node.config?.position || { 
            x: (index % 4) * 200 + 50, 
            y: Math.floor(index / 4) * 150 + 50 
          }
        }));
        
        // 转换为ReactFlow格式
        const reactFlowNodes = convertNodesToReactFlow(processedNodes);
        const reactFlowEdges = convertEdgesToReactFlow(workflowData.transitions || []);
        
        setNodes(reactFlowNodes);
        setEdges(reactFlowEdges);
        
        // 设置表单初始值
        form.setFieldsValue({
          name: workflowData.name,
          description: workflowData.description,
          is_default: workflowData.is_default,
          is_active: workflowData.is_active,
          notify_enabled: workflowData.config?.notify_enabled || false,
          notify_roles: workflowData.config?.notify_roles || [],
          notify_template: workflowData.config?.notify_template || ''
        });
      } else {
        console.error('工作流数据格式错误:', response);
        message.error('工作流数据格式错误');
      }
    } catch (error) {
      console.error('获取工作流详情失败:', error);
      message.error(`获取工作流详情失败: ${error.message || error.detail || '未知错误'}`);
    } finally {
      setLoading(false);
    }
  };

  // 获取AI模型列表
  const fetchAiModels = async () => {
    try {
      const response = await apiService.ai.getModels();
      if (response && response.data) {
        setAiModels(response.data.filter(model => 
          model.capabilities?.supports_vision || 
          model.capabilities?.supports_analysis
        ));
      }
    } catch (error) {
      console.error('获取AI模型失败:', error);
    }
  };

  // 渲染AI节点配置
  const renderAiNodeConfig = () => (
    <Card title="AI节点配置" style={{ marginBottom: 16 }}>
      <Row gutter={[16, 16]}>
        {aiNodeTypes.map((nodeType, index) => (
          <Col xs={24} sm={12} lg={6} key={index}>
            <Card 
              size="small"
              hoverable
              onClick={() => handleAddAiNode(nodeType)}
              style={{ 
                border: selectedAiNode?.type === nodeType.type ? '2px solid #1890ff' : '1px solid #d9d9d9',
                cursor: 'pointer'
              }}
            >
              <div style={{ textAlign: 'center' }}>
                <div style={{ fontSize: 24, marginBottom: 8 }}>
                  {nodeType.icon}
                </div>
                <div style={{ fontWeight: 500, marginBottom: 4 }}>
                  {nodeType.name}
                </div>
                <div style={{ fontSize: 12, color: '#666' }}>
                  {nodeType.description}
                </div>
              </div>
            </Card>
          </Col>
        ))}
      </Row>
    </Card>
  );

  // 渲染工作流模板选择
  const renderWorkflowTemplates = () => (
    <Card title="智能工作流模板" style={{ marginBottom: 16 }}>
      <Alert
        message="AI增强工作流"
        description="选择预设的AI工作流模板，快速构建智能化业务流程。"
        type="info"
        showIcon
        style={{ marginBottom: 16 }}
      />
      <Row gutter={[16, 16]}>
        {workflowTemplates.map((template, index) => (
          <Col xs={24} lg={8} key={index}>
            <Card 
              size="small"
              hoverable
              onClick={() => handleSelectTemplate(template)}
              style={{ cursor: 'pointer' }}
            >
              <div style={{ marginBottom: 12 }}>
                <div style={{ fontWeight: 500, marginBottom: 4 }}>
                  {template.name}
                </div>
                <div style={{ fontSize: 12, color: '#666', marginBottom: 8 }}>
                  {template.description}
                </div>
                <Space>
                  <Tag color="blue">{template.category}</Tag>
                  <Tag color="green">自动化 {template.automationLevel}</Tag>
                </Space>
              </div>
              <div style={{ fontSize: 12 }}>
                <div>AI节点: {template.aiNodes.join(', ')}</div>
                <div>预计时间: {template.estimatedTime}</div>
              </div>
            </Card>
          </Col>
        ))}
      </Row>
    </Card>
  );

  // 渲染工作流设计器
  const renderWorkflowDesigner = () => (
    <Card title="工作流设计器" style={{ marginBottom: 16 }}>
      <div style={{ marginBottom: 16 }}>
        <Space>
          <Button icon={<PlusOutlined />} onClick={handleAddNode}>
            添加节点
          </Button>
          <Button icon={<RobotOutlined />} onClick={() => setAiNodeModalVisible(true)}>
            添加AI节点
          </Button>
          <Button icon={<SaveOutlined />} type="primary" onClick={handleSave}>
            保存工作流
          </Button>
        </Space>
      </div>

      {/* ReactFlow工作流画布 */}
      <div style={{ height: 500, border: '1px solid #d9d9d9', borderRadius: 6 }}>
        {nodes.length === 0 ? (
          <div style={{ 
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
            color: '#666'
          }}>
            <RobotOutlined style={{ fontSize: 48, marginBottom: 16 }} />
            <div>点击上方按钮开始设计您的AI工作流</div>
            <div style={{ fontSize: 12, marginTop: 8 }}>
              或选择预设模板快速开始
            </div>
          </div>
        ) : (
          <ReactFlow
            nodes={nodes}
            edges={edges}
            onNodesChange={onNodesChange}
            onEdgesChange={onEdgesChange}
            onConnect={onConnect}
            onNodeClick={(event, node) => setSelectedNode(node)}
            fitView
            attributionPosition="bottom-left"
          >
            <Controls />
            <MiniMap />
            <Background variant="dots" gap={12} size={1} />
          </ReactFlow>
        )}
      </div>
    </Card>
  );

  // 处理函数
  const handleAddAiNode = (nodeType) => {
    setSelectedAiNode(nodeType);
    setAiNodeModalVisible(true);
  };

  const handleSelectTemplate = (template) => {
    Modal.confirm({
      title: '应用工作流模板',
      content: `确定要应用"${template.name}"模板吗？这将替换当前的工作流设计。`,
      onOk: () => {
        // 这里可以根据模板加载对应的工作流配置
        generateMockData(); // 暂时使用模拟数据
        message.success('模板应用成功');
      }
    });
  };

  const handleAddNode = () => {
    setNodeModalVisible(true);
  };

  const handleAddConnection = () => {
    if (nodes.length < 2) {
      message.warning('至少需要两个节点才能创建连接');
      return;
    }
    // 这里应该打开连接配置对话框
    message.info('请在画布上选择要连接的节点');
  };

  const handleSave = async () => {
    try {
      setSaving(true);
      
      // 将ReactFlow格式的数据转换回后端格式
      const backendNodes = nodes.map(node => ({
        id: node.id,
        type: getNodeTypeFromReactFlow(node),
        name: getNodeNameFromReactFlow(node),
        position: node.position,
        config: {}
      }));

      const backendEdges = edges.map(edge => ({
        id: edge.id,
        source_node_id: edge.source,
        target_node_id: edge.target,
        name: edge.label || '',
        condition: null
      }));

      const workflowConfig = {
        ...workflow,
        nodes: backendNodes,
        transitions: backendEdges,
        updated_at: new Date().toISOString()
      };

      const response = await apiService.project.businessSettings.updateWorkflow(id, workflowConfig);
      if (response && response.success) {
        message.success('工作流保存成功');
        navigate('/project/settings/business');
      } else {
        message.error('工作流保存失败');
      }
    } catch (error) {
      console.error('保存工作流失败:', error);
      message.error('保存工作流失败');
    } finally {
      setSaving(false);
    }
  };

  // 从ReactFlow节点获取节点类型
  const getNodeTypeFromReactFlow = (node) => {
    // 这里需要根据node的特征来推断类型，暂时返回默认值
    return 'action'; // 可以根据需要扩展
  };

  // 从ReactFlow节点获取节点名称
  const getNodeNameFromReactFlow = (node) => {
    return node.data?.label?.props?.children?.[1]?.props?.children || `节点${node.id}`;
  };

  // 生成模拟数据
  const generateMockData = () => {
    const mockNodes = [
      {
        id: 'start',
        type: 'start',
        name: '开始',
        description: '工作流开始节点',
        position: { x: 100, y: 100 },
        config: {}
      },
      {
        id: 'loss_detection',
        type: 'ai_vision',
        name: 'AI视觉检测',
        description: '使用AI视觉模型检测损耗情况',
        position: { x: 300, y: 100 },
        config: {
          aiModel: 'vision_damage_assessment',
          inputFields: ['product_image', 'product_info'],
          outputFields: ['damage_level', 'weight_estimate', 'loss_amount'],
          confidence_threshold: 0.8,
          auto_approve_threshold: 0.95
        }
      },
      {
        id: 'ai_assessment',
        type: 'ai_multimodal',
        name: 'AI多模态评估',
        description: '结合图像、文本和历史数据进行损耗评估',
        position: { x: 500, y: 100 },
        config: {
          aiModel: 'multimodal_loss_assessment',
          inputSources: ['image', 'text', 'historical_data'],
          assessmentCriteria: {
            damage_severity: 'high',
            weight_accuracy: 0.9,
            value_estimation: 'market_price'
          },
          outputFormat: 'structured_report'
        }
      },
      {
        id: 'decision_gate',
        type: 'condition',
        name: '自动审核决策',
        description: '基于AI评估结果自动决策',
        position: { x: 700, y: 100 },
        config: {
          conditions: [
            {
              field: 'confidence_score',
              operator: '>=',
              value: 0.95,
              action: 'auto_approve'
            },
            {
              field: 'loss_amount',
              operator: '<=',
              value: 100,
              action: 'auto_approve'
            },
            {
              field: 'damage_level',
              operator: '==',
              value: 'minor',
              action: 'auto_approve'
            }
          ],
          default_action: 'manual_review'
        }
      },
      {
        id: 'auto_approve',
        type: 'action',
        name: '自动通过',
        description: 'AI评估通过，自动审核',
        position: { x: 600, y: 250 },
        config: {
          action: 'approve_loss',
          notifications: ['store_manager', 'finance_team'],
          update_inventory: true
        }
      },
      {
        id: 'manual_review',
        type: 'human_task',
        name: '人工审核',
        description: '需要采购经理人工审核',
        position: { x: 800, y: 250 },
        config: {
          assignee_role: 'purchase_manager',
          deadline_hours: 24,
          required_fields: ['review_comment', 'approve_decision'],
          escalation_rules: {
            '24h': 'department_manager',
            '48h': 'general_manager'
          }
        }
      },
      {
        id: 'final_approval',
        type: 'action',
        name: '最终处理',
        description: '完成报损流程',
        position: { x: 700, y: 400 },
        config: {
          actions: ['update_inventory', 'generate_report', 'notify_finance'],
          generate_analytics: true
        }
      },
      {
        id: 'end',
        type: 'end',
        name: '结束',
        description: '工作流结束节点',
        position: { x: 900, y: 400 },
        config: {}
      }
    ];

    const mockConnections = [
      { id: 'e1', source: 'start', target: 'loss_detection', name: '提交审核' },
      { id: 'e2', source: 'loss_detection', target: 'ai_assessment', name: '视觉检测' },
      { id: 'e3', source: 'ai_assessment', target: 'decision_gate', name: '多模态评估' },
      { id: 'e4', source: 'decision_gate', target: 'auto_approve', name: '自动通过' },
      { id: 'e5', source: 'decision_gate', target: 'manual_review', name: '人工审核' },
      { id: 'e6', source: 'auto_approve', target: 'final_approval', name: '财务确认' },
      { id: 'e7', source: 'manual_review', target: 'final_approval', name: '审核完成' },
      { id: 'e8', source: 'final_approval', target: 'end', name: '流程结束' }
    ];

    // 转换为ReactFlow格式
    const reactFlowNodes = convertNodesToReactFlow(mockNodes);
    const reactFlowEdges = convertEdgesToReactFlow(mockConnections);

    setWorkflowData({
      ...workflowData,
      nodes: mockNodes,
      edges: mockConnections
    });
    setNodes(reactFlowNodes);
    setEdges(reactFlowEdges);
    
    // 设置表单数据
    form.setFieldsValue({
      name: '智能报损工作流',
      type: 'ai_enhanced',
      status: 'active',
      description: '结合AI视觉检测和多模态分析的智能报损处理流程，实现自动初审和智能决策。'
    });
  };

  // 处理边连接
  const onConnect = useCallback(
    (params) => setEdges((eds) => addEdge({ ...params, type: 'smoothstep', animated: true }, eds)),
    [setEdges]
  );

  // 转换节点数据为ReactFlow格式
  const convertNodesToReactFlow = (workflowNodes) => {
    return workflowNodes.map((node, index) => {
      const nodeType = nodeTypes.find(type => type.value === node.type);
      
      return {
        id: node.id,
        type: 'default',
        data: { 
          label: (
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: 18, marginBottom: 4, color: nodeType?.color || '#666' }}>
                {getNodeIcon(node.type)}
              </div>
              <div style={{ fontWeight: 500, fontSize: 12 }}>{node.name}</div>
              {node.type.startsWith('ai_') && (
                <Tag size="small" color="blue" style={{ marginTop: 2 }}>AI</Tag>
              )}
            </div>
          ) 
        },
        position: node.position || { x: (index % 4) * 200 + 50, y: Math.floor(index / 4) * 150 + 50 },
        style: {
          background: getNodeBackground(node.type),
          border: `2px solid ${nodeType?.color || '#d9d9d9'}`,
          borderRadius: 8,
          padding: 8,
          width: 120,
          height: 80
        }
      };
    });
  };

  // 转换边数据为ReactFlow格式
  const convertEdgesToReactFlow = (workflowEdges) => {
    return workflowEdges.map(edge => ({
      id: edge.id || `${edge.source || edge.source_node_id}-${edge.target || edge.target_node_id}`,
      source: edge.source || edge.source_node_id,
      target: edge.target || edge.target_node_id,
      type: 'smoothstep',
      animated: true,
      label: edge.name,
      labelStyle: { fill: '#666', fontWeight: 500 },
      style: { stroke: '#1890ff', strokeWidth: 2 },
      markerEnd: {
        type: MarkerType.ArrowClosed,
        width: 20,
        height: 20,
        color: '#1890ff',
      }
    }));
  };

  // 获取节点图标
  const getNodeIcon = (type) => {
    const iconMap = {
      'start': <PlayCircleOutlined />,
      'end': <CheckCircleOutlined />,
      'approval': <AuditOutlined />,
      'notification': <NotificationOutlined />,
      'condition': <BranchesOutlined />,
      'action': <ThunderboltOutlined />,
      'human_task': <UserOutlined />,
      'timer': <ClockCircleOutlined />,
      'email': <MailOutlined />,
      'api_call': <ApiOutlined />,
      'data_process': <DatabaseOutlined />,
      'file_process': <FileSearchOutlined />,
      'form_fill': <FormOutlined />,
      'ai_vision': <EyeOutlined />,
      'ai_multimodal': <RobotOutlined />,
      'ai_nlp': <FileTextOutlined />,
      'ai_prediction': <LineChartOutlined />,
      'ai_analysis': <LineChartOutlined />,
      'ai_evaluation': <SecurityScanOutlined />
    };
    return iconMap[type] || <ExclamationCircleOutlined />;
  };

  // 获取节点背景色
  const getNodeBackground = (type) => {
    const backgroundMap = {
      'start': '#f6ffed',
      'end': '#fff2f0',
      'approval': '#e6f7ff',
      'notification': '#fff7e6',
      'condition': '#f9f0ff',
      'action': '#f6ffed',
      'human_task': '#fff0f6',
      'timer': '#fffbe6',
      'email': '#f6ffed',
      'api_call': '#fff2e8',
      'data_process': '#f0f5ff',
      'file_process': '#e6fffb',
      'form_fill': '#e6f7ff',
      'ai_vision': '#f9f0ff',
      'ai_multimodal': '#f6ffed',
      'ai_nlp': '#f9f0ff',
      'ai_prediction': '#fff7e6',
      'ai_analysis': '#f9f0ff',
      'ai_evaluation': '#f9f0ff'
    };
    return backgroundMap[type] || '#fafafa';
  };

  if (loading) {
    return (
      <Card>
        <div style={{ textAlign: 'center', padding: '50px 0' }}>
          <Spin size="large" />
          <p style={{ marginTop: 16 }}>加载中...</p>
        </div>
      </Card>
    );
  }

  if (!workflow) {
    return (
      <Card>
        <Empty description="未找到工作流" />
        <div style={{ textAlign: 'center', marginTop: 16 }}>
          <Button type="primary" onClick={() => navigate('/project/settings/business')}>
            返回
          </Button>
        </div>
      </Card>
    );
  }

  return (
    <div className="edit-workflow">
      {/* 页面头部 */}
      <Card style={{ marginBottom: 16 }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Space>
              <Button 
                icon={<ArrowLeftOutlined />} 
                onClick={() => navigate('/project/settings/business')}
              >
                返回工作流列表
              </Button>
              <Title level={4} style={{ margin: 0 }}>
                {id ? '编辑工作流' : '创建工作流'}
              </Title>
            </Space>
          </Col>
          <Col>
            <Space>
              <Button onClick={() => generateMockData()}>
                加载示例
              </Button>
              <Button 
                type="primary" 
                icon={<SaveOutlined />}
                onClick={handleSave}
                loading={saving}
              >
                保存工作流
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 基本信息 */}
      <Card title="基本信息" style={{ marginBottom: 16 }}>
        <Form form={form} layout="vertical">
          <Row gutter={16}>
            <Col xs={24} md={8}>
              <Form.Item
                label="工作流名称"
                name="name"
                rules={[{ required: true, message: '请输入工作流名称' }]}
              >
                <Input placeholder="请输入工作流名称" />
              </Form.Item>
            </Col>
            <Col xs={24} md={8}>
              <Form.Item
                label="工作流类型"
                name="type"
                rules={[{ required: true, message: '请选择工作流类型' }]}
              >
                <Select placeholder="请选择工作流类型">
                  <Option value="approval">审批流程</Option>
                  <Option value="automation">自动化流程</Option>
                  <Option value="ai_enhanced">AI增强流程</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} md={8}>
              <Form.Item
                label="状态"
                name="status"
              >
                <Select>
                  <Option value="draft">草稿</Option>
                  <Option value="active">激活</Option>
                  <Option value="inactive">停用</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Row>
            <Col xs={24}>
              <Form.Item
                label="工作流描述"
                name="description"
              >
                <TextArea rows={3} placeholder="请输入工作流描述" />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Card>

      {/* AI节点配置 */}
      {renderAiNodeConfig()}

      {/* 工作流模板 */}
      {renderWorkflowTemplates()}

      {/* 工作流设计器 */}
      {renderWorkflowDesigner()}

      {/* AI节点配置模态框 */}
      <Modal
        title={`配置${selectedAiNode?.name || 'AI节点'}`}
        open={aiNodeModalVisible}
        onOk={() => {
          if (selectedAiNode) {
            const newNodeId = `ai_${Date.now()}`;
            const newAiNode = {
              id: newNodeId,
              type: selectedAiNode.type,
              name: selectedAiNode.name,
              description: selectedAiNode.description,
              position: { x: Math.random() * 400 + 100, y: Math.random() * 200 + 100 },
              config: selectedAiNode.defaultConfig
            };
            
            // 转换为ReactFlow格式并添加
            const reactFlowNode = convertNodesToReactFlow([newAiNode])[0];
            setNodes((nds) => [...nds, reactFlowNode]);
            
            setAiNodeModalVisible(false);
            setSelectedAiNode(null);
            message.success('AI节点添加成功');
          }
        }}
        onCancel={() => {
          setAiNodeModalVisible(false);
          setSelectedAiNode(null);
        }}
        width={800}
        okText="添加节点"
        cancelText="取消"
      >
        {selectedAiNode && (
          <div>
            <Alert
              message={`${selectedAiNode.name}配置`}
              description={selectedAiNode.description}
              type="info"
              showIcon
              style={{ marginBottom: 16 }}
            />
            
            <Form layout="vertical">
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item label="节点名称">
                    <Input 
                      defaultValue={selectedAiNode.name}
                      placeholder="请输入节点名称"
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item label="节点描述">
                    <Input 
                      defaultValue={selectedAiNode.description}
                      placeholder="请输入节点描述"
                    />
                  </Form.Item>
                </Col>
              </Row>
              
              <Form.Item label="置信度阈值">
                <Slider
                  min={0.1}
                  max={1.0}
                  step={0.1}
                  defaultValue={selectedAiNode.defaultConfig?.confidence_threshold || 0.8}
                  marks={{
                    0.1: '0.1',
                    0.5: '0.5',
                    0.8: '0.8',
                    1.0: '1.0'
                  }}
                />
              </Form.Item>
              
              {selectedAiNode.type === 'ai_vision' && (
                <>
                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item label="输入图像格式">
                        <Select defaultValue="jpeg" style={{ width: '100%' }}>
                          <Option value="jpeg">JPEG</Option>
                          <Option value="png">PNG</Option>
                          <Option value="webp">WebP</Option>
                        </Select>
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item label="检测类型">
                        <Select mode="multiple" defaultValue={['damage', 'quality']} style={{ width: '100%' }}>
                          <Option value="damage">损坏检测</Option>
                          <Option value="quality">质量评估</Option>
                          <Option value="classification">分类识别</Option>
                          <Option value="counting">数量统计</Option>
                        </Select>
                      </Form.Item>
                    </Col>
                  </Row>
                </>
              )}
              
              {selectedAiNode.type === 'ai_multimodal' && (
                <>
                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item label="输入数据源">
                        <Select mode="multiple" defaultValue={['image', 'text']} style={{ width: '100%' }}>
                          <Option value="image">图像数据</Option>
                          <Option value="text">文本数据</Option>
                          <Option value="structured_data">结构化数据</Option>
                          <Option value="historical_data">历史数据</Option>
                        </Select>
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item label="融合策略">
                        <Select defaultValue="late_fusion" style={{ width: '100%' }}>
                          <Option value="early_fusion">早期融合</Option>
                          <Option value="late_fusion">晚期融合</Option>
                          <Option value="attention_fusion">注意力融合</Option>
                        </Select>
                      </Form.Item>
                    </Col>
                  </Row>
                </>
              )}
              
              {selectedAiNode.type === 'ai_nlp' && (
                <>
                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item label="处理任务">
                        <Select mode="multiple" defaultValue={['sentiment', 'classification']} style={{ width: '100%' }}>
                          <Option value="sentiment">情感分析</Option>
                          <Option value="classification">文本分类</Option>
                          <Option value="extraction">信息抽取</Option>
                          <Option value="translation">机器翻译</Option>
                        </Select>
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item label="语言类型">
                        <Select defaultValue="zh-cn" style={{ width: '100%' }}>
                          <Option value="zh-cn">简体中文</Option>
                          <Option value="en">英语</Option>
                          <Option value="auto">自动检测</Option>
                        </Select>
                      </Form.Item>
                    </Col>
                  </Row>
                </>
              )}
              
              {selectedAiNode.type === 'ai_prediction' && (
                <>
                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item label="预测时间范围">
                        <Select defaultValue="7d" style={{ width: '100%' }}>
                          <Option value="1d">1天</Option>
                          <Option value="7d">7天</Option>
                          <Option value="30d">30天</Option>
                          <Option value="90d">90天</Option>
                        </Select>
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item label="置信区间">
                        <Select defaultValue="0.95" style={{ width: '100%' }}>
                          <Option value="0.90">90%</Option>
                          <Option value="0.95">95%</Option>
                          <Option value="0.99">99%</Option>
                        </Select>
                      </Form.Item>
                    </Col>
                  </Row>
                </>
              )}
            </Form>
          </div>
        )}
      </Modal>

      {/* 普通节点选择弹窗 */}
      <Modal
        title="添加工作流节点"
        open={nodeModalVisible}
        onOk={() => {
          nodeForm.validateFields().then(values => {
            const newNodeId = `node_${Date.now()}`;
            const newNode = {
              id: newNodeId,
              type: values.type,
              name: values.name || nodeTypes.find(t => t.value === values.type)?.label,
              description: values.description || '',
              position: { x: Math.random() * 400 + 100, y: Math.random() * 200 + 100 },
              config: {}
            };
            
            // 转换为ReactFlow格式并添加
            const reactFlowNode = convertNodesToReactFlow([newNode])[0];
            setNodes((nds) => [...nds, reactFlowNode]);
            
            setNodeModalVisible(false);
            nodeForm.resetFields();
            message.success('节点添加成功');
          });
        }}
        onCancel={() => {
          setNodeModalVisible(false);
          nodeForm.resetFields();
        }}
        width={600}
        okText="添加节点"
        cancelText="取消"
      >
        <Form form={nodeForm} layout="vertical">
          <Form.Item 
            label="节点类型" 
            name="type"
            rules={[{ required: true, message: '请选择节点类型' }]}
          >
            <Select placeholder="请选择节点类型" style={{ width: '100%' }}>
              {nodeTypes.filter(type => !type.value.startsWith('ai_')).map(type => (
                <Option key={type.value} value={type.value}>
                  <Space>
                    {type.icon}
                    {type.label}
                  </Space>
                </Option>
              ))}
            </Select>
          </Form.Item>
          
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item 
                label="节点名称" 
                name="name"
                rules={[{ required: true, message: '请输入节点名称' }]}
              >
                <Input placeholder="请输入节点名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label="节点描述" name="description">
                <Input placeholder="请输入节点描述" />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    </div>
  );
};

export default EditWorkflow; 