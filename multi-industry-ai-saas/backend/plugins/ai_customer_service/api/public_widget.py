#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
AI 智能体客服公共组件配置 API

提供项目级别的公共组件配置管理接口。
"""

import logging
import uuid
import json
from typing import Dict, Any, Optional
from datetime import datetime
from fastapi import APIRouter, Request, HTTPException, status, Query, Path, Depends
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from sqlalchemy.ext.asyncio import AsyncSession

from db.database import get_db
from core.auth import get_current_user
from schemas.user import User

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter()

# 请求模型
class PublicWidgetConfigRequest(BaseModel):
    title: str = "AI智能客服"
    description: str = "我是您的专属智能客服，随时为您提供帮助"
    assistant_prompt: str = "你是一个专业、友好、耐心的智能客服助手。"
    theme_color: str = "#1890ff"
    position: str = "bottom-right"  # bottom-right, bottom-left, top-right, top-left
    size: str = "medium"  # small, medium, large
    auto_open: bool = False
    show_avatar: bool = True
    show_typing_indicator: bool = True
    enable_file_upload: bool = False
    enable_voice_input: bool = False
    auto_greeting: bool = True
    greeting_message: str = "您好！我是AI智能客服，有什么可以帮助您的吗？"
    custom_css: Optional[str] = None

@router.get(
    "/public-widget/config/{widget_id}",
    summary="获取公共组件配置",
    description="获取指定公共组件的配置信息"
)
async def get_public_widget_config(
    widget_id: str = Path(..., description="组件ID"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取公共组件配置"""
    try:
        from ..models.public_widget import PublicWidgetConfig
        from sqlalchemy import select
        
        # 查找配置
        result = await db.execute(
            select(PublicWidgetConfig).where(PublicWidgetConfig.widget_id == widget_id)
        )
        config_obj = result.scalars().first()
        
        if config_obj:
            config = config_obj.to_dict()
        else:
            # 返回默认配置
            config = {
                "widget_id": widget_id,
                "title": "AI智能客服",
                "description": "我是您的专属智能客服，随时为您提供帮助",
                "assistant_prompt": "你是一个专业、友好、耐心的智能客服助手。你的任务是：\n\n1. 为客户提供准确、有用的信息\n2. 以礼貌和专业的态度回应所有询问\n3. 当无法解决问题时，及时转接人工客服\n4. 始终保持积极正面的语调\n\n请记住：\n- 使用简洁明了的语言\n- 主动询问以更好地理解客户需求\n- 提供具体的解决方案\n- 对客户表示感谢和关心",
                "theme_color": "#1890ff",
                "position": "bottom-right",
                "size": "medium",
                "auto_open": False,
                "show_avatar": True,
                "show_typing_indicator": True,
                "enable_file_upload": False,
                "enable_voice_input": False,
                "auto_greeting": True,
                "greeting_message": "您好！我是AI智能客服，有什么可以帮助您的吗？",
                "business_hours_enabled": False,
                "business_hours": {
                    "timezone": "Asia/Shanghai",
                    "schedule": {
                        "monday": {"start": "09:00", "end": "18:00"},
                        "tuesday": {"start": "09:00", "end": "18:00"},
                        "wednesday": {"start": "09:00", "end": "18:00"},
                        "thursday": {"start": "09:00", "end": "18:00"},
                        "friday": {"start": "09:00", "end": "18:00"},
                        "saturday": {"start": "10:00", "end": "16:00"},
                        "sunday": {"start": "10:00", "end": "16:00"}
                    }
                },
                "offline_message": "客服暂时离线，请留言，我们会尽快回复您。",
                "quick_replies": [
                    "产品咨询",
                    "技术支持", 
                    "价格信息",
                    "联系方式"
                ]
            }
        
        return JSONResponse(content={
            "success": True,
            "data": config
        })
        
    except Exception as e:
        logger.error(f"获取公共组件配置失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取配置失败"
        )

@router.put(
    "/public-widget/config/{widget_id}",
    summary="更新公共组件配置",
    description="更新指定公共组件的配置"
)
async def update_public_widget_config(
    widget_id: str = Path(..., description="组件ID"),
    config_request: PublicWidgetConfigRequest = ...,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """更新公共组件配置"""
    try:
        from ..models.public_widget import PublicWidgetConfig
        from sqlalchemy import select
        
        # 获取当前用户的项目ID（假设已存储在用户上下文中）
        project_id = getattr(current_user, 'project_id', None)
        if not project_id:
            # 如果用户没有关联项目，使用默认项目ID
            project_id = uuid.UUID("*************-48ab-8092-e8eb7f663677")
        
        # 查找或创建配置
        result = await db.execute(
            select(PublicWidgetConfig).where(PublicWidgetConfig.widget_id == widget_id)
        )
        config_obj = result.scalars().first()
        
        if config_obj:
            # 更新现有配置
            config_obj.title = config_request.title
            config_obj.description = config_request.description
            config_obj.assistant_prompt = config_request.assistant_prompt
            config_obj.theme_color = config_request.theme_color
            config_obj.position = config_request.position
            config_obj.size = config_request.size
            config_obj.auto_open = config_request.auto_open
            config_obj.show_avatar = config_request.show_avatar
            config_obj.show_typing_indicator = config_request.show_typing_indicator
            config_obj.enable_file_upload = config_request.enable_file_upload
            config_obj.enable_voice_input = config_request.enable_voice_input
            config_obj.auto_greeting = config_request.auto_greeting
            config_obj.greeting_message = config_request.greeting_message
            config_obj.custom_css = config_request.custom_css
            config_obj.updated_at = datetime.utcnow()
        else:
            # 创建新配置
            config_obj = PublicWidgetConfig(
                project_id=project_id,
                widget_id=widget_id,
                title=config_request.title,
                description=config_request.description,
                assistant_prompt=config_request.assistant_prompt,
                theme_color=config_request.theme_color,
                position=config_request.position,
                size=config_request.size,
                auto_open=config_request.auto_open,
                show_avatar=config_request.show_avatar,
                show_typing_indicator=config_request.show_typing_indicator,
                enable_file_upload=config_request.enable_file_upload,
                enable_voice_input=config_request.enable_voice_input,
                auto_greeting=config_request.auto_greeting,
                greeting_message=config_request.greeting_message,
                custom_css=config_request.custom_css
            )
            db.add(config_obj)
        
        await db.commit()
        await db.refresh(config_obj)
        
        updated_config = config_obj.to_dict()
        
        return JSONResponse(content={
            "success": True,
            "message": "配置更新成功",
            "data": updated_config
        })
        
    except Exception as e:
        logger.error(f"更新公共组件配置失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新配置失败"
        )

@router.put(
    "/public-widget/config",
    summary="更新公共组件配置（默认）",
    description="更新默认公共组件的配置"
)
async def update_default_public_widget_config(
    config_request: PublicWidgetConfigRequest = ...,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """更新默认公共组件配置"""
    # 使用默认widget_id
    default_widget_id = "default"
    return await update_public_widget_config(default_widget_id, config_request, db, current_user)

@router.get(
    "/public-widget/analytics/{widget_id}",
    summary="获取公共组件分析数据",
    description="获取指定公共组件的使用分析数据"
)
async def get_public_widget_analytics(
    widget_id: str = Path(..., description="组件ID"),
    days: int = Query(7, ge=1, le=90, description="分析天数"),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取公共组件分析数据"""
    try:
        # 这里需要从数据库获取实际分析数据
        # 暂时返回模拟数据
        
        analytics = {
            "widget_id": widget_id,
            "period_days": days,
            "total_sessions": 156,
            "total_messages": 892,
            "avg_session_duration": 180,  # 秒
            "user_satisfaction": 4.2,
            "top_intents": [
                {"intent": "product_inquiry", "count": 45, "percentage": 28.8},
                {"intent": "technical_support", "count": 32, "percentage": 20.5},
                {"intent": "pricing", "count": 28, "percentage": 17.9},
                {"intent": "contact_info", "count": 25, "percentage": 16.0},
                {"intent": "complaint", "count": 15, "percentage": 9.6}
            ],
            "daily_stats": [
                {"date": "2025-01-01", "sessions": 22, "messages": 128},
                {"date": "2025-01-02", "sessions": 18, "messages": 95},
                {"date": "2025-01-03", "sessions": 25, "messages": 142},
                {"date": "2025-01-04", "sessions": 20, "messages": 115},
                {"date": "2025-01-05", "sessions": 28, "messages": 165},
                {"date": "2025-01-06", "sessions": 24, "messages": 138},
                {"date": "2025-01-07", "sessions": 19, "messages": 109}
            ],
            "device_stats": {
                "desktop": 45.2,
                "mobile": 42.3,
                "tablet": 12.5
            },
            "browser_stats": {
                "chrome": 58.7,
                "safari": 22.1,
                "firefox": 12.8,
                "edge": 6.4
            }
        }
        
        return JSONResponse(content={
            "success": True,
            "data": analytics
        })
        
    except Exception as e:
        logger.error(f"获取公共组件分析数据失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取分析数据失败"
        ) 