#!/usr/bin/env python
# -*- coding: utf-8 -*-

from typing import Optional, List, Dict, Any, Union
from datetime import datetime
from uuid import UUID
from pydantic import BaseModel, Field


class PluginBase(BaseModel):
    """插件基础模型"""
    name: str
    code: str
    description: Optional[str] = None
    version: str
    type: str = "marketplace"  # marketplace, system, custom
    category: str
    icon_url: Optional[str] = None
    price: float = 0
    author: Optional[str] = None
    homepage: Optional[str] = None
    features: Optional[List[str]] = None


class PluginCreate(PluginBase):
    """创建插件模型"""
    pass


class PluginUpdate(BaseModel):
    """更新插件模型"""
    name: Optional[str] = None
    description: Optional[str] = None
    version: Optional[str] = None
    type: Optional[str] = None
    category: Optional[str] = None
    icon_url: Optional[str] = None
    price: Optional[float] = None
    author: Optional[str] = None
    homepage: Optional[str] = None
    features: Optional[List[str]] = None
    is_active: Optional[bool] = None


class PluginInDB(PluginBase):
    """数据库中的插件模型"""
    id: UUID
    created_at: datetime
    updated_at: Optional[datetime] = None
    is_system: bool = False
    is_active: bool = True

    model_config = {"from_attributes": True}


class PluginDetail(PluginBase):
    """插件详情模型"""
    id: UUID
    created_at: datetime
    updated_at: Optional[datetime] = None
    is_system: bool = False
    is_active: bool = True
    installed: bool = False
    purchased: bool = False


class PluginList(BaseModel):
    """插件列表项模型"""
    id: UUID
    name: str
    code: str
    description: Optional[str] = None
    version: str
    type: str
    category: str
    icon_url: Optional[str] = None
    price: float
    author: Optional[str] = None
    is_system: bool = False
    is_active: bool = True
    installed: bool = False
    purchased: bool = False


class PluginResponse(BaseModel):
    """插件响应模型"""
    success: bool
    message: str
    data: Optional[PluginDetail] = None


class PluginsResponse(BaseModel):
    """插件列表响应模型"""
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None


class PluginPurchaseBase(BaseModel):
    """插件购买基础模型"""
    billing_cycle: str = "monthly"  # one_time, monthly, yearly
    duration: int = 1
    payment_method: str = "online"  # online, offline, free


class PluginPurchaseCreate(PluginPurchaseBase):
    """创建插件购买模型"""
    pass


class PluginPurchaseUpdate(BaseModel):
    """更新插件购买模型"""
    status: Optional[str] = None
    payment_status: Optional[str] = None


class PluginPurchaseInDB(PluginPurchaseBase):
    """数据库中的插件购买模型"""
    id: UUID
    tenant_id: UUID
    plugin_id: UUID
    service_provider_id: UUID
    order_number: str
    amount: float
    currency: str
    start_date: datetime
    end_date: Optional[datetime] = None
    status: str
    payment_status: str
    purchased_by: UUID
    created_at: datetime
    updated_at: Optional[datetime] = None

    model_config = {"from_attributes": True}


class PluginPurchaseDetail(PluginPurchaseBase):
    """插件购买详情模型"""
    id: UUID
    tenant_id: UUID
    plugin_id: UUID
    order_number: str
    amount: float
    currency: str
    start_date: datetime
    end_date: Optional[datetime] = None
    status: str
    payment_status: str
    created_at: datetime
    plugin_name: Optional[str] = None


class PluginPurchaseResponse(BaseModel):
    """插件购买响应模型"""
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None
