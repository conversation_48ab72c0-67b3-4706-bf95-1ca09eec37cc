import api from '../httpClient';
import { getProjectId } from '../httpClient';

/**
 * 项目-采购订单API
 */
const purchaseOrder = {
  /**
   * 获取采购订单列表
   * @param {Object} params - 查询参数
   * @returns {Promise} - 采购订单列表
   */
  getList: async (params = {}) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    // 自动过滤空字符串的 uuid 参数
    const filteredParams = { ...params };
    if (filteredParams.supplier_id === "") delete filteredParams.supplier_id;
    if (filteredParams.warehouse_id === "") delete filteredParams.warehouse_id;
    return api.get(`/project/${projectId}/purchase-orders`, { params: filteredParams });
  },

  /**
   * 预览上传数据
   * @param {Object} params - 预览参数
   * @param {Object} options - 请求选项（如signal, timeout等）
   * @returns {Promise} - 预览结果
   */
  previewUpload: async (params, options = {}) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    
    // 使用query参数而不是body参数
    return api.post(`/project/${projectId}/purchase-orders/preview-upload`, {}, {
      params: params,
      ...options
    });
  },

  /**
   * 使用AI智能处理预览上传数据
   * @param {Object} params - 预览参数，包含AI处理选项
   * @returns {Promise} - 预览结果
   */
  previewUploadWithAi: async (params) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    
    console.log('AI预览原始参数:', params);
    
    // 过滤掉undefined和null的参数，但保留空字符串和空数组
    const queryParams = {};
    Object.keys(params).forEach(key => {
      const value = params[key];
      if (value !== undefined && value !== null) {
        // 特殊处理数组参数
        if (Array.isArray(value)) {
          queryParams[key] = value.length > 0 ? JSON.stringify(value) : '[]';
        } else {
          queryParams[key] = value;
        }
      }
    });
    
    console.log('AI预览处理后参数:', queryParams);
    
    // 使用GET方式传递参数，因为后端使用Query参数
    return api.post(`/project/${projectId}/purchase-orders/preview-upload-ai`, {}, {
      params: queryParams
    });
  },

  /**
   * 确认上传
   * @param {Object} params - 确认参数
   * @returns {Promise} - 上传结果
   */
  confirmUpload: async (params) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    
    // 使用query参数而不是body参数
    return api.post(`/project/${projectId}/purchase-orders/confirm-upload`, {}, {
      params: params
    });
  },

  /**
   * 下载模板
   * @param {string} type - 模板类型: 'both', 'purchase', 'distribution'
   * @returns {Promise<Blob>} - 模板文件Blob
   */
  downloadTemplate: async (type = 'both') => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.get(`/project/${projectId}/purchase-orders/template`, {
      params: { type },
      responseType: 'blob'
    });
  },

  /**
   * 获取采购订单详情
   * @param {string} id - 采购订单ID
   * @returns {Promise} - 采购订单详情
   */
  getDetail: async (id) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.get(`/project/${projectId}/purchase-orders/${id}`);
  },

  /**
   * 创建采购订单
   * @param {Object} data - 采购订单数据
   * @returns {Promise} - 创建结果
   */
  create: async (data) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.post(`/project/${projectId}/purchase-orders`, data);
  },

  /**
   * 更新采购订单
   * @param {string} id - 采购订单ID
   * @param {Object} data - 采购订单数据
   * @returns {Promise} - 更新结果
   */
  update: async (id, data) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.put(`/project/${projectId}/purchase-orders/${id}`, data);
  },

  /**
   * 更新采购订单状态
   * @param {string} id - 采购订单ID
   * @param {Object} data - 状态数据
   * @returns {Promise} - 更新结果
   */
  updateStatus: async (id, data) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.put(`/project/${projectId}/purchase-orders/${id}/status`, data);
  },

  /**
   * 更新采购订单支付状态
   * @param {string} id - 采购订单ID
   * @param {Object} data - 支付状态数据
   * @returns {Promise} - 更新结果
   */
  updatePayment: async (id, data) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.put(`/project/${projectId}/purchase-orders/${id}/payment`, data);
  },

  /**
   * 删除采购订单
   * @param {string} id - 采购订单ID
   * @returns {Promise} - 删除结果
   */
  delete: async (id) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.delete(`/project/${projectId}/purchase-orders/${id}`);
  },

  /**
   * 添加采购订单项
   * @param {string} orderId - 采购订单ID
   * @param {Object} data - 订单项数据
   * @returns {Promise} - 添加结果
   */
  addItem: async (orderId, data) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.post(`/project/${projectId}/purchase-orders/${orderId}/items`, data);
  },

  /**
   * 更新采购订单项
   * @param {string} orderId - 采购订单ID
   * @param {string} itemId - 订单项ID
   * @param {Object} data - 订单项数据
   * @returns {Promise} - 更新结果
   */
  updateItem: async (orderId, itemId, data) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.put(`/project/${projectId}/purchase-orders/${orderId}/items/${itemId}`, data);
  },

  /**
   * 删除采购订单项
   * @param {string} orderId - 采购订单ID
   * @param {string} itemId - 订单项ID
   * @returns {Promise} - 删除结果
   */
  deleteItem: async (orderId, itemId) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.delete(`/project/${projectId}/purchase-orders/${orderId}/items/${itemId}`);
  },

  /**
   * 为采购订单创建分拨单
   * @param {string} orderId - 采购订单ID
   * @param {Object} data - 分拨单数据
   * @returns {Promise} - 创建结果
   */
  createDistribution: async (orderId, data) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.post(`/project/${projectId}/purchase-orders/${orderId}/create-arrival`, data);
  },

  /**
   * 获取采购报表数据
   * @param {Object} params - 查询参数
   * @returns {Promise} - 报表数据
   */
  getReport: async (params) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.get(`/project/${projectId}/purchase-orders/report`, { params });
  },

  /**
   * 导出采购报表
   * @param {Object} params - 查询参数
   * @returns {Promise} - 导出结果
   */
  exportReport: async (params) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.get(`/project/${projectId}/purchase-orders/report/export`, { params });
  },

  /**
   * 获取采购订单关联的分拨单
   * @param {string} orderId - 采购订单ID
   * @returns {Promise} - 分拨单列表
   */
  getArrivals: async (orderId) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.get(`/project/${projectId}/purchase-orders/${orderId}/arrivals`);
  },

  /**
   * 确认预览数据并创建采购订单和分拨单
   * @param {string} taskId - 任务ID
   * @param {Object} previewData - 预览数据
   * @returns {Promise} - 确认结果
   */
  confirmPreview: async (taskId, previewData) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.post(`/project/${projectId}/purchase-orders/confirm-preview/${taskId}`, previewData);
  }
};

export default purchaseOrder;
