#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
创建 sapi_token_applications 表的迁移脚本
"""

import logging
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession

logger = logging.getLogger(__name__)

async def migrate(db: AsyncSession) -> bool:
    """
    创建 sapi_token_applications 表
    
    Args:
        db: 数据库会话
        
    Returns:
        bool: 迁移是否成功
    """
    try:
        # 检查表是否存在
        result = await db.execute(text("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables
                WHERE table_name = 'sapi_token_applications'
            );
        """))
        
        table_exists = result.scalar()
        
        if not table_exists:
            # 创建表
            await db.execute(text("""
                CREATE TABLE IF NOT EXISTS sapi_token_applications (
                    id UUID PRIMARY KEY,
                    name VARCHAR(100) NOT NULL,
                    description TEXT,
                    purpose TEXT NOT NULL,
                    requested_permissions VARCHAR(50) DEFAULT 'read',
                    status VARCHAR(20) DEFAULT 'pending',
                    user_id UUID NOT NULL REFERENCES users(id),
                    created_at TIMESTAMP DEFAULT NOW(),
                    updated_at TIMESTAMP,
                    token VARCHAR(200),
                    rejection_reason TEXT
                )
            """))
            
            await db.commit()
            logger.info("成功创建 sapi_token_applications 表")
        else:
            logger.info("sapi_token_applications 表已存在，无需创建")
            
        return True
            
    except Exception as e:
        await db.rollback()
        logger.error(f"创建 sapi_token_applications 表失败: {str(e)}")
        raise
