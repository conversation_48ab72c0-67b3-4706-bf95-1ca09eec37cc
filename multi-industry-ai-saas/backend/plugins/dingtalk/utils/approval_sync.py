"""
钉钉审批同步服务 - 专注于业务数据到钉钉审批的同步
基于钉钉开放平台 2025 最新 API
"""
import json
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_
import uuid
from fastapi import HTTPException

from ..models import DingTalkApprovalInstance, DingTalkApprovalTemplate
from ..utils.dingtalk_api import DingTalkAPI

logger = logging.getLogger(__name__)

class ApprovalSyncService:
    """钉钉审批同步服务 - 专门处理业务数据同步到钉钉审批"""
    
    def __init__(self, dingtalk_api: DingTalkAPI, db: AsyncSession):
        self.dingtalk_api = dingtalk_api
        self.db = db
    
    # ==================== 核心同步功能 ====================
    
    async def create_and_sync_approval(
        self,
        tenant_id: uuid.UUID,
        project_id: uuid.UUID,
        business_type: str,
        business_data: Dict[str, Any],
        originator_user_id: uuid.UUID,
        sync_options: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        创建并同步业务数据到钉钉审批
        
        Args:
            tenant_id: 租户ID
            project_id: 项目ID
            business_type: 业务类型 (store_loss, warehouse_inbound, etc.)
            business_data: 业务数据
            originator_user_id: 发起人用户ID
            sync_options: 同步选项
        """
        try:
            logger.info(f"🔄 开始同步业务审批: {business_type}")
            
            # 1. 获取审批模板
            template = await self._get_business_template(project_id, business_type)
            if not template:
                return await self._create_local_only_approval(
                    tenant_id, project_id, business_type, business_data, originator_user_id
                )
            
            # 2. 获取用户钉钉ID
            dingtalk_user_id = await self._get_user_dingtalk_id(originator_user_id)
            if not dingtalk_user_id:
                logger.warning(f"⚠️ 用户 {originator_user_id} 未绑定钉钉账号，降级为本地审批")
                return await self._create_local_only_approval(
                    tenant_id, project_id, business_type, business_data, originator_user_id
                )
            
            # 3. 构建钉钉审批实例数据
            instance_data = await self._build_dingtalk_instance_data(
                template, business_data, dingtalk_user_id, business_type
            )
            
            # 4. 创建钉钉审批实例
            dingtalk_response = await self.dingtalk_api.create_approval_instance(instance_data)
            
            if not dingtalk_response or not dingtalk_response.get('success'):
                logger.warning(f"⚠️ 钉钉审批创建失败: {dingtalk_response.get('error') if dingtalk_response else '未知错误'}，降级为本地审批")
                return await self._create_local_only_approval(
                    tenant_id, project_id, business_type, business_data, originator_user_id
                )
            
            # 5. 创建本地审批记录
            local_instance = await self._create_local_approval_instance(
                tenant_id=tenant_id,
                project_id=project_id,
                template=template,
                business_type=business_type,
                business_data=business_data,
                originator_user_id=originator_user_id,
                dingtalk_instance_id=dingtalk_response.get('process_instance_id'),
                sync_status='synced'
            )
            
            logger.info(f"✅ 审批同步成功: {business_type} -> 钉钉实例ID {dingtalk_response.get('process_instance_id')}")
            
            return {
                "success": True,
                "data": {
                    "local_instance_id": str(local_instance.id),
                    "dingtalk_instance_id": dingtalk_response.get('process_instance_id'),
                    "process_code": template.process_code,
                    "sync_status": "synced",
                    "business_type": business_type
                },
                "message": "审批同步到钉钉成功"
            }
            
        except Exception as e:
            logger.error(f"❌ 审批同步失败: {str(e)}")
            # 降级为本地审批
            try:
                local_result = await self._create_local_only_approval(
                    tenant_id, project_id, business_type, business_data, originator_user_id
                )
                local_result["message"] = f"钉钉同步失败({str(e)})，已创建本地审批"
                return local_result
            except Exception as local_e:
                logger.error(f"❌ 本地审批创建也失败: {str(local_e)}")
                return {
                    "success": False,
                    "error": f"同步失败且本地审批创建失败: {str(e)} / {str(local_e)}",
                    "message": "审批创建完全失败"
                }
    
    async def _get_business_template(self, project_id: uuid.UUID, business_type: str) -> Optional[DingTalkApprovalTemplate]:
        """获取业务类型对应的审批模板"""
        try:
            stmt = select(DingTalkApprovalTemplate).where(
                and_(
                    DingTalkApprovalTemplate.project_id == project_id,
                    DingTalkApprovalTemplate.template_type == business_type,
                    DingTalkApprovalTemplate.status == 'ENABLE'
                )
            )
            result = await self.db.execute(stmt)
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"❌ 获取业务模板失败: {str(e)}")
            return None
    
    async def _get_user_dingtalk_id(self, user_id: uuid.UUID) -> Optional[str]:
        """获取用户的钉钉ID"""
        try:
            # 导入UnionIdService获取钉钉用户ID
            from ..services.user_service import UnionIdService
            union_service = UnionIdService(self.db)
            
            dingtalk_user_id = await union_service.get_union_id_by_user_id(str(user_id))
            return dingtalk_user_id
        except Exception as e:
            logger.error(f"❌ 获取用户钉钉ID失败: {str(e)}")
            return None
    
    async def _build_dingtalk_instance_data(
        self,
        template: DingTalkApprovalTemplate,
        business_data: Dict[str, Any],
        dingtalk_user_id: str,
        business_type: str
    ) -> Dict[str, Any]:
        """构建钉钉审批实例数据"""
        try:
            # 根据业务类型构建表单数据
            form_component_values = []
            
            if business_type == "store_loss":
                form_component_values = await self._build_loss_form_data(business_data)
            elif business_type == "warehouse_inbound":
                form_component_values = await self._build_inbound_form_data(business_data)
            else:
                # 通用表单数据构建
                form_component_values = await self._build_generic_form_data(business_data)
            
            # 构建审批实例数据
            instance_data = {
                "process_code": template.process_code,
                "originator_user_id": dingtalk_user_id,
                "title": self._generate_approval_title(business_type, business_data),
                "form_component_values": form_component_values
            }
            
            return instance_data
            
        except Exception as e:
            logger.error(f"❌ 构建钉钉实例数据失败: {str(e)}")
            raise
    
    async def _build_loss_form_data(self, business_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """构建门店报损表单数据"""
        form_data = []
        
        # 基础字段映射
        field_mappings = {
            "product_name": "产品名称",
            "quantity": "报损数量", 
            "unit_price": "单价",
            "reason": "报损原因",
            "notes": "备注说明"
        }
        
        for field, label in field_mappings.items():
            value = business_data.get(field)
            if value is not None:
                form_data.append({
                    "name": label,
                    "value": str(value)
                })
        
        # 计算总金额
        if business_data.get('quantity') and business_data.get('unit_price'):
            total_amount = float(business_data['quantity']) * float(business_data['unit_price'])
            form_data.append({
                "name": "总金额",
                "value": f"{total_amount:.2f}"
            })
        
        return form_data
    
    async def _build_inbound_form_data(self, business_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """构建仓库入库表单数据"""
        form_data = []
        
        field_mappings = {
            "supplier_name": "供应商名称",
            "notes": "入库说明",
            "warehouse_id": "仓库ID"
        }
        
        for field, label in field_mappings.items():
            value = business_data.get(field)
            if value is not None:
                form_data.append({
                    "name": label,
                    "value": str(value)
                })
        
        return form_data
    
    async def _build_generic_form_data(self, business_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """构建通用表单数据"""
        form_data = []
        
        for key, value in business_data.items():
            if value is not None:
                form_data.append({
                    "name": key.replace("_", " ").title(),
                    "value": str(value)
                })
        
        return form_data
    
    def _generate_approval_title(self, business_type: str, business_data: Dict[str, Any]) -> str:
        """生成审批标题"""
        titles = {
            "store_loss": "门店报损申请",
            "warehouse_inbound": "仓库入库申请",
            "purchase_request": "采购申请",
            "expense_reimbursement": "费用报销申请"
        }
        
        base_title = titles.get(business_type, "业务审批申请")
        
        # 根据业务数据添加额外信息
        if business_type == "store_loss" and business_data.get("reason"):
            return f"{base_title} - {business_data['reason']}"
        elif business_type == "warehouse_inbound" and business_data.get("supplier_name"):
            return f"{base_title} - {business_data['supplier_name']}"
        
        return base_title
    
    async def _create_local_approval_instance(
        self,
        tenant_id: uuid.UUID,
        project_id: uuid.UUID,
        template: DingTalkApprovalTemplate,
        business_type: str,
        business_data: Dict[str, Any],
        originator_user_id: uuid.UUID,
        dingtalk_instance_id: Optional[str] = None,
        sync_status: str = 'pending'
    ) -> DingTalkApprovalInstance:
        """创建本地审批实例记录"""
        try:
            instance = DingTalkApprovalInstance(
                id=uuid.uuid4(),
                tenant_id=tenant_id,
                project_id=project_id,
                template_id=template.id,
                instance_id=dingtalk_instance_id or f"LOCAL_{uuid.uuid4().hex[:16]}",
                process_code=template.process_code,
                title=self._generate_approval_title(business_type, business_data),
                description=f"业务类型: {business_type}",
                originator_user_id=str(originator_user_id),
                business_type=business_type,
                business_id=business_data.get("business_id", str(uuid.uuid4())),
                status="NEW" if dingtalk_instance_id else "PENDING",
                sync_status=sync_status,
                form_component_values=business_data,
                created_at=datetime.utcnow()
            )
            
            self.db.add(instance)
            await self.db.commit()
            await self.db.refresh(instance)
            
            return instance
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"❌ 创建本地审批实例失败: {str(e)}")
            raise
    
    async def _create_local_only_approval(
        self,
        tenant_id: uuid.UUID,
        project_id: uuid.UUID,
        business_type: str,
        business_data: Dict[str, Any],
        originator_user_id: uuid.UUID
    ) -> Dict[str, Any]:
        """创建仅本地的审批实例（降级方案）"""
        try:
            logger.info(f"🔽 创建本地审批实例: {business_type}")
            
            # 创建临时模板（如果没有）
            template = await self._get_business_template(project_id, business_type)
            if not template:
                template = await self._create_temporary_template(project_id, business_type)
            
            local_instance = await self._create_local_approval_instance(
                tenant_id=tenant_id,
                project_id=project_id,
                template=template,
                business_type=business_type,
                business_data=business_data,
                originator_user_id=originator_user_id,
                dingtalk_instance_id=None,
                sync_status='local_only'
            )
            
            logger.info(f"✅ 本地审批创建成功: {local_instance.id}")
            
            return {
                "success": True,
                "data": {
                    "local_instance_id": str(local_instance.id),
                    "dingtalk_instance_id": None,
                    "process_code": template.process_code,
                    "sync_status": "local_only",
                    "business_type": business_type
                },
                "message": "已创建本地审批实例"
            }
            
        except Exception as e:
            logger.error(f"❌ 创建本地审批失败: {str(e)}")
            raise
    
    async def _create_temporary_template(self, project_id: uuid.UUID, business_type: str) -> DingTalkApprovalTemplate:
        """创建临时审批模板"""
        try:
            template_configs = {
                "store_loss": {
                    "name": "门店报损审批（临时）",
                    "description": "临时创建的门店报损审批模板"
                },
                "warehouse_inbound": {
                    "name": "仓库入库审批（临时）",
                    "description": "临时创建的仓库入库审批模板"
                }
            }
            
            config = template_configs.get(business_type, {
                "name": f"{business_type}审批（临时）",
                "description": f"临时创建的{business_type}审批模板"
            })
            
            template = DingTalkApprovalTemplate(
                id=uuid.uuid4(),
                project_id=project_id,
                process_code=f"TEMP_{business_type.upper()}_{uuid.uuid4().hex[:8]}",
                name=config["name"],
                description=config["description"],
                template_type=business_type,
                form_schema={},
                form_components=[],
                status='ENABLE',
                created_at=datetime.utcnow()
            )
            
            self.db.add(template)
            await self.db.commit()
            await self.db.refresh(template)
            
            return template
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"❌ 创建临时模板失败: {str(e)}")
            raise 