#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
AI 模型服务
"""

import logging
import uuid
from typing import List, Optional, Dict, Any, Tuple
from sqlalchemy import select, func
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import joinedload

from models.ai import AIModel, AIProvider
from schemas.ai import AIModelCreate, AIModelUpdate
from services.ai.provider_service import AIProviderService

logger = logging.getLogger(__name__)

class AIModelService:
    """AI 模型服务"""

    @staticmethod
    async def get_models(
        db: AsyncSession,
        skip: int = 0,
        limit: int = 100,
        provider_id: Optional[uuid.UUID] = None,
        model_type: Optional[str] = None,
        status: Optional[str] = None,
    ) -> Tuple[List[AIModel], int]:
        """
        获取 AI 模型列表
        """
        # 构建查询条件
        conditions = []
        if provider_id:
            conditions.append(AIModel.provider_id == provider_id)
        if model_type:
            conditions.append(AIModel.model_type == model_type)
        if status:
            conditions.append(AIModel.status == status)
            
        # 获取总数 - 使用简单查询避免joinedload的影响
        count_query = select(func.count(AIModel.id))
        if conditions:
            count_query = count_query.where(*conditions)
        total = await db.scalar(count_query)
        
        # 获取分页数据 - 包含关联数据
        query = select(AIModel).options(joinedload(AIModel.provider))
        if conditions:
            query = query.where(*conditions)
        query = query.offset(skip).limit(limit).order_by(AIModel.created_at.desc())
        result = await db.execute(query)
        models = result.scalars().unique().all()
        
        return models, total

    @staticmethod
    async def get_model(
        db: AsyncSession,
        model_id: uuid.UUID,
    ) -> Optional[AIModel]:
        """
        获取 AI 模型详情
        """
        query = select(AIModel).options(joinedload(AIModel.provider)).where(AIModel.id == model_id)
        result = await db.execute(query)
        return result.scalars().first()

    @staticmethod
    async def get_model_by_name(
        db: AsyncSession,
        provider_id: uuid.UUID,
        name: str,
    ) -> Optional[AIModel]:
        """
        根据名称获取 AI 模型
        """
        query = select(AIModel).where(
            AIModel.provider_id == provider_id,
            AIModel.name == name
        )
        result = await db.execute(query)
        return result.scalars().first()

    @staticmethod
    async def create_model(
        db: AsyncSession,
        model_data: AIModelCreate,
    ) -> AIModel:
        """
        创建 AI 模型
        """
        # 检查提供商是否存在
        provider = await AIProviderService.get_provider(db, model_data.provider_id)
        if not provider:
            raise ValueError(f"提供商 ID '{model_data.provider_id}' 不存在")
            
        # 检查模型名称是否已存在
        existing_model = await AIModelService.get_model_by_name(db, model_data.provider_id, model_data.name)
        if existing_model:
            raise ValueError(f"模型名称 '{model_data.name}' 在该提供商下已存在")
            
        # 创建模型
        model = AIModel(**model_data.dict())
        db.add(model)
        await db.commit()
        await db.refresh(model)
        
        logger.info(f"创建 AI 模型成功: {model.id}")
        return model

    @staticmethod
    async def update_model(
        db: AsyncSession,
        model_id: uuid.UUID,
        model_data: AIModelUpdate,
    ) -> Optional[AIModel]:
        """
        更新 AI 模型
        """
        # 获取模型
        model = await AIModelService.get_model(db, model_id)
        if not model:
            return None
            
        # 更新模型
        update_data = model_data.dict(exclude_unset=True)
        for key, value in update_data.items():
            setattr(model, key, value)
            
        await db.commit()
        await db.refresh(model)
        
        logger.info(f"更新 AI 模型成功: {model.id}")
        return model

    @staticmethod
    async def delete_model(
        db: AsyncSession,
        model_id: uuid.UUID,
    ) -> bool:
        """
        删除 AI 模型
        """
        # 获取模型
        model = await AIModelService.get_model(db, model_id)
        if not model:
            return False
            
        # 检查是否为内置模型
        if model.is_builtin:
            raise ValueError("内置模型不能删除")
            
        # 删除模型
        await db.delete(model)
        await db.commit()
        
        logger.info(f"删除 AI 模型成功: {model.id}")
        return True

    @staticmethod
    async def init_builtin_models(db: AsyncSession) -> None:
        """
        初始化内置 AI 模型
        """
        # 获取内置提供商
        openai_provider = await AIProviderService.get_provider_by_name(db, "openai")
        anthropic_provider = await AIProviderService.get_provider_by_name(db, "anthropic")
        
        if not openai_provider or not anthropic_provider:
            logger.warning("内置提供商不存在，无法初始化内置模型")
            return
            
        # OpenAI 模型
        openai_models = [
            {
                "provider_id": openai_provider.id,
                "name": "gpt-4o",
                "display_name": "GPT-4o",
                "description": "OpenAI 最新的多模态大语言模型，支持文本、图像和音频输入",
                "model_type": "chat",
                "capabilities": {
                    "supports_function_calling": True,
                    "supports_vision": True,
                    "supports_audio_input": True,
                    "supports_audio_output": True,
                    "supports_streaming": True,
                    "supports_system_message": True
                },
                "context_window": 128000,
                "token_limit": 4096,
                "input_price_per_1k_tokens": 0.005,
                "output_price_per_1k_tokens": 0.015,
                "status": "active",
                "is_builtin": True
            },
            {
                "provider_id": openai_provider.id,
                "name": "gpt-4-turbo",
                "display_name": "GPT-4 Turbo",
                "description": "OpenAI GPT-4 Turbo 模型，支持更长的上下文窗口",
                "model_type": "chat",
                "capabilities": {
                    "supports_function_calling": True,
                    "supports_vision": False,
                    "supports_audio_input": False,
                    "supports_audio_output": False,
                    "supports_streaming": False,
                    "supports_system_message": False
                },
                "context_window": 128000,
                "token_limit": 4096,
                "input_price_per_1k_tokens": 0.01,
                "output_price_per_1k_tokens": 0.03,
                "status": "active",
                "is_builtin": True
            },
            {
                "provider_id": openai_provider.id,
                "name": "gpt-3.5-turbo",
                "display_name": "GPT-3.5 Turbo",
                "description": "OpenAI GPT-3.5 Turbo 模型，性价比高",
                "model_type": "chat",
                "capabilities": {
                    "supports_function_calling": True,
                    "supports_vision": False,
                    "supports_audio_input": False,
                    "supports_audio_output": False,
                    "supports_streaming": False,
                    "supports_system_message": False
                },
                "context_window": 16385,
                "token_limit": 4096,
                "input_price_per_1k_tokens": 0.0005,
                "output_price_per_1k_tokens": 0.0015,
                "status": "active",
                "is_builtin": True
            },
            {
                "provider_id": openai_provider.id,
                "name": "text-embedding-3-large",
                "display_name": "Text Embedding 3 Large",
                "description": "OpenAI 文本嵌入模型，用于生成文本的向量表示",
                "model_type": "embedding",
                "capabilities": {
                    "supports_function_calling": False,
                    "supports_vision": False,
                    "supports_audio_input": False,
                    "supports_audio_output": False,
                    "supports_streaming": False,
                    "supports_system_message": False
                },
                "context_window": 8191,
                "token_limit": 8191,
                "input_price_per_1k_tokens": 0.00013,
                "output_price_per_1k_tokens": 0,
                "status": "active",
                "is_builtin": True
            }
        ]
        
        # Anthropic 模型
        anthropic_models = [
            {
                "provider_id": anthropic_provider.id,
                "name": "claude-3-opus",
                "display_name": "Claude 3 Opus",
                "description": "Anthropic 最强大的模型，适用于高度复杂的任务",
                "model_type": "chat",
                "capabilities": {
                    "supports_function_calling": True,
                    "supports_vision": True,
                    "supports_audio_input": False,
                    "supports_audio_output": False,
                    "supports_streaming": False,
                    "supports_system_message": False
                },
                "context_window": 200000,
                "token_limit": 4096,
                "input_price_per_1k_tokens": 0.015,
                "output_price_per_1k_tokens": 0.075,
                "status": "active",
                "is_builtin": True
            },
            {
                "provider_id": anthropic_provider.id,
                "name": "claude-3-sonnet",
                "display_name": "Claude 3 Sonnet",
                "description": "Anthropic 平衡性能和成本的模型",
                "model_type": "chat",
                "capabilities": {
                    "supports_function_calling": True,
                    "supports_vision": True,
                    "supports_audio_input": False,
                    "supports_audio_output": False,
                    "supports_streaming": False,
                    "supports_system_message": False
                },
                "context_window": 200000,
                "token_limit": 4096,
                "input_price_per_1k_tokens": 0.003,
                "output_price_per_1k_tokens": 0.015,
                "status": "active",
                "is_builtin": True
            },
            {
                "provider_id": anthropic_provider.id,
                "name": "claude-3-haiku",
                "display_name": "Claude 3 Haiku",
                "description": "Anthropic 最快速和最经济的模型",
                "model_type": "chat",
                "capabilities": {
                    "supports_function_calling": True,
                    "supports_vision": True,
                    "supports_audio_input": False,
                    "supports_audio_output": False,
                    "supports_streaming": False,
                    "supports_system_message": False
                },
                "context_window": 200000,
                "token_limit": 4096,
                "input_price_per_1k_tokens": 0.00025,
                "output_price_per_1k_tokens": 0.00125,
                "status": "active",
                "is_builtin": True
            }
        ]
        
        # 合并所有模型
        all_models = openai_models + anthropic_models
        
        for model_data in all_models:
            # 检查是否已存在
            existing_model = await AIModelService.get_model_by_name(db, model_data["provider_id"], model_data["name"])
            if not existing_model:
                # 创建模型
                model = AIModel(**model_data)
                db.add(model)
                logger.info(f"创建内置 AI 模型: {model_data['name']}")
        
        await db.commit()
