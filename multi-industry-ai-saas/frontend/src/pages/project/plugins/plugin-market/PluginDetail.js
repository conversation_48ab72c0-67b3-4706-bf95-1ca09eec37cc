import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  Space,
  Tag,
  Typography,
  Divider,
  Row,
  Col,
  Tabs,
  message,
  Spin,
  Empty,
  Badge,
  Tooltip,
  Timeline,
  Descriptions,
  Modal
} from 'antd';
import {
  AppstoreOutlined,
  DownloadOutlined,
  ShoppingCartOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  InfoCircleOutlined,
  SettingOutlined,
  RocketOutlined,
  DollarOutlined,
  StarOutlined,
  ArrowLeftOutlined,
  HistoryOutlined
} from '@ant-design/icons';
import { useNavigate, useParams } from 'react-router-dom';
import { getPluginDetail, installPlugin, uninstallPlugin } from '../../../../services/pluginService';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;

/**
 * 插件详情组件
 */
const PluginDetail = () => {
  const [loading, setLoading] = useState(true);
  const [plugin, setPlugin] = useState(null);
  const [versions, setVersions] = useState([]);
  const [installation, setInstallation] = useState(null);
  const [purchase, setPurchase] = useState(null);
  const [purchaseModalVisible, setPurchaseModalVisible] = useState(false);
  
  const { id } = useParams();
  const navigate = useNavigate();
  
  // 获取插件详情
  const fetchPluginDetail = async () => {
    setLoading(true);
    try {
      const response = await getPluginDetail(id);
      
      if (response.success) {
        setPlugin(response.data.plugin);
        setVersions(response.data.versions || []);
        setInstallation(response.data.installation);
        setPurchase(response.data.purchase);
      } else {
        message.error('获取插件详情失败');
      }
    } catch (error) {
      console.error('获取插件详情失败:', error);
      message.error('获取插件详情失败');
    } finally {
      setLoading(false);
    }
  };
  
  // 处理安装插件
  const handleInstallPlugin = async () => {
    try {
      // 如果插件需要付费且未购买，则显示购买对话框
      if (plugin.price > 0 && !purchase?.is_purchased) {
        setPurchaseModalVisible(true);
        return;
      }
      
      const response = await installPlugin(plugin.id);
      
      if (response.success) {
        message.success('插件安装成功');
        fetchPluginDetail();
      } else {
        message.error('插件安装失败');
      }
    } catch (error) {
      console.error('安装插件失败:', error);
      message.error('安装插件失败');
    }
  };
  
  // 处理卸载插件
  const handleUninstallPlugin = async () => {
    Modal.confirm({
      title: '确认卸载',
      content: `确定要卸载插件 "${plugin.name}" 吗？`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        try {
          const response = await uninstallPlugin(plugin.id);
          
          if (response.success) {
            message.success('插件卸载成功');
            fetchPluginDetail();
          } else {
            message.error('插件卸载失败');
          }
        } catch (error) {
          console.error('卸载插件失败:', error);
          message.error('卸载插件失败');
        }
      }
    });
  };
  
  // 处理配置插件
  const handleConfigPlugin = () => {
    // 根据插件类型和名称构建路由
    const pluginName = plugin.name.toLowerCase().replace(/\s+/g, '_');
    
    if (plugin.type === 'marketplace') {
      navigate(`/project/plugin/${pluginName}`);
    } else if (plugin.type === 'ai') {
      navigate(`/project/ai/plugin/${pluginName}`);
    } else if (plugin.type === 'system') {
      navigate(`/system/plugin/${pluginName}`);
    }
  };
  
  // 初始化
  useEffect(() => {
    if (id) {
      fetchPluginDetail();
    }
  }, [id]);
  
  if (loading) {
    return (
      <Card>
        <div style={{ textAlign: 'center', padding: '50px 0' }}>
          <Spin size="large" />
          <p style={{ marginTop: 16 }}>加载中...</p>
        </div>
      </Card>
    );
  }
  
  if (!plugin) {
    return (
      <Card>
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description="未找到插件"
        />
        <div style={{ textAlign: 'center', marginTop: 16 }}>
          <Button type="primary" onClick={() => navigate('/project/plugins/plugin-market')}>
            返回插件市场
          </Button>
        </div>
      </Card>
    );
  }
  
  return (
    <Card
      title={
        <Space>
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={() => navigate('/project/plugins/plugin-market')}
          >
            返回
          </Button>
          <span>插件详情</span>
        </Space>
      }
      extra={
        <Space>
          {installation && installation.is_installed ? (
            <>
              <Button
                type="primary"
                icon={<SettingOutlined />}
                onClick={handleConfigPlugin}
              >
                配置插件
              </Button>
              <Button
                danger
                icon={<CloseCircleOutlined />}
                onClick={handleUninstallPlugin}
              >
                卸载插件
              </Button>
            </>
          ) : (
            <Button
              type="primary"
              icon={<DownloadOutlined />}
              onClick={handleInstallPlugin}
            >
              {plugin.price > 0 && !purchase?.is_purchased ? '购买并安装' : '安装插件'}
            </Button>
          )}
        </Space>
      }
    >
      <Row gutter={24}>
        <Col span={8}>
          <div style={{ 
            height: 200, 
            display: 'flex', 
            justifyContent: 'center', 
            alignItems: 'center',
            padding: 16,
            background: '#f5f5f5',
            borderRadius: 8,
            marginBottom: 16
          }}>
            {plugin.icon_url ? (
              <img 
                src={plugin.icon_url} 
                alt={plugin.name} 
                style={{ maxHeight: '100%', maxWidth: '100%' }}
              />
            ) : (
              <div style={{ fontSize: 64, color: '#1890ff' }}>
                <AppstoreOutlined />
              </div>
            )}
          </div>
          
          <Descriptions title="基本信息" column={1} bordered>
            <Descriptions.Item label="版本">v{plugin.version}</Descriptions.Item>
            <Descriptions.Item label="作者">{plugin.author || '-'}</Descriptions.Item>
            <Descriptions.Item label="分类">{plugin.category}</Descriptions.Item>
            <Descriptions.Item label="价格">
              {plugin.price > 0 ? (
                <Tag color="orange">¥{plugin.price}/{plugin.billing_cycle || '月'}</Tag>
              ) : (
                <Tag color="green">免费</Tag>
              )}
            </Descriptions.Item>
            <Descriptions.Item label="状态">
              {installation && installation.is_installed ? (
                <Badge status="success" text="已安装" />
              ) : (
                <Badge status="default" text="未安装" />
              )}
            </Descriptions.Item>
            {installation && installation.is_installed && (
              <Descriptions.Item label="安装时间">
                {new Date(installation.installed_at).toLocaleString()}
              </Descriptions.Item>
            )}
            {purchase && purchase.is_purchased && (
              <>
                <Descriptions.Item label="购买时间">
                  {new Date(purchase.purchased_at).toLocaleString()}
                </Descriptions.Item>
                <Descriptions.Item label="到期时间">
                  {new Date(purchase.expires_at).toLocaleString()}
                </Descriptions.Item>
              </>
            )}
          </Descriptions>
        </Col>
        
        <Col span={16}>
          <Title level={3}>{plugin.name}</Title>
          <Paragraph>{plugin.description}</Paragraph>
          
          <Tabs defaultActiveKey="features">
            <TabPane tab="功能特性" key="features">
              <div>
                {plugin.features && plugin.features.length > 0 ? (
                  <ul>
                    {plugin.features.map((feature, index) => (
                      <li key={index}>{feature}</li>
                    ))}
                  </ul>
                ) : (
                  <Empty description="暂无功能特性" />
                )}
              </div>
            </TabPane>
            
            <TabPane tab="版本历史" key="versions">
              {versions.length > 0 ? (
                <Timeline>
                  {versions.map((version, index) => (
                    <Timeline.Item key={index}>
                      <div>
                        <Text strong>v{version.version}</Text>
                        <Text type="secondary" style={{ marginLeft: 8 }}>
                          {new Date(version.released_at).toLocaleDateString()}
                        </Text>
                      </div>
                      <Paragraph>{version.description}</Paragraph>
                    </Timeline.Item>
                  ))}
                </Timeline>
              ) : (
                <Empty description="暂无版本历史" />
              )}
            </TabPane>
            
            <TabPane tab="使用说明" key="usage">
              <Paragraph>
                <Title level={4}>安装说明</Title>
                <ol>
                  <li>点击右上角的"安装插件"按钮</li>
                  <li>如果是付费插件，需要先完成购买</li>
                  <li>安装完成后，可以在"已安装"标签页中查看</li>
                  <li>点击"配置插件"按钮进行设置</li>
                </ol>
              </Paragraph>
              
              <Paragraph>
                <Title level={4}>使用说明</Title>
                <p>安装完成后，您可以在左侧菜单栏中找到该插件的入口。</p>
                {plugin.usage_instructions && (
                  <div dangerouslySetInnerHTML={{ __html: plugin.usage_instructions }} />
                )}
              </Paragraph>
            </TabPane>
          </Tabs>
        </Col>
      </Row>
      
      {/* 购买插件对话框 */}
      <Modal
        title="购买插件"
        open={purchaseModalVisible}
        onCancel={() => setPurchaseModalVisible(false)}
        onOk={handleInstallPlugin}
        okText="确认购买"
        cancelText="取消"
      >
        <div>
          <div style={{ marginBottom: 16 }}>
            <Space>
              <Title level={4}>{plugin.name}</Title>
              <Tag color="orange">¥{plugin.price}/{plugin.billing_cycle || '月'}</Tag>
            </Space>
          </div>
          <Paragraph>{plugin.description}</Paragraph>
          <Divider />
          <Row gutter={16}>
            <Col span={12}>
              <Text strong>版本：</Text>
              <Text>v{plugin.version}</Text>
            </Col>
            <Col span={12}>
              <Text strong>作者：</Text>
              <Text>{plugin.author}</Text>
            </Col>
          </Row>
          <Row gutter={16} style={{ marginTop: 8 }}>
            <Col span={12}>
              <Text strong>分类：</Text>
              <Text>{plugin.category}</Text>
            </Col>
            <Col span={12}>
              <Text strong>计费周期：</Text>
              <Text>
                {plugin.billing_cycle === 'monthly' ? '月付' :
                 plugin.billing_cycle === 'quarterly' ? '季付' :
                 plugin.billing_cycle === 'yearly' ? '年付' : '一次性'}
              </Text>
            </Col>
          </Row>
          <Divider />
          <Paragraph>
            <InfoCircleOutlined style={{ marginRight: 8 }} />
            购买后将自动安装插件，您可以随时在已安装插件列表中查看和配置。
          </Paragraph>
        </div>
      </Modal>
    </Card>
  );
};

export default PluginDetail;
