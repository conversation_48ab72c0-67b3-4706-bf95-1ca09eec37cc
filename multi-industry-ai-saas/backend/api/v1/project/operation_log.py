from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query, Path, status, Request
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import func, and_, or_, desc
import uuid
from datetime import datetime, timedelta

from db.database import get_db
from models.operation_log import OperationLog
from models.user import User
from schemas.operation_log import (
    OperationLogCreate, OperationLogInDB, OperationLogResponse, OperationLogListResponse,
    OperationLogQueryParams
)
from api.deps import get_current_user, get_current_project_id, get_current_tenant_id

router = APIRouter()

# 操作日志API
@router.get("/logs", response_model=OperationLogListResponse)
async def get_operation_logs(
    module: Optional[str] = None,
    action: Optional[str] = None,
    resource_type: Optional[str] = None,
    resource_id: Optional[uuid.UUID] = None,
    user_id: Optional[uuid.UUID] = None,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    search: Optional[str] = None,
    page: int = 1,
    size: int = 20,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    project_id: uuid.UUID = Depends(get_current_project_id)
):
    """
    获取操作日志列表
    """
    try:
        # 构建查询条件
        conditions = [
            OperationLog.project_id == project_id
        ]

        if module:
            conditions.append(OperationLog.module == module)

        if action:
            conditions.append(OperationLog.action == action)

        if resource_type:
            conditions.append(OperationLog.resource_type == resource_type)

        if resource_id:
            conditions.append(OperationLog.resource_id == resource_id)

        if user_id:
            conditions.append(OperationLog.user_id == user_id)

        if start_date:
            conditions.append(OperationLog.created_at >= start_date)

        if end_date:
            conditions.append(OperationLog.created_at <= end_date)

        if search:
            conditions.append(OperationLog.description.ilike(f"%{search}%"))

        # 计算分页参数
        skip = (page - 1) * size

        # 构建查询
        query = (
            select(
                OperationLog,
                User.full_name.label("user_name")
            )
            .outerjoin(User, OperationLog.user_id == User.id)
            .where(and_(*conditions))
            .order_by(desc(OperationLog.created_at))
            .offset(skip)
            .limit(size)
        )

        # 执行查询
        result = await db.execute(query)
        logs = result.all()

        # 获取总数
        count_query = select(func.count()).select_from(
            select(OperationLog)
            .where(and_(*conditions))
            .subquery()
        )
        result = await db.execute(count_query)
        total = result.scalar_one()

        # 构建响应数据
        log_items = []
        for log, user_name in logs:
            log_dict = {
                **log.__dict__,
                "user_name": user_name
            }

            # 确保 IP 地址是字符串
            if 'ip_address' in log_dict and log_dict['ip_address'] is not None:
                log_dict['ip_address'] = str(log_dict['ip_address'])

            log_items.append(log_dict)

        return {
            "success": True,
            "items": log_items,
            "total": total,
            "page": page,
            "size": size,
            "pages": (total + size - 1) // size if size > 0 else 1
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取操作日志列表失败: {str(e)}"
        )


@router.post("/logs", response_model=OperationLogResponse)
async def create_operation_log(
    log: OperationLogCreate,
    request: Request,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    project_id: uuid.UUID = Depends(get_current_project_id),
    tenant_id: Optional[uuid.UUID] = Depends(get_current_tenant_id)
):
    """
    创建操作日志
    """
    try:
        # 获取客户端IP和用户代理
        client_ip = request.client.host if request.client else None
        user_agent = request.headers.get("user-agent")

        # 创建操作日志
        new_log = OperationLog(
            id=uuid.uuid4(),
            tenant_id=tenant_id,
            project_id=project_id,
            user_id=current_user.id,
            module=log.module,
            action=log.action,
            resource_type=log.resource_type,
            resource_id=log.resource_id,
            description=log.description,
            details=log.details,
            ip_address=log.ip_address or client_ip,
            user_agent=log.user_agent or user_agent
        )

        db.add(new_log)
        await db.commit()
        await db.refresh(new_log)

        # 查询用户名
        user_query = select(User.full_name).where(User.id == current_user.id)
        result = await db.execute(user_query)
        user_name = result.scalar_one_or_none()

        # 构建响应数据
        log_dict = {
            **new_log.__dict__,
            "user_name": user_name
        }

        # 确保 IP 地址是字符串
        if 'ip_address' in log_dict and log_dict['ip_address'] is not None:
            log_dict['ip_address'] = str(log_dict['ip_address'])

        return {
            "success": True,
            "message": "操作日志创建成功",
            "data": log_dict
        }
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建操作日志失败: {str(e)}"
        )


@router.get("/logs/modules", response_model=Dict[str, List[str]])
async def get_log_modules(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    project_id: uuid.UUID = Depends(get_current_project_id)
):
    """
    获取操作日志模块列表
    """
    try:
        # 查询所有模块
        modules_query = (
            select(OperationLog.module)
            .where(OperationLog.project_id == project_id)
            .distinct()
        )
        result = await db.execute(modules_query)
        modules = [row[0] for row in result.all()]

        # 查询所有操作类型
        actions_query = (
            select(OperationLog.action)
            .where(OperationLog.project_id == project_id)
            .distinct()
        )
        result = await db.execute(actions_query)
        actions = [row[0] for row in result.all()]

        # 查询所有资源类型
        resource_types_query = (
            select(OperationLog.resource_type)
            .where(OperationLog.project_id == project_id)
            .distinct()
        )
        result = await db.execute(resource_types_query)
        resource_types = [row[0] for row in result.all()]

        return {
            "modules": modules,
            "actions": actions,
            "resource_types": resource_types
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取操作日志模块列表失败: {str(e)}"
        )
