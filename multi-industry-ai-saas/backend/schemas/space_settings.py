from typing import List, Optional, Dict, Any, Union
from pydantic import BaseModel, UUID4, Field
from datetime import datetime

# 模块空间设置模型
class ModuleSpaceSettingsBase(BaseModel):
    """模块空间设置基础模型"""
    module_code: str
    module_name: str
    max_file_size: Optional[int] = None
    allowed_extensions: Optional[List[str]] = None


class ModuleSpaceSettingsCreate(ModuleSpaceSettingsBase):
    """创建模块空间设置模型"""
    pass


class ModuleSpaceSettingsUpdate(BaseModel):
    """更新模块空间设置模型"""
    module_name: Optional[str] = None
    max_file_size: Optional[int] = None
    allowed_extensions: Optional[List[str]] = None


class ModuleSpaceSettingsInDB(ModuleSpaceSettingsBase):
    """数据库模块空间设置模型"""
    id: UUID4
    space_settings_id: UUID4
    created_at: datetime
    updated_at: datetime

    class Config:
        model_config = {"from_attributes": True}


# 空间设置模型
class SpaceSettingsBase(BaseModel):
    """空间设置基础模型"""
    max_file_size: int = 10485760  # 默认最大文件大小为10MB
    allowed_extensions: List[str] = ["jpg", "jpeg", "png", "gif", "pdf", "doc", "docx", "xls", "xlsx", "ppt", "pptx", "txt", "zip", "rar"]
    max_storage_per_user: int = 524288000  # 默认每用户最大存储空间为500MB
    max_storage_per_project: int = 5368709120  # 默认每项目最大存储空间为5GB


class SpaceSettingsCreate(SpaceSettingsBase):
    """创建空间设置模型"""
    module_settings: Optional[List[ModuleSpaceSettingsCreate]] = None


class SpaceSettingsUpdate(BaseModel):
    """更新空间设置模型"""
    max_file_size: Optional[int] = None
    allowed_extensions: Optional[List[str]] = None
    max_storage_per_user: Optional[int] = None
    max_storage_per_project: Optional[int] = None


class SpaceSettingsInDB(SpaceSettingsBase):
    """数据库空间设置模型"""
    id: UUID4
    tenant_id: Optional[UUID4] = None
    project_id: UUID4
    created_at: datetime
    updated_at: datetime
    created_by: Optional[UUID4] = None
    updated_by: Optional[UUID4] = None
    module_settings: List[ModuleSpaceSettingsInDB]

    class Config:
        model_config = {"from_attributes": True}


class SpaceSettingsResponse(BaseModel):
    """空间设置响应模型"""
    success: bool
    message: Optional[str] = None
    data: Optional[SpaceSettingsInDB] = None


# 模块空间设置响应模型
class ModuleSpaceSettingsResponse(BaseModel):
    """模块空间设置响应模型"""
    success: bool
    message: Optional[str] = None
    data: Optional[ModuleSpaceSettingsInDB] = None
