"""
JSON处理工具 - 使用json-repair处理递归和格式问题
"""
import json
import logging
from typing import Any, Dict, Optional
from json_repair import repair_json
from fastapi.encoders import jsonable_encoder
import uuid
from datetime import datetime
from decimal import Decimal

logger = logging.getLogger(__name__)

def safe_json_encode(obj: Any, max_depth: int = 10, current_depth: int = 0) -> Any:
    """
    安全的JSON编码，避免递归错误
    
    Args:
        obj: 要编码的对象
        max_depth: 最大递归深度
        current_depth: 当前递归深度
        
    Returns:
        JSON可序列化的对象
    """
    if current_depth > max_depth:
        return f"<Max depth reached: {type(obj).__name__}>"
    
    try:
        # 基本类型直接返回
        if obj is None or isinstance(obj, (bool, int, float, str)):
            return obj
            
        # UUID类型转换为字符串
        if isinstance(obj, uuid.UUID):
            return str(obj)
            
        # 日期时间类型转换为字符串
        if isinstance(obj, datetime):
            return obj.isoformat()
            
        # Decimal类型转换为float
        if isinstance(obj, Decimal):
            return float(obj)
            
        # 字典类型递归处理
        if isinstance(obj, dict):
            return {
                key: safe_json_encode(value, max_depth, current_depth + 1)
                for key, value in obj.items()
            }
            
        # 列表类型递归处理
        if isinstance(obj, (list, tuple, set)):
            return [
                safe_json_encode(item, max_depth, current_depth + 1)
                for item in obj
            ]
            
        # Pydantic模型处理
        if hasattr(obj, 'model_dump'):
            try:
                return safe_json_encode(obj.model_dump(), max_depth, current_depth + 1)
            except Exception as e:
                logger.warning(f"Failed to dump Pydantic model: {e}")
                return f"<Pydantic model: {type(obj).__name__}>"
                
        # SQLAlchemy模型处理
        if hasattr(obj, '__table__'):
            try:
                result = {}
                for column in obj.__table__.columns:
                    value = getattr(obj, column.name, None)
                    result[column.name] = safe_json_encode(value, max_depth, current_depth + 1)
                return result
            except Exception as e:
                logger.warning(f"Failed to serialize SQLAlchemy model: {e}")
                return f"<SQLAlchemy model: {type(obj).__name__}>"
                
        # 其他对象尝试转换为字符串
        return str(obj)
        
    except Exception as e:
        logger.error(f"JSON encoding error for {type(obj)}: {e}")
        return f"<Encoding error: {type(obj).__name__}>"

def safe_jsonable_encoder(obj: Any) -> Any:
    """
    安全的FastAPI jsonable_encoder替代方案
    
    Args:
        obj: 要编码的对象
        
    Returns:
        JSON可序列化的对象
    """
    try:
        # 首先尝试使用FastAPI的编码器
        return jsonable_encoder(obj)
    except RecursionError:
        logger.warning("RecursionError detected, using safe encoder")
        return safe_json_encode(obj)
    except Exception as e:
        logger.error(f"jsonable_encoder failed: {e}, using safe encoder")
        return safe_json_encode(obj)

def repair_json_string(json_str: str) -> Optional[Dict[str, Any]]:
    """
    修复损坏的JSON字符串
    
    Args:
        json_str: 可能损坏的JSON字符串
        
    Returns:
        修复后的字典对象，如果修复失败返回None
    """
    try:
        # 首先尝试正常解析
        return json.loads(json_str)
    except json.JSONDecodeError:
        try:
            # 使用json-repair修复
            repaired = repair_json(json_str)
            return json.loads(repaired)
        except Exception as e:
            logger.error(f"Failed to repair JSON: {e}")
            return None

def safe_json_response(data: Any) -> Dict[str, Any]:
    """
    创建安全的JSON响应
    
    Args:
        data: 响应数据
        
    Returns:
        安全的JSON响应
    """
    try:
        encoded_data = safe_jsonable_encoder(data)
        return {
            "success": True,
            "data": encoded_data
        }
    except Exception as e:
        logger.error(f"Failed to create JSON response: {e}")
        return {
            "success": False,
            "error": str(e),
            "data": None
        } 