from typing import List, Optional, Dict, Any, Union
from sqlalchemy import select, update, delete
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload
from ..models.models import MarketingGame, GamePrize, GameParticipant, GameRecord
from ..schemas.schemas import (
    MarketingGameCreate,
    MarketingGameUpdate,
    GamePrizeCreate,
    GamePrizeUpdate,
    GameParticipantCreate,
    GameRecordCreate
)

# 营销游戏 CRUD 操作
async def create_marketing_game(db: AsyncSession, game: MarketingGameCreate, project_id: int) -> MarketingGame:
    """创建营销游戏"""

    # 强制去除 tzinfo，确保入库为 naive datetime
    start_time_naive = game.start_time.replace(tzinfo=None) if game.start_time and game.start_time.tzinfo else game.start_time
    end_time_naive = game.end_time.replace(tzinfo=None) if game.end_time and game.end_time.tzinfo else game.end_time

    db_game = MarketingGame(
        project_id=project_id,
        name=game.name,
        description=game.description,
        game_type=game.game_type,
        marketing_type=game.marketing_type,
        start_time=start_time_naive, # 使用 naive 时间
        end_time=end_time_naive,     # 使用 naive 时间
        status=game.status,
        rules=game.rules,
        min_consumption=game.min_consumption,
        max_draws_per_user=game.max_draws_per_user,
        settings=game.settings
    )
    db.add(db_game)
    await db.commit()
    await db.refresh(db_game)

    # 重新查询以确保关联数据被加载
    result = await db.execute(
        select(MarketingGame)
        .options(selectinload(MarketingGame.prizes))
        .where(MarketingGame.id == db_game.id)
    )
    return result.scalars().first()

async def get_marketing_game(db: AsyncSession, game_id: int, project_id: int) -> Optional[MarketingGame]:
    """获取营销游戏详情"""
    result = await db.execute(
        select(MarketingGame)
        .options(selectinload(MarketingGame.prizes))
        .where(MarketingGame.id == game_id, MarketingGame.project_id == project_id)
    )
    return result.scalars().first()

async def get_marketing_games(
    db: AsyncSession,
    project_id: int,
    skip: int = 0,
    limit: int = 100,
    status: Optional[str] = None
) -> List[MarketingGame]:
    """获取营销游戏列表"""
    query = select(MarketingGame).where(MarketingGame.project_id == project_id)

    if status:
        query = query.where(MarketingGame.status == status)

    query = query.options(selectinload(MarketingGame.prizes))
    query = query.offset(skip).limit(limit)
    result = await db.execute(query)
    return result.scalars().all()

async def update_marketing_game(db: AsyncSession, game_id: int, game_update: MarketingGameUpdate) -> Optional[MarketingGame]:
    """更新营销游戏"""
    update_data = game_update.dict(exclude_unset=True)

    # 处理时区信息
    if 'start_time' in update_data and update_data['start_time'] and update_data['start_time'].tzinfo:
        update_data['start_time'] = update_data['start_time'].replace(tzinfo=None)
    if 'end_time' in update_data and update_data['end_time'] and update_data['end_time'].tzinfo:
        update_data['end_time'] = update_data['end_time'].replace(tzinfo=None)

    await db.execute(
        update(MarketingGame)
        .where(MarketingGame.id == game_id)
        .values(**update_data)
    )
    await db.commit()

    # 获取更新后的游戏，并预加载关联数据
    result = await db.execute(
        select(MarketingGame)
        .options(selectinload(MarketingGame.prizes))
        .where(MarketingGame.id == game_id)
    )
    return result.scalars().first()

async def delete_marketing_game(db: AsyncSession, game_id: int) -> None:
    """删除营销游戏"""
    await db.execute(delete(MarketingGame).where(MarketingGame.id == game_id))
    await db.commit()

# 游戏奖品 CRUD 操作
async def create_game_prize(db: AsyncSession, prize: GamePrizeCreate, game_id: int) -> GamePrize:
    """创建游戏奖品"""
    db_prize = GamePrize(
        game_id=game_id,
        name=prize.name,
        description=prize.description,
        prize_type=prize.prize_type,
        value=prize.value,
        quantity=prize.quantity,
        remaining=prize.quantity,  # 初始剩余数量等于总数量
        probability=prize.probability,
        image_url=prize.image_url,
        is_default=prize.is_default
    )
    db.add(db_prize)
    await db.commit()
    await db.refresh(db_prize)
    return db_prize

async def get_game_prize(db: AsyncSession, prize_id: int, game_id: int) -> Optional[GamePrize]:
    """获取游戏奖品详情"""
    result = await db.execute(
        select(GamePrize)
        .where(GamePrize.id == prize_id, GamePrize.game_id == game_id)
    )
    return result.scalars().first()

async def get_game_prizes(db: AsyncSession, game_id: int) -> List[GamePrize]:
    """获取游戏奖品列表"""
    result = await db.execute(
        select(GamePrize)
        .where(GamePrize.game_id == game_id)
    )
    return result.scalars().all()

async def update_game_prize(db: AsyncSession, prize_id: int, prize_update: GamePrizeUpdate) -> Optional[GamePrize]:
    """更新游戏奖品"""
    update_data = prize_update.dict(exclude_unset=True)

    # 如果更新了数量，同时更新剩余数量
    if "quantity" in update_data:
        # 获取当前奖品
        result = await db.execute(select(GamePrize).where(GamePrize.id == prize_id))
        current_prize = result.scalars().first()

        if current_prize:
            # 计算已使用数量
            used_quantity = current_prize.quantity - current_prize.remaining
            # 更新剩余数量 = 新总数量 - 已使用数量
            new_remaining = max(0, update_data["quantity"] - used_quantity)
            update_data["remaining"] = new_remaining

    await db.execute(
        update(GamePrize)
        .where(GamePrize.id == prize_id)
        .values(**update_data)
    )
    await db.commit()

    # 获取更新后的奖品
    result = await db.execute(select(GamePrize).where(GamePrize.id == prize_id))
    return result.scalars().first()

async def delete_game_prize(db: AsyncSession, prize_id: int) -> None:
    """删除游戏奖品"""
    await db.execute(delete(GamePrize).where(GamePrize.id == prize_id))
    await db.commit()

# 游戏参与者 CRUD 操作
async def create_game_participant(db: AsyncSession, participant: GameParticipantCreate, game_id: int) -> GameParticipant:
    """创建游戏参与者"""
    db_participant = GameParticipant(
        game_id=game_id,
        user_id=participant.user_id,
        member_id=participant.member_id,
        name=participant.name,
        phone=participant.phone,
        is_fixed_winner=participant.is_fixed_winner
    )
    db.add(db_participant)
    await db.commit()
    await db.refresh(db_participant)
    return db_participant

async def get_game_participant(db: AsyncSession, participant_id: int, game_id: int) -> Optional[GameParticipant]:
    """获取游戏参与者详情"""
    result = await db.execute(
        select(GameParticipant)
        .where(GameParticipant.id == participant_id, GameParticipant.game_id == game_id)
    )
    return result.scalars().first()

async def get_game_participants(
    db: AsyncSession,
    game_id: int,
    skip: int = 0,
    limit: int = 100
) -> List[GameParticipant]:
    """获取游戏参与者列表"""
    result = await db.execute(
        select(GameParticipant)
        .where(GameParticipant.game_id == game_id)
        .offset(skip).limit(limit)
    )
    return result.scalars().all()

# 游戏记录 CRUD 操作
async def create_game_record(db: AsyncSession, record: GameRecordCreate, game_id: int) -> GameRecord:
    """创建游戏记录"""
    db_record = GameRecord(
        game_id=game_id,
        participant_id=record.participant_id,
        prize_id=record.prize_id,
        receipt_id=record.receipt_id,
        consumption_amount=record.consumption_amount,
        is_win=record.is_win,
        remark=record.remark
    )
    db.add(db_record)

    # 如果中奖，更新奖品剩余数量和参与者中奖状态
    if record.is_win and record.prize_id:
        # 更新奖品剩余数量
        await db.execute(
            update(GamePrize)
            .where(GamePrize.id == record.prize_id)
            .values(remaining=GamePrize.remaining - 1)
        )

        # 更新参与者中奖状态
        await db.execute(
            update(GameParticipant)
            .where(GameParticipant.id == record.participant_id)
            .values(is_winner=True)
        )

    # 更新参与者抽奖次数
    await db.execute(
        update(GameParticipant)
        .where(GameParticipant.id == record.participant_id)
        .values(draws_count=GameParticipant.draws_count + 1)
    )

    await db.commit()
    await db.refresh(db_record)
    return db_record

async def get_game_record(db: AsyncSession, record_id: int, game_id: int) -> Optional[GameRecord]:
    """获取游戏记录详情"""
    result = await db.execute(
        select(GameRecord)
        .where(GameRecord.id == record_id, GameRecord.game_id == game_id)
    )
    return result.scalars().first()

async def get_game_records(
    db: AsyncSession,
    game_id: int,
    skip: int = 0,
    limit: int = 100
) -> List[GameRecord]:
    """获取游戏记录列表"""
    # 按创建时间倒序排序，确保最新的记录在前面
    # 使用selectinload加载关联的奖品和参与者信息
    from sqlalchemy.orm import selectinload
    result = await db.execute(
        select(GameRecord)
        .options(
            selectinload(GameRecord.prize),
            selectinload(GameRecord.participant)
        )
        .where(GameRecord.game_id == game_id)
        .order_by(GameRecord.created_at.desc())
        .offset(skip).limit(limit)
    )
    return result.scalars().all()
