#!/usr/bin/env python3
"""
Enhanced Purchase Order Task Executor
使用增强表格处理服务的采购订单任务执行器
"""

import asyncio
import json
import logging
from typing import Dict, Any, List, Optional
from uuid import UUID
from sqlalchemy.ext.asyncio import AsyncSession

from db.database import AsyncSess<PERSON>Local
from models.storage import StorageFile
from models.task import AsyncTask
from services.table_processing_service import TableProcessingService, Enhanced2025AIStrategy
from schemas.table_processing import TableProcessingRequest

logger = logging.getLogger(__name__)

class EnhancedPurchaseOrderTaskExecutor:
    """增强的采购订单任务执行器"""
    
    def __init__(self):
        self.enhanced_strategy = Enhanced2025AIStrategy()
    
    async def execute_task(self, task: AsyncTask, test_mode: bool = False) -> Dict[str, Any]:
        """执行采购订单任务"""
        
        try:
            logger.info(f"开始执行增强采购订单任务: {task.id}")
            
            # 获取文件信息
            async with AsyncSessionLocal() as session:
                file_upload = await session.get(StorageFile, UUID(task.input_data.get("file_id")))
                if not file_upload:
                    raise ValueError(f"文件未找到: {task.input_data.get('file_id')}")
            
            # 创建处理请求 - 使用2025年增强AI模式
            request = TableProcessingRequest(
                file_id=UUID(task.input_data.get("file_id")),
                user_id=task.user_id,
                project_id=task.project_id,
                processing_mode="enhanced_ai"  # 指定使用2025年增强AI
            )
            
            # 使用2025年增强AI策略处理表格
            async with AsyncSessionLocal() as db:
                # 加载文件为图像数据
                image_data = await TableProcessingService._load_file_as_image(
                    db, request.file_id, task.project_id
                )
                
                # 使用2025年增强AI策略处理
                result = await self.enhanced_strategy.process(
                    db, image_data, request, task.project_id, task.tenant_id, task.user_id
                )
            
            # 即使数据验证未完全通过，只要有提取到数据就继续处理
            if result.success or (result.extracted_data and len(result.extracted_data) > 0):
                # 获取项目门店列表用于匹配
                async with AsyncSessionLocal() as session:
                    stores = await self._get_project_stores(session, task.project_id)
                
                # 转换TableProcessingResult为前端格式
                frontend_data = self._convert_table_result_to_frontend_format(result, stores)
                
                return {
                    'success': True,
                    'data': frontend_data,
                    'metadata': {
                        'processing_method': result.processing_method,
                        'ai_processing_info': result.ai_processing_info,
                        'total_rows': result.total_rows,
                        'valid_rows': result.valid_rows
                    },
                    'message': '2025年增强AI采购订单处理完成'
                }
            else:
                return {
                    'success': False,
                    'error': result.errors[0]['message'] if result.errors else '2025年增强AI处理失败',
                    'data': self._get_empty_frontend_data(),
                    'metadata': {
                        'processing_method': result.processing_method,
                        'errors': result.errors
                    }
                }
                
        except Exception as e:
            logger.error(f"增强版采购订单任务执行失败: {str(e)}")
            import traceback
            traceback.print_exc()
            
            return {
                'success': False,
                'error': str(e),
                'data': self._get_empty_frontend_data(),
                'metadata': {'error_details': str(e)}
            }
    
    async def _get_project_stores(self, db, project_id: UUID) -> List[Dict[str, Any]]:
        """获取项目门店列表"""
        try:
            from models.store import Store
            from sqlalchemy import select
            
            stmt = select(Store).where(Store.project_id == project_id)
            result = await db.execute(stmt)
            stores = result.scalars().all()
            
            return [
                {
                    'id': str(store.id),
                    'name': store.name,
                    'code': store.code
                }
                for store in stores
            ]
        except Exception as e:
            logger.error(f"获取门店列表失败: {e}")
            return []
    
    def _match_store_by_name(self, store_name: str, stores: List[Dict[str, Any]]) -> Optional[str]:
        """根据门店名称匹配门店ID"""
        if not store_name or not stores:
            return None
        
        # 精确匹配
        for store in stores:
            if store['name'] == store_name:
                return store['id']
        
        # 模糊匹配（包含关系）
        for store in stores:
            if store_name in store['name'] or store['name'] in store_name:
                return store['id']
        
        # 编码匹配
        for store in stores:
            if store.get('code') == store_name:
                return store['id']
        
        return None
    
    def _convert_table_result_to_frontend_format(self, result, stores: List[Dict[str, Any]] = None) -> Dict[str, Any]:
        """转换TableProcessingResult为前端格式"""
        try:
            # 从TableProcessingResult中提取数据
            extracted_data = result.extracted_data
            
            if not extracted_data:
                return self._get_empty_frontend_data()
            
            # 将表格数据转换为商品和分拨格式
            products = []
            distribution = {}
            
            # 分析表格结构，识别商品信息列和门店列
            if extracted_data:
                sample_row = extracted_data[0]
                headers = list(sample_row.keys())
                
                # 识别商品信息列
                product_columns = []
                store_columns = []
                
                for header in headers:
                    if any(keyword in header for keyword in ['商品', '名称', '规格', '单位', '单价']):
                        product_columns.append(header)
                    elif not any(keyword in header for keyword in ['合计', '小计', '总计']):
                        # 其他列可能是门店列
                        store_columns.append(header)
                
                # 提取商品信息
                for row in extracted_data:
                    product_name = ''
                    specification = ''
                    unit = ''
                    unit_price = 0
                    
                    # 从行中提取商品信息
                    for col in product_columns:
                        if '商品名称' in col or col == '商品名称':
                            product_name = row.get(col, '')
                        elif '规格' in col:
                            specification = row.get(col, '')
                        elif '单位' in col:
                            unit = row.get(col, '')
                        elif '单价' in col:
                            unit_price = self._safe_float(row.get(col, 0))
                    
                    # 如果没有找到商品名称，尝试其他可能的列名
                    if not product_name:
                        for col, value in row.items():
                            if '商品' in col and value:
                                product_name = value
                                break
                    
                    if product_name:
                        # 添加到商品列表
                        product = {
                            'name': product_name,
                            'specification': specification,
                            'unit': unit,
                            'unit_price': unit_price,
                            'suggested_category': '',
                            'suggested_brand': ''
                        }
                        products.append(product)
                        
                        # 为每个门店创建分拨数据
                        for store_col in store_columns:
                            quantity = self._safe_float(row.get(store_col, 0))
                            if quantity > 0:
                                if store_col not in distribution:
                                    distribution[store_col] = []
                                
                                distribution[store_col].append({
                                    'product_name': product_name,
                                    'quantity': quantity,
                                    'unit_price': unit_price,
                                    'specification': specification,
                                    'unit': unit,
                                    'subtotal': quantity * unit_price  # 简化计算：数量 × 单价
                                })
            
            # 转换为前端格式
            return self._convert_to_frontend_format({'products': products, 'distribution': distribution}, stores)
            
        except Exception as e:
            logger.error(f"转换TableProcessingResult失败: {e}")
            return self._get_empty_frontend_data()

    def _convert_to_frontend_format(self, data: Dict[str, Any], stores: List[Dict[str, Any]] = None) -> Dict[str, Any]:
        """转换为正确的前端格式 - 区分采购单和分拨单业务逻辑"""
        
        # 兼容不同的数据结构
        products = data.get('products', [])
        distribution = data.get('distribution', {})
        
        # 如果没有分拨数据，可能数据在不同字段中
        if not distribution:
            for key in ['stores', 'store_data', 'distribution_data']:
                if key in data:
                    distribution = data[key]
                    break
        
        logger.info(f"转换前端格式 - 原始商品数: {len(products)}, 原始门店数: {len(distribution)}")
        
        # ===== 采购单处理：按商品维度汇总（正确的业务逻辑）=====
        purchase_items = []
        product_totals = {}  # 用于汇总同一商品在不同门店的数量
        
        # 首先从原始商品数据获取基础信息（避免重复计算）
        base_products = data.get('products', [])
        if base_products:
            # 如果有原始商品数据，直接使用
            for product in base_products:
                product_name = product.get('name', '')
                if not product_name:
                    continue
                    
                # 直接使用AI识别的原始数据
                specification = product.get('specification', '1')
                unit = product.get('unit', '')
                unit_price = self._safe_float(product.get('unit_price', 0))
                
                # 计算该商品在所有门店的总数量
                total_quantity = 0
                stores_with_product = []
                
                for store_name, store_products in distribution.items():
                    if not isinstance(store_products, list):
                        continue
                    
                    for item in store_products:
                        if (item.get('product_name', '') == product_name and 
                            item.get('specification', '1') == specification and
                            item.get('unit', '') == unit):
                            quantity = self._safe_float(item.get('quantity', 0))
                            total_quantity += quantity
                            if store_name not in stores_with_product:
                                stores_with_product.append(store_name)
                
                if total_quantity > 0:
                    # 计算总金额：规格 × 数量 × 单价
                    spec_number = self._safe_float(specification)
                    total_amount = spec_number * total_quantity * unit_price
                    
                    purchase_item = {
                        'product_name': product_name,
                        'product_specification': specification,  # 规格（数字）
                        'product_unit': unit,  # 单位（文字）
                        'product_code': '',
                        'quantity': total_quantity,  # 汇总数量
                        'unit_price': unit_price,
                        'total_amount': total_amount,
                        'notes': f"分布门店: {', '.join(sorted(stores_with_product))}",
                        'suggested_category': product.get('suggested_category', ''),  # 推荐分类
                        'suggested_brand': product.get('suggested_brand', '')  # 推荐品牌
                    }
                    purchase_items.append(purchase_item)
        else:
            # 如果没有原始商品数据，从分拨数据中汇总（备用逻辑）
            for store_name, store_products in distribution.items():
                if not isinstance(store_products, list):
                    continue
                    
                for item in store_products:
                    if not isinstance(item, dict):
                        continue
                        
                    product_name = item.get('product_name', '')
                    if not product_name:
                        continue
                    
                    # 直接使用AI识别的原始数据
                    specification = item.get('specification', '1')
                    unit = item.get('unit', '')
                    quantity = self._safe_float(item.get('quantity', 0))  # 数量
                    unit_price = self._safe_float(item.get('unit_price', 0))  # 单价
                    
                    # 创建商品唯一键（商品名+规格+单位）
                    product_key = f"{product_name}|{specification}|{unit}"
                    
                    if product_key not in product_totals:
                        product_totals[product_key] = {
                            'product_name': product_name,
                            'product_specification': specification,  # 规格应该是数字
                            'product_unit': unit,  # 单位应该是文字
                            'product_code': '',
                            'total_quantity': 0,
                            'unit_price': unit_price,
                            'stores': [],  # 记录哪些门店有这个商品
                            'suggested_category': item.get('suggested_category', ''),  # 推荐分类
                            'suggested_brand': item.get('suggested_brand', '')  # 推荐品牌
                        }
                    
                    # 累加数量
                    product_totals[product_key]['total_quantity'] += quantity
                    if store_name not in product_totals[product_key]['stores']:
                        product_totals[product_key]['stores'].append(store_name)
            
            # 转换为最终的采购单格式
            for product_info in product_totals.values():
                # 计算总金额：规格 × 数量 × 单价
                spec_number = self._safe_float(product_info['product_specification'])
                total_amount = spec_number * product_info['total_quantity'] * product_info['unit_price']
                
                purchase_item = {
                    'product_name': product_info['product_name'],
                    'product_specification': product_info['product_specification'],  # 规格（数字）
                    'product_unit': product_info['product_unit'],  # 单位（文字）
                    'product_code': '',
                    'quantity': product_info['total_quantity'],  # 汇总数量
                    'unit_price': product_info['unit_price'],
                    'total_amount': total_amount,
                    'notes': f"分布门店: {', '.join(product_info['stores'])}",  # 显示分布的门店
                    'suggested_category': product_info.get('suggested_category', ''),  # 推荐分类
                    'suggested_brand': product_info.get('suggested_brand', '')  # 推荐品牌
                }
                purchase_items.append(purchase_item)
        
        # ===== 分拨单处理：转换为前端期望的格式 =====
        distribution_destinations = []
        
        for store_name, store_products in distribution.items():
            if not isinstance(store_products, list):
                logger.warning(f"门店 {store_name} 的数据不是列表格式: {type(store_products)}")
                continue
                
            store_items = []
            store_total = 0
            
            for item in store_products:
                if not isinstance(item, dict):
                    logger.warning(f"门店 {store_name} 的商品数据不是字典格式: {type(item)}")
                    continue
                
                product_name = item.get('product_name', '')
                if not product_name:
                    continue
                    
                # 直接使用AI识别的原始数据
                specification = item.get('specification', '1')
                unit = item.get('unit', '')
                quantity = self._safe_float(item.get('quantity', 0))
                unit_price = self._safe_float(item.get('unit_price', 0))
                
                # 计算小计：规格 × 数量 × 单价
                spec_number = self._safe_float(specification)
                subtotal = spec_number * quantity * unit_price
                store_total += subtotal
                
                store_item = {
                    'product_name': product_name,
                    'specification': specification,  # 规格（数字）
                    'unit': unit,  # 单位（文字） 
                    'quantity': quantity,
                    'unit_price': unit_price,
                    'total_amount': subtotal
                }
                
                store_items.append(store_item)
            
            # 只添加有商品的门店，转换为前端期望的格式
            if store_items:
                # 尝试匹配门店ID
                matched_store_id = self._match_store_by_name(store_name, stores) if stores else None
                
                distribution_destinations.append({
                    'type': 'store',  # 前端期望的字段
                    'target_name': store_name,  # 前端期望的字段
                    'target_id': matched_store_id,  # 匹配到的门店ID
                    'items': store_items,
                    'total_amount': store_total
                })
        
        # 计算统计信息
        total_stores = len(distribution_destinations)
        total_products = len(purchase_items)
        total_amount = sum(item.get('total_amount', 0) for item in purchase_items)
        
        logger.info(f"转换后前端格式 - 采购商品数: {total_products}, 分拨门店数: {total_stores}, 总金额: {total_amount}")
        
        # 计算识别质量
        recognition_quality = self._calculate_recognition_quality(
            purchase_items, distribution_destinations, data
        )
        
        return {
            'preview': {
                'purchase_items': purchase_items,  # 按商品维度汇总的采购单
                'distribution_destinations': distribution_destinations  # 前端期望的分拨数据格式
            },
            'statistics': {
                'total_stores': total_stores,
                'total_products': total_products,
                'total_amount': total_amount
            },
            'recognition_quality': recognition_quality
        }
    
    def _get_empty_frontend_data(self) -> Dict[str, Any]:
        """获取空的前端数据结构"""
        return {
            'preview': {
                'purchase_items': [],
                'distribution_destinations': []
            },
            'statistics': {
                'total_stores': 0,
                'total_products': 0,
                'total_amount': 0
            },
            'recognition_quality': {
                'product_recognition_rate': 0,
                'store_recognition_rate': 0,
                'data_completeness': 'poor'
            }
        }
    
    def _calculate_recognition_quality(
        self, 
        products: List[Dict], 
        distribution: List[Dict], 
        raw_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """计算识别质量"""
        
        try:
            # 商品识别率（基于规格完整性）
            products_with_spec = sum(
                1 for p in products 
                if p.get('specification') and p['specification'] != '1' and p['specification'] != '无'
            )
            product_recognition_rate = min(100, (products_with_spec / len(products) * 100) if products else 0)
            
            # 门店识别率（基于真实门店名称）
            real_store_names = sum(
                1 for dest in distribution
                if dest.get('target_name') and not dest['target_name'].startswith('门店') and len(dest['target_name']) > 1
            )
            store_recognition_rate = min(100, (real_store_names / len(distribution) * 100) if distribution else 0)
            
            # 数据完整性 - 从新的metadata结构获取
            metadata = raw_data.get('metadata', {})
            completeness_score = metadata.get('completeness_score', 
                                            raw_data.get('summary', {}).get('data_completeness', 0))
            
            if completeness_score > 0.8:
                data_completeness = 'high'
            elif completeness_score > 0.5:
                data_completeness = 'medium'
            else:
                data_completeness = 'low'
            
            # 检查是否有提取警告
            extraction_warning = metadata.get('extraction_warning')
            
            result = {
                'product_recognition_rate': round(product_recognition_rate, 1),
                'store_recognition_rate': round(store_recognition_rate, 1),
                'data_completeness': data_completeness,
                'completeness_score': round(completeness_score, 2)
            }
            
            if extraction_warning:
                result['extraction_warning'] = extraction_warning
            
            return result
            
        except Exception as e:
            logger.error(f"计算识别质量失败: {e}")
            return {
                'product_recognition_rate': 0,
                'store_recognition_rate': 0,
                'data_completeness': 'poor',
                'completeness_score': 0.0
            }
    


    def _safe_float(self, value: Any) -> float:
        """安全转换为浮点数"""
        try:
            if isinstance(value, (int, float)):
                return float(value)
            elif isinstance(value, str):
                # 移除非数字字符
                import re
                cleaned = re.sub(r'[^\d.-]', '', value)
                return float(cleaned) if cleaned else 0.0
            else:
                return 0.0
        except (ValueError, TypeError):
            return 0.0



# 创建全局实例
enhanced_purchase_order_executor = EnhancedPurchaseOrderTaskExecutor() 