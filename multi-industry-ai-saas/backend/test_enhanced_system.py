#!/usr/bin/env python3
"""
测试增强表格处理系统
参考test_enhanced_ai.py的测试逻辑，测试新的增强版本
"""

import asyncio
import json
import logging
import uuid
import os
import base64
from pathlib import Path

# 设置环境
import sys
sys.path.append('/app')

from services.enhanced_table_processing_service import Enhanced2025TableProcessingService
from tasks.enhanced_purchase_order_task_executor import EnhancedPurchaseOrderTaskExecutor
from tasks.enhanced_2025_purchase_order_task_executor import Enhanced2025PurchaseOrderTaskExecutor
from models.storage import StorageFile
from models.task import AsyncTask
from schemas.table_processing import TableProcessingRequest
from db.database import AsyncSessionLocal
from sqlalchemy import select

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 使用真实的文件信息（与test_enhanced_ai.py保持一致）
TEST_FILE_ID = "d09595ab-5c4b-4231-bfd0-dc30e697d1b3"
TEST_PROJECT_ID = "*************-48ab-8092-e8eb7f663677"
TEST_USER_ID = "04a15201-2024-4d45-b434-a0bb4ff40c34"

async def test_enhanced_system():
    """测试增强系统 - 参考test_enhanced_ai.py的逻辑"""
    
    print("=== 测试增强表格处理系统 ===")
    print("使用真实文件和项目信息")
    print(f"文件ID: {TEST_FILE_ID}")
    print(f"项目ID: {TEST_PROJECT_ID}")
    print(f"用户ID: {TEST_USER_ID}")
    
    async with AsyncSessionLocal() as db:
        base64_image_string = None
        physical_path = None
        
        try:
            # 首先验证文件是否存在并加载图片为base64
            print("\n验证文件信息并加载图片...")
            
            stmt = select(StorageFile).where(
                StorageFile.id == uuid.UUID(TEST_FILE_ID),
                StorageFile.project_id == uuid.UUID(TEST_PROJECT_ID)
            )
            result = await db.execute(stmt)
            file_info = result.scalar_one_or_none()
            
            if file_info:
                print(f"✓ 文件名: {file_info.name}")
                base_upload_path = "/app/uploads"  # Docker环境路径
                physical_path = base_upload_path + file_info.storage_path
                if os.path.exists(physical_path):
                    print(f"✓ 物理文件存在: {physical_path}")
                    with open(physical_path, "rb") as image_file:
                        base64_image_string = base64.b64encode(image_file.read()).decode('utf-8')
                    print(f"✓ 图片已加载并编码为Base64 (长度: {len(base64_image_string)})")
                else:
                    print(f"❌ 物理文件不存在: {physical_path}")
                    return None
            else:
                print("❌ 数据库中文件记录不存在")
                return None
                
        except Exception as e:
            print(f"❌ 验证文件或加载图片失败: {e}")
            import traceback
            traceback.print_exc()
            return None
        
        if not base64_image_string:
            print("❌ 未能获取图片的Base64编码字符串，测试中止。")
            return None
        
        print("\n" + "=" * 50)
        
        try:
            # 1. 测试2025年增强表格处理服务
            print("1. 测试2025年增强表格处理服务...")

            # 创建处理请求
            request = TableProcessingRequest(
                file_id=uuid.UUID(TEST_FILE_ID),
                user_id=uuid.UUID(TEST_USER_ID),
                project_id=uuid.UUID(TEST_PROJECT_ID),
                processing_mode="enhanced_2025_ai"  # 使用2025年增强AI模式
            )

            enhanced_service = Enhanced2025TableProcessingService()
            service_result = await enhanced_service.process_table_with_enhanced_ai(request, file_info)
            
            print(f"增强服务处理结果: {service_result.get('success')}")
            if service_result.get('success'):
                data = service_result['data']
                metadata = service_result.get('metadata', {})
                
                print(f"识别的商品数量: {data.get('product_count', 0)}")
                print(f"识别的门店数量: {data.get('store_count', 0)}")
                print(f"处理阶段: {metadata.get('processing_stages', [])}")
                
                # 显示基础信息
                basic_info = metadata.get('basic_info', {})
                print(f"\n基础信息:")
                print(f"  表格类型: {basic_info.get('table_type')}")
                print(f"  行数: {basic_info.get('row_count')}")
                print(f"  列数: {basic_info.get('column_count')}")
                print(f"  图像质量: {basic_info.get('image_quality')}")
                
                # 显示格式信息
                table_format = metadata.get('table_format', {})
                print(f"\n格式信息:")
                print(f"  是否为采购分拨单: {table_format.get('is_purchase_distribution')}")
                print(f"  是否有门店列: {table_format.get('has_store_columns')}")
                print(f"  复杂度: {table_format.get('complexity_level')}")
                
                # 显示商品信息
                products = data.get('products', [])
                print(f"\n识别的商品 (前5个):")
                for i, product in enumerate(products[:5]):
                    print(f"  {i+1}. {product.get('name')} - 规格: {product.get('specification')} - 单价: {product.get('unit_price')}")
                
                # 显示门店分拨信息
                distribution = data.get('distribution', {})
                print(f"\n门店分拨信息:")
                for store_name, store_products in list(distribution.items())[:3]:
                    print(f"  {store_name}: {len(store_products)} 个商品")
                    for product in store_products[:2]:
                        print(f"    - {product.get('product_name')}: {product.get('quantity')} {product.get('specification', '')}")
            
            print("\n" + "=" * 50)
            
            # 2. 测试增强任务执行器 - 参考test_enhanced_ai.py的逻辑
            print("2. 测试2025年增强任务执行器...")

            # 创建模拟的AsyncTask对象
            mock_task = AsyncTask(
                id=str(uuid.uuid4()),
                project_id=uuid.UUID(TEST_PROJECT_ID),
                tenant_id=uuid.UUID(TEST_PROJECT_ID),  # 使用project_id作为tenant_id
                user_id=uuid.UUID(TEST_USER_ID),
                task_type="purchase_order_2025_ai",
                task_name="2025年增强版采购订单处理测试",  # 添加必需的task_name字段
                input_data={
                    "file_id": TEST_FILE_ID,
                    "upload_type": "allocation_slip",
                    "warehouse_id": None,
                    "processing_mode": "enhanced_2025_ai",
                    "task_params": {
                        "use_default_vision_model": True,
                        "vision_temperature": 0.01,  # 2025年版本使用极低温度
                        "vision_max_tokens": 16384   # 2025年版本使用更大的token限制
                    }
                }
            )

            # 实例化2025年增强版执行器
            executor = Enhanced2025PurchaseOrderTaskExecutor()
            print(f"Enhanced2025PurchaseOrderTaskExecutor 已实例化 (Task ID: {mock_task.id})")

            # 测试execute_task方法（使用测试模式）
            print("开始调用 Enhanced2025PurchaseOrderTaskExecutor.execute_task（测试模式）...")

            task_result = await executor.execute_task(mock_task, test_mode=True)
            
            # 显示详细结果 - 参考test_enhanced_ai.py的分析逻辑
            print("\n" + "=" * 50)
            print("=== Enhanced2025PurchaseOrderTaskExecutor 处理结果 ===")

            if task_result.get('success'):
                print(f"✅ 成功: 2025年增强版执行器返回了数据")
                frontend_data = task_result['data']
                metadata = task_result.get('metadata', {})
                
                # 显示前端数据统计
                statistics = frontend_data.get('statistics', {})
                quality = frontend_data.get('recognition_quality', {})
                
                print(f"\n📊 前端数据统计:")
                print(f"  总门店数: {statistics.get('total_stores')}")
                print(f"  总商品数: {statistics.get('total_products')}")
                print(f"  总金额: {statistics.get('total_amount')}")
                
                print(f"\n🎯 识别质量:")
                print(f"  商品识别率: {quality.get('product_recognition_rate')}%")
                print(f"  门店识别率: {quality.get('store_recognition_rate')}%")
                print(f"  数据完整性: {quality.get('data_completeness')}")
                print(f"  完整性评分: {quality.get('completeness_score', 0)}")
                
                # 显示处理元数据
                print(f"\n🔧 处理元数据:")
                extraction_method = metadata.get('extraction_method', 'unknown')
                processing_stages = metadata.get('processing_stages', [])
                print(f"  提取方法: {extraction_method}")
                print(f"  处理阶段: {' -> '.join(processing_stages)}")
                
                # 获取正确的数据结构
                preview_data = frontend_data.get('preview', {})
                purchase_items = preview_data.get('purchase_items', [])
                distribution_destinations = preview_data.get('distribution_destinations', [])

                # 显示商品信息
                print(f"\n📦 识别的商品 (前5个):")
                for i, product in enumerate(purchase_items[:5]):
                    spec = product.get('product_specification', '1')
                    price = product.get('unit_price', 0)
                    print(f"  {i+1}. {product.get('product_name')} - 规格: {spec} - 单价: {price}")

                # 显示门店分拨信息
                print(f"\n🏪 门店分拨信息 (前3个门店):")
                for destination in distribution_destinations[:3]:
                    store_name = destination.get('target_name', '')
                    store_items = destination.get('items', [])
                    print(f"  {store_name}: {len(store_items)} 个商品")
                    for product in store_items[:2]:
                        qty = product.get('quantity', 0)
                        spec = product.get('specification', '1')
                        print(f"    - {product.get('product_name')}: {qty} {spec}")

                # 详细数据分析
                print(f"\n📈 增强版结果数据分析:")
                all_product_names = []
                all_specifications = []
                stores_with_real_names = 0
                total_items = 0

                for destination in distribution_destinations:
                    store_name = destination.get('target_name', '')
                    store_items = destination.get('items', [])

                    # 检查是否为真实门店名称（不是"门店A"、"门店B"这种格式）
                    if store_name and not store_name.startswith("门店") and len(store_name) > 1:
                        stores_with_real_names += 1

                    total_items += len(store_items)
                    for product in store_items:
                        prod_name = product.get("product_name")
                        if prod_name:
                            all_product_names.append(str(prod_name))

                        specification = product.get("specification")
                        if specification and specification != "1":
                            all_specifications.append(str(specification))

                unique_product_names = sorted(list(set(filter(None, all_product_names))))
                unique_specifications = sorted(list(set(filter(None, all_specifications))))

                print(f"  独特商品名称: {len(unique_product_names)} 个")
                if unique_product_names:
                    print(f"    商品列表: {unique_product_names[:8]}{'...' if len(unique_product_names) > 8 else ''}")

                print(f"  规格参数提取: {len(unique_specifications)} 个独特规格")
                if unique_specifications:
                    print(f"    规格列表: {unique_specifications[:5]}{'...' if len(unique_specifications) > 5 else ''}")

                print(f"  真实门店名称: {stores_with_real_names}/{len(distribution_destinations)} 个门店")
                print(f"  总分拨条目: {total_items} 个")

                # 关键指标评估
                print(f"\n🎯 关键指标评估:")
                spec_rate = (len(unique_specifications) / len(unique_product_names) * 100) if unique_product_names else 0
                real_store_rate = (stores_with_real_names / len(distribution_destinations) * 100) if distribution_destinations else 0

                print(f"  规格提取成功率: {spec_rate:.1f}%")
                print(f"  真实门店识别率: {real_store_rate:.1f}%")

                # 问题诊断
                print(f"\n🔍 问题诊断:")
                issues = []
                if len(unique_specifications) == 0:
                    issues.append("❌ 规格参数完全未提取")
                elif spec_rate < 50:
                    issues.append("⚠️ 规格参数提取率偏低")

                if stores_with_real_names == 0:
                    issues.append("❌ 未识别到真实门店名称")
                elif real_store_rate < 50:
                    issues.append("⚠️ 真实门店识别率偏低")

                if len(distribution_destinations) < 3:
                    issues.append("⚠️ 识别的门店数量偏少")

                if not issues:
                    print("  ✅ 未发现明显问题")
                else:
                    for issue in issues:
                        print(f"  {issue}")

            else:
                print("❌ 失败: 增强版执行器未返回成功结果")
                error_msg = task_result.get('error', '未知错误')
                print(f"错误信息: {error_msg}")
                
                # 显示错误详情
                metadata = task_result.get('metadata', {})
                if 'error_details' in metadata:
                    print(f"错误详情: {metadata['error_details']}")
            
            print("\n=== 测试完成 ===")
            return task_result
            
        except Exception as e:
            logger.error(f"测试增强版执行器失败: {str(e)}")
            print(f"测试增强版执行器失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return None

async def test_json_repair():
    """测试JSON修复功能"""
    
    print("\n=== 测试JSON修复功能 ===")
    
    import json_repair
    
    # 测试各种损坏的JSON
    test_cases = [
        '{"name": "test", "value": 123',  # 缺少结束括号
        '```json\n{"name": "test"}\n```',  # markdown代码块
        '{"name": "test", "value": 123,}',  # 多余逗号
        'Some text before {"name": "test"} some text after',  # 包含其他文本
    ]
    
    for i, test_json in enumerate(test_cases):
        print(f"\n测试用例 {i+1}: {test_json}")
        try:
            # 清理JSON
            cleaned = test_json
            # 移除markdown代码块
            import re
            cleaned = re.sub(r'```json\s*', '', cleaned)
            cleaned = re.sub(r'```\s*$', '', cleaned)
            
            # 查找JSON对象
            json_match = re.search(r'\{.*\}', cleaned, re.DOTALL)
            if json_match:
                cleaned = json_match.group(0)
            
            # 使用json_repair修复
            repaired = json_repair.loads(cleaned)
            print(f"修复成功: {repaired}")
            
        except Exception as e:
            print(f"修复失败: {str(e)}")

async def main():
    """主测试函数"""
    print("🚀 开始增强版表格处理系统测试...")
    print("使用真实文件: " + TEST_FILE_ID)
    print("=" * 60)
    
    # 测试增强系统
    result = await test_enhanced_system()
    
    # 测试JSON修复功能
    await test_json_repair()
    
    print("\n" + "=" * 60)
    print("测试完成！")
    
    return result

if __name__ == "__main__":
    print("--- 增强版系统测试脚本启动 ---")
    try:
        asyncio.run(main())
        print("--- 增强版系统测试脚本成功完成 ---")
    except Exception as e:
        print(f"!!!!!! 增强版系统测试中出现未处理异常: {e} !!!!!!")
        import traceback
        print("!!!!!! 异常堆栈: !!!!!!")
        traceback.print_exc()
        print("!!!!!!!!!!!!!!!!!!!!!!!!!") 