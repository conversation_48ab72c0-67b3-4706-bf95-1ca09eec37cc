version: '3.3'

services:
  traefik:
    image: traefik:v3.3
    container_name: traefik
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
      - "8090:8080"  # Traefik 仪表板
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./traefik.yml:/etc/traefik/traefik.yml:ro
      - ./dynamic:/etc/traefik/dynamic:ro
    networks:
      - docker_retail-ai-saas-network  # 连接到主项目网络
    depends_on:
      - sapi-traefik-auth
    # v3.3新功能：更好的健康检查
    healthcheck:
      test: ["CMD", "traefik", "healthcheck", "--ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # SAPI Traefik鉴权微服务
  sapi-traefik-auth:
    build:
      context: ../backend/plugins/sapi
      dockerfile: Dockerfile.traefik-auth
    container_name: sapi-traefik-auth
    restart: unless-stopped
    environment:
      # 基础配置
      - LOG_LEVEL=INFO
      - PYTHONPATH=/app
      
      # 数据库配置
      - DATABASE_URL=${DATABASE_URL:-***********************************************************/retail_ai_saas}
      - REDIS_URL=${REDIS_URL:-redis://retail-ai-saas-redis:6379/0}
      
      # 安全配置
      - SECRET_KEY=${SECRET_KEY:-your-secret-key-here}
      - JWT_SECRET_KEY=${JWT_SECRET_KEY:-your-jwt-secret-key-here}
      - JWT_ALGORITHM=HS256
      - JWT_EXPIRE_MINUTES=30
      
      # SAPI配置
      - SAPI_SECRET_KEY=${SAPI_SECRET_KEY:-your-sapi-secret-key-here}
      - SYSTEM_TOKEN_EXPIRE_HOURS=24
      
      # 服务配置
      - SERVICE_NAME=sapi-traefik-auth
      - SERVICE_PORT=8001
      - CACHE_TTL=300
      
      # Traefik配置
      - TRAEFIK_MCP_DOMAIN_SUFFIX=saas.houshanai.com
      
    ports:
      - "8002:8001"
    
    networks:
      - docker_retail-ai-saas-network
    
    volumes:
      - ../backend/plugins/sapi/logs:/app/logs
    
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    labels:
      # Traefik标签
      - "traefik.enable=true"
      - "traefik.http.routers.sapi-auth.rule=Host(`sapi-auth.localhost`)"
      - "traefik.http.services.sapi-auth.loadbalancer.server.port=8001"
      
      # 服务发现标签
      - "service.name=sapi-traefik-auth"
      - "service.version=1.0.0"
      - "service.type=auth"

networks:
  docker_retail-ai-saas-network:
    external: true  # 使用主项目已创建的网络
