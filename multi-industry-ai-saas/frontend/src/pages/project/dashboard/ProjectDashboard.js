import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Typography,
  Table,
  Button,
  Spin,
  Empty,
  DatePicker,
  Tabs,
  List,
  Tag,
  Space,
  Alert,
  Tooltip,
  Divider
} from 'antd';
import {
  DollarOutlined,
  ShoppingCartOutlined,
  UserOutlined,
  ShopOutlined,
  RobotOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  SyncOutlined,
  LineChartOutlined,
  WarningOutlined,
  BellOutlined,
  LikeOutlined,
  DislikeOutlined,
  CloseCircleOutlined,
  CheckCircleOutlined,
  InfoCircleOutlined,
  ClockCircleOutlined,
  NotificationOutlined,
  InboxOutlined,
  TeamOutlined,
  FileTextOutlined,
  BarChartOutlined,
  SettingOutlined
} from '@ant-design/icons';
import { Line } from '@ant-design/plots';
import dayjs from 'dayjs';
import apiService from '../../../services/api';
import { message } from 'antd';
import { useNavigate, useLocation } from 'react-router-dom';

const { Title, Text, Paragraph } = Typography;
const { RangePicker } = DatePicker;
const { TabPane } = Tabs;

/**
 * 项目管理仪表盘组件
 * 展示项目整体运营情况，包括销售、订单、用户、门店等数据
 */
const ProjectDashboard = () => {
  const navigate = useNavigate();
  const location = useLocation();
  
  // 状态定义
  const [loading, setLoading] = useState(true);
  const [dashboardData, setDashboardData] = useState(null);
  const [dateRange, setDateRange] = useState([dayjs().subtract(6, 'days'), dayjs()]);
  const [aiLoading, setAiLoading] = useState(false);
  const [feedbackLoading, setFeedbackLoading] = useState(false);

  // 检查URL中是否有token参数（钉钉登录回调）
  useEffect(() => {
    const urlParams = new URLSearchParams(location.search);
    const token = urlParams.get('token');
    
    if (token) {
      // 如果有token，说明是钉钉登录回调，直接设置token
      localStorage.setItem('token', token);
      // 清除URL中的token参数
      const newUrl = window.location.pathname;
      window.history.replaceState({}, document.title, newUrl);
      message.success('登录成功');
    }
  }, [location]);

  // 初始化加载
  useEffect(() => {
    fetchDashboardData();
  }, [dateRange]);

  // 获取仪表盘数据
  const fetchDashboardData = async () => {
    setLoading(true);
    try {
      const params = {
        start_date: dateRange[0].format('YYYY-MM-DD'),
        end_date: dateRange[1].format('YYYY-MM-DD')
      };

      const response = await apiService.project.dashboard.getProjectDashboard(params);
      setDashboardData(response);
    } catch (error) {
      console.error('获取项目仪表盘数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 刷新AI推荐
  const refreshAIRecommendations = async () => {
    setAiLoading(true);
    try {
      const response = await apiService.project.dashboard.getAIRecommendations('project');
      if (response && response.recommendations) {
        setDashboardData(prev => ({
          ...prev,
          ai_recommendations: response.recommendations
        }));
      }
    } catch (error) {
      console.error('获取AI推荐失败:', error);
    } finally {
      setAiLoading(false);
    }
  };

  // 处理AI推荐反馈
  const handleRecommendationFeedback = async (recommendationId, isHelpful) => {
    setFeedbackLoading(true);
    try {
      await apiService.project.dashboard.submitAIFeedback({
        recommendation_id: recommendationId,
        is_helpful: isHelpful,
        feedback_type: 'project'
      });

      // 更新本地状态，标记反馈已提交
      setDashboardData(prev => ({
        ...prev,
        ai_recommendations: prev.ai_recommendations.map(item =>
          item.id === recommendationId
            ? { ...item, feedback_submitted: true, is_helpful: isHelpful }
            : item
        )
      }));

      message.success(isHelpful ? '感谢您的反馈！' : '感谢您的反馈，我们会继续改进');
    } catch (error) {
      console.error('提交AI推荐反馈失败:', error);
      message.error('提交反馈失败，请重试');
    } finally {
      setFeedbackLoading(false);
    }
  };

  // 处理日期范围变化
  const handleDateRangeChange = (dates) => {
    if (dates && dates.length === 2) {
      setDateRange(dates);
    }
  };

  // 刷新数据
  const handleRefresh = () => {
    fetchDashboardData();
  };

  // 渲染销售趋势图表
  const renderSalesTrendChart = () => {
    if (!dashboardData || !dashboardData.sales_trend || dashboardData.sales_trend.length === 0) {
      return <Empty description="暂无销售趋势数据" />;
    }

    const config = {
      data: dashboardData.sales_trend,
      xField: 'date',
      yField: 'value',
      seriesField: 'type',
      smooth: true,
      animation: {
        appear: {
          animation: 'path-in',
          duration: 1000
        }
      },
      xAxis: {
        title: {
          text: '日期'
        }
      },
      yAxis: {
        title: {
          text: '销售额 (元)'
        }
      },
      tooltip: {
        formatter: (datum) => {
          return { name: '销售额', value: `¥${datum.value.toFixed(2)}` };
        }
      }
    };

    return <Line {...config} height={300} />;
  };

  // 渲染库存预警表格
  const renderInventoryAlertsTable = () => {
    if (!dashboardData || !dashboardData.inventory_alerts || dashboardData.inventory_alerts.length === 0) {
      return <Empty description="暂无库存预警数据" />;
    }

    const columns = [
      {
        title: '商品名称',
        dataIndex: 'product_name',
        key: 'product_name',
      },
      {
        title: '门店',
        dataIndex: 'store_name',
        key: 'store_name',
      },
      {
        title: '当前库存',
        dataIndex: 'current_quantity',
        key: 'current_quantity',
      },
      {
        title: '预警阈值',
        dataIndex: 'min_quantity',
        key: 'min_quantity',
      },
      {
        title: '状态',
        key: 'status',
        render: (_, record) => {
          const percentage = (record.current_quantity / record.min_quantity) * 100;
          let color = 'green';
          let text = '正常';

          if (percentage <= 30) {
            color = 'red';
            text = '紧急';
          } else if (percentage <= 70) {
            color = 'orange';
            text = '警告';
          }

          return <Tag color={color}>{text}</Tag>;
        },
        sorter: (a, b) => {
          const percentageA = (a.current_quantity / a.min_quantity) * 100;
          const percentageB = (b.current_quantity / b.min_quantity) * 100;
          return percentageA - percentageB;
        },
        defaultSortOrder: 'ascend',
      },
      {
        title: '建议操作',
        key: 'recommendation',
        render: (_, record) => {
          const percentage = (record.current_quantity / record.min_quantity) * 100;
          let recommendation = '';

          if (percentage <= 30) {
            recommendation = `建议紧急采购${record.min_quantity - record.current_quantity + 10}${record.unit || '件'}`;
          } else if (percentage <= 70) {
            recommendation = `建议补充库存${record.min_quantity - record.current_quantity + 5}${record.unit || '件'}`;
          } else {
            recommendation = '暂无建议';
          }

          return <Text type={percentage <= 70 ? "warning" : "secondary"}>{recommendation}</Text>;
        },
      }
    ];

    return (
      <Table
        columns={columns}
        dataSource={dashboardData.inventory_alerts}
        rowKey="id"
        pagination={{ pageSize: 5 }}
      />
    );
  };

  // 渲染滞销商品表格
  const renderSlowMovingTable = () => {
    if (!dashboardData || !dashboardData.slow_moving || dashboardData.slow_moving.length === 0) {
      return <Empty description="暂无滞销商品数据" />;
    }

    const columns = [
      {
        title: '商品名称',
        dataIndex: 'product_name',
        key: 'product_name',
      },
      {
        title: '门店',
        dataIndex: 'store_name',
        key: 'store_name',
      },
      {
        title: '当前库存',
        dataIndex: 'current_quantity',
        key: 'current_quantity',
      },
      {
        title: '最后销售',
        dataIndex: 'last_sold_days',
        key: 'last_sold_days',
        render: (days) => `${days}天前`,
      },
      {
        title: '紧急程度',
        key: 'urgency',
        render: (_, record) => {
          const days = record.last_sold_days;
          let color = 'green';
          let text = '正常';

          if (days >= 60) {
            color = 'red';
            text = '紧急';
          } else if (days >= 30) {
            color = 'orange';
            text = '警告';
          }

          return <Tag color={color}>{text}</Tag>;
        },
        sorter: (a, b) => b.last_sold_days - a.last_sold_days,
        defaultSortOrder: 'descend',
      },
      {
        title: '建议操作',
        key: 'recommendation',
        render: (_, record) => {
          const days = record.last_sold_days;
          let recommendation = '';

          if (days >= 60) {
            recommendation = '建议清仓处理或促销';
          } else if (days >= 30) {
            recommendation = '建议安排促销活动';
          } else {
            recommendation = '正常销售';
          }

          return <Text type={days >= 30 ? "warning" : "secondary"}>{recommendation}</Text>;
        },
      }
    ];

    return (
      <Table
        columns={columns}
        dataSource={dashboardData.slow_moving}
        rowKey="id"
        pagination={{ pageSize: 5 }}
      />
    );
  };

  // 渲染通知列表
  const renderNotifications = () => {
    if (!dashboardData || !dashboardData.notifications || dashboardData.notifications.length === 0) {
      return <Empty description="暂无通知" />;
    }

    return (
      <List
        itemLayout="horizontal"
        dataSource={dashboardData.notifications}
        renderItem={item => (
          <List.Item>
            <List.Item.Meta
              avatar={
                item.type === 'warning' ? <WarningOutlined style={{ color: '#faad14', fontSize: 20 }} /> :
                item.type === 'error' ? <CloseCircleOutlined style={{ color: '#f5222d', fontSize: 20 }} /> :
                item.type === 'success' ? <CheckCircleOutlined style={{ color: '#52c41a', fontSize: 20 }} /> :
                <InfoCircleOutlined style={{ color: '#1890ff', fontSize: 20 }} />
              }
              title={item.title}
              description={
                <div>
                  <div>{item.content}</div>
                  <div style={{ fontSize: 12, color: '#999', marginTop: 4 }}>
                    {dayjs(item.created_at).format('YYYY-MM-DD HH:mm')}
                  </div>
                </div>
              }
            />
          </List.Item>
        )}
        pagination={{ pageSize: 5 }}
      />
    );
  };

  // 渲染AI推荐
  const renderAIRecommendations = () => {
    if (!dashboardData || !dashboardData.ai_recommendations || dashboardData.ai_recommendations.length === 0) {
      return <Empty description="暂无AI推荐" />;
    }

    return (
      <List
        dataSource={dashboardData.ai_recommendations}
        renderItem={(item) => (
          <List.Item>
            <div style={{ width: '100%' }}>
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'flex-start',
                marginBottom: 12
              }}>
                <div>
                  <Text strong style={{ fontSize: 16, marginRight: 8 }}>{item.title}</Text>
                  <Tag color={
                    item.type === '业务优化' ? 'blue' :
                    item.type === '销售增长' ? 'green' :
                    item.type === '成本控制' ? 'orange' :
                    item.type === '风险预警' ? 'red' : 'default'
                  }>
                    {item.type}
                  </Tag>
                  {item.priority && (
                    <Tag color={
                      item.priority === '高' ? 'red' :
                      item.priority === '中' ? 'orange' : 'green'
                    }>
                      {item.priority}优先级
                    </Tag>
                  )}
                </div>
                <div>
                  {item.feedback_submitted ? (
                    <span>
                      {item.is_helpful ?
                        <Tag icon={<LikeOutlined />} color="success">已标记为有帮助</Tag> :
                        <Tag icon={<DislikeOutlined />} color="default">已提交反馈</Tag>
                      }
                    </span>
                  ) : (
                    <Space>
                      <Button
                        type="primary"
                        size="small"
                        icon={<LikeOutlined />}
                        onClick={() => handleRecommendationFeedback && handleRecommendationFeedback(item.id, true)}
                      >
                        有帮助
                      </Button>
                      <Button
                        size="small"
                        icon={<DislikeOutlined />}
                        onClick={() => handleRecommendationFeedback && handleRecommendationFeedback(item.id, false)}
                      >
                        没有帮助
                      </Button>
                    </Space>
                  )}
                </div>
              </div>

              <Paragraph style={{ margin: '12px 0' }}>{item.content}</Paragraph>

              {item.action_items && item.action_items.length > 0 && (
                <div style={{
                  background: '#f5f5f5',
                  padding: '12px 16px',
                  borderRadius: '4px',
                  marginBottom: 12
                }}>
                  <Text strong>建议行动:</Text>
                  <ul style={{ marginTop: 8, marginBottom: 0, paddingLeft: 20 }}>
                    {item.action_items.map((action, index) => (
                      <li key={index}>{action}</li>
                    ))}
                  </ul>
                </div>
              )}

              {item.detailed_analysis && (
                <div style={{ marginTop: 12 }}>
                  <Divider style={{ margin: '12px 0' }} />
                  <Text strong>详细分析:</Text>
                  <Paragraph style={{ margin: '8px 0 0' }}>{item.detailed_analysis}</Paragraph>
                </div>
              )}
            </div>
          </List.Item>
        )}
        style={{ background: '#fff' }}
      />
    );
  };

  // 渲染门店性能表格
  const renderStorePerformanceTable = () => {
    if (!dashboardData || !dashboardData.store_performance || dashboardData.store_performance.length === 0) {
      return <Empty description="暂无门店性能数据" />;
    }

    const columns = [
      {
        title: '门店名称',
        dataIndex: 'name',
        key: 'name',
      },
      {
        title: '销售额',
        dataIndex: 'sales',
        key: 'sales',
        render: (text) => `¥${text.toFixed(2)}`,
        sorter: (a, b) => a.sales - b.sales,
      },
      {
        title: '订单数',
        dataIndex: 'orders',
        key: 'orders',
        sorter: (a, b) => a.orders - b.orders,
      }
    ];

    return (
      <Table
        columns={columns}
        dataSource={dashboardData.store_performance}
        rowKey="id"
        pagination={{ pageSize: 5 }}
      />
    );
  };

  // 渲染最近活动列表
  const renderRecentActivities = () => {
    if (!dashboardData || !dashboardData.recent_activities || dashboardData.recent_activities.length === 0) {
      return <Empty description="暂无最近活动" />;
    }

    return (
      <List
        dataSource={dashboardData.recent_activities}
        renderItem={(item) => (
          <List.Item>
            <List.Item.Meta
              title={item.user_name}
              description={
                <Space>
                  <Text type="secondary">{dayjs(item.timestamp).format('YYYY-MM-DD HH:mm:ss')}</Text>
                  <Text>{item.description}</Text>
                </Space>
              }
            />
          </List.Item>
        )}
      />
    );
  };

  return (
    <div className="project-dashboard">
      <Card>
        <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Title level={4} style={{ margin: 0 }}>项目管理仪表盘</Title>
          <Space>
            <RangePicker
              value={dateRange}
              onChange={handleDateRangeChange}
              allowClear={false}
            />
            <Button
              icon={<SyncOutlined />}
              onClick={handleRefresh}
              loading={loading}
            >
              刷新数据
            </Button>
          </Space>
        </div>

        <Spin spinning={loading}>
          {dashboardData ? (
            <>
              {/* 统计卡片 */}
              <Row gutter={[16, 16]}>
                <Col xs={24} sm={12} md={6}>
                  <Card>
                    <Statistic
                      title="今日销售额"
                      value={dashboardData.today_sales.value}
                      precision={2}
                      prefix={<DollarOutlined />}
                      suffix="元"
                      valueStyle={{ color: '#3f8600' }}
                    />
                    <div className="statistic-footer">
                      {dashboardData.today_sales.is_increase ? (
                        <Text type="success">
                          <ArrowUpOutlined /> {Math.abs(dashboardData.today_sales.change).toFixed(1)}%
                        </Text>
                      ) : (
                        <Text type="danger">
                          <ArrowDownOutlined /> {Math.abs(dashboardData.today_sales.change).toFixed(1)}%
                        </Text>
                      )}
                      <Text type="secondary" style={{ marginLeft: 8 }}>与昨日相比</Text>
                    </div>
                  </Card>
                </Col>
                <Col xs={24} sm={12} md={6}>
                  <Card>
                    <Statistic
                      title="今日订单数"
                      value={dashboardData.today_orders.value}
                      prefix={<ShoppingCartOutlined />}
                      suffix="单"
                      valueStyle={{ color: '#1890ff' }}
                    />
                    <div className="statistic-footer">
                      {dashboardData.today_orders.is_increase ? (
                        <Text type="success">
                          <ArrowUpOutlined /> {Math.abs(dashboardData.today_orders.change).toFixed(1)}%
                        </Text>
                      ) : (
                        <Text type="danger">
                          <ArrowDownOutlined /> {Math.abs(dashboardData.today_orders.change).toFixed(1)}%
                        </Text>
                      )}
                      <Text type="secondary" style={{ marginLeft: 8 }}>与昨日相比</Text>
                    </div>
                  </Card>
                </Col>
                <Col xs={24} sm={12} md={6}>
                  <Card>
                    <Statistic
                      title="总用户数"
                      value={dashboardData.total_users}
                      prefix={<UserOutlined />}
                      suffix="人"
                      valueStyle={{ color: '#722ed1' }}
                    />
                    <div className="statistic-footer">
                      {dashboardData.user_growth >= 0 ? (
                        <Text type="success">
                          <ArrowUpOutlined /> {Math.abs(dashboardData.user_growth).toFixed(1)}%
                        </Text>
                      ) : (
                        <Text type="danger">
                          <ArrowDownOutlined /> {Math.abs(dashboardData.user_growth).toFixed(1)}%
                        </Text>
                      )}
                      <Text type="secondary" style={{ marginLeft: 8 }}>增长率</Text>
                    </div>
                  </Card>
                </Col>
                <Col xs={24} sm={12} md={6}>
                  <Card>
                    <Statistic
                      title="AI使用量"
                      value={dashboardData.ai_usage}
                      prefix={<RobotOutlined />}
                      suffix="次"
                      valueStyle={{ color: '#fa8c16' }}
                    />
                    <div className="statistic-footer">
                      <Text type="secondary">本周期内AI使用总量</Text>
                    </div>
                  </Card>
                </Col>
              </Row>

              {/* 快捷入口 */}
              <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
                <Col xs={24} sm={8} md={6} lg={4}>
                  <Card
                    hoverable
                    onClick={() => navigate('/project/dashboard/store')}
                    style={{ textAlign: 'center', cursor: 'pointer' }}
                  >
                    <ShopOutlined style={{ fontSize: 24, color: '#1890ff', marginBottom: 8 }} />
                    <div>门店管理</div>
                  </Card>
                </Col>
                <Col xs={24} sm={8} md={6} lg={4}>
                  <Card
                    hoverable
                    onClick={() => navigate('/project/dashboard/warehouse')}
                    style={{ textAlign: 'center', cursor: 'pointer' }}
                  >
                    <InboxOutlined style={{ fontSize: 24, color: '#52c41a', marginBottom: 8 }} />
                    <div>仓储管理</div>
                  </Card>
                </Col>
                <Col xs={24} sm={8} md={6} lg={4}>
                  <Card
                    hoverable
                    onClick={() => navigate('/project/dashboard/finance')}
                    style={{ textAlign: 'center', cursor: 'pointer' }}
                  >
                    <DollarOutlined style={{ fontSize: 24, color: '#722ed1', marginBottom: 8 }} />
                    <div>财务管理</div>
                  </Card>
                </Col>
                <Col xs={24} sm={8} md={6} lg={4}>
                  <Card
                    hoverable
                    onClick={() => navigate('/project/dashboard/purchase')}
                    style={{ textAlign: 'center', cursor: 'pointer' }}
                  >
                    <ShoppingCartOutlined style={{ fontSize: 24, color: '#fa8c16', marginBottom: 8 }} />
                    <div>采购管理</div>
                  </Card>
                </Col>
                <Col xs={24} sm={8} md={6} lg={4}>
                  <Card
                    hoverable
                    onClick={() => navigate('/project/dashboard/operations')}
                    style={{ textAlign: 'center', cursor: 'pointer' }}
                  >
                    <BarChartOutlined style={{ fontSize: 24, color: '#13c2c2', marginBottom: 8 }} />
                    <div>运营管理</div>
                  </Card>
                </Col>
                <Col xs={24} sm={8} md={6} lg={4}>
                  <Card
                    hoverable
                    onClick={() => navigate('/project/ai/assistants')}
                    style={{ textAlign: 'center', cursor: 'pointer' }}
                  >
                    <RobotOutlined style={{ fontSize: 24, color: '#f5222d', marginBottom: 8 }} />
                    <div>AI助手</div>
                  </Card>
                </Col>
              </Row>

              {/* 图表区域 */}
              <Tabs defaultActiveKey="1" style={{ marginTop: 16 }}>
                <TabPane
                  tab={<span><LineChartOutlined /> 销售趋势</span>}
                  key="1"
                >
                  <Card>
                    {renderSalesTrendChart()}
                  </Card>
                </TabPane>
                <TabPane
                  tab={<span><WarningOutlined /> 库存预警</span>}
                  key="2"
                >
                  <Card>
                    {renderInventoryAlertsTable()}
                  </Card>
                </TabPane>
                <TabPane
                  tab={<span><ClockCircleOutlined /> 滞销商品</span>}
                  key="3"
                >
                  <Card>
                    {renderSlowMovingTable()}
                  </Card>
                </TabPane>
                <TabPane
                  tab={<span><ShopOutlined /> 门店性能</span>}
                  key="4"
                >
                  <Card>
                    {renderStorePerformanceTable()}
                  </Card>
                </TabPane>
                <TabPane
                  tab={<span><NotificationOutlined /> 系统通知</span>}
                  key="5"
                >
                  <Card>
                    {renderNotifications()}
                  </Card>
                </TabPane>
                <TabPane
                  tab={<span><BellOutlined /> 最近活动</span>}
                  key="6"
                >
                  <Card>
                    {renderRecentActivities()}
                  </Card>
                </TabPane>
              </Tabs>

              {/* AI推荐 */}
              <Card
                title={
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <span><RobotOutlined /> AI推荐</span>
                    <Button
                      icon={<SyncOutlined />}
                      onClick={refreshAIRecommendations}
                      loading={aiLoading}
                      size="small"
                    >
                      刷新AI推荐
                    </Button>
                  </div>
                }
                style={{ marginTop: 16 }}
              >
                {renderAIRecommendations()}
              </Card>
            </>
          ) : (
            <Empty description="暂无数据" />
          )}
        </Spin>
      </Card>
    </div>
  );
};

export default ProjectDashboard;
