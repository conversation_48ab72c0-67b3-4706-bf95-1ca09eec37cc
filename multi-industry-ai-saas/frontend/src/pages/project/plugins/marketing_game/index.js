import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  Table,
  Space,
  Tag,
  Modal,
  message,
  Tabs,
  Row,
  Col,
  Statistic,
  Tooltip,
  Typography,
  Input,
  Popover,
  Avatar,
  Badge,
  Empty,
  Layout,
  Dropdown
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  GiftOutlined,
  UserOutlined,
  HistoryOutlined,
  ExclamationCircleOutlined,
  QrcodeOutlined,
  CopyOutlined,
  LinkOutlined,
  TrophyOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  RocketOutlined,
  FireOutlined,
  StarOutlined,
  ThunderboltOutlined,
  DownOutlined,
  RobotOutlined,
  ReloadOutlined,
  WarningOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import dayjs from "dayjs";
import apiService from './service/api/marketingGame';
import GameForm from './components/GameForm';
import EnhancedGameForm from './components/EnhancedGameForm';
import AIAssistant from './components/AIAssistant';
import { gameStatusColors, gameStatusText, gameTypeText } from './utils/constants';

const { Title, Text, Paragraph } = Typography;
const { confirm } = Modal;
const { Content } = Layout;
const { TabPane } = Tabs;

const MarketingGameList = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [games, setGames] = useState([]);
  const [stats, setStats] = useState({
    total: 0,
    active: 0,
    ended: 0,
    participants: 0,
    winners: 0,
    todayParticipants: 0,
    conversionRate: 0
  });
  const [modalVisible, setModalVisible] = useState(false);
  const [currentGame, setCurrentGame] = useState(null);
  const [activeTab, setActiveTab] = useState('all');
  const [useEnhancedForm, setUseEnhancedForm] = useState(false);
  const [aiAssistantVisible, setAiAssistantVisible] = useState(false);
  const [selectedGameForAI, setSelectedGameForAI] = useState(null);

  // 游戏类型图标映射
  const gameTypeIcons = {
    lucky_draw: <GiftOutlined style={{ color: '#ff6b6b' }} />,
    scratch_card: <StarOutlined style={{ color: '#4ecdc4' }} />,
    wheel_of_fortune: <ThunderboltOutlined style={{ color: '#45b7d1' }} />,
    adventure_game: <RocketOutlined style={{ color: '#96ceb4' }} />
  };

  // 获取游戏列表
  const fetchGames = async (status = null) => {
    setLoading(true);
    try {
      const params = {};
      if (status && status !== 'all') {
        params.status = status;
      }

      const response = await apiService.getGames(params);

      if (response && response.success) {
        const gamesData = response.data || [];
        const safeGames = Array.isArray(gamesData) ? gamesData : [];
        setGames(safeGames);

        // 计算统计数据
        const total = safeGames.length;
        const active = safeGames.filter(game => game.status === 'published').length;
        const ended = safeGames.filter(game => game.status === 'ended').length;

        // 获取参与者和中奖者数量
        let participants = 0;
        let winners = 0;
        let todayParticipants = 0;
        
        const today = dayjs().format('YYYY-MM-DD');

        // 遍历每个游戏，获取参与者和中奖者数量
        const fetchGameStats = async () => {
          try {
            for (const game of safeGames) {
              // 获取参与者数量
              const participantsResponse = await apiService.getParticipants(game.id);
              if (participantsResponse.success && Array.isArray(participantsResponse.data)) {
                participants += participantsResponse.data.length;
                // 统计今天的参与者
                todayParticipants += participantsResponse.data.filter(p => 
                  dayjs(p.created_at).format('YYYY-MM-DD') === today
                ).length;
              }

              // 获取中奖记录数量
              const recordsResponse = await apiService.getRecords(game.id);
              if (recordsResponse.success && Array.isArray(recordsResponse.data)) {
                // 只计算中奖的记录
                winners += recordsResponse.data.filter(record => record.is_win === true).length;
              }
            }

            // 计算转化率
            const conversionRate = participants > 0 ? ((winners / participants) * 100).toFixed(1) : 0;

            // 更新统计数据
            setStats({ 
              total, 
              active, 
              ended, 
              participants, 
              winners, 
              todayParticipants,
              conversionRate: parseFloat(conversionRate)
            });
          } catch (error) {
            console.error('获取游戏统计数据失败:', error);
          }
        };

        // 如果有游戏，获取统计数据
        if (safeGames.length > 0) {
          fetchGameStats();
        } else {
          // 如果没有游戏，直接设置统计数据为0
          setStats({ 
            total: 0, 
            active: 0, 
            ended: 0, 
            participants: 0, 
            winners: 0, 
            todayParticipants: 0,
            conversionRate: 0 
          });
        }
      } else {
        message.error(response?.message || '获取营销游戏列表失败');
        setGames([]);
        setStats({ 
          total: 0, 
          active: 0, 
          ended: 0, 
          participants: 0, 
          winners: 0, 
          todayParticipants: 0,
          conversionRate: 0 
        });
      }
    } catch (error) {
      console.error('获取营销游戏列表失败:', error);
      if (error.response && error.response.status === 404) {
        message.error('营销游戏插件未安装或未正确配置，请联系管理员');
      } else {
        message.error(error.message || '获取营销游戏列表失败');
      }
      setGames([]);
      setStats({ 
        total: 0, 
        active: 0, 
        ended: 0, 
        participants: 0, 
        winners: 0, 
        todayParticipants: 0,
        conversionRate: 0 
      });
    } finally {
      setLoading(false);
    }
  };

  // 初始加载
  useEffect(() => {
    fetchGames();
  }, []);

  // 处理标签页切换
  const handleTabChange = (key) => {
    setActiveTab(key);
    fetchGames(key === 'all' ? null : key);
  };

  // 打开创建游戏表单
  const showCreateForm = () => {
    setCurrentGame(null);
    setModalVisible(true);
  };

  // 打开编辑游戏表单
  const showEditForm = (game) => {
    setCurrentGame(game);
    setModalVisible(true);
  };

  // 处理表单提交
  const handleFormSubmit = async (values) => {
    try {
      let response;

      if (currentGame) {
        response = await apiService.updateGame(currentGame.id, values);
      } else {
        response = await apiService.createGame(values);
      }

      if (response && response.success) {
        message.success(response.message || (currentGame ? '营销游戏更新成功' : '营销游戏创建成功'));
        setModalVisible(false);
        fetchGames(activeTab === 'all' ? null : activeTab);
      } else {
        message.error(response?.message || '操作失败');
      }
    } catch (error) {
      console.error('提交表单失败:', error);
      message.error(error.message || '操作失败');
    }
  };

  // 处理删除游戏
  const handleDeleteGame = (game) => {
    confirm({
      title: '确认删除',
      icon: <ExclamationCircleOutlined />,
      content: `确定要删除营销游戏"${game.name}"吗？此操作不可恢复。`,
      okText: '确认',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          const response = await apiService.deleteGame(game.id);
          if (response && response.success) {
            message.success(response.message || '营销游戏删除成功');
            fetchGames(activeTab === 'all' ? null : activeTab);
          } else {
            message.error(response?.message || '删除失败');
          }
        } catch (error) {
          console.error('删除游戏失败:', error);
          message.error(error.message || '删除失败');
        }
      },
    });
  };

  // 查看游戏详情
  const handleViewGame = (game) => {
    navigate(`/project/plugins/marketing_game/detail/${game.id}`);
  };

  // 管理奖品
  const handleManagePrizes = (game) => {
    navigate(`/project/plugins/marketing_game/prizes/${game.id}`);
  };

  // 查看参与者
  const handleViewParticipants = (game) => {
    navigate(`/project/plugins/marketing_game/participants/${game.id}`);
  };

  // 查看游戏记录
  const handleViewRecords = (game) => {
    navigate(`/project/plugins/marketing_game/records/${game.id}`);
  };

  // 生成游戏链接
  const generateGameLink = (gameId) => {
    const baseUrl = window.location.origin;
    return `${baseUrl}/game/marketing/${gameId}`;
  };

  // 复制到剪贴板
  const copyToClipboard = (text) => {
    // 检查是否支持 Clipboard API
    if (navigator.clipboard && navigator.clipboard.writeText) {
      navigator.clipboard.writeText(text).then(() => {
        message.success('已复制到剪贴板');
      }).catch((err) => {
        console.error('使用 Clipboard API 复制失败:', err);
        fallbackCopyTextToClipboard(text);
      });
    } else {
      // 使用 fallback 方法
      fallbackCopyTextToClipboard(text);
    }
  };

  // Fallback 复制方法（兼容旧浏览器）
  const fallbackCopyTextToClipboard = (text) => {
    const textArea = document.createElement("textarea");
    textArea.value = text;
    
    // 避免在页面上显示
    textArea.style.position = "fixed";
    textArea.style.top = "0";
    textArea.style.left = "0";
    textArea.style.width = "2em";
    textArea.style.height = "2em";
    textArea.style.padding = "0";
    textArea.style.border = "none";
    textArea.style.outline = "none";
    textArea.style.boxShadow = "none";
    textArea.style.background = "transparent";
    
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    
    try {
      const successful = document.execCommand('copy');
      if (successful) {
        message.success('已复制到剪贴板');
      } else {
        message.error('复制失败，请手动复制');
      }
    } catch (err) {
      console.error('Fallback 复制方法失败:', err);
      message.error('复制失败，请手动复制');
    }
    
    document.body.removeChild(textArea);
  };

  // 显示二维码
  const showQRCode = (gameId) => {
    Modal.info({
      title: '游戏二维码',
      content: (
        <div style={{ textAlign: 'center', padding: '20px' }}>
          <div style={{ fontSize: '48px', marginBottom: '16px' }}>
            <QrcodeOutlined />
          </div>
          <p>请使用微信扫描二维码参与游戏</p>
          <p style={{ color: '#999', fontSize: '12px' }}>
            游戏链接：{generateGameLink(gameId)}
          </p>
        </div>
      ),
      width: 400,
    });
  };

  // 渲染统计卡片
  const renderStatsCards = () => (
    <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
      <Col xs={12} sm={8} md={6} lg={4}>
        <Card hoverable style={{ background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' }}>
          <Statistic
            title={<span style={{ color: 'rgba(255,255,255,0.8)' }}>总游戏数</span>}
            value={stats.total}
            valueStyle={{ color: '#fff', fontSize: '24px' }}
            prefix={<FireOutlined />}
          />
        </Card>
      </Col>
      <Col xs={12} sm={8} md={6} lg={4}>
        <Card hoverable style={{ background: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)' }}>
          <Statistic
            title={<span style={{ color: 'rgba(255,255,255,0.8)' }}>进行中</span>}
            value={stats.active}
            valueStyle={{ color: '#fff', fontSize: '24px' }}
            prefix={<PlayCircleOutlined />}
          />
        </Card>
      </Col>
      <Col xs={12} sm={8} md={6} lg={4}>
        <Card hoverable style={{ background: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)' }}>
          <Statistic
            title={<span style={{ color: 'rgba(255,255,255,0.8)' }}>总参与</span>}
            value={stats.participants}
            valueStyle={{ color: '#fff', fontSize: '24px' }}
            prefix={<UserOutlined />}
          />
        </Card>
      </Col>
      <Col xs={12} sm={8} md={6} lg={4}>
        <Card hoverable style={{ background: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)' }}>
          <Statistic
            title={<span style={{ color: 'rgba(255,255,255,0.8)' }}>总中奖</span>}
            value={stats.winners}
            valueStyle={{ color: '#fff', fontSize: '24px' }}
            prefix={<TrophyOutlined />}
          />
        </Card>
      </Col>
      <Col xs={12} sm={8} md={6} lg={4}>
        <Card hoverable style={{ background: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)' }}>
          <Statistic
            title={<span style={{ color: 'rgba(255,255,255,0.8)' }}>今日参与</span>}
            value={stats.todayParticipants}
            valueStyle={{ color: '#fff', fontSize: '24px' }}
            prefix={<ThunderboltOutlined />}
          />
        </Card>
      </Col>
      <Col xs={12} sm={8} md={6} lg={4}>
        <Card hoverable style={{ background: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)' }}>
          <Statistic
            title={<span style={{ color: 'rgba(255,255,255,0.8)' }}>中奖率</span>}
            value={stats.conversionRate}
            valueStyle={{ color: '#fff', fontSize: '24px' }}
            suffix={<span style={{ fontSize: '14px' }}>%</span>}
            prefix={<StarOutlined />}
          />
        </Card>
      </Col>
    </Row>
  );

  // 定义表格列
  const columns = [
    {
      title: '游戏信息',
      key: 'gameInfo',
      width: 300,
      render: (_, record) => (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Avatar 
            size={48} 
            icon={gameTypeIcons[record.game_type] || <GiftOutlined />}
            style={{ 
              marginRight: 12,
              background: `linear-gradient(135deg, ${gameStatusColors[record.status] || '#ccc'} 0%, #764ba2 100%)`
            }}
          />
          <div>
            <div style={{ fontWeight: 'bold', fontSize: '14px' }}>{record.name}</div>
            <div style={{ color: '#666', fontSize: '12px' }}>
              {gameTypeText[record.game_type] || record.game_type}
            </div>
            <div style={{ color: '#999', fontSize: '11px' }}>
              创建时间：{dayjs(record.created_at).format('MM-DD HH:mm')}
            </div>
          </div>
        </div>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 120,
      render: (status) => {
        const statusConfig = {
          draft: { color: '#d9d9d9', text: '草稿', icon: <EditOutlined /> },
          published: { color: '#52c41a', text: '进行中', icon: <PlayCircleOutlined /> },
          ended: { color: '#ff4d4f', text: '已结束', icon: <PauseCircleOutlined /> },
        };
        const config = statusConfig[status] || statusConfig.draft;
        return (
          <Tag color={config.color} icon={config.icon}>
            {config.text}
          </Tag>
        );
      },
    },
    {
      title: '时间范围',
      key: 'dateRange',
      width: 200,
      render: (_, record) => (
        <div>
          <div style={{ fontSize: '12px' }}>
            开始：{record.start_time ? dayjs(record.start_time).format('MM-DD HH:mm') : '未设置'}
          </div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            结束：{record.end_time ? dayjs(record.end_time).format('MM-DD HH:mm') : '未设置'}
          </div>
        </div>
      ),
    },
    {
      title: '参与统计',
      key: 'stats',
      width: 120,
      render: (_, record) => (
        <div>
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: 4 }}>
            <UserOutlined style={{ fontSize: '14px', color: '#1890ff', marginRight: 4 }} />
            <span style={{ fontSize: '12px' }}>参与: {record.participant_count || 0}</span>
          </div>
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <TrophyOutlined style={{ fontSize: '14px', color: '#52c41a', marginRight: 4 }} />
            <span style={{ fontSize: '12px' }}>中奖: {record.winner_count || 0}</span>
          </div>
        </div>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_, record) => (
        <Space size="small" wrap>
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => handleViewGame(record)}
            />
          </Tooltip>
          <Tooltip title="编辑游戏">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => showEditForm(record)}
            />
          </Tooltip>
          <Tooltip title="管理奖品">
            <Button
              type="text"
              icon={<GiftOutlined />}
              onClick={() => handleManagePrizes(record)}
            />
          </Tooltip>
          <Tooltip title="参与者">
            <Button
              type="text"
              icon={<UserOutlined />}
              onClick={() => handleViewParticipants(record)}
            />
          </Tooltip>
          <Tooltip title="游戏记录">
            <Button
              type="text"
              icon={<HistoryOutlined />}
              onClick={() => handleViewRecords(record)}
            />
          </Tooltip>
          <Tooltip title="AI助手">
            <Button
              type="text"
              icon={<RobotOutlined />}
              onClick={() => {
                setSelectedGameForAI(record.id);
                setAiAssistantVisible(true);
              }}
            />
          </Tooltip>
          <Tooltip title="初始化游戏">
            <Button
              type="text"
              danger
              icon={<ReloadOutlined />}
              onClick={() => handleInitializeSingleGame(record)}
            />
          </Tooltip>
          <Popover
            content={
              <div style={{ minWidth: 200 }}>
                <div style={{ marginBottom: 8 }}>
                  <Input.Group compact>
                    <Input
                      style={{ width: '80%' }}
                      value={generateGameLink(record.id)}
                      readOnly
                      size="small"
                    />
                    <Button
                      size="small"
                      icon={<CopyOutlined />}
                      onClick={() => copyToClipboard(generateGameLink(record.id))}
                    />
                  </Input.Group>
                </div>
                <Button
                  block
                  size="small"
                  icon={<QrcodeOutlined />}
                  onClick={() => showQRCode(record.id)}
                >
                  显示二维码
                </Button>
              </div>
            }
            title="分享游戏"
            trigger="click"
          >
            <Tooltip title="分享">
              <Button type="text" icon={<LinkOutlined />} />
            </Tooltip>
          </Popover>
          <Tooltip title="删除">
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
              onClick={() => handleDeleteGame(record)}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  // 处理AI助手创建游戏的回调
  const handleGameCreated = (newGame) => {
    // 刷新游戏列表
    fetchGames();
    message.success(`游戏"${newGame.name}"创建成功！`);
  };

  // 处理AI助手更新游戏的回调
  const handleGameUpdated = (updatedGame) => {
    // 刷新游戏列表
    fetchGames();
    message.success(`游戏"${updatedGame.name}"更新成功！`);
  };

  // 一键初始化营销游戏数据
  const handleInitializeGames = () => {
    confirm({
      title: '确认初始化',
      icon: <WarningOutlined style={{ color: '#ff4d4f' }} />,
      content: (
        <div>
          <p style={{ color: '#ff4d4f', fontWeight: 'bold' }}>
            ⚠️ 危险操作：此操作将清空所有营销游戏数据！
          </p>
          <p>包括：</p>
          <ul>
            <li>所有游戏配置</li>
            <li>所有奖品设置</li>
            <li>所有参与者记录</li>
            <li>所有游戏记录</li>
          </ul>
          <p style={{ color: '#ff4d4f' }}>
            此操作不可恢复，请确认是否继续？
          </p>
        </div>
      ),
      okText: '确认初始化',
      okType: 'danger',
      cancelText: '取消',
      width: 500,
      onOk: async () => {
        try {
          setLoading(true);
          const response = await apiService.initializeGames();
          
          if (response.success) {
            message.success(
              `初始化成功！已清空 ${response.data.deleted_counts.games} 个游戏、` +
              `${response.data.deleted_counts.prizes} 个奖品、` +
              `${response.data.deleted_counts.participants} 个参与者、` +
              `${response.data.deleted_counts.records} 条记录`
            );
            
            // 刷新页面数据
            fetchGames();
          } else {
            message.error(response.message || '初始化失败');
          }
        } catch (error) {
          console.error('初始化失败:', error);
          // 安全地提取错误信息
          let errorMessage = '初始化失败，请稍后重试';
          if (error && typeof error === 'object') {
            if (error.response && error.response.data && error.response.data.detail) {
              errorMessage = error.response.data.detail;
            } else if (error.message) {
              errorMessage = error.message;
            } else if (typeof error === 'string') {
              errorMessage = error;
            }
          }
          message.error(errorMessage);
        } finally {
          setLoading(false);
        }
      },
    });
  };

  // 初始化单个游戏数据
  const handleInitializeSingleGame = (game) => {
    confirm({
      title: '确认初始化游戏',
      icon: <WarningOutlined style={{ color: '#ff4d4f' }} />,
      content: (
        <div>
          <p style={{ color: '#ff4d4f', fontWeight: 'bold' }}>
            ⚠️ 危险操作：此操作将清空游戏"{game.name}"的数据！
          </p>
          <p>将清空：</p>
          <ul>
            <li>所有奖品设置</li>
            <li>所有参与者记录</li>
            <li>所有游戏记录</li>
          </ul>
          <p style={{ color: '#52c41a' }}>
            ✅ 游戏配置将保留
          </p>
          <p style={{ color: '#ff4d4f' }}>
            此操作不可恢复，请确认是否继续？
          </p>
        </div>
      ),
      okText: '确认初始化',
      okType: 'danger',
      cancelText: '取消',
      width: 500,
      onOk: async () => {
        try {
          setLoading(true);
          const response = await apiService.initializeSingleGame(game.id);
          
          if (response.success) {
            // 安全地访问response数据
            const gameData = response.data || {};
            const deletedCounts = gameData.deleted_counts || {
              prizes: 0,
              participants: 0,
              records: 0
            };
            const gameName = gameData.game_name || game.name || '游戏';
            
            message.success(
              `游戏"${gameName}"初始化成功！已清空 ` +
              `${deletedCounts.prizes} 个奖品、` +
              `${deletedCounts.participants} 个参与者、` +
              `${deletedCounts.records} 条记录`
            );
            
            // 刷新页面数据
            fetchGames();
          } else {
            message.error(response.message || '初始化失败');
          }
        } catch (error) {
          console.error('初始化失败:', error);
          // 安全地提取错误信息
          let errorMessage = '初始化失败，请稍后重试';
          if (error && typeof error === 'object') {
            if (error.response && error.response.data && error.response.data.detail) {
              errorMessage = error.response.data.detail;
            } else if (error.message) {
              errorMessage = error.message;
            } else if (typeof error === 'string') {
              errorMessage = error;
            }
          }
          message.error(errorMessage);
        } finally {
          setLoading(false);
        }
      },
    });
  };

  return (
    <div className="marketing-game-plugin">
      <Card title="营销游戏管理" className="ant-card ant-card-bordered plugin-header css-dev-only-do-not-override-vrrzze">
        <Content style={{ padding: '24px' }}>
          {/* 页面头部 */}
          <div style={{ marginBottom: 24 }}>
            <Title level={2} style={{ margin: 0 }}>
              <GiftOutlined style={{ marginRight: 8, color: '#1890ff' }} />
              营销游戏管理
            </Title>
            <Paragraph style={{ margin: '8px 0 0 0', color: '#666' }}>
              创建和管理各种营销游戏，提升用户互动和转化率
            </Paragraph>
          </div>

          {/* 统计卡片 */}
          {renderStatsCards()}

          {/* 主内容区域 */}
          <Card style={{ borderRadius: 8, boxShadow: '0 2px 8px rgba(0,0,0,0.06)' }}>
        <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Title level={4} style={{ margin: 0 }}>游戏列表</Title>
          <Space>
            <Dropdown
              menu={{
                items: [
                  {
                    key: 'basic',
                    label: '创建基础游戏',
                    icon: <PlusOutlined />,
                    onClick: () => {
                      setCurrentGame(null);
                      setUseEnhancedForm(false);
                      setModalVisible(true);
                    }
                  },
                  {
                    key: 'enhanced',
                    label: '创建增强版游戏',
                    icon: <RocketOutlined />,
                    onClick: () => {
                      setCurrentGame(null);
                      setUseEnhancedForm(true);
                      setModalVisible(true);
                    }
                  }
                ]
              }}
              trigger={['click']}
            >
              <Button type="primary" size="large" style={{ borderRadius: 6 }}>
                <Space>
                  创建游戏
                  <DownOutlined />
                </Space>
              </Button>
            </Dropdown>
            <Button 
              icon={<RobotOutlined />}
              onClick={() => setAiAssistantVisible(true)}
              style={{ borderRadius: 6 }}
            >
              AI助手
            </Button>
            <Button 
              icon={<ReloadOutlined />}
              onClick={handleInitializeGames}
              danger
              style={{ borderRadius: 6 }}
            >
              一键初始化
            </Button>
          </Space>
        </div>

        <Tabs activeKey={activeTab} onChange={handleTabChange} style={{ marginBottom: 16 }}>
          <TabPane tab="全部游戏" key="all" />
          <TabPane tab="草稿" key="draft" />
          <TabPane tab="进行中" key="published" />
          <TabPane tab="已结束" key="ended" />
        </Tabs>

        <Table
          columns={columns}
          dataSource={games}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条记录`,
          }}
          locale={{
            emptyText: (
              <Empty 
                image={Empty.PRESENTED_IMAGE_SIMPLE}
                description="暂无游戏，点击上方按钮创建你的第一个营销游戏"
              />
            )
          }}
          style={{ marginTop: 16 }}
        />
      </Card>

          {/* 创建/编辑游戏模态框 */}
          <Modal
            title={currentGame ? '编辑营销游戏' : '创建营销游戏'}
            open={modalVisible}
            onCancel={() => setModalVisible(false)}
            footer={null}
            width={useEnhancedForm ? 1000 : 800}
            destroyOnClose
          >
            {useEnhancedForm ? (
              <EnhancedGameForm
                game={currentGame}
                visible={modalVisible}
                onCancel={() => setModalVisible(false)}
                onSubmit={handleFormSubmit}
                loading={loading}
              />
            ) : (
              <GameForm
                initialValues={currentGame}
                onSubmit={handleFormSubmit}
                onCancel={() => setModalVisible(false)}
              />
            )}
          </Modal>

          {/* AI助手 */}
          <AIAssistant
            gameId={selectedGameForAI}
            visible={aiAssistantVisible}
            onClose={() => {
              setAiAssistantVisible(false);
              setSelectedGameForAI(null);
            }}
            onGameCreated={handleGameCreated}
            onGameUpdated={handleGameUpdated}
          />
        </Content>
      </Card>
    </div>
  );
};

export default MarketingGameList;
