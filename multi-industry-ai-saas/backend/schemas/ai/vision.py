#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
AI 视觉 Schema
"""

from typing import Optional, List, Dict, Any, Union
from datetime import datetime
import uuid
from pydantic import BaseModel, Field, validator

class AIVisionRequest(BaseModel):
    """AI 视觉请求 Schema"""
    image_base64: str = Field(..., description="Base64 编码的图像数据")
    prompt: str = Field(..., description="分析图像的提示词")
    model_id: Optional[uuid.UUID] = Field(None, description="模型 ID，如果不指定则使用默认模型")
    config_id: Optional[uuid.UUID] = Field(None, description="配置 ID，如果不指定则使用默认配置")
    temperature: Optional[float] = Field(None, ge=0, le=2, description="温度参数，控制随机性")
    max_tokens: Optional[int] = Field(None, gt=0, description="最大 token 数")
    additional_messages: Optional[List[Dict[str, Any]]] = Field(None, description="额外的消息，用于多轮对话")

class AIVisionMessageContent(BaseModel):
    """AI 视觉消息内容 Schema"""
    type: str = Field(..., description="内容类型：text, image_url")
    text: Optional[str] = Field(None, description="文本内容")
    image_url: Optional[Dict[str, Any]] = Field(None, description="图像 URL")

class AIVisionMessage(BaseModel):
    """AI 视觉消息 Schema"""
    role: str = Field(..., description="消息角色：system, user, assistant")
    content: Union[str, List[AIVisionMessageContent]] = Field(..., description="消息内容")

class AIVisionResponseChoice(BaseModel):
    """AI 视觉响应选项 Schema"""
    index: int = Field(..., description="选项索引")
    message: AIVisionMessage = Field(..., description="消息内容")
    finish_reason: Optional[str] = Field(None, description="结束原因")

class AIVisionResponseUsage(BaseModel):
    """AI 视觉响应使用情况 Schema"""
    prompt_tokens: int = Field(..., description="提示 token 数")
    completion_tokens: int = Field(..., description="完成 token 数")
    total_tokens: int = Field(..., description="总 token 数")

class AIVisionResponse(BaseModel):
    """AI 视觉响应 Schema"""
    id: str = Field(..., description="响应 ID")
    object: str = Field(..., description="对象类型")
    created: int = Field(..., description="创建时间戳")
    model: str = Field(..., description="模型名称")
    choices: List[AIVisionResponseChoice] = Field(..., description="响应选项列表")
    usage: Optional[AIVisionResponseUsage] = Field(None, description="使用情况")

class AIVisionErrorResponse(BaseModel):
    """AI 视觉错误响应 Schema"""
    success: bool = Field(False, description="是否成功")
    message: str = Field(..., description="错误信息")
    error: Optional[Dict[str, Any]] = Field(None, description="错误详情")
