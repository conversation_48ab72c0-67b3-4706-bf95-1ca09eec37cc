/**
 * 系统设置页面
 * 系统管理员用于配置全局系统设置
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Input,
  Button,
  Switch,
  Select,
  InputNumber,
  Tabs,
  message,
  Typography,
  Divider,
  Space,
  Upload,
  Row,
  Col
} from 'antd';
import {
  SaveOutlined,
  UploadOutlined,
  GlobalOutlined,
  MailOutlined,
  CloudOutlined,
  SecurityScanOutlined,
  BgColorsOutlined,
  TeamOutlined
} from '@ant-design/icons';
import apiService from '../../services/api';

const { Title, Paragraph } = Typography;
const { Option } = Select;
const { TabPane } = Tabs;
const { TextArea } = Input;

const SystemSettings = () => {
  const [generalForm] = Form.useForm();
  const [emailForm] = Form.useForm();
  const [storageForm] = Form.useForm();
  const [securityForm] = Form.useForm();
  const [themeForm] = Form.useForm();
  const [rolesForm] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [logoUrl, setLogoUrl] = useState('');

  // 加载系统设置
  useEffect(() => {
    const fetchSettings = async () => {
      try {
        setLoading(true);

        // 实际项目中应该调用API获取系统设置
        // const settings = await apiService.getSystemSettings();

        // 模拟从本地存储获取设置或使用默认值
        let settings = {};
        const savedSettings = localStorage.getItem('system_settings');
        if (savedSettings) {
          settings = JSON.parse(savedSettings);
        } else {
          // 默认设置
          settings = {
            // 通用设置
            system_name: 'Retail AI SaaS',
            company_name: 'Retail AI',
            contact_email: '<EMAIL>',
            support_phone: '************',
            default_language: 'zh-CN',
            enable_registration: true,
            require_email_verification: true,
            logo_url: '',

            // 邮件设置
            smtp_server: 'smtp.example.com',
            smtp_port: 587,
            smtp_username: '<EMAIL>',
            smtp_password: '',
            smtp_use_tls: true,
            email_from_address: '<EMAIL>',
            email_from_name: 'Retail AI System',

            // 存储设置
            storage_provider: 'local',
            default_user_quota: 500,
            max_file_size: 50,
            allowed_file_types: 'jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx,ppt,pptx,txt,zip,rar',
            s3_bucket: '',
            s3_region: '',
            s3_access_key: '',
            s3_secret_key: '',

            // 安全设置
            password_min_length: 8,
            password_require_uppercase: true,
            password_require_lowercase: true,
            password_require_number: true,
            password_require_special: false,
            session_timeout: 60,
            max_login_attempts: 5,
            enable_2fa: false,

            // 主题设置
            primary_color: '#1890ff',
            secondary_color: '#52c41a',
            default_theme: 'light',
            enable_theme_customization: true,

            // 角色命名设置
            service_provider_name: '服务商',
            tenant_name: '租户',
            admin_name: '管理员',
            user_name: '用户'
          };
        }

        // 设置通用表单数据
        generalForm.setFieldsValue({
          system_name: settings.system_name,
          company_name: settings.company_name,
          contact_email: settings.contact_email,
          support_phone: settings.support_phone,
          default_language: settings.default_language,
          enable_registration: settings.enable_registration,
          require_email_verification: settings.require_email_verification,
          logo_url: settings.logo_url,
        });

        // 设置邮件表单数据
        emailForm.setFieldsValue({
          smtp_server: settings.smtp_server,
          smtp_port: settings.smtp_port,
          smtp_username: settings.smtp_username,
          smtp_password: settings.smtp_password,
          smtp_use_tls: settings.smtp_use_tls,
          email_from_address: settings.email_from_address,
          email_from_name: settings.email_from_name,
        });

        // 设置存储表单数据
        storageForm.setFieldsValue({
          storage_provider: settings.storage_provider,
          default_user_quota: settings.default_user_quota,
          max_file_size: settings.max_file_size,
          allowed_file_types: settings.allowed_file_types,
          s3_bucket: settings.s3_bucket,
          s3_region: settings.s3_region,
          s3_access_key: settings.s3_access_key,
          s3_secret_key: settings.s3_secret_key,
        });

        // 设置安全表单数据
        securityForm.setFieldsValue({
          password_min_length: settings.password_min_length,
          password_require_uppercase: settings.password_require_uppercase,
          password_require_lowercase: settings.password_require_lowercase,
          password_require_number: settings.password_require_number,
          password_require_special: settings.password_require_special,
          session_timeout: settings.session_timeout,
          max_login_attempts: settings.max_login_attempts,
          enable_2fa: settings.enable_2fa,
        });

        // 设置主题表单数据
        themeForm.setFieldsValue({
          primary_color: settings.primary_color,
          secondary_color: settings.secondary_color,
          default_theme: settings.default_theme,
          enable_theme_customization: settings.enable_theme_customization,
        });

        // 设置角色命名表单数据
        rolesForm.setFieldsValue({
          service_provider_name: settings.service_provider_name || '服务商',
          tenant_name: settings.tenant_name || '租户',
          admin_name: settings.admin_name || '管理员',
          user_name: settings.user_name || '用户'
        });

        // 设置Logo URL
        setLogoUrl(settings.logo_url);
      } catch (error) {
        message.error('获取系统设置失败');
        console.error('获取系统设置失败:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchSettings();
  }, [generalForm, emailForm, storageForm, securityForm, themeForm, rolesForm]);

  // 保存通用设置
  const saveGeneralSettings = async (values) => {
    try {
      setLoading(true);
      await apiService.updateSystemSettings({
        ...values,
        section: 'general',
      });
      message.success('通用设置保存成功');
    } catch (error) {
      message.error('保存通用设置失败');
      console.error('保存通用设置失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 保存邮件设置
  const saveEmailSettings = async (values) => {
    try {
      setLoading(true);
      await apiService.updateSystemSettings({
        ...values,
        section: 'email',
      });
      message.success('邮件设置保存成功');
    } catch (error) {
      message.error('保存邮件设置失败');
      console.error('保存邮件设置失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 保存存储设置
  const saveStorageSettings = async (values) => {
    try {
      setLoading(true);
      await apiService.updateSystemSettings({
        ...values,
        section: 'storage',
      });
      message.success('存储设置保存成功');
    } catch (error) {
      message.error('保存存储设置失败');
      console.error('保存存储设置失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 保存安全设置
  const saveSecuritySettings = async (values) => {
    try {
      setLoading(true);
      await apiService.updateSystemSettings({
        ...values,
        section: 'security',
      });
      message.success('安全设置保存成功');
    } catch (error) {
      message.error('保存安全设置失败');
      console.error('保存安全设置失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 保存主题设置
  const saveThemeSettings = async (values) => {
    try {
      setLoading(true);
      await apiService.updateSystemSettings({
        ...values,
        section: 'theme',
      });
      message.success('主题设置保存成功');
    } catch (error) {
      message.error('保存主题设置失败');
      console.error('保存主题设置失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 保存角色命名设置
  const saveRolesSettings = async (values) => {
    try {
      setLoading(true);

      // 实际项目中应该调用API保存设置
      // await apiService.updateSystemSettings({
      //   ...values,
      //   section: 'roles',
      // });

      // 模拟保存到本地存储
      const savedSettings = localStorage.getItem('system_settings');
      let settings = savedSettings ? JSON.parse(savedSettings) : {};

      // 更新角色命名设置
      settings = {
        ...settings,
        ...values
      };

      localStorage.setItem('system_settings', JSON.stringify(settings));

      message.success('角色命名设置保存成功');
    } catch (error) {
      message.error('保存角色命名设置失败');
      console.error('保存角色命名设置失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 测试邮件设置
  const testEmailSettings = async () => {
    try {
      setLoading(true);
      const values = await emailForm.validateFields();
      await apiService.testEmailSettings(values);
      message.success('测试邮件发送成功，请检查您的邮箱');
    } catch (error) {
      message.error('测试邮件发送失败');
      console.error('测试邮件发送失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 上传Logo配置
  const uploadProps = {
    name: 'file',
    action: '/api/upload/logo',
    headers: {
      authorization: 'Bearer ' + localStorage.getItem('token'),
    },
    onChange(info) {
      if (info.file.status === 'done') {
        message.success(`${info.file.name} 上传成功`);
        const logoUrl = info.file.response.url;
        setLogoUrl(logoUrl);
        generalForm.setFieldsValue({ logo_url: logoUrl });
      } else if (info.file.status === 'error') {
        message.error(`${info.file.name} 上传失败`);
      }
    },
  };

  return (
    <div>
      <Title level={2}>系统设置</Title>
      <Paragraph>配置系统的全局设置，包括通用设置、邮件设置、存储设置、安全设置和主题设置。</Paragraph>

      <Tabs defaultActiveKey="general">
        {/* 通用设置 */}
        <TabPane
          tab={<span><GlobalOutlined /> 通用设置</span>}
          key="general"
        >
          <Card loading={loading}>
            <Form
              form={generalForm}
              layout="vertical"
              onFinish={saveGeneralSettings}
            >
              <Row gutter={24}>
                <Col span={12}>
                  <Form.Item
                    name="system_name"
                    label="系统名称"
                    rules={[{ required: true, message: '请输入系统名称' }]}
                  >
                    <Input placeholder="系统名称" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="company_name"
                    label="公司名称"
                  >
                    <Input placeholder="公司名称" />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={24}>
                <Col span={12}>
                  <Form.Item
                    name="contact_email"
                    label="联系邮箱"
                    rules={[
                      { required: true, message: '请输入联系邮箱' },
                      { type: 'email', message: '请输入有效的邮箱地址' }
                    ]}
                  >
                    <Input placeholder="联系邮箱" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="support_phone"
                    label="支持电话"
                  >
                    <Input placeholder="支持电话" />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={24}>
                <Col span={12}>
                  <Form.Item
                    name="default_language"
                    label="默认语言"
                    initialValue="zh-CN"
                  >
                    <Select>
                      <Option value="zh-CN">简体中文</Option>
                      <Option value="en-US">English</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="logo_url"
                    label="系统Logo"
                  >
                    <Input placeholder="Logo URL" />
                  </Form.Item>
                  <Form.Item>
                    <Upload {...uploadProps}>
                      <Button icon={<UploadOutlined />}>上传Logo</Button>
                    </Upload>
                    {logoUrl && (
                      <div style={{ marginTop: 8 }}>
                        <img src={logoUrl} alt="Logo" style={{ maxHeight: 100 }} />
                      </div>
                    )}
                  </Form.Item>
                </Col>
              </Row>

              <Divider />

              <Row gutter={24}>
                <Col span={12}>
                  <Form.Item
                    name="enable_registration"
                    label="启用用户注册"
                    valuePropName="checked"
                    initialValue={true}
                  >
                    <Switch />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="require_email_verification"
                    label="要求邮箱验证"
                    valuePropName="checked"
                    initialValue={true}
                  >
                    <Switch />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  icon={<SaveOutlined />}
                  loading={loading}
                >
                  保存设置
                </Button>
              </Form.Item>
            </Form>
          </Card>
        </TabPane>

        {/* 邮件设置 */}
        <TabPane
          tab={<span><MailOutlined /> 邮件设置</span>}
          key="email"
        >
          <Card loading={loading}>
            <Form
              form={emailForm}
              layout="vertical"
              onFinish={saveEmailSettings}
            >
              <Row gutter={24}>
                <Col span={16}>
                  <Form.Item
                    name="smtp_server"
                    label="SMTP服务器"
                    rules={[{ required: true, message: '请输入SMTP服务器' }]}
                  >
                    <Input placeholder="SMTP服务器地址" />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    name="smtp_port"
                    label="SMTP端口"
                    rules={[{ required: true, message: '请输入SMTP端口' }]}
                    initialValue={587}
                  >
                    <InputNumber min={1} max={65535} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={24}>
                <Col span={12}>
                  <Form.Item
                    name="smtp_username"
                    label="SMTP用户名"
                    rules={[{ required: true, message: '请输入SMTP用户名' }]}
                  >
                    <Input placeholder="SMTP用户名" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="smtp_password"
                    label="SMTP密码"
                    rules={[{ required: true, message: '请输入SMTP密码' }]}
                  >
                    <Input.Password placeholder="SMTP密码" />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={24}>
                <Col span={12}>
                  <Form.Item
                    name="email_from_address"
                    label="发件人邮箱"
                    rules={[
                      { required: true, message: '请输入发件人邮箱' },
                      { type: 'email', message: '请输入有效的邮箱地址' }
                    ]}
                  >
                    <Input placeholder="发件人邮箱" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="email_from_name"
                    label="发件人名称"
                    rules={[{ required: true, message: '请输入发件人名称' }]}
                  >
                    <Input placeholder="发件人名称" />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item
                name="smtp_use_tls"
                label="使用TLS加密"
                valuePropName="checked"
                initialValue={true}
              >
                <Switch />
              </Form.Item>

              <Form.Item>
                <Space>
                  <Button
                    type="primary"
                    htmlType="submit"
                    icon={<SaveOutlined />}
                    loading={loading}
                  >
                    保存设置
                  </Button>
                  <Button
                    onClick={testEmailSettings}
                    loading={loading}
                  >
                    测试邮件设置
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </Card>
        </TabPane>

        {/* 存储设置 */}
        <TabPane
          tab={<span><CloudOutlined /> 存储设置</span>}
          key="storage"
        >
          <Card loading={loading}>
            <Form
              form={storageForm}
              layout="vertical"
              onFinish={saveStorageSettings}
            >
              <Form.Item
                name="storage_provider"
                label="存储提供商"
                rules={[{ required: true, message: '请选择存储提供商' }]}
                initialValue="local"
              >
                <Select>
                  <Option value="local">本地存储</Option>
                  <Option value="s3">Amazon S3</Option>
                  <Option value="oss">阿里云OSS</Option>
                  <Option value="cos">腾讯云COS</Option>
                </Select>
              </Form.Item>

              <Row gutter={24}>
                <Col span={12}>
                  <Form.Item
                    name="default_user_quota"
                    label="默认用户配额(MB)"
                    rules={[{ required: true, message: '请输入默认用户配额' }]}
                    initialValue={500}
                  >
                    <InputNumber min={1} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="max_file_size"
                    label="最大文件大小(MB)"
                    rules={[{ required: true, message: '请输入最大文件大小' }]}
                    initialValue={50}
                  >
                    <InputNumber min={1} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item
                name="allowed_file_types"
                label="允许的文件类型"
                rules={[{ required: true, message: '请输入允许的文件类型' }]}
                initialValue="jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx,ppt,pptx,txt,zip,rar"
              >
                <TextArea
                  placeholder="允许的文件类型，用逗号分隔，例如：jpg,png,pdf"
                  rows={2}
                />
              </Form.Item>

              <Divider>S3存储设置</Divider>

              <Form.Item
                noStyle
                shouldUpdate={(prevValues, currentValues) => prevValues.storage_provider !== currentValues.storage_provider}
              >
                {({ getFieldValue }) =>
                  getFieldValue('storage_provider') === 's3' ? (
                    <>
                      <Row gutter={24}>
                        <Col span={12}>
                          <Form.Item
                            name="s3_bucket"
                            label="S3存储桶"
                            rules={[{ required: true, message: '请输入S3存储桶' }]}
                          >
                            <Input placeholder="S3存储桶" />
                          </Form.Item>
                        </Col>
                        <Col span={12}>
                          <Form.Item
                            name="s3_region"
                            label="S3区域"
                            rules={[{ required: true, message: '请输入S3区域' }]}
                          >
                            <Input placeholder="S3区域" />
                          </Form.Item>
                        </Col>
                      </Row>

                      <Row gutter={24}>
                        <Col span={12}>
                          <Form.Item
                            name="s3_access_key"
                            label="S3访问密钥"
                            rules={[{ required: true, message: '请输入S3访问密钥' }]}
                          >
                            <Input placeholder="S3访问密钥" />
                          </Form.Item>
                        </Col>
                        <Col span={12}>
                          <Form.Item
                            name="s3_secret_key"
                            label="S3秘密密钥"
                            rules={[{ required: true, message: '请输入S3秘密密钥' }]}
                          >
                            <Input.Password placeholder="S3秘密密钥" />
                          </Form.Item>
                        </Col>
                      </Row>
                    </>
                  ) : null
                }
              </Form.Item>

              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  icon={<SaveOutlined />}
                  loading={loading}
                >
                  保存设置
                </Button>
              </Form.Item>
            </Form>
          </Card>
        </TabPane>

        {/* 安全设置 */}
        <TabPane
          tab={<span><SecurityScanOutlined /> 安全设置</span>}
          key="security"
        >
          <Card loading={loading}>
            <Form
              form={securityForm}
              layout="vertical"
              onFinish={saveSecuritySettings}
            >
              <Row gutter={24}>
                <Col span={12}>
                  <Form.Item
                    name="password_min_length"
                    label="密码最小长度"
                    rules={[{ required: true, message: '请输入密码最小长度' }]}
                    initialValue={8}
                  >
                    <InputNumber min={6} max={32} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="session_timeout"
                    label="会话超时时间(分钟)"
                    rules={[{ required: true, message: '请输入会话超时时间' }]}
                    initialValue={60}
                  >
                    <InputNumber min={5} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={24}>
                <Col span={12}>
                  <Form.Item
                    name="max_login_attempts"
                    label="最大登录尝试次数"
                    rules={[{ required: true, message: '请输入最大登录尝试次数' }]}
                    initialValue={5}
                  >
                    <InputNumber min={1} style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="enable_2fa"
                    label="启用两因素认证"
                    valuePropName="checked"
                    initialValue={false}
                  >
                    <Switch />
                  </Form.Item>
                </Col>
              </Row>

              <Divider>密码复杂度要求</Divider>

              <Row gutter={24}>
                <Col span={6}>
                  <Form.Item
                    name="password_require_uppercase"
                    label="要求大写字母"
                    valuePropName="checked"
                    initialValue={true}
                  >
                    <Switch />
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item
                    name="password_require_lowercase"
                    label="要求小写字母"
                    valuePropName="checked"
                    initialValue={true}
                  >
                    <Switch />
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item
                    name="password_require_number"
                    label="要求数字"
                    valuePropName="checked"
                    initialValue={true}
                  >
                    <Switch />
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item
                    name="password_require_special"
                    label="要求特殊字符"
                    valuePropName="checked"
                    initialValue={false}
                  >
                    <Switch />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  icon={<SaveOutlined />}
                  loading={loading}
                >
                  保存设置
                </Button>
              </Form.Item>
            </Form>
          </Card>
        </TabPane>

        {/* 主题设置 */}
        <TabPane
          tab={<span><BgColorsOutlined /> 主题设置</span>}
          key="theme"
        >
          <Card loading={loading}>
            <Form
              form={themeForm}
              layout="vertical"
              onFinish={saveThemeSettings}
            >
              <Row gutter={24}>
                <Col span={12}>
                  <Form.Item
                    name="primary_color"
                    label="主色调"
                    rules={[{ required: true, message: '请选择主色调' }]}
                    initialValue="#1890ff"
                  >
                    <Input type="color" style={{ width: '100%', height: 40 }} />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="secondary_color"
                    label="次要色调"
                    initialValue="#52c41a"
                  >
                    <Input type="color" style={{ width: '100%', height: 40 }} />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={24}>
                <Col span={12}>
                  <Form.Item
                    name="default_theme"
                    label="默认主题"
                    initialValue="light"
                  >
                    <Select>
                      <Option value="light">浅色主题</Option>
                      <Option value="dark">深色主题</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="enable_theme_customization"
                    label="允许用户自定义主题"
                    valuePropName="checked"
                    initialValue={true}
                  >
                    <Switch />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  icon={<SaveOutlined />}
                  loading={loading}
                >
                  保存设置
                </Button>
              </Form.Item>
            </Form>
          </Card>
        </TabPane>

        {/* 角色命名设置 */}
        <TabPane
          tab={<span><TeamOutlined /> 角色命名</span>}
          key="roles"
        >
          <Card loading={loading}>
            <Form
              form={rolesForm}
              layout="vertical"
              onFinish={saveRolesSettings}
            >
              <Paragraph>
                自定义系统中的角色名称，这些名称将在整个系统中显示，包括界面、邮件和通知等。
                修改后的名称将在下次登录时生效。
              </Paragraph>

              <Divider />

              <Row gutter={24}>
                <Col span={12}>
                  <Form.Item
                    name="service_provider_name"
                    label="服务商名称"
                    rules={[{ required: true, message: '请输入服务商名称' }]}
                    tooltip="自定义服务商的显示名称，例如：合作伙伴、代理商等"
                    initialValue="服务商"
                  >
                    <Input placeholder="服务商名称" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="tenant_name"
                    label="租户名称"
                    rules={[{ required: true, message: '请输入租户名称' }]}
                    tooltip="自定义租户的显示名称，例如：客户、商户等"
                    initialValue="租户"
                  >
                    <Input placeholder="租户名称" />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={24}>
                <Col span={12}>
                  <Form.Item
                    name="admin_name"
                    label="管理员名称"
                    rules={[{ required: true, message: '请输入管理员名称' }]}
                    tooltip="自定义管理员的显示名称"
                    initialValue="管理员"
                  >
                    <Input placeholder="管理员名称" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="user_name"
                    label="用户名称"
                    rules={[{ required: true, message: '请输入用户名称' }]}
                    tooltip="自定义普通用户的显示名称"
                    initialValue="用户"
                  >
                    <Input placeholder="用户名称" />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item>
                <Button
                  type="primary"
                  htmlType="submit"
                  icon={<SaveOutlined />}
                  loading={loading}
                >
                  保存设置
                </Button>
              </Form.Item>
            </Form>
          </Card>
        </TabPane>
      </Tabs>
    </div>
  );
};

export default SystemSettings;
