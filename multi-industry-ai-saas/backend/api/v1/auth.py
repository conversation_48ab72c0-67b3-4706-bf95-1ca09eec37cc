#!/usr/bin/env python
# -*- coding: utf-8 -*-

import logging
from typing import Optional, Dict, Any, List
from fastapi import APIRouter, Depends, HTTPException, status, Body, Request, Query
from fastapi.responses import RedirectResponse
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from pydantic import BaseModel, EmailStr, Field
import uuid
import httpx
from urllib.parse import urlencode
from datetime import datetime, timedelta

from db.database import get_db
from models.user import User, UserActivity
from models.tenant import Tenant
from models.project import Project
from services.system_config import SystemConfigService
from core.auth import create_access_token, authenticate_user, get_current_user
from core.config import settings
from core.tenant_context import get_tenant_context

# 配置日志
logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter()


# 认证模型
class Token(BaseModel):
    access_token: str
    token_type: str
    expires_in: int
    user_id: uuid.UUID
    username: str
    email: str
    role: Optional[str] = None
    is_system_admin: bool = False
    is_super_admin: bool = False
    is_service_provider_admin: bool = False
    is_tenant_admin: bool = False
    tenant_id: Optional[uuid.UUID] = None
    service_provider_id: Optional[uuid.UUID] = None
    # 项目相关字段
    project_role: Optional[str] = None  # 用户在当前项目中的角色代码
    project_role_name: Optional[str] = None  # 用户在当前项目中的角色名称
    is_project_admin: Optional[bool] = None  # 用户是否为项目管理员
    project_id: Optional[uuid.UUID] = None  # 当前项目ID


class UserCreate(BaseModel):
    username: str
    email: EmailStr
    password: str = Field(..., min_length=8)
    full_name: Optional[str] = None
    tenant_id: Optional[uuid.UUID] = None
    service_provider_id: Optional[uuid.UUID] = None
    is_tenant_admin: bool = False


class UserLogin(BaseModel):
    username: str
    password: str
    tenant_id: Optional[uuid.UUID] = None
    service_provider_id: Optional[uuid.UUID] = None


class UserResponse(BaseModel):
    id: uuid.UUID
    username: str
    email: str
    full_name: Optional[str] = None
    role: Optional[str] = None
    status: str
    is_system_admin: bool = False
    is_super_admin: bool = False
    is_service_provider_admin: bool = False
    is_tenant_admin: bool = False
    tenant_id: Optional[uuid.UUID] = None
    service_provider_id: Optional[uuid.UUID] = None
    created_at: datetime
    # 项目相关字段
    project_role: Optional[str] = None  # 用户在当前项目中的角色代码
    project_role_name: Optional[str] = None  # 用户在当前项目中的角色名称
    is_project_admin: Optional[bool] = None  # 用户是否为项目管理员
    project_id: Optional[uuid.UUID] = None  # 当前项目ID

    class Config:
        orm_mode = True


class SystemTokenRequest(BaseModel):
    project_id: uuid.UUID
    server_id: uuid.UUID  # ID of the MCP server instance requesting the token
    scope: Optional[str] = "mcp_server:system_operations"
    permissions_override: Optional[Dict[str, List[str]]] = None
    expires_in_minutes: Optional[int] = None


@router.post("/register", response_model=UserResponse, status_code=status.HTTP_201_CREATED)
async def register_user(
    user_data: UserCreate,
    db: AsyncSession = Depends(get_db),
):
    """
    注册新用户
    """
    try:
        # 检查用户名是否已存在
        result = await db.execute(select(User).where(User.username == user_data.username))
        existing_user = result.scalars().first()

        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="用户名已存在",
            )

        # 检查邮箱是否已存在
        result = await db.execute(select(User).where(User.email == user_data.email))
        existing_email = result.scalars().first()

        if existing_email:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="邮箱已被注册",
            )

        # 如果指定了租户ID，检查租户是否存在
        if user_data.tenant_id:
            result = await db.execute(select(Tenant).where(Tenant.id == user_data.tenant_id))
            tenant = result.scalars().first()

            if not tenant:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="租户不存在",
                )

        # 创建用户
        db_user = User(
            id=uuid.uuid4(),
            username=user_data.username,
            email=user_data.email,
            password_hash=User.get_password_hash(user_data.password),
            full_name=user_data.full_name,
            tenant_id=user_data.tenant_id,
            service_provider_id=user_data.service_provider_id,
            is_tenant_admin=user_data.is_tenant_admin,
            role="user",  # 默认角色
            status="active",
        )

        db.add(db_user)
        await db.commit()
        await db.refresh(db_user)

        logger.info(f"用户注册成功: {db_user.id}")
        return db_user
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"用户注册失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"用户注册失败: {str(e)}",
        )


@router.post("/token", response_model=Token)
async def login_for_access_token(
    form_data: OAuth2PasswordRequestForm = Depends(),
    request: Request = None,
    db: AsyncSession = Depends(get_db),
):
    """
    获取访问令牌
    """
    try:
        # 验证用户
        user = await authenticate_user(db, form_data.username, form_data.password)

        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户名或密码不正确",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # 检查用户状态
        if user.status != "active":
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="用户账户已被禁用",
            )

        # 创建访问令牌
        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)

        token_data = {
            "sub": user.username,
            "user_id": str(user.id),
            "role": user.role,
            "is_system_admin": user.is_system_admin,
            "is_super_admin": user.is_super_admin or user.is_system_admin,  # 兼容两种命名
            "is_service_provider_admin": user.is_service_provider_admin,
            "is_tenant_admin": user.is_tenant_admin,
        }

        # 添加租户和服务商信息
        if user.tenant_id:
            token_data["tenant_id"] = str(user.tenant_id)

        if user.service_provider_id:
            token_data["service_provider_id"] = str(user.service_provider_id)

        access_token = create_access_token(
            data=token_data,
            expires_delta=access_token_expires,
        )

        # 更新最后登录时间
        user.last_login = datetime.utcnow()
        
        # 记录登录活动
        if request:
            # 获取客户端IP地址
            ip_address = request.client.host if request.client else None
            if not ip_address:
                # 尝试从X-Forwarded-For头获取
                ip_address = request.headers.get("X-Forwarded-For", "").split(",")[0].strip()
            if not ip_address:
                # 尝试从X-Real-IP头获取
                ip_address = request.headers.get("X-Real-IP")
                
            # 获取User-Agent
            user_agent = request.headers.get("User-Agent")
            
            login_activity = UserActivity(
                user_id=user.id,
                project_id=None,  # token端点通常没有特定项目上下文
                activity_type="login",
                description="用户登录(token端点)",
                ip_address=ip_address,
                user_agent=user_agent,
                created_at=datetime.utcnow()
            )
            db.add(login_activity)
        
        await db.commit()

        logger.info(f"用户登录成功: {user.username}")

        # 检查用户是否有关联的项目
        project_role = None
        project_role_name = None
        is_project_admin = False
        project_id = None

        # 尝试从token_data中获取项目ID
        if token_data.get("project_id"):
            project_id = token_data.get("project_id")
            from models.project_user import ProjectUser
            from models.role import Role
            import uuid

            # 确保project_id是UUID类型
            try:
                if isinstance(project_id, str):
                    project_id_uuid = uuid.UUID(project_id)
                else:
                    project_id_uuid = project_id

                # 查询用户在项目中的关联信息
                result = await db.execute(
                    select(ProjectUser)
                    .join(Role, ProjectUser.role_id == Role.id)
                    .where(
                        ProjectUser.user_id == user.id,
                        ProjectUser.project_id == project_id_uuid
                    )
                )
                project_user = result.scalars().first()

                # 如果找到项目用户关联，获取角色信息
                if project_user and project_user.role:
                    project_role = project_user.role.code
                    project_role_name = project_user.role.name
                    is_project_admin = project_user.is_admin
                    project_id = str(project_id_uuid)  # 确保返回字符串类型
            except (ValueError, TypeError) as e:
                logger.error(f"转换项目ID为UUID失败: {e}")

        # 返回令牌和用户信息
        return {
            "access_token": access_token,
            "token_type": "bearer",
            "expires_in": settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
            "user_id": user.id,
            "username": user.username,
            "email": user.email,
            "role": user.role,
            "is_system_admin": user.is_system_admin or False,
            "is_super_admin": user.is_super_admin or user.is_system_admin or False,
            "is_service_provider_admin": user.is_service_provider_admin or False,
            "is_tenant_admin": user.is_tenant_admin or False,
            "tenant_id": user.tenant_id,
            "service_provider_id": user.service_provider_id,
            "project_role": project_role,
            "project_role_name": project_role_name,
            "is_project_admin": is_project_admin,
            "project_id": project_id,
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"登录失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"登录失败: {str(e)}",
        )


@router.post("/login", response_model=Token)
async def login(
    login_data: UserLogin,
    request: Request,
    db: AsyncSession = Depends(get_db),
):
    """
    用户登录
    """
    try:
        # 验证用户
        user = await authenticate_user(db, login_data.username, login_data.password)

        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="用户名或密码不正确",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # 检查用户状态
        if user.status != "active":
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="用户账户已被禁用",
            )

        # 创建访问令牌
        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)

        token_data = {
            "sub": user.username,
            "user_id": str(user.id),
            "role": user.role,
            "is_system_admin": user.is_system_admin,
            "is_super_admin": user.is_super_admin or user.is_system_admin,  # 兼容两种命名
            "is_service_provider_admin": user.is_service_provider_admin,
            "is_tenant_admin": user.is_tenant_admin,
        }

        # 添加租户和服务商信息
        if user.tenant_id:
            token_data["tenant_id"] = str(user.tenant_id)

        if user.service_provider_id:
            token_data["service_provider_id"] = str(user.service_provider_id)

        access_token = create_access_token(
            data=token_data,
            expires_delta=access_token_expires,
        )

        # 更新最后登录时间
        user.last_login = datetime.utcnow()
        
        # 初始化项目相关变量
        project_role = None
        project_role_name = None
        is_project_admin = False
        project_id = None
        
        # 记录登录活动
        # 获取客户端IP地址
        ip_address = request.client.host if request.client else None
        if not ip_address:
            # 尝试从X-Forwarded-For头获取
            ip_address = request.headers.get("X-Forwarded-For", "").split(",")[0].strip()
        if not ip_address:
            # 尝试从X-Real-IP头获取
            ip_address = request.headers.get("X-Real-IP")
            
        # 获取User-Agent
        user_agent = request.headers.get("User-Agent")
        
        login_activity = UserActivity(
            user_id=user.id,
            project_id=uuid.UUID(project_id) if project_id else None,
            activity_type="login",
            description="用户登录",
            ip_address=ip_address,
            user_agent=user_agent,
            created_at=datetime.utcnow()
        )
        db.add(login_activity)
        
        await db.commit()

        logger.info(f"用户登录成功: {user.username}")

        # 检查用户是否有关联的项目

        # 尝试从token_data中获取项目ID
        if token_data.get("project_id"):
            project_id = token_data.get("project_id")
            from models.project_user import ProjectUser
            from models.role import Role
            import uuid

            # 确保project_id是UUID类型
            try:
                if isinstance(project_id, str):
                    project_id_uuid = uuid.UUID(project_id)
                else:
                    project_id_uuid = project_id

                # 查询用户在项目中的关联信息
                result = await db.execute(
                    select(ProjectUser)
                    .join(Role, ProjectUser.role_id == Role.id)
                    .where(
                        ProjectUser.user_id == user.id,
                        ProjectUser.project_id == project_id_uuid
                    )
                )
                project_user = result.scalars().first()

                # 如果找到项目用户关联，获取角色信息
                if project_user and project_user.role:
                    project_role = project_user.role.code
                    project_role_name = project_user.role.name
                    is_project_admin = project_user.is_admin
                    project_id = str(project_id_uuid)  # 确保返回字符串类型
            except (ValueError, TypeError) as e:
                logger.error(f"转换项目ID为UUID失败: {e}")

        # 返回令牌和用户信息
        return {
            "access_token": access_token,
            "token_type": "bearer",
            "expires_in": settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
            "user_id": user.id,
            "username": user.username,
            "email": user.email,
            "role": user.role,
            "is_system_admin": user.is_system_admin or False,
            "is_super_admin": user.is_super_admin or user.is_system_admin or False,
            "is_service_provider_admin": user.is_service_provider_admin or False,
            "is_tenant_admin": user.is_tenant_admin or False,
            "tenant_id": user.tenant_id,
            "service_provider_id": user.service_provider_id,
            "project_role": project_role,
            "project_role_name": project_role_name,
            "is_project_admin": is_project_admin,
            "project_id": project_id,
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"登录失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"登录失败: {str(e)}",
        )


@router.get("/me", response_model=UserResponse)
async def get_current_user_info(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
    project_id: Optional[str] = None,
    request: Request = None,
):
    """
    获取当前用户信息

    如果用户有关联的项目，则同时返回用户在该项目中的角色信息
    """
    logger.info(f"获取用户信息，用户ID: {current_user.id}, 请求的项目ID: {project_id}")

    # 如果请求中提供了project_id查询参数，使用它
    if not project_id and request:
        # 尝试从请求头获取
        try:
            project_id_header = request.headers.get("X-Project-ID")
            if project_id_header:
                project_id = project_id_header
                logger.info(f"从请求头获取到项目ID: {project_id}")
        except Exception as e:
            logger.error(f"从请求头获取项目ID失败: {e}")

        # 尝试从cookies获取
        if not project_id:
            try:
                project_id_cookie = request.cookies.get("project_id")
                if project_id_cookie:
                    project_id = project_id_cookie
                    logger.info(f"从cookies获取到项目ID: {project_id}")
            except Exception as e:
                logger.error(f"从cookies获取项目ID失败: {e}")

    # 尝试从上下文获取
    if not project_id:
        try:
            context = get_tenant_context()
            if context and hasattr(context, 'project_id') and context.project_id:
                project_id = context.project_id
                logger.info(f"从上下文中获取到项目ID: {project_id}")
        except Exception as e:
            logger.error(f"从上下文获取项目ID失败: {e}")

    # 如果仍然没有获取到项目ID，尝试从用户对象获取
    if not project_id and hasattr(current_user, 'project_id') and current_user.project_id:
        project_id = current_user.project_id
        logger.info(f"从用户对象中获取到项目ID: {project_id}")

    # 如果仍然没有获取到项目ID，尝试查询用户关联的项目
    if not project_id:
        try:
            # 查询用户关联的项目
            from models.project_user import ProjectUser
            result = await db.execute(
                select(ProjectUser)
                .where(ProjectUser.user_id == current_user.id)
                .limit(1)
            )
            project_user = result.scalars().first()
            if project_user:
                project_id = str(project_user.project_id)
                logger.info(f"从用户关联的项目中获取到项目ID: {project_id}")
        except Exception as e:
            logger.error(f"查询用户关联的项目失败: {e}")

    # 如果有项目ID，获取用户在该项目中的角色
    project_role = None
    if project_id:
        from models.project_user import ProjectUser
        from models.role import Role
        import uuid
        from sqlalchemy import and_

        # 确保project_id是UUID类型
        try:
            if isinstance(project_id, str):
                project_id_uuid = uuid.UUID(project_id)
            else:
                project_id_uuid = project_id

            # 查询用户在项目中的关联信息
            logger.info(f"查询用户 {current_user.id} 在项目 {project_id_uuid} 中的关联信息")

            try:
                # 先查询项目用户关联
                result = await db.execute(
                    select(ProjectUser)
                    .where(
                        ProjectUser.user_id == current_user.id,
                        ProjectUser.project_id == project_id_uuid
                    )
                )
                project_user = result.scalars().first()

                if project_user:
                    logger.info(f"找到项目用户关联: {project_user.id}, 角色ID: {project_user.role_id}, 是否管理员: {project_user.is_admin}")

                    # 设置项目ID和管理员标志
                    current_user.project_id = project_id_uuid
                    current_user.is_project_admin = project_user.is_admin

                    # 如果有角色ID，查询角色信息
                    if project_user.role_id:
                        role_result = await db.execute(
                            select(Role).where(Role.id == project_user.role_id)
                        )
                        role = role_result.scalars().first()

                        if role:
                            logger.info(f"找到角色信息: {role.name} ({role.code})")
                            current_user.project_role = role.code
                            current_user.project_role_name = role.name
                        else:
                            logger.warning(f"未找到角色ID为 {project_user.role_id} 的角色")
                    else:
                        logger.warning(f"项目用户关联 {project_user.id} 没有角色ID")
                else:
                    logger.warning(f"未找到用户 {current_user.id} 在项目 {project_id_uuid} 中的关联信息")
            except Exception as e:
                logger.error(f"查询项目用户关联时发生错误: {e}")

            # 如果是租户管理员，但没有项目角色，设置为项目管理员
            if current_user.is_tenant_admin and not current_user.project_role:
                logger.info(f"用户 {current_user.id} 是租户管理员，但没有项目角色，设置为项目管理员")

                # 检查是否已经存在项目用户关联
                result = await db.execute(
                    select(ProjectUser)
                    .where(
                        and_(
                            ProjectUser.user_id == current_user.id,
                            ProjectUser.project_id == project_id_uuid
                        )
                    )
                )
                project_user = result.scalars().first()

                if not project_user:
                    logger.info(f"为租户管理员 {current_user.id} 创建项目用户关联")

                    # 查找项目管理员角色
                    result = await db.execute(
                        select(Role)
                        .where(
                            and_(
                                Role.project_id == project_id_uuid,
                                Role.code == "project_admin"
                            )
                        )
                    )
                    project_admin_role = result.scalars().first()

                    if not project_admin_role:
                        logger.info(f"创建项目管理员角色")
                        # 创建项目管理员角色
                        from schemas.role import RoleCreate
                        from services.role import RoleService

                        project_admin_role = await RoleService.create_role(
                            db,
                            RoleCreate(
                                project_id=project_id_uuid,
                                name="项目管理员",
                                code="project_admin",
                                description="项目管理员，拥有项目的所有权限",
                                permissions={
                                    "project_view": True,
                                    "project_edit": True,
                                    "project_admin": True,
                                    "user_manage": True,
                                    "role_manage": True,
                                    "store_manage": True,
                                    "product_manage": True,
                                    "inventory_manage": True,
                                    "purchase_manage": True,
                                    "finance_manage": True,
                                    "report_view": True
                                },
                                is_system_role=True
                            )
                        )

                    # 创建项目用户关联
                    project_user = ProjectUser(
                        user_id=current_user.id,
                        project_id=project_id_uuid,
                        role_id=project_admin_role.id,
                        is_admin=True,
                        status="active"
                    )
                    db.add(project_user)
                    await db.commit()
                    logger.info(f"为租户管理员 {current_user.id} 创建项目用户关联成功")

                current_user.project_role = "project_admin"
                current_user.project_role_name = "项目管理员"
                current_user.is_project_admin = True
                current_user.project_id = project_id_uuid
        except (ValueError, TypeError) as e:
            logger.error(f"转换项目ID为UUID失败: {e}")

    return current_user

# SAPI插件令牌转换请求模型
class TokenConversionRequest(BaseModel):
    plugin_token: str
    user_id: uuid.UUID
    tenant_id: Optional[uuid.UUID] = None
    project_id: Optional[uuid.UUID] = None
    server_id: Optional[uuid.UUID] = None  # 添加服务器ID字段
    server_permissions: Optional[Dict[str, List[str]]] = None  # 添加服务器权限映射字段


@router.post("/system-token", response_model=Token)
async def get_system_token(
    request_data: SystemTokenRequest = Body(...),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    为MCP服务器实例颁发一个系统级的JWT令牌。

    此令牌由已认证的用户（通过其自身的JWT）请求，
    旨在供MCP服务器实例用于向后端进行特定操作（例如列出其工具）的认证。
    """
    logger.info(f"System token requested by user {current_user.username} (ID: {current_user.id}) for server_id: {request_data.server_id} in project_id: {request_data.project_id}")

    # 授权检查:
    # 当前实现允许任何已认证的用户为其相关的项目/服务器请求系统令牌。
    # 生成的令牌在其声明中包含原始 user_id 和 project_id。
    # 依赖此系统令牌的资源服务器在授权访问时，应验证这些声明。
    # TODO: 如果需要，未来可以添加更严格的授权逻辑，例如检查用户是否对特定项目拥有管理权限。
    # 例如:
    # project = await get_project_by_id(db, request_data.project_id)
    # if not project or not user_has_permission_on_project(current_user, project):
    #     raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Not authorized for this project")

    # 确定令牌有效期
    if request_data.expires_in_minutes:
        expires_delta = timedelta(minutes=request_data.expires_in_minutes)
        logger.info(f"System token expiry set to {request_data.expires_in_minutes} minutes as per request.")
    else:
        expires_delta = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES) # 使用默认配置
        logger.info(f"System token expiry set to default: {settings.ACCESS_TOKEN_EXPIRE_MINUTES} minutes.")

    # 构建系统令牌的载荷
    token_payload = {
        "sub": f"mcp_server_system_account_user_{current_user.id}_server_{request_data.server_id}",
        "user_id": str(current_user.id),  # 发起此请求的原始用户ID
        "original_username": current_user.username,
        "project_id": str(request_data.project_id),
        "server_id": str(request_data.server_id),  # 此令牌适用的MCP服务器
        "scope": request_data.scope,
        "permissions": request_data.permissions_override or {},  # 嵌入特定权限
        "token_type": "system_mcp_server_token",  # 自定义令牌类型标识符
        "tenant_id": str(current_user.tenant_id) if current_user.tenant_id else None,
        # 系统令牌通常不应默认拥有广泛的管理权限
        "is_system_admin": False,
        "is_super_admin": False,
        "is_service_provider_admin": False,
        "is_tenant_admin": False,
        "role": "mcp_system_component"  # 此令牌的特定角色
    }

    access_token = create_access_token(
        data=token_payload,
        expires_delta=expires_delta
    )

    logger.info(f"System token successfully generated for server_id: {request_data.server_id}, project_id: {request_data.project_id}, scope: {request_data.scope}")

    return {
        "access_token": access_token,
        "token_type": "bearer",  # 用于Authorization头的标准令牌类型
        "expires_in": int(expires_delta.total_seconds()),
        "user_id": current_user.id,  # 反映发起请求的原始用户
        "username": f"system_token_for_{current_user.username}_server_{request_data.server_id}", # 派生的用户名，表明其用途
        "email": current_user.email,  # 原始用户的邮箱
        "role": "mcp_system_component",  # 特定角色
        "is_system_admin": False,  # 对令牌本身明确设置为False
        "is_super_admin": False,
        "is_service_provider_admin": False,
        "is_tenant_admin": False,
        "tenant_id": current_user.tenant_id,
        "service_provider_id": current_user.service_provider_id,  # 如果存在则沿用
    }


@router.post("/convert-token", response_model=Token)
async def convert_plugin_token(
    request: TokenConversionRequest,
    db: AsyncSession = Depends(get_db)
):
    """
    将SAPI插件的JWT令牌转换为系统JWT令牌

    此接口供SAPI插件内部使用，用于获取与主系统兼容的JWT令牌
    """
    try:
        logger.info(f"收到SAPI令牌转换请求，用户ID：{request.user_id}")

        # 查询用户信息
        result = await db.execute(select(User).where(User.id == request.user_id))
        user = result.scalars().first()

        if not user:
            logger.error(f"找不到用户ID为{request.user_id}的用户")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="用户不存在"
            )

        # 查询租户信息（如果提供了租户ID）
        tenant_name = None
        if request.tenant_id:
            result = await db.execute(select(Tenant).where(Tenant.id == request.tenant_id))
            tenant = result.scalars().first()
            if tenant:
                tenant_name = tenant.name

        # 创建访问令牌
        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        token_data = {
            "sub": str(user.id),
            "user_id": str(user.id),  # 新增，确保JWT里有user_id字段
            "email": user.email,
            "tenant_id": str(request.tenant_id) if request.tenant_id else None,
        }

        # 确保项目ID被包含在令牌中
        if request.project_id:
            token_data["project_id"] = str(request.project_id)
            logger.info(f"包含项目ID: {request.project_id}")

        # 添加服务器ID和服务器权限映射
        if request.server_id:
            token_data["server_id"] = str(request.server_id)
            logger.info(f"包含服务器ID: {request.server_id}")

        # 添加服务器权限映射
        if request.server_permissions:
            token_data["server_permissions"] = request.server_permissions
            logger.info(f"包含服务器权限映射: {request.server_permissions}")

        access_token = create_access_token(
            data=token_data,
            expires_delta=access_token_expires
        )

        logger.info(f"成功为用户 {user.email} 创建系统JWT令牌")

        return {
            "access_token": access_token,
            "token_type": "bearer",
            "expires_in": settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
            "user_id": user.id,
            "username": user.username,
            "email": user.email,
            "tenant_id": request.tenant_id,
            "tenant_name": tenant_name
        }

    except Exception as e:
        logger.error(f"令牌转换失败：{str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"令牌转换失败：{str(e)}"
        )

# 第三方登录相关路由

# 钉钉登录相关接口
@router.get("/dingtalk/login")
async def dingtalk_login_url(
    project_id: str = Query(..., description="项目ID"),
    redirect_url: Optional[str] = Query(None, description="登录成功后的重定向URL"),
    db: AsyncSession = Depends(get_db)
):
    """
    获取钉钉登录授权URL
    参考钉钉文档：https://open.dingtalk.com/document/orgapp/sso-overview
    """
    try:
        # 获取项目信息
        result = await db.execute(select(Project).where(Project.id == project_id))
        project = result.scalars().first()
        if not project:
            raise HTTPException(status_code=404, detail="项目不存在")
        
        # 获取钉钉配置
        dingtalk_configs = await SystemConfigService.get_configs_by_type(db, project.id, "third_party_login")
        config_dict = {config.config_key: config.config_value for config in dingtalk_configs}
        dingtalk_config = config_dict.get("dingtalk", {})
        
        if not dingtalk_config.get("enabled", False):
            raise HTTPException(status_code=400, detail="钉钉登录未启用")
        
        app_key = dingtalk_config.get("app_key")
        redirect_uri = dingtalk_config.get("redirect_uri")
        
        if not app_key or not redirect_uri:
            raise HTTPException(status_code=400, detail="钉钉配置不完整")
        
        # 生成state参数用于防止CSRF攻击
        state = str(uuid.uuid4())
        
        # 构建钉钉授权URL（根据官方文档添加必需的prompt参数）
        auth_params = {
            "response_type": "code", 
            "scope": "openid",
            "client_id": app_key,
            "redirect_uri": redirect_uri,
            "state": f"{state}:{project_id}:{redirect_url or ''}",
            "prompt": "consent"  # 必需参数，值为consent时会进入授权确认页
        }
        
        auth_url = f"https://login.dingtalk.com/oauth2/auth?{urlencode(auth_params)}"
        
        return {
            "auth_url": auth_url,
            "state": state
        }
        
    except Exception as e:
        logger.error(f"获取钉钉登录URL失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取钉钉登录URL失败: {str(e)}")

@router.get("/dingtalk/callback")
async def dingtalk_callback(
    code: str = Query(..., description="钉钉返回的授权码"),
    state: str = Query(..., description="状态参数"),
    db: AsyncSession = Depends(get_db)
):
    """
    钉钉登录回调处理
    """
    try:
        # 解析state参数 - 修复URL中包含冒号的问题
        state_parts = state.split(":", 2)  # 只分割前两个冒号
        if len(state_parts) < 2:
            raise HTTPException(status_code=400, detail="无效的state参数")
        
        state_uuid, project_id = state_parts[0], state_parts[1]
        remaining_parts = state_parts[2] if len(state_parts) > 2 else ""
        
        # 检查是否为绑定请求 - 绑定请求格式: uuid:project_id:user_id:bind
        is_bind_request = False
        if remaining_parts:
            # 继续解析剩余部分
            remaining_split = remaining_parts.split(":", 1)
            if len(remaining_split) == 2 and remaining_split[1] == "bind":
                # 是绑定请求
                is_bind_request = True
                user_id = remaining_split[0]
                redirect_url = None
            else:
                # 是登录请求，剩余部分就是redirect_url
                redirect_url = remaining_parts
                user_id = None
        else:
            redirect_url = None
            user_id = None
            
        # 添加调试日志
        logger.info(f"钉钉登录回调解析state: original={state}")
        logger.info(f"解析结果: is_bind_request={is_bind_request}, redirect_url={redirect_url}")
        
        # 获取项目信息
        result = await db.execute(select(Project).where(Project.id == project_id))
        project = result.scalars().first()
        if not project:
            raise HTTPException(status_code=404, detail="项目不存在")
        
        # 获取钉钉配置
        dingtalk_configs = await SystemConfigService.get_configs_by_type(db, project.id, "third_party_login")
        config_dict = {config.config_key: config.config_value for config in dingtalk_configs}
        dingtalk_config = config_dict.get("dingtalk", {})
        
        app_key = dingtalk_config.get("app_key")
        app_secret = dingtalk_config.get("app_secret")
        redirect_uri = dingtalk_config.get("redirect_uri")
        
        if not all([app_key, app_secret, redirect_uri]):
            raise HTTPException(status_code=400, detail="钉钉配置不完整")
        
        # 1. 使用授权码获取access_token
        logger.info(f"钉钉登录回调 - 项目ID: {project_id}, code: {code[:10]}..., app_key: {app_key[:10] if app_key else 'None'}...")
        
        token_data = {
            "clientId": app_key,
            "clientSecret": app_secret,
            "code": code,
            "grantType": "authorization_code"
        }
        
        async with httpx.AsyncClient() as client:
            # 获取access_token
            token_response = await client.post(
                "https://api.dingtalk.com/v1.0/oauth2/userAccessToken",
                json=token_data,
                headers={"Content-Type": "application/json"}
            )
            
            if token_response.status_code != 200:
                logger.error(f"获取钉钉access_token失败: {token_response.text}")
                raise HTTPException(status_code=400, detail="获取钉钉access_token失败")
            
            token_info = token_response.json()
            access_token = token_info.get("accessToken")
            
            if not access_token:
                raise HTTPException(status_code=400, detail="未获取到access_token")
            
            # 2. 使用access_token获取用户信息
            user_response = await client.get(
                "https://api.dingtalk.com/v1.0/contact/users/me",
                headers={
                    "x-acs-dingtalk-access-token": access_token,
                    "Content-Type": "application/json"
                }
            )
            
            if user_response.status_code != 200:
                logger.error(f"获取钉钉用户信息失败: {user_response.text}")
                raise HTTPException(status_code=400, detail="获取钉钉用户信息失败")
            
            user_info = user_response.json()
        
        # 3. 处理钉钉用户信息
        dingtalk_union_id = user_info.get("unionId")
        dingtalk_open_id = user_info.get("openId")  # 修正：这是openId，不是企业内userid
        user_name = user_info.get("nick", "")
        user_email = user_info.get("email", "")
        user_phone = user_info.get("mobile", "")  # 钉钉返回的手机号字段
        
        if not dingtalk_union_id:
            raise HTTPException(status_code=400, detail="未获取到钉钉用户唯一标识")
        
        logger.info(f"钉钉登录用户信息: unionId={dingtalk_union_id}, openId={dingtalk_open_id}, name={user_name}")
        
        # 根据请求类型处理不同逻辑
        if is_bind_request:
            # 处理绑定请求
            from fastapi.responses import HTMLResponse
            from models.user import ThirdPartyAccount
            from models.user import UserActivity
            
            # 获取要绑定的用户
            try:
                result = await db.execute(select(User).where(User.id == uuid.UUID(user_id)))
                target_user = result.scalars().first()
                if not target_user:
                    raise HTTPException(status_code=404, detail="用户不存在")
                
                # 检查是否已存在绑定记录
                result = await db.execute(select(ThirdPartyAccount).where(
                    ThirdPartyAccount.user_id == target_user.id,
                    ThirdPartyAccount.platform == "dingtalk"
                ))
                existing_account = result.scalars().first()
                
                # 修正：区分unionId和openId的存储
                # platform_user_id应该存储unionId（全局唯一标识）
                platform_user_id = dingtalk_union_id
                platform_username = user_name or f"钉钉用户"
                avatar_url = user_info.get("avatarUrl") or user_info.get("avatar")
                
                # 检查是否已有其他用户绑定了这个钉钉账号
                result = await db.execute(select(User).where(User.dingtalk_union_id == dingtalk_union_id))
                existing_user_with_dingtalk = result.scalars().first()
                if existing_user_with_dingtalk and existing_user_with_dingtalk.id != target_user.id:
                    raise HTTPException(status_code=400, detail="该钉钉账号已被其他用户绑定")
                
                # 同时更新User表和ThirdPartyAccount表
                target_user.dingtalk_union_id = dingtalk_union_id
                # 注意：这里暂时使用openId，实际的企业内userid需要通过企业API获取
                target_user.dingtalk_user_id = dingtalk_open_id  
                target_user.updated_at = datetime.utcnow()
                
                logger.info(f"绑定钉钉账号: user_id={target_user.id}, unionId={dingtalk_union_id}, openId={dingtalk_open_id}")
                
                if existing_account:
                    # 更新现有绑定
                    existing_account.platform_user_id = platform_user_id
                    existing_account.platform_username = platform_username
                    existing_account.avatar_url = avatar_url
                    existing_account.updated_at = datetime.utcnow()
                else:
                    # 创建新的绑定记录
                    new_account = ThirdPartyAccount(
                        id=uuid.uuid4(),
                        user_id=target_user.id,
                        platform="dingtalk",
                        platform_user_id=platform_user_id,
                        platform_username=platform_username,
                        avatar_url=avatar_url,
                        created_at=datetime.utcnow(),
                        updated_at=datetime.utcnow()
                    )
                    db.add(new_account)
                
                # 添加活动记录
                activity = UserActivity(
                    user_id=target_user.id,
                    project_id=uuid.UUID(project_id),
                    activity_type="third_party_bind",
                    description="绑定了钉钉账号",
                    created_at=datetime.utcnow()
                )
                db.add(activity)
                await db.commit()
                
                # 返回绑定成功的HTML页面
                success_html = f"""
                <!DOCTYPE html>
                <html>
                <head>
                    <title>绑定成功</title>
                    <meta charset="utf-8">
                </head>
                <body>
                    <script>
                        // 通知父页面绑定成功
                        if (window.opener) {{
                            window.opener.postMessage({{
                                type: 'BIND_SUCCESS',
                                platform: 'dingtalk',
                                data: {{
                                    platform_user_id: '{platform_user_id}',
                                    platform_username: '{platform_username}',
                                    avatar_url: '{avatar_url or ""}'
                                }}
                            }}, '*');
                            window.close();
                        }} else {{
                            // 如果不是弹窗，则重定向到用户资料页面
                            window.location.href = '/user/profile?bind_success=true&platform=dingtalk';
                        }}
                    </script>
                    <div style="text-align: center; padding: 50px; font-family: Arial, sans-serif;">
                        <h2>✅ 钉钉账号绑定成功！</h2>
                        <p>正在关闭窗口...</p>
                        <p>如果窗口没有自动关闭，请手动关闭此页面。</p>
                    </div>
                </body>
                </html>
                """
                return HTMLResponse(content=success_html, status_code=200)
                
            except Exception as e:
                logger.error(f"绑定处理失败: {str(e)}")
                error_html = f"""
                <!DOCTYPE html>
                <html>
                <head>
                    <title>绑定失败</title>
                    <meta charset="utf-8">
                </head>
                <body>
                    <script>
                        if (window.opener) {{
                            window.opener.postMessage({{
                                type: 'BIND_ERROR',
                                error: 'bind_failed',
                                message: '绑定处理失败: {str(e)}'
                            }}, '*');
                            window.close();
                        }} else {{
                            window.location.href = '/user/profile?bind_error=bind_failed';
                        }}
                    </script>
                    <div style="text-align: center; padding: 50px; font-family: Arial, sans-serif;">
                        <h2>❌ 绑定失败</h2>
                        <p>绑定处理失败，请重试。</p>
                        <p style="color: #666; font-size: 12px;">错误详情: {str(e)}</p>
                    </div>
                </body>
                </html>
                """
                return HTMLResponse(content=error_html, status_code=500)
        else:
            # 处理登录请求
            # 首先通过User表的dingtalk_union_id查找
            result = await db.execute(
                select(User).where(User.dingtalk_union_id == dingtalk_union_id)
            )
            user = result.scalars().first()
            
            # 如果没找到，尝试通过ThirdPartyAccount表查找
            if not user:
                from models.user import ThirdPartyAccount
                result = await db.execute(
                    select(User).join(ThirdPartyAccount).where(
                        ThirdPartyAccount.platform == "dingtalk",
                        ThirdPartyAccount.platform_user_id == dingtalk_union_id
                    )
                )
                user = result.scalars().first()
                
                # 如果通过ThirdPartyAccount找到了用户，同步更新User表的dingtalk字段
                if user:
                    user.dingtalk_union_id = dingtalk_union_id
                    user.dingtalk_user_id = dingtalk_open_id  # 修正：使用openId
                    await db.commit()
                    await db.refresh(user)
                    logger.info(f"已同步钉钉用户信息到User表: user_id={user.id}, dingtalk_union_id={dingtalk_union_id}, openId={dingtalk_open_id}")
            
            if not user:
                # 不再自动创建用户，而是引导用户联系管理员
                error_message = "该钉钉账号未绑定任何系统用户。请联系管理员创建账号或使用绑定功能关联现有账号。"
                # URL编码错误消息
                from urllib.parse import quote
                encoded_error = quote(error_message)
                
                if redirect_url and redirect_url.startswith(('http://', 'https://')):
                    # 确保redirect_url是完整的URL
                    separator = '&' if '?' in redirect_url else '?'
                    error_redirect = f"{redirect_url}{separator}login_error={encoded_error}"
                else:
                    # 使用前端URL
                    error_redirect = f"http://localhost:3001/login?login_error={encoded_error}"
                
                logger.info(f"用户未找到，重定向到: {error_redirect}")
                return RedirectResponse(url=error_redirect)
            
            # 4. 生成JWT令牌
            access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
            token_data = {
                "sub": user.username,
                "user_id": str(user.id),
                "email": user.email,
                "tenant_id": str(user.tenant_id) if user.tenant_id else None,
                "project_id": str(project.id)
            }
            
            access_token = create_access_token(
                data=token_data,
                expires_delta=access_token_expires
            )
            
            # 5. 重定向到前端，携带token
            logger.info(f"准备重定向 - redirect_url: {redirect_url}")
            logger.info(f"准备重定向 - settings.FRONTEND_URL: {getattr(settings, 'FRONTEND_URL', 'NONE')}")
            
            # 如果redirect_url是登录页，则重定向到项目首页避免闪烁
            if redirect_url and '/login' in redirect_url:
                redirect_to = f"{settings.FRONTEND_URL}/project/dashboard?token={access_token}"
                logger.info("检测到redirect_url是登录页，改为重定向到项目首页")
            elif redirect_url:
                redirect_to = f"{redirect_url}?token={access_token}"
            else:
                # 默认重定向到项目首页
                redirect_to = f"{settings.FRONTEND_URL}/project/dashboard?token={access_token}"
            
            # 添加调试日志
            logger.info(f"钉钉登录重定向到: {redirect_to}")
            logger.info(f"生成的token: {access_token[:20]}...")
            
            return RedirectResponse(url=redirect_to)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"钉钉登录回调处理失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"钉钉登录回调处理失败: {str(e)}")

# 微信登录相关接口
@router.get("/wechat/login")
async def wechat_login_url(
    project_id: str = Query(..., description="项目ID"),
    redirect_url: Optional[str] = Query(None, description="登录成功后的重定向URL"),
    db: AsyncSession = Depends(get_db)
):
    """
    获取微信登录授权URL
    """
    try:
        # 获取项目信息
        result = await db.execute(select(Project).where(Project.id == project_id))
        project = result.scalars().first()
        if not project:
            raise HTTPException(status_code=404, detail="项目不存在")
        
        # 获取微信配置
        wechat_configs = await SystemConfigService.get_configs_by_type(db, project.id, "third_party_login")
        config_dict = {config.config_key: config.config_value for config in wechat_configs}
        wechat_config = config_dict.get("wechat", {})
        
        if not wechat_config.get("enabled", False):
            raise HTTPException(status_code=400, detail="微信登录未启用")
        
        app_id = wechat_config.get("app_id")
        redirect_uri = wechat_config.get("redirect_uri")
        
        if not app_id or not redirect_uri:
            raise HTTPException(status_code=400, detail="微信配置不完整")
        
        # 生成state参数
        state = str(uuid.uuid4())
        
        # 构建微信授权URL
        auth_params = {
            "appid": app_id,
            "redirect_uri": redirect_uri,
            "response_type": "code",
            "scope": "snsapi_login",
            "state": f"{state}:{project_id}:{redirect_url or ''}"
        }
        
        auth_url = f"https://open.weixin.qq.com/connect/qrconnect?{urlencode(auth_params)}#wechat_redirect"
        
        return {
            "auth_url": auth_url,
            "state": state
        }
        
    except Exception as e:
        logger.error(f"获取微信登录URL失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取微信登录URL失败: {str(e)}")

@router.get("/wechat/callback")
async def wechat_callback(
    code: str = Query(..., description="微信返回的授权码"),
    state: str = Query(..., description="状态参数"),
    db: AsyncSession = Depends(get_db)
):
    """
    微信登录回调处理
    """
    try:
        # 解析state参数
        state_parts = state.split(":", 2)
        if len(state_parts) < 2:
            raise HTTPException(status_code=400, detail="无效的state参数")
        
        state_uuid, project_id = state_parts[0], state_parts[1]
        redirect_url = state_parts[2] if len(state_parts) > 2 else None
        
        # 获取项目信息
        result = await db.execute(select(Project).where(Project.id == project_id))
        project = result.scalars().first()
        if not project:
            raise HTTPException(status_code=404, detail="项目不存在")
        
        # 获取微信配置
        wechat_configs = await SystemConfigService.get_configs_by_type(db, project.id, "third_party_login")
        config_dict = {config.config_key: config.config_value for config in wechat_configs}
        wechat_config = config_dict.get("wechat", {})
        
        if not wechat_config.get("enabled", False):
            raise HTTPException(status_code=400, detail="微信登录未启用")
        
        app_id = wechat_config.get("app_id")
        app_secret = wechat_config.get("app_secret")
        
        if not all([app_id, app_secret]):
            raise HTTPException(status_code=400, detail="微信配置不完整")
        
        async with httpx.AsyncClient() as client:
            # 1. 使用授权码获取access_token
            token_params = {
                "appid": app_id,
                "secret": app_secret,
                "code": code,
                "grant_type": "authorization_code"
            }
            
            token_response = await client.get(
                "https://api.weixin.qq.com/sns/oauth2/access_token",
                params=token_params
            )
            
            if token_response.status_code != 200:
                logger.error(f"获取微信access_token失败: {token_response.text}")
                raise HTTPException(status_code=400, detail="获取微信access_token失败")
            
            token_info = token_response.json()
            
            if "access_token" not in token_info:
                logger.error(f"微信返回错误: {token_info}")
                raise HTTPException(status_code=400, detail=f"微信授权失败: {token_info.get('errmsg', '未知错误')}")
            
            access_token = token_info["access_token"]
            openid = token_info["openid"]
            
            # 2. 获取用户信息
            user_params = {
                "access_token": access_token,
                "openid": openid
            }
            
            user_response = await client.get(
                "https://api.weixin.qq.com/sns/userinfo",
                params=user_params
            )
            
            if user_response.status_code != 200:
                logger.error(f"获取微信用户信息失败: {user_response.text}")
                raise HTTPException(status_code=400, detail="获取微信用户信息失败")
            
            user_info = user_response.json()
            
            if "errcode" in user_info:
                logger.error(f"微信用户信息错误: {user_info}")
                raise HTTPException(status_code=400, detail=f"获取微信用户信息失败: {user_info.get('errmsg', '未知错误')}")
        
        # 3. 根据微信用户信息查找或创建本地用户
        wechat_openid = user_info.get("openid")
        wechat_unionid = user_info.get("unionid")
        nickname = user_info.get("nickname", "")
        
        if not wechat_openid:
            raise HTTPException(status_code=400, detail="未获取到微信用户标识")
        
        # 优先通过unionid查找用户，其次通过openid
        user = None
        if wechat_unionid:
            result = await db.execute(
                select(User).where(User.wechat_unionid == wechat_unionid)
            )
            user = result.scalars().first()
        
        if not user:
            result = await db.execute(
                select(User).where(User.wechat_openid == wechat_openid)
            )
            user = result.scalars().first()
        
        if not user:
            # 微信通常不提供邮箱和手机号，所以无法通过这些信息自动绑定现有用户
            # 不再自动创建用户，而是引导用户联系管理员或使用绑定功能
            error_message = "该微信账号未绑定任何系统用户。请联系管理员创建账号或使用个人中心的账号绑定功能关联现有账号。"
            if redirect_url:
                error_redirect = f"{redirect_url}?login_error={error_message}"
            else:
                error_redirect = f"{settings.FRONTEND_URL}/login?login_error={error_message}"
            return RedirectResponse(url=error_redirect)
        
        # 4. 记录登录活动
        login_activity = UserActivity(
            user_id=user.id,
            project_id=project.id,
            activity_type="third_party_login",
            description="微信登录",
            created_at=datetime.utcnow()
        )
        db.add(login_activity)
        
        # 更新最后登录时间
        user.last_login = datetime.utcnow()
        await db.commit()
        
        # 5. 生成JWT令牌
        access_token_expires = timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)
        token_data = {
            "sub": user.username,
            "user_id": str(user.id),
            "email": user.email,
            "tenant_id": str(user.tenant_id) if user.tenant_id else None,
            "project_id": str(project.id)
        }
        
        access_token = create_access_token(
            data=token_data,
            expires_delta=access_token_expires
        )
        
        # 6. 重定向到前端
        logger.info(f"准备重定向 - redirect_url: {redirect_url}")
        logger.info(f"准备重定向 - settings.FRONTEND_URL: {getattr(settings, 'FRONTEND_URL', 'NONE')}")
        
        # 如果redirect_url是登录页，则重定向到项目首页避免闪烁
        if redirect_url and '/login' in redirect_url:
            redirect_to = f"{settings.FRONTEND_URL}/project/dashboard?token={access_token}"
            logger.info("检测到redirect_url是登录页，改为重定向到项目首页")
        elif redirect_url:
            redirect_to = f"{redirect_url}?token={access_token}"
        else:
            # 默认重定向到项目首页
            redirect_to = f"{settings.FRONTEND_URL}/project/dashboard?token={access_token}"
        
        # 添加调试日志
        logger.info(f"微信登录重定向到: {redirect_to}")
        logger.info(f"生成的token: {access_token[:20]}...")
        
        return RedirectResponse(url=redirect_to)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"微信登录回调处理失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"微信登录回调处理失败: {str(e)}")

@router.get("/user/projects", response_model=Any)
async def get_user_projects(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """获取用户关联的所有项目"""
    try:
        from models.project import Project
        from models.project_user import ProjectUser
        from models.role import Role
        from sqlalchemy import and_

        # 查询用户关联的所有项目
        result = await db.execute(
            select(Project)
            .join(ProjectUser, Project.id == ProjectUser.project_id)
            .where(ProjectUser.user_id == current_user.id)
            .order_by(Project.name)
        )
        projects = result.scalars().all()

        # 获取每个项目中用户的角色
        project_list = []
        for project in projects:
            # 查询用户在项目中的角色
            result = await db.execute(
                select(ProjectUser)
                .join(Role, ProjectUser.role_id == Role.id, isouter=True)
                .where(
                    and_(
                        ProjectUser.user_id == current_user.id,
                        ProjectUser.project_id == project.id
                    )
                )
            )
            project_user = result.scalars().first()

            # 获取角色信息
            role = None
            role_name = None
            is_admin = False

            if project_user:
                is_admin = project_user.is_admin

                if project_user.role_id:
                    result = await db.execute(
                        select(Role).where(Role.id == project_user.role_id)
                    )
                    role_obj = result.scalars().first()
                    if role_obj:
                        role = role_obj.code
                        role_name = role_obj.name

            # 构建项目信息
            project_info = {
                "id": str(project.id),
                "name": project.name,
                "code": project.code,
                "description": project.description,
                "industry_type": project.industry_type,
                "status": project.status,
                "created_at": project.created_at,
                "role": role,
                "role_name": role_name,
                "is_admin": is_admin
            }

            project_list.append(project_info)

        return {"success": True, "data": project_list}
    except Exception as e:
        logger.error(f"获取用户项目列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取用户项目列表失败: {str(e)}"
        )
