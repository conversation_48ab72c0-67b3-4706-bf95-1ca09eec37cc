import React, { useState } from 'react';
import { Card, Tabs, Typography } from 'antd';
import {
  ExceptionOutlined,
  ScheduleOutlined,
  DollarOutlined,
  ShopOutlined,
  AuditOutlined
} from '@ant-design/icons';
import FinanceLossApproval from './FinanceLossApproval';
import FinanceStoreTasks from './FinanceStoreTasks';
import FinanceExpenseApproval from './FinanceExpenseApproval';
import FinanceArrivalApproval from './FinanceArrivalApproval';
import InventoryAudit from './InventoryAudit';
import { useNavigate, useLocation } from 'react-router-dom';

const { Title } = Typography;

/**
 * 业务中心组件
 */
const BusinessCenter = () => {
  const navigate = useNavigate();
  const location = useLocation();

  // 根据当前路径确定激活的标签页
  const getActiveKey = () => {
    const path = location.pathname;
    if (path.includes('/store-tasks')) return '2';
    if (path.includes('/expense-approval')) return '3';
    if (path.includes('/arrival-approval')) return '4';
    if (path.includes('/inventory-audit')) return '5';
    return '1'; // 默认为报损审核
  };

  // 处理标签页切换
  const handleTabChange = (key) => {
    switch (key) {
      case '1':
        navigate('/project/finance/business-center/loss-approval');
        break;
      case '2':
        navigate('/project/finance/business-center/store-tasks');
        break;
      case '3':
        navigate('/project/finance/business-center/expense-approval');
        break;
      case '4':
        navigate('/project/finance/business-center/arrival-approval');
        break;
      case '5':
        navigate('/project/finance/business-center/inventory-audit');
        break;
      default:
        navigate('/project/finance/business-center/loss-approval');
    }
  };

  return (
    <div className="business-center-container">
      <Card>
        <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between' }}>
          <Title level={4} style={{ margin: 0 }}>业务中心</Title>
        </div>

        <Tabs
          activeKey={getActiveKey()}
          onChange={handleTabChange}
          items={[
            {
              key: "1",
              label: (
                <span>
                  <ExceptionOutlined />
                  报损审核
                </span>
              ),
              children: <FinanceLossApproval />
            },
            {
              key: "2",
              label: (
                <span>
                  <ScheduleOutlined />
                  门店任务
                </span>
              ),
              children: <FinanceStoreTasks />
            },
            {
              key: "3",
              label: (
                <span>
                  <DollarOutlined />
                  费用审核
                </span>
              ),
              children: <FinanceExpenseApproval />
            },
            {
              key: "4",
              label: (
                <span>
                  <ShopOutlined />
                  到货审核
                </span>
              ),
              children: <FinanceArrivalApproval />
            },
            {
              key: "5",
              label: (
                <span>
                  <AuditOutlined />
                  盘点审核
                </span>
              ),
              children: <InventoryAudit />
            }
          ]}
        />
      </Card>
    </div>
  );
};

export default BusinessCenter;
