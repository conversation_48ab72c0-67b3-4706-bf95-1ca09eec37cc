#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
AI 智能体客服公共组件 API

提供公共访问的客服组件，支持嵌入到任何网站。
"""

import logging
import uuid
import json
from typing import Dict, Any, Optional
from datetime import datetime
from fastapi import APIRouter, Request, HTTPException, status, Query, Path
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.templating import Jinja2Templates
from pydantic import BaseModel

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter()

# 模板引擎
templates = Jinja2Templates(directory="plugins/ai_customer_service/templates")

# 请求模型
class PublicChatRequest(BaseModel):
    message: str
    session_id: Optional[str] = None
    user_info: Optional[Dict[str, Any]] = None
    widget_config: Optional[Dict[str, Any]] = None

class WidgetConfigRequest(BaseModel):
    title: str = "AI智能客服"
    description: str = "我是您的专属智能客服，随时为您提供帮助"
    assistant_prompt: str = "你是一个专业、友好、耐心的智能客服助手。"
    theme_color: str = "#1890ff"
    position: str = "bottom-right"  # bottom-right, bottom-left, top-right, top-left
    size: str = "medium"  # small, medium, large
    auto_open: bool = False
    show_avatar: bool = True
    show_typing_indicator: bool = True
    enable_file_upload: bool = False
    enable_voice_input: bool = False
    auto_greeting: bool = True
    greeting_message: str = "您好！我是AI智能客服，有什么可以帮助您的吗？"
    custom_css: Optional[str] = None

@router.get(
    "/widget/{widget_id}",
    response_class=HTMLResponse,
    summary="获取客服组件页面",
    description="获取可嵌入网站的客服组件页面"
)
async def get_widget_page(
    request: Request,
    widget_id: str = Path(..., description="组件ID"),
    theme: str = Query("light", description="主题 (light/dark)"),
    size: str = Query("medium", description="尺寸 (small/medium/large)"),
    position: str = Query("bottom-right", description="位置"),
    auto_open: bool = Query(False, description="是否自动打开")
):
    """获取客服组件页面"""
    try:
        # 这里需要根据widget_id获取配置
        # 暂时使用默认配置
        
        widget_config = {
            "widget_id": widget_id,
            "title": "AI智能客服",
            "welcome_message": "您好！我是AI智能客服，有什么可以帮助您的吗？",
            "theme": theme,
            "theme_color": "#1890ff",
            "position": position,
            "size": size,
            "auto_open": auto_open,
            "show_avatar": True,
            "enable_file_upload": False,
            "enable_voice_input": False,
            "api_base_url": str(request.base_url).rstrip("/"),
            "websocket_url": str(request.base_url).replace("http", "ws").rstrip("/")
        }
        
        return templates.TemplateResponse(
            "widget.html",
            {
                "request": request,
                "widget_config": widget_config,
                "config_json": json.dumps(widget_config)
            }
        )
        
    except Exception as e:
        logger.error(f"获取组件页面失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取组件页面失败"
        )

@router.get(
    "/embed/{widget_id}",
    response_class=HTMLResponse,
    summary="获取嵌入代码",
    description="获取可嵌入网站的JavaScript代码"
)
async def get_embed_code(
    request: Request,
    widget_id: str = Path(..., description="组件ID"),
    theme: str = Query("light", description="主题"),
    size: str = Query("medium", description="尺寸"),
    position: str = Query("bottom-right", description="位置")
):
    """获取嵌入代码"""
    try:
        base_url = str(request.base_url).rstrip("/")
        widget_url = f"{base_url}/api/v1/public/ai-customer-service/widget/{widget_id}"
        
        embed_code = f"""
<!-- AI智能客服组件 -->
<script>
(function() {{
    var config = {{
        widgetId: '{widget_id}',
        theme: '{theme}',
        size: '{size}',
        position: '{position}',
        apiBaseUrl: '{base_url}'
    }};
    
    var script = document.createElement('script');
    script.src = '{base_url}/static/ai-customer-service/widget.js';
    script.onload = function() {{
        if (window.AICustomerService) {{
            window.AICustomerService.init(config);
        }}
    }};
    document.head.appendChild(script);
    
    var link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = '{base_url}/static/ai-customer-service/widget.css';
    document.head.appendChild(link);
}})();
</script>
"""
        
        return HTMLResponse(content=embed_code, media_type="text/html")
        
    except Exception as e:
        logger.error(f"获取嵌入代码失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取嵌入代码失败"
        )

@router.post(
    "/chat/{widget_id}",
    summary="公共聊天接口",
    description="处理公共客服组件的聊天请求"
)
async def public_chat(
    widget_id: str = Path(..., description="组件ID"),
    chat_request: PublicChatRequest = ...
):
    """处理公共聊天请求"""
    try:
        from sqlalchemy.ext.asyncio import AsyncSession
        from db.database import AsyncSessionLocal
        from ..core.ai_engine import AICustomerServiceEngine
        from ..core.platform_adapter import PlatformMessage
        from ..services.customer_service import CustomerServiceManager
        import uuid as uuid_lib
        
        # 生成或获取会话ID
        session_id = chat_request.session_id or f"public_{uuid.uuid4().hex[:12]}"
        
        async with AsyncSessionLocal() as db:
            try:
                # 获取widget配置
                from ..models.public_widget import PublicWidgetConfig
                from sqlalchemy import select
                
                result = await db.execute(
                    select(PublicWidgetConfig).where(PublicWidgetConfig.widget_id == widget_id)
                )
                widget_config = result.scalars().first()
                
                # 获取AI助理描述，用于定制化回复
                assistant_prompt = None
                if widget_config and widget_config.assistant_prompt:
                    assistant_prompt = widget_config.assistant_prompt
                
                # 初始化AI引擎
                ai_engine = AICustomerServiceEngine()
                await ai_engine.initialize()
                
                # 创建平台消息对象
                platform_message = PlatformMessage(
                    platform_type="web_widget",
                    platform_user_id=f"widget_user_{session_id}",
                    user_name=chat_request.user_info.get("name", "访客") if chat_request.user_info else "访客",
                    message_type="text",
                    content=chat_request.message,
                    attachments=[],
                    timestamp=datetime.utcnow().timestamp(),
                    session_id=session_id,
                    meta_data={
                        "widget_id": widget_id,
                        "user_info": chat_request.user_info or {},
                        "widget_config": chat_request.widget_config or {},
                        "assistant_prompt": assistant_prompt
                    }
                )
                
                # 为公共API创建默认项目上下文，确保有足够权限
                project_id = uuid_lib.UUID("*************-48ab-8092-e8eb7f663677")  # 使用测试项目ID
                user_id = uuid_lib.UUID("04a15201-2024-4d45-b434-a0bb4ff40c34")     # 使用测试用户ID
                
                # 构建增强的用户上下文，确保AI引擎有足够权限
                enhanced_user_context = {
                    "user_id": str(user_id),
                    "project_id": str(project_id),
                    "tenant_id": str(project_id),
                    "session_id": session_id,
                    "platform": "web_widget",
                    "widget_id": widget_id,
                    "is_public_api": True,
                    "permissions": [
                        "read_basic_data",
                        "access_mcp_tools", 
                        "use_ai_models",
                        "read_knowledge_base"
                    ],
                    "user_type": "public_user",
                    "access_level": "basic"
                }
                
                # 处理消息并生成AI回复，传入增强上下文
                response, metadata = await ai_engine.process_message(
                    db=db,
                    message=platform_message,
                    session_id=session_id,
                    project_id=project_id,
                    user_id=user_id,
                    use_knowledge_base=True,
                    knowledge_base_ids=None
                )
                
                # 额外的意图分析和质量评估
                intent_analysis = await ai_engine.analyze_message_intent(
                    message=chat_request.message,
                    context=enhanced_user_context
                )
                
                quality_evaluation = await ai_engine.evaluate_response_quality(
                    user_message=chat_request.message,
                    ai_response=response.content,
                    context=enhanced_user_context
                )
                
                # 构建回复数据
                ai_response = {
                    "message": response.content,
                    "session_id": session_id,
                    "timestamp": datetime.utcnow().isoformat(),
                    "suggestions": [
                        "我想了解产品信息",
                        "我需要技术支持",
                        "我要投诉建议",
                        "查看门店信息"
                    ],
                    "metadata": {
                        "processing_time": metadata.get("processing_time", 0),
                        "ai_model_used": metadata.get("ai_model_used", "AI智能引擎 v2025.1"),
                        "confidence_score": metadata.get("confidence_score", quality_evaluation.get("quality_score", 0.85)),
                        "intent": intent_analysis.get("primary_intent", "general_inquiry"),
                        "intent_confidence": intent_analysis.get("confidence", 0.8),
                        "knowledge_sources": metadata.get("knowledge_sources", []),
                        "quality_scores": quality_evaluation,
                        "detected_entities": intent_analysis.get("entities", {}),
                        "sentiment": intent_analysis.get("sentiment", "neutral"),
                        "mcp_tools_used": metadata.get("mcp_tools_used", []),
                        "response_type": "ai_generated"
                    }
                }
                
                logger.info(f"公共AI客服回复生成成功: widget_id={widget_id}, session_id={session_id}")
                
                return JSONResponse(content={
                    "success": True,
                    "data": ai_response
                })
                
            except Exception as ai_error:
                logger.error(f"AI引擎处理失败: {ai_error}")
                
                # 如果AI引擎失败，提供友好的回退回复
                fallback_response = {
                    "message": "您好！感谢您的咨询。我现在可能遇到一些技术问题，但我会尽力为您提供帮助。请问您需要了解什么信息呢？",
                    "session_id": session_id,
                    "timestamp": datetime.utcnow().isoformat(),
                    "suggestions": [
                        "商品信息查询",
                        "门店位置查询",
                        "联系客服人员",
                        "技术支持"
                    ],
                    "metadata": {
                        "fallback_mode": True,
                        "error": "AI引擎暂时不可用"
                    }
                }
                
                return JSONResponse(content={
                    "success": True,
                    "data": fallback_response
                })
        
    except Exception as e:
        logger.error(f"处理公共聊天失败: {e}")
        import traceback
        traceback.print_exc()
        
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="处理聊天失败"
        )

@router.get(
    "/config/{widget_id}",
    summary="获取组件配置",
    description="获取指定组件的配置信息"
)
async def get_widget_config(
    widget_id: str = Path(..., description="组件ID")
):
    """获取组件配置"""
    try:
        from sqlalchemy.ext.asyncio import AsyncSession
        from db.database import AsyncSessionLocal
        from ..models.public_widget import PublicWidgetConfig
        from sqlalchemy import select
        
        async with AsyncSessionLocal() as db:
            # 从数据库获取配置
            result = await db.execute(
                select(PublicWidgetConfig).where(PublicWidgetConfig.widget_id == widget_id)
            )
            config_obj = result.scalars().first()
            
            if config_obj:
                config = config_obj.to_dict()
            else:
                # 返回默认配置
                config = {
                    "widget_id": widget_id,
                    "title": "AI智能客服",
                    "description": "我是您的专属智能客服，随时为您提供帮助",
                    "assistant_prompt": "你是一个专业、友好、耐心的智能客服助手。你的任务是：\n\n1. 为客户提供准确、有用的信息\n2. 以礼貌和专业的态度回应所有询问\n3. 当无法解决问题时，及时转接人工客服\n4. 始终保持积极正面的语调\n\n请记住：\n- 使用简洁明了的语言\n- 主动询问以更好地理解客户需求\n- 提供具体的解决方案\n- 对客户表示感谢和关心",
                    "theme_color": "#1890ff",
                    "position": "bottom-right",
                    "size": "medium",
                    "auto_open": False,
                    "show_avatar": True,
                    "show_typing_indicator": True,
                    "enable_file_upload": False,
                    "enable_voice_input": False,
                    "auto_greeting": True,
                    "greeting_message": "您好！我是AI智能客服，有什么可以帮助您的吗？",
                    "business_hours_enabled": False,
                    "business_hours": {
                        "timezone": "Asia/Shanghai",
                        "schedule": {
                            "monday": {"start": "09:00", "end": "18:00"},
                            "tuesday": {"start": "09:00", "end": "18:00"},
                            "wednesday": {"start": "09:00", "end": "18:00"},
                            "thursday": {"start": "09:00", "end": "18:00"},
                            "friday": {"start": "09:00", "end": "18:00"},
                            "saturday": {"start": "10:00", "end": "16:00"},
                            "sunday": {"start": "10:00", "end": "16:00"}
                        }
                    },
                    "offline_message": "客服暂时离线，请留言，我们会尽快回复您。",
                    "quick_replies": [
                        "产品咨询",
                        "技术支持", 
                        "价格信息",
                        "联系方式"
                    ]
                }
        
        return JSONResponse(content={
            "success": True,
            "data": config
        })
        
    except Exception as e:
        logger.error(f"获取组件配置失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取配置失败"
        )

@router.post(
    "/config/{widget_id}",
    summary="更新组件配置",
    description="更新指定组件的配置"
)
async def update_widget_config(
    widget_id: str = Path(..., description="组件ID"),
    config_request: WidgetConfigRequest = ...
):
    """更新组件配置"""
    try:
        from sqlalchemy.ext.asyncio import AsyncSession
        from db.database import AsyncSessionLocal
        from ..models.public_widget import PublicWidgetConfig
        from sqlalchemy import select
        import uuid
        
        async with AsyncSessionLocal() as db:
            # 查找或创建配置
            result = await db.execute(
                select(PublicWidgetConfig).where(PublicWidgetConfig.widget_id == widget_id)
            )
            config_obj = result.scalars().first()
            
            if config_obj:
                # 更新现有配置
                config_obj.title = config_request.title
                config_obj.description = config_request.description
                config_obj.assistant_prompt = config_request.assistant_prompt
                config_obj.theme_color = config_request.theme_color
                config_obj.position = config_request.position
                config_obj.size = config_request.size
                config_obj.auto_open = config_request.auto_open
                config_obj.show_avatar = config_request.show_avatar
                config_obj.show_typing_indicator = config_request.show_typing_indicator
                config_obj.enable_file_upload = config_request.enable_file_upload
                config_obj.enable_voice_input = config_request.enable_voice_input
                config_obj.auto_greeting = config_request.auto_greeting
                config_obj.greeting_message = config_request.greeting_message
                config_obj.custom_css = config_request.custom_css
                config_obj.updated_at = datetime.utcnow()
            else:
                # 创建新配置
                config_obj = PublicWidgetConfig(
                    project_id=uuid.UUID("*************-48ab-8092-e8eb7f663677"),  # 默认项目ID
                    widget_id=widget_id,
                    title=config_request.title,
                    description=config_request.description,
                    assistant_prompt=config_request.assistant_prompt,
                    theme_color=config_request.theme_color,
                    position=config_request.position,
                    size=config_request.size,
                    auto_open=config_request.auto_open,
                    show_avatar=config_request.show_avatar,
                    show_typing_indicator=config_request.show_typing_indicator,
                    enable_file_upload=config_request.enable_file_upload,
                    enable_voice_input=config_request.enable_voice_input,
                    auto_greeting=config_request.auto_greeting,
                    greeting_message=config_request.greeting_message,
                    custom_css=config_request.custom_css
                )
                db.add(config_obj)
            
            await db.commit()
            await db.refresh(config_obj)
            
            updated_config = config_obj.to_dict()
        
        return JSONResponse(content={
            "success": True,
            "message": "配置更新成功",
            "data": updated_config
        })
        
    except Exception as e:
        logger.error(f"更新组件配置失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新配置失败"
        )

@router.get(
    "/analytics/{widget_id}",
    summary="获取组件分析数据",
    description="获取指定组件的使用分析数据"
)
async def get_widget_analytics(
    widget_id: str = Path(..., description="组件ID"),
    days: int = Query(7, ge=1, le=90, description="分析天数")
):
    """获取组件分析数据"""
    try:
        # 这里需要从数据库获取实际分析数据
        # 暂时返回模拟数据
        
        analytics = {
            "widget_id": widget_id,
            "period_days": days,
            "total_sessions": 156,
            "total_messages": 892,
            "avg_session_duration": 180,  # 秒
            "user_satisfaction": 4.2,
            "top_intents": [
                {"intent": "product_inquiry", "count": 45, "percentage": 28.8},
                {"intent": "technical_support", "count": 32, "percentage": 20.5},
                {"intent": "pricing", "count": 28, "percentage": 17.9},
                {"intent": "contact_info", "count": 25, "percentage": 16.0},
                {"intent": "complaint", "count": 15, "percentage": 9.6}
            ],
            "daily_stats": [
                {"date": "2025-01-01", "sessions": 22, "messages": 128},
                {"date": "2025-01-02", "sessions": 18, "messages": 95},
                {"date": "2025-01-03", "sessions": 25, "messages": 142},
                {"date": "2025-01-04", "sessions": 20, "messages": 115},
                {"date": "2025-01-05", "sessions": 28, "messages": 165},
                {"date": "2025-01-06", "sessions": 24, "messages": 138},
                {"date": "2025-01-07", "sessions": 19, "messages": 109}
            ],
            "device_stats": {
                "desktop": 45.2,
                "mobile": 42.3,
                "tablet": 12.5
            },
            "browser_stats": {
                "chrome": 58.7,
                "safari": 22.1,
                "firefox": 12.8,
                "edge": 6.4
            }
        }
        
        return JSONResponse(content={
            "success": True,
            "data": analytics
        })
        
    except Exception as e:
        logger.error(f"获取组件分析数据失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取分析数据失败"
        )
