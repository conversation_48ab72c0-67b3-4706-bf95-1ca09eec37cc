/* 闯关游戏样式 */
.adventure-game-container {
  min-height: 100vh;
  width: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  position: relative;
  overflow: hidden;
}

.adventure-game-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 0;
}

.adventure-game-header {
  text-align: center;
  margin-bottom: 30px;
  position: relative;
  z-index: 1;
}

.game-logo {
  width: 120px;
  height: 120px;
  object-fit: contain;
  margin-bottom: 20px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.9);
  padding: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.game-title {
  color: #fff !important;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
  margin-bottom: 10px !important;
}

.game-description {
  color: rgba(255, 255, 255, 0.9) !important;
  font-size: 18px;
  max-width: 600px;
  margin: 0 auto;
}

.adventure-game-content {
  width: 100%;
  max-width: 900px;
  position: relative;
  z-index: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
}

.settings-bar {
  display: flex;
  justify-content: flex-end;
  width: 100%;
  margin-bottom: 20px;
}

.settings-item {
  display: flex;
  align-items: center;
  margin-left: 15px;
  background-color: rgba(255, 255, 255, 0.2);
  padding: 5px 10px;
  border-radius: 20px;
}

.settings-label {
  color: #fff;
  margin-right: 8px;
  font-size: 14px;
}

/* 游戏开始界面 */
.game-start-screen {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-bottom: 30px;
}

.game-info-card {
  width: 100%;
  max-width: 700px;
  background-color: rgba(255, 255, 255, 0.95) !important;
  border-radius: 15px !important;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2) !important;
  padding: 30px !important;
}

.level-preview {
  margin: 30px 0;
  padding: 20px;
  background-color: rgba(0, 0, 0, 0.03);
  border-radius: 10px;
}

.start-button {
  width: 100%;
  height: 50px;
  font-size: 18px;
  margin-top: 20px;
}

/* 游戏进行中界面 */
.game-play-screen {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.game-level-card {
  width: 100%;
  background-color: rgba(255, 255, 255, 0.95) !important;
  border-radius: 15px !important;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2) !important;
  padding: 30px !important;
  margin-bottom: 20px;
}

.level-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.level-info {
  flex: 1;
}

.level-score {
  min-width: 120px;
  text-align: center;
}

.question-container {
  margin: 20px 0;
}

.question {
  margin-bottom: 20px;
  padding: 15px;
  background-color: rgba(0, 0, 0, 0.03);
  border-radius: 10px;
}

.question-text {
  margin: 0 !important;
}

.options {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 15px;
  margin-bottom: 20px;
}

.option-button {
  height: auto !important;
  padding: 15px !important;
  text-align: left;
  white-space: normal;
  height: 100%;
  transition: all 0.3s;
}

.option-button.selected {
  border-color: #1890ff;
  background-color: rgba(24, 144, 255, 0.1);
}

.option-button.correct {
  border-color: #52c41a;
  background-color: rgba(82, 196, 26, 0.1);
}

.option-button.incorrect {
  border-color: #f5222d;
  background-color: rgba(245, 34, 45, 0.1);
}

.question-actions {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.game-progress {
  width: 100%;
  margin-top: 20px;
  padding: 15px;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 10px;
}

/* 游戏结束界面 */
.game-end-screen {
  width: 100%;
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 15px;
  padding: 30px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  justify-content: center;
  margin-top: 30px;
  gap: 15px;
}

.action-button {
  background-color: rgba(255, 255, 255, 0.9);
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.action-button:hover {
  background-color: #fff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* 页脚 */
.adventure-game-footer {
  width: 100%;
  text-align: center;
  margin-top: 40px;
  padding: 20px 0;
  color: rgba(255, 255, 255, 0.7);
  position: relative;
  z-index: 1;
}

.footer-links {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 10px;
}

.footer-link {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
}

.footer-link:hover {
  color: #fff;
  text-decoration: underline;
}

/* 加载状态 */
.adventure-game-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #7367f0, #ce9ffc);
}

.loading-icon {
  font-size: 48px;
  color: #fff;
}

.loading-text {
  color: #fff;
  font-size: 18px;
  margin-top: 20px;
}

/* 奖品结果 */
.prize-result {
  display: flex;
  align-items: center;
  margin-top: 20px;
  padding: 15px;
  background-color: rgba(0, 0, 0, 0.03);
  border-radius: 10px;
}

.prize-result-image {
  width: 80px;
  height: 80px;
  object-fit: contain;
  margin-right: 20px;
}

.prize-result-info {
  flex: 1;
}

.prize-result-title {
  margin: 0 0 5px 0 !important;
}

.prize-result-description {
  margin: 0 !important;
  color: rgba(0, 0, 0, 0.65);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .game-logo {
    width: 80px;
    height: 80px;
  }
  
  .game-title {
    font-size: 24px !important;
  }
  
  .game-description {
    font-size: 16px;
  }
  
  .game-info-card,
  .game-level-card,
  .game-end-screen {
    padding: 20px !important;
  }
  
  .options {
    grid-template-columns: 1fr;
  }
  
  .level-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .level-score {
    margin-top: 15px;
  }
  
  .action-buttons {
    flex-wrap: wrap;
  }
}
