-- 为项目初始化空间设置
-- 确保每个项目都有空间设置配置

DO $$
DECLARE
    project_record RECORD;
    settings_exists INTEGER;
BEGIN
    -- 为每个项目创建默认空间设置
    FOR project_record IN SELECT id, tenant_id FROM projects LOOP
        -- 检查空间设置是否已存在
        SELECT COUNT(*) INTO settings_exists 
        FROM space_settings 
        WHERE project_id = project_record.id;
        
        IF settings_exists = 0 THEN
            -- 插入项目级别的空间设置
            INSERT INTO space_settings (
                id, tenant_id, project_id, 
                max_file_size, allowed_extensions, 
                max_storage_per_user, max_storage_per_project,
                created_at, updated_at
            ) VALUES (
                gen_random_uuid(),
                project_record.tenant_id,
                project_record.id,
                10485760,  -- 10MB 默认最大文件大小
                ARRAY['jpg', 'jpeg', 'png', 'gif', 'pdf', 'doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt', 'zip', 'rar'],  -- 默认允许的文件类型
                524288000,  -- 500MB 默认每用户最大存储空间
                5368709120,  -- 5GB 默认每项目最大存储空间
                NOW(),
                NOW()
            );
            
            RAISE NOTICE '为项目 % 创建了默认空间设置', project_record.id;
        ELSE
            RAISE NOTICE '项目 % 的空间设置已存在', project_record.id;
        END IF;
    END LOOP;
    
    RAISE NOTICE '空间设置初始化完成';
END $$; 