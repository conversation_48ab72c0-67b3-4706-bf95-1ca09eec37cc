import React, { useState, useEffect } from 'react';
import {
  Table,
  Button,
  Space,
  Tag,
  Input,
  DatePicker,
  Form,
  Select,
  Modal,
  message,
  Typography,
  Tooltip,
  Tabs,
  Row,
  Col,
  Statistic,
  Card,
  Divider,
  Badge,
  Popconfirm,
  Upload,
  Avatar,
  Dropdown,
  Menu
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  EditOutlined,
  DeleteOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ClockCircleOutlined,
  SyncOutlined,
  UploadOutlined,
  FileTextOutlined,
  UserOutlined,
  TeamOutlined,
  CalendarOutlined,
  BellOutlined,
  MoreOutlined,
  FilterOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import dayjs from 'dayjs';
import apiService from '../../../../services/api';

const { Title, Text, Paragraph } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;
const { TextArea } = Input;

// 任务状态标签颜色映射
const statusColors = {
  pending: 'default',
  in_progress: 'processing',
  completed: 'success',
  overdue: 'error',
  cancelled: 'warning'
};

// 任务状态文本映射
const statusTexts = {
  pending: '待处理',
  in_progress: '进行中',
  completed: '已完成',
  overdue: '已逾期',
  cancelled: '已取消'
};

// 任务优先级标签颜色映射
const priorityColors = {
  low: 'default',
  medium: 'blue',
  high: 'orange',
  urgent: 'red'
};

// 任务优先级文本映射
const priorityTexts = {
  low: '低',
  medium: '中',
  high: '高',
  urgent: '紧急'
};

// 任务类别映射
const categoryTexts = {
  store: '门店',
  operation: '运营',
  marketing: '营销',
  inventory: '库存',
  finance: '财务',
  other: '其他'
};

// 任务类型映射
const taskTypeTexts = {
  inventory: '盘点任务',
  reconciliation: '对账任务',
  report: '报表任务',
  other: '其他任务'
};

/**
 * 财务部门门店任务管理组件
 */
const FinanceStoreTasks = () => {
  // 状态
  const [activeTab, setActiveTab] = useState('1');
  const [loading, setLoading] = useState(false);
  const [loadingUsers, setLoadingUsers] = useState(false);
  const [tasks, setTasks] = useState([]);
  const [stores, setStores] = useState([]);
  const [users, setUsers] = useState([]);
  const [filteredUsers, setFilteredUsers] = useState([]);
  const [selectedStores, setSelectedStores] = useState([]);
  const [taskStats, setTaskStats] = useState({
    total: 0,
    pending: 0,
    in_progress: 0,
    completed: 0,
    overdue: 0,
    cancelled: 0
  });
  const [storeStats, setStoreStats] = useState([]);
  const [selectedTask, setSelectedTask] = useState(null);
  const [modalVisible, setModalVisible] = useState(false);
  const [fileList, setFileList] = useState([]);
  const [form] = Form.useForm();
  const [filterForm] = Form.useForm();
  const [filters, setFilters] = useState({
    status: undefined,
    priority: undefined,
    category: 'finance',
    assignee: undefined,
    dateRange: null,
    search: ''
  });
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });

  // 初始化
  useEffect(() => {
    // 先获取门店和用户数据
    fetchStores();

    // 获取用户数据并确保设置过滤后的用户列表
    fetchUsers().then(() => {
      // 确保用户数据加载后设置过滤后的用户列表
      setTimeout(() => {
        if (users.length > 0 && filteredUsers.length === 0) {
          setFilteredUsers(users);
          console.log('设置初始过滤用户列表:', users);
        }
      }, 500);
    });

    // 获取任务列表
    fetchTasks();
  }, []);

  // 获取门店列表
  const fetchStores = async () => {
    try {
      const response = await apiService.project.store.getList();
      if (response && response.items) {
        setStores(response.items);
      }
    } catch (error) {
      console.error('获取门店列表失败:', error);
      message.error('获取门店列表失败');
    }
  };

  // 获取用户列表
  const fetchUsers = async (storeIds = []) => {
    return new Promise(async (resolve) => {
      try {
        setLoadingUsers(true);
        const response = await apiService.project.user.getList();

        if (response && Array.isArray(response)) {
          // 处理返回的数据格式
          const allUsers = response
            .filter(item => item.success && item.data)
            .map(item => {
              const userData = item.data;
              // 确保用户数据包含必要的字段
              return {
                ...userData,
                // 如果没有name，使用username
                name: userData.name || userData.username,
                // 如果有多个门店名称，用逗号连接
                store_name: Array.isArray(userData.store_names) && userData.store_names.length > 0
                  ? userData.store_names.join(', ')
                  : ''
              };
            });

          setUsers(allUsers);
          console.log('获取到的用户列表:', allUsers);

          // 如果选择了特定门店，过滤用户
          if (storeIds && storeIds.length > 0 && !storeIds.includes('all')) {
            // 过滤出选定门店的用户
            const storeUsers = allUsers.filter(user => {
              // 检查用户是否属于选定的门店
              if (Array.isArray(user.store_ids)) {
                return user.store_ids.some(storeId =>
                  storeIds.includes(storeId.toString())
                );
              }
              return false;
            });

            console.log('过滤后的用户列表:', storeUsers);
            setFilteredUsers(storeUsers.length > 0 ? storeUsers : allUsers);
          } else {
            // 如果选择了"所有门店"或没有选择门店，显示所有用户
            setFilteredUsers(allUsers);
          }
        } else if (response && response.items) {
          // 兼容原有的数据格式
          const allUsers = response.items;
          setUsers(allUsers);
          setFilteredUsers(allUsers);
        } else {
          // 如果没有数据，设置为空数组
          setUsers([]);
          setFilteredUsers([]);
        }
        resolve(true);
      } catch (error) {
        console.error('获取用户列表失败:', error);
        message.error('获取用户列表失败');
        resolve(false);
      } finally {
        setLoadingUsers(false);
      }
    });
  };

  // 处理门店选择变化
  const handleStoreChange = (values) => {
    // 检查是否选择了"所有门店"
    if (values && values.includes('all')) {
      // 如果选择了"所有门店"，移除其他所有选项
      form.setFieldsValue({ store_id: ['all'] });
      setSelectedStores(['all']);
      fetchUsers(); // 加载所有用户
    } else if (values && values.length > 0) {
      // 如果选择了特定门店，加载这些门店的用户
      setSelectedStores(values);
      fetchUsers(values);
    } else {
      // 如果没有选择任何门店，清空用户列表
      setSelectedStores([]);
      setFilteredUsers([]);
    }
  };

  // 获取任务列表
  const fetchTasks = async () => {
    setLoading(true);
    try {
      // 构建查询参数
      const params = {
        status: filters.status,
        priority: filters.priority,
        category: filters.category,
        assignee_id: filters.assignee,
        start_date: filters.dateRange?.[0]?.format('YYYY-MM-DD'),
        end_date: filters.dateRange?.[1]?.format('YYYY-MM-DD'),
        search: filters.search || undefined,
        page: pagination.current,
        size: pagination.pageSize
      };

      try {
        // 获取当前用户信息
        const currentUser = await apiService.project.user.getCurrentUser();

        // 如果当前用户不是管理员，则只显示分配给自己的任务
        if (currentUser && !currentUser.is_admin) {
          // 检查用户是否属于财务部门
          const isFinanceUser = currentUser.roles && currentUser.roles.some(
            role => role.name.includes('财务') || role.name.includes('finance')
          );

          if (isFinanceUser) {
            // 财务部门用户可以看到所有财务任务
            params.category = 'finance';
          } else {
            // 非财务部门用户只能看到分配给自己的任务
            params.assignee_id = currentUser.id;
          }
        }
      } catch (error) {
        console.error('获取当前用户信息失败:', error);
        // 如果获取用户信息失败，默认只显示财务任务
        params.category = 'finance';
      }

      // 调用API获取任务列表
      try {
        const response = await apiService.project.routineTask.getList(params);
        if (response && response.items) {
          setTasks(response.items);
          setPagination({
            ...pagination,
            total: response.total || 0
          });

          // 更新任务统计
          updateTaskStats(response.items);

          // 更新门店统计
          updateStoreStats(response.items);
        } else {
          setTasks([]);
        }
      } catch (error) {
        console.error('获取任务列表失败:', error);
        message.error('获取任务列表失败: ' + (error.message || '未知错误'));
        setTasks([]);
      }
    } catch (error) {
      console.error('获取任务列表失败:', error);
      message.error('获取任务列表失败: ' + (error.message || '未知错误'));
    } finally {
      setLoading(false);
    }
  };

  // 更新任务统计
  const updateTaskStats = (taskList) => {
    const stats = {
      total: taskList.length,
      pending: 0,
      in_progress: 0,
      completed: 0,
      overdue: 0,
      cancelled: 0
    };

    taskList.forEach(task => {
      if (task.status) {
        stats[task.status] = (stats[task.status] || 0) + 1;
      }
    });

    setTaskStats(stats);
  };

  // 更新门店统计
  const updateStoreStats = (taskList) => {
    const storeMap = new Map();

    taskList.forEach(task => {
      if (task.store_id && task.store_name) {
        if (!storeMap.has(task.store_id)) {
          storeMap.set(task.store_id, {
            store_id: task.store_id,
            store_name: task.store_name,
            total: 0,
            pending: 0,
            in_progress: 0,
            completed: 0,
            overdue: 0,
            cancelled: 0,
            completion_rate: 0
          });
        }

        const storeStat = storeMap.get(task.store_id);
        storeStat.total += 1;

        if (task.status) {
          storeStat[task.status] = (storeStat[task.status] || 0) + 1;
        }
      }
    });

    // 计算完成率
    storeMap.forEach(stat => {
      stat.completion_rate = stat.total > 0 ? Math.round((stat.completed / stat.total) * 100) : 0;
    });

    setStoreStats(Array.from(storeMap.values()));
  };

  // 打开任务表单
  const handleOpenTaskForm = async (task = null) => {
    try {
      let isAdmin = false;
      let isProjectAdmin = false;
      let isFinanceUser = false;

      try {
        // 获取当前用户信息
        const currentUser = await apiService.project.user.getCurrentUser();

        // 检查用户是否有权限创建/编辑任务
        isAdmin = currentUser && (currentUser.is_admin || currentUser.is_system_admin || currentUser.is_super_admin);
        isProjectAdmin = currentUser && (currentUser.is_project_admin || currentUser.is_tenant_admin);
        isFinanceUser = currentUser && currentUser.roles && currentUser.roles.some(
          role => role.name.includes('财务') || role.name.includes('finance')
        );
      } catch (error) {
        console.error('获取当前用户信息失败:', error);
        // 如果获取用户信息失败，默认允许操作，后端会进行权限验证
        isFinanceUser = true;
      }

      // 如果是项目管理员，允许创建/编辑任务
      if (isProjectAdmin) {
        isAdmin = true;
      }

      // 如果不是管理员也不是财务部门用户，则不允许创建/编辑任务
      if (!isAdmin && !isFinanceUser) {
        message.error('您没有权限创建或编辑财务任务');
        return;
      }

      // 如果是编辑任务，检查是否有权限编辑
      if (task && !isAdmin) {
        // 检查任务是否属于财务部门
        if (task.category !== 'finance') {
          message.error('您只能编辑财务部门的任务');
          return;
        }

        try {
          // 获取当前用户信息
          const currentUser = await apiService.project.user.getCurrentUser();

          // 检查任务是否分配给自己
          const isAssignee = currentUser && task.assignee_ids && task.assignee_ids.includes(currentUser.id);
          if (!isAssignee && !isFinanceUser) {
            message.error('您只能编辑分配给自己的任务');
            return;
          }
        } catch (error) {
          console.error('获取当前用户信息失败:', error);
          // 如果获取用户信息失败，默认允许操作，后端会进行权限验证
        }
      }

      setSelectedTask(task);
      form.resetFields();

      if (task) {
        // 编辑任务
        const storeIds = task.store_id ?
          (Array.isArray(task.store_id) ? task.store_id : [task.store_id]) :
          [];

        // 如果是所有门店
        if (task.is_all_stores) {
          storeIds.push('all');
        }

        // 加载相关门店的用户
        setSelectedStores(storeIds);
        fetchUsers(storeIds);

        form.setFieldsValue({
          ...task,
          start_date: task.start_date ? dayjs(task.start_date).startOf('day') : null,
          due_date: task.due_date ? dayjs(task.due_date).startOf('day') : null,
          store_id: storeIds,
          assignee_ids: task.assignee_ids || []
        });

        // 设置附件
        if (task.attachments && task.attachments.length > 0) {
          const files = task.attachments.map((url, index) => ({
            uid: `-${index}`,
            name: url.split('/').pop(),
            status: 'done',
            url
          }));
          setFileList(files);
        } else {
          setFileList([]);
        }
      } else {
        // 新建任务
        setSelectedStores([]);
        setFilteredUsers(users); // 显示所有用户

        // 设置表单初始值
        // 使用当前日期，确保只有日期部分
        const currentDate = dayjs().startOf('day');
        console.log('设置新建任务开始日期:', currentDate.format('YYYY-MM-DD'));

        form.setFieldsValue({
          category: 'finance', // 财务部门只能创建财务任务
          priority: 'medium',
          start_date: currentDate, // 使用当前日期作为开始日期
          assignee_ids: [],
          store_id: []
        });
        setFileList([]);
      }

      setModalVisible(true);
    } catch (error) {
      console.error('获取用户信息失败:', error);
      message.error('获取用户信息失败: ' + (error.message || '未知错误'));
    }
  };

  // 提交任务表单
  const handleSubmitTask = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      let isAdmin = false;
      let isProjectAdmin = false;
      let isFinanceUser = false;

      try {
        // 获取当前用户信息
        const currentUser = await apiService.project.user.getCurrentUser();

        // 检查用户是否有权限创建/编辑任务
        isAdmin = currentUser && (currentUser.is_admin || currentUser.is_system_admin || currentUser.is_super_admin);
        isProjectAdmin = currentUser && (currentUser.is_project_admin || currentUser.is_tenant_admin);
        isFinanceUser = currentUser && currentUser.roles && currentUser.roles.some(
          role => role.name.includes('财务') || role.name.includes('finance')
        );
      } catch (error) {
        console.error('获取当前用户信息失败:', error);
        // 如果获取用户信息失败，默认允许操作，后端会进行权限验证
        isFinanceUser = true;
      }

      // 如果是项目管理员，允许创建/编辑任务
      if (isProjectAdmin) {
        isAdmin = true;
      }

      // 如果不是管理员也不是财务部门用户，则不允许创建/编辑任务
      if (!isAdmin && !isFinanceUser) {
        message.error('您没有权限创建或编辑财务任务');
        setLoading(false);
        return;
      }

      // 确保任务类别为财务
      if (values.category !== 'finance') {
        values.category = 'finance';
      }

      // 准备提交数据
      const storeIds = values.store_id || [];
      const isAllStores = storeIds.includes('all');

      // 处理门店选择
      let finalStoreIds = [];
      if (isAllStores) {
        // 如果选择了"所有门店"，获取所有门店ID
        finalStoreIds = stores.map(store => store.id);
      } else {
        // 否则使用选择的门店ID，但要过滤掉可能的 'all' 值
        finalStoreIds = storeIds.filter(id => id !== 'all');
      }

      // 确保至少有一个门店ID
      if (finalStoreIds.length === 0) {
        throw new Error('请至少选择一个门店');
      }

      // 确保日期格式正确
      let startDate = null;
      if (values.start_date) {
        // 使用 startOf('day') 确保只有日期部分
        startDate = values.start_date.startOf('day').format('YYYY-MM-DD');
      } else {
        startDate = dayjs().startOf('day').format('YYYY-MM-DD');
      }

      let dueDate = null;
      if (values.due_date) {
        dueDate = values.due_date.startOf('day').format('YYYY-MM-DD');
      }

      console.log('提交任务开始日期:', startDate);
      console.log('提交任务截止日期:', dueDate);

      // 根据后端API要求，如果是单个门店，store_id应该是字符串而不是数组
      const taskData = {
        ...values,
        store_id: finalStoreIds.length === 1 ? finalStoreIds[0] : finalStoreIds,
        is_all_stores: isAllStores,
        start_date: startDate,
        due_date: dueDate,
        attachments: fileList.map(file => file.response?.url || file.url),
        tags: values.task_type ? [values.task_type] : ['finance'] // 使用tags字段存储任务类型
      };

      // 删除task_type字段，因为后端模型中没有这个字段
      if (taskData.task_type) {
        delete taskData.task_type;
      }

      // 打印提交的数据，方便调试
      console.log('提交任务数据:', taskData);

      if (selectedTask) {
        // 如果是编辑任务，检查是否有权限编辑
        if (!isAdmin) {
          try {
            // 获取当前用户信息
            const currentUser = await apiService.project.user.getCurrentUser();

            // 检查任务是否分配给自己
            const isAssignee = currentUser && selectedTask.assignee_ids && selectedTask.assignee_ids.includes(currentUser.id);
            if (!isAssignee && !isFinanceUser) {
              message.error('您只能编辑分配给自己的任务');
              setLoading(false);
              return;
            }
          } catch (error) {
            console.error('获取当前用户信息失败:', error);
            // 如果获取用户信息失败，默认允许操作，后端会进行权限验证
          }
        }

        // 更新任务
        await apiService.project.routineTask.update(selectedTask.id, taskData);
        message.success('任务更新成功');
      } else {
        // 创建任务
        await apiService.project.routineTask.create(taskData);
        message.success('任务创建成功');
      }

      setModalVisible(false);
      fetchTasks();
    } catch (error) {
      console.error('提交任务失败:', error);
      if (error.errorFields) {
        // 表单验证错误
        return;
      }
      // 显示更详细的错误信息
      if (error.response?.data?.detail) {
        if (Array.isArray(error.response.data.detail)) {
          // 如果是数组形式的错误信息
          const errorDetails = error.response.data.detail.map(err => {
            return `${err.loc.join('.')}：${err.msg}`;
          }).join('; ');
          message.error('提交任务失败: ' + errorDetails);
        } else {
          message.error('提交任务失败: ' + error.response.data.detail);
        }
      } else {
        message.error('提交任务失败: ' + error.message);
      }

      // 打印完整错误信息到控制台，方便调试
      console.error('提交任务失败详细信息:', error.response?.data);
    } finally {
      setLoading(false);
    }
  };

  // 更新任务状态
  const handleUpdateTaskStatus = async (taskId, status) => {
    try {
      let isAdmin = false;
      let isProjectAdmin = false;
      let isFinanceUser = false;
      let currentUserId = null;

      try {
        // 获取当前用户信息
        const currentUser = await apiService.project.user.getCurrentUser();

        // 检查用户是否有权限更新任务状态
        isAdmin = currentUser && (currentUser.is_admin || currentUser.is_system_admin || currentUser.is_super_admin);
        isProjectAdmin = currentUser && (currentUser.is_project_admin || currentUser.is_tenant_admin);
        isFinanceUser = currentUser && currentUser.roles && currentUser.roles.some(
          role => role.name.includes('财务') || role.name.includes('finance')
        );
        currentUserId = currentUser && currentUser.id;
      } catch (error) {
        console.error('获取当前用户信息失败:', error);
        // 如果获取用户信息失败，默认允许操作，后端会进行权限验证
        isFinanceUser = true;
      }

      // 如果是项目管理员，允许更新任务状态
      if (isProjectAdmin) {
        isAdmin = true;
      }

      // 获取任务详情
      const task = tasks.find(t => t.id === taskId);
      if (!task) {
        message.error('任务不存在');
        return;
      }

      // 检查任务是否属于财务部门
      if (task.category !== 'finance' && !isAdmin) {
        message.error('您只能更新财务部门的任务');
        return;
      }

      // 检查任务是否分配给自己
      const isAssignee = currentUserId && task.assignee_ids && task.assignee_ids.includes(currentUserId);
      if (!isAdmin && !isFinanceUser && !isAssignee) {
        message.error('您只能更新分配给自己的任务');
        return;
      }

      await apiService.project.routineTask.updateStatus(taskId, { status });
      message.success('任务状态更新成功');
      fetchTasks();
    } catch (error) {
      console.error('更新任务状态失败:', error);
      message.error('更新任务状态失败: ' + (error.message || '未知错误'));
    }
  };

  // 删除任务
  const handleDeleteTask = async (taskId) => {
    try {
      let isAdmin = false;
      let isProjectAdmin = false;
      let isFinanceUser = false;

      try {
        // 获取当前用户信息
        const currentUser = await apiService.project.user.getCurrentUser();

        // 检查用户是否有权限删除任务
        isAdmin = currentUser && (currentUser.is_admin || currentUser.is_system_admin || currentUser.is_super_admin);
        isProjectAdmin = currentUser && (currentUser.is_project_admin || currentUser.is_tenant_admin);
        isFinanceUser = currentUser && currentUser.roles && currentUser.roles.some(
          role => role.name.includes('财务') || role.name.includes('finance')
        );
      } catch (error) {
        console.error('获取当前用户信息失败:', error);
        // 如果获取用户信息失败，默认允许操作，后端会进行权限验证
        isFinanceUser = true;
      }

      // 如果是项目管理员，允许删除任务
      if (isProjectAdmin) {
        isAdmin = true;
      }

      // 如果不是管理员也不是财务部门用户，则不允许删除任务
      if (!isAdmin && !isFinanceUser) {
        message.error('您没有权限删除财务任务');
        return;
      }

      // 获取任务详情
      const task = tasks.find(t => t.id === taskId);
      if (!task) {
        message.error('任务不存在');
        return;
      }

      // 检查任务是否属于财务部门
      if (task.category !== 'finance' && !isAdmin) {
        message.error('您只能删除财务部门的任务');
        return;
      }

      await apiService.project.routineTask.delete(taskId);
      message.success('任务删除成功');
      fetchTasks();
    } catch (error) {
      console.error('删除任务失败:', error);
      message.error('删除任务失败: ' + (error.message || '未知错误'));
    }
  };

  // 处理筛选条件变化
  const handleFilterChange = (key, value) => {
    setFilters({
      ...filters,
      [key]: value
    });
  };

  // 应用筛选条件
  const handleApplyFilters = () => {
    setPagination({
      ...pagination,
      current: 1
    });
    fetchTasks();
  };

  // 重置筛选条件
  const handleResetFilters = () => {
    setFilters({
      status: undefined,
      priority: undefined,
      category: 'finance',
      assignee: undefined,
      dateRange: null,
      search: ''
    });
    filterForm.resetFields();

    // 延迟执行，确保状态更新后再获取数据
    setTimeout(() => {
      fetchTasks();
    }, 0);
  };

  // 处理标签页切换
  const handleTabChange = (key) => {
    setActiveTab(key);
  };

  // 处理表格分页变化
  const handleTableChange = (pagination) => {
    setPagination(pagination);
    fetchTasks();
  };

  // 处理文件上传变化
  const handleFileChange = ({ fileList }) => {
    setFileList(fileList);
  };

  // 文件上传前检查
  const beforeUpload = (file) => {
    const isLt10M = file.size / 1024 / 1024 < 10;
    if (!isLt10M) {
      message.error('文件大小不能超过10MB!');
    }
    return isLt10M;
  };

  // 任务表格列定义
  const columns = [
    {
      title: '任务标题',
      dataIndex: 'title',
      key: 'title',
      render: (text, record) => (
        <a onClick={() => handleOpenTaskForm(record)}>{text}</a>
      )
    },
    {
      title: '门店',
      dataIndex: 'store_name',
      key: 'store_name',
      ellipsis: true
    },
    {
      title: '类别',
      dataIndex: 'category',
      key: 'category',
      render: (text) => categoryTexts[text] || text
    },
    {
      title: '任务类型',
      dataIndex: 'task_type',
      key: 'task_type',
      render: (text) => taskTypeTexts[text] || text || '未分类'
    },
    {
      title: '优先级',
      dataIndex: 'priority',
      key: 'priority',
      render: (priority) => (
        <Tag color={priorityColors[priority] || 'default'}>
          {priorityTexts[priority] || priority}
        </Tag>
      )
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={statusColors[status] || 'default'}>
          {statusTexts[status] || status}
        </Tag>
      )
    },
    {
      title: '开始日期',
      dataIndex: 'start_date',
      key: 'start_date',
    },
    {
      title: '截止日期',
      dataIndex: 'due_date',
      key: 'due_date',
    },
    {
      title: '指派人',
      dataIndex: 'assignee_names',
      key: 'assignees',
      render: (assignees) => {
        if (!assignees || assignees.length === 0) return '-';
        return (
          <Avatar.Group maxCount={3}>
            {assignees.map((name, index) => (
              <Tooltip key={index} title={name}>
                <Avatar style={{ backgroundColor: '#' + Math.floor(Math.random()*16777215).toString(16) }}>
                  {name.substring(0, 1)}
                </Avatar>
              </Tooltip>
            ))}
          </Avatar.Group>
        );
      }
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_, record) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleOpenTaskForm(record)}
          >
            编辑
          </Button>

          <Dropdown overlay={
            <Menu>
              {record.status !== 'in_progress' && (
                <Menu.Item key="start" onClick={() => handleUpdateTaskStatus(record.id, 'in_progress')}>
                  <ClockCircleOutlined /> 开始任务
                </Menu.Item>
              )}

              {record.status !== 'completed' && (
                <Menu.Item key="complete" onClick={() => handleUpdateTaskStatus(record.id, 'completed')}>
                  <CheckCircleOutlined /> 完成任务
                </Menu.Item>
              )}

              {record.status !== 'cancelled' && (
                <Menu.Item key="cancel" onClick={() => handleUpdateTaskStatus(record.id, 'cancelled')}>
                  <CloseCircleOutlined /> 取消任务
                </Menu.Item>
              )}

              <Menu.Item key="delete" danger onClick={() => {
                Modal.confirm({
                  title: '确认删除',
                  content: '确定要删除此任务吗？此操作不可恢复。',
                  okText: '确认',
                  cancelText: '取消',
                  onOk: () => handleDeleteTask(record.id)
                });
              }}>
                <DeleteOutlined /> 删除任务
              </Menu.Item>
            </Menu>
          } trigger={['click']}>
            <Button type="link" size="small">
              更多 <MoreOutlined />
            </Button>
          </Dropdown>
        </Space>
      ),
    },
  ];

  // 门店统计表格列
  const storeStatsColumns = [
    {
      title: '门店',
      dataIndex: 'store_name',
      key: 'store_name',
    },
    {
      title: '总任务数',
      dataIndex: 'total',
      key: 'total',
    },
    {
      title: '待处理',
      dataIndex: 'pending',
      key: 'pending',
    },
    {
      title: '进行中',
      dataIndex: 'in_progress',
      key: 'in_progress',
    },
    {
      title: '已完成',
      dataIndex: 'completed',
      key: 'completed',
    },
    {
      title: '已逾期',
      dataIndex: 'overdue',
      key: 'overdue',
    },
    {
      title: '完成率',
      dataIndex: 'completion_rate',
      key: 'completion_rate',
      render: (text) => `${text}%`,
    },
  ];

  // 任务表单组件
  const TaskForm = () => (
    <Form
      form={form}
      layout="vertical"
    >
      <Form.Item
        name="title"
        label="任务标题"
        rules={[{ required: true, message: '请输入任务标题' }]}
      >
        <Input placeholder="请输入任务标题" />
      </Form.Item>

      <Form.Item
        name="description"
        label="任务描述"
        rules={[{ required: true, message: '请输入任务描述' }]}
      >
        <TextArea rows={4} placeholder="请输入任务描述" />
      </Form.Item>

      <Row gutter={16}>
        <Col span={12}>
          <Form.Item
            name="store_id"
            label="所属门店"
            rules={[{ required: true, message: '请选择门店' }]}
          >
            <Select
              placeholder="请选择门店"
              allowClear
              showSearch
              optionFilterProp="children"
              onChange={(value) => handleStoreChange(value)}
              mode="multiple"
              maxTagCount={3}
            >
              <Option key="all" value="all">所有门店</Option>
              {stores.map(store => (
                <Option key={store.id} value={store.id}>{store.name}</Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            name="category"
            label="任务类别"
            rules={[{ required: true, message: '请选择任务类别' }]}
            initialValue="finance"
          >
            <Select placeholder="请选择任务类别" disabled defaultValue="finance">
              <Option value="finance">财务</Option>
            </Select>
            <div style={{ color: '#999', fontSize: '12px', marginTop: '4px' }}>
              财务管理模块只能创建财务类别的任务
            </div>
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={12}>
          <Form.Item
            name="task_type"
            label="任务类型"
            rules={[{ required: true, message: '请选择任务类型' }]}
          >
            <Select placeholder="请选择任务类型">
              <Option value="inventory">盘点任务</Option>
              <Option value="reconciliation">对账任务</Option>
              <Option value="report">报表任务</Option>
              <Option value="other">其他任务</Option>
            </Select>
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            name="priority"
            label="优先级"
            rules={[{ required: true, message: '请选择优先级' }]}
          >
            <Select placeholder="请选择优先级">
              <Option value="low">低</Option>
              <Option value="medium">中</Option>
              <Option value="high">高</Option>
              <Option value="urgent">紧急</Option>
            </Select>
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={24}>
          <Form.Item
            name="assignee_ids"
            label="指派人"
            rules={[{ required: true, message: '请选择指派人' }]}
          >
            <Select
              mode="multiple"
              placeholder="请选择指派人"
              optionFilterProp="children"
              loading={loadingUsers}
              showSearch
              maxTagCount={3}
            >
              {filteredUsers.map(user => (
                <Option key={user.id} value={user.id}>
                  {user.name || user.username} {user.store_name ? `(${user.store_name})` : ''}
                </Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={12}>
          <Form.Item
            name="start_date"
            label="开始日期"
            rules={[{ required: true, message: '请选择开始日期' }]}
          >
            <DatePicker
              style={{ width: '100%' }}
              format="YYYY-MM-DD"
              placeholder="请选择开始日期"
              showTime={false} // 不显示时间选择器
              onChange={(date) => {
                if (date) {
                  // 确保只有日期部分
                  const dateOnly = date.startOf('day');
                  form.setFieldsValue({ start_date: dateOnly });
                }
              }}
            />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            name="due_date"
            label="截止日期"
          >
            <DatePicker
              style={{ width: '100%' }}
              format="YYYY-MM-DD"
              placeholder="请选择截止日期"
              showTime={false} // 不显示时间选择器
              onChange={(date) => {
                if (date) {
                  // 确保只有日期部分
                  const dateOnly = date.startOf('day');
                  form.setFieldsValue({ due_date: dateOnly });
                }
              }}
            />
          </Form.Item>
        </Col>
      </Row>

      <Form.Item
        name="attachments"
        label="附件"
      >
        <Upload
          listType="picture"
          fileList={fileList}
          onChange={handleFileChange}
          beforeUpload={beforeUpload}
          action="/api/v1/upload"
          multiple
        >
          <Button icon={<UploadOutlined />}>上传附件</Button>
        </Upload>
      </Form.Item>
    </Form>
  );

  // 筛选表单组件
  const FilterForm = () => (
    <Form
      form={filterForm}
      layout="inline"
      initialValues={filters}
      style={{ marginBottom: 16 }}
    >
      <Form.Item name="status" label="状态">
        <Select
          style={{ width: 120 }}
          placeholder="任务状态"
          allowClear
          onChange={(value) => handleFilterChange('status', value)}
        >
          <Option value="">全部</Option>
          <Option value="pending">待处理</Option>
          <Option value="in_progress">进行中</Option>
          <Option value="completed">已完成</Option>
          <Option value="overdue">已逾期</Option>
          <Option value="cancelled">已取消</Option>
        </Select>
      </Form.Item>

      <Form.Item name="priority" label="优先级">
        <Select
          style={{ width: 120 }}
          placeholder="优先级"
          allowClear
          onChange={(value) => handleFilterChange('priority', value)}
        >
          <Option value="">全部</Option>
          <Option value="low">低</Option>
          <Option value="medium">中</Option>
          <Option value="high">高</Option>
          <Option value="urgent">紧急</Option>
        </Select>
      </Form.Item>

      <Form.Item name="dateRange" label="日期范围">
        <RangePicker
          onChange={(dates) => handleFilterChange('dateRange', dates)}
          style={{ width: 240 }}
          defaultValue={[dayjs().startOf('month'), dayjs().endOf('month')]}
          format="YYYY-MM-DD"
        />
      </Form.Item>

      <Form.Item name="search" label="搜索">
        <Input
          placeholder="任务标题/描述"
          style={{ width: 200 }}
          allowClear
          onChange={(e) => handleFilterChange('search', e.target.value)}
        />
      </Form.Item>

      <Form.Item>
        <Button
          type="primary"
          onClick={handleApplyFilters}
          icon={<SearchOutlined />}
        >
          搜索
        </Button>
      </Form.Item>

      <Form.Item>
        <Button
          onClick={handleResetFilters}
          icon={<ReloadOutlined />}
        >
          重置
        </Button>
      </Form.Item>
    </Form>
  );

  // 渲染统计卡片
  const renderStatisticCards = () => (
    <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
      <Col span={4}>
        <Card>
          <Statistic
            title="总任务数"
            value={taskStats.total}
            suffix="个"
          />
        </Card>
      </Col>
      <Col span={4}>
        <Card>
          <Statistic
            title="待处理"
            value={taskStats.pending}
            suffix="个"
            valueStyle={{ color: '#1890ff' }}
          />
        </Card>
      </Col>
      <Col span={4}>
        <Card>
          <Statistic
            title="进行中"
            value={taskStats.in_progress}
            suffix="个"
            valueStyle={{ color: '#52c41a' }}
          />
        </Card>
      </Col>
      <Col span={4}>
        <Card>
          <Statistic
            title="已完成"
            value={taskStats.completed}
            suffix="个"
            valueStyle={{ color: '#52c41a' }}
          />
        </Card>
      </Col>
      <Col span={4}>
        <Card>
          <Statistic
            title="已逾期"
            value={taskStats.overdue}
            suffix="个"
            valueStyle={{ color: '#f5222d' }}
          />
        </Card>
      </Col>
      <Col span={4}>
        <Card>
          <Statistic
            title="已取消"
            value={taskStats.cancelled}
            suffix="个"
            valueStyle={{ color: '#faad14' }}
          />
        </Card>
      </Col>
    </Row>
  );

  return (
    <div className="finance-store-tasks">
      <Tabs
        activeKey={activeTab}
        onChange={handleTabChange}
        items={[
          {
            key: "1",
            label: "任务列表",
            children: (
              <>
                <div style={{ marginBottom: 16 }}>
                  <FilterForm />
                </div>

                <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between' }}>
                  <div>
                    <Button
                      type="primary"
                      icon={<PlusOutlined />}
                      onClick={() => handleOpenTaskForm()}
                      style={{ marginRight: 8 }}
                    >
                      新建任务
                    </Button>
                    <Button
                      icon={<SyncOutlined />}
                      onClick={() => fetchTasks()}
                    >
                      刷新
                    </Button>
                  </div>
                </div>

                {taskStats.total > 0 && renderStatisticCards()}

                <Table
                  columns={columns}
                  dataSource={tasks}
                  rowKey="id"
                  pagination={pagination}
                  onChange={handleTableChange}
                  loading={loading}
                  scroll={{ x: 1200 }}
                />
              </>
            )
          },
          {
            key: "2",
            label: "门店统计",
            children: (
              <Table
                columns={storeStatsColumns}
                dataSource={storeStats}
                rowKey="store_id"
                pagination={false}
                loading={loading}
              />
            )
          }
        ]}
      />

      <Modal
        title={selectedTask ? '编辑任务' : '新建任务'}
        open={modalVisible}
        onOk={handleSubmitTask}
        onCancel={() => setModalVisible(false)}
        width={700}
        confirmLoading={loading}
      >
        <TaskForm />
      </Modal>
    </div>
  );
};

export default FinanceStoreTasks;
