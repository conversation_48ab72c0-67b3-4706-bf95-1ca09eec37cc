/**
 * 日期时间工具函数
 * 统一处理系统中的日期时间格式和时区
 */
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore';
import apiService from '../services/api';

// 加载插件
dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(customParseFormat);
dayjs.extend(isSameOrBefore);

// 默认配置
const DEFAULT_CONFIG = {
  timezone: 'Asia/Shanghai',
  dateFormat: 'YYYY-MM-DD',
  timeFormat: '24hour'
};

// 缓存系统设置
let systemSettings = null;

/**
 * 获取系统设置
 * @returns {Promise<Object>} 系统设置
 */
export const getSystemSettings = async () => {
  if (systemSettings) {
    return systemSettings;
  }

  try {
    // 检查是否有项目ID
    const projectId = localStorage.getItem('project_id');
    if (!projectId) {
      console.warn('未找到项目ID，使用默认设置');
      return DEFAULT_CONFIG;
    }

    const settings = await apiService.project.settings.getGeneral();
    systemSettings = {
      timezone: settings.timezone || DEFAULT_CONFIG.timezone,
      dateFormat: settings.dateFormat || DEFAULT_CONFIG.dateFormat,
      timeFormat: settings.timeFormat || DEFAULT_CONFIG.timeFormat
    };
    return systemSettings;
  } catch (error) {
    console.error('获取系统设置失败:', error);
    return DEFAULT_CONFIG;
  }
};

/**
 * 清除系统设置缓存
 */
export const clearSystemSettingsCache = () => {
  systemSettings = null;
};

/**
 * 格式化日期时间
 * @param {string|Date|dayjs} date 日期时间
 * @param {string} format 格式
 * @param {Object} settings 设置
 * @returns {string} 格式化后的日期时间
 */
export const formatDateTime = (date, format, settings = null) => {
  if (!date) return '';

  const config = settings || systemSettings || DEFAULT_CONFIG;
  let dayjsDate;

  // 处理不同类型的日期输入
  if (dayjs.isDayjs(date)) {
    // 如果已经是dayjs对象
    dayjsDate = date.clone();
  } else if (date instanceof Date) {
    // 如果是Date对象
    dayjsDate = dayjs(date);
  } else if (typeof date === 'string') {
    // 如果是字符串
    dayjsDate = dayjs(date);
  } else {
    // 其他情况
    dayjsDate = dayjs(date);
  }

  if (!dayjsDate.isValid()) {
    console.error('无效的日期:', date);
    return '';
  }

  // 使用 dayjs 的 timezone 插件处理时区
  // 确保日期是UTC格式，然后转换到目标时区
  if (typeof date === 'string' && date.includes('T') && date.includes('Z')) {
    // 如果是ISO格式的UTC时间，直接转换
    dayjsDate = dayjs(date).tz(config.timezone);
  } else if (typeof date === 'string' && date.includes('T')) {
    // 如果是ISO格式但没有Z，假设是UTC时间
    dayjsDate = dayjs(date).utc().tz(config.timezone);
  } else {
    // 其他情况，先转为UTC再转到目标时区
    dayjsDate = dayjsDate.tz(config.timezone);
  }

  // 如果没有指定格式，则使用系统设置的格式
  if (!format) {
    if (config.timeFormat === '12hour') {
      return dayjsDate.format(`${config.dateFormat} hh:mm:ss A`);
    } else {
      return dayjsDate.format(`${config.dateFormat} HH:mm:ss`);
    }
  }

  return dayjsDate.format(format);
};

/**
 * 格式化日期
 * @param {string|Date|dayjs} date 日期
 * @param {Object} settings 设置
 * @returns {string} 格式化后的日期
 */
export const formatDate = (date, settings = null) => {
  const config = settings || systemSettings || DEFAULT_CONFIG;
  return formatDateTime(date, config.dateFormat, config);
};

/**
 * 格式化时间
 * @param {string|Date|dayjs} time 时间
 * @param {Object} settings 设置
 * @returns {string} 格式化后的时间
 */
export const formatTime = (time, settings = null) => {
  const config = settings || systemSettings || DEFAULT_CONFIG;
  const format = config.timeFormat === '12hour' ? 'hh:mm:ss A' : 'HH:mm:ss';
  return formatDateTime(time, format, config);
};

/**
 * 获取当前日期时间
 * @param {string} format 格式
 * @param {Object} settings 设置
 * @returns {string} 当前日期时间
 */
export const getCurrentDateTime = (format = null, settings = null) => {
  return formatDateTime(new Date(), format, settings);
};

/**
 * 获取当前日期
 * @param {Object} settings 设置
 * @returns {string} 当前日期
 */
export const getCurrentDate = (settings = null) => {
  return formatDate(new Date(), settings);
};

/**
 * 获取当前时间
 * @param {Object} settings 设置
 * @returns {string} 当前时间
 */
export const getCurrentTime = (settings = null) => {
  return formatTime(new Date(), settings);
};

/**
 * 初始化日期时间工具
 * 预加载系统设置
 */
export const initDateTimeUtils = async () => {
  await getSystemSettings();
};

const dateTimeUtils = {
  formatDateTime,
  formatDate,
  formatTime,
  getCurrentDateTime,
  getCurrentDate,
  getCurrentTime,
  getSystemSettings,
  clearSystemSettingsCache,
  initDateTimeUtils
};

export default dateTimeUtils;
