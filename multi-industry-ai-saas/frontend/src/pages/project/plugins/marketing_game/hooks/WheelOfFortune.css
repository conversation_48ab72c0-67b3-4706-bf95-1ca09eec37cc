/* 幸运大转盘页面样式 */
.wheel-fortune-container {
  min-height: 100vh;
  padding: 30px;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.wheel-fortune-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.9);
  z-index: 0;
}

.wheel-fortune-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
}

.wheel-fortune-header {
  text-align: center;
  margin-bottom: 40px;
  position: relative;
  z-index: 1;
  animation: fadeIn 0.8s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-20px); }
  to { opacity: 1; transform: translateY(0); }
}

.game-logo {
  width: 100px;
  height: 100px;
  margin-bottom: 20px;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
}

.game-title {
  color: #e91e63;
  margin-bottom: 15px;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.2);
  font-weight: bold;
}

.game-description {
  max-width: 700px;
  margin: 0 auto;
  color: #333;
  font-size: 16px;
}

.wheel-fortune-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 1;
  width: 100%;
  max-width: 900px;
  animation: scaleIn 0.8s ease-in-out;
}

@keyframes scaleIn {
  from { opacity: 0; transform: scale(0.9); }
  to { opacity: 1; transform: scale(1); }
}

.wheel-card {
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
  padding: 40px;
  margin-bottom: 30px;
  width: 100%;
  max-width: 900px;
  display: flex;
  justify-content: center;
  border: none;
  overflow: visible;
}

/* 转盘样式 */
.wheel-container {
  position: relative;
  width: 500px;
  height: 500px;
  margin-bottom: 40px;
}

.wheel {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  position: relative;
  overflow: hidden;
  border: 15px solid #ff5722;
  box-shadow: 0 0 0 15px #ffeb3b, 0 0 60px rgba(0, 0, 0, 0.4);
  transition: transform 5s cubic-bezier(0.32, 0.64, 0.45, 1);
  background-color: #f5f5f5;
  animation: glow 2s infinite alternate;
  transform-origin: center;
  z-index: 1;
}

/* 添加外部椭圆装饰 */
.wheel::before {
  content: '';
  position: absolute;
  top: -40px;
  left: -40px;
  right: -40px;
  bottom: -40px;
  border-radius: 50%;
  border: 5px solid #FFA000;
  z-index: -1;
  box-shadow: 0 0 30px rgba(255, 160, 0, 0.5);
  pointer-events: none;
}

@keyframes glow {
  from { box-shadow: 0 0 0 15px #ffeb3b, 0 0 40px rgba(0, 0, 0, 0.3); }
  to { box-shadow: 0 0 0 15px #ffeb3b, 0 0 70px rgba(255, 87, 34, 0.7); }
}

.wheel-pointer {
  position: absolute;
  top: -40px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 30px solid transparent;
  border-right: 30px solid transparent;
  border-top: 60px solid #e91e63;
  z-index: 2;
  filter: drop-shadow(0 6px 8px rgba(0, 0, 0, 0.4));
}

.wheel-pointer::after {
  content: '';
  position: absolute;
  top: -60px;
  left: -12px;
  width: 24px;
  height: 24px;
  background-color: #fff;
  border-radius: 50%;
  border: 4px solid #e91e63;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.4);
}

.wheel-item {
  position: absolute;
  width: 50%;
  height: 50%;
  transform-origin: bottom right;
  left: 0;
  top: 0;
  color: white;
  overflow: hidden;
  box-sizing: border-box;
}

.wheel-item-content {
  position: absolute;
  left: -100%;
  width: 200%;
  height: 200%;
  transform-origin: 100% 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: 20px;
  box-sizing: border-box;
  transform: rotate(90deg);
}

.prize-image {
  width: 80px;
  height: 80px;
  margin-bottom: 15px;
  filter: drop-shadow(0 4px 6px rgba(0, 0, 0, 0.3));
  object-fit: contain;
}

.prize-name {
  font-size: 20px;
  font-weight: bold;
  text-align: center;
  white-space: nowrap;
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.5);
  transform: rotate(0deg); /* 修改为正常方向 */
  margin-top: 10px;
  max-width: 90%;
}

/* 按钮样式 */
.wheel-fortune-actions {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 30px;
  width: 100%;
}

.spin-button {
  font-size: 20px;
  height: 60px;
  width: 280px;
  margin-bottom: 30px;
  background-color: #e91e63;
  border-color: #e91e63;
  border-radius: 30px;
  box-shadow: 0 4px 12px rgba(233, 30, 99, 0.4);
  transition: all 0.3s ease;
}

.spin-button:hover, .spin-button:focus {
  background-color: #d81b60;
  border-color: #d81b60;
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(233, 30, 99, 0.5);
}

.spin-button:active {
  transform: translateY(1px);
  box-shadow: 0 2px 8px rgba(233, 30, 99, 0.4);
}

.action-buttons {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
  justify-content: center;
}

.action-buttons button {
  border-radius: 20px;
  height: 40px;
  padding: 0 20px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.action-buttons button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* 奖品结果样式 */
.prize-result {
  display: flex;
  align-items: center;
  margin: 25px 0;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.prize-result-image {
  width: 100px;
  height: 100px;
  margin-right: 25px;
  object-fit: contain;
  filter: drop-shadow(0 3px 5px rgba(0, 0, 0, 0.2));
}

.prize-result-info {
  flex: 1;
}

/* 响应式样式 */
@media (max-width: 576px) {
  .wheel-card {
    max-width: 100%;
    padding: 20px;
  }

  .wheel-container {
    width: 320px;
    height: 320px;
  }

  .wheel-pointer {
    top: -30px;
    border-left: 20px solid transparent;
    border-right: 20px solid transparent;
    border-top: 40px solid #e91e63;
  }

  .wheel-pointer::after {
    top: -40px;
    left: -8px;
    width: 16px;
    height: 16px;
  }

  .prize-image {
    width: 40px;
    height: 40px;
    margin-bottom: 8px;
  }

  .prize-name {
    font-size: 14px;
    margin-top: 5px;
  }

  .spin-button {
    width: 220px;
    height: 50px;
    font-size: 18px;
  }

  .action-buttons {
    flex-direction: column;
    width: 100%;
    max-width: 220px;
  }

  .action-buttons button {
    width: 100%;
  }

  .prize-result {
    flex-direction: column;
    text-align: center;
  }

  .prize-result-image {
    margin-right: 0;
    margin-bottom: 15px;
  }
}

@media (min-width: 577px) and (max-width: 991px) {
  .wheel-card {
    max-width: 800px;
    padding: 30px;
  }

  .wheel-container {
    width: 600px;
    height: 600px;
  }

  .prize-image {
    width: 60px;
    height: 60px;
  }

  .prize-name {
    font-size: 18px;
  }
}

@media (min-width: 992px) {
  .wheel-card {
    max-width: 900px;
  }

  .wheel-container {
    width: 800px;
    height: 800px;
  }

  .prize-image {
    width: 90px;
    height: 90px;
  }

  .prize-name {
    font-size: 22px;
  }
}
