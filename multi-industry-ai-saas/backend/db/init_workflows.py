import asyncio
import uuid
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import and_

from db.database import get_db
from models.workflow import Workflow, WorkflowNode, WorkflowTransition
from models.user import User
from models.project import Project

async def init_default_workflows():
    """初始化默认工作流"""
    print("开始初始化默认工作流...")
    
    # 获取数据库会话
    async for db in get_db():
        try:
            # 获取所有项目
            projects_query = select(Project)
            result = await db.execute(projects_query)
            projects = result.scalars().all()
            
            if not projects:
                print("没有找到项目，跳过工作流初始化")
                return
            
            # 获取系统管理员用户
            admin_query = select(User).where(User.is_system_admin == True).limit(1)
            result = await db.execute(admin_query)
            admin = result.scalar_one_or_none()
            
            if not admin:
                print("没有找到系统管理员用户，跳过工作流初始化")
                return
            
            # 为每个项目创建默认工作流
            for project in projects:
                await create_loss_workflow(db, project, admin)
                await create_purchase_workflow(db, project, admin)
                await create_inventory_check_workflow(db, project, admin)
            
            print("默认工作流初始化完成")
        except Exception as e:
            print(f"初始化默认工作流失败: {str(e)}")
            await db.rollback()
        finally:
            await db.close()

async def create_loss_workflow(db: AsyncSession, project: Project, admin: User):
    """创建报损工作流"""
    # 检查是否已存在
    query = select(Workflow).where(
        and_(
            Workflow.project_id == project.id,
            Workflow.type == "loss_report",
            Workflow.code == "default_loss_workflow"
        )
    )
    result = await db.execute(query)
    existing_workflow = result.scalar_one_or_none()
    
    if existing_workflow:
        print(f"项目 {project.name} 已存在默认报损工作流，跳过创建")
        return
    
    print(f"为项目 {project.name} 创建默认报损工作流")
    
    # 创建工作流
    workflow = Workflow(
        id=uuid.uuid4(),
        tenant_id=project.tenant_id,
        project_id=project.id,
        name="默认报损工作流",
        code="default_loss_workflow",
        description="门店发起报损申请，采购审核，财务汇总",
        type="loss_report",
        is_default=True,
        is_active=True,
        config={
            "notify_enabled": True,
            "notify_roles": ["purchase", "finance"],
            "notify_template": "门店 {{store_name}} 提交了产品 {{product_name}} 的报损申请，数量: {{quantity}}，金额: {{amount}}元，请及时审核"
        },
        created_by=admin.id,
        updated_by=admin.id
    )
    
    db.add(workflow)
    await db.flush()
    
    # 创建节点
    start_node = WorkflowNode(
        id=uuid.uuid4(),
        workflow_id=workflow.id,
        name="发起报损",
        code="start",
        description="门店发起报损申请",
        type="start",
        order=1,
        config={
            "roles": ["store"],
            "form_fields": [
                {"name": "store_id", "label": "门店", "type": "select", "required": True},
                {"name": "product_id", "label": "产品", "type": "select", "required": True},
                {"name": "quantity", "label": "数量", "type": "number", "required": True},
                {"name": "amount", "label": "金额", "type": "number", "required": True},
                {"name": "reason", "label": "原因", "type": "textarea", "required": True},
                {"name": "images", "label": "图片", "type": "upload", "required": False}
            ],
            "notify": {
                "enabled": True,
                "roles": ["purchase"],
                "title": "新报损申请通知",
                "template": "门店 {{store_name}} 提交了产品 {{product_name}} 的报损申请，数量: {{quantity}}，金额: {{amount}}元，请及时审核"
            }
        }
    )
    
    purchase_approval_node = WorkflowNode(
        id=uuid.uuid4(),
        workflow_id=workflow.id,
        name="采购审核",
        code="purchase_approval",
        description="采购部门审核报损申请",
        type="approval",
        order=2,
        config={
            "roles": ["purchase", "admin"],
            "form_fields": [
                {"name": "approval_result", "label": "审核结果", "type": "radio", "required": True, "options": [
                    {"label": "通过", "value": "approved"},
                    {"label": "拒绝", "value": "rejected"}
                ]},
                {"name": "comment", "label": "审核意见", "type": "textarea", "required": False}
            ],
            "notify": {
                "enabled": True,
                "roles": ["store", "finance"],
                "title": "报损申请审核结果通知",
                "template": "您的报损申请 {{product_name}} 已被{{approval_result == 'approved' ? '通过' : '拒绝'}}，审核意见: {{comment}}"
            }
        }
    )
    
    finance_summary_node = WorkflowNode(
        id=uuid.uuid4(),
        workflow_id=workflow.id,
        name="财务汇总",
        code="finance_summary",
        description="财务部门汇总报损记录",
        type="approval",
        order=3,
        config={
            "roles": ["finance", "admin"],
            "form_fields": [
                {"name": "summary_result", "label": "汇总结果", "type": "radio", "required": True, "options": [
                    {"label": "已汇总", "value": "summarized"},
                    {"label": "需补充", "value": "need_supplement"}
                ]},
                {"name": "comment", "label": "汇总意见", "type": "textarea", "required": False}
            ],
            "notify": {
                "enabled": True,
                "roles": ["purchase", "store"],
                "title": "报损申请财务汇总通知",
                "template": "报损申请 {{product_name}} 已被财务{{summary_result == 'summarized' ? '汇总' : '标记为需补充'}}，汇总意见: {{comment}}"
            }
        }
    )
    
    end_node = WorkflowNode(
        id=uuid.uuid4(),
        workflow_id=workflow.id,
        name="完成",
        code="end",
        description="报损流程完成",
        type="end",
        order=4,
        config={
            "notify": {
                "enabled": True,
                "roles": ["store", "purchase", "finance"],
                "title": "报损流程完成通知",
                "template": "报损申请 {{product_name}} 流程已完成"
            }
        }
    )
    
    db.add_all([start_node, purchase_approval_node, finance_summary_node, end_node])
    await db.flush()
    
    # 创建转换
    transitions = [
        WorkflowTransition(
            id=uuid.uuid4(),
            workflow_id=workflow.id,
            source_node_id=start_node.id,
            target_node_id=purchase_approval_node.id,
            name="提交审核",
            condition=None
        ),
        WorkflowTransition(
            id=uuid.uuid4(),
            workflow_id=workflow.id,
            source_node_id=purchase_approval_node.id,
            target_node_id=finance_summary_node.id,
            name="审核通过",
            condition={"approval_result": "approved"}
        ),
        WorkflowTransition(
            id=uuid.uuid4(),
            workflow_id=workflow.id,
            source_node_id=purchase_approval_node.id,
            target_node_id=end_node.id,
            name="审核拒绝",
            condition={"approval_result": "rejected"}
        ),
        WorkflowTransition(
            id=uuid.uuid4(),
            workflow_id=workflow.id,
            source_node_id=finance_summary_node.id,
            target_node_id=end_node.id,
            name="汇总完成",
            condition={"summary_result": "summarized"}
        ),
        WorkflowTransition(
            id=uuid.uuid4(),
            workflow_id=workflow.id,
            source_node_id=finance_summary_node.id,
            target_node_id=purchase_approval_node.id,
            name="需补充",
            condition={"summary_result": "need_supplement"}
        )
    ]
    
    db.add_all(transitions)
    await db.commit()
    
    print(f"项目 {project.name} 的默认报损工作流创建成功")

async def create_purchase_workflow(db: AsyncSession, project: Project, admin: User):
    """创建采购工作流"""
    # 检查是否已存在
    query = select(Workflow).where(
        and_(
            Workflow.project_id == project.id,
            Workflow.type == "purchase_order",
            Workflow.code == "default_purchase_workflow"
        )
    )
    result = await db.execute(query)
    existing_workflow = result.scalar_one_or_none()
    
    if existing_workflow:
        print(f"项目 {project.name} 已存在默认采购工作流，跳过创建")
        return
    
    print(f"为项目 {project.name} 创建默认采购工作流")
    
    # 创建工作流
    workflow = Workflow(
        id=uuid.uuid4(),
        tenant_id=project.tenant_id,
        project_id=project.id,
        name="默认采购工作流",
        code="default_purchase_workflow",
        description="采购发起采购申请，主管审核，财务审批，仓库验收",
        type="purchase_order",
        is_default=True,
        is_active=True,
        config={
            "notify_enabled": True,
            "notify_roles": ["purchase_manager", "finance", "warehouse"],
            "notify_template": "采购专员 {{user_name}} 提交了采购申请，供应商: {{supplier_name}}，总金额: {{total_amount}}元，请及时审核"
        },
        created_by=admin.id,
        updated_by=admin.id
    )
    
    db.add(workflow)
    await db.flush()
    
    # 创建节点
    start_node = WorkflowNode(
        id=uuid.uuid4(),
        workflow_id=workflow.id,
        name="发起采购",
        code="start",
        description="采购专员发起采购申请",
        type="start",
        order=1,
        config={
            "roles": ["purchase"],
            "form_fields": [
                {"name": "supplier_id", "label": "供应商", "type": "select", "required": True},
                {"name": "expected_delivery_date", "label": "预计交付日期", "type": "date", "required": True},
                {"name": "items", "label": "采购项目", "type": "table", "required": True, "columns": [
                    {"name": "product_id", "label": "产品", "type": "select", "required": True},
                    {"name": "quantity", "label": "数量", "type": "number", "required": True},
                    {"name": "price", "label": "单价", "type": "number", "required": True},
                    {"name": "amount", "label": "金额", "type": "number", "required": True}
                ]},
                {"name": "total_amount", "label": "总金额", "type": "number", "required": True},
                {"name": "remark", "label": "备注", "type": "textarea", "required": False}
            ],
            "notify": {
                "enabled": True,
                "roles": ["purchase_manager"],
                "title": "新采购申请通知",
                "template": "采购专员 {{user_name}} 提交了采购申请，供应商: {{supplier_name}}，总金额: {{total_amount}}元，请及时审核"
            }
        }
    )
    
    manager_approval_node = WorkflowNode(
        id=uuid.uuid4(),
        workflow_id=workflow.id,
        name="主管审核",
        code="manager_approval",
        description="采购主管审核采购申请",
        type="approval",
        order=2,
        config={
            "roles": ["purchase_manager", "admin"],
            "form_fields": [
                {"name": "approval_result", "label": "审核结果", "type": "radio", "required": True, "options": [
                    {"label": "通过", "value": "approved"},
                    {"label": "拒绝", "value": "rejected"}
                ]},
                {"name": "comment", "label": "审核意见", "type": "textarea", "required": False}
            ],
            "notify": {
                "enabled": True,
                "roles": ["purchase", "finance"],
                "title": "采购申请审核结果通知",
                "template": "采购申请已被{{approval_result == 'approved' ? '通过' : '拒绝'}}，审核意见: {{comment}}"
            }
        }
    )
    
    finance_approval_node = WorkflowNode(
        id=uuid.uuid4(),
        workflow_id=workflow.id,
        name="财务审批",
        code="finance_approval",
        description="财务部门审批采购申请",
        type="approval",
        order=3,
        config={
            "roles": ["finance", "admin"],
            "form_fields": [
                {"name": "approval_result", "label": "审批结果", "type": "radio", "required": True, "options": [
                    {"label": "通过", "value": "approved"},
                    {"label": "拒绝", "value": "rejected"}
                ]},
                {"name": "comment", "label": "审批意见", "type": "textarea", "required": False}
            ],
            "notify": {
                "enabled": True,
                "roles": ["purchase", "purchase_manager", "warehouse"],
                "title": "采购申请财务审批通知",
                "template": "采购申请已被财务{{approval_result == 'approved' ? '通过' : '拒绝'}}，审批意见: {{comment}}"
            }
        }
    )
    
    warehouse_receipt_node = WorkflowNode(
        id=uuid.uuid4(),
        workflow_id=workflow.id,
        name="仓库验收",
        code="warehouse_receipt",
        description="仓库验收采购商品",
        type="approval",
        order=4,
        config={
            "roles": ["warehouse", "admin"],
            "form_fields": [
                {"name": "receipt_result", "label": "验收结果", "type": "radio", "required": True, "options": [
                    {"label": "全部验收", "value": "all_received"},
                    {"label": "部分验收", "value": "partial_received"},
                    {"label": "拒收", "value": "rejected"}
                ]},
                {"name": "items", "label": "验收项目", "type": "table", "required": True, "columns": [
                    {"name": "product_id", "label": "产品", "type": "select", "required": True},
                    {"name": "expected_quantity", "label": "预期数量", "type": "number", "required": True},
                    {"name": "actual_quantity", "label": "实际数量", "type": "number", "required": True},
                    {"name": "is_qualified", "label": "是否合格", "type": "checkbox", "required": True}
                ]},
                {"name": "comment", "label": "验收意见", "type": "textarea", "required": False}
            ],
            "notify": {
                "enabled": True,
                "roles": ["purchase", "finance"],
                "title": "采购商品验收通知",
                "template": "采购商品已被仓库{{receipt_result == 'all_received' ? '全部验收' : receipt_result == 'partial_received' ? '部分验收' : '拒收'}}，验收意见: {{comment}}"
            }
        }
    )
    
    end_node = WorkflowNode(
        id=uuid.uuid4(),
        workflow_id=workflow.id,
        name="完成",
        code="end",
        description="采购流程完成",
        type="end",
        order=5,
        config={
            "notify": {
                "enabled": True,
                "roles": ["purchase", "purchase_manager", "finance", "warehouse"],
                "title": "采购流程完成通知",
                "template": "采购申请流程已完成"
            }
        }
    )
    
    db.add_all([start_node, manager_approval_node, finance_approval_node, warehouse_receipt_node, end_node])
    await db.flush()
    
    # 创建转换
    transitions = [
        WorkflowTransition(
            id=uuid.uuid4(),
            workflow_id=workflow.id,
            source_node_id=start_node.id,
            target_node_id=manager_approval_node.id,
            name="提交审核",
            condition=None
        ),
        WorkflowTransition(
            id=uuid.uuid4(),
            workflow_id=workflow.id,
            source_node_id=manager_approval_node.id,
            target_node_id=finance_approval_node.id,
            name="主管通过",
            condition={"approval_result": "approved"}
        ),
        WorkflowTransition(
            id=uuid.uuid4(),
            workflow_id=workflow.id,
            source_node_id=manager_approval_node.id,
            target_node_id=end_node.id,
            name="主管拒绝",
            condition={"approval_result": "rejected"}
        ),
        WorkflowTransition(
            id=uuid.uuid4(),
            workflow_id=workflow.id,
            source_node_id=finance_approval_node.id,
            target_node_id=warehouse_receipt_node.id,
            name="财务通过",
            condition={"approval_result": "approved"}
        ),
        WorkflowTransition(
            id=uuid.uuid4(),
            workflow_id=workflow.id,
            source_node_id=finance_approval_node.id,
            target_node_id=end_node.id,
            name="财务拒绝",
            condition={"approval_result": "rejected"}
        ),
        WorkflowTransition(
            id=uuid.uuid4(),
            workflow_id=workflow.id,
            source_node_id=warehouse_receipt_node.id,
            target_node_id=end_node.id,
            name="验收完成",
            condition=None
        )
    ]
    
    db.add_all(transitions)
    await db.commit()
    
    print(f"项目 {project.name} 的默认采购工作流创建成功")

async def create_inventory_check_workflow(db: AsyncSession, project: Project, admin: User):
    """创建库存盘点工作流"""
    # 检查是否已存在
    query = select(Workflow).where(
        and_(
            Workflow.project_id == project.id,
            Workflow.type == "inventory_check",
            Workflow.code == "default_inventory_check_workflow"
        )
    )
    result = await db.execute(query)
    existing_workflow = result.scalar_one_or_none()
    
    if existing_workflow:
        print(f"项目 {project.name} 已存在默认库存盘点工作流，跳过创建")
        return
    
    print(f"为项目 {project.name} 创建默认库存盘点工作流")
    
    # 创建工作流
    workflow = Workflow(
        id=uuid.uuid4(),
        tenant_id=project.tenant_id,
        project_id=project.id,
        name="默认库存盘点工作流",
        code="default_inventory_check_workflow",
        description="仓库发起盘点，主管审核，财务确认",
        type="inventory_check",
        is_default=True,
        is_active=True,
        config={
            "notify_enabled": True,
            "notify_roles": ["warehouse_manager", "finance"],
            "notify_template": "仓库 {{warehouse_name}} 提交了库存盘点申请，差异数量: {{difference_count}}，差异金额: {{difference_amount}}元，请及时审核"
        },
        created_by=admin.id,
        updated_by=admin.id
    )
    
    db.add(workflow)
    await db.flush()
    
    # 创建节点
    start_node = WorkflowNode(
        id=uuid.uuid4(),
        workflow_id=workflow.id,
        name="发起盘点",
        code="start",
        description="仓库发起库存盘点",
        type="start",
        order=1,
        config={
            "roles": ["warehouse"],
            "form_fields": [
                {"name": "warehouse_id", "label": "仓库", "type": "select", "required": True},
                {"name": "check_date", "label": "盘点日期", "type": "date", "required": True},
                {"name": "items", "label": "盘点项目", "type": "table", "required": True, "columns": [
                    {"name": "product_id", "label": "产品", "type": "select", "required": True},
                    {"name": "system_quantity", "label": "系统数量", "type": "number", "required": True},
                    {"name": "actual_quantity", "label": "实际数量", "type": "number", "required": True},
                    {"name": "difference", "label": "差异", "type": "number", "required": True}
                ]},
                {"name": "difference_count", "label": "差异数量", "type": "number", "required": True},
                {"name": "difference_amount", "label": "差异金额", "type": "number", "required": True},
                {"name": "remark", "label": "备注", "type": "textarea", "required": False}
            ],
            "notify": {
                "enabled": True,
                "roles": ["warehouse_manager"],
                "title": "新库存盘点通知",
                "template": "仓库 {{warehouse_name}} 提交了库存盘点申请，差异数量: {{difference_count}}，差异金额: {{difference_amount}}元，请及时审核"
            }
        }
    )
    
    manager_approval_node = WorkflowNode(
        id=uuid.uuid4(),
        workflow_id=workflow.id,
        name="主管审核",
        code="manager_approval",
        description="仓库主管审核盘点结果",
        type="approval",
        order=2,
        config={
            "roles": ["warehouse_manager", "admin"],
            "form_fields": [
                {"name": "approval_result", "label": "审核结果", "type": "radio", "required": True, "options": [
                    {"label": "通过", "value": "approved"},
                    {"label": "拒绝", "value": "rejected"}
                ]},
                {"name": "comment", "label": "审核意见", "type": "textarea", "required": False}
            ],
            "notify": {
                "enabled": True,
                "roles": ["warehouse", "finance"],
                "title": "库存盘点审核结果通知",
                "template": "库存盘点已被{{approval_result == 'approved' ? '通过' : '拒绝'}}，审核意见: {{comment}}"
            }
        }
    )
    
    finance_confirmation_node = WorkflowNode(
        id=uuid.uuid4(),
        workflow_id=workflow.id,
        name="财务确认",
        code="finance_confirmation",
        description="财务确认盘点结果",
        type="approval",
        order=3,
        config={
            "roles": ["finance", "admin"],
            "form_fields": [
                {"name": "confirmation_result", "label": "确认结果", "type": "radio", "required": True, "options": [
                    {"label": "确认", "value": "confirmed"},
                    {"label": "退回", "value": "returned"}
                ]},
                {"name": "comment", "label": "确认意见", "type": "textarea", "required": False}
            ],
            "notify": {
                "enabled": True,
                "roles": ["warehouse", "warehouse_manager"],
                "title": "库存盘点财务确认通知",
                "template": "库存盘点已被财务{{confirmation_result == 'confirmed' ? '确认' : '退回'}}，确认意见: {{comment}}"
            }
        }
    )
    
    end_node = WorkflowNode(
        id=uuid.uuid4(),
        workflow_id=workflow.id,
        name="完成",
        code="end",
        description="盘点流程完成",
        type="end",
        order=4,
        config={
            "notify": {
                "enabled": True,
                "roles": ["warehouse", "warehouse_manager", "finance"],
                "title": "库存盘点流程完成通知",
                "template": "库存盘点流程已完成"
            }
        }
    )
    
    db.add_all([start_node, manager_approval_node, finance_confirmation_node, end_node])
    await db.flush()
    
    # 创建转换
    transitions = [
        WorkflowTransition(
            id=uuid.uuid4(),
            workflow_id=workflow.id,
            source_node_id=start_node.id,
            target_node_id=manager_approval_node.id,
            name="提交审核",
            condition=None
        ),
        WorkflowTransition(
            id=uuid.uuid4(),
            workflow_id=workflow.id,
            source_node_id=manager_approval_node.id,
            target_node_id=finance_confirmation_node.id,
            name="主管通过",
            condition={"approval_result": "approved"}
        ),
        WorkflowTransition(
            id=uuid.uuid4(),
            workflow_id=workflow.id,
            source_node_id=manager_approval_node.id,
            target_node_id=end_node.id,
            name="主管拒绝",
            condition={"approval_result": "rejected"}
        ),
        WorkflowTransition(
            id=uuid.uuid4(),
            workflow_id=workflow.id,
            source_node_id=finance_confirmation_node.id,
            target_node_id=end_node.id,
            name="财务确认",
            condition={"confirmation_result": "confirmed"}
        ),
        WorkflowTransition(
            id=uuid.uuid4(),
            workflow_id=workflow.id,
            source_node_id=finance_confirmation_node.id,
            target_node_id=manager_approval_node.id,
            name="财务退回",
            condition={"confirmation_result": "returned"}
        )
    ]
    
    db.add_all(transitions)
    await db.commit()
    
    print(f"项目 {project.name} 的默认库存盘点工作流创建成功")

if __name__ == "__main__":
    asyncio.run(init_default_workflows())
