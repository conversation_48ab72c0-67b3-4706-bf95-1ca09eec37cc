<?xml version="1.0" encoding="UTF-8"?>
<svg width="400px" height="300px" viewBox="0 0 400 300" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>Creating Project</title>
    <defs>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-1">
            <stop stop-color="#F5F7FA" offset="0%"></stop>
            <stop stop-color="#E4E7EB" offset="100%"></stop>
        </linearGradient>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-2">
            <stop stop-color="#1890FF" offset="0%"></stop>
            <stop stop-color="#096DD9" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="Creating-Project" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <rect id="Background" fill="url(#linearGradient-1)" x="0" y="0" width="400" height="300" rx="8"></rect>
        <g id="Person" transform="translate(220.000, 150.000)">
            <path d="M40,120 C62.09,120 80,102.09 80,80 C80,57.91 62.09,40 40,40 C17.91,40 0,57.91 0,80 C0,102.09 17.91,120 40,120 Z" id="Body" fill="#FFB74D"></path>
            <path d="M40,40 C51.05,40 60,31.05 60,20 C60,8.95 51.05,0 40,0 C28.95,0 20,8.95 20,20 C20,31.05 28.95,40 40,40 Z" id="Head" fill="#FFB74D"></path>
            <path d="M30,25 C32.76,25 35,22.76 35,20 C35,17.24 32.76,15 30,15 C27.24,15 25,17.24 25,20 C25,22.76 27.24,25 30,25 Z" id="Eye-Left" fill="#FFFFFF"></path>
            <path d="M50,25 C52.76,25 55,22.76 55,20 C55,17.24 52.76,15 50,15 C47.24,15 45,17.24 45,20 C45,22.76 47.24,25 50,25 Z" id="Eye-Right" fill="#FFFFFF"></path>
            <path d="M40,30 C44.42,30 48,26.42 48,22 L32,22 C32,26.42 35.58,30 40,30 Z" id="Smile" fill="#FFFFFF"></path>
            <rect id="Arm-Left" fill="#FFB74D" x="0" y="60" width="20" height="40" rx="10"></rect>
            <rect id="Arm-Right" fill="#FFB74D" x="60" y="60" width="20" height="40" rx="10"></rect>
        </g>
        <g id="Charts" transform="translate(100.000, 100.000)">
            <rect id="Chart-Background" fill="#FFFFFF" x="0" y="0" width="120" height="80" rx="4"></rect>
            <rect id="Bar-1" fill="#1890FF" x="20" y="20" width="10" height="40" rx="2"></rect>
            <rect id="Bar-2" fill="#52C41A" x="40" y="30" width="10" height="30" rx="2"></rect>
            <rect id="Bar-3" fill="#FAAD14" x="60" y="10" width="10" height="50" rx="2"></rect>
            <rect id="Bar-4" fill="#F5222D" x="80" y="25" width="10" height="35" rx="2"></rect>
        </g>
        <g id="Document" transform="translate(180.000, 100.000)">
            <rect id="Document-Background" fill="#FFFFFF" x="0" y="0" width="80" height="100" rx="4"></rect>
            <rect id="Line-1" fill="#E6F7FF" x="10" y="20" width="60" height="5" rx="2.5"></rect>
            <rect id="Line-2" fill="#E6F7FF" x="10" y="35" width="60" height="5" rx="2.5"></rect>
            <rect id="Line-3" fill="#E6F7FF" x="10" y="50" width="60" height="5" rx="2.5"></rect>
            <rect id="Line-4" fill="#E6F7FF" x="10" y="65" width="40" height="5" rx="2.5"></rect>
        </g>
        <g id="Cloud" transform="translate(50.000, 50.000)">
            <path d="M60,80 C82.09,80 100,62.09 100,40 C100,17.91 82.09,0 60,0 C37.91,0 20,17.91 20,40 C20,40.34 20.01,40.67 20.02,41 C8.4,41.47 -0.06,51.24 0,63 C0.06,75.15 9.85,85 22,85 L60,85 L60,80 Z" id="Cloud-Shape" fill="#FFFFFF"></path>
            <path d="M60,40 L80,60 L60,80 L40,60 L60,40 Z" id="Diamond" fill="url(#linearGradient-2)"></path>
        </g>
        <g id="Trees" transform="translate(300.000, 200.000)">
            <path d="M20,40 C31.05,40 40,31.05 40,20 C40,8.95 31.05,0 20,0 C8.95,0 0,8.95 0,20 C0,31.05 8.95,40 20,40 Z" id="Tree-Top-1" fill="#52C41A"></path>
            <rect id="Tree-Trunk-1" fill="#8B4513" x="15" y="40" width="10" height="20" rx="2"></rect>
            <path d="M70,30 C78.28,30 85,23.28 85,15 C85,6.72 78.28,0 70,0 C61.72,0 55,6.72 55,15 C55,23.28 61.72,30 70,30 Z" id="Tree-Top-2" fill="#52C41A"></path>
            <rect id="Tree-Trunk-2" fill="#8B4513" x="65" y="30" width="10" height="15" rx="2"></rect>
        </g>
    </g>
</svg>
