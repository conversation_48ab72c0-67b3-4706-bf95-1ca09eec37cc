#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
AI 客服引擎

集成项目现有的 AI 助理系统，提供智能对话、多模态处理、工具调用等功能。
"""

import logging
import uuid
import asyncio
import math
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from services.ai.assistant_service import AIAssistantService
from services.ai.chat_service import AIChatService
from services.ai.knowledge_service import AIKnowledgeService
from schemas.ai.assistant import AIAssistantChatRequest
from .platform_adapter import PlatformMessage, PlatformResponse

logger = logging.getLogger(__name__)

def safe_round(value: float, decimals: int = 2) -> float:
    """安全的四舍五入函数，处理NaN和无穷大"""
    if value is None:
        return 0.0
    
    # 处理字符串和其他非数值类型
    try:
        value = float(value)
    except (ValueError, TypeError):
        return 0.5
    
    if math.isnan(value) or math.isinf(value):
        return 0.5  # 返回中性值
    
    try:
        return round(float(value), decimals)
    except (ValueError, TypeError):
        return 0.5

class AICustomerServiceEngine:
    """AI 客服引擎"""
    
    def __init__(self):
        self.default_assistant_id = None
        self.default_model_id = None
        self.default_config_id = None
        self.session_threads: Dict[str, uuid.UUID] = {}  # 会话ID -> 线程ID映射
        self.ai_configs = {}  # 存储AI模型配置
        self.project_config = {}  # 存储项目AI客服配置
        
    async def initialize(self, project_id: Optional[uuid.UUID] = None, db: Optional[AsyncSession] = None):
        """初始化 AI 引擎"""
        try:
            logger.info("正在初始化 AI 客服引擎...")
            
            # 如果提供了项目ID和数据库连接，加载项目配置
            if project_id and db:
                await self._load_project_ai_config(db, project_id)
            
            logger.info("AI 客服引擎初始化完成")
            
        except Exception as e:
            logger.error(f"AI 客服引擎初始化失败: {e}")
            raise
            
    async def _load_project_ai_config(self, db: AsyncSession, project_id: uuid.UUID):
        """加载项目AI配置"""
        try:
            # 直接从数据库读取AI客服插件配置
            from sqlalchemy import select
            from ..models.ai_integration import AICustomerServiceIntegration
            
            query = select(AICustomerServiceIntegration).where(
                AICustomerServiceIntegration.project_id == project_id
            )
            result = await db.execute(query)
            integration_record = result.scalar_one_or_none()
            
            config = None
            if integration_record:
                config = {
                    'ai_model_id': integration_record.ai_model_id,
                    'vision_model_id': integration_record.vision_model_id,
                    'audio_model_id': integration_record.audio_model_id,
                    'embedding_model_id': integration_record.embedding_model_id,
                    'temperature': integration_record.temperature or 0.7,
                    'max_tokens': integration_record.max_tokens or 2000,
                    'enable_knowledge_search': integration_record.enable_knowledge_search,
                    'enable_mcp_tools': integration_record.enable_mcp_tools,
                    'knowledge_base_ids': integration_record.knowledge_base_ids or [],
                    'mcp_server_ids': integration_record.mcp_server_ids or [],  # 添加缺失的字段
                    'mcp_tool_ids': integration_record.mcp_tool_ids or []
                }
                
                # 添加详细的调试日志
                logger.info(f"AI客服配置详情: enable_knowledge_search={config.get('enable_knowledge_search')}, "
                          f"knowledge_base_ids={config.get('knowledge_base_ids')}, "
                          f"kb_count={len(config.get('knowledge_base_ids', []))}")
            
            if config:
                self.project_config = {
                    'chat_model_id': config.get('ai_model_id'),
                    'vision_model_id': config.get('vision_model_id'),
                    'audio_model_id': config.get('audio_model_id'),
                    'embedding_model_id': config.get('embedding_model_id'),
                    'temperature': config.get('temperature', 0.7),
                    'max_tokens': config.get('max_tokens', 2000),
                    'enable_knowledge_search': config.get('enable_knowledge_search', True),
                    'enable_mcp_tools': config.get('enable_mcp_tools', True),
                    'knowledge_base_ids': config.get('knowledge_base_ids', []),
                    'mcp_server_ids': config.get('mcp_server_ids', []),  # 添加缺失的字段
                    'mcp_tool_ids': config.get('mcp_tool_ids', [])
                }
                logger.info(f"已加载项目AI配置: 聊天模型={self.project_config.get('chat_model_id')}, "
                          f"视觉模型={self.project_config.get('vision_model_id')}, "
                          f"语音模型={self.project_config.get('audio_model_id')}, "
                          f"知识库搜索={self.project_config.get('enable_knowledge_search')}, "
                          f"知识库数量={len(self.project_config.get('knowledge_base_ids', []))}, "
                          f"MCP工具启用={self.project_config.get('enable_mcp_tools')}, "
                          f"MCP服务器数量={len(self.project_config.get('mcp_server_ids', []))}")
            else:
                logger.warning(f"项目 {project_id} 未找到AI客服配置，将使用默认配置")
                
        except Exception as e:
            logger.error(f"加载项目AI配置失败: {e}")
            # 使用默认配置
            self.project_config = {
                'temperature': 0.7,
                'max_tokens': 2000,
                'enable_knowledge_search': True,
                'enable_mcp_tools': True
            }
            
    async def shutdown(self):
        """关闭 AI 引擎"""
        logger.info("AI 客服引擎已关闭")
        
    async def process_message(
        self,
        db: AsyncSession,
        message: PlatformMessage,
        session_id: str,
        project_id: uuid.UUID,
        user_id: Optional[uuid.UUID] = None,
        assistant_id: Optional[uuid.UUID] = None,
        use_knowledge_base: bool = True,
        knowledge_base_ids: Optional[List[str]] = None
    ) -> Tuple[PlatformResponse, Dict[str, Any]]:
        """
        处理用户消息并生成AI回复
        
        Args:
            db: 数据库会话
            message: 平台消息
            session_id: 会话ID
            project_id: 项目ID
            user_id: 用户ID
            assistant_id: AI助手ID
            use_knowledge_base: 是否使用知识库
            knowledge_base_ids: 知识库ID列表
            
        Returns:
            Tuple[PlatformResponse, Dict[str, Any]]: AI回复和处理元数据
        """
        try:
            start_time = datetime.utcnow()
            
            # 获取或创建对话线程
            thread_id = await self._get_or_create_thread(
                db, session_id, project_id, user_id
            )
            
            # 分析消息意图和实体
            intent_analysis = await self.analyze_message_intent(message.content)
            intent = intent_analysis.get("primary_intent", "general_inquiry")
            entities = intent_analysis.get("entities", {})
            
            # 构建用户上下文
            user_context = {
                "project_id": project_id,
                "user_id": user_id,
                "user_role": "customer",  # 可以从用户信息中获取
                "permissions": []  # 可以从用户权限中获取
            }
            
            # 如果消息元数据中包含assistant_prompt，添加到用户上下文
            if message.meta_data and message.meta_data.get("assistant_prompt"):
                user_context["assistant_prompt"] = message.meta_data["assistant_prompt"]
            
            # 尝试使用真实AI模型生成回复
            ai_response_content = None
            model_used = "AI客服引擎 v2025.5"
            knowledge_sources = []
            tools_used = []
            rag_used = False
            
            try:
                # 如果配置了AI模型，使用插件配置的模型
                if self.project_config.get('chat_model_id'):
                    ai_response_content, model_used, knowledge_sources, tools_used, rag_used = await self._generate_ai_response_with_plugin_config(
                        db=db,
                        message=message.content,
                        project_id=project_id,
                        user_id=user_id,
                        intent=intent,
                        entities=entities,
                        user_context=user_context
                    )
            except Exception as ai_error:
                logger.warning(f"插件AI模型调用失败，使用内置智能回复: {ai_error}")
                logger.debug(f"AI错误详情: {ai_error}", exc_info=True)
            
            # 如果AI模型调用失败或未配置，使用内置智能回复
            if not ai_response_content:
                ai_response_content = await self._generate_intelligent_response(
                    intent=intent,
                    entities=entities,
                    message=message.content,
                    conversation_history=[],  # 可以传入会话历史
                    user_context=user_context
                )
            
            # 计算处理时间
            processing_time = (datetime.utcnow() - start_time).total_seconds()
            
            # 构建平台响应
            platform_response = PlatformResponse(
                message_type="text",
                content=ai_response_content,
                attachments=[],
                meta_data={
                    "ai_response": True,
                    "processing_time": processing_time,
                    "model_used": model_used,
                    "thread_id": str(thread_id),
                    "intent": intent,
                    "entities": entities
                }
            )
            
            # 处理元数据
            processing_metadata = {
                "processing_time": processing_time,
                "model_used": model_used,
                "confidence_score": 0.85,
                "knowledge_sources": knowledge_sources,
                "rag_used": rag_used,
                "thread_id": str(thread_id),
                "tools_used": tools_used,
                "intent": intent,
                "entities": entities
            }
            
            return platform_response, processing_metadata
            
        except Exception as e:
            logger.error(f"AI消息处理失败: {e}")
            
            # 返回错误响应
            error_response = PlatformResponse(
                message_type="text",
                content="抱歉，系统暂时繁忙，请稍后再试。",
                attachments=[],
                meta_data={"error": True, "error_message": str(e)}
            )
            
            error_metadata = {
                "error": True,
                "error_message": str(e),
                "processing_time": 0.0
            }
            
            return error_response, error_metadata
            
    async def _get_or_create_thread(
        self,
        db: AsyncSession,
        session_id: str,
        project_id: uuid.UUID,
        user_id: Optional[uuid.UUID]
    ) -> uuid.UUID:
        """获取或创建对话线程"""
        try:
            # 检查是否已有线程
            if session_id in self.session_threads:
                return self.session_threads[session_id]
                
            # 创建新线程
            # 这里应该调用AI助手服务创建线程
            # 暂时生成一个UUID作为线程ID
            thread_id = uuid.uuid4()
            
            # 缓存线程ID
            self.session_threads[session_id] = thread_id
            
            logger.debug(f"为会话 {session_id} 创建新线程 {thread_id}")
            
            return thread_id
            
        except Exception as e:
            logger.error(f"创建对话线程失败: {e}")
            # 返回一个临时线程ID
            return uuid.uuid4()
    
    async def _generate_ai_response_with_plugin_config(
        self,
        db: AsyncSession,
        message: str,
        project_id: uuid.UUID,
        user_id: Optional[uuid.UUID],
        intent: str,
        entities: Dict[str, Any],
        user_context: Dict[str, Any]
    ) -> Tuple[str, str, List[Dict], List[str], bool]:
        """使用插件配置的AI模型生成回复"""
        try:
            chat_model_id = self.project_config.get('chat_model_id')
            vision_model_id = self.project_config.get('vision_model_id')
            
            logger.info(f"使用插件配置的AI模型生成回复: chat_model={chat_model_id}, vision_model={vision_model_id}, intent={intent}")
            
            # 根据消息类型选择合适的模型
            model_id = chat_model_id
            if entities.get('has_image') and vision_model_id:
                model_id = vision_model_id
                logger.info(f"检测到图像，使用视觉模型: {vision_model_id}")
            
            # 确保model_id不为空
            if not model_id:
                logger.warning("未配置AI模型ID，无法使用插件配置的模型")
                return None, None, [], [], False
            
            # 从AI配置服务获取模型配置
            from services.ai.config_service import AIConfigService
            
            # 安全处理UUID转换
            try:
                if isinstance(model_id, str):
                    model_uuid = uuid.UUID(model_id)
                elif isinstance(model_id, uuid.UUID):
                    model_uuid = model_id
                else:
                    logger.warning(f"无效的模型ID类型: {type(model_id)}")
                    return None, None, [], [], False
            except ValueError as e:
                logger.warning(f"无效的UUID格式: {model_id}, 错误: {e}")
                return None, None, [], [], False
            
            ai_config = await AIConfigService.get_config(db, model_uuid)
            
            if not ai_config:
                logger.warning(f"未找到AI配置: {model_id}")
                return None, None, [], [], False
            
            # 构建聊天请求，使用插件的温度和token配置
            from services.ai.chat_service import AIChatService
            
            chat_service = AIChatService()
            
            # 构建上下文信息
            context = {
                'intent': intent,
                'entities': entities,
                'platform': 'ai_customer_service',
                'user_role': user_context.get('user_role', 'customer'),
                'project_id': str(project_id),
                'plugin_config': self.project_config  # 传递插件配置
            }
            
            # 先尝试搜索知识库（如果启用）
            knowledge_sources = []
            rag_used = False
            enhanced_system_prompt = self._build_customer_service_system_prompt(intent, entities, user_context)
            
            if self.project_config.get('enable_knowledge_search', True) and self.project_config.get('knowledge_base_ids'):
                try:
                    # 从配置中获取知识库ID列表
                    kb_ids = self.project_config.get('knowledge_base_ids', [])
                    logger.info(f"开始知识库搜索，配置的知识库IDs: {kb_ids}")
                    
                    if isinstance(kb_ids, str):
                        # 如果是JSON字符串，尝试解析
                        import json
                        try:
                            kb_ids = json.loads(kb_ids)
                            logger.info(f"解析JSON后的知识库IDs: {kb_ids}")
                        except:
                            kb_ids = [kb_ids]  # 作为单个ID处理
                            logger.info(f"作为单个ID处理: {kb_ids}")
                    
                    # 确保kb_ids是UUID列表
                    uuid_kb_ids = []
                    for kb_id in kb_ids:
                        try:
                            if isinstance(kb_id, str):
                                uuid_kb_ids.append(uuid.UUID(kb_id))
                            elif isinstance(kb_id, uuid.UUID):
                                uuid_kb_ids.append(kb_id)
                        except ValueError:
                            logger.warning(f"无效的知识库ID: {kb_id}")
                    
                    logger.info(f"转换后的UUID知识库IDs: {uuid_kb_ids}")
                    
                    if uuid_kb_ids:
                        # 直接使用知识库服务搜索，传递knowledge_base_ids参数
                        from services.ai.knowledge_service import AIKnowledgeService
                        
                        logger.info(f"开始搜索知识库，查询: '{message}', 项目ID: {project_id}")
                        
                        search_results = await AIKnowledgeService.search_knowledge(
                            db=db,
                            query=message,
                            project_id=project_id,
                            knowledge_base_ids=uuid_kb_ids,  # 传递具体的知识库ID列表
                            top_k=5,
                            similarity_threshold=0.6,  # 降低阈值以获得更多结果
                            user_id=user_id,
                            include_metadata=True
                        )
                        
                        logger.info(f"知识库搜索完成，结果数量: {len(search_results) if search_results else 0}")
                        
                        if search_results:
                            knowledge_sources = search_results
                            rag_used = True
                            # 增强系统提示词，包含知识库信息
                            kb_context = self._build_knowledge_context(knowledge_sources)
                            if kb_context:
                                enhanced_system_prompt += f"\n\n**知识库信息：**\n{kb_context}\n\n请优先基于以上知识库信息回答用户问题。如果知识库信息不足以回答问题，请明确说明。"
                                logger.info(f"知识库搜索成功，找到 {len(knowledge_sources)} 条相关信息，已增强系统提示词")
                            else:
                                logger.warning("知识库搜索有结果但无法构建上下文")
                        else:
                            logger.info("知识库搜索无相关结果")
                    else:
                        logger.warning("没有有效的知识库ID")
                        
                except Exception as kb_error:
                    logger.error(f"知识库搜索失败: {kb_error}", exc_info=True)
            else:
                logger.info(f"知识库搜索未启用或未配置知识库，enable_search={self.project_config.get('enable_knowledge_search')}, kb_ids={self.project_config.get('knowledge_base_ids')}")
            
            # 获取MCP工具（如果配置了）
            available_tools = []
            if self.project_config.get('enable_mcp_tools', False) and self.project_config.get('mcp_server_ids'):
                try:
                    # 获取MCP服务器工具
                    from services.ai.assistant_service import AIAssistantService
                    
                    mcp_server_ids = self.project_config.get('mcp_server_ids', [])
                    if isinstance(mcp_server_ids, str):
                        import json
                        try:
                            mcp_server_ids = json.loads(mcp_server_ids)
                        except:
                            mcp_server_ids = [mcp_server_ids]
                    
                    # 转换为UUID列表
                    uuid_mcp_server_ids = []
                    for server_id in mcp_server_ids:
                        try:
                            if isinstance(server_id, str):
                                uuid_mcp_server_ids.append(uuid.UUID(server_id))
                            elif isinstance(server_id, uuid.UUID):
                                uuid_mcp_server_ids.append(server_id)
                        except ValueError:
                            logger.warning(f"无效的MCP服务器ID: {server_id}")
                    
                    if uuid_mcp_server_ids:
                        mcp_tools = await AIAssistantService.get_mcp_tools_from_servers(
                            db, project_id, uuid_mcp_server_ids
                        )
                        available_tools.extend(mcp_tools)
                        logger.info(f"AI客服获取到 {len(available_tools)} 个MCP工具")
                    
                except Exception as mcp_error:
                    logger.warning(f"获取MCP工具失败: {mcp_error}")
            
            # 调用AI聊天服务（使用chat_completion方法）
            start_time = datetime.utcnow()
            
            # 构建消息列表，包含增强的系统提示词
            messages = []
            if enhanced_system_prompt:
                messages.append({"role": "system", "content": enhanced_system_prompt})
            messages.append({"role": "user", "content": message})
            
            # 构建聊天参数
            chat_params = {
                "db": db,
                "project_id": project_id,
                "user_id": user_id,
                "messages": messages,
                "config_id": model_uuid,
                "temperature": self.project_config.get('temperature', 0.7),
                "max_tokens": self.project_config.get('max_tokens', 2000),
                "is_shared": False  # 确保记录使用统计
            }
            
            # 如果有可用工具，添加到请求中
            if available_tools:
                chat_params["tools"] = available_tools
                chat_params["tool_choice"] = "auto"
                logger.info(f"AI客服启用工具调用，工具数量: {len(available_tools)}")
            
            response = await chat_service.chat_completion(**chat_params)
            
            # 检查是否有工具调用
            tool_calls = []
            if "choices" in response and response["choices"]:
                message = response["choices"][0].get("message", {})
                if "tool_calls" in message:
                    tool_calls = message["tool_calls"]
            
            # 处理工具调用
            if tool_calls:
                tool_results = []
                for tool_call in tool_calls:
                    function_name = tool_call["function"]["name"]
                    function_args = json.loads(tool_call["function"]["arguments"])
                    
                    # 查找对应的MCP服务器工具
                    tool_result = None
                    for tool_spec in available_tools:
                        if (tool_spec["function"]["name"] == function_name and 
                            "_mcp_server_id" in tool_spec):
                            try:
                                from services.ai.assistant_service import AIAssistantService
                                server_id = uuid.UUID(tool_spec["_mcp_server_id"])
                                tool_result = await AIAssistantService.execute_mcp_tool(
                                    db=db,
                                    server_id=server_id,
                                    tool_name=function_name,
                                    parameters=function_args,
                                    user_id=user_id
                                )
                                break
                            except Exception as e:
                                logger.error(f"执行MCP工具失败: {e}")
                                tool_result = {"success": False, "error": str(e)}
                                break
                    
                    # 构建工具结果
                    if tool_result:
                        if tool_result.get("success"):
                            content = json.dumps(tool_result.get("data", {}))
                        else:
                            content = f"工具执行失败: {tool_result.get('error', '未知错误')}"
                    else:
                        content = f"未找到工具: {function_name}"
                    
                    tool_results.append({
                        "tool_call_id": tool_call["id"],
                        "role": "tool",
                        "name": function_name,
                        "content": content
                    })
                
                # 如果有工具调用结果，需要再次调用AI获取最终回复
                if tool_results:
                    # 添加工具调用消息到对话历史
                    messages.append(response["choices"][0]["message"])
                    messages.extend(tool_results)
                    
                    # 重新构建聊天参数（不再包含工具，避免循环调用）
                    final_chat_params = {
                        "db": db,
                        "project_id": project_id,
                        "user_id": user_id,
                        "messages": messages,
                        "config_id": model_uuid,
                        "temperature": self.project_config.get('temperature', 0.7),
                        "max_tokens": self.project_config.get('max_tokens', 2000),
                        "is_shared": False
                    }
                    
                    # 获取最终回复
                    response = await chat_service.chat_completion(**final_chat_params)
            
            end_time = datetime.utcnow()
            
            # 处理chat_completion的响应格式
            if response and "choices" in response and len(response["choices"]) > 0:
                content = response["choices"][0]["message"]["content"]
                
                # 安全地构建ai_config字典，确保UUID正确转换
                ai_config_dict = {
                    "id": str(ai_config.id) if ai_config.id else None,
                    "name": ai_config.name,
                    "provider": ai_config.provider.name if ai_config.provider else None,
                    "model": ai_config.model.name if ai_config.model else None,
                    "model_id": str(ai_config.model.id) if ai_config.model and ai_config.model.id else None,
                    "temperature": ai_config.temperature,
                    "max_tokens": ai_config.max_tokens
                }
                
                await self._record_ai_usage(
                    db=db,
                    project_id=project_id,
                    user_id=user_id,
                    ai_config=ai_config_dict,
                    request_type="ai_cs_chat",  # 缩短为20字符以内
                    message=message,
                    response_content=content,
                    start_time=start_time,
                    end_time=end_time,
                    context=context
                )
                
                model_used = f"{ai_config.name or 'AI助手'} (客服插件配置)"
                
                # 收集工具使用信息
                tools_used = []
                if tool_calls:
                    for tool_call in tool_calls:
                        tools_used.append(tool_call["function"]["name"])
                
                logger.info(f"插件AI模型回复成功: model={model_used}, response_length={len(content)}, rag_used={rag_used}, tools_used={tools_used}")
                
                return content, model_used, knowledge_sources, tools_used, rag_used
            else:
                logger.warning("插件AI模型回复失败或返回空内容")
                return None, None, [], [], False
                
        except Exception as e:
            logger.error(f"插件AI模型生成回复失败: {e}")
            raise

    def _build_knowledge_context(self, knowledge_sources: List[Dict[str, Any]]) -> str:
        """构建知识库上下文信息"""
        if not knowledge_sources:
            return ""
        
        context_parts = []
        for i, source in enumerate(knowledge_sources[:3], 1):  # 只使用前3个最相关的结果
            content = source.get('content', '').strip()
            similarity = source.get('similarity', 0)
            document_title = source.get('document_title', '知识库文档')
            
            if content:
                # 清理内容，移除Excel格式标记
                import re
                cleaned_content = content
                cleaned_content = re.sub(r'=== Sheet\d+ ===\s*', '', cleaned_content)
                cleaned_content = re.sub(r'Unnamed: \d+\s*', '', cleaned_content)
                cleaned_content = re.sub(r'\n+', '\n', cleaned_content).strip()
                
                if len(cleaned_content) > 300:
                    cleaned_content = cleaned_content[:300] + "..."
                
                context_parts.append(f"{i}. {cleaned_content}")
                if document_title and document_title != '知识库文档':
                    context_parts.append(f"   (来源：{document_title})")
        
        return "\n\n".join(context_parts) if context_parts else ""
    
    def _build_customer_service_system_prompt(
        self, 
        intent: str, 
        entities: Dict[str, Any], 
        user_context: Dict[str, Any]
    ) -> str:
        """构建智能客服的系统提示词"""
        
        # 检查是否有自定义的assistant_prompt（来自公共组件配置）
        custom_prompt = user_context.get("assistant_prompt")
        
        if custom_prompt:
            # 使用自定义的AI助理描述作为基础提示词
            base_prompt = custom_prompt
        else:
            # 使用默认的专业客服提示词
            base_prompt = """你是一个专业的AI智能客服助手，具备以下特点：

🎯 **核心职能**：
- 提供友好、专业、高效的客户服务
- 理解客户需求并提供准确的解答
- 协助处理各种业务咨询和问题

💡 **服务能力**：
- 产品咨询和推荐
- 订单查询和处理
- 售后服务支持
- 门店信息查询
- 技术问题协助

🗣️ **对话风格**：
- 语气友好、耐心、专业
- 回答准确、简洁、有条理
- 主动询问细节以更好地帮助客户
- 对于无法解决的问题，及时转接人工客服

⚡ **特殊指令**：
- 始终保持礼貌和专业
- 优先使用已有的知识库信息
- 在不确定时坦诚说明并寻求协助
- 记录重要的客户需求和反馈"""

        # 根据意图添加特定指引
        intent_specific = ""
        if intent == "product_inquiry":
            intent_specific = "\n\n当前客户咨询：产品相关问题\n请重点关注产品特性、价格、库存等信息。"
        elif intent == "complaint":
            intent_specific = "\n\n当前客户状态：投诉或不满\n请优先安抚客户情绪，认真倾听问题，提供解决方案。"
        elif intent == "technical_support":
            intent_specific = "\n\n当前客户需求：技术支持\n请提供详细的技术指导，必要时收集错误信息。"
        elif intent == "store_inquiry":
            intent_specific = "\n\n当前客户咨询：门店相关信息\n请提供准确的门店位置、营业时间、联系方式等信息。"
        
        # 根据用户角色调整语调
        role_context = ""
        user_role = user_context.get('user_role', 'customer')
        if user_role in ['store_manager', 'purchase_manager', 'finance_manager']:
            role_context = f"\n\n注意：当前用户是{user_role}，请提供更专业和详细的信息。"
        
        return base_prompt + intent_specific + role_context
    
    async def _generate_ai_response(
        self,
        db: AsyncSession,
        message: str,
        project_id: uuid.UUID,
        user_id: Optional[uuid.UUID],
        assistant_id: uuid.UUID,
        intent: str,
        entities: Dict[str, Any],
        user_context: Dict[str, Any]
    ) -> Tuple[str, str, List[Dict], List[str], bool]:
        """使用传统AI助手生成回复（保留作为备用）"""
        try:
            logger.info(f"使用AI助手生成回复: assistant_id={assistant_id}, intent={intent}")
            
            # 构建AI助手聊天请求
            chat_request = AIAssistantChatRequest(
                message=message,
                conversation_id=user_context.get('conversation_id'),
                user_id=user_id,
                assistant_id=assistant_id,
                context={
                    'intent': intent,
                    'entities': entities,
                    'platform': 'ai_customer_service',
                    'user_role': user_context.get('user_role', 'customer'),
                    'project_id': str(project_id)
                },
                enable_knowledge_search=self.project_config.get('enable_knowledge_search', True),
                enable_tools=self.project_config.get('enable_mcp_tools', True),
                temperature=self.project_config.get('temperature', 0.7),
                max_tokens=self.project_config.get('max_tokens', 2000)
            )
            
            # 调用AI聊天服务
            chat_service = AIChatService()
            response = await chat_service.chat_with_assistant(
                db=db,
                request=chat_request,
                user_id=user_id
            )
            
            if response and response.get('success'):
                content = response.get('content', '')
                model_used = response.get('model_name', 'AI助手')
                knowledge_sources = response.get('knowledge_sources', [])
                tools_used = response.get('tools_used', [])
                rag_used = len(knowledge_sources) > 0
                
                logger.info(f"AI助手回复成功: model={model_used}, rag_used={rag_used}, tools_count={len(tools_used)}")
                
                return content, model_used, knowledge_sources, tools_used, rag_used
            else:
                logger.warning("AI助手回复失败或返回空内容")
                return None, None, [], [], False
                
        except Exception as e:
            logger.error(f"AI助手生成回复失败: {e}")
            raise
    
    async def _generate_intelligent_response(self, intent: str, entities: Dict[str, Any], message: str, conversation_history: List[Dict[str, Any]], user_context: Dict[str, Any]) -> str:
        """
        生成智能回复 - 2025增强版超级全能零售客服
        
        基于项目深度整合的AI智能客服，具备：
        1. 零售业务全流程支持（门店、采购、库存、销售、财务）
        2. MCP工具能力集成（数据查询、业务操作、报表生成）
        3. 多渠道管理（微信群、企业微信、钉钉等）
        4. 实时业务数据分析
        5. 智能决策建议
        """
        
        try:
            # 获取用户项目和角色信息
            project_id = user_context.get('project_id')
            user_role = user_context.get('user_role', 'customer')
            user_permissions = user_context.get('permissions', [])
            
            # 根据意图和业务场景生成回复
            if intent == "greeting":
                return await self._handle_greeting_intent(user_context, message)
            
            elif intent == "store_inquiry":
                return await self._handle_store_inquiry(entities, user_context, message)
                
            elif intent == "product_inquiry":
                return await self._handle_product_inquiry(entities, user_context, message)
                
            elif intent == "inventory_inquiry":
                return await self._handle_inventory_inquiry(entities, user_context, message)
                
            elif intent == "sales_inquiry":
                return await self._handle_sales_inquiry(entities, user_context, message)
                
            elif intent == "purchase_inquiry":
                return await self._handle_purchase_inquiry(entities, user_context, message)
                
            elif intent == "financial_inquiry":
                return await self._handle_financial_inquiry(entities, user_context, message)
                
            elif intent == "channel_management":
                return await self._handle_channel_management(entities, user_context, message)
                
            elif intent == "business_analysis":
                return await self._handle_business_analysis(entities, user_context, message)
                
            elif intent == "task_management":
                return await self._handle_task_management(entities, user_context, message)
                
            elif intent == "system_help":
                return await self._handle_system_help(entities, user_context, message)
                
            elif intent == "complaint":
                return await self._handle_complaint_intent(entities, user_context, message)
                
            elif intent == "technical_support":
                return await self._handle_technical_support(entities, user_context, message)
                
            else:
                return await self._handle_general_inquiry(intent, entities, user_context, message, conversation_history)
                
        except Exception as e:
            logger.error(f"生成智能回复失败: {e}")
            return "抱歉，我遇到了一些技术问题，请稍后再试或联系技术支持。"
    
    async def _handle_greeting_intent(self, user_context: Dict[str, Any], message: str) -> str:
        """处理问候意图"""
        user_role = user_context.get('user_role', 'customer')
        current_time = datetime.now().hour
        
        # 时间问候
        if 5 <= current_time < 12:
            time_greeting = "早上好"
        elif 12 <= current_time < 18:
            time_greeting = "下午好"
        else:
            time_greeting = "晚上好"
        
        # 角色定制问候
        if user_role == 'store_manager':
            return f"{time_greeting}！我是您的AI智能助手，可以帮您：\n📊 查看门店经营数据\n📦 管理库存和采购\n👥 处理员工排班\n📈 分析销售趋势\n💬 协调渠道沟通\n\n有什么需要我协助的吗？"
            
        elif user_role == 'purchase_manager':
            return f"{time_greeting}！我是采购智能助手，专门为您提供：\n🛒 采购订单管理和跟踪\n🏭 供应商信息查询\n📊 采购成本分析\n⚠️ 库存预警提醒\n📋 审批流程处理\n\n请问需要查询什么信息？"
            
        elif user_role == 'finance_manager':
            return f"{time_greeting}！我是财务智能助手，可以为您：\n💰 实时财务数据查询\n📈 销售收入分析\n💸 成本费用统计\n📊 生成财务报表\n⚖️ 利润分析\n\n需要查看哪方面的财务信息？"
            
        elif user_role in ['store_staff', 'cashier']:
            return f"{time_greeting}！我是门店助手，随时为您服务：\n🛍️ 商品信息查询\n💳 销售操作指导\n📦 库存状态查看\n📝 日常任务提醒\n🆘 问题快速解答\n\n有什么可以帮您的？"
            
        else:
            # 普通客户或未知角色
            return f"{time_greeting}！我是AI智能客服，很高兴为您服务！\n\n我可以帮您：\n🛍️ 商品咨询和推荐\n📍 门店位置和营业时间\n💰 优惠活动信息\n📞 售后服务支持\n📱 使用指导和帮助\n\n请问有什么可以为您效劳的？"
    
    async def _handle_store_inquiry(self, entities: Dict[str, Any], user_context: Dict[str, Any], message: str) -> str:
        """处理门店相关询问"""
        try:
            # 尝试调用MCP工具获取门店数据
            store_data = await self._call_mcp_tool("store_query", entities, user_context)
            
            if store_data and store_data.get('success'):
                stores = store_data.get('data', [])
                if stores:
                    response = "📍 为您找到以下门店信息：\n\n"
                    for i, store in enumerate(stores[:3], 1):  # 最多显示3个门店
                        response += f"{i}. **{store.get('name', '未知门店')}**\n"
                        response += f"   📍 地址：{store.get('address', '地址未提供')}\n"
                        response += f"   📞 电话：{store.get('phone', '电话未提供')}\n"
                        response += f"   🕐 营业时间：{store.get('business_hours', '9:00-21:00')}\n"
                        response += f"   📊 今日销售：¥{store.get('today_sales', 0):,.2f}\n\n"
                    
                    response += "💡 需要了解更多信息，请告诉我具体需求！"
                    return response
            
            # 默认门店信息回复
            return """📍 **门店信息查询**

🏪 **总部旗舰店**
   📍 地址：XX市XX区XX路123号
   📞 电话：400-XXX-XXXX
   🕐 营业时间：9:00-21:00
   🚗 停车：免费停车位50个

🏬 **购物中心店**
   📍 地址：XX购物中心3F-A区
   📞 电话：021-XXXX-XXXX
   🕐 营业时间：10:00-22:00
   🚇 交通：地铁2号线直达

💡 输入"附近门店"可查找离您最近的门店
💡 输入"门店活动"可了解各店优惠信息"""

        except Exception as e:
            logger.error(f"处理门店询问失败: {e}")
            return "抱歉，暂时无法获取门店信息，请稍后再试或拨打客服热线：400-XXX-XXXX"
    
    async def _handle_product_inquiry(self, entities: Dict[str, Any], user_context: Dict[str, Any], message: str) -> str:
        """处理商品相关询问"""
        try:
            # 提取商品关键词
            product_keywords = entities.get('product_name', '') or entities.get('keywords', '')
            
            # 调用MCP工具查询商品
            product_data = await self._call_mcp_tool("product_search", {
                "keywords": product_keywords,
                "category": entities.get('category'),
                "price_range": entities.get('price_range')
            }, user_context)
            
            if product_data and product_data.get('success'):
                products = product_data.get('data', [])
                if products:
                    response = f"🛍️ 为您找到 {len(products)} 款相关商品：\n\n"
                    
                    for i, product in enumerate(products[:5], 1):  # 最多显示5个商品
                        response += f"{i}. **{product.get('name', '商品名称')}**\n"
                        response += f"   💰 价格：¥{product.get('price', 0):,.2f}\n"
                        response += f"   📦 库存：{product.get('stock', 0)} 件\n"
                        response += f"   ⭐ 评分：{product.get('rating', 0)}/5.0\n"
                        
                        # 如果有库存预警
                        if product.get('stock', 0) < 10:
                            response += f"   ⚠️ 库存紧张，建议尽快购买\n"
                        
                        response += "\n"
                    
                    response += "💡 需要了解详细规格或其他信息，请告诉我！"
                    return response
            
            # 智能推荐回复
            recommendations = [
                "🔥 **热销推荐**：XX品牌新品，限时优惠85折！",
                "💎 **精选好物**：高品质XX，性价比之选",
                "🎯 **为您推荐**：根据您的需求，这款XX很适合",
                "⭐ **店长推荐**：本月热销爆品，好评如潮",
                "🏆 **获奖产品**：XX大奖获得者，品质保证"
            ]
            
            import random
            base_response = random.choice(recommendations)
            
            return f"""🛍️ **商品推荐**

{base_response}

📦 **产品特点**：
• 优质材料，精工制作
• 全国联保，7天无理由退换
• 限时特价，数量有限

💡 **购买建议**：
• 会员可享额外9.5折优惠
• 单笔满299元免运费
• 支持分期付款，0手续费

📞 需要详细咨询请说："联系人工客服"
🔍 查看更多商品请说："浏览商品目录" """

        except Exception as e:
            logger.error(f"处理商品询问失败: {e}")
            return "抱歉，商品查询暂时遇到问题，请稍后再试或联系人工客服为您详细介绍。"
    
    async def _handle_inventory_inquiry(self, entities: Dict[str, Any], user_context: Dict[str, Any], message: str) -> str:
        """处理库存相关询问"""
        try:
            user_role = user_context.get('user_role', 'customer')
            
            # 检查权限
            if user_role not in ['store_manager', 'purchase_manager', 'inventory_manager', 'store_staff']:
                return "抱歉，库存信息查询需要相应权限。如需了解商品现货情况，我可以为您查询。"
            
            # 调用MCP工具查询库存
            inventory_data = await self._call_mcp_tool("inventory_query", entities, user_context)
            
            if inventory_data and inventory_data.get('success'):
                inventory_info = inventory_data.get('data', {})
                
                response = "📦 **库存状态报告**\n\n"
                
                # 总体库存情况
                if inventory_info.get('summary'):
                    summary = inventory_info['summary']
                    response += f"📊 **整体概况**\n"
                    response += f"• 总商品数：{summary.get('total_products', 0)} 种\n"
                    response += f"• 总库存值：¥{summary.get('total_value', 0):,.2f}\n"
                    response += f"• 周转率：{summary.get('turnover_rate', 0):.2f}\n\n"
                
                # 库存预警
                if inventory_info.get('alerts'):
                    alerts = inventory_info['alerts']
                    response += "⚠️ **库存预警**\n"
                    for alert in alerts[:5]:  # 最多显示5个预警
                        response += f"• {alert.get('product_name')}: 仅剩 {alert.get('quantity')} 件\n"
                    response += "\n"
                
                # 热销商品库存
                if inventory_info.get('hot_products'):
                    hot_products = inventory_info['hot_products']
                    response += "🔥 **热销商品库存**\n"
                    for product in hot_products[:3]:
                        response += f"• {product.get('name')}: {product.get('stock')} 件\n"
                    response += "\n"
                
                response += "💡 需要查看详细库存数据，请登录管理系统或联系仓库管理员。"
                return response
            
            # 默认回复
            return """📦 **库存管理功能**

🔍 **可查询信息**：
• 商品库存数量
• 库存周转情况
• 安全库存预警
• 滞销商品统计

⚡ **快速查询**：
• "查看XX商品库存"
• "库存预警情况"
• "本月库存报告"
• "滞销商品清单"

🔧 **管理操作**：
• 库存调拨申请
• 盘点任务创建
• 补货需求提交

💡 详细库存数据请登录系统查看"""

        except Exception as e:
            logger.error(f"处理库存询问失败: {e}")
            return "抱歉，库存查询暂时遇到问题，请稍后再试或联系仓库管理员。"
    
    async def _handle_channel_management(self, entities: Dict[str, Any], user_context: Dict[str, Any], message: str) -> str:
        """处理渠道管理相关询问"""
        try:
            user_role = user_context.get('user_role', 'customer')
            
            # 检查权限
            if user_role not in ['channel_manager', 'operation_manager', 'project_admin']:
                return "抱歉，渠道管理功能需要相应权限。如有业务需求，请联系您的渠道经理。"
            
            # 调用MCP工具查询渠道数据
            channel_data = await self._call_mcp_tool("channel_query", entities, user_context)
            
            if channel_data and channel_data.get('success'):
                channels = channel_data.get('data', [])
                
                response = "📱 **渠道管理概览**\n\n"
                
                for channel in channels[:5]:  # 最多显示5个渠道
                    response += f"🔗 **{channel.get('name', '未知渠道')}**\n"
                    response += f"   类型：{channel.get('type', '未知')}\n"
                    response += f"   状态：{'🟢 正常' if channel.get('status') == 'active' else '🔴 异常'}\n"
                    response += f"   今日消息：{channel.get('today_messages', 0)} 条\n"
                    response += f"   活跃用户：{channel.get('active_users', 0)} 人\n\n"
                
                response += """🛠️ **可执行操作**：
• "发送群通知" - 向指定群发送消息
• "查看群成员" - 查看群成员列表
• "群数据统计" - 查看群活跃度数据
• "设置自动回复" - 配置群自动回复

💡 输入具体操作指令即可执行"""
                
                return response
            
            # 默认渠道管理功能介绍
            return """📱 **渠道管理中心**

🌐 **支持渠道**：
• 微信群管理 (个人微信/企业微信)
• 钉钉群管理
• QQ群管理
• 飞书群管理

🤖 **智能功能**：
• 自动回复设置
• 群消息监控
• 用户行为分析
• 营销内容推送

📊 **数据分析**：
• 群活跃度统计
• 用户互动分析
• 消息效果评估
• 转化率跟踪

🛠️ **管理工具**：
• 批量消息发送
• 群成员管理
• 内容素材库
• 定时任务设置

💡 说出具体需求，我来协助您操作！"""

        except Exception as e:
            logger.error(f"处理渠道管理失败: {e}")
            return "抱歉，渠道管理功能暂时遇到问题，请稍后再试或联系技术支持。"
    
    async def _handle_business_analysis(self, entities: Dict[str, Any], user_context: Dict[str, Any], message: str) -> str:
        """处理业务分析相关询问"""
        try:
            user_role = user_context.get('user_role', 'customer')
            
            # 检查权限
            if user_role not in ['finance_manager', 'operation_manager', 'store_manager', 'project_admin']:
                return "抱歉，业务分析功能需要管理权限。如需了解业务情况，请联系您的上级主管。"
            
            # 调用MCP工具进行业务分析
            analysis_data = await self._call_mcp_tool("business_analysis", entities, user_context)
            
            if analysis_data and analysis_data.get('success'):
                data = analysis_data.get('data', {})
                
                response = "📈 **业务分析报告**\n\n"
                
                # 销售分析
                if data.get('sales'):
                    sales = data['sales']
                    response += f"💰 **销售概况**\n"
                    response += f"• 今日销售：¥{sales.get('today', 0):,.2f}\n"
                    response += f"• 本月销售：¥{sales.get('month', 0):,.2f}\n"
                    response += f"• 环比增长：{sales.get('growth_rate', 0):+.2%}\n\n"
                
                # 客户分析
                if data.get('customers'):
                    customers = data['customers']
                    response += f"👥 **客户分析**\n"
                    response += f"• 活跃客户：{customers.get('active', 0)} 人\n"
                    response += f"• 新增客户：{customers.get('new', 0)} 人\n"
                    response += f"• 复购率：{customers.get('repeat_rate', 0):.2%}\n\n"
                
                # 库存分析
                if data.get('inventory'):
                    inventory = data['inventory']
                    response += f"📦 **库存分析**\n"
                    response += f"• 库存周转：{inventory.get('turnover', 0):.2f} 次/月\n"
                    response += f"• 滞销商品：{inventory.get('slow_moving', 0)} 种\n"
                    response += f"• 缺货预警：{inventory.get('stockout_risk', 0)} 种\n\n"
                
                response += "📊 需要更详细的分析报告，请访问BI分析平台。"
                return response
            
            # 默认分析功能介绍
            return """📈 **业务智能分析**

📊 **分析维度**：
• 销售业绩分析
• 客户行为分析
• 库存效率分析
• 渠道效果分析
• 财务状况分析

🎯 **关键指标**：
• 销售额和增长率
• 客户获取成本
• 库存周转率
• 毛利率分析
• ROI投资回报

📈 **趋势预测**：
• 销售趋势预测
• 季节性分析
• 市场机会识别
• 风险预警提示

💡 **智能建议**：
• 营销策略优化
• 库存补货建议
• 价格策略调整
• 渠道投入优化

🔍 说出您关心的指标，我为您深度分析！"""

        except Exception as e:
            logger.error(f"处理业务分析失败: {e}")
            return "抱歉，业务分析功能暂时遇到问题，请稍后再试或联系数据分析师。"
    
    async def _call_mcp_tool(self, tool_name: str, params: Dict[str, Any], user_context: Dict[str, Any]) -> Dict[str, Any]:
        """
        调用MCP工具 - 2025增强版集成
        
        使用真实的MCP服务集成，提供强大的零售业务支持
        """
        try:
            # 导入MCP集成服务
            from ..services.mcp_integration_service import mcp_integration_service
            
            # 增强用户上下文，确保有足够权限
            enhanced_user_context = {
                **user_context,
                "permissions": ["read_store_data", "read_product_data", "read_inventory_data", "read_sales_data", "use_mcp_tools"],
                "user_role": user_context.get("user_role", "customer"),
                "source": "ai_customer_service_engine"
            }
            
            # 调用真实的MCP工具
            result = await mcp_integration_service.execute_tool(
                tool_name=tool_name,
                parameters=params,
                user_context=enhanced_user_context
            )
            
            if result.success:
                logger.info(f"MCP工具 {tool_name} 执行成功: 数据行数={len(result.data) if isinstance(result.data, list) else 'N/A'}")
                return {
                    "success": True,
                    "data": result.data,
                    "execution_time": result.execution_time,
                    "metadata": result.metadata
                }
            else:
                logger.error(f"MCP工具 {tool_name} 执行失败: {result.error}")
                # 即使失败也记录调用次数，然后回退
                return await self._fallback_mcp_tool(tool_name, params, user_context)
                
        except ImportError as e:
            logger.warning(f"MCP集成服务导入失败: {e}")
            # 如果导入失败，返回错误信息
            return {
                "success": False,
                "error": "MCP集成服务不可用",
                "message": f"工具 {tool_name} 需要MCP服务支持，请检查系统配置"
            }
            
        except Exception as e:
            logger.error(f"MCP工具调用异常 {tool_name}: {e}")
            # 任何异常都返回错误信息
            return {
                "success": False,
                "error": str(e),
                "message": f"调用工具 {tool_name} 时发生错误"
            }

    
    async def _handle_sales_inquiry(self, entities: Dict[str, Any], user_context: Dict[str, Any], message: str) -> str:
        """处理销售相关询问"""
        # 类似于库存询问的处理逻辑
        return "📈 销售数据查询功能正在完善中，请稍后再试。"
    
    async def _handle_purchase_inquiry(self, entities: Dict[str, Any], user_context: Dict[str, Any], message: str) -> str:
        """处理采购相关询问"""
        # 类似于库存询问的处理逻辑
        return "🛒 采购管理功能正在完善中，请稍后再试。"
    
    async def _handle_financial_inquiry(self, entities: Dict[str, Any], user_context: Dict[str, Any], message: str) -> str:
        """处理财务相关询问"""
        # 类似于库存询问的处理逻辑
        return "💰 财务数据查询功能正在完善中，请稍后再试。"
    
    async def _handle_task_management(self, entities: Dict[str, Any], user_context: Dict[str, Any], message: str) -> str:
        """处理任务管理相关询问"""
        return "📝 任务管理功能正在完善中，请稍后再试。"
    
    async def _handle_system_help(self, entities: Dict[str, Any], user_context: Dict[str, Any], message: str) -> str:
        """处理系统帮助询问"""
        return """🤖 **AI智能客服帮助**

💡 **我能为您做什么**：
• 商品咨询和推荐
• 门店信息查询
• 库存状态查看
• 销售数据分析
• 渠道管理协助
• 业务流程指导

🗣️ **如何与我对话**：
• 直接说出您的需求
• 使用自然语言即可
• 支持语音输入（如果启用）

⚡ **快速指令**：
• "查看门店" - 查询门店信息
• "库存查询" - 查看库存状态  
• "销售报告" - 获取销售数据
• "人工客服" - 转接人工服务

📞 **技术支持**：如遇问题请联系技术支持团队"""
    
    async def _handle_complaint_intent(self, entities: Dict[str, Any], user_context: Dict[str, Any], message: str) -> str:
        """处理投诉意图"""
        complaint_types = [
            "非常抱歉给您带来不便！我会立即为您处理这个问题。",
            "感谢您的反馈，我们非常重视您的意见。",
            "抱歉让您有不好的体验，让我来帮您解决。",
            "我们会认真对待您的投诉，马上为您跟进处理。"
        ]
        
        import random
        base_response = random.choice(complaint_types)
        
        return f"""{base_response}

🔍 **问题记录**：您的问题已登记，编号为 #{random.randint(100000, 999999)}

⚡ **立即处理**：
• 我已将问题升级至相关部门
• 预计24小时内给您回复
• 您可随时查询处理进度

📞 **紧急联系**：
• 客服热线：400-XXX-XXXX
• 投诉邮箱：<EMAIL>
• 在线客服：说"转接人工"

💡 如需补充问题描述，请继续说明"""
    
    async def _handle_technical_support(self, entities: Dict[str, Any], user_context: Dict[str, Any], message: str) -> str:
        """处理技术支持询问"""
        tech_responses = [
            "我来帮您解决技术问题！",
            "技术支持小助手为您服务！",
            "让我协助您解决技术难题。",
            "技术问题交给我来处理！"
        ]
        
        import random
        base_response = random.choice(tech_responses)
        
        return f"""{base_response}

🔧 **常见问题解决**：
• 登录问题：请检查用户名密码
• 页面异常：请刷新页面或清除缓存
• 功能故障：请尝试重启应用
• 数据异常：请联系技术人员

🆘 **技术支持渠道**：
• 在线技术支持：说"技术支持"
• 技术热线：400-XXX-XXXX转2
• 技术邮箱：<EMAIL>
• 远程协助：可申请远程桌面支持

💻 **系统要求**：
• 推荐使用Chrome/Safari浏览器
• 建议网络带宽不低于10Mbps
• 支持Windows/Mac/iOS/Android

📝 请详细描述您遇到的技术问题，我来为您诊断！"""
    
    async def _handle_general_inquiry(self, intent: str, entities: Dict[str, Any], user_context: Dict[str, Any], message: str, conversation_history: List[Dict[str, Any]]) -> str:
        """处理一般性询问，整合知识库搜索"""
        try:
            # 首先尝试从知识库搜索相关信息
            if hasattr(self, '_db_session') and self._db_session:
                knowledge_response = await self._search_knowledge_base(
                    message=message, 
                    intent=intent, 
                    entities=entities,
                    user_context=user_context,
                    db=self._db_session
                )
                
                # 如果知识库有相关信息，优先使用
                if knowledge_response and knowledge_response.get('success') and knowledge_response.get('content'):
                    logger.info("从知识库找到相关信息，使用知识库回复")
                    return knowledge_response['content']
            
            # 如果知识库没有找到信息，使用默认回复
            general_responses = [
                "我理解您的需求，让我为您查找相关信息。",
                "好的，我来为您详细介绍一下。",
                "这是个很好的问题，我来为您解答。",
                "我会尽力为您提供帮助。",
                "让我为您查询相关信息。"
            ]
            
            import random
            base_response = random.choice(general_responses)
            
            return f"""{base_response}

💡 **相关建议**：
• 如需具体商品信息，请说"商品查询"
• 如需门店服务，请说"门店咨询"  
• 如需人工服务，请说"转接人工"
• 如需技术支持，请说"技术帮助"

🔍 **搜索提示**：
您可以输入更具体的关键词，比如：
• 商品名称、型号
• 具体需求或问题
• 想了解的功能

📞 **其他方式**：
• 客服热线：400-XXX-XXXX
• 在线客服：工作时间9:00-21:00
• 官方网站：www.company.com

有什么具体问题，请随时告诉我！"""
            
        except Exception as e:
            logger.error(f"处理一般询问失败: {e}")
            return "抱歉，处理您的询问时遇到了一些问题。请稍后再试，或联系我们的客服人员。"

    async def analyze_message_intent(
        self,
        message: str,
        context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        分析消息意图 - 2025增强版
        
        基于零售行业特性的智能意图识别，支持：
        1. 业务流程意图（门店、采购、库存、销售、财务）
        2. 渠道管理意图（微信群、企业微信等）
        3. 技术支持意图
        4. 投诉处理意图
        5. 一般询问意图
        
        Args:
            message: 用户消息
            context: 上下文信息
            
        Returns:
            Dict[str, Any]: 意图分析结果
        """
        try:
            # 零售行业专业意图关键词映射
            intent_keywords = {
                "greeting": ["你好", "您好", "hello", "hi", "早上好", "下午好", "晚上好", "在吗"],
                "store_inquiry": ["门店", "店铺", "地址", "位置", "营业时间", "电话", "联系方式", "附近", "分店"],
                "product_inquiry": ["商品", "产品", "价格", "多少钱", "推荐", "介绍", "详情", "规格", "参数", "型号"],
                "inventory_inquiry": ["库存", "现货", "有货", "缺货", "补货", "库存量", "库房", "仓库", "盘点"],
                "sales_inquiry": ["销售", "业绩", "销量", "营业额", "收入", "订单", "成交"],
                "purchase_inquiry": ["采购", "进货", "供应商", "采购单", "订货", "采购计划"],
                "financial_inquiry": ["财务", "财报", "利润", "成本", "费用", "收支", "结算", "账务"],
                "channel_management": ["群", "微信群", "企业微信", "钉钉", "渠道", "群管理", "群发", "通知"],
                "business_analysis": ["分析", "报表", "统计", "数据", "趋势", "对比", "增长", "BI"],
                "task_management": ["任务", "工作", "待办", "提醒", "排班", "安排", "计划"],
                "complaint": ["投诉", "不满", "问题", "故障", "错误", "抱怨", "意见", "建议", "反馈"],
                "technical_support": ["技术", "支持", "帮助", "故障", "bug", "异常", "登录", "系统", "软件"],
                "system_help": ["帮助", "如何", "怎么", "教程", "指导", "说明", "使用方法"]
            }
            
            message_lower = message.lower()
            detected_intents = []
            intent_scores = {}
            
            # 计算每个意图的匹配分数
            for intent, keywords in intent_keywords.items():
                score = 0
                matched_keywords = []
                
                for keyword in keywords:
                    if keyword in message_lower:
                        score += 1
                        matched_keywords.append(keyword)
                
                if score > 0:
                    intent_scores[intent] = {
                        "score": score,
                        "keywords": matched_keywords,
                        "confidence": min(score / len(keywords), 1.0)
                    }
                    detected_intents.append(intent)
            
            # 如果没有检测到特定意图，进行二次分析
            if not detected_intents:
                # 检查是否包含疑问词
                question_words = ["什么", "怎么", "如何", "为什么", "哪里", "多少", "几", "吗", "？", "?"]
                if any(word in message_lower for word in question_words):
                    detected_intents = ["general_inquiry"]
                    intent_scores["general_inquiry"] = {"score": 1, "keywords": [], "confidence": 0.6}
                else:
                    detected_intents = ["general_inquiry"]
                    intent_scores["general_inquiry"] = {"score": 1, "keywords": [], "confidence": 0.5}
            
            # 确定主要意图（得分最高的）
            primary_intent = max(intent_scores.keys(), key=lambda x: intent_scores[x]["score"]) if intent_scores else "general_inquiry"
            
            # 实体提取（简化版）
            entities = self._extract_entities(message, primary_intent)
            
            # 情感分析（简化版）
            sentiment = self._analyze_sentiment(message)
            
            return {
                "intents": detected_intents,
                "primary_intent": primary_intent,
                "intent_scores": intent_scores,
                "confidence": intent_scores.get(primary_intent, {}).get("confidence", 0.5),
                "entities": entities,
                "sentiment": sentiment,
                "message_length": len(message),
                "has_question": any(word in message_lower for word in ["？", "?", "吗", "呢"])
            }
            
        except Exception as e:
            logger.error(f"意图分析失败: {e}")
            return {
                "intents": ["general_inquiry"],
                "primary_intent": "general_inquiry",
                "intent_scores": {"general_inquiry": {"score": 1, "keywords": [], "confidence": 0.5}},
                "confidence": 0.5,
                "entities": {},
                "sentiment": "neutral",
                "message_length": len(message),
                "has_question": False
            }
    
    def _extract_entities(self, message: str, intent: str) -> Dict[str, Any]:
        """提取消息中的实体"""
        try:
            import re  # 将import移到函数开头
            entities = {}
            message_lower = message.lower()
            
            # 根据意图提取相关实体
            if intent == "store_inquiry":
                # 提取地理位置相关实体
                location_keywords = ["附近", "周边", "这里", "那里", "xx区", "xx路", "xx街"]
                for keyword in location_keywords:
                    if keyword in message_lower:
                        entities["location"] = keyword
                        break
            
            elif intent == "product_inquiry":
                # 提取商品相关实体
                # 提取价格范围
                price_pattern = r'(\d+)-(\d+)元?|(\d+)元?以下|(\d+)元?左右'
                price_match = re.search(price_pattern, message)
                if price_match:
                    entities["price_range"] = price_match.group(0)
                
                # 提取商品类别
                categories = ["电子产品", "服装", "食品", "日用品", "化妆品", "家具", "图书"]
                for category in categories:
                    if category in message:
                        entities["category"] = category
                        break
            
            elif intent == "inventory_inquiry":
                # 提取商品名称
                product_pattern = r'([\u4e00-\u9fa5a-zA-Z0-9]+)库存|库存([\u4e00-\u9fa5a-zA-Z0-9]+)'
                product_match = re.search(product_pattern, message)
                if product_match:
                    entities["product_name"] = product_match.group(1) or product_match.group(2)
            
            elif intent == "channel_management":
                # 提取渠道类型
                channel_types = ["微信群", "企业微信", "钉钉", "QQ群", "飞书"]
                for channel_type in channel_types:
                    if channel_type in message:
                        entities["channel_type"] = channel_type
                        break
            
            # 通用实体提取
            # 提取数字
            numbers = re.findall(r'\d+', message)
            if numbers:
                entities["numbers"] = numbers
            
            # 提取时间
            time_keywords = ["今天", "昨天", "明天", "本周", "本月", "上个月", "去年", "今年"]
            for time_keyword in time_keywords:
                if time_keyword in message:
                    entities["time_period"] = time_keyword
                    break
            
            return entities
            
        except Exception as e:
            logger.error(f"实体提取失败: {e}")
            return {}
    
    def _analyze_sentiment(self, message: str) -> str:
        """分析消息情感（简化版）"""
        try:
            # 正面情感词
            positive_words = ["好", "棒", "满意", "喜欢", "谢谢", "感谢", "赞", "优秀", "完美"]
            # 负面情感词
            negative_words = ["差", "坏", "不满", "投诉", "问题", "故障", "错误", "失望", "糟糕"]
            # 中性/疑问词
            neutral_words = ["请问", "咨询", "了解", "查询", "帮助"]
            
            message_lower = message.lower()
            
            positive_count = sum(1 for word in positive_words if word in message_lower)
            negative_count = sum(1 for word in negative_words if word in message_lower)
            neutral_count = sum(1 for word in neutral_words if word in message_lower)
            
            if negative_count > positive_count:
                return "negative"
            elif positive_count > negative_count:
                return "positive"
            else:
                return "neutral"
                
        except Exception as e:
            logger.error(f"情感分析失败: {e}")
            return "neutral"
    
    async def evaluate_response_quality(
        self,
        user_message: str,
        ai_response: str,
        context: Optional[Dict[str, Any]] = None,
        intent_analysis: Dict[str, Any] = None,
        execution_time: float = None
    ) -> Dict[str, Any]:
        """
        评估AI回复质量 - 2025增强版多维度评分
        """
        try:
            # 初始化评分
            relevance_score = 0.5
            coherence_score = 0.5
            helpfulness_score = 0.5
            tone_score = 0.5
            accuracy_score = 0.5
            engagement_score = 0.5
            
            # 1. 相关性评分 - 基于关键词匹配和语义相似度
            try:
                # 简单关键词匹配
                user_words = set(user_message.lower().split())
                response_words = set(ai_response.lower().split())
                common_words = user_words.intersection(response_words)
                
                if len(user_words) > 0:
                    keyword_match_ratio = len(common_words) / len(user_words)
                    relevance_score = min(0.3 + keyword_match_ratio * 0.7, 1.0)
                else:
                    relevance_score = 0.5
                    
                # 如果使用了MCP工具，提高相关性评分
                if context.get("mcp_tools_used"):
                    relevance_score = min(relevance_score + 0.2, 1.0)
                    
            except Exception as e:
                logger.warning(f"相关性评分计算失败: {e}")
                relevance_score = 0.5
            
            # 2. 连贯性评分 - 基于回复长度和结构
            try:
                response_length = len(ai_response)
                if 10 <= response_length <= 1000:
                    coherence_score = 0.8
                elif response_length < 10:
                    coherence_score = 0.3
                elif response_length > 1000:
                    coherence_score = 0.6
                else:
                    coherence_score = 0.5
                    
                # 检查是否有结构化内容（列表、编号等）
                if any(marker in ai_response for marker in ['1.', '2.', '•', '-', '：']):
                    coherence_score = min(coherence_score + 0.1, 1.0)
                    
            except Exception as e:
                logger.warning(f"连贯性评分计算失败: {e}")
                coherence_score = 0.5
            
            # 3. 有用性评分 - 基于意图匹配和业务价值
            try:
                if intent_analysis:
                    intent_confidence = intent_analysis.get("confidence", 0.5)
                    business_entities = intent_analysis.get("entities", {})
                    
                    # 意图识别置信度越高，有用性越高
                    helpfulness_score = max(0.3, min(intent_confidence, 1.0))
                    
                    # 包含业务实体提高有用性
                    if business_entities:
                        entity_bonus = min(len(business_entities) * 0.1, 0.3)
                        helpfulness_score = min(helpfulness_score + entity_bonus, 1.0)
                else:
                    helpfulness_score = 0.6
                    
                # 如果回复包含具体信息（数字、时间、产品名等），提高有用性
                if any(char.isdigit() for char in ai_response):
                    helpfulness_score = min(helpfulness_score + 0.1, 1.0)
                    
            except Exception as e:
                logger.warning(f"有用性评分计算失败: {e}")
                helpfulness_score = 0.5
            
            # 4. 语调评分 - 基于礼貌用语和情感表达
            try:
                polite_phrases = ['请', '谢谢', '抱歉', '很高兴', '为您', '希望', '建议']
                polite_count = sum(1 for phrase in polite_phrases if phrase in ai_response)
                
                tone_score = min(0.5 + polite_count * 0.1, 1.0)
                
                # 检查是否过于冷淡或过于热情
                if len(ai_response) < 20 and polite_count == 0:
                    tone_score = 0.3
                elif polite_count > 5:
                    tone_score = min(tone_score - 0.1, 0.9)
                    
            except Exception as e:
                logger.warning(f"语调评分计算失败: {e}")
                tone_score = 0.5
            
            # 5. 准确性评分 - 基于事实核查和一致性
            try:
                # 检查是否有明显的矛盾或错误信息
                accuracy_score = 0.8  # 默认较高准确性
                
                # 如果回复包含"不确定"、"可能"等词汇，降低准确性评分
                uncertain_phrases = ['不确定', '可能', '大概', '也许', '或许']
                uncertain_count = sum(1 for phrase in uncertain_phrases if phrase in ai_response)
                if uncertain_count > 0:
                    accuracy_score = max(0.4, accuracy_score - uncertain_count * 0.1)
                
                # 如果使用了数据工具，提高准确性
                if context.get("mcp_tools_used"):
                    accuracy_score = min(accuracy_score + 0.15, 1.0)
                    
            except Exception as e:
                logger.warning(f"准确性评分计算失败: {e}")
                accuracy_score = 0.5
            
            # 6. 互动性评分 - 基于问题回答和后续引导
            try:
                engagement_score = 0.6  # 基础分
                
                # 检查是否回答了用户问题
                question_markers = ['？', '?', '什么', '怎么', '为什么', '何时', '哪里']
                has_question = any(marker in user_message for marker in question_markers)
                
                if has_question:
                    # 检查回复是否直接回答了问题
                    if any(marker in ai_response for marker in ['是', '因为', '可以', '需要']):
                        engagement_score = min(engagement_score + 0.2, 1.0)
                
                # 检查是否提供了后续选择或引导
                follow_up_phrases = ['还有其他', '您还可以', '如果您需要', '建议您']
                if any(phrase in ai_response for phrase in follow_up_phrases):
                    engagement_score = min(engagement_score + 0.1, 1.0)
                    
            except Exception as e:
                logger.warning(f"互动性评分计算失败: {e}")
                engagement_score = 0.5
            
            # 计算加权总分 - 安全处理所有数值
            weights = {
                "relevance": 0.25,
                "coherence": 0.15, 
                "helpfulness": 0.25,
                "tone": 0.10,
                "accuracy": 0.15,
                "engagement": 0.10
            }
            
            # 确保所有分数都是有效数值
            relevance_score = safe_round(relevance_score)
            coherence_score = safe_round(coherence_score)
            helpfulness_score = safe_round(helpfulness_score)
            tone_score = safe_round(tone_score)
            accuracy_score = safe_round(accuracy_score)
            engagement_score = safe_round(engagement_score)
            
            overall_score = (
                relevance_score * weights["relevance"] +
                coherence_score * weights["coherence"] +
                helpfulness_score * weights["helpfulness"] +
                tone_score * weights["tone"] +
                accuracy_score * weights["accuracy"] +
                engagement_score * weights["engagement"]
            )
            
            overall_score = safe_round(overall_score)
            
            # 性能评分
            performance_score = 1.0
            if execution_time:
                try:
                    # 响应时间评分：<2秒=优秀，2-5秒=良好，>5秒=需改进
                    if execution_time < 2.0:
                        performance_score = 1.0
                    elif execution_time < 5.0:
                        performance_score = 0.8
                    else:
                        performance_score = 0.6
                except (ValueError, TypeError):
                    performance_score = 0.8
            
            performance_score = safe_round(performance_score)
            
            # 质量等级判定
            if overall_score >= 0.8:
                quality_level = "excellent"
                quality_desc = "回复质量优秀"
            elif overall_score >= 0.6:
                quality_level = "good"
                quality_desc = "回复质量良好"
            elif overall_score >= 0.4:
                quality_level = "fair"
                quality_desc = "回复质量一般"
            else:
                quality_level = "poor"
                quality_desc = "回复质量需要改进"
            
            return {
                "overall_score": overall_score,
                "quality_level": quality_level,
                "quality_description": quality_desc,
                "dimension_scores": {
                    "relevance": relevance_score,
                    "coherence": coherence_score,
                    "helpfulness": helpfulness_score,
                    "tone": tone_score,
                    "accuracy": accuracy_score,
                    "engagement": engagement_score
                },
                "performance_score": performance_score,
                "response_time": safe_round(execution_time or 0, 3),
                "evaluation_timestamp": datetime.utcnow().isoformat(),
                "evaluation_metadata": {
                    "user_message_length": len(user_message),
                    "ai_response_length": len(ai_response),
                    "has_intent_analysis": intent_analysis is not None,
                    "mcp_tools_used": bool(context.get("mcp_tools_used")),
                    "evaluation_version": "2025.1.0"
                }
            }
            
        except Exception as e:
            logger.error(f"评估回复质量失败: {e}")
            # 返回默认的安全评分
            return {
                "overall_score": 0.5,
                "quality_level": "unknown",
                "quality_description": "评估失败，使用默认评分",
                "dimension_scores": {
                    "relevance": 0.5,
                    "coherence": 0.5,
                    "helpfulness": 0.5,
                    "tone": 0.5,
                    "accuracy": 0.5,
                    "engagement": 0.5
                },
                "performance_score": 0.5,
                "response_time": 0.0,
                "evaluation_timestamp": datetime.utcnow().isoformat(),
                "evaluation_metadata": {
                    "evaluation_error": str(e),
                    "evaluation_version": "2025.1.0"
                }
            }

    async def _fallback_mcp_tool(self, tool_name: str, params: Dict[str, Any], user_context: Dict[str, Any]) -> Dict[str, Any]:
        """
        MCP工具回退处理 - 2025版本移除所有模拟数据
        
        当MCP工具调用失败时，返回错误信息而非模拟数据
        """
        logger.warning(f"MCP工具 {tool_name} 调用失败，进入回退模式")
        
        return {
            "success": False,
            "error": "MCP工具调用失败",
            "message": f"工具 {tool_name} 暂时不可用，请稍后再试或联系技术支持",
            "tool_name": tool_name,
            "fallback_reason": "mcp_service_unavailable",
            "suggested_action": "请检查MCP服务状态或使用其他查询方式"
        }

    async def generate_smart_reply_suggestions(
        self,
        message: str,
        intent: str,
        context: Dict[str, Any] = None
    ) -> List[str]:
        """
        生成智能回复建议
        
        Args:
            message: 用户消息
            intent: 识别到的意图
            context: 上下文信息
            
        Returns:
            回复建议列表
        """
        try:
            context = context or {}
            suggestions = []
            
            # 根据意图生成对应的快捷回复建议
            if intent == "greeting":
                suggestions = [
                    "您好！很高兴为您服务，请问有什么可以帮助您的？",
                    "欢迎咨询！我是您的专属AI客服，请告诉我您的需求。",
                    "您好！我会尽力为您解答问题，请问您想了解什么？"
                ]
            elif intent == "product_inquiry":
                suggestions = [
                    "我来为您介绍一下相关产品信息",
                    "请告诉我您感兴趣的具体产品类型，我会为您详细介绍",
                    "我们有多种产品可供选择，您有特定的需求吗？"
                ]
            elif intent == "store_inquiry":
                suggestions = [
                    "请问您想了解哪个地区的门店信息？",
                    "我来帮您查询门店的详细信息",
                    "我们在多个城市都有门店，您方便告诉我您的位置吗？"
                ]
            elif intent == "complaint":
                suggestions = [
                    "非常抱歉给您带来了困扰，请详细告诉我遇到的问题",
                    "我会认真处理您的投诉，请您先别着急",
                    "为了更好地解决问题，请您详细描述一下具体情况"
                ]
            elif intent == "technical_support":
                suggestions = [
                    "我来帮您解决技术问题，请详细描述遇到的情况",
                    "请告诉我您使用的是哪个功能，我会为您提供详细指导",
                    "让我来协助您解决技术问题，请说明具体的错误信息"
                ]
            elif intent == "sales_inquiry":
                suggestions = [
                    "我来为您介绍优惠活动和价格信息",
                    "请问您想了解哪类产品的价格？",
                    "我会为您提供最新的报价信息"
                ]
            else:
                # 通用建议
                suggestions = [
                    "我明白了您的问题，让我来为您处理",
                    "感谢您的咨询，我会尽力为您解答",
                    "请稍等，我来为您查询相关信息"
                ]
            
            # 如果有知识库结果，添加基于知识的建议
            knowledge_sources = context.get("knowledge_sources", [])
            if knowledge_sources:
                suggestions.append("根据我们的知识库，我为您找到了相关信息")
                suggestions.append("让我基于我们的资料为您详细解答")
            
            # 如果有业务动作，添加相关建议
            business_actions = context.get("business_actions", [])
            if business_actions:
                suggestions.append("我已经为您启动了相关的业务流程")
                suggestions.append("相关部门会及时处理您的需求")
            
            # 限制建议数量
            return suggestions[:5]
            
        except Exception as e:
            logger.error(f"生成智能回复建议失败: {e}")
            return [
                "我会尽力为您解答问题",
                "请告诉我更多详细信息",
                "让我来帮助您解决这个问题"
            ]

    async def _record_ai_usage(
        self,
        db: AsyncSession,
        project_id: uuid.UUID,
        user_id: Optional[uuid.UUID],
        ai_config: Dict[str, Any],
        request_type: str,
        message: str,
        response_content: str,
        start_time: datetime,
        end_time: datetime,
        context: Dict[str, Any] = None
    ):
        """
        记录AI使用情况到统一计费系统
        
        Args:
            db: 数据库会话
            project_id: 项目ID
            user_id: 用户ID
            ai_config: AI配置信息
            request_type: 请求类型 (chat, vision, audio等)
            message: 用户消息
            response_content: AI回复内容
            start_time: 开始时间
            end_time: 结束时间
            context: 上下文信息
        """
        try:
            from services.ai.usage_service import AIUsageService
            from schemas.ai.usage import AIUsageCreate
            from models.project import Project
            from sqlalchemy import select
            
            # 获取项目信息
            project_query = select(Project).where(Project.id == project_id)
            project_result = await db.execute(project_query)
            project = project_result.scalars().first()
            
            if not project:
                logger.warning(f"未找到项目: {project_id}")
                return
            
            # 计算token数量（简化估算）
            prompt_tokens = len(message.split()) * 1.3  # 估算输入token数
            completion_tokens = len(response_content.split()) * 1.3  # 估算输出token数
            total_tokens = int(prompt_tokens + completion_tokens)
            
            # 计算处理时间
            duration_ms = int((end_time - start_time).total_seconds() * 1000)
            
            # 估算成本（基于token数量，实际应该根据模型定价计算）
            # OpenAI GPT-4大概 $0.03/1K输入tokens，$0.06/1K输出tokens
            input_cost = (prompt_tokens / 1000) * 0.03
            output_cost = (completion_tokens / 1000) * 0.06
            total_cost = input_cost + output_cost
            
            # 创建使用记录
            usage_data = AIUsageCreate(
                tenant_id=project.tenant_id,
                project_id=project_id,
                user_id=user_id or project.created_by,  # 如果没有用户ID，使用项目创建者
                config_id=self._safe_uuid_convert(ai_config.get('id')),
                model_id=self._safe_uuid_convert(ai_config.get('model_id')),
                request_type=request_type,  # 直接使用传入的request_type，已缩短
                prompt_tokens=int(prompt_tokens),
                completion_tokens=int(completion_tokens),
                total_tokens=total_tokens,
                cost=round(total_cost, 6),
                duration_ms=duration_ms,
                status="success",
                request_metadata={
                    "plugin": "ai_customer_service",
                    "plugin_version": "2025.1.0",
                    "intent": context.get('intent') if context else None,
                    "entities": context.get('entities') if context else {},
                    "platform": context.get('platform') if context else 'ai_customer_service',
                    "user_role": context.get('user_role') if context else 'customer',
                    "message_length": len(message),
                    "response_length": len(response_content),
                    "temperature": self.project_config.get('temperature', 0.7),
                    "max_tokens": self.project_config.get('max_tokens', 2000),
                    "knowledge_search_enabled": self.project_config.get('enable_knowledge_search', True),
                    "tools_enabled": self.project_config.get('enable_mcp_tools', True),
                    "model_name": ai_config.get('name', 'Unknown'),
                    "provider": ai_config.get('provider', 'Unknown')
                }
            )
            
            # 保存使用记录
            await AIUsageService.create_usage(db, usage_data)
            
            logger.info(f"AI客服插件使用记录已保存: {request_type}, tokens={total_tokens}, cost=${total_cost:.6f}, duration={duration_ms}ms")
            
        except Exception as e:
            logger.error(f"记录AI使用情况失败: {e}")
            # 计费记录失败不应该影响主要功能，所以只记录错误

    async def _search_knowledge_base(
        self,
        message: str,
        intent: str = None,
        entities: Dict[str, Any] = None,
        user_context: Dict[str, Any] = None,
        project_id: Optional[uuid.UUID] = None,
        user_id: Optional[uuid.UUID] = None,
        db: Optional[AsyncSession] = None
    ) -> Dict[str, Any]:
        """
        搜索知识库并生成基于RAG的回复
        
        Args:
            message: 用户消息
            intent: 意图
            entities: 实体
            user_context: 用户上下文
            project_id: 项目ID
            user_id: 用户ID
            db: 数据库会话
            
        Returns:
            Dict[str, Any]: 知识库搜索结果和RAG回复
        """
        try:
            if not db:
                logger.warning("知识库搜索需要数据库会话")
                return {"success": False, "error": "缺少数据库会话"}
            
            project_id = project_id or user_context.get('project_id')
            if isinstance(project_id, str):
                project_id = uuid.UUID(project_id)
            
            # 使用增强的知识库搜索
            from services.ai.knowledge_service import AIKnowledgeService
            
            search_result = await AIKnowledgeService.enhanced_search_with_reranking(
                db=db,
                query=message,
                project_id=project_id,
                top_k=5,
                similarity_threshold=0.7,
                user_id=user_id,
                enable_semantic_reranking=True,
                query_expansion=True
            )
            
            if not search_result.get('success') or not search_result.get('results'):
                logger.info(f"知识库搜索无结果: {message[:50]}...")
                return {
                    "success": False,
                    "message": "知识库中未找到相关信息",
                    "search_metadata": search_result.get('metadata', {})
                }
            
            knowledge_results = search_result['results']
            search_metadata = search_result['metadata']
            
            # 生成基于知识库的回复
            rag_response = await self._generate_rag_response(
                query=message,
                knowledge_results=knowledge_results,
                intent=intent,
                entities=entities or {},
                user_context=user_context or {}
            )
            
            return {
                "success": True,
                "content": rag_response,
                "knowledge_sources": knowledge_results,
                "search_metadata": search_metadata,
                "total_sources": len(knowledge_results)
            }
            
        except Exception as e:
            logger.error(f"知识库搜索失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "message": "知识库搜索过程中出现错误"
            }
    
    async def _generate_rag_response(
        self,
        query: str,
        knowledge_results: List[Dict[str, Any]],
        intent: str = None,
        entities: Dict[str, Any] = None,
        user_context: Dict[str, Any] = None
    ) -> str:
        """
        基于知识库搜索结果生成RAG回复
        
        Args:
            query: 用户查询
            knowledge_results: 知识库搜索结果
            intent: 意图
            entities: 实体
            user_context: 用户上下文
            
        Returns:
            str: 生成的回复
        """
        try:
            if not knowledge_results:
                return "抱歉，我没有找到相关的信息。请您提供更多详细信息，或者联系人工客服获得帮助。"
            
            # 选择最相关的结果
            best_result = knowledge_results[0]
            
            if best_result["similarity"] < 0.7:
                return "抱歉，我没有找到完全匹配的信息。请您提供更多详细信息，或者联系人工客服获得帮助。"
            
            # 构建基于知识库的回复
            response_parts = []
            
            # 主要回复内容
            main_content = best_result['content']
            
            # 根据意图和用户角色调整回复语调
            user_role = user_context.get('user_role', 'customer')
            
            if intent == "complaint":
                response_parts.append("我理解您的关切，让我为您查找相关信息。")
                response_parts.append(f"\n{main_content}")
                response_parts.append("\n如果这个信息不能解决您的问题，我可以为您转接到专门的客服人员。")
            
            elif intent == "technical_support":
                response_parts.append("根据技术文档，我为您找到了以下信息：")
                response_parts.append(f"\n{main_content}")
                if len(knowledge_results) > 1:
                    response_parts.append(f"\n另外，{knowledge_results[1]['content'][:100]}...")
                response_parts.append("\n如需更详细的技术支持，请联系我们的技术团队。")
            
            elif user_role in ['store_manager', 'purchase_manager', 'finance_manager']:
                response_parts.append("根据相关政策和操作指南：")
                response_parts.append(f"\n{main_content}")
                if len(knowledge_results) > 1:
                    response_parts.append(f"\n补充信息：{knowledge_results[1]['content'][:150]}...")
            
            else:
                # 普通客户
                response_parts.append("根据我的了解：")
                response_parts.append(f"\n{main_content}")
                
                # 如果有多个相关结果，提供补充信息
                if len(knowledge_results) > 1 and knowledge_results[1]["similarity"] > 0.6:
                    response_parts.append(f"\n\n另外，{knowledge_results[1]['content'][:200]}...")
            
            # 添加来源信息
            if best_result.get("document_title"):
                response_parts.append(f"\n\n（参考资料：{best_result['document_title']}）")
            
            # 如果有多个高质量来源，显示数量
            high_quality_sources = [r for r in knowledge_results if r["similarity"] > 0.8]
            if len(high_quality_sources) > 1:
                response_parts.append(f"\n\n💡 找到 {len(high_quality_sources)} 条相关信息")
            
            return "".join(response_parts)
            
        except Exception as e:
            logger.error(f"生成RAG回复失败: {e}")
            return "抱歉，处理知识库信息时出现了错误。请稍后再试或联系人工客服。"

    def _safe_uuid_convert(self, value):
        """安全地转换UUID，处理各种输入格式"""
        if value is None:
            return None
        
        try:
            if isinstance(value, uuid.UUID):
                return value
            elif isinstance(value, str) and value.strip():
                return uuid.UUID(value.strip())
            else:
                logger.warning(f"无效的UUID值类型: {type(value)}, 值: {value}")
                return None
        except (ValueError, TypeError) as e:
            logger.warning(f"UUID转换失败: {value}, 错误: {e}")
            return None
