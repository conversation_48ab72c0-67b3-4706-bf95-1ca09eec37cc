// AI识别数据解析测试工具
// 用于验证修复后的AI识别效果

const testAIRecognitionData = {
  // 模拟AI识别的原始数据
  mockData: {
    columns: ["商品名称", "单位", "规格", "单价", "张斌桥"],
    data: [
      {
        "商品名称": "云南杨梅",
        "单位": "公斤", 
        "规格": "1.9",
        "单价": 64,
        "张斌桥": 8
      },
      {
        "商品名称": "本地杨梅",
        "单位": "公斤",
        "规格": "7.5", 
        "单价": 134,
        "张斌桥": 2
      },
      {
        "商品名称": "白枇杷A果",
        "单位": "公斤",
        "规格": "11.5",
        "单价": 36,
        "张斌桥": 1
      }
    ]
  },

  // 测试商品标准化处理
  normalizeProductInfo: (productName, specification = "") => {
    const name = String(productName).trim();
    let spec = String(specification).trim();
    
    // 提取规格信息（如果规格为空，尝试从名称中提取）
    if (!spec && name) {
      const specPatterns = [
        /\(([^)]+)\)/,  // 括号内容
        /（([^）]+)）/,  // 中文括号
        /(\d+\.?\d*[a-zA-Z\u4e00-\u9fa5]+)/,  // 数字+单位
      ];
      
      for (const pattern of specPatterns) {
        const matches = name.match(pattern);
        if (matches) {
          spec = matches[1];
          break;
        }
      }
    }
    
    return {
      name,
      specification: spec,
      unique_key: spec ? `${name}|${spec}` : name
    };
  },

  // 测试采购数据处理
  processPurchaseData: (mockData) => {
    const { columns, data } = mockData;
    
    // 识别列类型
    const productColumns = new Set(["商品名称", "产品名称", "商品", "product_name"]);
    const unitColumns = new Set(["单位", "unit"]);
    const specColumns = new Set(["规格", "specification", "spec"]);
    const priceColumns = new Set(["单价", "价格", "unit_price", "price"]);
    const quantityColumns = new Set(["数量", "合计", "总数", "quantity", "total", "总计"]);
    
    // 识别分拨目标列
    const distributionTargets = columns.filter(col => 
      !productColumns.has(col) && 
      !unitColumns.has(col) && 
      !specColumns.has(col) && 
      !priceColumns.has(col) && 
      !quantityColumns.has(col) &&
      col.trim()
    );
    
    console.log('识别的分拨目标列:', distributionTargets);
    
    const purchaseItems = [];
    let purchaseTotal = 0;
    
    data.forEach((row, idx) => {
      const productName = this.findColumnValue(row, productColumns);
      const unit = this.findColumnValue(row, unitColumns);
      const specification = this.findColumnValue(row, specColumns);
      const unitPrice = this.parseNumber(this.findColumnValue(row, priceColumns));
      
      // 计算采购数量：累加分拨数量
      let quantity = 0;
      distributionTargets.forEach(target => {
        quantity += this.parseNumber(row[target] || 0);
      });
      
      if (!productName || quantity <= 0) return;
      
      // 标准化商品信息
      const normalizedProduct = this.normalizeProductInfo(productName, specification);
      
      const totalAmount = unitPrice * quantity;
      
      const item = {
        index: idx,
        product_name: normalizedProduct.name,
        product_code: "",
        product_unit: unit || "",
        product_specification: normalizedProduct.specification,
        quantity,
        unit_price: unitPrice,
        total_amount: totalAmount,
        unique_key: normalizedProduct.unique_key
      };
      
      purchaseItems.push(item);
      purchaseTotal += totalAmount;
    });
    
    return { purchaseItems, purchaseTotal };
  },

  // 测试分拨数据处理
  processDistributionData: (mockData) => {
    const { columns, data } = mockData;
    
    const productColumns = new Set(["商品名称", "产品名称", "商品", "product_name"]);
    const unitColumns = new Set(["单位", "unit"]);
    const specColumns = new Set(["规格", "specification", "spec"]);
    const priceColumns = new Set(["单价", "价格", "unit_price", "price"]);
    const quantityColumns = new Set(["数量", "合计", "总数", "quantity", "total", "总计"]);
    
    // 识别分拨目标列
    const distributionTargets = columns.filter(col => 
      !productColumns.has(col) && 
      !unitColumns.has(col) && 
      !specColumns.has(col) && 
      !priceColumns.has(col) && 
      !quantityColumns.has(col) &&
      col.trim()
    );
    
    const distributionDestinations = [];
    
    distributionTargets.forEach(targetName => {
      const targetItems = [];
      let targetTotal = 0;
      
      data.forEach(row => {
        const quantity = this.parseNumber(row[targetName] || 0);
        
        if (quantity > 0) {
          const productName = this.findColumnValue(row, productColumns);
          const unit = this.findColumnValue(row, unitColumns);
          const specification = this.findColumnValue(row, specColumns);
          const unitPrice = this.parseNumber(this.findColumnValue(row, priceColumns));
          
          if (!productName) return;
          
          // 标准化商品信息
          const normalizedProduct = this.normalizeProductInfo(productName, specification);
          
          const totalAmount = quantity * unitPrice;
          
          targetItems.push({
            product_name: normalizedProduct.name,
            product_specification: normalizedProduct.specification,
            product_unit: unit || "",
            quantity,
            unit_price: unitPrice,
            total_amount: totalAmount,
            unique_key: normalizedProduct.unique_key
          });
          
          targetTotal += totalAmount;
        }
      });
      
      if (targetItems.length > 0) {
        distributionDestinations.push({
          type: "store", // 默认为门店
          target_id: null,
          target_name: targetName.trim(),
          matched_name: null,
          confidence: 0.0,
          items: targetItems,
          total_amount: targetTotal
        });
      }
    });
    
    return distributionDestinations;
  },

  // 辅助函数
  findColumnValue: (row, possibleColumns) => {
    for (const col of possibleColumns) {
      if (row.hasOwnProperty(col) && row[col] !== null && row[col] !== undefined) {
        return row[col];
      }
    }
    return null;
  },

  parseNumber: (value) => {
    if (typeof value === 'number') return value;
    if (typeof value === 'string') {
      const num = parseFloat(value.replace(/[^\d.-]/g, ''));
      return isNaN(num) ? 0 : num;
    }
    return 0;
  },

  // 运行测试
  runTest: () => {
    console.log("=== AI识别数据解析测试 ===");
    console.log("原始数据:", testAIRecognitionData.mockData);
    
    // 测试采购数据处理
    const purchaseResult = testAIRecognitionData.processPurchaseData(testAIRecognitionData.mockData);
    console.log("\n采购数据处理结果:", purchaseResult);
    
    // 测试分拨数据处理
    const distributionResult = testAIRecognitionData.processDistributionData(testAIRecognitionData.mockData);
    console.log("\n分拨数据处理结果:", distributionResult);
    
    // 验证结果
    console.log("\n=== 验证结果 ===");
    console.log(`采购商品数量: ${purchaseResult.purchaseItems.length} (期望: 3)`);
    console.log(`分拨目标数量: ${distributionResult.length} (期望: 1, 目标名称: 张斌桥)`);
    
    if (distributionResult.length > 0) {
      console.log(`分拨目标: ${distributionResult[0].target_name}`);
      console.log(`分拨商品数量: ${distributionResult[0].items.length}`);
    }
    
    return {
      purchaseResult,
      distributionResult,
      success: purchaseResult.purchaseItems.length === 3 && distributionResult.length === 1
    };
  }
};

// 如果在浏览器环境中，可以直接运行测试
if (typeof window !== 'undefined') {
  window.testAIRecognition = testAIRecognitionData;
  console.log("AI识别测试工具已加载，使用 window.testAIRecognition.runTest() 运行测试");
}

export default testAIRecognitionData; 