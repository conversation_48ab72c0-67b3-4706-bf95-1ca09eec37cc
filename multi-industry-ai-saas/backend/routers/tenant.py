#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
租户相关API路由
"""

import logging
from fastapi import APIRouter, Depends, HTTPException, status, Query, Path
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import update, delete, and_
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
import uuid
from datetime import datetime
import random
import json

from db.database import get_db
from models.tenant import Tenant
from models.project_user import ProjectUser
from models.project import Project
from models.user import User
from schemas.tenant import TenantCreate, TenantUpdate, TenantResponse
from core.auth import get_current_user, get_current_tenant_admin
from core.tenant_context import get_tenant_context

# 配置日志
logger = logging.getLogger(__name__)

router = APIRouter(prefix="/tenant", tags=["租户"])

# 项目模型
class ProjectBase(BaseModel):
    name: str
    description: Optional[str] = None
    industry_type: str
    max_users: Optional[int] = 5
    max_storage_gb: Optional[int] = 1
    settings: Optional[Dict[str, Any]] = {}
    plugins: Optional[List[str]] = []

class ProjectCreate(ProjectBase):
    pass

class ProjectUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    industry_type: Optional[str] = None
    status: Optional[str] = None
    max_users: Optional[int] = None
    max_storage_gb: Optional[int] = None
    settings: Optional[Dict[str, Any]] = None
    plugins: Optional[List[str]] = None

class ProjectResponse(ProjectBase):
    id: uuid.UUID
    tenant_id: uuid.UUID
    status: str
    created_at: datetime
    updated_at: datetime

    model_config = {"from_attributes": True}

class ProjectUserBase(BaseModel):
    user_id: uuid.UUID
    role: str

class ProjectUserCreate(ProjectUserBase):
    pass

class ProjectUserResponse(ProjectUserBase):
    id: uuid.UUID
    project_id: uuid.UUID
    created_at: datetime
    updated_at: datetime

    model_config = {"from_attributes": True}


@router.get("/info", response_model=TenantResponse)
async def get_tenant_info(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """获取当前用户所属租户的信息"""
    if not current_user.tenant_id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="当前用户没有关联的租户",
        )

    # 查询租户信息
    result = await db.execute(select(Tenant).where(Tenant.id == current_user.tenant_id))
    tenant = result.scalars().first()

    if not tenant:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="租户不存在",
        )

    return tenant


@router.get("/projects", response_model=List[ProjectResponse])
async def get_tenant_projects(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    status: Optional[str] = Query(None),
    industry_type: Optional[str] = Query(None),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    获取当前租户的项目列表
    """
    try:
        # 获取租户ID
        tenant_id = current_user.tenant_id
        tenant_context = get_tenant_context()

        if tenant_context and tenant_context.tenant_id:
            tenant_id = tenant_context.tenant_id

        if not tenant_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="未找到租户信息",
            )

        # 构建查询 - 使用显式列选择，暂时不包括 code 字段
        query = select(
            Project.id,
            Project.tenant_id,
            Project.name,
            Project.description,
            Project.industry_type,
            Project.logo_url,
            Project.status,
            Project.settings,
            Project.created_at,
            Project.updated_at
        ).where(Project.tenant_id == tenant_id)

        # 应用过滤条件
        if status:
            query = query.where(Project.status == status)

        if industry_type:
            query = query.where(Project.industry_type == industry_type)

        # 应用分页
        query = query.offset(skip).limit(limit)

        # 执行查询
        result = await db.execute(query)
        projects_data = result.fetchall()

        # 转换为 Project 对象
        projects = []
        for row in projects_data:
            project = Project(
                id=row[0],
                tenant_id=row[1],
                name=row[2],
                description=row[3],
                industry_type=row[4],
                logo_url=row[5],
                status=row[6],
                settings=row[7],
                created_at=row[8],
                updated_at=row[9],
                code="temp_code"  # 临时添加 code 字段
            )
            projects.append(project)

        # 如果是普通用户，只返回有权限访问的项目
        if not current_user.is_tenant_admin:
            # 查询用户有权限的项目
            result = await db.execute(
                select(ProjectUser.project_id).where(ProjectUser.user_id == current_user.id)
            )
            allowed_project_ids = [row[0] for row in result]

            # 过滤项目
            projects = [p for p in projects if p.id in allowed_project_ids]

        return projects
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取项目列表失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取项目列表失败: {str(e)}",
        )


@router.get("/projects/{project_id}", response_model=ProjectResponse)
async def get_tenant_project(
    project_id: uuid.UUID = Path(...),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    获取项目详情
    """
    try:
        # 查询项目 - 使用显式列选择，暂时不包括 code 字段
        query = select(
            Project.id,
            Project.tenant_id,
            Project.name,
            Project.description,
            Project.industry_type,
            Project.logo_url,
            Project.status,
            Project.settings,
            Project.created_at,
            Project.updated_at
        ).where(Project.id == project_id)

        result = await db.execute(query)
        project_data = result.fetchone()

        if not project_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="项目不存在",
            )

        # 转换为 Project 对象
        project = Project(
            id=project_data[0],
            tenant_id=project_data[1],
            name=project_data[2],
            description=project_data[3],
            industry_type=project_data[4],
            logo_url=project_data[5],
            status=project_data[6],
            settings=project_data[7],
            created_at=project_data[8],
            updated_at=project_data[9],
            code="temp_code"  # 临时添加 code 字段
        )

        # 检查权限
        if current_user.tenant_id != project.tenant_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有权限访问此项目",
            )

        # 如果不是租户管理员，检查是否有项目权限
        if not current_user.is_tenant_admin:
            result = await db.execute(
                select(ProjectUser).where(
                    ProjectUser.project_id == project_id,
                    ProjectUser.user_id == current_user.id,
                )
            )
            project_user = result.scalars().first()

            if not project_user:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="没有权限访问此项目",
                )

        return project
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取项目详情失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取项目详情失败: {str(e)}",
        )


@router.post("/projects", response_model=ProjectResponse, status_code=status.HTTP_201_CREATED)
async def create_tenant_project(
    project: ProjectCreate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_tenant_admin),
):
    """
    创建项目（租户管理员权限）
    """
    try:
        # 获取租户ID
        tenant_id = current_user.tenant_id
        tenant_context = get_tenant_context()

        if tenant_context and tenant_context.tenant_id:
            tenant_id = tenant_context.tenant_id

        if not tenant_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="未找到租户信息",
            )

        # 检查租户是否存在
        result = await db.execute(select(Tenant).where(Tenant.id == tenant_id))
        tenant = result.scalars().first()

        if not tenant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="租户不存在",
            )

        # 检查项目数量是否超过限制
        result = await db.execute(select(Project).where(Project.tenant_id == tenant_id))
        existing_projects = result.scalars().all()

        if len(existing_projects) >= tenant.max_projects:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"项目数量已达到上限: {tenant.max_projects}",
            )

        # 兜底生成 code
        project_code = getattr(project, "code", None)
        if not project_code or not str(project_code).strip():
            # 生成9-11位不重复数字串
            while True:
                code_candidate = str(random.randint(10**8, 10**11-1))
                result = await db.execute(select(Project).where(Project.code == code_candidate))
                if not result.scalars().first():
                    project_code = code_candidate
                    break
        # 兜底处理 plugins
        plugins = project.plugins
        if isinstance(plugins, str):
            try:
                plugins = json.loads(plugins)
            except Exception:
                plugins = []
        if plugins is None:
            plugins = []
        # 兜底处理 settings
        settings = project.settings
        if isinstance(settings, str):
            try:
                settings = json.loads(settings)
            except Exception:
                settings = {}
        if settings is None:
            settings = {}
        # 创建项目
        db_project = Project(
            id=uuid.uuid4(),
            tenant_id=tenant_id,
            name=project.name,
            code=project_code,  # 一定要传 code
            description=project.description,
            industry_type=project.industry_type,
            max_users=project.max_users,
            max_storage_gb=project.max_storage_gb,
            settings=settings,
            plugins=plugins,
        )

        db.add(db_project)
        await db.commit()
        await db.refresh(db_project)

        # 创建项目角色
        from models.role import Role
        from services.role import RoleService
        from schemas.role import RoleCreate

        try:
            # 1. 创建项目管理员角色
            project_admin_role = await RoleService.get_role_by_code(db, db_project.id, "project_admin")

            if not project_admin_role:
                project_admin_role = await RoleService.create_role(
                    db,
                    RoleCreate(
                        project_id=db_project.id,
                        name="项目管理员",
                        code="project_admin",
                        description="项目管理员，拥有项目的所有权限",
                        permissions={
                            "project_view": True,
                            "project_edit": True,
                            "project_admin": True,
                            "user_manage": True,
                            "role_manage": True,
                            "store_manage": True,
                            "product_manage": True,
                            "inventory_manage": True,
                            "purchase_manage": True,
                            "finance_manage": True,
                            "report_view": True
                        },
                        is_system_role=True
                    )
                )
                logger.info(f"创建项目管理员角色成功: {project_admin_role.id}")

            # 2. 创建门店管理员角色
            store_admin_role = await RoleService.get_role_by_code(db, db_project.id, "store_admin")

            if not store_admin_role:
                store_admin_role = await RoleService.create_role(
                    db,
                    RoleCreate(
                        project_id=db_project.id,
                        name="门店管理员",
                        code="store_admin",
                        description="门店管理员，负责管理门店相关业务",
                        permissions={
                            "store_view": True,
                            "store_edit": True,
                            "store_report": True,
                            "store_staff_manage": True
                        },
                        is_system_role=True
                    )
                )
                logger.info(f"创建门店管理员角色成功: {store_admin_role.id}")

            # 3. 创建仓库主管角色
            inventory_manager_role = await RoleService.get_role_by_code(db, db_project.id, "inventory_manager")

            if not inventory_manager_role:
                inventory_manager_role = await RoleService.create_role(
                    db,
                    RoleCreate(
                        project_id=db_project.id,
                        name="仓库主管",
                        code="inventory_manager",
                        description="仓库主管，负责全面仓储管理",
                        permissions={
                            "inventory_view": True,
                            "inventory_edit": True,
                            "inventory_approve": True,
                            "inventory_staff_manage": True
                        },
                        status="active",
                        is_system_role=True
                    )
                )
                logger.info(f"创建仓库主管角色成功: {inventory_manager_role.id}")
                
            # 4. 创建仓管员角色
            inventory_staff_role = await RoleService.get_role_by_code(db, db_project.id, "inventory_staff")

            if not inventory_staff_role:
                inventory_staff_role = await RoleService.create_role(
                    db,
                    RoleCreate(
                        project_id=db_project.id,
                        name="仓管员",
                        code="inventory_staff",
                        description="仓管员，负责日常仓库和库存管理",
                        permissions={
                            "inventory_view": True,
                            "inventory_edit": True,
                            "inventory_check": True
                        },
                        status="active",
                        is_system_role=True
                    )
                )
                logger.info(f"创建仓管员角色成功: {inventory_staff_role.id}")

            # 5. 创建普通用户角色
            user_role = await RoleService.get_role_by_code(db, db_project.id, "user")

            if not user_role:
                user_role = await RoleService.create_role(
                    db,
                    RoleCreate(
                        project_id=db_project.id,
                        name="普通用户",
                        code="user",
                        description="普通用户，拥有基本权限",
                        permissions={
                            "project_view": True
                        },
                        is_system_role=True
                    )
                )
                logger.info(f"创建普通用户角色成功: {user_role.id}")

            # 提交角色创建事务
            await db.commit()

            # 5. 创建项目用户关联（将当前用户添加为项目管理员）
            # 先检查是否已存在关联
            result = await db.execute(
                select(ProjectUser)
                .where(
                    and_(
                        ProjectUser.user_id == current_user.id,
                        ProjectUser.project_id == db_project.id
                    )
                )
            )
            existing_project_user = result.scalars().first()

            if not existing_project_user:
                project_user = ProjectUser(
                    id=uuid.uuid4(),
                    project_id=db_project.id,
                    user_id=current_user.id,
                    role_id=project_admin_role.id,
                    is_admin=True,  # 设置为项目管理员
                    status="active"
                )
                db.add(project_user)
                await db.commit()
                logger.info(f"创建项目用户关联成功: {project_user.id}")
            else:
                logger.info(f"项目用户关联已存在: {existing_project_user.id}")
                # 确保角色是项目管理员
                if existing_project_user.role_id != project_admin_role.id or not existing_project_user.is_admin:
                    existing_project_user.role_id = project_admin_role.id
                    existing_project_user.is_admin = True
                    db.add(existing_project_user)
                    await db.commit()
                    logger.info(f"更新项目用户关联为项目管理员: {existing_project_user.id}")

            # 6. 验证关联是否创建成功
            result = await db.execute(
                select(ProjectUser)
                .where(
                    and_(
                        ProjectUser.user_id == current_user.id,
                        ProjectUser.project_id == db_project.id
                    )
                )
            )
            project_user_check = result.scalars().first()

            if project_user_check:
                logger.info(f"项目用户关联验证成功: {project_user_check.id}, 角色ID: {project_user_check.role_id}, 是否管理员: {project_user_check.is_admin}")
            else:
                logger.error("项目用户关联创建失败，未找到关联记录")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="项目用户关联创建失败"
                )
        except Exception as e:
            logger.error(f"创建项目角色和用户关联失败: {e}")
            await db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"创建项目角色和用户关联失败: {str(e)}"
            )

        logger.info(f"项目创建成功: {db_project.id}")
        return db_project
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"创建项目失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建项目失败: {str(e)}",
        )


@router.put("/projects/{project_id}", response_model=ProjectResponse)
async def update_tenant_project(
    project_id: uuid.UUID = Path(...),
    project_update: ProjectUpdate = None,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    更新项目信息
    """
    try:
        # 查询项目
        result = await db.execute(select(Project).where(Project.id == project_id))
        db_project = result.scalars().first()

        if not db_project:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="项目不存在",
            )

        # 检查权限
        if current_user.tenant_id != db_project.tenant_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有权限更新此项目",
            )

        # 如果不是租户管理员，检查是否有项目管理权限
        if not current_user.is_tenant_admin:
            result = await db.execute(
                select(ProjectUser).where(
                    ProjectUser.project_id == project_id,
                    ProjectUser.user_id == current_user.id,
                    ProjectUser.is_admin == True,
                )
            )
            project_admin = result.scalars().first()

            if not project_admin:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="没有权限更新此项目",
                )

        # 更新项目信息
        update_data = project_update.dict(exclude_unset=True)

        if update_data:
            await db.execute(
                update(Project)
                .where(Project.id == project_id)
                .values(**update_data)
            )
            await db.commit()

        # 重新查询项目
        result = await db.execute(select(Project).where(Project.id == project_id))
        updated_project = result.scalars().first()

        return updated_project
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"更新项目失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新项目失败: {str(e)}",
        )


@router.delete("/projects/{project_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_tenant_project(
    project_id: uuid.UUID = Path(...),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_tenant_admin),
):
    """
    删除项目（租户管理员权限）
    """
    try:
        # 查询项目
        result = await db.execute(select(Project).where(Project.id == project_id))
        db_project = result.scalars().first()

        if not db_project:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="项目不存在",
            )

        # 检查权限
        if current_user.tenant_id != db_project.tenant_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="没有权限删除此项目",
            )

        # 删除项目
        await db.execute(delete(Project).where(Project.id == project_id))
        await db.commit()

        logger.info(f"项目删除成功: {project_id}")
        return None
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"删除项目失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除项目失败: {str(e)}",
        )
