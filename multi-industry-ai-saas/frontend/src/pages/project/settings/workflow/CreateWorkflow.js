import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Input,
  Button,
  Select,
  Switch,
  message,
  Space,
  Divider,
  Typography,
  Steps,
  Row,
  Col,
  Table,
  Modal,
  Radio,
  Tooltip
} from 'antd';
import {
  PlusOutlined,
  SaveOutlined,
  ArrowLeftOutlined,
  DeleteOutlined,
  EditOutlined,
  QuestionCircleOutlined,
  NodeIndexOutlined,
  BranchesOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import apiService from '../../../../services/api';

const { Title, Text } = Typography;
const { Option } = Select;
const { Step } = Steps;
const { TextArea } = Input;

/**
 * 创建工作流组件
 */
const CreateWorkflow = () => {
  const [current, setCurrent] = useState(0);
  const [loading, setLoading] = useState(false);
  const [basicForm] = Form.useForm();
  const [nodeForm] = Form.useForm();
  const [transitionForm] = Form.useForm();
  const [nodes, setNodes] = useState([]);
  const [transitions, setTransitions] = useState([]);
  const [editingNode, setEditingNode] = useState(null);
  const [editingTransition, setEditingTransition] = useState(null);
  const [nodeModalVisible, setNodeModalVisible] = useState(false);
  const [transitionModalVisible, setTransitionModalVisible] = useState(false);
  const navigate = useNavigate();

  // 工作流类型选项
  const workflowTypes = [
    { value: 'loss_report', label: '报损工作流' },
    { value: 'purchase_order', label: '采购工作流' },
    { value: 'inventory_check', label: '盘点工作流' },
    { value: 'custom', label: '自定义工作流' }
  ];

  // 节点类型选项
  const nodeTypes = [
    { value: 'start', label: '开始节点' },
    { value: 'approval', label: '审批节点' },
    { value: 'notification', label: '通知节点' },
    { value: 'condition', label: '条件节点' },
    { value: 'end', label: '结束节点' }
  ];

  // 角色选项
  const roleOptions = [
    { value: 'admin', label: '管理员' },
    { value: 'store', label: '门店' },
    { value: 'purchase', label: '采购' },
    { value: 'finance', label: '财务' },
    { value: 'warehouse', label: '仓库' },
    { value: 'operation', label: '运营' }
  ];

  // 处理步骤变化
  const handleStepChange = (value) => {
    setCurrent(value);
  };

  // 处理基本信息保存
  const handleBasicInfoSave = async () => {
    try {
      const values = await basicForm.validateFields();
      console.log('基本信息:', values);
      setCurrent(1);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  // 处理添加节点
  const handleAddNode = () => {
    setEditingNode(null);
    nodeForm.resetFields();
    
    // 设置默认值
    nodeForm.setFieldsValue({
      type: 'approval',
      order: nodes.length + 1,
      config: {
        roles: ['admin'],
        notify: {
          enabled: true,
          roles: ['admin'],
          title: '',
          template: ''
        }
      }
    });
    
    setNodeModalVisible(true);
  };

  // 处理编辑节点
  const handleEditNode = (node) => {
    setEditingNode(node);
    
    // 设置表单值
    nodeForm.setFieldsValue({
      name: node.name,
      code: node.code,
      description: node.description,
      type: node.type,
      order: node.order,
      config: node.config
    });
    
    setNodeModalVisible(true);
  };

  // 处理删除节点
  const handleDeleteNode = (nodeId) => {
    // 删除节点
    setNodes(nodes.filter(node => node.id !== nodeId));
    
    // 删除相关的转换
    setTransitions(transitions.filter(
      transition => transition.source_node_id !== nodeId && transition.target_node_id !== nodeId
    ));
  };

  // 处理保存节点
  const handleSaveNode = async () => {
    try {
      const values = await nodeForm.validateFields();
      
      // 生成唯一ID
      const nodeId = editingNode ? editingNode.id : `node_${Date.now()}`;
      
      // 创建节点对象
      const node = {
        id: nodeId,
        name: values.name,
        code: values.code,
        description: values.description,
        type: values.type,
        order: values.order,
        config: values.config
      };
      
      // 更新节点列表
      if (editingNode) {
        setNodes(nodes.map(n => n.id === nodeId ? node : n));
      } else {
        setNodes([...nodes, node]);
      }
      
      setNodeModalVisible(false);
      message.success(`${editingNode ? '更新' : '添加'}节点成功`);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  // 处理添加转换
  const handleAddTransition = () => {
    if (nodes.length < 2) {
      message.warning('请先添加至少两个节点');
      return;
    }
    
    setEditingTransition(null);
    transitionForm.resetFields();
    
    // 设置默认值
    transitionForm.setFieldsValue({
      source_node_id: nodes[0].id,
      target_node_id: nodes[1].id,
      name: '默认转换'
    });
    
    setTransitionModalVisible(true);
  };

  // 处理编辑转换
  const handleEditTransition = (transition) => {
    setEditingTransition(transition);
    
    // 设置表单值
    transitionForm.setFieldsValue({
      name: transition.name,
      source_node_id: transition.source_node_id,
      target_node_id: transition.target_node_id,
      condition: transition.condition
    });
    
    setTransitionModalVisible(true);
  };

  // 处理删除转换
  const handleDeleteTransition = (transitionId) => {
    setTransitions(transitions.filter(transition => transition.id !== transitionId));
  };

  // 处理保存转换
  const handleSaveTransition = async () => {
    try {
      const values = await transitionForm.validateFields();
      
      // 检查源节点和目标节点是否相同
      if (values.source_node_id === values.target_node_id) {
        message.error('源节点和目标节点不能相同');
        return;
      }
      
      // 生成唯一ID
      const transitionId = editingTransition ? editingTransition.id : `transition_${Date.now()}`;
      
      // 创建转换对象
      const transition = {
        id: transitionId,
        name: values.name,
        source_node_id: values.source_node_id,
        target_node_id: values.target_node_id,
        condition: values.condition
      };
      
      // 更新转换列表
      if (editingTransition) {
        setTransitions(transitions.map(t => t.id === transitionId ? transition : t));
      } else {
        setTransitions([...transitions, transition]);
      }
      
      setTransitionModalVisible(false);
      message.success(`${editingTransition ? '更新' : '添加'}转换成功`);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  // 处理保存工作流
  const handleSaveWorkflow = async () => {
    try {
      // 验证基本信息
      const basicInfo = await basicForm.validateFields();
      
      // 检查节点和转换
      if (nodes.length === 0) {
        message.error('请添加至少一个节点');
        return;
      }
      
      // 构建工作流数据
      const workflowData = {
        ...basicInfo,
        nodes: nodes.map(node => ({
          name: node.name,
          code: node.code,
          description: node.description,
          type: node.type,
          order: node.order,
          config: node.config
        })),
        transitions: transitions.map(transition => ({
          name: transition.name,
          source_node_id: transition.source_node_id,
          target_node_id: transition.target_node_id,
          condition: transition.condition
        }))
      };
      
      setLoading(true);
      
      // 调用API创建工作流
      const response = await apiService.project.businessSettings.createWorkflow(workflowData);
      
      if (response && response.success) {
        message.success('工作流创建成功');
        navigate('/project/settings/business');
      } else {
        message.error('工作流创建失败');
      }
    } catch (error) {
      console.error('创建工作流失败:', error);
      message.error('创建工作流失败');
    } finally {
      setLoading(false);
    }
  };

  // 节点表格列定义
  const nodeColumns = [
    {
      title: '序号',
      dataIndex: 'order',
      key: 'order',
      width: 80
    },
    {
      title: '节点名称',
      dataIndex: 'name',
      key: 'name'
    },
    {
      title: '节点代码',
      dataIndex: 'code',
      key: 'code'
    },
    {
      title: '节点类型',
      dataIndex: 'type',
      key: 'type',
      render: (type) => {
        const nodeType = nodeTypes.find(item => item.value === type);
        return nodeType ? nodeType.label : type;
      }
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_, record) => (
        <Space size="small">
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEditNode(record)}
          >
            编辑
          </Button>
          <Button
            type="link"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDeleteNode(record.id)}
          >
            删除
          </Button>
        </Space>
      )
    }
  ];

  // 转换表格列定义
  const transitionColumns = [
    {
      title: '转换名称',
      dataIndex: 'name',
      key: 'name'
    },
    {
      title: '源节点',
      dataIndex: 'source_node_id',
      key: 'source_node_id',
      render: (sourceNodeId) => {
        const node = nodes.find(node => node.id === sourceNodeId);
        return node ? node.name : sourceNodeId;
      }
    },
    {
      title: '目标节点',
      dataIndex: 'target_node_id',
      key: 'target_node_id',
      render: (targetNodeId) => {
        const node = nodes.find(node => node.id === targetNodeId);
        return node ? node.name : targetNodeId;
      }
    },
    {
      title: '条件',
      dataIndex: 'condition',
      key: 'condition',
      render: (condition) => condition ? JSON.stringify(condition) : '无条件'
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_, record) => (
        <Space size="small">
          <Button
            type="link"
            icon={<EditOutlined />}
            onClick={() => handleEditTransition(record)}
          >
            编辑
          </Button>
          <Button
            type="link"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDeleteTransition(record.id)}
          >
            删除
          </Button>
        </Space>
      )
    }
  ];

  // 渲染步骤内容
  const renderStepContent = () => {
    switch (current) {
      case 0:
        return renderBasicInfoStep();
      case 1:
        return renderNodesStep();
      case 2:
        return renderTransitionsStep();
      default:
        return null;
    }
  };

  // 渲染基本信息步骤
  const renderBasicInfoStep = () => {
    return (
      <Form
        form={basicForm}
        layout="vertical"
        initialValues={{
          name: '',
          code: '',
          description: '',
          type: 'loss_report',
          is_default: false,
          is_active: true,
          config: {
            notify_enabled: true,
            notify_roles: ['admin'],
            notify_template: ''
          }
        }}
      >
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="name"
              label="工作流名称"
              rules={[{ required: true, message: '请输入工作流名称' }]}
            >
              <Input placeholder="请输入工作流名称" />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="code"
              label="工作流代码"
              rules={[{ required: true, message: '请输入工作流代码' }]}
            >
              <Input placeholder="请输入工作流代码，如custom_workflow" />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="type"
              label="工作流类型"
              rules={[{ required: true, message: '请选择工作流类型' }]}
            >
              <Select placeholder="请选择工作流类型">
                {workflowTypes.map(type => (
                  <Option key={type.value} value={type.value}>{type.label}</Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name={['config', 'notify_enabled']}
              label="启用通知"
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>
          </Col>
        </Row>
        <Form.Item
          name="description"
          label="工作流描述"
        >
          <TextArea rows={4} placeholder="请输入工作流描述" />
        </Form.Item>
        <Form.Item
          noStyle
          shouldUpdate={(prevValues, currentValues) => {
            return prevValues.config?.notify_enabled !== currentValues.config?.notify_enabled;
          }}
        >
          {({ getFieldValue }) => {
            const notifyEnabled = getFieldValue(['config', 'notify_enabled']);
            
            return notifyEnabled ? (
              <>
                <Form.Item
                  name={['config', 'notify_roles']}
                  label="通知角色"
                  rules={[{ required: true, message: '请选择通知角色' }]}
                >
                  <Select mode="multiple" placeholder="请选择通知角色">
                    {roleOptions.map(role => (
                      <Option key={role.value} value={role.value}>{role.label}</Option>
                    ))}
                  </Select>
                </Form.Item>
                <Form.Item
                  name={['config', 'notify_template']}
                  label="通知模板"
                  rules={[{ required: true, message: '请输入通知模板' }]}
                >
                  <TextArea rows={4} placeholder="请输入通知模板，可使用变量，如：{{variable_name}}" />
                </Form.Item>
              </>
            ) : null;
          }}
        </Form.Item>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="is_default"
              label="默认工作流"
              valuePropName="checked"
              tooltip="设置为默认工作流后，同类型的其他工作流将不再是默认工作流"
            >
              <Switch />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="is_active"
              label="启用状态"
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>
          </Col>
        </Row>
        <Form.Item>
          <Space>
            <Button type="primary" onClick={handleBasicInfoSave}>
              下一步
            </Button>
            <Button onClick={() => navigate('/project/settings/business')}>
              取消
            </Button>
          </Space>
        </Form.Item>
      </Form>
    );
  };

  // 渲染节点步骤
  const renderNodesStep = () => {
    return (
      <div>
        <div style={{ marginBottom: 16 }}>
          <Space>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAddNode}
            >
              添加节点
            </Button>
            <Button
              onClick={() => setCurrent(0)}
            >
              上一步
            </Button>
            <Button
              type="primary"
              onClick={() => setCurrent(2)}
              disabled={nodes.length < 2}
            >
              下一步
            </Button>
          </Space>
        </div>
        <Table
          columns={nodeColumns}
          dataSource={nodes}
          rowKey="id"
          pagination={false}
        />
      </div>
    );
  };

  // 渲染转换步骤
  const renderTransitionsStep = () => {
    return (
      <div>
        <div style={{ marginBottom: 16 }}>
          <Space>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAddTransition}
            >
              添加转换
            </Button>
            <Button
              onClick={() => setCurrent(1)}
            >
              上一步
            </Button>
            <Button
              type="primary"
              icon={<SaveOutlined />}
              onClick={handleSaveWorkflow}
              loading={loading}
            >
              保存工作流
            </Button>
          </Space>
        </div>
        <Table
          columns={transitionColumns}
          dataSource={transitions}
          rowKey="id"
          pagination={false}
        />
      </div>
    );
  };

  return (
    <Card
      title={
        <Space>
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={() => navigate('/project/settings/business')}
          >
            返回
          </Button>
          <span>创建工作流</span>
        </Space>
      }
    >
      <Steps current={current} onChange={handleStepChange}>
        <Step title="基本信息" description="设置工作流基本信息" />
        <Step title="节点配置" description="添加工作流节点" />
        <Step title="转换配置" description="设置节点间转换" />
      </Steps>
      <div style={{ marginTop: 24 }}>
        {renderStepContent()}
      </div>

      {/* 节点编辑模态框 */}
      <Modal
        title={`${editingNode ? '编辑' : '添加'}节点`}
        open={nodeModalVisible}
        onOk={handleSaveNode}
        onCancel={() => setNodeModalVisible(false)}
        width={800}
      >
        <Form
          form={nodeForm}
          layout="vertical"
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="节点名称"
                rules={[{ required: true, message: '请输入节点名称' }]}
              >
                <Input placeholder="请输入节点名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="code"
                label="节点代码"
                rules={[{ required: true, message: '请输入节点代码' }]}
              >
                <Input placeholder="请输入节点代码，如approval_node" />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="type"
                label="节点类型"
                rules={[{ required: true, message: '请选择节点类型' }]}
              >
                <Select placeholder="请选择节点类型">
                  {nodeTypes.map(type => (
                    <Option key={type.value} value={type.value}>{type.label}</Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="order"
                label="节点顺序"
                rules={[{ required: true, message: '请输入节点顺序' }]}
              >
                <Input type="number" placeholder="请输入节点顺序" />
              </Form.Item>
            </Col>
          </Row>
          <Form.Item
            name="description"
            label="节点描述"
          >
            <TextArea rows={2} placeholder="请输入节点描述" />
          </Form.Item>
          
          <Divider orientation="left">节点配置</Divider>
          
          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) => {
              return prevValues.type !== currentValues.type;
            }}
          >
            {({ getFieldValue }) => {
              const nodeType = getFieldValue('type');
              
              if (nodeType === 'approval') {
                return (
                  <>
                    <Form.Item
                      name={['config', 'roles']}
                      label="审批角色"
                      rules={[{ required: true, message: '请选择审批角色' }]}
                    >
                      <Select mode="multiple" placeholder="请选择审批角色">
                        {roleOptions.map(role => (
                          <Option key={role.value} value={role.value}>{role.label}</Option>
                        ))}
                      </Select>
                    </Form.Item>
                    <Form.Item
                      name={['config', 'notify', 'enabled']}
                      label="启用通知"
                      valuePropName="checked"
                      initialValue={true}
                    >
                      <Switch />
                    </Form.Item>
                    <Form.Item
                      noStyle
                      shouldUpdate={(prevValues, currentValues) => {
                        return prevValues.config?.notify?.enabled !== currentValues.config?.notify?.enabled;
                      }}
                    >
                      {({ getFieldValue }) => {
                        const notifyEnabled = getFieldValue(['config', 'notify', 'enabled']);
                        
                        return notifyEnabled ? (
                          <>
                            <Form.Item
                              name={['config', 'notify', 'roles']}
                              label="通知角色"
                              rules={[{ required: true, message: '请选择通知角色' }]}
                            >
                              <Select mode="multiple" placeholder="请选择通知角色">
                                {roleOptions.map(role => (
                                  <Option key={role.value} value={role.value}>{role.label}</Option>
                                ))}
                              </Select>
                            </Form.Item>
                            <Form.Item
                              name={['config', 'notify', 'title']}
                              label="通知标题"
                              rules={[{ required: true, message: '请输入通知标题' }]}
                            >
                              <Input placeholder="请输入通知标题" />
                            </Form.Item>
                            <Form.Item
                              name={['config', 'notify', 'template']}
                              label="通知模板"
                              rules={[{ required: true, message: '请输入通知模板' }]}
                            >
                              <TextArea rows={3} placeholder="请输入通知模板，可使用变量，如：{{variable_name}}" />
                            </Form.Item>
                          </>
                        ) : null;
                      }}
                    </Form.Item>
                  </>
                );
              } else if (nodeType === 'notification') {
                return (
                  <>
                    <Form.Item
                      name={['config', 'roles']}
                      label="通知角色"
                      rules={[{ required: true, message: '请选择通知角色' }]}
                    >
                      <Select mode="multiple" placeholder="请选择通知角色">
                        {roleOptions.map(role => (
                          <Option key={role.value} value={role.value}>{role.label}</Option>
                        ))}
                      </Select>
                    </Form.Item>
                    <Form.Item
                      name={['config', 'title']}
                      label="通知标题"
                      rules={[{ required: true, message: '请输入通知标题' }]}
                    >
                      <Input placeholder="请输入通知标题" />
                    </Form.Item>
                    <Form.Item
                      name={['config', 'template']}
                      label="通知模板"
                      rules={[{ required: true, message: '请输入通知模板' }]}
                    >
                      <TextArea rows={3} placeholder="请输入通知模板，可使用变量，如：{{variable_name}}" />
                    </Form.Item>
                  </>
                );
              } else if (nodeType === 'condition') {
                return (
                  <Form.Item
                    name={['config', 'condition']}
                    label="条件表达式"
                    rules={[{ required: true, message: '请输入条件表达式' }]}
                  >
                    <TextArea rows={3} placeholder="请输入条件表达式，如：status === 'approved'" />
                  </Form.Item>
                );
              } else if (nodeType === 'end') {
                return (
                  <Form.Item
                    name={['config', 'notify', 'enabled']}
                    label="启用通知"
                    valuePropName="checked"
                    initialValue={true}
                  >
                    <Switch />
                  </Form.Item>
                );
              }
              
              return null;
            }}
          </Form.Item>
        </Form>
      </Modal>

      {/* 转换编辑模态框 */}
      <Modal
        title={`${editingTransition ? '编辑' : '添加'}转换`}
        open={transitionModalVisible}
        onOk={handleSaveTransition}
        onCancel={() => setTransitionModalVisible(false)}
        width={600}
      >
        <Form
          form={transitionForm}
          layout="vertical"
        >
          <Form.Item
            name="name"
            label="转换名称"
            rules={[{ required: true, message: '请输入转换名称' }]}
          >
            <Input placeholder="请输入转换名称" />
          </Form.Item>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="source_node_id"
                label="源节点"
                rules={[{ required: true, message: '请选择源节点' }]}
              >
                <Select placeholder="请选择源节点">
                  {nodes.map(node => (
                    <Option key={node.id} value={node.id}>{node.name}</Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="target_node_id"
                label="目标节点"
                rules={[{ required: true, message: '请选择目标节点' }]}
              >
                <Select placeholder="请选择目标节点">
                  {nodes.map(node => (
                    <Option key={node.id} value={node.id}>{node.name}</Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Form.Item
            name="condition"
            label={
              <span>
                条件
                <Tooltip title="条件为JSON格式，如：{'status': 'approved'}">
                  <QuestionCircleOutlined style={{ marginLeft: 4 }} />
                </Tooltip>
              </span>
            }
          >
            <TextArea rows={3} placeholder="请输入条件，JSON格式，如：{'status': 'approved'}" />
          </Form.Item>
        </Form>
      </Modal>
    </Card>
  );
};

export default CreateWorkflow;
