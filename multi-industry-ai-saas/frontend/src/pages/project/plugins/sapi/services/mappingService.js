import api from '../../../../../services/api/httpClient';
import { getProjectId } from '../../../../../services/api/httpClient';

/**
 * 获取工具映射列表
 *
 * @param {Object} params 查询参数，支持 page, page_size, search
 * @returns {Promise<Object>} 映射列表
 */
export const getMappingList = async (params = {}) => {
  try {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }

    // 确保参数名称与后端一致
    const queryParams = {
      page: params.page || 1,
      page_size: params.page_size || 10
    };

    // 只有当搜索关键词不为空时才添加搜索参数
    if (params.search) {
      queryParams.search = params.search;
    }

    const response = await api.get(`/project/${projectId}/plugin/sapi/mappings`, { params: queryParams });
    return response;
  } catch (error) {
    console.error('获取工具映射列表失败:', error);
    throw error;
  }
};

/**
 * 创建工具映射
 *
 * @param {Object} data 映射数据
 * @returns {Promise<Object>} 创建结果
 */
export const createMapping = async (data) => {
  try {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    const response = await api.post(`/project/${projectId}/plugin/sapi/mappings`, data);
    return response;
  } catch (error) {
    console.error('创建工具映射失败:', error);
    throw error;
  }
};

/**
 * 更新工具映射
 *
 * @param {string} mappingId 映射ID
 * @param {Object} data 更新数据
 * @returns {Promise<Object>} 更新结果
 */
export const updateMapping = async (mappingId, data) => {
  try {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    const response = await api.put(`/project/${projectId}/plugin/sapi/mappings/${mappingId}`, data);
    return response;
  } catch (error) {
    console.error('更新工具映射失败:', error);
    throw error;
  }
};

/**
 * 删除工具映射
 *
 * @param {string} mappingId 映射ID
 * @returns {Promise<Object>} 删除结果
 */
export const deleteMapping = async (mappingId) => {
  try {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    const response = await api.delete(`/project/${projectId}/plugin/sapi/mappings/${mappingId}`);
    return response;
  } catch (error) {
    console.error('删除工具映射失败:', error);
    throw error;
  }
};

/**
 * 获取工具映射详情
 *
 * @param {string} mappingId 映射ID
 * @returns {Promise<Object>} 映射详情
 */
export const getMappingDetail = async (mappingId) => {
  try {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    const response = await api.get(`/project/${projectId}/plugin/sapi/mappings/${mappingId}`);
    return response;
  } catch (error) {
    console.error('获取工具映射详情失败:', error);
    throw error;
  }
};

/**
 * 测试工具映射
 *
 * @param {string} mappingId 映射ID
 * @param {Object} data 测试数据
 * @returns {Promise<Object>} 测试结果
 */
export const testMapping = async (mappingId, data) => {
  try {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    const response = await api.post(`/project/${projectId}/plugin/sapi/mappings/${mappingId}/test`, data);
    return response;
  } catch (error) {
    console.error('测试工具映射失败:', error);
    throw error;
  }
};

/**
 * 批量创建工具映射
 *
 * @param {Object} data 批量创建数据
 * @returns {Promise<Object>} 创建结果
 */
export const batchCreateMappings = async (data) => {
  try {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    const response = await api.post(`/project/${projectId}/plugin/sapi/mappings/batch`, data);
    return response;
  } catch (error) {
    console.error('批量创建工具映射失败:', error);
    throw error;
  }
};
