#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
修复 sapi_mcp_server_processes 表中的问题
"""

import logging
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text

logger = logging.getLogger(__name__)

async def migrate(db: AsyncSession) -> bool:
    """
    修复 sapi_mcp_server_processes 表中的问题
    
    1. 删除所有 server_id 为 NULL 的记录
    2. 确保 server_id 列有 NOT NULL 约束
    
    Args:
        db: 数据库会话
        
    Returns:
        bool: 迁移是否成功
    """
    try:
        # 检查表是否存在
        result = await db.execute(text("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_name = 'sapi_mcp_server_processes'
            );
        """))
        
        table_exists = result.scalar()
        if not table_exists:
            logger.info("sapi_mcp_server_processes 表不存在，无需修复")
            return True
        
        # 删除所有 server_id 为 NULL 的记录
        await db.execute(text("""
            DELETE FROM sapi_mcp_server_processes
            WHERE server_id IS NULL;
        """))
        
        # 提交更改
        await db.commit()
        logger.info("成功删除 sapi_mcp_server_processes 表中 server_id 为 NULL 的记录")
        
        return True
    except Exception as e:
        await db.rollback()
        logger.error(f"修复 sapi_mcp_server_processes 表失败: {str(e)}")
        return False
