#!/usr/bin/env python
# -*- coding: utf-8 -*-

from typing import Optional, Dict, Any, List
from pydantic import BaseModel, UUID4, validator
from datetime import datetime

# MCP工具基础模型
class MCPToolBase(BaseModel):
    name: str
    display_name: str
    description: Optional[str] = None
    server_url: str
    server_type: str = "sse"  # sse, stdio, streamhttp
    auth_type: str = "none"  # none, api_key, oauth, custom
    auth_config: Optional[Dict[str, Any]] = None
    capabilities: Optional[Dict[str, Any]] = None
    parameters_schema: Optional[Dict[str, Any]] = None
    response_schema: Optional[Dict[str, Any]] = None
    timeout: int = 30
    retry_count: int = 3
    is_enabled: bool = True
    tags: Optional[List[str]] = None
    version: Optional[str] = None

    @validator('server_type')
    def validate_server_type(cls, v):
        allowed_types = ['streamhttp', 'sse', 'stdio']
        if v not in allowed_types:
            raise ValueError(f'server_type must be one of {allowed_types}')
        return v

    @validator('auth_type')
    def validate_auth_type(cls, v):
        allowed_types = ['none', 'api_key', 'oauth', 'custom']
        if v not in allowed_types:
            raise ValueError(f'auth_type must be one of {allowed_types}')
        return v

# 创建MCP工具请求模型
class MCPToolCreate(MCPToolBase):
    project_id: Optional[UUID4] = None
    tenant_id: Optional[UUID4] = None

# 更新MCP工具请求模型
class MCPToolUpdate(BaseModel):
    display_name: Optional[str] = None
    description: Optional[str] = None
    server_url: Optional[str] = None
    server_type: Optional[str] = None
    auth_type: Optional[str] = None
    auth_config: Optional[Dict[str, Any]] = None
    capabilities: Optional[Dict[str, Any]] = None
    parameters_schema: Optional[Dict[str, Any]] = None
    response_schema: Optional[Dict[str, Any]] = None
    timeout: Optional[int] = None
    retry_count: Optional[int] = None
    is_enabled: Optional[bool] = None
    tags: Optional[List[str]] = None
    version: Optional[str] = None

# MCP工具响应模型
class MCPToolResponse(MCPToolBase):
    id: UUID4
    project_id: Optional[UUID4] = None
    tenant_id: Optional[UUID4] = None
    is_builtin: bool
    created_at: datetime
    updated_at: Optional[datetime] = None
    created_by: Optional[UUID4] = None
    updated_by: Optional[UUID4] = None

    class Config:
        orm_mode = True

# MCP工具列表响应模型
class MCPToolListResponse(BaseModel):
    success: bool
    message: str
    data: List[MCPToolResponse]
    total: int
    page: int
    page_size: int

# MCP工具执行请求模型
class MCPToolExecuteRequest(BaseModel):
    tool_id: UUID4
    parameters: Dict[str, Any]
    session_id: Optional[str] = None
    timeout: Optional[int] = None

# MCP工具执行响应模型
class MCPToolExecuteResponse(BaseModel):
    success: bool
    message: str
    data: Optional[Dict[str, Any]] = None
    execution_time: Optional[int] = None
    usage_id: Optional[UUID4] = None

# MCP工具使用记录基础模型
class MCPToolUsageBase(BaseModel):
    tool_id: UUID4

    session_id: Optional[str] = None
    request_data: Optional[Dict[str, Any]] = None
    response_data: Optional[Dict[str, Any]] = None
    status: str = "pending"
    error_message: Optional[str] = None
    execution_time: Optional[int] = None

# MCP工具使用记录响应模型
class MCPToolUsageResponse(MCPToolUsageBase):
    id: UUID4
    project_id: UUID4
    tenant_id: Optional[UUID4] = None
    created_at: datetime
    created_by: Optional[UUID4] = None

    class Config:
        orm_mode = True

# MCP工具使用记录列表响应模型
class MCPToolUsageListResponse(BaseModel):
    success: bool
    message: str
    data: List[MCPToolUsageResponse]
    total: int
    page: int
    page_size: int

# MCP工具测试请求模型
class MCPToolTestRequest(BaseModel):
    server_url: str
    server_type: str = "sse"
    auth_type: str = "none"
    auth_config: Optional[Dict[str, Any]] = None
    timeout: int = 30

# MCP工具测试响应模型
class MCPToolTestResponse(BaseModel):
    success: bool
    message: str
    available_tools: Optional[List[Dict[str, Any]]] = None
    server_info: Optional[Dict[str, Any]] = None
    response_time: Optional[int] = None 