import React, { useState, useEffect } from 'react';
import {
  Table,
  Card,
  Button,
  Space,
  Tag,
  Modal,
  Form,
  Input,
  Select,
  DatePicker,
  Row,
  Col,
  Statistic,
  message,
  Popconfirm,
  Tooltip,
  Typography,
  Divider
} from 'antd';
import {
  EyeOutlined,
  EditOutlined,
  DeleteOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  SearchOutlined,
  ReloadOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import apiService from '../../../services/api';

const { Option } = Select;
const { RangePicker } = DatePicker;
const { Text, Title } = Typography;

const DistributionOrderManagement = ({ stores = [], warehouses = [] }) => {
  const [loading, setLoading] = useState(false);
  const [arrivalList, setArrivalList] = useState([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [currentArrival, setCurrentArrival] = useState(null);
  const [productDetails, setProductDetails] = useState([]);
  const [form] = Form.useForm();
  
  // 关联采购单相关状态
  const [purchaseOrderModalVisible, setPurchaseOrderModalVisible] = useState(false);
  const [relatedPurchaseOrder, setRelatedPurchaseOrder] = useState(null);
  
  const [stats, setStats] = useState({
    total: 0,
    pending: 0,
    confirmed: 0,
    rejected: 0
  });

  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });

  const [filters, setFilters] = useState({
    status: null,
    store_id: null,
    warehouse_id: null,
    date_range: null,
    arrival_number: null,
    purchase_order_number: null
  });

  // 获取分拨订单列表（到货确认单）
  const fetchArrivalList = async (page = 1, pageSize = 10) => {
    try {
      setLoading(true);
      
      const params = {
        page,
        page_size: pageSize
      };

      // 处理过滤条件
      if (filters.status) {
        params.status_filter = filters.status;
      }
      
      if (filters.store_id) {
        params.store_id = filters.store_id;
      }
      
      if (filters.warehouse_id) {
        params.warehouse_id = filters.warehouse_id;
      }

      if (filters.arrival_number) {
        params.arrival_number = filters.arrival_number;
      }

      if (filters.purchase_order_number) {
        params.purchase_order_number = filters.purchase_order_number;
      }

      // 处理日期范围参数
      if (filters.date_range && filters.date_range.length === 2) {
        // 确保日期格式正确
        params.start_date = typeof filters.date_range[0] === 'string' 
          ? filters.date_range[0] 
          : filters.date_range[0].format('YYYY-MM-DD');
        params.end_date = typeof filters.date_range[1] === 'string' 
          ? filters.date_range[1] 
          : filters.date_range[1].format('YYYY-MM-DD');
      }

      console.log('分拨订单管理API请求参数:', params);

      const response = await apiService.project.store_operations.getArrivalList(params);
      
      console.log('分拨订单管理API响应:', response);
      
      if (response && response.success) {
        const arrivalData = response.items || [];
        console.log('分拨单列表数据详情:', arrivalData);
        if (arrivalData.length > 0) {
          console.log('第一个分拨单的关联采购单字段:', {
            purchase_order_id: arrivalData[0].purchase_order_id,
            purchase_order_number: arrivalData[0].purchase_order_number
          });
        }
        setArrivalList(arrivalData);
        setPagination({
          current: page,
          pageSize,
          total: response.total || 0
        });
        
        // 修复：计算统计数据应该基于response中的统计信息，而不是当前页面数据
        if (response.statistics) {
          setStats(response.statistics);
        } else {
          // 如果后端没有返回统计信息，使用基本统计
          const newStats = {
            total: response.total || 0,
            pending: arrivalData.filter(item => item.status === 'pending').length,
            confirmed: arrivalData.filter(item => item.status === 'confirmed').length,
            rejected: arrivalData.filter(item => item.status === 'rejected').length
          };
          setStats(newStats);
        }
        
        console.log('分拨订单列表数据:', arrivalData.length, '条，总计:', response.total);
      } else {
        console.error('获取分拨订单列表失败:', response);
        message.error(response?.message || '获取分拨订单列表失败');
        setArrivalList([]);
        setStats({ total: 0, pending: 0, confirmed: 0, rejected: 0 });
      }
    } catch (error) {
      console.error('获取分拨订单列表失败:', error);
      message.error('获取分拨订单列表失败: ' + (error.response?.data?.detail || error.message));
      setArrivalList([]);
      setStats({ total: 0, pending: 0, confirmed: 0, rejected: 0 });
    } finally {
      setLoading(false);
    }
  };

  // 获取到货详情
  const fetchArrivalDetail = async (arrivalId) => {
    try {
      setLoading(true);
      const response = await apiService.project.store_operations.getArrivalDetail(arrivalId);
      
      if (response && response.success) {
        setProductDetails(response.data.items || []);
      } else {
        message.error(response?.message || '获取到货详情失败');
      }
    } catch (error) {
      console.error('获取到货详情失败:', error);
      message.error('获取到货详情失败');
    } finally {
      setLoading(false);
    }
  };

  // 查看详情
  const handleView = (record) => {
    setCurrentArrival(record);
    fetchArrivalDetail(record.id);
    setModalVisible(true);
  };

  // 查看关联采购单
  const handleViewPurchaseOrder = async (record) => {
    if (!record.purchase_order_id) {
      message.warning('该分拨单没有关联的采购单');
      return;
    }

    try {
      setLoading(true);
      const response = await apiService.project.purchaseOrder.getDetail(record.purchase_order_id);
      
      if (response && response.data) {
        setRelatedPurchaseOrder(response.data);
        setPurchaseOrderModalVisible(true);
      } else {
        message.error('获取关联采购单失败');
      }
    } catch (error) {
      console.error('获取关联采购单失败:', error);
      message.error('获取关联采购单失败');
    } finally {
      setLoading(false);
    }
  };

  // 撤回分拨单（仅限待确认状态）
  const handleCancel = (record) => {
    Modal.confirm({
      title: '撤回分拨单',
      content: '确定要撤回此分拨单吗？撤回后需要重新发送。',
      onOk: async () => {
        try {
          setLoading(true);
          // 调用撤回接口（暂时使用拒绝接口，后续可能需要新增撤回接口）
          const response = await apiService.project.store_operations.rejectArrival(record.id, {
            notes: '采购方撤回分拨单'
          });

          if (response && response.success) {
            message.success('撤回分拨单成功');
            fetchArrivalList();
          } else {
            message.error(response?.message || '撤回分拨单失败');
          }
        } catch (error) {
          console.error('撤回分拨单失败:', error);
          message.error('撤回分拨单失败');
        } finally {
          setLoading(false);
        }
      }
    });
  };

  // 重新发送分拨单（仅限被拒绝状态）
  const handleResend = (record) => {
    Modal.confirm({
      title: '重新发送分拨单',
      content: '确定要重新发送此分拨单吗？',
      onOk: async () => {
        try {
          setLoading(true);
          // 这里应该调用重新发送接口，暂时模拟
          // 实际应该是更新状态为pending或者创建新的分拨单
          message.success('重新发送功能开发中，请联系管理员');
          
          // TODO: 实现重新发送逻辑
          // const response = await apiService.project.purchase.resendDistribution(record.id);
          // if (response && response.success) {
          //   message.success('重新发送分拨单成功');
          //   fetchArrivalList();
          // }
        } catch (error) {
          console.error('重新发送分拨单失败:', error);
          message.error('重新发送分拨单失败');
        } finally {
          setLoading(false);
        }
      }
    });
  };

  // 搜索
  const handleSearch = (values) => {
    console.log('搜索参数:', values);
    
    const newFilters = {
      status: values.status || null,
      store_id: values.store_id || null,
      warehouse_id: values.warehouse_id || null,
      date_range: values.date_range || null,
      arrival_number: values.arrival_number || null,
      purchase_order_number: values.purchase_order_number || null
    };
    
    setFilters(newFilters);
    setPagination(prev => ({ ...prev, current: 1 })); // 重置到第一页
    
    // 手动触发数据获取，使用新的过滤条件
    setTimeout(() => {
      fetchArrivalList(1, pagination.pageSize);
    }, 100);
  };

  // 重置搜索
  const handleReset = () => {
    form.resetFields();
    const resetFilters = {
      status: null,
      store_id: null,
      warehouse_id: null,
      date_range: null,
      arrival_number: null,
      purchase_order_number: null
    };
    setFilters(resetFilters);
    setPagination(prev => ({ ...prev, current: 1 }));
    
    // 重新获取数据
    setTimeout(() => {
      fetchArrivalList(1, pagination.pageSize);
    }, 100);
  };

  // 表格变化处理
  const handleTableChange = (paginationConfig) => {
    fetchArrivalList(paginationConfig.current, paginationConfig.pageSize);
  };

  // 监听过滤条件变化
  useEffect(() => {
    console.log('过滤条件变化，重新获取数据:', filters);
    fetchArrivalList(pagination.current, pagination.pageSize);
  }, [filters]);

  // 初始化数据获取
  useEffect(() => {
    console.log('组件初始化，获取分拨订单列表');
    fetchArrivalList(1, 10);
  }, []);

  const columns = [
    {
      title: '到货单号',
      dataIndex: 'arrival_number',
      key: 'arrival_number',
      render: (text, record) => (
        <Button type="link" onClick={() => handleView(record)}>
          {text}
        </Button>
      )
    },
    {
      title: '关联采购单',
      key: 'purchase_order',
      render: (_, record) => {
        if (record.purchase_order_id && record.purchase_order_number) {
          return (
            <Button 
              type="link" 
              size="small"
              onClick={() => {
                window.open(`/project/purchase/orders/detail/${record.purchase_order_id}`, '_blank');
              }}
            >
              {record.purchase_order_number}
            </Button>
          );
        } else {
          return <Text type="secondary">无</Text>;
        }
      }
    },
    {
      title: '目标类型',
      dataIndex: 'store_id',
      key: 'target_type',
      render: (store_id, record) => (
        <Tag color={store_id ? 'blue' : 'green'}>
          {store_id ? '门店' : '仓库'}
        </Tag>
      )
    },
    {
      title: '目标名称',
      key: 'target_name',
      render: (_, record) => {
        if (record.store_name) {
          return record.store_name;
        }
        if (record.warehouse_name) {
          return record.warehouse_name;
        }
        if (record.store_id) {
          const store = stores.find(s => s.id === record.store_id);
          return store?.name || '未知门店';
        } else if (record.warehouse_id) {
          const warehouse = warehouses.find(w => w.id === record.warehouse_id);
          return warehouse?.name || '未知仓库';
        }
        return '-';
      }
    },
    {
      title: '商品数量',
      dataIndex: 'total_items',
      key: 'total_items',
      align: 'center'
    },
    {
      title: '到货金额',
      dataIndex: 'total_amount',
      key: 'total_amount',
      align: 'right',
      render: (amount) => `¥${parseFloat(amount || 0).toFixed(2)}`
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => {
        const statusConfig = {
          pending: { color: 'warning', text: '待确认' },
          confirmed: { color: 'success', text: '已确认' },
          rejected: { color: 'error', text: '已拒绝' }
        };
        const config = statusConfig[status] || { color: 'default', text: status };
        return <Tag color={config.color}>{config.text}</Tag>;
      }
    },
    {
      title: '到货时间',
      dataIndex: 'arrival_date',
      key: 'arrival_date',
      render: (time) => time ? new Date(time).toLocaleString() : '-'
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              type="text"
              size="small"
              icon={<InfoCircleOutlined />}
              onClick={() => handleView(record)}
            />
          </Tooltip>
          {record.purchase_order_id && (
            <Tooltip title="查看关联采购单">
              <Button
                type="text"
                size="small"
                icon={<EyeOutlined />}
                onClick={() => handleViewPurchaseOrder(record)}
              />
            </Tooltip>
          )}
          {record.status === 'rejected' && (
            <Tooltip title="重新发送">
              <Button
                type="text"
                size="small"
                icon={<ReloadOutlined />}
                onClick={() => handleResend(record)}
              />
            </Tooltip>
          )}
          {record.status === 'pending' && (
            <Tooltip title="撤回分拨单">
              <Button
                type="text"
                size="small"
                danger
                icon={<CloseCircleOutlined />}
                onClick={() => handleCancel(record)}
              />
            </Tooltip>
          )}
        </Space>
      )
    }
  ];

  return (
    <div>
      {/* 搜索表单 */}
      <Card style={{ marginBottom: 16 }}>
        <Form
          form={form}
          layout="inline"
          onFinish={handleSearch}
        >
          <Form.Item name="arrival_number">
            <Input
              placeholder="到货单号"
              prefix={<SearchOutlined />}
              style={{ width: 200 }}
            />
          </Form.Item>
          <Form.Item name="purchase_order_number">
            <Input
              placeholder="采购单号"
              prefix={<SearchOutlined />}
              style={{ width: 200 }}
            />
          </Form.Item>
          <Form.Item name="status">
            <Select placeholder="状态" style={{ width: 120 }} allowClear>
              <Option value="pending">待确认</Option>
              <Option value="confirmed">已确认</Option>
              <Option value="rejected">已拒绝</Option>
            </Select>
          </Form.Item>
          <Form.Item name="store_id">
            <Select placeholder="选择门店" style={{ width: 150 }} allowClear>
              {stores.map(store => (
                <Option key={store.id} value={store.id}>
                  {store.name}
                </Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item name="warehouse_id">
            <Select placeholder="选择仓库" style={{ width: 150 }} allowClear>
              {warehouses.map(warehouse => (
                <Option key={warehouse.id} value={warehouse.id}>
                  {warehouse.name}
                </Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item name="date_range">
            <RangePicker placeholder={['开始日期', '结束日期']} />
          </Form.Item>
          <Form.Item>
            <Button type="primary" htmlType="submit" icon={<SearchOutlined />}>
              搜索
            </Button>
          </Form.Item>
          <Form.Item>
            <Button onClick={handleReset} icon={<ReloadOutlined />}>
              重置
            </Button>
          </Form.Item>
        </Form>
      </Card>

      {/* 统计信息 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={6}>
          <Card>
            <Statistic title="总订单" value={stats.total} />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic title="待确认" value={stats.pending} valueStyle={{ color: '#fa8c16' }} />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic title="已确认" value={stats.confirmed} valueStyle={{ color: '#52c41a' }} />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic title="已拒绝" value={stats.rejected} valueStyle={{ color: '#ff4d4f' }} />
          </Card>
        </Col>
      </Row>

      {/* 分拨订单表格 */}
      <Card>
        <Table
          columns={columns}
          dataSource={arrivalList}
          rowKey="id"
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`
          }}
          onChange={handleTableChange}
          loading={loading}
        />
      </Card>

      {/* 到货详情模态框 */}
      <Modal
        title="到货详情"
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setModalVisible(false)}>
            关闭
          </Button>
        ]}
        width={800}
      >
        {currentArrival && (
          <div>
            <Row gutter={16} style={{ marginBottom: 16 }}>
              <Col span={8}>
                <Text><strong>到货单号：</strong>{currentArrival.arrival_number}</Text>
              </Col>
              <Col span={8}>
                <Text><strong>目标：</strong>
                  {currentArrival.store_id 
                    ? stores.find(s => s.id === currentArrival.store_id)?.name || '未知门店'
                    : warehouses.find(w => w.id === currentArrival.warehouse_id)?.name || '未知仓库'
                  }
                </Text>
              </Col>
              <Col span={8}>
                <Text><strong>状态：</strong>
                  <Tag color={
                    currentArrival.status === 'confirmed' ? 'success' :
                    currentArrival.status === 'rejected' ? 'error' : 'warning'
                  }>
                    {currentArrival.status === 'confirmed' ? '已确认' :
                     currentArrival.status === 'rejected' ? '已拒绝' : '待确认'}
                  </Tag>
                </Text>
              </Col>
            </Row>

            <Divider />

            <Table
              columns={[
                { title: '商品名称', dataIndex: 'product_name', key: 'product_name' },
                { title: '规格', dataIndex: 'specification', key: 'specification' },
                { title: '单位', dataIndex: 'unit', key: 'unit' },
                { title: '应到数量', dataIndex: 'expected_quantity', key: 'expected_quantity' },
                { title: '实收数量', dataIndex: 'actual_quantity', key: 'actual_quantity' },
                { title: '单价', dataIndex: 'price', key: 'price', render: (text) => `¥${parseFloat(text || 0).toFixed(2)}` },
                { title: '金额', dataIndex: 'amount', key: 'amount', render: (text) => `¥${parseFloat(text || 0).toFixed(2)}` },
                {
                  title: '状态',
                  dataIndex: 'is_confirmed',
                  key: 'is_confirmed',
                  render: (text) => text ? <Tag color="green">已确认</Tag> : <Tag color="red">未确认</Tag>
                }
              ]}
              dataSource={productDetails}
              rowKey="id"
              pagination={false}
            />
          </div>
        )}
      </Modal>

      {/* 关联采购单详情模态框 */}
      <Modal
        title={`关联采购单详情 - ${relatedPurchaseOrder?.order_number || ''}`}
        open={purchaseOrderModalVisible}
        onCancel={() => setPurchaseOrderModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setPurchaseOrderModalVisible(false)}>
            关闭
          </Button>,
          <Button 
            key="view" 
            type="primary"
            onClick={() => {
              // 在新窗口中打开采购单详情页
              window.open(`/project/purchase/orders/detail/${relatedPurchaseOrder?.id}`, '_blank');
            }}
          >
            查看完整详情
          </Button>
        ]}
        width={1000}
      >
        {relatedPurchaseOrder && (
          <div>
            <Row gutter={16} style={{ marginBottom: 16 }}>
              <Col span={6}>
                <Text><strong>订单编号：</strong>{relatedPurchaseOrder.order_number}</Text>
              </Col>
              <Col span={6}>
                <Text><strong>订单状态：</strong>
                  <Tag color={
                    relatedPurchaseOrder.status === 'confirmed' ? 'success' :
                    relatedPurchaseOrder.status === 'received' ? 'processing' :
                    relatedPurchaseOrder.status === 'cancelled' ? 'error' : 'default'
                  }>
                    {relatedPurchaseOrder.status === 'confirmed' ? '已确认' :
                     relatedPurchaseOrder.status === 'received' ? '已收货' :
                     relatedPurchaseOrder.status === 'cancelled' ? '已取消' : '草稿'}
                  </Tag>
                </Text>
              </Col>
              <Col span={6}>
                <Text><strong>订单金额：</strong>¥{parseFloat(relatedPurchaseOrder.final_amount || 0).toFixed(2)}</Text>
              </Col>
              <Col span={6}>
                <Text><strong>创建时间：</strong>{relatedPurchaseOrder.created_at ? new Date(relatedPurchaseOrder.created_at).toLocaleString() : '-'}</Text>
              </Col>
            </Row>

            <Divider />

            <div style={{ marginBottom: 16 }}>
              <Text strong>采购商品列表：</Text>
            </div>

            <Table
              columns={[
                {
                  title: '商品信息',
                  key: 'product_info',
                  render: (_, record) => (
                    <div>
                      <div><strong>{record.product_name}</strong></div>
                      {record.product_code && <div style={{ fontSize: '12px', color: '#666' }}>编码: {record.product_code}</div>}
                      {record.product_specification && <div style={{ fontSize: '12px', color: '#666' }}>规格: {record.product_specification}</div>}
                    </div>
                  )
                },
                {
                  title: '供应商',
                  dataIndex: 'supplier_name',
                  key: 'supplier_name',
                  render: (text) => text || '未指定'
                },
                {
                  title: '数量',
                  dataIndex: 'quantity',
                  key: 'quantity',
                  align: 'center'
                },
                {
                  title: '单位',
                  dataIndex: 'product_unit',
                  key: 'product_unit',
                  align: 'center'
                },
                {
                  title: '单价',
                  dataIndex: 'unit_price',
                  key: 'unit_price',
                  align: 'right',
                  render: (price) => `¥${parseFloat(price || 0).toFixed(2)}`
                },
                {
                  title: '小计',
                  dataIndex: 'total_amount',
                  key: 'total_amount',
                  align: 'right',
                  render: (amount) => `¥${parseFloat(amount || 0).toFixed(2)}`
                }
              ]}
              dataSource={relatedPurchaseOrder.items || []}
              rowKey="id"
              pagination={false}
              size="small"
            />
          </div>
        )}
      </Modal>
    </div>
  );
};

export default DistributionOrderManagement; 