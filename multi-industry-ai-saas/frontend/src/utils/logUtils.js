import apiService from '../services/api';

/**
 * 记录操作日志
 * 
 * @param {Object} options - 日志选项
 * @param {string} options.module - 模块名称，如 user, role, store 等
 * @param {string} options.action - 操作类型，如 create, update, delete 等
 * @param {string} options.resourceType - 资源类型，如 user, role, store 等
 * @param {string} options.resourceId - 资源ID，可选
 * @param {string} options.description - 操作描述
 * @param {Object} options.details - 操作详情，可选
 * @returns {Promise} - 返回创建结果
 */
export const logOperation = async (options) => {
  try {
    const { module, action, resourceType, resourceId, description, details } = options;
    
    // 验证必填参数
    if (!module || !action || !resourceType || !description) {
      console.error('记录操作日志失败：缺少必填参数');
      return;
    }
    
    // 创建日志
    const logData = {
      module,
      action,
      resource_type: resourceType,
      description,
    };
    
    // 添加可选参数
    if (resourceId) {
      logData.resource_id = resourceId;
    }
    
    if (details) {
      logData.details = details;
    }
    
    // 调用API创建日志
    return await apiService.project.operationLogs.createLog(logData);
  } catch (error) {
    console.error('记录操作日志失败:', error);
    // 不抛出异常，避免影响主要业务流程
    return null;
  }
};

/**
 * 模块名称映射
 */
export const MODULE_NAMES = {
  user: '用户管理',
  role: '角色管理',
  store: '门店管理',
  warehouse: '仓库管理',
  product: '产品管理',
  supplier: '供应商管理',
  inventory: '库存管理',
  purchase: '采购管理',
  sales: '销售管理',
  system: '系统设置',
  knowledge: '知识库管理',
};

/**
 * 操作类型映射
 */
export const ACTION_NAMES = {
  create: '创建',
  update: '更新',
  delete: '删除',
  login: '登录',
  logout: '登出',
  approve: '审批',
  reject: '拒绝',
};

/**
 * 资源类型映射
 */
export const RESOURCE_TYPE_NAMES = {
  user: '用户',
  role: '角色',
  store: '门店',
  warehouse: '仓库',
  product: '产品',
  supplier: '供应商',
  inventory: '库存',
  purchase_order: '采购单',
  sales_report: '销售报表',
  system_config: '系统配置',
  knowledge_document: '知识文档',
};

const logUtils = {
  logOperation,
  MODULE_NAMES,
  ACTION_NAMES,
  RESOURCE_TYPE_NAMES,
};

export default logUtils;
