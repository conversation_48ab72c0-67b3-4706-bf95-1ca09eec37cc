import React, { useState, useEffect } from 'react';
import {
  Card, Table, Button, Space, Modal, Form, Input, Select, Switch,
  message, Popconfirm, Tag, Tooltip, Badge, Drawer, Typography,
  Row, Col, Statistic, Alert, Tabs, Upload, Divider
} from 'antd';
import {
  PlusOutlined, EditOutlined, DeleteOutlined, PlayCircleOutlined,
  StopOutlined, SettingOutlined, ApiOutlined, CloudServerOutlined,
  BugOutlined, CheckCircleOutlined, ExclamationCircleOutlined,
  InfoCircleOutlined, UploadOutlined, DownloadOutlined,
  ArrowLeftOutlined, EyeOutlined
} from '@ant-design/icons';
import { useProject } from '../../contexts/ProjectContext';
import { useNavigate } from 'react-router-dom';
import apiService from '../../services/api';

const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;
const { TabPane } = Tabs;
const { Option } = Select;

/**
 * MCP服务器管理页面
 */
const MCPToolManagement = () => {
  const { currentProject } = useProject();
  const navigate = useNavigate();
  const [tools, setTools] = useState([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [testModalVisible, setTestModalVisible] = useState(false);
  const [detailDrawerVisible, setDetailDrawerVisible] = useState(false);
  const [editingTool, setEditingTool] = useState(null);
  const [selectedTool, setSelectedTool] = useState(null);
  const [testResult, setTestResult] = useState(null);
  const [testLoading, setTestLoading] = useState(false);
  const [usageStats, setUsageStats] = useState({});
  const [form] = Form.useForm();
  const [testForm] = Form.useForm();
  const [availableTools, setAvailableTools] = useState([]);
  const [selectedServer, setSelectedServer] = useState(null);

  // 获取工具列表
  const fetchTools = async () => {
    try {
      setLoading(true);
      const response = await apiService.project.ai.mcpTools.list(currentProject.id);
      setTools(response.data || []);
    } catch (error) {
      console.error('获取MCP工具列表失败:', error);
      message.error('获取工具列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取使用统计
  const fetchUsageStats = async () => {
    try {
      const response = await apiService.project.ai.mcpTools.getUsageStats(currentProject.id);
      setUsageStats(response.data || {});
    } catch (error) {
      console.error('获取使用统计失败:', error);
    }
  };

  // 获取服务器可用工具
  const fetchServerTools = async (serverId) => {
    try {
      const response = await apiService.project.ai.mcpTools.get(currentProject.id, serverId);
      setAvailableTools(response.data.available_tools || []);
    } catch (error) {
      console.error('获取服务器工具失败:', error);
      message.error('获取服务器工具失败');
    }
  };

  // 显示服务器详情
  const showServerDetail = (server) => {
    setSelectedServer(server);
    fetchServerTools(server.id);
    setDetailDrawerVisible(true);
  };

  // 启用/禁用工具
  const toggleTool = async (serverId, toolName, enabled) => {
    try {
      await apiService.project.ai.mcpTools.update(currentProject.id, serverId, {
        enabled_tools: enabled ? 
          [...(selectedServer.enabled_tools || []), toolName] :
          (selectedServer.enabled_tools || []).filter(t => t !== toolName)
      });
      message.success(`${enabled ? '启用' : '禁用'}工具成功`);
      fetchTools();
      fetchServerTools(serverId);
    } catch (error) {
      console.error('切换工具状态失败:', error);
      message.error('操作失败');
    }
  };

  useEffect(() => {
    if (currentProject?.id) {
      fetchTools();
      fetchUsageStats();
    }
  }, [currentProject]);

  // 创建/更新工具
  const handleSubmit = async (values) => {
    try {
      if (editingTool) {
        await apiService.project.ai.mcpTools.update(currentProject.id, editingTool.id, values);
        message.success('更新工具成功');
      } else {
        await apiService.project.ai.mcpTools.create(currentProject.id, values);
        message.success('创建工具成功');
      }
      
      setModalVisible(false);
      setEditingTool(null);
      form.resetFields();
      fetchTools();
    } catch (error) {
      console.error('保存工具失败:', error);
      message.error(editingTool ? '更新工具失败' : '创建工具失败');
    }
  };

  // 删除工具
  const handleDelete = async (toolId) => {
    try {
      await apiService.project.ai.mcpTools.delete(currentProject.id, toolId);
      message.success('删除工具成功');
      fetchTools();
    } catch (error) {
      console.error('删除工具失败:', error);
      message.error('删除工具失败');
    }
  };

  // 测试工具连接
  const handleTestConnection = async (values) => {
    try {
      setTestLoading(true);
      const response = await apiService.project.ai.mcpTools.testConnection(currentProject.id, values);
      setTestResult(response);
      
      if (response.success) {
        message.success('连接测试成功');
      } else {
        message.error(`连接测试失败: ${response.message}`);
      }
    } catch (error) {
      console.error('测试连接失败:', error);
      message.error('测试连接失败');
      setTestResult({
        success: false,
        message: error.message || '未知错误'
      });
    } finally {
      setTestLoading(false);
    }
  };

  // 执行工具
  const handleExecuteTool = async (tool, parameters = {}) => {
    try {
      const response = await apiService.project.ai.mcpTools.execute(currentProject.id, tool.id, {
        parameters,
        session_id: `session_${Date.now()}`
      });
      
      if (response.success) {
        message.success('工具执行成功');
        Modal.info({
          title: '执行结果',
          content: (
            <div>
              <p><strong>执行时间:</strong> {response.execution_time}ms</p>
              <p><strong>结果:</strong></p>
              <pre style={{ background: '#f5f5f5', padding: '8px', borderRadius: '4px' }}>
                {JSON.stringify(response.data, null, 2)}
              </pre>
            </div>
          ),
          width: 600
        });
      } else {
        message.error(`工具执行失败: ${response.message}`);
      }
    } catch (error) {
      console.error('执行工具失败:', error);
      message.error('执行工具失败');
    }
  };

  // 表格列定义
  const columns = [
    {
      title: '服务器名称',
      dataIndex: 'display_name',
      key: 'display_name',
      render: (text, record) => (
        <div>
          <div style={{ fontWeight: 'bold' }}>{text}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>{record.name}</div>
        </div>
      )
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
      width: 200
    },
    {
      title: '协议类型',
      dataIndex: 'server_type',
      key: 'server_type',
      render: (type) => {
        const typeMap = {
          'stdio': { color: 'blue', text: 'STDIO' },
          'http': { color: 'green', text: 'HTTP' },
          'sse': { color: 'orange', text: 'SSE' },
          'websocket': { color: 'purple', text: 'WebSocket' }
        };
        const config = typeMap[type] || { color: 'default', text: type };
        return <Tag color={config.color}>{config.text}</Tag>;
      }
    },
    {
      title: '认证方式',
      dataIndex: 'auth_type',
      key: 'auth_type',
      render: (type) => {
        const typeMap = {
          none: { color: 'default', text: '无认证' },
          api_key: { color: 'blue', text: 'API Key' },
          oauth: { color: 'green', text: 'OAuth' },
          custom: { color: 'orange', text: '自定义' }
        };
        const config = typeMap[type] || { color: 'default', text: type };
        return <Tag color={config.color}>{config.text}</Tag>;
      }
    },
    {
      title: '状态',
      dataIndex: 'is_enabled',
      key: 'is_enabled',
      render: (enabled, record) => (
        <div>
          <Badge 
            status={enabled ? 'success' : 'default'} 
            text={enabled ? '启用' : '禁用'} 
          />
          {record.is_builtin && (
            <Tag color="gold" style={{ marginLeft: 8 }}>内置</Tag>
          )}
        </div>
      )
    },
    {
      title: '标签',
      dataIndex: 'tags',
      key: 'tags',
      render: (tags) => (
        <div>
          {tags?.slice(0, 2).map(tag => (
            <Tag key={tag} size="small">{tag}</Tag>
          ))}
          {tags?.length > 2 && (
            <Tag size="small">+{tags.length - 2}</Tag>
          )}
        </div>
      )
    },
    {
      title: '操作',
      key: 'actions',
      width: 200,
      render: (_, record) => (
        <Space>
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => showServerDetail(record)}
            />
          </Tooltip>
          
          <Tooltip title="编辑">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => {
                setEditingTool(record);
                form.setFieldsValue({
                  ...record,
                  server_config: record.server_config || {}
                });
                setModalVisible(true);
              }}
            />
          </Tooltip>
          
          <Tooltip title="测试连接">
            <Button
              type="text"
              icon={<BugOutlined />}
              onClick={() => {
                testForm.setFieldsValue({
                  server_url: record.server_url,
                  server_type: record.server_type,
                  auth_type: record.auth_type,
                  auth_config: JSON.stringify(record.auth_config, null, 2),
                  timeout: record.timeout || 30
                });
                setTestModalVisible(true);
              }}
            />
          </Tooltip>
          
          <Popconfirm
            title="确定要删除这个服务器吗？"
            onConfirm={() => handleDelete(record.id)}
          >
            <Tooltip title="删除">
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      )
    }
  ];

  return (
    <div className="ant-card ant-card-bordered css-dev-only-do-not-override-vrrzze">
      <div className="ant-card-head">
        <div className="ant-card-head-wrapper">
          <div className="ant-card-head-title">
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <Button
                icon={<ArrowLeftOutlined />}
                onClick={() => navigate('/project/ai/assistants')}
                style={{ marginRight: 16 }}
              >
                返回AI助手管理
              </Button>
              <Title level={2} style={{ margin: 0 }}>MCP服务器管理</Title>
            </div>
          </div>
        </div>
      </div>
      <div className="ant-card-body" style={{ padding: '24px' }}>
        <div style={{ marginBottom: '24px' }}>
          <Paragraph type="secondary">
            管理Model Context Protocol (MCP) 服务器，支持stdio、http、sse、websocket等协议类型。添加服务器后可选择其提供的工具为AI助手提供外部功能扩展。
          </Paragraph>
        </div>

        {/* 统计卡片 */}
        <Row gutter={16} style={{ marginBottom: '24px' }}>
          <Col span={6}>
            <Card>
              <Statistic
                title="总工具数"
                value={tools.length}
                prefix={<ApiOutlined />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="启用工具"
                value={tools.filter(t => t.is_enabled).length}
                prefix={<CheckCircleOutlined />}
                valueStyle={{ color: '#3f8600' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="内置工具"
                value={tools.filter(t => t.is_builtin).length}
                prefix={<CloudServerOutlined />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="今日调用"
                value={usageStats.today_calls || 0}
                prefix={<PlayCircleOutlined />}
                valueStyle={{ color: '#722ed1' }}
              />
            </Card>
          </Col>
        </Row>

        {/* 工具列表 */}
        <Card
          title="服务器列表"
          extra={
            <Space>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => {
                  setEditingTool(null);
                  form.resetFields();
                  setModalVisible(true);
                }}
              >
                添加服务器
              </Button>
              <Button
                icon={<BugOutlined />}
                onClick={() => {
                  testForm.resetFields();
                  setTestModalVisible(true);
                }}
              >
                测试连接
              </Button>
            </Space>
          }
        >
          <Table
            columns={columns}
            dataSource={tools}
            rowKey="id"
            loading={loading}
            pagination={{
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total) => `共 ${total} 个工具`
            }}
          />
        </Card>

        {/* 创建/编辑工具模态框 */}
        <Modal
          title={editingTool ? '编辑服务器' : '添加服务器'}
          open={modalVisible}
          onCancel={() => {
            setModalVisible(false);
            setEditingTool(null);
            form.resetFields();
          }}
          footer={null}
          width={800}
        >
          <Form
            form={form}
            layout="vertical"
            onFinish={handleSubmit}
          >
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="name"
                  label="服务器标识"
                  rules={[{ required: true, message: '请输入服务器标识' }]}
                >
                  <Input placeholder="如: web_search_server" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="display_name"
                  label="显示名称"
                  rules={[{ required: true, message: '请输入显示名称' }]}
                >
                  <Input placeholder="如: 网络搜索服务器" />
                </Form.Item>
              </Col>
            </Row>

            <Form.Item
              name="description"
              label="描述"
            >
              <TextArea rows={3} placeholder="服务器功能描述" />
            </Form.Item>

            <Row gutter={16}>
              <Col span={8}>
                <Form.Item
                  name="server_type"
                  label="协议类型"
                  rules={[{ required: true, message: '请选择协议类型' }]}
                >
                  <Select>
                    <Option value="stdio">STDIO</Option>
                    <Option value="http">HTTP</Option>
                    <Option value="sse">SSE</Option>
                    <Option value="websocket">WebSocket</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={16}>
                <Form.Item
                  name="server_url"
                  label="服务器地址"
                  rules={[{ required: true, message: '请输入服务器地址' }]}
                >
                  <Input placeholder="https://api.example.com 或 stdio://path/to/executable" />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="auth_type"
                  label="认证方式"
                  rules={[{ required: true, message: '请选择认证方式' }]}
                >
                  <Select>
                    <Option value="none">无认证</Option>
                    <Option value="api_key">API Key</Option>
                    <Option value="oauth">OAuth</Option>
                    <Option value="custom">自定义</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="timeout"
                  label="超时时间(秒)"
                  initialValue={30}
                >
                  <Input type="number" min={1} max={300} />
                </Form.Item>
              </Col>
            </Row>

            <Form.Item
              name="auth_config"
              label="认证配置"
            >
              <TextArea 
                rows={3} 
                placeholder='JSON格式，如: {"api_key": "your_key", "header": "Authorization"}'
              />
            </Form.Item>

            <Row gutter={16}>
              <Col span={8}>
                <Form.Item
                  name="retry_count"
                  label="重试次数"
                  initialValue={3}
                >
                  <Input type="number" min={0} max={10} />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="is_enabled"
                  label="启用状态"
                  valuePropName="checked"
                  initialValue={true}
                >
                  <Switch />
                </Form.Item>
              </Col>
            </Row>

            <Form.Item
              name="tags"
              label="标签"
            >
              <Select
                mode="tags"
                placeholder="添加标签"
                style={{ width: '100%' }}
              />
            </Form.Item>

            <Form.Item>
              <Space>
                <Button type="primary" htmlType="submit">
                  {editingTool ? '更新' : '创建'}
                </Button>
                <Button onClick={() => {
                  setModalVisible(false);
                  setEditingTool(null);
                  form.resetFields();
                }}>
                  取消
                </Button>
              </Space>
            </Form.Item>
          </Form>
        </Modal>

        {/* 测试连接模态框 */}
        <Modal
          title="测试服务器连接"
          open={testModalVisible}
          onCancel={() => {
            setTestModalVisible(false);
            setTestResult(null);
            testForm.resetFields();
          }}
          footer={null}
          width={600}
        >
          <Form
            form={testForm}
            layout="vertical"
            onFinish={handleTestConnection}
          >
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="server_type"
                  label="协议类型"
                  rules={[{ required: true, message: '请选择协议类型' }]}
                >
                  <Select>
                    <Option value="stdio">STDIO</Option>
                    <Option value="http">HTTP</Option>
                    <Option value="sse">SSE</Option>
                    <Option value="websocket">WebSocket</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="auth_type"
                  label="认证方式"
                  rules={[{ required: true, message: '请选择认证方式' }]}
                >
                  <Select>
                    <Option value="none">无认证</Option>
                    <Option value="api_key">API Key</Option>
                    <Option value="oauth">OAuth</Option>
                    <Option value="custom">自定义</Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>

            <Form.Item
              name="server_url"
              label="服务器地址"
              rules={[{ required: true, message: '请输入服务器地址' }]}
            >
              <Input placeholder="https://api.example.com" />
            </Form.Item>

            <Form.Item
              name="auth_config"
              label="认证配置"
            >
              <TextArea 
                rows={3} 
                placeholder='JSON格式，如: {"api_key": "your_key"}'
              />
            </Form.Item>

            <Form.Item
              name="timeout"
              label="超时时间(秒)"
              initialValue={30}
            >
              <Input type="number" min={1} max={300} />
            </Form.Item>

            <Form.Item>
              <Button type="primary" htmlType="submit" loading={testLoading}>
                测试连接
              </Button>
            </Form.Item>

            {testResult && (
              <Alert
                type={testResult.success ? 'success' : 'error'}
                message={testResult.success ? '连接成功' : '连接失败'}
                description={
                  <div>
                    <p>{testResult.message}</p>
                    {testResult.response_time && (
                      <p>响应时间: {testResult.response_time}ms</p>
                    )}
                    {testResult.available_tools && (
                      <div>
                        <p>可用工具:</p>
                        <ul>
                          {testResult.available_tools.map((tool, index) => (
                            <li key={index}>{tool.name || tool}</li>
                          ))}
                        </ul>
                      </div>
                    )}
                  </div>
                }
                style={{ marginTop: 16 }}
              />
            )}
          </Form>
        </Modal>

        {/* 服务器详情抽屉 */}
        <Drawer
          title="服务器详情"
          placement="right"
          onClose={() => setDetailDrawerVisible(false)}
          open={detailDrawerVisible}
          width={600}
        >
          {selectedServer && (
            <div>
              <Tabs defaultActiveKey="basic">
                <TabPane tab="基本信息" key="basic">
                  <div style={{ marginBottom: 16 }}>
                    <Text strong>服务器名称:</Text> {selectedServer.display_name}
                  </div>
                  <div style={{ marginBottom: 16 }}>
                    <Text strong>标识符:</Text> {selectedServer.name}
                  </div>
                  <div style={{ marginBottom: 16 }}>
                    <Text strong>描述:</Text> {selectedServer.description}
                  </div>
                  <div style={{ marginBottom: 16 }}>
                    <Text strong>协议类型:</Text> {selectedServer.server_type}
                  </div>
                  <div style={{ marginBottom: 16 }}>
                    <Text strong>服务器地址:</Text> {selectedServer.server_url}
                  </div>
                  <div style={{ marginBottom: 16 }}>
                    <Text strong>认证方式:</Text> {selectedServer.auth_type}
                  </div>
                  <div style={{ marginBottom: 16 }}>
                    <Text strong>连接状态:</Text> 
                    <Tag color={selectedServer.connection_status === 'connected' ? 'green' : 'red'} style={{ marginLeft: 8 }}>
                      {selectedServer.connection_status === 'connected' ? '已连接' : '未连接'}
                    </Tag>
                  </div>
                  <div style={{ marginBottom: 16 }}>
                    <Text strong>状态:</Text> 
                    <Badge 
                      status={selectedServer.is_enabled ? 'success' : 'default'} 
                      text={selectedServer.is_enabled ? '启用' : '禁用'} 
                      style={{ marginLeft: 8 }}
                    />
                  </div>
                  <div style={{ marginBottom: 16 }}>
                    <Text strong>标签:</Text>
                    <div style={{ marginTop: 4 }}>
                      {selectedServer.tags?.map(tag => (
                        <Tag key={tag}>{tag}</Tag>
                      ))}
                    </div>
                  </div>
                </TabPane>
                
                <TabPane tab="可用工具" key="tools">
                  <div>
                    <Alert
                      message="工具管理"
                      description="启用或禁用服务器提供的工具。启用的工具可以在AI助手中使用。"
                      type="info"
                      showIcon
                      style={{ marginBottom: 16 }}
                    />
                    
                    {availableTools.map((tool, index) => (
                      <Card key={index} size="small" style={{ marginBottom: 8 }}>
                        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                          <div style={{ flex: 1 }}>
                            <div style={{ fontWeight: 'bold' }}>{tool.name || `工具 ${index + 1}`}</div>
                            <div style={{ fontSize: '12px', color: '#666', marginTop: 4 }}>
                              {tool.description || '暂无描述'}
                            </div>
                            {tool.parameters && (
                              <div style={{ fontSize: '12px', color: '#999', marginTop: 4 }}>
                                参数: {Object.keys(tool.parameters).join(', ')}
                              </div>
                            )}
                          </div>
                          <div>
                            <Switch
                              size="small"
                              checked={selectedServer.enabled_tools?.includes(tool.name)}
                              onChange={(checked) => toggleTool(selectedServer.id, tool.name, checked)}
                            />
                          </div>
                        </div>
                      </Card>
                    ))}
                    
                    {availableTools.length === 0 && (
                      <div style={{ textAlign: 'center', padding: '20px', color: '#999' }}>
                        {selectedServer.connection_status === 'connected' ? 
                          '该服务器暂无可用工具' : 
                          '请先连接服务器以获取可用工具'
                        }
                      </div>
                    )}
                  </div>
                </TabPane>
                
                <TabPane tab="认证配置" key="auth">
                  <pre style={{ 
                    background: '#f5f5f5', 
                    padding: '16px', 
                    borderRadius: '4px',
                    fontSize: '12px'
                  }}>
                    {JSON.stringify(selectedServer.auth_config, null, 2)}
                  </pre>
                </TabPane>
              </Tabs>
            </div>
          )}
        </Drawer>
      </div>
    </div>
  );
};

export default MCPToolManagement; 