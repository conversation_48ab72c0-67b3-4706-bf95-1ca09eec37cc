#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
钉钉Stream模式WebSocket连接管理服务
支持与钉钉开放平台的Stream模式事件订阅
"""

import asyncio
import json
import logging
import time
import uuid
from typing import Dict, Any, Optional, Callable, List
from datetime import datetime, timedelta
import websockets
import httpx
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_

from ..models.models import DingTalkWebhook, DingTalkEventLog
from ..utils.dingtalk_api import DingTalkAPI
from services.redis_manager import RedisManager

logger = logging.getLogger(__name__)


class DingTalkStreamConnection:
    """
    钉钉Stream模式连接管理器
    
    负责建立和维护与钉钉开放平台的WebSocket连接
    """
    
    def __init__(self, webhook_config: DingTalkWebhook, dingtalk_api: DingTalkAPI, db: AsyncSession):
        self.webhook_config = webhook_config
        self.dingtalk_api = dingtalk_api
        self.db = db
        self.websocket = None
        self.is_connected = False
        self.reconnect_attempts = 0
        self.max_reconnect_attempts = 5
        self.reconnect_delay = 5  # 秒
        self.heartbeat_interval = 30  # 秒
        self.last_heartbeat = None
        self.event_handlers: Dict[str, Callable] = {}
        
        # Stream连接信息
        self.stream_endpoint = None
        self.ticket = None
        
    async def connect(self) -> bool:
        """
        建立Stream连接
        
        Returns:
            bool: 连接是否成功
        """
        try:
            # 1. 获取Stream连接信息
            connection_info = await self._get_stream_connection_info()
            if not connection_info:
                logger.error("获取Stream连接信息失败")
                return False
            
            self.stream_endpoint = connection_info['endpoint']
            self.ticket = connection_info['ticket']
            
            # 2. 建立WebSocket连接
            logger.info(f"正在连接到钉钉Stream服务: {self.stream_endpoint}")
            
            self.websocket = await websockets.connect(
                f"{self.stream_endpoint}?ticket={self.ticket}",
                ping_interval=self.heartbeat_interval,
                ping_timeout=10,
                close_timeout=10
            )
            
            self.is_connected = True
            self.reconnect_attempts = 0
            self.last_heartbeat = datetime.utcnow()
            
            # 3. 更新数据库状态
            await self._update_connection_status('connected')
            
            logger.info("钉钉Stream连接建立成功")
            return True
            
        except Exception as e:
            logger.error(f"建立Stream连接失败: {e}")
            await self._update_connection_status('error')
            return False
    
    async def disconnect(self):
        """断开Stream连接"""
        try:
            self.is_connected = False
            
            if self.websocket:
                await self.websocket.close()
                self.websocket = None
            
            await self._update_connection_status('disconnected')
            logger.info("钉钉Stream连接已断开")
            
        except Exception as e:
            logger.error(f"断开Stream连接失败: {e}")
    
    async def start_listening(self):
        """
        开始监听Stream事件
        
        这是主要的事件循环，负责接收和处理钉钉推送的事件
        """
        while self.is_connected:
            try:
                if not self.websocket:
                    logger.warning("WebSocket连接不存在，尝试重连")
                    if not await self._reconnect():
                        break
                    continue
                
                # 接收消息
                try:
                    message = await asyncio.wait_for(
                        self.websocket.recv(), 
                        timeout=self.heartbeat_interval + 10
                    )
                    
                    # 处理消息
                    await self._handle_message(message)
                    self.last_heartbeat = datetime.utcnow()
                    
                except asyncio.TimeoutError:
                    logger.warning("Stream连接超时，检查连接状态")
                    if not await self._check_connection_health():
                        await self._reconnect()
                
                except websockets.exceptions.ConnectionClosed:
                    logger.warning("Stream连接已关闭，尝试重连")
                    await self._reconnect()
                
            except Exception as e:
                logger.error(f"Stream监听过程中发生错误: {e}")
                await asyncio.sleep(1)
    
    async def _get_stream_connection_info(self) -> Optional[Dict[str, str]]:
        """
        获取Stream连接信息
        
        Returns:
            Optional[Dict[str, str]]: 包含endpoint和ticket的连接信息
        """
        try:
            # 获取访问令牌
            access_token = await self.dingtalk_api.get_access_token()
            if not access_token:
                logger.error("获取访问令牌失败")
                return None
            
            # 调用钉钉API获取Stream连接信息
            url = f"{self.dingtalk_api.base_url}/v1.0/gateway/connections/open"
            headers = {
                'x-acs-dingtalk-access-token': access_token,
                'Content-Type': 'application/json'
            }
            
            data = {
                'clientId': self.dingtalk_api.app_key,
                'clientSecret': self.dingtalk_api.app_secret,
                'ua': 'DingTalkStream/1.0.0',
                'subscriptions': self._get_subscription_list()
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.post(url, headers=headers, json=data)
                
                if response.status_code == 200:
                    result = response.json()
                    if result.get('success'):
                        return {
                            'endpoint': result['endpoint'],
                            'ticket': result['ticket']
                        }
                    else:
                        logger.error(f"获取Stream连接信息失败: {result}")
                else:
                    logger.error(f"Stream连接API调用失败: HTTP {response.status_code}")
            
            return None
            
        except Exception as e:
            logger.error(f"获取Stream连接信息异常: {e}")
            return None
    
    def _get_subscription_list(self) -> List[Dict[str, str]]:
        """
        获取订阅的事件类型列表
        
        Returns:
            List[Dict[str, str]]: 订阅配置列表
        """
        subscriptions = []
        
        if self.webhook_config.event_types:
            for event_type in self.webhook_config.event_types:
                subscriptions.append({
                    'type': 'EVENT',
                    'topic': event_type
                })
        
        # 默认订阅一些基础事件
        if not subscriptions:
            default_events = [
                'bpms_task_change',
                'bpms_instance_change', 
                'user_add_org',
                'user_modify_org',
                'user_leave_org'
            ]
            
            for event_type in default_events:
                subscriptions.append({
                    'type': 'EVENT',
                    'topic': event_type
                })
        
        return subscriptions
    
    async def _handle_message(self, message: str):
        """
        处理接收到的Stream消息
        
        Args:
            message: 接收到的消息内容
        """
        try:
            # 解析消息
            data = json.loads(message)
            
            # 记录接收到的消息
            logger.info(f"收到Stream消息: {data.get('type', 'unknown')}")
            
            message_type = data.get('type')
            
            if message_type == 'SYSTEM':
                # 系统消息处理
                await self._handle_system_message(data)
            elif message_type == 'EVENT':
                # 事件消息处理
                await self._handle_event_message(data)
            elif message_type == 'CALLBACK':
                # 回调消息处理
                await self._handle_callback_message(data)
            else:
                logger.warning(f"未知的消息类型: {message_type}")
            
        except json.JSONDecodeError as e:
            logger.error(f"解析Stream消息失败: {e}, 消息内容: {message}")
        except Exception as e:
            logger.error(f"处理Stream消息异常: {e}")
    
    async def _handle_system_message(self, data: Dict[str, Any]):
        """处理系统消息"""
        headers = data.get('headers', {})
        topic = headers.get('topic')
        
        if topic == 'ping':
            # 响应ping消息
            pong_message = {
                'code': 200,
                'headers': {
                    'contentType': 'application/json'
                },
                'message': 'OK',
                'data': {}
            }
            
            if self.websocket:
                await self.websocket.send(json.dumps(pong_message))
                logger.debug("已响应ping消息")
    
    async def _handle_event_message(self, data: Dict[str, Any]):
        """处理事件消息"""
        try:
            headers = data.get('headers', {})
            event_type = headers.get('topic')
            event_data = data.get('data', {})
            
            # 记录事件日志
            event_log = DingTalkEventLog(
                event_type=event_type or 'unknown',
                event_data=event_data,
                signature='',
                timestamp=str(int(time.time())),
                status="pending",
                webhook_id=self.webhook_config.id
            )
            
            self.db.add(event_log)
            await self.db.commit()
            
            # 调用事件处理器
            if event_type in self.event_handlers:
                try:
                    await self.event_handlers[event_type](event_data)
                    event_log.status = "processed"
                    event_log.process_result = {'success': True}
                except Exception as e:
                    logger.error(f"事件处理器执行失败: {e}")
                    event_log.status = "failed"
                    event_log.process_result = {'success': False, 'error': str(e)}
                
                await self.db.commit()
            
            # 通过现有WebSocket系统推送钉钉事件
            await self._publish_to_websocket(event_type, event_data)
            
            # 发送ACK响应
            ack_message = {
                'code': 200,
                'headers': {
                    'contentType': 'application/json'
                },
                'message': 'OK',
                'data': {}
            }
            
            if self.websocket:
                await self.websocket.send(json.dumps(ack_message))
            
            logger.info(f"已处理事件: {event_type}")
            
        except Exception as e:
            logger.error(f"处理事件消息异常: {e}")
    
    async def _handle_callback_message(self, data: Dict[str, Any]):
        """处理回调消息"""
        # 回调消息通常用于API调用的响应
        logger.info(f"收到回调消息: {data}")
    
    async def _check_connection_health(self) -> bool:
        """
        检查连接健康状态
        
        Returns:
            bool: 连接是否健康
        """
        if not self.websocket:
            return False
        
        try:
            # 发送ping检查连接
            await self.websocket.ping()
            return True
        except Exception:
            return False
    
    async def _reconnect(self) -> bool:
        """
        重新连接
        
        Returns:
            bool: 重连是否成功
        """
        if self.reconnect_attempts >= self.max_reconnect_attempts:
            logger.error("达到最大重连次数，停止重连")
            await self._update_connection_status('error')
            return False
        
        self.reconnect_attempts += 1
        logger.info(f"尝试重连 ({self.reconnect_attempts}/{self.max_reconnect_attempts})")
        
        # 先断开现有连接
        if self.websocket:
            try:
                await self.websocket.close()
            except:
                pass
            self.websocket = None
        
        # 等待一段时间后重连
        await asyncio.sleep(self.reconnect_delay)
        
        # 尝试重新连接
        return await self.connect()
    
    async def _update_connection_status(self, status: str):
        """
        更新连接状态到数据库
        
        Args:
            status: 连接状态 (connected, disconnected, error)
        """
        try:
            # 更新webhook配置的stream状态
            webhook = await self.db.scalar(
                select(DingTalkWebhook).where(DingTalkWebhook.id == self.webhook_config.id)
            )
            
            if webhook:
                webhook.stream_status = status
                webhook.updated_at = datetime.utcnow()
                await self.db.commit()
                
        except Exception as e:
            logger.error(f"更新连接状态失败: {e}")
    
    def register_event_handler(self, event_type: str, handler: Callable):
        """
        注册事件处理器
        
        Args:
            event_type: 事件类型
            handler: 处理函数
        """
        self.event_handlers[event_type] = handler
        logger.info(f"已注册事件处理器: {event_type}")
    
    async def _publish_to_websocket(self, event_type: str, event_data: Dict[str, Any]):
        """
        通过现有WebSocket系统推送钉钉事件
        
        Args:
            event_type: 事件类型
            event_data: 事件数据
        """
        try:
            # 构建WebSocket消息
            message = {
                "type": "dingtalk_event",
                "event_type": event_type,
                "event_data": event_data,
                "source": "dingtalk_stream",
                "webhook_id": str(self.webhook_config.id),
                "webhook_name": self.webhook_config.name,
                "timestamp": datetime.utcnow().isoformat()
            }
            
            # 获取Redis管理器
            redis_manager = await RedisManager.get_instance()
            
            # 发布到项目通道（所有项目成员都能收到钉钉事件）
            await redis_manager.publish_notification(
                f"project:{self.webhook_config.project_id}:notifications",
                message
            )
            
            # 如果事件包含特定用户信息，也发送到用户通道
            user_id = event_data.get('UserId') or event_data.get('StaffId')
            if user_id:
                # 这里需要将钉钉的userid转换为系统的user_id
                # 可以通过UnionIdService来实现
                try:
                    from ..services.union_id_service import UnionIdService
                    union_id_service = UnionIdService(
                        self.db, 
                        self.dingtalk_api, 
                        str(self.webhook_config.tenant_id), 
                        str(self.webhook_config.project_id)
                    )
                    
                    # 通过钉钉userid获取系统user_id（这里需要反向查询）
                    # 暂时跳过用户特定通知，只发送到项目通道
                    pass
                    
                except Exception as e:
                    logger.warning(f"获取用户ID失败: {e}")
            
            logger.info(f"钉钉事件已推送到WebSocket: {event_type}")
            
        except Exception as e:
            logger.error(f"推送钉钉事件到WebSocket失败: {e}")


class DingTalkStreamManager:
    """
    钉钉Stream连接管理器
    
    负责管理多个Stream连接
    """
    
    def __init__(self, db: AsyncSession):
        self.db = db
        self.connections: Dict[str, DingTalkStreamConnection] = {}
        self.is_running = False
    
    async def start(self):
        """启动Stream管理器"""
        self.is_running = True
        logger.info("钉钉Stream管理器已启动")
        
        # 加载并启动所有Stream模式的Webhook
        await self._load_stream_webhooks()
        
        # 启动监控任务
        asyncio.create_task(self._monitor_connections())
    
    async def stop(self):
        """停止Stream管理器"""
        self.is_running = False
        
        # 断开所有连接
        for connection in self.connections.values():
            await connection.disconnect()
        
        self.connections.clear()
        logger.info("钉钉Stream管理器已停止")
    
    async def _load_stream_webhooks(self):
        """加载Stream模式的Webhook配置"""
        try:
            # 查询所有启用的Stream模式Webhook
            result = await self.db.execute(
                select(DingTalkWebhook).where(
                    and_(
                        DingTalkWebhook.enabled == True,
                        DingTalkWebhook.subscription_mode == 'stream'
                    )
                )
            )
            
            webhooks = result.scalars().all()
            
            for webhook in webhooks:
                await self._create_connection(webhook)
                
        except Exception as e:
            logger.error(f"加载Stream Webhook配置失败: {e}")
    
    async def _create_connection(self, webhook: DingTalkWebhook):
        """
        为Webhook创建Stream连接
        
        Args:
            webhook: Webhook配置
        """
        try:
            # 创建DingTalkAPI实例
            dingtalk_api = DingTalkAPI(
                app_key=webhook.secret,  # 这里需要从配置中获取真正的app_key
                app_secret=webhook.secret,  # 这里需要从配置中获取真正的app_secret
                base_url="https://oapi.dingtalk.com"
            )
            
            # 创建连接
            connection = DingTalkStreamConnection(webhook, dingtalk_api, self.db)
            
            # 注册默认事件处理器
            self._register_default_handlers(connection)
            
            # 建立连接
            if await connection.connect():
                self.connections[str(webhook.id)] = connection
                
                # 启动监听任务
                asyncio.create_task(connection.start_listening())
                
                logger.info(f"Stream连接已创建: {webhook.name}")
            else:
                logger.error(f"Stream连接创建失败: {webhook.name}")
                
        except Exception as e:
            logger.error(f"创建Stream连接异常: {e}")
    
    def _register_default_handlers(self, connection: DingTalkStreamConnection):
        """注册默认的事件处理器"""
        
        async def handle_user_event(event_data):
            """处理用户相关事件"""
            logger.info(f"处理用户事件: {event_data}")
            # 这里可以添加具体的用户事件处理逻辑
        
        async def handle_approval_event(event_data):
            """处理审批相关事件"""
            logger.info(f"处理审批事件: {event_data}")
            # 这里可以添加具体的审批事件处理逻辑
        
        # 注册处理器
        connection.register_event_handler('user_add_org', handle_user_event)
        connection.register_event_handler('user_modify_org', handle_user_event)
        connection.register_event_handler('user_leave_org', handle_user_event)
        connection.register_event_handler('bpms_task_change', handle_approval_event)
        connection.register_event_handler('bpms_instance_change', handle_approval_event)
    
    async def _monitor_connections(self):
        """监控连接状态"""
        while self.is_running:
            try:
                # 检查所有连接的健康状态
                for webhook_id, connection in list(self.connections.items()):
                    if not connection.is_connected:
                        logger.warning(f"检测到连接断开: {webhook_id}")
                        # 可以在这里添加重连逻辑或通知
                
                # 每30秒检查一次
                await asyncio.sleep(30)
                
            except Exception as e:
                logger.error(f"连接监控异常: {e}")
                await asyncio.sleep(5)


# 全局Stream管理器实例
stream_manager: Optional[DingTalkStreamManager] = None


async def get_stream_manager(db: AsyncSession) -> DingTalkStreamManager:
    """
    获取Stream管理器实例
    
    Args:
        db: 数据库会话
        
    Returns:
        DingTalkStreamManager: Stream管理器实例
    """
    global stream_manager
    
    if stream_manager is None:
        stream_manager = DingTalkStreamManager(db)
        await stream_manager.start()
    
    return stream_manager


async def start_stream_service(db: AsyncSession):
    """
    启动Stream服务
    
    Args:
        db: 数据库会话
    """
    manager = await get_stream_manager(db)
    logger.info("钉钉Stream服务已启动")


async def stop_stream_service():
    """停止Stream服务"""
    global stream_manager
    
    if stream_manager:
        await stream_manager.stop()
        stream_manager = None
    
    logger.info("钉钉Stream服务已停止") 