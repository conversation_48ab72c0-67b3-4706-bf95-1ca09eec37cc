FROM node:18-alpine as build

WORKDIR /app

# 设置环境变量
ENV NODE_ENV=production \
    PATH=/app/node_modules/.bin:$PATH \
    TZ=Asia/Shanghai

# 使用国内npm镜像源
RUN npm config set registry https://registry.npmmirror.com

# 安装依赖
COPY package.json package-lock.json* ./
RUN npm ci --legacy-peer-deps

# 复制应用代码
COPY . .

# 构建应用
RUN npm run build

# 第二阶段：使用nginx提供静态文件
FROM nginx:stable-alpine

# 复制构建产物
COPY --from=build /app/build /usr/share/nginx/html

# 复制nginx配置
COPY nginx.conf /etc/nginx/conf.d/default.conf

# 暴露端口
EXPOSE 80

# 启动nginx
CMD ["nginx", "-g", "daemon off;"]
