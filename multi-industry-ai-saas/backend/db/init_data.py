#!/usr/bin/env python
# -*- coding: utf-8 -*-

# 兼容Python 3.10以下的anext
try:
    anext
except NameError:
    async def anext(ait):
        return await ait.__anext__()

import logging
import asyncio
from sqlalchemy.ext.asyncio import AsyncSession
from uuid import UUID
from sqlalchemy import select

from db.database import AsyncSessionLocal
from db.migrations import create_tables, initialize_system_data
from db.extensions import init_extensions
from db.init_workflows import init_default_workflows
from db.init_plugins import initialize_plugins
from models.ai.provider import AIProvider
from models.ai.model import AIModel

# 配置日志
logger = logging.getLogger(__name__)


async def init_database():
    """
    初始化数据库
    """
    try:
        # 获取数据库会话
        async with AsyncSessionLocal() as db:
            # 初始化数据库扩展
            await init_extensions(db)

        # 创建表
        await create_tables()

        # 获取数据库会话
        async with AsyncSessionLocal() as db:
            # 初始化系统数据
            await initialize_system_data(db)
            
            # 初始化 OpenRouter 模型
            await initialize_openrouter_models(db)

        # 初始化默认工作流
        await init_default_workflows()

        # 初始化插件
        try:
            from db.init_plugins import initialize_plugins
            await initialize_plugins()
            logger.info("插件初始化成功")
        except Exception as e:
            logger.error(f"插件初始化失败: {str(e)}")
            import traceback
            traceback.print_exc()

        # 初始化 SAPI 插件
        try:
            from plugins import initialize_plugins as init_sapi_plugins
            await init_sapi_plugins()
            logger.info("SAPI 插件初始化成功")
        except Exception as e:
            logger.error(f"SAPI 插件初始化失败: {str(e)}")
            import traceback
            traceback.print_exc()

        logger.info("数据库初始化成功")
        return True
    except Exception as e:
        logger.error(f"数据库初始化失败: {e}")
        raise


def init_project(project_id: UUID):
    """
    初始化项目数据

    Args:
        project_id: 项目ID

    Returns:
        初始化的数据
    """
    logger.info(f"开始初始化项目数据，项目ID: {project_id}")

    db = AsyncSessionLocal()
    try:
        # 直接调用本地函数init_project_data
        data = init_project_data(db, project_id)
        logger.info(f"项目数据初始化完成，项目ID: {project_id}")
        return data
    except Exception as e:
        logger.error(f"项目数据初始化失败: {e}", exc_info=True)
        db.rollback()
        raise
    finally:
        db.close()


async def initialize_openrouter_models(db: AsyncSession):
    """初始化 OpenRouter 提供商的模型"""
    
    # 获取 OpenRouter 提供商
    provider_query = select(AIProvider).where(AIProvider.name == "openrouter")
    result = await db.execute(provider_query)
    openrouter_provider = result.scalar_one_or_none()
    
    if not openrouter_provider:
        print("OpenRouter 提供商不存在，跳过模型初始化")
        return
    
    # 新增的模型定义
    new_models = [
        {
            "name": "deepseek/deepseek-r1-0528-qwen3-8b:free",
            "display_name": "DeepSeek R1 Qwen3 8B Free",
            "description": "DeepSeek R1 based on Qwen3 8B architecture, free version with advanced reasoning capabilities",
            "model_type": "chat",
            "capabilities": {
                "supports_function_calling": True,
                "supports_vision": False,
                "supports_audio_input": False,
                "supports_audio_output": False,
                "supports_speech_to_text": False,
                "supports_text_to_speech": False,
                "supports_system_message": True,
                "supports_streaming": True,
                "supports_reasoning": True,
                "supports_code_generation": True,
                "supports_multilingual": True
            },
            "context_window": 32768,
            "token_limit": 4096,
            "input_price_per_1k_tokens": 0.0,
            "output_price_per_1k_tokens": 0.0,
            "average_response_time": 2.5,
            "rate_limit_per_minute": 100
        },
        {
            "name": "deepseek/deepseek-r1-0528:free",
            "display_name": "DeepSeek R1 Free",
            "description": "DeepSeek R1 large model with enhanced reasoning capabilities, free version",
            "model_type": "chat",
            "capabilities": {
                "supports_function_calling": True,
                "supports_vision": False,
                "supports_audio_input": False,
                "supports_audio_output": False,
                "supports_speech_to_text": False,
                "supports_text_to_speech": False,
                "supports_system_message": True,
                "supports_streaming": True,
                "supports_reasoning": True,
                "supports_code_generation": True,
                "supports_multilingual": True,
                "supports_math": True
            },
            "context_window": 65536,
            "token_limit": 8192,
            "input_price_per_1k_tokens": 0.0,
            "output_price_per_1k_tokens": 0.0,
            "average_response_time": 3.0,
            "rate_limit_per_minute": 60
        },
        {
            "name": "google/gemma-3n-e4b-it:free",
            "display_name": "Gemma 3n E4B IT Free",
            "description": "Google Gemma 3n Edge 4B model optimized for edge deployment and mobile devices, instruction-tuned",
            "model_type": "chat",
            "capabilities": {
                "supports_function_calling": True,
                "supports_vision": False,
                "supports_audio_input": False,
                "supports_audio_output": False,
                "supports_speech_to_text": False,
                "supports_text_to_speech": False,
                "supports_system_message": True,
                "supports_streaming": True,
                "supports_multilingual": True,
                "supports_code_generation": True,
                "supports_edge_deployment": True
            },
            "context_window": 32768,
            "token_limit": 4096,
            "input_price_per_1k_tokens": 0.0,
            "output_price_per_1k_tokens": 0.0,
            "average_response_time": 1.5,
            "rate_limit_per_minute": 120
        },
        {
            "name": "qwen/qwen2.5-vl-32b-instruct:free",
            "display_name": "Qwen 2.5 VL 32B Instruct Free",
            "description": "Qwen 2.5 Vision-Language 32B model with multimodal capabilities, instruction-tuned version",
            "model_type": "multimodal",
            "capabilities": {
                "supports_function_calling": True,
                "supports_vision": True,
                "supports_audio_input": False,
                "supports_audio_output": False,
                "supports_speech_to_text": False,
                "supports_text_to_speech": False,
                "supports_system_message": True,
                "supports_streaming": True,
                "supports_multilingual": True,
                "supports_code_generation": True,
                "supports_document_analysis": True,
                "supports_chart_analysis": True,
                "supports_ocr": True,
                "supports_image_understanding": True,
                "supports_video_understanding": True,
                "supports_grounding": True
            },
            "context_window": 131072,
            "token_limit": 8192,
            "input_price_per_1k_tokens": 0.0,
            "output_price_per_1k_tokens": 0.0,
            "image_price_per_1k_tokens": 0.0,
            "average_response_time": 4.0,
            "rate_limit_per_minute": 30
        }
    ]

    # 检查现有模型并创建新模型
    for model_data in new_models:
        model_query = select(AIModel).where(
            (AIModel.provider_id == openrouter_provider.id) &
            (AIModel.name == model_data["name"])
        )
        result = await db.execute(model_query)
        existing_model = result.scalar_one_or_none()
        
        if not existing_model:
            model = AIModel(
                provider_id=openrouter_provider.id,
                **model_data,
                status="active",
                is_builtin=True
            )
            db.add(model)
            print(f"已添加新模型: {model_data['display_name']}")
        else:
            print(f"模型已存在: {model_data['display_name']}")
    
    await db.commit()
    print("OpenRouter 新模型初始化完成")


if __name__ == "__main__":
    """
    直接运行此脚本初始化数据库
    """
    asyncio.run(init_database())
