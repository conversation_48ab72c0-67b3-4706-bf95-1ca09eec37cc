#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
增强版AI客服对话 API
整合意图识别、学习优化、知识库RAG等完整功能
"""

import logging
import uuid
import json
from typing import Dict, Any, Optional, List
from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel

from api.deps import get_db, get_current_project
from core.auth import get_current_user
from models.user import User
from models.project import Project
from ..services.customer_service import get_customer_service_manager
from ..core.ai_engine import AICustomerServiceEngine
from ..core.platform_adapter import PlatformMessage, PlatformResponse

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter()

# 请求模型
class EnhancedChatRequest(BaseModel):
    message: str
    session_id: Optional[str] = None
    platform_type: str = "test"
    platform_user_id: str = "test_user"
    image_urls: Optional[List[str]] = None
    context: Optional[Dict[str, Any]] = None
    enable_intent_analysis: bool = True
    enable_knowledge_search: bool = True
    enable_learning: bool = True

class EnhancedChatResponse(BaseModel):
    # 基础响应
    response: str
    session_id: str
    
    # AI处理信息
    model_used: str
    confidence: float
    response_time: int
    
    # 意图分析结果
    intent_analysis: Dict[str, Any]
    
    # 知识库查询结果
    knowledge_sources: List[Dict[str, Any]]
    
    # 业务处理结果
    business_actions: List[Dict[str, Any]]
    
    # 回复建议
    reply_suggestions: List[str]
    
    # 质量评估
    quality_assessment: Dict[str, Any]
    
    # 学习标记
    learning_feedback: Dict[str, Any]

@router.post(
    "/enhanced-chat",
    response_model=EnhancedChatResponse,
    summary="增强版AI客服对话",
    description="支持意图识别、知识库RAG、业务流转、学习优化的完整AI客服对话"
)
async def enhanced_chat(
    request: EnhancedChatRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """增强版AI客服对话"""
    try:
        start_time = datetime.utcnow()
        
        # 获取客服管理器
        cs_manager = get_customer_service_manager()
        
        # 确保客服管理器已初始化
        await cs_manager.initialize()
        
        # 获取AI引擎并初始化项目配置
        ai_engine = AICustomerServiceEngine()
        await ai_engine.initialize(project_id=project.id, db=db)
        
        # 构建平台消息
        platform_message = PlatformMessage(
            platform_type=request.platform_type,
            platform_user_id=request.platform_user_id,
            user_name=request.platform_user_id,
            session_id=request.session_id or str(uuid.uuid4()),
            message_type="text",
            content=request.message,
            attachments=[{"url": url} for url in (request.image_urls or [])],
            timestamp=datetime.utcnow().timestamp(),
            meta_data=request.context or {}
        )
        
        # 1. 意图分析
        intent_analysis = {}
        if request.enable_intent_analysis:
            intent_analysis = await ai_engine.analyze_message_intent(
                message=request.message,
                context={
                    "session_id": platform_message.session_id,
                    "platform_type": request.platform_type,
                    "project_id": str(project.id)
                }
            )
            logger.info(f"意图分析结果: {intent_analysis}")
        
        # 2. 知识库查询
        knowledge_sources = []
        if request.enable_knowledge_search:
            try:
                from services.ai.knowledge_service import AIKnowledgeService
                
                # 查询知识库
                knowledge_results = await AIKnowledgeService.search_knowledge(
                    db=db,
                    query=request.message,
                    project_id=project.id,
                    user_id=current_user.id,
                    top_k=5
                )
                
                knowledge_sources = [
                    {
                        "id": result.get("chunk_id"),
                        "title": result.get("document_title", ""),
                        "content": result.get("content", "")[:200] + "..." if len(result.get("content", "")) > 200 else result.get("content", ""),
                        "score": result.get("similarity", 0.0),
                        "source": result.get("knowledge_base_name", "knowledge_base")
                    }
                    for result in knowledge_results
                ]
                
                logger.info(f"知识库查询找到 {len(knowledge_sources)} 个相关结果")
                
            except Exception as e:
                logger.warning(f"知识库查询失败: {e}")
                knowledge_sources = []
        
        # 3. AI对话处理（使用插件独立配置的AI模型）
        ai_response, ai_metadata = await ai_engine.process_message(
            db=db,
            message=platform_message,
            session_id=platform_message.session_id,
            project_id=project.id,
            user_id=current_user.id,
            assistant_id=None,  # 不使用助手ID，直接使用插件配置的模型
            use_knowledge_base=request.enable_knowledge_search,
            knowledge_base_ids=[]
        )
        
        # 4. 业务流转处理
        business_actions = []
        try:
            from ..core.business_handler import BusinessHandler
            
            business_handler = BusinessHandler()
            business_result = await business_handler.handle_customer_inquiry(
                db=db,
                session_id=platform_message.session_id,
                message=request.message,
                intent=intent_analysis.get("primary_intent", "inquiry"),
                project_id=project.id,
                user_id=current_user.id,
                platform_user_id=request.platform_user_id,
                platform_type=request.platform_type
            )
            
            business_actions = business_result.get("actions", [])
            logger.info(f"业务流转处理完成，生成 {len(business_actions)} 个动作")
            
        except Exception as e:
            logger.warning(f"业务流转处理失败: {e}")
            business_actions = []
        
        # 5. 生成回复建议
        reply_suggestions = []
        try:
            reply_suggestions = await ai_engine.generate_smart_reply_suggestions(
                message=request.message,
                intent=intent_analysis.get("primary_intent", "inquiry"),
                context={
                    "knowledge_sources": knowledge_sources,
                    "business_actions": business_actions
                }
            )
        except Exception as e:
            logger.warning(f"回复建议生成失败: {e}")
            reply_suggestions = []
        
        # 6. 质量评估
        quality_assessment = {}
        try:
            quality_assessment = await ai_engine.evaluate_response_quality(
                user_message=request.message,
                ai_response=ai_response.content,
                context={
                    "intent_analysis": intent_analysis,
                    "knowledge_used": len(knowledge_sources) > 0
                }
            )
        except Exception as e:
            logger.warning(f"质量评估失败: {e}")
            quality_assessment = {"quality_score": 0.8}
        
        # 7. 学习反馈收集
        learning_feedback = {}
        if request.enable_learning:
            learning_feedback = {
                "conversation_id": platform_message.session_id,
                "user_message": request.message,
                "ai_response": ai_response.content,
                "intent_detected": intent_analysis.get("primary_intent"),
                "confidence": intent_analysis.get("confidence", 0.0),
                "knowledge_used": len(knowledge_sources) > 0,
                "business_action_triggered": len(business_actions) > 0,
                "quality_score": quality_assessment.get("quality_score", 0.8),
                "timestamp": datetime.utcnow().isoformat(),
                "needs_review": quality_assessment.get("quality_score", 0.8) < 0.7
            }
            
            # 添加学习数据到后台任务
            background_tasks.add_task(
                save_learning_data,
                project.id,
                learning_feedback
            )
        
        # 计算响应时间
        end_time = datetime.utcnow()
        response_time = int((end_time - start_time).total_seconds() * 1000)
        
        # 构建增强响应
        return EnhancedChatResponse(
            response=ai_response.content,
            session_id=platform_message.session_id,
            model_used=ai_metadata.get("model_used", "AI助手"),
            confidence=ai_metadata.get("confidence_score", 0.8),
            response_time=response_time,
            intent_analysis=intent_analysis,
            knowledge_sources=knowledge_sources,
            business_actions=business_actions,
            reply_suggestions=reply_suggestions,
            quality_assessment=quality_assessment,
            learning_feedback=learning_feedback
        )
        
    except Exception as e:
        logger.error(f"增强版AI对话失败: {e}")
        
        # 返回降级响应
        return EnhancedChatResponse(
            response="抱歉，系统暂时繁忙，请稍后再试。",
            session_id=request.session_id or str(uuid.uuid4()),
            model_used="fallback",
            confidence=0.0,
            response_time=0,
            intent_analysis={"primary_intent": "error", "confidence": 0.0},
            knowledge_sources=[],
            business_actions=[],
            reply_suggestions=[],
            quality_assessment={"quality_score": 0.0},
            learning_feedback={"error": str(e)}
        )

@router.post(
    "/feedback",
    summary="学习反馈",
    description="提供对AI回复的反馈，用于学习优化"
)
async def provide_feedback(
    session_id: str,
    message_id: str,
    feedback_type: str,  # positive, negative, neutral
    feedback_details: Optional[Dict[str, Any]] = None,
    background_tasks: BackgroundTasks = BackgroundTasks(),
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """提供学习反馈"""
    try:
        feedback_data = {
            "session_id": session_id,
            "message_id": message_id,
            "feedback_type": feedback_type,
            "feedback_details": feedback_details or {},
            "user_id": str(current_user.id),
            "project_id": str(project.id),
            "timestamp": datetime.utcnow().isoformat()
        }
        
        # 添加反馈处理到后台任务
        background_tasks.add_task(
            process_learning_feedback,
            project.id,
            feedback_data
        )
        
        return {
            "success": True,
            "message": "反馈已收到，将用于改进AI服务质量"
        }
        
    except Exception as e:
        logger.error(f"处理学习反馈失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"处理学习反馈失败: {str(e)}"
        )

async def save_learning_data(project_id: uuid.UUID, learning_data: Dict[str, Any]):
    """保存学习数据（后台任务）"""
    try:
        # 这里应该保存到数据库或学习数据存储
        logger.info(f"保存学习数据: 项目 {project_id}, 数据: {learning_data['conversation_id']}")
        
        # TODO: 实现实际的数据保存逻辑
        # 例如：保存到专门的学习数据表
        
    except Exception as e:
        logger.error(f"保存学习数据失败: {e}")

async def process_learning_feedback(project_id: uuid.UUID, feedback_data: Dict[str, Any]):
    """处理学习反馈（后台任务）"""
    try:
        # 这里应该处理用户反馈，用于模型优化
        logger.info(f"处理学习反馈: 项目 {project_id}, 反馈类型: {feedback_data['feedback_type']}")
        
        # TODO: 实现实际的反馈处理逻辑
        # 例如：更新模型参数、调整意图识别阈值等
        
    except Exception as e:
        logger.error(f"处理学习反馈失败: {e}") 