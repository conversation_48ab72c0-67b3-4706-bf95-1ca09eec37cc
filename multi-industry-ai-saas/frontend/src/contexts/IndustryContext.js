import React, { createContext, useContext, useState, useEffect, lazy } from 'react';
import { message } from 'antd';

// 创建行业上下文
const IndustryContext = createContext();

/**
 * 行业上下文提供者
 * 管理行业特定的配置、组件和服务
 */
export const IndustryProvider = ({ children }) => {
  // 状态
  const [industryType, setIndustryType] = useState('retail');
  const [industryComponents, setIndustryComponents] = useState({});
  const [loading, setLoading] = useState(false);

  // 初始化时加载组件
  useEffect(() => {
    loadIndustryComponents();
  }, []);

  // 加载行业特定组件
  const loadIndustryComponents = () => {
    try {
      setLoading(true);

      // 预加载所有行业组件
      const components = {
        retail: {
          Dashboard: lazy(() => import('../pages/retail/Dashboard')),
          Products: lazy(() => import('../pages/retail/Products')),
          Inventory: lazy(() => import('../pages/retail/Inventory')),
        },
        restaurant: {
          Dashboard: lazy(() => import('../pages/restaurant/Dashboard')),
          Menu: lazy(() => import('../pages/restaurant/Menu')),
          Orders: lazy(() => import('../pages/restaurant/Orders')),
        },
        hotel: {
          Dashboard: lazy(() => import('../pages/hotel/Dashboard')),
          Rooms: lazy(() => import('../pages/hotel/Rooms')),
          Bookings: lazy(() => import('../pages/hotel/Bookings')),
        }
      };

      setIndustryComponents(components);
    } catch (error) {
      console.error('加载行业组件失败:', error);
      message.error('加载行业组件失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取行业特定组件
  const getIndustryComponent = (componentName, type = 'retail') => {
    if (!industryComponents[type]) {
      return null;
    }
    return industryComponents[type][componentName] || null;
  };

  // 获取行业名称
  const getIndustryName = (type = 'retail') => {
    const industryNames = {
      'retail': '零售业',
      'restaurant': '餐饮业',
      'hotel': '酒店业',
      'beauty': '美容美发业',
      'education': '教育培训业',
      'healthcare': '医疗健康业',
    };

    return industryNames[type] || '零售业';
  };

  // 上下文值
  const contextValue = {
    industryType,
    loading,
    getIndustryComponent,
    getIndustryName,
  };

  return (
    <IndustryContext.Provider value={contextValue}>
      {children}
    </IndustryContext.Provider>
  );
};

// 自定义Hook，用于在组件中使用行业上下文
export const useIndustry = () => {
  const context = useContext(IndustryContext);
  if (!context) {
    throw new Error('useIndustry必须在IndustryProvider内部使用');
  }
  return context;
};

export default IndustryContext;
