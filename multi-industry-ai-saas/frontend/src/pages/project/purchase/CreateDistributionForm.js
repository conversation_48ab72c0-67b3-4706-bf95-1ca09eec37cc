import React, { useState, useEffect } from 'react';
import {
  Form,
  Input,
  Button,
  Select,
  Table,
  InputNumber,
  Space,
  Row,
  Col,
  Card,
  message,
  Divider,
  Typography
} from 'antd';
import { PlusOutlined, DeleteOutlined } from '@ant-design/icons';

const { Option } = Select;
const { Text } = Typography;

const CreateDistributionForm = ({ 
  purchaseOrder, 
  stores = [], 
  warehouses = [], 
  onSubmit, 
  onCancel, 
  loading = false 
}) => {
  const [form] = Form.useForm();
  const [distributionItems, setDistributionItems] = useState([]);

  // 初始化分拨商品
  useEffect(() => {
    if (purchaseOrder && purchaseOrder.items) {
      const items = purchaseOrder.items.map(item => ({
        id: item.id,
        product_name: item.product_name,
        product_code: item.product_code,
        product_unit: item.product_unit,
        product_specification: item.product_specification,
        available_quantity: item.quantity - (item.distributed_quantity || 0),
        unit_price: item.unit_price,
        selected_quantity: 0,
        target_type: 'store',
        target_id: null
      }));
      setDistributionItems(items.filter(item => item.available_quantity > 0));
    }
  }, [purchaseOrder]);

  // 更新分拨商品
  const updateDistributionItem = (index, field, value) => {
    const updatedItems = [...distributionItems];
    updatedItems[index] = {
      ...updatedItems[index],
      [field]: value
    };
    setDistributionItems(updatedItems);
  };

  // 处理表单提交
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      
      // 验证分拨商品
      const validItems = distributionItems.filter(item => 
        item.selected_quantity > 0 && item.target_id
      );

      if (validItems.length === 0) {
        message.error('请至少选择一个商品进行分拨');
        return;
      }

      // 验证分拨数量
      const invalidItems = validItems.filter(item => 
        item.selected_quantity > item.available_quantity
      );

      if (invalidItems.length > 0) {
        message.error('分拨数量不能超过可分拨数量');
        return;
      }

      const distributionData = {
        ...values,
        items: validItems.map(item => ({
          purchase_order_item_id: item.id,
          product_name: item.product_name,
          product_code: item.product_code,
          product_unit: item.product_unit,
          product_specification: item.product_specification,
          quantity: item.selected_quantity,
          unit_price: item.unit_price,
          target_type: item.target_type,
          target_id: item.target_id
        }))
      };

      await onSubmit(distributionData);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  const columns = [
    {
      title: '商品信息',
      key: 'product_info',
      render: (_, record) => (
        <div>
          <div><strong>{record.product_name}</strong></div>
          {record.product_code && <div style={{ fontSize: '12px', color: '#666' }}>编码: {record.product_code}</div>}
          {record.product_specification && <div style={{ fontSize: '12px', color: '#666' }}>规格: {record.product_specification}</div>}
        </div>
      )
    },
    {
      title: '单位',
      dataIndex: 'product_unit',
      key: 'product_unit',
      width: 80
    },
    {
      title: '可分拨数量',
      dataIndex: 'available_quantity',
      key: 'available_quantity',
      width: 100,
      render: (quantity) => <Text strong>{quantity}</Text>
    },
    {
      title: '分拨数量',
      key: 'selected_quantity',
      width: 120,
      render: (_, record, index) => (
        <InputNumber
          value={record.selected_quantity}
          onChange={(value) => updateDistributionItem(index, 'selected_quantity', value || 0)}
          min={0}
          max={record.available_quantity}
          style={{ width: '100%' }}
        />
      )
    },
    {
      title: '目标类型',
      key: 'target_type',
      width: 100,
      render: (_, record, index) => (
        <Select
          value={record.target_type}
          onChange={(value) => {
            updateDistributionItem(index, 'target_type', value);
            updateDistributionItem(index, 'target_id', null);
          }}
          style={{ width: '100%' }}
        >
          <Option value="store">门店</Option>
          <Option value="warehouse">仓库</Option>
        </Select>
      )
    },
    {
      title: '目标',
      key: 'target_id',
      width: 200,
      render: (_, record, index) => (
        <Select
          value={record.target_id}
          onChange={(value) => updateDistributionItem(index, 'target_id', value)}
          placeholder="选择目标"
          style={{ width: '100%' }}
          showSearch
          filterOption={(input, option) =>
            option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
          }
        >
          {(record.target_type === 'store' ? stores : warehouses).map(target => (
            <Option key={target.id} value={target.id}>
              {target.name}
            </Option>
          ))}
        </Select>
      )
    },
    {
      title: '小计',
      key: 'subtotal',
      width: 100,
      align: 'right',
      render: (_, record) => {
        const total = record.selected_quantity * record.unit_price;
        return `¥${total.toFixed(2)}`;
      }
    }
  ];

  return (
    <div>
      <Card size="small" style={{ marginBottom: 16 }}>
        <Row>
          <Col span={12}>
            <Text strong>采购订单号：</Text>{purchaseOrder?.order_number}
          </Col>
          <Col span={12}>
            <Text strong>订单状态：</Text>{purchaseOrder?.status}
          </Col>
        </Row>
      </Card>

      <Form form={form} layout="vertical">
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="arrival_type"
              label="分拨类型"
              initialValue="warehouse"
              rules={[{ required: true, message: '请选择分拨类型' }]}
            >
              <Select>
                <Option value="warehouse">仓库配货</Option>
                <Option value="market_purchase">市场采购</Option>
                <Option value="supplier_direct">供应商直送</Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="notes"
              label="备注"
            >
              <Input.TextArea rows={2} placeholder="输入备注信息..." />
            </Form.Item>
          </Col>
        </Row>
      </Form>

      <Divider />

      <div style={{ marginBottom: 16 }}>
        <Text strong>选择分拨商品：</Text>
      </div>

      <Table
        columns={columns}
        dataSource={distributionItems}
        rowKey="id"
        pagination={false}
        size="small"
        scroll={{ y: 300 }}
      />

      <div style={{ marginTop: 16, textAlign: 'right' }}>
        <Text strong>
          总计：¥{distributionItems.reduce((sum, item) => 
            sum + (item.selected_quantity * item.unit_price), 0
          ).toFixed(2)}
        </Text>
      </div>

      <div style={{ marginTop: 24, textAlign: 'right' }}>
        <Space>
          <Button onClick={onCancel}>
            取消
          </Button>
          <Button 
            type="primary" 
            onClick={handleSubmit}
            loading={loading}
          >
            创建分拨单
          </Button>
        </Space>
      </div>
    </div>
  );
};

export default CreateDistributionForm; 