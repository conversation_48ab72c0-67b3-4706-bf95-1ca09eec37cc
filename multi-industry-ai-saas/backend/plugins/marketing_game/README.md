# 营销游戏插件

营销游戏插件提供多种营销游戏活动，帮助客户线下互动，更好的留存。支持抽奖活动、消费满额抽奖、奖品设置、限制抽奖次数、内定中奖人、自动识别小票等功能。

## 功能特点

- **多种游戏类型**：支持抽奖活动、刮刮卡、幸运大转盘等多种游戏类型
- **奖品管理**：支持设置多种奖品，包括实物奖品、优惠券、积分和虚拟物品
- **参与者管理**：记录参与者信息，支持内定中奖人
- **游戏记录**：记录每次游戏的结果，包括中奖情况、领取状态等
- **小票识别**：支持自动识别小票信息，提取消费金额、日期、商品等信息

## 安装依赖

插件依赖以下 Python 包：

- **必需依赖**：
  - numpy >= 1.20.0
  - pillow >= 9.0.0

- **可选依赖**（用于真实 OCR 识别）：
  - pytesseract >= 0.3.10
  - opencv-python-headless >= 4.5.0

安装依赖：

```bash
pip install numpy pillow
pip install pytesseract opencv-python-headless  # 可选，用于真实 OCR 识别
```

此外，如果要使用真实 OCR 识别功能，还需要安装 Tesseract OCR 引擎：

### Windows:
1. 下载并安装 Tesseract: https://github.com/UB-Mannheim/tesseract/wiki
2. 将 Tesseract 安装路径添加到环境变量 PATH 中
3. 下载中文语言包：https://github.com/tesseract-ocr/tessdata/blob/main/chi_sim.traineddata
4. 将语言包放入 Tesseract 安装目录下的 tessdata 文件夹中

### Linux (Ubuntu/Debian):
```bash
sudo apt update
sudo apt install tesseract-ocr
sudo apt install tesseract-ocr-chi-sim  # 中文简体语言包
```

### macOS:
```bash
brew install tesseract
brew install tesseract-lang  # 安装所有语言包
```

## 使用方法

### 1. 创建营销游戏

```python
from plugins.marketing_game.schemas.schemas import MarketingGameCreate
from plugins.marketing_game.crud.crud import create_marketing_game

# 创建营销游戏
game = MarketingGameCreate(
    name="双11抽奖活动",
    description="消费满100元参与抽奖",
    game_type="lucky_draw",
    start_time=datetime(2023, 11, 1),
    end_time=datetime(2023, 11, 15),
    rules="消费满100元参与抽奖，每人每天最多抽奖3次",
    min_consumption=100,
    max_draws_per_user=3,
    status="published"
)

# 保存到数据库
game_db = await create_marketing_game(db, game, project_id)
```

### 2. 设置奖品

```python
from plugins.marketing_game.schemas.schemas import GamePrizeCreate
from plugins.marketing_game.crud.crud import create_game_prize

# 创建奖品
prize = GamePrizeCreate(
    name="iPhone 15",
    description="最新款iPhone",
    prize_type="physical",
    value=8999,
    quantity=1,
    probability=0.01,
    image_url="https://example.com/iphone15.jpg",
    is_default=False
)

# 保存到数据库
prize_db = await create_game_prize(db, prize, game_id)
```

### 3. 识别小票

```python
from plugins.marketing_game.utils.receipt_recognition import recognize_receipt

# 读取小票图片
with open("receipt.jpg", "rb") as f:
    image_data = f.read()

# 识别小票
result = await recognize_receipt(image_data)

# 检查识别结果
if result["success"]:
    print(f"消费总金额: {result['total_amount']}")
    print(f"会员ID: {result['member_id']}")
    print(f"商店名称: {result['store_name']}")
    print(f"交易时间: {result['transaction_time']}")
    print(f"小票ID: {result['receipt_id']}")
    print(f"商品列表: {result['items']}")
else:
    print(f"小票识别失败: {result['error_message']}")
```

### 4. 记录游戏结果

```python
from plugins.marketing_game.schemas.schemas import GameRecordCreate
from plugins.marketing_game.crud.crud import create_game_record

# 创建游戏记录
record = GameRecordCreate(
    participant_id=1,
    prize_id=1,
    receipt_id="R123456",
    consumption_amount=200,
    is_win=True,
    claim_status="unclaimed",
    remark="消费满200元，抽中iPhone 15"
)

# 保存到数据库
record_db = await create_game_record(db, record, game_id)
```

## API 接口

插件提供以下 API 接口：

### 游戏管理

- `GET /marketing-game/games`：获取游戏列表
- `POST /marketing-game/games`：创建新游戏
- `GET /marketing-game/games/{game_id}`：获取游戏详情
- `PUT /marketing-game/games/{game_id}`：更新游戏
- `DELETE /marketing-game/games/{game_id}`：删除游戏

### 奖品管理

- `GET /marketing-game/games/{game_id}/prizes`：获取奖品列表
- `POST /marketing-game/games/{game_id}/prizes`：创建新奖品
- `PUT /marketing-game/games/{game_id}/prizes/{prize_id}`：更新奖品
- `DELETE /marketing-game/games/{game_id}/prizes/{prize_id}`：删除奖品

### 参与者管理

- `GET /marketing-game/games/{game_id}/participants`：获取参与者列表
- `POST /marketing-game/games/{game_id}/participants`：创建新参与者

### 游戏记录

- `GET /marketing-game/games/{game_id}/records`：获取游戏记录列表
- `POST /marketing-game/games/{game_id}/records`：创建新游戏记录

### 小票识别

- `POST /marketing-game/games/{game_id}/receipt-recognition`：识别小票信息

## 配置选项

插件提供以下配置选项：

- `enable_receipt_recognition`：是否启用小票识别功能，默认为 `true`
- `use_real_ocr`：是否使用真实 OCR 识别小票，需要安装 pytesseract 和 opencv-python-headless，默认为 `false`
- `min_consumption_default`：默认最低消费金额，默认为 `100`
- `max_draws_per_user_default`：默认每用户最大抽奖次数，默认为 `3`

## 注意事项

1. 真实 OCR 识别功能需要安装 Tesseract OCR 引擎和相关 Python 包
2. 如果未安装 Tesseract OCR 引擎或相关 Python 包，插件将使用模拟模式进行小票识别
3. 小票识别功能的准确率取决于小票图片的质量和 Tesseract OCR 引擎的识别能力
4. 建议使用清晰、无反光、无褶皱的小票图片，以提高识别准确率
