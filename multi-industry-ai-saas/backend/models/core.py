#!/usr/bin/env python
# -*- coding: utf-8 -*-

from sqlalchemy import Column, String, <PERSON>olean, DateTime, ForeignKey, JSON, Text, Integer, UniqueConstraint
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid
from datetime import datetime

from db.database import Base


class ServiceProvider(Base):
    """服务商模型"""
    __tablename__ = "service_providers"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(100), nullable=False)
    company_name = Column(String(200), nullable=False)
    contact_person = Column(String(50), nullable=False)
    contact_email = Column(String(100), nullable=False)
    contact_phone = Column(String(20), nullable=False)
    status = Column(String(20), default="active", nullable=False)  # active, inactive, suspended
    commission_rate = Column(Integer, default=15, nullable=False)  # 分成比例，百分比
    supported_industries = Column(JSON, default=list)  # 支持的行业列表
    branding = Column(JSON, default=dict)  # 品牌设置，包括logo、颜色等
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 关系
    tenants = relationship("Tenant", back_populates="service_provider", cascade="all, delete-orphan")
    admins = relationship("User", back_populates="service_provider", primaryjoin="and_(User.service_provider_id==ServiceProvider.id, User.is_service_provider_admin==True)")
    subscription_plans = relationship("SubscriptionPlan", back_populates="service_provider", cascade="all, delete-orphan")
    plugin_sales = relationship("PluginPurchase", back_populates="service_provider")

    def __repr__(self):
        return f"<ServiceProvider(id={self.id}, name='{self.name}')>"


class Industry(Base):
    """行业模型"""
    __tablename__ = "industries"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    code = Column(String(50), nullable=False, unique=True)  # 行业代码
    name = Column(String(100), nullable=False)  # 行业名称
    description = Column(Text, nullable=True)  # 行业描述
    icon = Column(String(200), nullable=True)  # 行业图标
    is_active = Column(Boolean, default=True)  # 是否激活
    settings = Column(JSON, default=dict)  # 行业特定设置
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    def __repr__(self):
        return f"<Industry(id={self.id}, code='{self.code}', name='{self.name}')>"


class SubscriptionPlan(Base):
    """订阅计划模型"""
    __tablename__ = "subscription_plans"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    service_provider_id = Column(UUID(as_uuid=True), ForeignKey("service_providers.id"), nullable=True)  # 如果为空，则为系统级别的计划
    name = Column(String(100), nullable=False)
    description = Column(Text, nullable=True)
    price = Column(Integer, nullable=False)  # 价格（分）
    billing_cycle = Column(String(20), default="monthly")  # monthly, quarterly, yearly
    max_projects = Column(Integer, nullable=False)
    max_users_per_tenant = Column(Integer, nullable=False)
    max_storage_gb = Column(Integer, nullable=False)
    features = Column(JSON, default=list)  # 包含的功能列表
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 关系
    service_provider = relationship("ServiceProvider", back_populates="subscription_plans")
    tenants = relationship("Tenant", back_populates="subscription_plan")

    def __repr__(self):
        return f"<SubscriptionPlan(id={self.id}, name='{self.name}', price={self.price})>"
