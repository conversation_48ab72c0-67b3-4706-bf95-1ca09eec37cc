import React, { useState, useEffect } from 'react';
import { Card, Typography, Button, Table, Space, Tag, Modal, Form, Input, Select, Switch, Tabs, Spin, message, Tooltip, Popconfirm, Badge, Drawer, Descriptions, Alert } from 'antd';
import { PlusOutlined, ReloadOutlined, DeleteOutlined, EditOutlined, EyeOutlined, LinkOutlined, ApiOutlined, SyncOutlined } from '@ant-design/icons';
import ReactJson from 'react-json-view';
import { getEndpointList, createEndpoint, updateEndpoint, deleteEndpoint, syncEndpoints } from '../services/endpointService';

const { Title, Paragraph, Text } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;
const { TextArea } = Input;

/**
 * API端点管理组件
 */
const EndpointManage = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [syncLoading, setSyncLoading] = useState(false);
  const [endpoints, setEndpoints] = useState([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [modalTitle, setModalTitle] = useState('创建API端点');
  const [editingEndpoint, setEditingEndpoint] = useState(null);
  const [detailVisible, setDetailVisible] = useState(false);
  const [detailEndpoint, setDetailEndpoint] = useState(null);

  // 加载端点列表
  useEffect(() => {
    fetchEndpoints();
  }, []);

  // 获取端点列表
  const fetchEndpoints = async () => {
    setLoading(true);
    try {
      const response = await getEndpointList();
      if (response.success) {
        setEndpoints(response.data);
      } else {
        message.error('获取API端点列表失败');
      }
    } catch (error) {
      console.error('获取API端点列表失败:', error);
      message.error('获取API端点列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 同步API端点
  const handleSyncEndpoints = async () => {
    setSyncLoading(true);
    try {
      const response = await syncEndpoints();
      if (response.success) {
        message.success('API端点同步成功');
        fetchEndpoints();
      } else {
        message.error('API端点同步失败: ' + (response.message || '未知错误'));
      }
    } catch (error) {
      console.error('API端点同步失败:', error);
      message.error('API端点同步失败: ' + (error.message || '未知错误'));
    } finally {
      setSyncLoading(false);
    }
  };

  // 打开创建端点模态框
  const showCreateModal = () => {
    setModalTitle('创建API端点');
    setEditingEndpoint(null);
    form.resetFields();
    setModalVisible(true);
  };

  // 打开编辑端点模态框
  const showEditModal = (endpoint) => {
    setModalTitle('编辑API端点');
    setEditingEndpoint(endpoint);
    
    // 设置表单初始值
    form.setFieldsValue({
      name: endpoint.name,
      description: endpoint.description,
      endpoint_path: endpoint.endpoint_path,
      method: endpoint.method,
      enabled: endpoint.enabled,
      parameters: endpoint.parameters ? JSON.stringify(endpoint.parameters, null, 2) : '{}',
      response_template: endpoint.response_template || ''
    });
    
    setModalVisible(true);
  };

  // 处理表单提交
  const handleSubmit = async (values) => {
    try {
      // 解析参数
      let parameters = {};
      try {
        parameters = JSON.parse(values.parameters);
      } catch (error) {
        message.error('参数格式错误，请输入有效的JSON');
        return;
      }
      
      // 构建端点配置
      const endpointConfig = {
        ...values,
        parameters
      };
      
      if (editingEndpoint) {
        // 更新端点
        const response = await updateEndpoint(editingEndpoint.id, endpointConfig);
        if (response.success) {
          message.success('API端点更新成功');
          setModalVisible(false);
          fetchEndpoints();
        } else {
          message.error('API端点更新失败: ' + (response.message || '未知错误'));
        }
      } else {
        // 创建端点
        const response = await createEndpoint(endpointConfig);
        if (response.success) {
          message.success('API端点创建成功');
          setModalVisible(false);
          fetchEndpoints();
        } else {
          message.error('API端点创建失败: ' + (response.message || '未知错误'));
        }
      }
    } catch (error) {
      console.error('提交表单失败:', error);
      message.error('提交表单失败: ' + (error.message || '未知错误'));
    }
  };

  // 处理删除端点
  const handleDelete = async (endpointId) => {
    try {
      const response = await deleteEndpoint(endpointId);
      if (response.success) {
        message.success('API端点删除成功');
        fetchEndpoints();
      } else {
        message.error('API端点删除失败: ' + (response.message || '未知错误'));
      }
    } catch (error) {
      console.error('删除API端点失败:', error);
      message.error('删除API端点失败: ' + (error.message || '未知错误'));
    }
  };

  // 查看端点详情
  const showEndpointDetail = (endpoint) => {
    setDetailEndpoint(endpoint);
    setDetailVisible(true);
  };

  // 渲染HTTP方法标签
  const renderMethodTag = (method) => {
    const methodColors = {
      GET: 'green',
      POST: 'blue',
      PUT: 'orange',
      DELETE: 'red',
      PATCH: 'purple'
    };
    
    return <Tag color={methodColors[method] || 'default'}>{method}</Tag>;
  };

  // 表格列定义
  const columns = [
    {
      title: '状态',
      key: 'enabled',
      render: (_, record) => (
        <Badge 
          status={record.enabled ? 'success' : 'default'} 
          text={record.enabled ? '启用' : '禁用'}
        />
      ),
      width: 80,
    },
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      render: text => <Text strong>{text}</Text>
    },
    {
      title: '方法',
      dataIndex: 'method',
      key: 'method',
      render: method => renderMethodTag(method),
      width: 80,
    },
    {
      title: '路径',
      dataIndex: 'endpoint_path',
      key: 'endpoint_path',
      ellipsis: true,
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },
    {
      title: '参数',
      key: 'parameters',
      render: (_, record) => {
        const paramCount = record.parameters ? Object.keys(record.parameters).length : 0;
        return paramCount > 0 ? `${paramCount} 个参数` : '无参数';
      },
      width: 100,
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space>
          <Tooltip title="查看详情">
            <Button 
              icon={<EyeOutlined />} 
              size="small"
              onClick={() => showEndpointDetail(record)}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button 
              icon={<EditOutlined />} 
              size="small"
              onClick={() => showEditModal(record)}
            />
          </Tooltip>
          <Tooltip title="删除">
            <Popconfirm
              title="确定要删除此API端点吗？"
              onConfirm={() => handleDelete(record.id)}
              okText="确定"
              cancelText="取消"
            >
              <Button 
                icon={<DeleteOutlined />} 
                size="small"
                danger
              />
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <Card>
      <Title level={4}>API端点管理</Title>
      <Paragraph>
        管理API端点，包括创建、编辑和删除端点。API端点可以映射为MCP工具，供大语言模型使用。
      </Paragraph>

      <div style={{ marginBottom: 16 }}>
        <Space>
          <Button 
            type="primary" 
            icon={<PlusOutlined />}
            onClick={showCreateModal}
          >
            创建端点
          </Button>
          <Button 
            icon={<ReloadOutlined />}
            onClick={fetchEndpoints}
          >
            刷新列表
          </Button>
          <Button 
            icon={<SyncOutlined />}
            loading={syncLoading}
            onClick={handleSyncEndpoints}
          >
            同步系统API
          </Button>
        </Space>
      </div>

      <Spin spinning={loading}>
        <Table 
          dataSource={endpoints} 
          columns={columns} 
          rowKey="id"
          pagination={{ pageSize: 10 }}
        />
      </Spin>

      {/* 创建/编辑端点模态框 */}
      <Modal
        title={modalTitle}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={700}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            method: 'GET',
            enabled: true,
            parameters: '{}',
            response_template: ''
          }}
        >
          <Form.Item
            name="name"
            label="端点名称"
            rules={[{ required: true, message: '请输入端点名称' }]}
          >
            <Input placeholder="请输入端点名称" />
          </Form.Item>

          <Form.Item
            name="description"
            label="端点描述"
          >
            <TextArea 
              placeholder="请输入端点描述" 
              rows={2}
            />
          </Form.Item>

          <Form.Item
            name="endpoint_path"
            label="端点路径"
            rules={[{ required: true, message: '请输入端点路径' }]}
            tooltip="API端点的路径，例如: /api/v1/project/users"
          >
            <Input 
              placeholder="请输入端点路径" 
              addonBefore={<LinkOutlined />}
            />
          </Form.Item>

          <Form.Item
            name="method"
            label="HTTP方法"
            rules={[{ required: true, message: '请选择HTTP方法' }]}
          >
            <Select>
              <Option value="GET">GET</Option>
              <Option value="POST">POST</Option>
              <Option value="PUT">PUT</Option>
              <Option value="DELETE">DELETE</Option>
              <Option value="PATCH">PATCH</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="enabled"
            label="启用状态"
            valuePropName="checked"
            tooltip="是否启用此API端点"
          >
            <Switch />
          </Form.Item>

          <Form.Item
            name="parameters"
            label="参数定义"
            tooltip="JSON格式的参数定义，包含参数名称、类型、描述和是否必填"
          >
            <TextArea 
              placeholder="请输入JSON格式的参数定义" 
              rows={5}
            />
          </Form.Item>

          <Form.Item
            name="response_template"
            label="响应模板"
            tooltip="用于格式化API响应的模板，支持变量替换"
          >
            <TextArea 
              placeholder="请输入响应模板" 
              rows={3}
            />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                {editingEndpoint ? '更新' : '创建'}
              </Button>
              <Button onClick={() => setModalVisible(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 端点详情抽屉 */}
      <Drawer
        title={detailEndpoint?.name}
        placement="right"
        width={700}
        onClose={() => setDetailVisible(false)}
        open={detailVisible}
      >
        {detailEndpoint && (
          <Tabs defaultActiveKey="1">
            <TabPane 
              tab={<span><ApiOutlined /> 基本信息</span>} 
              key="1"
            >
              <Descriptions bordered column={1}>
                <Descriptions.Item label="ID">{detailEndpoint.id}</Descriptions.Item>
                <Descriptions.Item label="名称">{detailEndpoint.name}</Descriptions.Item>
                <Descriptions.Item label="描述">{detailEndpoint.description}</Descriptions.Item>
                <Descriptions.Item label="路径">{detailEndpoint.endpoint_path}</Descriptions.Item>
                <Descriptions.Item label="方法">
                  {renderMethodTag(detailEndpoint.method)}
                </Descriptions.Item>
                <Descriptions.Item label="状态">
                  <Badge 
                    status={detailEndpoint.enabled ? 'success' : 'default'} 
                    text={detailEndpoint.enabled ? '启用' : '禁用'}
                  />
                </Descriptions.Item>
                <Descriptions.Item label="创建时间">{new Date(detailEndpoint.created_at).toLocaleString()}</Descriptions.Item>
                <Descriptions.Item label="更新时间">{new Date(detailEndpoint.updated_at).toLocaleString()}</Descriptions.Item>
              </Descriptions>

              {detailEndpoint.parameters && Object.keys(detailEndpoint.parameters).length > 0 && (
                <div style={{ marginTop: 16 }}>
                  <Title level={5}>参数定义</Title>
                  <ReactJson 
                    src={detailEndpoint.parameters} 
                    name={false} 
                    collapsed={1}
                    displayDataTypes={false}
                  />
                </div>
              )}

              {detailEndpoint.response_template && (
                <div style={{ marginTop: 16 }}>
                  <Title level={5}>响应模板</Title>
                  <Card size="small">
                    <pre style={{ margin: 0 }}>{detailEndpoint.response_template}</pre>
                  </Card>
                </div>
              )}
            </TabPane>
            
            <TabPane 
              tab={<span><LinkOutlined /> 工具映射</span>} 
              key="2"
            >
              {detailEndpoint.mappings && detailEndpoint.mappings.length > 0 ? (
                <Table
                  dataSource={detailEndpoint.mappings}
                  rowKey="id"
                  pagination={false}
                  columns={[
                    {
                      title: '工具名称',
                      dataIndex: 'tool_name',
                      key: 'tool_name',
                      render: text => <Text strong>{text}</Text>
                    },
                    {
                      title: '服务器',
                      dataIndex: 'server_name',
                      key: 'server_name',
                    },
                    {
                      title: '状态',
                      key: 'enabled',
                      render: (_, record) => (
                        <Badge 
                          status={record.enabled ? 'success' : 'default'} 
                          text={record.enabled ? '启用' : '禁用'}
                        />
                      ),
                    },
                    {
                      title: '创建时间',
                      dataIndex: 'created_at',
                      key: 'created_at',
                      render: text => new Date(text).toLocaleString(),
                    }
                  ]}
                />
              ) : (
                <Alert
                  message="暂无工具映射"
                  description="此API端点尚未映射到任何MCP工具，请在工具映射管理中创建映射"
                  type="info"
                  showIcon
                />
              )}
            </TabPane>
          </Tabs>
        )}
      </Drawer>
    </Card>
  );
};

export default EndpointManage;
