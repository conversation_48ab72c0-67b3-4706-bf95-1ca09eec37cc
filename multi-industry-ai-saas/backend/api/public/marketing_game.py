#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
营销游戏公开API
用于支持分享链接，无需登录即可访问
"""

import logging
from typing import Dict, Any, List
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import and_
from sqlalchemy.orm import selectinload

from db.database import get_db
from plugins.marketing_game.models import MarketingGame, GamePrize, GameRecord, GameParticipant
from plugins.marketing_game.schemas import MarketingGameResponse, GamePrizeResponse

# 配置日志
logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter()

@router.get("/games/{game_id}", response_model=MarketingGameResponse)
async def get_public_game_detail(
    game_id: str,
    db: AsyncSession = Depends(get_db)
):
    """
    获取营销游戏详情（公开接口）
    
    Args:
        game_id: 游戏ID
        db: 数据库会话
        
    Returns:
        游戏详情
    """
    try:
        # 转换game_id为整数
        try:
            game_id_int = int(game_id)
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无效的游戏ID"
            )
        
        # 查询游戏
        query = select(MarketingGame).options(
            selectinload(MarketingGame.prizes)
        ).where(
            and_(
                MarketingGame.id == game_id_int,
                MarketingGame.status.in_(["published", "active"])  # 只返回已发布或活跃的游戏
            )
        )
        result = await db.execute(query)
        game = result.scalar_one_or_none()
        
        if not game:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="游戏不存在或未发布"
            )
        
        # 检查游戏是否在有效期内
        from datetime import datetime
        now = datetime.now()
        
        if game.end_time and now > game.end_time:
            # 游戏已结束，但仍然返回数据，让前端处理
            pass
        
        if game.start_time and now < game.start_time:
            # 游戏未开始
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="游戏尚未开始"
            )
        
        return game
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取公开游戏详情失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取游戏详情失败"
        )

@router.get("/games/{game_id}/prizes", response_model=List[GamePrizeResponse])
async def get_public_game_prizes(
    game_id: str,
    db: AsyncSession = Depends(get_db)
):
    """
    获取营销游戏奖品列表（公开接口）
    支持奖品的细粒度配置过滤
    
    Args:
        game_id: 游戏ID
        db: 数据库会话
        
    Returns:
        当前可用的奖品列表
    """
    try:
        from datetime import datetime
        
        # 转换game_id为整数
        try:
            game_id_int = int(game_id)
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无效的游戏ID"
            )
        
        # 先检查游戏是否存在且已发布
        game_query = select(MarketingGame).where(
            and_(
                MarketingGame.id == game_id_int,
                MarketingGame.status.in_(["published", "active"])
            )
        )
        game_result = await db.execute(game_query)
        game = game_result.scalar_one_or_none()
        
        if not game:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="游戏不存在或未发布"
            )
        
        current_time = datetime.now()
        
        # 查询所有奖品
        prizes_query = select(GamePrize).where(
            GamePrize.game_id == game_id_int
        ).order_by(GamePrize.probability.desc())
        
        prizes_result = await db.execute(prizes_query)
        all_prizes = prizes_result.scalars().all()
        
        # 过滤可用奖品
        available_prizes = []
        
        for prize in all_prizes:
            # 检查库存
            if prize.remaining <= 0 and not prize.is_default:
                continue
                
            # 检查生效日期和失效日期
            if prize.effective_date and current_time < prize.effective_date:
                continue
                
            if prize.expiry_date and current_time > prize.expiry_date:
                continue
            
            # 检查每日生效时间段
            if prize.effective_time_start and prize.effective_time_end:
                current_time_str = current_time.strftime("%H:%M:%S")
                if not (prize.effective_time_start <= current_time_str <= prize.effective_time_end):
                    continue
            
            # 检查游戏开始天数限制
            if game.start_time:
                days_since_start = (current_time - game.start_time).days + 1
                
                # 检查最小天数限制
                if prize.min_days_since_game_start > 0:
                    if days_since_start < prize.min_days_since_game_start:
                        continue
                
                # 检查最大天数限制
                if prize.max_days_since_game_start > 0:
                    if days_since_start > prize.max_days_since_game_start:
                        continue
            
            # 检查每日限量（简化版本，不考虑具体用户）
            if prize.daily_limit > 0:
                # 这里可以添加每日限量检查逻辑
                # 为了简化，暂时跳过
                pass
            
            available_prizes.append(prize)
        
        # 确保至少有一个默认奖品
        has_default = any(prize.is_default for prize in available_prizes)
        if not has_default:
            # 创建一个临时的默认奖品
            default_prize = GamePrize(
                id=0,
                game_id=int(game_id),
                name="谢谢参与",
                description="再接再厉",
                prize_type="virtual",
                value=0,
                quantity=999999,
                remaining=999999,
                probability=0,
                image_url="https://img.icons8.com/color/96/000000/sad--v1.png",
                is_default=True,
                weight_factor=1.0
            )
            available_prizes.append(default_prize)
        
        return available_prizes
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取公开游戏奖品失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取奖品列表失败"
        )

@router.post("/games/{game_id}/draw")
async def public_draw_prize(
    game_id: str,
    participant_data: dict,
    db: AsyncSession = Depends(get_db)
):
    """
    公开抽奖接口（用于外部分享页面）
    
    Args:
        game_id: 游戏ID
        participant_data: 参与者数据 (phone, name, receipt_code, receipt_amount等)
        db: 数据库会话
        
    Returns:
        抽奖结果
    """
    try:
        # 转换game_id为整数
        try:
            game_id_int = int(game_id)
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无效的游戏ID"
            )
        
        # 先检查游戏是否存在且已发布
        game_query = select(MarketingGame).where(
            and_(
                MarketingGame.id == game_id_int,
                MarketingGame.status.in_(["published", "active"])
            )
        )
        game_result = await db.execute(game_query)
        game = game_result.scalar_one_or_none()
        
        if not game:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="游戏不存在或未发布"
            )
        
        # 检查游戏时间
        from datetime import datetime
        current_time = datetime.now()
        if current_time < game.start_time:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="游戏尚未开始"
            )
        if current_time > game.end_time:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="游戏已结束"
            )
        
        # 验证参与者信息
        phone = participant_data.get('phone', '')
        name = participant_data.get('name', '匿名用户')
        receipt_code = participant_data.get('receipt_code', '')
        receipt_amount = participant_data.get('receipt_amount', 0)
        
        # 检查是否需要手机验证
        if game.settings and game.settings.get('enable_phone_verification'):
            if not phone or len(phone) != 11:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="请提供有效的手机号码"
                )
        
        # 检查是否需要小票验证
        if game.min_consumption > 0:
            if receipt_amount < game.min_consumption:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"消费金额需满{game.min_consumption}元"
                )
        
        # 创建或获取参与者
        from plugins.marketing_game.models.models import GameParticipant
        
        # 查找现有参与者（通过手机号）
        participant = None
        if phone:
            participant_query = select(GameParticipant).where(
                and_(
                    GameParticipant.game_id == game_id_int,
                    GameParticipant.phone == phone
                )
            )
            participant_result = await db.execute(participant_query)
            participant = participant_result.scalar_one_or_none()
        
        # 如果没有找到参与者，创建新的
        if not participant:
            participant = GameParticipant(
                game_id=int(game_id_int),
                name=name,
                phone=phone,
                draws_count=0,
                is_winner=False,
                is_fixed_winner=False
            )
            db.add(participant)
            await db.flush()  # 获取ID
        
        # 检查抽奖次数限制
        if participant.draws_count >= game.max_draws_per_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"已达到最大抽奖次数({game.max_draws_per_user}次)"
            )
        
        # 获取奖品列表并执行抽奖逻辑
        prizes_query = select(GamePrize).where(
            GamePrize.game_id == game_id_int
        ).order_by(GamePrize.probability.desc())
        
        prizes_result = await db.execute(prizes_query)
        all_prizes = prizes_result.scalars().all()
        
        if not all_prizes:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="游戏暂无奖品"
            )
        
        # 过滤可用奖品（应用细粒度配置）
        available_prizes = []
        default_prize = None
        
        logger.info(f"开始过滤奖品，总奖品数: {len(all_prizes)}")
        
        for prize in all_prizes:
            logger.info(f"Enhanced 检查奖品: ID={prize.id}, Name={prize.name}, IsDefault={prize.is_default}, Remaining={prize.remaining}, Probability={prize.probability}")
            
            # 记录默认奖品
            if prize.is_default:
                default_prize = prize
                logger.info(f"找到默认奖品: {prize.name}")
                continue
                
            # 检查库存
            if prize.remaining <= 0:
                logger.info(f"Enhanced 奖品 {prize.name} 库存不足，跳过 (remaining={prize.remaining})")
                continue
                
            # 检查生效日期和失效日期
            if prize.effective_date and current_time < prize.effective_date:
                logger.info(f"Enhanced 奖品 {prize.name} 还未生效 (生效日期: {prize.effective_date}, 当前时间: {current_time})，跳过")
                continue
                
            if prize.expiry_date and current_time > prize.expiry_date:
                logger.info(f"Enhanced 奖品 {prize.name} 已过期 (失效日期: {prize.expiry_date}, 当前时间: {current_time})，跳过")
                continue
            
            # 检查每日生效时间段
            if prize.effective_time_start and prize.effective_time_end:
                current_time_str = current_time.strftime("%H:%M:%S")
                if not (prize.effective_time_start <= current_time_str <= prize.effective_time_end):
                    logger.info(f"Enhanced 奖品 {prize.name} 不在生效时间段 ({prize.effective_time_start}-{prize.effective_time_end}, 当前时间: {current_time_str})，跳过")
                    continue
            
            # 检查游戏开始天数限制
            if game.start_time:
                days_since_start = (current_time - game.start_time).days + 1
                
                # 检查最小天数限制
                if prize.min_days_since_game_start > 0:
                    if days_since_start < prize.min_days_since_game_start:
                        logger.info(f"Enhanced 奖品 {prize.name} 最小天数限制 (需要第{prize.min_days_since_game_start}天，当前第{days_since_start}天)，跳过")
                        continue
                
                # 检查最大天数限制
                if prize.max_days_since_game_start > 0:
                    if days_since_start > prize.max_days_since_game_start:
                        logger.info(f"Enhanced 奖品 {prize.name} 最大天数限制 (最大第{prize.max_days_since_game_start}天，当前第{days_since_start}天)，跳过")
                        continue
            
            # 检查每日限量、每小时限量、每用户限量等
            # 这里可以添加更复杂的限量检查逻辑
            
            logger.info(f"Enhanced 奖品 {prize.name} 通过所有检查，添加到可用奖品列表")
            available_prizes.append(prize)
        
        logger.info(f"过滤完成，可用奖品数: {len(available_prizes)}")
        
        # 构建用于角度计算的完整奖品列表（包含默认奖品）
        prizes_for_angle_calc = available_prizes.copy()
        if default_prize:
            prizes_for_angle_calc.append(default_prize)
        
        # 检查保底奖品条件
        guaranteed_prize = None
        for prize in available_prizes:
            if prize.is_guaranteed and prize.guaranteed_condition:
                # 检查保底条件（例如：连续未中奖次数）
                condition = prize.guaranteed_condition
                if isinstance(condition, dict):
                    # 获取用户历史记录检查保底条件
                    records_query = select(GameRecord).where(
                        and_(
                            GameRecord.game_id == game_id_int,
                            GameRecord.participant_id == participant.id
                        )
                    ).order_by(GameRecord.draw_time.desc()).limit(10)
                    
                    recent_records_result = await db.execute(records_query)
                    recent_records = recent_records_result.scalars().all()
                    
                    # 检查连续未中奖次数
                    consecutive_losses = 0
                    for record in recent_records:
                        if not record.is_win:
                            consecutive_losses += 1
                        else:
                            break
                    
                    min_losses = condition.get('min_consecutive_losses', 0)
                    if consecutive_losses >= min_losses:
                        guaranteed_prize = prize
                        break
        
        selected_prize = None
        is_win = False
        
        # 如果有保底奖品，直接使用
        if guaranteed_prize:
            selected_prize = guaranteed_prize
            is_win = True
            logger.info(f"触发保底奖品: {guaranteed_prize.name}")
        else:
            # 正常抽奖逻辑
            if available_prizes:
                # 计算加权概率
                weighted_prizes = []
                total_weight = 0
                
                for prize in available_prizes:
                    # 应用权重因子
                    weighted_probability = prize.probability * (prize.weight_factor or 1.0)
                    weighted_prizes.append({
                        'prize': prize,
                        'weighted_probability': weighted_probability
                    })
                    total_weight += weighted_probability
                
                if total_weight > 0:
                    # 生成随机数
                    import random
                    random_value = random.random() * total_weight
                    cumulative_weight = 0
                    
                    # 按加权概率选择奖品
                    for item in weighted_prizes:
                        cumulative_weight += item['weighted_probability']
                        if random_value <= cumulative_weight:
                            selected_prize = item['prize']
                            is_win = True
                            break
        
        # 如果没有选中任何奖品，使用默认奖品
        if not selected_prize:
            if default_prize:
                selected_prize = default_prize
                is_win = False
            else:
                # 创建临时默认奖品
                selected_prize = type('TempPrize', (), {
                    'id': 0,
                    'name': '谢谢参与',
                    'description': '再接再厉',
                    'image_url': 'https://img.icons8.com/color/96/000000/sad--v1.png',
                    'value': 0,
                    'is_default': True
                })()
                is_win = False
        
        # 创建抽奖记录
        from plugins.marketing_game.models.models import GameRecord
        
        record = GameRecord(
            game_id=int(game_id_int),
            participant_id=participant.id,
            prize_id=selected_prize.id if hasattr(selected_prize, 'id') and selected_prize.id > 0 else None,
            is_win=is_win,
            draw_time=current_time,
            draw_method="guaranteed" if guaranteed_prize else "random",
            consumption_amount=receipt_amount,
            probability_used=selected_prize.probability if hasattr(selected_prize, 'probability') else 0,
            random_number=None  # 可以记录随机数用于调试
        )
        db.add(record)
        
        # 更新参与者统计
        participant.draws_count += 1
        if is_win:
            participant.is_winner = True
        
        # 更新参与者的总消费金额
        if receipt_amount > 0:
            participant.total_consumption = (participant.total_consumption or 0) + receipt_amount
        
        # 如果中奖且不是默认奖品，减少奖品库存
        if is_win and selected_prize and not selected_prize.is_default and hasattr(selected_prize, 'remaining'):
            selected_prize.remaining = max(0, selected_prize.remaining - 1)
        
        await db.commit()
        
        # 计算指针应该指向的角度（用于前端动画）
        pointer_angle = 0
        if hasattr(selected_prize, 'id') and selected_prize.id > 0:
            # 计算与前端一致的角度分配
            # 计算总概率
            total_probability = sum(prize.probability or 0 for prize in prizes_for_angle_calc)
            
            # 如果总概率为0，使用平均分配
            use_equal_distribution = total_probability == 0
            
            # 计算每个奖品的角度
            current_angle = 0
            for i, prize in enumerate(prizes_for_angle_calc):
                if use_equal_distribution:
                    # 平均分配
                    angle = 360 / len(prizes_for_angle_calc)
                else:
                    # 根据概率分配
                    probability = prize.probability or 0
                    angle = (probability / total_probability) * 360
                
                # 如果是目标奖品，计算指针应该指向的角度
                if prize.id == selected_prize.id:
                    # 指向奖品区域的中心
                    pointer_angle = current_angle + (angle / 2)
                    break
                
                current_angle += angle
        
        # 返回结果
        result = {
            "success": True,
            "is_win": is_win,
            "record_id": record.id,
            "prize_id": selected_prize.id if hasattr(selected_prize, 'id') else None,
            "prize": {
                "id": selected_prize.id if hasattr(selected_prize, 'id') else 0,
                "name": selected_prize.name,
                "description": getattr(selected_prize, 'description', ''),
                "image_url": getattr(selected_prize, 'image_url', ''),
                "value": getattr(selected_prize, 'value', 0),
                "is_default": getattr(selected_prize, 'is_default', False)
            },
            "pointer_angle": pointer_angle,  # 新增：指针应该指向的角度
            "remaining_draws": game.max_draws_per_user - participant.draws_count,
            "message": "恭喜中奖！" if is_win else "谢谢参与！",
            "draw_method": "guaranteed" if guaranteed_prize else "random",  # 新增：抽奖方式
            # 调试信息
            "debug_info": {
                "total_prizes": len(all_prizes),
                "available_prizes_count": len(available_prizes),
                "available_prizes": [{"id": p.id, "name": p.name, "probability": p.probability} for p in available_prizes],
                "selected_prize_info": {
                    "id": getattr(selected_prize, 'id', None),
                    "name": getattr(selected_prize, 'name', ''),
                    "is_default": getattr(selected_prize, 'is_default', False),
                    "effective_date": getattr(selected_prize, 'effective_date', None),
                    "expiry_date": getattr(selected_prize, 'expiry_date', None),
                    "min_days_since_game_start": getattr(selected_prize, 'min_days_since_game_start', 0),
                    "max_days_since_game_start": getattr(selected_prize, 'max_days_since_game_start', 0)
                }
            }
        }
        
        logger.info(f"Enhanced 公开抽奖完整结果: {result}")
        logger.info(f"Enhanced 选中奖品详情: ID={getattr(selected_prize, 'id', 'None')}, Name={getattr(selected_prize, 'name', 'None')}, Is_Default={getattr(selected_prize, 'is_default', 'None')}")
        logger.info(f"Enhanced 是否中奖: {is_win}")
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"公开抽奖失败: {str(e)}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="抽奖失败"
        )

@router.post("/games/{game_id}/claim/{record_id}")
async def claim_prize(
    game_id: str,
    record_id: int,
    participant_data: dict,
    db: AsyncSession = Depends(get_db)
):
    """
    领取奖品（公开接口）
    
    Args:
        game_id: 游戏ID
        record_id: 中奖记录ID
        participant_data: 参与者数据（包含手机号等验证信息）
        db: 数据库会话
        
    Returns:
        领取结果
    """
    try:
        from datetime import datetime
        
        # 转换game_id为整数
        try:
            game_id_int = int(game_id)
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无效的游戏ID"
            )
        
        # 验证游戏存在
        game_query = select(MarketingGame).where(
            and_(
                MarketingGame.id == game_id_int,
                MarketingGame.status.in_(["published", "active"])
            )
        )
        game_result = await db.execute(game_query)
        game = game_result.scalar_one_or_none()
        
        if not game:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="游戏不存在或未发布"
            )
        
        # 查询中奖记录
        record_query = select(GameRecord).options(
            selectinload(GameRecord.prize),
            selectinload(GameRecord.participant)
        ).where(
            and_(
                GameRecord.id == record_id,
                GameRecord.game_id == game_id_int,
                GameRecord.is_win == True
            )
        )
        record_result = await db.execute(record_query)
        record = record_result.scalar_one_or_none()
        
        if not record:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="中奖记录不存在"
            )
        
        # 验证参与者身份（通过手机号）
        phone = participant_data.get('phone', '')
        if not phone or record.participant.phone != phone:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="身份验证失败"
            )
        
        # 检查是否已经领取
        if record.claim_status == "claimed":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="奖品已经领取过了"
            )
        
        # 更新领取状态
        record.claim_status = "claimed"
        record.claim_time = datetime.now()
        
        await db.commit()
        
        return {
            "success": True,
            "message": "奖品领取成功",
            "data": {
                "record_id": record.id,
                "prize_name": record.prize.name if record.prize else "未知奖品",
                "claim_time": record.claim_time.isoformat() if record.claim_time else None
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"领取奖品失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="领取奖品失败"
        )

@router.get("/games/{game_id}/prizes/query/{phone}")
async def query_prizes_by_phone(
    game_id: str,
    phone: str,
    db: AsyncSession = Depends(get_db)
):
    """
    根据手机号查询中奖记录（公开接口）
    
    Args:
        game_id: 游戏ID
        phone: 手机号
        db: 数据库会话
        
    Returns:
        中奖记录列表
    """
    try:
        # 转换game_id为整数
        try:
            game_id_int = int(game_id)
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无效的游戏ID"
            )
        
        # 验证游戏存在
        game_query = select(MarketingGame).where(
            and_(
                MarketingGame.id == game_id_int,
                MarketingGame.status.in_(["published", "active"])
            )
        )
        game_result = await db.execute(game_query)
        game = game_result.scalar_one_or_none()
        
        if not game:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="游戏不存在或未发布"
            )
        
        # 查询参与者
        participant_query = select(GameParticipant).where(
            and_(
                GameParticipant.game_id == game_id_int,
                GameParticipant.phone == phone
            )
        )
        participant_result = await db.execute(participant_query)
        participant = participant_result.scalar_one_or_none()
        
        if not participant:
            return {
                "success": True,
                "message": "未找到参与记录",
                "data": {
                    "total_records": 0,
                    "win_records": [],
                    "total_draws": 0,
                    "total_wins": 0
                }
            }
        
        # 查询中奖记录
        records_query = select(GameRecord).options(
            selectinload(GameRecord.prize)
        ).where(
            and_(
                GameRecord.game_id == game_id_int,
                GameRecord.participant_id == participant.id
            )
        ).order_by(GameRecord.draw_time.desc())
        
        records_result = await db.execute(records_query)
        all_records = records_result.scalars().all()
        
        # 过滤中奖记录
        win_records = [record for record in all_records if record.is_win and record.prize]
        
        # 构建返回数据
        win_data = []
        for record in win_records:
            win_data.append({
                "id": record.id,
                "prize_name": record.prize.name,
                "prize_description": record.prize.description,
                "prize_value": record.prize.value,
                "prize_image": record.prize.image_url,
                "draw_time": record.draw_time.isoformat() if record.draw_time else None,
                "claim_status": record.claim_status,
                "claim_time": record.claim_time.isoformat() if record.claim_time else None
            })
        
        return {
            "success": True,
            "message": "查询成功",
            "data": {
                "participant_name": participant.name,
                "participant_phone": participant.phone,
                "total_records": len(all_records),
                "total_draws": len(all_records),
                "total_wins": len(win_records),
                "win_records": win_data
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"查询中奖记录失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="查询中奖记录失败"
        )

@router.post("/games/{game_id}/verify-phone")
async def verify_phone_public(
    game_id: str,
    verification_data: dict,
    db: AsyncSession = Depends(get_db)
):
    """
    公开手机号验证接口
    
    Args:
        game_id: 游戏ID
        verification_data: 验证数据（包含手机号等）
        db: 数据库会话
        
    Returns:
        验证结果
    """
    try:
        # 转换game_id为整数
        try:
            game_id_int = int(game_id)
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无效的游戏ID"
            )
        
        # 验证游戏存在
        game_query = select(MarketingGame).where(
            and_(
                MarketingGame.id == game_id_int,
                MarketingGame.status.in_(["published", "active"])
            )
        )
        game_result = await db.execute(game_query)
        game = game_result.scalar_one_or_none()
        
        if not game:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="游戏不存在或未发布"
            )
        
        # 获取验证数据
        phone = verification_data.get('phone', '')
        amount = verification_data.get('amount', 0)
        consumption_amount = verification_data.get('consumption_amount', amount)
        
        # 验证手机号格式
        if not phone or len(phone) != 11 or not phone.startswith('1'):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="请输入有效的手机号码"
            )
        
        # 检查消费金额
        if game.min_consumption > 0 and consumption_amount < game.min_consumption:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"消费金额不能低于{game.min_consumption}元"
            )
        
        # 查找或创建参与者
        participant_query = select(GameParticipant).where(
            and_(
                GameParticipant.game_id == game_id_int,
                GameParticipant.phone == phone
            )
        )
        participant_result = await db.execute(participant_query)
        participant = participant_result.scalar_one_or_none()
        
        if not participant:
            # 创建新参与者
            participant = GameParticipant(
                game_id=game_id_int,
                name=f"用户{phone[-4:]}",
                phone=phone,
                draws_count=0,
                is_winner=False,
                is_fixed_winner=False
            )
            db.add(participant)
            await db.commit()
            await db.refresh(participant)
        
        return {
            "success": True,
            "message": "手机号验证成功",
            "data": {
                "participant": {
                    "id": participant.id,
                    "name": participant.name,
                    "phone": participant.phone
                }
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"公开手机号验证失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="手机号验证失败"
        )

@router.post("/games/{game_id}/verify-receipt")
async def verify_receipt_public(
    game_id: str,
    verification_data: dict,
    db: AsyncSession = Depends(get_db)
):
    """
    公开小票验证接口
    
    Args:
        game_id: 游戏ID
        verification_data: 验证数据（包含小票号码等）
        db: 数据库会话
        
    Returns:
        验证结果
    """
    try:
        # 转换game_id为整数
        try:
            game_id_int = int(game_id)
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="无效的游戏ID"
            )
        
        # 验证游戏存在
        game_query = select(MarketingGame).where(
            and_(
                MarketingGame.id == game_id_int,
                MarketingGame.status.in_(["published", "active"])
            )
        )
        game_result = await db.execute(game_query)
        game = game_result.scalar_one_or_none()
        
        if not game:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="游戏不存在或未发布"
            )
        
        # 获取验证数据
        receipt_code = verification_data.get('receipt_code', '')
        amount = verification_data.get('amount', 0)
        consumption_amount = verification_data.get('consumption_amount', amount)
        
        # 验证小票号码
        if not receipt_code:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="请输入小票号码"
            )
        
        # 检查消费金额
        if game.min_consumption > 0 and consumption_amount < game.min_consumption:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"小票金额不能低于{game.min_consumption}元"
            )
        
        # 这里可以添加小票验证逻辑
        # 简化版本：直接通过验证
        
        return {
            "success": True,
            "message": "小票验证成功",
            "data": {
                "receipt_code": receipt_code,
                "amount": consumption_amount
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"公开小票验证失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="小票验证失败"
        ) 