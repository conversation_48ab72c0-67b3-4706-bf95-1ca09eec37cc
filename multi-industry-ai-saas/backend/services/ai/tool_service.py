#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
AI 工具服务
"""

import logging
import uuid
import json
import re
import math
from typing import List, Optional, Dict, Any, Union
from datetime import datetime, timedelta
from sqlalchemy import select, func, and_, or_, desc, text
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import joinedload

from models.ai import AIAssistantTool
from models.project import Project
from models.tenant import Tenant
from models.user import User
from models.product import Product
from models.sales_report import SalesReport
from models.inventory import InventoryItem

logger = logging.getLogger(__name__)

class AIToolService:
    """AI 工具服务"""

    @staticmethod
    async def create_builtin_tools(
        db: AsyncSession,
        tenant_id: uuid.UUID,
        project_id: Optional[uuid.UUID] = None,
        user_id: Optional[uuid.UUID] = None,
    ) -> List[AIAssistantTool]:
        """
        创建内置工具
        """
        # 检查是否已存在内置工具
        query = select(AIAssistantTool).where(
            and_(
                AIAssistantTool.tenant_id == tenant_id,
                AIAssistantTool.is_builtin == True
            )
        )

        if project_id:
            query = query.where(AIAssistantTool.project_id == project_id)

        result = await db.execute(query)
        existing_tools = result.scalars().all()

        # 如果已存在内置工具，则返回
        if existing_tools:
            return existing_tools

        # 创建内置工具
        tools = []

        # 1. 搜索工具
        search_tool = AIAssistantTool(
            tenant_id=tenant_id,
            project_id=project_id,
            name="数据搜索工具",
            description="搜索系统中的数据，包括产品、销售、库存等信息",
            type="function",
            function_name="search_data",
            function_description="搜索系统中的数据",
            parameters_schema={
                "type": "object",
                "properties": {
                    "data_type": {
                        "type": "string",
                        "enum": ["product", "sales", "inventory"],
                        "description": "要搜索的数据类型"
                    },
                    "query": {
                        "type": "string",
                        "description": "搜索关键词"
                    },
                    "limit": {
                        "type": "integer",
                        "description": "返回结果数量限制",
                        "default": 10
                    }
                },
                "required": ["data_type", "query"]
            },
            implementation="""
async def search_data(db, project_id, data_type, query, limit=10):
    if data_type == "product":
        # 搜索产品
        products_query = select(Product).where(
            and_(
                Product.project_id == project_id,
                or_(
                    Product.name.ilike(f"%{query}%"),
                    Product.description.ilike(f"%{query}%"),
                    Product.sku.ilike(f"%{query}%")
                )
            )
        ).limit(limit)

        result = await db.execute(products_query)
        products = result.scalars().all()

        return [
            {
                "id": str(product.id),
                "name": product.name,
                "sku": product.sku,
                "price": product.price,
                "description": product.description,
                "category": product.category
            }
            for product in products
        ]

    elif data_type == "sales":
        # 搜索销售记录
        sales_query = select(SalesReport).where(
            and_(
                SalesReport.project_id == project_id,
                or_(
                    SalesReport.store_name.ilike(f"%{query}%"),
                    SalesReport.channel.ilike(f"%{query}%"),
                    SalesReport.payment_method.ilike(f"%{query}%")
                )
            )
        ).limit(limit)

        result = await db.execute(sales_query)
        sales = result.scalars().all()

        return [
            {
                "id": str(sale.id),
                "date": sale.date.strftime("%Y-%m-%d"),
                "store_name": sale.store_name,
                "channel": sale.channel,
                "amount": sale.amount,
                "payment_method": sale.payment_method
            }
            for sale in sales
        ]

    elif data_type == "inventory":
        # 搜索库存
        inventory_query = select(InventoryItem).join(Product).where(
            and_(
                InventoryItem.project_id == project_id,
                or_(
                    Product.name.ilike(f"%{query}%"),
                    Product.sku.ilike(f"%{query}%")
                )
            )
        ).limit(limit)

        result = await db.execute(inventory_query)
        inventories = result.scalars().all()

        return [
            {
                "id": str(inventory.id),
                "product_name": inventory.product.name,
                "warehouse_name": inventory.warehouse.name,
                "quantity": inventory.quantity,
                "last_updated": inventory.updated_at.strftime("%Y-%m-%d %H:%M:%S")
            }
            for inventory in inventories
        ]

    return []
            """,
            is_builtin=True,
            status="active",
            created_by=user_id,
        )

        db.add(search_tool)
        tools.append(search_tool)

        # 2. 计算工具
        calculator_tool = AIAssistantTool(
            tenant_id=tenant_id,
            project_id=project_id,
            name="数据计算工具",
            description="执行数学计算和数据处理",
            type="function",
            function_name="calculate",
            function_description="执行数学计算和数据处理",
            parameters_schema={
                "type": "object",
                "properties": {
                    "expression": {
                        "type": "string",
                        "description": "要计算的数学表达式"
                    }
                },
                "required": ["expression"]
            },
            implementation="""
import re
import math

def calculate(expression):
    # 安全地计算数学表达式
    # 只允许基本数学运算和一些数学函数

    # 清理表达式
    expression = expression.strip()

    # 检查是否包含不安全的代码
    if re.search(r'[^0-9+\-*/().,%\s]', expression):
        # 检查是否使用了允许的数学函数
        allowed_funcs = ['sin', 'cos', 'tan', 'sqrt', 'log', 'log10', 'exp', 'abs', 'round', 'floor', 'ceil']
        for func in allowed_funcs:
            expression = expression.replace(func, '')

        # 再次检查是否包含不安全的代码
        if re.search(r'[^0-9+\-*/().,%\s]', expression):
            return {"error": "表达式包含不允许的字符或函数"}

    try:
        # 创建安全的局部环境
        safe_dict = {
            'sin': math.sin,
            'cos': math.cos,
            'tan': math.tan,
            'sqrt': math.sqrt,
            'log': math.log,
            'log10': math.log10,
            'exp': math.exp,
            'abs': abs,
            'round': round,
            'floor': math.floor,
            'ceil': math.ceil,
            'pi': math.pi,
            'e': math.e
        }

        # 计算表达式
        result = eval(expression, {"__builtins__": {}}, safe_dict)

        return {
            "expression": expression,
            "result": result
        }
    except Exception as e:
        return {"error": f"计算错误: {str(e)}"}
            """,
            is_builtin=True,
            status="active",
            created_by=user_id,
        )

        db.add(calculator_tool)
        tools.append(calculator_tool)

        # 3. 数据分析工具
        analysis_tool = AIAssistantTool(
            tenant_id=tenant_id,
            project_id=project_id,
            name="数据分析工具",
            description="分析系统中的数据，生成统计报告",
            type="function",
            function_name="analyze_data",
            function_description="分析系统中的数据，生成统计报告",
            parameters_schema={
                "type": "object",
                "properties": {
                    "data_type": {
                        "type": "string",
                        "enum": ["sales", "inventory", "product"],
                        "description": "要分析的数据类型"
                    },
                    "time_range": {
                        "type": "string",
                        "enum": ["day", "week", "month", "year"],
                        "description": "时间范围",
                        "default": "month"
                    },
                    "group_by": {
                        "type": "string",
                        "enum": ["store", "channel", "payment_method", "category"],
                        "description": "分组方式",
                        "default": "channel"
                    }
                },
                "required": ["data_type"]
            },
            implementation="""
from datetime import datetime, timedelta
from sqlalchemy import func, and_, desc

async def analyze_data(db, project_id, data_type, time_range="month", group_by="channel"):
    # 计算时间范围
    now = datetime.now()

    if time_range == "day":
        start_date = now.replace(hour=0, minute=0, second=0, microsecond=0)
    elif time_range == "week":
        start_date = now - timedelta(days=now.weekday())
        start_date = start_date.replace(hour=0, minute=0, second=0, microsecond=0)
    elif time_range == "month":
        start_date = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
    elif time_range == "year":
        start_date = now.replace(month=1, day=1, hour=0, minute=0, second=0, microsecond=0)
    else:
        start_date = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)  # 默认为本月

    if data_type == "sales":
        # 分析销售数据
        if group_by == "store":
            # 按门店分组
            query = select(
                SalesReport.store_name,
                func.sum(SalesReport.amount).label("total_amount"),
                func.count().label("transaction_count")
            ).where(
                and_(
                    SalesReport.project_id == project_id,
                    SalesReport.date >= start_date
                )
            ).group_by(SalesReport.store_name).order_by(desc("total_amount"))

            result = await db.execute(query)
            data = result.mappings().all()

            return {
                "time_range": time_range,
                "group_by": "store",
                "data": [
                    {
                        "store_name": item["store_name"],
                        "total_amount": float(item["total_amount"]),
                        "transaction_count": item["transaction_count"],
                        "average_transaction": float(item["total_amount"]) / item["transaction_count"] if item["transaction_count"] > 0 else 0
                    }
                    for item in data
                ]
            }

        elif group_by == "channel":
            # 按渠道分组
            query = select(
                SalesReport.channel,
                func.sum(SalesReport.amount).label("total_amount"),
                func.count().label("transaction_count")
            ).where(
                and_(
                    SalesReport.project_id == project_id,
                    SalesReport.date >= start_date
                )
            ).group_by(SalesReport.channel).order_by(desc("total_amount"))

            result = await db.execute(query)
            data = result.mappings().all()

            return {
                "time_range": time_range,
                "group_by": "channel",
                "data": [
                    {
                        "channel": item["channel"],
                        "total_amount": float(item["total_amount"]),
                        "transaction_count": item["transaction_count"],
                        "average_transaction": float(item["total_amount"]) / item["transaction_count"] if item["transaction_count"] > 0 else 0
                    }
                    for item in data
                ]
            }

        elif group_by == "payment_method":
            # 按支付方式分组
            query = select(
                SalesReport.payment_method,
                func.sum(SalesReport.amount).label("total_amount"),
                func.count().label("transaction_count")
            ).where(
                and_(
                    SalesReport.project_id == project_id,
                    SalesReport.date >= start_date
                )
            ).group_by(SalesReport.payment_method).order_by(desc("total_amount"))

            result = await db.execute(query)
            data = result.mappings().all()

            return {
                "time_range": time_range,
                "group_by": "payment_method",
                "data": [
                    {
                        "payment_method": item["payment_method"],
                        "total_amount": float(item["total_amount"]),
                        "transaction_count": item["transaction_count"],
                        "average_transaction": float(item["total_amount"]) / item["transaction_count"] if item["transaction_count"] > 0 else 0
                    }
                    for item in data
                ]
            }

    elif data_type == "inventory":
        # 分析库存数据
        query = select(
            Product.category,
            func.sum(InventoryItem.quantity).label("total_quantity"),
            func.count().label("product_count")
        ).join(Product).where(
            InventoryItem.project_id == project_id
        ).group_by(Product.category).order_by(desc("total_quantity"))

        result = await db.execute(query)
        data = result.mappings().all()

        return {
            "data_type": "inventory",
            "group_by": "category",
            "data": [
                {
                    "category": item["category"],
                    "total_quantity": item["total_quantity"],
                    "product_count": item["product_count"],
                    "average_quantity": item["total_quantity"] / item["product_count"] if item["product_count"] > 0 else 0
                }
                for item in data
            ]
        }

    elif data_type == "product":
        # 分析产品数据
        if group_by == "category":
            # 按类别分组
            query = select(
                Product.category,
                func.count().label("product_count"),
                func.avg(Product.price).label("average_price")
            ).where(
                Product.project_id == project_id
            ).group_by(Product.category)

            result = await db.execute(query)
            data = result.mappings().all()

            return {
                "data_type": "product",
                "group_by": "category",
                "data": [
                    {
                        "category": item["category"],
                        "product_count": item["product_count"],
                        "average_price": float(item["average_price"])
                    }
                    for item in data
                ]
            }

    return {"error": "不支持的数据类型或分组方式"}
            """,
            is_builtin=True,
            status="active",
            created_by=user_id,
        )

        db.add(analysis_tool)
        tools.append(analysis_tool)

        # 提交事务
        await db.commit()

        # 刷新对象
        for tool in tools:
            await db.refresh(tool)

        return tools
