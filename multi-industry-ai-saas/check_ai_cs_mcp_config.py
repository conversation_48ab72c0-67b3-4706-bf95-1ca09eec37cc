#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
检查AI客服MCP工具配置
"""

import asyncio
import sys
import os
import uuid

# 添加项目根目录到Python路径
sys.path.append('/app')

async def check_ai_cs_mcp_config():
    """检查AI客服MCP工具配置"""
    print("=== 检查AI客服MCP工具配置 ===\n")
    
    try:
        # 导入必要的模块
        from sqlalchemy.ext.asyncio import AsyncSession
        from db.database import get_db
        from plugins.ai_customer_service.models.ai_integration import AICustomerServiceIntegration
        from models.ai_mcp_server import AIMCPServer
        from sqlalchemy import select
        
        project_id = uuid.UUID("*************-48ab-8092-e8eb7f663677")
        
        # 创建数据库会话
        db_gen = get_db()
        db = await db_gen.__anext__()
        
        try:
            # 1. 检查AI客服集成配置
            print("1. 检查AI客服集成配置...")
            result = await db.execute(
                select(AICustomerServiceIntegration).where(
                    AICustomerServiceIntegration.project_id == project_id
                )
            )
            config = result.scalars().first()
            
            if config:
                print(f"   ✅ 找到AI客服配置")
                print(f"      项目ID: {config.project_id}")
                print(f"      聊天模型ID: {config.ai_model_id}")
                print(f"      启用MCP工具: {config.enable_mcp_tools}")
                print(f"      MCP服务器IDs: {config.mcp_server_ids}")
                print(f"      知识库IDs: {config.knowledge_base_ids}")
                print(f"      启用知识库搜索: {config.enable_knowledge_search}")
            else:
                print("   ❌ 未找到AI客服配置")
                return
            
            # 2. 检查MCP服务器配置
            print("\n2. 检查MCP服务器配置...")
            if config.mcp_server_ids:
                mcp_server_ids = config.mcp_server_ids
                if isinstance(mcp_server_ids, str):
                    import json
                    try:
                        mcp_server_ids = json.loads(mcp_server_ids)
                    except:
                        mcp_server_ids = [mcp_server_ids]
                
                print(f"   配置的MCP服务器数量: {len(mcp_server_ids)}")
                
                for i, server_id in enumerate(mcp_server_ids, 1):
                    try:
                        server_uuid = uuid.UUID(str(server_id))
                        result = await db.execute(
                            select(AIMCPServer).where(AIMCPServer.id == server_uuid)
                        )
                        server = result.scalars().first()
                        
                        if server:
                            print(f"   服务器{i}: ✅ {server.name}")
                            print(f"      ID: {server.id}")
                            print(f"      状态: {server.status}")
                            print(f"      启用: {server.enabled}")
                            print(f"      传输类型: {server.transport_type}")
                            print(f"      可用工具数量: {len(server.available_tools) if server.available_tools else 0}")
                            
                            if server.available_tools:
                                for j, tool in enumerate(server.available_tools[:3], 1):
                                    print(f"         工具{j}: {tool.get('name', 'N/A')} - {tool.get('description', 'N/A')}")
                        else:
                            print(f"   服务器{i}: ❌ 未找到 (ID: {server_id})")
                    except Exception as e:
                        print(f"   服务器{i}: ❌ 错误 - {e}")
            else:
                print("   ⚠️  未配置MCP服务器")
            
            # 3. 检查AI客服引擎的配置加载
            print("\n3. 测试AI客服引擎配置加载...")
            try:
                from plugins.ai_customer_service.core.ai_engine import AICustomerServiceEngine
                
                engine = AICustomerServiceEngine()
                await engine.initialize(project_id=project_id, db=db)
                
                print(f"   ✅ AI客服引擎初始化成功")
                print(f"      项目配置已加载: {engine.project_config is not None}")
                
                if engine.project_config:
                    print(f"      启用MCP工具: {engine.project_config.get('enable_mcp_tools', False)}")
                    print(f"      MCP服务器IDs: {engine.project_config.get('mcp_server_ids', [])}")
                    print(f"      聊天模型ID: {engine.project_config.get('chat_model_id', 'N/A')}")
                
            except Exception as e:
                print(f"   ❌ AI客服引擎初始化失败: {e}")
            
            # 4. 测试MCP工具获取
            print("\n4. 测试从MCP服务器获取工具...")
            try:
                if config.enable_mcp_tools and config.mcp_server_ids:
                    from services.ai.assistant_service import AIAssistantService
                    
                    mcp_server_ids = config.mcp_server_ids
                    if isinstance(mcp_server_ids, str):
                        import json
                        try:
                            mcp_server_ids = json.loads(mcp_server_ids)
                        except:
                            mcp_server_ids = [mcp_server_ids]
                    
                    # 转换为UUID列表
                    uuid_mcp_server_ids = []
                    for server_id in mcp_server_ids:
                        try:
                            uuid_mcp_server_ids.append(uuid.UUID(str(server_id)))
                        except ValueError:
                            continue
                    
                    if uuid_mcp_server_ids:
                        tools = await AIAssistantService.get_mcp_tools_from_servers(
                            db, project_id, uuid_mcp_server_ids
                        )
                        
                        print(f"   ✅ 成功获取 {len(tools)} 个MCP工具")
                        
                        for i, tool in enumerate(tools[:5], 1):
                            function_spec = tool.get("function", {})
                            print(f"      工具{i}: {function_spec.get('name', 'N/A')} - {function_spec.get('description', 'N/A')}")
                    else:
                        print("   ⚠️  没有有效的MCP服务器ID")
                else:
                    print("   ⚠️  MCP工具未启用或未配置")
                    
            except Exception as e:
                print(f"   ❌ MCP工具获取失败: {e}")
                
        finally:
            await db.close()
                
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(check_ai_cs_mcp_config()) 