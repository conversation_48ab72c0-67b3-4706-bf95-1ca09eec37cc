#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
API代理路由

负责将API请求转发到MCP工具
"""

import json
import time
import logging
import uuid
from datetime import datetime
from typing import Dict, Any, Optional

from fastapi import APIRouter, Depends, HTTPException, Request, Response
from sqlalchemy import and_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from db.database import get_db
from models.user import User
from core.auth import get_current_user_optional

from ..models import SAPIEndpoint, SAPIToken, SAPIUsageLog, APIMCPToolMapping, MCPServer
from .. import router

logger = logging.getLogger(__name__)

# 创建动态路由处理器
# 注意：这个路由应该放在所有其他路由之后，以避免路由冲突
# 特别是不要匹配 token-applications 相关的路径
@router.api_route("/api/{path:path}", methods=["GET", "POST", "PUT", "DELETE"], include_in_schema=False)
async def api_proxy(
    path: str,
    request: Request,
    db: AsyncSession = Depends(get_db),
    current_user: Optional[User] = Depends(get_current_user_optional)
):
    """API代理路由，将API请求转发到MCP工具"""
    try:
        # 获取请求方法
        method = request.method

        # 构建完整路径
        full_path = f"/{path}"
        logger.info(f"收到API请求: {method} {full_path}")

        # 查询匹配的API端点
        result = await db.execute(
            select(SAPIEndpoint).where(
                and_(
                    SAPIEndpoint.endpoint_path == full_path,
                    SAPIEndpoint.method == method,
                    SAPIEndpoint.enabled == True
                )
            )
        )
        endpoint = result.scalars().first()

        if not endpoint:
            logger.warning(f"未找到匹配的API端点: {method} {full_path}")
            return Response(
                content=json.dumps({"error": "未找到匹配的API端点"}),
                status_code=404,
                media_type="application/json"
            )

        # 检查认证
        if endpoint.requires_auth:
            # 获取认证头
            auth_header = request.headers.get("Authorization")
            if not auth_header:
                logger.warning(f"缺少认证头: {method} {full_path}")
                return Response(
                    content=json.dumps({"error": "缺少认证头"}),
                    status_code=401,
                    media_type="application/json"
                )

            # 解析认证头
            auth_parts = auth_header.split()
            if len(auth_parts) != 2 or auth_parts[0].lower() != "bearer":
                logger.warning(f"认证头格式错误: {method} {full_path}")
                return Response(
                    content=json.dumps({"error": "认证头格式错误"}),
                    status_code=401,
                    media_type="application/json"
                )

            token_value = auth_parts[1]

            # 查询令牌
            result = await db.execute(
                select(SAPIToken).where(
                    and_(
                        SAPIToken.token == token_value,
                        SAPIToken.status == "active"
                    )
                )
            )
            token = result.scalars().first()

            if not token:
                logger.warning(f"无效的令牌: {method} {full_path}")
                return Response(
                    content=json.dumps({"error": "无效的令牌"}),
                    status_code=401,
                    media_type="application/json"
                )

            # 检查令牌是否过期
            if token.expires_at and token.expires_at < datetime.now():
                logger.warning(f"令牌已过期: {method} {full_path}")
                return Response(
                    content=json.dumps({"error": "令牌已过期"}),
                    status_code=401,
                    media_type="application/json"
                )

            # 检查令牌权限
            if method != "GET" and token.permissions == "read":
                logger.warning(f"令牌权限不足: {method} {full_path}")
                return Response(
                    content=json.dumps({"error": "令牌权限不足"}),
                    status_code=403,
                    media_type="application/json"
                )

            # 更新令牌最后使用时间
            token.last_used = datetime.now()
            await db.commit()

            # 设置用户
            if not current_user:
                # 查询令牌关联的用户
                result = await db.execute(
                    select(User).where(User.id == token.user_id)
                )
                current_user = result.scalars().first()

        # 获取请求数据
        request_data = {}
        if method in ["POST", "PUT"]:
            try:
                request_data = await request.json()
            except:
                try:
                    request_data = await request.form()
                    request_data = dict(request_data)
                except:
                    pass

        # 记录开始时间
        start_time = time.time()

        # 查询API到MCP映射
        result = await db.execute(
            select(APIMCPToolMapping).where(APIMCPToolMapping.endpoint_id == endpoint.id)
        )
        mapping = result.scalars().first()

        if mapping:
            # 查询MCP服务器
            result = await db.execute(
                select(MCPServer).where(MCPServer.id == mapping.server_id)
            )
            server = result.scalars().first()

            if not server:
                logger.warning(f"未找到MCP服务器: {mapping.server_id}")
                return Response(
                    content=json.dumps({"error": "未找到MCP服务器"}),
                    status_code=500,
                    media_type="application/json"
                )

            # 转换请求参数
            tool_params = {}
            if mapping.parameter_mapping:
                for api_field, tool_field in mapping.parameter_mapping.items():
                    if api_field in request_data:
                        tool_params[tool_field] = request_data[api_field]
            else:
                # 如果没有参数映射，直接使用请求数据
                tool_params = request_data

            # 构建MCP请求
            from ..mcp_server_adapter import mcp_server_adapter
            mcp_response = await mcp_server_adapter.call_tool(
                server_id=str(server.id),
                tool_name=mapping.tool_name,
                tool_params=tool_params
            )

            # 计算执行时间
            execution_time = (time.time() - start_time) * 1000  # 毫秒

            # 检查MCP响应
            if "error" in mcp_response:
                logger.warning(f"MCP工具调用失败: {mcp_response['error']}")

                # 记录使用日志
                usage_log = SAPIUsageLog(
                    id=uuid.uuid4(),
                    user_id=current_user.id if current_user else None,
                    endpoint_id=endpoint.id,
                    request_data=request_data,
                    response_data={"error": mcp_response["error"]},
                    status_code=500,
                    execution_time=execution_time,
                    token_id=token.id if "token" in locals() else None
                )
                db.add(usage_log)
                await db.commit()

                return Response(
                    content=json.dumps({"error": mcp_response["error"]}),
                    status_code=500,
                    media_type="application/json"
                )

            # 提取工具结果
            tool_result = None
            for message in mcp_response.get("choices", [{}])[0].get("message", {}).get("tool_calls", []):
                if message.get("function", {}).get("name") == mapping.tool_name:
                    try:
                        tool_result = json.loads(message.get("function", {}).get("arguments", "{}"))
                        break
                    except:
                        pass

            if not tool_result:
                # 如果找不到工具结果，尝试从消息内容中提取
                content = mcp_response.get("choices", [{}])[0].get("message", {}).get("content", "")
                try:
                    # 尝试解析JSON
                    tool_result = json.loads(content)
                except:
                    # 如果解析失败，使用文本内容
                    tool_result = {"result": content}

            # 转换响应
            api_response = {}
            if mapping.response_mapping:
                for tool_field, api_field in mapping.response_mapping.items():
                    if tool_field in tool_result:
                        api_response[api_field] = tool_result[tool_field]
            else:
                # 如果没有响应映射，直接使用工具结果
                api_response = tool_result

            # 记录使用日志
            usage_log = SAPIUsageLog(
                id=uuid.uuid4(),
                user_id=current_user.id if current_user else None,
                endpoint_id=endpoint.id,
                request_data=request_data,
                response_data=api_response,
                status_code=200,
                execution_time=execution_time,
                token_id=token.id if "token" in locals() else None
            )
            db.add(usage_log)
            await db.commit()

            return Response(
                content=json.dumps(api_response),
                status_code=200,
                media_type="application/json"
            )
        else:
            # 如果没有映射，返回模拟响应
            # 在实际实现中，这里应该调用真实的API处理逻辑

            # 构建模拟响应
            api_response = {
                "success": True,
                "message": "API请求处理成功",
                "data": request_data
            }

            # 计算执行时间
            execution_time = (time.time() - start_time) * 1000  # 毫秒

            # 记录使用日志
            usage_log = SAPIUsageLog(
                id=uuid.uuid4(),
                user_id=current_user.id if current_user else None,
                endpoint_id=endpoint.id,
                request_data=request_data,
                response_data=api_response,
                status_code=200,
                execution_time=execution_time,
                token_id=token.id if "token" in locals() else None
            )
            db.add(usage_log)
            await db.commit()

            return Response(
                content=json.dumps(api_response),
                status_code=200,
                media_type="application/json"
            )
    except Exception as e:
        logger.error(f"处理API请求失败: {str(e)}")
        return Response(
            content=json.dumps({"error": f"处理API请求失败: {str(e)}"}),
            status_code=500,
            media_type="application/json"
        )
