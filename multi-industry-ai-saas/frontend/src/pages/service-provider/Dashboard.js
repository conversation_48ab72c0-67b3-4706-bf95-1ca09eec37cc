import React, { useState, useEffect } from 'react';
import { Row, Col, Card, Statistic, Typography, Table, Button, Tag, Divider, Space, Progress, List, Avatar } from 'antd';
import {
  ShopOutlined,
  UserOutlined,
  AppstoreOutlined,
  CloudServerOutlined,
  RiseOutlined,
  TeamOutlined,
  SettingOutlined,
  PlusOutlined,
  EyeOutlined,
  EditOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import apiService from '../../services/api';
import { useAuth } from '../../contexts/AuthContext';
import './Dashboard.css';

const { Title, Text } = Typography;

const ServiceProviderDashboard = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    tenantCount: 0,
    activeTenantsCount: 0,
    totalUsers: 0,
    totalProjects: 0,
    totalStorage: 0,
    totalRevenue: 0
  });
  const [tenants, setTenants] = useState([]);
  const [recentActivities, setRecentActivities] = useState([]);

  // 加载数据
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);

        // 获取服务商统计数据
        // 实际项目中应该调用真实API
        const mockStats = {
          tenantCount: 28,
          activeTenantsCount: 24,
          totalUsers: 156,
          totalProjects: 42,
          totalStorage: 128, // GB
          totalRevenue: 25800 // 元
        };
        setStats(mockStats);

        // 获取租户列表
        if (user && user.service_provider_id) {
          const tenantsResponse = await apiService.getTenantsByServiceProvider(user.service_provider_id);
          setTenants(tenantsResponse || []);
        } else {
          console.log('用户没有服务商ID，使用模拟数据');
          // 使用模拟数据
          const mockTenants = [
            { id: '1', name: '优鲜生活超市', industry_type: 'retail', status: 'active', max_users: 20, current_users: 15, max_storage_gb: 50, current_storage_gb: 35, subscription_plan_name: '专业版' },
            { id: '2', name: '全家便利店', industry_type: 'retail', status: 'active', max_users: 15, current_users: 10, max_storage_gb: 30, current_storage_gb: 20, subscription_plan_name: '标准版' },
            { id: '3', name: '好邻居超市', industry_type: 'retail', status: 'trial', max_users: 10, current_users: 5, max_storage_gb: 20, current_storage_gb: 5, subscription_plan_name: '试用版' },
            { id: '4', name: '华润万家', industry_type: 'retail', status: 'active', max_users: 30, current_users: 25, max_storage_gb: 100, current_storage_gb: 80, subscription_plan_name: '企业版' },
            { id: '5', name: '永辉超市', industry_type: 'retail', status: 'active', max_users: 25, current_users: 20, max_storage_gb: 80, current_storage_gb: 60, subscription_plan_name: '专业版' }
          ];
          setTenants(mockTenants);
        }

        // 获取最近活动
        const mockActivities = [
          { id: 1, type: 'tenant_created', tenant: '优鲜生活超市', time: '2023-06-15 14:30', user: '张经理' },
          { id: 2, type: 'plan_upgraded', tenant: '全家便利店', time: '2023-06-14 09:15', user: '李店长' },
          { id: 3, type: 'storage_added', tenant: '好邻居超市', time: '2023-06-13 16:45', user: '王管理' },
          { id: 4, type: 'plugin_activated', tenant: '华润万家', time: '2023-06-12 11:20', user: '赵经理' },
          { id: 5, type: 'user_added', tenant: '永辉超市', time: '2023-06-11 10:05', user: '刘店长' }
        ];
        setRecentActivities(mockActivities);
      } catch (error) {
        console.error('获取数据失败:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [user]);

  // 租户表格列
  const tenantColumns = [
    {
      title: '租户名称',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <Space>
          <Avatar icon={<ShopOutlined />} style={{ backgroundColor: '#1890ff' }} />
          <Text strong>{text}</Text>
        </Space>
      )
    },
    {
      title: '行业',
      dataIndex: 'industry_type',
      key: 'industry_type',
      render: (text) => {
        const industryMap = {
          'retail': { color: 'blue', name: '零售' },
          'restaurant': { color: 'orange', name: '餐饮' },
          'hotel': { color: 'purple', name: '酒店' }
        };
        const industry = industryMap[text] || { color: 'default', name: text };
        return <Tag color={industry.color}>{industry.name}</Tag>;
      }
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => {
        const statusMap = {
          'active': { color: 'success', text: '正常' },
          'inactive': { color: 'default', text: '未激活' },
          'suspended': { color: 'error', text: '已暂停' },
          'trial': { color: 'processing', text: '试用中' }
        };
        const statusInfo = statusMap[status] || { color: 'default', text: status };
        return <Tag color={statusInfo.color}>{statusInfo.text}</Tag>;
      }
    },
    {
      title: '用户数',
      dataIndex: 'max_users',
      key: 'max_users',
      render: (max, record) => (
        <Space direction="vertical" size={0} style={{ width: '100%' }}>
          <Text>{record.current_users || 0}/{max}</Text>
          <Progress
            percent={Math.round(((record.current_users || 0) / max) * 100)}
            size="small"
            showInfo={false}
            status={((record.current_users || 0) / max) > 0.8 ? 'exception' : 'normal'}
          />
        </Space>
      )
    },
    {
      title: '存储空间',
      dataIndex: 'max_storage_gb',
      key: 'max_storage_gb',
      render: (max, record) => (
        <Space direction="vertical" size={0} style={{ width: '100%' }}>
          <Text>{record.current_storage_gb || 0}/{max} GB</Text>
          <Progress
            percent={Math.round(((record.current_storage_gb || 0) / max) * 100)}
            size="small"
            showInfo={false}
            status={((record.current_storage_gb || 0) / max) > 0.8 ? 'exception' : 'normal'}
          />
        </Space>
      )
    },
    {
      title: '订阅计划',
      dataIndex: 'subscription_plan_name',
      key: 'subscription_plan_name',
      render: (text) => <Tag color="gold">{text || '标准版'}</Tag>
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="small">
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => navigate(`/service-provider/tenants/${record.id}`)}
          />
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => navigate(`/service-provider/tenants/${record.id}/edit`)}
          />
          <Button
            type="text"
            icon={<SettingOutlined />}
            onClick={() => navigate(`/service-provider/tenants/${record.id}/settings`)}
          />
        </Space>
      )
    }
  ];

  // 活动类型图标
  const getActivityIcon = (type) => {
    switch (type) {
      case 'tenant_created':
        return <ShopOutlined style={{ color: '#1890ff' }} />;
      case 'plan_upgraded':
        return <RiseOutlined style={{ color: '#52c41a' }} />;
      case 'storage_added':
        return <CloudServerOutlined style={{ color: '#722ed1' }} />;
      case 'plugin_activated':
        return <AppstoreOutlined style={{ color: '#fa8c16' }} />;
      case 'user_added':
        return <UserOutlined style={{ color: '#eb2f96' }} />;
      default:
        return <SettingOutlined style={{ color: '#1890ff' }} />;
    }
  };

  // 活动类型文本
  const getActivityText = (activity) => {
    switch (activity.type) {
      case 'tenant_created':
        return <Text>{activity.user} 创建了新租户 <Text strong>{activity.tenant}</Text></Text>;
      case 'plan_upgraded':
        return <Text>{activity.tenant} 升级了订阅计划</Text>;
      case 'storage_added':
        return <Text>{activity.tenant} 增加了存储空间</Text>;
      case 'plugin_activated':
        return <Text>{activity.tenant} 激活了新插件</Text>;
      case 'user_added':
        return <Text>{activity.tenant} 添加了新用户 {activity.user}</Text>;
      default:
        return <Text>{activity.tenant} 有新活动</Text>;
    }
  };

  return (
    <div className="service-provider-dashboard">
      <div className="dashboard-header">
        <Title level={2}><CloudServerOutlined /> 服务商管理中心</Title>
        <Text type="secondary">管理您的租户、订阅计划和收益</Text>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} className="stats-row">
        <Col xs={24} sm={12} md={8} lg={4}>
          <Card bordered={false} className="stat-card">
            <Statistic
              title="租户总数"
              value={stats.tenantCount}
              prefix={<ShopOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
            <div className="stat-footer">
              <Text type="secondary">活跃: {stats.activeTenantsCount}</Text>
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={12} md={8} lg={4}>
          <Card bordered={false} className="stat-card">
            <Statistic
              title="用户总数"
              value={stats.totalUsers}
              prefix={<TeamOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
            <div className="stat-footer">
              <Text type="secondary">平均: {Math.round(stats.totalUsers / (stats.tenantCount || 1))}/租户</Text>
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={12} md={8} lg={4}>
          <Card bordered={false} className="stat-card">
            <Statistic
              title="项目总数"
              value={stats.totalProjects}
              prefix={<AppstoreOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
            <div className="stat-footer">
              <Text type="secondary">平均: {Math.round(stats.totalProjects / (stats.tenantCount || 1))}/租户</Text>
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={12} md={8} lg={4}>
          <Card bordered={false} className="stat-card">
            <Statistic
              title="存储空间"
              value={stats.totalStorage}
              suffix="GB"
              prefix={<CloudServerOutlined />}
              valueStyle={{ color: '#fa8c16' }}
            />
            <div className="stat-footer">
              <Text type="secondary">平均: {Math.round(stats.totalStorage / (stats.tenantCount || 1))} GB/租户</Text>
            </div>
          </Card>
        </Col>
        <Col xs={24} sm={12} md={8} lg={8}>
          <Card bordered={false} className="stat-card revenue-card">
            <Statistic
              title="总收益"
              value={stats.totalRevenue}
              prefix={<RiseOutlined />}
              suffix="元"
              valueStyle={{ color: '#cf1322' }}
            />
            <div className="stat-footer">
              <Text type="secondary">平均: {Math.round(stats.totalRevenue / (stats.tenantCount || 1))} 元/租户</Text>
            </div>
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
        {/* 租户列表 */}
        <Col xs={24} lg={16}>
          <Card
            title={<Title level={4}><ShopOutlined /> 租户管理</Title>}
            extra={
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => navigate('/service-provider/tenants/create')}
              >
                创建租户
              </Button>
            }
            bordered={false}
            className="tenants-card"
          >
            <Table
              columns={tenantColumns}
              dataSource={tenants}
              rowKey="id"
              loading={loading}
              pagination={{ pageSize: 5 }}
            />
          </Card>
        </Col>

        {/* 最近活动 */}
        <Col xs={24} lg={8}>
          <Card
            title={<Title level={4}><RiseOutlined /> 最近活动</Title>}
            bordered={false}
            className="activities-card"
          >
            <List
              itemLayout="horizontal"
              dataSource={recentActivities}
              renderItem={item => (
                <List.Item>
                  <List.Item.Meta
                    avatar={<Avatar icon={getActivityIcon(item.type)} />}
                    title={getActivityText(item)}
                    description={item.time}
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>
      </Row>

      {/* 快速操作 */}
      <Card
        title={<Title level={4}><SettingOutlined /> 快速操作</Title>}
        bordered={false}
        style={{ marginTop: 16 }}
        className="quick-actions-card"
      >
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={12} md={8} lg={6}>
            <Button
              type="default"
              icon={<ShopOutlined />}
              onClick={() => navigate('/service-provider/tenants')}
              block
              size="large"
              className="action-button"
            >
              租户管理
            </Button>
          </Col>
          <Col xs={24} sm={12} md={8} lg={6}>
            <Button
              type="default"
              icon={<AppstoreOutlined />}
              onClick={() => navigate('/service-provider/subscription-plans')}
              block
              size="large"
              className="action-button"
            >
              订阅计划
            </Button>
          </Col>
          <Col xs={24} sm={12} md={8} lg={6}>
            <Button
              type="default"
              icon={<CloudServerOutlined />}
              onClick={() => navigate('/service-provider/plugins')}
              block
              size="large"
              className="action-button"
            >
              插件管理
            </Button>
          </Col>
          <Col xs={24} sm={12} md={8} lg={6}>
            <Button
              type="default"
              icon={<SettingOutlined />}
              onClick={() => navigate('/service-provider/settings')}
              block
              size="large"
              className="action-button"
            >
              服务商设置
            </Button>
          </Col>
        </Row>
      </Card>
    </div>
  );
};

export default ServiceProviderDashboard;
