import React, { useState, useEffect } from 'react';
import {
  Card, Table, Button, Space, Modal, Form, Input, Select, Switch,
  message, Popconfirm, Tag, Tooltip, Badge, Drawer, Typography,
  Row, Col, Statistic, Alert, Tabs, Divider
} from 'antd';
import {
  PlusOutlined, EditOutlined, DeleteOutlined, PlayCircleOutlined,
  StopOutlined, SettingOutlined, ApiOutlined, CloudServerOutlined,
  BugOutlined, CheckCircleOutlined, ExclamationCircleOutlined,
  InfoCircleOutlined, EyeOutlined, ArrowLeftOutlined, ToolOutlined,
  UploadOutlined
} from '@ant-design/icons';
import { useProject } from '../../contexts/ProjectContext';
import { useNavigate } from 'react-router-dom';
import apiService from '../../services/api';

const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;
const { TabPane } = Tabs;
const { Option } = Select;

/**
 * MCP服务器管理页面
 */
const MCPServerManagement = () => {
  const { currentProject } = useProject();
  const navigate = useNavigate();
  const [servers, setServers] = useState([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [detailDrawerVisible, setDetailDrawerVisible] = useState(false);
  const [editingServer, setEditingServer] = useState(null);
  const [selectedServer, setSelectedServer] = useState(null);
  const [serverTools, setServerTools] = useState([]);
  const [form] = Form.useForm();
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [createForm] = Form.useForm();
  const [jsonImportModalVisible, setJsonImportModalVisible] = useState(false);
  const [jsonImportForm] = Form.useForm();

  // 获取服务器列表
  const fetchServers = async () => {
    try {
      setLoading(true);
      
      // 直接从AI助手的MCP服务器数据库获取所有服务器
      const response = await apiService.project.ai.mcpServers.list(currentProject.id);
      
      if (response.success !== false) {
        const servers = response.data || response || [];
        
        // 为每个服务器获取工具数量
        const serversWithToolCount = await Promise.all(
          servers.map(async (server) => {
            try {
              // 统一使用AI助手的MCP服务器工具API
              const tools = await apiService.project.ai.mcpServers.getTools(currentProject.id, server.id);
              server.tools_count = Array.isArray(tools) ? tools.length : 0;
            } catch (error) {
              console.warn(`获取服务器 ${server.id} 工具数量失败:`, error);
              server.tools_count = 0;
            }
            
            // 确保transport_types是数组格式
            if (!server.transport_types && server.transport_type) {
              server.transport_types = [server.transport_type];
            }
            
            return server;
          })
        );
        
                 console.log('🔍 获取到的MCP服务器列表:', serversWithToolCount);
         setServers(serversWithToolCount);
       } else {
         console.error('获取MCP服务器列表失败:', response.message);
         message.error('获取服务器列表失败');
         setServers([]);
       }
     } catch (error) {
      console.error('获取MCP服务器列表失败:', error);
      message.error('获取服务器列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取服务器工具列表
  const fetchServerTools = async (serverId) => {
    try {
      const tools = await apiService.project.ai.mcpServers.getTools(currentProject.id, serverId);
      setServerTools(Array.isArray(tools) ? tools : []);
    } catch (error) {
      console.error('获取服务器工具失败:', error);
      message.error('获取服务器工具失败');
      setServerTools([]);
    }
  };

  // 显示服务器详情
  const showServerDetail = (server) => {
    setSelectedServer(server);
    fetchServerTools(server.id);
    setDetailDrawerVisible(true);
  };

  // 添加MCP服务器
  const handleCreateServer = async (values) => {
    try {
      // 构建MCP服务器配置
      const serverConfig = {
        name: values.name,
        description: values.description,
        transport_type: values.transport_type,
        deployment_mode: values.deployment_mode,
        enabled: true,
        config: {}
      };

      // 根据传输协议设置配置
      if (values.transport_type === 'sse') {
        serverConfig.config = {
          url: values.sse_url,
          headers: values.headers ? JSON.parse(values.headers) : {}
        };
      } else if (values.transport_type === 'streamhttp') {
        serverConfig.config = {
          url: values.http_url,
          method: values.http_method || 'POST',
          headers: values.headers ? JSON.parse(values.headers) : {}
        };
      } else if (values.transport_type === 'stdio') {
        serverConfig.config = {
          command: values.command,
          args: values.args ? values.args.split(' ') : [],
          env: values.env ? JSON.parse(values.env) : {}
        };
      }

      const result = await apiService.project.ai.mcpServers.create(currentProject.id, serverConfig);
      
      message.success('MCP服务器添加成功');
      setCreateModalVisible(false);
      createForm.resetFields();
      
      // 自动测试连接和初始化
      const serverId = result.data?.id || result.id;
      if (serverId) {
        message.info('正在测试连接和初始化服务器...');
        
        // 先测试连接
        try {
          await handleTestConnection(serverId);
          // 连接成功后进行初始化
          setTimeout(() => {
            handleInitializeServer(serverId);
          }, 1000);
        } catch (error) {
          console.warn('自动测试连接失败:', error);
        }
      }
      
      fetchServers(); // 刷新列表
    } catch (error) {
      console.error('添加MCP服务器失败:', error);
      message.error('添加MCP服务器失败');
    }
  };

  // 显示创建服务器模态框
  const showCreateModal = () => {
    setCreateModalVisible(true);
    createForm.resetFields();
  };

  // 处理JSON导入
  const handleJsonImport = async (values) => {
    try {
      const jsonConfig = JSON.parse(values.json_config);
      
      // 验证JSON格式
      if (!jsonConfig.mcpServers && !jsonConfig.servers) {
        message.error('无效的MCP配置格式，请确保包含 mcpServers 或 servers 字段');
        return;
      }
      
      const servers = jsonConfig.mcpServers || jsonConfig.servers || {};
      let successCount = 0;
      let errorCount = 0;
      
      // 批量导入服务器
      for (const [serverName, serverConfig] of Object.entries(servers)) {
        try {
          // 转换配置格式
          const mcpServerConfig = {
            name: serverName,
            description: serverConfig.description || `从JSON导入的${serverName}服务器`,
            deployment_mode: 'external',
            enabled: true,
            config: {}
          };
          
          // 根据配置类型设置传输协议和配置
          if (serverConfig.command) {
            // STDIO协议
            mcpServerConfig.transport_type = 'stdio';
            mcpServerConfig.config = {
              command: serverConfig.command,
              args: serverConfig.args || [],
              env: serverConfig.env || {}
            };
          } else if (serverConfig.url) {
            // HTTP/SSE协议
            mcpServerConfig.transport_type = serverConfig.type === 'sse' ? 'sse' : 'streamhttp';
            mcpServerConfig.config = {
              url: serverConfig.url,
              headers: serverConfig.headers || {}
            };
          } else {
            console.warn(`跳过服务器 ${serverName}：缺少必要的配置信息`);
            errorCount++;
            continue;
          }
          
          // 调用API创建服务器
          const response = await fetch(`/api/v1/project/${currentProject.id}/ai/mcp-servers`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${localStorage.getItem('token')}`
            },
            body: JSON.stringify(mcpServerConfig)
          });
          
          const result = await response.json();
          
          if (result.success) {
            successCount++;
          } else {
            console.error(`导入服务器 ${serverName} 失败:`, result.message);
            errorCount++;
          }
        } catch (error) {
          console.error(`导入服务器 ${serverName} 失败:`, error);
          errorCount++;
        }
      }
      
      // 显示导入结果
      if (successCount > 0) {
        message.success(`成功导入 ${successCount} 个MCP服务器`);
      }
      if (errorCount > 0) {
        message.warning(`${errorCount} 个服务器导入失败，请检查配置格式`);
      }
      
      setJsonImportModalVisible(false);
      jsonImportForm.resetFields();
      fetchServers(); // 刷新列表
    } catch (error) {
      console.error('JSON导入失败:', error);
      message.error('JSON格式错误，请检查配置文件格式');
    }
  };

  // 测试连接
  const handleTestConnection = async (serverId) => {
    try {
      const result = await apiService.project.ai.mcpServers.test(currentProject.id, serverId);
      
      if (result.success !== false) {
        message.success('连接测试成功');
        fetchServers(); // 刷新状态
      } else {
        message.error(`连接测试失败: ${result.message}`);
      }
    } catch (error) {
      console.error('连接测试失败:', error);
      message.error('连接测试失败');
    }
  };

  // 初始化服务器（获取工具列表、prompt和资源）
  const handleInitializeServer = async (serverId) => {
    try {
      const result = await apiService.project.ai.mcpServers.initialize(currentProject.id, serverId);
      
      if (result.success !== false) {
        message.success('服务器初始化成功，已获取工具列表和资源');
        fetchServers(); // 刷新列表
      } else {
        message.error(`初始化失败: ${result.message}`);
      }
    } catch (error) {
      console.error('初始化服务器失败:', error);
      message.error('初始化服务器失败');
    }
  };



  useEffect(() => {
    if (currentProject?.id) {
      fetchServers();
    }
  }, [currentProject]);

  // 渲染状态标签
  const renderStatusTag = (status) => {
    const statusConfig = {
      running: { color: 'green', text: '运行中' },
      stopped: { color: 'red', text: '已停止' },
      unknown: { color: 'gray', text: '未知' }
    };
    
    const config = statusConfig[status] || statusConfig.unknown;
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 渲染协议类型标签
  const renderProtocolTag = (serverType, index = 0) => {
    const protocolConfig = {
      sse: { color: 'blue', text: 'SSE' },
      streamhttp: { color: 'green', text: 'StreamHTTP' },
      stdio: { color: 'orange', text: 'STDIO' },
      websocket: { color: 'purple', text: 'WebSocket' }
    };
    
    const config = protocolConfig[serverType] || { color: 'default', text: serverType };
    return <Tag key={`${serverType}-${index}`} color={config.color}>{config.text}</Tag>;
  };

  // 渲染认证类型标签
  const renderAuthTag = (authType) => {
    const authConfig = {
      none: { color: 'gray', text: '无认证' },
      api_key: { color: 'blue', text: 'API Key' },
      bearer: { color: 'green', text: 'Bearer Token' },
      oauth: { color: 'purple', text: 'OAuth' }
    };
    
    const config = authConfig[authType] || { color: 'default', text: authType };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // 表格列定义
  const columns = [
    {
      title: '服务器名称',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <Space>
          <CloudServerOutlined />
          <span>{text}</span>
          {record.source === 'external' && <Tag color="green">外部</Tag>}
          {record.source === 'internal' && <Tag color="blue">内部</Tag>}
          {record.deployment_mode === 'docker' && <Tag color="cyan">Docker</Tag>}
          {record.deployment_mode === 'npm' && <Tag color="orange">NPM</Tag>}
          {record.simulation && <Tag color="purple">仿真</Tag>}
        </Space>
      )
    },
    {
      title: '协议类型',
      dataIndex: 'transport_types',
      key: 'transport_types',
      render: (transport_types, record) => {
        // 优先使用transport_types数组，如果没有则使用transport_type
        const types = transport_types || (record.transport_type ? [record.transport_type] : []);
        if (!types || types.length === 0) return '-';
        return types.map((type, index) => renderProtocolTag(type, index));
      }
    },
    {
      title: '部署模式',
      dataIndex: 'deployment_mode',
      key: 'deployment_mode',
      render: (mode) => {
        const modeConfig = {
          local: { color: 'blue', text: '本地' },
          docker: { color: 'green', text: 'Docker' },
          npm: { color: 'orange', text: 'NPM' }
        };
        const config = modeConfig[mode] || { color: 'default', text: mode };
        return <Tag color={config.color}>{config.text}</Tag>;
      }
    },
    {
      title: '连接状态',
      dataIndex: 'status',
      key: 'status',
      render: renderStatusTag
    },
    {
      title: '可用工具',
      dataIndex: 'tools_count',
      key: 'tools_count',
      render: (count) => (
        <Badge count={count} showZero color="#52c41a" />
      )
    },
    {
      title: '状态',
      dataIndex: 'enabled',
      key: 'enabled',
      render: (enabled) => (
        <Tag color={enabled ? 'green' : 'red'}>
          {enabled ? '启用' : '禁用'}
        </Tag>
      )
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (time) => time ? new Date(time).toLocaleString() : '-'
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => showServerDetail(record)}
            />
          </Tooltip>
          <Tooltip title="测试连接">
            <Button
              type="text"
              icon={<ApiOutlined />}
              onClick={() => handleTestConnection(record.id)}
            />
          </Tooltip>
          <Tooltip title="初始化服务器">
            <Button
              type="text"
              icon={<SettingOutlined />}
              onClick={() => handleInitializeServer(record.id)}
            />
          </Tooltip>
        </Space>
      )
    }
  ];

  return (
    <div className="ant-card ant-card-bordered settings-card css-dev-only-do-not-override-vrrzze" style={{ padding: '24px' }}>
      {/* 页面头部 */}
      <Row justify="space-between" align="middle" style={{ marginBottom: '24px' }}>
        <Col>
          <Space>
            <Button
              icon={<ArrowLeftOutlined />}
              onClick={() => navigate('/project/ai/assistants')}
            >
              返回AI助手
            </Button>
            <Title level={2} style={{ margin: 0 }}>
              <CloudServerOutlined /> MCP服务器管理
            </Title>
          </Space>
        </Col>
        <Col>
          <Space>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={showCreateModal}
            >
              添加MCP服务器
            </Button>
            <Button
              icon={<UploadOutlined />}
              onClick={() => setJsonImportModalVisible(true)}
            >
              从JSON导入
            </Button>
          </Space>
        </Col>
      </Row>

      {/* 说明信息 */}
      <Alert
        message="MCP服务器管理"
        description={
          <div>
            <p>管理Model Context Protocol (MCP) 服务器，支持sse、stdio、streamhttp等协议类型。</p>
            <p><strong>操作说明：</strong></p>
            <ul style={{ marginBottom: 0, paddingLeft: '20px' }}>
              <li><strong>测试连接：</strong>验证服务器是否可访问</li>
              <li><strong>初始化：</strong>自动获取工具列表、prompt和资源</li>
              <li><strong>启动/停止：</strong>仅适用于内部部署的服务器（本地、Docker、NPM）</li>
              <li><strong>外部服务器：</strong>只能测试连接和初始化，无法控制启停</li>
            </ul>
          </div>
        }
        type="info"
        showIcon
        style={{ marginBottom: '24px' }}
      />

      {/* 服务器列表 */}
      <Card>
        <Table
          columns={columns}
          dataSource={servers}
          rowKey="id"
          loading={loading}
          pagination={{
            total: servers.length,
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 个服务器`
          }}
        />
      </Card>

      {/* 服务器详情抽屉 */}
      <Drawer
        title={
          <Space>
            <CloudServerOutlined />
            服务器详情
          </Space>
        }
        width={800}
        open={detailDrawerVisible}
        onClose={() => setDetailDrawerVisible(false)}
      >
        {selectedServer && (
          <div>
            <Tabs defaultActiveKey="info">
              <TabPane tab="基本信息" key="info">
                <Row gutter={[16, 16]}>
                  <Col span={12}>
                    <Statistic title="服务器名称" value={selectedServer.name} />
                  </Col>
                  <Col span={12}>
                    <Statistic 
                      title="协议类型" 
                      value={selectedServer.transport_types ? selectedServer.transport_types.join(', ') : '-'} 
                    />
                  </Col>
                  <Col span={12}>
                    <Statistic title="部署模式" value={selectedServer.deployment_mode || '-'} />
                  </Col>
                  <Col span={12}>
                    <Statistic title="工具数量" value={selectedServer.tools_count} />
                  </Col>
                  <Col span={24}>
                    <Text strong>描述:</Text>
                    <br />
                    <Text>{selectedServer.description || '暂无描述'}</Text>
                  </Col>
                  <Col span={24}>
                    <Text strong>安装目录:</Text>
                    <br />
                    <Text code>{selectedServer.install_dir || '-'}</Text>
                  </Col>
                  <Col span={24}>
                    <Text strong>配置信息:</Text>
                    <br />
                    <Text>仿真模式: {selectedServer.simulation ? '是' : '否'}</Text>
                    <br />
                    <Text>使用系统AI: {selectedServer.use_system_ai ? '是' : '否'}</Text>
                    <br />
                    <Text>支持媒体: {selectedServer.supports_media ? '是' : '否'}</Text>
                  </Col>
                </Row>
              </TabPane>
              <TabPane tab="可用工具" key="tools">
                <Table
                  columns={[
                    {
                      title: '工具名称',
                      dataIndex: 'name',
                      key: 'name'
                    },
                    {
                      title: '描述',
                      dataIndex: 'description',
                      key: 'description'
                    },
                    {
                      title: '输入参数',
                      dataIndex: 'inputSchema',
                      key: 'inputSchema',
                      render: (schema) => {
                        if (!schema || !schema.properties) return '无参数';
                        const paramCount = Object.keys(schema.properties).length;
                        return `${paramCount} 个参数`;
                      }
                    },
                    {
                      title: '支持流式',
                      dataIndex: 'metadata',
                      key: 'supports_streaming',
                      render: (metadata) => {
                        const supports = metadata?.supports_streaming;
                        return (
                          <Tag color={supports ? 'green' : 'gray'}>
                            {supports ? '支持' : '不支持'}
                          </Tag>
                        );
                      }
                    }
                  ]}
                  dataSource={serverTools}
                  rowKey="name"
                  pagination={false}
                  size="small"
                />
              </TabPane>
            </Tabs>
          </div>
        )}
      </Drawer>

      {/* 添加外部MCP服务器模态框 */}
      <Modal
        title={
          <Space>
            <CloudServerOutlined />
            添加MCP服务器
          </Space>
        }
        open={createModalVisible}
        onCancel={() => setCreateModalVisible(false)}
        onOk={() => createForm.submit()}
        width={700}
        destroyOnHidden
      >
        <Alert
          message="添加MCP服务器"
          description={
            <div>
              <p>添加Model Context Protocol服务器，支持SSE、StreamHTTP、STDIO等协议。</p>
              <p><strong>注意：</strong>外部服务器添加后将自动测试连接并初始化获取工具列表。内部服务器可以控制启停状态。</p>
            </div>
          }
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />
        
        <Form
          form={createForm}
          layout="vertical"
          onFinish={handleCreateServer}
        >
          <Form.Item
            name="name"
            label="服务器名称"
            rules={[{ required: true, message: '请输入服务器名称' }]}
          >
            <Input placeholder="例如：Weather API Server" />
          </Form.Item>

          <Form.Item
            name="description"
            label="服务器描述"
          >
            <TextArea rows={2} placeholder="描述此MCP服务器的功能和用途" />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="transport_type"
                label="传输协议"
                rules={[{ required: true, message: '请选择传输协议' }]}
                initialValue="streamhttp"
              >
                <Select
                  placeholder="请选择传输协议"
                  onChange={(value) => {
                    // 根据协议类型清空相关字段
                    createForm.setFieldsValue({
                      sse_url: undefined,
                      http_url: undefined,
                      command: undefined,
                      args: undefined
                    });
                  }}
                >
                  <Option value="sse">SSE (Server-Sent Events)</Option>
                  <Option value="streamhttp">StreamHTTP</Option>
                  <Option value="stdio">STDIO (本地进程)</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="deployment_mode"
                label="部署模式"
                rules={[{ required: true, message: '请选择部署模式' }]}
                initialValue="external"
              >
                <Select placeholder="请选择部署模式">
                  <Option value="external">外部服务</Option>
                  <Option value="local">本地部署</Option>
                  <Option value="docker">Docker容器</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          {/* SSE协议配置 */}
          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) =>
              prevValues.transport_type !== currentValues.transport_type
            }
          >
            {({ getFieldValue }) => {
              const transportType = getFieldValue('transport_type');
              
              if (transportType === 'sse') {
                return (
                  <>
                    <Form.Item
                      name="sse_url"
                      label="SSE服务器URL"
                      rules={[{ required: true, message: '请输入SSE服务器URL' }]}
                    >
                      <Input placeholder="https://your-mcp-server.com/sse" />
                    </Form.Item>
                  </>
                );
              }
              
              if (transportType === 'streamhttp') {
                return (
                  <>
                    <Row gutter={16}>
                      <Col span={18}>
                        <Form.Item
                          name="http_url"
                          label="HTTP服务器URL"
                          rules={[{ required: true, message: '请输入HTTP服务器URL' }]}
                        >
                          <Input placeholder="https://your-mcp-server.com/mcp" />
                        </Form.Item>
                      </Col>
                      <Col span={6}>
                        <Form.Item
                          name="http_method"
                          label="HTTP方法"
                          initialValue="POST"
                        >
                          <Select>
                            <Option value="POST">POST</Option>
                            <Option value="GET">GET</Option>
                          </Select>
                        </Form.Item>
                      </Col>
                    </Row>
                  </>
                );
              }
              
              if (transportType === 'stdio') {
                return (
                  <>
                    <Form.Item
                      name="command"
                      label="启动命令"
                      rules={[{ required: true, message: '请输入启动命令' }]}
                    >
                      <Input placeholder="例如：node server.js 或 python mcp_server.py" />
                    </Form.Item>
                    <Form.Item
                      name="args"
                      label="命令参数"
                    >
                      <Input placeholder="例如：--port 8080 --config config.json" />
                    </Form.Item>
                  </>
                );
              }
              
              return null;
            }}
          </Form.Item>

          <Form.Item
            name="headers"
            label="HTTP请求头"
            help="JSON格式，用于认证和其他配置"
          >
            <TextArea
              rows={3}
              placeholder='{"Authorization": "Bearer your-token", "Content-Type": "application/json"}'
            />
          </Form.Item>

          <Form.Item
            name="env"
            label="环境变量"
            help="JSON格式，仅对STDIO协议有效"
          >
            <TextArea
              rows={3}
              placeholder='{"API_KEY": "your-api-key", "PORT": "8000"}'
            />
          </Form.Item>
        </Form>
      </Modal>

      {/* JSON导入模态框 */}
      <Modal
        title={
          <Space>
            <UploadOutlined />
            从JSON导入MCP服务器
          </Space>
        }
        open={jsonImportModalVisible}
        onCancel={() => setJsonImportModalVisible(false)}
        onOk={() => jsonImportForm.submit()}
        width={800}
        destroyOnHidden
      >
        <Alert
          message="支持的JSON格式"
          description={
            <div>
              <p>支持以下MCP配置格式：</p>
              <ul style={{ marginBottom: 0, paddingLeft: '20px' }}>
                <li><strong>Claude Desktop格式：</strong>包含 mcpServers 字段</li>
                <li><strong>VS Code格式：</strong>包含 servers 字段</li>
                <li><strong>标准MCP格式：</strong>支持 stdio、sse、http 传输协议</li>
              </ul>
            </div>
          }
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />
        
        <Form
          form={jsonImportForm}
          layout="vertical"
          onFinish={handleJsonImport}
        >
          <Form.Item
            name="json_config"
            label="MCP配置JSON"
            rules={[{ required: true, message: '请输入MCP配置JSON' }]}
          >
            <TextArea
              rows={15}
              placeholder={`请粘贴MCP配置JSON，例如：

Claude Desktop格式：
{
  "mcpServers": {
    "weather-api": {
      "command": "npx",
      "args": ["-y", "weather-mcp-server"],
      "env": {
        "API_KEY": "your-api-key"
      }
    },
    "remote-service": {
      "type": "sse",
      "url": "https://api.example.com/sse",
      "headers": {
        "Authorization": "Bearer token"
      }
    }
  }
}

VS Code格式：
{
  "servers": {
    "file-system": {
      "command": "python",
      "args": ["server.py"],
      "env": {"PATH": "/usr/bin"}
    }
  }
}`}
              style={{ fontFamily: 'monospace' }}
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default MCPServerManagement; 