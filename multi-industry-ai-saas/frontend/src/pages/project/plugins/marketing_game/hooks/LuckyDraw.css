/* 抽奖页面样式 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-20px); }
  60% { transform: translateY(-10px); }
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.lucky-draw-container {
  min-height: 100vh;
  height: 100vh;
  padding: 20px;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  position: relative;
  animation: fadeIn 0.8s ease-in-out;
  overflow: hidden;
}

.lucky-draw-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.85);
  z-index: 0;
}

.lucky-draw-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
}

.loading-icon {
  font-size: 48px;
  margin-bottom: 20px;
  color: #ff5722;
}

.loading-text {
  font-size: 18px;
  color: #333;
  margin-top: 15px;
}

.lucky-draw-header {
  text-align: center;
  margin-bottom: 20px;
  position: relative;
  z-index: 1;
  width: 100%;
  max-width: 1000px;
}

.game-logo {
  width: 80px;
  height: 80px;
  margin-bottom: 15px;
  border-radius: 50%;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  object-fit: cover;
  background-color: white;
  padding: 3px;
}

.game-title {
  color: #ff5722;
  margin-bottom: 10px;
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
  font-size: 28px;
  font-weight: bold;
}

.game-description {
  max-width: 800px;
  margin: 0 auto;
  color: #333;
  font-size: 16px;
  line-height: 1.5;
  font-weight: bold;
  text-shadow: 0px 0px 3px rgba(255, 255, 255, 0.8);
}

.lucky-draw-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 1;
  width: 100%;
  max-width: 1000px;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 15px;
  padding: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  flex: 1;
  justify-content: center;
}

.settings-bar {
  display: flex;
  justify-content: flex-end;
  width: 100%;
  margin-bottom: 10px;
}

.settings-item {
  display: flex;
  align-items: center;
  margin-left: 20px;
  color: #666;
}

.settings-label {
  margin-right: 8px;
  font-size: 14px;
}

/* 转盘样式 */
.wheel-container {
  position: relative;
  width: 320px;
  height: 320px;
  margin: 0 auto 30px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.wheel {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  position: relative;
  overflow: hidden;
  border: 8px solid #ff5722;
  box-shadow: 0 0 0 8px #ffeb3b, 0 0 20px rgba(0, 0, 0, 0.2);
  transition: transform 4s cubic-bezier(0.17, 0.67, 0.83, 0.67);
  animation: pulse 3s infinite ease-in-out;
  background-color: #fff;
}

/* 增加分割线 */
.wheel::before {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background:
    repeating-conic-gradient(
      transparent 0deg,
      transparent calc(360deg / var(--prize-count, 8) - 2deg),
      rgba(0, 0, 0, 0.2) calc(360deg / var(--prize-count, 8) - 2deg),
      rgba(0, 0, 0, 0.2) calc(360deg / var(--prize-count, 8))
    );
  z-index: 3;
  pointer-events: none;
}

.wheel-pointer {
  position: absolute;
  top: -25px;
  left: 50%;
  transform: translateX(-50%);
  width: 30px;
  height: 30px;
  background-color: #ff5722;
  clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
  z-index: 5;
  filter: drop-shadow(0 0 5px rgba(0, 0, 0, 0.3));
  animation: bounce 2s infinite ease-in-out;
}

/* 转盘中心装饰 */
.wheel::after {
  content: '';
  position: absolute;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #fff;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border: 4px solid #ff5722;
  box-shadow: 0 0 0 4px #ffeb3b, 0 0 10px rgba(0, 0, 0, 0.2);
  z-index: 5;
}

.wheel-item {
  position: absolute;
  top: 0;
  left: 0;
  width: 50%;
  height: 50%;
  transform-origin: bottom right;
  overflow: hidden;
  text-align: center;
}

.wheel-item-content {
  position: absolute;
  left: -100%;
  width: 200%;
  height: 200%;
  transform-origin: 100% 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: 0;
}

.prize-image {
  width: 30px;
  height: 30px;
  object-fit: contain;
  margin-bottom: 5px;
  position: relative;
  z-index: 1;
  margin-top: -20px;
  filter: drop-shadow(0 0 3px rgba(255, 255, 255, 0.8));
}

/* 确保"谢谢参与"文字在各种背景上都清晰可见 */
.prize-name {
  font-size: 14px;
  font-weight: bold;
  color: #333;
  text-shadow: 0 0 3px white, 0 0 6px white;
  max-width: 100px;
  padding: 4px 6px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  line-height: 1.2;
  margin-top: 3px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

/* 按钮样式 */
.lucky-draw-actions {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 20px;
  width: 100%;
}

.wheel-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
  width: 60px;
  height: 60px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: white;
  border-radius: 50%;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
}

.draw-button {
  background-color: #ff5722;
  color: white;
  border: none;
  font-weight: bold;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.2);
  height: 50px !important;
  width: 50px !important;
  border-radius: 50% !important;
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  padding: 0 !important;
  font-size: 14px !important;
  line-height: 1.2 !important;
  transition: all 0.3s ease;
  position: relative;
  z-index: 10;
}

.draw-button:hover, .draw-button:focus {
  background-color: #e64a19;
  border-color: #e64a19;
  transform: translateY(-5px) scale(1.05);
  box-shadow: 0 12px 24px rgba(255, 87, 34, 0.5);
}

.remaining-draws {
  text-align: center;
  margin: 10px 0;
  font-size: 16px;
  color: #333;
  font-weight: bold;
}

.remaining-count {
  color: #ff5722;
  font-size: 18px;
  margin: 0 5px;
}

.action-buttons {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
  justify-content: center;
  margin-top: 10px;
}

.action-button {
  min-width: 110px;
  height: 40px;
  border-radius: 20px;
  transition: all 0.3s ease;
}

.action-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
}

.prize-result {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 20px;
  background-color: #fff9f0;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.prize-result-image {
  width: 80px;
  height: 80px;
  object-fit: contain;
  border-radius: 8px;
  border: 1px solid #FFD8A8;
}

.prize-result-info {
  flex: 1;
}

.prize-result-title {
  margin-bottom: 5px !important;
  color: #ff5722;
}

.prize-result-description {
  color: #666;
  margin-bottom: 0 !important;
}

.lucky-draw-footer {
  position: relative;
  z-index: 1;
  width: 100%;
  max-width: 1000px;
  text-align: center;
  padding: 15px 0;
  margin-top: 10px;
  color: #666;
  font-size: 14px;
}

.footer-links {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 10px;
}

.footer-link {
  color: #666;
  text-decoration: none;
}

.footer-link:hover {
  color: #ff5722;
  text-decoration: underline;
}

/* 响应式样式 */
@media (max-width: 1200px) {
  .lucky-draw-content {
    max-width: 800px;
  }
}

@media (max-width: 992px) {
  .lucky-draw-content {
    max-width: 700px;
    padding: 25px;
  }

  .wheel-container {
    width: 350px;
    height: 350px;
  }

  .game-title {
    font-size: 28px;
  }
}

@media (max-width: 768px) {
  .lucky-draw-container {
    padding: 20px;
  }

  .lucky-draw-content {
    max-width: 100%;
    padding: 20px;
  }

  .wheel-container {
    width: 300px;
    height: 300px;
  }

  .draw-button {
    width: 250px;
    height: 50px;
    font-size: 18px;
  }

  .game-title {
    font-size: 24px;
  }

  .game-description {
    font-size: 14px;
  }

  .prize-image {
    width: 30px;
    height: 30px;
  }

  .prize-name {
    font-size: 12px;
  }
}

@media (max-width: 576px) {
  .lucky-draw-container {
    padding: 15px;
  }

  .lucky-draw-content {
    padding: 15px;
  }

  .wheel-container {
    width: 250px;
    height: 250px;
    margin-bottom: 30px;
  }

  .wheel {
    border-width: 8px;
    box-shadow: 0 0 0 8px #ffeb3b, 0 0 20px rgba(0, 0, 0, 0.3);
  }

  .wheel-pointer {
    top: -25px;
    border-left-width: 20px;
    border-right-width: 20px;
    border-top-width: 40px;
  }

  .draw-button {
    width: 200px;
    height: 45px;
    font-size: 16px;
    margin-bottom: 20px;
  }

  .action-buttons {
    flex-direction: column;
    gap: 10px;
  }

  .action-button {
    width: 100%;
  }

  .game-logo {
    width: 80px;
    height: 80px;
  }

  .game-title {
    font-size: 20px;
  }

  .prize-result {
    flex-direction: column;
    text-align: center;
  }

  .prize-result-image {
    margin-right: 0;
    margin-bottom: 15px;
  }

  .settings-bar {
    flex-direction: column;
    align-items: flex-end;
  }

  .settings-item {
    margin-left: 0;
    margin-bottom: 10px;
  }
}
