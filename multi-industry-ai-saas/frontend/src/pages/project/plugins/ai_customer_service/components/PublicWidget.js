import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  Space,
  Form,
  Input,
  Select,
  Switch,
  ColorPicker,
  Typography,
  Alert,
  Divider,
  Row,
  Col,
  Modal,
  message,
  Tabs,
  Table,
  Tag,
  Statistic
} from 'antd';
import {
  ShareAltOutlined,
  CopyOutlined,
  EyeOutlined,
  SettingOutlined,
  CodeOutlined,
  Bar<PERSON>hartOutlined,
  GlobalOutlined,
  MobileOutlined,
  DesktopOutlined,
  TabletOutlined,
  MessageOutlined,
  ClockCircleOutlined
} from '@ant-design/icons';
import ChatTestComponent from './ChatTestComponent';

const { TextArea } = Input;
const { Option } = Select;
const { Title, Paragraph, Text } = Typography;
const { TabPane } = Tabs;

// 分析统计Tab组件
const AnalyticsTab = () => {
  const [analyticsData, setAnalyticsData] = useState({
    overview: {
      total_visits: 0,
      total_sessions: 0,
      conversion_rate: 0,
      avg_session_duration: 0
    },
    device_distribution: [],
    traffic_sources: [],
    user_behavior: [],
    time_trends: []
  });
  const [loading, setLoading] = useState(false);

  // 获取项目ID
  const getProjectId = () => {
    const projectId = localStorage.getItem('project_id');
    if (projectId && projectId !== 'null' && projectId !== 'undefined') {
      return projectId;
    }

    const userData = JSON.parse(localStorage.getItem('user_data') || '{}');
    if (userData && userData.project_id) {
      localStorage.setItem('project_id', userData.project_id);
      return userData.project_id;
    }

    return null;
  };

  // 获取分析数据
  const fetchAnalyticsData = async () => {
    try {
      setLoading(true);
      const projectId = getProjectId();
      if (!projectId) return;

      const response = await fetch(`/api/v1/project/${projectId}/plugin/ai-customer-service/analytics/widget`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setAnalyticsData(data);
      }
    } catch (error) {
      console.error('获取分析数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchAnalyticsData();
  }, []);

  const deviceColumns = [
    {
      title: '设备类型',
      dataIndex: 'device_type',
      key: 'device_type',
      render: (text) => {
        const deviceIcons = {
          'desktop': <DesktopOutlined />,
          'mobile': <MobileOutlined />,
          'tablet': <TabletOutlined />
        };
        const deviceNames = {
          'desktop': '桌面端',
          'mobile': '移动端',
          'tablet': '平板端'
        };
        return (
          <Space>
            {deviceIcons[text] || <DesktopOutlined />}
            {deviceNames[text] || text}
          </Space>
        );
      }
    },
    {
      title: '访问量',
      dataIndex: 'visits',
      key: 'visits',
      render: (value) => value.toLocaleString()
    },
    {
      title: '占比',
      dataIndex: 'percentage',
      key: 'percentage',
      render: (value) => `${value.toFixed(1)}%`
    }
  ];

  return (
    <div>
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总访问量"
              value={analyticsData.overview.total_visits}
              prefix={<GlobalOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="对话会话"
              value={analyticsData.overview.total_sessions}
              prefix={<MessageOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="转化率"
              value={analyticsData.overview.conversion_rate}
              suffix="%"
              precision={1}
              prefix={<BarChartOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="平均会话时长"
              value={analyticsData.overview.avg_session_duration}
              suffix="秒"
              prefix={<ClockCircleOutlined />}
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={12}>
          <Card title="设备分布" loading={loading}>
            <Table
              dataSource={analyticsData.device_distribution}
              columns={deviceColumns}
              rowKey="device_type"
              pagination={false}
              size="small"
            />
          </Card>
        </Col>
        <Col span={12}>
          <Card title="流量来源" loading={loading}>
            <Table
              dataSource={analyticsData.traffic_sources}
              columns={[
                {
                  title: '来源',
                  dataIndex: 'source',
                  key: 'source'
                },
                {
                  title: '访问量',
                  dataIndex: 'visits',
                  key: 'visits'
                },
                {
                  title: '占比',
                  dataIndex: 'percentage',
                  key: 'percentage',
                  render: (value) => `${value.toFixed(1)}%`
                }
              ]}
              rowKey="source"
              pagination={false}
              size="small"
            />
          </Card>
        </Col>
      </Row>
    </div>
  );
};

const PublicWidget = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [previewVisible, setPreviewVisible] = useState(false);
  const [embedCodeVisible, setEmbedCodeVisible] = useState(false);
  const [widgetConfig, setWidgetConfig] = useState({
    title: 'AI智能客服',
    welcome_message: '您好！我是AI智能客服，有什么可以帮助您的吗？',
    theme_color: '#1890ff',
    position: 'bottom-right',
    size: 'medium',
    auto_open: false,
    show_avatar: true,
    enable_file_upload: false,
    enable_voice_input: false
  });
  const [widgetStats, setWidgetStats] = useState({
    total_sessions: 0,
    total_messages: 0,
    avg_session_duration: 0,
    user_satisfaction: 0
  });
  const [previewDevice, setPreviewDevice] = useState('desktop');

  // 生成嵌入代码
  const generateEmbedCode = () => {
    const widgetId = 'widget_' + Date.now();
    const baseUrl = window.location.origin;
    
    return `<!-- AI智能客服组件 -->
<script>
(function() {
    var config = {
        widgetId: '${widgetId}',
        theme: 'light',
        size: '${widgetConfig.size}',
        position: '${widgetConfig.position}',
        apiBaseUrl: '${baseUrl}'
    };
    
    var script = document.createElement('script');
    script.src = '${baseUrl}/static/ai-customer-service/widget.js';
    script.onload = function() {
        if (window.AICustomerService) {
            window.AICustomerService.init(config);
        }
    };
    document.head.appendChild(script);
    
    var link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = '${baseUrl}/static/ai-customer-service/widget.css';
    document.head.appendChild(link);
})();
</script>`;
  };

  // 复制嵌入代码
  const copyEmbedCode = () => {
    const code = generateEmbedCode();
    navigator.clipboard.writeText(code).then(() => {
      message.success('嵌入代码已复制到剪贴板');
    }).catch(() => {
      message.error('复制失败，请手动复制');
    });
  };

  // 保存配置
  const handleSaveConfig = async (values) => {
    try {
      setLoading(true);

      const projectId = getProjectId();
      if (!projectId) {
        message.error('无法获取项目ID');
        setLoading(false);
        return;
      }

      const response = await fetch(`/api/v1/project/${projectId}/plugin/ai-customer-service/public-widget/config`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(values)
      });

      if (response.ok) {
        setWidgetConfig({ ...widgetConfig, ...values });
        message.success('配置保存成功');
      } else {
        message.error('保存配置失败');
      }
    } catch (error) {
      console.error('保存配置失败:', error);
      message.error('保存配置失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取项目ID
  const getProjectId = () => {
    const projectId = localStorage.getItem('project_id');
    if (projectId && projectId !== 'null' && projectId !== 'undefined') {
      return projectId;
    }

    const userData = JSON.parse(localStorage.getItem('user_data') || '{}');
    if (userData && userData.project_id) {
      localStorage.setItem('project_id', userData.project_id);
      return userData.project_id;
    }

    return null;
  };

  // 获取统计数据
  const fetchStats = async () => {
    try {
      const projectId = getProjectId();
      if (!projectId) {
        console.error('无法获取项目ID');
        return;
      }

      const response = await fetch(`/api/v1/project/${projectId}/plugin/ai-customer-service/analytics/overview`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setWidgetStats({
          total_sessions: data.total_sessions || 0,
          total_messages: data.total_messages || 0,
          avg_session_duration: data.avg_session_duration || 0,
          user_satisfaction: data.avg_satisfaction || 0
        });
      } else {
        // 如果API调用失败，使用默认值
        setWidgetStats({
          total_sessions: 0,
          total_messages: 0,
          avg_session_duration: 0,
          user_satisfaction: 0
        });
      }
    } catch (error) {
      console.error('获取统计数据失败:', error);
      setWidgetStats({
        total_sessions: 0,
        total_messages: 0,
        avg_session_duration: 0,
        user_satisfaction: 0
      });
    }
  };

  // 加载配置
  const loadConfig = async () => {
    try {
      const projectId = getProjectId();
      if (!projectId) {
        console.error('无法获取项目ID');
        return;
      }

      const response = await fetch(`/api/v1/project/${projectId}/plugin/ai-customer-service/public-widget/config/default`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success && result.data) {
          // 更新配置状态
          setWidgetConfig(result.data);
          // 更新表单值
          form.setFieldsValue(result.data);
          console.log('配置加载成功:', result.data);
        }
      } else {
        console.log('使用默认配置');
        // 使用默认配置
        form.setFieldsValue(widgetConfig);
      }
    } catch (error) {
      console.error('加载配置失败:', error);
      // 使用默认配置
      form.setFieldsValue(widgetConfig);
    }
  };

  useEffect(() => {
    loadConfig();
    fetchStats();
  }, []);

  // 预览数据
  const previewData = [
    {
      key: '1',
      device: 'Desktop',
      resolution: '1920x1080',
      sessions: 45,
      icon: <DesktopOutlined />
    },
    {
      key: '2',
      device: 'Mobile',
      resolution: '375x667',
      sessions: 89,
      icon: <MobileOutlined />
    },
    {
      key: '3',
      device: 'Tablet',
      resolution: '768x1024',
      sessions: 22,
      icon: <TabletOutlined />
    }
  ];

  const previewColumns = [
    {
      title: '设备类型',
      key: 'device',
      render: (_, record) => (
        <Space>
          {record.icon}
          <span>{record.device}</span>
        </Space>
      )
    },
    {
      title: '分辨率',
      dataIndex: 'resolution',
      key: 'resolution'
    },
    {
      title: '会话数',
      dataIndex: 'sessions',
      key: 'sessions',
      render: (sessions) => <Tag color="blue">{sessions}</Tag>
    }
  ];

  return (
    <div>
      <Card title="公共客服组件">
        <Alert
          message="公共分享功能"
          description={
            <div>
              <Paragraph>
                生成可嵌入任何网站的客服组件，支持多种分辨率和自定义主题。
                组件会自动适配桌面、平板、手机等各种设备。
              </Paragraph>
              <ul>
                <li>响应式设计，自动适配各种屏幕尺寸</li>
                <li>支持自定义主题颜色、位置、大小</li>
                <li>无需登录即可使用，降低用户使用门槛</li>
                <li>提供详细的使用统计和分析数据</li>
              </ul>
            </div>
          }
          type="info"
          showIcon
          style={{ marginBottom: 24 }}
        />

        <Tabs 
          defaultActiveKey="1"
          activeKey={(() => {
            const urlParams = new URLSearchParams(window.location.search);
            const tab = urlParams.get('tab');
            if (tab === 'test') return "3";
            return "1";
          })()}
          onChange={(key) => {
            const url = new URL(window.location.href);
            if (key === "3") {
              url.searchParams.set('tab', 'test');
            } else {
              url.searchParams.delete('tab');
            }
            window.history.replaceState({}, '', url.toString());
          }}
        >
          <TabPane tab="组件配置" key="1">
            <Row gutter={24}>
              <Col xs={24} lg={12}>
                <Card title="基础配置" style={{ marginBottom: 16 }}>
                  <Form
                    form={form}
                    layout="vertical"
                    onFinish={handleSaveConfig}
                    initialValues={{
                      title: '智能客服',
                      description: '我是您的专属智能客服，随时为您提供帮助',
                      assistant_prompt: '你是一个专业、友好、耐心的智能客服助手。你的任务是：\n\n1. 为客户提供准确、有用的信息\n2. 以礼貌和专业的态度回应所有询问\n3. 当无法解决问题时，及时转接人工客服\n4. 始终保持积极正面的语调\n\n请记住：\n- 使用简洁明了的语言\n- 主动询问以更好地理解客户需求\n- 提供具体的解决方案\n- 对客户表示感谢和关心',
                      primary_color: '#1890ff',
                      position: 'bottom-right',
                      auto_greeting: true,
                      greeting_message: '您好！我是智能客服，有什么可以帮助您的吗？',
                      show_avatar: true,
                      show_typing_indicator: true,
                      enable_file_upload: false,
                      enable_voice_input: false
                    }}
                  >
                    <Row gutter={16}>
                      <Col span={12}>
                        <Form.Item
                          name="title"
                          label="标题"
                          rules={[{ required: true, message: '请输入标题' }]}
                        >
                          <Input placeholder="智能客服" />
                        </Form.Item>
                      </Col>
                      <Col span={12}>
                        <Form.Item
                          name="description"
                          label="副标题"
                        >
                          <Input placeholder="我是您的专属智能客服，随时为您提供帮助" />
                        </Form.Item>
                      </Col>
                    </Row>

                    <Form.Item
                      name="assistant_prompt"
                      label="AI助理人设描述"
                      extra="定义AI助理的角色、行为方式和回复风格，这将影响AI的所有回复"
                    >
                      <TextArea
                        rows={8}
                        placeholder="请描述AI助理的角色定位、服务范围、回复风格等..."
                        showCount
                        maxLength={1000}
                      />
                    </Form.Item>

                    <Form.Item
                      name="theme_color"
                      label="主题颜色"
                    >
                      <ColorPicker
                        value={widgetConfig.theme_color}
                        onChange={(color) => {
                          const newConfig = { ...widgetConfig, theme_color: color.toHexString() };
                          setWidgetConfig(newConfig);
                          form.setFieldValue('theme_color', color.toHexString());
                        }}
                      />
                    </Form.Item>

                    <Row gutter={16}>
                      <Col span={12}>
                        <Form.Item
                          name="position"
                          label="显示位置"
                        >
                          <Select>
                            <Option value="bottom-right">右下角</Option>
                            <Option value="bottom-left">左下角</Option>
                            <Option value="top-right">右上角</Option>
                            <Option value="top-left">左上角</Option>
                          </Select>
                        </Form.Item>
                      </Col>
                      <Col span={12}>
                        <Form.Item
                          name="size"
                          label="组件大小"
                        >
                          <Select>
                            <Option value="small">小</Option>
                            <Option value="medium">中</Option>
                            <Option value="large">大</Option>
                          </Select>
                        </Form.Item>
                      </Col>
                    </Row>

                    <Form.Item
                      name="auto_open"
                      label="自动打开"
                      valuePropName="checked"
                    >
                      <Switch />
                    </Form.Item>

                    <Form.Item
                      name="show_avatar"
                      label="显示头像"
                      valuePropName="checked"
                    >
                      <Switch />
                    </Form.Item>

                    <Form.Item
                      name="enable_file_upload"
                      label="启用文件上传"
                      valuePropName="checked"
                    >
                      <Switch />
                    </Form.Item>

                    <Form.Item
                      name="enable_voice_input"
                      label="启用语音输入"
                      valuePropName="checked"
                    >
                      <Switch />
                    </Form.Item>

                    <Form.Item
                      name="auto_greeting"
                      label="自动问候"
                      valuePropName="checked"
                      extra="页面加载时是否自动发送问候消息"
                    >
                      <Switch />
                    </Form.Item>

                    <Form.Item
                      name="greeting_message"
                      label="问候消息"
                      extra="设置AI客服的欢迎消息"
                    >
                      <TextArea
                        rows={2}
                        placeholder="您好！我是AI智能客服，有什么可以帮助您的吗？"
                        maxLength={200}
                        showCount
                      />
                    </Form.Item>

                    <Form.Item
                      name="offline_message"
                      label="离线消息"
                      extra="客服系统离线时显示的提示消息"
                    >
                      <TextArea
                        rows={2}
                        placeholder="客服暂时离线，请留言，我们会尽快回复您。"
                        maxLength={200}
                        showCount
                      />
                    </Form.Item>

                    <Form.Item
                      name="show_typing_indicator"
                      label="显示输入指示器"
                      valuePropName="checked"
                      extra="显示AI正在输入的动画效果"
                    >
                      <Switch />
                    </Form.Item>

                    <Form.Item>
                      <Space>
                        <Button type="primary" htmlType="submit" loading={loading}>
                          保存配置
                        </Button>
                        <Button onClick={() => setPreviewVisible(true)}>
                          <EyeOutlined /> 预览效果
                        </Button>
                      </Space>
                    </Form.Item>
                  </Form>
                </Card>
              </Col>

              <Col xs={24} lg={12}>
                <Card title="使用统计" size="small">
                  <Row gutter={16}>
                    <Col span={12}>
                      <Statistic
                        title="总会话数"
                        value={widgetStats.total_sessions}
                        prefix={<GlobalOutlined />}
                      />
                    </Col>
                    <Col span={12}>
                      <Statistic
                        title="总消息数"
                        value={widgetStats.total_messages}
                        prefix={<BarChartOutlined />}
                      />
                    </Col>
                    <Col span={12}>
                      <Statistic
                        title="平均会话时长"
                        value={widgetStats.avg_session_duration}
                        suffix="秒"
                      />
                    </Col>
                    <Col span={12}>
                      <Statistic
                        title="用户满意度"
                        value={widgetStats.user_satisfaction}
                        precision={1}
                        suffix="/ 5.0"
                      />
                    </Col>
                  </Row>

                  <Divider />

                  <div>
                    <Title level={5}>设备分布</Title>
                    <Table
                      columns={previewColumns}
                      dataSource={previewData}
                      pagination={false}
                      size="small"
                    />
                  </div>
                </Card>
              </Col>
            </Row>
          </TabPane>

          <TabPane tab="嵌入代码" key="2">
            <Card>
              <Space direction="vertical" style={{ width: '100%' }}>
                <div>
                  <Title level={4}>嵌入代码</Title>
                  <Paragraph>
                    将以下代码复制到您的网站页面中，即可集成AI客服组件。
                    代码会自动加载必要的样式和脚本文件。
                  </Paragraph>
                </div>

                <div style={{ position: 'relative' }}>
                  <TextArea
                    value={generateEmbedCode()}
                    rows={15}
                    readOnly
                    style={{ fontFamily: 'monospace', fontSize: 12 }}
                  />
                  <Button
                    type="primary"
                    icon={<CopyOutlined />}
                    style={{ position: 'absolute', top: 8, right: 8 }}
                    onClick={copyEmbedCode}
                  >
                    复制代码
                  </Button>
                </div>

                <Alert
                  message="使用说明"
                  description={
                    <ul>
                      <li>将代码粘贴到网页的 &lt;head&gt; 或 &lt;body&gt; 标签中</li>
                      <li>组件会自动在页面加载完成后初始化</li>
                      <li>支持在同一页面多次调用，但建议只使用一个实例</li>
                      <li>如需自定义样式，可以通过CSS覆盖默认样式</li>
                    </ul>
                  }
                  type="info"
                  showIcon
                />
              </Space>
            </Card>
          </TabPane>

          <TabPane tab="对话测试" key="3">
            <Card>
              <Title level={4}>超级全能客服测试</Title>
              <Paragraph>
                在这里可以直接测试AI客服的对话能力，验证配置是否正确。
              </Paragraph>

              <ChatTestComponent projectId={getProjectId()} />
            </Card>
          </TabPane>

          <TabPane tab="分析统计" key="4">
            <AnalyticsTab />
          </TabPane>
        </Tabs>
      </Card>

      {/* 预览弹窗 */}
      <Modal
        title="组件预览"
        visible={previewVisible}
        onCancel={() => setPreviewVisible(false)}
        footer={null}
        width={800}
      >
        <div style={{ textAlign: 'center', padding: 20 }}>
          <Alert
            message="预览功能"
            description="这里显示客服组件在不同设备上的预览效果。"
            type="info"
            showIcon
            style={{ marginBottom: 20 }}
          />
          
          {/* 设备预览选择 */}
          <div style={{ marginBottom: 20 }}>
            <Space>
              <Button 
                type={previewDevice === 'desktop' ? 'primary' : 'default'}
                icon={<DesktopOutlined />}
                onClick={() => setPreviewDevice('desktop')}
              >
                桌面端
              </Button>
              <Button 
                type={previewDevice === 'tablet' ? 'primary' : 'default'}
                icon={<TabletOutlined />}
                onClick={() => setPreviewDevice('tablet')}
              >
                平板端
              </Button>
              <Button 
                type={previewDevice === 'mobile' ? 'primary' : 'default'}
                icon={<MobileOutlined />}
                onClick={() => setPreviewDevice('mobile')}
              >
                移动端
              </Button>
            </Space>
          </div>
          
          {/* 预览容器 */}
          <div style={{ 
            border: '1px solid #d9d9d9', 
            borderRadius: 6, 
            padding: 20,
            background: '#f0f2f5',
            minHeight: 400,
            position: 'relative',
            overflow: 'hidden'
          }}>
            {/* 模拟网页背景 */}
            <div style={{
              background: 'white',
              padding: 20,
              borderRadius: 4,
              marginBottom: 20,
              boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
            }}>
              <div style={{ color: '#999', fontSize: 14 }}>
                这里是您的网页内容...
              </div>
            </div>
            
            {/* AI客服组件预览 */}
            <div style={{
              position: 'absolute',
              bottom: 20,
              right: 20,
              width: previewDevice === 'desktop' ? 350 : previewDevice === 'tablet' ? 300 : 280,
              height: previewDevice === 'desktop' ? 500 : previewDevice === 'tablet' ? 450 : 400,
              background: 'white',
              borderRadius: 8,
              boxShadow: '0 4px 20px rgba(0,0,0,0.15)',
              border: '1px solid #e8e8e8',
              display: 'flex',
              flexDirection: 'column',
              overflow: 'hidden'
            }}>
              {/* 客服组件头部 */}
              <div style={{
                background: widgetConfig.primary_color || '#1890ff',
                color: 'white',
                padding: 12,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between'
              }}>
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  {widgetConfig.show_avatar && (
                    <div style={{
                      width: 32,
                      height: 32,
                      borderRadius: '50%',
                      background: 'rgba(255,255,255,0.2)',
                      marginRight: 8,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center'
                    }}>
                      👤
                    </div>
                  )}
                  <div>
                    <div style={{ fontWeight: 'bold', fontSize: 14 }}>
                      {widgetConfig.title || 'AI智能客服'}
                    </div>
                    <div style={{ fontSize: 12, opacity: 0.8 }}>
                      在线为您服务
                    </div>
                  </div>
                </div>
                <Button 
                  type="text" 
                  size="small"
                  style={{ color: 'white' }}
                  onClick={() => setPreviewVisible(false)}
                >
                  ✕
                </Button>
              </div>
              
              {/* 聊天区域 */}
              <div style={{
                flex: 1,
                padding: 12,
                background: '#fafafa',
                overflow: 'auto'
              }}>
                {/* 示例消息 */}
                <div style={{ marginBottom: 12 }}>
                  <div style={{
                    background: 'white',
                    padding: 8,
                    borderRadius: 6,
                    maxWidth: '80%',
                    boxShadow: '0 1px 2px rgba(0,0,0,0.1)'
                  }}>
                    <div style={{ fontSize: 12, color: '#666', marginBottom: 4 }}>
                      AI客服
                    </div>
                    <div style={{ fontSize: 14 }}>
                      您好！我是AI智能客服，有什么可以帮助您的吗？
                    </div>
                  </div>
                </div>
                
                <div style={{ marginBottom: 12, textAlign: 'right' }}>
                  <div style={{
                    background: widgetConfig.primary_color || '#1890ff',
                    color: 'white',
                    padding: 8,
                    borderRadius: 6,
                    maxWidth: '80%',
                    marginLeft: 'auto',
                    display: 'inline-block'
                  }}>
                    <div style={{ fontSize: 14 }}>
                      我想了解你们的产品
                    </div>
                  </div>
                </div>
                
                <div style={{ marginBottom: 12 }}>
                  <div style={{
                    background: 'white',
                    padding: 8,
                    borderRadius: 6,
                    maxWidth: '80%',
                    boxShadow: '0 1px 2px rgba(0,0,0,0.1)'
                  }}>
                    <div style={{ fontSize: 12, color: '#666', marginBottom: 4 }}>
                      AI客服
                    </div>
                    <div style={{ fontSize: 14 }}>
                      当然可以！我们的产品包括多行业AI SaaS解决方案，支持零售、餐饮、教育等行业。您希望了解哪个方面呢？
                    </div>
                  </div>
                </div>
              </div>
              
              {/* 输入区域 */}
              <div style={{
                padding: 12,
                borderTop: '1px solid #e8e8e8',
                background: 'white'
              }}>
                <div style={{ display: 'flex', alignItems: 'center' }}>
                  <Input
                    placeholder="输入您的问题..."
                    style={{ flex: 1, marginRight: 8 }}
                    size="small"
                  />
                  {widgetConfig.enable_file_upload && (
                    <Button size="small" type="text" icon="📎" style={{ marginRight: 4 }} />
                  )}
                  {widgetConfig.enable_voice_input && (
                    <Button size="small" type="text" icon="🎤" style={{ marginRight: 4 }} />
                  )}
                  <Button 
                    type="primary" 
                    size="small"
                    style={{ background: widgetConfig.primary_color || '#1890ff' }}
                  >
                    发送
                  </Button>
                </div>
              </div>
            </div>
            
            {/* 设备框架 */}
            {previewDevice === 'mobile' && (
              <div style={{
                position: 'absolute',
                top: 10,
                left: '50%',
                transform: 'translateX(-50%)',
                width: 300,
                height: 600,
                border: '8px solid #333',
                borderRadius: 25,
                background: 'white',
                pointerEvents: 'none',
                zIndex: 1
              }}>
                <div style={{
                  position: 'absolute',
                  top: 10,
                  left: '50%',
                  transform: 'translateX(-50%)',
                  width: 60,
                  height: 4,
                  background: '#333',
                  borderRadius: 2
                }} />
              </div>
            )}
          </div>
          
          <div style={{ marginTop: 16, textAlign: 'left' }}>
            <Text type="secondary">
              <strong>提示：</strong>
              实际效果会根据您网站的样式和配置自动调整。建议在不同设备上实际测试以获得最佳体验。
            </Text>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default PublicWidget;
