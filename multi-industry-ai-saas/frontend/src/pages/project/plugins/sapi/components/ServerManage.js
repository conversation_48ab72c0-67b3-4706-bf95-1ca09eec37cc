import React, { useState, useEffect } from 'react';
import { Card, Typography, Button, Table, Space, Tag, Modal, Form, Input, Select, Switch, Tabs, Spin, message, Tooltip, Popconfirm, Badge, Drawer, Descriptions, Alert, Radio } from 'antd';
import { PlusOutlined, ReloadOutlined, PlayCircleOutlined, PauseCircleOutlined, DeleteOutlined, EditOutlined, EyeOutlined, SettingOutlined, CodeOutlined, ApiOutlined, ToolOutlined } from '@ant-design/icons';
import ReactJson from 'react-json-view';
import { getServerList, createServer, updateServer, deleteServer, startServer, stopServer, getServerStatus, getServerTools, getClientConfig, addToAIAssistant } from '../services/mcpService';

const { Title, Paragraph, Text } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;
const { TextArea } = Input;

/**
 * MCP服务器管理组件
 */
const ServerManage = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [servers, setServers] = useState([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [modalTitle, setModalTitle] = useState('创建MCP服务器');
  const [editingServer, setEditingServer] = useState(null);
  const [detailVisible, setDetailVisible] = useState(false);
  const [detailServer, setDetailServer] = useState(null);
  const [detailTab, setDetailTab] = useState('1');
  const [serverTools, setServerTools] = useState([]);
  const [toolsLoading, setToolsLoading] = useState(false);
  const [configLoading, setConfigLoading] = useState(false);
  const [clientConfig, setClientConfig] = useState(null);
  const [configType, setConfigType] = useState('python');
  const [transportType, setTransportType] = useState('streamhttp');
  const [environmentType, setEnvironmentType] = useState('docker');

  // 加载服务器列表
  useEffect(() => {
    fetchServers();
  }, []);

  // 获取服务器列表
  const fetchServers = async () => {
    setLoading(true);
    try {
      const response = await getServerList();
      if (response.success) {
        setServers(response.data);
      } else {
        message.error('获取服务器列表失败');
      }
    } catch (error) {
      console.error('获取服务器列表失败:', error);
      message.error('获取服务器列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 打开创建服务器模态框
  const showCreateModal = () => {
    setModalTitle('创建MCP服务器');
    setEditingServer(null);
    form.resetFields();
    setModalVisible(true);
  };

  // 打开编辑服务器模态框
  const showEditModal = (server) => {
    setModalTitle('编辑MCP服务器');
    setEditingServer(server);

    // 设置表单初始值
    form.setFieldsValue({
      name: server.name,
      description: server.description,
      transport_types: Array.isArray(server.transport_types) ? server.transport_types : [server.transport_type || 'streamhttp'],
      deployment_mode: server.deployment_mode || 'local',
      simulation: server.simulation || false,
      use_system_ai: server.use_system_ai || false,
      supports_media: server.supports_media || false,
      env: server.env ? JSON.stringify(server.env, null, 2) : '{}',
      docker_image: server.docker_image || '',
      docker_tag: server.docker_tag || 'latest',
      npm_package: server.npm_package || '',
      npm_version: server.npm_version || 'latest'
    });

    setModalVisible(true);
  };

  // 处理表单提交
  const handleSubmit = async (values) => {
    try {
      // 解析环境变量
      let env = {};
      try {
        env = JSON.parse(values.env);
      } catch (error) {
        message.error('环境变量格式错误，请输入有效的JSON');
        return;
      }

      // 构建服务器配置
      const serverConfig = {
        ...values,
        env
      };

      if (editingServer) {
        // 更新服务器
        const response = await updateServer(editingServer.id, serverConfig);
        if (response.success) {
          message.success('服务器更新成功');
          setModalVisible(false);
          fetchServers();
        } else {
          message.error('服务器更新失败: ' + (response.message || '未知错误'));
        }
      } else {
        // 创建服务器
        const response = await createServer(serverConfig);
        if (response.success) {
          message.success('服务器创建成功');
          setModalVisible(false);
          fetchServers();
        } else {
          message.error('服务器创建失败: ' + (response.message || '未知错误'));
        }
      }
    } catch (error) {
      console.error('提交表单失败:', error);
      message.error('提交表单失败: ' + (error.message || '未知错误'));
    }
  };

  // 处理删除服务器
  const handleDelete = async (serverId) => {
    try {
      const response = await deleteServer(serverId);
      if (response.success) {
        message.success('服务器删除成功');
        fetchServers();
      } else {
        message.error('服务器删除失败: ' + (response.message || '未知错误'));
      }
    } catch (error) {
      console.error('删除服务器失败:', error);
      message.error('删除服务器失败: ' + (error.message || '未知错误'));
    }
  };

  // 处理启动服务器
  const handleStart = async (serverId) => {
    try {
      const response = await startServer(serverId);
      console.log('启动服务器响应:', response); // 添加调试日志
      if (response.success) {
        message.success(response.message || '服务器启动成功');
        fetchServers();
      } else {
        message.error('服务器启动失败: ' + (response.message || '未知错误'));
      }
    } catch (error) {
      console.error('启动服务器失败:', error);
      message.error('启动服务器失败: ' + (error.message || '未知错误'));
    }
  };

    // 处理停止服务器
  const handleStop = async (serverId) => {
    try {
      const response = await stopServer(serverId);
      if (response.success) {
        message.success('服务器停止成功');
        fetchServers();
      } else {
        message.error('服务器停止失败: ' + (response.message || '未知错误'));
      }
    } catch (error) {
      console.error('停止服务器失败:', error);
      message.error('停止服务器失败: ' + (error.message || '未知错误'));
    }
  };

  // 处理一键添加到AI助手
  const handleAddToAIAssistant = async (serverId) => {
    try {
      const response = await addToAIAssistant(serverId);
      if (response.success) {
        message.success('成功添加到AI助手');
      } else {
        message.error('添加到AI助手失败: ' + (response.message || '未知错误'));
      }
    } catch (error) {
      console.error('添加到AI助手失败:', error);
      message.error('添加到AI助手失败: ' + (error.message || '未知错误'));
    }
  };
 
   // 查看服务器详情
  const showServerDetail = async (server) => {
    setDetailServer(server);
    setDetailVisible(true);
    setDetailTab('1');

    // 获取服务器状态
    try {
      const response = await getServerStatus(server.id);
      if (response.success) {
        setDetailServer(prev => ({
          ...prev,
          status_detail: response.data
        }));
      }
    } catch (error) {
      console.error('获取服务器状态失败:', error);
    }

    // 获取服务器工具
    fetchServerTools(server.id);
  };

  // 获取服务器工具列表
  const fetchServerTools = async (serverId) => {
    setToolsLoading(true);
    try {
      const response = await getServerTools(serverId);
      console.log('工具列表API完整响应:', response);
      console.log('response类型:', typeof response);
      
      // 检查是否直接返回了数组（没有success字段）
      if (Array.isArray(response)) {
        console.log('响应是数组格式，直接使用，工具数量:', response.length);
        setServerTools(response);
      } else if (response && response.success && Array.isArray(response.data)) {
        console.log('响应是标准格式，使用data字段，工具数量:', response.data.length);
        setServerTools(response.data);
      } else {
        console.log('API返回格式异常:', response);
        setServerTools([]);
      }
    } catch (error) {
      console.error('获取服务器工具失败:', error);
      setServerTools([]);
    } finally {
      setToolsLoading(false);
    }
  };

  // 获取客户端配置
  const fetchClientConfig = async (serverId) => {
    setConfigLoading(true);
    try {
      const response = await getClientConfig(serverId, {
        config_type: configType,
        transport_types: transportType, // 使用 transport_types 而不是 transport_type
        use_token: true
      });
      if (response.success) {
        setClientConfig(response.data);
      } else {
        message.error('获取MCP客户端配置失败');
      }
    } catch (error) {
      console.error('获取MCP客户端配置失败:', error);
      message.error('获取MCP客户端配置失败');
    } finally {
      setConfigLoading(false);
    }
  };

  // 处理详情标签页切换
  const handleDetailTabChange = (key) => {
    setDetailTab(key);
    if (key === '2' && detailServer) {
      fetchServerTools(detailServer.id);
    } else if (key === '3' && detailServer) {
      fetchClientConfig(detailServer.id);
    }
  };

  // 处理配置类型切换
  const handleConfigTypeChange = (e) => {
    setConfigType(e.target.value);
    if (detailServer) {
      fetchClientConfig(detailServer.id);
    }
  };

  // 处理传输类型切换
  const handleTransportTypeChange = (e) => {
    setTransportType(e.target.value);
    if (detailServer) {
      fetchClientConfig(detailServer.id);
    }
  };

  // 处理环境类型切换
  const handleEnvironmentTypeChange = (e) => {
    setEnvironmentType(e.target.value);
    if (detailServer) {
      fetchClientConfig(detailServer.id);
    }
  };

  // 生成Docker环境变量配置
  const generateDockerEnv = () => {
    if (!detailServer) return '';

    const envVars = [
      `MCP_SERVER_ID=${detailServer.id}`,
      `MCP_SERVER_NAME=${detailServer.name}`,
      `MCP_TRANSPORT_TYPES=${transportType}`,
      `MCP_SUPPORTS_MEDIA=${detailServer.supports_media ? 'true' : 'false'}`,
      `MCP_USE_SYSTEM_AI=${detailServer.use_system_ai ? 'true' : 'false'}`,
      `MCP_SIMULATION=${detailServer.simulation ? 'true' : 'false'}`
    ];

    return envVars.join('\n');
  };

  // 生成直接服务器环境变量配置
  const generateServerEnv = () => {
    if (!detailServer) return '';

    const envVars = [
      `export MCP_SERVER_ID=${detailServer.id}`,
      `export MCP_SERVER_NAME=${detailServer.name}`,
      `export MCP_TRANSPORT_TYPES=${transportType}`,
      `export MCP_SUPPORTS_MEDIA=${detailServer.supports_media ? 'true' : 'false'}`,
      `export MCP_USE_SYSTEM_AI=${detailServer.use_system_ai ? 'true' : 'false'}`,
      `export MCP_SIMULATION=${detailServer.simulation ? 'true' : 'false'}`
    ];

    return envVars.join('\n');
  };

  // 生成Docker运行命令
  const generateDockerCommand = () => {
    if (!detailServer) return '';

    return `docker run -d \\
  --name mcp-server-${detailServer.id.substring(0, 8)} \\
  -p 3000:3000 \\
  -e MCP_SERVER_ID=${detailServer.id} \\
  -e MCP_SERVER_NAME=${detailServer.name} \\
  -e MCP_TRANSPORT_TYPES=${transportType} \\
  -e MCP_SUPPORTS_MEDIA=${detailServer.supports_media ? 'true' : 'false'} \\
  -e MCP_USE_SYSTEM_AI=${detailServer.use_system_ai ? 'true' : 'false'} \\
  -e MCP_SIMULATION=${detailServer.simulation ? 'true' : 'false'} \\
  mcp-server:latest`;
  };

  // 渲染服务器状态标签
  const renderStatusTag = (status) => {
    if (status === 'running') {
      return <Tag color="green">运行中</Tag>;
    } else if (status === 'stopped') {
      return <Tag color="red">已停止</Tag>;
    } else if (status === 'error') {
      return <Tag color="orange">错误</Tag>;
    } else {
      return <Tag color="default">未知</Tag>;
    }
  };

  // 渲染服务器类型标签
  const renderTypeTag = (type) => {
    if (type === 'streamhttp') {
      return <Tag color="blue">Stream HTTP</Tag>;
    } else if (type === 'sse') {
      return <Tag color="purple">SSE</Tag>;
    } else if (type === 'stdio') {
      return <Tag color="cyan">STDIO</Tag>;
    } else {
      return <Tag color="default">{type || '未知'}</Tag>;
    }
  };

  // 表格列定义
  const columns = [
    {
      title: '状态',
      key: 'status',
      render: (_, record) => (
        <Badge
          status={record.status === 'running' ? 'success' : 'default'}
          text={renderStatusTag(record.status)}
        />
      ),
      width: 100,
    },
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      render: text => <Text strong>{text}</Text>
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },
    {
      title: '通信方式',
      key: 'transport_types',
      dataIndex: 'transport_types',
      render: types => (
        <Space>
          {Array.isArray(types) ?
            types.map(type => <span key={type}>{renderTypeTag(type)}</span>) :
            renderTypeTag(types) // 兼容旧数据
          }
        </Space>
      ),
      width: 200,
    },
    {
      title: '部署模式',
      key: 'deployment_mode',
      dataIndex: 'deployment_mode',
      render: mode => {
        const modeMap = {
          local: { color: 'green', text: '本地部署' },
          docker: { color: 'blue', text: 'Docker' },
          npm: { color: 'orange', text: 'NPM' }
        };
        const config = modeMap[mode] || { color: 'default', text: mode || '未知' };
        return <Tag color={config.color}>{config.text}</Tag>;
      },
      width: 100,
    },
    {
      title: '特性',
      key: 'features',
      render: (_, record) => (
        <Space>
          {record.simulation && <Tag color="orange">模拟</Tag>}
          {record.use_system_ai && <Tag color="geekblue">系统AI</Tag>}
          {record.supports_media && <Tag color="purple">媒体支持</Tag>}
        </Space>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: text => new Date(text).toLocaleString(),
      width: 180,
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space>
          <Tooltip title="查看详情">
            <Button
              icon={<EyeOutlined />}
              size="small"
              onClick={() => showServerDetail(record)}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button
              icon={<EditOutlined />}
              size="small"
              onClick={() => showEditModal(record)}
            />
          </Tooltip>
          {record.status === 'running' ? (
            <Tooltip title="停止">
              <Button
                icon={<PauseCircleOutlined />}
                size="small"
                onClick={() => handleStop(record.id)}
                danger
              />
            </Tooltip>
          ) : (
            <Tooltip title="启动">
              <Button
                icon={<PlayCircleOutlined />}
                size="small"
                type="primary"
                onClick={() => handleStart(record.id)}
              />
            </Tooltip>
          )}
          <Tooltip title="一键集成到AI助手">
            <Button
              icon={<ApiOutlined />}
              size="small"
              type="primary"
              ghost
              onClick={() => handleAddToAIAssistant(record.id)}
            />
          </Tooltip>
          <Tooltip title="删除">
            <Popconfirm
              title="确定要删除此服务器吗？"
              onConfirm={() => handleDelete(record.id)}
              okText="确定"
              cancelText="取消"
            >
              <Button
                icon={<DeleteOutlined />}
                size="small"
                danger
              />
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <Card>
      <Title level={4}>MCP服务器管理</Title>
      <Paragraph>
        管理MCP服务器，包括创建、编辑、启动、停止和删除服务器。MCP服务器用于将API端点转换为MCP工具，供大语言模型使用。
      </Paragraph>

      <div style={{ marginBottom: 16 }}>
        <Space>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={showCreateModal}
          >
            创建服务器
          </Button>
          <Button
            icon={<ReloadOutlined />}
            onClick={fetchServers}
          >
            刷新列表
          </Button>
        </Space>
      </div>

      <Spin spinning={loading}>
        <Table
          dataSource={servers}
          columns={columns}
          rowKey="id"
          pagination={{ pageSize: 10 }}
        />
      </Spin>

      {/* 创建/编辑服务器模态框 */}
      <Modal
        title={modalTitle}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={700}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            transport_types: ['streamhttp'],
            deployment_mode: 'local',
            simulation: false,
            use_system_ai: false,
            supports_media: true,
            env: '{}',
            docker_tag: 'latest',
            npm_version: 'latest'
          }}
        >
          <Form.Item
            name="name"
            label="服务器名称"
            rules={[{ required: true, message: '请输入服务器名称' }]}
          >
            <Input placeholder="请输入服务器名称" />
          </Form.Item>

          <Form.Item
            name="description"
            label="服务器描述"
          >
            <TextArea
              placeholder="请输入服务器描述"
              rows={2}
            />
          </Form.Item>

          <Form.Item
            name="transport_types"
            label="通信方式"
            rules={[{ required: true, message: '请选择通信方式' }]}
            tooltip="选择MCP服务器与客户端的通信方式，可多选"
          >
            <Select mode="multiple">
              <Option value="streamhttp">Stream HTTP</Option>
              <Option value="stdio">STDIO</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="deployment_mode"
            label="部署模式"
            rules={[{ required: true, message: '请选择部署模式' }]}
            tooltip="选择MCP服务器的部署方式"
          >
            <Select>
              <Option value="local">本地部署</Option>
              <Option value="docker">Docker部署</Option>
              <Option value="npm">NPM部署</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="simulation"
            label="模拟模式"
            valuePropName="checked"
            tooltip="模拟模式下不会实际启动服务器进程，而是直接从数据库中获取API端点"
          >
            <Switch />
          </Form.Item>

          <Form.Item
            name="use_system_ai"
            label="使用系统AI"
            valuePropName="checked"
            tooltip="使用系统AI模块处理请求，而不是转发到外部服务"
          >
            <Switch />
          </Form.Item>

          <Form.Item
            name="supports_media"
            label="支持媒体"
            valuePropName="checked"
            tooltip="是否支持图片等媒体类型"
          >
            <Switch />
          </Form.Item>

          {/* Docker 部署相关配置 */}
          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) => prevValues.deployment_mode !== currentValues.deployment_mode}
          >
            {({ getFieldValue }) => {
              const deploymentMode = getFieldValue('deployment_mode');

              if (deploymentMode === 'docker') {
                return (
                  <>
                    <Form.Item
                      name="docker_image"
                      label="Docker 镜像"
                      rules={[{ required: true, message: '请输入 Docker 镜像名称' }]}
                      tooltip="Docker 镜像名称，例如：mcpserver/server:latest"
                    >
                      <Input placeholder="请输入 Docker 镜像名称" />
                    </Form.Item>

                    <Form.Item
                      name="docker_tag"
                      label="Docker 标签"
                      tooltip="Docker 镜像标签，默认为 latest"
                    >
                      <Input placeholder="请输入 Docker 镜像标签" />
                    </Form.Item>
                  </>
                );
              }

              if (deploymentMode === 'npm') {
                return (
                  <>
                    <Form.Item
                      name="npm_package"
                      label="NPM 包名"
                      rules={[{ required: true, message: '请输入 NPM 包名' }]}
                      tooltip="NPM 包名，例如：@mcpserver/server"
                    >
                      <Input placeholder="请输入 NPM 包名" />
                    </Form.Item>

                    <Form.Item
                      name="npm_version"
                      label="NPM 版本"
                      tooltip="NPM 包版本，默认为 latest"
                    >
                      <Input placeholder="请输入 NPM 包版本" />
                    </Form.Item>
                  </>
                );
              }

              return (
                <>
                  <Form.Item
                    name="install_dir"
                    label="安装目录"
                    tooltip="MCP 服务器安装目录"
                  >
                    <Input placeholder="请输入安装目录" />
                  </Form.Item>

                  <Form.Item
                    name="command"
                    label="启动命令"
                    tooltip="MCP 服务器启动命令"
                  >
                    <Input placeholder="请输入启动命令" />
                  </Form.Item>
                </>
              );
            }}
          </Form.Item>

          <Form.Item
            name="env"
            label="环境变量"
            tooltip="JSON格式的环境变量配置"
          >
            <TextArea
              placeholder="请输入JSON格式的环境变量配置"
              rows={5}
            />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                {editingServer ? '更新' : '创建'}
              </Button>
              <Button onClick={() => setModalVisible(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 服务器详情抽屉 */}
      <Drawer
        title={detailServer?.name}
        placement="right"
        width={800}
        onClose={() => setDetailVisible(false)}
        open={detailVisible}
      >
        {detailServer && (
          <Tabs activeKey={detailTab} onChange={handleDetailTabChange}>
            <TabPane
              tab={<span><SettingOutlined /> 基本信息</span>}
              key="1"
            >
              <Descriptions bordered column={1}>
                <Descriptions.Item label="ID">{detailServer.id}</Descriptions.Item>
                <Descriptions.Item label="名称">{detailServer.name}</Descriptions.Item>
                <Descriptions.Item label="描述">{detailServer.description}</Descriptions.Item>
                <Descriptions.Item label="状态">
                  {renderStatusTag(detailServer.status)}
                </Descriptions.Item>
                <Descriptions.Item label="通信方式">
                  <Space>
                    {Array.isArray(detailServer.transport_types) ?
                      detailServer.transport_types.map(type => <span key={type}>{renderTypeTag(type)}</span>) :
                      renderTypeTag(detailServer.transport_type || 'streamhttp') // 兼容旧数据
                    }
                  </Space>
                </Descriptions.Item>
                <Descriptions.Item label="特性">
                  <Space>
                    {detailServer.simulation && <Tag color="orange">模拟</Tag>}
                    {detailServer.use_system_ai && <Tag color="geekblue">系统AI</Tag>}
                    {detailServer.supports_media && <Tag color="purple">媒体支持</Tag>}
                  </Space>
                </Descriptions.Item>
                <Descriptions.Item label="安装目录">{detailServer.install_dir || '未指定'}</Descriptions.Item>
                <Descriptions.Item label="创建时间">{new Date(detailServer.created_at).toLocaleString()}</Descriptions.Item>
                <Descriptions.Item label="更新时间">{new Date(detailServer.updated_at).toLocaleString()}</Descriptions.Item>
              </Descriptions>

              {detailServer.status_detail && (
                <div style={{ marginTop: 16 }}>
                  <Title level={5}>状态详情</Title>
                  <ReactJson
                    src={detailServer.status_detail}
                    name={false}
                    collapsed={1}
                    displayDataTypes={false}
                  />
                </div>
              )}

              {detailServer.env && (
                <div style={{ marginTop: 16 }}>
                  <Title level={5}>环境变量</Title>
                  <ReactJson
                    src={typeof detailServer.env === 'string' ? JSON.parse(detailServer.env) : detailServer.env}
                    name={false}
                    collapsed={1}
                    displayDataTypes={false}
                  />
                </div>
              )}
            </TabPane>

            <TabPane
              tab={<span><ApiOutlined /> 工具列表</span>}
              key="2"
            >
              <Spin spinning={toolsLoading}>
                {serverTools.length > 0 ? (
                  <Table
                    dataSource={serverTools}
                    rowKey="name"
                    pagination={false}
                    columns={[
                      {
                        title: '工具名称',
                        dataIndex: 'name',
                        key: 'name',
                        render: text => <Text strong>{text}</Text>
                      },
                      {
                        title: '描述',
                        dataIndex: 'description',
                        key: 'description',
                        ellipsis: true,
                      },
                      {
                        title: '参数',
                        dataIndex: 'inputSchema',
                        key: 'inputSchema',
                        render: inputSchema => {
                          // 兼容两种数据格式：inputSchema 和 parameters
                          const schema = inputSchema || {};
                          const properties = schema.properties || {};
                          const required = schema.required || [];
                          
                          if (!properties || Object.keys(properties).length === 0) return '-';

                          const paramNames = Object.keys(properties);
                          return (
                            <Tooltip
                              title={
                                <ul style={{ margin: 0, paddingLeft: 16 }}>
                                  {paramNames.map(name => (
                                    <li key={name}>
                                      <Text strong>{name}</Text>
                                      {required.includes(name) && (
                                        <Tag color="red" style={{ marginLeft: 4 }}>必填</Tag>
                                      )}
                                      <div>{properties[name].description || properties[name].title || '无描述'}</div>
                                    </li>
                                  ))}
                                </ul>
                              }
                            >
                              <div>{paramNames.length} 个参数</div>
                            </Tooltip>
                          );
                        }
                      }
                    ]}
                  />
                ) : (
                  <Alert
                    message="暂无工具"
                    description="此服务器尚未配置任何工具，请先创建API端点并映射到MCP工具"
                    type="info"
                    showIcon
                  />
                )}
              </Spin>
            </TabPane>

            <TabPane
              tab={<span><CodeOutlined /> 客户端配置</span>}
              key="3"
            >
              <div style={{ marginBottom: 16 }}>
                <Space direction="vertical" style={{ width: '100%' }}>
                  <div>
                    <Text strong>配置类型：</Text>
                    <Radio.Group value={configType} onChange={handleConfigTypeChange}>
                      <Radio.Button value="python">Python</Radio.Button>
                      <Radio.Button value="javascript">JavaScript</Radio.Button>
                      <Radio.Button value="curl">cURL</Radio.Button>
                    </Radio.Group>
                  </div>

                  <div>
                    <Text strong>通信方式：</Text>
                    <Radio.Group value={transportType} onChange={handleTransportTypeChange}>
                      <Radio.Button value="stdio">stdio</Radio.Button>
                      <Radio.Button value="streamhttp">streamhttp</Radio.Button>
                    </Radio.Group>
                  </div>

                  <div>
                    <Text strong>环境类型：</Text>
                    <Radio.Group value={environmentType} onChange={handleEnvironmentTypeChange}>
                      <Radio.Button value="docker">Docker</Radio.Button>
                      <Radio.Button value="server">直接服务器</Radio.Button>
                    </Radio.Group>
                  </div>
                </Space>
              </div>

              <Spin spinning={configLoading}>
                <Tabs defaultActiveKey="1">
                  <TabPane tab="环境变量" key="1">
                    <Card size="small" title="环境变量配置">
                      <pre style={{ background: '#f5f5f5', padding: 16, borderRadius: 4 }}>
                        {environmentType === 'docker' ? generateDockerEnv() : generateServerEnv()}
                      </pre>
                    </Card>
                  </TabPane>

                  {environmentType === 'docker' && (
                    <TabPane tab="Docker命令" key="2">
                      <Card size="small" title="Docker运行命令">
                        <pre style={{ background: '#f5f5f5', padding: 16, borderRadius: 4 }}>
                          {generateDockerCommand()}
                        </pre>
                      </Card>
                    </TabPane>
                  )}

                  <TabPane tab="客户端代码" key="3">
                    {clientConfig ? (
                      <Card size="small" title={`${configType.toUpperCase()} 客户端代码`}>
                        <pre style={{ background: '#f5f5f5', padding: 16, borderRadius: 4 }}>
                          {clientConfig[configType]?.code || '暂无代码示例'}
                        </pre>
                      </Card>
                    ) : (
                      <div style={{ textAlign: 'center', padding: '20px 0' }}>
                        <Text type="secondary">暂无客户端配置</Text>
                      </div>
                    )}
                  </TabPane>
                </Tabs>
              </Spin>
            </TabPane>

            <TabPane
              tab={<span><ToolOutlined /> 操作</span>}
              key="4"
            >
              <Space direction="vertical" style={{ width: '100%' }}>
                <Card size="small" title="服务器操作">
                  <Space>
                    {detailServer.status === 'running' ? (
                      <Button
                        icon={<PauseCircleOutlined />}
                        onClick={() => {
                          handleStop(detailServer.id);
                          setDetailVisible(false);
                        }}
                        danger
                      >
                        停止服务器
                      </Button>
                    ) : (
                      <Button
                        icon={<PlayCircleOutlined />}
                        type="primary"
                        onClick={() => {
                          handleStart(detailServer.id);
                          setDetailVisible(false);
                        }}
                      >
                        启动服务器
                      </Button>
                    )}
                    <Button
                      icon={<EditOutlined />}
                      onClick={() => {
                        showEditModal(detailServer);
                        setDetailVisible(false);
                      }}
                    >
                      编辑服务器
                    </Button>
                    <Popconfirm
                      title="确定要删除此服务器吗？"
                      onConfirm={() => {
                        handleDelete(detailServer.id);
                        setDetailVisible(false);
                      }}
                      okText="确定"
                      cancelText="取消"
                    >
                      <Button
                        icon={<DeleteOutlined />}
                        danger
                      >
                        删除服务器
                      </Button>
                    </Popconfirm>
                  </Space>
                </Card>

                <Alert
                  message="注意事项"
                  description={
                    <ul>
                      <li>停止服务器会终止所有相关进程，请确保没有正在处理的请求</li>
                      <li>删除服务器会同时删除所有相关的工具映射，此操作不可恢复</li>
                      <li>如果服务器无法正常启动，请检查环境变量配置和安装目录</li>
                    </ul>
                  }
                  type="warning"
                  showIcon
                />
              </Space>
            </TabPane>
          </Tabs>
        )}
      </Drawer>
    </Card>
  );
};

export default ServerManage;
