#!/usr/bin/env python
# -*- coding: utf-8 -*-

from typing import List, Optional, Union
from fastapi import APIRouter, Depends, HTTPException, Query, Path, Body, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import or_, and_, func
from datetime import datetime, timedelta, date
import uuid
import logging
from pydantic import validator

from db.database import get_db
from models.project import Project
from models.user import User
from models.store import Store
from models.routine_task import RoutineTask, TaskComment
from schemas.task import (
    TaskCreate,
    TaskUpdate,
    TaskStatusUpdate,
    TaskResponse,
    TaskListResponse,
    TaskCommentCreate,
    TaskCommentResponse,
    TaskCommentListResponse,
    TaskStatsResponse
)
from api.deps import get_current_user, get_current_project
from services.task import TaskService

# 设置日志记录器
logger = logging.getLogger(__name__)

# 辅助函数：解析日期字符串
def parse_date_string(date_str: Optional[str]) -> Optional[datetime]:
    """
    将 YYYY-MM-DD 格式的日期字符串解析为 datetime 对象
    """
    if not date_str:
        return None

    try:
        # 尝试解析 ISO 格式的日期时间
        return datetime.fromisoformat(date_str.replace('Z', '+00:00'))
    except ValueError:
        try:
            # 尝试解析 YYYY-MM-DD 格式的日期
            parsed_date = datetime.strptime(date_str, "%Y-%m-%d")
            return parsed_date
        except ValueError:
            # 如果无法解析，返回 None
            return None

# 创建路由
router = APIRouter(
    tags=["tasks"],
)

@router.get("/admin", response_model=TaskListResponse)
async def get_admin_tasks(
    task_status: Optional[str] = None,
    priority: Optional[str] = None,
    category: Optional[str] = None,
    assignee_id: Optional[uuid.UUID] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    search: Optional[str] = None,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    page: Optional[int] = Query(None, ge=1),
    size: Optional[int] = Query(None, ge=1, le=100),
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    管理员获取所有任务列表，不受 assignee_id 限制
    """
    # 检查用户是否有管理员权限
    is_admin = current_user.is_system_admin or current_user.is_super_admin or current_user.is_tenant_admin
    is_project_admin = False

    # 检查用户是否是项目管理员
    if hasattr(current_user, 'roles'):
        is_project_admin = any(
            role.name.lower() in ['项目管理员', 'project admin', 'admin']
            for role in current_user.roles
        )

    # 如果不是管理员，则返回错误
    if not (is_admin or is_project_admin):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="只有管理员可以访问此接口"
        )

    try:
        # 解析日期字符串
        parsed_start_date = parse_date_string(start_date) if start_date else None
        parsed_end_date = parse_date_string(end_date) if end_date else None

        # 如果提供了 page 和 size 参数，则优先使用它们计算 skip 和 limit
        if page is not None and size is not None:
            skip = (page - 1) * size
            limit = size

        # 调用服务获取所有任务，不传递 assignee_id 参数
        # 如果只有 end_date 而没有 start_date，则不应用 end_date 过滤
        # 因为 end_date 过滤的是 due_date，而很多任务可能没有设置 due_date
        if end_date and not start_date:
            parsed_end_date = None

        tasks, total = await TaskService.get_tasks(
            db=db,
            project_id=project.id,
            status=task_status,
            priority=priority,
            category=category,
            start_date=parsed_start_date,
            end_date=parsed_end_date,
            search=search,
            skip=skip,
            limit=limit
        )

        return {
            "items": tasks,
            "total": total
        }
    except Exception as e:
        logger.error(f"获取管理员任务列表失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取管理员任务列表失败: {str(e)}"
        )

@router.get("", response_model=TaskListResponse)
async def get_tasks(
    task_status: Optional[str] = None,
    priority: Optional[str] = None,
    category: Optional[str] = None,
    assignee_id: Optional[uuid.UUID] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    search: Optional[str] = None,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    page: Optional[int] = Query(None, ge=1),
    size: Optional[int] = Query(None, ge=1, le=100),
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取任务列表
    """
    try:
        # 解析日期字符串
        parsed_start_date = parse_date_string(start_date) if start_date else None
        parsed_end_date = parse_date_string(end_date) if end_date else None

        # 如果提供了 page 和 size 参数，则优先使用它们计算 skip 和 limit
        if page is not None and size is not None:
            skip = (page - 1) * size
            limit = size

        # 如果只有 end_date 而没有 start_date，则不应用 end_date 过滤
        # 因为 end_date 过滤的是 due_date，而很多任务可能没有设置 due_date
        if end_date and not start_date:
            parsed_end_date = None

        tasks, total = await TaskService.get_tasks(
            db=db,
            project_id=project.id,
            status=task_status,
            priority=priority,
            category=category,
            assignee_id=assignee_id,
            start_date=parsed_start_date,
            end_date=parsed_end_date,
            search=search,
            skip=skip,
            limit=limit
        )

        return {
            "items": tasks,
            "total": total
        }
    except Exception as e:
        logger.error(f"获取任务列表失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取任务列表失败: {str(e)}"
        )

@router.post("", response_model=TaskResponse, status_code=status.HTTP_201_CREATED)
async def create_task(
    task_data: TaskCreate,
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    创建任务
    """
    try:
        task = await TaskService.create_task(
            db=db,
            project_id=project.id,
            task_data=task_data,
            created_by=current_user.id
        )

        return task
    except Exception as e:
        logger.error(f"创建任务失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建任务失败: {str(e)}"
        )

@router.get("/stats", response_model=TaskStatsResponse)
async def get_task_stats(
    period: str = Query("month", description="统计周期: week-最近一周, month-最近一月, year-最近一年"),
    category: Optional[str] = Query(None, description="任务类别过滤"),
    store_id: Optional[uuid.UUID] = Query(None, description="门店ID过滤"),
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取任务统计数据

    返回按状态、门店、类别、任务类型、优先级和时间的统计数据
    """
    try:
        # 检查用户是否有管理员权限
        is_admin = current_user.is_system_admin or current_user.is_super_admin or current_user.is_tenant_admin
        is_project_admin = False

        # 检查用户是否是项目管理员
        if hasattr(current_user, 'roles'):
            is_project_admin = any(
                role.name.lower() in ['项目管理员', 'project admin', 'admin']
                for role in current_user.roles
            )

        # 如果不是管理员，则返回错误
        if not (is_admin or is_project_admin):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="只有管理员可以访问此接口"
            )

        # 验证 period 参数
        if period not in ["week", "month", "year"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="period 参数必须是 week, month 或 year"
            )

        # 获取任务统计数据
        stats = await TaskService.get_task_stats(
            db=db,
            project_id=project.id,
            period=period,
            category=category,
            store_id=store_id
        )

        return stats
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"获取任务统计数据失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取任务统计数据失败: {str(e)}"
        )

@router.get("/{task_id}", response_model=TaskResponse)
async def get_task(
    task_id: uuid.UUID = Path(...),
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取任务详情
    """
    try:
        task = await TaskService.get_task_by_id(
            db=db,
            project_id=project.id,
            task_id=task_id
        )

        if not task:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="任务不存在"
            )

        return task
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"获取任务详情失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取任务详情失败: {str(e)}"
        )

@router.put("/{task_id}", response_model=TaskResponse)
async def update_task(
    task_data: TaskUpdate,
    task_id: uuid.UUID = Path(...),
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    更新任务
    """
    try:
        # 检查任务是否存在
        task = await TaskService.get_task_by_id(
            db=db,
            project_id=project.id,
            task_id=task_id
        )

        if not task:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="任务不存在"
            )

        # 更新任务
        updated_task = await TaskService.update_task(
            db=db,
            project_id=project.id,
            task_id=task_id,
            task_data=task_data,
            updated_by=current_user.id
        )

        return updated_task
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"更新任务失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新任务失败: {str(e)}"
        )

@router.patch("/{task_id}/status", response_model=TaskResponse)
async def update_task_status(
    status_data: TaskStatusUpdate,
    task_id: uuid.UUID = Path(...),
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    更新任务状态
    """
    try:
        # 检查任务是否存在
        task = await TaskService.get_task_by_id(
            db=db,
            project_id=project.id,
            task_id=task_id
        )

        if not task:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="任务不存在"
            )

        # 更新任务状态
        updated_task = await TaskService.update_task_status(
            db=db,
            project_id=project.id,
            task_id=task_id,
            status=status_data.status,
            updated_by=current_user.id
        )

        return updated_task
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"更新任务状态失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新任务状态失败: {str(e)}"
        )

@router.delete("/{task_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_task(
    task_id: uuid.UUID = Path(...),
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    删除任务
    """
    try:
        # 检查任务是否存在
        task = await TaskService.get_task_by_id(
            db=db,
            project_id=project.id,
            task_id=task_id
        )

        if not task:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="任务不存在"
            )

        # 删除任务
        await TaskService.delete_task(
            db=db,
            project_id=project.id,
            task_id=task_id
        )

        return None
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"删除任务失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除任务失败: {str(e)}"
        )

@router.get("/{task_id}/comments", response_model=TaskCommentListResponse)
async def get_task_comments(
    task_id: uuid.UUID = Path(...),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=100),
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取任务评论列表
    """
    try:
        # 检查任务是否存在
        task = await TaskService.get_task_by_id(
            db=db,
            project_id=project.id,
            task_id=task_id
        )

        if not task:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="任务不存在"
            )

        # 获取任务评论
        comments, total = await TaskService.get_task_comments(
            db=db,
            task_id=task_id,
            skip=skip,
            limit=limit
        )

        return {
            "items": comments,
            "total": total
        }
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"获取任务评论失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取任务评论失败: {str(e)}"
        )

@router.post("/{task_id}/comments", response_model=TaskCommentResponse, status_code=status.HTTP_201_CREATED)
async def create_task_comment(
    comment_data: TaskCommentCreate,
    task_id: uuid.UUID = Path(...),
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    创建任务评论
    """
    try:
        # 检查任务是否存在
        task = await TaskService.get_task_by_id(
            db=db,
            project_id=project.id,
            task_id=task_id
        )

        if not task:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="任务不存在"
            )

        # 创建任务评论
        comment = await TaskService.create_task_comment(
            db=db,
            task_id=task_id,
            comment_data=comment_data,
            created_by=current_user.id
        )

        return comment
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"创建任务评论失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建任务评论失败: {str(e)}"
        )



@router.delete("/{task_id}/comments/{comment_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_task_comment(
    task_id: uuid.UUID = Path(...),
    comment_id: uuid.UUID = Path(...),
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    删除任务评论
    """
    try:
        # 检查任务是否存在
        task = await TaskService.get_task_by_id(
            db=db,
            project_id=project.id,
            task_id=task_id
        )

        if not task:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="任务不存在"
            )

        # 检查评论是否存在
        comment = await TaskService.get_comment_by_id(
            db=db,
            comment_id=comment_id
        )

        if not comment or comment.task_id != task_id:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="评论不存在"
            )

        # 检查权限
        if comment.created_by != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权删除此评论"
            )

        # 删除评论
        await TaskService.delete_task_comment(
            db=db,
            comment_id=comment_id
        )

        return None
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"删除任务评论失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除任务评论失败: {str(e)}"
        )
