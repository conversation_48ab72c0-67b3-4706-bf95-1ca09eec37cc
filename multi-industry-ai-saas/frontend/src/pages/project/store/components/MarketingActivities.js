import React, { useState, useEffect } from 'react';
import {
  Card,
  List,
  Tag,
  Typography,
  Space,
  Spin,
  Empty,
  Badge,
  Divider,
  Progress,
  Button,
  Modal,
  Descriptions,
  Row,
  Col,
  Input,
  Select,
  DatePicker,
  message
} from 'antd';
import {
  GiftOutlined,
  CalendarOutlined,
  ShopOutlined,
  UserOutlined,
  PercentageOutlined,
  SearchOutlined,
  FilterOutlined,
  SyncOutlined
} from '@ant-design/icons';
import apiService from '../../../../services/api';
import dayjs from 'dayjs';

const { Title, Text, Paragraph } = Typography;
const { Search } = Input;
const { Option } = Select;
const { RangePicker } = DatePicker;

/**
 * 营销活动组件
 * 显示门店可以参与的营销活动
 */
const MarketingActivities = () => {
  const [activities, setActivities] = useState([]);
  const [loading, setLoading] = useState(false);
  const [detailVisible, setDetailVisible] = useState(false);
  const [currentActivity, setCurrentActivity] = useState(null);
  const [searchText, setSearchText] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');

  // 获取营销活动列表
  useEffect(() => {
    fetchActivities();
  }, []);

  // 获取营销活动
  const fetchActivities = async () => {
    setLoading(true);
    try {
      const response = await apiService.project.marketingActivity.getList();
      
      if (response && response.items) {
        setActivities(response.items);
      } else {
        message.error('获取营销活动失败');
      }
    } catch (error) {
      console.error('获取营销活动失败:', error);
      message.error('获取营销活动失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  // 查看活动详情
  const viewActivityDetail = (activity) => {
    setCurrentActivity(activity);
    setDetailVisible(true);
  };

  // 获取活动状态
  const getActivityStatus = (activity) => {
    const now = dayjs();
    const startDate = dayjs(activity.start_date);
    const endDate = dayjs(activity.end_date);

    if (activity.status === 'cancelled') {
      return { status: 'cancelled', text: '已取消', color: 'error' };
    } else if (now.isBefore(startDate)) {
      return { status: 'upcoming', text: '即将开始', color: 'warning' };
    } else if (now.isAfter(endDate)) {
      return { status: 'ended', text: '已结束', color: 'default' };
    } else {
      return { status: 'active', text: '进行中', color: 'success' };
    }
  };

  // 筛选活动
  const filteredActivities = activities.filter(activity => {
    // 搜索文本筛选
    const matchesSearch = searchText ? 
      (activity.title && activity.title.toLowerCase().includes(searchText.toLowerCase())) || 
      (activity.description && activity.description.toLowerCase().includes(searchText.toLowerCase())) : 
      true;
    
    // 状态筛选
    const activityStatus = getActivityStatus(activity).status;
    const matchesStatus = statusFilter === 'all' ? true : activityStatus === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  // 渲染活动列表
  const renderActivityList = () => {
    return (
      <List
        grid={{ gutter: 16, xs: 1, sm: 1, md: 2, lg: 3, xl: 3, xxl: 4 }}
        dataSource={filteredActivities}
        renderItem={activity => {
          const { status, text, color } = getActivityStatus(activity);
          return (
            <List.Item>
              <Card 
                hoverable
                cover={activity.image_url && <img alt={activity.title} src={activity.image_url} />}
                actions={[
                  <Button type="link" onClick={() => viewActivityDetail(activity)}>查看详情</Button>
                ]}
              >
                <Badge.Ribbon text={text} color={color}>
                  <Card.Meta
                    title={activity.title}
                    description={
                      <Space direction="vertical" style={{ width: '100%' }}>
                        <Text type="secondary" ellipsis={{ tooltip: activity.description }}>
                          {activity.description || '暂无描述'}
                        </Text>
                        <Space>
                          <CalendarOutlined /> 
                          {dayjs(activity.start_date).format('YYYY-MM-DD')} 至 {dayjs(activity.end_date).format('YYYY-MM-DD')}
                        </Space>
                        {activity.discount_rate && (
                          <Tag color="red">
                            <PercentageOutlined /> 折扣: {activity.discount_rate * 10}折
                          </Tag>
                        )}
                      </Space>
                    }
                  />
                </Badge.Ribbon>
              </Card>
            </List.Item>
          );
        }}
      />
    );
  };

  return (
    <div className="marketing-activities-container">
      <div className="marketing-activities-header" style={{ marginBottom: 16 }}>
        <Row gutter={16} align="middle">
          <Col xs={24} sm={8} md={6} lg={6}>
            <Search
              placeholder="搜索活动名称或描述"
              value={searchText}
              onChange={e => setSearchText(e.target.value)}
              style={{ width: '100%' }}
            />
          </Col>
          <Col xs={12} sm={8} md={4} lg={4}>
            <Select
              placeholder="活动状态"
              style={{ width: '100%' }}
              value={statusFilter}
              onChange={value => setStatusFilter(value)}
            >
              <Option value="all">全部状态</Option>
              <Option value="active">进行中</Option>
              <Option value="upcoming">即将开始</Option>
              <Option value="ended">已结束</Option>
            </Select>
          </Col>
          <Col xs={12} sm={8} md={4} lg={4}>
            <Button 
              type="primary" 
              icon={<SyncOutlined />} 
              onClick={fetchActivities}
            >
              刷新
            </Button>
          </Col>
        </Row>
      </div>

      <Spin spinning={loading}>
        {filteredActivities.length > 0 ? renderActivityList() : <Empty description="暂无营销活动" />}
      </Spin>

      {/* 活动详情弹窗 */}
      <Modal
        title="活动详情"
        open={detailVisible}
        onCancel={() => setDetailVisible(false)}
        footer={[
          <Button key="close" onClick={() => setDetailVisible(false)}>
            关闭
          </Button>
        ]}
        width={700}
      >
        {currentActivity && (
          <div className="activity-detail">
            <Title level={4}>{currentActivity.title}</Title>
            <Descriptions bordered column={1}>
              <Descriptions.Item label="活动状态">
                <Tag color={getActivityStatus(currentActivity).color}>
                  {getActivityStatus(currentActivity).text}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="活动时间">
                {dayjs(currentActivity.start_date).format('YYYY-MM-DD')} 至 {dayjs(currentActivity.end_date).format('YYYY-MM-DD')}
              </Descriptions.Item>
              {currentActivity.discount_rate && (
                <Descriptions.Item label="折扣力度">
                  {currentActivity.discount_rate * 10}折
                </Descriptions.Item>
              )}
              <Descriptions.Item label="活动描述">
                {currentActivity.description || '暂无描述'}
              </Descriptions.Item>
            </Descriptions>
            
            {currentActivity.image_url && (
              <div style={{ marginTop: 16, textAlign: 'center' }}>
                <img 
                  src={currentActivity.image_url} 
                  alt={currentActivity.title} 
                  style={{ maxWidth: '100%', maxHeight: 300 }} 
                />
              </div>
            )}
          </div>
        )}
      </Modal>
    </div>
  );
};

export default MarketingActivities;
