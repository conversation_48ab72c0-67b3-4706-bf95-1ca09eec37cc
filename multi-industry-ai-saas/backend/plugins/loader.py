#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
插件加载器

这个模块负责发现和加载插件。
"""

import os
import importlib
import logging
import inspect
from typing import Dict, List, Any, Optional, Set

from fastapi import APIRouter, Depends, HTTPException, status
from fastapi.security import HTT<PERSON><PERSON>earer, HTTPAuthorizationCredentials

from .base import BasePlugin, plugin_manager, verify_jwt_token
# 使用相对导入
try:
    from api.auth.jwt import JWTPayload
except ImportError:
    # 在Docker容器中，路径可能不同
    try:
        from ..api.auth.jwt import JWTPayload
    except ImportError:
        # 如果都失败，尝试直接导入
        try:
            import sys
            import os
            sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
            from api.auth.jwt import JWTPayload
        except ImportError:
            # 如果还是失败，使用本地实现
            from dataclasses import dataclass
            from typing import Dict, List, Optional

            @dataclass
            class JWTPayload:
                """JWT负载"""
                sub: str  # 用户ID
                token_id: str  # 令牌ID
                token_type: str  # 令牌类型：access, refresh, mcp
                project_id: Optional[str] = None  # 项目ID
                permissions: Optional[List[str]] = None  # 权限列表
                server_permissions: Optional[Dict[str, List[str]]] = None  # 服务器权限映射
                default_permissions: Optional[List[str]] = None  # 默认权限
                exp: Optional[int] = None  # 过期时间
                iat: Optional[int] = None  # 签发时间
                jti: Optional[str] = None  # JWT ID

logger = logging.getLogger(__name__)

class PluginLoader:
    """
    插件加载器

    负责发现和加载插件
    """

    def __init__(self, plugins_dir: str = "plugins"):
        """
        初始化插件加载器

        Args:
            plugins_dir: 插件目录
        """
        self.plugins_dir = plugins_dir
        self.plugin_manager = plugin_manager

    async def discover_plugins(self) -> None:
        """
        发现并加载所有插件
        """
        logger.info(f"开始发现插件，插件目录: {self.plugins_dir}")

        # 获取插件目录的绝对路径
        plugins_path = os.path.abspath(self.plugins_dir)

        # 确保插件目录存在
        if not os.path.exists(plugins_path):
            logger.warning(f"插件目录 {plugins_path} 不存在")
            return

        # 遍历插件目录
        for item in os.listdir(plugins_path):
            # 忽略特殊文件和目录
            if item.startswith("__") or item.startswith(".") or item in ["base.py", "loader.py"]:
                continue

            # 构建插件路径
            plugin_path = os.path.join(plugins_path, item)

            # 只处理目录
            if os.path.isdir(plugin_path):
                await self._load_plugin_from_directory(item, plugin_path)

    async def _load_plugin_from_directory(self, plugin_name: str, plugin_path: str) -> None:
        """
        从目录加载插件

        Args:
            plugin_name: 插件名称
            plugin_path: 插件路径
        """
        logger.info(f"尝试加载插件: {plugin_name}")

        try:
            # 检查插件目录中是否有 plugin.py 文件
            plugin_file = os.path.join(plugin_path, "plugin.py")
            if os.path.exists(plugin_file):
                # 导入插件模块
                module_name = f"plugins.{plugin_name}.plugin"
                plugin_module = importlib.import_module(module_name)

                # 查找插件类
                for name, obj in inspect.getmembers(plugin_module):
                    if (inspect.isclass(obj) and
                        issubclass(obj, BasePlugin) and
                        obj is not BasePlugin):
                        # 创建插件实例
                        plugin_instance = obj()

                        # 注册插件
                        await self.plugin_manager.register_plugin(plugin_instance)

                        logger.info(f"插件 {plugin_name} 加载成功")
                        break
                else:
                    logger.warning(f"插件 {plugin_name} 中未找到 BasePlugin 的子类")
            else:
                logger.warning(f"插件 {plugin_name} 中未找到 plugin.py 文件")
        except Exception as e:
            logger.error(f"加载插件 {plugin_name} 失败: {str(e)}")
            import traceback
            traceback.print_exc()

    def create_mcp_server_router(self, plugin_id: str) -> APIRouter:
        """
        创建MCP服务器路由

        Args:
            plugin_id: 插件ID

        Returns:
            APIRouter: MCP服务器路由
        """
        plugin = self.plugin_manager.get_plugin(plugin_id)
        if not plugin:
            raise ValueError(f"插件 {plugin_id} 不存在")

        router = APIRouter()

        # 添加MCP服务器路由
        @router.get("/tools", summary="获取工具列表")
        async def get_tools(
            payload: JWTPayload = Depends(lambda: verify_jwt_token(required_permissions=["read"], required_server_id=plugin_id))
        ):
            """获取可用的工具列表"""
            # 这里应该返回插件支持的工具列表
            return {"tools": []}

        @router.post("/invoke", summary="调用工具")
        async def invoke_tool(
            request: Dict[str, Any],
            payload: JWTPayload = Depends(lambda: verify_jwt_token(required_permissions=["write"], required_server_id=plugin_id))
        ):
            """调用工具"""
            # 这里应该调用插件的工具
            return {"result": "工具调用成功"}

        return router


# 创建全局插件加载器实例
plugin_loader = PluginLoader()
