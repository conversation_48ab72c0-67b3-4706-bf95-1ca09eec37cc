import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Table, 
  Button, 
  Space, 
  Tag, 
  Modal, 
  Form, 
  Input, 
  Select, 
  message, 
  Tabs, 
  Statistic, 
  Row, 
  Col,
  Tooltip,
  Divider,
  Upload
} from 'antd';
import { 
  FolderOutlined, 
  FileTextOutlined, 
  SyncOutlined, 
  UploadOutlined,
  DownloadOutlined,
  EditOutlined,
  EyeOutlined,
  ShareAltOutlined,
  CloudUploadOutlined
} from '@ant-design/icons';
import dingtalkApi from '../service/dingtalkApi';
import apiService from '../../../../../services/api';

const { TextArea } = Input;

const { TabPane } = Tabs;
const { Option } = Select;

const DocumentManagement = () => {
  const [loading, setLoading] = useState(false);
  const [spaces, setSpaces] = useState([]);
  const [documents, setDocuments] = useState([]);
  const [statistics, setStatistics] = useState({});
  const [activeTab, setActiveTab] = useState('overview');
  
  // 知识库相关状态
  const [knowledgeBases, setKnowledgeBases] = useState([]);
  const [loadingKnowledgeBases, setLoadingKnowledgeBases] = useState(false);
  
  // 模态框状态
  const [syncModalVisible, setSyncModalVisible] = useState(false);
  const [uploadModalVisible, setUploadModalVisible] = useState(false);
  const [batchUploadModalVisible, setBatchUploadModalVisible] = useState(false);
  const [viewModalVisible, setViewModalVisible] = useState(false);
  const [selectedDocument, setSelectedDocument] = useState(null);
  
  // 表单
  const [syncForm] = Form.useForm();
  const [uploadForm] = Form.useForm();
  const [batchUploadForm] = Form.useForm();

  useEffect(() => {
    loadData();
    loadAvailableKnowledgeBases();
  }, []);

  const loadData = async () => {
    setLoading(true);
    try {
      await Promise.all([
        loadSpaces(),
        loadDocuments(),
        loadStatistics()
      ]);
    } catch (error) {
      console.error('加载数据失败:', error);
      message.error('加载数据失败');
    } finally {
      setLoading(false);
    }
  };

  const loadSpaces = async () => {
    try {
      const response = await dingtalkApi.getDingtalkSpaces();
      console.log('获取知识库列表响应:', response); // 添加日志便于调试
      
      if (response && response.success && response.data) {
        // 根据后端实际返回结构解析知识库数据
        const spacesData = response.data.spaces || [];
        console.log('解析的知识库数据:', spacesData);
        setSpaces(spacesData);
      } else {
        console.warn('获取知识库列表失败:', response);
        setSpaces([]);
      }
    } catch (error) {
      console.error('获取知识库列表失败:', error);
      setSpaces([]);
    }
  };

  const loadDocuments = async () => {
    try {
      const response = await dingtalkApi.getProjectDocuments();
      if (response.success) {
        setDocuments(response.data.spaces || []);
      }
    } catch (error) {
      console.error('获取文档列表失败:', error);
    }
  };

  const loadStatistics = async () => {
    try {
      // 从API获取准确的统计信息
      const response = await dingtalkApi.getDingtalkSpaces();
      
      if (response && response.success && response.data) {
        const apiStatistics = response.data.statistics || {};
        const spacesData = response.data.spaces || [];
        
        // 优先使用API返回的统计信息
        setStatistics({
          totalSpaces: apiStatistics.total_workspaces || spacesData.length,
          totalDocuments: apiStatistics.total_documents || 0,
          syncedDocuments: apiStatistics.synced_documents || 0,
          syncRate: apiStatistics.sync_rate || 0
        });
      } else {
        // 备选：基于本地数据计算统计信息
        const totalSpaces = spaces.length;
        const totalDocuments = documents.reduce((sum, space) => sum + (space.documents?.length || 0), 0);
        const syncedDocuments = documents.reduce((sum, space) => 
          sum + (space.documents?.filter(doc => doc.sync_status === 'synced').length || 0), 0
        );
        
        setStatistics({
          totalSpaces,
          totalDocuments,
          syncedDocuments,
          syncRate: totalDocuments > 0 ? Math.round((syncedDocuments / totalDocuments) * 100) : 0
        });
      }
    } catch (error) {
      console.error('获取统计信息失败:', error);
      // 如果API失败，使用默认值
      setStatistics({
        totalSpaces: 0,
        totalDocuments: 0,
        syncedDocuments: 0,
        syncRate: 0
      });
    }
  };

  // 加载可用的知识库列表（项目知识库和AI知识库）
  const loadAvailableKnowledgeBases = async () => {
    setLoadingKnowledgeBases(true);
    try {
      const [projectKnowledgeBases, aiKnowledgeBases] = await Promise.all([
        loadProjectKnowledgeBases(),
        loadAIKnowledgeBases()
      ]);
      
      // 确保返回的数据是数组
      const safeProjectKbs = Array.isArray(projectKnowledgeBases) ? projectKnowledgeBases : [];
      const safeAiKbs = Array.isArray(aiKnowledgeBases) ? aiKnowledgeBases : [];
      
      const allKnowledgeBases = [
        ...safeProjectKbs.map(kb => ({ ...kb, type: 'project' })),
        ...safeAiKbs.map(kb => ({ ...kb, type: 'ai' }))
      ];
      
      setKnowledgeBases(allKnowledgeBases);
    } catch (error) {
      console.error('加载知识库列表失败:', error);
      message.error('加载知识库列表失败');
    } finally {
      setLoadingKnowledgeBases(false);
    }
  };

  // 加载项目知识库
  const loadProjectKnowledgeBases = async () => {
    try {
      const response = await apiService.project.knowledgeBase.getCategories();
      // 处理API返回结构，可能是 {success: true, data: []} 或直接返回数组
      if (response && response.success && Array.isArray(response.data)) {
        return response.data;
      } else if (Array.isArray(response)) {
        return response;
      } else {
        return [];
      }
    } catch (error) {
      console.error('获取项目知识库失败:', error);
      return [];
    }
  };

  // 加载AI知识库
  const loadAIKnowledgeBases = async () => {
    try {
      const response = await apiService.project.knowledgeBase.getAvailableAIKnowledgeBases();
      // 处理API返回结构，可能是 {success: true, data: []} 或直接返回数组
      if (response && response.success && Array.isArray(response.data)) {
        return response.data;
      } else if (Array.isArray(response)) {
        return response;
      } else {
        return [];
      }
    } catch (error) {
      console.error('获取AI知识库失败:', error);
      return [];
    }
  };

  const handleSyncDocuments = async (values) => {
    setLoading(true);
    try {
      console.log('开始同步钉钉文档，参数:', values);
      
      // 调用增强的同步API
      const response = await dingtalkApi.syncDingtalkDocuments({
        sync_type: values.sync_type || 'full',
        specific_workspace_ids: values.specific_workspace_ids || [],
        storage_strategy: values.storage_strategy || 'mixed',
        max_file_size_mb: values.max_file_size_mb || 10,
        include_subfolders: values.include_subfolders !== false,
        enable_incremental: values.enable_incremental === true
      });
      
      console.log('文档同步响应:', response);
      
      if (response && response.success) {
        const details = response.data?.details || {};
        const syncedCount = details.total_documents || 0;
        const spacesCount = details.total_workspaces || 0;
        
        message.success(
          `文档同步成功！同步了 ${spacesCount} 个工作空间，${syncedCount} 个文档`
        );
        
        // 刷新数据
        await loadData();
      } else {
        const errorMsg = response?.message || response?.error || '同步失败';
        console.error('文档同步失败:', errorMsg);
        message.error(`文档同步失败: ${errorMsg}`);
      }
    } catch (error) {
      console.error('文档同步异常:', error);
      message.error(`文档同步异常: ${error.message}`);
    } finally {
      setLoading(false);
      setSyncModalVisible(false);
      syncForm.resetFields();
    }
  };

  const handleUploadToDigntalk = async (values) => {
    setLoading(true);
    try {
      const response = await dingtalkApi.uploadDocumentToDingtalk(values);
      if (response.success) {
        message.success('文档上传到钉钉成功');
        setUploadModalVisible(false);
        uploadForm.resetFields();
        await loadData();
      } else {
        message.error(response.message || '文档上传失败');
      }
    } catch (error) {
      console.error('文档上传失败:', error);
      message.error('文档上传失败');
    } finally {
      setLoading(false);
    }
  };

  // 批量上传知识库到钉钉
  const handleBatchUploadToDigntalk = async (values) => {
    setLoading(true);
    try {
      const response = await dingtalkApi.uploadKnowledgeBasesToDingtalk(values);
      if (response.success) {
        message.success(response.message || '知识库批量上传到钉钉成功');
        setBatchUploadModalVisible(false);
        batchUploadForm.resetFields();
        await loadData();
      } else {
        message.error(response.message || '知识库批量上传失败');
      }
    } catch (error) {
      console.error('知识库批量上传失败:', error);
      message.error('知识库批量上传失败');
    } finally {
      setLoading(false);
    }
  };

  const handleViewDocument = (document) => {
    setSelectedDocument(document);
    setViewModalVisible(true);
  };

  const getSyncStatusColor = (status) => {
    const colors = {
      'synced': 'green',
      'pending': 'orange',
      'failed': 'red',
      'draft': 'blue'
    };
    return colors[status] || 'default';
  };

  const getSyncStatusText = (status) => {
    switch (status) {
      case 'synced': return '已同步';
      case 'syncing': return '同步中';
      case 'failed': return '同步失败';
      case 'pending': return '待同步';
      default: return '未知';
    }
  };

  // 获取文档类型图标
  const getDocumentTypeIcon = (type, nodeType) => {
    // 处理钉钉文档类型
    if (type) {
      switch (type.toLowerCase()) {
        case 'sheet':
        case 'table':
        case 'spreadsheet':
          return '📊';
        case 'doc':
        case 'document':
          return '📄';
        case 'presentation':
        case 'slide':
          return '📊';
        case 'file':
          return '📁';
        case 'folder':
          return '📂';
        default:
          return '📄';
      }
    }
    
    // 处理钉钉节点类型
    if (nodeType) {
      switch (nodeType.toLowerCase()) {
        case 'file':
          return '📄';
        case 'folder':
          return '📂';
        case 'sheet':
          return '📊';
        case 'doc':
          return '📄';
        default:
          return '📄';
      }
    }
    
    return '📄';
  };

  // 获取存储策略显示
  const getStorageStrategyDisplay = (strategy) => {
    switch (strategy) {
      case 'link_only':
        return { text: '🔗 仅链接', color: 'blue' };
      case 'local_storage':
        return { text: '💾 本地存储', color: 'green' };
      case 'mixed':
        return { text: '🔀 混合模式', color: 'orange' };
      default:
        return { text: '❓ 未知', color: 'default' };
    }
  };

  // 格式化文件大小
  const formatFileSize = (bytes) => {
    if (!bytes || bytes === 0) return '--';
    
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  // 知识库表格列
  const spaceColumns = [
    {
      title: '知识库名称',
      dataIndex: 'space_name',
      key: 'space_name',
      render: (text, record) => (
        <Space>
          <FolderOutlined />
          <span>{text}</span>
        </Space>
      ),
    },
    {
      title: '描述',
      dataIndex: 'space_description',
      key: 'space_description',
      ellipsis: true,
    },
    {
      title: '文档数量',
      dataIndex: 'doc_count',
      key: 'doc_count',
      render: (count) => <Tag color="blue">{count || 0}</Tag>,
    },
    {
      title: '同步状态',
      dataIndex: 'sync_status',
      key: 'sync_status',
      render: (status) => (
        <Tag color={getSyncStatusColor(status)}>
          {getSyncStatusText(status)}
        </Tag>
      ),
    },
    {
      title: '自动同步',
      dataIndex: 'auto_sync',
      key: 'auto_sync',
      render: (autoSync) => (
        <Tag color={autoSync ? 'green' : 'default'}>
          {autoSync ? '开启' : '关闭'}
        </Tag>
      ),
    },
    {
      title: '最后同步时间',
      dataIndex: 'last_sync_at',
      key: 'last_sync_at',
      render: (time) => time ? new Date(time).toLocaleString() : '-',
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Tooltip title="同步文档">
            <Button 
              key="sync"
              type="link" 
              icon={<SyncOutlined />}
              onClick={() => {
                syncForm.setFieldsValue({ space_id: record.dingtalk_space_id });
                setSyncModalVisible(true);
              }}
            />
          </Tooltip>
          <Tooltip title="查看详情">
            <Button 
              key="view"
              type="link" 
              icon={<EyeOutlined />}
              onClick={() => handleViewDocument(record)}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  // 文档表格列
  const documentColumns = [
    {
      title: '文档标题',
      dataIndex: 'title',
      key: 'title',
      render: (text, record) => (
        <Space>
          <FileTextOutlined />
          <span>{text}</span>
          {record.dingtalk_url && (
            <Tag 
              color="orange" 
              style={{ cursor: 'pointer' }}
              onClick={() => window.open(record.dingtalk_url, '_blank')}
            >
              🔗 钉钉链接
            </Tag>
          )}
        </Space>
      ),
    },
    {
      title: '文档类型',
      dataIndex: 'document_type',
      key: 'document_type',
      render: (type) => {
        const typeIcons = {
          'sheet': '📊',
          'doc': '📄',
          'slide': '📊',
          'file': '📁',
          'folder': '📂'
        };
        const typeNames = {
          'sheet': '表格',
          'doc': '文档',
          'slide': '演示',
          'file': '文件',
          'folder': '文件夹'
        };
        return (
          <Tag>
            {typeIcons[type] || '📄'} {typeNames[type] || type || '未知'}
          </Tag>
        );
      },
    },
    {
      title: '存储策略',
      dataIndex: 'storage_strategy',
      key: 'storage_strategy',
      render: (strategy, record) => {
        if (strategy === 'link_only') {
          return (
            <Tag color="blue" title="钉钉文档只能通过链接访问，无法下载内容">
              🔗 仅链接
            </Tag>
          );
        } else if (strategy === 'download_local') {
          return (
            <Tag color="green" title="文档内容已下载到本地">
              💾 本地存储
            </Tag>
          );
        }
        return <Tag color="default">{strategy || '未知'}</Tag>;
      },
    },
    {
      title: '文件大小',
      dataIndex: 'file_size',
      key: 'file_size',
      render: (size) => {
        if (!size || size === 0) return '-';
        if (size < 1024) return `${size} B`;
        if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)} KB`;
        if (size < 1024 * 1024 * 1024) return `${(size / (1024 * 1024)).toFixed(1)} MB`;
        return `${(size / (1024 * 1024 * 1024)).toFixed(1)} GB`;
      },
    },
    {
      title: '同步状态',
      dataIndex: 'sync_status',
      key: 'sync_status',
      render: (status) => (
        <Tag color={getSyncStatusColor(status)}>
          {getSyncStatusText(status)}
        </Tag>
      ),
    },
    {
      title: '最后同步时间',
      dataIndex: 'updated_at',
      key: 'updated_at',
      render: (time) => time ? new Date(time).toLocaleString() : '-',
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Tooltip title="查看内容">
            <Button 
              key="view-content"
              type="link" 
              icon={<EyeOutlined />}
              onClick={() => handleViewDocument(record)}
            />
          </Tooltip>
          {record.dingtalk_url && (
            <Tooltip title="在钉钉中打开">
              <Button 
                key="open-dingtalk"
                type="link" 
                icon={<ShareAltOutlined />}
                onClick={() => window.open(record.dingtalk_url, '_blank')}
              />
            </Tooltip>
          )}
          <Tooltip title="重新同步">
            <Button 
              key="resync"
              type="link" 
              icon={<SyncOutlined />}
              onClick={() => {
                // 重新同步单个文档
                message.info('重新同步功能开发中...');
              }}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  // 展开所有文档为平面列表
  const flatDocuments = documents.reduce((acc, space) => {
    if (space.documents) {
      return acc.concat(space.documents.map(doc => ({
        ...doc,
        space_name: space.space_name || '未知知识库'
      })));
    }
    return acc;
  }, []);

  return (
    <div className="document-management">
      <Card title="钉钉文档集成管理" loading={loading}>
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          {/* 概览页面 */}
          <TabPane tab="概览" key="overview">
            <Row gutter={16} style={{ marginBottom: 24 }}>
              <Col span={6}>
                <Statistic
                  title="知识库总数"
                  value={statistics.totalSpaces}
                  prefix={<FolderOutlined />}
                />
              </Col>
              <Col span={6}>
                <Statistic
                  title="文档总数"
                  value={statistics.totalDocuments}
                  prefix={<FileTextOutlined />}
                />
              </Col>
              <Col span={6}>
                <Statistic
                  title="已同步文档"
                  value={statistics.syncedDocuments}
                  prefix={<SyncOutlined />}
                />
              </Col>
              <Col span={6}>
                <Statistic
                  title="同步率"
                  value={statistics.syncRate}
                  suffix="%"
                  prefix={<SyncOutlined />}
                />
              </Col>
            </Row>

            <Space style={{ marginBottom: 16 }}>
              <Button 
                type="primary" 
                icon={<SyncOutlined />}
                onClick={() => setSyncModalVisible(true)}
                loading={loading}
              >
                同步钉钉文档
              </Button>
              <Button 
                icon={<CloudUploadOutlined />}
                onClick={() => setUploadModalVisible(true)}
              >
                上传文档到钉钉
              </Button>
              <Button 
                type="primary"
                icon={<UploadOutlined />}
                onClick={() => setBatchUploadModalVisible(true)}
              >
                批量上传知识库
              </Button>
              <Button 
                icon={<SyncOutlined />}
                onClick={loadData}
                loading={loading}
              >
                刷新数据
              </Button>
            </Space>

            <Divider />

            <Row gutter={16}>
              <Col span={12}>
                <Card title="最近同步的知识库" size="small">
                  <Table
                    dataSource={spaces.slice(0, 5)}
                    columns={spaceColumns.slice(0, 3)}
                    pagination={false}
                    size="small"
                    rowKey={(record) => record.id || record.dingtalk_space_id}
                  />
                </Card>
              </Col>
              <Col span={12}>
                <Card title="最近同步的文档" size="small">
                  <Table
                    dataSource={flatDocuments.slice(0, 5)}
                    columns={documentColumns.slice(0, 3)}
                    pagination={false}
                    size="small"
                    rowKey={(record) => record.id || record.dingtalk_doc_id}
                  />
                </Card>
              </Col>
            </Row>
          </TabPane>

          {/* 知识库管理 */}
          <TabPane tab="知识库管理" key="spaces">
            <Space style={{ marginBottom: 16 }}>
              <Button 
                type="primary" 
                icon={<SyncOutlined />}
                onClick={() => setSyncModalVisible(true)}
                loading={loading}
              >
                同步知识库
              </Button>
            </Space>

            <Table
              dataSource={spaces}
              columns={spaceColumns}
              rowKey="id"
              pagination={{
                total: spaces.length,
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 个知识库`,
              }}
            />
          </TabPane>

          {/* 文档管理 */}
          <TabPane tab="文档管理" key="documents">
            <Space style={{ marginBottom: 16 }}>
              <Button 
                icon={<CloudUploadOutlined />}
                onClick={() => setUploadModalVisible(true)}
              >
                上传文档到钉钉
              </Button>
              <Button 
                icon={<DownloadOutlined />}
                onClick={() => {
                  message.info('批量下载功能开发中...');
                }}
              >
                批量下载
              </Button>
            </Space>

            <Table
              dataSource={flatDocuments}
              columns={[
                ...documentColumns.slice(0, 1),
                {
                  title: '所属知识库',
                  dataIndex: 'space_name',
                  key: 'space_name',
                  render: (name) => <Tag color="blue">{name}</Tag>,
                },
                ...documentColumns.slice(1)
              ]}
              rowKey="id"
              pagination={{
                total: flatDocuments.length,
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 个文档`,
              }}
            />
          </TabPane>
        </Tabs>
      </Card>

      {/* 同步文档模态框 */}
      <Modal
        title="同步钉钉文档"
        visible={syncModalVisible}
        onOk={() => syncForm.submit()}
        onCancel={() => setSyncModalVisible(false)}
        confirmLoading={loading}
      >
        <Form
          form={syncForm}
          layout="vertical"
          onFinish={handleSyncDocuments}
        >
          <Form.Item
            name="sync_type"
            label="同步类型"
            rules={[{ required: true, message: '请选择同步类型' }]}
            initialValue="full"
          >
            <Select>
              <Option key="sync-full" value="full">完整同步</Option>
              <Option key="sync-incremental" value="incremental">增量同步</Option>
              <Option key="sync-spaces-only" value="spaces_only">仅同步知识库</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="target_knowledge_base"
            label="目标知识库（可选）"
          >
            <Select 
              allowClear 
              placeholder="选择同步目标知识库，留空则同步到钉钉文档空间"
              loading={loadingKnowledgeBases}
            >
              <Option key="default-space" value="">钉钉文档空间</Option>
              {knowledgeBases.map((kb, index) => (
                <Option key={`sync-${kb.type}-${kb.id}-${index}`} value={`${kb.type}:${kb.id}`}>
                  <Space>
                    <Tag color={kb.type === 'project' ? 'blue' : 'green'}>
                      {kb.type === 'project' ? '项目' : 'AI'}
                    </Tag>
                    {kb.name || kb.title}
                  </Space>
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="space_id"
            label="指定钉钉知识库（可选）"
          >
            <Select allowClear placeholder="留空则同步所有钉钉知识库">
              {spaces.map((space, index) => (
                <Option key={`space-${space.dingtalk_space_id || space.id || index}`} value={space.dingtalk_space_id}>
                  {space.space_name}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="auto_sync"
            label="自动同步"
            initialValue={true}
          >
            <Select>
              <Option key="auto-sync-true" value={true}>开启自动同步</Option>
              <Option key="auto-sync-false" value={false}>关闭自动同步</Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>

      {/* 上传文档模态框 */}
      <Modal
        title="上传文档到钉钉"
        visible={uploadModalVisible}
        onOk={() => uploadForm.submit()}
        onCancel={() => setUploadModalVisible(false)}
        confirmLoading={loading}
        width={600}
      >
        <Form
          form={uploadForm}
          layout="vertical"
          onFinish={handleUploadToDigntalk}
        >
          <Form.Item
            name="space_id"
            label="目标知识库"
            rules={[{ required: true, message: '请选择目标知识库' }]}
          >
            <Select placeholder="选择要上传到的知识库">
              {spaces.map((space, index) => (
                <Option key={`upload-space-${space.dingtalk_space_id || space.id || index}`} value={space.dingtalk_space_id}>
                  {space.space_name}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="title"
            label="文档标题"
            rules={[{ required: true, message: '请输入文档标题' }]}
          >
            <Input placeholder="输入文档标题" />
          </Form.Item>

          <Form.Item
            name="content"
            label="文档内容"
            rules={[{ required: true, message: '请输入文档内容' }]}
          >
            <TextArea
              rows={10}
              placeholder="输入文档内容（支持 Markdown 格式）"
            />
          </Form.Item>

          <Form.Item
            name="content_format"
            label="内容格式"
            initialValue="markdown"
          >
            <Select>
              <Option key="format-markdown" value="markdown">Markdown</Option>
              <Option key="format-html" value="html">HTML</Option>
              <Option key="format-plain" value="plain">纯文本</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="tags"
            label="标签（可选）"
          >
            <Input placeholder="输入标签，用逗号分隔" />
          </Form.Item>
        </Form>
      </Modal>

      {/* 批量上传知识库模态框 */}
      <Modal
        title="批量上传知识库到钉钉"
        visible={batchUploadModalVisible}
        onOk={() => batchUploadForm.submit()}
        onCancel={() => setBatchUploadModalVisible(false)}
        confirmLoading={loading}
        width={700}
      >
        <Form
          form={batchUploadForm}
          layout="vertical"
          onFinish={handleBatchUploadToDigntalk}
        >
          <Form.Item
            name="knowledge_base_ids"
            label="选择知识库"
            rules={[{ required: true, message: '请选择要上传的知识库' }]}
          >
            <Select
              mode="multiple"
              placeholder="选择要上传的知识库"
              loading={loadingKnowledgeBases}
              style={{ width: '100%' }}
            >
              {knowledgeBases.map((kb, index) => (
                <Option key={`batch-kb-${kb.type}-${kb.id || kb.name || index}`} value={`${kb.type}:${kb.id}`}>
                  <Space>
                    <Tag color={kb.type === 'project' ? 'blue' : 'green'}>
                      {kb.type === 'project' ? '项目' : 'AI'}
                    </Tag>
                    {kb.name || kb.title}
                  </Space>
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="target_space_id"
            label="目标钉钉知识库"
            rules={[{ required: true, message: '请选择目标钉钉知识库' }]}
          >
            <Select placeholder="选择上传到的钉钉知识库">
              {spaces.map((space, index) => (
                <Option key={`batch-target-${space.dingtalk_space_id || space.id || index}`} value={space.dingtalk_space_id}>
                  {space.space_name}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="upload_mode"
            label="上传模式"
            initialValue="incremental"
          >
            <Select>
              <Option key="upload-full" value="full">完整上传（覆盖已有文档）</Option>
              <Option key="upload-incremental" value="incremental">增量上传（仅上传新文档）</Option>
              <Option key="upload-update-only" value="update_only">仅更新已存在的文档</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="include_metadata"
            label="包含元数据"
            initialValue={true}
          >
            <Select>
              <Option key="metadata-true" value={true}>包含标签和分类信息</Option>
              <Option key="metadata-false" value={false}>仅上传文档内容</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="auto_sync"
            label="自动同步"
            initialValue={false}
          >
            <Select>
              <Option key="batch-auto-sync-true" value={true}>开启后续自动同步</Option>
              <Option key="batch-auto-sync-false" value={false}>仅本次上传</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="description"
            label="批量上传说明（可选）"
          >
            <TextArea
              rows={3}
              placeholder="输入本次批量上传的说明信息"
            />
          </Form.Item>
        </Form>
      </Modal>

      {/* 查看文档模态框 */}
      <Modal
        title="文档详情"
        visible={viewModalVisible}
        onCancel={() => setViewModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setViewModalVisible(false)}>
            关闭
          </Button>,
          selectedDocument && selectedDocument.dingtalk_url && (
            <Button 
              key="open-dingtalk" 
              type="primary"
              icon={<ShareAltOutlined />}
              onClick={() => window.open(selectedDocument.dingtalk_url, '_blank')}
            >
              在钉钉中打开
            </Button>
          )
        ].filter(Boolean)}
        width={900}
      >
        {selectedDocument && (
          <div>
            <div style={{ marginBottom: 16 }}>
              <Space direction="vertical" style={{ width: '100%' }}>
                <h3>{selectedDocument.title || selectedDocument.space_name}</h3>
                <Space wrap>
                  <Tag color="blue">
                    {getDocumentTypeIcon(selectedDocument.document_type, selectedDocument.node_type)}
                  </Tag>
                  <Tag color={getSyncStatusColor(selectedDocument.sync_status)}>
                    {getSyncStatusText(selectedDocument.sync_status)}
                  </Tag>
                  {selectedDocument.storage_strategy === 'link_only' && (
                    <Tag color="orange" icon="🔗">
                      仅链接访问
                    </Tag>
                  )}
                  {selectedDocument.file_size > 0 && (
                    <Tag color="default">
                      {formatFileSize(selectedDocument.file_size)}
                    </Tag>
                  )}
                </Space>
              </Space>
            </div>
            
            {selectedDocument.dingtalk_url && (
              <div style={{ marginBottom: 16, padding: 12, backgroundColor: '#f0f8ff', borderRadius: 4 }}>
                <h4>🔗 钉钉访问链接</h4>
                <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                  <Input 
                    value={selectedDocument.dingtalk_url}
                    readOnly
                    style={{ flex: 1 }}
                    addonAfter={
                      <Button 
                        type="link" 
                        size="small"
                        onClick={() => {
                          navigator.clipboard.writeText(selectedDocument.dingtalk_url);
                          message.success('链接已复制到剪贴板');
                        }}
                      >
                        复制
                      </Button>
                    }
                  />
                  <Button 
                    type="primary" 
                    icon={<ShareAltOutlined />}
                    onClick={() => window.open(selectedDocument.dingtalk_url, '_blank')}
                  >
                    打开
                  </Button>
                </div>
                <div style={{ marginTop: 8, fontSize: '12px', color: '#666' }}>
                  💡 钉钉文档需要在钉钉环境中查看完整内容，点击"打开"按钮访问原文档
                </div>
              </div>
            )}
            
            {selectedDocument.content && (
              <div style={{ marginBottom: 16 }}>
                <h4>📄 文档预览</h4>
                <div style={{ 
                  backgroundColor: '#f5f5f5', 
                  padding: 16, 
                  borderRadius: 4,
                  maxHeight: 400,
                  overflow: 'auto',
                  whiteSpace: 'pre-wrap',
                  fontSize: '14px',
                  lineHeight: '1.6'
                }}>
                  {selectedDocument.content}
                </div>
                {selectedDocument.storage_strategy === 'link_only' && (
                  <div style={{ marginTop: 8, fontSize: '12px', color: '#666' }}>
                    ⚠️ 这是预览内容，完整文档请通过上方钉钉链接访问
                  </div>
                )}
              </div>
            )}
            
            {selectedDocument.space_description && (
              <div style={{ marginBottom: 16 }}>
                <h4>📝 描述</h4>
                <p style={{ backgroundColor: '#fafafa', padding: 8, borderRadius: 4 }}>
                  {selectedDocument.space_description}
                </p>
              </div>
            )}
            
            <div style={{ marginTop: 16 }}>
              <h4>ℹ️ 同步信息</h4>
              <Row gutter={16}>
                <Col span={12}>
                  <div style={{ marginBottom: 8 }}>
                    <strong>最后同步:</strong> {selectedDocument.updated_at ? new Date(selectedDocument.updated_at).toLocaleString() : '未同步'}
                  </div>
                  <div style={{ marginBottom: 8 }}>
                    <strong>创建时间:</strong> {selectedDocument.created_at ? new Date(selectedDocument.created_at).toLocaleString() : '-'}
                  </div>
                </Col>
                <Col span={12}>
                  <div style={{ marginBottom: 8 }}>
                    <strong>同步方向:</strong> {selectedDocument.sync_direction === 'from_dingtalk' ? '从钉钉' : selectedDocument.sync_direction || '-'}
                  </div>
                  <div style={{ marginBottom: 8 }}>
                    <strong>存储策略:</strong> {selectedDocument.storage_strategy === 'link_only' ? '仅链接' : selectedDocument.storage_strategy || '-'}
                  </div>
                </Col>
              </Row>
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default DocumentManagement; 