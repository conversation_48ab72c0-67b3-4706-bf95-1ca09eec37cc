from typing import Dict, Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query, Path
from sqlalchemy.orm import Session
from uuid import UUID
from sqlalchemy import select

from db.database import get_db
from core.auth import get_current_user
from api.deps import get_current_project
from services.warehouse import WarehouseService, WarehouseSettingService
from schemas.warehouse import (
    Warehouse, WarehouseCreate, WarehouseUpdate, WarehouseList, WarehouseDetail,
    WarehouseSetting, WarehouseSettingCreate, WarehouseSettingUpdate
)
from models.project import Project

router = APIRouter()

# 获取仓库管理员用户列表（放在/{warehouse_id}之前避免路由冲突）
@router.get("/managers", response_model=List[Dict[str, Any]])
async def get_warehouse_managers(
    project: Project = Depends(get_current_project),
    db: Session = Depends(get_db)
):
    """获取具有仓库管理员角色的用户列表"""
    from models.project_user import ProjectUser
    from models.user import User
    from models.role import Role
    from sqlalchemy.orm import selectinload
    
    # 查找仓库管理员角色
    result = await db.execute(
        select(Role).where(
            Role.project_id == project.id,
            Role.code == "inventory_manager"
        )
    )
    warehouse_manager_role = result.scalars().first()
    
    if not warehouse_manager_role:
        # 如果没有找到仓库管理员角色，返回空列表
        return []
    
    # 查找具有仓库管理员角色的项目用户
    result = await db.execute(
        select(ProjectUser)
        .options(selectinload(ProjectUser.user))
        .where(
            ProjectUser.project_id == project.id,
            ProjectUser.role_id == warehouse_manager_role.id,
            ProjectUser.status == "active"
        )
    )
    project_users = result.scalars().all()
    
    # 构造用户列表
    managers = []
    for project_user in project_users:
        if project_user.user:
            managers.append({
                "project_user_id": str(project_user.id),
                "user_id": str(project_user.user.id),
                "username": project_user.user.username,
                "full_name": project_user.user.full_name,
                "email": project_user.user.email,
                "display_name": project_user.user.full_name or project_user.user.username
            })
    
    return managers

# 仓库相关路由
@router.get("", response_model=WarehouseList)
async def get_warehouses(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    warehouse_type: Optional[str] = None,
    status: Optional[str] = None,
    project: Project = Depends(get_current_project),
    db: Session = Depends(get_db)
):
    """获取仓库列表"""
    warehouses = await WarehouseService.get_warehouses(
        db, project.id, skip, limit, warehouse_type, status
    )
    total = await WarehouseService.count_warehouses(
        db, project.id, warehouse_type, status
    )
    return {
        "items": warehouses,
        "total": total,
        "page": skip // limit + 1,
        "size": limit,
        "pages": (total + limit - 1) // limit
    }

@router.get("/{warehouse_id}", response_model=WarehouseDetail)
async def get_warehouse(
    warehouse_id: UUID = Path(...),
    project: Project = Depends(get_current_project),
    db: Session = Depends(get_db)
):
    """获取仓库详情"""
    from services.warehouse import ProjectUserWarehouseService
    
    warehouse = await WarehouseService.get_warehouse_by_id(db, warehouse_id)
    if not warehouse or warehouse.project_id != project.id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="仓库不存在"
        )
    
    # 获取管理员信息
    manager_project_user_id = await ProjectUserWarehouseService.get_manager_project_user_id(db, warehouse_id)
    manager_user = await ProjectUserWarehouseService.get_manager_user(db, warehouse_id)
    
    # 构造响应字典
    warehouse_data = warehouse.__dict__.copy()
    warehouse_data['manager_user_id'] = manager_project_user_id
    
    # 更新manager字段为真实姓名
    if manager_user:
        warehouse_data['manager'] = manager_user.full_name or manager_user.username
    
    return WarehouseDetail.parse_obj(warehouse_data)

@router.post("", response_model=Warehouse, status_code=status.HTTP_201_CREATED)
async def create_warehouse(
    warehouse: WarehouseCreate,
    project: Project = Depends(get_current_project),
    db: Session = Depends(get_db)
):
    """创建仓库"""
    from services.warehouse import ProjectUserWarehouseService
    from models.project_user import ProjectUser
    
    # 检查仓库编码是否已存在
    existing_warehouse = await WarehouseService.get_warehouse_by_code(db, project.id, warehouse.code)
    if existing_warehouse:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="仓库编码已存在"
        )
    
    # 确保项目ID正确
    warehouse.project_id = project.id
    
    # 创建仓库
    db_warehouse = await WarehouseService.create_warehouse(db, warehouse)
    
    # 如果提供了manager_user_id，则设置仓库管理员
    if warehouse.manager_user_id:
        from services.warehouse import ProjectUserWarehouseService
        from models.project_user import ProjectUser
        # 首先尝试作为project_user_id查找
        result = await db.execute(
            select(ProjectUser).where(
                ProjectUser.id == warehouse.manager_user_id,
                ProjectUser.project_id == project.id
            )
        )
        project_user = result.scalars().first()
        
        # 如果没找到，再尝试作为user_id查找
        if not project_user:
            result = await db.execute(
                select(ProjectUser).where(
                    ProjectUser.user_id == warehouse.manager_user_id,
                    ProjectUser.project_id == project.id
                )
            )
            project_user = result.scalars().first()
        
        if not project_user:
            raise HTTPException(status_code=400, detail="仓库管理员用户未加入该项目")
        
        # 设置为仓库管理员
        await ProjectUserWarehouseService.set_manager(db, db_warehouse.id, project_user.id)
        
        # 更新仓库的manager字段为用户姓名
        if project_user.user:
            manager_name = project_user.user.full_name or project_user.user.username
            db_warehouse.manager = manager_name
            db.add(db_warehouse)
            await db.commit()
            await db.refresh(db_warehouse)
    
    return db_warehouse

@router.put("/{warehouse_id}", response_model=Warehouse)
async def update_warehouse(
    warehouse_update: WarehouseUpdate,
    warehouse_id: UUID = Path(...),
    project: Project = Depends(get_current_project),
    db: Session = Depends(get_db)
):
    """更新仓库"""
    # 检查仓库是否存在
    warehouse = await WarehouseService.get_warehouse_by_id(db, warehouse_id)
    if not warehouse or warehouse.project_id != project.id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="仓库不存在"
        )
    
    # 检查仓库编码是否已存在
    if warehouse_update.code != warehouse.code:
        existing_warehouse = await WarehouseService.get_warehouse_by_code(db, project.id, warehouse_update.code)
        if existing_warehouse:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="仓库编码已存在"
            )
    
    updated_warehouse = await WarehouseService.update_warehouse(db, warehouse_id, warehouse_update)
    if not updated_warehouse:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新仓库失败"
        )
    return updated_warehouse

@router.delete("/{warehouse_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_warehouse(
    warehouse_id: UUID = Path(...),
    project: Project = Depends(get_current_project),
    db: Session = Depends(get_db)
):
    """删除仓库"""
    # 检查仓库是否存在
    warehouse = await WarehouseService.get_warehouse_by_id(db, warehouse_id)
    if not warehouse or warehouse.project_id != project.id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="仓库不存在"
        )
    
    success = await WarehouseService.delete_warehouse(db, warehouse_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="删除仓库失败"
        )
    return None

# 仓库设置相关路由
@router.get("/warehouse-settings", response_model=WarehouseSetting)
async def get_warehouse_settings(
    project: Project = Depends(get_current_project),
    db: Session = Depends(get_db)
):
    """获取仓库设置"""
    settings = await WarehouseSettingService.get_or_create_setting(db, project.id)
    return settings

@router.post("/warehouse-settings", response_model=WarehouseSetting)
async def create_warehouse_settings(
    settings: WarehouseSettingCreate,
    project: Project = Depends(get_current_project),
    db: Session = Depends(get_db)
):
    """创建仓库设置"""
    # 检查是否已存在设置
    existing_settings = await WarehouseSettingService.get_setting_by_project_id(db, project.id)
    if existing_settings:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="仓库设置已存在，请使用PUT方法更新"
        )
    
    # 确保项目ID正确
    settings.project_id = project.id
    
    return await WarehouseSettingService.create_setting(db, settings)

@router.put("/warehouse-settings", response_model=WarehouseSetting)
async def update_warehouse_settings(
    settings_update: WarehouseSettingUpdate,
    project: Project = Depends(get_current_project),
    db: Session = Depends(get_db)
):
    """更新仓库设置"""
    updated_settings = await WarehouseSettingService.update_setting(db, project.id, settings_update)
    if not updated_settings:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新仓库设置失败"
        )
    return updated_settings
