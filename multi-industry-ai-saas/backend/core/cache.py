#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Redis缓存客户端配置
"""

import logging
from typing import Optional
import redis.asyncio as redis
from core.config import settings

logger = logging.getLogger(__name__)

# 全局Redis客户端
_redis_client: Optional[redis.Redis] = None

async def get_redis_client() -> redis.Redis:
    """获取Redis客户端"""
    global _redis_client
    
    if _redis_client is None:
        try:
            # 创建Redis连接
            _redis_client = redis.from_url(
                settings.REDIS_URL,
                decode_responses=True,
                retry_on_timeout=True,
                socket_keepalive=True,
                socket_keepalive_options={},
                health_check_interval=30
            )
            
            # 测试连接
            await _redis_client.ping()
            logger.info("Redis客户端连接成功")
            
        except Exception as e:
            logger.error(f"Redis连接失败: {e}")
            # 返回一个模拟的Redis客户端用于开发环境
            _redis_client = MockRedisClient()
            
    return _redis_client

async def close_redis_client():
    """关闭Redis客户端"""
    global _redis_client
    if _redis_client and hasattr(_redis_client, 'close'):
        await _redis_client.close()
        _redis_client = None
        logger.info("Redis客户端已关闭")

class MockRedisClient:
    """模拟Redis客户端，用于开发环境"""
    
    def __init__(self):
        self._data = {}
        logger.warning("使用模拟Redis客户端")
    
    async def ping(self):
        """模拟ping"""
        return True
    
    async def get(self, key: str) -> Optional[str]:
        """模拟get"""
        return self._data.get(key)
    
    async def set(self, key: str, value: str, ex: Optional[int] = None) -> bool:
        """模拟set"""
        self._data[key] = value
        return True
    
    async def delete(self, *keys) -> int:
        """模拟delete"""
        count = 0
        for key in keys:
            if key in self._data:
                del self._data[key]
                count += 1
        return count
    
    async def exists(self, *keys) -> int:
        """模拟exists"""
        count = 0
        for key in keys:
            if key in self._data:
                count += 1
        return count
    
    async def expire(self, key: str, time: int) -> bool:
        """模拟expire"""
        return True
    
    async def close(self):
        """模拟close"""
        self._data.clear()
        logger.info("模拟Redis客户端已关闭") 