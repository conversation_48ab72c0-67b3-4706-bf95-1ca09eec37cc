from sqlalchemy import Column, String, Boolean, DateTime, ForeignKey, JSON, Text, Integer, UniqueConstraint
from sqlalchemy.dialects.postgresql import UUID, ARRAY
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid

from db.database import Base

class NotificationSettings(Base):
    """通知设置模型"""
    __tablename__ = "notification_settings"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=True)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id"), nullable=False)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    
    # 普通通知设置
    normal_frequency = Column(String(20), default="realtime", nullable=False)  # realtime, hourly, daily
    
    # 高优先级通知设置
    high_priority_frequency = Column(String(20), default="realtime", nullable=False)  # realtime, hourly, daily
    
    # 通知方式设置
    email_notifications = Column(Boolean, default=True, nullable=False)
    push_notifications = Column(Boolean, default=True, nullable=False)
    sms_notifications = Column(Boolean, default=False, nullable=False)
    sound_enabled = Column(Boolean, default=True, nullable=False)
    
    # 通知类型设置
    system_notifications = Column(Boolean, default=True, nullable=False)
    task_notifications = Column(Boolean, default=True, nullable=False)
    inventory_notifications = Column(Boolean, default=True, nullable=False)
    order_notifications = Column(Boolean, default=True, nullable=False)
    security_notifications = Column(Boolean, default=True, nullable=False)
    
    # 工作流通知设置
    workflow_notifications = Column(JSON, default=dict, nullable=False)
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 关系
    tenant = relationship("Tenant", backref="notification_settings")
    project = relationship("Project", backref="notification_settings")
    user = relationship("User", backref="notification_settings")

    __table_args__ = (
        UniqueConstraint('project_id', 'user_id', name='uq_notification_settings_per_user_project'),
    )

    def __repr__(self):
        return f"<NotificationSettings(id={self.id}, user_id={self.user_id}, project_id={self.project_id})>"
