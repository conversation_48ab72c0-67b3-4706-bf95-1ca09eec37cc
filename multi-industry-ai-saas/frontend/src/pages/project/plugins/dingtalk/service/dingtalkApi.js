import api from '../../../../../services/api/httpClient';

const BASE_URL = '/api/v1/plugins/dingtalk';

// 获取项目ID的辅助函数
const getProjectId = () => localStorage.getItem('project_id');

export const dingtalkApi = {
  // ==================== 基础设置 ====================
  
  /**
   * 获取钉钉插件设置
   */
  getSettings: () => {
    const projectId = getProjectId();
    return api.get(`/project/${projectId}/plugin/dingtalk/settings`);
  },
  
  /**
   * 更新钉钉插件设置
   */
  updateSettings: (settings) => {
    const projectId = getProjectId();
    return api.post(`/project/${projectId}/plugin/dingtalk/settings`, settings);
  },

  // ==================== 统一Webhook管理 ====================
  
  /**
   * 获取Webhook列表（包括机器人和事件订阅）
   */
  getWebhooks: () => {
    const projectId = getProjectId();
    return api.get(`/project/${projectId}/plugin/dingtalk/webhooks/subscriptions`);
  },
  
  /**
   * 创建Webhook（机器人或事件订阅）
   */
  createWebhook: (data) => {
    const projectId = getProjectId();
    return api.post(`project/${projectId}/plugin/dingtalk/webhooks/subscriptions`, data);
  },
  
  /**
   * 更新Webhook
   */
  updateWebhook: (id, data) => {
    const projectId = getProjectId();
    return api.put(`/project/${projectId}/plugin/dingtalk/webhooks/subscriptions/${id}`, data);
  },
  
  /**
   * 删除Webhook
   */
  deleteWebhook: (id) => {
    const projectId = getProjectId();
    return api.delete(`/project/${projectId}/plugin/dingtalk/webhooks/subscriptions/${id}`);
  },

  /**
   * 测试Webhook
   */
  testWebhook: (testData) => {
    const projectId = getProjectId();
    return api.post(`/project/${projectId}/plugin/dingtalk/test`, testData);
  },


  
  /**
   * 获取事件日志
   */
  getEventLogs: (params = {}) => {
    const projectId = getProjectId();
    return api.get(`/project/${projectId}/plugin/dingtalk/webhooks/events`, { params });
  },
  
  /**
   * 获取事件统计
   */
  getEventStatistics: () => {
    const projectId = getProjectId();
    return api.get(`/project/${projectId}/plugin/dingtalk/webhooks/statistics`);
  },

  // ==================== 组织架构管理 ====================
  
  /**
   * 获取组织架构状态
   */
  getOrganizationStatus: () => {
    const projectId = getProjectId();
    return api.get(`/project/${projectId}/plugin/dingtalk/admin/organization-status`);
  },
  
  /**
   * 增强组织架构同步（支持全量、增量、按部门同步等多种模式）
   */
  syncOrganization: (syncOptions = {}) => {
    const projectId = getProjectId();
    return api.post(`/project/${projectId}/plugin/dingtalk/admin/sync-organization`, syncOptions);
  },

  /**
   * 获取组织架构树（包含用户信息）
   */
  getOrganizationTree: () => {
    const projectId = getProjectId();
    return api.get(`/project/${projectId}/plugin/dingtalk/admin/organization-tree`);
  },

  /**
   * 一键创建项目用户（从钉钉用户）
   */
  createProjectUsers: (userData) => {
    const projectId = getProjectId();
    return api.post(`/project/${projectId}/plugin/dingtalk/admin/create-project-users`, userData);
  },

  /**
   * 获取项目角色列表
   */
  getProjectRoles: () => {
    const projectId = getProjectId();
    return api.get(`/project/${projectId}/plugin/dingtalk/admin/roles`);
  },

  /**
   * 获取项目门店列表
   */
  getProjectStores: () => {
    const projectId = getProjectId();
    return api.get(`/project/${projectId}/plugin/dingtalk/admin/stores`);
  },

  // ==================== 用户管理 ====================
  
  /**
   * 获取用户列表
   */
  getUserList: (params = {}) => {
    const projectId = getProjectId();
    return api.get(`/project/${projectId}/plugin/dingtalk/admin/users`, { params });
  },

  /**
   * 获取当前用户的钉钉绑定和插件映射信息
   */
  getUserInfo: () => {
    const projectId = getProjectId();
    return api.get(`/project/${projectId}/plugin/dingtalk/admin/users/current/details`);
  },

  /**
   * 从平台主绑定关系同步当前用户的钉钉映射
   */
  syncUserMappingFromPlatform: () => {
    const projectId = getProjectId();
    return api.post(`/project/${projectId}/plugin/dingtalk/admin/users/sync-mapping`);
  },

  /**
   * 清除当前用户的钉钉插件映射信息
   */
  clearUserMapping: () => {
    const projectId = getProjectId();
    return api.post(`/project/${projectId}/plugin/dingtalk/admin/users/clear-mapping`);
  },

  /**
   * 获取用户钉钉绑定信息（简化版，快速刷新用）
   * 注意：此接口现在与getUserInfo完全相同，保留它是为了向后兼容，建议未来统一
   */
  getUserInfoSimple: () => {
    const projectId = getProjectId();
    return api.get(`/project/${projectId}/plugin/dingtalk/admin/users/current/details`);
  },

  // ==================== 文档管理 ====================
  
  /**
   * 获取钉钉知识库列表
   */
  getDingtalkSpaces: () => {
    const projectId = getProjectId();
    return api.get(`/project/${projectId}/plugin/dingtalk/admin/spaces`);
  },
  
  /**
   * 同步文档
   */
  syncDocuments: (data) => {
    const projectId = getProjectId();
    return api.post(`/project/${projectId}/plugin/dingtalk/admin/sync-documents`, data);
  },

  /**
   * 增强文档同步（支持深度递归获取、存储策略等）
   */
  syncDingtalkDocuments: (syncOptions = {}) => {
    const projectId = getProjectId();
    return api.post(`/project/${projectId}/plugin/dingtalk/admin/sync-dingtalk-documents`, syncOptions);
  },

  /**
   * 获取项目文档列表
   */
  getProjectDocuments: (params = {}) => {
    const projectId = getProjectId();
    return api.get(`/project/${projectId}/plugin/dingtalk/admin/documents`, { params });
  },

  /**
   * 上传文档到钉钉
   */
  uploadDocumentToDingtalk: (documentData) => {
    const projectId = getProjectId();
    // 处理标签字符串
    if (documentData.tags && typeof documentData.tags === 'string') {
      documentData.tags = documentData.tags.split(',').map(tag => tag.trim()).filter(tag => tag);
    }
    return api.post(`/project/${projectId}/plugin/dingtalk/admin/documents/sync-to-dingtalk`, documentData);
  },

  /**
   * 批量上传知识库到钉钉
   */
  uploadKnowledgeBasesToDingtalk: (uploadData) => {
    const projectId = getProjectId();
    return api.post(`/project/${projectId}/plugin/dingtalk/admin/upload-to-dingtalk`, uploadData);
  },

  // ==================== 审批模板管理 ====================
  
  /**
   * 获取审批模板列表
   */
  getApprovalTemplates: () => {
    const projectId = getProjectId();
    return api.get(`/project/${projectId}/plugin/dingtalk/approval/templates/list`);
  },
  
  /**
   * 创建审批模板
   */
  createApprovalTemplate: (data) => {
    const projectId = getProjectId();
    return api.post(`/project/${projectId}/plugin/dingtalk/approval/templates`, data);
  },
  
  /**
   * 更新审批模板
   */
  updateApprovalTemplate: (id, data) => {
    const projectId = getProjectId();
    return api.put(`/project/${projectId}/plugin/dingtalk/approval/templates/${id}`, data);
  },
  
  /**
   * 删除审批模板
   */
  deleteApprovalTemplate: (id) => {
    const projectId = getProjectId();
    return api.delete(`/project/${projectId}/plugin/dingtalk/approval/templates/${id}`);
  },

  // ==================== 审批实例管理 ====================
  
  /**
   * 获取审批实例列表
   */
  getApprovalInstances: (params = {}) => {
    const projectId = getProjectId();
    return api.get(`/project/${projectId}/plugin/dingtalk/approval/instances/list`, { params });
  },
  
  /**
   * 创建审批实例
   */
  createApprovalInstance: (data) => {
    const projectId = getProjectId();
    return api.post(`/project/${projectId}/plugin/dingtalk/approval/instances`, data);
  },

  /**
   * 获取审批实例详情
   */
  getApprovalInstanceDetail: (instanceId) => {
    const projectId = getProjectId();
    return api.get(`/project/${projectId}/plugin/dingtalk/approval/instances/${instanceId}`);
  },

  /**
   * 撤销审批实例
   */
  revokeApprovalInstance: (instanceId) => {
    const projectId = getProjectId();
    return api.post(`/project/${projectId}/plugin/dingtalk/approval/instances/${instanceId}/revoke`);
  },

  /**
   * 审批实例
   */
  approveInstance: (instanceId, approvalData) => {
    const projectId = getProjectId();
    return api.post(`/project/${projectId}/plugin/dingtalk/approval/instances/${instanceId}/approve`, approvalData);
  },

  /**
   * 同步审批状态
   */
  syncApprovalStatus: () => {
    const projectId = getProjectId();
    return api.post(`/project/${projectId}/plugin/dingtalk/approval/sync-status`);
  },

  /**
   * 同步审批实例
   */
  syncApprovalInstances: () => {
    const projectId = getProjectId();
    return api.post(`/project/${projectId}/plugin/dingtalk/approval/sync-instances`);
  },

  /**
   * 获取审批统计
   */
  getApprovalStatistics: () => {
    const projectId = getProjectId();
    return api.get(`/project/${projectId}/plugin/dingtalk/approval/statistics`);
  },

  /**
   * 获取业务统计
   */
  getBusinessStats: () => {
    const projectId = getProjectId();
    return api.get(`/project/${projectId}/plugin/dingtalk/approval/business-stats`);
  },

  /**
   * 发现项目中待审批的业务数据
   */
  discoverPendingBusinessApprovals: () => {
    const projectId = getProjectId();
    return api.post(`/project/${projectId}/plugin/dingtalk/approval/discover-business`);
  },

  /**
   * 自动同步发现的待审批业务到钉钉
   */
  autoSyncDiscoveredApprovals: (syncOptions = {}) => {
    const projectId = getProjectId();
    return api.post(`/project/${projectId}/plugin/dingtalk/approval/auto-sync`, syncOptions);
  },

  /**
   * 同步指定的业务数据到钉钉审批
   */
  syncSpecificBusinessApproval: (businessData) => {
    const projectId = getProjectId();
    return api.post(`/project/${projectId}/plugin/dingtalk/approval/sync-business`, businessData);
  },

  // ==================== 待办任务管理 ====================
  
  /**
   * 获取待办任务列表
   */
  getTodoTasks: (params = {}) => {
    const projectId = getProjectId();
    return api.get(`/project/${projectId}/plugin/dingtalk/admin/todo-tasks`, { params });
  },
  
  /**
   * 同步待办任务
   */
  syncTodoTasks: (syncData = {}) => {
    const projectId = getProjectId();
    return api.post(`/project/${projectId}/plugin/dingtalk/admin/todo-tasks/sync`, syncData);
  },

  /**
   * 创建待办任务
   */
  createTodoTask: (taskData) => {
    const projectId = getProjectId();
    return api.post(`/project/${projectId}/plugin/dingtalk/admin/todo-tasks/create`, taskData);
  },

  /**
   * 批量创建待办任务
   */
  batchCreateTodoTasks: (tasksData) => {
    const projectId = getProjectId();
    return api.post(`/project/${projectId}/plugin/dingtalk/admin/todo-tasks/batch-create`, tasksData);
  },

  /**
   * 更新待办任务
   */
  updateTodoTask: (taskId, taskData) => {
    const projectId = getProjectId();
    return api.put(`/project/${projectId}/plugin/dingtalk/admin/todo-tasks/${taskId}`, taskData);
  },

  /**
   * 完成待办任务
   */
  completeTodoTask: (taskId) => {
    const projectId = getProjectId();
    return api.post(`/project/${projectId}/plugin/dingtalk/admin/todo-tasks/${taskId}/complete`);
  },

  /**
   * 删除待办任务
   */
  deleteTodoTask: (taskId) => {
    const projectId = getProjectId();
    return api.delete(`/project/${projectId}/plugin/dingtalk/admin/todo-tasks/${taskId}`);
  },

  // ==================== AI聊天 ====================
  
  /**
   * AI聊天对话
   */
  sendChatMessage: (chatData) => {
    const projectId = getProjectId();
    return api.post(`/project/${projectId}/plugin/dingtalk/ai/chat-json`, chatData);
  },


  
  /**
   * 获取聊天历史
   */
  getChatHistory: (params = {}) => {
    const projectId = getProjectId();
    return api.get(`/project/${projectId}/plugin/dingtalk/ai/history`, { params });
  },

  /**
   * AI聊天对话 - 支持文件上传
   */
  aiChatWithFiles: (formData) => {
    const projectId = getProjectId();
    return api.post(`/project/${projectId}/plugin/dingtalk/ai/chat`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
  },

  /**
   * 发送AI回复到钉钉
   */
  sendAIResponseToDingtalk: (sendData) => {
    const projectId = getProjectId();
    return api.post(`/project/${projectId}/plugin/dingtalk/ai/send-to-dingtalk`, sendData);
  },

  /**
   * 获取AI助手列表
   */
  getAIAssistants: () => {
    const projectId = getProjectId();
    return api.get(`/project/${projectId}/plugin/dingtalk/ai/assistants`);
  },

  /**
   * 获取对话线程列表
   */
  getAIThreads: (assistantId = null) => {
    const projectId = getProjectId();
    const params = assistantId ? { assistant_id: assistantId } : {};
    return api.get(`/project/${projectId}/plugin/dingtalk/ai/threads`, { params });
  },

  /**
   * 同步钉钉消息
   */
  syncDingtalkMessages: (syncData) => {
    const projectId = getProjectId();
    return api.post(`/project/${projectId}/plugin/dingtalk/ai/sync-messages`, syncData);
  },

  // ==================== 插件配置 ====================
  
  /**
   * 获取插件配置
   */
  getPluginConfig: () => 
    api.get(`${BASE_URL}/admin/config`),
  
  /**
   * 更新插件配置
   */
  updatePluginConfig: (data) => 
    api.put(`${BASE_URL}/admin/config`, data),

  // ==================== 认证相关 ====================
  
  /**
   * 获取认证URL
   */
  getAuthUrl: () => 
    api.get(`${BASE_URL}/auth/url`),
  
  /**
   * 处理认证回调
   */
  handleAuthCallback: (params) => 
    api.post(`${BASE_URL}/auth/callback`, params),

  // ==================== 统计信息 ====================
  
  /**
   * 获取统计信息
   */
  getStatistics: () => 
    api.get(`${BASE_URL}/admin/statistics`),

  // ==================== 通用功能 ====================

  /**
   * 发送钉钉消息
   */
  sendMessage: (messageData) => {
    const projectId = getProjectId();
    return api.post(`/project/${projectId}/plugin/dingtalk/send`, messageData);
  },

  /**
   * 获取通知日志
   */
  getLogs: (params = {}) => {
    const projectId = getProjectId();
    return api.get(`/project/${projectId}/plugin/dingtalk/logs`, { params });
  },

  /**
   * 同步钉钉数据
   */
  syncDingtalk: (syncData) => {
    const projectId = getProjectId();
    return api.post(`/project/${projectId}/plugin/dingtalk/sync`, syncData);
  },

  /**
   * 获取知识库列表
   */
  getKnowledgeBases: () => {
    const projectId = getProjectId();
    return api.get(`/project/${projectId}/knowledge-base/categories`);
  },

  // 重发用户创建通知
  resendNotification(data) {
    const projectId = getProjectId();
    return api.post(`/project/${projectId}/plugin/dingtalk/admin/users/resend-notification`, data);
  },
};

export default dingtalkApi; 