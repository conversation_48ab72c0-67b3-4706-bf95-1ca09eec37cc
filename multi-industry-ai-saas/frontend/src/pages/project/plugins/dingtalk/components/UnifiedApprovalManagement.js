import React, { useState, useEffect } from 'react';
import {
  Card, Table, Button, Space, Form, Input, Modal, message, Tag, 
  Select, DatePicker, Row, Col, Tabs, Statistic, Alert, Badge,
  InputNumber, Upload, Tooltip, Popconfirm, Empty, Typography,
  Descriptions, Steps, List, Timeline, Collapse, Drawer, Divider,
  Switch, Radio, Checkbox, TreeSelect, Transfer
} from 'antd';
import {
  PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined,
  DingdingOutlined, ShopOutlined, FileTextOutlined, MoneyCollectOutlined,
  BarChartOutlined, CheckCircleOutlined, ClockCircleOutlined,
  ExclamationCircleOutlined, CloseCircleOutlined, UploadOutlined,
  SyncOutlined, BellOutlined, SettingOutlined, AuditOutlined,
  ToolOutlined, BankOutlined, ShoppingCartOutlined, CalendarOutlined,
  FilterOutlined, DollarOutlined, TeamOutlined, ExportOutlined,
  DragOutlined, CopyOutlined, CodeOutlined, EyeInvisibleOutlined,
  QuestionCircleOutlined
} from '@ant-design/icons';
import dayjs from 'dayjs';
import dingtalkApi from '../service/dingtalkApi';

const { Title, Text } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;
const { TextArea } = Input;
const { Panel } = Collapse;
const { RangePicker } = DatePicker;

/**
 * 统一审批管理组件
 * 整合业务审批和通用审批流程管理功能
 */
const UnifiedApprovalManagement = () => {
  // 状态管理
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('instances');
  const [stores, setStores] = useState([]);
  const [approvalInstances, setApprovalInstances] = useState([]);
  const [approvalTemplates, setApprovalTemplates] = useState([]);
  const [businessStats, setBusinessStats] = useState({
    total: 0,
    pending: 0,
    approved: 0,
    rejected: 0
  });

  // 模态框和表单状态
  const [createModalVisible, setCreateModalVisible] = useState(false);
  const [templateModalVisible, setTemplateModalVisible] = useState(false);
  const [instanceDetailVisible, setInstanceDetailVisible] = useState(false);
  const [selectedInstance, setSelectedInstance] = useState(null);
  const [selectedTemplateType, setSelectedTemplateType] = useState(null);
  const [createTemplateLoading, setCreateTemplateLoading] = useState(false);
  const [createForm] = Form.useForm();
  const [templateForm] = Form.useForm();
  const [fileList, setFileList] = useState([]);
  
  // 业务发现和同步相关状态
  const [discoveredBusinesses, setDiscoveredBusinesses] = useState([]);
  const [businessDiscoveryVisible, setBusinessDiscoveryVisible] = useState(false);
  const [syncLoading, setSyncLoading] = useState(false);
  const [autoSyncEnabled, setAutoSyncEnabled] = useState(false);

  // 新增：可视化模板组件管理状态
  const [formComponents, setFormComponents] = useState([]);
  const [componentModalVisible, setComponentModalVisible] = useState(false);
  const [editingComponent, setEditingComponent] = useState(null);
  const [editingTemplate, setEditingTemplate] = useState(null);
  const [previewModalVisible, setPreviewModalVisible] = useState(false);
  const [componentForm] = Form.useForm();

  // 筛选参数
  const [filters, setFilters] = useState({
    store_id: null,
    template_type: 'all',
    status: 'all',
    date_range: null
  });

  // 业务审批类型配置（增强版）
  const businessApprovalTypes = [
    {
      key: 'store_loss',
      name: '门店报损',
      icon: <ExclamationCircleOutlined />,
      color: '#ff4d4f',
      category: 'business',
      description: '门店商品报损申请审批',
      fields: [
        { key: 'store_name', label: '门店名称', type: 'select', required: true },
        { key: 'product_name', label: '产品名称', type: 'input', required: true },
        { key: 'product_code', label: '产品编码', type: 'input', required: true },
        { key: 'quantity', label: '报损数量', type: 'number', required: true },
        { key: 'unit_price', label: '单价', type: 'money', required: true },
        { key: 'loss_reason', label: '报损原因', type: 'select', required: true, 
          options: [
            { value: 'expired', label: '过期变质' },
            { value: 'damaged', label: '破损' },
            { value: 'theft', label: '盗损' },
            { value: 'other', label: '其他原因' }
          ]},
        { key: 'description', label: '报损说明', type: 'textarea', required: false },
        { key: 'evidence', label: '报损凭证', type: 'upload', required: false }
      ]
    },
    {
      key: 'sales_report',
      name: '销售业绩上报',
      icon: <BarChartOutlined />,
      color: '#52c41a',
      category: 'business',
      description: '门店销售业绩数据上报审批',
      fields: [
        { key: 'store_name', label: '门店名称', type: 'select', required: true },
        { key: 'report_date', label: '报告日期', type: 'date', required: true },
        { key: 'total_sales', label: '总销售额', type: 'money', required: true },
        { key: 'customer_count', label: '客流量', type: 'number', required: true },
        { key: 'average_price', label: '客单价', type: 'money', required: true },
        { key: 'top_products', label: '热销产品', type: 'textarea', required: false },
        { key: 'performance_summary', label: '业绩总结', type: 'textarea', required: false }
      ]
    },
    {
      key: 'expense_reimbursement',
      name: '费用报销',
      icon: <MoneyCollectOutlined />,
      color: '#1890ff',
      category: 'business',
      description: '门店日常费用报销申请',
      fields: [
        { key: 'store_name', label: '门店名称', type: 'select', required: true },
        { key: 'expense_date', label: '费用日期', type: 'date', required: true },
        { key: 'expense_type', label: '费用类型', type: 'select', required: true,
          options: [
            { value: 'misc', label: '杂费' },
            { value: 'material', label: '物料费' },
            { value: 'electricity', label: '电费' },
            { value: 'internet', label: '网费' },
            { value: 'maintenance', label: '维修费' },
            { value: 'other', label: '其他' }
          ]},
        { key: 'amount', label: '报销金额', type: 'money', required: true },
        { key: 'description', label: '费用说明', type: 'textarea', required: true },
        { key: 'vendor', label: '供应商/商家', type: 'input', required: false },
        { key: 'receipts', label: '发票凭证', type: 'upload', required: true }
      ]
    }
  ];

  // 钉钉表单组件类型配置（支持所有官方组件）
  const dingtalkComponentTypes = [
    {
      type: 'TextField',
      name: '单行文本',
      icon: <FileTextOutlined />,
      color: '#1890ff',
      category: 'basic',
      description: '单行文本输入框',
      defaultProps: {
        label: '单行文本',
        required: false,
        placeholder: '请输入内容',
        maxLength: 200
      }
    },
    {
      type: 'TextareaField',
      name: '多行文本',
      icon: <FileTextOutlined />,
      color: '#52c41a',
      category: 'basic',
      description: '多行文本输入框',
      defaultProps: {
        label: '多行文本',
        required: false,
        placeholder: '请输入内容',
        rows: 3
      }
    },
    {
      type: 'NumberField',
      name: '数字输入',
      icon: <DollarOutlined />,
      color: '#fa8c16',
      category: 'basic',
      description: '数字输入框',
      defaultProps: {
        label: '数字',
        required: false,
        placeholder: '请输入数字',
        format: 'dn',
        min: 0
      }
    },
    {
      type: 'MoneyField',
      name: '金额输入',
      icon: <MoneyCollectOutlined />,
      color: '#eb2f96',
      category: 'basic',
      description: '金额输入框',
      defaultProps: {
        label: '金额',
        required: false,
        placeholder: '请输入金额',
        unit: '元',
        precision: 2
      }
    },
    {
      type: 'DDDateField',
      name: '日期选择',
      icon: <CalendarOutlined />,
      color: '#722ed1',
      category: 'selection',
      description: '日期选择器',
      defaultProps: {
        label: '日期',
        required: false,
        format: 'yyyy-MM-dd',
        unit: '天',
        placeholder: '请选择日期'
      }
    },
    {
      type: 'DDDateTimeField',
      name: '日期时间选择',
      icon: <CalendarOutlined />,
      color: '#13c2c2',
      category: 'selection',
      description: '日期时间选择器',
      defaultProps: {
        label: '日期时间',
        required: false,
        format: 'yyyy-MM-dd HH:mm',
        placeholder: '请选择日期时间'
      }
    },
    {
      type: 'DDSelectField',
      name: '下拉单选',
      icon: <FilterOutlined />,
      color: '#f5222d',
      category: 'selection',
      description: '下拉单选框',
      defaultProps: {
        label: '下拉选择',
        required: false,
        placeholder: '请选择',
        options: [
          { key: 'option1', value: '选项1' },
          { key: 'option2', value: '选项2' }
        ]
      }
    },
    {
      type: 'RadioField',
      name: '单选按钮',
      icon: <CheckCircleOutlined />,
      color: '#fa541c',
      category: 'selection',
      description: '单选按钮组',
      defaultProps: {
        label: '单选',
        required: false,
        options: [
          { key: 'yes', value: '是' },
          { key: 'no', value: '否' }
        ]
      }
    },
    {
      type: 'CheckboxField',
      name: '多选框',
      icon: <CheckCircleOutlined />,
      color: '#a0d911',
      category: 'selection',
      description: '多选框组',
      defaultProps: {
        label: '多选',
        required: false,
        options: [
          { key: 'check1', value: '选项1' },
          { key: 'check2', value: '选项2' }
        ]
      }
    },
    {
      type: 'DDPhotoField',
      name: '图片上传',
      icon: <UploadOutlined />,
      color: '#2f54eb',
      category: 'upload',
      description: '图片上传组件',
      defaultProps: {
        label: '图片',
        required: false,
        limit: 9,
        placeholder: '请上传图片'
      }
    },
    {
      type: 'FileField',
      name: '文件上传',
      icon: <UploadOutlined />,
      color: '#096dd9',
      category: 'upload',
      description: '文件上传组件',
      defaultProps: {
        label: '文件',
        required: false,
        limit: 5,
        placeholder: '请上传文件'
      }
    },
    {
      type: 'DDContactField',
      name: '联系人',
      icon: <TeamOutlined />,
      color: '#7cb305',
      category: 'contact',
      description: '钉钉联系人选择',
      defaultProps: {
        label: '联系人',
        required: false,
        multiple: false,
        placeholder: '请选择联系人'
      }
    },
    {
      type: 'DDDeptField',
      name: '部门选择',
      icon: <ShopOutlined />,
      color: '#389e0d',
      category: 'contact',
      description: '钉钉部门选择',
      defaultProps: {
        label: '部门',
        required: false,
        multiple: false,
        placeholder: '请选择部门'
      }
    }
  ];

  // 获取项目ID
  const getProjectId = () => {
    return localStorage.getItem('project_id') || '';
  };

  // 初始化数据
  useEffect(() => {
    loadInitialData();
  }, []);

  useEffect(() => {
    if (activeTab === 'instances') {
      loadApprovalInstances();
    } else if (activeTab === 'templates') {
      loadApprovalTemplates();
    }
  }, [activeTab, filters]);

  // 初始化数据加载
  const loadInitialData = async () => {
    setLoading(true);
    try {
      await Promise.all([
        loadStores(),
        loadBusinessStats()
      ]);
    } catch (error) {
      console.error('加载初始数据失败:', error);
      message.error('加载数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 加载门店列表
  const loadStores = async () => {
    try {
      // 使用真实的API调用，需要根据实际项目结构调整
      // 这里暂时使用模拟数据，实际应该调用门店管理API
      const mockStores = [
        { id: 1, name: '总店', address: '北京市朝阳区' },
        { id: 2, name: '分店A', address: '北京市海淀区' },
        { id: 3, name: '分店B', address: '北京市西城区' }
      ];
      setStores(mockStores);
    } catch (error) {
      console.error('加载门店列表失败:', error);
    }
  };

  // 加载审批模板
  const loadApprovalTemplates = async () => {
    try {
      setLoading(true);
      const response = await dingtalkApi.getApprovalTemplates();
      console.log('模板列表API响应:', response); // 调试日志
      
      if (response.success && response.data && response.data.templates) {
        // 只使用新的返回结构：{ data: { templates: [], total: 1 } }
        const templates = response.data.templates;
        console.log('提取到的模板数据:', templates);
        setApprovalTemplates(templates);
        console.log('设置的模板数组:', templates);
      } else {
        console.error('API响应失败或数据结构错误:', response);
        setApprovalTemplates([]);
      }
    } catch (error) {
      console.error('加载审批模板失败:', error);
      message.error('加载审批模板失败');
      setApprovalTemplates([]);
    } finally {
      setLoading(false);
    }
  };

  // 加载审批实例
  const loadApprovalInstances = async () => {
    try {
      setLoading(true);
      const params = {};
      
      if (filters.store_id) params.store_id = filters.store_id;
      if (filters.template_type !== 'all') params.template_type = filters.template_type;
      if (filters.status !== 'all') params.status = filters.status;
      if (filters.date_range && filters.date_range.length === 2) {
        params.start_date = filters.date_range[0].format('YYYY-MM-DD');
        params.end_date = filters.date_range[1].format('YYYY-MM-DD');
      }

      const response = await dingtalkApi.getApprovalInstances(params);
      if (response.success && response.data) {
        setApprovalInstances(response.data.instances || []);
      }
    } catch (error) {
      console.error('加载审批实例失败:', error);
      message.error('加载审批实例失败');
    } finally {
      setLoading(false);
    }
  };

  // 加载统计数据
  const loadBusinessStats = async () => {
    try {
      const response = await dingtalkApi.getBusinessStats();
      if (response.success && response.data) {
        setBusinessStats(response.data);
      }
    } catch (error) {
      console.error('加载统计数据失败:', error);
    }
  };

  // 创建审批模板
  // 编辑模板
  const handleEditTemplate = (template) => {
    console.log('编辑模板数据:', template); // 调试日志
    setEditingTemplate(template);
    setTemplateModalVisible(true);
    
    // 预填充表单数据 - 使用实际返回的字段名
    templateForm.setFieldsValue({
      name: template.name,                    // 后端返回的是name字段
      template_type: template.template_type,  // 后端返回的是template_type字段
      description: template.description || '',
      status: template.status || 'ENABLE',
      approvers: template.approvers || [],
      owners: template.owners || [],
      dir_id: template.dir_id || '-1'
    });
    
    // 设置模板类型
    setSelectedTemplateType(template.template_type);
    
    // 加载模板组件 - 使用实际返回的字段名
    const components = template.form_components; // 后端返回的是form_components字段
    console.log('模板组件数据:', components); // 调试日志
    
    if (components && Array.isArray(components) && components.length > 0) {
      console.log('加载现有组件:', components);
      setFormComponents(components);
    } else {
      console.log('无现有组件，加载默认组件');
      // 如果没有组件数据，尝试从模板类型加载默认组件
      const templateConfig = getApprovalTypeConfig(template.template_type);
      if (templateConfig && templateConfig.fields) {
        const defaultComponents = templateConfig.fields.map((field, index) => ({
          id: `${field.key}_${Date.now()}_${index}`,
          type: _mapFieldTypeToComponent(field.type),
          props: {
            componentId: field.key,
            label: field.label,
            required: field.required,
            placeholder: field.placeholder || `请输入${field.label}`,
            ...field
          },
          sortOrder: index
        }));
        setFormComponents(defaultComponents);
      } else {
        setFormComponents([]);
      }
    }
  };

  // 删除模板
  const handleDeleteTemplate = async (template) => {
    try {
      await dingtalkApi.deleteApprovalTemplate(template.id);
      message.success('删除模板成功');
      loadApprovalTemplates();
    } catch (error) {
      console.error('删除模板失败:', error);
      message.error('删除模板失败');
    }
  };

  const handleCreateTemplate = async (values) => {
    try {
      setCreateTemplateLoading(true);
      
      // 使用可视化组件或预定义配置
      let templateComponents = [];
      
      if (formComponents.length > 0) {
        // 使用用户自定义的组件
        templateComponents = formComponents.map(component => ({
          componentType: component.type,
          props: {
            componentId: component.id,
            label: component.props.label,
            required: component.props.required,
            placeholder: component.props.placeholder,
            ...component.props
          }
        }));
      } else {
        // 使用预定义配置
        const templateConfig = getApprovalTypeConfig(selectedTemplateType);
        if (!templateConfig) {
          message.error('请先选择模板类型或添加表单组件');
          return;
        }
        templateComponents = templateConfig.fields.map(field => ({
          componentType: _mapFieldTypeToComponent(field.type),
          props: {
            componentId: field.key,
            label: field.label,
            required: field.required,
            placeholder: field.placeholder || `请输入${field.label}`,
            ...field
          }
        }));
      }

      const templateData = {
        name: values.name,
        description: values.description,
        template_type: selectedTemplateType || 'custom',  // 使用template_type字段
        form_components: templateComponents,               // 使用form_components字段
        approvers: values.approvers || [],
        owners: values.owners || [],
        dir_id: values.dir_id || '-1'
      };

      let response;
      if (editingTemplate) {
        // 编辑模式 - 添加process_code字段
        templateData.process_code = editingTemplate.process_code;
        response = await dingtalkApi.updateApprovalTemplate(editingTemplate.id, templateData);
      } else {
        // 创建模式
        response = await dingtalkApi.createApprovalTemplate(templateData);
      }
      
      if (response.success) {
        message.success(editingTemplate ? '审批模板更新成功' : '审批模板创建成功');
        setTemplateModalVisible(false);
        templateForm.resetFields();
        setSelectedTemplateType(null);
        setFormComponents([]);
        setEditingTemplate(null);
        await loadApprovalTemplates();
      } else {
        message.error(response.message || (editingTemplate ? '更新模板失败' : '创建模板失败'));
      }
    } catch (error) {
      console.error(editingTemplate ? '更新审批模板失败:' : '创建审批模板失败:', error);
      message.error(editingTemplate ? '更新模板失败' : '创建模板失败');
    } finally {
      setCreateTemplateLoading(false);
    }
  };

  // 字段类型映射到钉钉组件类型
  const _mapFieldTypeToComponent = (fieldType) => {
    const mapping = {
      'input': 'TextField',
      'textarea': 'TextareaField',
      'number': 'NumberField',
      'money': 'MoneyField',
      'date': 'DDDateField',
      'datetime': 'DDDateTimeField',
      'select': 'DDSelectField',
      'radio': 'RadioField',
      'checkbox': 'CheckboxField',
      'upload': 'DDPhotoField',
      'file': 'FileField',
      'contact': 'DDContactField',
      'department': 'DDDeptField'
    };
    return mapping[fieldType] || 'TextField';
  };

  // ==================== 可视化组件管理功能 ====================

  // 添加表单组件
  const handleAddComponent = (componentType) => {
    const componentConfig = dingtalkComponentTypes.find(c => c.type === componentType);
    if (!componentConfig) return;

    const newComponent = {
      id: `${componentType}_${Date.now()}`,
      type: componentType,
      props: {
        ...componentConfig.defaultProps,
        componentId: `${componentType}_${Date.now()}`
      },
      sortOrder: formComponents.length
    };

    setFormComponents([...formComponents, newComponent]);
    message.success(`已添加${componentConfig.name}组件`);
  };

  // 编辑组件属性
  const handleEditComponent = (component) => {
    setEditingComponent(component);
    componentForm.setFieldsValue({
      label: component.props.label,
      required: component.props.required,
      placeholder: component.props.placeholder,
      ...component.props
    });
    setComponentModalVisible(true);
  };

  // 删除组件
  const handleDeleteComponent = (componentId) => {
    setFormComponents(formComponents.filter(c => c.id !== componentId));
    message.success('组件已删除');
  };

  // 复制组件
  const handleCopyComponent = (component) => {
    const newComponent = {
      ...component,
      id: `${component.type}_${Date.now()}`,
      props: {
        ...component.props,
        componentId: `${component.type}_${Date.now()}`,
        label: component.props.label + '_副本'
      },
      sortOrder: formComponents.length
    };
    setFormComponents([...formComponents, newComponent]);
    message.success('组件已复制');
  };

  // 上移组件
  const handleMoveComponentUp = (index) => {
    if (index <= 0) return;
    const newComponents = [...formComponents];
    [newComponents[index], newComponents[index - 1]] = [newComponents[index - 1], newComponents[index]];
    setFormComponents(newComponents);
  };

  // 下移组件
  const handleMoveComponentDown = (index) => {
    if (index >= formComponents.length - 1) return;
    const newComponents = [...formComponents];
    [newComponents[index], newComponents[index + 1]] = [newComponents[index + 1], newComponents[index]];
    setFormComponents(newComponents);
  };

  // 保存组件属性
  const handleSaveComponentProps = async (values) => {
    if (!editingComponent) return;

    const updatedComponents = formComponents.map(component => {
      if (component.id === editingComponent.id) {
        return {
          ...component,
          props: {
            ...component.props,
            ...values
          }
        };
      }
      return component;
    });

    setFormComponents(updatedComponents);
    setComponentModalVisible(false);
    setEditingComponent(null);
    componentForm.resetFields();
    message.success('组件属性已更新');
  };

  // 获取组件类型配置
  const getComponentTypeConfig = (type) => {
    return dingtalkComponentTypes.find(c => c.type === type);
  };

  // 清空所有组件
  const handleClearAllComponents = () => {
    setFormComponents([]);
    message.success('已清空所有组件');
  };

  // 从预设模板加载组件
  const handleLoadTemplateComponents = (templateType) => {
    const templateConfig = getApprovalTypeConfig(templateType);
    if (!templateConfig) return;

    const components = templateConfig.fields.map((field, index) => ({
      id: `${field.key}_${Date.now()}_${index}`,
      type: _mapFieldTypeToComponent(field.type),
      props: {
        componentId: field.key,
        label: field.label,
        required: field.required,
        placeholder: field.placeholder || `请输入${field.label}`,
        ...field
      },
      sortOrder: index
    }));

    setFormComponents(components);
    message.success(`已加载${templateConfig.name}模板组件`);
  };

  // 获取审批类型配置
  const getApprovalTypeConfig = (type) => {
    return businessApprovalTypes.find(t => t.key === type);
  };
  
  // ==================== 业务发现和同步功能 ====================
  
  const discoverPendingBusinesses = async () => {
    try {
      setLoading(true);
      const response = await dingtalkApi.discoverPendingBusinessApprovals(getProjectId());
      
      if (response.success) {
        setDiscoveredBusinesses(response.data.discovered_items || []);
        setBusinessDiscoveryVisible(true);
        message.success(`发现了 ${response.data.total} 个待审批业务`);
      } else {
        message.error(response.message || '发现业务数据失败');
      }
    } catch (error) {
      console.error('发现业务数据失败:', error);
      message.error('发现业务数据失败');
    } finally {
      setLoading(false);
    }
  };
  
  const autoSyncDiscoveredApprovals = async (syncOptions = {}) => {
    try {
      setSyncLoading(true);
      const response = await dingtalkApi.autoSyncDiscoveredApprovals(getProjectId(), syncOptions);
      
      if (response.success) {
        const { successful_syncs, total_discovered } = response.data;
        message.success(`成功同步 ${successful_syncs}/${total_discovered} 个审批到钉钉`);
        
        // 刷新数据
        await loadApprovalInstances();
        setBusinessDiscoveryVisible(false);
      } else {
        message.error(response.message || '自动同步失败');
      }
    } catch (error) {
      console.error('自动同步失败:', error);
      message.error('自动同步失败');
    } finally {
      setSyncLoading(false);
    }
  };
  
  const syncSpecificBusiness = async (businessItem) => {
    try {
      setLoading(true);
      const syncData = {
        business_type: businessItem.business_type,
        details: businessItem.details,
        originator_user_id: businessItem.creator_user_id
      };
      
      const response = await dingtalkApi.syncSpecificBusinessApproval(getProjectId(), syncData);
      
      if (response.success) {
        message.success(`成功同步 ${businessItem.title} 到钉钉`);
        await loadApprovalInstances();
        
        // 更新发现列表，移除已同步的项目
        setDiscoveredBusinesses(prev => 
          prev.filter(item => item.business_id !== businessItem.business_id)
        );
      } else {
        message.error(response.message || '同步失败');
      }
    } catch (error) {
      console.error('同步业务失败:', error);
      message.error('同步业务失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取状态标签
  const getStatusTag = (status) => {
    const statusMap = {
      'NEW': { color: 'blue', text: '新建' },
      'RUNNING': { color: 'orange', text: '审批中' },
      'COMPLETED': { color: 'green', text: '已通过' },
      'TERMINATED': { color: 'red', text: '已拒绝' }
    };
    const config = statusMap[status] || { color: 'default', text: status };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  // ==================== 渲染函数 ====================

  // 渲染组件选择器
  const renderComponentSelector = () => {
    const categories = [...new Set(dingtalkComponentTypes.map(c => c.category))];
    
    return (
      <Card size="small" title="组件库" style={{ marginBottom: 16 }}>
        <Collapse size="small" ghost>
          {categories.map(category => {
            const categoryComponents = dingtalkComponentTypes.filter(c => c.category === category);
            const categoryNames = {
              'basic': '基础输入',
              'selection': '选择器',
              'upload': '上传组件',
              'contact': '联系人'
            };
            
            return (
              <Panel key={category} header={categoryNames[category] || category}>
                <Row gutter={[8, 8]}>
                  {categoryComponents.map(component => (
                    <Col span={12} key={`${category}-${component.type}`}>
                      <Button
                        size="small"
                        block
                        onClick={() => handleAddComponent(component.type)}
                        style={{ 
                          display: 'flex', 
                          alignItems: 'center', 
                          justifyContent: 'flex-start',
                          padding: '4px 8px',
                          height: 'auto'
                        }}
                      >
                        <Space size={4}>
                          {component.icon}
                          <span style={{ fontSize: '12px' }}>{component.name}</span>
                        </Space>
                      </Button>
                    </Col>
                  ))}
                </Row>
              </Panel>
            );
          })}
        </Collapse>
      </Card>
    );
  };

  // 渲染组件列表
  const renderComponentList = () => {
    if (formComponents.length === 0) {
      return (
        <Card size="small" title="表单组件" style={{ marginBottom: 16 }}>
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description="暂无组件，请从组件库中添加"
          />
        </Card>
      );
    }

    return (
      <Card 
        size="small" 
        title={
          <Space>
            <span>表单组件</span>
            <Badge count={formComponents.length} style={{ backgroundColor: '#52c41a' }} />
          </Space>
        }
        extra={
          <Space size="small">
            <Button
              size="small"
              icon={<EyeOutlined />}
              onClick={() => setPreviewModalVisible(true)}
            >
              预览
            </Button>
            <Popconfirm
              title="确定要清空所有组件吗？"
              onConfirm={handleClearAllComponents}
              okText="确定"
              cancelText="取消"
            >
              <Button size="small" danger icon={<DeleteOutlined />}>
                清空
              </Button>
            </Popconfirm>
          </Space>
        }
        style={{ marginBottom: 16 }}
      >
        <List
          size="small"
          dataSource={formComponents}
          renderItem={(component, index) => {
            const typeConfig = getComponentTypeConfig(component.type);
            return (
              <List.Item
                key={`${component.type}-${component.id}`}
                style={{ 
                  border: '1px solid #f0f0f0', 
                  borderRadius: 4, 
                  marginBottom: 8,
                  padding: '8px 12px'
                }}
                actions={[
                  <Button
                    type="text"
                    size="small"
                    icon={<EditOutlined />}
                    onClick={() => handleEditComponent(component)}
                  />,
                  <Button
                    type="text"
                    size="small"
                    icon={<CopyOutlined />}
                    onClick={() => handleCopyComponent(component)}
                  />,
                  <Button
                    type="text"
                    size="small"
                    icon={<DragOutlined />}
                    disabled={index === 0}
                    onClick={() => handleMoveComponentUp(index)}
                  />,
                  <Button
                    type="text"
                    size="small"
                    icon={<DragOutlined style={{ transform: 'rotate(180deg)' }} />}
                    disabled={index === formComponents.length - 1}
                    onClick={() => handleMoveComponentDown(index)}
                  />,
                  <Popconfirm
                    title="确定要删除这个组件吗？"
                    onConfirm={() => handleDeleteComponent(component.id)}
                    okText="确定"
                    cancelText="取消"
                  >
                    <Button
                      type="text"
                      size="small"
                      danger
                      icon={<DeleteOutlined />}
                    />
                  </Popconfirm>
                ]}
              >
                <List.Item.Meta
                  avatar={
                    <div style={{ 
                      width: 32, 
                      height: 32, 
                      borderRadius: 4, 
                      backgroundColor: typeConfig?.color + '20',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      color: typeConfig?.color
                    }}>
                      {typeConfig?.icon}
                    </div>
                  }
                  title={
                    <Space>
                      <Text strong>{component.props.label}</Text>
                      {component.props.required && <Tag color="red" size="small">必填</Tag>}
                    </Space>
                  }
                  description={
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      {typeConfig?.name} • ID: {component.props.componentId}
                    </Text>
                  }
                />
              </List.Item>
            );
          }}
        />
      </Card>
    );
  };

  // 渲染快速操作
  const renderQuickActions = () => (
    <Card size="small" title="快速操作" style={{ marginBottom: 16 }}>
      <Space wrap>
        <Button size="small" onClick={() => handleLoadTemplateComponents('store_loss')}>
          门店报损模板
        </Button>
        <Button size="small" onClick={() => handleLoadTemplateComponents('sales_report')}>
          销售上报模板
        </Button>
        <Button size="small" onClick={() => handleLoadTemplateComponents('expense_reimbursement')}>
          费用报销模板
        </Button>
      </Space>
    </Card>
  );

  // 渲染统计卡片
  const renderStatsCards = () => (
    <Row gutter={16} style={{ marginBottom: 24 }}>
      <Col span={6}>
        <Card>
          <Statistic
            title="总审批数"
            value={businessStats.total}
            prefix={<AuditOutlined />}
          />
        </Card>
      </Col>
      <Col span={6}>
        <Card>
          <Statistic
            title="待审批"
            value={businessStats.pending}
            prefix={<ClockCircleOutlined />}
            valueStyle={{ color: '#fa8c16' }}
          />
        </Card>
      </Col>
      <Col span={6}>
        <Card>
          <Statistic
            title="已通过"
            value={businessStats.approved}
            prefix={<CheckCircleOutlined />}
            valueStyle={{ color: '#3f8600' }}
          />
        </Card>
      </Col>
      <Col span={6}>
        <Card>
          <Statistic
            title="已拒绝"
            value={businessStats.rejected}
            prefix={<CloseCircleOutlined />}
            valueStyle={{ color: '#cf1322' }}
          />
        </Card>
      </Col>
    </Row>
  );

  // 审批实例表格列定义
  const instanceColumns = [
    {
      title: '审批ID',
      dataIndex: 'id',
      key: 'id',
      width: 100,
      render: (id) => <Text code>{id}</Text>
    },
    {
      title: '审批标题',
      dataIndex: 'title',
      key: 'title',
      render: (title, record) => (
        <Space direction="vertical" size={0}>
          <Text strong>{title}</Text>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {getApprovalTypeConfig(record.template_type)?.name}
          </Text>
        </Space>
      )
    },
    {
      title: '申请人',
      dataIndex: 'originator_user_name',
      key: 'originator_user_name',
      width: 100
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status) => getStatusTag(status)
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 150,
      render: (time) => time ? new Date(time).toLocaleString() : '-'
    },
    {
      title: '完成时间',
      dataIndex: 'approved_at',
      key: 'approved_at',
      width: 150,
      render: (time) => time ? new Date(time).toLocaleString() : '-'
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_, record) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => {
              setSelectedInstance(record);
              setInstanceDetailVisible(true);
            }}
          >
            查看
          </Button>
          {record.dingtalk_url && (
            <Button
              type="link"
              size="small"
              onClick={() => window.open(record.dingtalk_url, '_blank')}
            >
              钉钉查看
            </Button>
          )}
        </Space>
      )
    }
  ];

  // 模板表格列定义
  const templateColumns = [
    {
      title: '模板名称',
      dataIndex: 'name',
      key: 'name'
    },
    {
      title: '模板类型',
      dataIndex: 'template_type',
      key: 'template_type',
      render: (type) => {
        const config = getApprovalTypeConfig(type);
        return config ? (
          <Space>
            {config.icon}
            {config.name}
          </Space>
        ) : type;
      }
    },
    {
      title: '流程编码',
      dataIndex: 'process_code',
      key: 'process_code',
      render: (code) => <Text code>{code}</Text>
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={status === 'ENABLE' ? 'green' : 'red'}>
          {status === 'ENABLE' ? '启用' : '禁用'}
        </Tag>
      )
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (time) => time ? new Date(time).toLocaleString() : '-'
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="small">
          <Button 
            type="link" 
            size="small"
            icon={<EditOutlined />}
            onClick={() => handleEditTemplate(record)}
          >
            编辑
          </Button>
          <Button 
            type="link" 
            size="small"
            icon={<EyeOutlined />}
            onClick={() => {
              // 预览模板功能 - 使用实际的字段名
              console.log('预览模板数据:', record);
              const components = record.form_components || [];
              console.log('原始组件数据:', components);
              
              // 确保组件数据格式正确
              const formattedComponents = components.map((comp, index) => {
                console.log(`组件${index}:`, comp);
                
                // 处理不同的组件数据格式
                let formattedComp = {
                  id: comp.id || comp.props?.componentId || `component_${index}`,
                  type: comp.componentType || comp.type || 'TextField',
                  props: comp.props || {},
                  componentType: comp.componentType || comp.type || 'TextField'
                };
                
                // 确保props包含必要字段
                if (!formattedComp.props.componentId) {
                  formattedComp.props.componentId = formattedComp.id;
                }
                if (!formattedComp.props.label) {
                  formattedComp.props.label = comp.label || formattedComp.type;
                }
                
                return formattedComp;
              });
              
              console.log('格式化后的组件:', formattedComponents);
              setFormComponents(formattedComponents);
              setPreviewModalVisible(true);
            }}
          >
            预览
          </Button>
          <Button 
            type="link" 
            size="small"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDeleteTemplate(record)}
          >
            删除
          </Button>
        </Space>
      )
    }
  ];

  return (
    <div className="unified-approval-management">
      {/* 统计卡片 */}
      {renderStatsCards()}

      <Alert
        message="统一审批管理"
        description="整合钉钉业务审批和通用审批流程管理功能，支持门店管理相关的各种审批场景。"
        type="info"
        style={{ marginBottom: 24 }}
      />

      <Card>
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab={<span><AuditOutlined />审批实例</span>} key="instances">
            <div>
              {/* 筛选器 */}
              <Card size="small" style={{ marginBottom: 16 }}>
                <Row gutter={16} align="middle">
                  <Col span={4}>
                    <Select
                      placeholder="选择门店"
                      allowClear
                      style={{ width: '100%' }}
                      value={filters.store_id}
                      onChange={(value) => setFilters({ ...filters, store_id: value })}
                    >
                      {stores.map(store => (
                        <Option key={`store-${store.id}`} value={store.id}>{store.name}</Option>
                      ))}
                    </Select>
                  </Col>
                  <Col span={4}>
                    <Select
                      placeholder="审批类型"
                      style={{ width: '100%' }}
                      value={filters.template_type}
                      onChange={(value) => setFilters({ ...filters, template_type: value })}
                    >
                      <Option key="type-all" value="all">全部类型</Option>
                      {businessApprovalTypes.map(type => (
                        <Option key={`type-${type.key}`} value={type.key}>{type.name}</Option>
                      ))}
                    </Select>
                  </Col>
                  <Col span={4}>
                    <Select
                      placeholder="审批状态"
                      style={{ width: '100%' }}
                      value={filters.status}
                      onChange={(value) => setFilters({ ...filters, status: value })}
                    >
                      <Option key="status-all" value="all">全部状态</Option>
                      <Option key="status-new" value="NEW">新建</Option>
                      <Option key="status-running" value="RUNNING">审批中</Option>
                      <Option key="status-completed" value="COMPLETED">已通过</Option>
                      <Option key="status-terminated" value="TERMINATED">已拒绝</Option>
                    </Select>
                  </Col>
                  <Col span={6}>
                    <RangePicker
                      style={{ width: '100%' }}
                      value={filters.date_range}
                      onChange={(dates) => setFilters({ ...filters, date_range: dates })}
                    />
                  </Col>
                  <Col span={6}>
                    <Space>
                      <Button 
                        type="primary"
                        icon={<AuditOutlined />}
                        onClick={discoverPendingBusinesses}
                        loading={loading}
                      >
                        发现待审批业务
                      </Button>
                      <Button 
                        type="primary"
                        icon={<FilterOutlined />}
                        onClick={loadApprovalInstances}
                      >
                        查询
                      </Button>
                      <Button 
                        icon={<SyncOutlined />}
                        onClick={loadApprovalInstances}
                      >
                        刷新
                      </Button>
                      <Button 
                        icon={<ExportOutlined />}
                      >
                        导出
                      </Button>
                    </Space>
                  </Col>
                </Row>
              </Card>

              {/* 审批实例表格 */}
              <Table
                columns={instanceColumns}
                dataSource={approvalInstances}
                loading={loading}
                rowKey="id"
                pagination={{
                  showSizeChanger: true,
                  showQuickJumper: true,
                  showTotal: (total) => `共 ${total} 条记录`
                }}
              />
            </div>
          </TabPane>
          
          <TabPane tab={<span><SettingOutlined />模板管理</span>} key="templates">
            <div>
              <Alert
                message="📋 审批模板管理"
                description={
                  <div>
                    <p style={{ marginBottom: 8 }}>在这里您可以创建和管理钉钉审批模板：</p>
                    <ul style={{ marginBottom: 0, paddingLeft: 20 }}>
                      <li><strong>预设模板</strong>：选择业务类型快速创建常用审批模板</li>
                      <li><strong>自定义模板</strong>：使用可视化组件库自由设计表单</li>
                      <li><strong>组件管理</strong>：支持拖拽排序、属性编辑、实时预览</li>
                      <li><strong>组件类型</strong>：支持文本、数字、日期、选择器、上传、联系人等14种钉钉官方组件</li>
                    </ul>
                  </div>
                }
                type="info"
                showIcon
                style={{ marginBottom: 24 }}
              />
              
              <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
                <Col span={24}>
                  <Space>
                    <Button
                      type="primary"
                      icon={<PlusOutlined />}
                      onClick={() => setTemplateModalVisible(true)}
                    >
                      创建模板
                    </Button>
                    <Button
                      icon={<SyncOutlined />}
                      onClick={loadApprovalTemplates}
                    >
                      刷新
                    </Button>
                  </Space>
                </Col>
              </Row>

              {/* 模板类型卡片 */}
              <Alert
                message="业务审批模板类型"
                description="根据业务场景选择合适的模板类型，系统会自动配置相应的字段和审批流程。"
                type="info"
                style={{ marginBottom: 16 }}
              />
              
              <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
                {businessApprovalTypes.map(type => (
                  <Col span={8} key={`type-${type.key}`}>
                    <Card
                      size="small"
                      title={
                        <Space>
                          {type.icon}
                          {type.name}
                        </Space>
                      }
                      extra={
                        <Badge 
                          count={approvalTemplates.filter(t => t.template_type === type.key).length}
                          style={{ backgroundColor: type.color }}
                        />
                      }
                    >
                      <div style={{ marginBottom: 8 }}>
                        <Text type="secondary">{type.description}</Text>
                      </div>
                      <div style={{ marginBottom: 12 }}>
                        <Tag color={type.color}>{type.category === 'business' ? '业务类型' : '通用类型'}</Tag>
                      </div>
                      <Button 
                        type="primary" 
                        size="small" 
                        block
                        onClick={() => {
                          setSelectedTemplateType(type.key);
                          setTemplateModalVisible(true);
                        }}
                      >
                        创建模板
                      </Button>
                    </Card>
                  </Col>
                ))}
                
                {/* 自定义模板卡片 */}
                <Col span={8}>
                  <Card
                    size="small"
                    title={
                      <Space>
                        <ToolOutlined />
                        自定义模板
                      </Space>
                    }
                    extra={
                      <Badge 
                        count={approvalTemplates.filter(t => t.template_type === 'custom').length}
                        style={{ backgroundColor: '#722ed1' }}
                      />
                    }
                    style={{ border: '2px dashed #722ed1' }}
                  >
                    <div style={{ marginBottom: 8 }}>
                      <Text type="secondary">从空白模板开始，自由添加表单组件</Text>
                    </div>
                    <div style={{ marginBottom: 12 }}>
                      <Tag color="#722ed1">自定义类型</Tag>
                    </div>
                    <Button 
                      type="primary" 
                      size="small" 
                      block
                      style={{ backgroundColor: '#722ed1', borderColor: '#722ed1' }}
                      onClick={() => {
                        setSelectedTemplateType(null);
                        setFormComponents([]);
                        setTemplateModalVisible(true);
                      }}
                    >
                      创建自定义模板
                    </Button>
                  </Card>
                </Col>
              </Row>

              {/* 已创建的模板列表 */}
              <Card title="已创建的模板" size="small">
                <Table 
                  columns={templateColumns} 
                  dataSource={approvalTemplates}
                  rowKey={(record) => record.id || record.process_code || Math.random().toString(36)}
                  loading={loading}
                  pagination={{
                    total: approvalTemplates.length,
                    pageSize: 10,
                    showTotal: (total) => `共 ${total} 个模板`
                  }}
                />
              </Card>
            </div>
          </TabPane>
        </Tabs>
      </Card>

      {/* 创建模板模态框 */}
      <Modal
        title={editingTemplate ? "编辑审批模板" : "创建审批模板"}
        visible={templateModalVisible}
        onCancel={() => {
          setTemplateModalVisible(false);
          templateForm.resetFields();
          setSelectedTemplateType(null);
          setFormComponents([]);
        }}
        footer={null}
        width={1200}
        style={{ top: 20 }}
      >
        <Row gutter={16}>
          {/* 左侧：组件库和表单组件 */}
          <Col span={14}>
            <div style={{ maxHeight: '70vh', overflowY: 'auto', paddingRight: 8 }}>
              {/* 快速操作 */}
              {renderQuickActions()}
              
              {/* 组件库 */}
              {renderComponentSelector()}
              
              {/* 表单组件列表 */}
              {renderComponentList()}
            </div>
          </Col>
          
          {/* 右侧：模板基本信息 */}
          <Col span={10}>
            <div style={{ borderLeft: '1px solid #f0f0f0', paddingLeft: 16 }}>
              <Form
                form={templateForm}
                layout="vertical"
                onFinish={handleCreateTemplate}
              >
                {selectedTemplateType && (
                  <Alert
                    message={`创建${getApprovalTypeConfig(selectedTemplateType)?.name}模板`}
                    description={getApprovalTypeConfig(selectedTemplateType)?.description}
                    type="info"
                    style={{ marginBottom: 16 }}
                  />
                )}

                <Form.Item
                  name="name"
                  label="模板名称"
                  rules={[{ required: true, message: '请输入模板名称' }]}
                >
                  <Input placeholder="请输入模板名称" />
                </Form.Item>

                <Form.Item
                  name="description"
                  label="模板描述"
                >
                  <Input.TextArea rows={3} placeholder="请输入模板描述" />
                </Form.Item>

                <Form.Item label="组件统计">
                  <Card size="small">
                    <Row gutter={16}>
                      <Col span={12}>
                        <Statistic
                          title="总组件"
                          value={formComponents.length}
                          prefix={<ToolOutlined />}
                        />
                      </Col>
                      <Col span={12}>
                        <Statistic
                          title="必填组件"
                          value={formComponents.filter(c => c.props.required).length}
                          prefix={<ExclamationCircleOutlined />}
                          valueStyle={{ color: '#cf1322' }}
                        />
                      </Col>
                    </Row>
                  </Card>
                </Form.Item>

                {formComponents.length === 0 && !selectedTemplateType && (
                  <Alert
                    message="请添加表单组件"
                    description="您可以从左侧组件库添加组件，或使用快速操作加载预设模板。"
                    type="warning"
                    style={{ marginBottom: 16 }}
                  />
                )}

                <Form.Item>
                  <Space>
                    <Button
                      type="primary"
                      htmlType="submit"
                      loading={createTemplateLoading}
                      disabled={formComponents.length === 0 && !selectedTemplateType}
                    >
                      {editingTemplate ? "更新模板" : "创建模板"}
                    </Button>
                    <Button
                      onClick={() => {
                        setTemplateModalVisible(false);
                        templateForm.resetFields();
                        setSelectedTemplateType(null);
                        setFormComponents([]);
                        setEditingTemplate(null);
                      }}
                    >
                      取消
                    </Button>
                  </Space>
                </Form.Item>
              </Form>
            </div>
          </Col>
        </Row>
      </Modal>

      {/* 审批实例详情抽屉 */}
      <Drawer
        title="审批实例详情"
        visible={instanceDetailVisible}
        onClose={() => setInstanceDetailVisible(false)}
        width={600}
      >
        {selectedInstance && (
          <div>
            <Descriptions title="基本信息" column={1} bordered>
              <Descriptions.Item label="审批ID">{selectedInstance.id}</Descriptions.Item>
              <Descriptions.Item label="审批标题">{selectedInstance.title}</Descriptions.Item>
              <Descriptions.Item label="审批类型">
                {getApprovalTypeConfig(selectedInstance.template_type)?.name}
              </Descriptions.Item>
              <Descriptions.Item label="申请人">{selectedInstance.originator_user_name}</Descriptions.Item>
              <Descriptions.Item label="状态">{getStatusTag(selectedInstance.status)}</Descriptions.Item>
              <Descriptions.Item label="创建时间">
                {selectedInstance.created_at ? new Date(selectedInstance.created_at).toLocaleString() : '-'}
              </Descriptions.Item>
              <Descriptions.Item label="完成时间">
                {selectedInstance.approved_at ? new Date(selectedInstance.approved_at).toLocaleString() : '-'}
              </Descriptions.Item>
            </Descriptions>

            {selectedInstance.form_component_values && (
              <div style={{ marginTop: 24 }}>
                <Title level={5}>表单数据</Title>
                <Descriptions column={1} bordered>
                  {selectedInstance.form_component_values.map((item, index) => (
                    <Descriptions.Item key={`form-value-${item.name || item.component_id || index}`} label={item.name}>
                      {item.value}
                    </Descriptions.Item>
                  ))}
                </Descriptions>
              </div>
            )}

            {selectedInstance.dingtalk_url && (
              <div style={{ marginTop: 24 }}>
                <Button
                  type="primary"
                  onClick={() => window.open(selectedInstance.dingtalk_url, '_blank')}
                >
                  在钉钉中查看
                </Button>
              </div>
            )}
          </div>
        )}
      </Drawer>

      {/* 组件属性编辑模态框 */}
      <Modal
        title={editingComponent ? `编辑 ${getComponentTypeConfig(editingComponent.type)?.name}` : '编辑组件'}
        visible={componentModalVisible}
        onCancel={() => {
          setComponentModalVisible(false);
          setEditingComponent(null);
          componentForm.resetFields();
        }}
        footer={null}
        width={600}
      >
        {editingComponent && (
          <Form
            form={componentForm}
            layout="vertical"
            onFinish={handleSaveComponentProps}
            initialValues={editingComponent.props}
          >
            <Form.Item
              name="label"
              label="组件标签"
              rules={[{ required: true, message: '请输入组件标签' }]}
            >
              <Input placeholder="请输入组件标签" />
            </Form.Item>

            <Form.Item
              name="required"
              label="是否必填"
              valuePropName="checked"
            >
              <Switch checkedChildren="必填" unCheckedChildren="可选" />
            </Form.Item>

            <Form.Item
              name="placeholder"
              label="占位符文本"
            >
              <Input placeholder="请输入占位符文本" />
            </Form.Item>

            {/* 根据组件类型显示不同的配置项 */}
            {editingComponent.type === 'NumberField' && (
              <>
                <Form.Item name="min" label="最小值">
                  <InputNumber style={{ width: '100%' }} placeholder="最小值" />
                </Form.Item>
                <Form.Item name="max" label="最大值">
                  <InputNumber style={{ width: '100%' }} placeholder="最大值" />
                </Form.Item>
                <Form.Item name="precision" label="小数位数">
                  <InputNumber min={0} max={10} style={{ width: '100%' }} />
                </Form.Item>
              </>
            )}

            {editingComponent.type === 'MoneyField' && (
              <>
                <Form.Item name="unit" label="货币单位">
                  <Select>
                    <Option key="currency-cny" value="元">元</Option>
                    <Option key="currency-usd" value="美元">美元</Option>
                    <Option key="currency-eur" value="欧元">欧元</Option>
                  </Select>
                </Form.Item>
                <Form.Item name="precision" label="小数位数">
                  <InputNumber min={0} max={4} style={{ width: '100%' }} defaultValue={2} />
                </Form.Item>
              </>
            )}

            {editingComponent.type === 'TextField' && (
              <Form.Item name="maxLength" label="最大字符数">
                <InputNumber min={1} max={1000} style={{ width: '100%' }} />
              </Form.Item>
            )}

            {editingComponent.type === 'TextareaField' && (
              <>
                <Form.Item name="rows" label="显示行数">
                  <InputNumber min={2} max={20} style={{ width: '100%' }} />
                </Form.Item>
                <Form.Item name="maxLength" label="最大字符数">
                  <InputNumber min={1} max={5000} style={{ width: '100%' }} />
                </Form.Item>
              </>
            )}

            {editingComponent.type === 'DDDateField' && (
              <>
                <Form.Item name="format" label="日期格式">
                  <Select>
                    <Option key="date-format-1" value="yyyy-MM-dd">yyyy-MM-dd</Option>
                    <Option key="date-format-2" value="yyyy/MM/dd">yyyy/MM/dd</Option>
                    <Option key="date-format-3" value="MM/dd/yyyy">MM/dd/yyyy</Option>
                  </Select>
                </Form.Item>
                <Form.Item name="unit" label="单位">
                  <Select>
                    <Option key="unit-day" value="天">天</Option>
                    <Option key="unit-week" value="周">周</Option>
                    <Option key="unit-month" value="月">月</Option>
                  </Select>
                </Form.Item>
              </>
            )}

            {(editingComponent.type === 'DDSelectField' || 
              editingComponent.type === 'RadioField' || 
              editingComponent.type === 'CheckboxField') && (
              <Form.Item label="选项配置">
                <Form.List name="options" initialValue={editingComponent.props.options || []}>
                  {(fields, { add, remove }) => (
                    <>
                      {fields.map(({ key, name, ...restField }) => (
                        <Space key={key} style={{ display: 'flex', marginBottom: 8 }} align="baseline">
                          <Form.Item
                            {...restField}
                            name={[name, 'key']}
                            rules={[{ required: true, message: '请输入选项键' }]}
                          >
                            <Input placeholder="选项键" />
                          </Form.Item>
                          <Form.Item
                            {...restField}
                            name={[name, 'value']}
                            rules={[{ required: true, message: '请输入选项值' }]}
                          >
                            <Input placeholder="选项值" />
                          </Form.Item>
                          <Button type="link" danger onClick={() => remove(name)}>
                            删除
                          </Button>
                        </Space>
                      ))}
                      <Form.Item>
                        <Button type="dashed" onClick={() => add()} block icon={<PlusOutlined />}>
                          添加选项
                        </Button>
                      </Form.Item>
                    </>
                  )}
                </Form.List>
              </Form.Item>
            )}

            {(editingComponent.type === 'DDPhotoField' || editingComponent.type === 'FileField') && (
              <Form.Item name="limit" label="最大文件数">
                <InputNumber min={1} max={20} style={{ width: '100%' }} />
              </Form.Item>
            )}

            {(editingComponent.type === 'DDContactField' || editingComponent.type === 'DDDeptField') && (
              <Form.Item name="multiple" label="多选" valuePropName="checked">
                <Switch checkedChildren="多选" unCheckedChildren="单选" />
              </Form.Item>
            )}

            <Form.Item>
              <Space>
                <Button type="primary" htmlType="submit">
                  保存属性
                </Button>
                <Button onClick={() => {
                  setComponentModalVisible(false);
                  setEditingComponent(null);
                  componentForm.resetFields();
                }}>
                  取消
                </Button>
              </Space>
            </Form.Item>
          </Form>
        )}
      </Modal>

      {/* 业务发现和同步模态框 */}
      <Modal
        title={
          <Space>
            <AuditOutlined />
            待审批业务发现
            <Badge count={discoveredBusinesses.length} style={{ backgroundColor: '#52c41a' }} />
          </Space>
        }
        visible={businessDiscoveryVisible}
        onCancel={() => setBusinessDiscoveryVisible(false)}
        footer={
          <Space>
            <Button onClick={() => setBusinessDiscoveryVisible(false)}>
              关闭
            </Button>
            <Button 
              type="primary" 
              icon={<SyncOutlined />}
              loading={syncLoading}
              onClick={() => autoSyncDiscoveredApprovals()}
              disabled={discoveredBusinesses.length === 0}
            >
              批量同步到钉钉 ({discoveredBusinesses.length})
            </Button>
          </Space>
        }
        width={900}
      >
        <div style={{ marginBottom: 16 }}>
          <Alert
            message="发现了项目中需要审批的业务数据"
            description="以下是系统发现的待审批业务，您可以选择同步到钉钉进行审批流程。"
            type="info"
            showIcon
          />
        </div>
        
        {discoveredBusinesses.length === 0 ? (
          <Empty description="暂无待审批业务" />
        ) : (
          <List
            dataSource={discoveredBusinesses}
            renderItem={(item, index) => {
              const typeConfig = getApprovalTypeConfig(item.business_type);
              return (
                <List.Item
                  key={`business-${item.business_id}-${index}`}
                  actions={[
                    <Button
                      type="primary"
                      size="small"
                      icon={<SyncOutlined />}
                      onClick={() => syncSpecificBusiness(item)}
                      loading={loading}
                    >
                      同步到钉钉
                    </Button>
                  ]}
                >
                  <List.Item.Meta
                    avatar={
                      <div style={{
                        width: 40,
                        height: 40,
                        borderRadius: 8,
                        backgroundColor: typeConfig?.color + '20',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        color: typeConfig?.color
                      }}>
                        {typeConfig?.icon || <AuditOutlined />}
                      </div>
                    }
                    title={
                      <Space>
                        <Text strong>{item.title}</Text>
                        <Tag color={typeConfig?.color}>{typeConfig?.name || item.business_type}</Tag>
                      </Space>
                    }
                    description={
                      <div>
                        <Text type="secondary">创建时间: {item.created_at ? new Date(item.created_at).toLocaleString() : '未知'}</Text>
                        {item.amount && (
                          <div>
                            <Text type="secondary">金额: </Text>
                            <Text strong style={{ color: '#fa8c16' }}>¥{item.amount}</Text>
                          </div>
                        )}
                        {item.details && (
                          <div style={{ marginTop: 8 }}>
                            <Collapse size="small" ghost>
                              <Panel header="查看详情" key="details">
                                <Descriptions size="small" column={2}>
                                  {Object.entries(item.details).map(([key, value]) => (
                                    <Descriptions.Item 
                                      key={`detail-${key}`}
                                      label={key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                                    >
                                      {value?.toString() || '-'}
                                    </Descriptions.Item>
                                  ))}
                                </Descriptions>
                              </Panel>
                            </Collapse>
                          </div>
                        )}
                      </div>
                    }
                  />
                </List.Item>
              );
            }}
          />
        )}
      </Modal>

      {/* 模板预览模态框 */}
      <Modal
        title="模板预览"
        visible={previewModalVisible}
        onCancel={() => setPreviewModalVisible(false)}
        footer={
          <Button onClick={() => setPreviewModalVisible(false)}>
            关闭
          </Button>
        }
        width={800}
      >
        <div style={{ maxHeight: '60vh', overflowY: 'auto' }}>
          <Alert
            message="预览说明"
            description="以下是根据当前组件配置生成的表单预览，实际钉钉审批界面可能略有差异。"
            type="info"
            style={{ marginBottom: 16 }}
          />
          
          {formComponents.length === 0 ? (
            <Empty description="暂无组件可预览" />
          ) : (
            <div>
              {/* 调试信息 */}
              <Alert
                message={`共找到 ${formComponents.length} 个组件`}
                description={`组件类型: ${formComponents.map(c => c.componentType || c.type).join(', ')}`}
                type="info"
                style={{ marginBottom: 16 }}
              />
              
              <Form layout="vertical">
                {formComponents.map((component, index) => {
                  const componentType = component.componentType || component.type;
                  const typeConfig = getComponentTypeConfig(componentType);
                  const componentProps = component.props || {};
                  
                  console.log(`渲染组件 ${index}:`, { componentType, typeConfig, componentProps });
                  
                  return (
                    <div key={`${componentType}-${componentProps.componentId || index}`} style={{ 
                      border: '1px dashed #d9d9d9', 
                      borderRadius: 4, 
                      padding: 16, 
                      marginBottom: 16,
                      position: 'relative'
                    }}>
                      {/* 组件类型标识 */}
                      <div style={{ 
                        position: 'absolute', 
                        top: -8, 
                        left: 12, 
                        backgroundColor: '#fff',
                        padding: '0 8px',
                        fontSize: '12px',
                        color: typeConfig?.color || '#666'
                      }}>
                        <Space size={4}>
                          {typeConfig?.icon || <QuestionCircleOutlined />}
                          {typeConfig?.name || componentType}
                        </Space>
                      </div>
                      
                      {/* 组件标题和属性 */}
                      <Form.Item 
                        label={componentProps.label || typeConfig?.label || componentType}
                        required={componentProps.required || false}
                      >
                        {/* 根据组件类型渲染不同的预览 */}
                        {componentType === 'TextField' && (
                          <Input 
                            placeholder={componentProps.placeholder || '请输入文本'}
                            disabled
                          />
                        )}
                        
                        {componentType === 'DDSelectField' && (
                          <Select 
                            placeholder={componentProps.placeholder || '请选择'}
                            disabled
                          >
                            {(componentProps.options || []).map((option, idx) => (
                              <Option key={idx} value={option.value || option}>
                                {option.label || option.value || option}
                              </Option>
                            ))}
                          </Select>
                        )}
                        
                        {componentType === 'NumberField' && (
                          <InputNumber 
                            placeholder={componentProps.placeholder || '请输入数字'}
                            style={{ width: '100%' }}
                            disabled
                          />
                        )}
                        
                        {componentType === 'MoneyField' && (
                          <InputNumber 
                            placeholder={componentProps.placeholder || '请输入金额'}
                            prefix="¥"
                            style={{ width: '100%' }}
                            disabled
                          />
                        )}
                        
                        {componentType === 'DDDateField' && (
                          <DatePicker 
                            placeholder={componentProps.placeholder || '请选择日期'}
                            style={{ width: '100%' }}
                            disabled
                          />
                        )}
                        
                        {componentType === 'TextareaField' && (
                          <Input.TextArea 
                            placeholder={componentProps.placeholder || '请输入内容'}
                            rows={4}
                            disabled
                          />
                        )}
                        
                        {componentType === 'RadioField' && (
                          <Radio.Group disabled>
                            {(componentProps.options || []).map((option, idx) => (
                              <Radio key={idx} value={option.value || option}>
                                {option.label || option.value || option}
                              </Radio>
                            ))}
                          </Radio.Group>
                        )}
                        
                        {componentType === 'CheckboxField' && (
                          <Checkbox.Group disabled>
                            {(componentProps.options || []).map((option, idx) => (
                              <Checkbox key={idx} value={option.value || option}>
                                {option.label || option.value || option}
                              </Checkbox>
                            ))}
                          </Checkbox.Group>
                        )}
                        
                        {componentType === 'DDContactField' && (
                          <div style={{ 
                            padding: '8px 12px', 
                            border: '1px solid #d9d9d9', 
                            borderRadius: 4,
                            backgroundColor: '#f5f5f5',
                            color: '#666'
                          }}>
                            <Space>
                              <TeamOutlined />
                              联系人选择器 (仅钉钉中可用)
                            </Space>
                          </div>
                        )}
                        
                        {componentType === 'DDDeptField' && (
                          <div style={{ 
                            padding: '8px 12px', 
                            border: '1px solid #d9d9d9', 
                            borderRadius: 4,
                            backgroundColor: '#f5f5f5',
                            color: '#666'
                          }}>
                            <Space>
                              <ShopOutlined />
                              部门选择器 (仅钉钉中可用)
                            </Space>
                          </div>
                        )}
                        
                        {componentType === 'DDPhotoField' && (
                          <div style={{ 
                            padding: '8px 12px', 
                            border: '1px solid #d9d9d9', 
                            borderRadius: 4,
                            backgroundColor: '#f5f5f5',
                            color: '#666'
                          }}>
                            <Space>
                              <UploadOutlined />
                              图片上传 (最多{componentProps.limit || 9}张)
                            </Space>
                          </div>
                        )}
                        
                        {componentType === 'FileField' && (
                          <div style={{ 
                            padding: '8px 12px', 
                            border: '1px solid #d9d9d9', 
                            borderRadius: 4,
                            backgroundColor: '#f5f5f5',
                            color: '#666'
                          }}>
                            <Space>
                              <UploadOutlined />
                              文件上传 (最多{componentProps.limit || 5}个)
                            </Space>
                          </div>
                        )}
                        
                        {/* 默认显示 */}
                        {!['TextField', 'DDSelectField', 'NumberField', 'MoneyField', 'DDDateField', 'TextareaField', 'RadioField', 'CheckboxField', 'DDContactField', 'DDDeptField', 'DDPhotoField', 'FileField'].includes(componentType) && (
                          <div style={{ 
                            padding: '8px 12px', 
                            border: '1px solid #d9d9d9', 
                            borderRadius: 4,
                            backgroundColor: '#f5f5f5',
                            color: '#666'
                          }}>
                            <Space>
                              <QuestionCircleOutlined />
                              {typeConfig?.name || componentType} 组件预览
                            </Space>
                            {componentProps.placeholder && (
                              <div style={{ fontSize: '12px', marginTop: '4px' }}>
                                提示：{componentProps.placeholder}
                              </div>
                            )}
                          </div>
                        )}
                      </Form.Item>
                    </div>
                  );
                })}
              </Form>
            </div>
          )}
        </div>
      </Modal>
    </div>
  );
};

export default UnifiedApprovalManagement; 