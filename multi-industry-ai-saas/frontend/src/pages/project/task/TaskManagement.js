import React, { useState, useEffect, useRef } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Modal,
  Form,
  Input,
  Select,
  DatePicker,
  message,
  Tabs,
  Tooltip,
  Badge,
  Row,
  Col,
  Typography,
  Statistic,
  Progress,
  Spin,
  Radio,
  Empty,
  Popconfirm,
  Drawer
} from 'antd';
import {
  PlusOutlined,
  SyncOutlined,
  EditOutlined,
  DeleteOutlined,
  CheckOutlined,
  ExclamationCircleOutlined,
  SearchOutlined,
  FilterOutlined,
  PieChartOutlined,
  Bar<PERSON><PERSON>Outlined,
  LineChartOutlined,
  CloudServerOutlined,
  EyeOutlined,
  StopOutlined
} from '@ant-design/icons';
import dayjs from 'dayjs';
import apiService, { api } from '../../../services/api';
import ReactECharts from 'echarts-for-react';
import { AsyncTaskProgress, TaskList } from '../../../components/AsyncTaskProgress';
import { useAsyncTask, useTaskExecution } from '../../../hooks/useAsyncTask';
import AsyncTaskDemo from '../../demo/AsyncTaskDemo';
import { useProject } from '../../../contexts/ProjectContext';

const { Title, Text } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;
const { TextArea } = Input;
const { RangePicker } = DatePicker;
const { confirm } = Modal;

/**
 * 任务管理组件
 * 用于管理项目任务，包括创建、编辑、删除、查看任务
 */
const TaskManagement = () => {
  // 获取项目信息
  const { currentProject } = useProject();

  // 状态定义
  const [loading, setLoading] = useState(false);
  const [loadingUsers, setLoadingUsers] = useState(false);
  const [tasks, setTasks] = useState([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingTask, setEditingTask] = useState(null);
  const [activeTab, setActiveTab] = useState('1');
  const [isAdminView, setIsAdminView] = useState(false); // 是否显示管理员视图
  const [form] = Form.useForm();
  const [users, setUsers] = useState([]);
  const [filteredUsers, setFilteredUsers] = useState([]);
  const [stores, setStores] = useState([]);
  const [fileList, setFileList] = useState([]);
  const [selectedStores, setSelectedStores] = useState([]);
  const [loadingStats, setLoadingStats] = useState(false); // 统计数据加载状态
  const [statsPeriod, setStatsPeriod] = useState('month'); // 统计周期：week, month, year
  const [taskStats, setTaskStats] = useState({
    total: 0,
    pending: 0,
    in_progress: 0,
    completed: 0,
    overdue: 0,
    cancelled: 0
  });
  const [storeStats, setStoreStats] = useState([]); // 按门店统计
  const [categoryStats, setCategoryStats] = useState([]); // 按类别统计
  const [typeStats, setTypeStats] = useState([]); // 按任务类型统计
  const [priorityStats, setPriorityStats] = useState([]); // 按优先级统计
  const [timeStats, setTimeStats] = useState([]); // 按时间统计
  const [filters, setFilters] = useState({
    status: undefined,
    priority: undefined,
    category: undefined,
    task_type: undefined,
    assignee: undefined,
    dateRange: [dayjs().subtract(30, 'days'), dayjs()],
    search: ''
  });

  // 异步任务相关状态
  const [asyncTasks, setAsyncTasks] = useState([]);
  const [loadingAsyncTasks, setLoadingAsyncTasks] = useState(false);
  const [asyncTaskFilter, setAsyncTaskFilter] = useState('all');
  const [selectedAsyncTasks, setSelectedAsyncTasks] = useState([]);
  const [asyncTaskModule, setAsyncTaskModule] = useState('all');
  const [asyncTaskSearch, setAsyncTaskSearch] = useState('');
  const [asyncTaskDateRange, setAsyncTaskDateRange] = useState([dayjs().subtract(30, 'days'), dayjs().add(1, 'day')]);
  const [taskDetailVisible, setTaskDetailVisible] = useState(false);
  const [selectedTask, setSelectedTask] = useState(null);

  // 任务类别文本映射
  const categoryTexts = {
    store: '门店',
    operation: '运营',
    marketing: '营销',
    inventory: '库存',
    finance: '财务',
    other: '其他'
  };

  // 任务类型映射
  const taskTypeTexts = {
    // 项目任务类型
    planning: '规划任务',
    implementation: '实施任务',
    review: '审核任务',
    coordination: '协调任务',
    reporting: '报告任务',
    // 通用任务类型
    daily: '日常任务',
    weekly: '周期任务',
    monthly: '月度任务',
    other: '其他任务'
  };

  // 任务优先级文本映射
  const priorityTexts = {
    low: '低',
    medium: '中',
    high: '高',
    urgent: '紧急'
  };

  // 任务优先级颜色映射
  const priorityColors = {
    low: 'green',
    medium: 'blue',
    high: 'orange',
    urgent: 'red'
  };

  // 任务状态文本映射
  const statusTexts = {
    pending: '待处理',
    in_progress: '进行中',
    completed: '已完成',
    overdue: '已逾期',
    cancelled: '已取消'
  };

  // 任务状态颜色映射
  const statusColors = {
    pending: 'default',
    in_progress: 'processing',
    completed: 'success',
    overdue: 'error',
    cancelled: 'warning'
  };

  // 初始化
  useEffect(() => {
    fetchTasks();
    fetchUsers();
    fetchStores();
  }, []);

  // 当切换到统计页面时，获取统计数据
  useEffect(() => {
    if (activeTab === '2') {
      fetchTaskStats();
    } else if (activeTab === '3') {
      fetchAsyncTasks();
    }
  }, [activeTab, statsPeriod, asyncTaskFilter]);

  // 获取任务统计数据
  const fetchTaskStats = async () => {
    setLoadingStats(true);
    try {
      // 设置请求参数
      const params = {
        period: statsPeriod // week, month, year
      };

      // 调用后端统计接口
      const response = await apiService.project.routineTask.getStats(params);

      if (response) {
        // 更新状态统计
        setTaskStats(response.status_stats);

        // 更新门店统计
        setStoreStats(response.store_stats);

        // 更新类别统计
        setCategoryStats(response.category_stats);

        // 更新任务类型统计
        setTypeStats(response.type_stats);

        // 更新优先级统计
        setPriorityStats(response.priority_stats);

        // 更新时间统计
        setTimeStats(response.time_stats);
      }
    } catch (error) {
      console.error('获取任务统计数据失败:', error);
      message.error('获取任务统计数据失败');
    } finally {
      setLoadingStats(false);
    }
  };

  // 获取任务列表
  const fetchTasks = async () => {
    setLoading(true);
    try {
      const response = await apiService.project.routineTask.getList({
        view: isAdminView ? 'admin' : 'personal',
        status: filters.status,
        priority: filters.priority,
        category: filters.category,
        task_type: filters.task_type,
        assignee: filters.assignee,
        start_date: filters.dateRange[0]?.format('YYYY-MM-DD'),
        end_date: filters.dateRange[1]?.format('YYYY-MM-DD'),
        search: filters.search
      });

      if (response) {
        // 确保response是数组，或者从response中提取数组数据
        let taskData = [];
        
        if (Array.isArray(response)) {
          taskData = response;
        } else if (response && Array.isArray(response.data)) {
          taskData = response.data;
        } else if (response && Array.isArray(response.items)) {
          taskData = response.items;
        } else if (response && response.success && Array.isArray(response.data)) {
          taskData = response.data;
        }
        
        setTasks(taskData);
      } else {
        setTasks([]);
      }
    } catch (error) {
      console.error('获取任务列表失败:', error);
      message.error('获取任务列表失败');
      setTasks([]); // 发生错误时设置为空数组
    } finally {
      setLoading(false);
    }
  };

  // 获取异步任务列表
  const fetchAsyncTasks = async () => {
    setLoadingAsyncTasks(true);
    try {
      const params = {
        status: asyncTaskFilter === 'all' ? undefined : asyncTaskFilter,
        task_module: asyncTaskModule === 'all' ? undefined : asyncTaskModule,
        search: asyncTaskSearch || undefined,
        start_date: asyncTaskDateRange[0]?.startOf('day').format('YYYY-MM-DD'),
        end_date: asyncTaskDateRange[1]?.endOf('day').format('YYYY-MM-DD'),
        limit: 100, // 增加限制数量
        offset: 0
      };
      
      console.log('异步任务查询参数:', params);
      
      // 使用正确的异步任务API路径
      const response = await api.get(`/project/${currentProject?.id || ''}/tasks`, { params });
      
      if (response && response.success && response.data) {
        // 如果有tasks数组，使用tasks数组
        const tasks = response.data.tasks || response.data || [];
        console.log('获取到异步任务数量:', tasks.length);
        setAsyncTasks(Array.isArray(tasks) ? tasks : []);
      } else if (response && Array.isArray(response)) {
        // 直接返回数组的情况
        console.log('获取到异步任务数量:', response.length);
        setAsyncTasks(response);
      } else {
        console.log('异步任务响应格式:', response);
        setAsyncTasks([]);
      }
    } catch (error) {
      console.error('获取异步任务列表失败:', error);
      message.error('获取异步任务列表失败');
      // 发生错误时设置为空数组
      setAsyncTasks([]);
    } finally {
      setLoadingAsyncTasks(false);
    }
  };

  // 取消异步任务
  const cancelAsyncTask = async (taskId) => {
    try {
      const response = await api.post(`/project/${currentProject?.id || ''}/tasks/${taskId}/cancel`);
      if (response && response.success) {
        message.success('任务已取消');
        fetchAsyncTasks();
      }
    } catch (error) {
      console.error('取消任务失败:', error);
      message.error('取消任务失败');
    }
  };

  // 删除异步任务
  const deleteAsyncTask = async (taskId) => {
    try {
      const response = await api.delete(`/project/${currentProject?.id || ''}/tasks/${taskId}`);
      if (response && response.success) {
        message.success('任务已删除');
        fetchAsyncTasks();
        // 从选中列表中移除
        setSelectedAsyncTasks(prev => prev.filter(id => id !== taskId));
      }
    } catch (error) {
      console.error('删除任务失败:', error);
      message.error('删除任务失败');
    }
  };

  // 批量删除异步任务
  const batchDeleteAsyncTasks = async () => {
    if (selectedAsyncTasks.length === 0) {
      message.warning('请先选择要删除的任务');
      return;
    }

    Modal.confirm({
      title: '确认批量删除',
      content: `确定要删除选中的 ${selectedAsyncTasks.length} 个任务吗？此操作不可恢复。`,
      okText: '确定删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          const response = await api.post(`/project/${currentProject?.id || ''}/tasks/batch-delete`, {
            task_ids: selectedAsyncTasks
          });
          
          if (response && response.success) {
            const { deleted, failed } = response.data;
            if (deleted > 0) {
              message.success(`成功删除 ${deleted} 个任务${failed > 0 ? `，${failed} 个任务删除失败` : ''}`);
            } else {
              message.error('删除失败');
            }
            fetchAsyncTasks();
            setSelectedAsyncTasks([]);
          }
        } catch (error) {
          console.error('批量删除任务失败:', error);
          message.error('批量删除任务失败');
        }
      }
    });
  };

  // 处理异步任务选择
  const handleAsyncTaskSelection = (selectedRowKeys) => {
    setSelectedAsyncTasks(selectedRowKeys);
  };

  // 重置异步任务筛选条件
  const resetAsyncTaskFilters = () => {
    setAsyncTaskFilter('all');
    setAsyncTaskModule('all');
    setAsyncTaskSearch('');
    setAsyncTaskDateRange([dayjs().subtract(30, 'days'), dayjs().add(1, 'day')]);
    setTimeout(() => {
      fetchAsyncTasks();
    }, 0);
  };

  // 查看任务详情
  const viewTaskDetail = (task) => {
    setSelectedTask(task);
    setTaskDetailVisible(true);
  };

  // 查看异步任务结果
  const viewAsyncTaskResult = (result) => {
    console.log('查看任务结果，原始数据:', result);
    
    if (!result) {
      message.info('任务结果为空');
      return;
    }

    // 尝试从不同的数据结构中提取预览数据
    let preview = null;
    let purchaseItems = [];
    
    // 检查多种可能的数据结构
    if (result.data && result.data.preview) {
      preview = result.data.preview;
      purchaseItems = preview.purchase_items || [];
      console.log('从 result.data.preview 中找到数据');
    } else if (result.result && result.result.data && result.result.data.preview) {
      preview = result.result.data.preview;
      purchaseItems = preview.purchase_items || [];
      console.log('从 result.result.data.preview 中找到数据');
    } else if (result.preview) {
      preview = result.preview;
      purchaseItems = preview.purchase_items || [];
      console.log('从 result.preview 中找到数据');
    } else if (result.purchase_items) {
      purchaseItems = result.purchase_items || [];
      preview = result;
      console.log('从 result.purchase_items 中找到数据');
    } else if (result.data && result.data.purchase_items) {
      purchaseItems = result.data.purchase_items || [];
      preview = result.data;
      console.log('从 result.data.purchase_items 中找到数据');
    }

    console.log('提取的预览数据:', preview);
    console.log('提取的商品数据:', purchaseItems);

    // 如果是采购分拨单AI处理结果，显示专门的结果界面
    if (purchaseItems && purchaseItems.length > 0) {
      const totalAmount = purchaseItems.reduce((sum, item) => sum + (parseFloat(item.total_amount) || 0), 0);
      
      Modal.info({
        title: (
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <CheckOutlined style={{ color: '#52c41a', marginRight: 8 }} />
            AI识别结果 - 采购分拨单
          </div>
        ),
        width: 900,
        content: (
          <div>
            <div style={{ marginBottom: 16, padding: 12, background: '#f6ffed', borderRadius: 6 }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <div>
                  <Text strong style={{ color: '#389e0d' }}>
                    🎉 AI智能识别成功！共识别到 {purchaseItems.length} 个商品
                  </Text>
                </div>
                <div>
                  <Text strong style={{ fontSize: '16px', color: '#fa8c16' }}>
                    总金额: ¥{totalAmount.toFixed(2)}
                  </Text>
                </div>
              </div>
            </div>
            
            <div style={{ marginBottom: 16 }}>
              <Text strong>识别的商品列表：</Text>
              <div style={{ 
                marginTop: 8, 
                maxHeight: 350, 
                overflow: 'auto',
                border: '1px solid #d9d9d9',
                borderRadius: 6
              }}>
                {purchaseItems.map((item, index) => (
                  <div key={index} style={{ 
                    padding: 12, 
                    borderBottom: index < purchaseItems.length - 1 ? '1px solid #f0f0f0' : 'none',
                    background: index % 2 === 0 ? '#fafafa' : '#fff'
                  }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <div>
                        <div style={{ fontWeight: 'bold', fontSize: '14px' }}>
                          {item.product_name || item.name || '未知商品'}
                        </div>
                        <div style={{ fontSize: '12px', color: '#666', marginTop: 4 }}>
                          规格: {item.product_specification || item.specification || '无'} | 
                          单位: {item.product_unit || item.unit || '个'} | 
                          编码: {item.product_code || item.sku || '无'}
                        </div>
                        {(item.suggested_category || item.product_category) && (
                          <div style={{ fontSize: '12px', color: '#1890ff', marginTop: 2 }}>
                            分类: {item.suggested_category || item.product_category}
                          </div>
                        )}
                        {(item.suggested_brand || item.product_brand) && (
                          <div style={{ fontSize: '12px', color: '#722ed1', marginTop: 2 }}>
                            品牌: {item.suggested_brand || item.product_brand}
                          </div>
                        )}
                      </div>
                      <div style={{ textAlign: 'right' }}>
                        <div style={{ fontSize: '14px' }}>
                          数量: <strong>{item.quantity || 0}</strong>
                        </div>
                        <div style={{ fontSize: '14px' }}>
                          单价: <strong>¥{parseFloat(item.unit_price || 0).toFixed(2)}</strong>
                        </div>
                        <div style={{ fontSize: '14px', color: '#f56a00' }}>
                          金额: <strong>¥{parseFloat(item.total_amount || 0).toFixed(2)}</strong>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            
            <div style={{ 
              background: '#e6f7ff', 
              border: '1px solid #91d5ff',
              borderRadius: 6,
              padding: 16,
              marginTop: 16
            }}>
              <div style={{ fontSize: '14px', fontWeight: 'bold', marginBottom: 12 }}>
                📋 后续操作选项：
              </div>
              <div style={{ display: 'flex', gap: 12, flexWrap: 'wrap' }}>
                <Button 
                  type="primary"
                  onClick={() => {
                    Modal.destroyAll();
                    // 跳转到采购管理页面
                    window.open('/project/purchase/orders', '_blank');
                  }}
                >
                  前往采购管理
                </Button>
                <Button 
                  onClick={() => {
                    Modal.destroyAll();
                    // 跳转到采购分拨页面
                    window.open('/project/purchase/orders?tab=2', '_blank');
                  }}
                >
                  继续分拨操作
                </Button>
                <Button 
                  onClick={() => {
                    // 下载识别结果
                    const dataStr = JSON.stringify(preview || result, null, 2);
                    const dataBlob = new Blob([dataStr], {type: 'application/json'});
                    const url = URL.createObjectURL(dataBlob);
                    const link = document.createElement('a');
                    link.href = url;
                    link.download = `AI识别结果_${new Date().toISOString().slice(0,10)}.json`;
                    link.click();
                    URL.revokeObjectURL(url);
                    message.success('识别结果已下载');
                  }}
                >
                  下载结果
                </Button>
                <Button 
                  onClick={() => {
                    // 复制到剪贴板
                    const text = purchaseItems.map(item => 
                      `${item.product_name || item.name}\t${item.quantity}\t${item.unit_price}\t${item.total_amount}`
                    ).join('\n');
                    navigator.clipboard.writeText(text).then(() => {
                      message.success('商品清单已复制到剪贴板');
                    }).catch(() => {
                      message.error('复制失败，请手动复制');
                    });
                  }}
                >
                  复制清单
                </Button>
              </div>
              <div style={{ fontSize: '12px', color: '#666', marginTop: 8 }}>
                💡 提示：您可以前往采购管理页面确认识别结果并生成正式的采购订单
              </div>
            </div>
          </div>
        ),
        onOk() {},
      });
    } else {
      // 其他类型的结果，显示通用结果框
      Modal.info({
        title: '任务结果',
        width: 800,
        content: (
          <div>
            <div style={{ marginBottom: 16 }}>
              <Text strong>任务执行结果：</Text>
            </div>
            
            {/* 尝试显示结构化信息 */}
            {result.success !== undefined && (
              <div style={{ marginBottom: 12 }}>
                <Text>执行状态: </Text>
                <Tag color={result.success ? 'green' : 'red'}>
                  {result.success ? '成功' : '失败'}
                </Tag>
              </div>
            )}
            
            {result.message && (
              <div style={{ marginBottom: 12 }}>
                <Text strong>消息: </Text>
                <Text>{result.message}</Text>
              </div>
            )}
            
            {result.error && (
              <div style={{ marginBottom: 12 }}>
                <Text strong>错误: </Text>
                <Text type="danger">{result.error}</Text>
              </div>
            )}
            
            <div style={{ marginTop: 16 }}>
              <Text strong>完整结果数据：</Text>
              <pre style={{ 
                background: '#f5f5f5', 
                padding: 12, 
                borderRadius: 4, 
                fontSize: '12px',
                maxHeight: '400px',
                overflow: 'auto',
                marginTop: 8
              }}>
                {JSON.stringify(result, null, 2)}
              </pre>
            </div>
            
            <div style={{ textAlign: 'center', marginTop: 16 }}>
              <Button 
                onClick={() => {
                  // 下载结果
                  const dataStr = JSON.stringify(result, null, 2);
                  const dataBlob = new Blob([dataStr], {type: 'application/json'});
                  const url = URL.createObjectURL(dataBlob);
                  const link = document.createElement('a');
                  link.href = url;
                  link.download = `任务结果_${new Date().toISOString().slice(0,10)}.json`;
                  link.click();
                  URL.revokeObjectURL(url);
                  message.success('任务结果已下载');
                }}
              >
                下载结果
              </Button>
            </div>
          </div>
        ),
      });
    }
  };

  // 获取用户列表
  const fetchUsers = async (storeIds = []) => {
    return new Promise(async (resolve) => {
      try {
        setLoadingUsers(true);
        const response = await apiService.project.user.getList();

        if (response && Array.isArray(response)) {
          // 处理返回的数据格式
          const allUsers = response
            .filter(item => item.success && item.data)
            .map(item => {
              const userData = item.data;
              // 确保用户数据包含必要的字段
              return {
                ...userData,
                // 如果没有name，使用username
                name: userData.name || userData.username,
                // 如果有多个门店名称，用逗号连接
                store_name: Array.isArray(userData.store_names) && userData.store_names.length > 0
                  ? userData.store_names.join(', ')
                  : ''
              };
            });

          setUsers(allUsers);

          // 如果选择了特定门店，过滤用户
          if (storeIds && storeIds.length > 0 && !storeIds.includes('all')) {
            // 过滤出选定门店的用户
            const storeUsers = allUsers.filter(user => {
              // 检查用户是否属于选定的门店
              if (Array.isArray(user.store_ids)) {
                return user.store_ids.some(storeId =>
                  storeIds.includes(storeId.toString())
                );
              }
              return false;
            });

            setFilteredUsers(storeUsers.length > 0 ? storeUsers : allUsers);
          } else {
            // 如果选择了"所有门店"或没有选择门店，显示所有用户
            setFilteredUsers(allUsers);
          }
        } else if (response && response.items) {
          // 兼容原有的数据格式
          const allUsers = response.items;
          setUsers(allUsers);
          setFilteredUsers(allUsers);
        } else {
          // 如果没有数据，设置为空数组
          setUsers([]);
          setFilteredUsers([]);
        }
        resolve(true);
      } catch (error) {
        console.error('获取用户列表失败:', error);
        message.error('获取用户列表失败');
        resolve(false);
      } finally {
        setLoadingUsers(false);
      }
    });
  };

  // 获取门店列表
  const fetchStores = async () => {
    try {
      const response = await apiService.project.store.getList();
      if (response && response.items) {
        setStores(response.items);
      }
    } catch (error) {
      console.error('获取门店列表失败:', error);
    }
  };

  // 处理门店选择变化
  const handleStoreChange = (values) => {
    // 检查是否选择了"所有门店"
    if (values && values.includes('all')) {
      // 如果选择了"所有门店"，移除其他所有选项
      form.setFieldsValue({ store_id: ['all'] });
      setSelectedStores(['all']);
      fetchUsers(); // 加载所有用户
    } else if (values && values.length > 0) {
      // 如果选择了特定门店，加载这些门店的用户
      setSelectedStores(values);
      fetchUsers(values);
    } else {
      // 如果没有选择任何门店，清空用户列表
      setSelectedStores([]);
      setFilteredUsers([]);
    }
  };

  // 处理文件上传前的验证
  const beforeUpload = (file) => {
    const isLt10M = file.size / 1024 / 1024 < 10;
    if (!isLt10M) {
      message.error('文件大小不能超过10MB!');
    }
    return isLt10M;
  };

  // 处理文件上传变化
  const handleFileChange = ({ fileList: newFileList }) => {
    setFileList(newFileList);
  };

  // 打开任务表单
  const handleOpenTaskForm = (task = null) => {
    setEditingTask(task);
    form.resetFields();

    if (task) {
      // 编辑任务
      const storeIds = task.store_id ?
        (Array.isArray(task.store_id) ? task.store_id : [task.store_id]) :
        [];

      // 如果是所有门店
      if (task.is_all_stores) {
        storeIds.push('all');
      }

      // 加载相关门店的用户
      setSelectedStores(storeIds);
      fetchUsers(storeIds);

      form.setFieldsValue({
        title: task.title,
        description: task.description,
        store_id: storeIds,
        category: task.category,
        task_type: task.task_type || 'planning',
        priority: task.priority,
        status: task.status,
        assignee_ids: task.assignee_ids,
        supervisor_ids: task.supervisor_ids,
        start_date: task.start_date ? dayjs(task.start_date) : undefined,
        due_date: task.due_date ? dayjs(task.due_date) : undefined
      });

      // 设置附件
      if (task.attachments && task.attachments.length > 0) {
        setFileList(task.attachments.map((url, index) => ({
          uid: `-${index}`,
          name: url.split('/').pop(),
          status: 'done',
          url
        })));
      } else {
        setFileList([]);
      }
    } else {
      // 创建任务
      setSelectedStores([]);
      setFilteredUsers(users); // 显示所有用户

      form.setFieldsValue({
        category: 'store',
        task_type: 'planning',
        priority: 'medium',
        status: 'pending',
        start_date: dayjs()
      });
      setFileList([]);
    }

    setModalVisible(true);
  };

  // 提交任务表单
  const handleSubmitTask = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      // 准备提交数据
      const storeIds = values.store_id || [];
      const isAllStores = storeIds.includes('all');

      // 处理门店选择
      let finalStoreIds = [];
      if (isAllStores) {
        // 如果选择了"所有门店"，获取所有门店ID
        finalStoreIds = stores.map(store => store.id);
      } else {
        // 否则使用选择的门店ID，但要过滤掉可能的 'all' 值
        finalStoreIds = storeIds.filter(id => id !== 'all');
      }

      // 确保至少有一个门店ID
      if (finalStoreIds.length === 0) {
        throw new Error('请至少选择一个门店');
      }

      // 确保日期格式正确
      let startDate = null;
      if (values.start_date) {
        // 使用 startOf('day') 确保只有日期部分
        startDate = values.start_date.startOf('day').format('YYYY-MM-DD');
      } else {
        startDate = dayjs().startOf('day').format('YYYY-MM-DD');
      }

      let dueDate = null;
      if (values.due_date) {
        dueDate = values.due_date.startOf('day').format('YYYY-MM-DD');
      }

      // 根据后端API要求，如果是单个门店，store_id应该是字符串而不是数组
      const taskData = {
        ...values,
        store_id: finalStoreIds.length === 1 ? finalStoreIds[0] : finalStoreIds,
        is_all_stores: isAllStores,
        start_date: startDate,
        due_date: dueDate,
        attachments: fileList.map(file => file.response?.url || file.url)
      };

      // 如果有task_type字段，将其转换为tags
      if (values.task_type) {
        taskData.tags = [values.task_type];
        delete taskData.task_type;
      }

      // 打印提交的数据，方便调试
      console.log('提交任务数据:', taskData);

      if (editingTask) {
        // 更新任务
        await apiService.project.routineTask.update(editingTask.id, taskData);
        message.success('任务更新成功');
      } else {
        // 创建任务
        await apiService.project.routineTask.create(taskData);
        message.success('任务创建成功');
      }

      setModalVisible(false);
      fetchTasks();
    } catch (error) {
      console.error('提交任务失败:', error);
      if (error.errorFields) {
        // 表单验证错误
        return;
      }
      // 显示更详细的错误信息
      if (error.response?.data?.detail) {
        if (Array.isArray(error.response.data.detail)) {
          // 如果是数组形式的错误信息
          const errorDetails = error.response.data.detail.map(err => {
            return `${err.loc.join('.')}：${err.msg}`;
          }).join('; ');
          message.error('提交任务失败: ' + errorDetails);
        } else {
          message.error('提交任务失败: ' + error.response.data.detail);
        }
      } else {
        message.error('提交任务失败: ' + error.message);
      }

      // 打印完整错误信息到控制台，方便调试
      console.error('提交任务失败详细信息:', error.response?.data);
    } finally {
      setLoading(false);
    }
  };

  // 删除任务
  const handleDeleteTask = (taskId) => {
    confirm({
      title: '确定要删除此任务吗?',
      icon: <ExclamationCircleOutlined />,
      content: '删除后无法恢复',
      okText: '确定',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          await apiService.project.routineTask.delete(taskId);
          message.success('任务删除成功');
          fetchTasks();
        } catch (error) {
          console.error('删除任务失败:', error);
          message.error('删除任务失败: ' + (error.message || '未知错误'));
        }
      }
    });
  };

  // 更新任务状态
  const handleUpdateTaskStatus = async (taskId, status) => {
    try {
      await apiService.project.routineTask.updateStatus(taskId, { status });
      message.success('任务状态更新成功');
      fetchTasks();
    } catch (error) {
      console.error('更新任务状态失败:', error);
      message.error('更新任务状态失败: ' + (error.message || '未知错误'));
    }
  };

  // 处理筛选条件变化
  const handleFilterChange = (key, value) => {
    setFilters({
      ...filters,
      [key]: value
    });
  };

  // 应用筛选条件
  const handleApplyFilters = () => {
    fetchTasks();
  };

  // 重置筛选条件
  const handleResetFilters = () => {
    setFilters({
      status: undefined,
      priority: undefined,
      category: undefined,
      assignee: undefined,
      dateRange: [dayjs().subtract(30, 'days'), dayjs()],
      search: ''
    });

    // 延迟执行，确保状态更新后再获取数据
    setTimeout(() => {
      fetchTasks();
    }, 0);
  };

  // 处理标签页切换
  const handleTabChange = (key) => {
    setActiveTab(key);
  };

  // 任务表格列定义
  const columns = [
    {
      title: '任务标题',
      dataIndex: 'title',
      key: 'title',
      render: (text, record) => (
        <a onClick={() => handleOpenTaskForm(record)}>{text}</a>
      )
    },
    {
      title: '门店',
      dataIndex: 'store_name',
      key: 'store_name',
      ellipsis: true,
      render: (text, record) => {
        if (record.is_all_stores) {
          return <Tag color="blue">所有门店</Tag>;
        } else if (record.store_names && record.store_names.length > 0) {
          // 使用 overlay 属性代替 title 属性，避免 findDOMNode 警告
          const content = record.store_names.join(', ');
          return (
            <Tooltip overlay={content}>
              <span>{record.store_names.length > 1 ? `${record.store_names[0]} 等 ${record.store_names.length} 个门店` : text}</span>
            </Tooltip>
          );
        } else {
          return text || '-';
        }
      }
    },
    {
      title: '类别',
      dataIndex: 'category',
      key: 'category',
      render: (text) => categoryTexts[text] || text
    },
    {
      title: '任务类型',
      dataIndex: 'task_type',
      key: 'task_type',
      render: (text) => taskTypeTexts[text] || text
    },
    {
      title: '优先级',
      dataIndex: 'priority',
      key: 'priority',
      render: (priority) => (
        <Tag color={priorityColors[priority] || 'default'}>
          {priorityTexts[priority] || priority}
        </Tag>
      )
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={statusColors[status] || 'default'}>
          {statusTexts[status] || status}
        </Tag>
      )
    },
    {
      title: '负责人',
      dataIndex: 'assignee_names',
      key: 'assignee_names',
      render: (names) => {
        if (!names || names.length === 0) return '未指派';
        if (names.length === 1) return names[0];
        // 使用 overlay 属性代替 title 属性，避免 findDOMNode 警告
        const content = names.join(', ');
        return (
          <Tooltip overlay={content}>
            <span>{names[0]} 等 {names.length} 人</span>
          </Tooltip>
        );
      }
    },
    {
      title: '截止日期',
      dataIndex: 'due_date',
      key: 'due_date',
      render: (text) => {
        if (!text) return '-';
        const date = dayjs(text);
        const isOverdue = date.isBefore(dayjs(), 'day');
        return (
          <span style={{ color: isOverdue ? '#f5222d' : 'inherit' }}>
            {date.format('YYYY-MM-DD')}
            {isOverdue && <Badge status="error" style={{ marginLeft: 8 }} />}
          </span>
        );
      }
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="small">
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => handleOpenTaskForm(record)}
          >
            编辑
          </Button>
          {record.status !== 'completed' && record.status !== 'cancelled' && (
            <Button
              type="text"
              icon={<CheckOutlined />}
              onClick={() => handleUpdateTaskStatus(record.id, 'completed')}
            >
              完成
            </Button>
          )}
          <Button
            type="text"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDeleteTask(record.id)}
          >
            删除
          </Button>
        </Space>
      )
    }
  ];

  return (
    <div className="task-management">
      <Card title="任务管理" bordered={false}>
        <Tabs activeKey={activeTab} onChange={handleTabChange}>
          <TabPane tab="任务列表" key="1">
            <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between' }}>
              <div>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => handleOpenTaskForm()}
                  style={{ marginRight: 8 }}
                >
                  新建任务
                </Button>
                <Button
                  icon={<SyncOutlined />}
                  onClick={() => fetchTasks()}
                  style={{ marginRight: 8 }}
                >
                  刷新
                </Button>
                {/* 管理员视图切换按钮 */}
                <Button
                  type={isAdminView ? "primary" : "default"}
                  onClick={() => {
                    setIsAdminView(!isAdminView);
                    // 切换视图后重新获取任务
                    setTimeout(() => fetchTasks(), 0);
                  }}
                >
                  {isAdminView ? "所有任务" : "我的任务"}
                </Button>
              </div>
            </div>

            {/* 筛选条件 */}
            <Card style={{ marginBottom: 16 }}>
              <Row gutter={16}>
                <Col span={6}>
                  <Form.Item label="状态">
                    <Select
                      placeholder="选择状态"
                      allowClear
                      style={{ width: '100%' }}
                      value={filters.status}
                      onChange={(value) => handleFilterChange('status', value)}
                    >
                      <Option value="pending">待处理</Option>
                      <Option value="in_progress">进行中</Option>
                      <Option value="completed">已完成</Option>
                      <Option value="overdue">已逾期</Option>
                      <Option value="cancelled">已取消</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item label="优先级">
                    <Select
                      placeholder="选择优先级"
                      allowClear
                      style={{ width: '100%' }}
                      value={filters.priority}
                      onChange={(value) => handleFilterChange('priority', value)}
                    >
                      <Option value="low">低</Option>
                      <Option value="medium">中</Option>
                      <Option value="high">高</Option>
                      <Option value="urgent">紧急</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item label="类别">
                    <Select
                      placeholder="选择类别"
                      allowClear
                      style={{ width: '100%' }}
                      value={filters.category}
                      onChange={(value) => handleFilterChange('category', value)}
                    >
                      <Option value="store">门店</Option>
                      <Option value="operation">运营</Option>
                      <Option value="marketing">营销</Option>
                      <Option value="inventory">库存</Option>
                      <Option value="finance">财务</Option>
                      <Option value="other">其他</Option>
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item label="任务类型">
                    <Select
                      placeholder="选择任务类型"
                      allowClear
                      style={{ width: '100%' }}
                      value={filters.task_type}
                      onChange={(value) => handleFilterChange('task_type', value)}
                    >
                      <Option value="planning">规划任务</Option>
                      <Option value="implementation">实施任务</Option>
                      <Option value="review">审核任务</Option>
                      <Option value="coordination">协调任务</Option>
                      <Option value="reporting">报告任务</Option>
                      <Option value="daily">日常任务</Option>
                      <Option value="weekly">周期任务</Option>
                      <Option value="monthly">月度任务</Option>
                      <Option value="other">其他任务</Option>
                    </Select>
                  </Form.Item>
                </Col>
              </Row>
              <Row gutter={16}>
                <Col span={6}>
                  <Form.Item label="负责人">
                    <Select
                      placeholder="选择负责人"
                      allowClear
                      style={{ width: '100%' }}
                      value={filters.assignee}
                      onChange={(value) => handleFilterChange('assignee', value)}
                    >
                      {users.map(user => (
                        <Option key={user.id} value={user.id}>{user.name || user.username}</Option>
                      ))}
                    </Select>
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item label="日期范围">
                    <RangePicker
                      style={{ width: '100%' }}
                      value={filters.dateRange}
                      onChange={(dates) => handleFilterChange('dateRange', dates)}
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item label="搜索">
                    <Input
                      placeholder="搜索任务标题或描述"
                      allowClear
                      value={filters.search}
                      onChange={(e) => handleFilterChange('search', e.target.value)}
                      prefix={<SearchOutlined />}
                    />
                  </Form.Item>
                </Col>
              </Row>
              <Row>
                <Col span={24} style={{ textAlign: 'right' }}>
                  <Button
                    type="primary"
                    icon={<FilterOutlined />}
                    onClick={handleApplyFilters}
                    style={{ marginRight: 8 }}
                  >
                    筛选
                  </Button>
                  <Button
                    onClick={handleResetFilters}
                  >
                    重置
                  </Button>
                </Col>
              </Row>
            </Card>

            {/* 任务表格 */}
            <Table
              columns={columns}
              dataSource={tasks}
              rowKey="id"
              loading={loading}
              pagination={{
                showSizeChanger: true,
                showTotal: (total) => `共 ${total} 条记录`,
                defaultPageSize: 10,
                pageSizeOptions: ['10', '20', '50']
              }}
            />
          </TabPane>
          <TabPane tab="任务统计" key="2">
            <div style={{ padding: '16px 0' }}>
              {/* 统计周期选择 */}
              <Card style={{ marginBottom: 16 }}>
                <Row gutter={16} align="middle">
                  <Col span={12}>
                    <Title level={4}>任务统计分析</Title>
                  </Col>
                  <Col span={12} style={{ textAlign: 'right' }}>
                    <Radio.Group
                      value={statsPeriod}
                      onChange={(e) => setStatsPeriod(e.target.value)}
                      buttonStyle="solid"
                    >
                      <Radio.Button value="week">最近一周</Radio.Button>
                      <Radio.Button value="month">最近一月</Radio.Button>
                      <Radio.Button value="year">最近一年</Radio.Button>
                    </Radio.Group>
                    <Button
                      icon={<SyncOutlined />}
                      style={{ marginLeft: 8 }}
                      onClick={fetchTaskStats}
                    >
                      刷新
                    </Button>
                  </Col>
                </Row>
              </Card>

              {/* 任务状态统计卡片 */}
              <Card title="任务状态统计" style={{ marginBottom: 16 }}>
                <Spin spinning={loadingStats}>
                  <Row gutter={16}>
                    <Col span={4}>
                      <Statistic
                        title="总任务数"
                        value={taskStats.total}
                        valueStyle={{ color: '#1890ff' }}
                      />
                    </Col>
                    <Col span={4}>
                      <Statistic
                        title="待处理"
                        value={taskStats.pending}
                        valueStyle={{ color: '#d9d9d9' }}
                      />
                      <Progress
                        percent={Math.round((taskStats.pending / taskStats.total || 0) * 100)}
                        status="normal"
                        size="small"
                        showInfo={false}
                      />
                    </Col>
                    <Col span={4}>
                      <Statistic
                        title="进行中"
                        value={taskStats.in_progress}
                        valueStyle={{ color: '#1890ff' }}
                      />
                      <Progress
                        percent={Math.round((taskStats.in_progress / taskStats.total || 0) * 100)}
                        status="active"
                        size="small"
                        showInfo={false}
                      />
                    </Col>
                    <Col span={4}>
                      <Statistic
                        title="已完成"
                        value={taskStats.completed}
                        valueStyle={{ color: '#52c41a' }}
                      />
                      <Progress
                        percent={Math.round((taskStats.completed / taskStats.total || 0) * 100)}
                        status="success"
                        size="small"
                        showInfo={false}
                      />
                    </Col>
                    <Col span={4}>
                      <Statistic
                        title="已逾期"
                        value={taskStats.overdue}
                        valueStyle={{ color: '#f5222d' }}
                      />
                      <Progress
                        percent={Math.round((taskStats.overdue / taskStats.total || 0) * 100)}
                        status="exception"
                        size="small"
                        showInfo={false}
                      />
                    </Col>
                    <Col span={4}>
                      <Statistic
                        title="已取消"
                        value={taskStats.cancelled}
                        valueStyle={{ color: '#faad14' }}
                      />
                      <Progress
                        percent={Math.round((taskStats.cancelled / taskStats.total || 0) * 100)}
                        strokeColor="#faad14"
                        size="small"
                        showInfo={false}
                      />
                    </Col>
                  </Row>

                  {/* 任务状态饼图 */}
                  <Row style={{ marginTop: 24 }}>
                    <Col span={24}>
                      <ReactECharts
                        option={{
                          tooltip: {
                            trigger: 'item',
                            formatter: '{a} <br/>{b}: {c} ({d}%)'
                          },
                          legend: {
                            orient: 'horizontal',
                            bottom: 'bottom',
                            data: Object.keys(statusTexts).map(key => statusTexts[key])
                          },
                          series: [
                            {
                              name: '任务状态',
                              type: 'pie',
                              radius: ['40%', '70%'],
                              avoidLabelOverlap: false,
                              itemStyle: {
                                borderRadius: 10,
                                borderColor: '#fff',
                                borderWidth: 2
                              },
                              label: {
                                show: false,
                                position: 'center'
                              },
                              emphasis: {
                                label: {
                                  show: true,
                                  fontSize: '18',
                                  fontWeight: 'bold'
                                }
                              },
                              labelLine: {
                                show: false
                              },
                              data: [
                                { value: taskStats.pending, name: statusTexts.pending, itemStyle: { color: '#d9d9d9' } },
                                { value: taskStats.in_progress, name: statusTexts.in_progress, itemStyle: { color: '#1890ff' } },
                                { value: taskStats.completed, name: statusTexts.completed, itemStyle: { color: '#52c41a' } },
                                { value: taskStats.overdue, name: statusTexts.overdue, itemStyle: { color: '#f5222d' } },
                                { value: taskStats.cancelled, name: statusTexts.cancelled, itemStyle: { color: '#faad14' } }
                              ]
                            }
                          ]
                        }}
                        style={{ height: '300px' }}
                      />
                    </Col>
                  </Row>
                </Spin>
              </Card>

              {/* 按门店和类别统计 */}
              <Row gutter={16}>
                <Col span={12}>
                  <Card title="按门店统计" style={{ marginBottom: 16 }}>
                    <Spin spinning={loadingStats}>
                      {storeStats.length > 0 ? (
                        <ReactECharts
                          option={{
                            tooltip: {
                              trigger: 'axis',
                              axisPointer: {
                                type: 'shadow'
                              }
                            },
                            grid: {
                              left: '3%',
                              right: '4%',
                              bottom: '3%',
                              containLabel: true
                            },
                            xAxis: {
                              type: 'value'
                            },
                            yAxis: {
                              type: 'category',
                              data: storeStats.slice(0, 10).map(item => item.name),
                              axisLabel: {
                                interval: 0,
                                rotate: 30
                              }
                            },
                            series: [
                              {
                                name: '任务数量',
                                type: 'bar',
                                data: storeStats.slice(0, 10).map(item => item.value)
                              }
                            ]
                          }}
                          style={{ height: '300px' }}
                        />
                      ) : (
                        <Empty description="暂无数据" />
                      )}
                    </Spin>
                  </Card>
                </Col>
                <Col span={12}>
                  <Card title="按类别统计" style={{ marginBottom: 16 }}>
                    <Spin spinning={loadingStats}>
                      {categoryStats.length > 0 ? (
                        <ReactECharts
                          option={{
                            tooltip: {
                              trigger: 'item',
                              formatter: '{a} <br/>{b}: {c} ({d}%)'
                            },
                            legend: {
                              orient: 'horizontal',
                              bottom: 'bottom'
                            },
                            series: [
                              {
                                name: '任务类别',
                                type: 'pie',
                                radius: '70%',
                                center: ['50%', '45%'],
                                data: categoryStats.map(item => ({
                                  name: item.name,
                                  value: item.value
                                })),
                                emphasis: {
                                  itemStyle: {
                                    shadowBlur: 10,
                                    shadowOffsetX: 0,
                                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                                  }
                                }
                              }
                            ]
                          }}
                          style={{ height: '300px' }}
                        />
                      ) : (
                        <Empty description="暂无数据" />
                      )}
                    </Spin>
                  </Card>
                </Col>
              </Row>

              {/* 按任务类型和优先级统计 */}
              <Row gutter={16}>
                <Col span={12}>
                  <Card title="按任务类型统计" style={{ marginBottom: 16 }}>
                    <Spin spinning={loadingStats}>
                      {typeStats.length > 0 ? (
                        <ReactECharts
                          option={{
                            tooltip: {
                              trigger: 'axis',
                              axisPointer: {
                                type: 'shadow'
                              }
                            },
                            grid: {
                              left: '3%',
                              right: '4%',
                              bottom: '3%',
                              containLabel: true
                            },
                            xAxis: {
                              type: 'value'
                            },
                            yAxis: {
                              type: 'category',
                              data: typeStats.map(item => item.name),
                              axisLabel: {
                                interval: 0,
                                rotate: 30
                              }
                            },
                            series: [
                              {
                                name: '任务数量',
                                type: 'bar',
                                data: typeStats.map(item => item.value)
                              }
                            ]
                          }}
                          style={{ height: '300px' }}
                        />
                      ) : (
                        <Empty description="暂无数据" />
                      )}
                    </Spin>
                  </Card>
                </Col>
                <Col span={12}>
                  <Card title="按优先级统计" style={{ marginBottom: 16 }}>
                    <Spin spinning={loadingStats}>
                      {priorityStats.length > 0 ? (
                        <ReactECharts
                          option={{
                            tooltip: {
                              trigger: 'item',
                              formatter: '{a} <br/>{b}: {c} ({d}%)'
                            },
                            legend: {
                              orient: 'horizontal',
                              bottom: 'bottom'
                            },
                            series: [
                              {
                                name: '优先级',
                                type: 'pie',
                                radius: '70%',
                                center: ['50%', '45%'],
                                data: priorityStats.map(item => ({
                                  name: item.name,
                                  value: item.value,
                                  itemStyle: {
                                    color: item.name === '低' ? '#52c41a' :
                                           item.name === '中' ? '#1890ff' :
                                           item.name === '高' ? '#faad14' :
                                           item.name === '紧急' ? '#f5222d' : undefined
                                  }
                                })),
                                emphasis: {
                                  itemStyle: {
                                    shadowBlur: 10,
                                    shadowOffsetX: 0,
                                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                                  }
                                }
                              }
                            ]
                          }}
                          style={{ height: '300px' }}
                        />
                      ) : (
                        <Empty description="暂无数据" />
                      )}
                    </Spin>
                  </Card>
                </Col>
              </Row>

              {/* 任务趋势统计 */}
              <Card title="任务创建趋势" style={{ marginBottom: 16 }}>
                <Spin spinning={loadingStats}>
                  {timeStats.length > 0 ? (
                    <ReactECharts
                      option={{
                        tooltip: {
                          trigger: 'axis',
                          axisPointer: {
                            type: 'cross',
                            label: {
                              backgroundColor: '#6a7985'
                            }
                          }
                        },
                        grid: {
                          left: '3%',
                          right: '4%',
                          bottom: '3%',
                          containLabel: true
                        },
                        xAxis: {
                          type: 'category',
                          boundaryGap: false,
                          data: timeStats.map(item => item.date)
                        },
                        yAxis: {
                          type: 'value'
                        },
                        series: [
                          {
                            name: '任务数量',
                            type: 'line',
                            stack: '总量',
                            areaStyle: {},
                            emphasis: {
                              focus: 'series'
                            },
                            data: timeStats.map(item => item.count)
                          }
                        ]
                      }}
                      style={{ height: '300px' }}
                    />
                  ) : (
                    <Empty description="暂无数据" />
                  )}
                </Spin>
              </Card>
            </div>
          </TabPane>
          <TabPane tab="异步任务监控" key="3">
            <div style={{ padding: '16px 0' }}>
              <Card title={
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <span>
                    <CloudServerOutlined style={{ marginRight: 8 }} />
                    系统异步任务监控
                  </span>
                  <Space>
                    {selectedAsyncTasks.length > 0 && (
                      <Button
                        danger
                        icon={<DeleteOutlined />}
                        onClick={batchDeleteAsyncTasks}
                      >
                        批量删除 ({selectedAsyncTasks.length})
                      </Button>
                    )}
                    <Button
                      icon={<SyncOutlined />}
                      onClick={fetchAsyncTasks}
                      loading={loadingAsyncTasks}
                    >
                      刷新
                    </Button>
                  </Space>
                </div>
              } bordered={false}>
                
                {/* 筛选条件 */}
                <Card style={{ marginBottom: 16 }}>
                  <Row gutter={16}>
                    <Col span={6}>
                      <Form.Item label="任务状态">
                        <Select
                          value={asyncTaskFilter}
                          onChange={(value) => setAsyncTaskFilter(value)}
                          style={{ width: '100%' }}
                        >
                          <Option value="all">全部状态</Option>
                          <Option value="pending">等待中</Option>
                          <Option value="running">运行中</Option>
                          <Option value="success">已完成</Option>
                          <Option value="failed">已失败</Option>
                          <Option value="cancelled">已取消</Option>
                        </Select>
                      </Form.Item>
                    </Col>
                    <Col span={6}>
                      <Form.Item label="任务模块">
                        <Select
                          value={asyncTaskModule}
                          onChange={(value) => setAsyncTaskModule(value)}
                          style={{ width: '100%' }}
                        >
                          <Option value="all">全部模块</Option>
                          <Option value="purchase">采购管理</Option>
                          <Option value="inventory">库存管理</Option>
                          <Option value="sales">销售管理</Option>
                          <Option value="ai">AI处理</Option>
                          <Option value="report">报表导出</Option>
                          <Option value="import">数据导入</Option>
                          <Option value="sync">数据同步</Option>
                        </Select>
                      </Form.Item>
                    </Col>
                    <Col span={6}>
                      <Form.Item label="搜索">
                        <Input
                          placeholder="搜索任务名称或类型"
                          value={asyncTaskSearch}
                          onChange={(e) => setAsyncTaskSearch(e.target.value)}
                          prefix={<SearchOutlined />}
                          allowClear
                        />
                      </Form.Item>
                    </Col>
                    <Col span={6}>
                      <Form.Item label="日期范围">
                        <RangePicker
                          value={asyncTaskDateRange}
                          onChange={(dates) => setAsyncTaskDateRange(dates)}
                          style={{ width: '100%' }}
                          format="YYYY-MM-DD"
                        />
                      </Form.Item>
                    </Col>
                  </Row>
                  <Row>
                    <Col span={24} style={{ textAlign: 'right' }}>
                      <Button
                        type="primary"
                        icon={<FilterOutlined />}
                        onClick={fetchAsyncTasks}
                        style={{ marginRight: 8 }}
                      >
                        筛选
                      </Button>
                      <Button
                        onClick={resetAsyncTaskFilters}
                      >
                        重置
                      </Button>
                    </Col>
                  </Row>
                </Card>

                <Spin spinning={loadingAsyncTasks}>
                  {asyncTasks.length > 0 ? (
                    <Table
                      rowSelection={{
                        selectedRowKeys: selectedAsyncTasks,
                        onChange: handleAsyncTaskSelection,
                        getCheckboxProps: (record) => ({
                          disabled: record.status === 'running', // 运行中的任务不能选择删除
                        }),
                      }}
                      columns={[
                        {
                          title: '任务名称',
                          dataIndex: 'task_name',
                          key: 'task_name',
                          ellipsis: true,
                          render: (text, record) => (
                            <div>
                              <div style={{ fontWeight: 'bold' }}>
                                <a onClick={() => viewTaskDetail(record)}>{text}</a>
                              </div>
                              <div style={{ fontSize: '12px', color: '#666' }}>
                                {record.task_type}
                              </div>
                            </div>
                          )
                        },
                        {
                          title: '状态',
                          dataIndex: 'status',
                          key: 'status',
                          width: 100,
                          render: (status) => {
                            const statusConfig = {
                              pending: { color: 'default', text: '等待中' },
                              running: { color: 'processing', text: '运行中' },
                              success: { color: 'success', text: '已完成' },
                              failed: { color: 'error', text: '已失败' },
                              cancelled: { color: 'warning', text: '已取消' }
                            };
                            const config = statusConfig[status] || { color: 'default', text: status };
                            return <Tag color={config.color}>{config.text}</Tag>;
                          }
                        },
                        {
                          title: '进度',
                          dataIndex: 'progress',
                          key: 'progress',
                          width: 120,
                          render: (progress, record) => (
                            <div>
                              <Progress 
                                percent={progress || 0} 
                                size="small" 
                                status={record.status === 'failed' ? 'exception' : 'normal'}
                              />
                            </div>
                          )
                        },
                        {
                          title: '创建时间',
                          dataIndex: 'created_at',
                          key: 'created_at',
                          width: 150,
                          render: (time) => time ? dayjs(time).format('MM-DD HH:mm') : '-'
                        },
                        {
                          title: '完成时间',
                          dataIndex: 'completed_at',
                          key: 'completed_at',
                          width: 150,
                          render: (time) => time ? dayjs(time).format('MM-DD HH:mm') : '-'
                        },
                        {
                          title: '操作',
                          key: 'action',
                          width: 200,
                          render: (_, record) => (
                            <Space size="small">
                              {record.status === 'success' && record.result_data && (
                                <Button
                                  type="link"
                                  size="small"
                                  icon={<EyeOutlined />}
                                  onClick={() => viewAsyncTaskResult(record.result_data)}
                                >
                                  查看结果
                                </Button>
                              )}
                              {(record.status === 'pending' || record.status === 'running') && (
                                <Button
                                  type="link"
                                  size="small"
                                  danger
                                  icon={<StopOutlined />}
                                  onClick={() => cancelAsyncTask(record.id)}
                                >
                                  取消
                                </Button>
                              )}
                              {record.status !== 'running' && (
                                <Popconfirm
                                  title="确定要删除此任务吗？"
                                  onConfirm={() => deleteAsyncTask(record.id)}
                                  okText="确定"
                                  cancelText="取消"
                                >
                                  <Button
                                    type="link"
                                    size="small"
                                    danger
                                    icon={<DeleteOutlined />}
                                  >
                                    删除
                                  </Button>
                                </Popconfirm>
                              )}
                            </Space>
                          )
                        }
                      ]}
                      dataSource={asyncTasks}
                      rowKey="id"
                      size="small"
                      pagination={{
                        showSizeChanger: true,
                        showTotal: (total) => `共 ${total} 条记录`,
                        defaultPageSize: 20,
                        pageSizeOptions: ['10', '20', '50']
                      }}
                    />
                  ) : (
                    <Empty 
                      description="暂无异步任务"
                      style={{ margin: '40px 0' }}
                    />
                  )}
                </Spin>
              </Card>
            </div>
          </TabPane>
          <TabPane tab="异步任务演示" key="4">
            <div style={{ padding: '16px 0' }}>
              <Card title="异步任务功能演示" bordered={false}>
                <AsyncTaskDemo />
              </Card>
            </div>
          </TabPane>
        </Tabs>

        {/* 任务表单弹窗 */}
        <Modal
          title={editingTask ? '编辑任务' : '新建任务'}
          visible={modalVisible}
          onOk={handleSubmitTask}
          onCancel={() => setModalVisible(false)}
          width={800}
          confirmLoading={loading}
        >
          <Form
            form={form}
            layout="vertical"
          >
            <Form.Item
              name="title"
              label="任务标题"
              rules={[{ required: true, message: '请输入任务标题' }]}
            >
              <Input placeholder="请输入任务标题" />
            </Form.Item>

            <Form.Item
              name="description"
              label="任务描述"
              rules={[{ required: true, message: '请输入任务描述' }]}
            >
              <TextArea rows={4} placeholder="请输入任务描述" />
            </Form.Item>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="store_id"
                  label="所属门店"
                  rules={[{ required: true, message: '请选择门店' }]}
                >
                  <Select
                    placeholder="请选择门店"
                    allowClear
                    showSearch
                    optionFilterProp="children"
                    onChange={(value) => handleStoreChange(value)}
                    mode="multiple"
                    maxTagCount={3}
                  >
                    <Option key="all" value="all">所有门店</Option>
                    {stores.map(store => (
                      <Option key={store.id} value={store.id}>{store.name}</Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="category"
                  label="任务类别"
                  rules={[{ required: true, message: '请选择任务类别' }]}
                >
                  <Select placeholder="请选择任务类别">
                    <Option value="store">门店</Option>
                    <Option value="operation">运营</Option>
                    <Option value="marketing">营销</Option>
                    <Option value="inventory">库存</Option>
                    <Option value="finance">财务</Option>
                    <Option value="other">其他</Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="task_type"
                  label="任务类型"
                  rules={[{ required: true, message: '请选择任务类型' }]}
                >
                  <Select placeholder="请选择任务类型">
                    <Option value="planning">规划任务</Option>
                    <Option value="implementation">实施任务</Option>
                    <Option value="review">审核任务</Option>
                    <Option value="coordination">协调任务</Option>
                    <Option value="reporting">报告任务</Option>
                    <Option value="daily">日常任务</Option>
                    <Option value="weekly">周期任务</Option>
                    <Option value="monthly">月度任务</Option>
                    <Option value="other">其他任务</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="priority"
                  label="优先级"
                  rules={[{ required: true, message: '请选择优先级' }]}
                >
                  <Select placeholder="请选择优先级">
                    <Option value="low">低</Option>
                    <Option value="medium">中</Option>
                    <Option value="high">高</Option>
                    <Option value="urgent">紧急</Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={24}>
                <Form.Item
                  name="assignee_ids"
                  label="负责人"
                  rules={[{ required: true, message: '请选择负责人' }]}
                >
                  <Select
                    mode="multiple"
                    placeholder="请选择负责人"
                    optionFilterProp="children"
                    loading={loadingUsers}
                    showSearch
                    maxTagCount={3}
                  >
                    {filteredUsers.map(user => (
                      <Option key={user.id} value={user.id}>
                        {user.name || user.username} {user.store_name ? `(${user.store_name})` : ''}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="start_date"
                  label="开始日期"
                  rules={[{ required: true, message: '请选择开始日期' }]}
                >
                  <DatePicker
                    style={{ width: '100%' }}
                    format="YYYY-MM-DD"
                    placeholder="请选择开始日期"
                    showTime={false} // 不显示时间选择器
                    onChange={(date) => {
                      if (date) {
                        // 确保只有日期部分
                        const dateOnly = date.startOf('day');
                        form.setFieldsValue({ start_date: dateOnly });
                      }
                    }}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="due_date"
                  label="截止日期"
                >
                  <DatePicker
                    style={{ width: '100%' }}
                    format="YYYY-MM-DD"
                    placeholder="请选择截止日期"
                    showTime={false} // 不显示时间选择器
                    onChange={(date) => {
                      if (date) {
                        // 确保只有日期部分
                        const dateOnly = date.startOf('day');
                        form.setFieldsValue({ due_date: dateOnly });
                      }
                    }}
                  />
                </Form.Item>
              </Col>
            </Row>

            <Form.Item
              name="status"
              label="状态"
              rules={[{ required: true, message: '请选择状态' }]}
            >
              <Select placeholder="请选择状态">
                <Option value="pending">待处理</Option>
                <Option value="in_progress">进行中</Option>
                <Option value="completed">已完成</Option>
                <Option value="overdue">已逾期</Option>
                <Option value="cancelled">已取消</Option>
              </Select>
            </Form.Item>

            <Form.Item
              name="supervisor_ids"
              label="监督人"
            >
              <Select
                mode="multiple"
                placeholder="请选择监督人"
                optionFilterProp="children"
                showSearch
                maxTagCount={3}
              >
                {users.map(user => (
                  <Option key={user.id} value={user.id}>{user.name || user.username}</Option>
                ))}
              </Select>
            </Form.Item>
          </Form>
        </Modal>

        {/* 任务详情抽屉 */}
        <Drawer
          title="任务详情"
          placement="right"
          width={600}
          onClose={() => setTaskDetailVisible(false)}
          visible={taskDetailVisible}
        >
          {selectedTask && (
            <div>
              <Card title="基本信息" size="small" style={{ marginBottom: 16 }}>
                <Row gutter={16}>
                  <Col span={12}>
                    <div style={{ marginBottom: 8 }}>
                      <Text strong>任务名称：</Text>
                      <Text>{selectedTask.task_name}</Text>
                    </div>
                  </Col>
                  <Col span={12}>
                    <div style={{ marginBottom: 8 }}>
                      <Text strong>任务类型：</Text>
                      <Text>{selectedTask.task_type}</Text>
                    </div>
                  </Col>
                  <Col span={12}>
                    <div style={{ marginBottom: 8 }}>
                      <Text strong>状态：</Text>
                      <Tag color={
                        selectedTask.status === 'success' ? 'success' :
                        selectedTask.status === 'failed' ? 'error' :
                        selectedTask.status === 'running' ? 'processing' :
                        selectedTask.status === 'cancelled' ? 'warning' : 'default'
                      }>
                        {selectedTask.status === 'pending' ? '等待中' :
                         selectedTask.status === 'running' ? '运行中' :
                         selectedTask.status === 'success' ? '已完成' :
                         selectedTask.status === 'failed' ? '已失败' :
                         selectedTask.status === 'cancelled' ? '已取消' : selectedTask.status}
                      </Tag>
                    </div>
                  </Col>
                  <Col span={12}>
                    <div style={{ marginBottom: 8 }}>
                      <Text strong>进度：</Text>
                      <Progress 
                        percent={selectedTask.progress || 0} 
                        size="small" 
                        style={{ width: 100 }}
                        status={selectedTask.status === 'failed' ? 'exception' : 'normal'}
                      />
                    </div>
                  </Col>
                </Row>
                
                {selectedTask.description && (
                  <div style={{ marginTop: 16 }}>
                    <Text strong>描述：</Text>
                    <div style={{ marginTop: 4, padding: 8, background: '#f5f5f5', borderRadius: 4 }}>
                      {selectedTask.description}
                    </div>
                  </div>
                )}
              </Card>

              <Card title="时间信息" size="small" style={{ marginBottom: 16 }}>
                <Row gutter={16}>
                  <Col span={12}>
                    <div style={{ marginBottom: 8 }}>
                      <Text strong>创建时间：</Text>
                      <Text>{selectedTask.created_at ? dayjs(selectedTask.created_at).format('YYYY-MM-DD HH:mm:ss') : '-'}</Text>
                    </div>
                  </Col>
                  <Col span={12}>
                    <div style={{ marginBottom: 8 }}>
                      <Text strong>开始时间：</Text>
                      <Text>{selectedTask.started_at ? dayjs(selectedTask.started_at).format('YYYY-MM-DD HH:mm:ss') : '-'}</Text>
                    </div>
                  </Col>
                  <Col span={12}>
                    <div style={{ marginBottom: 8 }}>
                      <Text strong>完成时间：</Text>
                      <Text>{selectedTask.completed_at ? dayjs(selectedTask.completed_at).format('YYYY-MM-DD HH:mm:ss') : '-'}</Text>
                    </div>
                  </Col>
                  <Col span={12}>
                    <div style={{ marginBottom: 8 }}>
                      <Text strong>耗时：</Text>
                      <Text>
                        {selectedTask.started_at && selectedTask.completed_at ? 
                          `${dayjs(selectedTask.completed_at).diff(dayjs(selectedTask.started_at), 'second')}秒` : 
                          selectedTask.started_at ? 
                            `${dayjs().diff(dayjs(selectedTask.started_at), 'second')}秒` : '-'
                        }
                      </Text>
                    </div>
                  </Col>
                </Row>
              </Card>

              {selectedTask.input_data && (
                <Card title="输入参数" size="small" style={{ marginBottom: 16 }}>
                  <pre style={{ 
                    background: '#f5f5f5', 
                    padding: 12, 
                    borderRadius: 4, 
                    fontSize: '12px',
                    maxHeight: '200px',
                    overflow: 'auto'
                  }}>
                    {JSON.stringify(selectedTask.input_data, null, 2)}
                  </pre>
                </Card>
              )}

              {selectedTask.error_message && (
                <Card title="错误信息" size="small" style={{ marginBottom: 16 }}>
                  <div style={{ 
                    background: '#fff2f0', 
                    border: '1px solid #ffccc7',
                    padding: 12, 
                    borderRadius: 4,
                    color: '#a8071a'
                  }}>
                    {selectedTask.error_message}
                  </div>
                </Card>
              )}

              {selectedTask.result_data && (
                <Card title="执行结果" size="small" style={{ marginBottom: 16 }}>
                  <div style={{ marginBottom: 12 }}>
                    <Button 
                      type="primary" 
                      size="small"
                      onClick={() => viewAsyncTaskResult(selectedTask.result_data)}
                      style={{ marginRight: 8 }}
                    >
                      查看详细结果
                    </Button>
                    <Button 
                      size="small"
                      onClick={() => {
                        const dataStr = JSON.stringify(selectedTask.result_data, null, 2);
                        const dataBlob = new Blob([dataStr], {type: 'application/json'});
                        const url = URL.createObjectURL(dataBlob);
                        const link = document.createElement('a');
                        link.href = url;
                        link.download = `任务结果_${selectedTask.task_name}_${new Date().toISOString().slice(0,10)}.json`;
                        link.click();
                        URL.revokeObjectURL(url);
                        message.success('任务结果已下载');
                      }}
                    >
                      下载结果
                    </Button>
                  </div>
                  <pre style={{ 
                    background: '#f5f5f5', 
                    padding: 12, 
                    borderRadius: 4, 
                    fontSize: '12px',
                    maxHeight: '300px',
                    overflow: 'auto'
                  }}>
                    {JSON.stringify(selectedTask.result_data, null, 2)}
                  </pre>
                </Card>
              )}

              <div style={{ textAlign: 'center', marginTop: 24 }}>
                <Space>
                  {(selectedTask.status === 'pending' || selectedTask.status === 'running') && (
                    <Button 
                      danger
                      onClick={() => {
                        cancelAsyncTask(selectedTask.id);
                        setTaskDetailVisible(false);
                      }}
                    >
                      取消任务
                    </Button>
                  )}
                  {selectedTask.status !== 'running' && (
                    <Button 
                      danger
                      onClick={() => {
                        Modal.confirm({
                          title: '确认删除',
                          content: '确定要删除此任务吗？此操作不可恢复。',
                          onOk: () => {
                            deleteAsyncTask(selectedTask.id);
                            setTaskDetailVisible(false);
                          }
                        });
                      }}
                    >
                      删除任务
                    </Button>
                  )}
                  <Button onClick={() => setTaskDetailVisible(false)}>
                    关闭
                  </Button>
                </Space>
              </div>
            </div>
          )}
        </Drawer>
      </Card>
    </div>
  );
};

export default TaskManagement;
