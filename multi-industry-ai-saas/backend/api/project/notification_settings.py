from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query, Path, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import func, and_, or_
import uuid
from datetime import datetime

from db.database import get_db
from models.notification_settings import NotificationSettings
from models.user import User
from schemas.notification_settings import (
    NotificationSettingsCreate, NotificationSettingsUpdate, NotificationSettingsResponse,
    WorkflowNotificationSettings, WorkflowNotificationSettingsUpdate
)
from api.deps import get_current_user, get_current_project_id, get_current_tenant_id

router = APIRouter()

# 通知设置API
@router.get("", response_model=NotificationSettingsResponse)
async def get_notification_settings(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    project_id: uuid.UUID = Depends(get_current_project_id)
):
    """
    获取通知设置
    """
    try:
        # 查询通知设置
        query = select(NotificationSettings).where(
            NotificationSettings.project_id == project_id,
            NotificationSettings.user_id == current_user.id
        )
        result = await db.execute(query)
        settings = result.scalar_one_or_none()
        
        if not settings:
            # 如果不存在，创建默认设置
            settings = NotificationSettings(
                id=uuid.uuid4(),
                tenant_id=current_user.tenant_id,
                project_id=project_id,
                user_id=current_user.id
            )
            db.add(settings)
            await db.commit()
            await db.refresh(settings)
        
        return {
            "success": True,
            "data": settings
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取通知设置失败: {str(e)}"
        )


@router.put("", response_model=NotificationSettingsResponse)
async def update_notification_settings(
    settings_update: NotificationSettingsUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    project_id: uuid.UUID = Depends(get_current_project_id)
):
    """
    更新通知设置
    """
    try:
        # 查询通知设置
        query = select(NotificationSettings).where(
            NotificationSettings.project_id == project_id,
            NotificationSettings.user_id == current_user.id
        )
        result = await db.execute(query)
        settings = result.scalar_one_or_none()
        
        if not settings:
            # 如果不存在，创建默认设置
            settings = NotificationSettings(
                id=uuid.uuid4(),
                tenant_id=current_user.tenant_id,
                project_id=project_id,
                user_id=current_user.id
            )
            db.add(settings)
            await db.flush()
        
        # 更新字段
        update_data = settings_update.dict(exclude_unset=True)
        
        for key, value in update_data.items():
            setattr(settings, key, value)
        
        settings.updated_at = datetime.now()
        
        await db.commit()
        await db.refresh(settings)
        
        return {
            "success": True,
            "message": "通知设置更新成功",
            "data": settings
        }
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新通知设置失败: {str(e)}"
        )


@router.get("/workflow/{workflow_type}", response_model=Dict[str, Any])
async def get_workflow_notification_settings(
    workflow_type: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    project_id: uuid.UUID = Depends(get_current_project_id)
):
    """
    获取工作流通知设置
    """
    try:
        # 查询通知设置
        query = select(NotificationSettings).where(
            NotificationSettings.project_id == project_id,
            NotificationSettings.user_id == current_user.id
        )
        result = await db.execute(query)
        settings = result.scalar_one_or_none()
        
        if not settings:
            # 如果不存在，创建默认设置
            settings = NotificationSettings(
                id=uuid.uuid4(),
                tenant_id=current_user.tenant_id,
                project_id=project_id,
                user_id=current_user.id
            )
            db.add(settings)
            await db.commit()
            await db.refresh(settings)
        
        # 获取工作流通知设置
        workflow_settings = settings.workflow_notifications.get(workflow_type, {})
        
        # 如果没有设置，返回默认设置
        if not workflow_settings:
            if workflow_type == "loss_report":
                workflow_settings = {
                    "enabled": True,
                    "notify_type": "roles",
                    "notify_roles": ["purchase", "admin"],
                    "notify_users": [],
                    "title": "门店报损审核通知",
                    "template": "门店 {{store_name}} 提交了产品 {{product_name}} 的报损申请，数量: {{quantity}}，金额: {{amount}}元，请及时审核"
                }
            else:
                workflow_settings = {
                    "enabled": True,
                    "notify_type": "roles",
                    "notify_roles": ["admin"],
                    "notify_users": [],
                    "title": f"{workflow_type}通知",
                    "template": f"有新的{workflow_type}需要处理"
                }
        
        return {
            "success": True,
            "data": workflow_settings
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取工作流通知设置失败: {str(e)}"
        )


@router.put("/workflow/{workflow_type}", response_model=Dict[str, Any])
async def update_workflow_notification_settings(
    workflow_type: str,
    settings_update: WorkflowNotificationSettingsUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    project_id: uuid.UUID = Depends(get_current_project_id)
):
    """
    更新工作流通知设置
    """
    try:
        # 查询通知设置
        query = select(NotificationSettings).where(
            NotificationSettings.project_id == project_id,
            NotificationSettings.user_id == current_user.id
        )
        result = await db.execute(query)
        settings = result.scalar_one_or_none()
        
        if not settings:
            # 如果不存在，创建默认设置
            settings = NotificationSettings(
                id=uuid.uuid4(),
                tenant_id=current_user.tenant_id,
                project_id=project_id,
                user_id=current_user.id,
                workflow_notifications={}
            )
            db.add(settings)
            await db.flush()
        
        # 获取工作流通知设置
        workflow_settings = settings.workflow_notifications.get(workflow_type, {})
        
        # 如果没有设置，创建默认设置
        if not workflow_settings:
            if workflow_type == "loss_report":
                workflow_settings = {
                    "enabled": True,
                    "notify_type": "roles",
                    "notify_roles": ["purchase", "admin"],
                    "notify_users": [],
                    "title": "门店报损审核通知",
                    "template": "门店 {{store_name}} 提交了产品 {{product_name}} 的报损申请，数量: {{quantity}}，金额: {{amount}}元，请及时审核"
                }
            else:
                workflow_settings = {
                    "enabled": True,
                    "notify_type": "roles",
                    "notify_roles": ["admin"],
                    "notify_users": [],
                    "title": f"{workflow_type}通知",
                    "template": f"有新的{workflow_type}需要处理"
                }
        
        # 更新字段
        update_data = settings_update.dict(exclude_unset=True)
        
        for key, value in update_data.items():
            workflow_settings[key] = value
        
        # 更新工作流通知设置
        if not settings.workflow_notifications:
            settings.workflow_notifications = {}
        
        settings.workflow_notifications[workflow_type] = workflow_settings
        settings.updated_at = datetime.now()
        
        await db.commit()
        await db.refresh(settings)
        
        return {
            "success": True,
            "message": "工作流通知设置更新成功",
            "data": workflow_settings
        }
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新工作流通知设置失败: {str(e)}"
        )
