import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  Button,
  message as antMessage,
  Modal,
  Typography,
  List,
  Avatar,
  Tag,
  Divider,
  Input,
  Form,
  Switch,
  Result,
  Empty,
  Row,
  Col
} from 'antd';
import {
  TrophyOutlined,
  HistoryOutlined,
  UserOutlined,
  SoundOutlined,
  LoadingOutlined,
  GiftOutlined,
  SmileOutlined,
  QuestionOutlined
} from '@ant-design/icons';
import marketingGameApi from '../service/api/marketingGame';
import './CardDraw.css';

const { Title, Paragraph } = Typography;

// 默认奖品图标
const DEFAULT_PRIZE_ICONS = {
  none: 'https://img.icons8.com/color/96/000000/gift--v1.png',
  coupon: 'https://img.icons8.com/color/96/000000/coupon--v1.png',
  points: 'https://img.icons8.com/color/96/000000/prize--v1.png',
  product: 'https://img.icons8.com/color/96/000000/shopping-bag--v1.png',
  thanks: 'https://img.icons8.com/color/96/000000/sad--v1.png'
};

// 主题背景图片
const THEME_BACKGROUNDS = {
  classic: 'https://img.freepik.com/free-vector/abstract-colorful-shapes-background_23-2148769631.jpg',
  apple: 'https://img.freepik.com/free-vector/gradient-white-monochrome-background_23-2149011361.jpg',
  cartoon: 'https://img.freepik.com/free-vector/flat-abstract-colorful-decorative-background_23-2148874272.jpg',
  business: 'https://img.freepik.com/free-vector/white-abstract-background_23-2148810113.jpg',
  ghibli: 'https://img.freepik.com/free-vector/watercolor-nature-background-with-leaves_52683-61481.jpg'
};

// 卡片背面图案
const CARD_BACKS = {
  classic: 'https://img.freepik.com/free-vector/abstract-pattern-design_1053-517.jpg',
  apple: 'https://img.freepik.com/free-vector/gradient-geometric-pattern-background_52683-30746.jpg',
  cartoon: 'https://img.freepik.com/free-vector/cute-pattern-with-doodle-cartoon-style_1308-86900.jpg',
  business: 'https://img.freepik.com/free-vector/abstract-blue-geometric-pattern-background-vector_53876-140255.jpg',
  ghibli: 'https://img.freepik.com/free-vector/watercolor-nature-pattern-background_52683-67583.jpg'
};

/**
 * 抽卡式随机抽奖组件
 * 完全重写的新版本，支持多种主题风格
 */
const CardDraw = ({ gameId }) => {
  // 状态管理
  const [loading, setLoading] = useState(true);
  const [game, setGame] = useState(null);
  const [prizes, setPrizes] = useState([]);
  const [cards, setCards] = useState([]);
  const [flippedCards, setFlippedCards] = useState([]);
  const [winningCard, setWinningCard] = useState(null);
  const [showResult, setShowResult] = useState(false);
  const [remainingDraws, setRemainingDraws] = useState(0);
  const [soundEnabled, setSoundEnabled] = useState(true);
  const [showPrizes, setShowPrizes] = useState(false);
  const [showRecords, setShowRecords] = useState(false);
  const [showRules, setShowRules] = useState(false);
  const [records, setRecords] = useState([]);
  const [receiptVerified, setReceiptVerified] = useState(false);
  const [receiptCode, setReceiptCode] = useState('');
  const [receiptAmount, setReceiptAmount] = useState('');
  const [verifying, setVerifying] = useState(false);
  const [phoneVerified, setPhoneVerified] = useState(false);
  const [phone, setPhone] = useState('');
  const [verifyingPhone, setVerifyingPhone] = useState(false);
  const [showVerification, setShowVerification] = useState(false);
  const [isDrawing, setIsDrawing] = useState(false);
  const [winningPrize, setWinningPrize] = useState(null);

  // 引用
  const flipSound = useRef(new Audio(require('../assets/audio/card-flip.mp3')));
  const winSound = useRef(new Audio(require('../assets/audio/win.mp3')));
  const loseSound = useRef(new Audio(require('../assets/audio/lose.mp3')));

  // 主题样式配置
  const themeStyles = {
    classic: {
      cardBorderColor: '#ff5722',
      cardShadowColor: '#ffeb3b',
      buttonBgColor: '#ff5722',
      textColor: '#333'
    },
    apple: {
      cardBorderColor: '#007AFF',
      cardShadowColor: '#5AC8FA',
      buttonBgColor: '#007AFF',
      textColor: '#333'
    },
    cartoon: {
      cardBorderColor: '#FF6B6B',
      cardShadowColor: '#FFE66D',
      buttonBgColor: '#FF6B6B',
      textColor: '#333'
    },
    business: {
      cardBorderColor: '#34495E',
      cardShadowColor: '#BDC3C7',
      buttonBgColor: '#3498DB',
      textColor: '#2C3E50'
    },
    ghibli: {
      cardBorderColor: '#1A7A89',
      cardShadowColor: '#D4A017',
      buttonBgColor: '#1A7A89',
      textColor: '#333'
    }
  };

  // 获取当前主题样式
  const getCurrentTheme = (themeStyle = 'classic') => {
    return themeStyles[themeStyle] || themeStyles.classic;
  };

  // 将fetchGameData提升到组件作用域
  const fetchGameData = useCallback(async () => {
    try {
      setLoading(true);

      // 获取游戏详情
      const gameResponse = await marketingGameApi.getGameDetail(gameId);
      if (!gameResponse.success) {
        antMessage.error(gameResponse.message || '获取游戏详情失败');
        setLoading(false);
        return;
      }

      // 确保game.remaining_draws有默认值
      const gameData = {
        ...gameResponse.data,
        remaining_draws: gameResponse.data.remaining_draws ||
                         gameResponse.data.max_draws_per_user ||
                         0,
        // 添加默认配置
        settings: {
          ...gameResponse.data.settings,
          enable_receipt_verification:
            gameResponse.data.settings?.enable_receipt_verification !== undefined
              ? gameResponse.data.settings.enable_receipt_verification
              : gameResponse.data.min_consumption > 0,
          theme_style: gameResponse.data.settings?.theme_style || 'classic',
          card_count: gameResponse.data.settings?.card_count || 9
        }
      };

      setGame(gameData);
      setRemainingDraws(gameData.remaining_draws);

      // 获取奖品列表
      const prizesResponse = await marketingGameApi.getPrizes(gameId);
      if (!prizesResponse.success) {
        antMessage.error(prizesResponse.message || '获取奖品列表失败');
        setLoading(false);
        return;
      }

      // 确保至少有一个奖品
      let prizeList = prizesResponse.data || [];
      const defaultPrize = {
        id: 'default',
        name: '谢谢参与',
        image_url: DEFAULT_PRIZE_ICONS.thanks,
        probability: 0,
        is_default: true
      };

      if (prizeList.length === 0) {
        prizeList.push(defaultPrize);
      }

      setPrizes(prizeList);

      // 初始化卡片
      initializeCards(gameData.settings.card_count || 9);

      // 获取中奖记录
      const recordsResponse = await marketingGameApi.getDrawRecords(gameId);
      if (recordsResponse.success) {
        setRecords(recordsResponse.data || []);
      }

      setLoading(false);
    } catch (error) {
      console.error('Error fetching game data:', error);
      antMessage.error('获取游戏数据失败，请稍后再试');
      setLoading(false);
    }
  }, [gameId]);

  // 初始化卡片
  const initializeCards = (count) => {
    const newCards = Array(count).fill(null).map((_, index) => ({
      id: `card_${index}`,
      flipped: false,
      index
    }));
    setCards(newCards);
    setFlippedCards([]);
    setWinningCard(null);
  };

  // 初始化
  useEffect(() => {
    fetchGameData();
  }, [fetchGameData]);

  // 处理卡片点击
  const handleCardClick = async (_, index) => {
    // 如果已经在抽奖中或者卡片已经翻开，则不处理
    if (isDrawing || flippedCards.includes(index) || !remainingDraws) {
      return;
    }

    // 检查是否需要验证
    if (game?.settings?.enable_receipt_verification && !receiptVerified) {
      setShowVerification(true);
      return;
    }

    if (game?.settings?.enable_phone_verification && !phoneVerified) {
      setShowVerification(true);
      return;
    }

    try {
      setIsDrawing(true);

      // 播放翻牌音效
      if (soundEnabled) {
        flipSound.current.play();
      }

      // 翻开卡片
      setFlippedCards(prev => [...prev, index]);

      // 调用抽奖API
      const response = await marketingGameApi.draw(gameId);
      if (!response.success) {
        antMessage.error(response.message || '抽奖失败');
        setIsDrawing(false);
        return;
      }

      const winPrize = response.data;
      setWinningPrize(winPrize);
      setWinningCard(index);

      // 更新剩余抽奖次数
      setRemainingDraws(prev => Math.max(0, prev - 1));

      // 延迟显示结果
      setTimeout(() => {
        setShowResult(true);
        setIsDrawing(false);

        // 播放音效
        if (soundEnabled) {
          if (winPrize.is_default) {
            loseSound.current.play();
          } else {
            winSound.current.play();
          }
        }

        // 刷新中奖记录
        fetchGameData();
      }, 1000);
    } catch (error) {
      console.error('Error during draw:', error);
      antMessage.error('抽奖失败，请稍后再试');
      setIsDrawing(false);
    }
  };

  // 重置卡片
  const resetCards = () => {
    const cardCount = game?.settings?.card_count || 9;
    initializeCards(cardCount);
  };

  // 验证小票
  const handleVerifyReceipt = async () => {
    if (!receiptCode) {
      antMessage.error('请输入小票号码');
      return;
    }

    if (game?.min_consumption > 0 && (!receiptAmount || parseFloat(receiptAmount) < game.min_consumption)) {
      antMessage.error(`小票金额不能低于${game.min_consumption}元`);
      return;
    }

    try {
      setVerifying(true);

      // 调用验证API
      const response = await marketingGameApi.verifyReceipt(gameId, {
        receipt_code: receiptCode,
        amount: receiptAmount
      });

      if (response.success) {
        antMessage.success('小票验证成功');
        setReceiptVerified(true);
        setShowVerification(false);
      } else {
        antMessage.error(response.message || '小票验证失败');
      }
    } catch (error) {
      console.error('Error verifying receipt:', error);
      antMessage.error('小票验证失败，请稍后再试');
    } finally {
      setVerifying(false);
    }
  };

  // 验证手机号
  const handleVerifyPhone = async () => {
    if (!phone || !/^1[3-9]\d{9}$/.test(phone)) {
      antMessage.error('请输入有效的手机号码');
      return;
    }

    try {
      setVerifyingPhone(true);

      // 调用验证API
      const response = await marketingGameApi.verifyPhone(gameId, {
        phone: phone
      });

      if (response.success) {
        antMessage.success('手机号验证成功');
        setPhoneVerified(true);
        setShowVerification(false);
      } else {
        antMessage.error(response.message || '手机号验证失败');
      }
    } catch (error) {
      console.error('Error verifying phone:', error);
      antMessage.error('手机号验证失败，请稍后再试');
    } finally {
      setVerifyingPhone(false);
    }
  };

  // 渲染加载状态
  if (loading) {
    return (
      <div className="card-draw-loading">
        <LoadingOutlined className="loading-icon" />
        <div className="loading-text">加载中，请稍候...</div>
      </div>
    );
  }

  // 获取当前主题
  const themeStyle = game?.settings?.theme_style || 'classic';
  const currentTheme = getCurrentTheme(themeStyle);
  const backgroundImage = THEME_BACKGROUNDS[themeStyle] || THEME_BACKGROUNDS.classic;
  const cardBackImage = CARD_BACKS[themeStyle] || CARD_BACKS.classic;

  return (
    <div
      className="card-draw-container"
      style={{ backgroundImage: `url(${backgroundImage})` }}
    >
      <div className="card-draw-header">
        <img src={game?.logo_url || DEFAULT_PRIZE_ICONS.none} alt="游戏Logo" className="game-logo" />
        <Title level={2} className="game-title">{game?.name || '幸运抽卡'}</Title>
        <Paragraph className="game-description">{game?.description || '翻开卡片，赢取精美礼品'}</Paragraph>
      </div>

      <div className="card-draw-content">
        <div className="settings-bar">
          <div className="settings-item">
            <span className="settings-label">音效</span>
            <Switch
              checked={soundEnabled}
              onChange={setSoundEnabled}
              size="small"
              checkedChildren={<SoundOutlined />}
              unCheckedChildren={<SoundOutlined />}
            />
          </div>
        </div>

        <div className="remaining-draws">
          剩余抽奖次数: <span className="remaining-count">{remainingDraws}</span>
        </div>

        <div className="cards-container">
          <Row gutter={[16, 16]} justify="center">
            {cards.map((card, index) => (
              <Col key={card.id} xs={8} sm={8} md={8} lg={8} xl={8}>
                <div
                  className={`card ${flippedCards.includes(index) ? 'flipped' : ''} ${winningCard === index ? 'winning' : ''}`}
                  onClick={() => handleCardClick(card.id, index)}
                  style={{
                    borderColor: currentTheme.cardBorderColor,
                    boxShadow: `0 0 10px ${currentTheme.cardShadowColor}`
                  }}
                >
                  <div className="card-inner">
                    <div
                      className="card-front"
                      style={{ backgroundImage: `url(${cardBackImage})` }}
                    >
                      <div className="card-question">
                        <QuestionOutlined />
                      </div>
                    </div>
                    <div className="card-back">
                      {winningCard === index && winningPrize && (
                        <div className="card-prize">
                          <img
                            src={winningPrize.image_url || DEFAULT_PRIZE_ICONS.none}
                            alt={winningPrize.name}
                            className="prize-image"
                          />
                          <div className="prize-name">{winningPrize.name}</div>
                        </div>
                      )}
                      {flippedCards.includes(index) && winningCard !== index && (
                        <div className="card-empty">
                          <img
                            src={DEFAULT_PRIZE_ICONS.thanks}
                            alt="谢谢参与"
                            className="prize-image"
                          />
                          <div className="prize-name">谢谢参与</div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </Col>
            ))}
          </Row>
        </div>

        <div className="card-draw-actions">
          <Button
            type="primary"
            onClick={resetCards}
            disabled={isDrawing || !flippedCards.length}
            className="reset-button"
            style={{ backgroundColor: currentTheme.buttonBgColor }}
          >
            重置卡片
          </Button>
        </div>

        <div className="action-buttons">
          <Button
            icon={<TrophyOutlined />}
            onClick={() => setShowPrizes(true)}
            className="action-button"
          >
            奖品设置
          </Button>
          <Button
            icon={<HistoryOutlined />}
            onClick={() => setShowRecords(true)}
            className="action-button"
          >
            中奖记录
          </Button>
          <Button
            icon={<UserOutlined />}
            onClick={() => setShowRules(true)}
            className="action-button"
          >
            活动规则
          </Button>
        </div>
      </div>

      <div className="card-draw-footer">
        <div>© {new Date().getFullYear()} {game?.company_name || '营销游戏'} 版权所有</div>
        <div className="footer-links">
          <a href="/privacy" className="footer-link">隐私政策</a>
          <a href="/terms" className="footer-link">用户协议</a>
        </div>
      </div>

      {/* 中奖结果弹窗 */}
      <Modal
        open={showResult}
        footer={null}
        onCancel={() => setShowResult(false)}
        width={400}
        centered
        closable={false}
        styles={{ body: { padding: '30px 20px' } }}
      >
        <Result
          icon={winningPrize?.is_default ? <SmileOutlined /> : <GiftOutlined />}
          status={winningPrize?.is_default ? 'info' : 'success'}
          title={winningPrize?.is_default ? '谢谢参与' : '恭喜中奖'}
          subTitle={winningPrize?.name}
          extra={[
            <Button key="close" onClick={() => setShowResult(false)}>
              关闭
            </Button>,
            !winningPrize?.is_default && (
              <Button key="claim" type="primary" onClick={() => setShowResult(false)}>
                领取奖品
              </Button>
            )
          ].filter(Boolean)}
        />
      </Modal>

      {/* 奖品列表弹窗 */}
      <Modal
        title="奖品列表"
        open={showPrizes}
        onCancel={() => setShowPrizes(false)}
        footer={null}
        width={600}
      >
        <List
          itemLayout="horizontal"
          dataSource={prizes.filter(p => !p.is_default)}
          renderItem={item => (
            <List.Item>
              <List.Item.Meta
                avatar={<Avatar src={item.image_url || DEFAULT_PRIZE_ICONS.none} />}
                title={item.name}
                description={
                  <>
                    <div>{item.description}</div>
                    <div>
                      <Tag color="blue">概率: {item.probability}%</Tag>
                      <Tag color="green">剩余: {item.remaining_quantity || '不限'}</Tag>
                    </div>
                  </>
                }
              />
            </List.Item>
          )}
          locale={{
            emptyText: <Empty description="暂无奖品" />
          }}
        />
      </Modal>

      {/* 中奖记录弹窗 */}
      <Modal
        title="中奖记录"
        open={showRecords}
        onCancel={() => setShowRecords(false)}
        footer={null}
        width={600}
      >
        <List
          itemLayout="horizontal"
          dataSource={records.filter(r => !r.prize?.is_default)}
          renderItem={item => (
            <List.Item>
              <List.Item.Meta
                avatar={<Avatar src={item.prize?.image_url || DEFAULT_PRIZE_ICONS.none} />}
                title={item.prize?.name}
                description={
                  <>
                    <div>用户: {item.user_name || '匿名用户'}</div>
                    <div>时间: {new Date(item.created_at).toLocaleString()}</div>
                  </>
                }
              />
            </List.Item>
          )}
          locale={{
            emptyText: <Empty description="暂无中奖记录" />
          }}
        />
      </Modal>

      {/* 活动规则弹窗 */}
      <Modal
        title="活动规则"
        open={showRules}
        onCancel={() => setShowRules(false)}
        footer={null}
        width={600}
      >
        <div>
          <Paragraph>
            <strong>活动时间：</strong>
            {game?.start_time && game?.end_time
              ? `${new Date(game.start_time).toLocaleDateString()} 至 ${new Date(game.end_time).toLocaleDateString()}`
              : '长期有效'}
          </Paragraph>

          <Paragraph>
            <strong>参与方式：</strong>
            {game?.settings?.enable_receipt_verification
              ? `消费满${game?.min_consumption || 0}元，凭小票参与抽奖`
              : '无门槛参与'}
          </Paragraph>

          <Paragraph>
            <strong>抽奖次数：</strong>
            每人每天最多可抽奖{game?.max_draws_per_user || '不限'}次
          </Paragraph>

          <Divider />

          <Paragraph>
            <strong>奖品设置：</strong>
          </Paragraph>

          <List
            itemLayout="horizontal"
            dataSource={prizes.filter(p => !p.is_default)}
            renderItem={item => (
              <List.Item>
                <List.Item.Meta
                  avatar={<Avatar src={item.image_url || DEFAULT_PRIZE_ICONS.none} />}
                  title={item.name}
                  description={item.description}
                />
              </List.Item>
            )}
            locale={{
              emptyText: <Empty description="暂无奖品" />
            }}
          />

          <Divider />

          <Paragraph>
            <strong>注意事项：</strong>
          </Paragraph>

          <Paragraph>
            1. 实物奖品请在中奖后30天内领取，逾期视为自动放弃。
          </Paragraph>

          <Paragraph>
            2. 活动最终解释权归{game?.company_name || '主办方'}所有。
          </Paragraph>
        </div>
      </Modal>

      {/* 验证弹窗 */}
      <Modal
        title="验证信息"
        open={showVerification}
        onCancel={() => setShowVerification(false)}
        footer={null}
        width={400}
      >
        <div>
          {game?.settings?.enable_phone_verification && !phoneVerified && (
            <>
              <Form layout="vertical">
                <Form.Item
                  label="手机号码"
                  required
                  rules={[{ required: true, message: '请输入手机号码' }]}
                >
                  <Input
                    placeholder="请输入手机号码"
                    value={phone}
                    onChange={e => setPhone(e.target.value)}
                  />
                </Form.Item>
              </Form>

              <div style={{ textAlign: 'right', marginBottom: 16 }}>
                <Button
                  type="primary"
                  onClick={handleVerifyPhone}
                  loading={verifyingPhone}
                >
                  验证手机号
                </Button>
              </div>

              <Divider />
            </>
          )}

          {game?.settings?.enable_receipt_verification && !receiptVerified && (
            <Form layout="vertical">
              <Form.Item
                label="小票号码"
                required
                rules={[{ required: true, message: '请输入小票号码' }]}
              >
                <Input
                  placeholder="请输入小票号码"
                  value={receiptCode}
                  onChange={e => setReceiptCode(e.target.value)}
                />
              </Form.Item>

              {game?.min_consumption > 0 && (
                <Form.Item
                  label={`消费金额（最低${game.min_consumption}元）`}
                  required
                  rules={[{ required: true, message: '请输入消费金额' }]}
                >
                  <Input
                    placeholder="请输入消费金额"
                    value={receiptAmount}
                    onChange={e => setReceiptAmount(e.target.value)}
                    type="number"
                    min={game.min_consumption}
                    addonAfter="元"
                  />
                </Form.Item>
              )}

              <div style={{ textAlign: 'right' }}>
                <Button
                  type="primary"
                  onClick={handleVerifyReceipt}
                  loading={verifying}
                >
                  验证小票
                </Button>
              </div>
            </Form>
          )}

          {phoneVerified && receiptVerified && (
            <Result
              status="success"
              title="验证成功"
              subTitle="您已完成所有验证，可以开始抽奖了"
              extra={[
                <Button
                  key="draw"
                  type="primary"
                  onClick={() => {
                    setShowVerification(false);
                  }}
                >
                  开始抽奖
                </Button>
              ]}
            />
          )}
        </div>
      </Modal>
    </div>
  );
};

export default CardDraw;
