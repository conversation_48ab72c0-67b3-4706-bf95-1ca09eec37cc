import api from '../httpClient';
import { getProjectId } from '../httpClient';

const knowledgeBase = {
  // 获取知识库文档列表
  getList: async (params) => {
    const projectId = getProjectId();
    return api.get(`/project/${projectId}/knowledge-base`, { params });
  },

  // 获取知识库文档详情
  getDetail: async (documentId) => {
    const projectId = getProjectId();
    return api.get(`/project/${projectId}/knowledge-base/${documentId}`);
  },

  // 获取知识库分类
  getCategories: async () => {
    const projectId = getProjectId();
    return api.get(`/project/${projectId}/knowledge-base/categories`);
  },

  // 创建知识库分类
  createCategory: async (data) => {
    const projectId = getProjectId();
    return api.post(`/project/${projectId}/knowledge-base/categories`, data);
  },

  // 创建知识库文档
  create: async (data) => {
    const projectId = getProjectId();
    return api.post(`/project/${projectId}/knowledge-base`, data);
  },

  // 更新知识库文档
  update: async (id, data) => {
    const projectId = getProjectId();
    return api.put(`/project/${projectId}/knowledge-base/${id}`, data);
  },

  // 删除知识库文档
  delete: async (id) => {
    const projectId = getProjectId();
    return api.delete(`/project/${projectId}/knowledge-base/${id}`);
  },

  // 获取可用的 AI 知识库列表
  getAvailableAIKnowledgeBases: async () => {
    const projectId = getProjectId();
    return api.get(`/project/${projectId}/knowledge-base/ai-knowledge-bases`);
  },

  // 获取文档已同步到的 AI 知识库列表
  getDocumentAIKnowledgeBases: async (documentId) => {
    const projectId = getProjectId();
    return api.get(`/project/${projectId}/knowledge-base/${documentId}/ai-knowledge-bases`);
  },

  // 将文档同步到 AI 知识库
  syncToAIKnowledgeBase: async (documentId, knowledgeBaseId) => {
    const projectId = getProjectId();
    return api.post(`/project/${projectId}/knowledge-base/${documentId}/sync-to-ai?knowledge_base_id=${knowledgeBaseId}`);
  }
};

export default knowledgeBase;