#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
端点路由

负责管理API端点
"""

import logging
import uuid
import os
import json
from datetime import datetime
from typing import Dict, Any, List, Optional
import copy # 确保导入 copy 模块

from fastapi import APIRouter, Depends, HTTPException, Body, Query, Path, Request
from sqlalchemy import and_, func, or_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from pydantic import BaseModel

from db.database import get_db
from models.user import User
from core.auth import get_current_user

from ..models import SAPIEndpoint, SAPIConfig, APIMCPToolMapping
from .. import router

logger = logging.getLogger(__name__)

# TODO: USER - Please verify and correct this import based on your project structure
# from ....main import app as main_fastapi_app # Example: if main.py is three levels up
# from ...main import app as main_fastapi_app # Previous attempt with 3 dots
# from ..main import app as main_fastapi_app # User's attempt with 2 dots

# Using AppContext to get the app instance
# from backend.core.app_context import AppContext # Commented out original import

# --- BEGIN DIAGNOSTIC BLOCK ---
# All diagnostic code related to sys.path, os.getcwd, and AppContext import attempts is removed.
# --- END DIAGNOSTIC BLOCK ---

# 新增的辅助函数
def _resolve_openapi_ref(ref_string: str, components: Dict[str, Any], openapi_spec: Dict[str, Any], depth: int = 0, max_depth: int = 5) -> Optional[Dict[str, Any]]:
    """
    解析 OpenAPI $ref 字符串。
    - ref_string: The $ref string, e.g., "#/components/schemas/MyModel".
    - components: The "components" section of the OpenAPI spec.
    - openapi_spec: The entire OpenAPI spec, used if a $ref itself contains another $ref (recursive resolution).
    - depth: Current recursion depth.
    - max_depth: Maximum allowed recursion depth.
    """
    if not ref_string.startswith("#/"):
        logger.warning(f"Unsupported $ref format: {ref_string}. Only local refs like '#/components/schemas/Name' are currently supported.")
        return None

    path_parts = ref_string.strip("#/").split('/')
    
    current_level = openapi_spec # Start with the full spec for resolving paths like #/components/...
    for part in path_parts:
        if isinstance(current_level, dict) and part in current_level:
            current_level = current_level[part]
        elif isinstance(current_level, list) and part.isdigit() and int(part) < len(current_level):
            current_level = current_level[int(part)]
        else:
            logger.warning(f"Could not find path part '{part}' in $ref '{ref_string}' within the OpenAPI spec.")
            return None

    resolved_component = copy.deepcopy(current_level)

    # 递归解析 $ref 内部可能存在的其他 $ref (简单实现，避免无限循环)
    # 注意：更健壮的实现需要处理循环引用和最大深度
    MAX_RECURSION_DEPTH = 5 # 防止无限递归
    
    def _recursive_resolve(node: Any, current_depth: int) -> Any:
        if current_depth > MAX_RECURSION_DEPTH:
            logger.warning(f"Max recursion depth reached while resolving $refs within {ref_string}")
            return node # 返回未完全解析的节点

        if isinstance(node, dict):
            if "$ref" in node and isinstance(node["$ref"], str):
                # 再次调用 _resolve_openapi_ref 来解析嵌套的 $ref
                # 但这次是从完整的 openapi_spec 开始，因为嵌套的 $ref 路径也是从根开始的
                nested_ref_path = node["$ref"]
                # 传递 openapi_spec 给下一次递归调用
                replacement = _resolve_openapi_ref(nested_ref_path, components, openapi_spec, current_depth + 1, max_depth)
                return replacement if replacement else node # 如果解析失败，则保留原始 $ref
            
            new_dict = {}
            for k, v in node.items():
                if k == "required" and isinstance(v, list): # Specifically handle 'required' lists
                    sanitized_required_list = []
                    original_required_list = v # For logging
                    for item in original_required_list:
                        if isinstance(item, str):
                            sanitized_required_list.append(item)
                        else:
                            # ref_string is from the outer scope of _resolve_openapi_ref
                            # It represents the top-level $ref that initiated this resolution chain.
                            logger.warning(
                                f"OpenAPI schema definition contains non-string item '{item}' in 'required' list "
                                f"for a sub-schema (part of resolved $ref: {ref_string}). This item will be skipped. "
                                f"Original required list segment: {original_required_list}"
                            )
                    new_dict[k] = sanitized_required_list
                else:
                    new_dict[k] = _recursive_resolve(v, current_depth + 1)
            return new_dict
        elif isinstance(node, list):
            new_list = []
            for item in node:
                new_list.append(_recursive_resolve(item, current_depth + 1))
            return new_list
        else:
            return node

    resolved_target = copy.deepcopy(resolved_component)
    for k, v in resolved_target.items():
        if isinstance(v, dict) and "$ref" in v:
            # If a value is a $ref, resolve it recursively
            # Depth check prevents infinite recursion for circular dependencies
            if depth < max_depth:
                nested_resolved = _recursive_resolve(v, 0)
                # If nested_resolved is a dictionary, update it in place in resolved_target
                # This is to handle cases where a $ref resolves to a component that itself has $refs
                if isinstance(nested_resolved, dict):
                    resolved_target[k] = nested_resolved
                # else:
                #     resolved_target[k] = v # Keep original $ref if resolution failed or max depth
            else:
                ref_to_log = v.get("$ref", "UNKNOWN_REF_VALUE")
                logger.warning(f"Max recursion depth {max_depth} reached resolving $ref '{ref_to_log}'. Keeping original $ref.")
                resolved_target[k] = v # Keep original $ref if max depth reached

    return resolved_target

# 获取端点列表
@router.get("/endpoints", response_model=Dict[str, Any])
async def get_endpoints(
    config_id: Optional[str] = None,
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(10, ge=1, le=100, description="每页数量"),
    search: Optional[str] = Query(None, description="搜索关键词，支持端点名称、描述和路径"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """获取端点列表"""
    try:
        # 构建查询
        query = select(SAPIEndpoint)

        # 应用过滤条件
        if config_id:
            query = query.where(SAPIEndpoint.config_id == config_id)

        # 添加搜索条件
        if search:
            search_term = f"%{search}%"
            query = query.where(
                or_(
                    SAPIEndpoint.name.ilike(search_term),
                    SAPIEndpoint.description.ilike(search_term),
                    SAPIEndpoint.endpoint_path.ilike(search_term)
                )
            )

        # 查询总数
        count_query = select(func.count()).select_from(query.subquery())
        result = await db.execute(count_query)
        total = result.scalar()

        # 添加分页
        query = query.offset((page - 1) * page_size).limit(page_size)

        # 执行查询
        result = await db.execute(query)
        endpoints = result.scalars().all()

        # 构建响应
        result_list = [] # Renamed from result to avoid conflict with db result
        for endpoint in endpoints:
            result_list.append({
                "id": str(endpoint.id),
                "name": endpoint.name,
                "description": endpoint.description,
                "endpoint_path": endpoint.endpoint_path,
                "method": endpoint.method,
                "requires_auth": endpoint.requires_auth,
                "allowed_roles": endpoint.allowed_roles,
                "parameters": endpoint.parameters,
                "enabled": endpoint.enabled,
                "created_at": endpoint.created_at.isoformat() if endpoint.created_at else None,
                "updated_at": endpoint.updated_at.isoformat() if endpoint.updated_at else None,
                "config_id": str(endpoint.config_id)
            })

        return {
            "success": True,
            "message": "获取端点列表成功",
            "data": result_list,
            "total": total,
            "page": page,
            "page_size": page_size
        }
    except Exception as e:
        logger.error(f"获取端点列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取端点列表失败: {str(e)}")

# 获取端点详情
@router.get("/endpoints/{endpoint_id}", response_model=Dict[str, Any])
async def get_endpoint(
    endpoint_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """获取端点详情"""
    try:
        # 查询端点
        result = await db.execute(select(SAPIEndpoint).where(SAPIEndpoint.id == uuid.UUID(endpoint_id))) # Ensure endpoint_id is UUID
        endpoint = result.scalars().first()
        if not endpoint:
            raise HTTPException(status_code=404, detail="端点不存在")

        # 构建响应
        response_data = { # Renamed from result
            "id": str(endpoint.id),
            "name": endpoint.name,
            "description": endpoint.description,
            "endpoint_path": endpoint.endpoint_path,
            "method": endpoint.method,
            "requires_auth": endpoint.requires_auth,
            "allowed_roles": endpoint.allowed_roles,
            "parameters": endpoint.parameters,
            "response_template": endpoint.response_template,
            "enabled": endpoint.enabled,
            "created_at": endpoint.created_at.isoformat() if endpoint.created_at else None,
            "updated_at": endpoint.updated_at.isoformat() if endpoint.updated_at else None,
            "config_id": str(endpoint.config_id)
        }

        return {
            "success": True,
            "message": "获取端点详情成功",
            "data": response_data
        }
    except HTTPException:
        raise
    except ValueError: # Handle invalid UUID format for endpoint_id
        logger.error(f"获取端点详情失败: 无效的 endpoint_id 格式 '{endpoint_id}'")
        raise HTTPException(status_code=400, detail=f"无效的 endpoint_id 格式")
    except Exception as e:
        logger.error(f"获取端点详情失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取端点详情失败: {str(e)}")

# 创建端点
@router.post("/endpoints", response_model=Dict[str, Any])
async def create_endpoint(
    endpoint_data: dict = Body(...),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """创建端点"""
    try:
        # 检查配置是否存在
        config_id_str = endpoint_data.get("config_id")
        if not config_id_str:
            raise HTTPException(status_code=400, detail="config_id is required.")
        try:
            config_id = uuid.UUID(config_id_str)
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid config_id format.")

        result = await db.execute(select(SAPIConfig).where(SAPIConfig.id == config_id))
        config = result.scalars().first()
        if not config:
            raise HTTPException(status_code=404, detail="配置不存在")

        # 检查端点路径是否已存在
        # endpoint_path_value = endpoint_data.get("endpoint_path") # old name
        # method_value = endpoint_data.get("method") # old name
        
        # Renaming for clarity within this scope
        current_endpoint_path = endpoint_data.get("endpoint_path")
        current_method = endpoint_data.get("method")

        result = await db.execute(
            select(SAPIEndpoint).where(
                and_(
                    SAPIEndpoint.endpoint_path == current_endpoint_path,
                    SAPIEndpoint.method == current_method
                )
            )
        )
        existing_endpoint = result.scalars().first()

        if existing_endpoint:
            raise HTTPException(status_code=400, detail="端点路径已存在")

        # 处理端点路径，确保格式正确
        raw_endpoint_path = endpoint_data.get("endpoint_path")
        if not raw_endpoint_path or not isinstance(raw_endpoint_path, str):
            raise HTTPException(status_code=400, detail="endpoint_path is required and must be a string.")

        # 1. Ensure /api/v1/ prefix and disallow others for external SAPI endpoints
        if not raw_endpoint_path.startswith("/api/v1/"):
            logger.error(f"Invalid endpoint_path prefix: '{raw_endpoint_path}'. Must start with /api/v1/.")
            raise HTTPException(status_code=400, detail="Invalid endpoint_path prefix. Must start with /api/v1/ for SAPI tools.")

        normalized_path = raw_endpoint_path

        # 2. Remove duplicate /v1/ if present (e.g. /api/v1/v1/ -> /api/v1/)
        if "/api/v1/v1/" in normalized_path:
            normalized_path = normalized_path.replace("/api/v1/v1/", "/api/v1/")
            logger.warning(f"Corrected duplicate /v1 in path: {raw_endpoint_path} -> {normalized_path}")

        # 3. Normalize projectId placeholder and ensure its presence for /project/ routes
        # Standardize to {project_id} (lowercase)
        if "{projectId}" in normalized_path: # Check for common alternative
            normalized_path = normalized_path.replace("{projectId}", "{project_id}")
            logger.warning(f"Normalized projectId placeholder: {raw_endpoint_path} -> {normalized_path}")

        # If it's a project-specific path but doesn't have the placeholder, add it.
        # Example: /api/v1/project/stores -> /api/v1/project/{project_id}/stores
        # More robust checking: if '/project/' is followed by something that isn't '{project_id}'
        project_segment_index = normalized_path.find("/project/")
        if project_segment_index != -1:
            path_after_project_segment = normalized_path[project_segment_index + len("/project/"):]
            if not path_after_project_segment.startswith("{project_id}"):
                # Insert {project_id} before the next segment
                segments = path_after_project_segment.split('/', 1)
                normalized_path_temp = normalized_path[:project_segment_index + len("/project/")] + "{project_id}" # Use temp var
                if segments and segments[0]: # Check if segments[0] is not empty
                    normalized_path_temp += "/" + "/".join(segments)
                # Ensure no double slashes if segments was empty or just one segment
                normalized_path = normalized_path_temp.replace("//", "/")
                logger.warning(f"Added missing {{project_id}} placeholder: {raw_endpoint_path} -> {normalized_path}")

        endpoint_path = normalized_path

        # 创建端点
        new_endpoint = SAPIEndpoint(
            id=uuid.uuid4(),
            name=endpoint_data.get("name"),
            description=endpoint_data.get("description"),
            endpoint_path=endpoint_path,  # 使用修正后的路径
            method=endpoint_data.get("method"),
            requires_auth=endpoint_data.get("requires_auth", True),
            allowed_roles=endpoint_data.get("allowed_roles"),
            parameters=endpoint_data.get("parameters", {}),
            response_template=endpoint_data.get("response_template"),
            enabled=endpoint_data.get("enabled", True),
            config_id=config.id, # Use the UUID version
            created_at=datetime.now(),
            updated_at=datetime.now()
        )

        db.add(new_endpoint)
        await db.commit()
        await db.refresh(new_endpoint)

        return {
            "success": True,
            "message": "端点创建成功",
            "data": {
                "id": str(new_endpoint.id),
                "name": new_endpoint.name,
                "endpoint_path": new_endpoint.endpoint_path,
                "method": new_endpoint.method
            }
        }
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"创建端点失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"创建端点失败: {str(e)}")

# 更新端点
@router.put("/endpoints/{endpoint_id}", response_model=Dict[str, Any])
async def update_endpoint(
    endpoint_id: str, # Should be validated as UUID
    endpoint_data: dict = Body(...),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """更新端点"""
    try:
        try:
            endpoint_uuid = uuid.UUID(endpoint_id)
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid endpoint_id format.")

        # 查询端点
        result = await db.execute(select(SAPIEndpoint).where(SAPIEndpoint.id == endpoint_uuid))
        endpoint = result.scalars().first()
        if not endpoint:
            raise HTTPException(status_code=404, detail="端点不存在")

        # 检查端点路径是否已存在 (if path or method is being updated)
        if "endpoint_path" in endpoint_data or "method" in endpoint_data:
            check_path = endpoint_data.get("endpoint_path", endpoint.endpoint_path)
            check_method = endpoint_data.get("method", endpoint.method)

            result = await db.execute(
                select(SAPIEndpoint).where(
                    and_(
                        SAPIEndpoint.endpoint_path == check_path,
                        SAPIEndpoint.method == check_method,
                        SAPIEndpoint.id != endpoint_uuid # Exclude current endpoint
                    )
                )
            )
            existing_endpoint = result.scalars().first()

            if existing_endpoint:
                raise HTTPException(status_code=400, detail="端点路径已存在")

        # 更新字段
        if "name" in endpoint_data:
            endpoint.name = endpoint_data["name"] # Use direct access if key is present
        if "description" in endpoint_data:
            endpoint.description = endpoint_data["description"]
        if "endpoint_path" in endpoint_data:
            # 处理端点路径，确保格式正确
            raw_endpoint_path = endpoint_data["endpoint_path"]
            if not isinstance(raw_endpoint_path, str): # Basic type check
                raise HTTPException(status_code=400, detail="endpoint_path must be a string if provided.")

            if not raw_endpoint_path.startswith("/api/v1/"):
                logger.error(f"Invalid endpoint_path prefix for update: '{raw_endpoint_path}'. Must start with /api/v1/.")
                raise HTTPException(status_code=400, detail="Invalid endpoint_path prefix for update. Must start with /api/v1/.")

            normalized_path = raw_endpoint_path

            if "/api/v1/v1/" in normalized_path:
                normalized_path = normalized_path.replace("/api/v1/v1/", "/api/v1/")
                logger.warning(f"Update: Corrected duplicate /v1 in path: {raw_endpoint_path} -> {normalized_path}")

            if "{projectId}" in normalized_path:
                normalized_path = normalized_path.replace("{projectId}", "{project_id}")
                logger.warning(f"Update: Normalized projectId placeholder: {raw_endpoint_path} -> {normalized_path}")

            project_segment_index = normalized_path.find("/project/")
            if project_segment_index != -1:
                path_after_project_segment = normalized_path[project_segment_index + len("/project/"):]
                if path_after_project_segment and not path_after_project_segment.startswith("{") :
                    segments = path_after_project_segment.split('/', 1)
                    normalized_path_temp = normalized_path[:project_segment_index + len("/project/")] + "{project_id}"
                    if segments and segments[0]: # Ensure there's a segment to join
                        normalized_path_temp += "/" + "/".join(segments)
                    normalized_path = normalized_path_temp.replace("//", "/") # Ensure no double slashes
                    logger.warning(f"Update: Added missing {{project_id}} placeholder: {raw_endpoint_path} -> {normalized_path}")

            endpoint.endpoint_path = normalized_path
        if "method" in endpoint_data:
            endpoint.method = endpoint_data["method"]
        if "requires_auth" in endpoint_data:
            endpoint.requires_auth = endpoint_data["requires_auth"]
        if "allowed_roles" in endpoint_data:
            endpoint.allowed_roles = endpoint_data["allowed_roles"]
        if "parameters" in endpoint_data:
            endpoint.parameters = endpoint_data["parameters"]
        if "response_template" in endpoint_data:
            endpoint.response_template = endpoint_data["response_template"]
        if "enabled" in endpoint_data:
            endpoint.enabled = endpoint_data["enabled"]
        if "config_id" in endpoint_data:
            # 检查配置是否存在
            config_id_str = endpoint_data["config_id"]
            try:
                new_config_id = uuid.UUID(config_id_str)
            except ValueError:
                raise HTTPException(status_code=400, detail="Invalid config_id format in update.")
            
            result = await db.execute(select(SAPIConfig).where(SAPIConfig.id == new_config_id))
            config = result.scalars().first()
            if not config:
                raise HTTPException(status_code=404, detail="配置不存在 (config_id for update).")
            endpoint.config_id = new_config_id

        endpoint.updated_at = datetime.now()

        await db.commit()
        await db.refresh(endpoint)

        return {
            "success": True,
            "message": "端点更新成功",
            "data": {
                "id": str(endpoint.id),
                "name": endpoint.name,
                "endpoint_path": endpoint.endpoint_path,
                "method": endpoint.method
            }
        }
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"更新端点失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"更新端点失败: {str(e)}")

# 删除端点
@router.delete("/endpoints/{endpoint_id}", response_model=Dict[str, Any])
async def delete_endpoint(
    endpoint_id: str, # Validate as UUID
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """删除端点"""
    try:
        try:
            endpoint_uuid = uuid.UUID(endpoint_id)
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid endpoint_id format.")
        # 查询端点
        result = await db.execute(select(SAPIEndpoint).where(SAPIEndpoint.id == endpoint_uuid))
        endpoint = result.scalars().first()
        if not endpoint:
            raise HTTPException(status_code=404, detail="端点不存在")

        # 删除端点
        await db.delete(endpoint)
        await db.commit()

        return {
            "success": True,
            "message": "端点删除成功"
        }
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"删除端点失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除端点失败: {str(e)}")

# 同步端点
@router.post("/endpoints/sync", response_model=Dict[str, Any])
async def sync_endpoints(
    request: Request, 
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """从FastAPI应用同步API端点到数据库"""
    try:
        logger.info("开始同步API端点...")
        
        # 动态导入主 FastAPI 应用实例
        # 注意: 这里的导入路径非常关键，取决于项目的目录结构
        # 用户需要确保这个导入是正确的。
        # 我们尝试从 request.app 获取，这通常是可行的。
        main_fastapi_app = request.app
        
        if not hasattr(main_fastapi_app, "openapi"):
            logger.error("无法从 request.app 获取 FastAPI 应用实例或实例没有 openapi 方法。")
            raise HTTPException(status_code=500, detail="无法获取FastAPI应用实例以同步端点。")

        openapi_schema = main_fastapi_app.openapi()
        if not openapi_schema:
            logger.error("获取 OpenAPI schema 失败。")
            raise HTTPException(status_code=500, detail="获取 OpenAPI schema 失败。")

        openapi_components = openapi_schema.get("components", {}) # 获取 components 部分以解析 $ref

        # 获取所有现有的 SAPI 端点，用于后续比较和更新
        existing_sapi_endpoints_result = await db.execute(select(SAPIEndpoint))
        existing_sapi_endpoints_list = existing_sapi_endpoints_result.scalars().all()
        existing_endpoints_map = {(e.endpoint_path, e.method.upper()): e for e in existing_sapi_endpoints_list}
        
        synced_endpoint_ids = set()
        endpoints_created_count = 0
        endpoints_updated_count = 0
        endpoints_skipped_count = 0
        endpoints_failed_count = 0

        paths = openapi_schema.get("paths", {})
        sapi_config_id = None # SAPI配置ID，假设所有同步的端点都关联到一个默认的或特定的SAPI配置

        # 尝试获取一个默认的SAPI配置，如果不存在则创建一个
        default_config_name = "Default SAPI Config"
        sapi_config_result = await db.execute(select(SAPIConfig).where(SAPIConfig.name == default_config_name))
        sapi_config = sapi_config_result.scalars().first()

        if not sapi_config:
            logger.info(f"未找到名为 '{default_config_name}' 的SAPI配置，将创建一个新的。")
            sapi_config = SAPIConfig(
                id=uuid.uuid4(),
                name=default_config_name,
                model_name="default_model", # 或者其他合适的默认值
                enabled=True,
                created_by_id=current_user.id if current_user else None # 关联创建者
            )
            db.add(sapi_config)
            await db.flush() # 需要 flush 来获取 ID
            logger.info(f"已创建新的SAPI配置: {sapi_config.id}")
        
        sapi_config_id = sapi_config.id

        for path_str, path_item_obj in paths.items():
            if not isinstance(path_item_obj, dict):
                logger.warning(f"路径项 {path_str} 不是一个字典，已跳过。")
                continue

            # 将路径中的 FastAPI 风格参数 {param} 转换为 OpenAPI 风格 {param}
            # (实际上FastAPI已经这样做了, 但确保一下)
            # openapi_path_str = path_str 

            for method_str, operation_obj in path_item_obj.items():
                method_upper = method_str.upper()
                if method_upper not in ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS", "HEAD"]:
                    continue

                if not isinstance(operation_obj, dict):
                    logger.warning(f"操作项 {path_str} {method_upper} 不是一个字典，已跳过。")
                    continue

                # 跳过SAPI插件自身的路由, 以及 /docs, /openapi.json 等
                # 同时跳过 /mcp/ 和 /ws/ 开头的路径 (MCP服务器和WebSocket相关的路径)
                if (path_str.startswith("/api/v1/sapi/") or
                    path_str.startswith("/sapi/") or
                    path_str in ["/docs", "/openapi.json", "/redoc"] or
                    path_str.startswith("/mcp/") or
                    path_str.startswith("/ws/")):
                    logger.debug(f"跳过内部或文档路径: {method_upper} {path_str}")
                    endpoints_skipped_count += 1
                    continue

                # 从 operation_obj 中提取信息
                summary = operation_obj.get("summary", "")
                operation_id = operation_obj.get("operationId", "")
                description = operation_obj.get("description", "")
                tags = operation_obj.get("tags", [])

                # 构建名称
                name = summary or operation_id or f"{method_upper} {path_str}"

                # 构建描述
                endpoint_description = description or summary or f"{method_upper} {path_str}"

                # 初始化将存入数据库的参数结构
                parameters_for_db = {
                    "openapi_parameters": {}, 
                    "requestBody": None, # 稍后填充
                    "metadata": {"path_params": []} 
                }
                
                path_params_list_for_metadata = []

                # 1. 处理路径、查询、头部等参数 (OpenAPI 'parameters' 数组)
                if "parameters" in operation_obj and isinstance(operation_obj["parameters"], list):
                    for param_info_ref in operation_obj["parameters"]:
                        param_info = param_info_ref 
                        if isinstance(param_info_ref, dict) and "$ref" in param_info_ref:
                            # 解析参数引用
                            resolved_param_info = _resolve_openapi_ref(param_info_ref["$ref"], openapi_components, openapi_schema)
                            if resolved_param_info:
                                param_info = resolved_param_info
                            else:
                                logger.warning(f"无法解析参数 $ref: {param_info_ref['$ref']} for {method_upper} {path_str}")
                                continue # 跳过无法解析的参数

                        if not isinstance(param_info, dict) or "name" not in param_info or "in" not in param_info:
                            logger.warning(f"在 {method_upper} {path_str} 中发现无效的参数定义: {param_info}，已跳过。")
                            continue
                        
                        param_name = param_info["name"]
                        param_in = param_info["in"]
                        param_schema = param_info.get("schema", {}) # schema 通常在参数对象内部
                        
                        # OpenAPI 参数对象本身就是其 schema，但有时 'schema' 键下有更详细的类型信息
                        # 我们需要的是包含 type, description, format 等的完整 schema
                        # param_info 通常包含 name, in, description, required, schema
                        
                        # 构建存储到 openapi_parameters 的结构
                        # 我们直接存储 param_info，因为它包含了所有元数据如 'in', 'required'
                        # 但对于 $ref 引用的 schema，确保它已被解析
                        if isinstance(param_schema, dict) and "$ref" in param_schema:
                            resolved_schema_for_param = _resolve_openapi_ref(param_schema["$ref"], openapi_components, openapi_schema)
                            if resolved_schema_for_param:
                                # 创建一个新字典，因为 param_info 可能来自原始的 openapi_schema，不应直接修改
                                param_info_for_db = param_info.copy()
                                param_info_for_db["schema"] = resolved_schema_for_param
                                parameters_for_db["openapi_parameters"][param_name] = param_info_for_db
                            else:
                                logger.warning(f"无法解析参数内schema $ref: {param_schema['$ref']} for param {param_name} in {method_upper} {path_str}")
                                parameters_for_db["openapi_parameters"][param_name] = param_info # 存原始的，包含$ref
                        else:
                            parameters_for_db["openapi_parameters"][param_name] = param_info # 存储整个参数对象

                        if param_in == "path":
                            if param_name not in path_params_list_for_metadata:
                                path_params_list_for_metadata.append(param_name)

                # 设置路径参数到metadata中
                parameters_for_db["metadata"]["path_params"] = path_params_list_for_metadata

                # 2. 处理请求体 (requestBody)
                if "requestBody" in operation_obj and isinstance(operation_obj["requestBody"], dict):
                    # 深拷贝以避免修改原始 openapi_schema
                    original_request_body = copy.deepcopy(operation_obj["requestBody"])
                    
                    # 解析 requestBody 自身的 $ref (如果存在)
                    if "$ref" in original_request_body:
                        resolved_rb_component = _resolve_openapi_ref(original_request_body["$ref"], openapi_components, openapi_schema)
                        if resolved_rb_component:
                            original_request_body = resolved_rb_component # 用解析后的内容替换整个 requestBody 定义
                        else:
                            logger.warning(f"无法解析 requestBody $ref: {original_request_body['$ref']} for {method_upper} {path_str}. 将使用原始引用。")
                            # 如果无法解析顶层 requestBody $ref，我们仍将其存入，让后续步骤处理（或失败）
                    
                    # 现在处理 requestBody.content.<media-type>.schema 中的 $ref
                    if isinstance(original_request_body, dict) and "content" in original_request_body:
                        for media_type, media_type_obj in original_request_body.get("content", {}).items():
                            if isinstance(media_type_obj, dict) and "schema" in media_type_obj:
                                schema_val = media_type_obj["schema"]
                                if isinstance(schema_val, dict) and "$ref" in schema_val:
                                    resolved_schema_for_content = _resolve_openapi_ref(schema_val["$ref"], openapi_components, openapi_schema)
                                    if resolved_schema_for_content:
                                        media_type_obj["schema"] = resolved_schema_for_content
                                    else:
                                        logger.warning(f"无法解析 requestBody content schema $ref: {schema_val['$ref']} for {method_upper} {path_str} under media type {media_type}. 将使用原始引用。")
                    
                    parameters_for_db["requestBody"] = original_request_body

                # 检查端点是否已存在
                existing_endpoint = existing_endpoints_map.get((path_str, method_upper))

                if existing_endpoint:
                    # 更新现有端点
                    update_fields = {
                        "name": name,
                        "description": endpoint_description,
                        "parameters": parameters_for_db, # 最重要的部分
                        "updated_at": datetime.now(),
                        "config_id": sapi_config_id # 确保关联到正确的SAPI配置
                        # enabled, requires_auth, allowed_roles 等可以根据需要决定是否也从OpenAPI同步
                    }
                    # 比较是否有实际变化
                    changed = False
                    for field, value in update_fields.items():
                        if getattr(existing_endpoint, field) != value:
                            setattr(existing_endpoint, field, value)
                            changed = True
                    
                    if changed:
                        try:
                            await db.merge(existing_endpoint) # 使用 merge 进行更新
                            endpoints_updated_count += 1
                            logger.info(f"已更新端点: {name} (ID: {existing_endpoint.id})")
                        except Exception as e_update:
                            logger.error(f"更新端点 {name} (ID: {existing_endpoint.id}) 失败: {e_update}")
                            endpoints_failed_count +=1
                    else:
                        logger.debug(f"端点 {name} (ID: {existing_endpoint.id}) 无需更新。")
                        endpoints_skipped_count +=1
                    
                    synced_endpoint_ids.add(existing_endpoint.id)

                else:
                    # 创建新端点
                    new_endpoint_id = uuid.uuid4()
                    new_sapi_endpoint = SAPIEndpoint(
                        id=new_endpoint_id,
                        name=name,
                        description=endpoint_description,
                        endpoint_path=path_str,
                        method=method_upper,
                        parameters=parameters_for_db,
                        enabled=True, # 默认为启用
                        requires_auth=True, # 默认需要认证 (可以后续调整或从OpenAPI扩展字段读取)
                        config_id=sapi_config_id,
                        created_at=datetime.now(),
                        updated_at=datetime.now(),
                        # created_by_id = current_user.id # 考虑是否需要设置
                    )
                    db.add(new_sapi_endpoint)
                    endpoints_created_count += 1
                    synced_endpoint_ids.add(new_endpoint_id)
                    logger.info(f"准备创建新端点: {name} (Path: {path_str}, Method: {method_upper})")

        # 删除在 OpenAPI schema 中不再存在的旧端点 (软删除或硬删除)
        # 注意：这里只处理与当前 default_sapi_config 关联的端点
        endpoints_deleted_count = 0
        if sapi_config_id: # 仅当 sapi_config_id 有效时执行删除
            for existing_path_method, existing_ep in existing_endpoints_map.items():
                # 只考虑属于当前处理的SAPIConfig的端点，避免误删其他配置的端点
                if existing_ep.config_id == sapi_config_id and existing_ep.id not in synced_endpoint_ids:
                    # 检查该端点是否被任何 APIMCPToolMapping 使用
                    mapping_check_result = await db.execute(
                        select(func.count(APIMCPToolMapping.id)).where(APIMCPToolMapping.endpoint_id == existing_ep.id)
                    )
                    if mapping_check_result.scalar_one() > 0:
                        logger.warning(f"端点 {existing_ep.name} (ID: {existing_ep.id}) 正在被映射使用，将禁用而不是删除。")
                        existing_ep.enabled = False
                        existing_ep.updated_at = datetime.now()
                        await db.merge(existing_ep)
                        endpoints_skipped_count +=1 # 算作跳过删除
                    else:
                        logger.info(f"正在删除不再存在的端点: {existing_ep.name} (ID: {existing_ep.id})")
                        await db.delete(existing_ep)
                        endpoints_deleted_count += 1
        
        # 提交所有更改
        await db.commit()
        logger.info(f"API端点同步完成。创建: {endpoints_created_count}, 更新: {endpoints_updated_count}, 删除: {endpoints_deleted_count}, 跳过/无变化: {endpoints_skipped_count}, 失败: {endpoints_failed_count}")

        return {
            "success": True,
            "message": "API端点同步成功",
            "data": {
                "created": endpoints_created_count,
                "updated": endpoints_updated_count,
                "deleted": endpoints_deleted_count,
                "skipped_or_unchanged": endpoints_skipped_count,
                "failed": endpoints_failed_count,
            }
        }
    except HTTPException: # 重新抛出已知的HTTP异常
        raise
    except Exception as e:
        await db.rollback() # 发生意外错误时回滚
        logger.error(f"API端点同步过程中发生错误: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"API端点同步失败: {str(e)}")
