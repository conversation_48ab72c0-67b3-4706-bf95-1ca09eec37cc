from typing import List, Dict, Any, Optional, Union
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import and_, select, delete
from uuid import UUID

from models.role import Role, Permission, RolePermission
from schemas.role import RoleCreate, RoleUpdate, PermissionCreate, PermissionUpdate, RolePermissionCreate

class RoleService:
    @staticmethod
    async def get_role_by_id(db: AsyncSession, role_id: Union[UUID, str]) -> Optional[Role]:
        """根据ID获取角色"""
        result = await db.execute(select(Role).where(Role.id == role_id))
        return result.scalars().first()

    @staticmethod
    async def get_role_by_code(db: AsyncSession, project_id: Union[UUID, str], code: str) -> Optional[Role]:
        """根据编码获取角色"""
        result = await db.execute(select(Role).where(and_(Role.project_id == project_id, Role.code == code)))
        return result.scalars().first()

    @staticmethod
    async def get_roles(
        db: AsyncSession, 
        project_id: Union[UUID, str], 
        skip: int = 0, 
        limit: int = 100,
        is_system_role: Optional[bool] = None
    ) -> List[Role]:
        """获取角色列表"""
        query = select(Role).where(Role.project_id == project_id)
        if is_system_role is not None:
            query = query.where(Role.is_system_role == is_system_role)
        query = query.offset(skip).limit(limit)
        result = await db.execute(query)
        return result.scalars().all()

    @staticmethod
    async def count_roles(
        db: AsyncSession, 
        project_id: Union[UUID, str],
        is_system_role: Optional[bool] = None
    ) -> int:
        """统计角色数量"""
        query = select(Role).where(Role.project_id == project_id)
        if is_system_role is not None:
            query = query.where(Role.is_system_role == is_system_role)
        result = await db.execute(query)
        return len(result.scalars().all())

    @staticmethod
    async def create_role(db: AsyncSession, role: RoleCreate) -> Role:
        """创建角色"""
        db_role = Role(
            project_id=role.project_id,
            name=role.name,
            code=role.code,
            description=role.description,
            permissions=role.permissions,
            status=role.status,
            is_system_role=role.is_system_role
        )
        db.add(db_role)
        await db.commit()
        await db.refresh(db_role)
        return db_role

    @staticmethod
    async def update_role(db: AsyncSession, role_id: Union[UUID, str], role_update: RoleUpdate) -> Optional[Role]:
        """更新角色"""
        db_role = await RoleService.get_role_by_id(db, role_id)
        if db_role:
            # 使用model_dump替代dict（Pydantic v2兼容）
            try:
                update_data = role_update.model_dump(exclude_unset=True)
            except AttributeError:
                # 兼容Pydantic v1
                update_data = role_update.dict(exclude_unset=True)
            
            # 过滤掉None值和空值，避免不必要的比较
            filtered_data = {k: v for k, v in update_data.items() if v is not None and v != ""}
            
            for key, value in filtered_data.items():
                if hasattr(db_role, key):
                    setattr(db_role, key, value)
            
            await db.commit()
            await db.refresh(db_role)
        return db_role

    @staticmethod
    async def delete_role(db: AsyncSession, role_id: Union[UUID, str]) -> bool:
        """删除角色"""
        db_role = await RoleService.get_role_by_id(db, role_id)
        if db_role:
            from models.project_user import ProjectUser
            result = await db.execute(select(ProjectUser).where(ProjectUser.role_id == role_id))
            user_count = len(result.scalars().all())
            if user_count > 0:
                return False
            await db.execute(delete(RolePermission).where(RolePermission.role_id == role_id))
            await db.delete(db_role)
            await db.commit()
            return True
        return False


class PermissionService:
    @staticmethod
    async def get_permission_by_id(db: AsyncSession, permission_id: Union[UUID, str]) -> Optional[Permission]:
        """根据ID获取权限"""
        result = await db.execute(select(Permission).where(Permission.id == permission_id))
        return result.scalars().first()

    @staticmethod
    async def get_permission_by_code(db: AsyncSession, code: str) -> Optional[Permission]:
        """根据编码获取权限"""
        result = await db.execute(select(Permission).where(Permission.code == code))
        return result.scalars().first()

    @staticmethod
    async def get_permissions(
        db: AsyncSession, 
        skip: int = 0, 
        limit: int = 100,
        module: Optional[str] = None,
        is_system_permission: Optional[bool] = None
    ) -> List[Permission]:
        """获取权限列表"""
        query = select(Permission)
        if module:
            query = query.where(Permission.module == module)
        if is_system_permission is not None:
            query = query.where(Permission.is_system_permission == is_system_permission)
        query = query.offset(skip).limit(limit)
        result = await db.execute(query)
        return result.scalars().all()

    @staticmethod
    async def count_permissions(
        db: AsyncSession,
        module: Optional[str] = None,
        is_system_permission: Optional[bool] = None
    ) -> int:
        """统计权限数量"""
        query = select(Permission)
        if module:
            query = query.where(Permission.module == module)
        if is_system_permission is not None:
            query = query.where(Permission.is_system_permission == is_system_permission)
        result = await db.execute(query)
        return len(result.scalars().all())

    @staticmethod
    async def create_permission(db: AsyncSession, permission: PermissionCreate) -> Permission:
        """创建权限"""
        db_permission = Permission(
            name=permission.name,
            code=permission.code,
            description=permission.description,
            module=permission.module,
            action=permission.action,
            resource=permission.resource,
            is_system_permission=permission.is_system_permission
        )
        db.add(db_permission)
        await db.commit()
        await db.refresh(db_permission)
        return db_permission

    @staticmethod
    async def update_permission(db: AsyncSession, permission_id: Union[UUID, str], permission_update: PermissionUpdate) -> Optional[Permission]:
        """更新权限"""
        db_permission = await PermissionService.get_permission_by_id(db, permission_id)
        if db_permission:
            update_data = permission_update.dict(exclude_unset=True)
            for key, value in update_data.items():
                setattr(db_permission, key, value)
            await db.commit()
            await db.refresh(db_permission)
        return db_permission

    @staticmethod
    async def delete_permission(db: AsyncSession, permission_id: Union[UUID, str]) -> bool:
        """删除权限"""
        db_permission = await PermissionService.get_permission_by_id(db, permission_id)
        if db_permission:
            result = await db.execute(select(RolePermission).where(RolePermission.permission_id == permission_id))
            role_count = len(result.scalars().all())
            if role_count > 0:
                return False
            await db.delete(db_permission)
            await db.commit()
            return True
        return False


class RolePermissionService:
    @staticmethod
    async def get_role_permissions(db: AsyncSession, role_id: Union[UUID, str]) -> List[RolePermission]:
        """获取角色的权限关联"""
        result = await db.execute(select(RolePermission).where(RolePermission.role_id == role_id))
        return result.scalars().all()

    @staticmethod
    async def get_role_permission(db: AsyncSession, role_id: Union[UUID, str], permission_id: Union[UUID, str]) -> Optional[RolePermission]:
        """获取角色的特定权限关联"""
        result = await db.execute(select(RolePermission).where(and_(RolePermission.role_id == role_id, RolePermission.permission_id == permission_id)))
        return result.scalars().first()

    @staticmethod
    async def create_role_permission(db: AsyncSession, role_permission: RolePermissionCreate) -> RolePermission:
        """创建角色权限关联"""
        db_role_permission = RolePermission(
            role_id=role_permission.role_id,
            permission_id=role_permission.permission_id
        )
        db.add(db_role_permission)
        await db.commit()
        await db.refresh(db_role_permission)
        return db_role_permission

    @staticmethod
    async def delete_role_permission(db: AsyncSession, role_id: Union[UUID, str], permission_id: Union[UUID, str]) -> bool:
        """删除角色权限关联"""
        db_role_permission = await RolePermissionService.get_role_permission(db, role_id, permission_id)
        if db_role_permission:
            await db.delete(db_role_permission)
            await db.commit()
            return True
        return False

    @staticmethod
    async def bulk_update_role_permissions(db: AsyncSession, role_id: Union[UUID, str], permission_ids: List[Union[UUID, str]]) -> List[RolePermission]:
        """批量更新角色权限"""
        await db.execute(delete(RolePermission).where(RolePermission.role_id == role_id))
        await db.commit()
        result = []
        for permission_id in permission_ids:
            role_permission = RolePermissionCreate(
                role_id=role_id,
                permission_id=permission_id
            )
            db_role_permission = await RolePermissionService.create_role_permission(db, role_permission)
            result.append(db_role_permission)
        return result
