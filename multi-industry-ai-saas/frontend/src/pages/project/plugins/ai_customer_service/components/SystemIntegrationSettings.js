import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Select,
  Switch,
  Button,
  Space,
  Typography,
  Alert,
  Divider,
  Row,
  Col,
  Statistic,
  Tag,
  List,
  message,
  Tooltip,
  InputNumber,
  Input,
  Spin
} from 'antd';
import {
  RobotOutlined,
  DatabaseOutlined,
  SettingOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  InfoCircleOutlined,
  EyeOutlined,
  AudioOutlined,
  MessageOutlined,
  SaveOutlined
} from '@ant-design/icons';

const { Option } = Select;
const { Title, Paragraph, Text } = Typography;

/**
 * 系统AI整合设置组件
 */
const SystemIntegrationSettings = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [aiConfigs, setAiConfigs] = useState([]);
  const [config, setConfig] = useState(null);
  const [mcpTools, setMcpTools] = useState([]);
  const [mcpServers, setMcpServers] = useState([]);
  const [knowledgeBases, setKnowledgeBases] = useState([]);

  // 获取项目ID
  const getProjectId = () => {
    const projectId = localStorage.getItem('project_id');
    if (projectId && projectId !== 'null' && projectId !== 'undefined') {
      return projectId;
    }
    
    const userData = JSON.parse(localStorage.getItem('user_data') || '{}');
    if (userData && userData.project_id) {
      localStorage.setItem('project_id', userData.project_id);
      return userData.project_id;
    }
    
    return null;
  };

  // 模型类型配置
  const modelTypes = [
    { key: 'chat', label: '聊天模型', icon: <MessageOutlined />, color: '#1890ff' },
    { key: 'vision', label: '视觉模型', icon: <EyeOutlined />, color: '#52c41a' },
    { key: 'audio', label: '语音模型', icon: <AudioOutlined />, color: '#fa8c16' },
    { key: 'embedding', label: '嵌入模型', icon: <RobotOutlined />, color: '#722ed1' }
  ];

  useEffect(() => {
    const projectId = getProjectId();
    if (projectId) {
      loadData();
      loadMcpData();
      loadKnowledgeBases();
    }
  }, []);

  const loadMcpData = async () => {
    try {
      console.log('开始加载MCP数据...');
      
      // 加载MCP工具 - 尝试多个API路径
      try {
        let toolsResponse;
      const projectId = getProjectId();

        // 从AI客服插件获取系统工具配置（注意：这些是从项目AI助理获取的）
        try {
          toolsResponse = await fetch(`/api/v1/project/${projectId}/plugin/ai-customer-service/integration/mcp/tools`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });

          if (toolsResponse.ok) {
            const data = await toolsResponse.json();
            toolsResponse = data;
            console.log('从AI客服插件获取MCP工具成功（来源：项目AI助理）');
          } else {
            throw new Error('AI客服插件MCP工具API不可用');
          }
        } catch (e) {
          console.log('AI客服插件MCP工具API不可用，使用示例数据');
          // 注意：这里不再尝试SAPI插件，因为AI客服应该从项目AI助理获取MCP工具
          throw e;
        }
        
        if (toolsResponse && toolsResponse.success !== false) {
          const toolsData = toolsResponse.data || toolsResponse || [];
                        console.log('系统工具数据:', toolsData);
          setMcpTools(Array.isArray(toolsData) ? toolsData : []);
        } else {
          // 使用示例数据（来自项目AI助理）
          console.log('使用系统工具示例数据（来源：项目AI助理）');
          setMcpTools([
            {
              id: 'tool_1',
              name: '销售数据分析',
              tool_type: 'business_analysis',
              description: '分析销售趋势和业绩数据',
              enabled: true,
              source: 'project_ai_assistant'
            },
            {
              id: 'tool_2', 
              name: '库存查询',
              tool_type: 'inventory_management',
              description: '查询商品库存信息',
              enabled: true,
              source: 'project_ai_assistant'
            },
            {
              id: 'tool_3',
              name: '客户信息查询',
              tool_type: 'customer_management', 
              description: '查询客户基本信息和历史记录',
              enabled: true,
              source: 'project_ai_assistant'
            },
            {
              id: 'tool_4',
              name: '数据报表生成',
              tool_type: 'reporting',
              description: '生成业务数据报表',
              enabled: true,
              source: 'project_ai_assistant'
            }
          ]);
        }
      } catch (toolError) {
        console.error('加载系统工具失败:', toolError);
        setMcpTools([]);
      }

      // 加载MCP服务器
      try {
        let serversResponse;
      const projectId = getProjectId();

        // 从AI客服插件获取MCP服务器配置（注意：这些是从项目AI助理获取的）
        try {
          serversResponse = await fetch(`/api/v1/project/${projectId}/plugin/ai-customer-service/integration/mcp/servers`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });

          if (serversResponse.ok) {
            const data = await serversResponse.json();
            serversResponse = data;
            console.log('从AI客服插件获取MCP服务器成功（来源：项目AI助理）');
          } else {
            throw new Error('AI客服插件MCP服务器API不可用');
          }
        } catch (e) {
          console.log('AI客服插件MCP服务器API不可用，使用示例数据');
          // 注意：这里应该从项目AI助理获取，不是其他系统
          throw e;
        }
        
        if (serversResponse && serversResponse.success !== false) {
          const serversData = serversResponse.data || serversResponse || [];
          console.log('MCP服务器数据:', serversData);
          setMcpServers(Array.isArray(serversData) ? serversData : []);
        } else {
          // 使用示例数据（来自项目AI助理）
          console.log('使用MCP服务器示例数据（来源：项目AI助理）');
          setMcpServers([
            {
              id: 'server_1',
              name: '业务工具服务器',
              status: 'active',
              description: '提供业务分析和管理工具',
              url: 'http://localhost:8001',
              enabled: true,
              source: 'project_ai_assistant'
            },
            {
              id: 'server_2',
              name: '数据分析服务器', 
              status: 'active',
              description: '提供数据分析和报表工具',
              url: 'http://localhost:8002',
              enabled: true,
              source: 'project_ai_assistant'
            }
          ]);
        }
      } catch (serverError) {
        console.error('加载MCP服务器失败:', serverError);
        setMcpServers([]);
      }
      
      console.log('MCP数据加载完成');
    } catch (error) {
      console.error('加载MCP数据失败:', error);
      setMcpTools([]);
      setMcpServers([]);
    }
  };

  const loadKnowledgeBases = async () => {
    try {
      const projectId = getProjectId();
      if (!projectId) return;

      console.log('开始加载知识库数据...');
      
      // 尝试加载AI知识库
      try {
        const aiKnowledgeResponse = await fetch(`/api/project/${projectId}/ai/knowledge/bases`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          }
        });

        if (aiKnowledgeResponse.ok) {
          const aiKnowledgeData = await aiKnowledgeResponse.json();
          const aiKnowledgeBases = aiKnowledgeData.data || [];
          console.log('AI知识库数据:', aiKnowledgeBases);
          
          if (aiKnowledgeBases.length > 0) {
            setKnowledgeBases(aiKnowledgeBases.map(kb => ({
              id: kb.id,
              name: kb.name || kb.title,
              type: 'ai_knowledge_base',
              description: kb.description,
              status: kb.status,
              source: 'ai_knowledge_system'
            })));
            console.log('使用AI知识库数据');
            return;
          }
        }
      } catch (error) {
        console.log('AI知识库API不可用，尝试传统知识库API');
      }

      // 如果AI知识库不可用，尝试传统知识库
      try {
        const knowledgeResponse = await fetch(`/api/v1/project/${projectId}/knowledge-base`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          }
        });

        if (knowledgeResponse.ok) {
          const knowledgeData = await knowledgeResponse.json();
          const traditionalKnowledgeBases = knowledgeData.items || knowledgeData.data || [];
          console.log('传统知识库数据:', traditionalKnowledgeBases);
          
          if (traditionalKnowledgeBases.length > 0) {
            setKnowledgeBases(traditionalKnowledgeBases.map(kb => ({
              id: kb.id,
              name: kb.title || kb.name,
              type: 'knowledge_document',
              description: kb.content ? kb.content.substring(0, 100) + '...' : '',
              status: 'active',
              source: 'traditional_knowledge_base'
            })));
            console.log('使用传统知识库数据');
            return;
          }
        }
      } catch (error) {
        console.log('传统知识库API不可用');
      }

      // 如果都不可用，提供空数组
      setKnowledgeBases([]);
      console.log('未找到可用的知识库数据');
      
    } catch (error) {
      console.error('加载知识库数据失败:', error);
      setKnowledgeBases([]);
    }
  };

  const loadData = async () => {
    setLoading(true);
    try {
      const projectId = getProjectId();
      if (!projectId) return;

      // 加载AI模型配置 - 使用项目AI助理的配置列表
      console.log('开始加载AI模型配置...');
      try {
        // 使用标准的API服务获取项目AI配置
        const availableData = await fetch(`/api/project/${projectId}/ai/configs`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          }
        });
        
        let availableModels = [];
        if (availableData.ok) {
          const response = await availableData.json();
          availableModels = response.data || response || [];
          console.log('项目可用AI模型配置:', availableModels.length, '个');
        } else {
          console.error('获取AI配置失败:', availableData.status, availableData.statusText);
          console.log('尝试从AI模型API获取...');
          // 如果configs不可用，尝试从models API获取
          const modelsResponse = await fetch(`/api/project/${projectId}/ai/models`, {
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json'
            }
          });
          
          if (modelsResponse.ok) {
            const modelsData = await modelsResponse.json();
            availableModels = (modelsData.data || modelsData || []).map(model => ({
              id: model.id,
              name: model.name || model.display_name,
              model_type: model.model_type || 'chat',
              provider: model.provider || 'unknown',
              model: model.model,
              enabled: model.enabled !== false,
              description: model.description,
              capabilities: model.capabilities
            }));
            console.log('从AI模型API获取到:', availableModels.length, '个模型');
          }
        }
        
        // 2. 再获取AI客服插件的配置（哪些模型被选中、如何配置）
        const customerServiceConfigResponse = await fetch(`/api/v1/project/${projectId}/plugin/ai-customer-service/integration/ai-models`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          }
        });
        
        let customerServiceConfigs = [];
        if (customerServiceConfigResponse.ok) {
          const configData = await customerServiceConfigResponse.json();
          customerServiceConfigs = configData.data || configData || [];
          console.log('AI客服插件配置:', customerServiceConfigs.length, '个');
        }
        
                 // 3. 合并数据：可用模型 + AI客服配置
         if (availableModels.length > 0) {
           // 将AI客服配置映射到可用模型上
           const enhancedConfigs = availableModels.map(model => {
             // 获取模型类型，优先使用model.model_type，然后尝试从model.model推断
             let modelType = 'chat'; // 默认类型
             
             // 首先检查是否有明确的model_type字段
             if (model.model_type && typeof model.model_type === 'string') {
               modelType = model.model_type.toLowerCase();
             } else if (model.model && typeof model.model === 'string') {
               // 如果没有model_type，尝试从model字段推断
               const modelName = model.model.toLowerCase();
               if (modelName.includes('vision') || modelName.includes('gpt-4v') || modelName.includes('claude-3') || modelName.includes('qwen-vl')) {
                 modelType = 'vision';
               } else if (modelName.includes('whisper') || modelName.includes('audio') || modelName.includes('speech')) {
                 modelType = 'audio';
               } else if (modelName.includes('embedding') || modelName.includes('ada') || modelName.includes('text-embedding')) {
                 modelType = 'embedding';
               } else if (modelName.includes('dall-e') || modelName.includes('midjourney') || modelName.includes('stable-diffusion')) {
                 modelType = 'image';
               }
             } else if (model.name && typeof model.name === 'string') {
               // 如果model.model不存在，尝试使用model.name推断类型
               const modelName = model.name.toLowerCase();
               if (modelName.includes('vision') || modelName.includes('gpt-4v') || modelName.includes('视觉') || modelName.includes('图像') || modelName.includes('ocr')) {
                 modelType = 'vision';
               } else if (modelName.includes('whisper') || modelName.includes('audio') || modelName.includes('语音') || modelName.includes('音频')) {
                 modelType = 'audio';
               } else if (modelName.includes('embedding') || modelName.includes('ada') || modelName.includes('嵌入') || modelName.includes('向量')) {
                 modelType = 'embedding';
               } else if (modelName.includes('dall-e') || modelName.includes('midjourney') || modelName.includes('图片生成')) {
                 modelType = 'image';
               }
             }

             // 检查capabilities来进一步确认模型类型
             const capabilities = model.capabilities || {};
             if (capabilities.supports_vision === true && modelType === 'chat') {
               modelType = 'vision';
             } else if ((capabilities.supports_audio_input === true || capabilities.supports_speech_to_text === true) && modelType === 'chat') {
               modelType = 'audio';
             }
             
             const customerServiceConfig = customerServiceConfigs.find(config => 
               config.source_model_id === model.id || config.model === model.model
             );
             
             return {
               id: model.id,
               name: model.name,
               model_type: modelType,
               provider: model.provider || 'unknown',
               model: model.model,
               enabled: model.enabled !== false, // 默认启用
               description: model.description || `${model.provider} ${modelType} 模型`,
               capabilities: model.capabilities || {}, // 确保capabilities被传递
               // AI客服插件特有配置
               customer_service_enabled: customerServiceConfig?.enabled || false,
               customer_service_temperature: customerServiceConfig?.temperature || 0.7,
               customer_service_max_tokens: customerServiceConfig?.max_tokens || 2000,
               customer_service_config_id: customerServiceConfig?.id || null,
               is_configured_for_customer_service: !!customerServiceConfig,
               // 标记来源和计费
               source: 'project_ai_system',
               billing_type: 'project_unified', // 统一计费到项目
               usage_context: 'ai_customer_service'
             };
           });
           
           setAiConfigs(enhancedConfigs);
           console.log('AI模型配置加载成功 - 来源：项目AI系统，计费：项目统一');
           console.log('加载的模型类型分布:', enhancedConfigs.reduce((acc, model) => {
             acc[model.model_type] = (acc[model.model_type] || 0) + 1;
             return acc;
           }, {}));
           
           // 调试：打印每个模型的详细信息
           enhancedConfigs.forEach(model => {
             console.log(`模型 ${model.name} (ID: ${model.id}):`, {
               model_type: model.model_type,
               capabilities: model.capabilities,
               provider: model.provider
             });
           });
         } else {
          // 使用示例配置，包含更多模型类型
          console.log('使用AI模型示例配置');
          setAiConfigs([
            { 
              id: 'ai_model_1', 
              name: 'GPT-4 Turbo', 
              model_type: 'chat', 
              provider: 'openai', 
              enabled: true,
              source: 'project_ai_system',
              customer_service_enabled: true,
              is_configured_for_customer_service: false,
              description: 'OpenAI GPT-4 Turbo 聊天模型',
              capabilities: { supports_chat: true }
            },
            { 
              id: 'ai_model_2', 
              name: 'GPT-4 Vision', 
              model_type: 'vision', 
              provider: 'openai', 
              enabled: true,
              source: 'project_ai_system',
              customer_service_enabled: true,
              is_configured_for_customer_service: false,
              description: 'OpenAI GPT-4 Vision 视觉模型',
              capabilities: { supports_vision: true, supports_chat: true }
            },
            { 
              id: 'ai_model_3', 
              name: 'Claude 3.5 Sonnet', 
              model_type: 'chat', 
              provider: 'anthropic', 
              enabled: true,
              source: 'project_ai_system',
              customer_service_enabled: true,
              is_configured_for_customer_service: false,
              description: 'Anthropic Claude 3.5 Sonnet 聊天模型',
              capabilities: { supports_chat: true }
            },
            { 
              id: 'ai_model_4', 
              name: 'Whisper Large', 
              model_type: 'audio', 
              provider: 'openai', 
              enabled: true,
              source: 'project_ai_system',
              customer_service_enabled: true,
              is_configured_for_customer_service: false,
              description: 'OpenAI Whisper 语音识别模型',
              capabilities: { supports_speech_to_text: true, supports_audio_input: true }
            },
            { 
              id: 'ai_model_5', 
              name: 'Text Embedding 3 Large', 
              model_type: 'embedding', 
              provider: 'openai', 
              enabled: true,
              source: 'project_ai_system',
              customer_service_enabled: true,
              is_configured_for_customer_service: false,
              description: 'OpenAI 文本嵌入模型',
              capabilities: { supports_embedding: true }
            }
          ]);
        }
      } catch (configError) {
        console.error('加载AI模型配置失败:', configError);
        // 提供默认配置
        setAiConfigs([
          { 
            id: 'default_1', 
            name: '默认AI模型', 
            model_type: 'chat', 
            provider: 'default', 
            enabled: true,
            source: 'project_ai_system',
            customer_service_enabled: true,
            is_configured_for_customer_service: false,
            capabilities: { supports_chat: true }
          }
        ]);
      }

      // 然后加载AI客服系统整合配置 - 使用正确的AI客服插件API
      try {
        const integrationResponse = await fetch(`/api/v1/project/${projectId}/plugin/ai-customer-service/integration/settings`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });

        if (integrationResponse.ok) {
          const integrationData = await integrationResponse.json();
          console.log('AI客服整合配置响应:', integrationData);
          
          const currentConfig = integrationData.data || integrationData;
          if (currentConfig && Object.keys(currentConfig).length > 0) {
            setConfig(currentConfig);
            
            // 设置表单字段值，包括映射AI模型配置
            const formData = {
              // AI模型配置映射
              ai_model_id: currentConfig.ai_model_id,
              default_chat_model_id: currentConfig.ai_model_id,
              default_vision_model_id: currentConfig.vision_model_id,
              default_audio_model_id: currentConfig.audio_model_id,
              default_embedding_model_id: currentConfig.embedding_model_id,
              
              // 知识库配置
              knowledge_base_ids: currentConfig.knowledge_base_ids || [],
              
              // 参数配置 - 修复浮点数精度问题
              temperature: parseFloat((currentConfig.temperature || 0.7).toFixed(1)),
              chat_temperature: parseFloat((currentConfig.temperature || 0.7).toFixed(1)),
              vision_temperature: parseFloat((currentConfig.vision_temperature || 0.7).toFixed(1)),
              max_tokens: currentConfig.max_tokens || 2000,
              chat_max_tokens: currentConfig.max_tokens || 2000,
              confidence_threshold: parseFloat((currentConfig.confidence_threshold || 0.8).toFixed(1)),
              
              // 功能开关
              enable_context_memory: currentConfig.enable_context_memory !== false,
              enable_knowledge_search: currentConfig.enable_knowledge_search !== false,
              fallback_to_human: currentConfig.fallback_to_human !== false,
              
              // 系统工具配置
              enable_mcp_tools: currentConfig.enable_mcp_tools !== false,
              mcp_server_ids: currentConfig.mcp_server_ids || [],
              mcp_tool_ids: currentConfig.mcp_tool_ids || []
            };
            
            form.setFieldsValue(formData);
          }
        } else {
          console.log('AI客服整合配置API不可用，使用默认配置');
          // 设置默认配置
          const defaultConfig = {
            temperature: 0.7,
            chat_temperature: 0.7,
            vision_temperature: 0.7,
            max_tokens: 2000,
            chat_max_tokens: 2000,
            confidence_threshold: 0.8,
            enable_context_memory: true,
            enable_knowledge_search: true,
            fallback_to_human: true,
            enable_mcp_tools: true
          };
          form.setFieldsValue(defaultConfig);
        }
      } catch (integrationError) {
        console.error('加载AI客服整合配置失败:', integrationError);
        // 设置默认配置
        const defaultConfig = {
          temperature: 0.7,
          chat_temperature: 0.7,
          vision_temperature: 0.7,
          max_tokens: 2000,
          chat_max_tokens: 2000,
          confidence_threshold: 0.8,
          enable_context_memory: true,
          enable_knowledge_search: true,
          fallback_to_human: true,
          enable_mcp_tools: true
        };
        form.setFieldsValue(defaultConfig);
      }
      
    } catch (error) {
      console.error('加载数据失败:', error);
      message.error('加载配置失败');
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async (values) => {
    try {
      setSaving(true);
      const projectId = getProjectId();
      if (!projectId) {
        message.error('无法获取项目ID');
        return;
      }

      console.log('保存AI客服整合配置:', values);

      // 构建AI客服整合配置数据结构
      const integrationData = {
        // 主要AI模型配置
        ai_model_id: values.default_chat_model_id || values.ai_model_id,
        vision_model_id: values.default_vision_model_id,
        audio_model_id: values.default_audio_model_id,
        embedding_model_id: values.default_embedding_model_id,
        
        // 知识库配置
        knowledge_base_ids: values.knowledge_base_ids || [],
        
        // 参数配置 - 修复浮点数精度问题
        temperature: parseFloat((values.chat_temperature || values.temperature || 0.7).toFixed(1)),
        vision_temperature: parseFloat((values.vision_temperature || 0.7).toFixed(1)),
        max_tokens: values.chat_max_tokens || values.max_tokens || 2000,
        confidence_threshold: parseFloat((values.confidence_threshold || 0.8).toFixed(1)),
        
        // 功能开关
        enable_context_memory: values.enable_context_memory !== false,
        enable_knowledge_search: values.enable_knowledge_search !== false,
        fallback_to_human: values.fallback_to_human !== false,
        
        // 系统工具配置
        enable_mcp_tools: values.enable_mcp_tools !== false,
        mcp_server_ids: values.mcp_server_ids || [],
        mcp_tool_ids: values.mcp_tool_ids || []
      };

      // 使用AI客服插件的整合设置API - 修复URL构建逻辑
      // 后端会根据项目ID查找配置，如果存在则更新，不存在则创建
      const url = `/api/v1/project/${projectId}/plugin/ai-customer-service/integration/settings`;
      
      // 总是使用PUT方法，因为后端的update_integration_settings会处理创建和更新逻辑
      const method = 'PUT';

      const response = await fetch(url, {
        method,
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(integrationData)
      });

      if (response.ok) {
        const savedConfig = await response.json();
        console.log('AI客服整合配置保存成功:', savedConfig);
        
        setConfig(savedConfig.data || savedConfig);
        message.success('AI客服整合配置保存成功');
        
        // 重新加载数据以确保显示最新配置
        await loadData();
      } else {
        const errorData = await response.text();
        console.error('保存AI客服整合配置失败:', errorData);
        message.error('保存AI客服整合配置失败');
      }
    } catch (error) {
      console.error('保存AI客服整合配置失败:', error);
      message.error('保存AI客服整合配置失败');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>
          <Text>加载AI客服系统配置中...</Text>
        </div>
      </div>
    );
  }

  return (
    <div style={{ padding: '0' }}>
      <Alert
        message="AI客服系统整合设置"
        description={
          <div>
            <p>• <strong>AI模型配置</strong>：使用项目AI模型池，所有AI调用统一计费到项目（显示在AI助手中）</p>
            <p>• <strong>系统工具集成</strong>：接入项目AI助理的系统工具（面向内部用户）</p>
            <p>• <strong>系统整合</strong>：配置回退机制、超时时间、重试次数</p>
            <p>• <strong>角色定位</strong>：AI助理（内部用户）vs AI智能体客服（消费者）</p>
          </div>
        }
        type="info"
        showIcon
        style={{ marginBottom: 24 }}
      />

            <Form
              form={form}
              layout="vertical"
        onFinish={handleSave}
      >
        {/* AI模型配置 */}
        <Card 
          title={
            <Space>
              <span>AI模型配置</span>
              <Tag color="blue">项目AI模型池</Tag>
              <Tag color="green">统一计费</Tag>
            </Space>
          } 
          style={{ marginBottom: 24 }}
          extra={
            <Text type="secondary" style={{ fontSize: '12px' }}>
              来源：项目AI系统 | 计费：统一到项目 | 显示：AI助手
            </Text>
          }
            >
          <Row gutter={24}>
            {modelTypes.map(modelType => (
              <Col span={6} key={modelType.key}>
              <Form.Item
                  name={`default_${modelType.key}_model_id`}
                label={
                  <Space>
                      <span style={{ color: modelType.color }}>{modelType.icon}</span>
                      <Text>{modelType.label}</Text>
                  </Space>
                }
                >
                  <Select 
                    placeholder={`选择${modelType.label}`}
                    allowClear
                    showSearch
                    optionFilterProp="children"
                  >
                    {aiConfigs
                      .filter(config => {
                        if (!config || !config.model_type) {
                          console.log('跳过配置（缺少model_type）:', config);
                          return false;
                        }
                        
                        // 使用与AI助理完全相同的筛选逻辑
                        const modelConfigType = config.model_type;
                        const baseCaps = config.capabilities || {};
                        
                        // 获取能力覆盖（如果有的话）
                        const capabilitiesOverride = config.config?.capabilities_override || {};
                        
                        // 应用能力覆盖，构建有效能力
                        const effectiveCaps = {
                          ...baseCaps,
                          ...capabilitiesOverride
                        };
                        
                        let typeMatch = false;
                        
                        switch(modelType.key) {
                          case 'chat':
                            typeMatch = modelConfigType === 'chat' || 
                                   modelConfigType === 'completion' || 
                                   modelConfigType === 'reasoning' ||
                                   effectiveCaps.supports_chat === true;
                            break;
                          case 'vision':
                            typeMatch = modelConfigType === 'vision' || 
                                   effectiveCaps.supports_vision === true;
                            break;
                          case 'audio':
                            typeMatch = modelConfigType === 'audio' || 
                                   effectiveCaps.supports_audio_input === true || 
                                   effectiveCaps.supports_audio_output === true ||
                                   effectiveCaps.supports_speech_to_text === true ||
                                   effectiveCaps.supports_text_to_speech === true;
                            break;
                          case 'embedding':
                            typeMatch = modelConfigType === 'embedding';
                            break;
                          default:
                            typeMatch = false;
                        }
                        
                        console.log(`模型 ${config.name} (${modelConfigType}) 匹配类型 ${modelType.key}: ${typeMatch}`, {
                          model_type: modelConfigType,
                          base_capabilities: baseCaps,
                          capabilities_override: capabilitiesOverride,
                          effective_capabilities: effectiveCaps,
                          target_type: modelType.key
                        });
                        
                        return typeMatch;
                      })
                      .map(config => (
                        <Option key={config.id} value={config.id}>
                          <Space direction="vertical" size="small" style={{ width: '100%' }}>
                            <Space>
                              <Text strong>{config.name || 'Unknown Model'}</Text>
                              {config.is_configured_for_customer_service && (
                                <Tag size="small" color="green">已配置</Tag>
                              )}
                              {config.customer_service_enabled && (
                                <Tag size="small" color="blue">启用</Tag>
                              )}
                            </Space>
                            <Text type="secondary" style={{ fontSize: '12px' }}>
                              {(typeof config.provider === 'string' ? config.provider : config.provider?.name || config.provider?.display_name || '未知提供商')} | 来源: {(typeof config.source === 'string' ? config.source : '项目AI系统')}
                            </Text>
                          </Space>
                        </Option>
                      ))
                    }
                  </Select>
                </Form.Item>
              </Col>
            ))}
          </Row>

          <Row gutter={24}>
            <Col span={8}>
              <Form.Item
                name="chat_temperature"
                label="聊天模型温度"
              >
                <Select defaultValue={0.7}>
                  <Option value={0.1}>0.1 (精确)</Option>
                  <Option value={0.3}>0.3 (保守)</Option>
                  <Option value={0.5}>0.5 (平衡)</Option>
                  <Option value={0.7}>0.7 (创造)</Option>
                  <Option value={0.9}>0.9 (灵活)</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="chat_max_tokens"
                label="最大输出Token"
              >
                <Select defaultValue={2048}>
                  <Option value={512}>512</Option>
                  <Option value={1024}>1024</Option>
                  <Option value={2048}>2048</Option>
                  <Option value={4096}>4096</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="vision_temperature"
                label="视觉模型温度"
              >
                <Select defaultValue={0.7}>
                  <Option value={0.1}>0.1 (精确)</Option>
                  <Option value={0.3}>0.3 (保守)</Option>
                  <Option value={0.5}>0.5 (平衡)</Option>
                  <Option value={0.7}>0.7 (创造)</Option>
                  <Option value={0.9}>0.9 (灵活)</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
        </Card>

        {/* 功能开关 */}
        <Card title="功能开关" style={{ marginBottom: 24 }}>
          <Row gutter={24}>
            <Col span={6}>
              <Form.Item
                name="enable_context_memory"
                label="上下文记忆"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item
                name="enable_knowledge_search"
                label="知识库搜索"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item
                name="fallback_to_human"
                label="人工兜底"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item
                name="enable_mcp_tools"
                label="系统工具集成"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
          </Row>
        </Card>

        {/* MCP工具配置 */}
        <Card title="MCP工具配置" style={{ marginBottom: 24 }}>
          <Row gutter={24}>
            <Col span={12}>
              <Form.Item
                name="mcp_server_ids"
                label="MCP服务器"
              >
                <Select
                  mode="multiple"
                  placeholder="选择MCP服务器"
                  allowClear
                >
                  {mcpServers.map(server => (
                    <Option key={server.id} value={server.id}>
                      <Space>
                        <Tag color={server.status === 'active' ? 'green' : 'red'}>
                          {server.status || 'unknown'}
                        </Tag>
                        {server.name || 'Unknown Server'}
                      </Space>
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="mcp_tool_ids"
                label="系统工具"
              >
                <Select 
                  mode="multiple" 
                  placeholder="选择系统工具"
                  allowClear
                >
                  {mcpTools.map(tool => (
                    <Option key={tool.id} value={tool.id}>
                      <Space>
                        <Tag color="blue">{tool.tool_type || 'unknown'}</Tag>
                        <Text strong>{tool.name || 'Unknown Tool'}</Text>
                        <Text type="secondary">{tool.description || 'No description'}</Text>
                        <Tag color={tool.enabled ? 'green' : 'red'}>
                          {tool.enabled ? '已启用' : '未启用'}
                        </Tag>
                      </Space>
                    </Option>
                  ))}
                </Select>
                  </Form.Item>
                </Col>
              </Row>

          {mcpTools.length > 0 && (
            <div style={{ marginTop: 16 }}>
              <Text strong>可用系统工具:</Text>
              <List
                size="small"
                dataSource={mcpTools}
                renderItem={tool => (
                  <List.Item>
                    <Space>
                      <Tag color="blue">{tool.tool_type || 'unknown'}</Tag>
                      <Text strong>{tool.name || 'Unknown Tool'}</Text>
                      <Text type="secondary">{tool.description || 'No description'}</Text>
                      <Tag color={tool.enabled ? 'green' : 'red'}>
                        {tool.enabled ? '已启用' : '未启用'}
                      </Tag>
                    </Space>
                  </List.Item>
                )}
              />
            </div>
          )}
        </Card>

        {/* 高级配置 */}
        <Card title="高级配置" style={{ marginBottom: 24 }}>
          <Row gutter={24}>
            <Col span={8}>
              <Form.Item
                name="confidence_threshold"
                label="置信度阈值"
              >
                <InputNumber
                  min={0.1}
                  max={1.0}
                  step={0.1}
                  precision={1}
                  placeholder="0.8"
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="knowledge_base_ids"
                label="知识库"
              >
                <Select
                  mode="multiple"
                  placeholder="选择知识库"
                  allowClear
                  showSearch
                  optionFilterProp="children"
                  notFoundContent={knowledgeBases.length === 0 ? "暂无可用知识库" : "无匹配项"}
                >
                  {knowledgeBases.map(kb => (
                    <Option key={kb.id} value={kb.id}>
                      <Space direction="vertical" size="small" style={{ width: '100%' }}>
                        <Space>
                          <Text strong>{kb.name}</Text>
                          <Tag size="small" color={kb.status === 'active' ? 'green' : 'default'}>
                            {kb.status || '未知'}
                          </Tag>
                          <Tag size="small" color={kb.type === 'ai_knowledge_base' ? 'blue' : 'orange'}>
                            {kb.type === 'ai_knowledge_base' ? 'AI知识库' : '传统知识库'}
                          </Tag>
                        </Space>
                        {kb.description && (
                          <Text type="secondary" style={{ fontSize: '12px' }}>
                            {kb.description}
                          </Text>
                        )}
                      </Space>
                    </Option>
                  ))}
                  {knowledgeBases.length === 0 && (
                    <Option disabled value="">
                      <Text type="secondary">
                        暂无可用知识库，请先在AI知识库或项目知识库页面创建知识库
                      </Text>
                    </Option>
                  )}
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="max_context_length"
                label="最大上下文长度"
              >
                <InputNumber
                  min={1000}
                  max={32000}
                  step={1000}
                  placeholder="8000"
                  style={{ width: '100%' }}
                />
              </Form.Item>
        </Col>
          </Row>
          </Card>

        {/* 保存按钮 */}
        <Card>
          <div style={{ textAlign: 'center' }}>
            <Button
              type="primary"
              size="large"
              icon={<SaveOutlined />}
              htmlType="submit"
              loading={saving}
              style={{ minWidth: 120 }}
            >
              {saving ? '保存中...' : '保存配置'}
            </Button>
          </div>
          </Card>
      </Form>
    </div>
  );
};

export default SystemIntegrationSettings;
