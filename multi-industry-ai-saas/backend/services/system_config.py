from typing import List, Dict, Any, Optional, Union
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import and_
from sqlalchemy.future import select
from uuid import UUID

from models.system_config import SystemConfig
from schemas.system_config import SystemConfigCreate, SystemConfigUpdate

class SystemConfigService:
    @staticmethod
    async def get_config_by_type_and_key(
        db: AsyncSession, project_id: Union[UUID, str], config_type: str, config_key: str
    ) -> Optional[SystemConfig]:
        """根据类型和键获取配置"""
        result = await db.execute(select(SystemConfig).where(
            and_(
                SystemConfig.project_id == project_id,
                SystemConfig.config_type == config_type,
                SystemConfig.config_key == config_key
            )
        ))
        return result.scalars().first()

    @staticmethod
    async def get_configs_by_type(
        db: AsyncSession, project_id: Union[UUID, str], config_type: str
    ) -> List[SystemConfig]:
        """根据类型获取所有配置"""
        result = await db.execute(select(SystemConfig).where(
            and_(
                SystemConfig.project_id == project_id,
                SystemConfig.config_type == config_type
            )
        ))
        return result.scalars().all()

    @staticmethod
    async def get_all_configs(db: AsyncSession, project_id: Union[UUID, str]) -> List[SystemConfig]:
        """获取项目的所有配置"""
        result = await db.execute(select(SystemConfig).where(SystemConfig.project_id == project_id))
        return result.scalars().all()

    @staticmethod
    async def create_config(db: AsyncSession, config: SystemConfigCreate) -> SystemConfig:
        """创建配置"""
        db_config = SystemConfig(
            project_id=config.project_id,
            config_type=config.config_type,
            config_key=config.config_key,
            config_value=config.config_value
        )
        db.add(db_config)
        await db.commit()
        await db.refresh(db_config)
        return db_config

    @staticmethod
    async def update_config(
        db: AsyncSession, project_id: Union[UUID, str], config_type: str, config_key: str, config_update: SystemConfigUpdate
    ) -> Optional[SystemConfig]:
        """更新配置"""
        db_config = await SystemConfigService.get_config_by_type_and_key(
            db, project_id, config_type, config_key
        )
        if db_config:
            db_config.config_value = config_update.config_value
            await db.commit()
            await db.refresh(db_config)
        return db_config

    @staticmethod
    async def delete_config(
        db: AsyncSession, project_id: Union[UUID, str], config_type: str, config_key: str
    ) -> bool:
        """删除配置"""
        db_config = await SystemConfigService.get_config_by_type_and_key(
            db, project_id, config_type, config_key
        )
        if db_config:
            await db.delete(db_config)
            await db.commit()
            return True
        return False

    @staticmethod
    async def upsert_config(
        db: AsyncSession, project_id: Union[UUID, str], config_type: str, config_key: str, config_value: Dict[str, Any]
    ) -> SystemConfig:
        """更新或创建配置"""
        db_config = await SystemConfigService.get_config_by_type_and_key(
            db, project_id, config_type, config_key
        )
        if db_config:
            db_config.config_value = config_value
            await db.commit()
            await db.refresh(db_config)
            return db_config
        else:
            config_create = SystemConfigCreate(
                project_id=project_id,
                config_type=config_type,
                config_key=config_key,
                config_value=config_value
            )
            return await SystemConfigService.create_config(db, config_create)

    @staticmethod
    async def bulk_upsert_configs(
        db: AsyncSession, project_id: Union[UUID, str], config_type: str, configs: Dict[str, Any]
    ) -> List[SystemConfig]:
        """批量更新或创建配置"""
        result = []
        for key, value in configs.items():
            config = await SystemConfigService.upsert_config(
                db, project_id, config_type, key, value
            )
            result.append(config)
        return result
