#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
异步任务模型
"""

import uuid
from datetime import datetime, timezone
from sqlalchemy import Column, String, Text, Boolean, ForeignKey, DateTime, func, Integer
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship

from db.database import Base

class AsyncTask(Base):
    """异步任务模型"""
    __tablename__ = "async_tasks"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4, comment="任务ID")
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id", ondelete="CASCADE"), nullable=True, comment="租户ID")
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id", ondelete="CASCADE"), nullable=True, comment="项目ID")
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id", ondelete="CASCADE"), nullable=False, comment="用户ID")
    
    # 任务基本信息
    task_type = Column(String(50), nullable=False, comment="任务类型")
    task_name = Column(String(200), nullable=False, comment="任务名称")
    description = Column(Text, nullable=True, comment="任务描述")
    
    # 任务状态
    status = Column(String(20), nullable=False, default="pending", comment="任务状态")
    progress = Column(Integer, nullable=False, default=0, comment="任务进度 (0-100)")
    
    # 任务数据
    input_data = Column(JSONB, nullable=True, comment="输入数据")
    result_data = Column(JSONB, nullable=True, comment="结果数据")
    error_message = Column(Text, nullable=True, comment="错误消息")
    
    # 时间信息
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    started_at = Column(DateTime(timezone=True), nullable=True, comment="开始时间")
    completed_at = Column(DateTime(timezone=True), nullable=True, comment="完成时间")
    updated_at = Column(DateTime(timezone=True), nullable=True, comment="更新时间")
    
    # 任务控制
    priority = Column(Integer, nullable=False, default=0, comment="优先级")
    timeout_seconds = Column(Integer, nullable=True, comment="超时时间(秒)")
    retry_count = Column(Integer, nullable=False, default=0, comment="重试次数")
    max_retries = Column(Integer, nullable=False, default=3, comment="最大重试次数")

    # 关系
    tenant = relationship("Tenant", back_populates="async_tasks")
    project = relationship("Project", back_populates="async_tasks")
    user = relationship("User", back_populates="async_tasks")

    def __repr__(self):
        return f"<AsyncTask(id={self.id}, type='{self.task_type}', status='{self.status}')>"

    @property
    def is_completed(self):
        """任务是否已完成"""
        return self.status in ['success', 'failed', 'cancelled']

    @property
    def is_running(self):
        """任务是否正在运行"""
        return self.status in ['pending', 'running']

    @property
    def duration(self):
        """任务持续时间（秒）"""
        if not self.started_at:
            return None
        end_time = self.completed_at or datetime.now(timezone.utc)
        return (end_time - self.started_at).total_seconds()

    def to_dict(self):
        """转换为字典"""
        return {
            "id": str(self.id),
            "tenant_id": str(self.tenant_id) if self.tenant_id else None,
            "project_id": str(self.project_id) if self.project_id else None,
            "user_id": str(self.user_id),
            "task_type": self.task_type,
            "task_name": self.task_name,
            "description": self.description,
            "status": self.status,
            "progress": self.progress,
            "input_data": self.input_data,
            "result_data": self.result_data,
            "error_message": self.error_message,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "started_at": self.started_at.isoformat() if self.started_at else None,
            "completed_at": self.completed_at.isoformat() if self.completed_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "priority": self.priority,
            "timeout_seconds": self.timeout_seconds,
            "retry_count": self.retry_count,
            "max_retries": self.max_retries,
            "is_completed": self.is_completed,
            "is_running": self.is_running,
            "duration": self.duration,
            # 为了前端兼容性，同时提供result和result_data字段
            "result": self.result_data,
        } 