#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
AI 聊天 Schema
"""

from typing import Optional, List, Dict, Any, Union
from datetime import datetime
import uuid
from pydantic import BaseModel, Field, validator

class AIChatMessage(BaseModel):
    """AI 聊天消息 Schema"""
    role: str = Field(..., description="消息角色：system, user, assistant, function")
    content: Optional[str] = Field(None, description="消息内容")
    name: Optional[str] = Field(None, description="消息名称，用于 function 角色")
    function_call: Optional[Dict[str, Any]] = Field(None, description="函数调用信息")

class AIFunction(BaseModel):
    """AI 函数 Schema"""
    name: str = Field(..., description="函数名称")
    description: Optional[str] = Field(None, description="函数描述")
    parameters: Dict[str, Any] = Field(..., description="函数参数")

class AIChatRequest(BaseModel):
    """AI 聊天请求 Schema"""
    messages: List[AIChatMessage] = Field(..., description="消息列表")
    model_id: Optional[uuid.UUID] = Field(None, description="模型 ID，如果不指定则使用默认模型")
    config_id: Optional[uuid.UUID] = Field(None, description="配置 ID，如果不指定则使用默认配置")
    temperature: Optional[float] = Field(None, ge=0, le=2, description="温度参数，控制随机性")
    max_tokens: Optional[int] = Field(None, gt=0, description="最大 token 数")
    functions: Optional[List[AIFunction]] = Field(None, description="可用函数列表")
    function_call: Optional[Union[str, Dict[str, Any]]] = Field(None, description="函数调用设置")
    stream: bool = Field(False, description="是否流式响应")

class AIChatResponseChoice(BaseModel):
    """AI 聊天响应选项 Schema"""
    index: int = Field(..., description="选项索引")
    message: AIChatMessage = Field(..., description="消息内容")
    finish_reason: Optional[str] = Field(None, description="结束原因")

class AIChatResponseUsage(BaseModel):
    """AI 聊天响应使用情况 Schema"""
    prompt_tokens: int = Field(..., description="提示 token 数")
    completion_tokens: int = Field(..., description="完成 token 数")
    total_tokens: int = Field(..., description="总 token 数")

class AIChatResponse(BaseModel):
    """AI 聊天响应 Schema"""
    id: str = Field(..., description="响应 ID")
    object: str = Field(..., description="对象类型")
    created: int = Field(..., description="创建时间戳")
    model: str = Field(..., description="模型名称")
    choices: List[AIChatResponseChoice] = Field(..., description="响应选项列表")
    usage: Optional[AIChatResponseUsage] = Field(None, description="使用情况")

class AIChatStreamResponse(BaseModel):
    """AI 聊天流式响应 Schema"""
    id: str = Field(..., description="响应 ID")
    object: str = Field(..., description="对象类型")
    created: int = Field(..., description="创建时间戳")
    model: str = Field(..., description="模型名称")
    choices: List[Dict[str, Any]] = Field(..., description="响应选项列表")
    
class AIChatErrorResponse(BaseModel):
    """AI 聊天错误响应 Schema"""
    success: bool = Field(False, description="是否成功")
    message: str = Field(..., description="错误信息")
    error: Optional[Dict[str, Any]] = Field(None, description="错误详情")
