import React, { useState, useEffect } from 'react';
import {
  Card,
  Typography,
  Button,
  Upload,
  Table,
  Space,
  Tag,
  Tooltip,
  Popconfirm,
  message,
  Row,
  Col,
  Statistic,
  Progress,
  Tabs,
  Input,
  Select,
  List,
  Modal,
  Form,
  Spin,
  Breadcrumb,
  Menu,
  Dropdown,
  Image,
  Divider
} from 'antd';
import {
  UploadOutlined,
  DownloadOutlined,
  DeleteOutlined,
  EyeOutlined,
  ShareAltOutlined,
  FolderOutlined,
  FileOutlined,
  FileImageOutlined,
  FileWordOutlined,
  FileExcelOutlined,
  FilePdfOutlined,
  FileTextOutlined,
  FileZipOutlined,
  FileUnknownOutlined,
  PlusOutlined,
  SearchOutlined,
  ReloadOutlined,
  FolderAddOutlined,
  ArrowLeftOutlined,
  LinkOutlined,
  CopyOutlined,
  LockOutlined,
  UnlockOutlined,
  InboxOutlined
} from '@ant-design/icons';
import apiService from '../../../services/api';
import { formatBytes, getFileTypeIcon, getFileExtension } from '../../../utils/fileUtils';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;
const { Dragger } = Upload;
const { Option } = Select;

/**
 * 项目空间组件
 * 用于管理项目文件、文档和资源
 */
const ProjectSpace = () => {
  // 状态管理
  const [loading, setLoading] = useState(false);
  const [files, setFiles] = useState([]);
  const [folders, setFolders] = useState([]);
  const [currentFolder, setCurrentFolder] = useState(null);
  const [folderPath, setFolderPath] = useState([{ id: 'root', name: '根目录' }]);
  const [activeTab, setActiveTab] = useState('all');
  const [searchText, setSearchText] = useState('');
  const [storageUsage, setStorageUsage] = useState({
    used: 0,
    total: 0,
    percentage: 0
  });
  const [fileTypeStats, setFileTypeStats] = useState([]);
  const [createFolderVisible, setCreateFolderVisible] = useState(false);
  const [newFolderName, setNewFolderName] = useState('');
  const [shareModalVisible, setShareModalVisible] = useState(false);
  const [currentFile, setCurrentFile] = useState(null);
  const [shareLink, setShareLink] = useState('');
  const [sharePassword, setSharePassword] = useState('');
  const [requirePassword, setRequirePassword] = useState(false);
  const [uploadModalVisible, setUploadModalVisible] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploading, setUploading] = useState(false);
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewImage, setPreviewImage] = useState('');
  const [previewTitle, setPreviewTitle] = useState('');
  const [fileList, setFileList] = useState([]);
  const [form] = Form.useForm();

  // 获取存储使用情况
  const fetchStorageUsage = async () => {
    try {
      const response = await apiService.project.storage.getUsage();
      if (response && response.success) {
        const { used, total, percentage } = response.data;
        setStorageUsage({
          used,
          total,
          percentage
        });
      }
    } catch (error) {
      console.error('获取存储使用情况失败:', error);
      message.error('获取存储使用情况失败');
    }
  };

  // 获取文件类型统计
  const fetchFileTypeStats = async () => {
    try {
      const response = await apiService.project.storage.getFileTypeStats();
      if (response && response.success) {
        setFileTypeStats(response.data);
      }
    } catch (error) {
      console.error('获取文件类型统计失败:', error);
      message.error('获取文件类型统计失败');
    }
  };

  // 获取文件列表
  const fetchFiles = async (folderId = null) => {
    setLoading(true);
    try {
      const response = await apiService.project.storage.getFiles({
        folder_id: folderId,
        search: searchText
      });

      if (response && response.success) {
        setFiles(response.data.files || []);
        setFolders(response.data.folders || []);
      }
    } catch (error) {
      console.error('获取文件列表失败:', error);
      message.error('获取文件列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 初始化
  useEffect(() => {
    fetchStorageUsage();
    fetchFileTypeStats();
    fetchFiles();
  }, []);

  // 搜索变化时重新获取文件
  useEffect(() => {
    fetchFiles(currentFolder?.id);
  }, [searchText]);

  // 处理文件上传
  const handleUpload = async (file) => {
    // 检查文件大小
    const maxSize = 100 * 1024 * 1024; // 100MB
    if (file.size > maxSize) {
      message.error('文件大小不能超过100MB');
      return Upload.LIST_IGNORE;
    }

    setUploading(true);
    setUploadProgress(0);

    try {
      const formData = new FormData();
      formData.append('file', file);

      if (currentFolder) {
        formData.append('folder_id', currentFolder.id);
      }

      const response = await apiService.project.storage.uploadFile(formData, {
        onUploadProgress: (progressEvent) => {
          const percentage = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          setUploadProgress(percentage);
        }
      });

      if (response && response.success) {
        message.success(`${file.name} 上传成功`);
        fetchFiles(currentFolder?.id);
        fetchStorageUsage();
        fetchFileTypeStats();
      } else {
        message.error(`${file.name} 上传失败: ${response.message}`);
      }
    } catch (error) {
      console.error('文件上传失败:', error);
      message.error(`${file.name} 上传失败: ${error.message}`);
    } finally {
      setUploading(false);
      setUploadProgress(0);
    }

    return false;
  };

  // 处理文件删除
  const handleDeleteFile = async (fileId) => {
    try {
      const response = await apiService.project.storage.deleteFile(fileId);
      if (response && response.success) {
        message.success('文件删除成功');
        fetchFiles(currentFolder?.id);
        fetchStorageUsage();
        fetchFileTypeStats();
      } else {
        message.error(`文件删除失败: ${response.message}`);
      }
    } catch (error) {
      console.error('文件删除失败:', error);
      message.error(`文件删除失败: ${error.message}`);
    }
  };

  // 处理文件夹删除
  const handleDeleteFolder = async (folderId) => {
    try {
      const response = await apiService.project.storage.deleteFolder(folderId);
      if (response && response.success) {
        message.success('文件夹删除成功');
        fetchFiles(currentFolder?.id);
      } else {
        message.error(`文件夹删除失败: ${response.message}`);
      }
    } catch (error) {
      console.error('文件夹删除失败:', error);
      message.error(`文件夹删除失败: ${error.message}`);
    }
  };

  // 处理文件下载
  const handleDownloadFile = async (fileId, fileName) => {
    try {
      const response = await apiService.project.storage.downloadFile(fileId);
      if (response) {
        // 创建下载链接
        const url = window.URL.createObjectURL(new Blob([response]));
        const link = document.createElement('a');
        link.href = url;
        link.setAttribute('download', fileName);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }
    } catch (error) {
      console.error('文件下载失败:', error);
      message.error('文件下载失败');
    }
  };

  // 处理文件预览
  const handlePreviewFile = (file) => {
    // 检查文件类型是否可预览
    const fileExt = getFileExtension(file.name).toLowerCase();
    const previewableTypes = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'pdf'];

    if (previewableTypes.includes(fileExt)) {
      if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(fileExt)) {
        // 图片预览
        setPreviewImage(apiService.project.storage.getFilePreviewUrl(file.id));
        setPreviewTitle(file.name);
        setPreviewVisible(true);
      } else {
        // PDF等文件预览，在新窗口打开
        window.open(apiService.project.storage.getFilePreviewUrl(file.id), '_blank');
      }
    } else {
      message.info('此文件类型不支持预览');
    }
  };

  // 处理文件分享
  const handleShareFile = (file) => {
    setCurrentFile(file);
    setShareLink('');
    setSharePassword('');
    setRequirePassword(false);
    setShareModalVisible(true);
  };

  // 生成分享链接
  const generateShareLink = async () => {
    if (!currentFile) return;

    try {
      const response = await apiService.project.storage.createFileShare({
        file_id: currentFile.id,
        password: requirePassword ? sharePassword : null,
        expires_in: 7 * 24 * 60 * 60 // 7天
      });

      if (response && response.success) {
        setShareLink(response.data.share_link);
        message.success('分享链接生成成功');
      } else {
        message.error(`生成分享链接失败: ${response.message}`);
      }
    } catch (error) {
      console.error('生成分享链接失败:', error);
      message.error('生成分享链接失败');
    }
  };

  // 复制分享链接
  const copyShareLink = () => {
    if (shareLink) {
      navigator.clipboard.writeText(shareLink)
        .then(() => message.success('链接已复制到剪贴板'))
        .catch(() => message.error('复制失败，请手动复制'));
    }
  };

  // 处理创建文件夹
  const handleCreateFolder = async () => {
    if (!newFolderName.trim()) {
      message.error('请输入文件夹名称');
      return;
    }

    try {
      const response = await apiService.project.storage.createFolder({
        name: newFolderName,
        parent_id: currentFolder?.id || null
      });

      if (response && response.success) {
        message.success('文件夹创建成功');
        setNewFolderName('');
        setCreateFolderVisible(false);
        fetchFiles(currentFolder?.id);
      } else {
        message.error(`文件夹创建失败: ${response.message}`);
      }
    } catch (error) {
      console.error('文件夹创建失败:', error);
      message.error('文件夹创建失败');
    }
  };

  // 处理进入文件夹
  const handleEnterFolder = (folder) => {
    setCurrentFolder(folder);
    setFolderPath([...folderPath, { id: folder.id, name: folder.name }]);
    fetchFiles(folder.id);
  };

  // 处理返回上级文件夹
  const handleGoBack = () => {
    if (folderPath.length <= 1) {
      // 已经在根目录
      return;
    }

    const newPath = [...folderPath];
    newPath.pop();
    setFolderPath(newPath);

    const parentFolder = newPath.length > 1 ? { id: newPath[newPath.length - 1].id } : null;
    setCurrentFolder(parentFolder);
    fetchFiles(parentFolder?.id);
  };

  // 处理导航到指定文件夹
  const handleNavigateToFolder = (index) => {
    if (index >= folderPath.length - 1) {
      // 已经在当前文件夹
      return;
    }

    const newPath = folderPath.slice(0, index + 1);
    setFolderPath(newPath);

    const targetFolder = index > 0 ? { id: newPath[index].id } : null;
    setCurrentFolder(targetFolder);
    fetchFiles(targetFolder?.id);
  };

  // 处理标签页切换
  const handleTabChange = (key) => {
    setActiveTab(key);
  };

  // 处理搜索
  const handleSearch = (value) => {
    setSearchText(value);
  };

  // 获取过滤后的文件列表
  const getFilteredFiles = () => {
    if (activeTab === 'all') {
      return files;
    }

    return files.filter(file => {
      const fileExt = getFileExtension(file.name).toLowerCase();

      if (activeTab === 'images') {
        return ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(fileExt);
      }

      if (activeTab === 'documents') {
        return ['doc', 'docx', 'pdf', 'txt', 'rtf', 'odt', 'xls', 'xlsx', 'ppt', 'pptx'].includes(fileExt);
      }

      if (activeTab === 'others') {
        const imageExts = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
        const docExts = ['doc', 'docx', 'pdf', 'txt', 'rtf', 'odt', 'xls', 'xlsx', 'ppt', 'pptx'];
        return !imageExts.includes(fileExt) && !docExts.includes(fileExt);
      }

      return true;
    });
  };

  // 渲染文件列表
  const renderFileList = () => {
    const columns = [
      {
        title: '文件名',
        dataIndex: 'name',
        key: 'name',
        render: (text, record) => (
          <Space>
            {getFileTypeIcon(record.name)}
            <span>{text}</span>
          </Space>
        )
      },
      {
        title: '大小',
        dataIndex: 'size',
        key: 'size',
        width: 120,
        render: (size) => formatBytes(size)
      },
      {
        title: '上传时间',
        dataIndex: 'created_at',
        key: 'created_at',
        width: 180
      },
      {
        title: '上传者',
        dataIndex: 'creator_name',
        key: 'creator_name',
        width: 120
      },
      {
        title: '操作',
        key: 'action',
        width: 180,
        render: (_, record) => {
          // 判断文件类型是否可预览
          const fileExt = getFileExtension(record.name).toLowerCase();
          const canPreview = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'pdf'].includes(fileExt);

          return (
            <Space size="small">
              {canPreview && (
                <Button
                  type="text"
                  icon={<EyeOutlined />}
                  onClick={() => handlePreviewFile(record)}
                  title="预览"
                />
              )}
              <Button
                type="text"
                icon={<DownloadOutlined />}
                onClick={() => handleDownloadFile(record.id, record.name)}
                title="下载"
              />
              <Button
                type="text"
                icon={<ShareAltOutlined />}
                onClick={() => handleShareFile(record)}
                title="分享"
              />
              <Popconfirm
                title="确定要删除此文件吗?"
                onConfirm={() => handleDeleteFile(record.id)}
                okText="确定"
                cancelText="取消"
              >
                <Button
                  type="text"
                  danger
                  icon={<DeleteOutlined />}
                  title="删除"
                />
              </Popconfirm>
            </Space>
          );
        }
      }
    ];

    return (
      <Table
        columns={columns}
        dataSource={getFilteredFiles()}
        rowKey="id"
        loading={loading}
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
          showTotal: (total) => `共 ${total} 个文件`
        }}
      />
    );
  };

  // 渲染文件夹列表
  const renderFolderList = () => {
    const columns = [
      {
        title: '文件夹名',
        dataIndex: 'name',
        key: 'name',
        render: (text, record) => (
          <Space>
            <FolderOutlined style={{ color: '#faad14' }} />
            <a onClick={() => handleEnterFolder(record)}>{text}</a>
          </Space>
        )
      },
      {
        title: '创建时间',
        dataIndex: 'created_at',
        key: 'created_at',
        width: 180
      },
      {
        title: '创建者',
        dataIndex: 'creator_name',
        key: 'creator_name',
        width: 120
      },
      {
        title: '操作',
        key: 'action',
        width: 120,
        render: (_, record) => (
          <Space size="small">
            <Button
              type="text"
              icon={<FolderOutlined />}
              onClick={() => handleEnterFolder(record)}
              title="打开"
            />
            <Popconfirm
              title="确定要删除此文件夹吗? 文件夹内的所有文件将被删除。"
              onConfirm={() => handleDeleteFolder(record.id)}
              okText="确定"
              cancelText="取消"
            >
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
                title="删除"
              />
            </Popconfirm>
          </Space>
        )
      }
    ];

    return (
      <Table
        columns={columns}
        dataSource={folders}
        rowKey="id"
        loading={loading}
        pagination={false}
      />
    );
  };

  // 渲染面包屑导航
  const renderBreadcrumb = () => {
    return (
      <Breadcrumb style={{ marginBottom: 16 }}>
        {folderPath.map((folder, index) => (
          <Breadcrumb.Item key={folder.id}>
            {index === folderPath.length - 1 ? (
              folder.name
            ) : (
              <a onClick={() => handleNavigateToFolder(index)}>{folder.name}</a>
            )}
          </Breadcrumb.Item>
        ))}
      </Breadcrumb>
    );
  };

  // 渲染存储使用情况
  const renderStorageUsage = () => {
    return (
      <Card style={{ marginBottom: 16 }}>
        <Row gutter={16}>
          <Col span={12}>
            <Statistic
              title="存储空间使用情况"
              value={`${formatBytes(storageUsage.used)} / ${formatBytes(storageUsage.total)}`}
            />
            <Progress
              percent={typeof storageUsage.percentage !== 'undefined' ? storageUsage.percentage : 0}
              status={typeof storageUsage.percentage !== 'undefined' && storageUsage.percentage > 90 ? 'exception' : 'normal'}
              style={{ marginTop: 8 }}
            />
          </Col>
          <Col span={12}>
            <Row gutter={[16, 16]}>
              {fileTypeStats.map(stat => (
                <Col span={8} key={stat.type}>
                  <Statistic
                    title={stat.type === 'image' ? '图片' : (stat.type === 'document' ? '文档' : '其他')}
                    value={stat.count}
                    suffix="个"
                  />
                  <div>{formatBytes(stat.size)}</div>
                </Col>
              ))}
            </Row>
          </Col>
        </Row>
      </Card>
    );
  };

  return (
    <div className="project-space">
      <Card>
        <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 16 }}>
          <Title level={4}>项目空间</Title>
          <Space>
            <Input.Search
              placeholder="搜索文件"
              onSearch={handleSearch}
              style={{ width: 250 }}
            />
            <Button
              type="primary"
              icon={<UploadOutlined />}
              onClick={() => setUploadModalVisible(true)}
            >
              上传文件
            </Button>
            <Button
              icon={<FolderAddOutlined />}
              onClick={() => setCreateFolderVisible(true)}
            >
              新建文件夹
            </Button>
          </Space>
        </div>

        {renderStorageUsage()}

        <div style={{ marginBottom: 16 }}>
          {currentFolder && (
            <Button
              icon={<ArrowLeftOutlined />}
              onClick={handleGoBack}
              style={{ marginRight: 8 }}
            >
              返回上级
            </Button>
          )}
          {renderBreadcrumb()}
        </div>

        <Tabs activeKey={activeTab} onChange={handleTabChange}>
          <TabPane tab="全部" key="all">
            {folders.length > 0 && (
              <>
                <Title level={5}>文件夹</Title>
                {renderFolderList()}
                <Divider />
              </>
            )}
            <Title level={5}>文件</Title>
            {renderFileList()}
          </TabPane>
          <TabPane tab="图片" key="images">
            {renderFileList()}
          </TabPane>
          <TabPane tab="文档" key="documents">
            {renderFileList()}
          </TabPane>
          <TabPane tab="其他" key="others">
            {renderFileList()}
          </TabPane>
        </Tabs>
      </Card>

      {/* 文件上传模态框 */}
      <Modal
        title="上传文件"
        open={uploadModalVisible}
        onCancel={() => setUploadModalVisible(false)}
        footer={null}
      >
        <Dragger
          name="file"
          multiple={false}
          showUploadList={true}
          beforeUpload={handleUpload}
          fileList={fileList}
        >
          <p className="ant-upload-drag-icon">
            <InboxOutlined />
          </p>
          <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
          <p className="ant-upload-hint">
            支持单个或批量上传，单个文件大小不超过100MB
          </p>
        </Dragger>
        {uploading && (
          <div style={{ marginTop: 16 }}>
            <Progress percent={uploadProgress} status="active" />
          </div>
        )}
      </Modal>

      {/* 创建文件夹模态框 */}
      <Modal
        title="新建文件夹"
        open={createFolderVisible}
        onOk={handleCreateFolder}
        onCancel={() => setCreateFolderVisible(false)}
      >
        <Input
          placeholder="请输入文件夹名称"
          value={newFolderName}
          onChange={(e) => setNewFolderName(e.target.value)}
          autoFocus
        />
      </Modal>

      {/* 文件分享模态框 */}
      <Modal
        title="分享文件"
        open={shareModalVisible}
        onCancel={() => setShareModalVisible(false)}
        footer={[
          <Button key="cancel" onClick={() => setShareModalVisible(false)}>
            取消
          </Button>,
          <Button
            key="copy"
            type="primary"
            disabled={!shareLink}
            onClick={copyShareLink}
          >
            复制链接
          </Button>,
          <Button
            key="generate"
            type="primary"
            onClick={generateShareLink}
            disabled={requirePassword && !sharePassword}
          >
            生成链接
          </Button>
        ]}
      >
        <div style={{ marginBottom: 16 }}>
          <Form layout="vertical">
            <Form.Item
              label="是否需要密码"
            >
              <Select
                value={requirePassword}
                onChange={(value) => setRequirePassword(value)}
              >
                <Option value={false}>无密码</Option>
                <Option value={true}>需要密码</Option>
              </Select>
            </Form.Item>

            {requirePassword && (
              <Form.Item
                label="访问密码"
              >
                <Input.Password
                  value={sharePassword}
                  onChange={(e) => setSharePassword(e.target.value)}
                  placeholder="请输入访问密码"
                />
              </Form.Item>
            )}

            {shareLink && (
              <Form.Item
                label="分享链接"
              >
                <Input
                  value={shareLink}
                  readOnly
                  addonAfter={
                    <CopyOutlined
                      onClick={copyShareLink}
                      style={{ cursor: 'pointer' }}
                    />
                  }
                />
              </Form.Item>
            )}
          </Form>
        </div>
      </Modal>

      {/* 图片预览 */}
      <Modal
        visible={previewVisible}
        title={previewTitle}
        footer={null}
        onCancel={() => setPreviewVisible(false)}
      >
        <img alt="预览图片" style={{ width: '100%' }} src={previewImage} />
      </Modal>
    </div>
  );
};

export default ProjectSpace;