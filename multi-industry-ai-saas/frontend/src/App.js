import React, { useEffect, lazy, Suspense } from 'react';
import { Routes, Route, Navigate, useParams } from 'react-router-dom';
import { ConfigProvider } from 'antd';
import zhCN from 'antd/lib/locale/zh_CN';
import { initTheme } from './utils/theme';
import { initDateTimeUtils } from './utils/dateTimeUtils';
import { setupGlobalWebSocketErrorHandler } from './utils/websocketErrorHandler';
import './App.css';
import './styles/dashboard.css';

import Login from './pages/Login';
import Register from './pages/Register';
import NotFound from './pages/NotFound';

// 注意：插件页面已移至懒加载导入

// 超级管理员页面
import AdminLogin from './pages/admin/Login';
import AdminDashboard from './pages/admin/Dashboard';
import AdminSystemSettings from './pages/admin/SystemSettings';

// 服务商页面
import ServiceProviderLogin from './pages/service-provider/Login';
import ServiceProviderDashboard from './pages/service-provider/Dashboard';
import TenantManagement from './pages/service-provider/TenantManagement';
import TenantDetail from './pages/service-provider/TenantDetail';
import PluginManagement from './pages/service-provider/PluginManagement';

// 租户页面
import ProjectSelection from './pages/tenant/ProjectSelection';
import CreateProject from './pages/tenant/CreateProject';
import CreateProjectSteps from './pages/tenant/CreateProjectSteps';

// 项目页面
import ProjectDashboard from './pages/project/Dashboard';
import Dashboard from './pages/project/dashboard';
import ProjectDashboardPage from './pages/project/dashboard/ProjectDashboardPage';
import OperationsDashboardPage from './pages/project/dashboard/OperationsDashboardPage';
import FinanceDashboardPage from './pages/project/dashboard/FinanceDashboardPage';
import WarehouseDashboardPage from './pages/project/dashboard/WarehouseDashboardPage';
import PurchaseDashboardPage from './pages/project/dashboard/PurchaseDashboardPage';
import StoreDashboardPage from './pages/project/dashboard/StoreDashboardPage';
import UserManagement from './pages/project/settings/UserManagement';
import RoleManagement from './pages/project/settings/RoleManagement';
import SystemSettings from './pages/project/settings/SystemSettings';
import BasicSettings from './pages/project/settings/components/BasicSettings';
import BusinessSettings from './pages/project/settings/BusinessSettings';
import OperationLogs from './pages/project/settings/OperationLogs';
import NotificationSettings from './pages/project/settings/NotificationSettings';
import CreateWorkflow from './pages/project/settings/workflow/CreateWorkflow';
import WorkflowDetail from './pages/project/settings/workflow/WorkflowDetail';
import EditWorkflow from './pages/project/settings/workflow/EditWorkflow';
import UserProfile from './pages/project/user/Profile';
import NotificationCenter from './pages/project/notifications/NotificationCenter';
import NotificationCreate from './pages/project/notifications/NotificationCreate';
import ProductManagement from './pages/project/product/ProductManagement';
import ProductDetail from './pages/project/product/ProductDetail';
import SupplierManagement from './pages/project/supplier/SupplierManagement';
import SupplierDetail from './pages/project/supplier/SupplierDetail';
import StoreLoss from './pages/project/store/Loss';
import SalesReport from './pages/project/store/SalesReport';
import SalesReportForm from './pages/project/store/SalesReportForm';
import SalesReportDetail from './pages/project/store/SalesReportDetail';
import SalesMonthlyReport from './pages/project/store/SalesMonthlyReport';
import Reconciliation from './pages/project/finance/Reconciliation';
import DataAnalysis from './pages/project/finance/data-analysis';
import BusinessCenter from './pages/project/finance/business-center';
import RoutineTasks from './pages/project/store/RoutineTasks';
import RoutineTaskDetail from './pages/project/store/RoutineTaskDetail';
import DailyOperations from './pages/project/store/DailyOperations';
import StoreOperation from './pages/project/operation/StoreOperation';
import ChannelManagement from './pages/project/operation/ChannelManagement';
import KnowledgeDetail from './pages/project/operation/KnowledgeDetail';
import ProjectSpace from './pages/project/space/ProjectSpace';
import SharedFile from './pages/project/space/SharedFile';
import LossApproval from './pages/project/purchase/LossApproval';
import Management from './pages/project/inventory/Management';
import InventoryAlert from './pages/project/inventory/InventoryAlert';
import InventoryTransfer from './pages/project/inventory/InventoryTransfer';
import InventoryCheck from './pages/project/inventory/InventoryCheck';
import PurchaseOrderList from './pages/project/purchase/PurchaseOrderList';
import PurchaseOrderDetail from './pages/project/purchase/PurchaseOrderDetail';
import PurchaseOrderForm from './pages/project/purchase/PurchaseOrderForm';
import OperationSuperDashboard from './pages/project/operation/OperationSuperDashboard';
import StoreSuperDashboard from './pages/project/store/StoreSuperDashboard';

// 布局组件
import AppLayout from './components/layout/AppLayout';
import ProjectLayout from './components/layout/ProjectLayout';

// 通用组件
import PlaceholderPage from './components/common/PlaceholderPage';
import ProtectedRoute from './components/common/ProtectedRoute';
import RootRedirect from './components/common/RootRedirect';

// 上下文提供者
import { ProjectProvider } from './contexts/ProjectContext';

// 懒加载插件页面
const PluginMarket = lazy(() => import('./pages/project/plugins/plugin-market'));
const PluginDetail = lazy(() => import('./pages/project/plugins/plugin-market/PluginDetail'));
const PluginOrders = lazy(() => import('./pages/project/plugins/PluginOrders'));
const DingTalkRobot = lazy(() => import('./pages/project/plugins/dingtalk/DingTalkRobot'));
const GamePage = lazy(() => import('./pages/project/plugins/marketing_game/components/GamePage'));
const SAPIPlugin = lazy(() => import('./pages/project/plugins/sapi'));
const AICustomerServicePlugin = lazy(() => import('./pages/project/plugins/ai_customer_service'));
const MultiChannelOperations = lazy(() => import('./pages/project/plugins/multi_channel_operations'));

// 经营分析页面
const BusinessAnalysis = lazy(() => import('./pages/project/business-analysis/BusinessAnalysis'));

// 价格监控详情页面
const PriceMonitoringDetail = lazy(() => import('./pages/project/operation/PriceMonitoringDetail'));

// 临时占位组件
const PlaceholderComponent = ({ title }) => (
  <div style={{ padding: '50px', textAlign: 'center' }}>
    <h2>{title}</h2>
    <p>此页面正在开发中...</p>
  </div>
);

// 路由重定向组件
const RedirectWithParams = ({ to }) => {
  const { gameId } = useParams();
  const redirectPath = to.replace(':gameId', gameId);
  return <Navigate to={redirectPath} replace />;
};

// 主页
const Home = () => (
  <div style={{ padding: '50px', textAlign: 'center' }}>
    <h1>Retail AI SaaS - 智能零售管理系统</h1>
    <p>欢迎使用Retail AI SaaS系统</p>
    <p>这是一个多租户、多行业的SaaS平台</p>
  </div>
);

const MarketingGamePluginDetail = lazy(() => import('./pages/project/plugins/marketing_game/detail'));
const MarketingGamePluginParticipants = lazy(() => import('./pages/project/plugins/marketing_game/participants'));
const MarketingGamePluginRecords = lazy(() => import('./pages/project/plugins/marketing_game/records'));
const MarketingGamePluginPrizes = lazy(() => import('./pages/project/plugins/marketing_game/prizes'));
const MarketingGamePluginList = lazy(() => import('./pages/project/plugins/marketing_game/index'));
const TaskManagement = lazy(() => import('./pages/project/task/TaskManagement'));
const TaskDetail = lazy(() => import('./pages/project/task/TaskDetail'));
const WarehouseManagement = lazy(() => import('./pages/project/warehouse/WarehouseManagement'));

// AI 助手页面
const AssistantManagement = lazy(() => import('./pages/ai/AssistantManagement'));
const AssistantChat = lazy(() => import('./pages/ai/AssistantChat'));
const MCPToolManagement = lazy(() => import('./pages/ai/MCPToolManagement'));
const MCPServerManagement = lazy(() => import('./pages/ai/MCPServerManagement'));
const AIModelList = lazy(() => import('./pages/ai/AIModelList'));
const AIToolList = lazy(() => import('./pages/ai/AIToolList'));
const AIKnowledgeBaseList = lazy(() => import('./pages/ai/AIKnowledgeBaseList'));
const RoleAssistants = lazy(() => import('./pages/ai/RoleAssistants'));
const SystemIntegrationSettings = lazy(() => import('./pages/ai/SystemIntegrationSettings'));

// 分享页面
const SharedAssistantChat = lazy(() => import('./pages/shared/AIAssistantChat'));

function App() {
  // 初始化主题
  useEffect(() => {
    initTheme();
    initDateTimeUtils();
    
    // 初始化全局WebSocket错误处理器（仅在开发环境）
    setupGlobalWebSocketErrorHandler();
    
    console.log('[App] 应用初始化完成');
  }, []);

  // 检查用户是否已登录
  const isAuthenticated = localStorage.getItem('token') !== null;

  // 获取用户类型和用户信息
  const userType = localStorage.getItem('user_type');
  const userDataStr = localStorage.getItem('user_data');
  const userData = userDataStr ? JSON.parse(userDataStr) : null;

  // 根据用户类型确定登录页面
  const getLoginPath = () => {
    if (userType === 'admin') return '/admin/login';
    if (userType === 'service_provider') return '/service-provider/login';
    return '/login';
  };

  // 检查用户是否有权限访问特定路由
  const checkAccess = (requiredRole) => {
    if (!isAuthenticated || !userData) return false;

    // 超级管理员可以访问所有页面
    if (userData.is_system_admin || userData.is_super_admin) return true;

    // 服务商管理员只能访问服务商页面
    if (requiredRole === 'service_provider' && userData.is_service_provider_admin) return true;

    // 租户管理员只能访问租户页面
    if (requiredRole === 'tenant' && userData.is_tenant_admin) return true;

    return false;
  };

  return (
    <ConfigProvider locale={zhCN}>
      <ProjectProvider>
        <Routes>
          {/* 公共路由 */}
          <Route path="/login" element={!isAuthenticated ? <Login /> : <RootRedirect />} />
          <Route path="/admin/login" element={!isAuthenticated ? <AdminLogin /> : <Navigate to="/admin/dashboard" replace />} />
          <Route path="/service-provider/login" element={!isAuthenticated ? <ServiceProviderLogin /> : <Navigate to="/service-provider/dashboard" replace />} />
          <Route path="/register" element={!isAuthenticated ? <Register /> : <RootRedirect />} />
          <Route path="/404" element={<NotFound />} />

          {/* 分享页面 - 无需登录 */}
          <Route path="/shared/assistant/:assistantId" element={
            <Suspense fallback={<div>加载中...</div>}>
              <SharedAssistantChat />
            </Suspense>
          } />

          {/* 根路径重定向 - 根据登录状态和用户角色决定跳转目标 */}
          <Route path="/" element={<RootRedirect />} />

          {/* 移除重复的dashboard路由，使用ProjectLayout的那个 */}

          {/* 管理员路由 */}
          <Route path="/admin/dashboard" element={checkAccess('admin') ?
            <AppLayout><AdminDashboard /></AppLayout> :
            <Navigate to={getLoginPath()} replace />} />
          <Route path="/admin/users" element={checkAccess('admin') ?
            <AppLayout><PlaceholderComponent title="用户管理" /></AppLayout> :
            <Navigate to={getLoginPath()} replace />} />
          <Route path="/admin/industries" element={checkAccess('admin') ?
            <AppLayout><PlaceholderComponent title="行业管理" /></AppLayout> :
            <Navigate to={getLoginPath()} replace />} />
          <Route path="/admin/settings" element={checkAccess('admin') ?
            <AppLayout><AdminSystemSettings /></AppLayout> :
            <Navigate to={getLoginPath()} replace />} />
          <Route path="/admin/storage" element={checkAccess('admin') ?
            <AppLayout><PlaceholderComponent title="存储管理" /></AppLayout> :
            <Navigate to={getLoginPath()} replace />} />

          {/* 服务商路由 */}
          <Route path="/service-provider/dashboard" element={checkAccess('service_provider') ?
            <AppLayout><ServiceProviderDashboard /></AppLayout> :
            <Navigate to={getLoginPath()} replace />} />
          <Route path="/service-provider/tenants" element={checkAccess('service_provider') ?
            <AppLayout><TenantManagement /></AppLayout> :
            <Navigate to={getLoginPath()} replace />} />
          <Route path="/service-provider/tenants/:tenantId" element={checkAccess('service_provider') ?
            <AppLayout><TenantDetail /></AppLayout> :
            <Navigate to={getLoginPath()} replace />} />
          <Route path="/service-provider/plugins" element={checkAccess('service_provider') ?
            <AppLayout><PluginManagement /></AppLayout> :
            <Navigate to={getLoginPath()} replace />} />

          {/* 租户路由 - 项目选择页面不需要权限检查，避免重定向循环 */}
          <Route path="/tenant/projects" element={isAuthenticated ?
            <ProjectSelection /> :
            <Navigate to={getLoginPath()} replace />} />
          <Route path="/tenant/projects/create" element={checkAccess('tenant') ?
            <CreateProject /> :
            <Navigate to={getLoginPath()} replace />} />
          <Route path="/tenant/projects/create-steps" element={checkAccess('tenant') ?
            <CreateProjectSteps /> :
            <Navigate to={getLoginPath()} replace />} />
          <Route path="/tenant/dashboard" element={checkAccess('tenant') ?
            <AppLayout><PlaceholderComponent title="租户仪表盘" /></AppLayout> :
            <Navigate to={getLoginPath()} replace />} />

          {/* 插件市场路由 - 已移至项目路由 */}

          {/* 项目路由 - 使用嵌套路由方式 */}
          <Route path="/dashboard" element={isAuthenticated ? <Navigate to="/project/dashboard" replace /> : <Navigate to={getLoginPath()} replace />} />

          {/* 项目管理路由 */}
          <Route path="/project/task" element={isAuthenticated ? <ProjectLayout /> : <Navigate to={getLoginPath()} replace />}>
            <Route index element={
              <Suspense fallback={<div>加载中...</div>}>
                <TaskManagement />
              </Suspense>
            } />
            <Route path=":taskId" element={
              <Suspense fallback={<div>加载中...</div>}>
                <TaskDetail />
              </Suspense>
            } />
          </Route>

          {/* 项目功能模块路由 - 使用嵌套路由方式 */}
          <Route path="/project" element={isAuthenticated ? <ProjectLayout /> : <Navigate to={getLoginPath()} replace />}>
            {/* 用户相关 */}
            <Route path="user/profile" element={<UserProfile />} />
            <Route path="notifications" element={<NotificationCenter />} />
            <Route path="notifications/create" element={<NotificationCreate />} />

            {/* 项目空间 */}
            <Route path="space" element={<ProjectSpace />} />

            {/* 门店管理 - 所有角色都可访问，作为默认仪表盘 */}
            <Route path="store/dashboard" element={<StoreDashboardPage />} />
            <Route path="store/super-dashboard" element={
              <ProtectedRoute allowedRoles={['project_admin', 'store_admin', 'tenant_admin', 'store_admin', 'store_staff']}>
                <StoreSuperDashboard />
              </ProtectedRoute>
            } />
            <Route path="store/routine-tasks" element={<RoutineTasks />} />
            <Route path="store/routine-tasks/create" element={<PlaceholderPage title="创建例行任务" />} />
            <Route path="store/routine-tasks/edit/:id" element={<PlaceholderPage title="编辑例行任务" />} />
            <Route path="store/routine-tasks/detail/:id" element={<RoutineTaskDetail />} />
            <Route path="store/daily-operations" element={<DailyOperations />} />
            <Route path="store/sales-report" element={<SalesReport />} />
            <Route path="store/sales-report/create" element={<SalesReportForm />} />
            <Route path="store/sales-report/edit/:id" element={<SalesReportForm />} />
            <Route path="store/sales-report/detail/:id" element={<SalesReportDetail />} />
            <Route path="store/sales-monthly-report" element={<SalesMonthlyReport />} />
            <Route path="store/loss-report" element={<StoreLoss />} />

            {/* 财务管理路由 */}
            <Route path="finance/reconciliation" element={<Reconciliation />} />
            <Route path="finance/data-analysis" element={<DataAnalysis />} />
            <Route path="finance/data-analysis/sales" element={<DataAnalysis />} />
            <Route path="finance/data-analysis/recharge" element={<DataAnalysis />} />
            <Route path="finance/data-analysis/profit" element={<DataAnalysis />} />
            <Route path="finance/data-analysis/purchase" element={<DataAnalysis />} />
            <Route path="finance/business-center" element={<BusinessCenter />} />
            <Route path="finance/business-center/loss-approval" element={<BusinessCenter />} />
            <Route path="finance/business-center/store-tasks" element={<BusinessCenter />} />
            <Route path="finance/business-center/expense-approval" element={<BusinessCenter />} />
            <Route path="finance/business-center/arrival-approval" element={<BusinessCenter />} />
            <Route path="finance/business-center/inventory-audit" element={<BusinessCenter />} />

            {/* 项目管理仪表盘 - 只有项目管理员和租户管理员可访问 */}
            <Route path="dashboard" element={
              <ProtectedRoute
                allowedRoles={['project_admin', 'tenant_admin']}
              >
                <ProjectDashboardPage />
              </ProtectedRoute>
            } />

            {/* 运营管理 - 只有运营角色和项目管理员可访问 */}
            <Route path="operation/dashboard" element={
              <ProtectedRoute allowedRoles={['project_admin', 'tenant_admin', 'operation_admin', 'operation_staff', 'operation']}>
                <OperationsDashboardPage />
              </ProtectedRoute>
            } />
            <Route path="operation/super-dashboard" element={
              <ProtectedRoute allowedRoles={['project_admin', 'operation_admin', 'operation_manager', 'operation_staff', 'tenant_admin']}>
                <OperationSuperDashboard />
              </ProtectedRoute>
            } />
            <Route path="operation/price-monitoring-detail" element={
              <ProtectedRoute allowedRoles={['project_admin', 'operation_admin', 'operation_manager', 'operation_staff', 'tenant_admin', 'store_admin']}>
                <Suspense fallback={<div>加载中...</div>}>
                  <PriceMonitoringDetail />
                </Suspense>
              </ProtectedRoute>
            } />
            <Route path="operation/channel-management" element={<ChannelManagement />} />
            <Route path="operation/store-operation" element={<StoreOperation />} />
            <Route path="operation/knowledge-base/:documentId" element={<KnowledgeDetail />} />

            {/* 采购管理 - 只有采购角色和项目管理员可访问 */}
            <Route path="purchase/dashboard" element={
              <ProtectedRoute allowedRoles={['project_admin', 'tenant_admin', 'purchase_admin', 'purchase_staff', 'purchase']}>
                <PurchaseDashboardPage />
              </ProtectedRoute>
            } />
            <Route path="purchase/orders" element={<PurchaseOrderList />} />
            <Route path="purchase/orders/create" element={<PurchaseOrderForm />} />
            <Route path="purchase/orders/edit/:id" element={<PurchaseOrderForm />} />
            <Route path="purchase/orders/detail/:id" element={<PurchaseOrderDetail />} />
            <Route path="purchase/products" element={<ProductManagement />} />
            <Route path="purchase/products/:productId" element={<ProductDetail />} />
            <Route path="purchase/suppliers" element={<SupplierManagement />} />
            <Route path="purchase/suppliers/detail/:id" element={<SupplierDetail />} />
            <Route path="purchase/loss-approval" element={<LossApproval />} />

            {/* 财务管理 - 只有财务角色和项目管理员可访问 */}
            <Route path="finance/dashboard" element={
              <ProtectedRoute allowedRoles={['project_admin', 'tenant_admin', 'finance_admin', 'finance_staff', 'finance']}>
                <FinanceDashboardPage />
              </ProtectedRoute>
            } />
            <Route path="finance/sales-analysis" element={<PlaceholderPage title="销售分析" />} />
            <Route path="finance/inventory-analysis" element={<PlaceholderPage title="库存分析" />} />

            {/* 仓储管理 - 只有仓储角色和项目管理员可访问 */}
            <Route path="inventory/dashboard" element={
              <ProtectedRoute allowedRoles={['project_admin', 'tenant_admin', 'inventory_admin', 'inventory_staff', 'inventory']}>
                <WarehouseDashboardPage />
              </ProtectedRoute>
            } />
            <Route path="inventory/management" element={<Management />} />
            <Route path="inventory/alert" element={<InventoryAlert />} />
            <Route path="inventory/warehouse" element={
              <Suspense fallback={<div>加载中...</div>}>
                <WarehouseManagement />
              </Suspense>
            } />
            <Route path="inventory/transfer" element={<InventoryTransfer />} />
            <Route path="inventory/check" element={<InventoryCheck />} />

            {/* 系统设置 - 确保租户管理员和项目管理员都可以访问 */}
            <Route path="settings/users" element={<UserManagement />} />
            <Route path="settings/roles" element={<RoleManagement />} />
            <Route path="settings/system" element={<SystemSettings />} />
            <Route path="settings/basic" element={<BasicSettings />} />
            <Route path="settings/business" element={<BusinessSettings />} />
            <Route path="settings/operation-logs" element={<OperationLogs />} />
            <Route path="settings/workflow" element={<BusinessSettings />} />
            <Route path="settings/workflow/create" element={<CreateWorkflow />} />
            <Route path="settings/workflow/:id" element={<WorkflowDetail />} />
            <Route path="settings/workflow/edit/:id" element={<EditWorkflow />} />
            <Route path="user/notification-settings" element={<NotificationSettings />} />

            {/* 插件路由 - 旧版（已废弃，保留兼容） */}
            <Route path="plugin/market" element={<Navigate to="/project/plugin/plugin-market" replace />} />
            <Route path="plugin/detail/:id" element={<Navigate to="/project/plugin/plugin-market/detail/:id" replace />} />
            <Route path="plugin/marketing_game" element={<Navigate to="/project/plugin/marketing_game" replace />} />
            <Route path="plugin/marketing_game/prizes/:gameId" element={<RedirectWithParams to="/project/plugin/marketing_game/prizes/:gameId" />} />
            <Route path="plugin/marketing_game/detail/:gameId" element={<RedirectWithParams to="/project/plugin/marketing_game/detail/:gameId" />} />
            <Route path="plugin/marketing_game/participants/:gameId" element={<RedirectWithParams to="/project/plugin/marketing_game/participants/:gameId" />} />
            <Route path="plugin/marketing_game/records/:gameId" element={<RedirectWithParams to="/project/plugin/marketing_game/records/:gameId" />} />

            {/* 新版插件路由 */}
            <Route path="plugin/plugin-market" element={
              <Suspense fallback={<div>加载中...</div>}>
                <PluginMarket />
              </Suspense>
            } />
            <Route path="plugin/plugin-market/detail/:id" element={
              <Suspense fallback={<div>加载中...</div>}>
                <PluginDetail />
              </Suspense>
            } />
            <Route path="plugin/orders" element={
              <Suspense fallback={<div>加载中...</div>}>
                <PluginOrders />
              </Suspense>
            } />
            <Route path="plugin/dingtalk" element={
              <Suspense fallback={<div>Loading...</div>}>
                <DingTalkRobot />
              </Suspense>
            } />

            {/* marketing_game插件路由 */}
            <Route path="plugin/marketing_game" element={
              <Suspense fallback={<div>加载中...</div>}>
                <MarketingGamePluginList />
              </Suspense>
            } />
            <Route path="plugin/marketing_game/prizes/:gameId" element={
              <Suspense fallback={<div>加载中...</div>}>
                <MarketingGamePluginPrizes />
              </Suspense>
            } />
            <Route path="plugin/marketing_game/detail/:gameId" element={
              <Suspense fallback={<div>加载中...</div>}>
                <MarketingGamePluginDetail />
              </Suspense>
            } />
            <Route path="plugin/marketing_game/participants/:gameId" element={
              <Suspense fallback={<div>加载中...</div>}>
                <MarketingGamePluginParticipants />
              </Suspense>
            } />
            <Route path="plugin/marketing_game/records/:gameId" element={
              <Suspense fallback={<div>加载中...</div>}>
                <MarketingGamePluginRecords />
              </Suspense>
            } />

            {/* SAPI插件路由 */}
            <Route path="plugin/sapi" element={
              <Suspense fallback={<div>加载中...</div>}>
                <SAPIPlugin />
              </Suspense>
            } />
            <Route path="plugin/ai_customer_service" element={
              <Suspense fallback={<div>加载中...</div>}>
                <AICustomerServicePlugin />
              </Suspense>
            } />

            {/* 全渠道运营助手插件路由 */}
            <Route path="plugin/multi-channel-operations" element={
              <Suspense fallback={<div>加载中...</div>}>
                <MultiChannelOperations />
              </Suspense>
            } />
            <Route path="plugin/multi-channel-operations/:tab" element={
              <Suspense fallback={<div>加载中...</div>}>
                <MultiChannelOperations />
              </Suspense>
            } />

            {/* AI 助手路由 */}
            <Route path="ai/assistants" element={
              <Suspense fallback={<div>加载中...</div>}>
                <AssistantManagement />
              </Suspense>
            } />
            <Route path="ai/system-integration" element={
              <Suspense fallback={<div>加载中...</div>}>
                <SystemIntegrationSettings />
              </Suspense>
            } />
            <Route path="ai/mcp-servers" element={
              <Suspense fallback={<div>加载中...</div>}>
                <MCPServerManagement />
              </Suspense>
            } />
            <Route path="ai/mcp-tools" element={
              <Suspense fallback={<div>加载中...</div>}>
                <MCPToolManagement />
              </Suspense>
            } />
            <Route path="ai/models" element={
              <Suspense fallback={<div>加载中...</div>}>
                <AIModelList />
              </Suspense>
            } />
            <Route path="ai/chat/:assistantId" element={
              <Suspense fallback={<div>加载中...</div>}>
                <AssistantChat />
              </Suspense>
            } />
            <Route path="ai/role-assistants" element={
              <Suspense fallback={<div>加载中...</div>}>
                <RoleAssistants />
              </Suspense>
            } />
            <Route path="ai/tools" element={
              <Suspense fallback={<div>加载中...</div>}>
                <AIToolList />
              </Suspense>
            } />
            <Route path="ai/knowledge-base" element={
              <Suspense fallback={<div>加载中...</div>}>
                <AIKnowledgeBaseList />
              </Suspense>
            } />

            {/* 经营分析路由 - 项目管理核心模块 */}
            <Route path="business-analysis" element={
              <ProtectedRoute allowedRoles={['project_admin', 'tenant_admin', 'operation_admin', 'operation_staff', 'operation']}>
                <Suspense fallback={<div>加载中...</div>}>
                  <BusinessAnalysis />
                </Suspense>
              </ProtectedRoute>
            } />
          </Route>

          {/* 用户路由 */}
          <Route path="/user/profile" element={isAuthenticated ?
            <AppLayout><UserProfile /></AppLayout> :
            <Navigate to={getLoginPath()} replace />} />
          <Route path="/notifications" element={isAuthenticated ?
            <AppLayout><NotificationCenter /></AppLayout> :
            <Navigate to={getLoginPath()} replace />} />

          {/* 行业特定路由 */}
          <Route path="/retail/dashboard" element={checkAccess('tenant') ?
            <AppLayout><PlaceholderComponent title="零售仪表盘" /></AppLayout> :
            <Navigate to={getLoginPath()} replace />} />
          <Route path="/restaurant/dashboard" element={checkAccess('tenant') ?
            <AppLayout><PlaceholderComponent title="餐饮仪表盘" /></AppLayout> :
            <Navigate to={getLoginPath()} replace />} />
          <Route path="/hotel/dashboard" element={checkAccess('tenant') ?
            <AppLayout><PlaceholderComponent title="酒店仪表盘" /></AppLayout> :
            <Navigate to={getLoginPath()} replace />} />

          {/* 文件分享路由 - 不需要认证 */}
          <Route path="/space/share/:shareToken" element={<SharedFile />} />

          {/* 游戏页面路由 - 不需要认证 */}
          <Route path="/game/marketing/:gameId" element={
            <Suspense fallback={<div>加载中...</div>}>
              <GamePage />
            </Suspense>
          } />

          {/* 未匹配路由重定向到404 */}
          <Route path="*" element={<Navigate to="/404" replace />} />
        </Routes>
      </ProjectProvider>
    </ConfigProvider>
  );
}

export default App;
