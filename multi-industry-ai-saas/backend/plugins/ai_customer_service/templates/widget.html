<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ widget_config.title }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: #f5f5f5;
        }
        
        .ai-chat-widget {
            position: fixed;
            z-index: 9999;
            transition: all 0.3s ease;
        }
        
        /* 位置样式 */
        .ai-chat-widget.bottom-right {
            bottom: 20px;
            right: 20px;
        }
        
        .ai-chat-widget.bottom-left {
            bottom: 20px;
            left: 20px;
        }
        
        .ai-chat-widget.top-right {
            top: 20px;
            right: 20px;
        }
        
        .ai-chat-widget.top-left {
            top: 20px;
            left: 20px;
        }
        
        /* 尺寸样式 */
        .ai-chat-widget.small .chat-container {
            width: 300px;
            height: 400px;
        }
        
        .ai-chat-widget.medium .chat-container {
            width: 380px;
            height: 500px;
        }
        
        .ai-chat-widget.large .chat-container {
            width: 450px;
            height: 600px;
        }
        
        .chat-trigger {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: {{ widget_config.theme_color }};
            color: white;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            transition: all 0.3s ease;
        }
        
        .chat-trigger:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
        }
        
        .chat-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
            display: none;
            flex-direction: column;
            overflow: hidden;
        }
        
        .chat-container.open {
            display: flex;
        }
        
        .chat-header {
            background: {{ widget_config.theme_color }};
            color: white;
            padding: 16px 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .chat-title {
            font-size: 16px;
            font-weight: 600;
        }
        
        .chat-close {
            background: none;
            border: none;
            color: white;
            font-size: 20px;
            cursor: pointer;
            padding: 0;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #fafafa;
        }
        
        .message {
            margin-bottom: 16px;
            display: flex;
            align-items: flex-start;
        }
        
        .message.user {
            flex-direction: row-reverse;
        }
        
        .message-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: {{ widget_config.theme_color }};
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            margin: 0 8px;
        }
        
        .message-content {
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 18px;
            font-size: 14px;
            line-height: 1.4;
        }
        
        .message.user .message-content {
            background: {{ widget_config.theme_color }};
            color: white;
        }
        
        .message.ai .message-content {
            background: white;
            color: #333;
            border: 1px solid #e1e1e1;
        }
        
        .quick-replies {
            padding: 0 20px 10px;
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }
        
        .quick-reply {
            background: #f0f0f0;
            border: 1px solid #d9d9d9;
            border-radius: 16px;
            padding: 6px 12px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .quick-reply:hover {
            background: {{ widget_config.theme_color }};
            color: white;
            border-color: {{ widget_config.theme_color }};
        }
        
        .chat-input {
            padding: 16px 20px;
            border-top: 1px solid #e1e1e1;
            background: white;
        }
        
        .input-container {
            display: flex;
            align-items: center;
            background: #f5f5f5;
            border-radius: 24px;
            padding: 8px 16px;
        }
        
        .message-input {
            flex: 1;
            border: none;
            background: none;
            outline: none;
            font-size: 14px;
            padding: 8px 0;
        }
        
        .send-button {
            background: {{ widget_config.theme_color }};
            color: white;
            border: none;
            border-radius: 50%;
            width: 32px;
            height: 32px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 8px;
        }
        
        .send-button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .typing-indicator {
            display: none;
            padding: 12px 16px;
            background: white;
            border-radius: 18px;
            margin: 0 8px;
            border: 1px solid #e1e1e1;
        }
        
        .typing-dots {
            display: flex;
            gap: 4px;
        }
        
        .typing-dot {
            width: 6px;
            height: 6px;
            border-radius: 50%;
            background: #999;
            animation: typing 1.4s infinite ease-in-out;
        }
        
        .typing-dot:nth-child(1) { animation-delay: -0.32s; }
        .typing-dot:nth-child(2) { animation-delay: -0.16s; }
        
        @keyframes typing {
            0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
            40% { transform: scale(1); opacity: 1; }
        }
        
        /* 响应式设计 */
        @media (max-width: 480px) {
            .ai-chat-widget {
                bottom: 10px !important;
                right: 10px !important;
                left: 10px !important;
                top: auto !important;
            }
            
            .ai-chat-widget .chat-container {
                width: 100% !important;
                height: 70vh !important;
                max-height: 500px;
            }
            
            .chat-trigger {
                width: 50px;
                height: 50px;
                font-size: 20px;
            }
        }
        
        /* 暗色主题 */
        .ai-chat-widget.dark .chat-container {
            background: #2f2f2f;
        }
        
        .ai-chat-widget.dark .chat-messages {
            background: #1f1f1f;
        }
        
        .ai-chat-widget.dark .message.ai .message-content {
            background: #3f3f3f;
            color: #fff;
            border-color: #555;
        }
        
        .ai-chat-widget.dark .chat-input {
            background: #2f2f2f;
            border-color: #555;
        }
        
        .ai-chat-widget.dark .input-container {
            background: #1f1f1f;
        }
        
        .ai-chat-widget.dark .message-input {
            color: #fff;
        }
    </style>
</head>
<body>
    <div class="ai-chat-widget {{ widget_config.size }} {{ widget_config.position }}" id="aiChatWidget">
        <!-- 触发按钮 -->
        <button class="chat-trigger" id="chatTrigger">
            💬
        </button>
        
        <!-- 聊天容器 -->
        <div class="chat-container" id="chatContainer">
            <!-- 聊天头部 -->
            <div class="chat-header">
                <div class="chat-title">{{ widget_config.title }}</div>
                <button class="chat-close" id="chatClose">×</button>
            </div>
            
            <!-- 消息区域 -->
            <div class="chat-messages" id="chatMessages">
                <!-- 欢迎消息 -->
                <div class="message ai">
                    {% if widget_config.show_avatar %}
                    <div class="message-avatar">🤖</div>
                    {% endif %}
                    <div class="message-content">{{ widget_config.welcome_message }}</div>
                </div>
                
                <!-- 打字指示器 -->
                <div class="message ai">
                    <div class="message-avatar">🤖</div>
                    <div class="typing-indicator" id="typingIndicator">
                        <div class="typing-dots">
                            <div class="typing-dot"></div>
                            <div class="typing-dot"></div>
                            <div class="typing-dot"></div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 快捷回复 -->
            <div class="quick-replies" id="quickReplies">
                <div class="quick-reply" data-message="产品咨询">产品咨询</div>
                <div class="quick-reply" data-message="技术支持">技术支持</div>
                <div class="quick-reply" data-message="价格信息">价格信息</div>
                <div class="quick-reply" data-message="联系方式">联系方式</div>
            </div>
            
            <!-- 输入区域 -->
            <div class="chat-input">
                <div class="input-container">
                    <input type="text" class="message-input" id="messageInput" placeholder="请输入您的问题..." maxlength="500">
                    <button class="send-button" id="sendButton">➤</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 组件配置
        const widgetConfig = {{ config_json | safe }};
        
        // 组件状态
        let isOpen = {{ 'true' if widget_config.auto_open else 'false' }};
        let sessionId = null;
        let isTyping = false;
        
        // DOM 元素
        const widget = document.getElementById('aiChatWidget');
        const trigger = document.getElementById('chatTrigger');
        const container = document.getElementById('chatContainer');
        const closeBtn = document.getElementById('chatClose');
        const messagesArea = document.getElementById('chatMessages');
        const messageInput = document.getElementById('messageInput');
        const sendButton = document.getElementById('sendButton');
        const typingIndicator = document.getElementById('typingIndicator');
        const quickReplies = document.getElementById('quickReplies');
        
        // 初始化
        function init() {
            // 绑定事件
            trigger.addEventListener('click', toggleChat);
            closeBtn.addEventListener('click', closeChat);
            sendButton.addEventListener('click', sendMessage);
            messageInput.addEventListener('keypress', handleKeyPress);
            
            // 绑定快捷回复
            quickReplies.addEventListener('click', handleQuickReply);
            
            // 自动打开
            if (widgetConfig.auto_open) {
                openChat();
            }
            
            // 生成会话ID
            sessionId = 'widget_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        }
        
        // 切换聊天窗口
        function toggleChat() {
            if (isOpen) {
                closeChat();
            } else {
                openChat();
            }
        }
        
        // 打开聊天窗口
        function openChat() {
            container.classList.add('open');
            trigger.style.display = 'none';
            isOpen = true;
            messageInput.focus();
        }
        
        // 关闭聊天窗口
        function closeChat() {
            container.classList.remove('open');
            trigger.style.display = 'flex';
            isOpen = false;
        }
        
        // 处理键盘事件
        function handleKeyPress(e) {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        }
        
        // 处理快捷回复
        function handleQuickReply(e) {
            if (e.target.classList.contains('quick-reply')) {
                const message = e.target.getAttribute('data-message');
                messageInput.value = message;
                sendMessage();
            }
        }
        
        // 发送消息
        async function sendMessage() {
            const message = messageInput.value.trim();
            if (!message || isTyping) return;
            
            // 添加用户消息
            addMessage(message, 'user');
            messageInput.value = '';
            sendButton.disabled = true;
            
            // 显示打字指示器
            showTyping();
            
            try {
                // 发送到服务器
                const response = await fetch(`${widgetConfig.api_base_url}/api/v1/public/ai-customer-service/chat/${widgetConfig.widget_id}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        message: message,
                        session_id: sessionId,
                        user_info: {
                            user_agent: navigator.userAgent,
                            referrer: document.referrer,
                            timestamp: new Date().toISOString()
                        },
                        widget_config: widgetConfig
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    // 添加AI回复
                    addMessage(result.data.message, 'ai');
                    
                    // 更新会话ID
                    if (result.data.session_id) {
                        sessionId = result.data.session_id;
                    }
                    
                    // 显示建议回复
                    if (result.data.suggestions && result.data.suggestions.length > 0) {
                        updateQuickReplies(result.data.suggestions);
                    }
                } else {
                    addMessage('抱歉，系统暂时繁忙，请稍后再试。', 'ai');
                }
            } catch (error) {
                console.error('发送消息失败:', error);
                addMessage('网络连接失败，请检查网络后重试。', 'ai');
            } finally {
                hideTyping();
                sendButton.disabled = false;
                messageInput.focus();
            }
        }
        
        // 添加消息
        function addMessage(content, type) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}`;
            
            let avatarHtml = '';
            if (widgetConfig.show_avatar) {
                const avatar = type === 'user' ? '👤' : '🤖';
                avatarHtml = `<div class="message-avatar">${avatar}</div>`;
            }
            
            messageDiv.innerHTML = `
                ${avatarHtml}
                <div class="message-content">${content}</div>
            `;
            
            // 插入到打字指示器之前
            const typingMessage = typingIndicator.closest('.message');
            messagesArea.insertBefore(messageDiv, typingMessage);
            
            // 滚动到底部
            messagesArea.scrollTop = messagesArea.scrollHeight;
        }
        
        // 显示打字指示器
        function showTyping() {
            isTyping = true;
            typingIndicator.style.display = 'block';
            messagesArea.scrollTop = messagesArea.scrollHeight;
        }
        
        // 隐藏打字指示器
        function hideTyping() {
            isTyping = false;
            typingIndicator.style.display = 'none';
        }
        
        // 更新快捷回复
        function updateQuickReplies(suggestions) {
            quickReplies.innerHTML = '';
            suggestions.forEach(suggestion => {
                const replyDiv = document.createElement('div');
                replyDiv.className = 'quick-reply';
                replyDiv.setAttribute('data-message', suggestion);
                replyDiv.textContent = suggestion;
                quickReplies.appendChild(replyDiv);
            });
        }
        
        // 启动组件
        init();
    </script>
</body>
</html>
