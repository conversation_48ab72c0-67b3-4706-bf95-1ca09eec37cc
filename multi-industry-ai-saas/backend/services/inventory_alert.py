from typing import List, Optional, Dict, Any
from sqlalchemy import select, and_, or_, func
from sqlalchemy.ext.asyncio import AsyncSession
from datetime import datetime, timedelta

from models.inventory import InventoryItem
from models.product import Product
from models.warehouse import Warehouse
from models.project import Project
from schemas.inventory import InventoryAlertResponse, InventoryAlertItem, InventoryAlertSummary


class InventoryAlertService:
    """库存预警服务"""

    @staticmethod
    async def get_inventory_alerts(
        db: AsyncSession,
        project_id: str,
        warehouse_id: Optional[str] = None,
        alert_type: Optional[str] = None,
        product_category_id: Optional[str] = None,
        skip: int = 0,
        limit: int = 100
    ) -> dict:
        """获取库存预警列表"""

        # 构建基础查询条件
        conditions = [InventoryItem.project_id == project_id]

        if warehouse_id:
            conditions.append(InventoryItem.warehouse_id == warehouse_id)

        # 添加预警类型过滤
        if alert_type == "low":
            conditions.append(InventoryItem.quantity <= InventoryItem.min_quantity)
        elif alert_type == "high":
            conditions.append(InventoryItem.quantity >= InventoryItem.max_quantity)
        else:
            # 默认查询所有预警
            conditions.append(
                or_(
                    InventoryItem.quantity <= InventoryItem.min_quantity,
                    InventoryItem.quantity >= InventoryItem.max_quantity
                )
            )

        # 添加产品类别过滤
        if product_category_id:
            conditions.append(Product.category_id == product_category_id)

        # 构建查询
        query = (
            select(
                InventoryItem,
                Product.name.label("product_name"),
                Product.sku.label("product_code"),  # 使用sku替代code
                Product.category_id.label("product_category_id"),
                Warehouse.name.label("warehouse_name")
            )
            .join(Product, InventoryItem.product_id == Product.id)
            .join(Warehouse, InventoryItem.warehouse_id == Warehouse.id)
            .where(and_(*conditions))
            .order_by(InventoryItem.updated_at.desc())
            .offset(skip)
            .limit(limit)
        )

        # 执行查询
        result = await db.execute(query)
        rows = result.all()

        # 计算总数
        count_query = (
            select(func.count())
            .select_from(InventoryItem)
            .join(Product, InventoryItem.product_id == Product.id)
            .where(and_(*conditions))
        )
        count_result = await db.execute(count_query)
        total = count_result.scalar_one()

        # 构建响应数据
        items = []
        for row in rows:
            inventory_item = row.InventoryItem

            # 确定预警类型
            if inventory_item.quantity <= inventory_item.min_quantity:
                current_alert_type = "low"
            elif inventory_item.quantity >= inventory_item.max_quantity:
                current_alert_type = "high"
            else:
                current_alert_type = "normal"

            # 只返回符合预警条件的项目
            if current_alert_type != "normal":
                items.append(
                    InventoryAlertItem(
                        id=str(inventory_item.id),
                        product_id=str(inventory_item.product_id),
                        product_name=row.product_name,
                        product_sku=row.product_code,  # 使用product_code值赋给product_sku字段
                        warehouse_id=str(inventory_item.warehouse_id),
                        warehouse_name=row.warehouse_name,
                        quantity=inventory_item.quantity,
                        min_quantity=inventory_item.min_quantity,
                        max_quantity=inventory_item.max_quantity or 0,
                        alert_type=current_alert_type,
                        created_at=inventory_item.created_at,
                        updated_at=inventory_item.updated_at
                    )
                )

        return {
            "items": items,
            "total": total,
            "success": True
        }

    @staticmethod
    async def get_inventory_alert_summary(
        db: AsyncSession,
        project_id: str
    ) -> InventoryAlertSummary:
        """获取库存预警摘要"""

        # 查询低库存数量
        low_stock_query = (
            select(func.count())
            .select_from(InventoryItem)
            .where(
                and_(
                    InventoryItem.project_id == project_id,
                    InventoryItem.quantity <= InventoryItem.min_quantity
                )
            )
        )
        low_stock_result = await db.execute(low_stock_query)
        low_stock_count = low_stock_result.scalar_one()

        # 查询高库存数量
        high_stock_query = (
            select(func.count())
            .select_from(InventoryItem)
            .where(
                and_(
                    InventoryItem.project_id == project_id,
                    InventoryItem.quantity >= InventoryItem.max_quantity,
                    InventoryItem.max_quantity > 0
                )
            )
        )
        high_stock_result = await db.execute(high_stock_query)
        high_stock_count = high_stock_result.scalar_one()

        # 查询滞销商品数量
        slow_moving_query = (
            select(func.count())
            .select_from(InventoryItem)
            .where(
                and_(
                    InventoryItem.project_id == project_id,
                    InventoryItem.is_slow_moving == True
                )
            )
        )
        slow_moving_result = await db.execute(slow_moving_query)
        slow_moving_count = slow_moving_result.scalar_one()

        # 返回摘要数据
        return InventoryAlertSummary(
            low_stock_count=low_stock_count,
            high_stock_count=high_stock_count,
            slow_moving_count=slow_moving_count,
            total_alert_count=low_stock_count + high_stock_count + slow_moving_count
        )
