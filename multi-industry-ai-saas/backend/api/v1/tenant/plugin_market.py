#!/usr/bin/env python
# -*- coding: utf-8 -*-

import logging
from fastapi import APIRouter, Depends, HTTPException, status, Body, Query, Path
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import and_, or_, desc, func
from typing import Dict, List, Any, Optional
import uuid
from datetime import datetime, timedelta

from db.database import get_db
from api.deps import get_current_user, get_current_tenant, get_current_service_provider
from models.user import User
from models.tenant import Tenant
from models.core import ServiceProvider
from models.plugin import Plugin, TenantPlugin, PluginPurchase, PluginVersion

# 初始化日志
logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(prefix="/plugins", tags=["tenant_plugins"])

@router.get("/market")
async def get_plugin_market(
    category: Optional[str] = Query(None),
    search: Optional[str] = Query(None),
    page: int = Query(1, ge=1),
    page_size: int = Query(20, ge=1, le=100),
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: AsyncSession = Depends(get_db)
):
    """获取插件市场列表"""
    try:
        # 构建查询条件
        conditions = [
            Plugin.is_active == True
        ]
        
        if category:
            conditions.append(Plugin.category == category)
        
        if search:
            conditions.append(
                or_(
                    Plugin.name.ilike(f"%{search}%"),
                    Plugin.description.ilike(f"%{search}%")
                )
            )
        
        # 查询插件总数
        count_query = select(func.count()).select_from(Plugin).where(and_(*conditions))
        result = await db.execute(count_query)
        total_count = result.scalar_one()
        
        # 查询插件列表
        plugins_query = select(Plugin).where(
            and_(*conditions)
        ).order_by(
            Plugin.category,
            Plugin.name
        ).offset(
            (page - 1) * page_size
        ).limit(page_size)
        
        result = await db.execute(plugins_query)
        plugins = result.scalars().all()
        
        # 查询已安装的插件
        installed_query = select(TenantPlugin).where(
            TenantPlugin.tenant_id == current_tenant.id
        )
        result = await db.execute(installed_query)
        installed_plugins = result.scalars().all()
        installed_plugin_ids = {str(plugin.plugin_id) for plugin in installed_plugins}
        
        # 返回结果
        return {
            "success": True,
            "data": {
                "plugins": [
                    {
                        "id": str(plugin.id),
                        "code": plugin.code,
                        "name": plugin.name,
                        "description": plugin.description,
                        "version": plugin.version,
                        "author": plugin.author,
                        "website": plugin.website,
                        "icon_url": plugin.icon_url,
                        "category": plugin.category,
                        "price": plugin.price,
                        "billing_cycle": plugin.billing_cycle,
                        "is_system": plugin.is_system,
                        "requires_subscription": plugin.requires_subscription,
                        "min_subscription_plan": plugin.min_subscription_plan,
                        "features": plugin.features,
                        "is_installed": str(plugin.id) in installed_plugin_ids,
                        "created_at": plugin.created_at.isoformat() if plugin.created_at else None
                    }
                    for plugin in plugins
                ],
                "pagination": {
                    "page": page,
                    "page_size": page_size,
                    "total": total_count,
                    "pages": (total_count + page_size - 1) // page_size
                },
                "categories": [
                    {"value": "ai", "label": "AI 智能"},
                    {"value": "message", "label": "消息通知"},
                    {"value": "marketing", "label": "营销工具"},
                    {"value": "financial", "label": "财务工具"},
                    {"value": "analysis", "label": "数据分析"},
                    {"value": "integration", "label": "第三方集成"},
                    {"value": "utility", "label": "实用工具"}
                ]
            }
        }
    
    except Exception as e:
        logger.error(f"获取插件市场列表失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取插件市场列表失败: {str(e)}"
        )

@router.get("/market/{plugin_id}")
async def get_plugin_detail(
    plugin_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: AsyncSession = Depends(get_db)
):
    """获取插件详情"""
    try:
        # 查询插件
        plugin_query = select(Plugin).where(Plugin.id == plugin_id)
        result = await db.execute(plugin_query)
        plugin = result.scalar_one_or_none()
        
        if not plugin:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="插件不存在"
            )
        
        # 查询插件版本
        versions_query = select(PluginVersion).where(
            PluginVersion.plugin_id == plugin_id
        ).order_by(
            desc(PluginVersion.released_at)
        )
        result = await db.execute(versions_query)
        versions = result.scalars().all()
        
        # 查询是否已安装
        tenant_plugin_query = select(TenantPlugin).where(
            and_(
                TenantPlugin.tenant_id == current_tenant.id,
                TenantPlugin.plugin_id == plugin_id
            )
        )
        result = await db.execute(tenant_plugin_query)
        tenant_plugin = result.scalar_one_or_none()
        
        # 查询购买记录
        purchase_query = select(PluginPurchase).where(
            and_(
                PluginPurchase.tenant_id == current_tenant.id,
                PluginPurchase.plugin_id == plugin_id,
                PluginPurchase.status == "active"
            )
        ).order_by(
            desc(PluginPurchase.created_at)
        )
        result = await db.execute(purchase_query)
        purchase = result.scalar_one_or_none()
        
        # 返回结果
        return {
            "success": True,
            "data": {
                "plugin": {
                    "id": str(plugin.id),
                    "code": plugin.code,
                    "name": plugin.name,
                    "description": plugin.description,
                    "version": plugin.version,
                    "author": plugin.author,
                    "website": plugin.website,
                    "icon_url": plugin.icon_url,
                    "category": plugin.category,
                    "price": plugin.price,
                    "billing_cycle": plugin.billing_cycle,
                    "is_system": plugin.is_system,
                    "requires_subscription": plugin.requires_subscription,
                    "min_subscription_plan": plugin.min_subscription_plan,
                    "features": plugin.features,
                    "settings_schema": plugin.settings_schema,
                    "created_at": plugin.created_at.isoformat() if plugin.created_at else None
                },
                "versions": [
                    {
                        "id": str(version.id),
                        "version": version.version,
                        "release_notes": version.release_notes,
                        "is_latest": version.is_latest,
                        "released_at": version.released_at.isoformat() if version.released_at else None
                    }
                    for version in versions
                ],
                "installation": {
                    "is_installed": tenant_plugin is not None,
                    "status": tenant_plugin.status if tenant_plugin else None,
                    "installed_at": tenant_plugin.installed_at.isoformat() if tenant_plugin and tenant_plugin.installed_at else None,
                    "version": tenant_plugin.version if tenant_plugin else None
                },
                "purchase": {
                    "is_purchased": purchase is not None,
                    "purchase_id": str(purchase.id) if purchase else None,
                    "start_date": purchase.start_date.isoformat() if purchase and purchase.start_date else None,
                    "end_date": purchase.end_date.isoformat() if purchase and purchase.end_date else None,
                    "status": purchase.status if purchase else None
                }
            }
        }
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取插件详情失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取插件详情失败: {str(e)}"
        )

@router.post("/market/{plugin_id}/purchase")
async def purchase_plugin(
    plugin_id: uuid.UUID,
    purchase_data: Dict[str, Any] = Body(...),
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: AsyncSession = Depends(get_db)
):
    """购买插件"""
    try:
        # 查询插件
        plugin_query = select(Plugin).where(Plugin.id == plugin_id)
        result = await db.execute(plugin_query)
        plugin = result.scalar_one_or_none()
        
        if not plugin:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="插件不存在"
            )
        
        # 检查是否已购买
        purchase_query = select(PluginPurchase).where(
            and_(
                PluginPurchase.tenant_id == current_tenant.id,
                PluginPurchase.plugin_id == plugin_id,
                PluginPurchase.status == "active"
            )
        )
        result = await db.execute(purchase_query)
        existing_purchase = result.scalar_one_or_none()
        
        if existing_purchase:
            # 如果已购买且未过期，则返回错误
            if existing_purchase.end_date and existing_purchase.end_date > datetime.now():
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="插件已购买且未过期"
                )
        
        # 获取服务商
        service_provider_id = purchase_data.get("service_provider_id")
        service_provider = None
        
        if service_provider_id:
            sp_query = select(ServiceProvider).where(ServiceProvider.id == service_provider_id)
            result = await db.execute(sp_query)
            service_provider = result.scalar_one_or_none()
            
            if not service_provider:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="服务商不存在"
                )
        
        # 计算购买周期
        billing_cycle = purchase_data.get("billing_cycle") or plugin.billing_cycle
        duration = purchase_data.get("duration") or 1
        
        # 计算开始和结束日期
        start_date = datetime.now()
        end_date = None
        
        if billing_cycle == "monthly":
            end_date = start_date + timedelta(days=30 * duration)
        elif billing_cycle == "quarterly":
            end_date = start_date + timedelta(days=90 * duration)
        elif billing_cycle == "yearly":
            end_date = start_date + timedelta(days=365 * duration)
        
        # 创建订单号
        order_number = f"PLG{datetime.now().strftime('%Y%m%d%H%M%S')}{uuid.uuid4().hex[:6]}"
        
        # 创建购买记录
        purchase = PluginPurchase(
            id=uuid.uuid4(),
            tenant_id=current_tenant.id,
            plugin_id=plugin_id,
            service_provider_id=service_provider_id,
            order_number=order_number,
            amount=plugin.price * duration,
            currency="CNY",
            billing_cycle=billing_cycle,
            duration=duration,
            start_date=start_date,
            end_date=end_date,
            status="active",
            payment_method=purchase_data.get("payment_method", "online"),
            payment_status="paid",
            purchased_by=current_user.id
        )
        
        db.add(purchase)
        
        # 检查是否已安装
        tenant_plugin_query = select(TenantPlugin).where(
            and_(
                TenantPlugin.tenant_id == current_tenant.id,
                TenantPlugin.plugin_id == plugin_id
            )
        )
        result = await db.execute(tenant_plugin_query)
        tenant_plugin = result.scalar_one_or_none()
        
        # 如果未安装，则自动安装
        if not tenant_plugin:
            tenant_plugin = TenantPlugin(
                id=uuid.uuid4(),
                tenant_id=current_tenant.id,
                plugin_id=plugin_id,
                status="active",
                version=plugin.version,
                installed_at=datetime.now(),
                installed_by=current_user.id
            )
            db.add(tenant_plugin)
        else:
            # 如果已安装但状态不是active，则更新状态
            if tenant_plugin.status != "active":
                tenant_plugin.status = "active"
                tenant_plugin.last_updated_at = datetime.now()
        
        await db.commit()
        
        # 返回结果
        return {
            "success": True,
            "message": "插件购买成功",
            "data": {
                "purchase_id": str(purchase.id),
                "order_number": purchase.order_number,
                "amount": purchase.amount,
                "start_date": purchase.start_date.isoformat(),
                "end_date": purchase.end_date.isoformat() if purchase.end_date else None,
                "status": purchase.status
            }
        }
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"购买插件失败: {str(e)}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"购买插件失败: {str(e)}"
        )

@router.post("/market/{plugin_id}/install")
async def install_plugin(
    plugin_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: AsyncSession = Depends(get_db)
):
    """安装插件"""
    try:
        # 查询插件
        plugin_query = select(Plugin).where(Plugin.id == plugin_id)
        result = await db.execute(plugin_query)
        plugin = result.scalar_one_or_none()
        
        if not plugin:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="插件不存在"
            )
        
        # 检查是否需要购买
        if plugin.price > 0 and not plugin.is_system:
            # 查询购买记录
            purchase_query = select(PluginPurchase).where(
                and_(
                    PluginPurchase.tenant_id == current_tenant.id,
                    PluginPurchase.plugin_id == plugin_id,
                    PluginPurchase.status == "active"
                )
            )
            result = await db.execute(purchase_query)
            purchase = result.scalar_one_or_none()
            
            if not purchase:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="请先购买插件"
                )
            
            # 检查是否过期
            if purchase.end_date and purchase.end_date < datetime.now():
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="插件已过期，请续费"
                )
        
        # 检查是否已安装
        tenant_plugin_query = select(TenantPlugin).where(
            and_(
                TenantPlugin.tenant_id == current_tenant.id,
                TenantPlugin.plugin_id == plugin_id
            )
        )
        result = await db.execute(tenant_plugin_query)
        tenant_plugin = result.scalar_one_or_none()
        
        if tenant_plugin:
            # 如果已安装，则更新状态
            tenant_plugin.status = "active"
            tenant_plugin.version = plugin.version
            tenant_plugin.last_updated_at = datetime.now()
        else:
            # 创建安装记录
            tenant_plugin = TenantPlugin(
                id=uuid.uuid4(),
                tenant_id=current_tenant.id,
                plugin_id=plugin_id,
                status="active",
                version=plugin.version,
                installed_at=datetime.now(),
                installed_by=current_user.id
            )
            db.add(tenant_plugin)
        
        await db.commit()
        
        # 初始化插件
        if plugin.installation_path and plugin.entry_point:
            try:
                # 动态导入插件模块
                module_path = f"{plugin.installation_path}"
                module = __import__(module_path, fromlist=["*"])
                
                # 获取初始化函数
                init_func = getattr(module, plugin.entry_point, None)
                
                if init_func and callable(init_func):
                    # 执行初始化
                    await init_func()
            except Exception as e:
                logger.error(f"初始化插件失败: {str(e)}")
        
        # 返回结果
        return {
            "success": True,
            "message": "插件安装成功",
            "data": {
                "plugin_id": str(plugin_id),
                "tenant_id": str(current_tenant.id),
                "status": "active",
                "version": plugin.version,
                "installed_at": datetime.now().isoformat()
            }
        }
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"安装插件失败: {str(e)}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"安装插件失败: {str(e)}"
        )

@router.post("/market/{plugin_id}/uninstall")
async def uninstall_plugin(
    plugin_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: AsyncSession = Depends(get_db)
):
    """卸载插件"""
    try:
        # 查询插件
        plugin_query = select(Plugin).where(Plugin.id == plugin_id)
        result = await db.execute(plugin_query)
        plugin = result.scalar_one_or_none()
        
        if not plugin:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="插件不存在"
            )
        
        # 检查是否已安装
        tenant_plugin_query = select(TenantPlugin).where(
            and_(
                TenantPlugin.tenant_id == current_tenant.id,
                TenantPlugin.plugin_id == plugin_id
            )
        )
        result = await db.execute(tenant_plugin_query)
        tenant_plugin = result.scalar_one_or_none()
        
        if not tenant_plugin:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="插件未安装"
            )
        
        # 更新状态
        tenant_plugin.status = "uninstalled"
        tenant_plugin.last_updated_at = datetime.now()
        
        await db.commit()
        
        # 返回结果
        return {
            "success": True,
            "message": "插件卸载成功",
            "data": {
                "plugin_id": str(plugin_id),
                "tenant_id": str(current_tenant.id),
                "status": "uninstalled"
            }
        }
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"卸载插件失败: {str(e)}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"卸载插件失败: {str(e)}"
        )

@router.get("/installed")
async def get_installed_plugins(
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: AsyncSession = Depends(get_db)
):
    """获取已安装的插件列表"""
    try:
        # 查询已安装的插件
        query = select(
            TenantPlugin,
            Plugin
        ).join(
            Plugin,
            TenantPlugin.plugin_id == Plugin.id
        ).where(
            and_(
                TenantPlugin.tenant_id == current_tenant.id,
                TenantPlugin.status == "active"
            )
        )
        
        result = await db.execute(query)
        installed_plugins = result.all()
        
        # 返回结果
        return {
            "success": True,
            "data": [
                {
                    "id": str(tenant_plugin.id),
                    "plugin_id": str(tenant_plugin.plugin_id),
                    "status": tenant_plugin.status,
                    "version": tenant_plugin.version,
                    "installed_at": tenant_plugin.installed_at.isoformat() if tenant_plugin.installed_at else None,
                    "settings": tenant_plugin.settings,
                    "plugin": {
                        "id": str(plugin.id),
                        "code": plugin.code,
                        "name": plugin.name,
                        "description": plugin.description,
                        "icon_url": plugin.icon_url,
                        "category": plugin.category,
                        "entry_point": plugin.entry_point
                    }
                }
                for tenant_plugin, plugin in installed_plugins
            ]
        }
    
    except Exception as e:
        logger.error(f"获取已安装的插件列表失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取已安装的插件列表失败: {str(e)}"
        )

@router.get("/installed/{plugin_id}/settings")
async def get_plugin_settings(
    plugin_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: AsyncSession = Depends(get_db)
):
    """获取插件设置"""
    try:
        # 查询已安装的插件
        query = select(
            TenantPlugin,
            Plugin
        ).join(
            Plugin,
            TenantPlugin.plugin_id == Plugin.id
        ).where(
            and_(
                TenantPlugin.tenant_id == current_tenant.id,
                TenantPlugin.plugin_id == plugin_id,
                TenantPlugin.status == "active"
            )
        )
        
        result = await db.execute(query)
        installed_plugin = result.first()
        
        if not installed_plugin:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="插件未安装或未激活"
            )
        
        tenant_plugin, plugin = installed_plugin
        
        # 返回结果
        return {
            "success": True,
            "data": {
                "id": str(tenant_plugin.id),
                "plugin_id": str(tenant_plugin.plugin_id),
                "settings": tenant_plugin.settings,
                "plugin": {
                    "id": str(plugin.id),
                    "code": plugin.code,
                    "name": plugin.name,
                    "settings_schema": plugin.settings_schema
                }
            }
        }
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取插件设置失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取插件设置失败: {str(e)}"
        )

@router.post("/installed/{plugin_id}/settings")
async def update_plugin_settings(
    plugin_id: uuid.UUID,
    settings: Dict[str, Any] = Body(...),
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: AsyncSession = Depends(get_db)
):
    """更新插件设置"""
    try:
        # 查询已安装的插件
        query = select(TenantPlugin).where(
            and_(
                TenantPlugin.tenant_id == current_tenant.id,
                TenantPlugin.plugin_id == plugin_id,
                TenantPlugin.status == "active"
            )
        )
        
        result = await db.execute(query)
        tenant_plugin = result.scalar_one_or_none()
        
        if not tenant_plugin:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="插件未安装或未激活"
            )
        
        # 更新设置
        tenant_plugin.settings = settings
        tenant_plugin.last_updated_at = datetime.now()
        
        await db.commit()
        
        # 返回结果
        return {
            "success": True,
            "message": "插件设置更新成功",
            "data": {
                "id": str(tenant_plugin.id),
                "plugin_id": str(tenant_plugin.plugin_id),
                "settings": tenant_plugin.settings
            }
        }
    
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新插件设置失败: {str(e)}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新插件设置失败: {str(e)}"
        )
