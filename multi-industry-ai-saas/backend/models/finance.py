from sqlalchemy import Column, String, Float, <PERSON>olean, Foreign<PERSON>ey, Text, JSON, Enum, DateTime, Integer
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid
from datetime import datetime
from .base import TimestampMixin
from db.database import Base

class FinancialReconciliation(Base, TimestampMixin):
    """财务对账模型"""
    __tablename__ = "financial_reconciliations"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id", ondelete="CASCADE"), nullable=False)
    store_id = Column(UUID(as_uuid=True), ForeignKey("stores.id", ondelete="CASCADE"), nullable=True)
    channel_id = Column(UUID(as_uuid=True), ForeignKey("sales_channels.id", ondelete="CASCADE"), nullable=True)
    reconciliation_date = Column(DateTime, nullable=False)
    reconciliation_type = Column(Enum("store", "channel", name="reconciliation_type"), nullable=False)
    reported_sales = Column(Float, default=0.0, nullable=False)
    system_sales = Column(Float, default=0.0, nullable=False)
    reported_recharge = Column(Float, default=0.0, nullable=False)
    system_recharge = Column(Float, default=0.0, nullable=False)
    sales_diff = Column(Float, default=0.0, nullable=False)
    recharge_diff = Column(Float, default=0.0, nullable=False)
    status = Column(Enum("normal", "warning", "error", name="reconciliation_status"), default="normal", nullable=False)
    details = Column(JSON, nullable=True)
    notes = Column(Text, nullable=True)

    # 关联关系
    project = relationship("Project", back_populates="financial_reconciliations")
    store = relationship("Store", back_populates="financial_reconciliations")
    channel = relationship("SalesChannel", back_populates="financial_reconciliations")

    def __repr__(self):
        return f"<FinancialReconciliation {self.id}>"


class ReconciliationUpload(Base, TimestampMixin):
    """对账数据上传记录"""
    __tablename__ = "reconciliation_uploads"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id", ondelete="CASCADE"), nullable=False)
    file_name = Column(String(255), nullable=False)
    file_path = Column(String(255), nullable=False)
    upload_type = Column(Enum("store", "channel", name="upload_type"), nullable=False)
    reconciliation_date = Column(DateTime, nullable=False)
    status = Column(Enum("pending", "processed", "failed", name="upload_status"), default="pending", nullable=False)
    result = Column(JSON, nullable=True)
    processed_count = Column(Integer, default=0, nullable=False)
    error_count = Column(Integer, default=0, nullable=False)

    # 关联关系
    project = relationship("Project", back_populates="reconciliation_uploads")

    def __repr__(self):
        return f"<ReconciliationUpload {self.id}>"
