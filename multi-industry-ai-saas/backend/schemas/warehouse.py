from typing import Optional, Dict, Any, List, Union
from pydantic import BaseModel, UUID4, Field
from datetime import datetime
import uuid

# 仓库基础模型
class WarehouseBase(BaseModel):
    name: str
    code: str
    type: str  # central(总仓), regional(区域仓), distribution(配送中心), front(前置仓)
    address: Optional[str] = None
    phone: Optional[str] = None
    manager: Optional[str] = None
    capacity: Optional[float] = None
    capacity_unit: Optional[str] = "sqm"
    business_hours: Optional[str] = None
    status: str = "active"

# 创建仓库请求模型
class WarehouseCreate(WarehouseBase):
    project_id: Optional[UUID4] = None
    settings: Optional[Dict[str, Any]] = None
    manager_user_id: Optional[UUID4] = None

# 更新仓库请求模型
class WarehouseUpdate(WarehouseBase):
    settings: Optional[Dict[str, Any]] = None
    manager_user_id: Optional[UUID4] = None

# 仓库响应模型
class Warehouse(WarehouseBase):
    id: UUID4
    project_id: UUID4
    settings: Optional[Dict[str, Any]] = None
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        model_config = {"from_attributes": True}

# 仓库详情响应模型（包含管理员信息）
class WarehouseDetail(Warehouse):
    manager_user_id: Optional[UUID4] = None

    class Config:
        model_config = {"from_attributes": True}

# 仓库列表响应模型
class WarehouseList(BaseModel):
    items: List[Warehouse]
    total: int
    page: int
    size: int
    pages: int

# 仓库设置基础模型
class WarehouseSettingBase(BaseModel):
    warehouse_mode: str = "multi"  # central(总仓模式), multi(多仓模式), front(前置仓模式)
    default_business_hours_start: str = "09:00"
    default_business_hours_end: str = "18:00"
    enable_front_warehouse: bool = True
    enable_cross_warehouse_transfer: bool = True
    inventory_warning_threshold: int = 10
    auto_replenishment: bool = True
    default_capacity_unit: str = "sqm"

# 创建仓库设置请求模型
class WarehouseSettingCreate(WarehouseSettingBase):
    project_id: Optional[UUID4] = None

# 更新仓库设置请求模型
class WarehouseSettingUpdate(WarehouseSettingBase):
    pass

# 仓库设置响应模型
class WarehouseSetting(WarehouseSettingBase):
    id: UUID4
    project_id: UUID4
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        model_config = {"from_attributes": True}
        from_attributes = True

# 仓库入库单项模型
class WarehouseInboundItemBase(BaseModel):
    product_id: Optional[uuid.UUID] = None
    product_name: str
    product_code: Optional[str] = None
    product_specification: Optional[str] = None
    product_unit: Optional[str] = None
    quantity: float
    unit_price: float
    purchase_order_item_id: Optional[uuid.UUID] = None

class WarehouseInboundItemCreate(WarehouseInboundItemBase):
    pass

class WarehouseInboundItemUpdate(WarehouseInboundItemBase):
    pass

class WarehouseInboundItem(WarehouseInboundItemBase):
    id: uuid.UUID
    inbound_id: uuid.UUID
    project_id: uuid.UUID
    tenant_id: Optional[uuid.UUID] = None
    total_amount: float

    class Config:
        model_config = {"from_attributes": True}
        from_attributes = True

# 仓库入库单模型
class WarehouseInboundBase(BaseModel):
    inbound_number: str
    purchase_order_id: Optional[uuid.UUID] = None
    inbound_date: str
    status: str
    notes: Optional[str] = None

class WarehouseInboundCreate(WarehouseInboundBase):
    items: List[WarehouseInboundItemCreate]

class WarehouseInboundUpdate(WarehouseInboundBase):
    items: List[WarehouseInboundItemUpdate]

class WarehouseInbound(WarehouseInboundBase):
    id: uuid.UUID
    warehouse_id: uuid.UUID
    project_id: uuid.UUID
    tenant_id: Optional[uuid.UUID] = None
    purchase_order_number: Optional[str] = None
    total_amount: float = 0.0
    item_count: int = 0
    created_at: datetime
    updated_at: datetime
    created_by: Optional[uuid.UUID] = None
    updated_by: Optional[uuid.UUID] = None
    items: Optional[List[WarehouseInboundItem]] = None

    class Config:
        model_config = {"from_attributes": True}
        from_attributes = True

# 响应模型
class WarehouseResponse(BaseModel):
    success: bool
    message: str
    data: Optional[Warehouse] = None

class WarehouseListResponse(BaseModel):
    success: bool
    message: str
    total: int
    items: List[Warehouse]

class WarehouseInboundResponse(BaseModel):
    success: bool
    message: str
    data: Optional[WarehouseInbound] = None

class WarehouseInboundListResponse(BaseModel):
    success: bool
    message: str
    total: int
    items: List[WarehouseInbound]