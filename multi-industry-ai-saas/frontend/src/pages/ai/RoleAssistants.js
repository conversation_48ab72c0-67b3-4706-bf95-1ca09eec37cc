import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Row, 
  Col, 
  Typography, 
  Button, 
  Empty, 
  Spin, 
  message,
  Tag,
  Avatar,
  Space
} from 'antd';
import { 
  RobotOutlined, 
  MessageOutlined, 
  ArrowLeftOutlined,
  PlayCircleOutlined 
} from '@ant-design/icons';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useProject } from '../../contexts/ProjectContext';
import apiService from '../../services/api';

const { Title, Text, Paragraph } = Typography;

const RoleAssistants = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { currentProject } = useProject();
  const [loading, setLoading] = useState(false);
  const [assistants, setAssistants] = useState([]);
  
  const role = searchParams.get('role') || 'store_manager';
  
  const roleNames = {
    'store_manager': '门店经理',
    'finance_manager': '财务经理',
    'warehouse_manager': '仓库经理',
    'purchase_manager': '采购经理',
    'operation_manager': '运营经理'
  };

  useEffect(() => {
    fetchRoleAssistants();
  }, [role, currentProject.id]);

  const fetchRoleAssistants = async () => {
    setLoading(true);
    try {
      const response = await apiService.project.ai.aiAssistants.list(currentProject.id);
      // 过滤出发布给当前角色的助手
      const roleAssistants = (response.data || []).filter(assistant => 
        assistant.published_roles && assistant.published_roles.includes(role)
      );
      setAssistants(roleAssistants);
    } catch (error) {
      console.error('获取角色AI助手失败:', error);
      message.error('获取AI助手失败');
    } finally {
      setLoading(false);
    }
  };

  const handleChatWithAssistant = (assistant) => {
    // 跳转到聊天页面
    navigate(`/project/ai/chat/${assistant.id}`);
  };

  return (
    <div style={{ padding: '24px' }}>
      <Card>
        <div style={{ marginBottom: '24px' }}>
          <Button 
            icon={<ArrowLeftOutlined />} 
            onClick={() => navigate(-1)}
            style={{ marginBottom: '16px' }}
          >
            返回
          </Button>
          <Title level={2}>
            <RobotOutlined style={{ marginRight: '8px' }} />
            {roleNames[role]}专用AI助手
          </Title>
          <Paragraph type="secondary">
            为{roleNames[role]}角色定制的AI助手，帮助您更高效地完成工作任务
          </Paragraph>
        </div>

        <Spin spinning={loading}>
          {assistants.length > 0 ? (
            <Row gutter={[16, 16]}>
              {assistants.map(assistant => (
                <Col xs={24} sm={12} md={8} lg={6} key={assistant.id}>
                  <Card
                    hoverable
                    actions={[
                      <Button 
                        type="primary" 
                        icon={<MessageOutlined />}
                        onClick={() => handleChatWithAssistant(assistant)}
                        block
                      >
                        开始对话
                      </Button>
                    ]}
                  >
                    <Card.Meta
                      avatar={
                        <Avatar 
                          size={48} 
                          icon={<RobotOutlined />}
                          style={{ backgroundColor: '#1890ff' }}
                        />
                      }
                      title={
                        <div>
                          <div style={{ marginBottom: '4px' }}>{assistant.name}</div>
                          <Space>
                            <Tag color={assistant.status === 'active' ? 'green' : 'default'}>
                              {assistant.status === 'active' ? '活跃' : '停用'}
                            </Tag>
                            <Tag color="blue">{assistant.model_name}</Tag>
                          </Space>
                        </div>
                      }
                      description={
                        <div>
                          <Paragraph 
                            ellipsis={{ rows: 2 }} 
                            style={{ marginBottom: '8px' }}
                          >
                            {assistant.description || '暂无描述'}
                          </Paragraph>
                          {assistant.welcome_message && (
                            <Text type="secondary" style={{ fontSize: '12px' }}>
                              "{assistant.welcome_message}"
                            </Text>
                          )}
                        </div>
                      }
                    />
                  </Card>
                </Col>
              ))}
            </Row>
          ) : (
            <Empty 
              description={
                <div>
                  <div>暂无为{roleNames[role]}发布的AI助手</div>
                  <div style={{ marginTop: '8px' }}>
                    <Text type="secondary">
                      请联系管理员为您的角色配置专用AI助手
                    </Text>
                  </div>
                </div>
              }
            />
          )}
        </Spin>
      </Card>
    </div>
  );
};

export default RoleAssistants; 