import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  DatePicker,
  Select,
  Button,
  Table,
  Typography,
  Space,
  Statistic,
  Spin,
  Empty,
  message
} from 'antd';
import {
  DownloadOutlined,
  LineChartOutlined,
  PieChartOutlined,
  BarChartOutlined,
  FileExcelOutlined
} from '@ant-design/icons';
import { Line, Pie, Column } from '@ant-design/plots';
import dayjs from 'dayjs';
import apiService from '../../../services/api';

const { Title, Text } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;

const PurchaseReport = () => {
  // 状态定义
  const [loading, setLoading] = useState(false);
  const [reportData, setReportData] = useState(null);
  const [dateRange, setDateRange] = useState([dayjs().subtract(29, 'days'), dayjs()]);
  const [reportType, setReportType] = useState('overview');
  const [suppliers, setSuppliers] = useState([]);
  const [selectedSupplier, setSelectedSupplier] = useState(null);
  const [warehouses, setWarehouses] = useState([]);
  const [selectedWarehouse, setSelectedWarehouse] = useState(null);

  // 初始化加载
  useEffect(() => {
    fetchSuppliers();
    fetchWarehouses();
  }, []);

  useEffect(() => {
    if (dateRange && dateRange.length === 2) {
      fetchReportData();
    }
  }, [dateRange, reportType, selectedSupplier, selectedWarehouse]);

  // 获取供应商列表
  const fetchSuppliers = async () => {
    try {
      const response = await apiService.project.supplier.getList();
      setSuppliers(response.items || []);
    } catch (error) {
      console.error('获取供应商列表失败:', error);
    }
  };

  // 获取仓库列表
  const fetchWarehouses = async () => {
    try {
      const response = await apiService.project.warehouse.getList();
      setWarehouses(response.items || []);
    } catch (error) {
      console.error('获取仓库列表失败:', error);
    }
  };

  // 获取报表数据
  const fetchReportData = async () => {
    setLoading(true);
    try {
      const params = {
        start_date: dateRange[0].format('YYYY-MM-DD'),
        end_date: dateRange[1].format('YYYY-MM-DD'),
        report_type: reportType,
        supplier_id: selectedSupplier,
        warehouse_id: selectedWarehouse
      };

      const response = await apiService.project.purchaseOrder.getReport(params);
      setReportData(response);
    } catch (error) {
      console.error('获取采购报表数据失败:', error);
      message.error('获取报表数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 导出报表
  const handleExport = async () => {
    try {
      const params = {
        start_date: dateRange[0].format('YYYY-MM-DD'),
        end_date: dateRange[1].format('YYYY-MM-DD'),
        report_type: reportType,
        supplier_id: selectedSupplier,
        warehouse_id: selectedWarehouse,
        export: true
      };

      const response = await apiService.project.purchaseOrder.exportReport(params);
      
      // 创建下载链接
      if (response.download_url) {
        const link = document.createElement('a');
        link.href = response.download_url;
        link.download = `采购报表_${dayjs().format('YYYY-MM-DD')}.xlsx`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        message.success('报表导出成功');
      }
    } catch (error) {
      console.error('导出采购报表失败:', error);
      message.error('导出报表失败');
    }
  };

  // 渲染统计卡片
  const renderStatistics = () => {
    if (!reportData || !reportData.statistics) {
      return null;
    }

    const { statistics } = reportData;

    return (
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="采购订单总数"
              value={statistics.total_orders || 0}
              prefix={<LineChartOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="采购总金额"
              value={statistics.total_amount || 0}
              prefix="¥"
              precision={2}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="供应商数量"
              value={statistics.supplier_count || 0}
              prefix={<PieChartOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="商品种类"
              value={statistics.product_count || 0}
              prefix={<BarChartOutlined />}
            />
          </Card>
        </Col>
      </Row>
    );
  };

  // 渲染采购趋势图表
  const renderTrendChart = () => {
    if (!reportData || !reportData.trend_data || reportData.trend_data.length === 0) {
      return <Empty description="暂无趋势数据" />;
    }

    const config = {
      data: reportData.trend_data,
      xField: 'date',
      yField: 'amount',
      smooth: true,
      point: {
        size: 4,
        shape: 'circle'
      },
      tooltip: {
        formatter: (datum) => {
          return { name: '采购金额', value: '¥' + (datum.amount ? datum.amount.toFixed(2) : '0.00') };
        }
      }
    };

    return <Line {...config} height={300} />;
  };

  // 渲染供应商分布图表
  const renderSupplierChart = () => {
    if (!reportData || !reportData.supplier_data || reportData.supplier_data.length === 0) {
      return <Empty description="暂无供应商数据" />;
    }

    const config = {
      data: reportData.supplier_data,
      angleField: 'amount',
      colorField: 'name',
      radius: 0.8,
      legend: {
        position: 'bottom'
      }
    };

    return <Pie {...config} height={300} />;
  };

  // 渲染明细表格
  const renderDetailTable = () => {
    if (!reportData || !reportData.detail_data || reportData.detail_data.length === 0) {
      return <Empty description="暂无明细数据" />;
    }

    const columns = [
      {
        title: '订单编号',
        dataIndex: 'order_number',
        key: 'order_number',
      },
      {
        title: '供应商',
        dataIndex: 'supplier_name',
        key: 'supplier_name',
      },
      {
        title: '仓库',
        dataIndex: 'warehouse_name',
        key: 'warehouse_name',
      },
      {
        title: '订单金额',
        dataIndex: 'total_amount',
        key: 'total_amount',
        render: (amount) => `¥${parseFloat(amount || 0).toFixed(2)}`,
        align: 'right'
      },
      {
        title: '下单日期',
        dataIndex: 'order_date',
        key: 'order_date',
        render: (date) => date ? dayjs(date).format('YYYY-MM-DD') : '-'
      },
      {
        title: '状态',
        dataIndex: 'status',
        key: 'status',
        render: (status) => {
          const statusMap = {
            draft: '草稿',
            confirmed: '已确认',
            received: '已收货',
            cancelled: '已取消'
          };
          return statusMap[status] || status;
        }
      }
    ];

    return (
      <Table
        columns={columns}
        dataSource={reportData.detail_data}
        rowKey="id"
        pagination={{
          pageSize: 10,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `共 ${total} 条记录`
        }}
        scroll={{ x: 800 }}
      />
    );
  };

  return (
    <div>
      {/* 筛选条件 */}
      <Card style={{ marginBottom: 16 }}>
        <Row gutter={16} align="middle">
          <Col span={6}>
            <Text strong>日期范围：</Text>
            <RangePicker
              value={dateRange}
              onChange={setDateRange}
              style={{ width: '100%' }}
            />
          </Col>
          <Col span={4}>
            <Text strong>报表类型：</Text>
            <Select
              value={reportType}
              onChange={setReportType}
              style={{ width: '100%' }}
            >
              <Option value="overview">总览报表</Option>
              <Option value="supplier">供应商报表</Option>
              <Option value="product">商品报表</Option>
              <Option value="trend">趋势报表</Option>
            </Select>
          </Col>
          <Col span={4}>
            <Text strong>供应商：</Text>
            <Select
              value={selectedSupplier}
              onChange={setSelectedSupplier}
              allowClear
              placeholder="全部供应商"
              style={{ width: '100%' }}
            >
              {suppliers.map(supplier => (
                <Option key={supplier.id} value={supplier.id}>
                  {supplier.name}
                </Option>
              ))}
            </Select>
          </Col>
          <Col span={4}>
            <Text strong>仓库：</Text>
            <Select
              value={selectedWarehouse}
              onChange={setSelectedWarehouse}
              allowClear
              placeholder="全部仓库"
              style={{ width: '100%' }}
            >
              {warehouses.map(warehouse => (
                <Option key={warehouse.id} value={warehouse.id}>
                  {warehouse.name}
                </Option>
              ))}
            </Select>
          </Col>
          <Col span={6}>
            <Space>
              <Button
                type="primary"
                onClick={fetchReportData}
                loading={loading}
              >
                查询
              </Button>
              <Button
                icon={<FileExcelOutlined />}
                onClick={handleExport}
              >
                导出Excel
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      <Spin spinning={loading}>
        {reportData && (
          <>
            {/* 统计卡片 */}
            {renderStatistics()}

            {/* 图表展示 */}
            <Row gutter={16} style={{ marginBottom: 24 }}>
              <Col span={12}>
                <Card title="采购趋势">
                  {renderTrendChart()}
                </Card>
              </Col>
              <Col span={12}>
                <Card title="供应商分布">
                  {renderSupplierChart()}
                </Card>
              </Col>
            </Row>

            {/* 明细表格 */}
            <Card title="采购明细">
              {renderDetailTable()}
            </Card>
          </>
        )}

        {!reportData && !loading && (
          <Card>
            <Empty description="请选择日期范围后查询报表数据" />
          </Card>
        )}
      </Spin>
    </div>
  );
};

export default PurchaseReport; 