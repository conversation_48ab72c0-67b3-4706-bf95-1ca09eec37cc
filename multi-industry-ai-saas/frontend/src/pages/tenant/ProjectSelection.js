import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Button, Input, Typography, Tag, Spin, Empty, message, Tooltip } from 'antd';
import { PlusOutlined, SearchOutlined, StarOutlined, StarFilled, UserOutlined, TeamOutlined, AppstoreOutlined } from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { tenant, setProjectId } from '../../services';
import './ProjectSelection.css';

const { Title, Text } = Typography;

const ProjectSelection = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [projects, setProjects] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchText, setSearchText] = useState('');
  const [favorites, setFavorites] = useState([]);

  // 在组件加载时重置重定向计数器，防止无限循环
  useEffect(() => {
    console.log('ProjectSelection - 组件加载，重置重定向计数器');
    localStorage.setItem('redirect_attempts', '0');

    // 检查用户是否有项目角色，如果没有，尝试设置默认角色
    const userData = JSON.parse(localStorage.getItem('user_data') || '{}');
    if (!userData.project_role && userData.role && userData.role.code) {
      console.log('ProjectSelection - 用户没有项目角色，尝试设置默认角色');
      const roleCode = userData.role.code.toLowerCase();
      if (roleCode.includes('store')) {
        userData.project_role = 'store_admin';
      } else if (roleCode.includes('purchase')) {
        userData.project_role = 'purchase_admin';
      } else if (roleCode.includes('inventory')) {
        userData.project_role = 'inventory_admin';
      } else if (roleCode.includes('operation')) {
        userData.project_role = 'operation_admin';
      } else if (roleCode.includes('finance')) {
        userData.project_role = 'finance_admin';
      }

      // 更新本地存储中的用户数据
      localStorage.setItem('user_data', JSON.stringify(userData));
      console.log('ProjectSelection - 已更新用户数据:', userData);
    }
  }, []);

  // 获取项目列表
  useEffect(() => {
    const fetchProjects = async () => {
      try {
        setLoading(true);
        const response = await tenant.project.getList();

        // 处理API响应，确保我们有一个项目数组
        let projectsData = [];
        if (Array.isArray(response)) {
          projectsData = response;
        } else if (response && Array.isArray(response.items)) {
          projectsData = response.items;
        } else if (response && typeof response === 'object') {
          // 如果响应是一个对象，尝试提取项目数据
          console.log('API响应格式:', response);
          projectsData = response.projects || response.data || [];
        }

        setProjects(projectsData);

        // 从本地存储获取收藏的项目
        const storedFavorites = localStorage.getItem('favoriteProjects');
        if (storedFavorites) {
          setFavorites(JSON.parse(storedFavorites));
        }
      } catch (error) {
        console.error('获取项目列表失败:', error);
        message.error('获取项目列表失败');
      } finally {
        setLoading(false);
      }
    };

    fetchProjects();
  }, []);

  // 处理项目选择
  const handleProjectSelect = async (project) => {
    try {
      // 设置当前项目
      setProjectId(project.id);

      // 保存项目ID和名称到localStorage
      localStorage.setItem('project_id', project.id);
      localStorage.setItem('project_name', project.name);

      // 获取用户数据
      const userData = JSON.parse(localStorage.getItem('user_data') || '{}');

      // 在进入项目前，先获取用户在该项目中的角色信息
      try {
        const response = await fetch(`/api/v1/auth/me?project_id=${project.id}`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'X-Project-ID': project.id
          }
        });

        if (response.ok) {
          const userInfo = await response.json();
          console.log('获取用户项目角色信息成功:', userInfo);

          // 更新用户数据中的项目角色信息
          userData.project_role = userInfo.project_role;
          userData.project_role_name = userInfo.project_role_name;
          userData.is_project_admin = userInfo.is_project_admin;
          userData.project_id = userInfo.project_id || project.id;

          // 保存更新后的用户数据
          localStorage.setItem('user_data', JSON.stringify(userData));
        } else {
          console.error('获取用户项目角色信息失败:', response.statusText);
        }
      } catch (error) {
        console.error('获取用户项目角色信息出错:', error);
      }

      // 记录最近访问的项目
      const recentProjects = JSON.parse(localStorage.getItem('recentProjects') || '[]');
      const updatedRecentProjects = [
        project.id,
        ...recentProjects.filter(id => id !== project.id)
      ].slice(0, 5); // 只保留最近5个

      localStorage.setItem('recentProjects', JSON.stringify(updatedRecentProjects));

      // 重置重定向计数器，防止无限循环
      localStorage.removeItem('redirect_attempts');

      // 导航到项目仪表盘
      message.success('正在进入项目: ' + project.name);

      // 使用window.location.href而不是navigate，确保页面完全刷新
      // 根据用户角色决定跳转目标
      let redirectPath = '/dashboard';

      // 如果用户有项目角色，根据角色跳转到相应的仪表盘
      if (userData.project_role) {
        const projectRole = userData.project_role.toLowerCase();
        if (projectRole.includes('store')) {
          redirectPath = '/project/store/dashboard';
        } else if (projectRole.includes('purchase')) {
          redirectPath = '/project/purchase/dashboard';
        } else if (projectRole.includes('inventory')) {
          redirectPath = '/project/inventory/dashboard';
        } else if (projectRole.includes('operation')) {
          redirectPath = '/project/operation/dashboard';
        } else if (projectRole.includes('finance')) {
          redirectPath = '/project/finance/dashboard';
        } else if (userData.is_project_admin || projectRole.includes('project_admin')) {
          redirectPath = '/project/dashboard';
        }
      }

      console.log('项目选择 - 跳转到:', redirectPath);
      window.location.href = redirectPath;
    } catch (error) {
      console.error('选择项目失败:', error);
      message.error('选择项目失败');
    }
  };

  // 处理创建项目
  const handleCreateProjectSteps = () => {
    navigate('/tenant/projects/create-steps');
  };

  // 处理收藏/取消收藏项目
  const handleToggleFavorite = (e, projectId) => {
    e.stopPropagation();

    let newFavorites;
    if (favorites.includes(projectId)) {
      newFavorites = favorites.filter(id => id !== projectId);
    } else {
      newFavorites = [...favorites, projectId];
    }

    setFavorites(newFavorites);
    localStorage.setItem('favoriteProjects', JSON.stringify(newFavorites));
  };

  // 过滤项目
  const filteredProjects = projects.filter(project =>
    project.name.toLowerCase().includes(searchText.toLowerCase()) ||
    project.description?.toLowerCase().includes(searchText.toLowerCase())
  );

  // 排序项目：收藏的在前，然后按创建时间倒序
  const sortedProjects = [...filteredProjects].sort((a, b) => {
    // 收藏的项目排在前面
    if (favorites.includes(a.id) && !favorites.includes(b.id)) return -1;
    if (!favorites.includes(a.id) && favorites.includes(b.id)) return 1;

    // 然后按创建时间倒序
    return new Date(b.created_at) - new Date(a.created_at);
  });

  // 获取项目状态标签
  const getStatusTag = (status) => {
    switch (status) {
      case 'active':
        return <Tag color="green">运行中</Tag>;
      case 'inactive':
        return <Tag color="orange">已停用</Tag>;
      case 'archived':
        return <Tag color="gray">已归档</Tag>;
      default:
        return <Tag>{status}</Tag>;
    }
  };

  return (
    <div className="project-selection-container">
      <div className="project-selection-header">
        <Title level={2}>选择项目</Title>
        <Text type="secondary">请选择要进入的项目，或创建新项目</Text>
        {/* 移除预览链接，不再需要 */}
      </div>

      <div className="project-selection-actions">
        <Input
          placeholder="搜索项目..."
          prefix={<SearchOutlined />}
          value={searchText}
          onChange={e => setSearchText(e.target.value)}
          className="search-input"
        />
        <div className="action-buttons">
          {/* 只有租户管理员才能看到创建项目按钮 */}
          {(user?.is_tenant_admin || localStorage.getItem('user_type') === 'tenant') && (
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleCreateProjectSteps}
            >
              创建项目
            </Button>
          )}
        </div>
      </div>

      {loading ? (
        <div className="loading-container">
          <Spin size="large" />
        </div>
      ) : sortedProjects.length > 0 ? (
        <Row gutter={[16, 16]} className="projects-grid">
          {sortedProjects.map(project => (
            <Col xs={24} sm={12} md={8} key={project.id}>
              <Card
                className="project-card"
                onClick={() => handleProjectSelect(project)}
                hoverable
              >
                <div className="project-card-header">
                  <div className="project-card-title">
                    <div className="project-icon">
                      {project.industry_type === 'retail' ? (
                        <AppstoreOutlined />
                      ) : project.industry_type === 'restaurant' ? (
                        <AppstoreOutlined />
                      ) : (
                        <AppstoreOutlined />
                      )}
                    </div>
                    <div>
                      <div className="project-name">{project.name}</div>
                      <div className="project-meta">
                        创建时间: {new Date(project.created_at).toLocaleDateString()}
                      </div>
                    </div>
                  </div>
                  <div
                    className="favorite-icon"
                    onClick={(e) => handleToggleFavorite(e, project.id)}
                  >
                    {favorites.includes(project.id) ? (
                      <StarFilled style={{ color: '#faad14' }} />
                    ) : (
                      <StarOutlined />
                    )}
                  </div>
                </div>

                <div className="project-description">
                  {project.description || '暂无描述'}
                </div>

                <div className="project-footer">
                  <div className="project-stats">
                    <Tooltip title="门店数量">
                      <div className="stat-item">
                        <UserOutlined />
                        <span>{Math.floor(Math.random() * 20)}</span>
                      </div>
                    </Tooltip>
                    <Tooltip title="用户数量">
                      <div className="stat-item">
                        <TeamOutlined />
                        <span>{Math.floor(Math.random() * 50)}</span>
                      </div>
                    </Tooltip>
                  </div>
                  <div className="project-status">
                    {getStatusTag(project.status)}
                  </div>
                </div>
              </Card>
            </Col>
          ))}
        </Row>
      ) : (
        <Empty
          description="暂无项目"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        />
      )}
    </div>
  );
};

export default ProjectSelection;
