import React, { useState, useEffect } from 'react';
import { Card, Tabs, Typography, <PERSON>ert, Button, Space, Input, Select, Collapse, Spin, message, Divider, Table, Tag, Tooltip, Switch } from 'antd';
import { CopyOutlined, SettingOutlined, CodeOutlined, ApiOutlined, CheckCircleOutlined, SyncOutlined } from '@ant-design/icons';
import SyntaxHighlighter from 'react-syntax-highlighter';
import { vs2015 } from 'react-syntax-highlighter/dist/esm/styles/hljs';
import { getClientConfig, getAvailableServers } from '../services/mcpService';

const { Title, Paragraph, Text } = Typography;
const { TabPane } = Tabs;
const { Panel } = Collapse;
const { Option } = Select;

/**
 * MCP客户端配置组件
 */
const ClientConfig = () => {
  const [loading, setLoading] = useState(false);
  const [servers, setServers] = useState([]);
  const [selectedServer, setSelectedServer] = useState(null);
  const [clientConfig, setClientConfig] = useState(null);
  const [configType, setConfigType] = useState('python');
  const [transportType, setTransportType] = useState('streamhttp');
  const [useToken, setUseToken] = useState(true);
  const [token, setToken] = useState('');

  // 加载服务器列表
  useEffect(() => {
    const fetchServers = async () => {
      setLoading(true);
      try {
        const response = await getAvailableServers();
        if (response.success) {
          setServers(response.data);
          if (response.data.length > 0) {
            setSelectedServer(response.data[0].id);
          }
        } else {
          message.error('获取服务器列表失败');
        }
      } catch (error) {
        console.error('获取服务器列表失败:', error);
        message.error('获取服务器列表失败');
      } finally {
        setLoading(false);
      }
    };

    fetchServers();
  }, []);

  // 加载客户端配置
  useEffect(() => {
    if (!selectedServer) return;

    const fetchClientConfig = async () => {
      setLoading(true);
      try {
        const response = await getClientConfig(selectedServer, {
          config_type: configType,
          transport_type: transportType,
          use_token: useToken,
          token: token
        });
        
        if (response.success) {
          setClientConfig(response.data);
        } else {
          message.error('获取客户端配置失败');
        }
      } catch (error) {
        console.error('获取客户端配置失败:', error);
        message.error('获取客户端配置失败');
      } finally {
        setLoading(false);
      }
    };

    fetchClientConfig();
  }, [selectedServer, configType, transportType, useToken, token]);

  // 复制代码到剪贴板
  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text).then(
      () => {
        message.success('已复制到剪贴板');
      },
      () => {
        message.error('复制失败');
      }
    );
  };

  // 渲染Python配置示例
  const renderPythonConfig = () => {
    if (!clientConfig || !clientConfig.python) return null;

    return (
      <div>
        <Paragraph>
          使用Python SDK连接到MCP服务器，您可以使用以下代码：
        </Paragraph>
        
        <div style={{ position: 'relative' }}>
          <SyntaxHighlighter language="python" style={vs2015}>
            {clientConfig.python.installation || '# 安装依赖\npip install mcp-client'}
          </SyntaxHighlighter>
          <Button 
            icon={<CopyOutlined />} 
            size="small" 
            style={{ position: 'absolute', top: 10, right: 10 }}
            onClick={() => copyToClipboard(clientConfig.python.installation || '# 安装依赖\npip install mcp-client')}
          />
        </div>

        <Divider dashed />

        <div style={{ position: 'relative' }}>
          <SyntaxHighlighter language="python" style={vs2015}>
            {clientConfig.python.code || '# 示例代码加载中...'}
          </SyntaxHighlighter>
          <Button 
            icon={<CopyOutlined />} 
            size="small" 
            style={{ position: 'absolute', top: 10, right: 10 }}
            onClick={() => copyToClipboard(clientConfig.python.code || '# 示例代码加载中...')}
          />
        </div>
      </div>
    );
  };

  // 渲染JavaScript配置示例
  const renderJavaScriptConfig = () => {
    if (!clientConfig || !clientConfig.javascript) return null;

    return (
      <div>
        <Paragraph>
          使用JavaScript SDK连接到MCP服务器，您可以使用以下代码：
        </Paragraph>
        
        <div style={{ position: 'relative' }}>
          <SyntaxHighlighter language="bash" style={vs2015}>
            {clientConfig.javascript.installation || '# 安装依赖\nnpm install mcp-client'}
          </SyntaxHighlighter>
          <Button 
            icon={<CopyOutlined />} 
            size="small" 
            style={{ position: 'absolute', top: 10, right: 10 }}
            onClick={() => copyToClipboard(clientConfig.javascript.installation || '# 安装依赖\nnpm install mcp-client')}
          />
        </div>

        <Divider dashed />

        <div style={{ position: 'relative' }}>
          <SyntaxHighlighter language="javascript" style={vs2015}>
            {clientConfig.javascript.code || '// 示例代码加载中...'}
          </SyntaxHighlighter>
          <Button 
            icon={<CopyOutlined />} 
            size="small" 
            style={{ position: 'absolute', top: 10, right: 10 }}
            onClick={() => copyToClipboard(clientConfig.javascript.code || '// 示例代码加载中...')}
          />
        </div>
      </div>
    );
  };

  // 渲染cURL配置示例
  const renderCurlConfig = () => {
    if (!clientConfig || !clientConfig.curl) return null;

    return (
      <div>
        <Paragraph>
          使用cURL命令行工具连接到MCP服务器，您可以使用以下命令：
        </Paragraph>
        
        <div style={{ position: 'relative' }}>
          <SyntaxHighlighter language="bash" style={vs2015}>
            {clientConfig.curl.code || '# 示例命令加载中...'}
          </SyntaxHighlighter>
          <Button 
            icon={<CopyOutlined />} 
            size="small" 
            style={{ position: 'absolute', top: 10, right: 10 }}
            onClick={() => copyToClipboard(clientConfig.curl.code || '# 示例命令加载中...')}
          />
        </div>
      </div>
    );
  };

  // 渲染服务器信息
  const renderServerInfo = () => {
    if (!selectedServer || !servers.length) return null;

    const server = servers.find(s => s.id === selectedServer);
    if (!server) return null;

    return (
      <div style={{ marginBottom: 16 }}>
        <Alert
          message={
            <Space>
              <span>当前选择的服务器: <Text strong>{server.name}</Text></span>
              <Tag color={server.status === 'running' ? 'green' : 'red'}>
                {server.status === 'running' ? '运行中' : '已停止'}
              </Tag>
              {server.transport_type && (
                <Tag color="blue">{server.transport_type}</Tag>
              )}
              {server.supports_media && (
                <Tag color="purple">支持媒体</Tag>
              )}
            </Space>
          }
          description={server.description}
          type="info"
          showIcon
        />
      </div>
    );
  };

  return (
    <Card>
      <Title level={4}>MCP客户端配置</Title>
      <Paragraph>
        MCP（Model Context Protocol）是一种标准协议，用于连接大语言模型与外部工具和数据源。
        使用以下配置，您可以将您的应用程序连接到MCP服务器，使用系统提供的工具和功能。
      </Paragraph>

      <Spin spinning={loading}>
        <Space direction="vertical" style={{ width: '100%', marginBottom: 16 }}>
          <Card size="small" title="配置选项">
            <Space wrap>
              <div>
                <Text>选择服务器:</Text>
                <Select
                  style={{ width: 200, marginLeft: 8 }}
                  value={selectedServer}
                  onChange={setSelectedServer}
                  placeholder="选择MCP服务器"
                  disabled={loading || !servers.length}
                >
                  {servers.map(server => (
                    <Option key={server.id} value={server.id}>
                      {server.name}
                      {server.status === 'running' ? 
                        <CheckCircleOutlined style={{ color: 'green', marginLeft: 8 }} /> : 
                        <SyncOutlined style={{ color: 'red', marginLeft: 8 }} />
                      }
                    </Option>
                  ))}
                </Select>
              </div>

              <div>
                <Text>配置类型:</Text>
                <Select
                  style={{ width: 150, marginLeft: 8 }}
                  value={configType}
                  onChange={setConfigType}
                  disabled={loading}
                >
                  <Option value="python">Python</Option>
                  <Option value="javascript">JavaScript</Option>
                  <Option value="curl">cURL</Option>
                </Select>
              </div>

              <div>
                <Text>通信方式:</Text>
                <Select
                  style={{ width: 150, marginLeft: 8 }}
                  value={transportType}
                  onChange={setTransportType}
                  disabled={loading}
                >
                  <Option value="streamhttp">Stream HTTP</Option>
                  <Option value="sse">SSE</Option>
                  <Option value="stdio">STDIO</Option>
                </Select>
              </div>

              <div>
                <Text>使用令牌:</Text>
                <Switch
                  checked={useToken}
                  onChange={setUseToken}
                  style={{ marginLeft: 8 }}
                  disabled={loading}
                />
              </div>

              {useToken && (
                <div>
                  <Text>令牌:</Text>
                  <Input
                    style={{ width: 200, marginLeft: 8 }}
                    value={token}
                    onChange={e => setToken(e.target.value)}
                    placeholder="输入API令牌"
                    disabled={loading}
                  />
                </div>
              )}
            </Space>
          </Card>

          {renderServerInfo()}

          <Tabs defaultActiveKey="1">
            <TabPane 
              tab={<span><CodeOutlined /> 客户端代码</span>} 
              key="1"
            >
              {configType === 'python' && renderPythonConfig()}
              {configType === 'javascript' && renderJavaScriptConfig()}
              {configType === 'curl' && renderCurlConfig()}
            </TabPane>
            
            <TabPane 
              tab={<span><ApiOutlined /> 可用工具</span>} 
              key="2"
            >
              {clientConfig && clientConfig.tools && (
                <Table
                  dataSource={clientConfig.tools}
                  rowKey="name"
                  pagination={false}
                  columns={[
                    {
                      title: '工具名称',
                      dataIndex: 'name',
                      key: 'name',
                      render: text => <Text strong>{text}</Text>
                    },
                    {
                      title: '描述',
                      dataIndex: 'description',
                      key: 'description',
                    },
                    {
                      title: '参数',
                      dataIndex: 'parameters',
                      key: 'parameters',
                      render: parameters => {
                        if (!parameters || !parameters.properties) return '-';
                        
                        const paramNames = Object.keys(parameters.properties);
                        return (
                          <Tooltip 
                            title={
                              <ul style={{ margin: 0, paddingLeft: 16 }}>
                                {paramNames.map(name => (
                                  <li key={name}>
                                    <Text strong>{name}</Text>
                                    {parameters.required && parameters.required.includes(name) && (
                                      <Tag color="red" style={{ marginLeft: 4 }}>必填</Tag>
                                    )}
                                    <div>{parameters.properties[name].description}</div>
                                  </li>
                                ))}
                              </ul>
                            }
                          >
                            <div>{paramNames.length} 个参数</div>
                          </Tooltip>
                        );
                      }
                    }
                  ]}
                />
              )}
            </TabPane>
            
            <TabPane 
              tab={<span><SettingOutlined /> 高级配置</span>} 
              key="3"
            >
              <Collapse>
                <Panel header="连接设置" key="1">
                  {clientConfig && clientConfig.connection && (
                    <div>
                      <Paragraph>
                        <Text strong>服务器URL:</Text> {clientConfig.connection.url}
                      </Paragraph>
                      <Paragraph>
                        <Text strong>超时设置:</Text> {clientConfig.connection.timeout}ms
                      </Paragraph>
                      <Paragraph>
                        <Text strong>重试策略:</Text> {clientConfig.connection.retry_strategy}
                      </Paragraph>
                    </div>
                  )}
                </Panel>
                <Panel header="认证设置" key="2">
                  {clientConfig && clientConfig.auth && (
                    <div>
                      <Paragraph>
                        <Text strong>认证类型:</Text> {clientConfig.auth.type}
                      </Paragraph>
                      <Paragraph>
                        <Text strong>认证头:</Text> {clientConfig.auth.header}
                      </Paragraph>
                    </div>
                  )}
                </Panel>
                <Panel header="调试设置" key="3">
                  {clientConfig && clientConfig.debug && (
                    <div>
                      <Paragraph>
                        <Text strong>日志级别:</Text> {clientConfig.debug.log_level}
                      </Paragraph>
                      <Paragraph>
                        <Text strong>调试模式:</Text> {clientConfig.debug.debug_mode ? '开启' : '关闭'}
                      </Paragraph>
                    </div>
                  )}
                </Panel>
              </Collapse>
            </TabPane>
          </Tabs>
        </Space>
      </Spin>
    </Card>
  );
};

export default ClientConfig;
