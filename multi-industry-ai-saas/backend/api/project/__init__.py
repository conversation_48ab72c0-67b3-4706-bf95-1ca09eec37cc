import logging
from fastapi import APIRouter
from .user_profile import router as user_profile_router
from .system_config import router as system_config_router
from .init import router as init_router
from .business_settings import router as business_settings_router
from .notification_settings import router as notification_settings_router
from .space_settings import router as space_settings_router
from .project_settings import router as project_settings_router
from .workflow import router as workflow_router
from .ai_providers import router as ai_providers_router
from .ai_models import router as ai_models_router
from .ai_configs import router as ai_configs_router
from .ai_usage import router as ai_usage_router
from .ai_knowledge import router as ai_knowledge_router
from .ai_embedding import router as ai_embedding_router
from .ai_assistants import router as ai_assistants_router
from .ai_tools import router as ai_tools_router
from .ai_mcp_servers import router as ai_mcp_servers_router
from .mcp_tools import router as mcp_tools_router
from .monthly_inventory import router as monthly_inventory_router
from .ai_system_integration import router as ai_system_integration_router

# 导入v1项目空间路由
from ..v1.project.space import router as space_router

# 导入插件路由
from plugins import router as plugins_router

router = APIRouter()

router.include_router(user_profile_router, prefix="/{project_id}/users", tags=["用户管理"])
router.include_router(system_config_router, prefix="/{project_id}/settings", tags=["project-settings"])
router.include_router(init_router, prefix="", tags=["project"])
router.include_router(business_settings_router, prefix="/{project_id}/business-settings", tags=["project-business-settings"])
router.include_router(notification_settings_router, prefix="/{project_id}/notification-settings", tags=["project-notification-settings"])
router.include_router(space_settings_router, prefix="/{project_id}/space-settings", tags=["project-space-settings"])
router.include_router(project_settings_router, prefix="/{project_id}/project-settings", tags=["project-project-settings"])
router.include_router(workflow_router, prefix="/{project_id}/workflows",tags=["工作流"])

# 项目空间路由
router.include_router(space_router, prefix="/{project_id}/space", tags=["project-space"])

# AI 模块路由
router.include_router(ai_providers_router, prefix="/{project_id}", tags=["project-ai-providers"])
router.include_router(ai_models_router, prefix="/{project_id}", tags=["project-ai-models"])
router.include_router(ai_configs_router, prefix="/{project_id}", tags=["project-ai-configs"])
router.include_router(ai_usage_router, prefix="/{project_id}", tags=["project-ai-usage"])
router.include_router(ai_knowledge_router, prefix="/{project_id}", tags=["project-ai-knowledge"])
router.include_router(ai_embedding_router, prefix="/{project_id}", tags=["project-ai-embedding"])
router.include_router(ai_assistants_router, prefix="/{project_id}", tags=["project-ai-assistants"])
router.include_router(ai_tools_router, prefix="/{project_id}", tags=["project-ai-tools"])
router.include_router(ai_mcp_servers_router, prefix="/{project_id}", tags=["project-ai-mcp-servers"])
router.include_router(mcp_tools_router, prefix="/{project_id}", tags=["project-mcp-tools"])
router.include_router(ai_system_integration_router, prefix="/{project_id}", tags=["project-ai-system-integration"])


# 月度盘点路由
router.include_router(monthly_inventory_router, prefix="/{project_id}/inventory", tags=["月度盘点"])

# 财务盘点审核路由
router.include_router(monthly_inventory_router, prefix="/{project_id}/finance", tags=["财务盘点审核"])

# 插件路由 - 统一使用单数形式
router.include_router(plugins_router, prefix="/{project_id}/plugin", tags=["插件"])

