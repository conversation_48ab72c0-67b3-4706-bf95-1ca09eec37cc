import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  Space,
  Typography,
  Descriptions,
  Table,
  Tag,
  Divider,
  message,
  Spin,
  Empty,
  Modal,
  Tooltip
} from 'antd';
import {
  ArrowLeftOutlined,
  EditOutlined,
  NodeIndexOutlined,
  BranchesOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import { useNavigate, useParams } from 'react-router-dom';
import apiService from '../../../../services/api';
import ReactFlow, { 
  Controls, 
  Background, 
  MiniMap,
  MarkerType
} from 'reactflow';
import 'reactflow/dist/style.css';

const { Title, Text, Paragraph } = Typography;

/**
 * 工作流详情组件
 */
const WorkflowDetail = () => {
  const [workflow, setWorkflow] = useState(null);
  const [loading, setLoading] = useState(true);
  const [nodes, setNodes] = useState([]);
  const [edges, setEdges] = useState([]);
  const [flowVisible, setFlowVisible] = useState(false);
  const { id } = useParams();
  const navigate = useNavigate();

  // 工作流类型映射
  const workflowTypeMap = {
    'loss_report': '报损工作流',
    'purchase_order': '采购工作流',
    'inventory_check': '盘点工作流',
    'custom': '自定义工作流'
  };

  // 节点类型映射
  const nodeTypeMap = {
    'start': '开始节点',
    'approval': '审批节点',
    'notification': '通知节点',
    'condition': '条件节点',
    'end': '结束节点'
  };

  // 获取工作流详情
  const fetchWorkflowDetail = async () => {
    setLoading(true);
    try {
      const response = await apiService.project.businessSettings.getWorkflow(id);
      if (response && response.data) {
        setWorkflow(response.data);
        
        // 生成流程图数据
        generateFlowData(response.data);
      } else {
        message.error('获取工作流详情失败');
      }
    } catch (error) {
      console.error('获取工作流详情失败:', error);
      message.error('获取工作流详情失败');
    } finally {
      setLoading(false);
    }
  };

  // 生成流程图数据
  const generateFlowData = (workflow) => {
    if (!workflow || !workflow.nodes || !workflow.transitions) {
      return;
    }
    
    // 生成节点
    const flowNodes = workflow.nodes.map((node, index) => {
      // 计算节点位置
      const x = (index % 3) * 250 + 50;
      const y = Math.floor(index / 3) * 150 + 50;
      
      // 根据节点类型设置样式
      let nodeStyle = {};
      let nodeClass = '';
      
      switch (node.type) {
        case 'start':
          nodeStyle = { background: '#d4f7d4', borderColor: '#13c2c2' };
          nodeClass = 'start-node';
          break;
        case 'approval':
          nodeStyle = { background: '#e6f7ff', borderColor: '#1890ff' };
          nodeClass = 'approval-node';
          break;
        case 'notification':
          nodeStyle = { background: '#fff7e6', borderColor: '#fa8c16' };
          nodeClass = 'notification-node';
          break;
        case 'condition':
          nodeStyle = { background: '#f9f0ff', borderColor: '#722ed1' };
          nodeClass = 'condition-node';
          break;
        case 'end':
          nodeStyle = { background: '#fff1f0', borderColor: '#f5222d' };
          nodeClass = 'end-node';
          break;
        default:
          nodeStyle = { background: '#f0f2f5', borderColor: '#d9d9d9' };
          nodeClass = 'default-node';
      }
      
      return {
        id: node.id,
        type: 'default',
        data: { 
          label: (
            <div>
              <div style={{ fontWeight: 'bold' }}>{node.name}</div>
              <div style={{ fontSize: '12px' }}>{nodeTypeMap[node.type] || node.type}</div>
            </div>
          ) 
        },
        position: { x, y },
        style: {
          ...nodeStyle,
          padding: '10px',
          borderWidth: '2px',
          borderStyle: 'solid',
          borderRadius: '5px',
          width: 180
        },
        className: nodeClass
      };
    });
    
    // 生成边
    const flowEdges = workflow.transitions.map(transition => {
      return {
        id: transition.id,
        source: transition.source_node_id,
        target: transition.target_node_id,
        label: transition.name,
        labelStyle: { fill: '#666', fontWeight: 500 },
        style: { stroke: '#888' },
        markerEnd: {
          type: MarkerType.ArrowClosed,
          width: 20,
          height: 20,
          color: '#888',
        },
        animated: true
      };
    });
    
    setNodes(flowNodes);
    setEdges(flowEdges);
  };

  // 初始化
  useEffect(() => {
    if (id) {
      fetchWorkflowDetail();
    }
  }, [id]);

  // 节点表格列定义
  const nodeColumns = [
    {
      title: '序号',
      dataIndex: 'order',
      key: 'order',
      width: 80
    },
    {
      title: '节点名称',
      dataIndex: 'name',
      key: 'name'
    },
    {
      title: '节点代码',
      dataIndex: 'code',
      key: 'code'
    },
    {
      title: '节点类型',
      dataIndex: 'type',
      key: 'type',
      render: (type) => (
        <Tag color={
          type === 'start' ? 'green' :
          type === 'approval' ? 'blue' :
          type === 'notification' ? 'orange' :
          type === 'condition' ? 'purple' :
          type === 'end' ? 'red' : 'default'
        }>
          {nodeTypeMap[type] || type}
        </Tag>
      )
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true
    },
    {
      title: '通知配置',
      key: 'notify',
      render: (_, record) => {
        if (record.type === 'approval' || record.type === 'notification' || record.type === 'end') {
          const notify = record.config?.notify;
          if (notify && notify.enabled) {
            return (
              <Tooltip title={`通知角色: ${notify.roles?.join(', ')}`}>
                <Tag color="green">已启用</Tag>
              </Tooltip>
            );
          } else {
            return <Tag color="red">未启用</Tag>;
          }
        }
        return '-';
      }
    }
  ];

  // 转换表格列定义
  const transitionColumns = [
    {
      title: '转换名称',
      dataIndex: 'name',
      key: 'name'
    },
    {
      title: '源节点',
      dataIndex: 'source_node_id',
      key: 'source_node_id',
      render: (sourceNodeId) => {
        const node = workflow?.nodes?.find(node => node.id === sourceNodeId);
        return node ? node.name : sourceNodeId;
      }
    },
    {
      title: '目标节点',
      dataIndex: 'target_node_id',
      key: 'target_node_id',
      render: (targetNodeId) => {
        const node = workflow?.nodes?.find(node => node.id === targetNodeId);
        return node ? node.name : targetNodeId;
      }
    },
    {
      title: '条件',
      dataIndex: 'condition',
      key: 'condition',
      render: (condition) => {
        if (!condition) {
          return <Tag color="green">无条件</Tag>;
        }
        return (
          <Tooltip title={JSON.stringify(condition, null, 2)}>
            <Tag color="blue">有条件</Tag>
          </Tooltip>
        );
      }
    }
  ];

  // 显示流程图
  const showFlowChart = () => {
    setFlowVisible(true);
  };

  if (loading) {
    return (
      <Card>
        <div style={{ textAlign: 'center', padding: '50px 0' }}>
          <Spin size="large" />
          <p style={{ marginTop: 16 }}>加载中...</p>
        </div>
      </Card>
    );
  }

  if (!workflow) {
    return (
      <Card>
        <Empty description="未找到工作流" />
        <div style={{ textAlign: 'center', marginTop: 16 }}>
          <Button type="primary" onClick={() => navigate('/project/settings/business')}>
            返回
          </Button>
        </div>
      </Card>
    );
  }

  return (
    <Card
      title={
        <Space>
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={() => navigate('/project/settings/business')}
          >
            返回
          </Button>
          <span>工作流详情</span>
        </Space>
      }
      extra={
        <Space>
          <Button
            icon={<NodeIndexOutlined />}
            onClick={showFlowChart}
          >
            查看流程图
          </Button>
          <Button
            type="primary"
            icon={<EditOutlined />}
            onClick={() => navigate(`/project/settings/workflow/edit/${id}`)}
          >
            编辑工作流
          </Button>
        </Space>
      }
    >
      <Descriptions title="基本信息" bordered>
        <Descriptions.Item label="工作流名称">{workflow.name}</Descriptions.Item>
        <Descriptions.Item label="工作流代码">{workflow.code}</Descriptions.Item>
        <Descriptions.Item label="工作流类型">{workflowTypeMap[workflow.type] || workflow.type}</Descriptions.Item>
        <Descriptions.Item label="默认工作流">{workflow.is_default ? '是' : '否'}</Descriptions.Item>
        <Descriptions.Item label="启用状态">{workflow.is_active ? '启用' : '禁用'}</Descriptions.Item>
        <Descriptions.Item label="创建时间">{new Date(workflow.created_at).toLocaleString()}</Descriptions.Item>
        <Descriptions.Item label="描述" span={3}>{workflow.description || '-'}</Descriptions.Item>
      </Descriptions>

      <Divider orientation="left">通知配置</Divider>
      <Descriptions bordered>
        <Descriptions.Item label="启用通知">{workflow.config?.notify_enabled ? '是' : '否'}</Descriptions.Item>
        <Descriptions.Item label="通知角色">{workflow.config?.notify_roles?.join(', ') || '-'}</Descriptions.Item>
        <Descriptions.Item label="通知模板">{workflow.config?.notify_template || '-'}</Descriptions.Item>
      </Descriptions>

      <Divider orientation="left">节点配置</Divider>
      <Table
        columns={nodeColumns}
        dataSource={workflow.nodes}
        rowKey="id"
        pagination={false}
      />

      <Divider orientation="left">转换配置</Divider>
      <Table
        columns={transitionColumns}
        dataSource={workflow.transitions}
        rowKey="id"
        pagination={false}
      />

      <Modal
        title="工作流流程图"
        open={flowVisible}
        onCancel={() => setFlowVisible(false)}
        width={1000}
        footer={null}
        bodyStyle={{ height: 600 }}
      >
        <div style={{ width: '100%', height: '100%' }}>
          <ReactFlow
            nodes={nodes}
            edges={edges}
            fitView
          >
            <Controls />
            <MiniMap />
            <Background variant="dots" gap={12} size={1} />
          </ReactFlow>
        </div>
      </Modal>
    </Card>
  );
};

export default WorkflowDetail;
