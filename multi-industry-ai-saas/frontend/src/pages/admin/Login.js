import React, { useState } from 'react';
import { Form, Input, Button, Checkbox, Card, Typography, message, Divider, Alert } from 'antd';
import { UserOutlined, LockOutlined, SafetyCertificateOutlined } from '@ant-design/icons';
import { Link } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import './Login.css';

const { Title, Text } = Typography;

const AdminLogin = () => {
  const { login, loading } = useAuth();
  const [form] = Form.useForm();
  const [error, setError] = useState('');

  // 处理登录
  const handleLogin = async (values) => {
    try {
      setError('');
      console.log('超级管理员登录尝试:', values.username, values.password);
      await login(values.username, values.password);
    } catch (error) {
      console.error('登录失败:', error);
      setError('登录失败，请检查用户名和密码');
    }
  };

  return (
    <div className="admin-login-container">
      <Card className="admin-login-card">
        <div className="login-header">
          <Title level={2} className="login-title">超级管理员登录</Title>
          <Text type="secondary">多行业AI SaaS系统 - 超级管理控制台</Text>
        </div>

        {error && (
          <Alert
            message={error}
            type="error"
            showIcon
            style={{ marginBottom: 24 }}
          />
        )}

        <Form
          form={form}
          name="admin_login"
          initialValues={{ remember: true }}
          onFinish={handleLogin}
          size="large"
          layout="vertical"
        >
          <Form.Item
            name="username"
            label="管理员用户名"
            rules={[{ required: true, message: '请输入管理员用户名' }]}
          >
            <Input prefix={<UserOutlined />} placeholder="请输入管理员用户名" />
          </Form.Item>

          <Form.Item
            name="password"
            label="密码"
            rules={[{ required: true, message: '请输入密码' }]}
          >
            <Input.Password prefix={<LockOutlined />} placeholder="请输入密码" />
          </Form.Item>

          <Form.Item>
            <div className="login-options">
              <Form.Item name="remember" valuePropName="checked" noStyle>
                <Checkbox>记住我</Checkbox>
              </Form.Item>
              <Link to="/forgot-password">忘记密码</Link>
            </div>
          </Form.Item>

          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              className="login-button"
              icon={<SafetyCertificateOutlined />}
              block
            >
              超级管理员登录
            </Button>
          </Form.Item>
        </Form>

        <Divider />

        <div className="login-footer">
          <Text type="secondary">超级管理员登录入口仅限系统管理人员使用</Text>
          <div style={{ marginTop: 8 }}>
            <Link to="/login">普通用户登录</Link>
            <Divider type="vertical" />
            <Link to="/service-provider/login">服务商登录</Link>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default AdminLogin;
