#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
WebSocket API模块
"""

from fastapi import APIRouter

from .notification import router as notification_router

# 创建WebSocket路由器
router = APIRouter()

# 注册WebSocket路由 - 直接包含，不添加前缀
router.include_router(notification_router)

# 打印路由信息
import logging
import inspect
logger = logging.getLogger(__name__)
logger.info("==== WebSocket Routes in ws/__init__.py ====")
for route in router.routes:
    # 检查路由类型，不依赖于特定的类名
    if hasattr(route, 'path') and 'websocket' in str(route.__class__).lower():
        logger.info(f"WebSocket Route: {route.path}")
logger.info("==========================================")
