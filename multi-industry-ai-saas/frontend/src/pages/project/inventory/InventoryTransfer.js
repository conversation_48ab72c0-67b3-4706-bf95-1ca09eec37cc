import React, { useState, useEffect } from 'react';
import {
  Card, Tabs, Button, Table, Form, Input, Select, InputNumber,
  DatePicker, Space, Upload, message, Modal, Spin, Row, Col, Divider
} from 'antd';
import {
  PlusOutlined, UploadOutlined, DownloadOutlined,
  InboxOutlined, DeleteOutlined, SearchOutlined
} from '@ant-design/icons';
import dayjs from 'dayjs';
import apiService from '../../../services/api';
import { getProjectId } from '../../../services/api/httpClient';

const { TabPane } = Tabs;
const { Option } = Select;
const { Dragger } = Upload;

/**
 * 库存调拨组件
 */
const InventoryTransfer = () => {
  // 状态
  const [activeTab, setActiveTab] = useState('1');
  const [loading, setLoading] = useState(false);
  const [warehouses, setWarehouses] = useState([]);
  const [stores, setStores] = useState([]);
  const [products, setProducts] = useState([]);
  const [transferList, setTransferList] = useState([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });
  const [modalVisible, setModalVisible] = useState(false);
  const [transferType, setTransferType] = useState('store'); // store: 门店配货, third_party: 三方调拨
  const [transferForm] = Form.useForm();
  const [fileList, setFileList] = useState([]);
  const [uploading, setUploading] = useState(false);
  const [selectedSourceWarehouse, setSelectedSourceWarehouse] = useState(null);
  const [selectedDestination, setSelectedDestination] = useState(null);
  const [transferItems, setTransferItems] = useState([]);
  const [searchForm] = Form.useForm();

  // 获取仓库列表
  const fetchWarehouses = async () => {
    try {
      setLoading(true);
      const response = await apiService.project.warehouse.getList();
      if (response && response.items) {
        setWarehouses(response.items);
      }
    } catch (error) {
      console.error('获取仓库列表失败:', error);
      message.error('获取仓库列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取门店列表
  const fetchStores = async () => {
    try {
      setLoading(true);
      const response = await apiService.project.store.getList();
      if (response && response.items) {
        setStores(response.items);
      }
    } catch (error) {
      console.error('获取门店列表失败:', error);
      message.error('获取门店列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取产品列表
  const fetchProducts = async () => {
    try {
      setLoading(true);
      const response = await apiService.project.product.getList();
      if (response && response.items) {
        setProducts(response.items);
      }
    } catch (error) {
      console.error('获取产品列表失败:', error);
      message.error('获取产品列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取调拨记录列表
  const fetchTransferList = async (params = {}) => {
    try {
      setLoading(true);
      const response = await apiService.project.inventory.getTransferList(params);
      if (response && response.items) {
        setTransferList(response.items);
        setPagination({
          ...pagination,
          current: params.page || pagination.current,
          total: response.total || 0
        });
      }
    } catch (error) {
      console.error('获取调拨记录列表失败:', error);
      message.error('获取调拨记录列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 初始化
  useEffect(() => {
    fetchWarehouses();
    fetchStores();
    fetchProducts();
    fetchTransferList();
  }, []);

  // 处理表格变化
  const handleTableChange = (pagination, filters, sorter) => {
    fetchTransferList({
      page: pagination.current,
      pageSize: pagination.pageSize,
      ...filters
    });
  };

  // 处理搜索
  const handleSearch = (values) => {
    fetchTransferList({
      ...values,
      page: 1
    });
  };

  // 处理重置搜索
  const handleResetSearch = () => {
    searchForm.resetFields();
    fetchTransferList({
      page: 1
    });
  };

  // 处理添加调拨项
  const handleAddItem = () => {
    setTransferItems([
      ...transferItems,
      {
        key: Date.now(),
        product_id: null,
        quantity: 1,
        unit: '',
        notes: ''
      }
    ]);
  };

  // 处理删除调拨项
  const handleRemoveItem = (key) => {
    setTransferItems(transferItems.filter(item => item.key !== key));
  };

  // 处理调拨项变化
  const handleItemChange = (key, field, value) => {
    setTransferItems(transferItems.map(item => {
      if (item.key === key) {
        if (field === 'product_id' && value) {
          // 查找产品信息
          const product = products.find(p => p.id === value);
          return {
            ...item,
            [field]: value,
            unit: product ? product.unit : ''
          };
        }
        return { ...item, [field]: value };
      }
      return item;
    }));
  };

  // 处理创建调拨
  const handleCreateTransfer = async () => {
    try {
      const values = await transferForm.validateFields();

      if (transferItems.length === 0) {
        message.warning('请添加至少一个调拨商品');
        return;
      }

      setLoading(true);

      // 准备调拨数据
      const transferData = {
        ...values,
        transfer_type: transferType,
        source_warehouse_id: selectedSourceWarehouse,
        destination_id: selectedDestination,
        transfer_date: values.transfer_date.format('YYYY-MM-DD'),
        items: transferItems.map(item => ({
          product_id: item.product_id,
          quantity: item.quantity,
          notes: item.notes
        }))
      };

      // 调用API创建调拨
      const response = await apiService.project.inventory.createTransfer(transferData);

      if (response && response.success) {
        message.success('创建调拨成功');
        setModalVisible(false);
        resetTransferForm();
        fetchTransferList();
      } else {
        message.error(response?.message || '创建调拨失败');
      }
    } catch (error) {
      console.error('创建调拨失败:', error);
      message.error('创建调拨失败');
    } finally {
      setLoading(false);
    }
  };

  // 重置调拨表单
  const resetTransferForm = () => {
    transferForm.resetFields();
    setTransferItems([]);
    setSelectedSourceWarehouse(null);
    setSelectedDestination(null);
    setTransferType('store');
  };

  // 处理文件上传
  const handleUpload = async () => {
    if (fileList.length === 0) {
      message.warning('请选择要上传的文件');
      return;
    }

    if (!selectedSourceWarehouse) {
      message.warning('请选择源仓库');
      return;
    }

    if (!selectedDestination) {
      message.warning(`请选择${transferType === 'store' ? '目标门店' : '调拨目标'}`);
      return;
    }

    setUploading(true);

    try {
      const formData = new FormData();
      formData.append('file', fileList[0]);
      formData.append('source_warehouse_id', selectedSourceWarehouse);
      formData.append('destination_id', selectedDestination);
      formData.append('transfer_type', transferType);

      // 调用API上传文件
      const response = await apiService.project.inventory.uploadTransferSheet(formData);

      if (response && response.success) {
        message.success('上传成功');
        setFileList([]);
        fetchTransferList();
      } else {
        message.error(response?.message || '上传失败');
      }
    } catch (error) {
      console.error('上传失败:', error);
      message.error('上传失败');
    } finally {
      setUploading(false);
    }
  };

  // 下载模板
  const handleDownloadTemplate = () => {
    const projectId = getProjectId();
    if (!projectId) {
      message.error('未找到项目ID');
      return;
    }

    window.open(`/api/v1/project/${projectId}/inventories/transfer-template?type=${transferType}`, '_blank');
  };

  // 调拨记录表格列
  const transferColumns = [
    {
      title: '调拨单号',
      dataIndex: 'transfer_number',
      key: 'transfer_number',
    },
    {
      title: '调拨类型',
      dataIndex: 'transfer_type',
      key: 'transfer_type',
      render: (text) => text === 'store' ? '门店配货' : '三方调拨'
    },
    {
      title: '源仓库',
      dataIndex: 'source_warehouse_name',
      key: 'source_warehouse_name',
    },
    {
      title: '目标',
      dataIndex: 'destination_name',
      key: 'destination_name',
    },
    {
      title: '调拨日期',
      dataIndex: 'transfer_date',
      key: 'transfer_date',
    },
    {
      title: '商品数量',
      dataIndex: 'total_items',
      key: 'total_items',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (text) => {
        const statusMap = {
          pending: '待处理',
          processing: '处理中',
          completed: '已完成',
          cancelled: '已取消'
        };
        return statusMap[text] || text;
      }
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="small">
          <Button type="link" size="small" onClick={() => viewTransferDetail(record)}>
            详情
          </Button>
        </Space>
      ),
    },
  ];

  // 查看调拨详情
  const viewTransferDetail = (record) => {
    // 实现查看详情功能
    console.log('查看详情:', record);
  };

  // 渲染门店配货Tab
  const renderStoreTransferTab = () => {
    return (
      <div>
        <Row gutter={16}>
          <Col span={8}>
            <Form.Item label="源仓库">
              <Select
                placeholder="请选择源仓库"
                value={selectedSourceWarehouse}
                onChange={setSelectedSourceWarehouse}
                style={{ width: '100%' }}
              >
                {warehouses.map(warehouse => (
                  <Option key={warehouse.id} value={warehouse.id}>{warehouse.name}</Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="目标门店">
              <Select
                placeholder="请选择目标门店"
                value={selectedDestination}
                onChange={setSelectedDestination}
                style={{ width: '100%' }}
              >
                {stores.map(store => (
                  <Option key={store.id} value={store.id}>{store.name}</Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label=" " colon={false}>
              <Space>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => {
                    setTransferType('store');
                    setModalVisible(true);
                  }}
                >
                  新建调拨
                </Button>
              </Space>
            </Form.Item>
          </Col>
        </Row>

        <Divider />

        <Card title="上传调拨表">
          <Dragger
            fileList={fileList}
            beforeUpload={(file) => {
              setFileList([file]);
              return false;
            }}
            onRemove={() => {
              setFileList([]);
            }}
            multiple={false}
            accept=".xlsx,.xls,.csv"
          >
            <p className="ant-upload-drag-icon">
              <InboxOutlined />
            </p>
            <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
            <p className="ant-upload-hint">
              支持 .xlsx, .xls, .csv 格式的调拨表文件
            </p>
          </Dragger>

          <div style={{ marginTop: 16, textAlign: 'center' }}>
            <Space>
              <Button
                type="primary"
                onClick={handleUpload}
                disabled={fileList.length === 0 || !selectedSourceWarehouse || !selectedDestination}
                loading={uploading}
              >
                上传文件
              </Button>
              <Button onClick={handleDownloadTemplate}>
                下载模板
              </Button>
            </Space>
          </div>
        </Card>
      </div>
    );
  };

  // 渲染仓库间调拨Tab
  const renderWarehouseTransferTab = () => {
    return (
      <div>
        <Row gutter={16}>
          <Col span={8}>
            <Form.Item label="源仓库">
              <Select
                placeholder="请选择源仓库"
                value={selectedSourceWarehouse}
                onChange={setSelectedSourceWarehouse}
                style={{ width: '100%' }}
              >
                {warehouses.map(warehouse => (
                  <Option key={warehouse.id} value={warehouse.id}>{warehouse.name}</Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="目标仓库">
              <Select
                placeholder="请选择目标仓库"
                value={selectedDestination}
                onChange={setSelectedDestination}
                style={{ width: '100%' }}
              >
                {warehouses.map(warehouse => (
                  <Option key={warehouse.id} value={warehouse.id}>{warehouse.name}</Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label=" " colon={false}>
              <Space>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => {
                    setTransferType('warehouse');
                    setModalVisible(true);
                  }}
                >
                  新建调拨
                </Button>
              </Space>
            </Form.Item>
          </Col>
        </Row>

        <Divider />

        <Card title="上传调拨表">
          <Dragger
            fileList={fileList}
            beforeUpload={(file) => {
              setFileList([file]);
              return false;
            }}
            onRemove={() => {
              setFileList([]);
            }}
            multiple={false}
            accept=".xlsx,.xls,.csv"
          >
            <p className="ant-upload-drag-icon">
              <InboxOutlined />
            </p>
            <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
            <p className="ant-upload-hint">
              支持 .xlsx, .xls, .csv 格式的调拨表文件
            </p>
          </Dragger>

          <div style={{ marginTop: 16, textAlign: 'center' }}>
            <Space>
              <Button
                type="primary"
                onClick={handleUpload}
                disabled={fileList.length === 0 || !selectedSourceWarehouse || !selectedDestination}
                loading={uploading}
              >
                上传文件
              </Button>
              <Button onClick={handleDownloadTemplate}>
                下载模板
              </Button>
            </Space>
          </div>
        </Card>
      </div>
    );
  };

  // 渲染三方调拨Tab
  const renderThirdPartyTransferTab = () => {
    return (
      <div>
        <Row gutter={16}>
          <Col span={8}>
            <Form.Item label="源仓库">
              <Select
                placeholder="请选择源仓库"
                value={selectedSourceWarehouse}
                onChange={setSelectedSourceWarehouse}
                style={{ width: '100%' }}
              >
                {warehouses.map(warehouse => (
                  <Option key={warehouse.id} value={warehouse.id}>{warehouse.name}</Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label="调拨目标">
              <Input
                placeholder="请输入调拨目标名称"
                value={selectedDestination}
                onChange={(e) => setSelectedDestination(e.target.value)}
                style={{ width: '100%' }}
              />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item label=" " colon={false}>
              <Space>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => {
                    setTransferType('third_party');
                    setModalVisible(true);
                  }}
                >
                  新建调拨
                </Button>
              </Space>
            </Form.Item>
          </Col>
        </Row>

        <Divider />

        <Card title="上传调拨表">
          <Dragger
            fileList={fileList}
            beforeUpload={(file) => {
              setFileList([file]);
              return false;
            }}
            onRemove={() => {
              setFileList([]);
            }}
            multiple={false}
            accept=".xlsx,.xls,.csv"
          >
            <p className="ant-upload-drag-icon">
              <InboxOutlined />
            </p>
            <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
            <p className="ant-upload-hint">
              支持 .xlsx, .xls, .csv 格式的调拨表文件
            </p>
          </Dragger>

          <div style={{ marginTop: 16, textAlign: 'center' }}>
            <Space>
              <Button
                type="primary"
                onClick={handleUpload}
                disabled={fileList.length === 0 || !selectedSourceWarehouse || !selectedDestination}
                loading={uploading}
              >
                上传文件
              </Button>
              <Button onClick={handleDownloadTemplate}>
                下载模板
              </Button>
            </Space>
          </div>
        </Card>
      </div>
    );
  };

  return (
    <div className="inventory-transfer">
      <Card title="库存调拨">
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab="调拨记录" key="1">
            <div style={{ marginBottom: 16 }}>
              <Form
                form={searchForm}
                layout="inline"
                onFinish={handleSearch}
              >
                <Form.Item name="transfer_type" label="调拨类型">
                  <Select style={{ width: 120 }} allowClear>
                    <Option value="store">门店配货</Option>
                    <Option value="third_party">三方调拨</Option>
                    <Option value="warehouse">仓库间调拨</Option>
                  </Select>
                </Form.Item>
                <Form.Item name="status" label="状态">
                  <Select style={{ width: 120 }} allowClear>
                    <Option value="pending">待处理</Option>
                    <Option value="processing">处理中</Option>
                    <Option value="completed">已完成</Option>
                    <Option value="cancelled">已取消</Option>
                  </Select>
                </Form.Item>
                <Form.Item name="keyword" label="关键词">
                  <Input placeholder="调拨单号/仓库/目标" />
                </Form.Item>
                <Form.Item>
                  <Button type="primary" htmlType="submit" icon={<SearchOutlined />}>
                    搜索
                  </Button>
                </Form.Item>
                <Form.Item>
                  <Button onClick={handleResetSearch}>重置</Button>
                </Form.Item>
              </Form>
            </div>

            <Table
              columns={transferColumns}
              dataSource={transferList}
              rowKey="id"
              pagination={pagination}
              onChange={handleTableChange}
              loading={loading}
            />
          </TabPane>
          <TabPane tab="门店配货" key="2">
            {renderStoreTransferTab()}
          </TabPane>
          <TabPane tab="三方调拨" key="3">
            {renderThirdPartyTransferTab()}
          </TabPane>
          <TabPane tab="仓库间调拨" key="4">
            {renderWarehouseTransferTab()}
          </TabPane>
        </Tabs>
      </Card>

      {/* 创建调拨模态框 */}
      <Modal
        title={`创建${transferType === 'store' ? '门店配货' : '三方调拨'}`}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        width={800}
        footer={[
          <Button key="cancel" onClick={() => setModalVisible(false)}>
            取消
          </Button>,
          <Button key="submit" type="primary" loading={loading} onClick={handleCreateTransfer}>
            确认
          </Button>
        ]}
      >
        <Form
          form={transferForm}
          layout="vertical"
          initialValues={{
            transfer_date: dayjs(),
            payment_method: 'cash'
          }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="source_warehouse_id"
                label="源仓库"
                rules={[{ required: true, message: '请选择源仓库' }]}
              >
                <Select placeholder="请选择源仓库">
                  {warehouses.map(warehouse => (
                    <Option key={warehouse.id} value={warehouse.id}>{warehouse.name}</Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              {transferType === 'store' ? (
                <Form.Item
                  name="destination_id"
                  label="目标门店"
                  rules={[{ required: true, message: '请选择目标门店' }]}
                >
                  <Select placeholder="请选择目标门店">
                    {stores.map(store => (
                      <Option key={store.id} value={store.id}>{store.name}</Option>
                    ))}
                  </Select>
                </Form.Item>
              ) : (
                <Form.Item
                  name="destination_name"
                  label="调拨目标"
                  rules={[{ required: true, message: '请输入调拨目标' }]}
                >
                  <Input placeholder="请输入调拨目标名称" />
                </Form.Item>
              )}
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="transfer_date"
                label="调拨日期"
                rules={[{ required: true, message: '请选择调拨日期' }]}
              >
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            {transferType === 'third_party' && (
              <Col span={12}>
                <Form.Item
                  name="payment_method"
                  label="支付方式"
                  rules={[{ required: true, message: '请选择支付方式' }]}
                >
                  <Select placeholder="请选择支付方式">
                    <Option value="cash">现金支付</Option>
                    <Option value="credit">欠款代收</Option>
                    <Option value="warehouse_collect">仓管代收</Option>
                    <Option value="other">其他方式</Option>
                  </Select>
                </Form.Item>
              </Col>
            )}
          </Row>

          <Form.Item
            name="notes"
            label="备注"
          >
            <Input.TextArea rows={2} placeholder="请输入备注信息" />
          </Form.Item>

          <Divider orientation="left">调拨商品</Divider>

          {transferItems.map((item, index) => (
            <Row gutter={16} key={item.key} style={{ marginBottom: 16 }}>
              <Col span={8}>
                <Form.Item
                  label={index === 0 ? "商品" : ""}
                  required
                >
                  <Select
                    placeholder="请选择商品"
                    value={item.product_id}
                    onChange={(value) => handleItemChange(item.key, 'product_id', value)}
                    style={{ width: '100%' }}
                  >
                    {products.map(product => (
                      <Option key={product.id} value={product.id}>{product.name}</Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={4}>
                <Form.Item
                  label={index === 0 ? "数量" : ""}
                  required
                >
                  <InputNumber
                    min={1}
                    value={item.quantity}
                    onChange={(value) => handleItemChange(item.key, 'quantity', value)}
                    style={{ width: '100%' }}
                  />
                </Form.Item>
              </Col>
              <Col span={4}>
                <Form.Item
                  label={index === 0 ? "单位" : ""}
                >
                  <Input
                    value={item.unit}
                    disabled
                    style={{ width: '100%' }}
                  />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  label={index === 0 ? "备注" : ""}
                >
                  <Input
                    placeholder="备注"
                    value={item.notes}
                    onChange={(e) => handleItemChange(item.key, 'notes', e.target.value)}
                    style={{ width: '100%' }}
                  />
                </Form.Item>
              </Col>
              <Col span={2}>
                <Form.Item
                  label={index === 0 ? " " : ""}
                  colon={false}
                >
                  <Button
                    type="link"
                    danger
                    icon={<DeleteOutlined />}
                    onClick={() => handleRemoveItem(item.key)}
                  />
                </Form.Item>
              </Col>
            </Row>
          ))}

          <Form.Item>
            <Button type="dashed" onClick={handleAddItem} block icon={<PlusOutlined />}>
              添加商品
            </Button>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default InventoryTransfer;
