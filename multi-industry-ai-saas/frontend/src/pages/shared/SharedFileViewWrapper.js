/**
 * 共享文件查看包装组件
 * 用于处理共享文件的查看逻辑
 */

import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Result, Spin, Button, Typography, Card, Space, Divider } from 'antd';
import { FileOutlined, DownloadOutlined, EyeOutlined } from '@ant-design/icons';
import apiService from '../../services/api';

const { Title, Text, Paragraph } = Typography;

const SharedFileViewWrapper = () => {
  const { shareToken } = useParams();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [fileData, setFileData] = useState(null);

  // 加载共享文件数据
  useEffect(() => {
    const fetchSharedFile = async () => {
      try {
        setLoading(true);
        const data = await apiService.getSharedFile(shareToken);
        setFileData(data);
      } catch (err) {
        console.error('获取共享文件失败:', err);
        setError(err.response?.data?.message || '获取共享文件失败');
      } finally {
        setLoading(false);
      }
    };

    if (shareToken) {
      fetchSharedFile();
    }
  }, [shareToken]);

  // 处理下载文件
  const handleDownload = () => {
    if (fileData?.download_url) {
      window.open(fileData.download_url, '_blank');
    }
  };

  // 处理查看文件
  const handleView = () => {
    if (fileData?.view_url) {
      window.open(fileData.view_url, '_blank');
    }
  };

  // 返回首页
  const goToHome = () => {
    navigate('/');
  };

  // 加载中状态
  if (loading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <Spin size="large" tip="正在加载共享文件..." />
      </div>
    );
  }

  // 错误状态
  if (error) {
    return (
      <Result
        status="error"
        title="无法访问共享文件"
        subTitle={error}
        extra={[
          <Button type="primary" key="home" onClick={goToHome}>
            返回首页
          </Button>,
        ]}
      />
    );
  }

  // 文件不存在或已过期
  if (!fileData) {
    return (
      <Result
        status="404"
        title="共享文件不存在或已过期"
        subTitle="请检查您的链接是否正确，或者联系文件分享者获取新的链接。"
        extra={[
          <Button type="primary" key="home" onClick={goToHome}>
            返回首页
          </Button>,
        ]}
      />
    );
  }

  // 文件存在，显示文件信息
  return (
    <div style={{ 
      display: 'flex', 
      justifyContent: 'center', 
      alignItems: 'center', 
      minHeight: '100vh',
      padding: '20px',
      background: '#f0f2f5'
    }}>
      <Card 
        style={{ 
          width: '100%', 
          maxWidth: 600,
          boxShadow: '0 4px 12px rgba(0,0,0,0.1)'
        }}
        title={
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <FileOutlined style={{ fontSize: 24, marginRight: 12 }} />
            <span>共享文件</span>
          </div>
        }
      >
        <div style={{ textAlign: 'center', marginBottom: 24 }}>
          <Title level={3}>{fileData.name}</Title>
          <Paragraph type="secondary">
            分享者: {fileData.shared_by_name || '未知用户'}
          </Paragraph>
          {fileData.expiry_date && (
            <Text type="warning">
              此链接将于 {new Date(fileData.expiry_date).toLocaleString()} 过期
            </Text>
          )}
        </div>
        
        <Divider />
        
        <div style={{ marginBottom: 24 }}>
          <Paragraph>
            <Text strong>文件类型:</Text> {fileData.mime_type || '未知'}
          </Paragraph>
          <Paragraph>
            <Text strong>文件大小:</Text> {(fileData.size / 1024 / 1024).toFixed(2)} MB
          </Paragraph>
          {fileData.description && (
            <Paragraph>
              <Text strong>描述:</Text> {fileData.description}
            </Paragraph>
          )}
        </div>
        
        <div style={{ display: 'flex', justifyContent: 'center' }}>
          <Space size="middle">
            {fileData.can_download && (
              <Button 
                type="primary" 
                icon={<DownloadOutlined />} 
                onClick={handleDownload}
              >
                下载文件
              </Button>
            )}
            {fileData.can_view && (
              <Button 
                icon={<EyeOutlined />} 
                onClick={handleView}
              >
                查看文件
              </Button>
            )}
          </Space>
        </div>
      </Card>
    </div>
  );
};

export default SharedFileViewWrapper;
