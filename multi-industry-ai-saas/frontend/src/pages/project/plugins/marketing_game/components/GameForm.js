import React, { useState } from 'react';
import {
  Form,
  Input,
  Button,
  DatePicker,
  Select,
  InputNumber,
  Row,
  Col,
  Divider,
  Space,
  Switch,
  Upload,
  Tabs,
  Card,
  Radio,
  ColorPicker,
  message
} from 'antd';
import {
  PictureOutlined
} from '@ant-design/icons';
import dayjs from "dayjs";
import { gameTypeOptions } from '../utils/constants';
import GameTypeSettings from './GameTypeSettings';
import GameSettings from './GameSettings';

const { TextArea } = Input;
const { RangePicker } = DatePicker;
const { Option } = Select;
const { TabPane } = Tabs;

// 工具函数：确保theme_color为字符串
function getHexColor(val) {
  if (!val) return '#e91e63';
  if (typeof val === 'string') return val;
  if (val.metaColor && val.metaColor.isValid && val.metaColor.r !== undefined) {
    const { r, g, b } = val.metaColor;
    return (
      '#' +
      [r, g, b]
        .map(x => {
          const hex = x.toString(16);
          return hex.length === 1 ? '0' + hex : hex;
        })
        .join('')
    );
  }
  return '#e91e63';
}

const GameForm = ({ initialValues, onSubmit, onCancel }) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('basic');
  const [backgroundType, setBackgroundType] = useState('default');
  const [customBackground, setCustomBackground] = useState(null);
  const [enableSound, setEnableSound] = useState(true);

  // 处理背景图片上传
  const handleBackgroundUpload = (info) => {
    if (info.file.status === 'done') {
      setCustomBackground(info.file.response.url);
      form.setFieldsValue({
        settings: {
          ...form.getFieldValue('settings'),
          background_image: info.file.response.url
        }
      });
      message.success(`${info.file.name} 上传成功`);
    } else if (info.file.status === 'error') {
      message.error(`${info.file.name} 上传失败`);
    }
  };

  // 处理表单提交
  const handleSubmit = async () => {
    try {
      setLoading(true);
      const values = await form.validateFields();

      // 处理日期范围
      if (values.date_range) {
        values.start_time = values.date_range[0].toISOString();
        values.end_time = values.date_range[1].toISOString();
        delete values.date_range;
      }

      // 获取当前表单中的所有设置值
      const currentSettings = form.getFieldValue('settings') || {};

      // 处理设置
      values.settings = {
        // 基本设置
        enable_receipt_recognition: values.enable_receipt_recognition,
        show_winners: values.show_winners,
        require_member: values.require_member,

        // 外观设置
        background_type: backgroundType,
        background_image: customBackground || values.settings?.background_image,
        theme_color: getHexColor(values.settings?.theme_color),

        // 游戏设置
        enable_sound: enableSound,
        prize_display_time: values.settings?.prize_display_time || 3,
        animation_speed: values.settings?.animation_speed || 'normal',

        // 验证设置 - 直接使用表单中的值，不使用默认值覆盖
        enable_phone_verification: currentSettings.enable_phone_verification,
        enable_receipt_verification: currentSettings.enable_receipt_verification,
        receipt_verification_type: currentSettings.receipt_verification_type || 'both',

        // 高级设置
        auto_draw: currentSettings.auto_draw,
        show_probability: currentSettings.show_probability !== undefined ? currentSettings.show_probability : true,
        enable_share: currentSettings.enable_share !== undefined ? currentSettings.enable_share : true,

        // 其他设置
        ...values.settings
      };

      console.log("提交的设置:", values.settings);

      // 删除额外字段
      delete values.enable_receipt_recognition;
      delete values.show_winners;
      delete values.require_member;

      await onSubmit(values);
    } catch (error) {
      console.error('表单验证失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 初始化表单值
  React.useEffect(() => {
    if (initialValues) {
      // 处理日期范围
      const dateRange = [
        dayjs(initialValues.start_time),
        dayjs(initialValues.end_time)
      ];

      // 处理设置
      const settings = initialValues.settings || {};

      form.setFieldsValue({
        ...initialValues,
        date_range: dateRange,
        enable_receipt_recognition: settings.enable_receipt_recognition !== false,
        show_winners: settings.show_winners !== false,
        require_member: settings.require_member || false,
        settings: {
          ...settings,
          theme_color: getHexColor(settings.theme_color)
        }
      });
    }
  }, [initialValues, form]);

  return (
    <Form
      form={form}
      layout="vertical"
      initialValues={{
        game_type: 'lucky_draw',
        status: 'draft',
        min_consumption: 0,
        max_draws_per_user: 1,
        enable_receipt_recognition: true,
        show_winners: true,
        require_member: false,
        settings: {
          background_type: 'default',
          theme_color: '#e91e63',
          theme_style: 'classic',
          enable_sound: true,
          prize_display_time: 3,
          animation_speed: 'normal',
          use_enhanced: true,
          auto_draw: false,
          show_probability: true,
          enable_share: true,
          enable_phone_verification: false,
          enable_receipt_verification: false,
          receipt_verification_type: 'both'
        }
      }}
    >
      <Tabs activeKey={activeTab} onChange={setActiveTab} type="card">
        <TabPane tab="基本信息" key="basic">
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="游戏名称"
                rules={[{ required: true, message: '请输入游戏名称' }]}
              >
                <Input placeholder="请输入游戏名称" maxLength={100} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="game_type"
                label="游戏类型"
                rules={[{ required: true, message: '请选择游戏类型' }]}
              >
                <Select placeholder="请选择游戏类型">
                  {gameTypeOptions.map(option => (
                    <Option key={option.value} value={option.value}>
                      {option.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="description"
            label="游戏描述"
          >
            <TextArea
              placeholder="请输入游戏描述"
              autoSize={{ minRows: 2, maxRows: 6 }}
              maxLength={500}
              showCount
            />
          </Form.Item>

          <Form.Item
            name="date_range"
            label="游戏时间"
            rules={[{ required: true, message: '请选择游戏时间范围' }]}
          >
            <RangePicker
              showTime
              format="YYYY-MM-DD HH:mm:ss"
              style={{ width: '100%' }}
              placeholder={['开始时间', '结束时间']}
            />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="min_consumption"
                label="最低消费金额"
                rules={[{ required: true, message: '请输入最低消费金额' }]}
                tooltip="用户需要消费满多少金额才能参与抽奖"
              >
                <InputNumber
                  placeholder="请输入最低消费金额"
                  min={0}
                  precision={2}
                  style={{ width: '100%' }}
                  addonBefore="¥"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="max_draws_per_user"
                label="每用户最大抽奖次数"
                rules={[{ required: true, message: '请输入每用户最大抽奖次数' }]}
                tooltip="每个用户最多可以抽奖的次数"
              >
                <InputNumber
                  placeholder="请输入每用户最大抽奖次数"
                  min={1}
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="rules"
            label="游戏规则"
            tooltip="详细的游戏规则说明，将显示给用户"
          >
            <TextArea
              placeholder="请输入游戏规则"
              autoSize={{ minRows: 3, maxRows: 8 }}
              maxLength={1000}
              showCount
            />
          </Form.Item>
        </TabPane>

        <TabPane tab="外观设置" key="appearance">
          <Card title="背景设置" bordered={false}>
            <Form.Item
              label="背景类型"
              tooltip="选择使用默认背景还是自定义背景"
            >
              <Radio.Group
                value={backgroundType}
                onChange={(e) => setBackgroundType(e.target.value)}
              >
                <Radio.Button value="default">默认背景</Radio.Button>
                <Radio.Button value="custom">自定义背景</Radio.Button>
              </Radio.Group>
            </Form.Item>

            {backgroundType === 'default' ? (
              <Form.Item
                name={['settings', 'background_index']}
                label="选择默认背景"
              >
                <Radio.Group>
                  <Row gutter={[16, 16]}>
                    {[0, 1, 2, 3, 4].map(index => (
                      <Col span={8} key={index}>
                        <Radio.Button value={index} style={{ height: 'auto', padding: 0 }}>
                          <img
                            src={`../assets/images/backgrounds/bg${index + 1}.jpg`}
                            alt={`背景${index + 1}`}
                            style={{ width: '100%', height: '80px', objectFit: 'cover' }}
                            onError={(e) => {
                              console.warn(`背景图片加载失败: bg${index + 1}.jpg`);
                              e.target.src = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII='; // 透明占位图
                            }}
                          />
                        </Radio.Button>
                      </Col>
                    ))}
                  </Row>
                </Radio.Group>
              </Form.Item>
            ) : (
              <Form.Item
                name={['settings', 'background_image']}
                label="上传自定义背景"
                tooltip="建议尺寸: 1920x1080px, 格式: JPG/PNG"
              >
                <Upload
                  name="background"
                  listType="picture-card"
                  showUploadList={true}
                  action="/api/v1/upload/image"
                  onChange={handleBackgroundUpload}
                  maxCount={1}
                >
                  <div>
                    <PictureOutlined />
                    <div style={{ marginTop: 8 }}>上传背景</div>
                  </div>
                </Upload>
              </Form.Item>
            )}
          </Card>

          <Card title="主题设置" bordered={false} style={{ marginTop: 16 }}>
            <Form.Item
              name={['settings', 'theme_style']}
              label="主题风格"
              tooltip="选择游戏的主题风格"
              initialValue="classic"
            >
              <Radio.Group>
                <Radio.Button value="classic">经典风格</Radio.Button>
                <Radio.Button value="cartoon">卡通风格</Radio.Button>
                <Radio.Button value="business">商业风格</Radio.Button>
                <Radio.Button value="apple">苹果风格</Radio.Button>
                <Radio.Button value="ghibli">吉卜力风格</Radio.Button>
              </Radio.Group>
            </Form.Item>

            <Form.Item
              name={['settings', 'theme_color']}
              label="主题颜色"
              tooltip="设置游戏的主题颜色（部分主题风格下可能不生效）"
            >
              <ColorPicker
                value={getHexColor(form.getFieldValue(['settings', 'theme_color']))}
                onChange={(_, hex) => {
                  form.setFieldsValue({
                    settings: {
                      ...form.getFieldValue('settings'),
                      theme_color: hex
                    }
                  });
                }}
              />
            </Form.Item>

            <Form.Item
              name={['settings', 'enable_sound']}
              label="启用音效"
              valuePropName="checked"
              tooltip="是否启用游戏音效"
            >
              <Switch
                checked={enableSound}
                onChange={setEnableSound}
                checkedChildren="开启"
                unCheckedChildren="关闭"
              />
            </Form.Item>
          </Card>
        </TabPane>

        <TabPane tab="游戏设置" key="game">
          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) =>
              prevValues.game_type !== currentValues.game_type
            }
          >
            {({ getFieldValue }) => {
              const gameType = getFieldValue('game_type');
              return <GameTypeSettings gameType={gameType} form={form} initialValues={initialValues} />;
            }}
          </Form.Item>
        </TabPane>

        <TabPane tab="高级设置" key="advanced">
          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) =>
              prevValues.game_type !== currentValues.game_type
            }
          >
            {({ getFieldValue }) => {
              const gameType = getFieldValue('game_type');
              return <GameSettings gameType={gameType} form={form} initialValues={initialValues} />;
            }}
          </Form.Item>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name={['settings', 'use_enhanced']}
                label="增强版界面"
                valuePropName="checked"
                tooltip="使用增强版游戏界面，包含更多主题和特效"
              >
                <Switch checkedChildren="增强版" unCheckedChildren="经典版" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name={['settings', 'auto_draw']}
                label="自动抽奖"
                valuePropName="checked"
                tooltip="验证后自动开始抽奖"
              >
                <Switch checkedChildren="开启" unCheckedChildren="关闭" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name={['settings', 'enable_share']}
                label="启用分享"
                valuePropName="checked"
                tooltip="是否允许用户分享游戏结果"
              >
                <Switch checkedChildren="开启" unCheckedChildren="关闭" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="status"
            label="游戏状态"
            rules={[{ required: true, message: '请选择游戏状态' }]}
          >
            <Select placeholder="请选择游戏状态">
              <Option value="draft">草稿</Option>
              <Option value="published">发布</Option>
              <Option value="cancelled">取消</Option>
              {initialValues && initialValues.status === 'ended' && (
                <Option value="ended">已结束</Option>
              )}
            </Select>
          </Form.Item>
        </TabPane>
      </Tabs>

      <Divider />

      <Form.Item>
        <Space>
          <Button type="primary" onClick={handleSubmit} loading={loading}>
            {initialValues ? '保存' : '创建'}
          </Button>
          <Button onClick={onCancel}>取消</Button>
        </Space>
      </Form.Item>
    </Form>
  );
};

export default GameForm;
