#!/usr/bin/env python
# -*- coding: utf-8 -*-

import logging
import uuid
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import and_, or_, update
import asyncio

from models.user import User
from models.base import Base
from ..models.models import DingTalkDocument, DingTalkSpace, DingTalkWorkspace, DingTalkUserMapping
from .dingtalk_api import DingTalkAPI

# 初始化日志
logger = logging.getLogger(__name__)

class DocumentSyncService:
    """钉钉文档同步服务"""
    
    def __init__(self, dingtalk_api: DingTalkAPI, db: AsyncSession):
        self.dingtalk_api = dingtalk_api
        self.db = db
        self.logger = logger  # 添加logger属性
    
    async def sync_workspaces_and_spaces(
        self,
        tenant_id: uuid.UUID,
        project_id: uuid.UUID,
        user_id: uuid.UUID,
        sync_options: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """同步工作空间和知识库（使用新版知识库API）"""
        try:
            logger.info(f"开始同步钉钉工作空间和知识库，项目ID: {project_id}")
            
            total_workspaces = 0
            total_spaces = 0
            total_documents = 0
            error_count = 0
            sync_details = []
            
            # 首先获取钉钉用户ID
            logger.info("正在获取用户钉钉绑定信息...")
            dingtalk_user_id = await self.dingtalk_api._get_dingtalk_user_id(str(user_id))
            
            if not dingtalk_user_id:
                logger.error("未找到用户的钉钉绑定信息，无法调用知识库API")
                return {
                    "success": False,
                    "message": "用户未绑定钉钉账号或数据库中无钉钉用户信息",
                    "details": {
                        "total_workspaces": 0,
                        "total_spaces": 0,
                        "total_documents": 0,
                        "error_count": 1,
                        "sync_time": datetime.now().isoformat(),
                        "sync_details": [{
                            "type": "user",
                            "name": "用户绑定检查",
                            "status": "failed",
                            "message": "未找到钉钉绑定信息，请确保用户已在个人资料页面绑定钉钉账号"
                        }]
                    }
                }
            
            # 获取工作空间列表（使用新版API，传递钉钉用户ID）
            logger.info(f"正在获取钉钉工作空间列表，用户ID: {dingtalk_user_id}")
            workspaces_result = await self.dingtalk_api.get_collaboration_spaces(dingtalk_user_id)
            
            if not workspaces_result or not workspaces_result.get("success"):
                error_msg = workspaces_result.get("message", "未知错误") if workspaces_result else "API调用失败"
                logger.warning(f"获取工作空间列表失败: {error_msg}")
                
                # 检查是否是权限问题
                if "权限" in error_msg or "403" in error_msg:
                    sync_details.append({
                        "type": "workspace",
                        "name": "工作空间权限检查",
                        "status": "failed",
                        "message": f"权限不足: {error_msg}，请确保已开通知识库权限 Wiki.Workspace.Read"
                    })
                else:
                    sync_details.append({
                        "type": "workspace",
                        "name": "工作空间列表",
                        "status": "failed",
                        "message": f"获取失败: {error_msg}"
                    })
                error_count += 1
            else:
                workspaces = workspaces_result.get("workspaces", [])
                total_workspaces = len(workspaces)
                logger.info(f"成功获取 {total_workspaces} 个工作空间")
                
                # 遍历工作空间
                for workspace in workspaces:
                    workspace_id = workspace.get("workspaceId")
                    workspace_name = workspace.get("name", "未命名空间")
                    
                    try:
                        logger.info(f"处理工作空间: {workspace_name} (ID: {workspace_id})")
                        
                        # 保存或更新工作空间信息
                        await self._save_workspace_info(tenant_id, project_id, workspace)
                        
                        # 工作空间本身就是一个知识库空间，直接处理
                        # 根据钉钉API文档，工作空间包含rootNodeId，这就是文档的根节点
                        root_node_id = workspace.get("rootNodeId")
                        workspace_space_count = 1  # 每个工作空间算作一个知识库空间
                        total_spaces += workspace_space_count
                        
                        logger.info(f"处理工作空间作为知识库空间: {workspace_name} (rootNodeId: {root_node_id})")
                        
                        try:
                            # 保存工作空间作为知识库空间
                            space_data = {
                                "spaceId": workspace_id,  # 使用workspaceId作为spaceId
                                "name": workspace_name,
                                "description": workspace.get("description", ""),
                                "rootNodeId": root_node_id
                            }
                            await self._save_space_info(tenant_id, project_id, workspace_id, space_data)
                            
                            # 根据同步选项决定是否同步文档
                            docs_count = 0
                            sync_type = sync_options.get("sync_type", "full") if sync_options else "full"
                            
                            if sync_type == "spaces_only":
                                # 仅同步知识库，不下载文档内容，只保存链接
                                logger.info(f"仅同步知识库模式，跳过文档内容下载: {workspace_name}")
                                docs_count = 0
                            elif root_node_id:
                                # 完整同步或增量同步，获取文档列表
                                docs_count = await self._sync_documents_in_workspace(
                                    tenant_id, project_id, workspace_id, root_node_id, workspace_name, sync_options
                                )
                            total_documents += docs_count
                            
                            sync_details.append({
                                "type": "workspace",
                                "name": workspace_name,
                                "document_count": docs_count,
                                "status": "success",
                                "message": f"同步了 {docs_count} 个文档"
                            })
                            
                        except Exception as e:
                            logger.error(f"处理工作空间 {workspace_name} 失败: {str(e)}")
                            sync_details.append({
                                "type": "workspace",
                                "name": workspace_name,
                                "status": "failed",
                                "message": str(e)
                            })
                            error_count += 1
                            
                    except Exception as e:
                        logger.error(f"处理工作空间 {workspace_name} 失败: {str(e)}")
                        sync_details.append({
                            "type": "workspace",
                            "name": workspace_name,
                            "status": "failed",
                            "message": str(e)
                        })
                        error_count += 1
            
            # 构建返回结果
            sync_time = datetime.now().isoformat()
            success = error_count == 0 or (total_workspaces > 0 and error_count < total_workspaces)
            
            # 提交所有数据库变更
            try:
                await self.db.commit()
                logger.info("数据库事务提交成功")
            except Exception as e:
                logger.error(f"数据库提交失败: {str(e)}")
                await self.db.rollback()
                # 更新错误信息
                error_count += 1
                sync_details.append({
                    "type": "database",
                    "name": "数据库提交",
                    "status": "failed",
                    "message": f"数据库提交失败: {str(e)}"
                })
            
            result = {
                "success": success,
                "message": f"同步完成: {total_workspaces}个工作空间, {total_spaces}个知识库, {total_documents}个文档",
                "details": {
                    "total_workspaces": total_workspaces,
                    "total_spaces": total_spaces,
                    "total_documents": total_documents,
                    "error_count": error_count,
                    "sync_time": sync_time,
                    "sync_details": sync_details,
                    "api_status": "使用钉钉新版知识库API v1.0",
                    "required_permissions": [
                        "Wiki.Workspace.Read（工作空间读权限）",
                        "Document.WorkspaceDocument.Write（知识库文档写权限）",
                        "Document.WorkspaceDocument.Read（知识库文档读权限）"
                    ]
                }
            }
            
            logger.info(f"钉钉文档同步完成: {result['message']}")
            return result
            
        except Exception as e:
            logger.error(f"同步钉钉文档时发生异常: {str(e)}")
            return {
                "success": False,
                "message": f"同步失败: {str(e)}",
                "details": {
                    "total_workspaces": 0,
                    "total_spaces": 0,
                    "total_documents": 0,
                    "error_count": 1,
                    "sync_time": datetime.now().isoformat(),
                    "sync_details": [{
                        "type": "system",
                        "name": "同步服务",
                        "status": "failed",
                        "message": str(e)
                    }]
                }
            }
    
    async def sync_document_to_dingtalk(
        self,
        tenant_id: uuid.UUID,
        project_id: uuid.UUID,
        user_id: uuid.UUID,
        document_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        将系统文档同步到钉钉
        
        Args:
            tenant_id: 租户ID
            project_id: 项目ID
            user_id: 用户ID
            document_data: 文档数据
            
        Returns:
            Dict[str, Any]: 同步结果
        """
        try:
            # 获取用户的钉钉映射
            user_mapping = await self._get_user_dingtalk_mapping(tenant_id, project_id, user_id)
            if not user_mapping:
                return {
                    "success": False,
                    "message": "用户未绑定钉钉账号",
                    "error_code": "USER_NOT_MAPPED"
                }
            
            # 检查目标知识库
            space_id = document_data.get("space_id")
            if not space_id:
                return {
                    "success": False,
                    "message": "请指定目标知识库",
                    "error_code": "SPACE_ID_REQUIRED"
                }
            
            # 在钉钉中创建文档
            dingtalk_result = await self.dingtalk_api.create_doc(
                space_id=space_id,
                title=document_data.get("title", ""),
                content=document_data.get("content", ""),
                parent_doc_id=document_data.get("parent_doc_id")
            )
            
            if not dingtalk_result:
                return {
                    "success": False,
                    "message": "在钉钉中创建文档失败",
                    "error_code": "DINGTALK_CREATE_FAILED"
                }
            
            # 创建本地映射记录
            doc_record = DingTalkDocument(
                id=uuid.uuid4(),
                tenant_id=tenant_id,
                project_id=project_id,
                user_id=user_id,
                dingtalk_doc_id=dingtalk_result.get("docId", ""),
                dingtalk_space_id=space_id,
                dingtalk_workspace_id=document_data.get("workspace_id"),
                title=document_data.get("title", ""),
                content=document_data.get("content", ""),
                content_format=document_data.get("content_format", "markdown"),
                document_type=document_data.get("document_type"),
                system_doc_id=document_data.get("system_doc_id"),
                sync_direction=document_data.get("sync_direction", "to_dingtalk"),
                sync_status="synced",
                last_sync_at=datetime.now(),
                tags=document_data.get("tags", []),
                metadata=document_data.get("metadata", {}),
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            
            self.db.add(doc_record)
            await self.db.commit()
            
            logger.info(f"成功将文档同步到钉钉: {document_data.get('title')}")
            
            return {
                "success": True,
                "message": "文档同步到钉钉成功",
                "data": {
                    "document_id": str(doc_record.id),
                    "dingtalk_doc_id": dingtalk_result.get("docId"),
                    "title": document_data.get("title")
                }
            }
            
        except Exception as e:
            logger.error(f"同步文档到钉钉失败: {str(e)}")
            await self.db.rollback()
            return {
                "success": False,
                "message": f"同步文档失败: {str(e)}",
                "error_code": "SYSTEM_ERROR"
            }
    
    async def sync_document_from_dingtalk(
        self,
        tenant_id: uuid.UUID,
        project_id: uuid.UUID,
        user_id: uuid.UUID,
        dingtalk_doc_id: str
    ) -> Dict[str, Any]:
        """
        从钉钉同步文档到系统
        
        Args:
            tenant_id: 租户ID
            project_id: 项目ID
            user_id: 用户ID
            dingtalk_doc_id: 钉钉文档ID
            
        Returns:
            Dict[str, Any]: 同步结果
        """
        try:
            # 获取钉钉文档内容
            doc_content = await self.dingtalk_api.get_doc_content(dingtalk_doc_id, "markdown")
            if not doc_content:
                return {
                    "success": False,
                    "message": "获取钉钉文档内容失败",
                    "error_code": "DINGTALK_GET_FAILED"
                }
            
            # 检查是否已存在映射
            existing_doc = await self._get_document_by_dingtalk_id(tenant_id, project_id, dingtalk_doc_id)
            if existing_doc:
                # 更新现有文档
                existing_doc.title = doc_content.get("title", existing_doc.title)
                existing_doc.content = doc_content.get("content", existing_doc.content)
                existing_doc.sync_status = "synced"
                existing_doc.last_sync_at = datetime.now()
                existing_doc.dingtalk_updated_at = datetime.now()
                existing_doc.updated_at = datetime.now()
                
                await self.db.commit()
                
                return {
                    "success": True,
                    "message": "文档更新成功",
                    "data": {
                        "document_id": str(existing_doc.id),
                        "action": "updated"
                    }
                }
            else:
                # 创建新的文档映射
                doc_record = DingTalkDocument(
                    id=uuid.uuid4(),
                    tenant_id=tenant_id,
                    project_id=project_id,
                    user_id=user_id,
                    dingtalk_doc_id=dingtalk_doc_id,
                    dingtalk_space_id=doc_content.get("spaceId", ""),
                    title=doc_content.get("title", ""),
                    content=doc_content.get("content", ""),
                    content_format="markdown",
                    sync_direction="from_dingtalk",
                    sync_status="synced",
                    last_sync_at=datetime.now(),
                    dingtalk_updated_at=datetime.now(),
                    created_at=datetime.now(),
                    updated_at=datetime.now()
                )
                
                self.db.add(doc_record)
                await self.db.commit()
                
                return {
                    "success": True,
                    "message": "文档导入成功",
                    "data": {
                        "document_id": str(doc_record.id),
                        "action": "created"
                    }
                }
            
        except Exception as e:
            logger.error(f"从钉钉同步文档失败: {str(e)}")
            await self.db.rollback()
            return {
                "success": False,
                "message": f"同步文档失败: {str(e)}",
                "error_code": "SYSTEM_ERROR"
            }
    
    async def get_project_documents(
        self,
        tenant_id: uuid.UUID,
        project_id: uuid.UUID,
        filters: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """
        获取项目的钉钉文档列表
        
        Args:
            tenant_id: 租户ID
            project_id: 项目ID
            filters: 过滤条件
            
        Returns:
            Dict[str, Any]: 文档列表
        """
        try:
            # 构建查询条件
            conditions = [
                DingTalkDocument.tenant_id == tenant_id,
                DingTalkDocument.project_id == project_id
            ]
            
            if filters:
                if filters.get("space_id"):
                    conditions.append(DingTalkDocument.dingtalk_space_id == filters["space_id"])
                if filters.get("sync_status"):
                    conditions.append(DingTalkDocument.sync_status == filters["sync_status"])
                if filters.get("user_id"):
                    conditions.append(DingTalkDocument.user_id == uuid.UUID(filters["user_id"]))
            
            # 查询文档列表
            query = select(DingTalkDocument).where(and_(*conditions)).order_by(DingTalkDocument.updated_at.desc())
            result = await self.db.execute(query)
            documents = result.scalars().all()
            
            # 按知识库分组
            spaces_dict = {}
            for doc in documents:
                space_id = doc.dingtalk_space_id
                if space_id not in spaces_dict:
                    spaces_dict[space_id] = {
                        "space_id": space_id,
                        "documents": []
                    }
                
                spaces_dict[space_id]["documents"].append({
                    "id": str(doc.id),
                    "title": doc.title,
                    "dingtalk_doc_id": doc.dingtalk_doc_id,
                    "content_format": doc.content_format,
                    "document_type": doc.document_type,
                    "sync_status": doc.sync_status,
                    "sync_direction": doc.sync_direction,
                    "last_sync_at": doc.last_sync_at.isoformat() if doc.last_sync_at else None,
                    "tags": doc.tags or [],
                    "created_at": doc.created_at.isoformat() if doc.created_at else None,
                    "updated_at": doc.updated_at.isoformat() if doc.updated_at else None
                })
            
            return {
                "success": True,
                "data": {
                    "total_count": len(documents),
                    "spaces": list(spaces_dict.values())
                }
            }
            
        except Exception as e:
            logger.error(f"获取项目文档列表失败: {str(e)}")
            return {
                "success": False,
                "message": f"获取文档列表失败: {str(e)}",
                "error_code": "SYSTEM_ERROR"
            }
    
    async def _sync_space_documents(
        self,
        tenant_id: uuid.UUID,
        project_id: uuid.UUID,
        space_id: str,
        sync_options: Dict[str, Any] = None
    ) -> int:
        """
        同步指定知识库中的文档
        
        根据钉钉官方文档使用节点查询API获取文档信息
        
        Args:
            tenant_id: 租户ID
            project_id: 项目ID
            space_id: 知识库ID
            sync_options: 同步选项
            
        Returns:
            同步的文档数量
        """
        logger.info(f"📄 开始同步知识库 {space_id} 中的文档")
        
        documents_synced = 0
        
        try:
            # 使用新的节点查询API获取知识库根节点列表
            nodes_result = await self.dingtalk_api.get_knowledge_node_list(space_id)
            
            if not nodes_result or not nodes_result.get("nodes"):
                logger.warning(f"⚠️ 知识库 {space_id} 中没有找到节点")
                return 0
            
            nodes = nodes_result["nodes"]
            logger.info(f"📋 找到 {len(nodes)} 个根节点")
            
            # 递归处理每个节点
            for node in nodes:
                node_count = await self._process_knowledge_node(
                    tenant_id, project_id, space_id, node, sync_options
                )
                documents_synced += node_count
            
            logger.info(f"✅ 知识库 {space_id} 文档同步完成，共同步 {documents_synced} 个文档")
            
        except Exception as e:
            logger.error(f"❌ 同步知识库 {space_id} 文档失败: {e}", exc_info=True)
            
        return documents_synced
    
    async def _process_knowledge_node(
        self,
        tenant_id: uuid.UUID,
        project_id: uuid.UUID,
        space_id: str,
        node: Dict[str, Any],
        sync_options: Dict[str, Any] = None,
        parent_path: str = ""
    ) -> int:
        """
        处理知识库节点（递归处理子节点）
        
        根据钉钉官方文档的节点结构处理不同类型的节点
        
        Args:
            tenant_id: 租户ID
            project_id: 项目ID
            space_id: 知识库ID
            node: 节点信息
            sync_options: 同步选项
            parent_path: 父路径
            
        Returns:
            处理的文档数量
        """
        documents_count = 0
        
        try:
            node_id = node.get("nodeId")
            node_name = node.get("name", "")
            node_type = node.get("type", "")  # file, doc, sheet, slide等
            has_children = node.get("hasChildren", False)
            
            current_path = f"{parent_path}/{node_name}" if parent_path else node_name
            
            logger.info(f"🔍 处理节点: {node_name} (ID: {node_id}, 类型: {node_type})")
            
            # 获取节点详细信息
            node_detail = await self.dingtalk_api.get_knowledge_node_info(node_id)
            
            if node_detail:
                # 保存节点信息为文档
                await self._save_node_as_document(
                    tenant_id, project_id, space_id, node_detail, current_path, sync_options
                )
                documents_count += 1
            
            # 如果有子节点，递归处理
            if has_children:
                logger.info(f"📁 节点 {node_name} 有子节点，开始递归处理")
                
                child_nodes_result = await self.dingtalk_api.get_knowledge_node_list(
                    space_id, parent_node_id=node_id
                )
                
                if child_nodes_result and child_nodes_result.get("nodes"):
                    for child_node in child_nodes_result["nodes"]:
                        child_count = await self._process_knowledge_node(
                            tenant_id, project_id, space_id, child_node, 
                            sync_options, current_path
                        )
                        documents_count += child_count
            
        except Exception as e:
            logger.error(f"❌ 处理节点失败: {e}", exc_info=True)
            
        return documents_count
    
    async def _save_node_as_document(
        self,
        tenant_id: uuid.UUID,
        project_id: uuid.UUID,
        space_id: str,
        node_detail: Dict[str, Any],
        node_path: str,
        sync_options: Dict[str, Any] = None
    ) -> None:
        """
        将知识库节点保存为文档记录
        
        Args:
            tenant_id: 租户ID
            project_id: 项目ID
            space_id: 知识库ID
            node_detail: 节点详细信息
            node_path: 节点路径
            sync_options: 同步选项
        """
        try:
            node_id = node_detail.get("nodeId")
            node_name = node_detail.get("name", "")
            node_type = node_detail.get("type", "doc")
            node_url = node_detail.get("url", "")
            created_time = node_detail.get("createTime")
            modified_time = node_detail.get("modifiedTime")
            creator = node_detail.get("creator", {})
            
            # 生成内容预览
            content_preview = self._generate_node_preview(node_detail, node_type)
            
            # 检查是否已存在
            existing_doc = await self._get_document_by_node_id(tenant_id, project_id, node_id)
            
            if existing_doc:
                # 更新现有文档
                existing_doc.title = node_name
                existing_doc.document_type = node_type
                existing_doc.dingtalk_url = node_url
                existing_doc.content_preview = content_preview
                existing_doc.file_path = node_path
                existing_doc.storage_strategy = "link_only"  # 钉钉文档只能链接访问
                existing_doc.updated_at = datetime.utcnow()
                
                logger.debug(f"📝 更新文档: {node_name}")
            else:
                # 创建新文档记录
                new_doc = DingTalkDocument(
                    id=uuid.uuid4(),
                    tenant_id=tenant_id,
                    project_id=project_id,
                    dingtalk_space_id=space_id,
                    dingtalk_doc_id=node_id,
                    title=node_name,
                    document_type=node_type,
                    dingtalk_url=node_url,
                    content_preview=content_preview,
                    file_path=node_path,
                    storage_strategy="link_only",  # 钉钉文档只能链接访问
                    sync_status="synced",
                    created_at=datetime.utcnow(),
                    updated_at=datetime.utcnow()
                )
                
                self.db.add(new_doc)
                logger.debug(f"📄 新增文档: {node_name}")
            
            await self.db.commit()
            
        except Exception as e:
            logger.error(f"❌ 保存节点文档失败: {e}", exc_info=True)
            await self.db.rollback()
    
    def _generate_node_preview(self, node_detail: Dict[str, Any], node_type: str) -> str:
        """
        根据节点类型生成内容预览
        
        Args:
            node_detail: 节点详细信息
            node_type: 节点类型
            
        Returns:
            内容预览文本
        """
        node_name = node_detail.get("name", "")
        creator = node_detail.get("creator", {})
        creator_name = creator.get("name", "未知")
        created_time = node_detail.get("createTime", "")
        
        # 根据节点类型生成不同的预览
        type_icons = {
            "doc": "📄",
            "sheet": "📊", 
            "slide": "📊",
            "file": "📁",
            "folder": "📂"
        }
        
        icon = type_icons.get(node_type, "📄")
        
        preview = f"{icon} {node_name}\n"
        preview += f"📅 创建时间: {created_time}\n"
        preview += f"👤 创建者: {creator_name}\n"
        preview += f"🔗 类型: {node_type}\n"
        preview += f"💡 说明: 钉钉知识库文档，需要在钉钉环境中查看完整内容"
        
        return preview
    
    async def _create_or_update_space(
        self,
        tenant_id: uuid.UUID,
        project_id: uuid.UUID,
        workspace_id: str,
        space_id: str,
        space_name: str,
        space_desc: str
    ) -> Optional[DingTalkSpace]:
        """创建或更新知识库记录"""
        try:
            # 查找现有记录
            query = select(DingTalkSpace).where(
                and_(
                    DingTalkSpace.tenant_id == tenant_id,
                    DingTalkSpace.project_id == project_id,
                    DingTalkSpace.dingtalk_space_id == space_id
                )
            )
            result = await self.db.execute(query)
            existing_space = result.scalar_one_or_none()
            
            if existing_space:
                # 更新现有记录
                existing_space.space_name = space_name
                existing_space.space_description = space_desc
                existing_space.updated_at = datetime.now()
                return existing_space
            else:
                # 创建新记录
                new_space = DingTalkSpace(
                    id=uuid.uuid4(),
                    tenant_id=tenant_id,
                    project_id=project_id,
                    dingtalk_space_id=space_id,
                    dingtalk_workspace_id=workspace_id,
                    space_name=space_name,
                    space_description=space_desc,
                    created_at=datetime.now(),
                    updated_at=datetime.now()
                )
                
                self.db.add(new_space)
                return new_space
                
        except Exception as e:
            logger.error(f"创建或更新知识库记录失败: {str(e)}")
            return None
    
    async def _get_space_by_dingtalk_id(
        self,
        tenant_id: uuid.UUID,
        project_id: uuid.UUID,
        dingtalk_space_id: str
    ) -> Optional[DingTalkSpace]:
        """根据钉钉知识库ID获取本地记录"""
        try:
            stmt = select(DingTalkSpace).where(
                and_(
                    DingTalkSpace.tenant_id == tenant_id,
                    DingTalkSpace.project_id == project_id,
                    DingTalkSpace.dingtalk_space_id == dingtalk_space_id
                )
            )
            result = await self.db.execute(stmt)
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"查询知识库记录失败: {str(e)}")
            return None
    
    async def _get_document_by_dingtalk_id(
        self,
        tenant_id: uuid.UUID,
        project_id: uuid.UUID,
        dingtalk_doc_id: str
    ) -> Optional[DingTalkDocument]:
        """根据钉钉文档ID获取本地记录"""
        try:
            stmt = select(DingTalkDocument).where(
                and_(
                    DingTalkDocument.tenant_id == tenant_id,
                    DingTalkDocument.project_id == project_id,
                    DingTalkDocument.dingtalk_doc_id == dingtalk_doc_id
                )
            )
            result = await self.db.execute(stmt)
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"查询文档记录失败: {str(e)}")
            return None
    
    async def _get_user_dingtalk_mapping(
        self,
        tenant_id: uuid.UUID,
        project_id: uuid.UUID,
        user_id: uuid.UUID
    ) -> Optional[DingTalkUserMapping]:
        """获取用户的钉钉映射"""
        query = select(DingTalkUserMapping).where(
            and_(
                DingTalkUserMapping.tenant_id == tenant_id,
                DingTalkUserMapping.project_id == project_id,
                DingTalkUserMapping.user_id == user_id,
                DingTalkUserMapping.is_active == True
            )
        )
        result = await self.db.execute(query)
        return result.scalar_one_or_none()
    
    async def _save_workspace_info(
        self,
        tenant_id: uuid.UUID,
        project_id: uuid.UUID,
        workspace_data: Dict[str, Any]
    ) -> None:
        """保存协作空间信息"""
        try:
            workspace_id = workspace_data.get("workspaceId")
            existing_workspace = await self._get_workspace_by_dingtalk_id(
                tenant_id, project_id, workspace_id
            )
            
            if existing_workspace:
                # 更新现有记录
                existing_workspace.name = workspace_data.get("name", "")
                existing_workspace.description = workspace_data.get("description", "")
                existing_workspace.updated_at = datetime.now()
                logger.debug(f"更新协作空间记录: {workspace_data.get('name')}")
            else:
                # 创建新记录
                new_workspace = DingTalkWorkspace(
                    id=uuid.uuid4(),
                    tenant_id=tenant_id,
                    project_id=project_id,
                    dingtalk_workspace_id=workspace_id,
                    name=workspace_data.get("name", ""),
                    description=workspace_data.get("description", ""),
                    created_at=datetime.now(),
                    updated_at=datetime.now()
                )
                self.db.add(new_workspace)
                logger.debug(f"创建协作空间记录: {workspace_data.get('name')}")
            
            # 提交数据库事务
            await self.db.commit()
            logger.debug(f"协作空间信息已保存到数据库: {workspace_data.get('name')}")
                
        except Exception as e:
            logger.error(f"保存协作空间信息失败: {str(e)}")
            await self.db.rollback()
    
    async def _save_space_info(
        self,
        tenant_id: uuid.UUID,
        project_id: uuid.UUID,
        workspace_id: str,
        space_data: Dict[str, Any]
    ) -> None:
        """保存知识库信息"""
        try:
            space_id = space_data.get("spaceId")
            space_name = space_data.get("name", "")
            space_desc = space_data.get("description", "")
            
            if not space_id:
                logger.warning(f"知识库数据缺少spaceId: {space_data}")
                return
            
            existing_space = await self._get_space_by_dingtalk_id(
                tenant_id, project_id, space_id
            )
            
            if existing_space:
                # 更新现有记录
                existing_space.space_name = space_name
                existing_space.space_description = space_desc
                existing_space.dingtalk_workspace_id = workspace_id
                existing_space.updated_at = datetime.now()
                logger.debug(f"更新知识库记录: {space_name}")
            else:
                # 创建新记录
                new_space = DingTalkSpace(
                    id=uuid.uuid4(),
                    tenant_id=tenant_id,
                    project_id=project_id,
                    dingtalk_space_id=space_id,
                    dingtalk_workspace_id=workspace_id,
                    space_name=space_name,
                    space_description=space_desc,
                    created_at=datetime.now(),
                    updated_at=datetime.now()
                )
                self.db.add(new_space)
            logger.debug(f"创建知识库记录: {space_name}")
            
        # 提交数据库事务
            await self.db.commit()
            logger.debug(f"知识库信息已保存到数据库: {space_name}")
                
        except Exception as e:
            logger.error(f"保存知识库信息失败: {str(e)}")
            await self.db.rollback()
    
    async def _sync_documents_in_workspace(
        self,
        tenant_id: uuid.UUID,
        project_id: uuid.UUID,
        workspace_id: str,
        root_node_id: str,
        workspace_name: str,
        sync_options: Dict[str, Any] = None
    ) -> int:
        """同步工作空间中的文档（使用知识库节点API）"""
        try:
            document_count = 0
            
            logger.info(f"🚀 开始同步工作空间 {workspace_name} 下的文档，根节点ID: {root_node_id}")
            
                # 获取钉钉用户ID作为operatorId
            dingtalk_user_id = await self.dingtalk_api._get_dingtalk_user_id()
            if not dingtalk_user_id:
                logger.warning(f"无法获取operatorId，跳过工作空间 {workspace_name} 的文档同步")
            return 0
            
            # 使用新的深度递归获取方法
            document_count = await self._recursive_get_workspace_nodes(
                tenant_id, project_id, workspace_id, root_node_id, 
                dingtalk_user_id, workspace_name, sync_options
            )
            
            logger.info(f"✅ 工作空间 {workspace_name} 文档同步完成，共同步 {document_count} 个文档")
            return document_count
            
        except Exception as e:
            logger.error(f"❌ 同步工作空间 {workspace_name} 文档失败: {e}", exc_info=True)
            return 0

    async def _recursive_get_workspace_nodes(
        self,
        tenant_id: uuid.UUID,
        project_id: uuid.UUID,
        workspace_id: str,
        parent_node_id: str,
        operator_id: str,
        workspace_name: str,
        sync_options: Dict[str, Any] = None,
        depth: int = 0,
        max_depth: int = 10
    ) -> int:
        """递归获取工作空间节点和文档"""
        if depth >= max_depth:
            logger.warning(f"达到最大递归深度 {max_depth}，停止递归")
            return 0
        
            document_count = 0
        
        try:
            # 使用改进的API参数获取节点列表
            nodes_result = await self.dingtalk_api.get_knowledge_nodes(
                parent_node_id=parent_node_id,
                max_results=50,  # 增加每次获取的数量
                with_permission_role=False,  # 简化权限检查
                operator_id=operator_id
            )
            
            if not nodes_result or not nodes_result.get("nodes"):
                logger.warning(f"获取节点列表失败: {parent_node_id}")
                return 0
            
            nodes = nodes_result.get("nodes", [])
            logger.info(f"{'  ' * depth}📁 深度 {depth}: 获取到 {len(nodes)} 个节点")
            
            for node in nodes:
                try:
                    node_id = node.get("nodeId")
                    node_name = node.get("name", "未命名节点")
                    node_type = node.get("nodeType", "unknown")
                    has_children = node.get("hasChildren", False)
                    
                    logger.info(f"{'  ' * depth}🔍 处理节点: {node_name} (类型: {node_type})")
                    
                    # 保存节点信息为文档记录
                    await self._save_workspace_node_as_document(
                        tenant_id, project_id, workspace_id, node, 
                        workspace_name, sync_options, depth
                    )
                    document_count += 1
                    
                    # 如果有子节点且不是叶子文档，递归获取
                    # 支持大小写不敏感的节点类型匹配
                    if has_children and node_type.lower() not in ["doc", "file"]:
                        child_count = await self._recursive_get_workspace_nodes(
                            tenant_id, project_id, workspace_id, node_id,
                            operator_id, workspace_name, sync_options, depth + 1, max_depth
                        )
                        document_count += child_count
                        
                    # 避免API频率限制
                    await asyncio.sleep(0.1)
            
                except Exception as e:
                    logger.error(f"{'  ' * depth}❌ 处理节点失败 {node.get('name', 'Unknown')}: {e}")
                    continue
            
        except Exception as e:
            logger.error(f"{'  ' * depth}❌ 递归获取节点失败: {e}")
        
        return document_count
    
    async def _save_workspace_node_as_document(
        self,
        tenant_id: uuid.UUID,
        project_id: uuid.UUID,
        workspace_id: str,
        node: Dict[str, Any],
        workspace_name: str,
        sync_options: Dict[str, Any] = None,
        depth: int = 0
    ) -> None:
        """保存工作空间节点为文档记录"""
        try:
            node_id = node.get("nodeId")
            node_name = node.get("name", "未命名节点")
            node_type = node.get("nodeType", "unknown")
            has_children = node.get("hasChildren", False)
            creator_id = node.get("creatorId", "")
            modifier_id = node.get("modifierId", "")
            create_time = node.get("createTime")
            modify_time = node.get("modifyTime")
            
            if not node_id:
                logger.warning(f"节点缺少nodeId，跳过: {node_name}")
                return
            
            # 生成钉钉访问URL
            if node_type == "folder":
                access_url = f"https://alidocs.dingtalk.com/i/workspace/{workspace_id}/folder/{node_id}"
            else:
                access_url = f"https://alidocs.dingtalk.com/i/nodes/{node_id}"
            
            # 构建文档内容
            content_preview = self._generate_node_content_preview(node, workspace_name, depth)
            
            # 检查同步类型决定存储策略
            sync_type = sync_options.get("sync_type", "full") if sync_options else "full"
            storage_strategy = "link_only" if sync_type == "spaces_only" else "mixed"
            
            # 检查是否已存在
            existing_doc = await self._get_document_by_node_id(tenant_id, project_id, node_id)
            
            if existing_doc:
                # 更新现有文档
                existing_doc.title = node_name
                existing_doc.content = content_preview
                existing_doc.dingtalk_url = access_url
                existing_doc.updated_at = datetime.now()
                existing_doc.last_sync_at = datetime.now()
                existing_doc.storage_strategy = storage_strategy
                
                # 更新元数据
                if existing_doc.document_metadata:
                    existing_doc.document_metadata.update({
                        "node_type": node_type,
                        "has_children": has_children,
                        "depth": depth,
                        "last_sync": datetime.now().isoformat()
                    })
                
                    await self.db.commit()
                    logger.info(f"{'  ' * depth}🔄 更新文档: {node_name}")
                else:
                # 创建新文档记录
                    from plugins.dingtalk.models.models import DingTalkDocument
                
                    document = DingTalkDocument(
                        id=uuid.uuid4(),
                        tenant_id=tenant_id,
                        project_id=project_id,
                        dingtalk_workspace_id=workspace_id,
                        dingtalk_space_id=workspace_id,  # 工作空间既是空间
                        dingtalk_doc_id=node_id,
                        title=node_name,
                        content=content_preview,
                        doc_type=self._map_node_type_to_doc_type(node_type),
                        dingtalk_url=access_url,
                        creator_dingtalk_id=creator_id,
                        last_modifier_dingtalk_id=modifier_id,
                        created_at_dingtalk=self._parse_dingtalk_time(create_time) if create_time else None,
                        updated_at_dingtalk=self._parse_dingtalk_time(modify_time) if modify_time else None,
                        storage_strategy=storage_strategy,
                        sync_status="synced",
                        last_sync_at=datetime.now(),
                        document_metadata={
                            "source": "dingtalk_workspace",
                            "workspace_name": workspace_name,
                            "node_type": node_type,
                            "has_children": has_children,
                            "depth": depth,
                            "sync_time": datetime.now().isoformat(),
                            "creator_id": creator_id,
                            "modifier_id": modifier_id
                        },
                        created_at=datetime.now(),
                        updated_at=datetime.now()
                    )
            
                    self.db.add(document)
                await self.db.commit()
                logger.info(f"{'  ' * depth}✨ 新增文档: {node_name}")
                
        except Exception as e:
            logger.error(f"{'  ' * depth}❌ 保存节点文档失败 {node.get('name', 'Unknown')}: {e}")
    
    def _generate_node_content_preview(self, node: Dict[str, Any], workspace_name: str, depth: int) -> str:
        """生成节点内容预览"""
        node_name = node.get("name", "未命名节点")
        node_type = node.get("nodeType", "unknown")
        has_children = node.get("hasChildren", False)
        creator_id = node.get("creatorId", "")
        
        # 根据节点类型生成不同的图标
        type_icons = {
            "doc": "📄",
            "sheet": "📊", 
            "slide": "📊",
            "folder": "📁",
            "file": "📎",
            "unknown": "❓"
        }
        
        icon = type_icons.get(node_type, "📄")
        
        content = f"""# {icon} {node_name}

**文档类型**: {node_type}
**所属工作空间**: {workspace_name}
**层级深度**: {depth}
**是否有子节点**: {'是' if has_children else '否'}
**创建者ID**: {creator_id}

---

> 这是一个从钉钉工作空间同步的{node_type}类型节点。
> 
> 💡 **查看完整内容**: 请点击下方的钉钉链接在钉钉环境中查看完整文档内容。

**同步时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
        
        return content
    
    def _map_node_type_to_doc_type(self, node_type: str) -> str:
        """映射钉钉节点类型到文档类型"""
        type_mapping = {
            "doc": "document",
            "sheet": "spreadsheet", 
            "slide": "presentation",
            "folder": "folder",
            "file": "file"
        }
        return type_mapping.get(node_type, "document")
    
    def _parse_dingtalk_time(self, time_str: str) -> Optional[datetime]:
        """解析钉钉时间格式"""
        if not time_str:
            return None
        
        try:
            # 钉钉时间通常是毫秒时间戳
            if time_str.isdigit():
                timestamp = int(time_str) / 1000  # 转换为秒
                return datetime.fromtimestamp(timestamp)
            else:
                # 尝试ISO格式
                return datetime.fromisoformat(time_str.replace('Z', '+00:00'))
        except Exception as e:
            logger.warning(f"解析时间失败: {time_str}, {e}")
            return None
    
    async def _get_workspace_by_dingtalk_id(
        self,
        tenant_id: uuid.UUID,
        project_id: uuid.UUID,
        dingtalk_workspace_id: str
    ) -> Optional[DingTalkWorkspace]:
        """根据钉钉协作空间ID获取本地记录"""
        try:
            stmt = select(DingTalkWorkspace).where(
                and_(
                    DingTalkWorkspace.tenant_id == tenant_id,
                    DingTalkWorkspace.project_id == project_id,
                    DingTalkWorkspace.dingtalk_workspace_id == dingtalk_workspace_id
                )
            )
            result = await self.db.execute(stmt)
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"查询协作空间记录失败: {str(e)}")
            return None
    
    async def sync_dingtalk_documents(
        self,
        tenant_id: uuid.UUID,
        project_id: uuid.UUID,
        sync_options: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        增强钉钉文档同步
        
        Args:
            tenant_id: 租户ID
            project_id: 项目ID
            sync_options: 同步选项
            
        Returns:
            Dict[str, Any]: 同步结果
        """
        try:
            self.logger.info(f"🚀 开始增强钉钉文档同步")
            self.logger.info(f"📋 同步选项: {sync_options}")
            
            # 解析同步选项
            space_id = sync_options.get("space_id")
            sync_type = sync_options.get("sync_type", "full")
            storage_strategy = sync_options.get("storage_strategy", "link_only")
            max_depth = sync_options.get("max_depth", 3)
            max_documents = sync_options.get("max_documents", 1000)
            
            # 获取用户unionId（用于API调用）
            users = await self.dingtalk_api.get_department_users(1)
            if not users or len(users) == 0:
                return {
            "success": False,
                    "message": "无法获取钉钉用户，请检查API配置",
                    "data": {"synced_documents": 0, "total_spaces": 0}
                }
            
            # 获取第一个用户的详细信息
            test_user = users[0]
            user_detail = await self.dingtalk_api.get_user_detail(test_user.get('userid'))
            if not user_detail or not user_detail.get('unionid'):
                return {
                    "success": False,
                    "message": "无法获取用户unionId，API调用失败",
                    "data": {"synced_documents": 0, "total_spaces": 0}
                }
            
            union_id = user_detail.get('unionid')
            self.logger.info(f"🎯 使用操作用户unionId: {union_id}")
            
            # 统计信息
            sync_stats = {
                "total_spaces": 0,
                "synced_documents": 0,
                "failed_documents": 0,
                "spaces_processed": [],
                "errors": []
            }
            
            if space_id:
                # 同步指定知识库
                self.logger.info(f"📁 同步指定知识库: {space_id}")
                result = await self._sync_specific_space_with_workspace_lookup(
                    space_id, union_id, tenant_id, project_id, 
                    storage_strategy, max_depth, sync_stats
                )
                sync_stats.update(result)
            else:
                # 同步所有知识库
                self.logger.info(f"📁 同步所有知识库")
                result = await self._sync_all_spaces(
                    union_id, tenant_id, project_id, 
                    storage_strategy, max_depth, max_documents, sync_stats
                )
                sync_stats.update(result)
            
            # 提交数据库更改
            await self.db.commit()
            
            self.logger.info(f"✅ 文档同步完成")
            self.logger.info(f"📊 同步统计: {sync_stats}")
            
            return {
                "success": True,
                "message": f"成功同步 {sync_stats['synced_documents']} 个文档",
                "data": sync_stats
            }
            
        except Exception as e:
            self.logger.error(f"❌ 文档同步异常: {str(e)}")
            await self.db.rollback()
            return {
                "success": False,
                "message": f"文档同步失败: {str(e)}",
                "data": {"synced_documents": 0, "total_spaces": 0, "error_details": str(e)}
            }

    async def _sync_specific_space_with_workspace_lookup(
        self,
        space_id: str,
        union_id: str,
        tenant_id: uuid.UUID,
        project_id: uuid.UUID,
        storage_strategy: str,
        max_depth: int,
        sync_stats: Dict[str, Any]
    ) -> Dict[str, Any]:
        """同步指定知识库，先查找对应的工作空间"""
        try:
            self.logger.info(f"🔍 查找知识库对应的工作空间: {space_id}")
            
            # 首先获取所有工作空间，找到匹配的
            workspaces_result = await self.dingtalk_api.get_collaboration_spaces(union_id, max_results=100)
            
            if not workspaces_result or not workspaces_result.get('success'):
                error_msg = workspaces_result.get('message', 'Unknown') if workspaces_result else 'API返回None'
                self.logger.error(f"❌ 获取工作空间列表失败: {error_msg}")
                sync_stats["errors"].append(f"获取工作空间失败: {error_msg}")
                return sync_stats
            
            workspaces = workspaces_result.get('workspaces', [])
            target_workspace = None
            
            # 查找匹配的工作空间
            for workspace in workspaces:
                workspace_id = workspace.get('workspaceId')
                if workspace_id == space_id:
                    target_workspace = workspace
                    break
            
            if not target_workspace:
                self.logger.error(f"❌ 未找到指定的知识库: {space_id}")
                sync_stats["errors"].append(f"未找到指定的知识库: {space_id}")
                return sync_stats
            
            workspace_name = target_workspace.get('name', f'工作空间_{space_id[:8]}')
            root_node_id = target_workspace.get('rootNodeId')
            
            if not root_node_id:
                self.logger.error(f"❌ 知识库 {workspace_name} 没有根节点ID")
                sync_stats["errors"].append(f"知识库 {workspace_name} 没有根节点ID")
                return sync_stats
            
            self.logger.info(f"✅ 找到目标知识库: {workspace_name} (rootNodeId: {root_node_id})")
            
            # 同步工作空间文档
            result = await self._sync_workspace_documents_with_root_node(
                space_id, root_node_id, workspace_name, union_id, 
                tenant_id, project_id, storage_strategy, max_depth
            )
            
            sync_stats["total_spaces"] = 1
            sync_stats["synced_documents"] = result.get("documents_synced", 0)
            sync_stats["failed_documents"] = result.get("documents_failed", 0)
            
            sync_stats["spaces_processed"].append({
                "workspace_id": space_id,
                "workspace_name": workspace_name,
                "root_node_id": root_node_id,
                "documents_synced": result.get("documents_synced", 0),
                "documents_failed": result.get("documents_failed", 0)
            })
            
            return sync_stats
            
        except Exception as e:
            error_msg = f"同步指定知识库失败: {str(e)}"
            self.logger.error(error_msg)
            sync_stats["errors"].append(error_msg)
            return sync_stats
    
    async def _sync_workspace_documents_with_root_node(
        self,
        workspace_id: str,
        root_node_id: str,
        workspace_name: str,
        union_id: str,
        tenant_id: uuid.UUID,
        project_id: uuid.UUID,
        storage_strategy: str,
        max_depth: int
    ) -> Dict[str, Any]:
        """使用根节点ID同步工作空间文档"""
        try:
            self.logger.info(f"📂 同步工作空间: {workspace_name} (根节点: {root_node_id})")
            
            result = {
                "documents_synced": 0,
                "documents_failed": 0,
                "documents_processed": []
            }
            
            # 获取并处理工作空间下的文档
            try:
                self.logger.info(f"🔍 获取工作空间 {workspace_name} 节点，参数: parent_node_id={root_node_id}, operator_id={union_id}")
                
                nodes_result = await self.dingtalk_api.get_knowledge_nodes(
                    parent_node_id=root_node_id,
                    max_results=50,
                    operator_id=union_id
                )
                
                if nodes_result and nodes_result.get('success'):
                    nodes = nodes_result.get('nodes', [])
                    self.logger.info(f"✅ 工作空间 {workspace_name} 获取到 {len(nodes)} 个节点")
                    
                    # 处理每个节点
                    for node in nodes:
                        try:
                            node_result = await self._process_node(
                                node, workspace_id, workspace_name, union_id,
                                tenant_id, project_id, storage_strategy, 0, max_depth
                            )
                            
                            result["documents_synced"] += node_result.get("documents_synced", 0)
                            result["documents_failed"] += node_result.get("documents_failed", 0)
                            
                        except Exception as e:
                            self.logger.error(f"❌ 处理节点失败: {str(e)}")
                            result["documents_failed"] += 1
                else:
                    error_msg = nodes_result.get('message', 'Unknown error') if nodes_result else 'API返回None'
                    self.logger.warning(f"⚠️ 获取工作空间 {workspace_name} 节点失败: {error_msg}")
                    # 这不是致命错误，继续处理其他工作空间
                    return result
            
            except Exception as e:
                self.logger.error(f"❌ 工作空间 {workspace_name} 同步异常: {str(e)}")
                result["documents_failed"] += 1
            
            return result
            
        except Exception as e:
            self.logger.error(f"同步工作空间文档异常: {str(e)}")
            return {"documents_synced": 0, "documents_failed": 1, "documents_processed": []}
    
    async def _sync_all_spaces(
        self,
        union_id: str,
        tenant_id: uuid.UUID,
        project_id: uuid.UUID,
        storage_strategy: str,
        max_depth: int,
        max_documents: int,
        sync_stats: Dict[str, Any]
    ) -> Dict[str, Any]:
        """同步所有工作空间"""
        try:
            # 获取工作空间列表
            workspaces_result = await self.dingtalk_api.get_collaboration_spaces(union_id, max_results=50)
            
            if not workspaces_result or not workspaces_result.get('success'):
                error_msg = workspaces_result.get('message', 'Unknown') if workspaces_result else 'API返回None'
                sync_stats["errors"].append(f"获取工作空间失败: {error_msg}")
                return sync_stats
            
            workspaces = workspaces_result.get('workspaces', [])
            sync_stats["total_spaces"] = len(workspaces)
            
            self.logger.info(f"📁 找到 {len(workspaces)} 个工作空间")
            
            document_count = 0
            
            for workspace in workspaces:
                if document_count >= max_documents:
                    self.logger.info(f"📊 已达到最大文档数量限制: {max_documents}")
                    break
                
                workspace_id = workspace.get('workspaceId')
                workspace_name = workspace.get('name', 'Unknown')
                
                try:
                    result = await self._sync_workspace_documents(
                        workspace_id, workspace_name, union_id, 
                        tenant_id, project_id, storage_strategy, max_depth
                    )
                    
                    sync_stats["synced_documents"] += result.get("documents_synced", 0)
                    sync_stats["failed_documents"] += result.get("documents_failed", 0)
                    document_count += result.get("documents_synced", 0)
                    
                    sync_stats["spaces_processed"].append({
                        "workspace_id": workspace_id,
                        "workspace_name": workspace_name,
                        "documents_synced": result.get("documents_synced", 0),
                        "documents_failed": result.get("documents_failed", 0)
                    })
            
                except Exception as e:
                    error_msg = f"同步工作空间 {workspace_name} 失败: {str(e)}"
                    self.logger.error(error_msg)
                    sync_stats["errors"].append(error_msg)
                    sync_stats["failed_documents"] += 1
            
            return sync_stats
            
        except Exception as e:
            sync_stats["errors"].append(f"同步所有工作空间异常: {str(e)}")
            return sync_stats
    
    async def _sync_workspace_documents(
        self,
        workspace_id: str,
        workspace_name: str,
        union_id: str,
        tenant_id: uuid.UUID,
        project_id: uuid.UUID,
        storage_strategy: str,
        max_depth: int
    ) -> Dict[str, Any]:
        """同步单个工作空间的文档"""
        try:
            self.logger.info(f"📂 同步工作空间: {workspace_name}")
            
            result = {
                "documents_synced": 0,
                "documents_failed": 0,
                "documents_processed": []
            }
            
            # 获取知识库节点
            nodes_result = await self.dingtalk_api.get_knowledge_nodes(
                parent_node_id=workspace_id,
                max_results=100,
                operator_id=union_id
            )
            
            if not nodes_result or not nodes_result.get('success'):
                error_msg = nodes_result.get('message', 'Unknown') if nodes_result else 'API返回None'
                self.logger.warning(f"⚠️ 获取工作空间 {workspace_name} 节点失败: {error_msg}")
                result["documents_failed"] += 1
            return result 

            nodes = nodes_result.get('nodes', [])
            self.logger.info(f"📑 工作空间 {workspace_name} 找到 {len(nodes)} 个节点")
            
            for node in nodes:
                try:
                    node_result = await self._process_node(
                        node, workspace_id, workspace_name, union_id, 
                        tenant_id, project_id, storage_strategy, 
                        current_depth=1, max_depth=max_depth
                    )
                    
                    result["documents_synced"] += node_result.get("documents_synced", 0)
                    result["documents_failed"] += node_result.get("documents_failed", 0)
                    result["documents_processed"].extend(node_result.get("documents_processed", []))
                    
                except Exception as e:
                    self.logger.error(f"处理节点失败 {node.get('name', 'Unknown')}: {str(e)}")
                    result["documents_failed"] += 1
            
            return result
            
        except Exception as e:
            self.logger.error(f"同步工作空间文档异常: {str(e)}")
            return {"documents_synced": 0, "documents_failed": 1, "documents_processed": []}
    
    async def _process_node(
        self,
        node: Dict[str, Any],
        workspace_id: str,
        workspace_name: str,
        union_id: str,
        tenant_id: uuid.UUID,
        project_id: uuid.UUID,
        storage_strategy: str,
        current_depth: int,
        max_depth: int
    ) -> Dict[str, Any]:
        """处理单个节点"""
        try:
            node_id = node.get('nodeId')
            node_name = node.get('name', 'Unknown')
            node_type = node.get('nodeType', 'unknown')
            
            result = {
                "documents_synced": 0,
                "documents_failed": 0,
                "documents_processed": []
            }
            
            self.logger.info(f"📝 处理节点: {node_name} (类型: {node_type}, 深度: {current_depth})")
            
            # 如果是文档类型，保存文档信息
            # 根据钉钉官方文档支持更全面的节点类型，包括：
            # - doc: 文档类型
            # - sheet: 表格类型  
            # - mind: 脑图类型
            # - file: 文件类型
            # - folder: 文件夹类型
            # - unknown: 未知类型（特殊文档格式）
            # - presentation: 演示文稿
            # - form: 表单
            # - app: 应用
            supported_types = ['doc', 'sheet', 'mind', 'file', 'folder', 'unknown', 'presentation', 'form', 'app']
            if node_type.lower() in supported_types:
                await self._save_document_info(
                    node, workspace_id, workspace_name, 
                    tenant_id, project_id, storage_strategy,
                    user_id=None  # 让方法内部获取真实用户ID
                )
                result["documents_synced"] += 1
                result["documents_processed"].append({
                    "node_id": node_id,
                    "node_name": node_name,
                    "node_type": node_type,
                    "workspace_name": workspace_name
                })
            
            # 如果有子节点且未达到最大深度，递归处理
            if (node.get('hasChildren', False) and 
                current_depth < max_depth and 
                node_type.lower() != 'doc'):  # 文档类型不需要递归，支持大小写不敏感
                
                try:
                    child_nodes_result = await self.dingtalk_api.get_knowledge_nodes(
                        parent_node_id=node_id,
                        max_results=50,
                        operator_id=union_id
                    )
                    
                    if child_nodes_result and child_nodes_result.get('success'):
                        child_nodes = child_nodes_result.get('nodes', [])
                        self.logger.info(f"📂 节点 {node_name} 有 {len(child_nodes)} 个子节点")
                        
                        for child_node in child_nodes:
                            child_result = await self._process_node(
                                child_node, workspace_id, workspace_name, union_id,
                                tenant_id, project_id, storage_strategy,
                                current_depth + 1, max_depth
                            )
                            
                            result["documents_synced"] += child_result.get("documents_synced", 0)
                            result["documents_failed"] += child_result.get("documents_failed", 0)
                            result["documents_processed"].extend(child_result.get("documents_processed", []))
                    
                except Exception as e:
                    self.logger.error(f"获取子节点失败 {node_name}: {str(e)}")
                    result["documents_failed"] += 1
            
            return result
            
        except Exception as e:
            self.logger.error(f"处理节点异常: {str(e)}")
            return {"documents_synced": 0, "documents_failed": 1, "documents_processed": []}
    
    async def _save_document_info(
        self,
        node: Dict[str, Any],
        workspace_id: str,
        workspace_name: str,
        tenant_id: uuid.UUID,
        project_id: uuid.UUID,
        storage_strategy: str,
        user_id: uuid.UUID = None
    ):
        """保存文档信息到数据库"""
        try:
            # 如果没有提供user_id，从数据库获取一个真实的用户ID
            if user_id is None:
                from models.user import User
                user_query = select(User).where(User.status == "active").limit(1)
                result = await self.db.execute(user_query)
                real_user = result.scalar_one_or_none()
                if real_user:
                    user_id = real_user.id
                else:
                    # 如果没有用户，跳过文档保存
                    self.logger.warning("没有找到真实用户，跳过文档保存")
                    return
            
            node_id = node.get('nodeId')
            node_name = node.get('name', 'Unknown')
            node_type = node.get('nodeType', 'unknown')
            
            # 检查文档是否已存在
            existing_query = select(DingTalkDocument).where(
                and_(
                    DingTalkDocument.tenant_id == tenant_id,
                    DingTalkDocument.project_id == project_id,
                    DingTalkDocument.dingtalk_node_id == node_id
                )
            )
            result = await self.db.execute(existing_query)
            existing_doc = result.scalar_one_or_none()
            
            # 生成访问链接
            access_url = f"dingtalk://dingtalkclient/action/openapp?corpid=&container_type=work_platform&app_id=0_workplace&redirect_type=jump&redirect_url=https://alidocs.dingtalk.com/i/nodes/{node_id}"
            
            if existing_doc:
                # 更新现有文档
                existing_doc.title = node_name
                existing_doc.node_type = node_type
                existing_doc.workspace_name = workspace_name
                existing_doc.storage_strategy = storage_strategy
                existing_doc.access_url = access_url
                existing_doc.updated_at = datetime.now()
                existing_doc.doc_metadata = node
            else:
                # 创建新文档记录，user_id设为None以满足非空约束
                new_doc = DingTalkDocument(
                    id=uuid.uuid4(),
                    tenant_id=tenant_id,
                    project_id=project_id,
                    user_id=user_id,  # 现在使用真实的用户ID
                    dingtalk_workspace_id=workspace_id,
                    dingtalk_space_id=workspace_id,  # 使用workspace_id作为space_id
                    dingtalk_node_id=node_id,
                    dingtalk_doc_id=node_id,  # 使用node_id作为doc_id
                    title=node_name,
                    node_type=node_type,
                    workspace_name=workspace_name,
                    storage_strategy=storage_strategy,
                    access_url=access_url,
                    doc_metadata=node,
                    created_at=datetime.now(),
                    updated_at=datetime.now()
                )
                self.db.add(new_doc)
            
            self.logger.info(f"💾 保存文档: {node_name} (策略: {storage_strategy})")
            
        except Exception as e:
            self.logger.error(f"保存文档信息失败: {str(e)}")
            raise
    
    async def get_synced_documents(
        self,
        tenant_id: uuid.UUID,
        project_id: uuid.UUID,
        workspace_id: Optional[str] = None,
        limit: int = 20,
        offset: int = 0
    ) -> Dict[str, Any]:
        """获取已同步的文档列表"""
        try:
            query = select(DingTalkDocument).where(
                and_(
                    DingTalkDocument.tenant_id == tenant_id,
                    DingTalkDocument.project_id == project_id
                )
            )
            
            if workspace_id:
                query = query.where(DingTalkDocument.dingtalk_workspace_id == workspace_id)
            
            # 排序和分页
            query = query.order_by(desc(DingTalkDocument.updated_at)).offset(offset).limit(limit)
            
            result = await self.db.execute(query)
            documents = result.scalars().all()
            
            documents_data = []
            for doc in documents:
                documents_data.append({
                    "id": str(doc.id),
                    "title": doc.title,
                    "node_type": doc.node_type,
                    "workspace_name": doc.workspace_name,
                    "storage_strategy": doc.storage_strategy,
                    "access_url": doc.access_url,
                    "updated_at": doc.updated_at.isoformat() if doc.updated_at else None
                })
            
            return {
                "success": True,
                "data": {
                    "documents": documents_data,
                    "total": len(documents_data)
                }
            }
            
        except Exception as e:
            self.logger.error(f"获取已同步文档失败: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "data": {"documents": [], "total": 0}
            }
    
    async def sync_workspace_documents(
        self,
        workspace_id: str,
        union_id: str
    ) -> Dict[str, Any]:
        """同步工作空间文档（兼容旧接口）"""
        try:
            self.logger.info(f"📁 同步工作空间文档: {workspace_id}")
            
            # 获取知识库节点
            nodes_result = await self.dingtalk_api.get_knowledge_nodes(
                parent_node_id=workspace_id,
                max_results=50,
                operator_id=union_id
            )
            
            if nodes_result and nodes_result.get('success'):
                nodes = nodes_result.get('nodes', [])
                
                return {
                    "success": True,
                    "data": {
                        "synced_documents": len(nodes),
                        "workspace_id": workspace_id,
                        "nodes_found": len(nodes)
                    }
                }
            else:
                error_msg = nodes_result.get('message', 'Unknown') if nodes_result else 'API返回None'
                return {
                    "success": False,
                    "error": error_msg,
                    "data": {"synced_documents": 0}
                }
                
        except Exception as e:
            self.logger.error(f"同步工作空间文档异常: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "data": {"synced_documents": 0}
            } 