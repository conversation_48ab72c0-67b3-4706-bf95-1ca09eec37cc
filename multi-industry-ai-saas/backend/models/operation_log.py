from sqlalchemy import Column, String, <PERSON>olean, DateTime, ForeignKey, JSON, Text, Integer, UniqueConstraint
from sqlalchemy.dialects.postgresql import UUID, INET
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid

from db.database import Base

class OperationLog(Base):
    """操作日志模型"""
    __tablename__ = "operation_logs"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=True)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id"), nullable=False)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    module = Column(String(50), nullable=False)  # 模块，如user, role, store, etc.
    action = Column(String(50), nullable=False)  # 操作，如create, update, delete, etc.
    resource_type = Column(String(50), nullable=False)  # 资源类型，如user, role, store, etc.
    resource_id = Column(UUID(as_uuid=True), nullable=True)  # 资源ID
    description = Column(Text, nullable=False)  # 操作描述
    details = Column(JSON, nullable=True)  # 操作详情，包含修改前后的数据
    ip_address = Column(INET, nullable=True)  # 操作IP地址
    user_agent = Column(Text, nullable=True)  # 用户代理
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # 关系
    tenant = relationship("Tenant", backref="operation_logs")
    project = relationship("Project", backref="operation_logs")
    user = relationship("User")

    def __repr__(self):
        return f"<OperationLog(id={self.id}, module='{self.module}', action='{self.action}')>"
