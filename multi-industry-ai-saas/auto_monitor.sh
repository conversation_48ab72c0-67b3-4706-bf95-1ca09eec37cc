#!/bin/bash

# 自动监控脚本
cd /media/dev/multi-industry-ai-saas

while true; do
    # 检查容器状态
    if ! docker ps | grep -q "retail-ai-saas-backend-dev"; then
        echo "[$(date)] 容器未运行，尝试重启..." >> /tmp/backend_monitor.log
        docker restart retail-ai-saas-backend-dev
        sleep 30
    fi
    
    # 检查健康状态
    response=$(curl -s -w "%{http_code}" -o /dev/null "http://localhost:8001/api/v1/health" 2>/dev/null)
    if [ "$response" != "200" ]; then
        echo "[$(date)] 健康检查失败 (HTTP $response)，尝试重启..." >> /tmp/backend_monitor.log
        docker restart retail-ai-saas-backend-dev
        sleep 30
    fi
    
    # 检查Redis错误
    redis_errors=$(docker logs retail-ai-saas-backend-dev --tail=20 2>/dev/null | grep -c "readuntil() called while another coroutine is already waiting")
    if [ "$redis_errors" -gt 5 ]; then
        echo "[$(date)] 检测到大量Redis错误 ($redis_errors)，重启容器..." >> /tmp/backend_monitor.log
        docker restart retail-ai-saas-backend-dev
        sleep 30
    fi
    
    # 等待60秒
    sleep 60
done 