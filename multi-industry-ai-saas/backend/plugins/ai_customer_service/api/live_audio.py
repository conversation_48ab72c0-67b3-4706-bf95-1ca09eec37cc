#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
实时语音处理 WebSocket API
"""

import logging
import asyncio
import json
import uuid
import tempfile
import os
from typing import Dict, Any, Optional
from datetime import datetime
from fastapi import APIRouter, WebSocket, WebSocketDisconnect, Query, Depends
from sqlalchemy.ext.asyncio import AsyncSession

from api.deps import get_db
from core.auth import get_current_user_ws
from models.user import User
from models.project import Project
from ..services.customer_service import get_customer_service_manager
from services.ai.audio_service import AIAudioService

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter()

class LiveAudioProcessor:
    """实时语音处理器"""
    
    def __init__(self):
        self.active_connections: Dict[str, Dict] = {}
        self.audio_buffers: Dict[str, list] = {}
        
    async def connect(self, websocket: WebSocket, connection_id: str, user: User, project: Project, db: AsyncSession):
        """建立WebSocket连接"""
        await websocket.accept()
        
        self.active_connections[connection_id] = {
            "websocket": websocket,
            "user": user,
            "project": project,
            "db": db,
            "connected_at": datetime.utcnow(),
            "is_processing": False
        }
        self.audio_buffers[connection_id] = []
        
        logger.info(f"实时语音连接建立: user={user.id}, project={project.id}")
        
        # 发送连接确认
        await websocket.send_text(json.dumps({
            "type": "connection_established",
            "message": "实时语音连接已建立",
            "connection_id": connection_id,
            "timestamp": datetime.utcnow().isoformat()
        }))
    
    async def disconnect(self, connection_id: str):
        """断开WebSocket连接"""
        if connection_id in self.active_connections:
            del self.active_connections[connection_id]
        if connection_id in self.audio_buffers:
            del self.audio_buffers[connection_id]
        
        logger.info(f"实时语音连接断开: {connection_id}")
    
    async def process_audio_chunk(self, connection_id: str, audio_data: bytes):
        """处理音频数据块"""
        try:
            if connection_id not in self.active_connections:
                return
            
            connection = self.active_connections[connection_id]
            
            # 添加到缓冲区
            self.audio_buffers[connection_id].append(audio_data)
            
            # 如果正在处理，跳过
            if connection["is_processing"]:
                return
            
            # 检查缓冲区大小，当累积足够数据时进行处理
            buffer_size = sum(len(chunk) for chunk in self.audio_buffers[connection_id])
            
            # 每100KB或每2秒处理一次（这里简化为每10个块处理一次）
            if len(self.audio_buffers[connection_id]) >= 10:
                connection["is_processing"] = True
                
                # 合并音频数据
                combined_audio = b''.join(self.audio_buffers[connection_id])
                self.audio_buffers[connection_id] = []  # 清空缓冲区
                
                # 异步处理音频转录
                asyncio.create_task(self._process_audio_transcription(connection_id, combined_audio))
                
        except Exception as e:
            logger.error(f"处理音频块失败: {e}")
    
    async def _process_audio_transcription(self, connection_id: str, audio_data: bytes):
        """处理音频转录"""
        try:
            connection = self.active_connections.get(connection_id)
            if not connection:
                return
            
            websocket = connection["websocket"]
            user = connection["user"]
            project = connection["project"]
            db = connection["db"]
            
            # 创建临时文件保存音频
            with tempfile.NamedTemporaryFile(suffix='.webm', delete=False) as temp_file:
                temp_file.write(audio_data)
                temp_file_path = temp_file.name
            
            try:
                # 调用AI音频服务进行转录
                transcription_result = await AIAudioService.transcribe_audio(
                    db=db,
                    project_id=project.id,
                    user_id=user.id,
                    audio_file_path=temp_file_path,
                    language="zh",
                    model_id=None  # 使用默认模型
                )
                
                transcription_text = transcription_result.get("text", "").strip()
                
                if transcription_text:
                    # 发送实时转录结果
                    await websocket.send_text(json.dumps({
                        "type": "transcription",
                        "text": transcription_text,
                        "timestamp": datetime.utcnow().isoformat(),
                        "is_final": False
                    }))
                    
                    # 如果转录文本足够长，可以触发AI回复
                    if len(transcription_text) > 10:
                        await self._generate_ai_response(connection_id, transcription_text)
                
            except Exception as e:
                logger.error(f"音频转录失败: {e}")
                await websocket.send_text(json.dumps({
                    "type": "error",
                    "message": f"音频转录失败: {str(e)}",
                    "timestamp": datetime.utcnow().isoformat()
                }))
            
            finally:
                # 清理临时文件
                if os.path.exists(temp_file_path):
                    os.unlink(temp_file_path)
                
                # 重置处理状态
                connection["is_processing"] = False
                
        except Exception as e:
            logger.error(f"音频转录处理失败: {e}")
            if connection_id in self.active_connections:
                self.active_connections[connection_id]["is_processing"] = False
    
    async def _generate_ai_response(self, connection_id: str, transcription_text: str):
        """生成AI回复"""
        try:
            connection = self.active_connections.get(connection_id)
            if not connection:
                return
            
            websocket = connection["websocket"]
            user = connection["user"]
            project = connection["project"]
            db = connection["db"]
            
            # 获取客服管理器
            customer_service_manager = get_customer_service_manager()
            
            # 构建消息对象
            from ..schemas.message import PlatformMessage
            
            platform_message = PlatformMessage(
                content=transcription_text,
                message_type="text",
                user_id=str(user.id),
                platform="live_audio",
                timestamp=datetime.utcnow(),
                metadata={"source": "live_audio_transcription"}
            )
            
            # 生成AI回复
            ai_response, analysis = await customer_service_manager.process_message(
                db=db,
                message=platform_message,
                session_id=f"live_audio_{connection_id}",
                project_id=project.id,
                user_id=user.id
            )
            
            if ai_response and ai_response.content:
                # 发送AI回复
                await websocket.send_text(json.dumps({
                    "type": "ai_response",
                    "response": ai_response.content,
                    "intent": analysis.get("intent", "unknown"),
                    "confidence": analysis.get("confidence", 0.0),
                    "sources": analysis.get("knowledge_sources", []),
                    "timestamp": datetime.utcnow().isoformat()
                }))
                
                # 可选：将回复转换为语音
                if project.id:  # 检查是否有TTS配置
                    try:
                        tts_result = await AIAudioService.text_to_speech(
                            db=db,
                            project_id=project.id,
                            user_id=user.id,
                            text=ai_response.content[:200],  # 限制长度
                            response_format="mp3"
                        )
                        
                        if tts_result.get("audio_base64"):
                            await websocket.send_text(json.dumps({
                                "type": "ai_audio_response",
                                "audio_base64": tts_result["audio_base64"],
                                "format": tts_result.get("format", "mp3"),
                                "timestamp": datetime.utcnow().isoformat()
                            }))
                    except Exception as e:
                        logger.warning(f"TTS转换失败: {e}")
            
        except Exception as e:
            logger.error(f"生成AI回复失败: {e}")
            connection = self.active_connections.get(connection_id)
            if connection:
                await connection["websocket"].send_text(json.dumps({
                    "type": "error",
                    "message": f"生成AI回复失败: {str(e)}",
                    "timestamp": datetime.utcnow().isoformat()
                }))

# 全局实时语音处理器实例
live_audio_processor = LiveAudioProcessor()

@router.websocket("/live-stream")
async def websocket_live_audio(
    websocket: WebSocket,
    token: str = Query(...),
    db: AsyncSession = Depends(get_db)
):
    """实时语音处理WebSocket端点"""
    
    connection_id = str(uuid.uuid4())
    
    try:
        # 验证用户身份
        user = await get_current_user_ws(token, db)
        if not user:
            await websocket.close(code=4001, reason="Unauthorized")
            return
        
        # 获取项目信息 (简化处理，实际应该从token或参数中获取)
        # 这里假设用户有默认项目或从token中解析
        project = user.projects[0] if user.projects else None
        if not project:
            await websocket.close(code=4002, reason="No project found")
            return
        
        # 建立连接
        await live_audio_processor.connect(websocket, connection_id, user, project, db)
        
        # 保持连接并处理消息
        while True:
            try:
                # 接收音频数据
                data = await websocket.receive_bytes()
                
                # 处理音频数据
                await live_audio_processor.process_audio_chunk(connection_id, data)
                
            except WebSocketDisconnect:
                logger.info(f"实时语音WebSocket断开: {connection_id}")
                break
            except Exception as e:
                logger.error(f"处理WebSocket消息失败: {e}")
                await websocket.send_text(json.dumps({
                    "type": "error",
                    "message": f"处理消息失败: {str(e)}",
                    "timestamp": datetime.utcnow().isoformat()
                }))
    
    except Exception as e:
        logger.error(f"WebSocket连接失败: {e}")
        try:
            await websocket.close(code=4000, reason=f"Connection failed: {str(e)}")
        except:
            pass
    
    finally:
        # 清理连接
        await live_audio_processor.disconnect(connection_id) 