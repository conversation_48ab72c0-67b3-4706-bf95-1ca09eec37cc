import api from '../httpClient';
import project from './project';
import user from './user';
import settings from './settings';
import plugins from './plugins';

/**
 * 租户API
 */
const tenant = {
  /**
   * 获取租户信息
   * @returns {Promise} - 租户信息
   */
  getInfo: async () => {
    return api.get('/tenant/info');
  },

  /**
   * 获取租户统计信息
   * @returns {Promise} - 统计信息
   */
  getStats: async () => {
    return api.get('/tenant/stats');
  },

  project,
  user,
  settings,
  plugins
};

export {
  project,
  user,
  settings,
  plugins
};

export default tenant;
