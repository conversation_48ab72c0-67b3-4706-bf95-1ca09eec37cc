import React, { useState, useEffect } from 'react';
import {
  Table,
  Card,
  Button,
  message,
  Tabs,
  Typography,
  Space,
  Tag,
  Modal,
  Form,
  Input,
  DatePicker,
  Select,
  Statistic,
  Row,
  Col,
  Tooltip,
  notification
} from 'antd';
import {
  ReloadOutlined,
  SearchOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ExclamationCircleOutlined,
  DollarOutlined,
  BellOutlined,
  DownloadOutlined
} from '@ant-design/icons';
import apiService from '../../../../services/api';
import dayjs from 'dayjs';

const { Title } = Typography;
const { TabPane } = Tabs;
const { RangePicker } = DatePicker;
const { Option } = Select;
const { confirm } = Modal;

/**
 * 到货审核组件
 * 汇总门店管理日常店务下的到货确认数据
 */
const FinanceArrivalApproval = () => {
  // 状态变量
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('daily');
  const [arrivalList, setArrivalList] = useState([]);
  const [stores, setStores] = useState([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });
  const [filters, setFilters] = useState({
    store_id: null,
    arrival_type: null,
    date_range: null,
    status: 'confirmed' // 默认只显示已确认的到货
  });
  const [arrivalStats, setArrivalStats] = useState({
    total_cost: 0,
    by_store: {},
    by_date: {},
    by_month: {}
  });
  const [selectedArrival, setSelectedArrival] = useState(null);
  const [detailVisible, setDetailVisible] = useState(false);
  const [productDetails, setProductDetails] = useState([]);
  const [remindVisible, setRemindVisible] = useState(false);
  const [remindForm] = Form.useForm();

  // 初始化
  useEffect(() => {
    fetchStores();
  }, []);

  // 监听筛选条件变化，刷新数据
  useEffect(() => {
    fetchArrivalList();
    fetchArrivalStatistics();
  }, [filters, pagination.current, pagination.pageSize, activeTab]);

  // 获取门店列表
  const fetchStores = async () => {
    try {
      const response = await apiService.project.store.getList();
      if (response && response.items) {
        setStores(response.items);
      }
    } catch (error) {
      console.error('获取门店列表失败:', error);
      message.error('获取门店列表失败');
    }
  };

  // 获取到货列表
  const fetchArrivalList = async (params = {}) => {
    setLoading(true);
    try {
      // 准备查询参数
      const queryParams = {
        page: params.page || pagination.current,
        page_size: params.pageSize || pagination.pageSize,
        ...filters
      };

      // 处理日期范围
      if (filters.date_range && filters.date_range.length === 2) {
        queryParams.start_date = filters.date_range[0].format('YYYY-MM-DD');
        queryParams.end_date = filters.date_range[1].format('YYYY-MM-DD');
        delete queryParams.date_range;
      }

      // 调用API获取到货列表
      const response = await apiService.project.finance.getArrivalList(queryParams);

      if (response && response.items) {
        setArrivalList(response.items);
        setPagination({
          ...pagination,
          current: params.page || pagination.current,
          total: response.total || 0
        });
      } else {
        message.error(response?.message || '获取到货列表失败');
      }
    } catch (error) {
      console.error('获取到货列表失败:', error);
      message.error('获取到货列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取到货统计
  const fetchArrivalStatistics = async () => {
    try {
      // 准备查询参数
      const queryParams = {};

      // 处理日期范围
      if (filters.date_range && filters.date_range.length === 2) {
        queryParams.start_date = filters.date_range[0].format('YYYY-MM-DD');
        queryParams.end_date = filters.date_range[1].format('YYYY-MM-DD');
      }

      if (filters.store_id) {
        queryParams.store_id = filters.store_id;
      }

      // 根据当前标签页设置时间范围
      if (activeTab === 'daily') {
        // 如果没有设置日期范围，默认查询当天
        if (!filters.date_range) {
          const today = dayjs();
          queryParams.start_date = today.format('YYYY-MM-DD');
          queryParams.end_date = today.format('YYYY-MM-DD');
        }
      } else if (activeTab === 'monthly') {
        // 如果没有设置日期范围，默认查询当月
        if (!filters.date_range) {
          const today = dayjs();
          queryParams.start_date = today.startOf('month').format('YYYY-MM-DD');
          queryParams.end_date = today.endOf('month').format('YYYY-MM-DD');
        }
      }

      // 调用API获取到货统计
      const response = await apiService.project.finance.getArrivalStatistics(queryParams);

      if (response && response.data) {
        setArrivalStats(response.data);
      }
    } catch (error) {
      console.error('获取到货统计失败:', error);
      message.error('获取到货统计失败');
    }
  };

  // 获取到货详情
  const fetchArrivalDetail = async (arrivalId) => {
    try {
      setLoading(true);
      const response = await apiService.project.store_operations.getArrivalDetail(arrivalId);

      if (response && response.data) {
        setSelectedArrival(response.data);
        setProductDetails(response.data.items || []);
      } else {
        message.error(response?.message || '获取到货详情失败');
      }
    } catch (error) {
      console.error('获取到货详情失败:', error);
      message.error('获取到货详情失败');
    } finally {
      setLoading(false);
    }
  };

  // 处理标签页切换
  const handleTabChange = (key) => {
    setActiveTab(key);

    // 重置日期范围
    let newFilters = { ...filters };
    delete newFilters.date_range;

    if (key === 'daily') {
      // 默认查询当天
      const today = dayjs();
      newFilters.date_range = [today, today];
    } else if (key === 'monthly') {
      // 默认查询当月
      const today = dayjs();
      newFilters.date_range = [today.startOf('month'), today.endOf('month')];
    }

    setFilters(newFilters);
  };

  // 处理表格变化
  const handleTableChange = (pagination, filters, sorter) => {
    fetchArrivalList({
      page: pagination.current,
      pageSize: pagination.pageSize,
      ...filters
    });
  };

  // 处理筛选变化
  const handleFilterChange = (key, value) => {
    const newFilters = { ...filters, [key]: value };
    setFilters(newFilters);

    // 重置分页
    setPagination({
      ...pagination,
      current: 1
    });
  };

  // 查看详情
  const handleViewDetail = (record) => {
    fetchArrivalDetail(record.id);
    setDetailVisible(true);
  };

  // 发送催办通知
  const handleSendReminder = (record) => {
    setSelectedArrival(record);
    remindForm.resetFields();
    setRemindVisible(true);
  };

  // 导出到货列表
  const handleExportArrivals = async () => {
    try {
      setLoading(true);

      // 准备查询参数
      const queryParams = { ...filters };

      // 处理日期范围
      if (filters.date_range && filters.date_range.length === 2) {
        queryParams.start_date = filters.date_range[0].format('YYYY-MM-DD');
        queryParams.end_date = filters.date_range[1].format('YYYY-MM-DD');
        delete queryParams.date_range;
      }

      // 调用导出API
      const response = await apiService.project.finance.exportArrivals(queryParams);

      // 创建下载链接
      const url = window.URL.createObjectURL(new Blob([response]));
      const link = document.createElement('a');
      link.href = url;

      // 设置文件名
      const fileName = `到货列表_${dayjs().format('YYYY-MM-DD_HHmmss')}.xlsx`;
      link.setAttribute('download', fileName);

      // 触发下载
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      message.success('导出成功');
    } catch (error) {
      console.error('导出到货列表失败:', error);
      message.error('导出到货列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 提交催办通知
  const handleSendReminderSubmit = async () => {
    try {
      const values = await remindForm.validateFields();
      setLoading(true);

      // 调用API发送催办通知
      const response = await apiService.project.finance.sendArrivalReminder(
        selectedArrival.id,
        {
          message: values.message
        }
      );

      if (response && response.success) {
        message.success('发送催办通知成功');
        setRemindVisible(false);
      } else {
        message.error(response?.message || '发送催办通知失败');
      }
    } catch (error) {
      console.error('发送催办通知失败:', error);
      if (error.errorFields) {
        // 表单验证错误
        return;
      }
      message.error('发送催办通知失败: ' + (error.message || '未知错误'));
    } finally {
      setLoading(false);
    }
  };

  // 渲染统计卡片
  const renderStatisticCards = () => {
    return (
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={8}>
          <Card>
            <Statistic
              title="总到货成本"
              value={arrivalStats.total_cost}
              precision={2}
              prefix={<DollarOutlined />}
              suffix="元"
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card>
            <Statistic
              title="已确认到货数"
              value={arrivalStats.confirmed_count || 0}
              suffix="单"
            />
          </Card>
        </Col>
        <Col span={8}>
          <Card>
            <Statistic
              title="未确认到货数"
              value={arrivalStats.pending_count || 0}
              suffix="单"
            />
          </Card>
        </Col>
      </Row>
    );
  };

  // 筛选表单
  const FilterForm = () => (
    <Form layout="inline" style={{ marginBottom: 16 }}>
      <Form.Item label="门店">
        <Select
          placeholder="选择门店"
          style={{ width: 200 }}
          allowClear
          onChange={(value) => handleFilterChange('store_id', value)}
          value={filters.store_id}
        >
          {stores.map(store => (
            <Option key={store.id} value={store.id}>{store.name}</Option>
          ))}
        </Select>
      </Form.Item>
      <Form.Item label="到货类型">
        <Select
          placeholder="选择到货类型"
          style={{ width: 150 }}
          allowClear
          onChange={(value) => handleFilterChange('arrival_type', value)}
          value={filters.arrival_type}
        >
          <Option value="purchase">采购到货</Option>
          <Option value="transfer">调拨到货</Option>
          <Option value="return">退货到货</Option>
          <Option value="other">其他到货</Option>
        </Select>
      </Form.Item>
      <Form.Item label="日期范围">
        <RangePicker
          onChange={(dates) => handleFilterChange('date_range', dates)}
          value={filters.date_range}
        />
      </Form.Item>
      <Form.Item label="状态">
        <Select
          placeholder="选择状态"
          style={{ width: 150 }}
          onChange={(value) => handleFilterChange('status', value)}
          value={filters.status}
        >
          <Option value="all">全部</Option>
          <Option value="pending">待确认</Option>
          <Option value="confirmed">已确认</Option>
          <Option value="rejected">已拒绝</Option>
        </Select>
      </Form.Item>
      <Form.Item>
        <Button
          type="primary"
          icon={<SearchOutlined />}
          onClick={() => fetchArrivalList()}
        >
          查询
        </Button>
      </Form.Item>
    </Form>
  );

  // 表格列定义
  const columns = [
    {
      title: '到货编号',
      dataIndex: 'arrival_number',
      key: 'arrival_number',
      width: 150,
    },
    {
      title: '门店',
      dataIndex: 'store_name',
      key: 'store_name',
      width: 150,
    },
    {
      title: '到货类型',
      dataIndex: 'arrival_type',
      key: 'arrival_type',
      width: 120,
      render: (text) => {
        const typeMap = {
          purchase: '采购到货',
          transfer: '调拨到货',
          return: '退货到货',
          other: '其他到货'
        };
        return typeMap[text] || text;
      }
    },
    {
      title: '到货日期',
      dataIndex: 'arrival_date',
      key: 'arrival_date',
      width: 120,
      render: (text) => text ? dayjs(text).format('YYYY-MM-DD') : '-'
    },
    {
      title: '总金额',
      dataIndex: 'total_amount',
      key: 'total_amount',
      width: 120,
      render: (text) => `¥${parseFloat(text).toFixed(2)}`
    },
    {
      title: '商品数量',
      dataIndex: 'total_items',
      key: 'total_items',
      width: 100,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (text) => {
        const statusMap = {
          pending: { color: 'warning', text: '待确认' },
          confirmed: { color: 'success', text: '已确认' },
          rejected: { color: 'error', text: '已拒绝' }
        };
        const status = statusMap[text] || { color: 'default', text };
        return <Tag color={status.color}>{status.text}</Tag>;
      }
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_, record) => (
        <Space size="small">
          <Button type="link" onClick={() => handleViewDetail(record)}>
            详情
          </Button>
          {record.status === 'pending' && (
            <Button type="link" onClick={() => handleSendReminder(record)}>
              催办
            </Button>
          )}
        </Space>
      )
    }
  ];

  return (
    <div className="finance-arrival-approval">
      <Tabs activeKey={activeTab} onChange={handleTabChange}>
        <TabPane tab="每日到货" key="daily">
          <div style={{ marginBottom: 16 }}>
            <FilterForm />
          </div>

          <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between' }}>
            <div>
              <Button
                type="primary"
                icon={<BellOutlined />}
                onClick={() => {
                  // 批量催办未确认的到货
                  confirm({
                    title: '批量催办',
                    icon: <ExclamationCircleOutlined />,
                    content: '确定要对所有未确认的到货进行催办吗？',
                    onOk() {
                      // 调用批量催办API
                      apiService.project.finance.batchSendArrivalReminders()
                        .then(response => {
                          if (response && response.success) {
                            message.success(`已成功发送${response.data.count || 0}条催办通知`);
                          } else {
                            message.error(response?.message || '批量催办失败');
                          }
                        })
                        .catch(error => {
                          console.error('批量催办失败:', error);
                          message.error('批量催办失败: ' + (error.message || '未知错误'));
                        });
                    }
                  });
                }}
                style={{ marginRight: 8 }}
              >
                批量催办
              </Button>
              <Button
                icon={<DownloadOutlined />}
                onClick={handleExportArrivals}
                style={{ marginRight: 8 }}
              >
                导出Excel
              </Button>
            </div>
            <div>
              <Button
                icon={<ReloadOutlined />}
                onClick={() => {
                  fetchArrivalList();
                  fetchArrivalStatistics();
                }}
              >
                刷新
              </Button>
            </div>
          </div>

          {arrivalStats.total_cost > 0 && renderStatisticCards()}

          <Table
            columns={columns}
            dataSource={arrivalList}
            rowKey="id"
            pagination={pagination}
            onChange={handleTableChange}
            loading={loading}
            scroll={{ x: 1200 }}
          />
        </TabPane>

        <TabPane tab="月度到货" key="monthly">
          <div style={{ marginBottom: 16 }}>
            <FilterForm />
          </div>

          {arrivalStats.total_cost > 0 && renderStatisticCards()}

          <Table
            columns={columns}
            dataSource={arrivalList}
            rowKey="id"
            pagination={pagination}
            onChange={handleTableChange}
            loading={loading}
            scroll={{ x: 1200 }}
          />
        </TabPane>
      </Tabs>

      {/* 详情弹窗 */}
      <Modal
        title="到货详情"
        open={detailVisible}
        onCancel={() => setDetailVisible(false)}
        footer={[
          <Button key="close" onClick={() => setDetailVisible(false)}>
            关闭
          </Button>,
          selectedArrival?.status === 'pending' && (
            <Button
              key="remind"
              type="primary"
              onClick={() => {
                setDetailVisible(false);
                handleSendReminder(selectedArrival);
              }}
            >
              催办确认
            </Button>
          )
        ]}
        width={800}
      >
        {selectedArrival && (
          <div>
            <Row gutter={16}>
              <Col span={8}>
                <p><strong>到货编号：</strong>{selectedArrival.arrival_number}</p>
              </Col>
              <Col span={8}>
                <p><strong>门店：</strong>{selectedArrival.store_name}</p>
              </Col>
              <Col span={8}>
                <p><strong>状态：</strong>
                  <Tag color={
                    selectedArrival.status === 'confirmed' ? 'success' :
                    selectedArrival.status === 'rejected' ? 'error' : 'warning'
                  }>
                    {
                      selectedArrival.status === 'confirmed' ? '已确认' :
                      selectedArrival.status === 'rejected' ? '已拒绝' : '待确认'
                    }
                  </Tag>
                </p>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={8}>
                <p><strong>到货日期：</strong>{dayjs(selectedArrival.arrival_date).format('YYYY-MM-DD')}</p>
              </Col>
              <Col span={8}>
                <p><strong>总金额：</strong>¥{parseFloat(selectedArrival.total_amount).toFixed(2)}</p>
              </Col>
              <Col span={8}>
                <p><strong>商品数量：</strong>{selectedArrival.total_items}</p>
              </Col>
            </Row>
            <Row>
              <Col span={24}>
                <p><strong>备注：</strong>{selectedArrival.notes || '无'}</p>
              </Col>
            </Row>

            <Title level={5} style={{ marginTop: 16 }}>商品明细</Title>
            <Table
              columns={[
                {
                  title: '商品名称',
                  dataIndex: 'product_name',
                  key: 'product_name',
                },
                {
                  title: '规格',
                  dataIndex: 'specification',
                  key: 'specification',
                },
                {
                  title: '单位',
                  dataIndex: 'unit',
                  key: 'unit',
                },
                {
                  title: '应到数量',
                  dataIndex: 'expected_quantity',
                  key: 'expected_quantity',
                },
                {
                  title: '实收数量',
                  dataIndex: 'actual_quantity',
                  key: 'actual_quantity',
                },
                {
                  title: '单价',
                  dataIndex: 'price',
                  key: 'price',
                  render: (text) => `¥${parseFloat(text).toFixed(2)}`
                },
                {
                  title: '金额',
                  dataIndex: 'amount',
                  key: 'amount',
                  render: (text) => `¥${parseFloat(text).toFixed(2)}`
                },
              ]}
              dataSource={productDetails}
              rowKey="id"
              pagination={false}
              size="small"
            />
          </div>
        )}
      </Modal>

      {/* 催办弹窗 */}
      <Modal
        title="发送催办通知"
        open={remindVisible}
        onOk={handleSendReminderSubmit}
        onCancel={() => setRemindVisible(false)}
        confirmLoading={loading}
      >
        <Form form={remindForm} layout="vertical">
          <Form.Item
            name="message"
            label="催办消息"
            initialValue="请尽快确认到货信息，以便财务部门进行成本核算。"
            rules={[{ required: true, message: '请输入催办消息' }]}
          >
            <Input.TextArea rows={4} placeholder="请输入催办消息" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default FinanceArrivalApproval;
