#!/bin/bash

# MCP服务器增强功能测试启动脚本

set -e

echo "🚀 MCP服务器增强功能测试启动脚本"
echo "=================================="

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 未安装，请先安装Python3"
    exit 1
fi

# 检查必要的Python包
echo "🔍 检查Python依赖包..."
python3 -c "
import sys
required_packages = ['pillow', 'httpx', 'fastapi', 'uvicorn']
missing_packages = []

for package in required_packages:
    try:
        __import__(package)
        print(f'✅ {package} - 已安装')
    except ImportError:
        missing_packages.append(package)
        print(f'❌ {package} - 未安装')

if missing_packages:
    print(f'\\n缺少以下包: {missing_packages}')
    print('请运行: pip install ' + ' '.join(missing_packages))
    sys.exit(1)
else:
    print('\\n✅ 所有必要的包都已安装')
"

if [ $? -ne 0 ]; then
    echo "❌ 依赖检查失败，请安装缺少的包"
    exit 1
fi

# 设置环境变量
export MCP_MAX_FILE_SIZE=10485760
export MCP_TEMP_DIR=/tmp/mcp
export MCP_STREAM_CHUNK_SIZE=8192
export MCP_STREAM_TIMEOUT=30
export MCP_SUPPORTED_IMAGE_FORMATS=jpg,png,gif,webp
export MCP_SUPPORTED_AUDIO_FORMATS=mp3,wav,aac
export MCP_SUPPORTED_VIDEO_FORMATS=mp4,webm,avi

echo "🔧 环境变量已设置"

# 创建临时目录
mkdir -p /tmp/mcp
echo "📁 临时目录已创建: /tmp/mcp"

# 检查是否存在MCP服务器
if [ ! -f "multi_protocol.py" ]; then
    echo "❌ 未找到 multi_protocol.py 文件"
    echo "请确保在正确的目录中运行此脚本"
    exit 1
fi

# 启动MCP服务器（后台运行）
echo "🚀 启动MCP服务器..."
python3 multi_protocol.py &
MCP_PID=$!

# 等待服务器启动
echo "⏳ 等待服务器启动..."
sleep 5

# 检查服务器是否正在运行
if ! kill -0 $MCP_PID 2>/dev/null; then
    echo "❌ MCP服务器启动失败"
    exit 1
fi

echo "✅ MCP服务器已启动 (PID: $MCP_PID)"

# 等待服务器完全就绪
echo "⏳ 等待服务器就绪..."
for i in {1..10}; do
    if curl -s http://localhost:5000/health > /dev/null 2>&1; then
        echo "✅ 服务器就绪"
        break
    fi
    if [ $i -eq 10 ]; then
        echo "❌ 服务器启动超时"
        kill $MCP_PID 2>/dev/null || true
        exit 1
    fi
    sleep 2
done

# 运行测试
echo ""
echo "🧪 开始运行增强功能测试..."
echo "=================================="

if [ -f "test_enhanced_features.py" ]; then
    python3 test_enhanced_features.py
    TEST_RESULT=$?
else
    echo "❌ 未找到测试文件 test_enhanced_features.py"
    TEST_RESULT=1
fi

# 清理
echo ""
echo "🧹 清理资源..."
echo "=================================="

# 停止MCP服务器
if kill -0 $MCP_PID 2>/dev/null; then
    echo "🛑 停止MCP服务器..."
    kill $MCP_PID
    sleep 2
    
    # 强制停止（如果需要）
    if kill -0 $MCP_PID 2>/dev/null; then
        echo "🔨 强制停止MCP服务器..."
        kill -9 $MCP_PID 2>/dev/null || true
    fi
fi

# 清理临时文件
if [ -d "/tmp/mcp" ]; then
    echo "🗑️ 清理临时文件..."
    rm -rf /tmp/mcp
fi

echo "✅ 清理完成"

# 显示测试结果
echo ""
echo "📊 测试结果"
echo "=================================="

if [ $TEST_RESULT -eq 0 ]; then
    echo "🎉 所有测试通过！"
    echo "✅ MCP服务器增强功能工作正常"
else
    echo "⚠️ 部分测试失败"
    echo "❌ 请检查日志和配置"
fi

echo ""
echo "📝 测试完成"
echo "如需查看详细日志，请检查服务器输出"

exit $TEST_RESULT 