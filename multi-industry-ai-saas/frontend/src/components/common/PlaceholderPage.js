import React from 'react';
import { Card, Typography, Button, Space, Empty } from 'antd';
import { useNavigate } from 'react-router-dom';
import { ArrowLeftOutlined } from '@ant-design/icons';

const { Title, Text } = Typography;

/**
 * 占位页面组件
 * 用于尚未实现的功能页面
 */
const PlaceholderPage = ({ title, description, backPath }) => {
  const navigate = useNavigate();

  return (
    <Card className="placeholder-page">
      <Space direction="vertical" size="large" style={{ width: '100%', textAlign: 'center' }}>
        <Title level={3}>{title}</Title>
        <Text type="secondary">{description || '该功能正在开发中，敬请期待...'}</Text>
        <Empty description="功能开发中" />
        {backPath && (
          <Button 
            type="primary" 
            icon={<ArrowLeftOutlined />} 
            onClick={() => navigate(backPath)}
          >
            返回
          </Button>
        )}
      </Space>
    </Card>
  );
};

export default PlaceholderPage;
