import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  Table,
  Space,
  Tag,
  Modal,
  Form,
  Input,
  Select,
  DatePicker,
  Switch,
  message,
  Descriptions,
  Tabs,
  Row,
  Col,
  Statistic,
  Alert,
  Tooltip,
  Popconfirm,
  Drawer,
  Progress,
  Radio,
  Checkbox,
  Badge
} from 'antd';
import {
  PlusOutlined,
  SyncOutlined,
  CheckOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  ReloadOutlined,
  InfoCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  UserOutlined,
  CalendarOutlined,
  BellOutlined,
  LinkOutlined,
  ProjectOutlined,
  ArrowRightOutlined
} from '@ant-design/icons';
import dingtalkApi from '../service/dingtalkApi';
import apiService from '../../../../../services/api';
import dayjs from 'dayjs';

const { TabPane } = Tabs;
const { Option } = Select;
const { TextArea } = Input;

/**
 * 钉钉待办任务管理组件 - 支持项目任务整合
 */
const TodoTaskManagement = () => {
  const [loading, setLoading] = useState(false);
  const [tasks, setTasks] = useState([]); // 钉钉待办任务
  const [projectTasks, setProjectTasks] = useState([]); // 项目系统任务
  const [statistics, setStatistics] = useState({});
  const [activeTab, setActiveTab] = useState('list');
  const [taskModalVisible, setTaskModalVisible] = useState(false);
  const [syncModalVisible, setSyncModalVisible] = useState(false);
  const [integrationModalVisible, setIntegrationModalVisible] = useState(false);
  const [detailDrawerVisible, setDetailDrawerVisible] = useState(false);
  const [editingTask, setEditingTask] = useState(null);
  const [selectedProjectTasks, setSelectedProjectTasks] = useState([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0
  });
  const [projectTaskPagination, setProjectTaskPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0
  });
  const [taskForm] = Form.useForm();
  const [syncForm] = Form.useForm();
  const [integrationForm] = Form.useForm();

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setLoading(true);
    try {
      await Promise.all([
        loadTasks(),
        loadProjectTasks(),
        loadStatistics()
      ]);
    } catch (error) {
      console.error('加载数据失败:', error);
      message.error('加载数据失败');
    } finally {
      setLoading(false);
    }
  };

  const loadTasks = async () => {
    try {
      console.log('🔄 开始加载钉钉待办任务...');
      const response = await dingtalkApi.getTodoTasks();
      console.log('📋 待办任务API响应:', response);
      
      if (response && response.success) {
        const tasksData = response.data?.tasks || [];
        console.log(`✅ 成功加载 ${tasksData.length} 个待办任务`);
        setTasks(tasksData);
        setPagination({
          ...pagination,
          total: response.data?.pagination?.total || tasksData.length
        });
      } else {
        console.warn('⚠️ 待办任务API返回失败:', response);
        setTasks([]);
        setPagination({
          ...pagination,
          total: 0
        });
      }
    } catch (error) {
      console.error('❌ 获取钉钉待办任务失败:', error);
      message.error('获取待办任务失败，请检查网络连接');
      setTasks([]);
      setPagination({
        ...pagination,
        total: 0
      });
    }
  };

  // 新增：加载项目系统任务
  const loadProjectTasks = async () => {
    try {
      // 先检查 apiService 结构
      console.log('apiService 结构:', apiService);
      
      // 尝试多种可能的API调用方式
      let response;
      if (apiService?.project?.task?.getList) {
        response = await apiService.project.task.getList({
          offset: (projectTaskPagination.current - 1) * projectTaskPagination.pageSize,
          limit: projectTaskPagination.pageSize,
          status: 'pending,in_progress',
        });
      } else if (apiService?.project?.tasks) {
        response = await apiService.project.tasks.getList({
          offset: (projectTaskPagination.current - 1) * projectTaskPagination.pageSize,
          limit: projectTaskPagination.pageSize,
          status: 'pending,in_progress',
        });
      } else {
        // 作为后备方案，暂时返回空列表
        console.warn('项目任务API不可用，返回空列表');
        setProjectTasks([]);
        return;
      }
      
      if (response && Array.isArray(response)) {
        setProjectTasks(response);
        setProjectTaskPagination({
          ...projectTaskPagination,
          total: response.length
        });
      } else if (response && response.data && Array.isArray(response.data)) {
        setProjectTasks(response.data);
        setProjectTaskPagination({
          ...projectTaskPagination,
          total: response.total || response.data.length
        });
      } else {
        setProjectTasks([]);
      }
    } catch (error) {
      console.error('获取项目任务失败:', error);
      setProjectTasks([]);
    }
  };

  const loadStatistics = async () => {
    try {
      // 计算钉钉任务统计
      const totalTasks = tasks.length;
      const completedTasks = tasks.filter(task => task.status === 'completed').length;
      const pendingTasks = tasks.filter(task => task.status === 'pending').length;
      const overdueTasks = tasks.filter(task => 
        task.status === 'pending' && dayjs(task.due_date).isBefore(dayjs())
      ).length;
      
      // 计算项目任务统计
      const totalProjectTasks = projectTasks.length;
      const pendingProjectTasks = projectTasks.filter(task => task.status === 'pending').length;
      const inProgressProjectTasks = projectTasks.filter(task => task.status === 'in_progress').length;
      const overdueProjectTasks = projectTasks.filter(task => 
        ['pending', 'in_progress'].includes(task.status) && 
        task.due_date && dayjs(task.due_date).isBefore(dayjs())
      ).length;
      
      // 计算同步统计
      const syncedTasks = tasks.filter(task => task.system_task_id).length;
      const unsyncedProjectTasks = projectTasks.filter(task => 
        !tasks.some(dingtalkTask => dingtalkTask.system_task_id === task.id)
      ).length;
      
      setStatistics({
        // 钉钉任务统计
        totalTasks,
        completedTasks,
        pendingTasks,
        overdueTasks,
        completionRate: totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0,
        
        // 项目任务统计
        totalProjectTasks,
        pendingProjectTasks,
        inProgressProjectTasks,
        overdueProjectTasks,
        
        // 同步统计
        syncedTasks,
        unsyncedProjectTasks,
        syncRate: totalProjectTasks > 0 ? Math.round((syncedTasks / totalProjectTasks) * 100) : 0
      });
    } catch (error) {
      console.error('计算统计信息失败:', error);
    }
  };

  // 新增：同步项目任务到钉钉
  const handleSyncProjectTasksToDingtalk = async () => {
    if (selectedProjectTasks.length === 0) {
      message.warning('请先选择要同步的项目任务');
      return;
    }

    try {
      setLoading(true);
      const syncData = {
        task_ids: selectedProjectTasks,
        sync_type: 'project_to_dingtalk'
      };
      
      const response = await dingtalkApi.syncTodoTasks(syncData);
      if (response.success) {
        message.success(`成功同步 ${selectedProjectTasks.length} 个项目任务到钉钉`);
        setSelectedProjectTasks([]);
        await loadData();
      }
    } catch (error) {
      console.error('同步项目任务失败:', error);
      message.error('同步项目任务失败');
    } finally {
      setLoading(false);
    }
  };

  // 新增：项目任务选择
  const projectTaskRowSelection = {
    selectedRowKeys: selectedProjectTasks,
    onChange: (selectedRowKeys) => {
      setSelectedProjectTasks(selectedRowKeys);
    },
    getCheckboxProps: (record) => ({
      disabled: tasks.some(task => task.system_task_id === record.id), // 已同步的任务不能重复选择
    }),
  };

  const handleCreateTask = () => {
    setEditingTask(null);
    taskForm.resetFields();
    setTaskModalVisible(true);
  };

  const handleEditTask = (task) => {
    setEditingTask(task);
    taskForm.setFieldsValue({
      ...task,
      due_date: task.due_date ? dayjs(task.due_date) : null
    });
    setTaskModalVisible(true);
  };

  const handleDeleteTask = async (taskId) => {
    try {
      setLoading(true);
      const response = await dingtalkApi.deleteTodoTask(taskId);
      if (response.success) {
        message.success('删除任务成功');
        await loadData();
      }
    } catch (error) {
      console.error('删除任务失败:', error);
      message.error('删除任务失败');
    } finally {
      setLoading(false);
    }
  };

  const handleCompleteTask = async (taskId) => {
    try {
      setLoading(true);
      const response = await dingtalkApi.completeTodoTask(taskId);
      if (response.success) {
        message.success('任务完成');
        await loadData();
      }
    } catch (error) {
      console.error('完成任务失败:', error);
      message.error('完成任务失败');
    } finally {
      setLoading(false);
    }
  };

  const handleTaskSave = async (values) => {
    try {
      setLoading(true);
      const taskData = {
        ...values,
        due_date: values.due_date ? values.due_date.format('YYYY-MM-DD HH:mm:ss') : null
      };
      
      let response;
      if (editingTask) {
        response = await dingtalkApi.updateTodoTask(editingTask.id, taskData);
      } else {
        response = await dingtalkApi.createTodoTask(taskData);
      }
      
      if (response.success) {
        message.success(`${editingTask ? '更新' : '创建'}任务成功`);
        setTaskModalVisible(false);
        taskForm.resetFields();
        await loadData();
      }
    } catch (error) {
      console.error('保存任务失败:', error);
      message.error('保存任务失败');
    } finally {
      setLoading(false);
    }
  };

  const handleSyncTasks = async (values) => {
    try {
      setLoading(true);
      const response = await dingtalkApi.syncTodoTasks(values);
      if (response.success) {
        message.success('同步任务成功');
        setSyncModalVisible(false);
        await loadData();
      }
    } catch (error) {
      console.error('同步任务失败:', error);
      message.error('同步任务失败');
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status) => {
    const colors = {
      'pending': 'orange',
      'in_progress': 'blue',
      'completed': 'green',
      'cancelled': 'red',
      'overdue': 'red'
    };
    return colors[status] || 'default';
  };

  const getStatusText = (status) => {
    const texts = {
      'pending': '待完成',
      'in_progress': '进行中',
      'completed': '已完成',
      'cancelled': '已取消',
      'overdue': '已超时'
    };
    return texts[status] || status;
  };

  const getPriorityColor = (priority) => {
    const colors = {
      'urgent': 'red',
      'high': 'volcano',
      'medium': 'orange',
      'low': 'blue'
    };
    return colors[priority] || 'default';
  };

  const getPriorityText = (priority) => {
    const texts = {
      'urgent': '紧急',
      'high': '高优先级',
      'medium': '中优先级',
      'low': '低优先级'
    };
    return texts[priority] || priority;
  };

  // 钉钉任务表格列
  const taskColumns = [
    {
      title: '任务标题',
      dataIndex: 'title',
      key: 'title',
      ellipsis: true,
      render: (text, record) => (
        <Space>
          <span>{text}</span>
          {record.system_task_id && (
            <Tooltip title="已关联项目任务">
              <LinkOutlined style={{ color: '#52c41a' }} />
            </Tooltip>
          )}
        </Space>
      ),
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
      width: 200,
    },
    {
      title: '优先级',
      dataIndex: 'priority',
      key: 'priority',
      render: (priority) => (
        <Tag color={getPriorityColor(priority)}>
          {getPriorityText(priority)}
        </Tag>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status, record) => {
        // 检查是否过期
        const isOverdue = status === 'pending' && dayjs(record.due_date).isBefore(dayjs());
        const displayStatus = isOverdue ? 'overdue' : status;
        
        return (
          <Tag color={getStatusColor(displayStatus)}>
            {getStatusText(displayStatus)}
          </Tag>
        );
      },
    },
    {
      title: '截止时间',
      dataIndex: 'due_date',
      key: 'due_date',
      render: (date) => date ? dayjs(date).format('YYYY-MM-DD HH:mm') : '-',
    },
    {
      title: '钉钉同步',
      dataIndex: 'dingtalk_record_id',
      key: 'dingtalk_sync',
      render: (recordId) => (
        <Tag color={recordId ? 'green' : 'default'}>
          {recordId ? '已同步' : '未同步'}
        </Tag>
      ),
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_, record) => (
        <Space>
          <Tooltip title="编辑">
            <Button
              type="link"
              icon={<EditOutlined />}
              onClick={() => handleEditTask(record)}
            />
          </Tooltip>
          {record.status === 'pending' && (
            <Tooltip title="完成任务">
              <Button
                type="link"
                icon={<CheckOutlined />}
                onClick={() => handleCompleteTask(record.id)}
              />
            </Tooltip>
          )}
          <Popconfirm
            title="确定要删除这个任务吗？"
            onConfirm={() => handleDeleteTask(record.id)}
          >
            <Tooltip title="删除">
              <Button
                type="link"
                danger
                icon={<DeleteOutlined />}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 项目任务表格列
  const projectTaskColumns = [
    {
      title: '任务标题',
      dataIndex: 'title',
      key: 'title',
      ellipsis: true,
      render: (text, record) => {
        const isSynced = tasks.some(task => task.system_task_id === record.id);
        return (
          <Space>
            <span>{text}</span>
            {isSynced && (
              <Tag color="green" size="small">已同步</Tag>
            )}
          </Space>
        );
      },
    },
    {
      title: '任务类型',
      dataIndex: 'task_type',
      key: 'task_type',
      render: (type) => {
        const typeMap = {
          'planning': '规划任务',
          'implementation': '实施任务',
          'review': '审核任务',
          'coordination': '协调任务',
          'reporting': '报告任务',
          'daily': '日常任务',
          'weekly': '周期任务',
          'monthly': '月度任务',
          'other': '其他任务'
        };
        return typeMap[type] || type;
      },
    },
    {
      title: '优先级',
      dataIndex: 'priority',
      key: 'priority',
      render: (priority) => (
        <Tag color={getPriorityColor(priority)}>
          {getPriorityText(priority)}
        </Tag>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status, record) => {
        const isOverdue = ['pending', 'in_progress'].includes(status) && 
                         record.due_date && dayjs(record.due_date).isBefore(dayjs());
        const displayStatus = isOverdue ? 'overdue' : status;
        
        return (
          <Tag color={getStatusColor(displayStatus)}>
            {getStatusText(displayStatus)}
          </Tag>
        );
      },
    },
    {
      title: '指派人',
      dataIndex: 'assignee_name',
      key: 'assignee_name',
      render: (name) => name || '-',
    },
    {
      title: '截止时间',
      dataIndex: 'due_date',
      key: 'due_date',
      render: (date) => date ? dayjs(date).format('YYYY-MM-DD HH:mm') : '-',
    },
  ];

  return (
    <div className="todo-task-management">
      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="钉钉待办任务"
              value={statistics.totalTasks || 0}
              prefix={<BellOutlined />}
              suffix={
                <div style={{ fontSize: '12px', color: '#666' }}>
                  完成率 {statistics.completionRate || 0}%
                </div>
              }
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="项目任务"
              value={statistics.totalProjectTasks || 0}
              prefix={<ProjectOutlined />}
              suffix={
                <div style={{ fontSize: '12px', color: '#666' }}>
                  待处理 {(statistics.pendingProjectTasks || 0) + (statistics.inProgressProjectTasks || 0)}
                </div>
              }
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="已同步任务"
              value={statistics.syncedTasks || 0}
              prefix={<SyncOutlined />}
              suffix={
                <div style={{ fontSize: '12px', color: '#666' }}>
                  同步率 {statistics.syncRate || 0}%
                </div>
              }
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="超时任务"
              value={(statistics.overdueTasks || 0) + (statistics.overdueProjectTasks || 0)}
              prefix={<ExclamationCircleOutlined />}
              valueStyle={{ color: '#cf1322' }}
            />
          </Card>
        </Col>
      </Row>

      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="钉钉待办任务" key="list">
          <Card
            title="钉钉待办任务管理"
            extra={
              <Space>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={handleCreateTask}
                >
                  创建任务
                </Button>
                <Button
                  icon={<SyncOutlined />}
                  onClick={() => setSyncModalVisible(true)}
                >
                  同步钉钉
                </Button>
              </Space>
            }
          >
            <Table
              columns={taskColumns}
              dataSource={tasks}
              rowKey="id"
              loading={loading}
              pagination={{
                ...pagination,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条记录`,
              }}
            />
          </Card>
        </TabPane>

        <TabPane 
          tab={
            <Badge count={statistics.unsyncedProjectTasks || 0} size="small">
              <span>项目任务同步</span>
            </Badge>
          } 
          key="project-tasks"
        >
          <Card
            title="项目任务列表"
            extra={
              <Space>
                <Button
                  type="primary"
                  icon={<ArrowRightOutlined />}
                  disabled={selectedProjectTasks.length === 0}
                  onClick={handleSyncProjectTasksToDingtalk}
                >
                  同步到钉钉 ({selectedProjectTasks.length})
                </Button>
                <Button
                  icon={<ReloadOutlined />}
                  onClick={() => loadProjectTasks()}
                >
                  刷新
                </Button>
              </Space>
            }
          >
            <Alert
              message="任务同步说明"
              description="选择项目任务后，可以将其同步到钉钉作为待办任务。已同步的任务会自动关联，完成状态会双向同步。"
              type="info"
              showIcon
              style={{ marginBottom: 16 }}
            />
            
            <Table
              rowSelection={projectTaskRowSelection}
              columns={projectTaskColumns}
              dataSource={projectTasks}
              rowKey="id"
              loading={loading}
              pagination={{
                ...projectTaskPagination,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条记录`,
                onChange: (page, pageSize) => {
                  setProjectTaskPagination({
                    ...projectTaskPagination,
                    current: page,
                    pageSize: pageSize
                  });
                  loadProjectTasks();
                },
              }}
            />
          </Card>
        </TabPane>

        <TabPane tab="统计概览" key="statistics">
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <Card title="钉钉任务统计">
                <Row gutter={16}>
                  <Col span={8}>
                    <Statistic title="总任务数" value={statistics.totalTasks || 0} />
                  </Col>
                  <Col span={8}>
                    <Statistic title="已完成" value={statistics.completedTasks || 0} valueStyle={{ color: '#3f8600' }} />
                  </Col>
                  <Col span={8}>
                    <Statistic title="待完成" value={statistics.pendingTasks || 0} valueStyle={{ color: '#faad14' }} />
                  </Col>
                </Row>
                <div style={{ marginTop: 16 }}>
                  <Progress
                    percent={statistics.completionRate || 0}
                    status={statistics.completionRate === 100 ? 'success' : 'active'}
                    strokeColor={{
                      '0%': '#108ee9',
                      '100%': '#87d068',
                    }}
                  />
                </div>
              </Card>
            </Col>
            
            <Col span={12}>
              <Card title="项目任务统计">
                <Row gutter={16}>
                  <Col span={8}>
                    <Statistic title="总任务数" value={statistics.totalProjectTasks || 0} />
                  </Col>
                  <Col span={8}>
                    <Statistic title="待处理" value={statistics.pendingProjectTasks || 0} valueStyle={{ color: '#faad14' }} />
                  </Col>
                  <Col span={8}>
                    <Statistic title="进行中" value={statistics.inProgressProjectTasks || 0} valueStyle={{ color: '#1890ff' }} />
                  </Col>
                </Row>
                <div style={{ marginTop: 16, textAlign: 'center' }}>
                  <div style={{ fontSize: '24px', fontWeight: 'bold', color: '#52c41a' }}>
                    {statistics.syncRate || 0}%
                  </div>
                  <div style={{ color: '#666' }}>任务同步率</div>
                </div>
              </Card>
            </Col>
          </Row>
        </TabPane>
      </Tabs>

      {/* 创建/编辑任务模态框 */}
      <Modal
        title={`${editingTask ? '编辑' : '创建'}任务`}
        open={taskModalVisible}
        onCancel={() => setTaskModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={taskForm}
          layout="vertical"
          onFinish={handleTaskSave}
        >
          <Form.Item
            name="title"
            label="任务标题"
            rules={[{ required: true, message: '请输入任务标题' }]}
          >
            <Input placeholder="请输入任务标题" />
          </Form.Item>

          <Form.Item
            name="description"
            label="任务描述"
          >
            <TextArea rows={3} placeholder="请输入任务描述" />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="priority"
                label="优先级"
                rules={[{ required: true, message: '请选择优先级' }]}
              >
                <Select placeholder="请选择优先级">
                  <Option value="urgent">紧急</Option>
                  <Option value="high">高优先级</Option>
                  <Option value="medium">中优先级</Option>
                  <Option value="low">低优先级</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="due_date"
                label="截止时间"
              >
                <DatePicker
                  showTime
                  style={{ width: '100%' }}
                  placeholder="请选择截止时间"
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="assignee"
            label="指派给"
          >
            <Input placeholder="请输入指派人员" />
          </Form.Item>

          <Form.Item style={{ textAlign: 'right', marginBottom: 0 }}>
            <Space>
              <Button onClick={() => setTaskModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit" loading={loading}>
                {editingTask ? '更新' : '创建'}
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 同步设置模态框 */}
      <Modal
        title="钉钉同步设置"
        open={syncModalVisible}
        onCancel={() => setSyncModalVisible(false)}
        footer={null}
        width={500}
      >
        <Form
          form={syncForm}
          layout="vertical"
          onFinish={handleSyncTasks}
        >
          <Form.Item
            name="sync_type"
            label="同步类型"
            rules={[{ required: true, message: '请选择同步类型' }]}
          >
            <Radio.Group>
              <Radio value="from_dingtalk">从钉钉同步到本地</Radio>
              <Radio value="to_dingtalk">从本地同步到钉钉</Radio>
              <Radio value="bidirectional">双向同步</Radio>
            </Radio.Group>
          </Form.Item>

          <Form.Item
            name="include_completed"
            label="包含已完成任务"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>

          <Form.Item style={{ textAlign: 'right', marginBottom: 0 }}>
            <Space>
              <Button onClick={() => setSyncModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit" loading={loading}>
                开始同步
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default TodoTaskManagement; 