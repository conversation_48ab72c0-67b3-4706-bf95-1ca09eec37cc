import httpClient from '../../../../../services/api/httpClient';
import apiService from '../../../../../services/api'; // 引入根api service

const getProjectId = () => localStorage.getItem('project_id');

/**
 * 全渠道运营助手服务
 */
class MultiChannelService {
  constructor() {
    this.baseUrl = '/plugins/multi-channel-operations';
  }

  // ==================== 运营总览 ====================

  /**
   * 获取运营总览数据
   */
  async getDashboardOverview() {
    const projectId = getProjectId();
    return await httpClient.get(`/project/${projectId}/plugins/multi-channel-operations/dashboard`);
  }

  /**
   * 获取渠道性能数据
   */
  async getChannelPerformance(projectId, days = 7) {
    // 临时模拟数据
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          success: true,
          data: {
            channels: [
              { channel_name: '美团外卖', sales: 15680, growth_rate: 12.5 },
              { channel_name: '抖音团购', sales: 12340, growth_rate: 8.3 },
              { channel_name: '饿了么零售', sales: 9870, growth_rate: -2.1 },
              { channel_name: '京东外卖', sales: 7650, growth_rate: 15.7 },
              { channel_name: '私域团购', sales: 5430, growth_rate: 22.1 }
            ]
          }
        });
      }, 300);
    });
    // return await apiService.get(`/project/${projectId}${this.baseUrl}/dashboard/channel-performance`, {
    //   params: { days }
    // });
  }

  /**
   * 获取操作趋势数据
   */
  async getOperationTrends(projectId, days = 30) {
    // 临时模拟数据
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          success: true,
          data: {
            trends: [
              {
                operation_type: '价格调整',
                data: [
                  { date: '2024-01-01', value: 5 },
                  { date: '2024-01-02', value: 8 },
                  { date: '2024-01-03', value: 12 },
                  { date: '2024-01-04', value: 6 },
                  { date: '2024-01-05', value: 9 }
                ]
              },
              {
                operation_type: '库存同步',
                data: [
                  { date: '2024-01-01', value: 15 },
                  { date: '2024-01-02', value: 18 },
                  { date: '2024-01-03', value: 22 },
                  { date: '2024-01-04', value: 16 },
                  { date: '2024-01-05', value: 19 }
                ]
              }
            ]
          }
        });
      }, 400);
    });
    // return await apiService.get(`/project/${projectId}${this.baseUrl}/dashboard/operation-trends`, {
    //   params: { days }
    // });
  }

  /**
   * 获取AI智能洞察
   */
  async getAiInsights(projectId) {
    // 临时模拟数据
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          success: true,
          data: {
            alerts: [
              { message: '美团外卖渠道销量较昨日上涨15%，建议增加库存' },
              { message: '竞品"XX餐厅"在抖音团购降价10%，建议关注' },
              { message: '饿了么零售渠道转化率下降，建议优化商品描述' }
            ]
          }
        });
      }, 200);
    });
    // return await apiService.get(`/project/${projectId}${this.baseUrl}/dashboard/ai-insights`);
  }

  // ==================== 渠道管理 ====================
  
  /**
   * 获取渠道列表
   */
  async getChannels(projectId, params = {}) {
    // 临时模拟数据
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          success: true,
          data: [
            {
              id: '1',
              custom_name: '上海总店美团外卖',
              platform_name: '美团',
              platform_code: 'meituan_takeout',
              service_name: '美团外卖',
              is_active: true,
              is_api_configured: true,
              last_sync_at: new Date().toISOString()
            },
            {
              id: '2',
              custom_name: '北京分店抖音团购',
              platform_name: '抖音',
              platform_code: 'douyin_group_buy',
              service_name: '抖音团购',
              is_active: true,
              is_api_configured: false,
              last_sync_at: null
            },
            {
              id: '3',
              custom_name: '深圳店饿了么零售',
              platform_name: '饿了么',
              platform_code: 'eleme_retail',
              service_name: '饿了么零售',
              is_active: false,
              is_api_configured: true,
              last_sync_at: new Date(Date.now() - 86400000).toISOString()
            }
          ]
        });
      }, 300);
    });
    // return await apiService.get(`/project/${projectId}${this.baseUrl}/channels`, {
    //   params
    // });
  }

  /**
   * 获取渠道详情
   */
  async getChannelDetail(channelId) {
    const projectId = getProjectId();
    return await httpClient.get(`/project/${projectId}/plugins/multi-channel-operations/channels/${channelId}`);
  }

  /**
   * 创建渠道配置
   */
  async createChannelConfig(data) {
    const projectId = getProjectId();
    return await httpClient.post(`/project/${projectId}/plugins/multi-channel-operations/channels`, data);
  }

  /**
   * 更新渠道配置
   */
  async updateChannelConfig(channelId, data) {
    const projectId = getProjectId();
    return await httpClient.put(`/project/${projectId}/plugins/multi-channel-operations/channels/${channelId}`, data);
  }

  /**
   * 删除渠道配置
   */
  async deleteChannelConfig(channelId) {
    const projectId = getProjectId();
    return await httpClient.delete(`/project/${projectId}/plugins/multi-channel-operations/channels/${channelId}`);
  }

  /**
   * 测试渠道API连接
   */
  async testChannelConnection(channelId) {
    const projectId = getProjectId();
    return await httpClient.post(`/project/${projectId}/plugins/multi-channel-operations/channels/${channelId}/test-connection`);
  }

  /**
   * 同步渠道数据
   */
  async syncChannelData(channelId, syncType = 'all') {
    const projectId = getProjectId();
    return await httpClient.post(`/project/${projectId}/plugins/multi-channel-operations/channels/${channelId}/sync`, { sync_type: syncType });
  }

  // ==================== 商品管理 ====================
  
  /**
   * 获取商品映射列表
   */
  async getProductMappings(params = {}) {
    const projectId = getProjectId();
    return await httpClient.get(`/project/${projectId}/plugins/multi-channel-operations/products`, { params });
  }

  /**
   * 创建商品映射
   */
  async createProductMapping(data) {
    const projectId = getProjectId();
    return await httpClient.post(`/project/${projectId}/plugins/multi-channel-operations/products`, data);
  }

  /**
   * 更新商品映射
   */
  async updateProductMapping(mappingId, data) {
    const projectId = getProjectId();
    return await httpClient.put(`/project/${projectId}/plugins/multi-channel-operations/products/${mappingId}/mapping`, data);
  }

  /**
   * 删除商品映射
   */
  async deleteProductMapping(mappingId) {
    const projectId = getProjectId();
    return await httpClient.delete(`/project/${projectId}/plugins/multi-channel-operations/product-mappings/${mappingId}`);
  }

  /**
   * 同步单个商品
   */
  async syncProduct(mappingId) {
    const projectId = getProjectId();
    return await httpClient.post(`/project/${projectId}/plugins/multi-channel-operations/product-mappings/${mappingId}/sync`);
  }

  /**
   * 批量同步商品
   */
  async batchSyncProducts(data) {
    const projectId = getProjectId();
    return await httpClient.post(`/project/${projectId}/plugins/multi-channel-operations/products/batch-sync`, data);
  }

  /**
   * 批量上架商品
   */
  async batchListProducts(data) {
    const projectId = getProjectId();
    return await httpClient.post(`/project/${projectId}/plugins/multi-channel-operations/products/batch-list`, data);
  }

  /**
   * 批量下架商品
   */
  async batchUnlistProducts(data) {
    const projectId = getProjectId();
    return await httpClient.post(`/project/${projectId}/plugins/multi-channel-operations/products/batch-unlist`, data);
  }

  // ==================== 定价策略 ====================
  
  /**
   * 获取定价策略列表
   */
  async getPricingStrategies(params = {}) {
    const projectId = getProjectId();
    return await httpClient.get(`/project/${projectId}/plugins/multi-channel-operations/pricing/strategies`, { params });
  }

  /**
   * 创建定价策略
   */
  async createPricingStrategy(data) {
    const projectId = getProjectId();
    return await httpClient.post(`/project/${projectId}/plugins/multi-channel-operations/pricing/strategies`, data);
  }

  /**
   * 更新定价策略
   */
  async updatePricingStrategy(strategyId, data) {
    const projectId = getProjectId();
    return await httpClient.put(`/project/${projectId}/plugins/multi-channel-operations/pricing/strategies/${strategyId}`, data);
  }

  /**
   * 删除定价策略
   */
  async deletePricingStrategy(strategyId) {
    const projectId = getProjectId();
    return await httpClient.delete(`/project/${projectId}/plugins/multi-channel-operations/pricing/strategies/${strategyId}`);
  }

  /**
   * 获取AI定价建议
   */
  async getAiPricingSuggestions(productId, channelId) {
    const projectId = getProjectId();
    return await httpClient.get(`/project/${projectId}/plugins/multi-channel-operations/pricing/ai-suggestions`, {
      params: { product_id: productId, channel_id: channelId }
    });
  }

  /**
   * 批量调价
   */
  async batchUpdatePrices(data) {
    const projectId = getProjectId();
    return await httpClient.post(`/project/${projectId}/plugins/multi-channel-operations/pricing/batch-update`, data);
  }

  // ==================== 竞品监控 ====================
  
  /**
   * 获取竞品列表
   */
  async getCompetitors(params = {}) {
    const projectId = getProjectId();
    return await httpClient.get(`/project/${projectId}/plugins/multi-channel-operations/competitors`, { params });
  }

  /**
   * 添加竞品监控
   */
  async addCompetitorMonitoring(data) {
    const projectId = getProjectId();
    return await httpClient.post(`/project/${projectId}/plugins/multi-channel-operations/competitors`, data);
  }

  /**
   * 更新竞品信息
   */
  async updateCompetitor(competitorId, data) {
    const projectId = getProjectId();
    return await httpClient.put(`/project/${projectId}/plugins/multi-channel-operations/competitors/${competitorId}`, data);
  }

  /**
   * 删除竞品监控
   */
  async deleteCompetitor(competitorId) {
    const projectId = getProjectId();
    return await httpClient.delete(`/project/${projectId}/plugins/multi-channel-operations/competitors/${competitorId}`);
  }

  /**
   * 获取竞品价格历史
   */
  async getCompetitorPriceHistory(competitorId, days = 30) {
    const projectId = getProjectId();
    return await httpClient.get(`/project/${projectId}/plugins/multi-channel-operations/competitors/${competitorId}/price-history`, {
      params: { days }
    });
  }

  /**
   * 手动更新竞品数据
   */
  async updateCompetitorData(competitorId) {
    const projectId = getProjectId();
    return await httpClient.post(`/project/${projectId}/plugins/multi-channel-operations/competitors/${competitorId}/update`);
  }

  // ==================== 自动化规则 ====================
  
  /**
   * 获取自动化规则列表
   */
  async getAutomationRules(params = {}) {
    const projectId = getProjectId();
    return await httpClient.get(`/project/${projectId}/plugins/multi-channel-operations/automation/rules`, { params });
  }

  /**
   * 创建自动化规则
   */
  async createAutomationRule(data) {
    const projectId = getProjectId();
    return await httpClient.post(`/project/${projectId}/plugins/multi-channel-operations/automation/rules`, data);
  }

  /**
   * 更新自动化规则
   */
  async updateAutomationRule(ruleId, data) {
    const projectId = getProjectId();
    return await httpClient.put(`/project/${projectId}/plugins/multi-channel-operations/automation/rules/${ruleId}`, data);
  }

  /**
   * 删除自动化规则
   */
  async deleteAutomationRule(ruleId) {
    const projectId = getProjectId();
    return await httpClient.delete(`/project/${projectId}/plugins/multi-channel-operations/automation/rules/${ruleId}`);
  }

  /**
   * 启用/禁用自动化规则
   */
  async toggleAutomationRule(ruleId, isActive) {
    const projectId = getProjectId();
    return await httpClient.patch(`/project/${projectId}/plugins/multi-channel-operations/automation/rules/${ruleId}/toggle`, {
      is_active: isActive
    });
  }

  /**
   * 手动执行自动化规则
   */
  async executeAutomationRule(ruleId) {
    const projectId = getProjectId();
    return await httpClient.post(`/project/${projectId}/plugins/multi-channel-operations/automation/rules/${ruleId}/execute`);
  }

  // ==================== 数据分析 ====================
  
  /**
   * 获取销售分析数据
   */
  async getSalesAnalytics(params = {}) {
    const projectId = getProjectId();
    return await httpClient.get(`/project/${projectId}/plugins/multi-channel-operations/analytics/sales`, { params });
  }

  /**
   * 获取渠道对比分析
   */
  async getChannelComparison(params = {}) {
    const projectId = getProjectId();
    return await httpClient.get(`/project/${projectId}/plugins/multi-channel-operations/analytics/channel-comparison`, { params });
  }

  /**
   * 获取商品表现分析
   */
  async getProductPerformance(params = {}) {
    const projectId = getProjectId();
    return await httpClient.get(`/project/${projectId}/plugins/multi-channel-operations/analytics/product-performance`, { params });
  }

  /**
   * 导出分析报告
   */
  async exportAnalyticsReport(reportType, params = {}) {
    const projectId = getProjectId();
    return await httpClient.get(`/project/${projectId}/plugins/multi-channel-operations/analytics/export/${reportType}`, {
      params,
      responseType: 'blob'
    });
  }

  // ==================== 插件设置 ====================
  
  /**
   * 获取插件设置
   */
  async getPluginSettings() {
    const projectId = getProjectId();
    return await httpClient.get(`/project/${projectId}/plugins/multi-channel-operations/settings`);
  }

  /**
   * 更新插件设置
   */
  async updatePluginSettings(data) {
    const projectId = getProjectId();
    return await httpClient.put(`/project/${projectId}/plugins/multi-channel-operations/settings`, data);
  }

  /**
   * 重置插件设置
   */
  async resetPluginSettings() {
    const projectId = getProjectId();
    return await httpClient.post(`/project/${projectId}/plugins/multi-channel-operations/settings/reset`);
  }

  // ==================== 新增API方法 ====================

  // 商品管理增强
  async getProducts(params = {}) {
    const projectId = getProjectId();
    return await httpClient.get(`/project/${projectId}/plugins/multi-channel-operations/products/list`, { params });
  }

  async multiChannelSync(data) {
    const projectId = getProjectId();
    return await httpClient.post(`/project/${projectId}/plugins/multi-channel-operations/products/multi-channel-sync`, data);
  }

  async generateProductImage(data) {
    const projectId = getProjectId();
    return await httpClient.post(`/project/${projectId}/plugins/multi-channel-operations/products/generate-image`, data);
  }

  async optimizeProductWithAI(data) {
    const projectId = getProjectId();
    return await httpClient.post(`/project/${projectId}/plugins/multi-channel-operations/products/ai-optimize`, data);
  }

  async batchOperation(data) {
    const projectId = getProjectId();
    return await httpClient.post(`/project/${projectId}/plugins/multi-channel-operations/products/batch-operation`, data);
  }

  // AI智能体
  async chatWithAI(data) {
    const projectId = getProjectId();
    return await httpClient.post(`/project/${projectId}/plugins/multi-channel-operations/ai-agent/chat`, data);
  }

  async getAITasks(params = {}) {
    const projectId = getProjectId();
    return await httpClient.get(`/project/${projectId}/plugins/multi-channel-operations/ai-agent/tasks`, { params });
  }

  async getAICapabilities() {
    const projectId = getProjectId();
    return await httpClient.get(`/project/${projectId}/plugins/multi-channel-operations/ai-agent/capabilities`);
  }

  async submitTaskFeedback(taskId, data) {
    const projectId = getProjectId();
    return await httpClient.post(`/project/${projectId}/plugins/multi-channel-operations/ai-agent/tasks/${taskId}/feedback`, data);
  }

  // AI图片生成
  async generateImage(data) {
    const projectId = getProjectId();
    return await httpClient.post(`/project/${projectId}/plugins/multi-channel-operations/ai-image/generate`, data);
  }

  async batchGenerateImages(data) {
    const projectId = getProjectId();
    return await httpClient.post(`/project/${projectId}/plugins/multi-channel-operations/ai-image/generate-batch`, data);
  }

  async generateChannelSpecificImage(data) {
    const projectId = getProjectId();
    return await httpClient.post(`/project/${projectId}/plugins/multi-channel-operations/ai-image/generate-channel-specific`, data);
  }

  async selectImage(data) {
    const projectId = getProjectId();
    return await httpClient.post(`/project/${projectId}/plugins/multi-channel-operations/ai-image/select-image`, data);
  }

  async getImageGenerationHistory(params = {}) {
    const projectId = getProjectId();
    return await httpClient.get(`/project/${projectId}/plugins/multi-channel-operations/ai-image/history`, { params });
  }

  async getSupportedModels() {
    const projectId = getProjectId();
    return await httpClient.get(`/project/${projectId}/plugins/multi-channel-operations/ai-image/models`);
  }

  async getImageStyles() {
    const projectId = getProjectId();
    return await httpClient.get(`/project/${projectId}/plugins/multi-channel-operations/ai-image/styles`);
  }

  async getPromptTemplates() {
    const projectId = getProjectId();
    return await httpClient.get(`/project/${projectId}/plugins/multi-channel-operations/ai-image/templates`);
  }

  // 渠道管理增强
  async getChannelConfigs(params = {}) {
    const projectId = getProjectId();
    return await httpClient.get(`/project/${projectId}/plugins/multi-channel-operations/channels/configs`, { params });
  }

  async testPlatformConnection(data) {
    const projectId = getProjectId();
    return await httpClient.post(`/project/${projectId}/plugins/multi-channel-operations/channels/test-connection`, data);
  }

  async getSupportedPlatforms() {
    const projectId = getProjectId();
    return await httpClient.get(`/project/${projectId}/plugins/multi-channel-operations/channels/supported-platforms`);
  }

  async batchTestConnections(data) {
    const projectId = getProjectId();
    return await httpClient.post(`/project/${projectId}/plugins/multi-channel-operations/channels/batch-test-connections`, data);
  }

  async toggleChannelConfig(configId) {
    const projectId = getProjectId();
    return await httpClient.post(`/project/${projectId}/plugins/multi-channel-operations/channels/configs/${configId}/toggle`);
  }

  // 类目映射
  async getCategoryMappings(params = {}) {
    const projectId = getProjectId();
    return await httpClient.get(`/project/${projectId}/plugins/multi-channel-operations/category-mappings`, { params });
  }

  async createCategoryMapping(data) {
    const projectId = getProjectId();
    return await httpClient.post(`/project/${projectId}/plugins/multi-channel-operations/category-mappings`, data);
  }

  async updateCategoryMapping(mappingId, data) {
    const projectId = getProjectId();
    return await httpClient.put(`/project/${projectId}/plugins/multi-channel-operations/category-mappings/${mappingId}`, data);
  }

  async deleteCategoryMapping(mappingId) {
    const projectId = getProjectId();
    return await httpClient.delete(`/project/${projectId}/plugins/multi-channel-operations/category-mappings/${mappingId}`);
  }

  async getLocalCategories() {
    const projectId = getProjectId();
    return await httpClient.get(`/project/${projectId}/plugins/multi-channel-operations/categories/local`);
  }

  async getPlatformCategories(channelId) {
    const projectId = getProjectId();
    return await httpClient.get(`/project/${projectId}/plugins/multi-channel-operations/categories/platform/${channelId}`);
  }

  async syncPlatformCategories(channelId) {
    const projectId = getProjectId();
    return await httpClient.post(`/project/${projectId}/plugins/multi-channel-operations/categories/sync/${channelId}`);
  }

  // 平台活动
  async getPlatformActivities(params = {}) {
    const projectId = getProjectId();
    return await httpClient.get(`/project/${projectId}/plugins/multi-channel-operations/platform-activities`, { params });
  }

  async applyPlatformActivity(data) {
    const projectId = getProjectId();
    return await httpClient.post(`/project/${projectId}/plugins/multi-channel-operations/platform-activities/apply`, data);
  }

  async syncPlatformActivities(channelId) {
    const projectId = getProjectId();
    return await httpClient.post(`/project/${projectId}/plugins/multi-channel-operations/platform-activities/sync/${channelId}`);
  }

  // 通用方法
  async getCategories() {
    const projectId = getProjectId();
    return await httpClient.get(`/project/${projectId}/plugins/multi-channel-operations/categories`);
  }
}

// 创建单例实例
const multiChannelService = new MultiChannelService();

export default multiChannelService;
