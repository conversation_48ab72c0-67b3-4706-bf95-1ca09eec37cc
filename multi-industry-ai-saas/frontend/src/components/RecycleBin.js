import React, { useState, useEffect } from 'react';
import {
  Modal,
  Table,
  Button,
  Space,
  Popconfirm,
  message,
  Typography,
  Tag,
  Tooltip,
  Empty
} from 'antd';
import {
  ReloadOutlined,
  DeleteOutlined,
  ClearOutlined,
  FileOutlined,
  QuestionCircleOutlined
} from '@ant-design/icons';
import * as projectSpaceService from '../services/api/project/space';

const { Title, Text } = Typography;

const RecycleBin = ({ visible, onClose, onRestore }) => {
  const [loading, setLoading] = useState(false);
  const [files, setFiles] = useState([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0
  });

  // 获取已删除文件列表
  const fetchDeletedFiles = async (page = 1, pageSize = 20) => {
    setLoading(true);
    try {
      const response = await projectSpaceService.getDeletedFiles({
        page,
        limit: pageSize
      });

      if (response.success) {
        setFiles(response.data.files || []);
        setPagination({
          current: page,
          pageSize: pageSize,
          total: response.data.pagination?.total || 0
        });
      }
    } catch (error) {
      console.error('获取回收站文件列表失败:', error);
      message.error('获取回收站文件列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 组件挂载时获取数据
  useEffect(() => {
    if (visible) {
      fetchDeletedFiles();
    }
  }, [visible]);

  // 还原文件
  const handleRestore = async (fileId, fileName) => {
    try {
      const response = await projectSpaceService.restoreFile(fileId);
      if (response.success) {
        message.success(`文件"${fileName}"还原成功`);
        fetchDeletedFiles(pagination.current, pagination.pageSize);
        if (onRestore) {
          onRestore();
        }
      }
    } catch (error) {
      console.error('还原文件失败:', error);
      message.error(error.response?.data?.detail || '还原文件失败');
    }
  };

  // 彻底删除文件
  const handlePermanentDelete = async (fileId, fileName) => {
    try {
      const response = await projectSpaceService.permanentDeleteFile(fileId);
      if (response.success) {
        message.success(`文件"${fileName}"已彻底删除`);
        fetchDeletedFiles(pagination.current, pagination.pageSize);
      }
    } catch (error) {
      console.error('彻底删除文件失败:', error);
      message.error(error.response?.data?.detail || '彻底删除文件失败');
    }
  };

  // 清空回收站
  const handleClearAll = async () => {
    try {
      const response = await projectSpaceService.clearRecycleBin();
      if (response.success) {
        message.success(response.message || '回收站已清空');
        fetchDeletedFiles(1, pagination.pageSize);
      }
    } catch (error) {
      console.error('清空回收站失败:', error);
      message.error(error.response?.data?.detail || '清空回收站失败');
    }
  };

  // 处理分页变化
  const handleTableChange = (paginationInfo) => {
    fetchDeletedFiles(paginationInfo.current, paginationInfo.pageSize);
  };

  // 获取文件图标
  const getFileIcon = (mimeType, category) => {
    if (category === 'image' || mimeType?.startsWith('image/')) {
      return <FileOutlined style={{ color: '#52c41a' }} />;
    } else if (category === 'document' || mimeType?.includes('pdf') || mimeType?.includes('doc') || mimeType?.includes('text')) {
      return <FileOutlined style={{ color: '#1890ff' }} />;
    } else {
      return <FileOutlined style={{ color: '#666' }} />;
    }
  };

  // 格式化文件大小
  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // 格式化日期
  const formatDate = (dateString) => {
    if (!dateString) return '-';
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN');
  };

  // 表格列定义
  const columns = [
    {
      title: '文件名',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <Space>
          {getFileIcon(record.mime_type, record.category)}
          <span>{text}</span>
        </Space>
      ),
    },
    {
      title: '文件大小',
      dataIndex: 'size',
      key: 'size',
      render: (size) => formatFileSize(size),
      width: 120,
    },
    {
      title: '文件类型',
      dataIndex: 'category',
      key: 'category',
      render: (category) => {
        const categoryMap = {
          image: { text: '图片', color: 'green' },
          document: { text: '文档', color: 'blue' },
          other: { text: '其他', color: 'default' }
        };
        const config = categoryMap[category] || categoryMap.other;
        return <Tag color={config.color}>{config.text}</Tag>;
      },
      width: 100,
    },
    {
      title: '删除时间',
      dataIndex: 'deleted_at',
      key: 'deleted_at',
      render: (date) => formatDate(date),
      width: 180,
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="还原文件">
            <Button
              type="primary"
              size="small"
              icon={<ReloadOutlined />}
              onClick={() => handleRestore(record.id, record.name)}
            />
          </Tooltip>
          <Popconfirm
            title="确定要彻底删除这个文件吗？"
            description="此操作不可撤销，文件将被永久删除。"
            onConfirm={() => handlePermanentDelete(record.id, record.name)}
            okText="确定"
            cancelText="取消"
            icon={<QuestionCircleOutlined style={{ color: 'red' }} />}
          >
            <Tooltip title="彻底删除">
              <Button
                danger
                size="small"
                icon={<DeleteOutlined />}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      ),
      width: 120,
    },
  ];

  return (
    <Modal
      title={
        <Space>
          <DeleteOutlined />
          <span>回收站</span>
        </Space>
      }
      open={visible}
      onCancel={onClose}
      width={800}
      footer={[
        <Popconfirm
          key="clear"
          title="确定要清空回收站吗？"
          description="此操作将永久删除回收站中的所有文件，不可撤销。"
          onConfirm={handleClearAll}
          okText="确定"
          cancelText="取消"
          icon={<QuestionCircleOutlined style={{ color: 'red' }} />}
        >
          <Button
            danger
            icon={<ClearOutlined />}
            disabled={files.length === 0}
          >
            清空回收站
          </Button>
        </Popconfirm>,
        <Button key="close" onClick={onClose}>
          关闭
        </Button>,
      ]}
    >
      <div style={{ marginBottom: 16 }}>
        <Text type="secondary">
          回收站中的文件可以还原到原位置，或彻底删除。彻底删除的文件无法恢复。
        </Text>
      </div>

      <Table
        columns={columns}
        dataSource={files}
        rowKey="id"
        loading={loading}
        pagination={{
          ...pagination,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total, range) =>
            `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
        }}
        onChange={handleTableChange}
        locale={{
          emptyText: (
            <Empty
              description="回收站为空"
              image={Empty.PRESENTED_IMAGE_SIMPLE}
            />
          ),
        }}
      />
    </Modal>
  );
};

export default RecycleBin; 