import traceback
from typing import Dict, Any, List
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm.attributes import flag_modified
from uuid import UUID

from api.deps import get_db, get_current_project
from core.auth import get_current_user
from models.project import Project
from models.user import User
from services.system_config import SystemConfigService
from schemas.system_config import (
    SystemConfig, SystemConfigUpdate, SystemConfigBulkUpdate,
    ThemeSettings, NotificationSettings, SecuritySettings, GeneralSettings, ThirdPartyLoginSettings, AISettings, ApiSettings
)

router = APIRouter()

@router.get("/theme", response_model=ThemeSettings)
async def get_theme_settings(
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """获取主题设置"""
    configs = await SystemConfigService.get_configs_by_type(db, project.id, "theme")

    # 如果没有配置，返回默认值
    if not configs:
        return ThemeSettings(
            mode="light",
            primaryColor="#1890ff",
            borderRadius=4,
            compactMode=False,
            customFont=False,
            fontFamily="default"
        )

    # 将配置列表转换为字典
    config_dict = {config.config_key: config.config_value for config in configs}

    # 返回主题设置
    return ThemeSettings(
        mode=config_dict.get("mode", {}).get("value", "light"),
        primaryColor=config_dict.get("primaryColor", {}).get("value", "#1890ff"),
        borderRadius=config_dict.get("borderRadius", {}).get("value", 4),
        compactMode=config_dict.get("compactMode", {}).get("value", False),
        customFont=config_dict.get("customFont", {}).get("value", False),
        fontFamily=config_dict.get("fontFamily", {}).get("value", "default")
    )

@router.post("/theme")
async def save_theme_settings(
    settings: ThemeSettings,
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """保存主题设置"""
    configs = {
        "mode": {"value": settings.mode},
        "primaryColor": {"value": settings.primaryColor},
        "borderRadius": {"value": settings.borderRadius},
        "compactMode": {"value": settings.compactMode},
        "customFont": {"value": settings.customFont},
        "fontFamily": {"value": settings.fontFamily}
    }

    await SystemConfigService.bulk_upsert_configs(db, project.id, "theme", configs)

    return {"message": "主题设置保存成功"}

@router.get("/notification", response_model=NotificationSettings)
async def get_notification_settings(
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """获取通知设置"""
    configs = await SystemConfigService.get_configs_by_type(db, project.id, "notification")

    # 如果没有配置，返回默认值
    if not configs:
        return NotificationSettings(
            emailEnabled=True,
            smsEnabled=False,
            pushEnabled=True,
            normalFrequency="realtime",
            highPriorityFrequency="realtime"
        )

    # 将配置列表转换为字典
    config_dict = {config.config_key: config.config_value for config in configs}

    # 返回通知设置
    return NotificationSettings(
        emailEnabled=config_dict.get("emailEnabled", {}).get("value", True),
        smsEnabled=config_dict.get("smsEnabled", {}).get("value", False),
        pushEnabled=config_dict.get("pushEnabled", {}).get("value", True),
        normalFrequency=config_dict.get("normalFrequency", {}).get("value", "realtime"),
        highPriorityFrequency=config_dict.get("highPriorityFrequency", {}).get("value", "realtime")
    )

@router.post("/notification")
async def save_notification_settings(
    settings: NotificationSettings,
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """保存通知设置"""
    configs = {
        "emailEnabled": {"value": settings.emailEnabled},
        "smsEnabled": {"value": settings.smsEnabled},
        "pushEnabled": {"value": settings.pushEnabled},
        "normalFrequency": {"value": settings.normalFrequency},
        "highPriorityFrequency": {"value": settings.highPriorityFrequency}
    }

    await SystemConfigService.bulk_upsert_configs(db, project.id, "notification", configs)

    return {"message": "通知设置保存成功"}

@router.get("/security", response_model=SecuritySettings)
async def get_security_settings(
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """获取安全设置"""
    configs = await SystemConfigService.get_configs_by_type(db, project.id, "security")

    # 如果没有配置，返回默认值
    if not configs:
        return SecuritySettings(
            passwordExpiration=90,
            twoFactorAuth=False,
            loginAttempts=5,
            sessionTimeout=30,
            ipRestriction=False
        )

    # 将配置列表转换为字典
    config_dict = {config.config_key: config.config_value for config in configs}

    # 返回安全设置
    return SecuritySettings(
        passwordExpiration=config_dict.get("passwordExpiration", {}).get("value", 90),
        twoFactorAuth=config_dict.get("twoFactorAuth", {}).get("value", False),
        loginAttempts=config_dict.get("loginAttempts", {}).get("value", 5),
        sessionTimeout=config_dict.get("sessionTimeout", {}).get("value", 30),
        ipRestriction=config_dict.get("ipRestriction", {}).get("value", False)
    )

@router.post("/security")
async def save_security_settings(
    settings: SecuritySettings,
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """保存安全设置"""
    configs = {
        "passwordExpiration": {"value": settings.passwordExpiration},
        "twoFactorAuth": {"value": settings.twoFactorAuth},
        "loginAttempts": {"value": settings.loginAttempts},
        "sessionTimeout": {"value": settings.sessionTimeout},
        "ipRestriction": {"value": settings.ipRestriction}
    }

    await SystemConfigService.bulk_upsert_configs(db, project.id, "security", configs)

    return {"message": "安全设置保存成功"}

@router.get("/general", response_model=GeneralSettings)
async def get_general_settings(
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """获取常规设置"""
    configs = await SystemConfigService.get_configs_by_type(db, project.id, "general")

    # 如果没有配置，返回默认值
    if not configs:
        return GeneralSettings(
            language="zh_CN",
            timezone="Asia/Shanghai",
            dateFormat="YYYY-MM-DD",
            timeFormat="24hour"
        )

    # 将配置列表转换为字典
    config_dict = {config.config_key: config.config_value for config in configs}

    # 返回常规设置
    return GeneralSettings(
        language=config_dict.get("language", {}).get("value", "zh_CN"),
        timezone=config_dict.get("timezone", {}).get("value", "Asia/Shanghai"),
        dateFormat=config_dict.get("dateFormat", {}).get("value", "YYYY-MM-DD"),
        timeFormat=config_dict.get("timeFormat", {}).get("value", "24hour")
    )

@router.post("/general")
async def save_general_settings(
    settings: GeneralSettings,
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """保存常规设置"""
    configs = {
        "language": {"value": settings.language},
        "timezone": {"value": settings.timezone},
        "dateFormat": {"value": settings.dateFormat},
        "timeFormat": {"value": settings.timeFormat}
    }

    await SystemConfigService.bulk_upsert_configs(db, project.id, "general", configs)

    return {"message": "常规设置保存成功"}

@router.get("/third_party_login", response_model=ThirdPartyLoginSettings)
@router.get("/third-party-login", response_model=ThirdPartyLoginSettings)  # 添加短划线别名
async def get_third_party_login_settings(
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """获取第三方登录设置"""
    configs = await SystemConfigService.get_configs_by_type(db, project.id, "third_party_login")

    # 如果没有配置，返回默认值
    if not configs:
        return ThirdPartyLoginSettings(
            wechat_work={
                "enabled": False,
                "corp_id": "",
                "agent_id": "",
                "secret": "",
                "redirect_uri": ""
            },
            dingtalk={
                "enabled": False,
                "app_key": "",
                "app_secret": "",
                "corp_id": "",
                "redirect_uri": ""
            },
            feishu={
                "enabled": False,
                "app_id": "",
                "app_secret": "",
                "redirect_uri": ""
            },
            wechat={
                "enabled": False,
                "app_id": "",
                "app_secret": "",
                "redirect_uri": ""
            }
        )

    # 将配置列表转换为字典
    config_dict = {config.config_key: config.config_value for config in configs}

    # 返回第三方登录设置
    def get_config_value(config_data, default_value):
        """获取配置值，兼容不同的数据结构"""
        if isinstance(config_data, dict):
            # 如果有value字段，使用value字段的值
            if "value" in config_data:
                return config_data["value"]
            # 否则直接使用配置数据
            return config_data
        return default_value

    return ThirdPartyLoginSettings(
        wechat_work=get_config_value(config_dict.get("wechat_work", {}), {
            "enabled": False,
            "corp_id": "",
            "agent_id": "",
            "secret": "",
            "redirect_uri": ""
        }),
        dingtalk=get_config_value(config_dict.get("dingtalk", {}), {
            "enabled": False,
            "app_key": "",
            "app_secret": "",
            "corp_id": "",
            "redirect_uri": ""
        }),
        feishu=get_config_value(config_dict.get("feishu", {}), {
            "enabled": False,
            "app_id": "",
            "app_secret": "",
            "redirect_uri": ""
        }),
        wechat=get_config_value(config_dict.get("wechat", {}), {
            "enabled": False,
            "app_id": "",
            "app_secret": "",
            "redirect_uri": ""
        })
    )

@router.post("/third_party_login")
async def save_third_party_login_settings(
    settings: ThirdPartyLoginSettings,
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """保存第三方登录设置"""
    configs = {
        "wechat_work": {"value": settings.wechat_work},
        "dingtalk": {"value": settings.dingtalk},
        "feishu": {"value": settings.feishu},
        "wechat": {"value": settings.wechat}
    }

    await SystemConfigService.bulk_upsert_configs(db, project.id, "third_party_login", configs)

    return {"message": "第三方登录设置保存成功"}

@router.get("/ai", response_model=AISettings)
async def get_ai_settings(
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """获取AI设置"""
    configs = await SystemConfigService.get_configs_by_type(db, project.id, "ai")

    # 如果没有配置，返回默认值
    if not configs:
        return AISettings(
            enabled=True,
            cache_duration="1hour",
            insights_enabled=True,
            suggestions_enabled=True,
            auto_analysis=False,
            analysis_frequency="daily",
            max_requests_per_hour=100,
            enable_data_learning=False,
            privacy_mode=True
        )

    # 将配置列表转换为字典
    config_dict = {config.config_key: config.config_value for config in configs}

    # 返回AI设置
    return AISettings(
        enabled=config_dict.get("enabled", {}).get("value", True),
        cache_duration=config_dict.get("cache_duration", {}).get("value", "1hour"),
        insights_enabled=config_dict.get("insights_enabled", {}).get("value", True),
        suggestions_enabled=config_dict.get("suggestions_enabled", {}).get("value", True),
        auto_analysis=config_dict.get("auto_analysis", {}).get("value", False),
        analysis_frequency=config_dict.get("analysis_frequency", {}).get("value", "daily"),
        max_requests_per_hour=config_dict.get("max_requests_per_hour", {}).get("value", 100),
        enable_data_learning=config_dict.get("enable_data_learning", {}).get("value", False),
        privacy_mode=config_dict.get("privacy_mode", {}).get("value", True)
    )

@router.post("/ai")
async def save_ai_settings(
    settings: AISettings,
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """保存AI设置"""
    configs = {
        "enabled": {"value": settings.enabled},
        "cache_duration": {"value": settings.cache_duration},
        "insights_enabled": {"value": settings.insights_enabled},
        "suggestions_enabled": {"value": settings.suggestions_enabled},
        "auto_analysis": {"value": settings.auto_analysis},
        "analysis_frequency": {"value": settings.analysis_frequency},
        "max_requests_per_hour": {"value": settings.max_requests_per_hour},
        "enable_data_learning": {"value": settings.enable_data_learning},
        "privacy_mode": {"value": settings.privacy_mode}
    }

    await SystemConfigService.bulk_upsert_configs(db, project.id, "ai", configs)

    return {"message": "AI设置保存成功"}

@router.get("/api", response_model=ApiSettings)
async def get_api_settings(
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """获取API接口设置"""
    configs = await SystemConfigService.get_configs_by_type(db, project.id, "api")
    if not configs:
        return ApiSettings()

    config_dict = {config.config_key: config.config_value for config in configs}
    return ApiSettings(
        weather=config_dict.get("weather", {}),
        gaode=config_dict.get("gaode", {})
    )

@router.post("/api")
async def save_api_settings(
    settings: ApiSettings,
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """保存API接口设置"""
    configs = {
        "weather": settings.weather,
        "gaode": settings.gaode,
    }
    await SystemConfigService.bulk_upsert_configs(db, project.id, "api", configs)
    return {"message": "API接口设置保存成功"}

@router.get("", response_model=Dict[str, Any])
async def get_system_config_overview(
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """
    获取系统配置总览
    """
    try:
        theme = await SystemConfigService.get_configs_by_type(db, project.id, "theme")
        notification = await SystemConfigService.get_configs_by_type(db, project.id, "notification")
        security = await SystemConfigService.get_configs_by_type(db, project.id, "security")
        general = await SystemConfigService.get_configs_by_type(db, project.id, "general")
        third_party_login = await SystemConfigService.get_configs_by_type(db, project.id, "third_party_login")
        ai = await SystemConfigService.get_configs_by_type(db, project.id, "ai")
        api_settings_raw = await SystemConfigService.get_configs_by_type(db, project.id, "api")

        # 将 api_settings_raw 转换为字典
        api_settings = {c.config_key: c.config_value for c in api_settings_raw}
        
        # 确保 weather 和 gaode key 存在
        if 'weather' not in api_settings:
            api_settings['weather'] = {"apiKey": "", "apiHost": "https://devapi.qweather.com"}
        if 'gaode' not in api_settings:
            api_settings['gaode'] = {"apiKey": "", "apiSecret": ""}

        return {
            "theme": {c.config_key: c.config_value for c in theme},
            "notification": {c.config_key: c.config_value for c in notification},
            "security": {c.config_key: c.config_value for c in security},
            "general": {c.config_key: c.config_value for c in general},
            "third_party_login": {c.config_key: c.config_value for c in third_party_login},
            "ai": {c.config_key: c.config_value for c in ai},
            "api": api_settings,
        }
    except Exception as e:
        logger.error(f"获取系统配置失败: {e}", exc_info=True)
        return {"error": str(e), "trace": traceback.format_exc()}

@router.put("", response_model=Dict[str, Any])
async def update_system_config_overview(
    config_data: Dict[str, Any],
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """
    批量更新系统配置
    """
    try:
        updated_configs = {}

        # 处理各个配置类型
        for config_type, configs in config_data.items():
            if config_type in ["theme", "notification", "security", "general", "third_party_login", "ai", "api"]:
                if configs:  # 只有当配置不为空时才更新
                    await SystemConfigService.bulk_upsert_configs(db, project.id, config_type, configs)
                    updated_configs[config_type] = "updated"
                else:
                    updated_configs[config_type] = "skipped"
        
        await db.commit()

        return {
            "message": "系统配置更新成功",
            "updated": updated_configs
        }
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=500,
            detail={"error": str(e), "trace": traceback.format_exc()}
        )
