#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
令牌路由

负责管理API令牌
"""

import logging
import uuid
import secrets
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Body, Query, Path
from sqlalchemy import and_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from pydantic import BaseModel

from db.database import get_db
from models.user import User
from core.auth import get_current_user

from ..models import SAPIToken
from .. import router
from ..auth import create_jwt_token, TokenType

logger = logging.getLogger(__name__)

# 生成令牌
def generate_token():
    """生成随机令牌"""
    return secrets.token_urlsafe(32)

# 获取令牌列表
@router.get("/tokens", response_model=Dict[str, Any])
async def get_tokens(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """获取令牌列表"""
    try:
        # 查询用户的令牌
        result = await db.execute(select(SAPIToken).where(SAPIToken.user_id == current_user.id))
        tokens = result.scalars().all()

        # 构建响应
        result = []
        for token in tokens:
            result.append({
                "id": str(token.id),
                "name": token.name,
                "description": token.description,
                "token": token.token[:8] + "..." + token.token[-8:],  # 只显示部分令牌
                "token_preview": token.token[:8] + "..." + token.token[-8:],  # 用于显示的令牌预览
                "permissions": token.permissions,
                "token_type": token.token_type,  # 添加令牌类型字段
                "status": token.status,
                "is_active": token.status == "active",  # 添加 is_active 字段
                "created_at": token.created_at.isoformat() if token.created_at else None,
                "last_used": token.last_used.isoformat() if token.last_used else None,
                "expires_at": token.expires_at.isoformat() if token.expires_at else None,
                "project_id": str(token.project_id) if token.project_id else None,  # 添加项目ID字段
                "server_permissions": token.server_permissions  # 添加服务器权限映射字段
            })

        return {
            "success": True,
            "message": "获取令牌列表成功",
            "data": result
        }
    except Exception as e:
        logger.error(f"获取令牌列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取令牌列表失败: {str(e)}")

# 创建令牌
@router.post("/tokens", response_model=Dict[str, Any])
async def create_token(
    token_data: dict = Body(...),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """创建令牌"""
    try:
        # 获取令牌类型，默认为 access
        token_type = token_data.get("token_type", "access")
        # 验证令牌类型是否有效
        if token_type not in ["access", "mcp"]:
            raise HTTPException(status_code=400, detail="无效的令牌类型，支持的类型有：access, mcp")

        token_value = generate_token()

        # 计算过期时间
        expires_at = None
        if token_data.get("expires_days"):
            expires_at = datetime.now() + timedelta(days=token_data.get("expires_days"))

        # 根据令牌类型处理不同的逻辑
        token_id = uuid.uuid4()

        if token_type == "mcp":
            # 对于MCP令牌，需要验证必要的参数
            project_id = token_data.get("project_id")
            if not project_id:
                raise HTTPException(status_code=400, detail="创建MCP令牌需要提供项目ID")

            server_id = token_data.get("server_id")
            if not server_id:
                raise HTTPException(status_code=400, detail="创建MCP令牌需要提供服务器ID")

            # 获取服务器权限映射
            server_permissions = token_data.get("server_permissions", {})
            if not server_permissions:
                # 如果没有指定服务器权限映射，则使用默认权限
                permissions = token_data.get("permissions", "read").split(",")
                server_permissions = {server_id: permissions}

            # 获取默认权限
            default_permissions = token_data.get("default_permissions", ["read"])
            if isinstance(default_permissions, str):
                default_permissions = default_permissions.split(",")

            # 计算过期时间（小时）
            expires_hours = token_data.get("expires_hours", 24)
            if expires_at:
                # 如果已经设置了expires_at，计算小时数
                expires_hours = int((expires_at - datetime.now()).total_seconds() / 3600)

            # 生成MCP令牌
            token_value = create_jwt_token(
                subject=str(current_user.id),
                token_type=TokenType.MCP,
                expires_delta=timedelta(hours=expires_hours),
                scopes=["mcp"],
                project_id=project_id,
                server_permissions=server_permissions,
                default_permissions=default_permissions,
                user_id=str(current_user.id),
                jti=str(token_id)
            )

            # 如果没有设置expires_at，根据expires_hours计算
            if not expires_at:
                expires_at = datetime.now() + timedelta(hours=expires_hours)
        else:
            # 对于access令牌，使用简单的生成方式
            if not expires_at and token_data.get("expires_minutes"):
                expires_at = datetime.now() + timedelta(minutes=token_data.get("expires_minutes"))

        # 创建令牌记录
        new_token = SAPIToken(
            id=token_id,
            name=token_data.get("name"),
            description=token_data.get("description"),
            token=token_value,
            user_id=current_user.id,
            permissions=token_data.get("permissions", "read"),
            token_type=token_type,
            server_permissions=token_data.get("server_permissions"),
            default_permissions=token_data.get("default_permissions"),
            project_id=token_data.get("project_id"),
            status="active",
            created_at=datetime.now(),
            expires_at=expires_at
        )

        db.add(new_token)
        await db.commit()
        await db.refresh(new_token)

        return {
            "success": True,
            "message": "令牌创建成功",
            "data": {
                "id": str(new_token.id),
                "name": new_token.name,
                "token": new_token.token,  # 返回完整令牌
                "permissions": new_token.permissions,
                "token_type": new_token.token_type,
                "expires_at": new_token.expires_at.isoformat() if new_token.expires_at else None
            }
        }
    except Exception as e:
        await db.rollback()
        logger.error(f"创建令牌失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"创建令牌失败: {str(e)}")

# 撤销令牌
@router.post("/tokens/{token_id}/revoke", response_model=Dict[str, Any])
async def revoke_token(
    token_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """撤销令牌"""
    try:
        # 查询令牌
        result = await db.execute(
            select(SAPIToken).where(
                and_(
                    SAPIToken.id == token_id,
                    SAPIToken.user_id == current_user.id
                )
            )
        )
        token = result.scalars().first()

        if not token:
            raise HTTPException(status_code=404, detail="令牌不存在")

        # 撤销令牌
        token.status = "revoked"
        await db.commit()

        return {
            "success": True,
            "message": "令牌撤销成功"
        }
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"撤销令牌失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"撤销令牌失败: {str(e)}")

# 更新令牌状态
@router.put("/tokens/{token_id}/status", response_model=Dict[str, Any])
async def update_token_status(
    token_id: str,
    status_data: dict = Body(...),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """更新令牌状态"""
    try:
        # 查询令牌
        result = await db.execute(
            select(SAPIToken).where(
                and_(
                    SAPIToken.id == token_id,
                    SAPIToken.user_id == current_user.id
                )
            )
        )
        token = result.scalars().first()

        if not token:
            raise HTTPException(status_code=404, detail="令牌不存在")

        # 更新令牌状态
        is_active = status_data.get("is_active", True)
        token.status = "active" if is_active else "inactive"
        await db.commit()

        return {
            "success": True,
            "message": f"令牌状态更新成功: {'启用' if is_active else '禁用'}"
        }
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"更新令牌状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"更新令牌状态失败: {str(e)}")

# 获取令牌详情
@router.get("/tokens/{token_id}", response_model=Dict[str, Any])
async def get_token_detail(
    token_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """获取令牌详情"""
    try:
        # 查询令牌
        result = await db.execute(
            select(SAPIToken).where(
                and_(
                    SAPIToken.id == token_id,
                    SAPIToken.user_id == current_user.id
                )
            )
        )
        token = result.scalars().first()

        if not token:
            raise HTTPException(status_code=404, detail="令牌不存在")

        # 构建响应
        token_data = {
            "id": str(token.id),
            "name": token.name,
            "description": token.description,
            "token": token.token,  # 返回完整令牌
            "permissions": token.permissions,
            "server_permissions": token.server_permissions,
            "default_permissions": token.default_permissions,
            "token_type": token.token_type,
            "project_id": str(token.project_id) if token.project_id else None,
            "status": token.status,
            "is_active": token.status == "active",
            "created_at": token.created_at.isoformat() if token.created_at else None,
            "last_used": token.last_used.isoformat() if token.last_used else None,
            "expires_at": token.expires_at.isoformat() if token.expires_at else None
        }

        return {
            "success": True,
            "message": "获取令牌详情成功",
            "data": token_data
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取令牌详情失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取令牌详情失败: {str(e)}")

# 物理删除令牌
@router.delete("/tokens/{token_id}", response_model=Dict[str, Any])
async def delete_token(
    token_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """物理删除令牌"""
    try:
        result = await db.execute(
            select(SAPIToken).where(
                and_(
                    SAPIToken.id == token_id,
                    SAPIToken.user_id == current_user.id
                )
            )
        )
        token = result.scalars().first()
        if not token:
            raise HTTPException(status_code=404, detail="令牌不存在")
        await db.delete(token)
        await db.commit()
        return {
            "success": True,
            "message": "令牌已删除"
        }
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"删除令牌失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除令牌失败: {str(e)}")
