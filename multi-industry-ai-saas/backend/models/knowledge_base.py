#!/usr/bin/env python
# -*- coding: utf-8 -*-

import uuid
from sqlalchemy import Column, String, Text, Boolean, ForeignKey, JSON, DateTime, func, Integer

from sqlalchemy.dialects.postgresql import UUID, ARRAY
from sqlalchemy.orm import relationship
from db.base_class import Base

class KnowledgeCategory(Base):
    """
    知识库分类
    """
    __tablename__ = "knowledge_categories"

    # 确保表名正确
    __table_args__ = {'extend_existing': True}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id", ondelete="CASCADE"), nullable=False)
    parent_id = Column(UUID(as_uuid=True), ForeignKey("knowledge_categories.id", ondelete="SET NULL"), nullable=True)
    name = Column(String(100), nullable=False)
    description = Column(Text, nullable=True)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    # 使用延迟加载定义关系，避免循环导入
    # 使用纯字符串形式的 primaryjoin 表达式
    # 使用纯表名和列名的字符串
    project = relationship(
        "Project",
        primaryjoin="knowledge_categories.c.project_id == projects.c.id",
        backref="knowledge_categories",
        lazy="joined",
        post_update=True  # 避免循环依赖
    )

    # 自引用关系
    parent = relationship(
        "KnowledgeCategory",
        remote_side=[id],
        backref="children"
    )

    # 使用纯表名和列名的字符串
    documents = relationship(
        "KnowledgeDocument",
        primaryjoin="knowledge_categories.c.id == knowledge_documents.c.category_id",
        back_populates="category",
        cascade="all, delete-orphan",
        lazy="dynamic"
    )

class KnowledgeDocument(Base):
    """
    知识库文档
    """
    __tablename__ = "knowledge_documents"

    # 确保表名正确
    __table_args__ = {'extend_existing': True}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id", ondelete="CASCADE"), nullable=False)
    category_id = Column(UUID(as_uuid=True), ForeignKey("knowledge_categories.id", ondelete="SET NULL"), nullable=True)
    title = Column(String(200), nullable=False)
    description = Column(Text, nullable=True)
    content = Column(Text, nullable=True)
    file_url = Column(String(500), nullable=True)
    file_name = Column(String(200), nullable=True)
    file_type = Column(String(50), nullable=True)
    file_size = Column(Integer, nullable=True)
    is_public = Column(Boolean, default=True)
    store_ids = Column(ARRAY(UUID(as_uuid=True)), nullable=True)
    document_metadata = Column(JSON, nullable=True)  # 改名为 document_metadata 避免与 SQLAlchemy 保留字冲突
    created_by = Column(UUID(as_uuid=True), ForeignKey("users.id", ondelete="SET NULL"), nullable=True)
    updated_by = Column(UUID(as_uuid=True), ForeignKey("users.id", ondelete="SET NULL"), nullable=True)
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())

    # 使用延迟加载定义关系，避免循环导入
    # 使用纯字符串形式的 primaryjoin 表达式
    # 使用纯表名和列名的字符串
    project = relationship(
        "Project",
        primaryjoin="knowledge_documents.c.project_id == projects.c.id",
        backref="knowledge_documents",
        lazy="joined",
        post_update=True  # 避免循环依赖
    )

    # 使用纯表名和列名的字符串
    category = relationship(
        "KnowledgeCategory",
        primaryjoin="knowledge_documents.c.category_id == knowledge_categories.c.id",
        back_populates="documents",
        lazy="joined"
    )

    # User 关系在 User 模型中定义，这里不需要重复定义

# 使用 post_update=True 解决循环导入问题