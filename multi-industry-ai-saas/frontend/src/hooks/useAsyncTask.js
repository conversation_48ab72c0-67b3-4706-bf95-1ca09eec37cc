/**
 * 异步任务 React Hook
 * 简化异步任务的状态管理和UI更新
 */

import { useState, useEffect, useCallback, useRef } from 'react';
import asyncTaskService from '../services/AsyncTaskService';

export const useAsyncTask = (taskId = null, options = {}) => {
  const {
    autoStart = true,
    interval = 5000,
    onComplete = null,
    onError = null,
    onProgress = null,
  } = options;

  const [taskInfo, setTaskInfo] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const taskIdRef = useRef(taskId);

  // 更新任务状态的回调
  const handleTaskUpdate = useCallback((updatedTaskInfo) => {
    setTaskInfo(updatedTaskInfo);
    
    // 触发进度回调
    if (onProgress) {
      onProgress(updatedTaskInfo);
    }

    // 如果任务完成
    if (updatedTaskInfo.status === 'success') {
      setLoading(false);
      setError(null);
      if (onComplete) {
        onComplete(updatedTaskInfo);
      }
    } else if (['failed', 'cancelled'].includes(updatedTaskInfo.status)) {
      setLoading(false);
      const errorMsg = updatedTaskInfo.error_message || '任务执行失败';
      setError(errorMsg);
      if (onError) {
        onError(new Error(errorMsg), updatedTaskInfo);
      }
    } else if (updatedTaskInfo.status === 'running') {
      setLoading(true);
      setError(null);
    }
  }, [onComplete, onError, onProgress]);

  // 开始监控任务
  const startTask = useCallback((newTaskId) => {
    const targetTaskId = newTaskId || taskIdRef.current;
    if (!targetTaskId) return;

    setLoading(true);
    setError(null);
    taskIdRef.current = targetTaskId;
    
    asyncTaskService.startPolling(targetTaskId, handleTaskUpdate, interval);
  }, [handleTaskUpdate, interval]);

  // 停止监控任务
  const stopTask = useCallback(() => {
    if (taskIdRef.current) {
      asyncTaskService.stopPolling(taskIdRef.current);
      setLoading(false);
    }
  }, []);

  // 取消任务
  const cancelTask = useCallback(async () => {
    if (taskIdRef.current) {
      try {
        await asyncTaskService.cancelTask(taskIdRef.current);
        stopTask();
      } catch (err) {
        console.error('取消任务失败:', err);
        setError('取消任务失败');
      }
    }
  }, [stopTask]);

  // 重试任务（重新获取状态）
  const retryTask = useCallback(async () => {
    if (taskIdRef.current) {
      try {
        setLoading(true);
        setError(null);
        const info = await asyncTaskService.getTaskStatus(taskIdRef.current);
        handleTaskUpdate(info);
        
        // 如果任务还在运行，开始轮询
        if (['pending', 'running'].includes(info.status)) {
          asyncTaskService.startPolling(taskIdRef.current, handleTaskUpdate, interval);
        }
      } catch (err) {
        console.error('重试任务失败:', err);
        setError('重试任务失败');
        setLoading(false);
      }
    }
  }, [handleTaskUpdate, interval]);

  // 自动开始任务监控
  useEffect(() => {
    if (autoStart && taskId) {
      startTask(taskId);
    }

    // 清理函数
    return () => {
      if (taskIdRef.current) {
        asyncTaskService.stopPolling(taskIdRef.current);
      }
    };
  }, [taskId, autoStart, startTask]);

  // 计算派生状态
  const isCompleted = taskInfo?.status === 'success';
  const isFailed = ['failed', 'cancelled'].includes(taskInfo?.status);
  const isRunning = ['pending', 'running'].includes(taskInfo?.status);
  const progress = taskInfo?.progress || 0;

  return {
    // 状态数据
    taskInfo,
    loading,
    error,
    
    // 派生状态
    isCompleted,
    isFailed,
    isRunning,
    progress,
    
    // 操作方法
    startTask,
    stopTask,
    cancelTask,
    retryTask,
  };
};

/**
 * 用于执行新任务的Hook
 */
export const useTaskExecution = (options = {}) => {
  const { onComplete, onError, onProgress } = options;
  
  const [executingTasks, setExecutingTasks] = useState(new Map());

  // 执行任务
  const executeTask = useCallback(async (taskFunction, taskKey = null) => {
    const key = taskKey || `task_${Date.now()}`;
    
    try {
      // 设置任务为执行中
      setExecutingTasks(prev => new Map(prev).set(key, {
        status: 'starting',
        taskId: null,
        startTime: new Date(),
      }));

      // 执行任务函数，应返回任务ID
      const taskId = await taskFunction();
      
      // 更新任务信息
      setExecutingTasks(prev => new Map(prev).set(key, {
        status: 'running',
        taskId,
        startTime: prev.get(key)?.startTime || new Date(),
      }));

      // 开始监控任务
      const handleUpdate = (taskInfo) => {
        setExecutingTasks(prev => {
          const newMap = new Map(prev);
          const currentTask = newMap.get(key);
          if (currentTask) {
            newMap.set(key, {
              ...currentTask,
              status: taskInfo.status,
              progress: taskInfo.progress,
              result: taskInfo.result,
              error: taskInfo.error_message,
            });
          }
          return newMap;
        });

        if (onProgress) {
          onProgress(taskInfo, key);
        }

        if (taskInfo.status === 'success') {
          if (onComplete) {
            onComplete(taskInfo, key);
          }
          // 延迟清理任务状态
          setTimeout(() => {
            setExecutingTasks(prev => {
              const newMap = new Map(prev);
              newMap.delete(key);
              return newMap;
            });
          }, 2000);
        } else if (['failed', 'cancelled'].includes(taskInfo.status)) {
          if (onError) {
            onError(new Error(taskInfo.error_message || '任务执行失败'), taskInfo, key);
          }
          // 延迟清理任务状态
          setTimeout(() => {
            setExecutingTasks(prev => {
              const newMap = new Map(prev);
              newMap.delete(key);
              return newMap;
            });
          }, 5000);
        }
      };

      asyncTaskService.startPolling(taskId, handleUpdate);
      
      return taskId;
    } catch (error) {
      console.error('执行任务失败:', error);
      setExecutingTasks(prev => {
        const newMap = new Map(prev);
        newMap.set(key, {
          status: 'failed',
          error: error.message,
          startTime: prev.get(key)?.startTime || new Date(),
        });
        return newMap;
      });
      
      if (onError) {
        onError(error, null, key);
      }
      
      throw error;
    }
  }, [onComplete, onError, onProgress]);

  // 取消任务
  const cancelTask = useCallback(async (taskKey) => {
    const task = executingTasks.get(taskKey);
    if (task?.taskId) {
      try {
        await asyncTaskService.cancelTask(task.taskId);
        asyncTaskService.stopPolling(task.taskId);
      } catch (error) {
        console.error('取消任务失败:', error);
      }
    }
    
    setExecutingTasks(prev => {
      const newMap = new Map(prev);
      newMap.delete(taskKey);
      return newMap;
    });
  }, [executingTasks]);

  // 清理所有任务
  const clearAllTasks = useCallback(() => {
    executingTasks.forEach((task) => {
      if (task.taskId) {
        asyncTaskService.stopPolling(task.taskId);
      }
    });
    setExecutingTasks(new Map());
  }, [executingTasks]);

  return {
    executingTasks,
    executeTask,
    cancelTask,
    clearAllTasks,
  };
}; 