import React, { useState, useEffect } from 'react';
import {
  Card, Table, Button, Space, Modal, Form, Input, Select, 
  InputNumber, Upload, message, Tabs, Row, Col, Statistic,
  DatePicker, Popconfirm, Tag, Divider, Alert, Typography,
  Tooltip, Progress, Empty, Transfer, Checkbox, Radio
} from 'antd';
import {
  PlusOutlined, UploadOutlined, DownloadOutlined, EditOutlined,
  DeleteOutlined, EyeOutlined, CheckCircleOutlined, CloseCircleOutlined,
  FileExcelOutlined, SearchOutlined, FilterOutlined, ReloadOutlined,
  BarChartOutlined, PieChartOutlined, ExclamationCircleOutlined,
  AppstoreAddOutlined, InboxOutlined
} from '@ant-design/icons';
import dayjs from 'dayjs';
import dateTimeUtils from '../../../../utils/dateTimeUtils';
import apiService from '../../../../services/api';
import { logOperation } from '../../../../utils/logUtils';

const { TabPane } = Tabs;
const { Option } = Select;
const { TextArea } = Input;
const { Title, Text } = Typography;

/**
 * 月度盘点组件
 * 提供盘点录入、盘点表上传、数据展示等功能
 */
const MonthlyInventory = ({ stores, currentStoreId, onStoreChange, loading }) => {
  const [activeTab, setActiveTab] = useState('entry');
  const [inventoryList, setInventoryList] = useState([]);
  const [productList, setProductList] = useState([]);
  const [categoryList, setCategoryList] = useState([]);
  const [inventoryLoading, setInventoryLoading] = useState(false);
  const [entryModalVisible, setEntryModalVisible] = useState(false);
  const [batchEntryModalVisible, setBatchEntryModalVisible] = useState(false);
  const [uploadModalVisible, setUploadModalVisible] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [editingInventory, setEditingInventory] = useState(null);
  const [selectedInventory, setSelectedInventory] = useState(null);
  const [submitting, setSubmitting] = useState(false);
  const [uploadFileList, setUploadFileList] = useState([]);
  const [batchProducts, setBatchProducts] = useState([]);
  const [selectedProducts, setSelectedProducts] = useState([]);
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewData, setPreviewData] = useState([]);
  const [fileId, setFileId] = useState(null);
  const [importStats, setImportStats] = useState({});
  const [filters, setFilters] = useState({
    month: dayjs().format('YYYY-MM'),
    category: null,
    status: null
  });
  const [statistics, setStatistics] = useState({
    total: 0,
    completed: 0,
    pending: 0,
    submitted: 0
  });

  const [entryForm] = Form.useForm();
  const [batchForm] = Form.useForm();
  const [uploadForm] = Form.useForm();

  // 初始化时间工具
  useEffect(() => {
    dateTimeUtils.initDateTimeUtils();
  }, []);

  // 获取盘点列表
  const fetchInventoryList = async () => {
    if (!currentStoreId) return;
    
    setInventoryLoading(true);
    try {
      const params = {
        store_id: currentStoreId,
        month: filters.month,
        category: filters.category,
        status: filters.status
      };
      
      const response = await apiService.project.inventory.getMonthlyInventoryList(params);
      if (response && response.success) {
        setInventoryList(response.data.items || []);
        setStatistics(response.data.statistics || {});
      }
    } catch (error) {
      console.error('获取盘点列表失败:', error);
      message.error('获取盘点列表失败');
    } finally {
      setInventoryLoading(false);
    }
  };

  // 获取商品列表
  const fetchProductList = async () => {
    try {
      const response = await apiService.project.product.getList({ 
        page_size: 1000,
        is_active: true 
      });
      
      console.log('月度盘点获取商品列表响应:', response); // 调试日志
      
      if (response && response.success) {
        // 处理 success: true 的情况，数据在 data 字段
        const products = response.data || [];
        setProductList(products);
        
        // 为批量录入准备数据
        const batchData = products.map(product => ({
          key: product.id,
          id: product.id,
          name: product.name,
          sku: product.sku,
          specification: product.specification,
          unit: product.unit,
          category_name: product.category_name,
          category_id: product.category_id,
          inventory_quantity: 0,
          actual_quantity: 0,
          remark: ''
        }));
        setBatchProducts(batchData);
        
        console.log('月度盘点设置产品列表成功:', products.length, '个产品');
      } else if (response && response.items) {
        // 处理直接返回 items 的情况
        const products = response.items || [];
        setProductList(products);
        
        // 为批量录入准备数据
        const batchData = products.map(product => ({
          key: product.id,
          id: product.id,
          name: product.name,
          sku: product.sku,
          specification: product.specification,
          unit: product.unit,
          category_name: product.category_name,
          category_id: product.category_id,
          inventory_quantity: 0,
          actual_quantity: 0,
          remark: ''
        }));
        setBatchProducts(batchData);
        
        console.log('月度盘点设置产品列表成功:', products.length, '个产品');
      } else {
        console.log('月度盘点获取商品列表返回空数据');
        setProductList([]);
        setBatchProducts([]);
      }
    } catch (error) {
      console.error('获取商品列表失败:', error);
      message.error('获取商品列表失败');
      setProductList([]);
      setBatchProducts([]);
    }
  };

  // 获取商品分类列表
  const fetchCategoryList = async () => {
    try {
      const response = await apiService.project.product.getCategories();
      if (response && response.success) {
        setCategoryList(response.data || []);
      }
    } catch (error) {
      console.error('获取分类列表失败:', error);
      message.error('获取分类列表失败');
    }
  };

  // 初始化数据
  useEffect(() => {
    if (currentStoreId) {
      fetchInventoryList();
    }
  }, [currentStoreId, filters]);

  useEffect(() => {
    fetchProductList();
    fetchCategoryList();
  }, []);

  // 处理门店切换
  const handleStoreChange = (storeId) => {
    onStoreChange(storeId);
  };

  // 打开单个录入模态框
  const handleOpenEntryModal = (record = null) => {
    setEditingInventory(record);
    if (record) {
      entryForm.setFieldsValue({
        product_id: record.product_id,
        inventory_quantity: record.inventory_quantity,
        actual_quantity: record.actual_quantity,
        unit: record.unit,
        remark: record.remark
      });
    } else {
      entryForm.resetFields();
    }
    setEntryModalVisible(true);
  };

  // 打开批量录入模态框
  const handleOpenBatchEntryModal = () => {
    setSelectedProducts([]);
    batchForm.resetFields();
    setBatchEntryModalVisible(true);
  };

  // 提交单个盘点录入
  const handleSubmitEntry = async () => {
    try {
      const values = await entryForm.validateFields();
      setSubmitting(true);

      const data = {
        store_id: currentStoreId,
        month: filters.month,
        ...values
      };

      let response;
      if (editingInventory) {
        response = await apiService.project.inventory.updateMonthlyInventory(editingInventory.id, data);
      } else {
        response = await apiService.project.inventory.createMonthlyInventory(data);
      }

      if (response && response.success) {
        message.success(editingInventory ? '更新成功' : '录入成功');
        
        // 记录操作日志
        await logOperation({
          module: 'inventory',
          action: editingInventory ? 'update' : 'create',
          resourceType: 'monthly_inventory',
          resourceId: editingInventory ? editingInventory.id : response.data?.id,
          description: editingInventory ? 
            `更新月度盘点记录：${values.product_id}` : 
            `创建月度盘点记录：${values.product_id}`,
          details: {
            store_id: currentStoreId,
            month: filters.month,
            inventory_quantity: values.inventory_quantity,
            actual_quantity: values.actual_quantity,
            unit: values.unit,
            remark: values.remark
          }
        });
        
        setEntryModalVisible(false);
        fetchInventoryList();
      }
    } catch (error) {
      console.error('提交失败:', error);
      message.error('提交失败');
    } finally {
      setSubmitting(false);
    }
  };

  // 提交批量盘点录入
  const handleSubmitBatchEntry = async () => {
    try {
      if (selectedProducts.length === 0) {
        message.error('请选择要盘点的商品');
        return;
      }

      setSubmitting(true);
      
      const inventoryData = selectedProducts.map(product => ({
        store_id: currentStoreId,
        month: filters.month,
        product_id: product.id,
        inventory_quantity: product.inventory_quantity || 0,
        actual_quantity: product.actual_quantity || 0,
        unit: product.unit,
        remark: product.remark || ''
      }));

      const response = await apiService.project.inventory.batchCreateMonthlyInventory(inventoryData);
      if (response && response.success) {
        message.success(`成功录入${selectedProducts.length}个商品的盘点数据`);
        
        // 记录操作日志
        await logOperation({
          module: 'inventory',
          action: 'batch_create',
          resourceType: 'monthly_inventory',
          description: `批量创建月度盘点记录：${selectedProducts.length}个商品`,
          details: {
            store_id: currentStoreId,
            month: filters.month,
            product_count: selectedProducts.length,
            products: selectedProducts.map(p => ({
              product_id: p.id,
              product_name: p.name,
              inventory_quantity: p.inventory_quantity,
              actual_quantity: p.actual_quantity
            }))
          }
        });
        
        setBatchEntryModalVisible(false);
        setSelectedProducts([]);
        fetchInventoryList();
      }
    } catch (error) {
      console.error('批量提交失败:', error);
      message.error('批量提交失败');
    } finally {
      setSubmitting(false);
    }
  };

  // 处理批量录入中的数据变更
  const handleBatchProductChange = (productId, field, value) => {
    setSelectedProducts(prev => 
      prev.map(product => 
        product.id === productId ? { ...product, [field]: value } : product
      )
    );
  };

  // 处理文件上传
  const handleUpload = async () => {
    try {
      const values = await uploadForm.validateFields();
      if (uploadFileList.length === 0) {
        message.error('请选择要上传的文件');
        return;
      }

      setSubmitting(true);
      const formData = new FormData();
      formData.append('file', uploadFileList[0]);
      formData.append('store_id', currentStoreId);
      formData.append('month', values.month.format('YYYY-MM-DD'));
      formData.append('remark', values.remark || '');

      const response = await apiService.project.inventory.uploadMonthlyInventory(formData);
      if (response && response.success) {
        message.success('上传成功');
        
        // 记录操作日志
        await logOperation({
          module: 'inventory',
          action: 'upload',
          resourceType: 'monthly_inventory',
          description: `上传月度盘点Excel文件：${uploadFileList[0].name}`,
          details: {
            store_id: currentStoreId,
            month: values.month.format('YYYY-MM-DD'),
            filename: uploadFileList[0].name,
            remark: values.remark
          }
        });
        
        setUploadModalVisible(false);
        setUploadFileList([]);
        uploadForm.resetFields();
        fetchInventoryList();
      }
    } catch (error) {
      console.error('上传失败:', error);
      message.error('上传失败');
    } finally {
      setSubmitting(false);
    }
  };

  // 删除盘点记录
  const handleDelete = async (id) => {
    try {
      const response = await apiService.project.inventory.deleteMonthlyInventory(id);
      if (response && response.success) {
        message.success('删除成功');
        
        // 记录操作日志
        await logOperation({
          module: 'inventory',
          action: 'delete',
          resourceType: 'monthly_inventory',
          resourceId: id,
          description: `删除月度盘点记录：${id}`,
          details: {
            store_id: currentStoreId,
            month: filters.month
          }
        });
        
        fetchInventoryList();
      }
    } catch (error) {
      console.error('删除失败:', error);
      message.error('删除失败');
    }
  };

  // 提交到财务审核
  const handleSubmitToFinance = async (id) => {
    try {
      const response = await apiService.project.inventory.submitMonthlyInventoryToFinance(id);
      if (response && response.success) {
        message.success('已提交到财务审核');
        
        // 记录操作日志
        await logOperation({
          module: 'inventory',
          action: 'submit',
          resourceType: 'monthly_inventory',
          resourceId: id,
          description: `提交月度盘点记录到财务审核：${id}`,
          details: {
            store_id: currentStoreId,
            month: filters.month
          }
        });
        
        fetchInventoryList();
      }
    } catch (error) {
      console.error('提交失败:', error);
      message.error('提交失败');
    }
  };

  // 撤回提交
  const handleWithdraw = async (id) => {
    try {
      const response = await apiService.project.inventory.withdrawMonthlyInventory(id);
      if (response && response.success) {
        message.success('撤回成功');
        
        // 记录操作日志
        await logOperation({
          module: 'inventory',
          action: 'withdraw',
          resourceType: 'monthly_inventory',
          resourceId: id,
          description: `撤回月度盘点记录：${id}`,
          details: {
            store_id: currentStoreId,
            month: filters.month
          }
        });
        
        fetchInventoryList();
      }
    } catch (error) {
      console.error('撤回失败:', error);
      message.error('撤回失败');
    }
  };

  // 查看详情
  const handleViewDetail = (record) => {
    setSelectedInventory(record);
    setDetailModalVisible(true);
  };

  // 下载模板
  const handleDownloadTemplate = async () => {
    try {
      const response = await apiService.project.inventory.downloadMonthlyInventoryTemplate();
      
      // 创建下载链接
      const url = window.URL.createObjectURL(new Blob([response]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', '月度盘点模板.xlsx');
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      
      message.success('模板下载成功');
    } catch (error) {
      console.error('下载模板失败:', error);
      message.error('下载模板失败');
    }
  };

  // 智能处理文件上传
  const handleIntelligentUpload = async () => {
    try {
      const values = await uploadForm.validateFields();
      if (uploadFileList.length === 0) {
        message.error('请选择要上传的文件');
        return;
      }

      if (!currentStoreId) {
        message.error('请选择门店');
        return;
      }

      setSubmitting(true);

      // 1. 首先上传文件到项目空间
      const formData = new FormData();
      formData.append('file', uploadFileList[0]);
      formData.append('category', 'monthly_inventory');
      
      // 上传文件到项目空间（需要实现此API）
      const uploadResponse = await apiService.project.space.uploadFile(formData);
      
      if (!uploadResponse || !uploadResponse.success) {
        throw new Error(uploadResponse?.message || '文件上传失败');
      }

      const fileId = uploadResponse.data.file_id;
      
      // 2. 使用AI智能处理预览
      const aiParams = {
        file_id: fileId,
        store_id: currentStoreId,
        month: filters.month,
        use_ai: values.use_ai !== false,
        processing_mode: values.processing_mode || 'auto'
      };

      const previewResponse = await apiService.project.inventory.previewMonthlyInventoryWithAi(aiParams);

      if (previewResponse && previewResponse.success) {
        message.success('智能解析完成');
        
        // 显示预览结果
        setPreviewData(previewResponse.data.preview_items || []);
        setImportStats({
          total: previewResponse.data.total_items || 0,
          processing_info: previewResponse.data.processing_info || {}
        });

        // 存储文件ID用于确认导入
        setFileId(fileId);
        setPreviewVisible(true);
        setUploadModalVisible(false);
      } else {
        throw new Error(previewResponse?.message || 'AI处理失败');
      }

    } catch (error) {
      console.error('智能上传失败:', error);
      message.error('智能上传失败: ' + error.message);
    } finally {
      setSubmitting(false);
    }
  };

  // 确认导入AI处理结果
  const handleConfirmImport = async () => {
    try {
      if (!fileId) {
        message.error('没有待导入的数据');
        return;
      }

      setSubmitting(true);

      const importData = {
        file_id: fileId,
        store_id: currentStoreId,
        month: filters.month,
        remark: `智能导入月度盘点数据 - ${new Date().toLocaleString()}`
      };

      const response = await apiService.project.inventory.confirmMonthlyInventoryImport(importData);

      if (response && response.success) {
        message.success(`导入成功，共导入 ${response.imported_count} 条数据`);
        
        // 记录操作日志
        await logOperation({
          module: 'inventory',
          action: 'ai_import',
          resourceType: 'monthly_inventory',
          description: `AI智能导入月度盘点数据：${response.imported_count}条`,
          details: {
            store_id: currentStoreId,
            month: filters.month,
            file_id: fileId,
            imported_count: response.imported_count
          }
        });

        setPreviewVisible(false);
        setFileId(null);
        setPreviewData([]);
        fetchInventoryList();
      } else {
        throw new Error(response?.message || '导入失败');
      }

    } catch (error) {
      console.error('确认导入失败:', error);
      message.error('确认导入失败: ' + error.message);
    } finally {
      setSubmitting(false);
    }
  };

  // 导出盘点数据
  const handleExport = () => {
    // 这里应该调用实际的导出API
    message.info('导出功能待实现');
  };

  // 盘点列表表格列
  const inventoryColumns = [
    {
      title: '商品编号',
      dataIndex: 'product_code',
      key: 'product_code',
      width: 120
    },
    {
      title: '商品名称',
      dataIndex: 'product_name',
      key: 'product_name',
      width: 200,
      ellipsis: true
    },
    {
      title: '规格',
      dataIndex: 'specification',
      key: 'specification',
      width: 120
    },
    {
      title: '单位',
      dataIndex: 'unit',
      key: 'unit',
      width: 80
    },
    {
      title: '分类',
      dataIndex: 'category_name',
      key: 'category_name',
      width: 120
    },
    {
      title: '库存量',
      dataIndex: 'inventory_quantity',
      key: 'inventory_quantity',
      width: 100,
      render: (text) => text || 0
    },
    {
      title: '实际量',
      dataIndex: 'actual_quantity',
      key: 'actual_quantity',
      width: 100,
      render: (text) => text || 0
    },
    // 门店端不显示差异量，因为不使用本系统进销存
    // {
    //   title: '差异',
    //   key: 'difference',
    //   width: 100,
    //   render: (_, record) => {
    //     const diff = (record.actual_quantity || 0) - (record.inventory_quantity || 0);
    //     return (
    //       <span style={{ color: diff === 0 ? '#52c41a' : diff > 0 ? '#1890ff' : '#ff4d4f' }}>
    //         {diff > 0 ? '+' : ''}{diff}
    //       </span>
    //     );
    //   }
    // },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status) => {
        const statusMap = {
          'draft': { color: 'default', text: '草稿' },
          'submitted': { color: 'processing', text: '已提交' },
          'approved': { color: 'success', text: '已审核' },
          'rejected': { color: 'error', text: '已驳回' }
        };
        const config = statusMap[status] || { color: 'default', text: status };
        return <Tag color={config.color}>{config.text}</Tag>;
      }
    },
    {
      title: '盘点时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 150,
      render: (text) => text ? dayjs(text).format('YYYY-MM-DD HH:mm') : '-'
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => handleViewDetail(record)}
          >
            查看
          </Button>
          {record.status === 'draft' && (
            <>
              <Button
                type="text"
                icon={<EditOutlined />}
                onClick={() => handleOpenEntryModal(record)}
              >
                编辑
              </Button>
              <Button
                type="text"
                onClick={() => handleSubmitToFinance(record.id)}
              >
                提交审核
              </Button>
              <Popconfirm
                title="确定要删除这条盘点记录吗？"
                onConfirm={() => handleDelete(record.id)}
                okText="确定"
                cancelText="取消"
              >
                <Button
                  type="text"
                  danger
                  icon={<DeleteOutlined />}
                >
                  删除
                </Button>
              </Popconfirm>
            </>
          )}
          {record.status === 'submitted' && (
            <Button
              type="text"
              onClick={() => handleWithdraw(record.id)}
            >
              撤回
            </Button>
          )}
        </Space>
      )
    }
  ];

  // 批量录入表格列
  const batchColumns = [
    {
      title: '商品编号',
      dataIndex: 'sku',
      key: 'sku',
      width: 120
    },
    {
      title: '商品名称',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      ellipsis: true
    },
    {
      title: '规格',
      dataIndex: 'specification',
      key: 'specification',
      width: 120
    },
    {
      title: '单位',
      dataIndex: 'unit',
      key: 'unit',
      width: 80
    },
    {
      title: '分类',
      dataIndex: 'category_name',
      key: 'category_name',
      width: 120
    },
    {
      title: '库存量',
      key: 'inventory_quantity',
      width: 120,
      render: (_, record) => (
        <InputNumber
          style={{ width: '100%' }}
          min={0}
          precision={2}
          value={record.inventory_quantity}
          onChange={(value) => handleBatchProductChange(record.id, 'inventory_quantity', value)}
          placeholder="库存量"
        />
      )
    },
    {
      title: '实际量',
      key: 'actual_quantity',
      width: 120,
      render: (_, record) => (
        <InputNumber
          style={{ width: '100%' }}
          min={0}
          precision={2}
          value={record.actual_quantity}
          onChange={(value) => handleBatchProductChange(record.id, 'actual_quantity', value)}
          placeholder="实际量"
        />
      )
    },
    {
      title: '备注',
      key: 'remark',
      width: 150,
      render: (_, record) => (
        <Input
          value={record.remark}
          onChange={(e) => handleBatchProductChange(record.id, 'remark', e.target.value)}
          placeholder="备注"
        />
      )
    }
  ];

  // 渲染统计卡片
  const renderStatistics = () => (
    <Row gutter={16} style={{ marginBottom: 16 }}>
      <Col span={6}>
        <Card>
          <Statistic
            title="总盘点数"
            value={statistics.total}
            prefix={<BarChartOutlined />}
          />
        </Card>
      </Col>
      <Col span={6}>
        <Card>
          <Statistic
            title="已完成"
            value={statistics.completed}
            prefix={<CheckCircleOutlined />}
            valueStyle={{ color: '#52c41a' }}
          />
        </Card>
      </Col>
      <Col span={6}>
        <Card>
          <Statistic
            title="待处理"
            value={statistics.pending}
            prefix={<ExclamationCircleOutlined />}
            valueStyle={{ color: '#faad14' }}
          />
        </Card>
      </Col>
      <Col span={6}>
        <Card>
          <Statistic
            title="已提交"
            value={statistics.submitted}
            prefix={<FileExcelOutlined />}
            valueStyle={{ color: '#1890ff' }}
          />
        </Card>
      </Col>
    </Row>
  );

  // 渲染分类统计
  const renderCategoryStatistics = () => {
    // 按分类汇总盘点数据
    const categoryStats = {};
    
    inventoryList.forEach(item => {
      const categoryName = item.category_name || '未分类';
      if (!categoryStats[categoryName]) {
        categoryStats[categoryName] = {
          categoryName,
          totalCount: 0,
          totalInventory: 0,
          totalActual: 0,
          totalDifference: 0,
          items: []
        };
      }
      
      const stats = categoryStats[categoryName];
      stats.totalCount += 1;
      stats.totalInventory += item.inventory_quantity || 0;
      stats.totalActual += item.actual_quantity || 0;
      stats.totalDifference += (item.actual_quantity || 0) - (item.inventory_quantity || 0);
      stats.items.push(item);
    });

    const categoryStatsArray = Object.values(categoryStats);

    const categoryColumns = [
      {
        title: '分类名称',
        dataIndex: 'categoryName',
        key: 'categoryName',
        width: 150
      },
      {
        title: '商品数量',
        dataIndex: 'totalCount',
        key: 'totalCount',
        width: 100
      },
      {
        title: '库存总量',
        dataIndex: 'totalInventory',
        key: 'totalInventory',
        width: 120,
        render: (value) => value.toFixed(2)
      },
      {
        title: '实际总量',
        dataIndex: 'totalActual',
        key: 'totalActual',
        width: 120,
        render: (value) => value.toFixed(2)
      },
      {
        title: '差异总量',
        dataIndex: 'totalDifference',
        key: 'totalDifference',
        width: 120,
        render: (value) => (
          <span style={{ color: value === 0 ? '#52c41a' : value > 0 ? '#1890ff' : '#ff4d4f' }}>
            {value > 0 ? '+' : ''}{value.toFixed(2)}
          </span>
        )
      },
      {
        title: '差异率',
        key: 'differenceRate',
        width: 100,
        render: (_, record) => {
          const rate = record.totalInventory > 0 ? 
            (record.totalDifference / record.totalInventory * 100) : 0;
          return (
            <span style={{ color: rate === 0 ? '#52c41a' : rate > 0 ? '#1890ff' : '#ff4d4f' }}>
              {rate > 0 ? '+' : ''}{rate.toFixed(2)}%
            </span>
          );
        }
      },
      {
        title: '操作',
        key: 'action',
        width: 100,
        render: (_, record) => (
          <Button
            type="link"
            onClick={() => {
              // 筛选该分类的商品
              setFilters({ ...filters, category: record.items[0]?.category_id });
              setActiveTab('entry');
            }}
          >
            查看明细
          </Button>
        )
      }
    ];

    return (
      <div>
        <Row gutter={16} style={{ marginBottom: 16 }}>
          <Col span={6}>
            <Card>
              <Statistic
                title="分类总数"
                value={categoryStatsArray.length}
                prefix={<PieChartOutlined />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="总库存量"
                value={categoryStatsArray.reduce((sum, cat) => sum + cat.totalInventory, 0).toFixed(2)}
                prefix={<BarChartOutlined />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="总实际量"
                value={categoryStatsArray.reduce((sum, cat) => sum + cat.totalActual, 0).toFixed(2)}
                prefix={<BarChartOutlined />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="总差异量"
                value={categoryStatsArray.reduce((sum, cat) => sum + cat.totalDifference, 0).toFixed(2)}
                prefix={<BarChartOutlined />}
                valueStyle={{ 
                  color: categoryStatsArray.reduce((sum, cat) => sum + cat.totalDifference, 0) >= 0 ? 
                    '#1890ff' : '#ff4d4f' 
                }}
              />
            </Card>
          </Col>
        </Row>

        <Table
          columns={categoryColumns}
          dataSource={categoryStatsArray}
          rowKey="categoryName"
          pagination={false}
          size="small"
        />
      </div>
    );
  };

  // 渲染筛选器
  const renderFilters = () => (
    <Card style={{ marginBottom: 16 }}>
      <Row gutter={16} align="middle">
        <Col span={4}>
          <Select
            placeholder="选择门店"
            style={{ width: '100%' }}
            value={currentStoreId}
            onChange={handleStoreChange}
            loading={loading}
          >
            {stores.map(store => (
              <Option key={store.id} value={store.id}>{store.name}</Option>
            ))}
          </Select>
        </Col>
        <Col span={4}>
          <DatePicker
            picker="month"
            placeholder="选择月份"
            style={{ width: '100%' }}
            value={filters.month ? dayjs(filters.month) : null}
            onChange={(date) => setFilters({ ...filters, month: date ? date.format('YYYY-MM') : null })}
          />
        </Col>
        <Col span={4}>
          <Select
            placeholder="选择分类"
            style={{ width: '100%' }}
            value={filters.category}
            onChange={(value) => setFilters({ ...filters, category: value })}
            allowClear
          >
            {categoryList.map(category => (
              <Option key={category.id} value={category.id}>{category.name}</Option>
            ))}
          </Select>
        </Col>
        <Col span={4}>
          <Select
            placeholder="选择状态"
            style={{ width: '100%' }}
            value={filters.status}
            onChange={(value) => setFilters({ ...filters, status: value })}
            allowClear
          >
            <Option value="draft">草稿</Option>
            <Option value="submitted">已提交</Option>
            <Option value="approved">已审核</Option>
            <Option value="rejected">已驳回</Option>
          </Select>
        </Col>
        <Col span={8}>
          <Space>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => handleOpenEntryModal()}
              disabled={!currentStoreId}
            >
              单个录入
            </Button>
            <Button
              type="primary"
              icon={<AppstoreAddOutlined />}
              onClick={handleOpenBatchEntryModal}
              disabled={!currentStoreId}
            >
              批量录入
            </Button>
            <Button
              icon={<UploadOutlined />}
              onClick={() => setUploadModalVisible(true)}
              disabled={!currentStoreId}
            >
              批量上传
            </Button>
            <Button
              icon={<DownloadOutlined />}
              onClick={handleDownloadTemplate}
            >
              下载模板
            </Button>
            <Button
              icon={<ReloadOutlined />}
              onClick={fetchInventoryList}
            >
              刷新
            </Button>
          </Space>
        </Col>
      </Row>
    </Card>
  );

  return (
    <div className="monthly-inventory">
      <Alert
        message="月度盘点说明"
        description="每月进行一次店库存盘点，录入实际库存量与系统库存量进行对比，发现差异及时处理。盘点完成后提交到财务审核。支持单个录入、批量录入和Excel上传三种方式。"
        type="info"
        showIcon
        style={{ marginBottom: 16 }}
      />

      {renderFilters()}
      {renderStatistics()}

      <Card>
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane tab="盘点录入" key="entry">
            <Table
              columns={inventoryColumns}
              dataSource={inventoryList}
              rowKey="id"
              loading={inventoryLoading}
              scroll={{ x: 1600 }}
              pagination={{
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条记录`
              }}
            />
          </TabPane>

          <TabPane tab="分类统计" key="statistics">
            {renderCategoryStatistics()}
          </TabPane>
        </Tabs>
      </Card>

      {/* 单个盘点录入模态框 */}
      <Modal
        title={editingInventory ? "编辑盘点" : "新增盘点"}
        open={entryModalVisible}
        onOk={handleSubmitEntry}
        onCancel={() => setEntryModalVisible(false)}
        confirmLoading={submitting}
        width={600}
      >
        <Form
          form={entryForm}
          layout="vertical"
          initialValues={{ unit: '个' }}
        >
          <Form.Item
            name="product_id"
            label="商品"
            rules={[{ required: true, message: '请选择商品' }]}
          >
            <Select
              placeholder="请选择商品"
              showSearch
              filterOption={(input, option) => {
                const product = productList.find(p => p.id === option.value);
                if (!product) return false;
                const searchText = `${product.name} ${product.sku || product.code} ${product.specification || ''} ${product.category_name || ''}`.toLowerCase();
                return searchText.indexOf(input.toLowerCase()) >= 0;
              }}
              onChange={(value) => {
                const selectedProduct = productList.find(p => p.id === value);
                if (selectedProduct) {
                  entryForm.setFieldsValue({
                    unit: selectedProduct.unit || '个'
                  });
                }
              }}
            >
              {productList.map(product => (
                <Option key={product.id} value={product.id}>
                  <div>
                    <div style={{ fontWeight: 'bold' }}>
                      {product.name} ({product.sku || product.code})
                    </div>
                    <div style={{ fontSize: '12px', color: '#666' }}>
                      规格: {product.specification || '无'} | 分类: {product.category_name || '无'}
                    </div>
                  </div>
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="inventory_quantity"
                label="库存量"
                rules={[{ required: true, message: '请输入库存量' }]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  min={0}
                  precision={2}
                  placeholder="请输入库存量"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="actual_quantity"
                label="实际量"
                rules={[{ required: true, message: '请输入实际量' }]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  min={0}
                  precision={2}
                  placeholder="请输入实际量"
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="unit"
            label="单位"
            rules={[{ required: true, message: '请输入单位' }]}
          >
            <Input placeholder="请输入单位" />
          </Form.Item>

          <Form.Item
            name="remark"
            label="备注"
          >
            <TextArea
              rows={3}
              placeholder="请输入备注信息"
            />
          </Form.Item>
        </Form>
      </Modal>

      {/* 批量盘点录入模态框 */}
      <Modal
        title="批量盘点录入"
        open={batchEntryModalVisible}
        onOk={handleSubmitBatchEntry}
        onCancel={() => setBatchEntryModalVisible(false)}
        confirmLoading={submitting}
        width={1200}
        style={{ top: 20 }}
      >
        <div style={{ marginBottom: 16 }}>
          <Alert
            message="批量录入说明"
            description="请先选择要盘点的商品，然后填写对应的库存量和实际量。可以按分类筛选商品，支持搜索功能。"
            type="info"
            showIcon
          />
        </div>
        
        <Transfer
          dataSource={batchProducts}
          titles={['可选商品', '已选商品']}
          targetKeys={selectedProducts.map(p => p.key)}
          onChange={(targetKeys) => {
            const selected = batchProducts.filter(product => targetKeys.includes(product.key));
            setSelectedProducts(selected);
          }}
          render={item => (
            <div>
              <div style={{ fontWeight: 'bold' }}>
                {item.name} ({item.sku})
              </div>
              <div style={{ fontSize: '12px', color: '#666' }}>
                规格: {item.specification || '无'} | 分类: {item.category_name || '无'}
              </div>
            </div>
          )}
          showSearch
          filterOption={(inputValue, option) =>
            option.name.toLowerCase().indexOf(inputValue.toLowerCase()) > -1 ||
            option.sku.toLowerCase().indexOf(inputValue.toLowerCase()) > -1 ||
            (option.specification && option.specification.toLowerCase().indexOf(inputValue.toLowerCase()) > -1) ||
            (option.category_name && option.category_name.toLowerCase().indexOf(inputValue.toLowerCase()) > -1)
          }
          style={{ marginBottom: 16 }}
        />

        {selectedProducts.length > 0 && (
          <div>
            <Divider>填写盘点数据</Divider>
            <Table
              columns={batchColumns}
              dataSource={selectedProducts}
              rowKey="id"
              pagination={false}
              scroll={{ y: 300 }}
              size="small"
            />
          </div>
        )}
      </Modal>

      {/* 批量上传模态框 */}
      <Modal
        title="批量上传盘点表"
        open={uploadModalVisible}
        onOk={handleIntelligentUpload}
        onCancel={() => {
          setUploadModalVisible(false);
          setUploadFileList([]);
          uploadForm.resetFields();
        }}
        confirmLoading={submitting}
        width={600}
      >
        <Form
          form={uploadForm}
          layout="vertical"
          initialValues={{
            use_ai: true,
            processing_mode: 'auto'
          }}
        >
          <Form.Item
            name="files"
            label="选择文件"
            rules={[{ required: true, message: '请选择要上传的文件' }]}
          >
            <Upload.Dragger
              fileList={uploadFileList}
              beforeUpload={(file) => {
                setUploadFileList([file]);
                return false;
              }}
              onRemove={() => {
                setUploadFileList([]);
              }}
              multiple={false}
              accept=".xlsx,.xls,.jpg,.jpeg,.png"
            >
              <p className="ant-upload-drag-icon">
                <InboxOutlined />
              </p>
              <p className="ant-upload-text">点击或拖拽月度盘点表到此区域上传</p>
              <p className="ant-upload-hint">
                支持 Excel 文件(.xlsx, .xls)和图片文件(.jpg, .jpeg, .png)<br/>
                <strong>AI将自动识别表格结构，智能提取盘点数据</strong>
              </p>
            </Upload.Dragger>
          </Form.Item>

          <Form.Item
            name="use_ai"
            valuePropName="checked"
            tooltip="启用AI可以识别图片格式的盘点表，提高识别准确率"
          >
            <Checkbox>启用AI智能识别</Checkbox>
          </Form.Item>

          <Form.Item
            name="processing_mode"
            label="处理模式"
            tooltip="自动模式会优先使用模板匹配，失败时自动切换到AI识别"
          >
            <Radio.Group>
              <Radio value="auto">自动选择（推荐）</Radio>
              <Radio value="template_only">仅模板匹配</Radio>
              <Radio value="ai_only">仅AI识别</Radio>
            </Radio.Group>
          </Form.Item>

          <div style={{ marginTop: 16, textAlign: 'center' }}>
            <Space>
              <Button
                type="primary"
                onClick={handleIntelligentUpload}
                loading={submitting}
                disabled={uploadFileList.length === 0}
              >
                智能上传
              </Button>
              <Button onClick={handleDownloadTemplate}>
                下载模板
              </Button>
            </Space>
          </div>
        </Form>
      </Modal>

      {/* 详情查看模态框 */}
      <Modal
        title="盘点详情"
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setDetailModalVisible(false)}>
            关闭
          </Button>
        ]}
        width={600}
      >
        {selectedInventory && (
          <div>
            <Row gutter={16}>
              <Col span={12}>
                <div><strong>商品名称：</strong>{selectedInventory.product_name}</div>
              </Col>
              <Col span={12}>
                <div><strong>商品编号：</strong>{selectedInventory.product_code}</div>
              </Col>
            </Row>
            <Divider />
            <Row gutter={16}>
              <Col span={12}>
                <div><strong>规格：</strong>{selectedInventory.specification || '-'}</div>
              </Col>
              <Col span={12}>
                <div><strong>单位：</strong>{selectedInventory.unit}</div>
              </Col>
            </Row>
            <Divider />
            <Row gutter={16}>
              <Col span={12}>
                <div><strong>分类：</strong>{selectedInventory.category_name || '-'}</div>
              </Col>
              <Col span={12}>
                <div><strong>状态：</strong>
                  <Tag color={selectedInventory.status === 'draft' ? 'default' : 
                              selectedInventory.status === 'submitted' ? 'processing' :
                              selectedInventory.status === 'approved' ? 'success' : 'error'}>
                    {selectedInventory.status === 'draft' ? '草稿' :
                     selectedInventory.status === 'submitted' ? '已提交' :
                     selectedInventory.status === 'approved' ? '已审核' : '已驳回'}
                  </Tag>
                </div>
              </Col>
            </Row>
            <Divider />
            <Row gutter={16}>
              <Col span={12}>
                <div><strong>库存量：</strong>{selectedInventory.inventory_quantity || 0}</div>
              </Col>
              <Col span={12}>
                <div><strong>实际量：</strong>{selectedInventory.actual_quantity || 0}</div>
              </Col>
            </Row>
            <Divider />
            <div><strong>备注：</strong>{selectedInventory.remark || '-'}</div>
            <Divider />
            <div><strong>盘点时间：</strong>{dayjs(selectedInventory.created_at).format('YYYY-MM-DD HH:mm:ss')}</div>
          </div>
        )}
      </Modal>

      {/* 智能预览数据模态框 */}
      <Modal
        title="智能识别预览"
        open={previewVisible}
        onOk={handleConfirmImport}
        onCancel={() => {
          setPreviewVisible(false);
          setPreviewData([]);
          setFileId(null);
        }}
        confirmLoading={submitting}
        width={1000}
        okText="确认导入"
        cancelText="取消"
      >
        <div style={{ marginBottom: 16 }}>
          <Alert
            message="智能识别结果"
            description={
              <div>
                <p>共识别到 <strong>{importStats.total || 0}</strong> 条商品记录</p>
                {importStats.processing_info && (
                  <p>处理方式：<strong>
                    {importStats.processing_info.method === 'ai' ? 'AI视觉识别' : 
                     importStats.processing_info.method === 'hybrid' ? '混合模式' : 
                     importStats.processing_info.method === 'template' ? '模板匹配' : '自动选择'}
                  </strong></p>
                )}
                {importStats.processing_info?.error_items > 0 && (
                  <p style={{color: '#ff4d4f'}}>
                    发现 {importStats.processing_info.error_items} 条错误记录，将在导入时自动过滤
                  </p>
                )}
              </div>
            }
            type="info"
            showIcon
          />
        </div>

        <Table
          dataSource={previewData}
          pagination={{ pageSize: 10 }}
          scroll={{ y: 400 }}
          size="small"
          columns={[
            {
              title: '商品编号',
              dataIndex: '商品编号',
              key: 'product_code',
              width: 120
            },
            {
              title: '商品名称',
              dataIndex: '商品名称',
              key: 'product_name',
              width: 150
            },
            {
              title: '规格',
              dataIndex: '规格',
              key: 'specification',
              width: 100
            },
            {
              title: '单位',
              dataIndex: '单位',
              key: 'unit',
              width: 80
            },
            {
              title: '分类',
              dataIndex: '分类',
              key: 'category',
              width: 100
            },
            {
              title: '库存量',
              dataIndex: '库存量',
              key: 'inventory_quantity',
              width: 80,
              render: (text) => typeof text === 'number' ? text : '-'
            },
            {
              title: '实际量',
              dataIndex: '实际量',
              key: 'actual_quantity',
              width: 80,
              render: (text) => typeof text === 'number' ? text : '-'
            },
            {
              title: '差异',
              key: 'difference',
              width: 80,
              render: (_, record) => {
                const inventory = Number(record['库存量']) || 0;
                const actual = Number(record['实际量']) || 0;
                const diff = actual - inventory;
                return (
                  <span style={{ color: diff !== 0 ? (diff > 0 ? '#52c41a' : '#ff4d4f') : 'inherit' }}>
                    {diff > 0 ? '+' : ''}{diff}
                  </span>
                );
              }
            },
            {
              title: '备注',
              dataIndex: '备注',
              key: 'remark',
              ellipsis: true
            }
          ]}
        />
      </Modal>
    </div>
  );
};

export default MonthlyInventory; 