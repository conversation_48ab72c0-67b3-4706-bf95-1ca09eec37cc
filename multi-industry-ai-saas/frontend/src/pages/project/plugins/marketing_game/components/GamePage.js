import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Card,
  Spin,
  Result,
  Button,
  Tabs,
  message,
  ConfigProvider,
  theme,
  Typography
} from 'antd';
import {
  HomeOutlined,
  LoadingOutlined,
  StarOutlined
} from '@ant-design/icons';
import apiService from '../service/api/marketingGame';
import CardDraw from './CardDraw';
import ScratchCard from './ScratchCard';
import AdventureGame from './AdventureGame';
import WheelOfFortune from './WheelOfFortune';
import EnhancedLuckyDraw from './EnhancedLuckyDraw';
import EnhancedScratchCard from './EnhancedScratchCard';
import EnhancedLevelGame from './EnhancedLevelGame';
import EnhancedWheelOfFortune from './EnhancedWheelOfFortune';
import './GamePage.css';

const { Title, Text } = Typography;

const GamePage = () => {
  const { gameId } = useParams();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [game, setGame] = useState(null);
  const [error, setError] = useState(null);
  const [isMobile, setIsMobile] = useState(window.innerWidth <= 768);

  // 检测屏幕宽度变化，响应式设计
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  useEffect(() => {
    const fetchGameData = async () => {
      try {
        setLoading(true);

        // 检查是否有项目ID，如果没有则使用公开API
        const projectId = localStorage.getItem('project_id');
        let response;
        
        if (projectId) {
          // 已登录用户，使用普通API
          response = await apiService.getGameDetail(gameId);
        } else {
          // 未登录用户（分享链接），使用公开API
          response = await apiService.getPublicGameDetail(gameId);
        }

        if (response.success) {
          setGame(response.data);
          // 设置页面标题
          document.title = `${response.data.title || '营销抽奖'} - 智能零售系统`;
        } else {
          setError(response.message || '游戏不存在或已结束');
        }
        setLoading(false);
      } catch (error) {
        console.error('获取游戏数据失败:', error);
        setError(error.message || '获取游戏数据失败');
        setLoading(false);
      }
    };

    fetchGameData();
  }, [gameId]);

  // 渲染游戏组件
  const renderGameComponent = () => {
    if (!game) return null;

    // 检查游戏状态
    if (game.status === 'ended') {
      return (
        <Result
          status="warning"
          title="活动已结束"
          subTitle="感谢您的参与，本次活动已经结束"
          className="game-result glassmorphism"
          extra={
            <Button
              type="primary"
              onClick={() => navigate('/')}
              size="large"
              icon={<HomeOutlined />}
              className="game-button-3d"
            >
              返回首页
            </Button>
          }
        />
      );
    }

    if (game.status === 'cancelled') {
      return (
        <Result
          status="error"
          title="活动已取消"
          subTitle="很抱歉，本次活动已经取消"
          className="game-result glassmorphism"
          extra={
            <Button
              type="primary"
              onClick={() => navigate('/')}
              size="large"
              icon={<HomeOutlined />}
              className="game-button-3d"
            >
              返回首页
            </Button>
          }
        />
      );
    }

    // 检查是否使用增强版组件（通过游戏设置判断）
    const useEnhanced = game.settings?.use_enhanced || false;

    // 根据游戏类型渲染不同的游戏组件
    switch (game.game_type) {
      case 'lucky_draw':
        return useEnhanced ? (
          <EnhancedLuckyDraw gameId={gameId} game={game} />
        ) : (
          <div className="game-content-wrapper glassmorphism">
            <div className="game-header">
              <Title level={2} className="game-title">
                <StarOutlined className="title-icon" /> {game.title}
              </Title>
              <Text className="game-description">{game.description}</Text>
            </div>
            <CardDraw gameId={gameId} />
          </div>
        );
      case 'scratch_card':
        return useEnhanced ? (
          <EnhancedScratchCard gameId={gameId} game={game} />
        ) : (
          <div className="game-content-wrapper glassmorphism">
            <div className="game-header">
              <Title level={2} className="game-title">
                <StarOutlined className="title-icon" /> {game.title}
              </Title>
              <Text className="game-description">{game.description}</Text>
            </div>
            <ScratchCard game={game} isMobile={isMobile} />
          </div>
        );
      case 'adventure_game':
        return useEnhanced ? (
          <EnhancedLevelGame gameId={gameId} game={game} />
        ) : (
          <AdventureGame game={game} isMobile={isMobile} />
        );
      case 'wheel_of_fortune':
        return useEnhanced ? (
          <EnhancedWheelOfFortune gameId={gameId} game={game} />
        ) : (
          <div className="game-content-wrapper glassmorphism">
            <div className="game-header">
              <Title level={2} className="game-title">
                <StarOutlined className="title-icon" /> {game.title}
              </Title>
              <Text className="game-description">{game.description}</Text>
            </div>
            <WheelOfFortune gameId={gameId} />
          </div>
        );
      default:
        return useEnhanced ? (
          <EnhancedLuckyDraw gameId={gameId} game={game} />
        ) : (
          <div className="game-content-wrapper glassmorphism">
            <div className="game-header">
              <Title level={2} className="game-title">
                <StarOutlined className="title-icon" /> {game.title}
              </Title>
              <Text className="game-description">{game.description}</Text>
            </div>
            <CardDraw gameId={gameId} />
          </div>
        );
    }
  };

  if (loading) {
    return (
      <div className="game-loading">
        <div className="game-loading-card glassmorphism">
          <Spin size="large" indicator={<LoadingOutlined className="loading-icon" spin />} />
          <p className="game-loading-text">精彩游戏即将开始...</p>
          <div className="shimmer-effect"></div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="game-error">
        <Result
          status="error"
          title="加载失败"
          subTitle={error}
          className="game-result glassmorphism"
          extra={
            <Button
              type="primary"
              onClick={() => navigate('/')}
              size="large"
              icon={<HomeOutlined />}
              className="game-button-3d"
            >
              返回首页
            </Button>
          }
        />
      </div>
    );
  }

  return (
    <ConfigProvider
      theme={{
        algorithm: theme.defaultAlgorithm,
        token: {
          colorPrimary: game?.settings?.theme_color || '#1890ff',
          borderRadius: 12,
        },
      }}
    >
      <div className="game-page" style={{
        backgroundImage: game?.settings?.background_image ?
          `url(${game.settings.background_image})` :
          'linear-gradient(135deg, #7367F0 0%, #CE9FFC 100%)'
      }}>
        <div className="game-backdrop"></div>
        <div className="game-pattern"></div>
        <div className={`game-page-container ${isMobile ? 'mobile' : 'desktop'}`}>
          {renderGameComponent()}

          {/* 底部信息 */}
          <footer className="game-footer glassmorphism">
            <Text type="secondary">© {new Date().getFullYear()} 智能零售营销系统</Text>
          </footer>
        </div>
      </div>
    </ConfigProvider>
  );
};

export default GamePage;
