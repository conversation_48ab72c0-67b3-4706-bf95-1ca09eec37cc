import React, { useState, useEffect } from 'react';
import { Card, Row, Col, DatePicker, Select, Button, Table, Statistic, Spin, Tabs, Space } from 'antd';
import { BarChartOutlined, LineChartOutlined, PieChartOutlined, DownloadOutlined } from '@ant-design/icons';
import { Line, Bar, Pie } from '@ant-design/plots';
import dayjs from "dayjs";
import apiService from '../../../../services/api';
import { logOperation } from '../../../../utils/logUtils';

const { RangePicker } = DatePicker;
const { Option } = Select;
const { TabPane } = Tabs;

/**
 * 利润分析组件
 */
const ProfitAnalysis = () => {
  // 状态
  const [loading, setLoading] = useState(false);
  const [stores, setStores] = useState([]);
  const [selectedStoreId, setSelectedStoreId] = useState(null);
  const [dateRange, setDateRange] = useState([dayjs().subtract(30, 'days'), dayjs()]);
  const [timeGranularity, setTimeGranularity] = useState('day');
  const [profitData, setProfitData] = useState([]);
  const [categoryData, setCategoryData] = useState([]);

  // 初始化
  useEffect(() => {
    fetchStores();
  }, []);

  // 监听筛选条件变化
  useEffect(() => {
    if (dateRange && dateRange.length === 2) {
      fetchProfitData();
    }
  }, [selectedStoreId, dateRange, timeGranularity]);

  // 获取门店列表
  const fetchStores = async () => {
    try {
      setLoading(true);
      const response = await apiService.project.store.getList();
      if (response && response.items) {
        setStores(response.items);
      }
    } catch (error) {
      console.error('获取门店列表失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 获取利润数据
  const fetchProfitData = async () => {
    try {
      setLoading(true);
      const startDate = dateRange[0].format('YYYY-MM-DD');
      const endDate = dateRange[1].format('YYYY-MM-DD');

      // 使用真实API获取数据
      const response = await apiService.project.finance.getProfitAnalysis({
        start_date: startDate,
        end_date: endDate,
        store_id: selectedStoreId,
        group_by: timeGranularity
      });

      if (response && response.items) {
        // 处理真实数据
        const processedData = processProfitData(response.items, timeGranularity);
        setProfitData(processedData.profitData);
        setCategoryData(processedData.categoryData);

        // 记录操作日志
        logOperation({
          module: 'finance',
          action: 'query',
          resourceType: 'profit_analysis',
          description: `查询利润分析数据，时间范围：${startDate}至${endDate}，时间粒度：${timeGranularity}${selectedStoreId ? '，指定门店' : '，所有门店'}`,
          details: {
            start_date: startDate,
            end_date: endDate,
            store_id: selectedStoreId,
            group_by: timeGranularity
          }
        });
      } else {
        // 如果没有数据，使用空数组
        console.log('未获取到真实数据，使用空数组');
        setProfitData([]);
        setCategoryData([]);
      }
    } catch (error) {
      console.error('获取利润数据失败:', error);
      // 出错时使用空数组
      setProfitData([]);
      setCategoryData([]);
    } finally {
      setLoading(false);
    }
  };

  // 处理真实利润数据
  const processProfitData = (items, granularity) => {
    if (!items || items.length === 0) {
      return {
        profitData: [],
        categoryData: []
      };
    }

    // 处理利润趋势数据
    const profitData = items.map(item => {
      // 使用后端计算的数据，避免前端重复计算
      return {
        date: item.date || item.month || item.week,
        sales: parseFloat(item.total_sales || 0),
        cost: parseFloat(item.total_cost || 0),
        profit: parseFloat(item.profit || 0),
        profit_rate: parseFloat(item.profit_rate || 0) / 100 // 转换为小数
      };
    });

    // 处理类别数据
    let categoryData = [];
    if (items[0] && items[0].categories) {
      categoryData = Object.entries(items[0].categories).map(([category, data]) => {
        // 使用后端计算的数据，避免前端重复计算
        return {
          category,
          name: data.name || category,
          sales: parseFloat(data.sales) || 0,
          cost: parseFloat(data.cost) || 0,
          profit: parseFloat(data.profit) || 0,
          profit_rate: parseFloat(data.profit_rate || 0) / 100 // 转换为小数
        };
      }).filter(item => item.sales > 0);
    } else {
      // 如果没有真实类别数据，提供一些基本类别
      categoryData = [
        { category: 'food', name: '食品', sales: 0, cost: 0, profit: 0, profit_rate: 0 },
        { category: 'beverage', name: '饮料', sales: 0, cost: 0, profit: 0, profit_rate: 0 },
        { category: 'daily', name: '日用品', sales: 0, cost: 0, profit: 0, profit_rate: 0 },
        { category: 'clothing', name: '服装', sales: 0, cost: 0, profit: 0, profit_rate: 0 },
        { category: 'electronics', name: '电子产品', sales: 0, cost: 0, profit: 0, profit_rate: 0 },
      ];
    }

    return { profitData, categoryData };
  };

  // 处理空数据情况
  const getEmptyData = () => {
    return {
      profitData: [],
      categoryData: []
    };
  };

  // 处理门店选择
  const handleStoreChange = (value) => {
    setSelectedStoreId(value);
  };

  // 处理日期范围选择
  const handleDateRangeChange = (dates) => {
    setDateRange(dates);
  };

  // 处理时间粒度选择
  const handleGranularityChange = (value) => {
    setTimeGranularity(value);
  };

  // 导出数据
  const handleExport = () => {
    // 实现导出功能
    console.log('导出数据');
  };

  // 利润趋势图配置
  const profitTrendConfig = {
    data: profitData,
    xField: 'date',
    yField: 'profit',
    seriesField: 'type',
    smooth: true,
    legend: {
      position: 'top',
    },
    xAxis: {
      type: 'time',
    },
    tooltip: {
      showMarkers: false,
    },
  };

  // 类别利润分布图配置
  const categoryProfitConfig = {
    data: categoryData,
    angleField: 'profit',
    colorField: 'name',
    radius: 0.8,
    label: {
      type: 'outer',
      formatter: ({name, percent}) => `${name}: ${(percent * 100).toFixed(2)}%`,
    },
    interactions: [
      {
        type: 'element-active',
      },
    ],
  };

  // 销售成本利润对比图配置
  const salesCostProfitConfig = {
    data: profitData,
    isGroup: true,
    xField: 'date',
    yField: 'value',
    seriesField: 'type',
    dodgePadding: 2,
    label: {
      position: 'middle',
      layout: [
        {
          type: 'interval-adjust-position',
        },
        {
          type: 'interval-hide-overlap',
        },
        {
          type: 'adjust-color',
        },
      ],
    },
  };

  // 处理销售成本利润对比数据
  const getSalesCostProfitData = () => {
    const result = [];
    profitData.forEach(item => {
      result.push({ date: item.date, value: item.sales, type: '销售额' });
      result.push({ date: item.date, value: item.cost, type: '成本' });
      result.push({ date: item.date, value: item.profit, type: '利润' });
    });
    return result;
  };

  // 利润数据表格列
  const profitColumns = [
    {
      title: '日期',
      dataIndex: 'date',
      key: 'date',
    },
    {
      title: '销售额(元)',
      dataIndex: 'sales',
      key: 'sales',
      render: (text) => text.toFixed(2),
    },
    {
      title: '成本(元)',
      dataIndex: 'cost',
      key: 'cost',
      render: (text) => text.toFixed(2),
    },
    {
      title: '利润(元)',
      dataIndex: 'profit',
      key: 'profit',
      render: (text) => text.toFixed(2),
    },
    {
      title: '利润率',
      dataIndex: 'profit_rate',
      key: 'profit_rate',
      render: (text) => (text * 100).toFixed(2) + '%',
    },
  ];

  // 类别利润表格列
  const categoryColumns = [
    {
      title: '类别',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '销售额(元)',
      dataIndex: 'sales',
      key: 'sales',
      render: (text) => text.toFixed(2),
    },
    {
      title: '成本(元)',
      dataIndex: 'cost',
      key: 'cost',
      render: (text) => text.toFixed(2),
    },
    {
      title: '利润(元)',
      dataIndex: 'profit',
      key: 'profit',
      render: (text) => text.toFixed(2),
    },
    {
      title: '利润率',
      dataIndex: 'profit_rate',
      key: 'profit_rate',
      render: (text) => (text * 100).toFixed(2) + '%',
    },
  ];

  return (
    <div className="profit-analysis-container">
      <Card>
        <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between' }}>
          <h2>利润分析</h2>
          <Space>
            <Select
              placeholder="选择门店"
              style={{ width: 200 }}
              allowClear
              onChange={handleStoreChange}
              value={selectedStoreId}
            >
              {stores.map(store => (
                <Option key={store.id} value={store.id}>{store.name}</Option>
              ))}
            </Select>
            <RangePicker
              value={dateRange}
              onChange={handleDateRangeChange}
            />
            <Select
              value={timeGranularity}
              onChange={handleGranularityChange}
              style={{ width: 120 }}
            >
              <Option value="day">按日</Option>
              <Option value="week">按周</Option>
              <Option value="month">按月</Option>
            </Select>
            <Button
              icon={<DownloadOutlined />}
              onClick={handleExport}
            >
              导出数据
            </Button>
          </Space>
        </div>

        <Spin spinning={loading}>
          <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
            <Col span={6}>
              <Card>
                <Statistic
                  title="总销售额"
                  value={profitData.reduce((sum, item) => sum + item.sales, 0)}
                  precision={2}
                  suffix="元"
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title="总成本"
                  value={profitData.reduce((sum, item) => sum + item.cost, 0)}
                  precision={2}
                  suffix="元"
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title="总利润"
                  value={profitData.length > 0 ?
                    profitData.reduce((sum, item) => sum + (item.profit || 0), 0) : 0}
                  precision={2}
                  suffix="元"
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title="平均利润率"
                  value={profitData.length > 0 ?
                    (profitData.reduce((sum, item) => sum + (item.profit_rate || 0), 0) * 100 / profitData.length) : 0}
                  precision={2}
                  suffix="%"
                />
              </Card>
            </Col>
          </Row>

          <Tabs defaultActiveKey="trend">
            <TabPane
              tab={<span><LineChartOutlined />利润趋势</span>}
              key="trend"
            >
              <Row gutter={[16, 16]}>
                <Col span={24}>
                  <Card>
                    <Line {...profitTrendConfig} />
                  </Card>
                </Col>
              </Row>
            </TabPane>
            <TabPane
              tab={<span><PieChartOutlined />类别分析</span>}
              key="category"
            >
              <Row gutter={[16, 16]}>
                <Col span={12}>
                  <Card title="类别利润分布">
                    <Pie {...categoryProfitConfig} />
                  </Card>
                </Col>
                <Col span={12}>
                  <Card title="类别利润明细">
                    <Table
                      columns={categoryColumns}
                      dataSource={categoryData}
                      rowKey="category"
                      pagination={false}
                    />
                  </Card>
                </Col>
              </Row>
            </TabPane>
            <TabPane
              tab={<span><BarChartOutlined />利润明细</span>}
              key="detail"
            >
              <Table
                columns={profitColumns}
                dataSource={profitData}
                rowKey="date"
                pagination={{ pageSize: 10 }}
              />
            </TabPane>
          </Tabs>
        </Spin>
      </Card>
    </div>
  );
};

export default ProfitAnalysis;
