#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
SAPI 插件数据库迁移脚本包
"""
import logging
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy.exc import IntegrityError

from ..models import (
    SAPIConfig,
    SAPIEndpoint,
    SAPIToken,
    SAPITokenApplication,
    SAPIUsageLog,
    MCPServer,
    MCPServerProcess,
    APIMCPToolMapping
)

# 导入迁移脚本
from . import add_transport_type_to_mcp_servers
from . import convert_transport_type_to_types
from . import add_deployment_mode_to_mcp_servers
from . import add_rejection_reason_to_token_applications
from . import fix_mcp_server_processes
from . import token_update
from . import token_application_update

logger = logging.getLogger(__name__)

async def create_tables(db: AsyncSession):
    """
    创建SAPI插件所需的数据库表

    Args:
        db: 数据库会话
    """
    try:
        # 运行迁移脚本
        logger.info("运行SAPI插件数据库迁移脚本")

        # 为 sapi_mcp_servers 表添加 transport_type 列
        await add_transport_type_to_mcp_servers.migrate(db)

        # 将 transport_type 列转换为 transport_types 列
        await convert_transport_type_to_types.migrate(db)

        # 添加部署模式相关列
        await add_deployment_mode_to_mcp_servers.migrate(db)

        # 创建 sapi_token_applications 表
        try:
            # 动态导入，避免循环导入
            from . import create_token_applications_table
            await create_token_applications_table.migrate(db)
        except ImportError:
            logger.warning("无法导入 create_token_applications_table 模块，跳过创建 sapi_token_applications 表")

        # 为 sapi_token_applications 表添加 rejection_reason 列
        await add_rejection_reason_to_token_applications.migrate(db)

        # 修复 sapi_mcp_server_processes 表中的问题
        await fix_mcp_server_processes.migrate(db)

        # 为 sapi_api_mcp_tool_mappings 表添加 tool_description 列
        try:
            # 动态导入，避免循环导入
            from . import add_tool_description_to_mappings
            await add_tool_description_to_mappings.migrate(db)
        except ImportError:
            logger.warning("无法导入 add_tool_description_to_mappings 模块，跳过添加 tool_description 列")

        # 更新令牌表结构，以支持JWT+OAuth2.1认证
        await token_update.update_token_table(db)

        # 更新令牌申请表结构，以支持JWT+OAuth2.1认证
        await token_application_update.update_token_application_table(db)

        # 检查是否已存在配置
        result = await db.execute(select(SAPIConfig).limit(1))
        config = result.scalar_one_or_none()

        if not config:
            # 创建默认配置
            default_config = SAPIConfig(
                name="默认配置",
                description="智能助手集成平台默认配置",
                model_name="gpt-4",
                enabled=True
            )
            db.add(default_config)
            await db.commit()
            logger.info("创建了SAPI默认配置")

            # 创建默认API端点
            default_endpoints = [
                SAPIEndpoint(
                    name="用户信息",
                    description="获取当前用户信息",
                    endpoint_path="/api/v1/users/me",
                    method="GET",
                    requires_auth=True,
                    allowed_roles="admin,user",
                    parameters={},
                    config_id=default_config.id
                ),
                SAPIEndpoint(
                    name="项目列表",
                    description="获取项目列表",
                    endpoint_path="/api/v1/projects",
                    method="GET",
                    requires_auth=True,
                    allowed_roles="admin,user",
                    parameters={
                        "page": {"type": "integer", "required": False, "description": "页码"},
                        "page_size": {"type": "integer", "required": False, "description": "每页数量"}
                    },
                    config_id=default_config.id
                )
            ]

            for endpoint in default_endpoints:
                db.add(endpoint)

            await db.commit()
            logger.info("创建了SAPI默认API端点")
        else:
            logger.info("SAPI配置已存在，跳过创建默认配置")

    except IntegrityError as e:
        await db.rollback()
        logger.error(f"创建SAPI数据库表时发生完整性错误: {str(e)}")
        import traceback
        traceback.print_exc()
        # 不重新抛出异常，允许应用程序继续启动
        # 但是记录错误，以便后续处理
    except Exception as e:
        await db.rollback()
        logger.error(f"创建SAPI数据库表时发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        # 不重新抛出异常，允许应用程序继续启动
        # 但是记录错误，以便后续处理
