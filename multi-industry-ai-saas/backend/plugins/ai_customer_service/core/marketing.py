#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
智能营销模块

基于客户行为分析，自动推送优惠券、个性化推荐等营销活动。
"""

import logging
import uuid
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, desc

from services.marketing_activity import MarketingActivityService
from services.notification_service import NotificationService
from schemas.notification import NotificationCreate
from ..models.customer_service import (
    CustomerServiceSession,
    MarketingAction,
    UserProfile
)

logger = logging.getLogger(__name__)

class IntelligentMarketing:
    """智能营销服务"""
    
    def __init__(self):
        self.marketing_rules = {
            "new_customer": {
                "trigger": "first_interaction",
                "actions": ["welcome_coupon", "product_introduction"],
                "delay": 0  # 立即执行
            },
            "loyal_customer": {
                "trigger": "multiple_interactions",
                "actions": ["loyalty_reward", "premium_offer"],
                "delay": 300  # 5分钟后
            },
            "complaint_recovery": {
                "trigger": "complaint_resolved",
                "actions": ["apology_coupon", "satisfaction_survey"],
                "delay": 3600  # 1小时后
            },
            "purchase_intent": {
                "trigger": "product_inquiry",
                "actions": ["product_recommendation", "limited_offer"],
                "delay": 600  # 10分钟后
            }
        }
        
    async def analyze_marketing_opportunity(
        self,
        db: AsyncSession,
        session_id: str,
        message: str,
        intent: str,
        customer_profile: Dict[str, Any],
        project_id: uuid.UUID
    ) -> List[Dict[str, Any]]:
        """
        分析营销机会
        
        Args:
            db: 数据库会话
            session_id: 会话ID
            message: 用户消息
            intent: 消息意图
            customer_profile: 客户画像
            project_id: 项目ID
            
        Returns:
            List[Dict[str, Any]]: 营销机会列表
        """
        try:
            opportunities = []
            
            # 分析客户类型
            customer_type = customer_profile.get("customer_type", "new")
            
            # 新客户营销
            if customer_type == "new":
                opportunities.extend(await self._analyze_new_customer_opportunities(
                    customer_profile, intent
                ))
            
            # 老客户营销
            elif customer_type in ["regular", "loyal"]:
                opportunities.extend(await self._analyze_returning_customer_opportunities(
                    customer_profile, intent
                ))
            
            # 基于意图的营销
            opportunities.extend(await self._analyze_intent_based_opportunities(
                intent, message, customer_profile
            ))
            
            # 基于行为的营销
            opportunities.extend(await self._analyze_behavior_based_opportunities(
                db, customer_profile, project_id
            ))
            
            # 去重和排序
            unique_opportunities = self._deduplicate_opportunities(opportunities)
            sorted_opportunities = sorted(
                unique_opportunities, 
                key=lambda x: x.get("priority", 0), 
                reverse=True
            )
            
            return sorted_opportunities[:5]  # 返回前5个机会
            
        except Exception as e:
            logger.error(f"分析营销机会失败: {e}")
            return []
            
    async def _analyze_new_customer_opportunities(
        self,
        customer_profile: Dict[str, Any],
        intent: str
    ) -> List[Dict[str, Any]]:
        """分析新客户营销机会"""
        try:
            opportunities = []
            
            # 欢迎优惠券
            opportunities.append({
                "type": "welcome_coupon",
                "title": "新用户专享优惠券",
                "description": "欢迎新用户，专享首单优惠",
                "action_data": {
                    "coupon_type": "percentage",
                    "discount_value": 10,
                    "min_order_amount": 100,
                    "valid_days": 7
                },
                "priority": 8,
                "trigger_condition": "immediate",
                "target_audience": "new_customer"
            })
            
            # 产品介绍
            if intent in ["inquiry", "greeting"]:
                opportunities.append({
                    "type": "product_introduction",
                    "title": "产品推荐",
                    "description": "为新用户推荐热门产品",
                    "action_data": {
                        "recommendation_type": "popular_products",
                        "product_count": 3,
                        "include_discount": True
                    },
                    "priority": 6,
                    "trigger_condition": "immediate",
                    "target_audience": "new_customer"
                })
            
            return opportunities
            
        except Exception as e:
            logger.error(f"分析新客户营销机会失败: {e}")
            return []
            
    async def _analyze_returning_customer_opportunities(
        self,
        customer_profile: Dict[str, Any],
        intent: str
    ) -> List[Dict[str, Any]]:
        """分析回头客营销机会"""
        try:
            opportunities = []
            
            total_sessions = customer_profile.get("total_sessions", 0)
            
            # 忠诚客户奖励
            if total_sessions >= 10:
                opportunities.append({
                    "type": "loyalty_reward",
                    "title": "忠诚客户专享奖励",
                    "description": "感谢您的长期支持，专享VIP优惠",
                    "action_data": {
                        "coupon_type": "fixed",
                        "discount_value": 50,
                        "min_order_amount": 200,
                        "valid_days": 30
                    },
                    "priority": 9,
                    "trigger_condition": "delayed",
                    "delay_minutes": 5,
                    "target_audience": "loyal_customer"
                })
            
            # 个性化推荐
            common_topics = customer_profile.get("common_topics", [])
            if common_topics:
                opportunities.append({
                    "type": "personalized_recommendation",
                    "title": "个性化产品推荐",
                    "description": "基于您的兴趣为您推荐",
                    "action_data": {
                        "recommendation_type": "personalized",
                        "based_on": common_topics,
                        "product_count": 5
                    },
                    "priority": 7,
                    "trigger_condition": "immediate",
                    "target_audience": "returning_customer"
                })
            
            return opportunities
            
        except Exception as e:
            logger.error(f"分析回头客营销机会失败: {e}")
            return []
            
    async def _analyze_intent_based_opportunities(
        self,
        intent: str,
        message: str,
        customer_profile: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """基于意图分析营销机会"""
        try:
            opportunities = []
            
            # 购买意图
            if intent == "purchase" or any(keyword in message.lower() for keyword in ["购买", "下单", "价格", "多少钱"]):
                opportunities.append({
                    "type": "purchase_incentive",
                    "title": "限时优惠",
                    "description": "现在下单享受特别优惠",
                    "action_data": {
                        "coupon_type": "percentage",
                        "discount_value": 5,
                        "min_order_amount": 50,
                        "valid_hours": 2,
                        "urgency": True
                    },
                    "priority": 9,
                    "trigger_condition": "immediate",
                    "target_audience": "purchase_intent"
                })
            
            # 比较购物
            if any(keyword in message.lower() for keyword in ["比较", "对比", "哪个好", "推荐"]):
                opportunities.append({
                    "type": "comparison_guide",
                    "title": "产品对比指南",
                    "description": "专业对比分析，帮您选择最适合的产品",
                    "action_data": {
                        "content_type": "comparison_guide",
                        "include_recommendation": True,
                        "include_discount": True
                    },
                    "priority": 6,
                    "trigger_condition": "immediate",
                    "target_audience": "comparison_shopper"
                })
            
            # 投诉恢复
            if intent == "complaint":
                opportunities.append({
                    "type": "complaint_recovery",
                    "title": "诚意道歉优惠券",
                    "description": "为给您带来的不便深表歉意",
                    "action_data": {
                        "coupon_type": "fixed",
                        "discount_value": 30,
                        "min_order_amount": 0,
                        "valid_days": 14,
                        "apology_message": True
                    },
                    "priority": 10,
                    "trigger_condition": "delayed",
                    "delay_minutes": 60,
                    "target_audience": "complaint_customer"
                })
            
            return opportunities
            
        except Exception as e:
            logger.error(f"基于意图分析营销机会失败: {e}")
            return []
            
    async def _analyze_behavior_based_opportunities(
        self,
        db: AsyncSession,
        customer_profile: Dict[str, Any],
        project_id: uuid.UUID
    ) -> List[Dict[str, Any]]:
        """基于行为分析营销机会"""
        try:
            opportunities = []
            
            platform_user_id = customer_profile.get("platform_user_id")
            if not platform_user_id:
                return opportunities
            
            # 查询最近的会话
            recent_sessions_query = select(CustomerServiceSession).where(
                and_(
                    CustomerServiceSession.project_id == project_id,
                    CustomerServiceSession.platform_user_id == platform_user_id,
                    CustomerServiceSession.started_at >= datetime.utcnow() - timedelta(days=30)
                )
            ).order_by(desc(CustomerServiceSession.started_at)).limit(5)
            
            recent_sessions_result = await db.execute(recent_sessions_query)
            recent_sessions = recent_sessions_result.scalars().all()
            
            # 分析会话频率
            if len(recent_sessions) >= 3:
                # 高频用户 - 推送会员计划
                opportunities.append({
                    "type": "membership_offer",
                    "title": "VIP会员邀请",
                    "description": "成为VIP会员，享受更多专属权益",
                    "action_data": {
                        "membership_type": "vip",
                        "benefits": ["专属客服", "优先发货", "会员价格"],
                        "trial_period": 30
                    },
                    "priority": 8,
                    "trigger_condition": "delayed",
                    "delay_minutes": 10,
                    "target_audience": "frequent_user"
                })
            
            # 分析时间间隔
            if recent_sessions:
                last_session = recent_sessions[0]
                days_since_last = (datetime.utcnow() - last_session.started_at).days
                
                if days_since_last >= 7:
                    # 流失预警 - 挽回优惠
                    opportunities.append({
                        "type": "winback_offer",
                        "title": "我们想念您",
                        "description": "特别优惠，欢迎您回来",
                        "action_data": {
                            "coupon_type": "percentage",
                            "discount_value": 15,
                            "min_order_amount": 80,
                            "valid_days": 10,
                            "winback_message": True
                        },
                        "priority": 7,
                        "trigger_condition": "immediate",
                        "target_audience": "returning_user"
                    })
            
            return opportunities
            
        except Exception as e:
            logger.error(f"基于行为分析营销机会失败: {e}")
            return []
            
    def _deduplicate_opportunities(
        self, 
        opportunities: List[Dict[str, Any]]
    ) -> List[Dict[str, Any]]:
        """去重营销机会"""
        try:
            seen_types = set()
            unique_opportunities = []
            
            for opportunity in opportunities:
                opp_type = opportunity.get("type")
                if opp_type not in seen_types:
                    seen_types.add(opp_type)
                    unique_opportunities.append(opportunity)
            
            return unique_opportunities
            
        except Exception as e:
            logger.error(f"去重营销机会失败: {e}")
            return opportunities
            
    async def execute_marketing_action(
        self,
        db: AsyncSession,
        opportunity: Dict[str, Any],
        session_id: str,
        platform_user_id: str,
        platform_type: str,
        project_id: uuid.UUID,
        user_id: Optional[uuid.UUID] = None
    ) -> bool:
        """
        执行营销动作
        
        Args:
            db: 数据库会话
            opportunity: 营销机会
            session_id: 会话ID
            platform_user_id: 平台用户ID
            platform_type: 平台类型
            project_id: 项目ID
            user_id: 用户ID
            
        Returns:
            bool: 执行是否成功
        """
        try:
            action_type = opportunity.get("type")
            action_data = opportunity.get("action_data", {})
            
            # 创建营销动作记录
            marketing_action = MarketingAction(
                tenant_id=project_id,  # 暂时使用project_id作为tenant_id
                project_id=project_id,
                session_id=uuid.UUID(session_id) if session_id else None,
                action_type=action_type,
                action_data=action_data,
                target_user_id=user_id,
                target_platform_id=platform_user_id,
                status="pending"
            )
            
            db.add(marketing_action)
            await db.commit()
            await db.refresh(marketing_action)
            
            # 根据动作类型执行具体操作
            success = False
            
            if action_type in ["welcome_coupon", "loyalty_reward", "purchase_incentive", "complaint_recovery"]:
                success = await self._send_coupon(
                    db, marketing_action, opportunity, project_id, user_id
                )
            elif action_type in ["product_introduction", "personalized_recommendation"]:
                success = await self._send_recommendation(
                    db, marketing_action, opportunity, project_id, user_id
                )
            elif action_type == "membership_offer":
                success = await self._send_membership_offer(
                    db, marketing_action, opportunity, project_id, user_id
                )
            else:
                success = await self._send_generic_marketing_message(
                    db, marketing_action, opportunity, project_id, user_id
                )
            
            # 更新动作状态
            marketing_action.status = "sent" if success else "failed"
            marketing_action.sent_at = datetime.utcnow() if success else None
            await db.commit()
            
            return success
            
        except Exception as e:
            logger.error(f"执行营销动作失败: {e}")
            await db.rollback()
            return False
            
    async def _send_coupon(
        self,
        db: AsyncSession,
        marketing_action: MarketingAction,
        opportunity: Dict[str, Any],
        project_id: uuid.UUID,
        user_id: Optional[uuid.UUID]
    ) -> bool:
        """发送优惠券"""
        try:
            action_data = opportunity.get("action_data", {})
            
            # 生成优惠券码
            coupon_code = f"CS{datetime.now().strftime('%Y%m%d')}{uuid.uuid4().hex[:8].upper()}"
            
            # 构建优惠券信息
            coupon_info = {
                "code": coupon_code,
                "type": action_data.get("coupon_type", "percentage"),
                "value": action_data.get("discount_value", 10),
                "min_amount": action_data.get("min_order_amount", 0),
                "valid_days": action_data.get("valid_days", 7),
                "valid_hours": action_data.get("valid_hours"),
                "description": opportunity.get("description", "")
            }
            
            # 发送通知
            if user_id:
                notification_data = NotificationCreate(
                    title=opportunity.get("title", "优惠券"),
                    content=f"""
                    {opportunity.get('description', '')}
                    
                    优惠券码：{coupon_code}
                    优惠金额：{coupon_info['value']}{'%' if coupon_info['type'] == 'percentage' else '元'}
                    最低消费：{coupon_info['min_amount']}元
                    有效期：{coupon_info['valid_days']}天
                    
                    立即使用，享受优惠！
                    """,
                    type="marketing",
                    priority="medium",
                    target_url="/coupons"
                )
                
                await NotificationService.create_notification(
                    db=db,
                    user_id=user_id,
                    project_id=project_id,
                    notification_data=notification_data
                )
            
            # 更新营销动作数据
            marketing_action.action_data.update({
                "coupon_code": coupon_code,
                "coupon_info": coupon_info
            })
            
            logger.info(f"发送优惠券成功: {coupon_code}")
            return True
            
        except Exception as e:
            logger.error(f"发送优惠券失败: {e}")
            return False
            
    async def _send_recommendation(
        self,
        db: AsyncSession,
        marketing_action: MarketingAction,
        opportunity: Dict[str, Any],
        project_id: uuid.UUID,
        user_id: Optional[uuid.UUID]
    ) -> bool:
        """发送产品推荐"""
        try:
            action_data = opportunity.get("action_data", {})
            
            # 这里应该调用产品推荐服务
            # 暂时模拟推荐数据
            recommendations = [
                {"name": "热门产品A", "price": 99, "discount": 10},
                {"name": "热门产品B", "price": 199, "discount": 20},
                {"name": "热门产品C", "price": 299, "discount": 30}
            ]
            
            # 发送通知
            if user_id:
                notification_data = NotificationCreate(
                    title=opportunity.get("title", "产品推荐"),
                    content=f"""
                    {opportunity.get('description', '')}
                    
                    为您推荐以下产品：
                    {chr(10).join([f"• {p['name']} - ¥{p['price']} (优惠{p['discount']}元)" for p in recommendations[:3]])}
                    
                    点击查看更多详情！
                    """,
                    type="marketing",
                    priority="low",
                    target_url="/products/recommended"
                )
                
                await NotificationService.create_notification(
                    db=db,
                    user_id=user_id,
                    project_id=project_id,
                    notification_data=notification_data
                )
            
            # 更新营销动作数据
            marketing_action.action_data.update({
                "recommendations": recommendations
            })
            
            logger.info("发送产品推荐成功")
            return True
            
        except Exception as e:
            logger.error(f"发送产品推荐失败: {e}")
            return False
            
    async def _send_membership_offer(
        self,
        db: AsyncSession,
        marketing_action: MarketingAction,
        opportunity: Dict[str, Any],
        project_id: uuid.UUID,
        user_id: Optional[uuid.UUID]
    ) -> bool:
        """发送会员邀请"""
        try:
            action_data = opportunity.get("action_data", {})
            
            # 发送通知
            if user_id:
                benefits = action_data.get("benefits", [])
                trial_period = action_data.get("trial_period", 30)
                
                notification_data = NotificationCreate(
                    title=opportunity.get("title", "VIP会员邀请"),
                    content=f"""
                    {opportunity.get('description', '')}
                    
                    VIP会员专享权益：
                    {chr(10).join([f"• {benefit}" for benefit in benefits])}
                    
                    现在加入，享受{trial_period}天免费试用！
                    """,
                    type="marketing",
                    priority="medium",
                    target_url="/membership"
                )
                
                await NotificationService.create_notification(
                    db=db,
                    user_id=user_id,
                    project_id=project_id,
                    notification_data=notification_data
                )
            
            logger.info("发送会员邀请成功")
            return True
            
        except Exception as e:
            logger.error(f"发送会员邀请失败: {e}")
            return False
            
    async def _send_generic_marketing_message(
        self,
        db: AsyncSession,
        marketing_action: MarketingAction,
        opportunity: Dict[str, Any],
        project_id: uuid.UUID,
        user_id: Optional[uuid.UUID]
    ) -> bool:
        """发送通用营销消息"""
        try:
            # 发送通知
            if user_id:
                notification_data = NotificationCreate(
                    title=opportunity.get("title", "特别推荐"),
                    content=opportunity.get("description", "我们为您准备了特别优惠！"),
                    type="marketing",
                    priority="low",
                    target_url="/"
                )
                
                await NotificationService.create_notification(
                    db=db,
                    user_id=user_id,
                    project_id=project_id,
                    notification_data=notification_data
                )
            
            logger.info("发送营销消息成功")
            return True
            
        except Exception as e:
            logger.error(f"发送营销消息失败: {e}")
            return False
