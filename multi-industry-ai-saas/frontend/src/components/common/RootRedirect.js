import React, { useEffect } from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import rbacService from '../../services/rbacService';

/**
 * 根路径重定向组件
 * 根据用户登录状态和角色决定重定向目标
 * 使用 RBAC 服务进行权限控制
 */
const RootRedirect = () => {
  const { isAuthenticated, user } = useAuth();

  // 如果未登录，重定向到登录页
  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  // 获取用户数据 - 优先使用 AuthContext 中的 user 对象
  let userData = user;

  // 如果 AuthContext 中的 user 对象为空或者不包含项目角色信息，尝试从 localStorage 获取
  if (!userData || !userData.project_role) {
    try {
      userData = JSON.parse(localStorage.getItem('user_data') || '{}');
      console.log('RootRedirect - 从 localStorage 获取用户数据');
    } catch (error) {
      console.error('RootRedirect - 解析 localStorage 中的用户数据失败:', error);
      userData = {};
    }
  }

  const projectId = localStorage.getItem('project_id');

  console.log('RootRedirect - 用户角色:', userData.project_role);
  console.log('RootRedirect - 项目ID:', projectId);
  console.log('RootRedirect - 完整用户数据:', userData);

  // 检查当前路径，如果已经在项目选择页面，则不再重定向
  const currentPath = window.location.pathname;
  if (currentPath === '/tenant/projects') {
    console.log('RootRedirect - 已经在项目选择页面，不再重定向');
    // 重置重定向计数器
    localStorage.setItem('redirect_attempts', '0');
    return null; // 不进行重定向
  }


  // 检查是否有重定向循环
  const redirectAttempts = parseInt(localStorage.getItem('redirect_attempts') || '0');
  console.log('RootRedirect - 重定向尝试次数:', redirectAttempts);

  // 如果重定向尝试次数过多，直接进入项目选择页面
  if (redirectAttempts > 2) {
    console.log('RootRedirect - 检测到多次重定向尝试，直接进入项目选择页面');
    localStorage.setItem('redirect_attempts', '0'); // 重置计数器
    return <Navigate to="/tenant/projects" replace />;
  }

  // 使用 RBAC 服务获取用户应该跳转的仪表盘路径
  const dashboardPath = rbacService.getDashboardPath(userData);
  console.log('RootRedirect - RBAC 服务返回的仪表盘路径:', dashboardPath);

  // 特殊处理：如果用户是租户管理员且没有项目ID，则进入项目选择页面
  if (userData.is_tenant_admin && !projectId && dashboardPath.startsWith('/project/')) {
    console.log('RootRedirect - 租户管理员没有项目ID，跳转到项目选择页面');
    return <Navigate to="/tenant/projects" replace />;
  }

  // 特殊处理：如果用户有项目角色但没有项目ID，则进入项目选择页面
  if (userData.project_role && !projectId && dashboardPath.startsWith('/project/')) {
    console.log('RootRedirect - 用户有项目角色但没有项目ID，跳转到项目选择页面');
    return <Navigate to="/tenant/projects" replace />;
  }

  // 特殊处理：如果用户没有项目ID，也没有项目角色，则进入项目选择页面
  if (!projectId && !userData.project_role) {
    console.log('RootRedirect - 用户没有项目ID和项目角色，跳转到项目选择页面');
    return <Navigate to="/tenant/projects" replace />;
  }

  // 返回 RBAC 服务确定的仪表盘路径
  return <Navigate to={dashboardPath} replace />;
};

export default RootRedirect;
