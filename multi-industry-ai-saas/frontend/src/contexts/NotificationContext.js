import React, { createContext, useContext, useState, useEffect } from 'react';
import { message, notification } from 'antd';
import { useAuth } from './AuthContext';
import notificationWebSocket from '../services/websocket/NotificationWebSocket';
import notificationsApi from '../services/api/project/notifications';
import { getProjectId } from '../services/api/httpClient';

// 创建通知上下文
const NotificationContext = createContext();

/**
 * 通知提供者组件
 * @param {Object} props - 组件属性
 * @returns {JSX.Element} - 通知提供者组件
 */
export const NotificationProvider = ({ children }) => {
  const { user } = useAuth();
  const [notifications, setNotifications] = useState([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [loading, setLoading] = useState(false);
  const [wsConnected, setWsConnected] = useState(false);

  // 获取通知列表
  const fetchNotifications = async () => {
    if (!user) return;

    // 检查是否有项目ID
    const projectId = user.project_id || getProjectId();
    if (!projectId) {
      console.log('跳过获取通知 - 没有项目ID');
      return;
    }

    setLoading(true);
    try {
      const response = await notificationsApi.getNotifications({
        page: 1,
        page_size: 50,
        is_read: null // 获取所有通知
      });
      if (response && response.success && response.data && response.data.items) {
        setNotifications(response.data.items);
        setUnreadCount(response.data.items.filter(item => !item.is_read).length);
      } else {
        // 如果没有数据或数据格式不正确，设置为空数组
        console.warn('通知数据格式不正确或为空:', response);
        setNotifications([]);
        setUnreadCount(0);
      }
    } catch (error) {
      console.error('获取通知失败:', error);
      // 出错时设置为空数组
      setNotifications([]);
      setUnreadCount(0);
    } finally {
      setLoading(false);
    }
  };

  // 获取未读通知数量
  const fetchUnreadCount = async () => {
    if (!user) return;

    // 检查是否有项目ID
    const projectId = user.project_id || getProjectId();
    if (!projectId) {
      console.log('跳过获取未读通知数量 - 没有项目ID');
      return;
    }

    try {
      const response = await notificationsApi.getUnreadCount();
      if (response && response.success && response.data && response.data.count !== undefined) {
        setUnreadCount(response.data.count);
      } else {
        console.warn('未读通知数量数据格式不正确或为空:', response);
        // 如果数据格式不正确，保持当前值不变
      }
    } catch (error) {
      console.error('获取未读通知数量失败:', error);
      // 出错时不更新未读数量
    }
  };

  // 标记通知为已读
  const markAsRead = async (notificationId) => {
    if (!user) return;

    // 检查是否有项目ID
    const projectId = user.project_id || getProjectId();
    if (!projectId) {
      console.log('跳过标记通知为已读 - 没有项目ID');
      return;
    }

    try {
      const response = await notificationsApi.markAsRead(notificationId);
      if (response && response.success) {
        // 更新本地通知状态
        setNotifications(prev =>
          prev.map(item =>
            item.id === notificationId ? { ...item, is_read: true } : item
          )
        );
        // 更新未读数量
        setUnreadCount(prev => Math.max(0, prev - 1));
      }
    } catch (error) {
      console.error('标记通知为已读失败:', error);
      message.error('标记通知为已读失败');
    }
  };

  // 标记所有通知为已读
  const markAllAsRead = async () => {
    if (!user) return;

    // 检查是否有项目ID
    const projectId = user.project_id || getProjectId();
    if (!projectId) {
      console.log('跳过标记所有通知为已读 - 没有项目ID');
      return;
    }

    try {
      const response = await notificationsApi.markAllAsRead();
      if (response && response.success) {
        // 更新本地通知状态
        setNotifications(prev =>
          prev.map(item => ({ ...item, is_read: true }))
        );
        // 更新未读数量
        setUnreadCount(0);
        message.success('所有通知已标记为已读');
      }
    } catch (error) {
      console.error('标记所有通知为已读失败:', error);
      message.error('标记所有通知为已读失败');
    }
  };

  // 删除通知
  const deleteNotification = async (notificationId) => {
    if (!user) return;

    // 检查是否有项目ID
    const projectId = user.project_id || getProjectId();
    if (!projectId) {
      console.log('跳过删除通知 - 没有项目ID');
      return;
    }

    try {
      const response = await notificationsApi.deleteNotification(notificationId);
      if (response && response.success) {
        // 更新本地通知状态
        const updatedNotifications = notifications.filter(item => item.id !== notificationId);
        setNotifications(updatedNotifications);
        // 更新未读数量
        const newUnreadCount = updatedNotifications.filter(item => !item.is_read).length;
        setUnreadCount(newUnreadCount);
        message.success('通知已删除');
      }
    } catch (error) {
      console.error('删除通知失败:', error);
      message.error('删除通知失败');
    }
  };

  // 处理新通知
  const handleNewNotification = (newNotification) => {
    // 添加到通知列表
    setNotifications(prev => [newNotification, ...prev]);

    // 更新未读数量
    if (!newNotification.is_read) {
      setUnreadCount(prev => prev + 1);
    }

    // 显示通知提醒
    notification.open({
      message: newNotification.title,
      description: newNotification.content,
      duration: 5,
      onClick: () => {
        // 点击通知时标记为已读
        markAsRead(newNotification.id);

        // 如果有目标URL，导航到目标页面
        if (newNotification.target_url) {
          window.location.href = newNotification.target_url;
        }
      }
    });
  };

  // 连接WebSocket
  const connectWebSocket = () => {
    if (!user || !user.id) return;

    // 获取项目ID
    const projectId = user.project_id || getProjectId();
    if (!projectId) {
      console.error('无法连接WebSocket: 缺少项目ID');
      return;
    }

    console.log('准备连接通知WebSocket:', { userId: user.id, projectId });

    // 注册连接成功回调
    notificationWebSocket.onConnect(() => {
      setWsConnected(true);
      console.log('通知WebSocket已连接成功');

      // 连接成功后，可以获取最新通知
      fetchNotifications();
    });

    // 注册断开连接回调
    notificationWebSocket.onDisconnect(() => {
      setWsConnected(false);
      console.log('通知WebSocket已断开连接');

      // 断开连接后，可以启动轮询作为备用
      // 注意：WebSocket客户端内部已有重连机制，这里不需要立即启动轮询
    });

    // 注册消息处理回调
    notificationWebSocket.onMessage(handleNewNotification);

    // 注册设置更新回调
    notificationWebSocket.onSettingsUpdate(handleSettingsUpdate);

    // 注册工作流设置更新回调
    notificationWebSocket.onWorkflowSettingsUpdate(handleWorkflowSettingsUpdate);

    // 连接WebSocket
    notificationWebSocket.connect(user.id, projectId)
      .then(() => {
        console.log('通知WebSocket连接初始化完成');
      })
      .catch(error => {
        console.error('连接通知WebSocket失败:', error);

        // 获取通知设置，决定是否启动轮询
        const checkSettingsAndStartPolling = async () => {
          try {
            // 从本地存储获取设置
            const cachedSettings = (() => {
              try {
                const settingsJson = localStorage.getItem('notificationSettings');
                if (settingsJson) {
                  return JSON.parse(settingsJson);
                }
              } catch (error) {
                console.error('解析本地存储的通知设置失败:', error);
              }
              return null;
            })();

            if (cachedSettings) {
              // 检查频率设置
              const normalFrequency = String(cachedSettings.normal_frequency || '').trim().toLowerCase();
              const highPriorityFrequency = String(cachedSettings.high_priority_frequency || '').trim().toLowerCase();

              const shouldPoll = normalFrequency === 'realtime' || highPriorityFrequency === 'realtime';

              console.log('WebSocket连接失败 - 根据缓存设置决定是否启动轮询:', {
                normalFrequency,
                highPriorityFrequency,
                shouldPoll
              });

              if (shouldPoll) {
                console.log('WebSocket连接失败 - 启动轮询作为备用机制');
                startPolling();
              } else {
                console.log('WebSocket连接失败 - 不启动轮询（根据用户设置）');
              }
            } else {
              // 如果没有缓存设置，从API获取
              const response = await notificationsApi.getSettings();
              if (response && response.success && response.data) {
                const settings = response.data;

                // 检查频率设置
                const normalFrequency = String(settings.normal_frequency || '').trim().toLowerCase();
                const highPriorityFrequency = String(settings.high_priority_frequency || '').trim().toLowerCase();

                const shouldPoll = normalFrequency === 'realtime' || highPriorityFrequency === 'realtime';

                console.log('WebSocket连接失败 - 根据API设置决定是否启动轮询:', {
                  normalFrequency,
                  highPriorityFrequency,
                  shouldPoll
                });

                if (shouldPoll) {
                  console.log('WebSocket连接失败 - 启动轮询作为备用机制');
                  startPolling();
                } else {
                  console.log('WebSocket连接失败 - 不启动轮询（根据用户设置）');
                }
              } else {
                // 如果无法获取设置，默认不启动轮询
                console.log('WebSocket连接失败 - 无法获取设置，不启动轮询');
              }
            }
          } catch (error) {
            console.error('WebSocket连接失败 - 检查设置出错:', error);
          }
        };

        // 执行检查
        checkSettingsAndStartPolling();
      });
  };

  // 处理设置更新
  const handleSettingsUpdate = (settings) => {
    console.log('收到通知设置更新，调整轮询行为:', settings);

    // 根据设置调整轮询行为
    adjustPollingBehavior(settings);
  };

  // 处理工作流设置更新
  const handleWorkflowSettingsUpdate = (workflowType, settings) => {
    console.log(`收到工作流 ${workflowType} 通知设置更新:`, settings);
    // 工作流设置更新通常不影响轮询行为，这里只记录日志
  };

  // 停止轮询
  const stopPolling = () => {
    if (window.notificationPollingInterval) {
      console.log('停止通知轮询');
      clearInterval(window.notificationPollingInterval);
      window.notificationPollingInterval = null;
    }
  };

  // 启动轮询
  const startPolling = () => {
    // 停止现有轮询
    stopPolling();

    // 从本地存储获取设置
    const getSettingsFromStorage = () => {
      try {
        const settingsJson = localStorage.getItem('notificationSettings');
        if (settingsJson) {
          return JSON.parse(settingsJson);
        }
      } catch (error) {
        console.error('解析本地存储的通知设置失败:', error);
      }
      return null;
    };

    // 获取用户通知设置
    const getUserSettings = async () => {
      try {
        // 检查是否有项目ID
        const projectId = user.project_id || getProjectId();
        if (!projectId) {
          console.log('跳过获取通知设置 - 没有项目ID');
          return false; // 没有项目ID时默认不启用轮询
        }

        // 首先尝试从本地存储获取设置
        const cachedSettings = getSettingsFromStorage();
        if (cachedSettings) {
          console.log('使用本地缓存的通知设置:', cachedSettings);
          return cachedSettings;
        }

        // 如果本地没有缓存，则从API获取
        const response = await notificationsApi.getSettings();
        if (response && response.success && response.data) {
          const settings = response.data;

          // 缓存设置到本地存储
          localStorage.setItem('notificationSettings', JSON.stringify(settings));

          // 输出详细的设置信息，帮助诊断
          console.log('轮询检查 - 获取到的通知设置:', {
            normal_frequency: settings.normal_frequency,
            high_priority_frequency: settings.high_priority_frequency,
            push_notifications: settings.push_notifications
          });

          // 严格检查字符串值
          const normalFrequency = String(settings.normal_frequency).trim().toLowerCase();
          const highPriorityFrequency = String(settings.high_priority_frequency).trim().toLowerCase();

          // 根据通知频率设置决定是否轮询
          if (normalFrequency === 'realtime' || highPriorityFrequency === 'realtime') {
            // 如果任一频率设置为实时，则启用轮询
            console.log('启用通知轮询 - 用户设置了实时通知', {
              normalFrequency,
              highPriorityFrequency
            });
            return true;
          } else {
            // 如果都不是实时，则禁用轮询
            console.log('禁用通知轮询 - 用户未设置实时通知', {
              normalFrequency,
              highPriorityFrequency
            });
            return false;
          }
        }
        console.log('默认禁用通知轮询 - 无法获取有效的设置数据');
        return false; // 默认禁用轮询
      } catch (error) {
        console.error('获取通知设置失败:', error);
        return false; // 出错时默认禁用轮询
      }
    };

    // 初始化时获取一次设置并决定是否启动轮询
    let shouldPoll = false; // 默认不启动轮询，直到确认设置

    // 异步获取设置并决定是否启动轮询
    const initPolling = async () => {
      try {
        // 获取设置
        const settings = await getUserSettings();

        // 检查设置是否是对象
        if (typeof settings === 'object' && settings !== null) {
          console.log('初始化轮询 - 获取到设置对象:', settings);

          // 检查频率设置
          const normalFrequency = String(settings.normal_frequency || '').trim().toLowerCase();
          const highPriorityFrequency = String(settings.high_priority_frequency || '').trim().toLowerCase();

          shouldPoll = normalFrequency === 'realtime' || highPriorityFrequency === 'realtime';

          console.log('初始化轮询 - 根据设置决定是否轮询:', {
            normalFrequency,
            highPriorityFrequency,
            shouldPoll
          });
        } else if (typeof settings === 'boolean') {
          // 兼容旧的返回值格式
          shouldPoll = settings;
          console.log('初始化轮询 - 根据布尔值决定是否轮询:', shouldPoll);
        } else {
          console.warn('初始化轮询 - 获取到无效的设置:', settings);
          shouldPoll = false;
        }

        // 如果不应该轮询，直接返回
        if (!shouldPoll) {
          console.log('根据设置不启动轮询');
          return;
        }

        // 停止现有轮询
        stopPolling();

        // 每30秒轮询一次
        window.notificationPollingInterval = setInterval(async () => {
          try {
            // 每次轮询前检查设置
            const currentSettings = await getUserSettings();

            // 检查设置是否是对象
            if (typeof currentSettings === 'object' && currentSettings !== null) {
              // 检查频率设置
              const normalFreq = String(currentSettings.normal_frequency || '').trim().toLowerCase();
              const highPriorityFreq = String(currentSettings.high_priority_frequency || '').trim().toLowerCase();

              shouldPoll = normalFreq === 'realtime' || highPriorityFreq === 'realtime';

              console.log('轮询检查 - 根据设置决定是否继续轮询:', {
                normalFreq,
                highPriorityFreq,
                shouldPoll
              });
            } else if (typeof currentSettings === 'boolean') {
              // 兼容旧的返回值格式
              shouldPoll = currentSettings;
            } else {
              console.warn('轮询检查 - 获取到无效的设置:', currentSettings);
              shouldPoll = false;
            }

            if (shouldPoll) {
              fetchNotifications();
            } else {
              console.log('跳过通知轮询 - 根据用户设置');
              // 如果设置变更为不需要轮询，停止轮询
              stopPolling();
            }
          } catch (error) {
            console.error('轮询检查失败:', error);
          }
        }, 30000);

        console.log('启动通知轮询 - 间隔30秒');
      } catch (error) {
        console.error('初始化轮询失败:', error);
      }
    };

    // 执行初始化
    initPolling();

    // 返回清理函数
    return () => stopPolling();
  };

  // 根据设置调整轮询行为
  const adjustPollingBehavior = (settings) => {
    // 检查是否需要轮询
    const normalFrequency = String(settings.normal_frequency || '').trim().toLowerCase();
    const highPriorityFrequency = String(settings.high_priority_frequency || '').trim().toLowerCase();

    console.log('调整轮询行为 - 设置值:', {
      normalFrequency,
      highPriorityFrequency,
      rawSettings: settings
    });

    const shouldPoll = normalFrequency === 'realtime' || highPriorityFrequency === 'realtime';

    if (shouldPoll) {
      console.log('根据设置启用轮询 - 频率设置为实时');
      startPolling();
    } else {
      console.log('根据设置禁用轮询 - 频率设置为非实时');
      stopPolling();

      // 确保轮询已停止
      if (window.notificationPollingInterval) {
        console.warn('轮询仍在运行，强制停止');
        clearInterval(window.notificationPollingInterval);
        window.notificationPollingInterval = null;
      }
    }
  };

  // 监听设置变更事件
  useEffect(() => {
    // 处理设置变更事件
    const handleSettingsChanged = (event) => {
      console.log('收到通知设置变更事件:', event.detail);
      if (event.detail && event.detail.settings) {
        // 调整轮询行为
        adjustPollingBehavior(event.detail.settings);
      }
    };

    // 添加事件监听器
    window.addEventListener('notificationSettingsChanged', handleSettingsChanged);

    // 清理函数
    return () => {
      window.removeEventListener('notificationSettingsChanged', handleSettingsChanged);
    };
  }, []);

  // 初始化
  useEffect(() => {
    if (user) {
      // 检查是否有项目ID
      const projectId = user.project_id || getProjectId();
      if (!projectId) {
        console.log('跳过通知初始化 - 没有项目ID');
        return;
      }

      // 获取通知列表
      fetchNotifications();

      // 连接WebSocket
      connectWebSocket();

      // 检查用户通知设置，决定是否启动轮询
      const checkSettingsAndStartPolling = async () => {
        try {
          const response = await notificationsApi.getSettings();
          if (response && response.success && response.data) {
            const settings = response.data;

            // 输出详细的设置信息，帮助诊断
            console.log('初始化 - 获取到的通知设置:', {
              normal_frequency: settings.normal_frequency,
              high_priority_frequency: settings.high_priority_frequency,
              push_notifications: settings.push_notifications,
              raw_response: JSON.stringify(response)
            });

            // 严格检查字符串值
            const normalFrequency = String(settings.normal_frequency || '').trim().toLowerCase();
            const highPriorityFrequency = String(settings.high_priority_frequency || '').trim().toLowerCase();

            console.log('处理后的频率值:', {
              normalFrequency,
              highPriorityFrequency
            });

            // 只有当用户设置了实时通知时才启动轮询
            if (normalFrequency === 'realtime' || highPriorityFrequency === 'realtime') {
              console.log('启动通知轮询 - 用户设置了实时通知', {
                normalFrequency,
                highPriorityFrequency
              });
              return startPolling();
            } else {
              console.log('不启动通知轮询 - 用户未设置实时通知', {
                normalFrequency,
                highPriorityFrequency
              });
              stopPolling();
              return () => {}; // 返回空清理函数
            }
          } else {
            // 如果无法获取设置，默认不启动轮询
            console.log('不启动通知轮询 - 无法获取用户设置，使用默认值（关闭）');
            stopPolling();
            return () => {};
          }
        } catch (error) {
          console.error('获取通知设置失败，默认不启动轮询:', error);
          stopPolling();
          return () => {};
        }
      };

      // 初始化时检查本地存储的设置
      const cachedSettings = (() => {
        try {
          const settingsJson = localStorage.getItem('notificationSettings');
          if (settingsJson) {
            return JSON.parse(settingsJson);
          }
        } catch (error) {
          console.error('解析本地存储的通知设置失败:', error);
        }
        return null;
      })();

      // 如果有本地缓存的设置，根据设置决定是否启动轮询
      if (cachedSettings) {
        console.log('初始化 - 使用本地缓存的通知设置:', cachedSettings);

        // 检查频率设置
        const normalFrequency = String(cachedSettings.normal_frequency || '').trim().toLowerCase();
        const highPriorityFrequency = String(cachedSettings.high_priority_frequency || '').trim().toLowerCase();

        const shouldPoll = normalFrequency === 'realtime' || highPriorityFrequency === 'realtime';

        console.log('初始化 - 根据缓存设置决定是否启动轮询:', {
          normalFrequency,
          highPriorityFrequency,
          shouldPoll
        });

        if (shouldPoll) {
          console.log('初始化 - 启动轮询（根据缓存设置）');
          adjustPollingBehavior(cachedSettings);
        } else {
          console.log('初始化 - 不启动轮询（根据缓存设置）');
          // 确保轮询已停止
          stopPolling();
        }
      } else {
        // 如果没有本地缓存，异步执行检查和启动轮询
        checkSettingsAndStartPolling();
      }

      // 清理函数
      return () => {
        stopPolling();
        notificationWebSocket.disconnect();
      };
    }
  }, [user]);

  // 上下文值
  const value = {
    notifications,
    unreadCount,
    loading,
    wsConnected,
    fetchNotifications,
    fetchUnreadCount,
    markAsRead,
    markAllAsRead,
    deleteNotification
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}
    </NotificationContext.Provider>
  );
};

/**
 * 使用通知上下文的Hook
 * @returns {Object} - 通知上下文
 */
export const useNotification = () => {
  const context = useContext(NotificationContext);
  if (!context) {
    throw new Error('useNotification必须在NotificationProvider内使用');
  }
  return context;
};

export default NotificationContext;
