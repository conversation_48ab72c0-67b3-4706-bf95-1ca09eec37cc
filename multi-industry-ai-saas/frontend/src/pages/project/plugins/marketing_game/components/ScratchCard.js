import React, { useState, useEffect, useRef } from 'react';
import { useParams } from 'react-router-dom';
import {
  Card,
  Button,
  message,
  Spin,
  Typography,
  Row,
  Col,
  Modal,
  Divider,
  List,
  Avatar,
  Tag,
  Result,
  Space,
  Statistic,
  Switch
} from 'antd';
import {
  GiftOutlined,
  TrophyOutlined,
  UserOutlined,
  HistoryOutlined,
  ShareAltOutlined,
  SmileOutlined,
  MehOutlined,
  LoadingOutlined,
  SoundOutlined
} from '@ant-design/icons';
import apiService from '../service/api/marketingGame';
import './ScratchCard.css';

const { Title, Paragraph, Text } = Typography;

// 音效资源
const AUDIO = {
  SCRATCH: '/pages/plugin/marketing_game/assets/audio/scratch.mp3',
  WIN: '/pages/plugin/marketing_game/assets/audio/win.mp3',
  LOSE: '/pages/plugin/marketing_game/assets/audio/lose.mp3',
  CLICK: '/pages/plugin/marketing_game/assets/audio/click.mp3'
};

// 默认背景图片
const DEFAULT_BACKGROUNDS = [
  '/pages/plugin/marketing_game/assets/images/backgrounds/bg1.jpg',
  '/pages/plugin/marketing_game/assets/images/backgrounds/bg2.jpg',
  '/pages/plugin/marketing_game/assets/images/backgrounds/bg3.jpg',
  '/pages/plugin/marketing_game/assets/images/backgrounds/bg4.jpg',
  '/pages/plugin/marketing_game/assets/images/backgrounds/bg5.jpg'
];

// 默认奖品图标
const DEFAULT_PRIZE_ICONS = {
  first: '/pages/plugin/marketing_game/assets/images/prizes/first-prize.png',
  second: '/pages/plugin/marketing_game/assets/images/prizes/second-prize.png',
  third: '/pages/plugin/marketing_game/assets/images/prizes/third-prize.png',
  lucky: '/pages/plugin/marketing_game/assets/images/prizes/lucky-prize.png',
  participation: '/pages/plugin/marketing_game/assets/images/prizes/participation-prize.png',
  none: '/pages/plugin/marketing_game/assets/images/prizes/no-prize.png'
};

// 音效播放函数
const playAudio = (audioPath, volume = 0.5) => {
  const audio = new Audio(audioPath);
  audio.volume = volume;
  audio.play().catch(error => {
    console.error('音频播放失败:', error);
  });
  return audio;
};

const ScratchCard = () => {
  const { gameId } = useParams();
  const [game, setGame] = useState(null);
  const [prizes, setPrizes] = useState([]);
  const [records, setRecords] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showRules, setShowRules] = useState(false);
  const [showRecords, setShowRecords] = useState(false);
  const [showPrizes, setShowPrizes] = useState(false);
  const [isScratching, setIsScratching] = useState(false);
  const [isRevealed, setIsRevealed] = useState(false);
  const [winningPrize, setWinningPrize] = useState(null);
  const [showResult, setShowResult] = useState(false);
  const [soundEnabled, setSoundEnabled] = useState(true);

  const canvasRef = useRef(null);
  const contextRef = useRef(null);
  const scratchCardRef = useRef(null);
  const scratchAudioRef = useRef(null);

  // 加载游戏数据
  useEffect(() => {
    const fetchGameData = async () => {
      try {
        setLoading(true);

        // 获取游戏详情
        const gameResponse = await apiService.getGameDetail(gameId);
        if (!gameResponse.success) {
          message.error(gameResponse.message || '获取游戏详情失败');
          setLoading(false);
          return;
        }

        setGame(gameResponse.data);

        // 获取奖品列表
        const prizesResponse = await apiService.getPrizes(gameId);
        if (!prizesResponse.success) {
          message.error(prizesResponse.message || '获取奖品列表失败');
          setLoading(false);
          return;
        }

        // 确保至少有一个默认奖品（未中奖）
        let prizesList = prizesResponse.data || [];
        if (!prizesList.some(p => p.is_default)) {
          prizesList.push({
            id: 'default',
            name: '谢谢参与',
            description: '下次再来',
            image_url: DEFAULT_PRIZE_ICONS.none,
            probability: 0,
            is_default: true,
            remaining: 999999
          });
        }

        setPrizes(prizesList);

        // 获取最近中奖记录
        const recordsResponse = await apiService.getRecords(gameId, { page: 1, page_size: 10 });
        if (recordsResponse.success) {
          setRecords(recordsResponse.data?.items || []);
        }

        setLoading(false);
      } catch (error) {
        console.error('获取游戏数据失败:', error);
        message.error('获取游戏数据失败');
        setLoading(false);
      }
    };

    fetchGameData();
  }, [gameId]);

  // 初始化刮刮卡
  useEffect(() => {
    if (!loading && canvasRef.current) {
      const canvas = canvasRef.current;
      const context = canvas.getContext('2d');
      contextRef.current = context;

      // 设置画布大小
      const scratchCard = scratchCardRef.current;
      if (scratchCard) {
        canvas.width = scratchCard.offsetWidth;
        canvas.height = scratchCard.offsetHeight;

        // 填充灰色背景
        context.fillStyle = '#CCCCCC';
        context.fillRect(0, 0, canvas.width, canvas.height);

        // 添加提示文字
        context.font = '20px Arial';
        context.fillStyle = '#333333';
        context.textAlign = 'center';
        context.textBaseline = 'middle';
        context.fillText('刮开查看结果', canvas.width / 2, canvas.height / 2);
      }
    }
  }, [loading]);

  // 处理刮卡
  const handleScratch = (e) => {
    if (isRevealed || !isScratching) return;

    const canvas = canvasRef.current;
    const context = contextRef.current;

    if (!canvas || !context) return;

    const rect = canvas.getBoundingClientRect();
    const x = (e.clientX || e.touches[0].clientX) - rect.left;
    const y = (e.clientY || e.touches[0].clientY) - rect.top;

    // 播放刮卡音效
    if (soundEnabled && !scratchAudioRef.current) {
      scratchAudioRef.current = playAudio(AUDIO.SCRATCH, 0.3);
      scratchAudioRef.current.loop = true;
    }

    // 清除鼠标位置周围的区域
    context.globalCompositeOperation = 'destination-out';
    context.beginPath();
    context.arc(x, y, 20, 0, Math.PI * 2);
    context.fill();

    // 计算已刮开的面积百分比
    const imageData = context.getImageData(0, 0, canvas.width, canvas.height);
    const pixelData = imageData.data;
    let transparentPixels = 0;

    for (let i = 0; i < pixelData.length; i += 4) {
      if (pixelData[i + 3] === 0) {
        transparentPixels++;
      }
    }

    const totalPixels = pixelData.length / 4;
    const scratchedPercentage = (transparentPixels / totalPixels) * 100;

    // 如果刮开面积超过50%，显示全部
    if (scratchedPercentage > 50) {
      context.clearRect(0, 0, canvas.width, canvas.height);
      setIsRevealed(true);
      setIsScratching(false);

      // 停止刮卡音效
      if (scratchAudioRef.current) {
        scratchAudioRef.current.pause();
        scratchAudioRef.current = null;
      }

      // 播放结果音效
      if (soundEnabled) {
        playAudio(winningPrize.is_default ? AUDIO.LOSE : AUDIO.WIN, 0.5);
      }

      // 显示结果
      setTimeout(() => {
        setShowResult(true);
      }, 500);
    }
  };

  // 开始新的刮刮卡
  const startNewScratchCard = async () => {
    if (soundEnabled) {
      playAudio(AUDIO.CLICK, 0.3);
    }

    if (game.remaining_draws <= 0) {
      message.warning('您的参与次数已用完');
      return;
    }

    try {
      // 确保用户ID存在
      const userId = localStorage.getItem('user_id') || `user_${Date.now()}_${Math.floor(Math.random() * 100000)}`;
      if (!localStorage.getItem('user_id')) {
        localStorage.setItem('user_id', userId);
      }

      const deviceId = localStorage.getItem('device_id') || `device_${Date.now()}_${Math.floor(Math.random() * 100000)}`;
      if (!localStorage.getItem('device_id')) {
        localStorage.setItem('device_id', deviceId);
      }

      // 调用API进行抽奖，确保提供participant_id
      const response = await apiService.createRecord(gameId, {
        participant_id: userId, // 确保participant_id有值
        participant_name: localStorage.getItem('user_name') || '匿名用户',
        device_id: deviceId,
        timestamp: Date.now()
      });

      let selectedPrize;

      if (response.success && response.data) {
        // 使用API返回的中奖结果
        selectedPrize = prizes.find(p => p.id === response.data.prize_id);
      } else {
        // 如果API调用失败，本地随机选择一个奖品（根据概率）
        const randomValue = Math.random();
        let cumulativeProbability = 0;

        for (const prize of prizes) {
          cumulativeProbability += prize.probability / 100; // 假设概率以百分比存储
          if (randomValue <= cumulativeProbability) {
            selectedPrize = prize;
            break;
          }
        }
      }

      // 如果没有选中任何奖品，使用默认奖品（谢谢参与）
      if (!selectedPrize) {
        selectedPrize = prizes.find(p => p.is_default) || prizes[prizes.length - 1];
      }

      setWinningPrize(selectedPrize);
      setIsScratching(true);
      setIsRevealed(false);

      // 重置画布
      const canvas = canvasRef.current;
      const context = contextRef.current;

      if (canvas && context) {
        context.globalCompositeOperation = 'source-over';
        context.fillStyle = '#CCCCCC';
        context.fillRect(0, 0, canvas.width, canvas.height);

        // 添加提示文字
        context.font = '20px Arial';
        context.fillStyle = '#333333';
        context.textAlign = 'center';
        context.textBaseline = 'middle';
        context.fillText('刮开查看结果', canvas.width / 2, canvas.height / 2);
      }

      // 更新剩余次数
      setGame(prev => ({
        ...prev,
        remaining_draws: prev.remaining_draws - 1
      }));
    } catch (error) {
      console.error('抽奖失败:', error);
      message.error('抽奖失败，请重试');
    }
  };

  // 清除音频
  useEffect(() => {
    return () => {
      if (scratchAudioRef.current) {
        scratchAudioRef.current.pause();
        scratchAudioRef.current = null;
      }
    };
  }, []);

  if (loading) {
    return (
      <div className="scratch-card-loading">
        <Spin size="large" indicator={<LoadingOutlined className="loading-icon" spin />} />
        <p className="loading-text">精彩刮刮卡即将开始...</p>
      </div>
    );
  }

  // 获取背景图片
  const backgroundImage = game.background_image || DEFAULT_BACKGROUNDS[1];

  return (
    <div className="scratch-card-container" style={{ backgroundImage: `url(${backgroundImage})` }}>
      <div className="scratch-card-header">
        <img
          src={game.logo_image || '/pages/plugin/marketing_game/assets/images/logo-default.png'}
          alt="Logo"
          className="game-logo"
        />
        <Title level={2} className="game-title">{game.name}</Title>
        <Paragraph className="game-description">{game.description}</Paragraph>
      </div>

      <div className="scratch-card-content">
        <div className="settings-bar">
          <div className="settings-item">
            <span className="settings-label">音效</span>
            <Switch
              checked={soundEnabled}
              onChange={setSoundEnabled}
              size="small"
              checkedChildren={<SoundOutlined />}
              unCheckedChildren={<SoundOutlined />}
            />
          </div>
        </div>

        <div
          className="scratch-card"
          ref={scratchCardRef}
          onMouseDown={() => isScratching && !isRevealed}
          onMouseMove={handleScratch}
          onTouchMove={handleScratch}
        >
          {winningPrize && (
            <div className="prize-content">
              <img
                src={winningPrize.image_url || DEFAULT_PRIZE_ICONS.none}
                alt={winningPrize.name}
                className="prize-image"
              />
              <Title level={4} className="prize-title">
                {winningPrize.is_default ? '很遗憾，未中奖' : `恭喜您获得${winningPrize.name}`}
              </Title>
              <Paragraph className="prize-description">{winningPrize.description}</Paragraph>
            </div>
          )}

          <canvas
            ref={canvasRef}
            className="scratch-canvas"
          />
        </div>

        <div className="scratch-card-actions">
          <Button
            type="primary"
            size="large"
            icon={<GiftOutlined />}
            onClick={startNewScratchCard}
            disabled={isScratching || game.remaining_draws <= 0}
            className="scratch-button"
          >
            {isScratching ? '正在刮卡...' : `开始刮卡 (剩余${game.remaining_draws}次)`}
          </Button>

          <div className="action-buttons">
            <Button
              icon={<TrophyOutlined />}
              onClick={() => setShowPrizes(true)}
              className="action-button"
            >
              奖品设置
            </Button>
            <Button
              icon={<HistoryOutlined />}
              onClick={() => setShowRecords(true)}
              className="action-button"
            >
              中奖记录
            </Button>
            <Button
              icon={<UserOutlined />}
              onClick={() => setShowRules(true)}
              className="action-button"
            >
              活动规则
            </Button>
          </div>
        </div>
      </div>

      <div className="scratch-card-footer">
        <p>© {new Date().getFullYear()} {game.name} - 版权所有</p>
        <div className="footer-links">
          <a href="/privacy" className="footer-link">隐私政策</a>
          <a href="/terms" className="footer-link">使用条款</a>
          <a href="/contact" className="footer-link">联系我们</a>
        </div>
      </div>

      {/* 活动规则弹窗 */}
      <Modal
        title="活动规则"
        open={showRules}
        onCancel={() => setShowRules(false)}
        footer={null}
      >
        <Paragraph>
          <pre>{game.rules}</pre>
        </Paragraph>
        <Divider />
        <Row>
          <Col span={12}>
            <Statistic title="最低消费" value={`¥${game.min_consumption}`} />
          </Col>
          <Col span={12}>
            <Statistic title="每人参与次数" value={game.max_draws_per_user} />
          </Col>
        </Row>
      </Modal>

      {/* 奖品设置弹窗 */}
      <Modal
        title="奖品设置"
        open={showPrizes}
        onCancel={() => setShowPrizes(false)}
        footer={null}
        width={700}
      >
        <List
          itemLayout="horizontal"
          dataSource={prizes.filter(prize => !prize.is_default)}
          renderItem={item => (
            <List.Item>
              <List.Item.Meta
                avatar={<Avatar src={item.image_url || DEFAULT_PRIZE_ICONS.none} size={64} />}
                title={item.name}
                description={item.description}
              />
              <div>
                <Tag color="blue">概率: {item.probability}%</Tag>
                <Tag color="green">剩余: {item.remaining}</Tag>
              </div>
            </List.Item>
          )}
        />
      </Modal>

      {/* 中奖记录弹窗 */}
      <Modal
        title="中奖记录"
        open={showRecords}
        onCancel={() => setShowRecords(false)}
        footer={null}
      >
        <List
          itemLayout="horizontal"
          dataSource={records}
          renderItem={item => (
            <List.Item>
              <List.Item.Meta
                avatar={<Avatar icon={<UserOutlined />} />}
                title={item.user_name}
                description={`获得 ${item.prize_name}`}
              />
              <div>{item.created_at}</div>
            </List.Item>
          )}
        />
      </Modal>

      {/* 抽奖结果弹窗 */}
      <Modal
        title="刮卡结果"
        open={showResult}
        onCancel={() => setShowResult(false)}
        footer={[
          <Button key="back" onClick={() => setShowResult(false)}>
            关闭
          </Button>,
          <Button
            key="share"
            type="primary"
            icon={<ShareAltOutlined />}
            onClick={() => {
              message.success('分享成功');
              setShowResult(false);
            }}
          >
            分享
          </Button>
        ]}
        width={500}
      >
        {winningPrize && (
          <Result
            icon={winningPrize.is_default ? <MehOutlined /> : <SmileOutlined />}
            status={winningPrize.is_default ? 'warning' : 'success'}
            title={winningPrize.is_default ? '很遗憾，未中奖' : '恭喜您，中奖了！'}
            subTitle={winningPrize.description}
            extra={
              !winningPrize.is_default && (
                <div className="prize-result">
                  <img src={winningPrize.image_url || DEFAULT_PRIZE_ICONS.none} alt={winningPrize.name} className="prize-result-image" />
                  <div className="prize-result-info">
                    <Title level={4} className="prize-result-title">{winningPrize.name}</Title>
                    <Paragraph className="prize-result-description">{winningPrize.description}</Paragraph>
                  </div>
                </div>
              )
            }
          />
        )}

        <Divider />

        <Space direction="vertical" style={{ width: '100%', textAlign: 'center' }}>
          <Text>剩余参与次数: {game.remaining_draws}</Text>
          {game.remaining_draws > 0 && (
            <Button
              type="primary"
              onClick={() => {
                setShowResult(false);
                setTimeout(() => {
                  startNewScratchCard();
                }, 500);
              }}
            >
              继续参与
            </Button>
          )}
        </Space>
      </Modal>
    </div>
  );
};

export default ScratchCard;
