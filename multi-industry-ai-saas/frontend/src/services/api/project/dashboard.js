import api from '../httpClient';
import { getProjectId } from '../httpClient';

/**
 * 项目仪表盘API
 */
const dashboard = {
  /**
   * 获取项目管理仪表盘数据
   * @param {Object} params - 查询参数
   * @param {string} params.start_date - 开始日期
   * @param {string} params.end_date - 结束日期
   * @returns {Promise} - 项目管理仪表盘数据
   */
  getProjectDashboard: async (params = {}) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.get(`/project/${projectId}/dashboard/project`, { params });
  },

  /**
   * 获取运营仪表盘数据
   * @param {Object} params - 查询参数
   * @param {string} params.start_date - 开始日期
   * @param {string} params.end_date - 结束日期
   * @param {string} [params.store_id] - 门店ID
   * @returns {Promise} - 运营仪表盘数据
   */
  getOperationsDashboard: async (params = {}) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.get(`/project/${projectId}/dashboard/operations`, { params });
  },

  /**
   * 获取财务仪表盘数据
   * @param {Object} params - 查询参数
   * @param {string} params.start_date - 开始日期
   * @param {string} params.end_date - 结束日期
   * @param {string} [params.store_id] - 门店ID
   * @returns {Promise} - 财务仪表盘数据
   */
  getFinanceDashboard: async (params = {}) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.get(`/project/${projectId}/dashboard/finance`, { params });
  },

  /**
   * 获取仓储仪表盘数据
   * @param {Object} params - 查询参数
   * @param {string} [params.warehouse_id] - 仓库ID
   * @returns {Promise} - 仓储仪表盘数据
   */
  getWarehouseDashboard: async (params = {}) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.get(`/project/${projectId}/dashboard/warehouse`, { params });
  },

  /**
   * 获取采购仪表盘数据
   * @param {Object} params - 查询参数
   * @param {string} params.start_date - 开始日期
   * @param {string} params.end_date - 结束日期
   * @param {string} [params.supplier_id] - 供应商ID
   * @returns {Promise} - 采购仪表盘数据
   */
  getPurchaseDashboard: async (params = {}) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.get(`/project/${projectId}/dashboard/purchase`, { params });
  },

  /**
   * 获取门店仪表盘数据
   * @param {Object} params - 查询参数
   * @param {string} params.start_date - 开始日期
   * @param {string} params.end_date - 结束日期
   * @param {string} [params.store_id] - 门店ID
   * @returns {Promise} - 门店仪表盘数据
   */
  getStoreDashboard: async (params = {}) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.get(`/project/${projectId}/dashboard/store`, { params });
  },

  /**
   * 获取仪表盘AI推荐
   * @param {string} dashboardType - 仪表盘类型: project, operations, finance, warehouse, purchase, store
   * @returns {Promise} - AI推荐数据
   */
  getAIRecommendations: async (dashboardType) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.get(`/project/${projectId}/dashboard/${dashboardType}/ai-recommendations`);
  },
  
  /**
   * 提交AI推荐反馈
   * @param {Object} data - 反馈数据
   * @param {string} data.recommendation_id - 推荐ID
   * @param {boolean} data.is_helpful - 是否有帮助
   * @param {string} data.feedback_type - 反馈类型: project, operations, finance, warehouse, purchase, store
   * @param {string} [data.feedback_content] - 反馈内容
   * @returns {Promise} - 反馈结果
   */
  submitAIFeedback: async (data) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.post(`/project/${projectId}/dashboard/ai-feedback`, data);
  }
};

export default dashboard;
