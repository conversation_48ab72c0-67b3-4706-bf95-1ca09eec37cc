#!/usr/bin/env python
# -*- coding: utf-8 -*-

import logging
from typing import List, Dict, Any
from enum import Enum
from fastapi import HTTPException, status
from sqlalchemy.orm import Session
from sqlalchemy import select, and_

from models.user import User
from models.project import Project

logger = logging.getLogger(__name__)

class DingTalkPermission(str, Enum):
    """钉钉插件权限枚举"""
    
    # 基础权限
    VIEW_PLUGIN = "view_plugin"
    USE_BASIC_FEATURES = "use_basic_features"
    
    # 用户相关权限
    VIEW_USER_INFO = "view_user_info"
    MANAGE_USERS = "manage_users"
    
    # AI功能权限
    USE_AI_CHAT = "use_ai_chat"
    MANAGE_AI_INTEGRATION = "manage_ai_integration"
    
    # 消息相关权限
    SEND_MESSAGE = "send_message"
    SEND_NOTIFICATION = "send_notification"
    
    # 管理权限
    MANAGE_SETTINGS = "manage_settings"
    MANAGE_WEBHOOKS = "manage_webhooks"
    SYNC_KNOWLEDGE = "sync_knowledge"
    
    # 系统权限
    VIEW_LOGS = "view_logs"
    VIEW_SYSTEM_STATUS = "view_system_status"
    
    # 高级功能权限
    PLUGIN_CONFIG = "plugin_config"
    SYSTEM_INTEGRATION = "system_integration"
    
    # 组织架构权限
    SYNC_ORGANIZATION = "sync_organization"
    VIEW_ORGANIZATION = "view_organization"
    MANAGE_ORGANIZATION = "manage_organization"
    
    # 待办任务权限
    MANAGE_TODO_TASKS = "manage_todo_tasks"
    VIEW_TODO_TASKS = "view_todo_tasks"
    SYNC_TODO_TASKS = "sync_todo_tasks"
    CREATE_TODO_TASKS = "create_todo_tasks"
    UPDATE_TODO_TASKS = "update_todo_tasks"
    DELETE_TODO_TASKS = "delete_todo_tasks"
    
    # 审批流程权限
    MANAGE_APPROVAL = "manage_approval"
    VIEW_APPROVAL = "view_approval"
    CREATE_APPROVAL = "create_approval"
    APPROVE_INSTANCE = "approve_instance"
    REVOKE_APPROVAL = "revoke_approval"
    VIEW_APPROVAL_STATISTICS = "view_approval_statistics"
    
    # 文档管理权限
    SYNC_DOCUMENTS = "sync_documents"
    MANAGE_DOCUMENTS = "manage_documents"
    VIEW_DOCUMENTS = "view_documents"
    UPLOAD_DOCUMENTS = "upload_documents"
    
    # 统计权限
    VIEW_STATISTICS = "view_statistics"

class DingTalkPermissionManager:
    """钉钉插件权限管理器"""
    
    # 角色权限映射 - 与前端RBAC系统保持一致
    ROLE_PERMISSIONS = {
        "super_admin": [
            # 所有权限
            DingTalkPermission.VIEW_PLUGIN,
            DingTalkPermission.USE_BASIC_FEATURES,
            DingTalkPermission.VIEW_USER_INFO,
            DingTalkPermission.MANAGE_USERS,
            DingTalkPermission.USE_AI_CHAT,
            DingTalkPermission.MANAGE_AI_INTEGRATION,
            DingTalkPermission.SEND_MESSAGE,
            DingTalkPermission.SEND_NOTIFICATION,
            DingTalkPermission.MANAGE_SETTINGS,
            DingTalkPermission.MANAGE_WEBHOOKS,
            DingTalkPermission.SYNC_KNOWLEDGE,
            DingTalkPermission.VIEW_LOGS,
            DingTalkPermission.VIEW_SYSTEM_STATUS,
            DingTalkPermission.SYNC_ORGANIZATION,
            DingTalkPermission.VIEW_ORGANIZATION,
            DingTalkPermission.MANAGE_TODO_TASKS,
            DingTalkPermission.VIEW_TODO_TASKS,
            DingTalkPermission.SYNC_TODO_TASKS,
            DingTalkPermission.SYNC_DOCUMENTS,
            DingTalkPermission.MANAGE_DOCUMENTS,
            DingTalkPermission.VIEW_DOCUMENTS,
            DingTalkPermission.VIEW_STATISTICS,
            DingTalkPermission.SYSTEM_INTEGRATION,
        ],
        "tenant_admin": [
            # 租户管理员权限
            DingTalkPermission.VIEW_PLUGIN,
            DingTalkPermission.USE_BASIC_FEATURES,
            DingTalkPermission.VIEW_USER_INFO,
            DingTalkPermission.MANAGE_USERS,
            DingTalkPermission.USE_AI_CHAT,
            DingTalkPermission.MANAGE_AI_INTEGRATION,
            DingTalkPermission.SEND_MESSAGE,
            DingTalkPermission.SEND_NOTIFICATION,
            DingTalkPermission.MANAGE_SETTINGS,
            DingTalkPermission.MANAGE_WEBHOOKS,
            DingTalkPermission.SYNC_KNOWLEDGE,
            DingTalkPermission.VIEW_LOGS,
            DingTalkPermission.VIEW_SYSTEM_STATUS,
            DingTalkPermission.SYNC_ORGANIZATION,
            DingTalkPermission.VIEW_ORGANIZATION,
            DingTalkPermission.MANAGE_TODO_TASKS,
            DingTalkPermission.VIEW_TODO_TASKS,
            DingTalkPermission.SYNC_TODO_TASKS,
            DingTalkPermission.SYNC_DOCUMENTS,
            DingTalkPermission.MANAGE_DOCUMENTS,
            DingTalkPermission.VIEW_DOCUMENTS,
            DingTalkPermission.VIEW_STATISTICS,
        ],
        "project_admin": [
            # 项目管理员权限
            DingTalkPermission.VIEW_PLUGIN,
            DingTalkPermission.USE_BASIC_FEATURES,
            DingTalkPermission.VIEW_USER_INFO,
            DingTalkPermission.MANAGE_USERS,
            DingTalkPermission.USE_AI_CHAT,
            DingTalkPermission.MANAGE_AI_INTEGRATION,
            DingTalkPermission.SEND_MESSAGE,
            DingTalkPermission.SEND_NOTIFICATION,
            DingTalkPermission.MANAGE_SETTINGS,
            DingTalkPermission.MANAGE_WEBHOOKS,
            DingTalkPermission.SYNC_KNOWLEDGE,
            DingTalkPermission.VIEW_LOGS,
            DingTalkPermission.VIEW_SYSTEM_STATUS,
            # 组织架构权限
            DingTalkPermission.SYNC_ORGANIZATION,
            DingTalkPermission.VIEW_ORGANIZATION,
            DingTalkPermission.MANAGE_ORGANIZATION,
            # 待办任务权限
            DingTalkPermission.MANAGE_TODO_TASKS,
            DingTalkPermission.VIEW_TODO_TASKS,
            DingTalkPermission.SYNC_TODO_TASKS,
            DingTalkPermission.CREATE_TODO_TASKS,
            DingTalkPermission.UPDATE_TODO_TASKS,
            DingTalkPermission.DELETE_TODO_TASKS,
            # 审批流程权限
            DingTalkPermission.MANAGE_APPROVAL,
            DingTalkPermission.VIEW_APPROVAL,
            DingTalkPermission.CREATE_APPROVAL,
            DingTalkPermission.APPROVE_INSTANCE,
            DingTalkPermission.REVOKE_APPROVAL,
            DingTalkPermission.VIEW_APPROVAL_STATISTICS,
            # 文档管理权限
            DingTalkPermission.SYNC_DOCUMENTS,
            DingTalkPermission.MANAGE_DOCUMENTS,
            DingTalkPermission.VIEW_DOCUMENTS,
            DingTalkPermission.UPLOAD_DOCUMENTS,
            DingTalkPermission.VIEW_STATISTICS,
        ],
        "operation_admin": [
            # 运营管理员权限（项目管理员的子集）
            DingTalkPermission.VIEW_PLUGIN,
            DingTalkPermission.USE_BASIC_FEATURES,
            DingTalkPermission.VIEW_USER_INFO,
            DingTalkPermission.USE_AI_CHAT,
            DingTalkPermission.SEND_MESSAGE,
            DingTalkPermission.SYNC_KNOWLEDGE,
            DingTalkPermission.VIEW_LOGS,
            # 组织架构权限（只查看）
            DingTalkPermission.VIEW_ORGANIZATION,
            # 待办任务权限（查看和同步）
            DingTalkPermission.VIEW_TODO_TASKS,
            DingTalkPermission.SYNC_TODO_TASKS,
            # 审批流程权限（查看和统计）
            DingTalkPermission.VIEW_APPROVAL,
            DingTalkPermission.VIEW_APPROVAL_STATISTICS,
            # 文档管理权限（查看）
            DingTalkPermission.VIEW_DOCUMENTS,
            # 统计权限
            DingTalkPermission.VIEW_STATISTICS,
        ],
        "user": [
            # 普通用户权限
            DingTalkPermission.VIEW_PLUGIN,
            DingTalkPermission.USE_BASIC_FEATURES,
            DingTalkPermission.VIEW_USER_INFO,
            DingTalkPermission.USE_AI_CHAT,
            DingTalkPermission.SEND_MESSAGE,
        ]
    }
    
    @classmethod
    def get_user_roles(cls, user: User, project: Project) -> List[str]:
        """
        获取用户在钉钉插件中的角色列表
        
        Args:
            user: 用户对象
            project: 项目对象
            
        Returns:
            用户角色列表
        """
        roles = []
        
        # 系统级角色
        if hasattr(user, 'is_system_admin') and user.is_system_admin:
            roles.append("system_admin")
        if hasattr(user, 'is_super_admin') and user.is_super_admin:
            roles.append("super_admin")
        if hasattr(user, 'is_tenant_admin') and user.is_tenant_admin:
            roles.append("tenant_admin")
        
        # 项目级角色 - 从动态属性获取
        if hasattr(user, 'is_project_admin') and user.is_project_admin:
            roles.append("project_admin")
        
        # 从项目角色代码中获取具体角色
        if hasattr(user, 'project_role') and user.project_role:
            project_role = user.project_role.lower()
            
            # 直接添加项目角色代码
            roles.append(project_role)
            
            # 处理特定角色映射
            if project_role == "project_admin":
                roles.append("project_admin")
            elif project_role == "store_admin":
                roles.append("store_admin")
            elif project_role == "operation_admin":
                roles.append("operation_admin")
            elif project_role == "purchase_admin":
                roles.append("purchase_admin")
            elif project_role == "finance_admin":
                roles.append("finance_admin")
            elif project_role == "inventory_admin":
                roles.append("inventory_admin")
            elif project_role == "store_staff":
                roles.append("store_staff")
            elif project_role == "operation_staff":
                roles.append("operation_staff")
            elif project_role == "purchase_staff":
                roles.append("purchase_staff")
            elif project_role == "finance_staff":
                roles.append("finance_staff")
            elif project_role == "inventory_staff":
                roles.append("inventory_staff")
            elif project_role == "user":
                roles.append("user")
        
        # 如果没有任何角色，默认为普通用户
        if not roles:
            roles.append("user")
        
        logger.debug(f"用户 {user.id} 在项目 {project.id} 中的角色: {roles}")
        return list(set(roles))  # 去重
    
    @classmethod
    def has_permission(cls, user: User, project: Project, permission: DingTalkPermission) -> bool:
        """
        检查用户是否拥有指定权限
        
        Args:
            user: 用户对象
            project: 项目对象
            permission: 权限枚举
            
        Returns:
            是否拥有权限
        """
        user_roles = cls.get_user_roles(user, project)
        
        for role in user_roles:
            role_permissions = cls.ROLE_PERMISSIONS.get(role, [])
            
            # 检查通配符权限
            if "*" in role_permissions:
                return True
            
            # 检查具体权限
            if permission in role_permissions:
                return True
        
        return False
    
    @classmethod
    def check_permission(cls, user: User, project: Project, permission: DingTalkPermission):
        """
        检查权限，如果没有权限则抛出异常
        
        Args:
            user: 用户对象
            project: 项目对象
            permission: 权限枚举
            
        Raises:
            HTTPException: 权限不足时抛出403异常
        """
        if not cls.has_permission(user, project, permission):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"权限不足：需要 {permission.value} 权限"
            )
    
    @classmethod
    def get_user_permissions(cls, user: User, project: Project) -> List[DingTalkPermission]:
        """
        获取用户的所有权限
        
        Args:
            user: 用户对象
            project: 项目对象
            
        Returns:
            权限列表
        """
        user_roles = cls.get_user_roles(user, project)
        permissions = set()
        
        for role in user_roles:
            role_permissions = cls.ROLE_PERMISSIONS.get(role, [])
            
            # 如果有通配符权限，返回所有权限
            if "*" in role_permissions:
                return list(DingTalkPermission)
            
            permissions.update(role_permissions)
        
        return list(permissions)
    
    @classmethod
    def get_user_capabilities(cls, user: User, project: Project) -> Dict[str, Any]:
        """
        获取用户能力描述
        
        Args:
            user: 用户对象
            project: 项目对象
            
        Returns:
            用户能力字典
        """
        roles = cls.get_user_roles(user, project)
        permissions = cls.get_user_permissions(user, project)
        
        # 确定主要角色
        primary_role = "user"
        if "system_admin" in roles or "super_admin" in roles:
            primary_role = "super_admin"
        elif "tenant_admin" in roles:
            primary_role = "admin"
        elif "project_admin" in roles:
            primary_role = "admin"
        elif any("admin" in role for role in roles):
            primary_role = "admin"
        
        capabilities = {
            "role": primary_role,
            "role_name": cls._get_role_name(primary_role),
            "roles": roles,
            "permissions": [p.value for p in permissions],
            "features": cls._get_available_features(permissions)
        }
        
        return capabilities
    
    @classmethod
    def _get_role_name(cls, role: str) -> str:
        """获取角色中文名称"""
        role_names = {
            "user": "普通用户",
            "admin": "管理员",
            "super_admin": "超级管理员"
        }
        return role_names.get(role, "未知角色")
    
    @classmethod
    def _get_available_features(cls, permissions: List[DingTalkPermission]) -> Dict[str, bool]:
        """根据权限获取可用功能"""
        features = {
            # 基础功能
            "user_management": DingTalkPermission.VIEW_USER_INFO in permissions or DingTalkPermission.MANAGE_USERS in permissions,
            "ai_chat": DingTalkPermission.USE_AI_CHAT in permissions,
            "message_sending": DingTalkPermission.SEND_MESSAGE in permissions or DingTalkPermission.SEND_NOTIFICATION in permissions,
            
            # 管理功能
            "plugin_settings": DingTalkPermission.MANAGE_SETTINGS in permissions,
            "webhook_management": DingTalkPermission.MANAGE_WEBHOOKS in permissions,
            "log_viewing": DingTalkPermission.VIEW_LOGS in permissions,
            "knowledge_sync": DingTalkPermission.SYNC_KNOWLEDGE in permissions,
            "ai_integration": DingTalkPermission.MANAGE_AI_INTEGRATION in permissions,
            
            # 组织架构功能
            "organization_management": DingTalkPermission.MANAGE_ORGANIZATION in permissions or DingTalkPermission.SYNC_ORGANIZATION in permissions,
            "organization_sync": DingTalkPermission.SYNC_ORGANIZATION in permissions,
            "organization_view": DingTalkPermission.VIEW_ORGANIZATION in permissions,
            
            # 待办任务功能
            "todo_task_management": DingTalkPermission.MANAGE_TODO_TASKS in permissions,
            "todo_task_view": DingTalkPermission.VIEW_TODO_TASKS in permissions,
            "todo_task_sync": DingTalkPermission.SYNC_TODO_TASKS in permissions,
            "todo_task_create": DingTalkPermission.CREATE_TODO_TASKS in permissions,
            "todo_task_update": DingTalkPermission.UPDATE_TODO_TASKS in permissions,
            "todo_task_delete": DingTalkPermission.DELETE_TODO_TASKS in permissions,
            
            # 审批流程功能
            "approval_management": DingTalkPermission.MANAGE_APPROVAL in permissions,
            "approval_view": DingTalkPermission.VIEW_APPROVAL in permissions,
            "approval_create": DingTalkPermission.CREATE_APPROVAL in permissions,
            "approval_approve": DingTalkPermission.APPROVE_INSTANCE in permissions,
            "approval_revoke": DingTalkPermission.REVOKE_APPROVAL in permissions,
            "approval_statistics": DingTalkPermission.VIEW_APPROVAL_STATISTICS in permissions,
            
            # 文档管理功能
            "document_management": DingTalkPermission.MANAGE_DOCUMENTS in permissions,
            "document_sync": DingTalkPermission.SYNC_DOCUMENTS in permissions,
            "document_view": DingTalkPermission.VIEW_DOCUMENTS in permissions,
            "document_upload": DingTalkPermission.UPLOAD_DOCUMENTS in permissions,
            
            # 高级功能
            "plugin_config": DingTalkPermission.PLUGIN_CONFIG in permissions,
            "system_integration": DingTalkPermission.SYSTEM_INTEGRATION in permissions,
            "statistics": DingTalkPermission.VIEW_SYSTEM_STATUS in permissions,
        }
        
        return features

# 权限装饰器
def require_permission(permission: DingTalkPermission):
    """权限检查装饰器"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            # 从参数中获取用户和项目对象
            current_user = kwargs.get('current_user')
            current_project = kwargs.get('current_project') or kwargs.get('project')
            
            if not current_user or not current_project:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="缺少用户或项目信息"
                )
            
            # 检查权限
            DingTalkPermissionManager.check_permission(current_user, current_project, permission)
            
            return func(*args, **kwargs)
        return wrapper
    return decorator 