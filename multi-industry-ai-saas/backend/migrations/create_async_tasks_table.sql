-- 创建async_tasks表
-- 用于存储异步任务的状态和结果

CREATE TABLE IF NOT EXISTS async_tasks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID REFERENCES tenants(id) ON DELETE CASCADE,
    project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    task_type VARCHAR(50) NOT NULL,
    task_name VARCHAR(100),
    status VARCHAR(20) NOT NULL DEFAULT 'pending',
    priority INTEGER DEFAULT 0,
    progress FLOAT DEFAULT 0.0,
    message TEXT,
    input_data JSONB,
    result JSONB,
    error_message TEXT,
    retry_count INTEGER DEFAULT 0,
    max_retries INTEGER DEFAULT 3,
    timeout_seconds INTEGER DEFAULT 300,
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_async_tasks_tenant_id ON async_tasks(tenant_id);
CREATE INDEX IF NOT EXISTS idx_async_tasks_project_id ON async_tasks(project_id);
CREATE INDEX IF NOT EXISTS idx_async_tasks_user_id ON async_tasks(user_id);
CREATE INDEX IF NOT EXISTS idx_async_tasks_status ON async_tasks(status);
CREATE INDEX IF NOT EXISTS idx_async_tasks_task_type ON async_tasks(task_type);
CREATE INDEX IF NOT EXISTS idx_async_tasks_created_at ON async_tasks(created_at);
CREATE INDEX IF NOT EXISTS idx_async_tasks_priority ON async_tasks(priority);

-- 创建复合索引
CREATE INDEX IF NOT EXISTS idx_async_tasks_project_status ON async_tasks(project_id, status);
CREATE INDEX IF NOT EXISTS idx_async_tasks_user_status ON async_tasks(user_id, status);

-- 添加约束
ALTER TABLE async_tasks 
ADD CONSTRAINT chk_async_tasks_status 
CHECK (status IN ('pending', 'running', 'success', 'failed', 'cancelled'));

ALTER TABLE async_tasks 
ADD CONSTRAINT chk_async_tasks_progress 
CHECK (progress >= 0.0 AND progress <= 100.0);

ALTER TABLE async_tasks 
ADD CONSTRAINT chk_async_tasks_retry_count 
CHECK (retry_count >= 0);

ALTER TABLE async_tasks 
ADD CONSTRAINT chk_async_tasks_max_retries 
CHECK (max_retries >= 0);

-- 创建更新时间触发器
CREATE OR REPLACE FUNCTION update_async_tasks_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS trigger_update_async_tasks_updated_at ON async_tasks;
CREATE TRIGGER trigger_update_async_tasks_updated_at
    BEFORE UPDATE ON async_tasks
    FOR EACH ROW
    EXECUTE FUNCTION update_async_tasks_updated_at();

-- 添加注释
COMMENT ON TABLE async_tasks IS '异步任务表';
COMMENT ON COLUMN async_tasks.id IS '任务ID';
COMMENT ON COLUMN async_tasks.tenant_id IS '租户ID';
COMMENT ON COLUMN async_tasks.project_id IS '项目ID';
COMMENT ON COLUMN async_tasks.user_id IS '用户ID';
COMMENT ON COLUMN async_tasks.task_type IS '任务类型';
COMMENT ON COLUMN async_tasks.task_name IS '任务名称';
COMMENT ON COLUMN async_tasks.status IS '任务状态: pending, running, success, failed, cancelled';
COMMENT ON COLUMN async_tasks.priority IS '任务优先级，数字越大优先级越高';
COMMENT ON COLUMN async_tasks.progress IS '任务进度 (0-100)';
COMMENT ON COLUMN async_tasks.message IS '任务消息';
COMMENT ON COLUMN async_tasks.input_data IS '输入数据(JSON)';
COMMENT ON COLUMN async_tasks.result IS '任务结果(JSON)';
COMMENT ON COLUMN async_tasks.error_message IS '错误消息';
COMMENT ON COLUMN async_tasks.retry_count IS '重试次数';
COMMENT ON COLUMN async_tasks.max_retries IS '最大重试次数';
COMMENT ON COLUMN async_tasks.timeout_seconds IS '超时时间(秒)';
COMMENT ON COLUMN async_tasks.started_at IS '开始时间';
COMMENT ON COLUMN async_tasks.completed_at IS '完成时间';
COMMENT ON COLUMN async_tasks.created_at IS '创建时间';
COMMENT ON COLUMN async_tasks.updated_at IS '更新时间'; 