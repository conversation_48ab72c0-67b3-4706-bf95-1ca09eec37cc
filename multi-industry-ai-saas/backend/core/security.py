#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
安全相关工具函数
"""

import os
import base64
import hashlib
from core.config import settings

# 使用统一的SECRET_KEY
SECRET_KEY = settings.SECRET_KEY

def encrypt_text(text):
    """
    简单加密文本

    注意：这不是一个安全的加密方法，仅用于基本的混淆
    在生产环境中，应该使用更安全的加密方法，如 cryptography.fernet
    """
    if not text:
        return text

    # 使用 base64 编码和简单的 XOR 加密
    key = hashlib.sha256(SECRET_KEY.encode()).digest()
    text_bytes = text.encode()
    encrypted_bytes = bytearray()

    for i, byte in enumerate(text_bytes):
        key_byte = key[i % len(key)]
        encrypted_bytes.append(byte ^ key_byte)

    return base64.urlsafe_b64encode(encrypted_bytes).decode()

def decrypt_text(encrypted_text):
    """
    简单解密文本

    注意：这不是一个安全的解密方法，仅用于基本的混淆
    在生产环境中，应该使用更安全的解密方法，如 cryptography.fernet
    """
    if not encrypted_text:
        return encrypted_text

    try:
        # 使用 base64 解码和简单的 XOR 解密
        key = hashlib.sha256(SECRET_KEY.encode()).digest()
        encrypted_bytes = base64.urlsafe_b64decode(encrypted_text.encode())
        decrypted_bytes = bytearray()

        for i, byte in enumerate(encrypted_bytes):
            key_byte = key[i % len(key)]
            decrypted_bytes.append(byte ^ key_byte)

        return decrypted_bytes.decode()
    except Exception as e:
        # 如果解密失败，返回原文本
        # 这可能发生在密钥更改或文本不是加密格式的情况下
        return encrypted_text
