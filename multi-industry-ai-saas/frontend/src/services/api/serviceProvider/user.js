import api from '../httpClient';
import { getServiceProviderId } from '../httpClient';

/**
 * 服务商-用户管理API
 */
const user = {
  /**
   * 获取用户列表
   * @param {Object} params - 查询参数
   * @returns {Promise} - 用户列表
   */
  getList: async (params = {}) => {
    const serviceProviderId = getServiceProviderId();
    if (!serviceProviderId) {
      throw new Error('未指定服务商ID');
    }
    return api.get('/service-provider/users', { params });
  },

  /**
   * 获取用户详情
   * @param {string} id - 用户ID
   * @returns {Promise} - 用户详情
   */
  getDetail: async (id) => {
    const serviceProviderId = getServiceProviderId();
    if (!serviceProviderId) {
      throw new Error('未指定服务商ID');
    }
    return api.get(`/service-provider/users/${id}`);
  },

  /**
   * 创建用户
   * @param {Object} data - 用户数据
   * @returns {Promise} - 创建结果
   */
  create: async (data) => {
    const serviceProviderId = getServiceProviderId();
    if (!serviceProviderId) {
      throw new Error('未指定服务商ID');
    }
    return api.post('/service-provider/users', data);
  },

  /**
   * 更新用户
   * @param {string} id - 用户ID
   * @param {Object} data - 用户数据
   * @returns {Promise} - 更新结果
   */
  update: async (id, data) => {
    const serviceProviderId = getServiceProviderId();
    if (!serviceProviderId) {
      throw new Error('未指定服务商ID');
    }
    return api.put(`/service-provider/users/${id}`, data);
  },

  /**
   * 删除用户
   * @param {string} id - 用户ID
   * @returns {Promise} - 删除结果
   */
  delete: async (id) => {
    const serviceProviderId = getServiceProviderId();
    if (!serviceProviderId) {
      throw new Error('未指定服务商ID');
    }
    return api.delete(`/service-provider/users/${id}`);
  },

  /**
   * 获取服务商管理员列表
   * @param {Object} params - 查询参数
   * @returns {Promise} - 服务商管理员列表
   */
  getAdmins: async (params = {}) => {
    const serviceProviderId = getServiceProviderId();
    if (!serviceProviderId) {
      throw new Error('未指定服务商ID');
    }
    return api.get('/service-provider/admins', { params });
  },

  /**
   * 创建服务商管理员
   * @param {Object} data - 管理员数据
   * @returns {Promise} - 创建结果
   */
  createAdmin: async (data) => {
    const serviceProviderId = getServiceProviderId();
    if (!serviceProviderId) {
      throw new Error('未指定服务商ID');
    }
    return api.post('/service-provider/admins', data);
  },

  /**
   * 更新服务商管理员
   * @param {string} id - 管理员ID
   * @param {Object} data - 管理员数据
   * @returns {Promise} - 更新结果
   */
  updateAdmin: async (id, data) => {
    const serviceProviderId = getServiceProviderId();
    if (!serviceProviderId) {
      throw new Error('未指定服务商ID');
    }
    return api.put(`/service-provider/admins/${id}`, data);
  },

  /**
   * 删除服务商管理员
   * @param {string} id - 管理员ID
   * @returns {Promise} - 删除结果
   */
  deleteAdmin: async (id) => {
    const serviceProviderId = getServiceProviderId();
    if (!serviceProviderId) {
      throw new Error('未指定服务商ID');
    }
    return api.delete(`/service-provider/admins/${id}`);
  }
};

export default user;
