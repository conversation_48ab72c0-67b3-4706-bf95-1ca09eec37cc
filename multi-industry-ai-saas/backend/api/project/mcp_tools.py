#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
MCP工具管理API
"""

import logging
from typing import Optional, List
from uuid import UUID
from fastapi import APIRouter, Depends, HTTPException, Query, Path, status
from sqlalchemy.ext.asyncio import AsyncSession

from db.database import get_db
from models.project import Project
from core.auth import get_current_user
from api.deps import get_current_project
from services.mcp_tool import MCPToolService
from schemas.mcp_tool import (
    MCPToolCreate,
    MCPToolUpdate,
    MCPToolResponse,
    MCPToolListResponse,
    MCPToolExecuteRequest,
    MCPToolExecuteResponse,
    MCPToolTestRequest,
    MCPToolTestResponse,
    MCPToolUsageListResponse
)

logger = logging.getLogger(__name__)
router = APIRouter()

@router.get("/mcp-tools", response_model=MCPToolListResponse)
async def list_mcp_tools(
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db),
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    name: Optional[str] = Query(None),
    server_type: Optional[str] = Query(None),
    is_enabled: Optional[bool] = Query(None),
    tags: Optional[List[str]] = Query(None),
):
    """获取MCP工具列表"""
    try:
        tools, total = await MCPToolService.get_tools(
            db=db,
            project_id=project.id,
            skip=skip,
            limit=limit,
            name=name,
            server_type=server_type,
            is_enabled=is_enabled,
            tags=tags
        )

        return {
            "success": True,
            "message": "获取MCP工具列表成功",
            "data": tools,
            "total": total,
            "page": skip // limit + 1,
            "page_size": limit,
        }
    except Exception as e:
        logger.error(f"获取MCP工具列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取MCP工具列表失败: {str(e)}",
        )

@router.get("/mcp-tools/usages", response_model=MCPToolUsageListResponse)
async def list_mcp_tool_usages(
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db),
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    tool_id: Optional[UUID] = Query(None),
    usage_status: Optional[str] = Query(None, alias="status"),
    session_id: Optional[str] = Query(None),
):
    """获取MCP工具使用记录"""
    try:
        usages, total = await MCPToolService.get_tool_usages(
            db=db,
            project_id=project.id,
            skip=skip,
            limit=limit,
            tool_id=tool_id,
            status=usage_status,
            session_id=session_id
        )

        return {
            "success": True,
            "message": "获取MCP工具使用记录成功",
            "data": usages,
            "total": total,
            "page": skip // limit + 1,
            "page_size": limit,
        }
    except Exception as e:
        logger.error(f"获取MCP工具使用记录失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取MCP工具使用记录失败: {str(e)}",
        )

@router.post("/mcp-tools/test-connection", response_model=MCPToolTestResponse)
async def test_mcp_tool_connection(
    test_data: MCPToolTestRequest,
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db),
):
    """测试MCP工具连接"""
    try:
        result = await MCPToolService.test_tool_connection(
            server_url=test_data.server_url,
            server_type=test_data.server_type,
            auth_type=test_data.auth_type,
            auth_config=test_data.auth_config,
            timeout=test_data.timeout
        )

        return result
    except Exception as e:
        logger.error(f"测试MCP工具连接失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"测试MCP工具连接失败: {str(e)}",
        )

@router.get("/mcp-tools/{tool_id}", response_model=MCPToolResponse)
async def get_mcp_tool(
    tool_id: UUID = Path(...),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db),
):
    """获取MCP工具详情"""
    try:
        tool = await MCPToolService.get_tool(db=db, tool_id=tool_id)

        if not tool:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"MCP工具 ID '{tool_id}' 不存在",
            )

        # 检查权限
        if tool.project_id and tool.project_id != project.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问该MCP工具",
            )

        return tool
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取MCP工具详情失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取MCP工具详情失败: {str(e)}",
        )

@router.post("/mcp-tools", response_model=MCPToolResponse, status_code=status.HTTP_201_CREATED)
async def create_mcp_tool(
    tool_data: MCPToolCreate,
    project: Project = Depends(get_current_project),
    current_user = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """创建MCP工具"""
    try:
        # 设置项目和租户ID
        tool_data.project_id = project.id
        tool_data.tenant_id = project.tenant_id

        tool = await MCPToolService.create_tool(
            db=db,
            tool_data=tool_data,
            user_id=current_user.get("id")
        )

        return tool
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )
    except Exception as e:
        logger.error(f"创建MCP工具失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建MCP工具失败: {str(e)}",
        )

@router.put("/mcp-tools/{tool_id}", response_model=MCPToolResponse)
async def update_mcp_tool(
    tool_id: UUID = Path(...),
    project: Project = Depends(get_current_project),
    current_user = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
    tool_data: MCPToolUpdate = None,
):
    """更新MCP工具"""
    try:
        # 获取工具
        tool = await MCPToolService.get_tool(db=db, tool_id=tool_id)

        if not tool:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"MCP工具 ID '{tool_id}' 不存在",
            )

        # 检查权限
        if tool.project_id != project.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权更新该MCP工具",
            )

        if tool.is_builtin:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="不能更新内置工具",
            )

        updated_tool = await MCPToolService.update_tool(
            db=db,
            tool_id=tool_id,
            tool_data=tool_data,
            user_id=current_user.get("id")
        )

        return updated_tool
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新MCP工具失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新MCP工具失败: {str(e)}",
        )

@router.delete("/mcp-tools/{tool_id}")
async def delete_mcp_tool(
    tool_id: UUID = Path(...),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db),
):
    """删除MCP工具"""
    try:
        # 获取工具
        tool = await MCPToolService.get_tool(db=db, tool_id=tool_id)

        if not tool:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"MCP工具 ID '{tool_id}' 不存在",
            )

        # 检查权限
        if tool.project_id != project.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权删除该MCP工具",
            )

        success = await MCPToolService.delete_tool(db=db, tool_id=tool_id)

        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="MCP工具不存在",
            )

        return {"success": True, "message": "删除MCP工具成功"}
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除MCP工具失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除MCP工具失败: {str(e)}",
        )

@router.post("/mcp-tools/{tool_id}/execute", response_model=MCPToolExecuteResponse)
async def execute_mcp_tool(
    execute_data: MCPToolExecuteRequest,
    tool_id: UUID = Path(...),
    project: Project = Depends(get_current_project),
    current_user = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """执行MCP工具"""
    try:
        # 设置工具ID
        execute_data.tool_id = tool_id

        result = await MCPToolService.execute_tool(
            db=db,
            project_id=project.id,
            request=execute_data,
            user_id=current_user.get("id")
        )

        return result
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )
    except Exception as e:
        logger.error(f"执行MCP工具失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"执行MCP工具失败: {str(e)}",
        ) 