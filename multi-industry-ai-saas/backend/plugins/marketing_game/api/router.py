from typing import List, Optional
import uuid
import logging
from fastapi import APIRouter, Depends, HTTPException, Query, Path, Body, UploadFile, File, Form
from sqlalchemy.ext.asyncio import AsyncSession
from db.database import get_db
from models.project import Project
from models.plugin import Plugin
from core.auth import get_current_user
from sqlalchemy.future import select
from sqlalchemy import and_, desc

from ..models.models import MarketingGame, GamePrize, GameParticipant, GameRecord
from ..schemas.schemas import (
    MarketingGameCreate,
    MarketingGameUpdate,
    MarketingGameResponse,
    GamePrizeCreate,
    GamePrizeUpdate,
    GamePrizeResponse,
    GameParticipantCreate,
    GameParticipantResponse,
    GameRecordCreate,
    GameRecordResponse,
    ReceiptRecognitionResponse
)
from ..crud.crud import (
    create_marketing_game,
    get_marketing_game,
    get_marketing_games,
    update_marketing_game,
    delete_marketing_game,
    create_game_prize,
    get_game_prize,
    get_game_prizes,
    update_game_prize,
    delete_game_prize,
    create_game_participant,
    get_game_participant,
    get_game_participants,
    create_game_record,
    get_game_record,
    get_game_records
)
from ..utils.receipt_recognition import recognize_receipt
from ..utils.lottery_engine import LotteryEngine

logger = logging.getLogger(__name__)
router = APIRouter()

@router.get("/games", response_model=List[MarketingGameResponse])
async def list_marketing_games(
    skip: int = 0,
    limit: int = 100,
    status: Optional[str] = None,
    project_id: uuid.UUID = Path(..., description="项目ID"),
    current_user = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取营销游戏列表
    """
    # 使用查询参数获取项目ID
    if not project_id:
        raise HTTPException(status_code=400, detail="未指定项目ID")

    # 检查插件是否已安装
    plugin_query = select(Plugin).where(Plugin.code == "marketing_game")
    result = await db.execute(plugin_query)
    plugin = result.scalars().first()
    if not plugin:
        raise HTTPException(status_code=404, detail="营销游戏插件未安装")

    games = await get_marketing_games(db, project_id, skip, limit, status)
    return [MarketingGameResponse.from_orm(game) for game in games]

@router.post("/games", response_model=MarketingGameResponse)
async def create_new_marketing_game(
    game: MarketingGameCreate,
    project_id: uuid.UUID = Path(..., description="项目ID"),
    current_user = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    创建新的营销游戏
    """
    # 使用查询参数获取项目ID
    if not project_id:
        raise HTTPException(status_code=400, detail="未指定项目ID")

    # 检查插件是否已安装
    plugin_query = select(Plugin).where(Plugin.code == "marketing_game")
    result = await db.execute(plugin_query)
    plugin = result.scalars().first()
    if not plugin:
        raise HTTPException(status_code=404, detail="营销游戏插件未安装")

    db_game = await create_marketing_game(db, game, project_id)
    # 确保关联数据被正确处理
    response = MarketingGameResponse.from_orm(db_game)
    # 设置空的奖品列表，避免懒加载
    response.prizes = []
    return response

@router.get("/games/{game_id}", response_model=MarketingGameResponse)
async def get_marketing_game_detail(
    game_id: int = Path(...),
    project_id: uuid.UUID = Path(..., description="项目ID"),
    current_user = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取营销游戏详情
    """
    # 使用查询参数获取项目ID
    if not project_id:
        raise HTTPException(status_code=400, detail="未指定项目ID")

    game = await get_marketing_game(db, game_id, project_id)
    if not game:
        raise HTTPException(status_code=404, detail="营销游戏不存在")

    # 确保关联数据被正确处理
    response = MarketingGameResponse.from_orm(game)
    # 如果游戏有奖品，转换为响应模型
    if hasattr(game, 'prizes') and game.prizes:
        response.prizes = [GamePrizeResponse.from_orm(prize) for prize in game.prizes]
    else:
        response.prizes = []
    return response

@router.put("/games/{game_id}", response_model=MarketingGameResponse)
async def update_marketing_game_detail(
    game_id: int = Path(...),
    game_update: MarketingGameUpdate = Body(...),
    project_id: uuid.UUID = Path(..., description="项目ID"),
    current_user = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    更新营销游戏
    """
    # 使用查询参数获取项目ID
    if not project_id:
        raise HTTPException(status_code=400, detail="未指定项目ID")

    game = await get_marketing_game(db, game_id, project_id)
    if not game:
        raise HTTPException(status_code=404, detail="营销游戏不存在")

    updated_game = await update_marketing_game(db, game_id, game_update)
    if not updated_game:
        raise HTTPException(status_code=404, detail="更新失败或游戏不存在")

    # 确保关联数据被正确处理
    response = MarketingGameResponse.from_orm(updated_game)
    # 如果游戏有奖品，转换为响应模型
    if hasattr(updated_game, 'prizes') and updated_game.prizes:
        response.prizes = [GamePrizeResponse.from_orm(prize) for prize in updated_game.prizes]
    else:
        response.prizes = []
    return response

@router.delete("/games/{game_id}")
async def delete_marketing_game_detail(
    game_id: int = Path(...),
    project_id: uuid.UUID = Path(..., description="项目ID"),
    current_user = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    删除营销游戏
    """
    # 使用查询参数获取项目ID
    if not project_id:
        raise HTTPException(status_code=400, detail="未指定项目ID")

    game = await get_marketing_game(db, game_id, project_id)
    if not game:
        raise HTTPException(status_code=404, detail="营销游戏不存在")

    await delete_marketing_game(db, game_id)
    return {"message": "营销游戏已删除"}

# 奖品管理
@router.get("/games/{game_id}/prizes", response_model=List[GamePrizeResponse])
async def list_game_prizes(
    game_id: int = Path(...),
    project_id: uuid.UUID = Path(..., description="项目ID"),
    current_user = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取游戏奖品列表
    """
    # 使用查询参数获取项目ID
    if not project_id:
        raise HTTPException(status_code=400, detail="未指定项目ID")

    game = await get_marketing_game(db, game_id, project_id)
    if not game:
        raise HTTPException(status_code=404, detail="营销游戏不存在")

    prizes = await get_game_prizes(db, game_id)
    return [GamePrizeResponse.from_orm(prize) for prize in prizes]

@router.post("/games/{game_id}/prizes", response_model=GamePrizeResponse)
async def create_new_game_prize(
    game_id: int = Path(...),
    prize: GamePrizeCreate = Body(...),
    project_id: uuid.UUID = Path(..., description="项目ID"),
    current_user = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    创建游戏奖品
    """
    # 使用查询参数获取项目ID
    if not project_id:
        raise HTTPException(status_code=400, detail="未指定项目ID")

    game = await get_marketing_game(db, game_id, project_id)
    if not game:
        raise HTTPException(status_code=404, detail="营销游戏不存在")

    db_prize = await create_game_prize(db, prize, game_id)
    return GamePrizeResponse.from_orm(db_prize)

@router.get("/games/{game_id}/prizes/{prize_id}", response_model=GamePrizeResponse)
async def get_game_prize_detail(
    game_id: int = Path(...),
    prize_id: int = Path(...),
    project_id: uuid.UUID = Path(..., description="项目ID"),
    current_user = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取游戏奖品详情
    """
    # 使用查询参数获取项目ID
    if not project_id:
        raise HTTPException(status_code=400, detail="未指定项目ID")

    game = await get_marketing_game(db, game_id, project_id)
    if not game:
        raise HTTPException(status_code=404, detail="营销游戏不存在")

    prize = await get_game_prize(db, prize_id, game_id)
    if not prize:
        raise HTTPException(status_code=404, detail="奖品不存在")

    return GamePrizeResponse.from_orm(prize)

@router.put("/games/{game_id}/prizes/{prize_id}", response_model=GamePrizeResponse)
async def update_game_prize_detail(
    game_id: int = Path(...),
    prize_id: int = Path(...),
    prize_update: GamePrizeUpdate = Body(...),
    project_id: uuid.UUID = Path(..., description="项目ID"),
    current_user = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    更新游戏奖品
    """
    # 使用查询参数获取项目ID
    if not project_id:
        raise HTTPException(status_code=400, detail="未指定项目ID")

    game = await get_marketing_game(db, game_id, project_id)
    if not game:
        raise HTTPException(status_code=404, detail="营销游戏不存在")

    prize = await get_game_prize(db, prize_id, game_id)
    if not prize:
        raise HTTPException(status_code=404, detail="奖品不存在")

    updated_prize = await update_game_prize(db, prize_id, prize_update)
    if not updated_prize:
        raise HTTPException(status_code=404, detail="更新失败或奖品不存在")
    return GamePrizeResponse.from_orm(updated_prize)

@router.delete("/games/{game_id}/prizes/{prize_id}")
async def delete_game_prize_detail(
    game_id: int = Path(...),
    prize_id: int = Path(...),
    project_id: uuid.UUID = Path(..., description="项目ID"),
    current_user = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    删除游戏奖品
    """
    # 使用查询参数获取项目ID
    if not project_id:
        raise HTTPException(status_code=400, detail="未指定项目ID")

    game = await get_marketing_game(db, game_id, project_id)
    if not game:
        raise HTTPException(status_code=404, detail="营销游戏不存在")

    prize = await get_game_prize(db, prize_id, game_id)
    if not prize:
        raise HTTPException(status_code=404, detail="奖品不存在")

    await delete_game_prize(db, prize_id)
    return {"message": "奖品已删除"}

# 游戏记录
@router.get("/games/{game_id}/records", response_model=List[dict])
async def list_game_records(
    game_id: int = Path(...),
    skip: int = 0,
    limit: int = 100,
    project_id: uuid.UUID = Path(..., description="项目ID"),
    current_user = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取游戏记录列表
    """
    # 使用查询参数获取项目ID
    if not project_id:
        raise HTTPException(status_code=400, detail="未指定项目ID")

    game = await get_marketing_game(db, game_id, project_id)
    if not game:
        raise HTTPException(status_code=404, detail="营销游戏不存在")

    records = await get_game_records(db, game_id, skip, limit)
    
    # 手动构建返回数据，确保包含所有关联信息
    result = []
    for record in records:
        record_data = {
            "id": record.id,
            "game_id": record.game_id,
            "participant_id": record.participant_id,
            "prize_id": record.prize_id,
            "receipt_id": record.receipt_id,
            "consumption_amount": record.consumption_amount,
            "is_win": record.is_win,
            "draw_time": record.draw_time,
            "claim_status": record.claim_status,
            "claim_time": record.claim_time,
            "draw_method": record.draw_method,
            "probability_used": record.probability_used,
            "created_at": record.created_at,
            "updated_at": record.updated_at,
            "remark": record.remark,
            
            # 参与者信息
            "participant": {
                "id": record.participant.id if record.participant else None,
                "name": record.participant.name if record.participant else None,
                "phone": record.participant.phone if record.participant else None,
            } if record.participant else None,
            
            # 奖品信息
            "prize": {
                "id": record.prize.id if record.prize else None,
                "name": record.prize.name if record.prize else None,
                "value": record.prize.value if record.prize else None,
                "description": record.prize.description if record.prize else None,
                "image_url": record.prize.image_url if record.prize else None,
                "is_default": record.prize.is_default if record.prize else False,
            } if record.prize else None,
            
            # 为前端兼容性添加额外字段
            "user_name": record.participant.name if record.participant else None,
            "phone": record.participant.phone if record.participant else None,
            "prize_name": record.prize.name if record.prize else None,
            "prize_value": record.prize.value if record.prize else 0,
        }
        result.append(record_data)
    
    return result

@router.post("/games/{game_id}/records", response_model=GameRecordResponse)
async def create_new_game_record(
    game_id: int = Path(...),
    record: GameRecordCreate = Body(...),
    project_id: uuid.UUID = Path(..., description="项目ID"),
    current_user = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    创建游戏记录
    """
    # 使用查询参数获取项目ID
    if not project_id:
        raise HTTPException(status_code=400, detail="未指定项目ID")

    game = await get_marketing_game(db, game_id, project_id)
    if not game:
        raise HTTPException(status_code=404, detail="营销游戏不存在")

    db_record = await create_game_record(db, record, game_id)
    return GameRecordResponse.from_orm(db_record)

@router.put("/games/{game_id}/records/{record_id}", response_model=GameRecordResponse)
async def update_game_record(
    game_id: int = Path(...),
    record_id: int = Path(...),
    record_update: dict = Body(...),
    project_id: uuid.UUID = Path(..., description="项目ID"),
    current_user = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    更新游戏记录
    """
    # 使用查询参数获取项目ID
    if not project_id:
        raise HTTPException(status_code=400, detail="未指定项目ID")

    game = await get_marketing_game(db, game_id, project_id)
    if not game:
        raise HTTPException(status_code=404, detail="营销游戏不存在")

    # 获取记录
    stmt = select(GameRecord).where(
        and_(
            GameRecord.id == record_id,
            GameRecord.game_id == game_id
        )
    )
    result = await db.execute(stmt)
    record = result.scalars().first()

    if not record:
        raise HTTPException(status_code=404, detail="记录不存在")

    # 更新记录
    for key, value in record_update.items():
        if hasattr(record, key):
            # 特殊处理日期字段
            if key in ['claim_time'] and isinstance(value, str):
                from datetime import datetime
                try:
                    # 尝试将ISO格式字符串转换为datetime对象，确保没有时区信息
                    # 如果有Z或时区信息，先去掉
                    if 'Z' in value or '+' in value or '-' in value and 'T' in value:
                        # 只保留日期和时间部分，去掉时区信息
                        value = value.split('+')[0].split('-0')[0].split('Z')[0]

                    # 解析日期时间字符串
                    dt = datetime.fromisoformat(value) if 'T' in value else datetime.strptime(value, "%Y-%m-%d %H:%M:%S")
                    setattr(record, key, dt)
                except ValueError as e:
                    # 如果转换失败，记录错误但不中断流程
                    print(f"Error converting date string: {value}, error: {e}")
                    continue
            else:
                setattr(record, key, value)

    await db.commit()
    await db.refresh(record)

    return GameRecordResponse.from_orm(record)

@router.post("/games/{game_id}/receipt-recognition", response_model=ReceiptRecognitionResponse)
async def recognize_game_receipt(
    game_id: int = Path(...),
    receipt: UploadFile = File(...),
    project_id: uuid.UUID = Path(..., description="项目ID"),
    current_user = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    识别小票信息
    """
    # 使用查询参数获取项目ID
    if not project_id:
        raise HTTPException(status_code=400, detail="未指定项目ID")

    game = await get_marketing_game(db, game_id, project_id)
    if not game:
        raise HTTPException(status_code=404, detail="营销游戏不存在")

    # 读取小票图片
    contents = await receipt.read()

    # 调用小票识别功能
    result = await recognize_receipt(contents)

    return result

@router.post("/games/{game_id}/verify-receipt")
async def verify_receipt(
    game_id: int = Path(...),
    receipt_data: dict = Body(...),
    project_id: uuid.UUID = Path(..., description="项目ID"),
    current_user = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    验证小票
    """
    # 使用查询参数获取项目ID
    if not project_id:
        raise HTTPException(status_code=400, detail="未指定项目ID")

    game = await get_marketing_game(db, game_id, project_id)
    if not game:
        raise HTTPException(status_code=404, detail="营销游戏不存在")

    receipt_code = receipt_data.get("receipt_code")
    amount = receipt_data.get("amount")

    if not receipt_code:
        raise HTTPException(status_code=400, detail="小票号码不能为空")

    # 检查小票是否已被使用
    stmt = select(GameRecord).where(
        and_(
            GameRecord.game_id == game_id,
            GameRecord.receipt_id == receipt_code
        )
    )
    result = await db.execute(stmt)
    existing_record = result.scalars().first()

    if existing_record:
        raise HTTPException(status_code=400, detail="该小票已被使用")

    # 检查消费金额是否满足最低要求
    if game.min_consumption > 0 and (not amount or float(amount) < game.min_consumption):
        raise HTTPException(status_code=400, detail=f"消费金额不能低于{game.min_consumption}元")

    return {"success": True, "message": "小票验证成功"}

@router.post("/games/{game_id}/verify-phone")
async def verify_phone(
    game_id: int = Path(...),
    phone_data: dict = Body(...),
    project_id: uuid.UUID = Path(..., description="项目ID"),
    current_user = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    验证手机号
    """
    # 使用查询参数获取项目ID
    if not project_id:
        raise HTTPException(status_code=400, detail="未指定项目ID")

    game = await get_marketing_game(db, game_id, project_id)
    if not game:
        raise HTTPException(status_code=404, detail="营销游戏不存在")

    phone = phone_data.get("phone")

    if not phone:
        raise HTTPException(status_code=400, detail="手机号不能为空")

    # 验证手机号格式
    import re
    if not re.match(r'^1[3-9]\d{9}$', phone):
        raise HTTPException(status_code=400, detail="手机号格式不正确")

    return {"success": True, "message": "手机号验证成功"}

@router.post("/games/{game_id}/draw", response_model=dict)
async def draw_prize(
    game_id: int = Path(...),
    project_id: uuid.UUID = Path(..., description="项目ID"),
    current_user = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    执行抽奖
    """
    try:
        # 检查游戏是否存在
        game = await get_marketing_game(db, game_id, project_id)
        if not game:
            raise HTTPException(status_code=404, detail="营销游戏不存在")
        
        # 检查游戏状态
        if game.status != "published":
            raise HTTPException(status_code=400, detail="游戏未开始或已结束")
        
        # 检查游戏时间
        from datetime import datetime
        current_time = datetime.now()
        if current_time < game.start_time or current_time > game.end_time:
            raise HTTPException(status_code=400, detail="游戏未在有效时间内")
        
        # 获取或创建参与者
        participant = await get_or_create_participant(db, game_id, current_user.id)
        
        # 检查抽奖次数
        if participant.draws_count >= game.max_draws_per_user:
            raise HTTPException(status_code=400, detail="已达到最大抽奖次数")
        
        # 使用增强的抽奖引擎
        lottery_engine = LotteryEngine(db)
        
        result = await lottery_engine.draw_prize(
            game_id=game_id,
            participant_id=participant.id,
            game_start_time=game.start_time,
            current_time=current_time
        )
        
        # 记录抽奖结果
        from ..models.models import GameRecord
        record_data = {
            "game_id": game_id,
            "participant_id": participant.id,
            "is_win": result.get("prize") is not None,
            "draw_time": current_time,
            "draw_method": result.get("method", "random"),
            "probability_used": result.get("probability_used"),
            "random_number": result.get("random_number"),
            "ip_address": "127.0.0.1",  # 可以从请求中获取
        }
        
        if result.get("prize"):
            # 查找奖品ID
            prize_data = result["prize"]
            record_data["prize_id"] = prize_data["id"]
        
        record = GameRecord(**record_data)
        db.add(record)
        
        # 更新参与者抽奖次数
        participant.draws_count += 1
        if result.get("prize"):
            participant.is_winner = True
        
        await db.commit()
        
        return {
            "success": True,
            "data": result,
            "message": result.get("message", "抽奖完成")
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"抽奖失败: {str(e)}", exc_info=True)
        await db.rollback()
        raise HTTPException(status_code=500, detail="抽奖系统异常")

@router.get("/games/{game_id}/user-stats", response_model=dict)
async def get_user_stats(
    game_id: int = Path(...),
    project_id: uuid.UUID = Path(..., description="项目ID"),
    current_user = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取用户游戏统计
    """
    try:
        # 检查游戏是否存在
        game = await get_marketing_game(db, game_id, project_id)
        if not game:
            raise HTTPException(status_code=404, detail="营销游戏不存在")
        
        # 在营销游戏中，参与者通常不是通过user_id关联的，而是通过手机号等
        # 由于 GameParticipant.user_id 是 Integer 类型，而系统用户 ID 是 UUID 类型，
        # 我们不能直接通过 user_id 查找，改为通过手机号查找
        participant = None
        
        # 尝试通过手机号查找参与者（如果用户有手机号）
        if hasattr(current_user, 'phone') and current_user.phone:
            participant_query = select(GameParticipant).where(
                and_(
                    GameParticipant.game_id == game_id,
                    GameParticipant.phone == current_user.phone
                )
            )
            result = await db.execute(participant_query)
            participant = result.scalar_one_or_none()
        
        # 如果没有找到参与者，返回默认统计数据
        if not participant:
            return {
                "success": True,
                "data": {
                    "total_spins": 0,
                    "win_count": 0,
                    "last_win_time": None,
                    "win_streak": 0,
                    "remaining_draws": game.max_draws_per_user if game.max_draws_per_user else 0
                }
            }
        
        # 获取抽奖记录
        records_query = select(GameRecord).where(
            GameRecord.participant_id == participant.id
        ).order_by(desc(GameRecord.draw_time))
        
        result = await db.execute(records_query)
        records = result.scalars().all()
        
        # 计算统计数据
        total_spins = len(records)
        win_count = len([r for r in records if r.is_win])
        
        # 计算连胜
        win_streak = 0
        for record in records:
            if record.is_win:
                win_streak += 1
            else:
                break
        
        # 最后中奖时间
        last_win_time = None
        for record in records:
            if record.is_win:
                last_win_time = record.draw_time
                break
        
        # 计算剩余抽奖次数
        remaining_draws = 0
        if game.max_draws_per_user:
            remaining_draws = max(0, game.max_draws_per_user - participant.draws_count)
        
        return {
            "success": True,
            "data": {
                "total_spins": total_spins,
                "win_count": win_count,
                "last_win_time": last_win_time.isoformat() if last_win_time else None,
                "win_streak": win_streak,
                "remaining_draws": remaining_draws
            }
        }
        
    except Exception as e:
        logger.error(f"获取用户统计失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail="获取统计数据失败")

async def get_or_create_participant(db: AsyncSession, game_id: int, user_id: int):
    """获取或创建参与者"""
    from ..models.models import GameParticipant
    
    # 查找现有参与者
    participant_query = select(GameParticipant).where(
        and_(
            GameParticipant.game_id == game_id,
            GameParticipant.user_id == user_id
        )
    )
    result = await db.execute(participant_query)
    participant = result.scalar_one_or_none()
    
    if not participant:
        # 创建新参与者
        participant = GameParticipant(
            game_id=game_id,
            user_id=user_id,
            draws_count=0,
            is_winner=False,
            is_fixed_winner=False
        )
        db.add(participant)
        await db.commit()
        await db.refresh(participant)
    
    return participant

# AI助手相关端点
@router.get("/games/{game_id}/ai-analysis", response_model=dict)
async def get_ai_analysis(
    game_id: int = Path(...),
    project_id: uuid.UUID = Path(..., description="项目ID"),
    current_user = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取AI分析数据
    """
    if not project_id:
        raise HTTPException(status_code=400, detail="未指定项目ID")

    game = await get_marketing_game(db, game_id, project_id)
    if not game:
        raise HTTPException(status_code=404, detail="营销游戏不存在")

    # 获取游戏记录进行分析
    records = await get_game_records(db, game_id, 0, 1000)
    
    # 计算分析数据
    total_participants = len(records)
    winners = [r for r in records if r.is_win]
    win_rate = (len(winners) / total_participants * 100) if total_participants > 0 else 0
    
    # 计算分享率（模拟数据）
    share_rate = min(25, total_participants * 0.15) if total_participants > 0 else 0
    
    # 计算综合评分
    participation_score = min(10, total_participants / 10)
    win_rate_score = 10 if 15 <= win_rate <= 30 else (6 if win_rate > 30 else 8)
    overall_score = (participation_score + win_rate_score) / 2

    return {
        "participants": total_participants,
        "win_rate": round(win_rate, 1),
        "share_rate": round(share_rate, 1),
        "overall_score": round(overall_score, 1)
    }

@router.get("/games/{game_id}/ai-suggestions", response_model=List[dict])
async def get_ai_suggestions(
    game_id: int = Path(...),
    project_id: uuid.UUID = Path(..., description="项目ID"),
    current_user = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取AI智能建议
    """
    if not project_id:
        raise HTTPException(status_code=400, detail="未指定项目ID")

    game = await get_marketing_game(db, game_id, project_id)
    if not game:
        raise HTTPException(status_code=404, detail="营销游戏不存在")

    # 获取游戏记录和奖品进行分析
    records = await get_game_records(db, game_id, 0, 1000)
    prizes = await get_game_prizes(db, game_id)
    
    suggestions = []
    
    # 基于参与数据生成建议
    if len(records) < 10:
        suggestions.append({
            "id": 1,
            "title": "提高游戏曝光度",
            "message": "参与人数较少，建议增加推广渠道和分享激励",
            "type": "promotion",
            "priority": "high"
        })

    # 基于中奖率生成建议
    winners = [r for r in records if r.is_win]
    win_rate = (len(winners) / len(records) * 100) if len(records) > 0 else 0
    
    if win_rate < 10:
        suggestions.append({
            "id": 2,
            "title": "优化中奖率设置",
            "message": "当前中奖率较低，可能影响用户参与积极性",
            "type": "optimization",
            "priority": "medium"
        })

    # 基于游戏配置生成建议
    if not game.description or len(game.description) < 20:
        suggestions.append({
            "id": 3,
            "title": "完善游戏描述",
            "message": "添加详细的游戏说明，提高用户理解度",
            "type": "content",
            "priority": "medium"
        })

    # 基于奖品设置生成建议
    if len(prizes) < 3:
        suggestions.append({
            "id": 4,
            "title": "丰富奖品设置",
            "message": "建议增加奖品种类，提高用户参与积极性",
            "type": "prize",
            "priority": "medium"
        })

    # 基于时间设置生成建议
    from datetime import datetime
    now = datetime.now()
    if game.end_time and game.end_time < now:
        suggestions.append({
            "id": 5,
            "title": "活动已结束",
            "message": "当前活动已结束，建议延长时间或创建新活动",
            "type": "timing",
            "priority": "high"
        })
    elif game.end_time:
        days_left = (game.end_time - now).days
        if 0 < days_left < 3:
            suggestions.append({
                "id": 6,
                "title": "考虑延长活动时间",
                "message": "活动即将结束，如效果良好可考虑延长",
                "type": "timing",
                "priority": "medium"
            })

    return suggestions

@router.get("/ai-overview", response_model=dict)
async def get_ai_overview(
    project_id: uuid.UUID = Path(..., description="项目ID"),
    current_user = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取项目营销游戏总览数据
    """
    if not project_id:
        raise HTTPException(status_code=400, detail="未指定项目ID")

    # 获取所有游戏
    games = await get_marketing_games(db, project_id, 0, 1000)
    
    total_games = len(games)
    active_games = len([g for g in games if g.status == 'active'])
    
    # 计算总参与人数（需要汇总所有游戏的参与数据）
    total_participants = 0
    for game in games:
        records = await get_game_records(db, game.id, 0, 1000)
        total_participants += len(records)
    
    return {
        "total_games": total_games,
        "active_games": active_games,
        "total_participants": total_participants,
        "overall_score": 8.5 if total_games > 0 else 0
    }

@router.post("/games/{game_id}/initialize", response_model=dict)
async def initialize_single_game(
    game_id: int = Path(...),
    project_id: uuid.UUID = Path(..., description="项目ID"),
    current_user = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    初始化单个营销游戏数据
    清空指定游戏的奖品、参与者和游戏记录，但保留游戏配置
    """
    if not project_id:
        raise HTTPException(status_code=400, detail="未指定项目ID")

    try:
        # 检查游戏是否存在
        game = await get_marketing_game(db, game_id, project_id)
        if not game:
            raise HTTPException(status_code=404, detail="营销游戏不存在")
        
        deleted_counts = {
            "prizes": 0,
            "participants": 0,
            "records": 0
        }
        
        # 删除游戏记录
        records_query = select(GameRecord).where(GameRecord.game_id == game_id)
        records_result = await db.execute(records_query)
        records = records_result.scalars().all()
        for record in records:
            await db.delete(record)
            deleted_counts["records"] += 1
        
        # 删除参与者
        participants_query = select(GameParticipant).where(GameParticipant.game_id == game_id)
        participants_result = await db.execute(participants_query)
        participants = participants_result.scalars().all()
        for participant in participants:
            await db.delete(participant)
            deleted_counts["participants"] += 1
        
        # 删除奖品
        prizes_query = select(GamePrize).where(GamePrize.game_id == game_id)
        prizes_result = await db.execute(prizes_query)
        prizes = prizes_result.scalars().all()
        for prize in prizes:
            await db.delete(prize)
            deleted_counts["prizes"] += 1
        
        await db.commit()
        
        return {
            "success": True,
            "message": f"游戏\"{game.name}\"数据初始化成功",
            "deleted_counts": deleted_counts,
            "game_name": game.name
        }
        
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"初始化单个游戏数据失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"初始化失败: {str(e)}")

@router.post("/initialize", response_model=dict)
async def initialize_marketing_games(
    project_id: uuid.UUID = Path(..., description="项目ID"),
    current_user = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    一键初始化营销游戏数据
    清空所有游戏、奖品、参与者和游戏记录
    """
    if not project_id:
        raise HTTPException(status_code=400, detail="未指定项目ID")

    try:
        # 获取项目下的所有游戏
        games = await get_marketing_games(db, project_id, 0, 10000)
        
        deleted_counts = {
            "games": 0,
            "prizes": 0,
            "participants": 0,
            "records": 0
        }
        
        for game in games:
            # 删除游戏记录
            records_query = select(GameRecord).where(GameRecord.game_id == game.id)
            records_result = await db.execute(records_query)
            records = records_result.scalars().all()
            for record in records:
                await db.delete(record)
                deleted_counts["records"] += 1
            
            # 删除参与者
            participants_query = select(GameParticipant).where(GameParticipant.game_id == game.id)
            participants_result = await db.execute(participants_query)
            participants = participants_result.scalars().all()
            for participant in participants:
                await db.delete(participant)
                deleted_counts["participants"] += 1
            
            # 删除奖品
            prizes_query = select(GamePrize).where(GamePrize.game_id == game.id)
            prizes_result = await db.execute(prizes_query)
            prizes = prizes_result.scalars().all()
            for prize in prizes:
                await db.delete(prize)
                deleted_counts["prizes"] += 1
            
            # 删除游戏
            await db.delete(game)
            deleted_counts["games"] += 1
        
        await db.commit()
        
        return {
            "success": True,
            "message": "营销游戏数据初始化成功",
            "deleted_counts": deleted_counts
        }
        
    except Exception as e:
        await db.rollback()
        logger.error(f"初始化营销游戏数据失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"初始化失败: {str(e)}")

@router.get("/statistics", response_model=dict)
async def get_marketing_game_statistics(
    project_id: uuid.UUID = Path(..., description="项目ID"),
    current_user = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取营销游戏统计数据
    """
    if not project_id:
        raise HTTPException(status_code=400, detail="未指定项目ID")

    try:
        # 获取所有游戏
        games = await get_marketing_games(db, project_id, 0, 10000)
        
        total_games = len(games)
        active_games = len([g for g in games if g.status == 'published'])
        draft_games = len([g for g in games if g.status == 'draft'])
        ended_games = len([g for g in games if g.status == 'ended'])
        
        # 统计所有数据
        total_prizes = 0
        total_participants = 0
        total_records = 0
        total_winners = 0
        
        for game in games:
            # 统计奖品
            prizes = await get_game_prizes(db, game.id)
            total_prizes += len(prizes)
            
            # 统计参与者
            participants = await get_game_participants(db, game.id, 0, 10000)
            total_participants += len(participants)
            
            # 统计游戏记录
            records = await get_game_records(db, game.id, 0, 10000)
            total_records += len(records)
            total_winners += len([r for r in records if r.is_win])
        
        return {
            "games": {
                "total": total_games,
                "active": active_games,
                "draft": draft_games,
                "ended": ended_games
            },
            "prizes": total_prizes,
            "participants": total_participants,
            "records": total_records,
            "winners": total_winners,
            "win_rate": round((total_winners / total_records * 100) if total_records > 0 else 0, 2)
        }
        
    except Exception as e:
        logger.error(f"获取统计数据失败: {str(e)}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"获取统计数据失败: {str(e)}")

# 参与者管理
@router.get("/games/{game_id}/participants", response_model=List[dict])
async def list_game_participants(
    game_id: int = Path(...),
    skip: int = 0,
    limit: int = 100,
    project_id: uuid.UUID = Path(..., description="项目ID"),
    current_user = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取游戏参与者列表
    """
    # 使用查询参数获取项目ID
    if not project_id:
        raise HTTPException(status_code=400, detail="未指定项目ID")

    game = await get_marketing_game(db, game_id, project_id)
    if not game:
        raise HTTPException(status_code=404, detail="营销游戏不存在")

    participants = await get_game_participants(db, game_id, skip, limit)
    
    # 手动构建返回数据，确保包含所有字段
    result = []
    for participant in participants:
        # 计算中奖次数 - 查询该参与者的中奖记录
        win_records_query = select(GameRecord).where(
            and_(
                GameRecord.participant_id == participant.id,
                GameRecord.is_win == True
            )
        )
        win_records_result = await db.execute(win_records_query)
        win_count = len(win_records_result.scalars().all())
        
        participant_data = {
            "id": participant.id,
            "game_id": participant.game_id,
            "user_id": participant.user_id,
            "member_id": participant.member_id,
            "name": participant.name,
            "phone": participant.phone,
            "draws_count": participant.draws_count,
            "is_winner": participant.is_winner,
            "is_fixed_winner": participant.is_fixed_winner,
            "total_consumption": participant.total_consumption or 0,
            "last_draw_time": participant.last_draw_time,
            "created_at": participant.created_at,
            "updated_at": participant.updated_at,
            
            # 为前端兼容性添加额外字段
            "user_name": participant.name,
            "draw_count": participant.draws_count,
            "win_count": win_count,
            "consumption_amount": participant.total_consumption or 0,
        }
        result.append(participant_data)
    
    return result

@router.post("/games/{game_id}/participants", response_model=GameParticipantResponse)
async def create_new_game_participant(
    game_id: int = Path(...),
    participant: GameParticipantCreate = Body(...),
    project_id: uuid.UUID = Path(..., description="项目ID"),
    current_user = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    创建游戏参与者
    """
    # 使用查询参数获取项目ID
    if not project_id:
        raise HTTPException(status_code=400, detail="未指定项目ID")

    game = await get_marketing_game(db, game_id, project_id)
    if not game:
        raise HTTPException(status_code=404, detail="营销游戏不存在")

    db_participant = await create_game_participant(db, participant, game_id)
    return GameParticipantResponse.from_orm(db_participant)
