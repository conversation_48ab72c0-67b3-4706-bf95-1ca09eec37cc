#!/usr/bin/env python
# -*- coding: utf-8 -*-

from typing import Dict, Any, List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import func, desc, and_, or_
from datetime import datetime, date, timedelta
import uuid
import pandas as pd
import io
import logging
from decimal import Decimal

from models.purchase_order import PurchaseOrder, PurchaseOrderItem
from models.supplier import Supplier
from models.warehouse import Warehouse
from models.product import Product
from models.store_operations import ArrivalConfirmation

logger = logging.getLogger(__name__)

class PurchaseReportService:
    """采购报表服务"""

    @staticmethod
    async def generate_report(
        db: AsyncSession,
        project_id: uuid.UUID,
        start_date: date,
        end_date: date,
        report_type: str = "overview",
        supplier_id: Optional[uuid.UUID] = None,
        warehouse_id: Optional[uuid.UUID] = None
    ) -> Dict[str, Any]:
        """生成采购报表数据"""
        
        try:
            # 基础查询条件
            base_conditions = [
                PurchaseOrder.project_id == project_id,
                PurchaseOrder.order_date >= start_date,
                PurchaseOrder.order_date <= end_date
            ]
            
            if supplier_id:
                # 通过订单项关联供应商
                base_conditions.append(
                    PurchaseOrder.id.in_(
                        select(PurchaseOrderItem.purchase_order_id)
                        .where(PurchaseOrderItem.supplier_id == supplier_id)
                    )
                )
            
            if warehouse_id:
                base_conditions.append(PurchaseOrder.warehouse_id == warehouse_id)

            # 获取统计数据
            statistics = await PurchaseReportService._get_statistics(db, base_conditions)
            
            # 获取趋势数据
            trend_data = await PurchaseReportService._get_trend_data(db, base_conditions, start_date, end_date)
            
            # 获取供应商数据
            supplier_data = await PurchaseReportService._get_supplier_data(db, base_conditions)
            
            # 获取明细数据
            detail_data = await PurchaseReportService._get_detail_data(db, base_conditions)

            return {
                "statistics": statistics,
                "trend_data": trend_data,
                "supplier_data": supplier_data,
                "detail_data": detail_data
            }
            
        except Exception as e:
            logger.error(f"生成采购报表失败: {e}")
            raise

    @staticmethod
    async def _get_statistics(db: AsyncSession, conditions: List) -> Dict[str, Any]:
        """获取统计数据"""
        
        # 分别查询订单数量和总金额（避免JOIN导致的重复计算）
        order_stats_query = select(
            func.count(PurchaseOrder.id).label('total_orders'),
            func.sum(PurchaseOrder.final_amount).label('total_amount')
        ).where(and_(*conditions))
        
        order_result = await db.execute(order_stats_query)
        order_row = order_result.first()
        
        # 查询供应商和产品数量
        item_stats_query = select(
            func.count(func.distinct(PurchaseOrderItem.supplier_id)).label('supplier_count'),
            func.count(func.distinct(PurchaseOrderItem.product_id)).label('product_count')
        ).select_from(
            PurchaseOrder
        ).join(
            PurchaseOrderItem, PurchaseOrder.id == PurchaseOrderItem.purchase_order_id
        ).where(and_(*conditions))
        
        item_result = await db.execute(item_stats_query)
        item_row = item_result.first()
        
        return {
            "total_orders": int(order_row.total_orders or 0),
            "total_amount": float(order_row.total_amount or 0),
            "supplier_count": int(item_row.supplier_count or 0) if item_row else 0,
            "product_count": int(item_row.product_count or 0) if item_row else 0
        }

    @staticmethod
    async def _get_trend_data(db: AsyncSession, conditions: List, start_date: date, end_date: date) -> List[Dict[str, Any]]:
        """获取趋势数据"""
        
        # 按日期分组统计
        trend_query = select(
            func.date(PurchaseOrder.order_date).label('date'),
            func.sum(PurchaseOrder.final_amount).label('amount')
        ).where(and_(*conditions)).group_by(
            func.date(PurchaseOrder.order_date)
        ).order_by(func.date(PurchaseOrder.order_date))
        
        result = await db.execute(trend_query)
        rows = result.all()
        
        # 填充缺失日期
        trend_data = []
        current_date = start_date
        date_amounts = {row.date: float(row.amount or 0) for row in rows}
        
        while current_date <= end_date:
            trend_data.append({
                "date": current_date.strftime('%Y-%m-%d'),
                "amount": date_amounts.get(current_date, 0)
            })
            current_date += timedelta(days=1)
        
        return trend_data

    @staticmethod
    async def _get_supplier_data(db: AsyncSession, conditions: List) -> List[Dict[str, Any]]:
        """获取供应商数据"""
        
        supplier_query = select(
            Supplier.name,
            func.sum(PurchaseOrderItem.total_amount).label('amount')
        ).select_from(
            PurchaseOrder
        ).join(
            PurchaseOrderItem, PurchaseOrder.id == PurchaseOrderItem.purchase_order_id
        ).outerjoin(
            Supplier, PurchaseOrderItem.supplier_id == Supplier.id
        ).where(and_(*conditions)).group_by(
            Supplier.id, Supplier.name
        ).order_by(func.sum(PurchaseOrderItem.total_amount).desc()).limit(10)
        
        result = await db.execute(supplier_query)
        rows = result.all()
        
        return [
            {
                "name": row.name or "未知供应商",
                "amount": float(row.amount or 0)
            }
            for row in rows
        ]

    @staticmethod
    async def _get_detail_data(db: AsyncSession, conditions: List) -> List[Dict[str, Any]]:
        """获取明细数据"""
        
        detail_query = select(
            PurchaseOrder.id,
            PurchaseOrder.order_number,
            PurchaseOrder.order_date,
            PurchaseOrder.status,
            PurchaseOrder.final_amount,
            Warehouse.name.label('warehouse_name')
        ).select_from(
            PurchaseOrder
        ).outerjoin(
            Warehouse, PurchaseOrder.warehouse_id == Warehouse.id
        ).where(and_(*conditions)).order_by(
            PurchaseOrder.order_date.desc()
        ).limit(100)
        
        result = await db.execute(detail_query)
        rows = result.all()
        
        return [
            {
                "id": str(row.id),
                "order_number": row.order_number,
                "order_date": row.order_date.isoformat() if row.order_date else None,
                "status": row.status,
                "total_amount": float(row.final_amount or 0),
                "warehouse_name": row.warehouse_name,
                "supplier_name": "多供应商"  # 由于一个订单可能有多个供应商，这里简化处理
            }
            for row in rows
        ]

    @staticmethod
    async def export_report(
        db: AsyncSession,
        project_id: uuid.UUID,
        start_date: date,
        end_date: date,
        report_type: str = "overview",
        supplier_id: Optional[uuid.UUID] = None,
        warehouse_id: Optional[uuid.UUID] = None
    ) -> str:
        """导出采购报表为Excel"""
        
        try:
            # 获取报表数据
            report_data = await PurchaseReportService.generate_report(
                db, project_id, start_date, end_date, report_type, supplier_id, warehouse_id
            )
            
            # 创建Excel文件
            output = io.BytesIO()
            
            with pd.ExcelWriter(output, engine='openpyxl') as writer:
                # 统计数据
                stats_df = pd.DataFrame([report_data['statistics']])
                stats_df.to_excel(writer, sheet_name='统计概览', index=False)
                
                # 趋势数据
                if report_data['trend_data']:
                    trend_df = pd.DataFrame(report_data['trend_data'])
                    trend_df.to_excel(writer, sheet_name='采购趋势', index=False)
                
                # 供应商数据
                if report_data['supplier_data']:
                    supplier_df = pd.DataFrame(report_data['supplier_data'])
                    supplier_df.to_excel(writer, sheet_name='供应商分析', index=False)
                
                # 明细数据
                if report_data['detail_data']:
                    detail_df = pd.DataFrame(report_data['detail_data'])
                    detail_df.to_excel(writer, sheet_name='采购明细', index=False)
            
            # 保存文件（这里简化处理，实际应该保存到文件存储服务）
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"purchase_report_{timestamp}.xlsx"
            
            # 返回下载URL（实际应该是真实的文件URL）
            return f"/api/reports/download/{filename}"
            
        except Exception as e:
            logger.error(f"导出采购报表失败: {e}")
            raise 