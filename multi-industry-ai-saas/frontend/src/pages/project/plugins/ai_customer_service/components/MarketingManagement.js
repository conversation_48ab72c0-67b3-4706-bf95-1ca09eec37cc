import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Statistic,
  Row,
  Col,
  Alert,
  Typography,
  Progress,
  List,
  Badge,
  Modal,
  Form,
  Input,
  Select,
  InputNumber,
  DatePicker,
  message,
  Popconfirm,
  Tooltip
} from 'antd';
import {
  ThunderboltOutlined,
  GiftOutlined,
  UserOutlined,
  TrophyOutlined,
  RiseOutlined,
  ReloadOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SendOutlined,
  EyeOutlined
} from '@ant-design/icons';

const { Title, Paragraph, Text } = Typography;
const { Option } = Select;
const { RangePicker } = DatePicker;

const MarketingManagement = () => {
  const [loading, setLoading] = useState(false);
  const [marketingStats, setMarketingStats] = useState({
    totalOpportunities: 0,
    convertedOpportunities: 0,
    totalCoupons: 0,
    totalRecommendations: 0
  });
  const [opportunities, setOpportunities] = useState([]);
  const [campaigns, setCampaigns] = useState([]);

  // 弹窗状态
  const [campaignModalVisible, setCampaignModalVisible] = useState(false);
  const [couponModalVisible, setCouponModalVisible] = useState(false);
  const [editingCampaign, setEditingCampaign] = useState(null);
  const [form] = Form.useForm();
  const [couponForm] = Form.useForm();

  // 获取项目ID
  const getProjectId = () => {
    const projectId = localStorage.getItem('project_id');
    if (projectId && projectId !== 'null' && projectId !== 'undefined') {
      return projectId;
    }

    const userData = JSON.parse(localStorage.getItem('user_data') || '{}');
    if (userData && userData.project_id) {
      localStorage.setItem('project_id', userData.project_id);
      return userData.project_id;
    }

    return null;
  };

  // 获取营销统计数据
  const fetchMarketingStats = async () => {
    setLoading(true);
    try {
      const projectId = getProjectId();
      if (!projectId) {
        console.error('无法获取项目ID');
        setLoading(false);
        return;
      }

      const response = await fetch(`/api/v1/project/${projectId}/plugin/ai-customer-service/marketing/stats`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setMarketingStats(data);
      } else {
        setMarketingStats({
          totalOpportunities: 0,
          convertedOpportunities: 0,
          totalCoupons: 0,
          totalRecommendations: 0
        });
      }
    } catch (error) {
      console.error('获取营销统计失败:', error);
      setMarketingStats({
        totalOpportunities: 0,
        convertedOpportunities: 0,
        totalCoupons: 0,
        totalRecommendations: 0
      });
    } finally {
      setLoading(false);
    }
  };

  // 获取营销机会
  const fetchOpportunities = async () => {
    try {
      const projectId = getProjectId();
      if (!projectId) {
        console.error('无法获取项目ID');
        setOpportunities([]);
        return;
      }

      const response = await fetch(`/api/v1/project/${projectId}/plugin/ai-customer-service/marketing/opportunities`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setOpportunities(data.opportunities || []);
      } else {
        setOpportunities([]);
      }
    } catch (error) {
      console.error('获取营销机会失败:', error);
      setOpportunities([]);
    }
  };

  // 创建营销活动
  const handleCreateCampaign = async (values) => {
    try {
      const projectId = getProjectId();
      if (!projectId) {
        message.error('无法获取项目ID');
        return;
      }

      const response = await fetch(`/api/v1/project/${projectId}/plugin/ai-customer-service/marketing/campaigns`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(values)
      });

      if (response.ok) {
        message.success('营销活动创建成功');
        setCampaignModalVisible(false);
        form.resetFields();
        fetchCampaigns();
      } else {
        message.error('创建营销活动失败');
      }
    } catch (error) {
      console.error('创建营销活动失败:', error);
      message.error('创建营销活动失败');
    }
  };

  // 编辑营销活动
  const handleEditCampaign = (campaign) => {
    setEditingCampaign(campaign);
    form.setFieldsValue(campaign);
    setCampaignModalVisible(true);
  };

  // 删除营销活动
  const handleDeleteCampaign = async (campaignId) => {
    try {
      const projectId = getProjectId();
      if (!projectId) {
        message.error('无法获取项目ID');
        return;
      }

      const response = await fetch(`/api/v1/project/${projectId}/plugin/ai-customer-service/marketing/campaigns/${campaignId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        message.success('营销活动删除成功');
        fetchCampaigns();
      } else {
        message.error('删除营销活动失败');
      }
    } catch (error) {
      console.error('删除营销活动失败:', error);
      message.error('删除营销活动失败');
    }
  };

  // 发送营销活动
  const handleSendCampaign = async (campaignId) => {
    try {
      const projectId = getProjectId();
      if (!projectId) {
        message.error('无法获取项目ID');
        return;
      }

      const response = await fetch(`/api/v1/project/${projectId}/plugin/ai-customer-service/marketing/campaigns/${campaignId}/send`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        message.success('营销活动发送成功');
        fetchCampaigns();
      } else {
        message.error('发送营销活动失败');
      }
    } catch (error) {
      console.error('发送营销活动失败:', error);
      message.error('发送营销活动失败');
    }
  };

  // 创建优惠券
  const handleCreateCoupon = async (values) => {
    try {
      const projectId = getProjectId();
      if (!projectId) {
        message.error('无法获取项目ID');
        return;
      }

      const response = await fetch(`/api/v1/project/${projectId}/plugin/ai-customer-service/marketing/coupons`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(values)
      });

      if (response.ok) {
        message.success('优惠券创建成功');
        setCouponModalVisible(false);
        couponForm.resetFields();
        fetchMarketingStats();
      } else {
        message.error('创建优惠券失败');
      }
    } catch (error) {
      console.error('创建优惠券失败:', error);
      message.error('创建优惠券失败');
    }
  };

  // 获取营销活动
  const fetchCampaigns = async () => {
    try {
      const projectId = getProjectId();
      if (!projectId) {
        console.error('无法获取项目ID');
        setCampaigns([]);
        return;
      }

      const response = await fetch(`/api/v1/project/${projectId}/plugin/ai-customer-service/marketing/campaigns`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setCampaigns(data.campaigns || []);
      } else {
        setCampaigns([]);
      }
    } catch (error) {
      console.error('获取营销活动失败:', error);
      setCampaigns([]);
    }
  };

  // 获取机会类型标签
  const getOpportunityTypeTag = (type) => {
    const typeMap = {
      'upsell': { color: 'green', text: '升级销售' },
      'cross_sell': { color: 'blue', text: '交叉销售' },
      'retention': { color: 'orange', text: '客户留存' }
    };
    
    const typeInfo = typeMap[type] || { color: 'default', text: type };
    return <Tag color={typeInfo.color}>{typeInfo.text}</Tag>;
  };

  // 获取状态标签
  const getStatusTag = (status) => {
    const statusMap = {
      'active': { color: 'processing', text: '进行中' },
      'converted': { color: 'success', text: '已转化' },
      'pending': { color: 'warning', text: '待处理' },
      'completed': { color: 'default', text: '已完成' }
    };
    
    const statusInfo = statusMap[status] || { color: 'default', text: status };
    return <Tag color={statusInfo.color}>{statusInfo.text}</Tag>;
  };

  // 获取活动类型标签
  const getCampaignTypeTag = (type) => {
    const typeMap = {
      'coupon': { color: 'red', text: '优惠券' },
      'recommendation': { color: 'blue', text: '产品推荐' },
      'retention': { color: 'purple', text: '留存营销' }
    };
    
    const typeInfo = typeMap[type] || { color: 'default', text: type };
    return <Tag color={typeInfo.color}>{typeInfo.text}</Tag>;
  };

  useEffect(() => {
    fetchMarketingStats();
    fetchOpportunities();
    fetchCampaigns();
  }, []);

  const opportunityColumns = [
    {
      title: '客户',
      dataIndex: 'customer_name',
      key: 'customer_name',
      render: (name) => (
        <Space>
          <UserOutlined />
          <span>{name}</span>
        </Space>
      )
    },
    {
      title: '机会类型',
      dataIndex: 'opportunity_type',
      key: 'opportunity_type',
      render: (type) => getOpportunityTypeTag(type)
    },
    {
      title: '产品兴趣',
      dataIndex: 'product_interest',
      key: 'product_interest'
    },
    {
      title: '置信度',
      dataIndex: 'confidence_score',
      key: 'confidence_score',
      render: (score) => (
        <div>
          <Progress percent={Math.round(score * 100)} size="small" />
          <Text style={{ fontSize: 12 }}>{(score * 100).toFixed(0)}%</Text>
        </div>
      )
    },
    {
      title: '预估价值',
      dataIndex: 'estimated_value',
      key: 'estimated_value',
      render: (value) => `¥${value.toLocaleString()}`
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => getStatusTag(status)
    }
  ];

  const campaignColumns = [
    {
      title: '活动名称',
      dataIndex: 'name',
      key: 'name'
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      render: (type) => getCampaignTypeTag(type)
    },
    {
      title: '目标受众',
      dataIndex: 'target_audience',
      key: 'target_audience'
    },
    {
      title: '发送数',
      dataIndex: 'sent_count',
      key: 'sent_count',
      render: (count) => <Badge count={count} showZero />
    },
    {
      title: '打开数',
      dataIndex: 'opened_count',
      key: 'opened_count',
      render: (count) => <Badge count={count} showZero style={{ backgroundColor: '#52c41a' }} />
    },
    {
      title: '转化数',
      dataIndex: 'converted_count',
      key: 'converted_count',
      render: (count) => <Badge count={count} showZero style={{ backgroundColor: '#1890ff' }} />
    },
    {
      title: '转化率',
      dataIndex: 'conversion_rate',
      key: 'conversion_rate',
      render: (rate) => `${rate}%`
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => getStatusTag(status)
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_, record) => (
        <Space>
          <Tooltip title="查看详情">
            <Button
              type="link"
              icon={<EyeOutlined />}
              size="small"
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button
              type="link"
              icon={<EditOutlined />}
              onClick={() => handleEditCampaign(record)}
              size="small"
            />
          </Tooltip>
          {record.status === 'draft' && (
            <Tooltip title="发送">
              <Popconfirm
                title="确定要发送这个营销活动吗？"
                onConfirm={() => handleSendCampaign(record.id)}
                okText="确定"
                cancelText="取消"
              >
                <Button
                  type="link"
                  icon={<SendOutlined />}
                  size="small"
                />
              </Popconfirm>
            </Tooltip>
          )}
          <Tooltip title="删除">
            <Popconfirm
              title="确定要删除这个营销活动吗？"
              onConfirm={() => handleDeleteCampaign(record.id)}
              okText="确定"
              cancelText="取消"
            >
              <Button
                type="link"
                icon={<DeleteOutlined />}
                danger
                size="small"
              />
            </Popconfirm>
          </Tooltip>
        </Space>
      )
    }
  ];

  return (
    <div>
      <Card title="智能营销管理">
        <Alert
          message="智能营销功能"
          description="基于客户行为分析和AI算法，自动识别营销机会，推送个性化优惠券和产品推荐，提升转化率和客户价值。"
          type="info"
          showIcon
          style={{ marginBottom: 24 }}
        />

        {/* 操作按钮 */}
        <div style={{ marginBottom: 24 }}>
          <Space>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {
                setEditingCampaign(null);
                form.resetFields();
                setCampaignModalVisible(true);
              }}
            >
              创建营销活动
            </Button>
            <Button
              icon={<GiftOutlined />}
              onClick={() => {
                couponForm.resetFields();
                setCouponModalVisible(true);
              }}
            >
              创建优惠券
            </Button>
          </Space>
        </div>

        {/* 营销统计 */}
        <Row gutter={16} style={{ marginBottom: 24 }}>
          <Col xs={24} sm={12} md={6}>
            <Card size="small">
              <Statistic
                title="营销机会"
                value={marketingStats.totalOpportunities}
                prefix={<ThunderboltOutlined />}
                valueStyle={{ color: '#3f8600' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card size="small">
              <Statistic
                title="转化机会"
                value={marketingStats.convertedOpportunities}
                prefix={<TrophyOutlined />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card size="small">
              <Statistic
                title="优惠券发放"
                value={marketingStats.totalCoupons}
                prefix={<GiftOutlined />}
                valueStyle={{ color: '#722ed1' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={12} md={6}>
            <Card size="small">
              <Statistic
                title="产品推荐"
                value={marketingStats.totalRecommendations}
                prefix={<RiseOutlined />}
                valueStyle={{ color: '#cf1322' }}
              />
            </Card>
          </Col>
        </Row>

        {/* 转化率统计 */}
        <Row gutter={16} style={{ marginBottom: 24 }}>
          <Col span={12}>
            <Card title="机会转化率" size="small">
              <Progress
                type="circle"
                percent={Math.round((marketingStats.convertedOpportunities / marketingStats.totalOpportunities) * 100)}
                format={percent => `${percent}%`}
                strokeColor="#52c41a"
              />
              <div style={{ textAlign: 'center', marginTop: 16 }}>
                <Text type="secondary">
                  {marketingStats.convertedOpportunities} / {marketingStats.totalOpportunities}
                </Text>
              </div>
            </Card>
          </Col>
          <Col span={12}>
            <Card title="营销效果" size="small">
              <List
                size="small"
                dataSource={[
                  { label: '平均转化率', value: '18.5%', color: '#52c41a' },
                  { label: '客户生命周期价值', value: '+25%', color: '#1890ff' },
                  { label: '复购率', value: '32%', color: '#722ed1' },
                  { label: '推荐成功率', value: '15.8%', color: '#faad14' }
                ]}
                renderItem={item => (
                  <List.Item>
                    <Space>
                      <Text>{item.label}:</Text>
                      <Text style={{ color: item.color, fontWeight: 'bold' }}>
                        {item.value}
                      </Text>
                    </Space>
                  </List.Item>
                )}
              />
            </Card>
          </Col>
        </Row>

        {/* 营销机会列表 */}
        <Card 
          title="营销机会" 
          size="small" 
          style={{ marginBottom: 16 }}
          extra={
            <Button
              icon={<ReloadOutlined />}
              onClick={fetchOpportunities}
              size="small"
            >
              刷新
            </Button>
          }
        >
          <Table
            columns={opportunityColumns}
            dataSource={opportunities}
            rowKey="id"
            pagination={false}
            size="small"
          />
        </Card>

        {/* 营销活动列表 */}
        <Card 
          title="营销活动" 
          size="small"
          extra={
            <Button
              icon={<ReloadOutlined />}
              onClick={fetchCampaigns}
              size="small"
            >
              刷新
            </Button>
          }
        >
          <Table
            columns={campaignColumns}
            dataSource={campaigns}
            rowKey="id"
            pagination={false}
            size="small"
          />
        </Card>
      </Card>

      {/* 创建/编辑营销活动弹窗 */}
      <Modal
        title={editingCampaign ? "编辑营销活动" : "创建营销活动"}
        visible={campaignModalVisible}
        onCancel={() => {
          setCampaignModalVisible(false);
          setEditingCampaign(null);
          form.resetFields();
        }}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleCreateCampaign}
        >
          <Form.Item
            name="name"
            label="活动名称"
            rules={[{ required: true, message: '请输入活动名称' }]}
          >
            <Input placeholder="请输入活动名称" />
          </Form.Item>

          <Form.Item
            name="type"
            label="活动类型"
            rules={[{ required: true, message: '请选择活动类型' }]}
          >
            <Select placeholder="请选择活动类型">
              <Option value="coupon">优惠券</Option>
              <Option value="recommendation">产品推荐</Option>
              <Option value="retention">留存营销</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="target_audience"
            label="目标受众"
            rules={[{ required: true, message: '请输入目标受众' }]}
          >
            <Input placeholder="请输入目标受众" />
          </Form.Item>

          <Form.Item
            name="description"
            label="活动描述"
          >
            <Input.TextArea rows={3} placeholder="请输入活动描述" />
          </Form.Item>

          <Form.Item
            name="schedule_time"
            label="发送时间"
          >
            <DatePicker
              showTime
              placeholder="选择发送时间（留空立即发送）"
              style={{ width: '100%' }}
            />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                {editingCampaign ? "更新" : "创建"}
              </Button>
              <Button onClick={() => {
                setCampaignModalVisible(false);
                setEditingCampaign(null);
                form.resetFields();
              }}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 创建优惠券弹窗 */}
      <Modal
        title="创建优惠券"
        visible={couponModalVisible}
        onCancel={() => {
          setCouponModalVisible(false);
          couponForm.resetFields();
        }}
        footer={null}
        width={500}
      >
        <Form
          form={couponForm}
          layout="vertical"
          onFinish={handleCreateCoupon}
        >
          <Form.Item
            name="name"
            label="优惠券名称"
            rules={[{ required: true, message: '请输入优惠券名称' }]}
          >
            <Input placeholder="请输入优惠券名称" />
          </Form.Item>

          <Form.Item
            name="discount_type"
            label="优惠类型"
            rules={[{ required: true, message: '请选择优惠类型' }]}
          >
            <Select placeholder="请选择优惠类型">
              <Option value="percentage">百分比折扣</Option>
              <Option value="fixed">固定金额</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="discount_value"
            label="优惠金额"
            rules={[{ required: true, message: '请输入优惠金额' }]}
          >
            <InputNumber
              placeholder="请输入优惠金额"
              style={{ width: '100%' }}
              min={0}
            />
          </Form.Item>

          <Form.Item
            name="min_amount"
            label="最低消费金额"
          >
            <InputNumber
              placeholder="请输入最低消费金额"
              style={{ width: '100%' }}
              min={0}
            />
          </Form.Item>

          <Form.Item
            name="valid_period"
            label="有效期"
            rules={[{ required: true, message: '请选择有效期' }]}
          >
            <RangePicker style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            name="quantity"
            label="发放数量"
            rules={[{ required: true, message: '请输入发放数量' }]}
          >
            <InputNumber
              placeholder="请输入发放数量"
              style={{ width: '100%' }}
              min={1}
            />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                创建
              </Button>
              <Button onClick={() => {
                setCouponModalVisible(false);
                couponForm.resetFields();
              }}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default MarketingManagement;
