import React, { useState, useEffect, useCallback } from 'react';
import {
  Card, Table, Button, Space, Tag, Modal, message,
  Typography, Input, Select, Tooltip, Statistic, Row, Col,
  Form, Drawer, InputNumber, Switch, Tabs, Divider, Spin,
  Upload
} from 'antd';
import {
  PlusOutlined, EditOutlined,
  SyncOutlined, SettingOutlined, LineChartOutlined,
  EyeOutlined, ArrowLeftOutlined, Bar<PERSON><PERSON>Outlined,
  DeleteOutlined, UploadOutlined, DownloadOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { useProject } from '../../contexts/ProjectContext';
import apiService from '../../services/api';

const { Title, Text } = Typography;
const { Option } = Select;
const { TabPane } = Tabs;

const AIModelList = () => {
  const navigate = useNavigate();
  const { currentProject } = useProject();
  const [loading, setLoading] = useState(false);
  const [models, setModels] = useState([]);
  const [configs, setConfigs] = useState([]);
  const [providers, setProviders] = useState([]);
  const [usageStats, setUsageStats] = useState({
    total_tokens: 0,
    total_cost: 0,
    current_month_tokens: 0,
    current_month_cost: 0,
    model_stats: []
  });
  const [activeTab, setActiveTab] = useState('models');
  const [modelPagination, setModelPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const [configPagination, setConfigPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const [configDrawerVisible, setConfigDrawerVisible] = useState(false);
  const [currentConfig, setCurrentConfig] = useState(null);
  const [configForm] = Form.useForm();
  const [advancedSettingsForm] = Form.useForm();
  const [advancedSettingsVisible, setAdvancedSettingsVisible] = useState(false);
  const [advancedSettings, setAdvancedSettings] = useState({
    knowledge_base_enabled: true,
    tools_enabled: true,
    max_history_messages: 10,
    default_system_prompt: '',
    cache_enabled: true,
    cache_ttl: 3600,
    log_level: 'info'
  });
  
  // 新增状态
  const [showApiKey, setShowApiKey] = useState(false);
  const [selectedModel, setSelectedModel] = useState(null);
  const [selectedProvider, setSelectedProvider] = useState(null);
  const [configUsageModalVisible, setConfigUsageModalVisible] = useState(false);
  const [currentConfigUsage, setCurrentConfigUsage] = useState(null);
  const [configUsageStats, setConfigUsageStats] = useState({});

  // 新增模型相关状态
  const [addModelModalVisible, setAddModelModalVisible] = useState(false);
  const [addModelForm] = Form.useForm();
  const [addingModel, setAddingModel] = useState(false);

  // 编辑模型相关状态
  const [editModelModalVisible, setEditModelModalVisible] = useState(false);
  const [editModelForm] = Form.useForm();
  const [editingModel, setEditingModel] = useState(false);
  const [currentEditModel, setCurrentEditModel] = useState(null);

  // 导入配置相关状态
  const [importModalVisible, setImportModalVisible] = useState(false);
  const [importForm] = Form.useForm();
  const [importingConfigs, setImportingConfigs] = useState(false);
  const [importFileData, setImportFileData] = useState(null);

  // 模型能力覆盖状态
  const [modelCapabilitiesOverride, setModelCapabilitiesOverride] = useState({});
  const [showCapabilitiesEditor, setShowCapabilitiesEditor] = useState(false);

  // 获取模型列表
  const fetchModels = useCallback(async (page = 1, pageSize = 10) => {
    if (!currentProject?.id) return;
    
    setLoading(true);
    try {
      const response = await apiService.project.ai.aiModels.list(currentProject.id, {
        skip: (page - 1) * pageSize,
        limit: pageSize
      });
      
      console.log('模型列表响应:', response);
      console.log('模型数据:', response.data?.length, '个模型');
      console.log('总数:', response.total);
      
      setModels(response.data || []);
      setModelPagination(prev => ({
        ...prev,
        current: page,
        pageSize: pageSize,
        total: response.total || 0,
      }));
    } catch (error) {
      console.error('获取模型列表出错:', error);
      message.error('获取模型列表失败，请稍后再试');
    } finally {
      setLoading(false);
    }
  }, [currentProject?.id]);

  // 获取所有模型（用于配置创建/编辑）
  const fetchAllModels = useCallback(async () => {
    if (!currentProject?.id) return;
    
    try {
      const response = await apiService.project.ai.aiModels.list(currentProject.id, {
        skip: 0,
        limit: 100  // 加载更多模型以确保包含所有模型
      });
      const allModels = response.data || [];
      setModels(allModels);
      
      console.log('加载的所有模型:', allModels);
      console.log('DeepSeek模型:', allModels?.filter(m => m.name?.includes('deepseek')));
      
      return allModels; // 返回模型数据
    } catch (error) {
      console.error('获取所有模型列表出错:', error);
      return [];
    }
  }, [currentProject?.id]);

  // 获取配置列表
  const fetchConfigs = useCallback(async (page = 1, pageSize = 10) => {
    if (!currentProject?.id) return;
    
    setLoading(true);
    try {
      console.log('获取AI配置列表...');
      const response = await apiService.project.ai.aiConfigs.list(currentProject.id, {
        skip: (page - 1) * pageSize,
        limit: pageSize
      });
      
      console.log('AI配置列表响应:', response);
      console.log('配置数据:', response.data?.length, '个配置');
      console.log('总数:', response.total);
      
      const configData = response.data || [];
      console.log('处理配置数据:', configData);
      
      // 详细检查每个配置的结构
      configData.forEach((config, index) => {
        console.log(`配置 ${index + 1} (${config.name || 'unnamed'}):`, {
          id: config.id,
          name: config.name,
          model_id: config.model_id,
          has_model: !!config.model,
          model_structure: config.model ? Object.keys(config.model) : null,
          model_name: config.model?.name,
          model_display_name: config.model?.display_name,
          model_capabilities: config.model?.capabilities,
          provider_id: config.provider_id,
          has_provider: !!config.provider,
          provider_name: config.provider?.name,
          status: config.status,
          full_structure: Object.keys(config)
        });
        
        if (config.name === 'Qwen2.5 VL 32B Instruct') {
          console.log('🔍 发现Qwen2.5 VL 32B Instruct配置详情:', config);
        }
      });
      
      setConfigs(configData);
      setConfigPagination(prev => ({
        ...prev,
        current: page,
        pageSize: pageSize,
        total: response.total || 0,
      }));
    } catch (error) {
      console.error('获取配置列表出错:', error);
      message.error('获取配置列表失败，请稍后再试');
    } finally {
      setLoading(false);
    }
  }, [currentProject?.id]);

  // 获取提供商列表
  const fetchProviders = useCallback(async () => {
    if (!currentProject?.id) return;
    
    try {
      const response = await apiService.project.ai.aiProviders.list(currentProject.id);
      setProviders(response.data || []);
    } catch (error) {
      console.error('获取提供商列表出错:', error);
      message.error('获取提供商列表失败，请稍后再试');
    }
  }, [currentProject?.id]);

  // 获取使用统计
  const fetchUsageStats = useCallback(async () => {
    if (!currentProject?.id) return;
    
    try {
      const response = await apiService.project.ai.aiUsage.getStats(currentProject.id);
      setUsageStats(response.data || {
        total_tokens: 0,
        total_cost: 0,
        current_month_tokens: 0,
        current_month_cost: 0,
        model_stats: []
      });
    } catch (error) {
      console.error('获取使用统计出错:', error);
    }
  }, [currentProject?.id]);

  // 获取高级设置
  const fetchAdvancedSettings = useCallback(async () => {
    if (!currentProject?.id) return;
    
    try {
      const response = await apiService.project.ai.aiSettings.get(currentProject.id);
      const settings = response.data || {};
      setAdvancedSettings({
        knowledge_base_enabled: settings.knowledge_base_enabled !== false,
        tools_enabled: settings.tools_enabled !== false,
        max_history_messages: settings.max_history_messages || 10,
        default_system_prompt: settings.default_system_prompt || '',
        cache_enabled: settings.cache_enabled !== false,
        cache_ttl: settings.cache_ttl || 3600,
        log_level: settings.log_level || 'info'
      });

      // 设置表单值
      advancedSettingsForm.setFieldsValue({
        knowledge_base_enabled: settings.knowledge_base_enabled !== false,
        tools_enabled: settings.tools_enabled !== false,
        max_history_messages: settings.max_history_messages || 10,
        default_system_prompt: settings.default_system_prompt || '',
        cache_enabled: settings.cache_enabled !== false,
        cache_ttl: settings.cache_ttl || 3600,
        log_level: settings.log_level || 'info'
      });
    } catch (error) {
      console.error('获取高级设置出错:', error);
    }
  }, [currentProject?.id, advancedSettingsForm]);

  // 保存高级设置
  const saveAdvancedSettings = async () => {
    try {
      const values = await advancedSettingsForm.validateFields();

      await apiService.project.ai.aiSettings.update(currentProject.id, values);
      message.success('高级设置保存成功');
      setAdvancedSettingsVisible(false);
      setAdvancedSettings(values);
    } catch (error) {
      console.error('保存高级设置出错:', error);
      message.error('保存高级设置失败');
    }
  };

  // 显示新增模型模态框
  const showAddModelModal = () => {
    addModelForm.resetFields();
    setAddModelModalVisible(true);
  };

  // 关闭新增模型模态框
  const closeAddModelModal = () => {
    setAddModelModalVisible(false);
    addModelForm.resetFields();
  };

  // 处理新增模型
  const handleAddModel = async () => {
    try {
      setAddingModel(true);
      const values = await addModelForm.validateFields();
      
      // 构建模型数据
      const modelData = {
        provider_id: values.provider_id,
        name: values.name,
        display_name: values.display_name,
        description: values.description,
        model_type: values.model_type,
        capabilities: {
          supports_function_calling: values.supports_function_calling || false,
          supports_vision: values.supports_vision || false,
          supports_audio_input: values.supports_audio_input || false,
          supports_audio_output: values.supports_audio_output || false,
          supports_speech_to_text: values.supports_speech_to_text || false,
          supports_text_to_speech: values.supports_text_to_speech || false,
          supports_system_message: values.supports_system_message !== false,
          supports_streaming: values.supports_streaming !== false,
          supports_multilingual: values.supports_multilingual || false,
          supports_code_generation: values.supports_code_generation || false,
          supports_reasoning: values.supports_reasoning || false,
          supports_math: values.supports_math || false,
          supports_document_analysis: values.supports_document_analysis || false,
          supports_chart_analysis: values.supports_chart_analysis || false,
          supports_ocr: values.supports_ocr || false,
          supports_image_understanding: values.supports_image_understanding || false,
          supports_video_understanding: values.supports_video_understanding || false,
          supports_grounding: values.supports_grounding || false,
          supports_edge_deployment: values.supports_edge_deployment || false
        },
        context_window: values.context_window,
        token_limit: values.token_limit,
        input_price_per_1k_tokens: values.input_price_per_1k_tokens || 0,
        output_price_per_1k_tokens: values.output_price_per_1k_tokens || 0,
        image_price_per_1k_tokens: values.image_price_per_1k_tokens || 0,
        audio_price_per_minute: values.audio_price_per_minute || 0,
        video_price_per_minute: values.video_price_per_minute || 0,
        average_response_time: values.average_response_time,
        rate_limit_per_minute: values.rate_limit_per_minute,
        status: 'active',
        is_builtin: false
      };

      // 调用API添加模型
      const response = await apiService.project.ai.aiModels.create(currentProject.id, modelData);
      
      if (response && response.success) {
        message.success('模型添加成功');
        closeAddModelModal();
        // 刷新模型列表
        if (activeTab === 'models') {
          fetchModels();
        } else {
          fetchAllModels(); // 为了配置页面能看到新模型
        }
      } else {
        message.error(response?.message || '添加模型失败');
      }
    } catch (error) {
      console.error('添加模型失败:', error);
      message.error('添加模型失败: ' + (error.response?.data?.detail || error.message));
    } finally {
      setAddingModel(false);
    }
  };

  // 显示高级设置模态框
  const showAdvancedSettingsModal = () => {
    fetchAdvancedSettings();
    setAdvancedSettingsVisible(true);
  };

  // 处理分页变化
  const handleTableChange = (newPagination) => {
    if (activeTab === 'models') {
      fetchModels(newPagination.current, newPagination.pageSize);
    } else {
      fetchConfigs(newPagination.current, newPagination.pageSize);
    }
  };

  // 首次加载
  useEffect(() => {
    const loadData = async () => {
      await fetchModels();
      await fetchConfigs();
      await fetchProviders();
      await fetchUsageStats();
      await fetchAdvancedSettings();
    };
    
    if (currentProject?.id) {
      loadData();
    }
  }, [currentProject?.id, fetchModels, fetchConfigs, fetchProviders, fetchUsageStats, fetchAdvancedSettings]);

  // 模型表格列定义
  const modelColumns = [
    {
      title: '模型名称',
      dataIndex: 'display_name',
      key: 'display_name',
      render: (text, record) => (
        <div>
          <div style={{ fontWeight: 'bold' }}>{text || record.name}</div>
          <div style={{ fontSize: '12px', color: '#666' }}>
            {record.description || '暂无描述'}
          </div>
        </div>
      ),
    },
    {
      title: '提供商',
      dataIndex: 'provider_id',
      key: 'provider_id',
      render: (providerId, record) => {
        const provider = providers.find(p => p.id === providerId);
        return (
          <div>
            <div style={{ fontWeight: 'bold' }}>
              {provider?.display_name || provider?.name || record.provider_name || '未知提供商'}
            </div>
            {provider?.description && (
              <div style={{ fontSize: '12px', color: '#666' }}>
                {provider.description}
              </div>
            )}
          </div>
        );
      },
    },
    {
      title: '类型',
      dataIndex: 'model_type',
      key: 'model_type',
      render: (type) => (
        <Tag color={
          type === 'chat' ? 'blue' : 
          type === 'embedding' ? 'purple' : 
          type === 'vision' ? 'green' :
          type === 'audio' ? 'orange' :
          type === 'multimodal' ? 'red' : 'default'
        }>
          {type === 'chat' ? '对话' : 
           type === 'embedding' ? '嵌入' : 
           type === 'vision' ? '视觉' :
           type === 'audio' ? '音频' :
           type === 'multimodal' ? '多模态' : type}
        </Tag>
      ),
    },
    {
      title: '能力',
      key: 'capabilities',
      render: (_, record) => {
        // 优先使用配置中的model对象
        let model = record.model;
        
        // 如果没有model对象，记录调试信息
        if (!model) {
          console.warn(`配置 ${record.name} 缺少模型信息`, {
            record_id: record.id,
            model_id: record.model_id,
            provider_id: record.provider_id,
            record_keys: Object.keys(record),
            has_model_obj: !!record.model,
            record_structure: record
          });
          
          return (
            <Tooltip title={`模型信息缺失，配置ID: ${record.id?.slice(0, 8)}...`}>
              <Text type="warning">
                模型信息缺失
                <Button 
                  type="link" 
                  size="small" 
                  onClick={() => {
                    console.log('配置详细信息:', record);
                    message.info('请检查浏览器控制台的详细信息');
                  }}
                >
                  [调试]
                </Button>
              </Text>
            </Tooltip>
          );
        }
        
        // 如果模型没有capabilities信息
        if (!model.capabilities) {
          console.log(`模型 ${model.name} 没有capabilities信息:`, model);
          return (
            <Tooltip title="此模型没有能力信息配置">
              <Text type="secondary">无能力信息</Text>
            </Tooltip>
          );
        }
        
        // 获取基础能力
        const baseCaps = model.capabilities;
        
        // 获取能力覆盖（如果有的话）
        const capabilitiesOverride = record.config?.capabilities_override || {};
        
        // 应用能力覆盖，构建有效能力
        const effectiveCaps = {
          ...baseCaps,
          ...capabilitiesOverride
        };
        
        // 解析模型能力
        const capabilities = [];
        
        if (effectiveCaps.supports_vision) capabilities.push('视觉');
        if (effectiveCaps.supports_audio_input) capabilities.push('音频输入');
        if (effectiveCaps.supports_audio_output) capabilities.push('音频输出');
        if (effectiveCaps.supports_function_calling) capabilities.push('函数调用');
        if (effectiveCaps.supports_streaming) capabilities.push('流式');
        if (effectiveCaps.supports_document_analysis) capabilities.push('文档');
        if (effectiveCaps.supports_code_execution) capabilities.push('代码执行');
        if (effectiveCaps.supports_web_search) capabilities.push('网络搜索');
        if (effectiveCaps.supports_thinking) capabilities.push('长思考');
        
        return (
          <Space wrap>
            {capabilities.length > 0 ? (
              capabilities.map(cap => {
                // 检查这个能力是否被覆盖了
                const isOverridden = capabilitiesOverride.hasOwnProperty(
                  cap === '视觉' ? 'supports_vision' :
                  cap === '音频输入' ? 'supports_audio_input' :
                  cap === '音频输出' ? 'supports_audio_output' :
                  cap === '函数调用' ? 'supports_function_calling' :
                  cap === '流式' ? 'supports_streaming' :
                  cap === '文档' ? 'supports_document_analysis' :
                  cap === '代码执行' ? 'supports_code_execution' :
                  cap === '网络搜索' ? 'supports_web_search' :
                  cap === '长思考' ? 'supports_thinking' : ''
                );
                
                return (
                  <Tag 
                    key={cap} 
                    color={
                      cap === '视觉' ? 'green' : 
                      cap.includes('音频') ? 'orange' : 
                      cap === '函数调用' ? 'blue' : 
                      cap === '流式' ? 'purple' : 
                      cap === '文档' ? 'cyan' :
                      cap === '代码执行' ? 'red' :
                      cap === '网络搜索' ? 'geekblue' :
                      cap === '长思考' ? 'magenta' : 'default'
                    }
                    style={isOverridden ? { borderColor: '#1890ff', borderWidth: 2 } : {}}
                  >
                    {cap}
                    {isOverridden && <span style={{ fontSize: '10px', marginLeft: '2px' }}>*</span>}
                  </Tag>
                );
              })
            ) : (
              <Text type="secondary">基础对话</Text>
            )}
            {Object.keys(capabilitiesOverride).length > 0 && (
              <Tooltip title="此配置包含能力覆盖设置，带*号的能力已被自定义">
                <Tag color="processing" size="small">
                  已自定义
                </Tag>
              </Tooltip>
            )}
          </Space>
        );
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={status === 'active' ? 'green' : 'default'}>
          {status === 'active' ? '启用' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '价格 (每1K tokens)',
      key: 'price',
      render: (_, record) => (
        <div>
          <div>输入: ¥{(record.input_price_per_1k_tokens || 0).toFixed(4)}</div>
          <div>输出: ¥{(record.output_price_per_1k_tokens || 0).toFixed(4)}</div>
          {record.image_price_per_1k_tokens && (
            <div style={{ fontSize: '12px', color: '#666' }}>
              图像: ¥{record.image_price_per_1k_tokens.toFixed(4)}
            </div>
          )}
        </div>
      ),
    },
    {
      title: '性能',
      key: 'performance',
      render: (_, record) => (
        <div style={{ fontSize: '12px' }}>
          {record.average_response_time && (
            <div>响应: {record.average_response_time}s</div>
          )}
          {record.context_window && (
            <div>上下文: {(record.context_window / 1000).toFixed(0)}K</div>
          )}
          {record.token_limit && (
            <div>输出: {(record.token_limit / 1000).toFixed(0)}K</div>
          )}
        </div>
      ),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => {
                const provider = providers.find(p => p.id === record.provider_id);
                Modal.info({
                  title: `模型详情 - ${record.display_name || record.name}`,
                  width: 800,
                  content: (
                    <div>
                      <p><strong>提供商:</strong> {provider?.display_name || provider?.name || '未知'}</p>
                      <p><strong>描述:</strong> {record.description || '暂无描述'}</p>
                      <p><strong>上下文窗口:</strong> {record.context_window || '未知'} tokens</p>
                      <p><strong>最大输出:</strong> {record.token_limit || '未知'} tokens</p>
                      
                      {record.capabilities && (
                        <div style={{ marginBottom: 16 }}>
                          <strong>支持的能力:</strong>
                          <div style={{ marginTop: 8 }}>
                            {Object.entries(record.capabilities)
                              .filter(([key, value]) => value === true && key.startsWith('supports_'))
                              .map(([key]) => (
                                <Tag key={key} style={{ margin: '2px' }}>
                                  {key.replace('supports_', '').replace('_', ' ')}
                                </Tag>
                              ))
                            }
                          </div>
                        </div>
                      )}
                      
                      {record.capabilities?.supported_image_formats && (
                        <p><strong>支持图像格式:</strong> {record.capabilities.supported_image_formats.join(', ')}</p>
                      )}
                      {record.capabilities?.supported_audio_formats && (
                        <p><strong>支持音频格式:</strong> {record.capabilities.supported_audio_formats.join(', ')}</p>
                      )}
                      {record.capabilities?.max_image_size && (
                        <p><strong>最大图像大小:</strong> {record.capabilities.max_image_size} MB</p>
                      )}
                      {record.capabilities?.max_audio_duration && (
                        <p><strong>最大音频时长:</strong> {record.capabilities.max_audio_duration} 秒</p>
                      )}
                      {record.capabilities?.max_file_size && (
                        <p><strong>最大文件大小:</strong> {record.capabilities.max_file_size} MB</p>
                      )}
                      {record.capabilities?.image_resolution_limit && (
                        <p><strong>图像分辨率限制:</strong> {record.capabilities.image_resolution_limit}</p>
                      )}
                      {record.capabilities?.supports_batch_processing !== undefined && (
                        <p><strong>批处理支持:</strong> {record.capabilities.supports_batch_processing ? '是' : '否'}</p>
                      )}
                      {record.capabilities?.concurrent_requests && (
                        <p><strong>并发请求数:</strong> {record.capabilities.concurrent_requests}</p>
                      )}
                      {record.capabilities?.supports_thinking !== undefined && (
                        <p><strong>长思考支持:</strong> {record.capabilities.supports_thinking ? '是' : '否'}</p>
                      )}
                      
                      {record.performance_metrics && (
                        <div style={{ marginTop: 16 }}>
                          <strong>性能指标:</strong>
                          <div style={{ marginTop: 8, background: '#f5f5f5', padding: 12, borderRadius: 4 }}>
                            {record.performance_metrics.average_response_time && (
                              <p style={{ margin: '4px 0' }}>平均响应时间: {record.performance_metrics.average_response_time}ms</p>
                            )}
                            {record.performance_metrics.tokens_per_second && (
                              <p style={{ margin: '4px 0' }}>处理速度: {record.performance_metrics.tokens_per_second} tokens/秒</p>
                            )}
                            {record.performance_metrics.uptime_percentage && (
                              <p style={{ margin: '4px 0' }}>可用性: {record.performance_metrics.uptime_percentage}%</p>
                            )}
                          </div>
                        </div>
                      )}
                      
                      {record.pricing_details && (
                        <div style={{ marginTop: 16 }}>
                          <strong>定价详情:</strong>
                          <div style={{ marginTop: 8, background: '#f5f5f5', padding: 12, borderRadius: 4 }}>
                            <p style={{ margin: '4px 0' }}>输入: ¥{(record.input_price_per_1k_tokens || 0).toFixed(4)}/1K tokens</p>
                            <p style={{ margin: '4px 0' }}>输出: ¥{(record.output_price_per_1k_tokens || 0).toFixed(4)}/1K tokens</p>
                            {record.image_price_per_1k_tokens && (
                              <p style={{ margin: '4px 0' }}>图像: ¥{record.image_price_per_1k_tokens.toFixed(4)}/1K tokens</p>
                            )}
                            {record.audio_price_per_minute && (
                              <p style={{ margin: '4px 0' }}>音频: ¥{record.audio_price_per_minute.toFixed(4)}/分钟</p>
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                  ),
                });
              }}
            />
          </Tooltip>
          <Tooltip title="查看使用情况">
            <Button
              type="text"
              icon={<LineChartOutlined />}
              onClick={() => {
                // 查找该模型的使用统计
                const modelStats = usageStats.model_stats?.find(stat => stat.model_id === record.id);
                if (modelStats) {
                  Modal.info({
                    title: `使用情况 - ${record.display_name || record.name}`,
                    width: 600,
                    content: (
                      <div>
                        <div style={{ marginBottom: 16 }}>
                          <strong>调用统计:</strong>
                          <div style={{ marginTop: 8, background: '#f5f5f5', padding: 12, borderRadius: 4 }}>
                            <p style={{ margin: '4px 0' }}>总调用次数: {modelStats.request_count || 0}</p>
                            <p style={{ margin: '4px 0' }}>成功次数: {modelStats.success_count || 0}</p>
                            <p style={{ margin: '4px 0' }}>成功率: {modelStats.request_count > 0 ? ((modelStats.success_count || 0) / modelStats.request_count * 100).toFixed(1) : 0}%</p>
                          </div>
                        </div>
                        
                        <div style={{ marginBottom: 16 }}>
                          <strong>Token使用:</strong>
                          <div style={{ marginTop: 8, background: '#f5f5f5', padding: 12, borderRadius: 4 }}>
                            <p style={{ margin: '4px 0' }}>输入Token: {(modelStats.input_tokens || 0).toLocaleString()}</p>
                            <p style={{ margin: '4px 0' }}>输出Token: {(modelStats.output_tokens || 0).toLocaleString()}</p>
                            <p style={{ margin: '4px 0' }}>总Token: {((modelStats.input_tokens || 0) + (modelStats.output_tokens || 0)).toLocaleString()}</p>
                          </div>
                        </div>
                        
                        <div style={{ marginBottom: 16 }}>
                          <strong>费用统计:</strong>
                          <div style={{ marginTop: 8, background: '#f5f5f5', padding: 12, borderRadius: 4 }}>
                            <p style={{ margin: '4px 0' }}>输入费用: ¥{(modelStats.input_cost || 0).toFixed(4)}</p>
                            <p style={{ margin: '4px 0' }}>输出费用: ¥{(modelStats.output_cost || 0).toFixed(4)}</p>
                            <p style={{ margin: '4px 0', fontWeight: 'bold', color: '#1890ff' }}>
                              总费用: ¥{((modelStats.input_cost || 0) + (modelStats.output_cost || 0)).toFixed(4)}
                            </p>
                          </div>
                        </div>
                        
                        <div>
                          <strong>性能指标:</strong>
                          <div style={{ marginTop: 8, background: '#f5f5f5', padding: 12, borderRadius: 4 }}>
                            <p style={{ margin: '4px 0' }}>平均响应时间: {modelStats.avg_response_time ? `${modelStats.avg_response_time.toFixed(2)}ms` : '暂无数据'}</p>
                            <p style={{ margin: '4px 0' }}>最后使用时间: {modelStats.last_used_at ? new Date(modelStats.last_used_at).toLocaleString() : '暂无数据'}</p>
                          </div>
                        </div>
                      </div>
                    ),
                  });
                } else {
                  message.info('该模型暂无使用记录');
                }
              }}
            />
          </Tooltip>
          {/* 只有非内置模型或自定义模型才显示编辑和删除按钮 */}
          {(!record.is_builtin || record.is_builtin === false) && (
            <>
              <Tooltip title="编辑">
                <Button
                  type="text"
                  icon={<EditOutlined />}
                  onClick={() => showEditModelModal(record)}
                />
              </Tooltip>
              <Tooltip title="删除">
                <Button
                  type="text"
                  icon={<DeleteOutlined />}
                  onClick={() => handleDeleteModel(record)}
                  danger
                />
              </Tooltip>
            </>
          )}
        </Space>
      ),
    },
  ];

  // 打开配置抽屉
  const showConfigDrawer = async (config = null) => {
    // 确保所有模型和提供商数据都已加载
    const latestModels = await fetchAllModels();
    await fetchProviders();
    
    setCurrentConfig(config);
    setShowApiKey(false); // 重置密钥显示状态

    if (config) {
      // 编辑现有配置 - 在数据加载完成后再查找模型和提供商
      const selectedModelData = latestModels.find(m => m.id === config.model_id);
      const selectedProviderData = providers.find(p => p.id === config.provider_id);
      
      console.log('编辑配置 - 当前配置:', config);
      console.log('编辑配置 - 配置中的model_id:', config.model_id);
      console.log('编辑配置 - 配置中的provider_id:', config.provider_id);
      console.log('编辑配置 - 最新模型列表数量:', latestModels.length);
      console.log('编辑配置 - 找到的模型:', selectedModelData);
      console.log('编辑配置 - 找到的提供商:', selectedProviderData);
      
      // 设置选中的模型和提供商
      setSelectedModel(selectedModelData);
      setSelectedProvider(selectedProviderData);
      
      configForm.setFieldsValue({
        name: config.name,
        description: config.description,
        provider_id: config.provider_id,
        model_id: config.model_id,
        api_key: config.api_key ? '••••••••••••••••' : '', // 显示星号表示已有密钥
        api_endpoint: config.api_endpoint,
        proxy_url: config.proxy_url,
        max_tokens: config.max_tokens || selectedModelData?.token_limit || 2048,
        temperature: config.temperature || 0.7,
        top_p: config.top_p || 1.0,
        frequency_penalty: config.frequency_penalty || 0.0,
        presence_penalty: config.presence_penalty || 0.0,
        is_default: config.is_default || false,
        priority: config.priority || 0,
        weight: config.weight || 1,
        status: config.status || 'active',
      });

      // 加载能力覆盖设置
      const capabilitiesOverride = config.config?.capabilities_override || {};
      setModelCapabilitiesOverride(capabilitiesOverride);
      setShowCapabilitiesEditor(Object.keys(capabilitiesOverride).length > 0);
    } else {
      // 创建新配置
      setSelectedModel(null);
      setSelectedProvider(null);
      setModelCapabilitiesOverride({});
      setShowCapabilitiesEditor(false);
      configForm.resetFields();
      configForm.setFieldsValue({
        max_tokens: 2048,
        temperature: 0.7,
        top_p: 1.0,
        frequency_penalty: 0.0,
        presence_penalty: 0.0,
        is_default: false,
        priority: 0,
        weight: 1,
        status: 'active',
      });
    }

    setConfigDrawerVisible(true);
  };

  // 处理提供商选择变化
  const handleProviderChange = (providerId) => {
    const provider = providers.find(p => p.id === providerId);
    setSelectedProvider(provider);
    setSelectedModel(null);
    
    // 清空模型选择
    configForm.setFieldsValue({
      model_id: undefined,
      api_endpoint: provider?.default_endpoint || '',
    });
  };

  // 处理模型选择变化
  const handleModelChange = (modelId) => {
    const model = models.find(m => m.id === modelId);
    setSelectedModel(model);
    
    if (model) {
      // 根据模型自动设置参数
      const updates = {
        max_tokens: model.token_limit || 2048, // 优先使用token_limit作为默认值
        api_endpoint: model.api_endpoint || selectedProvider?.default_endpoint || '',
      };
      
      // 根据模型类型设置默认温度
      if (model.model_type === 'embedding') {
        updates.temperature = 0.0;
      } else if (model.model_type === 'chat') {
        updates.temperature = 0.7;
      }
      
      configForm.setFieldsValue(updates);
    }
  };

  // 关闭配置抽屉
  const closeConfigDrawer = () => {
    setConfigDrawerVisible(false);
    setCurrentConfig(null);
    configForm.resetFields();
  };

  // 保存配置
  const handleSaveConfig = async () => {
    try {
      const values = await configForm.validateFields();

      console.log('准备保存配置:', {
        isEdit: !!currentConfig,
        currentConfig: currentConfig,
        formValues: values,
        selectedModel: selectedModel,
        selectedProvider: selectedProvider
      });

      // 验证配置
      if (!values.provider_id) {
        message.error('请选择AI提供商');
        return;
      }
      
      if (!values.model_id) {
        message.error('请选择AI模型');
        return;
      }
      
      // 如果是编辑模式且API密钥是星号，则不更新密钥
      if (currentConfig && values.api_key === '••••••••••••••••') {
        delete values.api_key;
      } else if (!currentConfig && !values.api_key) {
        message.error('请输入API密钥');
        return;
      }
      
      // 验证模型是否属于选择的提供商
      const selectedModelData = models.find(m => m.id === values.model_id);
      console.log('验证模型信息:', {
        model_id: values.model_id,
        provider_id: values.provider_id,
        foundModel: selectedModelData,
        allModels: models.map(m => ({id: m.id, name: m.name, provider_id: m.provider_id}))
      });
      
      if (selectedModelData && selectedModelData.provider_id !== values.provider_id) {
        message.error('选择的模型不属于当前提供商');
        return;
      }
      
      // 验证参数范围
      if (selectedModelData) {
        const maxTokens = selectedModelData.token_limit || selectedModelData.context_window;
        if (maxTokens && values.max_tokens > maxTokens) {
          message.error(`最大Token数不能超过模型限制: ${maxTokens}`);
          return;
        }
      }

      // 添加模型能力覆盖到配置中
      if (Object.keys(modelCapabilitiesOverride).length > 0) {
        values.config = {
          ...values.config,
          capabilities_override: modelCapabilitiesOverride
        };
      }

      console.log('即将保存的最终配置数据:', values);

      if (currentConfig) {
        // 更新配置
        console.log('更新配置中...', {configId: currentConfig.id, data: values});
        const result = await apiService.project.ai.aiConfigs.update(currentProject.id, currentConfig.id, values);
        console.log('配置更新结果:', result);
        message.success('更新配置成功');
      } else {
        // 创建配置
        console.log('创建配置中...', {data: values});
        const result = await apiService.project.ai.aiConfigs.create(currentProject.id, values);
        console.log('配置创建结果:', result);
        message.success('创建配置成功');
      }
      
      closeConfigDrawer();
      // 重新获取配置列表
      console.log('重新获取配置列表...');
      await fetchConfigs();
    } catch (error) {
      if (error.errorFields) {
        // 表单验证错误
        console.error('表单验证错误:', error.errorFields);
        message.error('请检查表单填写是否正确');
      } else {
        console.error('保存配置出错:', error);
        message.error('保存配置失败');
      }
    }
  };

  // 测试配置连接
  const handleTestConfig = async (config) => {
    try {
      setLoading(true);
      const response = await apiService.project.ai.aiConfigs.test(currentProject.id, config.id);
      
      if (response.success) {
        message.success(`连接测试成功 - 响应时间: ${response.response_time}ms`);
        Modal.info({
          title: '连接测试结果',
          content: (
            <div>
              <p><strong>状态:</strong> 连接成功</p>
              <p><strong>响应时间:</strong> {response.response_time}ms</p>
              <p><strong>模型信息:</strong> {response.model_info?.name || '未知'}</p>
              {response.test_message && (
                <div>
                  <p><strong>测试消息:</strong></p>
                  <div style={{ background: '#f5f5f5', padding: '8px', borderRadius: '4px' }}>
                    {response.test_message}
                  </div>
                </div>
              )}
            </div>
          ),
          width: 500
        });
      } else {
        message.error(`连接测试失败: ${response.message}`);
      }
    } catch (error) {
      console.error('测试配置连接失败:', error);
      message.error('测试连接失败，请检查配置');
    } finally {
      setLoading(false);
    }
  };

  // 显示配置使用情况模态框
  const showConfigUsageModal = async (config) => {
    try {
      setCurrentConfigUsage(config);
      setConfigUsageModalVisible(true);
      
      // 获取配置的使用统计
      const response = await apiService.project.ai.aiUsage.getConfigStats(currentProject.id, config.id);
      setConfigUsageStats(response.data || {});
    } catch (error) {
      console.error('获取配置使用统计失败:', error);
      message.error('获取使用统计失败');
    }
  };

  // 配置表格列定义
  const configColumns = [
    {
      title: '配置名称',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <div>
          <div style={{ fontWeight: 'bold' }}>{text}</div>
          {record.description && (
            <div style={{ fontSize: '12px', color: '#666' }}>{record.description}</div>
          )}
        </div>
      ),
    },
    {
      title: '提供商/模型',
      key: 'provider_model',
      render: (_, record) => {
        // 优先使用配置对象中的关联数据
        const provider = record.provider || providers.find(p => p.id === record.provider_id);
        const model = record.model || models.find(m => m.id === record.model_id);
        
        // 调试信息
        if (!model && record.model_id) {
          console.warn(`配置 ${record.name} 未找到对应模型:`, {
            config_name: record.name,
            model_id: record.model_id,
            provider_id: record.provider_id,
            available_models: models.map(m => ({id: m.id, name: m.name, display_name: m.display_name})),
            has_record_model: !!record.model,
            record_model: record.model
          });
        }
        
        return (
          <div>
            <div style={{ fontWeight: 'bold' }}>
              {provider?.display_name || provider?.name || '未知提供商'}
            </div>
            <div style={{ fontSize: '12px', color: '#666' }}>
              {model?.display_name || model?.name || record.model_id || '未知模型'}
            </div>
            {model && (
              <div style={{ fontSize: '11px', color: '#999' }}>
                {model.model_type} | {model.context_window ? `${(model.context_window / 1000).toFixed(0)}K上下文` : ''}
              </div>
            )}
            {!model && record.model_id && (
              <div style={{ fontSize: '11px', color: '#ff4d4f' }}>
                模型ID: {record.model_id.slice(0, 8)}... 
                <Button 
                  type="link" 
                  size="small" 
                  onClick={() => {
                    console.log('配置详细信息:', record);
                    console.log('当前可用模型:', models);
                    message.info('请检查浏览器控制台查看详细调试信息');
                  }}
                >
                  [调试]
                </Button>
              </div>
            )}
          </div>
        );
      },
    },
    {
      title: '能力',
      key: 'capabilities',
      render: (_, record) => {
        // 优先使用配置中的model对象
        let model = record.model;
        
        // 如果没有model对象，记录调试信息
        if (!model) {
          console.warn(`配置 ${record.name} 缺少模型信息`, {
            record_id: record.id,
            model_id: record.model_id,
            provider_id: record.provider_id,
            record_keys: Object.keys(record),
            has_model_obj: !!record.model,
            record_structure: record
          });
          
          return (
            <Tooltip title={`模型信息缺失，配置ID: ${record.id?.slice(0, 8)}...`}>
              <Text type="warning">
                模型信息缺失
                <Button 
                  type="link" 
                  size="small" 
                  onClick={() => {
                    console.log('配置详细信息:', record);
                    message.info('请检查浏览器控制台的详细信息');
                  }}
                >
                  [调试]
                </Button>
              </Text>
            </Tooltip>
          );
        }
        
        // 如果模型没有capabilities信息
        if (!model.capabilities) {
          console.log(`模型 ${model.name} 没有capabilities信息:`, model);
          return (
            <Tooltip title="此模型没有能力信息配置">
              <Text type="secondary">无能力信息</Text>
            </Tooltip>
          );
        }
        
        // 获取基础能力
        const baseCaps = model.capabilities;
        
        // 获取能力覆盖（如果有的话）
        const capabilitiesOverride = record.config?.capabilities_override || {};
        
        // 应用能力覆盖，构建有效能力
        const effectiveCaps = {
          ...baseCaps,
          ...capabilitiesOverride
        };
        
        // 解析模型能力
        const capabilities = [];
        
        if (effectiveCaps.supports_vision) capabilities.push('视觉');
        if (effectiveCaps.supports_audio_input) capabilities.push('音频输入');
        if (effectiveCaps.supports_audio_output) capabilities.push('音频输出');
        if (effectiveCaps.supports_function_calling) capabilities.push('函数调用');
        if (effectiveCaps.supports_streaming) capabilities.push('流式');
        if (effectiveCaps.supports_document_analysis) capabilities.push('文档');
        if (effectiveCaps.supports_code_execution) capabilities.push('代码执行');
        if (effectiveCaps.supports_web_search) capabilities.push('网络搜索');
        if (effectiveCaps.supports_thinking) capabilities.push('长思考');
        
        return (
          <Space wrap>
            {capabilities.length > 0 ? (
              capabilities.map(cap => {
                // 检查这个能力是否被覆盖了
                const isOverridden = capabilitiesOverride.hasOwnProperty(
                  cap === '视觉' ? 'supports_vision' :
                  cap === '音频输入' ? 'supports_audio_input' :
                  cap === '音频输出' ? 'supports_audio_output' :
                  cap === '函数调用' ? 'supports_function_calling' :
                  cap === '流式' ? 'supports_streaming' :
                  cap === '文档' ? 'supports_document_analysis' :
                  cap === '代码执行' ? 'supports_code_execution' :
                  cap === '网络搜索' ? 'supports_web_search' :
                  cap === '长思考' ? 'supports_thinking' : ''
                );
                
                return (
                  <Tag 
                    key={cap} 
                    color={
                      cap === '视觉' ? 'green' : 
                      cap.includes('音频') ? 'orange' : 
                      cap === '函数调用' ? 'blue' : 
                      cap === '流式' ? 'purple' : 
                      cap === '文档' ? 'cyan' :
                      cap === '代码执行' ? 'red' :
                      cap === '网络搜索' ? 'geekblue' :
                      cap === '长思考' ? 'magenta' : 'default'
                    }
                    style={isOverridden ? { borderColor: '#1890ff', borderWidth: 2 } : {}}
                  >
                    {cap}
                    {isOverridden && <span style={{ fontSize: '10px', marginLeft: '2px' }}>*</span>}
                  </Tag>
                );
              })
            ) : (
              <Text type="secondary">基础对话</Text>
            )}
            {Object.keys(capabilitiesOverride).length > 0 && (
              <Tooltip title="此配置包含能力覆盖设置，带*号的能力已被自定义">
                <Tag color="processing" size="small">
                  已自定义
                </Tag>
              </Tooltip>
            )}
          </Space>
        );
      },
    },
    {
      title: 'API配置',
      key: 'api_config',
      render: (_, record) => (
        <div>
          <div style={{ fontSize: '12px' }}>
            <div>密钥: {record.api_key ? '已配置 ✓' : '未配置 ✗'}</div>
            {record.api_endpoint && (
              <div style={{ color: '#666' }}>端点: 自定义</div>
            )}
            {record.proxy_url && (
              <div style={{ color: '#666' }}>代理: 已配置</div>
            )}
          </div>
        </div>
      ),
    },
    {
      title: '参数设置',
      key: 'parameters',
      render: (_, record) => (
        <div style={{ fontSize: '12px' }}>
          <div>温度: {record.temperature || 0.7}</div>
          <div>最大Token: {record.max_tokens || 2048}</div>
          {record.is_default && (
            <Tag color="gold" size="small">默认</Tag>
          )}
        </div>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status, record) => (
        <div>
          <Tag color={status === 'active' ? 'green' : 'default'}>
            {status === 'active' ? '启用' : '禁用'}
          </Tag>
          {record.priority > 0 && (
            <div style={{ fontSize: '11px', color: '#666' }}>
              优先级: {record.priority}
            </div>
          )}
        </div>
      ),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="编辑">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => showConfigDrawer(record)}
            />
          </Tooltip>
          <Tooltip title="测试连接">
            <Button
              type="text"
              icon={<SyncOutlined />}
              onClick={() => handleTestConfig(record)}
            />
          </Tooltip>
          <Tooltip title="查看使用情况">
            <Button
              type="text"
              icon={<BarChartOutlined />}
              onClick={() => showConfigUsageModal(record)}
            />
          </Tooltip>
          <Tooltip title="删除">
            <Button
              type="text"
              icon={<DeleteOutlined />}
              onClick={() => handleDeleteConfig(record)}
              danger
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  // 删除配置
  const handleDeleteConfig = async (config) => {
    Modal.confirm({
      title: '确认删除配置',
      content: (
        <div>
          <p>确定要删除配置 <strong>{config.name}</strong> 吗？</p>
          <p style={{ color: '#ff4d4f', fontSize: '12px' }}>
            注意：删除后无法恢复，且正在使用此配置的功能可能会受到影响。
          </p>
        </div>
      ),
      okText: '确定删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          await apiService.project.ai.aiConfigs.delete(currentProject.id, config.id);
          message.success('删除配置成功');
          fetchConfigs();
        } catch (error) {
          console.error('删除配置失败:', error);
          message.error('删除配置失败，请稍后再试');
        }
      }
    });
  };

  // 导入配置功能
  const showImportModal = () => {
    setImportModalVisible(true);
    setImportFileData(null);
    importForm.resetFields();
  };

  const closeImportModal = () => {
    setImportModalVisible(false);
    setImportFileData(null);
    importForm.resetFields();
  };

  const handleFileUpload = (file) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const data = JSON.parse(e.target.result);
        if (Array.isArray(data)) {
          setImportFileData(data);
          message.success(`已解析 ${data.length} 个配置`);
        } else {
          message.error('文件格式错误，请上传有效的JSON数组');
        }
      } catch (error) {
        message.error('文件解析失败，请检查文件格式');
      }
    };
    reader.readAsText(file);
    return false; // 阻止默认上传
  };

  const handleImportConfigs = async () => {
    if (!importFileData || importFileData.length === 0) {
      message.error('请先上传并解析配置文件');
      return;
    }

    try {
      const values = await importForm.validateFields();
      setImportingConfigs(true);

      let successCount = 0;
      let failCount = 0;
      const errors = [];

      for (const configData of importFileData) {
        try {
          // 验证必需字段
          if (!configData.name || !configData.provider_id || !configData.model_id) {
            throw new Error('缺少必需字段: name, provider_id, model_id');
          }

          // 处理导入选项
          const importData = { ...configData };
          
          if (values.merge_strategy === 'skip_existing') {
            // 检查是否已存在同名配置
            const existingConfig = configs.find(c => c.name === configData.name);
            if (existingConfig) {
              console.log(`跳过已存在的配置: ${configData.name}`);
              continue;
            }
          }

          if (values.merge_strategy === 'update_existing') {
            // 查找已存在的配置并更新
            const existingConfig = configs.find(c => c.name === configData.name);
            if (existingConfig) {
              await apiService.project.ai.aiConfigs.update(currentProject.id, existingConfig.id, importData);
              successCount++;
              continue;
            }
          }

          // 创建新配置
          await apiService.project.ai.aiConfigs.create(currentProject.id, importData);
          successCount++;
        } catch (error) {
          failCount++;
          errors.push(`${configData.name || '未知配置'}: ${error.message}`);
        }
      }

      // 显示导入结果
      if (successCount > 0 && failCount === 0) {
        message.success(`成功导入 ${successCount} 个配置`);
      } else if (successCount > 0 && failCount > 0) {
        Modal.warning({
          title: '导入完成（部分失败）',
          content: (
            <div>
              <p>成功导入: {successCount} 个</p>
              <p>失败: {failCount} 个</p>
              {errors.length > 0 && (
                <div>
                  <p>失败详情:</p>
                  <ul style={{ fontSize: '12px', color: '#ff4d4f' }}>
                    {errors.map((error, index) => (
                      <li key={index}>{error}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          ),
          width: 600
        });
      } else {
        Modal.error({
          title: '导入失败',
          content: (
            <div>
              {errors.length > 0 ? (
                <div>
                  <p>所有配置导入失败:</p>
                  <ul style={{ fontSize: '12px', color: '#ff4d4f' }}>
                    {errors.map((error, index) => (
                      <li key={index}>{error}</li>
                    ))}
                  </ul>
                </div>
              ) : (
                <p>导入过程中发生未知错误</p>
              )}
            </div>
          ),
          width: 600
        });
      }

      closeImportModal();
      fetchConfigs();
    } catch (error) {
      console.error('导入配置失败:', error);
      message.error('导入配置失败');
    } finally {
      setImportingConfigs(false);
    }
  };

  // 显示编辑模型模态框
  const showEditModelModal = (model) => {
    setCurrentEditModel(model);
    setEditModelModalVisible(true);
    
    // 填充表单数据
    editModelForm.setFieldsValue({
      provider_id: model.provider_id,
      name: model.name,
      display_name: model.display_name,
      description: model.description,
      model_type: model.model_type,
      context_window: model.context_window,
      token_limit: model.token_limit,
      input_price_per_1k_tokens: model.input_price_per_1k_tokens,
      output_price_per_1k_tokens: model.output_price_per_1k_tokens,
      image_price_per_1k_tokens: model.image_price_per_1k_tokens,
      audio_price_per_minute: model.audio_price_per_minute,
      video_price_per_minute: model.video_price_per_minute,
      average_response_time: model.average_response_time,
      rate_limit_per_minute: model.rate_limit_per_minute,
      status: model.status,
      // 能力设置
      supports_function_calling: model.capabilities?.supports_function_calling || false,
      supports_vision: model.capabilities?.supports_vision || false,
      supports_audio_input: model.capabilities?.supports_audio_input || false,
      supports_audio_output: model.capabilities?.supports_audio_output || false,
      supports_streaming: model.capabilities?.supports_streaming || false,
      supports_multilingual: model.capabilities?.supports_multilingual || false,
      supports_code_generation: model.capabilities?.supports_code_generation || false,
      supports_reasoning: model.capabilities?.supports_reasoning || false,
      supports_math: model.capabilities?.supports_math || false,
      supports_document_analysis: model.capabilities?.supports_document_analysis || false,
      supports_chart_analysis: model.capabilities?.supports_chart_analysis || false,
      supports_ocr: model.capabilities?.supports_ocr || false
    });
  };

  // 关闭编辑模型模态框
  const closeEditModelModal = () => {
    setEditModelModalVisible(false);
    setCurrentEditModel(null);
    editModelForm.resetFields();
  };

  // 处理编辑模型
  const handleEditModel = async () => {
    try {
      setEditingModel(true);
      const values = await editModelForm.validateFields();
      
      // 构建更新数据
      const updateData = {
        provider_id: values.provider_id,
        name: values.name,
        display_name: values.display_name,
        description: values.description,
        model_type: values.model_type,
        capabilities: {
          supports_function_calling: values.supports_function_calling || false,
          supports_vision: values.supports_vision || false,
          supports_audio_input: values.supports_audio_input || false,
          supports_audio_output: values.supports_audio_output || false,
          supports_streaming: values.supports_streaming || false,
          supports_multilingual: values.supports_multilingual || false,
          supports_code_generation: values.supports_code_generation || false,
          supports_reasoning: values.supports_reasoning || false,
          supports_math: values.supports_math || false,
          supports_document_analysis: values.supports_document_analysis || false,
          supports_chart_analysis: values.supports_chart_analysis || false,
          supports_ocr: values.supports_ocr || false
        },
        context_window: values.context_window,
        token_limit: values.token_limit,
        input_price_per_1k_tokens: values.input_price_per_1k_tokens || 0,
        output_price_per_1k_tokens: values.output_price_per_1k_tokens || 0,
        image_price_per_1k_tokens: values.image_price_per_1k_tokens || 0,
        audio_price_per_minute: values.audio_price_per_minute || 0,
        average_response_time: values.average_response_time,
        rate_limit_per_minute: values.rate_limit_per_minute,
        status: values.status
      };

      // 调用API更新模型
      const response = await apiService.project.ai.aiModels.update(currentProject.id, currentEditModel.id, updateData);
      
      if (response && response.success) {
        message.success('模型更新成功');
        closeEditModelModal();
        // 刷新模型列表
        if (activeTab === 'models') {
          fetchModels();
        } else {
          fetchAllModels();
        }
      } else {
        message.error(response?.message || '更新模型失败');
      }
    } catch (error) {
      console.error('更新模型失败:', error);
      message.error('更新模型失败: ' + (error.response?.data?.detail || error.message));
    } finally {
      setEditingModel(false);
    }
  };

  // 删除模型
  const handleDeleteModel = async (model) => {
    // 检查是否是内置模型
    if (model.is_builtin) {
      message.warning('内置模型无法删除');
      return;
    }

    Modal.confirm({
      title: '确认删除模型',
      content: (
        <div>
          <p>确定要删除模型 <strong>{model.display_name || model.name}</strong> 吗？</p>
          <p style={{ color: '#ff4d4f', fontSize: '12px' }}>
            注意：删除后无法恢复，且使用此模型的配置可能会受到影响。
          </p>
          {model.is_builtin && (
            <p style={{ color: '#fa8c16', fontSize: '12px' }}>
              警告：这是一个内置模型，删除可能影响系统功能。
            </p>
          )}
        </div>
      ),
      okText: '确定删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          await apiService.project.ai.aiModels.delete(currentProject.id, model.id);
          message.success('删除模型成功');
          if (activeTab === 'models') {
            fetchModels();
          } else {
            fetchAllModels();
          }
        } catch (error) {
          console.error('删除模型失败:', error);
          message.error('删除模型失败，请稍后再试');
        }
      }
    });
  };

  return (
    <Card>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Button
            icon={<ArrowLeftOutlined />}
            onClick={() => navigate('/project/ai/assistants')}
            style={{ marginRight: 16 }}
          >
            返回AI助手管理
          </Button>
          <Title level={2} style={{ margin: 0 }}>AI 模型与配置</Title>
        </div>
        <div>
          <Space>
            <Button
              icon={<SettingOutlined />}
              onClick={() => showAdvancedSettingsModal()}
            >
              高级设置
            </Button>
          </Space>
        </div>
      </div>
      
      {/* 使用统计卡片 */}
      <Card style={{ marginBottom: 16 }}>
        <Row gutter={16}>
          <Col span={6}>
            <Statistic
              title="总Token使用量"
              value={usageStats.total_tokens || 0}
              suffix="tokens"
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="总费用"
              value={(usageStats.total_cost || 0).toFixed(2)}
              prefix="¥"
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="本月Token使用量"
              value={usageStats.current_month_tokens || 0}
              suffix="tokens"
            />
          </Col>
          <Col span={6}>
            <Statistic
              title="本月费用"
              value={(usageStats.current_month_cost || 0).toFixed(2)}
              prefix="¥"
            />
          </Col>
        </Row>
      </Card>

      {/* 模型使用统计详情 */}
      {usageStats.model_stats && usageStats.model_stats.length > 0 && (
        <Card title="模型使用统计" style={{ marginBottom: 16 }}>
          <Table
            size="small"
            dataSource={usageStats.model_stats}
            rowKey="model_id"
            pagination={false}
            columns={[
              {
                title: '模型名称',
                dataIndex: 'model_name',
                key: 'model_name',
                render: (text, record) => (
                  <div>
                    <div style={{ fontWeight: 'bold' }}>{text}</div>
                    <div style={{ fontSize: '12px', color: '#666' }}>{record.provider_name}</div>
                  </div>
                )
              },
              {
                title: '调用次数',
                dataIndex: 'request_count',
                key: 'request_count',
                render: (count) => count || 0
              },
              {
                title: 'Token使用量',
                key: 'tokens',
                render: (_, record) => (
                  <div>
                    <div>输入: {(record.input_tokens || 0).toLocaleString()}</div>
                    <div>输出: {(record.output_tokens || 0).toLocaleString()}</div>
                    <div style={{ fontWeight: 'bold' }}>总计: {((record.input_tokens || 0) + (record.output_tokens || 0)).toLocaleString()}</div>
                  </div>
                )
              },
              {
                title: '费用统计',
                key: 'cost',
                render: (_, record) => (
                  <div>
                    <div>输入: ¥{(record.input_cost || 0).toFixed(4)}</div>
                    <div>输出: ¥{(record.output_cost || 0).toFixed(4)}</div>
                    <div style={{ fontWeight: 'bold', color: '#1890ff' }}>
                      总计: ¥{((record.input_cost || 0) + (record.output_cost || 0)).toFixed(4)}
                    </div>
                  </div>
                )
              },
              {
                title: '平均响应时间',
                dataIndex: 'avg_response_time',
                key: 'avg_response_time',
                render: (time) => time ? `${time.toFixed(2)}ms` : '-'
              },
              {
                title: '成功率',
                key: 'success_rate',
                render: (_, record) => {
                  const total = record.request_count || 0;
                  const success = record.success_count || 0;
                  const rate = total > 0 ? (success / total * 100) : 0;
                  return (
                    <div>
                      <div>{rate.toFixed(1)}%</div>
                      <div style={{ fontSize: '12px', color: '#666' }}>
                        {success}/{total}
                      </div>
                    </div>
                  );
                }
              },
              {
                title: '最后使用',
                dataIndex: 'last_used_at',
                key: 'last_used_at',
                render: (time) => time ? new Date(time).toLocaleString() : '-'
              }
            ]}
          />
        </Card>
      )}

      {/* 标签页切换 */}
      <div style={{ marginBottom: 16 }}>
        <Space size="large">
          <span
            style={{
              cursor: 'pointer',
              fontWeight: activeTab === 'models' ? 'bold' : 'normal',
              borderBottom: activeTab === 'models' ? '2px solid #1890ff' : 'none',
              paddingBottom: 8
            }}
            onClick={() => setActiveTab('models')}
          >
            模型列表
          </span>
          <span
            style={{
              cursor: 'pointer',
              fontWeight: activeTab === 'configs' ? 'bold' : 'normal',
              borderBottom: activeTab === 'configs' ? '2px solid #1890ff' : 'none',
              paddingBottom: 8
            }}
            onClick={() => setActiveTab('configs')}
          >
            配置列表
          </span>
        </Space>
      </div>

      {/* 刷新按钮 */}
      <div style={{ marginBottom: 16 }}>
        <Space>
          {activeTab === 'models' && (
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={showAddModelModal}
            >
              新增模型
            </Button>
          )}
          {activeTab === 'models' && (
            <Button
              icon={<SyncOutlined />}
              onClick={() => fetchModels()}
            >
              刷新
            </Button>
          )}
        </Space>
      </div>

      {/* 表格 */}
      {activeTab === 'models' ? (
        <Table
          columns={modelColumns}
          dataSource={models}
          rowKey="id"
          pagination={modelPagination}
          loading={loading}
          onChange={handleTableChange}
        />
      ) : (
        <>
          <div style={{ marginBottom: 16 }}>
            <Space>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => showConfigDrawer()}
              >
                创建配置
              </Button>
              <Button
                icon={<SyncOutlined />}
                onClick={() => fetchConfigs()}
              >
                刷新
              </Button>
              <Button
                icon={<UploadOutlined />}
                onClick={showImportModal}
              >
                导入配置
              </Button>
              <Button
                icon={<DownloadOutlined />}
                onClick={() => {
                  const dataStr = JSON.stringify(configs, null, 2);
                  const dataBlob = new Blob([dataStr], { type: 'application/json' });
                  const url = URL.createObjectURL(dataBlob);
                  const link = document.createElement('a');
                  link.href = url;
                  link.download = `ai_configs_${new Date().toISOString().split('T')[0]}.json`;
                  link.click();
                  URL.revokeObjectURL(url);
                  message.success('配置已导出');
                }}
              >
                导出配置
              </Button>
            </Space>
          </div>
          <Table
            columns={configColumns}
            dataSource={configs}
            rowKey="id"
            pagination={configPagination}
            loading={loading}
            onChange={handleTableChange}
          />
        </>
      )}

      {/* 配置抽屉 */}
      <Drawer
        title={currentConfig ? '编辑配置' : '创建配置'}
        width={700}
        onClose={closeConfigDrawer}
        open={configDrawerVisible}
        styles={{ body: { paddingBottom: 80 } }}
        footer={
          <div style={{ textAlign: 'right' }}>
            <Button onClick={closeConfigDrawer} style={{ marginRight: 8 }}>
              取消
            </Button>
            <Button onClick={handleSaveConfig} type="primary">
              保存
            </Button>
          </div>
        }
      >
        {/* 配置说明 */}
        <div style={{ 
          background: '#e6f7ff', 
          border: '1px solid #91d5ff', 
          borderRadius: '6px', 
          padding: '12px', 
          marginBottom: '16px' 
        }}>
          <div style={{ fontWeight: 'bold', marginBottom: '8px' }}>💡 配置说明</div>
          <div style={{ fontSize: '12px', lineHeight: '1.5' }}>
            <div>• <strong>AI配置</strong>：为特定的AI模型设置API密钥、端点和参数</div>
            <div>• <strong>必填项</strong>：提供商、模型、API密钥是必需的</div>
            <div>• <strong>自动配置</strong>：选择模型后会自动填充推荐参数和能力信息</div>
            <div>• <strong>优先级</strong>：数值越大优先级越高，用于模型路由选择</div>
            <div>• <strong>默认配置</strong>：标记为默认的配置会在未指定时使用</div>
          </div>
        </div>

        <Form
          form={configForm}
          layout="vertical"
        >
          <Form.Item
            name="name"
            label="配置名称"
            rules={[{ required: true, message: '请输入配置名称' }]}
          >
            <Input placeholder="如：GPT-4生产环境配置" />
          </Form.Item>

          <Form.Item
            name="description"
            label="配置描述"
          >
            <Input.TextArea 
              rows={2} 
              placeholder="描述此配置的用途和特点" 
            />
          </Form.Item>

          <Form.Item
            name="provider_id"
            label="AI 提供商"
            rules={[{ required: true, message: '请选择 AI 提供商' }]}
          >
            <Select placeholder="请选择 AI 提供商" onChange={handleProviderChange}>
              {providers.map(provider => (
                <Option key={provider.id} value={provider.id}>
                  {provider.display_name}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="model_id"
            label="AI 模型"
            rules={[{ required: true, message: '请选择 AI 模型' }]}
          >
            <Select 
              placeholder="请选择 AI 模型" 
              onChange={handleModelChange}
              disabled={!selectedProvider}
              notFoundContent={!selectedProvider ? "请先选择提供商" : "暂无可用模型"}
            >
              {models
                .filter(model => !selectedProvider || model.provider_id === selectedProvider.id)
                .map(model => (
                  <Option key={model.id} value={model.id}>
                    <div>
                      <div style={{ fontWeight: 'bold' }}>{model.display_name || model.name}</div>
                      <div style={{ fontSize: '12px', color: '#666' }}>
                        {model.model_type} | 上下文: {model.context_window ? `${(model.context_window / 1000).toFixed(0)}K` : '未知'}
                      </div>
                    </div>
                  </Option>
                ))
              }
            </Select>
          </Form.Item>

          {/* 显示选中模型的能力信息 */}
          {selectedModel && (
            <div style={{ 
              background: '#f5f5f5', 
              padding: '12px', 
              borderRadius: '6px', 
              marginBottom: '16px' 
            }}>
              <div style={{ 
                display: 'flex', 
                justifyContent: 'space-between', 
                alignItems: 'center',
                marginBottom: '8px' 
              }}>
                <div style={{ fontWeight: 'bold' }}>模型能力</div>
                <Button 
                  size="small" 
                  type="text"
                  onClick={() => setShowCapabilitiesEditor(!showCapabilitiesEditor)}
                >
                  {showCapabilitiesEditor ? '隐藏能力编辑' : '自定义能力'}
                </Button>
              </div>
              
              {!showCapabilitiesEditor && (
                <div>
                  <div style={{ display: 'flex', flexWrap: 'wrap', gap: '4px' }}>
                    {selectedModel.capabilities?.supports_vision && (
                      <Tag color="blue" size="small">视觉</Tag>
                    )}
                    {selectedModel.capabilities?.supports_audio_input && (
                      <Tag color="green" size="small">音频输入</Tag>
                    )}
                    {selectedModel.capabilities?.supports_audio_output && (
                      <Tag color="green" size="small">音频输出</Tag>
                    )}
                    {selectedModel.capabilities?.supports_function_calling && (
                      <Tag color="purple" size="small">函数调用</Tag>
                    )}
                    {selectedModel.capabilities?.supports_code_execution && (
                      <Tag color="red" size="small">代码执行</Tag>
                    )}
                    {selectedModel.capabilities?.supports_web_search && (
                      <Tag color="cyan" size="small">网络搜索</Tag>
                    )}
                    {selectedModel.capabilities?.supports_streaming && (
                      <Tag color="geekblue" size="small">流式输出</Tag>
                    )}
                    {selectedModel.capabilities?.supports_thinking && (
                      <Tag color="magenta" size="small">长思考</Tag>
                    )}
                  </div>
                  <div style={{ fontSize: '12px', color: '#666', marginTop: '8px' }}>
                    <div>上下文窗口: {selectedModel.context_window || '未知'} tokens</div>
                    <div>最大输出: {selectedModel.token_limit || '未知'} tokens</div>
                    {selectedModel.input_price_per_1k_tokens && (
                      <div>
                        定价: 输入 ¥{selectedModel.input_price_per_1k_tokens.toFixed(4)}/1K, 
                        输出 ¥{selectedModel.output_price_per_1k_tokens?.toFixed(4) || '未知'}/1K
                      </div>
                    )}
                  </div>
                </div>
              )}

              {/* 能力编辑器 */}
              {showCapabilitiesEditor && (
                <div style={{ 
                  background: '#fff', 
                  padding: '12px', 
                  borderRadius: '6px', 
                  border: '1px solid #d9d9d9' 
                }}>
                  <div style={{ 
                    fontSize: '12px', 
                    color: '#666', 
                    marginBottom: '12px' 
                  }}>
                    💡 您可以覆盖模型的默认能力设置。这些设置仅对当前配置生效。
                  </div>
                  
                  <Row gutter={[12, 8]}>
                    <Col span={8}>
                      <div style={{ fontSize: '12px', marginBottom: '4px' }}>视觉理解</div>
                      <Switch 
                        size="small"
                        checked={modelCapabilitiesOverride.supports_vision ?? selectedModel.capabilities?.supports_vision}
                        onChange={(checked) => setModelCapabilitiesOverride(prev => ({
                          ...prev,
                          supports_vision: checked
                        }))}
                      />
                    </Col>
                    <Col span={8}>
                      <div style={{ fontSize: '12px', marginBottom: '4px' }}>音频输入</div>
                      <Switch 
                        size="small"
                        checked={modelCapabilitiesOverride.supports_audio_input ?? selectedModel.capabilities?.supports_audio_input}
                        onChange={(checked) => setModelCapabilitiesOverride(prev => ({
                          ...prev,
                          supports_audio_input: checked
                        }))}
                      />
                    </Col>
                    <Col span={8}>
                      <div style={{ fontSize: '12px', marginBottom: '4px' }}>音频输出</div>
                      <Switch 
                        size="small"
                        checked={modelCapabilitiesOverride.supports_audio_output ?? selectedModel.capabilities?.supports_audio_output}
                        onChange={(checked) => setModelCapabilitiesOverride(prev => ({
                          ...prev,
                          supports_audio_output: checked
                        }))}
                      />
                    </Col>
                    <Col span={8}>
                      <div style={{ fontSize: '12px', marginBottom: '4px' }}>函数调用</div>
                      <Switch 
                        size="small"
                        checked={modelCapabilitiesOverride.supports_function_calling ?? selectedModel.capabilities?.supports_function_calling}
                        onChange={(checked) => setModelCapabilitiesOverride(prev => ({
                          ...prev,
                          supports_function_calling: checked
                        }))}
                      />
                    </Col>
                    <Col span={8}>
                      <div style={{ fontSize: '12px', marginBottom: '4px' }}>代码执行</div>
                      <Switch 
                        size="small"
                        checked={modelCapabilitiesOverride.supports_code_execution ?? selectedModel.capabilities?.supports_code_execution}
                        onChange={(checked) => setModelCapabilitiesOverride(prev => ({
                          ...prev,
                          supports_code_execution: checked
                        }))}
                      />
                    </Col>
                    <Col span={8}>
                      <div style={{ fontSize: '12px', marginBottom: '4px' }}>网络搜索</div>
                      <Switch 
                        size="small"
                        checked={modelCapabilitiesOverride.supports_web_search ?? selectedModel.capabilities?.supports_web_search}
                        onChange={(checked) => setModelCapabilitiesOverride(prev => ({
                          ...prev,
                          supports_web_search: checked
                        }))}
                      />
                    </Col>
                    <Col span={8}>
                      <div style={{ fontSize: '12px', marginBottom: '4px' }}>流式输出</div>
                      <Switch 
                        size="small"
                        checked={modelCapabilitiesOverride.supports_streaming ?? selectedModel.capabilities?.supports_streaming}
                        onChange={(checked) => setModelCapabilitiesOverride(prev => ({
                          ...prev,
                          supports_streaming: checked
                        }))}
                      />
                    </Col>
                    <Col span={8}>
                      <div style={{ fontSize: '12px', marginBottom: '4px' }}>长思考</div>
                      <Switch 
                        size="small"
                        checked={modelCapabilitiesOverride.supports_thinking ?? selectedModel.capabilities?.supports_thinking}
                        onChange={(checked) => setModelCapabilitiesOverride(prev => ({
                          ...prev,
                          supports_thinking: checked
                        }))}
                      />
                    </Col>
                    <Col span={8}>
                      <div style={{ fontSize: '12px', marginBottom: '4px' }}>文档分析</div>
                      <Switch 
                        size="small"
                        checked={modelCapabilitiesOverride.supports_document_analysis ?? selectedModel.capabilities?.supports_document_analysis}
                        onChange={(checked) => setModelCapabilitiesOverride(prev => ({
                          ...prev,
                          supports_document_analysis: checked
                        }))}
                      />
                    </Col>
                  </Row>

                  <div style={{ marginTop: '12px', textAlign: 'right' }}>
                    <Button 
                      size="small" 
                      onClick={() => {
                        setModelCapabilitiesOverride({});
                        message.success('已重置为模型默认能力');
                      }}
                    >
                      重置为默认
                    </Button>
                  </div>
                </div>
              )}
            </div>
          )}

          <Form.Item
            name="api_key"
            label="API 密钥"
            rules={[{ required: !currentConfig, message: '请输入 API 密钥' }]}
            extra={currentConfig ? "留空表示不修改现有密钥" : ""}
          >
            <Input.Password 
              placeholder="请输入 API 密钥"
              visibilityToggle={{
                visible: showApiKey,
                onVisibleChange: setShowApiKey,
              }}
            />
          </Form.Item>

          <Form.Item
            name="api_endpoint"
            label="API 端点"
            extra="可选，覆盖默认端点"
          >
            <Input placeholder="如: https://api.openai.com/v1" />
          </Form.Item>

          <Form.Item
            name="proxy_url"
            label="代理 URL"
            extra="可选，用于网络代理"
          >
            <Input placeholder="如: http://proxy.example.com:8080" />
          </Form.Item>

          <Divider>高级设置</Divider>

          <Form.Item
            name="max_tokens"
            label="最大 Token 数"
            rules={[{ required: true, message: '请输入最大 Token 数' }]}
            extra={selectedModel ? `模型支持最大: ${selectedModel.token_limit || selectedModel.context_window || '未知'} tokens` : "控制单次响应的最大长度"}
          >
            <InputNumber 
              min={1} 
              max={selectedModel?.token_limit || selectedModel?.context_window || 8192} 
              style={{ width: '100%' }} 
              placeholder="如：2048"
            />
          </Form.Item>

          <Form.Item
            name="temperature"
            label="温度参数"
            rules={[{ required: true, message: '请输入温度参数' }]}
            extra="控制回答的随机性，0-2之间，越高越随机"
          >
            <InputNumber 
              min={0} 
              max={2} 
              step={0.1} 
              style={{ width: '100%' }} 
              placeholder="0.7"
            />
          </Form.Item>

          <Form.Item
            name="top_p"
            label="Top P"
            rules={[{ required: true, message: '请输入 Top P' }]}
            extra="核采样参数，0-1之间，与temperature配合使用"
          >
            <InputNumber 
              min={0} 
              max={1} 
              step={0.1} 
              style={{ width: '100%' }} 
              placeholder="1.0"
            />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="frequency_penalty"
                label="频率惩罚"
                extra="减少重复内容"
              >
                <InputNumber 
                  min={-2} 
                  max={2} 
                  step={0.1} 
                  style={{ width: '100%' }} 
                  placeholder="0.0"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="presence_penalty"
                label="存在惩罚"
                extra="鼓励新话题"
              >
                <InputNumber 
                  min={-2} 
                  max={2} 
                  step={0.1} 
                  style={{ width: '100%' }} 
                  placeholder="0.0"
                />
              </Form.Item>
            </Col>
          </Row>

          <Divider>路由与负载均衡</Divider>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="priority"
                label="路由优先级"
                extra="数值越大优先级越高"
              >
                <InputNumber 
                  min={0} 
                  max={100} 
                  style={{ width: '100%' }} 
                  placeholder="0"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="weight"
                label="负载均衡权重"
                extra="用于负载均衡分配"
              >
                <InputNumber 
                  min={1} 
                  max={100} 
                  style={{ width: '100%' }} 
                  placeholder="1"
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="is_default"
                label="设为默认配置"
                valuePropName="checked"
                extra="未指定配置时使用"
              >
                <Switch />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="status"
                label="配置状态"
              >
                <Select placeholder="选择状态">
                  <Option value="active">启用</Option>
                  <Option value="inactive">禁用</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Drawer>

      {/* 高级设置模态框 */}
      <Modal
        title="AI 高级设置"
        open={advancedSettingsVisible}
        onOk={saveAdvancedSettings}
        onCancel={() => setAdvancedSettingsVisible(false)}
        width={700}
      >
        <Form
          form={advancedSettingsForm}
          layout="vertical"
          initialValues={advancedSettings}
        >
          <Tabs defaultActiveKey="general">
            <TabPane tab="基本设置" key="general">
              <Form.Item
                name="default_system_prompt"
                label="默认系统提示词"
                extra="设置 AI 助手的默认系统提示词，定义其行为和能力"
              >
                <Input.TextArea rows={4} placeholder="请输入默认系统提示词" />
              </Form.Item>

              <Form.Item
                name="max_history_messages"
                label="最大历史消息数"
                extra="AI 助手在对话中保留的最大历史消息数量"
                rules={[{ required: true, message: '请输入最大历史消息数' }]}
              >
                <InputNumber min={1} max={50} style={{ width: '100%' }} />
              </Form.Item>

              <Form.Item
                name="log_level"
                label="日志级别"
                extra="设置 AI 系统的日志记录级别"
              >
                <Select>
                  <Option value="debug">调试 (Debug)</Option>
                  <Option value="info">信息 (Info)</Option>
                  <Option value="warning">警告 (Warning)</Option>
                  <Option value="error">错误 (Error)</Option>
                </Select>
              </Form.Item>
            </TabPane>

            <TabPane tab="知识库与工具" key="features">
              <Form.Item
                name="knowledge_base_enabled"
                label="启用知识库"
                valuePropName="checked"
                extra="允许 AI 助手访问知识库内容"
              >
                <Switch />
              </Form.Item>

              <Form.Item
                name="tools_enabled"
                label="启用工具"
                valuePropName="checked"
                extra="允许 AI 助手使用工具"
              >
                <Switch />
              </Form.Item>
            </TabPane>

            <TabPane tab="缓存设置" key="cache">
              <Form.Item
                name="cache_enabled"
                label="启用缓存"
                valuePropName="checked"
                extra="启用 AI 响应缓存，可以提高响应速度并减少 API 调用"
              >
                <Switch />
              </Form.Item>

              <Form.Item
                name="cache_ttl"
                label="缓存有效期 (秒)"
                extra="缓存的有效期，超过此时间后缓存将失效"
                rules={[{ required: true, message: '请输入缓存有效期' }]}
              >
                <InputNumber min={60} max={86400} style={{ width: '100%' }} />
              </Form.Item>
            </TabPane>
          </Tabs>
        </Form>
      </Modal>

      {/* 配置使用情况模态框 */}
      <Modal
        title={`配置使用情况 - ${currentConfigUsage?.name || ''}`}
        open={configUsageModalVisible}
        onCancel={() => setConfigUsageModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setConfigUsageModalVisible(false)}>
            关闭
          </Button>
        ]}
        width={800}
      >
        {currentConfigUsage && (
          <div>
            <div style={{ marginBottom: 16 }}>
              <strong>配置信息:</strong>
              <div style={{ marginTop: 8, background: '#f5f5f5', padding: 12, borderRadius: 4 }}>
                <p style={{ margin: '4px 0' }}>配置名称: {currentConfigUsage.name}</p>
                <p style={{ margin: '4px 0' }}>描述: {currentConfigUsage.description || '暂无描述'}</p>
                <p style={{ margin: '4px 0' }}>状态: {currentConfigUsage.status === 'active' ? '启用' : '禁用'}</p>
                <p style={{ margin: '4px 0' }}>创建时间: {currentConfigUsage.created_at ? new Date(currentConfigUsage.created_at).toLocaleString() : '未知'}</p>
              </div>
            </div>
            
            <div style={{ marginBottom: 16 }}>
              <strong>调用统计:</strong>
              <div style={{ marginTop: 8, background: '#f5f5f5', padding: 12, borderRadius: 4 }}>
                <p style={{ margin: '4px 0' }}>总调用次数: {configUsageStats.request_count || 0}</p>
                <p style={{ margin: '4px 0' }}>成功次数: {configUsageStats.success_count || 0}</p>
                <p style={{ margin: '4px 0' }}>成功率: {configUsageStats.request_count > 0 ? ((configUsageStats.success_count || 0) / configUsageStats.request_count * 100).toFixed(1) : 0}%</p>
                <p style={{ margin: '4px 0' }}>失败次数: {(configUsageStats.request_count || 0) - (configUsageStats.success_count || 0)}</p>
              </div>
            </div>
            
            <div style={{ marginBottom: 16 }}>
              <strong>Token使用:</strong>
              <div style={{ marginTop: 8, background: '#f5f5f5', padding: 12, borderRadius: 4 }}>
                <p style={{ margin: '4px 0' }}>输入Token: {(configUsageStats.input_tokens || 0).toLocaleString()}</p>
                <p style={{ margin: '4px 0' }}>输出Token: {(configUsageStats.output_tokens || 0).toLocaleString()}</p>
                <p style={{ margin: '4px 0' }}>总Token: {((configUsageStats.input_tokens || 0) + (configUsageStats.output_tokens || 0)).toLocaleString()}</p>
              </div>
            </div>
            
            <div style={{ marginBottom: 16 }}>
              <strong>费用统计:</strong>
              <div style={{ marginTop: 8, background: '#f5f5f5', padding: 12, borderRadius: 4 }}>
                <p style={{ margin: '4px 0' }}>输入费用: ¥{(configUsageStats.input_cost || 0).toFixed(4)}</p>
                <p style={{ margin: '4px 0' }}>输出费用: ¥{(configUsageStats.output_cost || 0).toFixed(4)}</p>
                <p style={{ margin: '4px 0', fontWeight: 'bold', color: '#1890ff' }}>
                  总费用: ¥{((configUsageStats.input_cost || 0) + (configUsageStats.output_cost || 0)).toFixed(4)}
                </p>
              </div>
            </div>
            
            <div>
              <strong>性能指标:</strong>
              <div style={{ marginTop: 8, background: '#f5f5f5', padding: 12, borderRadius: 4 }}>
                <p style={{ margin: '4px 0' }}>平均响应时间: {configUsageStats.avg_response_time ? `${configUsageStats.avg_response_time.toFixed(2)}ms` : '暂无数据'}</p>
                <p style={{ margin: '4px 0' }}>最后使用时间: {configUsageStats.last_used_at ? new Date(configUsageStats.last_used_at).toLocaleString() : '暂无数据'}</p>
                <p style={{ margin: '4px 0' }}>最快响应: {configUsageStats.min_response_time ? `${configUsageStats.min_response_time.toFixed(2)}ms` : '暂无数据'}</p>
                <p style={{ margin: '4px 0' }}>最慢响应: {configUsageStats.max_response_time ? `${configUsageStats.max_response_time.toFixed(2)}ms` : '暂无数据'}</p>
              </div>
            </div>
          </div>
        )}
      </Modal>

      {/* 新增模型模态框 */}
      <Modal
        title="新增自定义模型"
        open={addModelModalVisible}
        onOk={handleAddModel}
        onCancel={closeAddModelModal}
        width={800}
        confirmLoading={addingModel}
        okText="添加模型"
        cancelText="取消"
      >
        <div style={{ 
          background: '#e6f7ff', 
          border: '1px solid #91d5ff', 
          borderRadius: '6px', 
          padding: '12px', 
          marginBottom: '16px' 
        }}>
          <div style={{ fontWeight: 'bold', marginBottom: '8px' }}>💡 新增模型说明</div>
          <div style={{ fontSize: '12px', lineHeight: '1.5' }}>
            <div>• <strong>兼容性</strong>：支持添加兼容 OpenAI API 格式的模型</div>
            <div>• <strong>必填项</strong>：提供商、模型名称、显示名称、模型类型是必需的</div>
            <div>• <strong>能力设置</strong>：请根据模型实际能力选择相应的功能支持</div>
            <div>• <strong>价格配置</strong>：可选填写，用于成本计算和统计</div>
          </div>
        </div>

        <Form
          form={addModelForm}
          layout="vertical"
        >
          <Form.Item
            name="provider_id"
            label="AI 提供商"
            rules={[{ required: true, message: '请选择 AI 提供商' }]}
          >
            <Select placeholder="请选择 AI 提供商">
              {providers.map(provider => (
                <Option key={provider.id} value={provider.id}>
                  {provider.display_name}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="name"
            label="模型名称 (API调用名称)"
            rules={[{ required: true, message: '请输入模型名称' }]}
            extra="API调用时使用的模型名称，如：gpt-4、claude-3-sonnet等"
          >
            <Input placeholder="如：gpt-4-turbo" />
          </Form.Item>

          <Form.Item
            name="display_name"
            label="显示名称"
            rules={[{ required: true, message: '请输入显示名称' }]}
            extra="在界面中显示的友好名称"
          >
            <Input placeholder="如：GPT-4 Turbo" />
          </Form.Item>

          <Form.Item
            name="description"
            label="模型描述"
          >
            <Input.TextArea 
              rows={2} 
              placeholder="描述模型的特点和适用场景" 
            />
          </Form.Item>

          <Form.Item
            name="model_type"
            label="模型类型"
            rules={[{ required: true, message: '请选择模型类型' }]}
          >
            <Select placeholder="请选择模型类型">
              <Option value="chat">对话模型 (Chat)</Option>
              <Option value="embedding">嵌入模型 (Embedding)</Option>
              <Option value="vision">视觉模型 (Vision)</Option>
              <Option value="audio">音频模型 (Audio)</Option>
              <Option value="multimodal">多模态模型 (Multimodal)</Option>
            </Select>
          </Form.Item>

          <div style={{ 
            background: '#f5f5f5', 
            padding: '12px', 
            borderRadius: '6px', 
            marginBottom: '16px' 
          }}>
            <div style={{ fontWeight: 'bold', marginBottom: '8px' }}>模型能力配置</div>
            <Row gutter={[16, 8]}>
              <Col span={8}>
                <Form.Item
                  name="supports_function_calling"
                  valuePropName="checked"
                  style={{ marginBottom: 8 }}
                >
                  <Switch size="small" /> 函数调用
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="supports_vision"
                  valuePropName="checked"
                  style={{ marginBottom: 8 }}
                >
                  <Switch size="small" /> 视觉理解
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="supports_audio_input"
                  valuePropName="checked"
                  style={{ marginBottom: 8 }}
                >
                  <Switch size="small" /> 音频输入
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="supports_audio_output"
                  valuePropName="checked"
                  style={{ marginBottom: 8 }}
                >
                  <Switch size="small" /> 音频输出
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="supports_streaming"
                  valuePropName="checked"
                  style={{ marginBottom: 8 }}
                >
                  <Switch size="small" /> 流式输出
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="supports_multilingual"
                  valuePropName="checked"
                  style={{ marginBottom: 8 }}
                >
                  <Switch size="small" /> 多语言
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="supports_code_generation"
                  valuePropName="checked"
                  style={{ marginBottom: 8 }}
                >
                  <Switch size="small" /> 代码生成
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="supports_reasoning"
                  valuePropName="checked"
                  style={{ marginBottom: 8 }}
                >
                  <Switch size="small" /> 推理能力
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="supports_math"
                  valuePropName="checked"
                  style={{ marginBottom: 8 }}
                >
                  <Switch size="small" /> 数学计算
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="supports_document_analysis"
                  valuePropName="checked"
                  style={{ marginBottom: 8 }}
                >
                  <Switch size="small" /> 文档分析
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="supports_chart_analysis"
                  valuePropName="checked"
                  style={{ marginBottom: 8 }}
                >
                  <Switch size="small" /> 图表分析
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="supports_ocr"
                  valuePropName="checked"
                  style={{ marginBottom: 8 }}
                >
                  <Switch size="small" /> OCR识别
                </Form.Item>
              </Col>
            </Row>
          </div>

          <div style={{ 
            background: '#f5f5f5', 
            padding: '12px', 
            borderRadius: '6px', 
            marginBottom: '16px' 
          }}>
            <div style={{ fontWeight: 'bold', marginBottom: '8px' }}>性能参数</div>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="context_window"
                  label="上下文窗口"
                  rules={[{ required: true, message: '请输入上下文窗口大小' }]}
                  extra="模型支持的最大上下文长度"
                >
                  <InputNumber 
                    min={1024} 
                    max={2000000} 
                    style={{ width: '100%' }} 
                    placeholder="如：32768"
                    addonAfter="tokens"
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="token_limit"
                  label="最大输出Token"
                  rules={[{ required: true, message: '请输入最大输出Token数' }]}
                  extra="单次响应的最大Token数"
                >
                  <InputNumber 
                    min={256} 
                    max={32768} 
                    style={{ width: '100%' }} 
                    placeholder="如：4096"
                    addonAfter="tokens"
                  />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="average_response_time"
                  label="平均响应时间"
                  extra="模型的平均响应时间（秒）"
                >
                  <InputNumber 
                    min={0.1} 
                    max={60} 
                    step={0.1}
                    style={{ width: '100%' }} 
                    placeholder="如：2.5"
                    addonAfter="秒"
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="rate_limit_per_minute"
                  label="每分钟请求限制"
                  extra="API的速率限制"
                >
                  <InputNumber 
                    min={1} 
                    max={10000} 
                    style={{ width: '100%' }} 
                    placeholder="如：60"
                    addonAfter="次/分钟"
                  />
                </Form.Item>
              </Col>
            </Row>
          </div>

          <div style={{ 
            background: '#f5f5f5', 
            padding: '12px', 
            borderRadius: '6px', 
            marginBottom: '16px' 
          }}>
            <div style={{ fontWeight: 'bold', marginBottom: '8px' }}>价格配置（可选）</div>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="input_price_per_1k_tokens"
                  label="输入价格（每1K tokens）"
                  extra="输入Token的价格，单位：人民币"
                >
                  <InputNumber 
                    min={0} 
                    max={1} 
                    step={0.0001}
                    style={{ width: '100%' }} 
                    placeholder="如：0.0030"
                    addonBefore="¥"
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="output_price_per_1k_tokens"
                  label="输出价格（每1K tokens）"
                  extra="输出Token的价格，单位：人民币"
                >
                  <InputNumber 
                    min={0} 
                    max={1} 
                    step={0.0001}
                    style={{ width: '100%' }} 
                    placeholder="如：0.0060"
                    addonBefore="¥"
                  />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="image_price_per_1k_tokens"
                  label="图像价格（每1K tokens）"
                  extra="图像处理的价格，单位：人民币"
                >
                  <InputNumber 
                    min={0} 
                    max={1} 
                    step={0.0001}
                    style={{ width: '100%' }} 
                    placeholder="如：0.0085"
                    addonBefore="¥"
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="audio_price_per_minute"
                  label="音频价格（每分钟）"
                  extra="音频处理的价格，单位：人民币"
                >
                  <InputNumber 
                    min={0} 
                    max={10} 
                    step={0.001}
                    style={{ width: '100%' }} 
                    placeholder="如：0.006"
                    addonBefore="¥"
                  />
                </Form.Item>
              </Col>
            </Row>
          </div>
        </Form>
      </Modal>

      {/* 导入配置模态框 */}
      <Modal
        title="导入AI配置"
        open={importModalVisible}
        onOk={handleImportConfigs}
        onCancel={closeImportModal}
        width={700}
        confirmLoading={importingConfigs}
        okText="开始导入"
        cancelText="取消"
      >
        <div style={{ 
          background: '#e6f7ff', 
          border: '1px solid #91d5ff', 
          borderRadius: '6px', 
          padding: '12px', 
          marginBottom: '16px' 
        }}>
          <div style={{ fontWeight: 'bold', marginBottom: '8px' }}>💡 导入说明</div>
          <div style={{ fontSize: '12px', lineHeight: '1.5' }}>
            <div>• <strong>文件格式</strong>：支持JSON格式的配置文件</div>
            <div>• <strong>数据结构</strong>：文件应包含配置对象数组</div>
            <div>• <strong>必需字段</strong>：name, provider_id, model_id, api_key</div>
            <div>• <strong>安全提醒</strong>：导入的API密钥将被加密存储</div>
          </div>
        </div>

        <Form
          form={importForm}
          layout="vertical"
          initialValues={{
            merge_strategy: 'skip_existing'
          }}
        >
          <Form.Item
            name="merge_strategy"
            label="导入策略"
            rules={[{ required: true, message: '请选择导入策略' }]}
          >
            <Select placeholder="选择如何处理重复配置">
              <Option value="skip_existing">跳过已存在的配置</Option>
              <Option value="update_existing">更新已存在的配置</Option>
              <Option value="create_new">创建新配置（重名时自动重命名）</Option>
            </Select>
          </Form.Item>

          <Form.Item
            label="配置文件"
            extra="请选择JSON格式的配置文件"
          >
            <Upload
              accept=".json"
              beforeUpload={handleFileUpload}
              showUploadList={false}
              maxCount={1}
            >
              <Button icon={<PlusOutlined />}>选择文件</Button>
            </Upload>
          </Form.Item>

          {importFileData && importFileData.length > 0 && (
            <div style={{ 
              background: '#f6ffed', 
              border: '1px solid #b7eb8f', 
              borderRadius: '6px', 
              padding: '12px', 
              marginTop: '16px' 
            }}>
              <div style={{ fontWeight: 'bold', marginBottom: '8px', color: '#52c41a' }}>
                ✓ 文件解析成功
              </div>
              <div style={{ fontSize: '12px' }}>
                <div>共解析到 {importFileData.length} 个配置</div>
                <div style={{ marginTop: 8 }}>
                  配置预览:
                  <ul style={{ margin: '4px 0', paddingLeft: '16px' }}>
                    {importFileData.slice(0, 5).map((config, index) => (
                      <li key={index}>
                        {config.name || '未命名配置'} 
                        {config.model?.name && ` (${config.model.name})`}
                      </li>
                    ))}
                    {importFileData.length > 5 && (
                      <li>... 还有 {importFileData.length - 5} 个配置</li>
                    )}
                  </ul>
                </div>
              </div>
            </div>
          )}
        </Form>
      </Modal>

      {/* 编辑模型模态框 */}
      <Modal
        title={`编辑模型 - ${currentEditModel?.display_name || currentEditModel?.name || ''}`}
        open={editModelModalVisible}
        onOk={handleEditModel}
        onCancel={closeEditModelModal}
        width={800}
        confirmLoading={editingModel}
        okText="保存更改"
        cancelText="取消"
      >
        <div style={{ 
          background: '#fff3cd', 
          border: '1px solid #ffeaa7', 
          borderRadius: '6px', 
          padding: '12px', 
          marginBottom: '16px' 
        }}>
          <div style={{ fontWeight: 'bold', marginBottom: '8px' }}>⚠️ 编辑模型说明</div>
          <div style={{ fontSize: '12px', lineHeight: '1.5' }}>
            <div>• <strong>谨慎操作</strong>：修改模型参数可能影响现有配置的正常使用</div>
            <div>• <strong>内置模型</strong>：系统内置模型建议不要随意修改核心参数</div>
            <div>• <strong>影响范围</strong>：更改将影响所有使用此模型的AI配置</div>
            <div>• <strong>备份建议</strong>：重要修改前建议先导出相关配置作为备份</div>
          </div>
        </div>

        <Form
          form={editModelForm}
          layout="vertical"
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="provider_id"
                label="AI 提供商"
                rules={[{ required: true, message: '请选择 AI 提供商' }]}
              >
                <Select placeholder="请选择 AI 提供商" disabled={currentEditModel?.is_builtin}>
                  {providers.map(provider => (
                    <Option key={provider.id} value={provider.id}>
                      {provider.display_name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="model_type"
                label="模型类型"
                rules={[{ required: true, message: '请选择模型类型' }]}
              >
                <Select placeholder="请选择模型类型" disabled={currentEditModel?.is_builtin}>
                  <Option value="chat">对话模型 (Chat)</Option>
                  <Option value="embedding">嵌入模型 (Embedding)</Option>
                  <Option value="vision">视觉模型 (Vision)</Option>
                  <Option value="audio">音频模型 (Audio)</Option>
                  <Option value="multimodal">多模态模型 (Multimodal)</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="name"
            label="模型名称 (API调用名称)"
            rules={[{ required: true, message: '请输入模型名称' }]}
            extra="API调用时使用的模型名称"
          >
            <Input placeholder="如：gpt-4-turbo" disabled={currentEditModel?.is_builtin} />
          </Form.Item>

          <Form.Item
            name="display_name"
            label="显示名称"
            rules={[{ required: true, message: '请输入显示名称' }]}
            extra="在界面中显示的友好名称"
          >
            <Input placeholder="如：GPT-4 Turbo" />
          </Form.Item>

          <Form.Item
            name="description"
            label="模型描述"
          >
            <Input.TextArea 
              rows={2} 
              placeholder="描述模型的特点和适用场景" 
            />
          </Form.Item>

          <Form.Item
            name="status"
            label="模型状态"
            rules={[{ required: true, message: '请选择模型状态' }]}
          >
            <Select placeholder="请选择模型状态">
              <Option value="active">启用</Option>
              <Option value="inactive">禁用</Option>
            </Select>
          </Form.Item>

          <div style={{ 
            background: '#f5f5f5', 
            padding: '12px', 
            borderRadius: '6px', 
            marginBottom: '16px' 
          }}>
            <div style={{ fontWeight: 'bold', marginBottom: '8px' }}>模型能力配置</div>
            <Row gutter={[16, 8]}>
              <Col span={8}>
                <Form.Item
                  name="supports_function_calling"
                  valuePropName="checked"
                  style={{ marginBottom: 8 }}
                >
                  <Switch size="small" /> 函数调用
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="supports_vision"
                  valuePropName="checked"
                  style={{ marginBottom: 8 }}
                >
                  <Switch size="small" /> 视觉理解
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="supports_audio_input"
                  valuePropName="checked"
                  style={{ marginBottom: 8 }}
                >
                  <Switch size="small" /> 音频输入
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="supports_audio_output"
                  valuePropName="checked"
                  style={{ marginBottom: 8 }}
                >
                  <Switch size="small" /> 音频输出
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="supports_streaming"
                  valuePropName="checked"
                  style={{ marginBottom: 8 }}
                >
                  <Switch size="small" /> 流式输出
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="supports_multilingual"
                  valuePropName="checked"
                  style={{ marginBottom: 8 }}
                >
                  <Switch size="small" /> 多语言
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="supports_code_generation"
                  valuePropName="checked"
                  style={{ marginBottom: 8 }}
                >
                  <Switch size="small" /> 代码生成
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="supports_reasoning"
                  valuePropName="checked"
                  style={{ marginBottom: 8 }}
                >
                  <Switch size="small" /> 推理能力
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="supports_math"
                  valuePropName="checked"
                  style={{ marginBottom: 8 }}
                >
                  <Switch size="small" /> 数学计算
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="supports_document_analysis"
                  valuePropName="checked"
                  style={{ marginBottom: 8 }}
                >
                  <Switch size="small" /> 文档分析
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="supports_chart_analysis"
                  valuePropName="checked"
                  style={{ marginBottom: 8 }}
                >
                  <Switch size="small" /> 图表分析
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item
                  name="supports_ocr"
                  valuePropName="checked"
                  style={{ marginBottom: 8 }}
                >
                  <Switch size="small" /> OCR识别
                </Form.Item>
              </Col>
            </Row>
          </div>

          <div style={{ 
            background: '#f5f5f5', 
            padding: '12px', 
            borderRadius: '6px', 
            marginBottom: '16px' 
          }}>
            <div style={{ fontWeight: 'bold', marginBottom: '8px' }}>性能参数</div>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="context_window"
                  label="上下文窗口"
                  rules={[{ required: true, message: '请输入上下文窗口大小' }]}
                  extra="模型支持的最大上下文长度"
                >
                  <InputNumber 
                    min={1024} 
                    max={2000000} 
                    style={{ width: '100%' }} 
                    placeholder="如：32768"
                    addonAfter="tokens"
                    disabled={currentEditModel?.is_builtin}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="token_limit"
                  label="最大输出Token"
                  rules={[{ required: true, message: '请输入最大输出Token数' }]}
                  extra="单次响应的最大Token数"
                >
                  <InputNumber 
                    min={256} 
                    max={32768} 
                    style={{ width: '100%' }} 
                    placeholder="如：4096"
                    addonAfter="tokens"
                    disabled={currentEditModel?.is_builtin}
                  />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="average_response_time"
                  label="平均响应时间"
                  extra="模型的平均响应时间（秒）"
                >
                  <InputNumber 
                    min={0.1} 
                    max={60} 
                    step={0.1}
                    style={{ width: '100%' }} 
                    placeholder="如：2.5"
                    addonAfter="秒"
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="rate_limit_per_minute"
                  label="每分钟请求限制"
                  extra="API的速率限制"
                >
                  <InputNumber 
                    min={1} 
                    max={10000} 
                    style={{ width: '100%' }} 
                    placeholder="如：60"
                    addonAfter="次/分钟"
                  />
                </Form.Item>
              </Col>
            </Row>
          </div>

          <div style={{ 
            background: '#f5f5f5', 
            padding: '12px', 
            borderRadius: '6px', 
            marginBottom: '16px' 
          }}>
            <div style={{ fontWeight: 'bold', marginBottom: '8px' }}>价格配置</div>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="input_price_per_1k_tokens"
                  label="输入价格（每1K tokens）"
                  extra="输入Token的价格，单位：人民币"
                >
                  <InputNumber 
                    min={0} 
                    max={1} 
                    step={0.0001}
                    style={{ width: '100%' }} 
                    placeholder="如：0.0030"
                    addonBefore="¥"
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="output_price_per_1k_tokens"
                  label="输出价格（每1K tokens）"
                  extra="输出Token的价格，单位：人民币"
                >
                  <InputNumber 
                    min={0} 
                    max={1} 
                    step={0.0001}
                    style={{ width: '100%' }} 
                    placeholder="如：0.0060"
                    addonBefore="¥"
                  />
                </Form.Item>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="image_price_per_1k_tokens"
                  label="图像价格（每1K tokens）"
                  extra="图像处理的价格，单位：人民币"
                >
                  <InputNumber 
                    min={0} 
                    max={1} 
                    step={0.0001}
                    style={{ width: '100%' }} 
                    placeholder="如：0.0085"
                    addonBefore="¥"
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="audio_price_per_minute"
                  label="音频价格（每分钟）"
                  extra="音频处理的价格，单位：人民币"
                >
                  <InputNumber 
                    min={0} 
                    max={10} 
                    step={0.001}
                    style={{ width: '100%' }} 
                    placeholder="如：0.006"
                    addonBefore="¥"
                  />
                </Form.Item>
              </Col>
            </Row>
          </div>
        </Form>
      </Modal>
    </Card>
  );
};

export default AIModelList;
