import logging
from typing import Dict, Any
from .ocr_engine import OCREngine

# 配置日志
logger = logging.getLogger(__name__)

async def recognize_receipt(image_data: bytes) -> Dict[str, Any]:
    """
    识别小票信息

    Args:
        image_data: 小票图片数据

    Returns:
        Dict: 识别结果，包含成功标志、消费总金额、会员ID、商店名称、交易时间、小票ID、商品列表和原始文本
    """
    try:
        # 使用OCR引擎识别小票
        result = OCREngine.recognize_receipt(image_data)
        return result
    except Exception as e:
        logger.error(f"小票识别失败: {str(e)}")
        return {
            "success": False,
            "error_message": str(e)
        }
