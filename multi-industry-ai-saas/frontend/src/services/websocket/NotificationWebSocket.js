import { message } from 'antd';
import { getProjectId } from '../api/httpClient';
import { 
  buildWebSocketUrl, 
  getReconnectConfig, 
  isOnline, 
  isHMRWebSocketError
} from '../../utils/websocketConfig';
import { handleWebSocketError } from '../../utils/websocketErrorHandler';

/**
 * 通知WebSocket客户端
 * 用于接收实时通知
 */
class NotificationWebSocket {
  constructor() {
    this.socket = null;
    this.connected = false;
    this.reconnectAttempts = 0;
    
    // 使用配置工具获取重连设置
    const reconnectConfig = getReconnectConfig();
    this.maxReconnectAttempts = reconnectConfig.maxAttempts;
    this.reconnectInterval = reconnectConfig.interval;
    this.maxReconnectDelay = reconnectConfig.maxDelay;
    
    this.onMessageCallbacks = [];
    this.onConnectCallbacks = [];
    this.onDisconnectCallbacks = [];
    this.onSettingsUpdateCallbacks = [];
    this.onWorkflowSettingsUpdateCallbacks = [];
    
    // 网络状态监听器
    this.onlineHandler = null;
    this.offlineHandler = null;
  }

  /**
   * 连接WebSocket
   * @param {string} userId - 用户ID
   * @param {string} projectId - 项目ID
   * @returns {Promise} - 连接Promise
   */
  connect(userId, projectId) {
    return new Promise((resolve, reject) => {
      if (this.connected && this.socket) {
        resolve(this.socket);
        return;
      }

      // 如果没有提供projectId，尝试从localStorage获取
      if (!projectId) {
        projectId = getProjectId();
      }

      // 如果没有userId或projectId，无法连接
      if (!userId || !projectId) {
        reject(new Error('缺少用户ID或项目ID'));
        return;
      }

      // 获取认证令牌
      const token = localStorage.getItem('token');
      if (!token) {
        reject(new Error('未登录'));
        return;
      }

      // 检查网络状态
      if (!isOnline()) {
        console.log('网络离线，无法连接WebSocket');
        reject(new Error('网络离线'));
        return;
      }

      // 使用配置工具构建WebSocket URL
      const wsUrl = buildWebSocketUrl('/api/v1/ws/notifications', {
        user_id: userId,
        project_id: projectId,
        token: token
      });

      console.log('尝试连接WebSocket:', wsUrl);

      // 创建WebSocket连接
      this.socket = new WebSocket(wsUrl);

      // 设置连接超时
      const connectTimeout = setTimeout(() => {
        if (this.socket && this.socket.readyState === WebSocket.CONNECTING) {
          console.log('WebSocket连接超时');
          this.socket.close();
          reject(new Error('连接超时'));
        }
      }, 10000); // 10秒超时

      // 连接打开事件
      this.socket.onopen = () => {
        clearTimeout(connectTimeout);
        console.log('通知WebSocket连接已建立');
        this.connected = true;
        this.reconnectAttempts = 0;

        // 触发连接回调
        this.onConnectCallbacks.forEach(callback => {
          try {
            callback();
          } catch (error) {
            console.error('WebSocket连接回调执行失败:', error);
          }
        });

        // 启动心跳
        this.startHeartbeat();

        resolve(this.socket);
      };

      // 接收消息事件
      this.socket.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          console.log('收到WebSocket消息:', data);

          // 处理不同类型的消息
          if (data.type === 'connection_established') {
            console.log('WebSocket连接已确认:', data.message);
            // 连接已确认，可以在这里执行一些初始化操作
          }
          else if (data.type === 'pong') {
            // 收到心跳响应
            console.log('收到心跳响应');
            this.lastPongTime = Date.now();
          }
          else if (data.type === 'settings_updated') {
            // 收到通知设置更新
            console.log('收到通知设置更新:', data.settings);

            // 保存设置到本地存储
            localStorage.setItem('notificationSettings', JSON.stringify(data.settings));

            // 触发设置更新回调
            this.onSettingsUpdateCallbacks.forEach(callback => callback(data.settings));
          }
          else if (data.type === 'workflow_settings_updated') {
            // 收到工作流通知设置更新
            console.log('收到工作流通知设置更新:', data.workflow_type, data.settings);

            // 获取现有设置
            let workflowSettings = JSON.parse(localStorage.getItem('workflowNotificationSettings') || '{}');

            // 更新特定工作流的设置
            workflowSettings[data.workflow_type] = data.settings;

            // 保存更新后的设置
            localStorage.setItem('workflowNotificationSettings', JSON.stringify(workflowSettings));

            // 触发工作流设置更新回调
            this.onWorkflowSettingsUpdateCallbacks.forEach(callback =>
              callback(data.workflow_type, data.settings)
            );
          }
          else {
            // 处理通知消息
            console.log('收到通知:', data);

            // 确认收到通知
            if (data.id) {
              this.acknowledgeNotification(data.id);
            }

            // 触发消息回调
            this.onMessageCallbacks.forEach(callback => callback(data));
          }
        } catch (error) {
          console.error('解析WebSocket消息失败:', error);
        }
      };

      // 连接关闭事件
      this.socket.onclose = (event) => {
        console.log('通知WebSocket连接已关闭', event);
        this.connected = false;

        // 触发断开连接回调
        this.onDisconnectCallbacks.forEach(callback => callback());

        // 检查是否应该重连
        const shouldReconnect = () => {
          try {
            // 从本地存储获取设置
            const settingsJson = localStorage.getItem('notificationSettings');
            if (settingsJson) {
              const settings = JSON.parse(settingsJson);

              // 检查频率设置
              const normalFrequency = String(settings.normal_frequency || '').trim().toLowerCase();
              const highPriorityFrequency = String(settings.high_priority_frequency || '').trim().toLowerCase();

              const shouldReconnect = normalFrequency === 'realtime' || highPriorityFrequency === 'realtime';

              console.log('WebSocket关闭后重连检查 - 根据设置决定是否重连:', {
                normalFrequency,
                highPriorityFrequency,
                shouldReconnect
              });

              return shouldReconnect;
            }
          } catch (error) {
            console.error('检查WebSocket关闭后重连设置失败:', error);
          }

          // 默认重连
          return true;
        };

        // 只有在设置为实时通知时才重连
        if (shouldReconnect()) {
          // 尝试重连
          this.reconnect(userId, projectId);
        } else {
          console.log('WebSocket关闭 - 不重连（根据用户设置）');
        }
      };

      // 连接错误事件
      this.socket.onerror = (error) => {
        clearTimeout(connectTimeout);
        console.error('通知WebSocket连接错误:', error);
        this.connected = false;

        // 使用统一的错误处理器
        handleWebSocketError(error, wsUrl, (errorMessage) => {
          // 对于跨域错误，直接显示错误消息
          if (errorMessage.includes('跨域')) {
            message.error(errorMessage);
            reject(error);
            return;
          }
          
          // 其他错误根据情况显示
          if (!isHMRWebSocketError(wsUrl)) {
            message.warning(errorMessage);
          }
        });

        // 检查是否需要重连
        if (this.shouldReconnectBasedOnSettings() && this.reconnectAttempts < this.maxReconnectAttempts) {
          console.log('WebSocket连接失败，将在稍后尝试重连');
          setTimeout(() => {
            this.reconnect(userId, projectId);
          }, 1000);
        } else {
          console.log('WebSocket连接失败 - 不重连（根据用户设置或达到最大重连次数）');
        }

        reject(error);
      };

      // 监听网络状态变化
      this.setupNetworkListeners(userId, projectId);
    });
  }

  /**
   * 重连WebSocket
   * @param {string} userId - 用户ID
   * @param {string} projectId - 项目ID
   */
  reconnect(userId, projectId) {
    // 如果已经连接或超过最大重连次数，不重连
    if (this.connected || this.reconnectAttempts >= this.maxReconnectAttempts) {
      if (this.reconnectAttempts >= this.maxReconnectAttempts) {
        console.log('WebSocket重连次数已达上限，停止重连');
        message.warning('通知连接失败，请刷新页面重试');
      }
      return;
    }

    // 检查网络状态
    if (!isOnline()) {
      console.log('网络离线，暂停重连');
      return;
    }

    // 检查是否应该重连
    if (!this.shouldReconnectBasedOnSettings()) {
      console.log('WebSocket重连 - 不重连（根据用户设置）');
      return;
    }

    this.reconnectAttempts++;
    console.log(`尝试重连WebSocket (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);

    // 延迟重连，避免频繁重连，使用指数退避
    const delay = Math.min(this.reconnectInterval * Math.pow(2, this.reconnectAttempts - 1), this.maxReconnectDelay);
    
    setTimeout(() => {
      // 重新连接
      this.connect(userId, projectId)
        .then(() => {
          console.log('WebSocket重连成功');
        })
        .catch((error) => {
          console.error('WebSocket重连失败:', error);
          
          // 如果还有重连机会，继续尝试
          if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnect(userId, projectId);
          } else {
            console.log('WebSocket重连失败，已达最大重连次数');
            message.error('通知连接失败，请刷新页面重试');
          }
        });
    }, delay);
  }

  /**
   * 断开WebSocket连接
   */
  disconnect() {
    // 停止心跳
    this.stopHeartbeat();

    // 清理网络监听器
    if (this.onlineHandler) {
      window.removeEventListener('online', this.onlineHandler);
      this.onlineHandler = null;
    }
    if (this.offlineHandler) {
      window.removeEventListener('offline', this.offlineHandler);
      this.offlineHandler = null;
    }

    if (this.socket) {
      this.socket.close();
      this.socket = null;
      this.connected = false;
    }

    // 重置重连计数
    this.reconnectAttempts = 0;
  }

  /**
   * 启动心跳
   */
  startHeartbeat() {
    // 清除现有的心跳定时器
    this.stopHeartbeat();

    // 设置心跳间隔为30秒，避免频繁发送
    this.heartbeatInterval = setInterval(() => {
      if (this.connected && this.socket && this.socket.readyState === WebSocket.OPEN) {
        try {
          this.socket.send(JSON.stringify({
            type: 'ping',
            timestamp: new Date().toISOString()
          }));
          console.log('发送心跳包');
        } catch (error) {
          console.error('发送心跳包失败:', error);
          this.connected = false;
        }
      } else {
        console.log('WebSocket未连接，停止心跳');
        this.stopHeartbeat();
      }
    }, 30000); // 30秒发送一次心跳

    // 设置心跳超时检查，如果60秒内没有收到pong，认为连接断开
    this.lastPongTime = Date.now();
    this.heartbeatTimeoutCheck = setInterval(() => {
      if (this.connected && Date.now() - this.lastPongTime > 60000) {
        console.log('心跳超时，关闭连接');
        this.disconnect();
      }
    }, 10000); // 每10秒检查一次
  }

  /**
   * 停止心跳
   */
  stopHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
    if (this.heartbeatTimeoutCheck) {
      clearInterval(this.heartbeatTimeoutCheck);
      this.heartbeatTimeoutCheck = null;
    }
  }

  /**
   * 确认收到通知
   * @param {string} notificationId - 通知ID
   */
  acknowledgeNotification(notificationId) {
    if (this.socket && this.connected && notificationId) {
      try {
        this.socket.send(JSON.stringify({
          type: 'ack',
          notification_id: notificationId,
          timestamp: new Date().toISOString()
        }));

        console.log('已确认收到通知:', notificationId);
      } catch (error) {
        console.error('确认通知失败:', error);
      }
    }
  }

  /**
   * 添加消息回调
   * @param {Function} callback - 回调函数
   */
  onMessage(callback) {
    if (typeof callback === 'function') {
      this.onMessageCallbacks.push(callback);
    }
  }

  /**
   * 添加连接回调
   * @param {Function} callback - 回调函数
   */
  onConnect(callback) {
    if (typeof callback === 'function') {
      this.onConnectCallbacks.push(callback);
    }
  }

  /**
   * 添加断开连接回调
   * @param {Function} callback - 回调函数
   */
  onDisconnect(callback) {
    if (typeof callback === 'function') {
      this.onDisconnectCallbacks.push(callback);
    }
  }

  /**
   * 移除消息回调
   * @param {Function} callback - 回调函数
   */
  removeMessageCallback(callback) {
    this.onMessageCallbacks = this.onMessageCallbacks.filter(cb => cb !== callback);
  }

  /**
   * 移除连接回调
   * @param {Function} callback - 回调函数
   */
  removeConnectCallback(callback) {
    this.onConnectCallbacks = this.onConnectCallbacks.filter(cb => cb !== callback);
  }

  /**
   * 移除断开连接回调
   * @param {Function} callback - 回调函数
   */
  removeDisconnectCallback(callback) {
    this.onDisconnectCallbacks = this.onDisconnectCallbacks.filter(cb => cb !== callback);
  }

  /**
   * 添加设置更新回调
   * @param {Function} callback - 回调函数，接收设置对象作为参数
   */
  onSettingsUpdate(callback) {
    if (typeof callback === 'function') {
      this.onSettingsUpdateCallbacks.push(callback);
    }
  }

  /**
   * 移除设置更新回调
   * @param {Function} callback - 回调函数
   */
  removeSettingsUpdateCallback(callback) {
    this.onSettingsUpdateCallbacks = this.onSettingsUpdateCallbacks.filter(cb => cb !== callback);
  }

  /**
   * 添加工作流设置更新回调
   * @param {Function} callback - 回调函数，接收工作流类型和设置对象作为参数
   */
  onWorkflowSettingsUpdate(callback) {
    if (typeof callback === 'function') {
      this.onWorkflowSettingsUpdateCallbacks.push(callback);
    }
  }

  /**
   * 移除工作流设置更新回调
   * @param {Function} callback - 回调函数
   */
  removeWorkflowSettingsUpdateCallback(callback) {
    this.onWorkflowSettingsUpdateCallbacks = this.onWorkflowSettingsUpdateCallbacks.filter(cb => cb !== callback);
  }

  /**
   * 是否已连接
   * @returns {boolean} - 是否已连接
   */
  isConnected() {
    return this.connected;
  }

  /**
   * 检查是否应该重连（基于用户设置）
   */
  shouldReconnectBasedOnSettings() {
    try {
      // 从本地存储获取设置
      const settingsJson = localStorage.getItem('notificationSettings');
      if (settingsJson) {
        const settings = JSON.parse(settingsJson);

        // 检查频率设置
        const normalFrequency = String(settings.normal_frequency || '').trim().toLowerCase();
        const highPriorityFrequency = String(settings.high_priority_frequency || '').trim().toLowerCase();

        const shouldReconnect = normalFrequency === 'realtime' || highPriorityFrequency === 'realtime';

        console.log('WebSocket重连检查 - 根据设置决定是否重连:', {
          normalFrequency,
          highPriorityFrequency,
          shouldReconnect
        });

        return shouldReconnect;
      }
    } catch (error) {
      console.error('检查WebSocket重连设置失败:', error);
    }

    // 默认不重连，避免频繁重连
    return false;
  }

  /**
   * 设置网络状态监听
   */
  setupNetworkListeners(userId, projectId) {
    // 移除之前的监听器
    if (this.onlineHandler) {
      window.removeEventListener('online', this.onlineHandler);
    }
    if (this.offlineHandler) {
      window.removeEventListener('offline', this.offlineHandler);
    }

    // 网络恢复时尝试重连
    this.onlineHandler = () => {
      console.log('网络已恢复，检查WebSocket连接状态');
      if (!this.connected && this.shouldReconnectBasedOnSettings()) {
        console.log('网络恢复后尝试重连WebSocket');
        // 重置重连次数
        this.reconnectAttempts = 0;
        setTimeout(() => {
          this.connect(userId, projectId).catch(error => {
            console.error('网络恢复后重连失败:', error);
          });
        }, 1000);
      }
    };

    // 网络断开时记录状态
    this.offlineHandler = () => {
      console.log('网络已断开，WebSocket连接将受影响');
      if (this.connected) {
        this.connected = false;
      }
    };

    window.addEventListener('online', this.onlineHandler);
    window.addEventListener('offline', this.offlineHandler);
  }
}

// 创建单例实例
const notificationWebSocket = new NotificationWebSocket();

export default notificationWebSocket;
