import apiService, {
  api,
  getUserRole,
  isSuperAdmin,
  isServiceProviderAdmin,
  isTenantAdmin,
  isProjectAdmin,
  getProjectId,
  getTenantId,
  getServiceProviderId,
  setProjectId,
  setTenantId,
  setServiceProviderId,

  // 认证服务
  auth,

  // 管理员服务（最顶层超级管理员）
  admin,

  // 服务商服务
  serviceProvider,

  // 租户服务
  tenant,

  // 项目服务
  project
} from './api/index';

// 导出所有API服务
export {
  api,
  getUserRole,
  isSuperAdmin,
  isServiceProviderAdmin,
  isTenantAdmin,
  isProjectAdmin,
  getProjectId,
  getTenantId,
  getServiceProviderId,
  setProjectId,
  setTenantId,
  setServiceProviderId,

  // 认证服务
  auth,

  // 管理员服务（最顶层超级管理员）
  admin,

  // 服务商服务
  serviceProvider,

  // 租户服务
  tenant,

  // 项目服务
  project
};

// 导出默认API对象
export default apiService;
