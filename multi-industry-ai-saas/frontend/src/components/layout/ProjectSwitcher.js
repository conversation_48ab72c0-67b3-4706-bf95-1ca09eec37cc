import React, { useState, useEffect } from 'react';
import { Modal, Input, List, Avatar, Button, Empty, Spin } from 'antd';
import { SearchOutlined, ShopOutlined } from '@ant-design/icons';
import { initDateTimeUtils } from '../../utils/dateTimeUtils';
import './ProjectSwitcher.css';

/**
 * 项目切换组件
 * 参考图片中的样式，实现项目切换功能
 */
const ProjectSwitcher = ({ visible, onClose, onSelect }) => {
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState('');
  const [projects, setProjects] = useState([]);
  const [filteredProjects, setFilteredProjects] = useState([]);

  // 获取项目列表
  useEffect(() => {
    if (visible) {
      fetchProjects();
    }
  }, [visible]);

  // 根据搜索文本过滤项目
  useEffect(() => {
    if (searchText) {
      const filtered = projects.filter(project =>
        project.name.toLowerCase().includes(searchText.toLowerCase())
      );
      setFilteredProjects(filtered);
    } else {
      setFilteredProjects(projects);
    }
  }, [searchText, projects]);

  // 获取项目数据
  const fetchProjects = async () => {
    try {
      setLoading(true);

      // 实际项目中应该从API获取项目数据
      // 这里使用模拟数据
      const mockProjects = [
        {
          id: 1,
          name: '旗舰店',
          type: 'retail',
          logo: null,
          created_at: '2023-06-01T10:00:00Z'
        },
        {
          id: 2,
          name: '中心店',
          type: 'retail',
          logo: null,
          created_at: '2023-06-02T10:00:00Z'
        },
        {
          id: 3,
          name: '西区店',
          type: 'retail',
          logo: null,
          created_at: '2023-06-03T10:00:00Z'
        },
        {
          id: 4,
          name: '东区店',
          type: 'retail',
          logo: null,
          created_at: '2023-06-04T10:00:00Z'
        }
      ];

      // 延迟一下，模拟网络请求
      setTimeout(() => {
        setProjects(mockProjects);
        setFilteredProjects(mockProjects);
        setLoading(false);
      }, 500);
    } catch (error) {
      console.error('获取项目列表失败:', error);
      setLoading(false);
    }
  };

  // 选择项目
  const handleSelectProject = async (project) => {
    // 保存项目ID到localStorage
    localStorage.setItem('project_id', project.id);
    localStorage.setItem('project_name', project.name);

    // 初始化日期时间工具
    try {
      await initDateTimeUtils();
      console.log('项目切换后日期时间工具初始化成功');
    } catch (error) {
      console.error('项目切换后日期时间工具初始化失败:', error);
    }

    if (onSelect) {
      onSelect(project);
    }
    onClose();
  };

  return (
    <Modal
      title="切换店铺"
      open={visible}
      onCancel={onClose}
      footer={null}
      width={400}
      className="project-switcher-modal"
    >
      <div className="project-switcher-search">
        <Input
          placeholder="搜索店铺名称"
          prefix={<SearchOutlined />}
          value={searchText}
          onChange={(e) => setSearchText(e.target.value)}
          allowClear
        />
      </div>

      <div className="project-switcher-content">
        {loading ? (
          <div className="project-switcher-loading">
            <Spin />
          </div>
        ) : filteredProjects.length > 0 ? (
          <List
            dataSource={filteredProjects}
            renderItem={(project) => (
              <List.Item
                key={project.id}
                className="project-switcher-item"
                onClick={() => handleSelectProject(project)}
              >
                <div className="project-switcher-item-content">
                  <Avatar
                    icon={<ShopOutlined />}
                    style={{ backgroundColor: '#f56a00' }}
                    className="project-switcher-avatar"
                  />
                  <div className="project-switcher-name">{project.name}</div>
                </div>
                <Button
                  type="primary"
                  onClick={(e) => {
                    e.stopPropagation();
                    handleSelectProject(project);
                  }}
                >
                  当前店铺后台
                </Button>
              </List.Item>
            )}
          />
        ) : (
          <Empty description="没有找到匹配的店铺" />
        )}
      </div>
    </Modal>
  );
};

export default ProjectSwitcher;
