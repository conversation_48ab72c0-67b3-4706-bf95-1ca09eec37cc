"""
项目初始化 API
"""
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from uuid import UUID

from db.database import get_db
from core.auth import get_current_user
from api.deps import get_current_project

router = APIRouter()

@router.post("/init", status_code=status.HTTP_200_OK)
def initialize_project(
    project_id: UUID = Depends(get_current_project),
    db: Session = Depends(get_db)
):
    """初始化项目数据"""
    try:
        # 延迟导入，避免循环依赖
        from db.init_data import init_project
        data = init_project(project_id)
        return {
            "message": "项目初始化成功",
            "data": {
                "roles_count": len(data.get("roles", [])),
                "stores_count": len(data.get("stores", [])),
                "warehouses_count": len(data.get("warehouses", [])),
                "configs_count": len(data.get("configs", []))
            }
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"项目初始化失败: {str(e)}"
        )
