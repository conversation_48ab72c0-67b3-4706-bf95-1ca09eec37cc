import api from '../httpClient';
import { getProjectId } from '../httpClient';

const routineTask = {
  // 获取任务列表
  getList: async (params) => {
    const projectId = getProjectId();
    return api.get(`/project/${projectId}/tasks`, { params });
  },

  // 管理员获取所有任务列表
  getAdminList: async (params) => {
    const projectId = getProjectId();
    return api.get(`/project/${projectId}/tasks/admin`, { params });
  },

  // 获取任务详情
  getDetail: async (taskId) => {
    const projectId = getProjectId();
    return api.get(`/project/${projectId}/tasks/${taskId}`);
  },

  // 创建任务
  create: async (data) => {
    const projectId = getProjectId();
    return api.post(`/project/${projectId}/tasks`, data);
  },

  // 更新任务
  update: async (taskId, data) => {
    const projectId = getProjectId();
    return api.put(`/project/${projectId}/tasks/${taskId}`, data);
  },

  // 更新任务状态
  updateStatus: async (taskId, data) => {
    const projectId = getProjectId();
    return api.patch(`/project/${projectId}/tasks/${taskId}/status`, data);
  },

  // 删除任务
  delete: async (taskId) => {
    const projectId = getProjectId();
    return api.delete(`/project/${projectId}/tasks/${taskId}`);
  },

  // 获取任务评论
  getComments: async (taskId, params) => {
    const projectId = getProjectId();
    return api.get(`/project/${projectId}/tasks/${taskId}/comments`, { params });
  },

  // 添加任务评论
  addComment: async (taskId, data) => {
    const projectId = getProjectId();
    return api.post(`/project/${projectId}/tasks/${taskId}/comments`, data);
  },

  // 删除任务评论
  deleteComment: async (taskId, commentId) => {
    const projectId = getProjectId();
    return api.delete(`/project/${projectId}/tasks/${taskId}/comments/${commentId}`);
  },

  // 获取任务统计数据
  getStats: async (params) => {
    const projectId = getProjectId();
    return api.get(`/project/${projectId}/tasks/stats`, { params });
  }
};

export default routineTask;