/* 主题相关样式 */

:root {
  --primary-color: #1890ff;
  --primary-color-hover: #40a9ff;
  --primary-color-active: #096dd9;
  --border-radius: 4px;
  --font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;

  /* 默认为浅色主题 */
  --bg-color: #f0f2f5;
  --component-bg: #ffffff;
  --text-color: rgba(0, 0, 0, 0.85);
  --text-color-secondary: rgba(0, 0, 0, 0.45);
  --border-color: #d9d9d9;
  --disabled-color: rgba(0, 0, 0, 0.25);
  --disabled-bg: #f5f5f5;
  --heading-color: rgba(0, 0, 0, 0.85);
}

/* 浅色主题 */
.theme-light {
  --bg-color: #f0f2f5;
  --component-bg: #ffffff;
  --text-color: rgba(0, 0, 0, 0.85);
  --text-color-secondary: rgba(0, 0, 0, 0.45);
  --border-color: #d9d9d9;
  --disabled-color: rgba(0, 0, 0, 0.25);
  --disabled-bg: #f5f5f5;
  --heading-color: rgba(0, 0, 0, 0.85);
}

/* 深色主题 */
.theme-dark {
  --bg-color: #18191c;
  --component-bg: #23272f;
  --text-color: #fff;
  --text-color-secondary: rgba(255,255,255,0.65);
  --border-color: #43454a;
  --disabled-color: rgba(255,255,255,0.35);
  --disabled-bg: #23272f;
  --heading-color: #fff;
  --input-bg: #23272f;
  --input-border: #43454a;
  --alert-bg: #23272f;
  --alert-text: #fff;
}

.theme-dark .ant-card,
.theme-dark .ant-card-body,
.theme-dark .ant-card-bordered,
.theme-dark .ant-card-head,
.theme-dark .ant-card-head-wrapper,
.theme-dark .ant-card-extra {
  background: var(--component-bg) !important;
  color: var(--text-color) !important;
  border-color: var(--border-color) !important;
  box-shadow: none !important;
}
.theme-dark .ant-card-body > * {
  background: transparent !important;
}

.theme-dark .ant-form,
.theme-dark .ant-tabs-content,
.theme-dark .ant-alert,
.theme-dark .ant-modal-content {
  background: transparent !important;
  color: var(--text-color) !important;
}

.theme-dark .ant-input,
.theme-dark .ant-select-selector,
.theme-dark .ant-input-password,
.theme-dark .ant-picker,
.theme-dark .ant-input-affix-wrapper {
  background: var(--input-bg) !important;
  color: var(--text-color) !important;
  border-color: var(--input-border) !important;
}

.theme-dark .ant-input::placeholder,
.theme-dark .ant-select-selection-placeholder {
  color: var(--text-color-secondary) !important;
}

.theme-dark .ant-btn {
  color: var(--text-color) !important;
  border-color: var(--input-border) !important;
  background: transparent !important;
}
.theme-dark .ant-btn-primary {
  background: var(--primary-color) !important;
  border-color: var(--primary-color) !important;
  color: #fff !important;
}

.theme-dark .ant-alert,
.theme-dark .ant-alert-content {
  background: var(--component-bg) !important;
  color: var(--text-color) !important;
  border-color: var(--border-color) !important;
}

.theme-dark .ant-tabs-tab {
  color: var(--text-color-secondary) !important;
}
.theme-dark .ant-tabs-tab-active {
  color: var(--primary-color) !important;
}
.theme-dark .ant-tabs-ink-bar {
  background: var(--primary-color) !important;
}

.theme-dark .ant-form-item-label > label {
  color: var(--text-color) !important;
}

.theme-dark .ant-table {
  background-color: var(--component-bg);
  color: var(--text-color);
}
.theme-dark .ant-table-thead > tr > th {
  background-color: #23272f;
  color: var(--text-color);
  border-bottom: 1px solid var(--border-color);
}
.theme-dark .ant-table-tbody > tr > td {
  border-bottom: 1px solid var(--border-color);
}
.theme-dark .ant-table-tbody > tr:hover > td {
  background-color: #18191c;
}

.theme-dark .ant-select-dropdown {
  background: var(--component-bg) !important;
  color: var(--text-color) !important;
}
.theme-dark .ant-select-item {
  color: var(--text-color) !important;
}
.theme-dark .ant-select-item-option-active:not(.ant-select-item-option-disabled) {
  background: #23272f !important;
}
.theme-dark .ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
  background: #18191c !important;
}

.theme-dark .ant-modal-header {
  background: var(--component-bg) !important;
  border-bottom: 1px solid var(--border-color) !important;
}
.theme-dark .ant-modal-title {
  color: var(--text-color) !important;
}
.theme-dark .ant-modal-footer {
  background: var(--component-bg) !important;
  border-top: 1px solid var(--border-color) !important;
}

.theme-dark .ant-menu,
.theme-dark .ant-menu-item,
.theme-dark .ant-menu-submenu-title {
  background: var(--component-bg) !important;
  color: var(--text-color) !important;
}
.theme-dark .ant-menu-item-selected {
  background: #18191c !important;
  color: var(--primary-color) !important;
}
.theme-dark .ant-layout-sider {
  background: #18191c !important;
}
.theme-dark .ant-layout-header,
.theme-dark .ant-layout-header * {
  color: #fff !important;
}
.theme-dark .ant-layout-footer {
  background: #18191c !important;
}

/* 应用主题变量 */
body {
  background-color: var(--bg-color);
  color: var(--text-color);
  font-family: var(--font-family);
  transition: background-color 0.3s, color 0.3s;
}

/* 应用到系统设置页面 */
.system-settings {
  background-color: var(--component-bg);
  color: var(--text-color);
  border-radius: var(--border-radius);
}

.settings-form {
  background-color: var(--component-bg);
  color: var(--text-color);
  border-radius: var(--border-radius);
}

.ant-card {
  background-color: var(--component-bg);
  color: var(--text-color);
  border-radius: var(--border-radius);
}

.ant-tabs-content {
  background-color: var(--component-bg);
  color: var(--text-color);
}

.ant-form-item-label > label {
  color: var(--text-color);
}

.ant-input,
.ant-select-selector,
.ant-radio-button-wrapper,
.ant-slider,
.ant-switch {
  border-radius: var(--border-radius) !important;
}

/* 紧凑模式 */
.compact-mode .ant-form-item {
  margin-bottom: 12px;
}

.compact-mode .ant-table-small {
  font-size: 12px;
}

.compact-mode .ant-btn {
  padding: 0 8px;
  height: 28px;
}

.compact-mode .ant-input {
  padding: 2px 8px;
}

/* 覆盖 Ant Design 组件样式 */
.ant-btn-primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.ant-btn-primary:hover {
  background-color: var(--primary-color-hover);
  border-color: var(--primary-color-hover);
}

.ant-btn-primary:active {
  background-color: var(--primary-color-active);
  border-color: var(--primary-color-active);
}

.ant-checkbox-checked .ant-checkbox-inner {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.ant-radio-checked .ant-radio-inner {
  border-color: var(--primary-color);
}

.ant-radio-inner::after {
  background-color: var(--primary-color);
}

.ant-switch-checked {
  background-color: var(--primary-color);
}

.ant-slider-track {
  background-color: var(--primary-color);
}

.ant-slider-handle {
  border-color: var(--primary-color);
}

.ant-pagination-item-active {
  border-color: var(--primary-color);
}

.ant-pagination-item-active a {
  color: var(--primary-color);
}

.ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
  background-color: rgba(var(--primary-color), 0.1);
}

/* 圆角样式 */
.ant-btn,
.ant-input,
.ant-select-selector,
.ant-card,
.ant-modal-content,
.ant-table-container,
.ant-pagination-item {
  border-radius: var(--border-radius);
}

.theme-dark .ant-layout-header,
.theme-dark .ant-layout-header * {
  color: #fff !important;
}

.theme-dark .ant-menu,
.theme-dark .ant-menu-item,
.theme-dark .ant-menu-submenu-title,
.theme-dark .ant-dropdown-menu,
.theme-dark .ant-dropdown-menu-item,
.theme-dark .ant-dropdown-menu-title-content,
.theme-dark .ant-avatar,
.theme-dark .anticon,
.theme-dark .ant-badge {
  color: #fff !important;
  background: transparent !important;
}

.theme-dark .ant-dropdown-menu {
  background: #23272f !important;
  color: #fff !important;
}

.theme-dark .ant-layout-header,
.theme-dark .project-header {
  background: var(--component-bg) !important;
}

.theme-dark .ant-layout-content,
.theme-dark .project-content {
  background: var(--component-bg) !important;
}

.theme-light .ant-layout-header,
.theme-light .project-header {
  background: #fff !important;
}

.theme-dark .system-settings {
  background: var(--component-bg) !important;
  color: var(--text-color) !important;
}
