/* 增强版随机抽奖样式 */
.enhanced-lucky-draw {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', <PERSON><PERSON>, 'Helvetica Neue', <PERSON><PERSON>, sans-serif;
}

/* 主题样式 */
.theme-classic {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.theme-neon {
  background: linear-gradient(135deg, #0c0c0c 0%, #1a1a1a 100%);
  color: #fff;
}

.theme-gradient {
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
}

.theme-dark {
  background: linear-gradient(135deg, #434343 0%, #000000 100%);
  color: #fff;
}

/* 游戏头部 */
.game-header {
  margin-bottom: 24px;
}

.header-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.theme-neon .header-card {
  background: rgba(0, 0, 0, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.theme-dark .header-card {
  background: rgba(0, 0, 0, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* 抽奖按钮 */
.draw-button {
  background: linear-gradient(135deg, #ff6b6b, #ee5a6f);
  border: none;
  border-radius: 50px;
  font-weight: bold;
  font-size: 16px;
  height: 48px;
  padding: 0 32px;
  box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.draw-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 107, 107, 0.6);
}

.draw-button:active {
  transform: translateY(0);
}

.draw-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.5s;
}

.draw-button:hover::before {
  left: 100%;
}

/* 统计卡片 */
.stats-section {
  margin-bottom: 24px;
}

.stat-card {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  border: none;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.theme-neon .stat-card {
  background: rgba(0, 0, 0, 0.6);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.theme-dark .stat-card {
  background: rgba(0, 0, 0, 0.6);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* 抽奖区域 */
.draw-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  border: none;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  position: relative;
}

.theme-neon .draw-card {
  background: rgba(0, 0, 0, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.theme-dark .draw-card {
  background: rgba(0, 0, 0, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.draw-area {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px;
  min-height: 400px;
}

/* 翻卡效果 */
.cards-container {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  max-width: 600px;
}

.draw-card {
  width: 140px;
  height: 180px;
  position: relative;
  cursor: pointer;
  transform-style: preserve-3d;
  transition: transform 0.6s;
  animation: cardAppear 0.5s ease-out forwards;
}

.draw-card.selected {
  transform: rotateY(180deg);
}

.card-front,
.card-back {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.card-front {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
}

.card-back {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: #fff;
  transform: rotateY(180deg);
}

.card-back img {
  width: 60px;
  height: 60px;
  border-radius: 8px;
  margin-bottom: 8px;
}

@keyframes cardAppear {
  0% {
    opacity: 0;
    transform: scale(0.8) translateY(20px);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* 轮盘效果 */
.roulette-container {
  position: relative;
  width: 300px;
  height: 300px;
}

.roulette-wheel {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  position: relative;
  box-shadow: 0 0 30px rgba(0, 0, 0, 0.3);
  overflow: hidden;
}

.roulette-sector {
  position: absolute;
  width: 50%;
  height: 50%;
  top: 50%;
  left: 50%;
  transform-origin: 0 0;
  clip-path: polygon(0 0, 100% 0, 0 100%);
  display: flex;
  align-items: center;
  justify-content: center;
}

.sector-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transform: rotate(-22.5deg);
  color: #fff;
  font-size: 12px;
  font-weight: bold;
}

.sector-content img {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  margin-bottom: 4px;
}

.roulette-pointer {
  position: absolute;
  top: -12px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 10;
  color: #ff4444;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

/* 简单抽奖容器 */
.simple-draw-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

.draw-box {
  padding: 60px;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.9);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  text-align: center;
  transition: all 0.3s ease;
}

.draw-box:hover {
  transform: scale(1.05);
}

/* 动画效果 */
.shake-animation {
  animation: shake 0.5s infinite;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px) rotate(-2deg); }
  75% { transform: translateX(5px) rotate(2deg); }
}

.flash-animation {
  animation: flash 0.3s infinite;
}

@keyframes flash {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.3; }
}

/* 奖品展示 */
.prizes-display {
  padding: 20px;
  background: rgba(0, 0, 0, 0.05);
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.theme-neon .prizes-display {
  background: rgba(255, 255, 255, 0.05);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.prize-card {
  text-align: center;
  border: none;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.prize-card:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.prize-name {
  display: block;
  margin-top: 8px;
  font-size: 12px;
  color: #666;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.theme-neon .prize-card {
  background: rgba(0, 0, 0, 0.6);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.theme-neon .prize-name {
  color: #fff;
}

/* 设置面板 */
.settings-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  border: none;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.theme-neon .settings-card {
  background: rgba(0, 0, 0, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.theme-dark .settings-card {
  background: rgba(0, 0, 0, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* 结果模态框 */
.result-modal .ant-modal-content {
  border-radius: 20px;
  overflow: hidden;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
}

.result-content {
  text-align: center;
  padding: 20px;
}

.win-result .celebration-icon {
  animation: celebration-bounce 1s ease-in-out infinite alternate;
}

@keyframes celebration-bounce {
  0% {
    transform: scale(1);
  }
  100% {
    transform: scale(1.1);
  }
}

.prize-info-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 20px;
  margin: 16px 0;
  color: #333;
}

.result-actions {
  margin-top: 24px;
}

/* 庆祝效果 */
.celebration-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 9999;
}

.celebration-particle {
  position: absolute;
  width: 8px;
  height: 8px;
  background: #FFD700;
  border-radius: 50%;
  animation: celebration-fall 3s linear forwards;
}

@keyframes celebration-fall {
  0% {
    opacity: 1;
    transform: translateY(-100vh) rotate(0deg);
  }
  100% {
    opacity: 0;
    transform: translateY(100vh) rotate(360deg);
  }
}

/* 加载动画 */
.draw-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .enhanced-lucky-draw {
    padding: 12px;
  }
  
  .draw-area {
    padding: 20px;
    min-height: 300px;
  }
  
  .cards-container {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }
  
  .draw-card {
    width: 100px;
    height: 130px;
  }
  
  .roulette-container {
    width: 200px;
    height: 200px;
  }
  
  .prizes-display {
    padding: 12px;
  }
  
  .prize-card {
    margin-bottom: 8px;
  }
}

@media (max-width: 480px) {
  .cards-container {
    grid-template-columns: 1fr;
  }
  
  .draw-card {
    width: 120px;
    height: 150px;
  }
  
  .draw-button {
    height: 40px;
    font-size: 14px;
    padding: 0 24px;
  }
} 