import React, { useState, useEffect, useRef } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Typography,
  Table,
  Button,
  Spin,
  Empty,
  Tabs,
  List,
  Tag,
  Space,
  Select,
  Tooltip,
  Progress,
  Divider,
  message
} from 'antd';
import {
  ShopOutlined,
  TeamOutlined,
  DollarOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  SyncOutlined,
  LineChartOutlined,
  PieChartOutlined,
  RobotOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  LikeOutlined,
  DislikeOutlined,
  FileTextOutlined,
  ScheduleOutlined,
  BookOutlined,
  ExceptionOutlined,
  BarChartOutlined,
  CreditCardOutlined
} from '@ant-design/icons';
// 导入图表组件
import { Line, Pie, Column } from '@ant-design/plots';
import dayjs from 'dayjs';
import apiService from '../../../services/api';
import { useNavigate } from 'react-router-dom';

// 导入自定义组件
import DashboardFilter from '../../../components/dashboard/DashboardFilter';
import StatisticCard from '../../../components/dashboard/StatisticCard';
import ExportButton from '../../../components/dashboard/ExportButton';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;

// 使用 @ant-design/plots 提供的配置来实现自定义图形效果
// 不再需要直接注册 shape

/**
 * 门店仪表盘组件
 * 展示门店销售、任务执行等数据
 */
const StoreDashboard = () => {
  // 引用
  const dashboardRef = useRef(null);
  const navigate = useNavigate();

  // 状态定义
  const [loading, setLoading] = useState(true);
  const [dashboardData, setDashboardData] = useState(null);
  const [aiLoading, setAiLoading] = useState(false);
  const [feedbackLoading, setFeedbackLoading] = useState(false);
  const [stores, setStores] = useState([]);
  const [selectedStore, setSelectedStore] = useState(null);
  const [dateRange, setDateRange] = useState([dayjs().subtract(29, 'days'), dayjs()]);

  // 初始化加载
  useEffect(() => {
    fetchStores();
    fetchDashboardData();
  }, [selectedStore, dateRange]);

  // 获取门店列表
  const fetchStores = async () => {
    try {
      const response = await apiService.project.store.getList();
      setStores(response.items || []);
      if (response.items && response.items.length > 0 && !selectedStore) {
        setSelectedStore(response.items[0].id);
      }
    } catch (error) {
      console.error('获取门店列表失败:', error);
    }
  };

  // 获取仪表盘数据
  const fetchDashboardData = async () => {
    if (!selectedStore) return;

    setLoading(true);
    try {
      const params = {
        start_date: dateRange[0].format('YYYY-MM-DD'),
        end_date: dateRange[1].format('YYYY-MM-DD'),
        store_id: selectedStore
      };

      // 使用API获取真实数据
      const response = await apiService.project.dashboard.getStoreDashboard(params);
      console.log('获取到的仪表盘数据:', response); // 添加日志以便调试
      setDashboardData(response);

      // 如果没有AI推荐，则自动获取
      if (!response.ai_recommendations || response.ai_recommendations.length === 0) {
        refreshAIRecommendations();
      }
    } catch (error) {
      console.error('获取门店仪表盘数据失败:', error);
      message.error('获取门店仪表盘数据失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  // 刷新AI推荐
  const refreshAIRecommendations = async () => {
    setAiLoading(true);
    try {
      const response = await apiService.project.dashboard.getAIRecommendations('store');
      if (response && response.recommendations) {
        setDashboardData(prev => ({
          ...prev,
          ai_recommendations: response.recommendations
        }));
        message.success('AI推荐已更新');
      }
    } catch (error) {
      console.error('获取AI推荐失败:', error);
      message.error('获取AI推荐失败，请重试');
    } finally {
      setAiLoading(false);
    }
  };

  // 处理AI推荐反馈
  const handleRecommendationFeedback = async (recommendationId, isHelpful) => {
    setFeedbackLoading(true);
    try {
      await apiService.project.dashboard.submitAIFeedback({
        recommendation_id: recommendationId,
        is_helpful: isHelpful,
        feedback_type: 'store'
      });

      // 更新本地状态，标记反馈已提交
      setDashboardData(prev => ({
        ...prev,
        ai_recommendations: prev.ai_recommendations.map(item =>
          item.id === recommendationId
            ? { ...item, feedback_submitted: true, is_helpful: isHelpful }
            : item
        )
      }));

      message.success(isHelpful ? '感谢您的反馈！' : '感谢您的反馈，我们会继续改进');
    } catch (error) {
      console.error('提交AI推荐反馈失败:', error);
      message.error('提交反馈失败，请重试');
    } finally {
      setFeedbackLoading(false);
    }
  };

  // 处理门店选择变化
  const handleStoreChange = (value) => {
    setSelectedStore(value);
  };

  // 处理日期范围变化
  const handleDateRangeChange = (dates) => {
    setDateRange(dates);
  };

  // 刷新数据
  const handleRefresh = () => {
    fetchDashboardData();
  };

  // 导出数据
  const getExportData = () => {
    if (!dashboardData) return [];
    return dashboardData.sales_trend || [];
  };

  // 渲染销售趋势图表
  const renderSalesTrendChart = () => {
    if (!dashboardData || !dashboardData.sales_trend || dashboardData.sales_trend.length === 0) {
      return <Empty description="暂无销售趋势数据" />;
    }

    const config = {
      data: dashboardData.sales_trend,
      xField: 'date',
      yField: 'value',
      seriesField: 'type',
      smooth: true,
      animation: {
        appear: {
          animation: 'path-in',
          duration: 1000
        }
      },
      xAxis: {
        title: {
          text: '日期'
        }
      },
      yAxis: {
        title: {
          text: '金额 (元)'
        }
      },
      tooltip: {
        formatter: (datum) => {
          return { name: datum.type, value: datum.type === '销售额' ? `¥${datum.value.toFixed(2)}` : datum.value };
        }
      },
      // 使用 @ant-design/plots 提供的配置来实现自定义点
      point: {
        shape: 'circle',
        size: 4,
        style: (x) => {
          return {
            fill: x.color,
            stroke: x.color,
            lineWidth: 1
          };
        }
      }
    };

    return <Line {...config} height={300} />;
  };

  // 渲染任务执行情况图表
  const renderTaskCompletionChart = () => {
    if (!dashboardData || !dashboardData.task_completion || dashboardData.task_completion.length === 0) {
      return <Empty description="暂无任务执行数据" />;
    }

    const config = {
      data: dashboardData.task_completion,
      xField: 'date',
      yField: 'value',
      seriesField: 'type',
      isStack: true,
      animation: {
        appear: {
          animation: 'path-in',
          duration: 1000
        }
      },
      xAxis: {
        title: {
          text: '日期'
        }
      },
      yAxis: {
        title: {
          text: '任务数量'
        }
      },
      tooltip: {
        formatter: (datum) => {
          return { name: datum.type, value: datum.value };
        }
      },
      // 使用 @ant-design/plots 提供的配置来实现自定义柱状图
      columnStyle: {
        radius: [4, 4, 0, 0],
      }
    };

    return <Column {...config} height={300} />;
  };

  // 渲染商品销售排行表格
  const renderProductRankingTable = () => {
    if (!dashboardData || !dashboardData.product_ranking || dashboardData.product_ranking.length === 0) {
      return <Empty description="暂无商品销售排行数据" />;
    }

    const columns = [
      {
        title: '商品名称',
        dataIndex: 'name',
        key: 'name',
      },
      {
        title: '销售额',
        dataIndex: 'sales',
        key: 'sales',
        render: (text) => `¥${text.toFixed(2)}`,
        sorter: (a, b) => a.sales - b.sales,
      },
      {
        title: '订单数',
        dataIndex: 'orders',
        key: 'orders',
        sorter: (a, b) => a.orders - b.orders,
      },
      {
        title: '利润',
        dataIndex: 'profit',
        key: 'profit',
        render: (text) => `¥${text.toFixed(2)}`,
        sorter: (a, b) => a.profit - b.profit,
      }
    ];

    return (
      <Table
        columns={columns}
        dataSource={dashboardData.product_ranking}
        rowKey="id"
        pagination={false}
      />
    );
  };

  // 渲染AI推荐
  const renderAIRecommendations = () => {
    if (!dashboardData || !dashboardData.ai_recommendations || dashboardData.ai_recommendations.length === 0) {
      return <Empty description="暂无AI推荐" />;
    }

    return (
      <List
        dataSource={dashboardData.ai_recommendations}
        renderItem={(item) => (
          <List.Item>
            <div style={{ width: '100%' }}>
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'flex-start',
                marginBottom: 12
              }}>
                <div>
                  <Text strong style={{ fontSize: 16, marginRight: 8 }}>{item.title}</Text>
                  <Tag color={
                    item.type === '业务优化' ? 'blue' :
                    item.type === '销售增长' ? 'green' :
                    item.type === '任务管理' ? 'orange' :
                    item.type === '服务优化' ? 'purple' :
                    item.type === '风险预警' ? 'red' : 'default'
                  }>
                    {item.type}
                  </Tag>
                  {item.priority && (
                    <Tag color={
                      item.priority === '高' ? 'red' :
                      item.priority === '中' ? 'orange' : 'green'
                    }>
                      {item.priority}优先级
                    </Tag>
                  )}
                </div>
                <div>
                  {item.feedback_submitted ? (
                    <span>
                      {item.is_helpful ?
                        <Tag icon={<LikeOutlined />} color="success">已标记为有帮助</Tag> :
                        <Tag icon={<DislikeOutlined />} color="default">已提交反馈</Tag>
                      }
                    </span>
                  ) : (
                    <Space>
                      <Button
                        type="primary"
                        size="small"
                        icon={<LikeOutlined />}
                        onClick={() => handleRecommendationFeedback(item.id, true)}
                        loading={feedbackLoading}
                      >
                        有帮助
                      </Button>
                      <Button
                        size="small"
                        icon={<DislikeOutlined />}
                        onClick={() => handleRecommendationFeedback(item.id, false)}
                        loading={feedbackLoading}
                      >
                        没有帮助
                      </Button>
                    </Space>
                  )}
                </div>
              </div>

              <Paragraph style={{ margin: '12px 0' }}>{item.content}</Paragraph>

              {item.action_items && item.action_items.length > 0 && (
                <div style={{
                  background: '#f5f5f5',
                  padding: '12px 16px',
                  borderRadius: '4px',
                  marginBottom: 12
                }}>
                  <Text strong>建议行动:</Text>
                  <ul style={{ marginTop: 8, marginBottom: 0, paddingLeft: 20 }}>
                    {item.action_items.map((action, index) => (
                      <li key={index}>{action}</li>
                    ))}
                  </ul>
                </div>
              )}

              {item.detailed_analysis && (
                <div style={{ marginTop: 12 }}>
                  <Divider style={{ margin: '12px 0' }} />
                  <Text strong>详细分析:</Text>
                  <Paragraph style={{ margin: '8px 0 0' }}>{item.detailed_analysis}</Paragraph>
                </div>
              )}
            </div>
          </List.Item>
        )}
        style={{ background: '#fff' }}
      />
    );
  };

  return (
    <div className="store-dashboard" ref={dashboardRef}>
      <Card>
        <div className="dashboard-header" style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Title level={4} style={{ margin: 0 }}>门店仪表盘</Title>
          <Space>
            <Select
              placeholder="选择门店"
              style={{ width: 200 }}
              value={selectedStore}
              onChange={handleStoreChange}
            >
              {stores.map(store => (
                <Option key={store.id} value={store.id}>{store.name}</Option>
              ))}
            </Select>
            <Button
              type="primary"
              icon={<SyncOutlined />}
              onClick={handleRefresh}
              loading={loading}
            >
              刷新
            </Button>
            <ExportButton
              data={getExportData()}
              filename="门店仪表盘数据"
            />
          </Space>
        </div>

        <Spin spinning={loading}>
          {dashboardData ? (
            <div className="dashboard-content">
              {/* 统计卡片 */}
              <Row gutter={[16, 16]}>
                <Col xs={24} sm={12} md={6}>
                  <Card>
                    <Statistic
                      title="今日销售额"
                      value={typeof dashboardData.today_sales === 'object' ? dashboardData.today_sales.value : dashboardData.today_sales}
                      precision={2}
                      prefix={<DollarOutlined />}
                      suffix="元"
                      valueStyle={{ color: '#3f8600' }}
                    />
                    <div style={{ marginTop: 8 }}>
                      {(dashboardData.today_sales?.change || dashboardData.sales_change) >= 0 ? (
                        <Text type="success">
                          <ArrowUpOutlined /> {Math.abs(dashboardData.today_sales?.change || dashboardData.sales_change)}% 较昨日
                        </Text>
                      ) : (
                        <Text type="danger">
                          <ArrowDownOutlined /> {Math.abs(dashboardData.today_sales?.change || dashboardData.sales_change)}% 较昨日
                        </Text>
                      )}
                    </div>
                  </Card>
                </Col>
                <Col xs={24} sm={12} md={6}>
                  <Card>
                    <Statistic
                      title="今日订单数"
                      value={typeof dashboardData.today_orders === 'object' ? dashboardData.today_orders.value : dashboardData.today_orders}
                      prefix={<ShopOutlined />}
                      suffix="单"
                      valueStyle={{ color: '#1890ff' }}
                    />
                    <div style={{ marginTop: 8 }}>
                      {(dashboardData.today_orders?.change || dashboardData.orders_change) >= 0 ? (
                        <Text type="success">
                          <ArrowUpOutlined /> {Math.abs(dashboardData.today_orders?.change || dashboardData.orders_change)}% 较昨日
                        </Text>
                      ) : (
                        <Text type="danger">
                          <ArrowDownOutlined /> {Math.abs(dashboardData.today_orders?.change || dashboardData.orders_change)}% 较昨日
                        </Text>
                      )}
                    </div>
                  </Card>
                </Col>
                <Col xs={24} sm={12} md={6}>
                  <Card>
                    <Statistic
                      title="任务完成率"
                      value={typeof dashboardData.today_customers === 'object' ? dashboardData.today_customers.value : dashboardData.task_completion_rate}
                      precision={2}
                      prefix={<CheckCircleOutlined />}
                      suffix="%"
                      valueStyle={{ color: '#722ed1' }}
                    />
                    <div style={{ marginTop: 8 }}>
                      {(dashboardData.today_customers?.change || dashboardData.task_rate_change) >= 0 ? (
                        <Text type="success">
                          <ArrowUpOutlined /> {Math.abs(dashboardData.today_customers?.change || dashboardData.task_rate_change)}% 较上周
                        </Text>
                      ) : (
                        <Text type="danger">
                          <ArrowDownOutlined /> {Math.abs(dashboardData.today_customers?.change || dashboardData.task_rate_change)}% 较上周
                        </Text>
                      )}
                    </div>
                  </Card>
                </Col>
                <Col xs={24} sm={12} md={6}>
                  <Card>
                    <Statistic
                      title="逾期任务"
                      value={dashboardData.overdue_tasks}
                      prefix={<ClockCircleOutlined />}
                      suffix="个"
                      valueStyle={{ color: '#cf1322' }}
                    />
                    <div style={{ marginTop: 8 }}>
                      <Button size="small" type="link">
                        查看详情
                      </Button>
                    </div>
                  </Card>
                </Col>
              </Row>

              {/* 快捷入口 */}
              <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
                <Col xs={24} sm={8} md={6} lg={4}>
                  <Card
                    hoverable
                    onClick={() => window.location.href = '/project/store/sales-report'}
                    style={{ textAlign: 'center', cursor: 'pointer' }}
                  >
                    <FileTextOutlined style={{ fontSize: 24, color: '#1890ff', marginBottom: 8 }} />
                    <div>销售上报</div>
                  </Card>
                </Col>
                <Col xs={24} sm={8} md={6} lg={4}>
                  <Card
                    hoverable
                    onClick={() => window.location.href = '/project/store/routine-tasks'}
                    style={{ textAlign: 'center', cursor: 'pointer' }}
                  >
                    <ScheduleOutlined style={{ fontSize: 24, color: '#52c41a', marginBottom: 8 }} />
                    <div>例行任务</div>
                  </Card>
                </Col>
                <Col xs={24} sm={8} md={6} lg={4}>
                  <Card
                    hoverable
                    onClick={() => window.location.href = '/project/ai/knowledge'}
                    style={{ textAlign: 'center', cursor: 'pointer' }}
                  >
                    <BookOutlined style={{ fontSize: 24, color: '#722ed1', marginBottom: 8 }} />
                    <div>知识库</div>
                  </Card>
                </Col>
                <Col xs={24} sm={8} md={6} lg={4}>
                  <Card
                    hoverable
                    onClick={() => window.location.href = '/project/store/loss-report'}
                    style={{ textAlign: 'center', cursor: 'pointer' }}
                  >
                    <ExceptionOutlined style={{ fontSize: 24, color: '#fa8c16', marginBottom: 8 }} />
                    <div>损耗上报</div>
                  </Card>
                </Col>
                <Col xs={24} sm={8} md={6} lg={4}>
                  <Card
                    hoverable
                    onClick={() => window.location.href = '/project/store/sales-monthly-report'}
                    style={{ textAlign: 'center', cursor: 'pointer' }}
                  >
                    <BarChartOutlined style={{ fontSize: 24, color: '#eb2f96', marginBottom: 8 }} />
                    <div>销售月报</div>
                  </Card>
                </Col>
                <Col xs={24} sm={8} md={6} lg={4}>
                  <Card
                    hoverable
                    onClick={() => navigate('/project/ai/role-assistants?role=store_manager')}
                    style={{ textAlign: 'center', cursor: 'pointer' }}
                  >
                    <RobotOutlined style={{ fontSize: 24, color: '#f5222d', marginBottom: 8 }} />
                    <div>AI助手</div>
                  </Card>
                </Col>
              </Row>

              {/* 渠道销售和充值售卡数据 */}
              <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
                <Col span={12}>
                  <Card title={<span><ShopOutlined /> 渠道销售</span>}>
                    {dashboardData.channel_sales && dashboardData.channel_sales.length > 0 ? (
                      <Pie
                        data={dashboardData.channel_sales.map(item => ({
                          ...item,
                          percentage: Math.round((item.value / dashboardData.channel_sales.reduce((sum, i) => sum + i.value, 0)) * 100)
                        }))}
                        angleField="value"
                        colorField="name"
                        radius={0.8}
                        label={false}
                        tooltip={{
                          formatter: (datum) => {
                            if (!datum || !datum.name) return { name: '未知', value: '0' };
                            return {
                              name: datum.name,
                              value: `¥${(datum.value || 0).toFixed(2)} (${datum.percentage || 0}%)`
                            };
                          }
                        }}
                        legend={{
                          position: 'bottom'
                        }}
                        interactions={[{ type: 'element-active' }]}
                        height={300}
                      />
                    ) : (
                      <Empty description="暂无渠道销售数据" />
                    )}
                  </Card>
                </Col>
                <Col span={12}>
                  <Card title={<span><CreditCardOutlined /> 充值售卡</span>}>
                    {dashboardData.recharge_data ? (
                      <div>
                        <Row gutter={[16, 16]}>
                          <Col span={12}>
                            <Statistic
                              title="会员充值金额"
                              value={dashboardData.recharge_data.recharge_amount}
                              precision={2}
                              prefix="¥"
                              suffix={`(${dashboardData.recharge_data.recharge_count}笔)`}
                            />
                          </Col>
                          <Col span={12}>
                            <Statistic
                              title="储值卡销售金额"
                              value={dashboardData.recharge_data.card_sales_amount}
                              precision={2}
                              prefix="¥"
                              suffix={`(${dashboardData.recharge_data.card_sales_count}张)`}
                            />
                          </Col>
                        </Row>
                        <div style={{ marginTop: 16 }}>
                          <Progress
                            percent={Math.round(
                              (dashboardData.recharge_data.recharge_amount /
                                (dashboardData.recharge_data.recharge_amount + dashboardData.recharge_data.card_sales_amount || 1)) *
                                100
                            )}
                            strokeColor="#1890ff"
                            trailColor="#f5222d"
                            format={() => '充值 vs 售卡'}
                          />
                        </div>
                      </div>
                    ) : (
                      <Empty description="暂无充值售卡数据" />
                    )}
                  </Card>
                </Col>
              </Row>

              {/* 图表区域 */}
              <Tabs defaultActiveKey="1" style={{ marginTop: 16 }}>
                <TabPane
                  tab={<span><LineChartOutlined /> 销售趋势</span>}
                  key="1"
                >
                  <Card>
                    {renderSalesTrendChart()}
                  </Card>
                </TabPane>
                <TabPane
                  tab={<span><PieChartOutlined /> 任务执行情况</span>}
                  key="2"
                >
                  <Card>
                    {renderTaskCompletionChart()}
                  </Card>
                </TabPane>
                <TabPane
                  tab={<span><ShopOutlined /> 商品销售排行</span>}
                  key="3"
                >
                  <Card>
                    {renderProductRankingTable()}
                  </Card>
                </TabPane>
              </Tabs>

              {/* AI推荐 */}
              <div style={{ marginTop: 16 }}>
                <Card
                  title={
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <span><RobotOutlined /> AI推荐</span>
                      <Button
                        icon={<SyncOutlined />}
                        onClick={refreshAIRecommendations}
                        loading={aiLoading}
                        size="small"
                      >
                        刷新AI推荐
                      </Button>
                    </div>
                  }
                >
                  {renderAIRecommendations()}
                </Card>
              </div>
            </div>
          ) : (
            <Empty description="暂无数据" />
          )}
        </Spin>
      </Card>
    </div>
  );
};

export default StoreDashboard;
