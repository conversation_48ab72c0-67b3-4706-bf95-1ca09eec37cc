import React from 'react';
import { Tabs, Card, Typography, Divider, Form, Select, Input, Slider } from 'antd';
import AdventureGameConfig from './AdventureGameConfig';

const { TabPane } = Tabs;
const { Title, Paragraph } = Typography;
const { Option } = Select;

/**
 * 根据游戏类型显示不同的设置界面
 */
const GameTypeSettings = ({ gameType, form, initialValues }) => {
  // 随机抽奖设置
  const renderLuckyDrawSettings = () => (
    <div className="lucky-draw-settings">
      <Card title="随机抽奖设置" bordered={false}>
        <Paragraph>
          配置随机抽奖游戏的奖品、概率和显示效果。
        </Paragraph>
        <Divider />
        <Card title="抽奖设置" size="small" style={{ marginBottom: '16px' }}>
          <Form.Item
            name={['settings', 'animation_duration']}
            label="动画时长(秒)"
            initialValue={initialValues?.settings?.animation_duration || 4}
          >
            <Tabs defaultActiveKey="1">
              <TabPane tab="基础设置" key="1">
                <div style={{ padding: '16px 0' }}>
                  <Form.Item
                    name={['settings', 'animation_duration']}
                    label="动画时长(秒)"
                    initialValue={initialValues?.settings?.animation_duration || 4}
                  >
                    <input type="range" min="2" max="6" step="0.5"
                      value={form.getFieldValue(['settings', 'animation_duration']) || 4}
                      onChange={(e) => {
                        form.setFieldsValue({
                          settings: {
                            ...form.getFieldValue('settings'),
                            animation_duration: parseFloat(e.target.value)
                          }
                        });
                      }}
                      style={{ width: '100%' }}
                    />
                    <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                      <span>快(2秒)</span>
                      <span>中(4秒)</span>
                      <span>慢(6秒)</span>
                    </div>
                  </Form.Item>
                </div>
              </TabPane>
            </Tabs>
          </Form.Item>
        </Card>
      </Card>
    </div>
  );

  // 刮刮卡设置
  const renderScratchCardSettings = () => (
    <div className="scratch-card-settings">
      <Card title="刮刮卡设置" bordered={false}>
        <Paragraph>
          配置刮刮卡游戏的奖品、概率和显示效果。
        </Paragraph>
        <Divider />
        <Card title="刮刮卡设置" size="small" style={{ marginBottom: '16px' }}>
          <Form.Item
            name={['settings', 'scratch_card_style']}
            label="刮刮卡样式"
            initialValue={initialValues?.settings?.scratch_card_style || 'classic'}
          >
            <Tabs defaultActiveKey="1">
              <TabPane tab="基础设置" key="1">
                <div style={{ padding: '16px 0' }}>
                  <Form.Item
                    name={['settings', 'scratch_area_size']}
                    label="刮奖区域大小"
                    initialValue={initialValues?.settings?.scratch_area_size || 'medium'}
                  >
                    <select
                      value={form.getFieldValue(['settings', 'scratch_area_size']) || 'medium'}
                      onChange={(e) => {
                        form.setFieldsValue({
                          settings: {
                            ...form.getFieldValue('settings'),
                            scratch_area_size: e.target.value
                          }
                        });
                      }}
                      style={{ width: '100%' }}
                    >
                      <option value="small">小</option>
                      <option value="medium">中</option>
                      <option value="large">大</option>
                    </select>
                  </Form.Item>
                </div>
              </TabPane>
            </Tabs>
          </Form.Item>
        </Card>
      </Card>
    </div>
  );

  // 大转盘设置
  const renderWheelOfFortuneSettings = () => (
    <div className="wheel-of-fortune-settings">
      <Card title="大转盘设置" bordered={false}>
        <Paragraph>
          配置大转盘游戏的奖品、概率和显示效果。
        </Paragraph>
        <Divider />
        <Card title="转盘设置" size="small" style={{ marginBottom: '16px' }}>
          <Tabs defaultActiveKey="1">
            <TabPane tab="基础设置" key="1">
              <div style={{ padding: '16px 0' }}>
                <Form.Item
                  name={['settings', 'wheel_size']}
                  label="转盘大小"
                  initialValue={initialValues?.settings?.wheel_size || 'medium'}
                >
                  <select
                    value={form.getFieldValue(['settings', 'wheel_size']) || 'medium'}
                    onChange={(e) => {
                      form.setFieldsValue({
                        settings: {
                          ...form.getFieldValue('settings'),
                          wheel_size: e.target.value
                        }
                      });
                    }}
                    style={{ width: '100%' }}
                  >
                    <option value="small">小</option>
                    <option value="medium">中</option>
                    <option value="large">大</option>
                  </select>
                </Form.Item>

                <Form.Item
                  name={['settings', 'rotation_time']}
                  label="旋转时间(秒)"
                  initialValue={initialValues?.settings?.rotation_time || 4}
                >
                  <input type="range" min="2" max="6" step="0.5"
                    value={form.getFieldValue(['settings', 'rotation_time']) || 4}
                    onChange={(e) => {
                      form.setFieldsValue({
                        settings: {
                          ...form.getFieldValue('settings'),
                          rotation_time: parseFloat(e.target.value)
                        }
                      });
                    }}
                    style={{ width: '100%' }}
                  />
                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                    <span>快(2秒)</span>
                    <span>中(4秒)</span>
                    <span>慢(6秒)</span>
                  </div>
                </Form.Item>
              </div>
            </TabPane>
          </Tabs>
        </Card>
      </Card>
    </div>
  );

  // 闯关游戏设置
  const renderAdventureGameSettings = () => (
    <div className="adventure-game-settings">
      <Card title="闯关游戏配置" bordered={false}>
        <Paragraph>
          配置闯关游戏的关卡、问题和奖励。每个关卡可以包含多个问题，玩家需要回答正确一定数量的问题才能通过关卡。
        </Paragraph>
        <Divider />
        <AdventureGameConfig form={form} initialValues={initialValues} />
      </Card>
    </div>
  );

  // 根据游戏类型渲染不同的设置
  const renderSettingsByGameType = () => {
    switch (gameType) {
      case 'lucky_draw':
        return renderLuckyDrawSettings();
      case 'scratch_card':
        return renderScratchCardSettings();
      case 'wheel_of_fortune':
        return renderWheelOfFortuneSettings();
      case 'adventure_game':
        return renderAdventureGameSettings();
      default:
        return <p>请选择游戏类型</p>;
    }
  };

  return (
    <div className="game-type-settings">
      <Tabs defaultActiveKey="1">
        <TabPane tab="游戏设置" key="1">
          <Tabs defaultActiveKey={gameType} onChange={(key) => console.log('游戏类型切换:', key)}>
            <TabPane tab="随机抽奖" key="lucky_draw">
              {renderLuckyDrawSettings()}
            </TabPane>
            <TabPane tab="刮刮卡" key="scratch_card">
              {renderScratchCardSettings()}
            </TabPane>
            <TabPane tab="大转盘" key="wheel_of_fortune">
              {renderWheelOfFortuneSettings()}
            </TabPane>
            <TabPane tab="闯关游戏" key="adventure_game">
              {renderAdventureGameSettings()}
            </TabPane>
          </Tabs>
        </TabPane>
      </Tabs>
    </div>
  );
};

export default GameTypeSettings;
