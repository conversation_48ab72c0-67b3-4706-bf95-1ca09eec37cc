#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
AI 设置 Schema
"""

from typing import Optional, Dict, Any
from pydantic import BaseModel, Field

class AISettingsBase(BaseModel):
    """AI 设置基础 Schema"""
    knowledge_base_enabled: bool = Field(True, description="是否启用知识库")
    tools_enabled: bool = Field(True, description="是否启用工具")
    max_history_messages: int = Field(10, description="最大历史消息数")
    default_system_prompt: Optional[str] = Field(None, description="默认系统提示词")
    cache_enabled: bool = Field(True, description="是否启用缓存")
    cache_ttl: int = Field(3600, description="缓存有效期（秒）")
    log_level: str = Field("info", description="日志级别")

class AISettingsUpdate(AISettingsBase):
    """更新 AI 设置 Schema"""
    knowledge_base_enabled: Optional[bool] = Field(None, description="是否启用知识库")
    tools_enabled: Optional[bool] = Field(None, description="是否启用工具")
    max_history_messages: Optional[int] = Field(None, description="最大历史消息数")
    default_system_prompt: Optional[str] = Field(None, description="默认系统提示词")
    cache_enabled: Optional[bool] = Field(None, description="是否启用缓存")
    cache_ttl: Optional[int] = Field(None, description="缓存有效期（秒）")
    log_level: Optional[str] = Field(None, description="日志级别")

class AISettingsResponse(BaseModel):
    """AI 设置响应 Schema"""
    success: bool = True
    message: Optional[str] = None
    data: Optional[Dict[str, Any]] = None
