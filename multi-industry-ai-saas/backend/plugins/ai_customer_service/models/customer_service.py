#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
AI 智能体客服数据模型
"""

import uuid
import enum
from datetime import datetime
from typing import Dict, Any, Optional, List
from sqlalchemy import Column, String, Text, DateTime, Boolean, Integer, Float, JSON, ForeignKey, Index
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.ext.declarative import declarative_base

from db.database import Base

class SessionStatus(str, enum.Enum):
    """会话状态"""
    ACTIVE = "active"
    WAITING = "waiting"
    RESOLVED = "resolved"
    ESCALATED = "escalated"
    CLOSED = "closed"

class MessageType(str, enum.Enum):
    """消息类型"""
    TEXT = "text"
    IMAGE = "image"
    FILE = "file"
    VOICE = "voice"
    VIDEO = "video"
    SYSTEM = "system"

class TicketStatus(str, enum.Enum):
    """工单状态"""
    OPEN = "open"
    IN_PROGRESS = "in_progress"
    RESOLVED = "resolved"
    CLOSED = "closed"

class TicketPriority(str, enum.Enum):
    """工单优先级"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    URGENT = "urgent"

class PlatformType(str, enum.Enum):
    """平台类型"""
    WECHAT = "wechat"
    WORK_WECHAT = "work_wechat"
    DINGTALK = "dingtalk"
    FEISHU = "feishu"
    QQ = "qq"
    TELEGRAM = "telegram"
    DISCORD = "discord"

class CustomerServiceSession(Base):
    """客服会话模型"""
    __tablename__ = "ai_cs_sessions"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    project_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    
    # 用户信息
    user_id = Column(UUID(as_uuid=True), nullable=True, index=True)  # 系统用户ID（如果已绑定）
    platform_user_id = Column(String(255), nullable=False, index=True)  # 平台用户ID
    platform_type = Column(String(50), nullable=False)  # 平台类型
    
    # 会话信息
    session_id = Column(String(255), nullable=False, unique=True, index=True)
    status = Column(String(50), nullable=False, default=SessionStatus.ACTIVE)
    title = Column(String(500), nullable=True)  # 会话标题
    
    # AI 助手信息
    assistant_id = Column(UUID(as_uuid=True), nullable=True)  # 关联的AI助手
    thread_id = Column(UUID(as_uuid=True), nullable=True)  # 对话线程ID
    
    # 统计信息
    message_count = Column(Integer, default=0)
    ai_response_count = Column(Integer, default=0)
    human_takeover_count = Column(Integer, default=0)
    satisfaction_score = Column(Float, nullable=True)  # 满意度评分
    
    # 时间信息
    started_at = Column(DateTime, default=datetime.utcnow)
    last_activity_at = Column(DateTime, default=datetime.utcnow)
    ended_at = Column(DateTime, nullable=True)
    
    # 元数据
    meta_data = Column(JSON, default=dict)  # 额外的会话元数据
    
    # 关系
    messages = relationship("CustomerServiceMessage", back_populates="session", cascade="all, delete-orphan")
    tickets = relationship("CustomerServiceTicket", back_populates="session")
    
    # 索引
    __table_args__ = (
        Index('idx_cs_session_platform', 'platform_type', 'platform_user_id'),
        Index('idx_cs_session_status', 'status', 'last_activity_at'),
        Index('idx_cs_session_tenant', 'tenant_id', 'project_id'),
    )

class CustomerServiceMessage(Base):
    """客服消息模型"""
    __tablename__ = "ai_cs_messages"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    session_id = Column(UUID(as_uuid=True), ForeignKey("ai_cs_sessions.id"), nullable=False, index=True)
    
    # 消息内容
    message_type = Column(String(50), nullable=False, default=MessageType.TEXT)
    content = Column(Text, nullable=False)
    attachments = Column(JSON, default=list)  # 附件信息
    
    # 发送者信息
    is_from_user = Column(Boolean, nullable=False)  # True: 用户消息, False: AI回复
    sender_name = Column(String(255), nullable=True)
    
    # AI 处理信息
    ai_model_used = Column(String(100), nullable=True)  # 使用的AI模型
    processing_time = Column(Float, nullable=True)  # 处理时间（秒）
    confidence_score = Column(Float, nullable=True)  # 置信度分数
    
    # 知识库信息
    knowledge_sources = Column(JSON, default=list)  # 引用的知识库来源
    rag_used = Column(Boolean, default=False)  # 是否使用了RAG
    
    # 时间信息
    created_at = Column(DateTime, default=datetime.utcnow, index=True)
    
    # 元数据
    meta_data = Column(JSON, default=dict)
    
    # 关系
    session = relationship("CustomerServiceSession", back_populates="messages")

class CustomerServiceTicket(Base):
    """客服工单模型"""
    __tablename__ = "ai_cs_tickets"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    project_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    session_id = Column(UUID(as_uuid=True), ForeignKey("ai_cs_sessions.id"), nullable=True)
    
    # 工单信息
    ticket_number = Column(String(100), nullable=False, unique=True, index=True)
    title = Column(String(500), nullable=False)
    description = Column(Text, nullable=False)
    category = Column(String(100), nullable=True)  # 工单分类
    
    # 状态和优先级
    status = Column(String(50), nullable=False, default=TicketStatus.OPEN)
    priority = Column(String(50), nullable=False, default=TicketPriority.MEDIUM)
    
    # 处理人员
    assigned_to = Column(UUID(as_uuid=True), nullable=True, index=True)  # 分配给的用户
    created_by = Column(UUID(as_uuid=True), nullable=True)  # 创建者
    
    # 客户信息
    customer_platform_id = Column(String(255), nullable=False)
    customer_name = Column(String(255), nullable=True)
    customer_contact = Column(String(255), nullable=True)
    
    # 时间信息
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    resolved_at = Column(DateTime, nullable=True)
    
    # 元数据
    meta_data = Column(JSON, default=dict)
    
    # 关系
    session = relationship("CustomerServiceSession", back_populates="tickets")

class CustomerServiceAnalytics(Base):
    """客服分析数据模型"""
    __tablename__ = "ai_cs_analytics"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    project_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    
    # 分析维度
    date = Column(DateTime, nullable=False, index=True)
    platform_type = Column(String(50), nullable=True)
    
    # 会话统计
    total_sessions = Column(Integer, default=0)
    active_sessions = Column(Integer, default=0)
    resolved_sessions = Column(Integer, default=0)
    escalated_sessions = Column(Integer, default=0)
    
    # 消息统计
    total_messages = Column(Integer, default=0)
    user_messages = Column(Integer, default=0)
    ai_responses = Column(Integer, default=0)
    
    # 性能指标
    avg_response_time = Column(Float, default=0.0)  # 平均响应时间
    avg_resolution_time = Column(Float, default=0.0)  # 平均解决时间
    ai_accuracy_rate = Column(Float, default=0.0)  # AI准确率
    customer_satisfaction = Column(Float, default=0.0)  # 客户满意度
    
    # 业务指标
    conversion_rate = Column(Float, default=0.0)  # 转化率
    upsell_rate = Column(Float, default=0.0)  # 追加销售率
    
    # 时间信息
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 索引
    __table_args__ = (
        Index('idx_cs_analytics_date', 'date', 'platform_type'),
        Index('idx_cs_analytics_tenant', 'tenant_id', 'project_id'),
    )

class CustomerServiceConfig(Base):
    """客服配置模型"""
    __tablename__ = "ai_cs_configs"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    project_id = Column(UUID(as_uuid=True), nullable=False, index=True)

    # 配置信息
    config_key = Column(String(255), nullable=False)
    config_value = Column(JSON, nullable=False)
    description = Column(Text, nullable=True)

    # 时间信息
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # 索引
    __table_args__ = (
        Index('idx_cs_config_key', 'tenant_id', 'project_id', 'config_key'),
    )

class PlatformAccount(Base):
    """平台账号模型"""
    __tablename__ = "ai_cs_platform_accounts"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    project_id = Column(UUID(as_uuid=True), nullable=False, index=True)

    # 平台信息
    platform_type = Column(String(50), nullable=False)
    platform_name = Column(String(255), nullable=False)

    # 账号信息
    account_id = Column(String(255), nullable=False)
    account_name = Column(String(255), nullable=True)
    credentials = Column(JSON, nullable=False)  # 加密存储的凭据

    # 配置信息
    config = Column(JSON, default=dict)
    is_active = Column(Boolean, default=True)

    # 时间信息
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_sync_at = Column(DateTime, nullable=True)

    # 索引
    __table_args__ = (
        Index('idx_platform_account', 'tenant_id', 'project_id', 'platform_type'),
    )

class MarketingAction(Base):
    """营销动作模型"""
    __tablename__ = "ai_cs_marketing_actions"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    project_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    session_id = Column(UUID(as_uuid=True), nullable=True, index=True)

    # 营销信息
    action_type = Column(String(100), nullable=False)  # coupon, recommendation, promotion
    action_data = Column(JSON, nullable=False)

    # 目标用户
    target_user_id = Column(UUID(as_uuid=True), nullable=True)
    target_platform_id = Column(String(255), nullable=False)

    # 执行状态
    status = Column(String(50), default="pending")  # pending, sent, delivered, clicked, converted
    sent_at = Column(DateTime, nullable=True)
    delivered_at = Column(DateTime, nullable=True)
    clicked_at = Column(DateTime, nullable=True)
    converted_at = Column(DateTime, nullable=True)

    # 时间信息
    created_at = Column(DateTime, default=datetime.utcnow)

    # 索引
    __table_args__ = (
        Index('idx_marketing_action', 'tenant_id', 'project_id', 'action_type'),
    )

class ConversationLog(Base):
    """对话日志模型"""
    __tablename__ = "ai_cs_conversation_logs"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    session_id = Column(UUID(as_uuid=True), nullable=False, index=True)

    # 对话信息
    conversation_data = Column(JSON, nullable=False)  # 完整的对话数据
    ai_analysis = Column(JSON, default=dict)  # AI分析结果

    # 质量评估
    quality_score = Column(Float, nullable=True)
    sentiment_score = Column(Float, nullable=True)
    intent_classification = Column(String(255), nullable=True)

    # 时间信息
    created_at = Column(DateTime, default=datetime.utcnow)

class UserProfile(Base):
    """用户画像模型"""
    __tablename__ = "ai_cs_user_profiles"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    project_id = Column(UUID(as_uuid=True), nullable=False, index=True)

    # 用户标识
    user_id = Column(UUID(as_uuid=True), nullable=True, index=True)
    platform_user_id = Column(String(255), nullable=False, index=True)
    platform_type = Column(String(50), nullable=False)

    # 用户画像
    profile_data = Column(JSON, default=dict)  # 用户画像数据
    preferences = Column(JSON, default=dict)  # 用户偏好
    behavior_patterns = Column(JSON, default=dict)  # 行为模式

    # 统计信息
    total_sessions = Column(Integer, default=0)
    total_messages = Column(Integer, default=0)
    avg_satisfaction = Column(Float, default=0.0)
    last_interaction = Column(DateTime, nullable=True)

    # 时间信息
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # 索引
    __table_args__ = (
        Index('idx_user_profile', 'tenant_id', 'project_id', 'platform_user_id'),
    )

class KnowledgeCache(Base):
    """知识库缓存模型"""
    __tablename__ = "ai_cs_knowledge_cache"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), nullable=False, index=True)
    project_id = Column(UUID(as_uuid=True), nullable=False, index=True)

    # 查询信息
    query_hash = Column(String(255), nullable=False, unique=True, index=True)
    query_text = Column(Text, nullable=False)

    # 缓存结果
    cached_result = Column(JSON, nullable=False)
    knowledge_sources = Column(JSON, default=list)

    # 统计信息
    hit_count = Column(Integer, default=0)
    last_hit_at = Column(DateTime, nullable=True)

    # 时间信息
    created_at = Column(DateTime, default=datetime.utcnow)
    expires_at = Column(DateTime, nullable=True)  # 缓存过期时间
