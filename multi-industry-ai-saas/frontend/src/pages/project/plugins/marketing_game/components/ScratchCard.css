/* 刮刮卡页面样式 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.scratch-card-container {
  min-height: 100vh;
  padding: 30px;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  animation: fadeIn 0.8s ease-in-out;
}

.scratch-card-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.85);
  z-index: 0;
}

.scratch-card-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
}

.loading-icon {
  font-size: 48px;
  margin-bottom: 20px;
  color: #2196f3;
}

.loading-text {
  font-size: 18px;
  color: #333;
  margin-top: 15px;
}

.scratch-card-header {
  text-align: center;
  margin-bottom: 40px;
  position: relative;
  z-index: 1;
  width: 100%;
  max-width: 1000px;
}

.game-logo {
  width: 100px;
  height: 100px;
  margin-bottom: 20px;
  border-radius: 50%;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  object-fit: cover;
  background-color: white;
  padding: 5px;
}

.game-title {
  color: #2196f3;
  margin-bottom: 15px;
  text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.2);
  font-size: 32px;
}

.game-description {
  max-width: 800px;
  margin: 0 auto;
  color: #333;
  font-size: 16px;
  line-height: 1.6;
}

.scratch-card-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  z-index: 1;
  width: 100%;
  max-width: 1000px;
  background-color: rgba(255, 255, 255, 0.7);
  border-radius: 15px;
  padding: 30px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.settings-bar {
  display: flex;
  justify-content: flex-end;
  width: 100%;
  margin-bottom: 20px;
}

.settings-item {
  display: flex;
  align-items: center;
  margin-left: 20px;
  color: #666;
}

.settings-label {
  margin-right: 8px;
  font-size: 14px;
}

/* 刮刮卡样式 */
.scratch-card {
  position: relative;
  width: 350px;
  height: 250px;
  margin-bottom: 40px;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  background-color: white;
  cursor: pointer;
  animation: pulse 2s infinite ease-in-out;
}

.prize-content {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  text-align: center;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  z-index: 1;
}

.prize-image {
  width: 80px;
  height: 80px;
  margin-bottom: 15px;
  object-fit: contain;
}

.prize-title {
  margin-bottom: 10px !important;
  color: #2196f3;
  font-weight: bold;
}

.prize-description {
  margin-bottom: 0 !important;
  color: #666;
  font-size: 16px;
}

.scratch-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
  cursor: pointer;
}

/* 按钮样式 */
.scratch-card-actions {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 30px;
  width: 100%;
}

.scratch-button {
  font-size: 20px;
  height: 60px;
  width: 300px;
  margin-bottom: 30px;
  background-color: #2196f3;
  border-color: #2196f3;
  border-radius: 30px;
  box-shadow: 0 5px 15px rgba(33, 150, 243, 0.3);
  transition: all 0.3s ease;
}

.scratch-button:hover, .scratch-button:focus {
  background-color: #1976d2;
  border-color: #1976d2;
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(33, 150, 243, 0.4);
}

.action-buttons {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
  justify-content: center;
}

.action-button {
  min-width: 120px;
  height: 40px;
  border-radius: 20px;
  transition: all 0.3s ease;
}

.action-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
}

/* 奖品结果样式 */
.prize-result {
  display: flex;
  align-items: center;
  margin: 20px 0;
  padding: 20px;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.prize-result-image {
  width: 100px;
  height: 100px;
  margin-right: 30px;
  object-fit: contain;
}

.prize-result-info {
  flex: 1;
}

.prize-result-title {
  color: #2196f3;
  margin-bottom: 10px;
}

.prize-result-description {
  color: #666;
  font-size: 16px;
}

/* 底部信息 */
.scratch-card-footer {
  margin-top: 40px;
  text-align: center;
  color: #666;
  font-size: 14px;
  width: 100%;
  max-width: 1000px;
  position: relative;
  z-index: 1;
}

.footer-links {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 10px;
}

.footer-link {
  color: #2196f3;
  text-decoration: none;
}

.footer-link:hover {
  text-decoration: underline;
}

/* 响应式样式 */
@media (max-width: 1200px) {
  .scratch-card-content {
    max-width: 800px;
  }
}

@media (max-width: 992px) {
  .scratch-card-content {
    max-width: 700px;
    padding: 25px;
  }
  
  .scratch-card {
    width: 300px;
    height: 200px;
  }
  
  .game-title {
    font-size: 28px;
  }
}

@media (max-width: 768px) {
  .scratch-card-container {
    padding: 20px;
  }
  
  .scratch-card-content {
    max-width: 100%;
    padding: 20px;
  }
  
  .scratch-card {
    width: 280px;
    height: 180px;
  }
  
  .scratch-button {
    width: 250px;
    height: 50px;
    font-size: 18px;
  }
  
  .game-title {
    font-size: 24px;
  }
  
  .game-description {
    font-size: 14px;
  }
  
  .prize-image {
    width: 60px;
    height: 60px;
  }
  
  .prize-title {
    font-size: 18px;
  }
}

@media (max-width: 576px) {
  .scratch-card-container {
    padding: 15px;
  }
  
  .scratch-card-content {
    padding: 15px;
  }
  
  .scratch-card {
    width: 250px;
    height: 160px;
    margin-bottom: 30px;
  }
  
  .scratch-button {
    width: 200px;
    height: 45px;
    font-size: 16px;
    margin-bottom: 20px;
  }
  
  .action-buttons {
    flex-direction: column;
    gap: 10px;
  }
  
  .action-button {
    width: 100%;
  }
  
  .game-logo {
    width: 80px;
    height: 80px;
  }
  
  .game-title {
    font-size: 20px;
  }
  
  .prize-result {
    flex-direction: column;
    text-align: center;
  }
  
  .prize-result-image {
    margin-right: 0;
    margin-bottom: 15px;
  }
  
  .settings-bar {
    flex-direction: column;
    align-items: flex-end;
  }
  
  .settings-item {
    margin-left: 0;
    margin-bottom: 10px;
  }
}
