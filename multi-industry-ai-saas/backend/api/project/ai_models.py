#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
项目级 AI 模型 API
"""

import logging
import uuid
from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, Query, Path, status
from sqlalchemy.ext.asyncio import AsyncSession

from db.database import get_db
from models.project import Project
from core.auth import get_current_user
from api.deps import get_current_project
from services.ai import AIModelService
from schemas.ai import (
    AIModelCreate,
    AIModelUpdate,
    AIModelResponse,
    AIModelListResponse,
)

logger = logging.getLogger(__name__)
router = APIRouter()

@router.get("/ai/models", response_model=AIModelListResponse)
async def list_models(
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db),
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    provider_id: Optional[uuid.UUID] = Query(None),
    model_type: Optional[str] = Query(None),
    model_status: Optional[str] = Query(None, alias="status"),
):
    """
    获取 AI 模型列表
    """
    try:
        models, total = await AIModelService.get_models(
            db=db,
            skip=skip,
            limit=limit,
            provider_id=provider_id,
            model_type=model_type,
            status=model_status,
        )

        return {
            "success": True,
            "message": "获取 AI 模型列表成功",
            "data": models,
            "total": total,
            "page": skip // limit + 1,
            "page_size": limit,
        }
    except Exception as e:
        logger.error(f"获取 AI 模型列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取 AI 模型列表失败: {str(e)}",
        )

@router.get("/ai/models/{model_id}", response_model=AIModelResponse)
async def get_model(
    model_id: uuid.UUID = Path(...),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db),
):
    """
    获取 AI 模型详情
    """
    try:
        model = await AIModelService.get_model(db=db, model_id=model_id)

        if not model:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"AI 模型 ID '{model_id}' 不存在",
            )

        return {
            "success": True,
            "message": "获取 AI 模型详情成功",
            "data": model,
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取 AI 模型详情失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取 AI 模型详情失败: {str(e)}",
        )

@router.post("/ai/models", response_model=AIModelResponse, status_code=status.HTTP_201_CREATED)
async def create_model(
    model_data: AIModelCreate,
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db),
):
    """
    创建 AI 模型

    支持项目用户创建自定义模型（兼容OpenAI接口格式）
    """
    try:
        # 项目用户可以创建自定义模型，但标记为非内置模型
        model_data_dict = model_data.dict()
        model_data_dict['is_builtin'] = False  # 用户创建的模型标记为非内置
        
        model = await AIModelService.create_model(db=db, model_data=model_data)

        return {
            "success": True,
            "message": "创建 AI 模型成功",
            "data": model,
        }
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建 AI 模型失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建 AI 模型失败: {str(e)}",
        )

@router.put("/ai/models/{model_id}", response_model=AIModelResponse)
async def update_model(
    model_id: uuid.UUID = Path(...),
    model_data: AIModelUpdate = None,
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db),
):
    """
    更新 AI 模型

    注意：只有系统管理员才能更新 AI 模型
    """
    try:
        # 检查权限
        if not get_current_user.is_system_admin:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="只有系统管理员才能更新 AI 模型",
            )

        model = await AIModelService.update_model(
            db=db,
            model_id=model_id,
            model_data=model_data,
        )

        if not model:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"AI 模型 ID '{model_id}' 不存在",
            )

        return {
            "success": True,
            "message": "更新 AI 模型成功",
            "data": model,
        }
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新 AI 模型失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新 AI 模型失败: {str(e)}",
        )

@router.delete("/ai/models/{model_id}", response_model=AIModelResponse)
async def delete_model(
    model_id: uuid.UUID = Path(...),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db),
):
    """
    删除 AI 模型

    注意：只有系统管理员才能删除 AI 模型
    """
    try:
        # 检查权限
        if not get_current_user.is_system_admin:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="只有系统管理员才能删除 AI 模型",
            )

        success = await AIModelService.delete_model(db=db, model_id=model_id)

        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"AI 模型 ID '{model_id}' 不存在",
            )

        return {
            "success": True,
            "message": "删除 AI 模型成功",
        }
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除 AI 模型失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除 AI 模型失败: {str(e)}",
        )
