from fastapi import APIRouter, Depends
from api.project.space_settings import router as space_settings_router
from api.project.operation_log import router as operation_log_router
from api.deps import get_current_project_id

router = APIRouter()

# 注册空间设置路由
router.include_router(
    space_settings_router,
    prefix="",
    tags=["project-space-settings"],
    dependencies=[Depends(get_current_project_id)]
)

# 注册操作日志路由
router.include_router(
    operation_log_router,
    prefix="",
    tags=["project-operation-log"],
    dependencies=[Depends(get_current_project_id)]
)
