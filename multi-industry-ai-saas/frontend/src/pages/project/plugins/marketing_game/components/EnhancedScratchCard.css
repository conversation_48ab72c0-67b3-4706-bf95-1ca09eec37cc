/* 增强版刮刮卡样式 */
.enhanced-scratch-card {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', <PERSON><PERSON>, sans-serif;
}

/* 主题样式 */
.theme-classic {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.theme-gold {
  background: linear-gradient(135deg, #ffd700 0%, #ff8c00 100%);
}

.theme-neon {
  background: linear-gradient(135deg, #0c0c0c 0%, #1a1a1a 100%);
  color: #fff;
}

.theme-rainbow {
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
}

/* 游戏头部 */
.game-header {
  margin-bottom: 24px;
}

.header-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.theme-neon .header-card {
  background: rgba(0, 0, 0, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: #fff;
}

.theme-gold .header-card {
  background: rgba(255, 215, 0, 0.2);
  border: 1px solid rgba(255, 215, 0, 0.3);
}

/* 刮奖按钮 */
.scratch-button {
  background: linear-gradient(135deg, #ff6b6b, #ee5a6f);
  border: none;
  border-radius: 50px;
  font-weight: bold;
  font-size: 16px;
  height: 48px;
  padding: 0 32px;
  box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.scratch-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 107, 107, 0.6);
}

.scratch-button:active {
  transform: translateY(0);
}

.scratch-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.5s;
}

.scratch-button:hover::before {
  left: 100%;
}

/* 统计卡片 */
.stats-section {
  margin-bottom: 24px;
}

.stat-card {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  border: none;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.theme-neon .stat-card {
  background: rgba(0, 0, 0, 0.6);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.theme-gold .stat-card {
  background: rgba(255, 215, 0, 0.3);
  border: 1px solid rgba(255, 215, 0, 0.4);
}

/* 刮奖卡片 */
.scratch-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  border: none;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  position: relative;
}

.theme-neon .scratch-card {
  background: rgba(0, 0, 0, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.theme-gold .scratch-card {
  background: rgba(255, 215, 0, 0.2);
  border: 1px solid rgba(255, 215, 0, 0.3);
}

/* 刮奖容器 */
.scratch-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px 20px;
}

.canvas-container {
  position: relative;
  margin-bottom: 20px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
}

.background-canvas {
  display: block;
  border-radius: 12px;
}

.overlay-canvas {
  position: absolute;
  top: 0;
  left: 0;
  border-radius: 12px;
  cursor: grab;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

.overlay-canvas:active {
  cursor: grabbing;
}

/* 刮奖进度 */
.scratch-progress {
  width: 100%;
  max-width: 400px;
  margin-bottom: 20px;
}

.scratch-progress .ant-progress-text {
  color: #666;
  font-weight: bold;
}

.theme-neon .scratch-progress .ant-progress-text {
  color: #fff;
}

/* 控制按钮 */
.scratch-controls {
  display: flex;
  justify-content: center;
  gap: 12px;
  margin-bottom: 20px;
}

.scratch-controls .ant-btn {
  border-radius: 20px;
  font-weight: bold;
  transition: all 0.3s ease;
}

.scratch-controls .ant-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* 奖品展示 */
.prizes-display {
  padding: 20px;
  background: rgba(0, 0, 0, 0.05);
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 0 0 20px 20px;
}

.theme-neon .prizes-display {
  background: rgba(255, 255, 255, 0.05);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.theme-gold .prizes-display {
  background: rgba(255, 215, 0, 0.1);
  border-top: 1px solid rgba(255, 215, 0, 0.2);
}

.prize-card {
  text-align: center;
  border: none;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.prize-card:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.prize-name {
  display: block;
  margin-top: 8px;
  font-size: 12px;
  color: #666;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.theme-neon .prize-card {
  background: rgba(0, 0, 0, 0.6);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.theme-neon .prize-name {
  color: #fff;
}

.theme-gold .prize-card {
  background: rgba(255, 215, 0, 0.3);
  border: 1px solid rgba(255, 215, 0, 0.4);
}

/* 设置面板 */
.settings-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  border: none;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.theme-neon .settings-card {
  background: rgba(0, 0, 0, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: #fff;
}

.theme-gold .settings-card {
  background: rgba(255, 215, 0, 0.2);
  border: 1px solid rgba(255, 215, 0, 0.3);
}

/* 结果模态框 */
.result-modal .ant-modal-content {
  border-radius: 20px;
  overflow: hidden;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
}

.result-content {
  text-align: center;
  padding: 20px;
}

.win-result .celebration-icon {
  animation: celebration-bounce 1s ease-in-out infinite alternate;
}

@keyframes celebration-bounce {
  0% {
    transform: scale(1);
  }
  100% {
    transform: scale(1.1);
  }
}

.prize-info-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 20px;
  margin: 16px 0;
  color: #333;
}

.result-actions {
  margin-top: 24px;
}

/* 刮奖动画效果 */
.scratch-animate {
  animation: scratch-shimmer 2s infinite;
}

@keyframes scratch-shimmer {
  0% {
    box-shadow: 0 0 5px rgba(255, 255, 255, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.8);
  }
  100% {
    box-shadow: 0 0 5px rgba(255, 255, 255, 0.5);
  }
}

/* 涂层样式效果 */
.silver-pattern {
  background: linear-gradient(135deg, #e8e8e8 0%, #c0c0c0 50%, #a0a0a0 100%);
}

.gold-pattern {
  background: linear-gradient(135deg, #ffd700 0%, #ffb347 50%, #ff8c00 100%);
}

.rainbow-pattern {
  background: linear-gradient(90deg, #ff0000 0%, #ff8000 16%, #ffff00 33%, #00ff00 50%, #0080ff 66%, #8000ff 83%, #ff0080 100%);
}

.dots-pattern {
  background-color: #c0c0c0;
  background-image: radial-gradient(circle at 10px 10px, #a0a0a0 2px, transparent 2px);
  background-size: 20px 20px;
}

.lines-pattern {
  background-color: #c0c0c0;
  background-image: repeating-linear-gradient(
    90deg,
    transparent,
    transparent 13px,
    #a0a0a0 13px,
    #a0a0a0 15px
  );
}

/* 加载动画 */
.scratch-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20px;
}

/* 触觉反馈效果 */
.scratch-feedback {
  animation: scratch-feedback 0.1s ease-out;
}

@keyframes scratch-feedback {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
  100% {
    transform: scale(1);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .enhanced-scratch-card {
    padding: 12px;
  }
  
  .scratch-container {
    padding: 20px 12px;
  }
  
  .canvas-container {
    margin-bottom: 16px;
  }
  
  .canvas-container canvas {
    max-width: 100%;
    height: auto;
  }
  
  .scratch-progress {
    margin-bottom: 16px;
  }
  
  .prizes-display {
    padding: 12px;
  }
  
  .prize-card {
    margin-bottom: 8px;
  }
}

@media (max-width: 480px) {
  .scratch-button {
    height: 40px;
    font-size: 14px;
    padding: 0 24px;
  }
  
  .scratch-controls {
    flex-direction: column;
    align-items: center;
  }
  
  .scratch-controls .ant-btn {
    width: 120px;
    margin-bottom: 8px;
  }
  
  .canvas-container {
    width: 100%;
    max-width: 300px;
  }
} 