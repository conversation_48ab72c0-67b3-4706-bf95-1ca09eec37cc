import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Typography,
  Table,
  Button,
  Space,
  Tooltip,
  Tag,
  Progress,
  Spin,
  Alert,
  Divider,
  List,
  Avatar,
  Badge,
  Calendar
} from 'antd';
import {
  DashboardOutlined,
  HomeOutlined,
  ShopOutlined,
  TeamOutlined,
  Bar<PERSON><PERSON>Outlined,
  ReloadOutlined,
  EyeOutlined,
  SettingOutlined,
  RiseOutlined,
  FallOutlined,
  ExclamationCircleOutlined,
  DollarOutlined,
  ClockCircleOutlined,
  FileTextOutlined,
  InfoCircleOutlined,
  StarOutlined,
  UserOutlined,
  CalendarOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { useTenant } from '../../contexts/TenantContext';
import apiService from '../../services/api';
import { Line, Column, Pie } from '@ant-design/plots';

const { Title, Paragraph, Text } = Typography;

const HotelDashboard = () => {
  const navigate = useNavigate();
  const { currentTenant, currentProject } = useTenant();

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [dashboardData, setDashboardData] = useState({
    summary: {
      totalRevenue: 0,
      totalBookings: 0,
      totalGuests: 0,
      totalRooms: 0,
      occupancyRate: 0,
      averageRoomRate: 0,
    },
    revenueTrend: [],
    topRoomTypes: [],
    topHotels: [],
    maintenanceAlerts: [],
    revenueBySource: [],
    recentBookings: [],
    aiRecommendations: [],
    todayArrivals: [],
    todayDepartures: []
  });

  // 加载仪表盘数据
  useEffect(() => {
    fetchDashboardData();
  }, [currentTenant, currentProject]);

  // 获取仪表盘数据
  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);

      // 这里应该调用API获取实际数据
      // 为了演示，使用模拟数据
      const mockData = {
        summary: {
          totalRevenue: 256890.75,
          totalBookings: 1256,
          totalGuests: 2356,
          totalRooms: 450,
          occupancyRate: 78.5,
          averageRoomRate: 458.60,
        },
        revenueTrend: [
          { date: '2023-06-01', revenue: 15560.25 },
          { date: '2023-06-02', revenue: 16890.50 },
          { date: '2023-06-03', revenue: 17120.75 },
          { date: '2023-06-04', revenue: 16450.30 },
          { date: '2023-06-05', revenue: 17560.80 },
          { date: '2023-06-06', revenue: 18250.40 },
          { date: '2023-06-07', revenue: 17870.60 },
          { date: '2023-06-08', revenue: 18560.90 },
          { date: '2023-06-09', revenue: 19250.30 },
          { date: '2023-06-10', revenue: 18890.75 },
          { date: '2023-06-11', revenue: 17780.45 },
          { date: '2023-06-12', revenue: 18450.80 },
          { date: '2023-06-13', revenue: 19780.25 },
          { date: '2023-06-14', revenue: 20560.90 },
          { date: '2023-06-15', revenue: 21450.60 },
        ],
        topRoomTypes: [
          { id: '1', name: '豪华大床房', category: '豪华房', revenue: 85600, bookings: 180, occupancy: 85.2 },
          { id: '2', name: '行政套房', category: '套房', revenue: 68900, bookings: 120, occupancy: 92.5 },
          { id: '3', name: '标准双床房', category: '标准房', revenue: 45600, bookings: 220, occupancy: 75.8 },
          { id: '4', name: '家庭套房', category: '套房', revenue: 38900, bookings: 85, occupancy: 82.3 },
          { id: '5', name: '商务大床房', category: '商务房', revenue: 28600, bookings: 150, occupancy: 78.6 },
        ],
        topHotels: [
          { id: '1', name: '北京王府井酒店', region: '华北', revenue: 85600, bookings: 450, growth: 15.2 },
          { id: '2', name: '上海南京路酒店', region: '华东', revenue: 68900, bookings: 380, growth: 12.5 },
          { id: '3', name: '广州天河酒店', region: '华南', revenue: 52600, bookings: 320, growth: 8.7 },
          { id: '4', name: '深圳福田酒店', region: '华南', revenue: 48900, bookings: 285, growth: 10.2 },
          { id: '5', name: '成都春熙路酒店', region: '西南', revenue: 35600, bookings: 250, growth: 9.5 },
        ],
        maintenanceAlerts: [
          { id: '1', room: '1201', hotel: '北京王府井酒店', issue: '空调故障', priority: 'high', status: 'pending' },
          { id: '2', room: '805', hotel: '上海南京路酒店', issue: '热水器故障', priority: 'high', status: 'processing' },
          { id: '3', room: '1508', hotel: '广州天河酒店', issue: '电视故障', priority: 'medium', status: 'pending' },
          { id: '4', room: '602', hotel: '深圳福田酒店', issue: '门锁故障', priority: 'high', status: 'processing' },
          { id: '5', room: '908', hotel: '成都春熙路酒店', issue: '窗帘故障', priority: 'low', status: 'pending' },
        ],
        revenueBySource: [
          { source: '直接预订', revenue: 102560, percentage: 39.9 },
          { source: '在线旅行社', revenue: 85600, percentage: 33.3 },
          { source: '企业客户', revenue: 45600, percentage: 17.8 },
          { source: '旅行社', revenue: 15600, percentage: 6.1 },
          { source: '其他', revenue: 7530, percentage: 2.9 },
        ],
        recentBookings: [
          { id: '1', guest: '张三', hotel: '北京王府井酒店', room_type: '豪华大床房', check_in: '2023-06-20', check_out: '2023-06-25', amount: 2560.50, status: 'confirmed', created_at: '2023-06-15T10:30:00Z' },
          { id: '2', guest: '李四', hotel: '上海南京路酒店', room_type: '行政套房', check_in: '2023-06-18', check_out: '2023-06-22', amount: 3200.75, status: 'confirmed', created_at: '2023-06-15T09:45:00Z' },
          { id: '3', guest: '王五', hotel: '广州天河酒店', room_type: '标准双床房', check_in: '2023-06-19', check_out: '2023-06-21', amount: 1800.25, status: 'pending', created_at: '2023-06-15T08:20:00Z' },
          { id: '4', guest: '赵六', hotel: '深圳福田酒店', room_type: '家庭套房', check_in: '2023-06-22', check_out: '2023-06-26', amount: 4500.80, status: 'confirmed', created_at: '2023-06-14T16:50:00Z' },
          { id: '5', guest: '钱七', hotel: '成都春熙路酒店', room_type: '商务大床房', check_in: '2023-06-21', check_out: '2023-06-24', amount: 2900.60, status: 'pending', created_at: '2023-06-14T15:30:00Z' },
        ],
        aiRecommendations: [
          { id: '1', title: '价格优化建议', content: '周末入住率高，建议提高周末房价，预计可增加收入15%。', type: 'success' },
          { id: '2', title: '维护提醒', content: '北京王府井酒店和上海南京路酒店有多个维护问题待处理，建议尽快安排。', type: 'warning' },
          { id: '3', title: '酒店表现分析', content: '行政套房和豪华大床房入住率高，建议增加此类房型数量。', type: 'success' },
          { id: '4', title: '客户行为洞察', content: '数据显示商务客人偏好快速入住和退房服务，建议优化相关流程。', type: 'info' },
          { id: '5', title: '营销建议', content: '直接预订渠道贡献最高收入，建议加强官网和APP推广。', type: 'success' },
        ],
        todayArrivals: [
          { id: '1', guest: '张三', room: '1201', hotel: '北京王府井酒店', time: '14:00', status: 'confirmed' },
          { id: '2', guest: '李四', room: '805', hotel: '上海南京路酒店', time: '15:30', status: 'confirmed' },
          { id: '3', guest: '王五', room: '1508', hotel: '广州天河酒店', time: '16:00', status: 'pending' },
        ],
        todayDepartures: [
          { id: '1', guest: '赵六', room: '602', hotel: '深圳福田酒店', time: '12:00', status: 'confirmed' },
          { id: '2', guest: '钱七', room: '908', hotel: '成都春熙路酒店', time: '12:30', status: 'pending' },
          { id: '3', guest: '孙八', room: '1105', hotel: '北京王府井酒店', time: '13:00', status: 'confirmed' },
        ],
      };

      setDashboardData(mockData);
    } catch (error) {
      console.error('获取仪表盘数据失败:', error);
      setError('获取仪表盘数据失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 获取状态标签
  const getStatusTag = (status) => {
    switch (status) {
      case 'confirmed':
        return <Tag color="success">已确认</Tag>;
      case 'pending':
        return <Tag color="warning">待确认</Tag>;
      case 'cancelled':
        return <Tag color="error">已取消</Tag>;
      case 'completed':
        return <Tag color="success">已完成</Tag>;
      case 'processing':
        return <Tag color="processing">处理中</Tag>;
      case 'high':
        return <Tag color="error">高</Tag>;
      case 'medium':
        return <Tag color="warning">中</Tag>;
      case 'low':
        return <Tag color="default">低</Tag>;
      default:
        return <Tag>{status}</Tag>;
    }
  };

  // 获取AI建议类型图标
  const getRecommendationIcon = (type) => {
    switch (type) {
      case 'success':
        return <RiseOutlined style={{ color: '#52c41a' }} />;
      case 'warning':
        return <ExclamationCircleOutlined style={{ color: '#faad14' }} />;
      case 'info':
        return <InfoCircleOutlined style={{ color: '#1890ff' }} />;
      default:
        return <InfoCircleOutlined style={{ color: '#1890ff' }} />;
    }
  };

  // 房型表格列
  const roomTypeColumns = [
    {
      title: '房型名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '分类',
      dataIndex: 'category',
      key: 'category',
    },
    {
      title: '收入',
      dataIndex: 'revenue',
      key: 'revenue',
      render: (text) => `¥${text.toLocaleString()}`,
      sorter: (a, b) => a.revenue - b.revenue,
    },
    {
      title: '预订数',
      dataIndex: 'bookings',
      key: 'bookings',
    },
    {
      title: '入住率',
      dataIndex: 'occupancy',
      key: 'occupancy',
      render: (text) => `${text}%`,
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => navigate(`/hotel/rooms/${record.id}`)}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  // 酒店表格列
  const hotelColumns = [
    {
      title: '酒店名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '区域',
      dataIndex: 'region',
      key: 'region',
    },
    {
      title: '收入',
      dataIndex: 'revenue',
      key: 'revenue',
      render: (text) => `¥${text.toLocaleString()}`,
      sorter: (a, b) => a.revenue - b.revenue,
    },
    {
      title: '预订数',
      dataIndex: 'bookings',
      key: 'bookings',
    },
    {
      title: '增长率',
      dataIndex: 'growth',
      key: 'growth',
      render: (text) => (
        <span>
          {text >= 0 ? (
            <RiseOutlined style={{ color: '#52c41a' }} />
          ) : (
            <FallOutlined style={{ color: '#f5222d' }} />
          )}
          {' '}
          {Math.abs(text)}%
        </span>
      ),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => navigate(`/hotel/hotels/${record.id}`)}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  // 预订表格列
  const bookingColumns = [
    {
      title: '预订号',
      dataIndex: 'id',
      key: 'id',
    },
    {
      title: '客人',
      dataIndex: 'guest',
      key: 'guest',
    },
    {
      title: '酒店',
      dataIndex: 'hotel',
      key: 'hotel',
    },
    {
      title: '房型',
      dataIndex: 'room_type',
      key: 'room_type',
    },
    {
      title: '入住日期',
      dataIndex: 'check_in',
      key: 'check_in',
    },
    {
      title: '退房日期',
      dataIndex: 'check_out',
      key: 'check_out',
    },
    {
      title: '金额',
      dataIndex: 'amount',
      key: 'amount',
      render: (text) => `¥${text.toLocaleString()}`,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (text) => getStatusTag(text),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => navigate(`/hotel/bookings/${record.id}`)}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  // 到达/离店表格列
  const arrivalDepartureColumns = [
    {
      title: '客人',
      dataIndex: 'guest',
      key: 'guest',
    },
    {
      title: '房间号',
      dataIndex: 'room',
      key: 'room',
    },
    {
      title: '酒店',
      dataIndex: 'hotel',
      key: 'hotel',
    },
    {
      title: '时间',
      dataIndex: 'time',
      key: 'time',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (text) => getStatusTag(text),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="small">
          <Button
            type="primary"
            size="small"
            onClick={() => navigate(`/hotel/check-in/${record.id}`)}
          >
            {record.status === 'confirmed' ? '办理' : '确认'}
          </Button>
        </Space>
      ),
    },
  ];

  // 收入趋势图配置
  const revenueTrendConfig = {
    data: dashboardData.revenueTrend,
    xField: 'date',
    yField: 'revenue',
    smooth: true,
    point: {
      size: 5,
      shape: 'diamond',
    },
    tooltip: {
      formatter: (datum) => {
        return { name: '收入', value: `¥${datum.revenue.toLocaleString()}` };
      },
    },
    xAxis: {
      title: {
        text: '日期',
      },
    },
    yAxis: {
      title: {
        text: '收入 (元)',
      },
    },
    meta: {
      revenue: {
        alias: '收入',
      },
      date: {
        alias: '日期',
      },
    },
  };

  // 收入来源图配置
  const revenueSourcePieConfig = {
    data: dashboardData.revenueBySource.map(item => ({
      type: item.source,
      value: item.revenue,
    })),
    angleField: 'value',
    colorField: 'type',
    radius: 0.8,
    label: {
      type: 'outer',
      formatter: (datum, item) => `${datum.type}: ${(item.percent * 100).toFixed(1)}%`,
    },
    tooltip: {
      formatter: (datum) => {
        return { name: datum.type, value: `¥${datum.value.toLocaleString()}` };
      },
    },
    legend: {
      layout: 'vertical',
      position: 'right',
    },
    interactions: [{ type: 'element-active' }],
  };

  // 错误提示
  const errorAlert = error ? (
    <Alert
      message="错误"
      description={error}
      type="error"
      showIcon
      style={{ marginBottom: 16 }}
    />
  ) : null;

  return (
    <div className="hotel-dashboard">
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
        <div>
          <Title level={2}><HomeOutlined /> 酒店仪表盘</Title>
          <Paragraph>
            {currentProject ? `${currentProject.name} - ` : ''}
            查看收入数据、入住情况和AI分析建议。
          </Paragraph>
        </div>
        <Button
          type="primary"
          icon={<ReloadOutlined />}
          onClick={fetchDashboardData}
          loading={loading}
        >
          刷新数据
        </Button>
      </div>

      {errorAlert}

      <Spin spinning={loading}>
        {/* 统计卡片 */}
        <Row gutter={16}>
          <Col span={4}>
            <Card>
              <Statistic
                title="总收入"
                value={dashboardData.summary.totalRevenue}
                precision={2}
                valueStyle={{ color: '#3f8600' }}
                prefix={<DollarOutlined />}
                suffix="元"
              />
            </Card>
          </Col>
          <Col span={4}>
            <Card>
              <Statistic
                title="总预订数"
                value={dashboardData.summary.totalBookings}
                valueStyle={{ color: '#1890ff' }}
                prefix={<FileTextOutlined />}
              />
            </Card>
          </Col>
          <Col span={4}>
            <Card>
              <Statistic
                title="客人数"
                value={dashboardData.summary.totalGuests}
                valueStyle={{ color: '#722ed1' }}
                prefix={<UserOutlined />}
              />
            </Card>
          </Col>
          <Col span={4}>
            <Card>
              <Statistic
                title="房间数"
                value={dashboardData.summary.totalRooms}
                valueStyle={{ color: '#fa8c16' }}
                prefix={<HomeOutlined />}
              />
            </Card>
          </Col>
          <Col span={4}>
            <Card>
              <Statistic
                title="入住率"
                value={dashboardData.summary.occupancyRate}
                precision={1}
                valueStyle={{ color: '#cf1322' }}
                prefix={<BarChartOutlined />}
                suffix="%"
              />
            </Card>
          </Col>
          <Col span={4}>
            <Card>
              <Statistic
                title="平均房价"
                value={dashboardData.summary.averageRoomRate}
                precision={2}
                valueStyle={{ color: '#13c2c2' }}
                prefix={<DollarOutlined />}
                suffix="元"
              />
            </Card>
          </Col>
        </Row>

        <Divider />

        {/* 图表 */}
        <Row gutter={16} style={{ marginTop: 16 }}>
          <Col span={16}>
            <Card title="收入趋势" className="dashboard-card">
              <div style={{ height: 300 }}>
                <Line {...revenueTrendConfig} />
              </div>
            </Card>
          </Col>
          <Col span={8}>
            <Card title="收入来源分布" className="dashboard-card">
              <div style={{ height: 300 }}>
                <Pie {...revenueSourcePieConfig} />
              </div>
            </Card>
          </Col>
        </Row>

        <Divider />

        {/* 今日到达和离店 */}
        <Row gutter={16} style={{ marginTop: 16 }}>
          <Col span={12}>
            <Card
              title={
                <span>
                  <CheckCircleOutlined style={{ color: '#52c41a', marginRight: 8 }} />
                  今日到达
                </span>
              }
              extra={<Button type="link" onClick={() => navigate('/hotel/arrivals')}>查看全部</Button>}
            >
              <Table
                columns={arrivalDepartureColumns}
                dataSource={dashboardData.todayArrivals}
                rowKey="id"
                pagination={false}
              />
            </Card>
          </Col>
          <Col span={12}>
            <Card
              title={
                <span>
                  <CloseCircleOutlined style={{ color: '#f5222d', marginRight: 8 }} />
                  今日离店
                </span>
              }
              extra={<Button type="link" onClick={() => navigate('/hotel/departures')}>查看全部</Button>}
            >
              <Table
                columns={arrivalDepartureColumns}
                dataSource={dashboardData.todayDepartures}
                rowKey="id"
                pagination={false}
              />
            </Card>
          </Col>
        </Row>

        {/* AI建议 */}
        <Card title="AI分析建议" style={{ marginTop: 16 }}>
          <List
            itemLayout="horizontal"
            dataSource={dashboardData.aiRecommendations}
            renderItem={item => (
              <List.Item>
                <List.Item.Meta
                  avatar={
                    <Avatar icon={getRecommendationIcon(item.type)} />
                  }
                  title={item.title}
                  description={item.content}
                />
              </List.Item>
            )}
          />
        </Card>

        {/* 热门房型 */}
        <Card
          title="热门房型"
          extra={<Button type="link" onClick={() => navigate('/hotel/rooms')}>查看全部</Button>}
          style={{ marginTop: 16 }}
        >
          <Table
            columns={roomTypeColumns}
            dataSource={dashboardData.topRoomTypes}
            rowKey="id"
            pagination={false}
          />
        </Card>

        {/* 酒店表现 */}
        <Card
          title="酒店表现"
          extra={<Button type="link" onClick={() => navigate('/hotel/hotels')}>查看全部</Button>}
          style={{ marginTop: 16 }}
        >
          <Table
            columns={hotelColumns}
            dataSource={dashboardData.topHotels}
            rowKey="id"
            pagination={false}
          />
        </Card>

        {/* 维护提醒 */}
        <Card
          title={
            <span>
              <ExclamationCircleOutlined style={{ color: '#faad14', marginRight: 8 }} />
              维护提醒
            </span>
          }
          extra={<Button type="link" onClick={() => navigate('/hotel/maintenance')}>查看全部</Button>}
          style={{ marginTop: 16 }}
        >
          <Table
            columns={[
              { title: '房间号', dataIndex: 'room', key: 'room' },
              { title: '酒店', dataIndex: 'hotel', key: 'hotel' },
              { title: '问题', dataIndex: 'issue', key: 'issue' },
              { title: '优先级', dataIndex: 'priority', key: 'priority', render: (text) => getStatusTag(text) },
              { title: '状态', dataIndex: 'status', key: 'status', render: (text) => getStatusTag(text) },
              {
                title: '操作',
                key: 'action',
                render: (_, record) => (
                  <Button type="primary" size="small" onClick={() => navigate('/hotel/maintenance/handle')}>
                    处理
                  </Button>
                ),
              },
            ]}
            dataSource={dashboardData.maintenanceAlerts}
            rowKey="id"
            pagination={false}
          />
        </Card>

        {/* 最近预订 */}
        <Card
          title="最近预订"
          extra={<Button type="link" onClick={() => navigate('/hotel/bookings')}>查看全部</Button>}
          style={{ marginTop: 16 }}
        >
          <Table
            columns={bookingColumns}
            dataSource={dashboardData.recentBookings}
            rowKey="id"
            pagination={false}
          />
        </Card>
      </Spin>
    </div>
  );
};

export default HotelDashboard;
