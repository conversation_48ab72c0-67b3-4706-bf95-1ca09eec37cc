import React, { useState, useEffect, useRef } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Form,
  Input,
  InputNumber,
  Select,
  Popconfirm,
  message,
  Typography,
  Tabs,
  Modal,
  Drawer,
  Row,
  Col,
  Statistic,
  Divider,
  Upload,
  Tag,
  Tooltip,
  Alert,
  Radio
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  SaveOutlined,
  CloseOutlined,
  SearchOutlined,
  ReloadOutlined,
  HistoryOutlined,
  InboxOutlined,
  UploadOutlined,
  DownloadOutlined,
  ExclamationCircleOutlined,
  CheckCircleOutlined,
  FolderOutlined
} from '@ant-design/icons';
import apiService from '../../../services/api';
import FileSpaceSelector from '../../../components/FileSpaceSelector';

const { Title, Text } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;
const { Dragger } = Upload;

// 可编辑单元格组件
const EditableCell = ({
  editing,
  dataIndex,
  title,
  inputType,
  record,
  index,
  children,
  ...restProps
}) => {
  const inputNode = inputType === 'number' ? (
    <InputNumber min={0} />
  ) : inputType === 'select' ? (
    <Select>
      <Option value="个">个</Option>
      <Option value="箱">箱</Option>
      <Option value="kg">kg</Option>
      <Option value="g">g</Option>
      <Option value="L">L</Option>
      <Option value="ml">ml</Option>
      <Option value="件">件</Option>
      <Option value="包">包</Option>
      <Option value="瓶">瓶</Option>
      <Option value="袋">袋</Option>
    </Select>
  ) : (
    <Input />
  );

  return (
    <td {...restProps}>
      {editing ? (
        <Form.Item
          name={dataIndex}
          style={{ margin: 0 }}
          rules={[
            {
              required: true,
              message: `请输入${title}!`,
            },
          ]}
        >
          {inputNode}
        </Form.Item>
      ) : (
        children
      )}
    </td>
  );
};

/**
 * 库存管理组件
 */
const InventoryManagement = () => {
  // 状态定义
  const [loading, setLoading] = useState(false);
  const [inventoryData, setInventoryData] = useState([]);
  const [editingKey, setEditingKey] = useState('');
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [modalForm] = Form.useForm();
  const [form] = Form.useForm();
  const [searchText, setSearchText] = useState('');
  const [searchedColumn, setSearchedColumn] = useState('');
  const searchInput = useRef(null);
  const [historyVisible, setHistoryVisible] = useState(false);
  const [historyData, setHistoryData] = useState([]);
  const [historyLoading, setHistoryLoading] = useState(false);
  const [currentItem, setCurrentItem] = useState(null);
  const [activeTab, setActiveTab] = useState('1');
  const [warehouses, setWarehouses] = useState([]);
  const [selectedWarehouse, setSelectedWarehouse] = useState(null);
  const [uploadVisible, setUploadVisible] = useState(false);
  const [fileList, setFileList] = useState([]);
  const [uploading, setUploading] = useState(false);
  const [previewData, setPreviewData] = useState([]);
  const [previewVisible, setPreviewVisible] = useState(false);
  const [importStats, setImportStats] = useState({
    total: 0,
    new_items: 0,
    updated_items: 0,
    errors: 0
  });
  
  // 新增：上传方式和项目空间选择相关状态
  const [uploadMethod, setUploadMethod] = useState('direct'); // 'direct' | 'space'
  const [spaceSelectorVisible, setSpaceSelectorVisible] = useState(false);
  const [selectedSpaceFile, setSelectedSpaceFile] = useState(null);

  // 判断是否处于编辑状态
  const isEditing = (record) => record.id === editingKey;

  // 获取库存数据
  const fetchInventoryData = async () => {
    setLoading(true);
    try {
      const response = await apiService.project.inventory.getItems({
        warehouse_id: selectedWarehouse
      });
      if (response && response.items) {
        setInventoryData(response.items);
      } else {
        setInventoryData([]);
      }
    } catch (error) {
      console.error('获取库存数据失败:', error);
      message.error('获取库存数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取仓库列表
  const fetchWarehouses = async () => {
    try {
      const response = await apiService.project.warehouse.getList();
      console.log('库存管理-获取仓库列表响应:', response);
      
      let warehouseList = [];
      if (response && Array.isArray(response.items)) {
        warehouseList = response.items;
      } else if (response && Array.isArray(response)) {
        warehouseList = response;
      }
      
      setWarehouses(warehouseList);
      if (warehouseList.length > 0 && !selectedWarehouse) {
        setSelectedWarehouse(warehouseList[0].id);
      }
    } catch (error) {
      console.error('获取仓库列表失败:', error);
      message.error('获取仓库列表失败');
      setWarehouses([]);
    }
  };

  // 获取库存历史记录
  const fetchInventoryHistory = async (itemId) => {
    setHistoryLoading(true);
    try {
      const response = await apiService.project.inventory.getItemHistory(itemId);
      if (response && response.history) {
        setHistoryData(response.history);
      } else {
        setHistoryData([]);
      }
    } catch (error) {
      console.error('获取库存历史记录失败:', error);
      message.error('获取库存历史记录失败');
    } finally {
      setHistoryLoading(false);
    }
  };

  // 初始化
  useEffect(() => {
    fetchWarehouses();
  }, []);

  // 当选择的仓库变化时，重新获取库存数据
  useEffect(() => {
    if (selectedWarehouse) {
      fetchInventoryData();
    }
  }, [selectedWarehouse]);

  // 开始编辑行
  const edit = (record) => {
    form.setFieldsValue({
      ...record,
    });
    setEditingKey(record.id);
  };

  // 取消编辑
  const cancel = () => {
    setEditingKey('');
  };

  // 保存编辑
  const save = async (key) => {
    try {
      const row = await form.validateFields();
      const newData = [...inventoryData];
      const index = newData.findIndex((item) => key === item.id);

      if (index > -1) {
        try {
          const response = await apiService.project.inventory.updateItem(key, row);
          if (response && response.success) {
            const item = response.data;
            newData.splice(index, 1, item);
            setInventoryData(newData);
            setEditingKey('');
            message.success('库存更新成功');
          } else {
            message.error('库存更新失败');
          }
        } catch (error) {
          console.error('更新库存失败:', error);
          message.error('更新库存失败: ' + error.message);
        }
      } else {
        newData.push(row);
        setInventoryData(newData);
        setEditingKey('');
      }
    } catch (errInfo) {
      console.error('验证失败:', errInfo);
    }
  };

  // 删除库存项
  const handleDelete = async (id) => {
    try {
      const response = await apiService.project.inventory.deleteItem(id);
      if (response && response.success) {
        setInventoryData(inventoryData.filter(item => item.id !== id));
        message.success('库存项目删除成功');
      } else {
        message.error('库存项目删除失败');
      }
    } catch (error) {
      console.error('删除库存项目失败:', error);
      message.error('删除库存项目删除失败: ' + error.message);
    }
  };

  // 添加新商品
  const handleAdd = async () => {
    try {
      const values = await modalForm.validateFields();
      values.warehouse_id = selectedWarehouse;

      try {
        const response = await apiService.project.inventory.addItem(values);
        if (response && response.success) {
          setInventoryData([...inventoryData, response.data]);
          setIsModalVisible(false);
          modalForm.resetFields();
          message.success('库存项目添加成功');
        } else {
          message.error('库存项目添加失败: ' + (response?.message || '未知错误'));
        }
      } catch (error) {
        console.error('添加库存项目失败:', error);
        message.error('添加库存项目失败: ' + error.message);
      }
    } catch (errorInfo) {
      console.error('验证失败:', errorInfo);
    }
  };

  // 搜索处理函数
  const handleSearch = (selectedKeys, confirm, dataIndex) => {
    confirm();
    setSearchText(selectedKeys[0]);
    setSearchedColumn(dataIndex);
  };

  // 重置搜索
  const handleReset = (clearFilters) => {
    clearFilters();
    setSearchText('');
  };

  // 获取搜索组件
  const getColumnSearchProps = (dataIndex) => ({
    filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
      <div style={{ padding: 8 }}>
        <Input
          ref={searchInput}
          placeholder={`搜索 ${dataIndex}`}
          value={selectedKeys[0]}
          onChange={(e) => setSelectedKeys(e.target.value ? [e.target.value] : [])}
          onPressEnter={() => handleSearch(selectedKeys, confirm, dataIndex)}
          style={{ width: 188, marginBottom: 8, display: 'block' }}
        />
        <Space>
          <Button
            type="primary"
            onClick={() => handleSearch(selectedKeys, confirm, dataIndex)}
            icon={<SearchOutlined />}
            size="small"
            style={{ width: 90 }}
          >
            搜索
          </Button>
          <Button
            onClick={() => handleReset(clearFilters)}
            size="small"
            style={{ width: 90 }}
          >
            重置
          </Button>
        </Space>
      </div>
    ),
    filterIcon: (filtered) => (
      <SearchOutlined style={{ color: filtered ? '#1890ff' : undefined }} />
    ),
    onFilter: (value, record) =>
      record[dataIndex]
        ? record[dataIndex].toString().toLowerCase().includes(value.toLowerCase())
        : '',
    onFilterDropdownVisibleChange: (visible) => {
      if (visible) {
        setTimeout(() => searchInput.current?.select(), 100);
      }
    },
    render: (text) =>
      searchedColumn === dataIndex ? (
        <span style={{ fontWeight: 'bold' }}>{text}</span>
      ) : (
        text
      ),
  });

  // 查看历史记录
  const viewHistory = (record) => {
    setCurrentItem(record);
    fetchInventoryHistory(record.id);
    setHistoryVisible(true);
  };

  // 处理仓库选择变化
  const handleWarehouseChange = (value) => {
    setSelectedWarehouse(value);
  };

  // 处理项目空间文件选择
  const handleSpaceFileSelect = (file) => {
    setSelectedSpaceFile(file);
    setSpaceSelectorVisible(false);
    
    // 模拟文件列表格式
    setFileList([{
      uid: file.id,
      name: file.name,
      status: 'done',
      spaceFile: true,
      spaceFileId: file.id
    }]);
  };

  // 处理文件上传
  const handleUpload = async () => {
    if (uploadMethod === 'direct' && fileList.length === 0) {
      message.error('请先选择文件');
      return;
    }
    
    if (uploadMethod === 'space' && !selectedSpaceFile) {
      message.error('请先选择项目空间文件');
      return;
    }

    if (!selectedWarehouse) {
      message.error('请选择仓库后再上传');
      return;
    }

    setUploading(true);

    try {
      let fileId;
      
      if (uploadMethod === 'space') {
        // 使用项目空间已选择的文件
        fileId = selectedSpaceFile.id;
        message.loading('AI正在智能识别库存表内容，请稍候...', 2);
      } else {
        // 直接上传文件到项目空间
        const uploadResult = await apiService.project.space.uploadFile(
          fileList[0].originFileObj || fileList[0],
          '/inventory-management',
          '库存表文件',
          false,
          (percent) => {
            console.log('库存表上传进度:', percent);
          }
        );

        if (!uploadResult || !uploadResult.success) {
          throw new Error(uploadResult?.message || '文件上传失败');
        }

        if (!uploadResult.data || !uploadResult.data.id) {
          throw new Error('文件上传成功但未返回文件ID');
        }

        fileId = uploadResult.data.id;
        message.loading('AI正在智能识别库存表内容，请稍候...', 2);
      }
      
      const previewParams = {
        file_id: fileId,
        warehouse_id: selectedWarehouse,
        use_ai: true, // 启用AI处理
        processing_mode: 'auto' // 使用自动模式（优先模板匹配，失败时使用AI）
      };

      console.log('发送库存表AI预览请求参数:', previewParams);

      // 调用AI智能处理的预览接口
      const response = await apiService.project.inventory.previewUploadWithAi(previewParams);

      console.log('库存表AI预览响应:', response);

      if (response && response.success) {
        console.log('库存表预览数据:', response.data);
        
        // 处理预览数据
        setPreviewData(response.data.preview || response.data.inventory_items || []);
        setImportStats({
          total: response.data.processing_info?.total_rows || 0,
          new_items: response.data.new_items || 0,
          updated_items: response.data.updated_items || 0,
          errors: response.data.processing_info?.error_rows || 0
        });
        
        setPreviewVisible(true);
        setFileList([]);
        setSelectedSpaceFile(null);
        
        // 显示AI处理结果信息
        if (response.data.processing_info) {
          const processingInfo = response.data.processing_info;
          message.success(
            `智能识别完成！使用${processingInfo.method === 'ai' ? 'AI视觉识别' : 
            processingInfo.method === 'hybrid' ? '混合模式' : '模板匹配'}处理，` +
            `共识别${processingInfo.total_rows}行数据，其中有效数据${processingInfo.valid_rows}行`
          );
        } else {
          message.success('智能识别完成！');
        }
      } else {
        console.error('库存表AI预览失败:', response);
        const errorMessage = response?.message || response?.detail || '智能识别失败，请检查文件格式';
        throw new Error(errorMessage);
      }
    } catch (error) {
      console.error('上传库存表失败:', error);
      message.error('上传库存表失败: ' + error.message);
    } finally {
      setUploading(false);
    }
  };

  // 确认导入
  const confirmImport = async () => {
    try {
      const response = await apiService.project.inventory.confirmImport({
        warehouse_id: selectedWarehouse
      });
      if (response && response.success) {
        message.success('库存数据导入成功');
        setPreviewVisible(false);
        fetchInventoryData();
      } else {
        message.error('库存数据导入失败: ' + (response?.message || '未知错误'));
      }
    } catch (error) {
      console.error('确认导入库存表失败:', error);
      message.error('确认导入库存表失败: ' + error.message);
    }
  };

  // 取消导入
  const cancelImport = async () => {
    try {
      await apiService.project.inventory.cancelImport();
      message.info('已取消导入');
      setPreviewVisible(false);
    } catch (error) {
      console.error('取消导入失败:', error);
      message.error('取消导入失败: ' + error.message);
    }
  };

  // 下载库存表模板
  const downloadTemplate = () => {
    window.open(apiService.project.inventory.getTemplateUrl(), '_blank');
  };

  // 表格列定义
  const columns = [
    {
      title: '商品名称',
      dataIndex: 'name',
      key: 'name',
      editable: true,
      width: '15%',
      ...getColumnSearchProps('name'),
    },
    {
      title: '商品编码',
      dataIndex: 'code',
      key: 'code',
      editable: true,
      width: '10%',
      ...getColumnSearchProps('code'),
    },
    {
      title: '分类',
      dataIndex: 'category',
      key: 'category',
      editable: true,
      width: '10%',
      filters: [
        { text: '食品', value: '食品' },
        { text: '饮料', value: '饮料' },
        { text: '日用品', value: '日用品' },
        { text: '其他', value: '其他' },
      ],
      onFilter: (value, record) => record.category === value,
    },
    {
      title: '当前库存',
      dataIndex: 'current_stock',
      key: 'current_stock',
      editable: true,
      width: '10%',
      sorter: (a, b) => a.current_stock - b.current_stock,
      render: (text, record) => {
        const isLowStock = record.current_stock < record.min_stock;
        return (
          <span style={{ color: isLowStock ? '#ff4d4f' : 'inherit' }}>
            {text} {isLowStock && <ExclamationCircleOutlined style={{ color: '#ff4d4f' }} />}
          </span>
        );
      },
    },
    {
      title: '最低库存',
      dataIndex: 'min_stock',
      key: 'min_stock',
      editable: true,
      width: '10%',
    },
    {
      title: '单位',
      dataIndex: 'unit',
      key: 'unit',
      editable: true,
      width: '8%',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: '10%',
      filters: [
        { text: '库存不足', value: 'low' },
        { text: '滞销', value: 'slow_moving' },
        { text: '正常', value: 'normal' },
      ],
      onFilter: (value, record) => {
        if (value === 'low') return record.current_stock < record.min_stock;
        if (value === 'slow_moving') return record.is_slow_moving;
        if (value === 'normal') return record.current_stock >= record.min_stock && !record.is_slow_moving;
        return true;
      },
      render: (_, record) => {
        if (record.current_stock < record.min_stock) {
          return <Tag color="error">库存不足</Tag>;
        }
        if (record.is_slow_moving) {
          return <Tag color="warning">滞销</Tag>;
        }
        return <Tag color="success">正常</Tag>;
      },
    },
    {
      title: '更新时间',
      dataIndex: 'updated_at',
      key: 'updated_at',
      width: '12%',
      sorter: (a, b) => new Date(a.updated_at) - new Date(b.updated_at),
    },
    {
      title: '操作',
      key: 'action',
      width: '15%',
      render: (_, record) => {
        const editable = isEditing(record);
        return editable ? (
          <Space>
            <Button
              type="link"
              icon={<SaveOutlined />}
              onClick={() => save(record.id)}
              style={{ marginRight: 8 }}
            >
              保存
            </Button>
            <Popconfirm title="确定取消?" onConfirm={cancel}>
              <Button type="link" icon={<CloseOutlined />}>取消</Button>
            </Popconfirm>
          </Space>
        ) : (
          <Space>
            <Button
              type="link"
              icon={<EditOutlined />}
              disabled={editingKey !== ''}
              onClick={() => edit(record)}
            >
              编辑
            </Button>
            <Popconfirm
              title="确定删除此商品?"
              onConfirm={() => handleDelete(record.id)}
              okText="确定"
              cancelText="取消"
            >
              <Button type="link" danger icon={<DeleteOutlined />}>
                删除
              </Button>
            </Popconfirm>
            <Button
              type="link"
              icon={<HistoryOutlined />}
              onClick={() => viewHistory(record)}
            >
              历史
            </Button>
          </Space>
        );
      },
    },
  ];

  // 历史记录表格列
  const historyColumns = [
    {
      title: '操作时间',
      dataIndex: 'timestamp',
      key: 'timestamp',
      width: '20%',
    },
    {
      title: '操作类型',
      dataIndex: 'action_type',
      key: 'action_type',
      width: '15%',
      render: (text) => {
        const actionMap = {
          'add': '新增',
          'update': '更新',
          'delete': '删除',
          'transfer_in': '转入',
          'transfer_out': '转出',
          'import': '导入',
        };
        return actionMap[text] || text;
      },
    },
    {
      title: '变更前数量',
      dataIndex: 'previous_stock',
      key: 'previous_stock',
      width: '15%',
    },
    {
      title: '变更后数量',
      dataIndex: 'new_stock',
      key: 'new_stock',
      width: '15%',
    },
    {
      title: '变更数量',
      dataIndex: 'change_amount',
      key: 'change_amount',
      width: '15%',
      render: (text) => {
        const value = parseFloat(text);
        return (
          <span style={{ color: value > 0 ? '#52c41a' : value < 0 ? '#ff4d4f' : 'inherit' }}>
            {value > 0 ? `+${text}` : text}
          </span>
        );
      },
    },
    {
      title: '操作人',
      dataIndex: 'operator',
      key: 'operator',
      width: '15%',
    },
    {
      title: '备注',
      dataIndex: 'notes',
      key: 'notes',
    },
  ];

  // 预览数据表格列
  const previewColumns = [
    {
      title: '商品名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '商品编码',
      dataIndex: 'code',
      key: 'code',
    },
    {
      title: '分类',
      dataIndex: 'category',
      key: 'category',
    },
    {
      title: '当前库存',
      dataIndex: 'current_stock',
      key: 'current_stock',
    },
    {
      title: '最低库存',
      dataIndex: 'min_stock',
      key: 'min_stock',
    },
    {
      title: '单位',
      dataIndex: 'unit',
      key: 'unit',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (text) => {
        if (text === 'new') return <Tag color="blue">新增</Tag>;
        if (text === 'update') return <Tag color="green">更新</Tag>;
        if (text === 'error') return <Tag color="red">错误</Tag>;
        return <Tag>{text}</Tag>;
      },
    },
    {
      title: '错误信息',
      dataIndex: 'error_message',
      key: 'error_message',
      render: (text) => text || '-',
    },
  ];

  // 合并可编辑属性
  const mergedColumns = columns.map((col) => {
    if (!col.editable) {
      return col;
    }
    return {
      ...col,
      onCell: (record) => ({
        record,
        inputType: col.dataIndex === 'current_stock' || col.dataIndex === 'min_stock' ? 'number' :
                  col.dataIndex === 'unit' ? 'select' : 'text',
        dataIndex: col.dataIndex,
        title: col.title,
        editing: isEditing(record),
      }),
    };
  });

  // 渲染库存管理内容
  const renderManagementContent = () => {
    return (
      <div className="inventory-management-page">
        {/* 统计卡片 */}
        <Row gutter={16} style={{ marginBottom: 16 }}>
          <Col span={6}>
            <Card>
              <Statistic
                title="总库存商品"
                value={inventoryData.length}
                valueStyle={{ color: '#1890ff' }}
                suffix="种"
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="库存不足"
                value={inventoryData.filter(i => i.current_stock < i.min_stock).length}
                valueStyle={{ color: '#ff4d4f' }}
                suffix="种"
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="滞销商品"
                value={inventoryData.filter(i => i.is_slow_moving).length}
                valueStyle={{ color: '#faad14' }}
                suffix="种"
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="正常库存"
                value={inventoryData.filter(i => i.current_stock >= i.min_stock && !i.is_slow_moving).length}
                valueStyle={{ color: '#52c41a' }}
                suffix="种"
              />
            </Card>
          </Col>
        </Row>

        {/* 搜索和筛选区域 */}
        <Form layout="inline" style={{ marginBottom: 16 }}>
          <Form.Item label="仓库">
            <Select
              placeholder="选择仓库"
              style={{ width: 200 }}
              value={selectedWarehouse}
              onChange={handleWarehouseChange}
            >
              {warehouses.map(warehouse => (
                <Option key={warehouse.id} value={warehouse.id}>{warehouse.name}</Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item label="商品分类">
            <Select style={{ width: 150 }} allowClear placeholder="全部分类">
              <Option value="">全部</Option>
              <Option value="食品">食品</Option>
              <Option value="饮料">饮料</Option>
              <Option value="日用品">日用品</Option>
              <Option value="其他">其他</Option>
            </Select>
          </Form.Item>
          <Form.Item label="库存状态">
            <Select style={{ width: 150 }} allowClear placeholder="全部状态">
              <Option value="">全部</Option>
              <Option value="low">库存不足</Option>
              <Option value="slow_moving">滞销</Option>
              <Option value="normal">正常</Option>
            </Select>
          </Form.Item>
          <Form.Item label="搜索">
            <Input
              placeholder="商品名称/编码"
              prefix={<SearchOutlined />}
              style={{ width: 200 }}
              allowClear
            />
          </Form.Item>
          <Form.Item>
            <Button type="primary" icon={<SearchOutlined />}>搜索</Button>
          </Form.Item>
          <Form.Item>
            <Button icon={<ReloadOutlined />} onClick={fetchInventoryData} loading={loading}>刷新</Button>
          </Form.Item>
        </Form>

        {/* 库存表格 */}
        <Form form={form} component={false}>
          <Table
            components={{
              body: {
                cell: EditableCell,
              },
            }}
            bordered
            dataSource={inventoryData}
            columns={mergedColumns}
            rowKey="id"
            rowClassName="editable-row"
            loading={loading}
            pagination={{
              onChange: cancel,
              pageSize: 10,
              showSizeChanger: true,
              showTotal: (total) => `共 ${total} 条记录`
            }}
          />
        </Form>

        {/* 添加商品模态框 */}
        <Modal
          title="添加库存商品"
          open={isModalVisible}
          onOk={handleAdd}
          onCancel={() => {
            setIsModalVisible(false);
            modalForm.resetFields();
          }}
        >
          <Form
            form={modalForm}
            layout="vertical"
            initialValues={{
              current_stock: 0,
              min_stock: 10,
              unit: '个',
              category: '其他',
            }}
          >
            <Form.Item
              name="name"
              label="商品名称"
              rules={[{ required: true, message: '请输入商品名称' }]}
            >
              <Input placeholder="请输入商品名称" />
            </Form.Item>
            <Form.Item
              name="code"
              label="商品编码"
              rules={[{ required: true, message: '请输入商品编码' }]}
            >
              <Input placeholder="请输入商品编码" />
            </Form.Item>
            <Form.Item
              name="category"
              label="分类"
              rules={[{ required: true, message: '请选择分类' }]}
            >
              <Select placeholder="请选择分类">
                <Option value="食品">食品</Option>
                <Option value="饮料">饮料</Option>
                <Option value="日用品">日用品</Option>
                <Option value="其他">其他</Option>
              </Select>
            </Form.Item>
            <Form.Item
              name="current_stock"
              label="当前库存"
              rules={[{ required: true, message: '请输入当前库存' }]}
            >
              <InputNumber min={0} style={{ width: '100%' }} />
            </Form.Item>
            <Form.Item
              name="min_stock"
              label="最低库存"
              rules={[{ required: true, message: '请输入最低库存' }]}
            >
              <InputNumber min={0} style={{ width: '100%' }} />
            </Form.Item>
            <Form.Item
              name="unit"
              label="单位"
              rules={[{ required: true, message: '请选择单位' }]}
            >
              <Select placeholder="请选择单位">
                <Option value="个">个</Option>
                <Option value="箱">箱</Option>
                <Option value="kg">kg</Option>
                <Option value="g">g</Option>
                <Option value="L">L</Option>
                <Option value="ml">ml</Option>
                <Option value="件">件</Option>
                <Option value="包">包</Option>
                <Option value="瓶">瓶</Option>
                <Option value="袋">袋</Option>
              </Select>
            </Form.Item>
          </Form>
        </Modal>

        {/* 历史记录抽屉 */}
        <Drawer
          title={`${currentItem?.name || ''} - 库存历史记录`}
          width={800}
          onClose={() => setHistoryVisible(false)}
          open={historyVisible}
        >
          {currentItem && (
            <>
              <Row gutter={16} style={{ marginBottom: 24 }}>
                <Col span={8}>
                  <Statistic
                    title="当前库存"
                    value={currentItem.current_stock}
                    prefix={<InboxOutlined />}
                  />
                </Col>
                <Col span={8}>
                  <Statistic
                    title="最低库存"
                    value={currentItem.min_stock}
                    valueStyle={{ color: currentItem.current_stock < currentItem.min_stock ? '#cf1322' : 'inherit' }}
                  />
                </Col>
                <Col span={8}>
                  <Statistic
                    title="库存状态"
                    value={
                      currentItem.current_stock < currentItem.min_stock
                        ? '库存不足'
                        : currentItem.is_slow_moving
                          ? '滞销'
                          : '正常'
                    }
                    valueStyle={{
                      color: currentItem.current_stock < currentItem.min_stock
                        ? '#cf1322'
                        : currentItem.is_slow_moving
                          ? '#faad14'
                          : '#3f8600'
                    }}
                  />
                </Col>
              </Row>

              <Divider>历史记录</Divider>

              <Table
                columns={historyColumns}
                dataSource={historyData}
                rowKey="id"
                loading={historyLoading}
                pagination={{ pageSize: 5 }}
              />
            </>
          )}
        </Drawer>
      </div>
    );
  };

  // 渲染上传内容
  const renderUploadContent = () => {
    return (
      <div>
        <Card title="智能上传库存表">
          <Alert
            message="AI智能识别"
            description="支持上传库存表的图片（JPG、PNG）或Excel文件。系统将优先使用模板匹配进行快速识别，如果模板匹配失败或置信度较低，则自动切换到AI视觉识别模式，确保准确提取商品库存信息。"
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />

          {/* 上传方式选择 */}
          <div style={{ marginBottom: 24 }}>
            <Title level={5}>选择上传方式</Title>
            <Radio.Group 
              value={uploadMethod} 
              onChange={(e) => {
                setUploadMethod(e.target.value);
                setFileList([]);
                setSelectedSpaceFile(null);
              }}
            >
              <Radio value="direct">
                <Space>
                  <UploadOutlined />
                  直接上传文件
                </Space>
              </Radio>
              <Radio value="space">
                <Space>
                  <FolderOutlined />
                  从项目空间选择
                </Space>
              </Radio>
            </Radio.Group>
          </div>

          {/* 直接上传模式 */}
          {uploadMethod === 'direct' && (
            <Dragger
              fileList={fileList}
              beforeUpload={(file) => {
                // 支持Excel和图片格式
                const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                               file.type === 'application/vnd.ms-excel' ||
                               file.type === 'text/csv';
                const isImage = file.type === 'image/jpeg' || 
                               file.type === 'image/jpg' || 
                               file.type === 'image/png';
                
                if (!isExcel && !isImage) {
                  message.error('只能上传Excel文件(.xlsx, .xls, .csv)或图片文件(.jpg, .jpeg, .png)!');
                  return false;
                }

                // 检查文件大小
                const maxSize = isImage ? 5 * 1024 * 1024 : 2 * 1024 * 1024; // 图片5MB，Excel 2MB
                if (file.size > maxSize) {
                  message.error(`文件大小不能超过${isImage ? '5MB' : '2MB'}!`);
                  return false;
                }

                setFileList([file]);
                return false;
              }}
              onRemove={() => {
                setFileList([]);
              }}
              multiple={false}
              accept=".xlsx,.xls,.csv,.jpg,.jpeg,.png"
              disabled={uploading}
            >
              <p className="ant-upload-drag-icon">
                <InboxOutlined />
              </p>
              <p className="ant-upload-text">点击或拖拽库存表到此区域上传</p>
              <p className="ant-upload-hint">
                支持 Excel 文件(.xlsx, .xls, .csv)和图片文件(.jpg, .jpeg, .png)<br/>
                Excel文件大小不超过2MB，图片文件不超过5MB<br/>
                <strong>AI将自动识别表格结构，智能提取库存数据</strong>
              </p>
            </Dragger>
          )}

          {/* 项目空间选择模式 */}
          {uploadMethod === 'space' && (
            <div>
              {!selectedSpaceFile ? (
                <div 
                  style={{ 
                    border: '1px dashed #d9d9d9', 
                    borderRadius: 6, 
                    padding: 24, 
                    textAlign: 'center',
                    cursor: 'pointer',
                    transition: 'border-color 0.3s'
                  }}
                  onMouseEnter={(e) => e.target.style.borderColor = '#1890ff'}
                  onMouseLeave={(e) => e.target.style.borderColor = '#d9d9d9'}
                  onClick={() => setSpaceSelectorVisible(true)}
                >
                  <FolderOutlined style={{ fontSize: 48, color: '#1890ff', marginBottom: 16 }} />
                  <div style={{ fontSize: 16, marginBottom: 8 }}>点击选择项目空间文件</div>
                  <div style={{ color: '#999', fontSize: 14 }}>从项目空间选择库存表文件（支持Excel或图片格式）</div>
                  <div style={{ color: '#666', fontSize: 12, marginTop: 8 }}>
                    支持文件类型：xlsx, xls, csv, jpg, jpeg, png
                  </div>
                </div>
              ) : (
                <div style={{ padding: 12, border: '1px solid #d9d9d9', borderRadius: 6 }}>
                  <Space>
                    <Tag color="blue">已选择</Tag>
                    <Text>{selectedSpaceFile.name}</Text>
                    <Button 
                      size="small" 
                      onClick={() => {
                        setSelectedSpaceFile(null);
                        setFileList([]);
                        setSpaceSelectorVisible(true);
                      }}
                    >
                      重新选择
                    </Button>
                  </Space>
                </div>
              )}
              
              <FileSpaceSelector
                onFileSelect={handleSpaceFileSelect}
                accept={['.xlsx', '.xls', '.csv', '.jpg', '.jpeg', '.png']}
                description="从项目空间选择库存表文件（支持Excel或图片格式）"
                visible={spaceSelectorVisible}
                onCancel={() => setSpaceSelectorVisible(false)}
              />
            </div>
          )}

          <div style={{ marginTop: 16, textAlign: 'center' }}>
            <Space>
              <Button
                type="primary"
                onClick={handleUpload}
                disabled={
                  !selectedWarehouse || 
                  (uploadMethod === 'direct' && fileList.length === 0) ||
                  (uploadMethod === 'space' && !selectedSpaceFile)
                }
                loading={uploading}
              >
                智能上传
              </Button>
              <Button onClick={downloadTemplate}>
                下载模板
              </Button>
            </Space>
          </div>
        </Card>

        {/* 预览数据模态框 */}
        <Modal
          title="导入预览"
          open={previewVisible}
          onOk={confirmImport}
          onCancel={cancelImport}
          width={1000}
          okText="确认导入"
          cancelText="取消导入"
        >
          <div style={{ marginBottom: 16 }}>
            <Row gutter={16}>
              <Col span={6}>
                <Statistic
                  title="总记录数"
                  value={importStats.total}
                />
              </Col>
              <Col span={6}>
                <Statistic
                  title="新增商品"
                  value={importStats.new_items}
                  valueStyle={{ color: '#1890ff' }}
                />
              </Col>
              <Col span={6}>
                <Statistic
                  title="更新商品"
                  value={importStats.updated_items}
                  valueStyle={{ color: '#52c41a' }}
                />
              </Col>
              <Col span={6}>
                <Statistic
                  title="错误记录"
                  value={importStats.errors}
                  valueStyle={{ color: '#ff4d4f' }}
                />
              </Col>
            </Row>
          </div>

          <Table
            columns={previewColumns}
            dataSource={previewData}
            rowKey={(record, index) => index}
            pagination={{ pageSize: 5 }}
            scroll={{ y: 300 }}
          />
        </Modal>
      </div>
    );
  };

  // 主界面渲染
  return (
    <div className="inventory-management-container">
      <Card>
        <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between' }}>
          <Title level={4} style={{ margin: 0 }}>库存管理</Title>
          <Space>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => setIsModalVisible(true)}
              disabled={!selectedWarehouse}
            >
              添加商品
            </Button>
            <Button
              icon={<DownloadOutlined />}
              onClick={downloadTemplate}
            >
              下载模板
            </Button>
          </Space>
        </div>

        <Tabs activeKey={activeTab} onChange={(key) => setActiveTab(key)}>
          <TabPane tab="库存管理" key="1">
            {renderManagementContent()}
          </TabPane>
          <TabPane tab="智能上传库存表" key="2">
            {renderUploadContent()}
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default InventoryManagement;