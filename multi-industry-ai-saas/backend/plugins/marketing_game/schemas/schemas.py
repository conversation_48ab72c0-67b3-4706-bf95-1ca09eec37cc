from typing import List, Optional, Dict, Any, Union
from pydantic import BaseModel, Field, validator
from datetime import datetime
from ..models.models import GameType, GameStatus, PrizeType, MarketingType
import uuid
from enum import Enum

# 基础模型
class GamePrizeBase(BaseModel):
    name: str = Field(..., description="奖品名称")
    description: Optional[str] = Field(None, description="奖品描述")
    prize_type: PrizeType = Field(..., description="奖品类型")
    value: float = Field(0, description="奖品价值")
    quantity: int = Field(1, description="奖品数量")
    probability: float = Field(0, description="中奖概率")
    image_url: Optional[str] = Field(None, description="奖品图片")
    is_default: bool = Field(False, description="是否默认奖品")

class GamePrizeCreate(GamePrizeBase):
    pass

class GamePrizeUpdate(BaseModel):
    name: Optional[str] = Field(None, description="奖品名称")
    description: Optional[str] = Field(None, description="奖品描述")
    prize_type: Optional[PrizeType] = Field(None, description="奖品类型")
    value: Optional[float] = Field(None, description="奖品价值")
    quantity: Optional[int] = Field(None, description="奖品数量")
    probability: Optional[float] = Field(None, description="中奖概率")
    image_url: Optional[str] = Field(None, description="奖品图片")
    is_default: Optional[bool] = Field(None, description="是否默认奖品")

class GamePrizeResponse(GamePrizeBase):
    id: int
    game_id: int
    remaining: int
    created_at: datetime
    updated_at: datetime
    model_config = {"from_attributes": True}

class GameParticipantBase(BaseModel):
    user_id: Optional[int] = Field(None, description="用户ID")
    member_id: Optional[int] = Field(None, description="会员ID")
    name: Optional[str] = Field(None, description="参与者姓名")
    phone: Optional[str] = Field(None, description="参与者电话")
    is_fixed_winner: bool = Field(False, description="是否内定中奖")

class GameParticipantCreate(GameParticipantBase):
    pass

class GameParticipantResponse(GameParticipantBase):
    id: int
    game_id: int
    draws_count: int
    is_winner: bool
    total_consumption: Optional[float] = Field(0, description="总消费金额")
    last_draw_time: Optional[datetime] = Field(None, description="最后抽奖时间")
    created_at: datetime
    updated_at: datetime
    model_config = {"from_attributes": True}

class GameRecordBase(BaseModel):
    participant_id: int = Field(..., description="参与者ID")
    prize_id: Optional[int] = Field(None, description="奖品ID")
    receipt_id: Optional[str] = Field(None, description="小票ID")
    consumption_amount: float = Field(0, description="消费金额")
    is_win: bool = Field(False, description="是否中奖")
    remark: Optional[str] = Field(None, description="备注")

class GameRecordCreate(GameRecordBase):
    pass

class GameRecordResponse(GameRecordBase):
    id: int
    game_id: int
    draw_time: datetime
    claim_status: str
    claim_time: Optional[datetime]
    draw_method: Optional[str] = Field(None, description="抽奖方式")
    probability_used: Optional[float] = Field(None, description="使用的概率")
    created_at: datetime
    updated_at: datetime
    
    # 关联数据
    participant: Optional[Dict[str, Any]] = Field(None, description="参与者信息")
    prize: Optional[Dict[str, Any]] = Field(None, description="奖品信息")
    
    model_config = {"from_attributes": True}

class MarketingGameBase(BaseModel):
    name: str = Field(..., description="游戏名称")
    description: Optional[str] = Field(None, description="游戏描述")
    game_type: GameType = Field(..., description="游戏类型")
    marketing_type: MarketingType = Field(MarketingType.DAILY_ACTIVITY, description="营销类型")
    start_time: datetime = Field(..., description="开始时间")
    end_time: datetime = Field(..., description="结束时间")
    rules: Optional[str] = Field(None, description="游戏规则")
    min_consumption: float = Field(0, description="最低消费金额")
    max_draws_per_user: int = Field(1, description="每用户最大抽奖次数")
    settings: Optional[Dict[str, Any]] = Field(None, description="游戏设置")

class MarketingGameCreate(MarketingGameBase):
    status: GameStatus = Field(GameStatus.DRAFT, description="游戏状态")

    @validator('start_time', 'end_time', pre=True, always=True)
    def remove_tz(cls, v):
        if v and hasattr(v, 'tzinfo') and v.tzinfo is not None:
            return v.replace(tzinfo=None)
        return v

class MarketingGameUpdate(BaseModel):
    name: Optional[str] = Field(None, description="游戏名称")
    description: Optional[str] = Field(None, description="游戏描述")
    game_type: Optional[GameType] = Field(None, description="游戏类型")
    start_time: Optional[datetime] = Field(None, description="开始时间")
    end_time: Optional[datetime] = Field(None, description="结束时间")
    status: Optional[GameStatus] = Field(None, description="游戏状态")
    rules: Optional[str] = Field(None, description="游戏规则")
    min_consumption: Optional[float] = Field(None, description="最低消费金额")
    max_draws_per_user: Optional[int] = Field(None, description="每用户最大抽奖次数")
    settings: Optional[Dict[str, Any]] = Field(None, description="游戏设置")

    @validator('start_time', 'end_time', pre=True, always=True)
    def remove_tz(cls, v):
        if v and hasattr(v, 'tzinfo') and v.tzinfo is not None:
            return v.replace(tzinfo=None)
        return v

class MarketingGameResponse(MarketingGameBase):
    id: int
    project_id: uuid.UUID
    status: GameStatus
    created_at: datetime
    updated_at: datetime
    prizes: List[GamePrizeResponse] = Field(default_factory=list)
    model_config = {"from_attributes": True, "arbitrary_types_allowed": True}

    @validator('prizes', pre=True, always=True)
    def handle_prizes(cls, v):
        # 如果 prizes 属性不存在或为None，返回空列表
        if v is None:
            return []

        # 如果是列表，直接返回
        if isinstance(v, list):
            return v

        # 如果是SQLAlchemy关系属性且已加载，转换为列表
        if hasattr(v, 'all'):
            try:
                return list(v)
            except Exception:
                return []

        # 其他情况返回空列表
        return []

# 小票识别响应
class ReceiptRecognitionResponse(BaseModel):
    success: bool = Field(..., description="是否成功")
    total_amount: Optional[float] = Field(None, description="消费总金额")
    member_id: Optional[str] = Field(None, description="会员ID")
    store_name: Optional[str] = Field(None, description="门店名称")
    transaction_time: Optional[datetime] = Field(None, description="交易时间")
    receipt_id: Optional[str] = Field(None, description="小票ID")
    items: Optional[List[Dict[str, Any]]] = Field(None, description="商品列表")
    raw_text: Optional[str] = Field(None, description="原始文本")
    error_message: Optional[str] = Field(None, description="错误信息")
