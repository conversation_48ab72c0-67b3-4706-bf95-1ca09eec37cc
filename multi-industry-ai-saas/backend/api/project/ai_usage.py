#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
项目级 AI 使用记录 API
"""

import logging
import uuid
from typing import Optional
from datetime import datetime, timedelta, timezone
from fastapi import APIRouter, Depends, HTTPException, Query, Path, status
from sqlalchemy.ext.asyncio import AsyncSession

from db.database import get_db
from models.project import Project
from core.auth import get_current_user
from api.deps import get_current_project
from services.ai import AIUsageService
from schemas.ai import (
    AIUsageResponse,
    AIUsageListResponse,
    AIUsageStatisticsResponse,
    AIUsageSummaryResponse,
)

logger = logging.getLogger(__name__)
router = APIRouter()

@router.get("/ai/usage", response_model=AIUsageListResponse)
async def list_usage(
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db),
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    user_id: Optional[uuid.UUID] = Query(None),
    config_id: Optional[uuid.UUID] = Query(None),
    model_id: Optional[uuid.UUID] = Query(None),
    request_type: Optional[str] = Query(None),
    status: Optional[str] = Query(None),
    start_date: Optional[datetime] = Query(None),
    end_date: Optional[datetime] = Query(None),
):
    """
    获取 AI 使用记录列表
    """
    try:
        usages, total = await AIUsageService.get_usages(
            db=db,
            skip=skip,
            limit=limit,
            project_id=project.id,
            user_id=user_id,
            config_id=config_id,
            model_id=model_id,
            request_type=request_type,
            status=status,
            start_date=start_date,
            end_date=end_date,
        )

        return {
            "success": True,
            "message": "获取 AI 使用记录列表成功",
            "data": usages,
            "total": total,
            "page": skip // limit + 1,
            "page_size": limit,
        }
    except Exception as e:
        logger.error(f"获取 AI 使用记录列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取 AI 使用记录列表失败: {str(e)}",
        )

@router.get("/ai/usage/stats", response_model=AIUsageSummaryResponse)
async def get_usage_stats(
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db),
):
    """
    获取 AI 使用统计摘要
    """
    try:
        print(f"[DEBUG] 调用 get_usage_summary 方法，project_id: {project.id}")
        logger.info(f"调用 get_usage_summary 方法，project_id: {project.id}")
        # 调用get_usage_summary方法获取摘要统计
        stats = await AIUsageService.get_usage_summary(
            db=db,
            project_id=project.id,
        )
        print(f"[DEBUG] get_usage_summary 返回数据: {stats}")
        logger.info(f"get_usage_summary 返回数据: {stats}")

        return {
            "success": True,
            "message": "获取 AI 使用统计摘要成功",
            "data": stats,
        }
    except Exception as e:
        logger.error(f"获取 AI 使用统计摘要失败: {e}")
        # 如果get_usage_summary失败，尝试使用get_usage_statistics作为备用
        try:
            logger.info(f"尝试使用 get_usage_statistics 作为备用方法")
            stats = await AIUsageService.get_usage_statistics(
                db=db,
                project_id=project.id,
            )
            logger.info(f"get_usage_statistics 返回数据: {stats}")
            return {
                "success": True,
                "message": "获取 AI 使用统计摘要成功",
                "data": stats,
            }
        except Exception as e2:
            logger.error(f"备用方法也失败: {e2}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"获取 AI 使用统计摘要失败: {str(e)}",
            )

@router.get("/ai/usage/configs/{config_id}/stats", response_model=AIUsageStatisticsResponse)
async def get_config_usage_stats(
    config_id: uuid.UUID = Path(...),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db),
    start_date: Optional[datetime] = Query(None),
    end_date: Optional[datetime] = Query(None),
):
    """
    获取指定配置的使用统计
    """
    try:
        # 如果未指定开始日期，则默认为过去 30 天
        if not start_date:
            start_date = datetime.now(timezone.utc) - timedelta(days=30)

        # 如果未指定结束日期，则默认为当前时间
        if not end_date:
            end_date = datetime.now(timezone.utc)

        # 获取配置的使用统计
        stats = await AIUsageService.get_config_usage_statistics(
            db=db,
            project_id=project.id,
            config_id=config_id,
            start_date=start_date,
            end_date=end_date,
        )

        return {
            "success": True,
            "message": "获取配置使用统计成功",
            "data": stats,
        }
    except Exception as e:
        logger.error(f"获取配置使用统计失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取配置使用统计失败: {str(e)}",
        )

@router.get("/ai/usage/{usage_id}", response_model=AIUsageResponse)
async def get_usage(
    usage_id: uuid.UUID = Path(...),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db),
):
    """
    获取 AI 使用记录详情
    """
    try:
        usage = await AIUsageService.get_usage(db=db, usage_id=usage_id)

        if not usage:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"AI 使用记录 ID '{usage_id}' 不存在",
            )

        # 检查是否有权限访问该使用记录
        if usage.project_id != project.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问该 AI 使用记录",
            )

        return {
            "success": True,
            "message": "获取 AI 使用记录详情成功",
            "data": usage,
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取 AI 使用记录详情失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取 AI 使用记录详情失败: {str(e)}",
        )
