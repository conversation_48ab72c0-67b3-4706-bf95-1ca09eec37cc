#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
客服消息 API
"""

import logging
import uuid
from typing import Dict, Any, Optional, List
from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException, status, Query, Path
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel

from api.deps import get_db
from core.auth import get_current_user
from api.deps import get_current_project
from models.user import User
from models.project import Project

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter()

# 响应模型
class MessageResponse(BaseModel):
    id: str
    session_id: str
    message_type: str
    content: str
    attachments: List[Dict[str, Any]]
    is_from_user: bool
    sender_name: Optional[str]
    ai_model_used: Optional[str]
    processing_time: Optional[float]
    confidence_score: Optional[float]
    knowledge_sources: List[Dict[str, Any]]
    rag_used: bool
    created_at: datetime

class MessageListResponse(BaseModel):
    items: List[MessageResponse]
    total: int
    page: int
    size: int
    pages: int

@router.get(
    "/session/{session_id}",
    response_model=MessageListResponse,
    summary="获取会话消息",
    description="获取指定会话的消息列表"
)
async def get_session_messages(
    session_id: str = Path(..., description="会话ID"),
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(50, ge=1, le=200, description="每页大小"),
    message_type: Optional[str] = Query(None, description="消息类型过滤"),
    is_from_user: Optional[bool] = Query(None, description="是否用户消息"),
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """获取会话消息"""
    try:
        # 这里需要实现实际的数据库查询
        # 暂时返回模拟数据
        
        messages = []
        for i in range(min(size, 20)):  # 模拟数据
            messages.append(MessageResponse(
                id=str(uuid.uuid4()),
                session_id=session_id,
                message_type="text",
                content=f"这是第{i+1}条消息内容",
                attachments=[],
                is_from_user=i % 2 == 0,
                sender_name="用户" if i % 2 == 0 else "AI助手",
                ai_model_used="gpt-4" if i % 2 == 1 else None,
                processing_time=1.2 if i % 2 == 1 else None,
                confidence_score=0.85 if i % 2 == 1 else None,
                knowledge_sources=[],
                rag_used=False,
                created_at=datetime.utcnow()
            ))
        
        total = 100  # 模拟总数
        pages = (total + size - 1) // size
        
        return MessageListResponse(
            items=messages,
            total=total,
            page=page,
            size=size,
            pages=pages
        )
        
    except Exception as e:
        logger.error(f"获取会话消息失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取会话消息失败: {str(e)}"
        )

@router.get(
    "/{message_id}",
    response_model=MessageResponse,
    summary="获取消息详情",
    description="获取指定消息的详细信息"
)
async def get_message(
    message_id: str = Path(..., description="消息ID"),
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """获取消息详情"""
    try:
        # 这里需要实现实际的数据库查询
        # 暂时返回模拟数据
        
        return MessageResponse(
            id=message_id,
            session_id="session_123",
            message_type="text",
            content="这是一条详细的消息内容",
            attachments=[],
            is_from_user=False,
            sender_name="AI助手",
            ai_model_used="gpt-4",
            processing_time=1.5,
            confidence_score=0.92,
            knowledge_sources=[
                {
                    "knowledge_base_id": "kb_123",
                    "document_title": "产品说明书",
                    "similarity": 0.85
                }
            ],
            rag_used=True,
            created_at=datetime.utcnow()
        )
        
    except Exception as e:
        logger.error(f"获取消息详情失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取消息详情失败: {str(e)}"
        )
