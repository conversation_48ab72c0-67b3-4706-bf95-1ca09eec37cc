import React, { useState, useEffect, useRef } from 'react';
import {
  Card,
  Table,
  Button,
  Input,
  Space,
  Tabs,
  message,
  Modal,
  Form,
  Row,
  Col,
  Select,
  InputNumber,
  Upload,
  Typography,
  Tooltip,
  Popconfirm,
  Tag,
  Divider,
  Spin,
  Switch,
  Breadcrumb,
  Dropdown,
  Menu,
  Radio
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  ReloadOutlined,
  EditOutlined,
  DeleteOutlined,
  ExportOutlined,
  ImportOutlined,
  EyeOutlined,
  TagOutlined,
  AppstoreOutlined,
  UploadOutlined,
  DownloadOutlined,
  QuestionCircleOutlined
} from '@ant-design/icons';
import productAPI from '../../../services/api/project/product';
import { useNavigate } from 'react-router-dom';
import { useProject } from '../../../contexts/ProjectContext';

const { Title, Text } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;

const ProductManagement = () => {
  const { currentProject } = useProject();
  const [loading, setLoading] = useState(false);
  const [products, setProducts] = useState([]);
  const [brands, setBrands] = useState([]);
  const [categories, setCategories] = useState([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total) => `共 ${total} 条`
  });
  const [total, setTotal] = useState(0);
  const [searchText, setSearchText] = useState('');
  const [activeTab, setActiveTab] = useState('products');
  const [modalVisible, setModalVisible] = useState(false);
  const [brandModalVisible, setBrandModalVisible] = useState(false);
  const [categoryModalVisible, setCategoryModalVisible] = useState(false);
  const [editingProduct, setEditingProduct] = useState(null);
  const [editingBrand, setEditingBrand] = useState(null);
  const [editingCategory, setEditingCategory] = useState(null);
  const [submitting, setSubmitting] = useState(false);
  const [importModalVisible, setImportModalVisible] = useState(false);
  const [importFile, setImportFile] = useState(null);
  const [importing, setImporting] = useState(false);
  const [mergeModalVisible, setMergeModalVisible] = useState(false);
  const [mergeSuggestions, setMergeSuggestions] = useState([]);
  const [loadingMerge, setLoadingMerge] = useState(false);
  const [variantModalVisible, setVariantModalVisible] = useState(false);
  const [variantsModalVisible, setVariantsModalVisible] = useState(false);
  const [variants, setVariants] = useState([]);
  const [baseProduct, setBaseProduct] = useState(null);
  const [currentProductGroupId, setCurrentProductGroupId] = useState(null);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [selectedProducts, setSelectedProducts] = useState([]);

  const [form] = Form.useForm();
  const [brandForm] = Form.useForm();
  const [categoryForm] = Form.useForm();
  const [variantForm] = Form.useForm();

  const navigate = useNavigate();
  const searchInputRef = useRef(null);

  // 初始化数据
  useEffect(() => {
    if (currentProject?.id) {
      fetchProducts();
      fetchBrands();
      fetchCategories();
    }
  }, [currentProject?.id]);

  // 监听搜索和分页变化
  useEffect(() => {
    if (currentProject?.id) {
      fetchProducts();
    }
  }, [searchText, pagination.current, pagination.pageSize, currentProject?.id]);

  // 检查项目是否加载
  if (!currentProject) {
    return <Spin size="large" style={{ display: 'flex', justifyContent: 'center', marginTop: '20%' }} />;
  }

  // 获取产品列表
  const fetchProducts = async () => {
    if (!currentProject?.id) return;
    
    setLoading(true);
    try {
      const response = await productAPI.getProducts(currentProject.id, {
        page: pagination.current,
        page_size: pagination.pageSize,
        search: searchText
      });

      if (response && response.success) {
        setProducts(response.data || []);
        setTotal(response.total || 0);
      } else {
        message.error(response?.message || '获取产品列表失败');
      }
    } catch (error) {
      console.error('获取产品失败:', error);
      message.error('获取产品列表失败: ' + (error.response?.data?.detail || error.message || '服务器错误'));
    } finally {
      setLoading(false);
    }
  };

  // 获取品牌列表
  const fetchBrands = async () => {
    if (!currentProject?.id) return;
    
    try {
      const response = await productAPI.brands.list(currentProject.id);
      if (response && response.success) {
        setBrands(response.data || []);
      } else {
        message.error(response?.message || '获取品牌列表失败');
      }
    } catch (error) {
      console.error('获取品牌失败:', error);
      message.error('获取品牌列表失败: ' + (error.response?.data?.detail || error.message || '服务器错误'));
    }
  };

  // 获取分类列表
  const fetchCategories = async () => {
    if (!currentProject?.id) return;
    
    try {
      const response = await productAPI.categories.list(currentProject.id);
      if (response && response.success) {
        setCategories(response.data || []);
      } else {
        message.error(response?.message || '获取分类列表失败');
      }
    } catch (error) {
      console.error('获取分类失败:', error);
      message.error('获取分类列表失败: ' + (error.response?.data?.detail || error.message || '服务器错误'));
    }
  };

  // 处理表格分页变化
  const handleTableChange = (pagination) => {
    setPagination(pagination);
  };

  // 处理行选择变化
  const handleRowSelectionChange = (selectedRowKeys, selectedRows) => {
    setSelectedRowKeys(selectedRowKeys);
    setSelectedProducts(selectedRows);
  };

  // 处理搜索
  const handleSearch = (value) => {
    setSearchText(value);
    setPagination({ ...pagination, current: 1 });
  };

  // 打开新增产品模态框
  const handleAddProduct = () => {
    setEditingProduct(null);
    form.resetFields();
    form.setFieldsValue({ unit: '公斤' });
    setModalVisible(true);
  };

  // 打开编辑产品模态框
  const handleEditProduct = (record) => {
    setEditingProduct(record);
    form.setFieldsValue({
      name: record.name,
      sku: record.sku,
      brand_id: record.brand_id,
      category_id: record.category_id,
      description: record.description,
      cost_price: record.cost_price,
      retail_price: record.retail_price,
      specification: record.specification,
      unit: record.unit || '公斤',
    });
    setModalVisible(true);
  };

  // 处理删除产品
  const handleDeleteProduct = async (id) => {
    if (!currentProject?.id) return;
    
    try {
      const response = await productAPI.deleteProduct(currentProject.id, id);
      if (response && response.success) {
        message.success('产品删除成功');
        fetchProducts();
      } else {
        message.error(response?.message || '删除产品失败');
      }
    } catch (error) {
      console.error('删除产品失败:', error);
      message.error('删除产品失败: ' + (error.response?.data?.detail || error.message || '服务器错误'));
    }
  };

  // 处理批量删除产品
  const handleBatchDeleteProducts = async () => {
    if (!currentProject?.id || selectedRowKeys.length === 0) return;
    
    try {
      const response = await productAPI.batchDeleteProducts(currentProject.id, selectedRowKeys);
      if (response && response.success) {
        message.success(response.message || '批量删除成功');
        setSelectedRowKeys([]);
        setSelectedProducts([]);
        fetchProducts();
      } else {
        message.error(response?.message || '批量删除失败');
      }
    } catch (error) {
      console.error('批量删除产品失败:', error);
      message.error('批量删除产品失败: ' + (error.response?.data?.detail || error.message || '服务器错误'));
    }
  };

  // 处理表单提交
  const handleFormSubmit = async () => {
    if (!currentProject?.id) return;
    
    try {
      const values = await form.validateFields();
      setSubmitting(true);

      let response;
      
      // 检查是否是基于现有商品创建变体
      if (values.is_variant_creation === 'variant' && values.base_product_id) {
        // 创建变体
        response = await productAPI.createVariant(currentProject.id, {
          base_product_id: values.base_product_id,
          specification: values.specification,
          unit: values.unit,
          cost_price: values.cost_price,
          retail_price: values.retail_price
        });
      } else if (editingProduct) {
        // 编辑产品
        response = await productAPI.updateProduct(currentProject.id, editingProduct.id, values);
      } else {
        // 创建新产品
        response = await productAPI.createProduct(currentProject.id, values);
      }

      if (response && response.success) {
        message.success(
          values.is_variant_creation === 'variant' 
            ? '变体创建成功' 
            : editingProduct 
              ? '产品更新成功' 
              : '产品创建成功'
        );
        setModalVisible(false);
        form.resetFields();
        setEditingProduct(null);
        fetchProducts();
      } else {
        message.error(response?.message || '操作失败');
      }
    } catch (error) {
      console.error('表单提交失败:', error);
      message.error('操作失败: ' + (error.response?.data?.detail || error.message || '服务器错误'));
    } finally {
      setSubmitting(false);
    }
  };

  // 打开新增品牌模态框
  const handleAddBrand = () => {
    setEditingBrand(null);
    brandForm.resetFields();
    setBrandModalVisible(true);
  };

  // 打开编辑品牌模态框
  const handleEditBrand = (record) => {
    setEditingBrand(record);
    brandForm.setFieldsValue({
      name: record.name,
      description: record.description
    });
    setBrandModalVisible(true);
  };

  // 处理删除品牌
  const handleDeleteBrand = async (id) => {
    if (!currentProject?.id) return;
    
    try {
      const response = await productAPI.brands.delete(currentProject.id, id);
      if (response && response.success) {
        message.success('品牌删除成功');
        fetchBrands();
      } else {
        message.error(response?.message || '删除品牌失败');
      }
    } catch (error) {
      console.error('删除品牌失败:', error);
      message.error('删除品牌失败: ' + (error.response?.data?.detail || error.message || '服务器错误'));
    }
  };

  // 处理品牌表单提交
  const handleBrandFormSubmit = async () => {
    if (!currentProject?.id) return;
    
    try {
      const values = await brandForm.validateFields();
      setSubmitting(true);

      if (editingBrand) {
        // 更新品牌
        const response = await productAPI.brands.update(currentProject.id, editingBrand.id, values);

        if (response && response.success) {
          message.success('品牌更新成功');
          setBrandModalVisible(false);
          fetchBrands();
        } else {
          message.error(response?.message || '更新品牌失败');
        }
      } else {
        // 创建品牌
        const response = await productAPI.brands.create(currentProject.id, values);

        if (response && response.success) {
          message.success('品牌创建成功');
          setBrandModalVisible(false);
          fetchBrands();
        } else {
          message.error(response?.message || '创建品牌失败');
        }
      }
    } catch (error) {
      console.error('提交品牌失败:', error);
      message.error('提交品牌失败: ' + (error.response?.data?.detail || error.message || '服务器错误'));
    } finally {
      setSubmitting(false);
    }
  };

  // 打开新增分类模态框
  const handleAddCategory = () => {
    setEditingCategory(null);
    categoryForm.resetFields();
    setCategoryModalVisible(true);
  };

  // 打开编辑分类模态框
  const handleEditCategory = (record) => {
    setEditingCategory(record);
    categoryForm.setFieldsValue({
      name: record.name,
      parent_id: record.parent_id,
      description: record.description
    });
    setCategoryModalVisible(true);
  };

  // 处理删除分类
  const handleDeleteCategory = async (id) => {
    if (!currentProject?.id) return;
    
    try {
      const response = await productAPI.categories.delete(currentProject.id, id);
      if (response && response.success) {
        message.success('分类删除成功');
        fetchCategories();
      } else {
        message.error(response?.message || '删除分类失败');
      }
    } catch (error) {
      console.error('删除分类失败:', error);
      message.error('删除分类失败: ' + (error.response?.data?.detail || error.message || '服务器错误'));
    }
  };

  // 处理分类表单提交
  const handleCategoryFormSubmit = async () => {
    if (!currentProject?.id) return;
    
    try {
      const values = await categoryForm.validateFields();
      setSubmitting(true);

      if (editingCategory) {
        // 更新分类
        const response = await productAPI.categories.update(currentProject.id, editingCategory.id, values);

        if (response && response.success) {
          message.success('分类更新成功');
          setCategoryModalVisible(false);
          fetchCategories();
        } else {
          message.error(response?.message || '更新分类失败');
        }
      } else {
        // 创建分类
        const response = await productAPI.categories.create(currentProject.id, values);

        if (response && response.success) {
          message.success('分类创建成功');
          setCategoryModalVisible(false);
          fetchCategories();
        } else {
          message.error(response?.message || '创建分类失败');
        }
      }
    } catch (error) {
      console.error('提交分类失败:', error);
      message.error('提交分类失败: ' + (error.response?.data?.detail || error.message || '服务器错误'));
    } finally {
      setSubmitting(false);
    }
  };

  // 处理导入
  const handleImport = async () => {
    if (!currentProject?.id || !importFile) return;
    
    try {
      setImporting(true);

      const response = await productAPI.batchImport(currentProject.id, importFile);

      if (response && response.success) {
        message.success('产品导入成功');
        setImportModalVisible(false);
        setImportFile(null);
        fetchProducts();
      } else {
        message.error(response?.message || '导入产品失败');
      }
    } catch (error) {
      console.error('导入产品失败:', error);
      message.error('导入产品失败: ' + (error.response?.data?.detail || error.message || '服务器错误'));
    } finally {
      setImporting(false);
    }
  };

  // 处理导出
  const handleExport = async () => {
    if (!currentProject?.id) return;
    
    try {
      const response = await productAPI.exportProducts(currentProject.id);

      if (response && response.success) {
        message.success('产品导出成功');
      } else {
        message.error(response?.message || '导出产品失败');
      }
    } catch (error) {
      console.error('导出产品失败:', error);
      message.error('导出产品失败: ' + (error.response?.data?.detail || error.message || '服务器错误'));
    }
  };

  // 查看产品详情
  const handleViewProduct = (productId) => {
    navigate(`/project/purchase/products/${productId}`);
  };

  // 打开合并建议模态框
  const handleShowMergeSuggestions = () => {
    setMergeModalVisible(true);
    fetchMergeSuggestions();
  };

  // 获取合并建议
  const fetchMergeSuggestions = async () => {
    if (!currentProject?.id) return;
    
    setLoadingMerge(true);
    try {
      const response = await productAPI.getMergeSuggestions(currentProject.id);
      if (response && response.success) {
        setMergeSuggestions(response.data || []);
        setMergeModalVisible(true);
      } else {
        message.error(response?.message || '获取合并建议失败');
      }
    } catch (error) {
      console.error('获取合并建议失败:', error);
      message.error('获取合并建议失败: ' + (error.response?.data?.detail || error.message || '服务器错误'));
    } finally {
      setLoadingMerge(false);
    }
  };

  // 处理合并产品
  const handleMergeProducts = async (primaryProductId, mergeProductIds) => {
    if (!currentProject?.id) return;
    
    try {
      const response = await productAPI.mergeProducts(currentProject.id, {
        primary_product_id: primaryProductId,
        merge_product_ids: mergeProductIds
      });

      if (response && response.success) {
        message.success('产品合并成功');
        setMergeModalVisible(false);
        fetchProducts();
        fetchMergeSuggestions();
      } else {
        message.error(response?.message || '合并产品失败');
      }
    } catch (error) {
      console.error('合并产品失败:', error);
      message.error('合并产品失败: ' + (error.response?.data?.detail || error.message || '服务器错误'));
    }
  };

  // 查看产品变体
  const handleViewVariants = async (productGroupId) => {
    if (!currentProject?.id || !productGroupId) return;
    
    try {
      const response = await productAPI.getVariants(currentProject.id, productGroupId);
      if (response && response.success) {
        setVariants(response.data || []);
        setCurrentProductGroupId(productGroupId);
        setVariantsModalVisible(true);
      } else {
        message.error(response?.message || '获取变体失败');
      }
    } catch (error) {
      console.error('获取变体失败:', error);
      message.error('获取变体失败: ' + (error.response?.data?.detail || error.message || '服务器错误'));
    }
  };

  // 创建商品变体
  const handleCreateVariant = (baseProduct) => {
    setBaseProduct(baseProduct);
    variantForm.setFieldsValue({
      base_product_name: baseProduct.display_name || baseProduct.name,
      base_product_id: baseProduct.id,
      cost_price: baseProduct.cost_price,
      retail_price: baseProduct.retail_price,
      unit: baseProduct.unit
    });
    setVariantModalVisible(true);
  };

  // 提交变体表单
  const handleVariantFormSubmit = async () => {
    try {
      const values = await variantForm.validateFields();
      setSubmitting(true);

      const response = await productAPI.createVariant(currentProject.id, {
        base_product_id: baseProduct.id,
        specification: values.specification,
        unit: values.unit,
        cost_price: values.cost_price,
        retail_price: values.retail_price
      });

      if (response && response.success) {
        message.success('变体创建成功');
        setVariantModalVisible(false);
        variantForm.resetFields();
        fetchProducts();
      } else {
        message.error(response?.message || '创建变体失败');
      }
    } catch (error) {
      console.error('创建变体失败:', error);
      message.error('创建变体失败: ' + (error.response?.data?.detail || error.message || '服务器错误'));
    } finally {
      setSubmitting(false);
    }
  };

  // 产品列表列定义
  const columns = [
    {
      title: '产品名称',
      dataIndex: 'name',
      key: 'name',
      ellipsis: true,
      width: 200,
      render: (text, record) => (
        <div>
          <a onClick={() => handleViewProduct(record.id)}>
            {record.display_name || text}
          </a>
          {record.is_variant && (
            <Tag color="blue" size="small" style={{ marginLeft: 8 }}>
              多规格
            </Tag>
          )}
        </div>
      )
    },
    {
      title: 'SKU',
      dataIndex: 'sku',
      key: 'sku',
      width: 120
    },
    {
      title: '规格',
      dataIndex: 'specification',
      key: 'specification',
      width: 100,
      render: (text) => text || '-'
    },
    {
      title: '品牌',
      dataIndex: 'brand_name',
      key: 'brand_name',
      width: 100,
      ellipsis: true
    },
    {
      title: '分类',
      dataIndex: 'category_name',
      key: 'category_name',
      width: 100,
      ellipsis: true
    },
    {
      title: '成本价(¥)',
      dataIndex: 'cost_price',
      key: 'cost_price',
      width: 100,
      render: (text) => text ? `¥${parseFloat(text).toFixed(2)}` : '-'
    },
    {
      title: '零售价(¥)',
      dataIndex: 'retail_price',
      key: 'retail_price',
      width: 100,
      render: (text) => text ? `¥${parseFloat(text).toFixed(2)}` : '-'
    },
    {
      title: '操作',
      key: 'action',
      fixed: 'right',
      width: 200,
      render: (_, record) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            onClick={() => handleViewProduct(record.id)}
          >
            查看
          </Button>
          <Button
            type="link"
            size="small"
            onClick={() => handleEditProduct(record)}
          >
            编辑
          </Button>
          {record.product_group_id && (
            <Button
              type="link"
              size="small"
              onClick={() => handleViewVariants(record.product_group_id)}
            >
              查看变体
            </Button>
          )}
          <Button
            type="link"
            size="small"
            onClick={() => handleCreateVariant(record)}
          >
            创建变体
          </Button>
          <Popconfirm
            title="确定要删除此产品吗？"
            onConfirm={() => handleDeleteProduct(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="link"
              size="small"
              danger
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      )
    },
  ];

  // 品牌列表列定义
  const brandColumns = [
    {
      title: '品牌名称',
      dataIndex: 'name',
      key: 'name'
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 180,
      render: (text) => text ? new Date(text).toLocaleString() : '-'
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_, record) => (
        <Space size="small">
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => handleEditBrand(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除此品牌吗？"
            onConfirm={() => handleDeleteBrand(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 分类列表列定义
  const categoryColumns = [
    {
      title: '分类名称',
      dataIndex: 'name',
      key: 'name'
    },
    {
      title: '上级分类',
      dataIndex: 'parent_name',
      key: 'parent_name',
      render: (text) => text || '无'
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 180,
      render: (text) => text ? new Date(text).toLocaleString() : '-'
    },
    {
      title: '操作',
      key: 'action',
      width: 150,
      render: (_, record) => (
        <Space size="small">
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => handleEditCategory(record)}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定要删除此分类吗？"
            onConfirm={() => handleDeleteCategory(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div className="product-management-page">
      <Card>
        <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 16 }}>
          <Title level={4}>产品管理</Title>
          <Space>
            <Input
              placeholder="搜索产品名称或SKU"
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              onPressEnter={() => handleSearch(searchText)}
              style={{ width: 200 }}
              prefix={<SearchOutlined />}
              allowClear
              ref={searchInputRef}
            />
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAddProduct}
            >
              新增产品
            </Button>
            <Button
              icon={<ImportOutlined />}
              onClick={() => setImportModalVisible(true)}
            >
              导入
            </Button>
            <Button
              icon={<ExportOutlined />}
              onClick={handleExport}
            >
              导出
            </Button>
            <Button
              icon={<TagOutlined />}
              onClick={handleShowMergeSuggestions}
            >
              智能合并
            </Button>
            <Button
              icon={<ReloadOutlined />}
              onClick={() => {
                setSearchText('');
                setPagination({ ...pagination, current: 1 });
                fetchProducts();
              }}
            >
              刷新
            </Button>
            {selectedRowKeys.length > 0 && (
              <Popconfirm
                title={`确定要删除这 ${selectedRowKeys.length} 个产品吗？`}
                description={
                  <div>
                    <div>所选产品：</div>
                    <div style={{ maxHeight: '120px', overflow: 'auto', marginTop: '8px' }}>
                      {selectedProducts.map(product => (
                        <div key={product.id}>• {product.display_name || product.name}</div>
                      ))}
                    </div>
                    <div style={{ marginTop: '8px', color: '#666' }}>
                      注意：有库存的产品将被标记为非活动状态，无库存的产品将被永久删除。
                    </div>
                  </div>
                }
                onConfirm={handleBatchDeleteProducts}
                okText="确认删除"
                cancelText="取消"
                okType="danger"
              >
                <Button
                  danger
                  icon={<DeleteOutlined />}
                >
                  批量删除 ({selectedRowKeys.length})
                </Button>
              </Popconfirm>
            )}
          </Space>
        </div>

        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane
            tab={<span><AppstoreOutlined />产品列表</span>}
            key="products"
          >
            <Table
              columns={columns}
              dataSource={products}
              rowKey="id"
              pagination={{
                ...pagination,
                total,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条`
              }}
              loading={loading}
              onChange={handleTableChange}
              scroll={{ x: 1200 }}
              rowSelection={{
                selectedRowKeys,
                onChange: handleRowSelectionChange,
                getCheckboxProps: (record) => ({
                  name: record.name,
                }),
              }}
            />
          </TabPane>

        <TabPane
          tab={<span><TagOutlined />品牌管理</span>}
          key="brands"
        >
          <Space style={{ marginBottom: 16 }}>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAddBrand}
            >
              新增品牌
            </Button>
            <Button
              icon={<ReloadOutlined />}
              onClick={fetchBrands}
            >
              刷新
            </Button>
          </Space>

          <Table
            columns={brandColumns}
            dataSource={brands}
            rowKey="id"
            pagination={{
              pageSize: 10,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total) => `共 ${total} 条`
            }}
          />
        </TabPane>

        <TabPane
          tab={<span><AppstoreOutlined />分类管理</span>}
          key="categories"
        >
          <Space style={{ marginBottom: 16 }}>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAddCategory}
            >
              新增分类
            </Button>
            <Button
              icon={<ReloadOutlined />}
              onClick={fetchCategories}
            >
              刷新
            </Button>
          </Space>

          <Table
            columns={categoryColumns}
            dataSource={categories}
            rowKey="id"
            pagination={{
              pageSize: 10,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total) => `共 ${total} 条`
            }}
          />
        </TabPane>
      </Tabs>
      </Card>

      {/* 产品编辑/新增模态框 */}
      <Modal
        title={editingProduct ? '编辑产品' : '添加产品'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={[
          <Button key="cancel" onClick={() => setModalVisible(false)}>
            取消
          </Button>,
          <Button
            key="submit"
            type="primary"
            loading={submitting}
            onClick={handleFormSubmit}
          >
            保存
          </Button>,
        ]}
        width={720}
      >
        <Form
          form={form}
          layout="vertical"
        >
          {/* 基于现有商品创建变体选项 */}
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name="is_variant_creation"
                label="创建方式"
                initialValue="new"
              >
                <Radio.Group onChange={(e) => {
                  const isVariant = e.target.value === 'variant';
                  if (isVariant) {
                    // 如果选择基于现有商品，清空某些字段
                    form.setFieldsValue({
                      name: '',
                      brand_id: undefined,
                      category_id: undefined,
                      cost_price: undefined,
                      retail_price: undefined,
                      unit: '',
                      description: ''
                    });
                  }
                }}>
                  <Radio value="new">新建商品</Radio>
                  <Radio value="variant">基于现有商品创建变体</Radio>
                </Radio.Group>
              </Form.Item>
            </Col>
          </Row>

          {/* 基于现有商品时显示商品选择器 */}
          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) => 
              prevValues.is_variant_creation !== currentValues.is_variant_creation
            }
          >
            {({ getFieldValue }) => {
              const isVariantCreation = getFieldValue('is_variant_creation') === 'variant';
              
              return isVariantCreation ? (
                <Row gutter={16}>
                  <Col span={24}>
                    <Form.Item
                      name="base_product_id"
                      label="选择基础商品"
                      rules={[{ required: true, message: '请选择基础商品' }]}
                    >
                      <Select 
                        placeholder="请选择要创建变体的基础商品"
                        showSearch
                        filterOption={(input, option) =>
                          (option?.children ?? '').toLowerCase().includes(input.toLowerCase())
                        }
                        onChange={(productId) => {
                          // 当选择基础商品时，自动填充相关信息
                          const baseProduct = products.find(p => p.id === productId);
                          if (baseProduct) {
                            form.setFieldsValue({
                              name: baseProduct.name,
                              brand_id: baseProduct.brand_id,
                              category_id: baseProduct.category_id,
                              cost_price: baseProduct.cost_price,
                              retail_price: baseProduct.retail_price,
                              unit: baseProduct.unit,
                              description: baseProduct.description
                            });
                          }
                        }}
                      >
                        {products.map(product => (
                          <Option key={product.id} value={product.id}>
                            {product.display_name || `${product.name}${product.specification ? ` (${product.specification})` : ''}`}
                          </Option>
                        ))}
                      </Select>
                    </Form.Item>
                  </Col>
                </Row>
              ) : null;
            }}
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="产品名称"
                rules={[{ required: true, message: '请输入产品名称' }]}
              >
                <Input placeholder="请输入产品名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="sku"
                label="SKU"
                rules={[{ required: true, message: '请输入产品SKU' }]}
              >
                <Input placeholder="请输入产品SKU" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="brand_id"
                label="品牌"
              >
                <Select placeholder="请选择品牌" allowClear>
                  {brands.map(brand => (
                    <Option key={brand.id} value={brand.id}>{brand.name}</Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="category_id"
                label="分类"
              >
                <Select placeholder="请选择分类" allowClear>
                  {categories.map(category => (
                    <Option key={category.id} value={category.id}>{category.name}</Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="cost_price"
                label="成本价"
              >
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="请输入成本价"
                  min={0}
                  precision={2}
                  prefix="¥"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="retail_price"
                label="零售价"
              >
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="请输入零售价"
                  min={0}
                  precision={2}
                  prefix="¥"
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="unit"
                label="单位"
                initialValue="公斤"
                rules={[{ required: true, message: '请输入产品单位' }]}
              >
                <Input placeholder="如：个、箱、瓶、公斤" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="specification"
                label="规格"
              >
                <Input placeholder="请输入产品规格" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="description"
            label="描述"
          >
            <Input.TextArea rows={4} placeholder="请输入产品描述" />
          </Form.Item>
        </Form>
      </Modal>

      {/* 品牌编辑/新增模态框 */}
      <Modal
        title={editingBrand ? '编辑品牌' : '添加品牌'}
        open={brandModalVisible}
        onCancel={() => setBrandModalVisible(false)}
        footer={[
          <Button key="cancel" onClick={() => setBrandModalVisible(false)}>
            取消
          </Button>,
          <Button
            key="submit"
            type="primary"
            loading={submitting}
            onClick={handleBrandFormSubmit}
          >
            保存
          </Button>,
        ]}
      >
        <Form
          form={brandForm}
          layout="vertical"
        >
          <Form.Item
            name="name"
            label="品牌名称"
            rules={[{ required: true, message: '请输入品牌名称' }]}
          >
            <Input placeholder="请输入品牌名称" />
          </Form.Item>

          <Form.Item
            name="description"
            label="描述"
          >
            <Input.TextArea rows={4} placeholder="请输入品牌描述" />
          </Form.Item>
        </Form>
      </Modal>

      {/* 分类编辑/新增模态框 */}
      <Modal
        title={editingCategory ? '编辑分类' : '添加分类'}
        open={categoryModalVisible}
        onCancel={() => setCategoryModalVisible(false)}
        footer={[
          <Button key="cancel" onClick={() => setCategoryModalVisible(false)}>
            取消
          </Button>,
          <Button
            key="submit"
            type="primary"
            loading={submitting}
            onClick={handleCategoryFormSubmit}
          >
            保存
          </Button>,
        ]}
      >
        <Form
          form={categoryForm}
          layout="vertical"
        >
          <Form.Item
            name="name"
            label="分类名称"
            rules={[{ required: true, message: '请输入分类名称' }]}
          >
            <Input placeholder="请输入分类名称" />
          </Form.Item>

          <Form.Item
            name="parent_id"
            label="上级分类"
          >
            <Select placeholder="请选择上级分类" allowClear>
              {categories.map(category => (
                <Option key={category.id} value={category.id}>{category.name}</Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="description"
            label="描述"
          >
            <Input.TextArea rows={4} placeholder="请输入分类描述" />
          </Form.Item>
        </Form>
      </Modal>

      {/* 批量导入模态框 */}
      <Modal
        title="批量导入产品"
        open={importModalVisible}
        onCancel={() => setImportModalVisible(false)}
        footer={[
          <Button key="cancel" onClick={() => setImportModalVisible(false)}>
            取消
          </Button>,
          <Button
            key="submit"
            type="primary"
            loading={importing}
            onClick={handleImport}
            disabled={!importFile}
          >
            导入
          </Button>,
        ]}
      >
        <div style={{ marginBottom: 16 }}>
          <Typography.Title level={5}>上传Excel文件</Typography.Title>
          <Typography.Paragraph>
            请上传符合模板格式的Excel文件，文件大小不超过10MB。
            <a href="#" style={{ marginLeft: 8 }}>下载模板</a>
          </Typography.Paragraph>
        </div>

        <Upload.Dragger
          name="file"
          accept=".xlsx,.xls"
          beforeUpload={(file) => {
            setImportFile(file);
            return false;
          }}
          onRemove={() => setImportFile(null)}
          fileList={importFile ? [importFile] : []}
        >
          <p className="ant-upload-drag-icon">
            <UploadOutlined />
          </p>
          <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
          <p className="ant-upload-hint">支持 .xlsx, .xls 格式</p>
        </Upload.Dragger>
      </Modal>

      {/* 商品合并建议模态框 */}
      <Modal
        title="智能商品合并建议"
        open={mergeModalVisible}
        onCancel={() => setMergeModalVisible(false)}
        footer={null}
        width={1000}
      >
        <div style={{ marginBottom: 16 }}>
          <Space>
            <Button
              type="primary"
              icon={<ReloadOutlined />}
              loading={loadingMerge}
              onClick={fetchMergeSuggestions}
            >
              刷新建议
            </Button>
            <Text type="secondary">
              系统会自动识别相似的商品并建议合并，以避免重复商品
            </Text>
          </Space>
        </div>

        {loadingMerge ? (
          <div style={{ textAlign: 'center', padding: '40px 0' }}>
            <Spin size="large" />
            <div style={{ marginTop: 16 }}>正在分析商品相似度...</div>
          </div>
        ) : mergeSuggestions.length === 0 ? (
          <div style={{ textAlign: 'center', padding: '40px 0' }}>
            <Text type="secondary">暂无相似商品需要合并</Text>
          </div>
        ) : (
          <div>
            {mergeSuggestions.map((suggestion, index) => (
              <Card
                key={suggestion.group_id}
                size="small"
                style={{ marginBottom: 16 }}
                title={
                  <Space>
                    <Tag color="blue">相似组 {index + 1}</Tag>
                    <Text strong>置信度: {(suggestion.confidence * 100).toFixed(1)}%</Text>
                  </Space>
                }
                extra={
                  <Popconfirm
                    title="确定要合并这些商品吗？"
                    description="合并后，被合并的商品将被标记为非活跃状态"
                    onConfirm={() => handleMergeProducts(
                      suggestion.primary_product.id,
                      suggestion.similar_products.map(p => p.id)
                    )}
                    okText="确定"
                    cancelText="取消"
                  >
                    <Button type="primary" size="small">
                      执行合并
                    </Button>
                  </Popconfirm>
                }
              >
                <Row gutter={16}>
                  <Col span={12}>
                    <div>
                      <Text strong style={{ color: '#1890ff' }}>主商品（保留）</Text>
                      <div style={{ marginTop: 8, padding: 12, backgroundColor: '#f0f8ff', borderRadius: 4 }}>
                        <div><Text strong>{suggestion.primary_product.name}</Text></div>
                        <div><Text type="secondary">SKU: {suggestion.primary_product.sku}</Text></div>
                        <div><Text type="secondary">规格: {suggestion.primary_product.specification || '无'}</Text></div>
                        <div><Text type="secondary">单位: {suggestion.primary_product.unit || '无'}</Text></div>
                      </div>
                    </div>
                  </Col>
                  <Col span={12}>
                    <div>
                      <Text strong style={{ color: '#ff7875' }}>相似商品（将被合并）</Text>
                      <div style={{ marginTop: 8 }}>
                        {suggestion.similar_products.map((product, idx) => (
                          <div
                            key={product.id}
                            style={{
                              marginBottom: 8,
                              padding: 12,
                              backgroundColor: '#fff2f0',
                              borderRadius: 4,
                              border: '1px solid #ffccc7'
                            }}
                          >
                            <div>
                              <Text strong>{product.name}</Text>
                              <Tag color="orange" style={{ marginLeft: 8 }}>
                                相似度: {(product.similarity * 100).toFixed(1)}%
                              </Tag>
                            </div>
                            <div><Text type="secondary">SKU: {product.sku}</Text></div>
                            <div><Text type="secondary">规格: {product.specification || '无'}</Text></div>
                            <div><Text type="secondary">单位: {product.unit || '无'}</Text></div>
                          </div>
                        ))}
                      </div>
                    </div>
                  </Col>
                </Row>
              </Card>
            ))}
          </div>
        )}
      </Modal>
      
      {/* 创建变体模态框 */}
      <Modal
        title="创建商品变体"
        open={variantModalVisible}
        onCancel={() => setVariantModalVisible(false)}
        footer={[
          <Button key="cancel" onClick={() => setVariantModalVisible(false)}>
            取消
          </Button>,
          <Button
            key="submit"
            type="primary"
            loading={submitting}
            onClick={handleVariantFormSubmit}
          >
            创建
          </Button>,
        ]}
      >
        <Form
          form={variantForm}
          layout="vertical"
        >
          <Form.Item
            name="base_product_name"
            label="基础商品"
          >
            <Input disabled />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="specification"
                label="规格说明"
                rules={[{ required: true, message: '请输入规格说明' }]}
              >
                <Input placeholder="如：500g、19kg、大号等" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="unit"
                label="单位"
                rules={[{ required: true, message: '请输入单位' }]}
              >
                <Input placeholder="如：袋、箱、个等" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="cost_price"
            label="成本价"
          >
            <InputNumber
              style={{ width: '100%' }}
              placeholder="请输入成本价"
              min={0}
              precision={2}
              prefix="¥"
            />
          </Form.Item>
        </Form>
      </Modal>

      {/* 查看变体模态框 */}
      <Modal
        title={`商品变体列表`}
        open={variantsModalVisible}
        onCancel={() => setVariantsModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setVariantsModalVisible(false)}>
            关闭
          </Button>
        ]}
        width={800}
      >
        <Table
          dataSource={variants}
          rowKey="id"
          pagination={false}
          columns={[
            {
              title: '显示名称',
              dataIndex: 'display_name',
              key: 'display_name',
              render: (text, record) => text || `${record.name} (${record.specification || '默认'})`
            },
            {
              title: 'SKU',
              dataIndex: 'sku',
              key: 'sku'
            },
            {
              title: '规格',
              dataIndex: 'specification',
              key: 'specification',
              render: (text) => text || '默认'
            },
            {
              title: '单位',
              dataIndex: 'unit',
              key: 'unit'
            },
            {
              title: '成本价',
              dataIndex: 'cost_price',
              key: 'cost_price',
              render: (text) => text ? `¥${parseFloat(text).toFixed(2)}` : '-'
            },
            {
              title: '零售价',
              dataIndex: 'retail_price',
              key: 'retail_price',
              render: (text) => text ? `¥${parseFloat(text).toFixed(2)}` : '-'
            },
            {
              title: '操作',
              key: 'action',
              render: (_, record) => (
                <Space size="small">
                  <Button
                    type="text"
                    size="small"
                    onClick={() => handleEditProduct(record)}
                  >
                    编辑
                  </Button>
                </Space>
              )
            }
          ]}
        />
      </Modal>
    </div>
  );
};

export default ProductManagement;
