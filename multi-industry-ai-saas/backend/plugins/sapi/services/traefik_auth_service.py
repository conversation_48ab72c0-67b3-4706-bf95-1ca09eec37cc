#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
SAPI Traefik鉴权微服务
用于Traefik ForwardAuth中间件，验证SAPI令牌并转换为系统令牌
"""

import os
import json
import logging
import asyncio
import time
import uuid
from typing import Dict, Any, Optional, List
from datetime import datetime, timedelta

import httpx
import redis.asyncio as redis
from fastapi import FastAPI, Request, HTTPException, Depends, Header
from fastapi.responses import JSONResponse
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, Field
from jose import jwt, JWTError

# 配置日志
logging.basicConfig(
    level=os.environ.get("LOG_LEVEL", "INFO").upper(),
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# 应用配置
app = FastAPI(
    title="SAPI Traefik鉴权微服务",
    description="为Traefik提供SAPI令牌验证和转换服务",
    version="1.0.0"
)

# CORS配置
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 配置常量
DATABASE_URL = os.environ.get("DATABASE_URL", "***********************************************************/retail_ai_saas")
REDIS_URL = os.environ.get("REDIS_URL", "redis://retail-ai-saas-redis:6379/0")
SECRET_KEY = os.environ.get("SECRET_KEY", "your-secret-key-here")
JWT_SECRET_KEY = os.environ.get("JWT_SECRET_KEY", "your-jwt-secret-key-here")
SAPI_SECRET_KEY = os.environ.get("SAPI_SECRET_KEY", "your-sapi-secret-key-here")
JWT_ALGORITHM = os.environ.get("JWT_ALGORITHM", "HS256")
JWT_EXPIRE_MINUTES = int(os.environ.get("JWT_EXPIRE_MINUTES", "30"))
SYSTEM_TOKEN_EXPIRE_HOURS = int(os.environ.get("SYSTEM_TOKEN_EXPIRE_HOURS", "24"))
CACHE_TTL = int(os.environ.get("CACHE_TTL", "300"))  # 5分钟缓存

# 全局变量
redis_client: Optional[redis.Redis] = None
session_cache: Dict[str, Dict[str, Any]] = {}

# 数据模型
class AuthRequest(BaseModel):
    """鉴权请求模型"""
    token: str
    server_id: Optional[str] = None
    project_id: Optional[str] = None

class AuthResponse(BaseModel):
    """鉴权响应模型"""
    success: bool
    user_id: Optional[str] = None
    project_id: Optional[str] = None
    system_token: Optional[str] = None
    permissions: List[str] = Field(default_factory=list)
    session_id: Optional[str] = None
    auth_type: str = "unknown"
    message: Optional[str] = None

class SessionCreateRequest(BaseModel):
    """会话创建请求"""
    sapi_token: str
    server_id: str
    project_id: Optional[str] = None

class SessionCreateResponse(BaseModel):
    """会话创建响应"""
    success: bool
    session_id: Optional[str] = None
    direct_access_token: Optional[str] = None
    expires_at: Optional[datetime] = None
    message: Optional[str] = None

# 工具函数
def sanitize_token(token: str, max_length: int = 20) -> str:
    """安全地截断令牌用于日志"""
    if not token:
        return "None"
    if len(token) <= max_length:
        return token[:max_length//2] + "..." + token[-max_length//2:]
    return token[:max_length//2] + "..." + token[-max_length//2:]

async def get_redis_client() -> redis.Redis:
    """获取Redis客户端"""
    global redis_client
    if redis_client is None:
        redis_client = redis.from_url(REDIS_URL, decode_responses=True)
    return redis_client

async def verify_sapi_token_and_convert(sapi_token: str, project_id: Optional[str] = None) -> Dict[str, Any]:
    """
    验证SAPI令牌并转换为系统令牌
    
    Args:
        sapi_token: SAPI令牌
        project_id: 项目ID（可选）
    
    Returns:
        包含用户信息和系统令牌的字典
    """
    try:
        # 构建API请求 - 使用MCP令牌验证端点
        # 注意：SAPI插件路由在项目级别，需要提供项目ID
        # 从令牌中提取项目ID，如果没有则使用默认项目ID
        project_id = None
        try:
            # 简单解码JWT令牌获取项目ID
            import base64
            parts = sapi_token.split('.')
            if len(parts) == 3:
                payload_part = parts[1]
                missing_padding = len(payload_part) % 4
                if missing_padding:
                    payload_part += '=' * (4 - missing_padding)
                payload_bytes = base64.urlsafe_b64decode(payload_part)
                payload = json.loads(payload_bytes.decode('utf-8'))
                project_id = payload.get('project_id')
        except Exception as e:
            logger.warning(f"从JWT令牌中提取项目ID失败: {e}")
        
        if not project_id:
            project_id = "default-project"
            
        api_url = f"http://retail-ai-saas-backend:8000/api/v1/project/{project_id}/plugin/sapi/mcp/token/verify"
        headers = {
            "Content-Type": "application/json"
        }
        
        payload = {"token": sapi_token}
        
        async with httpx.AsyncClient(timeout=10.0) as client:
            response = await client.post(api_url, headers=headers, json=payload)
            
            if response.status_code == 200:
                data = response.json()
                if data.get("valid"):
                    logger.info(f"SAPI令牌验证成功，用户ID: {data.get('user_id')}")
                    
                    # 从MCP令牌验证响应中提取信息
                    user_id = data.get("user_id")
                    project_id = data.get("project_id")
                    server_permissions = data.get("server_permissions", {})
                    default_permissions = data.get("default_permissions", [])
                    
                    # 生成系统令牌（这里简化处理，实际应该调用系统令牌生成接口）
                    system_token = sapi_token  # 暂时使用原始令牌作为系统令牌
                    
                    return {
                        "success": True,
                        "user_id": str(user_id),
                        "project_id": str(project_id) if project_id else None,
                        "system_token": system_token,
                        "permissions": default_permissions,
                        "auth_type": "sapi_converted"
                    }
                else:
                    logger.warning(f"SAPI令牌验证失败: {data.get('error', '未知错误')}")
                    return {
                        "success": False,
                        "message": f"SAPI令牌验证失败: {data.get('error', '未知错误')}"
                    }
            else:
                logger.warning(f"SAPI令牌验证失败: {response.status_code} - {response.text}")
                return {
                    "success": False,
                    "message": f"SAPI令牌验证失败: {response.status_code}"
                }
                
    except Exception as e:
        logger.error(f"SAPI令牌验证异常: {str(e)}")
        return {
            "success": False,
            "message": f"SAPI令牌验证异常: {str(e)}"
        }

async def create_direct_access_token(user_id: str, project_id: Optional[str], system_token: str, permissions: List[str]) -> str:
    """
    创建直接访问令牌
    
    Args:
        user_id: 用户ID
        project_id: 项目ID
        system_token: 系统令牌
        permissions: 权限列表
    
    Returns:
        直接访问令牌
    """
    payload = {
        "user_id": user_id,
        "project_id": project_id,
        "system_token": system_token,
        "permissions": permissions,
        "token_type": "direct_access",
        "iat": int(time.time()),
        "exp": int(time.time()) + (SYSTEM_TOKEN_EXPIRE_HOURS * 3600)
    }
    
    return jwt.encode(payload, JWT_SECRET_KEY, algorithm=JWT_ALGORITHM)

async def verify_direct_access_token(token: str) -> Dict[str, Any]:
    """
    验证直接访问令牌
    
    Args:
        token: 直接访问令牌
    
    Returns:
        令牌信息字典
    """
    try:
        payload = jwt.decode(token, JWT_SECRET_KEY, algorithms=[JWT_ALGORITHM])
        
        if payload.get("token_type") != "direct_access":
            return {"success": False, "message": "无效的令牌类型"}
        
        return {
            "success": True,
            "user_id": payload.get("user_id"),
            "project_id": payload.get("project_id"),
            "system_token": payload.get("system_token"),
            "permissions": payload.get("permissions", []),
            "auth_type": "direct_access"
        }
        
    except JWTError as e:
        logger.warning(f"直接访问令牌验证失败: {str(e)}")
        return {"success": False, "message": f"令牌验证失败: {str(e)}"}

async def get_session_with_cache(session_id: str) -> Optional[Dict[str, Any]]:
    """
    获取会话信息（带缓存）
    
    Args:
        session_id: 会话ID
    
    Returns:
        会话信息字典或None
    """
    # 先检查本地缓存
    if session_id in session_cache:
        session_data = session_cache[session_id]
        if session_data.get("expires_at", 0) > time.time():
            return session_data
        else:
            # 缓存过期，删除
            del session_cache[session_id]
    
    # 从Redis获取
    try:
        redis_client = await get_redis_client()
        session_data_str = await redis_client.get(f"mcp_session:{session_id}")
        
        if session_data_str:
            session_data = json.loads(session_data_str)
            # 更新本地缓存
            session_cache[session_id] = session_data
            return session_data
            
    except Exception as e:
        logger.error(f"从Redis获取会话失败: {str(e)}")
    
    return None

# API端点
@app.get("/auth/verify")
@app.post("/auth/verify")
@app.options("/auth/verify")
async def verify_auth(request: Request):
    """
    Traefik ForwardAuth验证端点
    验证请求中的Authorization头部
    """
    # 获取请求头
    authorization = request.headers.get("authorization", "")
    x_mcp_server_id = request.headers.get("x-mcp-server-id", "")
    x_forwarded_uri = request.headers.get("x-forwarded-uri", "")
    x_forwarded_method = request.headers.get("x-forwarded-method", request.method)
    
    logger.info(f"收到鉴权请求: URI={x_forwarded_uri}, ServerID={x_mcp_server_id}, Method={x_forwarded_method}")
    
    # 允许OPTIONS请求通过（CORS预检）
    if x_forwarded_method == "OPTIONS" or request.method == "OPTIONS":
        logger.info("允许OPTIONS请求通过（CORS预检）")
        return JSONResponse(
            content={"status": "authorized", "auth_type": "cors_preflight"},
            headers={
                "X-MCP-Auth-Type": "cors_preflight",
                "Access-Control-Allow-Origin": "*",
                "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
                "Access-Control-Allow-Headers": "Content-Type, Accept, Authorization, mcp-session-id, x-mcp-project-id, x-mcp-user-id",
                "Access-Control-Max-Age": "86400"
            }
        )
    
    if not authorization:
        logger.warning("缺少Authorization头部")
        raise HTTPException(status_code=401, detail="缺少Authorization头部")
    
    # 解析令牌
    if not authorization.startswith("Bearer "):
        logger.warning("无效的Authorization格式")
        raise HTTPException(status_code=401, detail="无效的Authorization格式")
    
    token = authorization[7:]  # 移除"Bearer "前缀
    token_preview = sanitize_token(token)
    logger.info(f"处理令牌: {token_preview}")
    
    # 尝试验证直接访问令牌
    direct_result = await verify_direct_access_token(token)
    if direct_result["success"]:
        logger.info(f"直接访问令牌验证成功，用户: {direct_result['user_id']}")
        
        # 设置响应头
        response_headers = {
            "X-MCP-User-ID": direct_result["user_id"],
            "X-MCP-Project-ID": direct_result.get("project_id", ""),
            "X-MCP-System-Token": direct_result["system_token"],
            "X-MCP-Permissions": json.dumps(direct_result["permissions"]),
            "X-MCP-Auth-Type": "direct_access"
        }
        
        return JSONResponse(
            content={"status": "authorized", "auth_type": "direct_access"},
            headers=response_headers
        )
    
    # 尝试验证SAPI令牌
    sapi_result = await verify_sapi_token_and_convert(token)
    if sapi_result["success"]:
        logger.info(f"SAPI令牌验证成功，用户: {sapi_result['user_id']}")
        
        # 设置响应头
        response_headers = {
            "X-MCP-User-ID": sapi_result["user_id"],
            "X-MCP-Project-ID": sapi_result.get("project_id", ""),
            "X-MCP-System-Token": sapi_result["system_token"],
            "X-MCP-Permissions": json.dumps(sapi_result["permissions"]),
            "X-MCP-Auth-Type": "sapi_converted"
        }
        
        return JSONResponse(
            content={"status": "authorized", "auth_type": "sapi_converted"},
            headers=response_headers
        )
    
    # 所有验证都失败
    logger.warning(f"令牌验证失败: {token_preview}")
    raise HTTPException(status_code=401, detail="令牌验证失败")

@app.post("/auth/session/create", response_model=SessionCreateResponse)
async def create_session(request: SessionCreateRequest):
    """
    创建会话并生成直接访问令牌
    """
    logger.info(f"创建会话请求: ServerID={request.server_id}")
    
    # 验证SAPI令牌
    sapi_result = await verify_sapi_token_and_convert(request.sapi_token, request.project_id)
    if not sapi_result["success"]:
        return SessionCreateResponse(
            success=False,
            message=sapi_result.get("message", "SAPI令牌验证失败")
        )
    
    # 生成会话ID和直接访问令牌
    session_id = str(uuid.uuid4())
    direct_token = await create_direct_access_token(
        user_id=sapi_result["user_id"],
        project_id=sapi_result.get("project_id"),
        system_token=sapi_result["system_token"],
        permissions=sapi_result["permissions"]
    )
    
    # 计算过期时间
    expires_at = datetime.now() + timedelta(hours=SYSTEM_TOKEN_EXPIRE_HOURS)
    
    # 保存会话到Redis
    session_data = {
        "session_id": session_id,
        "user_id": sapi_result["user_id"],
        "project_id": sapi_result.get("project_id"),
        "system_token": sapi_result["system_token"],
        "permissions": sapi_result["permissions"],
        "server_id": request.server_id,
        "direct_access_token": direct_token,
        "created_at": time.time(),
        "expires_at": expires_at.timestamp()
    }
    
    try:
        redis_client = await get_redis_client()
        await redis_client.setex(
            f"mcp_session:{session_id}",
            SYSTEM_TOKEN_EXPIRE_HOURS * 3600,
            json.dumps(session_data)
        )
        
        # 更新本地缓存
        session_cache[session_id] = session_data
        
        logger.info(f"会话创建成功: {session_id}")
        
        return SessionCreateResponse(
            success=True,
            session_id=session_id,
            direct_access_token=direct_token,
            expires_at=expires_at,
            message="会话创建成功"
        )
        
    except Exception as e:
        logger.error(f"保存会话失败: {str(e)}")
        return SessionCreateResponse(
            success=False,
            message=f"保存会话失败: {str(e)}"
        )

@app.get("/health")
async def health_check():
    """健康检查端点"""
    try:
        # 检查Redis连接
        redis_client = await get_redis_client()
        await redis_client.ping()
        redis_status = "healthy"
    except Exception as e:
        redis_status = f"unhealthy: {str(e)}"
    
    return {
        "status": "healthy",
        "service": "sapi-traefik-auth",
        "version": "1.0.0",
        "timestamp": datetime.now().isoformat(),
        "redis": redis_status,
        "cache_size": len(session_cache)
    }

@app.get("/metrics")
async def get_metrics():
    """获取服务指标"""
    return {
        "service": "sapi-traefik-auth",
        "version": "1.0.0",
        "uptime": time.time(),
        "cache": {
            "local_sessions": len(session_cache),
            "cache_ttl": CACHE_TTL
        },
        "config": {
            "jwt_expire_minutes": JWT_EXPIRE_MINUTES,
            "system_token_expire_hours": SYSTEM_TOKEN_EXPIRE_HOURS
        }
    }

# 启动事件
@app.on_event("startup")
async def startup_event():
    """应用启动事件"""
    logger.info("SAPI Traefik鉴权微服务启动")
    
    # 测试Redis连接
    try:
        redis_client = await get_redis_client()
        await redis_client.ping()
        logger.info("Redis连接测试成功")
    except Exception as e:
        logger.error(f"Redis连接测试失败: {str(e)}")

@app.on_event("shutdown")
async def shutdown_event():
    """应用关闭事件"""
    logger.info("SAPI Traefik鉴权微服务关闭")
    
    # 清理资源
    global redis_client
    if redis_client:
        await redis_client.close()
    
    session_cache.clear()

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8001) 