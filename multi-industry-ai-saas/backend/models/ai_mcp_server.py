#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
AI助手MCP服务器数据模型
"""

import uuid
from datetime import datetime
from sqlalchemy import Column, String, Boolean, DateTime, ForeignKey, JSON, Text, Integer
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID

from db.database import Base

class AIMCPServer(Base):
    """AI助手MCP服务器模型"""
    __tablename__ = "ai_mcp_servers"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(100), nullable=False)
    description = Column(Text, nullable=True)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id"), nullable=False)
    
    # 传输协议配置
    transport_type = Column(String(20), nullable=False)  # sse, streamhttp, stdio
    transport_types = Column(JSON, default=list)  # 支持的传输类型列表
    
    # 部署模式
    deployment_mode = Column(String(20), default="external")  # external, internal, local, docker, npm
    source = Column(String(20), default="manual")  # manual, sapi, import
    
    # 连接配置
    config = Column(JSON, default=dict)  # 连接配置（URL、认证等）
    
    # 状态信息
    enabled = Column(Boolean, default=True)
    status = Column(String(20), default="unknown")  # unknown, connected, disconnected, error
    last_connected_at = Column(DateTime, nullable=True)
    
    # 工具信息
    tools_count = Column(Integer, default=0)
    available_tools = Column(JSON, default=list)  # 缓存的工具列表
    
    # 资源信息
    resources_count = Column(Integer, default=0)
    available_resources = Column(JSON, default=list)  # 缓存的资源列表
    
    # 提示词信息
    prompts_count = Column(Integer, default=0)
    available_prompts = Column(JSON, default=list)  # 缓存的提示词列表
    
    # 服务器能力信息
    server_capabilities = Column(JSON, default=dict)  # 服务器能力
    server_info = Column(JSON, default=dict)  # 服务器信息
    
    # 元数据
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)
    created_by_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)
    
    # SAPI插件关联（如果是从SAPI添加的）
    sapi_server_id = Column(UUID(as_uuid=True), nullable=True)  # 关联的SAPI服务器ID

    # 关系
    project = relationship("Project")
    created_by = relationship("User", foreign_keys=[created_by_id]) 