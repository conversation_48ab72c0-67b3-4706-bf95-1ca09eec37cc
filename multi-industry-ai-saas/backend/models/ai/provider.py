#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
AI 提供商模型
"""

import uuid
from datetime import datetime
from sqlalchemy import Column, String, Boolean, DateTime, Text, JSON, func
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship

from db.database import Base

class AIProvider(Base):
    """AI 提供商模型"""
    __tablename__ = "ai_providers"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(50), nullable=False, unique=True, comment="提供商名称，如 openai, azure, anthropic 等")
    display_name = Column(String(100), nullable=False, comment="显示名称")
    description = Column(Text, nullable=True, comment="描述")
    api_base_url = Column(String(255), nullable=True, comment="API 基础 URL")
    auth_type = Column(String(20), nullable=False, default="api_key", comment="认证类型：api_key, oauth 等")
    icon_url = Column(String(255), nullable=True, comment="图标 URL")
    status = Column(String(20), nullable=False, default="active", comment="状态：active, inactive")
    is_builtin = Column(Boolean, nullable=False, default=False, comment="是否内置提供商")
    config_schema = Column(JSONB, nullable=True, comment="配置模式")
    created_at = Column(DateTime(timezone=True), server_default=func.now(), comment="创建时间")
    updated_at = Column(DateTime(timezone=True), onupdate=func.now(), comment="更新时间")

    # 关系
    models = relationship("AIModel", back_populates="provider", cascade="all, delete-orphan")
    configs = relationship("AIConfig", back_populates="provider", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<AIProvider(id={self.id}, name='{self.name}')>"
