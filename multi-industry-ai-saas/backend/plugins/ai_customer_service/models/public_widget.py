#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
AI客服公共组件配置模型
"""

import uuid
from datetime import datetime
from sqlalchemy import Column, String, Text, Boolean, DateTime, UUID, JSON
from sqlalchemy.dialects.postgresql import UUID as PgUUID
from db.database import Base

class PublicWidgetConfig(Base):
    """公共组件配置模型"""
    
    __tablename__ = "ai_customer_service_public_widget_config"
    
    id = Column(PgUUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    project_id = Column(PgUUID(as_uuid=True), nullable=False, index=True)
    widget_id = Column(String(255), nullable=False, unique=True, index=True)
    
    # 基础配置
    title = Column(String(255), default="AI智能客服")
    description = Column(String(500), default="我是您的专属智能客服，随时为您提供帮助")
    assistant_prompt = Column(Text, default="你是一个专业、友好、耐心的智能客服助手。")
    
    # 外观配置
    theme_color = Column(String(20), default="#1890ff")
    position = Column(String(50), default="bottom-right")  # bottom-right, bottom-left, top-right, top-left
    size = Column(String(20), default="medium")  # small, medium, large
    
    # 功能配置
    auto_open = Column(Boolean, default=False)
    show_avatar = Column(Boolean, default=True)
    show_typing_indicator = Column(Boolean, default=True)
    enable_file_upload = Column(Boolean, default=False)
    enable_voice_input = Column(Boolean, default=False)
    
    # 欢迎消息配置
    auto_greeting = Column(Boolean, default=True)
    greeting_message = Column(Text, default="您好！我是AI智能客服，有什么可以帮助您的吗？")
    
    # 高级配置
    custom_css = Column(Text, nullable=True)
    quick_replies = Column(JSON, default=lambda: ["产品咨询", "技术支持", "价格信息", "联系方式"])
    
    # 工作时间配置
    business_hours_enabled = Column(Boolean, default=False)
    business_hours = Column(JSON, default=lambda: {
        "timezone": "Asia/Shanghai",
        "schedule": {
            "monday": {"start": "09:00", "end": "18:00"},
            "tuesday": {"start": "09:00", "end": "18:00"},
            "wednesday": {"start": "09:00", "end": "18:00"},
            "thursday": {"start": "09:00", "end": "18:00"},
            "friday": {"start": "09:00", "end": "18:00"},
            "saturday": {"start": "10:00", "end": "16:00"},
            "sunday": {"start": "10:00", "end": "16:00"}
        }
    })
    offline_message = Column(Text, default="客服暂时离线，请留言，我们会尽快回复您。")
    
    # 元数据
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def to_dict(self):
        """转换为字典"""
        return {
            "id": str(self.id),
            "project_id": str(self.project_id),
            "widget_id": self.widget_id,
            "title": self.title,
            "description": self.description,
            "assistant_prompt": self.assistant_prompt,
            "theme_color": self.theme_color,
            "position": self.position,
            "size": self.size,
            "auto_open": self.auto_open,
            "show_avatar": self.show_avatar,
            "show_typing_indicator": self.show_typing_indicator,
            "enable_file_upload": self.enable_file_upload,
            "enable_voice_input": self.enable_voice_input,
            "auto_greeting": self.auto_greeting,
            "greeting_message": self.greeting_message,
            "custom_css": self.custom_css,
            "quick_replies": self.quick_replies,
            "business_hours_enabled": self.business_hours_enabled,
            "business_hours": self.business_hours,
            "offline_message": self.offline_message,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None
        } 