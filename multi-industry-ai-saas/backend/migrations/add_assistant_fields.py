#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
添加AI助手表的新字段

Revision ID: add_assistant_fields
Revises: 
Create Date: 2024-01-01 00:00:00.000000
"""

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers
revision = 'add_assistant_fields'
down_revision = None
branch_labels = None
depends_on = None

def upgrade():
    """添加新字段"""
    # 添加mcp_tools字段
    op.add_column('ai_assistants', sa.Column('mcp_tools', postgresql.ARRAY(postgresql.UUID(as_uuid=True)), nullable=True, comment='MCP工具 ID 列表'))
    
    # 添加capabilities字段
    op.add_column('ai_assistants', sa.Column('capabilities', postgresql.JSONB(astext_type=sa.Text()), nullable=True, comment='助手能力配置'))
    
    # 添加top_p字段
    op.add_column('ai_assistants', sa.<PERSON>umn('top_p', sa.Float(), nullable=True, comment='核心采样参数'))
    
    # 添加frequency_penalty字段
    op.add_column('ai_assistants', sa.Column('frequency_penalty', sa.Float(), nullable=True, comment='频率惩罚'))
    
    # 添加welcome_message字段
    op.add_column('ai_assistants', sa.Column('welcome_message', sa.Text(), nullable=True, comment='欢迎消息'))

def downgrade():
    """删除新字段"""
    op.drop_column('ai_assistants', 'welcome_message')
    op.drop_column('ai_assistants', 'frequency_penalty')
    op.drop_column('ai_assistants', 'top_p')
    op.drop_column('ai_assistants', 'capabilities')
    op.drop_column('ai_assistants', 'mcp_tools') 