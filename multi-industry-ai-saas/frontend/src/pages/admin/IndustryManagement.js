/**
 * 行业管理页面
 * 系统管理员用于管理支持的行业类型
 */

import React, { useState, useEffect } from 'react';
import { 
  Table, 
  Button, 
  Input, 
  Space, 
  Modal, 
  Form, 
  Switch, 
  message, 
  Popconfirm, 
  Tag, 
  Card, 
  Typography,
  Upload,
  Select
} from 'antd';
import { 
  PlusOutlined, 
  SearchOutlined, 
  EditOutlined, 
  DeleteOutlined,
  UploadOutlined
} from '@ant-design/icons';
import apiService from '../../services/api';

const { Title, Paragraph } = Typography;
const { Option } = Select;
const { TextArea } = Input;

const IndustryManagement = () => {
  const [industries, setIndustries] = useState([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [modalTitle, setModalTitle] = useState('添加行业');
  const [editingIndustry, setEditingIndustry] = useState(null);
  const [searchText, setSearchText] = useState('');
  const [form] = Form.useForm();

  // 加载行业数据
  const fetchIndustries = async () => {
    try {
      setLoading(true);
      const data = await apiService.getIndustries();
      setIndustries(data);
    } catch (error) {
      message.error('获取行业列表失败');
      console.error('获取行业列表失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 初始化数据
  useEffect(() => {
    fetchIndustries();
  }, []);

  // 打开添加行业模态框
  const showAddModal = () => {
    setModalTitle('添加行业');
    setEditingIndustry(null);
    form.resetFields();
    setModalVisible(true);
  };

  // 打开编辑行业模态框
  const showEditModal = (industry) => {
    setModalTitle('编辑行业');
    setEditingIndustry(industry);
    form.setFieldsValue({
      name: industry.name,
      code: industry.code,
      description: industry.description,
      icon: industry.icon,
      is_active: industry.is_active,
      order: industry.order,
    });
    setModalVisible(true);
  };

  // 关闭模态框
  const handleCancel = () => {
    setModalVisible(false);
  };

  // 提交表单
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      
      if (editingIndustry) {
        // 更新行业
        await apiService.updateIndustry(editingIndustry.id, values);
        message.success('行业更新成功');
      } else {
        // 创建行业
        await apiService.createIndustry(values);
        message.success('行业创建成功');
      }
      
      setModalVisible(false);
      fetchIndustries();
    } catch (error) {
      console.error('提交表单失败:', error);
      message.error('操作失败: ' + (error.response?.data?.message || error.message));
    }
  };

  // 删除行业
  const handleDelete = async (industryId) => {
    try {
      await apiService.deleteIndustry(industryId);
      message.success('行业删除成功');
      fetchIndustries();
    } catch (error) {
      console.error('删除行业失败:', error);
      message.error('删除失败: ' + (error.response?.data?.message || error.message));
    }
  };

  // 过滤行业数据
  const filteredIndustries = industries.filter(industry => 
    industry.name.toLowerCase().includes(searchText.toLowerCase()) ||
    industry.code.toLowerCase().includes(searchText.toLowerCase()) ||
    industry.description.toLowerCase().includes(searchText.toLowerCase())
  );

  // 表格列定义
  const columns = [
    {
      title: '行业名称',
      dataIndex: 'name',
      key: 'name',
      sorter: (a, b) => a.name.localeCompare(b.name),
    },
    {
      title: '行业代码',
      dataIndex: 'code',
      key: 'code',
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },
    {
      title: '图标',
      dataIndex: 'icon',
      key: 'icon',
      render: (icon) => icon ? <img src={icon} alt="行业图标" style={{ width: 24, height: 24 }} /> : '-',
    },
    {
      title: '排序',
      dataIndex: 'order',
      key: 'order',
      sorter: (a, b) => a.order - b.order,
    },
    {
      title: '状态',
      dataIndex: 'is_active',
      key: 'is_active',
      render: (isActive) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? '启用' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Button 
            icon={<EditOutlined />} 
            onClick={() => showEditModal(record)}
            size="small"
          />
          <Popconfirm
            title="确定要删除此行业吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button 
              icon={<DeleteOutlined />} 
              danger 
              size="small"
            />
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 上传图标配置
  const uploadProps = {
    name: 'file',
    action: '/api/upload/icon',
    headers: {
      authorization: 'Bearer ' + localStorage.getItem('token'),
    },
    onChange(info) {
      if (info.file.status === 'done') {
        message.success(`${info.file.name} 上传成功`);
        form.setFieldsValue({ icon: info.file.response.url });
      } else if (info.file.status === 'error') {
        message.error(`${info.file.name} 上传失败`);
      }
    },
  };

  return (
    <div>
      <Card>
        <Title level={2}>行业管理</Title>
        <Paragraph>
          管理系统支持的行业类型，为不同行业的租户提供定制化功能。
        </Paragraph>
        
        <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between' }}>
          <Input
            placeholder="搜索行业"
            prefix={<SearchOutlined />}
            style={{ width: 300 }}
            value={searchText}
            onChange={e => setSearchText(e.target.value)}
          />
          <Button 
            type="primary" 
            icon={<PlusOutlined />}
            onClick={showAddModal}
          >
            添加行业
          </Button>
        </div>
        
        <Table
          columns={columns}
          dataSource={filteredIndustries}
          rowKey="id"
          loading={loading}
          pagination={{ pageSize: 10 }}
        />
      </Card>
      
      <Modal
        title={modalTitle}
        open={modalVisible}
        onOk={handleSubmit}
        onCancel={handleCancel}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Form.Item
            name="name"
            label="行业名称"
            rules={[{ required: true, message: '请输入行业名称' }]}
          >
            <Input placeholder="行业名称" />
          </Form.Item>
          
          <Form.Item
            name="code"
            label="行业代码"
            rules={[
              { required: true, message: '请输入行业代码' },
              { pattern: /^[a-z0-9_]+$/, message: '行业代码只能包含小写字母、数字和下划线' }
            ]}
          >
            <Input placeholder="行业代码，如：retail, restaurant, hotel" />
          </Form.Item>
          
          <Form.Item
            name="description"
            label="行业描述"
          >
            <TextArea rows={4} placeholder="行业描述" />
          </Form.Item>
          
          <Form.Item
            name="icon"
            label="行业图标"
          >
            <Input placeholder="图标URL" />
          </Form.Item>
          
          <Form.Item>
            <Upload {...uploadProps}>
              <Button icon={<UploadOutlined />}>上传图标</Button>
            </Upload>
          </Form.Item>
          
          <Form.Item
            name="order"
            label="排序"
            initialValue={0}
          >
            <Input type="number" placeholder="排序值，数字越小越靠前" />
          </Form.Item>
          
          <Form.Item
            name="is_active"
            label="启用状态"
            valuePropName="checked"
            initialValue={true}
          >
            <Switch checkedChildren="启用" unCheckedChildren="禁用" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default IndustryManagement;
