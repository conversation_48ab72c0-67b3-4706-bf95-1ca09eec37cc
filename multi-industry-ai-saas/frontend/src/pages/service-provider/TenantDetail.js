import React, { useState, useEffect } from 'react';
import {
  Card,
  Descriptions,
  Button,
  Space,
  Tag,
  Typography,
  Row,
  Col,
  Tabs,
  Statistic,
  Table,
  Progress,
  List,
  Avatar,
  Tooltip,
  Divider,
  Badge,
  Empty,
  Spin,
  Alert,
  Modal
} from 'antd';
import {
  ShopOutlined,
  UserOutlined,
  AppstoreOutlined,
  CloudServerOutlined,
  SettingOutlined,
  EditOutlined,
  DeleteOutlined,
  ExclamationCircleOutlined,
  TeamOutlined,
  BarChartOutlined,
  ClockCircleOutlined,
  RollbackOutlined,
  PlusOutlined,
  DownloadOutlined,
  PauseCircleOutlined,
  PlayCircleOutlined,
  ReloadOutlined,
  HistoryOutlined,
  ApiOutlined,
  EyeOutlined
} from '@ant-design/icons';
import { useNavigate, useParams } from 'react-router-dom';
import apiService from '../../services/api';
import { useAuth } from '../../contexts/AuthContext';
import './TenantDetail.css';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;
const { confirm } = Modal;

const TenantDetail = () => {
  const navigate = useNavigate();
  const { tenantId } = useParams();
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [tenant, setTenant] = useState(null);
  const [users, setUsers] = useState([]);
  const [projects, setProjects] = useState([]);
  const [plugins, setPlugins] = useState([]);
  const [usageHistory, setUsageHistory] = useState([]);
  const [activeTab, setActiveTab] = useState('overview');
  const [error, setError] = useState(null);

  // 加载租户数据
  useEffect(() => {
    fetchTenantData();
  }, [tenantId]);

  // 获取租户详情和相关数据
  const fetchTenantData = async () => {
    try {
      setLoading(true);
      setError(null);

      // 获取租户详情
      const tenantData = await apiService.getTenant(tenantId);

      // 添加一些模拟数据以便展示
      const enhancedTenant = {
        ...tenantData,
        current_users: Math.floor(Math.random() * tenantData.max_users),
        current_storage_gb: Math.floor(Math.random() * tenantData.max_storage_gb),
        current_projects: Math.floor(Math.random() * tenantData.max_projects),
        subscription_plan_name: ['标准版', '专业版', '企业版', '尊享版'][Math.floor(Math.random() * 4)],
        subscription_start_date: new Date(Date.now() - Math.floor(Math.random() * 365) * 86400000).toLocaleDateString(),
        subscription_end_date: new Date(Date.now() + Math.floor(Math.random() * 365) * 86400000).toLocaleDateString(),
        last_active: new Date(Date.now() - Math.floor(Math.random() * 30) * 86400000).toLocaleDateString(),
        api_calls_limit: 10000,
        api_calls_used: Math.floor(Math.random() * 10000),
        contact_person: '张经理',
        contact_email: '<EMAIL>',
        contact_phone: '13800138000'
      };

      setTenant(enhancedTenant);

      // 模拟用户数据
      const mockUsers = Array.from({ length: 5 }, (_, i) => ({
        id: `user-${i}`,
        name: `用户${i + 1}`,
        email: `user${i + 1}@example.com`,
        role: ['管理员', '普通用户', '操作员', '查看者', '财务'][i],
        last_login: new Date(Date.now() - Math.floor(Math.random() * 30) * 86400000).toLocaleDateString(),
        status: ['active', 'active', 'active', 'inactive', 'active'][i]
      }));
      setUsers(mockUsers);

      // 模拟项目数据
      const mockProjects = Array.from({ length: 3 }, (_, i) => ({
        id: `project-${i}`,
        name: `项目${i + 1}`,
        description: `这是项目${i + 1}的描述`,
        created_at: new Date(Date.now() - Math.floor(Math.random() * 365) * 86400000).toLocaleDateString(),
        user_count: Math.floor(Math.random() * 10) + 1,
        status: ['active', 'active', 'inactive'][i]
      }));
      setProjects(mockProjects);

      // 模拟插件数据
      const mockPlugins = [
        { id: 'plugin-1', name: '数据分析插件', status: 'active', type: '分析', description: '提供高级数据分析功能' },
        { id: 'plugin-2', name: '报表生成器', status: 'active', type: '报表', description: '自定义报表生成工具' },
        { id: 'plugin-3', name: 'AI助手', status: 'inactive', type: 'AI', description: '智能AI助手功能' },
        { id: 'plugin-4', name: '钉钉集成', status: 'active', type: '集成', description: '与钉钉平台集成' },
        { id: 'plugin-5', name: '微信小程序', status: 'active', type: '集成', description: '微信小程序接入' }
      ];
      setPlugins(mockPlugins);

      // 模拟使用历史数据
      const mockUsageHistory = Array.from({ length: 12 }, (_, i) => {
        const date = new Date();
        date.setMonth(date.getMonth() - i);
        return {
          month: `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`,
          api_calls: Math.floor(Math.random() * 10000),
          storage_usage: Math.floor(Math.random() * enhancedTenant.max_storage_gb),
          active_users: Math.floor(Math.random() * enhancedTenant.max_users)
        };
      }).reverse();
      setUsageHistory(mockUsageHistory);
    } catch (error) {
      console.error('获取租户详情失败:', error);
      setError('获取租户详情失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 处理租户状态变更
  const handleStatusChange = async (newStatus) => {
    try {
      await apiService.updateTenant(tenantId, { status: newStatus });
      fetchTenantData();
    } catch (error) {
      console.error('更新租户状态失败:', error);
    }
  };

  // 处理删除租户
  const handleDelete = () => {
    confirm({
      title: '确定要删除此租户吗?',
      icon: <ExclamationCircleOutlined />,
      content: '删除后将无法恢复，租户的所有数据将被清除。',
      okText: '确定',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          await apiService.deleteTenant(tenantId);
          navigate('/service-provider/tenants');
        } catch (error) {
          console.error('删除租户失败:', error);
        }
      }
    });
  };

  // 用户表格列
  const userColumns = [
    {
      title: '用户名',
      dataIndex: 'name',
      key: 'name',
      render: (text) => (
        <Space>
          <Avatar icon={<UserOutlined />} />
          <Text>{text}</Text>
        </Space>
      )
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      key: 'email',
    },
    {
      title: '角色',
      dataIndex: 'role',
      key: 'role',
      render: (text) => <Tag color="blue">{text}</Tag>
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Badge
          status={status === 'active' ? 'success' : 'default'}
          text={status === 'active' ? '活跃' : '未活跃'}
        />
      )
    },
    {
      title: '最近登录',
      dataIndex: 'last_login',
      key: 'last_login',
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="small">
          <Button type="text" icon={<EditOutlined />} />
          <Button type="text" icon={<DeleteOutlined />} danger />
        </Space>
      ),
    },
  ];

  // 项目表格列
  const projectColumns = [
    {
      title: '项目名称',
      dataIndex: 'name',
      key: 'name',
      render: (text) => <Text strong>{text}</Text>
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },
    {
      title: '用户数',
      dataIndex: 'user_count',
      key: 'user_count',
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Badge
          status={status === 'active' ? 'success' : 'default'}
          text={status === 'active' ? '活跃' : '未活跃'}
        />
      )
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="small">
          <Button type="text" icon={<EyeOutlined />} />
          <Button type="text" icon={<SettingOutlined />} />
        </Space>
      ),
    },
  ];

  // 插件表格列
  const pluginColumns = [
    {
      title: '插件名称',
      dataIndex: 'name',
      key: 'name',
      render: (text) => <Text strong>{text}</Text>
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      render: (text) => <Tag color="purple">{text}</Tag>
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Badge
          status={status === 'active' ? 'success' : 'default'}
          text={status === 'active' ? '已启用' : '未启用'}
        />
      )
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="small">
          <Button
            type="text"
            icon={record.status === 'active' ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
          />
          <Button type="text" icon={<SettingOutlined />} />
        </Space>
      ),
    },
  ];

  // 使用历史表格列
  const usageHistoryColumns = [
    {
      title: '月份',
      dataIndex: 'month',
      key: 'month',
    },
    {
      title: 'API调用次数',
      dataIndex: 'api_calls',
      key: 'api_calls',
      render: (text) => `${text.toLocaleString()} 次`
    },
    {
      title: '存储使用量',
      dataIndex: 'storage_usage',
      key: 'storage_usage',
      render: (text) => `${text} GB`
    },
    {
      title: '活跃用户数',
      dataIndex: 'active_users',
      key: 'active_users',
      render: (text) => `${text} 人`
    },
  ];

  if (loading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%', padding: '50px' }}>
        <Spin size="large" tip="加载中..." />
      </div>
    );
  }

  if (error) {
    return (
      <div style={{ padding: '24px' }}>
        <Alert
          message="错误"
          description={error}
          type="error"
          showIcon
          action={
            <Button type="primary" onClick={fetchTenantData}>
              重试
            </Button>
          }
        />
      </div>
    );
  }

  if (!tenant) {
    return (
      <div style={{ padding: '24px' }}>
        <Empty
          description="未找到租户信息"
          image={Empty.PRESENTED_IMAGE_SIMPLE}
        >
          <Button type="primary" onClick={() => navigate('/service-provider/tenants')}>
            返回租户列表
          </Button>
        </Empty>
      </div>
    );
  }

  return (
    <div className="tenant-detail">
      <div className="page-header">
        <div className="header-left">
          <Button
            icon={<RollbackOutlined />}
            onClick={() => navigate('/service-provider/tenants')}
            style={{ marginRight: 16 }}
          >
            返回列表
          </Button>
          <Title level={2}>{tenant.name}</Title>
        </div>
        <div className="header-right">
          <Space>
            <Button
              icon={<ReloadOutlined />}
              onClick={fetchTenantData}
            >
              刷新
            </Button>
            <Button
              icon={<EditOutlined />}
              onClick={() => navigate(`/service-provider/tenants/${tenantId}/edit`)}
            >
              编辑
            </Button>
            {tenant.status === 'active' ? (
              <Button
                icon={<PauseCircleOutlined />}
                onClick={() => handleStatusChange('suspended')}
              >
                暂停
              </Button>
            ) : (
              <Button
                type="primary"
                icon={<PlayCircleOutlined />}
                onClick={() => handleStatusChange('active')}
              >
                激活
              </Button>
            )}
            <Button
              danger
              icon={<DeleteOutlined />}
              onClick={handleDelete}
            >
              删除
            </Button>
          </Space>
        </div>
      </div>

      <Card bordered={false} className="tenant-info-card">
        <Descriptions
          title="租户基本信息"
          bordered
          column={{ xxl: 4, xl: 3, lg: 3, md: 2, sm: 1, xs: 1 }}
        >
          <Descriptions.Item label="租户ID">{tenant.id}</Descriptions.Item>
          <Descriptions.Item label="行业类型">
            {tenant.industry_type === 'retail' ? '零售' :
             tenant.industry_type === 'restaurant' ? '餐饮' :
             tenant.industry_type === 'hotel' ? '酒店' : tenant.industry_type}
          </Descriptions.Item>
          <Descriptions.Item label="状态">
            <Tag color={
              tenant.status === 'active' ? 'success' :
              tenant.status === 'trial' ? 'processing' :
              tenant.status === 'suspended' ? 'error' : 'default'
            }>
              {tenant.status === 'active' ? '正常' :
               tenant.status === 'trial' ? '试用中' :
               tenant.status === 'suspended' ? '已暂停' :
               tenant.status === 'inactive' ? '未激活' : tenant.status}
            </Tag>
          </Descriptions.Item>
          <Descriptions.Item label="订阅计划">
            <Tag color="gold">{tenant.subscription_plan_name}</Tag>
          </Descriptions.Item>
          <Descriptions.Item label="订阅开始日期">{tenant.subscription_start_date}</Descriptions.Item>
          <Descriptions.Item label="订阅结束日期">{tenant.subscription_end_date}</Descriptions.Item>
          <Descriptions.Item label="最近活跃">{tenant.last_active}</Descriptions.Item>
          <Descriptions.Item label="创建时间">{new Date(tenant.created_at).toLocaleString()}</Descriptions.Item>
          <Descriptions.Item label="联系人">{tenant.contact_person}</Descriptions.Item>
          <Descriptions.Item label="联系邮箱">{tenant.contact_email}</Descriptions.Item>
          <Descriptions.Item label="联系电话">{tenant.contact_phone}</Descriptions.Item>
        </Descriptions>
      </Card>

      <Row gutter={[16, 16]} className="stats-row">
        <Col xs={24} sm={12} md={6}>
          <Card bordered={false} className="stat-card">
            <Statistic
              title="用户数"
              value={tenant.current_users}
              suffix={`/ ${tenant.max_users}`}
              prefix={<TeamOutlined />}
            />
            <Progress
              percent={Math.round((tenant.current_users / tenant.max_users) * 100)}
              status={(tenant.current_users / tenant.max_users) > 0.8 ? 'exception' : 'normal'}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card bordered={false} className="stat-card">
            <Statistic
              title="存储空间"
              value={tenant.current_storage_gb}
              suffix={`GB / ${tenant.max_storage_gb}GB`}
              prefix={<CloudServerOutlined />}
            />
            <Progress
              percent={Math.round((tenant.current_storage_gb / tenant.max_storage_gb) * 100)}
              status={(tenant.current_storage_gb / tenant.max_storage_gb) > 0.8 ? 'exception' : 'normal'}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card bordered={false} className="stat-card">
            <Statistic
              title="项目数"
              value={tenant.current_projects}
              suffix={`/ ${tenant.max_projects}`}
              prefix={<AppstoreOutlined />}
            />
            <Progress
              percent={Math.round((tenant.current_projects / tenant.max_projects) * 100)}
              status={(tenant.current_projects / tenant.max_projects) > 0.8 ? 'exception' : 'normal'}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card bordered={false} className="stat-card">
            <Statistic
              title="API调用"
              value={tenant.api_calls_used}
              suffix={`/ ${tenant.api_calls_limit}`}
              prefix={<ApiOutlined />}
            />
            <Progress
              percent={Math.round((tenant.api_calls_used / tenant.api_calls_limit) * 100)}
              status={(tenant.api_calls_used / tenant.api_calls_limit) > 0.8 ? 'exception' : 'normal'}
            />
          </Card>
        </Col>
      </Row>

      <Card bordered={false} className="tenant-tabs-card">
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane
            tab={<span><AppstoreOutlined />概览</span>}
            key="overview"
          >
            <Row gutter={[16, 16]}>
              <Col xs={24} md={12}>
                <Card title="用户列表" extra={<Button type="link" icon={<PlusOutlined />}>添加用户</Button>}>
                  <Table
                    columns={userColumns.filter(col => col.key !== 'action')}
                    dataSource={users.slice(0, 3)}
                    rowKey="id"
                    pagination={false}
                    size="small"
                  />
                  <div style={{ textAlign: 'center', marginTop: 16 }}>
                    <Button type="link" onClick={() => setActiveTab('users')}>查看全部</Button>
                  </div>
                </Card>
              </Col>
              <Col xs={24} md={12}>
                <Card title="项目列表" extra={<Button type="link" icon={<PlusOutlined />}>创建项目</Button>}>
                  <Table
                    columns={projectColumns.filter(col => col.key !== 'action')}
                    dataSource={projects}
                    rowKey="id"
                    pagination={false}
                    size="small"
                  />
                  <div style={{ textAlign: 'center', marginTop: 16 }}>
                    <Button type="link" onClick={() => setActiveTab('projects')}>查看全部</Button>
                  </div>
                </Card>
              </Col>
              <Col xs={24}>
                <Card title="已安装插件" extra={<Button type="link" icon={<PlusOutlined />}>添加插件</Button>}>
                  <Table
                    columns={pluginColumns.filter(col => col.key !== 'action')}
                    dataSource={plugins.filter(p => p.status === 'active').slice(0, 3)}
                    rowKey="id"
                    pagination={false}
                    size="small"
                  />
                  <div style={{ textAlign: 'center', marginTop: 16 }}>
                    <Button type="link" onClick={() => setActiveTab('plugins')}>查看全部</Button>
                  </div>
                </Card>
              </Col>
            </Row>
          </TabPane>
          <TabPane
            tab={<span><TeamOutlined />用户管理</span>}
            key="users"
          >
            <div className="tab-toolbar">
              <Button type="primary" icon={<PlusOutlined />}>添加用户</Button>
            </div>
            <Table
              columns={userColumns}
              dataSource={users}
              rowKey="id"
              pagination={{ pageSize: 10 }}
            />
          </TabPane>
          <TabPane
            tab={<span><AppstoreOutlined />项目管理</span>}
            key="projects"
          >
            <div className="tab-toolbar">
              <Button type="primary" icon={<PlusOutlined />}>创建项目</Button>
            </div>
            <Table
              columns={projectColumns}
              dataSource={projects}
              rowKey="id"
              pagination={{ pageSize: 10 }}
            />
          </TabPane>
          <TabPane
            tab={<span><AppstoreOutlined />插件管理</span>}
            key="plugins"
          >
            <div className="tab-toolbar">
              <Button type="primary" icon={<PlusOutlined />}>添加插件</Button>
            </div>
            <Table
              columns={pluginColumns}
              dataSource={plugins}
              rowKey="id"
              pagination={{ pageSize: 10 }}
            />
          </TabPane>
          <TabPane
            tab={<span><HistoryOutlined />使用历史</span>}
            key="usage"
          >
            <div className="tab-toolbar">
              <Button icon={<DownloadOutlined />}>导出数据</Button>
            </div>
            <Table
              columns={usageHistoryColumns}
              dataSource={usageHistory}
              rowKey="month"
              pagination={{ pageSize: 10 }}
            />
          </TabPane>
          <TabPane
            tab={<span><SettingOutlined />配置管理</span>}
            key="settings"
          >
            <div className="settings-container">
              <Row gutter={[16, 16]}>
                <Col xs={24} md={12}>
                  <Card title="基本设置">
                    <Descriptions column={1} bordered>
                      <Descriptions.Item label="租户名称">{tenant.name}</Descriptions.Item>
                      <Descriptions.Item label="行业类型">
                        {tenant.industry_type === 'retail' ? '零售' :
                         tenant.industry_type === 'restaurant' ? '餐饮' :
                         tenant.industry_type === 'hotel' ? '酒店' : tenant.industry_type}
                      </Descriptions.Item>
                      <Descriptions.Item label="状态">
                        <Tag color={
                          tenant.status === 'active' ? 'success' :
                          tenant.status === 'trial' ? 'processing' :
                          tenant.status === 'suspended' ? 'error' : 'default'
                        }>
                          {tenant.status === 'active' ? '正常' :
                           tenant.status === 'trial' ? '试用中' :
                           tenant.status === 'suspended' ? '已暂停' :
                           tenant.status === 'inactive' ? '未激活' : tenant.status}
                        </Tag>
                      </Descriptions.Item>
                    </Descriptions>
                    <div style={{ marginTop: 16, textAlign: 'right' }}>
                      <Button type="primary" icon={<EditOutlined />}>编辑基本信息</Button>
                    </div>
                  </Card>
                </Col>
                <Col xs={24} md={12}>
                  <Card title="订阅计划">
                    <Descriptions column={1} bordered>
                      <Descriptions.Item label="当前计划">
                        <Tag color="gold">{tenant.subscription_plan_name}</Tag>
                      </Descriptions.Item>
                      <Descriptions.Item label="订阅开始日期">{tenant.subscription_start_date}</Descriptions.Item>
                      <Descriptions.Item label="订阅结束日期">{tenant.subscription_end_date}</Descriptions.Item>
                    </Descriptions>
                    <div style={{ marginTop: 16, textAlign: 'right' }}>
                      <Button type="primary">升级计划</Button>
                    </div>
                  </Card>
                </Col>
                <Col xs={24}>
                  <Card title="资源配额">
                    <Row gutter={[16, 16]}>
                      <Col xs={24} md={8}>
                        <Card title="用户数">
                          <Statistic
                            value={tenant.current_users}
                            suffix={`/ ${tenant.max_users}`}
                          />
                          <Progress
                            percent={Math.round((tenant.current_users / tenant.max_users) * 100)}
                            status={(tenant.current_users / tenant.max_users) > 0.8 ? 'exception' : 'normal'}
                          />
                          <div style={{ marginTop: 16, textAlign: 'right' }}>
                            <Button>增加配额</Button>
                          </div>
                        </Card>
                      </Col>
                      <Col xs={24} md={8}>
                        <Card title="存储空间">
                          <Statistic
                            value={tenant.current_storage_gb}
                            suffix={`GB / ${tenant.max_storage_gb}GB`}
                          />
                          <Progress
                            percent={Math.round((tenant.current_storage_gb / tenant.max_storage_gb) * 100)}
                            status={(tenant.current_storage_gb / tenant.max_storage_gb) > 0.8 ? 'exception' : 'normal'}
                          />
                          <div style={{ marginTop: 16, textAlign: 'right' }}>
                            <Button>增加配额</Button>
                          </div>
                        </Card>
                      </Col>
                      <Col xs={24} md={8}>
                        <Card title="API调用">
                          <Statistic
                            value={tenant.api_calls_used}
                            suffix={`/ ${tenant.api_calls_limit}`}
                          />
                          <Progress
                            percent={Math.round((tenant.api_calls_used / tenant.api_calls_limit) * 100)}
                            status={(tenant.api_calls_used / tenant.api_calls_limit) > 0.8 ? 'exception' : 'normal'}
                          />
                          <div style={{ marginTop: 16, textAlign: 'right' }}>
                            <Button>增加配额</Button>
                          </div>
                        </Card>
                      </Col>
                    </Row>
                  </Card>
                </Col>
              </Row>
            </div>
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default TenantDetail;
