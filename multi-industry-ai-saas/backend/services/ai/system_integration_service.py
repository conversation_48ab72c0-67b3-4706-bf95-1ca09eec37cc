#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
系统 AI 整合设置服务
"""

import uuid
import logging
from typing import Optional, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_
from sqlalchemy.orm import selectinload

from models.ai.system_integration import SystemAIIntegration
from models.ai.model import AIModel
from schemas.ai.system_integration import (
    SystemAIIntegrationCreate,
    SystemAIIntegrationUpdate,
    SystemAIIntegrationResponse
)

logger = logging.getLogger(__name__)

class SystemAIIntegrationService:
    """系统AI整合设置服务"""

    @staticmethod
    async def get_integration_config(
        db: AsyncSession,
        project_id: Optional[uuid.UUID] = None,
        tenant_id: Optional[uuid.UUID] = None
    ) -> Optional[SystemAIIntegrationResponse]:
        """
        获取系统AI整合配置
        优先级: 项目级 > 租户级 > 全局级
        """
        try:
            # 构建查询条件，按优先级排序
            conditions = []
            
            if project_id:
                # 项目级配置 (最高优先级)
                conditions.append(
                    and_(
                        SystemAIIntegration.project_id == project_id,
                        SystemAIIntegration.tenant_id == tenant_id
                    )
                )
                
            if tenant_id:
                # 租户级配置
                conditions.append(
                    and_(
                        SystemAIIntegration.tenant_id == tenant_id,
                        SystemAIIntegration.project_id.is_(None)
                    )
                )
                
            # 全局配置 (最低优先级)
            conditions.append(
                and_(
                    SystemAIIntegration.tenant_id.is_(None),
                    SystemAIIntegration.project_id.is_(None)
                )
            )
            
            # 执行查询，按优先级查找第一个匹配的配置
            for condition in conditions:
                stmt = select(SystemAIIntegration).options(
                    selectinload(SystemAIIntegration.default_chat_model),
                    selectinload(SystemAIIntegration.default_vision_model),
                    selectinload(SystemAIIntegration.default_audio_model),
                    selectinload(SystemAIIntegration.default_embedding_model)
                ).where(condition)
                
                result = await db.execute(stmt)
                integration = result.scalar_one_or_none()
                
                if integration:
                    return await SystemAIIntegrationService._to_response(integration)
            
            # 如果没有找到任何配置，返回默认配置
            return await SystemAIIntegrationService._get_default_config(db)
            
        except Exception as e:
            logger.error(f"获取系统AI整合配置失败: {e}")
            return None

    @staticmethod
    async def create_integration_config(
        db: AsyncSession,
        config_data: SystemAIIntegrationCreate,
        project_id: Optional[uuid.UUID] = None,
        tenant_id: Optional[uuid.UUID] = None
    ) -> SystemAIIntegrationResponse:
        """创建系统AI整合配置"""
        try:
            # 检查是否已存在配置
            existing_config = await SystemAIIntegrationService._get_existing_config(
                db, project_id, tenant_id
            )
            
            if existing_config:
                # 如果已存在，更新现有配置
                update_data = SystemAIIntegrationUpdate(**config_data.model_dump(exclude_unset=True))
                return await SystemAIIntegrationService.update_integration_config(
                    db, existing_config.id, update_data
                )
            
            # 验证模型
            await SystemAIIntegrationService._validate_models(db, config_data)
            
            # 创建新配置
            config_dict = config_data.model_dump(exclude_unset=True)
            
            # 处理前端字段映射（如果有的话）
            if "default_chat_config_id" in config_dict:
                config_dict["default_chat_model_id"] = config_dict.pop("default_chat_config_id")
            if "default_vision_config_id" in config_dict:
                config_dict["default_vision_model_id"] = config_dict.pop("default_vision_config_id")
            if "default_audio_config_id" in config_dict:
                config_dict["default_audio_model_id"] = config_dict.pop("default_audio_config_id")
            if "default_embedding_config_id" in config_dict:
                config_dict["default_embedding_model_id"] = config_dict.pop("default_embedding_config_id")
            
            integration = SystemAIIntegration(
                tenant_id=tenant_id,
                project_id=project_id,
                **config_dict
            )
            
            db.add(integration)
            await db.commit()
            await db.refresh(integration)
            
            # 预加载关联模型
            await db.refresh(integration, [
                "default_chat_model",
                "default_vision_model", 
                "default_audio_model",
                "default_embedding_model"
            ])
            
            return await SystemAIIntegrationService._to_response(integration)
            
        except Exception as e:
            await db.rollback()
            logger.error(f"创建系统AI整合配置失败: {e}")
            raise

    @staticmethod
    async def update_integration_config(
        db: AsyncSession,
        config_id: uuid.UUID,
        config_data: SystemAIIntegrationUpdate
    ) -> SystemAIIntegrationResponse:
        """更新系统AI整合配置"""
        try:
            # 获取现有配置
            stmt = select(SystemAIIntegration).where(
                SystemAIIntegration.id == config_id
            )
            result = await db.execute(stmt)
            integration = result.scalar_one_or_none()
            
            if not integration:
                raise ValueError(f"系统AI整合配置 {config_id} 不存在")
            
            # 验证更新的模型
            if any([
                config_data.default_chat_model_id,
                config_data.default_vision_model_id,
                config_data.default_audio_model_id,
                config_data.default_embedding_model_id
            ]):
                await SystemAIIntegrationService._validate_models(db, config_data)
            
            # 更新配置
            update_data = config_data.model_dump(exclude_unset=True)
            for field, value in update_data.items():
                setattr(integration, field, value)
            
            await db.commit()
            await db.refresh(integration)
            
            # 预加载关联模型
            await db.refresh(integration, [
                "default_chat_model",
                "default_vision_model",
                "default_audio_model", 
                "default_embedding_model"
            ])
            
            return await SystemAIIntegrationService._to_response(integration)
            
        except Exception as e:
            await db.rollback()
            logger.error(f"更新系统AI整合配置失败: {e}")
            raise

    @staticmethod
    async def get_effective_model(
        db: AsyncSession,
        model_type: str,
        project_id: Optional[uuid.UUID] = None,
        tenant_id: Optional[uuid.UUID] = None
    ) -> Optional[AIModel]:
        """
        获取有效的默认模型
        
        Args:
            model_type: 模型类型 (chat, vision, audio, embedding)
        """
        try:
            config = await SystemAIIntegrationService.get_integration_config(
                db, project_id, tenant_id
            )
            
            if not config:
                return None
            
            # 处理字典类型的config
            if isinstance(config, dict):
                model_id_map = {
                    "chat": config.get("default_chat_model_id"),
                    "vision": config.get("default_vision_model_id"),
                    "audio": config.get("default_audio_model_id"),
                    "embedding": config.get("default_embedding_model_id")
                }
            else:
                # 处理对象类型的config
                model_id_map = {
                    "chat": config.default_chat_model_id,
                    "vision": config.default_vision_model_id,
                    "audio": config.default_audio_model_id,
                    "embedding": config.default_embedding_model_id
                }
            
            model_id = model_id_map.get(model_type)
            if not model_id:
                return None
                
            # 获取模型详情
            stmt = select(AIModel).where(AIModel.id == model_id)
            result = await db.execute(stmt)
            return result.scalar_one_or_none()
            
        except Exception as e:
            logger.error(f"获取有效默认模型失败: {e}")
            return None

    @staticmethod
    async def _validate_models(
        db: AsyncSession,
        config_data: SystemAIIntegrationCreate | SystemAIIntegrationUpdate
    ):
        """
        简化的模型验证：只验证模型存在性和基本状态
        前端已经根据能力筛选过了，这里只做安全边界检查
        """
        model_ids = [
            config_data.default_chat_model_id,
            config_data.default_vision_model_id,
            config_data.default_audio_model_id,
            config_data.default_embedding_model_id
        ]
        
        for model_id in model_ids:
            if model_id:
                stmt = select(AIModel).where(AIModel.id == model_id)
                result = await db.execute(stmt)
                model = result.scalar_one_or_none()
                
                if not model:
                    raise ValueError(f"模型 {model_id} 不存在")
                
                # 只检查基本状态，不再检查复杂的能力匹配
                # 因为前端已经根据能力（包括覆盖）正确筛选过了
                logger.info(f"验证模型 {model.name} (类型: {model.model_type}) 通过基础检查")

    @staticmethod
    async def _get_existing_config(
        db: AsyncSession,
        project_id: Optional[uuid.UUID],
        tenant_id: Optional[uuid.UUID]
    ) -> Optional[SystemAIIntegration]:
        """获取已存在的相同级别配置"""
        stmt = select(SystemAIIntegration).where(
            and_(
                SystemAIIntegration.project_id == project_id,
                SystemAIIntegration.tenant_id == tenant_id
            )
        )
        result = await db.execute(stmt)
        return result.scalar_one_or_none()

    @staticmethod
    async def _to_response(integration: SystemAIIntegration) -> SystemAIIntegrationResponse:
        """转换为响应Schema"""
        # 获取模型显示名称
        default_chat_model_name = None
        default_vision_model_name = None  
        default_audio_model_name = None
        default_embedding_model_name = None
        
        if hasattr(integration, 'default_chat_model') and integration.default_chat_model:
            default_chat_model_name = integration.default_chat_model.display_name or integration.default_chat_model.name
        
        if hasattr(integration, 'default_vision_model') and integration.default_vision_model:
            default_vision_model_name = integration.default_vision_model.display_name or integration.default_vision_model.name
            
        if hasattr(integration, 'default_audio_model') and integration.default_audio_model:
            default_audio_model_name = integration.default_audio_model.display_name or integration.default_audio_model.name
            
        if hasattr(integration, 'default_embedding_model') and integration.default_embedding_model:
            default_embedding_model_name = integration.default_embedding_model.display_name or integration.default_embedding_model.name
        
        # 创建响应数据
        response_data = SystemAIIntegrationResponse(
            id=integration.id,
            tenant_id=integration.tenant_id,
            project_id=integration.project_id,
            default_chat_model_id=integration.default_chat_model_id,
            default_vision_model_id=integration.default_vision_model_id,
            default_audio_model_id=integration.default_audio_model_id,
            default_embedding_model_id=integration.default_embedding_model_id,
            chat_temperature=integration.chat_temperature,
            chat_max_tokens=integration.chat_max_tokens,
            vision_temperature=integration.vision_temperature,
            vision_max_tokens=integration.vision_max_tokens,
            chat_enabled=integration.chat_enabled,
            vision_enabled=integration.vision_enabled,
            audio_enabled=integration.audio_enabled,
            embedding_enabled=integration.embedding_enabled,
            enable_fallback=integration.enable_fallback,
            request_timeout=integration.request_timeout,
            max_retries=integration.max_retries,
            default_chat_model_name=default_chat_model_name,
            default_vision_model_name=default_vision_model_name,
            default_audio_model_name=default_audio_model_name,
            default_embedding_model_name=default_embedding_model_name,
            created_at=integration.created_at.isoformat() if integration.created_at else None,
            updated_at=integration.updated_at.isoformat() if integration.updated_at else None
        )
        
        return response_data

    @staticmethod
    async def _get_default_config(db: AsyncSession):
        """获取默认配置"""
        return {
            "id": uuid.uuid4(),  # 临时ID
            "tenant_id": None,
            "project_id": None,
            "default_chat_model_id": None,
            "default_vision_model_id": None,
            "default_audio_model_id": None,
            "default_embedding_model_id": None,
            "chat_temperature": 0.7,
            "chat_max_tokens": None,
            "vision_temperature": 0.7,
            "vision_max_tokens": None,
            "chat_enabled": True,
            "vision_enabled": True,
            "audio_enabled": True,
            "embedding_enabled": True,
            "enable_fallback": True,
            "request_timeout": 30,
            "max_retries": 3,
            "created_at": None,
            "updated_at": None,
            "default_chat_model_name": None,
            "default_vision_model_name": None,
            "default_audio_model_name": None,
            "default_embedding_model_name": None,
            # 添加前端期望的字段名映射
            "default_chat_config_id": None,
            "default_vision_config_id": None,
            "default_audio_config_id": None,
            "default_embedding_config_id": None,
        }
    
    async def analyze_image_with_context(
        self,
        image_data: str,
        prompt: str,
        session_id: str = None,
        db: AsyncSession = None,
        project_id: uuid.UUID = None,
        user_id: uuid.UUID = None
    ) -> str:
        """
        建立视觉上下文 - 单次图片传输
        基于2025年最新视觉大模型技术
        """
        try:
            # 调用真正的AI视觉服务
            from services.ai.vision_service import AIVisionService
            
            if not db or not project_id or not user_id:
                raise ValueError("缺少必要参数：db, project_id, user_id")
            
            result = await AIVisionService.analyze_image(
                db=db,
                project_id=project_id,
                user_id=user_id,
                image_data=image_data,
                prompt=prompt
            )
            
            # 提取AI响应内容
            if isinstance(result, dict):
                if 'choices' in result and result['choices']:
                    return result['choices'][0]['message']['content']
                elif 'content' in result:
                    return result['content']
                elif 'response' in result:
                    return result['response']
                else:
                    return str(result)
            else:
                return str(result)
                
        except Exception as e:
            logger.error(f"建立视觉上下文失败: {e}")
            raise
    
    async def continue_vision_conversation(
        self,
        conversation_history: list,
        session_id: str = None,
        db: AsyncSession = None,
        project_id: uuid.UUID = None,
        user_id: uuid.UUID = None
    ) -> str:
        """
        继续视觉对话 - 基于已有的视觉上下文
        利用2025年Vision Remember技术
        """
        try:
            # 获取最新的用户消息
            latest_message = conversation_history[-1]['content'] if conversation_history else ""
            
            if not db or not project_id or not user_id:
                raise ValueError("缺少必要参数：db, project_id, user_id")
            
            # 调用真正的AI聊天服务进行多轮对话
            from services.ai.chat_service import AIChatService
            
            # 构建精简的对话上下文，避免token限制
            # 只保留最近的几轮对话和当前问题
            max_history = 3  # 最多保留3轮对话历史
            recent_history = conversation_history[-max_history:] if len(conversation_history) > max_history else conversation_history
            
            # 构建聊天消息格式
            chat_messages = []
            
            # 添加系统消息，说明这是基于视觉分析的对话
            chat_messages.append({
                "role": "system",
                "content": "你是一个AI助手，正在基于之前对图片的视觉分析进行多轮对话。请根据对话历史和当前问题提供准确的回答。"
            })
            
            # 添加对话历史
            for msg in recent_history:
                role = msg.get('role', 'user')
                content = msg.get('content', '')
                if role in ['user', 'assistant']:
                    chat_messages.append({
                        "role": role,
                        "content": content
                    })
            
            # 调用聊天服务
            result = await AIChatService.chat_completion(
                db=db,
                project_id=project_id,
                user_id=user_id,
                messages=chat_messages,
                temperature=0.7,
                max_tokens=2000
            )
            
            # 提取响应内容
            if isinstance(result, dict):
                if 'choices' in result and result['choices']:
                    return result['choices'][0]['message']['content']
                elif 'content' in result:
                    return result['content']
                elif 'response' in result:
                    return result['response']
                else:
                    return str(result)
            else:
                return str(result)
                
        except Exception as e:
            logger.error(f"继续视觉对话失败: {e}")
            raise 