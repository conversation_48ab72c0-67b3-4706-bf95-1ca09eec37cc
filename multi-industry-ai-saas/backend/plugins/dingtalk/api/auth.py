#!/usr/bin/env python
# -*- coding: utf-8 -*-

import logging
import uuid
from typing import Dict, Any, List, Optional
from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException, Body, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_

from db.database import get_db
from core.auth import get_current_user
from api.deps import get_current_project
from models.user import User, ThirdPartyAccount, UserActivity
from models.project import Project
from models.tenant import Tenant

from ..models.models import DingTalkUserMapping, DingTalkSettings
from ..utils.dingtalk_api import DingTalkAPI
from utils.json_utils import safe_json_response

# 初始化日志
logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter(
    tags=["钉钉插件用户信息"]
)

@router.get("/auth/user-info")
async def get_dingtalk_user_info(
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """获取用户的钉钉绑定信息和插件扩展信息"""
    try:
        # 查询第三方账号绑定（使用项目级别的通用绑定）
        third_party_query = select(ThirdPartyAccount).where(
            and_(
                ThirdPartyAccount.user_id == current_user.id,
                ThirdPartyAccount.platform == "dingtalk"
            )
        )
        result = await db.execute(third_party_query)
        third_party_account = result.scalar_one_or_none()
        
        # 查询钉钉插件扩展信息
        mapping_query = select(DingTalkUserMapping).where(
            and_(
                DingTalkUserMapping.user_id == current_user.id,
                DingTalkUserMapping.tenant_id == project.tenant_id,
                DingTalkUserMapping.project_id == project.id
            )
        )
        result = await db.execute(mapping_query)
        user_mapping = result.scalar_one_or_none()
        
        # 构建安全的响应数据
        response_data = {
            "user_id": str(current_user.id),  # 添加当前用户ID
            "username": current_user.username,  # 添加用户名
            "is_bound": third_party_account is not None,
            "third_party_account": None,
            "dingtalk_mapping": None
        }
        
        # 安全地处理第三方账号数据
        if third_party_account:
            response_data["third_party_account"] = {
                "id": str(third_party_account.id),
                "platform_user_id": third_party_account.platform_user_id,
                "platform_username": third_party_account.platform_username,
                "avatar_url": third_party_account.avatar_url,
                "access_token": third_party_account.access_token,  # 访问令牌信息
                "created_at": third_party_account.created_at.isoformat() if third_party_account.created_at else None,
                "updated_at": third_party_account.updated_at.isoformat() if third_party_account.updated_at else None
            }
        
        # 安全地处理钉钉映射数据
        if user_mapping:
            response_data["dingtalk_mapping"] = {
                "id": str(user_mapping.id),
                "dingtalk_user_id": user_mapping.dingtalk_user_id,
                "dingtalk_name": user_mapping.dingtalk_name,
                "dingtalk_mobile": user_mapping.dingtalk_mobile,
                "dingtalk_email": user_mapping.dingtalk_email,
                "dingtalk_department": user_mapping.dingtalk_department,
                "dingtalk_position": user_mapping.dingtalk_position,
                "created_at": user_mapping.created_at.isoformat() if user_mapping.created_at else None,
                "updated_at": user_mapping.updated_at.isoformat() if user_mapping.updated_at else None
            }
        
        return safe_json_response(response_data)
        
    except Exception as e:
        logger.error(f"获取钉钉用户信息失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取钉钉用户信息失败: {str(e)}"
        )

@router.post("/auth/sync-info")
async def sync_dingtalk_info(
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """同步钉钉用户信息到插件扩展表"""
    try:
        # 检查用户是否已绑定钉钉账号
        third_party_query = select(ThirdPartyAccount).where(
            and_(
                ThirdPartyAccount.user_id == current_user.id,
                ThirdPartyAccount.platform == "dingtalk"
            )
        )
        result = await db.execute(third_party_query)
        third_party_account = result.scalar_one_or_none()
        
        if not third_party_account:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="用户未绑定钉钉账号，请先在个人资料页面绑定钉钉账号"
            )
        
        # 获取钉钉配置
        from services.system_config import SystemConfigService
        configs = await SystemConfigService.get_configs_by_type(db, project.id, "third_party_login")
        config_dict = {config.config_key: config.config_value for config in configs}
        dingtalk_config = config_dict.get("dingtalk", {})
        
        app_key = dingtalk_config.get("app_key")
        app_secret = dingtalk_config.get("app_secret")
        
        if not app_key or not app_secret:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="钉钉配置不完整，无法同步用户信息"
            )
        
        # 使用钉钉API获取详细用户信息
        dingtalk_api = DingTalkAPI(app_key, app_secret)
        detailed_user_info = None
        
        # 尝试获取用户详细信息
        try:
            logger.info(f"尝试获取用户详情，platform_user_id: {third_party_account.platform_user_id}")
            detailed_user_info = await dingtalk_api.get_user_detail(third_party_account.platform_user_id)
            
            if detailed_user_info:
                logger.info("成功通过platform_user_id获取用户详情")
            else:
                # 如果API调用返回None，说明用户不存在或权限不足
                logger.warning("钉钉API返回空结果，用户可能不存在或权限不足")
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="无法从钉钉获取用户信息，请检查用户是否存在于钉钉组织中或应用权限配置"
                )
                
        except HTTPException:
            # 重新抛出HTTP异常
            raise
        except Exception as e:
            logger.error(f"调用钉钉API获取用户详情失败: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"同步钉钉用户信息失败: {str(e)}"
            )
        
        # 查询或创建钉钉用户映射
        mapping_query = select(DingTalkUserMapping).where(
            and_(
                DingTalkUserMapping.user_id == current_user.id,
                DingTalkUserMapping.tenant_id == project.tenant_id,
                DingTalkUserMapping.project_id == project.id
            )
        )
        result = await db.execute(mapping_query)
        user_mapping = result.scalar_one_or_none()
        
        if user_mapping:
            # 更新现有映射
            user_mapping.dingtalk_user_id = third_party_account.platform_user_id
            user_mapping.dingtalk_name = detailed_user_info.get("name", third_party_account.platform_username)
            user_mapping.dingtalk_mobile = detailed_user_info.get("mobile")
            user_mapping.dingtalk_email = detailed_user_info.get("email")
            user_mapping.updated_at = datetime.now()
        else:
            # 创建新的映射
            user_mapping = DingTalkUserMapping(
                id=uuid.uuid4(),
                user_id=current_user.id,
                tenant_id=project.tenant_id,
                project_id=project.id,
                dingtalk_user_id=third_party_account.platform_user_id,
                dingtalk_name=detailed_user_info.get("name", third_party_account.platform_username),
                dingtalk_mobile=detailed_user_info.get("mobile"),
                dingtalk_email=detailed_user_info.get("email"),
                created_at=datetime.now(),
                updated_at=datetime.now()
            )
            db.add(user_mapping)
        
        # 添加活动记录
        activity = UserActivity(
            user_id=current_user.id,
            project_id=project.id,
            activity_type="plugin_sync",
            description="同步了钉钉用户信息到插件",
            created_at=datetime.now()
        )
        db.add(activity)
        
        await db.commit()
        
        return {
            "success": True,
            "message": "钉钉用户信息同步成功",
            "data": {
                "dingtalk_name": user_mapping.dingtalk_name,
                "dingtalk_mobile": user_mapping.dingtalk_mobile,
                "dingtalk_email": user_mapping.dingtalk_email
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"同步钉钉用户信息失败: {str(e)}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"同步钉钉用户信息失败: {str(e)}"
        )

@router.get("/auth/user-info-simple")
async def get_dingtalk_user_info_simple(
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """
    获取用户的钉钉绑定信息（仅从数据库读取，适合操作面板使用）
    
    与 /auth/user-info 的区别：
    - 不调用钉钉API，仅从数据库读取
    - 适合用户管理操作面板等需要快速响应的场景
    - 减少API调用开销和复杂性
    """
    try:
        # 查询第三方账号绑定（使用项目级别的通用绑定）
        third_party_query = select(ThirdPartyAccount).where(
            and_(
                ThirdPartyAccount.user_id == current_user.id,
                ThirdPartyAccount.platform == "dingtalk"
            )
        )
        result = await db.execute(third_party_query)
        third_party_account = result.scalar_one_or_none()
        
        # 查询钉钉插件扩展信息
        mapping_query = select(DingTalkUserMapping).where(
            and_(
                DingTalkUserMapping.user_id == current_user.id,
                DingTalkUserMapping.tenant_id == project.tenant_id,
                DingTalkUserMapping.project_id == project.id
            )
        )
        result = await db.execute(mapping_query)
        user_mapping = result.scalar_one_or_none()
        
        # 构建响应数据（与用户管理页面数据结构保持一致）
        return {
            "success": True,
            "data": {
                "is_bound": third_party_account is not None,
                "has_third_party_account": third_party_account is not None,
                "has_dingtalk_mapping": user_mapping is not None,
                "third_party_account": {
                    "id": str(third_party_account.id) if third_party_account else None,
                    "platform": "dingtalk",
                    "platform_user_id": third_party_account.platform_user_id if third_party_account else None,
                    "platform_username": third_party_account.platform_username if third_party_account else None,

                    "avatar_url": third_party_account.avatar_url if third_party_account else None,
                    "created_at": third_party_account.created_at.isoformat() if third_party_account and third_party_account.created_at else None,
                    "updated_at": third_party_account.updated_at.isoformat() if third_party_account and third_party_account.updated_at else None
                } if third_party_account else None,
                "dingtalk_mapping": {
                    "id": str(user_mapping.id) if user_mapping else None,
                    "dingtalk_user_id": user_mapping.dingtalk_user_id if user_mapping else None,
                    "dingtalk_name": user_mapping.dingtalk_name if user_mapping else None,
                    "dingtalk_mobile": user_mapping.dingtalk_mobile if user_mapping else None,
                    "dingtalk_email": user_mapping.dingtalk_email if user_mapping else None,
                    "dingtalk_department": user_mapping.dingtalk_department if user_mapping else None,
                    "dingtalk_position": user_mapping.dingtalk_position if user_mapping else None,
                    "created_at": user_mapping.created_at.isoformat() if user_mapping and user_mapping.created_at else None,
                    "updated_at": user_mapping.updated_at.isoformat() if user_mapping and user_mapping.updated_at else None
                } if user_mapping else None,
                "message": "钉钉绑定信息获取成功（仅从数据库读取）"
            }
        }
        
    except Exception as e:
        logger.error(f"获取钉钉用户信息失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取钉钉用户信息失败: {str(e)}"
        )

@router.delete("/auth/clear-mapping")
async def clear_dingtalk_mapping(
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """清除钉钉插件用户映射（不影响账号绑定）"""
    try:
        # 查询钉钉用户映射
        mapping_query = select(DingTalkUserMapping).where(
            and_(
                DingTalkUserMapping.user_id == current_user.id,
                DingTalkUserMapping.tenant_id == project.tenant_id,
                DingTalkUserMapping.project_id == project.id
            )
        )
        result = await db.execute(mapping_query)
        user_mapping = result.scalar_one_or_none()
        
        if user_mapping:
            db.delete(user_mapping)
        
        # 添加活动记录
        activity = UserActivity(
            user_id=current_user.id,
            project_id=project.id,
            activity_type="plugin_clear",
            description="清除了钉钉插件用户映射",
            created_at=datetime.now()
        )
        db.add(activity)
        
        await db.commit()
        
        return {
            "success": True,
            "message": "钉钉插件用户映射已清除"
        }
        
    except Exception as e:
        logger.error(f"清除钉钉用户映射失败: {str(e)}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"清除钉钉用户映射失败: {str(e)}"
        ) 