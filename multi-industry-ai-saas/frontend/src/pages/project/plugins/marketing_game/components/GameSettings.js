import React, { useState, useEffect } from 'react';
import {
  Form,
  Input,
  Select,
  Switch,
  Radio,
  Divider,
  Tooltip,
  Space,
  Typography
} from 'antd';
import { InfoCircleOutlined } from '@ant-design/icons';

const { Option } = Select;
const { Text } = Typography;

/**
 * 游戏设置组件 - 根据游戏类型显示不同的设置选项
 */
const GameSettings = ({ gameType, form, initialValues }) => {
  const [enablePhoneVerification, setEnablePhoneVerification] = useState(
    initialValues?.settings?.enable_phone_verification || false
  );
  const [enableReceiptVerification, setEnableReceiptVerification] = useState(
    initialValues?.settings?.enable_receipt_verification || false
  );
  const [receiptVerificationType, setReceiptVerificationType] = useState(
    initialValues?.settings?.receipt_verification_type || 'both'
  );
  const [enableMemberIdentification, setEnableMemberIdentification] = useState(
    initialValues?.settings?.enable_member_identification || false
  );
  const [showWinnerName, setShowWinnerName] = useState(
    initialValues?.settings?.show_winner_name || false
  );

  const [enableScoring, setEnableScoring] = useState(
    initialValues?.settings?.enable_scoring || false
  );
  const [showResultCode, setShowResultCode] = useState(
    initialValues?.settings?.show_result_code || false
  );

  // 当表单值变化时更新状态
  useEffect(() => {
    const settings = form.getFieldValue('settings') || {};

    // 使用明确的布尔值转换，避免undefined被当作false
    setEnablePhoneVerification(settings.enable_phone_verification === true);
    setEnableReceiptVerification(settings.enable_receipt_verification === true);
    setReceiptVerificationType(settings.receipt_verification_type || 'both');
    setEnableMemberIdentification(settings.enable_member_identification === true);
    setShowWinnerName(settings.show_winner_name === true);

    setEnableScoring(settings.enable_scoring === true);
    setShowResultCode(settings.show_result_code === true);

    console.log("GameSettings - 当前表单设置值:", settings);
  }, [form]);

  // 当开关状态变化时更新表单值
  const updateFormSettings = (key, value) => {
    const settings = form.getFieldValue('settings') || {};

    // 创建新的设置对象，确保不会丢失其他设置
    const updatedSettings = {
      ...settings,
      [key]: value
    };

    // 更新表单值
    form.setFieldsValue({
      settings: updatedSettings
    });

    console.log(`更新设置 ${key}:`, value, "更新后的设置:", updatedSettings);
  };

  // 渲染通用设置
  const renderCommonSettings = () => (
    <>
      <Divider orientation="left">验证设置</Divider>
      <Space direction="vertical" style={{ width: '100%' }}>
        <div style={{ display: 'flex', alignItems: 'center', marginBottom: '16px' }}>
          <Text style={{ width: '150px' }}>启用手机号验证</Text>
          <Tooltip title="开启后，用户需要验证手机号才能参与游戏">
            <InfoCircleOutlined style={{ marginRight: '8px' }} />
          </Tooltip>
          <Switch
            checked={enablePhoneVerification}
            onChange={(checked) => {
              setEnablePhoneVerification(checked);
              updateFormSettings('enable_phone_verification', checked);
            }}
          />
        </div>

        <div style={{ display: 'flex', alignItems: 'center', marginBottom: '16px' }}>
          <Text style={{ width: '150px' }}>启用小票验证</Text>
          <Tooltip title="开启后，用户需要验证小票才能参与游戏">
            <InfoCircleOutlined style={{ marginRight: '8px' }} />
          </Tooltip>
          <Switch
            checked={enableReceiptVerification}
            onChange={(checked) => {
              setEnableReceiptVerification(checked);
              updateFormSettings('enable_receipt_verification', checked);
            }}
          />
        </div>

        {enableReceiptVerification && (
          <div style={{ marginLeft: '24px', marginBottom: '16px' }}>
            <Text style={{ width: '150px', display: 'inline-block' }}>小票验证方式</Text>
            <Select
              value={receiptVerificationType}
              style={{ width: '200px' }}
              onChange={(value) => {
                setReceiptVerificationType(value);
                updateFormSettings('receipt_verification_type', value);
              }}
            >
              <Option value="upload">上传图片</Option>
              <Option value="manual">手动输入</Option>
              <Option value="both">两种都支持</Option>
            </Select>
          </div>
        )}
      </Space>

      <Divider orientation="left">显示设置</Divider>
      <Space direction="vertical" style={{ width: '100%' }}>
        <div style={{ display: 'flex', alignItems: 'center', marginBottom: '16px' }}>
          <Text style={{ width: '150px' }}>显示中奖名单</Text>
          <Tooltip title="开启后，将显示中奖用户名称">
            <InfoCircleOutlined style={{ marginRight: '8px' }} />
          </Tooltip>
          <Switch
            checked={showWinnerName}
            onChange={(checked) => {
              setShowWinnerName(checked);
              updateFormSettings('show_winner_name', checked);
            }}
          />
        </div>

        <div style={{ display: 'flex', alignItems: 'center', marginBottom: '16px' }}>
          <Text style={{ width: '150px' }}>仅限会员参与</Text>
          <Tooltip title="开启后，仅允许会员参与游戏">
            <InfoCircleOutlined style={{ marginRight: '8px' }} />
          </Tooltip>
          <Switch
            checked={enableMemberIdentification}
            onChange={(checked) => {
              setEnableMemberIdentification(checked);
              updateFormSettings('enable_member_identification', checked);
            }}
          />
        </div>



        <div style={{ display: 'flex', alignItems: 'center', marginBottom: '16px' }}>
          <Text style={{ width: '150px' }}>显示二维码</Text>
          <Tooltip title="开启后，将显示结果二维码">
            <InfoCircleOutlined style={{ marginRight: '8px' }} />
          </Tooltip>
          <Switch
            checked={showResultCode}
            onChange={(checked) => {
              setShowResultCode(checked);
              updateFormSettings('show_result_code', checked);
            }}
          />
        </div>
      </Space>
    </>
  );

  // 渲染抽奖游戏特定设置
  const renderLuckyDrawSettings = () => (
    <>
      {renderCommonSettings()}
    </>
  );

  // 渲染刮刮卡特定设置
  const renderScratchCardSettings = () => (
    <>
      {renderCommonSettings()}
      <Divider orientation="left">刮刮卡设置</Divider>
      <Form.Item
        name={['settings', 'scratch_card_style']}
        label="刮刮卡样式"
        initialValue={initialValues?.settings?.scratch_card_style || 'classic'}
      >
        <Radio.Group>
          <Radio value="classic">经典样式</Radio>
          <Radio value="modern">现代样式</Radio>
          <Radio value="festive">节日样式</Radio>
        </Radio.Group>
      </Form.Item>
    </>
  );

  // 渲染大转盘特定设置
  const renderWheelOfFortuneSettings = () => (
    <>
      {renderCommonSettings()}
      <Divider orientation="left">大转盘设置</Divider>
      <Form.Item
        name={['settings', 'wheel_style']}
        label="转盘样式"
        initialValue={initialValues?.settings?.wheel_style || 'classic'}
      >
        <Radio.Group>
          <Radio value="classic">经典样式</Radio>
          <Radio value="modern">现代样式</Radio>
          <Radio value="festive">节日样式</Radio>
        </Radio.Group>
      </Form.Item>
    </>
  );

  // 渲染闯关游戏特定设置
  const renderAdventureGameSettings = () => (
    <>
      {renderCommonSettings()}
      <Divider orientation="left">闯关游戏设置</Divider>
      <div style={{ display: 'flex', alignItems: 'center', marginBottom: '16px' }}>
        <Text style={{ width: '150px' }}>启用积分</Text>
        <Tooltip title="开启后，用户完成关卡可获得积分">
          <InfoCircleOutlined style={{ marginRight: '8px' }} />
        </Tooltip>
        <Switch
          checked={enableScoring}
          onChange={(checked) => {
            setEnableScoring(checked);
            updateFormSettings('enable_scoring', checked);
          }}
        />
      </div>

      <Form.Item
        name={['settings', 'difficulty']}
        label="游戏难度"
        initialValue={initialValues?.settings?.difficulty || 'medium'}
      >
        <Radio.Group>
          <Radio value="easy">简单</Radio>
          <Radio value="medium">中等</Radio>
          <Radio value="hard">困难</Radio>
        </Radio.Group>
      </Form.Item>

      <Form.Item
        name={['settings', 'time_limit']}
        label="时间限制(秒)"
        initialValue={initialValues?.settings?.time_limit || 60}
      >
        <Input type="number" min={10} max={300} />
      </Form.Item>
    </>
  );

  // 根据游戏类型渲染不同的设置
  const renderSettingsByGameType = () => {
    switch (gameType) {
      case 'lucky_draw':
        return renderLuckyDrawSettings();
      case 'scratch_card':
        return renderScratchCardSettings();
      case 'wheel_of_fortune':
        return renderWheelOfFortuneSettings();
      case 'adventure_game':
        return renderAdventureGameSettings();
      default:
        return renderCommonSettings();
    }
  };

  return (
    <div className="game-settings">
      {renderSettingsByGameType()}
    </div>
  );
};

export default GameSettings;
