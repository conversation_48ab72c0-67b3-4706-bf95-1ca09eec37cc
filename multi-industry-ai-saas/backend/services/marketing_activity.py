#!/usr/bin/env python
# -*- coding: utf-8 -*-

from typing import List, Optional, Tuple, Dict, Any, Union
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import or_, and_, func, desc
from datetime import datetime, timedelta, date, timezone
import uuid
import logging
from collections import defaultdict

from models.marketing_activity import MarketingActivity
from models.user import User
from models.store import Store
from models.project import Project

logger = logging.getLogger(__name__)

class MarketingActivityService:
    """
    营销活动服务
    """
    
    @staticmethod
    async def get_marketing_activities(
        db: AsyncSession,
        project_id: uuid.UUID,
        skip: int = 0,
        limit: int = 100,
        status: Optional[str] = None,
        type: Optional[str] = None,
        store_id: Optional[uuid.UUID] = None,
        search: Optional[str] = None,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
    ) -> Tuple[List[Dict[str, Any]], int]:
        """
        获取营销活动列表
        """
        try:
            # 构建查询
            query = select(MarketingActivity).where(MarketingActivity.project_id == project_id)
            
            # 应用过滤条件
            if status:
                query = query.where(MarketingActivity.status == status)
            
            if type:
                query = query.where(MarketingActivity.type == type)
            
            if store_id:
                # 查询适用于特定门店的活动
                query = query.where(
                    or_(
                        MarketingActivity.is_all_stores == True,
                        MarketingActivity.target_stores.contains([str(store_id)])
                    )
                )
            
            if search:
                query = query.where(
                    or_(
                        MarketingActivity.title.ilike(f"%{search}%"),
                        MarketingActivity.description.ilike(f"%{search}%")
                    )
                )
            
            if start_date:
                query = query.where(MarketingActivity.start_date >= start_date)
            
            if end_date:
                query = query.where(MarketingActivity.end_date <= end_date)
            
            # 计算总数
            count_query = select(func.count()).select_from(query.subquery())
            total = await db.scalar(count_query)
            
            # 应用分页
            query = query.order_by(desc(MarketingActivity.created_at)).offset(skip).limit(limit)
            
            # 执行查询
            result = await db.execute(query)
            activities = result.scalars().all()
            
            # 获取门店信息
            store_ids = set()
            for activity in activities:
                if activity.target_stores and not activity.is_all_stores:
                    store_ids.update(activity.target_stores)
            
            stores_map = {}
            if store_ids:
                stores_query = select(Store).where(Store.id.in_(store_ids))
                stores_result = await db.execute(stores_query)
                stores = stores_result.scalars().all()
                stores_map = {str(store.id): store.name for store in stores}
            
            # 获取用户信息
            user_ids = set()
            for activity in activities:
                if activity.created_by:
                    user_ids.add(activity.created_by)
            
            users_map = {}
            if user_ids:
                users_query = select(User).where(User.id.in_(user_ids))
                users_result = await db.execute(users_query)
                users = users_result.scalars().all()
                users_map = {str(user.id): user.name or user.username for user in users}
            
            # 构建响应
            activities_data = []
            for activity in activities:
                activity_dict = {
                    "id": activity.id,
                    "project_id": activity.project_id,
                    "title": activity.title,
                    "description": activity.description,
                    "type": activity.type,
                    "status": activity.status,
                    "start_date": activity.start_date,
                    "end_date": activity.end_date,
                    "discount_rate": activity.discount_rate,
                    "image_url": activity.image_url,
                    "target_stores": activity.target_stores,
                    "is_all_stores": activity.is_all_stores,
                    "rules": activity.rules,
                    "attachments": activity.attachments,
                    "custom_fields": activity.custom_fields,
                    "created_by": activity.created_by,
                    "updated_by": activity.updated_by,
                    "created_at": activity.created_at,
                    "updated_at": activity.updated_at,
                }
                
                # 添加门店名称
                if activity.target_stores and not activity.is_all_stores:
                    activity_dict["store_names"] = [
                        stores_map.get(str(store_id), "未知门店") 
                        for store_id in activity.target_stores
                    ]
                elif activity.is_all_stores:
                    activity_dict["store_names"] = ["所有门店"]
                else:
                    activity_dict["store_names"] = []
                
                # 添加创建者名称
                if activity.created_by:
                    activity_dict["creator_name"] = users_map.get(str(activity.created_by), "未知用户")
                
                activities_data.append(activity_dict)
            
            return activities_data, total
            
        except Exception as e:
            logger.error(f"获取营销活动列表失败: {str(e)}")
            raise
    
    @staticmethod
    async def get_marketing_activity(
        db: AsyncSession,
        project_id: uuid.UUID,
        activity_id: uuid.UUID
    ) -> Optional[Dict[str, Any]]:
        """
        获取营销活动详情
        """
        try:
            # 构建查询
            query = select(MarketingActivity).where(
                and_(
                    MarketingActivity.project_id == project_id,
                    MarketingActivity.id == activity_id
                )
            )
            
            # 执行查询
            result = await db.execute(query)
            activity = result.scalars().first()
            
            if not activity:
                return None
            
            # 获取门店信息
            store_names = []
            if activity.target_stores and not activity.is_all_stores:
                stores_query = select(Store).where(Store.id.in_(activity.target_stores))
                stores_result = await db.execute(stores_query)
                stores = stores_result.scalars().all()
                store_names = [store.name for store in stores]
            elif activity.is_all_stores:
                store_names = ["所有门店"]
            
            # 获取创建者信息
            creator_name = None
            if activity.created_by:
                user_query = select(User).where(User.id == activity.created_by)
                user_result = await db.execute(user_query)
                user = user_result.scalars().first()
                if user:
                    creator_name = user.name or user.username
            
            # 构建响应
            activity_dict = {
                "id": activity.id,
                "project_id": activity.project_id,
                "title": activity.title,
                "description": activity.description,
                "type": activity.type,
                "status": activity.status,
                "start_date": activity.start_date,
                "end_date": activity.end_date,
                "discount_rate": activity.discount_rate,
                "image_url": activity.image_url,
                "target_stores": activity.target_stores,
                "is_all_stores": activity.is_all_stores,
                "rules": activity.rules,
                "attachments": activity.attachments,
                "custom_fields": activity.custom_fields,
                "created_by": activity.created_by,
                "updated_by": activity.updated_by,
                "created_at": activity.created_at,
                "updated_at": activity.updated_at,
                "store_names": store_names,
                "creator_name": creator_name
            }
            
            return activity_dict
            
        except Exception as e:
            logger.error(f"获取营销活动详情失败: {str(e)}")
            raise
    
    @staticmethod
    async def create_marketing_activity(
        db: AsyncSession,
        activity_data: Dict[str, Any],
        project_id: uuid.UUID,
        user_id: uuid.UUID
    ) -> MarketingActivity:
        """
        创建营销活动
        """
        try:
            # 创建活动
            activity = MarketingActivity(
                project_id=project_id,
                created_by=user_id,
                **activity_data
            )
            
            db.add(activity)
            await db.commit()
            await db.refresh(activity)
            
            return activity
            
        except Exception as e:
            await db.rollback()
            logger.error(f"创建营销活动失败: {str(e)}")
            raise
    
    @staticmethod
    async def update_marketing_activity(
        db: AsyncSession,
        project_id: uuid.UUID,
        activity_id: uuid.UUID,
        activity_data: Dict[str, Any],
        user_id: uuid.UUID
    ) -> Optional[MarketingActivity]:
        """
        更新营销活动
        """
        try:
            # 查询活动
            query = select(MarketingActivity).where(
                and_(
                    MarketingActivity.project_id == project_id,
                    MarketingActivity.id == activity_id
                )
            )
            
            result = await db.execute(query)
            activity = result.scalars().first()
            
            if not activity:
                return None
            
            # 更新活动
            for key, value in activity_data.items():
                if hasattr(activity, key) and value is not None:
                    setattr(activity, key, value)
            
            # 设置更新者
            activity.updated_by = user_id
            
            await db.commit()
            await db.refresh(activity)
            
            return activity
            
        except Exception as e:
            await db.rollback()
            logger.error(f"更新营销活动失败: {str(e)}")
            raise
    
    @staticmethod
    async def delete_marketing_activity(
        db: AsyncSession,
        project_id: uuid.UUID,
        activity_id: uuid.UUID
    ) -> bool:
        """
        删除营销活动
        """
        try:
            # 查询活动
            query = select(MarketingActivity).where(
                and_(
                    MarketingActivity.project_id == project_id,
                    MarketingActivity.id == activity_id
                )
            )
            
            result = await db.execute(query)
            activity = result.scalars().first()
            
            if not activity:
                return False
            
            # 删除活动
            await db.delete(activity)
            await db.commit()
            
            return True
            
        except Exception as e:
            await db.rollback()
            logger.error(f"删除营销活动失败: {str(e)}")
            raise
    
    @staticmethod
    async def update_marketing_activity_status(
        db: AsyncSession,
        project_id: uuid.UUID,
        activity_id: uuid.UUID,
        status: str,
        user_id: uuid.UUID
    ) -> Optional[MarketingActivity]:
        """
        更新营销活动状态
        """
        try:
            # 查询活动
            query = select(MarketingActivity).where(
                and_(
                    MarketingActivity.project_id == project_id,
                    MarketingActivity.id == activity_id
                )
            )
            
            result = await db.execute(query)
            activity = result.scalars().first()
            
            if not activity:
                return None
            
            # 更新状态
            activity.status = status
            activity.updated_by = user_id
            
            await db.commit()
            await db.refresh(activity)
            
            return activity
            
        except Exception as e:
            await db.rollback()
            logger.error(f"更新营销活动状态失败: {str(e)}")
            raise
    
    @staticmethod
    async def get_marketing_activity_stats(
        db: AsyncSession,
        project_id: uuid.UUID
    ) -> Dict[str, int]:
        """
        获取营销活动统计数据
        """
        try:
            # 构建查询
            query = select(
                MarketingActivity.status,
                func.count(MarketingActivity.id)
            ).where(
                MarketingActivity.project_id == project_id
            ).group_by(
                MarketingActivity.status
            )
            
            # 执行查询
            result = await db.execute(query)
            stats_data = result.all()
            
            # 初始化统计数据
            stats = {
                "total": 0,
                "draft": 0,
                "pending": 0,
                "active": 0,
                "completed": 0,
                "cancelled": 0
            }
            
            # 填充统计数据
            for status, count in stats_data:
                if status in stats:
                    stats[status] = count
                stats["total"] += count
            
            return stats
            
        except Exception as e:
            logger.error(f"获取营销活动统计数据失败: {str(e)}")
            raise
