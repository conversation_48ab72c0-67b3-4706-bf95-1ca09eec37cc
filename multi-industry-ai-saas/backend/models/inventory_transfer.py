#!/usr/bin/env python
# -*- coding: utf-8 -*-

from sqlalchemy import Column, String, Integer, Float, Text, Boolean, ForeignKey, DateTime, UniqueConstraint, Enum
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid
from datetime import datetime, timezone

from db.database import Base

class StoreInventoryTransfer(Base):
    """门店库存调拨模型"""
    __tablename__ = "store_inventory_transfers"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id", ondelete="SET NULL"), nullable=True, index=True)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id", ondelete="CASCADE"), nullable=False, index=True)

    transfer_number = Column(String(50), nullable=False, index=True, comment="调拨单号")
    transfer_type = Column(
        Enum("store", "third_party", name="transfer_type"),
        nullable=False,
        comment="调拨类型: store-门店配货, third_party-三方调拨"
    )

    source_warehouse_id = Column(UUID(as_uuid=True), ForeignKey("warehouses.id", ondelete="SET NULL"), nullable=True, index=True)
    destination_id = Column(UUID(as_uuid=True), ForeignKey("stores.id", ondelete="SET NULL"), nullable=True, index=True)
    destination_name = Column(String(200), nullable=True, comment="调拨目标名称，用于三方调拨")

    transfer_date = Column(DateTime, nullable=False, default=datetime.now(timezone.utc), comment="调拨日期")
    status = Column(
        Enum("pending", "processing", "completed", "cancelled", name="transfer_status"),
        nullable=False,
        default="pending",
        comment="状态: pending-待处理, processing-处理中, completed-已完成, cancelled-已取消"
    )

    payment_method = Column(
        Enum("cash", "credit", "warehouse_collect", "other", name="payment_method"),
        nullable=True,
        comment="支付方式: cash-现金支付, credit-欠款代收, warehouse_collect-仓管代收, other-其他方式"
    )

    total_amount = Column(Float, nullable=False, default=0.0, comment="总金额")
    total_items = Column(Integer, nullable=False, default=0, comment="商品总数")

    notes = Column(Text, nullable=True, comment="备注")

    created_by = Column(UUID(as_uuid=True), ForeignKey("users.id", ondelete="SET NULL"), nullable=True, comment="创建人ID")
    updated_by = Column(UUID(as_uuid=True), ForeignKey("users.id", ondelete="SET NULL"), nullable=True, comment="更新人ID")
    created_at = Column(DateTime, nullable=False, default=datetime.now(timezone.utc), comment="创建时间")
    updated_at = Column(DateTime, nullable=False, default=datetime.now(timezone.utc), onupdate=datetime.now(timezone.utc), comment="更新时间")

    # 关联关系
    tenant = relationship("Tenant", backref="store_inventory_transfers")
    project = relationship("Project", backref="store_inventory_transfers")
    source_warehouse = relationship("Warehouse", foreign_keys=[source_warehouse_id])
    destination_store = relationship("Store", foreign_keys=[destination_id])
    creator = relationship("User", foreign_keys=[created_by])
    updater = relationship("User", foreign_keys=[updated_by])
    items = relationship("StoreInventoryTransferItem", back_populates="transfer", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<StoreInventoryTransfer(id={self.id}, transfer_number='{self.transfer_number}')>"


class StoreInventoryTransferItem(Base):
    """门店库存调拨明细模型"""
    __tablename__ = "store_inventory_transfer_items"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    transfer_id = Column(UUID(as_uuid=True), ForeignKey("store_inventory_transfers.id", ondelete="CASCADE"), nullable=False, index=True)
    product_id = Column(UUID(as_uuid=True), ForeignKey("products.id", ondelete="SET NULL"), nullable=True, index=True)

    product_name = Column(String(200), nullable=False, comment="商品名称")
    product_code = Column(String(50), nullable=True, comment="商品编码")
    specification = Column(String(200), nullable=True, comment="规格")
    unit = Column(String(20), nullable=True, comment="单位")

    quantity = Column(Integer, nullable=False, comment="数量")
    price = Column(Float, nullable=True, comment="单价")
    amount = Column(Float, nullable=True, comment="金额")

    notes = Column(Text, nullable=True, comment="备注")

    # 关联关系
    transfer = relationship("StoreInventoryTransfer", back_populates="items")
    product = relationship("Product")

    def __repr__(self):
        return f"<StoreInventoryTransferItem(id={self.id}, product_name='{self.product_name}')>"
