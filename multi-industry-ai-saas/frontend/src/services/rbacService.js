/**
 * 基于角色的访问控制 (RBAC) 服务
 * 提供角色和权限管理功能
 */

// 角色层次结构 - 定义角色之间的继承关系
const ROLE_HIERARCHY = {
  // 系统级角色
  system_admin: ['*'], // 系统管理员拥有所有权限
  super_admin: ['*'], // 超级管理员拥有所有权限
  service_provider_admin: ['service_provider', '*_service_provider'], // 服务商管理员
  tenant_admin: ['tenant', '*_tenant', 'project_admin'], // 租户管理员继承项目管理员权限

  // 项目级角色
  project_admin: ['*_project', 'store_admin', 'inventory_admin', 'purchase_admin', 'operation_admin', 'finance_admin'], // 项目管理员拥有所有项目级权限

  // 功能模块角色
  store_admin: ['store', '*_store'], // 门店管理员
  inventory_admin: ['inventory', '*_inventory'], // 仓库管理员
  purchase_admin: ['purchase', '*_purchase'], // 采购管理员
  operation_admin: ['operation', '*_operation'], // 运营管理员
  finance_admin: ['finance', '*_finance'], // 财务管理员

  // 普通角色
  store_staff: ['view_store'], // 门店员工
  inventory_staff: ['view_inventory'], // 仓库员工
  purchase_staff: ['view_purchase'], // 采购员工
  operation_staff: ['view_operation'], // 运营员工
  finance_staff: ['view_finance'], // 财务员工
};

// 角色到仪表盘的映射（暂时保留，未来可能使用）
// const ROLE_DASHBOARD_MAP = {
//   system_admin: '/admin/dashboard',
//   super_admin: '/admin/dashboard',
//   service_provider_admin: '/service-provider/dashboard',
//   tenant_admin: '/tenant/projects',
//   project_admin: '/project/dashboard',
//   store_admin: '/project/store/dashboard',
//   inventory_admin: '/project/inventory/dashboard',
//   inventory_staff: '/project/inventory/dashboard',
//   purchase_admin: '/project/purchase/dashboard',
//   purchase_staff: '/project/purchase/dashboard',
//   operation_admin: '/project/operation/dashboard',
//   operation_staff: '/project/operation/dashboard',
//   finance_admin: '/project/finance/dashboard',
//   finance_staff: '/project/finance/dashboard',
// };

// 路径到所需角色的映射
const PATH_ROLE_MAP = {
  '/project/dashboard': ['project_admin', 'tenant_admin'],
  '/project/store/dashboard': ['store_admin', 'store_staff', 'project_admin', 'tenant_admin'],
  '/project/inventory/dashboard': ['inventory_admin', 'inventory_staff', 'project_admin', 'tenant_admin'],
  '/project/purchase/dashboard': ['purchase_admin', 'purchase_staff', 'project_admin', 'tenant_admin'],
  '/project/operation/dashboard': ['operation_admin', 'operation_staff', 'project_admin', 'tenant_admin'],
  '/project/finance/dashboard': ['finance_admin', 'finance_staff', 'project_admin', 'tenant_admin'],
};

/**
 * 获取用户的所有角色（包括继承的角色）
 * @param {Object} userData - 用户数据
 * @returns {Array} - 用户的所有角色
 */
const getUserRoles = (userData) => {
  if (!userData) return [];

  console.log('RBAC - getUserRoles - 用户数据:', userData);

  const roles = [];

  // 添加系统级角色
  if (userData.is_system_admin) roles.push('system_admin');
  if (userData.is_super_admin) roles.push('super_admin');
  if (userData.is_service_provider_admin) roles.push('service_provider_admin');
  if (userData.is_tenant_admin) roles.push('tenant_admin');
  if (userData.is_project_admin) roles.push('project_admin');

  // 添加项目角色
  const projectRole = userData.project_role || '';
  if (projectRole) {
    roles.push(projectRole);

    // 处理特殊情况 - 角色名称可能包含其他角色
    if (typeof projectRole === 'string') {
      const lowerCaseRole = projectRole.toLowerCase();
      if (lowerCaseRole.includes('purchase')) roles.push('purchase');
      if (lowerCaseRole.includes('inventory')) roles.push('inventory');
      if (lowerCaseRole.includes('operation')) roles.push('operation');
      if (lowerCaseRole.includes('finance')) roles.push('finance');
      if (lowerCaseRole.includes('store')) roles.push('store');
    }
  }

  console.log('RBAC - getUserRoles - 用户角色（不包括继承）:', roles);

  // 添加继承的角色
  const inheritedRoles = new Set(roles);
  let hasNewRoles = true;

  // 继续添加继承的角色，直到没有新角色
  while (hasNewRoles) {
    hasNewRoles = false;
    const currentRoles = [...inheritedRoles];

    for (const role of currentRoles) {
      if (ROLE_HIERARCHY[role]) {
        for (const inheritedRole of ROLE_HIERARCHY[role]) {
          if (!inheritedRoles.has(inheritedRole)) {
            inheritedRoles.add(inheritedRole);
            hasNewRoles = true;
          }
        }
      }
    }
  }

  console.log('RBAC - getUserRoles - 用户角色（包括继承）:', [...inheritedRoles]);

  return [...inheritedRoles];
};

/**
 * 检查用户是否有特定角色
 * @param {Object} userData - 用户数据
 * @param {string|Array} requiredRoles - 所需角色（单个角色或角色数组）
 * @returns {boolean} - 用户是否有所需角色
 */
const hasRole = (userData, requiredRoles) => {
  if (!userData) return false;

  console.log('RBAC - 检查角色:', { requiredRoles, userData });

  // 系统管理员、超级管理员、租户管理员和项目管理员拥有所有权限
  if (userData.is_system_admin || userData.is_super_admin ||
      userData.is_tenant_admin || userData.is_project_admin) {
    console.log('RBAC - 用户是管理员，拥有所有权限');
    return true;
  }

  // 获取用户的项目角色
  const projectRole = userData.project_role;
  console.log('RBAC - 用户项目角色:', projectRole);

  // 如果没有项目角色，返回 false
  if (!projectRole) {
    console.log('RBAC - 用户没有项目角色');
    return false;
  }

  // 单个角色检查
  if (typeof requiredRoles === 'string') {
    // 精确匹配
    if (projectRole === requiredRoles) {
      console.log(`RBAC - 用户角色 ${projectRole} 精确匹配所需角色 ${requiredRoles}`);
      return true;
    }

    // 包含匹配
    if (typeof projectRole === 'string' &&
        typeof requiredRoles === 'string' &&
        (projectRole.toLowerCase().includes(requiredRoles.toLowerCase()) ||
         requiredRoles.toLowerCase().includes(projectRole.toLowerCase()))) {
      console.log(`RBAC - 用户角色 ${projectRole} 包含匹配所需角色 ${requiredRoles}`);
      return true;
    }

    return false;
  }

  // 数组角色检查 - 只要满足其中一个角色即可
  if (Array.isArray(requiredRoles)) {
    // 精确匹配
    if (requiredRoles.includes(projectRole)) {
      console.log(`RBAC - 用户角色 ${projectRole} 精确匹配所需角色数组中的一个`);
      return true;
    }

    // 包含匹配
    for (const role of requiredRoles) {
      if (typeof projectRole === 'string' &&
          typeof role === 'string' &&
          (projectRole.toLowerCase().includes(role.toLowerCase()) ||
           role.toLowerCase().includes(projectRole.toLowerCase()))) {
        console.log(`RBAC - 用户角色 ${projectRole} 包含匹配所需角色 ${role}`);
        return true;
      }
    }

    return false;
  }

  return false;
};

/**
 * 获取用户应该跳转的仪表盘路径
 * @param {Object} userData - 用户数据
 * @returns {string} - 仪表盘路径
 */
const getDashboardPath = (userData) => {
  if (!userData) return '/login';

  console.log('RBAC - getDashboardPath - 用户数据:', userData);

  // 确保项目ID被正确处理
  let projectId = localStorage.getItem('project_id');

  // 如果localStorage中没有项目ID，但用户数据中有，则使用用户数据中的项目ID
  if (!projectId && userData.project_id) {
    projectId = userData.project_id;
    localStorage.setItem('project_id', projectId);
    console.log('RBAC - 从用户数据中获取到项目ID:', projectId);
  }

  // 如果用户数据中没有项目角色信息，尝试从API获取
  if (!userData.project_role && projectId) {
    console.log('RBAC - 用户数据中没有项目角色信息，但有项目ID，可能需要重新获取用户信息');
    // 这里不直接调用API，而是提示可能需要重新获取用户信息
  }

  // 系统级角色优先级检查
  if (userData.is_system_admin || userData.is_super_admin) {
    console.log('RBAC - 用户是系统管理员，跳转到系统管理仪表盘');
    return '/admin/dashboard';
  }

  if (userData.is_service_provider_admin) {
    console.log('RBAC - 用户是服务商管理员，跳转到服务商仪表盘');
    return '/service-provider/dashboard';
  }

  if (userData.is_tenant_admin) {
    // 如果有项目ID，进入项目仪表盘，否则进入项目选择页面
    if (projectId) {
      console.log('RBAC - 用户是租户管理员且有项目ID，跳转到项目仪表盘');
      return '/project/dashboard';
    } else {
      console.log('RBAC - 用户是租户管理员但没有项目ID，跳转到项目选择页面');
      return '/tenant/projects';
    }
  }

  // 检查是否有项目ID
  if (!projectId) {
    // 检查当前路径，如果已经在项目选择页面，则不再重定向
    const currentPath = window.location.pathname;
    if (currentPath === '/tenant/projects') {
      console.log('RBAC - 已经在项目选择页面，不再重定向');
      // 重置重定向计数器
      localStorage.setItem('redirect_attempts', '0');
      return '/tenant/projects'; // 保持在当前页面
    }

    // 检查是否有特定角色标记，避免无限重定向
    const redirectAttempts = parseInt(localStorage.getItem('redirect_attempts') || '0');

    // 如果已经尝试重定向超过一定次数，直接进入项目选择页面
    if (redirectAttempts > 2) {
      console.log('RBAC - 检测到多次重定向尝试，直接进入项目选择页面');
      localStorage.setItem('redirect_attempts', '0'); // 重置计数器
      return '/tenant/projects';
    }

    // 增加重定向尝试计数
    localStorage.setItem('redirect_attempts', (redirectAttempts + 1).toString());

    console.log('RBAC - 用户没有项目ID，跳转到项目选择页面');
    return '/tenant/projects';
  }

  // 重置重定向计数器
  localStorage.removeItem('redirect_attempts');

  if (userData.is_project_admin) {
    console.log('RBAC - 用户是项目管理员，跳转到项目仪表盘');
    return '/project/dashboard';
  }

  // 项目角色优先级检查 - 直接检查 project_role
  const projectRole = userData.project_role;
  console.log('RBAC - 项目角色:', projectRole);

  // 明确的角色匹配 - 采购角色
  if (projectRole === 'purchase_admin' || projectRole === 'purchase_staff' ||
      (typeof projectRole === 'string' && projectRole.toLowerCase().includes('purchase'))) {
    console.log('RBAC - 用户是采购角色，跳转到采购仪表盘');
    return '/project/purchase/dashboard';
  }

  // 明确的角色匹配 - 仓储角色
  if (projectRole === 'inventory_admin' || projectRole === 'inventory_staff' ||
      (typeof projectRole === 'string' && projectRole.toLowerCase().includes('inventory'))) {
    console.log('RBAC - 用户是仓储角色，跳转到仓储仪表盘');
    return '/project/inventory/dashboard';
  }

  // 明确的角色匹配 - 运营角色
  if (projectRole === 'operation_admin' || projectRole === 'operation_staff' ||
      (typeof projectRole === 'string' && projectRole.toLowerCase().includes('operation'))) {
    console.log('RBAC - 用户是运营角色，跳转到运营仪表盘');
    return '/project/operation/dashboard';
  }

  // 明确的角色匹配 - 财务角色
  if (projectRole === 'finance_admin' || projectRole === 'finance_staff' ||
      (typeof projectRole === 'string' && projectRole.toLowerCase().includes('finance'))) {
    console.log('RBAC - 用户是财务角色，跳转到财务仪表盘');
    return '/project/finance/dashboard';
  }

  // 明确的角色匹配 - 门店角色
  if (projectRole === 'store_admin' ||
      (typeof projectRole === 'string' && projectRole.toLowerCase().includes('store'))) {
    console.log('RBAC - 用户是门店角色，跳转到门店仪表盘');
    return '/project/store/dashboard';
  }

  // 如果没有匹配的角色，默认返回门店仪表盘
  console.log('RBAC - 没有匹配的角色，默认跳转到门店仪表盘');
  return '/project/store/dashboard';
};

/**
 * 检查用户是否有权限访问特定路径
 * @param {Object} userData - 用户数据
 * @param {string} path - 路径
 * @returns {boolean} - 用户是否有权限访问
 */
const canAccessPath = (userData, path) => {
  if (!userData) return false;

  console.log('RBAC - 检查路径访问权限:', { path, userData });

  // 获取用户的所有角色（包括继承的角色）
  const userRoles = getUserRoles(userData);
  console.log('RBAC - 用户角色（包括继承）:', userRoles);

  // 通配符检查 - 如果用户有 '*' 角色，则可以访问所有路径
  if (userRoles.includes('*')) return true;

  // 检查路径是否需要特定角色
  for (const [pathPattern, requiredRoles] of Object.entries(PATH_ROLE_MAP)) {
    if (path.startsWith(pathPattern)) {
      const hasRequiredRole = requiredRoles.some(role => userRoles.includes(role));
      console.log('RBAC - 路径匹配结果:', { pathPattern, requiredRoles, hasRequiredRole });
      return hasRequiredRole;
    }
  }

  // 默认允许访问
  return true;
};

/**
 * 获取用户无权访问路径时应该跳转的路径
 * @param {Object} userData - 用户数据
 * @returns {string} - 跳转路径
 */
const getFallbackPath = (userData) => {
  return getDashboardPath(userData);
};

const rbacService = {
  getUserRoles,
  hasRole,
  getDashboardPath,
  canAccessPath,
  getFallbackPath,
};

export default rbacService;
