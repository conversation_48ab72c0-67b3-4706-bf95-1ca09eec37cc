/* 增强版闯关游戏样式 */
.enhanced-level-game {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', <PERSON><PERSON>, 'Helvetica Neue', <PERSON><PERSON>, sans-serif;
}

/* 主题样式 */
.theme-classic {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.theme-adventure {
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
}

.theme-dark {
  background: linear-gradient(135deg, #434343 0%, #000000 100%);
  color: #fff;
}

.theme-galaxy {
  background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%);
  color: #fff;
}

/* 游戏头部 */
.game-header {
  margin-bottom: 24px;
}

.header-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.theme-dark .header-card,
.theme-galaxy .header-card {
  background: rgba(0, 0, 0, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: #fff;
}

.theme-adventure .header-card {
  background: rgba(255, 215, 0, 0.2);
  border: 1px solid rgba(255, 215, 0, 0.3);
}

/* 统计卡片 */
.stats-section {
  margin-bottom: 24px;
}

.stat-card {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  border: none;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.theme-dark .stat-card,
.theme-galaxy .stat-card {
  background: rgba(0, 0, 0, 0.6);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.theme-adventure .stat-card {
  background: rgba(255, 215, 0, 0.3);
  border: 1px solid rgba(255, 215, 0, 0.4);
}

/* 关卡地图 */
.level-map-section {
  margin-bottom: 24px;
}

.map-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  border: none;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.theme-dark .map-card,
.theme-galaxy .map-card {
  background: rgba(0, 0, 0, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: #fff;
}

.level-map {
  padding: 16px;
}

.level-card {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  border: 2px solid transparent;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.level-card:hover {
  transform: scale(1.02);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.level-card.completed {
  border-color: #52c41a;
  background: linear-gradient(135deg, rgba(82, 196, 26, 0.1), rgba(82, 196, 26, 0.05));
}

.level-card.locked {
  background: rgba(0, 0, 0, 0.05);
  cursor: not-allowed;
}

.level-card.completed::before {
  content: '✓';
  position: absolute;
  top: 8px;
  right: 8px;
  color: #52c41a;
  font-size: 18px;
  font-weight: bold;
}

.level-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.level-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

/* 游戏卡片 */
.ready-card,
.game-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  border: none;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  position: relative;
}

.theme-dark .ready-card,
.theme-dark .game-card,
.theme-galaxy .ready-card,
.theme-galaxy .game-card {
  background: rgba(0, 0, 0, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.ready-content {
  text-align: center;
  padding: 40px 20px;
}

/* 游戏状态栏 */
.game-status-bar {
  background: rgba(0, 0, 0, 0.05);
  padding: 16px 20px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.theme-dark .game-status-bar,
.theme-galaxy .game-status-bar {
  background: rgba(255, 255, 255, 0.05);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* 挑战区域 */
.challenge-area {
  padding: 40px 20px;
  min-height: 400px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.paused-overlay {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.1);
  border-radius: 16px;
  padding: 40px;
  text-align: center;
}

/* 挑战样式 */
.question-challenge,
.puzzle-challenge,
.memory-challenge,
.speed-challenge,
.luck-challenge {
  width: 100%;
  max-width: 600px;
  text-align: center;
}

.pattern-display {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 12px;
  padding: 20px;
  margin: 20px 0;
  font-family: monospace;
}

.calculation-display {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 12px;
  padding: 40px;
  margin: 20px 0;
}

.sequence-display {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 12px;
  padding: 20px;
  margin: 20px 0;
}

/* 挑战按钮 */
.question-challenge .ant-btn,
.puzzle-challenge .ant-btn,
.luck-challenge .ant-btn {
  transition: all 0.3s ease;
  border-radius: 8px;
  font-weight: 500;
}

.question-challenge .ant-btn:hover,
.puzzle-challenge .ant-btn:hover,
.luck-challenge .ant-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* 设置面板 */
.settings-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  border: none;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.theme-dark .settings-card,
.theme-galaxy .settings-card {
  background: rgba(0, 0, 0, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
  color: #fff;
}

.theme-adventure .settings-card {
  background: rgba(255, 215, 0, 0.2);
  border: 1px solid rgba(255, 215, 0, 0.3);
}

/* 结果模态框 */
.result-modal .ant-modal-content {
  border-radius: 20px;
  overflow: hidden;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
}

.result-content {
  text-align: center;
  padding: 20px;
}

.win-result .celebration-icon,
.success-result .success-icon {
  animation: celebration-bounce 1s ease-in-out infinite alternate;
}

@keyframes celebration-bounce {
  0% {
    transform: scale(1);
  }
  100% {
    transform: scale(1.1);
  }
}

.prize-info-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 20px;
  margin: 16px 0;
  color: #333;
}

.result-actions {
  margin-top: 24px;
}

/* 难度标签 */
.ant-tag {
  border-radius: 20px;
  font-weight: 500;
  padding: 4px 12px;
}

/* 进度条 */
.ant-progress-circle {
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

/* 动画效果 */
.level-unlock {
  animation: level-unlock 0.8s ease-out;
}

@keyframes level-unlock {
  0% {
    opacity: 0;
    transform: scale(0.8) rotateY(90deg);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1) rotateY(45deg);
  }
  100% {
    opacity: 1;
    transform: scale(1) rotateY(0deg);
  }
}

.challenge-appear {
  animation: challenge-appear 0.5s ease-out;
}

@keyframes challenge-appear {
  0% {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.correct-answer {
  animation: correct-flash 0.6s ease-in-out;
}

@keyframes correct-flash {
  0%, 100% {
    background-color: transparent;
  }
  50% {
    background-color: rgba(82, 196, 26, 0.3);
  }
}

.wrong-answer {
  animation: wrong-shake 0.6s ease-in-out;
}

@keyframes wrong-shake {
  0%, 20%, 40%, 60%, 80%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-10px);
  }
}

/* 加载动画 */
.level-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20px;
}

/* 特殊效果 */
.star-field {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  opacity: 0.1;
}

.star {
  position: absolute;
  width: 2px;
  height: 2px;
  background: #fff;
  border-radius: 50%;
  animation: twinkle 2s infinite;
}

@keyframes twinkle {
  0%, 100% {
    opacity: 0.3;
  }
  50% {
    opacity: 1;
  }
}

.theme-galaxy .star-field {
  opacity: 0.3;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .enhanced-level-game {
    padding: 12px;
  }
  
  .challenge-area {
    padding: 20px 12px;
    min-height: 300px;
  }
  
  .question-challenge .ant-col,
  .puzzle-challenge .ant-col,
  .luck-challenge .ant-col {
    margin-bottom: 8px;
  }
  
  .question-challenge .ant-btn,
  .puzzle-challenge .ant-btn,
  .luck-challenge .ant-btn {
    min-height: 44px;
    font-size: 14px;
  }
  
  .calculation-display,
  .pattern-display,
  .sequence-display {
    padding: 16px;
    margin: 16px 0;
  }
  
  .level-map .ant-col {
    margin-bottom: 12px;
  }
  
  .game-status-bar {
    padding: 12px;
  }
  
  .game-status-bar .ant-row {
    flex-direction: column;
    gap: 12px;
  }
}

@media (max-width: 480px) {
  .level-map .ant-col {
    span: 24 !important;
  }
  
  .question-challenge .ant-col,
  .puzzle-challenge .ant-col {
    span: 24 !important;
  }
  
  .luck-challenge .ant-col {
    span: 12 !important;
  }
  
  .stats-section .ant-col {
    span: 12 !important;
    margin-bottom: 12px;
  }
  
  .challenge-area {
    padding: 16px 8px;
  }
  
  .pattern-display,
  .calculation-display {
    font-size: 18px;
  }
}

/* 主题特定样式增强 */
.theme-adventure .level-card.completed {
  border-color: #faad14;
  background: linear-gradient(135deg, rgba(250, 173, 20, 0.1), rgba(250, 173, 20, 0.05));
}

.theme-dark .level-card.completed,
.theme-galaxy .level-card.completed {
  border-color: #722ed1;
  background: linear-gradient(135deg, rgba(114, 46, 209, 0.2), rgba(114, 46, 209, 0.1));
}

.theme-adventure .level-card.completed::before {
  color: #faad14;
}

.theme-dark .level-card.completed::before,
.theme-galaxy .level-card.completed::before {
  color: #722ed1;
} 