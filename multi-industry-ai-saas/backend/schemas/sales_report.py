#!/usr/bin/env python
# -*- coding: utf-8 -*-

from typing import List, Optional, Dict, Any
from datetime import datetime, date
from uuid import UUID
from pydantic import BaseModel, Field
from decimal import Decimal

from .store import Store
from .user import User

# 销售上报项基础模型
class SalesReportItemBase(BaseModel):
    product_id: Optional[UUID] = None
    product_name: str
    product_code: Optional[str] = None
    product_category: Optional[str] = None
    product_unit: Optional[str] = None
    quantity: float
    unit_price: float
    discount_amount: float = 0.0
    notes: Optional[str] = None

# 创建销售上报项请求模型
class SalesReportItemCreate(SalesReportItemBase):
    pass

# 更新销售上报项请求模型
class SalesReportItemUpdate(BaseModel):
    product_id: Optional[UUID] = None
    product_name: Optional[str] = None
    product_code: Optional[str] = None
    product_category: Optional[str] = None
    product_unit: Optional[str] = None
    quantity: Optional[float] = None
    unit_price: Optional[float] = None
    discount_amount: Optional[float] = None
    notes: Optional[str] = None

# 销售上报项响应模型
class SalesReportItemResponse(SalesReportItemBase):
    id: UUID
    sales_report_id: UUID
    total_amount: float
    created_at: datetime
    updated_at: datetime

    model_config = {"from_attributes": True}

# 支付方式基础模型
class PaymentMethodBase(BaseModel):
    method: str
    amount: float
    hourly_sales: Optional[Dict[str, float]] = None
    channel_sales: Optional[Dict[str, float]] = None
    notes: Optional[str] = None

# 充值/售卡基础模型
class RechargeSaleBase(BaseModel):
    type: str  # "recharge" 或 "card"
    amount: float
    count: Optional[int] = 0
    description: Optional[str] = None
    payment_methods: Optional[List[Dict[str, Any]]] = None

# 充值/售卡响应模型
class RechargeSaleResponse(RechargeSaleBase):
    pass

# 支付方式响应模型
class PaymentMethodResponse(BaseModel):
    method_id: str
    method_name: str
    sales_amount: float = 0.0
    recharge_amount: float = 0.0
    total_amount: float = 0.0

# 销售上报基础模型
class ChannelSaleBase(BaseModel):
    channel_id: UUID
    amount: float
    payment_methods: Optional[List[Dict[str, Any]]] = None

class SalesReportBase(BaseModel):
    store_id: UUID
    report_date: datetime = Field(default_factory=datetime.utcnow)
    report_type: str = "daily"
    total_sales: float = 0.0
    total_orders: int = 0
    total_customers: int = 0
    online_sales: float = 0.0
    offline_sales: float = 0.0
    payment_methods: Any = None
    product_categories: Optional[Dict[str, float]] = None
    hourly_sales: Optional[Dict[str, float]] = None
    channel_sales: Optional[Dict[str, float]] = None
    notes: Optional[str] = None

# ===================================================================
# 新的创建/更新/响应模型 - 2024-07-26 重构
# ===================================================================

class PaymentMethodDetailCreate(BaseModel):
    """支付方式明细 - 用于创建/更新"""
    payment_method_id: UUID
    amount: float = 0.0
    orders: int = 0

class ChannelDetailCreate(BaseModel):
    """渠道详情 - 用于创建/更新"""
    sales_channel_id: UUID
    payment_methods_details: List[PaymentMethodDetailCreate] = []

class RechargeSaleCreate(BaseModel):
    """充值/售卡项 - 用于创建/更新"""
    payment_method_id: UUID
    amount: float = 0.0
    orders: int = 0

class SalesReportCreate(BaseModel):
    """新的销售上报创建模型"""
    store_id: UUID
    report_date: date
    report_type: str = "daily"
    notes: Optional[str] = None
    channel_details: List[ChannelDetailCreate] = []
    recharge_sales_data: List[RechargeSaleCreate] = []

    model_config = {"from_attributes": True}

class SalesReportUpdate(BaseModel):
    """新的销售上报更新模型"""
    report_date: Optional[date] = None
    report_type: Optional[str] = None
    notes: Optional[str] = None
    channel_details: Optional[List[ChannelDetailCreate]] = None
    recharge_sales_data: Optional[List[RechargeSaleCreate]] = None

class PaymentMethodDetailResponse(BaseModel):
    """支付方式明细 - 用于响应"""
    payment_method_id: UUID
    amount: float
    orders: int
    payment_method: Optional[Any] # 在 service 层填充

    model_config = {"from_attributes": True}

class ChannelDetailResponse(BaseModel):
    """渠道详情 - 用于响应"""
    sales_channel_id: UUID
    total_sales: float
    total_orders: int
    payment_methods_details: List[PaymentMethodDetailResponse]
    sales_channel: Optional[Any] # 在 service 层填充

    model_config = {"from_attributes": True}

class RechargeSaleResponse(BaseModel):
    """充值/售卡项 - 用于响应"""
    payment_method_id: UUID
    amount: float
    orders: int
    payment_method: Optional[Any] # 在 service 层填充

    model_config = {"from_attributes": True}

class SalesReportResponse(BaseModel):
    """新的销售上报响应模型"""
    id: UUID
    project_id: UUID
    store_id: UUID
    reporter_id: Optional[UUID] = None
    approved_by: Optional[UUID] = None
    report_date: datetime
    report_type: str
    status: str
    notes: Optional[str] = None
    reject_reason: Optional[str] = None
    
    total_sales: float
    total_orders: int
    total_customers: int
    online_sales: float
    offline_sales: float
    
    recharge_amount: float
    card_sales_amount: float
    recharge_count: int
    card_sales_count: int
    
    payment_methods_data: Optional[Any] = None
    recharge_sales_data: Optional[Any] = None
    
    items: List[SalesReportItemResponse] = []
    channel_details: List[ChannelDetailResponse] = []
    
    created_at: datetime
    updated_at: datetime
    approved_at: Optional[datetime] = None

    # 关联对象
    store: Optional[Store] = None
    reporter: Optional[User] = None
    approver: Optional[User] = None

    # 从hybrid_property获取
    store_name: Optional[str] = None
    creator_name: Optional[str] = None
    updater_name: Optional[str] = None
    approver_name: Optional[str] = None

    model_config = {"from_attributes": True}

class SalesReportListResponse(BaseModel):
    items: List[SalesReportResponse]
    total: int
    page: int
    size: int

class ChannelSummaryItem(BaseModel):
    channel_name: str
    total_sales: float

class PaymentMethodSummaryItem(BaseModel):
    payment_method_name: str
    total_amount: float

class SalesReportStatisticsResponse(BaseModel):
    total_sales: float
    total_orders: int
    total_recharge_amount: float
    total_recharge_orders: int
    grand_total_amount: float
    grand_total_orders: int
    channel_summary: List[ChannelSummaryItem]
    payment_method_summary: List[PaymentMethodSummaryItem]

# ===================================================================
# 通用
# ===================================================================

class SalesReportStatusUpdate(BaseModel):
    status: str
    notes: Optional[str] = None
