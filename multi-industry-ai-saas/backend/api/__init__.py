#!/usr/bin/env python
# -*- coding: utf-8 -*-

from fastapi import APIRouter
from api.v1 import api_router as api_v1_router      # 业务层
from api.project import router as api_inner_router  # 设置层/项目层
from api.v1.ws import router as ws_router          # WebSocket路由
from .public import router as public_router

api_router = APIRouter()

# 注册业务层路由 - 不添加标签，避免所有子路由都继承业务层标签
api_router.include_router(api_v1_router, prefix="/v1")

# 注册WebSocket路由 - 注意：WebSocket路由需要特殊处理
api_router.include_router(ws_router, prefix="/v1/ws")

# 打印WebSocket路由信息，便于调试
from fastapi.routing import APIRoute
import logging
logger = logging.getLogger(__name__)
logger.info("==== WebSocket Routes ====")
for route in ws_router.routes:
    # 检查是否是WebSocket路由
    if hasattr(route, 'path') and 'websocket' in str(route.__class__).lower():
        logger.info(f"WebSocket: {route.path}")
    # 检查是否是API路由
    elif isinstance(route, APIRoute):
        logger.info(f"API: {route.path} [{','.join(route.methods)}] -> {route.name}")
logger.info("==========================")

# 注册设置层/项目层路由 - 不添加标签，避免所有子路由都继承项目标签
api_router.include_router(api_inner_router, prefix="/project")

# 包含公开API
api_router.include_router(public_router, prefix="/public")