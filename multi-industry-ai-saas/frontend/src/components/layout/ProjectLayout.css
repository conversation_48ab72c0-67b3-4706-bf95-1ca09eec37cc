.project-layout {
  min-height: 100vh;
}

.project-sider {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
  z-index: 10;
}

.logo {
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-bottom: 1px solid #f0f0f0;
}

.logo-text {
  font-size: 18px;
  font-weight: 600;
  color: #1890ff;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 180px;
}

.project-header {
  padding: 0 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  z-index: 9;
}

.header-left {
  display: flex;
  align-items: center;
}

.header-right {
  display: flex;
  align-items: center;
}

.trigger-button {
  margin-right: 16px;
  font-size: 18px;
}

.project-selector {
  cursor: pointer;
  padding: 0 12px;
  height: 40px;
  display: flex;
  align-items: center;
  border-radius: 4px;
  transition: all 0.3s;
  margin-right: 16px;
}

.project-selector:hover {
  background-color: #f5f5f5;
}

.project-name {
  font-weight: 500;
  max-width: 120px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.header-button {
  margin-right: 8px;
  font-size: 16px;
}

.notification-badge {
  margin-right: 16px;
}

.user-info {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 0 8px;
  height: 40px;
  border-radius: 4px;
  transition: all 0.3s;
}

.user-info:hover {
  background-color: #f5f5f5;
}

.user-avatar {
  margin-right: 8px;
}

.user-name {
  max-width: 100px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.project-content {
  padding: 24px;
  background-color: #f0f2f5;
  overflow: auto;
}

/* 系统设置页面样式 */
.system-settings {
  background-color: #fff;
  padding: 24px;
  border-radius: 4px;
}

.settings-card {
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
}

/* 角色管理页面样式 */
.role-management {
  background-color: #fff;
  padding: 24px;
  border-radius: 4px;
}

/* 用户管理页面样式 */
.user-management {
  background-color: #fff;
  padding: 24px;
  border-radius: 4px;
}

.project-menu {
  border-right: 0;
}

@media (max-width: 768px) {
  .project-header {
    padding: 0 12px;
  }

  .user-name {
    display: none;
  }

  .project-name {
    display: none;
  }

  .project-content {
    padding: 16px;
  }
}
