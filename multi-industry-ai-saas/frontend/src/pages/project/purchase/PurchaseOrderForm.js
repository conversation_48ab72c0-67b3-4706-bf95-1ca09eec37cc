import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Form, 
  Input, 
  Button, 
  Select, 
  DatePicker, 
  Space, 
  Table, 
  InputNumber, 
  Typography, 
  Divider, 
  message, 
  Popconfirm,
  Row,
  Col
} from 'antd';
import { 
  PlusOutlined, 
  DeleteOutlined, 
  SaveOutlined, 
  ArrowLeftOutlined, 
  MinusCircleOutlined
} from '@ant-design/icons';
import { useParams, useNavigate } from 'react-router-dom';
import dayjs from "dayjs";
import apiService from '../../../services/api';

const { Title, Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;

const PurchaseOrderForm = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const isEditing = !!id;
  
  // 状态管理
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [initialLoading, setInitialLoading] = useState(isEditing);
  const [suppliers, setSuppliers] = useState([]);
  const [warehouses, setWarehouses] = useState([]);
  const [products, setProducts] = useState([]);
  const [items, setItems] = useState([]);
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [orderTotals, setOrderTotals] = useState({
    totalAmount: 0,
    discountAmount: 0,
    taxAmount: 0,
    shippingFee: 0,
    finalAmount: 0
  });

  // 获取供应商列表
  const fetchSuppliers = async () => {
    try {
      const response = await apiService.project.supplier.getList();
      if (response && response.data) {
        setSuppliers(response.data);
      }
    } catch (error) {
      console.error('获取供应商列表失败:', error);
      message.error('获取供应商列表失败');
    }
  };

  // 获取仓库列表
  const fetchWarehouses = async () => {
    try {
      const response = await apiService.project.warehouse.getList();
      if (response && response.items) {
        setWarehouses(response.items);
      }
    } catch (error) {
      console.error('获取仓库列表失败:', error);
      message.error('获取仓库列表失败');
    }
  };

  // 获取产品列表
  const fetchProducts = async () => {
    try {
      const response = await apiService.project.product.getList();
      if (response && response.data) {
        setProducts(response.data);
        console.log('products:', response.data);
      }
    } catch (error) {
      console.error('获取产品列表失败:', error);
      message.error('获取产品列表失败');
    }
  };

  // 获取采购订单详情
  const fetchOrderDetail = async () => {
    if (!isEditing) return;
    
    setInitialLoading(true);
    try {
      const response = await apiService.project.purchaseOrder.getDetail(id);
      if (response) {
        // 设置表单初始值（移除supplier_id）
        form.setFieldsValue({
          warehouse_id: response.warehouse_id,
          order_date: response.order_date ? dayjs(response.order_date) : dayjs(),
          expected_delivery_date: response.expected_delivery_date ? dayjs(response.expected_delivery_date) : null,
          status: response.status,
          payment_method: response.payment_method,
          notes: response.notes,
          shipping_fee: response.shipping_fee
        });
        
        // 设置订单项
        setItems(response.items || []);
        
        // 计算订单金额
        calculateOrderTotals(response.items || [], response.shipping_fee || 0);
      } else {
        message.error('获取采购订单详情失败');
        navigate('/project/purchase/orders');
      }
    } catch (error) {
      console.error('获取采购订单详情失败:', error);
      message.error('获取采购订单详情失败: ' + (error.response?.data?.detail || error.message));
      navigate('/project/purchase/orders');
    } finally {
      setInitialLoading(false);
    }
  };

  // 计算订单金额
  const calculateOrderTotals = (orderItems, shippingFee = 0) => {
    let totalAmount = 0;
    let discountAmount = 0;
    let taxAmount = 0;
    
    orderItems.forEach(item => {
      const itemTotal = item.quantity * item.unit_price;
      const itemDiscount = itemTotal * (item.discount_rate / 100);
      const itemTax = (itemTotal - itemDiscount) * (item.tax_rate / 100);
      
      totalAmount += itemTotal;
      discountAmount += itemDiscount;
      taxAmount += itemTax;
    });
    
    const finalAmount = totalAmount - discountAmount + taxAmount + parseFloat(shippingFee || 0);
    
    setOrderTotals({
      totalAmount,
      discountAmount,
      taxAmount,
      shippingFee: parseFloat(shippingFee || 0),
      finalAmount
    });
  };

  // 处理产品选择
  const handleProductSelect = (productId) => {
    const product = products.find(p => p.id === productId);
    setSelectedProduct(product);
  };

  // 添加订单项
  const handleAddItem = () => {
    if (!selectedProduct) {
      message.warning('请先选择产品');
      return;
    }
    
    const newItem = {
      id: `temp_${Date.now()}`,
      product_id: selectedProduct.id,
      supplier_id: null,
      product_name: selectedProduct.name,
      product_code: selectedProduct.code,
      product_unit: selectedProduct.unit,
      product_specification: selectedProduct.specification,
      quantity: 1,
      unit_price: selectedProduct.price || 0,
      discount_rate: 0,
      tax_rate: 0,
      total_amount: selectedProduct.price || 0,
      notes: ''
    };
    
    const newItems = [...items, newItem];
    setItems(newItems);
    calculateOrderTotals(newItems, orderTotals.shippingFee);
    setSelectedProduct(null);
  };

  // 更新订单项
  const handleUpdateItem = (index, field, value) => {
    const newItems = [...items];
    newItems[index][field] = value;
    
    // 重新计算项目金额
    const item = newItems[index];
    const itemTotal = item.quantity * item.unit_price;
    const itemDiscount = itemTotal * (item.discount_rate / 100);
    const itemTax = (itemTotal - itemDiscount) * (item.tax_rate / 100);
    const itemFinal = itemTotal - itemDiscount + itemTax;
    
    newItems[index].total_amount = itemFinal;
    
    setItems(newItems);
    calculateOrderTotals(newItems, orderTotals.shippingFee);
  };

  // 删除订单项
  const handleDeleteItem = (index) => {
    const newItems = [...items];
    newItems.splice(index, 1);
    setItems(newItems);
    calculateOrderTotals(newItems, orderTotals.shippingFee);
  };

  // 更新运费
  const handleShippingFeeChange = (value) => {
    form.setFieldsValue({ shipping_fee: value });
    calculateOrderTotals(items, value);
  };

  // 提交表单
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      
      if (items.length === 0) {
        message.warning('请至少添加一个订单项');
        return;
      }
      
      setLoading(true);
      
      // 准备提交数据
      const orderData = {
        ...values,
        order_date: values.order_date ? values.order_date.format('YYYY-MM-DD') : dayjs().format('YYYY-MM-DD'),
        expected_delivery_date: values.expected_delivery_date ? values.expected_delivery_date.format('YYYY-MM-DD') : null,
        items: items.map(item => ({
          product_id: item.product_id,
          supplier_id: item.supplier_id,
          product_name: item.product_name,
          product_code: item.product_code,
          product_unit: item.product_unit,
          product_specification: item.product_specification,
          quantity: item.quantity,
          unit_price: item.unit_price,
          discount_rate: item.discount_rate,
          tax_rate: item.tax_rate,
          notes: item.notes
        }))
      };
      
      if (isEditing) {
        // 更新采购订单
        await apiService.project.purchaseOrder.update(id, orderData);
        message.success('采购订单更新成功');
      } else {
        // 创建采购订单
        await apiService.project.purchaseOrder.create(orderData);
        message.success('采购订单创建成功');
      }
      
      navigate('/project/purchase/orders');
    } catch (error) {
      console.error('提交采购订单失败:', error);
      if (error.errorFields) {
        // 表单验证错误
        return;
      }
      message.error('提交采购订单失败: ' + (error.response?.data?.detail || error.message));
    } finally {
      setLoading(false);
    }
  };

  // 返回列表
  const handleBack = () => {
    navigate('/project/purchase/orders');
  };

  // 初始化
  useEffect(() => {
    fetchSuppliers();
    fetchWarehouses();
    fetchProducts();
    
    if (isEditing) {
      fetchOrderDetail();
    } else {
      // 设置默认值
      form.setFieldsValue({
        order_date: dayjs(),
        status: 'draft',
        shipping_fee: 0
      });
    }
  }, []);

  // 订单项表格列定义
  const itemColumns = [
    {
      title: '产品名称',
      dataIndex: 'product_name',
      key: 'product_name',
      width: 200
    },
    {
      title: '产品编码',
      dataIndex: 'product_code',
      key: 'product_code',
      width: 120
    },
    {
      title: '供应商',
      dataIndex: 'supplier_id',
      key: 'supplier_id',
      width: 150,
      render: (text, record, index) => (
        <Select
          value={text}
          onChange={(value) => handleUpdateItem(index, 'supplier_id', value)}
          placeholder="选择供应商"
          style={{ width: '100%' }}
          allowClear
        >
          {suppliers.map(supplier => (
            <Option key={supplier.id} value={supplier.id}>{supplier.name}</Option>
          ))}
        </Select>
      )
    },
    {
      title: '规格',
      dataIndex: 'product_specification',
      key: 'product_specification',
      width: 120
    },
    {
      title: '单位',
      dataIndex: 'product_unit',
      key: 'product_unit',
      width: 80
    },
    {
      title: '数量',
      dataIndex: 'quantity',
      key: 'quantity',
      width: 100,
      render: (text, record, index) => (
        <InputNumber
          min={0.01}
          step={0.01}
          value={text}
          onChange={(value) => handleUpdateItem(index, 'quantity', value)}
          style={{ width: '100%' }}
        />
      )
    },
    {
      title: '单价',
      dataIndex: 'unit_price',
      key: 'unit_price',
      width: 120,
      render: (text, record, index) => (
        <InputNumber
          min={0}
          step={0.01}
          value={text}
          onChange={(value) => handleUpdateItem(index, 'unit_price', value)}
          prefix="¥"
          style={{ width: '100%' }}
        />
      )
    },
    {
      title: '折扣率(%)',
      dataIndex: 'discount_rate',
      key: 'discount_rate',
      width: 120,
      render: (text, record, index) => (
        <InputNumber
          min={0}
          max={100}
          value={text}
          onChange={(value) => handleUpdateItem(index, 'discount_rate', value)}
          style={{ width: '100%' }}
        />
      )
    },
    {
      title: '税率(%)',
      dataIndex: 'tax_rate',
      key: 'tax_rate',
      width: 100,
      render: (text, record, index) => (
        <InputNumber
          min={0}
          max={100}
          value={text}
          onChange={(value) => handleUpdateItem(index, 'tax_rate', value)}
          style={{ width: '100%' }}
        />
      )
    },
    {
      title: '总金额',
      dataIndex: 'total_amount',
      key: 'total_amount',
      width: 120,
      render: (text) => `¥${parseFloat(text || 0).toFixed(2)}`
    },
    {
      title: '操作',
      key: 'action',
      width: 80,
      render: (_, record, index) => (
        <Button
          type="link"
          danger
          icon={<DeleteOutlined />}
          onClick={() => handleDeleteItem(index)}
        />
      )
    }
  ];

  if (initialLoading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Title level={4}>{isEditing ? '加载采购订单...' : '创建采购订单'}</Title>
      </div>
    );
  }

  return (
    <div className="purchase-order-form-page">
      <Card>
        <div style={{ marginBottom: 16 }}>
          <Space>
            <Button icon={<ArrowLeftOutlined />} onClick={handleBack}>返回</Button>
            <Button 
              type="primary" 
              icon={<SaveOutlined />} 
              onClick={handleSubmit}
              loading={loading}
            >
              保存
            </Button>
          </Space>
        </div>

        <Title level={4}>{isEditing ? '编辑采购订单' : '创建采购订单'}</Title>

        <Form
          form={form}
          layout="vertical"
        >
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="warehouse_id"
                label="仓库"
              >
                <Select
                  placeholder="请选择仓库"
                  allowClear
                  showSearch
                  optionFilterProp="children"
                  filterOption={(input, option) =>
                    option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                  }
                >
                  {warehouses.map(warehouse => (
                    <Option key={warehouse.id} value={warehouse.id}>{warehouse.name}</Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            
            <Col span={8}>
              <Form.Item
                name="order_date"
                label="订单日期"
                rules={[{ required: true, message: '请选择订单日期' }]}
              >
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            
            <Col span={8}>
              <Form.Item
                name="expected_delivery_date"
                label="预计交货日期"
              >
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            
            <Col span={8}>
              <Form.Item
                name="status"
                label="订单状态"
                rules={[{ required: true, message: '请选择订单状态' }]}
              >
                <Select placeholder="请选择订单状态">
                  <Option value="draft">草稿</Option>
                  <Option value="confirmed">已确认</Option>
                  <Option value="received">已收货</Option>
                  <Option value="cancelled">已取消</Option>
                </Select>
              </Form.Item>
            </Col>
            
            <Col span={8}>
              <Form.Item
                name="payment_method"
                label="支付方式"
              >
                <Select placeholder="请选择支付方式" allowClear>
                  <Option value="cash">现金</Option>
                  <Option value="bank_transfer">银行转账</Option>
                  <Option value="credit_card">信用卡</Option>
                  <Option value="alipay">支付宝</Option>
                  <Option value="wechat">微信支付</Option>
                  <Option value="other">其他</Option>
                </Select>
              </Form.Item>
            </Col>
            
            <Col span={24}>
              <Form.Item
                name="notes"
                label="备注"
              >
                <TextArea rows={4} placeholder="请输入备注信息" />
              </Form.Item>
            </Col>
          </Row>
        </Form>

        <Divider orientation="left">订单项</Divider>
        
        <div style={{ marginBottom: 16 }}>
          <Space>
            <Select
              placeholder="选择产品"
              style={{ width: 300 }}
              showSearch
              optionFilterProp="children"
              filterOption={(input, option) =>
                option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
              }
              onChange={handleProductSelect}
            >
              {products.map(product => (
                <Option key={product.id} value={product.id}>{product.name} ({product.sku})</Option>
              ))}
            </Select>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAddItem}
              disabled={!selectedProduct}
            >
              添加产品
            </Button>
          </Space>
        </div>
        
        <Table
          columns={itemColumns}
          dataSource={items}
          rowKey="id"
          pagination={false}
          scroll={{ x: 1200 }}
        />
        
        <div style={{ marginTop: 16, textAlign: 'right' }}>
          <Row gutter={16}>
            <Col span={8} offset={16}>
              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 8 }}>
                <Text>商品总金额:</Text>
                <Text>¥{orderTotals.totalAmount.toFixed(2)}</Text>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 8 }}>
                <Text>折扣金额:</Text>
                <Text>¥{orderTotals.discountAmount.toFixed(2)}</Text>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 8 }}>
                <Text>税额:</Text>
                <Text>¥{orderTotals.taxAmount.toFixed(2)}</Text>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 8 }}>
                <Text>运费:</Text>
                <InputNumber
                  min={0}
                  step={0.01}
                  style={{ width: 120 }}
                  value={orderTotals.shippingFee}
                  onChange={handleShippingFeeChange}
                  prefix="¥"
                />
                <Form.Item name="shipping_fee" hidden>
                  <Input />
                </Form.Item>
              </div>
              <Divider style={{ margin: '8px 0' }} />
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <Text strong>最终金额:</Text>
                <Text strong style={{ fontSize: '18px', color: '#f50' }}>
                  ¥{orderTotals.finalAmount.toFixed(2)}
                </Text>
              </div>
            </Col>
          </Row>
        </div>
      </Card>
    </div>
  );
};

export default PurchaseOrderForm;
