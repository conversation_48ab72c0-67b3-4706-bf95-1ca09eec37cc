#!/usr/bin/env python
# -*- coding: utf-8 -*-

import logging
import asyncio
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import and_
import uuid
from datetime import datetime

from db.database import get_db
from models.plugin import Plugin, PluginVersion

# 初始化日志
logger = logging.getLogger(__name__)

async def initialize_plugins():
    """初始化插件数据"""
    logger.info("初始化插件数据")

    async for db in get_db():
        try:
            # 初始化钉钉机器人插件
            await initialize_dingtalk_robot_plugin(db)

            # 初始化营销游戏插件
            await initialize_marketing_game_plugin(db)
            
            # 初始化全渠道运营助手插件
            try:
                from plugins.multi_channel_operations.db_init import init_plugin as initialize_multi_channel_operations_plugin
                await initialize_multi_channel_operations_plugin()
                logger.info("全渠道运营助手插件初始化成功")
            except ImportError:
                logger.warning("未找到全渠道运营助手插件，跳过初始化。")
            except Exception as e:
                logger.error(f"初始化全渠道运营助手插件失败: {str(e)}")

            # 提交事务
            await db.commit()

            logger.info("插件数据初始化完成")

            # 初始化 SAPI 插件
            try:
                from plugins import initialize_plugins
                await initialize_plugins()
                logger.info("插件系统初始化成功")
            except Exception as e:
                logger.error(f"插件系统初始化失败: {str(e)}")
                import traceback
                traceback.print_exc()
        except Exception as e:
            logger.error(f"初始化插件数据失败: {str(e)}")
            await db.rollback()
            raise
        finally:
            await db.close()

async def initialize_dingtalk_robot_plugin(db: AsyncSession):
    """初始化钉钉机器人插件"""
    try:
        # 检查插件是否已存在
        plugin_query = select(Plugin).where(Plugin.code == "dingtalk_robot")
        result = await db.execute(plugin_query)
        plugin = result.scalar_one_or_none()

        if not plugin:
            # 创建插件
            plugin = Plugin(
                id=uuid.uuid4(),
                code="dingtalk_robot",
                name="钉钉智能机器人",
                description="AI增强的钉钉智能工作助手，支持智能对话、消息推送、用户管理等功能。集成先进AI技术，提供自然语言交互、上下文记忆、工具调用等能力，让钉钉成为您的智能工作伙伴。",
                version="1.0.0",
                author="Retail AI SaaS",
                website="https://www.example.com/plugins/dingtalk",
                icon_url="/static/plugins/dingtalk/icon.png",
                category="communication",
                price=0,  # 免费插件
                billing_cycle="one_time",
                is_system=True,
                is_active=True,
                requires_subscription=False,
                installation_path="plugins.dingtalk",
                entry_point="initialize",
                settings_schema={
                    "properties": {
                        "enable_dingtalk": {
                            "type": "boolean",
                            "title": "启用钉钉通知",
                            "default": True
                        },
                        "notification_level": {
                            "type": "string",
                            "title": "通知级别",
                            "enum": ["all", "high_only", "custom"],
                            "enumNames": ["所有通知", "仅高优先级", "自定义"],
                            "default": "all"
                        },
                        "retry_count": {
                            "type": "integer",
                            "title": "重试次数",
                            "minimum": 0,
                            "maximum": 10,
                            "default": 3
                        },
                        "retry_interval": {
                            "type": "integer",
                            "title": "重试间隔(秒)",
                            "minimum": 10,
                            "maximum": 300,
                            "default": 60
                        },
                        "default_template": {
                            "type": "string",
                            "title": "默认消息模板",
                            "format": "textarea",
                            "default": "### {{title}}\n\n{{content}}\n\n> 来自系统通知 - {{time}}"
                        }
                    }
                },
                features=[
                    "🤖 AI智能对话 - 与AI助手进行自然语言交互",
                    "💬 多助手支持 - 可选择不同的AI助手",
                    "📝 对话线程管理 - 支持多个对话线程切换",
                    "🔄 上下文记忆 - 维护对话历史和上下文",
                    "📤 智能消息推送 - AI回复可自动发送到钉钉",
                    "👤 用户管理 - 钉钉账号绑定和信息同步",
                    "🛠️ AI工具调用 - AI可主动发送消息和查询信息",
                    "📊 多种消息类型 - 文本、卡片、工作通知等",
                    "⚙️ 灵活配置 - 支持发送目标和类型设置",
                    "📋 操作引导 - 智能提示和帮助说明"
                ]
            )
            db.add(plugin)
            await db.flush()

            # 创建插件版本
            version = PluginVersion(
                id=uuid.uuid4(),
                plugin_id=plugin.id,
                version="1.0.0",
                release_notes="初始版本",
                is_latest=True,
                released_at=datetime.now()
            )
            db.add(version)

            logger.info(f"钉钉机器人插件初始化成功: {plugin.id}")
        else:
            # 更新现有插件的信息
            plugin.name = "钉钉智能机器人"
            plugin.description = "AI增强的钉钉智能工作助手，支持智能对话、消息推送、用户管理等功能。集成先进AI技术，提供自然语言交互、上下文记忆、工具调用等能力，让钉钉成为您的智能工作伙伴。"
            plugin.category = "communication"
            plugin.features = [
                "🤖 AI智能对话 - 与AI助手进行自然语言交互",
                "💬 多助手支持 - 可选择不同的AI助手",
                "📝 对话线程管理 - 支持多个对话线程切换",
                "🔄 上下文记忆 - 维护对话历史和上下文",
                "📤 智能消息推送 - AI回复可自动发送到钉钉",
                "👤 用户管理 - 钉钉账号绑定和信息同步",
                "🛠️ AI工具调用 - AI可主动发送消息和查询信息",
                "📊 多种消息类型 - 文本、卡片、工作通知等",
                "⚙️ 灵活配置 - 支持发送目标和类型设置",
                "📋 操作引导 - 智能提示和帮助说明"
            ]

    except Exception as e:
        logger.error(f"初始化钉钉机器人插件失败: {str(e)}")
        raise

async def initialize_marketing_game_plugin(db: AsyncSession):
    """初始化营销游戏插件"""
    try:
        # 检查插件是否已存在
        plugin_query = select(Plugin).where(Plugin.code == "marketing_game")
        result = await db.execute(plugin_query)
        plugin = result.scalar_one_or_none()

        if not plugin:
            # 创建插件
            plugin = Plugin(
                id=uuid.uuid4(),
                code="marketing_game",
                name="营销游戏插件",
                description="提供抽奖、刮刮卡、幸运大转盘等营销游戏功能，支持小票验证和奖品管理",
                version="1.0.0",
                author="Retail AI SaaS",
                website="https://www.example.com/plugins/marketing-game",
                icon_url="/static/plugins/marketing-game/icon.png",
                category="marketing",
                price=0,  # 免费插件
                billing_cycle="one_time",
                is_system=True,
                is_active=True,
                requires_subscription=False,
                installation_path="plugins.marketing_game",
                entry_point="initialize",
                settings_schema={
                    "properties": {
                        "enable_receipt_verification": {
                            "type": "boolean",
                            "title": "启用小票验证",
                            "default": True
                        },
                        "min_consumption_amount": {
                            "type": "number",
                            "title": "最低消费金额",
                            "minimum": 0,
                            "default": 0
                        },
                        "max_draws_per_user": {
                            "type": "integer",
                            "title": "每用户最大抽奖次数",
                            "minimum": 1,
                            "maximum": 100,
                            "default": 3
                        },
                        "default_game_duration": {
                            "type": "integer",
                            "title": "默认游戏持续天数",
                            "minimum": 1,
                            "maximum": 90,
                            "default": 7
                        }
                    }
                },
                features=["lucky_draw", "scratch_card", "wheel_of_fortune", "receipt_verification"]
            )
            db.add(plugin)
            await db.flush()

            # 创建插件版本
            version = PluginVersion(
                id=uuid.uuid4(),
                plugin_id=plugin.id,
                version="1.0.0",
                release_notes="初始版本",
                is_latest=True,
                released_at=datetime.now()
            )
            db.add(version)

            logger.info(f"营销游戏插件初始化成功: {plugin.id}")
        else:
            logger.info(f"营销游戏插件已存在: {plugin.id}")

    except Exception as e:
        logger.error(f"初始化营销游戏插件失败: {str(e)}")
        raise

if __name__ == "__main__":
    asyncio.run(initialize_plugins())
