-- 添加阿里云百炼OCR模型
-- qwen-vl-ocr-latest 专业OCR模型

DO $$
DECLARE
    alibaba_provider_id UUID;
    model_exists INTEGER;
BEGIN
    -- 获取阿里巴巴百炼提供商ID
    SELECT id INTO alibaba_provider_id 
    FROM ai_providers 
    WHERE name = 'alibaba_bailian';

    -- 检查提供商是否存在
    IF alibaba_provider_id IS NULL THEN
        RAISE EXCEPTION '阿里巴巴百炼提供商不存在，请先初始化AI提供商数据';
    END IF;

    -- 检查并添加 qwen-vl-ocr-latest 模型（专业OCR视觉模型）
    SELECT COUNT(*) INTO model_exists 
    FROM ai_models 
    WHERE provider_id = alibaba_provider_id AND name = 'qwen-vl-ocr-latest';
    
    IF model_exists = 0 THEN
        INSERT INTO ai_models (
            id, provider_id, name, display_name, description, model_type,
            capabilities, context_window, token_limit,
            input_price_per_1k_tokens, output_price_per_1k_tokens,
            image_price_per_1k_tokens, average_response_time, rate_limit_per_minute,
            status, is_builtin, created_at, updated_at
        ) VALUES (
            gen_random_uuid(),
            alibaba_provider_id,
            'qwen-vl-ocr-latest',
            '通义千问VL OCR最新版',
            '阿里云百炼专业OCR视觉模型，专门优化用于文档和表格识别，支持高精度文字提取和表格数据解析',
            'vision',
            jsonb_build_object(
                'supports_function_calling', true,
                'supports_vision', true,
                'supports_audio_input', false,
                'supports_audio_output', false,
                'supports_speech_to_text', false,
                'supports_text_to_speech', false,
                'supports_system_message', true,
                'supports_streaming', true,
                'supports_reasoning', false,
                'supports_code_generation', false,
                'supports_multilingual', true,
                'supports_document_analysis', true,
                'supports_chart_analysis', true,
                'supports_ocr', true,
                'supports_image_understanding', true,
                'supports_table_extraction', true,
                'supports_form_recognition', true
            ),
            38192,
            30000,
            0.0008,
            0.002,
            0.0008,
            2.5,
            60,
            'active',
            true,
            NOW(),
            NOW()
        );
        RAISE NOTICE '添加OCR模型: qwen-vl-ocr-latest';
    ELSE
        RAISE NOTICE '模型 qwen-vl-ocr-latest 已存在，跳过';
    END IF;

    RAISE NOTICE '阿里云百炼OCR模型添加完成！最大输出tokens: 8192';
END $$; 