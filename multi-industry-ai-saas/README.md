# 多行业AI SaaS系统

## 项目概述

本项目旨在将现有的零售AI系统改造为支持多行业的SaaS（Software as a Service）模式，使系统能够支持多租户架构和服务商模式，实现一套代码服务多个行业、多个客户的目标。通过SaaS化改造，系统将能够更高效地进行部署、维护和升级，同时为客户提供更灵活的服务模式和计费方式。

## 系统架构

### 现有系统架构

现有零售AI系统采用单体部署模式，主要包含以下组件：

1. **前端**：React应用，提供用户界面
2. **后端**：Python FastAPI应用，提供API服务
3. **数据库**：PostgreSQL，存储业务数据
4. **插件系统**：支持功能扩展，如SAIP、钉钉集成等

### SaaS化架构设计

改造后的SaaS架构将采用服务商-租户-项目的多级模式，支持多行业场景，主要包含以下组件：

1. **SaaS管理平台**
   - 服务商管理
   - 租户管理
   - 项目管理
   - 计费与订阅管理
   - 系统监控与运维
   - 行业模型管理

2. **多租户应用层**
   - 前端多租户支持
   - 后端多租户支持
   - API网关与认证服务

3. **数据存储层**
   - 多租户数据隔离
   - 数据备份与恢复
   - 数据迁移服务

4. **插件市场**
   - 插件管理
   - 插件安装与配置
   - 插件权限控制

## 多级模型设计

### 服务商模型

```
服务商(ServiceProvider)
├── 服务商ID
├── 服务商名称
├── 公司名称
├── 联系人
├── 联系方式
├── 服务商状态
├── 分成比例
├── 支持行业
├── 品牌设置
└── 管理员账户
```

### 租户模型

```
租户(Tenant)
├── 租户ID
├── 服务商ID
├── 租户名称
├── 行业类型
├── 租户状态
├── 订阅计划
├── 创建时间
├── 到期时间
└── 管理员账户
```

### 项目模型

```
项目(Project)
├── 项目ID
├── 项目名称
├── 租户ID
├── 行业类型
├── 项目状态
├── 创建时间
├── 存储配额
├── 用户配额
└── 插件配置
```

### 用户与权限模型

```
用户(User)
├── 用户ID
├── 用户名
├── 邮箱
├── 密码
├── 租户ID
├── 项目ID列表
├── 角色
└── 状态

角色(Role)
├── 角色ID
├── 角色名称
├── 租户ID
├── 项目ID
└── 权限列表
```

## 数据隔离策略

本系统将采用以下数据隔离策略：

1. **Schema隔离**：每个租户使用独立的数据库Schema
   - 优点：安全性高，租户间数据完全隔离
   - 缺点：数据库连接数增加，管理复杂度提高

2. **行级隔离**：共享数据表，通过租户ID字段区分数据
   - 优点：资源利用率高，易于管理
   - 缺点：需要在应用层严格控制数据访问

3. **混合模式**：核心业务数据使用Schema隔离，共享数据使用行级隔离
   - 优点：平衡安全性和资源利用率
   - 缺点：实现复杂度增加

## 技术栈

### 后端技术

- **语言**：Python 3.9+
- **Web框架**：FastAPI
- **ORM**：SQLAlchemy
- **数据库**：PostgreSQL
- **缓存**：Redis
- **消息队列**：RabbitMQ
- **认证**：JWT + OAuth2
- **多租户**：租户上下文 + Schema隔离

### 前端技术

- **框架**：React
- **UI库**：Ant Design
- **状态管理**：Redux
- **路由**：React Router
- **HTTP客户端**：Axios
- **多租户支持**：动态主题与配置
- **多行业支持**：动态组件加载
- **服务商定制**：品牌与主题定制

### 部署技术

- **容器化**：Docker
- **编排**：Docker Compose / Kubernetes
- **CI/CD**：GitHub Actions
- **监控**：Prometheus + Grafana
- **日志**：ELK Stack

## 系统功能模块

### 1. SaaS管理平台

#### 1.1 服务商管理

- 服务商创建与配置
- 服务商状态管理
- 服务商分成设置
- 服务商品牌定制
- 服务商订阅计划管理

#### 1.2 租户管理

- 租户创建与配置
- 租户状态管理
- 租户资源配额设置
- 租户数据备份与恢复
- 租户行业类型设置

#### 1.3 项目管理

- 项目创建与配置
- 项目模板管理
- 项目资源分配
- 项目状态监控
- 项目行业配置

#### 1.4 用户与权限管理

- 服务商管理员账户管理
- 租户管理员账户管理
- 角色与权限模板
- 跨项目权限控制
- 单点登录(SSO)支持

#### 1.5 计费与订阅

- 系统订阅计划管理
- 服务商订阅计划管理
- 租户订阅管理
- 计费周期设置
- 资源使用统计
- 账单生成与支付集成
- 服务商分成管理

#### 1.6 系统监控

- 服务商资源使用监控
- 租户资源使用监控
- 系统性能监控
- 异常告警
- 操作审计日志

#### 1.7 行业管理

- 行业类型管理
- 行业模型管理
- 行业特定插件管理
- 行业模板管理

### 2. 多租户应用层

#### 2.1 多租户前端

- 服务商品牌定制
- 租户品牌定制
- 动态主题配置
- 功能模块权限控制
- 多语言支持
- 行业特定组件动态加载

#### 2.2 多租户后端

- 服务商上下文管理
- 租户上下文管理
- API访问控制
- 数据过滤与隔离
- 租户配置管理
- 行业特定模型动态加载

#### 2.3 API网关

- 请求路由
- 服务商与租户识别
- 认证与授权
- 限流与熔断
- 请求日志
- 行业特定API路由

### 3. 数据存储层

#### 3.1 多租户数据库

- 服务商数据管理
- 租户Schema自动创建
- 行业特定数据模型
- 数据迁移管理
- 数据备份策略
- 性能优化

#### 3.2 文件存储

- 服务商文件管理
- 多租户文件隔离
- 多项目文件隔离
- 存储配额管理
- 文件访问控制
- CDN集成

#### 3.3 缓存服务

- 服务商缓存隔离
- 租户缓存隔离
- 项目缓存隔离
- 缓存失效策略
- 分布式缓存支持

### 4. 插件市场

#### 4.1 插件管理

- 插件上传与审核
- 插件版本管理
- 插件兼容性检查
- 插件评分与评论
- 行业特定插件分类

#### 4.2 插件安装

- 服务商插件管理
- 租户插件订阅
- 项目插件配置
- 插件自动安装
- 插件配置界面
- 插件状态监控
- 行业插件包管理

#### 4.3 插件开发SDK

- 插件开发文档
- 插件开发工具包
- 插件测试环境
- 插件示例代码
- 行业特定插件开发指南

## 实现路线图

### 第一阶段：基础架构改造（1-2个月）

1. 设计并实现服务商-租户-项目多级数据模型
2. 改造认证与授权系统
3. 实现服务商与租户上下文管理
4. 开发SaaS管理平台基础功能
5. 实现数据隔离机制
6. 设计多行业支持架构

### 第二阶段：核心业务适配（2-3个月）

1. 改造现有业务模块支持服务商-租户-项目多级模式
2. 实现服务商管理功能
3. 实现租户管理功能
4. 实现项目管理功能
5. 开发资源配额管理
6. 实现租户数据导入导出
7. 开发计费与订阅系统
8. 实现服务商分成管理

### 第三阶段：插件系统升级（1-2个月）

1. 改造插件系统支持服务商-租户-项目多级模式
2. 开发插件市场基础功能
3. 实现插件安装与配置
4. 开发插件权限控制
5. 实现行业特定插件管理
6. 编写插件开发文档

### 第四阶段：优化与完善（1-2个月）

1. 性能优化与压力测试
2. 安全审计与漏洞修复
3. 完善监控与告警系统
4. 开发运维管理工具
5. 实现多行业模板与配置
6. 完善服务商OEM定制功能
7. 编写系统文档与培训材料

## 订阅计划设计

### 服务商计划

- **初创版**：最多10个租户，分成比例15%，基础服务商功能
- **标准版**：最多50个租户，分成比例20%，完整服务商功能
- **企业版**：无限租户，分成比例25%，完整服务商功能与OEM定制

### 租户计划

#### 基础版

- 单一项目
- 最多10个用户
- 500MB存储空间
- 基础功能模块
- 社区支持

#### 标准版

- 最多3个项目
- 最多50个用户
- 2GB存储空间
- 全部功能模块
- 标准插件包
- 邮件支持
- 行业模板支持

#### 专业版

- 最多10个项目
- 最多200个用户
- 10GB存储空间
- 全部功能模块
- 全部插件访问权
- 优先技术支持
- 行业定制功能

#### 企业版

- 无限项目
- 无限用户
- 可定制存储空间
- 全部功能模块
- 专属插件定制
- 7x24小时技术支持
- 专属部署选项
- 多行业集成支持
- 品牌定制

## 技术挑战与解决方案

### 1. 数据隔离与安全

**挑战**：确保服务商、租户和项目间的数据完全隔离，防止数据泄露。

**解决方案**：
- 实现多级上下文管理（服务商、租户、项目）
- 采用Schema隔离与行级隔离相结合的策略
- 所有数据访问必须通过多级过滤
- 定期安全审计与渗透测试
- 实现细粒度的数据访问控制

### 2. 性能与可扩展性

**挑战**：随着服务商、租户和项目数量增加，系统性能可能下降。

**解决方案**：
- 实现数据库连接池与查询优化
- 引入多级缓存机制减轻数据库负担
- 采用微服务架构实现服务独立扩展
- 实现资源使用监控与自动扩容
- 按行业分片部署服务

### 3. 多级配置管理

**挑战**：不同服务商、租户和项目可能需要不同的系统配置，不同行业需要不同的功能配置。

**解决方案**：
- 设计多层配置系统（系统级、服务商级、租户级、项目级）
- 实现行业特定配置模板
- 实现配置缓存与动态更新
- 开发配置管理界面
- 支持配置模板与批量应用
- 支持服务商品牌定制配置

### 4. 插件兼容性与多行业支持

**挑战**：确保插件在多租户环境中正常工作，并支持不同行业的特定需求。

**解决方案**：
- 设计多级插件隔离机制（服务商、租户、项目）
- 实现插件沙箱环境
- 开发插件兼容性测试工具
- 建立插件审核流程
- 实现行业插件分类与管理
- 开发行业插件开发模板
- 支持插件扩展点标准化

## 迁移策略

### 数据迁移

1. 设计数据迁移工具
2. 创建服务商账户和管理员
3. 为每个现有客户创建租户
4. 根据客户行业类型设置行业属性
5. 将现有数据映射到新的多级模型
6. 根据行业特性调整数据结构
7. 执行数据验证与一致性检查
8. 提供回滚机制

### 客户迁移

1. 制定分批迁移计划，按行业分类迁移
2. 为不同行业客户定制迁移方案
3. 提供迁移前培训，包括行业特定功能培训
4. 按行业执行试点迁移并收集反馈
5. 根据反馈优化迁移流程和行业配置
6. 执行全面迁移
7. 提供行业特定的后续支持

## 结论

通过SaaS化改造，多行业AI系统将实现更高效的部署和运维，同时为客户提供更灵活的服务模式。服务商-租户-项目的多级架构将使系统能够更好地扩展，支持更多行业和客户，同时降低每个客户的使用成本。

多行业支持使系统能够适应不同行业的特定需求，如零售业、餐饮业、酒店业等，大大扩展了系统的市场覆盖面和应用场景。服务商模式则使得系统能够支持多级分销，促进业务增长。

本规划文档提供了SaaS化改造的整体框架和实施路线，后续将根据实际开发进展进行调整和细化。随着系统的不断发展，将支持更多行业类型和业务场景，成为真正的多行业智能SaaS平台。

# Retail AI SaaS 服务

这个项目是一个零售行业人工智能SaaS服务。

## 系统架构

系统由以下几个主要部分组成：

1. **主服务**：提供核心业务逻辑
2. **服务商管理系统**：供服务提供商管理其服务
3. **MCP服务器**：提供智能对话能力的中间件
4. **Traefik**：服务网关，负责路由和负载均衡
5. **Redis**：用于服务发现和缓存

## 最近更改

1. **MCP服务动态发现**：移除了硬编码的URL，改为基于Docker标签的自动发现
2. **增强Redis连接**：改进了Redis连接和注册机制，添加了重试逻辑，增强了错误处理
3. **动态端口分配**：MCP服务器启动时会分配端口并注册到Traefik

## MCP服务自动发现机制

系统使用Docker和Traefik的自动服务发现机制：

1. MCP服务器启动时会注册到Redis
2. MCP服务器启动容器时会自动添加Traefik标签
3. Traefik通过Docker提供者自动发现带标签的MCP服务
4. 无需硬编码MCP服务器的地址和端口

## 快速开始

### 前提条件

- Docker 和 Docker Compose
- Python 3.10 或更高版本

### 启动步骤

1. 启动基础设施（Redis, Traefik）：

```bash
cd Tools/retail-ai-saas
docker-compose up -d
```

2. 启动MCP服务器：

```bash
cd backend
python -m uvicorn plugins.sapi.main:app --host 0.0.0.0 --port 3001
```

3. 使用API创建MCP实例：

```bash
curl -X POST http://localhost:3001/api/v1/mcp/servers \
  -H "Content-Type: application/json" \
  -d '{"name": "测试MCP服务器", "transport_types": ["streamhttp"]}'
```

4. 访问MCP实例：

通过自动生成的URL访问MCP实例，格式为：`http://mcp-{server_id}.localhost`

## 访问MCP服务

可以通过以下URL访问MCP服务：

- 通用MCP API：http://localhost/mcp
- 特定MCP实例：http://mcp-{server_id}.localhost

## 故障排查

如果MCP服务无法访问，请检查：

1. Redis连接是否正常：

```bash
docker exec -it retail-ai-saas-redis-dev redis-cli ping
```

2. 检查MCP实例是否成功注册：

```bash
docker exec -it retail-ai-saas-redis-dev redis-cli keys "mcp:server:*"
docker exec -it retail-ai-saas-redis-dev redis-cli hgetall "mcp:server:{server_id}"
```

3. 检查Traefik配置生成器日志：

```bash
docker logs retail-ai-saas-traefik-config-generator
```

4. 检查生成的配置文件：

```bash
cat service-provider/docker/traefik/dynamic/mcp_services.yml
```
