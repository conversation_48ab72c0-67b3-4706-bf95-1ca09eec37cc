#!/usr/bin/env python
# -*- coding: utf-8 -*-

import uuid
import logging
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
from sqlalchemy import select, func, and_, or_, desc
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import joinedload

from models.area_survey import AreaSurvey
from models.store import Store
from models.user import User
from schemas.area_survey import AreaSurveyCreate, AreaSurveyUpdate, AreaSurveyStatusUpdate

logger = logging.getLogger(__name__)

class AreaSurveyService:
    @staticmethod
    async def get_surveys(
        db: AsyncSession,
        project_id: uuid.UUID,
        store_id: Optional[uuid.UUID] = None,
        status: Optional[str] = None,
        survey_type: Optional[str] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        search: Optional[str] = None,
        skip: int = 0,
        limit: int = 100
    ) -> Tuple[List[Dict[str, Any]], int]:
        """获取周边调研列表"""
        try:
            # 构建查询
            query = select(AreaSurvey).where(AreaSurvey.project_id == project_id)

            # 应用过滤条件
            if store_id:
                query = query.where(AreaSurvey.store_id == store_id)

            if status:
                query = query.where(AreaSurvey.status == status)

            if survey_type:
                query = query.where(AreaSurvey.survey_type == survey_type)

            if start_date:
                query = query.where(AreaSurvey.created_at >= start_date)

            if end_date:
                query = query.where(AreaSurvey.created_at <= end_date)

            if search:
                query = query.where(
                    or_(
                        AreaSurvey.title.ilike(f"%{search}%"),
                        AreaSurvey.description.ilike(f"%{search}%"),
                        AreaSurvey.target_store_name.ilike(f"%{search}%"),
                        AreaSurvey.target_store_address.ilike(f"%{search}%")
                    )
                )

            # 计算总数
            count_query = select(func.count()).select_from(query.subquery())
            total = await db.scalar(count_query)

            # 应用分页
            query = query.order_by(desc(AreaSurvey.created_at)).offset(skip).limit(limit)

            # 加载关联数据
            query = query.options(
                joinedload(AreaSurvey.store),
                joinedload(AreaSurvey.creator),
                joinedload(AreaSurvey.updater)
            )

            # 执行查询
            result = await db.execute(query)
            surveys = result.scalars().all()

            # 转换为字典列表
            survey_list = []
            for survey in surveys:
                survey_dict = {
                    "id": survey.id,
                    "title": survey.title,
                    "description": survey.description,
                    "survey_type": survey.survey_type,
                    "status": survey.status,
                    "start_date": survey.start_date,
                    "end_date": survey.end_date,
                    "completed_date": survey.completed_date,
                    "created_at": survey.created_at,
                    "updated_at": survey.updated_at,
                    "created_by": survey.created_by,
                    "updated_by": survey.updated_by,
                    "project_id": survey.project_id,
                    "store_id": survey.store_id,
                    "target_store_name": survey.target_store_name,
                    "target_store_address": survey.target_store_address,
                    "target_store_distance": survey.target_store_distance,
                    "target_store_type": survey.target_store_type,
                    "survey_data": survey.survey_data,
                    "conclusion": survey.conclusion,
                    "recommendations": survey.recommendations,
                    "attachments": survey.attachments,
                    "tags": survey.tags,
                    "rating": survey.rating,
                    "is_ai_analyzed": survey.is_ai_analyzed,
                    "ai_analysis": survey.ai_analysis,
                    "store_name": survey.store.name if survey.store else None,
                    "creator_name": survey.creator.full_name if survey.creator else None,
                    "updater_name": survey.updater.full_name if survey.updater else None
                }
                survey_list.append(survey_dict)

            return survey_list, total
        except Exception as e:
            logger.error(f"获取周边调研列表失败: {str(e)}")
            raise e

    @staticmethod
    async def get_survey_by_id(
        db: AsyncSession,
        project_id: uuid.UUID,
        survey_id: uuid.UUID
    ) -> Dict[str, Any]:
        """获取周边调研详情"""
        try:
            query = select(AreaSurvey).where(
                and_(
                    AreaSurvey.id == survey_id,
                    AreaSurvey.project_id == project_id
                )
            ).options(
                joinedload(AreaSurvey.store),
                joinedload(AreaSurvey.creator),
                joinedload(AreaSurvey.updater)
            )

            result = await db.execute(query)
            survey = result.scalar_one_or_none()

            if not survey:
                raise ValueError("周边调研不存在")

            survey_dict = {
                "id": survey.id,
                "title": survey.title,
                "description": survey.description,
                "survey_type": survey.survey_type,
                "status": survey.status,
                "start_date": survey.start_date,
                "end_date": survey.end_date,
                "completed_date": survey.completed_date,
                "created_at": survey.created_at,
                "updated_at": survey.updated_at,
                "created_by": survey.created_by,
                "updated_by": survey.updated_by,
                "project_id": survey.project_id,
                "store_id": survey.store_id,
                "target_store_name": survey.target_store_name,
                "target_store_address": survey.target_store_address,
                "target_store_distance": survey.target_store_distance,
                "target_store_type": survey.target_store_type,
                "survey_data": survey.survey_data,
                "conclusion": survey.conclusion,
                "recommendations": survey.recommendations,
                "attachments": survey.attachments,
                "tags": survey.tags,
                "rating": survey.rating,
                "is_ai_analyzed": survey.is_ai_analyzed,
                "ai_analysis": survey.ai_analysis,
                "store_name": survey.store.name if survey.store else None,
                "creator_name": survey.creator.full_name if survey.creator else None,
                "updater_name": survey.updater.full_name if survey.updater else None
            }

            return survey_dict
        except Exception as e:
            logger.error(f"获取周边调研详情失败: {str(e)}")
            raise e

    @staticmethod
    async def create_survey(
        db: AsyncSession,
        project_id: uuid.UUID,
        survey_data: AreaSurveyCreate,
        created_by: uuid.UUID
    ) -> Dict[str, Any]:
        """创建周边调研"""
        try:
            # 创建周边调研
            survey = AreaSurvey(
                title=survey_data.title,
                description=survey_data.description,
                survey_type=survey_data.survey_type,
                status=survey_data.status,
                start_date=survey_data.start_date,
                end_date=survey_data.end_date,
                target_store_name=survey_data.target_store_name,
                target_store_address=survey_data.target_store_address,
                target_store_distance=survey_data.target_store_distance,
                target_store_type=survey_data.target_store_type,
                survey_data=survey_data.survey_data or {},
                conclusion=survey_data.conclusion,
                recommendations=survey_data.recommendations,
                attachments=survey_data.attachments or [],
                tags=survey_data.tags or [],
                rating=survey_data.rating,
                project_id=project_id,
                store_id=survey_data.store_id,
                created_by=created_by
            )

            db.add(survey)
            await db.commit()
            await db.refresh(survey)

            # 获取周边调研详情
            survey_dict = await AreaSurveyService.get_survey_by_id(
                db=db,
                project_id=project_id,
                survey_id=survey.id
            )

            return survey_dict
        except Exception as e:
            await db.rollback()
            logger.error(f"创建周边调研失败: {str(e)}")
            raise e

    @staticmethod
    async def update_survey(
        db: AsyncSession,
        project_id: uuid.UUID,
        survey_id: uuid.UUID,
        survey_data: AreaSurveyUpdate,
        updated_by: uuid.UUID
    ) -> Dict[str, Any]:
        """更新周边调研"""
        try:
            # 获取周边调研
            query = select(AreaSurvey).where(
                and_(
                    AreaSurvey.id == survey_id,
                    AreaSurvey.project_id == project_id
                )
            )
            result = await db.execute(query)
            survey = result.scalar_one_or_none()

            if not survey:
                raise ValueError("周边调研不存在")

            # 更新周边调研
            update_data = survey_data.dict(exclude_unset=True)
            for key, value in update_data.items():
                setattr(survey, key, value)

            # 设置更新者和更新时间
            survey.updated_by = updated_by
            survey.updated_at = datetime.utcnow()

            # 如果状态为已完成，设置完成日期
            if update_data.get("status") == "completed" and not survey.completed_date:
                survey.completed_date = datetime.utcnow()

            await db.commit()
            await db.refresh(survey)

            # 获取周边调研详情
            survey_dict = await AreaSurveyService.get_survey_by_id(
                db=db,
                project_id=project_id,
                survey_id=survey.id
            )

            return survey_dict
        except Exception as e:
            await db.rollback()
            logger.error(f"更新周边调研失败: {str(e)}")
            raise e

    @staticmethod
    async def update_survey_status(
        db: AsyncSession,
        project_id: uuid.UUID,
        survey_id: uuid.UUID,
        status: str,
        updated_by: uuid.UUID
    ) -> Dict[str, Any]:
        """更新周边调研状态"""
        try:
            # 获取周边调研
            query = select(AreaSurvey).where(
                and_(
                    AreaSurvey.id == survey_id,
                    AreaSurvey.project_id == project_id
                )
            )
            result = await db.execute(query)
            survey = result.scalar_one_or_none()

            if not survey:
                raise ValueError("周边调研不存在")

            # 更新周边调研状态
            survey.status = status
            survey.updated_by = updated_by
            survey.updated_at = datetime.utcnow()

            # 如果状态为已完成，设置完成日期
            if status == "completed" and not survey.completed_date:
                survey.completed_date = datetime.utcnow()

            await db.commit()
            await db.refresh(survey)

            # 获取周边调研详情
            survey_dict = await AreaSurveyService.get_survey_by_id(
                db=db,
                project_id=project_id,
                survey_id=survey.id
            )

            return survey_dict
        except Exception as e:
            await db.rollback()
            logger.error(f"更新周边调研状态失败: {str(e)}")
            raise e

    @staticmethod
    async def delete_survey(
        db: AsyncSession,
        project_id: uuid.UUID,
        survey_id: uuid.UUID
    ) -> bool:
        """删除周边调研"""
        try:
            # 获取周边调研
            query = select(AreaSurvey).where(
                and_(
                    AreaSurvey.id == survey_id,
                    AreaSurvey.project_id == project_id
                )
            )
            result = await db.execute(query)
            survey = result.scalar_one_or_none()

            if not survey:
                raise ValueError("周边调研不存在")

            # 删除周边调研
            await db.delete(survey)
            await db.commit()

            return True
        except Exception as e:
            await db.rollback()
            logger.error(f"删除周边调研失败: {str(e)}")
            raise e

    @staticmethod
    async def get_survey_stats(
        db: AsyncSession,
        project_id: uuid.UUID,
        store_id: Optional[uuid.UUID] = None
    ) -> Dict[str, Any]:
        """获取周边调研统计数据"""
        try:
            # 构建基础查询
            base_query = select(AreaSurvey).where(AreaSurvey.project_id == project_id)

            if store_id:
                base_query = base_query.where(AreaSurvey.store_id == store_id)

            # 计算总数
            total_query = select(func.count()).select_from(base_query.subquery())
            total = await db.scalar(total_query)

            # 按状态统计
            stats = {
                "total": total,
                "draft": 0,
                "in_progress": 0,
                "completed": 0,
                "cancelled": 0,
                "by_type": {},
                "by_rating": {
                    "1": 0,
                    "2": 0,
                    "3": 0,
                    "4": 0,
                    "5": 0
                },
                "recent_surveys": []
            }

            # 按状态分组统计
            for status in ["draft", "in_progress", "completed", "cancelled"]:
                status_query = select(func.count()).select_from(
                    base_query.where(AreaSurvey.status == status).subquery()
                )
                status_count = await db.scalar(status_query)
                stats[status] = status_count

            # 按类型分组统计
            type_query = select(AreaSurvey.survey_type, func.count()).where(
                AreaSurvey.project_id == project_id
            )
            if store_id:
                type_query = type_query.where(AreaSurvey.store_id == store_id)
            type_query = type_query.group_by(AreaSurvey.survey_type)

            type_result = await db.execute(type_query)
            type_counts = type_result.all()

            for survey_type, count in type_counts:
                stats["by_type"][survey_type] = count

            # 按评分分组统计
            for rating in range(1, 6):
                rating_query = select(func.count()).select_from(
                    base_query.where(AreaSurvey.rating == rating).subquery()
                )
                rating_count = await db.scalar(rating_query)
                stats["by_rating"][str(rating)] = rating_count

            # 获取最近的调研
            recent_query = base_query.order_by(desc(AreaSurvey.created_at)).limit(5)
            recent_query = recent_query.options(
                joinedload(AreaSurvey.store),
                joinedload(AreaSurvey.creator)
            )

            recent_result = await db.execute(recent_query)
            recent_surveys = recent_result.scalars().all()

            # 转换为字典列表
            recent_list = []
            for survey in recent_surveys:
                survey_dict = {
                    "id": survey.id,
                    "project_id": survey.project_id,
                    "store_id": survey.store_id,
                    "title": survey.title,
                    "status": survey.status,
                    "survey_type": survey.survey_type,
                    "created_at": survey.created_at,
                    "target_store_name": survey.target_store_name,
                    "store_name": survey.store.name if survey.store else None,
                    "creator_name": survey.creator.full_name if survey.creator else (survey.creator.username if survey.creator else None),
                    "created_by": survey.created_by,
                    "updated_by": survey.updated_by,
                    "updated_at": survey.updated_at
                }
                recent_list.append(survey_dict)

            stats["recent_surveys"] = recent_list

            return stats
        except Exception as e:
            logger.error(f"获取周边调研统计数据失败: {str(e)}")
            raise e

    @staticmethod
    async def analyze_survey_with_ai(
        db: AsyncSession,
        project_id: uuid.UUID,
        survey_id: uuid.UUID
    ) -> Dict[str, Any]:
        """使用AI分析周边调研"""
        try:
            # 获取周边调研
            query = select(AreaSurvey).where(
                and_(
                    AreaSurvey.id == survey_id,
                    AreaSurvey.project_id == project_id
                )
            )
            result = await db.execute(query)
            survey = result.scalar_one_or_none()

            if not survey:
                raise ValueError("周边调研不存在")

            # TODO: 调用AI服务进行分析
            # 这里是一个示例分析结果
            ai_analysis = {
                "summary": "这是一个AI生成的分析摘要",
                "insights": [
                    "洞察1: 竞争对手价格普遍低于我们5-10%",
                    "洞察2: 竞争对手促销活动频率更高",
                    "洞察3: 我们的产品种类更丰富"
                ],
                "recommendations": [
                    "建议1: 考虑调整部分产品价格",
                    "建议2: 增加促销活动频率",
                    "建议3: 突出我们产品种类丰富的优势"
                ],
                "competitive_analysis": {
                    "price_comparison": {
                        "our_avg_price": 100,
                        "competitor_avg_price": 90,
                        "difference_percentage": -10
                    },
                    "promotion_comparison": {
                        "our_promotion_count": 2,
                        "competitor_promotion_count": 5
                    },
                    "product_comparison": {
                        "our_product_count": 200,
                        "competitor_product_count": 150
                    }
                }
            }

            # 更新周边调研
            survey.is_ai_analyzed = True
            survey.ai_analysis = ai_analysis

            # 如果没有结论和建议，使用AI生成的
            if not survey.conclusion:
                survey.conclusion = "根据AI分析，竞争对手价格普遍低于我们5-10%，促销活动频率更高，但我们的产品种类更丰富。"

            if not survey.recommendations:
                survey.recommendations = "1. 考虑调整部分产品价格\n2. 增加促销活动频率\n3. 突出我们产品种类丰富的优势"

            await db.commit()
            await db.refresh(survey)

            # 获取周边调研详情
            survey_dict = await AreaSurveyService.get_survey_by_id(
                db=db,
                project_id=project_id,
                survey_id=survey.id
            )

            return survey_dict
        except Exception as e:
            await db.rollback()
            logger.error(f"AI分析周边调研失败: {str(e)}")
            raise e
