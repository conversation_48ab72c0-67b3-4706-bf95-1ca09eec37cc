import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  Button,
  message,
  Modal,
  Typography,
  Card,
  Row,
  Col,
  Statistic,
  Badge,
  Progress,
  Space,
  Tag,
  Tooltip,
  Alert,
  Spin,
  Empty,
  Avatar,
  Switch,
  Select,
  notification,
  Form,
  Input,
  Result,
  Divider
} from 'antd';
import {
  TrophyOutlined,
  GiftOutlined,
  SoundOutlined,
  SoundFilled,
  PlayCircleOutlined,
  StarOutlined,
  FireOutlined,
  SmileOutlined,
  CrownOutlined,
  SettingOutlined,
  HistoryOutlined,
  ReloadOutlined,
  QrcodeOutlined,
  ShareAltOutlined
} from '@ant-design/icons';
import './EnhancedWheelOfFortune.css';
import marketingGameApi from '../service/api/marketingGame';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;

// 主题配置
const THEMES = {
  classic: {
    name: '经典风格',
    colors: ['#FF9800', '#2196F3', '#4CAF50', '#E91E63', '#673AB7', '#00BCD4', '#FFC107', '#607D8B'],
    bgGradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    centerColor: '#fff',
    borderColor: '#ff5722',
    glowColor: '#ffeb3b'
  },
  neon: {
    name: '霓虹风格',
    colors: ['#FF073A', '#39FF14', '#00BFFF', '#FF1493', '#FF8C00', '#8A2BE2', '#00CED1', '#FFD700'],
    bgGradient: 'linear-gradient(135deg, #0c0c0c 0%, #1a1a1a 100%)',
    centerColor: '#000',
    borderColor: '#00BFFF',
    glowColor: '#39FF14'
  },
  rainbow: {
    name: '彩虹风格',
    colors: ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#FFA07A', '#98FB98'],
    bgGradient: 'linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%)',
    centerColor: '#fff',
    borderColor: '#FF6B6B',
    glowColor: '#FFEAA7'
  },
  galaxy: {
    name: '星系风格',
    colors: ['#667eea', '#764ba2', '#f093fb', '#f5576c', '#4facfe', '#00f2fe', '#43e97b', '#38f9d7'],
    bgGradient: 'radial-gradient(ellipse at center, #1e3c72 0%, #2a5298 100%)',
    centerColor: '#fff',
    borderColor: '#667eea',
    glowColor: '#f093fb'
  }
};

// 粒子效果配置
const PARTICLE_CONFIGS = {
  sparkle: { count: 50, type: 'star', color: '#FFD700' },
  confetti: { count: 100, type: 'rect', color: 'random' },
  fireworks: { count: 30, type: 'circle', color: 'rainbow' },
  hearts: { count: 20, type: 'heart', color: '#FF69B4' }
};

const EnhancedWheelOfFortune = ({ gameId, embedded = false }) => {
  // 基础状态
  const [loading, setLoading] = useState(true);
  const [game, setGame] = useState(null);
  const [prizes, setPrizes] = useState([]);
  const [spinning, setSpinning] = useState(false);
  const [canSpin, setCanSpin] = useState(true);
  const [remainingDraws, setRemainingDraws] = useState(0);
  
  // 界面状态
  const [currentTheme, setCurrentTheme] = useState('classic');
  const [soundEnabled, setSoundEnabled] = useState(true);
  const [particleEffect, setParticleEffect] = useState('sparkle');
  const [autoRotate, setAutoRotate] = useState(true);
  const [showStats, setShowStats] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [showHistory, setShowHistory] = useState(false);
  
  // 游戏状态
  const [winningPrize, setWinningPrize] = useState(null);
  const [showResult, setShowResult] = useState(false);
  const [totalSpins, setTotalSpins] = useState(0);
  const [winCount, setWinCount] = useState(0);
  const [lastWinTime, setLastWinTime] = useState(null);
  const [winStreak, setWinStreak] = useState(0);
  const [winRecordId, setWinRecordId] = useState(null);
  const [claiming, setClaiming] = useState(false);
  const [showQueryModal, setShowQueryModal] = useState(false);
  const [queryPhone, setQueryPhone] = useState('');
  const [queryResult, setQueryResult] = useState(null);
  const [querying, setQuerying] = useState(false);
  
  // 验证状态
  const [needVerification, setNeedVerification] = useState(false);
  const [verifying, setVerifying] = useState(false);
  const [verified, setVerified] = useState(false);
  const [showVerification, setShowVerification] = useState(false);
  const [receiptVerified, setReceiptVerified] = useState(false);
  const [receiptCode, setReceiptCode] = useState('');
  const [receiptAmount, setReceiptAmount] = useState('');
  const [phoneVerified, setPhoneVerified] = useState(false);
  const [phone, setPhone] = useState('');
  const [verifyingPhone, setVerifyingPhone] = useState(false);
  
  // 引用
  const wheelRef = useRef(null);
  const canvasRef = useRef(null);
  const audioRef = useRef(null);
  const particleAnimationRef = useRef(null);
  
  // 音频引用
  const spinSoundRef = useRef(null);
  const winSoundRef = useRef(null);
  const loseSoundRef = useRef(null);
  const clickSoundRef = useRef(null);

  // 初始化音频
  useEffect(() => {
    if (soundEnabled) {
      try {
        spinSoundRef.current = new Audio('/sounds/wheel-spin.mp3');
        winSoundRef.current = new Audio('/sounds/win-celebration.mp3');
        loseSoundRef.current = new Audio('/sounds/try-again.mp3');
        clickSoundRef.current = new Audio('/sounds/click.mp3');
        
        // 设置音量
        [spinSoundRef, winSoundRef, loseSoundRef, clickSoundRef].forEach(ref => {
          if (ref.current) {
            ref.current.volume = 0.3;
            ref.current.preload = 'auto';
          }
        });
      } catch (error) {
        console.log('音频加载失败:', error);
      }
    }
  }, [soundEnabled]);

  // 监控奖品状态变化
  useEffect(() => {
    if (winningPrize) {
      console.log("Enhanced winningPrize状态更新:", winningPrize);
    }
  }, [winningPrize]);

  // 监控弹窗显示状态
  useEffect(() => {
    if (showResult) {
      console.log("Enhanced 显示结果弹窗，当前winningPrize状态:", winningPrize);
    }
  }, [showResult, winningPrize]);

  // 播放音效
  const playSound = useCallback((type) => {
    if (!soundEnabled) return;
    
    try {
      let audio = null;
      switch (type) {
        case 'spin':
          audio = spinSoundRef.current;
          break;
        case 'win':
          audio = winSoundRef.current;
          break;
        case 'lose':
          audio = loseSoundRef.current;
          break;
        case 'click':
          audio = clickSoundRef.current;
          break;
        default:
          return;
      }
      
      if (audio) {
        audio.currentTime = 0;
        audio.play().catch(e => console.log('音频播放失败:', e));
      }
    } catch (error) {
      console.log('播放音效失败:', error);
    }
  }, [soundEnabled]);

  // 获取游戏数据
  const fetchGameData = useCallback(async () => {
    try {
      setLoading(true);
      
      // 检查是否有项目ID，如果没有则使用公开API
      const projectId = localStorage.getItem('project_id');
      // 强制使用公开API，因为这是外部分享页面
      const isPublicAccess = true; // 强制设置为true

      let gameResponse, prizesResponse;

      if (isPublicAccess) {
        // 外部分享页面，使用公开API
        gameResponse = await marketingGameApi.getPublicGameDetail(gameId);
        prizesResponse = await marketingGameApi.getPublicGamePrizes(gameId);
      } else {
        // 内部用户，使用普通API
        gameResponse = await marketingGameApi.getGameDetail(gameId);
        prizesResponse = await marketingGameApi.getGamePrizes(gameId);
      }

      if (!gameResponse.success) {
        message.error(gameResponse.message || '获取游戏详情失败');
        return;
      }
      
      // 确保game.remaining_draws有默认值
      const gameData = {
        ...gameResponse.data,
        remaining_draws: gameResponse.data.remaining_draws !== undefined ? gameResponse.data.remaining_draws :
                         (gameResponse.data.max_draws_per_user !== undefined ? gameResponse.data.max_draws_per_user : 0),
        // 添加默认配置
        settings: {
          ...gameResponse.data.settings,
          enable_receipt_verification:
            gameResponse.data.settings?.enable_receipt_verification !== undefined
              ? gameResponse.data.settings.enable_receipt_verification
              : gameResponse.data.min_consumption > 0,
          theme_style: gameResponse.data.settings?.theme_style || 'classic'
        }
      };

      console.log("Enhanced Game settings:", gameData.settings);
      console.log("Enhanced Public access mode:", isPublicAccess);

      setGame(gameData);
      
      // 获取奖品列表
      if (!prizesResponse.success) {
        message.error(prizesResponse.message || '获取奖品列表失败');
        return;
      }

      // 处理奖品数据
      let originalPrizes = prizesResponse.data || [];

      // 过滤掉重复的奖品
      const uniquePrizes = [];
      const seenIds = new Set();

      for (const prize of originalPrizes) {
        if (prize.is_default) {
          if (!seenIds.has('default')) {
            uniquePrizes.push({
              ...prize,
              id: 'default'
            });
            seenIds.add('default');
          }
        } else if (!seenIds.has(prize.id)) {
          uniquePrizes.push(prize);
          seenIds.add(prize.id);
        }
      }

      originalPrizes = uniquePrizes;

      // 检查是否已经有默认奖品
      const hasDefaultPrize = originalPrizes.some(prize => prize.is_default);

      // 如果没有默认奖品，添加一个
      if (!hasDefaultPrize) {
        const defaultPrize = {
          id: 'default',
          name: '谢谢参与',
          image_url: 'https://img.icons8.com/color/96/000000/sad--v1.png',
          probability: 0,
          is_default: true,
          weight_factor: 1.0
        };
        originalPrizes.push(defaultPrize);
      }

      // 计算总概率，确保概率总和为100%
      const totalProbability = originalPrizes.reduce((sum, prize) => {
        return sum + (prize.probability || 0);
      }, 0);

      // 如果总概率为0，则平均分配；如果总概率不为100，则按比例调整
      const normalizedPrizes = originalPrizes.map(prize => {
        let normalizedProbability;
        
        if (totalProbability === 0) {
          // 总概率为0，平均分配
          normalizedProbability = 100 / originalPrizes.length;
        } else {
          // 按比例调整到100%
          normalizedProbability = (prize.probability || 0) * 100 / totalProbability;
        }

        return {
          ...prize,
          normalizedProbability,
          weight_factor: prize.weight_factor || 1.0  // 确保有默认的权重因子
        };
      });

      // 确保至少有一个奖品
      if (normalizedPrizes.length === 0) {
        normalizedPrizes.push({
          id: 'default',
          name: '谢谢参与',
          image_url: 'https://img.icons8.com/color/96/000000/sad--v1.png',
          probability: 100,
          normalizedProbability: 100,
          is_default: true,
          weight_factor: 1.0
        });
      }

      // console.log("Enhanced 最终奖品列表:", normalizedPrizes);
      setPrizes(normalizedPrizes);
      
      // 计算剩余抽奖次数
      let calculatedRemainingDraws = gameData.remaining_draws;
      
      if (isPublicAccess) {
        // 外部用户，需要根据手机号查询已使用次数
        const userPhone = localStorage.getItem('user_phone');
        if (userPhone && gameData.max_draws_per_user > 0) {
          try {
            // 查询该手机号的中奖记录
            const queryResponse = await marketingGameApi.queryPrizesByPhone(gameId, userPhone);
            if (queryResponse.success && queryResponse.data) {
              const usedDraws = queryResponse.data.total_draws || 0;
              calculatedRemainingDraws = Math.max(0, gameData.max_draws_per_user - usedDraws);
              console.log("Enhanced 外部用户已使用次数:", usedDraws, "剩余次数:", calculatedRemainingDraws);
            } else {
              // 如果查询失败，使用最大次数
              calculatedRemainingDraws = gameData.max_draws_per_user;
            }
          } catch (error) {
            console.error("Enhanced 查询用户抽奖记录失败:", error);
            calculatedRemainingDraws = gameData.max_draws_per_user;
          }
        } else {
          // 没有手机号或没有限制，使用默认值
          calculatedRemainingDraws = gameData.max_draws_per_user || 1;
        }
      }
      
      setRemainingDraws(calculatedRemainingDraws);
      
      // 只有内部用户才获取用户统计
      if (!isPublicAccess) {
        await fetchUserStats();
      } else {
        // 外部用户，尝试根据手机号获取统计数据
        const userPhone = localStorage.getItem('user_phone');
        if (userPhone) {
          try {
            const queryResponse = await marketingGameApi.queryPrizesByPhone(gameId, userPhone);
            if (queryResponse.success && queryResponse.data) {
              setTotalSpins(queryResponse.data.total_draws || 0);
              setWinCount(queryResponse.data.total_wins || 0);
              setWinStreak(0); // 连胜纪录暂时设为0，因为API没有返回这个数据
              setLastWinTime(null);
              console.log("Enhanced 外部用户统计数据:", queryResponse.data);
            } else {
              // 设置默认统计
              setTotalSpins(0);
              setWinCount(0);
              setWinStreak(0);
              setLastWinTime(null);
            }
          } catch (error) {
            console.error("Enhanced 获取外部用户统计失败:", error);
            // 设置默认统计
            setTotalSpins(0);
            setWinCount(0);
            setWinStreak(0);
            setLastWinTime(null);
          }
        } else {
          // 没有手机号，设置默认统计
          setTotalSpins(0);
          setWinCount(0);
          setWinStreak(0);
          setLastWinTime(null);
        }
      }
      
      // 检查是否需要验证
      const needVerify = gameData.min_consumption > 0 || gameData.settings?.enable_receipt_verification;
      setNeedVerification(needVerify);
      
    } catch (error) {
      console.error('获取游戏数据失败:', error);
      message.error('获取游戏数据失败');
    } finally {
      setLoading(false);
    }
  }, [gameId]);

  // 获取用户统计
  const fetchUserStats = useCallback(async () => {
    try {
      const response = await marketingGameApi.getUserStats(gameId);
      if (response.success) {
        const stats = response.data;
        setTotalSpins(stats.total_spins || 0);
        setWinCount(stats.win_count || 0);
        setLastWinTime(stats.last_win_time);
        setWinStreak(stats.win_streak || 0);
      }
    } catch (error) {
      console.error('获取用户统计失败:', error);
    }
  }, [gameId]);

  // 初始化
  useEffect(() => {
    fetchGameData();
  }, [fetchGameData]);

  // 绘制转盘
  const drawWheel = useCallback(() => {
    const canvas = canvasRef.current;
    
    if (!canvas) {
      // Canvas引用为空，延迟重试
      setTimeout(() => {
        const retryCanvas = canvasRef.current;
        if (retryCanvas && prizes.length > 0) {
          drawWheelInternal(retryCanvas);
        }
      }, 500);
      return;
    }
    
    if (!prizes.length) {
      return;
    }
    
    drawWheelInternal(canvas);
  }, [prizes, currentTheme]);

  // 实际的绘制函数
  const drawWheelInternal = useCallback((canvas) => {
    const ctx = canvas.getContext('2d');
    const centerX = canvas.width / 2;
    const centerY = canvas.height / 2;
    const radius = Math.min(centerX, centerY) - 20;
    
    // 清空画布
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    const theme = THEMES[currentTheme];
    
    // 计算总权重（简化逻辑，与基础版保持一致）
    const totalProbability = prizes.reduce((sum, prize) => {
      return sum + (prize.normalizedProbability || prize.probability || 0);
    }, 0);
    
    // 如果总概率为0，平均分配
    const useEqualDistribution = totalProbability === 0;
    
    // 计算每个奖品的角度
    let startAngle = 0;
    const prizeAngles = prizes.map((prize, index) => {
      let angle;
      if (useEqualDistribution) {
        // 平均分配
        angle = (2 * Math.PI) / prizes.length;
      } else {
        // 根据概率分配
        const probability = prize.normalizedProbability || prize.probability || 0;
        angle = (probability / totalProbability) * 2 * Math.PI;
      }
      
      const currentStartAngle = startAngle;
      startAngle += angle;
      
      return {
        prize,
        startAngle: currentStartAngle,
        endAngle: startAngle,
        angle
      };
    });
    
    // 绘制奖品扇形
    prizeAngles.forEach((item, index) => {
      const { prize, startAngle, endAngle, angle } = item;
      const colorIndex = index % theme.colors.length;
      
      // 绘制扇形
      ctx.beginPath();
      ctx.moveTo(centerX, centerY);
      ctx.arc(centerX, centerY, radius, startAngle, endAngle);
      ctx.closePath();
      
      // 填充颜色
      ctx.fillStyle = theme.colors[colorIndex];
      ctx.fill();
      
      // 绘制边框
      ctx.strokeStyle = theme.borderColor;
      ctx.lineWidth = 2;
      ctx.stroke();
      
      // 绘制发光效果
      if (currentTheme === 'neon') {
        ctx.shadowColor = theme.glowColor;
        ctx.shadowBlur = 10;
        ctx.stroke();
        ctx.shadowBlur = 0;
      }
      
      // 绘制文字和图片
      const textAngle = startAngle + angle / 2;
      const textRadius = radius * 0.7;
      const textX = centerX + Math.cos(textAngle) * textRadius;
      const textY = centerY + Math.sin(textAngle) * textRadius;
      
      ctx.save();
      ctx.translate(textX, textY);
      ctx.rotate(textAngle + Math.PI / 2);
      
      // 绘制奖品名称
      ctx.fillStyle = currentTheme === 'neon' ? '#fff' : '#333';
      ctx.font = 'bold 14px Arial';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      
      // 处理长文本
      const maxWidth = radius * 0.4;
      const text = prize.name || '奖品';
      if (ctx.measureText(text).width > maxWidth) {
        ctx.font = 'bold 12px Arial';
      }
      ctx.fillText(text, 0, -8);
      
      // 如果角度足够大，显示概率或权重信息
      if (angle > 0.3) { // 约17度
        ctx.font = '10px Arial';
        const probability = prize.normalizedProbability || prize.probability || 0;
        const weightFactor = prize.weight_factor || 1;
        
        // 格式化概率显示，保留1位小数
        const formattedProbability = probability.toFixed(1);
        
        if (weightFactor !== 1) {
          ctx.fillText(`${formattedProbability}% (x${weightFactor})`, 0, 8);
        } else {
          ctx.fillText(`${formattedProbability}%`, 0, 8);
        }
      }
      
      ctx.restore();
    });
    
    // 绘制中心圆
    ctx.beginPath();
    ctx.arc(centerX, centerY, 50, 0, 2 * Math.PI);
    ctx.fillStyle = theme.centerColor;
    ctx.fill();
    ctx.strokeStyle = theme.borderColor;
    ctx.lineWidth = 4;
    ctx.stroke();
    
    // 绘制中心文字
    ctx.fillStyle = currentTheme === 'neon' ? '#fff' : '#333';
    ctx.font = 'bold 16px Arial';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText('抽奖', centerX, centerY);
    
    // 不再在canvas上绘制指针，改为使用独立的HTML元素
    
  }, [prizes, currentTheme]);

  // 监听转盘变化重绘
  useEffect(() => {
    // 添加延迟确保Canvas已经渲染完成
    const timer = setTimeout(() => {
      drawWheel();
    }, 100);
    
    return () => clearTimeout(timer);
  }, [drawWheel]);

  // 监听奖品数据变化，重新绘制转盘
  useEffect(() => {
    if (prizes.length > 0) {
      // 多次尝试绘制，确保Canvas就绪
      const attemptDraw = (attempt = 1) => {
        const canvas = canvasRef.current;
        if (canvas) {
          drawWheelInternal(canvas);
        } else if (attempt < 10) {
          setTimeout(() => attemptDraw(attempt + 1), 100 * attempt);
        }
      };
      
      attemptDraw();
    }
  }, [prizes, currentTheme, drawWheelInternal]);

  // Canvas引用变化时重绘
  useEffect(() => {
    if (canvasRef.current && prizes.length > 0) {
      drawWheelInternal(canvasRef.current);
    }
  }, [canvasRef.current, drawWheelInternal]);

  // 执行抽奖
  const handleSpin = async () => {
    if (spinning || !canSpin) return;
    
    // 检查剩余次数
    if (remainingDraws <= 0) {
      message.warning('您今日的抽奖次数已用完');
      return;
    }
    
    // 对外部用户进行额外的次数检查
    const projectId = localStorage.getItem('project_id');
    const isPublicAccess = !projectId;
    
    if (isPublicAccess) {
      const userPhone = localStorage.getItem('user_phone');
      if (userPhone && game?.max_draws_per_user > 0) {
        try {
          // 实时查询该手机号的抽奖记录
          const queryResponse = await marketingGameApi.queryPrizesByPhone(gameId, userPhone);
          if (queryResponse.success && queryResponse.data) {
            const usedDraws = queryResponse.data.total_draws || 0;
            const currentRemainingDraws = Math.max(0, game.max_draws_per_user - usedDraws);
            
            if (currentRemainingDraws <= 0) {
              message.warning(`您已达到最大抽奖次数(${game.max_draws_per_user}次)`);
              setRemainingDraws(0);
              return;
            }
            
            // 更新显示的剩余次数
            setRemainingDraws(currentRemainingDraws);
          }
        } catch (error) {
          console.error("Enhanced 检查抽奖次数失败:", error);
        }
      }
    }
    
    // 检查是否需要验证
    if (
      (game?.settings?.enable_receipt_verification === true && !receiptVerified) ||
      (game?.settings?.enable_phone_verification === true && !phoneVerified)
    ) {
      setShowVerification(true);
      return;
    }
    
    playSound('click');
    
    try {
      setSpinning(true);
      setCanSpin(false);
      
      // 播放转动音效
      playSound('spin');
      
      // 先调用抽奖API获取结果
      const projectId = localStorage.getItem('project_id');
      const isPublicAccess = !projectId;

      let response;

      if (isPublicAccess) {
        // 外部分享页面，使用公开抽奖API
        const participantData = {
          phone: phone || localStorage.getItem('user_phone') || '',
          name: localStorage.getItem('user_name') || '匿名用户',
          receipt_code: receiptCode || localStorage.getItem('receipt_code') || '',
          receipt_amount: parseFloat(receiptAmount || localStorage.getItem('receipt_amount') || '0')
        };
        
        console.log("使用公开抽奖API，参与者数据:", participantData);
        response = await marketingGameApi.publicDraw(gameId, participantData);
      } else {
        // 内部用户，使用普通抽奖API
        console.log("使用内部抽奖API");
        response = await marketingGameApi.drawPrize(gameId);
      }
      
      if (response.success) {
        const result = response.data;
        
        // 添加详细的调试日志
        console.log("Enhanced 完整抽奖响应:", response);
        console.log("Enhanced 抽奖结果数据:", result);
        console.log("Enhanced 中奖状态:", result.is_win);
        console.log("Enhanced 奖品数据:", result.prize);
        console.log("Enhanced 奖品是否为默认:", result.prize?.is_default);
        
        // 根据抽奖结果执行精确的转盘动画
        const targetAngle = result.pointer_angle || null;
        console.log("目标角度:", targetAngle, "抽奖方式:", result.draw_method);
        
        // 执行转盘动画，指向正确的奖品
        await spinWheel(targetAngle);
        
        if (result.prize && !result.prize.is_default) {
          setWinningPrize(result.prize);
          setWinRecordId(result.record_id || result.id);
          setWinCount(prev => prev + 1);
          setWinStreak(prev => prev + 1);
          setLastWinTime(new Date());
          playSound('win');
          
          // 触发中奖动画效果
          triggerWinAnimation(result.prize);
          
          // 显示中奖通知
          notification.success({
            message: result.draw_method === 'guaranteed' ? '🎉 保底中奖！' : '🎉 恭喜中奖！',
            description: `您获得了：${result.prize.name}`,
            duration: 5,
            placement: 'topRight'
          });
          
          // 延迟显示弹窗，确保状态已更新
          setTimeout(() => {
            setShowResult(true);
          }, 100);
        } else {
          // 未中奖，清空中奖状态
          setWinningPrize(null);
          setWinRecordId(null);
          setWinStreak(0);
          playSound('lose');
          
          notification.info({
            message: '很遗憾',
            description: '本次未中奖，再试一次吧！',
            duration: 3,
            placement: 'topRight'
          });
          
          // 未中奖也延迟显示弹窗
          setTimeout(() => {
            setShowResult(true);
          }, 100);
        }
        
        // 更新总抽奖次数
        setTotalSpins(prev => prev + 1);
        
        // 处理剩余抽奖次数
        let newRemainingDraws;
        if (response.remaining_draws !== undefined) {
          newRemainingDraws = parseInt(response.remaining_draws);
        } else if (response.data && response.data.remaining_draws !== undefined) {
          newRemainingDraws = parseInt(response.data.remaining_draws);
        } else {
          newRemainingDraws = Math.max(0, remainingDraws - 1);
        }
        
        if (isNaN(newRemainingDraws)) {
          newRemainingDraws = 0;
        }
        
        setRemainingDraws(newRemainingDraws);
        
        // 更新统计数据
        if (isPublicAccess) {
          // 外部用户，通过查询API更新统计
          const userPhone = phone || localStorage.getItem('user_phone');
          if (userPhone) {
            try {
              const queryResponse = await marketingGameApi.queryPrizesByPhone(gameId, userPhone);
              if (queryResponse.success && queryResponse.data) {
                setTotalSpins(queryResponse.data.total_draws || 0);
                setWinCount(queryResponse.data.total_wins || 0);
                // 计算中奖率
                const totalDraws = queryResponse.data.total_draws || 0;
                const totalWins = queryResponse.data.total_wins || 0;
                if (totalDraws > 0) {
                  // 这里可以添加中奖率的显示逻辑
                }
              }
            } catch (error) {
              console.error("Enhanced 更新外部用户统计失败:", error);
            }
          }
        } else {
          // 内部用户，使用原有的统计更新方式
          await fetchUserStats();
        }
        
      } else {
        message.error(response.message || '抽奖失败');
        playSound('lose');
      }
      
    } catch (error) {
      console.error('抽奖失败:', error);
      message.error('抽奖失败，请重试');
      playSound('lose');
    } finally {
      setSpinning(false);
      setTimeout(() => setCanSpin(true), 1000);
    }
  };

  // 转盘旋转动画
  const spinWheel = (targetAngle = null) => {
    return new Promise((resolve) => {
      const canvas = canvasRef.current;
      if (!canvas) {
        resolve();
        return;
      }
      
      let rotation = 0;
      const spinDuration = 3000; // 3秒
      const baseSpins = 5; // 基础旋转圈数
      
      // 如果有目标角度，计算最终停止角度
      let finalAngle = 360 * baseSpins; // 默认旋转5圈
      if (targetAngle !== null) {
        // 确保指针指向目标奖品
        // 由于指针在顶部（0度），我们需要让转盘旋转到目标角度的相反方向
        const targetRotation = 360 - targetAngle; // 转盘需要旋转的角度
        finalAngle = 360 * baseSpins + targetRotation;
      }
      
      const startTime = Date.now();
      
      const animate = () => {
        const elapsed = Date.now() - startTime;
        const progress = Math.min(elapsed / spinDuration, 1);
        
        // 使用缓动函数，让转盘逐渐减速
        const easeOut = 1 - Math.pow(1 - progress, 3);
        rotation = easeOut * finalAngle;
        
        // 应用旋转
        canvas.style.transform = `rotate(${rotation}deg)`;
        
        if (progress < 1) {
          requestAnimationFrame(animate);
        } else {
          resolve();
        }
      };
      
      animate();
    });
  };

  // 验证小票
  const handleVerifyReceipt = async () => {
    if (!receiptCode) {
      message.error('请输入小票号码');
      return;
    }

    if (game?.min_consumption > 0 && (!receiptAmount || parseFloat(receiptAmount) < game.min_consumption)) {
      message.error(`小票金额不能低于${game.min_consumption}元`);
      return;
    }

    try {
      setVerifying(true);

      const amountValue = receiptAmount ? parseFloat(receiptAmount) : 0;

      // 使用公开验证接口
      const response = await marketingGameApi.verifyReceiptPublic(gameId, {
        receipt_code: receiptCode,
        amount: amountValue,
        consumption_amount: amountValue
      });

      if (response.success) {
        localStorage.setItem('receipt_code', receiptCode);
        localStorage.setItem('receipt_amount', amountValue.toString());

        if (response.data) {
          if (response.data.phone) {
            localStorage.setItem('user_phone', response.data.phone);
            const userName = `用户(${response.data.phone.slice(-4)})`;
            localStorage.setItem('user_name', userName);
          }

          if (response.data.participant && response.data.participant.name) {
            localStorage.setItem('user_name', response.data.participant.name);
          }
        }

        message.success('小票验证成功');
        setReceiptVerified(true);

        if (game?.settings?.auto_draw === true) {
          setShowVerification(false);
          handleSpin();
        }
      } else {
        message.error(response.message || '小票验证失败');
      }
    } catch (error) {
      console.error('Error verifying receipt:', error);
      message.error(error.message || '小票验证失败，请稍后再试');
    } finally {
      setVerifying(false);
    }
  };

  // 验证手机号
  const handleVerifyPhone = async () => {
    if (!phone || !/^1[3-9]\d{9}$/.test(phone)) {
      message.error('请输入有效的手机号码');
      return;
    }

    if (game?.min_consumption > 0 && (!receiptAmount || parseFloat(receiptAmount) < game.min_consumption)) {
      message.error(`消费金额不能低于${game.min_consumption}元`);
      return;
    }

    try {
      setVerifyingPhone(true);

      const amountValue = receiptAmount ? parseFloat(receiptAmount) : 0;

      // 使用公开验证接口
      const response = await marketingGameApi.verifyPhonePublic(gameId, {
        phone: phone,
        amount: amountValue,
        consumption_amount: amountValue
      });

      if (response.success) {
        localStorage.setItem('user_phone', phone);
        const userName = `用户(${phone.slice(-4)})`;
        localStorage.setItem('user_name', userName);
        localStorage.setItem('receipt_amount', amountValue.toString());

        if (response.data && response.data.participant) {
          if (response.data.participant.name) {
            localStorage.setItem('user_name', response.data.participant.name);
          }
        }

        message.success('手机号验证成功');
        setPhoneVerified(true);

        if (game?.settings?.auto_draw === true) {
          setShowVerification(false);
          handleSpin();
        }
      } else {
        message.error(response.message || '手机号验证失败');
      }
    } catch (error) {
      console.error('Error verifying phone:', error);
      message.error(error.message || '手机号验证失败，请稍后再试');
    } finally {
      setVerifyingPhone(false);
    }
  };

  // 领取奖品
  const handleClaimPrize = async () => {
    if (!winRecordId || !winningPrize) {
      message.error('无效的中奖记录');
      return;
    }

    const userPhone = phone || localStorage.getItem('user_phone');
    if (!userPhone) {
      message.error('请先验证手机号');
      return;
    }

    try {
      setClaiming(true);

      const response = await marketingGameApi.claimPrize(gameId, winRecordId, {
        phone: userPhone
      });

      if (response.success) {
        message.success('奖品领取成功！请联系客服兑换奖品');
        setShowResult(false);
        // 清空中奖状态
        setWinningPrize(null);
        setWinRecordId(null);
      } else {
        message.error(response.message || '领取失败');
      }
    } catch (error) {
      console.error('领取奖品失败:', error);
      message.error('领取奖品失败，请稍后再试');
    } finally {
      setClaiming(false);
    }
  };

  // 从查询模态框领取奖品
  const handleClaimPrizeFromQuery = async (recordId, userPhone) => {
    if (!recordId || !userPhone) {
      message.error('无效的中奖记录或手机号');
      return;
    }

    try {
      setClaiming(true);

      const response = await marketingGameApi.claimPrize(gameId, recordId, {
        phone: userPhone
      });

      if (response.success) {
        message.success('奖品领取成功！请联系客服兑换奖品');
        
        // 更新查询结果中的领取状态
        if (queryResult && queryResult.win_records) {
          const updatedRecords = queryResult.win_records.map(record => {
            if (record.id === recordId) {
              return {
                ...record,
                claim_status: 'claimed',
                claim_time: new Date().toISOString()
              };
            }
            return record;
          });
          
          setQueryResult({
            ...queryResult,
            win_records: updatedRecords
          });
        }
        
        // 只有在领取的是当前显示的中奖记录时才清空状态
        if (winRecordId === recordId) {
          setWinningPrize(null);
          setWinRecordId(null);
        }
      } else {
        message.error(response.message || '领取失败');
      }
    } catch (error) {
      console.error('领取奖品失败:', error);
      message.error('领取奖品失败，请稍后再试');
    } finally {
      setClaiming(false);
    }
  };

  // 查询中奖记录
  const handleQueryPrizes = async () => {
    if (!queryPhone || !/^1[3-9]\d{9}$/.test(queryPhone)) {
      message.error('请输入有效的手机号码');
      return;
    }

    try {
      setQuerying(true);

      const response = await marketingGameApi.queryPrizesByPhone(gameId, queryPhone);

      if (response.success) {
        setQueryResult(response.data);
        if (response.data.total_wins === 0) {
          message.info('该手机号暂无中奖记录');
        } else {
          message.success(`查询成功，共${response.data.total_wins}个中奖记录`);
        }
      } else {
        message.error(response.message || '查询失败');
        setQueryResult(null);
      }
    } catch (error) {
      console.error('查询中奖记录失败:', error);
      message.error('查询失败，请稍后再试');
      setQueryResult(null);
    } finally {
      setQuerying(false);
    }
  };

  // 触发中奖动画
  const triggerWinAnimation = (prize) => {
    const config = PARTICLE_CONFIGS[particleEffect];
    createParticleEffect(config);
    
    // 屏幕震动效果
    if (navigator.vibrate) {
      navigator.vibrate([200, 100, 200]);
    }
  };

  // 创建粒子效果
  const createParticleEffect = (config) => {
    // 这里可以实现粒子效果，使用canvas或CSS动画
    const container = document.createElement('div');
    container.className = 'particle-container';
    document.body.appendChild(container);
    
    for (let i = 0; i < config.count; i++) {
      const particle = document.createElement('div');
      particle.className = `particle particle-${config.type}`;
      particle.style.left = Math.random() * window.innerWidth + 'px';
      particle.style.top = Math.random() * window.innerHeight + 'px';
      particle.style.animationDelay = Math.random() * 2 + 's';
      container.appendChild(particle);
    }
    
    // 3秒后清除粒子
    setTimeout(() => {
      document.body.removeChild(container);
    }, 3000);
  };

  // 渲染统计信息
  const renderStats = () => (
    <Row gutter={16}>
      <Col span={6}>
        <Card className="stat-card">
          <Statistic
            title="总抽奖次数"
            value={totalSpins}
            prefix={<PlayCircleOutlined />}
            valueStyle={{ color: '#1890ff' }}
          />
        </Card>
      </Col>
      <Col span={6}>
        <Card className="stat-card">
          <Statistic
            title="中奖次数"
            value={winCount}
            prefix={<TrophyOutlined />}
            valueStyle={{ color: '#52c41a' }}
          />
        </Card>
      </Col>
      <Col span={6}>
        <Card className="stat-card">
          <Statistic
            title="中奖率"
            value={totalSpins > 0 ? ((winCount / totalSpins) * 100).toFixed(1) : 0}
            suffix="%"
            prefix={<StarOutlined />}
            valueStyle={{ color: '#faad14' }}
          />
        </Card>
      </Col>
      <Col span={6}>
        <Card className="stat-card">
          <Statistic
            title="连胜纪录"
            value={winStreak}
            prefix={<FireOutlined />}
            valueStyle={{ color: '#f5222d' }}
          />
        </Card>
      </Col>
    </Row>
  );

  // 渲染设置面板
  const renderSettings = () => (
    <Space direction="vertical" style={{ width: '100%' }}>
      <Card title="主题设置">
        <Select
          value={currentTheme}
          onChange={setCurrentTheme}
          style={{ width: '100%' }}
        >
          {Object.entries(THEMES).map(([key, theme]) => (
            <Option key={key} value={key}>
              {theme.name}
            </Option>
          ))}
        </Select>
      </Card>
      
      <Card title="音效设置">
        <Space>
          <Text>音效开关：</Text>
          <Switch
            checked={soundEnabled}
            onChange={setSoundEnabled}
            checkedChildren={<SoundFilled />}
            unCheckedChildren={<SoundOutlined />}
          />
        </Space>
      </Card>
      
      <Card title="动画效果">
        <Space>
          <Text>粒子效果：</Text>
          <Select
            value={particleEffect}
            onChange={setParticleEffect}
            style={{ width: 120 }}
          >
            <Option value="sparkle">✨ 闪烁</Option>
            <Option value="confetti">🎊 彩纸</Option>
            <Option value="fireworks">🎆 烟花</Option>
            <Option value="hearts">💖 爱心</Option>
          </Select>
        </Space>
      </Card>
      
      {needVerification && (
        <Card title="身份验证">
          <Space direction="vertical" style={{ width: '100%' }}>
            <Alert
              message="需要验证"
              description="参与抽奖需要完成身份验证"
              type="warning"
              showIcon
            />
            <Button 
              type="primary" 
              loading={verifying}
              onClick={() => {/* 实现验证逻辑 */}}
            >
              开始验证
            </Button>
          </Space>
        </Card>
      )}
    </Space>
  );

  if (loading) {
    return (
      <div className="wheel-loading">
        <Spin size="large" />
        <Title level={4} style={{ marginTop: 16 }}>
          加载游戏中...
        </Title>
      </div>
    );
  }

  if (!game || !prizes.length) {
    return (
      <Empty
        description="游戏暂未配置或奖品为空"
        image={Empty.PRESENTED_IMAGE_SIMPLE}
      />
    );
  }

  return (
    <div className={`enhanced-wheel-container theme-${currentTheme}`}>
      {/* 游戏头部 */}
      <div className="game-header">
        <Card className="header-card">
          <Row justify="space-between" align="middle">
            <Col>
              <Title level={3} style={{ margin: 0 }}>
                <GiftOutlined style={{ marginRight: 8 }} />
                {game.name}
              </Title>
              <Text type="secondary">{game.description}</Text>
            </Col>
            <Col>
              <Space>
                <Badge count={remainingDraws} showZero>
                  <Button
                    type="primary"
                    size="large"
                    onClick={handleSpin}
                    loading={spinning}
                    disabled={!canSpin || remainingDraws <= 0}
                    className="spin-button"
                  >
                    {spinning ? '转动中...' : '开始抽奖'}
                  </Button>
                </Badge>
                <Tooltip title="游戏统计">
                  <Button
                    icon={<HistoryOutlined />}
                    onClick={() => setShowStats(!showStats)}
                  />
                </Tooltip>
                <Tooltip title="游戏设置">
                  <Button
                    icon={<SettingOutlined />}
                    onClick={() => setShowSettings(!showSettings)}
                  />
                </Tooltip>
                <Tooltip title="查询中奖记录">
                  <Button
                    icon={<TrophyOutlined />}
                    onClick={() => setShowQueryModal(true)}
                  />
                </Tooltip>
              </Space>
            </Col>
          </Row>
        </Card>
      </div>

      {/* 统计信息 */}
      {showStats && (
        <div className="stats-section">
          {renderStats()}
        </div>
      )}

      {/* 主游戏区域 */}
      <div className="main-game-area">
        <Row gutter={24}>
          {/* 转盘区域 */}
          <Col span={showSettings ? 16 : 24}>
            <Card className="wheel-card">
              <div className="wheel-container">
                <canvas
                  ref={canvasRef}
                  width={400}
                  height={400}
                  className="wheel-canvas"
                />
                
                {/* 固定指针 */}
                <div className="wheel-pointer">
                  <div className="pointer-triangle"></div>
                </div>
                
                {/* 转盘装饰 */}
                <div className="wheel-decorations">
                  <div className="glow-ring"></div>
                  <div className="spark spark-1"></div>
                  <div className="spark spark-2"></div>
                  <div className="spark spark-3"></div>
                  <div className="spark spark-4"></div>
                </div>
                
                {/* 进度条 */}
                <div className="progress-container">
                  <Progress
                    type="circle"
                    percent={((game.max_draws_per_user - remainingDraws) / game.max_draws_per_user) * 100}
                    width={60}
                    strokeColor={{
                      '0%': '#108ee9',
                      '100%': '#87d068',
                    }}
                    format={percent => `${remainingDraws}次`}
                  />
                </div>
              </div>
              
              {/* 奖品展示 */}
              <div className="prizes-display">
                <Title level={5}>
                  <TrophyOutlined /> 丰厚奖品等你来拿
                </Title>
                <Row gutter={8}>
                  {prizes.slice(0, 6).map((prize, index) => (
                    <Col span={4} key={prize.id}>
                      <Card size="small" className="prize-card">
                        <Avatar
                          src={prize.image_url}
                          icon={<GiftOutlined />}
                          size={32}
                        />
                        <Text className="prize-name">{prize.name}</Text>
                      </Card>
                    </Col>
                  ))}
                </Row>
              </div>

              {/* 再来一次按钮 */}
              <div style={{ textAlign: 'center', marginTop: '20px' }}>
                <Button
                  type="default"
                  icon={<ReloadOutlined />}
                  onClick={() => {
                    // 清空验证状态
                    localStorage.removeItem('user_phone');
                    localStorage.removeItem('receipt_code');
                    localStorage.removeItem('receipt_amount');
                    setPhoneVerified(false);
                    setReceiptVerified(false);
                    setPhone('');
                    setReceiptCode('');
                    setReceiptAmount('');
                    // 刷新游戏数据
                    fetchGameData();
                    message.success('已重置，可以重新参与游戏');
                  }}
                  style={{ marginRight: '10px' }}
                >
                  再来一次
                </Button>
              </div>
            </Card>
          </Col>

          {/* 设置面板 */}
          {showSettings && (
            <Col span={8}>
              <Card title="游戏设置" className="settings-card">
                {renderSettings()}
              </Card>
            </Col>
          )}
        </Row>
      </div>

      {/* 中奖结果模态框 */}
      <Modal
        open={showResult}
        onCancel={() => setShowResult(false)}
        footer={null}
        centered
        className="result-modal"
        width={480}
      >
        <div className="result-content">
          {winningPrize ? (
            <div className="win-result">
              <div className="celebration-icon">
                <CrownOutlined style={{ fontSize: 64, color: '#faad14' }} />
              </div>
              <Title level={2} style={{ color: '#52c41a', margin: '16px 0' }}>
                🎉 恭喜中奖！
              </Title>
              <Card className="prize-info-card">
                <Avatar
                  src={winningPrize.image_url}
                  icon={<GiftOutlined />}
                  size={80}
                />
                <Title level={4} style={{ margin: '16px 0 8px' }}>
                  {winningPrize.name}
                </Title>
                <Text type="secondary">{winningPrize.description}</Text>
                {winningPrize.value > 0 && (
                  <Tag color="gold" style={{ marginTop: 8 }}>
                    价值 ¥{winningPrize.value}
                  </Tag>
                )}
              </Card>
              <Paragraph style={{ textAlign: 'center', marginTop: 16 }}>
                请及时联系客服领取您的奖品！
              </Paragraph>
              <div style={{ textAlign: 'center', marginTop: 16 }}>
                <Button
                  type="primary"
                  size="large"
                  loading={claiming}
                  onClick={handleClaimPrize}
                  style={{ marginRight: 16 }}
                >
                  立即领奖
                </Button>
                <Button onClick={() => setShowResult(false)}>
                  稍后领取
                </Button>
              </div>
            </div>
          ) : (
            <div className="lose-result">
              <div className="comfort-icon">
                <SmileOutlined style={{ fontSize: 64, color: '#1890ff' }} />
              </div>
              <Title level={3} style={{ margin: '16px 0' }}>
                再接再厉！
              </Title>
              <Paragraph style={{ textAlign: 'center' }}>
                很遗憾本次未中奖，不要气馁，下次一定会有好运气的！
              </Paragraph>
              <Alert
                message="小贴士"
                description="多参与活动可以提高中奖概率哦～"
                type="info"
                showIcon
                style={{ marginTop: 16 }}
              />
            </div>
          )}
          
          <div className="result-actions">
            <Space size="large">
              <Button onClick={() => setShowResult(false)}>
                关闭
              </Button>
              {remainingDraws > 0 && (
                <Button
                  type="primary"
                  icon={<ReloadOutlined />}
                  onClick={() => {
                    setShowResult(false);
                    setTimeout(handleSpin, 500);
                  }}
                >
                  再来一次
                </Button>
              )}
            </Space>
          </div>
        </div>
      </Modal>

      {/* 分享模态框 */}
      <Modal
        title="分享游戏"
        open={false}
        onCancel={() => {}}
        footer={null}
      >
        <Space direction="vertical" style={{ width: '100%' }}>
          <Card>
            <Space>
              <QrcodeOutlined style={{ fontSize: 24 }} />
              <div>
                <Text strong>扫码参与</Text>
                <br />
                <Text type="secondary">分享给朋友一起来抽奖</Text>
              </div>
            </Space>
          </Card>
          <Button type="primary" block icon={<ShareAltOutlined />}>
            生成分享链接
          </Button>
        </Space>
      </Modal>

      {/* 验证弹窗 */}
      <Modal
        title={
          <div>
            验证信息
            <div style={{ fontSize: '12px', color: '#999', fontWeight: 'normal', marginTop: '5px' }}>
              提示: 输入完成后可按空格键快速确认
            </div>
          </div>
        }
        open={showVerification}
        onCancel={() => setShowVerification(false)}
        footer={null}
        width={400}
      >
        <div>
          {game?.settings?.enable_phone_verification === true && !phoneVerified && (
            <>
              <Form layout="vertical">
                <Form.Item
                  label="手机号码"
                  required
                  rules={[{ required: true, message: '请输入手机号码' }]}
                >
                  <Input
                    placeholder="请输入手机号码"
                    value={phone}
                    onChange={e => setPhone(e.target.value)}
                    onKeyDown={e => {
                      if (e.key === ' ' || e.keyCode === 32) {
                        e.preventDefault();
                        if (phone && game?.min_consumption > 0 ? receiptAmount >= game.min_consumption : true) {
                          handleVerifyPhone();
                        } else if (!phone) {
                          message.warning('请输入手机号码');
                        } else if (game?.min_consumption > 0 && receiptAmount < game.min_consumption) {
                          message.warning(`消费金额需满${game.min_consumption}元`);
                        }
                      }
                    }}
                  />
                </Form.Item>

                {game?.min_consumption > 0 && (
                  <Form.Item
                    label={`消费金额（最低${game.min_consumption}元）`}
                    required
                    rules={[{ required: true, message: '请输入消费金额' }]}
                  >
                    <Input
                      placeholder="请输入消费金额"
                      value={receiptAmount}
                      onChange={e => setReceiptAmount(e.target.value)}
                      type="number"
                      min={game.min_consumption}
                      addonAfter="元"
                      onKeyDown={e => {
                        if (e.key === ' ' || e.keyCode === 32) {
                          e.preventDefault();
                          if (phone && receiptAmount >= game.min_consumption) {
                            handleVerifyPhone();
                          } else if (!phone) {
                            message.warning('请输入手机号码');
                          } else if (receiptAmount < game.min_consumption) {
                            message.warning(`消费金额需满${game.min_consumption}元`);
                          }
                        }
                      }}
                    />
                  </Form.Item>
                )}
              </Form>

              <div style={{ textAlign: 'right', marginBottom: 16 }}>
                <Button
                  type="primary"
                  onClick={handleVerifyPhone}
                  loading={verifyingPhone}
                >
                  验证手机号
                </Button>
              </div>

              <Divider />
            </>
          )}

          {game?.settings?.enable_receipt_verification === true && !receiptVerified && (
            <Form layout="vertical">
              <Form.Item
                label="小票号码"
                required
                rules={[{ required: true, message: '请输入小票号码' }]}
              >
                <Input
                  placeholder="请输入小票号码"
                  value={receiptCode}
                  onChange={e => setReceiptCode(e.target.value)}
                  onKeyDown={e => {
                    if (e.key === ' ' || e.keyCode === 32) {
                      e.preventDefault();
                      if (receiptCode && game?.min_consumption > 0 ? receiptAmount >= game.min_consumption : true) {
                        handleVerifyReceipt();
                      } else if (!receiptCode) {
                        message.warning('请输入小票号码');
                      } else if (game?.min_consumption > 0 && receiptAmount < game.min_consumption) {
                        message.warning(`消费金额需满${game.min_consumption}元`);
                      }
                    }
                  }}
                />
              </Form.Item>

              {game?.min_consumption > 0 && (
                <Form.Item
                  label={`消费金额（最低${game.min_consumption}元）`}
                  required
                  rules={[{ required: true, message: '请输入消费金额' }]}
                >
                  <Input
                    placeholder="请输入消费金额"
                    value={receiptAmount}
                    onChange={e => setReceiptAmount(e.target.value)}
                    type="number"
                    min={game.min_consumption}
                    addonAfter="元"
                    onKeyDown={e => {
                      if (e.key === ' ' || e.keyCode === 32) {
                        e.preventDefault();
                        if (receiptCode && receiptAmount >= game.min_consumption) {
                          handleVerifyReceipt();
                        } else if (!receiptCode) {
                          message.warning('请输入小票号码');
                        } else if (receiptAmount < game.min_consumption) {
                          message.warning(`消费金额需满${game.min_consumption}元`);
                        }
                      }
                    }}
                  />
                </Form.Item>
              )}

              <div style={{ textAlign: 'right' }}>
                <Button
                  type="primary"
                  onClick={handleVerifyReceipt}
                  loading={verifying}
                >
                  验证小票
                </Button>
              </div>
            </Form>
          )}

          {((game?.settings?.enable_phone_verification === true && phoneVerified) ||
            game?.settings?.enable_phone_verification !== true) &&
           ((game?.settings?.enable_receipt_verification === true && receiptVerified) ||
            game?.settings?.enable_receipt_verification !== true) && (
            <Result
              status="success"
              title="验证成功"
              subTitle="您已完成所有验证，可以开始抽奖了"
              extra={[
                <Button
                  key="draw"
                  type="primary"
                  onClick={() => {
                    setShowVerification(false);
                    handleSpin();
                  }}
                >
                  开始抽奖
                </Button>
              ]}
            />
          )}
        </div>
      </Modal>
      
      {/* 查询中奖记录模态框 */}
      <Modal
        title="查询中奖记录"
        open={showQueryModal}
        onCancel={() => {
          setShowQueryModal(false);
          setQueryPhone('');
          setQueryResult(null);
        }}
        footer={null}
        width={600}
      >
        <div>
          <Form layout="vertical">
            <Form.Item
              label="手机号码"
              required
            >
              <Input
                placeholder="请输入手机号码"
                value={queryPhone}
                onChange={e => setQueryPhone(e.target.value)}
                onPressEnter={handleQueryPrizes}
              />
            </Form.Item>
          </Form>

          <div style={{ textAlign: 'right', marginBottom: 16 }}>
            <Button
              type="primary"
              onClick={handleQueryPrizes}
              loading={querying}
            >
              查询中奖记录
            </Button>
          </div>

          {queryResult && (
            <div>
              <Divider />
              <div style={{ marginBottom: 16 }}>
                <Text strong>参与者：</Text>{queryResult.participant_name} ({queryResult.participant_phone})
                <br />
                <Text strong>总抽奖次数：</Text>{queryResult.total_draws}
                <br />
                <Text strong>中奖次数：</Text>{queryResult.total_wins}
              </div>

              {queryResult.win_records && queryResult.win_records.length > 0 ? (
                <div>
                  <Title level={5}>中奖记录</Title>
                  {queryResult.win_records.map((record, index) => (
                    <Card key={record.id} size="small" style={{ marginBottom: 8 }}>
                      <Row align="middle">
                        <Col span={4}>
                          <Avatar
                            src={record.prize_image}
                            icon={<GiftOutlined />}
                            size={40}
                          />
                        </Col>
                        <Col span={10}>
                          <div>
                            <Text strong>{record.prize_name}</Text>
                            <br />
                            <Text type="secondary" style={{ fontSize: 12 }}>
                              {record.prize_description}
                            </Text>
                            {record.prize_value > 0 && (
                              <Tag color="gold" size="small" style={{ marginLeft: 8 }}>
                                ¥{record.prize_value}
                              </Tag>
                            )}
                          </div>
                        </Col>
                        <Col span={10} style={{ textAlign: 'right' }}>
                          <div>
                            <div style={{ marginBottom: 8 }}>
                              {record.claim_status === 'claimed' ? (
                                <Tag color="green">已领取</Tag>
                              ) : (
                                <div>
                                  <Tag color="orange">未领取</Tag>
                                  <Button
                                    type="primary"
                                    size="small"
                                    style={{ marginLeft: 8 }}
                                    loading={claiming}
                                    onClick={() => {
                                      // 直接调用领取函数，不要设置winningPrize状态
                                      // 避免影响正在显示的中奖弹窗
                                      handleClaimPrizeFromQuery(record.id, queryPhone);
                                    }}
                                  >
                                    立即领取
                                  </Button>
                                </div>
                              )}
                            </div>
                            <Text type="secondary" style={{ fontSize: 12 }}>
                              {new Date(record.draw_time).toLocaleString()}
                            </Text>
                            {record.claim_time && (
                              <>
                                <br />
                                <Text type="secondary" style={{ fontSize: 12 }}>
                                  领取时间: {new Date(record.claim_time).toLocaleString()}
                                </Text>
                              </>
                            )}
                          </div>
                        </Col>
                      </Row>
                    </Card>
                  ))}
                </div>
              ) : (
                <Empty description="暂无中奖记录" />
              )}
            </div>
          )}
        </div>
      </Modal>
    </div>
  );
};

export default EnhancedWheelOfFortune; 