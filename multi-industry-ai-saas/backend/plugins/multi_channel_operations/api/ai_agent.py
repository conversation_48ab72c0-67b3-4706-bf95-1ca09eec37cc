#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
全渠道运营助手 - AI智能体API
"""

import logging
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Body
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_
from pydantic import BaseModel, Field

from db.database import get_db
from auth.dependencies import get_current_user, get_current_project
from models.user import User
from models.project import Project
from ..models.operation_log import AIAgentTask
from ..services.ai_agent import AIAgentService

logger = logging.getLogger(__name__)
router = APIRouter(tags=["全渠道运营-AI智能体"])

# Pydantic模型
class ChatRequest(BaseModel):
    message: str = Field(..., description="用户消息")
    conversation_id: Optional[str] = Field(None, description="对话ID")

class ChatResponse(BaseModel):
    success: bool
    task_id: str
    response: str
    actions: List[str]
    data: Dict[str, Any]

class TaskResponse(BaseModel):
    id: str
    task_type: str
    task_name: str
    task_description: str
    user_input: str
    conversation_id: Optional[str]
    ai_response: Optional[str]
    ai_actions: List[str]
    status: str
    progress: int
    result_data: Dict[str, Any]
    user_feedback: Optional[str]
    feedback_note: Optional[str]
    started_at: Optional[str]
    completed_at: Optional[str]
    created_at: str
    updated_at: str

# 初始化服务
ai_agent_service = AIAgentService()


@router.post("/chat", response_model=ChatResponse)
async def chat_with_ai_agent(
    request: ChatRequest,
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """与AI智能体对话"""
    try:
        result = await ai_agent_service.process_user_request(
            db=db,
            project_id=str(project.id),
            user_input=request.message,
            conversation_id=request.conversation_id,
            user_id=str(current_user.id)
        )

        return ChatResponse(
            success=result["success"],
            task_id=result.get("task_id", ""),
            response=result.get("response", ""),
            actions=result.get("actions", []),
            data=result.get("data", {})
        )

    except Exception as e:
        logger.error(f"AI智能体对话失败: {e}")
        raise HTTPException(status_code=500, detail=f"AI智能体对话失败: {str(e)}")


@router.get("/tasks", response_model=List[TaskResponse])
async def get_ai_tasks(
    task_type: Optional[str] = None,
    status: Optional[str] = None,
    skip: int = 0,
    limit: int = 20,
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """获取AI任务列表"""
    try:
        # 构建查询条件
        conditions = [AIAgentTask.project_id == project.id]

        if task_type:
            conditions.append(AIAgentTask.task_type == task_type)
        if status:
            conditions.append(AIAgentTask.status == status)

        # 执行查询
        stmt = select(AIAgentTask).where(
            and_(*conditions)
        ).offset(skip).limit(limit).order_by(AIAgentTask.created_at.desc())

        result = await db.execute(stmt)
        tasks = result.scalars().all()

        # 转换为响应格式
        return [
            TaskResponse(
                id=str(task.id),
                task_type=task.task_type,
                task_name=task.task_name,
                task_description=task.task_description or "",
                user_input=task.user_input,
                conversation_id=task.conversation_id,
                ai_response=task.ai_response,
                ai_actions=task.ai_actions or [],
                status=task.status,
                progress=task.progress,
                result_data=task.result_data or {},
                user_feedback=task.user_feedback,
                feedback_note=task.feedback_note,
                started_at=task.started_at.isoformat() if task.started_at else None,
                completed_at=task.completed_at.isoformat() if task.completed_at else None,
                created_at=task.created_at.isoformat(),
                updated_at=task.updated_at.isoformat()
            )
            for task in tasks
        ]

    except Exception as e:
        logger.error(f"获取AI任务列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取AI任务列表失败: {str(e)}")


@router.get("/tasks/{task_id}")
async def get_ai_task_detail(
    task_id: str,
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """获取AI任务详情"""
    try:
        stmt = select(AIAgentTask).where(
            and_(
                AIAgentTask.id == task_id,
                AIAgentTask.project_id == project.id
            )
        )
        result = await db.execute(stmt)
        task = result.scalar_one_or_none()

        if not task:
            raise HTTPException(status_code=404, detail="AI任务不存在")

        return {
            "id": str(task.id),
            "task_type": task.task_type,
            "task_name": task.task_name,
            "task_description": task.task_description,
            "user_input": task.user_input,
            "conversation_id": task.conversation_id,
            "ai_model": task.ai_model,
            "ai_response": task.ai_response,
            "ai_actions": task.ai_actions or [],
            "status": task.status,
            "progress": task.progress,
            "result_data": task.result_data or {},
            "affected_items": task.affected_items,
            "user_feedback": task.user_feedback,
            "feedback_note": task.feedback_note,
            "started_at": task.started_at.isoformat() if task.started_at else None,
            "completed_at": task.completed_at.isoformat() if task.completed_at else None,
            "created_at": task.created_at.isoformat(),
            "updated_at": task.updated_at.isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取AI任务详情失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取AI任务详情失败: {str(e)}")


@router.post("/tasks/{task_id}/feedback")
async def submit_task_feedback(
    task_id: str,
    feedback: str = Body(..., description="用户反馈"),
    feedback_note: Optional[str] = Body(None, description="反馈备注"),
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """提交任务反馈"""
    try:
        stmt = select(AIAgentTask).where(
            and_(
                AIAgentTask.id == task_id,
                AIAgentTask.project_id == project.id
            )
        )
        result = await db.execute(stmt)
        task = result.scalar_one_or_none()

        if not task:
            raise HTTPException(status_code=404, detail="AI任务不存在")

        # 更新反馈
        task.user_feedback = feedback
        task.feedback_note = feedback_note

        await db.commit()

        return {
            "success": True,
            "message": "反馈提交成功"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"提交任务反馈失败: {e}")
        raise HTTPException(status_code=500, detail=f"提交任务反馈失败: {str(e)}")


@router.get("/capabilities")
async def get_ai_capabilities():
    """获取AI智能体能力列表"""
    try:
        capabilities = {
            "supported_tasks": [
                {
                    "type": "product_sync",
                    "name": "商品同步",
                    "description": "同步商品到各个渠道平台",
                    "examples": [
                        "同步商品A到美团",
                        "将所有商品同步到抖音",
                        "批量同步商品到多个渠道"
                    ]
                },
                {
                    "type": "price_update",
                    "name": "价格更新",
                    "description": "批量更新商品价格",
                    "examples": [
                        "将商品A的价格改为99元",
                        "所有商品价格上调10%",
                        "根据竞品价格调整定价"
                    ]
                },
                {
                    "type": "image_generation",
                    "name": "AI图片生成",
                    "description": "为商品生成专业图片",
                    "examples": [
                        "为商品A生成一张图片",
                        "生成美团风格的商品图",
                        "批量生成商品主图"
                    ]
                },
                {
                    "type": "channel_analysis",
                    "name": "渠道分析",
                    "description": "分析渠道销售数据",
                    "examples": [
                        "分析本月销售数据",
                        "对比各渠道表现",
                        "生成运营报告"
                    ]
                },
                {
                    "type": "automation_setup",
                    "name": "自动化设置",
                    "description": "设置自动化运营规则",
                    "examples": [
                        "设置价格监控规则",
                        "配置库存自动同步",
                        "创建促销自动化"
                    ]
                }
            ],
            "supported_platforms": [
                {"code": "meituan", "name": "美团", "features": ["商品管理", "订单管理", "价格同步"]},
                {"code": "douyin", "name": "抖音", "features": ["商品管理", "订单管理", "价格同步"]},
                {"code": "eleme", "name": "饿了么", "features": ["商品管理", "订单管理", "价格同步"]}
            ],
            "ai_models": [
                {"name": "DALL-E 3", "type": "image_generation", "provider": "openai"},
                {"name": "GPT-4", "type": "text_generation", "provider": "openai"},
                {"name": "Claude", "type": "text_generation", "provider": "anthropic"}
            ]
        }

        return {
            "success": True,
            "data": capabilities
        }

    except Exception as e:
        logger.error(f"获取AI能力列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取AI能力列表失败: {str(e)}")


@router.get("/conversation-history/{conversation_id}")
async def get_conversation_history(
    conversation_id: str,
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """获取对话历史"""
    try:
        stmt = select(AIAgentTask).where(
            and_(
                AIAgentTask.conversation_id == conversation_id,
                AIAgentTask.project_id == project.id
            )
        ).order_by(AIAgentTask.created_at.asc())

        result = await db.execute(stmt)
        tasks = result.scalars().all()

        conversation = []
        for task in tasks:
            conversation.append({
                "role": "user",
                "content": task.user_input,
                "timestamp": task.created_at.isoformat()
            })

            if task.ai_response:
                conversation.append({
                    "role": "assistant",
                    "content": task.ai_response,
                    "timestamp": task.completed_at.isoformat() if task.completed_at else task.created_at.isoformat(),
                    "actions": task.ai_actions or [],
                    "task_id": str(task.id)
                })

        return {
            "success": True,
            "conversation_id": conversation_id,
            "messages": conversation
        }

    except Exception as e:
        logger.error(f"获取对话历史失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取对话历史失败: {str(e)}")