from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query, Path, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import func, and_, or_
import uuid
from datetime import datetime
import logging

from db.database import get_db
from models.notification_settings import NotificationSettings
from models.user import User
from models.project import Project
from schemas.notification_settings import (
    NotificationSettingsCreate, NotificationSettingsUpdate, NotificationSettingsResponse,
    WorkflowNotificationSettings, WorkflowNotificationSettingsUpdate
)
from api.deps import get_current_user, get_current_project

# 配置日志
logger = logging.getLogger(__name__)

router = APIRouter()

# 通知设置API
@router.get("", response_model=NotificationSettingsResponse)
async def get_notification_settings(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project)
):
    """
    获取通知设置
    """
    try:
        logger.info(f"获取通知设置: user_id={current_user.id}, project_id={current_project.id}")

        # 查询通知设置
        query = select(NotificationSettings).where(
            NotificationSettings.project_id == current_project.id,
            NotificationSettings.user_id == current_user.id
        )
        result = await db.execute(query)
        settings = result.scalar_one_or_none()

        if not settings:
            # 如果不存在，创建默认设置
            logger.info(f"为用户创建默认通知设置: user_id={current_user.id}, project_id={current_project.id}")
            settings = NotificationSettings(
                id=uuid.uuid4(),
                tenant_id=current_project.tenant_id,
                project_id=current_project.id,
                user_id=current_user.id,
                email_notifications=True,
                push_notifications=True,  # 确保使用正确的字段名
                sms_notifications=False,
                sound_enabled=True,
                normal_frequency="realtime",  # 默认为实时
                high_priority_frequency="realtime",  # 默认为实时
                system_notifications=True,
                task_notifications=True,
                inventory_notifications=True,
                order_notifications=True,
                security_notifications=True,
                workflow_notifications={}
            )
            db.add(settings)
            await db.commit()
            await db.refresh(settings)

        return {
            "success": True,
            "data": settings
        }
    except Exception as e:
        logger.error(f"获取通知设置失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取通知设置失败: {str(e)}"
        )


@router.put("", response_model=NotificationSettingsResponse)
async def update_notification_settings(
    settings_update: NotificationSettingsUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project)
):
    """
    更新通知设置
    """
    try:
        logger.info(f"更新通知设置: user_id={current_user.id}, project_id={current_project.id}")

        # 查询通知设置
        query = select(NotificationSettings).where(
            NotificationSettings.project_id == current_project.id,
            NotificationSettings.user_id == current_user.id
        )
        result = await db.execute(query)
        settings = result.scalar_one_or_none()

        if not settings:
            # 如果不存在，创建默认设置
            logger.info(f"为用户创建默认通知设置: user_id={current_user.id}, project_id={current_project.id}")
            settings = NotificationSettings(
                id=uuid.uuid4(),
                tenant_id=current_project.tenant_id,
                project_id=current_project.id,
                user_id=current_user.id,
                email_notifications=True,
                push_notifications=True,
                sms_notifications=False,
                sound_enabled=True,
                normal_frequency="realtime",
                high_priority_frequency="realtime",
                system_notifications=True,
                task_notifications=True,
                inventory_notifications=True,
                order_notifications=True,
                security_notifications=True,
                workflow_notifications={}
            )
            db.add(settings)
            await db.flush()

        # 更新字段
        update_data = settings_update.dict(exclude_unset=True)
        logger.info(f"更新通知设置字段: {update_data}")

        for key, value in update_data.items():
            setattr(settings, key, value)

        settings.updated_at = datetime.now()

        await db.commit()
        await db.refresh(settings)

        # 通过WebSocket推送设置变更
        try:
            from services.redis_manager import RedisManager

            # 准备通知设置数据
            settings_data = {
                "normal_frequency": settings.normal_frequency,
                "high_priority_frequency": settings.high_priority_frequency,
                "email_notifications": settings.email_notifications,
                "push_notifications": settings.push_notifications,
                "sms_notifications": settings.sms_notifications,
                "sound_enabled": settings.sound_enabled,
                "system_notifications": settings.system_notifications,
                "task_notifications": settings.task_notifications,
                "inventory_notifications": settings.inventory_notifications,
                "order_notifications": settings.order_notifications,
                "security_notifications": settings.security_notifications
            }

            # 创建WebSocket消息
            message = {
                "type": "settings_updated",
                "settings": settings_data,
                "timestamp": datetime.now().isoformat()
            }

            # 获取Redis管理器实例
            redis_manager = await RedisManager.get_instance()

            # 发布设置变更消息到用户通道
            await redis_manager.publish_notification(
                f"user:{current_user.id}:notifications",
                message
            )

            logger.info(f"通知设置变更已通过WebSocket推送: user_id={current_user.id}")
        except Exception as e:
            logger.error(f"推送通知设置变更失败: {str(e)}")
            # 不抛出异常，因为这不应该影响主流程

        return {
            "success": True,
            "message": "通知设置更新成功",
            "data": settings
        }
    except Exception as e:
        logger.error(f"更新通知设置失败: {str(e)}", exc_info=True)
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新通知设置失败: {str(e)}"
        )


@router.get("/workflow/{workflow_type}", response_model=Dict[str, Any])
async def get_workflow_notification_settings(
    workflow_type: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project)
):
    """
    获取工作流通知设置
    """
    try:
        logger.info(f"获取工作流通知设置: user_id={current_user.id}, project_id={current_project.id}, workflow_type={workflow_type}")

        # 查询通知设置
        query = select(NotificationSettings).where(
            NotificationSettings.project_id == current_project.id,
            NotificationSettings.user_id == current_user.id
        )
        result = await db.execute(query)
        settings = result.scalar_one_or_none()

        if not settings:
            # 如果不存在，创建默认设置
            logger.info(f"为用户创建默认通知设置: user_id={current_user.id}, project_id={current_project.id}")
            settings = NotificationSettings(
                id=uuid.uuid4(),
                tenant_id=current_project.tenant_id,
                project_id=current_project.id,
                user_id=current_user.id,
                email_notifications=True,
                push_notifications=True,
                sms_notifications=False,
                sound_enabled=True,
                normal_frequency="realtime",
                high_priority_frequency="realtime",
                system_notifications=True,
                task_notifications=True,
                inventory_notifications=True,
                order_notifications=True,
                security_notifications=True,
                workflow_notifications={}
            )
            db.add(settings)
            await db.commit()
            await db.refresh(settings)

        # 获取工作流通知设置
        workflow_settings = settings.workflow_notifications.get(workflow_type, {})

        # 如果没有设置，返回默认设置
        if not workflow_settings:
            logger.info(f"为工作流创建默认通知设置: workflow_type={workflow_type}")
            if workflow_type == "loss_report":
                workflow_settings = {
                    "enabled": True,
                    "notify_type": "roles",
                    "notify_roles": ["purchase", "admin"],
                    "notify_users": [],
                    "title": "门店报损审核通知",
                    "template": "门店 {{store_name}} 提交了产品 {{product_name}} 的报损申请，数量: {{quantity}}，金额: {{amount}}元，请及时审核"
                }
            else:
                workflow_settings = {
                    "enabled": True,
                    "notify_type": "roles",
                    "notify_roles": ["admin"],
                    "notify_users": [],
                    "title": f"{workflow_type}通知",
                    "template": f"有新的{workflow_type}需要处理"
                }

        return {
            "success": True,
            "data": workflow_settings
        }
    except Exception as e:
        logger.error(f"获取工作流通知设置失败: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取工作流通知设置失败: {str(e)}"
        )


@router.put("/workflow/{workflow_type}", response_model=Dict[str, Any])
async def update_workflow_notification_settings(
    workflow_type: str,
    settings_update: WorkflowNotificationSettingsUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project)
):
    """
    更新工作流通知设置
    """
    try:
        logger.info(f"更新工作流通知设置: user_id={current_user.id}, project_id={current_project.id}, workflow_type={workflow_type}")

        # 查询通知设置
        query = select(NotificationSettings).where(
            NotificationSettings.project_id == current_project.id,
            NotificationSettings.user_id == current_user.id
        )
        result = await db.execute(query)
        settings = result.scalar_one_or_none()

        if not settings:
            # 如果不存在，创建默认设置
            logger.info(f"为用户创建默认通知设置: user_id={current_user.id}, project_id={current_project.id}")
            settings = NotificationSettings(
                id=uuid.uuid4(),
                tenant_id=current_project.tenant_id,
                project_id=current_project.id,
                user_id=current_user.id,
                email_notifications=True,
                push_notifications=True,
                sms_notifications=False,
                sound_enabled=True,
                normal_frequency="realtime",
                high_priority_frequency="realtime",
                system_notifications=True,
                task_notifications=True,
                inventory_notifications=True,
                order_notifications=True,
                security_notifications=True,
                workflow_notifications={}
            )
            db.add(settings)
            await db.flush()

        # 获取工作流通知设置
        workflow_settings = settings.workflow_notifications.get(workflow_type, {})

        # 如果没有设置，创建默认设置
        if not workflow_settings:
            logger.info(f"为工作流创建默认通知设置: workflow_type={workflow_type}")
            if workflow_type == "loss_report":
                workflow_settings = {
                    "enabled": True,
                    "notify_type": "roles",
                    "notify_roles": ["purchase", "admin"],
                    "notify_users": [],
                    "title": "门店报损审核通知",
                    "template": "门店 {{store_name}} 提交了产品 {{product_name}} 的报损申请，数量: {{quantity}}，金额: {{amount}}元，请及时审核"
                }
            else:
                workflow_settings = {
                    "enabled": True,
                    "notify_type": "roles",
                    "notify_roles": ["admin"],
                    "notify_users": [],
                    "title": f"{workflow_type}通知",
                    "template": f"有新的{workflow_type}需要处理"
                }

        # 更新字段
        update_data = settings_update.dict(exclude_unset=True)
        logger.info(f"更新工作流通知设置字段: {update_data}")

        for key, value in update_data.items():
            workflow_settings[key] = value

        # 更新工作流通知设置
        if not settings.workflow_notifications:
            settings.workflow_notifications = {}

        settings.workflow_notifications[workflow_type] = workflow_settings
        settings.updated_at = datetime.now()

        await db.commit()
        await db.refresh(settings)

        # 通过WebSocket推送工作流设置变更
        try:
            from services.redis_manager import RedisManager

            # 创建WebSocket消息
            message = {
                "type": "workflow_settings_updated",
                "workflow_type": workflow_type,
                "settings": workflow_settings,
                "timestamp": datetime.now().isoformat()
            }

            # 获取Redis管理器实例
            redis_manager = await RedisManager.get_instance()

            # 发布设置变更消息到用户通道
            await redis_manager.publish_notification(
                f"user:{current_user.id}:notifications",
                message
            )

            logger.info(f"工作流通知设置变更已通过WebSocket推送: user_id={current_user.id}, workflow_type={workflow_type}")
        except Exception as e:
            logger.error(f"推送工作流通知设置变更失败: {str(e)}")
            # 不抛出异常，因为这不应该影响主流程

        return {
            "success": True,
            "message": "工作流通知设置更新成功",
            "data": workflow_settings
        }
    except Exception as e:
        logger.error(f"更新工作流通知设置失败: {str(e)}", exc_info=True)
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新工作流通知设置失败: {str(e)}"
        )
