import React, { useState, useEffect } from 'react';
import { Card, Row, Col, DatePicker, Select, Button, Table, Statistic, Spin, Tabs, Space, Empty } from 'antd';
import { BarChartOutlined, LineChartOutlined, PieChartOutlined, DownloadOutlined } from '@ant-design/icons';
import { Line, Bar, Pie } from '@ant-design/plots';
import dayjs from "dayjs";
import apiService from '../../../../services/api';
import { logOperation } from '../../../../utils/logUtils';

const { RangePicker } = DatePicker;
const { Option } = Select;
const { TabPane } = Tabs;

/**
 * 采购分析组件
 */
const PurchaseAnalysis = () => {
  // 状态
  const [loading, setLoading] = useState(false);
  const [warehouses, setWarehouses] = useState([]);
  const [suppliers, setSuppliers] = useState([]);
  const [selectedWarehouseId, setSelectedWarehouseId] = useState(null);
  const [selectedSupplierId, setSelectedSupplierId] = useState(null);
  const [dateRange, setDateRange] = useState([dayjs().subtract(30, 'days'), dayjs()]);
  const [reportType, setReportType] = useState('overview');
  const [purchaseData, setPurchaseData] = useState({
    statistics: {},
    trend_data: [],
    supplier_data: [],
    detail_data: []
  });

  // 初始化
  useEffect(() => {
    fetchWarehouses();
    fetchSuppliers();
  }, []);

  // 监听筛选条件变化
  useEffect(() => {
    if (dateRange && dateRange.length === 2) {
      fetchPurchaseData();
    }
  }, [selectedWarehouseId, selectedSupplierId, dateRange, reportType]);

  // 获取仓库列表
  const fetchWarehouses = async () => {
    try {
      const response = await apiService.project.warehouse.getList();
      if (response && Array.isArray(response.items)) {
        setWarehouses(response.items);
      } else if (response && Array.isArray(response)) {
        setWarehouses(response);
      }
    } catch (error) {
      console.error('获取仓库列表失败:', error);
    }
  };

  // 获取供应商列表
  const fetchSuppliers = async () => {
    try {
      const response = await apiService.project.supplier.getList();
      if (response && response.items) {
        setSuppliers(response.items);
      }
    } catch (error) {
      console.error('获取供应商列表失败:', error);
    }
  };

  // 获取采购数据
  const fetchPurchaseData = async () => {
    try {
      setLoading(true);
      const startDate = dateRange[0].format('YYYY-MM-DD');
      const endDate = dateRange[1].format('YYYY-MM-DD');

      const response = await apiService.project.purchaseOrder.getReport({
        start_date: startDate,
        end_date: endDate,
        report_type: reportType,
        supplier_id: selectedSupplierId,
        warehouse_id: selectedWarehouseId
      });

      if (response) {
        setPurchaseData(response);

        // 记录操作日志
        logOperation({
          module: 'finance',
          action: 'query',
          resourceType: 'purchase_analysis',
          description: `查询采购分析数据，时间范围：${startDate}至${endDate}${selectedWarehouseId ? '，指定仓库' : '，所有仓库'}${selectedSupplierId ? '，指定供应商' : '，所有供应商'}`,
          details: {
            start_date: startDate,
            end_date: endDate,
            warehouse_id: selectedWarehouseId,
            supplier_id: selectedSupplierId,
            report_type: reportType
          }
        });
      }
    } catch (error) {
      console.error('获取采购数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 处理仓库选择
  const handleWarehouseChange = (value) => {
    setSelectedWarehouseId(value);
  };

  // 处理供应商选择
  const handleSupplierChange = (value) => {
    setSelectedSupplierId(value);
  };

  // 处理日期范围选择
  const handleDateRangeChange = (dates) => {
    setDateRange(dates);
  };

  // 处理报表类型选择
  const handleReportTypeChange = (value) => {
    setReportType(value);
  };

  // 导出数据
  const handleExport = async () => {
    try {
      const startDate = dateRange[0].format('YYYY-MM-DD');
      const endDate = dateRange[1].format('YYYY-MM-DD');

      const response = await apiService.project.purchaseOrder.exportReport({
        start_date: startDate,
        end_date: endDate,
        report_type: reportType,
        supplier_id: selectedSupplierId,
        warehouse_id: selectedWarehouseId
      });

      if (response.download_url) {
        const link = document.createElement('a');
        link.href = response.download_url;
        link.download = `采购分析报表_${dayjs().format('YYYY-MM-DD')}.xlsx`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }
    } catch (error) {
      console.error('导出采购分析报表失败:', error);
    }
  };

  // 采购趋势图配置
  const purchaseTrendConfig = {
    data: purchaseData.trend_data || [],
    xField: 'date',
    yField: 'amount',
    smooth: true,
    point: {
      size: 4,
      shape: 'circle'
    },
    tooltip: {
      formatter: (datum) => {
        return { name: '采购金额', value: '¥' + (datum.amount ? datum.amount.toFixed(2) : '0.00') };
      }
    },
    xAxis: {
      type: 'time',
    },
    yAxis: {
      label: {
        formatter: (v) => `¥${v}`
      }
    }
  };

  // 供应商分布图配置
  const supplierConfig = {
    data: purchaseData.supplier_data || [],
    angleField: 'amount',
    colorField: 'name',
    radius: 0.8,
    label: {
      type: 'spider',
      labelHeight: 28,
      formatter: (datum) => {
        if (!datum || typeof datum !== 'object') return '';
        const name = datum.name || '未知';
        const percent = datum.percent || 0;
        return `${name}: ${(percent * 100).toFixed(2)}%`;
      },
    },
    interactions: [
      {
        type: 'element-active',
      },
    ],
  };

  // 采购数据表格列
  const purchaseColumns = [
    {
      title: '订单编号',
      dataIndex: 'order_number',
      key: 'order_number',
    },
    {
      title: '仓库',
      dataIndex: 'warehouse_name',
      key: 'warehouse_name',
    },
    {
      title: '订单金额',
      dataIndex: 'total_amount',
      key: 'total_amount',
      render: (amount) => `¥${parseFloat(amount || 0).toFixed(2)}`,
      align: 'right'
    },
    {
      title: '下单日期',
      dataIndex: 'order_date',
      key: 'order_date',
      render: (date) => date ? dayjs(date).format('YYYY-MM-DD') : '-'
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => {
        const statusMap = {
          draft: '草稿',
          confirmed: '已确认',
          received: '已收货',
          cancelled: '已取消'
        };
        return statusMap[status] || status;
      }
    }
  ];

  // 供应商数据表格列
  const supplierColumns = [
    {
      title: '供应商',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '采购金额',
      dataIndex: 'amount',
      key: 'amount',
      render: (amount) => `¥${parseFloat(amount || 0).toFixed(2)}`,
      align: 'right'
    },
    {
      title: '占比',
      key: 'percentage',
      render: (_, record) => {
        const total = (purchaseData.supplier_data || []).reduce((sum, item) => sum + (item.amount || 0), 0);
        const percentage = total > 0 ? ((record.amount || 0) / total * 100).toFixed(2) : 0;
        return `${percentage}%`;
      },
      align: 'right'
    }
  ];

  return (
    <div className="purchase-analysis-container">
      {/* 筛选条件 */}
      <Card style={{ marginBottom: 16 }}>
        <Row gutter={16}>
          <Col span={4}>
            <Space direction="vertical" style={{ width: '100%' }}>
              <label>日期范围：</label>
              <RangePicker
                value={dateRange}
                onChange={handleDateRangeChange}
                style={{ width: '100%' }}
              />
            </Space>
          </Col>
          <Col span={3}>
            <Space direction="vertical" style={{ width: '100%' }}>
              <label>仓库：</label>
              <Select
                value={selectedWarehouseId}
                onChange={handleWarehouseChange}
                allowClear
                placeholder="全部仓库"
                style={{ width: '100%' }}
              >
                {warehouses.map(warehouse => (
                  <Option key={warehouse.id} value={warehouse.id}>
                    {warehouse.name}
                  </Option>
                ))}
              </Select>
            </Space>
          </Col>
          <Col span={3}>
            <Space direction="vertical" style={{ width: '100%' }}>
              <label>供应商：</label>
              <Select
                value={selectedSupplierId}
                onChange={handleSupplierChange}
                allowClear
                placeholder="全部供应商"
                style={{ width: '100%' }}
              >
                {suppliers.map(supplier => (
                  <Option key={supplier.id} value={supplier.id}>
                    {supplier.name}
                  </Option>
                ))}
              </Select>
            </Space>
          </Col>
          <Col span={3}>
            <Space direction="vertical" style={{ width: '100%' }}>
              <label>报表类型：</label>
              <Select
                value={reportType}
                onChange={handleReportTypeChange}
                style={{ width: '100%' }}
              >
                <Option value="overview">综合分析</Option>
                <Option value="supplier">供应商分析</Option>
                <Option value="category">商品分析</Option>
              </Select>
            </Space>
          </Col>
          <Col span={6}>
            <Space style={{ marginTop: 24 }}>
              <Button
                type="primary"
                onClick={fetchPurchaseData}
                loading={loading}
              >
                查询
              </Button>
              <Button
                icon={<DownloadOutlined />}
                onClick={handleExport}
              >
                导出Excel
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      <Spin spinning={loading}>
        {/* 统计卡片 */}
        <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
          <Col span={6}>
            <Card>
              <Statistic
                title="采购订单总数"
                value={purchaseData.statistics?.total_orders || 0}
                prefix={<BarChartOutlined />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="采购总金额"
                value={purchaseData.statistics?.total_amount || 0}
                prefix="¥"
                precision={2}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="供应商数量"
                value={purchaseData.statistics?.supplier_count || 0}
                prefix={<PieChartOutlined />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="商品种类"
                value={purchaseData.statistics?.product_count || 0}
                prefix={<LineChartOutlined />}
              />
            </Card>
          </Col>
        </Row>

        {/* 图表展示 */}
        <Row gutter={16} style={{ marginBottom: 24 }}>
          <Col span={12}>
            <Card title="采购趋势">
              {(purchaseData.trend_data && purchaseData.trend_data.length > 0) ? (
                <Line {...purchaseTrendConfig} height={300} />
              ) : (
                <div style={{ height: 300, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                  <Empty description="暂无采购趋势数据" />
                </div>
              )}
            </Card>
          </Col>
          <Col span={12}>
            <Card title="供应商分布">
              {(purchaseData.supplier_data && purchaseData.supplier_data.length > 0) ? (
                <Pie {...supplierConfig} height={300} />
              ) : (
                <div style={{ height: 300, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                  <Empty description="暂无供应商数据" />
                </div>
              )}
            </Card>
          </Col>
        </Row>

        {/* 详细数据表格 */}
        <Tabs defaultActiveKey="1">
          <TabPane tab="采购明细" key="1">
            <Card title="采购订单明细">
              <Table
                columns={purchaseColumns}
                dataSource={purchaseData.detail_data || []}
                rowKey="id"
                pagination={{ pageSize: 10 }}
                scroll={{ x: 800 }}
              />
            </Card>
          </TabPane>
          <TabPane tab="供应商分析" key="2">
            <Card title="供应商采购统计">
              <Table
                columns={supplierColumns}
                dataSource={purchaseData.supplier_data || []}
                rowKey="name"
                pagination={{ pageSize: 10 }}
              />
            </Card>
          </TabPane>
        </Tabs>
      </Spin>
    </div>
  );
};

export default PurchaseAnalysis; 