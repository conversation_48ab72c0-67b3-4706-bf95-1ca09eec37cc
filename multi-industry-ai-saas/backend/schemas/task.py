#!/usr/bin/env python
# -*- coding: utf-8 -*-

from typing import List, Optional, Dict, Any, Union
from pydantic import BaseModel, Field, validator
from datetime import datetime
import uuid

# 任务创建模式
class TaskCreate(BaseModel):
    title: str = Field(..., description="任务标题")
    description: Optional[str] = Field(None, description="任务描述")
    task_type: str = Field("daily", description="任务类型: daily-每日, weekly-每周, monthly-每月, custom-自定义")
    category: str = Field("store", description="任务类别: store-门店, operation-运营, marketing-营销, inventory-库存, finance-财务, other-其他")
    priority: str = Field("medium", description="优先级: low-低, medium-中, high-高, urgent-紧急")
    store_id: Optional[uuid.UUID] = Field(None, description="门店ID（单个门店，向后兼容）")
    store_ids: Optional[List[uuid.UUID]] = Field(None, description="门店ID列表（多个门店）")
    is_all_stores: Optional[bool] = Field(False, description="是否适用于所有门店")
    start_date: datetime = Field(..., description="开始日期")
    due_date: Optional[datetime] = Field(None, description="截止日期")
    recurrence: Optional[Dict[str, Any]] = Field(None, description="重复规则")
    reminder: Optional[Dict[str, Any]] = Field(None, description="提醒设置")
    assignee_ids: Optional[List[uuid.UUID]] = Field(None, description="指派人ID列表")
    supervisor_ids: Optional[List[uuid.UUID]] = Field(None, description="监督人ID列表")
    attachments: Optional[List[str]] = Field(None, description="附件")
    checklist: Optional[List[Dict[str, Any]]] = Field(None, description="检查项")
    status: str = Field("pending", description="状态: pending-待处理, in_progress-进行中, completed-已完成, overdue-已逾期, cancelled-已取消")

    class Config:
        json_schema_extra = {
            "example": {
                "title": "每日盘点",
                "description": "完成门店每日商品盘点",
                "task_type": "daily",
                "category": "store",
                "priority": "medium",
                "store_id": "123e4567-e89b-12d3-a456-************",
                "start_date": "2023-01-01T00:00:00",
                "due_date": "2023-01-02T00:00:00",
                "assignee_ids": ["123e4567-e89b-12d3-a456-************"],
                "status": "pending"
            }
        }

# 任务更新模式
class TaskUpdate(BaseModel):
    title: Optional[str] = Field(None, description="任务标题")
    description: Optional[str] = Field(None, description="任务描述")
    task_type: Optional[str] = Field(None, description="任务类型: daily-每日, weekly-每周, monthly-每月, custom-自定义")
    category: Optional[str] = Field(None, description="任务类别: store-门店, operation-运营, marketing-营销, inventory-库存, finance-财务, other-其他")
    priority: Optional[str] = Field(None, description="优先级: low-低, medium-中, high-高, urgent-紧急")
    store_id: Optional[uuid.UUID] = Field(None, description="门店ID")
    start_date: Optional[datetime] = Field(None, description="开始日期")
    due_date: Optional[datetime] = Field(None, description="截止日期")
    recurrence: Optional[Dict[str, Any]] = Field(None, description="重复规则")
    reminder: Optional[Dict[str, Any]] = Field(None, description="提醒设置")
    assignee_ids: Optional[List[uuid.UUID]] = Field(None, description="指派人ID列表")
    supervisor_ids: Optional[List[uuid.UUID]] = Field(None, description="监督人ID列表")
    attachments: Optional[List[str]] = Field(None, description="附件")
    checklist: Optional[List[Dict[str, Any]]] = Field(None, description="检查项")
    status: Optional[str] = Field(None, description="状态: pending-待处理, in_progress-进行中, completed-已完成, overdue-已逾期, cancelled-已取消")
    completion_note: Optional[str] = Field(None, description="完成备注")

    class Config:
        json_schema_extra = {
            "example": {
                "title": "每日盘点",
                "description": "完成门店每日商品盘点",
                "priority": "high",
                "status": "in_progress"
            }
        }

# 任务状态更新模式
class TaskStatusUpdate(BaseModel):
    status: str = Field(..., description="状态: pending-待处理, in_progress-进行中, completed-已完成, overdue-已逾期, cancelled-已取消")
    completion_note: Optional[str] = Field(None, description="完成备注")

    class Config:
        json_schema_extra = {
            "example": {
                "status": "completed",
                "completion_note": "已完成所有商品盘点"
            }
        }

# 任务响应模式
class TaskResponse(BaseModel):
    id: str = Field(..., description="任务ID")
    project_id: str = Field(..., description="项目ID")
    store_id: Optional[str] = Field(None, description="门店ID（单个门店，向后兼容）")
    store_name: Optional[str] = Field(None, description="门店名称（单个门店，向后兼容）")
    store_ids: Optional[List[str]] = Field(None, description="门店ID列表（多个门店）")
    store_names: Optional[List[str]] = Field(None, description="门店名称列表（多个门店）")
    is_all_stores: Optional[bool] = Field(False, description="是否适用于所有门店")
    title: str = Field(..., description="任务标题")
    description: Optional[str] = Field(None, description="任务描述")
    task_type: Optional[str] = Field(None, description="任务类型: daily-每日, weekly-每周, monthly-每月, custom-自定义")
    category: str = Field(..., description="任务类别: store-门店, operation-运营, marketing-营销, inventory-库存, finance-财务, other-其他")
    priority: str = Field(..., description="优先级: low-低, medium-中, high-高, urgent-紧急")
    start_date: Optional[datetime] = Field(None, description="开始日期")
    due_date: Optional[datetime] = Field(None, description="截止日期")
    recurrence: Optional[Dict[str, Any]] = Field(None, description="重复规则")
    reminder: Optional[Dict[str, Any]] = Field(None, description="提醒设置")
    assignee_ids: Optional[List[str]] = Field(None, description="指派人ID列表")
    assignee_names: Optional[List[str]] = Field(None, description="指派人名称列表")
    supervisor_ids: Optional[List[str]] = Field(None, description="监督人ID列表")
    attachments: Optional[List[str]] = Field(None, description="附件")
    checklist: Optional[List[Dict[str, Any]]] = Field(None, description="检查项")
    status: str = Field(..., description="状态: pending-待处理, in_progress-进行中, completed-已完成, overdue-已逾期, cancelled-已取消")
    completion_date: Optional[datetime] = Field(None, description="完成日期")
    completion_note: Optional[str] = Field(None, description="完成备注")
    created_by: Optional[str] = Field(None, description="创建人ID")
    updated_by: Optional[str] = Field(None, description="更新人ID")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    class Config:
        from_attributes = True

# 任务列表响应模式
class TaskListResponse(BaseModel):
    items: List[TaskResponse] = Field(..., description="任务列表")
    total: int = Field(..., description="总数")

# 任务评论创建模式
class TaskCommentCreate(BaseModel):
    content: str = Field(..., description="评论内容")
    attachments: Optional[List[str]] = Field(None, description="附件")

    class Config:
        json_schema_extra = {
            "example": {
                "content": "已完成部分商品盘点，明天继续",
                "attachments": ["https://example.com/image.jpg"]
            }
        }

# 任务评论响应模式
class TaskCommentResponse(BaseModel):
    id: str = Field(..., description="评论ID")
    task_id: str = Field(..., description="任务ID")
    content: str = Field(..., description="评论内容")
    attachments: Optional[List[str]] = Field(None, description="附件")
    created_by: Optional[str] = Field(None, description="创建人ID")
    created_by_name: Optional[str] = Field(None, description="创建人名称")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")

    class Config:
        from_attributes = True

# 任务评论列表响应模式
class TaskCommentListResponse(BaseModel):
    items: List[TaskCommentResponse] = Field(..., description="评论列表")
    total: int = Field(..., description="总数")

# 任务统计项模式
class TaskStatItem(BaseModel):
    name: str = Field(..., description="统计项名称")
    value: int = Field(..., description="统计项值")

# 任务状态统计模式
class TaskStatusStats(BaseModel):
    total: int = Field(..., description="总任务数")
    pending: int = Field(..., description="待处理任务数")
    in_progress: int = Field(..., description="进行中任务数")
    completed: int = Field(..., description="已完成任务数")
    overdue: int = Field(..., description="已逾期任务数")
    cancelled: int = Field(..., description="已取消任务数")

# 任务时间趋势统计项模式
class TaskTimeStatItem(BaseModel):
    date: str = Field(..., description="日期")
    count: int = Field(..., description="任务数量")

# 任务统计响应模式
class TaskStatsResponse(BaseModel):
    status_stats: TaskStatusStats = Field(..., description="任务状态统计")
    store_stats: List[TaskStatItem] = Field(..., description="按门店统计")
    category_stats: List[TaskStatItem] = Field(..., description="按类别统计")
    type_stats: List[TaskStatItem] = Field(..., description="按任务类型统计")
    priority_stats: List[TaskStatItem] = Field(..., description="按优先级统计")
    time_stats: List[TaskTimeStatItem] = Field(..., description="按时间统计")
