import React, { useState, useEffect } from 'react';
import { Card, Table, Button, Space, Tag, Modal, Form, Input, Select, DatePicker, Tabs, Spin, message, Tooltip, Popconfirm, Badge, Drawer, Descriptions, Alert, Row, Col, Statistic, Empty, Typography, Divider, Upload, InputNumber, List, Image } from 'antd';
import { PlusOutlined, ReloadOutlined, DeleteOutlined, EditOutlined, EyeOutlined, Bar<PERSON>hartOutlined, RobotOutlined, SearchOutlined, EnvironmentOutlined, ShopOutlined, AimOutlined, UploadOutlined, InboxOutlined, CheckCircleOutlined } from '@ant-design/icons';
import { useParams } from 'react-router-dom';
import dayjs from 'dayjs';
import storeService from '../../../../services/api/project/store';
import dateTimeUtils from '../../../../utils/dateTimeUtils';
import apiService from '../../../../services/api';
import { getProjectId } from '../../../../services/api/httpClient';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;
const { RangePicker } = DatePicker;
const { TextArea } = Input;

const AreaSurvey = () => {
  const { projectId, storeId } = useParams();

  // 检查 storeId 是否有效
  const [currentStoreId, setCurrentStoreId] = useState(null);
  const [stores, setStores] = useState([]);

  // 状态
  const [loading, setLoading] = useState(false);
  const [surveys, setSurveys] = useState([]);
  const [pagination, setPagination] = useState({ current: 1, pageSize: 10, total: 0 });
  const [filters, setFilters] = useState({
    status: undefined,
    survey_type: undefined,
    date_range: undefined,
    search: undefined
  });

  // 表单状态
  const [form] = Form.useForm();
  const [modalVisible, setModalVisible] = useState(false);
  const [modalTitle, setModalTitle] = useState('创建周边调研');
  const [editingSurvey, setEditingSurvey] = useState(null);

  // 详情抽屉状态
  const [detailVisible, setDetailVisible] = useState(false);
  const [detailSurvey, setDetailSurvey] = useState(null);
  const [detailTab, setDetailTab] = useState('1');

  // 产品相关状态
  const [products, setProducts] = useState([]);
  const [loadingProducts, setLoadingProducts] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState(null);

  // 图片上传状态
  const [fileList, setFileList] = useState([]);
  const [uploadLoading, setUploadLoading] = useState(false);

  // 统计数据
  const [stats, setStats] = useState({
    total: 0,
    draft: 0,
    in_progress: 0,
    completed: 0,
    cancelled: 0,
    by_type: {},
    by_rating: {
      '1': 0,
      '2': 0,
      '3': 0,
      '4': 0,
      '5': 0
    },
    recent_surveys: []
  });

  // AI分析状态
  const [aiAnalyzing, setAiAnalyzing] = useState(false);

  // 初始化
  useEffect(() => {
    // 获取门店列表
    fetchStores();
  }, []);

  // 当 currentStoreId 变化时获取数据
  useEffect(() => {
    if (currentStoreId) {
      fetchSurveys();
      fetchStats();
      fetchProducts();
    }
  }, [currentStoreId]);

  // 获取产品列表
  const fetchProducts = async () => {
    try {
      if (!currentStoreId) return;

      setLoadingProducts(true);

      // 获取项目ID
      const projectId = getProjectId();
      if (!projectId) {
        message.error('未找到项目ID');
        return;
      }

      // 使用正确的产品API
      const response = await apiService.project.product.getList({
        page: 1,
        page_size: 100
      });

      if (response && response.data) {
        console.log('获取到产品列表:', response.data);
        setProducts(response.data);
      } else {
        console.log('产品API返回空数据');
        setProducts([]);
      }
    } catch (error) {
      console.error('获取产品列表失败:', error);
      message.error('获取产品列表失败');
      setProducts([]);
    } finally {
      setLoadingProducts(false);
    }
  };

  // 处理产品选择
  const handleProductSelect = (productId) => {
    const product = products.find(p => p.id === productId || p.id === String(productId));

    if (product) {
      console.log('选择产品:', product);
      setSelectedProduct(product);

      // 自动填充产品信息
      form.setFieldsValue({
        product_name: product.name,
        product_spec: product.specification || product.spec || '',
        product_unit: product.unit || '公斤',
        product_price: product.price || product.retail_price || product.cost_price || 0
      });
    } else {
      console.log('未找到产品:', productId);
      setSelectedProduct(null);
    }
  };

  // 处理图片上传前的检查
  const beforeUpload = (file) => {
    const isImage = file.type.startsWith('image/');
    if (!isImage) {
      message.error('只能上传图片文件!');
      return Upload.LIST_IGNORE;
    }

    const isLt2M = file.size / 1024 / 1024 < 2;
    if (!isLt2M) {
      message.error('图片必须小于 2MB!');
      return Upload.LIST_IGNORE;
    }

    // 不自动上传，而是添加到文件列表
    return false;
  };

  // 处理图片上传
  const handleUpload = async (file) => {
    try {
      setUploadLoading(true);

      const projectId = getProjectId();
      if (!projectId) {
        message.error('未找到项目ID');
        return;
      }

      // 使用项目空间上传API
      const formData = new FormData();
      formData.append('file', file);
      formData.append('folder_path', '/area-surveys');
      formData.append('description', '周边调研图片');
      formData.append('is_public', 'false');

      const response = await apiService.post(`/api/v1/project/${projectId}/space/upload`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });

      if (response.data && response.data.success) {
        // 上传成功，添加到文件列表
        const newFile = {
          uid: file.uid,
          name: file.name,
          status: 'done',
          url: response.data.url,
          response: response.data
        };

        const newFileList = [...fileList, newFile].slice(0, 5); // 限制最多5张图片
        setFileList(newFileList);

        // 更新表单中的附件列表
        const attachments = newFileList
          .filter(f => f.status === 'done')
          .map(f => f.response?.url || f.url);

        form.setFieldsValue({ attachments });

        message.success(`${file.name} 上传成功`);
      } else {
        message.error(`${file.name} 上传失败: ${response.data?.message || '未知错误'}`);
      }
    } catch (error) {
      console.error('上传图片失败:', error);
      message.error(`上传图片失败: ${error.message}`);
    } finally {
      setUploadLoading(false);
    }
  };

  // 处理图片上传状态变化
  const handleUploadChange = ({ fileList }) => {
    // 限制上传数量为 5 张
    const newFileList = fileList.slice(0, 5);
    setFileList(newFileList);
  };

  // 获取门店列表
  const fetchStores = async () => {
    try {
      setLoading(true);
      const response = await storeService.getList();

      if (response && response.items) {
        setStores(response.items);

        // 如果 URL 中有 storeId 且有效，则使用它
        if (storeId && storeId !== 'undefined') {
          const validStore = response.items.find(store => store.id === storeId);
          if (validStore) {
            setCurrentStoreId(storeId);
          } else {
            // 如果 URL 中的 storeId 无效，使用第一个门店
            setCurrentStoreId(response.items[0]?.id || null);
          }
        } else {
          // 如果 URL 中没有 storeId，使用第一个门店
          setCurrentStoreId(response.items[0]?.id || null);
        }
      }
    } catch (error) {
      console.error('获取门店列表失败:', error);
      message.error('获取门店列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 处理门店切换
  const handleStoreChange = (value) => {
    setCurrentStoreId(value);
  };

  // 获取周边调研列表
  const fetchSurveys = async (params = {}) => {
    try {
      if (!currentStoreId) {
        return;
      }

      setLoading(true);

      // 构建查询参数
      const queryParams = {
        page: params.page || pagination.current,
        page_size: params.pageSize || pagination.pageSize,
        ...filters
      };

      // 处理日期范围
      if (filters.date_range && filters.date_range.length === 2) {
        queryParams.start_date = filters.date_range[0].format('YYYY-MM-DD');
        queryParams.end_date = filters.date_range[1].format('YYYY-MM-DD');
        delete queryParams.date_range;
      }

      const response = await storeService.getAreaSurveys(currentStoreId, queryParams);

      // 无论 success 是否为 true，只要有数据就处理
      if (response && (response.items || response.data)) {
        const items = response.items || response.data || [];
        setSurveys(items);
        setPagination({
          current: response.page || 1,
          pageSize: response.page_size || 10,
          total: response.total || 0
        });
      } else {
        console.log('获取周边调研列表返回格式异常:', response);
        setSurveys([]);
      }
    } catch (error) {
      console.error('获取周边调研列表失败:', error);
      message.error('获取周边调研列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取统计数据
  const fetchStats = async () => {
    try {
      if (!currentStoreId) {
        return;
      }

      const response = await storeService.getAreaSurveyStats(currentStoreId);

      // 无论 success 是否为 true，只要有数据就处理
      if (response) {
        // 确保统计数据有默认值
        const defaultStats = {
          total: 0,
          completed: 0,
          in_progress: 0,
          draft: 0,
          cancelled: 0,
          price_surveys: 0,
          promotion_surveys: 0,
          product_surveys: 0,
          service_surveys: 0,
          other_surveys: 0
        };

        setStats({...defaultStats, ...response});
      } else {
        console.log('获取周边调研统计数据返回格式异常:', response);
        setStats({
          total: 0,
          completed: 0,
          in_progress: 0,
          draft: 0,
          cancelled: 0,
          price_surveys: 0,
          promotion_surveys: 0,
          product_surveys: 0,
          service_surveys: 0,
          other_surveys: 0
        });
      }
    } catch (error) {
      console.error('获取周边调研统计数据失败:', error);
      message.error('获取周边调研统计数据失败');
    }
  };

  // 处理表格变化
  const handleTableChange = (pagination, filters, sorter) => {
    fetchSurveys({
      page: pagination.current,
      pageSize: pagination.pageSize
    });
  };

  // 处理筛选变化
  const handleFilterChange = (key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  // 应用筛选
  const handleSearch = () => {
    setPagination(prev => ({
      ...prev,
      current: 1
    }));
    fetchSurveys({ page: 1 });
  };

  // 重置筛选
  const handleReset = () => {
    setFilters({
      status: undefined,
      survey_type: undefined,
      date_range: undefined,
      search: undefined
    });
    setPagination(prev => ({
      ...prev,
      current: 1
    }));
    fetchSurveys({ page: 1 });
  };

  // 打开创建表单
  const handleCreate = () => {
    setModalTitle('创建周边调研');
    setEditingSurvey(null);
    form.resetFields();
    setSelectedProduct(null);
    setFileList([]);

    // 设置默认值
    form.setFieldsValue({
      status: 'draft',
      survey_type: 'price',
      start_date: dayjs(),
      store_id: currentStoreId,
      attachments: []
    });

    setModalVisible(true);
  };

  // 打开编辑表单
  const handleEdit = (record) => {
    setModalTitle('编辑周边调研');
    setEditingSurvey(record);
    setSelectedProduct(null);

    // 处理附件
    const attachments = record.attachments || [];
    const fileList = attachments.map((url, index) => ({
      uid: `-${index}`,
      name: url.split('/').pop(),
      status: 'done',
      url: url,
    }));
    setFileList(fileList);

    // 如果是价格调研，尝试查找对应的产品
    if (record.survey_type === 'price' && record.product_id) {
      const product = products.find(p => p.id === record.product_id);
      if (product) {
        setSelectedProduct(product);
      }
    }

    // 设置表单值
    form.setFieldsValue({
      ...record,
      start_date: record.start_date ? dayjs(record.start_date) : null,
      end_date: record.end_date ? dayjs(record.end_date) : null,
      store_id: currentStoreId,
      attachments: record.attachments || []
    });

    setModalVisible(true);
  };

  // 查看详情
  const handleView = async (record) => {
    try {
      if (!currentStoreId) {
        message.error('请先选择门店');
        return;
      }

      setLoading(true);
      // 先显示基本信息，然后异步加载完整详情
      setDetailSurvey(record);
      setDetailVisible(true);
      setDetailTab('1');

      // 获取完整详情
      const response = await storeService.getAreaSurveyDetail(currentStoreId, record.id);

      // 检查响应格式，处理不同的响应结构
      if (response) {
        // 如果响应中有 success 字段且为 true，或者没有 success 字段但有必要的数据
        if ((response.success === true) ||
            (!response.hasOwnProperty('success') && response.id)) {
          // 如果响应本身就是数据对象
          if (response.id) {
            setDetailSurvey(response);
          }
          // 如果数据在 response.data 中
          else if (response.data && response.data.id) {
            setDetailSurvey(response.data);
          }
          // 如果是其他格式但包含必要信息
          else {
            // 合并原始记录和响应数据，确保有完整信息
            setDetailSurvey({...record, ...response});
          }
        } else {
          console.error('API响应格式不符合预期:', response);
          message.error('获取周边调研详情失败: ' + (response.message || '未知错误'));
          // 至少显示我们已有的数据
          setDetailSurvey(record);
        }
      } else {
        console.error('API响应为空');
        message.error('获取周边调研详情失败: 服务器未返回数据');
        // 至少显示我们已有的数据
        setDetailSurvey(record);
      }
    } catch (error) {
      console.error('获取周边调研详情失败:', error);
      message.error('获取周边调研详情失败');
    } finally {
      setLoading(false);
    }
  };

  // 删除调研
  const handleDelete = async (id) => {
    try {
      if (!currentStoreId) {
        message.error('请先选择门店');
        return;
      }

      setLoading(true);
      await storeService.deleteAreaSurvey(currentStoreId, id);
      message.success('删除周边调研成功');
      fetchSurveys();
      fetchStats();
    } catch (error) {
      console.error('删除周边调研失败:', error);
      message.error('删除周边调研失败');
    } finally {
      setLoading(false);
    }
  };

  // 更新调研状态
  const handleUpdateStatus = async (id, status) => {
    try {
      if (!currentStoreId) {
        message.error('请先选择门店');
        return;
      }

      setLoading(true);

      // 准备更新数据
      const updateData = {
        status: status,
        completed_date: status === 'completed' ? new Date().toISOString() : null
      };

      await storeService.updateAreaSurvey(currentStoreId, id, updateData);
      message.success(`调研已${status === 'completed' ? '标记为完成' : '更新状态'}`);
      fetchSurveys();
      fetchStats();
    } catch (error) {
      console.error('更新周边调研状态失败:', error);
      message.error('更新周边调研状态失败');
    } finally {
      setLoading(false);
    }
  };

  // AI分析
  const handleAiAnalysis = async (id) => {
    try {
      if (!currentStoreId) {
        message.error('请先选择门店');
        return;
      }

      setAiAnalyzing(true);
      const response = await storeService.analyzeAreaSurvey(currentStoreId, id);

      if (response.success) {
        message.success('AI分析完成');
        // 更新详情
        setDetailSurvey(response);
        // 刷新列表
        fetchSurveys();
      } else {
        message.error('AI分析失败');
      }
    } catch (error) {
      console.error('AI分析失败:', error);
      message.error('AI分析失败');
    } finally {
      setAiAnalyzing(false);
    }
  };

  // 提交表单
  const handleSubmit = async () => {
    try {
      if (!currentStoreId) {
        message.error('请先选择门店');
        return;
      }

      const values = await form.validateFields();
      setLoading(true);

      // 处理日期
      if (values.start_date) {
        values.start_date = values.start_date.format('YYYY-MM-DD HH:mm:ss');
      }

      if (values.end_date) {
        values.end_date = values.end_date.format('YYYY-MM-DD HH:mm:ss');
      }

      // 确保 store_id 是当前选中的门店
      values.store_id = currentStoreId;

      // 处理产品相关数据
      if (values.survey_type === 'price') {
        // 即使没有选择产品，也保存价格信息
        const productId = selectedProduct ? selectedProduct.id : null;
        values.product_id = productId;

        // 计算价格差异和百分比
        const price = parseFloat(values.product_price) || 0;
        const competitorPrice = parseFloat(values.competitor_price) || 0;
        const priceDifference = competitorPrice - price;
        const priceDifferencePercentage = price ? Math.round((priceDifference / price) * 100) : 0;

        // 保存到 product_data 对象
        values.product_data = {
          id: productId,
          name: values.product_name,
          specification: values.product_spec,
          unit: values.product_unit || '公斤',
          price: price,
          competitor_price: competitorPrice,
          price_difference: priceDifference,
          price_difference_percentage: priceDifferencePercentage,
          price_notes: values.price_notes
        };

        // 同时保存到顶级字段，确保即使 product_data 为 null 也能显示
        values.product_name = values.product_name;
        values.product_spec = values.product_spec;
        values.product_unit = values.product_unit || '公斤';
        values.product_price = price;
        values.competitor_price = competitorPrice;
        values.price_difference = priceDifference;
        values.price_difference_percentage = priceDifferencePercentage;
        values.price_notes = values.price_notes;
      }

      // 处理促销活动数据
      if (values.survey_type === 'promotion') {
        // 格式化日期
        const startDate = values.promotion_start_date ?
          values.promotion_start_date.format('YYYY-MM-DD') : null;
        const endDate = values.promotion_end_date ?
          values.promotion_end_date.format('YYYY-MM-DD') : null;

        // 保存到 promotion_data 对象
        values.promotion_data = {
          promotion_name: values.promotion_name,
          promotion_type: values.promotion_type,
          promotion_discount: values.promotion_discount,
          promotion_start_date: startDate,
          promotion_end_date: endDate,
          promotion_description: values.promotion_description,
          target_customers: values.target_customers,
          promotion_channels: values.promotion_channels,
          promotion_results: values.promotion_results
        };

        // 同时保存到顶级字段，确保即使 promotion_data 为 null 也能显示
        values.promotion_name = values.promotion_name;
        values.promotion_type = values.promotion_type;
        values.promotion_discount = values.promotion_discount;
        values.promotion_start_date = startDate;
        values.promotion_end_date = endDate;
        values.promotion_description = values.promotion_description;
        values.target_customers = values.target_customers;
        values.promotion_channels = values.promotion_channels;
        values.promotion_results = values.promotion_results;

        // 添加促销期间字段，方便显示
        if (startDate) {
          values.promotion_period = `${startDate} 至 ${endDate || '未定'}`;
        }
      }

      // 确保附件是数组
      if (!values.attachments) {
        values.attachments = [];
      } else if (!Array.isArray(values.attachments)) {
        values.attachments = [values.attachments];
      }

      // 从 fileList 中获取上传成功的文件 URL
      const uploadedFiles = fileList
        .filter(file => file.status === 'done')
        .map(file => file.response?.url || file.url);

      // 合并已有附件和新上传的附件
      values.attachments = [...values.attachments, ...uploadedFiles].filter(Boolean);

      let response;

      if (editingSurvey) {
        // 更新
        response = await storeService.updateAreaSurvey(currentStoreId, editingSurvey.id, values);
      } else {
        // 创建
        response = await storeService.createAreaSurvey(currentStoreId, values);
      }

      if (response.success) {
        message.success(editingSurvey ? '更新周边调研成功' : '创建周边调研成功');
        setModalVisible(false);
        fetchSurveys();
        fetchStats();
      } else {
        message.error(editingSurvey ? '更新周边调研失败' : '创建周边调研失败');
      }
    } catch (error) {
      console.error(editingSurvey ? '更新周边调研失败:' : '创建周边调研失败:', error);
      message.error(editingSurvey ? '更新周边调研失败' : '创建周边调研失败');
    } finally {
      setLoading(false);
    }
  };

  // 表格列定义
  const columns = [
    {
      title: '调研标题',
      dataIndex: 'title',
      key: 'title',
      render: (text, record) => (
        <a onClick={() => handleView(record)}>{text}</a>
      )
    },
    {
      title: '目标门店',
      dataIndex: 'target_store_name',
      key: 'target_store_name',
      render: (text, record) => (
        <Space>
          <ShopOutlined />
          <span>{text}</span>
          {record.target_store_distance && (
            <Tag color="blue">{record.target_store_distance}公里</Tag>
          )}
        </Space>
      )
    },
    {
      title: '调研类型',
      dataIndex: 'survey_type',
      key: 'survey_type',
      render: (text) => {
        const typeMap = {
          'price': { color: 'green', text: '价格调研' },
          'promotion': { color: 'orange', text: '促销活动' },
          'product': { color: 'blue', text: '产品调研' },
          'service': { color: 'purple', text: '服务调研' },
          'other': { color: 'default', text: '其他' }
        };

        const type = typeMap[text] || { color: 'default', text: '未知' };

        return <Tag color={type.color}>{type.text}</Tag>;
      }
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (text) => {
        const statusMap = {
          'draft': { color: 'default', text: '草稿' },
          'in_progress': { color: 'processing', text: '进行中' },
          'completed': { color: 'success', text: '已完成' },
          'cancelled': { color: 'error', text: '已取消' }
        };

        const status = statusMap[text] || { color: 'default', text: '未知' };

        return <Badge status={status.color} text={status.text} />;
      }
    },
    {
      title: '评分',
      dataIndex: 'rating',
      key: 'rating',
      render: (text) => {
        if (!text) return '-';

        const colors = ['', 'red', 'orange', 'yellow', 'blue', 'green'];
        return <Tag color={colors[text] || 'default'}>{text} 分</Tag>;
      }
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (text) => text ? dateTimeUtils.formatDateTime(text) : '-'
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => handleView(record)}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
              disabled={record.status === 'completed' || record.status === 'cancelled'}
            />
          </Tooltip>
          {record.status !== 'completed' && (
            <Tooltip title="标记完成">
              <Button
                type="text"
                icon={<CheckCircleOutlined />}
                onClick={() => handleUpdateStatus(record.id, 'completed')}
                style={{ color: '#52c41a' }}
              />
            </Tooltip>
          )}
          <Tooltip title="AI分析">
            <Button
              type="text"
              icon={<RobotOutlined />}
              onClick={() => handleAiAnalysis(record.id)}
              disabled={record.status !== 'completed'}
            />
          </Tooltip>
          <Popconfirm
            title="确定要删除这个周边调研吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
            />
          </Popconfirm>
        </Space>
      )
    }
  ];

  // 渲染统计卡片
  const renderStatCards = () => {
    return (
      <Row gutter={16} style={{ marginBottom: 16 }}>
        <Col span={4}>
          <Card>
            <Statistic
              title="总调研数"
              value={stats.total}
              prefix={<BarChartOutlined />}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="草稿"
              value={stats.draft}
              valueStyle={{ color: '#999' }}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="进行中"
              value={stats.in_progress}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="已完成"
              value={stats.completed}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="已取消"
              value={stats.cancelled}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="AI分析"
              value={Object.values(surveys).filter(s => s.is_ai_analyzed).length}
              prefix={<RobotOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>
    );
  };

  // 渲染筛选区域
  const renderFilters = () => {
    return (
      <div className="filter-section" style={{ marginBottom: 16 }}>
        <Row gutter={16} align="middle">
          <Col span={5}>
            <Space>
              <span>状态：</span>
              <Select
                placeholder="选择状态"
                allowClear
                style={{ width: 120 }}
                value={filters.status}
                onChange={(value) => handleFilterChange('status', value)}
              >
                <Select.Option value="draft">草稿</Select.Option>
                <Select.Option value="in_progress">进行中</Select.Option>
                <Select.Option value="completed">已完成</Select.Option>
                <Select.Option value="cancelled">已取消</Select.Option>
              </Select>
            </Space>
          </Col>
          <Col span={5}>
            <Space>
              <span>类型：</span>
              <Select
                placeholder="选择类型"
                allowClear
                style={{ width: 120 }}
                value={filters.survey_type}
                onChange={(value) => handleFilterChange('survey_type', value)}
              >
                <Select.Option value="price">价格调研</Select.Option>
                <Select.Option value="promotion">促销活动</Select.Option>
                <Select.Option value="product">产品调研</Select.Option>
                <Select.Option value="service">服务调研</Select.Option>
                <Select.Option value="other">其他</Select.Option>
              </Select>
            </Space>
          </Col>
          <Col span={8}>
            <Space>
              <span>日期范围：</span>
              <RangePicker
                value={filters.date_range}
                onChange={(dates) => handleFilterChange('date_range', dates)}
                placeholder={['开始日期', '结束日期']}
              />
            </Space>
          </Col>
          <Col span={6}>
            <Space>
              <Input
                placeholder="搜索标题或目标门店"
                allowClear
                value={filters.search}
                onChange={(e) => handleFilterChange('search', e.target.value)}
                prefix={<SearchOutlined />}
                style={{ width: 200 }}
              />
              <Button type="primary" onClick={handleSearch}>搜索</Button>
              <Button onClick={handleReset}>重置</Button>
            </Space>
          </Col>
        </Row>
      </div>
    );
  };

  // 监听表单字段变化
  const surveyType = Form.useWatch('survey_type', form);

  // 渲染表单
  const renderForm = () => {
    return (
      <Form
        form={form}
        layout="vertical"
      >
        <Form.Item
          name="title"
          label="调研标题"
          rules={[{ required: true, message: '请输入调研标题' }]}
        >
          <Input placeholder="请输入调研标题" />
        </Form.Item>

        <Row gutter={16}>
          <Col span={8}>
            <Form.Item
              name="survey_type"
              label="调研类型"
              rules={[{ required: true, message: '请选择调研类型' }]}
            >
              <Select placeholder="请选择调研类型">
                <Select.Option value="price">价格调研</Select.Option>
                <Select.Option value="promotion">促销活动</Select.Option>
                <Select.Option value="product">产品调研</Select.Option>
                <Select.Option value="service">服务调研</Select.Option>
                <Select.Option value="other">其他</Select.Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              name="status"
              label="状态"
              rules={[{ required: true, message: '请选择状态' }]}
            >
              <Select placeholder="请选择状态">
                <Select.Option value="draft">草稿</Select.Option>
                <Select.Option value="in_progress">进行中</Select.Option>
                <Select.Option value="completed">已完成</Select.Option>
                <Select.Option value="cancelled">已取消</Select.Option>
              </Select>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              name="rating"
              label="评分 (1-5)"
            >
              <Select placeholder="请选择评分">
                <Select.Option value={1}>1分</Select.Option>
                <Select.Option value={2}>2分</Select.Option>
                <Select.Option value={3}>3分</Select.Option>
                <Select.Option value={4}>4分</Select.Option>
                <Select.Option value={5}>5分</Select.Option>
              </Select>
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="start_date"
              label="开始日期"
            >
              <DatePicker style={{ width: '100%' }} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="end_date"
              label="结束日期"
            >
              <DatePicker style={{ width: '100%' }} />
            </Form.Item>
          </Col>
        </Row>

        <Form.Item
          name="target_store_name"
          label="目标门店名称"
          rules={[{ required: true, message: '请输入目标门店名称' }]}
        >
          <Input placeholder="请输入目标门店名称" prefix={<ShopOutlined />} />
        </Form.Item>

        <Row gutter={16}>
          <Col span={16}>
            <Form.Item
              name="target_store_address"
              label="目标门店地址"
            >
              <Input placeholder="请输入目标门店地址" prefix={<EnvironmentOutlined />} />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              name="target_store_distance"
              label="距离(公里)"
            >
              <InputNumber min={0} max={100} step={0.1} style={{ width: '100%' }} placeholder="请输入距离" addonAfter="公里" />
            </Form.Item>
          </Col>
        </Row>

        {/* 价格调研特有字段 */}
        {surveyType === 'price' && (
          <div className="price-survey-fields">
            <Card title="价格调研信息" style={{ marginBottom: 16 }}>
              <Form.Item
                name="product_id"
                label="选择产品"
                rules={[{ required: true, message: '请选择产品' }]}
              >
                <Select
                  placeholder="请选择产品"
                  loading={loadingProducts}
                  onChange={handleProductSelect}
                  showSearch
                  optionFilterProp="children"
                  notFoundContent={loadingProducts ? <Spin size="small" /> : (products.length === 0 ? "暂无产品数据" : null)}
                  style={{ width: '100%' }}
                >
                  {products && products.length > 0 ? (
                    products.map(product => (
                      <Select.Option key={product.id} value={product.id}>
                        {product.name} {product.specification ? `(${product.specification})` : ''}
                      </Select.Option>
                    ))
                  ) : (
                    <Select.Option disabled value="no-product">暂无产品数据</Select.Option>
                  )}
                </Select>
                {products && products.length === 0 && !loadingProducts && (
                  <div style={{ color: '#ff4d4f', marginTop: 8 }}>
                    未找到产品数据，请先在产品管理中添加产品
                  </div>
                )}
              </Form.Item>

              <Row gutter={16}>
                <Col span={8}>
                  <Form.Item
                    name="product_name"
                    label="产品名称"
                    rules={[{ required: true, message: '请输入产品名称' }]}
                  >
                    <Input placeholder="产品名称" disabled={!!selectedProduct} />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    name="product_spec"
                    label="规格"
                  >
                    <Input placeholder="产品规格" />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    name="product_unit"
                    label="单位"
                  >
                    <Input placeholder="产品单位" />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="product_price"
                    label="我方售价"
                    rules={[{ required: true, message: '请输入我方售价' }]}
                  >
                    <InputNumber
                      min={0}
                      step={0.01}
                      style={{ width: '100%' }}
                      placeholder="我方售价"
                      addonAfter="元"
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="competitor_price"
                    label="竞对售价"
                    rules={[{ required: true, message: '请输入竞对售价' }]}
                  >
                    <InputNumber
                      min={0}
                      step={0.01}
                      style={{ width: '100%' }}
                      placeholder="竞对售价"
                      addonAfter="元"
                    />
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col span={16}>
                  <Form.Item
                    name="price_notes"
                    label="价格备注"
                  >
                    <Input placeholder="价格备注" />
                  </Form.Item>
                </Col>
              </Row>
            </Card>
          </div>
        )}

        {/* 促销活动特有字段 */}
        {surveyType === 'promotion' && (
          <div className="promotion-survey-fields">
            <Card title="促销活动信息" style={{ marginBottom: 16 }}>
              <Row gutter={16}>
                <Col span={12}>
                  <Form.Item
                    name="promotion_name"
                    label="活动名称"
                    rules={[{ required: true, message: '请输入活动名称' }]}
                  >
                    <Input placeholder="活动名称" />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="promotion_type"
                    label="活动类型"
                    rules={[{ required: true, message: '请选择活动类型' }]}
                  >
                    <Select placeholder="请选择活动类型">
                      <Select.Option value="discount">折扣</Select.Option>
                      <Select.Option value="gift">赠品</Select.Option>
                      <Select.Option value="coupon">优惠券</Select.Option>
                      <Select.Option value="bundle">套餐</Select.Option>
                      <Select.Option value="other">其他</Select.Option>
                    </Select>
                  </Form.Item>
                </Col>
              </Row>

              <Row gutter={16}>
                <Col span={8}>
                  <Form.Item
                    name="promotion_discount"
                    label="折扣力度"
                  >
                    <InputNumber
                      min={0}
                      max={100}
                      step={0.1}
                      style={{ width: '100%' }}
                      placeholder="折扣力度"
                      addonAfter="%"
                    />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    name="promotion_start_date"
                    label="活动开始日期"
                  >
                    <DatePicker style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
                <Col span={8}>
                  <Form.Item
                    name="promotion_end_date"
                    label="活动结束日期"
                  >
                    <DatePicker style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
              </Row>

              <Form.Item
                name="promotion_description"
                label="活动描述"
              >
                <TextArea rows={3} placeholder="活动描述" />
              </Form.Item>
            </Card>
          </div>
        )}

        <Form.Item
          name="description"
          label="调研描述"
        >
          <TextArea rows={4} placeholder="请输入调研描述" />
        </Form.Item>

        {/* 图片上传 */}
        <Form.Item
          label="调研图片"
          extra="支持 jpg, png, jpeg 格式，单张图片不超过 2MB，最多上传 5 张"
        >
          <Upload
            name="file"
            listType="picture-card"
            fileList={fileList}
            onChange={handleUploadChange}
            beforeUpload={(file) => {
              beforeUpload(file) !== Upload.LIST_IGNORE && handleUpload(file);
              return false; // 阻止自动上传
            }}
            maxCount={5}
            multiple={false}
            onRemove={(file) => {
              const newFileList = fileList.filter(item => item.uid !== file.uid);
              setFileList(newFileList);

              // 更新表单中的附件列表
              const attachments = newFileList
                .filter(f => f.status === 'done')
                .map(f => f.response?.url || f.url);

              form.setFieldsValue({ attachments });
            }}
          >
            {fileList.length >= 5 ? null : (
              <div>
                <PlusOutlined />
                <div style={{ marginTop: 8 }}>上传图片</div>
              </div>
            )}
          </Upload>
          {uploadLoading && <Spin tip="上传中..." />}
        </Form.Item>

        <Form.Item
          name="conclusion"
          label="调研结论"
        >
          <TextArea rows={4} placeholder="请输入调研结论" />
        </Form.Item>

        <Form.Item
          name="recommendations"
          label="调研建议"
        >
          <TextArea rows={4} placeholder="请输入调研建议" />
        </Form.Item>

        <Form.Item name="store_id" hidden>
          <Input />
        </Form.Item>

        <Form.Item name="attachments" hidden>
          <Input />
        </Form.Item>
      </Form>
    );
  };

  // 渲染详情抽屉
  const renderDetailDrawer = () => {
    if (!detailSurvey) return null;

    return (
      <Drawer
        title={`周边调研详情: ${detailSurvey.title}`}
        width={700}
        placement="right"
        onClose={() => setDetailVisible(false)}
        open={detailVisible}
      >
        <Tabs activeKey={detailTab} onChange={setDetailTab}>
          <TabPane tab="基本信息" key="1">
            <Descriptions bordered column={2}>
              <Descriptions.Item label="调研标题" span={2}>{detailSurvey.title}</Descriptions.Item>
              <Descriptions.Item label="调研类型">
                {(() => {
                  const typeMap = {
                    'price': '价格调研',
                    'promotion': '促销活动',
                    'product': '产品调研',
                    'service': '服务调研',
                    'other': '其他'
                  };
                  return typeMap[detailSurvey.survey_type] || '未知';
                })()}
              </Descriptions.Item>
              <Descriptions.Item label="状态">
                {(() => {
                  const statusMap = {
                    'draft': '草稿',
                    'in_progress': '进行中',
                    'completed': '已完成',
                    'cancelled': '已取消'
                  };
                  return statusMap[detailSurvey.status] || '未知';
                })()}
              </Descriptions.Item>
              <Descriptions.Item label="开始日期">
                {detailSurvey.start_date ? dateTimeUtils.formatDateTime(detailSurvey.start_date) : '-'}
              </Descriptions.Item>
              <Descriptions.Item label="结束日期">
                {detailSurvey.end_date ? dateTimeUtils.formatDateTime(detailSurvey.end_date) : '-'}
              </Descriptions.Item>
              <Descriptions.Item label="目标门店" span={2}>
                <Space>
                  <ShopOutlined />
                  {detailSurvey.target_store_name}
                  {detailSurvey.target_store_distance && (
                    <Tag color="blue">{detailSurvey.target_store_distance}公里</Tag>
                  )}
                </Space>
              </Descriptions.Item>
              <Descriptions.Item label="目标门店地址" span={2}>
                {detailSurvey.target_store_address || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="评分">
                {detailSurvey.rating ? `${detailSurvey.rating}分` : '-'}
              </Descriptions.Item>
              <Descriptions.Item label="创建时间">
                {detailSurvey.created_at ? dateTimeUtils.formatDateTime(detailSurvey.created_at) : '-'}
              </Descriptions.Item>

              {/* 价格调研特有信息 */}
              {detailSurvey.survey_type === 'price' && (
                <>
                  <Descriptions.Item label="产品信息" span={2}>
                    <Card size="small" title="价格对比" style={{ marginBottom: 16 }}>
                      <Row gutter={16}>
                        <Col span={6}>
                          <Statistic
                            title="产品名称"
                            value={detailSurvey.product_data?.name || detailSurvey.product_name || '-'}
                            valueStyle={{ fontSize: 16 }}
                          />
                        </Col>
                        <Col span={6}>
                          <Statistic
                            title="规格/单位"
                            value={`${detailSurvey.product_data?.specification || detailSurvey.product_spec || '-'} / ${detailSurvey.product_data?.unit || '公斤'}`}
                            valueStyle={{ fontSize: 16 }}
                          />
                        </Col>
                        <Col span={6}>
                          <Statistic
                            title="我方售价"
                            value={detailSurvey.product_data?.price || detailSurvey.product_price || 0}
                            precision={2}
                            suffix="元"
                            valueStyle={{ fontSize: 16, color: '#1890ff' }}
                          />
                        </Col>
                        <Col span={6}>
                          <Statistic
                            title="竞对售价"
                            value={detailSurvey.product_data?.competitor_price || detailSurvey.competitor_price || 0}
                            precision={2}
                            suffix="元"
                            valueStyle={{ fontSize: 16, color: '#cf1322' }}
                          />
                        </Col>
                      </Row>
                      <Divider style={{ margin: '12px 0' }} />
                      <Row gutter={16}>
                        <Col span={12}>
                          <Statistic
                            title="价格差异"
                            value={detailSurvey.product_data?.price_difference || detailSurvey.price_difference || 0}
                            precision={2}
                            suffix="元"
                            valueStyle={{
                              fontSize: 16,
                              color: (detailSurvey.product_data?.price_difference || detailSurvey.price_difference || 0) > 0 ? '#cf1322' : '#52c41a'
                            }}
                          />
                        </Col>
                        <Col span={12}>
                          <Statistic
                            title="差异百分比"
                            value={detailSurvey.product_data?.price_difference_percentage || detailSurvey.price_difference_percentage || 0}
                            suffix="%"
                            valueStyle={{
                              fontSize: 16,
                              color: (detailSurvey.product_data?.price_difference_percentage || detailSurvey.price_difference_percentage || 0) > 0 ? '#cf1322' : '#52c41a'
                            }}
                          />
                        </Col>
                      </Row>
                      {(detailSurvey.product_data?.price_notes || detailSurvey.price_notes) && (
                        <>
                          <Divider style={{ margin: '12px 0' }} />
                          <Row>
                            <Col span={24}>
                              <div>
                                <strong>价格备注：</strong> {detailSurvey.product_data?.price_notes || detailSurvey.price_notes}
                              </div>
                            </Col>
                          </Row>
                        </>
                      )}
                    </Card>
                  </Descriptions.Item>
                </>
              )}

              {/* 促销活动特有信息 */}
              {detailSurvey.survey_type === 'promotion' && (
                <>
                  <Descriptions.Item label="促销活动" span={2}>
                    <Card size="small" title={detailSurvey.promotion_data?.promotion_name || detailSurvey.promotion_name || '促销活动详情'} style={{ marginBottom: 16 }}>
                      <Row gutter={16}>
                        <Col span={8}>
                          <Statistic
                            title="活动类型"
                            value={(() => {
                              const typeMap = {
                                'discount': '折扣',
                                'gift': '赠品',
                                'coupon': '优惠券',
                                'bundle': '套餐',
                                'other': '其他'
                              };
                              return typeMap[detailSurvey.promotion_data?.promotion_type || detailSurvey.promotion_type] || '未知';
                            })()}
                            valueStyle={{ fontSize: 16 }}
                          />
                        </Col>
                        <Col span={8}>
                          <Statistic
                            title="折扣力度"
                            value={detailSurvey.promotion_data?.promotion_discount || detailSurvey.promotion_discount || '-'}
                            suffix="%"
                            valueStyle={{ fontSize: 16, color: '#1890ff' }}
                          />
                        </Col>
                        <Col span={8}>
                          <Statistic
                            title="活动时间"
                            value={
                              (detailSurvey.promotion_data?.promotion_start_date || detailSurvey.promotion_start_date) ?
                              `${detailSurvey.promotion_data?.promotion_start_date || detailSurvey.promotion_start_date} 至 ${detailSurvey.promotion_data?.promotion_end_date || detailSurvey.promotion_end_date || '未定'}` :
                              (detailSurvey.promotion_period || '未设置')
                            }
                            valueStyle={{ fontSize: 14 }}
                          />
                        </Col>
                      </Row>
                      {(detailSurvey.promotion_data?.promotion_description || detailSurvey.promotion_description) && (
                        <>
                          <Divider style={{ margin: '12px 0' }} />
                          <div>
                            <strong>活动描述：</strong>
                            <Paragraph>{detailSurvey.promotion_data?.promotion_description || detailSurvey.promotion_description}</Paragraph>
                          </div>
                        </>
                      )}

                      {/* 显示其他促销活动相关字段 */}
                      {((detailSurvey.promotion_data?.target_customers || detailSurvey.target_customers) ||
                        (detailSurvey.promotion_data?.promotion_channels || detailSurvey.promotion_channels) ||
                        (detailSurvey.promotion_data?.promotion_results || detailSurvey.promotion_results)) && (
                        <>
                          <Divider style={{ margin: '12px 0' }} />
                          <Row gutter={[16, 16]}>
                            {(detailSurvey.promotion_data?.target_customers || detailSurvey.target_customers) && (
                              <Col span={24}>
                                <div>
                                  <strong>目标客户：</strong> {detailSurvey.promotion_data?.target_customers || detailSurvey.target_customers}
                                </div>
                              </Col>
                            )}
                            {(detailSurvey.promotion_data?.promotion_channels || detailSurvey.promotion_channels) && (
                              <Col span={24}>
                                <div>
                                  <strong>推广渠道：</strong> {detailSurvey.promotion_data?.promotion_channels || detailSurvey.promotion_channels}
                                </div>
                              </Col>
                            )}
                            {(detailSurvey.promotion_data?.promotion_results || detailSurvey.promotion_results) && (
                              <Col span={24}>
                                <div>
                                  <strong>活动效果：</strong> {detailSurvey.promotion_data?.promotion_results || detailSurvey.promotion_results}
                                </div>
                              </Col>
                            )}
                          </Row>
                        </>
                      )}
                    </Card>
                  </Descriptions.Item>
                </>
              )}

              {/* 产品调研特有信息 */}
              {detailSurvey.survey_type === 'product' && detailSurvey.survey_data && (
                <>
                  <Descriptions.Item label="产品调研" span={2}>
                    <Card size="small" title="产品调研信息" style={{ marginBottom: 16 }}>
                      {detailSurvey.survey_data.product_name && (
                        <Row style={{ marginBottom: 8 }}>
                          <Col span={6}><strong>产品名称：</strong></Col>
                          <Col span={18}>{detailSurvey.survey_data.product_name}</Col>
                        </Row>
                      )}
                      {detailSurvey.survey_data.product_category && (
                        <Row style={{ marginBottom: 8 }}>
                          <Col span={6}><strong>产品类别：</strong></Col>
                          <Col span={18}>{detailSurvey.survey_data.product_category}</Col>
                        </Row>
                      )}
                      {detailSurvey.survey_data.product_features && (
                        <Row style={{ marginBottom: 8 }}>
                          <Col span={6}><strong>产品特点：</strong></Col>
                          <Col span={18}>{detailSurvey.survey_data.product_features}</Col>
                        </Row>
                      )}
                      {detailSurvey.survey_data.product_price && (
                        <Row style={{ marginBottom: 8 }}>
                          <Col span={6}><strong>产品价格：</strong></Col>
                          <Col span={18}>{detailSurvey.survey_data.product_price}</Col>
                        </Row>
                      )}
                      {detailSurvey.survey_data.product_quality && (
                        <Row style={{ marginBottom: 8 }}>
                          <Col span={6}><strong>产品质量：</strong></Col>
                          <Col span={18}>{detailSurvey.survey_data.product_quality}</Col>
                        </Row>
                      )}
                      {detailSurvey.survey_data.product_packaging && (
                        <Row style={{ marginBottom: 8 }}>
                          <Col span={6}><strong>产品包装：</strong></Col>
                          <Col span={18}>{detailSurvey.survey_data.product_packaging}</Col>
                        </Row>
                      )}
                      {detailSurvey.survey_data.product_display && (
                        <Row style={{ marginBottom: 8 }}>
                          <Col span={6}><strong>产品陈列：</strong></Col>
                          <Col span={18}>{detailSurvey.survey_data.product_display}</Col>
                        </Row>
                      )}
                      {detailSurvey.survey_data.product_sales && (
                        <Row style={{ marginBottom: 8 }}>
                          <Col span={6}><strong>销售情况：</strong></Col>
                          <Col span={18}>{detailSurvey.survey_data.product_sales}</Col>
                        </Row>
                      )}
                      {detailSurvey.survey_data.product_notes && (
                        <Row style={{ marginBottom: 8 }}>
                          <Col span={6}><strong>备注：</strong></Col>
                          <Col span={18}>{detailSurvey.survey_data.product_notes}</Col>
                        </Row>
                      )}
                    </Card>
                  </Descriptions.Item>
                </>
              )}

              {/* 服务调研特有信息 */}
              {detailSurvey.survey_type === 'service' && detailSurvey.survey_data && (
                <>
                  <Descriptions.Item label="服务调研" span={2}>
                    <Card size="small" title="服务调研信息" style={{ marginBottom: 16 }}>
                      {detailSurvey.survey_data.service_type && (
                        <Row style={{ marginBottom: 8 }}>
                          <Col span={6}><strong>服务类型：</strong></Col>
                          <Col span={18}>{detailSurvey.survey_data.service_type}</Col>
                        </Row>
                      )}
                      {detailSurvey.survey_data.service_quality && (
                        <Row style={{ marginBottom: 8 }}>
                          <Col span={6}><strong>服务质量：</strong></Col>
                          <Col span={18}>{detailSurvey.survey_data.service_quality}</Col>
                        </Row>
                      )}
                      {detailSurvey.survey_data.service_efficiency && (
                        <Row style={{ marginBottom: 8 }}>
                          <Col span={6}><strong>服务效率：</strong></Col>
                          <Col span={18}>{detailSurvey.survey_data.service_efficiency}</Col>
                        </Row>
                      )}
                      {detailSurvey.survey_data.service_attitude && (
                        <Row style={{ marginBottom: 8 }}>
                          <Col span={6}><strong>服务态度：</strong></Col>
                          <Col span={18}>{detailSurvey.survey_data.service_attitude}</Col>
                        </Row>
                      )}
                      {detailSurvey.survey_data.customer_satisfaction && (
                        <Row style={{ marginBottom: 8 }}>
                          <Col span={6}><strong>顾客满意度：</strong></Col>
                          <Col span={18}>{detailSurvey.survey_data.customer_satisfaction}</Col>
                        </Row>
                      )}
                      {detailSurvey.survey_data.service_features && (
                        <Row style={{ marginBottom: 8 }}>
                          <Col span={6}><strong>服务特点：</strong></Col>
                          <Col span={18}>{detailSurvey.survey_data.service_features}</Col>
                        </Row>
                      )}
                      {detailSurvey.survey_data.service_notes && (
                        <Row style={{ marginBottom: 8 }}>
                          <Col span={6}><strong>备注：</strong></Col>
                          <Col span={18}>{detailSurvey.survey_data.service_notes}</Col>
                        </Row>
                      )}
                    </Card>
                  </Descriptions.Item>
                </>
              )}

              {/* 其他类型调研信息 */}
              {detailSurvey.survey_type === 'other' && detailSurvey.survey_data && Object.keys(detailSurvey.survey_data).length > 0 && (
                <>
                  <Descriptions.Item label="其他调研信息" span={2}>
                    <Card size="small" title="调研详情" style={{ marginBottom: 16 }}>
                      {Object.entries(detailSurvey.survey_data).map(([key, value]) => (
                        <Row key={key} style={{ marginBottom: 8 }}>
                          <Col span={6}><strong>{key}：</strong></Col>
                          <Col span={18}>{typeof value === 'object' ? JSON.stringify(value) : value}</Col>
                        </Row>
                      ))}
                    </Card>
                  </Descriptions.Item>
                </>
              )}

              {/* 调研图片 */}
              {detailSurvey.attachments && detailSurvey.attachments.length > 0 && (
                <Descriptions.Item label="调研图片" span={2}>
                  <Image.PreviewGroup>
                    <Row gutter={[16, 16]}>
                      {detailSurvey.attachments.map((url, index) => (
                        <Col span={6} key={index}>
                          <Image
                            src={url}
                            alt={`调研图片 ${index + 1}`}
                            style={{ width: '100%', height: 120, objectFit: 'cover' }}
                          />
                        </Col>
                      ))}
                    </Row>
                  </Image.PreviewGroup>
                </Descriptions.Item>
              )}

              <Descriptions.Item label="调研描述" span={2}>
                {detailSurvey.description || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="调研结论" span={2}>
                {detailSurvey.conclusion || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="调研建议" span={2}>
                {detailSurvey.recommendations || '-'}
              </Descriptions.Item>
            </Descriptions>
          </TabPane>
          <TabPane tab="AI分析" key="2">
            {detailSurvey.is_ai_analyzed ? (
              <div>
                <Alert
                  message="AI分析结果"
                  description="以下是基于调研数据的AI分析结果，仅供参考。"
                  type="info"
                  showIcon
                  style={{ marginBottom: 16 }}
                />

                <Card title="分析摘要">
                  <Paragraph>{detailSurvey.ai_analysis?.summary || '暂无分析摘要'}</Paragraph>
                </Card>

                <Divider />

                <Card title="洞察">
                  {detailSurvey.ai_analysis?.insights?.length > 0 ? (
                    <ul>
                      {detailSurvey.ai_analysis.insights.map((insight, index) => (
                        <li key={index}>{insight}</li>
                      ))}
                    </ul>
                  ) : (
                    <Empty description="暂无洞察" />
                  )}
                </Card>

                <Divider />

                <Card title="建议">
                  {detailSurvey.ai_analysis?.recommendations?.length > 0 ? (
                    <ul>
                      {detailSurvey.ai_analysis.recommendations.map((rec, index) => (
                        <li key={index}>{rec}</li>
                      ))}
                    </ul>
                  ) : (
                    <Empty description="暂无建议" />
                  )}
                </Card>
              </div>
            ) : (
              <Empty
                description={
                  <span>
                    尚未进行AI分析
                    <br />
                    <Button
                      type="primary"
                      icon={<RobotOutlined />}
                      onClick={() => handleAiAnalysis(detailSurvey.id)}
                      disabled={detailSurvey.status !== 'completed'}
                      loading={aiAnalyzing}
                      style={{ marginTop: 16 }}
                    >
                      开始AI分析
                    </Button>
                  </span>
                }
              />
            )}
          </TabPane>
        </Tabs>
      </Drawer>
    );
  };

  return (
    <div className="area-survey-page">
      <Card title="周边调研">
        <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Space>
            <span>选择门店：</span>
            <Select
              placeholder="请选择门店"
              style={{ width: 200 }}
              value={currentStoreId}
              onChange={handleStoreChange}
              loading={loading && stores.length === 0}
              disabled={loading && stores.length === 0}
            >
              {stores.map(store => (
                <Select.Option key={store.id} value={store.id}>{store.name}</Select.Option>
              ))}
            </Select>

            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleCreate}
              disabled={!currentStoreId}
            >
              新建调研
            </Button>
          </Space>

          <Button
            icon={<ReloadOutlined />}
            onClick={() => {
              fetchSurveys();
              fetchStats();
            }}
          >
            刷新
          </Button>
        </div>

        {!currentStoreId && (
          <Alert
            message="请先选择门店"
            description="请从上方下拉菜单中选择一个门店，以查看或创建周边调研。"
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />
        )}

        {currentStoreId && stats.total > 0 && renderStatCards()}

        {currentStoreId && renderFilters()}

        {currentStoreId && (
          <Table
            columns={columns}
            dataSource={surveys}
            rowKey="id"
            loading={loading}
            pagination={pagination}
            onChange={handleTableChange}
            locale={{ emptyText: '暂无数据' }}
          />
        )}
      </Card>

      <Modal
        title={modalTitle}
        open={modalVisible}
        onOk={handleSubmit}
        onCancel={() => setModalVisible(false)}
        confirmLoading={loading}
        width={700}
      >
        {renderForm()}
      </Modal>

      {renderDetailDrawer()}
    </div>
  );
};

export default AreaSurvey;