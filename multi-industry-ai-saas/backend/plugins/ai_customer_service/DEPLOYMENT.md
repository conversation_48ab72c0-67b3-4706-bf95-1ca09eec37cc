# AI 智能体客服插件部署指南

## 🚀 快速部署

### 1. 后端部署

#### 1.1 修复数据库字段冲突
```bash
# 已修复 metadata 字段冲突，改为 meta_data
# 无需额外操作
```

#### 1.2 注册插件到市场
```bash
cd /app
python plugins/ai_customer_service/register_plugin.py register
```

#### 1.3 检查插件状态
```bash
python plugins/ai_customer_service/register_plugin.py status
```

#### 1.4 运行测试
```bash
python test_ai_customer_service.py
```

### 2. 前端部署

#### 2.1 确认路由配置
前端路由已自动配置：
- 主路由：`/project/plugins/ai_customer_service`
- 插件市场已包含该插件

#### 2.2 重启前端服务
```bash
# 开发环境
npm start

# 生产环境
npm run build
```

## 🔧 配置说明

### 1. 环境变量配置

```bash
# AI 模型配置
AI_MODEL_PROVIDER=openai
AI_MODEL_NAME=gpt-4
AI_API_KEY=your_api_key

# 微信公众号配置
WECHAT_MP_APP_ID=your_app_id
WECHAT_MP_APP_SECRET=your_app_secret
WECHAT_MP_TOKEN=your_token
WECHAT_MP_ENCODING_AES_KEY=your_aes_key

# 企业微信配置
WECHAT_WORK_CORP_ID=your_corp_id
WECHAT_WORK_CORP_SECRET=your_corp_secret
WECHAT_WORK_AGENT_ID=your_agent_id

# 钉钉配置
DINGTALK_APP_KEY=your_app_key
DINGTALK_APP_SECRET=your_app_secret

# 飞书配置
FEISHU_APP_ID=your_app_id
FEISHU_APP_SECRET=your_app_secret
```

### 2. 数据库迁移

```bash
# 运行数据库迁移
python -c "
import asyncio
from plugins.ai_customer_service.migrations import run_migrations
asyncio.run(run_migrations('1.0.0'))
"
```

### 3. 插件初始化

```python
# 在应用启动时初始化插件
from plugins.ai_customer_service import initialize_plugin

await initialize_plugin(tenant_id="your_tenant", project_id="your_project")
```

## 📋 功能验证

### 1. 基础功能测试

```bash
# 运行完整测试套件
python test_ai_customer_service.py
```

### 2. API 接口测试

```bash
# 测试会话管理
curl -X GET "http://localhost:8000/api/v1/tenant/your_tenant/project/your_project/plugin/ai-customer-service/sessions"

# 测试平台管理
curl -X GET "http://localhost:8000/api/v1/tenant/your_tenant/project/your_project/plugin/ai-customer-service/platforms"

# 测试公共组件
curl -X GET "http://localhost:8000/api/v1/public/ai-customer-service/widget/test_widget"
```

### 3. 前端功能测试

1. 访问插件市场：`/project/plugins/plugin-market`
2. 找到"AI 智能体 - 超级全能客服"插件
3. 点击进入插件页面：`/project/plugins/ai_customer_service`
4. 测试各个功能模块

## 🔗 Webhook 配置

### 1. 微信公众号 Webhook

```
URL: https://your-domain.com/api/v1/webhooks/ai-customer-service/wechat-mp/webhook
Token: 在平台管理中配置
```

### 2. 企业微信 Webhook

```
URL: https://your-domain.com/api/v1/webhooks/ai-customer-service/wechat-work/webhook
```

### 3. 钉钉 Webhook

```
URL: https://your-domain.com/api/v1/webhooks/ai-customer-service/dingtalk/webhook
```

## 🌐 公共组件集成

### 1. 获取嵌入代码

访问插件页面 → 公共组件 → 嵌入代码，复制生成的代码。

### 2. 网站集成

```html
<!-- 将以下代码添加到网站页面 -->
<script>
(function() {
    var config = {
        widgetId: 'your_widget_id',
        theme: 'light',
        size: 'medium',
        position: 'bottom-right',
        apiBaseUrl: 'https://your-domain.com'
    };
    
    var script = document.createElement('script');
    script.src = 'https://your-domain.com/static/ai-customer-service/widget.js';
    script.onload = function() {
        if (window.AICustomerService) {
            window.AICustomerService.init(config);
        }
    };
    document.head.appendChild(script);
    
    var link = document.createElement('link');
    link.rel = 'stylesheet';
    link.href = 'https://your-domain.com/static/ai-customer-service/widget.css';
    document.head.appendChild(link);
})();
</script>
```

## 🔍 故障排除

### 1. 常见问题

#### 问题：插件无法加载
```bash
# 检查插件状态
python plugins/ai_customer_service/register_plugin.py status

# 重新注册插件
python plugins/ai_customer_service/register_plugin.py register
```

#### 问题：数据库错误
```bash
# 检查数据库连接
python -c "from db.database import get_async_engine; print('数据库连接正常')"

# 运行迁移
python -c "
import asyncio
from plugins.ai_customer_service.migrations import run_migrations
asyncio.run(run_migrations('1.0.0'))
"
```

#### 问题：前端路由404
```bash
# 检查路由配置
grep -r "ai_customer_service" frontend/src/

# 重启前端服务
npm start
```

### 2. 日志查看

```bash
# 查看应用日志
tail -f logs/app.log | grep "ai_customer_service"

# 查看插件特定日志
tail -f logs/plugins/ai_customer_service.log
```

### 3. 性能监控

```bash
# 检查插件健康状态
curl -X GET "http://localhost:8000/api/v1/tenant/your_tenant/project/your_project/plugin/ai-customer-service/health"

# 查看学习优化状态
curl -X GET "http://localhost:8000/api/v1/tenant/your_tenant/project/your_project/plugin/ai-customer-service/learning/analysis"
```

## 📈 监控和维护

### 1. 性能指标

- 响应时间：< 3秒
- 准确率：> 80%
- 客户满意度：> 4.0
- 系统可用性：> 99%

### 2. 定期维护

```bash
# 每日健康检查
python plugins/ai_customer_service/register_plugin.py status

# 每周学习优化
curl -X POST "http://localhost:8000/api/v1/tenant/your_tenant/project/your_project/plugin/ai-customer-service/learning/optimize"

# 每月数据清理
python -c "
import asyncio
from plugins.ai_customer_service.core.analytics import cleanup_old_data
asyncio.run(cleanup_old_data(days=90))
"
```

### 3. 备份策略

```bash
# 备份插件配置
pg_dump -t customer_service_* your_database > ai_customer_service_backup.sql

# 备份知识库
cp -r data/knowledge_base/ backup/knowledge_base_$(date +%Y%m%d)/
```

## 🎯 最佳实践

1. **定期更新知识库**：保持知识库内容的时效性
2. **监控学习效果**：定期查看学习优化报告
3. **优化业务流转**：根据实际业务调整流转规则
4. **客户反馈收集**：积极收集和处理客户反馈
5. **安全防护**：定期更新API密钥和访问令牌

## 📞 技术支持

如遇到问题，请：

1. 查看本部署指南
2. 运行测试脚本诊断
3. 查看应用日志
4. 联系技术支持团队

---

**部署完成后，您将拥有一个功能完整的AI智能体客服系统！** 🎉
