import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Card,
  Spin,
  Button,
  Descriptions,
  Tag,
  Typography,
  message,
  Space,
  Divider,
  Row,
  Col,
  Timeline,
  Avatar,
  Empty
} from 'antd';
import {
  ArrowLeftOutlined,
  EditOutlined,
  CheckOutlined,
  CloseOutlined,
  UserOutlined,
  CalendarOutlined,
  FlagOutlined
} from '@ant-design/icons';
import dayjs from 'dayjs';
import { api } from '../../../services/api';
import { useProject } from '../../../contexts/ProjectContext';

const { Title, Text, Paragraph } = Typography;

/**
 * 任务详情页面组件
 */
const TaskDetail = () => {
  const { taskId } = useParams();
  const navigate = useNavigate();
  const { currentProject } = useProject();
  
  const [loading, setLoading] = useState(true);
  const [task, setTask] = useState(null);
  const [updating, setUpdating] = useState(false);

  // 任务状态文本映射
  const statusTexts = {
    pending: '待处理',
    in_progress: '进行中',
    completed: '已完成',
    overdue: '已逾期',
    cancelled: '已取消'
  };

  // 任务状态颜色映射
  const statusColors = {
    pending: 'default',
    in_progress: 'processing',
    completed: 'success',
    overdue: 'error',
    cancelled: 'warning'
  };

  // 任务优先级文本映射
  const priorityTexts = {
    low: '低',
    medium: '中',
    high: '高',
    urgent: '紧急'
  };

  // 任务优先级颜色映射
  const priorityColors = {
    low: 'green',
    medium: 'blue',
    high: 'orange',
    urgent: 'red'
  };

  // 任务类别文本映射
  const categoryTexts = {
    store: '门店',
    operation: '运营',
    marketing: '营销',
    inventory: '库存',
    finance: '财务',
    other: '其他'
  };

  // 任务类型映射
  const taskTypeTexts = {
    planning: '规划任务',
    implementation: '实施任务',
    review: '审核任务',
    coordination: '协调任务',
    reporting: '报告任务',
    daily: '日常任务',
    weekly: '周期任务',
    monthly: '月度任务',
    other: '其他任务'
  };

  // 获取任务详情
  const fetchTaskDetail = async () => {
    if (!currentProject?.id || !taskId) return;
    
    setLoading(true);
    try {
      const response = await api.get(`/project/${currentProject.id}/tasks/${taskId}`);
      if (response.data) {
        setTask(response.data);
      }
    } catch (error) {
      console.error('获取任务详情失败:', error);
      message.error('获取任务详情失败');
      // 如果任务不存在，返回任务列表
      navigate('/project/task');
    } finally {
      setLoading(false);
    }
  };

  // 更新任务状态
  const updateTaskStatus = async (status) => {
    setUpdating(true);
    try {
      await api.patch(`/project/${currentProject.id}/tasks/${taskId}/status`, { status });
      message.success('任务状态更新成功');
      fetchTaskDetail(); // 重新获取任务详情
    } catch (error) {
      console.error('更新任务状态失败:', error);
      message.error('更新任务状态失败');
    } finally {
      setUpdating(false);
    }
  };

  useEffect(() => {
    fetchTaskDetail();
  }, [currentProject?.id, taskId]);

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px 0' }}>
        <Spin size="large" />
      </div>
    );
  }

  if (!task) {
    return (
      <div style={{ textAlign: 'center', padding: '50px 0' }}>
        <Empty description="任务不存在" />
        <Button type="primary" onClick={() => navigate('/project/task')}>
          返回任务列表
        </Button>
      </div>
    );
  }

  return (
    <div style={{ padding: '24px' }}>
      {/* 页面头部 */}
      <div style={{ marginBottom: '24px' }}>
        <Space>
          <Button 
            icon={<ArrowLeftOutlined />} 
            onClick={() => navigate('/project/task')}
          >
            返回任务列表
          </Button>
          <Title level={3} style={{ margin: 0 }}>
            任务详情
          </Title>
        </Space>
      </div>

      <Row gutter={[24, 24]}>
        {/* 左侧：任务基本信息 */}
        <Col xs={24} lg={16}>
          <Card title="任务信息" extra={
            <Space>
              <Button 
                icon={<EditOutlined />}
                onClick={() => navigate(`/project/task?edit=${taskId}`)}
              >
                编辑任务
              </Button>
              {task.status === 'pending' && (
                <Button 
                  type="primary"
                  icon={<CheckOutlined />}
                  loading={updating}
                  onClick={() => updateTaskStatus('in_progress')}
                >
                  开始任务
                </Button>
              )}
              {task.status === 'in_progress' && (
                <Button 
                  type="primary"
                  icon={<CheckOutlined />}
                  loading={updating}
                  onClick={() => updateTaskStatus('completed')}
                >
                  完成任务
                </Button>
              )}
              {['pending', 'in_progress'].includes(task.status) && (
                <Button 
                  danger
                  icon={<CloseOutlined />}
                  loading={updating}
                  onClick={() => updateTaskStatus('cancelled')}
                >
                  取消任务
                </Button>
              )}
            </Space>
          }>
            <Descriptions column={2} bordered>
              <Descriptions.Item label="任务标题" span={2}>
                <Text strong style={{ fontSize: '16px' }}>{task.title}</Text>
              </Descriptions.Item>
              
              <Descriptions.Item label="任务状态">
                <Tag color={statusColors[task.status]}>
                  {statusTexts[task.status] || task.status}
                </Tag>
              </Descriptions.Item>
              
              <Descriptions.Item label="优先级">
                <Tag color={priorityColors[task.priority]}>
                  <FlagOutlined /> {priorityTexts[task.priority] || task.priority}
                </Tag>
              </Descriptions.Item>
              
              <Descriptions.Item label="任务类别">
                <Tag>{categoryTexts[task.category] || task.category}</Tag>
              </Descriptions.Item>
              
              <Descriptions.Item label="任务类型">
                <Tag>{taskTypeTexts[task.task_type] || task.task_type}</Tag>
              </Descriptions.Item>
              
              <Descriptions.Item label="创建时间">
                <Space>
                  <CalendarOutlined />
                  {dayjs(task.created_at).format('YYYY-MM-DD HH:mm:ss')}
                </Space>
              </Descriptions.Item>
              
              <Descriptions.Item label="截止时间">
                <Space>
                  <CalendarOutlined />
                  {task.due_date ? dayjs(task.due_date).format('YYYY-MM-DD HH:mm:ss') : '无'}
                </Space>
              </Descriptions.Item>
              
              <Descriptions.Item label="创建者">
                <Space>
                  <Avatar size="small" icon={<UserOutlined />} />
                  {task.creator_name || '未知'}
                </Space>
              </Descriptions.Item>
              
              <Descriptions.Item label="负责人">
                <Space>
                  <Avatar size="small" icon={<UserOutlined />} />
                  {task.assignee_name || '未分配'}
                </Space>
              </Descriptions.Item>
              
              {task.description && (
                <Descriptions.Item label="任务描述" span={2}>
                  <Paragraph style={{ marginBottom: 0 }}>
                    {task.description}
                  </Paragraph>
                </Descriptions.Item>
              )}
              
              {task.notes && (
                <Descriptions.Item label="备注" span={2}>
                  <Paragraph style={{ marginBottom: 0 }}>
                    {task.notes}
                  </Paragraph>
                </Descriptions.Item>
              )}
            </Descriptions>
          </Card>
        </Col>

        {/* 右侧：任务进度和操作历史 */}
        <Col xs={24} lg={8}>
          <Card title="任务进度" style={{ marginBottom: '24px' }}>
            <div style={{ textAlign: 'center' }}>
              <div style={{ fontSize: '24px', fontWeight: 'bold', marginBottom: '8px' }}>
                {task.progress || 0}%
              </div>
              <div style={{ color: '#666', marginBottom: '16px' }}>
                任务完成度
              </div>
              <div style={{ width: '100%', height: '8px', backgroundColor: '#f0f0f0', borderRadius: '4px' }}>
                <div 
                  style={{ 
                    width: `${task.progress || 0}%`, 
                    height: '100%', 
                    backgroundColor: '#1890ff', 
                    borderRadius: '4px',
                    transition: 'width 0.3s ease'
                  }} 
                />
              </div>
            </div>
          </Card>

          <Card title="操作历史">
            <Timeline>
              <Timeline.Item color="blue">
                <div>
                  <Text strong>任务创建</Text>
                  <br />
                  <Text type="secondary">
                    {dayjs(task.created_at).format('YYYY-MM-DD HH:mm:ss')}
                  </Text>
                </div>
              </Timeline.Item>
              
              {task.started_at && (
                <Timeline.Item color="orange">
                  <div>
                    <Text strong>任务开始</Text>
                    <br />
                    <Text type="secondary">
                      {dayjs(task.started_at).format('YYYY-MM-DD HH:mm:ss')}
                    </Text>
                  </div>
                </Timeline.Item>
              )}
              
              {task.completed_at && (
                <Timeline.Item color="green">
                  <div>
                    <Text strong>任务完成</Text>
                    <br />
                    <Text type="secondary">
                      {dayjs(task.completed_at).format('YYYY-MM-DD HH:mm:ss')}
                    </Text>
                  </div>
                </Timeline.Item>
              )}
              
              {task.cancelled_at && (
                <Timeline.Item color="red">
                  <div>
                    <Text strong>任务取消</Text>
                    <br />
                    <Text type="secondary">
                      {dayjs(task.cancelled_at).format('YYYY-MM-DD HH:mm:ss')}
                    </Text>
                  </div>
                </Timeline.Item>
              )}
            </Timeline>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default TaskDetail; 