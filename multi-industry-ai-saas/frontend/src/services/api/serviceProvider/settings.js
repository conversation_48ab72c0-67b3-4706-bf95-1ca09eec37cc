import api from '../httpClient';
import { getServiceProviderId } from '../httpClient';

/**
 * 服务商-设置API
 */
const settings = {
  /**
   * 获取服务商设置
   * @returns {Promise} - 服务商设置
   */
  getSettings: async () => {
    const serviceProviderId = getServiceProviderId();
    if (!serviceProviderId) {
      throw new Error('未指定服务商ID');
    }
    return api.get('/service-provider/settings');
  },

  /**
   * 更新服务商设置
   * @param {Object} data - 设置数据
   * @returns {Promise} - 更新结果
   */
  updateSettings: async (data) => {
    const serviceProviderId = getServiceProviderId();
    if (!serviceProviderId) {
      throw new Error('未指定服务商ID');
    }
    return api.put('/service-provider/settings', data);
  },

  /**
   * 获取服务商品牌设置
   * @returns {Promise} - 品牌设置
   */
  getBrandSettings: async () => {
    const serviceProviderId = getServiceProviderId();
    if (!serviceProviderId) {
      throw new Error('未指定服务商ID');
    }
    return api.get('/service-provider/settings/brand');
  },

  /**
   * 更新服务商品牌设置
   * @param {Object} data - 品牌设置数据
   * @returns {Promise} - 更新结果
   */
  updateBrandSettings: async (data) => {
    const serviceProviderId = getServiceProviderId();
    if (!serviceProviderId) {
      throw new Error('未指定服务商ID');
    }
    return api.put('/service-provider/settings/brand', data);
  },

  /**
   * 获取服务商通知设置
   * @returns {Promise} - 通知设置
   */
  getNotificationSettings: async () => {
    const serviceProviderId = getServiceProviderId();
    if (!serviceProviderId) {
      throw new Error('未指定服务商ID');
    }
    return api.get('/service-provider/settings/notification');
  },

  /**
   * 更新服务商通知设置
   * @param {Object} data - 通知设置数据
   * @returns {Promise} - 更新结果
   */
  updateNotificationSettings: async (data) => {
    const serviceProviderId = getServiceProviderId();
    if (!serviceProviderId) {
      throw new Error('未指定服务商ID');
    }
    return api.put('/service-provider/settings/notification', data);
  },

  /**
   * 获取服务商安全设置
   * @returns {Promise} - 安全设置
   */
  getSecuritySettings: async () => {
    const serviceProviderId = getServiceProviderId();
    if (!serviceProviderId) {
      throw new Error('未指定服务商ID');
    }
    return api.get('/service-provider/settings/security');
  },

  /**
   * 更新服务商安全设置
   * @param {Object} data - 安全设置数据
   * @returns {Promise} - 更新结果
   */
  updateSecuritySettings: async (data) => {
    const serviceProviderId = getServiceProviderId();
    if (!serviceProviderId) {
      throw new Error('未指定服务商ID');
    }
    return api.put('/service-provider/settings/security', data);
  }
};

export default settings;
