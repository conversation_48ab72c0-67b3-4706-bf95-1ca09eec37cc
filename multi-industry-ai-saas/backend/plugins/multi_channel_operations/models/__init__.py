#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
全渠道运营助手插件模型
"""

from .channel_config import MultiChannelConfig
from .product_mapping import (
    MultiChannelProductMapping,
    MultiChannelBatchOperation,
    ProductImageGeneration
)
from .competitor import (
    CompetitorProduct,
    CompetitorPriceHistory,
    CompetitorAlert,
    CompetitorAnalysis
)
from .automation import (
    AutomationRule,
    AutomationExecution,
    AutomationNode,
    AutomationTemplate,
    ExternalIntegration
)
from .operation_log import (
    MultiChannelOperationLog,
    AIAgentTask,
    NotificationLog,
    SystemHealthCheck
)

__all__ = [
    # 渠道配置
    "MultiChannelConfig",

    # 商品映射
    "MultiChannelProductMapping",
    "MultiChannelBatchOperation",
    "ProductImageGeneration",

    # 竞品监控
    "CompetitorProduct",
    "CompetitorPriceHistory",
    "CompetitorAlert",
    "CompetitorAnalysis",

    # 自动化规则
    "AutomationRule",
    "AutomationExecution",
    "AutomationNode",
    "AutomationTemplate",
    "ExternalIntegration",

    # 操作日志
    "MultiChannelOperationLog",
    "AIAgentTask",
    "NotificationLog",
    "SystemHealthCheck"
]
