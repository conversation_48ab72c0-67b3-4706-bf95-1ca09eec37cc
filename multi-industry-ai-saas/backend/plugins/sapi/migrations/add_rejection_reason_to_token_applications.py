#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
为 sapi_token_applications 表添加 rejection_reason 列的迁移脚本
"""

import logging
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession

logger = logging.getLogger(__name__)

async def migrate(db: AsyncSession) -> bool:
    """
    为 sapi_token_applications 表添加 rejection_reason 列
    
    Args:
        db: 数据库会话
        
    Returns:
        bool: 迁移是否成功
    """
    try:
        # 检查表是否存在
        result = await db.execute(text("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables
                WHERE table_name = 'sapi_token_applications'
            );
        """))
        
        table_exists = result.scalar()
        
        if not table_exists:
            logger.warning("sapi_token_applications 表不存在，无法添加 rejection_reason 列")
            return True
            
        # 检查 rejection_reason 列是否存在
        result = await db.execute(text("""
            SELECT EXISTS (
                SELECT FROM information_schema.columns
                WHERE table_name = 'sapi_token_applications' AND column_name = 'rejection_reason'
            );
        """))
        
        column_exists = result.scalar()
        
        if not column_exists:
            # 添加 rejection_reason 列
            await db.execute(text("""
                ALTER TABLE sapi_token_applications
                ADD COLUMN rejection_reason TEXT;
            """))
            
            await db.commit()
            logger.info("成功为 sapi_token_applications 表添加 rejection_reason 列")
        else:
            logger.info("sapi_token_applications 表已有 rejection_reason 列，无需添加")
            
        return True
            
    except Exception as e:
        await db.rollback()
        logger.error(f"为 sapi_token_applications 表添加 rejection_reason 列失败: {str(e)}")
        raise
