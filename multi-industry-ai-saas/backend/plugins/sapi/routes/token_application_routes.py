#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
令牌申请路由

处理API令牌申请的创建、查询、审批等操作
"""

import uuid
import logging
import secrets
import string
from datetime import datetime, timedelta, timezone
from typing import Dict, Any, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Body
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from db.database import get_db
from models.user import User
from core.auth import get_current_user

from ..models import SAPITokenApplication, SAPIToken
from .. import router

logger = logging.getLogger(__name__)

# 导入JWT令牌生成函数
from ..auth import create_jwt_token, TokenType, TokenPermission

# 定义生成令牌的函数（已弃用，使用JWT令牌）
def generate_token(length=32):
    """
    生成随机令牌（已弃用，使用JWT令牌）

    Args:
        length: 令牌长度，默认为32

    Returns:
        str: 生成的令牌
    """
    alphabet = string.ascii_letters + string.digits
    token = ''.join(secrets.choice(alphabet) for _ in range(length))
    return token

# 获取令牌申请列表
@router.get("/token-applications", response_model=Dict[str, Any])
async def get_token_applications(
    status: Optional[str] = None,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """获取令牌申请列表"""
    try:
        # 构建查询
        query = select(SAPITokenApplication)

        # 如果不是管理员，只能查看自己的申请
        if current_user.role != "admin" and current_user.role != "project_admin":
            query = query.where(SAPITokenApplication.user_id == current_user.id)

        # 应用过滤条件
        if status:
            query = query.where(SAPITokenApplication.status == status)

        # 执行查询
        result = await db.execute(query)
        applications = result.scalars().all()

        # 构建响应
        result = []
        for application in applications:
            # 获取用户信息
            user_result = await db.execute(select(User).where(User.id == application.user_id))
            user = user_result.scalars().first()

            result.append({
                "id": str(application.id),
                "name": application.name,
                "description": application.description,
                "purpose": application.purpose,
                "requested_permissions": application.requested_permissions,
                "status": application.status,
                "created_at": application.created_at.isoformat() if application.created_at else None,
                "updated_at": application.updated_at.isoformat() if application.updated_at else None,
                "user": {
                    "id": str(user.id),
                    "username": user.username,
                    "email": user.email
                } if user else None
            })

        return {
            "success": True,
            "message": "获取令牌申请列表成功",
            "data": result
        }
    except Exception as e:
        logger.error(f"获取令牌申请列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取令牌申请列表失败: {str(e)}")

# 获取令牌申请详情
@router.get("/token-applications/{application_id}", response_model=Dict[str, Any])
async def get_token_application(
    application_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """获取令牌申请详情"""
    try:
        # 查询申请
        result = await db.execute(select(SAPITokenApplication).where(SAPITokenApplication.id == application_id))
        application = result.scalars().first()
        if not application:
            raise HTTPException(status_code=404, detail="令牌申请不存在")

        # 如果不是管理员，只能查看自己的申请
        if current_user.role != "admin" and current_user.role != "project_admin" and application.user_id != current_user.id:
            raise HTTPException(status_code=403, detail="无权查看此令牌申请")

        # 获取用户信息
        user_result = await db.execute(select(User).where(User.id == application.user_id))
        user = user_result.scalars().first()

        # 构建响应
        result = {
            "id": str(application.id),
            "name": application.name,
            "description": application.description,
            "purpose": application.purpose,
            "requested_permissions": application.requested_permissions,
            "status": application.status,
            "created_at": application.created_at.isoformat() if application.created_at else None,
            "updated_at": application.updated_at.isoformat() if application.updated_at else None,
            "token": application.token,
            "project_id": str(application.project_id) if application.project_id else None,
            "server_permissions": application.server_permissions,
            "default_permissions": application.default_permissions,
            "user": {
                "id": str(user.id),
                "username": user.username,
                "email": user.email
            } if user else None
        }

        return {
            "success": True,
            "message": "获取令牌申请详情成功",
            "data": result
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取令牌申请详情失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取令牌申请详情失败: {str(e)}")

# 创建令牌申请
@router.post("/token-applications", response_model=Dict[str, Any])
async def create_token_application(
    application_data: dict = Body(...),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """创建令牌申请"""
    try:
        # 验证权限
        requested_permissions = application_data.get("requested_permissions", "read")
        if not TokenPermission.validate_permission(requested_permissions):
            raise HTTPException(status_code=400, detail=f"无效的权限: {requested_permissions}")

        # 验证服务器权限映射
        server_permissions = application_data.get("server_permissions", {})
        for server_id, perms in server_permissions.items():
            for perm in perms:
                if not TokenPermission.validate_permission(perm):
                    raise HTTPException(status_code=400, detail=f"无效的权限: {perm}")

        # 验证默认权限
        default_permissions = application_data.get("default_permissions", [requested_permissions])
        for perm in default_permissions:
            if not TokenPermission.validate_permission(perm):
                raise HTTPException(status_code=400, detail=f"无效的权限: {perm}")

        # 验证令牌类型
        token_type = application_data.get("token_type", TokenType.MCP)
        if token_type not in [TokenType.ACCESS, TokenType.MCP]:
            raise HTTPException(status_code=400, detail=f"无效的令牌类型: {token_type}")

        # 创建申请
        new_application = SAPITokenApplication(
            id=uuid.uuid4(),
            name=application_data.get("name"),
            description=application_data.get("description"),
            purpose=application_data.get("purpose"),
            requested_permissions=requested_permissions,
            server_permissions=server_permissions,
            default_permissions=default_permissions,
            token_type=token_type,
            status="pending",
            user_id=current_user.id,
            project_id=application_data.get("project_id"),
            created_at=datetime.now(timezone.utc).replace(tzinfo=None)
        )

        db.add(new_application)
        await db.commit()
        await db.refresh(new_application)

        return {
            "success": True,
            "message": "令牌申请创建成功",
            "data": {
                "id": str(new_application.id),
                "name": new_application.name,
                "status": new_application.status,
                "token_type": new_application.token_type
            }
        }
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"创建令牌申请失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"创建令牌申请失败: {str(e)}")

# 审批令牌申请
@router.post("/token-applications/{application_id}/approve", response_model=Dict[str, Any])
async def approve_token_application(
    application_id: str,
    approval_data: dict = Body(...),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """审批令牌申请"""
    try:
        # 检查权限
        if not (current_user.role == "admin" or current_user.role == "project_admin" or current_user.is_tenant_admin or current_user.is_system_admin):
            raise HTTPException(status_code=403, detail="无权审批令牌申请")

        # 查询申请
        result = await db.execute(select(SAPITokenApplication).where(SAPITokenApplication.id == application_id))
        application = result.scalars().first()
        if not application:
            raise HTTPException(status_code=404, detail="令牌申请不存在")

        # 检查申请状态
        if application.status != "pending":
            raise HTTPException(status_code=400, detail="令牌申请已处理")

        # 获取审批状态
        status = approval_data.get("status")
        if status not in ["approved", "rejected"]:
            raise HTTPException(status_code=400, detail="无效的审批状态")

        # 更新申请状态
        application.status = status
        application.updated_at = datetime.now(timezone.utc).replace(tzinfo=None)

        # 如果批准，创建令牌
        if status == "approved":
            # 获取过期时间
            expires_hours = approval_data.get("expires_hours", 24 * 30)  # 默认30天
            expires_at = datetime.now(timezone.utc).replace(tzinfo=None) + timedelta(hours=expires_hours)

            # 获取项目ID
            project_id_from_app = application.project_id
            # 如果业务逻辑要求项目ID必须存在，这里的检查是合理的
            # 如果允许项目ID为空的令牌，则需要调整此逻辑
            if not project_id_from_app:
                # 根据您提供的令牌详情，project_id可能是null，所以我们暂时允许它为None
                # raise HTTPException(status_code=400, detail="缺少项目ID")
                pass # 允许 project_id_from_app 为 None

            # 获取令牌类型
            token_type = application.token_type or TokenType.MCP

            # 获取服务器权限映射 - 直接使用申请中的值，不做 or {}
            server_permissions_for_record = application.server_permissions

            # 获取默认权限 - 直接使用申请中的值，不做 or [application.requested_permissions]
            default_permissions_for_record = application.default_permissions

            # 为JWT准备权限。如果记录为None，JWT中这些声明可能不存在或为null
            # create_jwt_token需要能处理None值
            jwt_server_permissions = server_permissions_for_record
            jwt_default_permissions = default_permissions_for_record
            # 如果申请中的 default_permissions 为 None，但JWT业务上要求此时填充 requested_permissions，
            # 则可以在这里专门为JWT准备：
            # jwt_default_permissions = application.default_permissions if application.default_permissions is not None else [application.requested_permissions]
            # 但为了与数据库记录保持一致性（如果源为None则都为None），我们暂时保持直接传递

            # 生成JWT令牌
            token_id = str(uuid.uuid4())
            token_value = create_jwt_token(
                subject=str(application.user_id),
                token_type=token_type,
                expires_delta=timedelta(hours=expires_hours),
                scopes=["mcp"], # 默认给mcp范围，具体工具权限由下面两个字段控制
                project_id=str(project_id_from_app) if project_id_from_app else None,
                server_permissions=jwt_server_permissions, # 可能为 None
                default_permissions=jwt_default_permissions, # 可能为 None
                user_id=str(application.user_id),
                jti=token_id
            )

            # 创建令牌
            new_token = SAPIToken(
                id=uuid.UUID(token_id),
                name=application.name,
                description=application.description,
                token=token_value,
                user_id=application.user_id,
                permissions=application.requested_permissions, # 旧的单一权限字段
                server_permissions=server_permissions_for_record, # 直接使用从申请获取的值，可能为None
                default_permissions=default_permissions_for_record, # 直接使用从申请获取的值，可能为None
                token_type=token_type,
                status="active",
                project_id=project_id_from_app, # 直接使用从申请获取的值，可能为None
                created_at=datetime.now(timezone.utc).replace(tzinfo=None),
                expires_at=expires_at
            )

            db.add(new_token)

            # 更新申请中的令牌
            application.token = token_value
            application.expires_at = expires_at

        await db.commit()

        return {
            "success": True,
            "message": f"令牌申请{status}成功",
            "data": {
                "id": str(application.id),
                "name": application.name,
                "status": application.status,
                "token": application.token if status == "approved" else None,
                "expires_at": application.expires_at.isoformat() if status == "approved" and application.expires_at else None
            }
        }
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"审批令牌申请失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"审批令牌申请失败: {str(e)}")

# 拒绝令牌申请
@router.post("/token-applications/{application_id}/reject", response_model=Dict[str, Any])
async def reject_token_application(
    application_id: str,
    rejection_data: dict = Body(...),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """拒绝令牌申请"""
    try:
        # 检查权限
        if not (current_user.role == "admin" or current_user.role == "project_admin" or current_user.is_tenant_admin or current_user.is_system_admin):
            raise HTTPException(status_code=403, detail="无权拒绝令牌申请")

        # 查询申请
        result = await db.execute(select(SAPITokenApplication).where(SAPITokenApplication.id == application_id))
        application = result.scalars().first()
        if not application:
            raise HTTPException(status_code=404, detail="令牌申请不存在")

        # 检查申请状态
        if application.status != "pending":
            raise HTTPException(status_code=400, detail="令牌申请已处理")

        # 更新申请状态
        application.status = "rejected"
        application.updated_at = datetime.now(timezone.utc).replace(tzinfo=None)
        application.rejection_reason = rejection_data.get("reason")

        await db.commit()

        return {
            "success": True,
            "message": "令牌申请拒绝成功",
            "data": {
                "id": str(application.id),
                "name": application.name,
                "status": application.status
            }
        }
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"拒绝令牌申请失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"拒绝令牌申请失败: {str(e)}")
