import api from '../httpClient';
import { getTenantId } from '../httpClient';

/**
 * 租户-设置API
 */
const settings = {
  /**
   * 获取租户设置
   * @returns {Promise} - 租户设置
   */
  getSettings: async () => {
    const tenantId = getTenantId();
    if (!tenantId) {
      throw new Error('未指定租户ID');
    }
    return api.get('/tenant/settings');
  },

  /**
   * 更新租户设置
   * @param {Object} data - 设置数据
   * @returns {Promise} - 更新结果
   */
  updateSettings: async (data) => {
    const tenantId = getTenantId();
    if (!tenantId) {
      throw new Error('未指定租户ID');
    }
    return api.put('/tenant/settings', data);
  },

  /**
   * 获取租户品牌设置
   * @returns {Promise} - 品牌设置
   */
  getBrandSettings: async () => {
    const tenantId = getTenantId();
    if (!tenantId) {
      throw new Error('未指定租户ID');
    }
    return api.get('/tenant/settings/brand');
  },

  /**
   * 更新租户品牌设置
   * @param {Object} data - 品牌设置数据
   * @returns {Promise} - 更新结果
   */
  updateBrandSettings: async (data) => {
    const tenantId = getTenantId();
    if (!tenantId) {
      throw new Error('未指定租户ID');
    }
    return api.put('/tenant/settings/brand', data);
  },

  /**
   * 获取租户通知设置
   * @returns {Promise} - 通知设置
   */
  getNotificationSettings: async () => {
    const tenantId = getTenantId();
    if (!tenantId) {
      throw new Error('未指定租户ID');
    }
    return api.get('/tenant/settings/notification');
  },

  /**
   * 更新租户通知设置
   * @param {Object} data - 通知设置数据
   * @returns {Promise} - 更新结果
   */
  updateNotificationSettings: async (data) => {
    const tenantId = getTenantId();
    if (!tenantId) {
      throw new Error('未指定租户ID');
    }
    return api.put('/tenant/settings/notification', data);
  },

  /**
   * 获取租户安全设置
   * @returns {Promise} - 安全设置
   */
  getSecuritySettings: async () => {
    const tenantId = getTenantId();
    if (!tenantId) {
      throw new Error('未指定租户ID');
    }
    return api.get('/tenant/settings/security');
  },

  /**
   * 更新租户安全设置
   * @param {Object} data - 安全设置数据
   * @returns {Promise} - 更新结果
   */
  updateSecuritySettings: async (data) => {
    const tenantId = getTenantId();
    if (!tenantId) {
      throw new Error('未指定租户ID');
    }
    return api.put('/tenant/settings/security', data);
  }
};

export default settings;
