#!/usr/bin/env python
# -*- coding: utf-8 -*-

import logging
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status, Query, Path, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import update, delete
from pydantic import BaseModel, Field
import uuid
from datetime import datetime

from db.database import get_db
from models.tenant import Tenant
from models.core import ServiceProvider, SubscriptionPlan
from models.user import User
from core.auth import get_current_system_admin, get_current_service_provider_admin
from db.migrations import create_tenant_schema, create_industry_tables

# 配置日志
logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter()


# 租户模型
class TenantBase(BaseModel):
    name: str
    industry_type: str
    service_provider_id: Optional[uuid.UUID] = None
    is_direct: Optional[bool] = False
    subscription_plan_id: Optional[uuid.UUID] = None
    max_projects: Optional[int] = 1
    max_users: Optional[int] = 10
    max_storage_gb: Optional[int] = 5
    settings: Optional[Dict[str, Any]] = {}


class TenantCreate(TenantBase):
    pass


class TenantUpdate(BaseModel):
    name: Optional[str] = None
    industry_type: Optional[str] = None
    service_provider_id: Optional[uuid.UUID] = None
    is_direct: Optional[bool] = None
    status: Optional[str] = None
    subscription_plan_id: Optional[uuid.UUID] = None
    max_projects: Optional[int] = None
    max_users: Optional[int] = None
    max_storage_gb: Optional[int] = None
    settings: Optional[Dict[str, Any]] = None


class TenantResponse(TenantBase):
    id: uuid.UUID
    schema_name: str
    status: str
    subscription_start_date: Optional[datetime] = None
    subscription_end_date: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True


async def setup_tenant_schema(tenant_id: str, industry_type: str, db: AsyncSession):
    """
    设置租户Schema（后台任务）
    """
    try:
        # 创建租户Schema
        schema_name = await create_tenant_schema(tenant_id)

        # 创建行业特定的表
        await create_industry_tables(db, industry_type, schema_name)

        logger.info(f"租户Schema设置成功: {schema_name}, 行业: {industry_type}")
    except Exception as e:
        logger.error(f"设置租户Schema失败: {e}")
        # 这里可以添加失败处理逻辑，如发送通知等


@router.post("/tenants", response_model=TenantResponse, status_code=status.HTTP_201_CREATED)
async def create_tenant(
    tenant: TenantCreate,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_service_provider_admin),
):
    """
    创建租户（系统管理员或服务商管理员权限）
    """
    try:
        # 处理直接管理的租户
        if tenant.is_direct:
            # 只有系统管理员可以创建直接管理的租户
            if not current_user.is_system_admin and not current_user.is_super_admin:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="只有系统管理员可以创建直接管理的租户",
                )
            # 直接管理的租户不关联服务商
            tenant.service_provider_id = None
        elif tenant.service_provider_id:
            # 检查权限
            if not current_user.is_system_admin and not current_user.is_super_admin and current_user.service_provider_id != tenant.service_provider_id:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="没有权限为此服务商创建租户",
                )
        else:
            # 既不是直接管理也没有指定服务商
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="必须指定服务商ID或设置为直接管理的租户",
            )

        # 检查服务商是否存在（如果指定了服务商）
        if tenant.service_provider_id:
            result = await db.execute(select(ServiceProvider).where(ServiceProvider.id == tenant.service_provider_id))
            service_provider = result.scalars().first()

            if not service_provider:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="服务商不存在",
                )

        # 检查订阅计划是否存在
        if tenant.subscription_plan_id:
            result = await db.execute(select(SubscriptionPlan).where(SubscriptionPlan.id == tenant.subscription_plan_id))
            subscription_plan = result.scalars().first()

            if not subscription_plan:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="订阅计划不存在",
                )

        # 生成租户ID
        tenant_id = uuid.uuid4()

        # 生成Schema名称
        schema_name = f"tenant_{str(tenant_id).replace('-', '')}"

        # 创建租户
        db_tenant = Tenant(
            id=tenant_id,
            service_provider_id=tenant.service_provider_id,
            name=tenant.name,
            industry_type=tenant.industry_type,
            is_direct=tenant.is_direct,
            schema_name=schema_name,
            subscription_plan_id=tenant.subscription_plan_id,
            max_projects=tenant.max_projects,
            max_users=tenant.max_users,
            max_storage_gb=tenant.max_storage_gb,
            settings=tenant.settings,
        )

        db.add(db_tenant)
        await db.commit()
        await db.refresh(db_tenant)

        # 添加后台任务，设置租户Schema
        background_tasks.add_task(setup_tenant_schema, str(tenant_id), tenant.industry_type, db)

        logger.info(f"租户创建成功: {db_tenant.id}")
        return db_tenant
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"创建租户失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建租户失败: {str(e)}",
        )


@router.get("/tenants", response_model=List[TenantResponse])
async def get_tenants(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    service_provider_id: Optional[uuid.UUID] = Query(None),
    status: Optional[str] = Query(None),
    industry_type: Optional[str] = Query(None),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_service_provider_admin),
):
    """
    获取租户列表（系统管理员或服务商管理员权限）
    """
    try:
        # 构建查询
        query = select(Tenant)

        # 应用过滤条件
        if service_provider_id:
            # 检查权限
            if not current_user.is_system_admin and not current_user.is_super_admin and current_user.service_provider_id != service_provider_id:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="没有权限查看此服务商的租户",
                )
            query = query.where(Tenant.service_provider_id == service_provider_id)
        elif not current_user.is_system_admin and not current_user.is_super_admin:
            # 非系统管理员只能查看自己服务商的租户
            query = query.where(Tenant.service_provider_id == current_user.service_provider_id)

        # 处理直接管理的租户
        if current_user.is_system_admin or current_user.is_super_admin:
            # 系统管理员可以查看所有租户，包括直接管理的租户
            pass
        else:
            # 服务商管理员不能查看直接管理的租户
            query = query.where(Tenant.is_direct == False)

        if status:
            query = query.where(Tenant.status == status)

        if industry_type:
            query = query.where(Tenant.industry_type == industry_type)

        # 应用分页
        query = query.offset(skip).limit(limit)

        # 执行查询
        result = await db.execute(query)
        tenants = result.scalars().all()

        return tenants
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取租户列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取租户列表失败: {str(e)}",
        )


@router.get("/tenants/{tenant_id}", response_model=TenantResponse)
async def get_tenant(
    tenant_id: uuid.UUID = Path(...),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_service_provider_admin),
):
    """
    获取租户详情（系统管理员或服务商管理员权限）
    """
    try:
        # 查询租户
        result = await db.execute(select(Tenant).where(Tenant.id == tenant_id))
        tenant = result.scalars().first()

        if not tenant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="租户不存在",
            )

        # 检查权限
        if not current_user.is_system_admin and not current_user.is_super_admin:
            # 直接管理的租户只能由系统管理员访问
            if tenant.is_direct:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="没有权限访问此租户",
                )

            # 服务商管理员只能访问自己服务商的租户
            if current_user.service_provider_id != tenant.service_provider_id:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="没有权限访问此租户",
                )

        return tenant
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取租户详情失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取租户详情失败: {str(e)}",
        )


@router.put("/tenants/{tenant_id}", response_model=TenantResponse)
async def update_tenant(
    tenant_id: uuid.UUID = Path(...),
    tenant_update: TenantUpdate = None,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_service_provider_admin),
):
    """
    更新租户信息（系统管理员或服务商管理员权限）
    """
    try:
        # 查询租户
        result = await db.execute(select(Tenant).where(Tenant.id == tenant_id))
        db_tenant = result.scalars().first()

        if not db_tenant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="租户不存在",
            )

        # 检查权限
        if not current_user.is_system_admin and not current_user.is_super_admin:
            # 直接管理的租户只能由系统管理员更新
            if db_tenant.is_direct:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="没有权限更新此租户",
                )

            # 服务商管理员只能更新自己服务商的租户
            if current_user.service_provider_id != db_tenant.service_provider_id:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="没有权限更新此租户",
                )

        # 处理特殊字段
        if tenant_update.is_direct is not None:
            # 只有系统管理员可以修改is_direct字段
            if not current_user.is_system_admin and not current_user.is_super_admin:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="没有权限修改租户的直接管理状态",
                )

            # 如果设置为直接管理，则清除服务商ID
            if tenant_update.is_direct:
                tenant_update.service_provider_id = None

        # 更新租户信息
        update_data = tenant_update.dict(exclude_unset=True)

        if update_data:
            await db.execute(
                update(Tenant)
                .where(Tenant.id == tenant_id)
                .values(**update_data)
            )
            await db.commit()

        # 重新查询租户
        result = await db.execute(select(Tenant).where(Tenant.id == tenant_id))
        updated_tenant = result.scalars().first()

        return updated_tenant
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"更新租户失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新租户失败: {str(e)}",
        )


@router.delete("/tenants/{tenant_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_tenant(
    tenant_id: uuid.UUID = Path(...),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_system_admin),
):
    """
    删除租户（系统管理员权限）
    """
    try:
        # 查询租户
        result = await db.execute(select(Tenant).where(Tenant.id == tenant_id))
        db_tenant = result.scalars().first()

        if not db_tenant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="租户不存在",
            )

        # 删除租户
        await db.execute(delete(Tenant).where(Tenant.id == tenant_id))
        await db.commit()

        # 注意：这里不删除租户Schema，因为可能需要保留数据
        # 如果需要删除Schema，可以添加后台任务

        logger.info(f"租户删除成功: {tenant_id}")
        return None
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"删除租户失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除租户失败: {str(e)}",
        )
