import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import {
  Card,
  Breadcrumb,
  Button,
  Descriptions,
  Spin,
  message,
  Tabs,
  Row,
  Col,
  Statistic,
  Tag,
  Typography,
  Space,
  Modal,
  Input,
  QRCode,
  Table,
  Progress,
  Result,
  Tooltip
} from 'antd';
import {
  ArrowLeftOutlined,
  EditOutlined,
  GiftOutlined,
  UserOutlined,
  HistoryOutlined,
  QrcodeOutlined,
  LinkOutlined,
  CopyOutlined,
  ShareAltOutlined
} from '@ant-design/icons';
import dayjs from "dayjs";
import apiService from './service/api/marketingGame';
import { gameStatusColors, gameStatusText, gameTypeText } from './utils/constants';

// 奖品类型文本
const prizeTypeText = {
  physical: '实物奖品',
  coupon: '优惠券',
  points: '积分',
  virtual: '虚拟奖品'
};

const { Title, Paragraph, Text } = Typography;
const { TabPane } = Tabs;

const GameDetail = () => {
  const { gameId } = useParams();
  const navigate = useNavigate();
  const [game, setGame] = useState(null);
  const [loading, setLoading] = useState(true);
  const [prizes, setPrizes] = useState([]);
  const [participants, setParticipants] = useState([]);
  const [records, setRecords] = useState([]);
  const [stats, setStats] = useState({
    totalPrizes: 0,
    totalParticipants: 0,
    totalRecords: 0,
    remainingPrizes: 0
  });
  const [activeTab, setActiveTab] = useState('info');
  const [showQRCode, setShowQRCode] = useState(false);

  // 获取游戏详情
  const fetchGameDetail = async () => {
    try {
      const response = await apiService.getGameDetail(gameId);
      if (response.success) {
        setGame(response.data);
      } else {
        message.error(response.message || '获取游戏详情失败');
      }
    } catch (error) {
      console.error('获取游戏详情失败:', error);
      message.error('获取游戏详情失败');
    }
  };

  // 获取奖品列表
  const fetchPrizes = async () => {
    try {
      const response = await apiService.getPrizes(gameId);
      if (response.success) {
        setPrizes(response.data || []);

        // 计算奖品统计数据
        const totalPrizes = response.data.reduce((sum, prize) => sum + prize.quantity, 0);
        const remainingPrizes = response.data.reduce((sum, prize) => sum + prize.remaining, 0);

        setStats(prev => ({
          ...prev,
          totalPrizes,
          remainingPrizes
        }));
      } else {
        message.error(response.message || '获取奖品列表失败');
      }
    } catch (error) {
      console.error('获取奖品列表失败:', error);
      message.error('获取奖品列表失败');
    }
  };

  // 获取参与者列表
  const fetchParticipants = async () => {
    try {
      const response = await apiService.getParticipants(gameId);
      if (response.success) {
        setParticipants(response.data || []);
        setStats(prev => ({
          ...prev,
          totalParticipants: response.data.length
        }));
      } else {
        message.error(response.message || '获取参与者列表失败');
      }
    } catch (error) {
      console.error('获取参与者列表失败:', error);
      message.error('获取参与者列表失败');
    }
  };

  // 获取游戏记录
  const fetchRecords = async () => {
    try {
      const response = await apiService.getRecords(gameId);
      if (response.success) {
        setRecords(response.data || []);
        setStats(prev => ({
          ...prev,
          totalRecords: response.data.length
        }));
      } else {
        message.error(response.message || '获取游戏记录失败');
      }
    } catch (error) {
      console.error('获取游戏记录失败:', error);
      message.error('获取游戏记录失败');
    }
  };

  // 初始加载
  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      await fetchGameDetail();
      await fetchPrizes();
      await fetchParticipants();
      await fetchRecords();
      setLoading(false);
    };

    if (gameId) {
      fetchData();
    }
  }, [gameId]);

  // 生成游戏链接
  const generateGameLink = () => {
    const baseUrl = window.location.origin;
    return `${baseUrl}/game/marketing/${gameId}`;
  };

  // 复制链接到剪贴板
  const copyToClipboard = (text) => {
    // 检查是否支持 Clipboard API
    if (navigator.clipboard && navigator.clipboard.writeText) {
      navigator.clipboard.writeText(text)
        .then(() => {
          message.success('链接已复制到剪贴板');
        })
        .catch((err) => {
          console.error('使用 Clipboard API 复制失败:', err);
          fallbackCopyTextToClipboard(text);
        });
    } else {
      // 使用 fallback 方法
      fallbackCopyTextToClipboard(text);
    }
  };

  // Fallback 复制方法（兼容旧浏览器）
  const fallbackCopyTextToClipboard = (text) => {
    const textArea = document.createElement("textarea");
    textArea.value = text;
    
    // 避免在页面上显示
    textArea.style.position = "fixed";
    textArea.style.top = "0";
    textArea.style.left = "0";
    textArea.style.width = "2em";
    textArea.style.height = "2em";
    textArea.style.padding = "0";
    textArea.style.border = "none";
    textArea.style.outline = "none";
    textArea.style.boxShadow = "none";
    textArea.style.background = "transparent";
    
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    
    try {
      const successful = document.execCommand('copy');
      if (successful) {
        message.success('链接已复制到剪贴板');
      } else {
        message.error('复制失败，请手动复制');
      }
    } catch (err) {
      console.error('Fallback 复制方法失败:', err);
      message.error('复制失败，请手动复制');
    }
    
    document.body.removeChild(textArea);
  };

  // 处理编辑游戏
  const handleEditGame = () => {
    navigate(`/project/plugins/marketing_game`);
    setTimeout(() => {
      // 通过事件或其他方式触发编辑表单
      window.dispatchEvent(new CustomEvent('editMarketingGame', { detail: { gameId } }));
    }, 500);
  };

  if (loading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '400px' }}>
        <Spin size="large" />
      </div>
    );
  }

  if (!game) {
    return (
      <Card>
        <Result
          status="404"
          title="游戏不存在"
          subTitle="您查找的游戏不存在或已被删除"
          extra={
            <Button type="primary" onClick={() => navigate('/project/plugins/marketing_game')}>
              返回游戏列表
            </Button>
          }
        />
      </Card>
    );
  }

  return (
    <div className="game-detail">
      <Card>
        <Breadcrumb style={{ marginBottom: 16 }}>
          <Breadcrumb.Item>
            <Link to="/project/plugins/marketing_game">营销游戏</Link>
          </Breadcrumb.Item>
          <Breadcrumb.Item>游戏详情</Breadcrumb.Item>
        </Breadcrumb>

        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
          <Space>
            <Button
              icon={<ArrowLeftOutlined />}
              onClick={() => navigate('/project/plugins/marketing_game')}
            >
              返回
            </Button>
            <Title level={4} style={{ margin: 0 }}>
              {game.name}
              <Tag color={gameStatusColors[game.status]} style={{ marginLeft: 8 }}>
                {gameStatusText[game.status]}
              </Tag>
            </Title>
          </Space>

          <Space>
            <Button
              icon={<EditOutlined />}
              onClick={handleEditGame}
            >
              编辑
            </Button>
            <Button
              icon={<GiftOutlined />}
              onClick={() => navigate(`/project/plugins/marketing_game/prizes/${gameId}`)}
            >
              奖品管理
            </Button>
            <Button
              icon={<UserOutlined />}
              onClick={() => navigate(`/project/plugins/marketing_game/participants/${gameId}`)}
            >
              参与者管理
            </Button>
            <Button
              icon={<HistoryOutlined />}
              onClick={() => navigate(`/project/plugins/marketing_game/records/${gameId}`)}
            >
              游戏记录
            </Button>
            <Button
              type="primary"
              icon={<QrcodeOutlined />}
              onClick={() => setShowQRCode(true)}
            >
              游戏链接
            </Button>
          </Space>
        </div>

        <Row gutter={[16, 16]}>
          <Col span={6}>
            <Card>
              <Statistic title="奖品总数" value={stats.totalPrizes} />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic title="剩余奖品" value={stats.remainingPrizes} />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic title="参与人数" value={stats.totalParticipants} />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic title="抽奖次数" value={stats.totalRecords} />
            </Card>
          </Col>
        </Row>

        <Card style={{ marginTop: 16 }}>
          <Tabs activeKey={activeTab} onChange={setActiveTab}>
            <TabPane tab="基本信息" key="info">
              <Descriptions bordered column={2}>
                <Descriptions.Item label="游戏名称">{game.name}</Descriptions.Item>
                <Descriptions.Item label="游戏类型">{gameTypeText[game.game_type]}</Descriptions.Item>
                <Descriptions.Item label="游戏状态">
                  <Tag color={gameStatusColors[game.status]}>
                    {gameStatusText[game.status]}
                  </Tag>
                </Descriptions.Item>
                <Descriptions.Item label="每人抽奖次数">{game.max_draws_per_user}</Descriptions.Item>
                <Descriptions.Item label="最低消费金额">¥{game.min_consumption}</Descriptions.Item>
                <Descriptions.Item label="创建时间">{dayjs(game.created_at).format('YYYY-MM-DD HH:mm:ss')}</Descriptions.Item>
                <Descriptions.Item label="开始时间">{dayjs(game.start_time).format('YYYY-MM-DD HH:mm:ss')}</Descriptions.Item>
                <Descriptions.Item label="结束时间">{dayjs(game.end_time).format('YYYY-MM-DD HH:mm:ss')}</Descriptions.Item>
                <Descriptions.Item label="游戏描述" span={2}>
                  {game.description || '无'}
                </Descriptions.Item>
                <Descriptions.Item label="游戏规则" span={2}>
                  <pre style={{ whiteSpace: 'pre-wrap', margin: 0 }}>{game.rules || '无'}</pre>
                </Descriptions.Item>
              </Descriptions>
            </TabPane>
            <TabPane tab="奖品信息" key="prizes">
              <Table
                dataSource={prizes}
                rowKey="id"
                pagination={false}
                columns={[
                  {
                    title: '奖品名称',
                    dataIndex: 'name',
                    key: 'name',
                  },
                  {
                    title: '奖品类型',
                    dataIndex: 'prize_type',
                    key: 'prize_type',
                    render: (type) => (
                      <Tag color={type === 'physical' ? 'blue' : type === 'coupon' ? 'green' : type === 'points' ? 'gold' : 'purple'}>
                        {prizeTypeText[type] || type}
                      </Tag>
                    )
                  },
                  {
                    title: '奖品价值',
                    dataIndex: 'value',
                    key: 'value',
                    render: (value) => `¥${value ? value.toFixed(2) : '0.00'}`
                  },
                  {
                    title: '数量',
                    dataIndex: 'quantity',
                    key: 'quantity'
                  },
                  {
                    title: '剩余',
                    dataIndex: 'remaining',
                    key: 'remaining',
                    render: (remaining, record) => (
                      <Tooltip title={`${remaining}/${record.quantity}`}>
                        <Progress
                          percent={Math.round((remaining / record.quantity) * 100)}
                          size="small"
                          status={remaining === 0 ? 'exception' : 'normal'}
                        />
                      </Tooltip>
                    )
                  },
                  {
                    title: '中奖概率',
                    dataIndex: 'probability',
                    key: 'probability',
                    render: (probability) => `${probability ? probability.toFixed(2) : '0.00'}%`
                  },
                  {
                    title: '默认奖品',
                    dataIndex: 'is_default',
                    key: 'is_default',
                    render: (isDefault) => isDefault ? '是' : '否'
                  }
                ]}
              />
            </TabPane>
            <TabPane tab="参与者信息" key="participants">
              <Table
                dataSource={participants}
                rowKey="id"
                pagination={{ pageSize: 10 }}
                columns={[
                  {
                    title: '用户名',
                    dataIndex: 'user_name',
                    key: 'user_name',
                    render: (text, record) => record.user_name || record.name || '匿名用户'
                  },
                  {
                    title: '手机号',
                    dataIndex: 'phone',
                    key: 'phone',
                    render: (phone) => phone || '-'
                  },
                  {
                    title: '消费金额',
                    dataIndex: 'consumption_amount',
                    key: 'consumption_amount',
                    render: (amount, record) => {
                      const finalAmount = amount || record.total_consumption || 0;
                      return `¥${finalAmount.toFixed(2)}`;
                    }
                  },
                  {
                    title: '参与次数',
                    dataIndex: 'draw_count',
                    key: 'draw_count',
                    render: (count, record) => record.draw_count || record.draws_count || 0
                  },
                  {
                    title: '中奖次数',
                    dataIndex: 'win_count',
                    key: 'win_count',
                    render: (count, record) => record.win_count || record.wins_count || 0
                  },
                  {
                    title: '参与时间',
                    dataIndex: 'created_at',
                    key: 'created_at',
                    render: (time) => dayjs(time).format('YYYY-MM-DD HH:mm:ss')
                  }
                ]}
              />
            </TabPane>
            <TabPane tab="游戏记录" key="records">
              <Table
                dataSource={records}
                rowKey="id"
                pagination={{ pageSize: 10 }}
                columns={[
                  {
                    title: '用户名',
                    dataIndex: 'user_name',
                    key: 'user_name',
                    render: (text, record) => {
                      return record.user_name || 
                             (record.participant && record.participant.name) || 
                             record.participant_name || 
                             '匿名用户';
                    }
                  },
                  {
                    title: '手机号',
                    dataIndex: 'phone',
                    key: 'phone',
                    render: (text, record) => {
                      const phone = text || 
                                   (record.participant && record.participant.phone) || 
                                   record.user_phone || 
                                   record.phone;
                      return phone || '-';
                    }
                  },
                  {
                    title: '奖品',
                    dataIndex: 'prize_name',
                    key: 'prize_name',
                    render: (text, record) => {
                      return text || 
                             (record.prize && record.prize.name) || 
                             '未知奖品';
                    }
                  },
                  {
                    title: '奖品价值',
                    dataIndex: 'prize_value',
                    key: 'prize_value',
                    render: (value, record) => {
                      const finalValue = value || (record.prize && record.prize.value) || 0;
                      return `¥${finalValue.toFixed(2)}`;
                    }
                  },
                  {
                    title: '是否中奖',
                    dataIndex: 'is_win',
                    key: 'is_win',
                    render: (isWin) => (
                      <Tag color={isWin ? 'success' : 'default'}>
                        {isWin ? '中奖' : '未中奖'}
                      </Tag>
                    )
                  },
                  {
                    title: '抽奖时间',
                    key: 'draw_time',
                    render: (text, record) => {
                      const time = record.created_at || record.draw_time || record.updated_at;
                      if (!time) return '-';
                      
                      // 使用dayjs格式化时间，确保显示为本地时间
                      return dayjs(time).format('YYYY-MM-DD HH:mm:ss');
                    }
                  }
                ]}
              />
            </TabPane>
          </Tabs>
        </Card>
      </Card>

      {/* 游戏链接弹窗 */}
      <Modal
        title="游戏链接"
        open={showQRCode}
        onCancel={() => setShowQRCode(false)}
        footer={[
          <Button key="close" onClick={() => setShowQRCode(false)}>
            关闭
          </Button>,
          <Button
            key="copy"
            type="primary"
            icon={<CopyOutlined />}
            onClick={() => copyToClipboard(generateGameLink())}
          >
            复制链接
          </Button>,
          <Button
            key="share"
            type="primary"
            icon={<ShareAltOutlined />}
            onClick={() => {
              message.success('分享成功');
              setShowQRCode(false);
            }}
          >
            分享
          </Button>
        ]}
        width={400}
      >
        <div style={{ textAlign: 'center' }}>
          <div style={{ marginBottom: 16 }}>
            <QRCode value={generateGameLink()} size={200} />
          </div>
          <div>
            <Input.TextArea
              value={generateGameLink()}
              readOnly
              autoSize={{ minRows: 2, maxRows: 2 }}
            />
          </div>
          <Paragraph style={{ marginTop: 16 }}>
            扫描二维码或复制链接，即可参与游戏
          </Paragraph>
        </div>
      </Modal>
    </div>
  );
};

export default GameDetail;
