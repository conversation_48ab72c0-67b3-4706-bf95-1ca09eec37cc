#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
AI 使用记录服务
"""

import logging
import uuid
from typing import List, Optional, Dict, Any, Tuple
from datetime import datetime, timedelta
from sqlalchemy import select, func, and_, or_, desc, text, case
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import joinedload

from models.ai import AIUsage, AIConfig, AIModel, AIAssistant, AIProvider
from models.user import User
from schemas.ai import AIUsageCreate

logger = logging.getLogger(__name__)

class AIUsageService:
    """AI 使用记录服务"""

    @staticmethod
    async def get_usages(
        db: AsyncSession,
        skip: int = 0,
        limit: int = 100,
        tenant_id: Optional[uuid.UUID] = None,
        project_id: Optional[uuid.UUID] = None,
        user_id: Optional[uuid.UUID] = None,
        config_id: Optional[uuid.UUID] = None,
        model_id: Optional[uuid.UUID] = None,
        request_type: Optional[str] = None,
        status: Optional[str] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
    ) -> Tuple[List[AIUsage], int]:
        """
        获取 AI 使用记录列表
        """
        query = select(AIUsage).options(
            joinedload(AIUsage.user),
            joinedload(AIUsage.model),
            joinedload(AIUsage.config)
        )

        # 构建查询条件
        conditions = []

        if tenant_id:
            conditions.append(AIUsage.tenant_id == tenant_id)

        if project_id:
            conditions.append(AIUsage.project_id == project_id)

        if user_id:
            conditions.append(AIUsage.user_id == user_id)

        if config_id:
            conditions.append(AIUsage.config_id == config_id)

        if model_id:
            conditions.append(AIUsage.model_id == model_id)

        if request_type:
            conditions.append(AIUsage.request_type == request_type)

        if status:
            conditions.append(AIUsage.status == status)

        if start_date:
            conditions.append(AIUsage.created_at >= start_date)

        if end_date:
            conditions.append(AIUsage.created_at <= end_date)

        if conditions:
            query = query.where(and_(*conditions))

        # 获取总数
        count_query = select(func.count()).select_from(query.subquery())
        total = await db.scalar(count_query)

        # 获取分页数据
        query = query.order_by(desc(AIUsage.created_at)).offset(skip).limit(limit)
        result = await db.execute(query)
        usages = result.scalars().all()

        return usages, total

    @staticmethod
    async def get_usage(
        db: AsyncSession,
        usage_id: uuid.UUID,
    ) -> Optional[AIUsage]:
        """
        获取 AI 使用记录详情
        """
        query = select(AIUsage).options(
            joinedload(AIUsage.user),
            joinedload(AIUsage.model),
            joinedload(AIUsage.config)
        ).where(AIUsage.id == usage_id)

        result = await db.execute(query)
        return result.scalars().first()

    @staticmethod
    async def create_usage(
        db: AsyncSession,
        usage_data: AIUsageCreate,
    ) -> AIUsage:
        """
        创建 AI 使用记录
        """
        # 创建使用记录
        usage = AIUsage(**usage_data.dict())
        db.add(usage)
        await db.commit()
        await db.refresh(usage)

        logger.info(f"创建 AI 使用记录成功: {usage.id}")
        return usage

    @staticmethod
    async def get_usage_summary(
        db: AsyncSession,
        tenant_id: Optional[uuid.UUID] = None,
        project_id: Optional[uuid.UUID] = None,
    ) -> Dict[str, Any]:
        """
        获取 AI 使用统计摘要
        """
        logger.info(f"get_usage_summary 方法被调用，project_id: {project_id}, tenant_id: {tenant_id}")
        
        # 构建查询条件
        conditions = []

        if tenant_id:
            conditions.append(AIUsage.tenant_id == tenant_id)

        if project_id:
            conditions.append(AIUsage.project_id == project_id)

        # 总体统计
        total_query = select(
            func.sum(AIUsage.total_tokens).label("total_tokens"),
            func.sum(AIUsage.cost).label("total_cost"),
            func.count().label("total_requests")
        )
        
        if conditions:
            total_query = total_query.where(and_(*conditions))

        total_result = await db.execute(total_query)
        total_stats = total_result.mappings().first()

        total_tokens = total_stats["total_tokens"] or 0
        total_cost = total_stats["total_cost"] or 0
        total_requests = total_stats["total_requests"] or 0

        # 本月统计
        current_month_start = datetime.now().replace(day=1, hour=0, minute=0, second=0, microsecond=0)

        month_query = select(
            func.sum(AIUsage.total_tokens).label("total_tokens"),
            func.sum(AIUsage.cost).label("total_cost"),
            func.count().label("total_requests")
        )
        
        month_conditions = conditions + [AIUsage.created_at >= current_month_start]
        if month_conditions:
            month_query = month_query.where(and_(*month_conditions))

        month_result = await db.execute(month_query)
        month_stats = month_result.mappings().first()

        current_month_tokens = month_stats["total_tokens"] or 0
        current_month_cost = month_stats["total_cost"] or 0
        current_month_requests = month_stats["total_requests"] or 0

        # 获取助手统计
        assistant_conditions = []
        if tenant_id:
            assistant_conditions.append(AIAssistant.tenant_id == tenant_id)
        if project_id:
            assistant_conditions.append(AIAssistant.project_id == project_id)

        assistant_query = select(
            func.count().label("total_assistants"),
            func.sum(case((AIAssistant.status == 'active', 1), else_=0)).label("active_assistants")
        )
        
        if assistant_conditions:
            assistant_query = assistant_query.where(and_(*assistant_conditions))

        assistant_result = await db.execute(assistant_query)
        assistant_stats = assistant_result.mappings().first()

        total_assistants = assistant_stats["total_assistants"] or 0
        active_assistants = assistant_stats["active_assistants"] or 0

        # 按模型统计（包含模型名称）
        model_stats = []
        
        # 如果有使用记录，尝试获取模型统计
        if total_requests > 0:
            try:
                # 带模型信息的统计查询
                model_query = select(
                    AIUsage.model_id,
                    AIModel.name.label("model_name"),
                    AIModel.display_name.label("model_display_name"),
                    AIProvider.name.label("provider_name"),
                    func.count(AIUsage.id).label("request_count"),
                    func.sum(AIUsage.prompt_tokens).label("input_tokens"),
                    func.sum(AIUsage.completion_tokens).label("output_tokens"),
                    func.sum(AIUsage.total_tokens).label("total_tokens"),
                    func.sum(AIUsage.cost).label("total_cost"),
                    func.avg(AIUsage.duration_ms).label("avg_response_time"),
                    func.sum(case((AIUsage.status == "success", 1), else_=0)).label("success_count"),
                    func.max(AIUsage.created_at).label("last_used_at")
                ).join(
                    AIModel, AIUsage.model_id == AIModel.id
                ).join(
                    AIProvider, AIModel.provider_id == AIProvider.id
                )
                
                if conditions:
                    model_query = model_query.where(and_(*conditions))
                    
                model_query = model_query.group_by(
                    AIUsage.model_id, 
                    AIModel.name, 
                    AIModel.display_name,
                    AIProvider.name
                )

                model_result = await db.execute(model_query)
                model_stats_list = model_result.mappings().all()

                for stat in model_stats_list:
                    request_count = stat["request_count"] or 0
                    success_count = stat["success_count"] or 0
                    total_cost = stat["total_cost"] or 0
                    
                    # 简化费用分配（假设输入和输出费用比例为1:3）
                    input_cost = total_cost * 0.25 if total_cost > 0 else 0
                    output_cost = total_cost * 0.75 if total_cost > 0 else 0
                    
                    model_stats.append({
                        "model_id": str(stat["model_id"]) if stat["model_id"] else "unknown",
                        "model_name": stat["model_display_name"] or stat["model_name"] or "未知模型",
                        "provider_name": stat["provider_name"] or "未知提供商",
                        "request_count": request_count,
                        "success_count": success_count,
                        "input_tokens": stat["input_tokens"] or 0,
                        "output_tokens": stat["output_tokens"] or 0,
                        "total_tokens": stat["total_tokens"] or 0,
                        "input_cost": float(input_cost),
                        "output_cost": float(output_cost),
                        "total_cost": float(total_cost),
                        "avg_response_time": float(stat["avg_response_time"] or 0),
                        "last_used_at": stat["last_used_at"].isoformat() if stat["last_used_at"] else None
                    })
            except Exception as e:
                logger.warning(f"获取模型统计失败，使用空列表: {e}")
                model_stats = []

        # 返回统计结果
        return {
            "total_tokens": total_tokens,
            "total_cost": float(total_cost),
            "current_month_tokens": current_month_tokens,
            "current_month_cost": float(current_month_cost),
            "total_conversations": current_month_requests,  # 本月对话数
            "active_assistants": active_assistants,
            "total_assistants": total_assistants,
            "total_requests": total_requests,  # 总请求数
            "current_month_conversations": current_month_requests,  # 本月对话数（别名）
            "model_stats": model_stats  # 添加模型统计明细
        }

    @staticmethod
    async def get_usage_statistics(
        db: AsyncSession,
        tenant_id: Optional[uuid.UUID] = None,
        project_id: Optional[uuid.UUID] = None,
        user_id: Optional[uuid.UUID] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        group_by: str = "day",
    ) -> Dict[str, Any]:
        """
        获取 AI 使用统计
        """
        # 构建查询条件
        conditions = []

        if tenant_id:
            conditions.append(AIUsage.tenant_id == tenant_id)

        if project_id:
            conditions.append(AIUsage.project_id == project_id)

        if user_id:
            conditions.append(AIUsage.user_id == user_id)

        if start_date:
            conditions.append(AIUsage.created_at >= start_date)

        if end_date:
            conditions.append(AIUsage.created_at <= end_date)

        # 总体统计
        total_query = select(
            func.count().label("total_requests"),
            func.sum(AIUsage.total_tokens).label("total_tokens"),
            func.sum(AIUsage.cost).label("total_cost"),
            func.avg(AIUsage.duration_ms).label("avg_duration_ms"),
            func.sum(case((AIUsage.status == "success", 1), else_=0)).label("success_count")
        ).where(and_(*conditions))

        total_result = await db.execute(total_query)
        total_stats = total_result.mappings().first()

        total_requests = total_stats["total_requests"] or 0
        total_tokens = total_stats["total_tokens"] or 0
        total_cost = total_stats["total_cost"] or 0
        avg_duration_ms = total_stats["avg_duration_ms"] or 0
        success_count = total_stats["success_count"] or 0

        success_rate = (success_count / total_requests * 100) if total_requests > 0 else 0

        # 按模型统计
        model_query = select(
            AIModel.name.label("model_name"),
            func.count().label("request_count"),
            func.sum(AIUsage.total_tokens).label("total_tokens"),
            func.sum(AIUsage.cost).label("total_cost"),
            func.avg(AIUsage.duration_ms).label("avg_duration_ms"),
            func.sum(case((AIUsage.status == "success", 1), else_=0)).label("success_count")
        ).join(AIModel, AIUsage.model_id == AIModel.id).where(
            and_(*conditions)
        ).group_by(AIModel.name)

        model_result = await db.execute(model_query)
        model_stats = model_result.mappings().all()

        by_model = {}
        for stat in model_stats:
            model_name = stat["model_name"]
            request_count = stat["request_count"] or 0
            model_success_count = stat["success_count"] or 0
            model_success_rate = (model_success_count / request_count * 100) if request_count > 0 else 0

            by_model[model_name] = {
                "request_count": request_count,
                "total_tokens": stat["total_tokens"] or 0,
                "total_cost": stat["total_cost"] or 0,
                "avg_duration_ms": stat["avg_duration_ms"] or 0,
                "success_rate": model_success_rate,
                "percentage": (request_count / total_requests * 100) if total_requests > 0 else 0
            }

        # 按请求类型统计
        type_query = select(
            AIUsage.request_type,
            func.count().label("request_count"),
            func.sum(AIUsage.total_tokens).label("total_tokens"),
            func.sum(AIUsage.cost).label("total_cost"),
            func.avg(AIUsage.duration_ms).label("avg_duration_ms"),
            func.sum(case((AIUsage.status == "success", 1), else_=0)).label("success_count")
        ).where(
            and_(*conditions)
        ).group_by(AIUsage.request_type)

        type_result = await db.execute(type_query)
        type_stats = type_result.mappings().all()

        by_request_type = {}
        for stat in type_stats:
            request_type = stat["request_type"]
            request_count = stat["request_count"] or 0
            type_success_count = stat["success_count"] or 0
            type_success_rate = (type_success_count / request_count * 100) if request_count > 0 else 0

            by_request_type[request_type] = {
                "request_count": request_count,
                "total_tokens": stat["total_tokens"] or 0,
                "total_cost": stat["total_cost"] or 0,
                "avg_duration_ms": stat["avg_duration_ms"] or 0,
                "success_rate": type_success_rate,
                "percentage": (request_count / total_requests * 100) if total_requests > 0 else 0
            }

        # 按时间统计（暂时简化，避免GROUP BY错误）
        by_date = {}
        
        # 如果需要按时间统计，可以后续优化
        # 目前先返回基本统计信息

        # 返回统计结果
        return {
            "total_requests": total_requests,
            "total_tokens": total_tokens,
            "total_cost": float(total_cost),
            "success_rate": success_rate,
            "avg_duration_ms": float(avg_duration_ms),
            "by_model": by_model,
            "by_request_type": by_request_type,
            "by_date": by_date
        }

    @staticmethod
    async def get_config_usage_statistics(
        db: AsyncSession,
        project_id: uuid.UUID,
        config_id: uuid.UUID,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
    ) -> Dict[str, Any]:
        """
        获取指定配置的使用统计
        """
        # 构建查询条件
        conditions = [
            AIUsage.project_id == project_id,
            AIUsage.config_id == config_id
        ]

        if start_date:
            conditions.append(AIUsage.created_at >= start_date)

        if end_date:
            conditions.append(AIUsage.created_at <= end_date)

        # 总体统计
        total_query = select(
            func.count().label("request_count"),
            func.sum(AIUsage.prompt_tokens).label("input_tokens"),
            func.sum(AIUsage.completion_tokens).label("output_tokens"),
            func.sum(AIUsage.total_tokens).label("total_tokens"),
            func.sum(AIUsage.cost).label("total_cost"),
            func.avg(AIUsage.duration_ms).label("avg_response_time"),
            func.min(AIUsage.duration_ms).label("min_response_time"),
            func.max(AIUsage.duration_ms).label("max_response_time"),
            func.sum(case((AIUsage.status == "success", 1), else_=0)).label("success_count"),
            func.max(AIUsage.created_at).label("last_used_at")
        ).where(and_(*conditions))

        result = await db.execute(total_query)
        stats = result.mappings().first()

        request_count = stats["request_count"] or 0
        success_count = stats["success_count"] or 0
        
        # 计算输入和输出费用（基于token数量和模型定价）
        input_tokens = stats["input_tokens"] or 0
        output_tokens = stats["output_tokens"] or 0
        total_cost = stats["total_cost"] or 0
        
        # 简化费用分配（假设输入和输出费用比例为1:3）
        input_cost = total_cost * 0.25 if total_cost > 0 else 0
        output_cost = total_cost * 0.75 if total_cost > 0 else 0

        # 计算成功率
        success_rate = (success_count / request_count * 100) if request_count > 0 else 0

        return {
            "total_requests": request_count,
            "total_tokens": stats["total_tokens"] or 0,
            "total_cost": float(total_cost),
            "success_rate": success_rate,
            "avg_duration_ms": float(stats["avg_response_time"] or 0),
            "by_model": {},
            "by_request_type": {},
            "by_date": {},
            # 详细统计信息
            "request_count": request_count,
            "success_count": success_count,
            "input_tokens": input_tokens,
            "output_tokens": output_tokens,
            "input_cost": float(input_cost),
            "output_cost": float(output_cost),
            "min_response_time": float(stats["min_response_time"] or 0),
            "max_response_time": float(stats["max_response_time"] or 0),
            "last_used_at": stats["last_used_at"].isoformat() if stats["last_used_at"] else None
        }
