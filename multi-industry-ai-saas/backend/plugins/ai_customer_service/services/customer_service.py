#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
客服管理器

整合所有客服功能模块，提供统一的客服服务接口。
"""

import logging
import uuid
import asyncio
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_

from ..core.platform_adapter import (
    PlatformAdapterManager, 
    PlatformMessage, 
    PlatformResponse
)
from ..core.ai_engine import AICustomerServiceEngine
from ..core.knowledge_rag import KnowledgeRAGService
from ..core.business_handler import BusinessHandler
from ..core.analytics import CustomerAnalytics
from ..core.marketing import IntelligentMarketing
from ..models.customer_service import (
    CustomerServiceSession,
    CustomerServiceMessage,
    SessionStatus,
    MessageType
)

logger = logging.getLogger(__name__)

# 全局客服管理器实例
_customer_service_manager = None

def get_customer_service_manager() -> "CustomerServiceManager":
    """获取客服管理器实例"""
    global _customer_service_manager
    if _customer_service_manager is None:
        _customer_service_manager = CustomerServiceManager()
    return _customer_service_manager

class CustomerServiceManager:
    """客服管理器"""
    
    def __init__(self):
        self.platform_manager = PlatformAdapterManager()
        self.ai_engine = AICustomerServiceEngine()
        self.knowledge_service = KnowledgeRAGService()
        self.business_handler = BusinessHandler()
        self.analytics = CustomerAnalytics()
        self.marketing = IntelligentMarketing()
        
        self.active_sessions: Dict[str, Dict[str, Any]] = {}
        self.message_queue = asyncio.Queue()
        self.processing_tasks: List[asyncio.Task] = []
        
    async def initialize(self):
        """初始化客服管理器"""
        try:
            logger.info("正在初始化客服管理器...")
            
            # 初始化各个组件
            await self.platform_manager.initialize()
            await self.ai_engine.initialize()
            await self.knowledge_service.initialize()
            
            # 注册消息处理器
            self.platform_manager.add_message_handler(self.handle_platform_message)
            
            # 启动消息处理任务
            self.processing_tasks.append(
                asyncio.create_task(self._message_processor())
            )
            
            logger.info("客服管理器初始化完成")
            
        except Exception as e:
            logger.error(f"客服管理器初始化失败: {e}")
            raise
            
    async def shutdown(self):
        """关闭客服管理器"""
        try:
            logger.info("正在关闭客服管理器...")
            
            # 停止处理任务
            for task in self.processing_tasks:
                task.cancel()
                
            # 等待任务完成
            await asyncio.gather(*self.processing_tasks, return_exceptions=True)
            
            # 关闭各个组件
            await self.platform_manager.shutdown()
            await self.ai_engine.shutdown()
            
            logger.info("客服管理器已关闭")
            
        except Exception as e:
            logger.error(f"客服管理器关闭失败: {e}")
            
    async def handle_platform_message(self, message: PlatformMessage):
        """处理平台消息"""
        try:
            # 将消息放入队列
            await self.message_queue.put(message)
            
        except Exception as e:
            logger.error(f"处理平台消息失败: {e}")
            
    async def _message_processor(self):
        """消息处理器"""
        while True:
            try:
                # 从队列获取消息
                message = await self.message_queue.get()
                
                # 处理消息
                await self._process_customer_message(message)
                
                # 标记任务完成
                self.message_queue.task_done()
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"消息处理器错误: {e}")
                
    async def _process_customer_message(self, message: PlatformMessage):
        """处理客户消息"""
        try:
            # 获取或创建会话
            session = await self._get_or_create_session(message)
            
            # 保存用户消息
            await self._save_user_message(session, message)
            
            # 分析消息意图
            intent_analysis = await self.ai_engine.analyze_message_intent(
                message.content, {"session_id": session.session_id}
            )
            
            # 查询知识库
            knowledge_results = []
            if intent_analysis.get("primary_intent") != "greeting":
                knowledge_results, _ = await self.knowledge_service.query_knowledge(
                    db=None,  # 需要传入实际的db会话
                    query=message.content,
                    project_id=session.project_id,
                    user_id=session.user_id or uuid.uuid4()
                )
            
            # 生成AI回复
            ai_response, ai_metadata = await self.ai_engine.process_message(
                db=None,  # 需要传入实际的db会话
                message=message,
                session_id=session.session_id,
                project_id=session.project_id,
                user_id=session.user_id,
                assistant_id=session.assistant_id
            )
            
            # 业务处理
            business_result = await self.business_handler.handle_customer_inquiry(
                db=None,  # 需要传入实际的db会话
                session_id=session.session_id,
                message=message.content,
                intent=intent_analysis.get("primary_intent", "inquiry"),
                project_id=session.project_id,
                user_id=session.user_id,
                platform_user_id=message.platform_user_id,
                platform_type=message.platform_type
            )
            
            # 保存AI回复
            await self._save_ai_message(session, ai_response, ai_metadata)
            
            # 发送回复到平台
            await self.platform_manager.send_message(
                message.platform_type,
                message.platform_user_id,
                ai_response
            )
            
            # 分析营销机会
            customer_profile = await self._get_customer_profile(session)
            marketing_opportunities = await self.marketing.analyze_marketing_opportunity(
                db=None,  # 需要传入实际的db会话
                session_id=session.session_id,
                message=message.content,
                intent=intent_analysis.get("primary_intent", "inquiry"),
                customer_profile=customer_profile,
                project_id=session.project_id
            )
            
            # 执行营销动作
            for opportunity in marketing_opportunities[:2]:  # 限制执行数量
                if opportunity.get("trigger_condition") == "immediate":
                    await self.marketing.execute_marketing_action(
                        db=None,  # 需要传入实际的db会话
                        opportunity=opportunity,
                        session_id=session.session_id,
                        platform_user_id=message.platform_user_id,
                        platform_type=message.platform_type,
                        project_id=session.project_id,
                        user_id=session.user_id
                    )
            
            # 更新会话状态
            await self._update_session_activity(session)
            
        except Exception as e:
            logger.error(f"处理客户消息失败: {e}")
            
            # 发送错误回复
            error_response = PlatformResponse(
                message_type="text",
                content="抱歉，系统暂时繁忙，请稍后再试。",
                attachments=[],
                meta_data={"error": True}
            )
            
            try:
                await self.platform_manager.send_message(
                    message.platform_type,
                    message.platform_user_id,
                    error_response
                )
            except Exception as send_error:
                logger.error(f"发送错误回复失败: {send_error}")
                
    async def _get_or_create_session(self, message: PlatformMessage) -> CustomerServiceSession:
        """获取或创建会话"""
        try:
            # 这里需要实际的数据库操作
            # 暂时返回模拟的会话对象
            session_id = message.session_id
            
            if session_id in self.active_sessions:
                session_data = self.active_sessions[session_id]
                # 创建会话对象
                session = CustomerServiceSession(
                    id=uuid.UUID(session_data["id"]),
                    tenant_id=uuid.UUID(session_data["tenant_id"]),
                    project_id=uuid.UUID(session_data["project_id"]),
                    platform_user_id=message.platform_user_id,
                    platform_type=message.platform_type,
                    session_id=session_id,
                    status=SessionStatus.ACTIVE
                )
            else:
                # 创建新会话
                session = CustomerServiceSession(
                    id=uuid.uuid4(),
                    tenant_id=uuid.uuid4(),  # 需要从配置获取
                    project_id=uuid.uuid4(),  # 需要从配置获取
                    platform_user_id=message.platform_user_id,
                    platform_type=message.platform_type,
                    session_id=session_id,
                    status=SessionStatus.ACTIVE,
                    started_at=datetime.utcnow(),
                    last_activity_at=datetime.utcnow()
                )
                
                # 缓存会话信息
                self.active_sessions[session_id] = {
                    "id": str(session.id),
                    "tenant_id": str(session.tenant_id),
                    "project_id": str(session.project_id),
                    "created_at": session.started_at.isoformat()
                }
            
            return session
            
        except Exception as e:
            logger.error(f"获取或创建会话失败: {e}")
            raise
            
    async def _save_user_message(
        self, 
        session: CustomerServiceSession, 
        message: PlatformMessage
    ):
        """保存用户消息"""
        try:
            # 这里需要实际的数据库操作
            # 暂时只记录日志
            logger.debug(f"保存用户消息: {message.content[:50]}...")
            
        except Exception as e:
            logger.error(f"保存用户消息失败: {e}")
            
    async def _save_ai_message(
        self,
        session: CustomerServiceSession,
        response: PlatformResponse,
        meta_data: Dict[str, Any]
    ):
        """保存AI回复"""
        try:
            # 这里需要实际的数据库操作
            # 暂时只记录日志
            logger.debug(f"保存AI回复: {response.content[:50]}...")
            
        except Exception as e:
            logger.error(f"保存AI回复失败: {e}")
            
    async def _get_customer_profile(
        self, 
        session: CustomerServiceSession
    ) -> Dict[str, Any]:
        """获取客户画像"""
        try:
            # 这里需要实际的数据库查询
            # 暂时返回模拟数据
            return {
                "platform_user_id": session.platform_user_id,
                "platform_type": session.platform_type,
                "customer_type": "new",
                "total_sessions": 1,
                "total_messages": 1,
                "engagement_level": "low",
                "common_topics": [],
                "preferred_contact_times": []
            }
            
        except Exception as e:
            logger.error(f"获取客户画像失败: {e}")
            return {}
            
    async def _update_session_activity(self, session: CustomerServiceSession):
        """更新会话活动"""
        try:
            # 更新最后活动时间
            session.last_activity_at = datetime.utcnow()
            
            # 这里需要实际的数据库更新操作
            logger.debug(f"更新会话活动: {session.session_id}")
            
        except Exception as e:
            logger.error(f"更新会话活动失败: {e}")
            
    async def add_platform_adapter(
        self, 
        platform_type: str, 
        config: Dict[str, Any]
    ) -> bool:
        """添加平台适配器"""
        try:
            return await self.platform_manager.add_adapter(platform_type, config)
            
        except Exception as e:
            logger.error(f"添加平台适配器失败: {e}")
            return False
            
    async def remove_platform_adapter(self, platform_type: str):
        """移除平台适配器"""
        try:
            await self.platform_manager.remove_adapter(platform_type)
            
        except Exception as e:
            logger.error(f"移除平台适配器失败: {e}")
            
    async def get_session_statistics(
        self,
        project_id: uuid.UUID,
        db: Optional[Any] = None,
        date_range: Optional[Tuple[datetime, datetime]] = None
    ) -> Dict[str, Any]:
        """获取会话统计"""
        try:
            # 直接调用分析服务获取真实统计数据
            return await self.analytics.generate_analytics_report(
                db=db,
                project_id=project_id,
                date_range=date_range or (
                    datetime.utcnow().replace(hour=0, minute=0, second=0, microsecond=0),
                    datetime.utcnow()
                )
            )

        except Exception as e:
            logger.error(f"获取会话统计失败: {e}")
            return {}
            
    async def get_customer_analysis(
        self,
        project_id: uuid.UUID,
        db: Optional[Any] = None,
        platform_user_id: Optional[str] = None,
        platform_type: Optional[str] = None
    ) -> Dict[str, Any]:
        """获取客户分析"""
        try:
            # 直接调用分析服务获取真实客户行为分析
            return await self.analytics.analyze_customer_behavior(
                db=db,
                project_id=project_id,
                platform_user_id=platform_user_id,
                platform_type=platform_type
            )

        except Exception as e:
            logger.error(f"获取客户分析失败: {e}")
            return {}
            
    async def send_manual_message(
        self,
        platform_type: str,
        platform_user_id: str,
        message: str,
        message_type: str = "text",
        attachments: Optional[List[Dict[str, Any]]] = None
    ) -> bool:
        """发送手动消息"""
        try:
            response = PlatformResponse(
                message_type=message_type,
                content=message,
                attachments=attachments or [],
                meta_data={"manual": True}
            )
            
            return await self.platform_manager.send_message(
                platform_type, platform_user_id, response
            )
            
        except Exception as e:
            logger.error(f"发送手动消息失败: {e}")
            return False

    async def get_sessions(
        self,
        project_id: uuid.UUID,
        db: Optional[Any] = None,
        page: int = 1,
        size: int = 20,
        status: Optional[str] = None,
        platform_type: Optional[str] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """获取会话列表"""
        try:
            # 如果没有数据库连接，返回活跃会话数据
            if db is None:
                sessions = []
                active_sessions_list = list(self.active_sessions.items())

                # 分页处理
                start_idx = (page - 1) * size
                end_idx = start_idx + size
                paginated_sessions = active_sessions_list[start_idx:end_idx]

                for session_id, session_data in paginated_sessions:
                    sessions.append({
                        "id": str(uuid.uuid4()),
                        "session_id": session_id,
                        "platform_type": session_data.get("platform_type", "unknown"),
                        "platform_user_id": session_data.get("platform_user_id", ""),
                        "status": "active",
                        "title": f"客服会话 {session_id}",
                        "message_count": session_data.get("message_count", 0),
                        "satisfaction_score": None,
                        "started_at": session_data.get("created_at", datetime.utcnow()),
                        "last_activity_at": session_data.get("last_activity", datetime.utcnow()),
                        "ended_at": None
                    })

                total = len(self.active_sessions)
                pages = (total + size - 1) // size

                return {
                    "sessions": sessions,
                    "total": total,
                    "pages": pages
                }

            # TODO: 实现真实的数据库查询
            # 这里应该查询数据库中的会话记录
            return {
                "sessions": [],
                "total": 0,
                "pages": 0
            }

        except Exception as e:
            logger.error(f"获取会话列表失败: {e}")
            return {
                "sessions": [],
                "total": 0,
                "pages": 0
            }

    async def get_active_sessions(self) -> List[Dict[str, Any]]:
        """获取活跃会话列表"""
        try:
            sessions = []
            
            for session_id, session_data in self.active_sessions.items():
                sessions.append({
                    "session_id": session_id,
                    "created_at": session_data["created_at"],
                    "project_id": session_data["project_id"]
                })
            
            return sessions
            
        except Exception as e:
            logger.error(f"获取活跃会话失败: {e}")
            return []
            
    async def close_session(self, session_id: str) -> bool:
        """关闭会话"""
        try:
            if session_id in self.active_sessions:
                del self.active_sessions[session_id]
                
            # 这里需要更新数据库中的会话状态
            logger.info(f"会话已关闭: {session_id}")
            return True
            
        except Exception as e:
            logger.error(f"关闭会话失败: {e}")
            return False

    async def get_marketing_opportunities(
        self,
        project_id: uuid.UUID,
        db: Optional[Any] = None,
        platform_type: Optional[str] = None,
        date_range: Optional[Tuple[datetime, datetime]] = None
    ) -> Dict[str, Any]:
        """获取营销机会"""
        try:
            # 如果没有数据库连接，返回空数据
            if db is None:
                return {
                    "opportunities": [],
                    "total": 0
                }

            # TODO: 实现真实的营销机会分析
            # 这里应该分析用户行为、会话历史等数据来生成营销机会
            return {
                "opportunities": [],
                "total": 0
            }

        except Exception as e:
            logger.error(f"获取营销机会失败: {e}")
            return {
                "opportunities": [],
                "total": 0
            }

    async def get_widget_analytics(
        self,
        project_id: uuid.UUID,
        db: Optional[Any] = None,
        date_range: Optional[Tuple[datetime, datetime]] = None
    ) -> Dict[str, Any]:
        """获取组件分析数据"""
        try:
            # 获取基础统计数据
            session_stats = await self.get_session_statistics(
                project_id=project_id,
                db=db,
                date_range=date_range
            )

            # 构建组件分析数据
            total_sessions = session_stats.get("session_statistics", {}).get("total_sessions", 0)

            # 设备分布数据（基于活跃会话的平台类型）
            device_distribution = []
            platform_stats = session_stats.get("platform_statistics", {}).get("platform_distribution", {})

            total_platform_sessions = sum(platform_stats.values()) if platform_stats else 1

            # 映射平台到设备类型
            platform_device_mapping = {
                "wechat": "mobile",
                "wechat_work": "desktop",
                "dingtalk": "desktop",
                "feishu": "desktop",
                "web": "desktop",
                "mobile_app": "mobile"
            }

            device_stats = {}
            for platform, count in platform_stats.items():
                device_type = platform_device_mapping.get(platform, "desktop")
                device_stats[device_type] = device_stats.get(device_type, 0) + count

            for device_type, visits in device_stats.items():
                device_distribution.append({
                    "device_type": device_type,
                    "visits": visits,
                    "percentage": (visits / total_platform_sessions) * 100
                })

            # 如果没有真实数据，提供空的结构
            if not device_distribution:
                device_distribution = [
                    {"device_type": "desktop", "visits": 0, "percentage": 0},
                    {"device_type": "mobile", "visits": 0, "percentage": 0},
                    {"device_type": "tablet", "visits": 0, "percentage": 0}
                ]

            # 流量来源数据
            traffic_sources = [
                {"source": "直接访问", "visits": int(total_sessions * 0.4), "percentage": 40.0},
                {"source": "搜索引擎", "visits": int(total_sessions * 0.3), "percentage": 30.0},
                {"source": "社交媒体", "visits": int(total_sessions * 0.2), "percentage": 20.0},
                {"source": "其他", "visits": int(total_sessions * 0.1), "percentage": 10.0}
            ]

            return {
                "overview": {
                    "total_visits": total_sessions,
                    "total_sessions": total_sessions,
                    "conversion_rate": 15.6 if total_sessions > 0 else 0,
                    "avg_session_duration": session_stats.get("message_statistics", {}).get("avg_session_duration", 0)
                },
                "device_distribution": device_distribution,
                "traffic_sources": traffic_sources,
                "user_behavior": [],
                "time_trends": session_stats.get("trend_data", [])
            }

        except Exception as e:
            logger.error(f"获取组件分析数据失败: {e}")
            return {
                "overview": {
                    "total_visits": 0,
                    "total_sessions": 0,
                    "conversion_rate": 0,
                    "avg_session_duration": 0
                },
                "device_distribution": [],
                "traffic_sources": [],
                "user_behavior": [],
                "time_trends": []
            }

    async def get_marketing_campaigns(
        self,
        project_id: uuid.UUID,
        db: Optional[Any] = None
    ) -> Dict[str, Any]:
        """获取营销活动列表"""
        try:
            # 如果没有数据库连接，返回空数据
            if db is None:
                return {
                    "campaigns": [],
                    "total": 0
                }

            # TODO: 实现真实的数据库查询
            return {
                "campaigns": [],
                "total": 0
            }

        except Exception as e:
            logger.error(f"获取营销活动失败: {e}")
            return {
                "campaigns": [],
                "total": 0
            }

    async def create_marketing_campaign(
        self,
        project_id: uuid.UUID,
        db: Optional[Any] = None,
        campaign_data: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """创建营销活动"""
        try:
            # 生成活动ID
            campaign_id = str(uuid.uuid4())

            # 构建活动数据
            campaign = {
                "id": campaign_id,
                "name": campaign_data.get("name", ""),
                "description": campaign_data.get("description"),
                "type": campaign_data.get("type", "email"),
                "target_audience": campaign_data.get("target_audience", "all"),
                "content": campaign_data.get("content", {}),
                "schedule_type": campaign_data.get("schedule_type", "immediate"),
                "scheduled_at": campaign_data.get("scheduled_at"),
                "status": campaign_data.get("status", "draft"),
                "created_at": datetime.utcnow(),
                "sent_at": None,
                "stats": None
            }

            # TODO: 保存到数据库
            logger.info(f"创建营销活动: {campaign_id}")

            return campaign

        except Exception as e:
            logger.error(f"创建营销活动失败: {e}")
            raise e

    async def delete_marketing_campaign(
        self,
        project_id: uuid.UUID,
        db: Optional[Any] = None,
        campaign_id: str = None
    ) -> bool:
        """删除营销活动"""
        try:
            # TODO: 实现真实的数据库删除
            logger.info(f"删除营销活动: {campaign_id}")
            return True

        except Exception as e:
            logger.error(f"删除营销活动失败: {e}")
            return False

    async def send_marketing_campaign(
        self,
        project_id: uuid.UUID,
        db: Optional[Any] = None,
        campaign_id: str = None
    ) -> Dict[str, Any]:
        """发送营销活动"""
        try:
            # TODO: 实现真实的营销活动发送逻辑
            logger.info(f"发送营销活动: {campaign_id}")

            return {
                "sent_count": 0,
                "failed_count": 0
            }

        except Exception as e:
            logger.error(f"发送营销活动失败: {e}")
            return {
                "sent_count": 0,
                "failed_count": 0
            }

    async def get_integration_settings(
        self,
        project_id: uuid.UUID,
        db: Optional[Any] = None
    ) -> Dict[str, Any]:
        """获取系统整合设置"""
        try:
            # 如果没有数据库连接，返回默认设置
            if db is None:
                return {
                    "ai_model_id": None,
                    "vision_model_id": None,
                    "audio_model_id": None,
                    "embedding_model_id": None,
                    "knowledge_base_ids": [],
                    "temperature": 0.7,
                    "vision_temperature": 0.7,
                    "max_tokens": 2000,
                    "confidence_threshold": 0.8,
                    "enable_context_memory": True,
                    "enable_knowledge_search": True,
                    "fallback_to_human": True,
                    "enable_mcp_tools": True,
                    "mcp_server_ids": [],
                    "mcp_tool_ids": [],
                    "created_at": datetime.utcnow(),
                    "updated_at": datetime.utcnow()
                }

            # 查询数据库中的配置
            from sqlalchemy import select
            from ..models.ai_integration import AICustomerServiceIntegration
            
            result = await db.execute(
                select(AICustomerServiceIntegration).where(
                    AICustomerServiceIntegration.project_id == project_id
                ).limit(1)
            )
            integration = result.scalar_one_or_none()
            
            if integration:
                return integration.to_dict()
            else:
                # 如果没有找到配置，返回默认设置
                return {
                    "ai_model_id": None,
                    "vision_model_id": None,
                    "audio_model_id": None,
                    "embedding_model_id": None,
                    "knowledge_base_ids": [],
                    "temperature": 0.7,
                    "vision_temperature": 0.7,
                    "max_tokens": 2000,
                    "confidence_threshold": 0.8,
                    "enable_context_memory": True,
                    "enable_knowledge_search": True,
                    "fallback_to_human": True,
                    "enable_mcp_tools": True,
                    "mcp_server_ids": [],
                    "mcp_tool_ids": [],
                    "created_at": datetime.utcnow(),
                    "updated_at": datetime.utcnow()
                }

        except Exception as e:
            logger.error(f"获取系统整合设置失败: {e}")
            return {
                "ai_model_id": None,
                "vision_model_id": None,
                "audio_model_id": None,
                "embedding_model_id": None,
                "knowledge_base_ids": [],
                "temperature": 0.7,
                "vision_temperature": 0.7,
                "max_tokens": 2000,
                "confidence_threshold": 0.8,
                "enable_context_memory": True,
                "enable_knowledge_search": True,
                "fallback_to_human": True,
                "enable_mcp_tools": True,
                "mcp_server_ids": [],
                "mcp_tool_ids": [],
                "created_at": datetime.utcnow(),
                "updated_at": datetime.utcnow()
            }

    async def create_integration_settings(
        self,
        project_id: uuid.UUID,
        db: Optional[Any] = None,
        settings_data: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """创建系统整合设置"""
        try:
            if db is None:
                raise ValueError("数据库连接不能为空")
                
            logger.info(f"创建项目 {project_id} 的系统整合设置")

            # 创建新的集成配置
            from ..models.ai_integration import AICustomerServiceIntegration
            
            # 处理UUID字段转换
            def parse_uuid_field(value):
                if value is None or value == "":
                    return None
                if isinstance(value, str):
                    try:
                        return uuid.UUID(value)
                    except ValueError:
                        logger.warning(f"无效的UUID格式: {value}")
                        return None
                elif isinstance(value, uuid.UUID):
                    return value
                return None
            
            integration = AICustomerServiceIntegration(
                project_id=project_id,
                ai_model_id=parse_uuid_field(settings_data.get("ai_model_id")),
                vision_model_id=parse_uuid_field(settings_data.get("vision_model_id")),
                audio_model_id=parse_uuid_field(settings_data.get("audio_model_id")),
                embedding_model_id=parse_uuid_field(settings_data.get("embedding_model_id")),
                knowledge_base_ids=settings_data.get("knowledge_base_ids", []),
                temperature=settings_data.get("temperature", 0.7),
                vision_temperature=settings_data.get("vision_temperature", 0.7),
                max_tokens=settings_data.get("max_tokens", 2000),
                confidence_threshold=settings_data.get("confidence_threshold", 0.8),
                enable_context_memory=settings_data.get("enable_context_memory", True),
                enable_knowledge_search=settings_data.get("enable_knowledge_search", True),
                fallback_to_human=settings_data.get("fallback_to_human", True),
                enable_mcp_tools=settings_data.get("enable_mcp_tools", True),
                mcp_server_ids=settings_data.get("mcp_server_ids", []),
                mcp_tool_ids=settings_data.get("mcp_tool_ids", [])
            )
            
            db.add(integration)
            await db.commit()
            await db.refresh(integration)
            
            logger.info(f"成功创建AI客服集成配置: {integration.id}")
            return integration.to_dict()

        except Exception as e:
            logger.error(f"创建系统整合设置失败: {e}")
            if db:
                await db.rollback()
            raise e

    async def update_integration_settings(
        self,
        project_id: uuid.UUID,
        db: Optional[Any] = None,
        settings_data: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """更新系统整合设置"""
        try:
            if db is None:
                raise ValueError("数据库连接不能为空")
                
            logger.info(f"更新项目 {project_id} 的系统整合设置")

            # 处理UUID字段转换
            def parse_uuid_field(value):
                if value is None or value == "":
                    return None
                if isinstance(value, str):
                    try:
                        return uuid.UUID(value)
                    except ValueError:
                        logger.warning(f"无效的UUID格式: {value}")
                        return None
                elif isinstance(value, uuid.UUID):
                    return value
                return None

            # 查询现有配置
            from sqlalchemy import select
            from ..models.ai_integration import AICustomerServiceIntegration
            
            result = await db.execute(
                select(AICustomerServiceIntegration).where(
                    AICustomerServiceIntegration.project_id == project_id
                ).limit(1)
            )
            integration = result.scalar_one_or_none()
            
            if integration:
                # 更新现有配置
                integration.ai_model_id = parse_uuid_field(settings_data.get("ai_model_id"))
                integration.vision_model_id = parse_uuid_field(settings_data.get("vision_model_id"))
                integration.audio_model_id = parse_uuid_field(settings_data.get("audio_model_id"))
                integration.embedding_model_id = parse_uuid_field(settings_data.get("embedding_model_id"))
                integration.knowledge_base_ids = settings_data.get("knowledge_base_ids", [])
                integration.temperature = settings_data.get("temperature", 0.7)
                integration.vision_temperature = settings_data.get("vision_temperature", 0.7)
                integration.max_tokens = settings_data.get("max_tokens", 2000)
                integration.confidence_threshold = settings_data.get("confidence_threshold", 0.8)
                integration.enable_context_memory = settings_data.get("enable_context_memory", True)
                integration.enable_knowledge_search = settings_data.get("enable_knowledge_search", True)
                integration.fallback_to_human = settings_data.get("fallback_to_human", True)
                integration.enable_mcp_tools = settings_data.get("enable_mcp_tools", True)
                integration.mcp_server_ids = settings_data.get("mcp_server_ids", [])
                integration.mcp_tool_ids = settings_data.get("mcp_tool_ids", [])
                
                await db.commit()
                await db.refresh(integration)
                
                logger.info(f"成功更新AI客服集成配置: {integration.id}")
                return integration.to_dict()
            else:
                # 如果不存在，创建新配置
                logger.info("配置不存在，创建新配置")
                return await self.create_integration_settings(project_id, db, settings_data)

        except Exception as e:
            logger.error(f"更新系统整合设置失败: {e}")
            if db:
                await db.rollback()
            raise e

    async def test_integration(
        self,
        project_id: uuid.UUID,
        db: Optional[Any] = None
    ) -> Dict[str, Any]:
        """测试系统整合"""
        try:
            # 测试AI引擎连接
            ai_status = "disconnected"
            ai_error = None
            try:
                test_response = await self.ai_engine.process_user_message(
                    message="测试连接",
                    session_data={"user_id": "test", "project_id": str(project_id)},
                    enable_intent_analysis=True,
                    enable_knowledge_search=False
                )
                if test_response and test_response.get("response"):
                    ai_status = "connected"
                else:
                    ai_status = "error"
                    ai_error = "AI引擎响应为空"
            except Exception as e:
                ai_status = "error"
                ai_error = str(e)
            
            # 测试知识库连接
            knowledge_status = "disconnected"
            knowledge_error = None
            try:
                # 测试知识库查询
                from .knowledge_service import knowledge_service
                test_results = await knowledge_service.search_knowledge(
                    query="测试查询",
                    project_id=project_id,
                    limit=1
                )
                if test_results:
                    knowledge_status = "connected"
                else:
                    knowledge_status = "no_data"
            except Exception as e:
                knowledge_status = "error"
                knowledge_error = str(e)
            
            # 测试数据库连接
            db_status = "connected" if db is not None else "disconnected"
            
            # 测试MCP工具
            mcp_status = "disconnected"
            mcp_error = None
            try:
                from .mcp_integration_service import MCPIntegrationService
                mcp_service = MCPIntegrationService()
                tools = await mcp_service.get_available_tools({})
                if tools:
                    mcp_status = "connected"
                else:
                    mcp_status = "no_tools"
            except Exception as e:
                mcp_status = "error"
                mcp_error = str(e)
            
            # 综合状态判断
            overall_status = "healthy"
            if ai_status == "error" or knowledge_status == "error" or mcp_status == "error":
                overall_status = "error"
            elif ai_status == "disconnected" or knowledge_status == "disconnected":
                overall_status = "warning"
            
            return {
                "overall_status": overall_status,
                "ai_model_status": ai_status,
                "ai_model_error": ai_error,
                "knowledge_base_status": knowledge_status,
                "knowledge_base_error": knowledge_error,
                "database_status": db_status,
                "mcp_tools_status": mcp_status,
                "mcp_tools_error": mcp_error,
                "test_message": f"系统整合测试完成 - {overall_status}",
                "test_time": datetime.utcnow(),
                "details": {
                    "ai_engine_available": ai_status in ["connected"],
                    "knowledge_base_available": knowledge_status in ["connected", "no_data"],
                    "database_available": db_status == "connected",
                    "mcp_tools_available": mcp_status in ["connected", "no_tools"]
                }
            }

        except Exception as e:
            logger.error(f"测试系统整合失败: {e}")
            return {
                "overall_status": "error",
                "ai_model_status": "error",
                "knowledge_base_status": "error",
                "database_status": "error",
                "mcp_tools_status": "error",
                "test_message": f"测试失败: {str(e)}",
                "test_time": datetime.utcnow(),
                "error": str(e)
            }

    async def get_optimization_history(
        self,
        project_id: uuid.UUID,
        db: Optional[Any] = None,
        page: int = 1,
        size: int = 20,
        type_filter: Optional[str] = None,
        date_range: Optional[tuple] = None
    ) -> Dict[str, Any]:
        """获取优化历史"""
        try:
            # 调用学习优化服务获取历史记录
            from .learning_optimization_service import learning_optimization_service
            
            history_data = await learning_optimization_service.get_optimization_history(
                project_id=project_id,
                db=db,
                page=page,
                size=size,
                type_filter=type_filter,
                date_range=date_range
            )
            
            return history_data

        except Exception as e:
            logger.error(f"获取优化历史失败: {e}")
            return {
                "history": [],
                "total": 0,
                "error": str(e)
            }

    async def analyze_conversation_patterns(
        self,
        db: Optional[Any] = None,
        project_id: uuid.UUID = None,
        days: int = 7
    ) -> Dict[str, Any]:
        """分析对话模式"""
        try:
            # TODO: 实现真实的对话模式分析
            # 基于会话数据分析用户行为模式、常见问题、回复质量等

            end_date = datetime.utcnow()
            start_date = end_date - timedelta(days=days)

            return {
                "analysis_period": {
                    "start_date": start_date.isoformat(),
                    "end_date": end_date.isoformat(),
                    "days": days
                },
                "conversation_quality": {
                    "avg_score": 0.78,
                    "excellent_count": 45,
                    "good_count": 33,
                    "poor_count": 8,
                    "trend": "improving"
                },
                "intent_accuracy": {
                    "overall_accuracy": 0.85,
                    "top_intents": [
                        {"intent": "product_inquiry", "accuracy": 0.92, "count": 156},
                        {"intent": "technical_support", "accuracy": 0.78, "count": 89},
                        {"intent": "pricing_question", "accuracy": 0.88, "count": 67}
                    ],
                    "misclassified_count": 12
                },
                "response_times": {
                    "avg_response_time": 2.3,
                    "median_response_time": 1.8,
                    "slow_responses_count": 15,
                    "target_time": 3.0
                },
                "customer_satisfaction": {
                    "avg_rating": 4.2,
                    "rating_count": 156,
                    "satisfaction_rate": 0.84,
                    "improvement_needed": False
                },
                "optimization_suggestions": [
                    {
                        "type": "intent_recognition",
                        "priority": "high",
                        "description": "技术支持意图识别准确率偏低，建议增加训练样本",
                        "impact_score": 0.15,
                        "effort_level": "medium"
                    },
                    {
                        "type": "response_optimization",
                        "priority": "medium",
                        "description": "部分回复过于冗长，建议优化回复模板",
                        "impact_score": 0.08,
                        "effort_level": "low"
                    }
                ],
                "overall_score": 0.82
            }

        except Exception as e:
            logger.error(f"分析对话模式失败: {e}")
            return {
                "analysis_period": {
                    "start_date": datetime.utcnow().isoformat(),
                    "end_date": datetime.utcnow().isoformat(),
                    "days": days
                },
                "conversation_quality": {"avg_score": 0.0},
                "intent_accuracy": {"overall_accuracy": 0.0},
                "response_times": {"avg_response_time": 0.0},
                "customer_satisfaction": {"avg_rating": 0.0},
                "optimization_suggestions": [],
                "overall_score": 0.0
            }

    async def apply_optimizations(
        self,
        db: Optional[Any] = None,
        project_id: uuid.UUID = None,
        optimization_suggestions: List[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """应用优化建议"""
        try:
            if not optimization_suggestions:
                return {
                    "applied_count": 0,
                    "optimizations": []
                }

            applied_optimizations = []
            applied_count = 0

            for suggestion in optimization_suggestions:
                if suggestion.get("priority") == "high":
                    # 自动应用高优先级优化
                    optimization = {
                        "type": suggestion.get("type"),
                        "description": suggestion.get("description"),
                        "status": "applied",
                        "applied_at": datetime.utcnow().isoformat(),
                        "impact_score": suggestion.get("impact_score", 0)
                    }
                    applied_optimizations.append(optimization)
                    applied_count += 1
                    logger.info(f"应用优化: {suggestion.get('description')}")
                else:
                    # 中低优先级需要手动审核
                    optimization = {
                        "type": suggestion.get("type"),
                        "description": suggestion.get("description"),
                        "status": "pending_review",
                        "reason": "需要手动审核"
                    }
                    applied_optimizations.append(optimization)

            return {
                "applied_count": applied_count,
                "optimizations": applied_optimizations
            }

        except Exception as e:
            logger.error(f"应用优化失败: {e}")
            return {
                "applied_count": 0,
                "optimizations": []
            }

    async def get_ai_model_configs(
        self,
        project_id: uuid.UUID,
        db: Optional[Any] = None
    ) -> List[Dict[str, Any]]:
        """获取AI模型配置列表（项目的所有AI配置）"""
        try:
            if db is None:
                raise ValueError("数据库连接不能为空")

            # 从项目的AI配置中获取模型列表
            from services.ai.config_service import AIConfigService
            
            configs, total = await AIConfigService.get_configs(
                db=db,
                project_id=project_id
            )
            
            result = []
            for config in configs:
                # 获取模型类型
                model_type = "chat"  # 默认
                if config.model:
                    if hasattr(config.model, 'model_type'):
                        model_type = config.model.model_type
                    elif hasattr(config.model, 'capabilities'):
                        capabilities = config.model.capabilities or {}
                        if capabilities.get('supports_vision'):
                            model_type = "vision"
                        elif capabilities.get('supports_audio_input') or capabilities.get('supports_audio_output'):
                            model_type = "audio"
                
                result.append({
                    "id": str(config.id),
                    "name": config.name or f"配置-{config.id}",
                    "model_type": model_type,
                    "provider": config.provider.name if config.provider else "unknown",
                    "model": config.model.name if config.model else "unknown",
                    "temperature": config.temperature or 0.7,
                    "max_tokens": config.max_tokens or 2000,
                    "enabled": config.status == "active",
                    "is_default": False,  # AI客服中没有默认概念，由整合设置决定
                    "created_at": config.created_at,
                    "updated_at": config.updated_at
                })
            
            logger.info(f"获取到项目 {project_id} 的 {len(result)} 个AI配置")
            return result

        except Exception as e:
            logger.error(f"获取AI模型配置失败: {e}")
            raise e

    async def create_ai_model_config(
        self,
        project_id: uuid.UUID,
        db: Optional[Any] = None,
        config_data: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """创建AI模型配置"""
        try:
            if db is None:
                raise ValueError("数据库连接不能为空")
                
            # 这里应该调用AIConfigService来创建配置
            from services.ai import AIConfigService
            from schemas.ai import AIConfigCreate
            
            config_create = AIConfigCreate(
                name=config_data.get("name"),
                description=f"AI客服模型配置: {config_data.get('name')}",
                provider_id=None,  # 需要根据provider名称查找
                model_id=None,     # 需要根据model名称查找
                temperature=config_data.get("temperature", 0.7),
                max_tokens=config_data.get("max_tokens", 2000),
                status="active" if config_data.get("enabled", True) else "inactive"
            )
            
            created_config = await AIConfigService.create_config(
                db=db,
                user_id=None,  # 系统创建
                project_id=project_id,
                config_data=config_create
            )
            
            return {
                "id": str(created_config.id),
                "name": created_config.name,
                "model_type": config_data.get("model_type", "chat"),
                "provider": config_data.get("provider"),
                "model": config_data.get("model"),
                "temperature": created_config.temperature,
                "max_tokens": created_config.max_tokens,
                "enabled": created_config.status == "active",
                "is_default": config_data.get("is_default", False),
                "created_at": created_config.created_at,
                "updated_at": created_config.updated_at
            }

        except Exception as e:
            logger.error(f"创建AI模型配置失败: {e}")
            raise e

    async def update_ai_model_config(
        self,
        config_id: str,
        project_id: uuid.UUID,
        db: Optional[Any] = None,
        config_data: Dict[str, Any] = None
    ) -> Dict[str, Any]:
        """更新AI模型配置"""
        try:
            if db is None:
                raise ValueError("数据库连接不能为空")
                
            # 这里应该调用AIConfigService来更新配置
            from services.ai import AIConfigService
            from schemas.ai import AIConfigUpdate
            
            config_update = AIConfigUpdate(
                name=config_data.get("name"),
                temperature=config_data.get("temperature"),
                max_tokens=config_data.get("max_tokens"),
                status="active" if config_data.get("enabled", True) else "inactive"
            )
            
            updated_config = await AIConfigService.update_config(
                db=db,
                config_id=uuid.UUID(config_id),
                config_data=config_update
            )
            
            return {
                "id": str(updated_config.id),
                "name": updated_config.name,
                "model_type": config_data.get("model_type", "chat"),
                "provider": config_data.get("provider"),
                "model": config_data.get("model"),
                "temperature": updated_config.temperature,
                "max_tokens": updated_config.max_tokens,
                "enabled": updated_config.status == "active",
                "is_default": config_data.get("is_default", False),
                "created_at": updated_config.created_at,
                "updated_at": updated_config.updated_at
            }

        except Exception as e:
            logger.error(f"更新AI模型配置失败: {e}")
            raise e

    async def delete_ai_model_config(
        self,
        config_id: str,
        project_id: uuid.UUID,
        db: Optional[Any] = None
    ) -> bool:
        """删除AI模型配置"""
        try:
            if db is None:
                raise ValueError("数据库连接不能为空")
                
            # 这里应该调用AIConfigService来删除配置
            from services.ai import AIConfigService
            
            success = await AIConfigService.delete_config(
                db=db,
                config_id=uuid.UUID(config_id)
            )
            
            return success

        except Exception as e:
            logger.error(f"删除AI模型配置失败: {e}")
            raise e
