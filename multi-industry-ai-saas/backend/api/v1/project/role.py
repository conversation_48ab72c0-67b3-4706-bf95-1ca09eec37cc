import logging
logger = logging.getLogger(__name__)
logger.info("api/project/role.py loaded")
from typing import Dict, Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query, Path
from sqlalchemy.ext.asyncio import AsyncSession
from uuid import UUID
from sqlalchemy import select

from db.database import get_db
from core.auth import get_current_user
from api.deps import get_current_project
from services.role import RoleService, PermissionService, RolePermissionService
from schemas.role import (
    Role, RoleCreate, RoleUpdate, RoleList,
    Permission, PermissionCreate, PermissionUpdate,
    RolePermission, RolePermissionCreate, RolePermissionBulkUpdate
)
from models.project import Project

router = APIRouter()

# 角色相关路由
@router.get("", response_model=RoleList)
async def get_roles(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    is_system_role: Optional[bool] = None,
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """获取角色列表"""
    roles = await RoleService.get_roles(
        db, project.id, skip, limit, is_system_role
    )
    total = await RoleService.count_roles(
        db, project.id, is_system_role
    )
    return {
        "items": roles,
        "total": total,
        "page": skip // limit + 1,
        "size": limit,
        "pages": (total + limit - 1) // limit
    }

@router.get("/{role_id}", response_model=Role)
async def get_role(
    role_id: UUID = Path(...),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """获取角色详情"""
    role = await RoleService.get_role_by_id(db, role_id)
    if not role or role.project_id != project.id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="角色不存在"
        )
    return role

@router.post("", response_model=Role, status_code=status.HTTP_201_CREATED)
async def create_role(
    role: RoleCreate,
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """创建角色"""
    # 检查角色编码是否已存在
    existing_role = await RoleService.get_role_by_code(db, project.id, role.code)
    if existing_role:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="角色编码已存在"
        )
    
    # 确保项目ID正确
    role.project_id = project.id
    
    return await RoleService.create_role(db, role)

@router.put("/{role_id}", response_model=Role)
async def update_role(
    role_update: RoleUpdate,
    role_id: UUID = Path(...),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """更新角色"""
    # 检查角色是否存在
    role = await RoleService.get_role_by_id(db, role_id)
    if not role or role.project_id != project.id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="角色不存在"
        )
    
    # 检查角色编码是否已存在
    if role_update.code and role_update.code != role.code:
        existing_role = await RoleService.get_role_by_code(db, project.id, role_update.code)
        if existing_role:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="角色编码已存在"
            )
    
    updated_role = await RoleService.update_role(db, role_id, role_update)
    if not updated_role:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新角色失败"
        )
    return updated_role

@router.delete("/{role_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_role(
    role_id: UUID = Path(...),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """删除角色"""
    # 检查角色是否存在
    role = await RoleService.get_role_by_id(db, role_id)
    if not role or role.project_id != project.id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="角色不存在"
        )
    
    # 检查是否为系统角色
    if role.is_system_role:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="系统角色不能删除"
        )
    
    success = await RoleService.delete_role(db, role_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="无法删除已被使用的角色"
        )
    return None

# 权限相关路由
@router.get("/permissions", response_model=List[Permission])
async def get_permissions(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    module: Optional[str] = None,
    is_system_permission: Optional[bool] = None,
    db: AsyncSession = Depends(get_db)
):
    """获取权限列表"""
    return await PermissionService.get_permissions(
        db, skip, limit, module, is_system_permission
    )

@router.get("/permissions/{permission_id}", response_model=Permission)
async def get_permission(
    permission_id: UUID = Path(...),
    db: AsyncSession = Depends(get_db)
):
    """获取权限详情"""
    permission = await PermissionService.get_permission_by_id(db, permission_id)
    if not permission:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="权限不存在"
        )
    return permission

@router.post("/permissions", response_model=Permission, status_code=status.HTTP_201_CREATED)
async def create_permission(
    permission: PermissionCreate,
    db: AsyncSession = Depends(get_db)
):
    """创建权限"""
    # 检查权限编码是否已存在
    existing_permission = await PermissionService.get_permission_by_code(db, permission.code)
    if existing_permission:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="权限编码已存在"
        )
    
    return await PermissionService.create_permission(db, permission)

@router.put("/permissions/{permission_id}", response_model=Permission)
async def update_permission(
    permission_update: PermissionUpdate,
    permission_id: UUID = Path(...),
    db: AsyncSession = Depends(get_db)
):
    """更新权限"""
    # 检查权限是否存在
    permission = await PermissionService.get_permission_by_id(db, permission_id)
    if not permission:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="权限不存在"
        )
    
    # 检查权限编码是否已存在
    if permission_update.code and permission_update.code != permission.code:
        existing_permission = await PermissionService.get_permission_by_code(db, permission_update.code)
        if existing_permission:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="权限编码已存在"
            )
    
    updated_permission = await PermissionService.update_permission(db, permission_id, permission_update)
    if not updated_permission:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新权限失败"
        )
    return updated_permission

@router.delete("/permissions/{permission_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_permission(
    permission_id: UUID = Path(...),
    db: AsyncSession = Depends(get_db)
):
    """删除权限"""
    permission = await PermissionService.get_permission_by_id(db, permission_id)
    if not permission:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="权限不存在"
        )
    
    success = await PermissionService.delete_permission(db, permission_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="无法删除已被使用的权限"
        )
    return None

# 角色权限关联相关路由
@router.get("/roles/{role_id}/permissions", response_model=List[RolePermission])
async def get_role_permissions(
    role_id: UUID = Path(...),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """获取角色的权限列表"""
    # 检查角色是否存在
    role = await RoleService.get_role_by_id(db, role_id)
    if not role or role.project_id != project.id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="角色不存在"
        )
    
    return await RolePermissionService.get_role_permissions(db, role_id)

@router.post("/{role_id}/permissions", response_model=RolePermission, status_code=status.HTTP_201_CREATED)
async def create_role_permission(
    role_permission: RolePermissionCreate,
    role_id: UUID = Path(...),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """为角色添加权限"""
    # 检查角色是否存在
    role = await RoleService.get_role_by_id(db, role_id)
    if not role or role.project_id != project.id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="角色不存在"
        )
    
    # 检查权限是否存在
    permission = await PermissionService.get_permission_by_id(db, role_permission.permission_id)
    if not permission:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="权限不存在"
        )
    
    # 检查关联是否已存在
    existing_role_permission = await RolePermissionService.get_role_permission(db, role_id, role_permission.permission_id)
    if existing_role_permission:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="角色权限关联已存在"
        )
    
    role_permission.role_id = role_id
    return await RolePermissionService.create_role_permission(db, role_permission)

@router.delete("/{role_id}/permissions/{permission_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_role_permission(
    role_id: UUID = Path(...),
    permission_id: UUID = Path(...),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """删除角色权限关联"""
    # 检查角色是否存在
    role = await RoleService.get_role_by_id(db, role_id)
    if not role or role.project_id != project.id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="角色不存在"
        )
    
    # 检查权限是否存在
    permission = await PermissionService.get_permission_by_id(db, permission_id)
    if not permission:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="权限不存在"
        )
    
    success = await RolePermissionService.delete_role_permission(db, role_id, permission_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="角色权限关联不存在"
        )
    return None

@router.put("/{role_id}/permissions", status_code=status.HTTP_200_OK)
async def bulk_update_role_permissions(
    role_permissions: RolePermissionBulkUpdate,
    role_id: UUID = Path(...),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """批量更新角色权限"""
    # 检查角色是否存在
    role = await RoleService.get_role_by_id(db, role_id)
    if not role or role.project_id != project.id:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="角色不存在"
        )
    
    # 检查所有权限是否存在
    for permission_id in role_permissions.permission_ids:
        permission = await PermissionService.get_permission_by_id(db, permission_id)
        if not permission:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"权限 {permission_id} 不存在"
            )
    
    result = await RolePermissionService.bulk_update_role_permissions(db, role_id, role_permissions.permission_ids)
    return {"message": "角色权限更新成功", "count": len(result)}
