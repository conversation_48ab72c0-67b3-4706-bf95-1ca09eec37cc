#!/bin/bash

# 获取脚本所在目录
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"

# 设置数据库连接参数
DB_CONTAINER="retail-ai-saas-db-1"  # 数据库容器名称
DB_NAME="retail_ai_saas"            # 数据库名称
DB_USER="postgres"                  # 数据库用户名

echo "正在更新数据库..."

# 执行SQL脚本
docker exec -i $DB_CONTAINER psql -U $DB_USER -d $DB_NAME -f - < "$SCRIPT_DIR/update_tenant_data_sharing.sql"

# 检查执行结果
if [ $? -eq 0 ]; then
    echo "数据库更新成功！"
else
    echo "数据库更新失败！"
    exit 1
fi

echo "完成！"
