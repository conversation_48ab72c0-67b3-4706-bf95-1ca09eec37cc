import React, { createContext, useContext, useState, useEffect } from 'react';
import { message } from 'antd';
import { auth } from '../services';
import rbacService from '../services/rbacService';

// 创建认证上下文
const AuthContext = createContext();

/**
 * 认证上下文提供者
 * 管理用户认证状态和操作
 */
export const AuthProvider = ({ children }) => {

  // 状态
  const [user, setUser] = useState(null);
  const [token, setToken] = useState(localStorage.getItem('token'));
  const [loading, setLoading] = useState(false);
  const [initialized, setInitialized] = useState(false);
  const [projectId, setProjectId] = useState(localStorage.getItem('project_id'));

  // 检查是否已认证
  const isAuthenticated = !!token;

  // 初始化认证状态
  useEffect(() => {
    const initAuth = async () => {
      const storedToken = localStorage.getItem('token');
      if (storedToken) {
        try {
          setLoading(true);
          // 获取当前用户信息
          const userData = await auth.getCurrentUser();
          console.log('初始化认证状态，获取到的用户数据:', userData);

          // 保存用户数据到localStorage
          localStorage.setItem('user_data', JSON.stringify(userData));

          // 设置用户状态
          setUser(userData);

          // 确保项目ID被正确保存到localStorage
          if (userData.project_id) {
            console.log('从用户数据中获取到项目ID:', userData.project_id);
            localStorage.setItem('project_id', userData.project_id);
            setProjectId(userData.project_id);
          }

          // 如果用户数据中没有项目ID，但有项目角色，尝试调用项目列表API获取项目ID
          if (!userData.project_id && userData.project_role) {
            try {
              console.log('用户有项目角色但没有项目ID，尝试获取项目列表');
              const projectsResponse = await fetch('/api/v1/auth/user/projects', {
                headers: {
                  'Authorization': `Bearer ${storedToken}`
                }
              });

              if (projectsResponse.ok) {
                const projectsData = await projectsResponse.json();
                console.log('获取到的项目列表:', projectsData);

                if (projectsData && Array.isArray(projectsData.data) && projectsData.data.length > 0) {
                  if (projectsData.data.length === 1) {
                    // 如果只关联了一个项目，直接进入该项目
                    const project = projectsData.data[0];
                    localStorage.setItem('project_id', project.id);
                    localStorage.setItem('project_name', project.name);
                    setProjectId(project.id);

                    // 更新用户数据中的项目ID
                    const updatedUserData = { ...userData, project_id: project.id };
                    localStorage.setItem('user_data', JSON.stringify(updatedUserData));
                    setUser(updatedUserData);
                  }
                }
              }
            } catch (error) {
              console.error('获取项目列表失败:', error);
            }
          }
        } catch (error) {
          console.error('获取用户信息失败:', error);
          // 清除无效的token
          localStorage.removeItem('token');
          setToken(null);
        } finally {
          setLoading(false);
          setInitialized(true);
        }
      } else {
        setInitialized(true);
      }
    };

    initAuth();
  }, []);

  // 登录
  const login = async (username, password) => {
    try {
      setLoading(true);

      // 获取当前路径，判断是否是特殊登录页面
      const currentPath = window.location.pathname;
      const isServiceProviderLogin = currentPath.includes('/service-provider/login');
      const isAdminLogin = currentPath.includes('/admin/login');

      // 设置用户类型标志
      if (isServiceProviderLogin) {
        localStorage.setItem('user_type', 'service_provider');
      } else if (isAdminLogin) {
        localStorage.setItem('user_type', 'admin');
      } else {
        localStorage.setItem('user_type', 'tenant');
      }

      console.log('登录尝试:', { username, password, currentPath });

      // 调用登录API - 简化为只传递用户名和密码
      const response = await auth.login(username, password);
      console.log('登录响应:', response);

      // 保存token和用户信息
      const {
        access_token,
        user_id,
        project_role,
        project_role_name,
        is_project_admin,
        project_id,
        ...userData
      } = response;

      // 保存用户信息，包括项目角色信息
      const userDataToSave = {
        id: user_id,
        project_role,
        project_role_name,
        is_project_admin,
        project_id,
        ...userData
      };

      localStorage.setItem('token', access_token);
      localStorage.setItem('user_data', JSON.stringify(userDataToSave));

      // 如果有项目ID，保存到localStorage
      if (project_id) {
        localStorage.setItem('project_id', project_id);
        console.log('登录时保存项目ID到localStorage:', project_id);
      } else if (userData.project_id) {
        // 如果响应中没有project_id但userData中有，也保存到localStorage
        localStorage.setItem('project_id', userData.project_id);
        console.log('登录时从userData保存项目ID到localStorage:', userData.project_id);
      }

      setToken(access_token);
      setUser(userDataToSave);

      message.success('登录成功');

      // 根据用户角色和登录入口决定导航目标
      console.log('用户角色信息:', {
        is_system_admin: userData.is_system_admin,
        is_super_admin: userData.is_super_admin,
        is_service_provider_admin: userData.is_service_provider_admin,
        is_tenant_admin: userData.is_tenant_admin,
        project_role: userData.project_role,
        role: userData.role
      });

      // 保存用户类型，用于后续路由判断
      if (userData.is_system_admin || userData.is_super_admin) {
        localStorage.setItem('user_type', 'admin');
      } else if (userData.is_service_provider_admin) {
        localStorage.setItem('user_type', 'service_provider');
      } else if (userData.is_tenant_admin) {
        localStorage.setItem('user_type', 'tenant');
      } else {
        localStorage.setItem('user_type', 'user');
      }

      // 普通用户（项目管理员或其他角色）
      // 检查用户是否关联了多个项目
      try {
        // 添加访问方式检测和调试日志
        const currentHostname = window.location.hostname;
        const currentOrigin = window.location.origin;
        const isIPAccess = currentHostname.match(/^\d+\.\d+\.\d+\.\d+$/);
        const isDomainAccess = currentHostname === 'saas.houshanai.com';
        
        console.log('=== 登录调试信息 ===');
        console.log('当前访问方式:', {
          hostname: currentHostname,
          origin: currentOrigin,
          isIPAccess: !!isIPAccess,
          isDomainAccess: isDomainAccess,
          protocol: window.location.protocol
        });
        
        // 获取用户关联的项目列表
        const projectsApiUrl = '/api/v1/auth/user/projects';
        console.log('准备调用项目列表API:', projectsApiUrl);
        console.log('完整URL将解析为:', new URL(projectsApiUrl, window.location.origin).href);
        
        const projectsResponse = await fetch('/api/v1/auth/user/projects', {
          headers: {
            'Authorization': `Bearer ${response.access_token}`
          }
        });

        console.log('项目列表API响应状态:', projectsResponse.status);

        if (projectsResponse.ok) {
          const projectsData = await projectsResponse.json();
          console.log('用户关联的项目:', projectsData);

          if (projectsData && Array.isArray(projectsData.data) && projectsData.data.length > 0) {
            console.log('用户关联项目数量:', projectsData.data.length);
            if (projectsData.data.length === 1) {
              // 如果只关联了一个项目，直接进入该项目
              const project = projectsData.data[0];
              localStorage.setItem('project_id', project.id);
              localStorage.setItem('project_name', project.name);
              setProjectId(project.id);
              console.log('只有一个项目，直接进入:', project.name, 'ID:', project.id);
            } else {
              // 如果关联了多个项目，进入项目选择页面
              localStorage.removeItem('project_id');
              localStorage.removeItem('project_name');
              setProjectId(null);
              console.log('多个项目，进入项目选择页面');
            }
          } else {
            console.log('没有关联项目，检查用户数据中的project_id');
            // 如果没有关联项目，但有项目ID，使用它
            if (userData.project_id) {
              localStorage.setItem('project_id', userData.project_id);
              setProjectId(userData.project_id);
              console.log('使用用户数据中的project_id:', userData.project_id);
            } else {
              localStorage.removeItem('project_id');
              localStorage.removeItem('project_name');
              setProjectId(null);
              console.log('完全没有项目ID，清除相关数据');
            }
          }
        } else {
          console.error('项目列表API调用失败:', projectsResponse.status, projectsResponse.statusText);
          // 如果API调用失败，回退到原来的逻辑
          if (userData.project_id) {
            localStorage.setItem('project_id', userData.project_id);
            setProjectId(userData.project_id);
            console.log('API失败，回退使用用户数据中的project_id:', userData.project_id);
          } else {
            localStorage.removeItem('project_id');
            localStorage.removeItem('project_name');
            setProjectId(null);
            console.log('API失败且无project_id，清除相关数据');
          }
        }
      } catch (error) {
        console.error('获取用户项目列表失败:', error);
        // 如果出错，回退到原来的逻辑
        if (userData.project_id) {
          localStorage.setItem('project_id', userData.project_id);
          setProjectId(userData.project_id);
          console.log('异常处理，回退使用用户数据中的project_id:', userData.project_id);
        } else {
          localStorage.removeItem('project_id');
          localStorage.removeItem('project_name');
          setProjectId(null);
          console.log('异常处理且无project_id，清除相关数据');
        }
      }

      // 使用 RBAC 服务获取用户应该跳转的仪表盘路径
      const redirectPath = rbacService.getDashboardPath(userData);

      console.log('=== RBAC重定向信息 ===');
      console.log('最终重定向路径:', redirectPath);
      console.log('当前项目ID:', localStorage.getItem('project_id'));
      console.log('===============');

      // 保存登录状态，防止重定向循环
      localStorage.setItem('isLoggedIn', 'true');

      // 直接使用window.location.replace进行导航，避免历史记录中出现登录页
      // 使用replace而不是href，这样不会在历史记录中添加登录页
      window.location.replace(redirectPath);

      return response;
    } catch (error) {
      console.error('登录失败:', error);
      message.error('登录失败: ' + (error.response?.data?.detail || '用户名或密码错误'));
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // 注册
  const register = async (userData) => {
    try {
      setLoading(true);
      const response = await auth.register(userData);
      message.success('注册成功，请登录');
      return response;
    } catch (error) {
      console.error('注册失败:', error);
      message.error('注册失败: ' + (error.response?.data?.detail || '注册信息有误'));
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // 登出
  const logout = () => {
    // 获取用户类型，用于决定登出后导航到哪个登录页面
    const userType = localStorage.getItem('user_type');

    // 清除所有认证相关的本地存储
    localStorage.removeItem('token');
    localStorage.removeItem('user_type');
    localStorage.removeItem('user_data');
    localStorage.removeItem('isLoggedIn');
    localStorage.removeItem('project_id');
    localStorage.removeItem('project_name');
    localStorage.removeItem('recentProjects');

    // 清除状态
    setToken(null);
    setUser(null);

    message.success('已退出登录');

    // 根据用户类型导航到相应的登录页面
    let loginPath = '/login';
    if (userType === 'admin') {
      loginPath = '/admin/login';
    } else if (userType === 'service_provider') {
      loginPath = '/service-provider/login';
    }

    // 使用window.location.replace进行导航，确保完全跳转到登录页面
    window.location.replace(loginPath);
  };



  // 检查用户是否有特定权限
  const hasPermission = (permission) => {
    if (!user) return false;

    // 系统管理员或超级管理员拥有所有权限
    if (user.is_system_admin || user.is_super_admin) return true;

    // 服务商管理员拥有服务商相关权限
    if (user.is_service_provider_admin && permission.startsWith('service_provider_')) return true;

    // 租户管理员拥有租户相关权限
    if (user.is_tenant_admin && permission.startsWith('tenant_')) return true;

    // 检查用户权限
    return user.permissions && user.permissions[permission];
  };

  // 检查用户是否有特定角色
  const hasRole = (role) => {
    if (!user) return false;

    console.log('AuthContext.hasRole - 检查角色:', role);

    // 使用 RBAC 服务检查用户是否有所需角色
    return rbacService.hasRole(user, role);
  };

  // 上下文值
  const contextValue = {
    user,
    token,
    loading,
    isAuthenticated,
    initialized,
    projectId,
    login,
    register,
    logout,
    hasPermission,
    hasRole,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

// 自定义Hook，用于在组件中使用认证上下文
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth必须在AuthProvider内部使用');
  }
  return context;
};

export default AuthContext;
