import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Typography,
  Input,
  Row,
  Col,
  Dropdown,
  Menu,
  Modal,
  Form,
  Select,
  Switch,
  Tooltip,
  Badge,
  Avatar,
  Tabs,
  Statistic,
  Divider,
  List,
  Rate,
  Progress,
  Empty
} from 'antd';
import {
  AppstoreOutlined,
  PlusOutlined,
  SearchOutlined,
  FilterOutlined,
  MoreOutlined,
  EditOutlined,
  SettingOutlined,
  DeleteOutlined,
  ExclamationCircleOutlined,
  CloudDownloadOutlined,
  CloudUploadOutlined,
  ShopOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  InfoCircleOutlined,
  StarOutlined,
  DownloadOutlined,
  UploadOutlined,
  CodeOutlined,
  ApiOutlined,
  RobotOutlined,
  DashboardOutlined,
  FileTextOutlined,
  TeamOutlined,
  GlobalOutlined,
  BarChartOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import apiService from '../../services/api';
import { useAuth } from '../../contexts/AuthContext';
import './PluginManagement.css';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;
const { confirm } = Modal;
const { Meta } = Card;

const PluginManagement = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [plugins, setPlugins] = useState([]);
  const [searchText, setSearchText] = useState('');
  const [filterCategory, setFilterCategory] = useState('all');
  const [activeTab, setActiveTab] = useState('all');
  const [modalVisible, setModalVisible] = useState(false);
  const [selectedPlugin, setSelectedPlugin] = useState(null);
  const [form] = Form.useForm();
  const [statistics, setStatistics] = useState({
    total: 0,
    active: 0,
    inactive: 0,
    tenantUsage: 0
  });

  // 模拟插件数据
  const mockPlugins = [
    {
      id: 'plugin-1',
      name: '数据分析插件',
      description: '提供高级数据分析功能，支持多维度数据可视化和报表生成',
      category: 'analytics',
      version: '1.2.3',
      author: 'AI系统团队',
      price: 0,
      is_free: true,
      status: 'active',
      icon: <BarChartOutlined />,
      rating: 4.5,
      downloads: 1280,
      tenant_usage: 18,
      created_at: '2023-01-15',
      updated_at: '2023-05-20',
      features: ['数据可视化', '自定义报表', '数据导出', 'API集成'],
      requirements: ['需要数据库访问权限', '至少5GB存储空间']
    },
    {
      id: 'plugin-2',
      name: '智能客服机器人',
      description: '基于AI的智能客服机器人，可自动回答客户问题，提高客服效率',
      category: 'ai',
      version: '2.0.1',
      author: 'AI助手团队',
      price: 299,
      is_free: false,
      status: 'active',
      icon: <RobotOutlined />,
      rating: 4.8,
      downloads: 950,
      tenant_usage: 12,
      created_at: '2023-02-10',
      updated_at: '2023-06-15',
      features: ['自然语言处理', '多轮对话', '知识库集成', '情感分析'],
      requirements: ['需要API调用权限', '至少2GB内存']
    },
    {
      id: 'plugin-3',
      name: '钉钉集成',
      description: '与钉钉平台无缝集成，支持消息推送、任务分配和审批流程',
      category: 'integration',
      version: '1.5.0',
      author: '协同办公团队',
      price: 199,
      is_free: false,
      status: 'active',
      icon: <GlobalOutlined />,
      rating: 4.2,
      downloads: 820,
      tenant_usage: 15,
      created_at: '2023-03-05',
      updated_at: '2023-06-01',
      features: ['消息推送', '任务分配', '审批流程', '用户同步'],
      requirements: ['需要钉钉企业账号', '需要外网访问权限']
    },
    {
      id: 'plugin-4',
      name: '微信小程序',
      description: '快速创建与系统集成的微信小程序，支持商品展示、会员管理等功能',
      category: 'integration',
      version: '2.1.0',
      author: '移动应用团队',
      price: 399,
      is_free: false,
      status: 'inactive',
      icon: <GlobalOutlined />,
      rating: 4.6,
      downloads: 760,
      tenant_usage: 8,
      created_at: '2023-01-20',
      updated_at: '2023-05-10',
      features: ['商品展示', '会员管理', '订单处理', '支付集成'],
      requirements: ['需要微信公众号', '需要商户支付权限']
    },
    {
      id: 'plugin-5',
      name: '高级报表生成器',
      description: '强大的报表生成工具，支持自定义模板、多种图表类型和数据导出',
      category: 'reporting',
      version: '1.8.2',
      author: '数据团队',
      price: 299,
      is_free: false,
      status: 'active',
      icon: <FileTextOutlined />,
      rating: 4.3,
      downloads: 680,
      tenant_usage: 14,
      created_at: '2023-02-25',
      updated_at: '2023-06-10',
      features: ['自定义模板', '多种图表', '数据导出', '定时生成'],
      requirements: ['需要数据库读取权限', '至少3GB存储空间']
    },
    {
      id: 'plugin-6',
      name: 'AI图像识别',
      description: '基于深度学习的图像识别功能，可识别商品、人脸、文字等',
      category: 'ai',
      version: '1.3.0',
      author: 'AI视觉团队',
      price: 499,
      is_free: false,
      status: 'active',
      icon: <RobotOutlined />,
      rating: 4.7,
      downloads: 520,
      tenant_usage: 6,
      created_at: '2023-03-15',
      updated_at: '2023-06-20',
      features: ['商品识别', '人脸识别', '文字识别', '场景分析'],
      requirements: ['需要GPU支持', '至少4GB内存']
    },
    {
      id: 'plugin-7',
      name: '库存优化',
      description: '基于AI的库存优化工具，预测销售趋势，优化库存水平',
      category: 'retail',
      version: '1.0.5',
      author: '零售解决方案团队',
      price: 399,
      is_free: false,
      status: 'inactive',
      icon: <DashboardOutlined />,
      rating: 4.1,
      downloads: 320,
      tenant_usage: 5,
      created_at: '2023-04-10',
      updated_at: '2023-06-05',
      features: ['销售预测', '库存优化', '补货建议', '季节性分析'],
      requirements: ['需要历史销售数据', '至少6个月的数据']
    },
    {
      id: 'plugin-8',
      name: '多渠道营销',
      description: '整合多个营销渠道，统一管理营销活动和客户数据',
      category: 'marketing',
      version: '2.2.1',
      author: '营销团队',
      price: 299,
      is_free: false,
      status: 'active',
      icon: <GlobalOutlined />,
      rating: 4.4,
      downloads: 480,
      tenant_usage: 9,
      created_at: '2023-03-20',
      updated_at: '2023-06-25',
      features: ['渠道整合', '活动管理', '客户分析', '效果追踪'],
      requirements: ['需要第三方API访问权限', '需要客户数据访问权限']
    }
  ];

  // 加载插件数据
  useEffect(() => {
    const fetchPlugins = async () => {
      try {
        setLoading(true);
        // 实际项目中应该调用API获取数据
        // const response = await apiService.getPlugins();
        // setPlugins(response);

        // 使用模拟数据
        setPlugins(mockPlugins);

        // 计算统计数据
        const stats = {
          total: mockPlugins.length,
          active: mockPlugins.filter(p => p.status === 'active').length,
          inactive: mockPlugins.filter(p => p.status === 'inactive').length,
          tenantUsage: mockPlugins.reduce((sum, p) => sum + p.tenant_usage, 0)
        };
        setStatistics(stats);
      } catch (error) {
        console.error('获取插件列表失败:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchPlugins();
  }, []);

  // 过滤插件数据
  const getFilteredPlugins = () => {
    return plugins.filter(plugin => {
      const matchesSearch = plugin.name.toLowerCase().includes(searchText.toLowerCase()) ||
                           plugin.description.toLowerCase().includes(searchText.toLowerCase());
      const matchesCategory = filterCategory === 'all' || plugin.category === filterCategory;
      const matchesTab = activeTab === 'all' ||
                        (activeTab === 'free' && plugin.is_free) ||
                        (activeTab === 'paid' && !plugin.is_free) ||
                        (activeTab === 'active' && plugin.status === 'active') ||
                        (activeTab === 'inactive' && plugin.status === 'inactive');

      return matchesSearch && matchesCategory && matchesTab;
    });
  };

  // 显示插件详情
  const showPluginDetail = (plugin) => {
    setSelectedPlugin(plugin);
    setModalVisible(true);
  };

  // 获取插件图标
  const getPluginIcon = (category) => {
    switch (category) {
      case 'analytics':
        return <BarChartOutlined style={{ fontSize: 24, color: '#1890ff' }} />;
      case 'ai':
        return <RobotOutlined style={{ fontSize: 24, color: '#722ed1' }} />;
      case 'integration':
        return <ApiOutlined style={{ fontSize: 24, color: '#52c41a' }} />;
      case 'reporting':
        return <FileTextOutlined style={{ fontSize: 24, color: '#fa8c16' }} />;
      case 'retail':
        return <ShopOutlined style={{ fontSize: 24, color: '#eb2f96' }} />;
      case 'marketing':
        return <GlobalOutlined style={{ fontSize: 24, color: '#f5222d' }} />;
      default:
        return <AppstoreOutlined style={{ fontSize: 24, color: '#1890ff' }} />;
    }
  };

  // 获取插件分类名称
  const getCategoryName = (category) => {
    const categoryMap = {
      'analytics': '数据分析',
      'ai': '人工智能',
      'integration': '系统集成',
      'reporting': '报表工具',
      'retail': '零售功能',
      'marketing': '营销工具'
    };
    return categoryMap[category] || category;
  };

  return (
    <div className="plugin-management">
      <div className="page-header">
        <Title level={2}><AppstoreOutlined /> 插件管理</Title>
        <Text type="secondary">管理和分配插件给您的租户，提升系统功能</Text>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} className="stats-row">
        <Col xs={24} sm={12} md={6}>
          <Card bordered={false} className="stat-card">
            <Statistic
              title="插件总数"
              value={statistics.total}
              prefix={<AppstoreOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card bordered={false} className="stat-card">
            <Statistic
              title="已启用插件"
              value={statistics.active}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card bordered={false} className="stat-card">
            <Statistic
              title="未启用插件"
              value={statistics.inactive}
              prefix={<CloseCircleOutlined />}
              valueStyle={{ color: '#bfbfbf' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={6}>
          <Card bordered={false} className="stat-card">
            <Statistic
              title="租户使用量"
              value={statistics.tenantUsage}
              prefix={<ShopOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
      </Row>

      <Card bordered={false} className="plugin-card">
        {/* 搜索和过滤 */}
        <div className="table-toolbar">
          <Input
            placeholder="搜索插件名称或描述"
            prefix={<SearchOutlined />}
            value={searchText}
            onChange={e => setSearchText(e.target.value)}
            style={{ width: 250 }}
            allowClear
          />
          <Space>
            <Select
              placeholder="分类筛选"
              style={{ width: 150 }}
              value={filterCategory}
              onChange={setFilterCategory}
              allowClear
            >
              <Select.Option value="all">全部分类</Select.Option>
              <Select.Option value="analytics">数据分析</Select.Option>
              <Select.Option value="ai">人工智能</Select.Option>
              <Select.Option value="integration">系统集成</Select.Option>
              <Select.Option value="reporting">报表工具</Select.Option>
              <Select.Option value="retail">零售功能</Select.Option>
              <Select.Option value="marketing">营销工具</Select.Option>
            </Select>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => navigate('/service-provider/plugins/upload')}
            >
              上传插件
            </Button>
          </Space>
        </div>

        {/* 插件列表标签页 */}
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          className="plugin-tabs"
        >
          <TabPane tab="全部插件" key="all" />
          <TabPane tab="免费插件" key="free" />
          <TabPane tab="付费插件" key="paid" />
          <TabPane tab="已启用" key="active" />
          <TabPane tab="未启用" key="inactive" />
        </Tabs>

        {/* 插件列表 */}
        <Row gutter={[16, 16]} className="plugin-list">
          {getFilteredPlugins().length > 0 ? (
            getFilteredPlugins().map(plugin => (
              <Col xs={24} sm={12} md={8} lg={6} key={plugin.id}>
                <Card
                  hoverable
                  className="plugin-item"
                  cover={
                    <div className="plugin-cover">
                      <Avatar
                        size={64}
                        icon={plugin.icon || <AppstoreOutlined />}
                        className="plugin-avatar"
                      />
                      <Badge
                        status={plugin.status === 'active' ? 'success' : 'default'}
                        text={plugin.status === 'active' ? '已启用' : '未启用'}
                        className="plugin-status"
                      />
                    </div>
                  }
                  actions={[
                    <Tooltip title="查看详情">
                      <InfoCircleOutlined key="info" onClick={() => showPluginDetail(plugin)} />
                    </Tooltip>,
                    <Tooltip title="分配给租户">
                      <ShopOutlined key="assign" onClick={() => navigate(`/service-provider/plugins/${plugin.id}/assign`)} />
                    </Tooltip>,
                    <Tooltip title={plugin.status === 'active' ? '禁用插件' : '启用插件'}>
                      {plugin.status === 'active' ?
                        <CloseCircleOutlined key="disable" /> :
                        <CheckCircleOutlined key="enable" />
                      }
                    </Tooltip>,
                  ]}
                >
                  <Meta
                    title={
                      <div className="plugin-title">
                        <Text strong>{plugin.name}</Text>
                        {plugin.is_free ?
                          <Tag color="green">免费</Tag> :
                          <Tag color="blue">¥{plugin.price}</Tag>
                        }
                      </div>
                    }
                    description={
                      <div className="plugin-info">
                        <Paragraph ellipsis={{ rows: 2 }} className="plugin-desc">
                          {plugin.description}
                        </Paragraph>
                        <div className="plugin-meta">
                          <Tag color="purple">{getCategoryName(plugin.category)}</Tag>
                          <div className="plugin-rating">
                            <Rate disabled defaultValue={plugin.rating} allowHalf size="small" />
                            <Text type="secondary" style={{ marginLeft: 8 }}>{plugin.rating}</Text>
                          </div>
                        </div>
                        <div className="plugin-stats">
                          <Text type="secondary">下载: {plugin.downloads}</Text>
                          <Text type="secondary">租户使用: {plugin.tenant_usage}</Text>
                        </div>
                      </div>
                    }
                  />
                </Card>
              </Col>
            ))
          ) : (
            <Col span={24}>
              <Empty description="没有找到匹配的插件" />
            </Col>
          )}
        </Row>
      </Card>

      {/* 插件详情模态框 */}
      <Modal
        title={selectedPlugin?.name}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={[
          <Button key="back" onClick={() => setModalVisible(false)}>
            关闭
          </Button>,
          <Button
            key="assign"
            type="primary"
            onClick={() => {
              setModalVisible(false);
              navigate(`/service-provider/plugins/${selectedPlugin?.id}/assign`);
            }}
          >
            分配给租户
          </Button>,
        ]}
        width={700}
      >
        {selectedPlugin && (
          <div className="plugin-detail">
            <div className="plugin-header">
              <Avatar
                size={64}
                icon={selectedPlugin.icon || <AppstoreOutlined />}
                className="plugin-avatar"
              />
              <div className="plugin-header-info">
                <div className="plugin-tags">
                  <Tag color="purple">{getCategoryName(selectedPlugin.category)}</Tag>
                  {selectedPlugin.is_free ?
                    <Tag color="green">免费</Tag> :
                    <Tag color="blue">¥{selectedPlugin.price}</Tag>
                  }
                  <Tag color={selectedPlugin.status === 'active' ? 'success' : 'default'}>
                    {selectedPlugin.status === 'active' ? '已启用' : '未启用'}
                  </Tag>
                </div>
                <div className="plugin-rating">
                  <Rate disabled defaultValue={selectedPlugin.rating} allowHalf />
                  <Text style={{ marginLeft: 8 }}>{selectedPlugin.rating} 分</Text>
                </div>
              </div>
            </div>

            <Divider />

            <Paragraph>{selectedPlugin.description}</Paragraph>

            <Row gutter={[16, 16]} className="plugin-stats-row">
              <Col span={8}>
                <Statistic
                  title="下载次数"
                  value={selectedPlugin.downloads}
                  prefix={<DownloadOutlined />}
                />
              </Col>
              <Col span={8}>
                <Statistic
                  title="租户使用"
                  value={selectedPlugin.tenant_usage}
                  prefix={<ShopOutlined />}
                />
              </Col>
              <Col span={8}>
                <Statistic
                  title="版本"
                  value={selectedPlugin.version}
                  prefix={<CodeOutlined />}
                />
              </Col>
            </Row>

            <Divider orientation="left">功能特点</Divider>
            <List
              grid={{ gutter: 16, column: 2 }}
              dataSource={selectedPlugin.features}
              renderItem={item => (
                <List.Item>
                  <Text><CheckCircleOutlined style={{ color: '#52c41a', marginRight: 8 }} />{item}</Text>
                </List.Item>
              )}
            />

            <Divider orientation="left">系统要求</Divider>
            <List
              grid={{ gutter: 16, column: 1 }}
              dataSource={selectedPlugin.requirements}
              renderItem={item => (
                <List.Item>
                  <Text><InfoCircleOutlined style={{ color: '#1890ff', marginRight: 8 }} />{item}</Text>
                </List.Item>
              )}
            />

            <Divider />

            <Row>
              <Col span={12}>
                <Text type="secondary">开发者: {selectedPlugin.author}</Text>
              </Col>
              <Col span={12} style={{ textAlign: 'right' }}>
                <Text type="secondary">更新时间: {selectedPlugin.updated_at}</Text>
              </Col>
            </Row>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default PluginManagement;
