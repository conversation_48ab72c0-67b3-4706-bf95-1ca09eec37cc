#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
数据库初始化脚本
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from db.migrations import main

if __name__ == "__main__":
    print("正在初始化数据库...")
    try:
        asyncio.run(main())
        print("\n✅ 数据库初始化成功！")
    except Exception as e:
        print(f"\n❌ 数据库初始化失败: {e}")
        sys.exit(1) 