"""
钉钉插件基础服务类
提供通用的认证、配置获取等功能
"""
import json
import requests
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from sqlalchemy.orm import Session
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import and_, or_, select

from models.system_config import SystemConfig
from ..models import DingTalkAccessToken
# DingTalkConfig 已删除，统一使用 DingTalkSettings

logger = logging.getLogger(__name__)

class DingTalkBaseService:
    """钉钉插件基础服务类"""
    
    def __init__(self, db: AsyncSession, project_id):
        self.db = db
        # 如果传入的是字符串，转换为UUID
        if isinstance(project_id, str):
            import uuid
            self.project_id = uuid.UUID(project_id)
        else:
            self.project_id = project_id
        self.api_base_url = "https://oapi.dingtalk.com"
        self._access_token = None
        self._token_expires_at = None
    
    async def get_access_token(self) -> str:
        """获取钉钉Access Token"""
        try:
            # 检查缓存的token是否还有效
            if (self._access_token and 
                self._token_expires_at and 
                datetime.utcnow() < self._token_expires_at):
                return self._access_token
            
            # 从数据库获取配置
            config = await self.get_dingtalk_config()
            if not config or not config.get('app_key') or not config.get('app_secret'):
                raise Exception("钉钉配置不完整")
            
            # 调用钉钉API获取token
            url = f"{self.api_base_url}/gettoken"
            params = {
                "appkey": config['app_key'],
                "appsecret": config['app_secret']
            }
            
            response = requests.get(url, params=params)
            result = response.json()
            
            if result.get("errcode") == 0:
                self._access_token = result["access_token"]
                # token有效期通常为2小时，提前10分钟刷新
                self._token_expires_at = datetime.utcnow() + timedelta(seconds=result.get("expires_in", 7200) - 600)
                
                # 保存到数据库
                token_record = self.db.query(DingTalkAccessToken)\
                                    .filter(DingTalkAccessToken.project_id == self.project_id)\
                                    .first()
                
                if token_record:
                    token_record.access_token = self._access_token
                    token_record.expires_at = self._token_expires_at
                    token_record.updated_at = datetime.utcnow()
                else:
                    token_record = DingTalkAccessToken(
                        project_id=self.project_id,
                        access_token=self._access_token,
                        expires_at=self._token_expires_at,
                        created_at=datetime.utcnow(),
                        updated_at=datetime.utcnow()
                    )
                    self.db.add(token_record)
                
                self.db.commit()
                return self._access_token
            else:
                raise Exception(f"获取钉钉access_token失败: {result.get('errmsg', '未知错误')}")
                
        except Exception as e:
            logger.error(f"获取钉钉access_token失败: {str(e)}")
            raise
    
    async def get_dingtalk_config(self) -> Dict[str, Any]:
        """获取钉钉配置（使用统一的设置服务）"""
        try:
            # 使用统一的设置服务获取配置
            from ..services.unified_settings_service import UnifiedSettingsService
            # 需要获取tenant_id，这里简化处理
            from models.project import Project
            project_query = select(Project).where(Project.id == self.project_id)
            result = await self.db.execute(project_query)
            project = result.scalar_one_or_none()
            
            if not project:
                return {}
                
            settings_service = UnifiedSettingsService(self.db, str(project.tenant_id), str(self.project_id))
            return await settings_service.get_config_for_api()
            
        except Exception as e:
            logger.error(f"获取钉钉配置失败: {str(e)}")
            return {}
    
    async def call_dingtalk_api(
        self, 
        endpoint: str, 
        method: str = "POST", 
        data: Dict[str, Any] = None,
        params: Dict[str, str] = None
    ) -> Dict[str, Any]:
        """统一的钉钉API调用方法"""
        try:
            access_token = await self.get_access_token()
            
            # 构建URL
            url = f"{self.api_base_url}/{endpoint.lstrip('/')}"
            
            # 添加access_token到参数
            if not params:
                params = {}
            params["access_token"] = access_token
            
            # 发送请求
            if method.upper() == "GET":
                response = requests.get(url, params=params)
            else:
                response = requests.post(url, params=params, json=data)
            
            result = response.json()
            
            if result.get("errcode") == 0:
                return result
            else:
                raise Exception(f"钉钉API调用失败: {result.get('errmsg', '未知错误')}")
                
        except Exception as e:
            logger.error(f"钉钉API调用失败: {str(e)}")
            raise
    
    def format_dingtalk_time(self, timestamp: int) -> datetime:
        """格式化钉钉时间戳"""
        return datetime.fromtimestamp(timestamp / 1000) if timestamp else None
    
    def format_response_time(self, dt: datetime) -> str:
        """格式化响应时间"""
        return dt.isoformat() if dt else None 