#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
钉钉业务审批集成示例
演示如何使用钉钉审批流程与门店管理系统深度集成
"""

import asyncio
import json
from datetime import datetime
from typing import Dict, Any

from ..utils.dingtalk_api import DingTalkAPI
from ..services.business_integration import (
    integrate_store_loss,
    integrate_sales_report, 
    integrate_expense_reimbursement
)

# 示例：门店管理业务审批流程集成

class BusinessApprovalExample:
    """业务审批示例类"""
    
    def __init__(self, app_key: str, app_secret: str):
        self.dingtalk_api = DingTalkAPI(app_key, app_secret)
    
    async def create_store_loss_approval_template(self) -> Dict[str, Any]:
        """
        创建门店报损审批模板
        这个模板将在钉钉中创建一个专门用于门店报损申请的审批流程
        """
        template_data = {
            "name": "门店商品报损申请",
            "description": "用于门店商品报损申请的审批流程，审批通过后自动创建报损记录",
            "type": "store_loss",
            "fields": [
                "store_name",      # 门店名称
                "product_name",    # 产品名称  
                "product_code",    # 产品编码
                "quantity",        # 报损数量
                "unit_price",      # 单价
                "loss_reason",     # 报损原因
                "description",     # 报损说明
                "evidence"         # 报损凭证
            ],
            "approvers": [
                {
                    "name": "门店经理审批",
                    "user_ids": ["manager_001", "manager_002"],
                    "mode": "or"  # 或签，任一门店经理审批即可
                },
                {
                    "name": "区域总监审批", 
                    "user_ids": ["director_001"],
                    "mode": "and"  # 必须区域总监审批
                }
            ]
        }
        
        result = await self.dingtalk_api.create_or_update_approval_template(template_data)
        print(f"创建门店报损审批模板: {result}")
        return result
    
    async def create_sales_report_approval_template(self) -> Dict[str, Any]:
        """
        创建销售业绩上报审批模板
        """
        template_data = {
            "name": "门店销售业绩上报",
            "description": "门店每日销售业绩数据上报审批，审批通过后自动录入销售系统",
            "type": "sales_report",
            "fields": [
                "store_name",         # 门店名称
                "report_date",        # 报告日期
                "total_sales",        # 总销售额
                "customer_count",     # 客流量
                "average_price",      # 客单价
                "top_products",       # 热销产品
                "performance_summary" # 业绩总结
            ],
            "approvers": [
                {
                    "name": "区域经理审批",
                    "user_ids": ["area_manager_001"],
                    "mode": "and"
                }
            ]
        }
        
        result = await self.dingtalk_api.create_or_update_approval_template(template_data)
        print(f"创建销售业绩上报审批模板: {result}")
        return result
    
    async def create_expense_reimbursement_template(self) -> Dict[str, Any]:
        """
        创建费用报销审批模板
        """
        template_data = {
            "name": "门店费用报销申请",
            "description": "门店日常运营费用报销申请，审批通过后自动录入财务系统",
            "type": "expense_reimbursement", 
            "fields": [
                "store_name",     # 门店名称
                "expense_date",   # 费用日期
                "expense_type",   # 费用类型
                "amount",         # 报销金额
                "description",    # 费用说明
                "vendor",         # 供应商/商家
                "receipts"        # 发票凭证
            ],
            "approvers": [
                {
                    "name": "门店经理审批",
                    "user_ids": ["manager_001"],
                    "mode": "and"
                },
                {
                    "name": "财务审批",
                    "user_ids": ["finance_001", "finance_002"],
                    "mode": "or"
                }
            ]
        }
        
        result = await self.dingtalk_api.create_or_update_approval_template(template_data)
        print(f"创建费用报销审批模板: {result}")
        return result
    
    async def submit_store_loss_approval(self, process_code: str, form_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        提交门店报损审批申请
        
        Args:
            process_code: 审批模板代码
            form_data: 表单数据
            
        Returns:
            Dict[str, Any]: 提交结果
        """
        # 构建审批实例数据
        instance_data = {
            "process_code": process_code,
            "originator_user_id": form_data.get("originator_user_id", "user_001"),
            "form_component_values": [
                {"name": "store_name", "value": form_data["store_name"]},
                {"name": "product_name", "value": form_data["product_name"]},
                {"name": "product_code", "value": form_data["product_code"]},
                {"name": "quantity", "value": str(form_data["quantity"])},
                {"name": "unit_price", "value": str(form_data["unit_price"])},
                {"name": "total_amount", "value": str(form_data["quantity"] * form_data["unit_price"])},
                {"name": "loss_reason", "value": form_data["loss_reason"]},
                {"name": "description", "value": form_data.get("description", "")},
            ],
            "approvers": [],  # 使用模板定义的审批人
            "cc_list": [],    # 抄送人列表
            "cc_position": "FINISH"  # 完成时抄送
        }
        
        result = await self.dingtalk_api.create_approval_instance(instance_data)
        print(f"提交门店报损审批: {result}")
        return result
    
    async def handle_approval_callback(self, callback_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理审批回调 - 当审批状态变化时自动触发业务系统集成
        
        Args:
            callback_data: 钉钉回调数据
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        event_type = callback_data.get('EventType')
        process_instance_id = callback_data.get('processInstanceId')
        status = callback_data.get('status')
        
        print(f"收到审批回调: 事件={event_type}, 实例={process_instance_id}, 状态={status}")
        
        if event_type == 'bpms_instance_change' and status == 'COMPLETED':
            # 审批通过，触发业务系统集成
            print(f"审批实例 {process_instance_id} 已通过，开始业务系统集成...")
            
            # 获取审批实例详情
            instance_detail = await self.dingtalk_api.get_approval_instance_detail(process_instance_id)
            
            if instance_detail:
                # 根据审批类型进行相应的业务集成
                template_name = instance_detail.get('title', '')
                
                if '报损' in template_name:
                    print("开始集成门店报损数据...")
                    # 在实际应用中，这里会调用 integrate_store_loss 函数
                    
                elif '销售业绩' in template_name:
                    print("开始集成销售业绩数据...")
                    # 在实际应用中，这里会调用 integrate_sales_report 函数
                    
                elif '费用报销' in template_name:
                    print("开始集成费用报销数据...")
                    # 在实际应用中，这里会调用 integrate_expense_reimbursement 函数
        
        return await self.dingtalk_api.handle_approval_callback(callback_data)
    
    async def sync_approval_status(self) -> Dict[str, Any]:
        """
        同步审批状态
        定期调用此方法来同步钉钉中的审批状态到本地系统
        """
        # 获取最近30天的审批实例
        end_time = int(datetime.now().timestamp() * 1000)
        start_time = end_time - (30 * 24 * 60 * 60 * 1000)  # 30天前
        
        params = {
            "start_time": start_time,
            "end_time": end_time,
            "size": 100
        }
        
        instances = await self.dingtalk_api.get_approval_instances(params)
        print(f"获取到 {len(instances.get('list', []))} 个审批实例")
        
        for instance_id in instances.get('list', []):
            detail = await self.dingtalk_api.get_approval_instance_detail(instance_id)
            if detail:
                print(f"实例 {instance_id}: {detail.get('title')} - {detail.get('status')}")
        
        return instances

# 使用示例
async def main():
    """
    主函数 - 演示完整的业务审批流程
    """
    # 初始化钉钉API (请替换为实际的app_key和app_secret)
    example = BusinessApprovalExample(
        app_key="your_app_key_here",
        app_secret="your_app_secret_here"
    )
    
    print("=== 钉钉业务审批集成示例 ===\n")
    
    # 1. 创建审批模板
    print("1. 创建审批模板...")
    store_loss_template = await example.create_store_loss_approval_template()
    sales_template = await example.create_sales_report_approval_template()
    expense_template = await example.create_expense_reimbursement_template()
    
    # 2. 提交审批申请示例
    if store_loss_template.get("success"):
        print("\n2. 提交门店报损审批申请...")
        form_data = {
            "originator_user_id": "store_staff_001",
            "store_name": "上海南京路店",
            "product_name": "苹果iPhone 15",
            "product_code": "IP15-001",
            "quantity": 2,
            "unit_price": 6999.00,
            "loss_reason": "damaged",
            "description": "运输过程中包装损坏，屏幕破裂"
        }
        
        approval_result = await example.submit_store_loss_approval(
            store_loss_template["process_code"], 
            form_data
        )
        print(f"审批申请结果: {approval_result}")
    
    # 3. 模拟审批回调处理
    print("\n3. 模拟审批回调处理...")
    callback_data = {
        "EventType": "bpms_instance_change",
        "processInstanceId": "example_instance_001",
        "status": "COMPLETED",
        "result": "agree"
    }
    
    callback_result = await example.handle_approval_callback(callback_data)
    print(f"回调处理结果: {callback_result}")
    
    # 4. 同步审批状态
    print("\n4. 同步审批状态...")
    sync_result = await example.sync_approval_status()
    print(f"同步结果: 找到 {len(sync_result.get('list', []))} 个实例")
    
    print("\n=== 示例完成 ===")

if __name__ == "__main__":
    # 运行示例
    asyncio.run(main())


# 钉钉业务审批集成的主要优势：

"""
1. 深度集成：
   - 审批模板自动创建，无需手动配置
   - 表单字段与业务系统完全对应
   - 审批流程可灵活定制

2. 自动化处理：
   - 审批通过后自动创建业务记录
   - 状态实时同步，避免数据不一致
   - 回调机制确保及时响应

3. 业务闭环：
   - 从申请提交到数据录入全程自动化
   - 审批历史完整保留，便于追溯
   - 支持多种业务场景（报损、销售、费用等）

4. 用户体验：
   - 在钉钉中直接发起和处理审批
   - 移动端随时随地操作
   - 消息推送及时提醒

5. 管理效率：
   - 统一的审批入口和流程
   - 数据自动汇总和分析
   - 权限控制和角色管理
""" 