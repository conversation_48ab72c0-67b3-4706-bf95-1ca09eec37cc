#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
全渠道运营助手 - 自动化规则模型
"""

import uuid
from sqlalchemy import Column, String, Boolean, ForeignKey, Text, DateTime, Integer, Float, Index
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship
from datetime import datetime

from db.database import Base


class AutomationRule(Base):
    """自动化规则模型"""
    __tablename__ = "multi_channel_automation_rules"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id", ondelete="CASCADE"), nullable=False)
    
    # 规则基本信息
    name = Column(String(200), nullable=False, comment="规则名称")
    description = Column(Text, nullable=True, comment="规则描述")
    category = Column(String(50), nullable=False, comment="规则类别: pricing, inventory, promotion, content")
    
    # 触发条件
    trigger_type = Column(String(50), nullable=False, comment="触发类型: time, event, condition")
    trigger_config = Column(JSONB, default=dict, comment="触发配置")
    
    # 条件设置
    conditions = Column(JSONB, default=list, comment="执行条件列表")
    condition_logic = Column(String(10), default="AND", comment="条件逻辑: AND, OR")
    
    # 执行动作
    actions = Column(JSONB, default=list, comment="执行动作列表")
    
    # 目标范围
    target_channels = Column(JSONB, default=list, comment="目标渠道ID列表")
    target_products = Column(JSONB, default=list, comment="目标商品ID列表")
    target_filters = Column(JSONB, default=dict, comment="目标过滤条件")
    
    # 执行设置
    is_active = Column(Boolean, default=True, comment="是否启用")
    priority = Column(Integer, default=0, comment="优先级")
    max_executions = Column(Integer, nullable=True, comment="最大执行次数")
    execution_count = Column(Integer, default=0, comment="已执行次数")
    
    # 时间设置
    start_time = Column(DateTime, nullable=True, comment="开始时间")
    end_time = Column(DateTime, nullable=True, comment="结束时间")
    
    # 外部集成
    external_integration = Column(JSONB, default=dict, comment="外部集成配置（如n8n）")
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_executed_at = Column(DateTime, nullable=True, comment="最后执行时间")
    
    # 索引
    __table_args__ = (
        Index('idx_rule_active', 'project_id', 'is_active'),
        Index('idx_rule_category', 'category'),
        Index('idx_rule_trigger', 'trigger_type'),
    )


class AutomationExecution(Base):
    """自动化规则执行记录"""
    __tablename__ = "multi_channel_automation_executions"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    rule_id = Column(UUID(as_uuid=True), ForeignKey("multi_channel_automation_rules.id", ondelete="CASCADE"), nullable=False)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id", ondelete="CASCADE"), nullable=False)
    
    # 执行信息
    execution_id = Column(String(100), nullable=False, comment="执行ID")
    trigger_event = Column(JSONB, default=dict, comment="触发事件数据")
    
    # 执行状态
    status = Column(String(50), default="pending", comment="执行状态: pending, running, completed, failed, cancelled")
    progress = Column(Integer, default=0, comment="执行进度百分比")
    
    # 执行结果
    affected_items = Column(Integer, default=0, comment="影响的项目数量")
    success_items = Column(Integer, default=0, comment="成功处理的项目数量")
    failed_items = Column(Integer, default=0, comment="失败的项目数量")
    
    # 详细结果
    execution_details = Column(JSONB, default=list, comment="执行详情列表")
    error_details = Column(JSONB, default=list, comment="错误详情列表")
    
    # 时间信息
    started_at = Column(DateTime, nullable=True, comment="开始时间")
    completed_at = Column(DateTime, nullable=True, comment="完成时间")
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # 索引
    __table_args__ = (
        Index('idx_execution_rule', 'rule_id', 'created_at'),
        Index('idx_execution_status', 'status'),
    )


class AutomationNode(Base):
    """自动化流程节点配置"""
    __tablename__ = "multi_channel_automation_nodes"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    rule_id = Column(UUID(as_uuid=True), ForeignKey("multi_channel_automation_rules.id", ondelete="CASCADE"), nullable=False)
    
    # 节点信息
    node_id = Column(String(100), nullable=False, comment="节点ID")
    node_type = Column(String(50), nullable=False, comment="节点类型: trigger, condition, action, delay, branch")
    node_name = Column(String(200), nullable=False, comment="节点名称")
    
    # 节点配置
    node_config = Column(JSONB, default=dict, comment="节点配置")
    
    # 流程控制
    parent_node_id = Column(String(100), nullable=True, comment="父节点ID")
    next_node_ids = Column(JSONB, default=list, comment="下一个节点ID列表")
    position = Column(JSONB, default=dict, comment="节点位置信息")
    
    # 条件分支
    branch_conditions = Column(JSONB, default=list, comment="分支条件")
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)


class AutomationTemplate(Base):
    """自动化规则模板"""
    __tablename__ = "multi_channel_automation_templates"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    
    # 模板信息
    name = Column(String(200), nullable=False, comment="模板名称")
    description = Column(Text, nullable=True, comment="模板描述")
    category = Column(String(50), nullable=False, comment="模板类别")
    tags = Column(JSONB, default=list, comment="标签列表")
    
    # 模板配置
    template_config = Column(JSONB, default=dict, comment="模板配置")
    
    # 使用统计
    usage_count = Column(Integer, default=0, comment="使用次数")
    
    # 模板状态
    is_public = Column(Boolean, default=False, comment="是否公开")
    is_active = Column(Boolean, default=True, comment="是否启用")
    
    # 创建者信息
    created_by = Column(UUID(as_uuid=True), nullable=True, comment="创建者ID")
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)


class ExternalIntegration(Base):
    """外部集成配置"""
    __tablename__ = "multi_channel_external_integrations"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id", ondelete="CASCADE"), nullable=False)
    
    # 集成信息
    integration_type = Column(String(50), nullable=False, comment="集成类型: n8n, zapier, webhook, api")
    integration_name = Column(String(200), nullable=False, comment="集成名称")
    
    # 连接配置
    endpoint_url = Column(String(500), nullable=True, comment="端点URL")
    auth_config = Column(JSONB, default=dict, comment="认证配置")
    headers = Column(JSONB, default=dict, comment="请求头")
    
    # 映射配置
    data_mapping = Column(JSONB, default=dict, comment="数据映射配置")
    
    # 状态
    is_active = Column(Boolean, default=True, comment="是否启用")
    last_test_at = Column(DateTime, nullable=True, comment="最后测试时间")
    test_status = Column(String(50), nullable=True, comment="测试状态")
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
