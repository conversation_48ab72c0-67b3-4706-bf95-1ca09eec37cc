#!/usr/bin/env python
# -*- coding: utf-8 -*-

import logging
import uuid
from typing import Dict, Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query, Path, Body
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, desc, func
from datetime import datetime, timedelta

from db.database import get_db
from models.tenant import Tenant
from models.project import Project
from models.plugin import Plugin, TenantPlugin, PluginPurchase
from models.user import User
from core.auth import get_current_user, get_current_tenant
from schemas.plugin import PluginPurchaseCreate, PluginPurchaseResponse

# 配置日志
logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter()

@router.post("/{plugin_id}/purchase", response_model=PluginPurchaseResponse)
async def purchase_plugin(
    plugin_id: uuid.UUID,
    purchase_data: PluginPurchaseCreate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
    current_tenant: Tenant = Depends(get_current_tenant)
):
    """
    购买插件
    """
    try:
        # 检查插件是否存在
        plugin_query = select(Plugin).where(Plugin.id == plugin_id)
        result = await db.execute(plugin_query)
        plugin = result.scalar_one_or_none()
        
        if not plugin:
            return {
                "success": False,
                "message": "插件不存在",
                "data": None
            }
        
        # 检查插件状态
        if plugin.status != "active":
            return {
                "success": False,
                "message": "插件当前不可购买",
                "data": None
            }
        
        # 检查是否已购买
        purchase_query = select(PluginPurchase).where(
            and_(
                PluginPurchase.tenant_id == current_tenant.id,
                PluginPurchase.plugin_id == plugin_id,
                PluginPurchase.status == "active"
            )
        )
        result = await db.execute(purchase_query)
        existing_purchase = result.scalar_one_or_none()
        
        if existing_purchase:
            # 如果已购买且未过期，返回错误
            if existing_purchase.end_date and existing_purchase.end_date > datetime.now():
                return {
                    "success": False,
                    "message": "您已购买此插件，无需重复购买",
                    "data": None
                }
        
        # 计算购买周期和价格
        billing_cycle = purchase_data.billing_cycle
        duration = purchase_data.duration
        
        if billing_cycle == "one_time":
            start_date = datetime.now()
            end_date = None  # 永久有效
        elif billing_cycle == "monthly":
            start_date = datetime.now()
            end_date = start_date + timedelta(days=30 * duration)
        elif billing_cycle == "yearly":
            start_date = datetime.now()
            end_date = start_date + timedelta(days=365 * duration)
        else:
            return {
                "success": False,
                "message": "无效的计费周期",
                "data": None
            }
        
        # 获取服务商ID
        service_provider_id = plugin.service_provider_id
        
        # 创建订单号
        order_number = f"PLG{datetime.now().strftime('%Y%m%d%H%M%S')}{uuid.uuid4().hex[:6]}"
        
        # 计算最终价格
        final_price = plugin.price * duration
        
        # 创建购买记录
        purchase = PluginPurchase(
            id=uuid.uuid4(),
            tenant_id=current_tenant.id,
            plugin_id=plugin_id,
            service_provider_id=service_provider_id,
            order_number=order_number,
            amount=final_price,
            currency="CNY",
            billing_cycle=billing_cycle,
            duration=duration,
            start_date=start_date,
            end_date=end_date,
            status="active",
            payment_method=purchase_data.payment_method,
            payment_status="paid",  # 假设支付已完成
            purchased_by=current_user.id
        )
        
        db.add(purchase)
        
        # 同步到服务商系统的订单表
        # 这里需要调用服务商系统的API或直接操作数据库
        # 为简化实现，我们直接在这里创建订单记录
        from models.order import Order
        
        order = Order(
            id=uuid.uuid4(),
            service_provider_id=service_provider_id,
            tenant_id=current_tenant.id,
            order_no=order_number,
            order_type="plugin",
            item_id=plugin_id,
            item_name=plugin.name,
            amount=final_price,
            discount_amount=0,
            final_amount=final_price,
            status="paid",
            payment_method=purchase_data.payment_method,
            payment_time=datetime.now(),
            created_by=current_user.id
        )
        
        db.add(order)
        
        # 检查是否已安装
        tenant_plugin_query = select(TenantPlugin).where(
            and_(
                TenantPlugin.tenant_id == current_tenant.id,
                TenantPlugin.plugin_id == plugin_id
            )
        )
        result = await db.execute(tenant_plugin_query)
        tenant_plugin = result.scalar_one_or_none()
        
        # 如果未安装，则自动安装
        if not tenant_plugin:
            tenant_plugin = TenantPlugin(
                id=uuid.uuid4(),
                tenant_id=current_tenant.id,
                plugin_id=plugin_id,
                status="active",
                version=plugin.version,
                settings={}
            )
            db.add(tenant_plugin)
        
        await db.commit()
        
        return {
            "success": True,
            "message": "插件购买成功",
            "data": {
                "id": purchase.id,
                "tenant_id": purchase.tenant_id,
                "plugin_id": purchase.plugin_id,
                "order_number": purchase.order_number,
                "amount": purchase.amount,
                "billing_cycle": purchase.billing_cycle,
                "duration": purchase.duration,
                "start_date": purchase.start_date,
                "end_date": purchase.end_date,
                "status": purchase.status,
                "created_at": purchase.created_at
            }
        }
    except Exception as e:
        await db.rollback()
        logger.error(f"购买插件失败: {str(e)}")
        return {
            "success": False,
            "message": f"购买插件失败: {str(e)}",
            "data": None
        }
