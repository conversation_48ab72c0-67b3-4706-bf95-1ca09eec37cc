-- 创建租户数据共享表
CREATE TABLE IF NOT EXISTS tenant_data_sharing (
    id UUID PRIMARY KEY,
    source_tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    target_tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    data_type VARCHAR(50) NOT NULL,
    data_id UUID NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'active',
    access_level VARCHAR(20) NOT NULL DEFAULT 'read',
    expiry_date TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    created_by UUID NOT NULL REFERENCES users(id),
    updated_by UUID REFERENCES users(id),
    sharing_metadata JSONB DEFAULT '{}'::jsonb,
    CONSTRAINT uq_tenant_data_sharing UNIQUE (source_tenant_id, target_tenant_id, data_type, data_id)
);

-- 创建租户数据共享日志表
CREATE TABLE IF NOT EXISTS tenant_data_sharing_logs (
    id UUID PRIMARY KEY,
    sharing_id UUID NOT NULL REFERENCES tenant_data_sharing(id) ON DELETE CASCADE,
    action VARCHAR(50) NOT NULL,
    action_by UUID NOT NULL REFERENCES users(id),
    action_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    details JSONB DEFAULT '{}'::jsonb
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_tenant_data_sharing_source ON tenant_data_sharing(source_tenant_id);
CREATE INDEX IF NOT EXISTS idx_tenant_data_sharing_target ON tenant_data_sharing(target_tenant_id);
CREATE INDEX IF NOT EXISTS idx_tenant_data_sharing_data ON tenant_data_sharing(data_type, data_id);
CREATE INDEX IF NOT EXISTS idx_tenant_data_sharing_status ON tenant_data_sharing(status);
CREATE INDEX IF NOT EXISTS idx_tenant_data_sharing_logs_sharing ON tenant_data_sharing_logs(sharing_id);
CREATE INDEX IF NOT EXISTS idx_tenant_data_sharing_logs_action ON tenant_data_sharing_logs(action);
