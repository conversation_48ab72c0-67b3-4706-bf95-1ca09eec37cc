import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Select,
  DatePicker,
  Button,
  Space,
  Typography,
  Statistic,
  Table,
  Tag
} from 'antd';
import {
  DownloadOutlined,
  BarChartOutlined,
  LineChartOutlined,
  PieChartOutlined
} from '@ant-design/icons';
import { Line, Column, Pie } from '@ant-design/plots';
import { useParams } from 'react-router-dom';
import multiChannelService from '../services/multiChannelService';

const { Title, Text } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;

const Analytics = () => {
  const { projectId } = useParams();
  const [loading, setLoading] = useState(false);
  const [dateRange, setDateRange] = useState([]);
  const [selectedChannels, setSelectedChannels] = useState([]);
  const [salesData, setSalesData] = useState([]);
  const [channelComparison, setChannelComparison] = useState([]);
  const [productPerformance, setProductPerformance] = useState([]);

  useEffect(() => {
    loadAnalyticsData();
  }, [projectId, dateRange, selectedChannels]);

  const loadAnalyticsData = async () => {
    try {
      setLoading(true);
      
      const params = {
        start_date: dateRange[0]?.format('YYYY-MM-DD'),
        end_date: dateRange[1]?.format('YYYY-MM-DD'),
        channels: selectedChannels
      };

      const [sales, comparison, performance] = await Promise.all([
        multiChannelService.getSalesAnalytics(projectId, params),
        multiChannelService.getChannelComparison(projectId, params),
        multiChannelService.getProductPerformance(projectId, params)
      ]);

      setSalesData(sales.data || []);
      setChannelComparison(comparison.data || []);
      setProductPerformance(performance.data || []);
      
    } catch (error) {
      console.error('加载分析数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleExport = async (reportType) => {
    try {
      const params = {
        start_date: dateRange[0]?.format('YYYY-MM-DD'),
        end_date: dateRange[1]?.format('YYYY-MM-DD'),
        channels: selectedChannels
      };

      const response = await multiChannelService.exportAnalyticsReport(projectId, reportType, params);
      
      // 创建下载链接
      const url = window.URL.createObjectURL(new Blob([response]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `${reportType}_report.xlsx`);
      document.body.appendChild(link);
      link.click();
      link.remove();
      
    } catch (error) {
      console.error('导出报告失败:', error);
    }
  };

  // 销售趋势图配置
  const salesTrendConfig = {
    data: salesData,
    xField: 'date',
    yField: 'sales',
    seriesField: 'channel',
    height: 300,
    smooth: true,
    point: { size: 3 },
    legend: { position: 'top' },
    tooltip: {
      formatter: (datum) => ({
        name: datum.channel,
        value: `¥${datum.sales?.toLocaleString()}`
      })
    }
  };

  // 渠道对比图配置
  const channelComparisonConfig = {
    data: channelComparison,
    xField: 'sales',
    yField: 'channel',
    height: 300,
    label: {
      position: 'middle',
      style: { fill: '#FFFFFF', opacity: 0.6 }
    },
    tooltip: {
      formatter: (datum) => ({
        name: '销售额',
        value: `¥${datum.sales?.toLocaleString()}`
      })
    }
  };

  // 商品表现表格列
  const productColumns = [
    {
      title: '商品名称',
      dataIndex: 'product_name',
      key: 'product_name'
    },
    {
      title: '销售额',
      dataIndex: 'sales',
      key: 'sales',
      render: (value) => `¥${value?.toLocaleString()}`
    },
    {
      title: '销量',
      dataIndex: 'quantity',
      key: 'quantity',
      render: (value) => `${value} 件`
    },
    {
      title: '平均价格',
      dataIndex: 'avg_price',
      key: 'avg_price',
      render: (value) => `¥${value?.toFixed(2)}`
    },
    {
      title: '增长率',
      dataIndex: 'growth_rate',
      key: 'growth_rate',
      render: (value) => (
        <Tag color={value > 0 ? 'green' : value < 0 ? 'red' : 'default'}>
          {value > 0 ? '+' : ''}{value}%
        </Tag>
      )
    }
  ];

  return (
    <div>

      {/* 筛选条件 */}
      <Card style={{ marginBottom: 24 }}>
        <Row gutter={16} align="middle">
          <Col>
            <Text strong>时间范围：</Text>
          </Col>
          <Col>
            <RangePicker
              value={dateRange}
              onChange={setDateRange}
              style={{ width: 240 }}
            />
          </Col>
          <Col>
            <Text strong>渠道：</Text>
          </Col>
          <Col>
            <Select
              mode="multiple"
              placeholder="选择渠道"
              style={{ width: 200 }}
              value={selectedChannels}
              onChange={setSelectedChannels}
            >
              {/* 这里应该加载渠道列表 */}
            </Select>
          </Col>
          <Col>
            <Space>
              <Button
                type="primary"
                onClick={loadAnalyticsData}
                loading={loading}
              >
                查询
              </Button>
              <Button
                icon={<DownloadOutlined />}
                onClick={() => handleExport('sales')}
              >
                导出报告
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 核心指标 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总销售额"
              value={salesData.reduce((sum, item) => sum + (item.sales || 0), 0)}
              precision={2}
              prefix="¥"
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="总订单数"
              value={salesData.reduce((sum, item) => sum + (item.orders || 0), 0)}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="平均客单价"
              value={
                salesData.length > 0
                  ? salesData.reduce((sum, item) => sum + (item.avg_order_value || 0), 0) / salesData.length
                  : 0
              }
              precision={2}
              prefix="¥"
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="活跃渠道"
              value={new Set(salesData.map(item => item.channel)).size}
              suffix="个"
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 销售趋势图 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={16}>
          <Card
            title={
              <Space>
                <LineChartOutlined />
                <span>销售趋势</span>
              </Space>
            }
            extra={
              <Button size="small" onClick={() => handleExport('trend')}>
                导出
              </Button>
            }
          >
            {salesData.length > 0 ? (
              <Line {...salesTrendConfig} />
            ) : (
              <div style={{ textAlign: 'center', padding: '50px' }}>
                <Text type="secondary">暂无数据</Text>
              </div>
            )}
          </Card>
        </Col>
        
        <Col span={8}>
          <Card
            title={
              <Space>
                <BarChartOutlined />
                <span>渠道对比</span>
              </Space>
            }
            extra={
              <Button size="small" onClick={() => handleExport('channel')}>
                导出
              </Button>
            }
          >
            {channelComparison.length > 0 ? (
              <Column {...channelComparisonConfig} />
            ) : (
              <div style={{ textAlign: 'center', padding: '50px' }}>
                <Text type="secondary">暂无数据</Text>
              </div>
            )}
          </Card>
        </Col>
      </Row>

      {/* 商品表现 */}
      <Card
        title={
          <Space>
            <PieChartOutlined />
            <span>商品表现排行</span>
          </Space>
        }
        extra={
          <Button size="small" onClick={() => handleExport('product')}>
            导出
          </Button>
        }
      >
        <Table
          columns={productColumns}
          dataSource={productPerformance}
          rowKey="product_id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 个商品`
          }}
        />
      </Card>
    </div>
  );
};

export default Analytics;
