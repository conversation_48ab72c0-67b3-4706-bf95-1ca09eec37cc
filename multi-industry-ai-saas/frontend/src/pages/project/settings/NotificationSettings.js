import React, { useState, useEffect } from 'react';
import {
  Card,
  Tabs,
  Form,
  Input,
  Button,
  Select,
  Switch,
  Radio,
  message,
  Space,
  Divider,
  Typography,
  Row,
  Col,
  Table,
  Tag,
  Tooltip
} from 'antd';
import {
  BellOutlined,
  MailOutlined,
  MobileOutlined,
  SoundOutlined,
  ClockCircleOutlined,
  SettingOutlined,
  SaveOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import apiService from '../../../services/api';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;
const { TextArea } = Input;

/**
 * 通知设置组件
 */
const NotificationSettings = () => {
  const [activeTab, setActiveTab] = useState('general');
  const [loading, setLoading] = useState(false);
  const [generalSettings, setGeneralSettings] = useState(null);
  const [workflowSettings, setWorkflowSettings] = useState({});
  const [generalForm] = Form.useForm();
  const [workflowForm] = Form.useForm();
  const [workflowTypes, setWorkflowTypes] = useState([
    { key: 'loss_report', name: '报损工作流' },
    { key: 'purchase_order', name: '采购工作流' },
    { key: 'inventory_check', name: '盘点工作流' }
  ]);
  const [currentWorkflowType, setCurrentWorkflowType] = useState('loss_report');

  // 获取通知设置
  const fetchGeneralSettings = async () => {
    setLoading(true);
    try {
      const response = await apiService.project.notifications.getSettings();
      if (response && response.data) {
        setGeneralSettings(response.data);
        generalForm.setFieldsValue({
          normal_frequency: response.data.normal_frequency,
          high_priority_frequency: response.data.high_priority_frequency,
          email_notifications: response.data.email_notifications,
          push_notifications: response.data.push_notifications,
          sms_notifications: response.data.sms_notifications,
          sound_enabled: response.data.sound_enabled,
          system_notifications: response.data.system_notifications,
          task_notifications: response.data.task_notifications,
          inventory_notifications: response.data.inventory_notifications,
          order_notifications: response.data.order_notifications,
          security_notifications: response.data.security_notifications
        });
      }
    } catch (error) {
      console.error('获取通知设置失败:', error);
      message.error('获取通知设置失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取工作流通知设置
  const fetchWorkflowSettings = async (workflowType) => {
    setLoading(true);
    try {
      const response = await apiService.project.notifications.getWorkflowSettings(workflowType);
      if (response && response.data) {
        setWorkflowSettings({
          ...workflowSettings,
          [workflowType]: response.data
        });

        if (workflowType === currentWorkflowType) {
          workflowForm.setFieldsValue({
            enabled: response.data.enabled,
            notify_type: response.data.notify_type,
            notify_roles: response.data.notify_roles,
            notify_users: response.data.notify_users,
            title: response.data.title,
            template: response.data.template
          });
        }
      }
    } catch (error) {
      console.error('获取工作流通知设置失败:', error);
      message.error('获取工作流通知设置失败');
    } finally {
      setLoading(false);
    }
  };

  // 初始化
  useEffect(() => {
    fetchGeneralSettings();
    workflowTypes.forEach(type => {
      fetchWorkflowSettings(type.key);
    });
  }, []);

  // 处理标签页切换
  const handleTabChange = (key) => {
    setActiveTab(key);
  };

  // 处理保存通用设置
  const handleSaveGeneralSettings = async (values) => {
    setLoading(true);
    try {
      // 记录保存前的设置
      console.log('保存通知设置 - 提交的值:', values);

      const response = await apiService.project.notifications.updateSettings(values);
      if (response && response.success) {
        message.success('通知设置保存成功');

        // 刷新设置
        fetchGeneralSettings();

        // 立即更新本地存储的设置，以便通知上下文可以使用
        try {
          localStorage.setItem('notificationSettings', JSON.stringify(values));
          console.log('通知设置已保存到本地存储');

          // 触发自定义事件，通知其他组件设置已更改
          const event = new CustomEvent('notificationSettingsChanged', {
            detail: { settings: values }
          });
          window.dispatchEvent(event);
          console.log('已触发通知设置变更事件');
        } catch (error) {
          console.error('保存设置到本地存储失败:', error);
        }

        // 如果禁用了推送通知，提示用户
        if (generalSettings?.push_notifications && !values.push_notifications) {
          message.info('已禁用推送通知，您将不会收到实时通知', 5);
        }
      } else {
        message.error('通知设置保存失败');
      }
    } catch (error) {
      console.error('保存通知设置失败:', error);
      message.error('保存通知设置失败');
    } finally {
      setLoading(false);
    }
  };

  // 处理保存工作流设置
  const handleSaveWorkflowSettings = async (values) => {
    setLoading(true);
    try {
      const response = await apiService.project.notifications.updateWorkflowSettings(
        currentWorkflowType,
        values
      );
      if (response && response.success) {
        message.success('工作流通知设置保存成功');
        fetchWorkflowSettings(currentWorkflowType);
      } else {
        message.error('工作流通知设置保存失败');
      }
    } catch (error) {
      console.error('保存工作流通知设置失败:', error);
      message.error('保存工作流通知设置失败');
    } finally {
      setLoading(false);
    }
  };

  // 处理工作流类型切换
  const handleWorkflowTypeChange = (type) => {
    setCurrentWorkflowType(type);

    // 如果已经加载了该工作流的设置，则填充表单
    if (workflowSettings[type]) {
      workflowForm.setFieldsValue({
        enabled: workflowSettings[type].enabled,
        notify_type: workflowSettings[type].notify_type,
        notify_roles: workflowSettings[type].notify_roles,
        notify_users: workflowSettings[type].notify_users,
        title: workflowSettings[type].title,
        template: workflowSettings[type].template
      });
    } else {
      // 否则加载该工作流的设置
      fetchWorkflowSettings(type);
    }
  };

  // 渲染通用设置标签页
  const renderGeneralTab = () => {
    return (
      <Form
        form={generalForm}
        layout="vertical"
        onFinish={handleSaveGeneralSettings}
        initialValues={{
          normal_frequency: 'realtime',
          high_priority_frequency: 'realtime',
          email_notifications: true,
          push_notifications: true,
          sms_notifications: false,
          sound_enabled: true,
          system_notifications: true,
          task_notifications: true,
          inventory_notifications: true,
          order_notifications: true,
          security_notifications: true
        }}
      >
        <Divider orientation="left">通知频率设置</Divider>
        <Row gutter={16}>
          <Col span={12}>
            <Form.Item
              name="normal_frequency"
              label="普通通知频率"
              tooltip="设置普通优先级通知的接收频率"
            >
              <Radio.Group>
                <Radio.Button value="realtime">
                  <ClockCircleOutlined /> 实时
                </Radio.Button>
                <Radio.Button value="hourly">
                  <ClockCircleOutlined /> 每小时
                </Radio.Button>
                <Radio.Button value="daily">
                  <ClockCircleOutlined /> 每天
                </Radio.Button>
              </Radio.Group>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="high_priority_frequency"
              label="高优先级通知频率"
              tooltip="设置高优先级通知的接收频率"
            >
              <Radio.Group>
                <Radio.Button value="realtime">
                  <ClockCircleOutlined /> 实时
                </Radio.Button>
                <Radio.Button value="hourly">
                  <ClockCircleOutlined /> 每小时
                </Radio.Button>
                <Radio.Button value="daily">
                  <ClockCircleOutlined /> 每天
                </Radio.Button>
              </Radio.Group>
            </Form.Item>
          </Col>
        </Row>

        <Divider orientation="left">通知方式设置</Divider>
        <Row gutter={16}>
          <Col span={6}>
            <Form.Item
              name="email_notifications"
              label="邮件通知"
              valuePropName="checked"
              tooltip="是否接收邮件通知"
            >
              <Switch checkedChildren={<MailOutlined />} unCheckedChildren={<MailOutlined />} />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item
              name="push_notifications"
              label="推送通知"
              valuePropName="checked"
              tooltip="是否接收推送通知"
            >
              <Switch checkedChildren={<BellOutlined />} unCheckedChildren={<BellOutlined />} />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item
              name="sms_notifications"
              label="短信通知"
              valuePropName="checked"
              tooltip="是否接收短信通知"
            >
              <Switch checkedChildren={<MobileOutlined />} unCheckedChildren={<MobileOutlined />} />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item
              name="sound_enabled"
              label="声音提醒"
              valuePropName="checked"
              tooltip="是否开启声音提醒"
            >
              <Switch checkedChildren={<SoundOutlined />} unCheckedChildren={<SoundOutlined />} />
            </Form.Item>
          </Col>
        </Row>

        <Divider orientation="left">通知类型设置</Divider>
        <Row gutter={16}>
          <Col span={6}>
            <Form.Item
              name="system_notifications"
              label="系统通知"
              valuePropName="checked"
              tooltip="是否接收系统通知"
            >
              <Switch checkedChildren="开启" unCheckedChildren="关闭" />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item
              name="task_notifications"
              label="任务通知"
              valuePropName="checked"
              tooltip="是否接收任务通知"
            >
              <Switch checkedChildren="开启" unCheckedChildren="关闭" />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item
              name="inventory_notifications"
              label="库存通知"
              valuePropName="checked"
              tooltip="是否接收库存通知"
            >
              <Switch checkedChildren="开启" unCheckedChildren="关闭" />
            </Form.Item>
          </Col>
          <Col span={6}>
            <Form.Item
              name="order_notifications"
              label="订单通知"
              valuePropName="checked"
              tooltip="是否接收订单通知"
            >
              <Switch checkedChildren="开启" unCheckedChildren="关闭" />
            </Form.Item>
          </Col>
        </Row>
        <Row gutter={16}>
          <Col span={6}>
            <Form.Item
              name="security_notifications"
              label="安全通知"
              valuePropName="checked"
              tooltip="是否接收安全通知"
            >
              <Switch checkedChildren="开启" unCheckedChildren="关闭" />
            </Form.Item>
          </Col>
        </Row>

        <Form.Item>
          <Button type="primary" htmlType="submit" icon={<SaveOutlined />} loading={loading}>
            保存设置
          </Button>
        </Form.Item>
      </Form>
    );
  };

  // 渲染工作流设置标签页
  const renderWorkflowTab = () => {
    return (
      <div>
        <div style={{ marginBottom: 16 }}>
          <Space>
            {workflowTypes.map(type => (
              <Button
                key={type.key}
                type={currentWorkflowType === type.key ? 'primary' : 'default'}
                onClick={() => handleWorkflowTypeChange(type.key)}
              >
                {type.name}
              </Button>
            ))}
          </Space>
        </div>

        <Form
          form={workflowForm}
          layout="vertical"
          onFinish={handleSaveWorkflowSettings}
          initialValues={{
            enabled: true,
            notify_type: 'roles',
            notify_roles: ['admin'],
            notify_users: [],
            title: '',
            template: ''
          }}
        >
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name="enabled"
                label="启用通知"
                valuePropName="checked"
                tooltip="是否启用此工作流的通知"
              >
                <Switch checkedChildren="启用" unCheckedChildren="禁用" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="notify_type"
                label="通知方式"
                tooltip="选择按角色通知还是按用户通知"
              >
                <Radio.Group>
                  <Radio.Button value="roles">按角色通知</Radio.Button>
                  <Radio.Button value="users">按用户通知</Radio.Button>
                </Radio.Group>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) => prevValues.notify_type !== currentValues.notify_type}
          >
            {({ getFieldValue }) => {
              const notifyType = getFieldValue('notify_type');
              return notifyType === 'roles' ? (
                <Form.Item
                  name="notify_roles"
                  label="通知角色"
                  tooltip="选择需要通知的角色"
                  rules={[{ required: true, message: '请选择通知角色' }]}
                >
                  <Select mode="multiple" placeholder="请选择角色">
                    <Option value="admin">管理员</Option>
                    <Option value="store">门店</Option>
                    <Option value="purchase">采购</Option>
                    <Option value="finance">财务</Option>
                    <Option value="warehouse">仓库</Option>
                    <Option value="operation">运营</Option>
                  </Select>
                </Form.Item>
              ) : (
                <Form.Item
                  name="notify_users"
                  label="通知用户"
                  tooltip="选择需要通知的用户"
                  rules={[{ required: true, message: '请选择通知用户' }]}
                >
                  <Select mode="multiple" placeholder="请选择用户">
                    <Option value="user1">用户1</Option>
                    <Option value="user2">用户2</Option>
                    <Option value="user3">用户3</Option>
                  </Select>
                </Form.Item>
              );
            }}
          </Form.Item>

          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name="title"
                label="通知标题"
                tooltip="通知的标题模板，可使用变量"
                rules={[{ required: true, message: '请输入通知标题' }]}
              >
                <Input placeholder="请输入通知标题" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                name="template"
                label="通知内容模板"
                tooltip="通知的内容模板，可使用变量"
                rules={[{ required: true, message: '请输入通知内容模板' }]}
              >
                <TextArea
                  rows={4}
                  placeholder="请输入通知内容模板，可使用变量，如：{{store_name}}、{{product_name}}等"
                />
              </Form.Item>
            </Col>
          </Row>

          <div style={{ marginBottom: 16 }}>
            <Tooltip title="可用变量说明">
              <InfoCircleOutlined style={{ marginRight: 8 }} />
              <Text type="secondary">可用变量说明：</Text>
            </Tooltip>
            <ul>
              <li><Text code>{'{{store_name}}'}</Text> - 门店名称</li>
              <li><Text code>{'{{product_name}}'}</Text> - 产品名称</li>
              <li><Text code>{'{{quantity}}'}</Text> - 数量</li>
              <li><Text code>{'{{amount}}'}</Text> - 金额</li>
              <li><Text code>{'{{user_name}}'}</Text> - 用户名称</li>
              <li><Text code>{'{{date}}'}</Text> - 日期</li>
            </ul>
          </div>

          <Form.Item>
            <Button type="primary" htmlType="submit" icon={<SaveOutlined />} loading={loading}>
              保存设置
            </Button>
          </Form.Item>
        </Form>
      </div>
    );
  };

  return (
    <Card title="通知设置">
      <Tabs activeKey={activeTab} onChange={handleTabChange}>
        <TabPane
          tab={
            <span>
              <SettingOutlined />
              通用设置
            </span>
          }
          key="general"
        >
          {renderGeneralTab()}
        </TabPane>
        <TabPane
          tab={
            <span>
              <BellOutlined />
              工作流通知
            </span>
          }
          key="workflow"
        >
          {renderWorkflowTab()}
        </TabPane>
      </Tabs>
    </Card>
  );
};

export default NotificationSettings;
