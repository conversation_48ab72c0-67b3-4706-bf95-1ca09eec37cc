import React, { useState } from 'react';
import {
  Table, Button, Space, Modal, Form, Input, Select,
  Popconfirm, message, Row, Col, Tag
} from 'antd';
import {
  PlusOutlined, EditOutlined, DeleteOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import apiService from '../../../../services/api';

const { Option } = Select;
// const { TextArea } = Input;

/**
 * 仓库列表组件
 * 用于管理仓库基本信息和仓库列表
 */
const WarehouseList = ({ warehouses, loading, refreshWarehouses }) => {
  const [warehouseModalOpen, setWarehouseModalOpen] = useState(false);
  const [editingWarehouse, setEditingWarehouse] = useState(null);
  const [warehouseForm] = Form.useForm();
  const [users, setUsers] = useState([]);
  const [loadingUsers, setLoadingUsers] = useState(false);

  // 获取仓库管理员用户列表
  const fetchUsers = async () => {
    setLoadingUsers(true);
    try {
      // 使用专门的获取仓库管理员API
      const response = await apiService.project.warehouse.getManagers();
      console.log('获取仓库管理员列表响应:', response);
      
      // 处理API返回的数据结构
      if (response && Array.isArray(response)) {
        setUsers(response);
      } else if (response && response.data && Array.isArray(response.data)) {
        setUsers(response.data);
      } else {
        console.warn('获取仓库管理员列表返回格式异常:', response);
        setUsers([]);
      }
    } catch (error) {
      console.error('获取仓库管理员列表失败:', error);
      // 显示错误消息，让用户知道问题
      message.warning('获取仓库管理员列表失败，请检查是否已配置仓库管理员角色');
      setUsers([]);
    } finally {
      setLoadingUsers(false);
    }
  };

  // 显示创建仓库模态框
  const showCreateWarehouseModal = () => {
    setEditingWarehouse(null);
    warehouseForm.resetFields();
    warehouseForm.setFieldsValue({
      type: 'distribution',
      status: 'active',
      capacity_unit: 'sqm'
    });
    fetchUsers();
    setWarehouseModalOpen(true);
  };

  // 显示编辑仓库模态框
  const showEditWarehouseModal = (warehouse) => {
    setEditingWarehouse(warehouse);
    fetchUsers();
    warehouseForm.setFieldsValue({
      name: warehouse.name,
      code: warehouse.code,
      address: warehouse.address,
      type: warehouse.type,
      manager: warehouse.manager,
      manager_id: warehouse.manager_id,
      phone: warehouse.phone,
      capacity: warehouse.capacity,
      capacity_unit: warehouse.capacity_unit,
      status: warehouse.status,
      business_hours: warehouse.business_hours
    });
    setWarehouseModalOpen(true);
  };

  // 处理仓库表单提交
  const handleWarehouseSubmit = async () => {
    try {
      const values = await warehouseForm.validateFields();

      try {
        // 转换manager_id为manager_user_id供后端使用
        const submitData = { ...values };
        if (submitData.manager_id) {
          submitData.manager_user_id = submitData.manager_id;
          delete submitData.manager_id;
        }

        if (editingWarehouse) {
          // 更新仓库
          await apiService.project.warehouse.update(editingWarehouse.id, submitData);
          message.success('仓库更新成功');
          refreshWarehouses();
        } else {
          // 创建仓库
          await apiService.project.warehouse.create(submitData);
          message.success('仓库创建成功');
          refreshWarehouses();
        }

        setWarehouseModalOpen(false);
      } catch (error) {
        console.error('保存仓库失败:', error);
        message.error('保存仓库失败: ' + (error.message || '未知错误'));
      }
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  // 处理删除仓库
  const handleDeleteWarehouse = async (id) => {
    try {
      await apiService.project.warehouse.delete(id);
      message.success('仓库删除成功');
      refreshWarehouses();
    } catch (error) {
      console.error('删除仓库失败:', error);
      message.error('删除仓库失败: ' + (error.message || '未知错误'));
    }
  };

  // 仓库类型映射
  const warehouseTypeMap = {
    central: { text: '总仓', color: 'blue' },
    regional: { text: '区域仓', color: 'green' },
    distribution: { text: '配送中心', color: 'orange' },
    front: { text: '前置仓', color: 'purple' }
  };

  // 仓库状态映射
  const warehouseStatusMap = {
    active: { text: '正常', color: 'success' },
    inactive: { text: '停用', color: 'warning' },
    closed: { text: '关闭', color: 'error' }
  };

  // 仓库列定义
  const warehouseColumns = [
    {
      title: '仓库名称',
      dataIndex: 'name',
      key: 'name'
    },
    {
      title: '仓库编码',
      dataIndex: 'code',
      key: 'code'
    },
    {
      title: '仓库类型',
      dataIndex: 'type',
      key: 'type',
      render: (type) => {
        const { text, color } = warehouseTypeMap[type] || { text: type, color: 'default' };
        return <Tag color={color}>{text}</Tag>;
      }
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => {
        const { text, color } = warehouseStatusMap[status] || { text: status, color: 'default' };
        return <Tag color={color}>{text}</Tag>;
      }
    },
    {
      title: '地址',
      dataIndex: 'address',
      key: 'address',
      ellipsis: true
    },
    {
      title: '仓库容量',
      dataIndex: 'capacity',
      key: 'capacity',
      render: (capacity, record) => `${capacity} ${record.capacity_unit === 'sqm' ? '平方米' : '立方米'}`
    },
    {
      title: '仓库经理',
      dataIndex: 'manager',
      key: 'manager'
    },
    {
      title: '联系电话',
      dataIndex: 'phone',
      key: 'phone'
    },
    {
      title: '营业时间',
      dataIndex: 'business_hours',
      key: 'business_hours'
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => showEditWarehouseModal(record)}
          />
          <Popconfirm
            title="确定要删除这个仓库吗？"
            onConfirm={() => handleDeleteWarehouse(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
            />
          </Popconfirm>
        </Space>
      )
    }
  ];

  return (
    <div className="warehouse-list">
      <div style={{ marginBottom: 16 }}>
        <Space>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={showCreateWarehouseModal}
          >
            新增仓库
          </Button>
          <Button
            icon={<ReloadOutlined />}
            onClick={refreshWarehouses}
          >
            刷新
          </Button>
        </Space>
      </div>

      <Table
        columns={warehouseColumns}
        dataSource={warehouses}
        rowKey="id"
        loading={loading}
        pagination={{ pageSize: 10 }}
      />

      {/* 仓库表单模态框 */}
      <Modal
        title={editingWarehouse ? '编辑仓库' : '新增仓库'}
        open={warehouseModalOpen}
        onOk={handleWarehouseSubmit}
        onCancel={() => setWarehouseModalOpen(false)}
        width={700}
      >
        <Form
          form={warehouseForm}
          layout="vertical"
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="仓库名称"
                rules={[{ required: true, message: '请输入仓库名称' }]}
              >
                <Input placeholder="请输入仓库名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="code"
                label="仓库编码"
                rules={[{ required: true, message: '请输入仓库编码' }]}
              >
                <Input placeholder="请输入仓库编码" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="type"
                label="仓库类型"
                rules={[{ required: true, message: '请选择仓库类型' }]}
              >
                <Select placeholder="请选择仓库类型">
                  <Option value="central">总仓</Option>
                  <Option value="regional">区域仓</Option>
                  <Option value="distribution">配送中心</Option>
                  <Option value="front">前置仓</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="status"
                label="状态"
                rules={[{ required: true, message: '请选择状态' }]}
              >
                <Select placeholder="请选择状态">
                  <Option value="active">正常</Option>
                  <Option value="inactive">停用</Option>
                  <Option value="closed">关闭</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="address"
            label="地址"
          >
            <Input placeholder="请输入地址" />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="manager_id"
                label="仓库经理"
              >
                <Select
                  placeholder="请选择仓库经理"
                  loading={loadingUsers}
                  allowClear
                  showSearch
                  filterOption={(input, option) =>
                    option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                  }
                  onChange={(value, option) => {
                    if (value && option) {
                      warehouseForm.setFieldsValue({ manager: option.children });
                    }
                  }}
                >
                  {users.map(user => (
                    <Option 
                      key={user.project_user_id} 
                      value={user.project_user_id}
                      title={`${user.display_name} (${user.username})`}
                    >
                      {user.display_name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
              <Form.Item name="manager" hidden>
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="phone"
                label="联系电话"
              >
                <Input placeholder="请输入联系电话" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="capacity"
                label="仓库容量"
              >
                <Input placeholder="请输入仓库容量" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="capacity_unit"
                label="容量单位"
              >
                <Select placeholder="请选择容量单位">
                  <Option value="sqm">平方米</Option>
                  <Option value="cbm">立方米</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="business_hours"
            label="营业时间"
          >
            <Input placeholder="请输入营业时间，如：09:00-18:00" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default WarehouseList;
