#!/usr/bin/env python
# -*- coding: utf-8 -*-

import logging
import asyncio
import httpx
import json
import uuid
from typing import Dict, Any, List, Optional
from datetime import datetime
from sqlalchemy.orm import Session
from sqlalchemy import select, and_

# 导入知识库相关模型和服务
from models.knowledge_base import KnowledgeDocument, KnowledgeCategory
from models.ai.knowledge import AIKnowledgeBase, AIKnowledgeDocument
from services.knowledge_base import KnowledgeBaseService
from services.ai.knowledge_service import AIKnowledgeService

logger = logging.getLogger(__name__)

class DingTalkKnowledgeSync:
    """钉钉知识库同步器"""
    
    def __init__(self, access_token: str, db: Session = None):
        self.access_token = access_token
        self.base_url = "https://oapi.dingtalk.com"
        self.db = db
        
    async def sync_knowledge_base(self, tenant_id: str, project_id: str, options: Dict[str, Any]) -> Dict[str, Any]:
        """
        同步钉钉知识库到项目知识库系统
        
        Args:
            tenant_id: 租户ID
            project_id: 项目ID
            options: 同步选项
            
        Returns:
            同步结果
        """
        try:
            sync_type = options.get("sync_type", "full")
            target_knowledge_bases = options.get("target_knowledge_bases", ["project", "ai"])  # 默认同步到项目知识库和AI知识库
            
            sync_result = {
                "success": False,
                "sync_type": sync_type,
                "target_knowledge_bases": target_knowledge_bases,
                "start_time": datetime.now().isoformat(),
                "synced_items": [],
                "errors": [],
                "statistics": {
                    "total_items": 0,
                    "synced_count": 0,
                    "error_count": 0,
                    "project_kb_synced": 0,
                    "ai_kb_synced": 0
                }
            }
            
            # 确保钉钉知识库分类存在
            dingtalk_category = await self._ensure_dingtalk_category(tenant_id, project_id)
            
            # 确保AI知识库存在
            ai_knowledge_base = None
            if "ai" in target_knowledge_bases:
                ai_knowledge_base = await self._ensure_ai_knowledge_base(tenant_id, project_id)
            
            if sync_type == "full":
                result = await self._full_sync(tenant_id, project_id, options, dingtalk_category, ai_knowledge_base)
            elif sync_type == "incremental":
                result = await self._incremental_sync(tenant_id, project_id, options, dingtalk_category, ai_knowledge_base)
            elif sync_type == "selective":
                result = await self._selective_sync(tenant_id, project_id, options, dingtalk_category, ai_knowledge_base)
            else:
                raise ValueError(f"不支持的同步类型: {sync_type}")
            
            sync_result.update(result)
            sync_result["end_time"] = datetime.now().isoformat()
            sync_result["success"] = True
            
            logger.info(f"知识库同步完成: {sync_result['statistics']}")
            return sync_result
            
        except Exception as e:
            logger.error(f"知识库同步失败: {str(e)}")
            sync_result["success"] = False
            sync_result["error"] = str(e)
            sync_result["end_time"] = datetime.now().isoformat()
            return sync_result
    
    async def _ensure_dingtalk_category(self, tenant_id: str, project_id: str) -> KnowledgeCategory:
        """确保钉钉知识库分类存在"""
        if not self.db:
            raise ValueError("数据库会话未提供")
        
        # 查找现有的钉钉分类
        query = select(KnowledgeCategory).where(
            and_(
                KnowledgeCategory.project_id == project_id,
                KnowledgeCategory.name == "钉钉知识库"
            )
        )
        result = self.db.execute(query)
        category = result.scalar_one_or_none()
        
        if not category:
            # 创建钉钉知识库分类
            category_data = {
                "project_id": project_id,
                "name": "钉钉知识库",
                "description": "从钉钉平台同步的知识库内容，包括部门信息、用户信息、群组信息等"
            }
            category = await KnowledgeBaseService.create_category(self.db, category_data)
            logger.info(f"创建钉钉知识库分类: {category.id}")
        
        return category
    
    async def _ensure_ai_knowledge_base(self, tenant_id: str, project_id: str) -> AIKnowledgeBase:
        """确保AI知识库存在"""
        if not self.db:
            raise ValueError("数据库会话未提供")
        
        # 查找现有的钉钉AI知识库
        query = select(AIKnowledgeBase).where(
            and_(
                AIKnowledgeBase.tenant_id == tenant_id,
                AIKnowledgeBase.project_id == project_id,
                AIKnowledgeBase.name == "钉钉知识库"
            )
        )
        result = self.db.execute(query)
        ai_kb = result.scalar_one_or_none()
        
        if not ai_kb:
            # 创建钉钉AI知识库
            ai_kb_data = {
                "tenant_id": tenant_id,
                "project_id": project_id,
                "name": "钉钉知识库",
                "description": "从钉钉平台同步的AI知识库，用于AI助手回答钉钉相关问题",
                "industry_type": "general",
                "status": "active"
            }
            ai_kb = await AIKnowledgeService.create_knowledge_base(self.db, ai_kb_data)
            logger.info(f"创建钉钉AI知识库: {ai_kb.id}")
        
        return ai_kb
    
    async def _full_sync(self, tenant_id: str, project_id: str, options: Dict[str, Any], 
                        dingtalk_category: KnowledgeCategory, ai_knowledge_base: AIKnowledgeBase) -> Dict[str, Any]:
        """全量同步"""
        result = {
            "synced_items": [],
            "errors": [],
            "statistics": {"total_items": 0, "synced_count": 0, "error_count": 0, "project_kb_synced": 0, "ai_kb_synced": 0}
        }
        
        try:
            # 1. 同步部门信息
            departments = await self._sync_departments()
            if departments:
                await self._save_to_knowledge_bases(
                    departments, "部门信息", dingtalk_category, ai_knowledge_base, 
                    project_id, options.get("target_knowledge_bases", ["project", "ai"]), result
                )
            
            # 2. 同步用户信息
            users = await self._sync_users()
            if users:
                await self._save_to_knowledge_bases(
                    users, "用户信息", dingtalk_category, ai_knowledge_base, 
                    project_id, options.get("target_knowledge_bases", ["project", "ai"]), result
                )
            
            # 3. 同步群组信息
            groups = await self._sync_groups()
            if groups:
                await self._save_to_knowledge_bases(
                    groups, "群组信息", dingtalk_category, ai_knowledge_base, 
                    project_id, options.get("target_knowledge_bases", ["project", "ai"]), result
                )
            
            # 4. 同步文档信息（如果有权限）
            if options.get("include_documents", False):
                documents = await self._sync_documents()
                if documents:
                    await self._save_to_knowledge_bases(
                        documents, "文档信息", dingtalk_category, ai_knowledge_base, 
                        project_id, options.get("target_knowledge_bases", ["project", "ai"]), result
                    )
            
            result["statistics"]["total_items"] = result["statistics"]["synced_count"]
            
        except Exception as e:
            result["errors"].append(f"全量同步失败: {str(e)}")
            result["statistics"]["error_count"] += 1
        
        return result
    
    async def _save_to_knowledge_bases(self, items: List[Dict[str, Any]], content_type: str,
                                     dingtalk_category: KnowledgeCategory, ai_knowledge_base: AIKnowledgeBase,
                                     project_id: str, target_knowledge_bases: List[str], result: Dict[str, Any]):
        """保存数据到知识库"""
        try:
            # 生成文档内容
            content = self._format_content(items, content_type)
            title = f"钉钉{content_type} - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            
            # 保存到项目知识库
            if "project" in target_knowledge_bases and self.db:
                try:
                    document_data = {
                        "project_id": project_id,
                        "category_id": dingtalk_category.id,
                        "title": title,
                        "description": f"从钉钉平台同步的{content_type}",
                        "content": content,
                        "is_public": True,
                        "document_metadata": {
                            "source": "dingtalk",
                            "sync_time": datetime.now().isoformat(),
                            "content_type": content_type,
                            "item_count": len(items)
                        }
                    }
                    
                    project_doc = await KnowledgeBaseService.create_document(self.db, document_data)
                    result["statistics"]["project_kb_synced"] += 1
                    result["synced_items"].append({
                        "type": "project_knowledge_document",
                        "id": str(project_doc.id),
                        "title": title,
                        "content_type": content_type,
                        "item_count": len(items)
                    })
                    logger.info(f"保存到项目知识库: {project_doc.id}")
                    
                except Exception as e:
                    result["errors"].append(f"保存到项目知识库失败: {str(e)}")
                    result["statistics"]["error_count"] += 1
            
            # 保存到AI知识库
            if "ai" in target_knowledge_bases and ai_knowledge_base and self.db:
                try:
                    ai_doc_data = {
                        "knowledge_base_id": ai_knowledge_base.id,
                        "title": title,
                        "content": content,
                        "file_type": "text",
                        "request_metadata": {
                            "source": "dingtalk",
                            "sync_time": datetime.now().isoformat(),
                            "content_type": content_type,
                            "item_count": len(items)
                        }
                    }
                    
                    ai_doc = await AIKnowledgeService.create_document(self.db, ai_doc_data)
                    result["statistics"]["ai_kb_synced"] += 1
                    result["synced_items"].append({
                        "type": "ai_knowledge_document",
                        "id": str(ai_doc.id),
                        "title": title,
                        "content_type": content_type,
                        "item_count": len(items)
                    })
                    logger.info(f"保存到AI知识库: {ai_doc.id}")
                    
                except Exception as e:
                    result["errors"].append(f"保存到AI知识库失败: {str(e)}")
                    result["statistics"]["error_count"] += 1
            
            result["statistics"]["synced_count"] += len(items)
            
        except Exception as e:
            result["errors"].append(f"保存{content_type}失败: {str(e)}")
            result["statistics"]["error_count"] += 1
    
    def _format_content(self, items: List[Dict[str, Any]], content_type: str) -> str:
        """格式化内容为可读的文档"""
        content_lines = [f"# 钉钉{content_type}\n"]
        content_lines.append(f"同步时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        content_lines.append(f"数据条数: {len(items)}\n")
        
        if content_type == "部门信息":
            content_lines.append("## 部门列表\n")
            for item in items:
                content_lines.append(f"- **{item.get('name', 'N/A')}** (ID: {item.get('id', 'N/A')})")
                if item.get('parent_id'):
                    content_lines.append(f"  - 上级部门ID: {item.get('parent_id')}")
                content_lines.append("")
                
        elif content_type == "用户信息":
            content_lines.append("## 用户列表\n")
            for item in items:
                content_lines.append(f"- **{item.get('name', 'N/A')}** (ID: {item.get('id', 'N/A')})")
                if item.get('dept_id'):
                    content_lines.append(f"  - 部门ID: {item.get('dept_id')}")
                content_lines.append("")
                
        elif content_type == "群组信息":
            content_lines.append("## 群组列表\n")
            for item in items:
                content_lines.append(f"- **{item.get('name', 'N/A')}** (ID: {item.get('id', 'N/A')})")
                if item.get('owner'):
                    content_lines.append(f"  - 群主: {item.get('owner')}")
                if item.get('member_count'):
                    content_lines.append(f"  - 成员数: {item.get('member_count')}")
                content_lines.append("")
        
        return "\n".join(content_lines)
    
    async def _incremental_sync(self, tenant_id: str, project_id: str, options: Dict[str, Any],
                               dingtalk_category: KnowledgeCategory, ai_knowledge_base: AIKnowledgeBase) -> Dict[str, Any]:
        """增量同步"""
        result = {
            "synced_items": [],
            "errors": [],
            "statistics": {"total_items": 0, "synced_count": 0, "error_count": 0, "project_kb_synced": 0, "ai_kb_synced": 0}
        }
        
        try:
            # 获取上次同步时间
            last_sync_time = options.get("last_sync_time")
            if not last_sync_time:
                result["errors"].append("增量同步需要提供上次同步时间")
                result["statistics"]["error_count"] += 1
                return result
            
            # 获取变更的数据
            changes = await self._get_changes_since(last_sync_time)
            
            if changes:
                await self._save_to_knowledge_bases(
                    changes, "增量更新", dingtalk_category, ai_knowledge_base, 
                    project_id, options.get("target_knowledge_bases", ["project", "ai"]), result
                )
            
            result["statistics"]["total_items"] = len(changes)
            
        except Exception as e:
            result["errors"].append(f"增量同步失败: {str(e)}")
            result["statistics"]["error_count"] += 1
        
        return result
    
    async def _selective_sync(self, tenant_id: str, project_id: str, options: Dict[str, Any],
                             dingtalk_category: KnowledgeCategory, ai_knowledge_base: AIKnowledgeBase) -> Dict[str, Any]:
        """选择性同步"""
        result = {
            "synced_items": [],
            "errors": [],
            "statistics": {"total_items": 0, "synced_count": 0, "error_count": 0, "project_kb_synced": 0, "ai_kb_synced": 0}
        }
        
        try:
            sync_targets = options.get("sync_targets", [])
            if not sync_targets:
                result["errors"].append("选择性同步需要指定同步目标")
                result["statistics"]["error_count"] += 1
                return result
            
            for target in sync_targets:
                try:
                    if target["type"] == "department":
                        items = await self._sync_department_by_id(target["id"])
                        content_type = "部门信息"
                    elif target["type"] == "user":
                        items = await self._sync_user_by_id(target["id"])
                        content_type = "用户信息"
                    elif target["type"] == "group":
                        items = await self._sync_group_by_id(target["id"])
                        content_type = "群组信息"
                    else:
                        result["errors"].append(f"不支持的同步目标类型: {target['type']}")
                        result["statistics"]["error_count"] += 1
                        continue
                    
                    if items:
                        await self._save_to_knowledge_bases(
                            items, content_type, dingtalk_category, ai_knowledge_base, 
                            project_id, options.get("target_knowledge_bases", ["project", "ai"]), result
                        )
                    
                except Exception as e:
                    result["errors"].append(f"同步目标 {target} 失败: {str(e)}")
                    result["statistics"]["error_count"] += 1
            
            result["statistics"]["total_items"] = len(sync_targets)
            
        except Exception as e:
            result["errors"].append(f"选择性同步失败: {str(e)}")
            result["statistics"]["error_count"] += 1
        
        return result

    async def _sync_departments(self) -> List[Dict[str, Any]]:
        """同步部门信息"""
        departments = []
        try:
            async with httpx.AsyncClient() as client:
                url = f"{self.base_url}/topapi/v2/department/listsub"
                params = {"access_token": self.access_token}
                data = {"dept_id": 1}  # 从根部门开始
                
                response = await client.post(url, params=params, json=data)
                if response.status_code == 200:
                    result = response.json()
                    if result.get("errcode") == 0:
                        dept_list = result.get("result", [])
                        for dept in dept_list:
                            departments.append({
                                "type": "department",
                                "id": dept.get("dept_id"),
                                "name": dept.get("name"),
                                "parent_id": dept.get("parent_id"),
                                "synced_at": datetime.now().isoformat()
                            })
                    else:
                        logger.error(f"获取部门列表失败: {result.get('errmsg')}")
                else:
                    logger.error(f"请求部门列表失败: HTTP {response.status_code}")
        except Exception as e:
            logger.error(f"同步部门信息失败: {str(e)}")
        
        return departments
    
    async def _sync_users(self) -> List[Dict[str, Any]]:
        """同步用户信息"""
        users = []
        try:
            # 首先获取所有部门
            departments = await self._sync_departments()
            
            for dept in departments:
                dept_id = dept.get("id")
                if dept_id:
                    dept_users = await self._sync_users_in_department(dept_id)
                    users.extend(dept_users)
        except Exception as e:
            logger.error(f"同步用户信息失败: {str(e)}")
        
        return users
    
    async def _sync_users_in_department(self, dept_id: int) -> List[Dict[str, Any]]:
        """同步指定部门的用户"""
        users = []
        try:
            async with httpx.AsyncClient() as client:
                url = f"{self.base_url}/topapi/user/simplelist"
                params = {"access_token": self.access_token}
                data = {
                    "dept_id": dept_id,
                    "cursor": 0,
                    "size": 100
                }
                
                response = await client.post(url, params=params, json=data)
                if response.status_code == 200:
                    result = response.json()
                    if result.get("errcode") == 0:
                        user_list = result.get("result", {}).get("list", [])
                        for user in user_list:
                            users.append({
                                "type": "user",
                                "id": user.get("userid"),
                                "name": user.get("name"),
                                "dept_id": dept_id,
                                "synced_at": datetime.now().isoformat()
                            })
                    else:
                        logger.error(f"获取部门 {dept_id} 用户列表失败: {result.get('errmsg')}")
                else:
                    logger.error(f"请求部门 {dept_id} 用户列表失败: HTTP {response.status_code}")
        except Exception as e:
            logger.error(f"同步部门 {dept_id} 用户失败: {str(e)}")
        
        return users
    
    async def _sync_groups(self) -> List[Dict[str, Any]]:
        """同步群组信息"""
        groups = []
        try:
            async with httpx.AsyncClient() as client:
                url = f"{self.base_url}/topapi/im/chat/scenegroup/list"
                params = {"access_token": self.access_token}
                data = {
                    "cursor": 0,
                    "size": 100
                }
                
                response = await client.post(url, params=params, json=data)
                if response.status_code == 200:
                    result = response.json()
                    if result.get("errcode") == 0:
                        group_list = result.get("result", {}).get("group_list", [])
                        for group in group_list:
                            groups.append({
                                "type": "group",
                                "id": group.get("open_conversation_id"),
                                "name": group.get("title"),
                                "owner": group.get("owner"),
                                "member_count": group.get("member_count", 0),
                                "synced_at": datetime.now().isoformat()
                            })
                    else:
                        logger.error(f"获取群组列表失败: {result.get('errmsg')}")
                else:
                    logger.error(f"请求群组列表失败: HTTP {response.status_code}")
        except Exception as e:
            logger.error(f"同步群组信息失败: {str(e)}")
        
        return groups
    
    async def _sync_documents(self) -> List[Dict[str, Any]]:
        """同步文档信息（需要相应权限）"""
        documents = []
        # 注意：文档同步需要特殊权限，这里提供基础框架
        logger.info("文档同步功能需要特殊权限配置")
        return documents
    
    async def _get_changes_since(self, last_sync_time: str) -> List[Dict[str, Any]]:
        """获取自上次同步以来的变更"""
        changes = []
        # 这里需要根据钉钉API的具体实现来获取变更
        # 目前钉钉API可能不直接支持变更查询，需要通过其他方式实现
        logger.info(f"获取自 {last_sync_time} 以来的变更")
        return changes
    
    async def _sync_single_item(self, change: Dict[str, Any]) -> Dict[str, Any]:
        """同步单个项目"""
        # 根据变更类型同步单个项目
        item_type = change.get("type")
        item_id = change.get("id")
        
        if item_type == "department":
            return await self._sync_department_by_id(item_id)
        elif item_type == "user":
            return await self._sync_user_by_id(item_id)
        elif item_type == "group":
            return await self._sync_group_by_id(item_id)
        else:
            raise ValueError(f"不支持的项目类型: {item_type}")
    
    async def _sync_department_by_id(self, dept_id: str) -> List[Dict[str, Any]]:
        """根据ID同步部门"""
        # 实现部门同步逻辑
        return [{"type": "department", "id": dept_id, "synced_at": datetime.now().isoformat()}]
    
    async def _sync_user_by_id(self, user_id: str) -> List[Dict[str, Any]]:
        """根据ID同步用户"""
        # 实现用户同步逻辑
        return [{"type": "user", "id": user_id, "synced_at": datetime.now().isoformat()}]
    
    async def _sync_group_by_id(self, group_id: str) -> List[Dict[str, Any]]:
        """根据ID同步群组"""
        # 实现群组同步逻辑
        return [{"type": "group", "id": group_id, "synced_at": datetime.now().isoformat()}] 