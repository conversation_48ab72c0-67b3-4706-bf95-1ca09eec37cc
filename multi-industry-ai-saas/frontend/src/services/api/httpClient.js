import axios from 'axios';

// 验证UUID格式
const isValidUUID = (uuid) => {
  if (!uuid) return false;
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
  return uuidRegex.test(uuid);
};

// 创建axios实例 - 用于业务级API (v1路径)
const api = axios.create({
  baseURL: '/api/v1',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    // 从localStorage获取token
    const token = localStorage.getItem('token');

    // 如果有token，添加到请求头
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }

    // 从localStorage获取租户ID和项目ID
    const tenantId = localStorage.getItem('tenant_id');
    const projectId = localStorage.getItem('project_id');
    const serviceProviderId = localStorage.getItem('service_provider_id');

    // 如果有租户ID，验证是否为有效的UUID格式，然后添加到请求头
    if (tenantId) {
      if (isValidUUID(tenantId)) {
        config.headers['X-Tenant-ID'] = tenantId;
      } else {
        console.warn('租户ID不是有效的UUID格式，不添加到请求头:', tenantId);
        // 移除无效的租户ID
        localStorage.removeItem('tenant_id');
      }
    }

    // 如果有项目ID，验证是否为有效的UUID格式，然后添加到请求头
    if (projectId) {
      if (isValidUUID(projectId)) {
        config.headers['X-Project-ID'] = projectId;
        console.log('添加项目ID到请求头:', projectId);
      } else {
        console.warn('项目ID不是有效的UUID格式，不添加到请求头:', projectId);
        // 不移除无效的项目ID，避免导致其他组件无法获取项目ID
        // localStorage.removeItem('project_id');
      }
    } else {
      // 尝试从用户数据中获取项目ID
      const userData = JSON.parse(localStorage.getItem('user_data') || '{}');
      if (userData && userData.project_id && isValidUUID(userData.project_id)) {
        // 如果用户数据中有项目ID，保存到localStorage并添加到请求头
        localStorage.setItem('project_id', userData.project_id);
        config.headers['X-Project-ID'] = userData.project_id;
        console.log('从用户数据中获取到项目ID:', userData.project_id);
      } else {
        // 只有在URL中包含 '/project/' 且不是项目选择页面时才显示警告
        const url = config.url || '';
        const isProjectRelatedRequest = url.includes('/project/') || url.includes('/api/v1/project/');
        const isProjectSelectionPage = window.location.pathname.includes('/tenant/projects') ||
                                      window.location.pathname === '/';

        if (isProjectRelatedRequest && !isProjectSelectionPage) {
          console.warn('未找到项目ID，请求可能无法获取项目相关数据:', url);
        }
      }
    }

    // 如果有服务商ID，验证是否为有效的UUID格式，然后添加到请求头
    if (serviceProviderId) {
      if (isValidUUID(serviceProviderId)) {
        config.headers['X-Service-Provider-ID'] = serviceProviderId;
      } else {
        console.warn('服务商ID不是有效的UUID格式，不添加到请求头:', serviceProviderId);
        // 移除无效的服务商ID
        localStorage.removeItem('service_provider_id');
      }
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response) => {
    return response.data;
  },
  (error) => {
    // 处理401错误（未授权）
    if (error.response && error.response.status === 401) {
      // 获取当前请求的URL路径
      const requestUrl = error.config.url;
      
      // 排除登录API路径，避免登录失败时刷新页面
      const isLoginRequest = requestUrl.includes('/auth/login');
      
      if (!isLoginRequest) {
        // 清除token和用户信息
        localStorage.removeItem('token');
        localStorage.removeItem('tenant_id');
        localStorage.removeItem('project_id');
        localStorage.removeItem('service_provider_id');

        // 重定向到登录页
        window.location.href = '/login';
      }
    }

    return Promise.reject(error);
  }
);

// 获取当前用户角色
export const getUserRole = () => {
  const currentUser = JSON.parse(localStorage.getItem('user') || '{}');

  if (currentUser.is_super_admin) {
    return 'super_admin';
  } else if (currentUser.is_service_provider_admin) {
    return 'service_provider_admin';
  } else if (currentUser.is_tenant_admin) {
    return 'tenant_admin';
  } else if (currentUser.is_project_admin) {
    return 'project_admin';
  } else {
    return 'user';
  }
};

// 判断当前用户是否为超级管理员
export const isSuperAdmin = () => {
  const currentUser = JSON.parse(localStorage.getItem('user') || '{}');
  return currentUser.is_super_admin === true;
};

// 判断当前用户是否为服务商管理员
export const isServiceProviderAdmin = () => {
  const currentUser = JSON.parse(localStorage.getItem('user') || '{}');
  return currentUser.is_service_provider_admin === true;
};

// 判断当前用户是否为租户管理员
export const isTenantAdmin = () => {
  const currentUser = JSON.parse(localStorage.getItem('user') || '{}');
  return currentUser.is_tenant_admin === true;
};

// 判断当前用户是否为项目管理员
export const isProjectAdmin = () => {
  const currentUser = JSON.parse(localStorage.getItem('user') || '{}');
  return currentUser.is_project_admin === true;
};

// 获取当前项目ID
export const getProjectId = () => {
  // 首先尝试从localStorage获取项目ID
  const projectId = localStorage.getItem('project_id');
  if (projectId) {
    return projectId;
  }

  // 如果localStorage中没有项目ID，尝试从用户数据中获取
  const userData = JSON.parse(localStorage.getItem('user_data') || '{}');
  if (userData && userData.project_id) {
    // 将项目ID保存到localStorage中，以便下次使用
    localStorage.setItem('project_id', userData.project_id);
    console.log('从用户数据中获取到项目ID:', userData.project_id);
    return userData.project_id;
  }

  return null;
};

// 获取当前租户ID
export const getTenantId = () => {
  return localStorage.getItem('tenant_id');
};

// 获取当前服务商ID
export const getServiceProviderId = () => {
  return localStorage.getItem('service_provider_id');
};

// 设置当前项目ID
export const setProjectId = (projectId) => {
  if (projectId) {
    localStorage.setItem('project_id', projectId);
  } else {
    localStorage.removeItem('project_id');
  }
};

// 设置当前租户ID
export const setTenantId = (tenantId) => {
  if (tenantId) {
    localStorage.setItem('tenant_id', tenantId);
  } else {
    localStorage.removeItem('tenant_id');
  }
};

// 设置当前服务商ID
export const setServiceProviderId = (serviceProviderId) => {
  if (serviceProviderId) {
    localStorage.setItem('service_provider_id', serviceProviderId);
  } else {
    localStorage.removeItem('service_provider_id');
  }
};

export default api;
