/* 增强版大转盘样式 */
.enhanced-wheel-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
  font-family: -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', <PERSON><PERSON>, 'Helvetica Neue', <PERSON><PERSON>, sans-serif;
}

/* 主题样式 */
.theme-classic {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.theme-neon {
  background: linear-gradient(135deg, #0c0c0c 0%, #1a1a1a 100%);
  color: #fff;
}

.theme-rainbow {
  background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
}

.theme-galaxy {
  background: radial-gradient(ellipse at center, #1e3c72 0%, #2a5298 100%);
  color: #fff;
}

/* 游戏头部 */
.game-header {
  margin-bottom: 24px;
}

.header-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.theme-neon .header-card {
  background: rgba(0, 0, 0, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.theme-galaxy .header-card {
  background: rgba(30, 60, 114, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* 转动按钮 */
.spin-button {
  background: linear-gradient(135deg, #ff6b6b, #ee5a6f);
  border: none;
  border-radius: 50px;
  font-weight: bold;
  font-size: 16px;
  height: 48px;
  padding: 0 32px;
  box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.spin-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 107, 107, 0.6);
}

.spin-button:active {
  transform: translateY(0);
}

.spin-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.5s;
}

.spin-button:hover::before {
  left: 100%;
}

/* 统计卡片 */
.stats-section {
  margin-bottom: 24px;
}

.stat-card {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  border: none;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.theme-neon .stat-card {
  background: rgba(0, 0, 0, 0.6);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.theme-galaxy .stat-card {
  background: rgba(30, 60, 114, 0.6);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* 转盘区域 */
.wheel-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  border: none;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  position: relative;
}

.theme-neon .wheel-card {
  background: rgba(0, 0, 0, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.theme-galaxy .wheel-card {
  background: rgba(30, 60, 114, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.wheel-container {
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px;
  min-height: 500px;
}

.wheel-canvas {
  border-radius: 50%;
  box-shadow: 0 0 50px rgba(0, 0, 0, 0.3);
  transition: transform 0.1s ease-out;
  cursor: pointer;
}

.theme-neon .wheel-canvas {
  box-shadow: 0 0 50px rgba(57, 255, 20, 0.5);
}

.theme-galaxy .wheel-canvas {
  box-shadow: 0 0 50px rgba(240, 147, 251, 0.5);
}

/* 转盘装饰 */
.wheel-decorations {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  pointer-events: none;
}

/* 固定指针 */
.wheel-pointer {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  pointer-events: none;
  z-index: 10;
}

.pointer-triangle {
  position: absolute;
  top: -210px; /* 调整到转盘边缘 */
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 20px solid transparent;
  border-right: 20px solid transparent;
  border-bottom: 30px solid #FF4444;
  filter: drop-shadow(2px 2px 4px rgba(0, 0, 0, 0.3));
}

.pointer-triangle::after {
  content: '';
  position: absolute;
  top: 30px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 15px solid transparent;
  border-right: 15px solid transparent;
  border-top: 20px solid #AA0000;
}

.glow-ring {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 420px;
  height: 420px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  animation: glow-pulse 2s ease-in-out infinite alternate;
}

@keyframes glow-pulse {
  0% {
    box-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
  }
  100% {
    box-shadow: 0 0 40px rgba(255, 255, 255, 0.8);
  }
}

.spark {
  position: absolute;
  width: 4px;
  height: 4px;
  background: #fff;
  border-radius: 50%;
  animation: sparkle 1.5s ease-in-out infinite;
}

.spark-1 {
  top: 10%;
  left: 50%;
  animation-delay: 0s;
}

.spark-2 {
  top: 50%;
  right: 10%;
  animation-delay: 0.3s;
}

.spark-3 {
  bottom: 10%;
  left: 50%;
  animation-delay: 0.6s;
}

.spark-4 {
  top: 50%;
  left: 10%;
  animation-delay: 0.9s;
}

@keyframes sparkle {
  0%, 100% {
    opacity: 0;
    transform: scale(0);
  }
  50% {
    opacity: 1;
    transform: scale(1);
  }
}

/* 进度条容器 */
.progress-container {
  position: absolute;
  top: 20px;
  right: 20px;
}

/* 奖品展示 */
.prizes-display {
  padding: 20px;
  background: rgba(0, 0, 0, 0.05);
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.theme-neon .prizes-display {
  background: rgba(255, 255, 255, 0.05);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.prize-card {
  text-align: center;
  border: none;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.prize-card:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.prize-name {
  display: block;
  margin-top: 8px;
  font-size: 12px;
  color: #666;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.theme-neon .prize-card {
  background: rgba(0, 0, 0, 0.6);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.theme-neon .prize-name {
  color: #fff;
}

/* 设置面板 */
.settings-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  border: none;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.theme-neon .settings-card {
  background: rgba(0, 0, 0, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.theme-galaxy .settings-card {
  background: rgba(30, 60, 114, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* 结果模态框 */
.result-modal .ant-modal-content {
  border-radius: 20px;
  overflow: hidden;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
}

.result-content {
  text-align: center;
  padding: 20px;
}

.win-result .celebration-icon {
  animation: celebration-bounce 1s ease-in-out infinite alternate;
}

@keyframes celebration-bounce {
  0% {
    transform: scale(1);
  }
  100% {
    transform: scale(1.1);
  }
}

.prize-info-card {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16px;
  padding: 20px;
  margin: 16px 0;
  color: #333;
}

.result-actions {
  margin-top: 24px;
}

/* 加载动画 */
.wheel-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 20px;
}

/* 粒子效果 */
.particle-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 9999;
}

.particle {
  position: absolute;
  pointer-events: none;
}

.particle-star {
  width: 8px;
  height: 8px;
  background: #FFD700;
  clip-path: polygon(50% 0%, 61% 35%, 98% 35%, 68% 57%, 79% 91%, 50% 70%, 21% 91%, 32% 57%, 2% 35%, 39% 35%);
  animation: particle-fall 3s linear forwards;
}

.particle-rect {
  width: 6px;
  height: 6px;
  background: #ff6b6b;
  animation: particle-fall 3s linear forwards;
}

.particle-circle {
  width: 8px;
  height: 8px;
  background: #4ecdc4;
  border-radius: 50%;
  animation: particle-explode 3s ease-out forwards;
}

.particle-heart {
  width: 12px;
  height: 12px;
  background: #FF69B4;
  position: relative;
  transform: rotate(-45deg);
  animation: particle-float 3s ease-out forwards;
}

.particle-heart:before,
.particle-heart:after {
  content: '';
  width: 12px;
  height: 12px;
  position: absolute;
  left: 6px;
  transform: rotate(-45deg);
  transform-origin: 0 6px;
  border-radius: 6px 6px 0 0;
  background: #FF69B4;
}

.particle-heart:after {
  left: 0;
  transform: rotate(45deg);
  transform-origin: 6px 6px;
}

@keyframes particle-fall {
  0% {
    opacity: 1;
    transform: translateY(-100vh) rotate(0deg);
  }
  100% {
    opacity: 0;
    transform: translateY(100vh) rotate(360deg);
  }
}

@keyframes particle-explode {
  0% {
    opacity: 1;
    transform: scale(0) translate(0, 0);
  }
  50% {
    opacity: 1;
    transform: scale(1) translate(0, 0);
  }
  100% {
    opacity: 0;
    transform: scale(0) translate(200px, -200px);
  }
}

@keyframes particle-float {
  0% {
    opacity: 1;
    transform: translateY(0) rotate(-45deg);
  }
  100% {
    opacity: 0;
    transform: translateY(-300px) rotate(-45deg);
  }
}

/* 转盘旋转动画 */
@keyframes wheel-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(1800deg);
  }
}

.wheel-spinning {
  animation: wheel-spin 3s cubic-bezier(0.23, 1, 0.32, 1) forwards;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .enhanced-wheel-container {
    padding: 12px;
  }
  
  .wheel-container {
    padding: 20px;
    min-height: 400px;
  }
  
  .wheel-canvas {
    width: 300px;
    height: 300px;
  }
  
  .glow-ring {
    width: 320px;
    height: 320px;
  }
  
  .prizes-display {
    padding: 12px;
  }
  
  .prize-card {
    margin-bottom: 8px;
  }
}

@media (max-width: 480px) {
  .wheel-canvas {
    width: 250px;
    height: 250px;
  }
  
  .glow-ring {
    width: 270px;
    height: 270px;
  }
  
  .spin-button {
    height: 40px;
    font-size: 14px;
    padding: 0 24px;
  }
} 