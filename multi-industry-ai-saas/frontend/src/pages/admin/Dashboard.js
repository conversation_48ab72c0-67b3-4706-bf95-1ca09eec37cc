import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Typography,
  Table,
  Button,
  Space,
  Tooltip,
  Tag,
  Progress,
  Spin,
  Alert,
  Divider
} from 'antd';
import {
  DashboardOutlined,
  UserOutlined,
  CloudServerOutlined,
  BankOutlined,
  ProjectOutlined,
  ReloadOutlined,
  EyeOutlined,
  SettingOutlined,
  ApartmentOutlined,
  CloudOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import apiService from '../../services/api';
import { Line, Pie } from '@ant-design/plots';

const { Title, Paragraph } = Typography;

const AdminDashboard = () => {
  const navigate = useNavigate();

  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [dashboardData, setDashboardData] = useState({
    serviceProviders: { total: 0, active: 0 },
    tenants: { total: 0, active: 0 },
    users: { total: 0, active: 0 },
    projects: { total: 0, active: 0 },
    industries: { total: 0, active: 0 },
    storage: { total: 0, used: 0 },
    recentTenants: [],
    recentUsers: [],
    tenantsByIndustry: [],
    tenantActivity: []
  });

  // 加载仪表盘数据
  useEffect(() => {
    fetchDashboardData();
  }, []);

  // 获取仪表盘数据
  const fetchDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);

      // 这里应该调用API获取实际数据
      // 为了演示，使用模拟数据
      const mockData = {
        serviceProviders: { total: 15, active: 12 },
        tenants: { total: 48, active: 42 },
        users: { total: 256, active: 198 },
        projects: { total: 124, active: 98 },
        industries: { total: 6, active: 6 },
        storage: { total: 1024 * 1024 * 1024 * 100, used: 1024 * 1024 * 1024 * 35 }, // 100GB总容量，已使用35GB
        recentTenants: [
          { id: '1', name: '北京零售有限公司', industry_type: 'retail', service_provider_name: '零售云服务', status: 'active', created_at: '2023-06-15T08:30:00Z' },
          { id: '2', name: '上海餐饮集团', industry_type: 'restaurant', service_provider_name: '餐饮云服务', status: 'active', created_at: '2023-06-14T10:15:00Z' },
          { id: '3', name: '广州酒店管理有限公司', industry_type: 'hotel', service_provider_name: '酒店云服务', status: 'active', created_at: '2023-06-13T14:45:00Z' },
          { id: '4', name: '深圳美容美发连锁', industry_type: 'beauty', service_provider_name: '美业云服务', status: 'inactive', created_at: '2023-06-12T09:20:00Z' },
          { id: '5', name: '杭州教育培训中心', industry_type: 'education', service_provider_name: '教育云服务', status: 'active', created_at: '2023-06-11T16:30:00Z' },
        ],
        recentUsers: [
          { id: '1', username: 'admin1', email: '<EMAIL>', role: 'system_admin', status: 'active', created_at: '2023-06-15T08:30:00Z' },
          { id: '2', username: 'provider1', email: '<EMAIL>', role: 'service_provider_admin', status: 'active', created_at: '2023-06-14T10:15:00Z' },
          { id: '3', username: 'tenant1', email: '<EMAIL>', role: 'tenant_admin', status: 'active', created_at: '2023-06-13T14:45:00Z' },
          { id: '4', username: 'user1', email: '<EMAIL>', role: 'user', status: 'active', created_at: '2023-06-12T09:20:00Z' },
          { id: '5', username: 'user2', email: '<EMAIL>', role: 'user', status: 'inactive', created_at: '2023-06-11T16:30:00Z' },
        ],
        tenantsByIndustry: [
          { industry: 'retail', count: 18 },
          { industry: 'restaurant', count: 12 },
          { industry: 'hotel', count: 8 },
          { industry: 'beauty', count: 5 },
          { industry: 'education', count: 3 },
          { industry: 'healthcare', count: 2 },
        ],
        tenantActivity: [
          { date: '2023-06-01', count: 35 },
          { date: '2023-06-02', count: 38 },
          { date: '2023-06-03', count: 42 },
          { date: '2023-06-04', count: 40 },
          { date: '2023-06-05', count: 43 },
          { date: '2023-06-06', count: 45 },
          { date: '2023-06-07', count: 48 },
          { date: '2023-06-08', count: 46 },
          { date: '2023-06-09', count: 49 },
          { date: '2023-06-10', count: 52 },
          { date: '2023-06-11', count: 55 },
          { date: '2023-06-12', count: 58 },
          { date: '2023-06-13', count: 56 },
          { date: '2023-06-14', count: 60 },
          { date: '2023-06-15', count: 65 },
        ],
      };

      setDashboardData(mockData);
    } catch (error) {
      console.error('获取仪表盘数据失败:', error);
      setError('获取仪表盘数据失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 获取行业名称
  const getIndustryName = (code) => {
    const industryNames = {
      'retail': '零售业',
      'restaurant': '餐饮业',
      'hotel': '酒店业',
      'beauty': '美容美发业',
      'education': '教育培训业',
      'healthcare': '医疗健康业',
    };

    return industryNames[code] || code;
  };

  // 获取状态标签
  const getStatusTag = (status) => {
    switch (status) {
      case 'active':
        return <Tag color="success">活跃</Tag>;
      case 'inactive':
        return <Tag color="error">停用</Tag>;
      case 'pending':
        return <Tag color="warning">待审核</Tag>;
      default:
        return <Tag>{status}</Tag>;
    }
  };

  // 获取角色名称
  const getRoleName = (role) => {
    const roleNames = {
      'system_admin': '系统管理员',
      'service_provider_admin': '服务商管理员',
      'tenant_admin': '租户管理员',
      'user': '普通用户',
    };

    return roleNames[role] || role;
  };

  // 租户表格列
  const tenantColumns = [
    {
      title: '租户名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '行业',
      dataIndex: 'industry_type',
      key: 'industry_type',
      render: (text) => getIndustryName(text),
    },
    {
      title: '服务商',
      dataIndex: 'service_provider_name',
      key: 'service_provider_name',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (text) => getStatusTag(text),
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (text) => new Date(text).toLocaleString(),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => navigate(`/admin/tenants/${record.id}`)}
            />
          </Tooltip>
          <Tooltip title="设置">
            <Button
              type="text"
              icon={<SettingOutlined />}
              onClick={() => navigate(`/admin/tenants/${record.id}/settings`)}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  // 用户表格列
  const userColumns = [
    {
      title: '用户名',
      dataIndex: 'username',
      key: 'username',
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      key: 'email',
    },
    {
      title: '角色',
      dataIndex: 'role',
      key: 'role',
      render: (text) => getRoleName(text),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (text) => getStatusTag(text),
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (text) => new Date(text).toLocaleString(),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => navigate(`/admin/users/${record.id}`)}
            />
          </Tooltip>
          <Tooltip title="设置">
            <Button
              type="text"
              icon={<SettingOutlined />}
              onClick={() => navigate(`/admin/users/${record.id}/settings`)}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  // 行业分布图配置
  const industryPieConfig = {
    data: dashboardData.tenantsByIndustry.map(item => ({
      type: getIndustryName(item.industry),
      value: item.count,
    })),
    angleField: 'value',
    colorField: 'type',
    radius: 0.8,
    label: {
      type: 'outer',
      content: '{name}: {percentage}',
    },
    interactions: [{ type: 'element-active' }],
  };

  // 租户活跃度图配置
  const activityLineConfig = {
    data: dashboardData.tenantActivity,
    xField: 'date',
    yField: 'count',
    point: {
      size: 5,
      shape: 'diamond',
    },
    label: {
      style: {
        fill: '#aaa',
      },
    },
  };

  // 错误提示
  const errorAlert = error ? (
    <Alert
      message="错误"
      description={error}
      type="error"
      showIcon
      style={{ marginBottom: 16 }}
    />
  ) : null;

  return (
    <div className="admin-dashboard">
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
        <div>
          <Title level={2}><DashboardOutlined /> 系统管理仪表盘</Title>
          <Paragraph>查看系统整体运行状况、租户和用户统计信息。</Paragraph>
        </div>
        <Button
          type="primary"
          icon={<ReloadOutlined />}
          onClick={fetchDashboardData}
          loading={loading}
        >
          刷新数据
        </Button>
      </div>

      {errorAlert}

      <Spin spinning={loading}>
        {/* 统计卡片 */}
        <Row gutter={16}>
          <Col span={4}>
            <Card>
              <Statistic
                title="服务商"
                value={dashboardData.serviceProviders.active}
                suffix={`/ ${dashboardData.serviceProviders.total}`}
                prefix={<CloudServerOutlined />}
              />
            </Card>
          </Col>
          <Col span={4}>
            <Card>
              <Statistic
                title="租户"
                value={dashboardData.tenants.active}
                suffix={`/ ${dashboardData.tenants.total}`}
                prefix={<BankOutlined />}
              />
            </Card>
          </Col>
          <Col span={4}>
            <Card>
              <Statistic
                title="用户"
                value={dashboardData.users.active}
                suffix={`/ ${dashboardData.users.total}`}
                prefix={<UserOutlined />}
              />
            </Card>
          </Col>
          <Col span={4}>
            <Card>
              <Statistic
                title="项目"
                value={dashboardData.projects.active}
                suffix={`/ ${dashboardData.projects.total}`}
                prefix={<ProjectOutlined />}
              />
            </Card>
          </Col>
          <Col span={4}>
            <Card>
              <Statistic
                title="行业"
                value={dashboardData.industries.active}
                suffix={`/ ${dashboardData.industries.total}`}
                prefix={<ApartmentOutlined />}
              />
            </Card>
          </Col>
          <Col span={4}>
            <Card>
              <Statistic
                title="存储使用"
                value={`${Math.round(dashboardData.storage.used / dashboardData.storage.total * 100)}%`}
                prefix={<CloudOutlined />}
              />
              <Progress
                percent={Math.round(dashboardData.storage.used / dashboardData.storage.total * 100)}
                size="small"
                status={dashboardData.storage.used / dashboardData.storage.total > 0.9 ? "exception" : "normal"}
              />
            </Card>
          </Col>
        </Row>

        <Divider />

        {/* 图表 */}
        <Row gutter={16} style={{ marginTop: 16 }}>
          <Col span={12}>
            <Card title="租户行业分布">
              <Pie {...industryPieConfig} />
            </Card>
          </Col>
          <Col span={12}>
            <Card title="租户活跃度趋势">
              <Line {...activityLineConfig} />
            </Card>
          </Col>
        </Row>

        <Divider />

        {/* 最近租户 */}
        <Card
          title="最近租户"
          extra={<Button type="link" onClick={() => navigate('/admin/tenants')}>查看全部</Button>}
          style={{ marginTop: 16 }}
        >
          <Table
            columns={tenantColumns}
            dataSource={dashboardData.recentTenants}
            rowKey="id"
            pagination={false}
          />
        </Card>

        {/* 最近用户 */}
        <Card
          title="最近用户"
          extra={<Button type="link" onClick={() => navigate('/admin/users')}>查看全部</Button>}
          style={{ marginTop: 16 }}
        >
          <Table
            columns={userColumns}
            dataSource={dashboardData.recentUsers}
            rowKey="id"
            pagination={false}
          />
        </Card>
      </Spin>
    </div>
  );
};

export default AdminDashboard;
