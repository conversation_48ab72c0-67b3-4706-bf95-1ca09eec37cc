#!/usr/bin/env python
# -*- coding: utf-8 -*-

import random
import logging
from datetime import datetime, time, timedelta
from typing import List, Optional, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import and_, func, desc

from ..models.models import GamePrize, GameRecord, GameParticipant

logger = logging.getLogger(__name__)

class LotteryEngine:
    """增强版抽奖引擎"""
    
    def __init__(self, db: AsyncSession):
        self.db = db
    
    async def draw_prize(
        self, 
        game_id: int, 
        participant_id: int, 
        game_start_time: datetime,
        current_time: Optional[datetime] = None,
        user_context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        执行抽奖逻辑
        
        Args:
            game_id: 游戏ID
            participant_id: 参与者ID
            game_start_time: 游戏开始时间
            current_time: 当前时间（用于测试）
            user_context: 用户上下文信息
            
        Returns:
            抽奖结果字典
        """
        if current_time is None:
            current_time = datetime.now()
            
        logger.info(f"开始执行抽奖: game_id={game_id}, participant_id={participant_id}")
        
        try:
            # 获取所有可用奖品
            available_prizes = await self._get_available_prizes(
                game_id, participant_id, game_start_time, current_time
            )
            
            if not available_prizes:
                logger.info("没有可用的奖品")
                return {
                    "success": False,
                    "message": "暂无可抽奖品",
                    "prize": None,
                    "method": "no_prize"
                }
            
            # 检查是否触发保底
            guaranteed_prize = await self._check_guaranteed_prize(
                game_id, participant_id, available_prizes
            )
            
            if guaranteed_prize:
                logger.info(f"触发保底奖品: {guaranteed_prize.name}")
                return await self._award_prize(
                    guaranteed_prize, participant_id, current_time, "guaranteed"
                )
            
            # 检查是否有内定中奖
            fixed_prize = await self._check_fixed_winner(
                game_id, participant_id, available_prizes
            )
            
            if fixed_prize:
                logger.info(f"内定中奖: {fixed_prize.name}")
                return await self._award_prize(
                    fixed_prize, participant_id, current_time, "fixed"
                )
            
            # 执行随机抽奖
            random_result = await self._random_draw(available_prizes, participant_id, current_time)
            
            return random_result
            
        except Exception as e:
            logger.error(f"抽奖过程中发生错误: {str(e)}", exc_info=True)
            return {
                "success": False,
                "message": "抽奖系统异常，请稍后重试",
                "prize": None,
                "method": "error"
            }
    
    async def _get_available_prizes(
        self, 
        game_id: int, 
        participant_id: int, 
        game_start_time: datetime,
        current_time: datetime
    ) -> List[GamePrize]:
        """获取当前可用的奖品列表"""
        
        # 基础查询：获取游戏的所有奖品
        query = select(GamePrize).where(
            and_(
                GamePrize.game_id == game_id,
                GamePrize.remaining > 0  # 剩余数量大于0
            )
        )
        
        result = await self.db.execute(query)
        all_prizes = result.scalars().all()
        
        available_prizes = []
        
        for prize in all_prizes:
            # 检查奖品是否在有效期内
            if not await self._is_prize_time_valid(prize, current_time):
                continue
                
            # 检查游戏开始天数限制
            if not await self._is_prize_day_valid(prize, game_start_time, current_time):
                continue
                
            # 检查每日/每小时限量
            if not await self._is_prize_limit_valid(prize, current_time):
                continue
                
            # 检查用户限量
            if not await self._is_user_limit_valid(prize, participant_id):
                continue
                
            # 检查特殊条件
            if not await self._check_special_conditions(prize, participant_id):
                continue
                
            available_prizes.append(prize)
        
        return available_prizes
    
    async def _is_prize_time_valid(self, prize: GamePrize, current_time: datetime) -> bool:
        """检查奖品时间有效性"""
        
        # 检查生效日期和失效日期
        if prize.effective_date and current_time < prize.effective_date:
            return False
            
        if prize.expiry_date and current_time > prize.expiry_date:
            return False
        
        # 检查每日生效时间段
        if prize.effective_time_start and prize.effective_time_end:
            current_time_str = current_time.strftime("%H:%M:%S")
            if not (prize.effective_time_start <= current_time_str <= prize.effective_time_end):
                return False
        
        return True
    
    async def _is_prize_day_valid(
        self, 
        prize: GamePrize, 
        game_start_time: datetime, 
        current_time: datetime
    ) -> bool:
        """检查游戏天数限制"""
        
        days_since_start = (current_time - game_start_time).days + 1
        
        # 检查最小天数限制
        if prize.min_days_since_game_start > 0:
            if days_since_start < prize.min_days_since_game_start:
                return False
        
        # 检查最大天数限制
        if prize.max_days_since_game_start > 0:
            if days_since_start > prize.max_days_since_game_start:
                return False
        
        return True
    
    async def _is_prize_limit_valid(self, prize: GamePrize, current_time: datetime) -> bool:
        """检查限量限制"""
        
        # 检查每日限量
        if prize.daily_limit > 0:
            today_start = current_time.replace(hour=0, minute=0, second=0, microsecond=0)
            today_end = today_start + timedelta(days=1)
            
            daily_count_query = select(func.count(GameRecord.id)).where(
                and_(
                    GameRecord.prize_id == prize.id,
                    GameRecord.is_win == True,
                    GameRecord.draw_time >= today_start,
                    GameRecord.draw_time < today_end
                )
            )
            
            result = await self.db.execute(daily_count_query)
            daily_count = result.scalar() or 0
            
            if daily_count >= prize.daily_limit:
                return False
        
        # 检查每小时限量
        if prize.hourly_limit > 0:
            hour_start = current_time.replace(minute=0, second=0, microsecond=0)
            hour_end = hour_start + timedelta(hours=1)
            
            hourly_count_query = select(func.count(GameRecord.id)).where(
                and_(
                    GameRecord.prize_id == prize.id,
                    GameRecord.is_win == True,
                    GameRecord.draw_time >= hour_start,
                    GameRecord.draw_time < hour_end
                )
            )
            
            result = await self.db.execute(hourly_count_query)
            hourly_count = result.scalar() or 0
            
            if hourly_count >= prize.hourly_limit:
                return False
        
        return True
    
    async def _is_user_limit_valid(self, prize: GamePrize, participant_id: int) -> bool:
        """检查用户限量"""
        
        if prize.user_limit <= 0:
            return True
        
        user_count_query = select(func.count(GameRecord.id)).where(
            and_(
                GameRecord.prize_id == prize.id,
                GameRecord.participant_id == participant_id,
                GameRecord.is_win == True
            )
        )
        
        result = await self.db.execute(user_count_query)
        user_count = result.scalar() or 0
        
        return user_count < prize.user_limit
    
    async def _check_special_conditions(self, prize: GamePrize, participant_id: int) -> bool:
        """检查特殊条件"""
        
        if not prize.special_conditions:
            return True
        
        # 这里可以根据具体需求实现特殊条件检查
        # 比如：用户等级、消费金额、注册时间等
        
        return True
    
    async def _check_guaranteed_prize(
        self, 
        game_id: int, 
        participant_id: int, 
        available_prizes: List[GamePrize]
    ) -> Optional[GamePrize]:
        """检查是否触发保底奖品"""
        
        guaranteed_prizes = [p for p in available_prizes if p.is_guaranteed]
        
        if not guaranteed_prizes:
            return None
        
        # 获取用户的抽奖记录
        records_query = select(GameRecord).where(
            and_(
                GameRecord.game_id == game_id,
                GameRecord.participant_id == participant_id
            )
        ).order_by(desc(GameRecord.draw_time))
        
        result = await self.db.execute(records_query)
        user_records = result.scalars().all()
        
        # 检查每个保底奖品的触发条件
        for prize in guaranteed_prizes:
            if await self._check_guaranteed_condition(prize, user_records):
                return prize
        
        return None
    
    async def _check_guaranteed_condition(
        self, 
        prize: GamePrize, 
        user_records: List[GameRecord]
    ) -> bool:
        """检查保底条件"""
        
        if not prize.guaranteed_condition:
            return False
        
        condition = prize.guaranteed_condition
        
        # 检查连续未中奖次数
        if "consecutive_losses" in condition:
            required_losses = condition["consecutive_losses"]
            consecutive_losses = 0
            
            for record in user_records:
                if record.is_win:
                    break
                consecutive_losses += 1
            
            if consecutive_losses >= required_losses:
                return True
        
        # 检查总抽奖次数
        if "total_draws" in condition:
            required_draws = condition["total_draws"]
            if len(user_records) >= required_draws:
                return True
        
        return False
    
    async def _check_fixed_winner(
        self, 
        game_id: int, 
        participant_id: int, 
        available_prizes: List[GamePrize]
    ) -> Optional[GamePrize]:
        """检查是否为内定中奖者"""
        
        # 查询参与者信息
        participant_query = select(GameParticipant).where(
            GameParticipant.id == participant_id
        )
        
        result = await self.db.execute(participant_query)
        participant = result.scalar_one_or_none()
        
        if not participant or not participant.is_fixed_winner:
            return None
        
        # 返回第一个非默认奖品
        for prize in available_prizes:
            if not prize.is_default:
                return prize
        
        return None
    
    async def _random_draw(
        self, 
        available_prizes: List[GamePrize], 
        participant_id: int, 
        current_time: datetime
    ) -> Dict[str, Any]:
        """执行随机抽奖"""
        
        # 计算加权概率
        weighted_prizes = []
        total_weight = 0
        
        for prize in available_prizes:
            weight = prize.probability * prize.weight_factor
            if weight > 0:
                weighted_prizes.append((prize, weight))
                total_weight += weight
        
        if total_weight <= 0:
            logger.info("没有有效的中奖概率")
            return {
                "success": True,
                "message": "很遗憾，未中奖",
                "prize": None,
                "method": "random"
            }
        
        # 生成随机数
        random_num = random.random() * total_weight
        current_weight = 0
        
        for prize, weight in weighted_prizes:
            current_weight += weight
            if random_num <= current_weight:
                return await self._award_prize(prize, participant_id, current_time, "random")
        
        # 如果没有中奖，返回谢谢参与
        return {
            "success": True,
            "message": "很遗憾，未中奖",
            "prize": None,
            "method": "random"
        }
    
    async def _award_prize(
        self, 
        prize: GamePrize, 
        participant_id: int, 
        current_time: datetime, 
        method: str
    ) -> Dict[str, Any]:
        """颁发奖品"""
        
        try:
            # 减少奖品剩余数量
            prize.remaining -= 1
            await self.db.commit()
            
            logger.info(f"成功颁发奖品: {prize.name}, 剩余数量: {prize.remaining}")
            
            return {
                "success": True,
                "message": f"恭喜您获得：{prize.name}",
                "prize": {
                    "id": prize.id,
                    "name": prize.name,
                    "description": prize.description,
                    "prize_type": prize.prize_type,
                    "value": prize.value,
                    "image_url": prize.image_url,
                    "animation_style": prize.animation_style,
                    "sound_effect_url": prize.sound_effect_url
                },
                "method": method
            }
            
        except Exception as e:
            logger.error(f"颁发奖品时发生错误: {str(e)}", exc_info=True)
            await self.db.rollback()
            
            return {
                "success": False,
                "message": "颁奖过程中发生错误，请联系客服",
                "prize": None,
                "method": method
            } 