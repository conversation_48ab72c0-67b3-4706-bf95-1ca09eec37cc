#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
插件信息路由

提供插件信息
"""

import logging
from typing import Dict, Any

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from db.database import get_db
from models.user import User
from core.auth import get_current_user

from .. import router

logger = logging.getLogger(__name__)

# 获取插件信息
@router.get("/plugin-info", response_model=Dict[str, Any])
async def get_plugin_info(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取插件信息"""
    try:
        # 构建插件信息
        plugin_info = {
            "id": "sapi",
            "code": "sapi",
            "name": "智能助手集成平台",
            "description": "智能助手集成平台(SAPI)是一个强大的API集成工具，支持Model Context Protocol(MCP)协议，允许您将系统功能与各类应用程序和服务无缝连接。",
            "version": "1.0.0",
            "type": "marketplace",
            "category": "integration",
            "icon_url": None,
            "is_installed": True,
            "price": 0,
            "author": "系统",
            "homepage": None,
            "features": [
                "API端点管理",
                "MCP服务器管理",
                "API到MCP映射",
                "令牌管理",
                "使用统计"
            ]
        }
        
        return {
            "success": True,
            "message": "获取插件信息成功",
            "data": plugin_info
        }
    except Exception as e:
        logger.error(f"获取插件信息失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取插件信息失败: {str(e)}")
