#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
全渠道运营助手 - 商品映射模型
"""

import uuid
from sqlalchemy import Column, String, Boolean, ForeignKey, Text, DateTime, JSON, Integer, Float, Index
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship
from datetime import datetime

from db.database import Base


class MultiChannelProductMapping(Base):
    """多渠道商品映射模型 - 支持多规格商品"""
    __tablename__ = "multi_channel_product_mappings"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id", ondelete="CASCADE"), nullable=False)
    product_id = Column(UUID(as_uuid=True), nullable=False, comment="商品ID")
    product_spec_id = Column(UUID(as_uuid=True), nullable=True, comment="商品规格ID，为空表示主商品")
    channel_id = Column(UUID(as_uuid=True), ForeignKey("sales_channels.id", ondelete="CASCADE"), nullable=False)
    
    # 外部平台商品ID
    external_product_id = Column(String(255), nullable=True, comment="外部平台商品ID")
    external_spec_id = Column(String(255), nullable=True, comment="外部平台规格ID")
    
    # 渠道特定的商品数据
    channel_product_data = Column(JSONB, default=dict, comment="渠道特定商品数据")
    
    # 价格策略
    price_strategy = Column(String(50), default="manual", comment="定价策略: manual, auto, competitive")
    base_price = Column(Float, nullable=True, comment="基础价格")
    current_price = Column(Float, nullable=True, comment="当前价格")
    min_price = Column(Float, nullable=True, comment="最低价格")
    max_price = Column(Float, nullable=True, comment="最高价格")
    
    # 库存管理
    stock_sync_enabled = Column(Boolean, default=True, comment="是否启用库存同步")
    current_stock = Column(Integer, nullable=True, comment="当前库存")
    
    # 同步状态
    sync_status = Column(String(50), default="pending", comment="同步状态: pending, syncing, success, failed")
    last_sync_at = Column(DateTime, nullable=True, comment="最后同步时间")
    sync_error = Column(Text, nullable=True, comment="同步错误信息")
    
    # 上架状态
    is_active = Column(Boolean, default=True, comment="是否启用")
    is_online = Column(Boolean, default=False, comment="是否已上架")
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 索引
    __table_args__ = (
        Index('idx_product_channel', 'project_id', 'product_id', 'channel_id'),
        Index('idx_external_product', 'channel_id', 'external_product_id'),
        Index('idx_sync_status', 'sync_status'),
    )


class MultiChannelBatchOperation(Base):
    """多渠道批量操作记录"""
    __tablename__ = "multi_channel_batch_operations"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id", ondelete="CASCADE"), nullable=False)
    
    # 操作信息
    operation_type = Column(String(50), nullable=False, comment="操作类型: sync, price_update, stock_update, status_update")
    operation_name = Column(String(200), nullable=False, comment="操作名称")
    
    # 目标信息
    target_channels = Column(JSONB, default=list, comment="目标渠道ID列表")
    target_products = Column(JSONB, default=list, comment="目标商品ID列表")
    
    # 操作参数
    operation_params = Column(JSONB, default=dict, comment="操作参数")
    
    # 执行状态
    status = Column(String(50), default="pending", comment="执行状态: pending, running, completed, failed, cancelled")
    progress = Column(Integer, default=0, comment="执行进度百分比")
    total_items = Column(Integer, default=0, comment="总项目数")
    completed_items = Column(Integer, default=0, comment="已完成项目数")
    failed_items = Column(Integer, default=0, comment="失败项目数")
    
    # 结果信息
    result_summary = Column(JSONB, default=dict, comment="执行结果摘要")
    error_details = Column(JSONB, default=list, comment="错误详情列表")
    
    # 时间信息
    started_at = Column(DateTime, nullable=True, comment="开始时间")
    completed_at = Column(DateTime, nullable=True, comment="完成时间")
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)


class ProductImageGeneration(Base):
    """AI商品图片生成记录"""
    __tablename__ = "multi_channel_product_image_generations"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id", ondelete="CASCADE"), nullable=False)
    product_id = Column(UUID(as_uuid=True), nullable=False, comment="商品ID")
    channel_id = Column(UUID(as_uuid=True), ForeignKey("sales_channels.id", ondelete="CASCADE"), nullable=True)
    
    # 生成参数
    prompt = Column(Text, nullable=False, comment="生成提示词")
    style = Column(String(100), nullable=True, comment="图片风格")
    size = Column(String(50), default="1024x1024", comment="图片尺寸")
    
    # 生成结果
    status = Column(String(50), default="pending", comment="生成状态: pending, generating, completed, failed")
    generated_images = Column(JSONB, default=list, comment="生成的图片URL列表")
    selected_image_url = Column(String(500), nullable=True, comment="选中的图片URL")
    
    # AI模型信息
    model_name = Column(String(100), nullable=True, comment="使用的AI模型")
    generation_params = Column(JSONB, default=dict, comment="生成参数")
    
    # 错误信息
    error_message = Column(Text, nullable=True, comment="错误信息")
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
