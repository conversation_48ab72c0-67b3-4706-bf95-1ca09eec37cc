#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
添加阿里云百炼 qwen-vl-ocr-latest 模型
"""

import os
import sys
import asyncio
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from sqlalchemy import select, text
from db.database import get_db
from models.ai.provider import AIProvider
from models.ai.model import AIModel

async def add_ocr_model():
    """添加OCR模型"""
    async for db in get_db():
        try:
            # 查找阿里巴巴百炼提供商
            provider_stmt = select(AIProvider).where(AIProvider.name == 'alibaba_bailian')
            result = await db.execute(provider_stmt)
            provider = result.scalar_one_or_none()
            
            if not provider:
                print("错误: 未找到阿里巴巴百炼提供商，请先初始化AI提供商数据")
                return False
            
            # 检查模型是否已存在
            model_stmt = select(AIModel).where(
                AIModel.provider_id == provider.id,
                AIModel.name == 'qwen-vl-ocr-latest'
            )
            result = await db.execute(model_stmt)
            existing_model = result.scalar_one_or_none()
            
            if existing_model:
                print("模型 qwen-vl-ocr-latest 已存在，跳过添加")
                # 更新现有模型的token_limit（如果需要）
                if existing_model.token_limit != 8192: # 用户指定的输出限制
                    print(f"更新现有模型 qwen-vl-ocr-latest 的 token_limit 从 {existing_model.token_limit} 到 8192")
                    existing_model.token_limit = 8192
                    existing_model.context_window = 38192 # 用户指定的上下文
                    # 注意：用户指定的输入限制是30000，AIModel中没有直接的输入限制字段，token_limit通常指输出或总限制
                    # 此处假设原有的token_limit（可能是总限制）需要更新为输出限制，并保留原有的context_window
                    # 如果AIModel.token_limit的定义是总限制，那可能需要调整，但目前信息不足以确定
                    await db.commit()
                    print("模型 token_limit 更新成功")
                return True
            
            # 创建新模型
            new_model = AIModel(
                provider_id=provider.id,
                name='qwen-vl-ocr-latest',
                display_name='通义千问VL OCR最新版',
                description='阿里云百炼专业OCR视觉模型，专门优化用于文档和表格识别，支持高精度文字提取和表格数据解析',
                model_type='vision',
                capabilities={
                    'supports_function_calling': True,
                    'supports_vision': True,
                    'supports_audio_input': False,
                    'supports_audio_output': False,
                    'supports_speech_to_text': False,
                    'supports_text_to_speech': False,
                    'supports_system_message': True,
                    'supports_streaming': True,
                    'supports_reasoning': False,
                    'supports_code_generation': False,
                    'supports_multilingual': True,
                    'supports_document_analysis': True,
                    'supports_chart_analysis': True,
                    'supports_ocr': True,
                    'supports_image_understanding': True,
                    'supports_table_extraction': True,
                    'supports_form_recognition': True
                },
                context_window=38192,  # 用户指定的上下文窗口
                token_limit=8192,    # 用户指定的输出 token 限制 (AIModel.token_limit)
                # AIModel没有直接的输入token限制字段，用户指定的是30000。此信息可能用于其他地方或需调整模型定义。
                input_price_per_1k_tokens=0.0008,
                output_price_per_1k_tokens=0.002,
                image_price_per_1k_tokens=0.0008,
                average_response_time=2.5,
                rate_limit_per_minute=60,
                status='active',
                is_builtin=True,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            
            db.add(new_model)
            await db.commit()
            
            print(f"成功添加OCR模型: qwen-vl-ocr-latest")
            print(f"模型ID: {new_model.id}")
            print(f"上下文窗口: {new_model.context_window}")
            print(f"Token限制 (输出/总计): {new_model.token_limit}")
            
            return True
            
        except Exception as e:
            print(f"添加OCR模型失败: {e}")
            await db.rollback()
            return False
        finally:
            await db.close()

if __name__ == "__main__":
    success = asyncio.run(add_ocr_model())
    if success:
        print("✅ OCR模型添加/更新完成！")
    else:
        print("❌ OCR模型添加/更新失败！")
        sys.exit(1) 