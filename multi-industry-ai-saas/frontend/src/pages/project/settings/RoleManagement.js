import React, { useState, useEffect } from 'react';
import {
  Card, Table, Button, Space, Tag, Modal, Form,
  Input, Select, Switch, message, Tooltip, Popconfirm
} from 'antd';
import {
  PlusOutlined, EditOutlined, DeleteOutlined,
  ExclamationCircleOutlined, QuestionCircleOutlined
} from '@ant-design/icons';
import apiService from '../../../services/api';
import './RoleManagement.css';

const { Option } = Select;

/**
 * 角色管理页面
 * 用于管理项目内的不同角色，包括门店、运营、采购、财务、仓管等角色
 */
const RoleManagement = () => {
  const [roles, setRoles] = useState([]);
  const [loading, setLoading] = useState(true);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingRole, setEditingRole] = useState(null);
  const [form] = Form.useForm();

  // 获取角色列表
  useEffect(() => {
    fetchRoles();
  }, []);

  // 获取角色数据
  const fetchRoles = async () => {
    try {
      setLoading(true);
      const res = await apiService.project.role.getList();
      // 适配后端返回结构
      const roleList = Array.isArray(res.data) ? res.data : (res.items || []);
      
      // 确保每个角色都有status字段，如果没有则设置默认值
      const rolesWithStatus = roleList.map(role => ({
        ...role,
        status: role.status || 'active' // 如果状态为空或undefined，默认为active
      }));
      
      setRoles(rolesWithStatus);
      console.log('角色列表数据:', rolesWithStatus); // 添加调试日志
      setLoading(false);
    } catch (error) {
      console.error('获取角色列表失败:', error);
      message.error('获取角色列表失败');
      setLoading(false);
    }
  };

  // 打开创建角色模态框
  const showCreateModal = () => {
    setEditingRole(null);
    form.resetFields();
    form.setFieldsValue({
      category: '自定义',
      status: 'active'
    });
    setModalVisible(true);
  };

  // 打开编辑角色模态框
  const showEditModal = (role) => {
    setEditingRole(role);
    
    // 处理权限数据 - 确保是数组格式
    let permissionsArray = [];
    if (role.permissions) {
      if (Array.isArray(role.permissions)) {
        permissionsArray = role.permissions;
      } else if (typeof role.permissions === 'object') {
        // 如果是对象格式 {permission: true}，转换为数组
        permissionsArray = Object.keys(role.permissions).filter(key => role.permissions[key]);
      }
    }
    
    form.setFieldsValue({
      name: role.name,
      code: role.code,
      description: role.description,
      permissions: permissionsArray, // 确保是数组格式
      status: role.status,
      category: role.category || '自定义'
    });
    setModalVisible(true);
  };

  // 关闭模态框
  const handleCancel = () => {
    setModalVisible(false);
  };

  // 提交表单
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      console.log('表单值:', values);
      console.log('编辑角色信息:', editingRole);
      
      // 获取 project_id
      const projectId = localStorage.getItem('project_id');
      // permissions 转换为 dict
      const permissionsDict = {};
      (values.permissions || []).forEach(perm => { permissionsDict[perm] = true; });
      const payload = {
        ...values,
        project_id: projectId,
        permissions: permissionsDict
      };
      
      console.log('提交载荷:', payload);
      
      if (editingRole) {
        console.log('更新角色ID:', editingRole.id);
        const response = await apiService.project.role.update(editingRole.id, payload);
        console.log('更新响应:', response);
        message.success('角色更新成功');
      } else {
        const response = await apiService.project.role.create(payload);
        console.log('创建响应:', response);
        message.success('角色创建成功');
      }
      setModalVisible(false);
      fetchRoles();
    } catch (error) {
      console.error('提交表单详细错误:', error);
      console.error('错误响应:', error.response?.data);
      console.error('错误状态:', error.response?.status);
      
      let errorMessage = '操作失败';
      if (error.response?.status === 404) {
        errorMessage = '角色不存在，可能已被删除';
      } else if (error.response?.status === 400) {
        errorMessage = error.response?.data?.detail || '请求参数错误';
      } else if (error.response?.data?.detail) {
        errorMessage = error.response.data.detail;
      } else if (error.message) {
        errorMessage = error.message;
      }
      
      message.error(errorMessage);
    }
  };

  // 删除角色
  const handleDelete = async (role) => {
    try {
      await apiService.project.role.delete(role.id);
      message.success('角色删除成功');
      fetchRoles();
    } catch (error) {
      console.error('删除角色失败:', error);
      message.error('删除角色失败');
    }
  };

  // 表格列配置
  const columns = [
    {
      title: '角色名称',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <span>
          {text}
          {record.is_system && (
            <Tooltip title="系统预置角色，不可删除">
              <Tag color="blue" style={{ marginLeft: 8 }}>预置</Tag>
            </Tooltip>
          )}
        </span>
      )
    },
    {
      title: '角色分类',
      dataIndex: 'category',
      key: 'category',
      filters: [
        { text: '系统', value: '系统' },
        { text: '门店', value: '门店' },
        { text: '财务', value: '财务' },
        { text: '采购', value: '采购' },
        { text: '运营', value: '运营' },
        { text: '人事', value: '人事' },
        { text: '物流', value: '物流' },
        { text: '仓储', value: '仓储' },
        { text: '自定义', value: '自定义' }
      ],
      onFilter: (value, record) => record.category === value || (!record.category && value === '自定义'),
      render: (category) => {
        if (!category) return <Tag color="purple">自定义</Tag>;

        let color;
        switch (category) {
          case '系统': color = 'magenta'; break;
          case '门店': color = 'green'; break;
          case '财务': color = 'blue'; break;
          case '采购': color = 'orange'; break;
          case '运营': color = 'cyan'; break;
          case '人事': color = 'geekblue'; break;
          case '物流': color = 'volcano'; break;
          case '仓储': color = 'gold'; break;
          default: color = 'default';
        }

        return <Tag color={color}>{category}</Tag>;
      }
    },
    {
      title: '角色代码',
      dataIndex: 'code',
      key: 'code'
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true
    },
    {
      title: '权限',
      dataIndex: 'permissions',
      key: 'permissions',
      render: (permissions) => {
        const perms = Object.keys(permissions || {});
        return (
          <span>
            {perms.includes('all') ? (
              <Tag color="gold">所有权限</Tag>
            ) : (
              perms.slice(0, 3).map(perm => {
                let color;
                if (perm.includes('view')) color = 'green';
                else if (perm.includes('edit')) color = 'blue';
                else if (perm.includes('approve')) color = 'red';
                else if (perm.includes('manage')) color = 'purple';
                else color = 'orange';

                return (
                  <Tag color={color} key={perm}>
                    {perm}
                  </Tag>
                );
              })
            )}
            {perms.length > 3 && <Tag>+{perms.length - 3}项</Tag>}
          </span>
        );
      }
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      filters: [
        { text: '启用', value: 'active' },
        { text: '禁用', value: 'inactive' }
      ],
      onFilter: (value, record) => record.status === value,
      render: (status) => (
        <Tag color={status === 'active' ? 'green' : 'red'}>
          {status === 'active' ? '启用' : '禁用'}
        </Tag>
      )
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      sorter: (a, b) => new Date(a.created_at) - new Date(b.created_at),
      render: (text) => new Date(text).toLocaleString()
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => showEditModal(record)}
          />
          {!record.is_system && (
            <Popconfirm
              title="确定要删除这个角色吗？"
              onConfirm={() => handleDelete(record)}
              okText="确定"
              cancelText="取消"
            >
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
              />
            </Popconfirm>
          )}
        </Space>
      )
    }
  ];

  // 可选权限列表，按模块分组
  const permissionOptions = [
    // 系统权限
    { label: '所有权限', value: 'all' },

    // 门店权限
    { label: '门店查看', value: 'store_view' },
    { label: '门店编辑', value: 'store_edit' },
    { label: '门店报表', value: 'store_report' },
    { label: '门店收银', value: 'store_cashier' },
    { label: '门店人员管理', value: 'store_staff_manage' },

    // 运营权限
    { label: '运营查看', value: 'operation_view' },
    { label: '运营编辑', value: 'operation_edit' },
    { label: '运营报表', value: 'operation_report' },
    { label: '渠道管理', value: 'operation_channel_manage' },

    // 采购权限
    { label: '采购查看', value: 'purchase_view' },
    { label: '采购编辑', value: 'purchase_edit' },
    { label: '采购报表', value: 'purchase_report' },
    { label: '采购审批', value: 'purchase_approve' },
    { label: '有限采购编辑', value: 'purchase_edit_limited' },
    { label: '采购人员管理', value: 'purchase_staff_manage' },

    // 财务权限
    { label: '财务查看', value: 'finance_view' },
    { label: '财务编辑', value: 'finance_edit' },
    { label: '财务报表', value: 'finance_report' },
    { label: '财务审批', value: 'finance_approve' },
    { label: '出纳操作', value: 'finance_cashier' },
    { label: '财务人员管理', value: 'finance_staff_manage' },

    // 库存权限
    { label: '库存查看', value: 'inventory_view' },
    { label: '库存编辑', value: 'inventory_edit' },
    { label: '库存盘点', value: 'inventory_check' },
    { label: '库存审批', value: 'inventory_approve' },
    { label: '库存人员管理', value: 'inventory_staff_manage' },

    // 人事权限
    { label: '人事查看', value: 'hr_view' },
    { label: '人事编辑', value: 'hr_edit' },
    { label: '人事审批', value: 'hr_approve' },
    { label: '人事人员管理', value: 'hr_staff_manage' },

    // 物流权限
    { label: '物流查看', value: 'logistics_view' },
    { label: '物流编辑', value: 'logistics_edit' },
    { label: '物流执行', value: 'logistics_execute' },
    { label: '物流审批', value: 'logistics_approve' },
    { label: '物流人员管理', value: 'logistics_staff_manage' }
  ];

  // 角色预设配置
  const rolePresets = {
    '系统': {
      name: '系统管理员',
      code: 'system_admin',
      permissions: ['all'],
      description: '系统管理员，拥有系统所有权限'
    },
    '门店': {
      name: '门店管理员',
      code: 'store_admin',
      permissions: ['store_view', 'store_edit', 'store_report', 'store_staff_manage'],
      description: '门店管理员，负责管理门店相关业务'
    },
    '财务': {
      name: '财务专员',
      code: 'finance_staff',
      permissions: ['finance_view', 'finance_edit', 'finance_report'],
      description: '财务专员，负责财务相关业务'
    },
    '采购': {
      name: '采购专员',
      code: 'purchase_staff',
      permissions: ['purchase_view', 'purchase_edit', 'purchase_report'],
      description: '采购专员，负责采购相关业务'
    },
    '运营': {
      name: '运营专员',
      code: 'operation_staff',
      permissions: ['operation_view', 'operation_edit', 'operation_report', 'operation_channel_manage'],
      description: '运营专员，负责运营相关业务'
    },
    '人事': {
      name: '人事专员',
      code: 'hr_staff',
      permissions: ['hr_view', 'hr_edit', 'hr_approve'],
      description: '人事专员，负责人事相关业务'
    },
    '物流': {
      name: '物流专员',
      code: 'logistics_staff',
      permissions: ['logistics_view', 'logistics_edit', 'logistics_execute'],
      description: '物流专员，负责物流相关业务'
    },
    '仓储': {
      name: '仓管员',
      code: 'inventory_staff',
      permissions: ['inventory_view', 'inventory_edit', 'inventory_check'],
      description: '仓管员，负责日常仓库和库存管理'
    }
  };

  // 处理角色分类变化
  const handleCategoryChange = (category) => {
    // 如果选择了预设角色分类，自动填充相关信息
    if (category !== '自定义' && rolePresets[category]) {
      const preset = rolePresets[category];
      form.setFieldsValue({
        name: preset.name,
        code: preset.code,
        permissions: preset.permissions,
        description: preset.description
      });
    }
  };

  console.log('RoleManagement 组件渲染', { roles, loading });

  return (
    <div className="role-management">
      <Card
        title="角色管理"
        extra={
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={showCreateModal}
          >
            创建角色
          </Button>
        }
      >
        <Table
          columns={columns}
          dataSource={roles}
          rowKey="id"
          loading={loading}
          pagination={{ pageSize: 10 }}
        />
      </Card>

      <Modal
        title={editingRole ? '编辑角色' : '创建角色'}
        open={modalVisible}
        onOk={handleSubmit}
        onCancel={handleCancel}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Form.Item
            name="name"
            label="角色名称"
            rules={[{ required: true, message: '请输入角色名称' }]}
          >
            <Input placeholder="请输入角色名称" />
          </Form.Item>

          <Form.Item
            name="category"
            label="角色分类"
            initialValue="自定义"
          >
            <Select
              placeholder="请选择角色分类"
              onChange={handleCategoryChange}
            >
              <Option value="系统">系统</Option>
              <Option value="门店">门店</Option>
              <Option value="财务">财务</Option>
              <Option value="采购">采购</Option>
              <Option value="运营">运营</Option>
              <Option value="人事">人事</Option>
              <Option value="物流">物流</Option>
              <Option value="仓储">仓储</Option>
              <Option value="自定义">自定义</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="code"
            label="角色代码"
            rules={[{ required: true, message: '请输入角色代码' }]}
          >
            <Input placeholder="请输入角色代码，如store_admin" />
          </Form.Item>

          <Form.Item
            name="description"
            label="角色描述"
          >
            <Input.TextArea placeholder="请输入角色描述" rows={3} />
          </Form.Item>

          <Form.Item
            name="permissions"
            label={
              <span>
                权限设置
                <Tooltip title="选择该角色拥有的权限">
                  <QuestionCircleOutlined style={{ marginLeft: 4 }} />
                </Tooltip>
              </span>
            }
            rules={[{ required: true, message: '请选择权限' }]}
          >
            <Select
              mode="multiple"
              placeholder="请选择权限"
              style={{ width: '100%' }}
              options={permissionOptions}
              optionFilterProp="label"
              showSearch
              listHeight={400}
            />
          </Form.Item>

          <Form.Item
            name="status"
            label="状态"
            initialValue="active"
          >
            <Select>
              <Option value="active">启用</Option>
              <Option value="inactive">禁用</Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default RoleManagement;
