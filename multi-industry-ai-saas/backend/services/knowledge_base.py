#!/usr/bin/env python
# -*- coding: utf-8 -*-

import uuid
from typing import List, Optional, Tuple, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import or_, and_, func
from datetime import datetime

from models.knowledge_base import KnowledgeDocument, KnowledgeCategory
from models.store import Store
from models.user import User
from schemas.knowledge_base import KnowledgeDocumentCreate, KnowledgeDocumentUpdate

class KnowledgeBaseService:
    """
    知识库服务
    """

    @staticmethod
    async def get_documents(
        db: AsyncSession,
        project_id: uuid.UUID,
        skip: int = 0,
        limit: int = 10,
        category_id: Optional[uuid.UUID] = None,
        store_id: Optional[uuid.UUID] = None,
        search: Optional[str] = None
    ) -> Tuple[List[Dict[str, Any]], int]:
        """
        获取知识库文档列表
        """
        # 构建查询条件
        conditions = [KnowledgeDocument.project_id == project_id]

        if category_id:
            conditions.append(KnowledgeDocument.category_id == category_id)

        if store_id:
            conditions.append(or_(
                KnowledgeDocument.is_public == True,
                KnowledgeDocument.store_ids.contains([store_id])
            ))

        if search:
            conditions.append(or_(
                KnowledgeDocument.title.ilike(f"%{search}%"),
                KnowledgeDocument.description.ilike(f"%{search}%"),
                KnowledgeDocument.content.ilike(f"%{search}%")
            ))

        # 查询文档总数
        query = select(func.count()).select_from(KnowledgeDocument).where(and_(*conditions))
        result = await db.execute(query)
        total = result.scalar() or 0

        # 查询文档列表
        query = (
            select(
                KnowledgeDocument,
                KnowledgeCategory.name.label("category_name"),
                User.full_name.label("creator_name")
            )
            .outerjoin(KnowledgeCategory, KnowledgeDocument.category_id == KnowledgeCategory.id)
            .outerjoin(User, KnowledgeDocument.created_by == User.id)
            .where(and_(*conditions))
            .order_by(KnowledgeDocument.created_at.desc())
            .offset(skip)
            .limit(limit)
        )

        result = await db.execute(query)
        rows = result.all()

        # 处理结果
        documents = []
        for row in rows:
            document = row.KnowledgeDocument
            document_dict = {
                "id": document.id,
                "project_id": document.project_id,
                "category_id": document.category_id,
                "category_name": row.category_name,
                "title": document.title,
                "description": document.description,
                "content": document.content,
                "file_url": document.file_url,
                "file_name": document.file_name,
                "file_type": document.file_type,
                "is_public": document.is_public,
                "store_ids": document.store_ids,
                "metadata": document.document_metadata,  # 使用 document_metadata 字段，但返回时仍使用 metadata 键名
                "created_by": document.created_by,
                "updated_by": document.updated_by,
                "creator_name": row.creator_name,
                "created_at": document.created_at,
                "updated_at": document.updated_at
            }

            # 获取门店名称
            if document.store_ids:
                query = select(Store.name).where(Store.id.in_(document.store_ids))
                result = await db.execute(query)
                store_names = [row[0] for row in result.all()]
                document_dict["store_names"] = store_names
            else:
                document_dict["store_names"] = []

            documents.append(document_dict)

        return documents, total

    @staticmethod
    async def get_categories(
        db: AsyncSession,
        project_id: uuid.UUID
    ) -> List[Dict[str, Any]]:
        """
        获取知识库分类列表
        """
        query = (
            select(KnowledgeCategory)
            .where(KnowledgeCategory.project_id == project_id)
            .order_by(KnowledgeCategory.name)
        )

        result = await db.execute(query)
        categories = result.scalars().all()

        return [
            {
                "id": category.id,
                "project_id": category.project_id,
                "parent_id": category.parent_id,
                "name": category.name,
                "description": category.description,
                "created_at": category.created_at,
                "updated_at": category.updated_at
            }
            for category in categories
        ]

    @staticmethod
    async def create_category(
        db: AsyncSession,
        project_id: uuid.UUID,
        category_data: Dict[str, Any],
        created_by: uuid.UUID
    ) -> Dict[str, Any]:
        """
        创建知识库分类
        """
        # 创建分类
        category = KnowledgeCategory(
            project_id=project_id,
            parent_id=category_data.get("parent_id"),
            name=category_data["name"],
            description=category_data.get("description", ""),
            created_by=created_by,
            updated_by=created_by
        )

        db.add(category)
        await db.commit()
        await db.refresh(category)

        return {
            "id": category.id,
            "project_id": category.project_id,
            "parent_id": category.parent_id,
            "name": category.name,
            "description": category.description,
            "created_at": category.created_at,
            "updated_at": category.updated_at
        }

    @staticmethod
    async def get_document_by_id(
        db: AsyncSession,
        project_id: uuid.UUID,
        document_id: uuid.UUID
    ) -> Optional[Dict[str, Any]]:
        """
        根据ID获取知识库文档
        """
        query = (
            select(
                KnowledgeDocument,
                KnowledgeCategory.name.label("category_name"),
                User.full_name.label("creator_name")
            )
            .outerjoin(KnowledgeCategory, KnowledgeDocument.category_id == KnowledgeCategory.id)
            .outerjoin(User, KnowledgeDocument.created_by == User.id)
            .where(
                and_(
                    KnowledgeDocument.id == document_id,
                    KnowledgeDocument.project_id == project_id
                )
            )
        )

        result = await db.execute(query)
        row = result.first()

        if not row:
            return None

        document = row.KnowledgeDocument
        document_dict = {
            "id": document.id,
            "project_id": document.project_id,
            "category_id": document.category_id,
            "category_name": row.category_name,
            "title": document.title,
            "description": document.description,
            "content": document.content,
            "file_url": document.file_url,
            "file_name": document.file_name,
            "file_type": document.file_type,
            "is_public": document.is_public,
            "store_ids": document.store_ids,
            "metadata": document.document_metadata,
            "created_by": document.created_by,
            "updated_by": document.updated_by,
            "creator_name": row.creator_name,
            "created_at": document.created_at,
            "updated_at": document.updated_at
        }

        # 获取门店名称
        if document.store_ids:
            query = select(Store.name).where(Store.id.in_(document.store_ids))
            result = await db.execute(query)
            store_names = [row[0] for row in result.all()]
            document_dict["store_names"] = store_names
        else:
            document_dict["store_names"] = []

        return document_dict

    @staticmethod
    async def create_document(
        db: AsyncSession,
        project_id: uuid.UUID,
        document_data: KnowledgeDocumentCreate,
        created_by: uuid.UUID
    ) -> Dict[str, Any]:
        """
        创建知识库文档
        """
        # 创建文档
        document = KnowledgeDocument(
            project_id=project_id,
            category_id=document_data.category_id,
            title=document_data.title,
            description=document_data.description,
            content=document_data.content,
            file_url=document_data.file_url,
            file_name=document_data.file_name,
            file_type=document_data.file_type,
            is_public=document_data.is_public,
            store_ids=document_data.store_ids,
            document_metadata=document_data.metadata,
            created_by=created_by,
            updated_by=created_by
        )

        db.add(document)
        await db.commit()
        await db.refresh(document)

        # 获取分类名称
        category_name = None
        if document.category_id:
            query = select(KnowledgeCategory.name).where(KnowledgeCategory.id == document.category_id)
            result = await db.execute(query)
            category_name = result.scalar()

        # 获取创建者名称
        creator_name = None
        if document.created_by:
            query = select(User.full_name).where(User.id == document.created_by)
            result = await db.execute(query)
            creator_name = result.scalar()

        # 获取门店名称
        store_names = []
        if document.store_ids:
            query = select(Store.name).where(Store.id.in_(document.store_ids))
            result = await db.execute(query)
            store_names = [row[0] for row in result.all()]

        return {
            "id": document.id,
            "project_id": document.project_id,
            "category_id": document.category_id,
            "category_name": category_name,
            "title": document.title,
            "description": document.description,
            "content": document.content,
            "file_url": document.file_url,
            "file_name": document.file_name,
            "file_type": document.file_type,
            "is_public": document.is_public,
            "store_ids": document.store_ids,
            "store_names": store_names,
            "metadata": document.metadata,
            "created_by": document.created_by,
            "updated_by": document.updated_by,
            "creator_name": creator_name,
            "created_at": document.created_at,
            "updated_at": document.updated_at
        }

    @staticmethod
    async def update_document(
        db: AsyncSession,
        project_id: uuid.UUID,
        document_id: uuid.UUID,
        document_data: KnowledgeDocumentUpdate,
        updated_by: uuid.UUID
    ) -> Dict[str, Any]:
        """
        更新知识库文档
        """
        # 获取文档
        query = select(KnowledgeDocument).where(
            and_(
                KnowledgeDocument.id == document_id,
                KnowledgeDocument.project_id == project_id
            )
        )
        result = await db.execute(query)
        document = result.scalar_one_or_none()

        if not document:
            raise ValueError("文档不存在")

        # 更新文档
        update_data = document_data.dict(exclude_unset=True)
        for key, value in update_data.items():
            setattr(document, key, value)

        document.updated_by = updated_by
        document.updated_at = datetime.now()

        await db.commit()
        await db.refresh(document)

        # 获取分类名称
        category_name = None
        if document.category_id:
            query = select(KnowledgeCategory.name).where(KnowledgeCategory.id == document.category_id)
            result = await db.execute(query)
            category_name = result.scalar()

        # 获取创建者名称
        creator_name = None
        if document.created_by:
            query = select(User.full_name).where(User.id == document.created_by)
            result = await db.execute(query)
            creator_name = result.scalar()

        # 获取更新者名称
        updater_name = None
        if document.updated_by:
            query = select(User.full_name).where(User.id == document.updated_by)
            result = await db.execute(query)
            updater_name = result.scalar()

        # 获取门店名称
        store_names = []
        if document.store_ids:
            query = select(Store.name).where(Store.id.in_(document.store_ids))
            result = await db.execute(query)
            store_names = [row[0] for row in result.all()]

        return {
            "id": document.id,
            "project_id": document.project_id,
            "category_id": document.category_id,
            "category_name": category_name,
            "title": document.title,
            "description": document.description,
            "content": document.content,
            "file_url": document.file_url,
            "file_name": document.file_name,
            "file_type": document.file_type,
            "is_public": document.is_public,
            "store_ids": document.store_ids,
            "store_names": store_names,
            "metadata": document.metadata,
            "created_by": document.created_by,
            "updated_by": document.updated_by,
            "creator_name": creator_name,
            "updater_name": updater_name,
            "created_at": document.created_at,
            "updated_at": document.updated_at
        }

    @staticmethod
    async def delete_document(
        db: AsyncSession,
        project_id: uuid.UUID,
        document_id: uuid.UUID
    ) -> None:
        """
        删除知识库文档
        """
        # 获取文档
        query = select(KnowledgeDocument).where(
            and_(
                KnowledgeDocument.id == document_id,
                KnowledgeDocument.project_id == project_id
            )
        )
        result = await db.execute(query)
        document = result.scalar_one_or_none()

        if not document:
            raise ValueError("文档不存在")

        # 删除文档
        await db.delete(document)
        await db.commit()
