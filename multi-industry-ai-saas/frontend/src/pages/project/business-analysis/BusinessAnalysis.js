import React, { useState, useEffect } from 'react';
import {
  Card, Tabs, Row, Col, Statistic, Button, Space, Alert, 
  Spin, message, Typography, Divider, Tag, Progress,
  Select, DatePicker, Table, Empty
} from 'antd';
import {
  RiseOutlined, ShoppingOutlined, UserOutlined, 
  PartitionOutlined, RobotOutlined, SyncOutlined,
  <PERSON><PERSON><PERSON>Outlined, <PERSON><PERSON>hartOutlined, Pie<PERSON>hartOutlined,
  ThunderboltOutlined, FireOutlined, EyeOutlined,
  DollarOutlined, TeamOutlined, ShopOutlined, DatabaseOutlined,
  ShoppingCartOutlined, ApartmentOutlined
} from '@ant-design/icons';
import { useAuth } from '../../../contexts/AuthContext';
import { api } from '../../../services/api';
import ProductAnalysis from './components/ProductAnalysis';
import CustomerAnalysis from './components/CustomerAnalysis';
import ChannelAnalysis from './components/ChannelAnalysis';
import DataSourceConfig from './components/DataSourceConfig';
import PurchaseAnalysis from './components/PurchaseAnalysis';
import SupplyChainAnalysis from './components/SupplyChainAnalysis';
import './BusinessAnalysis.css';

const { TabPane } = Tabs;
const { Title, Text } = Typography;
const { RangePicker } = DatePicker;

/**
 * 经营分析主页面
 * 提供商品分析、客户分析、渠道分析三个核心模块
 */
const BusinessAnalysis = () => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('product');
  const [overviewData, setOverviewData] = useState(null);
  const [aiInsights, setAiInsights] = useState(null);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    fetchOverviewData();
  }, []);

  // 获取概览数据
  const fetchOverviewData = async () => {
    try {
      setLoading(true);
      
      // 获取项目ID
      const projectId = localStorage.getItem('project_id');
      if (!projectId) {
        throw new Error('未找到项目ID');
      }
      
      const response = await api.get(`/project/${projectId}/business-analysis/overview`);
      setOverviewData(response.data);
      
      // 获取AI洞察
      const aiResponse = await api.get(`/project/${projectId}/business-analysis/ai-insights`);
      setAiInsights(aiResponse.data);
    } catch (error) {
      console.error('获取经营分析数据失败:', error);
      message.error('获取数据失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 刷新数据
  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchOverviewData();
    setRefreshing(false);
    message.success('数据已刷新');
  };

  // 渲染概览卡片
  const renderOverviewCards = () => {
    if (!overviewData) return null;

    const cards = [
      {
        title: '商品竞争力指数',
        value: overviewData.productCompetitiveness || 0,
        suffix: '分',
        prefix: <ShoppingOutlined style={{ color: '#1890ff' }} />,
        color: '#1890ff',
        trend: overviewData.productTrend || 0
      },
      {
        title: '客户活跃度',
        value: overviewData.customerActivity || 0,
        suffix: '%',
        prefix: <UserOutlined style={{ color: '#52c41a' }} />,
        color: '#52c41a',
        trend: overviewData.customerTrend || 0
      },
      {
        title: '渠道效率',
        value: overviewData.channelEfficiency || 0,
        suffix: '%',
        prefix: <PartitionOutlined style={{ color: '#722ed1' }} />,
        color: '#722ed1',
        trend: overviewData.channelTrend || 0
      },
      {
        title: 'AI分析建议',
        value: overviewData.aiSuggestions || 0,
        suffix: '条',
        prefix: <RobotOutlined style={{ color: '#fa8c16' }} />,
        color: '#fa8c16',
        trend: 0
      }
    ];

    return (
      <Row gutter={[16, 16]}>
        {cards.map((card, index) => (
          <Col xs={24} sm={12} lg={6} key={index}>
            <Card>
              <Statistic
                title={card.title}
                value={card.value}
                suffix={card.suffix}
                prefix={card.prefix}
                valueStyle={{ color: card.color }}
              />
              {card.trend !== 0 && (
                <div style={{ marginTop: 8 }}>
                  <Text type={card.trend > 0 ? 'success' : 'danger'}>
                    {card.trend > 0 ? '↗' : '↘'} {Math.abs(card.trend)}%
                  </Text>
                  <Text type="secondary" style={{ marginLeft: 8 }}>
                    较上期
                  </Text>
                </div>
              )}
            </Card>
          </Col>
        ))}
      </Row>
    );
  };

  // 渲染AI洞察
  const renderAiInsights = () => {
    if (!aiInsights || !aiInsights.length) {
      return (
        <Card title={
          <Space>
            <RobotOutlined />
            AI智能洞察
          </Space>
        }>
          <Empty description="暂无AI分析结果" />
        </Card>
      );
    }

    return (
      <Card 
        title={
          <Space>
            <RobotOutlined />
            AI智能洞察
            <Tag color="blue">实时更新</Tag>
          </Space>
        }
        extra={
          <Button 
            icon={<SyncOutlined />} 
            onClick={handleRefresh}
            loading={refreshing}
            size="small"
          >
            刷新
          </Button>
        }
      >
        <div className="ai-insights">
          {aiInsights.map((insight, index) => (
            <div key={index} className="insight-item">
              <div className="insight-header">
                <Space>
                  {insight.type === 'opportunity' && <FireOutlined style={{ color: '#ff4d4f' }} />}
                  {insight.type === 'warning' && <ThunderboltOutlined style={{ color: '#faad14' }} />}
                  {insight.type === 'suggestion' && <EyeOutlined style={{ color: '#1890ff' }} />}
                  <Text strong>{insight.title}</Text>
                  <Tag color={
                    insight.priority === 'high' ? 'red' : 
                    insight.priority === 'medium' ? 'orange' : 'blue'
                  }>
                    {insight.priority === 'high' ? '高优先级' : 
                     insight.priority === 'medium' ? '中优先级' : '低优先级'}
                  </Tag>
                </Space>
              </div>
              <div className="insight-content">
                <Text>{insight.content}</Text>
              </div>
              {insight.suggestion && (
                <div className="insight-suggestion">
                  <Text type="secondary">建议：{insight.suggestion}</Text>
                </div>
              )}
            </div>
          ))}
        </div>
      </Card>
    );
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>加载经营分析数据...</div>
      </div>
    );
  }

  return (
    <div className="business-analysis">
      <div className="page-header">
        <Title level={2}>
          <RiseOutlined style={{ marginRight: 8 }} />
          经营分析
        </Title>
        <Text type="secondary">
          基于AI智能分析的全方位经营洞察，助力项目决策优化
        </Text>
      </div>

      <Alert
        message="智能经营分析"
        description="结合内部数据和外部市场信息，通过AI分析为您提供商品竞争力、客户洞察和渠道优化建议。"
        type="info"
        showIcon
        style={{ marginBottom: 24 }}
      />

      {/* 概览数据 */}
      {renderOverviewCards()}

      <Divider />

      {/* AI洞察 */}
      {renderAiInsights()}

      <Divider />

      {/* 详细分析Tab */}
      <Card>
        <Tabs 
          activeKey={activeTab} 
          onChange={setActiveTab}
          size="large"
          tabBarStyle={{ marginBottom: 24 }}
        >
          <TabPane 
            tab={
              <Space>
                <ShoppingOutlined />
                商品分析（货）
              </Space>
            } 
            key="product"
          >
            <ProductAnalysis />
          </TabPane>
          
          <TabPane 
            tab={
              <Space>
                <UserOutlined />
                客户分析（人）
              </Space>
            } 
            key="customer"
          >
            <CustomerAnalysis />
          </TabPane>
          
          <TabPane 
            tab={
              <Space>
                <PartitionOutlined />
                渠道分析（场）
              </Space>
            } 
            key="channel"
          >
            <ChannelAnalysis />
          </TabPane>
          
          <TabPane 
            tab={
              <Space>
                <DatabaseOutlined />
                配置数据源
              </Space>
            } 
            key="datasource"
          >
            <DataSourceConfig />
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default BusinessAnalysis; 