#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
MCP工具示例 - 支持multipart/form-data、图片处理和流式服务
"""

import asyncio
import json
import logging
import base64
from typing import Dict, Any, List
from base import MCPSession

logger = logging.getLogger(__name__)

async def image_analysis_tool(session: MCPSession, params: Dict[str, Any]) -> Dict[str, Any]:
    """
    图片分析工具示例 - 支持多种图片输入格式
    
    参数:
    - image: 图片数据（支持base64、URL、文件路径）
    - analysis_type: 分析类型（"objects", "text", "faces"等）
    """
    logger.info(f"[图片分析工具] 用户: {session.user_id}, 项目: {session.project_id}")
    
    image_data = params.get("image")
    analysis_type = params.get("analysis_type", "objects")
    
    if not image_data:
        return {
            "content": [
                {
                    "type": "text",
                    "text": "错误：缺少图片数据"
                }
            ],
            "isError": True
        }
    
    # 模拟图片分析过程
    await asyncio.sleep(1)  # 模拟处理时间
    
    # 根据分析类型返回不同结果
    if analysis_type == "objects":
        result = {
            "detected_objects": [
                {"name": "person", "confidence": 0.95, "bbox": [100, 100, 200, 300]},
                {"name": "car", "confidence": 0.87, "bbox": [300, 150, 500, 250]}
            ],
            "total_objects": 2
        }
    elif analysis_type == "text":
        result = {
            "extracted_text": "示例文本内容",
            "confidence": 0.92,
            "language": "zh-CN"
        }
    else:
        result = {
            "analysis_type": analysis_type,
            "status": "completed",
            "message": f"已完成{analysis_type}分析"
        }
    
    return {
        "content": [
            {
                "type": "text",
                "text": json.dumps(result, ensure_ascii=False, indent=2)
            }
        ],
        "isError": False
    }

async def streaming_data_processor(session: MCPSession, params: Dict[str, Any]) -> None:
    """
    流式数据处理工具示例 - 演示如何发送流式响应
    
    参数:
    - data_source: 数据源类型
    - chunk_size: 数据块大小
    - total_chunks: 总块数
    """
    logger.info(f"[流式数据处理] 开始处理，会话: {session.session_id}")
    
    data_source = params.get("data_source", "sample")
    chunk_size = params.get("chunk_size", 100)
    total_chunks = params.get("total_chunks", 10)

    if not session.internal_message_queue:
        logger.error("会话缺少消息队列，无法进行流式处理")
        return

    try:
        # 发送开始通知
        start_notification = {
            "jsonrpc": "2.0",
            "method": "notifications/toolStreamStart",
            "params": {
                "toolName": "streaming_data_processor",
                "totalChunks": total_chunks,
                "content": [
                    {
                        "type": "text",
                        "text": f"开始处理{data_source}数据，共{total_chunks}个数据块"
                    }
                ]
            }
        }
        await session.internal_message_queue.put(start_notification)
        
        # 模拟流式数据处理
        for i in range(total_chunks):
            # 模拟处理时间
            await asyncio.sleep(0.5)
            
            # 生成模拟数据
            chunk_data = {
                "chunk_id": i + 1,
                "data": f"数据块 {i + 1}: " + "x" * chunk_size,
                "timestamp": asyncio.get_event_loop().time(),
                "progress": (i + 1) / total_chunks * 100
            }
            
            # 发送数据块
            chunk_notification = {
                "jsonrpc": "2.0",
                "method": "notifications/toolStreamChunk",
                "params": {
                    "toolName": "streaming_data_processor",
                    "chunkIndex": i + 1,
                    "content": [
                        {
                            "type": "text",
                            "text": json.dumps(chunk_data, ensure_ascii=False)
                    }
                    ]
                }
            }
            await session.internal_message_queue.put(chunk_notification)
            
            logger.debug(f"已发送数据块 {i + 1}/{total_chunks}")

        # 发送完成通知
        completion_notification = {
            "jsonrpc": "2.0",
            "method": "notifications/toolStreamComplete",
            "params": {
                "toolName": "streaming_data_processor",
                "content": [
                    {
                        "type": "text",
                        "text": f"流式数据处理完成，共处理{total_chunks}个数据块"
                    }
                ]
            }
        }
        await session.internal_message_queue.put(completion_notification)
        
        logger.info(f"[流式数据处理] 完成，会话: {session.session_id}")

    except Exception as e:
        logger.error(f"流式数据处理出错: {e}", exc_info=True)
        # 发送错误通知
        error_notification = {
            "jsonrpc": "2.0",
            "method": "notifications/toolStreamError",
            "params": {
                "toolName": "streaming_data_processor",
                "error": {
                    "message": str(e),
                    "code": 1006
                },
                "content": [
                    {
                        "type": "text",
                        "text": f"流式数据处理出错: {str(e)}"
                    }
                ]
            }
        }
        await session.internal_message_queue.put(error_notification)

async def file_upload_tool(session: MCPSession, params: Dict[str, Any]) -> Dict[str, Any]:
    """
    文件上传工具示例 - 支持multipart/form-data
    
    参数:
    - files: 文件列表（支持多种格式）
    - description: 文件描述
    - tags: 标签列表
    """
    logger.info(f"[文件上传工具] 用户: {session.user_id}, 项目: {session.project_id}")
    
    files = params.get("files", [])
    description = params.get("description", "")
    tags = params.get("tags", [])
    
    if not files:
        return {
            "content": [
                {
                    "type": "text",
                    "text": "错误：没有提供文件"
                    }
                ],
            "isError": True
        }
    
    # 处理文件列表
    processed_files = []
    
    if not isinstance(files, list):
        files = [files]
    
    for i, file_data in enumerate(files):
        file_info = {
            "index": i + 1,
            "type": "unknown",
            "size": 0,
            "name": f"file_{i + 1}"
        }
        
        if isinstance(file_data, str):
            if file_data.startswith("data:"):
                # Base64编码的文件
                try:
                    header, data = file_data.split(',', 1)
                    content_type = header.split(';')[0].split(':')[1]
                    file_content = base64.b64decode(data)
                    
                    file_info.update({
                        "type": content_type,
                        "size": len(file_content),
                        "encoding": "base64"
                    })
                except Exception as e:
                    file_info["error"] = str(e)
            
            elif file_data.startswith(("http://", "https://")):
                # URL文件
                file_info.update({
                    "type": "url",
                    "url": file_data
                })
            
            else:
                # 文本内容
                file_info.update({
                    "type": "text/plain",
                    "size": len(file_data.encode('utf-8')),
                    "content_preview": file_data[:100] + "..." if len(file_data) > 100 else file_data
                })
        
        processed_files.append(file_info)
    
    # 模拟上传过程
    await asyncio.sleep(0.5)
    
    result = {
        "upload_id": f"upload_{session.session_id}_{asyncio.get_event_loop().time()}",
        "files_processed": len(processed_files),
        "files": processed_files,
        "description": description,
        "tags": tags,
        "status": "completed"
    }
    
    return {
        "content": [
            {
                "type": "text",
                "text": json.dumps(result, ensure_ascii=False, indent=2)
            }
        ],
        "isError": False
    }

async def multimedia_converter(session: MCPSession, params: Dict[str, Any]) -> Dict[str, Any]:
    """
    多媒体转换工具示例 - 处理图片、音频、视频等
    
    参数:
    - input_file: 输入文件
    - output_format: 输出格式
    - quality: 质量设置
    """
    logger.info(f"[多媒体转换] 用户: {session.user_id}, 项目: {session.project_id}")
    
    input_file = params.get("input_file")
    output_format = params.get("output_format", "jpg")
    quality = params.get("quality", 80)
    
    if not input_file:
        return {
            "content": [
                {
                    "type": "text",
                    "text": "错误：缺少输入文件"
                }
            ],
            "isError": True
        }
    
    # 模拟转换过程
    await asyncio.sleep(2)
    
    # 生成模拟的转换结果
    converted_data = f"data:image/{output_format};base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
    
    result = {
        "input_format": "auto-detected",
        "output_format": output_format,
        "quality": quality,
        "file_size_before": "1.2MB",
        "file_size_after": "800KB",
        "compression_ratio": "33%",
        "processing_time": "2.1s"
    }
    
    return {
        "content": [
            {
                "type": "text",
                "text": json.dumps(result, ensure_ascii=False, indent=2)
            },
            {
                "type": "image",
                "data": converted_data,
                "mimeType": f"image/{output_format}"
            }
        ],
        "isError": False
    }

# 可用工具字典
AVAILABLE_TOOLS = {
    "image_analysis": image_analysis_tool,
    "streaming_processor": streaming_data_processor,
    "file_upload": file_upload_tool,
    "multimedia_converter": multimedia_converter,
}

# 工具元数据（用于工具发现和验证）
TOOL_METADATA = {
    "image_analysis": {
        "name": "image_analysis",
        "description": "分析图片内容，支持对象检测、文本提取等功能",
        "inputSchema": {
            "type": "object",
            "properties": {
                "image": {
                    "type": "string",
                    "description": "图片数据（base64、URL或文件路径）",
                    "format": "binary"
                },
                "analysis_type": {
                    "type": "string",
                    "enum": ["objects", "text", "faces", "scenes"],
                    "description": "分析类型",
                    "default": "objects"
                }
            },
            "required": ["image"]
        },
        "supports_streaming": False
    },
    "streaming_processor": {
        "name": "streaming_processor",
        "description": "流式数据处理工具，演示实时数据处理",
        "inputSchema": {
            "type": "object",
            "properties": {
                "data_source": {
                    "type": "string",
                    "description": "数据源类型",
                    "default": "sample"
                },
                "chunk_size": {
                    "type": "integer",
                    "description": "数据块大小",
                    "default": 100,
                    "minimum": 10,
                    "maximum": 1000
                },
                "total_chunks": {
                    "type": "integer",
                    "description": "总数据块数",
                    "default": 10,
                    "minimum": 1,
                    "maximum": 100
                }
            }
        },
        "supports_streaming": True
    },
    "file_upload": {
        "name": "file_upload",
        "description": "文件上传工具，支持多种文件格式和multipart/form-data",
        "inputSchema": {
            "type": "object",
            "properties": {
                "files": {
                    "type": "array",
                    "items": {
                        "type": "string",
                        "format": "binary"
                    },
                    "description": "文件列表"
                },
                "description": {
                    "type": "string",
                    "description": "文件描述"
                },
                "tags": {
                    "type": "array",
                    "items": {
                        "type": "string"
                    },
                    "description": "标签列表"
                }
            },
            "required": ["files"]
        },
        "supports_streaming": False,
        "content_type": "multipart/form-data"
    },
    "multimedia_converter": {
        "name": "multimedia_converter",
        "description": "多媒体文件转换工具",
        "inputSchema": {
            "type": "object",
            "properties": {
                "input_file": {
                    "type": "string",
                    "format": "binary",
                    "description": "输入文件"
                },
                "output_format": {
                    "type": "string",
                    "enum": ["jpg", "png", "webp", "gif"],
                    "description": "输出格式",
                    "default": "jpg"
                },
                "quality": {
                    "type": "integer",
                    "minimum": 1,
                    "maximum": 100,
                    "description": "输出质量",
                    "default": 80
                }
            },
            "required": ["input_file"]
        },
        "supports_streaming": False
    }
}