import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Modal,
  Form,
  Input,
  Select,
  Switch,
  message,
  Tooltip,
  Badge,
  Typography,
  Alert,
  Divider
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  ReloadOutlined,
  WechatOutlined,
  DingdingOutlined,
  ApiOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  InfoCircleOutlined,
  ImportOutlined
} from '@ant-design/icons';

const { Option } = Select;
const { TextArea } = Input;
const { Text, Paragraph } = Typography;

const PlatformManagement = () => {
  const [loading, setLoading] = useState(false);
  const [platforms, setPlatforms] = useState([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingPlatform, setEditingPlatform] = useState(null);
  const [importModalVisible, setImportModalVisible] = useState(false);
  const [thirdPartyConfigs, setThirdPartyConfigs] = useState({});
  const [form] = Form.useForm();

  // 获取项目ID
  const getProjectId = () => {
    const projectId = localStorage.getItem('project_id');
    if (projectId && projectId !== 'null' && projectId !== 'undefined') {
      return projectId;
    }

    const userData = JSON.parse(localStorage.getItem('user_data') || '{}');
    if (userData && userData.project_id) {
      localStorage.setItem('project_id', userData.project_id);
      return userData.project_id;
    }

    return null;
  };

  // 获取第三方登录配置
  const fetchThirdPartyConfigs = async () => {
    try {
      const projectId = getProjectId();
      if (!projectId) {
        console.error('无法获取项目ID');
        return;
      }

      const response = await fetch(`/api/project/${projectId}/settings/third_party_login`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setThirdPartyConfigs(data);
      }
    } catch (error) {
      console.error('获取第三方登录配置失败:', error);
    }
  };

  // 导入第三方登录配置
  const handleImportThirdPartyConfig = (platformType) => {
    const configMap = {
      'wechat_mp': thirdPartyConfigs.wechat,
      'wechat_work': thirdPartyConfigs.wechat_work,
      'dingtalk': thirdPartyConfigs.dingtalk,
      'feishu': thirdPartyConfigs.feishu
    };

    const config = configMap[platformType];
    if (!config || !config.enabled) {
      message.warning(`${getPlatformName(platformType)}配置未启用或不存在`);
      return;
    }

    // 根据平台类型映射配置字段
    let formValues = {
      platform_type: platformType,
      name: `${getPlatformName(platformType)}客服`
    };

    if (platformType === 'wechat_mp' || platformType === 'wechat') {
      formValues = {
        ...formValues,
        app_id: config.app_id,
        app_secret: config.app_secret
      };
    } else if (platformType === 'wechat_work') {
      formValues = {
        ...formValues,
        app_id: config.corp_id,
        app_secret: config.secret,
        agent_id: config.agent_id
      };
    } else if (platformType === 'dingtalk') {
      formValues = {
        ...formValues,
        app_id: config.app_key,
        app_secret: config.app_secret
      };
    } else if (platformType === 'feishu') {
      formValues = {
        ...formValues,
        app_id: config.app_id,
        app_secret: config.app_secret
      };
    }

    form.setFieldsValue(formValues);
    setImportModalVisible(false);
    message.success('配置导入成功');
  };

  // 获取平台列表
  const fetchPlatforms = async () => {
    setLoading(true);
    try {
      const projectId = getProjectId();
      if (!projectId) {
        console.error('无法获取项目ID');
        setLoading(false);
        return;
      }

      const response = await fetch(`/api/v1/project/${projectId}/plugin/ai-customer-service/platforms`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setPlatforms(data.platforms || []);
      } else {
        // 如果API调用失败，使用模拟数据
        const mockPlatforms = [
          {
            id: '1',
            platform_type: 'wechat_mp',
            name: '微信公众号客服',
            app_id: 'wx1234567890',
            status: 'active',
            is_enabled: true,
            webhook_url: 'https://api.example.com/webhooks/wechat-mp',
            last_sync_at: '2025-01-08 12:00:00',
            message_count: 1256,
            error_count: 2,
            config: {
              app_secret: '***hidden***',
              token: '***hidden***',
              encoding_aes_key: '***hidden***'
            }
          },
          {
            id: '2',
            platform_type: 'wechat_work',
            name: '企业微信客服',
            app_id: 'ww1234567890',
            status: 'active',
            is_enabled: true,
            webhook_url: 'https://api.example.com/webhooks/wechat-work',
            last_sync_at: '2025-01-08 11:45:00',
            message_count: 856,
            error_count: 0,
            config: {
              corp_secret: '***hidden***',
              agent_id: '1000001'
            }
          },
          {
            id: '3',
            platform_type: 'dingtalk',
            name: '钉钉智能机器人',
            app_id: 'ding1234567890',
            status: 'inactive',
            is_enabled: false,
            webhook_url: 'https://api.example.com/webhooks/dingtalk',
            last_sync_at: '2025-01-07 18:30:00',
            message_count: 234,
            error_count: 5,
            config: {
              app_secret: '***hidden***',
              robot_code: 'robot_001'
            }
          }
        ];

        setPlatforms(mockPlatforms);
      }
    } catch (error) {
      console.error('获取平台列表失败:', error);
      message.error('获取平台列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 添加/编辑平台
  const handleSavePlatform = async (values) => {
    try {
      setLoading(true);
      
      if (editingPlatform) {
        // 编辑平台
        console.log('编辑平台:', editingPlatform.id, values);
        message.success('平台配置更新成功');
      } else {
        // 添加平台
        console.log('添加平台:', values);
        message.success('平台添加成功');
      }
      
      setModalVisible(false);
      setEditingPlatform(null);
      form.resetFields();
      fetchPlatforms();
    } catch (error) {
      console.error('保存平台失败:', error);
      message.error('保存平台失败');
    } finally {
      setLoading(false);
    }
  };

  // 删除平台
  const handleDeletePlatform = (platform) => {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除平台 "${platform.name}" 吗？删除后将无法恢复。`,
      okText: '确认删除',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          console.log('删除平台:', platform.id);
          message.success('平台删除成功');
          fetchPlatforms();
        } catch (error) {
          console.error('删除平台失败:', error);
          message.error('删除平台失败');
        }
      }
    });
  };

  // 切换平台状态
  const handleTogglePlatform = async (platform, enabled) => {
    try {
      console.log('切换平台状态:', platform.id, enabled);
      message.success(`平台已${enabled ? '启用' : '禁用'}`);
      fetchPlatforms();
    } catch (error) {
      console.error('切换平台状态失败:', error);
      message.error('切换平台状态失败');
    }
  };

  // 测试平台连接
  const handleTestPlatform = async (platform) => {
    try {
      setLoading(true);
      console.log('测试平台连接:', platform.id);
      
      // 模拟测试
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      message.success('平台连接测试成功');
    } catch (error) {
      console.error('测试平台连接失败:', error);
      message.error('测试平台连接失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取平台图标
  const getPlatformIcon = (platformType) => {
    const iconMap = {
      'wechat_mp': <WechatOutlined style={{ color: '#52c41a' }} />,
      'wechat_work': <WechatOutlined style={{ color: '#1890ff' }} />,
      'dingtalk': <DingdingOutlined style={{ color: '#faad14' }} />,
      'feishu': <ApiOutlined style={{ color: '#722ed1' }} />
    };
    
    return iconMap[platformType] || <ApiOutlined />;
  };

  // 获取平台名称
  const getPlatformName = (platformType) => {
    const nameMap = {
      'wechat_mp': '微信公众号',
      'wechat_work': '企业微信',
      'dingtalk': '钉钉',
      'feishu': '飞书'
    };
    
    return nameMap[platformType] || platformType;
  };

  // 获取状态标签
  const getStatusTag = (status, isEnabled) => {
    if (!isEnabled) {
      return <Tag color="default">已禁用</Tag>;
    }
    
    const statusMap = {
      'active': { color: 'success', text: '正常' },
      'inactive': { color: 'warning', text: '离线' },
      'error': { color: 'error', text: '错误' }
    };
    
    const statusInfo = statusMap[status] || { color: 'default', text: status };
    return <Tag color={statusInfo.color}>{statusInfo.text}</Tag>;
  };

  useEffect(() => {
    fetchPlatforms();
    fetchThirdPartyConfigs();
  }, []);

  const columns = [
    {
      title: '平台',
      key: 'platform',
      width: 150,
      render: (_, record) => (
        <Space>
          {getPlatformIcon(record.platform_type)}
          <div>
            <div>{record.name}</div>
            <Text type="secondary" style={{ fontSize: 12 }}>
              {getPlatformName(record.platform_type)}
            </Text>
          </div>
        </Space>
      )
    },
    {
      title: 'App ID',
      dataIndex: 'app_id',
      key: 'app_id',
      width: 150,
      render: (text) => <Text code>{text}</Text>
    },
    {
      title: '状态',
      key: 'status',
      width: 100,
      render: (_, record) => getStatusTag(record.status, record.is_enabled)
    },
    {
      title: '消息统计',
      key: 'stats',
      width: 120,
      render: (_, record) => (
        <div>
          <div>
            <Badge count={record.message_count} showZero style={{ backgroundColor: '#52c41a' }} />
            <Text style={{ marginLeft: 8, fontSize: 12 }}>消息</Text>
          </div>
          {record.error_count > 0 && (
            <div>
              <Badge count={record.error_count} style={{ backgroundColor: '#f5222d' }} />
              <Text style={{ marginLeft: 8, fontSize: 12 }}>错误</Text>
            </div>
          )}
        </div>
      )
    },
    {
      title: '最后同步',
      dataIndex: 'last_sync_at',
      key: 'last_sync_at',
      width: 150,
      render: (time) => (
        <Text style={{ fontSize: 12 }}>
          {time ? new Date(time).toLocaleString() : '-'}
        </Text>
      )
    },
    {
      title: '启用状态',
      key: 'enabled',
      width: 100,
      render: (_, record) => (
        <Switch
          checked={record.is_enabled}
          onChange={(checked) => handleTogglePlatform(record, checked)}
          size="small"
        />
      )
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_, record) => (
        <Space>
          <Tooltip title="编辑配置">
            <Button
              type="link"
              icon={<EditOutlined />}
              onClick={() => {
                setEditingPlatform(record);
                form.setFieldsValue({
                  platform_type: record.platform_type,
                  name: record.name,
                  app_id: record.app_id,
                  ...record.config
                });
                setModalVisible(true);
              }}
            />
          </Tooltip>
          <Tooltip title="测试连接">
            <Button
              type="link"
              icon={<CheckCircleOutlined />}
              onClick={() => handleTestPlatform(record)}
              loading={loading}
            />
          </Tooltip>
          <Tooltip title="删除平台">
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
              onClick={() => handleDeletePlatform(record)}
            />
          </Tooltip>
        </Space>
      )
    }
  ];

  return (
    <div>
      <Card
        title="平台管理"
        extra={
          <Space>
            <Button
              icon={<ImportOutlined />}
              onClick={() => setImportModalVisible(true)}
            >
              导入配置
            </Button>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {
                setEditingPlatform(null);
                form.resetFields();
                setModalVisible(true);
              }}
            >
              添加平台
            </Button>
            <Button
              icon={<ReloadOutlined />}
              onClick={fetchPlatforms}
              loading={loading}
            >
              刷新
            </Button>
          </Space>
        }
      >
        <Alert
          message="平台接入说明"
          description={
            <div>
              <Paragraph>
                支持多种平台接入，每个平台需要配置相应的应用信息和密钥。
                微信公众号支持15秒响应机制和异步消息处理。
              </Paragraph>
              <ul>
                <li><strong>微信公众号</strong>：需要配置 App ID、App Secret、Token 和 EncodingAESKey</li>
                <li><strong>企业微信</strong>：需要配置 Corp ID、Corp Secret 和 Agent ID</li>
                <li><strong>钉钉</strong>：需要配置 App Key、App Secret 和机器人代码</li>
                <li><strong>飞书</strong>：需要配置 App ID、App Secret 和机器人密钥</li>
              </ul>
            </div>
          }
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />

        <Table
          columns={columns}
          dataSource={platforms}
          rowKey="id"
          loading={loading}
          pagination={false}
        />
      </Card>

      {/* 添加/编辑平台弹窗 */}
      <Modal
        title={editingPlatform ? '编辑平台' : '添加平台'}
        visible={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          setEditingPlatform(null);
          form.resetFields();
        }}
        onOk={() => form.submit()}
        confirmLoading={loading}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSavePlatform}
        >
          <Form.Item
            name="platform_type"
            label="平台类型"
            rules={[{ required: true, message: '请选择平台类型' }]}
          >
            <Select placeholder="选择平台类型" disabled={!!editingPlatform}>
              <Option value="wechat_mp">微信公众号</Option>
              <Option value="wechat_work">企业微信</Option>
              <Option value="dingtalk">钉钉</Option>
              <Option value="feishu">飞书</Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="name"
            label="平台名称"
            rules={[{ required: true, message: '请输入平台名称' }]}
          >
            <Input placeholder="输入平台名称" />
          </Form.Item>

          <Form.Item
            name="app_id"
            label="App ID"
            rules={[{ required: true, message: '请输入App ID' }]}
          >
            <Input placeholder="输入App ID" />
          </Form.Item>

          <Form.Item
            name="app_secret"
            label="App Secret"
            rules={[{ required: true, message: '请输入App Secret' }]}
          >
            <Input.Password placeholder="输入App Secret" />
          </Form.Item>

          <Form.Item
            name="token"
            label="Token"
            rules={[{ required: true, message: '请输入Token' }]}
          >
            <Input placeholder="输入Token" />
          </Form.Item>

          <Form.Item
            name="encoding_aes_key"
            label="EncodingAESKey"
          >
            <Input placeholder="输入EncodingAESKey（可选）" />
          </Form.Item>

          <Form.Item
            name="webhook_url"
            label="Webhook URL"
          >
            <Input placeholder="系统将自动生成" disabled />
          </Form.Item>
        </Form>
      </Modal>

      {/* 导入第三方登录配置弹窗 */}
      <Modal
        title="导入第三方登录配置"
        visible={importModalVisible}
        onCancel={() => setImportModalVisible(false)}
        footer={null}
        width={600}
      >
        <Alert
          message="从系统设置导入配置"
          description="选择要导入的第三方登录配置，系统将自动填充相应的平台参数。"
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />

        <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 16 }}>
          {/* 微信公众号 */}
          <Card
            size="small"
            title={
              <Space>
                <WechatOutlined style={{ color: '#52c41a' }} />
                微信公众号
              </Space>
            }
            extra={
              thirdPartyConfigs.wechat?.enabled ? (
                <Tag color="success">已配置</Tag>
              ) : (
                <Tag color="default">未配置</Tag>
              )
            }
          >
            <Button
              block
              disabled={!thirdPartyConfigs.wechat?.enabled}
              onClick={() => handleImportThirdPartyConfig('wechat_mp')}
            >
              导入配置
            </Button>
          </Card>

          {/* 企业微信 */}
          <Card
            size="small"
            title={
              <Space>
                <WechatOutlined style={{ color: '#1890ff' }} />
                企业微信
              </Space>
            }
            extra={
              thirdPartyConfigs.wechat_work?.enabled ? (
                <Tag color="success">已配置</Tag>
              ) : (
                <Tag color="default">未配置</Tag>
              )
            }
          >
            <Button
              block
              disabled={!thirdPartyConfigs.wechat_work?.enabled}
              onClick={() => handleImportThirdPartyConfig('wechat_work')}
            >
              导入配置
            </Button>
          </Card>

          {/* 钉钉 */}
          <Card
            size="small"
            title={
              <Space>
                <DingdingOutlined style={{ color: '#faad14' }} />
                钉钉
              </Space>
            }
            extra={
              thirdPartyConfigs.dingtalk?.enabled ? (
                <Tag color="success">已配置</Tag>
              ) : (
                <Tag color="default">未配置</Tag>
              )
            }
          >
            <Button
              block
              disabled={!thirdPartyConfigs.dingtalk?.enabled}
              onClick={() => handleImportThirdPartyConfig('dingtalk')}
            >
              导入配置
            </Button>
          </Card>

          {/* 飞书 */}
          <Card
            size="small"
            title={
              <Space>
                <ApiOutlined style={{ color: '#722ed1' }} />
                飞书
              </Space>
            }
            extra={
              thirdPartyConfigs.feishu?.enabled ? (
                <Tag color="success">已配置</Tag>
              ) : (
                <Tag color="default">未配置</Tag>
              )
            }
          >
            <Button
              block
              disabled={!thirdPartyConfigs.feishu?.enabled}
              onClick={() => handleImportThirdPartyConfig('feishu')}
            >
              导入配置
            </Button>
          </Card>
        </div>

        <Divider />

        <div style={{ textAlign: 'center' }}>
          <Button onClick={() => setImportModalVisible(false)}>
            关闭
          </Button>
        </div>
      </Modal>
    </div>
  );
};

export default PlatformManagement;
