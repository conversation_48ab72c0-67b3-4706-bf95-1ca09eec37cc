#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
OCR 引擎模块
提供小票图像识别功能
"""

import io
import re
import logging
import numpy as np
from datetime import datetime
from typing import Dict, Any, List, Optional, Tuple
from PIL import Image, ImageEnhance, ImageFilter

# 尝试导入 pytesseract，如果不可用则使用模拟模式
try:
    import pytesseract
    import cv2
    OCR_AVAILABLE = True
except ImportError:
    OCR_AVAILABLE = False
    logging.warning("pytesseract 或 cv2 未安装，将使用模拟模式进行小票识别")

# 配置日志
logger = logging.getLogger(__name__)

class OCREngine:
    """OCR 引擎类，提供小票图像识别功能"""
    
    @staticmethod
    def preprocess_image(image_data: bytes) -> np.ndarray:
        """
        预处理图像，提高OCR识别准确率
        
        Args:
            image_data: 图像二进制数据
            
        Returns:
            处理后的图像数组
        """
        if not OCR_AVAILABLE:
            return np.array([])
            
        try:
            # 将二进制数据转换为图像
            image = Image.open(io.BytesIO(image_data))
            
            # 转换为灰度图
            image = image.convert('L')
            
            # 增强对比度
            enhancer = ImageEnhance.Contrast(image)
            image = enhancer.enhance(2.0)
            
            # 锐化图像
            image = image.filter(ImageFilter.SHARPEN)
            
            # 转换为OpenCV格式
            img_np = np.array(image)
            
            # 二值化处理
            _, binary = cv2.threshold(img_np, 150, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            
            # 降噪处理
            kernel = np.ones((1, 1), np.uint8)
            binary = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)
            
            return binary
        except Exception as e:
            logger.error(f"图像预处理失败: {str(e)}")
            return np.array([])
    
    @staticmethod
    def recognize_text(image: np.ndarray) -> str:
        """
        识别图像中的文本
        
        Args:
            image: 预处理后的图像数组
            
        Returns:
            识别出的文本
        """
        if not OCR_AVAILABLE or image.size == 0:
            return ""
            
        try:
            # 使用 pytesseract 识别文本
            text = pytesseract.image_to_string(
                image, 
                lang='chi_sim+eng',  # 使用中文和英文识别
                config='--psm 6 --oem 3'  # 假设是块状文本
            )
            
            return text
        except Exception as e:
            logger.error(f"文本识别失败: {str(e)}")
            return ""
    
    @staticmethod
    def extract_total_amount(text: str) -> Optional[float]:
        """
        从文本中提取消费总金额
        
        Args:
            text: 识别出的文本
            
        Returns:
            消费总金额，如果未找到则返回None
        """
        # 匹配常见的金额模式
        patterns = [
            r'总计[:：]?\s*￥?\s*(\d+\.?\d*)',
            r'合计[:：]?\s*￥?\s*(\d+\.?\d*)',
            r'总金额[:：]?\s*￥?\s*(\d+\.?\d*)',
            r'金额[:：]?\s*￥?\s*(\d+\.?\d*)',
            r'Total[:：]?\s*￥?\s*(\d+\.?\d*)',
            r'￥\s*(\d+\.?\d*)',
            r'¥\s*(\d+\.?\d*)',
            r'RMB\s*(\d+\.?\d*)'
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            if matches:
                try:
                    # 返回找到的最大金额
                    amounts = [float(match) for match in matches]
                    return max(amounts)
                except ValueError:
                    continue
        
        return None
    
    @staticmethod
    def extract_date(text: str) -> Optional[datetime]:
        """
        从文本中提取日期
        
        Args:
            text: 识别出的文本
            
        Returns:
            日期，如果未找到则返回None
        """
        # 匹配常见的日期模式
        patterns = [
            r'(\d{4})[年/-](\d{1,2})[月/-](\d{1,2})日?',  # 2023-05-01 或 2023年5月1日
            r'(\d{2})[年/-](\d{1,2})[月/-](\d{1,2})日?',  # 23-05-01 或 23年5月1日
            r'日期[:：]?\s*(\d{4})[年/-](\d{1,2})[月/-](\d{1,2})日?',
            r'日期[:：]?\s*(\d{2})[年/-](\d{1,2})[月/-](\d{1,2})日?',
            r'Date[:：]?\s*(\d{4})[年/-](\d{1,2})[月/-](\d{1,2})日?',
            r'Date[:：]?\s*(\d{2})[年/-](\d{1,2})[月/-](\d{1,2})日?'
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, text)
            if matches:
                try:
                    year, month, day = matches[0]
                    year = int(year)
                    month = int(month)
                    day = int(day)
                    
                    # 处理两位数年份
                    if year < 100:
                        year += 2000
                    
                    return datetime(year, month, day)
                except (ValueError, IndexError):
                    continue
        
        return None
    
    @staticmethod
    def extract_store_info(text: str) -> Tuple[Optional[str], Optional[str]]:
        """
        从文本中提取商店信息和小票号
        
        Args:
            text: 识别出的文本
            
        Returns:
            (商店名称, 小票号)，如果未找到则返回(None, None)
        """
        # 提取商店名称
        store_patterns = [
            r'^(.+?)\n',  # 第一行通常是商店名称
            r'店名[:：]?\s*(.+)',
            r'商店[:：]?\s*(.+)',
            r'Store[:：]?\s*(.+)'
        ]
        
        store_name = None
        for pattern in store_patterns:
            matches = re.search(pattern, text)
            if matches:
                store_name = matches.group(1).strip()
                break
        
        # 提取小票号
        receipt_patterns = [
            r'小票号[:：]?\s*(\w+)',
            r'单号[:：]?\s*(\w+)',
            r'票据号[:：]?\s*(\w+)',
            r'Receipt No[:：]?\s*(\w+)',
            r'No[:：]?\s*(\w+)'
        ]
        
        receipt_id = None
        for pattern in receipt_patterns:
            matches = re.search(pattern, text)
            if matches:
                receipt_id = matches.group(1).strip()
                break
        
        return store_name, receipt_id
    
    @staticmethod
    def extract_items(text: str) -> List[Dict[str, Any]]:
        """
        从文本中提取商品列表
        
        Args:
            text: 识别出的文本
            
        Returns:
            商品列表，每个商品包含名称、数量、单价和金额
        """
        items = []
        
        # 匹配商品行的模式
        # 格式可能是: 商品名 数量 单价 金额
        # 或者: 商品名 x数量 单价 金额
        item_patterns = [
            r'(.+?)\s+(\d+)\s+(\d+\.?\d*)\s+(\d+\.?\d*)',  # 商品名 数量 单价 金额
            r'(.+?)\s+x\s*(\d+)\s+(\d+\.?\d*)\s+(\d+\.?\d*)',  # 商品名 x数量 单价 金额
            r'(.+?)\s+(\d+)\s+(\d+\.?\d*)',  # 商品名 数量 单价
            r'(.+?)\s+x\s*(\d+)\s+(\d+\.?\d*)'  # 商品名 x数量 单价
        ]
        
        lines = text.split('\n')
        for line in lines:
            for pattern in item_patterns:
                matches = re.search(pattern, line)
                if matches:
                    groups = matches.groups()
                    if len(groups) >= 3:
                        name = groups[0].strip()
                        quantity = int(groups[1])
                        price = float(groups[2])
                        
                        # 如果有金额，使用它；否则计算金额
                        amount = float(groups[3]) if len(groups) >= 4 else price * quantity
                        
                        items.append({
                            "name": name,
                            "quantity": quantity,
                            "price": price,
                            "amount": amount
                        })
                    break
        
        return items
    
    @staticmethod
    def extract_member_id(text: str) -> Optional[str]:
        """
        从文本中提取会员ID
        
        Args:
            text: 识别出的文本
            
        Returns:
            会员ID，如果未找到则返回None
        """
        # 匹配会员ID的模式
        patterns = [
            r'会员号[:：]?\s*(\w+)',
            r'会员ID[:：]?\s*(\w+)',
            r'会员卡号[:：]?\s*(\w+)',
            r'Member ID[:：]?\s*(\w+)',
            r'Member No[:：]?\s*(\w+)'
        ]
        
        for pattern in patterns:
            matches = re.search(pattern, text)
            if matches:
                return matches.group(1).strip()
        
        return None
    
    @classmethod
    def recognize_receipt(cls, image_data: bytes) -> Dict[str, Any]:
        """
        识别小票信息
        
        Args:
            image_data: 小票图像二进制数据
            
        Returns:
            识别结果，包含成功标志、消费总金额、会员ID、商店名称、交易时间、小票ID、商品列表和原始文本
        """
        try:
            if OCR_AVAILABLE:
                # 预处理图像
                processed_image = cls.preprocess_image(image_data)
                
                # 如果预处理失败，返回失败结果
                if processed_image.size == 0:
                    logger.warning("图像预处理失败，将使用模拟模式")
                    return cls.mock_receipt_recognition()
                
                # 识别文本
                text = cls.recognize_text(processed_image)
                
                # 如果文本识别失败，返回失败结果
                if not text:
                    logger.warning("文本识别失败，将使用模拟模式")
                    return cls.mock_receipt_recognition()
                
                # 提取信息
                total_amount = cls.extract_total_amount(text)
                transaction_time = cls.extract_date(text)
                store_name, receipt_id = cls.extract_store_info(text)
                member_id = cls.extract_member_id(text)
                items = cls.extract_items(text)
                
                # 如果关键信息提取失败，使用模拟数据补充
                if total_amount is None:
                    logger.warning("未能提取消费总金额，将使用模拟数据")
                    total_amount = 100.0
                
                if transaction_time is None:
                    logger.warning("未能提取交易时间，将使用当前时间")
                    transaction_time = datetime.now()
                
                if store_name is None:
                    logger.warning("未能提取商店名称，将使用模拟数据")
                    store_name = "未知商店"
                
                if receipt_id is None:
                    logger.warning("未能提取小票ID，将使用模拟数据")
                    receipt_id = f"R{datetime.now().strftime('%Y%m%d%H%M%S')}"
                
                if not member_id:
                    logger.warning("未能提取会员ID，将使用模拟数据")
                    member_id = f"M{datetime.now().strftime('%Y%m%d%H%M%S')}"
                
                if not items:
                    logger.warning("未能提取商品列表，将使用模拟数据")
                    items = [
                        {
                            "name": "未识别商品",
                            "quantity": 1,
                            "price": total_amount,
                            "amount": total_amount
                        }
                    ]
                
                return {
                    "success": True,
                    "total_amount": total_amount,
                    "member_id": member_id,
                    "store_name": store_name,
                    "transaction_time": transaction_time,
                    "receipt_id": receipt_id,
                    "items": items,
                    "raw_text": text
                }
            else:
                # 如果OCR不可用，使用模拟模式
                logger.warning("OCR不可用，将使用模拟模式")
                return cls.mock_receipt_recognition()
        except Exception as e:
            logger.error(f"小票识别失败: {str(e)}")
            return {
                "success": False,
                "error_message": str(e)
            }
    
    @staticmethod
    def mock_receipt_recognition() -> Dict[str, Any]:
        """
        模拟小票识别结果
        
        Returns:
            模拟的小票识别结果
        """
        import random
        
        # 生成随机的消费金额
        total_amount = round(random.uniform(50, 500), 2)
        
        # 生成随机的会员ID
        member_id = f"M{random.randint(10000, 99999)}"
        
        # 生成随机的门店名称
        store_names = ["旗舰店", "中心店", "西区店", "东区店", "北区店", "南区店"]
        store_name = random.choice(store_names)
        
        # 生成随机的交易时间（最近7天内）
        days_ago = random.randint(0, 7)
        hours = random.randint(9, 21)
        minutes = random.randint(0, 59)
        transaction_time = datetime.now().replace(
            hour=hours, 
            minute=minutes, 
            second=0, 
            microsecond=0
        )
        
        # 生成随机的小票ID
        receipt_id = f"R{random.randint(100000, 999999)}"
        
        # 生成随机的商品列表
        items = []
        product_names = [
            "牛奶", "面包", "饼干", "巧克力", "咖啡", "茶叶", 
            "水果", "蔬菜", "肉类", "海鲜", "饮料", "零食"
        ]
        
        item_count = random.randint(1, 5)
        for _ in range(item_count):
            product_name = random.choice(product_names)
            quantity = random.randint(1, 3)
            price = round(random.uniform(5, 50), 2)
            items.append({
                "name": product_name,
                "quantity": quantity,
                "price": price,
                "amount": round(quantity * price, 2)
            })
        
        # 生成模拟的原始文本
        raw_text = f"""
        {store_name}
        收银小票
        日期：{transaction_time.strftime('%Y-%m-%d %H:%M:%S')}
        小票号：{receipt_id}
        会员号：{member_id}
        ------------------------
        """
        
        for item in items:
            raw_text += f"{item['name']} x{item['quantity']} {item['price']}元 = {item['amount']}元\n"
        
        raw_text += f"""
        ------------------------
        总计：{total_amount}元
        谢谢惠顾，欢迎再次光临！
        """
        
        return {
            "success": True,
            "total_amount": total_amount,
            "member_id": member_id,
            "store_name": store_name,
            "transaction_time": transaction_time,
            "receipt_id": receipt_id,
            "items": items,
            "raw_text": raw_text
        }
