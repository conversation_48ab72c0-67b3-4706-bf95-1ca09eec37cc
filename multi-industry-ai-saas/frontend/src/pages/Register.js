import React, { useState, useEffect } from 'react';
import { Form, Input, Button, Card, Typography, message, Divider, Select, Alert } from 'antd';
import { UserOutlined, LockOutlined, MailOutlined, BankOutlined, CloudServerOutlined, PhoneOutlined } from '@ant-design/icons';
import { Link, useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import apiService from '../services/api';

const { Title, Text } = Typography;
const { Option } = Select;

const Register = () => {
  const { register, loading } = useAuth();
  const navigate = useNavigate();
  const [form] = Form.useForm();
  
  const [serviceProviders, setServiceProviders] = useState([]);
  const [tenants, setTenants] = useState([]);
  const [loadingProviders, setLoadingProviders] = useState(false);
  const [loadingTenants, setLoadingTenants] = useState(false);
  const [error, setError] = useState('');
  const [selectedServiceProvider, setSelectedServiceProvider] = useState(null);
  
  // 加载服务商列表
  useEffect(() => {
    const fetchServiceProviders = async () => {
      try {
        setLoadingProviders(true);
        const response = await apiService.getServiceProviders({ is_active: true });
        setServiceProviders(response);
      } catch (error) {
        console.error('获取服务商列表失败:', error);
      } finally {
        setLoadingProviders(false);
      }
    };
    
    fetchServiceProviders();
  }, []);
  
  // 当选择服务商时，加载该服务商下的租户
  useEffect(() => {
    if (selectedServiceProvider) {
      const fetchTenants = async () => {
        try {
          setLoadingTenants(true);
          const response = await apiService.getTenantsByServiceProvider(selectedServiceProvider, { is_active: true });
          setTenants(response);
        } catch (error) {
          console.error('获取租户列表失败:', error);
        } finally {
          setLoadingTenants(false);
        }
      };
      
      fetchTenants();
    } else {
      setTenants([]);
    }
  }, [selectedServiceProvider]);
  
  // 处理服务商变化
  const handleServiceProviderChange = (value) => {
    setSelectedServiceProvider(value);
    form.setFieldsValue({ tenant_id: undefined });
  };
  
  // 处理注册
  const handleRegister = async (values) => {
    try {
      setError('');
      await register(values);
      message.success('注册成功，请登录');
      navigate('/login');
    } catch (error) {
      console.error('注册失败:', error);
      setError('注册失败，请检查输入信息');
    }
  };
  
  return (
    <div style={{ 
      display: 'flex', 
      justifyContent: 'center', 
      alignItems: 'center', 
      height: '100vh',
      background: '#f0f2f5'
    }}>
      <Card style={{ width: 400, boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)' }}>
        <div style={{ textAlign: 'center', marginBottom: 24 }}>
          <Title level={2}>多行业AI SaaS系统</Title>
          <Text type="secondary">创建新账户</Text>
        </div>
        
        {error && (
          <Alert
            message={error}
            type="error"
            showIcon
            style={{ marginBottom: 24 }}
          />
        )}
        
        <Form
          form={form}
          name="register"
          onFinish={handleRegister}
          size="large"
          scrollToFirstError
        >
          <Form.Item
            name="service_provider_id"
            rules={[{ required: false, message: '请选择服务商' }]}
          >
            <Select
              placeholder="选择服务商（可选）"
              allowClear
              loading={loadingProviders}
              onChange={handleServiceProviderChange}
              suffixIcon={<CloudServerOutlined />}
            >
              {serviceProviders.map(provider => (
                <Option key={provider.id} value={provider.id}>
                  {provider.name}
                </Option>
              ))}
            </Select>
          </Form.Item>
          
          <Form.Item
            name="tenant_id"
            rules={[{ required: false, message: '请选择租户' }]}
          >
            <Select
              placeholder="选择租户（可选）"
              allowClear
              loading={loadingTenants}
              disabled={!selectedServiceProvider || loadingTenants}
              suffixIcon={<BankOutlined />}
            >
              {tenants.map(tenant => (
                <Option key={tenant.id} value={tenant.id}>
                  {tenant.name}
                </Option>
              ))}
            </Select>
          </Form.Item>
          
          <Form.Item
            name="username"
            rules={[
              { required: true, message: '请输入用户名' },
              { min: 3, message: '用户名至少3个字符' },
              { max: 20, message: '用户名最多20个字符' },
            ]}
          >
            <Input prefix={<UserOutlined />} placeholder="用户名" />
          </Form.Item>
          
          <Form.Item
            name="email"
            rules={[
              { required: true, message: '请输入邮箱' },
              { type: 'email', message: '请输入有效的邮箱地址' },
            ]}
          >
            <Input prefix={<MailOutlined />} placeholder="邮箱" />
          </Form.Item>
          
          <Form.Item
            name="password"
            rules={[
              { required: true, message: '请输入密码' },
              { min: 8, message: '密码至少8个字符' },
            ]}
            hasFeedback
          >
            <Input.Password prefix={<LockOutlined />} placeholder="密码" />
          </Form.Item>
          
          <Form.Item
            name="confirm"
            dependencies={['password']}
            hasFeedback
            rules={[
              { required: true, message: '请确认密码' },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (!value || getFieldValue('password') === value) {
                    return Promise.resolve();
                  }
                  return Promise.reject(new Error('两次输入的密码不一致'));
                },
              }),
            ]}
          >
            <Input.Password prefix={<LockOutlined />} placeholder="确认密码" />
          </Form.Item>
          
          <Form.Item
            name="full_name"
            rules={[{ required: true, message: '请输入姓名' }]}
          >
            <Input prefix={<UserOutlined />} placeholder="姓名" />
          </Form.Item>
          
          <Form.Item
            name="phone"
            rules={[{ required: false, message: '请输入手机号码' }]}
          >
            <Input prefix={<PhoneOutlined />} placeholder="手机号码（可选）" />
          </Form.Item>
          
          <Form.Item>
            <Button type="primary" htmlType="submit" loading={loading} block>
              注册
            </Button>
          </Form.Item>
          
          <Divider plain>或者</Divider>
          
          <div style={{ textAlign: 'center' }}>
            <Text type="secondary">已有账户？</Text>{' '}
            <Link to="/login">立即登录</Link>
          </div>
        </Form>
      </Card>
    </div>
  );
};

export default Register;
