#!/usr/bin/env python
# -*- coding: utf-8 -*-

import logging
import os
import uuid
import shutil
from typing import Dict, Any, Optional, List
from fastapi import UploadFile, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from datetime import datetime
import aiofiles
from pathlib import Path

from core.config import settings
from models.user import User

# 配置日志
logger = logging.getLogger(__name__)

# 文件上传配置
UPLOAD_DIR = Path(settings.UPLOAD_DIR)
MAX_FILE_SIZE = settings.MAX_FILE_SIZE  # 默认10MB
ALLOWED_IMAGE_TYPES = ["image/jpeg", "image/png", "image/gif", "image/webp"]
ALLOWED_DOCUMENT_TYPES = ["application/pdf", "application/msword", "application/vnd.openxmlformats-officedocument.wordprocessingml.document"]

class FileUploadService:
    @staticmethod
    async def save_file(
        file: UploadFile,
        user_id: uuid.UUID,
        project_id: uuid.UUID,
        module: str = "default",
        category: str = "other",
        description: str = None,
        db: AsyncSession = None
    ) -> Optional[Dict[str, Any]]:
        """
        保存上传的文件
        
        Args:
            file: 上传的文件
            user_id: 用户ID
            project_id: 项目ID
            module: 模块名称
            category: 文件类别 (image, document, other)
            description: 文件描述
            db: 数据库会话
            
        Returns:
            包含文件信息的字典
        """
        try:
            # 检查文件大小
            file_content = await file.read()
            file_size = len(file_content)
            
            if file_size > MAX_FILE_SIZE:
                raise HTTPException(
                    status_code=400,
                    detail=f"文件大小不能超过{MAX_FILE_SIZE/1024/1024}MB"
                )
            
            # 重置文件位置
            await file.seek(0)
            
            # 确定文件类型
            mimetype = file.content_type
            if not category or category == "other":
                if mimetype in ALLOWED_IMAGE_TYPES:
                    category = "image"
                elif mimetype in ALLOWED_DOCUMENT_TYPES:
                    category = "document"
                else:
                    category = "other"
            
            # 构建文件存储路径
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            filename = f"{timestamp}_{uuid.uuid4().hex[:8]}_{file.filename}"
            
            # 创建目录结构：uploads/项目ID/模块/用户ID/
            upload_dir = UPLOAD_DIR / str(project_id) / module / str(user_id)
            upload_dir.mkdir(parents=True, exist_ok=True)
            
            file_path = upload_dir / filename
            
            # 保存文件
            async with aiofiles.open(file_path, "wb") as f:
                await f.write(file_content)
            
            # 构建文件URL
            file_url = f"/api/project/{project_id}/files/{module}/{user_id}/{filename}"
            
            # 构建文件信息
            file_info = {
                "id": str(uuid.uuid4()),
                "filename": file.filename,
                "path": str(file_path),
                "url": file_url,
                "size": file_size,
                "mimetype": mimetype,
                "category": category,
                "upload_time": datetime.now().isoformat(),
                "user_id": str(user_id),
                "project_id": str(project_id),
                "module": module,
                "description": description or f"从{module}模块上传"
            }
            
            # 在实际应用中，这里应该将文件信息保存到数据库
            # 例如：
            # db_file = StorageFile(**file_info)
            # db.add(db_file)
            # await db.commit()
            # await db.refresh(db_file)
            
            logger.info(f"文件上传成功: {file_info['path']}")
            return file_info
        except Exception as e:
            logger.error(f"文件上传失败: {e}")
            # 如果文件已保存，但后续处理失败，则删除文件
            if 'file_path' in locals() and os.path.exists(file_path):
                os.remove(file_path)
            raise
