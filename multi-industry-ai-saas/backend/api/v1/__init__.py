#!/usr/bin/env python
# -*- coding: utf-8 -*-

from fastapi import APIRouter

#  业务层接口导入
from .auth import router as auth_router
from .service_provider import router as service_provider_router
from .tenant import router as tenant_router
from .project import router as project_router
from .industry import router as industry_router
from .space_share import router as space_share_router
from .shared_assistant import router as shared_assistant_router

# 导入租户信息路由
from routers.tenant import router as tenant_info_router

# 创建API路由器
api_router = APIRouter()

# 注册路由
api_router.include_router(auth_router, prefix="/auth", tags=["认证"])
api_router.include_router(service_provider_router, prefix="/service-provider", tags=["服务商"])
api_router.include_router(tenant_router, prefix="/tenant", tags=["租户"])
# 项目路由不添加标签，避免所有子路由都继承项目标签
api_router.include_router(project_router, prefix="/project")
api_router.include_router(industry_router, prefix="/industry", tags=["行业"])

# 注册分享助手路由
api_router.include_router(shared_assistant_router, tags=["分享助手"])

# 注册项目路由

# 注册租户路由
api_router.include_router(tenant_info_router)

# 注册文件分享路由 - 使用正确的路径前缀
api_router.include_router(space_share_router, prefix="/space/share", tags=["文件分享"])

