#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
业务流转 API
"""

import logging
import uuid
from typing import Dict, Any, Optional, List
from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel

from api.deps import get_db
from core.auth import get_current_user
from api.deps import get_current_project
from models.user import User
from models.project import Project
from ..services.customer_service import get_customer_service_manager

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter()

# 请求模型
class BusinessFlowCreateRequest(BaseModel):
    name: str
    description: Optional[str] = None
    trigger_intent: str
    target_type: str = "roles"  # "roles" 或 "users"
    target_roles: List[str] = []  # 目标角色列表
    target_users: List[str] = []  # 目标用户ID列表
    priority: str = "medium"
    auto_escalate: bool = True
    escalation_timeout_hours: int = 2
    notification_channels: List[str] = ["websocket", "email"]
    is_enabled: bool = True

class BusinessFlowUpdateRequest(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    trigger_intent: Optional[str] = None
    target_roles: Optional[List[str]] = None
    priority: Optional[str] = None
    auto_escalate: Optional[bool] = None
    escalation_timeout_hours: Optional[int] = None
    notification_channels: Optional[List[str]] = None
    is_enabled: Optional[bool] = None

# 响应模型
class BusinessFlowResponse(BaseModel):
    id: str
    name: str
    description: Optional[str]
    trigger_intent: str
    target_type: str = "roles"
    target_roles: List[str] = []
    target_users: List[str] = []
    priority: str
    auto_escalate: bool
    escalation_timeout_hours: int
    notification_channels: List[str]
    is_enabled: bool
    created_at: datetime
    updated_at: datetime

class BusinessFlowListResponse(BaseModel):
    flows: List[BusinessFlowResponse]
    total: int

@router.get(
    "",
    response_model=BusinessFlowListResponse,
    summary="获取业务流转列表",
    description="获取项目下的业务流转规则列表"
)
async def get_business_flows(
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """获取业务流转列表"""
    try:
        # 暂时返回模拟数据
        mock_flows = [
            BusinessFlowResponse(
                id=str(uuid.uuid4()),
                name="投诉处理流转",
                description="客户投诉自动转给客服经理和运营经理",
                trigger_intent="complaint",
                target_roles=["customer_service_manager", "operations_manager"],
                priority="high",
                auto_escalate=True,
                escalation_timeout_hours=1,
                notification_channels=["websocket", "email", "sms"],
                is_enabled=True,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            ),
            BusinessFlowResponse(
                id=str(uuid.uuid4()),
                name="合作咨询流转",
                description="合作相关咨询转给商务拓展和合作经理",
                trigger_intent="partnership",
                target_roles=["business_development", "partnership_manager"],
                priority="medium",
                auto_escalate=True,
                escalation_timeout_hours=4,
                notification_channels=["websocket", "email"],
                is_enabled=True,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            ),
            BusinessFlowResponse(
                id=str(uuid.uuid4()),
                name="加盟咨询流转",
                description="加盟相关咨询转给加盟经理和区域经理",
                trigger_intent="franchise",
                target_roles=["franchise_manager", "regional_manager"],
                priority="medium",
                auto_escalate=True,
                escalation_timeout_hours=2,
                notification_channels=["websocket", "email", "push"],
                is_enabled=True,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            ),
            BusinessFlowResponse(
                id=str(uuid.uuid4()),
                name="技术支持流转",
                description="技术问题转给技术支持和高级工程师",
                trigger_intent="technical_support",
                target_roles=["technical_support", "senior_engineer"],
                priority="high",
                auto_escalate=False,
                escalation_timeout_hours=0,
                notification_channels=["websocket"],
                is_enabled=False,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
        ]
        
        return BusinessFlowListResponse(
            flows=mock_flows,
            total=len(mock_flows)
        )
        
    except Exception as e:
        logger.error(f"获取业务流转列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取业务流转列表失败: {str(e)}"
        )

@router.post(
    "",
    response_model=BusinessFlowResponse,
    status_code=status.HTTP_201_CREATED,
    summary="创建业务流转",
    description="创建新的业务流转规则"
)
async def create_business_flow(
    flow_data: BusinessFlowCreateRequest,
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """创建业务流转"""
    try:
        # 暂时返回模拟数据
        new_flow = BusinessFlowResponse(
            id=str(uuid.uuid4()),
            name=flow_data.name,
            description=flow_data.description,
            trigger_intent=flow_data.trigger_intent,
            target_roles=flow_data.target_roles,
            priority=flow_data.priority,
            auto_escalate=flow_data.auto_escalate,
            escalation_timeout_hours=flow_data.escalation_timeout_hours,
            notification_channels=flow_data.notification_channels,
            is_enabled=flow_data.is_enabled,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        
        return new_flow
        
    except Exception as e:
        logger.error(f"创建业务流转失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建业务流转失败: {str(e)}"
        )

@router.put(
    "/{flow_id}",
    response_model=BusinessFlowResponse,
    summary="更新业务流转",
    description="更新指定的业务流转规则"
)
async def update_business_flow(
    flow_id: str,
    flow_data: BusinessFlowUpdateRequest,
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """更新业务流转"""
    try:
        # 暂时返回模拟数据
        updated_flow = BusinessFlowResponse(
            id=flow_id,
            name=flow_data.name or "更新的流转规则",
            description=flow_data.description,
            trigger_intent=flow_data.trigger_intent or "complaint",
            target_roles=flow_data.target_roles or ["customer_service_manager"],
            priority=flow_data.priority or "medium",
            auto_escalate=flow_data.auto_escalate if flow_data.auto_escalate is not None else True,
            escalation_timeout_hours=flow_data.escalation_timeout_hours or 2,
            notification_channels=flow_data.notification_channels or ["websocket", "email"],
            is_enabled=flow_data.is_enabled if flow_data.is_enabled is not None else True,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        
        return updated_flow
        
    except Exception as e:
        logger.error(f"更新业务流转失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新业务流转失败: {str(e)}"
        )

@router.delete(
    "/{flow_id}",
    summary="删除业务流转",
    description="删除指定的业务流转规则"
)
async def delete_business_flow(
    flow_id: str,
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """删除业务流转"""
    try:
        return {"success": True, "message": "业务流转删除成功"}
        
    except Exception as e:
        logger.error(f"删除业务流转失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除业务流转失败: {str(e)}"
        )
