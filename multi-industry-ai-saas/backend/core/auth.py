#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
认证相关功能
"""
# -*- coding: utf-8 -*-

import logging
from typing import Optional, Dict, Any, List, Union
from datetime import datetime, timedelta
from fastapi import Depends, HTTPException, status, Request
from fastapi.security import OAuth2PasswordBearer
from jose import JWTError, jwt
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from pydantic import BaseModel

from core.config import settings
from db.database import get_db
from models.user import User
from core.tenant_context import TenantContext, set_tenant_context, get_tenant_context

# 配置日志
logger = logging.getLogger(__name__)

# OAuth2密码Bearer
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/api/v1/auth/token")


class TokenData(BaseModel):
    """令牌数据模型"""
    sub: str  # 用户名
    user_id: str  # 用户ID
    service_provider_id: Optional[str] = None  # 服务商ID
    tenant_id: Optional[str] = None  # 租户ID
    project_id: Optional[str] = None  # 项目ID
    role: Optional[str] = None  # 角色
    permissions: Optional[Dict[str, bool]] = None  # 权限
    is_system_admin: bool = False  # 是否系统管理员
    is_super_admin: bool = False  # 是否超级管理员(与is_system_admin同义，更明确的命名)
    is_service_provider_admin: bool = False  # 是否服务商管理员
    is_tenant_admin: bool = False  # 是否租户管理员
    exp: Optional[datetime] = None  # 过期时间


def create_access_token(data: Dict[str, Any], expires_delta: Optional[timedelta] = None) -> str:
    """
    创建访问令牌
    """
    to_encode = data.copy()

    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=settings.ACCESS_TOKEN_EXPIRE_MINUTES)

    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM)

    return encoded_jwt


async def get_current_user(token: str = Depends(oauth2_scheme), db: AsyncSession = Depends(get_db)) -> User:
    """
    获取当前用户
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="无效的认证凭据",
        headers={"WWW-Authenticate": "Bearer"},
    )

    def is_valid_uuid(val):
        import uuid
        try:
            uuid.UUID(str(val))
            return True
        except Exception:
            return False

    try:
        # 解码JWT令牌
        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
        username = payload.get("sub")
        user_id = payload.get("user_id")
        token_type = payload.get("type")

        # 特殊处理 system_instance_token
        if token_type == "system_instance_token" and user_id == "mcp_system_user":
            logger.info(f"系统实例令牌验证通过: {username} (mcp_system_user)")
            # 为系统实例令牌创建一个临时的User对象，或赋予其足够的权限信息
            # 注意：这个User对象不会与数据库交互，仅用于权限检查
            system_user = User(
                id=user_id, # 使用 "mcp_system_user" 作为ID
                username=username,
                email="<EMAIL>", # 虚拟邮箱
                password_hash="N/A", # 无需密码哈希
                status="active",
                is_system_admin=True, # 假设系统实例令牌拥有系统管理员权限
                is_super_admin=True,  # 假设系统实例令牌拥有系统管理员权限
                role="system_instance",
                permissions=payload.get("permissions", {"tools:read": True}) # 从payload获取或赋予默认权限
            )
            # 如果有租户/项目信息，也可以设置到这个临时用户对象上或上下文中
            tenant_id = payload.get("tenant_id")
            service_provider_id = payload.get("service_provider_id")
            project_id = payload.get("project_id")
            if tenant_id:
                context = TenantContext(
                    tenant_id=tenant_id,
                    service_provider_id=service_provider_id,
                    project_id=project_id,
                )
                set_tenant_context(context)
            return system_user

        # 常规用户令牌验证
        if username is None or user_id is None or not is_valid_uuid(user_id):
            logger.error(f"user_id 非法: {user_id}")
            raise credentials_exception

        # 构建令牌数据
        token_data = TokenData(
            sub=username,
            user_id=user_id,
            service_provider_id=payload.get("service_provider_id"),
            tenant_id=payload.get("tenant_id"),
            project_id=payload.get("project_id"),
            role=payload.get("role"),
            permissions=payload.get("permissions", {}),
            is_system_admin=bool(payload.get("is_system_admin", False) or False),
            is_super_admin=bool(payload.get("is_super_admin", False) or payload.get("is_system_admin", False) or False),
            is_service_provider_admin=bool(payload.get("is_service_provider_admin", False) or False),
            is_tenant_admin=bool(payload.get("is_tenant_admin", False) or False),
        )
    except JWTError:
        logger.error("JWT解码失败", exc_info=True)
        raise credentials_exception

    # 查询用户
    result = await db.execute(select(User).where(User.id == user_id))
    user = result.scalars().first()

    if user is None:
        logger.warning(f"用户不存在: {user_id}")
        raise credentials_exception

    if user.status != "active":
        logger.warning(f"用户状态非活动: {user.username}, 状态: {user.status}")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="用户账户已被禁用",
        )

    # 设置租户上下文
    if token_data.tenant_id:
        context = TenantContext(
            tenant_id=token_data.tenant_id,
            service_provider_id=token_data.service_provider_id,
            project_id=token_data.project_id,
        )
        set_tenant_context(context)

    return user


async def get_current_active_user(current_user: User = Depends(get_current_user)) -> User:
    """
    获取当前活动用户
    """
    if current_user.status != "active":
        raise HTTPException(status_code=400, detail="用户账户已被禁用")
    return current_user


async def get_current_system_admin(current_user: User = Depends(get_current_user)) -> User:
    """
    获取当前系统管理员
    """
    if not current_user.is_system_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要系统管理员权限",
        )
    return current_user


async def get_current_service_provider_admin(current_user: User = Depends(get_current_user)) -> User:
    """
    获取当前服务商管理员
    """
    if not (current_user.is_system_admin or current_user.is_service_provider_admin):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要服务商管理员权限",
        )
    return current_user


async def get_current_tenant_admin(current_user: User = Depends(get_current_user)) -> User:
    """
    获取当前租户管理员
    """
    if not (current_user.is_system_admin or current_user.is_service_provider_admin or current_user.is_tenant_admin):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="需要租户管理员权限",
        )
    return current_user


def check_permissions(required_permissions: Union[str, List[str]]):
    """
    检查权限装饰器
    """
    if isinstance(required_permissions, str):
        required_permissions = [required_permissions]

    async def permission_checker(current_user: User = Depends(get_current_user)):
        # 系统管理员拥有所有权限
        if current_user.is_system_admin:
            return current_user

        # 检查用户权限
        user_permissions = current_user.permissions or {}
        for permission in required_permissions:
            if not user_permissions.get(permission, False):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"需要权限: {permission}",
                )

        return current_user

    return permission_checker


async def authenticate_user(db: AsyncSession, username: str, password: str) -> Optional[User]:
    """
    验证用户
    """
    result = await db.execute(select(User).where(User.username == username))
    user = result.scalars().first()

    if not user:
        return None

    if not user.verify_password(password):
        return None

    return user


async def get_current_user_optional(token: Optional[str] = Depends(oauth2_scheme), db: AsyncSession = Depends(get_db)) -> Optional[User]:
    """
    获取当前用户（可选）

    如果提供了有效的令牌，则返回用户对象，否则返回None
    """
    if not token:
        return None

    try:
        # 解码JWT令牌
        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
        username = payload.get("sub")
        user_id = payload.get("user_id")

        if username is None or user_id is None:
            return None

        # 查询用户
        result = await db.execute(select(User).where(User.id == user_id))
        user = result.scalars().first()

        if user is None or user.status != "active":
            return None

        # 设置租户上下文
        tenant_id = payload.get("tenant_id")
        if tenant_id:
            context = TenantContext(
                tenant_id=tenant_id,
                service_provider_id=payload.get("service_provider_id"),
                project_id=payload.get("project_id"),
            )
            set_tenant_context(context)

        return user
    except JWTError:
        return None


async def get_current_tenant(current_user: User = Depends(get_current_user), db: AsyncSession = Depends(get_db)):
    """
    获取当前租户

    基于当前用户的租户上下文，返回租户对象
    """
    from models.tenant import Tenant

    # 获取租户上下文
    context = get_tenant_context()
    if not context or not context.tenant_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="未找到租户上下文",
        )

    # 查询租户
    result = await db.execute(select(Tenant).where(Tenant.id == context.tenant_id))
    tenant = result.scalars().first()

    if not tenant:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="租户不存在",
        )

    if tenant.status != "active":
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="租户已被禁用",
        )

    return tenant


async def get_current_user_ws(token: str, db: AsyncSession) -> User:
    """
    WebSocket认证：获取当前用户

    Args:
        token: JWT令牌
        db: 数据库会话

    Returns:
        User: 用户对象

    Raises:
        HTTPException: 如果认证失败
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="无效的认证凭据",
        headers={"WWW-Authenticate": "Bearer"},
    )

    try:
        # 记录令牌信息（不记录完整令牌，只记录前10个字符）
        token_prefix = token[:10] if len(token) > 10 else token
        logger.debug(f"WebSocket认证: 尝试验证令牌 {token_prefix}...")

        # 解码JWT令牌
        payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
        username = payload.get("sub")
        user_id = payload.get("user_id")

        if username is None or user_id is None:
            logger.warning(f"WebSocket认证失败: 令牌缺少必要字段 (username={username}, user_id={user_id})")
            raise credentials_exception

        # 查询用户
        result = await db.execute(select(User).where(User.id == user_id))
        user = result.scalars().first()

        if user is None:
            logger.warning(f"WebSocket认证失败: 未找到用户 (user_id={user_id})")
            raise credentials_exception

        if user.status != "active":
            logger.warning(f"WebSocket认证失败: 用户账户已被禁用 (user_id={user_id})")
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="用户账户已被禁用",
            )

        # 设置租户上下文
        tenant_id = payload.get("tenant_id")
        if tenant_id:
            context = TenantContext(
                tenant_id=tenant_id,
                service_provider_id=payload.get("service_provider_id"),
                project_id=payload.get("project_id"),
            )
            set_tenant_context(context)

        logger.debug(f"WebSocket认证成功: 用户 {user.username} (id={user.id})")
        return user

    except JWTError as e:
        logger.warning(f"WebSocket认证失败: JWT解码错误 - {str(e)}")
        raise credentials_exception
    except Exception as e:
        logger.error(f"WebSocket认证失败: 未预期的错误 - {str(e)}")
        raise credentials_exception
