#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
钉钉回调加解密工具类
基于钉钉官方提供的加解密算法实现
支持HTTP推送模式的数据加解密和签名验证
"""

import base64
import hashlib
import hmac
import json
import logging
import struct
import time
from typing import Dict, Any, Optional, Tuple
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.primitives import padding
from cryptography.hazmat.backends import default_backend
import secrets

logger = logging.getLogger(__name__)


class DingTalkCallbackCrypto:
    """
    钉钉回调加解密类
    
    用于处理钉钉HTTP推送模式的事件订阅数据加解密
    基于钉钉官方加解密算法标准实现
    """
    
    def __init__(self, token: str, aes_key: str, owner_key: str):
        """
        初始化加解密工具
        
        Args:
            token: 钉钉应用的签名Token
            aes_key: 钉钉应用的数据加密密钥（EncodingAESKey）
            owner_key: 钉钉应用的所有者密钥
                      - 企业级事件推送：应用的Client ID（即appKey）
                      - 三方应用回调：suiteKey
        """
        self.token = token
        self.aes_key = base64.b64decode(aes_key + "=")  # 补齐base64填充
        self.owner_key = owner_key
        
        logger.debug(f"初始化钉钉加解密工具:")
        logger.debug(f"  - Token: {token}")
        logger.debug(f"  - AES Key长度: {len(self.aes_key)}字节")
        logger.debug(f"  - Owner Key: {owner_key}")
        logger.debug(f"  - Owner Key类型: {'appKey(企业级事件)' if len(owner_key) > 15 else 'suiteKey(三方应用)'}")
    
    def get_decrypt_msg(self, msg_signature: str, timestamp: str, nonce: str, encrypt_msg: str) -> str:
        """
        解密钉钉推送的加密消息
        
        Args:
            msg_signature: 消息签名
            timestamp: 时间戳
            nonce: 随机字符串
            encrypt_msg: 加密的消息内容
            
        Returns:
            str: 解密后的消息内容
            
        Raises:
            ValueError: 签名验证失败或解密失败
        """
        logger.debug(f"开始解密消息，encrypt_msg长度: {len(encrypt_msg)}")
        
        # 1. 验证签名
        if not self._verify_signature(msg_signature, timestamp, nonce, encrypt_msg):
            raise ValueError("消息签名验证失败")
        
        # 2. 解密消息
        try:
            decrypted_msg = self._decrypt(encrypt_msg)
            logger.info("消息解密成功")
            return decrypted_msg
        except Exception as e:
            logger.error(f"消息解密失败: {e}")
            raise ValueError(f"消息解密失败: {e}")
    
    def get_encrypted_map(self, msg: str) -> Dict[str, str]:
        """
        加密响应消息
        
        Args:
            msg: 要加密的消息内容（通常是"success"）
            
        Returns:
            Dict[str, str]: 包含加密消息的字典
        """
        timestamp = str(int(time.time()))
        nonce = self._generate_nonce()
        
        # 加密消息
        encrypted_msg = self._encrypt(msg)
        
        # 生成签名
        signature = self._generate_signature(timestamp, nonce, encrypted_msg)
        
        return {
            "msg_signature": signature,
            "timeStamp": timestamp,
            "nonce": nonce,
            "encrypt": encrypted_msg
        }
    
    def _verify_signature(self, msg_signature: str, timestamp: str, nonce: str, encrypt_msg: str) -> bool:
        """
        验证消息签名
        
        Args:
            msg_signature: 消息签名
            timestamp: 时间戳
            nonce: 随机字符串
            encrypt_msg: 加密消息
            
        Returns:
            bool: 签名是否有效
        """
        expected_signature = self._generate_signature(timestamp, nonce, encrypt_msg)
        
        # 记录签名验证的详细信息
        logger.debug(f"签名验证详情:")
        logger.debug(f"  - 接收到的签名: {msg_signature}")
        logger.debug(f"  - 计算出的签名: {expected_signature}")
        logger.debug(f"  - token: {self.token}")
        logger.debug(f"  - timestamp: {timestamp}")
        logger.debug(f"  - nonce: {nonce}")
        logger.debug(f"  - encrypt_msg前50字符: {encrypt_msg[:50]}...")
        
        is_valid = hmac.compare_digest(msg_signature, expected_signature)
        logger.debug(f"  - 签名验证结果: {is_valid}")
        
        return is_valid
    
    def _generate_signature(self, timestamp: str, nonce: str, encrypt_msg: str) -> str:
        """
        生成消息签名
        
        Args:
            timestamp: 时间戳
            nonce: 随机字符串
            encrypt_msg: 加密消息
            
        Returns:
            str: 生成的签名
        """
        # 按字典序排序
        sort_list = [self.token, timestamp, nonce, encrypt_msg]
        sort_list.sort()
        
        # 拼接字符串
        content = ''.join(sort_list)
        
        # SHA1哈希
        sha1 = hashlib.sha1()
        sha1.update(content.encode('utf-8'))
        
        return sha1.hexdigest()
    
    def _decrypt(self, encrypt_msg: str) -> str:
        """
        解密消息 - 基于钉钉官方标准算法
        
        钉钉加密消息结构：
        1. Base64解码后得到密文
        2. 使用AES密钥的前16字节作为IV
        3. 使用AES/CBC/NoPadding模式解密
        4. 解密后的数据结构：16字节随机值 + 4字节消息长度 + 消息内容 + corp_id
        5. 手动移除PKCS7填充
        
        Args:
            encrypt_msg: base64编码的加密消息
            
        Returns:
            str: 解密后的消息
        """
        try:
            logger.debug(f"开始解密，原始encrypt_msg: {encrypt_msg}")
            
            # 1. Base64解码
            cipher_text = base64.b64decode(encrypt_msg)
            logger.debug(f"Base64解码后长度: {len(cipher_text)}字节")
            
            # 2. 使用AES密钥的前16字节作为IV（钉钉标准做法）
            iv = self.aes_key[:16]
            logger.debug(f"使用IV: {iv.hex()}")
            
            # 3. AES解密 - 使用CBC模式，NoPadding（后续手动处理填充）
            cipher = Cipher(
                algorithms.AES(self.aes_key),
                modes.CBC(iv),
                backend=default_backend()
            )
            decryptor = cipher.decryptor()
            decrypted = decryptor.update(cipher_text) + decryptor.finalize()
            logger.debug(f"AES解密后长度: {len(decrypted)}字节")
            
            # 4. 手动移除PKCS7填充
            try:
                # 获取最后一个字节作为填充长度
                pad_length = decrypted[-1]
                logger.debug(f"填充长度: {pad_length}")
                
                # 验证填充的有效性
                if pad_length < 1 or pad_length > 32:
                    logger.warning(f"填充长度异常: {pad_length}，尝试继续处理")
                    pad_length = 0
                else:
                    # 验证填充字节是否正确
                    padding_bytes = decrypted[-pad_length:]
                    if all(b == pad_length for b in padding_bytes):
                        decrypted = decrypted[:-pad_length]
                        logger.debug(f"移除填充后长度: {len(decrypted)}字节")
                    else:
                        logger.warning("填充字节验证失败，尝试不移除填充继续处理")
                        pad_length = 0
                        
            except Exception as e:
                logger.warning(f"填充处理失败: {e}，尝试继续处理")
            
            # 5. 解析消息结构：16字节随机值 + 4字节消息长度 + 消息内容 + corp_id
            if len(decrypted) < 20:  # 至少需要16+4字节
                raise ValueError(f"解密后数据长度不足: {len(decrypted)}字节")
                
            # 跳过前16字节的随机值
            data_start = 16
            
            # 解析消息长度（大端序）
            msg_len_bytes = decrypted[data_start:data_start+4]
            msg_len = struct.unpack('>I', msg_len_bytes)[0]  # 使用大端序
            logger.debug(f"消息长度: {msg_len}字节")
            
            # 提取消息内容
            msg_start = data_start + 4
            msg_end = msg_start + msg_len
            
            if msg_end > len(decrypted):
                raise ValueError(f"消息长度超出数据范围: {msg_len} > {len(decrypted) - msg_start}")
                
            msg_content = decrypted[msg_start:msg_end].decode('utf-8')
            logger.debug(f"消息内容: {msg_content[:100]}...")
            
            # 提取ownerKey（剩余部分）
            # 根据钉钉官方文档：
            # - 企业回调：ownerKey = corpId
            # - 三方应用回调：ownerKey = suiteKey
            owner_key_bytes = decrypted[msg_end:]
            if owner_key_bytes:
                try:
                    from_owner_key = owner_key_bytes.decode('utf-8').rstrip('\x00')  # 移除可能的null字符
                    logger.debug(f"🔍 解析出的ownerKey: '{from_owner_key}'")
                    logger.debug(f"🔍 配置中的owner_key: '{self.owner_key}'")
                    logger.debug(f"🔍 ownerKey长度: {len(from_owner_key)}, owner_key长度: {len(self.owner_key)}")
                    
                    # 验证ownerKey
                    if from_owner_key != self.owner_key:
                        logger.warning(f"⚠️ OwnerKey不匹配:")
                        logger.warning(f"   期望(owner_key): '{self.owner_key}'")
                        logger.warning(f"   实际(解密得到): '{from_owner_key}'")
                        logger.warning(f"   这可能是因为:")
                        logger.warning(f"   1. 配置的owner_key不正确")
                        logger.warning(f"   2. 解密得到的是app_key而非owner_key")
                        logger.warning(f"   3. 企业有多个owner_key")
                        logger.warning(f"   4. 三方应用应该使用suiteKey而非owner_key")
                        
                        # 继续处理，但记录不匹配情况
                        logger.info("🔄 OwnerKey不匹配，但继续处理解密内容")
                    else:
                        logger.info(f"✅ OwnerKey验证通过: {from_owner_key}")
                        
                except UnicodeDecodeError as e:
                    logger.error(f"❌ OwnerKey解码失败: {e}")
                    logger.error(f"原始字节: {owner_key_bytes}")
                    from_owner_key = "解码失败"
            else:
                logger.warning("⚠️ 未找到ownerKey数据")
                from_owner_key = "未找到"
            
            logger.info(f"🎉 解密成功完成")
            logger.info(f"📊 解密统计:")
            logger.info(f"   - 随机值长度: 16字节")
            logger.info(f"   - 消息长度: {msg_len}字节")
            logger.info(f"   - 消息内容长度: {len(msg_content)}字符")
            logger.info(f"   - OwnerKey: '{from_owner_key}'")
            
            return msg_content
            
        except Exception as e:
            logger.error(f"❌ 解密过程发生错误: {e}", exc_info=True)
            raise ValueError(f"解密失败: {str(e)}")
    
    def _encrypt(self, msg: str) -> str:
        """
        加密消息 - 基于钉钉官方标准算法
        
        Args:
            msg: 要加密的消息
            
        Returns:
            str: base64编码的加密消息
        """
        try:
            # 1. 生成16字节随机值
            random_bytes = secrets.token_bytes(16)
            
            # 2. 构造消息结构：16字节随机值 + 4字节消息长度 + 消息内容 + corp_id
            msg_bytes = msg.encode('utf-8')
            msg_len = struct.pack('>I', len(msg_bytes))  # 大端序
            corp_id_bytes = self.owner_key.encode('utf-8')
            
            plain_text = random_bytes + msg_len + msg_bytes + corp_id_bytes
            
            # 3. PKCS7填充到32字节边界
            pad_length = 32 - (len(plain_text) % 32)
            if pad_length == 0:
                pad_length = 32
            padding_bytes = bytes([pad_length] * pad_length)
            padded_data = plain_text + padding_bytes
            
            # 4. 使用AES密钥的前16字节作为IV
            iv = self.aes_key[:16]
            
            # 5. AES加密
            cipher = Cipher(
                algorithms.AES(self.aes_key),
                modes.CBC(iv),
                backend=default_backend()
            )
            encryptor = cipher.encryptor()
            encrypted = encryptor.update(padded_data) + encryptor.finalize()
            
            # 6. Base64编码
            return base64.b64encode(encrypted).decode('utf-8')
            
        except Exception as e:
            logger.error(f"加密过程出错: {e}")
            raise
    
    def _generate_nonce(self) -> str:
        """
        生成随机字符串
        
        Returns:
            str: 16位随机字符串
        """
        import string
        
        chars = string.ascii_letters + string.digits
        return ''.join(secrets.choice(chars) for _ in range(16))


class DingTalkEventValidator:
    """
    钉钉事件验证器
    
    用于验证和处理钉钉事件订阅的各种验证场景
    """
    
    @staticmethod
    def validate_url_check(event_data: Dict[str, Any]) -> bool:
        """
        验证URL检查事件
        
        Args:
            event_data: 事件数据
            
        Returns:
            bool: 是否为URL检查事件
        """
        return event_data.get('EventType') == 'check_url'
    
    @staticmethod
    def extract_challenge(event_data: Dict[str, Any]) -> Optional[str]:
        """
        提取URL验证的challenge值
        
        Args:
            event_data: 事件数据
            
        Returns:
            Optional[str]: challenge值
        """
        if DingTalkEventValidator.validate_url_check(event_data):
            return event_data.get('challenge')
        return None
    
    @staticmethod
    def validate_event_structure(event_data: Dict[str, Any]) -> Tuple[bool, str]:
        """
        验证事件数据结构
        
        Args:
            event_data: 事件数据
            
        Returns:
            Tuple[bool, str]: (是否有效, 错误信息)
        """
        required_fields = ['EventType']
        
        for field in required_fields:
            if field not in event_data:
                return False, f"缺少必需字段: {field}"
        
        event_type = event_data.get('EventType')
        if not isinstance(event_type, str):
            return False, "EventType必须为字符串类型"
        
        return True, ""
    
    @staticmethod
    def get_event_info(event_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        提取事件基本信息
        
        Args:
            event_data: 事件数据
            
        Returns:
            Dict[str, Any]: 事件信息
        """
        return {
            'event_type': event_data.get('EventType'),
            'corp_id': event_data.get('CorpId'),
            'event_time': event_data.get('EventTime'),
            'event_id': event_data.get('EventId'),
            'user_id': event_data.get('UserId'),
            'staff_id': event_data.get('StaffId'),
        }


def create_crypto_instance(webhook_config: Dict[str, Any]) -> DingTalkCallbackCrypto:
    """
    创建加解密实例的工厂函数
    
    Args:
        webhook_config: webhook配置信息
        
    Returns:
        DingTalkCallbackCrypto: 加解密实例
        
    Raises:
        ValueError: 配置信息不完整
    """
    required_fields = ['token', 'aes_key', 'owner_key']
    
    for field in required_fields:
        if field not in webhook_config or not webhook_config[field]:
            raise ValueError(f"缺少必需的配置字段: {field}")
    
    return DingTalkCallbackCrypto(
        token=webhook_config['token'],
        aes_key=webhook_config['aes_key'],
        owner_key=webhook_config['owner_key']
    )


def validate_webhook_config(config: Dict[str, Any]) -> Tuple[bool, str]:
    """
    验证webhook配置的完整性
    
    Args:
        config: 配置字典
        
    Returns:
        Tuple[bool, str]: (是否有效, 错误信息)
    """
    required_fields = {
        'token': '签名Token',
        'aes_key': 'AES加密密钥',
        'owner_key': '所有者密钥'
    }
    
    for field, name in required_fields.items():
        if field not in config:
            return False, f"缺少{name}配置"
        
        value = config[field]
        if not value or not isinstance(value, str):
            return False, f"{name}配置无效"
        
        # 特定字段验证
        if field == 'aes_key' and len(value.strip()) != 43:
            return False, f"AES密钥长度必须为43字符，当前为{len(value.strip())}字符"
    
    return True, ""


# 使用示例
if __name__ == "__main__":
    # 示例配置
    config = {
        'token': 'your_token_here',
        'aes_key': 'your_43_char_aes_key_here',
        'owner_key': 'your_owner_key_here'
    }
    
    try:
        crypto = create_crypto_instance(config)
        
        # 加密响应示例
        response = crypto.get_encrypted_map("success")
        print("加密响应:", json.dumps(response, indent=2))
        
    except Exception as e:
        print(f"示例执行失败: {e}") 