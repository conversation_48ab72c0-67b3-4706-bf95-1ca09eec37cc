#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
将 sapi_mcp_servers 表的 transport_type 列转换为 transport_types 列的迁移脚本
"""

import logging
import json
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession

logger = logging.getLogger(__name__)

async def migrate(db: AsyncSession) -> bool:
    """
    将 sapi_mcp_servers 表的 transport_type 列转换为 transport_types 列
    
    Args:
        db: 数据库会话
        
    Returns:
        bool: 迁移是否成功
    """
    try:
        # 检查表是否存在
        result = await db.execute(text("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables
                WHERE table_name = 'sapi_mcp_servers'
            );
        """))
        
        table_exists = result.scalar()
        
        if not table_exists:
            logger.warning("sapi_mcp_servers 表不存在，无法转换 transport_type 列")
            return True
            
        # 检查 transport_type 列是否存在
        result = await db.execute(text("""
            SELECT EXISTS (
                SELECT FROM information_schema.columns
                WHERE table_name = 'sapi_mcp_servers' AND column_name = 'transport_type'
            );
        """))
        
        transport_type_exists = result.scalar()
        
        # 检查 transport_types 列是否存在
        result = await db.execute(text("""
            SELECT EXISTS (
                SELECT FROM information_schema.columns
                WHERE table_name = 'sapi_mcp_servers' AND column_name = 'transport_types'
            );
        """))
        
        transport_types_exists = result.scalar()
        
        # 如果 transport_type 存在但 transport_types 不存在，添加 transport_types 列
        if transport_type_exists and not transport_types_exists:
            # 添加 transport_types 列
            await db.execute(text("""
                ALTER TABLE sapi_mcp_servers
                ADD COLUMN transport_types JSONB DEFAULT '["streamhttp"]'::jsonb;
            """))
            
            # 将 transport_type 的值转换为 transport_types 数组
            await db.execute(text("""
                UPDATE sapi_mcp_servers
                SET transport_types = jsonb_build_array(transport_type);
            """))
            
            await db.commit()
            logger.info("成功将 transport_type 列转换为 transport_types 列")
        elif not transport_type_exists and not transport_types_exists:
            # 如果两个列都不存在，添加 transport_types 列
            await db.execute(text("""
                ALTER TABLE sapi_mcp_servers
                ADD COLUMN transport_types JSONB DEFAULT '["streamhttp"]'::jsonb;
            """))
            
            await db.commit()
            logger.info("成功添加 transport_types 列")
        elif transport_types_exists:
            logger.info("transport_types 列已存在，无需转换")
            
        return True
            
    except Exception as e:
        await db.rollback()
        logger.error(f"转换 transport_type 列失败: {str(e)}")
        raise
