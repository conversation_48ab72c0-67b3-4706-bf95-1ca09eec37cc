#!/usr/bin/env python
# -*- coding: utf-8 -*-

import logging
import uuid
import json
from typing import Dict, Any, List, Optional
from datetime import datetime
from sqlalchemy.orm import Session
from sqlalchemy import select, and_

from models.user import User, ThirdPartyAccount
from models.project import Project
from models.knowledge_base import KnowledgeCategory, KnowledgeDocument
from models.workflow import Workflow, WorkflowInstance
from services.knowledge_base import KnowledgeBaseService
# from services.workflow import WorkflowService  # 暂时注释，服务可能不存在
from .dingtalk_api import DingTalkAPI

logger = logging.getLogger(__name__)

class DingTalkAITools:
    """钉钉AI工具集成类"""
    
    def __init__(self, dingtalk_api: DingTalkAPI, db: Session, project: Project, user: User):
        self.dingtalk_api = dingtalk_api
        self.db = db
        self.project = project
        self.user = user
    
    async def get_available_tools(self) -> List[Dict[str, Any]]:
        """获取可用的AI工具列表"""
        tools = [
            {
                "name": "search_knowledge_base",
                "description": "搜索项目知识库获取相关信息",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "query": {"type": "string", "description": "搜索关键词"},
                        "category_id": {"type": "string", "description": "知识库分类ID（可选）"}
                    },
                    "required": ["query"]
                }
            },
            {
                "name": "send_dingtalk_message",
                "description": "发送消息到钉钉用户或群组",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "target_type": {"type": "string", "enum": ["user", "group"], "description": "目标类型"},
                        "target_id": {"type": "string", "description": "目标ID"},
                        "message": {"type": "string", "description": "消息内容"},
                        "message_type": {"type": "string", "enum": ["text", "card"], "description": "消息类型"}
                    },
                    "required": ["target_type", "target_id", "message"]
                }
            },
            {
                "name": "get_dingtalk_user_info",
                "description": "获取钉钉用户信息",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "user_id": {"type": "string", "description": "钉钉用户ID"}
                    },
                    "required": ["user_id"]
                }
            },
            {
                "name": "get_dingtalk_department_users",
                "description": "获取钉钉部门用户列表",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "dept_id": {"type": "integer", "description": "部门ID"}
                    },
                    "required": ["dept_id"]
                }
            },
            {
                "name": "create_knowledge_document",
                "description": "创建知识库文档",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "title": {"type": "string", "description": "文档标题"},
                        "content": {"type": "string", "description": "文档内容"},
                        "category_id": {"type": "string", "description": "分类ID（可选）"}
                    },
                    "required": ["title", "content"]
                }
            },
            {
                "name": "trigger_workflow",
                "description": "触发工作流执行",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "workflow_id": {"type": "string", "description": "工作流ID"},
                        "input_data": {"type": "object", "description": "输入数据"}
                    },
                    "required": ["workflow_id"]
                }
            },
            {
                "name": "get_project_workflows",
                "description": "获取项目工作流列表",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "status": {"type": "string", "description": "工作流状态（可选）"}
                    }
                }
            }
        ]
        return tools
    
    async def execute_tool(self, tool_name: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """执行AI工具"""
        try:
            if tool_name == "search_knowledge_base":
                return await self._search_knowledge_base(parameters)
            elif tool_name == "send_dingtalk_message":
                return await self._send_dingtalk_message(parameters)
            elif tool_name == "get_dingtalk_user_info":
                return await self._get_dingtalk_user_info(parameters)
            elif tool_name == "get_dingtalk_department_users":
                return await self._get_dingtalk_department_users(parameters)
            elif tool_name == "create_knowledge_document":
                return await self._create_knowledge_document(parameters)
            elif tool_name == "trigger_workflow":
                return await self._trigger_workflow(parameters)
            elif tool_name == "get_project_workflows":
                return await self._get_project_workflows(parameters)
            else:
                return {"success": False, "error": f"未知工具: {tool_name}"}
        except Exception as e:
            logger.error(f"执行AI工具 {tool_name} 失败: {str(e)}")
            return {"success": False, "error": str(e)}
    
    async def _search_knowledge_base(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """搜索知识库"""
        query = parameters.get("query")
        category_id = parameters.get("category_id")
        
        try:
            documents, total = await KnowledgeBaseService.get_documents(
                db=self.db,
                project_id=self.project.id,
                search=query,
                category_id=uuid.UUID(category_id) if category_id else None,
                limit=10
            )
            
            results = []
            for doc in documents:
                results.append({
                    "id": str(doc["id"]),
                    "title": doc["title"],
                    "content": doc["content"][:500] + "..." if len(doc["content"]) > 500 else doc["content"],
                    "category_name": doc.get("category_name"),
                    "created_at": doc["created_at"]
                })
            
            return {
                "success": True,
                "data": {
                    "documents": results,
                    "total": total,
                    "query": query
                }
            }
        except Exception as e:
            return {"success": False, "error": f"搜索知识库失败: {str(e)}"}
    
    async def _send_dingtalk_message(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """发送钉钉消息"""
        target_type = parameters.get("target_type")
        target_id = parameters.get("target_id")
        message = parameters.get("message")
        message_type = parameters.get("message_type", "text")
        
        try:
            if message_type == "card":
                result = await self.dingtalk_api.send_ai_response_card(
                    target_type=target_type,
                    target_id=target_id,
                    ai_response=message,
                    assistant_name="AI助手"
                )
            else:
                if target_type == "user":
                    result = await self.dingtalk_api.send_work_notification(
                        user_id=target_id,
                        message=message
                    )
                else:
                    result = await self.dingtalk_api.send_group_message(
                        chat_id=target_id,
                        message=message
                    )
            
            return result
        except Exception as e:
            return {"success": False, "error": f"发送钉钉消息失败: {str(e)}"}
    
    async def _get_dingtalk_user_info(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """获取钉钉用户信息"""
        user_id = parameters.get("user_id")
        
        try:
            user_info = await self.dingtalk_api.get_user_detail(user_id)
            if user_info:
                return {
                    "success": True,
                    "data": {
                        "userid": user_info.get("userid"),
                        "name": user_info.get("name"),
                        "mobile": user_info.get("mobile"),
                        "email": user_info.get("email"),
                        "department": user_info.get("dept_id_list", []),
                        "position": user_info.get("position"),
                        "avatar": user_info.get("avatar")
                    }
                }
            else:
                return {"success": False, "error": "用户不存在"}
        except Exception as e:
            return {"success": False, "error": f"获取用户信息失败: {str(e)}"}
    
    async def _get_dingtalk_department_users(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """获取钉钉部门用户列表"""
        dept_id = parameters.get("dept_id", 1)
        
        try:
            users = await self.dingtalk_api.get_department_users(dept_id)
            if users:
                return {
                    "success": True,
                    "data": {
                        "users": users,
                        "count": len(users),
                        "dept_id": dept_id
                    }
                }
            else:
                return {"success": False, "error": "部门不存在或无用户"}
        except Exception as e:
            return {"success": False, "error": f"获取部门用户失败: {str(e)}"}
    
    async def _create_knowledge_document(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """创建知识库文档"""
        title = parameters.get("title")
        content = parameters.get("content")
        category_id = parameters.get("category_id")
        
        try:
            from schemas.knowledge_base import KnowledgeDocumentCreate
            
            document_data = KnowledgeDocumentCreate(
                title=title,
                content=content,
                category_id=uuid.UUID(category_id) if category_id else None,
                is_public=True
            )
            
            document = await KnowledgeBaseService.create_document(
                db=self.db,
                project_id=self.project.id,
                document_data=document_data,
                created_by=self.user.id
            )
            
            return {
                "success": True,
                "data": {
                    "id": str(document["id"]),
                    "title": document["title"],
                    "created_at": document["created_at"]
                }
            }
        except Exception as e:
            return {"success": False, "error": f"创建知识库文档失败: {str(e)}"}
    
    async def _trigger_workflow(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """触发工作流执行"""
        workflow_id = parameters.get("workflow_id")
        input_data = parameters.get("input_data", {})
        
        try:
            # 检查工作流是否存在
            workflow_query = select(Workflow).where(
                and_(
                    Workflow.id == uuid.UUID(workflow_id),
                    Workflow.project_id == self.project.id
                )
            )
            result = await self.db.execute(workflow_query)
            workflow = result.scalar_one_or_none()
            
            if not workflow:
                return {"success": False, "error": "工作流不存在"}
            
            # 创建工作流实例（简化实现）
            from schemas.workflow import WorkflowInstanceCreate
            
            instance_data = WorkflowInstanceCreate(
                workflow_id=uuid.UUID(workflow_id),
                reference_id=uuid.uuid4(),  # 临时引用ID
                reference_type="ai_trigger",
                data=input_data
            )
            
            # 这里应该调用工作流服务来创建实例
            # 由于WorkflowService可能不存在，我们简化处理
            return {
                "success": True,
                "data": {
                    "workflow_id": workflow_id,
                    "workflow_name": workflow.name,
                    "status": "triggered",
                    "triggered_at": datetime.now().isoformat(),
                    "message": "工作流触发成功（简化实现）"
                }
            }
        except Exception as e:
            return {"success": False, "error": f"触发工作流失败: {str(e)}"}
    
    async def _get_project_workflows(self, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """获取项目工作流列表"""
        status = parameters.get("status")
        
        try:
            workflows_query = select(Workflow).where(Workflow.project_id == self.project.id)
            if status:
                workflows_query = workflows_query.where(Workflow.status == status)
            
            result = await self.db.execute(workflows_query)
            workflows = result.scalars().all()
            
            workflow_list = []
            for workflow in workflows:
                workflow_list.append({
                    "id": str(workflow.id),
                    "name": workflow.name,
                    "description": workflow.description,
                    "status": workflow.status,
                    "trigger_type": workflow.trigger_type,
                    "created_at": workflow.created_at.isoformat() if workflow.created_at else None
                })
            
            return {
                "success": True,
                "data": {
                    "workflows": workflow_list,
                    "count": len(workflow_list)
                }
            }
        except Exception as e:
            return {"success": False, "error": f"获取工作流列表失败: {str(e)}"} 