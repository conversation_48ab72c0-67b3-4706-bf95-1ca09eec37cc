import React, { useState, useEffect } from 'react';
import { Layout, Menu } from 'antd';
import { useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { useTenant } from '../../contexts/TenantContext';
import { useIndustry } from '../../contexts/IndustryContext';
import {
  DashboardOutlined,
  ShopOutlined,
  ShoppingOutlined,
  TeamOutlined,
  SettingOutlined,
  AppstoreOutlined,
  CloudServerOutlined,
  BankOutlined,
  HomeOutlined,
  UserOutlined,
  ProjectOutlined,
  BellOutlined,
  CloudOutlined,
  FileOutlined,
  BookOutlined,
  BarChartOutlined,
  CoffeeOutlined,
  ShoppingCartOutlined,
  ScheduleOutlined,
  SolutionOutlined,
  ApartmentOutlined,
  GlobalOutlined,
  AppstoreAddOutlined,
  ToolOutlined,
  KeyOutlined,
  DatabaseOutlined,
  FolderOutlined,
  FileImageOutlined,
  HddOutlined,
  RobotOutlined
} from '@ant-design/icons';

const { Sider } = Layout;

const AppSider = () => {
  const [collapsed, setCollapsed] = useState(false);
  const { user } = useAuth();
  const { currentTenant, currentProject, industryType } = useTenant();
  const { getIndustryConfig } = useIndustry();

  const location = useLocation();
  const navigate = useNavigate();

  // 获取当前选中的菜单项
  const getSelectedKeys = () => {
    const path = location.pathname;
    return [path];
  };

  // 获取菜单项
  const getMenuItems = () => {
    const menuItems = [];

    // 系统管理员菜单
    if (user?.is_system_admin) {
      menuItems.push({
        key: 'admin',
        icon: <SettingOutlined />,
        label: '系统管理',
        children: [
          {
            key: '/admin/dashboard',
            icon: <DashboardOutlined />,
            label: '系统仪表盘',
            onClick: () => navigate('/admin/dashboard'),
          },
          {
            key: '/admin/service-providers',
            icon: <CloudServerOutlined />,
            label: '服务商管理',
            onClick: () => navigate('/admin/service-providers'),
          },
          {
            key: '/admin/tenants',
            icon: <BankOutlined />,
            label: '租户管理',
            onClick: () => navigate('/admin/tenants'),
          },
          {
            key: '/admin/users',
            icon: <TeamOutlined />,
            label: '用户管理',
            onClick: () => navigate('/admin/users'),
          },
          {
            key: '/admin/industries',
            icon: <ApartmentOutlined />,
            label: '行业管理',
            onClick: () => navigate('/admin/industries'),
          },
          {
            key: '/admin/settings',
            icon: <SettingOutlined />,
            label: '系统设置',
            onClick: () => navigate('/admin/settings'),
          },
          {
            key: '/admin/storage',
            icon: <CloudOutlined />,
            label: '存储管理',
            onClick: () => navigate('/admin/storage'),
          },
        ],
      });
    }

    // 服务商管理员菜单
    if (user?.is_service_provider_admin) {
      menuItems.push({
        key: 'service-provider',
        icon: <CloudServerOutlined />,
        label: '服务商管理',
        children: [
          {
            key: '/service-provider/dashboard',
            icon: <DashboardOutlined />,
            label: '服务商仪表盘',
            onClick: () => navigate('/service-provider/dashboard'),
          },
          {
            key: '/service-provider/tenants',
            icon: <BankOutlined />,
            label: '租户管理',
            onClick: () => navigate('/service-provider/tenants'),
          },
          {
            key: '/service-provider/settings',
            icon: <SettingOutlined />,
            label: '服务商设置',
            onClick: () => navigate('/service-provider/settings'),
          },
        ],
      });
    }

    // 租户管理员菜单
    if (user?.is_tenant_admin && currentTenant) {
      menuItems.push({
        key: 'tenant',
        icon: <BankOutlined />,
        label: '租户管理',
        children: [
          {
            key: '/tenant/dashboard',
            icon: <DashboardOutlined />,
            label: '租户仪表盘',
            onClick: () => navigate('/tenant/dashboard'),
          },
          {
            key: '/tenant/projects',
            icon: <ProjectOutlined />,
            label: '项目管理',
            onClick: () => navigate('/tenant/projects'),
          },
          {
            key: '/tenant/settings',
            icon: <SettingOutlined />,
            label: '租户设置',
            onClick: () => navigate('/tenant/settings'),
          },
        ],
      });
    }

    // 行业特定菜单
    if (currentTenant && industryType) {
      switch (industryType) {
        case 'retail':
          // 零售业菜单
          menuItems.push({
            key: 'retail',
            icon: <ShoppingCartOutlined />,
            label: '零售管理',
            children: [
              {
                key: '/dashboard',
                icon: <DashboardOutlined />,
                label: '零售仪表盘',
                onClick: () => navigate('/dashboard'),
              },
              {
                key: '/retail/products',
                icon: <ShoppingOutlined />,
                label: '商品管理',
                onClick: () => navigate('/retail/products'),
              },
              {
                key: '/retail/inventory',
                icon: <HddOutlined />,
                label: '库存管理',
                onClick: () => navigate('/retail/inventory'),
              },
              {
                key: '/retail/stores',
                icon: <ShopOutlined />,
                label: '门店管理',
                onClick: () => navigate('/retail/stores'),
              },
              {
                key: '/retail/sales',
                icon: <BarChartOutlined />,
                label: '销售管理',
                onClick: () => navigate('/retail/sales'),
              },
              {
                key: '/retail/suppliers',
                icon: <SolutionOutlined />,
                label: '供应商管理',
                onClick: () => navigate('/retail/suppliers'),
              },
            ],
          });
          break;

        case 'restaurant':
          // 餐饮业菜单
          menuItems.push({
            key: 'restaurant',
            icon: <CoffeeOutlined />,
            label: '餐饮管理',
            children: [
              {
                key: '/dashboard',
                icon: <DashboardOutlined />,
                label: '餐饮仪表盘',
                onClick: () => navigate('/dashboard'),
              },
              {
                key: '/restaurant/menu',
                icon: <FileOutlined />,
                label: '菜单管理',
                onClick: () => navigate('/restaurant/menu'),
              },
              {
                key: '/restaurant/orders',
                icon: <ShoppingCartOutlined />,
                label: '订单管理',
                onClick: () => navigate('/restaurant/orders'),
              },
              {
                key: '/restaurant/tables',
                icon: <AppstoreOutlined />,
                label: '餐桌管理',
                onClick: () => navigate('/restaurant/tables'),
              },
              {
                key: '/restaurant/reservations',
                icon: <ScheduleOutlined />,
                label: '预订管理',
                onClick: () => navigate('/restaurant/reservations'),
              },
              {
                key: '/restaurant/inventory',
                icon: <HddOutlined />,
                label: '库存管理',
                onClick: () => navigate('/restaurant/inventory'),
              },
            ],
          });
          break;

        case 'hotel':
          // 酒店业菜单
          menuItems.push({
            key: 'hotel',
            icon: <HomeOutlined />,
            label: '酒店管理',
            children: [
              {
                key: '/dashboard',
                icon: <DashboardOutlined />,
                label: '酒店仪表盘',
                onClick: () => navigate('/dashboard'),
              },
              {
                key: '/hotel/rooms',
                icon: <HomeOutlined />,
                label: '房间管理',
                onClick: () => navigate('/hotel/rooms'),
              },
              {
                key: '/hotel/bookings',
                icon: <ScheduleOutlined />,
                label: '预订管理',
                onClick: () => navigate('/hotel/bookings'),
              },
              {
                key: '/hotel/guests',
                icon: <UserOutlined />,
                label: '客人管理',
                onClick: () => navigate('/hotel/guests'),
              },
              {
                key: '/hotel/services',
                icon: <ToolOutlined />,
                label: '服务管理',
                onClick: () => navigate('/hotel/services'),
              },
              {
                key: '/hotel/housekeeping',
                icon: <SolutionOutlined />,
                label: '客房服务',
                onClick: () => navigate('/hotel/housekeeping'),
              },
            ],
          });
          break;

        default:
          // 默认菜单
          menuItems.push({
            key: 'default',
            icon: <DashboardOutlined />,
            label: '仪表盘',
            onClick: () => navigate('/dashboard'),
          });
          break;
      }
    }

    // 通用菜单
    menuItems.push({
      key: 'common',
      icon: <AppstoreOutlined />,
      label: '通用功能',
      children: [
        {
          key: '/profile',
          icon: <UserOutlined />,
          label: '个人中心',
          onClick: () => navigate('/profile'),
        },
        {
          key: '/notifications',
          icon: <BellOutlined />,
          label: '通知中心',
          onClick: () => navigate('/notifications'),
        },
        {
          key: '/tenant/plugin/market',
          icon: <AppstoreAddOutlined />,
          label: '插件市场',
          onClick: () => navigate('/tenant/plugin/market'),
        },
      ],
    });

    // AI功能菜单
    menuItems.push({
      key: 'ai',
      icon: <RobotOutlined />,
      label: 'AI功能',
      children: [
        {
          key: '/ai/assistant',
          icon: <RobotOutlined />,
          label: 'AI助手',
          onClick: () => navigate('/ai/assistant'),
        },
        {
          key: '/ai/analysis',
          icon: <BarChartOutlined />,
          label: 'AI分析',
          onClick: () => navigate('/ai/analysis'),
        },
        {
          key: '/ai/knowledge',
          icon: <BookOutlined />,
          label: '知识库',
          onClick: () => navigate('/ai/knowledge'),
        },
      ],
    });

    return menuItems;
  };

  return (
    <Sider
      collapsible
      collapsed={collapsed}
      onCollapse={value => setCollapsed(value)}
      width={220}
      theme="light"
    >
      <div className="logo" style={{ height: '64px', padding: '16px', textAlign: 'center' }} />
      <Menu
        theme="light"
        mode="inline"
        selectedKeys={getSelectedKeys()}
        defaultOpenKeys={['admin', 'service-provider', 'tenant', 'retail', 'restaurant', 'hotel', 'common', 'ai']}
        items={getMenuItems()}
      />
    </Sider>
  );
};

export default AppSider;
