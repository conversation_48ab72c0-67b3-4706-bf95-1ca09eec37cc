# 多行业AI SaaS系统架构设计

## 1. 系统架构概述

多行业AI SaaS系统采用现代化的多层架构设计，支持服务商-租户-项目的多级模式和多行业部署模式，主要分为以下几个层次：

```
多行业AI SaaS系统
├── 接入层 (Access Layer)
│   ├── 负载均衡
│   ├── API网关
│   └── CDN
├── 应用层 (Application Layer)
│   ├── SaaS管理平台
│   │   ├── 服务商管理
│   │   ├── 租户管理
│   │   ├── 项目管理
│   │   └── 行业管理
│   ├── 租户应用服务
│   ├── 行业应用服务
│   │   ├── 零售业务
│   │   ├── 餐饮业务
│   │   └── 其他行业业务
│   └── 公共服务
├── 数据层 (Data Layer)
│   ├── 关系型数据库
│   │   ├── 公共Schema
│   │   └── 租户Schema
│   ├── 缓存服务
│   ├── 文件存储
│   └── 消息队列
└── 基础设施层 (Infrastructure Layer)
    ├── 容器编排
    ├── 监控系统
    ├── 日志系统
    └── 备份恢复
```

## 2. 多级架构设计

### 2.1 服务商-租户-项目模型

系统采用服务商-租户-项目的多级模型，支持多级分销和多行业部署：

1. **服务商层**：服务商可以创建和管理多个租户，实现自主运营和OEM定制
2. **租户层**：租户可以创建和管理多个项目，并指定行业类型
3. **项目层**：项目是实际业务数据和功能的承载单元，可以根据行业类型加载不同的功能模块

### 2.2 数据隔离模型

系统采用混合数据隔离模型，结合了以下几种隔离策略的优点：

1. **应用层隔离**：通过服务商、租户和项目的多级上下文进行逻辑隔离
2. **数据层隔离**：
   - 服务商数据：采用行级隔离，存储在公共Schema中
   - 租户配置数据：采用行级隔离，存储在公共Schema中
   - 租户业务数据：采用Schema隔离，每个租户独立Schema
   - 行业特定数据：在租户Schema中根据行业类型创建不同的表结构

### 2.3 多级识别与路由

1. **服务商识别机制**：
   - 域名识别：`*.{service-provider-domain}.com`
   - 请求头识别：`X-Service-Provider-ID`
   - API路径识别：`/api/service-providers/{service-provider-id}/...`

2. **租户识别机制**：
   - 域名识别：`{tenant-id}.{service-provider-domain}.com`
   - 请求头识别：`X-Tenant-ID`
   - API路径识别：`/api/tenants/{tenant-id}/...`

3. **项目识别机制**：
   - 请求头识别：`X-Project-ID`
   - API路径识别：`/api/projects/{project-id}/...`

4. **多级路由策略**：
   - API网关根据服务商ID、租户ID和项目ID进行请求路由
   - 应用服务根据多级上下文加载对应配置
   - 数据库连接池根据租户ID选择对应Schema
   - 行业服务根据租户和项目的行业类型加载对应功能模块

### 2.4 服务商与租户生命周期管理

1. **服务商创建流程**：
   - 注册服务商基本信息
   - 设置服务商分成比例
   - 配置服务商支持的行业类型
   - 创建服务商管理员账户
   - 设置服务商品牌与OEM定制

2. **租户创建流程**：
   - 注册租户基本信息
   - 关联服务商
   - 设置租户行业类型
   - 创建租户数据库Schema
   - 根据行业类型初始化租户数据结构
   - 创建租户管理员账户
   - 分配资源配额

3. **项目创建流程**：
   - 注册项目基本信息
   - 设置项目行业类型
   - 加载行业特定功能模块
   - 分配项目资源配额

4. **服务商/租户暂停/恢复**：
   - 暂停：禁用访问，保留数据
   - 恢复：重新启用访问

5. **服务商/租户删除**：
   - 软删除：标记删除状态，保留数据一定期限
   - 硬删除：彻底删除数据（需管理员确认）

## 3. 核心组件设计

### 3.1 SaaS管理平台

管理平台是系统的核心组件，负责服务商、租户、项目、用户、行业和订阅的全生命周期管理。

#### 3.1.1 服务商管理模块

- **功能**：
  - 服务商创建、编辑、暂停、恢复、删除
  - 服务商分成比例设置
  - 服务商品牌与OEM定制
  - 服务商行业支持配置
  - 服务商订阅计划管理

- **关键API**：
  - `POST /api/admin/service-providers` - 创建服务商
  - `GET /api/admin/service-providers` - 获取服务商列表
  - `GET /api/admin/service-providers/{service_provider_id}` - 获取服务商详情
  - `PUT /api/admin/service-providers/{service_provider_id}` - 更新服务商信息
  - `DELETE /api/admin/service-providers/{service_provider_id}` - 删除服务商

#### 3.1.2 租户管理模块

- **功能**：
  - 租户创建、编辑、暂停、恢复、删除
  - 租户资源配额管理
  - 租户使用统计与报表
  - 租户配置管理
  - 租户行业类型设置

- **关键API**：
  - `POST /api/admin/tenants` - 创建租户
  - `GET /api/admin/tenants` - 获取租户列表
  - `GET /api/admin/tenants/{tenant_id}` - 获取租户详情
  - `PUT /api/admin/tenants/{tenant_id}` - 更新租户信息
  - `DELETE /api/admin/tenants/{tenant_id}` - 删除租户
  - `POST /api/service-provider/tenants` - 服务商创建租户
  - `GET /api/service-provider/tenants` - 服务商获取租户列表

#### 3.1.3 项目管理模块

- **功能**：
  - 项目模板管理
  - 项目创建、配置、删除
  - 项目资源分配
  - 项目状态监控
  - 项目行业类型设置
  - 行业特定功能配置

- **关键API**：
  - `POST /api/admin/tenants/{tenant_id}/projects` - 创建项目
  - `GET /api/admin/tenants/{tenant_id}/projects` - 获取项目列表
  - `PUT /api/admin/tenants/{tenant_id}/projects/{project_id}` - 更新项目
  - `POST /api/tenant/projects` - 租户创建项目
  - `GET /api/tenant/projects` - 租户获取项目列表

#### 3.1.4 用户与权限管理

- **功能**：
  - 系统管理员账户管理
  - 服务商管理员账户管理
  - 租户管理员账户管理
  - 角色与权限模板
  - 跨项目权限控制
  - 单点登录(SSO)支持
  - 行业特定角色管理

- **关键API**：
  - `POST /api/admin/service-providers/{service_provider_id}/admins` - 创建服务商管理员
  - `POST /api/admin/tenants/{tenant_id}/users` - 创建租户用户
  - `GET /api/admin/tenants/{tenant_id}/users` - 获取租户用户列表
  - `POST /api/admin/tenants/{tenant_id}/roles` - 创建角色
  - `POST /api/service-provider/tenants/{tenant_id}/admins` - 服务商创建租户管理员

#### 3.1.5 计费与订阅管理

- **功能**：
  - 系统订阅计划管理
  - 服务商订阅计划管理
  - 租户订阅管理
  - 计费周期设置
  - 资源使用统计
  - 账单生成与支付集成
  - 服务商分成管理
  - 行业特定计费方案

- **关键API**：
  - `GET /api/admin/billing/plans` - 获取系统订阅计划
  - `POST /api/admin/service-providers/{service_provider_id}/plans` - 创建服务商订阅计划
  - `GET /api/service-provider/plans` - 获取服务商订阅计划
  - `POST /api/admin/tenants/{tenant_id}/subscriptions` - 创建租户订阅
  - `GET /api/admin/tenants/{tenant_id}/invoices` - 获取租户账单
  - `GET /api/admin/service-providers/{service_provider_id}/commissions` - 获取服务商分成

#### 3.1.6 行业管理模块

- **功能**：
  - 行业类型管理
  - 行业模型管理
  - 行业特定插件管理
  - 行业模板管理
  - 行业配置管理

- **关键API**：
  - `GET /api/admin/industries` - 获取行业列表
  - `POST /api/admin/industries` - 创建行业
  - `GET /api/admin/industries/{industry_id}` - 获取行业详情
  - `PUT /api/admin/industries/{industry_id}` - 更新行业
  - `GET /api/admin/industries/{industry_id}/models` - 获取行业模型
  - `POST /api/admin/industries/{industry_id}/models` - 创建行业模型
  - `GET /api/admin/industries/{industry_id}/plugins` - 获取行业插件

### 3.2 多级应用服务

#### 3.2.1 多级上下文管理

- **功能**：
  - 服务商识别与验证
  - 租户识别与验证
  - 项目识别与验证
  - 行业类型识别
  - 多级配置加载
  - 资源限制检查
  - 操作审计

- **实现方式**：
  - 请求中间件提取服务商ID、租户ID和项目ID
  - 多级上下文对象贯穿请求生命周期
  - 数据访问层自动应用多级过滤
  - 行业类型动态加载对应功能模块

#### 3.2.2 多行业业务功能模块

系统将现有零售AI系统的业务功能模块改造为支持多租户、多行业：

##### 零售行业功能模块

- **门店管理**：支持租户内多门店管理
- **商品管理**：支持租户特定商品目录
- **库存管理**：支持租户内库存跟踪
- **销售管理**：支持租户销售数据记录与分析
- **采购管理**：支持租户采购流程
- **财务管理**：支持租户财务数据管理

##### 餐饮行业功能模块

- **菜品管理**：支持餐厅菜品管理
- **订单管理**：支持餐厅订单处理
- **厨房管理**：支持厨房生产流程
- **预订管理**：支持餐厅预订服务
- **外卖管理**：支持餐厅外卖服务

##### 酒店行业功能模块

- **房间管理**：支持酒店房间管理
- **预订管理**：支持酒店预订服务
- **前台管理**：支持酒店前台服务
- **客房管理**：支持酒店客房服务

##### 通用功能模块

- **报表分析**：支持租户数据可视化与分析
- **知识库**：支持租户知识管理
- **任务管理**：支持租户内任务分配与跟踪
- **客户管理**：支持租户客户关系管理
- **员工管理**：支持租户员工管理

### 3.3 插件系统

#### 3.3.1 插件架构

- **插件类型**：
  - 功能插件：扩展系统功能
  - 集成插件：与第三方系统集成
  - 主题插件：自定义UI主题
  - 行业插件：行业特定功能扩展
  - 服务商插件：服务商定制功能

- **插件隔离**：
  - 每个插件在独立容器中运行
  - 通过API网关与主系统通信
  - 插件数据存储在租户Schema中
  - 插件权限根据服务商、租户和项目级别控制
  - 行业插件根据项目行业类型自动加载

#### 3.3.2 插件市场

- **功能**：
  - 插件浏览与搜索
  - 插件分类管理
  - 行业插件分类
  - 插件安装与配置
  - 插件评分与评论
  - 插件更新管理
  - 服务商插件定制

- **关键API**：
  - `GET /api/plugins` - 获取插件列表
  - `GET /api/plugins/industries/{industry_id}` - 获取行业插件
  - `POST /api/service-provider/plugins` - 服务商上传插件
  - `POST /api/tenants/{tenant_id}/plugins/{plugin_id}/install` - 安装插件
  - `PUT /api/tenants/{tenant_id}/plugins/{plugin_id}/config` - 配置插件
  - `POST /api/projects/{project_id}/plugins/{plugin_id}/install` - 项目安装插件

## 4. 数据模型设计

### 4.1 核心数据模型

#### 4.1.1 租户模型

```sql
CREATE TABLE tenants (
    id UUID PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    domain_prefix VARCHAR(50) UNIQUE,
    status VARCHAR(20) NOT NULL DEFAULT 'active',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    subscription_id UUID REFERENCES subscriptions(id),
    settings JSONB
);
```

#### 4.1.2 项目模型

```sql
CREATE TABLE projects (
    id UUID PRIMARY KEY,
    tenant_id UUID REFERENCES tenants(id),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    status VARCHAR(20) NOT NULL DEFAULT 'active',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    storage_quota BIGINT DEFAULT 536870912, -- 512MB in bytes
    user_quota INTEGER DEFAULT 10,
    settings JSONB
);
```

#### 4.1.3 用户模型

```sql
CREATE TABLE users (
    id UUID PRIMARY KEY,
    tenant_id UUID REFERENCES tenants(id),
    username VARCHAR(50) NOT NULL,
    email VARCHAR(100) NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'active',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_login_at TIMESTAMP WITH TIME ZONE,
    is_tenant_admin BOOLEAN DEFAULT FALSE,
    settings JSONB
);

CREATE TABLE user_projects (
    user_id UUID REFERENCES users(id),
    project_id UUID REFERENCES projects(id),
    role_id UUID REFERENCES roles(id),
    PRIMARY KEY (user_id, project_id)
);
```

#### 4.1.4 订阅模型

```sql
CREATE TABLE subscription_plans (
    id UUID PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    price DECIMAL(10, 2) NOT NULL,
    billing_cycle VARCHAR(20) NOT NULL,
    max_projects INTEGER,
    max_users_per_tenant INTEGER,
    storage_quota BIGINT, -- in bytes
    features JSONB
);

CREATE TABLE subscriptions (
    id UUID PRIMARY KEY,
    tenant_id UUID REFERENCES tenants(id),
    plan_id UUID REFERENCES subscription_plans(id),
    status VARCHAR(20) NOT NULL DEFAULT 'active',
    start_date DATE NOT NULL,
    end_date DATE,
    auto_renew BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

### 4.2 业务数据模型

业务数据模型将基于现有零售AI系统的数据模型，但需要添加租户和项目标识：

1. **租户级数据**：添加`tenant_id`字段
2. **项目级数据**：添加`project_id`字段

例如，改造后的门店模型：

```sql
CREATE TABLE stores (
    id UUID PRIMARY KEY,
    tenant_id UUID NOT NULL,
    project_id UUID NOT NULL,
    name VARCHAR(100) NOT NULL,
    address TEXT,
    contact_person VARCHAR(100),
    contact_phone VARCHAR(20),
    status VARCHAR(20) DEFAULT 'active',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);
```

### 4.3 数据迁移策略

1. **Schema设计**：
   - 公共Schema：存储租户、订阅等SaaS平台数据
   - 租户Schema：每个租户独立Schema存储业务数据

2. **迁移工具**：
   - 开发自动化迁移工具
   - 支持增量Schema更新
   - 支持数据验证与回滚

3. **迁移流程**：
   - 创建租户Schema
   - 应用基础表结构
   - 迁移租户数据
   - 验证数据一致性

## 5. API设计

### 5.1 API架构

系统API采用RESTful设计风格，主要分为以下几类：

1. **管理API**：SaaS平台管理功能
   - 前缀：`/api/admin/`
   - 权限：系统管理员

2. **租户API**：租户管理功能
   - 前缀：`/api/tenants/`
   - 权限：租户管理员

3. **项目API**：项目相关功能
   - 前缀：`/api/projects/`
   - 权限：项目成员

4. **业务API**：业务功能
   - 前缀：`/api/{tenant_id}/{project_id}/`
   - 权限：根据具体功能设置

### 5.2 认证与授权

1. **认证机制**：
   - JWT (JSON Web Token)
   - OAuth2.0 支持
   - API密钥认证

2. **授权策略**：
   - 基于角色的访问控制 (RBAC)
   - 基于属性的访问控制 (ABAC)
   - 租户与项目级权限隔离

3. **权限检查流程**：
   - 验证用户身份
   - 检查租户访问权限
   - 检查项目访问权限
   - 检查具体操作权限

### 5.3 API版本控制

1. **版本策略**：
   - URL路径版本：`/api/v1/`
   - 请求头版本：`Accept: application/vnd.retail-ai.v1+json`

2. **兼容性保证**：
   - 向后兼容设计
   - 版本过渡期支持
   - API变更通知机制

## 6. 前端架构

### 6.1 多租户前端设计

1. **动态主题**：
   - 基于租户配置动态加载主题
   - 支持品牌定制（logo、颜色、字体等）

2. **动态模块加载**：
   - 基于租户订阅计划动态加载功能模块
   - 插件功能动态集成

3. **多级导航**：
   - 租户级导航
   - 项目级导航
   - 功能模块导航

### 6.2 前端技术架构

1. **核心框架**：React
2. **状态管理**：Redux + Redux Toolkit
3. **路由**：React Router
4. **UI组件**：Ant Design
5. **图表**：AntV/G2
6. **地图**：AntV/L7
7. **HTTP客户端**：Axios
8. **国际化**：i18next
9. **构建工具**：Webpack / Vite

### 6.3 前端模块组织

```
src/
├── assets/            # 静态资源
├── components/        # 通用组件
│   ├── common/        # 基础组件
│   ├── layout/        # 布局组件
│   └── business/      # 业务组件
├── contexts/          # React上下文
│   ├── AuthContext.js # 认证上下文
│   └── TenantContext.js # 租户上下文
├── hooks/             # 自定义Hooks
├── pages/             # 页面组件
│   ├── admin/         # 管理平台页面
│   ├── tenant/        # 租户管理页面
│   └── project/       # 项目页面
├── services/          # API服务
├── store/             # Redux状态
├── styles/            # 全局样式
├── utils/             # 工具函数
├── App.js             # 应用入口
└── index.js           # 渲染入口
```

## 7. 部署架构

### 7.1 容器化部署

系统采用Docker容器化部署，主要组件包括：

1. **前端容器**：
   - Nginx + React静态文件
   - 配置动态加载

2. **后端容器**：
   - FastAPI应用服务
   - 租户上下文管理

3. **数据库容器**：
   - PostgreSQL
   - 多Schema支持

4. **缓存容器**：
   - Redis
   - 多租户缓存隔离

5. **消息队列容器**：
   - RabbitMQ
   - 多租户队列隔离

### 7.2 Kubernetes编排

对于大规模部署，系统支持Kubernetes编排：

1. **部署策略**：
   - 前端：Deployment + Service + Ingress
   - 后端：Deployment + Service + HPA
   - 数据库：StatefulSet + PVC

2. **扩展策略**：
   - 水平Pod自动扩展 (HPA)
   - 垂直Pod自动扩展 (VPA)
   - 集群自动扩展

3. **网络策略**：
   - 服务网格 (Istio)
   - 网络策略隔离
   - 入口流量管理

### 7.3 CI/CD流水线

1. **持续集成**：
   - 代码质量检查
   - 单元测试
   - 集成测试
   - 安全扫描

2. **持续部署**：
   - 环境管理（开发、测试、生产）
   - 自动化部署
   - 蓝绿部署
   - 金丝雀发布

## 8. 监控与运维

### 8.1 监控系统

1. **基础设施监控**：
   - 服务器资源监控
   - 容器监控
   - 数据库监控

2. **应用监控**：
   - API性能监控
   - 错误率监控
   - 用户体验监控

3. **业务监控**：
   - 租户活跃度监控
   - 资源使用监控
   - 业务指标监控

### 8.2 日志管理

1. **日志收集**：
   - 应用日志
   - 系统日志
   - 审计日志

2. **日志存储**：
   - 分布式存储
   - 日志分片
   - 日志保留策略

3. **日志分析**：
   - 实时日志搜索
   - 日志可视化
   - 异常检测

### 8.3 备份与恢复

1. **备份策略**：
   - 定时全量备份
   - 增量备份
   - 跨区域备份

2. **恢复策略**：
   - 租户级恢复
   - 项目级恢复
   - 数据级恢复

3. **灾难恢复**：
   - 多区域部署
   - 故障转移
   - 恢复演练

## 9. 安全架构

### 9.1 数据安全

1. **数据加密**：
   - 传输加密 (TLS)
   - 存储加密
   - 敏感数据加密

2. **数据隔离**：
   - 租户数据隔离
   - 访问控制
   - 数据脱敏

3. **数据合规**：
   - 数据分类
   - 数据留存策略
   - 合规审计

### 9.2 应用安全

1. **身份认证**：
   - 多因素认证
   - 单点登录
   - 会话管理

2. **访问控制**：
   - 基于角色的访问控制
   - 最小权限原则
   - API权限控制

3. **安全开发**：
   - 安全编码规范
   - 依赖管理
   - 漏洞扫描

### 9.3 基础设施安全

1. **网络安全**：
   - 网络隔离
   - 防火墙
   - DDoS防护

2. **容器安全**：
   - 镜像扫描
   - 运行时保护
   - 特权控制

3. **密钥管理**：
   - 密钥轮换
   - 密钥访问控制
   - 密钥审计

## 10. 扩展性设计

### 10.1 水平扩展

1. **应用层扩展**：
   - 无状态设计
   - 负载均衡
   - 会话共享

2. **数据层扩展**：
   - 读写分离
   - 分片
   - 复制

### 10.2 功能扩展

1. **插件系统**：
   - 插件接口标准化
   - 插件生命周期管理
   - 插件权限控制

2. **API扩展**：
   - API网关扩展
   - 自定义端点
   - Webhook支持

### 10.3 集成扩展

1. **第三方集成**：
   - 认证集成 (OAuth, SAML)
   - 支付集成
   - 通知集成

2. **数据集成**：
   - ETL工具
   - 数据导入导出
   - API数据同步

## 11. 性能优化

### 11.1 应用性能

1. **代码优化**：
   - 异步处理
   - 批处理
   - 缓存策略

2. **资源优化**：
   - 内存管理
   - 连接池
   - 线程池

### 11.2 数据库性能

1. **查询优化**：
   - 索引设计
   - 查询重写
   - 执行计划分析

2. **数据结构优化**：
   - 表设计
   - 数据类型选择
   - 分区策略

### 11.3 前端性能

1. **加载优化**：
   - 代码分割
   - 懒加载
   - 资源压缩

2. **渲染优化**：
   - 虚拟列表
   - 组件缓存
   - 按需渲染

## 12. 实施路线图

### 12.1 第一阶段：基础架构（1-2个月）

1. 设计并实现多租户数据模型
2. 开发租户上下文管理
3. 实现基本的租户管理功能
4. 开发认证与授权系统
5. 搭建基础部署架构

### 12.2 第二阶段：核心功能（2-3个月）

1. 开发项目管理功能
2. 实现用户与权限管理
3. 开发订阅与计费系统
4. 改造核心业务模块支持多租户
5. 实现基础监控与日志

### 12.3 第三阶段：插件与扩展（1-2个月）

1. 开发插件系统架构
2. 实现插件市场基础功能
3. 开发API扩展机制
4. 实现第三方集成框架
5. 开发数据导入导出工具

### 12.4 第四阶段：优化与完善（1-2个月）

1. 性能优化与压力测试
2. 安全审计与漏洞修复
3. 完善监控与告警系统
4. 开发运维管理工具
5. 编写系统文档与培训材料
