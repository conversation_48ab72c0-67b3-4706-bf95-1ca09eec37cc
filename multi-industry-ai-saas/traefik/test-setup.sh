#!/bin/bash

# Traefik与主项目集成测试脚本
# 作者: AI Assistant

set -e

echo "🧪 开始测试Traefik与主项目集成..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 测试函数
test_endpoint() {
    local url=$1
    local description=$2
    local expected_status=${3:-200}
    
    echo -e "${BLUE}🔍 测试: $description${NC}"
    echo "   URL: $url"
    
    if response=$(curl -s -w "%{http_code}" -o /dev/null "$url" 2>/dev/null); then
        if [ "$response" -eq "$expected_status" ]; then
            echo -e "${GREEN}✅ 通过 (HTTP $response)${NC}"
            return 0
        else
            echo -e "${YELLOW}⚠️  状态码不匹配 (期望: $expected_status, 实际: $response)${NC}"
            return 1
        fi
    else
        echo -e "${RED}❌ 连接失败${NC}"
        return 1
    fi
}

# 检查服务状态
check_service_status() {
    local service_name=$1
    local container_name=$2
    
    echo -e "${BLUE}🔍 检查服务状态: $service_name${NC}"
    
    if docker ps | grep -q "$container_name"; then
        echo -e "${GREEN}✅ $service_name 运行正常${NC}"
        return 0
    else
        echo -e "${RED}❌ $service_name 未运行或状态异常${NC}"
        return 1
    fi
}

# 检查网络连接
check_network_connectivity() {
    echo -e "${BLUE}🔍 检查Docker网络连接${NC}"
    
    if docker network inspect docker_retail-ai-saas-network >/dev/null 2>&1; then
        echo -e "${GREEN}✅ docker_retail-ai-saas-network 网络存在${NC}"
    else
        echo -e "${RED}❌ docker_retail-ai-saas-network 网络不存在${NC}"
        return 1
    fi
    
    # 检查Traefik是否连接到网络
    if docker inspect traefik | grep -q "docker_retail-ai-saas-network"; then
        echo -e "${GREEN}✅ Traefik已连接到主项目网络${NC}"
    else
        echo -e "${RED}❌ Traefik未连接到主项目网络${NC}"
        return 1
    fi
}

# 主测试流程
main() {
    echo "=" * 60
    echo "🚀 Traefik与主项目集成测试"
    echo "=" * 60
    
    # 1. 检查服务状态
    echo -e "\n${BLUE}📋 第一步: 检查服务状态${NC}"
    check_service_status "主项目Nginx" "retail-ai-saas-nginx" || exit 1
    check_service_status "Traefik" "traefik" || exit 1
    
    # 2. 检查网络连接
    echo -e "\n${BLUE}📋 第二步: 检查网络连接${NC}"
    check_network_connectivity || exit 1
    
    # 3. 等待服务就绪
    echo -e "\n${BLUE}📋 第三步: 等待服务就绪${NC}"
    echo "等待10秒让服务完全启动..."
    sleep 10
    
    # 4. 测试Traefik仪表板
    echo -e "\n${BLUE}📋 第四步: 测试Traefik仪表板${NC}"
    test_endpoint "http://localhost:8090/ping" "Traefik健康检查" 200
    test_endpoint "http://localhost:8090/api/rawdata" "Traefik API" 200
    
    # 5. 测试主项目路由
    echo -e "\n${BLUE}📋 第五步: 测试主项目路由${NC}"
    test_endpoint "http://localhost/health" "主项目健康检查" 200
    test_endpoint "http://localhost/api/v1/health" "主项目API健康检查" 200
    
    # 6. 测试静态资源路由（可能返回404，但应该能连接）
    echo -e "\n${BLUE}📋 第六步: 测试静态资源路由${NC}"
    test_endpoint "http://localhost/uploads/" "上传文件路由" 404  # 404是正常的，说明路由工作
    
    # 7. 测试MCP路由（如果MCP服务运行）
    echo -e "\n${BLUE}📋 第七步: 测试MCP路由${NC}"
    if test_endpoint "http://localhost/mcp/health" "MCP服务健康检查" 200; then
        echo -e "${GREEN}✅ MCP服务正常${NC}"
    else
        echo -e "${YELLOW}⚠️  MCP服务可能未启动，这是正常的${NC}"
    fi
    
    # 8. 检查路由配置
    echo -e "\n${BLUE}📋 第八步: 检查路由配置${NC}"
    if curl -s "http://localhost:8090/api/http/routers" | grep -q "main-router"; then
        echo -e "${GREEN}✅ 主项目路由配置正确${NC}"
    else
        echo -e "${RED}❌ 主项目路由配置异常${NC}"
    fi
    
    # 9. 测试容器间连接
    echo -e "\n${BLUE}📋 第九步: 测试容器间连接${NC}"
    if docker exec traefik ping -c 1 retail-ai-saas-nginx >/dev/null 2>&1; then
        echo -e "${GREEN}✅ Traefik可以连接到主项目nginx${NC}"
    else
        echo -e "${RED}❌ Traefik无法连接到主项目nginx${NC}"
    fi
    
    echo -e "\n${GREEN}🎉 测试完成！${NC}"
    echo -e "${BLUE}📊 测试总结:${NC}"
    echo "- Traefik仪表板: http://localhost:8090"
    echo "- 主项目访问: http://localhost"
    echo "- API访问: http://localhost/api"
    echo "- 健康检查: http://localhost/health"
    
    echo -e "\n${YELLOW}💡 提示:${NC}"
    echo "- 如果测试失败，请检查服务启动顺序"
    echo "- 确保主项目先启动，然后启动Traefik"
    echo "- 查看日志: docker-compose logs -f traefik"
}

# 运行主函数
main "$@" 