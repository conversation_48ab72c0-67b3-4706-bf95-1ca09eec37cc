#!/usr/bin/env python
# -*- coding: utf-8 -*-

from typing import List, Optional, Dict, Any, Union
from datetime import datetime, date
from uuid import UUID
from pydantic import BaseModel, Field, validator

# 财务对账基础模型
class FinancialReconciliationBase(BaseModel):
    store_id: Optional[UUID] = None
    channel_id: Optional[UUID] = None
    reconciliation_date: datetime
    reconciliation_type: str
    reported_sales: float = 0.0
    system_sales: float = 0.0
    reported_recharge: float = 0.0
    system_recharge: float = 0.0
    sales_diff: float = 0.0
    recharge_diff: float = 0.0
    status: str = "normal"
    details: Optional[Dict[str, Any]] = None
    notes: Optional[str] = None

# 创建财务对账请求模型
class FinancialReconciliationCreate(FinancialReconciliationBase):
    pass

# 更新财务对账请求模型
class FinancialReconciliationUpdate(BaseModel):
    reported_sales: Optional[float] = None
    system_sales: Optional[float] = None
    reported_recharge: Optional[float] = None
    system_recharge: Optional[float] = None
    sales_diff: Optional[float] = None
    recharge_diff: Optional[float] = None
    status: Optional[str] = None
    details: Optional[Dict[str, Any]] = None
    notes: Optional[str] = None

# 财务对账响应模型
class FinancialReconciliationResponse(FinancialReconciliationBase):
    id: UUID
    project_id: UUID
    created_at: datetime
    updated_at: datetime
    
    # 关联信息
    store_name: Optional[str] = None
    channel_name: Optional[str] = None

    class Config:
        model_config = {"from_attributes": True}

# 财务对账列表响应模型
class FinancialReconciliationListResponse(BaseModel):
    items: List[FinancialReconciliationResponse]
    total: int
    page: int
    size: int
    pages: int

# 对账数据上传基础模型
class ReconciliationUploadBase(BaseModel):
    file_name: str
    upload_type: str
    reconciliation_date: datetime

# 创建对账数据上传请求模型
class ReconciliationUploadCreate(ReconciliationUploadBase):
    pass

# 对账数据上传响应模型
class ReconciliationUploadResponse(ReconciliationUploadBase):
    id: UUID
    project_id: UUID
    file_path: str
    status: str
    result: Optional[Dict[str, Any]] = None
    processed_count: int
    error_count: int
    created_at: datetime
    updated_at: datetime

    class Config:
        model_config = {"from_attributes": True}

# 对账数据上传列表响应模型
class ReconciliationUploadListResponse(BaseModel):
    items: List[ReconciliationUploadResponse]
    total: int
    page: int
    size: int
    pages: int

# 门店对账明细项模型
class StoreReconciliationDetailItem(BaseModel):
    date: str
    reported_sales: float
    system_sales: float
    sales_diff: float
    reported_recharge: float
    system_recharge: float
    recharge_diff: float
    status: str

# 渠道销售对账项模型
class ChannelSalesReconciliationItem(BaseModel):
    channel_id: str
    channel_name: str
    reported_amount: float
    system_amount: float
    diff: float
    status: str

# 充值对账数据模型
class RechargeReconciliationData(BaseModel):
    reported: float
    system: float
    diff: float
    status: str

# 门店对账明细响应模型
class StoreReconciliationDetailResponse(BaseModel):
    store_id: UUID
    store_name: str
    month: str
    daily_sales: List[StoreReconciliationDetailItem]
    sales_channels: List[ChannelSalesReconciliationItem]
    recharge_data: Dict[str, RechargeReconciliationData]

# 渠道对账数据模型
class ChannelReconciliationData(BaseModel):
    total_reported_sales: float
    total_system_sales: float
    total_sales_diff: float
    total_reported_recharge: float
    total_system_recharge: float
    total_recharge_diff: float
    status: str
    channels: List[ChannelSalesReconciliationItem]

# 财务对账统计响应模型
class FinancialReconciliationStatisticsResponse(BaseModel):
    stores: List[FinancialReconciliationResponse]
    platform_data: ChannelReconciliationData
