import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, Link } from 'react-router-dom';
import {
  Card,
  Breadcrumb,
  Button,
  Table,
  Spin,
  message,
  Input,
  Space,
  Tag,
  Typography,
  DatePicker,
  Select,
  Statistic,
  Row,
  Col,
  Modal,
  Tooltip,
  Divider
} from 'antd';
import {
  ArrowLeftOutlined,
  SearchOutlined,
  ReloadOutlined,
  ExportOutlined,
  EyeOutlined,
  PieChartOutlined,
  BarChartOutlined
} from '@ant-design/icons';
import dayjs from "dayjs";
import apiService from './service/api/marketingGame';

const { Title, Text } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;

const GameRecords = () => {
  const { gameId } = useParams();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [game, setGame] = useState(null);
  const [records, setRecords] = useState([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });
  const [searchText, setSearchText] = useState('');
  const [dateRange, setDateRange] = useState(null);
  const [prizeFilter, setPrizeFilter] = useState(null);
  const [prizes, setPrizes] = useState([]);
  const [stats, setStats] = useState({
    totalRecords: 0,
    totalWins: 0,
    winRate: 0,
    totalPrizeValue: 0
  });
  const [showStatsModal, setShowStatsModal] = useState(false);
  const [statsType, setStatsType] = useState('pie');

  // 获取游戏详情
  const fetchGameDetail = async () => {
    try {
      const response = await apiService.getGameDetail(gameId);
      if (response.success) {
        setGame(response.data);
      } else {
        message.error(response.message || '获取游戏详情失败');
      }
    } catch (error) {
      console.error('获取游戏详情失败:', error);
      message.error('获取游戏详情失败');
    }
  };

  // 获取奖品列表
  const fetchPrizes = async () => {
    try {
      const response = await apiService.getPrizes(gameId);
      if (response.success) {
        setPrizes(response.data || []);
      } else {
        message.error(response.message || '获取奖品列表失败');
      }
    } catch (error) {
      console.error('获取奖品列表失败:', error);
      message.error('获取奖品列表失败');
    }
  };

  // 获取游戏记录
  const fetchRecords = async (page = 1, pageSize = 10, params = {}) => {
    setLoading(true);
    try {
      const queryParams = {
        page,
        page_size: pageSize,
        search: params.search || '',
        start_time: params.startTime,
        end_time: params.endTime,
        prize_id: params.prizeId
      };

      const response = await apiService.getRecords(gameId, queryParams);
      if (response.success) {
        // 处理不同的响应格式
        let recordsData = [];
        let totalCount = 0;

        if (Array.isArray(response.data)) {
          // 如果是数组，直接使用
          recordsData = response.data;
          totalCount = response.data.length;
        } else if (response.data && typeof response.data === 'object') {
          // 如果是对象，尝试获取items属性
          recordsData = response.data.items || response.data.results || [];
          totalCount = response.data.total || response.data.count || recordsData.length;
        }

        // 确保每个记录对象都有必要的属性
        const formattedRecords = recordsData.map(r => ({
          ...r,
          id: r.id || r.record_id || `temp_${Math.random().toString(36).substring(2, 9)}`,
          user_name: r.user_name || (r.participant && r.participant.name) || '匿名用户',
          phone: r.phone || (r.participant && r.participant.phone) || r.user_phone || '',
          prize_name: r.prize_name || (r.prize && r.prize.name) || '未知奖品',
          prize_value: r.prize_value || (r.prize && r.prize.value) || 0,
          is_win: r.is_win !== undefined ? r.is_win : (r.prize && !r.prize.is_default) || false,
          created_at: r.created_at || r.draw_time || new Date().toISOString()
        }));

        setRecords(formattedRecords);
        setPagination({
          current: page,
          pageSize,
          total: totalCount
        });

        // 计算统计数据
        const totalRecords = totalCount;
        const totalWins = formattedRecords.filter(r => r.is_win).length;
        const winRate = totalRecords > 0 ? (totalWins / totalRecords) * 100 : 0;
        const totalPrizeValue = formattedRecords.reduce((sum, r) => sum + (r.is_win ? r.prize_value : 0), 0);

        setStats({
          totalRecords,
          totalWins,
          winRate,
          totalPrizeValue
        });
      } else {
        message.error(response.message || '获取游戏记录失败');
      }
    } catch (error) {
      console.error('获取游戏记录失败:', error);
      message.error('获取游戏记录失败');
    } finally {
      setLoading(false);
    }
  };

  // 初始加载
  useEffect(() => {
    const fetchData = async () => {
      await fetchGameDetail();
      await fetchPrizes();
      await fetchRecords();
    };

    if (gameId) {
      fetchData();
    }
  }, [gameId]);

  // 处理表格变化
  const handleTableChange = (pagination) => {
    fetchRecords(pagination.current, pagination.pageSize, {
      search: searchText,
      startTime: dateRange ? dateRange[0].format('YYYY-MM-DD HH:mm:ss') : null,
      endTime: dateRange ? dateRange[1].format('YYYY-MM-DD HH:mm:ss') : null,
      prizeId: prizeFilter
    });
  };

  // 处理搜索
  const handleSearch = () => {
    fetchRecords(1, pagination.pageSize, {
      search: searchText,
      startTime: dateRange ? dateRange[0].format('YYYY-MM-DD HH:mm:ss') : null,
      endTime: dateRange ? dateRange[1].format('YYYY-MM-DD HH:mm:ss') : null,
      prizeId: prizeFilter
    });
  };

  // 处理重置
  const handleReset = () => {
    setSearchText('');
    setDateRange(null);
    setPrizeFilter(null);
    fetchRecords(1, pagination.pageSize, {});
  };

  // 导出记录数据
  const handleExport = () => {
    // 在实际应用中，这里应该调用API导出记录数据
    message.success('导出成功');
  };

  // 显示统计图表
  const handleShowStats = (type) => {
    setStatsType(type);
    setShowStatsModal(true);
  };

  // 表格列定义
  const columns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80
    },
    {
      title: '用户名',
      dataIndex: 'user_name',
      key: 'user_name',
      render: (text) => <a>{text}</a>
    },
    {
      title: '手机号',
      dataIndex: 'phone',
      key: 'phone'
    },
    {
      title: '奖品',
      dataIndex: 'prize_name',
      key: 'prize_name'
    },
    {
      title: '奖品价值',
      dataIndex: 'prize_value',
      key: 'prize_value',
      render: (value) => `¥${value ? value.toFixed(2) : '0.00'}`
    },
    {
      title: '是否中奖',
      dataIndex: 'is_win',
      key: 'is_win',
      render: (isWin) => (
        <Tag color={isWin ? 'success' : 'default'}>
          {isWin ? '中奖' : '未中奖'}
        </Tag>
      )
    },
    {
      title: '抽奖时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (time) => dayjs(time).format('YYYY-MM-DD HH:mm:ss')
    },
    {
      title: '操作',
      key: 'action',
      width: 100,
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => {
                // 查看记录详情
                Modal.info({
                  title: '抽奖记录详情',
                  content: (
                    <div>
                      <p><strong>用户名:</strong> {record.user_name}</p>
                      <p><strong>手机号:</strong> {record.phone}</p>
                      <p><strong>奖品:</strong> {record.prize_name}</p>
                      <p><strong>奖品价值:</strong> ¥{record.prize_value ? record.prize_value.toFixed(2) : '0.00'}</p>
                      <p><strong>是否中奖:</strong> {record.is_win ? '是' : '否'}</p>
                      <p><strong>抽奖时间:</strong> {dayjs(record.created_at).format('YYYY-MM-DD HH:mm:ss')}</p>
                    </div>
                  ),
                  width: 500
                });
              }}
            />
          </Tooltip>
        </Space>
      )
    }
  ];

  return (
    <div className="game-records">
      <Card>
        <Breadcrumb style={{ marginBottom: 16 }}>
          <Breadcrumb.Item>
            <Link to="/project/plugin/marketing_game">营销游戏</Link>
          </Breadcrumb.Item>
          <Breadcrumb.Item>
            <Link to={`/project/plugin/marketing_game/detail/${gameId}`}>游戏详情</Link>
          </Breadcrumb.Item>
          <Breadcrumb.Item>游戏记录</Breadcrumb.Item>
        </Breadcrumb>

        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
          <Space>
            <Button
              icon={<ArrowLeftOutlined />}
              onClick={() => navigate('/project/plugins/marketing_game')}
            >
              返回
            </Button>
            <Title level={4} style={{ margin: 0 }}>
              游戏记录
              {game && (
                <Text type="secondary" style={{ fontSize: 14, marginLeft: 8 }}>
                  ({game.name})
                </Text>
              )}
            </Title>
          </Space>

          <Space>
            <Input
              placeholder="搜索用户名或手机号"
              value={searchText}
              onChange={(e) => setSearchText(e.target.value)}
              onPressEnter={handleSearch}
              style={{ width: 200 }}
              prefix={<SearchOutlined />}
            />
            <RangePicker
              showTime
              format="YYYY-MM-DD HH:mm:ss"
              placeholder={['开始时间', '结束时间']}
              value={dateRange}
              onChange={(dates) => setDateRange(dates)}
            />
            <Select
              placeholder="选择奖品"
              style={{ width: 150 }}
              allowClear
              value={prizeFilter}
              onChange={(value) => setPrizeFilter(value)}
            >
              {prizes.map(prize => (
                <Option key={prize.id} value={prize.id}>{prize.name}</Option>
              ))}
            </Select>
            <Button onClick={handleSearch}>搜索</Button>
            <Button icon={<ReloadOutlined />} onClick={handleReset}>重置</Button>
            <Button icon={<PieChartOutlined />} onClick={() => handleShowStats('pie')}>
              饼图统计
            </Button>
            <Button icon={<BarChartOutlined />} onClick={() => handleShowStats('bar')}>
              柱状图统计
            </Button>
            <Button icon={<ExportOutlined />} onClick={handleExport}>
              导出
            </Button>
          </Space>
        </div>

        <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
          <Col span={6}>
            <Card>
              <Statistic title="总抽奖次数" value={stats.totalRecords} />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic title="中奖次数" value={stats.totalWins} />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="中奖率"
                value={stats.winRate}
                precision={2}
                suffix="%"
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="奖品总价值"
                value={stats.totalPrizeValue}
                precision={2}
                prefix="¥"
              />
            </Card>
          </Col>
        </Row>

        <Table
          columns={columns}
          dataSource={records}
          rowKey="id"
          pagination={pagination}
          loading={loading}
          onChange={handleTableChange}
        />
      </Card>

      {/* 统计图表弹窗 */}
      <Modal
        title="抽奖统计"
        open={showStatsModal}
        onCancel={() => setShowStatsModal(false)}
        footer={null}
        width={800}
      >
        <div style={{ height: 400, display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
          {statsType === 'pie' ? (
            <div>
              <h3 style={{ textAlign: 'center' }}>奖品分布饼图</h3>
              <img
                src="https://gw.alipayobjects.com/zos/rmsportal/KDpgvguMpGfqaHPjicRK.svg"
                alt="饼图示例"
                style={{ width: '100%', maxWidth: 500 }}
              />
              <p style={{ textAlign: 'center', marginTop: 20 }}>
                注：此为示例图片，实际应用中应使用真实数据生成图表
              </p>
            </div>
          ) : (
            <div>
              <h3 style={{ textAlign: 'center' }}>每日抽奖次数柱状图</h3>
              <img
                src="https://gw.alipayobjects.com/zos/rmsportal/KDpgvguMpGfqaHPjicRK.svg"
                alt="柱状图示例"
                style={{ width: '100%', maxWidth: 500 }}
              />
              <p style={{ textAlign: 'center', marginTop: 20 }}>
                注：此为示例图片，实际应用中应使用真实数据生成图表
              </p>
            </div>
          )}
        </div>
      </Modal>
    </div>
  );
};

export default GameRecords;
