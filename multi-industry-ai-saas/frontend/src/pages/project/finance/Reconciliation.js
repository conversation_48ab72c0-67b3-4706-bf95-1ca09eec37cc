import React, { useState, useEffect } from 'react';
import {
  Card, Row, Col, Table, Tabs, DatePicker, Button, Statistic,
  Tag, Typography, Space, Spin, Modal, Alert, Divider, Empty,
  Select, Tooltip, message, Upload, Form, Input
} from 'antd';
import {
  SyncOutlined, FileSearchOutlined,
  CheckCircleOutlined, CloseCircleOutlined, WarningOutlined,
  ArrowUpOutlined, ArrowDownOutlined, DownloadOutlined,
  FilterOutlined, InfoCircleOutlined, UploadOutlined, InboxOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import dayjs from "dayjs";
import { Pie, Column } from '@ant-design/plots';
import apiService from '../../../services/api';
import './Reconciliation.css';

const { Title, Text } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;
const { Dragger } = Upload;
const { RangePicker } = DatePicker;

/**
 * 财务对账组件
 */
const Reconciliation = () => {
  // 状态定义
  const [loading, setLoading] = useState(false);
  const [detailLoading, setDetailLoading] = useState(false);
  const [reconciliationData, setReconciliationData] = useState(null);
  const [stores, setStores] = useState([]);
  const [selectedMonth, setSelectedMonth] = useState(dayjs());
  const [selectedStoreId, setSelectedStoreId] = useState(null);
  const [activeTab, setActiveTab] = useState('1');
  const [detailVisible, setDetailVisible] = useState(false);
  const [currentStore, setCurrentStore] = useState(null);
  const [detailData, setDetailData] = useState(null);
  const [uploadVisible, setUploadVisible] = useState(false);
  const [uploadForm] = Form.useForm();
  const [uploadLoading, setUploadLoading] = useState(false);

  const navigate = useNavigate();

  // 初始化
  useEffect(() => {
    fetchStores();
  }, []);

  // 监听月份变化（移除门店ID依赖，避免详情页面触发不必要的列表刷新）
  useEffect(() => {
    if (selectedMonth) {
      fetchReconciliationData();
    }
  }, [selectedMonth]);

  // 获取门店列表
  const fetchStores = async () => {
    try {
      const response = await apiService.project.store.getList();
      if (response && response.items) {
        setStores(response.items);
      }
    } catch (error) {
      console.error('获取门店列表失败:', error);
      message.error('获取门店列表失败');
    }
  };

  // 获取对账数据
  const fetchReconciliationData = async () => {
    setLoading(true);
    try {
      const month = selectedMonth.format('YYYY-MM');
      const params = { month };

      // 如果选择了门店，添加门店ID参数
      if (selectedStoreId) {
        params.store_id = selectedStoreId;
      }

      const response = await apiService.project.finance.getReconciliationStatistics(month, selectedStoreId);
      if (response) {
        console.log('原始对账数据:', response);

        // 处理数据，确保所有必要的字段都存在
        const processedData = {
          ...response,
          stores: Array.isArray(response.stores) ? response.stores.map(store => {
            return {
              ...store,
              // 确保所有数值字段都有默认值
              reported_sales: store.reported_sales || 0,
              system_sales: store.system_sales || 0,
              sales_diff: store.sales_diff || 0,
              reported_recharge: store.reported_recharge || 0,
              system_recharge: store.system_recharge || 0,
              recharge_diff: store.recharge_diff || 0,
              // 确保状态字段有默认值
              status: store.status || 'normal'
            };
          }) : [],
          platform_data: response.platform_data ? {
            ...response.platform_data,
            // 确保平台数据中的所有数值字段都有默认值
            total_reported_sales: response.platform_data.total_reported_sales || 0,
            total_system_sales: response.platform_data.total_system_sales || 0,
            total_sales_diff: response.platform_data.total_sales_diff || 0,
            total_reported_recharge: response.platform_data.total_reported_recharge || 0,
            total_system_recharge: response.platform_data.total_system_recharge || 0,
            total_recharge_diff: response.platform_data.total_recharge_diff || 0,
            // 确保状态字段有默认值
            status: response.platform_data.status || 'normal',
            // 确保渠道数据是数组
            channels: Array.isArray(response.platform_data.channels) ? response.platform_data.channels : []
          } : {
            total_reported_sales: 0,
            total_system_sales: 0,
            total_sales_diff: 0,
            total_reported_recharge: 0,
            total_system_recharge: 0,
            total_recharge_diff: 0,
            status: 'normal',
            channels: []
          }
        };

        setReconciliationData(processedData);
        console.log('处理后的对账数据:', processedData);
      } else {
        console.error('获取对账数据失败:', response);
        // 设置默认数据
        setReconciliationData({
          stores: [],
          platform_data: {
            total_reported_sales: 0,
            total_system_sales: 0,
            total_sales_diff: 0,
            total_reported_recharge: 0,
            total_system_recharge: 0,
            total_recharge_diff: 0,
            status: 'normal',
            channels: []
          }
        });
      }
    } catch (error) {
      console.error('获取对账数据错误:', error);
      // 设置默认数据
      setReconciliationData({
        stores: [],
        platform_data: {
          total_reported_sales: 0,
          total_system_sales: 0,
          total_sales_diff: 0,
          total_reported_recharge: 0,
          total_system_recharge: 0,
          total_recharge_diff: 0,
          status: 'normal',
          channels: []
        }
      });
    } finally {
      setLoading(false);
    }
  };

  // 获取门店对账明细
  const fetchStoreDetail = async (storeId) => {
    // 防止重复调用
    if (detailLoading) {
      console.log('详情数据正在加载中，跳过重复请求');
      return;
    }
    
    setDetailLoading(true);
    try {
      const month = selectedMonth.format('YYYY-MM');
      console.log(`获取门店对账明细: 门店ID=${storeId}, 月份=${month}`);
      const response = await apiService.project.finance.getStoreReconciliationDetail(storeId, month);
      if (response) {
        setDetailData(response);
        console.log('门店对账明细获取成功');
      } else {
        console.error('获取门店对账明细失败:', response);
        message.error('获取门店对账明细失败');
      }
    } catch (error) {
      console.error('获取门店对账明细错误:', error);
      message.error('获取门店对账明细失败');
    } finally {
      setDetailLoading(false);
    }
  };

  // 查看门店对账明细
  const handleViewDetail = (store) => {
    setCurrentStore(store);
    setDetailVisible(true);
    // 先显示模态框，再获取详情数据，避免不必要的重复调用
    fetchStoreDetail(store.id);
  };

  // 月份选择变化
  const handleMonthChange = (date) => {
    setSelectedMonth(date);
  };

  // 门店选择变化
  const handleStoreChange = (value) => {
    setSelectedStoreId(value);
  };

  // 标签页切换
  const handleTabChange = (key) => {
    setActiveTab(key);
  };

  // 上传对账数据
  const handleUpload = () => {
    setUploadVisible(true);
  };

  // 提交上传表单
  const handleUploadSubmit = async (values) => {
    if (!values.file || values.file.length === 0) {
      message.error('请选择要上传的文件');
      return;
    }

    setUploadLoading(true);
    try {
      const formData = new FormData();
      formData.append('file', values.file[0].originFileObj);
      formData.append('month', selectedMonth.format('YYYY-MM'));
      formData.append('upload_type', values.upload_type);

      const response = await apiService.project.finance.uploadReconciliationData(formData);
      if (response) {
        message.success('上传成功');
        setUploadVisible(false);
        uploadForm.resetFields();
        fetchReconciliationData();
      }
    } catch (error) {
      console.error('上传对账数据失败:', error);
      message.error('上传对账数据失败');
    } finally {
      setUploadLoading(false);
    }
  };

  // 导出对账报告
  const handleExport = () => {
    message.info('导出功能开发中...');
  };

  // 渲染状态标签
  const renderStatusTag = (status) => {
    switch (status) {
      case 'normal':
        return <Tag color="success" icon={<CheckCircleOutlined />}>正常</Tag>;
      case 'warning':
        return <Tag color="warning" icon={<WarningOutlined />}>警告</Tag>;
      case 'error':
        return <Tag color="error" icon={<CloseCircleOutlined />}>异常</Tag>;
      default:
        return <Tag>未知</Tag>;
    }
  };

  // 渲染差异值
  const renderDiffValue = (value) => {
    if (value === 0) {
      return <Text>0.00</Text>;
    }

    if (value > 0) {
      return <Text type="success">+{value.toFixed(2)} <ArrowUpOutlined /></Text>;
    }

    return <Text type="danger">{value.toFixed(2)} <ArrowDownOutlined /></Text>;
  };

  // 门店对账表格列定义
  const storeColumns = [
    {
      title: '门店名称',
      dataIndex: 'store_name',
      key: 'store_name',
      width: 150,
      render: (text, record) => {
        // 直接使用后端返回的 store_name
        return text || '未知门店';
      }
    },
    {
      title: '上报销售额(元)',
      dataIndex: 'reported_sales',
      key: 'reported_sales',
      width: 120,
      render: (value) => value.toFixed(2),
    },
    {
      title: '系统销售额(元)',
      dataIndex: 'system_sales',
      key: 'system_sales',
      width: 120,
      render: (value) => value.toFixed(2),
    },
    {
      title: '销售差异(元)',
      dataIndex: 'sales_diff',
      key: 'sales_diff',
      width: 120,
      render: (value) => renderDiffValue(value),
    },
    {
      title: '上报充值额(元)',
      dataIndex: 'reported_recharge',
      key: 'reported_recharge',
      width: 120,
      render: (value) => value.toFixed(2),
    },
    {
      title: '系统充值额(元)',
      dataIndex: 'system_recharge',
      key: 'system_recharge',
      width: 120,
      render: (value) => value.toFixed(2),
    },
    {
      title: '充值差异(元)',
      dataIndex: 'recharge_diff',
      key: 'recharge_diff',
      width: 120,
      render: (value) => renderDiffValue(value),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status) => renderStatusTag(status),
    },
    {
      title: '操作',
      key: 'action',
      width: 100,
      render: (_, record) => (
        <Button
          type="link"
          icon={<FileSearchOutlined />}
          onClick={() => handleViewDetail(record)}
        >
          查看明细
        </Button>
      ),
    },
  ];

  // 渠道对账表格列定义
  const channelColumns = [
    {
      title: '渠道名称',
      dataIndex: 'channel_name',
      key: 'channel_name',
      width: 150,
    },
    {
      title: '上报金额(元)',
      dataIndex: 'reported_amount',
      key: 'reported_amount',
      width: 120,
      render: (value) => value.toFixed(2),
    },
    {
      title: '系统金额(元)',
      dataIndex: 'system_amount',
      key: 'system_amount',
      width: 120,
      render: (value) => value.toFixed(2),
    },
    {
      title: '差异金额(元)',
      dataIndex: 'diff',
      key: 'diff',
      width: 120,
      render: (value) => renderDiffValue(value),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status) => renderStatusTag(status),
    },
  ];

  // 门店对账明细表格列定义
  const storeDetailColumns = [
    {
      title: '日期',
      dataIndex: 'date',
      key: 'date',
      width: 120,
    },
    {
      title: '上报销售额(元)',
      dataIndex: 'reported_sales',
      key: 'reported_sales',
      width: 120,
      render: (value) => value.toFixed(2),
    },
    {
      title: '系统销售额(元)',
      dataIndex: 'system_sales',
      key: 'system_sales',
      width: 120,
      render: (value) => value.toFixed(2),
    },
    {
      title: '销售差异(元)',
      dataIndex: 'sales_diff',
      key: 'sales_diff',
      width: 120,
      render: (value) => renderDiffValue(value),
    },
    {
      title: '上报充值额(元)',
      dataIndex: 'reported_recharge',
      key: 'reported_recharge',
      width: 120,
      render: (value) => value.toFixed(2),
    },
    {
      title: '系统充值额(元)',
      dataIndex: 'system_recharge',
      key: 'system_recharge',
      width: 120,
      render: (value) => value.toFixed(2),
    },
    {
      title: '充值差异(元)',
      dataIndex: 'recharge_diff',
      key: 'recharge_diff',
      width: 120,
      render: (value) => renderDiffValue(value),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status) => renderStatusTag(status),
    },
  ];

  // 渲染门店对账明细模态框
  const renderStoreDetailModal = () => {
    if (!detailData || !currentStore) {
      return null;
    }

    return (
      <Modal
        title={`${currentStore.name} - ${selectedMonth.format('YYYY年MM月')}对账明细`}
        visible={detailVisible}
        onCancel={() => setDetailVisible(false)}
        width={1000}
        footer={[
          <Button key="close" onClick={() => setDetailVisible(false)}>
            关闭
          </Button>,
          <Button
            key="export"
            type="primary"
            icon={<DownloadOutlined />}
            onClick={handleExport}
          >
            导出明细
          </Button>,
        ]}
      >
        <Spin spinning={detailLoading}>
          <Tabs defaultActiveKey="1">
            <TabPane tab="每日销售对账" key="1">
              <Table
                columns={storeDetailColumns}
                dataSource={detailData.daily_sales}
                rowKey="date"
                pagination={false}
                scroll={{ y: 300 }}
                size="small"
              />
            </TabPane>
            <TabPane tab="渠道销售对账" key="2">
              <Table
                columns={channelColumns}
                dataSource={detailData.sales_channels}
                rowKey="channel_id"
                pagination={false}
                size="small"
              />
            </TabPane>
            <TabPane tab="充值/售卡对账" key="3">
              <Row gutter={16}>
                <Col span={12}>
                  <Card title="会员充值对账">
                    <Statistic
                      title="上报金额(元)"
                      value={detailData.recharge_data.member_recharge.reported}
                      precision={2}
                    />
                    <Statistic
                      title="系统金额(元)"
                      value={detailData.recharge_data.member_recharge.system}
                      precision={2}
                      style={{ marginTop: 16 }}
                    />
                    <Statistic
                      title="差异金额(元)"
                      value={detailData.recharge_data.member_recharge.diff}
                      precision={2}
                      valueStyle={
                        detailData.recharge_data.member_recharge.diff === 0
                          ? {}
                          : detailData.recharge_data.member_recharge.diff > 0
                          ? { color: '#3f8600' }
                          : { color: '#cf1322' }
                      }
                      prefix={
                        detailData.recharge_data.member_recharge.diff === 0
                          ? null
                          : detailData.recharge_data.member_recharge.diff > 0
                          ? <ArrowUpOutlined />
                          : <ArrowDownOutlined />
                      }
                      style={{ marginTop: 16 }}
                    />
                    <div style={{ marginTop: 16 }}>
                      状态: {renderStatusTag(detailData.recharge_data.member_recharge.status)}
                    </div>
                  </Card>
                </Col>
                <Col span={12}>
                  <Card title="礼品卡销售对账">
                    <Statistic
                      title="上报金额(元)"
                      value={detailData.recharge_data.card_sales.reported}
                      precision={2}
                    />
                    <Statistic
                      title="系统金额(元)"
                      value={detailData.recharge_data.card_sales.system}
                      precision={2}
                      style={{ marginTop: 16 }}
                    />
                    <Statistic
                      title="差异金额(元)"
                      value={detailData.recharge_data.card_sales.diff}
                      precision={2}
                      valueStyle={
                        detailData.recharge_data.card_sales.diff === 0
                          ? {}
                          : detailData.recharge_data.card_sales.diff > 0
                          ? { color: '#3f8600' }
                          : { color: '#cf1322' }
                      }
                      prefix={
                        detailData.recharge_data.card_sales.diff === 0
                          ? null
                          : detailData.recharge_data.card_sales.diff > 0
                          ? <ArrowUpOutlined />
                          : <ArrowDownOutlined />
                      }
                      style={{ marginTop: 16 }}
                    />
                    <div style={{ marginTop: 16 }}>
                      状态: {renderStatusTag(detailData.recharge_data.card_sales.status)}
                    </div>
                  </Card>
                </Col>
              </Row>
            </TabPane>
          </Tabs>
        </Spin>
      </Modal>
    );
  };

  // 渲染上传对账数据模态框
  const renderUploadModal = () => {
    return (
      <Modal
        title="上传对账数据"
        visible={uploadVisible}
        onCancel={() => setUploadVisible(false)}
        footer={null}
      >
        <Form
          form={uploadForm}
          layout="vertical"
          onFinish={handleUploadSubmit}
        >
          <Form.Item
            name="upload_type"
            label="上传类型"
            rules={[{ required: true, message: '请选择上传类型' }]}
          >
            <Select placeholder="请选择上传类型">
              <Option value="store">门店对账数据</Option>
              <Option value="channel">渠道对账数据</Option>
            </Select>
          </Form.Item>
          <Form.Item
            name="file"
            label="对账数据文件"
            rules={[{ required: true, message: '请上传对账数据文件' }]}
          >
            <Dragger
              name="file"
              multiple={false}
              beforeUpload={() => false}
              accept=".xlsx,.xls,.csv"
            >
              <p className="ant-upload-drag-icon">
                <InboxOutlined />
              </p>
              <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
              <p className="ant-upload-hint">
                支持 Excel 或 CSV 格式文件，请确保文件格式正确
              </p>
            </Dragger>
          </Form.Item>
          <Form.Item>
            <Button
              type="primary"
              htmlType="submit"
              loading={uploadLoading}
              block
            >
              上传
            </Button>
          </Form.Item>
        </Form>
      </Modal>
    );
  };

  // 渲染页面内容
  return (
    <div className="reconciliation-container">
      <Card>
        <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between' }}>
          <Title level={4} style={{ margin: 0 }}>财务对账</Title>
          <Space>
            <DatePicker
              picker="month"
              value={selectedMonth}
              onChange={handleMonthChange}
              allowClear={false}
            />
            <Select
              placeholder="选择门店"
              style={{ width: 200 }}
              allowClear
              onChange={handleStoreChange}
              value={selectedStoreId}
            >
              {stores.map(store => (
                <Option key={store.id} value={store.id}>{store.name}</Option>
              ))}
            </Select>
            <Button
              type="primary"
              icon={<UploadOutlined />}
              onClick={handleUpload}
            >
              上传对账数据
            </Button>
            <Button
              icon={<DownloadOutlined />}
              onClick={handleExport}
            >
              导出报告
            </Button>
          </Space>
        </div>

        <Tabs activeKey={activeTab} onChange={handleTabChange}>
          <TabPane tab="门店对账" key="1">
            <Spin spinning={loading}>
              {reconciliationData && reconciliationData.stores ? (
                <>
                  {/* 汇总信息 */}
                  <Row gutter={16} style={{ marginBottom: 16 }}>
                    <Col span={6}>
                      <Card>
                        <Statistic
                          title="门店总数"
                          value={reconciliationData.stores.length}
                          suffix={`/ ${reconciliationData.stores.length}`}
                        />
                      </Card>
                    </Col>
                    <Col span={6}>
                      <Card>
                        <Statistic
                          title="异常门店数"
                          value={reconciliationData.stores.filter(s => s.status === 'warning' || s.status === 'error').length}
                          valueStyle={
                            reconciliationData.stores.filter(s => s.status === 'warning' || s.status === 'error').length > 0
                              ? { color: '#faad14' }
                              : { color: '#3f8600' }
                          }
                        />
                      </Card>
                    </Col>
                    <Col span={6}>
                      <Card>
                        <Statistic
                          title="销售差异总额"
                          value={reconciliationData.stores.reduce((sum, store) => sum + Math.abs(store.sales_diff), 0)}
                          precision={2}
                          valueStyle={{ color: '#cf1322' }}
                          suffix="元"
                        />
                      </Card>
                    </Col>
                    <Col span={6}>
                      <Card>
                        <Statistic
                          title="充值差异总额"
                          value={reconciliationData.stores.reduce((sum, store) => sum + Math.abs(store.recharge_diff), 0)}
                          precision={2}
                          valueStyle={{ color: '#cf1322' }}
                          suffix="元"
                        />
                      </Card>
                    </Col>
                  </Row>

                  {/* 门店对账表格 */}
                  <Table
                    columns={storeColumns}
                    dataSource={reconciliationData.stores.map(store => {
                      console.log('门店数据:', store);
                      return {
                        ...store,
                        // 确保门店名称显示
                        name: store.store_name || store.name || '未知门店'
                      };
                    })}
                    rowKey={(record) => record.id || record.store_id}
                    pagination={{
                      pageSize: 10,
                      showSizeChanger: true,
                      showTotal: (total) => `共 ${total} 条记录`
                    }}
                  />
                </>
              ) : (
                <Empty description="暂无门店对账数据" />
              )}
            </Spin>
          </TabPane>
          <TabPane tab="渠道对账" key="2">
            <Spin spinning={loading}>
              {reconciliationData && reconciliationData.platform_data ? (
                <Row gutter={16}>
                  <Col span={24}>
                    <Card
                      title="渠道销售对账汇总"
                      className="gradient-card"
                      extra={<Tooltip title="查看各销售渠道的对账情况"><InfoCircleOutlined /></Tooltip>}
                    >
                      <Row gutter={16}>
                        <Col span={8}>
                          <Statistic
                            title="上报总销售额(元)"
                            value={reconciliationData.platform_data.total_reported_sales}
                            precision={2}
                            valueStyle={{ fontSize: '24px', fontWeight: 'bold' }}
                          />
                        </Col>
                        <Col span={8}>
                          <Statistic
                            title="系统总销售额(元)"
                            value={reconciliationData.platform_data.total_system_sales}
                            precision={2}
                            valueStyle={{ fontSize: '24px', fontWeight: 'bold' }}
                          />
                        </Col>
                        <Col span={8}>
                          <Statistic
                            title="差异金额(元)"
                            value={reconciliationData.platform_data.total_sales_diff}
                            precision={2}
                            valueStyle={{
                              fontSize: '24px',
                              fontWeight: 'bold',
                              color: reconciliationData.platform_data.total_sales_diff === 0
                                ? 'inherit'
                                : Math.abs(reconciliationData.platform_data.total_sales_diff) > 0.01
                                  ? '#cf1322'
                                  : 'inherit'
                            }}
                            prefix={
                              reconciliationData.platform_data.total_sales_diff > 0
                                ? <ArrowUpOutlined />
                                : reconciliationData.platform_data.total_sales_diff < 0
                                  ? <ArrowDownOutlined />
                                  : null
                            }
                          />
                        </Col>
                      </Row>
                    </Card>
                  </Col>

                  <Col span={24} style={{ marginTop: 16 }}>
                    <Card
                      title="渠道充值/售卡对账汇总"
                      className="gradient-card"
                      extra={<Tooltip title="查看各渠道的充值和售卡情况"><InfoCircleOutlined /></Tooltip>}
                    >
                      <Row gutter={16}>
                        <Col span={8}>
                          <Statistic
                            title="上报总充值/售卡额(元)"
                            value={reconciliationData.platform_data.total_reported_recharge}
                            precision={2}
                            valueStyle={{ fontSize: '24px', fontWeight: 'bold' }}
                          />
                        </Col>
                        <Col span={8}>
                          <Statistic
                            title="系统总充值/售卡额(元)"
                            value={reconciliationData.platform_data.total_system_recharge}
                            precision={2}
                            valueStyle={{ fontSize: '24px', fontWeight: 'bold' }}
                          />
                        </Col>
                        <Col span={8}>
                          <Statistic
                            title="差异金额(元)"
                            value={reconciliationData.platform_data.total_recharge_diff}
                            precision={2}
                            valueStyle={{
                              fontSize: '24px',
                              fontWeight: 'bold',
                              color: reconciliationData.platform_data.total_recharge_diff === 0
                                ? 'inherit'
                                : Math.abs(reconciliationData.platform_data.total_recharge_diff) > 0.01
                                  ? '#cf1322'
                                  : 'inherit'
                            }}
                            prefix={
                              reconciliationData.platform_data.total_recharge_diff > 0
                                ? <ArrowUpOutlined />
                                : reconciliationData.platform_data.total_recharge_diff < 0
                                  ? <ArrowDownOutlined />
                                  : null
                            }
                          />
                        </Col>
                      </Row>
                    </Card>
                  </Col>

                  <Col span={24} style={{ marginTop: 16 }}>
                    <Table
                      columns={channelColumns}
                      dataSource={reconciliationData.platform_data.channels}
                      rowKey="channel_id"
                      pagination={false}
                    />
                  </Col>

                  <Col span={24} style={{ marginTop: 16 }}>
                    <Alert
                      message="对账结果"
                      description={
                        <>
                          <p>
                            系统对账状态: {renderStatusTag(reconciliationData.platform_data.status)}
                          </p>
                          <p>
                            建议: {
                              reconciliationData.platform_data.status === 'normal'
                                ? '各项数据正常，无需特别处理。'
                                : '存在数据差异，请查看渠道对账明细，联系相关负责人核实。'
                            }
                          </p>
                        </>
                      }
                      type={reconciliationData.platform_data.status === 'normal' ? 'success' : 'warning'}
                      showIcon
                    />
                  </Col>
                </Row>
              ) : (
                <Empty description="暂无渠道对账数据" />
              )}
            </Spin>
          </TabPane>
        </Tabs>
      </Card>

      {/* 渲染门店对账明细模态框 */}
      {renderStoreDetailModal()}

      {/* 渲染上传对账数据模态框 */}
      {renderUploadModal()}
    </div>
  );
};

export default Reconciliation;