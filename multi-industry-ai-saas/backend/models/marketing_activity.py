#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
营销活动模型
"""

import uuid
from datetime import datetime
from typing import List, Optional
from sqlalchemy import Column, String, Text, ForeignKey, DateTime, Float, Boolean
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship

from db.database import Base

class MarketingActivity(Base):
    """
    营销活动模型
    """
    __tablename__ = "marketing_activities"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    title = Column(String(100), nullable=False, comment="活动标题")
    description = Column(Text, nullable=True, comment="活动描述")
    
    # 活动类型：promotion(促销活动), discount(折扣活动), event(主题活动), etc.
    type = Column(String(50), nullable=True, comment="活动类型")
    
    # 活动状态：draft(草稿), pending(待开始), active(进行中), completed(已结束), cancelled(已取消)
    status = Column(String(20), nullable=False, default="pending", comment="活动状态")
    
    # 活动开始时间
    start_date = Column(DateTime, nullable=False, comment="活动开始时间")
    
    # 活动结束时间
    end_date = Column(DateTime, nullable=False, comment="活动结束时间")
    
    # 折扣率（如果适用）
    discount_rate = Column(Float, nullable=True, comment="折扣率")
    
    # 活动图片URL
    image_url = Column(String(255), nullable=True, comment="活动图片URL")
    
    # 活动创建时间
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")
    
    # 活动更新时间
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")
    
    # 活动创建者
    created_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True, comment="创建者ID")
    
    # 活动更新者
    updated_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True, comment="更新者ID")
    
    # 活动所属项目
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id"), nullable=False, comment="项目ID")
    
    # 活动目标门店
    target_stores = Column(JSONB, default=list, comment="目标门店ID列表")
    
    # 是否适用于所有门店
    is_all_stores = Column(Boolean, default=False, comment="是否适用于所有门店")
    
    # 活动规则
    rules = Column(Text, nullable=True, comment="活动规则")
    
    # 活动附件
    attachments = Column(JSONB, default=list, comment="附件列表")
    
    # 自定义字段
    custom_fields = Column(JSONB, default=dict, comment="自定义字段")
    
    # 关系
    project = relationship("Project", back_populates="marketing_activities")
    creator = relationship("User", foreign_keys=[created_by])
    updater = relationship("User", foreign_keys=[updated_by])
    
    def __repr__(self):
        return f"<MarketingActivity {self.title}>"
