#!/usr/bin/env python
# -*- coding: utf-8 -*-

from sqlalchemy import Column, String, Boolean, DateTime, ForeignKey, JSON, Text, Integer, Float, UniqueConstraint
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid

from db.database import Base

class Loss(Base):
    """门店报损记录模型"""
    __tablename__ = "losses"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id"), nullable=False)
    store_id = Column(UUID(as_uuid=True), ForeignKey("stores.id"), nullable=False)
    product_id = Column(UUID(as_uuid=True), ForeignKey("products.id"), nullable=False)
    quantity = Column(Float, nullable=False)
    product_unit = Column(String(20), nullable=True, comment="产品单位")
    unit_price = Column(Float, nullable=False)
    total_amount = Column(Float, nullable=False)
    reason = Column(String(100), nullable=False)  # 报损原因：过期、损坏、丢失等
    status = Column(String(20), default="pending", nullable=False)  # pending, approved, rejected, withdrawn
    images = Column(JSON, default=list)  # 图片路径列表，使用JSON类型存储
    images_size = Column(Integer, default=0)  # 图片大小，单位为字节
    notes = Column(Text, nullable=True)  # 备注
    created_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    approved_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)
    approved_at = Column(DateTime(timezone=True), nullable=True)
    reject_reason = Column(Text, nullable=True)  # 拒绝原因

    # 关联关系
    project = relationship("Project", backref="losses")
    store = relationship("Store", backref="losses")
    product = relationship("Product", backref="losses")
    creator = relationship("User", foreign_keys=[created_by], backref="created_losses")
    approver = relationship("User", foreign_keys=[approved_by], backref="approved_losses")

    def __repr__(self):
        return f"<Loss(id={self.id}, store_id={self.store_id}, product_id={self.product_id}, quantity={self.quantity}, status={self.status})>"
