import api from '../httpClient';
import { getProjectId } from '../httpClient';

/**
 * 项目-通知API
 */
const notification = {
  /**
   * 获取通知列表
   * @param {Object} params - 查询参数
   * @returns {Promise} - 通知列表
   */
  getList: async (params = {}) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.get(`/project/${projectId}/notifications`, { params });
  },

  /**
   * 获取未读通知数量
   * @returns {Promise} - 未读通知数量
   */
  getUnreadCount: async () => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.get(`/project/${projectId}/notifications/unread-count`);
  },

  /**
   * 标记通知为已读
   * @param {string} id - 通知ID
   * @returns {Promise} - 操作结果
   */
  markAsRead: async (id) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.put(`/project/${projectId}/notifications/${id}/read`);
  },

  /**
   * 标记所有通知为已读
   * @returns {Promise} - 操作结果
   */
  markAllAsRead: async () => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.put(`/project/${projectId}/notifications/read-all`);
  },

  /**
   * 删除通知
   * @param {string} id - 通知ID
   * @returns {Promise} - 操作结果
   */
  delete: async (id) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.delete(`/project/${projectId}/notifications/${id}`);
  },

  /**
   * 删除所有通知
   * @returns {Promise} - 操作结果
   */
  deleteAll: async () => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.delete(`/project/${projectId}/notifications/delete-all`);
  },

  /**
   * 获取通知设置
   * @returns {Promise} - 通知设置
   */
  getSettings: async () => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.get(`/project/${projectId}/notifications/settings`);
  },

  /**
   * 更新通知设置
   * @param {Object} data - 设置数据
   * @returns {Promise} - 操作结果
   */
  updateSettings: async (data) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.put(`/project/${projectId}/notifications/settings`, data);
  }
};

export default notification;
