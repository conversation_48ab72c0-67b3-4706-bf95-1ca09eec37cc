#!/usr/bin/env python
# -*- coding: utf-8 -*-

from pydantic import BaseModel, Field, validator
from typing import Optional, List
from datetime import date, datetime
from uuid import UUID

# 基础模型
class MonthlyInventoryBase(BaseModel):
    """月度盘点基础模型"""
    store_id: UUID = Field(..., description="门店ID")
    product_id: UUID = Field(..., description="商品ID")
    month: date = Field(..., description="盘点月份")
    inventory_quantity: float = Field(0, description="库存量")
    actual_quantity: float = Field(0, description="实际量")
    unit: Optional[str] = Field(None, description="单位")
    remark: Optional[str] = Field(None, description="备注")

    @validator('inventory_quantity', 'actual_quantity')
    def validate_quantities(cls, v):
        if v < 0:
            raise ValueError('数量不能为负数')
        return v

class MonthlyInventoryCreate(MonthlyInventoryBase):
    """创建月度盘点"""
    pass

class MonthlyInventoryUpdate(BaseModel):
    """更新月度盘点"""
    inventory_quantity: Optional[float] = Field(None, description="库存量")
    actual_quantity: Optional[float] = Field(None, description="实际量")
    unit: Optional[str] = Field(None, description="单位")
    remark: Optional[str] = Field(None, description="备注")

    @validator('inventory_quantity', 'actual_quantity')
    def validate_quantities(cls, v):
        if v is not None and v < 0:
            raise ValueError('数量不能为负数')
        return v

class MonthlyInventoryResponse(MonthlyInventoryBase):
    """月度盘点响应模型"""
    id: UUID
    tenant_id: Optional[UUID]
    project_id: UUID
    status: str = Field(..., description="状态")
    submitted_at: Optional[datetime] = Field(None, description="提交时间")
    audited_at: Optional[datetime] = Field(None, description="审核时间")
    audit_remark: Optional[str] = Field(None, description="审核备注")
    created_at: datetime
    updated_at: datetime
    created_by: Optional[UUID]
    updated_by: Optional[UUID]
    audited_by: Optional[UUID]
    
    # 关联数据
    product_name: Optional[str] = Field(None, description="商品名称")
    product_code: Optional[str] = Field(None, description="商品编号")
    specification: Optional[str] = Field(None, description="规格")
    category_name: Optional[str] = Field(None, description="分类名称")
    store_name: Optional[str] = Field(None, description="门店名称")
    
    # 计算字段
    difference: Optional[float] = Field(None, description="差异")
    difference_rate: Optional[float] = Field(None, description="差异率")

    class Config:
        from_attributes = True

class MonthlyInventoryBatchCreate(BaseModel):
    """批量创建月度盘点"""
    items: List[MonthlyInventoryCreate] = Field(..., description="盘点项目列表")

class MonthlyInventorySubmit(BaseModel):
    """提交月度盘点"""
    inventory_ids: List[UUID] = Field(..., description="盘点记录ID列表")

class MonthlyInventoryWithdraw(BaseModel):
    """撤回月度盘点"""
    inventory_ids: List[UUID] = Field(..., description="盘点记录ID列表")

# 汇总模型
class MonthlyInventorySummaryBase(BaseModel):
    """月度盘点汇总基础模型"""
    store_id: UUID = Field(..., description="门店ID")
    month: date = Field(..., description="盘点月份")
    total_items: int = Field(0, description="总盘点商品数")
    completed_items: int = Field(0, description="已完成盘点商品数")
    diff_items: int = Field(0, description="有差异商品数")
    total_diff_amount: float = Field(0, description="总差异金额")

class MonthlyInventorySummaryResponse(MonthlyInventorySummaryBase):
    """月度盘点汇总响应模型"""
    id: UUID
    tenant_id: Optional[UUID]
    project_id: UUID
    status: str = Field(..., description="状态")
    submitted_at: Optional[datetime] = Field(None, description="提交时间")
    audited_at: Optional[datetime] = Field(None, description="审核时间")
    audit_remark: Optional[str] = Field(None, description="审核备注")
    created_at: datetime
    updated_at: datetime
    created_by: Optional[UUID]
    updated_by: Optional[UUID]
    audited_by: Optional[UUID]
    
    # 关联数据
    store_name: Optional[str] = Field(None, description="门店名称")
    
    # 计算字段
    completion_rate: Optional[float] = Field(None, description="完成率")
    diff_rate: Optional[float] = Field(None, description="差异率")

    class Config:
        from_attributes = True

# 审核模型
class MonthlyInventoryAudit(BaseModel):
    """月度盘点审核"""
    store_id: UUID = Field(..., description="门店ID")
    month: date = Field(..., description="盘点月份")
    action: str = Field(..., description="审核动作: approve, reject")
    audit_remark: str = Field(..., description="审核备注")

    @validator('action')
    def validate_action(cls, v):
        if v not in ['approve', 'reject']:
            raise ValueError('审核动作必须是 approve 或 reject')
        return v

class MonthlyInventoryBatchAudit(BaseModel):
    """批量审核月度盘点"""
    items: List[dict] = Field(..., description="审核项目列表，包含store_id和month")
    action: str = Field(..., description="审核动作: approve, reject")
    audit_remark: str = Field(..., description="审核备注")

    @validator('action')
    def validate_action(cls, v):
        if v not in ['approve', 'reject']:
            raise ValueError('审核动作必须是 approve 或 reject')
        return v

# 上传模型
class MonthlyInventoryUploadResponse(BaseModel):
    """月度盘点上传响应模型"""
    id: UUID
    tenant_id: Optional[UUID]
    project_id: UUID
    store_id: UUID
    month: date
    filename: str
    file_path: str
    total_rows: int
    success_rows: int
    error_rows: int
    error_details: Optional[str]
    status: str
    remark: Optional[str]
    created_at: datetime
    created_by: Optional[UUID]
    
    # 关联数据
    store_name: Optional[str] = Field(None, description="门店名称")

    class Config:
        from_attributes = True

# 查询模型
class MonthlyInventoryQuery(BaseModel):
    """月度盘点查询参数"""
    store_id: Optional[UUID] = Field(None, description="门店ID")
    month: Optional[str] = Field(None, description="盘点月份，格式：YYYY-MM")
    category: Optional[UUID] = Field(None, description="商品分类ID")
    status: Optional[str] = Field(None, description="状态")
    page: int = Field(1, ge=1, description="页码")
    page_size: int = Field(20, ge=1, le=100, description="每页数量")

class MonthlyInventoryAuditQuery(BaseModel):
    """月度盘点审核查询参数"""
    store_id: Optional[UUID] = Field(None, description="门店ID")
    month: Optional[str] = Field(None, description="盘点月份，格式：YYYY-MM")
    status: Optional[str] = Field(None, description="状态")
    start_date: Optional[date] = Field(None, description="开始日期")
    end_date: Optional[date] = Field(None, description="结束日期")
    group_by: Optional[str] = Field(None, description="分组方式：store_month")
    page: int = Field(1, ge=1, description="页码")
    page_size: int = Field(20, ge=1, le=100, description="每页数量")

# 统计模型
class MonthlyInventoryStatistics(BaseModel):
    """月度盘点统计"""
    total: int = Field(0, description="总数")
    completed: int = Field(0, description="已完成")
    pending: int = Field(0, description="待处理")
    submitted: int = Field(0, description="已提交")

class MonthlyInventoryAuditStatistics(BaseModel):
    """月度盘点审核统计"""
    total: int = Field(0, description="总数")
    pending: int = Field(0, description="待审核")
    approved: int = Field(0, description="已通过")
    rejected: int = Field(0, description="已驳回")

# 响应模型
class MonthlyInventoryListResponse(BaseModel):
    """月度盘点列表响应"""
    items: List[MonthlyInventoryResponse]
    total: int
    page: int
    page_size: int
    statistics: MonthlyInventoryStatistics

class MonthlyInventorySummaryListResponse(BaseModel):
    """月度盘点汇总列表响应"""
    items: List[MonthlyInventorySummaryResponse]
    total: int
    page: int
    page_size: int
    statistics: MonthlyInventoryAuditStatistics

class MonthlyInventoryItemsResponse(BaseModel):
    """月度盘点明细响应"""
    items: List[MonthlyInventoryResponse]
    total: int
    page: int
    page_size: int 