#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
项目级 AI 助手聊天 API
"""

import logging
import uuid
from typing import Optional, List, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query, Path, status
from sqlalchemy.ext.asyncio import AsyncSession

from db.database import get_db
from models.project import Project
from core.auth import get_current_user
from api.deps import get_current_project
from services.ai import AIAssistantService
from schemas.ai import (
    AIAssistantThreadCreate,
    AIAssistantThreadUpdate,
    AIAssistantThreadResponse,
    AIAssistantThreadListResponse,
    AIAssistantMessageCreate,
    AIAssistantMessageResponse,
    AIAssistantMessageListResponse,
    AIAssistantChatRequest,
    AIAssistantChatResponse,
)

logger = logging.getLogger(__name__)
router = APIRouter()

@router.get("/ai/assistant/threads", response_model=AIAssistantThreadListResponse)
async def list_threads(
    project: Project = Depends(get_current_project),
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    assistant_id: Optional[uuid.UUID] = Query(None),
    status: Optional[str] = Query(None),
):
    """
    获取 AI 助手对话线程列表
    
    只返回当前用户的对话线程
    """
    try:
        threads, total = await AIAssistantService.get_threads(
            db=db,
            skip=skip,
            limit=limit,
            assistant_id=assistant_id,
            user_id=current_user.id,
            status=status,
        )
        
        return {
            "success": True,
            "message": "获取 AI 助手对话线程列表成功",
            "data": threads,
            "total": total,
            "page": skip // limit + 1,
            "page_size": limit,
        }
    except Exception as e:
        logger.error(f"获取 AI 助手对话线程列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取 AI 助手对话线程列表失败: {str(e)}",
        )

@router.get("/ai/assistant/threads/{thread_id}", response_model=AIAssistantThreadResponse)
async def get_thread(
    thread_id: uuid.UUID = Path(...),
    project: Project = Depends(get_current_project),
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    获取 AI 助手对话线程详情
    
    只能获取当前用户的对话线程
    """
    try:
        thread = await AIAssistantService.get_thread(db=db, thread_id=thread_id)
        
        if not thread:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"AI 助手对话线程 ID '{thread_id}' 不存在",
            )
            
        # 检查是否有权限访问该线程
        if thread.user_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问该 AI 助手对话线程",
            )
            
        return {
            "success": True,
            "message": "获取 AI 助手对话线程详情成功",
            "data": thread,
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取 AI 助手对话线程详情失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取 AI 助手对话线程详情失败: {str(e)}",
        )

@router.post("/ai/assistant/threads", response_model=AIAssistantThreadResponse, status_code=status.HTTP_201_CREATED)
async def create_thread(
    thread_data: AIAssistantThreadCreate,
    project: Project = Depends(get_current_project),
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    创建 AI 助手对话线程
    """
    try:
        # 设置用户 ID
        thread_data.user_id = current_user.id
        
        thread = await AIAssistantService.create_thread(db=db, thread_data=thread_data)
        
        return {
            "success": True,
            "message": "创建 AI 助手对话线程成功",
            "data": thread,
        }
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建 AI 助手对话线程失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建 AI 助手对话线程失败: {str(e)}",
        )

@router.put("/ai/assistant/threads/{thread_id}", response_model=AIAssistantThreadResponse)
async def update_thread(
    thread_id: uuid.UUID = Path(...),
    thread_data: AIAssistantThreadUpdate = None,
    project: Project = Depends(get_current_project),
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    更新 AI 助手对话线程
    
    只能更新当前用户的对话线程
    """
    try:
        # 获取线程
        thread = await AIAssistantService.get_thread(db=db, thread_id=thread_id)
        
        if not thread:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"AI 助手对话线程 ID '{thread_id}' 不存在",
            )
            
        # 检查是否有权限更新该线程
        if thread.user_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权更新该 AI 助手对话线程",
            )
            
        thread = await AIAssistantService.update_thread(
            db=db,
            thread_id=thread_id,
            thread_data=thread_data,
        )
        
        return {
            "success": True,
            "message": "更新 AI 助手对话线程成功",
            "data": thread,
        }
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新 AI 助手对话线程失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新 AI 助手对话线程失败: {str(e)}",
        )

@router.delete("/ai/assistant/threads/{thread_id}", response_model=AIAssistantThreadResponse)
async def delete_thread(
    thread_id: uuid.UUID = Path(...),
    project: Project = Depends(get_current_project),
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    删除 AI 助手对话线程
    
    只能删除当前用户的对话线程
    """
    try:
        # 获取线程
        thread = await AIAssistantService.get_thread(db=db, thread_id=thread_id)
        
        if not thread:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"AI 助手对话线程 ID '{thread_id}' 不存在",
            )
            
        # 检查是否有权限删除该线程
        if thread.user_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权删除该 AI 助手对话线程",
            )
            
        success = await AIAssistantService.delete_thread(db=db, thread_id=thread_id)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"AI 助手对话线程 ID '{thread_id}' 不存在",
            )
            
        return {
            "success": True,
            "message": "删除 AI 助手对话线程成功",
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除 AI 助手对话线程失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除 AI 助手对话线程失败: {str(e)}",
        )

@router.get("/ai/assistant/threads/{thread_id}/messages", response_model=AIAssistantMessageListResponse)
async def list_messages(
    thread_id: uuid.UUID = Path(...),
    project: Project = Depends(get_current_project),
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    role: Optional[str] = Query(None),
):
    """
    获取 AI 助手消息列表
    
    只能获取当前用户的对话线程中的消息
    """
    try:
        # 获取线程
        thread = await AIAssistantService.get_thread(db=db, thread_id=thread_id)
        
        if not thread:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"AI 助手对话线程 ID '{thread_id}' 不存在",
            )
            
        # 检查是否有权限访问该线程
        if thread.user_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问该 AI 助手对话线程",
            )
            
        messages, total = await AIAssistantService.get_messages(
            db=db,
            thread_id=thread_id,
            skip=skip,
            limit=limit,
            role=role,
        )
        
        return {
            "success": True,
            "message": "获取 AI 助手消息列表成功",
            "data": messages,
            "total": total,
            "page": skip // limit + 1,
            "page_size": limit,
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取 AI 助手消息列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取 AI 助手消息列表失败: {str(e)}",
        )

@router.post("/ai/assistant/chat", response_model=AIAssistantChatResponse)
async def chat_with_assistant(
    chat_request: AIAssistantChatRequest,
    project: Project = Depends(get_current_project),
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    与 AI 助手对话
    
    发送消息给 AI 助手并获取回复。
    """
    try:
        # 如果指定了线程 ID，检查是否有权限访问该线程
        if chat_request.thread_id:
            thread = await AIAssistantService.get_thread(db=db, thread_id=chat_request.thread_id)
            
            if not thread:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"AI 助手对话线程 ID '{chat_request.thread_id}' 不存在",
                )
                
            if thread.user_id != current_user.id:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="无权访问该 AI 助手对话线程",
                )
                
        # 发送聊天请求
        result = await AIAssistantService.chat_with_assistant(
            db=db,
            chat_request=chat_request,
            user_id=current_user.id,
        )
        
        return result
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"AI 助手对话失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"AI 助手对话失败: {str(e)}",
        )
