/**
 * AI API服务
 */
const aiApi = {
  /**
   * 生成AI提示词
   * @param {Object} params - 参数
   * @returns {Promise} - 生成结果
   */
  generatePrompts: async (params = {}) => {
    try {
      const projectId = localStorage.getItem('project_id');
      if (!projectId) {
        return {
          success: false,
          message: '获取项目ID失败',
          data: []
        };
      }

      // 模拟AI生成的提示词
      const mockPrompts = [
        {
          id: 1,
          title: '欢迎提示',
          content: '欢迎参与我们的营销游戏！点击开始按钮即可参与抽奖！',
          type: 'welcome',
          category: 'greeting'
        },
        {
          id: 2,
          title: '规则说明',
          content: '游戏规则：每人每天可参与一次，中奖概率为20%，奖品丰富多样！',
          type: 'rules',
          category: 'instruction'
        },
        {
          id: 3,
          title: '鼓励参与',
          content: '还没试试手气吗？快来参与抽奖，好运等着你！',
          type: 'encouragement',
          category: 'motivation'
        },
        {
          id: 4,
          title: '分享邀请',
          content: '分享给朋友一起参与，好运加倍！',
          type: 'share',
          category: 'social'
        }
      ];

      return {
        success: true,
        data: mockPrompts,
        message: '生成成功'
      };
    } catch (error) {
      console.error('生成AI提示词失败:', error);
      return {
        success: false,
        data: [],
        message: error.message || '生成失败'
      };
    }
  },

  /**
   * AI内容优化
   * @param {Object} params - 优化参数
   * @returns {Promise} - 优化结果
   */
  optimizeContent: async (params = {}) => {
    try {
      const projectId = localStorage.getItem('project_id');
      if (!projectId) {
        return {
          success: false,
          message: '获取项目ID失败',
          data: null
        };
      }

      // 模拟AI优化结果
      const optimizedContent = {
        original: params.content || '',
        optimized: `优化后的内容：${params.content || '示例内容'}，更加吸引人且符合营销目标。`,
        improvements: [
          '增强了吸引力',
          '优化了语言表达',
          '提高了转化率'
        ],
        score: 85
      };

      return {
        success: true,
        data: optimizedContent,
        message: '优化成功'
      };
    } catch (error) {
      console.error('AI内容优化失败:', error);
      return {
        success: false,
        data: null,
        message: error.message || '优化失败'
      };
    }
  },

  /**
   * 获取AI建议
   * @param {Object} params - 参数
   * @returns {Promise} - 建议列表
   */
  getSuggestions: async (params = {}) => {
    try {
      const projectId = localStorage.getItem('project_id');
      if (!projectId) {
        return {
          success: false,
          message: '获取项目ID失败',
          data: []
        };
      }

      // 模拟AI建议
      const mockSuggestions = [
        {
          id: 1,
          title: '优化奖品设置',
          description: '建议增加更多中等价值奖品，提高用户参与积极性',
          priority: 'high',
          category: 'prize'
        },
        {
          id: 2,
          title: '改进用户界面',
          description: '简化操作流程，提升用户体验',
          priority: 'medium',
          category: 'ui'
        },
        {
          id: 3,
          title: '增强分享功能',
          description: '添加社交媒体分享，扩大传播范围',
          priority: 'medium',
          category: 'social'
        }
      ];

      return {
        success: true,
        data: mockSuggestions,
        message: '获取成功'
      };
    } catch (error) {
      console.error('获取AI建议失败:', error);
      return {
        success: false,
        data: [],
        message: error.message || '获取失败'
      };
    }
  }
};

export default aiApi; 