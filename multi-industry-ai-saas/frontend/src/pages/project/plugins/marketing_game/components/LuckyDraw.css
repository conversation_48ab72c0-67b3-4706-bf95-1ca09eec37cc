/* 全新抽奖页面样式 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes bounce {
  0%, 100% { transform: translateX(-50%) translateY(0); }
  50% { transform: translateX(-50%) translateY(-5px); }
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

@keyframes float {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
}

.lucky-draw-container {
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  padding: 20px;
  position: relative;
  overflow: hidden;
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
  animation: fadeIn 0.8s ease-in-out;
}

.lucky-draw-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.7);
  z-index: 0;
}

.lucky-draw-header {
  position: relative;
  z-index: 1;
  text-align: center;
  margin-bottom: 20px;
  width: 100%;
  max-width: 800px;
}

.game-logo {
  width: 100px;
  height: 100px;
  object-fit: contain;
  margin-bottom: 10px;
  border-radius: 50%;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  background-color: white;
  padding: 5px;
  animation: float 6s ease-in-out infinite;
}

.game-title {
  color: #333;
  margin-bottom: 10px !important;
  font-weight: bold;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.game-description {
  color: #666;
  margin-bottom: 20px !important;
  max-width: 600px;
  margin-left: auto !important;
  margin-right: auto !important;
}

.lucky-draw-content {
  position: relative;
  z-index: 1;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 20px;
  padding: 30px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 1000px;
  margin-bottom: 30px;
}

.lucky-draw-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  width: 100%;
}

.loading-icon {
  font-size: 40px;
  color: #ff5722;
  animation: pulse 1.5s infinite ease-in-out;
}

.loading-text {
  margin-top: 20px;
  font-size: 16px;
  color: #666;
}

.settings-bar {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 20px;
}

.settings-item {
  display: flex;
  align-items: center;
  margin-left: 20px;
}

.settings-label {
  margin-right: 10px;
  color: #666;
  font-size: 14px;
}

/* 转盘容器 */
.wheel-container {
  position: relative;
  width: 300px;
  height: 300px;
  margin: 0 auto 30px;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 转盘样式 - 经典风格 */
.wheel-classic {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  position: relative;
  overflow: hidden;
  border: 8px solid #ff5722;
  box-shadow: 0 0 0 8px #ffeb3b, 0 0 20px rgba(0, 0, 0, 0.2);
  transition: transform 4s cubic-bezier(0.17, 0.67, 0.83, 0.67);
  animation: pulse 3s infinite ease-in-out;
  background-color: #fff;
}

/* 转盘样式 - 苹果风格 */
.wheel-apple {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  position: relative;
  overflow: hidden;
  border: 8px solid #007AFF;
  box-shadow: 0 0 0 8px #5AC8FA, 0 0 20px rgba(0, 0, 0, 0.2);
  transition: transform 4s cubic-bezier(0.17, 0.67, 0.83, 0.67);
  animation: pulse 3s infinite ease-in-out;
  background-color: #fff;
}

/* 转盘样式 - 卡通风格 */
.wheel-cartoon {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  position: relative;
  overflow: hidden;
  border: 8px solid #FF6B6B;
  box-shadow: 0 0 0 8px #FFE66D, 0 0 20px rgba(0, 0, 0, 0.2);
  transition: transform 4s cubic-bezier(0.17, 0.67, 0.83, 0.67);
  animation: pulse 3s infinite ease-in-out;
  background-color: #fff;
}

/* 转盘样式 - 商业风格 */
.wheel-business {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  position: relative;
  overflow: hidden;
  border: 8px solid #34495E;
  box-shadow: 0 0 0 8px #BDC3C7, 0 0 20px rgba(0, 0, 0, 0.2);
  transition: transform 4s cubic-bezier(0.17, 0.67, 0.83, 0.67);
  animation: pulse 3s infinite ease-in-out;
  background-color: #ECF0F1;
}

/* 转盘样式 - 吉卜力风格 */
.wheel-ghibli {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  position: relative;
  overflow: hidden;
  border: 8px solid #1A7A89;
  box-shadow: 0 0 0 8px #D4A017, 0 0 20px rgba(0, 0, 0, 0.2);
  transition: transform 4s cubic-bezier(0.17, 0.67, 0.83, 0.67);
  animation: pulse 3s infinite ease-in-out;
  background-color: #fff;
}

/* 增加分割线 */
.wheel-classic::before,
.wheel-apple::before,
.wheel-cartoon::before,
.wheel-business::before,
.wheel-ghibli::before {
  content: '';
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background:
    repeating-conic-gradient(
      transparent 0deg,
      transparent calc(360deg / var(--prize-count, 8) - 2deg),
      rgba(0, 0, 0, 0.2) calc(360deg / var(--prize-count, 8) - 2deg),
      rgba(0, 0, 0, 0.2) calc(360deg / var(--prize-count, 8))
    );
  z-index: 3;
  pointer-events: none;
}

/* 指针样式 - 经典风格 */
.pointer-classic {
  position: absolute;
  top: -25px;
  left: 50%;
  transform: translateX(-50%);
  width: 30px;
  height: 30px;
  background-color: #ff5722;
  clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
  z-index: 5;
  filter: drop-shadow(0 0 5px rgba(0, 0, 0, 0.3));
  animation: bounce 2s infinite ease-in-out;
}

/* 指针样式 - 苹果风格 */
.pointer-apple {
  position: absolute;
  top: -25px;
  left: 50%;
  transform: translateX(-50%);
  width: 30px;
  height: 30px;
  background-color: #007AFF;
  clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
  z-index: 5;
  filter: drop-shadow(0 0 5px rgba(0, 0, 0, 0.3));
  animation: bounce 2s infinite ease-in-out;
}

/* 指针样式 - 卡通风格 */
.pointer-cartoon {
  position: absolute;
  top: -25px;
  left: 50%;
  transform: translateX(-50%);
  width: 30px;
  height: 30px;
  background-color: #FF6B6B;
  clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
  z-index: 5;
  filter: drop-shadow(0 0 5px rgba(0, 0, 0, 0.3));
  animation: bounce 2s infinite ease-in-out;
}

/* 指针样式 - 商业风格 */
.pointer-business {
  position: absolute;
  top: -25px;
  left: 50%;
  transform: translateX(-50%);
  width: 30px;
  height: 30px;
  background-color: #34495E;
  clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
  z-index: 5;
  filter: drop-shadow(0 0 5px rgba(0, 0, 0, 0.3));
  animation: bounce 2s infinite ease-in-out;
}

/* 指针样式 - 吉卜力风格 */
.pointer-ghibli {
  position: absolute;
  top: -25px;
  left: 50%;
  transform: translateX(-50%);
  width: 30px;
  height: 30px;
  background-color: #1A7A89;
  clip-path: polygon(50% 0%, 0% 100%, 100% 100%);
  z-index: 5;
  filter: drop-shadow(0 0 5px rgba(0, 0, 0, 0.3));
  animation: bounce 2s infinite ease-in-out;
}

/* 转盘中心装饰 - 经典风格 */
.center-classic {
  content: '';
  position: absolute;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #fff;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border: 4px solid #ff5722;
  box-shadow: 0 0 0 4px #ffeb3b, 0 0 10px rgba(0, 0, 0, 0.2);
  z-index: 5;
}

/* 转盘中心装饰 - 苹果风格 */
.center-apple {
  content: '';
  position: absolute;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #fff;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border: 4px solid #007AFF;
  box-shadow: 0 0 0 4px #5AC8FA, 0 0 10px rgba(0, 0, 0, 0.2);
  z-index: 5;
}

/* 转盘中心装饰 - 卡通风格 */
.center-cartoon {
  content: '';
  position: absolute;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #fff;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border: 4px solid #FF6B6B;
  box-shadow: 0 0 0 4px #FFE66D, 0 0 10px rgba(0, 0, 0, 0.2);
  z-index: 5;
}

/* 转盘中心装饰 - 商业风格 */
.center-business {
  content: '';
  position: absolute;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #ECF0F1;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border: 4px solid #34495E;
  box-shadow: 0 0 0 4px #BDC3C7, 0 0 10px rgba(0, 0, 0, 0.2);
  z-index: 5;
}

/* 转盘中心装饰 - 吉卜力风格 */
.center-ghibli {
  content: '';
  position: absolute;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #fff;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border: 4px solid #1A7A89;
  box-shadow: 0 0 0 4px #D4A017, 0 0 10px rgba(0, 0, 0, 0.2);
  z-index: 5;
}

.wheel-item {
  position: absolute;
  top: 0;
  left: 0;
  width: 50%;
  height: 50%;
  transform-origin: bottom right;
  overflow: hidden;
  text-align: center;
}

.wheel-item-content {
  position: absolute;
  left: -100%;
  width: 200%;
  height: 200%;
  transform-origin: 100% 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: 0;
}

.prize-image {
  width: 30px;
  height: 30px;
  object-fit: contain;
  margin-bottom: 5px;
  position: relative;
  z-index: 1;
  margin-top: -20px;
  filter: drop-shadow(0 0 3px rgba(255, 255, 255, 0.8));
}

/* 确保"谢谢参与"文字在各种背景上都清晰可见 */
.prize-name {
  font-size: 14px;
  font-weight: bold;
  color: #333;
  text-shadow: 0 0 3px white, 0 0 6px white;
  max-width: 100px;
  padding: 4px 6px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 8px;
  line-height: 1.2;
  margin-top: 3px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

/* 按钮样式 */
.lucky-draw-actions {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 20px;
  width: 100%;
}

.wheel-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
  width: 60px;
  height: 60px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: white;
  border-radius: 50%;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
}

/* 按钮样式 - 经典风格 */
.button-classic {
  background-color: #ff5722 !important;
  color: white !important;
  border: none !important;
  font-weight: bold !important;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.2) !important;
  height: 50px !important;
  width: 50px !important;
  border-radius: 50% !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  text-align: center !important;
  padding: 0 !important;
  font-size: 14px !important;
  line-height: 1.2 !important;
  transition: all 0.3s ease !important;
  position: relative !important;
  z-index: 10 !important;
}

/* 按钮样式 - 苹果风格 */
.button-apple {
  background-color: #007AFF !important;
  color: white !important;
  border: none !important;
  font-weight: bold !important;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.2) !important;
  height: 50px !important;
  width: 50px !important;
  border-radius: 50% !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  text-align: center !important;
  padding: 0 !important;
  font-size: 14px !important;
  line-height: 1.2 !important;
  transition: all 0.3s ease !important;
  position: relative !important;
  z-index: 10 !important;
}

/* 按钮样式 - 卡通风格 */
.button-cartoon {
  background-color: #FF6B6B !important;
  color: white !important;
  border: none !important;
  font-weight: bold !important;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.2) !important;
  height: 50px !important;
  width: 50px !important;
  border-radius: 50% !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  text-align: center !important;
  padding: 0 !important;
  font-size: 14px !important;
  line-height: 1.2 !important;
  transition: all 0.3s ease !important;
  position: relative !important;
  z-index: 10 !important;
}

/* 按钮样式 - 商业风格 */
.button-business {
  background-color: #3498DB !important;
  color: white !important;
  border: none !important;
  font-weight: bold !important;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.2) !important;
  height: 50px !important;
  width: 50px !important;
  border-radius: 50% !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  text-align: center !important;
  padding: 0 !important;
  font-size: 14px !important;
  line-height: 1.2 !important;
  transition: all 0.3s ease !important;
  position: relative !important;
  z-index: 10 !important;
}

/* 按钮样式 - 吉卜力风格 */
.button-ghibli {
  background-color: #1A7A89 !important;
  color: white !important;
  border: none !important;
  font-weight: bold !important;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.2) !important;
  height: 50px !important;
  width: 50px !important;
  border-radius: 50% !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  text-align: center !important;
  padding: 0 !important;
  font-size: 14px !important;
  line-height: 1.2 !important;
  transition: all 0.3s ease !important;
  position: relative !important;
  z-index: 10 !important;
}

.button-classic:hover, .button-apple:hover, .button-cartoon:hover, .button-business:hover, .button-ghibli:hover,
.button-classic:focus, .button-apple:focus, .button-cartoon:focus, .button-business:focus, .button-ghibli:focus {
  transform: translateY(-3px) scale(1.05);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3) !important;
}

.remaining-draws {
  text-align: center;
  margin: 10px 0;
  font-size: 16px;
  color: #333;
  font-weight: bold;
}

.remaining-count {
  color: #ff5722;
  font-size: 18px;
  margin: 0 5px;
}

.action-buttons {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
  justify-content: center;
  margin-top: 10px;
}

.action-button {
  min-width: 110px;
  height: 40px;
  border-radius: 20px;
  transition: all 0.3s ease;
}

.action-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
}

.prize-result {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 20px;
  background-color: #fff9f0;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.prize-result-image {
  width: 80px;
  height: 80px;
  object-fit: contain;
  border-radius: 8px;
  border: 1px solid #FFD8A8;
}

.prize-result-info {
  flex: 1;
}

.prize-result-title {
  margin-bottom: 5px !important;
  color: #ff5722;
}

.prize-result-description {
  color: #666;
  margin-bottom: 0 !important;
}

.lucky-draw-footer {
  position: relative;
  z-index: 1;
  width: 100%;
  max-width: 1000px;
  text-align: center;
  padding: 15px 0;
  margin-top: 10px;
  color: #666;
  font-size: 14px;
}

.footer-links {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 10px;
}

.footer-link {
  color: #666;
  text-decoration: none;
}

.footer-link:hover {
  color: #ff5722;
  text-decoration: underline;
}

/* 响应式样式 */
@media (max-width: 1200px) {
  .lucky-draw-content {
    max-width: 800px;
  }
}

@media (max-width: 992px) {
  .lucky-draw-content {
    max-width: 700px;
    padding: 25px;
  }

  .wheel-container {
    width: 280px;
    height: 280px;
  }

  .game-title {
    font-size: 28px;
  }
}

@media (max-width: 768px) {
  .lucky-draw-container {
    padding: 20px;
  }

  .lucky-draw-content {
    max-width: 100%;
    padding: 20px;
  }

  .wheel-container {
    width: 250px;
    height: 250px;
  }

  .game-title {
    font-size: 24px;
  }

  .game-description {
    font-size: 14px;
  }

  .prize-image {
    width: 25px;
    height: 25px;
  }

  .prize-name {
    font-size: 12px;
  }
}

@media (max-width: 576px) {
  .lucky-draw-container {
    padding: 15px;
  }

  .lucky-draw-content {
    padding: 15px;
  }

  .wheel-container {
    width: 220px;
    height: 220px;
    margin-bottom: 20px;
  }

  .wheel-classic, .wheel-apple, .wheel-cartoon, .wheel-business, .wheel-ghibli {
    border-width: 6px;
  }

  .pointer-classic, .pointer-apple, .pointer-cartoon, .pointer-business, .pointer-ghibli {
    top: -20px;
    width: 25px;
    height: 25px;
  }

  .center-classic, .center-apple, .center-cartoon, .center-business, .center-ghibli {
    width: 35px;
    height: 35px;
    border-width: 3px;
  }

  .button-classic, .button-apple, .button-cartoon, .button-business, .button-ghibli {
    width: 45px !important;
    height: 45px !important;
    font-size: 12px !important;
  }

  .action-buttons {
    flex-direction: column;
    gap: 10px;
  }

  .action-button {
    width: 100%;
  }

  .game-logo {
    width: 80px;
    height: 80px;
  }

  .game-title {
    font-size: 20px;
  }

  .prize-result {
    flex-direction: column;
    text-align: center;
  }

  .prize-result-image {
    margin-right: 0;
    margin-bottom: 15px;
  }

  .settings-bar {
    flex-direction: column;
    align-items: flex-end;
  }

  .settings-item {
    margin-left: 0;
    margin-bottom: 10px;
  }
}
