"""
钉钉unionId智能管理服务
根据工程师最佳实践，提供unionId的缓存、获取和管理功能
"""
import logging
import uuid
from typing import Optional, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_
from datetime import datetime, timedelta, timezone

from ..utils.dingtalk_api import DingTalkAPI
from ..models.models import DingTalkUserMapping
from models.user import ThirdPartyAccount, User

logger = logging.getLogger(__name__)


class UnionIdService:
    """
    unionId智能管理服务
    
    采用工程师最佳实践：
    1. 优先从数据库缓存读取
    2. 缓存未命中时通过API获取并存储
    3. 定期刷新过期缓存
    4. 提供批量查询能力
    """
    
    def __init__(self, db: AsyncSession, dingtalk_api: DingTalkAPI, tenant_id: str, project_id: str):
        self.db = db
        self.dingtalk_api = dingtalk_api
        self.tenant_id = uuid.UUID(tenant_id) if isinstance(tenant_id, str) else tenant_id
        self.project_id = uuid.UUID(project_id) if isinstance(project_id, str) else project_id
        self.cache_expiry_hours = 24  # 缓存24小时过期
    
    async def get_union_id_by_user_id(self, user_id: str) -> Optional[str]:
        """
        根据系统用户ID获取unionId
        
        策略：
        1. 先查询DingTalkUserMapping缓存
        2. 缓存未命中则查询ThirdPartyAccount获取userid
        3. 通过userid调用钉钉API获取unionId
        4. 存储到缓存中
        
        Args:
            user_id: 系统用户ID
            
        Returns:
            str: 钉钉unionId，获取失败返回None
        """
        try:
            user_uuid = uuid.UUID(user_id)
            
            # 1. 优先从DingTalkUserMapping缓存读取
            cached_union_id = await self._get_cached_union_id(user_uuid)
            if cached_union_id:
                logger.info(f"从缓存获取unionId: user_id={user_id}, union_id={cached_union_id}")
                return cached_union_id
            
            # 2. 缓存未命中，获取钉钉userid
            dingtalk_userid = await self._get_dingtalk_userid(user_uuid)
            if not dingtalk_userid:
                logger.error(f"用户未绑定钉钉账号: user_id={user_id}")
                return None
            
            # 3. 通过userid调用API获取完整用户信息
            user_detail = await self.dingtalk_api.get_user_info_by_userid(dingtalk_userid)
            if not user_detail:
                logger.error(f"无法从钉钉获取用户详情: userid={dingtalk_userid}")
                logger.error(f"可能原因：1) userid不存在于钉钉企业中；2) 用户已被删除；3) 权限不足")
                
                # 清理无效的用户映射
                await self._clean_invalid_user_mapping(user_uuid, dingtalk_userid)
                return None
            
            union_id = user_detail.get("unionid")
            if not union_id:
                logger.error(f"用户详情中无unionid字段: userid={dingtalk_userid}")
                return None
            
            # 4. 存储到缓存
            await self._cache_union_id(user_uuid, dingtalk_userid, union_id, user_detail)
            
            logger.info(f"成功获取并缓存unionId: user_id={user_id}, union_id={union_id}")
            return union_id
            
        except Exception as e:
            logger.error(f"获取unionId失败: user_id={user_id}, error={str(e)}")
            return None
    
    async def get_union_id_by_userid(self, dingtalk_userid: str) -> Optional[str]:
        """
        根据钉钉userid直接获取unionId
        
        Args:
            dingtalk_userid: 钉钉用户ID
            
        Returns:
            str: 钉钉unionId
        """
        try:
            # 先检查缓存
            mapping = await self.db.scalar(
                select(DingTalkUserMapping).where(
                    and_(
                        DingTalkUserMapping.tenant_id == self.tenant_id,
                        DingTalkUserMapping.project_id == self.project_id,
                        DingTalkUserMapping.dingtalk_user_id == dingtalk_userid,
                        DingTalkUserMapping.dingtalk_union_id.isnot(None)
                    )
                )
            )
            
            if mapping and self._is_cache_valid(mapping.updated_at):
                logger.info(f"从缓存获取unionId: userid={dingtalk_userid}, union_id={mapping.dingtalk_union_id}")
                return mapping.dingtalk_union_id
            
            # 缓存未命中或过期，调用API
            user_detail = await self.dingtalk_api.get_user_info_by_userid(dingtalk_userid)
            if not user_detail:
                return None
            
            union_id = user_detail.get("unionid")
            if union_id and mapping:
                # 更新现有缓存
                mapping.dingtalk_union_id = union_id
                mapping.updated_at = datetime.utcnow()
                await self.db.commit()
            
            return union_id
            
        except Exception as e:
            logger.error(f"通过userid获取unionId失败: userid={dingtalk_userid}, error={str(e)}")
            return None
    
    async def refresh_union_id_cache(self, user_id: str) -> bool:
        """
        强制刷新用户的unionId缓存
        
        Args:
            user_id: 系统用户ID
            
        Returns:
            bool: 刷新是否成功
        """
        try:
            user_uuid = uuid.UUID(user_id)
            
            # 获取当前映射
            mapping = await self.db.scalar(
                select(DingTalkUserMapping).where(
                    and_(
                        DingTalkUserMapping.tenant_id == self.tenant_id,
                        DingTalkUserMapping.project_id == self.project_id,
                        DingTalkUserMapping.user_id == user_uuid
                    )
                )
            )
            
            if not mapping or not mapping.dingtalk_user_id:
                logger.error(f"未找到钉钉用户映射: user_id={user_id}")
                return False
            
            # 调用API获取最新信息
            user_detail = await self.dingtalk_api.get_user_info_by_userid(mapping.dingtalk_user_id)
            if not user_detail:
                return False
            
            # 更新缓存
            union_id = user_detail.get("unionid")
            if union_id:
                mapping.dingtalk_union_id = union_id
                mapping.updated_at = datetime.utcnow()
                await self.db.commit()
                logger.info(f"成功刷新unionId缓存: user_id={user_id}, union_id={union_id}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"刷新unionId缓存失败: user_id={user_id}, error={str(e)}")
            return False
    
    async def sync_user_from_platform_binding(self, current_user: User) -> Dict[str, Any]:
        """
        从平台主绑定关系同步用户映射到钉钉插件
        - 查找用户在ThirdPartyAccount中的钉钉绑定记录
        - 获取unionid，然后调用API获取用户详情
        - 在DingTalkUserMapping中创建或更新记录
        """
        try:
            logger.info(f"开始为用户 {current_user.id} 同步平台钉钉绑定关系...")

            # 1. 查找用户在ThirdPartyAccount中的钉钉绑定记录
            stmt = select(ThirdPartyAccount).where(
                and_(
                    ThirdPartyAccount.user_id == current_user.id,
                    ThirdPartyAccount.platform == "dingtalk"
                )
            )
            platform_account = await self.db.scalar(stmt)

            if not platform_account or not platform_account.platform_user_id:
                logger.warning(f"用户 {current_user.id} 在平台中没有找到有效的钉钉绑定关系。")
                return {"success": False, "message": "未找到平台钉钉绑定关系，请先在个人中心完成绑定。"}

            unionid = platform_account.platform_user_id
            logger.info(f"为用户 {current_user.id} 找到平台绑定unionid: {unionid}")

            # 2. 获取用户详情
            user_detail = await self.dingtalk_api.get_user_info_by_unionid(unionid)
            if not user_detail:
                logger.error(f"无法通过unionid {unionid} 获取钉钉用户详情。")
                return {"success": False, "message": f"无法获取钉钉用户详情，请检查钉钉应用权限或稍后重试。"}

            dingtalk_userid = user_detail.get("userid")
            if not dingtalk_userid:
                logger.error(f"钉钉用户详情中缺少userid字段: {user_detail}")
                return {"success": False, "message": "获取到的钉钉用户信息不完整，缺少userid。"}

            # 3. 创建或更新DingTalkUserMapping记录
            mapping = await self._create_or_update_mapping(current_user.id, dingtalk_userid, unionid, user_detail)
            
            logger.info(f"成功为用户 {current_user.id} 同步了钉钉用户映射。")
            return {"success": True, "message": "同步成功", "data": mapping}

        except Exception as e:
            logger.error(f"同步平台绑定关系时发生异常: {str(e)}", exc_info=True)
            return {"success": False, "message": f"同步失败: {str(e)}"}

    async def _create_or_update_mapping(self, user_uuid: uuid.UUID, dingtalk_userid: str, union_id: str, user_detail: Dict[str, Any]) -> DingTalkUserMapping:
        """创建或更新DingTalkUserMapping记录"""
        stmt = select(DingTalkUserMapping).where(
            and_(
                DingTalkUserMapping.tenant_id == self.tenant_id,
                DingTalkUserMapping.project_id == self.project_id,
                DingTalkUserMapping.dingtalk_union_id == union_id
            )
        )
        mapping = await self.db.scalar(stmt)

        if mapping:
            logger.info(f"找到已存在的映射记录(id={mapping.id})，进行更新。")
        else:
            logger.info(f"未找到映射记录，创建新记录。")
            mapping = DingTalkUserMapping(
                tenant_id=self.tenant_id,
                project_id=self.project_id
            )

        # 填充或更新数据
        mapping.user_id = user_uuid
        mapping.dingtalk_user_id = dingtalk_userid
        mapping.dingtalk_union_id = union_id
        mapping.dingtalk_name = user_detail.get("name")
        mapping.dingtalk_mobile = user_detail.get("mobile")
        mapping.dingtalk_email = user_detail.get("email")
        mapping.dingtalk_avatar = user_detail.get("avatar")
        mapping.dingtalk_position = user_detail.get("title")
        mapping.dingtalk_job_number = user_detail.get("job_number")
        mapping.is_active = user_detail.get("active", True)
        mapping.updated_at = datetime.now(timezone.utc)
        
        if not mapping.id:
            self.db.add(mapping)
        
        await self.db.commit()
        await self.db.refresh(mapping)
        
        return mapping
    
    async def batch_get_union_ids(self, user_ids: list[str]) -> Dict[str, str]:
        """
        批量获取unionId
        
        Args:
            user_ids: 系统用户ID列表
            
        Returns:
            Dict[str, str]: user_id -> union_id 映射
        """
        result = {}
        
        for user_id in user_ids:
            union_id = await self.get_union_id_by_user_id(user_id)
            if union_id:
                result[user_id] = union_id
        
        return result
    
    async def _get_cached_union_id(self, user_uuid: uuid.UUID) -> Optional[str]:
        """从缓存获取unionId"""
        mapping = await self.db.scalar(
            select(DingTalkUserMapping).where(
                and_(
                    DingTalkUserMapping.tenant_id == self.tenant_id,
                    DingTalkUserMapping.project_id == self.project_id,
                    DingTalkUserMapping.user_id == user_uuid,
                    DingTalkUserMapping.dingtalk_union_id.isnot(None)
                )
            )
        )
        
        if mapping and self._is_cache_valid(mapping.updated_at):
            return mapping.dingtalk_union_id
        
        return None
    
    async def _get_dingtalk_userid(self, user_uuid: uuid.UUID) -> Optional[str]:
        """
        获取钉钉企业内的userid（不是unionId）
        
        策略：
        1. 先从DingTalkUserMapping查找缓存的企业内userid
        2. 如果没有，从主系统的ThirdPartyAccount表获取
        3. 通过unionId调用企业API获取真正的userid
        """
        # 先从DingTalkUserMapping查找缓存的企业内userid
        mapping = await self.db.scalar(
            select(DingTalkUserMapping).where(
                and_(
                    DingTalkUserMapping.tenant_id == self.tenant_id,
                    DingTalkUserMapping.project_id == self.project_id,
                    DingTalkUserMapping.user_id == user_uuid,
                    DingTalkUserMapping.dingtalk_user_id.isnot(None)
                )
            )
        )
        
        if mapping and mapping.dingtalk_user_id:
            # 检查缓存是否有效
            if self._is_cache_valid(mapping.updated_at):
                logger.debug(f"从缓存获取到企业内userid: {mapping.dingtalk_user_id}")
                return mapping.dingtalk_user_id
            else:
                logger.debug("缓存已过期，需要重新获取")
        
        # 2. 如果没有，从主系统的ThirdPartyAccount表获取
        # 注意：这里假设platform_user_id存储的是unionId，需要再转换为userid
        third_party_account = await self.db.scalar(
            select(ThirdPartyAccount).where(
                ThirdPartyAccount.user_id == user_uuid,
                ThirdPartyAccount.platform == 'dingtalk'
            )
        )
        
        if third_party_account and third_party_account.platform_user_id:
            union_id = third_party_account.platform_user_id
            logger.debug(f"从ThirdPartyAccount获取到unionId: {union_id}")
            
            # 3. 通过unionId调用企业API获取真正的userid
            userid = await self._get_userid_by_unionid(union_id)
            if userid:
                # 顺手缓存一下
                await self._cache_union_id(user_uuid, userid, union_id, {})
                return userid
        
        logger.warning(f"无法为用户 {user_uuid} 获取钉钉userid")
        return None
    
    async def _get_userid_by_unionid(self, union_id: str) -> Optional[str]:
        """通过unionId获取企业内userid"""
        logger.debug(f"正在通过unionId {union_id} 获取userid")
        user_detail = await self.dingtalk_api.get_user_info_by_unionid(union_id)
        if user_detail and user_detail.get("userid"):
            return user_detail.get("userid")
        logger.warning(f"无法从unionId {union_id} 获取userid")
        return None
    
    async def _cache_union_id(self, user_uuid: uuid.UUID, dingtalk_userid: str, union_id: str, user_detail: Dict[str, Any]):
        """将unionId和用户详情缓存到数据库"""
        # 查找现有映射
        stmt = select(DingTalkUserMapping).where(
            and_(
                DingTalkUserMapping.tenant_id == self.tenant_id,
                DingTalkUserMapping.project_id == self.project_id,
                DingTalkUserMapping.user_id == user_uuid,
            )
        )
        mapping = await self.db.scalar(stmt)
        
        if mapping:
            # 更新现有映射
            mapping.dingtalk_user_id = dingtalk_userid
            mapping.dingtalk_union_id = union_id
            mapping.updated_at = datetime.utcnow()
        else:
            # 创建新映射
            mapping = DingTalkUserMapping(
                tenant_id=self.tenant_id,
                project_id=self.project_id,
                user_id=user_uuid,
                dingtalk_user_id=dingtalk_userid,
                dingtalk_union_id=union_id
            )
            self.db.add(mapping)
        
        # 填充/更新详细信息
        mapping.dingtalk_name = user_detail.get("name")
        mapping.dingtalk_mobile = user_detail.get("mobile")
        mapping.dingtalk_email = user_detail.get("email")
        mapping.dingtalk_avatar = user_detail.get("avatar")
        mapping.is_active = user_detail.get("active", True)
        
        try:
            await self.db.commit()
            logger.info(f"成功缓存用户映射: user_id={user_uuid}, union_id={union_id}")
        except Exception as e:
            await self.db.rollback()
            logger.error(f"缓存用户映射失败: {str(e)}")
    
    def _is_cache_valid(self, updated_at: Optional[datetime]) -> bool:
        """检查缓存是否在有效期内"""
        if not updated_at:
            return False
        
        # 确保updated_at有时区信息
        if updated_at.tzinfo is None:
            updated_at = updated_at.replace(tzinfo=timezone.utc)
        
        # 使用UTC时间进行比较
        current_time = datetime.now(timezone.utc)
        expiry_time = updated_at + timedelta(hours=self.cache_expiry_hours)
        return current_time < expiry_time
    
    async def _clean_invalid_user_mapping(self, user_uuid: uuid.UUID, invalid_userid: str):
        """清理无效的用户映射"""
        try:
            # 删除或标记无效的DingTalkUserMapping
            mapping = await self.db.scalar(
                select(DingTalkUserMapping).where(
                    and_(
                        DingTalkUserMapping.tenant_id == self.tenant_id,
                        DingTalkUserMapping.project_id == self.project_id,
                        DingTalkUserMapping.user_id == user_uuid,
                        DingTalkUserMapping.dingtalk_user_id == invalid_userid
                    )
                )
            )
            
            if mapping:
                # 清空无效的userid和unionid，但保留记录
                mapping.dingtalk_user_id = None
                mapping.dingtalk_union_id = None
                mapping.updated_at = datetime.utcnow()
                await self.db.commit()
                logger.info(f"已清理无效的用户映射: user_id={user_uuid}, invalid_userid={invalid_userid}")
            
        except Exception as e:
            logger.error(f"清理无效用户映射失败: {str(e)}")
            await self.db.rollback() 