from typing import List, Optional, Dict, Any, Union
from pydantic import BaseModel, UUID4, Field
from datetime import datetime

# 工作流节点模型
class WorkflowNodeBase(BaseModel):
    """工作流节点基础模型"""
    name: str
    code: str
    description: Optional[str] = None
    type: str
    order: int
    config: Dict[str, Any]


class WorkflowNodeCreate(WorkflowNodeBase):
    """创建工作流节点模型"""
    pass


class WorkflowNodeUpdate(BaseModel):
    """更新工作流节点模型"""
    name: Optional[str] = None
    description: Optional[str] = None
    type: Optional[str] = None
    order: Optional[int] = None
    config: Optional[Dict[str, Any]] = None


class WorkflowNodeInDB(WorkflowNodeBase):
    """数据库工作流节点模型"""
    id: UUID4
    workflow_id: UUID4
    created_at: datetime
    updated_at: datetime

    class Config:
        model_config = {"from_attributes": True}


# 工作流转换模型
class WorkflowTransitionBase(BaseModel):
    """工作流转换基础模型"""
    source_node_id: UUID4
    target_node_id: UUID4
    name: str
    condition: Optional[Dict[str, Any]] = None


class WorkflowTransitionCreate(WorkflowTransitionBase):
    """创建工作流转换模型"""
    pass


class WorkflowTransitionUpdate(BaseModel):
    """更新工作流转换模型"""
    name: Optional[str] = None
    condition: Optional[Dict[str, Any]] = None


class WorkflowTransitionInDB(WorkflowTransitionBase):
    """数据库工作流转换模型"""
    id: UUID4
    workflow_id: UUID4
    created_at: datetime
    updated_at: datetime

    class Config:
        model_config = {"from_attributes": True}


# 工作流模型
class WorkflowBase(BaseModel):
    """工作流基础模型"""
    name: str
    code: str
    description: Optional[str] = None
    type: str
    is_default: bool = False
    is_active: bool = True
    config: Dict[str, Any]


class WorkflowCreate(WorkflowBase):
    """创建工作流模型"""
    nodes: List[WorkflowNodeCreate]
    transitions: List[WorkflowTransitionCreate]


class WorkflowUpdate(BaseModel):
    """更新工作流模型"""
    name: Optional[str] = None
    description: Optional[str] = None
    is_default: Optional[bool] = None
    is_active: Optional[bool] = None
    config: Optional[Dict[str, Any]] = None


class WorkflowInDB(WorkflowBase):
    """数据库工作流模型"""
    id: UUID4
    tenant_id: Optional[UUID4] = None
    project_id: UUID4
    created_at: datetime
    updated_at: datetime
    created_by: Optional[UUID4] = None
    updated_by: Optional[UUID4] = None
    nodes: List[WorkflowNodeInDB]
    transitions: List[WorkflowTransitionInDB]

    class Config:
        model_config = {"from_attributes": True}


class WorkflowResponse(BaseModel):
    """工作流响应模型"""
    success: bool
    message: Optional[str] = None
    data: Optional[WorkflowInDB] = None


class WorkflowListResponse(BaseModel):
    """工作流列表响应模型"""
    success: bool
    message: Optional[str] = None
    items: List[WorkflowInDB]
    total: int
    page: Optional[int] = None
    size: Optional[int] = None
    pages: Optional[int] = None


# 工作流实例模型
class WorkflowInstanceBase(BaseModel):
    """工作流实例基础模型"""
    workflow_id: UUID4
    reference_id: UUID4
    reference_type: str
    data: Optional[Dict[str, Any]] = None


class WorkflowInstanceCreate(WorkflowInstanceBase):
    """创建工作流实例模型"""
    pass


class WorkflowInstanceUpdate(BaseModel):
    """更新工作流实例模型"""
    current_node_id: Optional[UUID4] = None
    status: Optional[str] = None
    data: Optional[Dict[str, Any]] = None


class WorkflowInstanceInDB(WorkflowInstanceBase):
    """数据库工作流实例模型"""
    id: UUID4
    tenant_id: Optional[UUID4] = None
    project_id: UUID4
    current_node_id: UUID4
    status: str
    created_at: datetime
    updated_at: datetime
    created_by: Optional[UUID4] = None
    updated_by: Optional[UUID4] = None
    current_node: WorkflowNodeInDB

    class Config:
        model_config = {"from_attributes": True}


class WorkflowInstanceResponse(BaseModel):
    """工作流实例响应模型"""
    success: bool
    message: Optional[str] = None
    data: Optional[WorkflowInstanceInDB] = None


class WorkflowInstanceListResponse(BaseModel):
    """工作流实例列表响应模型"""
    success: bool
    message: Optional[str] = None
    items: List[WorkflowInstanceInDB]
    total: int
    page: Optional[int] = None
    size: Optional[int] = None
    pages: Optional[int] = None


# 工作流日志模型
class WorkflowLogBase(BaseModel):
    """工作流日志基础模型"""
    instance_id: UUID4
    node_id: UUID4
    transition_id: Optional[UUID4] = None
    action: str
    comment: Optional[str] = None
    data: Optional[Dict[str, Any]] = None


class WorkflowLogCreate(WorkflowLogBase):
    """创建工作流日志模型"""
    pass


class WorkflowLogInDB(WorkflowLogBase):
    """数据库工作流日志模型"""
    id: UUID4
    created_at: datetime
    created_by: Optional[UUID4] = None
    user_name: Optional[str] = None  # 非数据库字段，用于前端展示

    class Config:
        model_config = {"from_attributes": True}


class WorkflowLogResponse(BaseModel):
    """工作流日志响应模型"""
    success: bool
    message: Optional[str] = None
    data: Optional[WorkflowLogInDB] = None


class WorkflowLogListResponse(BaseModel):
    """工作流日志列表响应模型"""
    success: bool
    message: Optional[str] = None
    items: List[WorkflowLogInDB]
    total: int
    page: Optional[int] = None
    size: Optional[int] = None
    pages: Optional[int] = None


# 工作流操作模型
class WorkflowAction(BaseModel):
    """工作流操作模型"""
    action: str
    comment: Optional[str] = None
    data: Optional[Dict[str, Any]] = None
    transition_id: Optional[UUID4] = None
