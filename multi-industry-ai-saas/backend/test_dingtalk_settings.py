#!/usr/bin/env python3
import sys
import asyncio
sys.path.append('/app')

from db.database import AsyncSessionLocal
from plugins.dingtalk.services.unified_settings_service import UnifiedSettingsService

async def test_settings_save():
    print("🔧 测试钉钉插件设置保存功能...")
    
    # 测试数据
    test_settings = {
        "app_key": "dingjaupjxkfrpt7kh43",
        "app_secret": "32oN7qG6_BWRfwwHRw9ODshWS87VG_xB019K9w9Ymlojx5haa9iJQ9Ev3EneADSK",
        "corp_id": "ding1bc1134b4d6147e9",
        "enabled": True
    }
    
    tenant_id = "e2e6b60e-78ea-4779-8633-21fc32b7304f"
    project_id = "93289212-7943-48ab-8092-e8eb7f663677"
    
    try:
        async with AsyncSessionLocal() as db:
            # 创建设置服务
            settings_service = UnifiedSettingsService(db, tenant_id, project_id)
            
            # 1. 获取当前设置
            print("\n📊 获取当前设置...")
            current_settings = await settings_service.get_settings()
            print(f"当前设置: {current_settings}")
            
            # 2. 更新设置
            print("\n💾 更新设置...")
            result = await settings_service.update_settings(test_settings)
            print(f"更新结果: {result}")
            
            if result.get("success"):
                print("✅ 设置保存成功!")
            else:
                print(f"❌ 设置保存失败: {result.get('message')}")
                
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_settings_save()) 