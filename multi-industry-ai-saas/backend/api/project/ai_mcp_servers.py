#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
AI助手MCP服务器管理API
"""

import logging
import uuid
import json
import time
import httpx
import asyncio
from typing import Optional, List, Dict, Any
from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException, Query, Path, status, Body
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import and_, func

from db.database import get_db
from models.project import Project
from models.user import User
from models.ai_mcp_server import AIMCPServer
from core.auth import get_current_user
from api.deps import get_current_project

logger = logging.getLogger(__name__)
router = APIRouter()

async def _send_mcp_requests_in_sse_context(client, sse_response, message_endpoint, init_request, headers, tools, resources, prompts, server_info, server_capabilities):
    """在SSE连接活跃时发送MCP请求序列（简化版本，不依赖已消费的SSE响应）"""
    import asyncio
    import time
    
    logger.info(f"开始MCP通信序列到: {message_endpoint}")
    
    mcp_headers = {"Content-Type": "application/json"}
    mcp_headers.update(headers)
    
    try:
        # 1. 发送初始化请求
        logger.info("发送初始化请求...")
        init_response = await client.post(
            message_endpoint, 
            json=init_request, 
            headers=mcp_headers, 
            timeout=15
        )
        
        logger.info(f"初始化响应状态码: {init_response.status_code}")
        
        # 检查HTTP响应
        init_data = None
        if init_response.text.strip():
            try:
                init_data = init_response.json()
                logger.info(f"HTTP初始化响应: {init_data}")
            except:
                logger.warning(f"HTTP响应无法解析: {init_response.text}")
        
        # 处理初始化响应
        if init_data and "result" in init_data:
            result_data = init_data["result"]
            server_info.update(result_data.get("serverInfo", {}))
            server_capabilities.update(result_data.get("capabilities", {}))
            logger.info(f"MCP服务器初始化成功: {server_info.get('name', 'Unknown')}")
        elif init_data and "error" in init_data:
            error_msg = init_data["error"].get("message", "未知错误")
            logger.error(f"初始化失败: {error_msg}")
            return
        else:
            logger.warning("未收到有效的初始化响应，继续执行")
            server_info.update({"name": "Unknown MCP Server", "version": "unknown"})
        
        # 2. 发送initialized通知
        logger.info("发送initialized通知...")
        initialized_notification = {
            "jsonrpc": "2.0",
            "method": "notifications/initialized"
        }
        
        try:
            notify_response = await client.post(
                message_endpoint, 
                json=initialized_notification, 
                headers=mcp_headers, 
                timeout=10
            )
            logger.info(f"通知响应状态码: {notify_response.status_code}")
        except Exception as e:
            logger.warning(f"发送initialized通知失败: {e}")
        
        # 等待服务器处理
        await asyncio.sleep(2)
        
        # 3. 获取工具列表
        logger.info("获取工具列表...")
        tools_request = {
            "jsonrpc": "2.0",
            "id": str(uuid.uuid4()),
            "method": "tools/list",
            "params": {}
        }
        
        tools_response = await client.post(
            message_endpoint, 
            json=tools_request, 
            headers=mcp_headers, 
            timeout=15
        )
        
        logger.info(f"工具列表响应状态码: {tools_response.status_code}")
        
        # 解析工具列表
        tools_data = None
        if tools_response.text.strip():
            try:
                tools_data = tools_response.json()
                logger.info(f"HTTP工具响应: 获取到数据")
            except:
                logger.warning(f"HTTP工具响应无法解析")
        
        # 处理工具数据
        if tools_data and "result" in tools_data and "tools" in tools_data["result"]:
            tools.extend(tools_data["result"]["tools"])
            logger.info(f"成功获取到 {len(tools_data['result']['tools'])} 个工具")
        elif tools_data and "error" in tools_data:
            error_msg = tools_data["error"].get("message", "未知错误")
            logger.warning(f"获取工具列表失败: {error_msg}")
        else:
            logger.warning("未获取到有效的工具列表")
        
        # 4. 获取资源列表
        logger.info("获取资源列表...")
        resources_request = {
            "jsonrpc": "2.0",
            "id": str(uuid.uuid4()),
            "method": "resources/list",
            "params": {}
        }
        
        try:
            resources_response = await client.post(
                message_endpoint, 
                json=resources_request, 
                headers=mcp_headers, 
                timeout=15
            )
            
            # 处理资源响应
            resources_data = None
            if resources_response.text.strip():
                try:
                    resources_data = resources_response.json()
                except:
                    pass
            
            if resources_data and "result" in resources_data and "resources" in resources_data["result"]:
                resources.extend(resources_data["result"]["resources"])
                logger.info(f"获取到 {len(resources_data['result']['resources'])} 个资源")
        except Exception as e:
            logger.warning(f"获取资源列表失败: {e}")
        
        # 5. 获取提示词列表
        logger.info("获取提示词列表...")
        prompts_request = {
            "jsonrpc": "2.0",
            "id": str(uuid.uuid4()),
            "method": "prompts/list",
            "params": {}
        }
        
        try:
            prompts_response = await client.post(
                message_endpoint, 
                json=prompts_request, 
                headers=mcp_headers, 
                timeout=15
            )
            
            # 处理提示词响应
            prompts_data = None
            if prompts_response.text.strip():
                try:
                    prompts_data = prompts_response.json()
                except:
                    pass
            
            if prompts_data and "result" in prompts_data and "prompts" in prompts_data["result"]:
                prompts.extend(prompts_data["result"]["prompts"])
                logger.info(f"获取到 {len(prompts_data['result']['prompts'])} 个提示词")
        except Exception as e:
            logger.warning(f"获取提示词列表失败: {e}")
        
        logger.info("MCP请求序列完成")
        
    except Exception as e:
        logger.error(f"MCP请求序列失败: {e}")
        raise

@router.get("/ai/mcp-servers", response_model=Dict[str, Any])
async def list_ai_mcp_servers(
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
    page: int = Query(1, ge=1),
    page_size: int = Query(10, ge=1, le=100)
):
    """获取AI助手MCP服务器列表"""
    try:
        # 计算偏移量
        offset = (page - 1) * page_size

        # 查询服务器总数
        result = await db.execute(
            select(func.count()).select_from(AIMCPServer)
            .where(AIMCPServer.project_id == project.id)
        )
        total = result.scalar()

        # 查询服务器列表
        result = await db.execute(
            select(AIMCPServer)
            .where(AIMCPServer.project_id == project.id)
            .offset(offset)
            .limit(page_size)
            .order_by(AIMCPServer.created_at.desc())
        )
        servers = result.scalars().all()

        # 转换为字典列表
        server_list = []
        for server in servers:
            server_dict = {
                "id": str(server.id),
                "name": server.name,
                "description": server.description,
                "transport_type": server.transport_type,
                "transport_types": server.transport_types or [server.transport_type],
                "deployment_mode": server.deployment_mode,
                "source": server.source,
                "config": server.config,
                "enabled": server.enabled,
                "status": server.status,
                "last_connected_at": server.last_connected_at.isoformat() if server.last_connected_at else None,
                "tools_count": server.tools_count,
                "available_tools": server.available_tools,
                "created_at": server.created_at.isoformat(),
                "updated_at": server.updated_at.isoformat(),
                "created_by_id": str(server.created_by_id) if server.created_by_id else None,
                "sapi_server_id": str(server.sapi_server_id) if server.sapi_server_id else None
            }
            server_list.append(server_dict)
        
        return {
            "success": True,
            "message": "获取AI助手MCP服务器列表成功",
            "data": server_list,
            "total": total,
            "page": page,
            "page_size": page_size
        }
    except Exception as e:
        logger.error(f"获取AI助手MCP服务器列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取AI助手MCP服务器列表失败: {str(e)}"
        )

@router.post("/ai/mcp-servers", response_model=Dict[str, Any])
async def create_ai_mcp_server(
    server_data: Dict[str, Any] = Body(...),
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """创建AI助手MCP服务器"""
    try:
        transport_type = server_data.get("transport_type")
        
        # 创建服务器记录
        server = AIMCPServer(
            name=server_data.get("name"),
            description=server_data.get("description", ""),
            project_id=project.id,
            transport_type=transport_type,
            transport_types=server_data.get("transport_types", [transport_type] if transport_type else []),
            deployment_mode=server_data.get("deployment_mode", "external"),
            source=server_data.get("source", "manual"),
            config=server_data.get("config", {}),
            enabled=server_data.get("enabled", True),
            status="unknown",
            tools_count=0,
            available_tools=[],
            created_by_id=current_user.id,
            sapi_server_id=server_data.get("sapi_server_id")
        )
        
        db.add(server)
        await db.commit()
        await db.refresh(server)
        
        # 构建返回数据
        server_dict = {
            "id": str(server.id),
            "name": server.name,
            "description": server.description,
            "transport_type": server.transport_type,
            "transport_types": server.transport_types,
            "deployment_mode": server.deployment_mode,
            "source": server.source,
            "config": server.config,
            "enabled": server.enabled,
            "status": server.status,
            "tools_count": server.tools_count,
            "available_tools": server.available_tools,
            "created_at": server.created_at.isoformat(),
            "updated_at": server.updated_at.isoformat(),
            "created_by_id": str(server.created_by_id),
            "sapi_server_id": str(server.sapi_server_id) if server.sapi_server_id else None
        }
        
        return {
            "success": True,
            "message": "AI助手MCP服务器创建成功",
            "data": server_dict
        }
    except Exception as e:
        logger.error(f"创建AI助手MCP服务器失败: {e}")
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建AI助手MCP服务器失败: {str(e)}"
        )

@router.post("/ai/mcp-servers/{server_id}/test", response_model=Dict[str, Any])
async def test_ai_mcp_server_connection(
    server_id: str = Path(...),
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """测试AI助手MCP服务器连接（按照MCP标准）"""
    try:
        # 获取服务器配置
        result = await db.execute(
            select(AIMCPServer).where(
                and_(
                    AIMCPServer.id == uuid.UUID(server_id),
                    AIMCPServer.project_id == project.id
                )
            )
        )
        server = result.scalars().first()
        
        if not server:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="MCP服务器不存在"
            )
        
        transport_type = server.transport_type
        config = server.config or {}
        
        success = False
        message = "连接测试失败"
        test_details = {}
        
        if transport_type == "stdio":
            # STDIO协议：验证配置完整性
            command = config.get("command")
            args = config.get("args", [])
            env = config.get("env", {})
            
            if not command:
                message = "STDIO协议缺少启动命令配置"
            else:
                success = True
                message = "STDIO协议配置验证成功"
                test_details = {
                    "command": command,
                    "args_count": len(args),
                    "env_vars_count": len(env)
                }
                
        elif transport_type == "sse":
            # SSE协议：测试连接
            url = config.get("url")
            headers = config.get("headers", {})
            
            if not url:
                message = "SSE协议缺少URL配置"
            else:
                async with httpx.AsyncClient() as client:
                    try:
                        # SSE通常使用GET请求建立连接
                        response = await client.get(url, headers=headers, timeout=10)
                        test_details["status_code"] = response.status_code
                        test_details["response_headers"] = dict(response.headers)
                        
                        if response.status_code == 200:
                            success = True
                            message = "SSE连接测试成功"
                            
                            # 检查是否是SSE格式
                            content_type = response.headers.get("content-type", "")
                            if "text/event-stream" in content_type:
                                test_details["sse_format"] = True
                                message += "，检测到正确的SSE格式"
                            else:
                                test_details["sse_format"] = False
                                message += "，但未检测到SSE格式"
                        else:
                            message = f"SSE连接失败，状态码: {response.status_code}"
                            
                    except httpx.TimeoutException:
                        message = "SSE连接超时"
                        test_details["error"] = "timeout"
                    except httpx.ConnectError:
                        message = "SSE连接失败，无法连接到服务器"
                        test_details["error"] = "connection_error"
                    except Exception as e:
                        message = f"SSE连接失败: {str(e)}"
                        test_details["error"] = str(e)
                        
        elif transport_type == "streamhttp":
            # StreamHTTP协议：测试MCP初始化
            url = config.get("url")
            headers = config.get("headers", {})
            method = config.get("method", "POST")
            
            if not url:
                message = "StreamHTTP协议缺少URL配置"
            else:
                headers.setdefault("Content-Type", "application/json")
                
                async with httpx.AsyncClient() as client:
                    try:
                        # 发送简化的MCP初始化请求进行连接测试
                        test_request = {
                            "jsonrpc": "2.0",
                            "id": str(uuid.uuid4()),
                            "method": "initialize",
                            "params": {
                                "protocolVersion": "2025-03-26",
                                "capabilities": {
                                    "tools": {}
                                },
                                "clientInfo": {
                                    "name": "AI Assistant Test Client",
                                    "version": "1.0.0"
                                }
                            }
                        }
                        
                        if method.upper() == "POST":
                            response = await client.post(url, json=test_request, headers=headers, timeout=10)
                        else:
                            # 对于非POST方法，先尝试简单的连接测试
                            response = await client.get(url, headers=headers, timeout=10)
                            
                        test_details["status_code"] = response.status_code
                        test_details["method"] = method.upper()
                        
                        if response.status_code == 200:
                            try:
                                response_data = response.json()
                                test_details["response_format"] = "json"
                                
                                # 检查是否是有效的MCP响应
                                if "jsonrpc" in response_data:
                                    test_details["mcp_format"] = True
                                    
                                    if "result" in response_data:
                                        result_data = response_data["result"]
                                        server_info = result_data.get("serverInfo", {})
                                        protocol_version = result_data.get("protocolVersion")
                                        
                                        test_details["server_info"] = server_info
                                        test_details["protocol_version"] = protocol_version
                                        
                                        success = True
                                        message = f"StreamHTTP连接测试成功，服务器: {server_info.get('name', 'Unknown')}"
                                        
                                        if protocol_version:
                                            message += f"，协议版本: {protocol_version}"
                                    elif "error" in response_data:
                                        error_info = response_data["error"]
                                        test_details["server_error"] = error_info
                                        message = f"服务器返回错误: {error_info.get('message', '未知错误')}"
                                    else:
                                        success = True
                                        message = "StreamHTTP连接成功，但响应格式异常"
                                else:
                                    test_details["mcp_format"] = False
                                    success = True
                                    message = "StreamHTTP连接成功，但不是标准MCP格式"
                                    
                            except Exception as json_error:
                                test_details["response_format"] = "non-json"
                                test_details["parse_error"] = str(json_error)
                                success = True
                                message = "StreamHTTP连接成功，但响应不是JSON格式"
                        else:
                            message = f"StreamHTTP连接失败，状态码: {response.status_code}"
                            try:
                                error_text = response.text
                                test_details["error_response"] = error_text[:500]  # 限制错误响应长度
                            except:
                                pass
                                
                    except httpx.TimeoutException:
                        message = "StreamHTTP连接超时"
                        test_details["error"] = "timeout"
                    except httpx.ConnectError:
                        message = "StreamHTTP连接失败，无法连接到服务器"
                        test_details["error"] = "connection_error"
                    except Exception as e:
                        message = f"StreamHTTP连接失败: {str(e)}"
                        test_details["error"] = str(e)
        else:
            message = f"不支持的传输协议: {transport_type}"
            test_details["unsupported_protocol"] = transport_type
        
        # 更新服务器状态
        server.status = "connected" if success else "disconnected"
        if success:
            server.last_connected_at = datetime.now()
        server.updated_at = datetime.now()
        
        await db.commit()
        
        return {
            "success": success,
            "message": message,
            "data": {
                "server_id": server_id,
                "transport_type": transport_type,
                "status": server.status,
                "test_time": datetime.now().isoformat(),
                "test_details": test_details
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"测试AI助手MCP服务器连接失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"测试连接失败: {str(e)}"
        )

@router.post("/ai/mcp-servers/{server_id}/initialize", response_model=Dict[str, Any])
async def initialize_ai_mcp_server(
    server_id: str = Path(...),
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """初始化AI助手MCP服务器（按照MCP 2025-03-26标准）"""
    try:
        # 获取服务器配置
        result = await db.execute(
            select(AIMCPServer).where(
                and_(
                    AIMCPServer.id == uuid.UUID(server_id),
                    AIMCPServer.project_id == project.id
                )
            )
        )
        server = result.scalars().first()
        
        if not server:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="MCP服务器不存在"
            )
        
        transport_type = server.transport_type
        config = server.config or {}
        
        tools = []
        resources = []
        prompts = []
        server_info = {}
        server_capabilities = {}
        
        # 根据不同的传输协议执行初始化
        if transport_type == "stdio":
            # STDIO协议：外部服务器无法直接初始化，只能验证配置
            command = config.get("command")
            if not command:
                raise Exception("STDIO协议需要配置启动命令")
            
            # 对于STDIO，我们只能标记为已配置，无法实际测试连接
            server_info = {
                "name": "STDIO MCP Server",
                "version": "unknown",
                "protocol": "stdio"
            }
            logger.info(f"STDIO MCP服务器配置验证成功: {server_id}")
            
        elif transport_type in ["sse", "streamhttp"]:
            url = config.get("url")
            if not url:
                raise Exception(f"{transport_type.upper()}协议需要配置URL")
                
            headers = config.get("headers", {})
            
            async with httpx.AsyncClient() as client:
                try:
                    # 1. 发送MCP初始化请求（按照2025-03-26标准）
                    init_request = {
                        "jsonrpc": "2.0",
                        "id": str(uuid.uuid4()),
                        "method": "initialize",
                        "params": {
                            "protocolVersion": "2025-03-26",  # 使用最新版本
                            "capabilities": {
                                "tools": {},
                                "resources": {},
                                "prompts": {}
                            },
                            "clientInfo": {
                                "name": "AI Assistant MCP Client",
                                "version": "1.0.0"
                            }
                        }
                    }
                    
                    logger.info(f"发送初始化请求到 {url}: {init_request}")
                    
                    # 初始化变量
                    message_endpoint = None
                    init_headers = headers.copy()
                    
                    if transport_type == "sse":
                        # SSE协议：先建立SSE连接获取消息端点，然后保持连接发送MCP请求
                        sse_headers = headers.copy()
                        sse_headers.setdefault("Accept", "text/event-stream")
                        sse_headers.setdefault("Cache-Control", "no-cache")
                        sse_headers.setdefault("Connection", "keep-alive")
                        
                        message_endpoint = None
                        session_id = None
                        mcp_requests_completed = False
                        
                        # 使用流式请求建立SSE连接（基于成功的并发模式）
                        async with client.stream('GET', url, headers=sse_headers, timeout=60) as sse_response:
                            if sse_response.status_code != 200:
                                raise Exception(f"SSE连接失败，状态码: {sse_response.status_code}")
                            
                            logger.info(f"SSE连接建立成功，状态码: {sse_response.status_code}")
                            
                            # 存储MCP响应
                            mcp_responses = {}
                            
                            async def sse_listener():
                                """监听SSE事件并解析MCP响应"""
                                nonlocal message_endpoint, session_id, mcp_responses
                                
                                buffer = ""
                                try:
                                    async for chunk in sse_response.aiter_text():
                                        buffer += chunk
                                        lines = buffer.split('\n')
                                        buffer = lines[-1]
                                        
                                        for line in lines[:-1]:
                                            line = line.strip()
                                            if line:
                                                logger.info(f"SSE事件: {line}")
                                                
                                                # 解析端点信息
                                                if line.startswith("data:") and "/mcp/message" in line:
                                                    endpoint_path = line[5:].strip()
                                                    if endpoint_path.startswith("/mcp/message"):
                                                        from urllib.parse import urlparse
                                                        parsed_url = urlparse(url)
                                                        message_endpoint = f"{parsed_url.scheme}://{parsed_url.netloc}{endpoint_path}"
                                                        
                                                        if "sessionId=" in endpoint_path:
                                                            session_id = endpoint_path.split("sessionId=")[1].split("&")[0]
                                                        
                                                        logger.info(f"获取到消息端点: {message_endpoint}, session: {session_id}")
                                                
                                                # 解析MCP响应
                                                elif line.startswith("data:") and line != "data:" and "{" in line:
                                                    try:
                                                        response_text = line[5:].strip()
                                                        response_data = json.loads(response_text)
                                                        if "id" in response_data:
                                                            request_id = response_data["id"]
                                                            mcp_responses[request_id] = response_data
                                                            logger.info(f"收到MCP响应: {request_id}")
                                                    except Exception as e:
                                                        logger.warning(f"解析SSE响应失败: {e}")
                                except Exception as e:
                                    logger.warning(f"SSE监听异常: {e}")
                            
                            async def send_mcp_requests():
                                """发送MCP请求序列"""
                                nonlocal mcp_requests_completed
                                
                                # 等待获取到端点
                                max_wait = 10
                                wait_time = 0
                                while not message_endpoint and wait_time < max_wait:
                                    await asyncio.sleep(0.5)
                                    wait_time += 0.5
                                
                                if not message_endpoint:
                                    logger.error("未能获取到消息端点")
                                    return
                                
                                logger.info("开始MCP通信序列...")
                                
                                mcp_headers = {"Content-Type": "application/json"}
                                mcp_headers.update(headers)
                                
                                try:
                                    # 1. 初始化请求
                                    logger.info("发送初始化请求...")
                                    init_response = await client.post(
                                        message_endpoint, 
                                        json=init_request, 
                                        headers=mcp_headers, 
                                        timeout=15
                                    )
                                    
                                    logger.info(f"初始化响应状态码: {init_response.status_code}")
                                    
                                    # 处理初始化响应
                                    init_data = None
                                    if init_response.text.strip():
                                        try:
                                            init_data = init_response.json()
                                            logger.info(f"HTTP初始化响应: 获取到数据")
                                        except:
                                            logger.warning(f"HTTP响应无法解析")
                                    else:
                                        # 等待SSE响应
                                        request_id = init_request["id"]
                                        wait_time = 0
                                        while wait_time < 10:
                                            if request_id in mcp_responses:
                                                init_data = mcp_responses.pop(request_id)
                                                logger.info(f"SSE初始化响应: 获取到数据")
                                                break
                                            await asyncio.sleep(0.5)
                                            wait_time += 0.5
                                    
                                    # 处理初始化数据
                                    if init_data and "result" in init_data:
                                        result_data = init_data["result"]
                                        server_info.update(result_data.get("serverInfo", {}))
                                        server_capabilities.update(result_data.get("capabilities", {}))
                                        logger.info(f"MCP服务器初始化成功: {server_info.get('name', 'Unknown')}")
                                    elif init_data and "error" in init_data:
                                        error_msg = init_data["error"].get("message", "未知错误")
                                        logger.error(f"初始化失败: {error_msg}")
                                        return
                                    else:
                                        logger.warning("未收到有效初始化响应，继续执行")
                                        server_info.update({"name": "Unknown MCP Server", "version": "unknown"})
                                    
                                    # 2. initialized通知
                                    logger.info("发送initialized通知...")
                                    initialized_notification = {
                                        "jsonrpc": "2.0",
                                        "method": "notifications/initialized"
                                    }
                                    
                                    notify_response = await client.post(
                                        message_endpoint, 
                                        json=initialized_notification, 
                                        headers=mcp_headers, 
                                        timeout=10
                                    )
                                    logger.info(f"通知响应状态码: {notify_response.status_code}")
                                    
                                    # 等待处理
                                    await asyncio.sleep(2)
                                    
                                    # 3. 获取工具列表  
                                    logger.info("获取工具列表...")
                                    tools_request = {
                                        "jsonrpc": "2.0",
                                        "id": str(uuid.uuid4()),
                                        "method": "tools/list",
                                        "params": {}
                                    }
                                    
                                    tools_response = await client.post(
                                        message_endpoint, 
                                        json=tools_request, 
                                        headers=mcp_headers, 
                                        timeout=15
                                    )
                                    
                                    logger.info(f"工具列表响应状态码: {tools_response.status_code}")
                                    
                                    # 处理工具响应
                                    tools_data = None
                                    if tools_response.text.strip():
                                        try:
                                            tools_data = tools_response.json()
                                            logger.info(f"HTTP工具响应: 获取到数据")
                                        except:
                                            logger.warning(f"HTTP工具响应无法解析")
                                    else:
                                        # 等待SSE响应
                                        request_id = tools_request["id"]
                                        logger.info(f"等待SSE工具响应: {request_id}")
                                        wait_time = 0
                                        while wait_time < 10:
                                            if request_id in mcp_responses:
                                                tools_data = mcp_responses.pop(request_id)
                                                logger.info(f"SSE工具响应: 获取到数据")
                                                break
                                            await asyncio.sleep(0.5)
                                            wait_time += 0.5
                                    
                                    # 处理工具数据
                                    if tools_data and "result" in tools_data and "tools" in tools_data["result"]:
                                        tools.extend(tools_data["result"]["tools"])
                                        logger.info(f"成功获取到 {len(tools_data['result']['tools'])} 个工具")
                                    elif tools_data and "error" in tools_data:
                                        error_msg = tools_data["error"].get("message", "未知错误")
                                        logger.warning(f"获取工具列表失败: {error_msg}")
                                    else:
                                        logger.warning("未获取到有效的工具列表")
                                    
                                    # 4. 获取资源列表
                                    logger.info("获取资源列表...")
                                    resources_request = {
                                        "jsonrpc": "2.0",
                                        "id": str(uuid.uuid4()),
                                        "method": "resources/list",
                                        "params": {}
                                    }
                                    
                                    try:
                                        resources_response = await client.post(
                                            message_endpoint, 
                                            json=resources_request, 
                                            headers=mcp_headers, 
                                            timeout=15
                                        )
                                        
                                        # 处理资源响应
                                        resources_data = None
                                        if resources_response.text.strip():
                                            try:
                                                resources_data = resources_response.json()
                                            except:
                                                pass
                                        else:
                                            request_id = resources_request["id"]
                                            wait_time = 0
                                            while wait_time < 5:
                                                if request_id in mcp_responses:
                                                    resources_data = mcp_responses.pop(request_id)
                                                    break
                                                await asyncio.sleep(0.5)
                                                wait_time += 0.5
                                        
                                        if resources_data and "result" in resources_data and "resources" in resources_data["result"]:
                                            resources.extend(resources_data["result"]["resources"])
                                            logger.info(f"获取到 {len(resources_data['result']['resources'])} 个资源")
                                    except Exception as e:
                                        logger.warning(f"获取资源列表失败: {e}")
                                    
                                    # 5. 获取提示词列表
                                    logger.info("获取提示词列表...")
                                    prompts_request = {
                                        "jsonrpc": "2.0",
                                        "id": str(uuid.uuid4()),
                                        "method": "prompts/list",
                                        "params": {}
                                    }
                                    
                                    try:
                                        prompts_response = await client.post(
                                            message_endpoint, 
                                            json=prompts_request, 
                                            headers=mcp_headers, 
                                            timeout=15
                                        )
                                        
                                        # 处理提示词响应
                                        prompts_data = None
                                        if prompts_response.text.strip():
                                            try:
                                                prompts_data = prompts_response.json()
                                            except:
                                                pass
                                        else:
                                            request_id = prompts_request["id"]
                                            wait_time = 0
                                            while wait_time < 5:
                                                if request_id in mcp_responses:
                                                    prompts_data = mcp_responses.pop(request_id)
                                                    break
                                                await asyncio.sleep(0.5)
                                                wait_time += 0.5
                                        
                                        if prompts_data and "result" in prompts_data and "prompts" in prompts_data["result"]:
                                            prompts.extend(prompts_data["result"]["prompts"])
                                            logger.info(f"获取到 {len(prompts_data['result']['prompts'])} 个提示词")
                                    except Exception as e:
                                        logger.warning(f"获取提示词列表失败: {e}")
                                    
                                    mcp_requests_completed = True
                                    logger.info("MCP请求序列完成")
                                    
                                except Exception as e:
                                    logger.error(f"MCP请求序列失败: {e}")
                            
                            # 启动并发任务
                            logger.info("启动SSE监听和MCP请求并发任务")
                            
                            sse_task = asyncio.create_task(sse_listener())
                            mcp_task = asyncio.create_task(send_mcp_requests())
                            
                            # 等待MCP任务完成或超时
                            try:
                                await asyncio.wait_for(mcp_task, timeout=45)
                            except asyncio.TimeoutError:
                                logger.warning("MCP任务超时")
                            finally:
                                # 取消SSE任务
                                sse_task.cancel()
                                try:
                                    await sse_task
                                except asyncio.CancelledError:
                                    pass
                            
                            # 检查是否成功
                            if not message_endpoint:
                                raise Exception("未能从SSE连接中获取消息端点")
                            elif not mcp_requests_completed:
                                logger.warning("SSE连接正常但MCP请求序列未完成")
                        
                    else:
                        # StreamHTTP协议使用POST请求
                        headers.setdefault("Content-Type", "application/json")
                        init_response = await client.post(url, json=init_request, headers=headers, timeout=15)
                        
                        logger.info(f"初始化响应状态码: {init_response.status_code}")
                        
                        if init_response.status_code != 200:
                            raise Exception(f"初始化失败，状态码: {init_response.status_code}, 响应: {init_response.text}")
                        
                        # 解析初始化响应
                        try:
                            init_data = init_response.json()
                            logger.info(f"初始化响应数据: {init_data}")
                            
                            # 检查是否有错误
                            if "error" in init_data:
                                error_msg = init_data["error"].get("message", "未知错误")
                                raise Exception(f"服务器返回错误: {error_msg}")
                            
                            # 获取服务器信息
                            result_data = init_data.get("result", {})
                            server_info = result_data.get("serverInfo", {})
                            server_capabilities = result_data.get("capabilities", {})
                            
                            # 验证协议版本
                            server_protocol_version = result_data.get("protocolVersion")
                            if server_protocol_version not in ["2025-03-26", "2024-11-05"]:
                                logger.warning(f"服务器协议版本不匹配: {server_protocol_version}")
                            
                            logger.info(f"MCP服务器初始化成功: {server_info.get('name', 'Unknown')}")
                            
                        except Exception as json_error:
                            logger.warning(f"解析初始化响应失败: {json_error}, 响应内容: {init_response.text}")
                            # 如果无法解析JSON，但状态码是200，可能是SSE格式或其他格式
                            server_info = {"name": "Unknown MCP Server", "version": "unknown"}
                            server_capabilities = {}
                        
                        # 2. 发送initialized通知（按照MCP标准）
                        initialized_notification = {
                            "jsonrpc": "2.0",
                            "method": "notifications/initialized"
                        }
                        
                        try:
                            await client.post(url, json=initialized_notification, headers=headers, timeout=10)
                            logger.info("已发送initialized通知")
                        except Exception as e:
                            logger.warning(f"发送initialized通知失败: {e}")
                            # 继续执行，不要因为通知失败而中断
                        
                        # 3. 获取工具列表
                        tools_request = {
                            "jsonrpc": "2.0",
                            "id": str(uuid.uuid4()),
                            "method": "tools/list",
                            "params": {}
                        }
                        
                        try:
                            tools_response = await client.post(url, json=tools_request, headers=headers, timeout=10)
                            if tools_response.status_code == 200:
                                tools_data = tools_response.json()
                                if "result" in tools_data and "tools" in tools_data["result"]:
                                    tools = tools_data["result"]["tools"]
                                    logger.info(f"获取到 {len(tools)} 个工具")
                        except Exception as e:
                            logger.warning(f"获取工具列表失败: {e}")
                        
                        # 4. 获取资源列表
                        resources_request = {
                            "jsonrpc": "2.0",
                            "id": str(uuid.uuid4()),
                            "method": "resources/list",
                            "params": {}
                        }
                        
                        try:
                            resources_response = await client.post(url, json=resources_request, headers=headers, timeout=10)
                            if resources_response.status_code == 200:
                                resources_data = resources_response.json()
                                if "result" in resources_data and "resources" in resources_data["result"]:
                                    resources = resources_data["result"]["resources"]
                                    logger.info(f"获取到 {len(resources)} 个资源")
                        except Exception as e:
                            logger.warning(f"获取资源列表失败: {e}")
                        
                        # 5. 获取提示词列表
                        prompts_request = {
                            "jsonrpc": "2.0",
                            "id": str(uuid.uuid4()),
                            "method": "prompts/list",
                            "params": {}
                        }
                        
                        try:
                            prompts_response = await client.post(url, json=prompts_request, headers=headers, timeout=10)
                            if prompts_response.status_code == 200:
                                prompts_data = prompts_response.json()
                                if "result" in prompts_data and "prompts" in prompts_data["result"]:
                                    prompts = prompts_data["result"]["prompts"]
                                    logger.info(f"获取到 {len(prompts)} 个提示词")
                        except Exception as e:
                            logger.warning(f"获取提示词列表失败: {e}")
                    
                except httpx.TimeoutException:
                    raise Exception("连接超时，请检查服务器是否正常运行")
                except httpx.ConnectError:
                    raise Exception("无法连接到服务器，请检查URL和网络连接")
                except Exception as e:
                    logger.error(f"初始化MCP服务器失败: {e}")
                    raise Exception(f"初始化失败: {str(e)}")
        else:
            raise Exception(f"不支持的传输协议: {transport_type}")
        
        # 更新服务器信息
        server.available_tools = tools
        server.tools_count = len(tools)
        server.available_resources = resources
        server.resources_count = len(resources)
        server.available_prompts = prompts
        server.prompts_count = len(prompts)
        server.server_info = server_info
        server.server_capabilities = server_capabilities
        server.status = "initialized"
        server.last_connected_at = datetime.now()
        server.updated_at = datetime.now()
        
        # 如果获取到服务器信息，更新描述
        if server_info.get("name") and server_info["name"] != "Unknown MCP Server":
            original_desc = server.description or ""
            server_name = server_info["name"]
            # 避免重复添加服务器名称
            if f"(服务器: {server_name})" not in original_desc:
                server.description = f"{original_desc} (服务器: {server_name})".strip()
        
        await db.commit()
        
        return {
            "success": True,
            "message": f"服务器初始化成功，获取到 {len(tools)} 个工具，{len(resources)} 个资源，{len(prompts)} 个提示词",
            "data": {
                "server_id": server_id,
                "server_info": server_info,
                "tools_count": len(tools),
                "resources_count": len(resources),
                "prompts_count": len(prompts),
                "protocol_version": "2025-03-26",
                "transport_type": transport_type,
                "init_time": datetime.now().isoformat()
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"初始化AI助手MCP服务器失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"初始化失败: {str(e)}"
        )

@router.get("/ai/mcp-servers/{server_id}", response_model=Dict[str, Any])
async def get_ai_mcp_server(
    server_id: str = Path(...),
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """获取AI助手MCP服务器详情"""
    try:
        # 获取服务器配置
        result = await db.execute(
            select(AIMCPServer).where(
                and_(
                    AIMCPServer.id == uuid.UUID(server_id),
                    AIMCPServer.project_id == project.id
                )
            )
        )
        server = result.scalars().first()
        
        if not server:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="MCP服务器不存在"
            )
        
        # 构建详情数据
        server_detail = {
            "id": str(server.id),
            "name": server.name,
            "description": server.description,
            "transport_type": server.transport_type,
            "transport_types": server.transport_types or [server.transport_type],
            "deployment_mode": server.deployment_mode,
            "source": server.source,
            "config": server.config,
            "enabled": server.enabled,
            "status": server.status,
            "last_connected_at": server.last_connected_at.isoformat() if server.last_connected_at else None,
            "tools_count": server.tools_count,
            "resources_count": server.resources_count,
            "prompts_count": server.prompts_count,
            "available_tools": server.available_tools or [],
            "available_resources": server.available_resources or [],
            "available_prompts": server.available_prompts or [],
            "server_info": server.server_info or {},
            "server_capabilities": server.server_capabilities or {},
            "created_at": server.created_at.isoformat(),
            "updated_at": server.updated_at.isoformat(),
            "created_by_id": str(server.created_by_id) if server.created_by_id else None,
            "sapi_server_id": str(server.sapi_server_id) if server.sapi_server_id else None
        }
        
        return {
            "success": True,
            "message": "获取AI助手MCP服务器详情成功",
            "data": server_detail
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取AI助手MCP服务器详情失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取服务器详情失败: {str(e)}"
        )

@router.get("/ai/mcp-servers/{server_id}/tools", response_model=List[Dict[str, Any]])
async def get_ai_mcp_server_tools(
    server_id: str = Path(...),
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """获取AI助手MCP服务器的工具列表"""
    try:
        # 获取服务器配置
        result = await db.execute(
            select(AIMCPServer).where(
                and_(
                    AIMCPServer.id == uuid.UUID(server_id),
                    AIMCPServer.project_id == project.id
                )
            )
        )
        server = result.scalars().first()
        
        if not server:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="MCP服务器不存在"
            )
        
        # 返回工具列表
        tools = server.available_tools or []
        return tools
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取AI助手MCP服务器工具列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取工具列表失败: {str(e)}"
        )

@router.post("/ai/mcp-servers/{server_id}/call-tool", response_model=Dict[str, Any])
async def call_ai_mcp_server_tool(
    server_id: str = Path(...),
    tool_call_data: Dict[str, Any] = Body(...),
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """调用AI助手MCP服务器的工具（支持SSE和StreamHTTP协议）"""
    try:
        # 获取服务器配置
        result = await db.execute(
            select(AIMCPServer).where(
                and_(
                    AIMCPServer.id == uuid.UUID(server_id),
                    AIMCPServer.project_id == project.id
                )
            )
        )
        server = result.scalars().first()
        
        if not server:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="MCP服务器不存在"
            )
        
        if not server.enabled:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="MCP服务器未启用"
            )
        
        transport_type = server.transport_type
        config = server.config or {}
        
        # 提取工具调用参数
        tool_name = tool_call_data.get("name")
        tool_arguments = tool_call_data.get("arguments", {})
        
        if not tool_name:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="缺少工具名称"
            )
        
        # 验证工具是否存在
        available_tools = server.available_tools or []
        tool_found = False
        for tool in available_tools:
            if tool.get("name") == tool_name:
                tool_found = True
                break
        
        if not tool_found:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"工具 '{tool_name}' 不存在"
            )
        
        tool_result = None
        
        if transport_type == "stdio":
            raise HTTPException(
                status_code=status.HTTP_501_NOT_IMPLEMENTED,
                detail="STDIO协议的工具调用暂未实现"
            )
            
        elif transport_type in ["sse", "streamhttp"]:
            url = config.get("url")
            if not url:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"{transport_type.upper()}协议需要配置URL"
                )
                
            headers = config.get("headers", {})
            
            async with httpx.AsyncClient() as client:
                try:
                    # 构建工具调用请求
                    tool_request = {
                        "jsonrpc": "2.0",
                        "id": str(uuid.uuid4()),
                        "method": "tools/call",
                        "params": {
                            "name": tool_name,
                            "arguments": tool_arguments
                        }
                    }
                    
                    logger.info(f"调用工具: {tool_name}, 参数: {tool_arguments}")
                    
                    if transport_type == "sse":
                        # SSE协议：需要先建立连接获取消息端点
                        sse_headers = headers.copy()
                        sse_headers.setdefault("Accept", "text/event-stream")
                        sse_headers.setdefault("Cache-Control", "no-cache")
                        sse_headers.setdefault("Connection", "keep-alive")
                        
                        async with client.stream('GET', url, headers=sse_headers, timeout=30) as sse_response:
                            if sse_response.status_code != 200:
                                raise Exception(f"SSE连接失败，状态码: {sse_response.status_code}")
                            
                            logger.info("SSE连接建立成功，获取消息端点...")
                            
                            message_endpoint = None
                            tool_response_data = None
                            
                            # 存储响应
                            mcp_responses = {}
                            
                            async def sse_listener():
                                """监听SSE响应"""
                                nonlocal message_endpoint, tool_response_data, mcp_responses
                                
                                buffer = ""
                                try:
                                    async for chunk in sse_response.aiter_text():
                                        buffer += chunk
                                        lines = buffer.split('\n')
                                        buffer = lines[-1]
                                        
                                        for line in lines[:-1]:
                                            line = line.strip()
                                            if line:
                                                logger.info(f"SSE: {line}")
                                                
                                                # 解析端点
                                                if line.startswith("data:") and "/mcp/message" in line:
                                                    endpoint_path = line[5:].strip()
                                                    if endpoint_path.startswith("/mcp/message"):
                                                        from urllib.parse import urlparse
                                                        parsed_url = urlparse(url)
                                                        message_endpoint = f"{parsed_url.scheme}://{parsed_url.netloc}{endpoint_path}"
                                                        logger.info(f"获取到消息端点: {message_endpoint}")
                                                
                                                # 解析工具响应
                                                elif line.startswith("data:") and line != "data:" and "{" in line:
                                                    try:
                                                        response_text = line[5:].strip()
                                                        response_data = json.loads(response_text)
                                                        if "id" in response_data:
                                                            request_id = response_data["id"]
                                                            mcp_responses[request_id] = response_data
                                                            logger.info(f"收到工具响应: {request_id}")
                                                    except Exception as e:
                                                        logger.warning(f"解析响应失败: {e}")
                                except Exception as e:
                                    logger.warning(f"SSE监听异常: {e}")
                            
                            async def call_tool():
                                """调用工具"""
                                nonlocal tool_response_data
                                
                                # 等待获取端点
                                wait_time = 0
                                while not message_endpoint and wait_time < 10:
                                    await asyncio.sleep(0.5)
                                    wait_time += 0.5
                                
                                if not message_endpoint:
                                    raise Exception("未能获取到消息端点")
                                
                                # 发送工具调用请求
                                call_headers = {"Content-Type": "application/json"}
                                call_headers.update(headers)
                                
                                response = await client.post(
                                    message_endpoint,
                                    json=tool_request,
                                    headers=call_headers,
                                    timeout=30
                                )
                                
                                logger.info(f"工具调用响应状态码: {response.status_code}")
                                
                                # 检查HTTP响应
                                if response.text.strip():
                                    try:
                                        tool_response_data = response.json()
                                        logger.info("从HTTP响应获取工具结果")
                                    except:
                                        logger.warning("HTTP响应无法解析")
                                
                                # 如果HTTP响应为空，等待SSE响应
                                if not tool_response_data:
                                    request_id = tool_request["id"]
                                    wait_time = 0
                                    while wait_time < 15:
                                        if request_id in mcp_responses:
                                            tool_response_data = mcp_responses[request_id]
                                            logger.info("从SSE响应获取工具结果")
                                            break
                                        await asyncio.sleep(0.5)
                                        wait_time += 0.5
                            
                            # 启动并发任务
                            sse_task = asyncio.create_task(sse_listener())
                            call_task = asyncio.create_task(call_tool())
                            
                            try:
                                await asyncio.wait_for(call_task, timeout=45)
                            except asyncio.TimeoutError:
                                logger.warning("工具调用超时")
                            finally:
                                sse_task.cancel()
                            
                            tool_result = tool_response_data
                    
                    else:
                        # StreamHTTP协议：直接POST请求
                        call_headers = headers.copy()
                        call_headers.setdefault("Content-Type", "application/json")
                        
                        response = await client.post(
                            url,
                            json=tool_request,
                            headers=call_headers,
                            timeout=30
                        )
                        
                        logger.info(f"工具调用响应状态码: {response.status_code}")
                        
                        if response.status_code == 200:
                            try:
                                tool_result = response.json()
                                logger.info("StreamHTTP工具调用成功")
                            except Exception as e:
                                logger.warning(f"解析工具响应失败: {e}")
                                tool_result = {
                                    "error": {
                                        "code": -32603,
                                        "message": f"解析响应失败: {str(e)}"
                                    }
                                }
                        else:
                            tool_result = {
                                "error": {
                                    "code": response.status_code,
                                    "message": f"工具调用失败，状态码: {response.status_code}"
                                }
                            }
                    
                except httpx.TimeoutException:
                    tool_result = {
                        "error": {
                            "code": -32603,
                            "message": "工具调用超时"
                        }
                    }
                except httpx.ConnectError:
                    tool_result = {
                        "error": {
                            "code": -32603,
                            "message": "无法连接到MCP服务器"
                        }
                    }
                except Exception as e:
                    logger.error(f"工具调用失败: {e}")
                    tool_result = {
                        "error": {
                            "code": -32603,
                            "message": f"工具调用失败: {str(e)}"
                        }
                    }
        else:
            raise HTTPException(
                status_code=status.HTTP_501_NOT_IMPLEMENTED,
                detail=f"不支持的传输协议: {transport_type}"
            )
        
        # 处理工具结果
        if tool_result:
            if "error" in tool_result:
                error_info = tool_result["error"]
                return {
                    "success": False,
                    "message": f"工具调用失败: {error_info.get('message', '未知错误')}",
                    "data": {
                        "tool_name": tool_name,
                        "error": error_info,
                        "call_time": datetime.now().isoformat()
                    }
                }
            elif "result" in tool_result:
                result_data = tool_result["result"]
                return {
                    "success": True,
                    "message": f"工具 '{tool_name}' 调用成功",
                    "data": {
                        "tool_name": tool_name,
                        "result": result_data,
                        "call_time": datetime.now().isoformat()
                    }
                }
            else:
                return {
                    "success": True,
                    "message": f"工具 '{tool_name}' 调用完成",
                    "data": {
                        "tool_name": tool_name,
                        "result": tool_result,
                        "call_time": datetime.now().isoformat()
                    }
                }
        else:
            return {
                "success": False,
                "message": "工具调用失败，未收到响应",
                "data": {
                    "tool_name": tool_name,
                    "call_time": datetime.now().isoformat()
                }
            }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"调用AI助手MCP工具失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"工具调用失败: {str(e)}"
        ) 