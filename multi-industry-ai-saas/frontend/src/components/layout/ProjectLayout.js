import React, { useState, useEffect, useMemo } from 'react';
import { Layout, Menu, Button, Avatar, Dropdown, Badge, theme, Tooltip } from 'antd';
import {
  MenuFoldOutlined, MenuUnfoldOutlined, UserOutlined, BellOutlined, CloudOutlined
} from '@ant-design/icons';
import { Link, Outlet, useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import ProjectSwitcher from './ProjectSwitcher';
import { getProjectMenuItems } from '../../config/menuConfig';
import projectSpaceService from '../../services/projectSpaceService';
import notificationsApi from '../../services/api/project/notifications';
import { initDateTimeUtils } from '../../utils/dateTimeUtils';
import './ProjectLayout.css';

const { Header, Sider, Content } = Layout;

/**
 * 项目布局组件
 * 作为单租户页面的基础框架，类似于retail-ai-system的布局
 */
const ProjectLayout = () => {
  const [collapsed, setCollapsed] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const { user, logout } = useAuth();
  const { token } = theme.useToken();
  const [currentProject, setCurrentProject] = useState(null);
  const [industryType, setIndustryType] = useState('retail'); // 默认为零售行业
  const [switcherVisible, setSwitcherVisible] = useState(false);
  const [openKeys, setOpenKeys] = useState(['store', 'operation', 'purchase', 'finance', 'inventory', 'settings', 'plugins']);
  const [storageUsage, setStorageUsage] = useState({ used: 0, total: 1024 * 1024 * 500, usedPercent: 0 });
  const [unreadNotificationCount, setUnreadNotificationCount] = useState(0);

  // 处理菜单折叠状态变化
  useEffect(() => {
    if (collapsed) {
      setOpenKeys([]);
    } else {
      setOpenKeys(['store', 'operation', 'purchase', 'finance', 'inventory', 'settings', 'plugins']);
    }
  }, [collapsed]);

  // 获取当前项目信息和用户角色
  useEffect(() => {
    const projectId = localStorage.getItem('project_id');
    console.log('ProjectLayout - 从localStorage获取项目ID:', projectId);

    if (projectId) {
      const fetchProject = async () => {
        try {
          // 初始化日期时间工具 - 只有在确定有项目ID时才初始化
          try {
            await initDateTimeUtils();
            console.log('日期时间工具初始化成功');
          } catch (dateTimeError) {
            console.error('日期时间工具初始化失败:', dateTimeError);
          }

          // 获取最新的用户角色信息
          try {
            console.log('正在获取用户项目角色信息，项目ID:', projectId);
            const response = await fetch(`/api/v1/auth/me?project_id=${projectId}`, {
              headers: {
                'Authorization': `Bearer ${localStorage.getItem('token')}`,
                'X-Project-ID': projectId
              }
            });

            if (response.ok) {
              const userInfo = await response.json();
              console.log('获取用户项目角色信息成功:', userInfo);

              // 更新用户数据中的项目角色信息
              const userData = JSON.parse(localStorage.getItem('user_data') || '{}');
              userData.project_role = userInfo.project_role;
              userData.project_role_name = userInfo.project_role_name;
              userData.is_project_admin = userInfo.is_project_admin;
              userData.project_id = userInfo.project_id || projectId;

              // 保存更新后的用户数据
              localStorage.setItem('user_data', JSON.stringify(userData));

              // 确保项目ID被正确保存
              if (userInfo.project_id) {
                localStorage.setItem('project_id', userInfo.project_id);
                console.log('更新localStorage中的项目ID:', userInfo.project_id);
              }
            } else {
              console.error('获取用户项目角色信息失败:', response.statusText);
            }
          } catch (error) {
            console.error('获取用户项目角色信息出错:', error);
          }

          // 使用本地存储的项目信息
          const mockProject = {
            id: projectId,
            name: localStorage.getItem('project_name') || '测试项目',
            industry_type: 'retail',
            description: '这是一个测试项目',
            created_at: new Date().toISOString()
          };
          setCurrentProject(mockProject);
          setIndustryType(mockProject.industry_type);

          // 获取真实存储使用情况
          try {
            const storageData = await projectSpaceService.getStorageUsage();
            if (storageData && storageData.success && storageData.data && storageData.data.total > 0) {
              const usedPercent = Math.floor((storageData.data.used / storageData.data.total) * 100);
              setStorageUsage({
                used: storageData.data.used,
                total: storageData.data.total,
                usedPercent: usedPercent
              });
            }
          } catch (storageError) {
            console.error('获取存储使用情况失败:', storageError);
          }

          // 获取未读通知数量
          try {
            const notificationData = await notificationsApi.getUnreadCount();
            if (notificationData && notificationData.success) {
              setUnreadNotificationCount(notificationData.count);
            }
          } catch (notificationError) {
            console.error('获取未读通知数量失败:', notificationError);
          }
        } catch (error) {
          console.error('获取项目信息失败:', error);
          // 使用本地存储的项目信息作为备用
          const mockProject = {
            id: projectId,
            name: localStorage.getItem('project_name') || '测试项目',
            industry_type: 'retail',
            description: '这是一个测试项目',
            created_at: new Date().toISOString()
          };
          setCurrentProject(mockProject);
          setIndustryType(mockProject.industry_type);
        }
      };

      fetchProject();
    }
  }, []);

  // 获取当前选中的菜单项
  const getSelectedKeys = () => {
    const path = location.pathname;
    console.log('当前路径:', path);

    // 如果是系统设置相关路径，返回对应的key
    if (path === '/project/settings/users') return ['/project/settings/users'];
    if (path === '/project/settings/roles') return ['/project/settings/roles'];
    if (path === '/project/settings/system') return ['/project/settings/system'];

    return [path];
  };

  // 用户菜单项
  const userMenuItems = [
    {
      key: 'profile',
      label: '个人中心',
      onClick: () => navigate('/project/user/profile'),
    },
    {
      key: 'notification_settings',
      label: '通知设置',
      onClick: () => navigate('/project/user/notification-settings'),
    },
    {
      key: 'logout',
      label: '退出登录',
      onClick: logout,
    },
  ];

  // 通知菜单项
  const notificationMenuItems = [
    {
      key: 'notifications',
      label: '查看全部通知',
      onClick: () => navigate('/project/notifications'),
    },
  ];

  // 处理项目切换
  const handleProjectSwitch = (project) => {
    // 实际项目中应该保存选择的项目ID到localStorage，并刷新页面
    localStorage.setItem('project_id', project.id);
    setCurrentProject(project);
    // 可以添加其他逻辑，如刷新页面或重新获取数据
  };

  // 检查用户是否有权限访问菜单
  const hasPermission = useMemo(() => {
    // 从localStorage中获取用户数据
    const userData = JSON.parse(localStorage.getItem('user_data') || '{}');
    console.log('初始化权限检查:', userData);

    return (requiredRole) => {
      // 如果没有指定角色要求，则所有用户都可以访问
      if (!requiredRole) return true;

      // 项目管理员和租户管理员拥有所有权限
      if (userData.is_project_admin || userData.is_tenant_admin) {
        return true;
      }

      // 优先使用项目角色（如果存在）
      const userRole = userData.project_role || userData.role;

      // 检查用户角色是否匹配
      if (Array.isArray(requiredRole)) {
        // 如果requiredRole包含tenant_admin，并且用户是租户管理员，则有权限
        if (requiredRole.includes('tenant_admin') && userData.is_tenant_admin) {
          return true;
        }

        // 检查精确匹配
        if (requiredRole.includes(userRole)) {
          return true;
        }

        // 检查包含匹配（例如，如果角色是 purchase_admin，应该匹配 purchase）
        for (const role of requiredRole) {
          if (userRole && userRole.includes(role)) {
            return true;
          }
          if (role && userRole && role.includes(userRole)) {
            return true;
          }
        }

        return false;
      }

      // 如果requiredRole是tenant_admin，并且用户是租户管理员，则有权限
      if (requiredRole === 'tenant_admin' && userData.is_tenant_admin) {
        return true;
      }

      // 单个角色匹配
      if (userRole === requiredRole) {
        return true;
      }

      // 包含匹配
      if (userRole && userRole.includes(requiredRole)) {
        return true;
      }
      if (requiredRole && userRole && requiredRole.includes(userRole)) {
        return true;
      }

      return false;
    };
  }, []);

  // 根据行业类型和用户角色获取菜单项
  const getMenuItems = useMemo(() => {
    return getProjectMenuItems({
      industryType,
      hasPermission,
      navigate
    });
  }, [industryType, hasPermission, navigate]);

  return (
    <Layout className="project-layout">
      <Sider
        trigger={null}
        collapsible
        collapsed={collapsed}
        width={220}
        theme="light"
        className="project-sider"
      >
        <div className="logo">
          {!collapsed && <span className="logo-text">{currentProject?.name || '项目'}</span>}
        </div>
        <Menu
          theme="light"
          mode="inline"
          selectedKeys={getSelectedKeys()}
          openKeys={openKeys}
          onOpenChange={setOpenKeys}
          items={getMenuItems}
          className="project-menu"
        />
      </Sider>
      <Layout>
        <Header className="project-header">
          <div className="header-left">
            <Button
              type="text"
              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
              onClick={() => setCollapsed(!collapsed)}
              className="trigger-button"
            />
          </div>
          <div className="header-right">
            <Tooltip
              title={`项目空间 (已使用 ${storageUsage.usedPercent || 0}%)`}
              placement="bottom"
            >
              <Button
                type="text"
                icon={<CloudOutlined />}
                className="header-button"
                onClick={() => navigate('/project/space')}
              />
            </Tooltip>
            <Dropdown menu={{ items: notificationMenuItems }} placement="bottomRight" arrow>
              <Badge count={unreadNotificationCount} className="notification-badge">
                <Button type="text" icon={<BellOutlined />} className="header-button" />
              </Badge>
            </Dropdown>
            <div
              className="project-selector"
              onClick={() => setSwitcherVisible(true)}
            >
              <span className="project-name">{currentProject?.name || '项目'}</span>
            </div>
            <Dropdown menu={{ items: userMenuItems }} placement="bottomRight" arrow>
              <div className="user-info">
                <Avatar icon={<UserOutlined />} className="user-avatar" />
                <span className="user-name">{user?.username || '用户'}</span>
              </div>
            </Dropdown>
          </div>
        </Header>
        <Content className="project-content">
          <Outlet />
        </Content>
      </Layout>

      {/* 项目切换器 */}
      <ProjectSwitcher
        visible={switcherVisible}
        onClose={() => setSwitcherVisible(false)}
        onSelect={handleProjectSwitch}
      />
    </Layout>
  );
};

export default ProjectLayout;
