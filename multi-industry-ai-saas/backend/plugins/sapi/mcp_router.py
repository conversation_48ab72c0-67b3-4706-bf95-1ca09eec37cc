from fastapi import API<PERSON>outer, Request, HTTPException, Depends, Response
from fastapi.responses import JSONResponse, StreamingResponse
from sse_starlette.sse import EventSourceResponse
import httpx
import logging
import json
import os
from typing import Optional, Dict, Any
from .mcp_proxy import MCPProxy

logger = logging.getLogger(__name__)

# Instantiate the MCPProxy service
# This might need to be configured if MCPProxy requires specific init arguments
# not handled by its defaults (e.g. if settings aren't available here easily)
mcp_proxy_service = MCPProxy()

mcp_router = APIRouter()

# Modified POST endpoint for StreamHTTP
@mcp_router.post("/servers/{server_id}/mcp")
async def mcp_proxy_endpoint(server_id: str, request: Request):
    """
    MCP代理端点 - 转发MCP请求到指定MCP服务器 (StreamHTTP POST)
    Handles both regular JSON responses and streamed SSE responses from the backend.
    """
    status_code, response_headers, stream_generator = await mcp_proxy_service.proxy_request_to_streamhttp(server_id, request)

    media_type = response_headers.get("Content-Type", "").lower()

    if "text/event-stream" in media_type:
        logger.info(f"Proxying StreamHTTP POST for {server_id} as SSE stream.")
        # Ensure any other necessary headers from response_headers are passed. 
        # StreamingResponse will set Content-Type from media_type.
        # We might want to filter/select which headers from response_headers are safe to pass on.
        # For now, passing all captured ones.
        return StreamingResponse(
            stream_generator, 
            status_code=status_code, 
            media_type="text/event-stream", # Explicitly set
            headers=response_headers
        )
    else:
        logger.info(f"Proxying StreamHTTP POST for {server_id} as regular response (Content-Type: {media_type})")
        # Accumulate content for non-streaming responses (e.g., JSON errors from proxy)
        full_content = b""
        async for chunk in stream_generator:
            full_content += chunk
        
        # Use Response to allow FastAPI to handle content based on headers
        return Response(
            content=full_content, 
            status_code=status_code, 
            headers=response_headers # This contains the Content-Type from backend/proxy
        )

@mcp_router.get("/servers/{server_id}/mcp", response_class=StreamingResponse)
async def mcp_stream_event_endpoint(server_id: str, request: Request):
    """
    MCP代理端点 - 处理到MCP服务器的StreamHTTP GET事件流请求。
    """
    return await mcp_proxy.proxy_event_stream_request(server_id, request)