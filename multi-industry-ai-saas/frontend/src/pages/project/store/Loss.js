import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Table, 
  Button, 
  Space, 
  Tag, 
  Input, 
  DatePicker, 
  Form, 
  Select, 
  Modal, 
  message, 
  Descriptions, 
  Image, 
  Typography, 
  Divider,
  Row,
  Col,
  InputNumber,
  Upload
} from 'antd';
import { 
  SearchOutlined, 
  PlusOutlined, 
  EditOutlined, 
  DeleteOutlined, 
  EyeOutlined, 
  ReloadOutlined,
  UploadOutlined,
  RollbackOutlined
} from '@ant-design/icons';
import dayjs from "dayjs";
import lossApi from '../../../services/api/project/loss';  
import storeApi from '../../../services/api/project/store';
import productApi from '../../../services/api/project/product';

const { Title, Text } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;
const { TextArea } = Input;

// 状态标签颜色映射
const statusColors = {
  pending: 'gold',
  approved: 'green',
  rejected: 'red',
  withdrawn: 'default'
};

// 状态文本映射
const statusTexts = {
  pending: '待审核',
  approved: '已通过',
  rejected: '已拒绝',
  withdrawn: '已撤回'
};

// 报损原因映射
const reasonTexts = {
  expired: '过期',
  damaged: '损坏',
  lost: '丢失',
  quality: '质量问题',
  other: '其他'
};

const StoreLoss = () => {
  // 状态管理
  const [loading, setLoading] = useState(false);
  const [lossRecords, setLossRecords] = useState([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });
  const [searchParams, setSearchParams] = useState({
    status: '',
    search: '',
    dateRange: null,
    storeId: null
  });
  const [stores, setStores] = useState([]);
  const [products, setProducts] = useState([]);
  const [currentRecord, setCurrentRecord] = useState(null);
  const [detailVisible, setDetailVisible] = useState(false);
  const [createVisible, setCreateVisible] = useState(false);
  const [editVisible, setEditVisible] = useState(false);
  const [withdrawVisible, setWithdrawVisible] = useState(false);
  const [fileList, setFileList] = useState([]);
  const [form] = Form.useForm();

  // 获取门店列表
  const fetchStores = async () => {
    try {
      const response = await storeApi.getList();
      if (response && response.items) {
        setStores(response.items);
      }
    } catch (error) {
      console.error('获取门店列表失败:', error);
      message.error('获取门店列表失败');
    }
  };

  // 获取产品列表
  const fetchProducts = async () => {
    try {
      const response = await productApi.getList();
      if (response && response.data) {
        setProducts(response.data);
      }
    } catch (error) {
      console.error('获取产品列表失败:', error);
      message.error('获取产品列表失败');
    }
  };

  // 获取报损记录列表
  const fetchLossRecords = async (params = {}) => {
    setLoading(true);
    try {
      const { current, pageSize } = pagination;
      const { status, search, dateRange, storeId } = searchParams;
      
      const queryParams = {
        skip: (current - 1) * pageSize,
        limit: pageSize,
        status,
        search: search || undefined,
        store_id: storeId || undefined,
        ...params
      };
      
      // 添加日期范围
      if (dateRange && dateRange[0] && dateRange[1]) {
        queryParams.start_date = dateRange[0].format('YYYY-MM-DD');
        queryParams.end_date = dateRange[1].format('YYYY-MM-DD');
      }
      
      const response = await lossApi.getList(queryParams);
      
      if (response) {
        setLossRecords(response.items || []);
        setPagination({
          ...pagination,
          current: response.page || 1,
          pageSize: response.size || 10,
          total: response.total || 0
        });
      }
    } catch (error) {
      console.error('获取报损记录失败:', error);
      message.error('获取报损记录失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取报损记录详情
  const fetchLossDetail = async (id) => {
    setLoading(true);
    try {
      const response = await lossApi.getDetail(id);
      return response;
    } catch (error) {
      console.error('获取报损记录详情失败:', error);
      message.error('获取报损记录详情失败');
      return null;
    } finally {
      setLoading(false);
    }
  };

  // 创建报损记录
  const handleCreate = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);
      
      // 准备上传的图片
      const images = fileList.map(file => file.originFileObj);
      
      await lossApi.create(values, images);
      
      message.success('创建报损记录成功');
      setCreateVisible(false);
      form.resetFields();
      setFileList([]);
      fetchLossRecords();
    } catch (error) {
      console.error('创建报损记录失败:', error);
      if (error.errorFields) {
        // 表单验证错误
        return;
      }
      message.error('创建报损记录失败');
    } finally {
      setLoading(false);
    }
  };

  // 更新报损记录
  const handleEdit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);
      
      // 准备上传的图片
      const images = fileList
        .filter(file => file.originFileObj)
        .map(file => file.originFileObj);
      
      await lossApi.update(currentRecord.id, values, images);
      
      message.success('更新报损记录成功');
      setEditVisible(false);
      form.resetFields();
      setFileList([]);
      setCurrentRecord(null);
      fetchLossRecords();
    } catch (error) {
      console.error('更新报损记录失败:', error);
      if (error.errorFields) {
        // 表单验证错误
        return;
      }
      message.error('更新报损记录失败');
    } finally {
      setLoading(false);
    }
  };

  // 撤回报损记录
  const handleWithdraw = async () => {
    if (!currentRecord) return;
    setLoading(true);
    try {
      await lossApi.updateStatus(currentRecord.id, { status: 'withdrawn' });
      message.success('撤回报损记录成功');
      setWithdrawVisible(false);
      setCurrentRecord(null);
      fetchLossRecords();
    } catch (error) {
      console.error('撤回报损记录失败:', error);
      message.error('撤回报损记录失败');
    } finally {
      setLoading(false);
    }
  };

  // 处理表格分页、排序、筛选变化
  const handleTableChange = (pagination, filters, sorter) => {
    setPagination(pagination);
    fetchLossRecords({
      skip: (pagination.current - 1) * pagination.pageSize,
      limit: pagination.pageSize
    });
  };

  // 处理搜索表单提交
  const handleSearch = (values) => {
    const { status, search, dateRange, storeId } = values;
    setSearchParams({ status, search, dateRange, storeId });
    setPagination({ ...pagination, current: 1 });
    fetchLossRecords({
      skip: 0,
      status,
      search: search || undefined,
      store_id: storeId || undefined,
      start_date: dateRange && dateRange[0] ? dateRange[0].format('YYYY-MM-DD') : undefined,
      end_date: dateRange && dateRange[1] ? dateRange[1].format('YYYY-MM-DD') : undefined
    });
  };

  // 重置搜索表单
  const handleReset = () => {
    form.resetFields();
    const defaultParams = {
      status: '',
      search: '',
      dateRange: null,
      storeId: null
    };
    setSearchParams(defaultParams);
    setPagination({ ...pagination, current: 1 });
    fetchLossRecords({
      skip: 0
    });
  };

  // 查看详情
  const handleViewDetail = async (record) => {
    const detailData = await fetchLossDetail(record.id);
    if (detailData) {
      setCurrentRecord(detailData);
      setDetailVisible(true);
    }
  };

  // 打开编辑弹窗
  const handleOpenEdit = async (record) => {
    const detailData = await fetchLossDetail(record.id);
    if (detailData) {
      setCurrentRecord(detailData);
      
      // 设置表单初始值
      form.setFieldsValue({
        quantity: detailData.quantity,
        reason: detailData.reason,
        notes: detailData.notes
      });
      
      // 设置图片列表
      if (detailData.images && detailData.images.length > 0) {
        const imageList = detailData.images.map((image, index) => ({
          uid: `-${index}`,
          name: image.filename || `图片${index + 1}`,
          status: 'done',
          url: image.url,
          thumbUrl: image.url
        }));
        setFileList(imageList);
      } else {
        setFileList([]);
      }
      
      setEditVisible(true);
    }
  };

  // 打开撤回弹窗
  const handleOpenWithdraw = async (record) => {
    const detailData = await fetchLossDetail(record.id);
    if (detailData) {
      setCurrentRecord(detailData);
      setWithdrawVisible(true);
    }
  };

  // 处理图片上传前的钩子
  const handleBeforeUpload = (file) => {
    // 不自动上传，由表单提交时统一处理
    return false;
  };

  // 处理图片列表变化
  const handleFileChange = ({ fileList }) => {
    setFileList(fileList);
  };

  // 打开新增报损弹窗时，重置表单
  const handleOpenCreateModal = () => {
    form.resetFields();
    setFileList([]);
    setCreateVisible(true);
  };

  // 初始化
  useEffect(() => {
    fetchStores();
    fetchProducts();
    fetchLossRecords();
  }, []);

  // 搜索表单
  const SearchForm = () => (
    <Form
      layout="inline"
      form={form}
      initialValues={searchParams}
      onFinish={handleSearch}
      style={{ marginBottom: 16 }}
    >
      <Form.Item name="status" label="状态">
        <Select style={{ width: 120 }}>
          <Option value="">全部</Option>
          <Option value="pending">待审核</Option>
          <Option value="approved">已通过</Option>
          <Option value="rejected">已拒绝</Option>
          <Option value="withdrawn">已撤回</Option>
        </Select>
      </Form.Item>
      
      <Form.Item name="storeId" label="门店">
        <Select style={{ width: 150 }} allowClear placeholder="选择门店">
          <Option value="">全部门店</Option>
          {stores.map(store => (
            <Option key={String(store.id)} value={String(store.id)}>{store.name}</Option>
          ))}
        </Select>
      </Form.Item>
      
      <Form.Item name="dateRange" label="日期范围">
        <RangePicker style={{ width: 240 }} />
      </Form.Item>
      
      <Form.Item name="search" label="搜索">
        <Input
          placeholder="产品名称/报损原因"
          prefix={<SearchOutlined />}
          style={{ width: 200 }}
        />
      </Form.Item>
      
      <Form.Item>
        <Button type="primary" htmlType="submit">
          搜索
        </Button>
      </Form.Item>
      
      <Form.Item>
        <Button onClick={handleReset}>
          重置
        </Button>
      </Form.Item>
    </Form>
  );

  // 表格列定义
  const columns = [
    {
      title: '门店',
      dataIndex: 'store_name',
      key: 'store_name',
      width: 150,
    },
    {
      title: '产品',
      dataIndex: 'product_name',
      key: 'product_name',
      width: 200,
      render: (text, record) => (
        <span>
          {text}
          <br />
          <Text type="secondary">{record.product_code}</Text>
        </span>
      ),
    },
    {
      title: '数量',
      dataIndex: 'quantity',
      key: 'quantity',
      width: 100,
      render: (text, record) => (
        <span>
          {text} {record.product_unit || '个'}
        </span>
      ),
    },
    {
      title: '金额',
      dataIndex: 'total_amount',
      key: 'total_amount',
      width: 100,
      render: (text) => `¥${text.toFixed(2)}`,
    },
    {
      title: '原因',
      dataIndex: 'reason',
      key: 'reason',
      width: 120,
      render: (text) => reasonTexts[text] || text,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status) => (
        <Tag color={statusColors[status] || 'default'}>
          {statusTexts[status] || status}
        </Tag>
      ),
    },
    {
      title: '申请时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 180,
      render: (text) => dayjs(text).format('YYYY-MM-DD HH:mm'),
    },
    {
      title: '操作',
      key: 'action',
      width: 180,
      render: (_, record) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => handleViewDetail(record)}
          >
            详情
          </Button>
          
          {record.status === 'pending' && (
            <>
              <Button
                type="link"
                size="small"
                icon={<EditOutlined />}
                onClick={() => handleOpenEdit(record)}
              >
                编辑
              </Button>
              <Button
                type="link"
                danger
                size="small"
                icon={<RollbackOutlined />}
                onClick={() => handleOpenWithdraw(record)}
              >
                撤回
              </Button>
            </>
          )}
        </Space>
      ),
    },
  ];

  return (
    <div className="store-loss-page">
      <Card>
        <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 16 }}>
          <Title level={4}>门店报损管理</Title>
          <Space>
            <Button
              icon={<ReloadOutlined />}
              onClick={() => fetchLossRecords()}
            >
              刷新
            </Button>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleOpenCreateModal}
            >
              新增报损
            </Button>
          </Space>
        </div>

        <SearchForm />

        <Table
          columns={columns}
          dataSource={lossRecords}
          rowKey="id"
          pagination={pagination}
          onChange={handleTableChange}
          loading={loading}
          scroll={{ x: 1200 }}
        />
      </Card>

      {/* 详情弹窗 */}
      <Modal
        title="报损详情"
        open={detailVisible}
        onCancel={() => setDetailVisible(false)}
        footer={[
          <Button key="close" onClick={() => setDetailVisible(false)}>
            关闭
          </Button>
        ]}
        width={700}
      >
        {currentRecord && (
          <>
            <Descriptions bordered column={2}>
              <Descriptions.Item label="门店" span={2}>{currentRecord.store_name}</Descriptions.Item>
              <Descriptions.Item label="产品名称" span={2}>{currentRecord.product_name}</Descriptions.Item>
              <Descriptions.Item label="产品编码">{currentRecord.product_code}</Descriptions.Item>
              <Descriptions.Item label="单位">{currentRecord.product_unit || '个'}</Descriptions.Item>
              <Descriptions.Item label="数量">{currentRecord.quantity}</Descriptions.Item>
              <Descriptions.Item label="单价">¥{currentRecord.unit_price.toFixed(2)}</Descriptions.Item>
              <Descriptions.Item label="总金额">¥{currentRecord.total_amount.toFixed(2)}</Descriptions.Item>
              <Descriptions.Item label="报损原因">{reasonTexts[currentRecord.reason] || currentRecord.reason}</Descriptions.Item>
              <Descriptions.Item label="状态">
                <Tag color={statusColors[currentRecord.status] || 'default'}>
                  {statusTexts[currentRecord.status] || currentRecord.status}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="申请人">{currentRecord.creator_name}</Descriptions.Item>
              <Descriptions.Item label="申请时间" span={2}>
                {dayjs(currentRecord.created_at).format('YYYY-MM-DD HH:mm:ss')}
              </Descriptions.Item>
              
              {currentRecord.status === 'approved' && (
                <>
                  <Descriptions.Item label="审核人">{currentRecord.approver_name}</Descriptions.Item>
                  <Descriptions.Item label="审核时间">
                    {dayjs(currentRecord.approved_at).format('YYYY-MM-DD HH:mm:ss')}
                  </Descriptions.Item>
                </>
              )}
              
              {currentRecord.status === 'rejected' && (
                <>
                  <Descriptions.Item label="审核人">{currentRecord.approver_name}</Descriptions.Item>
                  <Descriptions.Item label="审核时间">
                    {dayjs(currentRecord.approved_at).format('YYYY-MM-DD HH:mm:ss')}
                  </Descriptions.Item>
                  <Descriptions.Item label="拒绝原因" span={2}>
                    {currentRecord.reject_reason}
                  </Descriptions.Item>
                </>
              )}
              
              {currentRecord.notes && (
                <Descriptions.Item label="备注" span={2}>
                  {currentRecord.notes}
                </Descriptions.Item>
              )}
            </Descriptions>
            
            {currentRecord.images && currentRecord.images.length > 0 && (
              <>
                <Divider orientation="left">报损图片</Divider>
                <Row gutter={[16, 16]}>
                  {currentRecord.images.map((image, index) => (
                    <Col span={8} key={index}>
                      <Image
                        src={image.url}
                        alt={`报损图片${index + 1}`}
                        style={{ width: '100%', height: 150, objectFit: 'cover' }}
                      />
                    </Col>
                  ))}
                </Row>
              </>
            )}
          </>
        )}
      </Modal>

      {/* 创建报损弹窗 */}
      <Modal
        title="新增报损"
        open={createVisible}
        onOk={handleCreate}
        onCancel={() => setCreateVisible(false)}
        confirmLoading={loading}
        width={600}
      >
         <Form
          form={form}
          layout="vertical"
        >
          <Form.Item
            name="store_id"
            label="门店"
            rules={[{ required: true, message: '请选择门店' }]}
          >
            <Select placeholder="请选择门店">
              {stores.map(store => (
                <Option key={String(store.id)} value={String(store.id)}>{store.name}</Option>
              ))}
            </Select>
          </Form.Item>
          
          <Form.Item
            name="product_id"
            label="产品"
            rules={[{ required: true, message: '请选择产品' }]}
          >
            <Select
              placeholder="请选择产品"
              showSearch
              optionFilterProp="children"
              filterOption={(input, option) =>
                option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
              }
            >
              {Array.isArray(products) && products.length > 0 && products.map(product => (
                <Option key={String(product.id)} value={String(product.id)}>
                  {product.name} {product.sku ? `(${product.sku})` : ''}
                </Option>
              ))}
            </Select>
          </Form.Item>
          
          <Form.Item
            name="quantity"
            label="数量"
            rules={[
              { required: true, message: '请输入数量' },
              { type: 'number', min: 0.01, message: '数量必须大于0' }
            ]}
          >
            <InputNumber style={{ width: '100%' }} placeholder="请输入数量" precision={2} />
          </Form.Item>
          
          <Form.Item
            name="reason"
            label="报损原因"
            rules={[{ required: true, message: '请选择报损原因' }]}
          >
            <Select placeholder="请选择报损原因">
              <Option value="expired">过期</Option>
              <Option value="damaged">损坏</Option>
              <Option value="lost">丢失</Option>
              <Option value="quality">质量问题</Option>
              <Option value="other">其他</Option>
            </Select>
          </Form.Item>
          
          <Form.Item
            name="notes"
            label="备注"
          >
            <TextArea rows={4} placeholder="请输入备注信息" />
          </Form.Item>
          
          <Form.Item
            label="上传图片"
          >
            <Upload
              listType="picture-card"
              fileList={fileList}
              beforeUpload={handleBeforeUpload}
              onChange={handleFileChange}
              multiple
            >
              {fileList.length >= 8 ? null : (
                <div>
                  <UploadOutlined />
                  <div style={{ marginTop: 8 }}>上传</div>
                </div>
              )}
            </Upload>
            <Text type="secondary">最多上传8张图片，每张不超过2MB</Text>
          </Form.Item>
        </Form>
      </Modal>

      {/* 编辑报损弹窗 */}
      <Modal
        title="编辑报损"
        open={editVisible}
        onOk={handleEdit}
        onCancel={() => setEditVisible(false)}
        confirmLoading={loading}
        width={600}
      >
        {currentRecord && (
          <Form
            form={form}
            layout="vertical"
          >
            <Form.Item
              label="门店"
            >
              <Input value={currentRecord.store_name} disabled />
            </Form.Item>
            
            <Form.Item
              label="产品"
            >
              <Input value={currentRecord.product_name} disabled />
            </Form.Item>
            
            <Form.Item
              name="quantity"
              label="数量"
              rules={[
                { required: true, message: '请输入数量' },
                { type: 'number', min: 0.01, message: '数量必须大于0' }
              ]}
            >
              <InputNumber style={{ width: '100%' }} placeholder="请输入数量" precision={2} />
            </Form.Item>
            
            <Form.Item
              name="reason"
              label="报损原因"
              rules={[{ required: true, message: '请选择报损原因' }]}
            >
              <Select placeholder="请选择报损原因">
                <Option value="expired">过期</Option>
                <Option value="damaged">损坏</Option>
                <Option value="lost">丢失</Option>
                <Option value="quality">质量问题</Option>
                <Option value="other">其他</Option>
              </Select>
            </Form.Item>
            
            <Form.Item
              name="notes"
              label="备注"
            >
              <TextArea rows={4} placeholder="请输入备注信息" />
            </Form.Item>
            
            <Form.Item
              label="上传图片"
            >
              <Upload
                listType="picture-card"
                fileList={fileList}
                beforeUpload={handleBeforeUpload}
                onChange={handleFileChange}
                multiple
              >
                {fileList.length >= 8 ? null : (
                  <div>
                    <UploadOutlined />
                    <div style={{ marginTop: 8 }}>上传</div>
                  </div>
                )}
              </Upload>
              <Text type="secondary">最多上传8张图片，每张不超过2MB</Text>
            </Form.Item>
          </Form>
        )}
      </Modal>

      {/* 撤回报损弹窗 */}
      <Modal
        title="撤回报损"
        open={withdrawVisible}
        onOk={handleWithdraw}
        onCancel={() => setWithdrawVisible(false)}
        confirmLoading={loading}
      >
        {currentRecord && (
          <div>
            <p>确认撤回以下报损申请？</p>
            <Descriptions column={1} bordered size="small">
              <Descriptions.Item label="门店">{currentRecord.store_name}</Descriptions.Item>
              <Descriptions.Item label="产品">{currentRecord.product_name}</Descriptions.Item>
              <Descriptions.Item label="数量">{currentRecord.quantity} {currentRecord.product_unit || '个'}</Descriptions.Item>
              <Descriptions.Item label="金额">¥{currentRecord.total_amount.toFixed(2)}</Descriptions.Item>
              <Descriptions.Item label="原因">{reasonTexts[currentRecord.reason] || currentRecord.reason}</Descriptions.Item>
            </Descriptions>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default StoreLoss;
