from sqlalchemy import Column, String, Boolean, DateTime, ForeignKey, JSON, Text, Integer, UniqueConstraint, BigInteger
from sqlalchemy.dialects.postgresql import UUID, ARRAY
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid
from datetime import datetime

from db.database import Base

class StorageFile(Base):
    """存储文件模型"""
    __tablename__ = "storage_files"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=True)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id"), nullable=False)
    folder_id = Column(UUID(as_uuid=True), ForeignKey("storage_folders.id"), nullable=True)
    name = Column(String(255), nullable=False)
    original_name = Column(String(255), nullable=False)
    path = Column(String(500), nullable=False)
    mime_type = Column(String(100), nullable=False)
    size = Column(BigInteger, nullable=False)  # 文件大小（字节）
    extension = Column(String(20), nullable=True)
    storage_provider = Column(String(50), default="local")  # local, s3, oss, etc.
    storage_path = Column(String(500), nullable=False)  # 存储路径
    is_public = Column(Boolean, default=False)
    access_url = Column(String(1000), nullable=True)  # 公开访问地址
    uploaded_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    status = Column(String(20), default="active")  # active, deleted, locked
    file_metadata = Column(JSON, default=dict)
    thumbnail_path = Column(String(500), nullable=True)  # 缩略图路径
    preview_available = Column(Boolean, default=False)  # 是否支持预览
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    deleted_at = Column(DateTime(timezone=True), nullable=True)

    # 关系
    tenant = relationship("Tenant", backref="storage_files")
    project = relationship("Project", backref="storage_files")
    folder = relationship("StorageFolder", back_populates="files")
    uploader = relationship("User", foreign_keys=[uploaded_by])
    shares = relationship("FileShare", back_populates="file", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<StorageFile(id={self.id}, name='{self.name}')>"


class StorageFolder(Base):
    """存储文件夹模型"""
    __tablename__ = "storage_folders"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=True)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id"), nullable=False)
    parent_id = Column(UUID(as_uuid=True), ForeignKey("storage_folders.id"), nullable=True)
    name = Column(String(255), nullable=False)
    path = Column(String(500), nullable=False)  # 完整路径，如 /project/folder1/folder2
    created_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    status = Column(String(20), default="active")  # active, deleted
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    deleted_at = Column(DateTime(timezone=True), nullable=True)

    # 关系
    tenant = relationship("Tenant", backref="storage_folders")
    project = relationship("Project", backref="storage_folders")
    parent = relationship("StorageFolder", remote_side=[id], backref="subfolders")
    creator = relationship("User", foreign_keys=[created_by])
    files = relationship("StorageFile", back_populates="folder")

    __table_args__ = (
        UniqueConstraint('project_id', 'parent_id', 'name', name='uq_folder_name_per_parent'),
    )

    def __repr__(self):
        return f"<StorageFolder(id={self.id}, name='{self.name}', path='{self.path}')>"


class StorageQuota(Base):
    """存储配额模型"""
    __tablename__ = "storage_quotas"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=True)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id"), nullable=False)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)  # 如果为空，则为项目配额
    total_quota = Column(BigInteger, nullable=False)  # 总配额（字节）
    used_quota = Column(BigInteger, default=0, nullable=False)  # 已使用配额（字节）
    image_quota = Column(BigInteger, default=0, nullable=False)  # 图片占用空间
    document_quota = Column(BigInteger, default=0, nullable=False)  # 文档占用空间
    other_quota = Column(BigInteger, default=0, nullable=False)  # 其他文件占用空间
    last_updated = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # 关系
    tenant = relationship("Tenant", backref="storage_quotas")
    project = relationship("Project", backref="storage_quotas")
    user = relationship("User", backref="storage_quotas")

    __table_args__ = (
        UniqueConstraint('project_id', 'user_id', name='uq_quota_per_project_user'),
    )

    def __repr__(self):
        return f"<StorageQuota(id={self.id}, project_id='{self.project_id}', user_id='{self.user_id}')>"

    def get_usage_percentage(self):
        """获取存储使用百分比"""
        if self.total_quota == 0:
            return 100  # 防止除以零
        return (self.used_quota / self.total_quota) * 100

    def has_sufficient_space(self, file_size):
        """检查是否有足够的空间存储新文件"""
        return (self.used_quota + file_size) <= self.total_quota

    def update_category_usage(self, category, size, operation="add"):
        """更新各类别文件的存储空间使用情况"""
        if category == "image":
            if operation == "add":
                self.image_quota += size
            else:
                self.image_quota = max(0, self.image_quota - size)
        elif category == "document":
            if operation == "add":
                self.document_quota += size
            else:
                self.document_quota = max(0, self.document_quota - size)
        else:  # other
            if operation == "add":
                self.other_quota += size
            else:
                self.other_quota = max(0, self.other_quota - size)
        
        # 更新总使用量
        if operation == "add":
            self.used_quota += size
        else:
            self.used_quota = max(0, self.used_quota - size)


class FileShare(Base):
    """文件分享模型"""
    __tablename__ = "file_shares"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    file_id = Column(UUID(as_uuid=True), ForeignKey("storage_files.id"), nullable=False)
    share_token = Column(String(100), nullable=False, unique=True)
    created_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    expires_at = Column(DateTime(timezone=True), nullable=True)
    password = Column(String(100), nullable=True)
    access_count = Column(Integer, default=0)
    last_accessed_at = Column(DateTime(timezone=True), nullable=True)
    status = Column(String(20), default="active")  # active, expired, revoked
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 关系
    file = relationship("StorageFile", back_populates="shares")
    creator = relationship("User", foreign_keys=[created_by])

    def __repr__(self):
        return f"<FileShare(id={self.id}, file_id='{self.file_id}', share_token='{self.share_token}')>"
