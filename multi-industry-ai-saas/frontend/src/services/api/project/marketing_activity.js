import api from '../httpClient';
import { getProjectId } from '../httpClient';

const marketingActivity = {
  // 获取营销活动列表
  getList: async (params) => {
    const projectId = getProjectId();
    return api.get(`/project/${projectId}/marketing-activities`, { params });
  },

  // 获取营销活动详情
  getDetail: async (activityId) => {
    const projectId = getProjectId();
    return api.get(`/project/${projectId}/marketing-activities/${activityId}`);
  },

  // 创建营销活动
  create: async (data) => {
    const projectId = getProjectId();
    return api.post(`/project/${projectId}/marketing-activities`, data);
  },

  // 更新营销活动
  update: async (activityId, data) => {
    const projectId = getProjectId();
    return api.put(`/project/${projectId}/marketing-activities/${activityId}`, data);
  },

  // 更新营销活动状态
  updateStatus: async (activityId, status) => {
    const projectId = getProjectId();
    return api.patch(`/project/${projectId}/marketing-activities/${activityId}/status`, { status });
  },

  // 删除营销活动
  delete: async (activityId) => {
    const projectId = getProjectId();
    return api.delete(`/project/${projectId}/marketing-activities/${activityId}`);
  },

  // 获取营销活动统计数据
  getStats: async () => {
    const projectId = getProjectId();
    return api.get(`/project/${projectId}/marketing-activities-stats`);
  }
};

export default marketingActivity;