/* 大转盘样式 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes bounce {
  0%, 100% { transform: translateX(-50%) translateY(0); }
  50% { transform: translateX(-50%) translateY(-5px); }
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

@keyframes float {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 新增美化动画 */
@keyframes glow {
  0%, 100% { 
    box-shadow: 
      0 0 0 3px #ffeb3b, 
      0 0 0 6px #ff5722,
      0 0 25px rgba(255, 87, 34, 0.4),
      inset 0 0 20px rgba(255, 235, 59, 0.3);
  }
  50% { 
    box-shadow: 
      0 0 0 3px #ffeb3b, 
      0 0 0 6px #ff5722,
      0 0 35px rgba(255, 87, 34, 0.6),
      0 0 50px rgba(255, 235, 59, 0.4),
      inset 0 0 20px rgba(255, 235, 59, 0.5);
  }
}

@keyframes sparkle {
  0%, 100% { opacity: 0; transform: scale(0) rotate(0deg); }
  50% { opacity: 1; transform: scale(1) rotate(180deg); }
}

@keyframes shimmer {
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
}

/* 转盘旋转时的特殊效果 */
.wheel-spinning {
  animation: glow 0.5s ease-in-out infinite alternate !important;
}

/* 闪烁装饰元素 */
.wheel-container::before {
  content: '';
  position: absolute;
  top: -20px;
  left: -20px;
  right: -20px;
  bottom: -20px;
  border-radius: 50%;
  background: conic-gradient(
    from 0deg,
    transparent 0deg,
    rgba(255, 215, 0, 0.3) 45deg,
    transparent 90deg,
    rgba(255, 215, 0, 0.3) 135deg,
    transparent 180deg,
    rgba(255, 215, 0, 0.3) 225deg,
    transparent 270deg,
    rgba(255, 215, 0, 0.3) 315deg,
    transparent 360deg
  );
  animation: spin 3s linear infinite;
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.wheel-container:hover::before {
  opacity: 1;
}

.wheel-fortune-wrapper {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 10px;
  position: relative;
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
  animation: fadeIn 0.8s ease-in-out;
}

.wheel-fortune-header {
  position: relative;
  z-index: 1;
  text-align: center;
  margin-bottom: 20px;
  width: 100%;
  max-width: 800px;
}

.game-logo {
  width: 100px;
  height: 100px;
  object-fit: contain;
  margin-bottom: 10px;
  border-radius: 50%;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  background-color: white;
  padding: 5px;
  animation: float 6s ease-in-out infinite;
}

.game-title {
  color: #333;
  margin-bottom: 10px !important;
  font-weight: bold;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.game-description {
  color: #666;
  margin-bottom: 20px !important;
  max-width: 600px;
  margin-left: auto !important;
  margin-right: auto !important;
}

.wheel-fortune-content {
  position: relative;
  z-index: 1;
  padding: 10px;
  width: 100%;
  max-width: 800px;
  margin-bottom: 20px;
  overflow: visible;
}

.wheel-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  width: 100%;
}

.loading-icon {
  font-size: 40px;
  color: #ff5722;
  animation: pulse 1.5s infinite ease-in-out;
}

.loading-text {
  margin-top: 20px;
  font-size: 16px;
  color: #666;
}

.settings-bar {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 20px;
}

.settings-item {
  display: flex;
  align-items: center;
  margin-left: 20px;
}

.settings-label {
  margin-right: 10px;
  color: #666;
  font-size: 14px;
}

/* 转盘容器 */
.wheel-container {
  position: relative;
  width: 360px;
  height: 360px;
  margin: 0 auto 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
  aspect-ratio: 1 / 1; /* 确保宽高比为1:1，保持圆形 */
  border-radius: 50%; /* 确保容器是圆形 */
  overflow: visible; /* 改为visible，让阴影和指针可见 */
  /* 添加美观的背景渐变 */
  background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, rgba(0,0,0,0.05) 100%);
  /* 添加外层装饰阴影 */
  filter: drop-shadow(0 10px 30px rgba(0,0,0,0.2));
}

/* 转盘样式 - 经典风格 */
.wheel-fortune-classic {
  width: 340px; /* 固定宽度 */
  height: 340px; /* 固定高度，确保1:1比例 */
  border-radius: 50%;
  position: relative;
  overflow: hidden;
  border: 8px solid #ff5722;
  box-shadow: 
    0 0 0 3px #ffeb3b, 
    0 0 0 6px #ff5722,
    0 0 25px rgba(255, 87, 34, 0.4),
    inset 0 0 20px rgba(255, 235, 59, 0.3);
  transition: transform 4s cubic-bezier(0.17, 0.67, 0.83, 0.67);
  background: radial-gradient(circle, #ffffff 0%, #f8f9fa 100%);
  transform-style: preserve-3d;
  will-change: transform;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: auto;
  /* 确保完美圆形 */
  min-width: 340px;
  min-height: 340px;
  max-width: 340px;
  max-height: 340px;
}

/* 转盘样式 - 苹果风格 */
.wheel-fortune-apple {
  width: 340px;
  height: 340px;
  border-radius: 50%;
  position: relative;
  overflow: hidden;
  border: 8px solid #007AFF;
  box-shadow: 
    0 0 0 3px #5AC8FA, 
    0 0 0 6px #007AFF,
    0 0 25px rgba(0, 122, 255, 0.4),
    inset 0 0 20px rgba(90, 200, 250, 0.3);
  transition: transform 4s cubic-bezier(0.17, 0.67, 0.83, 0.67);
  background: radial-gradient(circle, #ffffff 0%, #f0f8ff 100%);
  transform-style: preserve-3d;
  will-change: transform;
  margin: auto;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 340px;
  min-height: 340px;
  max-width: 340px;
  max-height: 340px;
}

/* 转盘样式 - 卡通风格 */
.wheel-fortune-cartoon {
  width: 340px;
  height: 340px;
  border-radius: 50%;
  position: relative;
  overflow: hidden;
  border: 8px solid #FF6B6B;
  box-shadow: 
    0 0 0 3px #FFE66D, 
    0 0 0 6px #FF6B6B,
    0 0 25px rgba(255, 107, 107, 0.4),
    inset 0 0 20px rgba(255, 230, 109, 0.3);
  transition: transform 4s cubic-bezier(0.17, 0.67, 0.83, 0.67);
  background: radial-gradient(circle, #ffffff 0%, #fff5f5 100%);
  transform-style: preserve-3d;
  will-change: transform;
  margin: auto;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 340px;
  min-height: 340px;
  max-width: 340px;
  max-height: 340px;
}

/* 转盘样式 - 商业风格 */
.wheel-fortune-business {
  width: 340px;
  height: 340px;
  border-radius: 50%;
  position: relative;
  overflow: hidden;
  border: 8px solid #34495E;
  box-shadow: 
    0 0 0 3px #BDC3C7, 
    0 0 0 6px #34495E,
    0 0 25px rgba(52, 73, 94, 0.4),
    inset 0 0 20px rgba(189, 195, 199, 0.3);
  transition: transform 4s cubic-bezier(0.17, 0.67, 0.83, 0.67);
  background: radial-gradient(circle, #ECF0F1 0%, #D5DBDB 100%);
  transform-style: preserve-3d;
  will-change: transform;
  margin: auto;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 340px;
  min-height: 340px;
  max-width: 340px;
  max-height: 340px;
}

/* 转盘样式 - 吉卜力风格 */
.wheel-fortune-ghibli {
  width: 340px;
  height: 340px;
  border-radius: 50%;
  position: relative;
  overflow: hidden;
  border: 8px solid #1A7A89;
  box-shadow: 
    0 0 0 3px #D4A017, 
    0 0 0 6px #1A7A89,
    0 0 25px rgba(26, 122, 137, 0.4),
    inset 0 0 20px rgba(212, 160, 23, 0.3);
  transition: transform 4s cubic-bezier(0.17, 0.67, 0.83, 0.67);
  background: radial-gradient(circle, #ffffff 0%, #f0f8f0 100%);
  transform-style: preserve-3d;
  will-change: transform;
  margin: auto;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 340px;
  min-height: 340px;
  max-width: 340px;
  max-height: 340px;
}

/* 移除固定分割线，改为动态生成与奖品数量匹配的分割线 */

/* 指针样式 - 所有风格统一使用美观的红色指针 */
.wheel-pointer-classic,
.wheel-pointer-apple,
.wheel-pointer-cartoon,
.wheel-pointer-business,
.wheel-pointer-ghibli {
  position: absolute;
  top: -15px; /* 改回顶部，指向转盘 */
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  /* 使用border创建三角形指针 */
  border-left: 20px solid transparent;
  border-right: 20px solid transparent;
  border-top: 35px solid #ff0000;
  z-index: 15;
  filter: drop-shadow(0 3px 8px rgba(0, 0, 0, 0.4));
  animation: bounce 2s infinite ease-in-out;
}

/* 为指针添加装饰效果 */
.wheel-pointer-classic::before,
.wheel-pointer-apple::before,
.wheel-pointer-cartoon::before,
.wheel-pointer-business::before,
.wheel-pointer-ghibli::before {
  content: '';
  position: absolute;
  top: -32px;
  left: -15px;
  width: 0;
  height: 0;
  border-left: 15px solid transparent;
  border-right: 15px solid transparent;
  border-top: 28px solid #ffffff;
  z-index: 1;
}

/* 指针底部装饰圆点 */
.wheel-pointer-classic::after,
.wheel-pointer-apple::after,
.wheel-pointer-cartoon::after,
.wheel-pointer-business::after,
.wheel-pointer-ghibli::after {
  content: '';
  position: absolute;
  top: -45px;
  left: -8px;
  width: 16px;
  height: 16px;
  background: radial-gradient(circle, #ff0000 0%, #cc0000 100%);
  border-radius: 50%;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

/* 转盘中心装饰 - 经典风格 */
.wheel-center-classic {
  content: '';
  position: absolute;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: radial-gradient(circle, #ffffff 0%, #f8f9fa 50%, #e9ecef 100%);
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border: 4px solid #ff5722;
  box-shadow: 
    0 0 0 2px #ffeb3b, 
    0 0 0 4px #ff5722,
    0 0 15px rgba(255, 87, 34, 0.4),
    inset 0 2px 4px rgba(255, 255, 255, 0.8),
    inset 0 -2px 4px rgba(0, 0, 0, 0.1);
  z-index: 5;
}

/* 转盘中心装饰 - 苹果风格 */
.wheel-center-apple {
  content: '';
  position: absolute;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: radial-gradient(circle, #ffffff 0%, #f0f8ff 50%, #e6f3ff 100%);
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border: 4px solid #007AFF;
  box-shadow: 
    0 0 0 2px #5AC8FA, 
    0 0 0 4px #007AFF,
    0 0 15px rgba(0, 122, 255, 0.4),
    inset 0 2px 4px rgba(255, 255, 255, 0.8),
    inset 0 -2px 4px rgba(0, 0, 0, 0.1);
  z-index: 5;
}

/* 转盘中心装饰 - 卡通风格 */
.wheel-center-cartoon {
  content: '';
  position: absolute;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: radial-gradient(circle, #ffffff 0%, #fff5f5 50%, #ffe6e6 100%);
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border: 4px solid #FF6B6B;
  box-shadow: 
    0 0 0 2px #FFE66D, 
    0 0 0 4px #FF6B6B,
    0 0 15px rgba(255, 107, 107, 0.4),
    inset 0 2px 4px rgba(255, 255, 255, 0.8),
    inset 0 -2px 4px rgba(0, 0, 0, 0.1);
  z-index: 5;
}

/* 转盘中心装饰 - 商业风格 */
.wheel-center-business {
  content: '';
  position: absolute;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: radial-gradient(circle, #ECF0F1 0%, #D5DBDB 50%, #BDC3C7 100%);
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border: 4px solid #34495E;
  box-shadow: 
    0 0 0 2px #BDC3C7, 
    0 0 0 4px #34495E,
    0 0 15px rgba(52, 73, 94, 0.4),
    inset 0 2px 4px rgba(255, 255, 255, 0.8),
    inset 0 -2px 4px rgba(0, 0, 0, 0.1);
  z-index: 5;
}

/* 转盘中心装饰 - 吉卜力风格 */
.wheel-center-ghibli {
  content: '';
  position: absolute;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: radial-gradient(circle, #ffffff 0%, #f0f8f0 50%, #e6f0e6 100%);
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border: 4px solid #1A7A89;
  box-shadow: 
    0 0 0 2px #D4A017, 
    0 0 0 4px #1A7A89,
    0 0 15px rgba(26, 122, 137, 0.4),
    inset 0 2px 4px rgba(255, 255, 255, 0.8),
    inset 0 -2px 4px rgba(0, 0, 0, 0.1);
  z-index: 5;
}

.wheel-item {
  position: absolute;
  top: 0;
  left: 0;
  width: 50%;
  height: 50%;
  transform-origin: bottom right;
  overflow: hidden;
  text-align: center;
  /* 确保扇形不会超出转盘边界 */
  clip-path: polygon(100% 100%, 0 100%, 0 0);
}

.wheel-item-content {
  position: absolute;
  left: -100%;
  width: 200%;
  height: 200%;
  transform-origin: 100% 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding-top: 10px;
  transform: rotate(90deg);
  /* 确保内容不超出扇形区域 */
  clip-path: polygon(100% 0%, 0% 0%, 100% 100%);
}

.prize-image {
  width: 35px;
  height: 35px;
  object-fit: contain;
  margin-bottom: 5px;
  position: relative;
  z-index: 1;
  margin-top: -20px;
  filter: drop-shadow(0 0 3px rgba(255, 255, 255, 0.8));
}

/* 确保"谢谢参与"文字在各种背景上都清晰可见 */
.prize-name {
  font-size: 12px;
  font-weight: bold;
  color: #333;
  text-shadow: 0 0 3px white, 0 0 6px white;
  max-width: 80px;
  padding: 2px 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 4px;
  line-height: 1.2;
  margin-top: 5px;
  position: relative;
  z-index: 10;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  transform: rotate(0deg);
}

/* 按钮样式 */
.wheel-button-container {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
  width: 80px;
  height: 80px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: white;
  border-radius: 50%;
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);
}

/* 按钮样式 - 经典风格 */
.wheel-button-classic {
  background-color: #ff5722 !important;
  color: white !important;
  border: none !important;
  font-weight: bold !important;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.2) !important;
  height: 60px !important;
  width: 60px !important;
  border-radius: 50% !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  text-align: center !important;
  padding: 0 !important;
  font-size: 16px !important;
  line-height: 1.2 !important;
  transition: all 0.3s ease !important;
  position: relative !important;
  z-index: 10 !important;
}

/* 按钮样式 - 苹果风格 */
.wheel-button-apple {
  background-color: #007AFF !important;
  color: white !important;
  border: none !important;
  font-weight: bold !important;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.2) !important;
  height: 60px !important;
  width: 60px !important;
  border-radius: 50% !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  text-align: center !important;
  padding: 0 !important;
  font-size: 16px !important;
  line-height: 1.2 !important;
  transition: all 0.3s ease !important;
  position: relative !important;
  z-index: 10 !important;
}

/* 按钮样式 - 卡通风格 */
.wheel-button-cartoon {
  background-color: #FF6B6B !important;
  color: white !important;
  border: none !important;
  font-weight: bold !important;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.2) !important;
  height: 60px !important;
  width: 60px !important;
  border-radius: 50% !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  text-align: center !important;
  padding: 0 !important;
  font-size: 16px !important;
  line-height: 1.2 !important;
  transition: all 0.3s ease !important;
  position: relative !important;
  z-index: 10 !important;
}

/* 按钮样式 - 商业风格 */
.wheel-button-business {
  background-color: #3498DB !important;
  color: white !important;
  border: none !important;
  font-weight: bold !important;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.2) !important;
  height: 60px !important;
  width: 60px !important;
  border-radius: 50% !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  text-align: center !important;
  padding: 0 !important;
  font-size: 16px !important;
  line-height: 1.2 !important;
  transition: all 0.3s ease !important;
  position: relative !important;
  z-index: 10 !important;
}

/* 按钮样式 - 吉卜力风格 */
.wheel-button-ghibli {
  background-color: #1A7A89 !important;
  color: white !important;
  border: none !important;
  font-weight: bold !important;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.2) !important;
  height: 60px !important;
  width: 60px !important;
  border-radius: 50% !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  text-align: center !important;
  padding: 0 !important;
  font-size: 16px !important;
  line-height: 1.2 !important;
  transition: all 0.3s ease !important;
  position: relative !important;
  z-index: 10 !important;
}

.wheel-button-classic:hover, .wheel-button-apple:hover, .wheel-button-cartoon:hover, .wheel-button-business:hover, .wheel-button-ghibli:hover,
.wheel-button-classic:focus, .wheel-button-apple:focus, .wheel-button-cartoon:focus, .wheel-button-business:focus, .wheel-button-ghibli:focus {
  transform: scale(1.1);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3) !important;
}

.remaining-draws {
  text-align: center;
  margin: 10px 0;
  font-size: 16px;
  color: #333;
  font-weight: bold;
}

.remaining-count {
  color: #ff5722;
  font-size: 18px;
  margin: 0 5px;
}

.action-buttons {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
  justify-content: center;
  margin-top: 10px;
}

.action-button {
  min-width: 110px;
  height: 40px;
  border-radius: 20px;
  transition: all 0.3s ease;
}

.action-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
}

.prize-result {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 20px;
  background-color: #fff9f0;
  border-radius: 10px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.prize-result-image {
  width: 80px;
  height: 80px;
  object-fit: contain;
  border-radius: 8px;
  border: 1px solid #FFD8A8;
}

.prize-result-info {
  flex: 1;
}

.prize-result-title {
  margin-bottom: 5px !important;
  color: #ff5722;
}

.prize-result-description {
  color: #666;
  margin-bottom: 0 !important;
}

.wheel-fortune-footer {
  position: relative;
  z-index: 1;
  width: 100%;
  max-width: 1000px;
  text-align: center;
  padding: 15px 0;
  margin-top: 10px;
  color: #666;
  font-size: 14px;
}

.footer-links {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 10px;
}

.footer-link {
  color: #666;
  text-decoration: none;
}

.footer-link:hover {
  color: #ff5722;
  text-decoration: underline;
}

/* 响应式样式 */
@media (max-width: 1200px) {
  .wheel-fortune-content {
    max-width: 800px;
  }
}

@media (max-width: 992px) {
  .wheel-fortune-content {
    max-width: 700px;
    padding: 25px;
  }

  .wheel-container {
    width: 300px;
    height: 300px;
  }

  .wheel-fortune-classic, .wheel-fortune-apple, .wheel-fortune-cartoon, .wheel-fortune-business, .wheel-fortune-ghibli {
    width: 280px;
    height: 280px;
    min-width: 280px;
    min-height: 280px;
    max-width: 280px;
    max-height: 280px;
  }

  .game-title {
    font-size: 28px;
  }
}

@media (max-width: 768px) {
  .wheel-fortune-container {
    padding: 20px;
  }

  .wheel-fortune-content {
    max-width: 100%;
    padding: 20px;
  }

  .wheel-container {
    width: 260px;
    height: 260px;
  }

  .wheel-fortune-classic, .wheel-fortune-apple, .wheel-fortune-cartoon, .wheel-fortune-business, .wheel-fortune-ghibli {
    width: 240px;
    height: 240px;
    min-width: 240px;
    min-height: 240px;
    max-width: 240px;
    max-height: 240px;
  }

  .game-title {
    font-size: 24px;
  }

  .game-description {
    font-size: 14px;
  }

  .prize-image {
    width: 25px;
    height: 25px;
  }

  .prize-name {
    font-size: 11px;
    max-width: 60px;
  }
}

@media (max-width: 576px) {
  .wheel-fortune-container {
    padding: 15px;
  }

  .wheel-fortune-content {
    padding: 15px;
  }

  .wheel-container {
    width: 240px;
    height: 240px;
    margin-bottom: 20px;
  }

  .wheel-fortune-classic, .wheel-fortune-apple, .wheel-fortune-cartoon, .wheel-fortune-business, .wheel-fortune-ghibli {
    width: 220px;
    height: 220px;
    min-width: 220px;
    min-height: 220px;
    max-width: 220px;
    max-height: 220px;
    border-width: 6px;
  }

  .wheel-pointer-classic, .wheel-pointer-apple, .wheel-pointer-cartoon, .wheel-pointer-business, .wheel-pointer-ghibli {
    top: -12px;
    border-left: 15px solid transparent;
    border-right: 15px solid transparent;
    border-top: 25px solid #ff0000;
  }

  .wheel-pointer-classic::before,
  .wheel-pointer-apple::before,
  .wheel-pointer-cartoon::before,
  .wheel-pointer-business::before,
  .wheel-pointer-ghibli::before {
    top: -23px;
    left: -12px;
    border-left: 12px solid transparent;
    border-right: 12px solid transparent;
    border-top: 20px solid #ffffff;
  }

  .wheel-pointer-classic::after,
  .wheel-pointer-apple::after,
  .wheel-pointer-cartoon::after,
  .wheel-pointer-business::after,
  .wheel-pointer-ghibli::after {
    top: -32px;
    left: -6px;
    width: 12px;
    height: 12px;
  }

  .wheel-center-classic, .wheel-center-apple, .wheel-center-cartoon, .wheel-center-business, .wheel-center-ghibli {
    width: 45px;
    height: 45px;
    border-width: 3px;
  }

  .wheel-button-classic, .wheel-button-apple, .wheel-button-cartoon, .wheel-button-business, .wheel-button-ghibli {
    width: 45px !important;
    height: 45px !important;
    font-size: 12px !important;
  }

  .wheel-button-container {
    width: 60px;
    height: 60px;
  }

  .action-buttons {
    flex-direction: column;
    gap: 10px;
  }

  .action-button {
    width: 100%;
  }

  .game-logo {
    width: 80px;
    height: 80px;
  }

  .game-title {
    font-size: 20px;
  }

  .prize-result {
    flex-direction: column;
    text-align: center;
  }

  .prize-result-image {
    margin-right: 0;
    margin-bottom: 15px;
  }

  .settings-bar {
    flex-direction: column;
    align-items: flex-end;
  }

  .settings-item {
    margin-left: 0;
    margin-bottom: 10px;
  }

  .prize-image {
    width: 22px;
    height: 22px;
  }

  .prize-name {
    font-size: 10px;
    max-width: 50px;
  }
}
