#!/usr/bin/env python3
"""
测试修复后的AI助手MCP系统
"""
import asyncio
import sys
import os
sys.path.append("/media/dev/multi-industry-ai-saas/backend")

# 设置Django环境
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "settings")

import httpx
import uuid
import json
import time

async def test_ai_mcp_system():
    """测试修复后的AI助手MCP系统"""
    print("=== 测试修复后的AI助手MCP系统 ===")
    
    # 模拟高德MCP配置
    gaode_config = {
        "url": "https://mcp.amap.com/sse?key=94ed64a48ce623e65b18134befb65750",
        "headers": {}
    }
    
    print("1. 测试MCP初始化函数...")
    
    # 导入修复后的函数
    try:
        from api.project.ai_mcp_servers import _send_mcp_requests_in_sse_context
        print("✅ 成功导入修复后的函数")
    except Exception as e:
        print(f"❌ 导入函数失败: {e}")
        return False
    
    # 测试SSE连接和MCP初始化
    async with httpx.AsyncClient() as client:
        print("2. 建立SSE连接...")
        
        sse_headers = {
            "Accept": "text/event-stream",
            "Cache-Control": "no-cache",
            "Connection": "keep-alive"
        }
        
        try:
            async with client.stream('GET', gaode_config["url"], headers=sse_headers, timeout=30) as sse_response:
                if sse_response.status_code != 200:
                    print(f"❌ SSE连接失败，状态码: {sse_response.status_code}")
                    return False
                
                print(f"✅ SSE连接成功，状态码: {sse_response.status_code}")
                
                # 获取消息端点
                message_endpoint = None
                session_id = None
                event_count = 0
                
                buffer = ""
                async for chunk in sse_response.aiter_text():
                    buffer += chunk
                    lines = buffer.split('\n')
                    buffer = lines[-1]
                    
                    for line in lines[:-1]:
                        line = line.strip()
                        if line:
                            print(f"   SSE事件: {line}")
                            event_count += 1
                            
                            if line.startswith("data:") and "/mcp/message" in line:
                                endpoint_path = line[5:].strip()
                                if endpoint_path.startswith("/mcp/message"):
                                    message_endpoint = f"https://mcp.amap.com{endpoint_path}"
                                    
                                    if "sessionId=" in endpoint_path:
                                        session_id = endpoint_path.split("sessionId=")[1].split("&")[0]
                                    
                                    print(f"✅ 获取到消息端点: {message_endpoint}")
                                    print(f"   Session ID: {session_id}")
                                    break
                    
                    if message_endpoint:
                        break
                    
                    if event_count >= 3:
                        print("❌ 超过最大事件数限制")
                        return False
                
                if not message_endpoint:
                    print("❌ 未能获取到消息端点")
                    return False
                
                print("\n3. 测试修复后的MCP初始化...")
                
                # 准备MCP初始化请求
                init_request = {
                    "jsonrpc": "2.0",
                    "id": str(uuid.uuid4()),
                    "method": "initialize",
                    "params": {
                        "protocolVersion": "2024-11-05",
                        "capabilities": {
                            "tools": {},
                            "resources": {},
                            "prompts": {}
                        },
                        "clientInfo": {
                            "name": "AI Assistant Test",
                            "version": "1.0.0"
                        }
                    }
                }
                
                # 准备参数
                headers = gaode_config["headers"]
                tools = []
                resources = []
                prompts = []
                server_info = {}
                server_capabilities = {}
                
                # 调用修复后的函数
                print("   调用 _send_mcp_requests_in_sse_context...")
                
                try:
                    await _send_mcp_requests_in_sse_context(
                        client, sse_response, message_endpoint, init_request, headers,
                        tools, resources, prompts, server_info, server_capabilities
                    )
                    
                    # 检查结果
                    print(f"\n4. 测试结果:")
                    print(f"   服务器信息: {server_info}")
                    print(f"   服务器能力: {server_capabilities}")
                    print(f"   工具数量: {len(tools)}")
                    print(f"   资源数量: {len(resources)}")
                    print(f"   提示词数量: {len(prompts)}")
                    
                    if len(tools) > 0:
                        print(f"\n🎉 成功！获取到 {len(tools)} 个工具:")
                        for i, tool in enumerate(tools[:5], 1):  # 显示前5个
                            name = tool.get('name', '未知工具')
                            desc = tool.get('description', '无描述')
                            print(f"   {i}. {name}: {desc}")
                        
                        # 测试工具调用（如果有工具）
                        print(f"\n5. 测试工具调用...")
                        if tools:
                            first_tool = tools[0]
                            tool_name = first_tool.get('name')
                            
                            print(f"   测试调用工具: {tool_name}")
                            
                            # 构建工具调用请求
                            tool_request = {
                                "jsonrpc": "2.0",
                                "id": str(uuid.uuid4()),
                                "method": "tools/call",
                                "params": {
                                    "name": tool_name,
                                    "arguments": {}  # 使用空参数进行测试
                                }
                            }
                            
                            try:
                                call_headers = {"Content-Type": "application/json"}
                                call_headers.update(headers)
                                
                                call_response = await client.post(
                                    message_endpoint,
                                    json=tool_request,
                                    headers=call_headers,
                                    timeout=15
                                )
                                
                                print(f"   工具调用响应状态码: {call_response.status_code}")
                                if call_response.text:
                                    print(f"   响应内容: {call_response.text[:200]}...")
                                else:
                                    print("   响应为空（可能通过SSE返回）")
                                
                                print("✅ 工具调用测试完成")
                                
                            except Exception as call_error:
                                print(f"   ⚠️  工具调用测试失败: {call_error}")
                        
                        return True
                    else:
                        print("   ⚠️  未获取到工具，但函数执行完成")
                        return False
                        
                except Exception as func_error:
                    print(f"   ❌ 函数调用失败: {func_error}")
                    import traceback
                    traceback.print_exc()
                    return False
                
        except Exception as e:
            print(f"❌ 测试异常: {e}")
            import traceback
            traceback.print_exc()
            return False

if __name__ == "__main__":
    result = asyncio.run(test_ai_mcp_system())
    if result:
        print("\n🎉 AI助手MCP系统测试成功！")
        print("✅ 修复后的代码可以正常获取工具列表")
        print("✅ 支持SSE协议的并发模式")
        print("✅ 可以进行工具调用测试")
    else:
        print("\n❌ AI助手MCP系统测试失败！")
        print("需要进一步调试和修复") 