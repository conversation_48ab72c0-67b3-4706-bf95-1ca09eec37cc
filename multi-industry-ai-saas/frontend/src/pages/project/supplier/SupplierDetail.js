import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Descriptions, 
  Button, 
  Space, 
  Tabs, 
  Table, 
  Tag, 
  Typography, 
  Spin, 
  message, 
  Empty,
  Row,
  Col,
  Statistic
} from 'antd';
import { 
  ArrowLeftOutlined, 
  EditOutlined, 
  PhoneOutlined, 
  MailOutlined, 
  HomeOutlined,
  ShoppingOutlined,
  FileTextOutlined,
  StarOutlined
} from '@ant-design/icons';
import { useParams, useNavigate } from 'react-router-dom';
import apiService from '../../../services/api';
import dayjs from "dayjs";

const { Title, Text } = Typography;
const { TabPane } = Tabs;

const SupplierDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  
  const [supplier, setSupplier] = useState(null);
  const [products, setProducts] = useState([]);
  const [purchaseOrders, setPurchaseOrders] = useState([]);
  const [evaluations, setEvaluations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [productsLoading, setProductsLoading] = useState(false);
  const [ordersLoading, setOrdersLoading] = useState(false);
  const [evaluationsLoading, setEvaluationsLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('1');

  // 获取供应商详情
  useEffect(() => {
    const fetchSupplierDetail = async () => {
      setLoading(true);
      try {
        const response = await apiService.project.supplier.getDetail(id);
        if (response && response.data) {
          setSupplier(response.data);
        } else {
          message.error('获取供应商详情失败');
        }
      } catch (error) {
        console.error('获取供应商详情失败:', error);
        message.error('获取供应商详情失败: ' + (error.response?.data?.detail || error.message));
      } finally {
        setLoading(false);
      }
    };

    if (id) {
      fetchSupplierDetail();
    }
  }, [id]);

  // 获取供应商产品
  const fetchSupplierProducts = async () => {
    if (!id) return;
    
    setProductsLoading(true);
    try {
      const response = await apiService.project.supplier.getProducts(id);
      if (response && response.data) {
        setProducts(response.data);
      } else {
        setProducts([]);
      }
    } catch (error) {
      console.error('获取供应商产品失败:', error);
      message.error('获取供应商产品失败: ' + (error.response?.data?.detail || error.message));
      setProducts([]);
    } finally {
      setProductsLoading(false);
    }
  };

  // 获取供应商采购订单
  const fetchSupplierOrders = async () => {
    if (!id) return;
    
    setOrdersLoading(true);
    try {
      const response = await apiService.project.supplier.getPurchaseOrders(id);
      if (response && response.data) {
        setPurchaseOrders(response.data);
      } else {
        setPurchaseOrders([]);
      }
    } catch (error) {
      console.error('获取供应商采购订单失败:', error);
      message.error('获取供应商采购订单失败: ' + (error.response?.data?.detail || error.message));
      setPurchaseOrders([]);
    } finally {
      setOrdersLoading(false);
    }
  };

  // 获取供应商评估
  const fetchSupplierEvaluations = async () => {
    if (!id) return;
    
    setEvaluationsLoading(true);
    try {
      const response = await apiService.project.supplier.getEvaluations(id);
      if (response && response.data) {
        setEvaluations(response.data);
      } else {
        setEvaluations([]);
      }
    } catch (error) {
      console.error('获取供应商评估失败:', error);
      message.error('获取供应商评估失败: ' + (error.response?.data?.detail || error.message));
      setEvaluations([]);
    } finally {
      setEvaluationsLoading(false);
    }
  };

  // 处理标签页切换
  const handleTabChange = (key) => {
    setActiveTab(key);
    
    if (key === '2' && products.length === 0) {
      fetchSupplierProducts();
    } else if (key === '3' && purchaseOrders.length === 0) {
      fetchSupplierOrders();
    } else if (key === '4' && evaluations.length === 0) {
      fetchSupplierEvaluations();
    }
  };

  // 返回供应商列表
  const handleBack = () => {
    navigate('/project/purchase/suppliers');
  };

  // 编辑供应商
  const handleEdit = () => {
    navigate(`/project/purchase/suppliers/edit/${id}`);
  };

  // 产品列表列定义
  const productColumns = [
    {
      title: '产品名称',
      dataIndex: 'product_name',
      key: 'product_name',
    },
    {
      title: '供应商产品编码',
      dataIndex: 'supplier_product_code',
      key: 'supplier_product_code',
    },
    {
      title: '价格',
      dataIndex: 'price',
      key: 'price',
      render: (text) => `¥${parseFloat(text || 0).toFixed(2)}`,
    },
    {
      title: '最小订购量',
      dataIndex: 'min_order_quantity',
      key: 'min_order_quantity',
    },
    {
      title: '交货周期(天)',
      dataIndex: 'lead_time_days',
      key: 'lead_time_days',
    },
    {
      title: '首选供应商',
      dataIndex: 'is_preferred',
      key: 'is_preferred',
      render: (text) => text ? <Tag color="green">是</Tag> : <Tag color="default">否</Tag>,
    },
  ];

  // 采购订单列表列定义
  const orderColumns = [
    {
      title: '订单编号',
      dataIndex: 'order_number',
      key: 'order_number',
    },
    {
      title: '订单日期',
      dataIndex: 'order_date',
      key: 'order_date',
      render: (text) => text ? dayjs(text).format('YYYY-MM-DD') : '-',
    },
    {
      title: '预计交货日期',
      dataIndex: 'expected_delivery_date',
      key: 'expected_delivery_date',
      render: (text) => text ? dayjs(text).format('YYYY-MM-DD') : '-',
    },
    {
      title: '实际交货日期',
      dataIndex: 'actual_delivery_date',
      key: 'actual_delivery_date',
      render: (text) => text ? dayjs(text).format('YYYY-MM-DD') : '-',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => {
        const statusMap = {
          'draft': { color: 'default', text: '草稿' },
          'confirmed': { color: 'processing', text: '已确认' },
          'received': { color: 'success', text: '已收货' },
          'cancelled': { color: 'error', text: '已取消' },
        };
        const { color, text } = statusMap[status] || { color: 'default', text: status };
        return <Tag color={color}>{text}</Tag>;
      },
    },
    {
      title: '总金额',
      dataIndex: 'total_amount',
      key: 'total_amount',
      render: (text) => `¥${parseFloat(text || 0).toFixed(2)}`,
    },
  ];

  // 评估列表列定义
  const evaluationColumns = [
    {
      title: '评估日期',
      dataIndex: 'evaluation_date',
      key: 'evaluation_date',
      render: (text) => text ? dayjs(text).format('YYYY-MM-DD') : '-',
    },
    {
      title: '质量评分',
      dataIndex: 'quality_score',
      key: 'quality_score',
      render: (score) => score ? `${score}/5` : '-',
    },
    {
      title: '交付评分',
      dataIndex: 'delivery_score',
      key: 'delivery_score',
      render: (score) => score ? `${score}/5` : '-',
    },
    {
      title: '价格评分',
      dataIndex: 'price_score',
      key: 'price_score',
      render: (score) => score ? `${score}/5` : '-',
    },
    {
      title: '服务评分',
      dataIndex: 'service_score',
      key: 'service_score',
      render: (score) => score ? `${score}/5` : '-',
    },
    {
      title: '总体评分',
      dataIndex: 'overall_score',
      key: 'overall_score',
      render: (score) => score ? `${score.toFixed(1)}/5` : '-',
    },
  ];

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
      </div>
    );
  }

  if (!supplier) {
    return (
      <Empty 
        description="供应商不存在或已被删除" 
        style={{ margin: '50px 0' }}
      >
        <Button type="primary" onClick={handleBack}>返回列表</Button>
      </Empty>
    );
  }

  return (
    <div className="supplier-detail-page">
      <Card>
        <div style={{ marginBottom: 16 }}>
          <Space>
            <Button icon={<ArrowLeftOutlined />} onClick={handleBack}>返回</Button>
            <Button type="primary" icon={<EditOutlined />} onClick={handleEdit}>编辑</Button>
          </Space>
        </div>

        <Title level={4}>{supplier.name}</Title>

        <Row gutter={16} style={{ marginBottom: 24 }}>
          <Col span={8}>
            <Card>
              <Statistic 
                title="产品数量" 
                value={products.length || 0} 
                prefix={<ShoppingOutlined />} 
              />
            </Card>
          </Col>
          <Col span={8}>
            <Card>
              <Statistic 
                title="采购订单数" 
                value={purchaseOrders.length || 0} 
                prefix={<FileTextOutlined />} 
              />
            </Card>
          </Col>
          <Col span={8}>
            <Card>
              <Statistic 
                title="平均评分" 
                value={evaluations.length > 0 
                  ? (evaluations.reduce((sum, item) => sum + (item.overall_score || 0), 0) / evaluations.length).toFixed(1)
                  : '-'
                } 
                suffix="/5"
                prefix={<StarOutlined />} 
              />
            </Card>
          </Col>
        </Row>

        <Tabs activeKey={activeTab} onChange={handleTabChange}>
          <TabPane tab="基本信息" key="1">
            <Descriptions bordered column={2}>
              <Descriptions.Item label="供应商名称" span={2}>{supplier.name}</Descriptions.Item>
              <Descriptions.Item label="联系人">
                {supplier.contact_person || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="联系电话">
                {supplier.contact_phone ? (
                  <Space>
                    <PhoneOutlined />
                    {supplier.contact_phone}
                  </Space>
                ) : '-'}
              </Descriptions.Item>
              <Descriptions.Item label="电子邮箱">
                {supplier.email ? (
                  <Space>
                    <MailOutlined />
                    {supplier.email}
                  </Space>
                ) : '-'}
              </Descriptions.Item>
              <Descriptions.Item label="地址">
                {supplier.address ? (
                  <Space>
                    <HomeOutlined />
                    {supplier.address}
                  </Space>
                ) : '-'}
              </Descriptions.Item>
              <Descriptions.Item label="状态" span={2}>
                {supplier.is_active ? (
                  <Tag color="green">启用</Tag>
                ) : (
                  <Tag color="red">禁用</Tag>
                )}
              </Descriptions.Item>
              <Descriptions.Item label="描述" span={2}>
                {supplier.description || '-'}
              </Descriptions.Item>
              <Descriptions.Item label="创建时间">
                {supplier.created_at ? dayjs(supplier.created_at).format('YYYY-MM-DD HH:mm:ss') : '-'}
              </Descriptions.Item>
              <Descriptions.Item label="更新时间">
                {supplier.updated_at ? dayjs(supplier.updated_at).format('YYYY-MM-DD HH:mm:ss') : '-'}
              </Descriptions.Item>
            </Descriptions>
          </TabPane>

          <TabPane tab="供应产品" key="2">
            <Table
              columns={productColumns}
              dataSource={products}
              rowKey="id"
              loading={productsLoading}
              pagination={{ pageSize: 10 }}
              locale={{ emptyText: '暂无供应产品数据' }}
            />
          </TabPane>

          <TabPane tab="采购订单" key="3">
            <Table
              columns={orderColumns}
              dataSource={purchaseOrders}
              rowKey="id"
              loading={ordersLoading}
              pagination={{ pageSize: 10 }}
              locale={{ emptyText: '暂无采购订单数据' }}
            />
          </TabPane>

          <TabPane tab="供应商评估" key="4">
            <Table
              columns={evaluationColumns}
              dataSource={evaluations}
              rowKey="id"
              loading={evaluationsLoading}
              pagination={{ pageSize: 10 }}
              locale={{ emptyText: '暂无供应商评估数据' }}
            />
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default SupplierDetail;
