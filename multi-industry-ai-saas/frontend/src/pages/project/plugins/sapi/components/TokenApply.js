import React, { useState, useEffect } from 'react';
import { Form, Input, Button, Card, Alert, Typography, Select, DatePicker, message, Collapse, Switch, Table, Tag, InputNumber } from 'antd';
import { KeyOutlined, SendOutlined, InfoCircleOutlined, LockOutlined, CloudServerOutlined } from '@ant-design/icons';
import { applyForToken } from '../services/tokenService';
import { getProjectId } from '../../../../../services/api/httpClient';
import { getServerList } from '../services/mcpService';

const { Title, Paragraph, Text } = Typography;
const { TextArea } = Input;
const { Option } = Select;
const { Panel } = Collapse;

/**
 * 申请API令牌组件
 */
const TokenApply = () => {
  const projectId = getProjectId();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState(null);
  const [mcpServers, setMcpServers] = useState([]);
  const [loadingServers, setLoadingServers] = useState(false);
  const [advancedMode, setAdvancedMode] = useState(false);

  // 获取MCP服务器列表
  useEffect(() => {
    const fetchMCPServers = async () => {
      setLoadingServers(true);
      try {
        const response = await getServerList();
        if (response.success) {
          setMcpServers(response.data || []);
        } else {
          message.error('获取MCP服务器列表失败');
        }
      } catch (error) {
        console.error('获取MCP服务器列表失败:', error);
        message.error('获取MCP服务器列表失败');
      } finally {
        setLoadingServers(false);
      }
    };

    fetchMCPServers();
  }, [projectId]);

  // 处理表单提交
  const handleSubmit = async (values) => {
    setLoading(true);
    setError(null);
    setSuccess(false);

    try {
      // 处理服务器权限映射
      let serverPermissions = {};
      if (values.serverPermissions) {
        Object.keys(values.serverPermissions).forEach(serverId => {
          if (values.serverPermissions[serverId]) {
            serverPermissions[serverId] = [values.permissions];
          }
        });
      }

      // 构建请求数据
      const requestData = {
        ...values,
        project_id: projectId,
        token_type: 'mcp',
        server_permissions: Object.keys(serverPermissions).length > 0 ? serverPermissions : undefined,
        default_permissions: [values.permissions],
        expires_hours: values.expires_hours || 24 * 30 // 默认30天
      };

      const response = await applyForToken(requestData);

      if (response.success) {
        setSuccess(true);
        form.resetFields();
        message.success('令牌申请已提交，请等待管理员审批');
      } else {
        setError(response.message || '申请令牌失败');
        message.error('申请令牌失败');
      }
    } catch (error) {
      console.error('申请令牌失败:', error);
      setError('申请令牌失败: ' + (error.message || '未知错误'));
      message.error('申请令牌失败');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card>
      <Title level={4}>申请JWT令牌</Title>
      <Paragraph>
        JWT令牌用于访问智能助手集成平台的API和MCP服务器，您可以使用令牌将系统功能集成到您的应用程序中。
        请填写以下表单申请令牌，管理员审批后将通知您。
      </Paragraph>

      {success && (
        <Alert
          message="申请已提交"
          description="您的令牌申请已成功提交，请等待管理员审批。审批通过后，您将收到通知。"
          type="success"
          showIcon
          style={{ marginBottom: 16 }}
        />
      )}

      {error && (
        <Alert
          message="申请失败"
          description={error}
          type="error"
          showIcon
          style={{ marginBottom: 16 }}
        />
      )}

      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        initialValues={{
          permissions: 'read',
          token_type: 'mcp',
          expires_hours: 24 * 30, // 默认30天
        }}
      >
        <Form.Item
          name="name"
          label="令牌名称"
          rules={[{ required: true, message: '请输入令牌名称' }]}
        >
          <Input
            prefix={<KeyOutlined />}
            placeholder="例如: 我的MCP客户端令牌"
          />
        </Form.Item>

        <Form.Item
          name="description"
          label="令牌描述"
        >
          <TextArea
            placeholder="请简要描述令牌的用途"
            rows={2}
          />
        </Form.Item>

        <Form.Item
          name="purpose"
          label="申请目的"
          rules={[{ required: true, message: '请输入申请目的' }]}
        >
          <TextArea
            placeholder="请详细说明您申请令牌的目的和用途"
            rows={4}
          />
        </Form.Item>

        <Form.Item
          name="permissions"
          label="申请权限"
          rules={[{ required: true, message: '请选择申请权限' }]}
          tooltip="读取权限允许获取数据，写入权限允许修改数据"
        >
          <Select>
            <Option value="read">只读权限</Option>
            <Option value="write">只写权限</Option>
            <Option value="read_write">读写权限</Option>
          </Select>
        </Form.Item>

        <Form.Item
          name="token_type"
          label="令牌类型"
          rules={[{ required: true, message: '请选择令牌类型' }]}
          tooltip="MCP令牌用于访问MCP服务器，Access令牌用于访问REST API"
        >
          <Select>
            <Option value="mcp">MCP令牌</Option>
            <Option value="access">Access令牌</Option>
          </Select>
        </Form.Item>

        <Form.Item
          name="expires_hours"
          label="过期时间（小时）"
          tooltip="令牌的有效期，单位为小时，默认为720小时（30天）"
          rules={[{ required: true, message: '请输入过期时间' }]}
        >
          <InputNumber
            style={{ width: '100%' }}
            min={1}
            max={8760} // 最长一年
            placeholder="令牌有效期（小时）"
          />
        </Form.Item>

        <Collapse ghost>
          <Panel header="高级设置" key="advanced">
            <Form.Item
              label="服务器权限"
              tooltip="选择此令牌可以访问的MCP服务器"
            >
              <Table
                dataSource={mcpServers}
                rowKey="id"
                size="small"
                loading={loadingServers}
                pagination={false}
                columns={[
                  {
                    title: '服务器名称',
                    dataIndex: 'name',
                    key: 'name',
                  },
                  {
                    title: '状态',
                    dataIndex: 'status',
                    key: 'status',
                    render: (status) => (
                      <Tag color={status === 'running' ? 'green' : 'red'}>
                        {status === 'running' ? '运行中' : '已停止'}
                      </Tag>
                    ),
                  },
                  {
                    title: '授权',
                    key: 'auth',
                    render: (_, record) => (
                      <Form.Item
                        name={['serverPermissions', record.id]}
                        valuePropName="checked"
                        noStyle
                      >
                        <Switch
                          checkedChildren="已授权"
                          unCheckedChildren="未授权"
                          defaultChecked={false}
                        />
                      </Form.Item>
                    ),
                  },
                ]}
              />
            </Form.Item>
          </Panel>
        </Collapse>

        <Form.Item>
          <Button
            type="primary"
            htmlType="submit"
            loading={loading}
            icon={<SendOutlined />}
          >
            提交申请
          </Button>
        </Form.Item>
      </Form>

      <Alert
        message="注意事项"
        description={
          <ul>
            <li>令牌申请需要管理员审批，请耐心等待</li>
            <li>JWT令牌是您访问API和MCP服务器的凭证，请妥善保管，不要泄露给他人</li>
            <li>令牌使用Bearer认证方式，在HTTP请求头中添加：Authorization: Bearer {'{your_token}'}</li>
            <li>如果您的令牌泄露，请立即联系管理员撤销</li>
            <li>令牌使用过程中如有问题，请联系系统管理员</li>
          </ul>
        }
        type="info"
        icon={<InfoCircleOutlined />}
        showIcon
      />
    </Card>
  );
};

export default TokenApply;
