import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  Layout, Input, Button, Avatar, Typography, Space,
  Upload, message, Spin, Tooltip, Drawer, Switch, Slider,
  FloatButton, Modal, Tag, Alert
} from 'antd';
import {
  SendOutlined, PaperClipOutlined,
  SettingOutlined, RobotOutlined, UserOutlined, AudioOutlined,
  FullscreenOutlined, FullscreenExitOutlined,
  FileTextOutlined, AudioMutedOutlined, SoundOutlined,
  ClearOutlined, ShareAltOutlined, EyeOutlined, PlayCircleOutlined,
  PauseCircleOutlined, DownloadOutlined,
  VideoCameraOutlined, FileOutlined, PictureOutlined, GlobalOutlined,
  StopOutlined, CodeOutlined
} from '@ant-design/icons';
import { useParams } from 'react-router-dom';
import './AIAssistantChat.css';

const { Header, Content, Footer } = Layout;
const { TextArea } = Input;
const { Text, Title } = Typography;

/**
 * AI助手独立分享页面
 * 支持多终端适配和多模态交互
 */
const AIAssistantChat = () => {
  const { assistantId } = useParams();
  const [assistant, setAssistant] = useState(null);
  const [messages, setMessages] = useState([]);
  const [inputValue, setInputValue] = useState('');
  const [loading, setLoading] = useState(false);
  const [sending, setSending] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [settingsVisible, setSettingsVisible] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [fontSize, setFontSize] = useState(14);
  const [theme, setTheme] = useState('light');
  const [autoScroll, setAutoScroll] = useState(true);
  const [showTyping, setShowTyping] = useState(false);
  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewImage, setPreviewImage] = useState('');
  const [recordingTime, setRecordingTime] = useState(0);
  const [audioPlaying, setAudioPlaying] = useState(null);
  
  const messagesEndRef = useRef(null);
  const inputRef = useRef(null);
  const mediaRecorderRef = useRef(null);
  const audioChunksRef = useRef([]);
  const chatContainerRef = useRef(null);
  const recordingTimerRef = useRef(null);
  const audioRef = useRef(null);

  // 检测设备类型
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkDevice = () => {
      const width = window.innerWidth;
      const userAgent = navigator.userAgent;
      
      if (width <= 768 || /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent)) {
        setIsMobile(true);
      } else {
        setIsMobile(false);
      }
    };

    checkDevice();
    window.addEventListener('resize', checkDevice);
    return () => window.removeEventListener('resize', checkDevice);
  }, []);

  const fetchAssistant = useCallback(async () => {
    try {
      setLoading(true);
      // 调用真实的API获取助手信息
      const response = await fetch(`/api/v1/shared/assistant/${assistantId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      
      if (!response.ok) {
        throw new Error('助手不存在或已被删除');
      }
      
      const data = await response.json();
      setAssistant(data.data);
      
      // 添加欢迎消息
      if (data.data.welcome_message) {
        setMessages([{
          id: Date.now(),
          type: 'assistant',
          content: data.data.welcome_message,
          timestamp: new Date()
        }]);
      }
    } catch (error) {
      console.error('获取助手信息失败:', error);
      message.error('助手不存在或已被删除');
    } finally {
      setLoading(false);
    }
  }, [assistantId]);

  // 获取助手信息
  useEffect(() => {
    fetchAssistant();
  }, [fetchAssistant]);

  // 自动滚动到底部
  useEffect(() => {
    if (autoScroll && messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages, autoScroll]);

  // 发送消息
  const sendMessage = async (content, type = 'text', file = null) => {
    if (!content.trim() && !file) return;

    const userMessage = {
      id: Date.now(),
      type: 'user',
      content: content || '',
      file: file,
      contentType: type,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setSending(true);
    setShowTyping(true);

    try {
      // 调用真实的API发送消息
      const formData = new FormData();
      formData.append('content', content);
      formData.append('content_type', type);
      if (file) {
        formData.append('file', file);
      }

      const response = await fetch(`/api/v1/shared/assistant/${assistantId}/chat`, {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error('发送消息失败');
      }

      const data = await response.json();
      
      const assistantMessage = {
        id: Date.now() + 1,
        type: 'assistant',
        content: data.data.response || data.data.content,
        timestamp: new Date()
      };
      
      setMessages(prev => [...prev, assistantMessage]);
      setShowTyping(false);
      
      // 如果支持语音输出，自动播放
      if (assistant?.capabilities?.supports_text_to_speech && !isMuted) {
        speakText(assistantMessage.content);
      }
    } catch (error) {
      console.error('发送消息失败:', error);
      message.error('发送失败，请重试');
      setShowTyping(false);
      
      // 移除用户消息，因为发送失败
      setMessages(prev => prev.slice(0, -1));
    } finally {
      setSending(false);
    }
  };

  // 语音转文字
  const startRecording = async () => {
    if (!assistant?.capabilities?.supports_speech_to_text) {
      message.warning('当前助手不支持语音输入');
      return;
    }

    try {
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
      const mediaRecorder = new MediaRecorder(stream);
      mediaRecorderRef.current = mediaRecorder;
      audioChunksRef.current = [];

      mediaRecorder.ondataavailable = (event) => {
        audioChunksRef.current.push(event.data);
      };

      mediaRecorder.onstop = async () => {
        const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/wav' });
        const audioFile = new File([audioBlob], 'voice.wav', { type: 'audio/wav' });
        
        try {
          // 调用真实的语音转文字API
          const formData = new FormData();
          formData.append('audio', audioFile);
          
          const response = await fetch(`/api/v1/shared/assistant/${assistantId}/speech-to-text`, {
            method: 'POST',
            body: formData,
          });
          
          if (response.ok) {
            const data = await response.json();
            const transcribedText = data.data.text || '语音识别结果';
            await sendMessage(transcribedText, 'audio', audioFile);
          } else {
            // 如果语音转文字API不可用，直接发送音频文件
            await sendMessage('语音消息', 'audio', audioFile);
          }
        } catch (error) {
          console.error('语音转文字失败:', error);
          // 如果语音转文字失败，直接发送音频文件
          await sendMessage('语音消息', 'audio', audioFile);
        }
        
        stream.getTracks().forEach(track => track.stop());
        clearInterval(recordingTimerRef.current);
        setRecordingTime(0);
      };

      mediaRecorder.start();
      setIsRecording(true);
      
      // 开始计时
      recordingTimerRef.current = setInterval(() => {
        setRecordingTime(prev => prev + 1);
      }, 1000);
      
    } catch (error) {
      console.error('录音失败:', error);
      message.error('无法访问麦克风');
    }
  };

  const stopRecording = () => {
    if (mediaRecorderRef.current && isRecording) {
      mediaRecorderRef.current.stop();
      setIsRecording(false);
    }
  };

  // 文字转语音
  const speakText = (text) => {
    if (!assistant?.capabilities?.supports_text_to_speech || isMuted) return;
    
    const utterance = new SpeechSynthesisUtterance(text);
    utterance.lang = 'zh-CN';
    utterance.rate = 0.9;
    speechSynthesis.speak(utterance);
  };

  // 播放音频
  const playAudio = (audioUrl, messageId) => {
    if (audioPlaying === messageId) {
      audioRef.current?.pause();
      setAudioPlaying(null);
    } else {
      if (audioRef.current) {
        audioRef.current.src = audioUrl;
        audioRef.current.play();
        setAudioPlaying(messageId);
        
        audioRef.current.onended = () => {
          setAudioPlaying(null);
        };
      }
    }
  };

  // 处理文件上传
  const handleFileUpload = (file) => {
    if (!assistant?.capabilities?.supports_file_upload) {
      message.warning('当前助手不支持文件上传');
      return false;
    }

    const maxSize = (assistant.capabilities.max_file_size || 10) * 1024 * 1024; // MB to bytes
    if (file.size > maxSize) {
      message.error(`文件大小不能超过 ${assistant.capabilities.max_file_size || 10}MB`);
      return false;
    }

    const fileType = file.type.split('/')[0];
    const fileExt = file.name.split('.').pop().toLowerCase();

    if (fileType === 'image') {
      if (!assistant.capabilities.supports_vision) {
        message.warning('当前助手不支持图像处理');
        return false;
      }
      const supportedFormats = assistant.capabilities.supported_image_formats || ['jpg', 'jpeg', 'png', 'gif', 'webp'];
      if (!supportedFormats.includes(fileExt)) {
        message.error(`不支持的图像格式，支持格式：${supportedFormats.join(', ')}`);
        return false;
      }
      sendMessage(`上传了图片：${file.name}`, 'image', file);
    } else if (fileType === 'audio') {
      if (!assistant.capabilities.supports_audio_input) {
        message.warning('当前助手不支持音频处理');
        return false;
      }
      const supportedFormats = assistant.capabilities.supported_audio_formats || ['mp3', 'wav', 'ogg', 'm4a'];
      if (!supportedFormats.includes(fileExt)) {
        message.error(`不支持的音频格式，支持格式：${supportedFormats.join(', ')}`);
        return false;
      }
      sendMessage(`上传了音频：${file.name}`, 'audio', file);
    } else if (fileType === 'video') {
      if (!assistant.capabilities.supports_video_understanding) {
        message.warning('当前助手不支持视频处理');
        return false;
      }
      sendMessage(`上传了视频：${file.name}`, 'video', file);
    } else {
      // 其他文件类型
      sendMessage(`上传了文件：${file.name}`, 'file', file);
    }

    return false; // 阻止默认上传
  };

  // 全屏切换
  const toggleFullscreen = () => {
    if (!isFullscreen) {
      if (chatContainerRef.current?.requestFullscreen) {
        chatContainerRef.current.requestFullscreen();
      }
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen();
      }
    }
    setIsFullscreen(!isFullscreen);
  };

  // 清空对话
  const clearMessages = () => {
    setMessages(assistant?.welcome_message ? [{
      id: Date.now(),
      type: 'assistant',
      content: assistant.welcome_message,
      timestamp: new Date()
    }] : []);
  };

  // 渲染能力标签
  const renderCapabilityTags = (capabilities) => {
    if (!capabilities) return null;
    
    const tags = [];
    
    if (capabilities.supports_vision) {
      tags.push(<Tag key="vision" color="blue" icon={<EyeOutlined />} size="small">视觉</Tag>);
    }
    if (capabilities.supports_audio_input || capabilities.supports_audio_output) {
      tags.push(<Tag key="audio" color="green" icon={<AudioOutlined />} size="small">音频</Tag>);
    }
    if (capabilities.supports_speech_to_text || capabilities.supports_text_to_speech) {
      tags.push(<Tag key="speech" color="orange" icon={<SoundOutlined />} size="small">语音</Tag>);
    }
    if (capabilities.supports_file_upload) {
      tags.push(<Tag key="file" color="purple" icon={<FileOutlined />} size="small">文件</Tag>);
    }
    if (capabilities.supports_web_search) {
      tags.push(<Tag key="web" color="cyan" icon={<GlobalOutlined />} size="small">搜索</Tag>);
    }
    if (capabilities.supports_code_execution) {
      tags.push(<Tag key="code" color="red" icon={<CodeOutlined />} size="small">代码</Tag>);
    }
    
    return tags;
  };

  // 渲染消息
  const renderMessage = (msg) => {
    const isUser = msg.type === 'user';
    
    return (
      <div
        key={msg.id}
        className={`message ${isUser ? 'user-message' : 'assistant-message'}`}
        style={{ fontSize: `${fontSize}px` }}
      >
        <div className="message-avatar">
          <Avatar
            size={isMobile ? 32 : 40}
            icon={isUser ? <UserOutlined /> : <RobotOutlined />}
            src={isUser ? null : assistant?.avatar}
            style={{ backgroundColor: isUser ? '#1890ff' : '#52c41a' }}
          />
        </div>
        <div className="message-content">
          <div className="message-bubble">
            {msg.file && (
              <div className="message-file">
                {msg.file.type.startsWith('image/') ? (
                  <div style={{ position: 'relative' }}>
                    <img
                      src={URL.createObjectURL(msg.file)}
                      alt={msg.file.name}
                      style={{ 
                        maxWidth: '200px', 
                        maxHeight: '200px', 
                        borderRadius: '8px',
                        cursor: 'pointer'
                      }}
                      onClick={() => {
                        setPreviewImage(URL.createObjectURL(msg.file));
                        setPreviewVisible(true);
                      }}
                    />
                    <div style={{ 
                      position: 'absolute', 
                      top: 4, 
                      right: 4, 
                      background: 'rgba(0,0,0,0.5)', 
                      borderRadius: '4px',
                      padding: '2px 6px'
                    }}>
                      <EyeOutlined style={{ color: 'white', fontSize: '12px' }} />
                    </div>
                  </div>
                ) : msg.file.type.startsWith('audio/') ? (
                  <div className="audio-message">
                    <div style={{ 
                      display: 'flex', 
                      alignItems: 'center', 
                      gap: '8px',
                      padding: '8px',
                      background: 'rgba(0,0,0,0.05)',
                      borderRadius: '8px'
                    }}>
                      <Button
                        type="text"
                        size="small"
                        icon={audioPlaying === msg.id ? <PauseCircleOutlined /> : <PlayCircleOutlined />}
                        onClick={() => playAudio(URL.createObjectURL(msg.file), msg.id)}
                      />
                      <span style={{ fontSize: '12px' }}>{msg.file.name}</span>
                      <span style={{ fontSize: '10px', color: '#666' }}>
                        {(msg.file.size / 1024).toFixed(1)}KB
                      </span>
                    </div>
                  </div>
                ) : msg.file.type.startsWith('video/') ? (
                  <div className="video-message">
                    <video
                      controls
                      style={{ 
                        maxWidth: '300px', 
                        maxHeight: '200px', 
                        borderRadius: '8px'
                      }}
                    >
                      <source src={URL.createObjectURL(msg.file)} type={msg.file.type} />
                      您的浏览器不支持视频播放
                    </video>
                  </div>
                ) : (
                  <div className="file-info">
                    <FileTextOutlined style={{ fontSize: '24px', marginRight: '8px' }} />
                    <div>
                      <div style={{ fontWeight: 'bold' }}>{msg.file.name}</div>
                      <div style={{ fontSize: '12px', color: '#666' }}>
                        {(msg.file.size / 1024).toFixed(1)}KB
                      </div>
                    </div>
                    <Button
                      type="text"
                      size="small"
                      icon={<DownloadOutlined />}
                      onClick={() => {
                        const url = URL.createObjectURL(msg.file);
                        const a = document.createElement('a');
                        a.href = url;
                        a.download = msg.file.name;
                        a.click();
                        URL.revokeObjectURL(url);
                      }}
                    />
                  </div>
                )}
              </div>
            )}
            {msg.content && <div className="message-text">{msg.content}</div>}
          </div>
          <div className="message-time">
            {new Date(msg.timestamp).toLocaleTimeString()}
          </div>
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="loading-container">
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>加载中...</div>
      </div>
    );
  }

  if (!assistant) {
    return (
      <div className="error-container">
        <Title level={3}>助手不存在</Title>
        <Text>请检查链接是否正确</Text>
      </div>
    );
  }

  return (
    <Layout className={`ai-chat-layout ${theme} ${isMobile ? 'mobile' : ''}`} ref={chatContainerRef}>
      <Header className="chat-header">
        <div className="header-content">
          <div className="assistant-info">
            <Avatar
              size={isMobile ? 32 : 40}
              icon={<RobotOutlined />}
              src={assistant.avatar}
              style={{ backgroundColor: assistant.settings?.theme_color || '#1890ff' }}
            />
            <div className="assistant-details">
              <Title level={isMobile ? 5 : 4} style={{ margin: 0, color: 'white' }}>
                {assistant.name}
              </Title>
              {!isMobile && (
                <div style={{ display: 'flex', gap: '4px', marginTop: '4px' }}>
                  {renderCapabilityTags(assistant.capabilities)}
                </div>
              )}
              {!isMobile && assistant.description && (
                <Text style={{ color: 'rgba(255,255,255,0.8)', fontSize: '12px' }}>
                  {assistant.description}
                </Text>
              )}
            </div>
          </div>
          
          <div className="header-actions">
            <Space>
              {assistant.capabilities?.supports_text_to_speech && (
                <Tooltip title={isMuted ? '开启语音' : '关闭语音'}>
                  <Button
                    type="text"
                    icon={isMuted ? <AudioMutedOutlined /> : <SoundOutlined />}
                    onClick={() => setIsMuted(!isMuted)}
                    style={{ color: 'white' }}
                  />
                </Tooltip>
              )}
              
              <Tooltip title="设置">
                <Button
                  type="text"
                  icon={<SettingOutlined />}
                  onClick={() => setSettingsVisible(true)}
                  style={{ color: 'white' }}
                />
              </Tooltip>
              
              {!isMobile && (
                <Tooltip title={isFullscreen ? '退出全屏' : '全屏'}>
                  <Button
                    type="text"
                    icon={isFullscreen ? <FullscreenExitOutlined /> : <FullscreenOutlined />}
                    onClick={toggleFullscreen}
                    style={{ color: 'white' }}
                  />
                </Tooltip>
              )}
            </Space>
          </div>
        </div>
      </Header>

      <Content className="chat-content">
        <div className="messages-container">
          {messages.length === 0 && (
            <div style={{ 
              textAlign: 'center', 
              padding: '40px 20px',
              color: '#666'
            }}>
              <RobotOutlined style={{ fontSize: '48px', marginBottom: '16px' }} />
              <div style={{ fontSize: '16px', marginBottom: '8px' }}>
                欢迎与 {assistant.name} 对话
              </div>
              <div style={{ fontSize: '14px' }}>
                {assistant.description || '我是您的AI助手，有什么可以帮助您的吗？'}
              </div>
              {assistant.capabilities && (
                <div style={{ marginTop: '16px' }}>
                  <div style={{ fontSize: '12px', marginBottom: '8px' }}>支持的功能：</div>
                  <div style={{ display: 'flex', justifyContent: 'center', flexWrap: 'wrap', gap: '4px' }}>
                    {renderCapabilityTags(assistant.capabilities)}
                  </div>
                </div>
              )}
            </div>
          )}
          
          {messages.map(renderMessage)}
          
          {showTyping && (
            <div className="typing-indicator">
              <div className="message assistant-message">
                <div className="message-avatar">
                  <Avatar
                    size={isMobile ? 32 : 40}
                    icon={<RobotOutlined />}
                    src={assistant.avatar}
                    style={{ backgroundColor: '#52c41a' }}
                  />
                </div>
                <div className="message-content">
                  <div className="message-bubble">
                    <div className="typing-dots">
                      <span></span>
                      <span></span>
                      <span></span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
          
          <div ref={messagesEndRef} />
        </div>
      </Content>

      <Footer className="chat-footer">
        <div className="input-container">
          {isRecording && (
            <div style={{ 
              background: '#fff2f0', 
              border: '1px solid #ffccc7',
              borderRadius: '8px',
              padding: '8px 12px',
              marginBottom: '8px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between'
            }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                <div style={{ 
                  width: '8px', 
                  height: '8px', 
                  borderRadius: '50%', 
                  background: '#ff4d4f',
                  animation: 'pulse 1s infinite'
                }} />
                <span style={{ fontSize: '14px' }}>正在录音... {recordingTime}s</span>
              </div>
              <Button 
                size="small" 
                danger 
                icon={<StopOutlined />}
                onClick={stopRecording}
              >
                停止
              </Button>
            </div>
          )}
          
          <div className="input-wrapper">
            <TextArea
              ref={inputRef}
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              placeholder={isRecording ? "正在录音中..." : "输入消息..."}
              autoSize={{ minRows: 1, maxRows: isMobile ? 3 : 4 }}
              onPressEnter={(e) => {
                if (!e.shiftKey) {
                  e.preventDefault();
                  sendMessage(inputValue);
                }
              }}
              disabled={sending || isRecording}
              style={{ fontSize: `${fontSize}px` }}
            />
            
            <div className="input-actions">
              <Space>
                {assistant.capabilities?.supports_file_upload && (
                  <Upload
                    beforeUpload={handleFileUpload}
                    showUploadList={false}
                    multiple={false}
                  >
                    <Tooltip title="上传文件">
                      <Button
                        type="text"
                        icon={<PaperClipOutlined />}
                        size={isMobile ? 'small' : 'middle'}
                      />
                    </Tooltip>
                  </Upload>
                )}
                
                {assistant.capabilities?.supports_vision && (
                  <Upload
                    beforeUpload={handleFileUpload}
                    showUploadList={false}
                    accept="image/*"
                  >
                    <Tooltip title="上传图片">
                      <Button
                        type="text"
                        icon={<PictureOutlined />}
                        size={isMobile ? 'small' : 'middle'}
                      />
                    </Tooltip>
                  </Upload>
                )}
                
                {assistant.capabilities?.supports_video_understanding && (
                  <Upload
                    beforeUpload={handleFileUpload}
                    showUploadList={false}
                    accept="video/*"
                  >
                    <Tooltip title="上传视频">
                      <Button
                        type="text"
                        icon={<VideoCameraOutlined />}
                        size={isMobile ? 'small' : 'middle'}
                      />
                    </Tooltip>
                  </Upload>
                )}
                
                {assistant.capabilities?.supports_speech_to_text && (
                  <Tooltip title={isRecording ? '停止录音' : '语音输入'}>
                    <Button
                      type="text"
                      icon={isRecording ? <StopOutlined /> : <AudioOutlined />}
                      size={isMobile ? 'small' : 'middle'}
                      danger={isRecording}
                      onClick={isRecording ? stopRecording : startRecording}
                    />
                  </Tooltip>
                )}
                
                <Button
                  type="primary"
                  icon={<SendOutlined />}
                  onClick={() => sendMessage(inputValue)}
                  loading={sending}
                  disabled={(!inputValue.trim() && !sending) || isRecording}
                  size={isMobile ? 'small' : 'middle'}
                />
              </Space>
            </div>
          </div>
        </div>
      </Footer>

      {/* 图片预览模态框 */}
      <Modal
        open={previewVisible}
        title="图片预览"
        footer={null}
        onCancel={() => setPreviewVisible(false)}
        width="80%"
        style={{ maxWidth: '800px' }}
      >
        <img
          src={previewImage}
          style={{ width: '100%', height: 'auto' }}
          alt="预览"
        />
      </Modal>

      {/* 音频播放器 */}
      <audio ref={audioRef} style={{ display: 'none' }} />

      {/* 设置抽屉 */}
      <Drawer
        title="聊天设置"
        placement="right"
        onClose={() => setSettingsVisible(false)}
        open={settingsVisible}
        width={isMobile ? '100%' : 400}
      >
        <Space direction="vertical" style={{ width: '100%' }} size="large">
          <div>
            <Text strong>字体大小</Text>
            <Slider
              min={12}
              max={20}
              value={fontSize}
              onChange={setFontSize}
              marks={{ 12: '小', 16: '中', 20: '大' }}
            />
          </div>
          
          <div>
            <Text strong>主题模式</Text>
            <div style={{ marginTop: 8 }}>
              <Switch
                checked={theme === 'dark'}
                onChange={(checked) => setTheme(checked ? 'dark' : 'light')}
                checkedChildren="深色"
                unCheckedChildren="浅色"
              />
            </div>
          </div>
          
          <div>
            <Text strong>自动滚动</Text>
            <div style={{ marginTop: 8 }}>
              <Switch
                checked={autoScroll}
                onChange={setAutoScroll}
                checkedChildren="开启"
                unCheckedChildren="关闭"
              />
            </div>
          </div>
          
          {assistant.capabilities && (
            <div>
              <Text strong>助手能力</Text>
              <div style={{ marginTop: 8 }}>
                <Alert
                  message="支持的功能"
                  description={
                    <div style={{ marginTop: 8 }}>
                      {renderCapabilityTags(assistant.capabilities)}
                    </div>
                  }
                  type="info"
                  showIcon
                />
              </div>
            </div>
          )}
          
          {(assistant.mcp_tools?.length > 0 || assistant.mcp_server_ids?.length > 0) && (
            <div>
              <Text strong>MCP工具</Text>
              <div style={{ marginTop: 8 }}>
                <Alert
                  message="可用工具"
                  description={
                    <div style={{ marginTop: 8 }}>
                      {/* 显示传统MCP工具 */}
                      {assistant.mcp_tools?.map(toolId => (
                        <Tag key={toolId} size="small" color="processing" style={{ marginBottom: 4 }}>
                          MCP工具 {toolId.slice(0, 8)}
                        </Tag>
                      ))}
                      
                      {/* 显示MCP服务器工具 */}
                      {assistant.mcp_server_ids?.map(serverId => (
                        <Tag key={serverId} size="small" color="blue" style={{ marginBottom: 4 }}>
                          MCP服务器 {serverId.slice(0, 8)}
                        </Tag>
                      ))}
                    </div>
                  }
                  type="success"
                  showIcon
                />
              </div>
            </div>
          )}
          
          {(assistant.mcp_tools?.length > 0 || assistant.mcp_server_ids?.length > 0) && (
            <div>
              <Text strong>MCP工具</Text>
              <div style={{ marginTop: 8 }}>
                <Alert
                  message="可用工具"
                  description={
                    <div style={{ marginTop: 8 }}>
                      {/* 显示传统MCP工具 */}
                      {assistant.mcp_tools?.map(toolId => (
                        <Tag key={toolId} size="small" color="processing" style={{ marginBottom: 4 }}>
                          MCP工具 {toolId.slice(0, 8)}
                        </Tag>
                      ))}
                      
                      {/* 显示MCP服务器工具 */}
                      {assistant.mcp_server_ids?.map(serverId => (
                        <Tag key={serverId} size="small" color="blue" style={{ marginBottom: 4 }}>
                          MCP服务器 {serverId.slice(0, 8)}
                        </Tag>
                      ))}
                    </div>
                  }
                  type="success"
                  showIcon
                />
              </div>
            </div>
          )}
          
          <Button
            type="primary"
            danger
            icon={<ClearOutlined />}
            onClick={clearMessages}
            block
          >
            清空对话
          </Button>
        </Space>
      </Drawer>

      {/* 移动端浮动按钮 */}
      {isMobile && (
        <FloatButton.Group
          trigger="click"
          type="primary"
          style={{ right: 24, bottom: 80 }}
          icon={<SettingOutlined />}
        >
          <FloatButton
            icon={<ClearOutlined />}
            tooltip="清空对话"
            onClick={clearMessages}
          />
          <FloatButton
            icon={<ShareAltOutlined />}
            tooltip="分享"
            onClick={() => {
              if (navigator.share) {
                navigator.share({
                  title: assistant.name,
                  text: assistant.description,
                  url: window.location.href
                });
              } else {
                navigator.clipboard.writeText(window.location.href);
                message.success('链接已复制到剪贴板');
              }
            }}
          />
        </FloatButton.Group>
      )}

      {/* CSS动画 */}
      <style>
        {`
          @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
          }
        `}
      </style>
    </Layout>
  );
};

export default AIAssistantChat; 