.tenant-management {
  padding: 24px;
}

.page-header {
  margin-bottom: 24px;
}

.stats-row {
  margin-bottom: 24px;
}

.stat-card {
  height: 100%;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
  transition: all 0.3s;
}

.stat-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.action-card {
  display: flex;
  align-items: center;
  justify-content: center;
}

.tenant-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
}

.table-toolbar {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
}

.tenant-tabs {
  margin-bottom: 16px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .tenant-management {
    padding: 16px;
  }
  
  .table-toolbar {
    flex-direction: column;
    gap: 16px;
  }
  
  .table-toolbar .ant-input-affix-wrapper {
    width: 100% !important;
  }
}
