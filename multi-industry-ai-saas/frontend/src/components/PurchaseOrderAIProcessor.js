import React, { useState, useCallback } from 'react';
import {
  Card,
  Button,
  Steps,
  Alert,
  Space,
  Spin,
  Progress,
  Typography,
  Row,
  Col,
  Table,
  Tag,
  Modal,
  message,
  Divider
} from 'antd';
import {
  UploadOutlined,
  LoadingOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  CloseCircleOutlined,
  EyeOutlined,
  SendOutlined,
  EditOutlined
} from '@ant-design/icons';
import { usePurchaseOrderProcessing } from '../hooks/usePurchaseOrderProcessing';
import { AsyncTaskProgress } from './AsyncTaskProgress';

const { Step } = Steps;
const { Title, Text } = Typography;

/**
 * 采购分拨单AI处理组件
 * 提供文件上传、AI分析、结果预览和确认提交的完整流程
 */
export const PurchaseOrderAIProcessor = ({
  onProcessingComplete,
  onError,
  defaultParams = {},
  style = {}
}) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [previewVisible, setPreviewVisible] = useState(false);
  const [editMode, setEditMode] = useState(false);
  
  const {
    processing,
    progress,
    status,
    message: processingMessage,
    result,
    error,
    taskInfo,
    processAIPreview,
    confirmUpload,
    cancel,
    reset,
    isIdle,
    isSyncTrying,
    isFallbackToAsync,
    isAsyncProcessing,
    isCompleted,
    isError
  } = usePurchaseOrderProcessing();

  /**
   * 开始AI处理
   */
  const handleStartProcessing = useCallback(async (params) => {
    setCurrentStep(1);
    try {
      await processAIPreview({
        ...defaultParams,
        ...params
      });
      setCurrentStep(2);
    } catch (err) {
      setCurrentStep(0);
      onError?.(err);
    }
  }, [processAIPreview, defaultParams, onError]);

  /**
   * 确认并提交
   */
  const handleConfirmSubmit = useCallback(async () => {
    setCurrentStep(3);
    try {
      await confirmUpload({
        ...defaultParams,
        fileId: result?.file_id
      });
      setCurrentStep(4);
      onProcessingComplete?.(result);
    } catch (err) {
      setCurrentStep(2);
      onError?.(err);
    }
  }, [confirmUpload, defaultParams, result, onProcessingComplete, onError]);

  /**
   * 重新开始
   */
  const handleRestart = useCallback(() => {
    reset();
    setCurrentStep(0);
    setPreviewVisible(false);
    setEditMode(false);
  }, [reset]);

  /**
   * 获取当前步骤状态
   */
  const getStepStatus = (step) => {
    if (currentStep > step) return 'finish';
    if (currentStep === step) {
      if (isError) return 'error';
      if (processing) return 'process';
      if (isCompleted) return 'finish';
      return 'process';
    }
    return 'wait';
  };

  /**
   * 渲染AI处理进度
   */
  const renderProcessingProgress = () => {
    if (!processing && !isCompleted && !isError) return null;

    return (
      <Card style={{ marginBottom: 16 }}>
        <div style={{ textAlign: 'center' }}>
          {/* 处理状态指示 */}
          <div style={{ marginBottom: 16 }}>
            {isSyncTrying && (
              <div>
                <Spin indicator={<LoadingOutlined style={{ fontSize: 24 }} spin />} />
                <div style={{ marginTop: 8 }}>
                  <Text>正在快速处理...</Text>
                </div>
              </div>
            )}
            
            {isFallbackToAsync && (
              <div>
                <Alert 
                  message="处理模式切换"
                  description="文件较复杂，已转为后台智能处理模式，请稍候..."
                  type="info"
                  showIcon
                  style={{ marginBottom: 16 }}
                />
                {taskInfo && (
                  <AsyncTaskProgress
                    taskInfo={taskInfo}
                    title="AI智能分析中"
                    showDetails={true}
                    onCancel={cancel}
                  />
                )}
              </div>
            )}
            
            {(isAsyncProcessing || (processing && !isSyncTrying && !isFallbackToAsync)) && (
              <div>
                <Progress 
                  type="circle" 
                  percent={progress} 
                  status={isError ? 'exception' : 'active'}
                />
                <div style={{ marginTop: 8 }}>
                  <Text>{processingMessage}</Text>
                </div>
              </div>
            )}
            
            {isCompleted && (
              <div>
                <CheckCircleOutlined style={{ fontSize: 48, color: '#52c41a' }} />
                <div style={{ marginTop: 8 }}>
                  <Text strong>处理完成</Text>
                </div>
              </div>
            )}
            
            {isError && (
              <div>
                <CloseCircleOutlined style={{ fontSize: 48, color: '#ff4d4f' }} />
                <div style={{ marginTop: 8 }}>
                  <Text type="danger">{error?.message || '处理失败'}</Text>
                </div>
              </div>
            )}
          </div>

          {/* 操作按钮 */}
          <Space>
            {processing && (
              <Button onClick={cancel}>
                取消处理
              </Button>
            )}
            
            {isError && (
              <Button type="primary" onClick={handleRestart}>
                重新开始
              </Button>
            )}
            
            {isCompleted && result && (
              <>
                <Button icon={<EyeOutlined />} onClick={() => setPreviewVisible(true)}>
                  预览结果
                </Button>
                <Button type="primary" icon={<SendOutlined />} onClick={handleConfirmSubmit}>
                  确认提交
                </Button>
              </>
            )}
          </Space>
        </div>
      </Card>
    );
  };

  /**
   * 渲染结果预览
   */
  const renderResultPreview = () => {
    if (!result) return null;

    const { purchase_items = [], distribution_destinations = [], warehouse_entries = [] } = result;

    return (
      <Modal
        title="AI分析结果预览"
        visible={previewVisible}
        onCancel={() => setPreviewVisible(false)}
        width={1200}
        footer={[
          <Button key="edit" icon={<EditOutlined />} onClick={() => setEditMode(!editMode)}>
            {editMode ? '完成编辑' : '编辑数据'}
          </Button>,
          <Button key="cancel" onClick={() => setPreviewVisible(false)}>
            关闭
          </Button>,
          <Button key="submit" type="primary" icon={<SendOutlined />} onClick={handleConfirmSubmit}>
            确认提交
          </Button>
        ]}
      >
        {/* 采购商品 */}
        {purchase_items.length > 0 && (
          <div style={{ marginBottom: 24 }}>
            <Title level={4}>采购商品清单</Title>
            <Table
              dataSource={purchase_items}
              rowKey="index"
              pagination={false}
              size="small"
              columns={[
                { title: '商品名称', dataIndex: 'product_name', key: 'product_name' },
                { title: '规格', dataIndex: 'product_specification', key: 'product_specification' },
                { title: '单位', dataIndex: 'product_unit', key: 'product_unit' },
                { title: '数量', dataIndex: 'quantity', key: 'quantity', align: 'right' },
                { title: '单价', dataIndex: 'unit_price', key: 'unit_price', align: 'right' },
                { title: '总额', dataIndex: 'total_amount', key: 'total_amount', align: 'right' }
              ]}
            />
          </div>
        )}

        {/* 分拨计划 */}
        {distribution_destinations.length > 0 && (
          <div style={{ marginBottom: 24 }}>
            <Title level={4}>分拨计划</Title>
            {distribution_destinations.map((dest, index) => (
              <Card key={index} size="small" style={{ marginBottom: 16 }}>
                <div style={{ marginBottom: 8 }}>
                  <Tag color={dest.type === 'store' ? 'blue' : 'green'}>
                    {dest.type === 'store' ? '门店' : '仓库'}
                  </Tag>
                  <Text strong>{dest.target_name}</Text>
                  <Text type="secondary" style={{ marginLeft: 8 }}>
                    总额: ¥{dest.total_amount?.toFixed(2) || '0.00'}
                  </Text>
                </div>
                <Table
                  dataSource={dest.items}
                  rowKey={(item, idx) => `${index}-${idx}`}
                  pagination={false}
                  size="small"
                  columns={[
                    { title: '商品名称', dataIndex: 'product_name', key: 'product_name' },
                    { title: '数量', dataIndex: 'quantity', key: 'quantity', align: 'right' },
                    { title: '单价', dataIndex: 'unit_price', key: 'unit_price', align: 'right' },
                    { title: '金额', dataIndex: 'total_amount', key: 'total_amount', align: 'right' }
                  ]}
                />
              </Card>
            ))}
          </div>
        )}

        {/* 仓库入库 */}
        {warehouse_entries.length > 0 && (
          <div>
            <Title level={4}>仓库入库</Title>
            {warehouse_entries.map((entry, index) => (
              <Card key={index} size="small" style={{ marginBottom: 16 }}>
                <div style={{ marginBottom: 8 }}>
                  <Tag color="orange">仓库</Tag>
                  <Text strong>{entry.warehouse_name}</Text>
                  <Text type="secondary" style={{ marginLeft: 8 }}>
                    总额: ¥{entry.total_amount?.toFixed(2) || '0.00'}
                  </Text>
                </div>
                <Table
                  dataSource={entry.items}
                  rowKey={(item, idx) => `${index}-${idx}`}
                  pagination={false}
                  size="small"
                  columns={[
                    { title: '商品名称', dataIndex: 'product_name', key: 'product_name' },
                    { title: '数量', dataIndex: 'quantity', key: 'quantity', align: 'right' },
                    { title: '单价', dataIndex: 'unit_price', key: 'unit_price', align: 'right' },
                    { title: '金额', dataIndex: 'total_amount', key: 'total_amount', align: 'right' }
                  ]}
                />
              </Card>
            ))}
          </div>
        )}
      </Modal>
    );
  };

  return (
    <div style={style}>
      {/* 处理步骤 */}
      <Card style={{ marginBottom: 16 }}>
        <Steps current={currentStep} status={isError ? 'error' : undefined}>
          <Step 
            title="文件上传" 
            icon={<UploadOutlined />}
            status={getStepStatus(0)}
          />
          <Step 
            title="AI智能分析" 
            icon={processing ? <LoadingOutlined /> : undefined}
            status={getStepStatus(1)}
          />
          <Step 
            title="结果确认" 
            icon={<EyeOutlined />}
            status={getStepStatus(2)}
          />
          <Step 
            title="创建订单" 
            icon={<SendOutlined />}
            status={getStepStatus(3)}
          />
          <Step 
            title="完成" 
            icon={<CheckCircleOutlined />}
            status={getStepStatus(4)}
          />
        </Steps>
      </Card>

      {/* 处理进度 */}
      {renderProcessingProgress()}

      {/* 结果预览 */}
      {renderResultPreview()}
    </div>
  );
};

export default PurchaseOrderAIProcessor; 