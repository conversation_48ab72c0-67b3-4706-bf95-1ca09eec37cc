import React, { useState, useEffect, useCallback } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Select,
  DatePicker,
  Button,
  Table,
  Typography,
  Tag,
  Space,
  Tooltip,
  Radio,
  List,
  message,
  Progress,
  Spin,
  Modal,
  Checkbox,
} from 'antd';
import {
  Line<PERSON><PERSON>Outlined,
  <PERSON><PERSON><PERSON>Outlined,
  <PERSON><PERSON><PERSON>Outlined,
  <PERSON>UpOutlined,
  ArrowDownOutlined,
  QuestionCircleOutlined,
  RobotOutlined,
  ExclamationCircleOutlined,
  TrophyOutlined,
  CloudOutlined,
  ReadOutlined,
  ContainerOutlined,
  AimOutlined,
  WarningOutlined,
} from '@ant-design/icons';
import dayjs from 'dayjs';
import superDashboardService from '../../../services/api/project/superDashboard';
import { useAuth } from '../../../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;

const OperationSuperDashboard = () => {
  const { projectId } = useAuth();
  const navigate = useNavigate();
  const [loading, setLoading] = useState(false);
  const [selectedDate, setSelectedDate] = useState(dayjs());
  
  // Real data states
  const [overallStats, setOverallStats] = useState(null);
  const [storePerformance, setStorePerformance] = useState([]);
  const [keyProducts, setKeyProducts] = useState([]);
  const [lossRanking, setLossRanking] = useState([]);
  const [news, setNews] = useState([]);
  const [priceMonitoringData, setPriceMonitoringData] = useState([]);
  const [priceMonitoringPeriod, setPriceMonitoringPeriod] = useState('week');
  const [priceAdjustmentModalVisible, setPriceAdjustmentModalVisible] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState(null);
  const [stores, setStores] = useState([]);
  const [selectedStores, setSelectedStores] = useState([]);
  const [selectedStore, setSelectedStore] = useState('all'); // 新增门店筛选状态

  // Mock data for components not yet connected to backend
  const [priceFluctuation, setPriceFluctuation] = useState({ vsYesterday: [], vsLastWeek: [], vsLastMonth: [] });
  const [stagnantGoods, setStagnantGoods] = useState([]);
  const [pricePower, setPricePower] = useState([]);
  const [inventoryAlerts, setInventoryAlerts] = useState([]);
  const [weather, setWeather] = useState(null);
  const [weatherData, setWeatherData] = useState(null);

  const [priceFluctuationPeriod, setPriceFluctuationPeriod] = useState('vsYesterday');
  const [keyProductType, setKeyProductType] = useState('byAmount');
  
  // 解析AI返回的新闻内容
  const parseNewsContent = useCallback((content) => {
    const newsItems = [];
    const lines = content.split('\n').filter(line => line.trim());

    let currentTitle = '';
    let currentSummary = '';

    lines.forEach((line, index) => {
        const trimmedLine = line.trim();

        if (trimmedLine.match(/^\d+\./) || trimmedLine.includes('：') || trimmedLine.includes(':')) {
            if (currentTitle) {
                newsItems.push({
                    id: Date.now() + index,
                    title: currentTitle,
                    summary: currentSummary,
                    timestamp: new Date().toISOString()
                });
            }
            currentTitle = trimmedLine.replace(/^\d+\./, '').trim();
            currentSummary = '';
        } else {
            currentSummary += (currentSummary ? '\n' : '') + trimmedLine;
        }
    });

    if (currentTitle) {
        newsItems.push({
            id: Date.now() + lines.length,
            title: currentTitle,
            summary: currentSummary,
            timestamp: new Date().toISOString()
        });
    }

    // 如果解析失败，返回原始文本
    if (newsItems.length === 0 && content.length > 0) {
      return [{ id: 1, title: '行业动态', summary: content, timestamp: new Date().toISOString() }];
    }

    return newsItems;
  }, []);

  const handleFetchIndustryNews = useCallback(async () => {
    setLoading(true);
    try {
        const projectId = localStorage.getItem('project_id');
        if (!projectId) {
            message.error("项目ID不存在");
            return;
        }

        // 获取默认AI助手
        const assistantsRes = await superDashboardService.operation.getAIAssistants?.(projectId) ||
                              await import('../../../services/api/project/ai').then(ai => ai.default.getAssistants(projectId));

        if (!assistantsRes.success || !assistantsRes.data || assistantsRes.data.length === 0) {
            message.error("未找到可用的AI助手");
            return;
        }

        const defaultAssistant = assistantsRes.data[0];

        // 构建获取行业新闻的提示词
        const prompt = `
请帮我获取今日零售行业的最新资讯和动态，包括：
1. 行业政策变化
2. 市场趋势分析
3. 新技术应用
4. 竞争对手动态
5. 消费者行为变化

请提供3-5条最重要的行业新闻，每条新闻包含标题和简要摘要。
请确保信息来源可靠，内容与零售行业相关。

今日日期：${new Date().toLocaleDateString('zh-CN')}
        `;

        // 调用AI助手获取行业新闻
        const aiService = await import('../../../services/api/project/ai').then(ai => ai.default);
        const response = await aiService.chatWithAssistant(projectId, {
            assistant_id: defaultAssistant.id,
            message: prompt,
            context: {
                type: 'industry_news',
                date: new Date().toISOString().split('T')[0],
                industry: 'retail'
            }
        });

        if (response.success) {
            const newsContent = response.data?.message || response.message || '';

            // 解析AI返回的新闻内容
            const newsItems = parseNewsContent(newsContent);
            setNews(newsItems);

            message.success("行业动态已更新");
        } else {
            message.error("获取行业动态失败");
        }
    } catch (error) {
        console.error("获取行业动态时出错:", error);
        message.error("获取行业动态时出错");
    } finally {
        setLoading(false);
    }
  }, [parseNewsContent]);

  const fetchDashboardData = async () => {
    const projectId = localStorage.getItem('project_id');
    if (!projectId) {
        message.error("无法加载运营大盘数据：项目ID不存在，请重新选择项目。");
        setLoading(false);
        return;
    }

    if (!selectedDate) return;
    setLoading(true);
    try {
      const dateStr = selectedDate.format('YYYY-MM-DD');
      
      const [
        kpisRes,
        storeRankingRes,
        keyProductsRes,
        lossRankingRes,
        newsRes,
        priceMonitoringRes,
        weatherRes,
        pricePowerRes
      ] = await Promise.all([
        superDashboardService.operation.getKpis(dateStr),
        superDashboardService.operation.getStorePerformanceRanking(dateStr),
        superDashboardService.operation.getKeyProducts(dateStr, keyProductType),
        superDashboardService.operation.getLossRanking(dateStr),
        superDashboardService.getNews(),
        // 获取第一个门店的价格监控数据作为示例
        storePerformance.length > 0 ?
          superDashboardService.operation.getPriceMonitoring(storePerformance[0]?.id, priceMonitoringPeriod) :
          Promise.resolve(null),
        // 获取第一个门店的天气数据
        storePerformance.length > 0 ?
          superDashboardService.getWeather(storePerformance[0]?.id) :
          Promise.resolve(null),
        // 获取第一个门店的价格力数据
        storePerformance.length > 0 ?
          superDashboardService.getPricePower(storePerformance[0]?.id, dateStr) :
          Promise.resolve(null)
      ]);

      if (kpisRes.success) setOverallStats(kpisRes.data);
      if (storeRankingRes.success) setStorePerformance(storeRankingRes.data);
      if (keyProductsRes.success) setKeyProducts(keyProductsRes.data);
      if (lossRankingRes.success) setLossRanking(lossRankingRes.data);
      if (newsRes.success) setNews(newsRes.data);
      if (priceMonitoringRes && priceMonitoringRes.success) setPriceMonitoringData(priceMonitoringRes.data);
      if (weatherRes && weatherRes.success) setWeatherData(weatherRes.data);
      if (pricePowerRes && pricePowerRes.success) setPricePower(pricePowerRes.data);

      // TODO: Fetch other data once backend is ready
      
    } catch (error) {
      message.error('加载运营大盘数据失败');
      console.error('Failed to fetch operation dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };
  
  useEffect(() => {
    fetchDashboardData();
  }, [selectedDate]);

  useEffect(() => {
    const fetchKeyProducts = async () => {
      const projectId = localStorage.getItem('project_id');
      if (!projectId || !selectedDate) return;
      
      try {
        const dateStr = selectedDate.format('YYYY-MM-DD');
        const res = await superDashboardService.operation.getKeyProducts(dateStr, keyProductType);
        if (res.success) setKeyProducts(res.data);
      } catch (error) {
        message.error('获取重点单品数据失败');
      }
    }
    fetchKeyProducts();
  }, [keyProductType, selectedDate]);

  const handleAIAnalysis = (title) => {
    message.info(`请求对 ${title} 进行AI分析...`);
  };

  const renderComparison = (value, unit = '%') => {
    if (value === null || typeof value === 'undefined') {
      return <Text type="secondary">-</Text>;
    }
    const color = value > 0 ? '#f5222d' : '#52c41a';
    const icon = value > 0 ? <ArrowUpOutlined /> : <ArrowDownOutlined />;
    return (
      <Text style={{ color }}>
        {icon} {Math.abs(value)}{unit}
      </Text>
    );
  };
  
  const priceFluctuationColumns = [
    { title: '产品名称', dataIndex: 'name', key: 'name' },
    {
      title: '价格趋势',
      dataIndex: 'trend',
      key: 'trend',
      render: (trend) =>
        trend === 'up' ? (
          <Tag color="red" icon={<ArrowUpOutlined />}>上涨</Tag>
        ) : (
          <Tag color="green" icon={<ArrowDownOutlined />}>下跌</Tag>
        ),
    },
    { title: '波动幅度', dataIndex: 'change', key: 'change' },
    { title: '当前价格', dataIndex: 'currentPrice', key: 'currentPrice' },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => <Button type="link" size="small" onClick={() => message.success(`已发送针对'${record.name}'的改价通知`)}>一键改价</Button>,
    },
  ];

  const keyProductsColumns = [
    { title: '产品名称', dataIndex: 'name', key: 'name' },
    keyProductType === 'byAmount' 
        ? { title: '总采购金额', dataIndex: 'amount', key: 'amount' }
        : { title: '总采购量', dataIndex: 'volume', key: 'volume' },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => <Button type="link" onClick={() => console.log(`查看 ${record.name} 的详情`)}>查看详情</Button>,
    },
  ];
  
  const storePerformanceColumns = [
    { title: '排名', dataIndex: 'rank', key: 'rank', render: (text, record) => <Space>{record.rank === 1 ? <TrophyOutlined style={{color: '#faad14'}}/> : record.rank} {record.name}</Space>},
    { title: '销售额', dataIndex: 'revenue', key: 'revenue' },
    { title: '毛利率', dataIndex: 'profitMargin', key: 'profitMargin' },
    { title: '损耗率', dataIndex: 'lossRate', key: 'lossRate' },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => <Button type="link" onClick={() => handleViewStoreDetail(record)}>查看详情</Button>,
    },
  ];

  // 查看门店详情
  const handleViewStoreDetail = (store) => {
    // 跳转到门店大盘，传递门店ID
    navigate(`/project/store/dashboard?storeId=${store.id || store.store_id}`);
  };

  // 处理改价操作
  const handlePriceAdjustment = (product) => {
    setSelectedProduct(product);
    setPriceAdjustmentModalVisible(true);
    // 获取门店列表
    if (storePerformance.length > 0) {
      setStores(storePerformance.map(store => ({
        id: store.id,
        name: store.name
      })));
    }
  };

  // 确认改价任务
  const handleConfirmPriceAdjustment = async () => {
    if (!selectedProduct || selectedStores.length === 0) {
      message.warning('请选择要改价的门店');
      return;
    }

    try {
      // 创建改价任务
      const taskData = {
        title: `${selectedProduct.name} 改价任务`,
        description: `将 ${selectedProduct.name} 价格调整为市场均价 ${selectedProduct.marketPrice}`,
        type: 'price_adjustment',
        priority: 'high',
        stores: selectedStores,
        product: selectedProduct
      };

      // 这里应该调用创建任务的API
      console.log('创建改价任务:', taskData);
      message.success(`已为 ${selectedStores.length} 个门店创建改价任务`);

      setPriceAdjustmentModalVisible(false);
      setSelectedProduct(null);
      setSelectedStores([]);
    } catch (error) {
      console.error('创建改价任务失败:', error);
      message.error('创建改价任务失败');
    }
  };

  const lossRankingColumns = [
    { title: '产品名称', dataIndex: 'name', key: 'name' },
    { title: '损耗量', dataIndex: 'lossQuantity', key: 'lossQuantity' },
    { title: '损耗金额', dataIndex: 'lossAmount', key: 'lossAmount' },
  ];

  const stagnantGoodsColumns = [
    { title: '商品名称', dataIndex: 'name', key: 'name' },
    { title: '库存量', key: 'quantity', render: (_, record) => `${record.quantity} ${record.unit}` },
    { title: '滞留天数', dataIndex: 'stagnantDays', key: 'stagnantDays', sorter: (a, b) => a.stagnantDays - b.stagnantDays },
    { title: '操作', key: 'action', render: () => <Button type="link" size="small">创建清货单</Button> },
  ];

  const pricePowerColumns = [
    { title: '产品名称', dataIndex: 'name', key: 'name' },
    { title: '我方价格', dataIndex: 'ourPrice', key: 'ourPrice' },
    { title: '市场均价', dataIndex: 'marketPrice', key: 'marketPrice' },
    { title: '价格差', dataIndex: 'diff', key: 'diff', render: (diff) => renderComparison(diff, '%') },
    { title: '状态', dataIndex: 'status', key: 'status', render: (status) => status === 'advantage' ? <Tag color="green">优势</Tag> : <Tag color="red">劣势</Tag> },
    { title: '操作', key: 'action', render: (_, record) => <Button type="link" size="small" onClick={() => handlePriceAdjustment(record)}>改价</Button> },
  ];

  const priceMonitoringColumns = [
    { title: '产品名称', dataIndex: 'product_name', key: 'product_name' },
    {
      title: '平均价格',
      dataIndex: 'avg_price',
      key: 'avg_price',
      render: (text) => `¥${parseFloat(text).toFixed(2)}`
    },
    {
      title: '价格区间',
      key: 'price_range',
      render: (_, record) => `¥${parseFloat(record.min_price).toFixed(2)} - ¥${parseFloat(record.max_price).toFixed(2)}`
    },
    {
      title: '波动率',
      dataIndex: 'price_volatility',
      key: 'price_volatility',
      render: (text) => {
        const volatility = parseFloat(text);
        const color = volatility > 10 ? '#ff4d4f' : volatility > 5 ? '#faad14' : '#52c41a';
        return <span style={{ color }}>{volatility.toFixed(1)}%</span>;
      },
      sorter: (a, b) => a.price_volatility - b.price_volatility,
    },
    { title: '到货次数', dataIndex: 'arrival_count', key: 'arrival_count' },
    { title: '日期', dataIndex: 'arrival_date', key: 'arrival_date' },
  ];

  const renderCardTitle = (title, tooltip) => (
    <Space>
      {title}
      <Tooltip title={tooltip}>
        <QuestionCircleOutlined style={{ color: 'rgba(0,0,0,.45)' }}/>
      </Tooltip>
    </Space>
  );

  const renderAIButton = (title) => (
    <Button
      type="primary"
      ghost
      icon={<RobotOutlined />}
      size="small"
      onClick={() => handleAIAnalysis(title)}
    >
      AI 分析
    </Button>
  );
  
  // 当项目ID不存在时，显示加载中或提示，避免渲染不完整或错误的组件
  if (!projectId) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <Spin size="large" tip="正在加载项目信息..." />
      </div>
    );
  }

  return (
    <div style={{ padding: '20px' }}>
    <Spin spinning={loading} size="large" tip="正在加载运营数据...">
      <div style={{ backgroundColor: '#fff', padding: '24px' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '24px' }}>
            <div>
                <Title level={4} style={{ marginBottom: '8px' }}>运营大盘</Title>
                <Paragraph style={{ marginBottom: 0 }}>
                    从全局视角监控所有门店的运营状况，分析关键指标，发现潜在问题和增长机会。
                </Paragraph>
            </div>
            <Space>
                <Select
                  value={selectedStore}
                  onChange={setSelectedStore}
                  style={{ width: 150 }}
                  placeholder="选择门店"
                >
                  <Option value="all">全部门店</Option>
                  {storePerformance.map(store => (
                    <Option key={store.id || store.store_id} value={store.id || store.store_id}>
                      {store.name}
                    </Option>
                  ))}
                </Select>
                <DatePicker value={selectedDate} onChange={setSelectedDate} allowClear={false} />
                <Button type="primary" onClick={fetchDashboardData}>查询</Button>
            </Space>
        </div>

        {/* KPIs */}
        <Row gutter={[24, 24]} style={{ marginBottom: 24 }}>
          <Col xs={24} sm={12} md={8} lg={5}>
            <Card>
              <Statistic
                title={renderCardTitle("总销售额", "所有门店在选定日期的总销售额")}
                value={overallStats?.totalRevenue?.value || 0}
                precision={2}
                prefix="¥"
              />
              <Text type="secondary" style={{ fontSize: 12 }}>较昨日 {renderComparison(overallStats?.totalRevenue?.vsYesterday)}</Text><br/>
              <Text type="secondary" style={{ fontSize: 12 }}>较上周 {renderComparison(overallStats?.totalRevenue?.vsLastWeek)}</Text>
            </Card>
          </Col>
          <Col xs={24} sm={12} md={8} lg={5}>
            <Card>
              <Statistic
                title={renderCardTitle("总损耗", "所有门店在选定日期的总损耗金额")}
                value={overallStats?.totalLoss?.value || 0}
                precision={2}
                prefix="¥"
              />
              <Text type="secondary" style={{ fontSize: 12 }}>较昨日 {renderComparison(overallStats?.totalLoss?.vsYesterday)}</Text><br/>
              <Text type="secondary" style={{ fontSize: 12 }}>较上周 {renderComparison(overallStats?.totalLoss?.vsLastWeek)}</Text>
            </Card>
          </Col>
          <Col xs={24} sm={12} md={8} lg={5}>
            <Card>
              <Statistic
                title={renderCardTitle("总客流量", "所有门店在选定日期的总客流量")}
                value={overallStats?.totalCustomers?.value || 0}
              />
              <Text type="secondary" style={{ fontSize: 12 }}>较昨日 {renderComparison(overallStats?.totalCustomers?.vsYesterday)}</Text><br/>
              <Text type="secondary" style={{ fontSize: 12 }}>较上周 {renderComparison(overallStats?.totalCustomers?.vsLastWeek)}</Text>
            </Card>
          </Col>
          <Col xs={24} sm={12} md={8} lg={5}>
            <Card>
              <Statistic
                title={renderCardTitle("价格波动", "核心商品价格综合波动指数")}
                value={overallStats?.avgPriceFluctuation?.value || 0}
                suffix="%"
              />
              <Text type="secondary" style={{ fontSize: 12 }}>较昨日 {renderComparison(overallStats?.avgPriceFluctuation?.vsYesterday, '')}</Text><br/>
              <Text type="secondary" style={{ fontSize: 12 }}>较上周 {renderComparison(overallStats?.avgPriceFluctuation?.vsLastWeek, '')}</Text>
            </Card>
          </Col>
          <Col xs={24} sm={24} md={24} lg={4}>
            <Card style={{height: '100%', display: 'flex', flexDirection: 'column', justifyContent: 'center'}}>
                <Text>销售目标达成率</Text>
                <Progress 
                    percent={overallStats?.salesTarget?.percentage || 0} 
                    strokeColor={{ '0%': '#108ee9', '100%': '#87d068' }}
                />
                <Text type="secondary" style={{ fontSize: 12 }}>
                    目标: ¥{overallStats?.salesTarget?.total?.toLocaleString() || 0} / 
                    完成: ¥{overallStats?.salesTarget?.completed?.toLocaleString() || 0}
                </Text>
            </Card>
          </Col>
        </Row>
        
        {/* Main Content */}
        <Row gutter={[24, 24]}>
          <Col xs={24} lg={16}>
            <Row gutter={[24, 24]}>
                {/* 价格监控 */}
                <Col span={24}>
                    <Card
                        title={renderCardTitle('价格监控', '基于到货确认单的价格波动分析')}
                        extra={
                            <Space>
                                <Select
                                    value={priceMonitoringPeriod}
                                    onChange={setPriceMonitoringPeriod}
                                    size="small"
                                    style={{ width: 80 }}
                                >
                                    <Option value="day">日</Option>
                                    <Option value="week">周</Option>
                                    <Option value="month">月</Option>
                                </Select>
                                <Button
                                    size="small"
                                    onClick={() => navigate('/project/operation/price-monitoring-detail')}
                                >
                                    查看详情
                                </Button>
                                {renderAIButton('价格监控')}
                            </Space>
                        }
                    >
                        {priceMonitoringData.price_trends && priceMonitoringData.price_trends.length > 0 ? (
                            <>
                                <div style={{ marginBottom: 16 }}>
                                    <Space>
                                        <Text>平均波动率: <strong style={{ color: priceMonitoringData.summary?.avg_volatility > 10 ? '#ff4d4f' : '#52c41a' }}>{priceMonitoringData.summary?.avg_volatility}%</strong></Text>
                                        <Text>高波动产品: <strong style={{ color: '#faad14' }}>{priceMonitoringData.summary?.high_volatility_count}</strong></Text>
                                        <Text>监控产品: <strong>{priceMonitoringData.summary?.total_products}</strong></Text>
                                    </Space>
                                </div>
                                <Table
                                    rowKey={(record) => `${record.product_name}-${record.arrival_date}`}
                                    dataSource={priceMonitoringData.price_trends}
                                    columns={priceMonitoringColumns}
                                    pagination={{ pageSize: 5 }}
                                    size="small"
                                />
                            </>
                        ) : (
                            <div style={{ textAlign: 'center', padding: '20px' }}>
                                <Text type="secondary">暂无价格监控数据</Text>
                            </div>
                        )}
                    </Card>
                </Col>

                {/* 门店业绩排行 */}
                <Col span={24}>
                    <Card
                        title={renderCardTitle('门店业绩排行', '根据销售额、毛利率、损耗率综合排名')}
                        extra={renderAIButton('门店业绩')}
                    >
                        <Table
                            rowKey="id"
                            dataSource={storePerformance}
                            columns={storePerformanceColumns}
                            pagination={false}
                            size="small"
                        />
                    </Card>
                </Col>
                
                {/* 重点单品分析 & 损耗排行 */}
                <Col xs={24} md={12}>
                    <Card
                        title={renderCardTitle('重点单品分析', '采购量或采购额最高的TOP5商品')}
                        extra={
                            <Radio.Group value={keyProductType} onChange={(e) => setKeyProductType(e.target.value)} size="small">
                                <Radio.Button value="byAmount">按金额</Radio.Button>
                                <Radio.Button value="byQuantity">按数量</Radio.Button>
                            </Radio.Group>
                        }
                    >
                        <Table
                            rowKey="id"
                            dataSource={keyProducts}
                            columns={keyProductsColumns}
                            pagination={false}
                            size="small"
                        />
                    </Card>
                </Col>
                <Col xs={24} md={12}>
                    <Card title={renderCardTitle('损耗排行', '损耗金额最高的TOP5商品')}>
                        <Table
                            rowKey="id"
                            dataSource={lossRanking}
                            columns={lossRankingColumns}
                            pagination={false}
                            size="small"
                        />
                    </Card>
                </Col>
            </Row>
          </Col>

          {/* Right Sidebar */}
          <Col xs={24} lg={8}>
            <Row gutter={[24, 24]}>
                {/* 天气 & 行业动态 */}
                <Col span={24}>
                  <Card
                    title={<Space><CloudOutlined />天气与动态</Space>}
                  >
                    {weatherData ? (
                      <div style={{ marginBottom: 16 }}>
                        <Title level={5}><CloudOutlined /> {weatherData.city} 天气</Title>
                        {/* 今日天气 */}
                        <div style={{ marginBottom: 12, padding: 12, backgroundColor: '#f0f9ff', borderRadius: 6 }}>
                          <Text strong>今日: </Text>
                          <Text>{`${weatherData.current?.temperature || weatherData.temperature}°C, ${weatherData.current?.description || weatherData.description}`}</Text>
                          <br />
                          <Text type="secondary" style={{ fontSize: 12 }}>
                            {weatherData.current?.wind_power || weatherData.wind_power} | {weatherData.current?.humidity || weatherData.humidity}
                          </Text>
                        </div>
                        {/* 未来5天预报 */}
                        {weatherData.forecasts && weatherData.forecasts.length > 0 && (
                          <div>
                            <Text strong style={{ fontSize: 12, color: '#666' }}>未来5天预报:</Text>
                            {weatherData.forecasts.map((forecast, index) => (
                              <div key={index} style={{
                                marginTop: 8,
                                padding: 8,
                                backgroundColor: '#fafafa',
                                borderRadius: 4,
                                fontSize: 12
                              }}>
                                <Text strong>{forecast.date}: </Text>
                                <Text>{forecast.day_weather} {forecast.night_temp}°C~{forecast.day_temp}°C</Text>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    ) : <Text type="secondary">天气信息加载中...</Text>}
                    <div>
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 }}>
                        <Text strong>行业动态</Text>
                        <Button
                          size="small"
                          type="primary"
                          ghost
                          onClick={handleFetchIndustryNews}
                          loading={loading}
                        >
                          获取新闻
                        </Button>
                      </div>
                      {news && news.length > 0 ? (
                        <List
                          dataSource={news}
                          renderItem={item => (
                            <List.Item>
                              <List.Item.Meta
                                avatar={<ReadOutlined />}
                                title={<a href="#!" style={{ fontSize: 14 }}>{item.title}</a>}
                                description={item.summary && (
                                  <Text type="secondary" style={{ fontSize: 12 }}>
                                    {item.summary.substring(0, 80)}...
                                  </Text>
                                )}
                              />
                            </List.Item>
                          )}
                          size="small"
                        />
                      ) : (
                        <div style={{ textAlign: 'center', padding: '20px' }}>
                          <ReadOutlined style={{ fontSize: 32, color: '#d9d9d9', marginBottom: 8 }} />
                          <div>
                            <Text type="secondary">暂无行业动态</Text><br/>
                            <Text type="secondary" style={{ fontSize: 12 }}>点击"获取新闻"获取最新资讯</Text>
                          </div>
                        </div>
                      )}
                    </div>
                  </Card>
                </Col>

                {/* 价格力分析 */}
                <Col span={24}>
                    <Card
                        title={renderCardTitle('价格力分析', '我方售价与市场均价对比')}
                        extra={<Button size="small">竞调设置</Button>}
                    >
                        <Table
                            rowKey="id"
                            dataSource={pricePower}
                            columns={pricePowerColumns}
                            pagination={false}
                            size="small"
                        />
                    </Card>
                </Col>
                
                {/* 仓库滞销品 */}
                <Col span={24}>
                  <Card
                    title={renderCardTitle('仓库滞销品', '超过30天无销售记录的商品')}
                    extra={<Button size="small">查看全部</Button>}
                  >
                    <Table
                        rowKey="id"
                        dataSource={stagnantGoods}
                        columns={stagnantGoodsColumns}
                        pagination={false}
                        size="small"
                    />
                  </Card>
                </Col>
            </Row>
          </Col>
        </Row>
      </div>
    </Spin>

      {/* 改价任务Modal */}
      <Modal
        title="创建改价任务"
        open={priceAdjustmentModalVisible}
        onOk={handleConfirmPriceAdjustment}
        onCancel={() => setPriceAdjustmentModalVisible(false)}
        width={600}
      >
        {selectedProduct && (
          <div>
            <div style={{ marginBottom: 16 }}>
              <Text strong>产品信息：</Text>
              <p>产品名称：{selectedProduct.name}</p>
              <p>当前价格：{selectedProduct.ourPrice}</p>
              <p>市场均价：{selectedProduct.marketPrice}</p>
              <p>建议调整：调整至市场均价</p>
            </div>

            <div style={{ marginBottom: 16 }}>
              <Text strong>选择门店：</Text>
              <Checkbox.Group
                style={{ width: '100%', marginTop: 8 }}
                value={selectedStores}
                onChange={setSelectedStores}
              >
                <Row>
                  {stores.map(store => (
                    <Col span={12} key={store.id} style={{ marginBottom: 8 }}>
                      <Checkbox value={store.id}>{store.name}</Checkbox>
                    </Col>
                  ))}
                </Row>
              </Checkbox.Group>
            </div>

            <div style={{ padding: 16, backgroundColor: '#f5f5f5', borderRadius: 4 }}>
              <Text type="secondary">
                💡 确认后将为选中的门店创建改价任务，门店管理员将收到通知并执行价格调整。
              </Text>
            </div>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default OperationSuperDashboard;