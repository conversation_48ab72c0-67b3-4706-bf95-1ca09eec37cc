import api from '../httpClient';
import { getTenantId } from '../httpClient';

/**
 * 租户-用户管理API
 */
const user = {
  /**
   * 获取用户列表
   * @param {Object} params - 查询参数
   * @returns {Promise} - 用户列表
   */
  getList: async (params = {}) => {
    const tenantId = getTenantId();
    if (!tenantId) {
      throw new Error('未指定租户ID');
    }
    return api.get('/tenant/users', { params });
  },

  /**
   * 获取用户详情
   * @param {string} id - 用户ID
   * @returns {Promise} - 用户详情
   */
  getDetail: async (id) => {
    const tenantId = getTenantId();
    if (!tenantId) {
      throw new Error('未指定租户ID');
    }
    return api.get(`/tenant/users/${id}`);
  },

  /**
   * 创建用户
   * @param {Object} data - 用户数据
   * @returns {Promise} - 创建结果
   */
  create: async (data) => {
    const tenantId = getTenantId();
    if (!tenantId) {
      throw new Error('未指定租户ID');
    }
    return api.post('/tenant/users', data);
  },

  /**
   * 更新用户
   * @param {string} id - 用户ID
   * @param {Object} data - 用户数据
   * @returns {Promise} - 更新结果
   */
  update: async (id, data) => {
    const tenantId = getTenantId();
    if (!tenantId) {
      throw new Error('未指定租户ID');
    }
    return api.put(`/tenant/users/${id}`, data);
  },

  /**
   * 删除用户
   * @param {string} id - 用户ID
   * @returns {Promise} - 删除结果
   */
  delete: async (id) => {
    const tenantId = getTenantId();
    if (!tenantId) {
      throw new Error('未指定租户ID');
    }
    return api.delete(`/tenant/users/${id}`);
  },

  /**
   * 获取租户管理员列表
   * @param {Object} params - 查询参数
   * @returns {Promise} - 租户管理员列表
   */
  getAdmins: async (params = {}) => {
    const tenantId = getTenantId();
    if (!tenantId) {
      throw new Error('未指定租户ID');
    }
    return api.get('/tenant/admins', { params });
  },

  /**
   * 创建租户管理员
   * @param {Object} data - 管理员数据
   * @returns {Promise} - 创建结果
   */
  createAdmin: async (data) => {
    const tenantId = getTenantId();
    if (!tenantId) {
      throw new Error('未指定租户ID');
    }
    return api.post('/tenant/admins', data);
  },

  /**
   * 更新租户管理员
   * @param {string} id - 管理员ID
   * @param {Object} data - 管理员数据
   * @returns {Promise} - 更新结果
   */
  updateAdmin: async (id, data) => {
    const tenantId = getTenantId();
    if (!tenantId) {
      throw new Error('未指定租户ID');
    }
    return api.put(`/tenant/admins/${id}`, data);
  },

  /**
   * 删除租户管理员
   * @param {string} id - 管理员ID
   * @returns {Promise} - 删除结果
   */
  deleteAdmin: async (id) => {
    const tenantId = getTenantId();
    if (!tenantId) {
      throw new Error('未指定租户ID');
    }
    return api.delete(`/tenant/admins/${id}`);
  }
};

export default user;
