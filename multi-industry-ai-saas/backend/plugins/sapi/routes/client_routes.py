#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
客户端路由

负责管理MCP客户端配置
"""

import logging
from typing import Dict, Any, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Body, Query, Path
from sqlalchemy.orm import Session

from db.database import get_db
from models.user import User
from core.auth import get_current_user

from ..models import SAPIToken, MCPServer
from .. import router

logger = logging.getLogger(__name__)

# 获取客户端配置
@router.get("/client-config", response_model=Dict[str, Any])
async def get_client_config(
    token_id: Optional[str] = None,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取客户端配置"""
    try:
        # 获取令牌
        token = None
        if token_id:
            token = db.query(SAPIToken).filter(
                SAPIToken.id == token_id,
                SAPIToken.user_id == current_user.id
            ).first()
            
            if not token:
                raise HTTPException(status_code=404, detail="令牌不存在")
        
        # 获取MCP服务器
        servers = db.query(MCPServer).filter(MCPServer.enabled == True).all()
        
        # 获取工具列表（如有实际逻辑可替换）
        tools = []
        
        # 构建客户端配置
        config = {
            "user": {
                "id": str(current_user.id),
                "username": current_user.username,
                "email": current_user.email
            },
            "token": {
                "id": str(token.id),
                "name": token.name,
                "token": token.token,
                "permissions": token.permissions
            } if token else None,
            "servers": [],
            "tools": tools
        }
        
        # 添加服务器配置
        for server in servers:
            config["servers"].append({
                "id": str(server.id),
                "name": server.name,
                "description": server.description
            })
        
        # 添加客户端配置示例
        config["examples"] = {
            "claude": {
                "title": "Claude AI",
                "description": "在Claude AI中使用MCP工具",
                "config": {
                    "url": f"https://api.example.com/api/v1/project/plugin/sapi/mcp",
                    "headers": {
                        "Authorization": f"Bearer {token.token if token else 'YOUR_TOKEN'}"
                    }
                }
            },
            "cursor": {
                "title": "Cursor IDE",
                "description": "在Cursor IDE中使用MCP工具",
                "config": {
                    "url": f"https://api.example.com/api/v1/project/plugin/sapi/mcp",
                    "headers": {
                        "Authorization": f"Bearer {token.token if token else 'YOUR_TOKEN'}"
                    }
                }
            }
        }
        
        return {
            "success": True,
            "message": "获取客户端配置成功",
            "data": config
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取客户端配置失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取客户端配置失败: {str(e)}")

# 获取客户端配置示例
@router.get("/client-examples", response_model=Dict[str, Any])
async def get_client_examples(
    current_user: User = Depends(get_current_user)
):
    """获取客户端配置示例"""
    try:
        # 构建客户端配置示例
        examples = {
            "claude": {
                "title": "Claude AI",
                "description": "在Claude AI中使用MCP工具",
                "steps": [
                    "1. 登录Claude AI",
                    "2. 打开设置",
                    "3. 选择API集成",
                    "4. 添加新的API集成",
                    "5. 输入API URL和令牌",
                    "6. 保存配置"
                ],
                "config": {
                    "url": "https://api.example.com/api/v1/project/plugin/sapi/mcp",
                    "headers": {
                        "Authorization": "Bearer YOUR_TOKEN"
                    }
                }
            },
            "cursor": {
                "title": "Cursor IDE",
                "description": "在Cursor IDE中使用MCP工具",
                "steps": [
                    "1. 打开Cursor IDE",
                    "2. 打开设置",
                    "3. 选择AI集成",
                    "4. 添加新的API集成",
                    "5. 输入API URL和令牌",
                    "6. 保存配置"
                ],
                "config": {
                    "url": "https://api.example.com/api/v1/project/plugin/sapi/mcp",
                    "headers": {
                        "Authorization": "Bearer YOUR_TOKEN"
                    }
                }
            }
        }
        
        return {
            "success": True,
            "message": "获取客户端配置示例成功",
            "data": examples
        }
    except Exception as e:
        logger.error(f"获取客户端配置示例失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取客户端配置示例失败: {str(e)}")
