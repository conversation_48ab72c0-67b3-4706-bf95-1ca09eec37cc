#!/usr/bin/env python
# -*- coding: utf-8 -*-

import logging
from typing import Dict, Any, List, Optional
import uuid
import asyncio

from .utils.notification import send_notification_to_dingtalk

# 初始化日志
logger = logging.getLogger(__name__)

async def handle_system_notification(
    tenant_id: uuid.UUID,
    project_id: uuid.UUID,
    notification_type: str,
    title: str,
    content: str,
    target_users: List[str] = None,
    target_groups: List[str] = None,
    extra_data: Dict[str, Any] = None
) -> Dict[str, Any]:
    """
    处理系统通知，集成到钉钉

    Args:
        tenant_id: 租户ID
        project_id: 项目ID
        notification_type: 通知类型
        title: 通知标题
        content: 通知内容
        target_users: 目标用户ID列表
        target_groups: 目标群组ID列表
        extra_data: 额外数据

    Returns:
        Dict[str, Any]: 处理结果
    """
    try:
        # 发送通知到钉钉
        result = await send_notification_to_dingtalk(
            tenant_id=tenant_id,
            project_id=project_id,
            notification_type=notification_type,
            title=title,
            content=content,
            target_users=target_users,
            target_groups=target_groups,
            extra_data=extra_data
        )

        return result
    except Exception as e:
        logger.error(f"处理系统通知失败: {str(e)}")
        return {
            "success": False,
            "message": f"处理系统通知失败: {str(e)}",
            "data": {}
        }
