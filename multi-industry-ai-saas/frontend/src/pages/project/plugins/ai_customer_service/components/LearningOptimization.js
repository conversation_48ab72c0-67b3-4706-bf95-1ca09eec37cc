import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  Space,
  Progress,
  Table,
  Tag,
  Alert,
  Typography,
  Row,
  Col,
  Statistic,
  List,
  Badge,
  Modal,
  message,
  Tabs,
  Form,
  Switch,
  InputNumber,
  Select,
  Slider
} from 'antd';
import {
  BulbOutlined,
  RocketOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  TrophyOutlined,
  RiseOutlined,
  ReloadOutlined
} from '@ant-design/icons';

const { Title, Paragraph, Text } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;

const LearningOptimization = () => {
  const [loading, setLoading] = useState(false);
  const [analysisData, setAnalysisData] = useState(null);
  const [optimizationHistory, setOptimizationHistory] = useState([]);
  const [learningConfig, setLearningConfig] = useState({
    auto_optimization_enabled: true,
    optimization_frequency: 'daily',
    quality_threshold: 0.7,
    satisfaction_threshold: 4.0,
    response_time_threshold: 3.0,
    learning_rate: 0.1,
    min_samples_for_optimization: 100,
    enable_intent_learning: true,
    enable_response_optimization: true,
    enable_performance_tuning: true
  });
  const [configForm] = Form.useForm();

  // 获取项目ID
  const getProjectId = () => {
    const projectId = localStorage.getItem('project_id');
    if (projectId && projectId !== 'null' && projectId !== 'undefined') {
      return projectId;
    }

    const userData = JSON.parse(localStorage.getItem('user_data') || '{}');
    if (userData && userData.project_id) {
      localStorage.setItem('project_id', userData.project_id);
      return userData.project_id;
    }

    return null;
  };

  // 获取学习配置
  const fetchLearningConfig = async () => {
    try {
      const projectId = getProjectId();
      if (!projectId) {
        console.error('无法获取项目ID');
        return;
      }

      const response = await fetch(`/api/v1/project/${projectId}/plugin/ai-customer-service/learning/config`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setLearningConfig(data);
        configForm.setFieldsValue(data);
      }
    } catch (error) {
      console.error('获取学习配置失败:', error);
    }
  };

  // 保存学习配置
  const handleSaveConfig = async (values) => {
    try {
      const projectId = getProjectId();
      if (!projectId) {
        message.error('无法获取项目ID');
        return;
      }

      const response = await fetch(`/api/v1/project/${projectId}/plugin/ai-customer-service/learning/config`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(values)
      });

      if (response.ok) {
        setLearningConfig(values);
        message.success('学习配置保存成功');
      } else {
        message.error('保存学习配置失败');
      }
    } catch (error) {
      console.error('保存学习配置失败:', error);
      message.error('保存学习配置失败');
    }
  };

  // 获取学习分析数据
  const fetchAnalysisData = async () => {
    setLoading(true);
    try {
      const projectId = getProjectId();
      if (!projectId) {
        console.error('无法获取项目ID');
        message.error('无法获取项目ID');
        setLoading(false);
        return;
      }

      const response = await fetch(`/api/v1/project/${projectId}/plugin/ai-customer-service/learning/analysis`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        console.log('获取到真实学习分析数据:', data);
        setAnalysisData(data);
      } else {
        console.warn('学习分析API调用失败，状态码:', response.status);
        // 不再使用模拟数据，而是显示API调用失败的状态
        setAnalysisData(null);
        if (response.status === 404) {
          message.info('暂无学习分析数据，请先积累一些对话数据');
        } else {
          message.error('获取学习分析数据失败，请稍后重试');
        }
      }
    } catch (error) {
      console.error('获取分析数据失败:', error);
      message.error('获取分析数据失败，请检查网络连接');
      setAnalysisData(null);
    } finally {
      setLoading(false);
    }
  };

  // 获取优化历史
  const fetchOptimizationHistory = async () => {
    try {
      const projectId = getProjectId();
      if (!projectId) {
        console.error('无法获取项目ID');
        return;
      }

      const response = await fetch(`/api/v1/project/${projectId}/plugin/ai-customer-service/analytics/optimization-history`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setOptimizationHistory(data.history || []);
      } else {
        // 如果API调用失败，显示空数据
        setOptimizationHistory([]);
      }
    } catch (error) {
      console.error('获取优化历史失败:', error);
      setOptimizationHistory([]);
    }
  };

  // 应用优化建议
  const handleApplyOptimization = async (suggestion) => {
    try {
      setLoading(true);
      console.log('应用优化建议:', suggestion);
      
      // 模拟应用过程
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      message.success('优化建议已应用');
      fetchAnalysisData();
      fetchOptimizationHistory();
    } catch (error) {
      console.error('应用优化失败:', error);
      message.error('应用优化失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取优先级标签
  const getPriorityTag = (priority) => {
    const priorityMap = {
      'high': { color: 'red', text: '高' },
      'medium': { color: 'orange', text: '中' },
      'low': { color: 'green', text: '低' }
    };
    
    const priorityInfo = priorityMap[priority] || { color: 'default', text: priority };
    return <Tag color={priorityInfo.color}>{priorityInfo.text}</Tag>;
  };

  // 获取类型标签
  const getTypeTag = (type) => {
    const typeMap = {
      'automatic': { color: 'blue', text: '自动' },
      'manual': { color: 'purple', text: '手动' }
    };
    
    const typeInfo = typeMap[type] || { color: 'default', text: type };
    return <Tag color={typeInfo.color}>{typeInfo.text}</Tag>;
  };

  useEffect(() => {
    fetchAnalysisData();
    fetchOptimizationHistory();
    fetchLearningConfig();
  }, []);

  // 如果没有分析数据，显示空状态（已完全移除模拟数据回退）
  if (!loading && !analysisData) {
    return (
      <div style={{ padding: '50px', textAlign: 'center' }}>
        <Alert
          message="暂无学习分析数据"
          description={
            <div>
              <p>系统需要一定的对话数据来进行智能学习分析。</p>
              <p>建议操作步骤：</p>
              <ol style={{ textAlign: 'left', display: 'inline-block', marginTop: 16 }}>
                <li><strong>配置AI客服：</strong>确保已配置AI模型和知识库</li>
                <li><strong>进行对话测试：</strong>通过聊天界面或公共组件进行多次对话</li>
                <li><strong>积累数据：</strong>建议至少进行10-20次有意义的对话</li>
                <li><strong>等待分析：</strong>系统会自动分析对话质量并生成学习报告</li>
              </ol>
              <div style={{ marginTop: 24, padding: 16, backgroundColor: '#f6f8ff', borderRadius: 8 }}>
                <Text type="secondary">
                  💡 提示：学习优化功能基于真实的AI使用记录进行分析，
                  包括对话质量、意图识别准确性、响应时间和客户满意度等指标。
                </Text>
              </div>
              <Space style={{ marginTop: 24 }}>
                <Button 
                  type="primary" 
                  icon={<ReloadOutlined />}
                  onClick={fetchAnalysisData}
                >
                  重新检查数据
                </Button>
                <Button 
                  onClick={() => {
                    // 跳转到AI客服插件的公共组件配置页面的对话测试标签
                    const currentUrl = window.location.href;
                    const baseUrl = currentUrl.split('#')[0];
                    window.location.href = `${baseUrl}#/project/plugin/ai-customer-service/widget?tab=test`;
                  }}
                >
                  去进行对话测试
                </Button>
              </Space>
            </div>
          }
          type="info"
          showIcon
        />
      </div>
    );
  }

  return (
    <div>
      <Card title="智能学习优化">
        <Alert
          message="智能学习功能"
          description="基于对话数据自动学习和优化AI回复质量、意图识别准确性，实现系统的自我完善和持续改进。"
          type="info"
          showIcon
          style={{ marginBottom: 24 }}
        />

        <Tabs defaultActiveKey="1">
          <TabPane tab="学习分析" key="1">
            {/* 只有在有数据时才显示分析内容 */}
            {analysisData ? (
              <>
                {/* 综合评分 */}
                <Row gutter={16} style={{ marginBottom: 24 }}>
                  <Col span={24}>
                    <Card size="small">
                      <div style={{ textAlign: 'center' }}>
                        <Title level={3}>综合评分</Title>
                        <Progress
                          type="circle"
                          percent={Math.round((analysisData.overall_score || 0) * 100)}
                          format={percent => `${percent}分`}
                          strokeColor="#52c41a"
                          size={120}
                        />
                        <div style={{ marginTop: 16 }}>
                          <Text type="secondary">
                            基于对话质量、意图准确性、客户满意度等指标综合评估
                          </Text>
                        </div>
                      </div>
                    </Card>
                  </Col>
                </Row>

                {/* 详细指标 */}
                <Row gutter={16} style={{ marginBottom: 24 }}>
                  <Col xs={24} sm={12} md={6}>
                    <Card size="small">
                      <Statistic
                        title="对话质量"
                        value={Math.round((analysisData.conversation_quality?.avg_quality || 0) * 100)}
                        suffix="分"
                        prefix={<BulbOutlined />}
                        valueStyle={{ color: '#1890ff' }}
                      />
                      <Progress 
                        percent={Math.round((analysisData.conversation_quality?.avg_quality || 0) * 100)} 
                        size="small" 
                        showInfo={false}
                      />
                    </Card>
                  </Col>
                  <Col xs={24} sm={12} md={6}>
                    <Card size="small">
                      <Statistic
                        title="意图准确性"
                        value={Math.round((analysisData.intent_accuracy?.accuracy_score || 0) * 100)}
                        suffix="分"
                        prefix={<TrophyOutlined />}
                        valueStyle={{ color: '#52c41a' }}
                      />
                      <Progress 
                        percent={Math.round((analysisData.intent_accuracy?.accuracy_score || 0) * 100)} 
                        size="small" 
                        showInfo={false}
                      />
                    </Card>
                  </Col>
                  <Col xs={24} sm={12} md={6}>
                    <Card size="small">
                      <Statistic
                        title="响应速度"
                        value={analysisData.response_times?.avg_response_time || 0}
                        precision={1}
                        suffix="秒"
                        prefix={<RocketOutlined />}
                        valueStyle={{ color: '#722ed1' }}
                      />
                      <Text type="secondary" style={{ fontSize: 12 }}>
                        P95: {analysisData.response_times?.p95_response_time || 0}秒
                      </Text>
                    </Card>
                  </Col>
                  <Col xs={24} sm={12} md={6}>
                    <Card size="small">
                      <Statistic
                        title="客户满意度"
                        value={analysisData.customer_satisfaction?.avg_satisfaction || 0}
                        precision={1}
                        suffix="/ 5.0"
                        prefix={<RiseOutlined />}
                        valueStyle={{ color: '#f5222d' }}
                      />
                      <Text type="secondary" style={{ fontSize: 12 }}>
                        {analysisData.customer_satisfaction?.total_rated || 0} 次评价
                      </Text>
                    </Card>
                  </Col>
                </Row>

                {/* 优化建议 */}
                <Card title="优化建议" size="small">
                  <List
                    dataSource={analysisData.optimization_suggestions || []}
                    renderItem={suggestion => (
                      <List.Item
                        actions={[
                          <Button
                            type="primary"
                            size="small"
                            onClick={() => handleApplyOptimization(suggestion)}
                            loading={loading}
                          >
                            应用优化
                          </Button>
                        ]}
                      >
                        <List.Item.Meta
                          avatar={
                            <Badge 
                              status={suggestion.priority === 'high' ? 'error' : 'warning'} 
                            />
                          }
                          title={
                            <Space>
                              <span>{suggestion.title}</span>
                              {getPriorityTag(suggestion.priority)}
                            </Space>
                          }
                          description={
                            <div>
                              <Paragraph style={{ marginBottom: 8 }}>
                                {suggestion.description}
                              </Paragraph>
                              <div>
                                <Text strong>改进措施：</Text>
                                <ul style={{ marginTop: 4, marginBottom: 0 }}>
                                  {(suggestion.actions || []).map((action, index) => (
                                    <li key={index}>{action}</li>
                                  ))}
                                </ul>
                              </div>
                            </div>
                          }
                        />
                      </List.Item>
                    )}
                  />
                </Card>
              </>
            ) : (
              // 如果没有数据，显示加载或空状态
              <div style={{ padding: '50px', textAlign: 'center' }}>
                <Alert
                  message="正在加载学习分析数据..."
                  description="请稍候，系统正在分析对话数据并生成学习报告。"
                  type="info"
                  showIcon
                />
              </div>
            )}
          </TabPane>

          <TabPane tab="优化历史" key="2">
            <Card 
              title="优化历史记录"
              extra={
                <Button
                  icon={<ReloadOutlined />}
                  onClick={fetchOptimizationHistory}
                  size="small"
                >
                  刷新
                </Button>
              }
            >
              {optimizationHistory && optimizationHistory.length > 0 ? (
                <List
                  dataSource={optimizationHistory}
                  renderItem={record => (
                    <List.Item>
                      <List.Item.Meta
                        avatar={
                          <Badge 
                            status={record.status === 'completed' ? 'success' : 'processing'} 
                          />
                        }
                        title={
                          <Space>
                            <span>{record.date}</span>
                            {getTypeTag(record.type)}
                            <Tag color="green">+{(record.performance_gain * 100).toFixed(1)}%</Tag>
                          </Space>
                        }
                        description={
                          <div>
                            <Text strong>优化内容：</Text>
                            <ul style={{ marginTop: 4, marginBottom: 0 }}>
                              {(record.improvements || []).map((improvement, index) => (
                                <li key={index}>{improvement}</li>
                              ))}
                            </ul>
                            {record.usage_count && (
                              <div style={{ marginTop: 8 }}>
                                <Text type="secondary">
                                  基于 {record.usage_count} 次对话，平均token: {record.avg_tokens}，成本: ${record.avg_cost}
                                </Text>
                              </div>
                            )}
                          </div>
                        }
                      />
                    </List.Item>
                  )}
                />
              ) : (
                <div style={{ padding: '40px', textAlign: 'center' }}>
                  <Alert
                    message="暂无优化历史"
                    description={
                      <div>
                        <p>系统会根据对话数据自动生成学习优化记录。</p>
                        <p>优化历史基于真实的AI使用统计，包括：</p>
                        <ul style={{ textAlign: 'left', display: 'inline-block', marginTop: 8 }}>
                          <li>对话质量分析</li>
                          <li>响应时间优化</li>
                          <li>意图识别改进</li>
                          <li>知识库匹配增强</li>
                        </ul>
                      </div>
                    }
                    type="info"
                    showIcon
                  />
                </div>
              )}
            </Card>
          </TabPane>

          <TabPane tab="学习配置" key="3">
            <Card title="学习配置">
              <Form
                form={configForm}
                layout="vertical"
                onFinish={handleSaveConfig}
                initialValues={learningConfig}
              >
                <Row gutter={16}>
                  <Col span={12}>
                    <Card title="基础配置" size="small" style={{ marginBottom: 16 }}>
                      <Form.Item
                        name="auto_optimization_enabled"
                        label="启用自动优化"
                        valuePropName="checked"
                      >
                        <Switch />
                      </Form.Item>

                      <Form.Item
                        name="optimization_frequency"
                        label="优化频率"
                      >
                        <Select>
                          <Option value="hourly">每小时</Option>
                          <Option value="daily">每天</Option>
                          <Option value="weekly">每周</Option>
                          <Option value="monthly">每月</Option>
                        </Select>
                      </Form.Item>

                      <Form.Item
                        name="min_samples_for_optimization"
                        label="最小样本数"
                        help="触发优化所需的最小对话样本数"
                      >
                        <InputNumber min={10} max={1000} style={{ width: '100%' }} />
                      </Form.Item>
                    </Card>
                  </Col>

                  <Col span={12}>
                    <Card title="阈值配置" size="small" style={{ marginBottom: 16 }}>
                      <Form.Item
                        name="quality_threshold"
                        label="质量阈值"
                        help="低于此阈值将触发质量优化"
                      >
                        <Slider
                          min={0}
                          max={1}
                          step={0.1}
                          marks={{
                            0: '0',
                            0.5: '0.5',
                            1: '1'
                          }}
                        />
                      </Form.Item>

                      <Form.Item
                        name="satisfaction_threshold"
                        label="满意度阈值"
                        help="低于此阈值将触发满意度优化"
                      >
                        <Slider
                          min={1}
                          max={5}
                          step={0.1}
                          marks={{
                            1: '1',
                            3: '3',
                            5: '5'
                          }}
                        />
                      </Form.Item>

                      <Form.Item
                        name="response_time_threshold"
                        label="响应时间阈值（秒）"
                        help="超过此时间将触发性能优化"
                      >
                        <Slider
                          min={1}
                          max={10}
                          step={0.5}
                          marks={{
                            1: '1s',
                            5: '5s',
                            10: '10s'
                          }}
                        />
                      </Form.Item>
                    </Card>
                  </Col>
                </Row>

                <Row gutter={16}>
                  <Col span={12}>
                    <Card title="学习参数" size="small" style={{ marginBottom: 16 }}>
                      <Form.Item
                        name="learning_rate"
                        label="学习率"
                        help="控制模型学习的速度"
                      >
                        <Slider
                          min={0.01}
                          max={0.5}
                          step={0.01}
                          marks={{
                            0.01: '0.01',
                            0.1: '0.1',
                            0.5: '0.5'
                          }}
                        />
                      </Form.Item>
                    </Card>
                  </Col>

                  <Col span={12}>
                    <Card title="优化模块" size="small" style={{ marginBottom: 16 }}>
                      <Form.Item
                        name="enable_intent_learning"
                        label="启用意图学习"
                        valuePropName="checked"
                      >
                        <Switch />
                      </Form.Item>

                      <Form.Item
                        name="enable_response_optimization"
                        label="启用回复优化"
                        valuePropName="checked"
                      >
                        <Switch />
                      </Form.Item>

                      <Form.Item
                        name="enable_performance_tuning"
                        label="启用性能调优"
                        valuePropName="checked"
                      >
                        <Switch />
                      </Form.Item>
                    </Card>
                  </Col>
                </Row>

                <Form.Item>
                  <Space>
                    <Button type="primary" htmlType="submit">
                      保存配置
                    </Button>
                    <Button onClick={() => configForm.resetFields()}>
                      重置
                    </Button>
                  </Space>
                </Form.Item>
              </Form>
            </Card>
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default LearningOptimization;
