import api from '../httpClient';
import { getProjectId } from '../httpClient';

/**
 * 通知API
 */
const notificationsApi = {
  /**
   * 获取通知列表
   * @param {Object} params - 查询参数
   * @returns {Promise} - 返回通知列表
   */
  getNotifications: async (params = {}) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.get(`/project/${projectId}/notifications`, { params });
  },

  /**
   * 获取未读通知数量
   * @param {Object} params - 查询参数
   * @returns {Promise} - 返回未读通知数量
   */
  getUnreadCount: async (params = {}) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.get(`/project/${projectId}/notifications/unread-count`, { params });
  },

  /**
   * 标记通知为已读
   * @param {string} id - 通知ID
   * @returns {Promise} - 返回标记结果
   */
  markAsRead: async (id) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.put(`/project/${projectId}/notifications/${id}/read`);
  },

  /**
   * 标记所有通知为已读
   * @returns {Promise} - 返回标记结果
   */
  markAllAsRead: async () => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.put(`/project/${projectId}/notifications/read-all`);
  },

  /**
   * 删除通知
   * @param {string} id - 通知ID
   * @returns {Promise} - 返回删除结果
   */
  deleteNotification: async (id) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.delete(`/project/${projectId}/notifications/${id}`);
  },

  /**
   * 删除所有通知
   * @returns {Promise} - 返回删除结果
   */
  deleteAllNotifications: async () => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.delete(`/project/${projectId}/notifications/delete-all`);
  },

  /**
   * 创建通知
   * @param {Object} data - 通知数据
   * @returns {Promise} - 返回创建结果
   */
  createNotification: async (data) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.post(`/project/${projectId}/notifications`, data);
  },

  /**
   * 获取通知设置
   * @returns {Promise} - 返回通知设置
   */
  getSettings: async () => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.get(`/project/${projectId}/notifications/settings`);
  },

  /**
   * 更新通知设置
   * @param {Object} data - 通知设置数据
   * @returns {Promise} - 返回更新结果
   */
  updateSettings: async (data) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.put(`/project/${projectId}/notifications/settings`, data);
  },

  /**
   * 获取工作流通知设置
   * @param {string} workflowType - 工作流类型
   * @returns {Promise} - 返回工作流通知设置
   */
  getWorkflowSettings: async (workflowType) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.get(`/project/${projectId}/notifications/settings/workflow/${workflowType}`);
  },

  /**
   * 更新工作流通知设置
   * @param {string} workflowType - 工作流类型
   * @param {Object} data - 工作流通知设置数据
   * @returns {Promise} - 返回更新结果
   */
  updateWorkflowSettings: async (workflowType, data) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.put(`/project/${projectId}/notifications/settings/workflow/${workflowType}`, data);
  }
};

export default notificationsApi;
