<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>钉钉插件图标转换</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .preview {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            text-align: center;
        }
        .icon-sizes {
            display: flex;
            gap: 20px;
            justify-content: center;
            align-items: center;
            flex-wrap: wrap;
        }
        .icon-size {
            text-align: center;
        }
        .icon-size img {
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px;
        }
        button:hover {
            background: #0050b3;
        }
    </style>
</head>
<body>
    <h1>钉钉插件图标</h1>
    
    <div class="preview">
        <h2>图标预览</h2>
        <div class="icon-sizes">
            <div class="icon-size">
                <div>16x16</div>
                <img src="icon.svg" width="16" height="16" alt="16x16">
            </div>
            <div class="icon-size">
                <div>32x32</div>
                <img src="icon.svg" width="32" height="32" alt="32x32">
            </div>
            <div class="icon-size">
                <div>64x64</div>
                <img src="icon.svg" width="64" height="64" alt="64x64">
            </div>
            <div class="icon-size">
                <div>128x128</div>
                <img src="icon.svg" width="128" height="128" alt="128x128">
            </div>
        </div>
    </div>

    <div class="preview">
        <h2>转换为PNG</h2>
        <p>点击下面的按钮将SVG转换为PNG格式：</p>
        <button onclick="convertToPNG(64)">生成 64x64 PNG</button>
        <button onclick="convertToPNG(128)">生成 128x128 PNG</button>
        <button onclick="convertToPNG(256)">生成 256x256 PNG</button>
        
        <canvas id="canvas" style="display: none;"></canvas>
        <div id="download-links"></div>
    </div>

    <script>
        function convertToPNG(size) {
            const canvas = document.getElementById('canvas');
            const ctx = canvas.getContext('2d');
            canvas.width = size;
            canvas.height = size;

            const img = new Image();
            img.onload = function() {
                ctx.clearRect(0, 0, size, size);
                ctx.drawImage(img, 0, 0, size, size);
                
                canvas.toBlob(function(blob) {
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `dingtalk-icon-${size}x${size}.png`;
                    a.textContent = `下载 ${size}x${size} PNG`;
                    a.style.display = 'block';
                    a.style.margin = '10px 0';
                    
                    const linksDiv = document.getElementById('download-links');
                    linksDiv.appendChild(a);
                    
                    // 自动点击下载
                    a.click();
                }, 'image/png');
            };
            
            // 将SVG转换为Data URL
            const svgData = `<?xml version="1.0" encoding="UTF-8"?>
<svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
  <circle cx="32" cy="32" r="30" fill="#1890FF" stroke="#0050B3" stroke-width="2"/>
  <g transform="translate(16, 16)">
    <circle cx="16" cy="16" r="14" fill="none" stroke="white" stroke-width="2"/>
    <path d="M16 6 L16 26 M10 12 L22 12 M12 20 L20 20" stroke="white" stroke-width="2.5" stroke-linecap="round"/>
    <circle cx="24" cy="8" r="3" fill="#52C41A"/>
    <text x="24" y="10" text-anchor="middle" fill="white" font-size="4" font-weight="bold">AI</text>
  </g>
  <circle cx="48" cy="16" r="2" fill="white" opacity="0.8"/>
  <circle cx="16" cy="48" r="1.5" fill="white" opacity="0.6"/>
  <circle cx="52" cy="52" r="1" fill="white" opacity="0.4"/>
</svg>`;
            
            const svgBlob = new Blob([svgData], {type: 'image/svg+xml'});
            const svgUrl = URL.createObjectURL(svgBlob);
            img.src = svgUrl;
        }
    </script>
</body>
</html> 