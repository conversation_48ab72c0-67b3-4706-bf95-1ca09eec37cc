import React, { useState, useEffect } from 'react';
import {
  Table, Button, Space, Modal, Form, Input, Select,
  Popconfirm, message, Tabs, Tag, Row, Col,
  Radio, TimePicker, Switch, Divider
} from 'antd';
import {
  PlusOutlined, EditOutlined, DeleteOutlined,
  InboxOutlined, SettingOutlined,
  SaveOutlined, ReloadOutlined
} from '@ant-design/icons';
import dayjs from "dayjs";
import { project } from '../../../../services';
import apiService from '../../../../services/api';

const { Option } = Select;

/**
 * 仓储管理组件
 * 用于管理仓库基本信息和仓库列表
 */
const WarehouseManagement = () => {
  const [activeTab, setActiveTab] = useState('basic');
  const [warehouseModalOpen, setWarehouseModalOpen] = useState(false);
  const [editingWarehouse, setEditingWarehouse] = useState(null);
  const [warehouseForm] = Form.useForm();
  const [basicForm] = Form.useForm();
  const [loading, setLoading] = useState(false);

  // 仓库数据
  const [warehouses, setWarehouses] = useState([]);
  const [basicSettings, setBasicSettings] = useState({});

  // 初始化数据
  useEffect(() => {
    fetchWarehouses();
    fetchBasicSettings();
  }, []);

  // 获取仓库数据
  const fetchWarehouses = async () => {
    setLoading(true);

    try {
      // 调用API获取仓库列表
      const response = await project.warehouse.getList();
      if (response && response.items) {
        setWarehouses(response.items);
      } else {
        message.error('获取仓库列表失败');
      }
    } catch (error) {
      console.error('获取仓库列表失败:', error);
      message.error('获取仓库列表失败: ' + (error.message || '未知错误'));
      setWarehouses([]);
    } finally {
      setLoading(false);
    }
  };

  // 获取基本设置数据
  const fetchBasicSettings = async () => {
    // 模拟API调用
    setTimeout(() => {
      const mockSettings = {
        warehouse_mode: 'multi',
        default_business_hours_start: '09:00',
        default_business_hours_end: '18:00',
        enable_front_warehouse: true,
        enable_cross_warehouse_transfer: true,
        inventory_warning_threshold: 10,
        auto_replenishment: true,
        default_capacity_unit: 'sqm'
      };

      setBasicSettings(mockSettings);
      basicForm.setFieldsValue({
        ...mockSettings,
        default_business_hours: [
          dayjs(mockSettings.default_business_hours_start, 'HH:mm'),
          dayjs(mockSettings.default_business_hours_end, 'HH:mm')
        ]
      });
    }, 500);
  };

  // 仓库表格列配置
  const warehouseColumns = [
    {
      title: '仓库名称',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <Space>
          <InboxOutlined />
          <span>{text}</span>
          {record.status === 'inactive' && (
            <Tag color="red">已停用</Tag>
          )}
        </Space>
      )
    },
    {
      title: '仓库编码',
      dataIndex: 'code',
      key: 'code'
    },
    {
      title: '仓库类型',
      dataIndex: 'type',
      key: 'type',
      filters: [
        { text: '总仓', value: 'central' },
        { text: '区域仓', value: 'regional' },
        { text: '配送中心', value: 'distribution' },
        { text: '前置仓', value: 'front' }
      ],
      onFilter: (value, record) => record.type === value,
      render: (type) => {
        let text = '';
        let color = '';

        switch (type) {
          case 'central':
            text = '总仓';
            color = 'blue';
            break;
          case 'regional':
            text = '区域仓';
            color = 'green';
            break;
          case 'distribution':
            text = '配送中心';
            color = 'orange';
            break;
          case 'front':
            text = '前置仓';
            color = 'purple';
            break;
          default:
            text = '未知';
            color = 'default';
        }

        return <Tag color={color}>{text}</Tag>;
      }
    },
    {
      title: '仓库地址',
      dataIndex: 'address',
      key: 'address',
      ellipsis: true
    },
    {
      title: '仓库容量',
      dataIndex: 'capacity',
      key: 'capacity',
      render: (capacity, record) => `${capacity} ${record.capacity_unit === 'sqm' ? '平方米' : '立方米'}`
    },
    {
      title: '仓库经理',
      dataIndex: 'manager',
      key: 'manager'
    },
    {
      title: '联系电话',
      dataIndex: 'phone',
      key: 'phone'
    },
    {
      title: '营业时间',
      dataIndex: 'business_hours',
      key: 'business_hours'
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Button
            type="text"
            icon={<EditOutlined />}
            onClick={() => showEditWarehouseModal(record)}
          />
          <Popconfirm
            title="确定要删除这个仓库吗？"
            onConfirm={() => handleDeleteWarehouse(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
            />
          </Popconfirm>
        </Space>
      )
    }
  ];

  // 用户列表数据
  const [users, setUsers] = useState([]);
  const [loadingUsers, setLoadingUsers] = useState(false);

  // 获取用户列表，只获取仓库管理角色的用户
  const fetchUsers = async () => {
    setLoadingUsers(true);
    try {
      // 使用专门的获取仓库管理员API
      const response = await apiService.project.warehouse.getManagers();
      console.log('获取仓库管理员列表响应:', response);
      
      // 处理API返回的数据结构
      if (response && Array.isArray(response)) {
        setUsers(response);
      } else if (response && response.data && Array.isArray(response.data)) {
        setUsers(response.data);
      } else {
        console.warn('获取仓库管理员列表返回格式异常:', response);
        setUsers([]);
      }
    } catch (error) {
      console.error('获取仓库管理员列表失败:', error);
      // 显示错误消息，让用户知道问题
      message.warning('获取仓库管理员列表失败，请检查是否已配置仓库管理员角色');
      setUsers([]);
    } finally {
      setLoadingUsers(false);
    }
  };

  // 显示创建仓库模态框
  const showCreateWarehouseModal = () => {
    setEditingWarehouse(null);
    warehouseForm.resetFields();

    // 获取用户列表
    fetchUsers();

    // 设置默认值
    warehouseForm.setFieldsValue({
      status: 'active',
      type: 'regional',
      capacity_unit: basicSettings.default_capacity_unit || 'sqm',
      business_hours: `${basicSettings.default_business_hours_start || '09:00'}-${basicSettings.default_business_hours_end || '18:00'}`
    });

    setWarehouseModalOpen(true);
  };

  // 显示编辑仓库模态框
  const showEditWarehouseModal = (warehouse) => {
    setEditingWarehouse(warehouse);

    // 获取用户列表
    fetchUsers();

    warehouseForm.setFieldsValue({
      name: warehouse.name,
      code: warehouse.code,
      address: warehouse.address,
      type: warehouse.type,
      manager: warehouse.manager,
      manager_id: warehouse.manager_id,
      phone: warehouse.phone,
      capacity: warehouse.capacity,
      capacity_unit: warehouse.capacity_unit,
      status: warehouse.status,
      business_hours: warehouse.business_hours
    });
    setWarehouseModalOpen(true);
  };

  // 处理仓库表单提交
  const handleWarehouseSubmit = async () => {
    try {
      const values = await warehouseForm.validateFields();
      setLoading(true);

      try {
        if (editingWarehouse) {
          // 更新仓库
          await project.warehouse.update(editingWarehouse.id, values);
          message.success('仓库更新成功');

          // 刷新仓库列表
          fetchWarehouses();
        } else {
          // 创建仓库
          await project.warehouse.create(values);
          message.success('仓库创建成功');

          // 刷新仓库列表
          fetchWarehouses();
        }

        setWarehouseModalOpen(false);
      } catch (error) {
        console.error('保存仓库失败:', error);
        message.error('保存仓库失败: ' + (error.message || '未知错误'));
      } finally {
        setLoading(false);
      }
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  // 处理删除仓库
  const handleDeleteWarehouse = async (id) => {
    setLoading(true);

    try {
      // 调用API删除仓库
      await project.warehouse.delete(id);
      message.success('仓库删除成功');

      // 刷新仓库列表
      fetchWarehouses();
    } catch (error) {
      console.error('删除仓库失败:', error);
      message.error('删除仓库失败: ' + (error.message || '未知错误'));
    } finally {
      setLoading(false);
    }
  };

  // 保存基本设置
  const saveBasicSettings = async () => {
    try {
      const values = await basicForm.validateFields();
      setLoading(true);

      // 处理时间范围
      const businessHours = values.default_business_hours;
      const formattedValues = {
        ...values,
        default_business_hours_start: businessHours[0].format('HH:mm'),
        default_business_hours_end: businessHours[1].format('HH:mm')
      };
      delete formattedValues.default_business_hours;

      // 模拟API调用
      setTimeout(() => {
        setBasicSettings(formattedValues);
        message.success('基本设置保存成功');
        setLoading(false);
      }, 500);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  // 渲染基本设置表单
  const renderBasicSettingsForm = () => {
    return (
      <Form
        form={basicForm}
        layout="vertical"
        className="settings-form"
      >
        <Row gutter={24}>
          <Col span={12}>
            <Form.Item
              name="warehouse_mode"
              label="仓库模式"
              initialValue="multi"
              rules={[{ required: true, message: '请选择仓库模式' }]}
            >
              <Radio.Group>
                <Radio value="central">总仓模式</Radio>
                <Radio value="multi">多仓模式</Radio>
                <Radio value="front">前置仓模式</Radio>
              </Radio.Group>
            </Form.Item>
          </Col>

          <Col span={12}>
            <Form.Item
              name="default_business_hours"
              label="默认营业时间"
              rules={[{ required: true, message: '请设置默认营业时间' }]}
            >
              <TimePicker.RangePicker format="HH:mm" />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={24}>
          <Col span={12}>
            <Form.Item
              name="enable_front_warehouse"
              label="启用前置仓"
              valuePropName="checked"
              initialValue={true}
            >
              <Switch />
            </Form.Item>
          </Col>

          <Col span={12}>
            <Form.Item
              name="enable_cross_warehouse_transfer"
              label="启用跨仓调拨"
              valuePropName="checked"
              initialValue={true}
            >
              <Switch />
            </Form.Item>
          </Col>
        </Row>

        <Row gutter={24}>
          <Col span={12}>
            <Form.Item
              name="inventory_warning_threshold"
              label="库存预警阈值(%)"
              initialValue={10}
              rules={[{ required: true, message: '请设置库存预警阈值' }]}
            >
              <Select>
                <Option value={5}>5%</Option>
                <Option value={10}>10%</Option>
                <Option value={15}>15%</Option>
                <Option value={20}>20%</Option>
                <Option value={30}>30%</Option>
              </Select>
            </Form.Item>
          </Col>

          <Col span={12}>
            <Form.Item
              name="auto_replenishment"
              label="自动补货"
              valuePropName="checked"
              initialValue={true}
            >
              <Switch />
            </Form.Item>
          </Col>
        </Row>

        <Form.Item
          name="default_capacity_unit"
          label="默认容量单位"
          initialValue="sqm"
        >
          <Radio.Group>
            <Radio value="sqm">平方米</Radio>
            <Radio value="cbm">立方米</Radio>
          </Radio.Group>
        </Form.Item>

        <Divider />

        <Form.Item>
          <Space>
            <Button
              type="primary"
              icon={<SaveOutlined />}
              onClick={saveBasicSettings}
              loading={loading}
            >
              保存设置
            </Button>
            <Button
              icon={<ReloadOutlined />}
              onClick={() => basicForm.resetFields()}
            >
              重置
            </Button>
          </Space>
        </Form.Item>
      </Form>
    );
  };

  // 定义标签页内容
  const tabItems = [
    {
      key: 'basic',
      label: (
        <span>
          <SettingOutlined />
          基本设置
        </span>
      ),
      children: renderBasicSettingsForm()
    },
    {
      key: 'warehouses',
      label: (
        <span>
          <InboxOutlined />
          仓库管理
        </span>
      ),
      children: (
        <>
          <div style={{ marginBottom: 16 }}>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={showCreateWarehouseModal}
            >
              新增仓库
            </Button>
          </div>
          <Table
            columns={warehouseColumns}
            dataSource={warehouses}
            rowKey="id"
            loading={loading}
            pagination={{ pageSize: 10 }}
          />
        </>
      )
    }
  ];

  return (
    <div className="warehouse-management">
      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        items={tabItems}
      />

      {/* 仓库表单模态框 */}
      <Modal
        title={editingWarehouse ? '编辑仓库' : '新增仓库'}
        open={warehouseModalOpen}
        onOk={handleWarehouseSubmit}
        onCancel={() => setWarehouseModalOpen(false)}
        confirmLoading={loading}
        width={700}
      >
        <Form
          form={warehouseForm}
          layout="vertical"
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="仓库名称"
                rules={[{ required: true, message: '请输入仓库名称' }]}
              >
                <Input placeholder="请输入仓库名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="code"
                label="仓库编码"
                rules={[{ required: true, message: '请输入仓库编码' }]}
              >
                <Input placeholder="请输入仓库编码" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="type"
                label="仓库类型"
                rules={[{ required: true, message: '请选择仓库类型' }]}
              >
                <Select placeholder="请选择仓库类型">
                  <Option value="central">总仓</Option>
                  <Option value="regional">区域仓</Option>
                  <Option value="distribution">配送中心</Option>
                  <Option value="front">前置仓</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="business_hours"
                label="营业时间"
                rules={[{ required: true, message: '请输入营业时间' }]}
              >
                <Input placeholder="请输入营业时间，如09:00-18:00" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="address"
            label="仓库地址"
            rules={[{ required: true, message: '请输入仓库地址' }]}
          >
            <Input placeholder="请输入仓库地址" />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="capacity"
                label="仓库容量"
                rules={[{ required: true, message: '请输入仓库容量' }]}
              >
                <Input type="number" placeholder="请输入仓库容量" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="capacity_unit"
                label="容量单位"
                rules={[{ required: true, message: '请选择容量单位' }]}
              >
                <Select placeholder="请选择容量单位">
                  <Option value="sqm">平方米</Option>
                  <Option value="cbm">立方米</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="manager_id"
                label="仓库经理"
                rules={[{ required: true, message: '请选择仓库经理' }]}
              >
                <Select
                  placeholder="请选择仓库经理"
                  loading={loadingUsers}
                  showSearch
                  optionFilterProp="children"
                  filterOption={(input, option) =>
                    option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                  }
                  onChange={(value) => {
                    // 当选择仓库经理时，自动设置manager字段为对应的用户名
                    const selectedUser = users.find(user => {
                      // 支持user.project_user_id或user.user_id
                      return user.project_user_id === value || user.user_id === value;
                    });

                    if (selectedUser) {
                      warehouseForm.setFieldsValue({
                        manager: selectedUser.display_name || selectedUser.full_name || selectedUser.username
                      });
                    }
                  }}
                >
                  {users.map(user => {
                    // 显示用户显示名称，使用project_user_id作为值
                    const displayName = user.display_name || user.full_name || user.username;
                    const value = user.project_user_id || user.user_id;
                    return (
                      <Option key={value} value={value}>
                        {displayName}
                        {user.role_name && ` (${user.role_name})`}
                      </Option>
                    );
                  })}
                </Select>
              </Form.Item>
              <Form.Item
                name="manager"
                hidden
              >
                <Input />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="phone"
                label="联系电话"
                rules={[{ required: true, message: '请输入联系电话' }]}
              >
                <Input placeholder="请输入联系电话" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="status"
            label="仓库状态"
            initialValue="active"
          >
            <Select>
              <Option value="active">启用</Option>
              <Option value="inactive">停用</Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default WarehouseManagement;
