import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  Table,
  Tree,
  Space,
  Tag,
  Modal,
  Form,
  Input,
  Select,
  Switch,
  message,
  Descriptions,
  Tabs,
  Row,
  Col,
  Statistic,
  Progress,
  Alert,
  Tooltip,
  Popconfirm,
  Spin,
  Avatar,
  Radio,
  Divider
} from 'antd';
import {
  SyncOutlined,
  TeamOutlined,
  UserOutlined,
  SettingOutlined,
  InfoCircleOutlined,
  ReloadOutlined,
  EyeOutlined,
  BranchesOutlined,
  DownloadOutlined,
  UploadOutlined,
  UserAddOutlined,
  SendOutlined
} from '@ant-design/icons';
import dingtalkApi from '../service/dingtalkApi';

const { TabPane } = Tabs;
const { Option } = Select;
const { TextArea } = Input;

/**
 * 钉钉组织架构管理组件
 */
const OrganizationManagement = () => {
  const [loading, setLoading] = useState(false);
  const [syncModalVisible, setSyncModalVisible] = useState(false);
  const [statusData, setStatusData] = useState(null);
  const [departments, setDepartments] = useState([]);
  const [users, setUsers] = useState([]);
  const [treeData, setTreeData] = useState([]);
  const [selectedDeptId, setSelectedDeptId] = useState(null);
  const [activeTab, setActiveTab] = useState('structure');
  const [selectedDepartment, setSelectedDepartment] = useState(null);
  const [syncForm] = Form.useForm();
  const [statistics, setStatistics] = useState({});
  const [createUserModalVisible, setCreateUserModalVisible] = useState(false);
  const [createUserForm] = Form.useForm();
  const [selectedUsers, setSelectedUsers] = useState([]);
  const [projectRoles, setProjectRoles] = useState([]);
  const [projectStores, setProjectStores] = useState([]);



  // 获取项目角色列表
  const fetchProjectRoles = async () => {
    try {
      const response = await dingtalkApi.getProjectRoles();
      if (response.success) {
        setProjectRoles(response.data || []);
      }
    } catch (error) {
      console.error('获取项目角色失败:', error);
    }
  };

  // 获取项目门店列表
  const fetchProjectStores = async () => {
    try {
      const response = await dingtalkApi.getProjectStores();
      if (response.success) {
        setProjectStores(response.data || []);
      }
    } catch (error) {
      console.error('获取项目门店失败:', error);
    }
  };

  // 获取组织架构同步状态
  const fetchOrganizationStatus = async () => {
    try {
      setLoading(true);
      const response = await dingtalkApi.getOrganizationStatus();
      
      console.log('组织架构状态响应:', response); // 调试日志
      
      if (response.success) {
        setStatusData(response.data);
        
        // 根据实际返回结构解析数据
        const allDepartments = response.data.departments || [];
        const allUsers = response.data.users || [];
        const recentDepartments = response.data.recent_departments || [];
        const statistics = response.data.statistics || {};
        
        // 使用所有部门数据构建完整的组织架构树
        setDepartments(allDepartments);
        setUsers(allUsers);
        setStatistics(statistics);
        buildTreeData(allDepartments);
        
        console.log('解析的所有部门数据:', allDepartments);
        console.log('解析的所有用户数据:', allUsers);
        console.log('解析的最近部门数据:', recentDepartments);
        console.log('解析的统计数据:', statistics);
      } else {
        message.error('获取组织架构状态失败');
      }
    } catch (error) {
      console.error('获取组织架构状态失败:', error);
      message.error('获取组织架构状态失败');
    } finally {
      setLoading(false);
    }
  };

  // 处理组织架构同步
  const handleSyncOrganization = async (values) => {
    try {
      setLoading(true);
      
      // 构建同步选项
      const syncOptions = {
        // 同步模式
        sync_mode: values.sync_mode || 'full', // full: 全量同步, incremental: 增量同步, department: 按部门同步
        
        // 同步策略
        sync_inactive_users: values.sync_inactive_users === true,
        force_update: values.force_update === true, // 增量同步时是否强制更新
        
        // 按部门同步选项
        target_dept_ids: values.target_dept_ids || [],
        include_sub_depts: values.include_sub_depts !== false,
        
        // 增量同步选项
        last_sync_time: values.last_sync_time || null, // 上次同步时间，用于增量同步
        sync_time_window: values.sync_time_window || 7, // 同步时间窗口（天）
      };
      
      console.log('执行组织架构同步，参数:', syncOptions);
      
      // 统一使用增强同步API
      const response = await dingtalkApi.syncOrganization(syncOptions);
      
      console.log('组织架构同步响应:', response);
      
      if (response && response.success) {
        const data = response.data || {};
        
        // 显示同步结果
        const {
          departments_synced = 0,
          users_synced = 0,
          sync_mode = 'full'
        } = data;
        
        let successMessage = '';
        if (sync_mode === 'incremental') {
          successMessage = `增量同步完成！
更新时间窗口：${syncOptions.sync_time_window}天
部门更新：${departments_synced}个
用户更新：${users_synced}个`;
        } else if (sync_mode === 'department') {
          const deptNames = values.target_dept_names || [];
          successMessage = `部门同步完成！
目标部门：${deptNames.length > 0 ? deptNames.join(', ') : '已选择部门'}
部门同步：${departments_synced}个
用户同步：${users_synced}个`;
        } else {
          successMessage = `全量同步完成！
部门同步：${departments_synced}个
用户同步：${users_synced}个`;
        }
        
        Modal.success({
          title: '同步完成',
          content: <pre style={{ whiteSpace: 'pre-line' }}>{successMessage}</pre>,
          width: 500
        });
        
        setSyncModalVisible(false);
        syncForm.resetFields();
        await fetchOrganizationStatus();
      } else {
        const errorMsg = response?.message || '同步失败';
        message.error(`组织架构同步失败: ${errorMsg}`);
      }
    } catch (error) {
      console.error('同步组织架构失败:', error);
      message.error(`同步组织架构失败: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  // 查看部门详情
  const viewDepartmentDetails = (department) => {
    setSelectedDepartment(department);
  };

  const handleViewPassword = (record) => {
    Modal.info({
      title: '用户凭证查看',
      content: (
        <div>
          <p>请妥善保管以下信息，仅用于首次登录。</p>
          <Descriptions bordered column={1}>
            <Descriptions.Item label="系统账号">{record.username}</Descriptions.Item>
            <Descriptions.Item label="初始密码">{record.initial_password}</Descriptions.Item>
          </Descriptions>
        </div>
      ),
      okText: '好的',
    });
  };

  const handleResendNotification = async (record, role, store) => {
    message.loading({ content: '正在重发通知...', key: 'resend' });
    try {
        const resendData = {
            dingtalk_user_id: record.dingtalk_user_id,
            username: record.username,
            initial_password: record.initial_password,
            role_name: role ? role.name : 'N/A',
            store_name: store ? store.name : null,
        };
        await dingtalkApi.resendNotification(resendData);
        message.success({ content: `用户 ${record.username} 的通知已成功重发`, key: 'resend', duration: 2 });
    } catch (error) {
        message.error({ content: `重发失败: ${error.message}`, key: 'resend', duration: 2 });
    }
  };

  // 处理一键创建用户
  const handleCreateUsers = async (values) => {
    try {
      setLoading(true);
      
      const selectedRole = projectRoles.find(role => role.id === values.role_id);
      if (!selectedRole) {
        message.error('选择的角色不存在，请刷新后重试');
        setLoading(false);
        return;
      }
      const selectedStore = projectStores.find(store => store.id === values.store_id);

      const requestData = {
        dingtalk_user_ids: selectedUsers,
        role_id: selectedRole.id,
        store_id: values.store_id || null
      };

      const response = await dingtalkApi.createProjectUsers(requestData);
      
      setCreateUserModalVisible(false);
      createUserForm.resetFields();

      if (response.success) {
        const resultDetails = response.data.details || [];
        const { created_count, skipped_count, failed_count } = response.data;

        const resultColumns = [
            { title: '钉钉用户名', dataIndex: 'dingtalk_name', key: 'dingtalk_name', render: (text, record) => record.username },
            { title: '系统账号', dataIndex: 'username', key: 'username' },
            { 
              title: '状态', 
              dataIndex: 'status', 
              key: 'status',
              render: (status, record) => {
                if (status === 'created') return <Tag color="success">创建成功</Tag>;
                if (status === 'skipped') return <Tag color="default">已跳过</Tag>;
                return <Tag color="error" title={record.reason}>创建失败</Tag>;
              }
            },
            {
              title: '通知',
              dataIndex: 'notification_status',
              key: 'notification_status',
              render: (status, record) => {
                  if (record.status !== 'created') return 'N/A';
                  if (status === 'sent_success') return <Tag color="success">发送成功</Tag>;
                  return <Tag color="warning" title={record.notification_error}>发送失败</Tag>;
              }
            },
            {
              title: '操作',
              key: 'action',
              render: (text, record) => {
                if (record.status === 'created') {
                  return (
                    <Space>
                        <Button size="small" icon={<EyeOutlined />} onClick={() => handleViewPassword(record)}>查看凭证</Button>
                        <Button size="small" icon={<SendOutlined />} onClick={() => handleResendNotification(record, selectedRole, selectedStore)}>重发通知</Button>
                    </Space>
                  );
                }
                return null;
              }
            }
        ];

        Modal.info({
            title: '用户创建结果',
            width: 800,
            content: (
                <div>
                    <p>{`操作完成：成功 ${created_count}，跳过 ${skipped_count}，失败 ${failed_count}`}</p>
                    <Table
                        columns={resultColumns}
                        dataSource={resultDetails}
                        rowKey="dingtalk_user_id"
                        pagination={false}
                        size="small"
                    />
                </div>
            ),
            okText: '关闭'
        });

        fetchOrganizationStatus(); // 刷新状态以更新已关联的用户
      } else {
        message.error(`创建用户失败: ${response.message || '未知错误'}`);
      }
    } catch (error) {
      message.error(`创建用户操作失败: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  // 开始一键创建流程
  const startCreateUsers = (users) => {
    const unboundUsers = users.filter(user => !user.user_id);
    if (unboundUsers.length === 0) {
      message.info('选中的用户都已绑定项目账号');
      return;
    }
    
    setSelectedUsers(unboundUsers.map(user => user.dingtalk_user_id));
    setCreateUserModalVisible(true);
  };

  // 构建部门树数据
  const buildTreeData = (departments) => {
    // 构建部门树结构
    const deptMap = {};
    const rootNodes = [];

    // 创建映射表 - 使用dept_id作为键
    departments.forEach(dept => {
      const deptKey = dept.dept_id || dept.id;
      deptMap[deptKey] = {
        key: dept.id,
        title: (
          <Space>
            <TeamOutlined />
            <span>{dept.name}</span>
            <Tag color={dept.dept_id ? 'green' : 'default'} size="small">
              {dept.dept_id ? '已同步' : '未同步'}
            </Tag>
          </Space>
        ),
        children: [],
        ...dept
      };
    });

    // 构建树结构 - 使用parent_id查找父部门
    departments.forEach(dept => {
      const deptKey = dept.dept_id || dept.id;
      const parentKey = dept.parent_id;
      
      if (parentKey && deptMap[parentKey] && parentKey !== deptKey) {
        // 父部门存在，且不是自己
        if (!deptMap[parentKey].children) {
          deptMap[parentKey].children = [];
        }
        deptMap[parentKey].children.push(deptMap[deptKey]);
      } else {
        // 根部门
        rootNodes.push(deptMap[deptKey]);
      }
    });

    console.log('构建的树结构:', rootNodes); // 调试日志
    setTreeData(rootNodes);
  };

  // 部门列表列配置
  const departmentColumns = [
    {
      title: '部门名称',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <Space>
          <TeamOutlined />
          <span>{text}</span>
        </Space>
      ),
    },
    {
      title: '部门描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },
    {
      title: '上级部门',
      dataIndex: 'parent_name',
      key: 'parent_name',
      render: (text) => text || '根部门',
    },
    {
      title: '钉钉部门ID',
      dataIndex: 'dingtalk_dept_id',
      key: 'dingtalk_dept_id',
      render: (id) => id || '-',
    },
    {
      title: '同步状态',
      dataIndex: 'dingtalk_dept_id',
      key: 'sync_status',
      render: (id) => (
        <Tag color={id ? 'green' : 'default'}>
          {id ? '已同步' : '未同步'}
        </Tag>
      ),
    },
    {
      title: '成员数量',
      dataIndex: 'member_count',
      key: 'member_count',
      render: (count, record) => {
        const deptId = record.dept_id || record.dingtalk_dept_id;
        const deptUsers = users.filter(user => 
          user.dingtalk_dept_id === deptId || user.department_id === deptId
        );
        return (
          <Tag color="blue">
            {deptUsers.length} 人
          </Tag>
        );
      },
    }
  ];

  // 用户映射列配置
  const userMappingColumns = [
    {
      title: '钉钉姓名',
      dataIndex: 'dingtalk_name',
      key: 'dingtalk_name',
      render: (text, record) => (
        <Space>
          <Avatar size="small" icon={<UserOutlined />} />
          <span>{text}</span>
          {record.user_id && <Tag color="green" size="small">已绑定</Tag>}
          {!record.user_id && <Tag color="orange" size="small">未绑定</Tag>}
        </Space>
      ),
    },
    {
      title: '所属部门',
      dataIndex: 'dingtalk_dept_name',
      key: 'dingtalk_dept_name',
    },
    {
      title: '职位',
      dataIndex: 'dingtalk_position',
      key: 'dingtalk_position',
      render: (title) => title ? <Tag>{title}</Tag> : '-',
    },
    {
      title: '联系方式',
      key: 'contact',
      render: (_, record) => (
        <div>
          {record.dingtalk_mobile && <div>📱 {record.dingtalk_mobile}</div>}
          {record.dingtalk_email && <div>📧 {record.dingtalk_email}</div>}
        </div>
      ),
    },
    {
      title: '同步时间',
      dataIndex: 'updated_at',
      key: 'updated_at',
      render: (time) => time ? new Date(time).toLocaleString() : '-',
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space>
          {!record.user_id && (
            <Button
              type="primary"
              size="small"
              onClick={() => startCreateUsers([record])}
              disabled={loading}
            >
              一键创建
            </Button>
          )}
          {record.user_id && (
            <Tag color="green">已创建</Tag>
          )}
        </Space>
      ),
    },
  ];

  useEffect(() => {
    fetchOrganizationStatus();
    fetchProjectRoles();
    fetchProjectStores();
  }, []);

  return (
    <div className="organization-management">
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab="组织架构" key="structure">
          <Row gutter={16}>
            <Col span={8}>
              <Card
                title="部门树"
                extra={
                  <Space>
                    <Tooltip title="刷新">
                      <Button
                        icon={<ReloadOutlined />}
                        onClick={fetchOrganizationStatus}
                        loading={loading}
                      />
                    </Tooltip>
                    <Button
                      type="primary"
                      icon={<SyncOutlined />}
                      onClick={() => setSyncModalVisible(true)}
                    >
                      同步
                    </Button>
                  </Space>
                }
              >
                <Spin spinning={loading}>
                  <Tree
                    treeData={treeData}
                    onSelect={(selectedKeys) => {
                      if (selectedKeys.length > 0) {
                        setSelectedDeptId(selectedKeys[0]);
                      } else {
                        setSelectedDeptId(null);
                      }
                    }}
                    defaultExpandAll
                    showLine={{ showLeafIcon: false }}
                  />
                </Spin>
              </Card>
            </Col>
            <Col span={16}>
              <Card
                title={`部门成员${selectedDeptId ? ` (${departments.find(d => d.id === selectedDeptId)?.name || '未知部门'})` : ''}`}
                extra={
                  <Space>
                    <span>共 {users.filter(u => {
                      const selectedDept = departments.find(d => d.id === selectedDeptId);
                      if (!selectedDept) return false;
                      const deptId = selectedDept.dept_id || selectedDept.dingtalk_dept_id;
                      return u.dingtalk_dept_id === deptId || u.department_id === deptId;
                    }).length} 人</span>
                  </Space>
                }
              >
                <Table
                  columns={userMappingColumns}
                  dataSource={users.filter(u => {
                    // 查找选中的部门
                    const selectedDept = departments.find(d => d.id === selectedDeptId);
                    if (!selectedDept) return false;
                    
                    // 使用钉钉部门ID进行匹配
                    const deptId = selectedDept.dept_id || selectedDept.dingtalk_dept_id;
                    return u.dingtalk_dept_id === deptId || u.department_id === deptId;
                  })}
                  rowKey="id"
                  loading={loading}
                  pagination={{
                    pageSize: 10,
                    showSizeChanger: true,
                    showTotal: (total) => `共 ${total} 条记录`,
                  }}
                />
              </Card>
            </Col>
          </Row>
        </TabPane>

        <TabPane tab="部门管理" key="departments">
          <Card
            title="部门列表"
            extra={
              <Space>
                <Button
                  icon={<ReloadOutlined />}
                  onClick={fetchOrganizationStatus}
                  loading={loading}
                >
                  刷新
                </Button>
                <Button
                  type="primary"
                  icon={<SyncOutlined />}
                  onClick={() => setSyncModalVisible(true)}
                >
                  同步钉钉
                </Button>
              </Space>
            }
          >
            <Table
              columns={departmentColumns}
              dataSource={departments}
              rowKey="id"
              loading={loading}
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showTotal: (total) => `共 ${total} 条记录`,
              }}
            />
          </Card>
        </TabPane>

        <TabPane tab="用户管理" key="users">
          <Card
            title="用户列表"
            extra={
              <Space>
                <Button
                  icon={<ReloadOutlined />}
                  onClick={fetchOrganizationStatus}
                  loading={loading}
                >
                  刷新
                </Button>
                <Button
                  type="primary"
                  icon={<SyncOutlined />}
                  onClick={() => setSyncModalVisible(true)}
                >
                  同步钉钉
                </Button>
              </Space>
            }
          >
            <Table
              columns={userMappingColumns}
              dataSource={users}
              rowKey="id"
              loading={loading}
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `共 ${total} 条记录`,
              }}
            />
          </Card>
        </TabPane>

        <TabPane tab="统计概览" key="statistics">
          <Row gutter={[16, 16]}>
            <Col span={6}>
              <Card>
                <Statistic
                  title="总部门数"
                  value={statusData?.statistics?.department_count || 0}
                  prefix={<TeamOutlined />}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title="总用户数"
                  value={statusData?.statistics?.user_mapping_count || 0}
                  prefix={<UserOutlined />}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title="活跃用户"
                  value={statusData?.statistics?.active_user_count || 0}
                  prefix={<UserOutlined />}
                  valueStyle={{ color: '#3f8600' }}
                />
              </Card>
            </Col>
            <Col span={6}>
              <Card>
                <Statistic
                  title="已同步部门"
                  value={statusData?.statistics?.synced_department_count || 0}
                  prefix={<BranchesOutlined />}
                  valueStyle={{ color: '#1890ff' }}
                />
              </Card>
            </Col>
          </Row>

          <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
            <Col span={12}>
              <Card title="部门同步率">
                <div style={{ textAlign: 'center' }}>
                  <div style={{ fontSize: '2em', color: '#1890ff' }}>
                    {statusData?.statistics?.department_sync_rate ? Math.round(statusData.statistics.department_sync_rate * 100) : 0}%
                  </div>
                  <div>已同步 {statusData?.statistics?.synced_department_count || 0} / {statusData?.statistics?.department_count || 0} 个部门</div>
                </div>
              </Card>
            </Col>
            <Col span={12}>
              <Card title="用户同步率">
                <div style={{ textAlign: 'center' }}>
                  <div style={{ fontSize: '2em', color: '#52c41a' }}>
                    {statusData?.statistics?.user_sync_rate ? Math.round(statusData.statistics.user_sync_rate * 100) : 0}%
                  </div>
                  <div>已同步 {statusData?.statistics?.synced_user_count || 0} / {statusData?.statistics?.user_mapping_count || 0} 个用户</div>
                </div>
              </Card>
            </Col>
          </Row>
        </TabPane>
      </Tabs>

      {/* 增强组织架构同步模态框 */}
      <Modal
        title="同步钉钉组织架构"
        visible={syncModalVisible}
        onOk={() => syncForm.submit()}
        onCancel={() => setSyncModalVisible(false)}
        confirmLoading={loading}
        width={700}
      >
        <Form
          form={syncForm}
          layout="vertical"
          onFinish={handleSyncOrganization}
        >
          <Alert
            message="增强同步说明"
            description="智能同步钉钉组织架构，支持增量同步、按部门同步等高级功能。"
            type="info"
            style={{ marginBottom: 16 }}
          />

          {/* 同步模式选择 */}
          <Form.Item
            name="sync_mode"
            label="同步模式"
            initialValue="full"
            rules={[{ required: true, message: '请选择同步模式' }]}
          >
            <Radio.Group>
              <Radio value="full">
                <Space direction="vertical" size={0}>
                  <strong>全量同步</strong>
                  <span style={{ color: '#666', fontSize: '12px' }}>同步所有部门和用户信息</span>
                </Space>
              </Radio>
              <Radio value="incremental">
                <Space direction="vertical" size={0}>
                  <strong>增量同步</strong>
                  <span style={{ color: '#666', fontSize: '12px' }}>只同步指定时间内有变更的数据</span>
                </Space>
              </Radio>
              <Radio value="department">
                <Space direction="vertical" size={0}>
                  <strong>按部门同步</strong>
                  <span style={{ color: '#666', fontSize: '12px' }}>指定特定部门进行同步</span>
                </Space>
              </Radio>
            </Radio.Group>
          </Form.Item>

          {/* 增量同步选项 */}
          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) => {
              return prevValues.sync_mode !== currentValues.sync_mode;
            }}
          >
            {({ getFieldValue }) => {
              const syncMode = getFieldValue('sync_mode');
              
              if (syncMode === 'incremental') {
                return (
                  <>
                    <Form.Item
                      name="sync_time_window"
                      label="同步时间窗口（天）"
                      initialValue={7}
                      rules={[{ required: true, message: '请输入同步时间窗口' }]}
                    >
                      <Select>
                        <Option value={1}>最近1天</Option>
                        <Option value={3}>最近3天</Option>
                        <Option value={7}>最近7天</Option>
                        <Option value={15}>最近15天</Option>
                        <Option value={30}>最近30天</Option>
                      </Select>
                    </Form.Item>

                    <Form.Item
                      name="force_update"
                      label="强制更新"
                      valuePropName="checked"
                      initialValue={false}
                    >
                      <Switch 
                        checkedChildren="强制" 
                        unCheckedChildren="智能" 
                      />
                    </Form.Item>
                  </>
                );
              }

              if (syncMode === 'department') {
                return (
                  <>
                    <Form.Item
                      name="target_dept_ids"
                      label="选择部门"
                      rules={[{ required: true, message: '请选择要同步的部门' }]}
                    >
                      <Select
                        mode="multiple"
                        placeholder="选择要同步的部门"
                        style={{ width: '100%' }}
                        showSearch
                        filterOption={(input, option) => {
                          const children = option.children || option.props?.children || '';
                          const searchText = typeof children === 'string' ? children : '';
                          return searchText.toLowerCase().includes(input.toLowerCase());
                        }}
                        onChange={(values, options) => {
                          // 保存部门名称，用于结果显示
                          const names = options.map(option => {
                            const children = option.children || option.props?.children || '';
                            const displayText = typeof children === 'string' ? children : '';
                            return displayText.split(' (')[0];
                          });
                          syncForm.setFieldsValue({ target_dept_names: names });
                        }}
                      >
                        {departments.map(dept => (
                          <Option key={`dept-${dept.dept_id || dept.id}`} value={dept.dept_id || dept.id}>
                            {dept.name} {dept.dept_path && `(${dept.dept_path})`}
                          </Option>
                        ))}
                      </Select>
                    </Form.Item>

                    <Form.Item
                      name="target_dept_names"
                      style={{ display: 'none' }}
                    >
                      <Input />
                    </Form.Item>

                    <Form.Item
                      name="include_sub_depts"
                      label="包含子部门"
                      valuePropName="checked"
                      initialValue={true}
                    >
                      <Switch checkedChildren="包含" unCheckedChildren="不包含" />
                    </Form.Item>
                  </>
                );
              }
              
              return null;
            }}
          </Form.Item>



          <Divider orientation="left">高级选项</Divider>

          <Form.Item
            name="sync_inactive_users"
            label="同步非活跃用户"
            valuePropName="checked"
            initialValue={false}
          >
            <Switch checkedChildren="同步" unCheckedChildren="跳过" />
          </Form.Item>
        </Form>
      </Modal>

      {/* 一键创建用户模态框 */}
      <Modal
        title="一键创建项目用户"
        visible={createUserModalVisible}
        onOk={() => createUserForm.submit()}
        onCancel={() => {
          setCreateUserModalVisible(false);
          createUserForm.resetFields();
          setSelectedUsers([]);
        }}
        confirmLoading={loading}
        width={600}
      >
        <Form
          form={createUserForm}
          layout="vertical"
          onFinish={handleCreateUsers}
        >
          <Alert
            message={`即将为 ${selectedUsers.length} 个钉钉用户创建项目账号`}
            description="系统将基于钉钉用户信息创建项目账号，用户需要自己登录系统后进行钉钉绑定授权"
            type="info"
            style={{ marginBottom: 16 }}
          />

          <Form.Item name="role_id" label="选择角色" rules={[{ required: true, message: '请必须为用户选择一个角色' }]}>
            <Select placeholder="选择一个项目角色">
              {projectRoles.map(role => (
                <Option key={role.id} value={role.id}>
                  <div>
                    <Space>
                      <span style={{ fontWeight: 500 }}>{role.name}</span>
                      {role.is_store_role && <Tag color="orange" size="small">门店角色</Tag>}
                    </Space>
                    <div style={{ fontSize: '12px', color: '#888', whiteSpace: 'normal' }}>
                      {role.description}
                    </div>
                  </div>
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) => {
              return prevValues.role_id !== currentValues.role_id;
            }}
          >
            {({ getFieldValue }) => {
              const roleId = getFieldValue('role_id');
              const selectedRole = projectRoles.find(r => r.id === roleId);
              return selectedRole?.is_store_role ? (
                <Form.Item name="store_id" label="选择门店" rules={[{ required: true, message: '门店角色必须关联一个门店' }]}>
                    <Select placeholder="为门店角色选择一个所属门店">
                    {projectStores.map(store => (
                        <Option key={store.id} value={store.id}>
                          <div>
                            <div style={{ fontWeight: 500 }}>{store.name}</div>
                            <div style={{ fontSize: '12px', color: '#888', whiteSpace: 'normal' }}>
                              {store.code ? `编码: ${store.code}` : ''} {store.address || ''}
                            </div>
                          </div>
                        </Option>
                    ))}
                    </Select>
                </Form.Item>
              ) : null;
            }}
          </Form.Item>

          <div style={{ marginTop: 16, padding: 12, background: '#f5f5f5', borderRadius: 4 }}>
            <div style={{ fontWeight: 'bold', marginBottom: 8 }}>创建说明：</div>
            <ul style={{ margin: 0, paddingLeft: 20, fontSize: '12px', color: '#666' }}>
              <li>用户名：自动根据手机号或邮箱生成</li>
              <li>初始密码：使用钉钉用户ID生成，创建后显示</li>
              <li>角色权限：根据选择的角色分配相应权限</li>
              <li>钉钉绑定：不自动绑定，用户需登录后自行授权绑定</li>
              <li>账号状态：创建后立即激活，用户可直接登录</li>
            </ul>
          </div>
        </Form>
      </Modal>

      {/* 部门详情模态框 */}
      <Modal
        title="部门详情"
        open={!!selectedDepartment}
        onCancel={() => setSelectedDepartment(null)}
        footer={[
          <Button key="close" onClick={() => setSelectedDepartment(null)}>
            关闭
          </Button>
        ]}
        width={600}
      >
        {selectedDepartment && (
          <Descriptions column={2} bordered>
            <Descriptions.Item label="部门名称" span={2}>
              {selectedDepartment.name}
            </Descriptions.Item>
            <Descriptions.Item label="钉钉部门ID">
              {selectedDepartment.dept_id}
            </Descriptions.Item>
            <Descriptions.Item label="部门层级">
              {selectedDepartment.level}
            </Descriptions.Item>
            <Descriptions.Item label="部门路径" span={2}>
              {selectedDepartment.dept_path || '-'}
            </Descriptions.Item>
            <Descriptions.Item label="成员数量">
              {selectedDepartment.member_count || 0}
            </Descriptions.Item>
            <Descriptions.Item label="更新时间">
              {selectedDepartment.updated_at ? 
                new Date(selectedDepartment.updated_at).toLocaleString() : 
                '-'
              }
            </Descriptions.Item>
          </Descriptions>
        )}
      </Modal>
    </div>
  );
};

export default OrganizationManagement; 