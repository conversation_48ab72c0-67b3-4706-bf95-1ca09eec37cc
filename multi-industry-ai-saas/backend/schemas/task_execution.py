#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Schemas for task execution results.
"""

from typing import Dict, Any, Optional
from pydantic import BaseModel, Field

class TaskExecutionResult(BaseModel):
    """
    Standardized result object for all task executors.
    """
    success: bool = Field(..., description="Indicates whether the task's business logic succeeded.")
    data: Optional[Dict[str, Any]] = Field(None, description="The output data of the task if successful, or an error dictionary if failed.")

class TaskResult(BaseModel):
    """
    Represents the final result data stored in the AsyncTask model.
    """
    status: str
    message: str
    result: Optional[Any] = None 