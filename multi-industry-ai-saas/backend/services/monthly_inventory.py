#!/usr/bin/env python
# -*- coding: utf-8 -*-

from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, func, extract, desc, asc, select
from typing import List, Optional, Dict, Any, Tuple
from datetime import date, datetime
from uuid import UUID
import pandas as pd
import os
from fastapi import HTTPException, UploadFile

from models.monthly_inventory import MonthlyInventory, MonthlyInventorySummary, MonthlyInventoryUpload
from models.product import Product, ProductCategory
from models.store import Store
from schemas.monthly_inventory import (
    MonthlyInventoryCreate, MonthlyInventoryUpdate, MonthlyInventoryBatchCreate,
    MonthlyInventoryQuery, MonthlyInventoryAuditQuery,
    MonthlyInventoryAudit, MonthlyInventoryBatchAudit,
    MonthlyInventoryStatistics, MonthlyInventoryAuditStatistics
)
from services.excel_service import ExcelService


class MonthlyInventoryService:
    """月度盘点服务"""

    def __init__(self, db: Session):
        self.db = db
        self.excel_service = ExcelService()

    async def get_monthly_inventory_list(
        self, 
        project_id: UUID, 
        query: MonthlyInventoryQuery
    ) -> Tuple[List[MonthlyInventory], int, MonthlyInventoryStatistics]:
        """获取月度盘点列表"""
        
        filters = [MonthlyInventory.project_id == project_id]
        
        if query.store_id:
            filters.append(MonthlyInventory.store_id == query.store_id)
        
        if query.month:
            try:
                month_date = datetime.strptime(query.month, '%Y-%m').date()
                filters.append(MonthlyInventory.month == month_date)
            except ValueError:
                pass
        
        if query.status:
            filters.append(MonthlyInventory.status == query.status)
        
        if query.category:
            filters.append(Product.category_id == query.category)

        # 基础查询 - 使用异步语法
        base_query = select(MonthlyInventory).join(
            Product, MonthlyInventory.product_id == Product.id
        ).join(
            Store, MonthlyInventory.store_id == Store.id
        ).filter(and_(*filters))

        # 获取总数
        count_query = select(func.count()).select_from(base_query.subquery())
        total_result = await self.db.execute(count_query)
        total = total_result.scalar()

        # 分页查询
        items_query = base_query.order_by(desc(MonthlyInventory.created_at)).offset(
            (query.page - 1) * query.page_size
        ).limit(query.page_size)
        
        items_result = await self.db.execute(items_query)
        items = items_result.scalars().all()

        # 统计数据
        statistics = await self._get_inventory_statistics(project_id, query.store_id, query.month)

        return items, total, statistics

    async def get_monthly_inventory_by_id(self, inventory_id: UUID, project_id: UUID) -> Optional[MonthlyInventory]:
        """根据ID获取月度盘点记录"""
        query = select(MonthlyInventory).filter(
            MonthlyInventory.id == inventory_id,
            MonthlyInventory.project_id == project_id
        )
        result = await self.db.execute(query)
        return result.scalars().first()

    async def create_monthly_inventory(
        self, 
        project_id: UUID, 
        tenant_id: Optional[UUID],
        data: MonthlyInventoryCreate, 
        user_id: UUID
    ) -> MonthlyInventory:
        """创建月度盘点记录"""
        
        # 检查是否已存在相同的盘点记录
        existing_query = select(MonthlyInventory).filter(
            MonthlyInventory.project_id == project_id,
            MonthlyInventory.store_id == data.store_id,
            MonthlyInventory.product_id == data.product_id,
            MonthlyInventory.month == data.month
        )
        existing_result = await self.db.execute(existing_query)
        existing = existing_result.scalars().first()
        
        if existing:
            raise HTTPException(status_code=400, detail="该商品在此月份已有盘点记录")

        # 创建盘点记录
        inventory = MonthlyInventory(
            tenant_id=tenant_id,
            project_id=project_id,
            store_id=data.store_id,
            product_id=data.product_id,
            month=data.month,
            inventory_quantity=data.inventory_quantity,
            actual_quantity=data.actual_quantity,
            unit=data.unit,
            remark=data.remark,
            created_by=user_id
        )
        
        self.db.add(inventory)
        await self.db.commit()
        await self.db.refresh(inventory)
        
        # 更新汇总数据
        await self._update_summary(project_id, data.store_id, data.month)
        
        return inventory

    async def batch_create_monthly_inventory(
        self, 
        project_id: UUID, 
        tenant_id: Optional[UUID],
        data: MonthlyInventoryBatchCreate, 
        user_id: UUID
    ) -> List[MonthlyInventory]:
        """批量创建月度盘点记录"""
        
        created_items = []
        
        for item_data in data.items:
            # 检查是否已存在
            existing_query = select(MonthlyInventory).filter(
                MonthlyInventory.project_id == project_id,
                MonthlyInventory.store_id == item_data.store_id,
                MonthlyInventory.product_id == item_data.product_id,
                MonthlyInventory.month == item_data.month
            )
            existing_result = await self.db.execute(existing_query)
            existing = existing_result.scalars().first()
            
            if existing:
                continue  # 跳过已存在的记录
            
            inventory = MonthlyInventory(
                tenant_id=tenant_id,
                project_id=project_id,
                store_id=item_data.store_id,
                product_id=item_data.product_id,
                month=item_data.month,
                inventory_quantity=item_data.inventory_quantity,
                actual_quantity=item_data.actual_quantity,
                unit=item_data.unit,
                remark=item_data.remark,
                created_by=user_id
            )
            
            self.db.add(inventory)
            created_items.append(inventory)
        
        await self.db.commit()
        
        # 更新汇总数据（按门店月份分组）
        store_months = set((item.store_id, item.month) for item in created_items)
        for store_id, month in store_months:
            await self._update_summary(project_id, store_id, month)
        
        return created_items

    def update_monthly_inventory(
        self, 
        inventory_id: UUID, 
        project_id: UUID,
        data: MonthlyInventoryUpdate, 
        user_id: UUID
    ) -> Optional[MonthlyInventory]:
        """更新月度盘点记录"""
        
        inventory = self.get_monthly_inventory_by_id(inventory_id, project_id)
        if not inventory:
            return None
        
        # 只有草稿状态才能编辑
        if inventory.status != 'draft':
            raise HTTPException(status_code=400, detail="只有草稿状态的盘点记录才能编辑")
        
        # 更新字段
        update_data = data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(inventory, field, value)
        
        inventory.updated_by = user_id
        
        self.db.commit()
        self.db.refresh(inventory)
        
        # 更新汇总数据
        self._update_summary(project_id, inventory.store_id, inventory.month)
        
        return inventory

    def delete_monthly_inventory(self, inventory_id: UUID, project_id: UUID) -> bool:
        """删除月度盘点记录"""
        
        inventory = self.get_monthly_inventory_by_id(inventory_id, project_id)
        if not inventory:
            return False
        
        # 只有草稿状态才能删除
        if inventory.status != 'draft':
            raise HTTPException(status_code=400, detail="只有草稿状态的盘点记录才能删除")
        
        store_id = inventory.store_id
        month = inventory.month
        
        self.db.delete(inventory)
        self.db.commit()
        
        # 更新汇总数据
        self._update_summary(project_id, store_id, month)
        
        return True

    def submit_to_finance(
        self, 
        project_id: UUID, 
        store_id: UUID, 
        month: date, 
        user_id: UUID
    ) -> bool:
        """提交到财务审核"""
        
        # 更新盘点记录状态
        updated_count = self.db.query(MonthlyInventory).filter(
            MonthlyInventory.project_id == project_id,
            MonthlyInventory.store_id == store_id,
            MonthlyInventory.month == month,
            MonthlyInventory.status == 'draft'
        ).update({
            'status': 'submitted',
            'submitted_at': datetime.utcnow(),
            'updated_by': user_id
        })
        
        # 更新汇总状态
        summary = self._get_or_create_summary(project_id, store_id, month)
        summary.status = 'submitted'
        summary.submitted_at = datetime.utcnow()
        summary.updated_by = user_id
        
        self.db.commit()
        
        return updated_count > 0

    def withdraw_from_finance(
        self, 
        project_id: UUID, 
        store_id: UUID, 
        month: date, 
        user_id: UUID
    ) -> bool:
        """从财务审核撤回"""
        
        # 更新盘点记录状态
        updated_count = self.db.query(MonthlyInventory).filter(
            MonthlyInventory.project_id == project_id,
            MonthlyInventory.store_id == store_id,
            MonthlyInventory.month == month,
            MonthlyInventory.status == 'submitted'
        ).update({
            'status': 'draft',
            'submitted_at': None,
            'updated_by': user_id
        })
        
        # 更新汇总状态
        summary = self._get_or_create_summary(project_id, store_id, month)
        summary.status = 'draft'
        summary.submitted_at = None
        summary.updated_by = user_id
        
        self.db.commit()
        
        return updated_count > 0

    def upload_excel(
        self, 
        project_id: UUID, 
        tenant_id: Optional[UUID],
        store_id: UUID, 
        month: date, 
        file: UploadFile, 
        remark: Optional[str],
        user_id: UUID
    ) -> MonthlyInventoryUpload:
        """上传Excel盘点表"""
        
        # 保存文件
        upload_dir = f"uploads/monthly_inventory/{project_id}"
        os.makedirs(upload_dir, exist_ok=True)
        
        file_path = f"{upload_dir}/{file.filename}"
        with open(file_path, "wb") as buffer:
            buffer.write(file.file.read())
        
        # 创建上传记录
        upload_record = MonthlyInventoryUpload(
            tenant_id=tenant_id,
            project_id=project_id,
            store_id=store_id,
            month=month,
            filename=file.filename,
            file_path=file_path,
            remark=remark,
            created_by=user_id
        )
        
        self.db.add(upload_record)
        self.db.commit()
        self.db.refresh(upload_record)
        
        # 异步处理Excel文件
        self._process_excel_file(upload_record.id, project_id, tenant_id, user_id)
        
        return upload_record

    def _process_excel_file(
        self, 
        upload_id: UUID, 
        project_id: UUID, 
        tenant_id: Optional[UUID],
        user_id: UUID
    ):
        """处理Excel文件"""
        
        upload_record = self.db.query(MonthlyInventoryUpload).filter(
            MonthlyInventoryUpload.id == upload_id
        ).first()
        
        if not upload_record:
            return
        
        try:
            # 读取Excel文件
            df = pd.read_excel(upload_record.file_path)
            
            total_rows = len(df)
            success_rows = 0
            error_rows = 0
            error_details = []
            
            for index, row in df.iterrows():
                try:
                    # 根据商品编号查找商品
                    product = self.db.query(Product).filter(
                        Product.project_id == project_id,
                        Product.sku == row['商品编号']
                    ).first()
                    
                    if not product:
                        error_details.append(f"第{index+2}行：商品编号 {row['商品编号']} 不存在")
                        error_rows += 1
                        continue
                    
                    # 检查是否已存在
                    existing = self.db.query(MonthlyInventory).filter(
                        MonthlyInventory.project_id == project_id,
                        MonthlyInventory.store_id == upload_record.store_id,
                        MonthlyInventory.product_id == product.id,
                        MonthlyInventory.month == upload_record.month
                    ).first()
                    
                    if existing:
                        # 更新现有记录
                        existing.inventory_quantity = float(row['库存量'])
                        existing.actual_quantity = float(row['实际量'])
                        existing.remark = row.get('备注', '')
                        existing.updated_by = user_id
                    else:
                        # 创建新记录
                        inventory = MonthlyInventory(
                            tenant_id=tenant_id,
                            project_id=project_id,
                            store_id=upload_record.store_id,
                            product_id=product.id,
                            month=upload_record.month,
                            inventory_quantity=float(row['库存量']),
                            actual_quantity=float(row['实际量']),
                            unit=product.unit,
                            remark=row.get('备注', ''),
                            created_by=user_id
                        )
                        self.db.add(inventory)
                    
                    success_rows += 1
                    
                except Exception as e:
                    error_details.append(f"第{index+2}行：{str(e)}")
                    error_rows += 1
            
            # 更新上传记录
            upload_record.total_rows = total_rows
            upload_record.success_rows = success_rows
            upload_record.error_rows = error_rows
            upload_record.error_details = '\n'.join(error_details) if error_details else None
            upload_record.status = 'completed' if error_rows == 0 else 'completed'
            
            self.db.commit()
            
            # 更新汇总数据
            self._update_summary(project_id, upload_record.store_id, upload_record.month)
            
        except Exception as e:
            upload_record.status = 'failed'
            upload_record.error_details = str(e)
            self.db.commit()

    async def _get_inventory_statistics(
        self, 
        project_id: UUID, 
        store_id: Optional[UUID] = None, 
        month: Optional[str] = None
    ) -> MonthlyInventoryStatistics:
        """获取盘点统计数据"""
        
        filters = [MonthlyInventory.project_id == project_id]
        
        if store_id:
            filters.append(MonthlyInventory.store_id == store_id)
        
        if month:
            try:
                month_date = datetime.strptime(month, '%Y-%m').date()
                filters.append(MonthlyInventory.month == month_date)
            except ValueError:
                pass

        # 总数查询
        total_query = select(func.count()).select_from(
            select(MonthlyInventory).filter(and_(*filters)).subquery()
        )
        total_result = await self.db.execute(total_query)
        total = total_result.scalar() or 0

        # 各状态统计
        status_filters = filters.copy()
        
        # 已完成（已审核）
        completed_query = select(func.count()).select_from(
            select(MonthlyInventory).filter(
                and_(*status_filters + [MonthlyInventory.status == 'approved'])
            ).subquery()
        )
        completed_result = await self.db.execute(completed_query)
        completed = completed_result.scalar() or 0

        # 待处理（草稿）
        pending_query = select(func.count()).select_from(
            select(MonthlyInventory).filter(
                and_(*status_filters + [MonthlyInventory.status == 'draft'])
            ).subquery()
        )
        pending_result = await self.db.execute(pending_query)
        pending = pending_result.scalar() or 0

        # 已提交
        submitted_query = select(func.count()).select_from(
            select(MonthlyInventory).filter(
                and_(*status_filters + [MonthlyInventory.status == 'submitted'])
            ).subquery()
        )
        submitted_result = await self.db.execute(submitted_query)
        submitted = submitted_result.scalar() or 0

        return MonthlyInventoryStatistics(
            total=total,
            completed=completed,
            pending=pending,
            submitted=submitted
        )

    async def _update_summary(self, project_id: UUID, store_id: UUID, month: date):
        """更新汇总数据"""
        
        # 获取该门店该月份的所有盘点记录
        query = select(MonthlyInventory).filter(
            MonthlyInventory.project_id == project_id,
            MonthlyInventory.store_id == store_id,
            MonthlyInventory.month == month
        )
        result = await self.db.execute(query)
        inventories = result.scalars().all()
        
        # 计算汇总数据
        total_items = len(inventories)
        completed_items = len([inv for inv in inventories if inv.actual_quantity is not None])
        diff_items = len([inv for inv in inventories if inv.difference != 0])
        total_diff_amount = sum(inv.difference_amount or 0 for inv in inventories)
        
        # 获取或创建汇总记录
        summary = self._get_or_create_summary(project_id, store_id, month)
        
        # 更新汇总数据
        summary.total_items = total_items
        summary.completed_items = completed_items
        summary.diff_items = diff_items
        summary.total_diff_amount = total_diff_amount
        summary.updated_at = datetime.now()
        
        await self.db.commit()

    async def update_system_inventory(
        self,
        project_id: UUID,
        store_id: UUID,
        month: str,
        product_code: str,
        system_quantity: float,
        product_name: Optional[str] = None,
        unit: Optional[str] = None,
        specification: Optional[str] = None,
        category: Optional[str] = None,
        erp_system: str = "unknown",
        user_id: UUID = None
    ) -> bool:
        """更新或创建系统库存记录"""
        
        try:
            # 转换月份格式
            month_date = datetime.strptime(month, '%Y-%m').date()
            
            # 根据商品编号查找商品
            product_query = select(Product).filter(
                Product.project_id == project_id,
                Product.sku == product_code
            )
            product_result = await self.db.execute(product_query)
            product = product_result.scalars().first()
            
            if not product:
                # 如果商品不存在，可以选择创建或跳过
                # 这里选择跳过，记录错误
                raise ValueError(f"商品编号 {product_code} 不存在")
            
            # 查找现有的盘点记录
            inventory_query = select(MonthlyInventory).filter(
                MonthlyInventory.project_id == project_id,
                MonthlyInventory.store_id == store_id,
                MonthlyInventory.product_id == product.id,
                MonthlyInventory.month == month_date
            )
            inventory_result = await self.db.execute(inventory_query)
            inventory = inventory_result.scalars().first()
            
            if inventory:
                # 更新现有记录的系统库存数量
                inventory.system_quantity = system_quantity
                inventory.updated_at = datetime.now()
                inventory.updated_by = user_id
            else:
                # 创建新的盘点记录，只设置系统库存数量
                inventory = MonthlyInventory(
                    project_id=project_id,
                    store_id=store_id,
                    product_id=product.id,
                    month=month_date,
                    system_quantity=system_quantity,
                    inventory_quantity=0,  # 默认值，等待门店盘点
                    actual_quantity=None,  # 等待门店盘点
                    unit=unit or product.unit,
                    created_by=user_id,
                    status='draft'
                )
                self.db.add(inventory)
            
            await self.db.commit()
            return True
            
        except Exception as e:
            await self.db.rollback()
            raise e

    async def recalculate_inventory_differences(
        self,
        project_id: UUID,
        store_id: UUID,
        month: str
    ) -> bool:
        """重新计算库存差异量"""
        
        try:
            # 转换月份格式
            month_date = datetime.strptime(month, '%Y-%m').date()
            
            # 获取该门店该月份的所有盘点记录
            query = select(MonthlyInventory).filter(
                MonthlyInventory.project_id == project_id,
                MonthlyInventory.store_id == store_id,
                MonthlyInventory.month == month_date
            )
            result = await self.db.execute(query)
            inventories = result.scalars().all()
            
            # 重新计算每个记录的差异
            for inventory in inventories:
                if inventory.actual_quantity is not None and inventory.system_quantity is not None:
                    # 计算与系统库存的差异
                    inventory.system_difference = inventory.actual_quantity - inventory.system_quantity
                    # 如果有库存量，也计算与库存的差异
                    if inventory.inventory_quantity is not None:
                        inventory.inventory_difference = inventory.actual_quantity - inventory.inventory_quantity
                
                inventory.updated_at = datetime.now()
            
            await self.db.commit()
            
            # 更新汇总数据
            await self._update_summary(project_id, store_id, month_date)
            
            return True
            
        except Exception as e:
            await self.db.rollback()
            raise e

    def _get_or_create_summary(
        self, 
        project_id: UUID, 
        store_id: UUID, 
        month: date
    ) -> MonthlyInventorySummary:
        """获取或创建汇总记录"""
        
        summary = self.db.query(MonthlyInventorySummary).filter(
            MonthlyInventorySummary.project_id == project_id,
            MonthlyInventorySummary.store_id == store_id,
            MonthlyInventorySummary.month == month
        ).first()
        
        if not summary:
            summary = MonthlyInventorySummary(
                project_id=project_id,
                store_id=store_id,
                month=month
            )
            self.db.add(summary)
            self.db.commit()
            self.db.refresh(summary)
        
        return summary


class MonthlyInventoryAuditService:
    """月度盘点审核服务"""

    def __init__(self, db: Session):
        self.db = db

    async def get_audit_list(
        self, 
        project_id: UUID, 
        query: MonthlyInventoryAuditQuery
    ) -> Tuple[List[MonthlyInventorySummary], int, MonthlyInventoryAuditStatistics]:
        """获取审核列表（按门店月份汇总）"""
        
        filters = [MonthlyInventorySummary.project_id == project_id]
        
        if query.store_id:
            filters.append(MonthlyInventorySummary.store_id == query.store_id)
        
        if query.month:
            try:
                month_date = datetime.strptime(query.month, '%Y-%m').date()
                filters.append(MonthlyInventorySummary.month == month_date)
            except ValueError:
                pass
        
        if query.status:
            filters.append(MonthlyInventorySummary.status == query.status)

        # 基础查询
        base_query = select(MonthlyInventorySummary).filter(and_(*filters))

        # 获取总数
        count_query = select(func.count()).select_from(base_query.subquery())
        total_result = await self.db.execute(count_query)
        total = total_result.scalar()

        # 分页查询
        items_query = base_query.order_by(desc(MonthlyInventorySummary.created_at)).offset(
            (query.page - 1) * query.page_size
        ).limit(query.page_size)
        
        items_result = await self.db.execute(items_query)
        items = items_result.scalars().all()

        # 统计数据
        statistics = await self._get_audit_statistics(project_id, query.store_id, query.month)

        return items, total, statistics

    async def get_audit_items(
        self, 
        project_id: UUID, 
        store_id: UUID, 
        month: date
    ) -> List[MonthlyInventory]:
        """获取盘点明细列表"""
        
        query = select(MonthlyInventory).filter(
            MonthlyInventory.project_id == project_id,
            MonthlyInventory.store_id == store_id,
            MonthlyInventory.month == month
        ).order_by(MonthlyInventory.created_at)
        
        result = await self.db.execute(query)
        return result.scalars().all()

    async def audit_by_store_month(
        self, 
        project_id: UUID, 
        data: MonthlyInventoryAudit, 
        user_id: UUID
    ) -> bool:
        """按门店月份审核"""
        
        # 更新盘点记录状态
        new_status = 'approved' if data.action == 'approve' else 'rejected'
        
        updated_count = self.db.query(MonthlyInventory).filter(
            MonthlyInventory.project_id == project_id,
            MonthlyInventory.store_id == data.store_id,
            MonthlyInventory.month == data.month,
            MonthlyInventory.status == 'submitted'
        ).update({
            'status': new_status,
            'audited_at': datetime.utcnow(),
            'audit_remark': data.audit_remark,
            'audited_by': user_id
        })
        
        # 更新汇总状态
        summary = self.db.query(MonthlyInventorySummary).filter(
            MonthlyInventorySummary.project_id == project_id,
            MonthlyInventorySummary.store_id == data.store_id,
            MonthlyInventorySummary.month == data.month
        ).first()
        
        if summary:
            summary.status = new_status
            summary.audited_at = datetime.utcnow()
            summary.audit_remark = data.audit_remark
            summary.audited_by = user_id
        
        self.db.commit()
        
        return updated_count > 0

    async def batch_audit(
        self, 
        project_id: UUID, 
        data: MonthlyInventoryBatchAudit, 
        user_id: UUID
    ) -> int:
        """批量审核"""
        
        total_updated = 0
        new_status = 'approved' if data.action == 'approve' else 'rejected'
        
        for item in data.items:
            store_id = UUID(item['store_id'])
            month = item['month']
            
            # 更新盘点记录
            updated_count = self.db.query(MonthlyInventory).filter(
                MonthlyInventory.project_id == project_id,
                MonthlyInventory.store_id == store_id,
                MonthlyInventory.month == month,
                MonthlyInventory.status == 'submitted'
            ).update({
                'status': new_status,
                'audited_at': datetime.utcnow(),
                'audit_remark': data.audit_remark,
                'audited_by': user_id
            })
            
            # 更新汇总
            summary = self.db.query(MonthlyInventorySummary).filter(
                MonthlyInventorySummary.project_id == project_id,
                MonthlyInventorySummary.store_id == store_id,
                MonthlyInventorySummary.month == month
            ).first()
            
            if summary:
                summary.status = new_status
                summary.audited_at = datetime.utcnow()
                summary.audit_remark = data.audit_remark
                summary.audited_by = user_id
            
            total_updated += updated_count
        
        self.db.commit()
        
        return total_updated

    async def _get_audit_statistics(
        self, 
        project_id: UUID, 
        store_id: Optional[UUID] = None, 
        month: Optional[str] = None
    ) -> MonthlyInventoryAuditStatistics:
        """获取审核统计数据"""
        
        filters = [MonthlyInventorySummary.project_id == project_id]
        
        if store_id:
            filters.append(MonthlyInventorySummary.store_id == store_id)
        
        if month:
            try:
                month_date = datetime.strptime(month, '%Y-%m').date()
                filters.append(MonthlyInventorySummary.month == month_date)
            except ValueError:
                pass

        # 总数查询
        total_query = select(func.count()).select_from(
            select(MonthlyInventorySummary).filter(and_(*filters)).subquery()
        )
        total_result = await self.db.execute(total_query)
        total = total_result.scalar() or 0

        # 各状态统计
        status_filters = filters.copy()
        
        # 待审核
        pending_query = select(func.count()).select_from(
            select(MonthlyInventorySummary).filter(
                and_(*status_filters + [MonthlyInventorySummary.status == 'pending'])
            ).subquery()
        )
        pending_result = await self.db.execute(pending_query)
        pending = pending_result.scalar() or 0

        # 已通过
        approved_query = select(func.count()).select_from(
            select(MonthlyInventorySummary).filter(
                and_(*status_filters + [MonthlyInventorySummary.status == 'approved'])
            ).subquery()
        )
        approved_result = await self.db.execute(approved_query)
        approved = approved_result.scalar() or 0

        # 已驳回
        rejected_query = select(func.count()).select_from(
            select(MonthlyInventorySummary).filter(
                and_(*status_filters + [MonthlyInventorySummary.status == 'rejected'])
            ).subquery()
        )
        rejected_result = await self.db.execute(rejected_query)
        rejected = rejected_result.scalar() or 0

        return MonthlyInventoryAuditStatistics(
            total=total,
            pending=pending,
            approved=approved,
            rejected=rejected
        )

    @staticmethod
    async def update_system_inventory_batch(
        db: Session, 
        project_id: UUID, 
        store_id: UUID, 
        month: str, 
        erp_system: str, 
        records: list
    ):
        """批量更新第三方ERP系统库存数据"""
        from models.product import Product
        from models.product_category import ProductCategory
        from sqlalchemy import and_, or_
        
        success_count = 0
        error_count = 0
        error_details = []
        
        # 解析月份
        try:
            month_date = datetime.strptime(month, "%Y-%m").date()
        except ValueError:
            raise HTTPException(status_code=400, detail="月份格式错误，应为YYYY-MM")
        
        for index, record in enumerate(records):
            try:
                product_code = str(record.get('product_code', '')).strip()
                system_quantity = float(record.get('system_quantity', 0))
                product_name = str(record.get('product_name', '')).strip() if record.get('product_name') else None
                unit = str(record.get('unit', '')).strip() if record.get('unit') else None
                specification = str(record.get('specification', '')).strip() if record.get('specification') else None
                category_name = str(record.get('category', '')).strip() if record.get('category') else None
                
                if not product_code:
                    error_details.append(f"第{index+2}行：商品编号不能为空")
                    error_count += 1
                    continue
                
                # 查找商品，支持多种匹配方式
                product_query = select(Product).filter(
                    Product.project_id == project_id,
                    Product.is_active == True
                )
                
                # 首先尝试精确匹配SKU
                product_result = await db.execute(
                    product_query.filter(Product.sku == product_code)
                )
                product = product_result.scalar_one_or_none()
                
                # 如果SKU匹配失败，尝试条形码匹配
                if not product:
                    product_result = await db.execute(
                        product_query.filter(Product.barcode == product_code)
                    )
                    product = product_result.scalar_one_or_none()
                
                # 如果还是没找到，尝试名称+规格匹配
                if not product and product_name:
                    conditions = [Product.name.ilike(f"%{product_name}%")]
                    if specification:
                        conditions.append(Product.specification.ilike(f"%{specification}%"))
                    
                    product_result = await db.execute(
                        product_query.filter(and_(*conditions))
                    )
                    products = product_result.scalars().all()
                    
                    if len(products) == 1:
                        product = products[0]
                    elif len(products) > 1:
                        error_details.append(f"第{index+2}行：找到多个匹配的商品，请使用更精确的商品编号")
                        error_count += 1
                        continue
                
                if not product:
                    error_details.append(f"第{index+2}行：未找到商品编号为 {product_code} 的商品")
                    error_count += 1
                    continue
                
                # 查找或创建月度盘点记录
                inventory_query = select(MonthlyInventory).filter(
                    MonthlyInventory.project_id == project_id,
                    MonthlyInventory.store_id == store_id,
                    MonthlyInventory.product_id == product.id,
                    MonthlyInventory.month == month_date
                )
                inventory_result = await db.execute(inventory_query)
                inventory = inventory_result.scalar_one_or_none()
                
                if inventory:
                    # 更新现有记录的系统库存
                    inventory.system_quantity = system_quantity
                    inventory.updated_at = datetime.utcnow()
                else:
                    # 创建新的盘点记录
                    inventory = MonthlyInventory(
                        project_id=project_id,
                        store_id=store_id,
                        product_id=product.id,
                        month=month_date,
                        inventory_quantity=0,  # 默认库存量为0
                        actual_quantity=0,     # 默认实际量为0
                        system_quantity=system_quantity,
                        unit=unit or product.unit,
                        status="draft",
                        created_at=datetime.utcnow(),
                        updated_at=datetime.utcnow()
                    )
                    db.add(inventory)
                
                success_count += 1
                
            except Exception as e:
                error_details.append(f"第{index+2}行：{str(e)}")
                error_count += 1
        
        # 提交事务
        await db.commit()
        
        return {
            'success_count': success_count,
            'error_count': error_count,
            'error_details': error_details
        } 