#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
项目级 AI 聊天 API
"""

import logging
import uuid
from typing import Optional, List, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query, Path, status, Request, Response
from fastapi.responses import StreamingResponse
from sqlalchemy.ext.asyncio import AsyncSession
import json

from db.database import get_db
from models.project import Project
from core.auth import get_current_user
from api.deps import get_current_project
from services.ai import AIChatService
from schemas.ai import (
    AIChatRequest,
    AIChatResponse,
    AIChatStreamResponse,
    AIChatErrorResponse,
)

logger = logging.getLogger(__name__)
router = APIRouter()

@router.post("/ai/chat", response_model=AIChatResponse)
async def chat_completion(
    request: AIChatRequest,
    project: Project = Depends(get_current_project),
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    AI 聊天完成
    
    发送消息给 AI 模型并获取回复。
    """
    try:
        # 如果请求流式响应，使用 StreamingResponse
        if request.stream:
            return await stream_chat_completion(request, project, current_user, db)
            
        # 发送聊天请求
        result = await AIChatService.chat_completion(
            db=db,
            project_id=project.id,
            user_id=current_user.id,
            messages=request.messages,
            model_id=request.model_id,
            config_id=request.config_id,
            temperature=request.temperature,
            max_tokens=request.max_tokens,
            functions=request.functions,
            function_call=request.function_call,
            stream=False,
        )
        
        return result
    except ValueError as e:
        logger.error(f"AI 聊天请求失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )
    except Exception as e:
        logger.error(f"AI 聊天请求失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"AI 聊天请求失败: {str(e)}",
        )

async def stream_chat_completion(
    request: AIChatRequest,
    project: Project,
    current_user: dict,
    db: AsyncSession,
):
    """
    流式 AI 聊天完成
    """
    async def generate():
        try:
            # 发送聊天请求
            result = await AIChatService.chat_completion(
                db=db,
                project_id=project.id,
                user_id=current_user.id,
                messages=request.messages,
                model_id=request.model_id,
                config_id=request.config_id,
                temperature=request.temperature,
                max_tokens=request.max_tokens,
                functions=request.functions,
                function_call=request.function_call,
                stream=True,
            )
            
            # 解析流式响应
            for chunk in result:
                if chunk:
                    yield f"data: {json.dumps(chunk)}\n\n"
                    
            yield "data: [DONE]\n\n"
        except Exception as e:
            logger.error(f"流式 AI 聊天请求失败: {e}")
            error_response = {
                "success": False,
                "message": f"流式 AI 聊天请求失败: {str(e)}",
            }
            yield f"data: {json.dumps(error_response)}\n\n"
            
    return StreamingResponse(
        generate(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/event-stream",
        },
    )
