#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
项目级 AI 音频 API
"""

import logging
import uuid
import base64
from typing import Optional, List, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query, Path, status, UploadFile, File, Form, Response
from fastapi.responses import StreamingResponse
from sqlalchemy.ext.asyncio import AsyncSession
import io

from db.database import get_db
from models.project import Project
from core.auth import get_current_user
from api.deps import get_current_project
from services.ai import AIAudioService
from schemas.ai import (
    AITranscriptionRequest,
    AITranscriptionResponse,
    AITextToSpeechRequest,
    AITextToSpeechResponse,
    AIAudioErrorResponse,
)

logger = logging.getLogger(__name__)
router = APIRouter()

@router.post("/ai/audio/transcriptions", response_model=AITranscriptionResponse)
async def transcribe_audio(
    request: AITranscriptionRequest,
    project: Project = Depends(get_current_project),
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    转录音频
    
    将音频转换为文本。
    """
    try:
        # 发送音频转录请求
        result = await AIAudioService.transcribe_audio(
            db=db,
            project_id=project.id,
            user_id=current_user.id,
            audio_data=request.audio_base64,
            model_id=request.model_id,
            config_id=request.config_id,
            language=request.language,
            prompt=request.prompt,
            response_format=request.response_format,
            temperature=request.temperature,
        )
        
        # 构建响应
        if request.response_format == "json":
            return result
        else:
            # 文本格式
            return {"text": result.get("text", "")}
    except ValueError as e:
        logger.error(f"AI 音频转录请求失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )
    except Exception as e:
        logger.error(f"AI 音频转录请求失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"AI 音频转录请求失败: {str(e)}",
        )

@router.post("/ai/audio/transcriptions/upload")
async def transcribe_uploaded_audio(
    file: UploadFile = File(...),
    language: Optional[str] = Form(None),
    prompt: Optional[str] = Form(None),
    response_format: str = Form("text"),
    temperature: Optional[float] = Form(None),
    model_id: Optional[uuid.UUID] = Form(None),
    config_id: Optional[uuid.UUID] = Form(None),
    project: Project = Depends(get_current_project),
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    转录上传的音频
    
    上传音频文件并转换为文本。
    """
    try:
        # 读取上传的音频文件
        audio_data = await file.read()
        
        # 发送音频转录请求
        result = await AIAudioService.transcribe_audio(
            db=db,
            project_id=project.id,
            user_id=current_user.id,
            audio_data=audio_data,
            model_id=model_id,
            config_id=config_id,
            language=language,
            prompt=prompt,
            response_format=response_format,
            temperature=temperature,
        )
        
        # 构建响应
        if response_format == "json":
            return result
        else:
            # 文本格式
            return {"text": result.get("text", "")}
    except ValueError as e:
        logger.error(f"AI 音频转录请求失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )
    except Exception as e:
        logger.error(f"AI 音频转录请求失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"AI 音频转录请求失败: {str(e)}",
        )

@router.post("/ai/audio/speech", response_model=AITextToSpeechResponse)
async def text_to_speech(
    request: AITextToSpeechRequest,
    project: Project = Depends(get_current_project),
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    文本转语音
    
    将文本转换为语音。
    """
    try:
        # 发送文本转语音请求
        result = await AIAudioService.text_to_speech(
            db=db,
            project_id=project.id,
            user_id=current_user.id,
            text=request.text,
            model_id=request.model_id,
            config_id=request.config_id,
            voice=request.voice,
            response_format=request.response_format,
            speed=request.speed,
        )
        
        return result
    except ValueError as e:
        logger.error(f"AI 文本转语音请求失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )
    except Exception as e:
        logger.error(f"AI 文本转语音请求失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"AI 文本转语音请求失败: {str(e)}",
        )

@router.post("/ai/audio/speech/stream")
async def text_to_speech_stream(
    request: AITextToSpeechRequest,
    project: Project = Depends(get_current_project),
    current_user: dict = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    文本转语音（流式响应）
    
    将文本转换为语音，并以流式方式返回音频数据。
    """
    try:
        # 发送文本转语音请求
        result = await AIAudioService.text_to_speech(
            db=db,
            project_id=project.id,
            user_id=current_user.id,
            text=request.text,
            model_id=request.model_id,
            config_id=request.config_id,
            voice=request.voice,
            response_format=request.response_format,
            speed=request.speed,
        )
        
        # 获取音频数据
        audio_base64 = result.get("audio_base64")
        audio_format = result.get("format")
        
        # 解码 base64 数据
        audio_data = base64.b64decode(audio_base64)
        
        # 创建内存文件对象
        audio_stream = io.BytesIO(audio_data)
        audio_stream.seek(0)
        
        # 设置内容类型
        content_type = f"audio/{audio_format}"
        
        # 返回流式响应
        return StreamingResponse(
            audio_stream,
            media_type=content_type,
            headers={
                "Content-Disposition": f"attachment; filename=speech.{audio_format}"
            }
        )
    except ValueError as e:
        logger.error(f"AI 文本转语音请求失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )
    except Exception as e:
        logger.error(f"AI 文本转语音请求失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"AI 文本转语音请求失败: {str(e)}",
        )
