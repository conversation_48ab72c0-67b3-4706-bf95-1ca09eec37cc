import React, { useState, useEffect } from 'react';
import { Card, Typography, Select, Button, Space, Input, Tabs, Spin, message, Divider, Table, Tag, Form, Collapse, Alert, Steps, List } from 'antd';
import { SendOutlined, ReloadOutlined, CopyOutlined, ApiOutlined, CodeOutlined, HistoryOutlined, CheckCircleOutlined, CloseCircleOutlined, LinkOutlined, ToolOutlined, DisconnectOutlined, KeyOutlined } from '@ant-design/icons';
import SyntaxHighlighter from 'react-syntax-highlighter';
import { vs2015 } from 'react-syntax-highlighter/dist/esm/styles/hljs';
import ReactJson from 'react-json-view';
import { getAvailableServers, connectToMCPServer, getMCPSessionTools, callMCPTool, closeMCPSession } from '../services/mcpService';

const { Title, Paragraph, Text } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;
const { TextArea } = Input;
const { Panel } = Collapse;
const { Password } = Input;

/**
 * API测试组件
 */
const TestApi = () => {
  const [form] = Form.useForm();
  const [tokenForm] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [testLoading, setTestLoading] = useState(false);
  const [servers, setServers] = useState([]);
  const [selectedServer, setSelectedServer] = useState(null);
  const [tools, setTools] = useState([]);
  const [selectedTool, setSelectedTool] = useState(null);
  const [testResult, setTestResult] = useState(null);
  const [testHistory, setTestHistory] = useState([]);
  const [activeTab, setActiveTab] = useState('1');

  // MCP会话状态
  const [sessionId, setSessionId] = useState(null);
  const [serverInfo, setServerInfo] = useState(null);
  const [capabilities, setCapabilities] = useState(null);
  const [apiToken, setApiToken] = useState("");

  // 测试步骤
  const [currentStep, setCurrentStep] = useState(0);
  const [testSteps, setTestSteps] = useState([
    { title: '连接服务器', status: 'process', description: '输入API令牌并连接到MCP服务器', icon: <LinkOutlined /> },
    { title: '获取工具列表', status: 'wait', description: '获取可用的工具列表', icon: <ToolOutlined /> },
    { title: '调用工具', status: 'wait', description: '选择工具并发送请求', icon: <SendOutlined /> },
    { title: '关闭连接', status: 'wait', description: '关闭与服务器的连接', icon: <DisconnectOutlined /> }
  ]);

  // 加载服务器列表
  useEffect(() => {
    const fetchServers = async () => {
      setLoading(true);
      try {
        const response = await getAvailableServers();
        if (response.success) {
          setServers(response.data);
          if (response.data.length > 0) {
            setSelectedServer(response.data[0].id);
          }
        } else {
          message.error('获取服务器列表失败');
        }
      } catch (error) {
        console.error('获取服务器列表失败:', error);
        message.error('获取服务器列表失败');
      } finally {
        setLoading(false);
      }
    };

    fetchServers();
  }, []);

  // 处理连接到MCP服务器
  const handleConnect = async (values) => {
    if (!selectedServer) {
      message.error('请选择服务器');
      return;
    }

    setTestLoading(true);
    setApiToken(values.apiToken || "");

    try {
      // 更新步骤状态
      setTestSteps(steps => steps.map((step, index) => {
        if (index === 0) return { ...step, status: 'process' };
        return step;
      }));

      // 连接到MCP服务器
      const response = await connectToMCPServer(selectedServer, values.apiToken);

      if (response.success) {
        // 保存会话信息
        setSessionId(response.data.session_id);
        setServerInfo(response.data.server_info);
        setCapabilities(response.data.capabilities);

        // 更新步骤状态
        setTestSteps(steps => steps.map((step, index) => {
          if (index === 0) return { ...step, status: 'finish' };
          if (index === 1) return { ...step, status: 'process' };
          return step;
        }));
        setCurrentStep(1);

        message.success('连接MCP服务器成功');
      } else {
        // 连接失败
        setTestSteps(steps => steps.map((step, index) => {
          if (index === 0) return { ...step, status: 'error' };
          return step;
        }));
        message.error('连接MCP服务器失败: ' + (response.data?.error || response.message || '未知错误'));
      }
    } catch (error) {
      console.error('连接MCP服务器失败:', error);
      setTestSteps(steps => steps.map((step, index) => {
        if (index === 0) return { ...step, status: 'error' };
        return step;
      }));
      message.error('连接MCP服务器失败: ' + (error.message || '未知错误'));
    } finally {
      setTestLoading(false);
    }
  };

  // 处理获取工具列表
  const handleGetTools = async () => {
    if (!sessionId) {
      message.error('请先连接到MCP服务器');
      return;
    }

    setTestLoading(true);

    try {
      // 更新步骤状态
      setTestSteps(steps => steps.map((step, index) => {
        if (index === 1) return { ...step, status: 'process' };
        return step;
      }));

      // 获取工具列表
      const response = await getMCPSessionTools(sessionId);

      if (response.success) {
        // 保存工具列表
        setTools(response.data);
        if (response.data.length > 0) {
          setSelectedTool(response.data[0].name);
          // 重置表单
          form.resetFields();
        } else {
          setSelectedTool(null);
        }

        // 更新步骤状态
        setTestSteps(steps => steps.map((step, index) => {
          if (index <= 1) return { ...step, status: 'finish' };
          if (index === 2) return { ...step, status: 'process' };
          return step;
        }));
        setCurrentStep(2);

        message.success('获取工具列表成功');
      } else {
        // 获取失败
        setTestSteps(steps => steps.map((step, index) => {
          if (index === 0) return { ...step, status: 'finish' };
          if (index === 1) return { ...step, status: 'error' };
          return step;
        }));
        message.error('获取工具列表失败: ' + (response.data?.error || response.message || '未知错误'));
      }
    } catch (error) {
      console.error('获取工具列表失败:', error);
      setTestSteps(steps => steps.map((step, index) => {
        if (index === 0) return { ...step, status: 'finish' };
        if (index === 1) return { ...step, status: 'error' };
        return step;
      }));
      message.error('获取工具列表失败: ' + (error.message || '未知错误'));
    } finally {
      setTestLoading(false);
    }
  };

  // 处理工具选择
  const handleToolChange = (toolName) => {
    setSelectedTool(toolName);
    // 重置表单
    form.resetFields();
  };

  // 处理调用工具
  const handleCallTool = async (values) => {
    if (!sessionId || !selectedTool) {
      message.error('请先连接到MCP服务器并选择工具');
      return;
    }

    setTestLoading(true);
    setTestResult(null);

    try {
      // 更新步骤状态
      setTestSteps(steps => steps.map((step, index) => {
        if (index === 2) return { ...step, status: 'process' };
        return step;
      }));

      // 调用工具
      const response = await callMCPTool(sessionId, selectedTool, values);

      if (response.success) {
        // 保存测试结果
        setTestResult(response.data);

        // 添加到历史记录
        const historyItem = {
          id: Date.now(),
          timestamp: new Date().toISOString(),
          server: servers.find(s => s.id === selectedServer)?.name || selectedServer,
          tool: selectedTool,
          parameters: values,
          result: response.data
        };

        setTestHistory(prev => [historyItem, ...prev].slice(0, 10));

        // 更新步骤状态
        setTestSteps(steps => steps.map((step, index) => {
          if (index <= 2) return { ...step, status: 'finish' };
          if (index === 3) return { ...step, status: 'process' };
          return step;
        }));
        setCurrentStep(3);

        message.success('调用工具成功');

        // 切换到结果标签页
        setActiveTab('2');
      } else {
        // 调用失败
        setTestSteps(steps => steps.map((step, index) => {
          if (index <= 1) return { ...step, status: 'finish' };
          if (index === 2) return { ...step, status: 'error' };
          return step;
        }));
        message.error('调用工具失败: ' + (response.data?.error || response.message || '未知错误'));
      }
    } catch (error) {
      console.error('调用工具失败:', error);
      setTestSteps(steps => steps.map((step, index) => {
        if (index <= 1) return { ...step, status: 'finish' };
        if (index === 2) return { ...step, status: 'error' };
        return step;
      }));
      message.error('调用工具失败: ' + (error.message || '未知错误'));
    } finally {
      setTestLoading(false);
    }
  };

  // 处理关闭会话
  const handleCloseSession = async () => {
    if (!sessionId) {
      message.error('没有活动的会话');
      return;
    }

    setTestLoading(true);

    try {
      // 更新步骤状态
      setTestSteps(steps => steps.map((step, index) => {
        if (index === 3) return { ...step, status: 'process' };
        return step;
      }));

      // 关闭会话
      const response = await closeMCPSession(sessionId);

      if (response.success) {
        // 清除会话信息
        setSessionId(null);
        setServerInfo(null);
        setCapabilities(null);
        setTools([]);
        setSelectedTool(null);

        // 更新步骤状态
        setTestSteps(steps => steps.map((step, index) => {
          if (index <= 3) return { ...step, status: 'finish' };
          return step;
        }));

        // 重置到第一步
        setTimeout(() => {
          setTestSteps(steps => steps.map((step, index) => {
            if (index === 0) return { ...step, status: 'process' };
            return { ...step, status: 'wait' };
          }));
          setCurrentStep(0);
        }, 1000);

        message.success('会话已关闭');
      } else {
        // 关闭失败
        setTestSteps(steps => steps.map((step, index) => {
          if (index <= 2) return { ...step, status: 'finish' };
          if (index === 3) return { ...step, status: 'error' };
          return step;
        }));
        message.error('关闭会话失败: ' + (response.data?.error || response.message || '未知错误'));
      }
    } catch (error) {
      console.error('关闭会话失败:', error);
      setTestSteps(steps => steps.map((step, index) => {
        if (index <= 2) return { ...step, status: 'finish' };
        if (index === 3) return { ...step, status: 'error' };
        return step;
      }));
      message.error('关闭会话失败: ' + (error.message || '未知错误'));
    } finally {
      setTestLoading(false);
    }
  };

  // 复制到剪贴板
  const copyToClipboard = (text) => {
    navigator.clipboard.writeText(text).then(
      () => {
        message.success('已复制到剪贴板');
      },
      () => {
        message.error('复制失败');
      }
    );
  };

  // 获取当前选中的工具
  const getCurrentTool = () => {
    if (!selectedTool || !tools.length) return null;
    return tools.find(tool => tool.name === selectedTool);
  };

  // 刷新服务器列表
  const refreshServers = async () => {
    setLoading(true);
    try {
      const response = await getAvailableServers();
      if (response.success) {
        setServers(response.data);
        if (response.data.length > 0 && !selectedServer) {
          setSelectedServer(response.data[0].id);
        }
        message.success('服务器列表刷新成功');
      } else {
        message.error('获取服务器列表失败');
      }
    } catch (error) {
      console.error('获取服务器列表失败:', error);
      message.error('获取服务器列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 渲染参数表单
  const renderParameterForm = () => {
    const tool = getCurrentTool();
    if (!tool) {
      return (
        <Alert
          message="未选择工具"
          description="请先选择一个工具"
          type="warning"
          showIcon
        />
      );
    }

    if (!sessionId) {
      return (
        <Alert
          message="未连接到MCP服务器"
          description="请先连接到MCP服务器并获取工具列表"
          type="warning"
          showIcon
        />
      );
    }

    const parameters = tool.parameters?.properties || {};
    const requiredParams = tool.parameters?.required || [];

    return (
      <Form
        form={form}
        layout="vertical"
        onFinish={handleCallTool}
      >
        {Object.entries(parameters).map(([name, schema]) => {
          const isRequired = requiredParams.includes(name);

          // 根据参数类型渲染不同的表单项
          let formItem;

          if (schema.type === 'string') {
            if (schema.enum) {
              // 枚举类型
              formItem = (
                <Select placeholder={`请选择${schema.description || name}`}>
                  {schema.enum.map(value => (
                    <Option key={value} value={value}>{value}</Option>
                  ))}
                </Select>
              );
            } else if (schema.format === 'date-time' || schema.format === 'date') {
              // 日期类型
              formItem = <Input placeholder={`请输入${schema.description || name}`} />;
            } else {
              // 普通字符串
              formItem = <Input placeholder={`请输入${schema.description || name}`} />;
            }
          } else if (schema.type === 'number' || schema.type === 'integer') {
            // 数字类型
            formItem = <Input type="number" placeholder={`请输入${schema.description || name}`} />;
          } else if (schema.type === 'boolean') {
            // 布尔类型
            formItem = (
              <Select placeholder={`请选择${schema.description || name}`}>
                <Option value={true}>是</Option>
                <Option value={false}>否</Option>
              </Select>
            );
          } else if (schema.type === 'array') {
            // 数组类型
            formItem = <TextArea rows={3} placeholder={`请输入${schema.description || name}，多个值用逗号分隔`} />;
          } else if (schema.type === 'object') {
            // 对象类型
            formItem = <TextArea rows={5} placeholder={`请输入${schema.description || name}，JSON格式`} />;
          } else {
            // 默认文本输入
            formItem = <Input placeholder={`请输入${schema.description || name}`} />;
          }

          return (
            <Form.Item
              key={name}
              name={name}
              label={
                <span>
                  {schema.description || name}
                  {isRequired && <span style={{ color: '#ff4d4f' }}> *</span>}
                </span>
              }
              rules={[
                {
                  required: isRequired,
                  message: `请输入${schema.description || name}`
                }
              ]}
              tooltip={schema.description}
            >
              {formItem}
            </Form.Item>
          );
        })}

        <Form.Item>
          <Button
            type="primary"
            htmlType="submit"
            loading={testLoading}
            icon={<SendOutlined />}
          >
            调用工具
          </Button>
        </Form.Item>
      </Form>
    );
  };

  // 渲染测试结果
  const renderTestResult = () => {
    if (!testResult) return (
      <Alert
        message="暂无测试结果"
        description="请先在请求标签页中发送测试请求"
        type="info"
        showIcon
      />
    );

    return (
      <div>
        <Card
          title="MCP协议流程"
          size="small"
          style={{ marginBottom: 16 }}
        >
          <Steps
            items={testSteps.map(step => ({
              title: step.title,
              description: step.description,
              status: step.status,
              icon: step.status === 'finish' ? <CheckCircleOutlined /> :
                   step.status === 'error' ? <CloseCircleOutlined /> : null
            }))}
          />
        </Card>

        <Card
          title="响应结果"
          size="small"
          extra={
            <Button
              icon={<CopyOutlined />}
              size="small"
              onClick={() => copyToClipboard(JSON.stringify(testResult, null, 2))}
            >
              复制
            </Button>
          }
        >
          <ReactJson
            src={testResult}
            theme="monokai"
            displayDataTypes={false}
            name={false}
            collapsed={1}
            style={{ maxHeight: '400px', overflow: 'auto' }}
          />
        </Card>

        <Divider dashed />

        <Collapse>
          <Panel header="请求详情" key="1">
            <div>
              <Paragraph>
                <Text strong>服务器:</Text> {servers.find(s => s.id === selectedServer)?.name || selectedServer}
              </Paragraph>
              <Paragraph>
                <Text strong>工具:</Text> {selectedTool}
              </Paragraph>
              <Paragraph>
                <Text strong>参数:</Text>
              </Paragraph>
              <div style={{ marginLeft: 16 }}>
                <ReactJson
                  src={form.getFieldsValue()}
                  theme="monokai"
                  displayDataTypes={false}
                  name={false}
                  style={{ maxHeight: '200px', overflow: 'auto' }}
                />
              </div>
            </div>
          </Panel>
          <Panel header="示例代码" key="2">
            <Tabs defaultActiveKey="python">
              <TabPane tab="Python MCP客户端" key="python">
                <div style={{ position: 'relative' }}>
                  <SyntaxHighlighter language="python" style={vs2015}>
                    {`import requests
import uuid
import json

# MCP服务器地址
server_url = "${window.location.origin}/api/v1/project/plugin/sapi/servers/${selectedServer}/mcp"
headers = {
    "Content-Type": "application/json",
    "Authorization": "Bearer ${apiToken || "YOUR_API_TOKEN"}"
}

# 1. 初始化连接
init_request = {
    "jsonrpc": "2.0",
    "id": str(uuid.uuid4()),
    "method": "initialize",
    "params": {
        "protocolVersion": "2025-03-26",
        "capabilities": {
            "tools": {
                "listChanged": True
            }
        }
    }
}

init_response = requests.post(server_url, json=init_request, headers=headers)
init_data = init_response.json()
session_id = init_data.get("result", {}).get("sessionId")
print(f"已连接到服务器，会话ID: {session_id}")

# 2. 发送初始化完成通知
init_notification = {
    "jsonrpc": "2.0",
    "method": "initialized",
    "params": {}
}
requests.post(server_url, json=init_notification, headers=headers)
print("已发送初始化完成通知")

# 3. 获取工具列表
tools_request = {
    "jsonrpc": "2.0",
    "id": str(uuid.uuid4()),
    "method": "tools/list",
    "params": {
        "sessionId": session_id
    }
}

tools_response = requests.post(server_url, json=tools_request, headers=headers)
tools_data = tools_response.json()
tools = tools_data.get("result", {}).get("tools", [])
print(f"获取到 {len(tools)} 个工具")

# 4. 调用工具
tool_request = {
    "jsonrpc": "2.0",
    "id": str(uuid.uuid4()),
    "method": "tools/call",
    "params": {
        "sessionId": session_id,
        "name": "${selectedTool}",
        "arguments": ${JSON.stringify(form.getFieldsValue(), null, 4)}
    }
}

tool_response = requests.post(server_url, json=tool_request, headers=headers)
result = tool_response.json()
print("工具调用结果:")
print(json.dumps(result, indent=2))

# 5. 关闭会话
shutdown_request = {
    "jsonrpc": "2.0",
    "id": str(uuid.uuid4()),
    "method": "shutdown",
    "params": {
        "sessionId": session_id
    }
}
requests.post(server_url, json=shutdown_request, headers=headers)
print("会话已关闭")`}
                  </SyntaxHighlighter>
                  <Button
                    icon={<CopyOutlined />}
                    size="small"
                    style={{ position: 'absolute', top: 10, right: 10 }}
                    onClick={() => copyToClipboard(`import requests
import uuid
import json

# MCP服务器地址
server_url = "${window.location.origin}/api/v1/project/plugin/sapi/servers/${selectedServer}/mcp"
headers = {
    "Content-Type": "application/json",
    "Authorization": "Bearer ${apiToken || "YOUR_API_TOKEN"}"
}

# 1. 初始化连接
init_request = {
    "jsonrpc": "2.0",
    "id": str(uuid.uuid4()),
    "method": "initialize",
    "params": {
        "protocolVersion": "2025-03-26",
        "capabilities": {
            "tools": {
                "listChanged": True
            }
        }
    }
}

init_response = requests.post(server_url, json=init_request, headers=headers)
init_data = init_response.json()
session_id = init_data.get("result", {}).get("sessionId")
print(f"已连接到服务器，会话ID: {session_id}")

# 2. 发送初始化完成通知
init_notification = {
    "jsonrpc": "2.0",
    "method": "initialized",
    "params": {}
}
requests.post(server_url, json=init_notification, headers=headers)
print("已发送初始化完成通知")

# 3. 获取工具列表
tools_request = {
    "jsonrpc": "2.0",
    "id": str(uuid.uuid4()),
    "method": "tools/list",
    "params": {
        "sessionId": session_id
    }
}

tools_response = requests.post(server_url, json=tools_request, headers=headers)
tools_data = tools_response.json()
tools = tools_data.get("result", {}).get("tools", [])
print(f"获取到 {len(tools)} 个工具")

# 4. 调用工具
tool_request = {
    "jsonrpc": "2.0",
    "id": str(uuid.uuid4()),
    "method": "tools/call",
    "params": {
        "sessionId": session_id,
        "name": "${selectedTool}",
        "arguments": ${JSON.stringify(form.getFieldsValue(), null, 4)}
    }
}

tool_response = requests.post(server_url, json=tool_request, headers=headers)
result = tool_response.json()
print("工具调用结果:")
print(json.dumps(result, indent=2))

# 5. 关闭会话
shutdown_request = {
    "jsonrpc": "2.0",
    "id": str(uuid.uuid4()),
    "method": "shutdown",
    "params": {
        "sessionId": session_id
    }
}
requests.post(server_url, json=shutdown_request, headers=headers)
print("会话已关闭")`)}
                  />
                </div>
              </TabPane>
              <TabPane tab="JavaScript MCP客户端" key="javascript">
                <div style={{ position: 'relative' }}>
                  <SyntaxHighlighter language="javascript" style={vs2015}>
                    {`// MCP服务器地址
const serverUrl = "${window.location.origin}/api/v1/project/plugin/sapi/servers/${selectedServer}/mcp";
const headers = {
  "Content-Type": "application/json",
  "Authorization": "Bearer ${apiToken || "YOUR_API_TOKEN"}"
};

// 生成UUID
function generateUUID() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    var r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

// 1. 初始化连接
const initRequest = {
  jsonrpc: "2.0",
  id: generateUUID(),
  method: "initialize",
  params: {
    protocolVersion: "2025-03-26",
    capabilities: {
      tools: {
        listChanged: true
      }
    }
  }
};

fetch(serverUrl, {
  method: "POST",
  headers: headers,
  body: JSON.stringify(initRequest)
})
.then(response => response.json())
.then(initData => {
  const sessionId = initData.result.sessionId;
  console.log("已连接到服务器，会话ID:", sessionId);

  // 2. 发送初始化完成通知
  const initNotification = {
    jsonrpc: "2.0",
    method: "initialized",
    params: {}
  };

  return fetch(serverUrl, {
    method: "POST",
    headers: headers,
    body: JSON.stringify(initNotification)
  }).then(() => {
    console.log("已发送初始化完成通知");
    return sessionId;
  });
})
.then(sessionId => {
  // 3. 获取工具列表
  const toolsRequest = {
    jsonrpc: "2.0",
    id: generateUUID(),
    method: "tools/list",
    params: {
      sessionId: sessionId
    }
  };

  return fetch(serverUrl, {
    method: "POST",
    headers: headers,
    body: JSON.stringify(toolsRequest)
  })
  .then(response => response.json())
  .then(toolsData => {
    const tools = toolsData.result.tools || [];
    console.log("获取到工具列表:", tools.length, "个工具");
    return sessionId;
  });
})
.then(sessionId => {
  // 4. 调用工具
  const toolRequest = {
    jsonrpc: "2.0",
    id: generateUUID(),
    method: "tools/call",
    params: {
      sessionId: sessionId,
      name: "${selectedTool}",
      arguments: ${JSON.stringify(form.getFieldsValue(), null, 4)}
    }
  };

  return fetch(serverUrl, {
    method: "POST",
    headers: headers,
    body: JSON.stringify(toolRequest)
  })
  .then(response => response.json())
  .then(result => {
    console.log("工具调用结果:", result);
    return { sessionId, result };
  });
})
.then(({ sessionId, result }) => {
  // 5. 关闭会话
  const shutdownRequest = {
    jsonrpc: "2.0",
    id: generateUUID(),
    method: "shutdown",
    params: {
      sessionId: sessionId
    }
  };

  return fetch(serverUrl, {
    method: "POST",
    headers: headers,
    body: JSON.stringify(shutdownRequest)
  }).then(() => {
    console.log("会话已关闭");
  });
})
.catch(error => console.error("Error:", error));`}
                  </SyntaxHighlighter>
                  <Button
                    icon={<CopyOutlined />}
                    size="small"
                    style={{ position: 'absolute', top: 10, right: 10 }}
                    onClick={() => copyToClipboard(`// MCP服务器地址
const serverUrl = "${window.location.origin}/api/v1/project/plugin/sapi/servers/${selectedServer}/mcp";
const headers = {
  "Content-Type": "application/json",
  "Authorization": "Bearer ${apiToken || "YOUR_API_TOKEN"}"
};

// 生成UUID
function generateUUID() {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    var r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}

// 1. 初始化连接
const initRequest = {
  jsonrpc: "2.0",
  id: generateUUID(),
  method: "initialize",
  params: {
    protocolVersion: "2025-03-26",
    capabilities: {
      tools: {
        listChanged: true
      }
    }
  }
};

fetch(serverUrl, {
  method: "POST",
  headers: headers,
  body: JSON.stringify(initRequest)
})
.then(response => response.json())
.then(initData => {
  const sessionId = initData.result.sessionId;
  console.log("已连接到服务器，会话ID:", sessionId);

  // 2. 发送初始化完成通知
  const initNotification = {
    jsonrpc: "2.0",
    method: "initialized",
    params: {}
  };

  return fetch(serverUrl, {
    method: "POST",
    headers: headers,
    body: JSON.stringify(initNotification)
  }).then(() => {
    console.log("已发送初始化完成通知");
    return sessionId;
  });
})
.then(sessionId => {
  // 3. 获取工具列表
  const toolsRequest = {
    jsonrpc: "2.0",
    id: generateUUID(),
    method: "tools/list",
    params: {
      sessionId: sessionId
    }
  };

  return fetch(serverUrl, {
    method: "POST",
    headers: headers,
    body: JSON.stringify(toolsRequest)
  })
  .then(response => response.json())
  .then(toolsData => {
    const tools = toolsData.result.tools || [];
    console.log("获取到工具列表:", tools.length, "个工具");
    return sessionId;
  });
})
.then(sessionId => {
  // 4. 调用工具
  const toolRequest = {
    jsonrpc: "2.0",
    id: generateUUID(),
    method: "tools/call",
    params: {
      sessionId: sessionId,
      name: "${selectedTool}",
      arguments: ${JSON.stringify(form.getFieldsValue(), null, 4)}
    }
  };

  return fetch(serverUrl, {
    method: "POST",
    headers: headers,
    body: JSON.stringify(toolRequest)
  })
  .then(response => response.json())
  .then(result => {
    console.log("工具调用结果:", result);
    return { sessionId, result };
  });
})
.then(({ sessionId, result }) => {
  // 5. 关闭会话
  const shutdownRequest = {
    jsonrpc: "2.0",
    id: generateUUID(),
    method: "shutdown",
    params: {
      sessionId: sessionId
    }
  };

  return fetch(serverUrl, {
    method: "POST",
    headers: headers,
    body: JSON.stringify(shutdownRequest)
  }).then(() => {
    console.log("会话已关闭");
  });
})
.catch(error => console.error("Error:", error));`)}
                  />
                </div>
              </TabPane>
              <TabPane tab="cURL MCP客户端" key="curl">
                <div style={{ position: 'relative' }}>
                  <SyntaxHighlighter language="bash" style={vs2015}>
                    {`# 1. 初始化连接
curl -X POST "${window.location.origin}/api/v1/project/plugin/sapi/servers/${selectedServer}/mcp" \\
  -H "Content-Type: application/json" \\
  -H "Authorization: Bearer ${apiToken || "YOUR_API_TOKEN"}" \\
  -d '{
    "jsonrpc": "2.0",
    "id": "'$(uuidgen || cat /proc/sys/kernel/random/uuid)'",
    "method": "initialize",
    "params": {
      "protocolVersion": "2025-03-26",
      "capabilities": {
        "tools": {
          "listChanged": true
        }
      }
    }
  }'

# 保存会话ID (假设使用jq解析JSON)
SESSION_ID=$(curl -s -X POST "${window.location.origin}/api/v1/project/plugin/sapi/servers/${selectedServer}/mcp" \\
  -H "Content-Type: application/json" \\
  -H "Authorization: Bearer ${apiToken || "YOUR_API_TOKEN"}" \\
  -d '{
    "jsonrpc": "2.0",
    "id": "'$(uuidgen || cat /proc/sys/kernel/random/uuid)'",
    "method": "initialize",
    "params": {
      "protocolVersion": "2025-03-26",
      "capabilities": {
        "tools": {
          "listChanged": true
        }
      }
    }
  }' | jq -r '.result.sessionId')
echo "已连接到服务器，会话ID: $SESSION_ID"

# 2. 发送初始化完成通知
curl -X POST "${window.location.origin}/api/v1/project/plugin/sapi/servers/${selectedServer}/mcp" \\
  -H "Content-Type: application/json" \\
  -H "Authorization: Bearer ${apiToken || "YOUR_API_TOKEN"}" \\
  -d '{
    "jsonrpc": "2.0",
    "method": "initialized",
    "params": {}
  }'
echo "已发送初始化完成通知"

# 3. 获取工具列表
curl -X POST "${window.location.origin}/api/v1/project/plugin/sapi/servers/${selectedServer}/mcp" \\
  -H "Content-Type: application/json" \\
  -H "Authorization: Bearer ${apiToken || "YOUR_API_TOKEN"}" \\
  -d '{
    "jsonrpc": "2.0",
    "id": "'$(uuidgen || cat /proc/sys/kernel/random/uuid)'",
    "method": "tools/list",
    "params": {
      "sessionId": "'$SESSION_ID'"
    }
  }' | jq
echo "已获取工具列表"

# 4. 调用工具
curl -X POST "${window.location.origin}/api/v1/project/plugin/sapi/servers/${selectedServer}/mcp" \\
  -H "Content-Type: application/json" \\
  -H "Authorization: Bearer ${apiToken || "YOUR_API_TOKEN"}" \\
  -d '{
    "jsonrpc": "2.0",
    "id": "'$(uuidgen || cat /proc/sys/kernel/random/uuid)'",
    "method": "tools/call",
    "params": {
      "sessionId": "'$SESSION_ID'",
      "name": "${selectedTool}",
      "arguments": ${JSON.stringify(form.getFieldsValue(), null, 2)}
    }
  }' | jq
echo "已调用工具"

# 5. 关闭会话
curl -X POST "${window.location.origin}/api/v1/project/plugin/sapi/servers/${selectedServer}/mcp" \\
  -H "Content-Type: application/json" \\
  -H "Authorization: Bearer ${apiToken || "YOUR_API_TOKEN"}" \\
  -d '{
    "jsonrpc": "2.0",
    "id": "'$(uuidgen || cat /proc/sys/kernel/random/uuid)'",
    "method": "shutdown",
    "params": {
      "sessionId": "'$SESSION_ID'"
    }
  }'
echo "会话已关闭"`}
                  </SyntaxHighlighter>
                  <Button
                    icon={<CopyOutlined />}
                    size="small"
                    style={{ position: 'absolute', top: 10, right: 10 }}
                    onClick={() => copyToClipboard(`# 1. 初始化连接
curl -X POST "${window.location.origin}/api/v1/project/plugin/sapi/servers/${selectedServer}/mcp" \\
  -H "Content-Type: application/json" \\
  -H "Authorization: Bearer ${apiToken || "YOUR_API_TOKEN"}" \\
  -d '{
    "jsonrpc": "2.0",
    "id": "'$(uuidgen || cat /proc/sys/kernel/random/uuid)'",
    "method": "initialize",
    "params": {
      "protocolVersion": "2025-03-26",
      "capabilities": {
        "tools": {
          "listChanged": true
        }
      }
    }
  }'

# 保存会话ID (假设使用jq解析JSON)
SESSION_ID=$(curl -s -X POST "${window.location.origin}/api/v1/project/plugin/sapi/servers/${selectedServer}/mcp" \\
  -H "Content-Type: application/json" \\
  -H "Authorization: Bearer ${apiToken || "YOUR_API_TOKEN"}" \\
  -d '{
    "jsonrpc": "2.0",
    "id": "'$(uuidgen || cat /proc/sys/kernel/random/uuid)'",
    "method": "initialize",
    "params": {
      "protocolVersion": "2025-03-26",
      "capabilities": {
        "tools": {
          "listChanged": true
        }
      }
    }
  }' | jq -r '.result.sessionId')
echo "已连接到服务器，会话ID: $SESSION_ID"

# 2. 发送初始化完成通知
curl -X POST "${window.location.origin}/api/v1/project/plugin/sapi/servers/${selectedServer}/mcp" \\
  -H "Content-Type: application/json" \\
  -H "Authorization: Bearer ${apiToken || "YOUR_API_TOKEN"}" \\
  -d '{
    "jsonrpc": "2.0",
    "method": "initialized",
    "params": {}
  }'
echo "已发送初始化完成通知"

# 3. 获取工具列表
curl -X POST "${window.location.origin}/api/v1/project/plugin/sapi/servers/${selectedServer}/mcp" \\
  -H "Content-Type: application/json" \\
  -H "Authorization: Bearer ${apiToken || "YOUR_API_TOKEN"}" \\
  -d '{
    "jsonrpc": "2.0",
    "id": "'$(uuidgen || cat /proc/sys/kernel/random/uuid)'",
    "method": "tools/list",
    "params": {
      "sessionId": "'$SESSION_ID'"
    }
  }' | jq
echo "已获取工具列表"

# 4. 调用工具
curl -X POST "${window.location.origin}/api/v1/project/plugin/sapi/servers/${selectedServer}/mcp" \\
  -H "Content-Type: application/json" \\
  -H "Authorization: Bearer ${apiToken || "YOUR_API_TOKEN"}" \\
  -d '{
    "jsonrpc": "2.0",
    "id": "'$(uuidgen || cat /proc/sys/kernel/random/uuid)'",
    "method": "tools/call",
    "params": {
      "sessionId": "'$SESSION_ID'",
      "name": "${selectedTool}",
      "arguments": ${JSON.stringify(form.getFieldsValue(), null, 2)}
    }
  }' | jq
echo "已调用工具"

# 5. 关闭会话
curl -X POST "${window.location.origin}/api/v1/project/plugin/sapi/servers/${selectedServer}/mcp" \\
  -H "Content-Type: application/json" \\
  -H "Authorization: Bearer ${apiToken || "YOUR_API_TOKEN"}" \\
  -d '{
    "jsonrpc": "2.0",
    "id": "'$(uuidgen || cat /proc/sys/kernel/random/uuid)'",
    "method": "shutdown",
    "params": {
      "sessionId": "'$SESSION_ID'"
    }
  }'
echo "会话已关闭"`)}
                  />
                </div>
              </TabPane>
            </Tabs>
          </Panel>
        </Collapse>
      </div>
    );
  };

  // 渲染历史记录
  const renderHistory = () => {
    if (!testHistory.length) return (
      <Alert
        message="暂无测试历史"
        description="您的测试历史将显示在这里"
        type="info"
        showIcon
      />
    );

    return (
      <div>
        <Table
          dataSource={testHistory}
          rowKey="id"
          pagination={false}
          columns={[
            {
              title: '时间',
              dataIndex: 'timestamp',
              key: 'timestamp',
              render: text => new Date(text).toLocaleString()
            },
            {
              title: '服务器',
              dataIndex: 'server',
              key: 'server',
            },
            {
              title: '工具',
              dataIndex: 'tool',
              key: 'tool',
            },
            {
              title: '操作',
              key: 'action',
              render: (_, record) => (
                <Space>
                  <Button
                    size="small"
                    onClick={() => {
                      // 设置服务器和工具
                      const serverObj = servers.find(s => s.name === record.server);
                      if (serverObj) {
                        setSelectedServer(serverObj.id);
                      }
                      setSelectedTool(record.tool);

                      // 设置表单值
                      form.setFieldsValue(record.parameters);

                      // 设置测试结果
                      setTestResult(record.result);

                      // 切换到请求标签页
                      setActiveTab('1');
                    }}
                  >
                    重新测试
                  </Button>
                  <Button
                    size="small"
                    onClick={() => {
                      // 设置测试结果
                      setTestResult(record.result);

                      // 切换到结果标签页
                      setActiveTab('2');
                    }}
                  >
                    查看结果
                  </Button>
                </Space>
              )
            }
          ]}
        />
      </div>
    );
  };

  // 渲染连接表单
  const renderConnectForm = () => {
    return (
      <Form
        form={tokenForm}
        layout="vertical"
        onFinish={handleConnect}
      >
        <Form.Item
          name="apiToken"
          label="API令牌"
          tooltip="用于连接MCP服务器的API令牌"
        >
          <Password
            placeholder="输入API令牌（可选）"
            prefix={<KeyOutlined />}
            allowClear
          />
        </Form.Item>

        <Form.Item>
          <Button
            type="primary"
            htmlType="submit"
            loading={testLoading}
            icon={<LinkOutlined />}
          >
            连接到MCP服务器
          </Button>
        </Form.Item>
      </Form>
    );
  };

  // 渲染工具列表
  const renderToolsList = () => {
    if (!sessionId) {
      return (
        <Alert
          message="未连接到MCP服务器"
          description="请先连接到MCP服务器"
          type="warning"
          showIcon
        />
      );
    }

    return (
      <div>
        <Space direction="vertical" style={{ width: '100%' }}>
          <Alert
            message={`已连接到 ${serverInfo?.name || '未知服务器'}`}
            description={`会话ID: ${sessionId}`}
            type="success"
            showIcon
          />

          <Button
            type="primary"
            onClick={handleGetTools}
            loading={testLoading}
            icon={<ToolOutlined />}
          >
            获取工具列表
          </Button>

          {tools.length > 0 && (
            <List
              size="small"
              header={<div>可用工具列表</div>}
              bordered
              dataSource={tools}
              renderItem={tool => (
                <List.Item
                  actions={[
                    <Button
                      key="select"
                      type="link"
                      onClick={() => {
                        handleToolChange(tool.name);
                        setCurrentStep(2);
                        setActiveTab('1');
                      }}
                    >
                      选择
                    </Button>
                  ]}
                >
                  <List.Item.Meta
                    title={tool.name}
                    description={tool.description}
                  />
                </List.Item>
              )}
            />
          )}
        </Space>
      </div>
    );
  };

  // 渲染步骤内容
  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return renderConnectForm();
      case 1:
        return renderToolsList();
      case 2:
        return renderParameterForm();
      case 3:
        return (
          <div>
            <Alert
              message="工具调用成功"
              description="您可以在结果标签页查看调用结果"
              type="success"
              showIcon
              style={{ marginBottom: 16 }}
            />
            <Button
              type="primary"
              onClick={handleCloseSession}
              loading={testLoading}
              icon={<DisconnectOutlined />}
            >
              关闭会话
            </Button>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <Card>
      <Title level={4}>API测试</Title>
      <Paragraph>
        使用此工具测试API调用，验证参数和响应。按照MCP协议标准流程进行测试。
      </Paragraph>

      <Spin spinning={loading}>
        <Space direction="vertical" style={{ width: '100%', marginBottom: 16 }}>
          <Card size="small" title="MCP服务器">
            <Space wrap>
              <div>
                <Text>选择服务器:</Text>
                <Space>
                  <Select
                    style={{ width: 200, marginLeft: 8 }}
                    value={selectedServer}
                    onChange={setSelectedServer}
                    placeholder="选择MCP服务器"
                    disabled={loading || !servers.length || sessionId}
                  >
                    {servers.map(server => (
                      <Option key={server.id} value={server.id}>
                        {server.name}
                        <Tag
                          color={server.status === 'running' ? 'green' : 'red'}
                          style={{ marginLeft: 8 }}
                        >
                          {server.status === 'running' ? '运行中' : '已停止'}
                        </Tag>
                      </Option>
                    ))}
                  </Select>
                  <Button
                    icon={<ReloadOutlined />}
                    onClick={refreshServers}
                    loading={loading}
                    disabled={sessionId}
                    title="刷新服务器列表"
                  />
                </Space>
              </div>

              {sessionId && (
                <Tag color="green">已连接 - 会话ID: {sessionId.substring(0, 8)}...</Tag>
              )}
            </Space>
          </Card>

          <Card size="small" title="MCP协议流程">
            <Steps
              current={currentStep}
              items={testSteps.map(step => ({
                title: step.title,
                description: step.description,
                status: step.status,
                icon: step.icon
              }))}
              style={{ marginBottom: 16 }}
            />

            <Card size="small" title={testSteps[currentStep].title}>
              {renderStepContent()}
            </Card>
          </Card>

          <Tabs activeKey={activeTab} onChange={setActiveTab}>
            <TabPane
              tab={<span><ApiOutlined /> 请求</span>}
              key="1"
            >
              {currentStep === 2 ? renderParameterForm() : (
                <Alert
                  message="请按照步骤操作"
                  description={`当前步骤: ${testSteps[currentStep].title} - ${testSteps[currentStep].description}`}
                  type="info"
                  showIcon
                />
              )}
            </TabPane>

            <TabPane
              tab={<span><CodeOutlined /> 结果</span>}
              key="2"
            >
              {renderTestResult()}
            </TabPane>

            <TabPane
              tab={<span><HistoryOutlined /> 历史记录</span>}
              key="3"
            >
              {renderHistory()}
            </TabPane>
          </Tabs>
        </Space>
      </Spin>
    </Card>
  );
};

export default TestApi;
