import React, { useState, useEffect, useRef } from 'react';
import {
  Layout,
  Card,
  Button,
  Table,
  Space,
  Input,
  Upload,
  Modal,
  Form,
  message,
  Progress,
  Breadcrumb,
  Dropdown,
  Menu,
  Tooltip,
  Typography,
  Divider,
  Row,
  Col,
  Statistic,
  Tag,
  Spin,
  Empty,
  Select
} from 'antd';
import {
  FolderOutlined,
  FileOutlined,
  UploadOutlined,
  DownloadOutlined,
  ShareAltOutlined,
  DeleteOutlined,
  PlusOutlined,
  SearchOutlined,
  FileImageOutlined,
  FileTextOutlined,
  FilePdfOutlined,
  FileUnknownOutlined,
  EyeOutlined,
  FolderAddOutlined,
  ArrowLeftOutlined,
  InfoCircleOutlined,
  LinkOutlined,
  LockOutlined,
  ClockCircleOutlined,
  CopyOutlined,
  MoreOutlined
} from '@ant-design/icons';
import { useNavigate, useParams, Link } from 'react-router-dom';
import * as projectSpaceService from '../../../services/api/project/space';
import RecycleBin from '../../../components/RecycleBin';

const { Content } = Layout;
const { Title, Text } = Typography;
const { Dragger } = Upload;
const { Search } = Input;

/**
 * 项目空间组件
 */
const ProjectSpace = () => {
  const [loading, setLoading] = useState(false);
  const [files, setFiles] = useState([]);
  const [folders, setFolders] = useState([]);
  const [currentFolder, setCurrentFolder] = useState(null);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0
  });
  const [searchText, setSearchText] = useState('');
  const [fileType, setFileType] = useState(null);
  const [sortBy, setSortBy] = useState('created_at');
  const [sortOrder, setSortOrder] = useState('desc');
  const [uploadModalVisible, setUploadModalVisible] = useState(false);
  const [folderModalVisible, setFolderModalVisible] = useState(false);
  const [shareModalVisible, setShareModalVisible] = useState(false);
  const [previewModalVisible, setPreviewModalVisible] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploading, setUploading] = useState(false);
  const [selectedFile, setSelectedFile] = useState(null);
  const [shareInfo, setShareInfo] = useState(null);
  const [storageUsage, setStorageUsage] = useState(null);
  const [breadcrumbs, setBreadcrumbs] = useState([{ name: '根目录', id: null }]);
  const [aiRecognitionModalVisible, setAiRecognitionModalVisible] = useState(false);
  const [aiRecognitionLoading, setAiRecognitionLoading] = useState(false);
  const [aiRecognitionResult, setAiRecognitionResult] = useState(null);
  const [customPrompt, setCustomPrompt] = useState('');
  const [recycleBinVisible, setRecycleBinVisible] = useState(false);

  const [folderForm] = Form.useForm();
  const [shareForm] = Form.useForm();
  const navigate = useNavigate();

  // 文件上传列表
  const [fileList, setFileList] = useState([]);

  // 获取文件列表
  const fetchFiles = async (page = 1, pageSize = 20) => {
    setLoading(true);
    try {
      const params = {
        page,
        page_size: pageSize,
        folder_id: currentFolder?.id || null,
        search: searchText || null,
        file_type: fileType || null,
        sort_by: sortBy,
        sort_order: sortOrder
      };

      const response = await projectSpaceService.getFiles(params);

      if (response.success) {
        setFiles(response.data.files || []);
        setFolders(response.data.folders || []);
        setCurrentFolder(response.data.current_folder);
        setPagination({
          current: page,
          pageSize: pageSize,
          total: response.data.pagination.total
        });

        // 更新面包屑
        if (response.data.current_folder) {
          updateBreadcrumbs(response.data.current_folder);
        } else {
          setBreadcrumbs([{ name: '根目录', id: null }]);
        }
      } else {
        message.error('获取文件列表失败');
      }
    } catch (error) {
      console.error('获取文件列表失败:', error);
      message.error('获取文件列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取存储使用情况
  const fetchStorageUsage = async () => {
    try {
      const response = await projectSpaceService.getStorageUsage();

      if (response.success) {
        setStorageUsage(response.data);
      } else {
        message.error('获取存储使用情况失败');
      }
    } catch (error) {
      console.error('获取存储使用情况失败:', error);
      message.error('获取存储使用情况失败');
    }
  };

  // 更新面包屑
  const updateBreadcrumbs = (folder) => {
    // 如果是根目录
    if (!folder || !folder.path) {
      setBreadcrumbs([{ name: '根目录', id: null }]);
      return;
    }

    // 解析路径
    const pathParts = folder.path.split('/').filter(Boolean);

    // 构建面包屑
    const newBreadcrumbs = [{ name: '根目录', id: null }];

    // 添加中间路径（这里需要后端提供完整的路径信息）
    // 在实际应用中，可能需要额外的API调用来获取完整的路径信息

    // 添加当前文件夹
    newBreadcrumbs.push({ name: folder.name, id: folder.id });

    setBreadcrumbs(newBreadcrumbs);
  };

  // 处理文件夹点击
  const handleFolderClick = (folderId) => {
    // 重置分页
    setPagination({
      ...pagination,
      current: 1
    });

    // 更新当前文件夹
    setCurrentFolder({ id: folderId });

    // 获取文件列表
    fetchFiles(1, pagination.pageSize);
  };

  // 处理面包屑点击
  const handleBreadcrumbClick = (folderId) => {
    // 重置分页
    setPagination({
      ...pagination,
      current: 1
    });

    // 更新当前文件夹
    setCurrentFolder(folderId ? { id: folderId } : null);

    // 获取文件列表
    fetchFiles(1, pagination.pageSize);
  };

  // 处理搜索
  const handleSearch = (value) => {
    setSearchText(value);
    setPagination({
      ...pagination,
      current: 1
    });
    fetchFiles(1, pagination.pageSize);
  };

  // 处理文件类型筛选
  const handleFileTypeChange = (type) => {
    setFileType(type);
    setPagination({
      ...pagination,
      current: 1
    });
    fetchFiles(1, pagination.pageSize);
  };

  // 处理排序
  const handleSortChange = (sorter) => {
    setSortBy(sorter.field);
    setSortOrder(sorter.order === 'ascend' ? 'asc' : 'desc');
    fetchFiles(pagination.current, pagination.pageSize);
  };

  // 处理分页
  const handleTableChange = (pagination, filters, sorter) => {
    setPagination(pagination);
    fetchFiles(pagination.current, pagination.pageSize);
  };

  // 处理文件上传
  const handleUpload = async () => {
    if (fileList.length === 0) {
      message.warning('请选择要上传的文件');
      return;
    }

    setUploading(true);

    try {
      // 逐个上传文件
      for (let i = 0; i < fileList.length; i++) {
        const file = fileList[i].originFileObj;

        await projectSpaceService.uploadFile(
          file,
          currentFolder?.path || '/',
          '',
          false,
          (percent) => {
            setUploadProgress(percent);
          }
        );
      }

      message.success('文件上传成功');
      setUploadModalVisible(false);
      setFileList([]);
      setUploadProgress(0);

      // 刷新文件列表
      fetchFiles(pagination.current, pagination.pageSize);
    } catch (error) {
      console.error('文件上传失败:', error);
      message.error('文件上传失败');
    } finally {
      setUploading(false);
    }
  };

  // 处理创建文件夹
  const handleCreateFolder = async () => {
    try {
      const values = await folderForm.validateFields();

      const response = await projectSpaceService.createFolder(
        values.folderName,
        currentFolder?.id || null
      );

      if (response.success) {
        message.success('文件夹创建成功');
        setFolderModalVisible(false);
        folderForm.resetFields();

        // 刷新文件列表
        fetchFiles(pagination.current, pagination.pageSize);
      } else {
        message.error('文件夹创建失败');
      }
    } catch (error) {
      console.error('创建文件夹失败:', error);
      message.error('创建文件夹失败');
    }
  };

  // 处理文件删除
  const handleDeleteFile = async (fileId) => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除此文件吗？',
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        try {
          const response = await projectSpaceService.deleteFile(fileId);

          if (response.success) {
            message.success('文件删除成功');

            // 刷新文件列表
            fetchFiles(pagination.current, pagination.pageSize);
          } else {
            message.error('文件删除失败');
          }
        } catch (error) {
          console.error('删除文件失败:', error);
          message.error('删除文件失败');
        }
      }
    });
  };

  // 处理文件分享
  const handleShareFile = async (file) => {
    setSelectedFile(file);
    setShareModalVisible(true);
    shareForm.resetFields();
  };

  // 创建分享链接
  const handleCreateShare = async () => {
    try {
      const values = await shareForm.validateFields();

      const response = await projectSpaceService.createFileShare(
        selectedFile.id,
        values.expiresInDays,
        values.password
      );

      if (response.success) {
        setShareInfo(response.data);
        message.success('分享链接创建成功');
      } else {
        message.error('创建分享链接失败');
      }
    } catch (error) {
      console.error('创建分享链接失败:', error);
      message.error('创建分享链接失败');
    }
  };

  // 复制分享链接
  const handleCopyShareLink = () => {
    if (shareInfo && shareInfo.share_url) {
      // 确保使用正确的URL格式
      const shareUrl = window.location.origin + shareInfo.share_url;
      console.log('分享链接:', shareUrl);
      navigator.clipboard.writeText(shareUrl)
        .then(() => {
          message.success('分享链接已复制到剪贴板');
        })
        .catch(() => {
          message.error('复制失败，请手动复制');
        });
    }
  };

  // 处理文件预览
  const handlePreviewFile = (file) => {
    setSelectedFile(file);
    setPreviewModalVisible(true);
  };

  // 处理文件下载
  const handleDownloadFile = (file) => {
    const downloadUrl = projectSpaceService.getFileDownloadUrl(file.id);
    window.open(downloadUrl, '_blank');
  };

  // AI识图功能
  const handleAiRecognition = async (file) => {
    setSelectedFile(file);
    setAiRecognitionModalVisible(true);
    setAiRecognitionResult(null);
    setCustomPrompt('');
  };

  const performAiRecognition = async () => {
    if (!selectedFile) return;

    try {
      setAiRecognitionLoading(true);
      const response = await projectSpaceService.aiRecognizeFile(
        selectedFile.id, 
        customPrompt || undefined
      );

      if (response.success) {
        setAiRecognitionResult(response.data);
        message.success('AI识图完成');
      } else {
        message.error('AI识图失败：' + response.message);
      }
    } catch (error) {
      console.error('AI识图失败:', error);
      message.error('AI识图失败：' + error.message);
    } finally {
      setAiRecognitionLoading(false);
    }
  };

  // 初始化
  useEffect(() => {
    fetchFiles();
    fetchStorageUsage();
  }, []);

  // 文件图标映射
  const getFileIcon = (file) => {
    if (file.mime_type.startsWith('image/')) {
      return <FileImageOutlined />;
    } else if (file.mime_type.startsWith('text/')) {
      return <FileTextOutlined />;
    } else if (file.mime_type === 'application/pdf') {
      return <FilePdfOutlined />;
    } else {
      return <FileUnknownOutlined />;
    }
  };

  // 文件大小格式化
  const formatFileSize = (size) => {
    if (size < 1024) {
      return size + ' B';
    } else if (size < 1024 * 1024) {
      return (size / 1024).toFixed(2) + ' KB';
    } else if (size < 1024 * 1024 * 1024) {
      return (size / (1024 * 1024)).toFixed(2) + ' MB';
    } else {
      return (size / (1024 * 1024 * 1024)).toFixed(2) + ' GB';
    }
  };

  // 显示回收站
  const showRecycleBin = () => {
    setRecycleBinVisible(true);
  };

  // 关闭回收站
  const closeRecycleBin = () => {
    setRecycleBinVisible(false);
  };

  // 从回收站还原文件后刷新列表
  const handleRestoreFile = () => {
    fetchFiles();
    fetchStorageUsage();
  };

  // 文件列表列定义
  const columns = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <Space>
          {record.type === 'folder' ? (
            <>
              <FolderOutlined style={{ color: '#1890ff' }} />
              <a onClick={() => handleFolderClick(record.id)}>{text}</a>
            </>
          ) : (
            <>
              {getFileIcon(record)}
              <span>{text}</span>
            </>
          )}
        </Space>
      )
    },
    {
      title: '大小',
      dataIndex: 'size',
      key: 'size',
      render: (size, record) => (
        record.type === 'folder' ? '-' : formatFileSize(size)
      )
    },
    {
      title: '类型',
      dataIndex: 'mime_type',
      key: 'mime_type',
      render: (mimeType, record) => (
        record.type === 'folder' ? '文件夹' : (
          mimeType.split('/')[1].toUpperCase()
        )
      )
    },
    {
      title: '上传者',
      dataIndex: 'uploader_name',
      key: 'uploader_name',
      render: (text) => text || '-'
    },
    {
      title: '上传时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (text) => text ? new Date(text).toLocaleString() : '-'
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        record.type === 'folder' ? (
          <Button type="link" onClick={() => handleFolderClick(record.id)}>
            打开
          </Button>
        ) : (
          <Space>
            {record.preview_available && (
              <Tooltip title="预览">
                <Button
                  type="link"
                  icon={<EyeOutlined />}
                  onClick={() => handlePreviewFile(record)}
                />
              </Tooltip>
            )}
            <Tooltip title="下载">
              <Button
                type="link"
                icon={<DownloadOutlined />}
                onClick={() => handleDownloadFile(record)}
              />
            </Tooltip>
            {record.mime_type && record.mime_type.startsWith('image/') && (
              <Tooltip title="AI识图">
                <Button
                  type="link"
                  icon={<EyeOutlined />}
                  style={{ color: '#722ed1' }}
                  onClick={() => handleAiRecognition(record)}
                />
              </Tooltip>
            )}
            <Tooltip title="分享">
              <Button
                type="link"
                icon={<ShareAltOutlined />}
                onClick={() => handleShareFile(record)}
              />
            </Tooltip>
            <Tooltip title="删除">
              <Button
                type="link"
                danger
                icon={<DeleteOutlined />}
                onClick={() => handleDeleteFile(record.id)}
              />
            </Tooltip>
          </Space>
        )
      )
    }
  ];

  // 合并文件夹和文件数据
  const tableData = [
    ...folders.map(folder => ({ ...folder, type: 'folder' })),
    ...files
  ];

  return (
    <Layout className="site-layout">
      <Content style={{ margin: '0 16px' }}>
        <Card
          title={
            <Space>
              <Title level={4}>项目空间</Title>
              <Breadcrumb separator=">">
                {breadcrumbs.map((item, index) => (
                  <Breadcrumb.Item key={index}>
                    <a onClick={() => handleBreadcrumbClick(item.id)}>{item.name}</a>
                  </Breadcrumb.Item>
                ))}
              </Breadcrumb>
            </Space>
          }
          extra={
            <Space>
              <Button
                type="primary"
                icon={<UploadOutlined />}
                onClick={() => setUploadModalVisible(true)}
              >
                上传文件
              </Button>
              <Button
                icon={<FolderAddOutlined />}
                onClick={() => setFolderModalVisible(true)}
              >
                新建文件夹
              </Button>
              <Button
                icon={<DeleteOutlined />}
                onClick={showRecycleBin}
              >
                回收站
              </Button>
            </Space>
          }
        >
          <Row gutter={16} style={{ marginBottom: 16 }}>
            <Col span={8}>
              <Search
                placeholder="搜索文件"
                onSearch={handleSearch}
                style={{ width: '100%' }}
              />
            </Col>
            <Col span={16}>
              <Space>
                <Button
                  type={fileType === null ? 'primary' : 'default'}
                  onClick={() => handleFileTypeChange(null)}
                >
                  全部
                </Button>
                <Button
                  type={fileType === 'image' ? 'primary' : 'default'}
                  icon={<FileImageOutlined />}
                  onClick={() => handleFileTypeChange('image')}
                >
                  图片
                </Button>
                <Button
                  type={fileType === 'document' ? 'primary' : 'default'}
                  icon={<FileTextOutlined />}
                  onClick={() => handleFileTypeChange('document')}
                >
                  文档
                </Button>
                <Button
                  type={fileType === 'other' ? 'primary' : 'default'}
                  icon={<FileUnknownOutlined />}
                  onClick={() => handleFileTypeChange('other')}
                >
                  其他
                </Button>
              </Space>
            </Col>
          </Row>

          {storageUsage && (
            <Row gutter={16} style={{ marginBottom: 16 }}>
              <Col span={6}>
                <Statistic
                  title="存储空间"
                  value={formatFileSize(storageUsage.total)}
                  suffix={
                    <Tooltip title="总存储空间">
                      <InfoCircleOutlined />
                    </Tooltip>
                  }
                />
              </Col>
              <Col span={6}>
                <Statistic
                  title="已使用"
                  value={formatFileSize(storageUsage.used)}
                  suffix={
                    <Tooltip title="已使用存储空间">
                      <InfoCircleOutlined />
                    </Tooltip>
                  }
                />
              </Col>
              <Col span={6}>
                <Statistic
                  title="可用空间"
                  value={formatFileSize(storageUsage.available)}
                  suffix={
                    <Tooltip title="可用存储空间">
                      <InfoCircleOutlined />
                    </Tooltip>
                  }
                />
              </Col>
              <Col span={6}>
                <Statistic
                  title="使用率"
                  value={storageUsage.percentage}
                  suffix="%"
                  prefix={
                    <Progress
                      type="circle"
                      percent={storageUsage.percentage}
                      width={20}
                      strokeColor={
                        storageUsage.percentage > 90 ? '#ff4d4f' :
                        storageUsage.percentage > 70 ? '#faad14' : '#52c41a'
                      }
                    />
                  }
                />
              </Col>
            </Row>
          )}

          <Table
            columns={columns}
            dataSource={tableData}
            rowKey="id"
            pagination={pagination}
            loading={loading}
            onChange={handleTableChange}
            locale={{
              emptyText: (
                <Empty
                  image={Empty.PRESENTED_IMAGE_SIMPLE}
                  description="暂无文件"
                />
              )
            }}
          />
        </Card>

        {/* 上传文件模态框 */}
        <Modal
          title="上传文件"
          open={uploadModalVisible}
          onCancel={() => {
            setUploadModalVisible(false);
            setFileList([]);
            setUploadProgress(0);
          }}
          footer={[
            <Button
              key="cancel"
              onClick={() => {
                setUploadModalVisible(false);
                setFileList([]);
                setUploadProgress(0);
              }}
            >
              取消
            </Button>,
            <Button
              key="upload"
              type="primary"
              loading={uploading}
              onClick={handleUpload}
              disabled={fileList.length === 0}
            >
              上传
            </Button>
          ]}
        >
          <Dragger
            multiple
            beforeUpload={(file) => {
              setFileList([...fileList, { uid: file.uid, name: file.name, originFileObj: file }]);
              return false;
            }}
            onRemove={(file) => {
              setFileList(fileList.filter((item) => item.uid !== file.uid));
            }}
            fileList={fileList}
            disabled={uploading}
          >
            <p className="ant-upload-drag-icon">
              <UploadOutlined />
            </p>
            <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
            <p className="ant-upload-hint">支持单个或批量上传</p>
          </Dragger>

          {uploading && (
            <div style={{ marginTop: 16 }}>
              <Progress percent={uploadProgress} status="active" />
            </div>
          )}
        </Modal>

        {/* 创建文件夹模态框 */}
        <Modal
          title="新建文件夹"
          open={folderModalVisible}
          onCancel={() => {
            setFolderModalVisible(false);
            folderForm.resetFields();
          }}
          onOk={handleCreateFolder}
        >
          <Form form={folderForm}>
            <Form.Item
              name="folderName"
              label="文件夹名称"
              rules={[{ required: true, message: '请输入文件夹名称' }]}
            >
              <Input placeholder="请输入文件夹名称" />
            </Form.Item>
          </Form>
        </Modal>

        {/* 文件分享模态框 */}
        <Modal
          title="分享文件"
          open={shareModalVisible}
          onCancel={() => {
            setShareModalVisible(false);
            setShareInfo(null);
            shareForm.resetFields();
          }}
          footer={shareInfo ? [
            <Button
              key="close"
              onClick={() => {
                setShareModalVisible(false);
                setShareInfo(null);
                shareForm.resetFields();
              }}
            >
              关闭
            </Button>
          ] : [
            <Button
              key="cancel"
              onClick={() => {
                setShareModalVisible(false);
                setShareInfo(null);
                shareForm.resetFields();
              }}
            >
              取消
            </Button>,
            <Button
              key="share"
              type="primary"
              onClick={handleCreateShare}
            >
              创建分享
            </Button>
          ]}
        >
          {shareInfo ? (
            <div>
              <div style={{ marginBottom: 16 }}>
                <Text strong>文件名：</Text> {selectedFile?.name}
              </div>
              <div style={{ marginBottom: 16 }}>
                <Text strong>分享链接：</Text>
                <div style={{ display: 'flex', marginTop: 8 }}>
                  <Input
                    value={window.location.origin + shareInfo.share_url}
                    readOnly
                    addonAfter={
                      <Tooltip title="复制链接">
                        <CopyOutlined onClick={handleCopyShareLink} />
                      </Tooltip>
                    }
                  />
                </div>
              </div>
              {shareInfo.has_password && (
                <div style={{ marginBottom: 16 }}>
                  <Text strong>访问密码：</Text> {shareForm.getFieldValue('password')}
                </div>
              )}
              {shareInfo.expires_at && (
                <div style={{ marginBottom: 16 }}>
                  <Text strong>过期时间：</Text> {new Date(shareInfo.expires_at).toLocaleString()}
                </div>
              )}
              <div style={{ marginTop: 16 }}>
                <Tag color="blue" icon={<InfoCircleOutlined />}>
                  分享链接已创建，可以发送给他人访问
                </Tag>
              </div>
            </div>
          ) : (
            <Form form={shareForm}>
              <Form.Item
                name="expiresInDays"
                label="过期时间"
              >
                <Select placeholder="选择过期时间">
                  <Select.Option value={1}>1天</Select.Option>
                  <Select.Option value={7}>7天</Select.Option>
                  <Select.Option value={30}>30天</Select.Option>
                  <Select.Option value={null}>永不过期</Select.Option>
                </Select>
              </Form.Item>
              <Form.Item
                name="password"
                label="访问密码"
                extra="如果设置密码，访问者需要输入密码才能查看文件"
              >
                <Input.Password placeholder="设置访问密码（可选）" />
              </Form.Item>
            </Form>
          )}
        </Modal>

        {/* 文件预览模态框 */}
        <Modal
          title={selectedFile?.name}
          open={previewModalVisible}
          onCancel={() => setPreviewModalVisible(false)}
          footer={null}
          width={800}
        >
          {selectedFile && (
            <div style={{ textAlign: 'center' }}>
              {selectedFile.mime_type.startsWith('image/') ? (
                <img
                  src={projectSpaceService.getFileDownloadUrl(selectedFile.id)}
                  alt={selectedFile.name}
                  style={{ maxWidth: '100%', maxHeight: '500px' }}
                />
              ) : selectedFile.mime_type === 'application/pdf' ? (
                <iframe
                  src={projectSpaceService.getFileDownloadUrl(selectedFile.id)}
                  width="100%"
                  height="500px"
                  title={selectedFile.name}
                />
              ) : (
                <div>
                  <p>此文件类型不支持预览</p>
                  <Button
                    type="primary"
                    icon={<DownloadOutlined />}
                    onClick={() => handleDownloadFile(selectedFile)}
                  >
                    下载文件
                  </Button>
                </div>
              )}
            </div>
          )}
        </Modal>

        {/* AI识图模态框 */}
        <Modal
          title={
            <Space>
              <EyeOutlined style={{ color: '#722ed1' }} />
              AI识图 - {selectedFile?.name}
            </Space>
          }
          open={aiRecognitionModalVisible}
          onCancel={() => {
            setAiRecognitionModalVisible(false);
            setAiRecognitionResult(null);
            setCustomPrompt('');
          }}
          footer={[
            <Button
              key="cancel"
              onClick={() => {
                setAiRecognitionModalVisible(false);
                setAiRecognitionResult(null);
                setCustomPrompt('');
              }}
            >
              关闭
            </Button>,
            !aiRecognitionResult && (
              <Button
                key="recognize"
                type="primary"
                loading={aiRecognitionLoading}
                onClick={performAiRecognition}
                icon={<EyeOutlined />}
              >
                开始识图
              </Button>
            )
          ]}
          width={900}
          style={{ top: 20 }}
        >
          <div style={{ maxHeight: '70vh', overflowY: 'auto' }}>
            {selectedFile && (
              <Row gutter={16}>
                <Col span={12}>
                  <div style={{ textAlign: 'center', marginBottom: 16 }}>
                    <img
                      src={projectSpaceService.getFileDownloadUrl(selectedFile.id)}
                      alt={selectedFile.name}
                      style={{ maxWidth: '100%', maxHeight: '300px', border: '1px solid #f0f0f0' }}
                    />
                  </div>
                </Col>
                <Col span={12}>
                  {!aiRecognitionResult ? (
                    <div>
                      <h4>自定义识别提示（可选）</h4>
                      <Input.TextArea
                        value={customPrompt}
                        onChange={(e) => setCustomPrompt(e.target.value)}
                        placeholder="输入自定义识别提示，留空将使用默认提示..."
                        rows={4}
                        style={{ marginBottom: 16 }}
                      />
                      <div style={{ color: '#666', fontSize: 12 }}>
                        <p><strong>默认识别内容包括：</strong></p>
                        <ul style={{ paddingLeft: 20, margin: 0 }}>
                          <li>图片内容描述</li>
                          <li>技术信息分析</li>
                          <li>文字识别提取</li>
                          <li>用途分析</li>
                          <li>关键信息提取</li>
                        </ul>
                      </div>
                    </div>
                  ) : (
                    <div>
                      <h4>AI识图结果</h4>
                      <div style={{ 
                        border: '1px solid #f0f0f0', 
                        borderRadius: 6, 
                        padding: 16, 
                        background: '#fafafa',
                        maxHeight: '400px',
                        overflowY: 'auto'
                      }}>
                        <pre style={{ 
                          whiteSpace: 'pre-wrap', 
                          margin: 0, 
                          fontFamily: 'inherit',
                          fontSize: 14,
                          lineHeight: 1.6
                        }}>
                          {aiRecognitionResult.recognition_content}
                        </pre>
                      </div>
                      <div style={{ marginTop: 12, color: '#666', fontSize: 12 }}>
                        识别时间：{aiRecognitionResult.recognized_at ? new Date(aiRecognitionResult.recognized_at).toLocaleString() : ''}
                      </div>
                    </div>
                  )}
                </Col>
              </Row>
            )}
          </div>
        </Modal>

        {/* 回收站组件 */}
        <RecycleBin
          visible={recycleBinVisible}
          onClose={closeRecycleBin}
          onRestore={handleRestoreFile}
        />
      </Content>
    </Layout>
  );
};

export default ProjectSpace;
