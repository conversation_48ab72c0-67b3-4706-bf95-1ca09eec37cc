import React, { useState, useEffect } from 'react';
import {
  Card, Row, Col, Table, Button, Space, Tag, Progress, 
  Statistic, Alert, Tabs, Select, DatePicker, Input,
  message, Tooltip, Badge, Empty, Spin
} from 'antd';
import {
  ShoppingCartOutlined, LineChartOutlined, DollarOutlined,
  UserOutlined, CalendarOutlined, Bar<PERSON>hartOutlined,
  Pie<PERSON>hartOutlined, SearchOutlined,
  FilterOutlined, ExportOutlined, RobotOutlined,
  ShopOutlined, AlertOutlined, SyncOutlined
} from '@ant-design/icons';
import { Line, Column, Pie } from '@ant-design/plots';
import { api } from '../../../../services/api';

const { TabPane } = Tabs;
const { Option } = Select;
const { RangePicker } = DatePicker;
const { Search } = Input;

/**
 * 采购分析组件
 * 分析采购成本、供应商表现、采购趋势等
 */
const PurchaseAnalysis = () => {
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('cost');
  const [purchaseData, setPurchaseData] = useState([]);
  const [costData, setCostData] = useState([]);
  const [supplierData, setSupplierData] = useState([]);
  const [trendData, setTrendData] = useState([]);
  const [frequencyData, setFrequencyData] = useState([]);
  const [filters, setFilters] = useState({
    supplier: 'all',
    category: 'all',
    dateRange: null,
    keyword: ''
  });
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    // 先设置模拟数据，避免组件挂载时数据为空
    generateMockData();
    fetchPurchaseAnalysisData();
  }, [filters]);

  // 获取采购分析数据
  const fetchPurchaseAnalysisData = async () => {
    try {
      setLoading(true);
      
      const projectId = localStorage.getItem('project_id');
      if (!projectId) {
        throw new Error('未找到项目ID');
      }
      
      const params = {
        supplier: filters.supplier,
        category: filters.category,
        dateRange: filters.dateRange,
        keyword: filters.keyword
      };

      const response = await api.get(`/project/${projectId}/business-analysis/purchase-analysis`, { params });
      
      if (response && response.data) {
        setPurchaseData(response.data.purchases || []);
        setCostData(response.data.costs || []);
        setSupplierData(response.data.suppliers || []);
        setTrendData(response.data.trends || []);
        setFrequencyData(response.data.frequency || []);
      }
    } catch (error) {
      console.error('获取采购分析数据失败:', error);
      // 使用模拟数据
      generateMockData();
    } finally {
      setLoading(false);
    }
  };

  // 生成模拟数据
  const generateMockData = () => {
    const mockCostData = [
      { month: '2024-01', cost: 45000, orders: 23, avgCost: 1956.52 },
      { month: '2024-02', cost: 52000, orders: 27, avgCost: 1925.93 },
      { month: '2024-03', cost: 48000, orders: 25, avgCost: 1920.00 },
      { month: '2024-04', cost: 55000, orders: 29, avgCost: 1896.55 },
      { month: '2024-05', cost: 58000, orders: 31, avgCost: 1870.97 },
      { month: '2024-06', cost: 62000, orders: 33, avgCost: 1878.79 }
    ];

    const mockSupplierData = [
      { 
        id: 1, 
        name: '鲜果供应链', 
        totalAmount: 180000, 
        orderCount: 45, 
        avgDeliveryTime: 2.3, 
        qualityScore: 95, 
        priceIndex: 102,
        onTimeRate: 98,
        defectRate: 1.2
      },
      { 
        id: 2, 
        name: '优质农产品', 
        totalAmount: 145000, 
        orderCount: 38, 
        avgDeliveryTime: 1.8, 
        qualityScore: 88, 
        priceIndex: 96,
        onTimeRate: 95,
        defectRate: 2.1
      },
      { 
        id: 3, 
        name: '绿色食品批发', 
        totalAmount: 120000, 
        orderCount: 32, 
        avgDeliveryTime: 3.1, 
        qualityScore: 92, 
        priceIndex: 105,
        onTimeRate: 92,
        defectRate: 1.8
      }
    ];

    const mockTrendData = [
      { period: '第1周', purchaseAmount: 12000, orderCount: 8, avgUnitPrice: 15.5 },
      { period: '第2周', purchaseAmount: 14500, orderCount: 9, avgUnitPrice: 16.1 },
      { period: '第3周', purchaseAmount: 13200, orderCount: 7, avgUnitPrice: 18.9 },
      { period: '第4周', purchaseAmount: 15800, orderCount: 10, avgUnitPrice: 15.8 }
    ];

    setCostData(mockCostData);
    setSupplierData(mockSupplierData);
    setTrendData(mockTrendData);
  };

  // 刷新数据
  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchPurchaseAnalysisData();
    setRefreshing(false);
    message.success('数据已刷新');
  };

  // 采购成本分析表格列
  const costColumns = [
    {
      title: '月份',
      dataIndex: 'month',
      key: 'month',
      render: (month) => month.replace('-', '年') + '月'
    },
    {
      title: '采购总额',
      dataIndex: 'cost',
      key: 'cost',
      render: (cost) => (
        <Statistic 
          value={cost} 
          prefix="¥" 
          precision={0}
          valueStyle={{ fontSize: 14 }}
        />
      )
    },
    {
      title: '订单数量',
      dataIndex: 'orders',
      key: 'orders',
      render: (orders) => (
        <Badge count={orders} showZero color="#52c41a" />
      )
    },
    {
      title: '平均单价',
      dataIndex: 'avgCost',
      key: 'avgCost',
      render: (avgCost) => `¥${(avgCost || 0).toFixed(2)}`
    },
    {
      title: '成本趋势',
      key: 'trend',
      render: (_, record, index, data) => {
        if (!data || !Array.isArray(data) || index === 0 || !data[index - 1]) return '-';
        const prevCost = data[index - 1].cost;
        if (!prevCost || !record.cost) return '-';
        const trend = ((record.cost - prevCost) / prevCost * 100);
        return (
          <Tag color={trend > 0 ? 'red' : trend < 0 ? 'green' : 'default'}>
            {trend > 0 ? '↗' : trend < 0 ? '↘' : '→'} {Math.abs(trend).toFixed(1)}%
          </Tag>
        );
      }
    }
  ];

  // 供应商表现表格列
  const supplierColumns = [
    {
      title: '供应商',
      dataIndex: 'name',
      key: 'name',
      fixed: 'left',
      width: 150,
      render: (name) => (
        <div style={{ fontWeight: 500 }}>{name}</div>
      )
    },
    {
      title: '采购总额',
      dataIndex: 'totalAmount',
      key: 'totalAmount',
      render: (amount) => `¥${amount.toLocaleString()}`
    },
    {
      title: '订单数量',
      dataIndex: 'orderCount',
      key: 'orderCount'
    },
    {
      title: '平均交货时间',
      dataIndex: 'avgDeliveryTime',
      key: 'avgDeliveryTime',
      render: (time) => `${time}天`
    },
    {
      title: '质量评分',
      dataIndex: 'qualityScore',
      key: 'qualityScore',
      render: (score) => (
        <div>
          <Progress 
            percent={score} 
            size="small" 
            strokeColor={score >= 90 ? '#52c41a' : score >= 80 ? '#faad14' : '#ff4d4f'}
          />
          <div style={{ fontSize: 12, marginTop: 2 }}>{score}分</div>
        </div>
      )
    },
    {
      title: '价格指数',
      dataIndex: 'priceIndex',
      key: 'priceIndex',
      render: (index) => (
        <Tag color={index < 100 ? 'green' : index > 110 ? 'red' : 'orange'}>
          {index}
        </Tag>
      )
    },
    {
      title: '准时率',
      dataIndex: 'onTimeRate',
      key: 'onTimeRate',
      render: (rate) => `${rate}%`
    },
    {
      title: '次品率',
      dataIndex: 'defectRate',
      key: 'defectRate',
      render: (rate) => (
        <span style={{ color: rate > 2 ? '#ff4d4f' : '#52c41a' }}>
          {rate}%
        </span>
      )
    }
  ];

  // 渲染成本分析
  const renderCostAnalysis = () => {
    const costLineConfig = {
      data: costData || [],
      xField: 'month',
      yField: 'cost',
      point: {
        size: 5,
        shape: 'diamond',
      },
      label: {
        style: {
          fill: '#aaa',
        },
      },
    };

    return (
      <div>
        <Row gutter={[16, 16]} style={{ marginBottom: 16 }}>
          <Col xs={24} sm={6}>
            <Card>
              <Statistic
                title="本月采购总额"
                value={(costData && costData.length > 0) ? (costData[costData.length - 1]?.cost || 0) : 0}
                prefix="¥"
                valueStyle={{ color: '#3f8600' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={6}>
            <Card>
              <Statistic
                title="本月订单数"
                value={(costData && costData.length > 0) ? (costData[costData.length - 1]?.orders || 0) : 0}
                suffix="单"
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={6}>
            <Card>
              <Statistic
                title="平均单价"
                value={(costData && costData.length > 0) ? (costData[costData.length - 1]?.avgCost || 0) : 0}
                prefix="¥"
                precision={2}
                valueStyle={{ color: '#722ed1' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={6}>
            <Card>
              <Statistic
                title="成本环比"
                value={
                  (costData && costData.length >= 2) 
                    ? ((costData[costData.length - 1]?.cost || 0) - (costData[costData.length - 2]?.cost || 0)) / (costData[costData.length - 2]?.cost || 1) * 100
                    : 0
                }
                precision={1}
                suffix="%"
                valueStyle={{ 
                  color: (costData && costData.length >= 2 && (costData[costData.length - 1]?.cost || 0) > (costData[costData.length - 2]?.cost || 0)) ? '#cf1322' : '#3f8600' 
                }}
              />
            </Card>
          </Col>
        </Row>

        <Row gutter={[16, 16]}>
          <Col xs={24} lg={14}>
            <Card title="采购成本趋势">
              <Line {...costLineConfig} height={300} />
            </Card>
          </Col>
          <Col xs={24} lg={10}>
            <Card title="成本明细" bodyStyle={{ padding: 0 }}>
              <Table
                columns={costColumns}
                dataSource={costData || []}
                rowKey="month"
                pagination={false}
                size="small"
              />
            </Card>
          </Col>
        </Row>
      </div>
    );
  };

  // 渲染供应商分析
  const renderSupplierAnalysis = () => (
    <div>
      <Alert
        message="供应商表现评估"
        description="基于采购金额、交货时间、质量评分、价格竞争力等维度综合评估供应商表现。"
        type="info"
        showIcon
        style={{ marginBottom: 16 }}
      />

      <Table
        columns={supplierColumns}
        dataSource={supplierData || []}
        rowKey="id"
        scroll={{ x: 800 }}
        pagination={false}
      />

      <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
        <Col xs={24} lg={12}>
          <Card title="供应商采购占比">
            <Pie
              data={(supplierData || []).map(supplier => ({
                type: supplier.name,
                value: supplier.totalAmount
              }))}
              angleField="value"
              colorField="type"
              radius={0.8}
              label={{
                type: 'outer',
                content: '{name} {percentage}',
              }}
              height={300}
            />
          </Card>
        </Col>
        <Col xs={24} lg={12}>
          <Card title="供应商质量对比">
            <Column
              data={supplierData || []}
              xField="name"
              yField="qualityScore"
              height={300}
              columnStyle={{
                radius: [4, 4, 0, 0],
              }}
            />
          </Card>
        </Col>
      </Row>
    </div>
  );

  // 渲染采购趋势
  const renderTrendAnalysis = () => {
    const trendConfig = {
      data: trendData || [],
      xField: 'period',
      yField: 'purchaseAmount',
      height: 300,
      point: {
        size: 5,
        shape: 'diamond',
      },
    };

    return (
      <div>
        <Row gutter={[16, 16]}>
          <Col xs={24} lg={16}>
            <Card title="采购金额趋势">
              <Line {...trendConfig} />
            </Card>
          </Col>
          <Col xs={24} lg={8}>
            <Card title="趋势分析">
              <div className="trend-insights">
                <div style={{ marginBottom: 16 }}>
                  <h4><RobotOutlined /> AI分析洞察</h4>
                  <ul style={{ margin: 0, paddingLeft: 20 }}>
                    <li>采购成本呈上升趋势，建议关注价格波动</li>
                    <li>供应商1表现优异，可增加采购比重</li>
                    <li>建议优化采购频次，降低库存压力</li>
                    <li>部分商品价格偏高，建议寻找替代供应商</li>
                  </ul>
                </div>
                <Alert
                  message="建议"
                  description="基于历史数据分析，建议制定采购计划时重点关注成本控制和供应商管理。"
                  type="warning"
                  showIcon
                  size="small"
                />
              </div>
            </Card>
          </Col>
        </Row>

        <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
          <Col xs={24}>
            <Card title="采购频次分析">
              <Table
                columns={[
                  { title: '时期', dataIndex: 'period', key: 'period' },
                  { title: '采购金额', dataIndex: 'purchaseAmount', key: 'purchaseAmount', render: (amount) => `¥${amount.toLocaleString()}` },
                  { title: '订单数', dataIndex: 'orderCount', key: 'orderCount' },
                  { title: '平均单价', dataIndex: 'avgUnitPrice', key: 'avgUnitPrice', render: (price) => `¥${price}` },
                  { 
                    title: '采购效率', 
                    key: 'efficiency',
                    render: (_, record) => {
                      const efficiency = (record.purchaseAmount || 0) / (record.orderCount || 1);
                      return (
                        <Progress 
                          percent={Math.min(efficiency / 2000 * 100, 100)} 
                          size="small"
                          format={() => `¥${efficiency.toFixed(0)}/单`}
                        />
                      );
                    }
                  }
                ]}
                dataSource={trendData || []}
                rowKey="period"
                pagination={false}
                size="small"
              />
            </Card>
          </Col>
        </Row>
      </div>
    );
  };

  if (loading && !refreshing) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>加载采购分析数据...</div>
      </div>
    );
  }

  return (
    <div className="purchase-analysis">
      {/* 筛选工具栏 */}
      <Card style={{ marginBottom: 16 }}>
        <Row gutter={[16, 16]} align="middle">
          <Col xs={24} sm={6}>
            <Select
              placeholder="选择供应商"
              style={{ width: '100%' }}
              value={filters.supplier}
              onChange={(value) => setFilters({ ...filters, supplier: value })}
            >
              <Option value="all">全部供应商</Option>
              <Option value="supplier1">鲜果供应链</Option>
              <Option value="supplier2">优质农产品</Option>
              <Option value="supplier3">绿色食品批发</Option>
            </Select>
          </Col>
          <Col xs={24} sm={6}>
            <Select
              placeholder="选择商品类别"
              style={{ width: '100%' }}
              value={filters.category}
              onChange={(value) => setFilters({ ...filters, category: value })}
            >
              <Option value="all">全部类别</Option>
              <Option value="fruits">水果</Option>
              <Option value="vegetables">蔬菜</Option>
              <Option value="meat">肉类</Option>
            </Select>
          </Col>
          <Col xs={24} sm={6}>
            <RangePicker
              style={{ width: '100%' }}
              onChange={(dates) => setFilters({ ...filters, dateRange: dates })}
            />
          </Col>
          <Col xs={24} sm={6}>
            <Space>
              <Button 
                icon={<SyncOutlined spin={refreshing} />} 
                onClick={handleRefresh}
                loading={refreshing}
              >
                刷新
              </Button>
              <Button icon={<ExportOutlined />}>
                导出
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 分析内容Tab */}
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane 
          tab={
            <Space>
              <DollarOutlined />
              成本分析
            </Space>
          } 
          key="cost"
        >
          {renderCostAnalysis()}
        </TabPane>

        <TabPane 
          tab={
            <Space>
              <UserOutlined />
              供应商分析
            </Space>
          } 
          key="supplier"
        >
          {renderSupplierAnalysis()}
        </TabPane>

        <TabPane 
          tab={
            <Space>
              <LineChartOutlined />
              采购趋势
            </Space>
          } 
          key="trend"
        >
          {renderTrendAnalysis()}
        </TabPane>
      </Tabs>
    </div>
  );
};

export default PurchaseAnalysis; 