#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
API到MCP工具映射路由

提供API到MCP工具映射的管理功能，包括创建、更新和删除映射
"""

import logging
import uuid
from typing import Dict, Any, List, Optional
import copy

from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy import select, func
from sqlalchemy.ext.asyncio import AsyncSession

from db.database import get_db
from models.user import User
from core.auth import get_current_user

from .. import router
from ..models import APIMCPToolMapping, SAPIEndpoint, MCPServer
from ..mcp_server_adapter import MCPServerAdapter

logger = logging.getLogger(__name__)

# 创建MCP服务器适配器
server_adapter = MCPServerAdapter()

def _build_mcp_input_schema(sapi_endpoint: SAPIEndpoint, param_name_mapping: Dict[str, str] = None) -> Dict[str, Any]:
    """
    根据 SAPIEndpoint 的参数定义和可选的名称映射，构建MCP工具的 inputSchema。
    param_name_mapping: key是SAPI参数名, value是MCP工具参数名。
    """
    if param_name_mapping is None:
        param_name_mapping = {}

    final_schema_properties = {}
    final_schema_required = []

    sapi_params_data = sapi_endpoint.parameters if isinstance(sapi_endpoint.parameters, dict) else {}

    # 1. 处理 openapi_parameters (path, query, header)
    # sync_endpoints 存储 openapi_parameters 是一个字典: {"param_name": param_info_dict, ...}
    openapi_parameters_dict = sapi_params_data.get("openapi_parameters", {})
    if isinstance(openapi_parameters_dict, dict):
        for sapi_param_name, param_info in openapi_parameters_dict.items(): # Iterate over dict items
            if not isinstance(param_info, dict):
                logger.warning(f"Skipping invalid param_info value for '{sapi_param_name}' in openapi_parameters for endpoint '{sapi_endpoint.name}': {param_info}")
                continue

            # sapi_param_name 是从字典的键获取的
            if param_info.get("name") != sapi_param_name:
                param_content_name = param_info.get("name", "UNKNOWN_PARAM_NAME")
                logger.warning(f"Mismatch between openapi_parameters key '{sapi_param_name}' and its content name '{param_content_name}' for endpoint '{sapi_endpoint.name}'. Using key.")

            mcp_tool_param_name = param_name_mapping.get(sapi_param_name, sapi_param_name)
            if not isinstance(mcp_tool_param_name, str):
                logger.warning(
                    f"Parameter mapping for SAPI param '{sapi_param_name}' in endpoint '{sapi_endpoint.name}' "
                    f"resulted in a non-string MCP tool name: {mcp_tool_param_name}. "
                    f"Using SAPI name '{sapi_param_name}' instead."
                )
                mcp_tool_param_name = sapi_param_name

            # param_info 是完整的 OpenAPI 参数对象, 它本身包含 type, format, description, 和一个 schema 键 (通常更详细)
            param_schema_from_info_key = param_info.get("schema", {})

            prop_schema_for_mcp = {}
            if isinstance(param_schema_from_info_key, dict) and param_schema_from_info_key: #优先使用 param_info.schema
                prop_schema_for_mcp = copy.deepcopy(param_schema_from_info_key)
            else: # 否则，从 param_info 的顶层键构造 schema (兼容旧格式或简单参数)
                prop_schema_for_mcp["type"] = param_info.get("type", "string") # 默认类型为 string
                if "format" in param_info: prop_schema_for_mcp["format"] = param_info.get("format")
                # description 在后面统一处理
                for key_to_copy in ["items", "enum", "default", "pattern", "minLength", "maxLength", "minimum", "maximum"]:
                    if key_to_copy in param_info:
                        prop_schema_for_mcp[key_to_copy] = param_info[key_to_copy]

            # 确保 description 存在
            # 优先使用 param_info.description (OpenAPI参数对象的描述), 然后才是 schema 内部的描述 (如果有)
            if param_info.get("description"):
                prop_schema_for_mcp["description"] = param_info.get("description")
            elif "description" not in prop_schema_for_mcp or not prop_schema_for_mcp["description"]:
                prop_schema_for_mcp["description"] = f"Parameter {mcp_tool_param_name}" # 最后回退

            # 确保 type 存在
            if "type" not in prop_schema_for_mcp:
                prop_schema_for_mcp["type"] = "string"
                logger.warning(f"Param '{sapi_param_name}' for endpoint '{sapi_endpoint.name}' resolved schema missing type, defaulting to string for MCP: {prop_schema_for_mcp}")

            final_schema_properties[mcp_tool_param_name] = prop_schema_for_mcp

            if param_info.get("required", False) is True:
                if mcp_tool_param_name not in final_schema_required:
                    final_schema_required.append(mcp_tool_param_name)
    else:
        logger.warning(f"Endpoint '{sapi_endpoint.name}' parameters.openapi_parameters is not a dict or missing. Found: {type(openapi_parameters_dict)}")

    # 2. 处理 requestBody (这部分逻辑保持不变，因为它已经期望 sapi_params_data.requestBody)
    request_body_sapi = sapi_params_data.get("requestBody", {})
    if isinstance(request_body_sapi, dict) and "content" in request_body_sapi:
        content_types = request_body_sapi.get("content", {})

        selected_media_type = None
        if "application/json" in content_types and isinstance(content_types["application/json"], dict) and isinstance(content_types["application/json"].get("schema"), dict):
            selected_media_type = "application/json"
        elif "multipart/form-data" in content_types and isinstance(content_types["multipart/form-data"], dict) and isinstance(content_types["multipart/form-data"].get("schema"), dict):
            selected_media_type = "multipart/form-data"
        elif content_types: # Fallback to the first available media type with a valid schema
            for media_type, content_obj in content_types.items():
                if isinstance(content_obj, dict) and isinstance(content_obj.get("schema"), dict):
                    selected_media_type = media_type
                    logger.info(f"Endpoint '{sapi_endpoint.name}' using first available media type '{selected_media_type}' for requestBody schema.")
                    break

        if selected_media_type:
            body_schema_from_openapi = content_types[selected_media_type]["schema"]
            logger.info(f"Endpoint '{sapi_endpoint.name}': Processing requestBody schema from media type '{selected_media_type}'.")

            body_properties = body_schema_from_openapi.get("properties", {})
            if isinstance(body_properties, dict):
                for sapi_prop_name, prop_schema_sapi in body_properties.items():
                    if not isinstance(prop_schema_sapi, dict) or not isinstance(sapi_prop_name, str):
                        logger.warning(f"Skipping invalid requestBody property or name for '{sapi_prop_name}' in endpoint '{sapi_endpoint.name}': {prop_schema_sapi}")
                        continue

                    mcp_tool_prop_name = param_name_mapping.get(sapi_prop_name, sapi_prop_name)
                    if not isinstance(mcp_tool_prop_name, str):
                        logger.warning(
                            f"Parameter mapping for SAPI request body property '{sapi_prop_name}' in endpoint '{sapi_endpoint.name}' "
                            f"resulted in a non-string MCP tool name: {mcp_tool_prop_name}. "
                            f"Using SAPI name '{sapi_prop_name}' instead."
                        )
                        mcp_tool_prop_name = sapi_prop_name

                    prop_schema_for_mcp = copy.deepcopy(prop_schema_sapi)
                    if "description" not in prop_schema_for_mcp or not prop_schema_for_mcp["description"]:
                        prop_schema_for_mcp["description"] = f"Body parameter {mcp_tool_prop_name}"
                    if "type" not in prop_schema_for_mcp:
                         prop_schema_for_mcp["type"] = "string"
                         logger.warning(f"RequestBody property '{sapi_prop_name}' for endpoint '{sapi_endpoint.name}' missing type, defaulting to string.")

                    final_schema_properties[mcp_tool_prop_name] = prop_schema_for_mcp
            else:
                logger.warning(f"Endpoint '{sapi_endpoint.name}' requestBody for media type '{selected_media_type}' does not contain a valid 'properties' dictionary in its schema.")

            body_required_list = body_schema_from_openapi.get("required", [])
            if isinstance(body_required_list, list):
                for req_param_sapi_name in body_required_list:
                    if not isinstance(req_param_sapi_name, str):
                        logger.error(f"CRITICAL: Non-string '{req_param_sapi_name}' found in 'required' list from SAPI endpoint parameters for '{sapi_endpoint.name}'. Data might be corrupted or sync_endpoints failed.")
                        continue

                    mcp_tool_req_param_name = param_name_mapping.get(req_param_sapi_name, req_param_sapi_name)
                    if not isinstance(mcp_tool_req_param_name, str):
                        logger.warning(
                            f"Parameter mapping for SAPI required request body param '{req_param_sapi_name}' in endpoint '{sapi_endpoint.name}' "
                            f"resulted in a non-string MCP tool name: {mcp_tool_req_param_name}. "
                            f"Using SAPI name '{req_param_sapi_name}' instead for required list."
                        )
                        mcp_tool_req_param_name = req_param_sapi_name

                    if mcp_tool_req_param_name not in final_schema_required:
                        final_schema_required.append(mcp_tool_req_param_name)
            else:
                 logger.warning(f"Endpoint '{sapi_endpoint.name}' requestBody 'required' field is not a list (found type {type(body_required_list)}). This might be an issue with sync_endpoints or data corruption.")
        else:
            logger.debug(f"Endpoint '{sapi_endpoint.name}' has requestBody definition but no usable schema found.")
    else:
        logger.debug(f"Endpoint '{sapi_endpoint.name}' does not have a requestBody definition or it's not a dict.")

    # 特殊处理 project_id，如果它是路径参数，则在MCP schema中设为可选
    sapi_project_id_name = "project_id"
    mcp_project_id_name_candidate = param_name_mapping.get(sapi_project_id_name, sapi_project_id_name)
    if not isinstance(mcp_project_id_name_candidate, str):
        mcp_project_id_name_candidate = sapi_project_id_name # 回退

    is_project_id_path_param = False
    # 检查 openapi_parameters_dict (它现在是字典形式)
    if isinstance(openapi_parameters_dict, dict):
        project_id_param_info = openapi_parameters_dict.get(sapi_project_id_name)
        if isinstance(project_id_param_info, dict) and project_id_param_info.get("in") == "path":
            is_project_id_path_param = True

    if is_project_id_path_param and mcp_project_id_name_candidate in final_schema_required:
        logger.info(f"Path parameter '{mcp_project_id_name_candidate}' (SAPI: '{sapi_project_id_name}') for endpoint '{sapi_endpoint.name}' will be optional in MCP tool schema. Removing from required list.")
        final_schema_required.remove(mcp_project_id_name_candidate)
    elif is_project_id_path_param and mcp_project_id_name_candidate not in final_schema_properties:
        logger.info(f"Path parameter '{mcp_project_id_name_candidate}' (SAPI: '{sapi_project_id_name}') for endpoint '{sapi_endpoint.name}' was not in schema properties. Adding as optional string.")
        final_schema_properties[mcp_project_id_name_candidate] = {"type": "string", "format": "uuid", "description": "Project ID (provided by session)"}

    return {
        "type": "object",
        "properties": final_schema_properties,
        "required": final_schema_required
    }

# 获取工具映射列表
@router.get("/mappings", response_model=Dict[str, Any])
async def get_mapping_list(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
    page: int = Query(1, ge=1),
    page_size: int = Query(10, ge=1, le=100),
    search: Optional[str] = Query(None, description="搜索关键词，支持工具名称、端点名称和描述")
):
    """获取工具映射列表"""
    try:
        # 构建查询
        from sqlalchemy.orm import joinedload
        from sqlalchemy import or_

        query = select(APIMCPToolMapping).options(
            joinedload(APIMCPToolMapping.endpoint),
            joinedload(APIMCPToolMapping.server)
        )

        # 添加搜索条件
        if search:
            search_term = f"%{search}%"
            query = query.join(APIMCPToolMapping.endpoint).join(APIMCPToolMapping.server).filter(
                or_(
                    APIMCPToolMapping.tool_name.ilike(search_term),
                    APIMCPToolMapping.tool_description.ilike(search_term),
                    SAPIEndpoint.name.ilike(search_term),
                    SAPIEndpoint.description.ilike(search_term),
                    MCPServer.name.ilike(search_term)
                )
            )

        # 查询映射总数
        count_query = select(func.count()).select_from(query.subquery())
        result = await db.execute(count_query)
        total = result.scalar()

        # 添加分页
        query = query.offset((page - 1) * page_size).limit(page_size)

        # 执行查询
        result = await db.execute(query)
        mappings = result.scalars().all()

        # 转换为字典列表
        mapping_list = []
        for mapping in mappings:
            # 获取端点和服务器信息
            endpoint = mapping.endpoint
            server = mapping.server

            mapping_dict = {
                "id": str(mapping.id),
                "endpoint_id": str(mapping.endpoint_id),
                "endpoint_name": endpoint.name if endpoint else None,
                "endpoint_path": endpoint.endpoint_path if endpoint else None,
                "endpoint_method": endpoint.method if endpoint else None,
                "endpoint_description": endpoint.description if endpoint else None,
                "server_id": str(mapping.server_id),
                "server_name": server.name if server else None,
                "tool_name": mapping.tool_name,
                "tool_description": mapping.tool_description,
                "parameter_mapping": mapping.parameter_mapping,
                "response_mapping": mapping.response_mapping,
                "input_schema": mapping.input_schema,
                "supports_streaming": mapping.supports_streaming,
                "supports_media": mapping.supports_media,
                "enabled": mapping.enabled,
                "created_at": mapping.created_at.isoformat(),
                "updated_at": mapping.updated_at.isoformat(),
                "created_by_id": str(mapping.created_by_id) if mapping.created_by_id else None
            }

            mapping_list.append(mapping_dict)

        return {
            "success": True,
            "message": "获取工具映射列表成功",
            "data": mapping_list,
            "total": total,
            "page": page,
            "page_size": page_size
        }
    except Exception as e:
        logger.error(f"获取工具映射列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取工具映射列表失败: {str(e)}")

# 创建工具映射
@router.post("/mappings", response_model=Dict[str, Any])
async def create_mapping(
    mapping_data: Dict[str, Any],
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """创建工具映射"""
    try:
        # 检查端点是否存在
        endpoint_id = mapping_data.get("endpoint_id")
        result = await db.execute(
            select(SAPIEndpoint).where(SAPIEndpoint.id == uuid.UUID(endpoint_id))
        )
        endpoint = result.scalar_one_or_none()
        if not endpoint:
            raise HTTPException(status_code=404, detail="API端点不存在")

        # 检查服务器是否存在
        server_id = mapping_data.get("server_id")
        result = await db.execute(
            select(MCPServer).where(MCPServer.id == uuid.UUID(server_id))
        )
        server = result.scalar_one_or_none()
        if not server:
            raise HTTPException(status_code=404, detail="MCP服务器不存在")

        # 检查工具名称是否已存在
        result = await db.execute(
            select(APIMCPToolMapping).where(
                APIMCPToolMapping.server_id == uuid.UUID(server_id),
                APIMCPToolMapping.tool_name == mapping_data.get("tool_name")
            )
        )
        existing_mapping = result.scalar_one_or_none()

        if existing_mapping:
            raise HTTPException(status_code=400, detail="相同服务器上已存在同名工具")

        # 构建 input_schema
        parameter_map_for_schema = mapping_data.get("parameter_mapping", {})
        generated_input_schema = _build_mcp_input_schema(endpoint, parameter_map_for_schema)

        # 创建映射
        mapping = APIMCPToolMapping(
            id=uuid.uuid4(),
            endpoint_id=uuid.UUID(endpoint_id),
            server_id=uuid.UUID(server_id),
            tool_name=mapping_data.get("tool_name"),
            tool_description=mapping_data.get("tool_description"),
            parameter_mapping=parameter_map_for_schema,
            response_mapping=mapping_data.get("response_mapping", {}),
            input_schema=generated_input_schema,
            supports_streaming=mapping_data.get("supports_streaming", True),
            supports_media=mapping_data.get("supports_media", False),
            enabled=mapping_data.get("enabled", True),
            created_by_id=current_user.id
        )

        db.add(mapping)
        await db.commit()
        await db.refresh(mapping)

        return {
            "success": True,
            "message": "创建工具映射成功",
            "data": {
                "id": str(mapping.id),
                "endpoint_id": str(mapping.endpoint_id),
                "server_id": str(mapping.server_id),
                "tool_name": mapping.tool_name,
                "tool_description": mapping.tool_description,
                "parameter_mapping": mapping.parameter_mapping,
                "response_mapping": mapping.response_mapping,
                "input_schema": mapping.input_schema,
                "supports_streaming": mapping.supports_streaming,
                "supports_media": mapping.supports_media,
                "enabled": mapping.enabled,
                "created_at": mapping.created_at.isoformat(),
                "updated_at": mapping.updated_at.isoformat(),
                "created_by_id": str(mapping.created_by_id) if mapping.created_by_id else None
            }
        }
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"创建工具映射失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"创建工具映射失败: {str(e)}")

# 更新工具映射
@router.put("/mappings/{mapping_id}", response_model=Dict[str, Any])
async def update_mapping(
    mapping_id: str,
    mapping_data: Dict[str, Any],
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """更新工具映射"""
    try:
        # 查询映射
        result = await db.execute(
            select(APIMCPToolMapping).where(APIMCPToolMapping.id == uuid.UUID(mapping_id))
        )
        mapping = result.scalar_one_or_none()
        if not mapping:
            raise HTTPException(status_code=404, detail="工具映射不存在")

        # 检查端点是否存在 (此部分用于验证 mapping_data 中的 endpoint_id，如果提供了)
        if "endpoint_id" in mapping_data:
            result = await db.execute(
                select(SAPIEndpoint).where(SAPIEndpoint.id == uuid.UUID(mapping_data["endpoint_id"]))
            )
            endpoint_check = result.scalar_one_or_none()
            if not endpoint_check:
                raise HTTPException(status_code=404, detail="提供的API端点不存在")

        # 检查服务器是否存在 (此部分用于验证 mapping_data 中的 server_id，如果提供了)
        if "server_id" in mapping_data:
            result = await db.execute(
                select(MCPServer).where(MCPServer.id == uuid.UUID(mapping_data["server_id"]))
            )
            server_check = result.scalar_one_or_none()
            if not server_check:
                raise HTTPException(status_code=404, detail="提供的MCP服务器不存在")

        # 检查工具名称是否已存在 (如果 server_id 或 tool_name 正在被修改)
        if "server_id" in mapping_data or "tool_name" in mapping_data:
            check_server_id = uuid.UUID(mapping_data["server_id"]) if "server_id" in mapping_data else mapping.server_id
            check_tool_name = mapping_data.get("tool_name", mapping.tool_name)

            result = await db.execute(
                select(APIMCPToolMapping).where(
                    APIMCPToolMapping.server_id == check_server_id,
                    APIMCPToolMapping.tool_name == check_tool_name,
                    APIMCPToolMapping.id != uuid.UUID(mapping_id)
                )
            )
            existing_mapping = result.scalar_one_or_none()
            if existing_mapping:
                raise HTTPException(status_code=400, detail="相同服务器上已存在同名工具")

        # 确定用于Schema生成的SAPIEndpoint对象。
        # 这取决于本次更新中 'endpoint_id' 是否发生改变。
        current_endpoint_id_on_mapping_obj = mapping.endpoint_id
        endpoint_id_str_from_data = mapping_data.get("endpoint_id")

        if endpoint_id_str_from_data and endpoint_id_str_from_data != str(current_endpoint_id_on_mapping_obj):
            endpoint_id_for_schema_load = uuid.UUID(endpoint_id_str_from_data)
        else:
            endpoint_id_for_schema_load = current_endpoint_id_on_mapping_obj

        # 从数据库加载用于生成Schema的SAPIEndpoint实例
        result = await db.execute(
            select(SAPIEndpoint).where(SAPIEndpoint.id == endpoint_id_for_schema_load)
        )
        endpoint_for_schema_generation = result.scalar_one_or_none()
        if not endpoint_for_schema_generation:
            # 这个检查很重要，因为 endpoint_id_for_schema_load 可能来自用户输入
            raise HTTPException(status_code=404, detail=f"API端点 {endpoint_id_for_schema_load} 不存在 (用于Schema生成)")

        # 更新 mapping 对象上的字段
        # 首先处理影响Schema的字段 (endpoint_id, parameter_mapping)

        if endpoint_id_str_from_data: # 允许显式设置为相同值
            new_endpoint_id = uuid.UUID(endpoint_id_str_from_data)
            if mapping.endpoint_id != new_endpoint_id:
                mapping.endpoint_id = new_endpoint_id
                logger.info(f"Mapping {mapping_id}: endpoint_id 将更新为 {new_endpoint_id}.")

        # 确定用于Schema生成并且保存到mapping对象的 parameter_mapping
        # 如果 mapping_data 中提供了 "parameter_mapping"，则使用它；否则使用当前mapping上的值。
        parameter_mapping_for_update = mapping_data.get("parameter_mapping", mapping.parameter_mapping)
        if mapping.parameter_mapping != parameter_mapping_for_update: # 只有在实际更改时才记录日志
            mapping.parameter_mapping = parameter_mapping_for_update
            logger.info(f"Mapping {mapping_id}: parameter_mapping 将更新.")
        else:
             # 即使值相同，如果它来自 mapping_data，也应赋值以确保使用的是请求中的版本
            mapping.parameter_mapping = parameter_mapping_for_update


        # 总是使用确定的SAPI端点和参数映射（parameter_mapping）重新生成input_schema
        logger.info(f"Mapping {mapping_id}: 正在使用端点 '{endpoint_for_schema_generation.id}' 和参数映射 '{mapping.parameter_mapping}' 重新生成 input_schema.")
        newly_generated_input_schema = _build_mcp_input_schema(endpoint_for_schema_generation, mapping.parameter_mapping)

        if mapping.input_schema != newly_generated_input_schema:
            mapping.input_schema = newly_generated_input_schema
            logger.info(f"Mapping {mapping_id}: input_schema 已更新，因为内容与新生成的不同.")
        else:
            # 即使内容相同，也重新赋值，以反映它是基于最新数据生成的
            # 这也有助于处理 mapping.input_schema 为 None 或其他无效初始状态的情况
            mapping.input_schema = newly_generated_input_schema
            logger.info(f"Mapping {mapping_id}: input_schema 已刷新 (内容与新生成的一致或已是最新).")

        # 更新其他非Schema关键字段
        if "server_id" in mapping_data:
            new_server_id = uuid.UUID(mapping_data["server_id"])
            if mapping.server_id != new_server_id:
                # 此处应有 server 存在性检查，但前面已对 mapping_data["server_id"] 做了检查
                mapping.server_id = new_server_id
        if "tool_name" in mapping_data:
            mapping.tool_name = mapping_data["tool_name"]
        if "tool_description" in mapping_data:
            mapping.tool_description = mapping_data["tool_description"]
        if "response_mapping" in mapping_data:
            mapping.response_mapping = mapping_data["response_mapping"]
        if "supports_streaming" in mapping_data:
            mapping.supports_streaming = mapping_data["supports_streaming"]
        if "supports_media" in mapping_data:
            mapping.supports_media = mapping_data["supports_media"]
        if "enabled" in mapping_data:
            mapping.enabled = mapping_data["enabled"]

        await db.commit()
        await db.refresh(mapping)

        return {
            "success": True,
            "message": "更新工具映射成功",
            "data": {
                "id": str(mapping.id),
                "endpoint_id": str(mapping.endpoint_id),
                "server_id": str(mapping.server_id),
                "tool_name": mapping.tool_name,
                "tool_description": mapping.tool_description,
                "parameter_mapping": mapping.parameter_mapping,
                "response_mapping": mapping.response_mapping,
                "input_schema": mapping.input_schema,
                "supports_streaming": mapping.supports_streaming,
                "supports_media": mapping.supports_media,
                "enabled": mapping.enabled,
                "created_at": mapping.created_at.isoformat(),
                "updated_at": mapping.updated_at.isoformat(),
                "created_by_id": str(mapping.created_by_id) if mapping.created_by_id else None
            }
        }
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"更新工具映射失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"更新工具映射失败: {str(e)}")

# 删除工具映射
@router.delete("/mappings/{mapping_id}", response_model=Dict[str, Any])
async def delete_mapping(
    mapping_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """删除工具映射"""
    try:
        # 查询映射
        result = await db.execute(
            select(APIMCPToolMapping).where(APIMCPToolMapping.id == uuid.UUID(mapping_id))
        )
        mapping = result.scalar_one_or_none()
        if not mapping:
            raise HTTPException(status_code=404, detail="工具映射不存在")

        # 删除映射
        await db.delete(mapping)
        await db.commit()

        return {
            "success": True,
            "message": "删除工具映射成功"
        }
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"删除工具映射失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除工具映射失败: {str(e)}")

# 获取工具映射详情
@router.get("/mappings/{mapping_id}", response_model=Dict[str, Any])
async def get_mapping_detail(
    mapping_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """获取工具映射详情"""
    try:
        # 使用 joinedload 预加载关联的端点和服务器
        from sqlalchemy.orm import joinedload

        # 查询映射，预加载关联对象
        result = await db.execute(
            select(APIMCPToolMapping)
            .options(
                joinedload(APIMCPToolMapping.endpoint),
                joinedload(APIMCPToolMapping.server)
            )
            .where(APIMCPToolMapping.id == uuid.UUID(mapping_id))
        )
        mapping = result.scalar_one_or_none()
        if not mapping:
            raise HTTPException(status_code=404, detail="工具映射不存在")

        # 获取端点和服务器信息
        endpoint = mapping.endpoint
        server = mapping.server

        # 转换为字典
        mapping_dict = {
            "id": str(mapping.id),
            "endpoint_id": str(mapping.endpoint_id),
            "endpoint_name": endpoint.name if endpoint else None,
            "endpoint_path": endpoint.endpoint_path if endpoint else None,
            "endpoint_method": endpoint.method if endpoint else None,
            "endpoint_description": endpoint.description if endpoint else None,
            "server_id": str(mapping.server_id),
            "server_name": server.name if server else None,
            "tool_name": mapping.tool_name,
            "tool_description": mapping.tool_description,
            "parameter_mapping": mapping.parameter_mapping,
            "response_mapping": mapping.response_mapping,
            "input_schema": mapping.input_schema,
            "supports_streaming": mapping.supports_streaming,
            "supports_media": mapping.supports_media,
            "enabled": mapping.enabled,
            "created_at": mapping.created_at.isoformat(),
            "updated_at": mapping.updated_at.isoformat(),
            "created_by_id": str(mapping.created_by_id) if mapping.created_by_id else None
        }

        return {
            "success": True,
            "message": "获取工具映射详情成功",
            "data": mapping_dict
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取工具映射详情失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取工具映射详情失败: {str(e)}")

# 测试工具映射
@router.post("/mappings/{mapping_id}/test", response_model=Dict[str, Any])
async def test_mapping(
    mapping_id: str,
    parameters: Dict[str, Any],
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """测试工具映射"""
    try:
        # 使用 joinedload 预加载关联的端点和服务器
        from sqlalchemy.orm import joinedload

        # 查询映射，预加载关联对象
        result = await db.execute(
            select(APIMCPToolMapping)
            .options(
                joinedload(APIMCPToolMapping.endpoint),
                joinedload(APIMCPToolMapping.server)
            )
            .where(APIMCPToolMapping.id == uuid.UUID(mapping_id))
        )
        mapping = result.scalar_one_or_none()
        if not mapping:
            raise HTTPException(status_code=404, detail="工具映射不存在")

        # 获取端点和服务器信息
        endpoint = mapping.endpoint
        server = mapping.server

        # 这里实现测试工具映射的逻辑
        # 例如，构建请求，调用API，返回结果

        # 模拟测试结果
        return {
            "success": True,
            "message": "测试工具映射成功",
            "data": {
                "mapping": {
                    "id": str(mapping.id),
                    "tool_name": mapping.tool_name,
                    "endpoint_path": endpoint.endpoint_path if endpoint else None,
                    "endpoint_method": endpoint.method if endpoint else None,
                    "server_name": server.name if server else None
                },
                "parameters": parameters,
                "result": {
                    "success": True,
                    "message": "工具调用成功",
                    "data": {
                        "example": "response"
                    }
                }
            }
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"测试工具映射失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"测试工具映射失败: {str(e)}")

# 批量创建工具映射
@router.post("/mappings/batch", response_model=Dict[str, Any])
async def batch_create_mappings(
    batch_data: Dict[str, Any],
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """批量创建工具映射"""
    try:
        # 获取批量创建数据
        server_id = batch_data.get("server_id")
        endpoint_ids = batch_data.get("endpoint_ids", [])

        # 检查服务器是否存在
        result = await db.execute(
            select(MCPServer).where(MCPServer.id == uuid.UUID(server_id))
        )
        server = result.scalar_one_or_none()
        if not server:
            raise HTTPException(status_code=404, detail="MCP服务器不存在")

        # 批量创建映射
        created_mappings = []
        skipped_mappings = []

        for endpoint_id in endpoint_ids:
            # 检查端点是否存在
            result = await db.execute(
                select(SAPIEndpoint).where(SAPIEndpoint.id == uuid.UUID(endpoint_id))
            )
            endpoint = result.scalar_one_or_none()
            if not endpoint:
                skipped_mappings.append({
                    "endpoint_id": endpoint_id,
                    "reason": "API端点不存在"
                })
                continue

            # 提取端点路径中的最后一部分作为工具名称
            path_parts = endpoint.endpoint_path.strip('/').split('/')
            # 如果路径以/api/开头，则去掉api部分
            if path_parts and path_parts[0] == 'api':
                path_parts = path_parts[1:]
            tool_name = path_parts[-1] if path_parts else endpoint.name.lower().replace(' ', '_')

            # 检查工具名称是否已存在
            result = await db.execute(
                select(APIMCPToolMapping).where(
                    APIMCPToolMapping.server_id == uuid.UUID(server_id),
                    APIMCPToolMapping.tool_name == tool_name
                )
            )
            existing_mapping = result.scalar_one_or_none()

            if existing_mapping:
                skipped_mappings.append({
                    "endpoint_id": endpoint_id,
                    "endpoint_name": endpoint.name,
                    "reason": "相同服务器上已存在同名工具"
                })
                continue

            # 使用辅助函数构建 input_schema
            # 批量创建时，parameter_mapping 默认为空
            generated_input_schema = _build_mcp_input_schema(endpoint, {})

            # 创建映射
            mapping = APIMCPToolMapping(
                id=uuid.uuid4(),
                endpoint_id=uuid.UUID(endpoint_id),
                server_id=uuid.UUID(server_id),
                tool_name=tool_name,
                tool_description=endpoint.description,
                parameter_mapping={},  # 参数映射保持为空，因为我们直接在工具中使用参数
                response_mapping={},
                input_schema=generated_input_schema,
                supports_streaming=True,
                supports_media=False,
                enabled=True,
                created_by_id=current_user.id
            )

            db.add(mapping)

            created_mappings.append({
                "id": str(mapping.id),
                "endpoint_id": str(mapping.endpoint_id),
                "endpoint_name": endpoint.name,
                "server_id": str(mapping.server_id),
                "tool_name": mapping.tool_name,
                "input_schema": mapping.input_schema
            })

        await db.commit()

        return {
            "success": True,
            "message": "批量创建工具映射成功",
            "data": {
                "created": created_mappings,
                "skipped": skipped_mappings,
                "total_created": len(created_mappings),
                "total_skipped": len(skipped_mappings)
            }
        }
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"批量创建工具映射失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"批量创建工具映射失败: {str(e)}")
