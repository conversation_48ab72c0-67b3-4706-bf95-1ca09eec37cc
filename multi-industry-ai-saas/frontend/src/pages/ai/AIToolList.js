import React, { useState, useEffect } from 'react';
import {
  Card, Table, Button, Space, Tag, Modal, message,
  Typography, Input, Select, Tooltip, Popconfirm, Badge,
  Tabs, Drawer, Form, Radio, Divider, Collapse
} from 'antd';
import {
  PlusOutlined, EditOutlined, DeleteOutlined,
  ExclamationCircleOutlined, SearchOutlined,
  SyncOutlined, SettingOutlined, CodeOutlined,
  FunctionOutlined, DatabaseOutlined, CalculatorOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { useProject } from '../../contexts/ProjectContext';
import apiService from '../../services/api';
import CodeEditor from '../../components/CodeEditor';

const { Title, Text } = Typography;
const { Option } = Select;
const { confirm } = Modal;
const { Panel } = Collapse;
const { TabPane } = Tabs;

const AIToolList = () => {
  const navigate = useNavigate();
  const { currentProject } = useProject();
  const [loading, setLoading] = useState(false);
  const [tools, setTools] = useState([]);
  const [searchText, setSearchText] = useState('');
  const [typeFilter, setTypeFilter] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [currentTool, setCurrentTool] = useState(null);
  const [form] = Form.useForm();
  const [creatingBuiltin, setCreatingBuiltin] = useState(false);

  // 获取工具列表
  const fetchTools = async (page = 1, pageSize = 10) => {
    setLoading(true);
    try {
      const params = {
        skip: (page - 1) * pageSize,
        limit: pageSize,
      };

      if (typeFilter !== 'all') {
        params.type = typeFilter;
      }

      if (statusFilter !== 'all') {
        params.status = statusFilter;
      }

      const response = await apiService.project.ai.getTools(currentProject.id, params);
      if (response.success) {
        setTools(response.data || []);
        setPagination({
          ...pagination,
          current: page,
          pageSize,
          total: response.total || 0,
        });
      } else {
        message.error(response.message || '获取工具列表失败');
      }
    } catch (error) {
      console.error('获取工具列表出错:', error);
      message.error('获取工具列表失败，请稍后再试');
    } finally {
      setLoading(false);
    }
  };

  // 首次加载
  useEffect(() => {
    if (currentProject?.id) {
      fetchTools(pagination.current, pagination.pageSize);
    }
  }, [currentProject?.id, typeFilter, statusFilter]);

  // 搜索
  const handleSearch = () => {
    fetchTools(1, pagination.pageSize);
  };

  // 表格变化
  const handleTableChange = (pagination) => {
    fetchTools(pagination.current, pagination.pageSize);
  };

  // 删除工具
  const handleDelete = async (id) => {
    try {
      const response = await apiService.project.ai.deleteTool(currentProject.id, id);
      if (response.success) {
        message.success('删除工具成功');
        fetchTools(pagination.current, pagination.pageSize);
      } else {
        message.error(response.message || '删除工具失败');
      }
    } catch (error) {
      console.error('删除工具出错:', error);
      message.error('删除工具失败，请稍后再试');
    }
  };

  // 确认删除
  const showDeleteConfirm = (id, name) => {
    confirm({
      title: '确定要删除这个工具吗?',
      content: `工具名称: ${name}`,
      icon: <ExclamationCircleOutlined />,
      okText: '删除',
      okType: 'danger',
      cancelText: '取消',
      onOk() {
        return handleDelete(id);
      },
    });
  };

  // 创建内置工具
  const createBuiltinTools = async () => {
    setCreatingBuiltin(true);
    try {
      const response = await apiService.project.ai.createBuiltinTools(currentProject.id);
      if (response.success) {
        message.success('创建内置工具成功');
        fetchTools(pagination.current, pagination.pageSize);
      } else {
        message.error(response.message || '创建内置工具失败');
      }
    } catch (error) {
      console.error('创建内置工具出错:', error);
      message.error('创建内置工具失败，请稍后再试');
    } finally {
      setCreatingBuiltin(false);
    }
  };

  // 打开抽屉
  const showDrawer = (tool = null) => {
    setCurrentTool(tool);
    if (tool) {
      form.setFieldsValue({
        name: tool.name,
        description: tool.description,
        type: tool.type,
        function_name: tool.function_name,
        function_description: tool.function_description,
        parameters_schema: JSON.stringify(tool.parameters_schema || {}, null, 2),
        implementation: tool.implementation,
        status: tool.status,
      });
    } else {
      form.resetFields();
      form.setFieldsValue({
        type: 'function',
        status: 'active',
        parameters_schema: JSON.stringify({
          type: 'object',
          properties: {},
          required: []
        }, null, 2),
      });
    }
    setDrawerVisible(true);
  };

  // 关闭抽屉
  const closeDrawer = () => {
    setDrawerVisible(false);
    setCurrentTool(null);
    form.resetFields();
  };

  // 保存工具
  const handleSave = async () => {
    try {
      const values = await form.validateFields();
      
      // 解析 JSON
      try {
        values.parameters_schema = JSON.parse(values.parameters_schema);
      } catch (e) {
        message.error('参数模式 JSON 格式错误');
        return;
      }

      if (currentTool) {
        // 更新工具
        const response = await apiService.project.ai.updateTool(currentProject.id, currentTool.id, values);
        if (response.success) {
          message.success('更新工具成功');
          closeDrawer();
          fetchTools(pagination.current, pagination.pageSize);
        } else {
          message.error(response.message || '更新工具失败');
        }
      } else {
        // 创建工具
        const response = await apiService.project.ai.createTool(currentProject.id, values);
        if (response.success) {
          message.success('创建工具成功');
          closeDrawer();
          fetchTools(1, pagination.pageSize);
        } else {
          message.error(response.message || '创建工具失败');
        }
      }
    } catch (error) {
      console.error('保存工具出错:', error);
    }
  };

  // 表格列定义
  const columns = [
    {
      title: '工具名称',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <Space>
          {record.is_builtin && <Badge status="processing" color="blue" />}
          <span>{text}</span>
        </Space>
      ),
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      render: (type) => {
        let color = 'default';
        let icon = null;
        let text = type;

        if (type === 'function') {
          color = 'blue';
          icon = <FunctionOutlined />;
          text = '函数';
        } else if (type === 'retrieval') {
          color = 'green';
          icon = <DatabaseOutlined />;
          text = '检索';
        } else if (type === 'code_interpreter') {
          color = 'purple';
          icon = <CodeOutlined />;
          text = '代码解释器';
        }

        return (
          <Tag color={color} icon={icon}>
            {text}
          </Tag>
        );
      },
    },
    {
      title: '函数名称',
      dataIndex: 'function_name',
      key: 'function_name',
      render: (text) => text || '-',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={status === 'active' ? 'green' : 'default'}>
          {status === 'active' ? '启用' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="编辑">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => showDrawer(record)}
              disabled={record.is_builtin}
            />
          </Tooltip>
          <Tooltip title="删除">
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
              onClick={() => showDeleteConfirm(record.id, record.name)}
              disabled={record.is_builtin}
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Card variant="outlined">
        <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 16 }}>
          <Title level={4}>AI 工具管理</Title>
          <Space>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => showDrawer()}
            >
              创建工具
            </Button>
            <Button
              icon={<SettingOutlined />}
              onClick={createBuiltinTools}
              loading={creatingBuiltin}
            >
              创建内置工具
            </Button>
          </Space>
        </div>

        <div style={{ marginBottom: 16, display: 'flex', gap: 8 }}>
          <Input
            placeholder="搜索工具名称"
            value={searchText}
            onChange={(e) => setSearchText(e.target.value)}
            onPressEnter={handleSearch}
            style={{ width: 200 }}
            prefix={<SearchOutlined />}
          />
          <Select
            value={typeFilter}
            onChange={setTypeFilter}
            style={{ width: 150 }}
          >
            <Option value="all">全部类型</Option>
            <Option value="function">函数</Option>
            <Option value="retrieval">检索</Option>
            <Option value="code_interpreter">代码解释器</Option>
          </Select>
          <Select
            value={statusFilter}
            onChange={setStatusFilter}
            style={{ width: 120 }}
          >
            <Option value="all">全部状态</Option>
            <Option value="active">启用</Option>
            <Option value="inactive">禁用</Option>
          </Select>
          <Button
            icon={<SyncOutlined />}
            onClick={() => fetchTools(pagination.current, pagination.pageSize)}
          >
            刷新
          </Button>
        </div>

        <Table
          columns={columns}
          dataSource={tools}
          rowKey="id"
          pagination={pagination}
          loading={loading}
          onChange={handleTableChange}
        />
      </Card>

      {/* 工具表单抽屉 */}
      <Drawer
        title={currentTool ? '编辑工具' : '创建工具'}
        width={720}
        onClose={closeDrawer}
        open={drawerVisible}
        styles={{ body: { paddingBottom: 80 } }}
        footer={
          <div style={{ textAlign: 'right' }}>
            <Button onClick={closeDrawer} style={{ marginRight: 8 }}>
              取消
            </Button>
            <Button onClick={handleSave} type="primary">
              保存
            </Button>
          </div>
        }
      >
        <Form
          form={form}
          layout="vertical"
          initialValues={{
            type: 'function',
            status: 'active',
          }}
        >
          <Form.Item
            name="name"
            label="工具名称"
            rules={[{ required: true, message: '请输入工具名称' }]}
          >
            <Input placeholder="请输入工具名称" />
          </Form.Item>

          <Form.Item
            name="description"
            label="描述"
            rules={[{ required: true, message: '请输入工具描述' }]}
          >
            <Input.TextArea rows={2} placeholder="请输入工具描述" />
          </Form.Item>

          <Form.Item
            name="type"
            label="类型"
            rules={[{ required: true, message: '请选择工具类型' }]}
          >
            <Radio.Group>
              <Radio value="function">函数</Radio>
              <Radio value="retrieval">检索</Radio>
              <Radio value="code_interpreter">代码解释器</Radio>
            </Radio.Group>
          </Form.Item>

          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) => prevValues.type !== currentValues.type}
          >
            {({ getFieldValue }) => {
              const type = getFieldValue('type');
              return type === 'function' ? (
                <>
                  <Form.Item
                    name="function_name"
                    label="函数名称"
                    rules={[{ required: true, message: '请输入函数名称' }]}
                  >
                    <Input placeholder="请输入函数名称" />
                  </Form.Item>

                  <Form.Item
                    name="function_description"
                    label="函数描述"
                  >
                    <Input.TextArea rows={2} placeholder="请输入函数描述" />
                  </Form.Item>

                  <Form.Item
                    name="parameters_schema"
                    label="参数模式 (JSON)"
                    rules={[
                      { required: true, message: '请输入参数模式' },
                      {
                        validator: (_, value) => {
                          try {
                            JSON.parse(value);
                            return Promise.resolve();
                          } catch (error) {
                            return Promise.reject(new Error('JSON 格式错误'));
                          }
                        },
                      },
                    ]}
                  >
                    <Input.TextArea rows={6} placeholder="请输入参数模式 JSON" />
                  </Form.Item>

                  <Form.Item
                    name="implementation"
                    label="实现代码"
                    rules={[{ required: true, message: '请输入实现代码' }]}
                  >
                    <Input.TextArea rows={10} placeholder="请输入实现代码" />
                  </Form.Item>
                </>
              ) : null;
            }}
          </Form.Item>

          <Form.Item
            name="status"
            label="状态"
            rules={[{ required: true, message: '请选择状态' }]}
          >
            <Radio.Group>
              <Radio value="active">启用</Radio>
              <Radio value="inactive">禁用</Radio>
            </Radio.Group>
          </Form.Item>
        </Form>
      </Drawer>
    </div>
  );
};

export default AIToolList;
