from sqlalchemy import Column, String, Boolean, DateTime, ForeignKey, JSON, Text, Integer, UniqueConstraint
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid

from db.database import Base

class Tenant(Base):
    """租户模型"""
    __tablename__ = "tenants"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    service_provider_id = Column(UUID(as_uuid=True), ForeignKey("service_providers.id"), nullable=True)  # 可为空表示直接由超级管理员管理
    name = Column(String(100), nullable=False)
    industry_type = Column(String(50), nullable=False)  # 行业类型
    is_direct = Column(Boolean, default=False)  # 是否直接由超级管理员管理
    schema_name = Column(String(100), nullable=False, unique=True)  # 数据库Schema名称
    status = Column(String(20), default="active", nullable=False)  # active, inactive, suspended, trial
    subscription_plan_id = Column(UUID(as_uuid=True), ForeignKey("subscription_plans.id"), nullable=True)
    subscription_start_date = Column(DateTime(timezone=True), nullable=True)
    subscription_end_date = Column(DateTime(timezone=True), nullable=True)
    max_projects = Column(Integer, default=1)  # 最大项目数
    max_users = Column(Integer, default=10)  # 最大用户数
    max_storage_gb = Column(Integer, default=5)  # 最大存储空间(GB)
    settings = Column(JSON, default=dict)  # 租户特定设置
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 关系
    service_provider = relationship("ServiceProvider", back_populates="tenants")
    subscription_plan = relationship("SubscriptionPlan", back_populates="tenants")
    projects = relationship("Project", back_populates="tenant", cascade="all, delete-orphan")
    users = relationship("User", back_populates="tenant")
    product_brands = relationship("ProductBrand", back_populates="tenant")
    product_categories = relationship("ProductCategory", back_populates="tenant")
    products = relationship("Product", back_populates="tenant")
    inventory_items = relationship("InventoryItem", back_populates="tenant")
    suppliers = relationship("Supplier", back_populates="tenant")
    supplier_products = relationship("SupplierProduct", back_populates="tenant")
    supplier_evaluations = relationship("SupplierEvaluation", back_populates="tenant")
    purchase_orders = relationship("PurchaseOrder", back_populates="tenant")
    purchase_order_items = relationship("PurchaseOrderItem", back_populates="tenant")
    plugins = relationship("TenantPlugin", back_populates="tenant", cascade="all, delete-orphan")
    plugin_purchases = relationship("PluginPurchase", back_populates="tenant", cascade="all, delete-orphan")
    # AI 相关关系
    ai_configs = relationship("AIConfig", back_populates="tenant", cascade="all, delete-orphan")
    ai_assistants = relationship("AIAssistant", back_populates="tenant", cascade="all, delete-orphan")
    ai_tools = relationship("AIAssistantTool", back_populates="tenant", cascade="all, delete-orphan")
    ai_knowledge_bases = relationship("AIKnowledgeBase", back_populates="tenant", cascade="all, delete-orphan")
    ai_usages = relationship("AIUsage", back_populates="tenant", cascade="all, delete-orphan")

    # 数据共享关系
    shared_data_as_source = relationship("TenantDataSharing", foreign_keys="TenantDataSharing.source_tenant_id", back_populates="source_tenant", cascade="all, delete-orphan")
    shared_data_as_target = relationship("TenantDataSharing", foreign_keys="TenantDataSharing.target_tenant_id", back_populates="target_tenant", cascade="all, delete-orphan")
    
    # 异步任务关系
    async_tasks = relationship("AsyncTask", back_populates="tenant", cascade="all, delete-orphan")

    __table_args__ = (
        # 对于有服务商的租户，名称在该服务商下唯一
        # 对于直接管理的租户，名称全局唯一
        UniqueConstraint('service_provider_id', 'name', name='uq_tenant_name_per_provider'),
    )

    def __repr__(self):
        return f"<Tenant(id={self.id}, name='{self.name}', industry='{self.industry_type}')>"

