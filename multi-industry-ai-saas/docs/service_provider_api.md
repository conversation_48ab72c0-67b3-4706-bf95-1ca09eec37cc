# 服务商管理系统 API 设计

## 1. API 概述

服务商管理系统 API 采用 RESTful 风格设计，基于 HTTP 协议，使用 JSON 作为数据交换格式。API 的基础路径为 `/api/v1/service-provider`。

### 1.1 认证方式

API 使用 JWT（JSON Web Token）进行认证，客户端需要在请求头中携带 `Authorization` 字段，格式为 `Bearer {token}`。

### 1.2 响应格式

所有 API 响应均使用统一的 JSON 格式：

```json
{
  "success": true,
  "message": "操作成功",
  "data": { ... },
  "error": null
}
```

错误响应：

```json
{
  "success": false,
  "message": "操作失败",
  "data": null,
  "error": {
    "code": "ERROR_CODE",
    "detail": "详细错误信息"
  }
}
```

## 2. API 端点

### 2.1 认证相关 API

#### 2.1.1 登录

- **URL**: `/api/v1/service-provider/auth/login`
- **方法**: POST
- **描述**: 服务商管理员登录
- **请求体**:
  ```json
  {
    "username": "admin",
    "password": "password"
  }
  ```
- **响应**:
  ```json
  {
    "success": true,
    "message": "登录成功",
    "data": {
      "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "user": {
        "id": "uuid",
        "username": "admin",
        "name": "管理员",
        "role": "admin"
      }
    }
  }
  ```

#### 2.1.2 获取当前用户信息

- **URL**: `/api/v1/service-provider/auth/me`
- **方法**: GET
- **描述**: 获取当前登录用户信息
- **响应**:
  ```json
  {
    "success": true,
    "message": "获取成功",
    "data": {
      "id": "uuid",
      "username": "admin",
      "name": "管理员",
      "role": "admin",
      "permissions": ["user:read", "user:write", ...]
    }
  }
  ```

### 2.2 租户管理 API

#### 2.2.1 获取租户列表

- **URL**: `/api/v1/service-provider/tenants`
- **方法**: GET
- **描述**: 获取租户列表
- **查询参数**:
  - `page`: 页码，默认 1
  - `page_size`: 每页数量，默认 20
  - `status`: 状态筛选，可选值：active, inactive, pending, rejected
  - `search`: 搜索关键词
- **响应**:
  ```json
  {
    "success": true,
    "message": "获取成功",
    "data": {
      "items": [
        {
          "id": "uuid",
          "name": "租户名称",
          "code": "tenant_code",
          "status": "active",
          "created_at": "2023-01-01T00:00:00Z",
          "projects_count": 5,
          "users_count": 10
        }
      ],
      "total": 100,
      "page": 1,
      "page_size": 20,
      "pages": 5
    }
  }
  ```

#### 2.2.2 创建租户

- **URL**: `/api/v1/service-provider/tenants`
- **方法**: POST
- **描述**: 创建新租户
- **请求体**:
  ```json
  {
    "name": "租户名称",
    "code": "tenant_code",
    "description": "租户描述",
    "contact_person": "联系人",
    "contact_phone": "联系电话",
    "contact_email": "联系邮箱",
    "address": "地址",
    "industry_type": "retail"
  }
  ```
- **响应**:
  ```json
  {
    "success": true,
    "message": "创建成功",
    "data": {
      "id": "uuid",
      "name": "租户名称",
      "code": "tenant_code",
      "status": "active",
      "created_at": "2023-01-01T00:00:00Z"
    }
  }
  ```

### 2.3 项目管理 API

#### 2.3.1 获取项目列表

- **URL**: `/api/v1/service-provider/projects`
- **方法**: GET
- **描述**: 获取项目列表
- **查询参数**:
  - `page`: 页码，默认 1
  - `page_size`: 每页数量，默认 20
  - `tenant_id`: 租户 ID
  - `status`: 状态筛选，可选值：active, inactive, pending, rejected, trial
  - `search`: 搜索关键词
- **响应**:
  ```json
  {
    "success": true,
    "message": "获取成功",
    "data": {
      "items": [
        {
          "id": "uuid",
          "name": "项目名称",
          "code": "project_code",
          "tenant_id": "uuid",
          "tenant_name": "租户名称",
          "status": "active",
          "created_at": "2023-01-01T00:00:00Z",
          "package": {
            "id": "uuid",
            "name": "套餐名称",
            "expire_at": "2023-12-31T00:00:00Z"
          }
        }
      ],
      "total": 100,
      "page": 1,
      "page_size": 20,
      "pages": 5
    }
  }
  ```

#### 2.3.2 创建项目

- **URL**: `/api/v1/service-provider/projects`
- **方法**: POST
- **描述**: 创建新项目
- **请求体**:
  ```json
  {
    "name": "项目名称",
    "code": "project_code",
    "tenant_id": "uuid",
    "description": "项目描述",
    "industry_type": "retail",
    "package_id": "uuid",
    "is_trial": false
  }
  ```
- **响应**:
  ```json
  {
    "success": true,
    "message": "创建成功",
    "data": {
      "id": "uuid",
      "name": "项目名称",
      "code": "project_code",
      "tenant_id": "uuid",
      "status": "active",
      "created_at": "2023-01-01T00:00:00Z"
    }
  }
  ```

### 2.4 套餐管理 API

#### 2.4.1 获取套餐列表

- **URL**: `/api/v1/service-provider/packages`
- **方法**: GET
- **描述**: 获取套餐列表
- **查询参数**:
  - `page`: 页码，默认 1
  - `page_size`: 每页数量，默认 20
  - `status`: 状态筛选，可选值：active, inactive
  - `is_trial`: 是否为试用套餐
- **响应**:
  ```json
  {
    "success": true,
    "message": "获取成功",
    "data": {
      "items": [
        {
          "id": "uuid",
          "name": "套餐名称",
          "code": "package_code",
          "price": 999.00,
          "duration": 365,
          "is_trial": false,
          "status": "active",
          "created_at": "2023-01-01T00:00:00Z"
        }
      ],
      "total": 10,
      "page": 1,
      "page_size": 20,
      "pages": 1
    }
  }
  ```

#### 2.4.2 创建套餐

- **URL**: `/api/v1/service-provider/packages`
- **方法**: POST
- **描述**: 创建新套餐
- **请求体**:
  ```json
  {
    "name": "套餐名称",
    "code": "package_code",
    "description": "套餐描述",
    "price": 999.00,
    "original_price": 1299.00,
    "duration": 365,
    "features": ["feature1", "feature2"],
    "limits": {
      "storage": 10240,
      "users": 50,
      "products": 1000
    },
    "is_trial": false
  }
  ```
- **响应**:
  ```json
  {
    "success": true,
    "message": "创建成功",
    "data": {
      "id": "uuid",
      "name": "套餐名称",
      "code": "package_code",
      "price": 999.00,
      "duration": 365,
      "is_trial": false,
      "status": "active",
      "created_at": "2023-01-01T00:00:00Z"
    }
  }
  ```

### 2.5 插件管理 API

#### 2.5.1 获取插件列表

- **URL**: `/api/v1/service-provider/plugins`
- **方法**: GET
- **描述**: 获取插件列表
- **查询参数**:
  - `page`: 页码，默认 1
  - `page_size`: 每页数量，默认 20
  - `category_id`: 分类 ID
  - `status`: 状态筛选，可选值：active, inactive
- **响应**:
  ```json
  {
    "success": true,
    "message": "获取成功",
    "data": {
      "items": [
        {
          "id": "uuid",
          "name": "插件名称",
          "code": "plugin_code",
          "version": "1.0.0",
          "price": 99.00,
          "category_id": "uuid",
          "category_name": "插件分类",
          "status": "active",
          "created_at": "2023-01-01T00:00:00Z"
        }
      ],
      "total": 20,
      "page": 1,
      "page_size": 20,
      "pages": 1
    }
  }
  ```
