import React, { useState, useEffect } from 'react';
import {
  Card, Row, Col, Table, Button, Space, Tag, Progress, 
  Statistic, Alert, Tabs, Select, DatePicker, Input,
  Modal, Form, message, Tooltip, Badge, Empty, Spin,
  Avatar, List, Descriptions
} from 'antd';
import {
  UserOutlined, TeamOutlined, HeartOutlined,
  CrownOutlined, TrophyOutlined, SyncOutlined,
  Bar<PERSON><PERSON>Outlined, Line<PERSON>hartOutlined, Pie<PERSON>hartOutlined,
  SearchOutlined, FilterOutlined, ExportOutlined,
  RobotOutlined, ShoppingOutlined, DollarOutlined,
  ClockCircleOutlined, EnvironmentOutlined, PhoneOutlined
} from '@ant-design/icons';
import { Line, Column, Pie, Radar } from '@ant-design/plots';
import { api } from '../../../../services/api';

const { TabPane } = Tabs;
const { Option } = Select;
const { RangePicker } = DatePicker;
const { Search } = Input;

/**
 * 客户分析组件
 * 分析客户画像、行为模式、价值分层等
 */
const CustomerAnalysis = () => {
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('profile');
  const [customerData, setCustomerData] = useState([]);
  const [profileData, setProfileData] = useState([]);
  const [behaviorData, setBehaviorData] = useState([]);
  const [valueData, setValueData] = useState([]);
  const [segmentData, setSegmentData] = useState([]);
  const [filters, setFilters] = useState({
    segment: 'all',
    dateRange: null,
    keyword: ''
  });
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    fetchCustomerAnalysisData();
  }, [filters]);

  // 获取客户分析数据
  const fetchCustomerAnalysisData = async () => {
    try {
      setLoading(true);
      
      // 获取项目ID
      const projectId = localStorage.getItem('project_id');
      if (!projectId) {
        throw new Error('未找到项目ID');
      }
      
      const params = {
        segment: filters.segment,
        dateRange: filters.dateRange,
        keyword: filters.keyword
      };

      // 使用真实的API端点
      const response = await api.get(`/project/${projectId}/business-analysis/customer-analysis`, { params });
      
      if (response && response.data) {
        setCustomerData(response.data.customers || []);
        setProfileData(response.data.profile || []);
        setBehaviorData(response.data.behavior || []);
        setValueData(response.data.value || []);
        setSegmentData(response.data.segments || []);
      }
    } catch (error) {
      console.error('获取客户分析数据失败:', error);
      message.error('获取数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 刷新数据
  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchCustomerAnalysisData();
    setRefreshing(false);
    message.success('数据已刷新');
  };

  // 客户画像表格列
  const profileColumns = [
    {
      title: '客户信息',
      dataIndex: 'customer',
      key: 'customer',
      width: 200,
      fixed: 'left',
      render: (customer) => (
        <Space>
          <Avatar 
            src={customer.avatar} 
            icon={<UserOutlined />}
            size={40}
          />
          <div>
            <div style={{ fontWeight: 500 }}>{customer.name}</div>
            <div style={{ fontSize: 12, color: '#666' }}>
              {customer.phone} | {customer.gender} | {customer.age}岁
            </div>
          </div>
        </Space>
      )
    },
    {
      title: '客户等级',
      dataIndex: 'level',
      key: 'level',
      width: 100,
      render: (level) => {
        const levelConfig = {
          'vip': { color: 'gold', icon: <CrownOutlined />, text: 'VIP' },
          'premium': { color: 'purple', icon: <TrophyOutlined />, text: '高级' },
          'regular': { color: 'blue', icon: <UserOutlined />, text: '普通' },
          'new': { color: 'green', icon: <HeartOutlined />, text: '新客' }
        };
        const config = levelConfig[level] || levelConfig.regular;
        return (
          <Tag color={config.color} icon={config.icon}>
            {config.text}
          </Tag>
        );
      }
    },
    {
      title: '消费能力',
      dataIndex: 'spendingPower',
      key: 'spendingPower',
      width: 120,
      render: (power) => (
        <div>
          <Progress 
            percent={power} 
            size="small" 
            strokeColor={
              power >= 80 ? '#52c41a' : 
              power >= 60 ? '#faad14' : '#ff4d4f'
            }
          />
          <div style={{ fontSize: 12, marginTop: 4 }}>
            {power >= 80 ? '高消费' : power >= 60 ? '中消费' : '低消费'}
          </div>
        </div>
      )
    },
    {
      title: '活跃度',
      dataIndex: 'activity',
      key: 'activity',
      width: 100,
      render: (activity) => (
        <Tag color={activity >= 80 ? 'green' : activity >= 60 ? 'orange' : 'red'}>
          {activity >= 80 ? '高活跃' : activity >= 60 ? '中活跃' : '低活跃'}
        </Tag>
      )
    },
    {
      title: '偏好类别',
      dataIndex: 'preferences',
      key: 'preferences',
      width: 150,
      render: (preferences) => (
        <div>
          {preferences?.slice(0, 2).map((pref, index) => (
            <Tag key={index} size="small">{pref}</Tag>
          ))}
          {preferences?.length > 2 && <Tag size="small">+{preferences.length - 2}</Tag>}
        </div>
      )
    },
    {
      title: '地理位置',
      dataIndex: 'location',
      key: 'location',
      width: 120,
      render: (location) => (
        <Tooltip title={location.detail}>
          <Space size="small">
            <EnvironmentOutlined style={{ color: '#1890ff' }} />
            <span>{location.city}</span>
          </Space>
        </Tooltip>
      )
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Button size="small" type="link">详情</Button>
          <Button size="small" type="link">营销</Button>
        </Space>
      )
    }
  ];

  // 渲染客户画像分析
  const renderCustomerProfile = () => {
    // 使用真实的年龄分布数据
    const ageDistribution = profileData.ageStats || [];

    const ageConfig = {
      data: ageDistribution,
      xField: 'age',
      yField: 'count',
      label: {
        position: 'top',
        style: {
          fill: '#FFFFFF',
          opacity: 0.6,
        },
      },
      meta: {
        age: { alias: '年龄段' },
        count: { alias: '人数' },
      },
    };

    return (
      <div>
        <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
          <Col xs={24} sm={6}>
            <Card>
              <Statistic
                title="总用户数"
                value={customerData.summary?.total_users || 0}
                prefix={<TeamOutlined style={{ color: '#1890ff' }} />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={6}>
            <Card>
              <Statistic
                title="钉钉绑定用户"
                value={customerData.summary?.dingtalk_users || 0}
                suffix="人"
                prefix={<UserOutlined style={{ color: '#52c41a' }} />}
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={6}>
            <Card>
              <Statistic
                title="AI交互次数"
                value={customerData.summary?.ai_interactions || 0}
                suffix="次"
                prefix={<RobotOutlined style={{ color: '#faad14' }} />}
                valueStyle={{ color: '#faad14' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={6}>
            <Card>
              <Statistic
                title="活跃用户"
                value={customerData.summary?.active_users || 0}
                suffix="人"
                prefix={<HeartOutlined style={{ color: '#722ed1' }} />}
                valueStyle={{ color: '#722ed1' }}
              />
            </Card>
          </Col>
        </Row>

        <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
          <Col xs={24} lg={12}>
            <Card title="年龄分布">
              {ageDistribution.length > 0 ? (
                <Column {...ageConfig} height={250} />
              ) : (
                <Empty description="暂无年龄分布数据，需要用户完善个人资料" />
              )}
            </Card>
          </Col>
          <Col xs={24} lg={12}>
            <Card title="地域分布">
              <div className="location-stats">
                {profileData.locationStats?.length > 0 ? (
                  profileData.locationStats.map((item, index) => (
                    <div key={index} style={{ 
                      display: 'flex', 
                      justifyContent: 'space-between', 
                      alignItems: 'center',
                      padding: '8px 0',
                      borderBottom: index < profileData.locationStats.length - 1 ? '1px solid #f0f0f0' : 'none'
                    }}>
                      <Space>
                        <EnvironmentOutlined style={{ color: '#1890ff' }} />
                        <span>{item.city}</span>
                      </Space>
                      <div style={{ textAlign: 'right' }}>
                        <div style={{ fontWeight: 500 }}>{item.count}人</div>
                        <div style={{ fontSize: 12, color: '#666' }}>{item.percentage}%</div>
                      </div>
                    </div>
                  ))
                ) : (
                  <Empty description="暂无地域分布数据，需要用户完善个人资料" />
                )}
              </div>
            </Card>
          </Col>
        </Row>

        <Card title="客户详细画像">
          {profileData.customers?.length > 0 ? (
            <Table
              columns={profileColumns}
              dataSource={profileData.customers}
              rowKey="id"
              scroll={{ x: 1200 }}
              pagination={{ pageSize: 10 }}
              loading={loading}
            />
          ) : (
            <Empty description="暂无客户详细数据，随着系统使用会逐步积累" />
          )}
        </Card>
      </div>
    );
  };

  // 渲染行为分析
  const renderBehaviorAnalysis = () => {
    const behaviorTrendData = behaviorData.trends || [];
    
    const trendConfig = {
      data: behaviorTrendData,
      xField: 'date',
      yField: 'value',
      seriesField: 'type',
      smooth: true,
      animation: {
        appear: {
          animation: 'path-in',
          duration: 1000,
        },
      },
    };

    return (
      <div>
        <Alert
          message="客户行为分析"
          description="基于客户的购买行为、浏览记录、互动数据等，分析客户的行为模式和偏好。"
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />

        <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
          <Col xs={24} lg={16}>
            <Card title="行为趋势分析">
              <Line {...trendConfig} height={300} />
            </Card>
          </Col>
          <Col xs={24} lg={8}>
            <Card title="行为特征">
              <div className="behavior-features">
                {behaviorData.features?.map((feature, index) => (
                  <div key={index} style={{ marginBottom: 16 }}>
                    <div style={{ 
                      display: 'flex', 
                      justifyContent: 'space-between', 
                      marginBottom: 8 
                    }}>
                      <span>{feature.name}</span>
                      <span style={{ fontWeight: 500 }}>{feature.score}%</span>
                    </div>
                    <Progress 
                      percent={feature.score} 
                      size="small"
                      strokeColor={feature.score >= 70 ? '#52c41a' : '#1890ff'}
                    />
                  </div>
                )) || <Empty description="暂无数据" />}
              </div>
            </Card>
          </Col>
        </Row>

        <Row gutter={[16, 16]}>
          <Col xs={24} lg={12}>
            <Card title="购买时间偏好">
              <div className="time-preferences">
                {behaviorData.timePreferences?.map((time, index) => (
                  <div key={index} style={{ 
                    display: 'flex', 
                    justifyContent: 'space-between', 
                    alignItems: 'center',
                    padding: '8px 0',
                    borderBottom: index < behaviorData.timePreferences.length - 1 ? '1px solid #f0f0f0' : 'none'
                  }}>
                    <Space>
                      <ClockCircleOutlined style={{ color: '#1890ff' }} />
                      <span>{time.period}</span>
                    </Space>
                    <div style={{ textAlign: 'right' }}>
                      <div style={{ fontWeight: 500 }}>{time.percentage}%</div>
                      <Progress 
                        percent={time.percentage} 
                        size="small" 
                        showInfo={false}
                        strokeColor="#1890ff"
                      />
                    </div>
                  </div>
                )) || <Empty description="暂无数据" />}
              </div>
            </Card>
          </Col>

          <Col xs={24} lg={12}>
            <Card title="渠道偏好">
              <div className="channel-preferences">
                {behaviorData.channelPreferences?.map((channel, index) => (
                  <div key={index} style={{ 
                    display: 'flex', 
                    justifyContent: 'space-between', 
                    alignItems: 'center',
                    padding: '8px 0',
                    borderBottom: index < behaviorData.channelPreferences.length - 1 ? '1px solid #f0f0f0' : 'none'
                  }}>
                    <Space>
                      <ShoppingOutlined style={{ color: '#52c41a' }} />
                      <span>{channel.name}</span>
                    </Space>
                    <div style={{ textAlign: 'right' }}>
                      <div style={{ fontWeight: 500 }}>{channel.percentage}%</div>
                      <Progress 
                        percent={channel.percentage} 
                        size="small" 
                        showInfo={false}
                        strokeColor="#52c41a"
                      />
                    </div>
                  </div>
                )) || <Empty description="暂无数据" />}
              </div>
            </Card>
          </Col>
        </Row>
      </div>
    );
  };

  // 渲染价值分析
  const renderValueAnalysis = () => {
    const valueSegments = [
      { segment: '高价值客户', count: 124, percentage: 25, avgValue: 2580 },
      { segment: '中价值客户', count: 186, percentage: 37.5, avgValue: 1280 },
      { segment: '低价值客户', count: 148, percentage: 30, avgValue: 580 },
      { segment: '潜力客户', count: 38, percentage: 7.5, avgValue: 380 }
    ];

    return (
      <div>
        <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
          <Col xs={24} sm={6}>
            <Card>
              <Statistic
                title="客户总价值"
                value={658420}
                prefix="¥"
                precision={2}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={6}>
            <Card>
              <Statistic
                title="平均客户价值"
                value={1286}
                prefix="¥"
                precision={2}
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={6}>
            <Card>
              <Statistic
                title="客户生命周期价值"
                value={3580}
                prefix="¥"
                precision={2}
                valueStyle={{ color: '#faad14' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={6}>
            <Card>
              <Statistic
                title="客户获取成本"
                value={128}
                prefix="¥"
                precision={2}
                valueStyle={{ color: '#722ed1' }}
              />
            </Card>
          </Col>
        </Row>

        <Card title="客户价值分层" style={{ marginBottom: 16 }}>
          <Row gutter={[16, 16]}>
            {valueSegments.map((segment, index) => (
              <Col xs={24} sm={12} lg={6} key={index}>
                <Card size="small">
                  <div style={{ textAlign: 'center' }}>
                    <div style={{ fontSize: 16, fontWeight: 500, marginBottom: 8 }}>
                      {segment.segment}
                    </div>
                    <div style={{ fontSize: 24, fontWeight: 600, color: '#1890ff', marginBottom: 8 }}>
                      {segment.count}
                    </div>
                    <div style={{ fontSize: 12, color: '#666', marginBottom: 8 }}>
                      占比 {segment.percentage}%
                    </div>
                    <div style={{ fontSize: 14, color: '#52c41a' }}>
                      平均价值 ¥{segment.avgValue}
                    </div>
                  </div>
                </Card>
              </Col>
            ))}
          </Row>
        </Card>

        <Card title="价值分析详情">
          <List
            dataSource={valueData.details || []}
            renderItem={(item, index) => (
              <List.Item>
                <List.Item.Meta
                  avatar={<Avatar icon={<UserOutlined />} />}
                  title={
                    <Space>
                      <span>{item.customerName}</span>
                      <Tag color={item.segment === 'high' ? 'red' : item.segment === 'medium' ? 'orange' : 'blue'}>
                        {item.segment === 'high' ? '高价值' : item.segment === 'medium' ? '中价值' : '低价值'}
                      </Tag>
                    </Space>
                  }
                  description={
                    <div>
                      <div>总消费: ¥{item.totalSpent} | 订单数: {item.orderCount} | 平均订单: ¥{item.avgOrder}</div>
                      <div style={{ marginTop: 4 }}>
                        最近购买: {item.lastPurchase} | 活跃度: {item.activity}%
                      </div>
                    </div>
                  }
                />
                <div style={{ textAlign: 'right' }}>
                  <div style={{ fontSize: 16, fontWeight: 500, color: '#1890ff' }}>
                    ¥{item.predictedValue}
                  </div>
                  <div style={{ fontSize: 12, color: '#666' }}>
                    预测价值
                  </div>
                </div>
              </List.Item>
            )}
            pagination={{ pageSize: 5 }}
          />
        </Card>
      </div>
    );
  };

  if (loading && !refreshing) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>加载客户分析数据...</div>
      </div>
    );
  }

  return (
    <div className="customer-analysis">
      {/* 筛选工具栏 */}
      <Card style={{ marginBottom: 16 }}>
        <Row gutter={[16, 16]} align="middle">
          <Col xs={24} sm={8} md={6}>
            <Select
              placeholder="选择客户群体"
              style={{ width: '100%' }}
              value={filters.segment}
              onChange={(value) => setFilters({ ...filters, segment: value })}
            >
              <Option value="all">全部客户</Option>
              <Option value="vip">VIP客户</Option>
              <Option value="active">活跃客户</Option>
              <Option value="new">新客户</Option>
            </Select>
          </Col>
          <Col xs={24} sm={8} md={6}>
            <RangePicker
              style={{ width: '100%' }}
              onChange={(dates) => setFilters({ ...filters, dateRange: dates })}
            />
          </Col>
          <Col xs={24} sm={8} md={6}>
            <Search
              placeholder="搜索客户"
              onSearch={(value) => setFilters({ ...filters, keyword: value })}
              style={{ width: '100%' }}
            />
          </Col>
          <Col xs={24} sm={24} md={6}>
            <Space>
              <Button 
                icon={<SyncOutlined />} 
                onClick={handleRefresh}
                loading={refreshing}
              >
                刷新
              </Button>
              <Button icon={<ExportOutlined />}>
                导出
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 分析内容Tab */}
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane 
          tab={
            <Space>
              <UserOutlined />
              客户画像
            </Space>
          } 
          key="profile"
        >
          {renderCustomerProfile()}
        </TabPane>

        <TabPane 
          tab={
            <Space>
              <BarChartOutlined />
              行为分析
            </Space>
          } 
          key="behavior"
        >
          {renderBehaviorAnalysis()}
        </TabPane>

        <TabPane 
          tab={
            <Space>
              <DollarOutlined />
              价值分析
            </Space>
          } 
          key="value"
        >
          {renderValueAnalysis()}
        </TabPane>
      </Tabs>
    </div>
  );
};

export default CustomerAnalysis; 