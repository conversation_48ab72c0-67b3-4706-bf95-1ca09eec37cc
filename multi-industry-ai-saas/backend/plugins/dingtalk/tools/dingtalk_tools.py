#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
钉钉AI工具集
"""

import logging
import uuid
from typing import Dict, Any, List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_

from models.user import User, ThirdPartyAccount
from models.project import Project
from models.ai import AIAssistantTool
from ..utils.dingtalk_api import DingTalkAPI
from ..models.models import DingTalkUserMapping

logger = logging.getLogger(__name__)

class DingTalkAITools:
    """钉钉AI工具集"""
    
    @staticmethod
    async def create_dingtalk_tools(
        db: AsyncSession,
        tenant_id: uuid.UUID,
        project_id: uuid.UUID,
        user_id: uuid.UUID
    ) -> List[AIAssistantTool]:
        """创建钉钉相关的AI工具"""
        
        tools = []
        
        # 1. 发送钉钉消息工具
        send_message_tool = AIAssistantTool(
            tenant_id=tenant_id,
            project_id=project_id,
            name="钉钉消息发送工具",
            description="通过钉钉发送消息给用户或群组",
            type="function",
            function_name="send_dingtalk_message",
            function_description="发送钉钉消息",
            parameters_schema={
                "type": "object",
                "properties": {
                    "target_type": {
                        "type": "string",
                        "enum": ["user", "group"],
                        "description": "发送目标类型：user（用户）或 group（群组）"
                    },
                    "target_id": {
                        "type": "string",
                        "description": "目标ID（用户ID或群组ID）"
                    },
                    "message": {
                        "type": "string",
                        "description": "要发送的消息内容"
                    },
                    "message_type": {
                        "type": "string",
                        "enum": ["text", "card"],
                        "default": "text",
                        "description": "消息类型：text（文本）或 card（卡片）"
                    }
                },
                "required": ["target_type", "target_id", "message"]
            },
            implementation="""
async def send_dingtalk_message(db, project_id, user_id, target_type, target_id, message, message_type="text"):
    from services.system_config import SystemConfigService
    from plugins.dingtalk.utils.dingtalk_api import DingTalkAPI
    
    try:
        # 获取项目信息
        project_query = select(Project).where(Project.id == project_id)
        result = await db.execute(project_query)
        project = result.scalar_one_or_none()
        
        if not project:
            return {"success": False, "error": "项目不存在"}
        
        # 获取钉钉配置
        configs = await SystemConfigService.get_configs_by_type(db, project_id, "third_party_login")
        config_dict = {config.config_key: config.config_value for config in configs}
        dingtalk_config = config_dict.get("dingtalk", {})
        
        app_key = dingtalk_config.get("app_key")
        app_secret = dingtalk_config.get("app_secret")
        
        if not app_key or not app_secret:
            return {"success": False, "error": "钉钉配置不完整"}
        
        # 初始化钉钉API
        dingtalk_api = DingTalkAPI(app_key, app_secret)
        
        # 发送消息
        if target_type == "user":
            if message_type == "card":
                # 发送卡片消息
                card_data = await dingtalk_api.create_ai_card_message(
                    title="AI助手消息",
                    content=message
                )
                result = await dingtalk_api.send_interactive_card("user", target_id, card_data)
            else:
                # 发送文本消息
                result = await dingtalk_api.send_work_notification(
                    user_id=target_id,
                    message=f"🤖 AI助手：\\n\\n{message}"
                )
        elif target_type == "group":
            if message_type == "card":
                # 发送卡片消息
                card_data = await dingtalk_api.create_ai_card_message(
                    title="AI助手消息",
                    content=message
                )
                result = await dingtalk_api.send_interactive_card("group", target_id, card_data)
            else:
                # 发送文本消息
                result = await dingtalk_api.send_group_message(
                    chat_id=target_id,
                    message=f"🤖 AI助手：\\n\\n{message}"
                )
        else:
            return {"success": False, "error": "不支持的目标类型"}
        
        return result
        
    except Exception as e:
        return {"success": False, "error": str(e)}
            """,
            is_builtin=True,
            status="active",
            created_by=user_id
        )
        
        db.add(send_message_tool)
        tools.append(send_message_tool)
        
        # 2. 获取钉钉用户信息工具
        get_user_tool = AIAssistantTool(
            tenant_id=tenant_id,
            project_id=project_id,
            name="钉钉用户信息查询工具",
            description="查询钉钉用户的详细信息",
            type="function",
            function_name="get_dingtalk_user_info",
            function_description="获取钉钉用户信息",
            parameters_schema={
                "type": "object",
                "properties": {
                    "user_id": {
                        "type": "string",
                        "description": "钉钉用户ID"
                    },
                    "search_type": {
                        "type": "string",
                        "enum": ["userid", "mobile", "name"],
                        "default": "userid",
                        "description": "搜索类型：userid（用户ID）、mobile（手机号）或 name（姓名）"
                    }
                },
                "required": ["user_id"]
            },
            implementation="""
async def get_dingtalk_user_info(db, project_id, user_id, search_type="userid"):
    from services.system_config import SystemConfigService
    from plugins.dingtalk.utils.dingtalk_api import DingTalkAPI
    from plugins.dingtalk.models.models import DingTalkUserMapping
    
    try:
        # 获取项目信息
        project_query = select(Project).where(Project.id == project_id)
        result = await db.execute(project_query)
        project = result.scalar_one_or_none()
        
        if not project:
            return {"success": False, "error": "项目不存在"}
        
        # 获取钉钉配置
        configs = await SystemConfigService.get_configs_by_type(db, project_id, "third_party_login")
        config_dict = {config.config_key: config.config_value for config in configs}
        dingtalk_config = config_dict.get("dingtalk", {})
        
        app_key = dingtalk_config.get("app_key")
        app_secret = dingtalk_config.get("app_secret")
        
        if not app_key or not app_secret:
            return {"success": False, "error": "钉钉配置不完整"}
        
        # 初始化钉钉API
        dingtalk_api = DingTalkAPI(app_key, app_secret)
        
        # 根据搜索类型获取用户信息
        if search_type == "userid":
            user_info = await dingtalk_api.get_user_detail(user_id)
        else:
            # 从本地映射表搜索
            if search_type == "mobile":
                mapping_query = select(DingTalkUserMapping).where(
                    and_(
                        DingTalkUserMapping.tenant_id == project.tenant_id,
                        DingTalkUserMapping.project_id == project_id,
                        DingTalkUserMapping.dingtalk_mobile == user_id
                    )
                )
            elif search_type == "name":
                mapping_query = select(DingTalkUserMapping).where(
                    and_(
                        DingTalkUserMapping.tenant_id == project.tenant_id,
                        DingTalkUserMapping.project_id == project_id,
                        DingTalkUserMapping.dingtalk_name.ilike(f"%{user_id}%")
                    )
                )
            
            result = await db.execute(mapping_query)
            mapping = result.scalar_one_or_none()
            
            if mapping:
                user_info = await dingtalk_api.get_user_detail(mapping.dingtalk_user_id)
            else:
                user_info = None
        
        if user_info:
            return {
                "success": True,
                "data": {
                    "userid": user_info.get("userid"),
                    "name": user_info.get("name"),
                    "mobile": user_info.get("mobile"),
                    "email": user_info.get("email"),
                    "department": user_info.get("department"),
                    "position": user_info.get("position"),
                    "avatar": user_info.get("avatar")
                }
            }
        else:
            return {"success": False, "error": "用户不存在"}
        
    except Exception as e:
        return {"success": False, "error": str(e)}
            """,
            is_builtin=True,
            status="active",
            created_by=user_id
        )
        
        db.add(get_user_tool)
        tools.append(get_user_tool)
        
        # 3. 获取钉钉群组信息工具
        get_group_tool = AIAssistantTool(
            tenant_id=tenant_id,
            project_id=project_id,
            name="钉钉群组信息查询工具",
            description="查询钉钉群组的详细信息",
            type="function",
            function_name="get_dingtalk_group_info",
            function_description="获取钉钉群组信息",
            parameters_schema={
                "type": "object",
                "properties": {
                    "chat_id": {
                        "type": "string",
                        "description": "钉钉群组ID"
                    }
                },
                "required": ["chat_id"]
            },
            implementation="""
async def get_dingtalk_group_info(db, project_id, chat_id):
    from services.system_config import SystemConfigService
    from plugins.dingtalk.utils.dingtalk_api import DingTalkAPI
    
    try:
        # 获取项目信息
        project_query = select(Project).where(Project.id == project_id)
        result = await db.execute(project_query)
        project = result.scalar_one_or_none()
        
        if not project:
            return {"success": False, "error": "项目不存在"}
        
        # 获取钉钉配置
        configs = await SystemConfigService.get_configs_by_type(db, project_id, "third_party_login")
        config_dict = {config.config_key: config.config_value for config in configs}
        dingtalk_config = config_dict.get("dingtalk", {})
        
        app_key = dingtalk_config.get("app_key")
        app_secret = dingtalk_config.get("app_secret")
        
        if not app_key or not app_secret:
            return {"success": False, "error": "钉钉配置不完整"}
        
        # 初始化钉钉API
        dingtalk_api = DingTalkAPI(app_key, app_secret)
        
        # 获取群组信息
        group_info = await dingtalk_api.get_chat_info(chat_id)
        
        if group_info and group_info.get("errcode") == 0:
            chat_info = group_info.get("chat_info", {})
            return {
                "success": True,
                "data": {
                    "chatid": chat_info.get("chatid"),
                    "name": chat_info.get("name"),
                    "owner": chat_info.get("owner"),
                    "useridlist": chat_info.get("useridlist", []),
                    "member_count": len(chat_info.get("useridlist", [])),
                    "created_at": chat_info.get("created_at")
                }
            }
        else:
            return {"success": False, "error": "群组不存在或无权限访问"}
        
    except Exception as e:
        return {"success": False, "error": str(e)}
            """,
            is_builtin=True,
            status="active",
            created_by=user_id
        )
        
        db.add(get_group_tool)
        tools.append(get_group_tool)
        
        await db.commit()
        
        logger.info(f"创建钉钉AI工具成功，共 {len(tools)} 个工具")
        return tools
    
    @staticmethod
    async def get_dingtalk_tools(
        db: AsyncSession,
        tenant_id: uuid.UUID,
        project_id: uuid.UUID
    ) -> List[AIAssistantTool]:
        """获取钉钉相关的AI工具"""
        
        query = select(AIAssistantTool).where(
            and_(
                AIAssistantTool.tenant_id == tenant_id,
                AIAssistantTool.project_id == project_id,
                AIAssistantTool.function_name.in_([
                    "send_dingtalk_message",
                    "get_dingtalk_user_info", 
                    "get_dingtalk_group_info"
                ]),
                AIAssistantTool.status == "active"
            )
        )
        
        result = await db.execute(query)
        return result.scalars().all() 