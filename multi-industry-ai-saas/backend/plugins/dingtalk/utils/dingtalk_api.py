#!/usr/bin/env python
# -*- coding: utf-8 -*-

import logging
import httpx
import json
from typing import Dict, Any, Optional, List
import time
from datetime import datetime, timedelta
import hashlib
import hmac
import base64
import urllib.parse
import asyncio
from datetime import datetime as dt_module
from fastapi import HTTPException

# 数据库相关导入（可选）
try:
    from db.database import AsyncSessionLocal
    from models.user import User, ThirdPartyAccount
    DB_AVAILABLE = True
    logger = logging.getLogger(__name__)
    logger.debug("数据库模块导入成功")
except ImportError as e:
    AsyncSessionLocal = None
    User = None 
    ThirdPartyAccount = None
    DB_AVAILABLE = False
    logger = logging.getLogger(__name__)
    logger.warning(f"数据库模块导入失败: {e}，无法获取钉钉用户ID")

# 动态导入DingTalkUserMapping（避免循环导入）
try:
    from ..models.models import DingTalkUserMapping
    DINGTALK_MAPPING_AVAILABLE = True
    logger.debug("DingTalkUserMapping模型导入成功")
except ImportError as e:
    DingTalkUserMapping = None
    DINGTALK_MAPPING_AVAILABLE = False
    logger.warning(f"DingTalkUserMapping模型导入失败: {e}")

# 初始化日志
logger = logging.getLogger(__name__)

# 全局accessToken缓存
_ACCESS_TOKEN_CACHE = {
    "token": None,
    "expires_at": 0,
    "app_key": None
}

class DingTalkAPI:
    """钉钉API工具类"""
    
    def __init__(self, app_key: str, app_secret: str, agent_id: Optional[str] = None):
        """
        初始化钉钉API
        
        Args:
            app_key: 钉钉应用的AppKey
            app_secret: 钉钉应用的AppSecret
            agent_id: 应用的AgentID，用于发送工作通知
        """
        self.app_key = app_key
        self.app_secret = app_secret
        self.agent_id = agent_id
        self.base_url = "https://oapi.dingtalk.com"
        self.new_api_base = "https://api.dingtalk.com"
    
    async def get_access_token(self) -> Optional[str]:
        """
        获取企业应用访问令牌 (accessToken)
        
        根据钉钉官方文档：
        - accessToken有效期为7200秒（2小时）
        - 有效期内重复获取会返回相同结果并自动续期
        - 需要缓存accessToken，避免频繁调用
        
        Returns:
            str: 企业应用访问令牌
        """
        global _ACCESS_TOKEN_CACHE
        
        try:
            # 检查缓存是否有效（考虑app_key变化和过期时间）
            current_time = time.time()
            if (_ACCESS_TOKEN_CACHE["token"] and 
                _ACCESS_TOKEN_CACHE["app_key"] == self.app_key and
                current_time < _ACCESS_TOKEN_CACHE["expires_at"]):
                logger.debug(f"使用缓存的企业accessToken: {_ACCESS_TOKEN_CACHE['token'][:10]}...")
                return _ACCESS_TOKEN_CACHE["token"]
            
            # 获取新的accessToken
            url = f"{self.base_url}/gettoken"
            params = {
                "appkey": self.app_key,
                "appsecret": self.app_secret
            }
            
            logger.info(f"获取新的企业accessToken: {url}")
            logger.debug(f"请求参数: appkey={self.app_key}")
            
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.get(url, params=params)
                response_text = response.text
                logger.debug(f"企业accessToken响应状态: {response.status_code}")
                logger.debug(f"企业accessToken响应内容: {response_text}")
                
                if response.status_code == 200:
                    data = response.json()
                    if data.get("errcode") == 0:
                        access_token = data.get("access_token")
                        expires_in = data.get("expires_in", 7200)
                        
                        # 更新全局缓存（提前5分钟过期以确保安全）
                        _ACCESS_TOKEN_CACHE.update({
                            "token": access_token,
                            "expires_at": current_time + expires_in - 300,
                            "app_key": self.app_key
                        })
                        
                        logger.info(f"成功获取企业accessToken: {access_token[:10]}..., 有效期: {expires_in}秒")
                        return access_token
                    else:
                        logger.error(f"获取企业accessToken失败: errcode={data.get('errcode')}, errmsg={data.get('errmsg')}")
                        return None
                else:
                    logger.error(f"获取企业accessToken请求失败: status={response.status_code}, response={response_text}")
                    return None
        except Exception as e:
            logger.error(f"获取企业accessToken异常: {str(e)}", exc_info=True)
            return None
    
    async def get_user_access_token_by_userid(self, userid: str) -> Optional[str]:
        """
        根据钉钉官方文档第二种方式获取个人用户accessToken
        
        参考文档：https://open.dingtalk.com/document/orgapp/obtain-user-token
        API端点：POST /v1.0/oauth2/userAccessToken (使用最新v1.0 API)
        
        Args:
            userid: 钉钉用户ID
            
        Returns:
            str: 个人用户的accessToken
        """
        try:
            # 获取企业应用的accessToken
            corp_access_token = await self.get_access_token()
            if not corp_access_token:
                logger.error("获取企业accessToken失败，无法获取个人用户token")
                return None
            
            # 使用钉钉官方新版v1.0 API获取个人用户token
            url = f"{self.new_api_base}/v1.0/oauth2/userAccessToken"
            headers = {
                "Content-Type": "application/json"
            }
            data = {
                "clientId": self.app_key,
                "clientSecret": self.app_secret,
                "userid": userid,
                "grantType": "user_access_token"
            }
            
            logger.info(f"调用获取个人用户token API (v1.0): {url}, userid={userid}")
            
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.post(url, headers=headers, json=data)
                response_text = response.text
                logger.debug(f"个人用户token API响应状态: {response.status_code}")
                logger.debug(f"个人用户token API响应内容: {response_text}")
                
                if response.status_code == 200:
                    try:
                        result = response.json()
                        # 新版API可能直接返回accessToken字段
                        user_access_token = result.get("accessToken")
                        if user_access_token:
                            logger.info(f"成功获取个人用户accessToken: {user_access_token[:10]}...")
                            return user_access_token
                        else:
                            logger.error(f"响应中没有个人用户accessToken: {result}")
                            return None
                    except json.JSONDecodeError as e:
                        logger.error(f"解析个人用户token响应JSON失败: {e}, 响应内容: {response_text}")
                        return None
                else:
                    logger.error(f"个人用户token API请求失败: status={response.status_code}, response={response_text}")
                    return None
                    
        except Exception as e:
            logger.error(f"获取个人用户accessToken异常: {str(e)}", exc_info=True)
            return None
    
    async def get_user_info_by_code(self, code: str) -> Optional[Dict[str, Any]]:
        """
        通过授权码获取用户信息（使用标准OAuth 2.0流程）
        
        Args:
            code: 授权码（authCode）
            
        Returns:
            Dict[str, Any]: 用户信息
        """
        try:
            logger.info(f"开始通过授权码获取用户信息，code: {code[:10]}...")
            
            # 第一步：通过authCode获取用户token
            user_token = await self._get_user_token_by_code(code)
            if not user_token:
                logger.error("获取用户token失败")
                return None
            
            # 第二步：通过用户token获取用户信息
            user_info = await self._get_user_info_by_token(user_token)
            if user_info:
                logger.info(f"成功获取用户信息: {user_info.get('nick', 'N/A')}")
                return user_info
            else:
                logger.error("获取用户信息失败")
                return None
            
        except Exception as e:
            logger.error(f"通过授权码获取用户信息异常: {str(e)}", exc_info=True)
            return None
    
    async def _get_user_token_by_code(self, code: str) -> Optional[str]:
        """
        通过authCode获取用户token
        
        根据最新钉钉官方文档，使用正确的API端点：
        POST https://api.dingtalk.com/v1.0/oauth2/userAccessToken
        
        Args:
            code: 授权码
            
        Returns:
            str: 用户token
        """
        try:
            # 使用钉钉官方正确的API端点
            url = "https://api.dingtalk.com/v1.0/oauth2/userAccessToken"
            headers = {
                "Content-Type": "application/json"
            }
            data = {
                "clientId": self.app_key,
                "clientSecret": self.app_secret,
                "code": code,
                "grantType": "authorization_code"
            }
            
            logger.info(f"调用钉钉官方用户token API: {url}")
            logger.info(f"请求参数: clientId={self.app_key}, code={code[:10]}...")
            
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.post(url, headers=headers, json=data)
                response_text = response.text
                logger.info(f"用户token API响应状态: {response.status_code}")
                logger.info(f"用户token API响应内容: {response_text}")
                
                if response.status_code == 200:
                    result = response.json() if "application/json" in response.headers.get("content-type", "") else json.loads(response_text)
                    access_token = result.get("accessToken")
                    if access_token:
                        logger.info(f"成功获取用户token: {access_token[:10]}...")
                        return access_token
                    else:
                        logger.error(f"响应中没有accessToken: {result}")
                        return None
                else:
                    logger.error(f"用户token API请求失败: status={response.status_code}, response={response_text}")
                    return None
        except Exception as e:
            logger.error(f"获取用户token异常: {str(e)}", exc_info=True)
            return None
    
    async def _get_user_info_by_token(self, user_token: str) -> Optional[Dict[str, Any]]:
        """
        通过用户token获取用户信息
        
        Args:
            user_token: 用户token
            
        Returns:
            Dict[str, Any]: 用户信息
        """
        try:
            # 根据钉钉官方文档，使用正确的API端点和参数格式
            url = "https://api.dingtalk.com/v1.0/contact/users/me"
            headers = {
                "x-acs-dingtalk-access-token": user_token,
                "Content-Type": "application/json"
            }
            
            logger.info(f"调用获取用户信息API: {url}")
            logger.info(f"使用用户token: {user_token[:10]}...")
            logger.info(f"请求头: {headers}")
            
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.get(url, headers=headers)
                response_text = response.text
                logger.info(f"获取用户信息响应状态: {response.status_code}")
                logger.info(f"获取用户信息响应内容: {response_text}")
                
                if response.status_code == 200:
                    try:
                        result = response.json() if "application/json" in response.headers.get("content-type", "") else json.loads(response_text)
                        
                        # 验证响应是否包含用户信息
                        if "unionId" in result or "openId" in result:
                            # 构造返回的用户信息
                            user_info = {
                                "openid": result.get("openId"),
                                "unionid": result.get("unionId"), 
                                "nick": result.get("nick"),
                                "avatarUrl": result.get("avatarUrl"),
                                "mobile": result.get("mobile"),
                                "email": result.get("email"),
                                "name": result.get("name"),
                                "stateCode": result.get("stateCode")
                            }
                            
                            logger.info(f"成功解析用户信息: {user_info}")
                            return user_info
                        else:
                            logger.error(f"响应中缺少用户标识信息: {result}")
                            return None
                    except json.JSONDecodeError as e:
                        logger.error(f"解析响应JSON失败: {e}, 响应内容: {response_text}")
                        return None
                else:
                    logger.error(f"获取用户信息请求失败: status={response.status_code}, response={response_text}")
                    
                    # 如果是权限问题，尝试使用基本的用户信息结构
                    if response.status_code == 403:
                        logger.warning("权限被拒绝，可能需要在钉钉开发者后台添加'通讯录个人信息读权限'")
                        try:
                            # 尝试使用基本用户信息结构
                            logger.info("尝试使用基本用户信息结构")
                            return {
                                "openid": f"unknown_{user_token[:8]}",
                                "unionid": f"unknown_{user_token[:8]}",
                                "nick": "钉钉用户",
                                "avatarUrl": None,
                                "mobile": None,
                                "email": None,
                                "name": "钉钉用户",
                                "stateCode": None
                            }
                        except Exception:
                            pass
                    
                    return None
        except Exception as e:
            logger.error(f"获取用户信息异常: {str(e)}", exc_info=True)
            return None
    
    async def get_user_detail(self, userid: str) -> Optional[Dict[str, Any]]:
        """
        获取用户详细信息（智能选择API）
        
        根据钉钉官方文档，区分两种情况：
        1. 如果userid='me'，使用企业API但警告限制
        2. 如果是具体userid，使用企业用户查询API
        
        Args:
            userid: 钉钉用户ID或'me'
            
        Returns:
            Dict[str, Any]: 用户详细信息
        """
        try:
            if userid == 'me':
                # 企业API不支持'me'参数，这是架构限制
                logger.warning("企业API不支持'me'参数，需要个人访问令牌调用 GET /v1.0/contact/users/me")
                logger.warning("当前使用企业访问令牌，无法获取'me'用户信息")
                return None
            else:
                # 使用企业API获取指定用户信息
                return await self.get_user_info_by_userid(userid)
                
        except Exception as e:
            logger.error(f"获取用户详细信息异常: {str(e)}")
            return None

    async def get_user_info_by_userid(self, userid: str) -> Optional[Dict[str, Any]]:
        """
        通过userid获取用户信息
        使用企业用户查询API: https://oapi.dingtalk.com/topapi/v2/user/get
        使用企业访问令牌(access token)
        
        Args:
            userid: 钉钉用户ID
            
        Returns:
            Dict[str, Any]: 用户详细信息
        """
        try:
            access_token = await self.get_access_token()
            if not access_token:
                logger.error("获取企业access_token失败")
                return None
            
            url = f"{self.base_url}/topapi/v2/user/get"
            params = {
                "access_token": access_token
            }
            data = {
                "userid": userid,
                "language": "zh_CN"
            }
            
            logger.info(f"调用企业用户查询API: {url}, userid={userid}")
            
            async with httpx.AsyncClient() as client:
                response = await client.post(url, params=params, json=data)
                response_text = response.text
                logger.info(f"企业用户查询API响应状态: {response.status_code}")
                logger.debug(f"企业用户查询API响应内容: {response_text}")
                
                if response.status_code == 200:
                    result = response.json()
                    if result.get("errcode") == 0:
                        user_detail = result.get("result", {})
                        logger.info(f"成功获取用户详情: userid={userid}, unionid={user_detail.get('unionid')}")
                        return user_detail
                    else:
                        error_code = result.get("errcode")
                        error_msg = result.get("errmsg", "未知错误")
                        logger.error(f"获取用户详细信息失败: errcode={error_code}, errmsg={error_msg}")
                        return None
                else:
                    logger.error(f"获取用户详细信息请求失败: {response.status_code}")
                    return None
                    
        except Exception as e:
            logger.error(f"获取用户详细信息异常: {str(e)}")
            return None
    



    
    async def get_user_by_userid_list(self, userid_list: List[str]) -> Dict[str, Any]:
        """
        批量获取用户详细信息
        
        Args:
            userid_list: 用户ID列表
            
        Returns:
            Dict[str, Any]: 用户信息字典，key为userid
        """
        user_dict = {}
        
        for userid in userid_list:
            user_info = await self.get_user_detail(userid)
            if user_info:
                user_dict[userid] = user_info
            else:
                # 为找不到的用户创建默认信息
                user_dict[userid] = {
                    "userid": userid,
                    "name": f"未知用户{userid[:8]}",
                    "mobile": "",
                    "email": "",
                    "active": False,
                    "state_code": "86"
                }
        
        return user_dict
    
    async def send_work_notification(self, user_id: str = None, userid_list: List[str] = None, message: str = None, msg: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        发送工作通知（兼容单用户和多用户）
        
        Args:
            user_id: 单个用户ID
            userid_list: 用户ID列表
            message: 简单文本消息
            msg: 复杂消息内容
            
        Returns:
            Dict[str, Any]: 发送结果
        """
        try:
            access_token = await self.get_access_token()
            if not access_token:
                return {"success": False, "error": "获取访问令牌失败"}
            
            # 处理用户列表
            if user_id:
                target_users = [user_id]
            elif userid_list:
                target_users = userid_list
            else:
                return {"success": False, "error": "未指定目标用户"}
            
            # 处理消息内容
            if message and not msg:
                msg = {
                    "msgtype": "text",
                    "text": {
                        "content": message
                    }
                }
            elif not msg:
                return {"success": False, "error": "未指定消息内容"}
            
            url = f"{self.base_url}/topapi/message/corpconversation/asyncsend_v2"
            params = {
                "access_token": access_token
            }
            # 工作通知需要使用agent_id
            # 对于企业内部应用，agent_id通常与app_key相同
            # 但如果是第三方应用，需要单独获取agent_id
            agent_id = getattr(self, 'agent_id', None) or self.app_key
            
            logger.info(f"🔔 准备发送工作通知，agent_id: {agent_id}, 目标用户: {target_users}")
            
            data = {
                "agent_id": agent_id,
                "userid_list": ",".join(target_users),
                "msg": msg
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.post(url, params=params, json=data)
                if response.status_code == 200:
                    result = response.json()
                    if result.get("errcode") == 0:
                        logger.info(f"✅ 工作通知发送成功: {result}")
                        return {
                            "success": True,
                            "task_id": result.get("task_id"),
                            "message": "发送成功"
                        }
                    else:
                        error_msg = result.get("errmsg", "发送失败")
                        error_code = result.get("errcode")
                        if error_code == 60020:
                            logger.error(f"❌ 工作通知发送失败: agent_id无效 ({agent_id})")
                            return {"success": False, "error": f"agent_id无效，请检查应用配置: {error_msg}"}
                        else:
                            logger.error(f"❌ 工作通知发送失败: {result}")
                        return {
                            "success": False,
                                "error": f"发送失败({error_code}): {error_msg}"
                        }
                else:
                    logger.error(f"发送工作通知请求失败: {response.status_code}")
                    return {"success": False, "error": f"请求失败: {response.status_code}"}
        except Exception as e:
            logger.error(f"发送工作通知异常: {str(e)}")
            return {"success": False, "error": str(e)}
    
    async def send_group_message(self, chat_id: str, message: str = None, msg: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        发送群组消息
        
        Args:
            chat_id: 群组ID
            message: 简单文本消息
            msg: 复杂消息内容
            
        Returns:
            Dict[str, Any]: 发送结果
        """
        try:
            access_token = await self.get_access_token()
            if not access_token:
                return {"success": False, "error": "获取访问令牌失败"}
            
            # 处理消息内容
            if message and not msg:
                msg = {
                    "msgtype": "text",
                    "text": {
                        "content": message
                    }
                }
            elif not msg:
                return {"success": False, "error": "未指定消息内容"}
            
            url = f"{self.base_url}/chat/send"
            params = {
                "access_token": access_token
            }
            data = {
                "chatid": chat_id,
                "msg": msg
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.post(url, params=params, json=data)
                if response.status_code == 200:
                    result = response.json()
                    if result.get("errcode") == 0:
                        logger.info(f"发送群组消息成功: {result}")
                        return {
                            "success": True,
                            "message_id": result.get("messageId"),
                            "message": "发送成功"
                        }
                    else:
                        logger.error(f"发送群组消息失败: {result}")
                        return {
                            "success": False,
                            "error": result.get("errmsg", "发送失败")
                        }
                else:
                    logger.error(f"发送群组消息请求失败: {response.status_code}")
                    return {"success": False, "error": f"请求失败: {response.status_code}"}
        except Exception as e:
            logger.error(f"发送群组消息异常: {str(e)}")
            return {"success": False, "error": str(e)}
    
    async def send_interactive_card(self, target_type: str, target_id: str, card_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        发送交互式卡片消息
        
        Args:
            target_type: 目标类型 (user/group)
            target_id: 目标ID
            card_data: 卡片数据
            
        Returns:
            Dict[str, Any]: 发送结果
        """
        try:
            access_token = await self.get_access_token()
            if not access_token:
                return {"success": False, "error": "获取访问令牌失败"}
            
            if target_type == "user":
                url = f"{self.base_url}/topapi/message/corpconversation/asyncsend_v2"
                data = {
                    "agent_id": self.app_key,
                    "userid_list": target_id,
                    "msg": {
                        "msgtype": "action_card",
                        "action_card": card_data
                    }
                }
            elif target_type == "group":
                url = f"{self.base_url}/chat/send"
                data = {
                    "chatid": target_id,
                    "msg": {
                        "msgtype": "action_card",
                        "action_card": card_data
                    }
                }
            else:
                return {"success": False, "error": "不支持的目标类型"}
            
            params = {"access_token": access_token}
            
            async with httpx.AsyncClient() as client:
                response = await client.post(url, params=params, json=data)
                if response.status_code == 200:
                    result = response.json()
                    if result.get("errcode") == 0:
                        logger.info(f"发送交互式卡片成功: {result}")
                        return {"success": True, "result": result}
                    else:
                        logger.error(f"发送交互式卡片失败: {result}")
                        return {"success": False, "error": result.get("errmsg", "发送失败")}
                else:
                    logger.error(f"发送交互式卡片请求失败: {response.status_code}")
                    return {"success": False, "error": f"请求失败: {response.status_code}"}
        except Exception as e:
            logger.error(f"发送交互式卡片异常: {str(e)}")
            return {"success": False, "error": str(e)}
    
    async def get_chat_info(self, chat_id: str) -> Optional[Dict[str, Any]]:
        """
        获取群组信息
        
        Args:
            chat_id: 群组ID
            
        Returns:
            Dict[str, Any]: 群组信息
        """
        try:
            access_token = await self.get_access_token()
            if not access_token:
                return None
            
            url = f"{self.base_url}/chat/get"
            params = {
                "access_token": access_token,
                "chatid": chat_id
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.get(url, params=params)
                if response.status_code == 200:
                    result = response.json()
                    if result.get("errcode") == 0:
                        return result
                    else:
                        logger.error(f"获取群组信息失败: {result}")
                        return None
                else:
                    logger.error(f"获取群组信息请求失败: {response.status_code}")
                    return None
        except Exception as e:
            logger.error(f"获取群组信息异常: {str(e)}")
            return None
    
    async def create_ai_card_message(self, title: str, content: str, actions: List[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        创建AI助手卡片消息
        
        Args:
            title: 卡片标题
            content: 卡片内容
            actions: 操作按钮列表
            
        Returns:
            Dict[str, Any]: 卡片消息数据
        """
        card_data = {
            "title": title,
            "text": content,
            "hideAvatar": "0",
            "btnOrientation": "0"
        }
        
        if actions:
            if len(actions) == 1:
                # 单个按钮
                action = actions[0]
                card_data["singleTitle"] = action.get("title", "查看详情")
                card_data["singleURL"] = action.get("url", "#")
            else:
                # 多个按钮
                card_data["btns"] = [
                    {
                        "title": action.get("title", "按钮"),
                        "actionURL": action.get("url", "#")
                    }
                    for action in actions
                ]
        
        return card_data
    
    async def send_ai_response_card(self, target_type: str, target_id: str, ai_response: str, 
                                  thread_id: str = None, assistant_name: str = "AI助手") -> Dict[str, Any]:
        """
        发送AI回复卡片
        
        Args:
            target_type: 目标类型 (user/group)
            target_id: 目标ID
            ai_response: AI回复内容
            thread_id: 对话线程ID
            assistant_name: 助手名称
            
        Returns:
            Dict[str, Any]: 发送结果
        """
        # 创建操作按钮
        actions = [
            {
                "title": "继续对话",
                "url": f"https://saas.houshanai.com/dingtalk/chat?thread_id={thread_id}" if thread_id else "#"
            },
            {
                "title": "查看历史",
                "url": f"https://saas.houshanai.com/dingtalk/history?thread_id={thread_id}" if thread_id else "#"
            }
        ]
        
        # 创建卡片数据
        card_data = await self.create_ai_card_message(
            title=f"🤖 {assistant_name}",
            content=ai_response,
            actions=actions
        )
        
        # 发送卡片
        return await self.send_interactive_card(target_type, target_id, card_data)
    
    async def get_department_list(self, dept_id: int = 1) -> Optional[List[Dict[str, Any]]]:
        """
        获取部门列表
        
        Args:
            dept_id: 部门ID，默认为根部门
            
        Returns:
            List[Dict[str, Any]]: 部门列表
        """
        try:
            access_token = await self.get_access_token()
            if not access_token:
                return None
            
            url = f"{self.base_url}/topapi/v2/department/listsub"
            params = {
                "access_token": access_token
            }
            data = {
                "dept_id": dept_id
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.post(url, params=params, json=data)
                if response.status_code == 200:
                    result = response.json()
                    if result.get("errcode") == 0:
                        return result.get("result", [])
                    else:
                        logger.error(f"获取部门列表失败: {result}")
                        return None
                else:
                    logger.error(f"获取部门列表请求失败: {response.status_code}")
                    return None
        except Exception as e:
            logger.error(f"获取部门列表异常: {str(e)}")
            return None
    
    async def get_department_users(self, dept_id: int) -> Optional[List[Dict[str, Any]]]:
        """
        获取部门用户列表（使用v2版本API）
        
        Args:
            dept_id: 部门ID
            
        Returns:
            List[Dict[str, Any]]: 用户列表
        """
        import time
        start_time = time.time()
        
        try:
            logger.info(f"🔍 开始获取部门用户，部门ID: {dept_id}")
            
            access_token = await self.get_access_token()
            if not access_token:
                logger.error("获取访问令牌失败")
                return None
            
            token_time = time.time() - start_time
            logger.debug(f"⏱️ 获取访问令牌耗时: {token_time:.2f}秒")
            
            # 使用v2版本API，避免旧版API的"远程服务不存在"错误
            url = f"{self.base_url}/topapi/v2/user/list"
            params = {
                "access_token": access_token
            }
            data = {
                "dept_id": dept_id,
                "cursor": 0,
                "size": 100
            }
            
            logger.debug(f"📡 调用部门用户API: {url}, 数据: {data}")
            
            request_start = time.time()
            async with httpx.AsyncClient(timeout=15.0) as client:  # 设置15秒超时
                response = await client.post(url, params=params, json=data)
                request_time = time.time() - request_start
                logger.debug(f"⏱️ API请求耗时: {request_time:.2f}秒")
                
                if response.status_code == 200:
                    result = response.json()
                    if result.get("errcode") == 0:
                        # v2 API返回结构: result.list
                        user_list = result.get("result", {}).get("list", [])
                        total_time = time.time() - start_time
                        logger.info(f"✅ 成功获取部门{dept_id}用户: {len(user_list)}个用户，总耗时: {total_time:.2f}秒")
                        return user_list
                    else:
                        error_code = result.get("errcode")
                        error_msg = result.get("errmsg", "未知错误")
                        logger.error(f"❌ 获取部门用户失败: errcode={error_code}, errmsg={error_msg}")
                        
                        # 特殊错误处理
                        if error_code == 60003:
                            logger.error(f"部门不存在: dept_id={dept_id}")
                        elif error_code == 15:
                            logger.error("远程服务错误，请检查网络或API状态")
                        elif error_code == 88:
                            logger.error("access_token无效，需要重新获取")
                        
                        return []
                else:
                    logger.error(f"❌ 获取部门用户列表请求失败: status={response.status_code}, response={response.text[:200]}")
                    return None
        except asyncio.TimeoutError:
            total_time = time.time() - start_time
            logger.error(f"⏰ 获取部门{dept_id}用户超时，耗时: {total_time:.2f}秒")
            return None
        except Exception as e:
            total_time = time.time() - start_time
            logger.error(f"❌ 获取部门{dept_id}用户异常，耗时: {total_time:.2f}秒: {str(e)}")
            return None

    async def get_department_users_detail(self, dept_id: int, cursor: int = 0, size: int = 100) -> Optional[Dict[str, Any]]:
        """
        获取部门用户详细信息（支持分页）
        
        Args:
            dept_id: 部门ID
            cursor: 分页游标
            size: 页面大小
            
        Returns:
            Dict[str, Any]: 用户详细信息和分页信息
        """
        try:
            access_token = await self.get_access_token()
            if not access_token:
                return None
            
            url = f"{self.base_url}/topapi/v2/user/list"
            params = {
                "access_token": access_token
            }
            data = {
                "dept_id": dept_id,
                "cursor": cursor,
                "size": size
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.post(url, params=params, json=data)
                if response.status_code == 200:
                    result = response.json()
                    if result.get("errcode") == 0:
                        return result.get("result", {})
                    else:
                        logger.error(f"获取部门用户详细信息失败: {result}")
                        return None
                else:
                    logger.error(f"获取部门用户详细信息请求失败: {response.status_code}")
                    return None
        except Exception as e:
            logger.error(f"获取部门用户详细信息异常: {str(e)}")
            return None

    async def get_all_departments(self) -> Optional[List[Dict[str, Any]]]:
        """
        获取组织内所有部门
        
        Returns:
            List[Dict[str, Any]: 所有部门列表
        """
        try:
            access_token = await self.get_access_token()
            if not access_token:
                logger.error("获取企业access_token失败")
                return None
            
            url = f"{self.base_url}/topapi/v2/department/listsubid"
            params = {
                "access_token": access_token
            }
            data = {
                "dept_id": 1  # 从根部门开始
            }
            
            logger.info(f"调用获取所有部门ID API: {url}")
            
            all_departments = []
            
            # 递归获取所有部门
            async def get_departments_recursive(dept_id: int):
                try:
                    # 获取子部门ID列表
                    async with httpx.AsyncClient() as client:
                        response = await client.post(url, params=params, json={"dept_id": dept_id})
                        if response.status_code == 200:
                            result = response.json()
                            if result.get("errcode") == 0:
                                sub_dept_ids = result.get("result", {}).get("dept_id_list", [])
                                
                                # 获取每个部门的详细信息
                                for sub_dept_id in sub_dept_ids:
                                    dept_detail = await self.get_department_detail(sub_dept_id)
                                    if dept_detail:
                                        all_departments.append(dept_detail)
                                    
                                    # 递归获取子部门
                                    await get_departments_recursive(sub_dept_id)
                            else:
                                logger.error(f"获取部门{dept_id}的子部门失败: {result}")
                        else:
                            logger.error(f"获取部门{dept_id}的子部门请求失败: {response.status_code}")
                except Exception as e:
                    logger.error(f"递归获取部门{dept_id}异常: {str(e)}")
            
            # 添加根部门
            root_dept = await self.get_department_detail(1)
            if root_dept:
                all_departments.append(root_dept)
            
            # 获取所有子部门
            await get_departments_recursive(1)
            
            logger.info(f"成功获取所有部门，共{len(all_departments)}个部门")
            return all_departments
            
        except Exception as e:
            logger.error(f"获取所有部门异常: {str(e)}")
            return None

    async def get_department_detail(self, dept_id: int) -> Optional[Dict[str, Any]]:
        """
        获取部门详细信息
        
        Args:
            dept_id: 部门ID
            
        Returns:
            Dict[str, Any]: 部门详细信息
        """
        try:
            access_token = await self.get_access_token()
            if not access_token:
                return None
            
            url = f"{self.base_url}/topapi/v2/department/get"
            params = {
                "access_token": access_token
            }
            data = {
                "dept_id": dept_id
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.post(url, params=params, json=data)
                if response.status_code == 200:
                    result = response.json()
                    if result.get("errcode") == 0:
                        return result.get("result", {})
                    else:
                        logger.error(f"获取部门{dept_id}详细信息失败: {result}")
                        return None
                else:
                    logger.error(f"获取部门{dept_id}详细信息请求失败: {response.status_code}")
                    return None
        except Exception as e:
            logger.error(f"获取部门{dept_id}详细信息异常: {str(e)}")
            return None

    async def get_user_by_mobile(self, mobile: str) -> Optional[Dict[str, Any]]:
        """
        根据手机号获取用户信息
        
        Args:
            mobile: 手机号
            
        Returns:
            Dict[str, Any]: 用户信息
        """
        try:
            access_token = await self.get_access_token()
            if not access_token:
                return None
            
            url = f"{self.base_url}/topapi/v2/user/getbymobile"
            params = {
                "access_token": access_token
            }
            data = {
                "mobile": mobile
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.post(url, params=params, json=data)
                if response.status_code == 200:
                    result = response.json()
                    if result.get("errcode") == 0:
                        return result.get("result", {})
                    else:
                        logger.error(f"根据手机号获取用户信息失败: {result}")
                        return None
                else:
                    logger.error(f"根据手机号获取用户信息请求失败: {response.status_code}")
                    return None
        except Exception as e:
            logger.error(f"根据手机号获取用户信息异常: {str(e)}")
            return None

    async def get_all_users_in_org(self) -> Optional[List[Dict[str, Any]]]:
        """
        获取组织内所有用户
        
        Returns:
            List[Dict[str, Any]]: 所有用户列表
        """
        try:
            all_users = []
            departments = await self.get_all_departments()
            
            if not departments:
                return None
            
            # 为了避免重复，使用集合记录已处理的用户ID
            processed_users = set()
            
            for dept in departments:
                dept_id = dept.get("dept_id", dept.get("id"))
                if dept_id:
                    cursor = 0
                    has_more = True
                    
                    while has_more:
                        result = await self.get_department_users_detail(dept_id, cursor)
                        if result:
                            users = result.get("list", [])
                            for user in users:
                                user_id = user.get("userid")
                                if user_id and user_id not in processed_users:
                                    # 添加部门信息到用户数据
                                    user["department_info"] = {
                                        "dept_id": dept_id,
                                        "dept_name": dept.get("name", ""),
                                        "dept_parent_id": dept.get("parent_id", dept.get("parentid"))
                                    }
                                    all_users.append(user)
                                    processed_users.add(user_id)
                            
                            # 检查是否还有更多数据
                            has_more = result.get("has_more", False)
                            cursor = result.get("next_cursor", 0)
                        else:
                            has_more = False
            
            return all_users
            
        except Exception as e:
            logger.error(f"获取组织内所有用户异常: {str(e)}")
            return None

    # ==================== 待办任务相关API (v1.0最新版) ====================
    
    async def create_todo_task(self, task_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        创建钉钉待办任务 (使用2025年5月最新API)
        
        根据官方文档：https://open.dingtalk.com/document/orgapp/add-dingtalk-to-do-task
        使用 POST /v1.0/todo/users/{unionId}/tasks?operatorId={unionId}
        
        Args:
            task_data: 任务数据
            
        Returns:
            Dict[str, Any]: 创建结果
        """
        try:
            access_token = await self.get_access_token()
            if not access_token:
                return None
            
            user_id = task_data.get("userid")
            if not user_id:
                logger.error("创建待办任务失败：缺少用户ID")
                return None
            
            # 使用2025年5月最新的待办任务创建API
            # 添加operatorId参数，值为当前操作者用户的unionId
            url = f"https://api.dingtalk.com/v1.0/todo/users/{user_id}/tasks"
            params = {
                "operatorId": user_id  # operatorId为当前操作者用户的unionId
            }
            headers = {
                "x-acs-dingtalk-access-token": access_token,
                "Content-Type": "application/json"
            }
            
            # 构建时间戳
            due_time = None
            if task_data.get("due_date"):
                # 转换为毫秒时间戳格式（钉钉API期望的格式）
                if isinstance(task_data["due_date"], str):
                    # 如果是ISO格式字符串，解析为datetime然后转换为时间戳
                    try:
                        # 尝试使用datetime.fromisoformat（Python 3.7+）
                        if task_data["due_date"].endswith('Z'):
                            # 处理UTC时区标记
                            dt_str = task_data["due_date"][:-1] + '+00:00'
                        else:
                            dt_str = task_data["due_date"]
                        
                        # 移除可能的毫秒部分以适配不同格式
                        if '.' in dt_str and dt_str.count(':') == 2:
                            dt_str = dt_str.split('.')[0] + dt_str.split('.')[-1][-6:]
                        
                        dt = dt_module.fromisoformat(dt_str.replace('Z', '+00:00'))
                        due_time = int(dt.timestamp() * 1000)  # 转换为毫秒时间戳
                    except Exception as e:
                        logger.warning(f"ISO时间解析失败: {e}, 尝试其他方法")
                        # 如果解析失败，尝试直接转换为整数
                        try:
                            due_time = int(task_data["due_date"])
                        except ValueError:
                            logger.warning(f"无法解析due_date: {task_data['due_date']}")
                            due_time = None
                elif hasattr(task_data["due_date"], 'timestamp'):
                    # 如果是datetime对象
                    due_time = int(task_data["due_date"].timestamp() * 1000)
                else:
                    # 其他类型，尝试直接转换
                    try:
                        due_time = int(task_data["due_date"])
                    except (ValueError, TypeError):
                        logger.warning(f"无法转换due_date为时间戳: {task_data['due_date']}")
                        due_time = None
            
            # 2025年5月最新API数据格式 (修复版 - 正确的detailUrl格式)
            # 根据官方文档：从2024年2月1日起，detailUrl字段为必填项
            # detailUrl应该是包含pcUrl和appUrl的对象
            base_url = task_data.get("url") or task_data.get("detail_url") or task_data.get("sourcePath")
            if not base_url:
                # 如果没有提供detailUrl，使用默认的系统链接，包含正确的projectId
                system_task_id = task_data.get('system_task_id', 'default')
                project_id = task_data.get('project_id', 'default')  # 获取项目ID
                base_url = f"https://saas.houshanai.com/project/{project_id}/task/{system_task_id}"
            
            # 构建正确的detailUrl对象格式
            detail_url = {
                "pcUrl": base_url,      # PC端详情页url跳转地址
                "appUrl": base_url      # APP端详情页url跳转地址
            }
            
            data = {
                "sourceId": task_data.get("system_task_id", ""),  # 移到前面，作为重要标识
                "subject": task_data.get("title", ""),
                "creatorId": user_id,  # 添加创建者ID
                "description": task_data.get("description", ""),
                "detailUrl": detail_url,  # 必填项：工作待办详情页面链接对象
                "dueTime": due_time,
                "executorIds": [user_id],
                "participantIds": [user_id],
                "isOnlyShowExecutor": task_data.get("isOnlyShowExecutor", False),
                "priority": self._convert_priority(task_data.get("priority", "normal")),
                "notifyConfigs": {
                    "dingNotify": "1"  # 启用钉钉通知
                }
            }
            
            # 添加可选字段
            if task_data.get("note"):
                data["note"] = task_data.get("note")
            if task_data.get("customFields"):
                data["customFields"] = task_data.get("customFields")
            
            # 移除空值字段
            data = {k: v for k, v in data.items() if v is not None and v != ""}
            
            logger.info(f"使用2025年5月最新待办任务API创建任务: {url}")
            logger.info(f"operatorId参数: {params['operatorId']}")
            logger.debug(f"任务数据: {data}")
            
            async with httpx.AsyncClient() as client:
                response = await client.post(url, headers=headers, params=params, json=data)
                logger.info(f"待办任务创建响应状态: {response.status_code}")
                logger.debug(f"待办任务创建响应内容: {response.text}")
                
                if response.status_code == 200:
                    result = response.json()
                    logger.info(f"成功创建待办任务: {result}")
                    return {
                        "task_id": result.get("id"),
                        "record_id": result.get("id"),  # 兼容旧版字段名
                        "subject": result.get("subject")
                    }
                else:
                    logger.error(f"创建待办任务失败: {response.status_code}, {response.text}")
                    return None
        except Exception as e:
            logger.error(f"创建待办任务异常: {str(e)}")
            return None
    


    async def update_todo_task(self, user_id: str, task_id: str, task_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        更新钉钉待办任务 (使用最新v1.0 API)
        
        Args:
            user_id: 用户ID
            task_id: 任务ID
            task_data: 任务数据
            
        Returns:
            Dict[str, Any]: 更新结果
        """
        try:
            access_token = await self.get_access_token()
            if not access_token:
                return None
            
            url = f"https://api.dingtalk.com/v1.0/todo/users/{user_id}/tasks/{task_id}"
            headers = {
                "x-acs-dingtalk-access-token": access_token,
                "Content-Type": "application/json"
            }
            
            # 构建更新数据
            update_data = {}
            if "title" in task_data:
                update_data["subject"] = task_data["title"]
            if "description" in task_data:
                update_data["description"] = task_data["description"]
            if "due_date" in task_data:
                if isinstance(task_data["due_date"], str):
                    update_data["dueTime"] = task_data["due_date"]
                elif hasattr(task_data["due_date"], 'isoformat'):
                    update_data["dueTime"] = task_data["due_date"].isoformat()
            if "priority" in task_data:
                update_data["priority"] = self._convert_priority(task_data["priority"])
            if "url" in task_data:
                update_data["sourcePath"] = task_data["url"]
            
            async with httpx.AsyncClient() as client:
                response = await client.put(url, headers=headers, json=update_data)
                if response.status_code == 200:
                    logger.info(f"成功更新钉钉待办任务: {task_id}")
                    return {"success": True}
                else:
                    logger.error(f"更新钉钉待办任务失败: {response.status_code}, {response.text}")
                    return None
        except Exception as e:
            logger.error(f"更新钉钉待办任务异常: {str(e)}")
            return None

    async def get_todo_tasks(self, user_id: str, status: str = "NEW", limit: int = 20, next_token: str = None) -> Optional[Dict[str, Any]]:
        """
        获取用户的钉钉待办任务列表 (使用组织待办查询API)
        
        根据官方文档，查询接口没有变化：
        使用 POST /v1.0/todo/users/{unionId}/org/tasks/query
        
        Args:
            user_id: 用户unionId (钉钉用户的唯一标识)
            status: 任务状态 (NEW-新建, RUNNING-进行中, DONE-已完成, CANCELLED-已取消, ALL-所有状态)
            limit: 限制数量
            next_token: 分页token
            
        Returns:
            Dict[str, Any]: 任务列表
        """
        try:
            access_token = await self.get_access_token()
            if not access_token:
                logger.error("获取钉钉访问令牌失败")
                return None
            
            # 使用组织待办查询API
            return await self._query_org_todo_tasks(user_id, status, limit, next_token, access_token)
            
        except Exception as e:
            logger.error(f"获取钉钉待办任务异常: {str(e)}", exc_info=True)
            return {
                "success": False,
                "error": str(e),
                "tasks": []
            }
    
    async def _query_org_todo_tasks(self, user_id: str, status: str, limit: int, next_token: str, access_token: str) -> Optional[Dict[str, Any]]:
        """查询组织待办任务"""
        try:
            # 使用官方文档推荐的组织待办查询接口
            url = f"https://api.dingtalk.com/v1.0/todo/users/{user_id}/org/tasks/query"
            headers = {
                "x-acs-dingtalk-access-token": access_token,
                "Content-Type": "application/json"
            }
            
            all_tasks = []
            
            # 如果status是ALL，需要分别查询未完成和已完成的任务
            if status == "ALL":
                # 查询未完成的任务
                for is_done in [False, True]:
                    query_body = {
                        "isDone": is_done,
                        "maxResults": limit // 2 if status == "ALL" else limit  # 分配查询数量
                    }
                    if next_token:
                        query_body["nextToken"] = next_token
                    
                    logger.info(f"查询用户 {user_id} 的组织待办任务，isDone: {is_done}, 限制: {query_body['maxResults']}")
                    
                    async with httpx.AsyncClient() as client:
                        response = await client.post(url, headers=headers, json=query_body)
                        logger.info(f"组织待办任务查询响应状态: {response.status_code}")
                        
                        if response.status_code == 200:
                            result = response.json()
                            # 根据官方文档，返回的是todoCards而不是tasks
                            todo_cards = result.get("todoCards", result.get("tasks", result.get("result", [])))
                            all_tasks.extend(todo_cards)
                            logger.info(f"查询到 {len(todo_cards)} 个组织待办任务 (isDone: {is_done})")
                        else:
                            logger.warning(f"查询组织待办任务失败 (isDone: {is_done}): {response.status_code}, {response.text}")
                
                return {
                    "success": True,
                    "tasks": all_tasks,
                    "total": len(all_tasks)
                }
            else:
                # 单一状态查询 - 根据status转换为isDone
                if status == "NEW":
                    is_done = False
                elif status == "DONE":
                    is_done = True
                elif status == "CANCELLED":
                    is_done = True  # 取消的任务也算已完成
                else:
                    is_done = False  # 默认为未完成
                
                query_body = {
                    "isDone": is_done,
                    "maxResults": limit
                }
                if next_token:
                    query_body["nextToken"] = next_token
                
                logger.info(f"查询用户 {user_id} 的组织待办任务，状态: {status}, 限制: {limit}")
                
                async with httpx.AsyncClient() as client:
                    response = await client.post(url, headers=headers, json=query_body)
                    response_text = response.text
                    logger.info(f"组织待办任务查询响应状态: {response.status_code}")
                    logger.debug(f"组织待办任务查询响应内容: {response_text}")
                    
                    if response.status_code == 200:
                        result = response.json()
                        # 根据官方文档，返回的是todoCards而不是tasks
                        todo_cards = result.get("todoCards", result.get("tasks", result.get("result", [])))
                        
                        return {
                            "success": True,
                            "tasks": todo_cards,  # 保持统一的返回字段名
                            "todoCards": todo_cards,  # 同时提供原始字段名
                            "nextToken": result.get("nextToken"),
                            "hasMore": result.get("hasMore", False),
                            "total": len(todo_cards)
                        }
                    elif response.status_code == 403:
                        error_msg = "权限不足，请确保应用具有组织待办任务查询权限"
                        logger.error(f"组织待办任务查询失败: {error_msg}")
                        return {
                            "success": False,
                            "error": error_msg,
                            "tasks": []
                        }
                    elif response.status_code == 404:
                        error_msg = f"组织待办API不存在或用户 {user_id} 无权限访问"
                        logger.warning(f"组织待办任务查询失败: {error_msg}")
                        return {
                            "success": False,
                            "error": error_msg,
                            "tasks": []
                        }
                    else:
                        error_msg = f"组织待办任务查询失败: HTTP {response.status_code}, {response_text}"
                        logger.error(error_msg)
                        return {
                            "success": False,
                            "error": error_msg,
                            "tasks": []
                        }
        except Exception as e:
            logger.error(f"查询组织待办任务异常: {str(e)}", exc_info=True)
            return {
                "success": False,
                "error": str(e),
                "tasks": []
            }
    


    async def complete_todo_task(self, user_id: str, task_id: str) -> Optional[Dict[str, Any]]:
        """
        完成钉钉待办任务 (使用最新v1.0 API)
        
        Args:
            user_id: 用户ID
            task_id: 任务ID
            
        Returns:
            Dict[str, Any]: 操作结果
        """
        try:
            access_token = await self.get_access_token()
            if not access_token:
                return None
            
            url = f"https://api.dingtalk.com/v1.0/todo/users/{user_id}/tasks/{task_id}/finish"
            headers = {
                "x-acs-dingtalk-access-token": access_token,
                "Content-Type": "application/json"
            }
            
            # 新版API只需要POST请求，无需body
            async with httpx.AsyncClient() as client:
                response = await client.post(url, headers=headers)
                if response.status_code == 200:
                    logger.info(f"成功完成钉钉待办任务: {task_id}")
                    return {"success": True}
                else:
                    logger.error(f"完成钉钉待办任务失败: {response.status_code}, {response.text}")
                    return None
        except Exception as e:
            logger.error(f"完成钉钉待办任务异常: {str(e)}")
            return None

    async def delete_todo_task(self, user_id: str, task_id: str, operator_id: str = None) -> Optional[Dict[str, Any]]:
        """
        删除钉钉待办任务 (使用最新v1.0 API)
        
        Args:
            user_id: 用户unionId
            task_id: 任务ID
            operator_id: 操作者unionId (可选，默认与user_id相同)
            
        Returns:
            Dict[str, Any]: 删除结果
        """
        try:
            access_token = await self.get_access_token()
            if not access_token:
                return None
            
            # 如果没有指定操作者，使用用户自己的unionId
            if not operator_id:
                operator_id = user_id
            
            url = f"https://api.dingtalk.com/v1.0/todo/users/{user_id}/tasks/{task_id}"
            params = {
                "operatorId": operator_id
            }
            headers = {
                "x-acs-dingtalk-access-token": access_token
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.delete(url, params=params, headers=headers)
                if response.status_code == 200:
                    logger.info(f"✅ 成功删除钉钉待办任务: {task_id}, 操作者: {operator_id}")
                    return {"success": True}
                else:
                    logger.error(f"❌ 删除钉钉待办任务失败: {response.status_code}, {response.text}")
                    return None
        except Exception as e:
            logger.error(f"💥 删除钉钉待办任务异常: {str(e)}")
            return None

    async def get_todo_task_detail(self, user_id: str, task_id: str) -> Optional[Dict[str, Any]]:
        """
        获取钉钉待办任务详情 (使用最新v1.0 API)
        
        Args:
            user_id: 用户ID
            task_id: 任务ID
            
        Returns:
            Dict[str, Any]: 任务详情
        """
        try:
            access_token = await self.get_access_token()
            if not access_token:
                return None
            
            url = f"https://api.dingtalk.com/v1.0/todo/users/{user_id}/tasks/{task_id}"
            headers = {
                "x-acs-dingtalk-access-token": access_token
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.get(url, headers=headers)
                if response.status_code == 200:
                    return response.json()
                else:
                    logger.error(f"获取钉钉待办任务详情失败: {response.status_code}, {response.text}")
                    return None
        except Exception as e:
            logger.error(f"获取钉钉待办任务详情异常: {str(e)}")
            return None

    def _convert_priority(self, priority: str) -> int:
        """
        转换优先级格式
        
        Args:
            priority: 优先级字符串 (low, normal, high, urgent)
            
        Returns:
            int: 钉钉API的优先级数字 (10-低, 20-普通, 30-高, 40-紧急)
        """
        priority_map = {
            "low": 10,
            "normal": 20,
            "high": 30,
            "urgent": 40
        }
        return priority_map.get(priority.lower(), 20)

    # ==================== 旧版API兼容方法 (废弃，保留向后兼容) ====================
    
    async def create_todo_task_legacy(self, task_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        创建钉钉待办任务 (旧版API，已废弃)
        
        仅用于向后兼容，建议使用 create_todo_task()
        """
        logger.warning("使用了废弃的旧版待办任务API，建议升级到新版API")
        
        try:
            access_token = await self.get_access_token()
            if not access_token:
                return None
            
            url = f"{self.base_url}/topapi/workrecord/add"
            params = {
                "access_token": access_token
            }
            
            # 构建任务数据
            data = {
                "userid": task_data.get("userid"),
                "create_time": task_data.get("create_time", int(datetime.now().timestamp() * 1000)),
                "title": task_data.get("title", ""),
                "url": task_data.get("url", ""),
                "formItemList": task_data.get("formItemList", [])
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.post(url, params=params, json=data)
                if response.status_code == 200:
                    result = response.json()
                    if result.get("errcode") == 0:
                        return result.get("result", {})
                    else:
                        logger.error(f"创建钉钉待办任务失败: {result}")
                        return None
                else:
                    logger.error(f"创建钉钉待办任务请求失败: {response.status_code}")
                    return None
        except Exception as e:
            logger.error(f"创建钉钉待办任务异常: {str(e)}")
            return None

    # ==================== 钉钉文档/知识库相关API ====================
    
    async def get_workspace_list(self, user_id: str, max_results: int = 100) -> Optional[Dict[str, Any]]:
        """
        获取用户的工作空间列表
        
        Args:
            user_id: 用户ID
            max_results: 返回结果数量上限
            
        Returns:
            Dict[str, Any]: 工作空间列表
        """
        try:
            access_token = await self.get_access_token()
            if not access_token:
                return None
            
            url = f"{self.base_url}/topapi/wiki/workspace/list"
            params = {
                "access_token": access_token
            }
            
            data = {
                "userId": user_id,
                "maxResults": max_results
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.post(url, params=params, json=data)
                if response.status_code == 200:
                    result = response.json()
                    if result.get("errcode") == 0:
                        return result.get("result", {})
                    else:
                        logger.error(f"获取工作空间列表失败: {result}")
                        return None
                else:
                    logger.error(f"获取工作空间列表请求失败: {response.status_code}")
                    return None
        except Exception as e:
            logger.error(f"获取工作空间列表异常: {str(e)}")
            return None

    async def get_space_list(self, workspace_id: str, max_results: int = 100) -> Optional[Dict[str, Any]]:
        """
        获取工作空间下的知识库列表
        
        Args:
            workspace_id: 工作空间ID
            max_results: 返回结果数量上限
            
        Returns:
            Dict[str, Any]: 知识库列表
        """
        try:
            access_token = await self.get_access_token()
            if not access_token:
                return None
            
            url = f"{self.base_url}/topapi/wiki/space/list"
            params = {
                "access_token": access_token
            }
            
            data = {
                "workspaceId": workspace_id,
                "maxResults": max_results
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.post(url, params=params, json=data)
                if response.status_code == 200:
                    result = response.json()
                    if result.get("errcode") == 0:
                        return result.get("result", {})
                    else:
                        logger.error(f"获取知识库列表失败: {result}")
                        return None
                else:
                    logger.error(f"获取知识库列表请求失败: {response.status_code}")
                    return None
        except Exception as e:
            logger.error(f"获取知识库列表异常: {str(e)}")
            return None

    async def get_doc_list(self, space_id: str, max_results: int = 100, next_token: str = None) -> Optional[Dict[str, Any]]:
        """
        获取知识库下的文档列表
        
        Args:
            space_id: 知识库ID
            max_results: 返回结果数量上限
            next_token: 分页Token
            
        Returns:
            Dict[str, Any]: 文档列表
        """
        try:
            access_token = await self.get_access_token()
            if not access_token:
                return None
            
            url = f"{self.base_url}/topapi/wiki/doc/list"
            params = {
                "access_token": access_token
            }
            
            data = {
                "spaceId": space_id,
                "maxResults": max_results
            }
            
            if next_token:
                data["nextToken"] = next_token
            
            async with httpx.AsyncClient() as client:
                response = await client.post(url, params=params, json=data)
                if response.status_code == 200:
                    result = response.json()
                    if result.get("errcode") == 0:
                        return result.get("result", {})
                    else:
                        logger.error(f"获取文档列表失败: {result}")
                        return None
                else:
                    logger.error(f"获取文档列表请求失败: {response.status_code}")
                    return None
        except Exception as e:
            logger.error(f"获取文档列表异常: {str(e)}")
            return None

    async def get_doc_content(self, doc_id: str, target_format: str = "markdown") -> Optional[Dict[str, Any]]:
        """
        获取文档内容
        
        Args:
            doc_id: 文档ID
            target_format: 目标格式（markdown, html等）
            
        Returns:
            Dict[str, Any]: 文档内容
        """
        try:
            access_token = await self.get_access_token()
            if not access_token:
                return None
            
            url = f"{self.base_url}/topapi/wiki/doc/get"
            params = {
                "access_token": access_token
            }
            
            data = {
                "docId": doc_id,
                "targetFormat": target_format
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.post(url, params=params, json=data)
                if response.status_code == 200:
                    result = response.json()
                    if result.get("errcode") == 0:
                        return result.get("result", {})
                    else:
                        logger.error(f"获取文档内容失败: {result}")
                        return None
                else:
                    logger.error(f"获取文档内容请求失败: {response.status_code}")
                    return None
        except Exception as e:
            logger.error(f"获取文档内容异常: {str(e)}")
            return None

    async def create_doc(self, space_id: str, title: str, content: str, parent_doc_id: str = None) -> Optional[Dict[str, Any]]:
        """
        创建文档
        
        Args:
            space_id: 知识库ID
            title: 文档标题
            content: 文档内容
            parent_doc_id: 父文档ID（可选）
            
        Returns:
            Dict[str, Any]: 创建结果
        """
        try:
            access_token = await self.get_access_token()
            if not access_token:
                return None
            
            url = f"{self.base_url}/topapi/wiki/doc/create"
            params = {
                "access_token": access_token
            }
            
            data = {
                "spaceId": space_id,
                "title": title,
                "content": content
            }
            
            if parent_doc_id:
                data["parentDocId"] = parent_doc_id
            
            async with httpx.AsyncClient() as client:
                response = await client.post(url, params=params, json=data)
                if response.status_code == 200:
                    result = response.json()
                    if result.get("errcode") == 0:
                        return result.get("result", {})
                    else:
                        logger.error(f"创建文档失败: {result}")
                        return None
                else:
                    logger.error(f"创建文档请求失败: {response.status_code}")
                    return None
        except Exception as e:
            logger.error(f"创建文档异常: {str(e)}")
            return None

    async def update_doc(self, doc_id: str, title: str = None, content: str = None) -> Optional[Dict[str, Any]]:
        """
        更新文档
        
        Args:
            doc_id: 文档ID
            title: 新标题（可选）
            content: 新内容（可选）
            
        Returns:
            Dict[str, Any]: 更新结果
        """
        try:
            access_token = await self.get_access_token()
            if not access_token:
                return None
            
            url = f"{self.base_url}/topapi/wiki/doc/update"
            params = {
                "access_token": access_token
            }
            
            data = {
                "docId": doc_id
            }
            
            if title:
                data["title"] = title
            if content:
                data["content"] = content
            
            async with httpx.AsyncClient() as client:
                response = await client.post(url, params=params, json=data)
                if response.status_code == 200:
                    result = response.json()
                    if result.get("errcode") == 0:
                        return result.get("result", {})
                    else:
                        logger.error(f"更新文档失败: {result}")
                        return None
                else:
                    logger.error(f"更新文档请求失败: {response.status_code}")
                    return None
        except Exception as e:
            logger.error(f"更新文档异常: {str(e)}")
            return None

    async def get_doc_permission(self, doc_id: str) -> Optional[Dict[str, Any]]:
        """
        获取文档权限信息
        
        Args:
            doc_id: 文档ID
            
        Returns:
            Dict[str, Any]: 权限信息
        """
        try:
            access_token = await self.get_access_token()
            if not access_token:
                return None
            
            url = f"{self.base_url}/topapi/wiki/doc/permission/get"
            params = {
                "access_token": access_token
            }
            
            data = {
                "docId": doc_id
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.post(url, params=params, json=data)
                if response.status_code == 200:
                    result = response.json()
                    if result.get("errcode") == 0:
                        return result.get("result", {})
                    else:
                        logger.error(f"获取文档权限失败: {result}")
                        return None
                else:
                    logger.error(f"获取文档权限请求失败: {response.status_code}")
                    return None
        except Exception as e:
            logger.error(f"获取文档权限异常: {str(e)}")
            return None

    async def set_doc_permission(self, doc_id: str, permission_config: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        设置文档权限
        
        Args:
            doc_id: 文档ID
            permission_config: 权限配置
            
        Returns:
            Dict[str, Any]: 设置结果
        """
        try:
            access_token = await self.get_access_token()
            if not access_token:
                return None
            
            url = f"{self.base_url}/topapi/wiki/doc/permission/set"
            params = {
                "access_token": access_token
            }
            
            data = {
                "docId": doc_id,
                **permission_config
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.post(url, params=params, json=data)
                if response.status_code == 200:
                    result = response.json()
                    if result.get("errcode") == 0:
                        return result.get("result", {})
                    else:
                        logger.error(f"设置文档权限失败: {result}")
                        return None
                else:
                    logger.error(f"设置文档权限请求失败: {response.status_code}")
                    return None
        except Exception as e:
            logger.error(f"设置文档权限异常: {str(e)}")
            return None
    
    @staticmethod
    def generate_webhook_signature(timestamp: str, secret: str) -> str:
        """
        生成Webhook签名
        
        Args:
            timestamp: 时间戳
            secret: 密钥
            
        Returns:
            str: 签名
        """
        string_to_sign = f"{timestamp}\n{secret}"
        hmac_code = hmac.new(secret.encode(), string_to_sign.encode(), digestmod=hashlib.sha256).digest()
        sign = urllib.parse.quote_plus(base64.b64encode(hmac_code).decode())
        return sign
    
    @staticmethod
    async def send_webhook_message(webhook_url: str, message: Dict[str, Any], secret: str = None) -> bool:
        """
        发送Webhook消息
        
        Args:
            webhook_url: Webhook URL
            message: 消息内容
            secret: 密钥（可选）
            
        Returns:
            bool: 发送是否成功
        """
        try:
            # 如果有密钥，生成签名
            if secret:
                timestamp = str(int(round(time.time() * 1000)))
                sign = DingTalkAPI.generate_webhook_signature(timestamp, secret)
                webhook_url = f"{webhook_url}&timestamp={timestamp}&sign={sign}"
            
            async with httpx.AsyncClient() as client:
                response = await client.post(webhook_url, json=message)
                if response.status_code == 200:
                    result = response.json()
                    if result.get("errcode") == 0:
                        logger.info(f"发送Webhook消息成功: {result}")
                        return True
                    else:
                        logger.error(f"发送Webhook消息失败: {result}")
                        return False
                else:
                    logger.error(f"发送Webhook消息请求失败: {response.status_code}")
                    return False
        except Exception as e:
            logger.error(f"发送Webhook消息异常: {str(e)}")
            return False

    # ==================== 钉钉审批流程相关API ====================
    
    async def create_or_update_approval_template(self, template_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        创建或更新钉钉审批模板（使用官方v1.0 workflow API）
        
        参考文档：https://open.dingtalk.com/document/isvapp/create-or-update-approval-templates-new
        API端点：POST /v1.0/workflow/processCentres/schemas
        
        Args:
            template_data: 模板数据
            
        Returns:
            Dict[str, Any]: 操作结果
        """
        try:
            access_token = await self.get_access_token()
            if not access_token:
                return {
                    "success": False,
                    "error": "获取访问令牌失败"
                }
            
            # 使用官方文档确认的API端点
            url = f"https://api.dingtalk.com/v1.0/workflow/processCentres/schemas"
            headers = {
                "x-acs-dingtalk-access-token": access_token,
                "Content-Type": "application/json"
            }
            
            # 构建官方API数据结构
            dingtalk_template = self._build_official_approval_template_data(template_data)
            
            # 添加详细的请求日志
            logger.info(f"创建审批模板请求（官方workflow API）: {url}")
            logger.info(f"请求数据: {dingtalk_template}")
            
            async with httpx.AsyncClient() as client:
                response = await client.post(url, headers=headers, json=dingtalk_template)
                
                if response.status_code == 200:
                    result = response.json()
                    
                    # 钉钉创建成功，直接返回整个响应给service层处理
                    logger.info(f"钉钉审批模板API响应: {result}")
                    return {
                        "success": True,
                        "message": "模板创建/更新成功",
                        "result": result  # 完整返回钉钉的响应
                    }
                else:
                    try:
                        error_data = response.json()
                        error_code = error_data.get("code", "UNKNOWN")
                        error_message = error_data.get("message", "未知错误")
                        error_msg = f"钉钉API错误: {error_code} - {error_message}"
                    except:
                        error_msg = f"请求失败: HTTP {response.status_code}"
                    
                    logger.error(error_msg)
                    return {
                        "success": False,
                        "error": error_msg
                    }
        except Exception as e:
            logger.error(f"创建/更新审批模板异常: {str(e)}")
            logger.error(f"异常详情: {type(e).__name__}: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def _build_official_approval_template_data(self, template_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        构建官方钉钉审批模板数据结构（严格按照官方API文档）
        参考：https://open.dingtalk.com/document/isvapp/create-or-update-approval-templates-new
        
        Args:
            template_data: 原始模板数据
            
        Returns:
            Dict[str, Any]: 官方API数据结构
        """
        template_type = template_data.get('type', 'general')
        
        # 优先使用前端传递的formComponents，如果没有则根据类型构建
        form_components = template_data.get('formComponents', [])
        
        if not form_components:
            logger.info(f"前端未传递formComponents，根据模板类型 {template_type} 自动构建")
            # 根据业务类型构建表单组件（官方API格式）
            form_components = self._build_comprehensive_form_components(template_type, template_data.get('fields', []))
        else:
            logger.info(f"使用前端传递的formComponents，组件数量: {len(form_components)}")
        
        # 确保表单组件不为空，如果为空则使用默认组件
        if not form_components or len(form_components) == 0:
            logger.warning(f"模板类型 {template_type} 的表单组件为空，使用默认组件")
            form_components = self._build_default_components()
        
        # 验证表单组件结构
        validated_components = []
        for component in form_components:
            if self._validate_form_component(component):
                validated_components.append(component)
            else:
                logger.warning(f"表单组件验证失败，跳过: {component}")
        
        if not validated_components:
            logger.error("所有表单组件验证失败，使用默认组件")
            validated_components = self._build_default_components()
        
        # 严格按照官方API文档构建数据结构
        dingtalk_template = {
            "name": template_data['name'],
            "description": template_data.get('description', ''),
            "formComponents": validated_components,  # 官方文档字段名是formComponents，不是formComponentList
            
            # 添加官方文档要求的processFeatureConfig
            "processFeatureConfig": {
                "features": []  # 可以为空，但需要存在
            },
            
            # 添加官方文档要求的templateConfig
            "templateConfig": {
                "hidden": False,
                "disableSendCard": False
            }
        }
        
        # 如果是更新操作，添加processCode
        if template_data.get('process_code'):
            dingtalk_template['processCode'] = template_data['process_code']
        
        logger.info(f"构建的官方审批模板数据: name={dingtalk_template['name']}, 表单组件数量={len(validated_components)}")
        logger.debug(f"完整模板数据结构: {dingtalk_template}")
        return dingtalk_template
    
    def _validate_form_component(self, component: Dict[str, Any]) -> bool:
        """
        验证表单组件是否符合钉钉官方API要求
        
        Args:
            component: 表单组件数据
            
        Returns:
            bool: 验证是否通过
        """
        try:
            # 检查必要字段
            if not isinstance(component, dict):
                logger.error("表单组件必须是字典类型")
                return False
            
            if "componentType" not in component:
                logger.error("表单组件缺少componentType字段")
                return False
            
            if "props" not in component:
                logger.error("表单组件缺少props字段")
                return False
            
            props = component["props"]
            if not isinstance(props, dict):
                logger.error("表单组件props必须是字典类型")
                return False
            
            # 检查props中的必要字段
            if "componentId" not in props:
                logger.error("表单组件props缺少componentId字段")
                return False
            
            if "label" not in props:
                logger.error("表单组件props缺少label字段")
                return False
            
            # 验证支持的组件类型
            supported_types = [
                "TextField", "TextareaField", "NumberField", "MoneyField", 
                "DDDateField", "DDDateTimeField", "DDSelectField", "RadioField", 
                "CheckboxField", "DDPhotoField", "FileField", "UserField", 
                "DeptField", "RelateField"
            ]
            
            component_type = component["componentType"]
            if component_type not in supported_types:
                logger.warning(f"组件类型 {component_type} 可能不被钉钉支持，支持的类型: {supported_types}")
                # 不直接返回False，让钉钉API来决定是否支持
            
            logger.debug(f"表单组件验证通过: {component_type} - {props.get('label')}")
            return True
            
        except Exception as e:
            logger.error(f"表单组件验证异常: {str(e)}")
            return False
    
    def _build_default_components(self) -> List[Dict[str, Any]]:
        """构建默认表单组件，防止表单组件为空导致API调用失败"""
        return [
            {
                "componentType": "TextField",
                "props": {
                    "componentId": "TextField_title",
                    "label": "申请标题",
                    "required": True,
                    "placeholder": "请输入申请标题"
                }
            },
            {
                "componentType": "TextareaField",
                "props": {
                    "componentId": "TextareaField_content",
                    "label": "申请内容",
                    "required": True,
                    "placeholder": "请详细描述申请内容"
                }
            },
            {
                "componentType": "DateField",
                "props": {
                    "componentId": "DateField_apply_date",
                    "label": "申请日期",
                    "required": True,
                    "format": "yyyy-MM-dd"
                }
            }
        ]
    
    def _build_comprehensive_form_components(self, template_type: str, fields: List[str]) -> List[Dict[str, Any]]:
        """
        根据审批类型构建官方API表单组件（严格按照官方文档）
        
        参考文档：https://open.dingtalk.com/document/isvapp/create-or-update-approval-templates-new
        
        Args:
            template_type: 模板类型
            fields: 字段列表
            
        Returns:
            List[Dict[str, Any]]: 官方API表单组件列表
        """
        logger.info(f"构建表单组件: template_type={template_type}, fields={fields}")
        
        # 根据不同模板类型构建专业的表单组件
        if template_type == 'store_loss':
            components = self._build_store_loss_components()
            logger.info(f"门店报损组件构建完成，组件数量: {len(components)}")
            return components
        elif template_type == 'sales_report':
            return self._build_sales_report_components()
        elif template_type == 'purchase_request':
            return self._build_purchase_request_components()
        elif template_type == 'expense_reimbursement':
            return self._build_expense_reimbursement_components()
        elif template_type == 'leave_request':
            return self._build_leave_request_components()
        elif template_type == 'comprehensive_demo':
            return self._build_comprehensive_demo_components()
        elif template_type == 'survey_questionnaire':
            return self._build_survey_questionnaire_components()
        elif template_type == 'contract_approval':
            return self._build_contract_approval_components()
        elif template_type == 'recruitment_approval':
            return self._build_recruitment_approval_components()
        elif template_type == 'equipment_maintenance':
            return self._build_equipment_maintenance_components()
        else:
            logger.warning(f"未知模板类型 {template_type}，使用通用组件")
            return self._build_general_components()
    
    def _build_store_loss_components(self) -> List[Dict[str, Any]]:
        """构建产品报损申请表单组件（包含图片上传）"""
        return [
            {
                "componentType": "TextField",
                "props": {
                    "componentId": "TextField_store_name",
                    "label": "门店名称",
                    "required": True,
                    "placeholder": "请输入门店名称"
                }
            },
            {
                "componentType": "TextField",
                "props": {
                    "componentId": "TextField_product_name",
                    "label": "产品名称",
                    "required": True,
                    "placeholder": "请输入产品名称"
                }
            },
            {
                "componentType": "TextField",
                "props": {
                    "componentId": "TextField_product_code",
                    "label": "产品编码",
                    "required": False,
                    "placeholder": "请输入产品编码"
                }
            },
            {
                "componentType": "NumberField",
                "props": {
                    "componentId": "NumberField_quantity",
                    "label": "报损数量",
                    "required": True,
                    "placeholder": "请输入报损数量",
                    "format": "dn"
                }
            },
            {
                "componentType": "MoneyField",
                "props": {
                    "componentId": "MoneyField_unit_price",
                    "label": "单价（元）",
                    "required": True,
                    "placeholder": "请输入单价",
                    "unit": "元"
                }
            },
            {
                "componentType": "MoneyField",
                "props": {
                    "componentId": "MoneyField_total_amount",
                    "label": "总损失金额（元）",
                    "required": True,
                    "placeholder": "自动计算或手动输入",
                    "unit": "元"
                }
            },
            {
                "componentType": "DDSelectField",
                "props": {
                    "componentId": "DDSelectField_loss_reason",
                    "label": "报损原因",
                    "required": True,
                    "options": [
                        {"key": "expired", "value": "过期变质"},
                        {"key": "damaged", "value": "包装破损"},
                        {"key": "quality", "value": "质量问题"},
                        {"key": "theft", "value": "盗损"},
                        {"key": "other", "value": "其他原因"}
                    ]
                }
            },
            {
                "componentType": "TextareaField",
                "props": {
                    "componentId": "TextareaField_description",
                    "label": "详细说明",
                    "required": True,
                    "placeholder": "请详细描述报损情况、发生时间、处理措施等"
                }
            },
            {
                "componentType": "DDPhotoField",
                "props": {
                    "label": "报损证据照片",
                    "componentId": "DDPhotoField_evidence",
                    "required": True
                }
            },
            {
                "componentType": "DDDateField",
                "props": {
                    "componentId": "DDDateField_loss_date",
                    "label": "发现报损日期",
                    "required": True,
                    "unit": "天",
                    "format": "yyyy-MM-dd",
                    "placeholder": "请选择日期"
                }
            }
        ]
    
    def _build_sales_report_components(self) -> List[Dict[str, Any]]:
        """构建销售业绩上报表单组件"""
        return [
            {
                "componentType": "TextField",
                "props": {
                    "componentId": "TextField_store_name",
                    "label": "门店名称",
                    "required": True,
                    "placeholder": "请输入门店名称"
                }
            },
            {
                "componentType": "DDDateField",
                "props": {
                    "componentId": "DDDateField_report_date",
                    "label": "销售日期",
                    "required": True,
                    "unit": "天",
                    "format": "yyyy-MM-dd",
                    "placeholder": "请选择销售日期"
                }
            },
            {
                "componentType": "MoneyField",
                "props": {
                    "componentId": "MoneyField_total_sales",
                    "label": "总销售额（元）",
                    "required": True,
                    "placeholder": "请输入当日总销售额",
                    "unit": "元"
                }
            },
            {
                "componentType": "NumberField",
                "props": {
                    "componentId": "NumberField_customer_count",
                    "label": "客流量（人次）",
                    "required": True,
                    "placeholder": "请输入当日客流量",
                    "format": "dn"
                }
            },
            {
                "componentType": "NumberField",
                "props": {
                    "componentId": "NumberField_transaction_count",
                    "label": "交易笔数",
                    "required": False,
                    "placeholder": "请输入交易笔数",
                    "format": "dn"
                }
            },
            {
                "componentType": "TextareaField",
                "props": {
                    "componentId": "TextareaField_remarks",
                    "label": "备注说明",
                    "required": False,
                    "placeholder": "请填写特殊情况或其他说明"
                }
            }
        ]
    
    def _build_purchase_request_components(self) -> List[Dict[str, Any]]:
        """构建采购申请表单组件"""
        return [
            {
                "componentType": "TextField",
                "props": {
                    "componentId": "TextField_store_name",
                    "label": "申请门店",
                    "required": True,
                    "placeholder": "请输入门店名称"
                }
            },
            {
                "componentType": "DDDateField",
                "props": {
                    "componentId": "DDDateField_need_date",
                    "label": "需求日期",
                    "required": True,
                    "unit": "天",
                    "format": "yyyy-MM-dd",
                    "placeholder": "请选择需求日期"
                }
            },
            {
                "componentType": "MoneyField",
                "props": {
                    "componentId": "MoneyField_total_amount",
                    "label": "申请总金额（元）",
                    "required": True,
                    "placeholder": "请输入采购总金额",
                    "unit": "元"
                }
            },
            {
                "componentType": "TextareaField",
                "props": {
                    "componentId": "TextareaField_reason",
                    "label": "申请理由",
                    "required": True,
                    "placeholder": "请详细说明采购原因和用途"
                }
            },
            {
                "componentType": "TextareaField",
                "props": {
                    "componentId": "TextareaField_items",
                    "label": "采购清单",
                    "required": True,
                    "placeholder": "请列出具体的采购商品、数量、规格等"
                }
            }
        ]
    
    def _build_expense_reimbursement_components(self) -> List[Dict[str, Any]]:
        """构建费用报销表单组件（包含发票上传）"""
        return [
            {
                "componentType": "TextField",
                "props": {
                    "componentId": "TextField_store_name",
                    "label": "报销门店",
                    "required": True,
                    "placeholder": "请输入门店名称"
                }
            },
            {
                "componentType": "DDDateField",
                "props": {
                    "componentId": "DDDateField_expense_date",
                    "label": "费用发生日期",
                    "required": True,
                    "unit": "天",
                    "format": "yyyy-MM-dd",
                    "placeholder": "请选择费用发生日期"
                }
            },
            {
                "componentType": "DDSelectField",
                "props": {
                    "componentId": "DDSelectField_expense_type",
                    "label": "费用类型",
                    "required": True,
                    "options": [
                        {"key": "transport", "value": "交通费"},
                        {"key": "meal", "value": "餐费"},
                        {"key": "office", "value": "办公用品"},
                        {"key": "marketing", "value": "营销费用"},
                        {"key": "maintenance", "value": "维修费"},
                        {"key": "other", "value": "其他费用"}
                    ]
                }
            },
            {
                "componentType": "MoneyField",
                "props": {
                    "componentId": "MoneyField_amount",
                    "label": "报销金额（元）",
                    "required": True,
                    "placeholder": "请输入报销金额",
                    "unit": "元"
                }
            },
            {
                "componentType": "TextareaField",
                "props": {
                    "componentId": "TextareaField_description",
                    "label": "费用说明",
                    "required": True,
                    "placeholder": "请详细说明费用用途和必要性"
                }
            },
            {
                "componentType": "DDPhotoField",
                "props": {
                    "componentId": "DDPhotoField_invoice",
                    "label": "发票/收据",
                    "required": True,
                    "placeholder": "请上传发票或收据照片",
                    "limit": 10
                }
            }
        ]
    
    def _build_leave_request_components(self) -> List[Dict[str, Any]]:
        """构建请假申请表单组件"""
        return [
            {
                "componentType": "TextField",
                "props": {
                    "componentId": "TextField_store_name",
                    "label": "所属门店",
                    "required": True,
                    "placeholder": "请输入门店名称"
                }
            },
            {
                "componentType": "DDSelectField",
                "props": {
                    "componentId": "DDSelectField_leave_type",
                    "label": "请假类型",
                    "required": True,
                    "options": [
                        {"key": "annual", "value": "年假"},
                        {"key": "sick", "value": "病假"},
                        {"key": "personal", "value": "事假"},
                        {"key": "maternity", "value": "产假"},
                        {"key": "marriage", "value": "婚假"},
                        {"key": "funeral", "value": "丧假"},
                        {"key": "other", "value": "其他"}
                    ]
                }
            },
            {
                "componentType": "DDDateField",
                "props": {
                    "componentId": "DDDateField_start_date",
                    "label": "开始日期",
                    "required": True,
                    "unit": "天",
                    "format": "yyyy-MM-dd",
                    "placeholder": "请选择开始日期"
                }
            },
            {
                "componentType": "DDDateField",
                "props": {
                    "componentId": "DDDateField_end_date",
                    "label": "结束日期",
                    "required": True,
                    "unit": "天",
                    "format": "yyyy-MM-dd",
                    "placeholder": "请选择结束日期"
                }
            },
            {
                "componentType": "NumberField",
                "props": {
                    "componentId": "NumberField_days",
                    "label": "请假天数",
                    "required": True,
                    "placeholder": "请输入请假天数",
                    "format": "dn"
                }
            },
            {
                "componentType": "TextareaField",
                "props": {
                    "componentId": "TextareaField_reason",
                    "label": "请假理由",
                    "required": True,
                    "placeholder": "请详细说明请假原因"
                }
            },
            {
                "componentType": "DDPhotoField",
                "props": {
                    "componentId": "DDPhotoField_certificate",
                    "label": "相关证明",
                    "required": False,
                    "placeholder": "如病假需上传病假条等证明材料",
                    "limit": 3
                }
            }
        ]
    
    def _build_general_components(self) -> List[Dict[str, Any]]:
        """构建通用申请表单组件"""
        return [
            {
                "componentType": "TextField",
                "props": {
                    "componentId": "TextField_applicant",
                    "label": "申请人",
                    "required": True,
                    "placeholder": "请输入申请人姓名"
                }
            },
            {
                "componentType": "TextField",
                "props": {
                    "componentId": "TextField_department",
                    "label": "申请部门",
                    "required": True,
                    "placeholder": "请输入申请部门"
                }
            },
            {
                "componentType": "DDDateField",
                "props": {
                    "componentId": "DDDateField_apply_date",
                    "label": "申请日期",
                    "required": True,
                    "unit": "天",
                    "format": "yyyy-MM-dd",
                    "placeholder": "请选择申请日期"
                }
            },
            {
                "componentType": "TextareaField",
                "props": {
                    "componentId": "TextareaField_content",
                    "label": "申请内容",
                    "required": True,
                    "placeholder": "请详细描述申请内容"
                }
            },
            {
                "componentType": "TextareaField",
                "props": {
                    "componentId": "TextareaField_reason",
                    "label": "申请理由",
                    "required": True,
                    "placeholder": "请说明申请理由"
                }
            }
        ]
    
    async def get_approval_template_by_name(self, template_name: str) -> Optional[Dict[str, Any]]:
        """
        根据模板名称获取模板编码
        
        Args:
            template_name: 模板名称
            
        Returns:
            Dict[str, Any]: 模板信息
        """
        try:
            access_token = await self.get_access_token()
            if not access_token:
                return None
            
            url = f"{self.base_url}/topapi/process/gettemplatebyname"
            params = {
                "access_token": access_token
            }
            
            data = {
                "name": template_name
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.post(url, params=params, json=data)
                if response.status_code == 200:
                    result = response.json()
                    if result.get("errcode") == 0:
                        return result.get("result", {})
                    else:
                        logger.error(f"获取审批模板失败: {result}")
                        return None
                else:
                    logger.error(f"获取审批模板请求失败: {response.status_code}")
                    return None
        except Exception as e:
            logger.error(f"获取审批模板异常: {str(e)}")
            return None
    
    async def create_approval_instance(self, instance_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        创建审批实例
        
        Args:
            instance_data: 实例数据
            
        Returns:
            Dict[str, Any]: 创建结果
        """
        try:
            access_token = await self.get_access_token()
            if not access_token:
                return None
            
            url = f"{self.base_url}/topapi/processinstance/create"
            params = {
                "access_token": access_token
            }
            
            # 构建钉钉审批实例数据
            dingtalk_instance = {
                "process_code": instance_data["process_code"],
                "originator_user_id": instance_data["originator_user_id"],
                "form_component_values": instance_data["form_component_values"],
                "approvers": instance_data.get("approvers", []),
                "cc_list": instance_data.get("cc_list", []),
                "cc_position": instance_data.get("cc_position", "FINISH")
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.post(url, params=params, json=dingtalk_instance)
                if response.status_code == 200:
                    result = response.json()
                    if result.get("errcode") == 0:
                        logger.info(f"创建审批实例成功: {result}")
                        return {
                            "success": True,
                            "process_instance_id": result.get("result", {}).get("process_instance_id"),
                            "message": "创建审批实例成功"
                        }
                    else:
                        logger.error(f"创建审批实例失败: {result}")
                        return {
                            "success": False,
                            "error": result.get("errmsg", "未知错误")
                        }
                else:
                    logger.error(f"创建审批实例请求失败: {response.status_code}")
                    return {
                        "success": False,
                        "error": f"请求失败: {response.status_code}"
                    }
        except Exception as e:
            logger.error(f"创建审批实例异常: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def get_approval_instances(self, params: Dict[str, Any] = None) -> Optional[Dict[str, Any]]:
        """
        获取审批实例列表
        
        Args:
            params: 查询参数
            
        Returns:
            Dict[str, Any]: 实例列表
        """
        try:
            access_token = await self.get_access_token()
            if not access_token:
                return None
            
            url = f"{self.base_url}/topapi/processinstance/listids"
            request_params = {
                "access_token": access_token
            }
            
            # 修复参数格式 - 根据测试结果，process_code是必需参数
            query_params = {
                "process_code": params.get("process_code", ""),  # 空字符串表示查询所有流程
                "start_time": params.get("start_time", int((datetime.now() - timedelta(days=7)).timestamp() * 1000)),
                "end_time": params.get("end_time", int(datetime.now().timestamp() * 1000)),
                "size": min(params.get("size", 10), 20),  # 限制最大查询数量
                "cursor": params.get("cursor", 0)
            }
            
            # 添加用户ID过滤
            if params.get("userid_list"):
                query_params["userid_list"] = params["userid_list"]
            
            async with httpx.AsyncClient() as client:
                response = await client.post(url, params=request_params, json=query_params)
                if response.status_code == 200:
                    result = response.json()
                    if result.get("errcode") == 0:
                        return result.get("result", {})
                    else:
                        logger.error(f"获取审批实例列表失败: {result}")
                        return None
                else:
                    logger.error(f"获取审批实例列表请求失败: {response.status_code}")
                    return None
        except Exception as e:
            logger.error(f"获取审批实例列表异常: {str(e)}")
            return None
    
    async def get_approval_instance_detail(self, process_instance_id: str) -> Optional[Dict[str, Any]]:
        """
        获取审批实例详情
        
        Args:
            process_instance_id: 实例ID
            
        Returns:
            Dict[str, Any]: 实例详情
        """
        try:
            access_token = await self.get_access_token()
            if not access_token:
                return None
            
            url = f"{self.base_url}/topapi/processinstance/get"
            params = {
                "access_token": access_token
            }
            
            data = {
                "process_instance_id": process_instance_id
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.post(url, params=params, json=data)
                if response.status_code == 200:
                    result = response.json()
                    if result.get("errcode") == 0:
                        return result.get("result", {})
                    else:
                        logger.error(f"获取审批实例详情失败: {result}")
                        return None
                else:
                    logger.error(f"获取审批实例详情请求失败: {response.status_code}")
                    return None
        except Exception as e:
            logger.error(f"获取审批实例详情异常: {str(e)}")
            return None
    
    async def terminate_approval_instance(self, process_instance_id: str, operating_user_id: str, reason: str = "") -> Optional[Dict[str, Any]]:
        """
        终止审批实例
        
        Args:
            process_instance_id: 实例ID
            operating_user_id: 操作人用户ID
            reason: 终止原因
            
        Returns:
            Dict[str, Any]: 操作结果
        """
        try:
            access_token = await self.get_access_token()
            if not access_token:
                return None
            
            url = f"{self.base_url}/topapi/processinstance/terminate"
            params = {
                "access_token": access_token
            }
            
            data = {
                "process_instance_id": process_instance_id,
                "operating_user_id": operating_user_id,
                "reason": reason
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.post(url, params=params, json=data)
                if response.status_code == 200:
                    result = response.json()
                    if result.get("errcode") == 0:
                        logger.info(f"终止审批实例成功: {result}")
                        return {
                            "success": True,
                            "message": "终止审批实例成功"
                        }
                    else:
                        logger.error(f"终止审批实例失败: {result}")
                        return {
                            "success": False,
                            "error": result.get("errmsg", "未知错误")
                        }
                else:
                    logger.error(f"终止审批实例请求失败: {response.status_code}")
                    return {
                        "success": False,
                        "error": f"请求失败: {response.status_code}"
                    }
        except Exception as e:
            logger.error(f"终止审批实例异常: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def execute_approval_task(self, task_id: str, user_id: str, result: str, reason: str = "") -> Optional[Dict[str, Any]]:
        """
        执行审批任务
        
        Args:
            task_id: 任务ID
            user_id: 用户ID
            result: 审批结果 (agree/refuse)
            reason: 审批意见
            
        Returns:
            Dict[str, Any]: 执行结果
        """
        try:
            access_token = await self.get_access_token()
            if not access_token:
                return None
            
            url = f"{self.base_url}/topapi/processinstance/execute"
            params = {
                "access_token": access_token
            }
            
            data = {
                "task_id": task_id,
                "actionerUserId": user_id,
                "result": result,
                "reason": reason
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.post(url, params=params, json=data)
                if response.status_code == 200:
                    result_data = response.json()
                    if result_data.get("errcode") == 0:
                        logger.info(f"执行审批任务成功: {result_data}")
                        return {
                            "success": True,
                            "message": "执行审批任务成功"
                        }
                    else:
                        logger.error(f"执行审批任务失败: {result_data}")
                        return {
                            "success": False,
                            "error": result_data.get("errmsg", "未知错误")
                        }
                else:
                    logger.error(f"执行审批任务请求失败: {response.status_code}")
                    return {
                        "success": False,
                        "error": f"请求失败: {response.status_code}"
                    }
        except Exception as e:
            logger.error(f"执行审批任务异常: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def get_todo_num(self, user_id: str) -> Optional[Dict[str, Any]]:
        """
        获取待审批任务数量
        
        Args:
            user_id: 用户ID
            
        Returns:
            Dict[str, Any]: 待办数量
        """
        try:
            access_token = await self.get_access_token()
            if not access_token:
                return None
            
            url = f"{self.base_url}/topapi/processinstance/todonum"
            params = {
                "access_token": access_token
            }
            
            data = {
                "userid": user_id
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.post(url, params=params, json=data)
                if response.status_code == 200:
                    result = response.json()
                    if result.get("errcode") == 0:
                        return result.get("result", {})
                    else:
                        logger.error(f"获取待审批任务数量失败: {result}")
                        return None
                else:
                    logger.error(f"获取待审批任务数量请求失败: {response.status_code}")
                    return None
        except Exception as e:
            logger.error(f"获取待审批任务数量异常: {str(e)}")
            return None
    
    async def handle_approval_callback(self, callback_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理钉钉审批回调事件
        
        Args:
            callback_data: 回调数据
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        try:
            event_type = callback_data.get('EventType')
            
            if event_type == 'bpms_instance_change':
                # 审批实例状态变更
                return await self._handle_instance_change_callback(callback_data)
            elif event_type == 'bpms_task_change':
                # 审批任务状态变更
                return await self._handle_task_change_callback(callback_data)
            else:
                logger.warning(f"未处理的回调事件类型: {event_type}")
                return {
                    "success": True,
                    "message": f"未处理的事件类型: {event_type}"
                }
                
        except Exception as e:
            logger.error(f"处理审批回调异常: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _handle_instance_change_callback(self, callback_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理审批实例状态变更回调
        
        Args:
            callback_data: 回调数据
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        try:
            process_instance_id = callback_data.get('processInstanceId')
            status = callback_data.get('status')
            result = callback_data.get('result')
            
            logger.info(f"审批实例 {process_instance_id} 状态变更: {status}, 结果: {result}")
            
            # 这里可以添加业务逻辑处理
            # 例如：更新本地数据库记录、触发后续流程等
            
            return {
                "success": True,
                "message": "实例状态变更处理成功",
                "data": {
                    "process_instance_id": process_instance_id,
                    "status": status,
                    "result": result
                }
            }
            
        except Exception as e:
            logger.error(f"处理实例状态变更回调失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def _handle_task_change_callback(self, callback_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理审批任务状态变更回调
        
        Args:
            callback_data: 回调数据
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        try:
            process_instance_id = callback_data.get('processInstanceId')
            task_id = callback_data.get('taskId')
            status = callback_data.get('status')
            
            logger.info(f"审批任务 {task_id} (实例 {process_instance_id}) 状态变更: {status}")
            
            # 这里可以添加业务逻辑处理
            # 例如：发送通知、更新任务状态等
            
            return {
                "success": True,
                "message": "任务状态变更处理成功",
                "data": {
                    "process_instance_id": process_instance_id,
                    "task_id": task_id,
                    "status": status
                }
            }
            
        except Exception as e:
            logger.error(f"处理任务状态变更回调失败: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def start_approval_instance(self, instance_id: str, operating_user_id: str) -> Optional[Dict[str, Any]]:
        """
        启动审批实例
        
        Args:
            instance_id: 实例ID
            operating_user_id: 操作人用户ID
            
        Returns:
            Dict[str, Any]: 操作结果
        """
        try:
            access_token = await self.get_access_token()
            if not access_token:
                return {
                    "success": False,
                    "error": "获取访问令牌失败"
                }
            
            url = f"https://api.dingtalk.com/v1.0/workflow/processInstances/{instance_id}/start"
            headers = {
                "x-acs-dingtalk-access-token": access_token,
                "Content-Type": "application/json"
            }
            
            data = {
                "operatorUserId": operating_user_id
            }
            
            logger.info(f"启动审批实例: {instance_id}")
            
            async with httpx.AsyncClient() as client:
                response = await client.post(url, headers=headers, json=data)
                response_text = response.text
                logger.info(f"启动审批实例API响应: {response_text}")
                
                if response.status_code == 200:
                    result = response.json()
                    return {
                        "success": True,
                        "message": "审批实例启动成功",
                        "data": result
                    }
                else:
                    return {
                        "success": False,
                        "error": f"启动审批实例失败: HTTP {response.status_code}, {response_text}"
                    }
            
        except Exception as e:
            logger.error(f"启动审批实例异常: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def complete_approval_instance(self, instance_id: str, operating_user_id: str, result: str = "agree") -> Optional[Dict[str, Any]]:
        """
        完成审批实例
        
        Args:
            instance_id: 实例ID
            operating_user_id: 操作人用户ID
            result: 审批结果（agree/refuse）
            
        Returns:
            Dict[str, Any]: 操作结果
        """
        try:
            access_token = await self.get_access_token()
            if not access_token:
                return {
                    "success": False,
                    "error": "获取访问令牌失败"
                }
            
            url = f"https://api.dingtalk.com/v1.0/workflow/processInstances/{instance_id}/complete"
            headers = {
                "x-acs-dingtalk-access-token": access_token,
                "Content-Type": "application/json"
            }
            
            data = {
                "operatorUserId": operating_user_id,
                "result": result
            }
            
            logger.info(f"完成审批实例: {instance_id}, 结果: {result}")
            
            async with httpx.AsyncClient() as client:
                response = await client.post(url, headers=headers, json=data)
                response_text = response.text
                logger.info(f"完成审批实例API响应: {response_text}")
                
                if response.status_code == 200:
                    result_data = response.json()
                    return {
                        "success": True,
                        "message": "审批实例完成成功",
                        "data": result_data
                    }
                else:
                    return {
                        "success": False,
                        "error": f"完成审批实例失败: HTTP {response.status_code}, {response_text}"
                    }
            
        except Exception as e:
            logger.error(f"完成审批实例异常: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    async def delete_approval_instance(self, instance_id: str, operating_user_id: str) -> Optional[Dict[str, Any]]:
        """
        删除审批实例
        
        Args:
            instance_id: 实例ID
            operating_user_id: 操作人用户ID
            
        Returns:
            Dict[str, Any]: 操作结果
        """
        try:
            access_token = await self.get_access_token()
            if not access_token:
                return {
                    "success": False,
                    "error": "获取访问令牌失败"
                }
            
            url = f"https://api.dingtalk.com/v1.0/workflow/processInstances/{instance_id}"
            headers = {
                "x-acs-dingtalk-access-token": access_token,
                "Content-Type": "application/json"
            }
            
            params = {
                "operatorUserId": operating_user_id
            }
            
            logger.info(f"删除审批实例: {instance_id}")
            
            async with httpx.AsyncClient() as client:
                response = await client.delete(url, headers=headers, params=params)
                response_text = response.text
                logger.info(f"删除审批实例API响应: {response_text}")
                
                if response.status_code == 200:
                    return {
                        "success": True,
                        "message": "审批实例删除成功"
                    }
                else:
                    return {
                        "success": False,
                        "error": f"删除审批实例失败: HTTP {response.status_code}, {response_text}"
                    }
            
        except Exception as e:
            logger.error(f"删除审批实例异常: {str(e)}")
            return {
                "success": False,
                "error": str(e)
            }
    
    # ==================== 钉钉协作空间/知识库相关API (最新版本) ====================
    # 基于钉钉开放平台最新文档：https://open.dingtalk.com/document/orgapp/document-operations
    
    async def _get_dingtalk_user_id(self, system_user_id: str = None) -> Optional[str]:
        """
        获取钉钉用户ID，用于需要operatorId的API调用
        
        统一查询逻辑：仅查询ThirdPartyAccount表，与用户管理保持一致
        
        Args:
            system_user_id: 系统用户ID，如果为None则尝试查找任意有效用户
            
        Returns:
            Optional[str]: 钉钉用户ID (如: egiPiSYTK3x5AiE)
        """
        try:
            # 尝试导入数据库依赖
            try:
                from db.database import AsyncSessionLocal
                from models.user import ThirdPartyAccount
                from sqlalchemy import select, and_
                import uuid
            except ImportError as e:
                logger.warning(f"数据库模块导入失败: {e}，无法获取钉钉用户ID")
                return None
            
            async with AsyncSessionLocal() as db:
                
                # 如果提供了系统用户ID，查询指定用户的绑定信息
                if system_user_id:
                    try:
                        user_uuid = uuid.UUID(system_user_id)
                        
                        # 使用与用户管理相同的简单查询逻辑：只查ThirdPartyAccount表
                        stmt = select(ThirdPartyAccount.platform_user_id).where(
                            and_(
                                ThirdPartyAccount.user_id == user_uuid,
                                ThirdPartyAccount.platform == "dingtalk"
                            )
                        )
                        result = await db.execute(stmt)
                        dingtalk_user_id = result.scalar_one_or_none()
                        
                        if dingtalk_user_id:
                            logger.info(f"为用户 {system_user_id} 找到钉钉用户ID: {dingtalk_user_id}")
                            return dingtalk_user_id
                            
                    except (ValueError, TypeError):
                        # 如果不是UUID格式，可能是钉钉用户ID本身
                        if system_user_id and len(system_user_id) > 10:
                            logger.info(f"直接使用传入的钉钉用户ID: {system_user_id}")
                            return system_user_id
                
                # 如果没有找到指定用户或没有提供用户ID，查找任意有效的钉钉绑定用户
                logger.info("查找任意有效的钉钉绑定用户")
                stmt = select(ThirdPartyAccount.platform_user_id).where(
                    and_(
                        ThirdPartyAccount.platform == "dingtalk",
                        ThirdPartyAccount.platform_user_id.isnot(None),
                        ThirdPartyAccount.platform_user_id != ""
                    )
                ).limit(1)
                result = await db.execute(stmt)
                dingtalk_user_id = result.scalar_one_or_none()
                
                if dingtalk_user_id:
                    logger.info(f"找到钉钉绑定用户ID: {dingtalk_user_id}")
                    return dingtalk_user_id
                
                logger.warning("数据库中未找到钉钉绑定用户")
                return None
                
        except Exception as e:
            logger.error(f"获取钉钉用户ID失败: {str(e)}")
            return None
    
    async def _fallback_get_dingtalk_user_from_api(self) -> Optional[str]:
        """
        通过API获取钉钉用户ID（作为数据库查询的回退方案）
        
        Returns:
            Optional[str]: 钉钉用户ID
        """
        try:
            logger.info("尝试通过API获取部门用户")
            users_result = await self.get_department_users_detail(1)  # 根部门
            if users_result and users_result.get("list"):
                users = users_result["list"]
                for user in users:
                    if user.get("active", False):  # 只选择活跃用户
                        userid = user.get("userid")
                        if userid and len(userid) > 10:  # 钉钉用户ID通常比较长
                            logger.info(f"通过API获取到活跃的钉钉用户ID: {userid}")
                            return userid
            
            logger.warning("API中也未找到活跃的钉钉用户")
            return None
        except Exception as e:
            logger.warning(f"通过API获取部门用户失败: {str(e)}")
            return None

    async def get_collaboration_spaces(self, user_id: str, max_results: int = 100) -> Optional[Dict[str, Any]]:
        """
        获取知识库工作空间列表（钉钉官方知识库API）
        
        根据钉钉官方文档：https://open.dingtalk.com/document/orgapp/get-knowledge-base-list
        使用 GET /v2.0/wiki/workspaces 接口
        API权限要求：Wiki.Workspace.Read 或 Wiki.Workspace.Write
        
        Args:
            user_id: 钉钉用户ID（作为operatorId，可为unionId或userId）
            max_results: 返回结果数量上限（最大100）
            
        Returns:
            Dict[str, Any]: 知识库工作空间列表
        """
        try:
            access_token = await self.get_access_token()
            if not access_token:
                return {
                    "success": False,
                    "workspaces": [],
                    "total": 0,
                    "message": "获取访问令牌失败"
                }
            
            # 获取钉钉用户ID用作operatorId
            dingtalk_user_id = await self._get_dingtalk_user_id(user_id)
            if not dingtalk_user_id:
                logger.error("未能获取到钉钉用户ID，operatorId是必填参数")
                return {
                    "success": False,
                    "workspaces": [],
                    "total": 0,
                    "message": "operatorId参数是必填的，但未能获取到有效的钉钉用户ID。请确保已有用户绑定钉钉账号。"
                }
            
            # 根据钉钉官方文档，使用知识库工作空间API
            # https://open.dingtalk.com/document/orgapp/get-knowledge-base-list
            url = "https://api.dingtalk.com/v2.0/wiki/workspaces"
            headers = {
                "x-acs-dingtalk-access-token": access_token,
                "Content-Type": "application/json"
            }
            
            # 构建请求参数
            params = {
                "operatorId": dingtalk_user_id,
                "maxResults": min(max_results, 30),  # 官方文档限制最大30
                "orderBy": "CREATE_TIME_DESC",  # 按创建时间倒序
                "withPermissionRole": True  # 包含权限角色信息
            }
            
            logger.info(f"调用钉钉知识库工作空间API: {url}")
            logger.info(f"请求参数: {params}")
            logger.info(f"请求头: {headers}")
            
            async with httpx.AsyncClient() as client:
                response = await client.get(url, headers=headers, params=params)
                response_text = response.text
                logger.info(f"知识库工作空间API响应状态: {response.status_code}")
                logger.info(f"知识库工作空间API响应内容: {response_text}")
                
                if response.status_code == 200:
                    result = response.json()
                    # 解析知识库工作空间数据
                    workspaces_data = result.get("workspaces", [])
                    total_count = result.get("totalCount", len(workspaces_data))
                    
                    # 转换为标准工作空间格式（根据官方文档返回参数）
                    workspaces = []
                    for workspace in workspaces_data:
                        workspaces.append({
                            "workspaceId": workspace.get("workspaceId", ""),
                            "corpId": workspace.get("corpId", ""),
                            "rootNodeId": workspace.get("rootNodeId", ""),
                            "name": workspace.get("name", ""),
                            "type": workspace.get("type", ""),
                            "description": workspace.get("description", ""),
                            "url": workspace.get("url", ""),
                            "icon": workspace.get("icon", {}),
                            "cover": workspace.get("cover", ""),
                            "creatorId": workspace.get("creatorId", ""),
                            "modifierId": workspace.get("modifierId", ""),
                            "createTime": workspace.get("createTime", ""),  # 官方文档中是createTime
                            "modifiedTime": workspace.get("modifiedTime", ""),  # 官方文档中是modifiedTime
                            "permissionRole": workspace.get("permissionRole", "")
                        })
                    
                    logger.info(f"成功获取知识库工作空间列表，共{total_count}个工作空间")
                    
                    return {
                        "success": True,
                        "workspaces": workspaces,
                        "total": total_count,
                        "nextToken": result.get("nextToken"),
                        "message": "获取知识库工作空间列表成功"
                    }
                elif response.status_code == 404:
                    # 404错误，API端点不存在
                    error_msg = f"知识库工作空间API不存在或已变更: HTTP {response.status_code}, {response_text}"
                    logger.error(error_msg)
                    return {
                        "success": False,
                        "workspaces": [],
                        "total": 0,
                        "message": error_msg
                    }
                elif response.status_code == 403:
                    error_msg = (
                        "钉钉知识库权限不足。请确保已开通以下权限之一：\n"
                        "• Wiki.Workspace.Read - 知识库工作空间读权限\n"
                        "• Wiki.Workspace.Write - 知识库工作空间写权限\n"
                        "权限申请请参考：https://open.dingtalk.com/document/orgapp-server/add-api-permission"
                    )
                    logger.error(f"权限错误: {error_msg}")
                    return {
                        "success": False,
                        "workspaces": [],
                        "total": 0,
                        "message": error_msg
                    }
                else:
                    # 解析错误响应
                    try:
                        error_data = response.json()
                        error_code = error_data.get("code", "UNKNOWN")
                        error_message = error_data.get("message", "未知错误")
                        error_msg = f"钉钉API错误: {error_code} - {error_message}"
                    except:
                        error_msg = f"API请求失败: HTTP {response.status_code}, {response_text}"
                    
                    logger.error(error_msg)
                    return {
                        "success": False,
                        "workspaces": [],
                        "total": 0,
                        "message": error_msg
                    }
        except Exception as e:
            logger.error(f"获取工作空间列表异常: {str(e)}", exc_info=True)
            return {
                "success": False,
                "workspaces": [],
                "total": 0,
                "message": f"获取工作空间列表失败: {str(e)}"
            }
    
    async def get_knowledge_spaces(self, workspace_id: str, max_results: int = 100) -> Optional[Dict[str, Any]]:
        """
        获取知识库列表（新版知识库API）
        
        根据钉钉官方文档，需要使用v2.0/wiki/spaces API
        需要权限：Wiki.Workspace.Read 或 Wiki.Workspace.Write
        
        Args:
            workspace_id: 工作空间ID
            max_results: 返回结果数量上限
            
        Returns:
            Dict[str, Any]: 知识库列表
        """
        try:
            access_token = await self.get_access_token()
            if not access_token:
                return {
                    "success": False,
                    "spaces": [],
                    "total": 0,
                    "message": "获取访问令牌失败"
                }
            
            # 使用新版知识库API获取团队空间列表
            # 注意：根据钉钉API文档，知识库列表可能使用不同的端点
            url = f"https://api.dingtalk.com/v2.0/wiki/spaces"
            headers = {
                "x-acs-dingtalk-access-token": access_token,
                "Content-Type": "application/json"
            }
            
            params = {
                "workspaceId": workspace_id,
                "maxResults": max_results,
                "orderBy": "CREATE_TIME_DESC"
            }
            
            logger.info(f"调用新版知识库API: {url}, 工作空间ID: {workspace_id}")
            logger.info(f"请求参数: {params}")
            
            async with httpx.AsyncClient() as client:
                response = await client.get(url, headers=headers, params=params)
                response_text = response.text
                logger.info(f"知识库API响应状态: {response.status_code}")
                logger.info(f"知识库API响应内容: {response_text}")
                
                if response.status_code == 200:
                    result = response.json()
                    spaces = result.get("spaces", [])
                    return {
                        "success": True,
                        "spaces": spaces,
                        "total": len(spaces),
                        "nextToken": result.get("nextToken"),
                        "message": "获取知识库列表成功"
                    }
                elif response.status_code == 403:
                    error_msg = (
                        "钉钉知识库权限不足。请确保已开通以下权限之一：\n"
                        "• Wiki.Workspace.Read - 知识库工作空间读权限\n"
                        "• Wiki.Workspace.Write - 知识库工作空间写权限\n"
                        "权限申请请参考：https://open.dingtalk.com/document/orgapp-server/add-api-permission"
                    )
                    logger.error(f"权限错误: {error_msg}")
                    return {
                        "success": False,
                        "spaces": [],
                        "total": 0,
                        "message": error_msg
                    }
                else:
                    error_msg = f"API请求失败: HTTP {response.status_code}, {response_text}"
                    logger.error(error_msg)
                    return {
                        "success": False,
                        "spaces": [],
                        "total": 0,
                        "message": error_msg
                    }
        except Exception as e:
            logger.error(f"获取知识库列表异常: {str(e)}")
            return {
                "success": False,
                "spaces": [],
                "total": 0,
                "message": f"获取知识库列表失败: {str(e)}"
            }
    
    async def get_document_list(self, space_id: str, max_results: int = 100, next_token: str = None) -> Optional[Dict[str, Any]]:
        """
        获取文档列表
        
        Args:
            space_id: 知识库ID
            max_results: 返回结果数量上限
            next_token: 分页Token
            
        Returns:
            Dict[str, Any]: 文档列表
        """
        try:
            access_token = await self.get_access_token()
            if not access_token:
                return None
            
            # 使用新版API接口获取文档列表
            url = f"https://api.dingtalk.com/v1.0/doc/dentries"
            headers = {
                "x-acs-dingtalk-access-token": access_token,
                "Content-Type": "application/json"
            }
            
            params = {
                "spaceId": space_id,
                "maxResults": max_results
            }
            
            if next_token:
                params["nextToken"] = next_token
            
            logger.info(f"调用钉钉文档列表API: {url}, space_id={space_id}")
            
            async with httpx.AsyncClient() as client:
                response = await client.get(url, headers=headers, params=params)
                response_text = response.text
                logger.info(f"文档列表API响应状态: {response.status_code}")
                logger.debug(f"文档列表API响应内容: {response_text}")
                
                if response.status_code == 200:
                    result = response.json()
                    return {
                        "success": True,
                        "documents": result.get("dentries", []),
                        "total": len(result.get("dentries", [])),
                        "nextToken": result.get("nextToken"),
                        "hasMore": result.get("hasMore", False),
                        "message": "获取文档列表成功"
                    }
                else:
                    logger.error(f"获取文档列表请求失败: status={response.status_code}, response={response_text}")
                    return {
                        "success": False,
                        "documents": [],
                        "total": 0,
                        "nextToken": None,
                        "hasMore": False,
                        "message": f"API请求失败: {response.status_code}"
                    }
        except Exception as e:
            logger.error(f"获取文档列表异常: {str(e)}")
            return {
                "success": False,
                "documents": [],
                "total": 0,
                "nextToken": None,
                "hasMore": False,
                "message": f"获取文档列表失败: {str(e)}"
            }
    
    async def get_document_content(self, dentry_uuid: str, target_format: str = "markdown") -> Optional[Dict[str, Any]]:
        """
        获取文档内容
        
        Args:
            dentry_uuid: 文档UUID
            target_format: 目标格式（markdown, html等）
            
        Returns:
            Dict[str, Any]: 文档内容
        """
        try:
            access_token = await self.get_access_token()
            if not access_token:
                return None
            
            # 使用新版API接口获取文档内容
            url = f"https://api.dingtalk.com/v1.0/doc/dentries/{dentry_uuid}/contents"
            headers = {
                "x-acs-dingtalk-access-token": access_token,
                "Content-Type": "application/json"
            }
            
            params = {
                "targetFormat": target_format
            }
            
            logger.info(f"调用钉钉文档内容API: {url}, dentry_uuid={dentry_uuid}")
            
            async with httpx.AsyncClient() as client:
                response = await client.get(url, headers=headers, params=params)
                response_text = response.text
                logger.info(f"文档内容API响应状态: {response.status_code}")
                logger.debug(f"文档内容API响应内容: {response_text[:500]}...")  # 只记录前500字符
                
                if response.status_code == 200:
                    result = response.json()
                    return {
                        "success": True,
                        "content": result.get("content", ""),
                        "title": result.get("title", ""),
                        "format": target_format,
                        "updateTime": result.get("updateTime"),
                        "message": "获取文档内容成功"
                    }
                else:
                    logger.error(f"获取文档内容请求失败: status={response.status_code}, response={response_text}")
                    return {
                        "success": False,
                        "content": "",
                        "title": "",
                        "format": target_format,
                        "message": f"API请求失败: {response.status_code}"
                    }
        except Exception as e:
            logger.error(f"获取文档内容异常: {str(e)}")
            return {
                "success": False,
                "content": "",
                "title": "",
                "format": target_format,
                "message": f"获取文档内容失败: {str(e)}"
            }
    
    async def create_document(self, space_id: str, title: str, content: str = "", parent_uuid: str = None) -> Optional[Dict[str, Any]]:
        """
        创建文档
        
        Args:
            space_id: 知识库ID
            title: 文档标题
            content: 文档内容
            parent_uuid: 父文档UUID（可选）
            
        Returns:
            Dict[str, Any]: 创建结果
        """
        try:
            access_token = await self.get_access_token()
            if not access_token:
                return None
            
            # 使用新版API接口创建文档
            url = f"https://api.dingtalk.com/v1.0/doc/dentries"
            headers = {
                "x-acs-dingtalk-access-token": access_token,
                "Content-Type": "application/json"
            }
            
            data = {
                "spaceId": space_id,
                "name": title,
                "content": content,
                "dentryType": "doc"  # 文档类型
            }
            
            if parent_uuid:
                data["parentUuid"] = parent_uuid
            
            logger.info(f"调用钉钉创建文档API: {url}, title={title}")
            
            async with httpx.AsyncClient() as client:
                response = await client.post(url, headers=headers, json=data)
                response_text = response.text
                logger.info(f"创建文档API响应状态: {response.status_code}")
                logger.debug(f"创建文档API响应内容: {response_text}")
                
                if response.status_code == 200:
                    result = response.json()
                    return {
                        "success": True,
                        "dentry_uuid": result.get("dentryUuid"),
                        "title": title,
                        "message": "创建文档成功"
                    }
                else:
                    logger.error(f"创建文档请求失败: status={response.status_code}, response={response_text}")
                    return {
                        "success": False,
                        "message": f"创建文档失败: {response.status_code}"
                    }
        except Exception as e:
            logger.error(f"创建文档异常: {str(e)}")
            return {
                "success": False,
                "message": f"创建文档失败: {str(e)}"
            }
    
    async def update_document_content(self, dentry_uuid: str, content: str) -> Optional[Dict[str, Any]]:
        """
        更新文档内容
        
        Args:
            dentry_uuid: 文档UUID
            content: 新内容
            
        Returns:
            Dict[str, Any]: 更新结果
        """
        try:
            access_token = await self.get_access_token()
            if not access_token:
                return None
            
            # 使用新版API接口更新文档内容
            url = f"https://api.dingtalk.com/v1.0/doc/dentries/{dentry_uuid}/contents"
            headers = {
                "x-acs-dingtalk-access-token": access_token,
                "Content-Type": "application/json"
            }
            
            data = {
                "content": content
            }
            
            logger.info(f"调用钉钉更新文档API: {url}, dentry_uuid={dentry_uuid}")
            
            async with httpx.AsyncClient() as client:
                response = await client.put(url, headers=headers, json=data)
                response_text = response.text
                logger.info(f"更新文档API响应状态: {response.status_code}")
                logger.debug(f"更新文档API响应内容: {response_text}")
                
                if response.status_code == 200:
                    return {
                        "success": True,
                        "message": "更新文档内容成功"
                    }
                else:
                    logger.error(f"更新文档内容请求失败: status={response.status_code}, response={response_text}")
                    return {
                        "success": False,
                        "message": f"更新文档内容失败: {response.status_code}"
                    }
        except Exception as e:
            logger.error(f"更新文档内容异常: {str(e)}")
            return {
                "success": False,
                "message": f"更新文档内容失败: {str(e)}"
            }
    
    async def get_document_permissions(self, dentry_uuid: str) -> Optional[Dict[str, Any]]:
        """
        获取文档权限信息
        
        Args:
            dentry_uuid: 文档UUID
            
        Returns:
            Dict[str, Any]: 权限信息
        """
        try:
            access_token = await self.get_access_token()
            if not access_token:
                return None
            
            # 使用新版API接口获取文档权限
            url = f"https://api.dingtalk.com/v1.0/doc/dentries/{dentry_uuid}/permissions"
            headers = {
                "x-acs-dingtalk-access-token": access_token,
                "Content-Type": "application/json"
            }
            
            logger.info(f"调用钉钉文档权限API: {url}, dentry_uuid={dentry_uuid}")
            
            async with httpx.AsyncClient() as client:
                response = await client.get(url, headers=headers)
                response_text = response.text
                logger.info(f"文档权限API响应状态: {response.status_code}")
                logger.debug(f"文档权限API响应内容: {response_text}")
                
                if response.status_code == 200:
                    result = response.json()
                    return {
                        "success": True,
                        "permissions": result.get("permissions", []),
                        "message": "获取文档权限成功"
                    }
                else:
                    logger.error(f"获取文档权限请求失败: status={response.status_code}, response={response_text}")
                    return {
                        "success": False,
                        "permissions": [],
                        "message": f"获取文档权限失败: {response.status_code}"
                    }
        except Exception as e:
            logger.error(f"获取文档权限异常: {str(e)}")
            return {
                "success": False,
                "permissions": [],
                "message": f"获取文档权限失败: {str(e)}"
            }
    
    # 保留旧方法名以保持向后兼容
    async def get_workspace_list(self, user_id: str, max_results: int = 100) -> Optional[Dict[str, Any]]:
        """兼容性方法，调用新的get_collaboration_spaces"""
        return await self.get_collaboration_spaces(user_id, max_results)
    
    async def get_space_list(self, workspace_id: str, max_results: int = 100) -> Optional[Dict[str, Any]]:
        """兼容性方法，调用新的get_knowledge_spaces"""
        return await self.get_knowledge_spaces(workspace_id, max_results)
    
    async def get_doc_list(self, space_id: str, max_results: int = 100, next_token: str = None) -> Optional[Dict[str, Any]]:
        """兼容性方法，调用新的get_document_list"""
        return await self.get_document_list(space_id, max_results, next_token)
    
    async def get_doc_content(self, doc_id: str, target_format: str = "markdown") -> Optional[Dict[str, Any]]:
        """兼容性方法，调用新的get_document_content"""
        return await self.get_document_content(doc_id, target_format)
    
    async def create_doc(self, space_id: str, title: str, content: str, parent_doc_id: str = None) -> Optional[Dict[str, Any]]:
        """兼容性方法，调用新的create_document"""
        return await self.create_document(space_id, title, content, parent_doc_id)
    
    async def update_doc(self, doc_id: str, title: str = None, content: str = None) -> Optional[Dict[str, Any]]:
        """兼容性方法，调用新的update_document_content"""
        if content:
            return await self.update_document_content(doc_id, content)
        else:
            return {
                "success": True,
                "message": "无需更新（仅标题更新暂不支持）"
            }
    
    async def get_doc_permission(self, doc_id: str) -> Optional[Dict[str, Any]]:
        """兼容性方法，调用新的get_document_permissions"""
        return await self.get_document_permissions(doc_id)
    
    async def set_doc_permission(self, doc_id: str, permission_config: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """权限设置功能（新版API可能暂不支持）"""
        logger.warning("文档权限设置功能在新版API中可能暂不支持")
        return {
            "success": False,
            "message": "文档权限设置功能暂不支持"
        }

    async def get_todo_task(self, task_id: str, user_id: str) -> Optional[Dict[str, Any]]:
        """
        获取钉钉待办任务详情 (使用最新v1.0 API)
        
        Args:
            task_id: 任务ID
            user_id: 用户ID
            
        Returns:
            Dict[str, Any]: 任务详情
        """
        try:
            access_token = await self.get_access_token()
            if not access_token:
                return None
            
            # 使用新版v1.0 API
            url = f"https://api.dingtalk.com/v1.0/todo/users/{user_id}/tasks/{task_id}"
            headers = {
                "x-acs-dingtalk-access-token": access_token,
                "Content-Type": "application/json"
            }
            
            logger.info(f"获取待办任务详情: {url}")
            
            async with httpx.AsyncClient() as client:
                response = await client.get(url, headers=headers)
                
                if response.status_code == 200:
                    result = response.json()
                    logger.info(f"成功获取待办任务详情: {result}")
                    return result
                else:
                    logger.error(f"获取待办任务详情失败: {response.status_code} - {response.text}")
                    return None
        except Exception as e:
            logger.error(f"获取待办任务详情异常: {str(e)}")
            return None

    async def get_workbook_sheets(self, workbook_id: str, operator_id: str = None) -> Optional[Dict[str, Any]]:
        """
        获取工作表列表（表格文档的工作表）
        
        Args:
            workbook_id: 工作簿ID（即nodeId）
            operator_id: 操作员ID
            
        Returns:
            Dict[str, Any]: 工作表列表
        """
        try:
            access_token = await self.get_access_token()
            if not access_token:
                return None
            
            # 获取表格的工作表列表
            url = f"https://api.dingtalk.com/v1.0/doc/workbooks/{workbook_id}/sheets"
            headers = {
                "x-acs-dingtalk-access-token": access_token,
                "Content-Type": "application/json"
            }
            
            params = {}
            if operator_id:
                params["operatorId"] = operator_id
            
            logger.info(f"调用钉钉工作表列表API: {url}, workbook_id={workbook_id}")
            
            async with httpx.AsyncClient() as client:
                response = await client.get(url, headers=headers, params=params)
                response_text = response.text
                logger.info(f"工作表列表API响应状态: {response.status_code}")
                logger.debug(f"工作表列表API响应内容: {response_text}")
                
                if response.status_code == 200:
                    result = response.json()
                    sheets = result.get("sheets", [])
                    
                    # 构建工作表摘要信息
                    sheet_summary = []
                    for sheet in sheets:
                        sheet_info = {
                            "sheetId": sheet.get("sheetId"),
                            "name": sheet.get("name", "未命名工作表"),
                            "rowCount": sheet.get("rowCount", 0),
                            "columnCount": sheet.get("columnCount", 0)
                        }
                        sheet_summary.append(sheet_info)
                    
                    return {
                        "success": True,
                        "sheets": sheet_summary,
                        "total_sheets": len(sheet_summary),
                        "message": "获取工作表列表成功"
                    }
                else:
                    logger.error(f"获取工作表列表请求失败: status={response.status_code}, response={response_text}")
                    return {
                        "success": False,
                        "sheets": [],
                        "message": f"API请求失败: {response.status_code}"
                    }
        except Exception as e:
            logger.error(f"获取工作表列表异常: {str(e)}")
            return {
                "success": False,
                "sheets": [],
                "message": f"获取工作表列表失败: {str(e)}"
            }



    async def get_knowledge_nodes(self, parent_node_id: str, max_results: int = 30, next_token: str = None, 
                                with_permission_role: bool = False, operator_id: str = None) -> Optional[Dict[str, Any]]:
        """
        获取知识库节点列表（严格按照官方文档实现）
        参考文档：https://open.dingtalk.com/document/orgapp/get-node-list
        
        Args:
            parent_node_id: 父节点ID（如：MNDoBb60VLBPraakI1Ywxyyn8lemrZQ3）
            max_results: 最大返回结果数（默认30）
            next_token: 分页Token
            with_permission_role: 是否返回权限角色信息
            operator_id: 操作者ID（钉钉用户ID格式）
            
        Returns:
            Dict[str, Any]: 节点列表
        """
        try:
            access_token = await self.get_access_token()
            if not access_token:
                return {
                    "success": False,
                    "nodes": [],
                    "message": "获取访问令牌失败"
                }
            
            # 如果没有传入operator_id，尝试自动获取
            if not operator_id:
                operator_id = await self._get_dingtalk_user_id("system")
                if not operator_id:
                    logger.warning("未能获取operator_id，API调用可能会失败")
            
            # 使用官方v2.0 Wiki API
            url = "https://api.dingtalk.com/v2.0/wiki/nodes"
            headers = {
                "x-acs-dingtalk-access-token": access_token,
                "Content-Type": "application/json"
            }
            
            # 构建查询参数（严格按照官方文档）
            params = {
                "parentNodeId": parent_node_id,
                "maxResults": max_results,
                "withPermissionRole": with_permission_role
            }
            
            if next_token:
                params["nextToken"] = next_token
                
            # 根据钉钉官方文档，operatorId参数是必填的
            if operator_id:
                params["operatorId"] = operator_id
                logger.info(f"使用operatorId: {operator_id}")
            else:
                logger.error("operatorId是必填参数，但未能获取到有效的钉钉用户ID")
                return {
                    "success": False,
                    "nodes": [],
                    "message": "operatorId参数是必填的，但未能获取到有效的钉钉用户ID。请确保已有用户绑定钉钉账号。"
                }
            
            logger.info(f"调用钉钉知识库节点列表API: {url}")
            logger.info(f"请求参数: {params}")
            
            async with httpx.AsyncClient() as client:
                response = await client.get(url, headers=headers, params=params)
                response_text = response.text
                logger.info(f"节点列表API响应状态: {response.status_code}")
                logger.info(f"节点列表API响应内容: {response_text}")
                
                if response.status_code == 200:
                    result = response.json()
                    nodes = result.get("nodes", [])
                    
                    logger.info(f"成功获取知识库节点列表，共{len(nodes)}个节点")
                    
                    return {
                        "success": True,
                        "nodes": nodes,
                        "nextToken": result.get("nextToken"),
                        "hasMore": result.get("hasMore", False),
                        "message": "获取节点列表成功"
                    }
                elif response.status_code == 400:
                    # 解析错误响应
                    try:
                        error_data = response.json()
                        error_code = error_data.get("code", "UNKNOWN")
                        error_message = error_data.get("message", "未知错误")
                        
                        if "operatorId" in error_message:
                            error_msg = f"operatorId参数错误: {error_message}。请确保使用正确格式的钉钉用户ID"
                        else:
                            error_msg = f"请求参数错误: {error_code} - {error_message}"
                    except:
                        error_msg = f"请求参数错误: {response_text}"
                    
                    logger.error(error_msg)
                    return {
                        "success": False,
                        "nodes": [],
                        "message": error_msg
                    }
                else:
                    # 其他错误响应
                    try:
                        error_data = response.json()
                        error_code = error_data.get("code", "UNKNOWN")
                        error_message = error_data.get("message", "未知错误")
                        error_msg = f"钉钉API错误: {error_code} - {error_message}"
                    except:
                        error_msg = f"API请求失败: HTTP {response.status_code}, {response_text}"
                    
                    logger.error(error_msg)
                    return {
                        "success": False,
                        "nodes": [],
                        "message": error_msg
                    }
                    
        except Exception as e:
            logger.error(f"获取知识库节点列表异常: {str(e)}", exc_info=True)
            return {
                "success": False,
                "nodes": [],
                "message": f"获取节点列表失败: {str(e)}"
            }
    
    def _build_comprehensive_demo_components(self) -> List[Dict[str, Any]]:
        """构建全面展示钉钉支持的表单控件的演示模板"""
        return [
            # 基础输入控件
            {
                "componentType": "TextField",
                "props": {
                    "componentId": "TextField_single_line",
                    "label": "单行文本",
                    "required": True,
                    "placeholder": "请输入单行文本"
                }
            },
            {
                "componentType": "NumberField",
                "props": {
                    "componentId": "NumberField_number",
                    "label": "数字输入",
                    "required": True,
                    "placeholder": "请输入数字",
                    "format": "dn"
                }
            },
            {
                "componentType": "MoneyField",
                "props": {
                    "componentId": "MoneyField_money",
                    "label": "金额输入",
                    "required": True,
                    "placeholder": "请输入金额",
                    "unit": "元"
                }
            },
            # 选择控件
            {
                "componentType": "DDSelectField",
                "props": {
                    "componentId": "DDSelectField_single_select",
                    "label": "单选下拉",
                    "required": True,
                    "options": [
                        {"key": "option1", "value": "选项一"},
                        {"key": "option2", "value": "选项二"},
                        {"key": "option3", "value": "选项三"}
                    ]
                }
            },
            {
                "componentType": "RadioField",
                "props": {
                    "componentId": "RadioField_radio",
                    "label": "单选按钮",
                    "required": True,
                    "options": [
                        {"key": "yes", "value": "是"},
                        {"key": "no", "value": "否"}
                    ]
                }
            },
            {
                "componentType": "CheckboxField",
                "props": {
                    "componentId": "CheckboxField_checkbox",
                    "label": "多选框",
                    "required": False,
                    "options": [
                        {"key": "check1", "value": "复选项1"},
                        {"key": "check2", "value": "复选项2"},
                        {"key": "check3", "value": "复选项3"}
                    ]
                }
            },
            # 联系人控件
            {
                "componentType": "DDContactField",
                "props": {
                    "componentId": "DDContactField_contact",
                    "label": "联系人",
                    "required": False,
                    "multiple": False
                }
            },
            {
                "componentType": "DDDeptField",
                "props": {
                    "componentId": "DDDeptField_department",
                    "label": "部门选择",
                    "required": False,
                    "multiple": False
                }
            }
        ]
    
    def _build_survey_questionnaire_components(self) -> List[Dict[str, Any]]:
        """构建调查问卷模板组件"""
        return [
            {
                "componentType": "TextField",
                "props": {
                    "componentId": "TextField_survey_title",
                    "label": "问卷主题",
                    "required": True,
                    "placeholder": "请输入问卷调查主题"
                }
            },
            {
                "componentType": "RadioField",
                "props": {
                    "componentId": "RadioField_satisfaction",
                    "label": "总体满意度",
                    "required": True,
                    "options": [
                        {"key": "very_satisfied", "value": "非常满意"},
                        {"key": "satisfied", "value": "满意"},
                        {"key": "neutral", "value": "一般"},
                        {"key": "dissatisfied", "value": "不满意"}
                    ]
                }
            },
            {
                "componentType": "NumberField",
                "props": {
                    "componentId": "NumberField_score",
                    "label": "评分（1-10分）",
                    "required": True,
                    "placeholder": "请给出1-10的评分",
                    "format": "dn"
                }
            },
            {
                "componentType": "TextareaField",
                "props": {
                    "componentId": "TextareaField_suggestions",
                    "label": "意见建议",
                    "required": False,
                    "placeholder": "请提出您的宝贵意见和建议"
                }
            }
        ]
    
    def _build_contract_approval_components(self) -> List[Dict[str, Any]]:
        """构建合同审批模板组件"""
        return [
            {
                "componentType": "TextField",
                "props": {
                    "componentId": "TextField_contract_name",
                    "label": "合同名称",
                    "required": True,
                    "placeholder": "请输入合同名称"
                }
            },
            {
                "componentType": "TextField",
                "props": {
                    "componentId": "TextField_partner_company",
                    "label": "合作方名称",
                    "required": True,
                    "placeholder": "请输入合作方公司名称"
                }
            },
            {
                "componentType": "MoneyField",
                "props": {
                    "componentId": "MoneyField_contract_amount",
                    "label": "合同金额",
                    "required": True,
                    "placeholder": "请输入合同总金额",
                    "unit": "元"
                }
            },
            {
                "componentType": "DDDateField",
                "props": {
                    "componentId": "DDDateField_start_date",
                    "label": "合同开始日期",
                    "required": True,
                    "unit": "天",
                    "format": "yyyy-MM-dd",
                    "placeholder": "请选择开始日期"
                }
            }
        ]
    
    def _build_recruitment_approval_components(self) -> List[Dict[str, Any]]:
        """构建招聘需求审批模板组件"""
        return [
            {
                "componentType": "TextField",
                "props": {
                    "componentId": "TextField_position_name",
                    "label": "招聘岗位",
                    "required": True,
                    "placeholder": "请输入招聘岗位名称"
                }
            },
            {
                "componentType": "DDDeptField",
                "props": {
                    "componentId": "DDDeptField_hiring_dept",
                    "label": "用人部门",
                    "required": True,
                    "multiple": False
                }
            },
            {
                "componentType": "NumberField",
                "props": {
                    "componentId": "NumberField_recruit_count",
                    "label": "招聘人数",
                    "required": True,
                    "placeholder": "请输入计划招聘人数",
                    "format": "dn"
                }
            }
        ]
    
    def _build_equipment_maintenance_components(self) -> List[Dict[str, Any]]:
        """构建设备维修申请模板组件"""
        return [
            {
                "componentType": "TextField",
                "props": {
                    "componentId": "TextField_equipment_name",
                    "label": "设备名称",
                    "required": True,
                    "placeholder": "请输入设备名称"
                }
            },
            {
                "componentType": "TextField",
                "props": {
                    "componentId": "TextField_equipment_code",
                    "label": "设备编号",
                    "required": True,
                    "placeholder": "请输入设备编号"
                }
            },
            {
                "componentType": "DDSelectField",
                "props": {
                    "componentId": "DDSelectField_fault_type",
                    "label": "故障类型",
                    "required": True,
                    "options": [
                        {"key": "mechanical", "value": "机械故障"},
                        {"key": "electrical", "value": "电气故障"},
                        {"key": "software", "value": "软件故障"},
                        {"key": "other", "value": "其他"}
                    ]
                }
            }
        ]

    async def get_userid_by_unionid(self, unionid: str) -> Optional[str]:
        """
        根据unionid获取用户的userid
        https://open.dingtalk.com/document/orgapp/query-user-details-by-unionid
        """
        try:
            logger.info(f"Attempting to get userid for unionid: {unionid}")
            access_token = await self.get_access_token()
            if not access_token:
                logger.error("获取企业 access_token 失败")
                return None
            
            url = f"{self.base_url}/topapi/user/getbyunionid"
            params = {"access_token": access_token}
            data = {"unionid": unionid}

            async with httpx.AsyncClient() as client:
                response = await client.post(url, params=params, json=data)
                result = response.json()

            if result and result.get("errcode") == 0:
                user_id = result.get("result", {}).get("userid")
                logger.info(f"Successfully got userid: {user_id} for unionid: {unionid}")
                return user_id
            else:
                logger.error(f"Failed to get userid by unionid {unionid}. Response: {result}")
                return None
        except Exception as e:
            logger.error(f"Exception while getting userid by unionid {unionid}: {e}", exc_info=True)
            return None

    async def get_user_info_by_unionid(self, unionid: str, user_token: Optional[str] = None) -> Optional[Dict]:
        """
        根据unionid获取用户的详细信息（优化版，智能选择API）
        - 如果提供了 user_token，则使用V1.0 API，一次调用即可。
        - 如果未提供 user_token，则回退到旧版API，先获取userid再获取详情。
        """
        if user_token:
            logger.info(f"🚀 检测到 user_token，使用V1.0 API获取 unionid '{unionid}' 的用户详情。")
            return await self._get_user_info_by_unionid_v1(unionid, user_token)
        
        logger.info(f"🐢 未提供 user_token，使用旧版API获取 unionid '{unionid}' 的用户详情。")
        try:
            logger.info(f"开始通过 unionid 获取用户详情 (两步): '{unionid}'")
            userid = await self.get_userid_by_unionid(unionid)
            if not userid:
                logger.warning(f"无法将 unionid '{unionid}' 解析为 userid。")
                return None
            
            logger.info(f"成功将 unionid '{unionid}' 解析为 userid '{userid}', 继续获取详情。")
            return await self.get_user_info_by_userid(userid)
        except Exception as e:
            logger.error(f"通过 unionid '{unionid}' 获取用户详情时发生异常: {e}", exc_info=True)
            return None

    async def _get_user_info_by_unionid_v1(self, unionid: str, user_token: str) -> Optional[Dict]:
        """
        [V1.0 API] 根据unionid获取用户详情，需要用户授权的access_token。
        https://open.dingtalk.com/document/orgapp/query-user-details-by-unionid
        """
        try:
            url = f"{self.new_api_base}/v1.0/contact/users/{unionid}"
            headers = {
                "x-acs-dingtalk-access-token": user_token,
            }
            async with httpx.AsyncClient() as client:
                response = await client.get(url, headers=headers)
                result = response.json()
                if response.status_code == 200:
                    logger.info(f"成功通过V1.0 API获取 unionid '{unionid}' 的用户详情。")
                    return result
                else:
                    logger.error(f"通过V1.0 API获取 unionid '{unionid}' 详情失败: {result}")
                    return None
        except Exception as e:
            logger.error(f"通过V1.0 API获取 unionid '{unionid}' 详情时发生异常: {e}", exc_info=True)
            return None

    async def get_user_info_by_mobile(self, mobile: str) -> Optional[Dict]:
        """
        根据手机号获取用户信息
        """
        try:
            result = await self.post(
                "/topapi/v2/user/getbymobile",
                {"mobile": mobile}
            )
            if result and result.get("errcode") == 0:
                return result.get("result", {})
            else:
                logger.error(f"根据手机号获取用户信息失败: {result}")
                return None
        except Exception as e:
            logger.error(f"根据手机号获取用户信息异常: {str(e)}")
            return None