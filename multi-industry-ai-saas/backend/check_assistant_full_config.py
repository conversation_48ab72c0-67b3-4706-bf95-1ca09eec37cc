import asyncio
import uuid
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker, selectinload
from sqlalchemy import select
import os
import sys

# 确保能正确导入项目模块
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from models.ai.assistant import AIAssistant
from models.ai.model import AIModel
from models.ai.config import AIConfig
from core.config import settings

DATABASE_URL = settings.DATABASE_URL

async def check_full_config():
    """
    连接数据库并完整检查AI助手、模型和配置的关联状态
    """
    engine = create_async_engine(DATABASE_URL, echo=False)
    AsyncSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine, class_=AsyncSession)

    async with AsyncSessionLocal() as db:
        print("--- 1. 正在查找AI助手 '小忆助手' 的完整配置 ---")
        
        # 使用 selectinload 预加载关联数据，避免N+1问题和异步懒加载错误
        stmt = (
            select(AIAssistant)
            .options(
                selectinload(AIAssistant.model),
                selectinload(AIAssistant.config).selectinload(AIConfig.provider),
                selectinload(AIAssistant.config).selectinload(AIConfig.model)
            )
            .where(AIAssistant.name == "小忆助手")
        )
        result = await db.execute(stmt)
        assistant = result.scalar_one_or_none()

        if not assistant:
            print("❌ 错误：在数据库中找不到名为 '小忆助手' 的AI助手。")
            return

        print(f"✅ 找到助手: {assistant.name} (ID: {assistant.id})")

        print("\n--- 2. 检查模型 (Model) 关联 ---")
        if assistant.model_id and assistant.model:
            print(f"  - ✅ 已关联模型ID: {assistant.model_id}")
            print(f"  - ✅ 模型名称: {assistant.model.name}")
            print(f"  - ✅ 模型类型: {assistant.model.model_type}")
        elif assistant.model_id:
            print(f"  - ⚠️ 警告：有关联的模型ID ({assistant.model_id})，但无法加载模型对象。可能ID无效或数据不一致。")
        else:
            print("  - ❌ 错误：助手未关联任何基础模型 (model_id 为空)。")

        print("\n--- 3. 检查配置 (Config) 关联 ---")
        if assistant.config_id and assistant.config:
            config = assistant.config
            
            api_key_status = "已设置" if config.api_key else "未设置或为空"
            is_enabled_status = "✅ 已启用" if config.status == 'active' else "❌ 已禁用"
            
            print(f"  - ✅ 已关联配置ID: {assistant.config_id}")
            print(f"  - 配置名称: {config.name}")
            print(f"  - 来源服务商 (Provider): {config.provider.name if config.provider else '未知'}")
            if config.model:
                print(f"  - 配置指向的模型: {config.model.name}")
            else:
                print("  - ⚠️ 警告：此配置未正确关联到任何模型。")
            print(f"  - 服务商状态: {is_enabled_status}")
            print(f"  - API Key 状态: {api_key_status}")
            
            if config.status != 'active':
                print("  - ⚠️ 警告：此配置当前为 '禁用' 状态。")
            if not config.api_key:
                print("  - ⚠️ 警告：此配置的 'API Key' 为空。")
                
        elif assistant.config_id:
            print(f"  - ⚠️ 警告：有关联的配置ID ({assistant.config_id})，但无法加载配置对象。可能ID无效或数据不一致。")
        else:
            print("  - ❌ 错误：助手未关联任何服务商配置 (config_id 为空)。")

        print("\n--- 4. 最终诊断结论 ---")
        if assistant.config_id and assistant.config and assistant.config.status == 'active' and assistant.config.api_key:
            print("✅ 配置完整！ '小忆助手' 已正确关联到一个已启用的、包含API Key的服务商配置。")
            print("   如果仍然出现 'no available server' 错误，问题可能在于 AIChatService 的内部逻辑，")
            print("   例如它可能错误地忽略了 config_id 而只使用了 model_id 去泛泛地寻找服务商，或者代码中存在bug。")
        else:
            print("❌ 配置不完整！ '小忆助手' 没有正确关联到一个已启用的、包含API Key的服务商配置。")
            print("   这是导致 'no available server' 错误的直接原因。")
            print("\n   **解决方案**: 请前往 'AI 中心 -> AI 助手管理'，编辑 '小忆助手'，")
            print("   确保您不仅选择了基础模型，还必须在下方的 **'高级选项' -> '指定配置'** 中，")
            print("   明确选择一个来自 OpenRouter 的、已经配置好API Key的配置项，然后保存。")

if __name__ == "__main__":
    asyncio.run(check_full_config()) 