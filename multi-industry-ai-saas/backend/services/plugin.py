#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
插件服务模块
提供插件相关的服务功能
"""

import logging
from typing import List, Optional, Union, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import and_, or_
import uuid
from datetime import datetime

from models.plugin import Plugin, TenantPlugin, ProjectPlugin
from models.project import Project

# 配置日志
logger = logging.getLogger(__name__)

class PluginService:
    """插件服务类"""
    
    @staticmethod
    async def get_plugin_by_id(db: AsyncSession, plugin_id: Union[uuid.UUID, str]) -> Optional[Plugin]:
        """根据ID获取插件"""
        result = await db.execute(select(Plugin).where(Plugin.id == plugin_id))
        return result.scalars().first()
    
    @staticmethod
    async def get_plugin_by_code(db: AsyncSession, code: str, project_id: Optional[Union[uuid.UUID, str]] = None) -> Optional[Plugin]:
        """根据代码获取插件"""
        query = select(Plugin).where(Plugin.code == code)
        
        # 如果提供了项目ID，检查插件是否已安装到该项目
        if project_id:
            # 获取项目所属的租户ID
            project_result = await db.execute(select(Project).where(Project.id == project_id))
            project = project_result.scalars().first()
            
            if not project:
                return None
                
            tenant_id = project.tenant_id
            
            # 检查插件是否已安装到该租户
            query = select(Plugin).join(
                TenantPlugin, 
                and_(
                    TenantPlugin.plugin_id == Plugin.id,
                    TenantPlugin.tenant_id == tenant_id,
                    TenantPlugin.status == "active"
                )
            ).where(Plugin.code == code)
            
            # 检查插件是否已安装到该项目
            project_plugin_query = select(ProjectPlugin).where(
                and_(
                    ProjectPlugin.plugin_id == Plugin.id,
                    ProjectPlugin.project_id == project_id,
                    ProjectPlugin.status == "active"
                )
            )
            project_plugin_result = await db.execute(project_plugin_query)
            project_plugin = project_plugin_result.scalars().first()
            
            if not project_plugin:
                # 如果插件未安装到项目，但是系统插件，也允许使用
                query = query.where(Plugin.is_system == True)
        
        result = await db.execute(query)
        return result.scalars().first()
    
    @staticmethod
    async def get_plugins(
        db: AsyncSession, 
        skip: int = 0, 
        limit: int = 100,
        category: Optional[str] = None,
        is_system: Optional[bool] = None,
        is_active: Optional[bool] = None
    ) -> List[Plugin]:
        """获取插件列表"""
        query = select(Plugin)
        
        if category:
            query = query.where(Plugin.category == category)
        
        if is_system is not None:
            query = query.where(Plugin.is_system == is_system)
            
        if is_active is not None:
            query = query.where(Plugin.is_active == is_active)
            
        query = query.offset(skip).limit(limit)
        result = await db.execute(query)
        return result.scalars().all()
    
    @staticmethod
    async def get_tenant_plugins(
        db: AsyncSession, 
        tenant_id: Union[uuid.UUID, str],
        skip: int = 0, 
        limit: int = 100,
        status: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """获取租户已安装的插件列表"""
        query = select(Plugin, TenantPlugin).join(
            TenantPlugin,
            and_(
                TenantPlugin.plugin_id == Plugin.id,
                TenantPlugin.tenant_id == tenant_id
            )
        )
        
        if status:
            query = query.where(TenantPlugin.status == status)
            
        query = query.offset(skip).limit(limit)
        result = await db.execute(query)
        
        plugins = []
        for plugin, tenant_plugin in result:
            plugin_dict = {
                "id": str(plugin.id),
                "code": plugin.code,
                "name": plugin.name,
                "description": plugin.description,
                "version": plugin.version,
                "author": plugin.author,
                "website": plugin.website,
                "icon_url": plugin.icon_url,
                "category": plugin.category,
                "price": plugin.price,
                "billing_cycle": plugin.billing_cycle,
                "is_system": plugin.is_system,
                "is_active": plugin.is_active,
                "requires_subscription": plugin.requires_subscription,
                "min_subscription_plan": plugin.min_subscription_plan,
                "features": plugin.features,
                "created_at": plugin.created_at.isoformat() if plugin.created_at else None,
                "updated_at": plugin.updated_at.isoformat() if plugin.updated_at else None,
                "tenant_plugin": {
                    "id": str(tenant_plugin.id),
                    "status": tenant_plugin.status,
                    "version": tenant_plugin.version,
                    "installed_at": tenant_plugin.installed_at.isoformat() if tenant_plugin.installed_at else None,
                    "settings": tenant_plugin.settings
                }
            }
            plugins.append(plugin_dict)
            
        return plugins
    
    @staticmethod
    async def get_project_plugins(
        db: AsyncSession, 
        project_id: Union[uuid.UUID, str],
        skip: int = 0, 
        limit: int = 100,
        status: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """获取项目已安装的插件列表"""
        # 获取项目所属的租户ID
        project_result = await db.execute(select(Project).where(Project.id == project_id))
        project = project_result.scalars().first()
        
        if not project:
            return []
            
        tenant_id = project.tenant_id
        
        # 查询租户已安装的插件和项目已安装的插件
        query = select(Plugin, TenantPlugin, ProjectPlugin).join(
            TenantPlugin,
            and_(
                TenantPlugin.plugin_id == Plugin.id,
                TenantPlugin.tenant_id == tenant_id,
                TenantPlugin.status == "active"
            )
        ).outerjoin(
            ProjectPlugin,
            and_(
                ProjectPlugin.plugin_id == Plugin.id,
                ProjectPlugin.project_id == project_id
            )
        )
        
        if status:
            query = query.where(or_(
                ProjectPlugin.status == status,
                ProjectPlugin.id == None  # 如果项目未安装，但租户已安装，也返回
            ))
            
        query = query.offset(skip).limit(limit)
        result = await db.execute(query)
        
        plugins = []
        for plugin, tenant_plugin, project_plugin in result:
            plugin_dict = {
                "id": str(plugin.id),
                "code": plugin.code,
                "name": plugin.name,
                "description": plugin.description,
                "version": plugin.version,
                "author": plugin.author,
                "website": plugin.website,
                "icon_url": plugin.icon_url,
                "category": plugin.category,
                "price": plugin.price,
                "billing_cycle": plugin.billing_cycle,
                "is_system": plugin.is_system,
                "is_active": plugin.is_active,
                "requires_subscription": plugin.requires_subscription,
                "min_subscription_plan": plugin.min_subscription_plan,
                "features": plugin.features,
                "created_at": plugin.created_at.isoformat() if plugin.created_at else None,
                "updated_at": plugin.updated_at.isoformat() if plugin.updated_at else None,
                "tenant_plugin": {
                    "id": str(tenant_plugin.id),
                    "status": tenant_plugin.status,
                    "version": tenant_plugin.version,
                    "installed_at": tenant_plugin.installed_at.isoformat() if tenant_plugin.installed_at else None,
                    "settings": tenant_plugin.settings
                }
            }
            
            # 如果项目已安装插件，添加项目插件信息
            if project_plugin:
                plugin_dict["project_plugin"] = {
                    "id": str(project_plugin.id),
                    "status": project_plugin.status,
                    "settings": project_plugin.settings
                }
            else:
                plugin_dict["project_plugin"] = None
                
            plugins.append(plugin_dict)
            
        return plugins
