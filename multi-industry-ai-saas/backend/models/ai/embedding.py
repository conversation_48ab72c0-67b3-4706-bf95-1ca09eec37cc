"""
AI 嵌入记录模型
"""

import uuid
from datetime import datetime
from typing import Optional, Dict, Any, List
from sqlalchemy import Column, String, Text, Integer, Float, Boolean, DateTime, ForeignKey, JSON
from sqlalchemy.dialects.postgresql import UUID, ARRAY
from sqlalchemy.orm import relationship, Mapped, mapped_column
from sqlalchemy.ext.hybrid import hybrid_property

from db.base_class import Base


class AIEmbedding(Base):
    """AI嵌入记录"""
    __tablename__ = "ai_embeddings"

    id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id: Mapped[Optional[uuid.UUID]] = mapped_column(UUID(as_uuid=True), nullable=True)
    project_id: Mapped[Optional[uuid.UUID]] = mapped_column(UUID(as_uuid=True), nullable=True)
    
    # 模型关联（关联到统一的ai_models表）
    model_id: Mapped[uuid.UUID] = mapped_column(UUID(as_uuid=True), ForeignKey("ai_models.id"), nullable=False)
    
    # 输入和输出
    input_text: Mapped[str] = mapped_column(Text, nullable=False, comment="输入文本")
    embedding_vector: Mapped[List[float]] = mapped_column(ARRAY(Float), nullable=False, comment="嵌入向量")
    dimensions: Mapped[int] = mapped_column(Integer, nullable=False, comment="向量维度")
    
    # 元数据
    embedding_metadata: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSON, nullable=True, comment="元数据")
    token_count: Mapped[Optional[int]] = mapped_column(Integer, nullable=True, comment="token数量")
    
    # 时间戳
    created_at: Mapped[datetime] = mapped_column(DateTime, nullable=False, default=datetime.utcnow)
    created_by: Mapped[Optional[uuid.UUID]] = mapped_column(UUID(as_uuid=True), nullable=True)

    # 关系（关联到统一的AIModel）
    model = relationship("AIModel", lazy="select")
    
    def __repr__(self):
        return f"<AIEmbedding {self.id}>"

    @hybrid_property
    def vector_norm(self) -> float:
        """计算向量的模长"""
        import math
        return math.sqrt(sum(x * x for x in self.embedding_vector))

    def cosine_similarity(self, other_vector: List[float]) -> float:
        """计算与另一个向量的余弦相似度"""
        import math
        
        # 计算点积
        dot_product = sum(a * b for a, b in zip(self.embedding_vector, other_vector))
        
        # 计算向量模长
        norm1 = math.sqrt(sum(x * x for x in self.embedding_vector))
        norm2 = math.sqrt(sum(x * x for x in other_vector))
        
        if norm1 == 0 or norm2 == 0:
            return 0.0
            
        return dot_product / (norm1 * norm2) 