#!/usr/bin/env python
# -*- coding: utf-8 -*-

import logging
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form, Query, Body
from sqlalchemy.orm import Session
from typing import List, Optional
from uuid import UUID
from datetime import date

from db.database import get_db
from models.monthly_inventory import MonthlyInventory, MonthlyInventorySummary, MonthlyInventoryUpload
from models.store import Store
from models.project import Project
from models.user import User
from schemas.monthly_inventory import (
    MonthlyInventoryCreate, MonthlyInventoryUpdate, MonthlyInventoryQuery,
    MonthlyInventoryAuditQuery, MonthlyInventoryAudit, MonthlyInventoryBatchAudit,
    MonthlyInventoryStatistics, MonthlyInventoryAuditStatistics,
    MonthlyInventoryResponse, MonthlyInventoryListResponse,
    MonthlyInventoryUploadResponse, MonthlyInventoryBatchCreate,
    MonthlyInventorySummaryResponse, MonthlyInventorySummaryListResponse,
    MonthlyInventoryItemsResponse
)
from services.monthly_inventory import MonthlyInventoryService, MonthlyInventoryAuditService
from api.deps import get_current_user, get_current_project

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/monthly", response_model=MonthlyInventoryListResponse)
async def get_monthly_inventory_list(
    store_id: Optional[UUID] = Query(None, description="门店ID"),
    month: Optional[str] = Query(None, description="盘点月份，格式：YYYY-MM"),
    category: Optional[UUID] = Query(None, description="商品分类ID"),
    status: Optional[str] = Query(None, description="状态"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project)
):
    """获取月度盘点列表"""
    
    service = MonthlyInventoryService(db)
    query = MonthlyInventoryQuery(
        store_id=store_id,
        month=month,
        category=category,
        status=status,
        page=page,
        page_size=page_size
    )
    
    items, total, statistics = await service.get_monthly_inventory_list(current_project.id, query)
    
    # 转换为响应模型
    response_items = []
    for item in items:
        response_item = MonthlyInventoryResponse.from_orm(item)
        # 添加关联数据
        if item.product:
            response_item.product_name = item.product.name
            response_item.product_code = item.product.sku
            response_item.specification = item.product.specification
            if item.product.category:
                response_item.category_name = item.product.category.name
        if item.store:
            response_item.store_name = item.store.name
        # 计算字段
        response_item.difference = item.difference
        response_item.difference_rate = item.difference_rate
        response_items.append(response_item)
    
    return MonthlyInventoryListResponse(
        items=response_items,
        total=total,
        page=page,
        page_size=page_size,
        statistics=statistics
    )


@router.get("/monthly/download-template")
async def download_monthly_inventory_template():
    """下载月度盘点模板"""
    
    try:
        import pandas as pd
        from io import BytesIO
        from fastapi.responses import Response
        
        # 创建模板数据
        template_data = {
            '商品编号': ['P001', 'P002', 'P003'],
            '商品名称': ['苹果', '香蕉', '橙子'], 
            '规格': ['红富士', '进口', '脐橙'],
            '单位': ['公斤', '公斤', '公斤'],
            '分类': ['水果', '水果', '水果'],
            '库存量': [100, 50, 75],
            '实际量': [98, 48, 73],
            '备注': ['正常损耗', '正常损耗', '正常损耗']
        }
        
        # 创建DataFrame
        df = pd.DataFrame(template_data)
        
        # 保存为Excel文件
        output = BytesIO()
        df.to_excel(output, index=False, sheet_name='月度盘点模板')
        
        output.seek(0)
        
        # 返回文件
        return Response(
            content=output.getvalue(),
            media_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            headers={
                'Content-Disposition': 'attachment; filename="monthly_inventory_template.xlsx"'
            }
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"下载月度盘点模板失败: {str(e)}")


@router.get("/monthly/{inventory_id}", response_model=MonthlyInventoryResponse)
async def get_monthly_inventory_detail(
    inventory_id: UUID,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project)
):
    """获取月度盘点详情"""
    
    service = MonthlyInventoryService(db)
    inventory = await service.get_monthly_inventory_by_id(inventory_id, current_project.id)
    
    if not inventory:
        raise HTTPException(status_code=404, detail="盘点记录不存在")
    
    response = MonthlyInventoryResponse.from_orm(inventory)
    # 添加关联数据
    if inventory.product:
        response.product_name = inventory.product.name
        response.product_code = inventory.product.sku
        response.specification = inventory.product.specification
        if inventory.product.category:
            response.category_name = inventory.product.category.name
    if inventory.store:
        response.store_name = inventory.store.name
    # 计算字段
    response.difference = inventory.difference
    response.difference_rate = inventory.difference_rate
    
    return response


@router.post("/monthly", response_model=MonthlyInventoryResponse)
async def create_monthly_inventory(
    data: MonthlyInventoryCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project)
):
    """创建月度盘点记录"""
    
    service = MonthlyInventoryService(db)
    inventory = await service.create_monthly_inventory(
        current_project.id,
        current_project.tenant_id,
        data,
        current_user.id
    )
    
    return MonthlyInventoryResponse.from_orm(inventory)


@router.post("/monthly/batch", response_model=List[MonthlyInventoryResponse])
async def batch_create_monthly_inventory(
    data: MonthlyInventoryBatchCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project)
):
    """批量创建月度盘点记录"""
    
    service = MonthlyInventoryService(db)
    inventories = await service.batch_create_monthly_inventory(
        current_project.id,
        current_project.tenant_id,
        data,
        current_user.id
    )
    
    return [MonthlyInventoryResponse.from_orm(inv) for inv in inventories]


@router.put("/monthly/{inventory_id}", response_model=MonthlyInventoryResponse)
async def update_monthly_inventory(
    inventory_id: UUID,
    data: MonthlyInventoryUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project)
):
    """更新月度盘点记录"""
    
    service = MonthlyInventoryService(db)
    inventory = await service.update_monthly_inventory(
        inventory_id,
        current_project.id,
        data,
        current_user.id
    )
    
    if not inventory:
        raise HTTPException(status_code=404, detail="盘点记录不存在")
    
    return MonthlyInventoryResponse.from_orm(inventory)


@router.delete("/monthly/{inventory_id}")
async def delete_monthly_inventory(
    inventory_id: UUID,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project)
):
    """删除月度盘点记录"""
    
    service = MonthlyInventoryService(db)
    success = service.delete_monthly_inventory(inventory_id, current_project.id)
    
    if not success:
        raise HTTPException(status_code=404, detail="盘点记录不存在")
    
    return {"message": "删除成功"}


@router.post("/monthly/submit-to-finance")
async def submit_monthly_inventory_to_finance(
    store_id: UUID = Form(..., description="门店ID"),
    month: date = Form(..., description="盘点月份"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project)
):
    """提交月度盘点到财务审核"""
    
    service = MonthlyInventoryService(db)
    success = service.submit_to_finance(
        current_project.id,
        store_id,
        month,
        current_user.id
    )
    
    if not success:
        raise HTTPException(status_code=400, detail="提交失败，没有可提交的盘点记录")
    
    return {"message": "提交成功"}


@router.post("/monthly/withdraw-from-finance")
async def withdraw_monthly_inventory_from_finance(
    store_id: UUID = Form(..., description="门店ID"),
    month: date = Form(..., description="盘点月份"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project)
):
    """从财务审核撤回月度盘点"""
    
    service = MonthlyInventoryService(db)
    success = service.withdraw_from_finance(
        current_project.id,
        store_id,
        month,
        current_user.id
    )
    
    if not success:
        raise HTTPException(status_code=400, detail="撤回失败，没有可撤回的盘点记录")
    
    return {"message": "撤回成功"}


@router.post("/monthly/upload", response_model=MonthlyInventoryUploadResponse)
async def upload_monthly_inventory(
    store_id: UUID = Form(..., description="门店ID"),
    month: date = Form(..., description="盘点月份"),
    remark: Optional[str] = Form(None, description="备注"),
    file: UploadFile = File(..., description="Excel文件"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project)
):
    """上传月度盘点Excel文件"""
    
    # 验证文件类型
    if not file.filename.endswith(('.xlsx', '.xls')):
        raise HTTPException(status_code=400, detail="只支持Excel文件格式")
    
    service = MonthlyInventoryService(db)
    upload_record = service.upload_excel(
        current_project.id,
        current_project.tenant_id,
        store_id,
        month,
        file,
        remark,
        current_user.id
    )
    
    response = MonthlyInventoryUploadResponse.from_orm(upload_record)
    # 添加关联数据
    if upload_record.store:
        response.store_name = upload_record.store.name
    
    return response


# ========== 财务审核相关API ==========

@router.get("/audit", response_model=MonthlyInventorySummaryListResponse)
async def get_inventory_audit_list(
    store_id: Optional[UUID] = Query(None, description="门店ID"),
    month: Optional[str] = Query(None, description="盘点月份，格式：YYYY-MM"),
    status: Optional[str] = Query(None, description="状态"),
    start_date: Optional[date] = Query(None, description="开始日期"),
    end_date: Optional[date] = Query(None, description="结束日期"),
    group_by: Optional[str] = Query(None, description="分组方式：store_month"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project)
):
    """获取盘点审核列表（按门店月份汇总）"""
    
    service = MonthlyInventoryAuditService(db)
    query = MonthlyInventoryAuditQuery(
        store_id=store_id,
        month=month,
        status=status,
        start_date=start_date,
        end_date=end_date,
        group_by=group_by,
        page=page,
        page_size=page_size
    )
    
    items, total, statistics = await service.get_audit_list(current_project.id, query)
    
    # 转换为响应模型
    response_items = []
    for item in items:
        response_item = MonthlyInventorySummaryResponse.from_orm(item)
        # 添加关联数据
        if item.store:
            response_item.store_name = item.store.name
        # 计算字段
        response_item.completion_rate = item.completion_rate
        response_item.diff_rate = item.diff_rate
        response_items.append(response_item)
    
    return MonthlyInventorySummaryListResponse(
        items=response_items,
        total=total,
        page=page,
        page_size=page_size,
        statistics=statistics
    )


@router.get("/audit/items", response_model=MonthlyInventoryItemsResponse)
async def get_inventory_audit_items(
    store_id: UUID = Query(..., description="门店ID"),
    month: date = Query(..., description="盘点月份"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页数量"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project)
):
    """获取盘点审核明细"""
    
    service = MonthlyInventoryAuditService(db)
    items = await service.get_audit_items(current_project.id, store_id, month)
    
    # 分页处理
    total = len(items)
    start = (page - 1) * page_size
    end = start + page_size
    paginated_items = items[start:end]
    
    # 转换为响应模型
    response_items = []
    for item in paginated_items:
        response_item = MonthlyInventoryResponse.from_orm(item)
        # 添加关联数据
        if item.product:
            response_item.product_name = item.product.name
            response_item.product_code = item.product.sku
            response_item.specification = item.product.specification
            if item.product.category:
                response_item.category_name = item.product.category.name
        if item.store:
            response_item.store_name = item.store.name
        # 计算字段
        response_item.difference = item.difference
        response_item.difference_rate = item.difference_rate
        response_items.append(response_item)
    
    return MonthlyInventoryItemsResponse(
        items=response_items,
        total=total,
        page=page,
        page_size=page_size
    )


@router.post("/audit/audit-by-store-month")
async def audit_inventory_by_store_month(
    data: MonthlyInventoryAudit,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project)
):
    """按门店月份审核盘点"""
    
    service = MonthlyInventoryAuditService(db)
    success = await service.audit_by_store_month(current_project.id, data, current_user.id)
    
    if not success:
        raise HTTPException(status_code=400, detail="审核失败，没有可审核的盘点记录")
    
    return {"message": "审核完成"}


@router.post("/audit/batch-audit")
async def batch_audit_inventory(
    data: MonthlyInventoryBatchAudit,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project)
):
    """批量审核盘点"""
    
    service = MonthlyInventoryAuditService(db)
    updated_count = await service.batch_audit(current_project.id, data, current_user.id)
    
    if updated_count == 0:
        raise HTTPException(status_code=400, detail="批量审核失败，没有可审核的盘点记录")
    
    return {"message": f"批量审核完成，共处理 {updated_count} 条记录"}


@router.get("/audit/export")
async def export_inventory_audit(
    store_id: Optional[UUID] = Query(None, description="门店ID"),
    month: Optional[str] = Query(None, description="盘点月份，格式：YYYY-MM"),
    status: Optional[str] = Query(None, description="状态"),
    start_date: Optional[date] = Query(None, description="开始日期"),
    end_date: Optional[date] = Query(None, description="结束日期"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project)
):
    """导出盘点审核数据"""
    
    # TODO: 实现导出功能
    raise HTTPException(status_code=501, detail="导出功能待实现")


@router.get("/monthly/export")
async def export_monthly_inventory(
    store_id: Optional[UUID] = Query(None, description="门店ID"),
    month: Optional[str] = Query(None, description="盘点月份，格式：YYYY-MM"),
    category: Optional[UUID] = Query(None, description="商品分类ID"),
    status: Optional[str] = Query(None, description="状态"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project)
):
    """导出月度盘点数据"""
    
    # TODO: 实现导出功能
    raise HTTPException(status_code=501, detail="导出功能待实现")


@router.post("/audit/upload-system-inventory")
async def upload_system_inventory(
    store_id: UUID = Form(..., description="门店ID"),
    month: str = Form(..., description="盘点月份，格式：YYYY-MM"),
    erp_system: str = Form("unknown", description="ERP系统类型"),
    file: UploadFile = File(..., description="Excel文件"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project)
):
    """上传第三方ERP系统库存数据"""
    
    # 验证文件类型
    if not file.filename.endswith(('.xlsx', '.xls')):
        raise HTTPException(status_code=400, detail="只支持Excel文件格式")
    
    try:
        import pandas as pd
        import io
        from datetime import datetime
        
        # 读取Excel文件
        content = await file.read()
        df = pd.read_excel(io.BytesIO(content))
        
        # 验证必需的列
        required_columns = ['商品编号', '系统库存数量']
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            raise HTTPException(
                status_code=400, 
                detail=f"Excel文件缺少必需的列: {', '.join(missing_columns)}"
            )
        
        # 标准化列名映射
        column_mapping = {
            '商品编号': 'product_code',
            '商品名称': 'product_name', 
            '系统库存数量': 'system_quantity',
            '单位': 'unit',
            '规格': 'specification',
            '分类': 'category'
        }
        
        # 重命名列
        df_renamed = df.rename(columns=column_mapping)
        
        # 验证数据
        if df_renamed.empty:
            raise HTTPException(status_code=400, detail="Excel文件没有数据")
        
        # 检查必需字段
        if df_renamed['product_code'].isnull().any():
            raise HTTPException(status_code=400, detail="商品编号不能为空")
        
        if df_renamed['system_quantity'].isnull().any():
            raise HTTPException(status_code=400, detail="系统库存数量不能为空")
        
        # 调用服务层处理
        result = await MonthlyInventoryService.update_system_inventory_batch(
            db, current_project.id, store_id, month, erp_system, df_renamed.to_dict('records')
        )
        
        return {
            "success": True,
            "message": f"系统库存上传完成，成功处理{result['success_count']}条记录，失败{result['error_count']}条",
            "data": {
                "success_count": result['success_count'],
                "error_count": result['error_count'],
                "error_details": result['error_details'][:10],  # 只返回前10个错误
                "total_errors": len(result['error_details'])
            }
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"上传系统库存失败: {str(e)}"
        )


@router.post("/monthly/preview-upload-ai", response_model=dict)
async def preview_monthly_inventory_with_ai(
    file_id: UUID,
    store_id: UUID,
    month: str,
    use_ai: bool = True,
    processing_mode: str = "auto",
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project)
):
    """
    使用AI智能处理月度盘点表预览上传
    
    处理模式说明：
    - auto: 自动选择（推荐）- 优先模板匹配，若模板匹配失败或置信度低则使用AI
    - template_only: 仅模板匹配 - 适用于标准格式表格  
    - ai_only: 仅AI识别 - 适用于复杂或非标准格式
    """
    try:
        # 导入异步数据库支持
        from sqlalchemy.ext.asyncio import AsyncSession
        from db.database import get_async_db
        
        # 转换为异步数据库会话
        # 这里需要特殊处理，因为原有代码使用同步Session
        # 简化起见，我们先使用现有的同步方式，后续可以优化
        
        # 验证门店是否存在
        store = db.query(Store).filter(Store.id == store_id, Store.project_id == current_project.id).first()
        if not store:
            raise HTTPException(status_code=404, detail="门店不存在")
        
        # 验证文件是否存在 - 这里需要调用项目空间服务
        # 暂时简化处理，直接进行AI表格处理
        
        logger.info(f"开始处理月度盘点表AI预览: file_id={file_id}, processing_mode={processing_mode}")
        
        # 构建表格处理请求 - 需要转换为异步方式
        from services.table_processing_service import TableProcessingService
        from schemas.table_processing import TableProcessingRequest
        
        # 月度盘点专用AI提示词
        monthly_inventory_prompt = """
请分析这个月度盘点表格图像，提取其中的数据。

表格结构说明：
- 包含商品基础信息：商品编号、商品名称、规格、单位、分类等
- 包含盘点数据：库存量（系统库存）、实际量（实际盘点数量）、备注等

请按照以下格式返回JSON：
{
    "columns": ["商品编号", "商品名称", "规格", "单位", "分类", "库存量", "实际量", "备注"],
    "data": [
        {
            "商品编号": "P001",
            "商品名称": "苹果", 
            "规格": "红富士",
            "单位": "公斤", 
            "分类": "水果",
            "库存量": 100,
            "实际量": 98,
            "备注": "正常损耗"
        }
    ]
}

要求：
1. 准确识别所有列名，特别是商品编号、商品名称
2. 提取所有商品行的盘点数据
3. 数字保持为数字格式（库存量、实际量等）
4. 空白单元格用合适的默认值填充
5. 确保库存量和实际量的数据准确性
"""
        
        # 构建表格处理请求
        table_request = TableProcessingRequest(
            file_id=file_id,
            processing_mode=processing_mode,
            template_hint="monthly_inventory",
            custom_prompt=monthly_inventory_prompt,
            use_default_vision_model=True,
            vision_model_id=None,
            vision_temperature=0.7,
            enable_validation=True,
            enable_correction=True
        )
        
        # 暂时返回模拟数据，等异步重构完成后再实现实际的AI处理
        # 这里需要实际调用TableProcessingService，但需要异步数据库支持
        
        # 模拟返回数据结构
        mock_data = {
            "success": True,
            "message": "智能解析完成（模拟数据）",
            "data": {
                "total_items": 3,
                "processing_info": {
                    "method": "template_guided_ai",
                    "total_rows": 3,
                    "valid_rows": 3,
                    "error_items": 0,
                    "confidence": 0.9
                },
                "preview_items": [
                    {
                        "商品编号": "P001",
                        "商品名称": "苹果",
                        "规格": "红富士",
                        "单位": "公斤",
                        "分类": "水果",
                        "库存量": 100,
                        "实际量": 98,
                        "备注": "正常损耗"
                    }
                ],
                "file_id": str(file_id)
            }
        }
        
        return mock_data
        
    except Exception as e:
        logger.error(f"月度盘点表预览处理失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"月度盘点表预览处理失败: {str(e)}"
        )


@router.post("/monthly/confirm-import", response_model=dict)
async def confirm_monthly_inventory_import(
    file_id: UUID = Body(...),
    store_id: UUID = Body(...),
    month: str = Body(...),
    remark: Optional[str] = Body(None),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user),
    current_project: Project = Depends(get_current_project)
):
    """确认导入月度盘点AI处理结果"""
    try:
        # 验证门店是否存在
        store = db.query(Store).filter(Store.id == store_id, Store.project_id == current_project.id).first()
        if not store:
            raise HTTPException(status_code=404, detail="门店不存在")
        
        # 这里应该从缓存或临时存储中获取AI处理的结果数据
        # 然后批量创建月度盘点记录
        
        # 暂时返回模拟的导入结果
        mock_result = {
            "success": True,
            "message": "月度盘点数据导入成功",
            "imported_count": 3
        }
        
        logger.info(f"月度盘点导入完成: store_id={store_id}, month={month}, imported_count=3")
        
        return mock_result
        
    except Exception as e:
        logger.error(f"确认导入月度盘点失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"确认导入月度盘点失败: {str(e)}"
        ) 