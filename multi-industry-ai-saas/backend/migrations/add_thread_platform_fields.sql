-- Add new columns to ai_assistant_threads table for multi-platform integration
ALTER TABLE ai_assistant_threads
ADD COLUMN platform VARCHAR(50),
ADD COLUMN conversation_id VARCHAR(255),
ADD COLUMN external_user_id VARCHAR(255),
ADD COLUMN project_id UUID,
ADD COLUMN tenant_id UUID;

-- Add comments to the new columns
COMMENT ON COLUMN ai_assistant_threads.platform IS '平台来源 (e.g., ''dingtalk'', ''wechat'')';
COMMENT ON COLUMN ai_assistant_threads.conversation_id IS '外部平台的会话ID';
COMMENT ON COLUMN ai_assistant_threads.external_user_id IS '外部平台的用户ID';
COMMENT ON COLUMN ai_assistant_threads.project_id IS '项目 ID';
COMMENT ON COLUMN ai_assistant_threads.tenant_id IS '租户 ID';

-- Make user_id nullable
ALTER TABLE ai_assistant_threads ALTER COLUMN user_id DROP NOT NULL;
COMMENT ON COLUMN ai_assistant_threads.user_id IS '内部用户 ID，对于外部平台可以为空';

-- Create indexes for faster lookups
CREATE INDEX IF NOT EXISTS idx_thread_platform ON ai_assistant_threads (platform);
CREATE INDEX IF NOT EXISTS idx_thread_conversation_id ON ai_assistant_threads (conversation_id);
CREATE INDEX IF NOT EXISTS idx_thread_external_user_id ON ai_assistant_threads (external_user_id);

-- Optional: Add foreign key constraints if you want to enforce integrity.
-- Be cautious if you have existing rows with null project_id or tenant_id.
-- ALTER TABLE ai_assistant_threads ADD CONSTRAINT fk_project FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE;
-- ALTER TABLE ai_assistant_threads ADD CONSTRAINT fk_tenant FOREIGN KEY (tenant_id) REFERENCES tenants(id) ON DELETE CASCADE;

SELECT 'Migration to add platform integration fields to ai_assistant_threads completed successfully.'; 