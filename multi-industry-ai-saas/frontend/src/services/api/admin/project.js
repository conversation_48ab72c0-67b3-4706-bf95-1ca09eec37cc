import api from '../httpClient';

/**
 * 项目API
 */
const project = {
  /**
   * 获取项目列表
   * @param {Object} params - 查询参数
   * @returns {Promise} - 项目列表
   */
  getList: async (params = {}) => {
    return api.get('/admin/projects', { params });
  },

  /**
   * 根据租户ID获取项目列表
   * @param {string} tenantId - 租户ID
   * @param {Object} params - 查询参数
   * @returns {Promise} - 项目列表
   */
  getListByTenant: async (tenantId, params = {}) => {
    if (!tenantId) {
      throw new Error('未指定租户ID');
    }
    return api.get('/admin/projects', {
      params: {
        ...params,
        tenant_id: tenantId
      }
    });
  },

  /**
   * 获取项目信息
   * @param {string} id - 项目ID
   * @returns {Promise} - 项目信息
   */
  getInfo: async (id) => {
    if (!id) {
      throw new Error('未指定项目ID');
    }
    return api.get(`/admin/projects/${id}`);
  },

  /**
   * 创建项目
   * @param {Object} data - 项目数据
   * @returns {Promise} - 创建结果
   */
  create: async (data) => {
    return api.post('/admin/projects', data);
  },

  /**
   * 更新项目
   * @param {string} id - 项目ID
   * @param {Object} data - 项目数据
   * @returns {Promise} - 更新结果
   */
  update: async (id, data) => {
    if (!id) {
      throw new Error('未指定项目ID');
    }
    return api.put(`/admin/projects/${id}`, data);
  },

  /**
   * 删除项目
   * @param {string} id - 项目ID
   * @returns {Promise} - 删除结果
   */
  delete: async (id) => {
    if (!id) {
      throw new Error('未指定项目ID');
    }
    return api.delete(`/admin/projects/${id}`);
  },

  /**
   * 获取项目用户列表
   * @param {string} projectId - 项目ID
   * @param {Object} params - 查询参数
   * @returns {Promise} - 用户列表
   */
  getUsers: async (projectId, params = {}) => {
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.get(`/admin/projects/${projectId}/users`, { params });
  },

  /**
   * 添加项目用户
   * @param {string} projectId - 项目ID
   * @param {Object} data - 用户数据
   * @returns {Promise} - 添加结果
   */
  addUser: async (projectId, data) => {
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.post(`/admin/projects/${projectId}/users`, data);
  },

  /**
   * 移除项目用户
   * @param {string} projectId - 项目ID
   * @param {string} userId - 用户ID
   * @returns {Promise} - 移除结果
   */
  removeUser: async (projectId, userId) => {
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    if (!userId) {
      throw new Error('未指定用户ID');
    }
    return api.delete(`/admin/projects/${projectId}/users/${userId}`);
  }
};

export default project;
