#!/usr/bin/env python
# -*- coding: utf-8 -*-

import logging
import uuid
from typing import Optional, List, Dict, Any
from datetime import datetime, date
import io
import uuid
from fastapi import APIRouter, Depends, HTTPException, Query, Path, File, UploadFile, Form
from fastapi.responses import StreamingResponse
from starlette import status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_
from pydantic import parse_obj_as

from db.database import get_db
from models.project import Project
from models.user import User
from models.store import Store
from models.store_operations import DailyExpense, ArrivalConfirmation, ArrivalConfirmationItem
from models.project_user import ProjectUser
from services.finance import FinanceService
from services.notification_service import NotificationService
from services.excel_service import ExcelService
from schemas.finance import (
    FinancialReconciliationCreate,
    FinancialReconciliationUpdate,
    FinancialReconciliationResponse,
    FinancialReconciliationListResponse,
    StoreReconciliationDetailResponse,
    FinancialReconciliationStatisticsResponse
)
from schemas.store_operations import (
    DailyExpenseInDB,
    DailyExpenseListResponse,
    ArrivalConfirmationInDB,
    ArrivalConfirmationListResponse
)
from api.deps import get_current_user, get_current_project, get_current_project_id, get_current_tenant_id

router = APIRouter()

logger = logging.getLogger(__name__)

@router.get("/reconciliation", response_model=FinancialReconciliationListResponse)
async def get_reconciliations(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(10, ge=1, le=100, description="每页数量"),
    store_id: Optional[uuid.UUID] = Query(None, description="门店ID"),
    channel_id: Optional[uuid.UUID] = Query(None, description="渠道ID"),
    reconciliation_type: Optional[str] = Query(None, description="对账类型: store, channel"),
    status: Optional[str] = Query(None, description="状态: normal, warning, error"),
    start_date: Optional[date] = Query(None, description="开始日期"),
    end_date: Optional[date] = Query(None, description="结束日期"),
    sort_by: str = Query("reconciliation_date", description="排序字段"),
    sort_order: str = Query("desc", description="排序方向: asc, desc"),
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取财务对账列表
    """
    try:
        # 转换日期为datetime
        start_datetime = None
        end_datetime = None

        if start_date:
            start_datetime = datetime.combine(start_date, datetime.min.time())

        if end_date:
            end_datetime = datetime.combine(end_date, datetime.max.time())

        # 计算分页参数
        skip = (page - 1) * size

        # 获取对账列表
        reconciliations = await FinanceService.get_reconciliations(
            db=db,
            project_id=project.id,
            skip=skip,
            limit=size,
            store_id=store_id,
            channel_id=channel_id,
            reconciliation_type=reconciliation_type,
            status=status,
            start_date=start_datetime,
            end_date=end_datetime,
            sort_by=sort_by,
            sort_order=sort_order
        )

        # 获取总数
        total = await FinanceService.count_reconciliations(
            db=db,
            project_id=project.id,
            store_id=store_id,
            channel_id=channel_id,
            reconciliation_type=reconciliation_type,
            status=status,
            start_date=start_datetime,
            end_date=end_datetime
        )

        # 计算总页数
        pages = (total + size - 1) // size

        # 构建响应数据
        items = []
        for reconciliation in reconciliations:
            item = FinancialReconciliationResponse.from_orm(reconciliation)

            # 添加关联信息
            if reconciliation.store:
                item.store_name = reconciliation.store.name

            if reconciliation.channel:
                item.channel_name = reconciliation.channel.name

            items.append(item)

        return {
            "items": items,
            "total": total,
            "page": page,
            "size": size,
            "pages": pages
        }
    except Exception as e:
        logger.error(f"获取财务对账列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取财务对账列表失败: {str(e)}"
        )

@router.post("/reconciliation", response_model=FinancialReconciliationResponse, status_code=status.HTTP_201_CREATED)
async def create_reconciliation(
    reconciliation_data: FinancialReconciliationCreate,
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    创建财务对账
    """
    try:
        # 创建财务对账
        reconciliation = await FinanceService.create_reconciliation(
            db=db,
            reconciliation_data=reconciliation_data,
            project_id=project.id
        )

        # 构建响应数据
        response = FinancialReconciliationResponse.from_orm(reconciliation)

        # 添加关联信息
        if reconciliation.store:
            response.store_name = reconciliation.store.name

        if reconciliation.channel:
            response.channel_name = reconciliation.channel.name

        return response
    except Exception as e:
        logger.error(f"创建财务对账失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建财务对账失败: {str(e)}"
        )

@router.get("/reconciliation/statistics", response_model=FinancialReconciliationStatisticsResponse)
async def get_reconciliation_statistics(
    month: str = Query(..., description="月份，格式为YYYY-MM"),
    store_id: Optional[uuid.UUID] = Query(None, description="门店ID"),
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取财务对账统计数据
    """
    try:
        # 获取财务对账统计数据
        statistics = await FinanceService.get_reconciliation_statistics(
            db=db,
            project_id=project.id,
            month=month,
            store_id=store_id
        )

        return parse_obj_as(FinancialReconciliationStatisticsResponse, statistics)
    except Exception as e:
        logger.error(f"获取财务对账统计数据失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取财务对账统计数据失败: {str(e)}"
        )

@router.get("/reconciliation/store-detail/{store_id}", response_model=StoreReconciliationDetailResponse)
async def get_store_reconciliation_detail(
    store_id: uuid.UUID = Path(..., description="门店ID"),
    month: str = Query(..., description="月份，格式为YYYY-MM"),
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取门店对账明细
    """
    try:
        # 获取门店对账明细
        detail = await FinanceService.get_store_reconciliation_detail(
            db=db,
            project_id=project.id,
            store_id=store_id,
            month=month
        )

        if not detail:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="门店对账明细不存在"
            )

        return parse_obj_as(StoreReconciliationDetailResponse, detail)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取门店对账明细失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取门店对账明细失败: {str(e)}"
        )

@router.get("/reconciliation/{reconciliation_id}", response_model=FinancialReconciliationResponse)
async def get_reconciliation(
    reconciliation_id: uuid.UUID = Path(..., description="财务对账ID"),
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取财务对账详情
    """
    try:
        # 获取财务对账
        reconciliation = await FinanceService.get_reconciliation_by_id(
            db=db,
            reconciliation_id=reconciliation_id
        )

        if not reconciliation:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="财务对账不存在"
            )

        # 检查财务对账是否属于当前项目
        if reconciliation.project_id != project.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问此财务对账"
            )

        # 构建响应数据
        response = FinancialReconciliationResponse.from_orm(reconciliation)

        # 添加关联信息
        if reconciliation.store:
            response.store_name = reconciliation.store.name

        if reconciliation.channel:
            response.channel_name = reconciliation.channel.name

        return response
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取财务对账详情失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取财务对账详情失败: {str(e)}"
        )

@router.put("/reconciliation/{reconciliation_id}", response_model=FinancialReconciliationResponse)
async def update_reconciliation(
    reconciliation_data: FinancialReconciliationUpdate,
    reconciliation_id: uuid.UUID = Path(..., description="财务对账ID"),
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    更新财务对账
    """
    try:
        # 获取财务对账
        reconciliation = await FinanceService.get_reconciliation_by_id(
            db=db,
            reconciliation_id=reconciliation_id
        )

        if not reconciliation:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="财务对账不存在"
            )

        # 检查财务对账是否属于当前项目
        if reconciliation.project_id != project.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权修改此财务对账"
            )

        # 更新财务对账
        updated_reconciliation = await FinanceService.update_reconciliation(
            db=db,
            reconciliation_id=reconciliation_id,
            reconciliation_data=reconciliation_data
        )

        # 构建响应数据
        response = FinancialReconciliationResponse.from_orm(updated_reconciliation)

        # 添加关联信息
        if updated_reconciliation.store:
            response.store_name = updated_reconciliation.store.name

        if updated_reconciliation.channel:
            response.channel_name = updated_reconciliation.channel.name

        return response
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新财务对账失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新财务对账失败: {str(e)}"
        )

@router.delete("/reconciliation/{reconciliation_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_reconciliation(
    reconciliation_id: uuid.UUID = Path(..., description="财务对账ID"),
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    删除财务对账
    """
    try:
        # 获取财务对账
        reconciliation = await FinanceService.get_reconciliation_by_id(
            db=db,
            reconciliation_id=reconciliation_id
        )

        if not reconciliation:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="财务对账不存在"
            )

        # 检查财务对账是否属于当前项目
        if reconciliation.project_id != project.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权删除此财务对账"
            )

        # 删除财务对账
        result = await FinanceService.delete_reconciliation(
            db=db,
            reconciliation_id=reconciliation_id
        )

        if not result:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="财务对账不存在"
            )

        return None
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除财务对账失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除财务对账失败: {str(e)}"
        )


# 费用审核相关API
@router.get("/expenses", response_model=DailyExpenseListResponse)
async def get_expenses(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(10, ge=1, le=100, description="每页数量"),
    store_id: Optional[uuid.UUID] = Query(None, description="门店ID"),
    expense_type: Optional[str] = Query(None, description="费用类型: misc, material, electricity, internet, other"),
    status: Optional[str] = Query(None, description="状态: normal, abnormal"),
    start_date: Optional[date] = Query(None, description="开始日期"),
    end_date: Optional[date] = Query(None, description="结束日期"),
    project_id: uuid.UUID = Depends(get_current_project_id),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取费用列表
    """
    try:
        # 转换日期为datetime
        start_datetime = None
        end_datetime = None

        if start_date:
            start_datetime = datetime.combine(start_date, datetime.min.time())

        if end_date:
            end_datetime = datetime.combine(end_date, datetime.max.time())

        # 计算分页偏移量
        skip = (page - 1) * page_size

        # 构建查询条件
        conditions = [DailyExpense.project_id == project_id]

        # 检查用户是否是项目管理员
        result = await db.execute(
            select(ProjectUser).where(
                ProjectUser.user_id == current_user.id,
                ProjectUser.project_id == project_id
            )
        )
        project_user = result.scalars().first()
        is_admin = project_user and project_user.is_admin

        # 添加筛选条件
        if store_id:
            conditions.append(DailyExpense.store_id == store_id)

        if expense_type:
            conditions.append(DailyExpense.expense_type == expense_type)

        if status == 'abnormal':
            conditions.append(DailyExpense.is_abnormal == True)
        elif status == 'normal':
            conditions.append(DailyExpense.is_abnormal == False)

        if start_datetime:
            conditions.append(DailyExpense.expense_date >= start_datetime)

        if end_datetime:
            conditions.append(DailyExpense.expense_date <= end_datetime)

        # 查询费用列表
        query = (
            select(
                DailyExpense,
                Store.name.label("store_name")
            )
            .outerjoin(Store, DailyExpense.store_id == Store.id)
            .where(*conditions)
            .order_by(DailyExpense.expense_date.desc())
            .offset(skip)
            .limit(page_size)
        )

        result = await db.execute(query)
        rows = result.all()

        # 查询总数
        count_query = select(func.count()).select_from(DailyExpense).where(*conditions)
        count_result = await db.execute(count_query)
        total = count_result.scalar_one()

        # 构建响应数据
        items = []
        for row in rows:
            expense = row[0]
            store_name = row[1]

            expense_dict = DailyExpenseInDB.model_validate(expense.__dict__).model_dump()
            expense_dict["store_name"] = store_name

            items.append(expense_dict)

        return {
            "success": True,
            "message": "获取费用列表成功",
            "items": items,
            "total": total,
            "page": page,
            "page_size": page_size,
            "pages": (total + page_size - 1) // page_size
        }
    except Exception as e:
        logger.error(f"获取费用列表失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取费用列表失败: {str(e)}"
        )


@router.get("/expenses-statistics")
async def get_expense_statistics(
    store_id: Optional[uuid.UUID] = Query(None, description="门店ID"),
    start_date: Optional[date] = Query(None, description="开始日期"),
    end_date: Optional[date] = Query(None, description="结束日期"),
    project_id: uuid.UUID = Depends(get_current_project_id),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取费用统计
    """
    try:
        # 转换日期为datetime
        start_datetime = None
        end_datetime = None

        if start_date:
            start_datetime = datetime.combine(start_date, datetime.min.time())

        if end_date:
            end_datetime = datetime.combine(end_date, datetime.max.time())

        # 构建查询条件
        conditions = [DailyExpense.project_id == project_id]

        if store_id:
            conditions.append(DailyExpense.store_id == store_id)

        if start_datetime:
            conditions.append(DailyExpense.expense_date >= start_datetime)

        if end_datetime:
            conditions.append(DailyExpense.expense_date <= end_datetime)

        # 查询总费用
        total_query = select(func.sum(DailyExpense.amount)).where(*conditions)
        total_result = await db.execute(total_query)
        total_amount = total_result.scalar_one() or 0

        # 查询按费用类型分组的费用
        type_query = (
            select(
                DailyExpense.expense_type,
                func.sum(DailyExpense.amount).label("total_amount")
            )
            .where(*conditions)
            .group_by(DailyExpense.expense_type)
        )

        type_result = await db.execute(type_query)
        type_rows = type_result.all()

        # 处理按费用类型分组的数据
        by_type = {}
        for row in type_rows:
            expense_type = row[0]
            amount = float(row[1]) if row[1] else 0
            by_type[expense_type] = amount

        # 查询按月份分组的费用
        month_expr = func.date_trunc('month', DailyExpense.expense_date)
        month_query = (
            select(
                month_expr.label("month"),
                func.sum(DailyExpense.amount).label("total_amount")
            )
            .where(*conditions)
            .group_by(month_expr)
            .order_by(month_expr)
        )

        month_result = await db.execute(month_query)
        month_rows = month_result.all()

        # 处理按月份分组的数据
        by_month = {}
        for row in month_rows:
            month = row[0].strftime('%Y-%m') if row[0] else 'unknown'
            amount = float(row[1]) if row[1] else 0
            by_month[month] = amount

        # 查询按门店分组的费用
        store_query = (
            select(
                DailyExpense.store_id,
                Store.name.label("store_name"),
                func.sum(DailyExpense.amount).label("total_amount")
            )
            .outerjoin(Store, DailyExpense.store_id == Store.id)
            .where(*conditions)
            .group_by(DailyExpense.store_id, Store.name)
            .order_by(func.sum(DailyExpense.amount).desc())
        )

        store_result = await db.execute(store_query)
        store_rows = store_result.all()

        # 处理按门店分组的数据
        by_store = {}
        for row in store_rows:
            store_id = str(row[0]) if row[0] else 'unknown'
            store_name = row[1] or 'unknown'
            amount = float(row[2]) if row[2] else 0
            by_store[store_id] = {
                "name": store_name,
                "amount": amount
            }

        return {
            "success": True,
            "message": "获取费用统计成功",
            "data": {
                "total": float(total_amount),
                "by_type": by_type,
                "by_month": by_month,
                "by_store": by_store
            }
        }
    except Exception as e:
        logger.error(f"获取费用统计失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取费用统计失败: {str(e)}"
        )


@router.put("/expenses/{expense_id}/mark-abnormal")
async def mark_expense_abnormal(
    expense_id: uuid.UUID = Path(..., description="费用ID"),
    data: dict = None,
    project_id: uuid.UUID = Depends(get_current_project_id),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    标记费用异常
    """
    try:
        # 查询费用
        query = select(DailyExpense).where(
            DailyExpense.id == expense_id,
            DailyExpense.project_id == project_id
        )
        result = await db.execute(query)
        expense = result.scalars().first()

        if not expense:
            raise HTTPException(
                status_code=404,
                detail="费用不存在"
            )

        # 更新费用状态
        expense.is_abnormal = True
        expense.abnormal_reason = data.get("abnormal_reason") if data else None
        expense.updated_by = current_user.id
        expense.updated_at = datetime.utcnow()

        await db.commit()
        await db.refresh(expense)

        return {
            "success": True,
            "message": "标记费用异常成功",
            "data": DailyExpenseInDB.model_validate(expense.__dict__).model_dump()
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"标记费用异常失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"标记费用异常失败: {str(e)}"
        )


@router.post("/expenses", status_code=201)
async def create_expense(
    data: dict,
    project_id: uuid.UUID = Depends(get_current_project_id),
    current_user: User = Depends(get_current_user),
    tenant_id: Optional[uuid.UUID] = Depends(get_current_tenant_id),
    db: AsyncSession = Depends(get_db)
):
    """
    新增门店费用
    """
    try:
        # 生成费用编号
        expense_number = f"EXP{datetime.now().strftime('%Y%m%d%H%M%S')}"

        # 创建费用
        expense = DailyExpense(
            id=uuid.uuid4(),
            tenant_id=tenant_id,
            project_id=project_id,
            store_id=data.get("store_id"),
            expense_number=expense_number,
            expense_type=data.get("expense_type"),
            expense_date=datetime.strptime(data.get("expense_date"), "%Y-%m-%d") if isinstance(data.get("expense_date"), str) else data.get("expense_date"),
            amount=float(data.get("amount")),
            description=data.get("description"),
            receipt_url=data.get("receipt_url"),
            has_receipt=bool(data.get("receipt_url")),
            created_by=current_user.id,
            updated_by=current_user.id
        )

        db.add(expense)
        await db.commit()
        await db.refresh(expense)

        # 查询门店名称
        store_query = select(Store).where(Store.id == expense.store_id)
        store_result = await db.execute(store_query)
        store = store_result.scalars().first()
        store_name = store.name if store else None

        # 构建响应数据
        expense_dict = DailyExpenseInDB.model_validate(expense.__dict__).model_dump()
        expense_dict["store_name"] = store_name

        return {
            "success": True,
            "message": "新增费用成功",
            "data": expense_dict
        }
    except Exception as e:
        logger.error(f"新增费用失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"新增费用失败: {str(e)}"
        )


# 到货审核相关API
@router.get("/arrivals", response_model=ArrivalConfirmationListResponse)
async def get_arrivals(
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(10, ge=1, le=100, description="每页数量"),
    store_id: Optional[uuid.UUID] = Query(None, description="门店ID"),
    arrival_type: Optional[str] = Query(None, description="到货类型: purchase, transfer, return, other"),
    status: Optional[str] = Query('confirmed', description="状态: all, pending, confirmed, rejected"),
    start_date: Optional[date] = Query(None, description="开始日期"),
    end_date: Optional[date] = Query(None, description="结束日期"),
    project_id: uuid.UUID = Depends(get_current_project_id),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取到货列表
    """
    try:
        # 转换日期为datetime
        start_datetime = None
        end_datetime = None

        if start_date:
            start_datetime = datetime.combine(start_date, datetime.min.time())

        if end_date:
            end_datetime = datetime.combine(end_date, datetime.max.time())

        # 计算分页偏移量
        skip = (page - 1) * page_size

        # 构建查询条件
        conditions = [ArrivalConfirmation.project_id == project_id]

        # 检查用户是否是项目管理员
        result = await db.execute(
            select(ProjectUser).where(
                ProjectUser.user_id == current_user.id,
                ProjectUser.project_id == project_id
            )
        )
        project_user = result.scalars().first()
        is_admin = project_user and project_user.is_admin

        # 添加筛选条件
        if store_id:
            conditions.append(ArrivalConfirmation.store_id == store_id)

        if arrival_type:
            conditions.append(ArrivalConfirmation.arrival_type == arrival_type)

        if status != 'all':
            conditions.append(ArrivalConfirmation.status == status)

        if start_datetime:
            conditions.append(ArrivalConfirmation.arrival_date >= start_datetime)

        if end_datetime:
            conditions.append(ArrivalConfirmation.arrival_date <= end_datetime)

        # 查询到货列表
        query = (
            select(
                ArrivalConfirmation,
                Store.name.label("store_name")
            )
            .outerjoin(Store, ArrivalConfirmation.store_id == Store.id)
            .where(*conditions)
            .order_by(ArrivalConfirmation.arrival_date.desc())
            .offset(skip)
            .limit(page_size)
        )

        result = await db.execute(query)
        rows = result.all()

        # 查询总数
        count_query = select(func.count()).select_from(ArrivalConfirmation).where(*conditions)
        count_result = await db.execute(count_query)
        total = count_result.scalar_one()

        # 构建响应数据
        items = []
        for row in rows:
            arrival = row[0]
            store_name = row[1]

            arrival_dict = ArrivalConfirmationInDB.model_validate(arrival.__dict__).model_dump()
            arrival_dict["store_name"] = store_name

            items.append(arrival_dict)

        return {
            "success": True,
            "message": "获取到货列表成功",
            "items": items,
            "total": total,
            "page": page,
            "page_size": page_size,
            "pages": (total + page_size - 1) // page_size
        }
    except Exception as e:
        logger.error(f"获取到货列表失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取到货列表失败: {str(e)}"
        )


@router.get("/arrivals-statistics")
async def get_arrival_statistics(
    store_id: Optional[uuid.UUID] = Query(None, description="门店ID"),
    start_date: Optional[date] = Query(None, description="开始日期"),
    end_date: Optional[date] = Query(None, description="结束日期"),
    project_id: uuid.UUID = Depends(get_current_project_id),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取到货统计
    """
    try:
        # 转换日期为datetime
        start_datetime = None
        end_datetime = None

        if start_date:
            start_datetime = datetime.combine(start_date, datetime.min.time())

        if end_date:
            end_datetime = datetime.combine(end_date, datetime.max.time())

        # 构建查询条件
        conditions = [ArrivalConfirmation.project_id == project_id]

        if store_id:
            conditions.append(ArrivalConfirmation.store_id == store_id)

        if start_datetime:
            conditions.append(ArrivalConfirmation.arrival_date >= start_datetime)

        if end_datetime:
            conditions.append(ArrivalConfirmation.arrival_date <= end_datetime)

        # 查询已确认的到货总成本
        confirmed_conditions = conditions.copy()
        confirmed_conditions.append(ArrivalConfirmation.status == 'confirmed')

        total_cost_query = select(func.sum(ArrivalConfirmation.total_amount)).where(*confirmed_conditions)
        total_cost_result = await db.execute(total_cost_query)
        total_cost = total_cost_result.scalar_one() or 0

        # 查询各状态的到货数量
        status_query = (
            select(
                ArrivalConfirmation.status,
                func.count().label("count")
            )
            .where(*conditions)
            .group_by(ArrivalConfirmation.status)
        )

        status_result = await db.execute(status_query)
        status_rows = status_result.all()

        # 处理状态统计
        status_counts = {
            "confirmed": 0,
            "pending": 0,
            "rejected": 0
        }

        for row in status_rows:
            status = row[0]
            count = row[1]
            status_counts[status] = count

        # 查询按门店分组的到货成本
        store_query = (
            select(
                ArrivalConfirmation.store_id,
                Store.name.label("store_name"),
                func.sum(ArrivalConfirmation.total_amount).label("total_cost")
            )
            .outerjoin(Store, ArrivalConfirmation.store_id == Store.id)
            .where(*confirmed_conditions)
            .group_by(ArrivalConfirmation.store_id, Store.name)
            .order_by(func.sum(ArrivalConfirmation.total_amount).desc())
        )

        store_result = await db.execute(store_query)
        store_rows = store_result.all()

        # 处理按门店分组的数据
        by_store = {}
        for row in store_rows:
            store_id = str(row[0]) if row[0] else 'unknown'
            store_name = row[1] or 'unknown'
            cost = float(row[2]) if row[2] else 0
            by_store[store_id] = {
                "name": store_name,
                "cost": cost
            }

        # 查询按日期分组的到货成本
        date_expr = func.date_trunc('day', ArrivalConfirmation.arrival_date)
        date_query = (
            select(
                date_expr.label("date"),
                func.sum(ArrivalConfirmation.total_amount).label("total_cost")
            )
            .where(*confirmed_conditions)
            .group_by(date_expr)
            .order_by(date_expr)
        )

        date_result = await db.execute(date_query)
        date_rows = date_result.all()

        # 处理按日期分组的数据
        by_date = {}
        for row in date_rows:
            date = row[0].strftime('%Y-%m-%d') if row[0] else 'unknown'
            cost = float(row[1]) if row[1] else 0
            by_date[date] = cost

        # 查询按月份分组的到货成本
        month_expr = func.date_trunc('month', ArrivalConfirmation.arrival_date)
        month_query = (
            select(
                month_expr.label("month"),
                func.sum(ArrivalConfirmation.total_amount).label("total_cost")
            )
            .where(*confirmed_conditions)
            .group_by(month_expr)
            .order_by(month_expr)
        )

        month_result = await db.execute(month_query)
        month_rows = month_result.all()

        # 处理按月份分组的数据
        by_month = {}
        for row in month_rows:
            month = row[0].strftime('%Y-%m') if row[0] else 'unknown'
            cost = float(row[1]) if row[1] else 0
            by_month[month] = cost

        return {
            "success": True,
            "message": "获取到货统计成功",
            "data": {
                "total_cost": float(total_cost),
                "confirmed_count": status_counts["confirmed"],
                "pending_count": status_counts["pending"],
                "rejected_count": status_counts["rejected"],
                "by_store": by_store,
                "by_date": by_date,
                "by_month": by_month
            }
        }
    except Exception as e:
        logger.error(f"获取到货统计失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取到货统计失败: {str(e)}"
        )


@router.post("/arrivals/{arrival_id}/remind")
async def send_arrival_reminder(
    arrival_id: uuid.UUID = Path(..., description="到货ID"),
    data: dict = None,
    project_id: uuid.UUID = Depends(get_current_project_id),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    发送到货催办通知
    """
    try:
        # 查询到货
        query = (
            select(
                ArrivalConfirmation,
                Store.name.label("store_name")
            )
            .outerjoin(Store, ArrivalConfirmation.store_id == Store.id)
            .where(
                ArrivalConfirmation.id == arrival_id,
                ArrivalConfirmation.project_id == project_id
            )
        )
        result = await db.execute(query)
        row = result.first()

        if not row:
            raise HTTPException(
                status_code=404,
                detail="到货记录不存在"
            )

        arrival = row[0]
        store_name = row[1]

        # 检查到货状态
        if arrival.status != 'pending':
            raise HTTPException(
                status_code=400,
                detail="只能催办待确认的到货"
            )

        # 发送通知
        message = data.get("message") if data else "请尽快确认到货信息，以便财务部门进行成本核算。"

        notification_data = {
            "title": f"到货确认催办 - {store_name}",
            "content": message,
            "type": "arrival_reminder",
            "target_id": str(arrival_id),
            "target_type": "arrival_confirmation",
            "store_id": str(arrival.store_id) if arrival.store_id else None,
            "priority": "high"
        }

        # 调用通知服务发送通知
        await NotificationService.send_store_notification(
            db=db,
            project_id=project_id,
            store_id=arrival.store_id,
            notification_data=notification_data,
            sender_id=current_user.id
        )

        return {
            "success": True,
            "message": "发送催办通知成功"
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"发送催办通知失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"发送催办通知失败: {str(e)}"
        )


@router.post("/arrivals/batch-remind")
async def batch_send_arrival_reminders(
    project_id: uuid.UUID = Depends(get_current_project_id),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    批量发送到货催办通知
    """
    try:
        # 查询所有待确认的到货
        query = (
            select(
                ArrivalConfirmation,
                Store.name.label("store_name")
            )
            .outerjoin(Store, ArrivalConfirmation.store_id == Store.id)
            .where(
                ArrivalConfirmation.project_id == project_id,
                ArrivalConfirmation.status == 'pending'
            )
        )
        result = await db.execute(query)
        rows = result.all()

        if not rows:
            return {
                "success": True,
                "message": "没有需要催办的到货记录",
                "data": {
                    "count": 0
                }
            }

        # 批量发送通知
        count = 0
        for row in rows:
            arrival = row[0]
            store_name = row[1]

            notification_data = {
                "title": f"到货确认催办 - {store_name}",
                "content": "请尽快确认到货信息，以便财务部门进行成本核算。",
                "type": "arrival_reminder",
                "target_id": str(arrival.id),
                "target_type": "arrival_confirmation",
                "store_id": str(arrival.store_id) if arrival.store_id else None,
                "priority": "high"
            }

            # 调用通知服务发送通知
            await NotificationService.send_store_notification(
                db=db,
                project_id=project_id,
                store_id=arrival.store_id,
                notification_data=notification_data,
                sender_id=current_user.id
            )

            count += 1

        return {
            "success": True,
            "message": f"成功发送{count}条催办通知",
            "data": {
                "count": count
            }
        }
    except Exception as e:
        logger.error(f"批量发送催办通知失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"批量发送催办通知失败: {str(e)}"
        )


@router.get("/expenses/export")
async def export_expenses(
    store_id: Optional[uuid.UUID] = Query(None, description="门店ID"),
    expense_type: Optional[str] = Query(None, description="费用类型: misc, material, electricity, internet, other"),
    status: Optional[str] = Query(None, description="状态: normal, abnormal"),
    start_date: Optional[date] = Query(None, description="开始日期"),
    end_date: Optional[date] = Query(None, description="结束日期"),
    project_id: uuid.UUID = Depends(get_current_project_id),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    导出费用列表为Excel
    """
    try:
        # 转换日期为datetime
        start_datetime = None
        end_datetime = None

        if start_date:
            start_datetime = datetime.combine(start_date, datetime.min.time())

        if end_date:
            end_datetime = datetime.combine(end_date, datetime.max.time())

        # 构建查询条件
        conditions = [DailyExpense.project_id == project_id]

        # 添加筛选条件
        if store_id:
            conditions.append(DailyExpense.store_id == store_id)

        if expense_type:
            conditions.append(DailyExpense.expense_type == expense_type)

        if status == 'abnormal':
            conditions.append(DailyExpense.is_abnormal == True)
        elif status == 'normal':
            conditions.append(DailyExpense.is_abnormal == False)

        if start_datetime:
            conditions.append(DailyExpense.expense_date >= start_datetime)

        if end_datetime:
            conditions.append(DailyExpense.expense_date <= end_datetime)

        # 查询费用列表
        query = (
            select(
                DailyExpense,
                Store.name.label("store_name")
            )
            .outerjoin(Store, DailyExpense.store_id == Store.id)
            .where(*conditions)
            .order_by(DailyExpense.expense_date.desc())
        )

        result = await db.execute(query)
        rows = result.all()

        # 准备Excel数据
        data = []
        headers = ["费用编号", "门店", "费用类型", "费用日期", "金额", "状态", "异常原因", "描述", "创建时间"]

        # 费用类型映射
        type_map = {
            "misc": "杂费",
            "material": "物料费",
            "electricity": "电费",
            "internet": "网费",
            "other": "其他"
        }

        for row in rows:
            expense = row[0]
            store_name = row[1]

            # 格式化数据
            expense_type_text = type_map.get(expense.expense_type, expense.expense_type)
            status_text = "异常" if expense.is_abnormal else "正常"

            data.append([
                expense.expense_number,
                store_name,
                expense_type_text,
                expense.expense_date.strftime("%Y-%m-%d") if expense.expense_date else "",
                float(expense.amount),
                status_text,
                expense.abnormal_reason or "",
                expense.description or "",
                expense.created_at.strftime("%Y-%m-%d %H:%M:%S") if expense.created_at else ""
            ])

        # 生成Excel文件
        excel_file = io.BytesIO()

        # 使用Excel服务生成文件
        ExcelService.generate_excel(excel_file, "费用列表", headers, data)

        # 设置文件指针到开始位置
        excel_file.seek(0)

        # 返回Excel文件
        return StreamingResponse(
            excel_file,
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            headers={"Content-Disposition": f"attachment; filename=expenses_{datetime.now().strftime('%Y%m%d%H%M%S')}.xlsx"}
        )
    except Exception as e:
        logger.error(f"导出费用列表失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"导出费用列表失败: {str(e)}"
        )


@router.get("/arrivals/export")
async def export_arrivals(
    store_id: Optional[uuid.UUID] = Query(None, description="门店ID"),
    arrival_type: Optional[str] = Query(None, description="到货类型: purchase, transfer, return, other"),
    status: Optional[str] = Query('confirmed', description="状态: all, pending, confirmed, rejected"),
    start_date: Optional[date] = Query(None, description="开始日期"),
    end_date: Optional[date] = Query(None, description="结束日期"),
    project_id: uuid.UUID = Depends(get_current_project_id),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    导出到货列表为Excel
    """
    try:
        # 转换日期为datetime
        start_datetime = None
        end_datetime = None

        if start_date:
            start_datetime = datetime.combine(start_date, datetime.min.time())

        if end_date:
            end_datetime = datetime.combine(end_date, datetime.max.time())

        # 构建查询条件
        conditions = [ArrivalConfirmation.project_id == project_id]

        # 添加筛选条件
        if store_id:
            conditions.append(ArrivalConfirmation.store_id == store_id)

        if arrival_type:
            conditions.append(ArrivalConfirmation.arrival_type == arrival_type)

        if status != 'all':
            conditions.append(ArrivalConfirmation.status == status)

        if start_datetime:
            conditions.append(ArrivalConfirmation.arrival_date >= start_datetime)

        if end_datetime:
            conditions.append(ArrivalConfirmation.arrival_date <= end_datetime)

        # 查询到货列表
        query = (
            select(
                ArrivalConfirmation,
                Store.name.label("store_name")
            )
            .outerjoin(Store, ArrivalConfirmation.store_id == Store.id)
            .where(*conditions)
            .order_by(ArrivalConfirmation.arrival_date.desc())
        )

        result = await db.execute(query)
        rows = result.all()

        # 准备Excel数据
        data = []
        headers = ["到货编号", "门店", "到货类型", "到货日期", "总金额", "商品数量", "状态", "备注", "创建时间"]

        # 到货类型映射
        type_map = {
            "purchase": "采购到货",
            "transfer": "调拨到货",
            "return": "退货到货",
            "other": "其他到货"
        }

        # 状态映射
        status_map = {
            "pending": "待确认",
            "confirmed": "已确认",
            "rejected": "已拒绝"
        }

        for row in rows:
            arrival = row[0]
            store_name = row[1]

            # 格式化数据
            arrival_type_text = type_map.get(arrival.arrival_type, arrival.arrival_type)
            status_text = status_map.get(arrival.status, arrival.status)

            data.append([
                arrival.arrival_number,
                store_name,
                arrival_type_text,
                arrival.arrival_date.strftime("%Y-%m-%d") if arrival.arrival_date else "",
                float(arrival.total_amount),
                arrival.total_items,
                status_text,
                arrival.notes or "",
                arrival.created_at.strftime("%Y-%m-%d %H:%M:%S") if arrival.created_at else ""
            ])

        # 生成Excel文件
        excel_file = io.BytesIO()

        # 使用Excel服务生成文件
        ExcelService.generate_excel(excel_file, "到货列表", headers, data)

        # 设置文件指针到开始位置
        excel_file.seek(0)

        # 返回Excel文件
        return StreamingResponse(
            excel_file,
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            headers={"Content-Disposition": f"attachment; filename=arrivals_{datetime.now().strftime('%Y%m%d%H%M%S')}.xlsx"}
        )
    except Exception as e:
        logger.error(f"导出到货列表失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"导出到货列表失败: {str(e)}"
        )

# ========== 财务审核相关API ==========

@router.get("/audit")
async def get_finance_audit_list(
    page: int = Query(1, ge=1, description="页码"),
    size: int = Query(10, ge=1, le=100, description="每页数量"),
    store_id: Optional[uuid.UUID] = Query(None, description="门店ID"),
    month: Optional[str] = Query(None, description="月份，格式为YYYY-MM"),
    status: Optional[str] = Query(None, description="状态"),
    group_by: Optional[str] = Query(None, description="分组方式: store_month"),
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取财务审核列表（盘点审核）
    """
    try:
        # 导入月度盘点服务
        from services.monthly_inventory import MonthlyInventoryAuditService
        from schemas.monthly_inventory import MonthlyInventoryAuditQuery
        
        service = MonthlyInventoryAuditService(db)
        query = MonthlyInventoryAuditQuery(
            page=page,
            size=size,
            store_id=store_id,
            month=month,
            status=status,
            group_by=group_by
        )
        
        items, total, statistics = await service.get_audit_list(project.id, query)
        
        # 计算总页数
        pages = (total + size - 1) // size
        
        return {
            "items": items,
            "total": total,
            "page": page,
            "size": size,
            "pages": pages,
            "statistics": statistics
        }
    except Exception as e:
        logger.error(f"获取财务审核列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取财务审核列表失败: {str(e)}"
        )

@router.get("/audit/items")
async def get_finance_audit_items(
    store_id: uuid.UUID = Query(..., description="门店ID"),
    month: str = Query(..., description="月份，格式为YYYY-MM"),
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取财务审核明细列表
    """
    try:
        from services.monthly_inventory import MonthlyInventoryAuditService
        
        service = MonthlyInventoryAuditService(db)
        items = await service.get_audit_items(project.id, store_id, month)
        
        return {
            "items": items
        }
    except Exception as e:
        logger.error(f"获取财务审核明细失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取财务审核明细失败: {str(e)}"
        )

@router.post("/audit/audit-by-store-month")
async def audit_finance_by_store_month(
    data: dict,
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    按门店月份审核财务数据
    """
    try:
        from services.monthly_inventory import MonthlyInventoryAuditService
        from schemas.monthly_inventory import MonthlyInventoryAudit
        
        service = MonthlyInventoryAuditService(db)
        audit_data = MonthlyInventoryAudit(**data)
        
        success = await service.audit_by_store_month(project.id, audit_data, current_user.id)
        
        if success:
            return {"success": True, "message": "审核成功"}
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="审核失败"
            )
    except Exception as e:
        logger.error(f"财务审核失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"财务审核失败: {str(e)}"
        )

@router.post("/audit/batch-audit")
async def batch_audit_finance(
    data: dict,
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    批量审核财务数据
    """
    try:
        from services.monthly_inventory import MonthlyInventoryAuditService
        from schemas.monthly_inventory import MonthlyInventoryBatchAudit
        
        service = MonthlyInventoryAuditService(db)
        audit_data = MonthlyInventoryBatchAudit(**data)
        
        updated_count = await service.batch_audit(project.id, audit_data, current_user.id)
        
        return {"success": True, "message": f"批量审核成功，共处理{updated_count}条记录"}
    except Exception as e:
        logger.error(f"批量财务审核失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"批量财务审核失败: {str(e)}"
        )

@router.get("/audit/export")
async def export_finance_audit(
    store_id: Optional[uuid.UUID] = Query(None, description="门店ID"),
    month: Optional[str] = Query(None, description="月份"),
    status: Optional[str] = Query(None, description="状态"),
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    导出财务审核数据
    """
    try:
        from services.monthly_inventory import MonthlyInventoryAuditService
        
        service = MonthlyInventoryAuditService(db)
        
        # 获取数据用于导出
        from schemas.monthly_inventory import MonthlyInventoryAuditQuery
        query = MonthlyInventoryAuditQuery(
            store_id=store_id,
            month=month,
            status=status
        )
        
        items, _, _ = await service.get_audit_list(project.id, query)
        
        # 生成Excel文件
        import io
        import pandas as pd
        
        df_data = []
        for item in items:
            df_data.append({
                '门店': item.store_name if hasattr(item, 'store_name') else '',
                '月份': item.month if hasattr(item, 'month') else '',
                '总商品数': item.total_products if hasattr(item, 'total_products') else 0,
                '盘点商品数': item.checked_products if hasattr(item, 'checked_products') else 0,
                '差异商品数': item.diff_products if hasattr(item, 'diff_products') else 0,
                '状态': item.status if hasattr(item, 'status') else '',
                '审核时间': item.audited_at.strftime('%Y-%m-%d %H:%M:%S') if hasattr(item, 'audited_at') and item.audited_at else '',
                '审核备注': item.audit_remark if hasattr(item, 'audit_remark') else ''
            })
        
        df = pd.DataFrame(df_data)
        
        # 生成Excel文件
        excel_buffer = io.BytesIO()
        with pd.ExcelWriter(excel_buffer, engine='xlsxwriter') as writer:
            df.to_excel(writer, sheet_name='财务审核数据', index=False)
        
        excel_buffer.seek(0)
        
        # 生成文件名
        filename = f"财务审核数据_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        
        # 返回文件流
        return StreamingResponse(
            io.BytesIO(excel_buffer.read()),
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )
        
    except Exception as e:
        logger.error(f"导出财务审核数据失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"导出财务审核数据失败: {str(e)}"
        )

@router.post("/audit/upload-system-inventory")
async def upload_system_inventory(
    file: UploadFile = File(...),
    store_id: uuid.UUID = Form(...),
    month: str = Form(...),
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    上传第三方ERP系统库存
    """
    try:
        # 验证文件类型
        if not file.filename.endswith(('.xlsx', '.xls')):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="只支持Excel文件格式"
            )
        
        # 读取文件内容
        file_content = await file.read()
        
        # 这里可以调用monthly_inventory服务来处理上传的系统库存文件
        # 暂时返回成功响应
        return {
            "success": True,
            "message": "系统库存上传成功",
            "filename": file.filename,
            "store_id": str(store_id),
            "month": month
        }
        
    except Exception as e:
        logger.error(f"上传系统库存失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"上传系统库存失败: {str(e)}"
        )
