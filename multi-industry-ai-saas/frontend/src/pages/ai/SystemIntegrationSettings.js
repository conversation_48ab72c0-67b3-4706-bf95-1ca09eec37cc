import React, { useState, useEffect } from 'react';
import {
  Card, Form, Select, Switch, Button, message, 
  Typography, Divider, Space, Row, Col, Spin, Alert, Tooltip
} from 'antd';
import { 
  SettingOutlined, 
  RobotOutlined, 
  EyeOutlined, 
  AudioOutlined, 
  MessageOutlined,
  SaveOutlined
} from '@ant-design/icons';
import { useProject } from '../../contexts/ProjectContext';
import apiService from '../../services/api';

const { Title, Text } = Typography;
const { Option } = Select;

/**
 * 系统AI整合设置组件
 */
const SystemIntegrationSettings = () => {
  const { currentProject } = useProject();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [aiConfigs, setAiConfigs] = useState([]);
  const [config, setConfig] = useState(null);

  // 模型类型配置
  const modelTypes = [
    { key: 'chat', label: '聊天模型', icon: <MessageOutlined />, color: '#1890ff' },
    { key: 'vision', label: '视觉模型', icon: <EyeOutlined />, color: '#52c41a' },
    { key: 'audio', label: '语音模型', icon: <AudioOutlined />, color: '#fa8c16' },
    { key: 'embedding', label: '嵌入模型', icon: <RobotOutlined />, color: '#722ed1' }
  ];

  useEffect(() => {
    if (currentProject?.id) {
      loadData();
    }
  }, [currentProject?.id]);

  const loadData = async () => {
    setLoading(true);
    try {
      // 先加载AI配置列表，确保后续处理有完整数据
      console.log('开始加载AI配置列表...');
      const configsResponse = await apiService.project.ai.aiConfigs.list(currentProject.id);
      
      console.log('AI配置列表响应:', configsResponse);
      
      let currentAiConfigs = [];
      if (configsResponse?.success !== false) {
        // 处理不同的响应格式
        const configData = configsResponse.data || configsResponse || [];
        console.log('处理后的配置数据:', configData);
        currentAiConfigs = Array.isArray(configData) ? configData : [];
        setAiConfigs(currentAiConfigs);
      } else {
        console.warn('AI配置列表加载失败:', configsResponse);
        setAiConfigs([]);
      }

      // 在AI配置加载完成后，再加载系统整合配置
      try {
        console.log('开始加载系统整合配置...');
        const configResponse = await apiService.project.ai.getSystemIntegration(currentProject.id);
        console.log('系统整合配置响应:', configResponse);
        if (configResponse?.success !== false) {
          const configData = configResponse.data || configResponse;
          setConfig(configData);
          
          console.log('原始配置数据:', configData);
          console.log('当前AI配置列表:', currentAiConfigs);
          
          // 将模型ID转换为配置ID用于表单显示
          const formValues = {
            ...configData,
          };
          
          // 通过模型ID查找对应的配置ID
          if (configData.default_chat_model_id) {
            const chatConfig = currentAiConfigs.find(c => c.model_id === configData.default_chat_model_id);
            if (chatConfig) {
              formValues.default_chat_config_id = chatConfig.id;
              console.log(`聊天模型: 模型ID ${configData.default_chat_model_id} -> 配置ID ${chatConfig.id}`);
            } else {
              console.warn('未找到对应聊天模型的配置:', configData.default_chat_model_id);
            }
          }
          
          if (configData.default_vision_model_id) {
            const visionConfig = currentAiConfigs.find(c => c.model_id === configData.default_vision_model_id);
            if (visionConfig) {
              formValues.default_vision_config_id = visionConfig.id;
              console.log(`视觉模型: 模型ID ${configData.default_vision_model_id} -> 配置ID ${visionConfig.id}`);
            } else {
              console.warn('未找到对应视觉模型的配置:', configData.default_vision_model_id);
            }
          }
          
          if (configData.default_audio_model_id) {
            const audioConfig = currentAiConfigs.find(c => c.model_id === configData.default_audio_model_id);
            if (audioConfig) {
              formValues.default_audio_config_id = audioConfig.id;
              console.log(`音频模型: 模型ID ${configData.default_audio_model_id} -> 配置ID ${audioConfig.id}`);
            } else {
              console.warn('未找到对应音频模型的配置:', configData.default_audio_model_id);
            }
          }
          
          if (configData.default_embedding_model_id) {
            const embeddingConfig = currentAiConfigs.find(c => c.model_id === configData.default_embedding_model_id);
            if (embeddingConfig) {
              formValues.default_embedding_config_id = embeddingConfig.id;
              console.log(`嵌入模型: 模型ID ${configData.default_embedding_model_id} -> 配置ID ${embeddingConfig.id}`);
            } else {
              console.warn('未找到对应嵌入模型的配置:', configData.default_embedding_model_id);
            }
          }
          
          console.log('设置表单值:', formValues);
          form.setFieldsValue(formValues);
        }
      } catch (configError) {
        // 如果配置不存在，使用默认值
        console.log('系统整合配置不存在，将创建新配置:', configError);
      }
      
    } catch (error) {
      console.error('加载系统AI整合配置失败:', error);
      message.error('加载配置失败: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async (values) => {
    setSaving(true);
    try {
      console.log('保存前的表单值:', values);
      console.log('当前AI配置列表:', aiConfigs);
      
      // 将前端的AI配置ID转换为对应的模型ID
      const mappedValues = {
        ...values,
        // 删除前端字段名
      };
      
      // 删除前端字段名，避免冲突
      delete mappedValues.default_chat_config_id;
      delete mappedValues.default_vision_config_id;
      delete mappedValues.default_audio_config_id;
      delete mappedValues.default_embedding_config_id;
      
      // 将AI配置ID转换为模型ID
      if (values.default_chat_config_id) {
        const chatConfig = aiConfigs.find(c => c.id === values.default_chat_config_id);
        if (chatConfig && chatConfig.model_id) {
          mappedValues.default_chat_model_id = chatConfig.model_id;
          console.log(`聊天模型: 配置${chatConfig.name} -> 模型ID ${chatConfig.model_id}`);
        } else {
          console.error('未找到聊天配置或模型ID:', values.default_chat_config_id);
          message.error('选择的聊天模型配置无效');
          return;
        }
      }
      
      if (values.default_vision_config_id) {
        const visionConfig = aiConfigs.find(c => c.id === values.default_vision_config_id);
        if (visionConfig && visionConfig.model_id) {
          mappedValues.default_vision_model_id = visionConfig.model_id;
          console.log(`视觉模型: 配置${visionConfig.name} -> 模型ID ${visionConfig.model_id}`);
        } else {
          console.error('未找到视觉配置或模型ID:', values.default_vision_config_id);
          message.error('选择的视觉模型配置无效');
          return;
        }
      }
      
      if (values.default_audio_config_id) {
        const audioConfig = aiConfigs.find(c => c.id === values.default_audio_config_id);
        if (audioConfig && audioConfig.model_id) {
          mappedValues.default_audio_model_id = audioConfig.model_id;
          console.log(`音频模型: 配置${audioConfig.name} -> 模型ID ${audioConfig.model_id}`);
        } else {
          console.error('未找到音频配置或模型ID:', values.default_audio_config_id);
          message.error('选择的音频模型配置无效');
          return;
        }
      }
      
      if (values.default_embedding_config_id) {
        const embeddingConfig = aiConfigs.find(c => c.id === values.default_embedding_config_id);
        if (embeddingConfig && embeddingConfig.model_id) {
          mappedValues.default_embedding_model_id = embeddingConfig.model_id;
          console.log(`嵌入模型: 配置${embeddingConfig.name} -> 模型ID ${embeddingConfig.model_id}`);
        } else {
          console.error('未找到嵌入配置或模型ID:', values.default_embedding_config_id);
          message.error('选择的嵌入模型配置无效');
          return;
        }
      }
      
      console.log('映射后的值:', mappedValues);
      
      let response;
      
      if (config?.id) {
        // 更新现有配置
        response = await apiService.project.ai.updateSystemIntegration(
          currentProject.id, 
          config.id, 
          mappedValues
        );
      } else {
        // 创建新配置
        response = await apiService.project.ai.createSystemIntegration(
          currentProject.id, 
          mappedValues
        );
      }

      if (response?.success !== false) {
        // 提取实际的配置数据
        const configData = response.data || response;
        setConfig(configData);
        message.success('系统AI整合配置保存成功');
        
        // 重新加载数据以确保最新状态
        await loadData();
      } else {
        throw new Error(response?.message || '保存失败');
      }
    } catch (error) {
      console.error('保存系统AI整合配置失败:', error);
      message.error('保存配置失败: ' + error.message);
    } finally {
      setSaving(false);
    }
  };

  const getConfigsByType = (type) => {
    console.log(`筛选${type}类型的配置, 总配置数:`, aiConfigs.length);
    console.log('所有AI配置:', aiConfigs);
    
    const filteredConfigs = aiConfigs.filter(aiConfig => {
      // 首先检查基本结构
      if (!aiConfig) {
        console.log('配置项为空, 跳过');
        return false;
      }
      
      if (!aiConfig.model) {
        console.log('配置项缺少model字段:', aiConfig);
        return false;
      }
      
      const model = aiConfig.model;
      console.log(`检查模型 ${model.name || model.id}:`, model);
      
      // 检查基本状态
      if (aiConfig.status !== 'active') {
        console.log(`配置 ${aiConfig.name} 状态不是active:`, aiConfig.status);
        return false;
      }
      
      if (aiConfig.is_enabled === false) {
        console.log(`配置 ${aiConfig.name} 被禁用`);
        return false;
      }
      
      // 检查API Key是否已配置
      if (!aiConfig.api_key) {
        console.log(`配置 ${aiConfig.name} 未配置API Key`);
        return false;
      }
      
      // 简化模型类型筛选逻辑
      let typeMatch = false;
      const modelType = model.model_type;
      const baseCaps = model.capabilities || {};
      
      // 获取能力覆盖（如果有的话）
      const capabilitiesOverride = aiConfig.config?.capabilities_override || {};
      
      // 应用能力覆盖，构建有效能力
      const effectiveCaps = {
        ...baseCaps,
        ...capabilitiesOverride
      };
      
      switch(type) {
        case 'chat':
          // 聊天模型：模型类型为chat、completion、reasoning，或者支持聊天
          typeMatch = modelType === 'chat' || 
                     modelType === 'completion' || 
                     modelType === 'reasoning' ||
                     effectiveCaps.supports_chat === true;
          break;
          
        case 'vision':
          // 视觉模型：模型类型为vision，或者支持视觉能力（包括覆盖能力）
          typeMatch = modelType === 'vision' || 
                     effectiveCaps.supports_vision === true;
          break;
          
        case 'audio':
          // 音频模型：模型类型为audio，或者支持音频输入/输出（包括覆盖能力）
          typeMatch = modelType === 'audio' || 
                     effectiveCaps.supports_audio_input === true || 
                     effectiveCaps.supports_audio_output === true ||
                     effectiveCaps.supports_speech_to_text === true ||
                     effectiveCaps.supports_text_to_speech === true;
          break;
          
        case 'embedding':
          // 嵌入模型：模型类型为embedding
          typeMatch = modelType === 'embedding';
          break;
          
        default:
          console.warn(`未知的模型类型: ${type}`);
          typeMatch = false;
      }
      
      console.log(`配置 ${aiConfig.name} 的模型 ${model.name} 类型匹配 ${type}: ${typeMatch}`, {
        model_type: modelType,
        base_capabilities: baseCaps,
        capabilities_override: capabilitiesOverride,
        effective_capabilities: effectiveCaps,
        判断规则: type === 'chat' ? '聊天模型' : 
                 type === 'vision' ? '视觉模型' : 
                 type === 'audio' ? '音频模型' : 
                 type === 'embedding' ? '嵌入模型' : '未知类型'
      });
      
      return typeMatch;
    });
    
    console.log(`${type}类型的有效配置数量:`, filteredConfigs.length);
    console.log(`${type}类型的有效配置详情:`, filteredConfigs.map(c => ({
      id: c.id,
      name: c.name,
      model_name: c.model?.name,
      model_type: c.model?.model_type,
      capabilities: c.model?.capabilities
    })));
    
    return filteredConfigs;
  };

  const getConfigName = (configId) => {
    if (!configId) return '未选择';
    
    const aiConfig = aiConfigs.find(c => c.id === configId);
    if (!aiConfig || !aiConfig.model) {
      // 如果找不到配置，显示UUID的前8位作为标识
      return `配置 ${configId.toString().substring(0, 8)}...`;
    }
    
    return `${aiConfig.model.display_name || aiConfig.model.name} (${aiConfig.name})`;
  };

  const getConfigNameByModelId = (modelId) => {
    if (!modelId) return '未选择';
    
    // 首先尝试通过模型ID查找配置
    const aiConfig = aiConfigs.find(c => c.model?.id === modelId);
    if (aiConfig && aiConfig.model) {
      return `${aiConfig.model.display_name || aiConfig.model.name} (${aiConfig.name})`;
    }
    
    // 如果找不到配置，尝试查找模型本身
    const allModels = aiConfigs.map(c => c.model).filter(Boolean);
    const model = allModels.find(m => m.id === modelId);
    if (model) {
      return model.display_name || model.name;
    }
    
    // 如果都找不到，显示模型ID的前8位作为标识
    return `模型 ${modelId.toString().substring(0, 8)}...`;
  };

  // 新增：通过模型ID获取模型名称
  const getModelNameById = (modelId) => {
    if (!modelId) return '未选择';
    
    console.log('查找模型ID:', modelId);
    console.log('可用AI配置:', aiConfigs.map(c => ({
      id: c.id,
      name: c.name,
      model_id: c.model_id,
      model_name: c.model?.name,
      model_display_name: c.model?.display_name
    })));
    
    // 首先尝试通过模型ID查找配置
    const aiConfig = aiConfigs.find(c => c.model?.id === modelId);
    if (aiConfig && aiConfig.model) {
      console.log('找到匹配的配置:', aiConfig);
      return aiConfig.model.display_name || aiConfig.model.name;
    }
    
    // 如果找不到配置，检查是否有直接的model_id匹配
    const configByModelId = aiConfigs.find(c => c.model_id === modelId);
    if (configByModelId) {
      console.log('通过model_id找到配置:', configByModelId);
      if (configByModelId.model) {
        return configByModelId.model.display_name || configByModelId.model.name;
      } else {
        // 如果配置存在但model对象缺失，使用配置名称
        return `${configByModelId.name} (配置)`;
      }
    }
    
    console.warn('未找到模型ID对应的配置:', modelId);
    return `模型 ${modelId.toString().substring(0, 8)}...`;
  };

  // 新增：获取当前配置的显示信息
  const getCurrentConfigInfo = () => {
    if (!config) return {};
    
    const info = {
      chatModel: getModelNameById(config.default_chat_model_id),
      visionModel: getModelNameById(config.default_vision_model_id),
      audioModel: getModelNameById(config.default_audio_model_id),
      embeddingModel: getModelNameById(config.default_embedding_model_id),
    };
    
    console.log('当前配置显示信息:', info);
    console.log('原始配置数据:', config);
    
    return info;
  };

  const renderModelSelector = (type, label, icon, color) => (
    <Card 
      key={type}
      size="small" 
      title={
        <Space>
          <span style={{ color }}>{icon}</span>
          <Text strong>{label}</Text>
        </Space>
      }
      style={{ marginBottom: 16 }}
    >
      <Row gutter={16}>
        <Col span={18}>
          <Form.Item
            name={`default_${type}_config_id`}
            label="默认配置"
            rules={[{ required: false, message: `请选择默认${label}配置` }]}
          >
            <Select
              placeholder={`选择默认${label}配置`}
              allowClear
              showSearch
              optionFilterProp="children"
              notFoundContent={getConfigsByType(type).length === 0 ? "暂无可用配置" : null}
            >
              {getConfigsByType(type).map(aiConfig => (
                <Option key={aiConfig.id} value={aiConfig.id}>
                  <Space direction="vertical" size="small" style={{ width: '100%' }}>
                    <Space>
                      <Text strong>{aiConfig.model.display_name || aiConfig.model.name}</Text>
                      <Text type="secondary">({aiConfig.name})</Text>
                    </Space>
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      提供商: {aiConfig.provider?.display_name || '未知'} | 状态: 
                      <Text type={aiConfig.status === 'active' ? 'success' : 'warning'}>
                        {aiConfig.status === 'active' ? '可用' : '不可用'}
                      </Text>
                    </Text>
                  </Space>
                </Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
        <Col span={6}>
          <Form.Item
            name={`${type}_enabled`}
            label="启用功能"
            valuePropName="checked"
          >
            <Switch checkedChildren="开启" unCheckedChildren="关闭" />
          </Form.Item>
        </Col>
      </Row>
    </Card>
  );

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>
          <Text>加载系统AI整合配置中...</Text>
        </div>
      </div>
    );
  }

  return (
    <div style={{ padding: '0' }}>
      <Alert
        message="系统AI整合设置"
        description={
          <div>
            <p>• <strong>聊天模型</strong>：用于文本对话、内容生成、表格处理等功能</p>
            <p>• <strong>视觉模型</strong>：用于图像识别、采购分拨单AI解析等功能</p>
            <p>• <strong>语音模型</strong>：用于语音识别、文本转语音等功能</p>
            <p>• <strong>嵌入模型</strong>：用于向量化、语义搜索等功能</p>
            <p>• 这里选择的是已配置API Key的AI配置，确保模型可正常使用</p>
            <p>• 模型参数（温度、Token等）将使用对应AI配置中的设置</p>
          </div>
        }
        type="info"
        showIcon
        style={{ marginBottom: 24 }}
      />

      <Form
        form={form}
        layout="vertical"
        onFinish={handleSave}
        initialValues={{
          chat_enabled: true,
          vision_enabled: true,
          audio_enabled: true,
          embedding_enabled: true,
          enable_fallback: true,
          request_timeout: 30,
          max_retries: 3
        }}
      >
        <div style={{ marginBottom: 24 }}>
          <Title level={4}>
            <SettingOutlined /> 默认AI配置选择
          </Title>
          <Divider />
          
          {modelTypes.map(({ key, label, icon, color }) => 
            renderModelSelector(key, label, icon, color)
          )}
        </div>

        <Card title="通用配置" style={{ marginBottom: 24 }}>
          <Row gutter={24}>
            <Col span={8}>
              <Form.Item
                name="enable_fallback"
                label={
                  <Space>
                    <Text>启用模型回退机制</Text>
                    <Tooltip title="当主模型不可用时，自动尝试使用备用模型">
                      <span style={{ color: '#1890ff', cursor: 'help' }}>?</span>
                    </Tooltip>
                  </Space>
                }
                valuePropName="checked"
              >
                <Switch checkedChildren="开启" unCheckedChildren="关闭" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="request_timeout"
                label="请求超时时间(秒)"
              >
                <Select defaultValue={30}>
                  <Option value={15}>15秒</Option>
                  <Option value={30}>30秒</Option>
                  <Option value={60}>60秒</Option>
                  <Option value={120}>120秒</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="max_retries"
                label="最大重试次数"
              >
                <Select defaultValue={3}>
                  <Option value={0}>不重试</Option>
                  <Option value={1}>1次</Option>
                  <Option value={2}>2次</Option>
                  <Option value={3}>3次</Option>
                  <Option value={5}>5次</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
        </Card>

        <div style={{ textAlign: 'center' }}>
          <Space>
            <Button 
              type="primary" 
              htmlType="submit" 
              icon={<SaveOutlined />}
              loading={saving}
              size="large"
            >
              保存配置
            </Button>
            <Button 
              onClick={() => form.resetFields()} 
              disabled={saving}
              size="large"
            >
              重置
            </Button>
          </Space>
        </div>
      </Form>

      {config && (
        <Card 
          title={
            <Space>
              <SettingOutlined />
              <Text strong>当前配置状态</Text>
            </Space>
          }
          style={{ marginTop: 24 }}
          type="inner"
        >
          <Row gutter={16}>
            {modelTypes.map(({ key, label, icon, color }) => {
              const modelId = config[`default_${key}_model_id`];
              const isEnabled = config[`${key}_enabled`];
              const modelName = getModelNameById(modelId);
              
              return (
                <Col span={6} key={`status-${key}`}>
                  <div style={{ textAlign: 'center', padding: 16 }}>
                    <div style={{ marginBottom: 8 }}>
                      <span style={{ color, fontSize: '18px' }}>{icon}</span>
                    </div>
                    <Text strong>{label}</Text>
                    <div style={{ marginTop: 8 }}>
                      <Text type={isEnabled ? 'success' : 'secondary'}>
                        {isEnabled ? '已启用' : '已禁用'}
                      </Text>
                    </div>
                    {modelId && (
                      <div style={{ marginTop: 4 }}>
                        <Text type="secondary" style={{ fontSize: 12 }}>
                          {modelName}
                        </Text>
                      </div>
                    )}
                  </div>
                </Col>
              );
            })}
          </Row>
          
          <Divider />
          
          <Row gutter={16}>
            <Col span={8}>
              <div style={{ textAlign: 'center' }}>
                <Text strong>回退机制</Text>
                <div style={{ marginTop: 4 }}>
                  <Text type={config.enable_fallback ? 'success' : 'secondary'}>
                    {config.enable_fallback ? '已启用' : '已禁用'}
                  </Text>
                </div>
              </div>
            </Col>
            <Col span={8}>
              <div style={{ textAlign: 'center' }}>
                <Text strong>超时时间</Text>
                <div style={{ marginTop: 4 }}>
                  <Text>{config.request_timeout || 30}秒</Text>
                </div>
              </div>
            </Col>
            <Col span={8}>
              <div style={{ textAlign: 'center' }}>
                <Text strong>最大重试</Text>
                <div style={{ marginTop: 4 }}>
                  <Text>{config.max_retries || 3}次</Text>
                </div>
              </div>
            </Col>
          </Row>
        </Card>
      )}
    </div>
  );
};

export default SystemIntegrationSettings; 