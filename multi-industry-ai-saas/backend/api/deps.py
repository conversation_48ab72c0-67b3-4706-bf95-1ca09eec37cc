from fastapi import Depends, HTTPException, Request, Header, Path, Query
from typing import Any, Optional, AsyncGenerator
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import configure_mappers
from contextlib import asynccontextmanager
import jwt
from datetime import datetime, timedelta
import uuid
from sqlalchemy.future import select

from db.database import AsyncSessionLocal
from core.config import settings
from models.user import User
from models.tenant import Tenant
from models.core import ServiceProvider
from models.project import Project
import models  # 导入所有模型，确保它们都被注册
from core.auth import get_current_user

@asynccontextmanager
async def get_db_context():
    """使用异步上下文管理器获取数据库会话"""
    # 导入所有模型，确保它们都被注册
    import models

    # 配置映射器
    configure_mappers()

    session = AsyncSessionLocal()
    try:
        yield session
    finally:
        await session.close()

async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """获取数据库会话的依赖函数"""
    async with get_db_context() as session:
        yield session

async def get_current_project(
    project_id: str = Path(..., description="项目ID")
) -> Project:
    """
    获取当前项目
    直接从路径参数获取项目ID，并返回项目信息
    """
    # 检查 project_id 是否为 "null" 字符串
    if project_id == "null":
        raise HTTPException(
            status_code=400,
            detail="无效的项目ID: 'null'"
        )

    # 尝试将 project_id 转换为 UUID
    try:
        project_id_uuid = uuid.UUID(project_id)
    except ValueError:
        raise HTTPException(
            status_code=400,
            detail=f"无效的项目ID格式: {project_id}"
        )

    # 从数据库获取项目信息
    async with AsyncSessionLocal() as db:
        result = await db.execute(select(Project).where(Project.id == project_id_uuid))
        project = result.scalars().first()
        if not project:
            raise HTTPException(
                status_code=404,
                detail="项目不存在"
            )
        return project

async def get_current_tenant(
    current_user: User = Depends(get_current_user)
) -> Tenant:
    """
    获取当前租户
    从当前用户中获取租户ID，并返回租户信息
    """
    if not current_user.tenant_id:
        raise HTTPException(
            status_code=400,
            detail="用户未关联租户"
        )

    # 从数据库获取租户信息
    async with AsyncSessionLocal() as db:
        # 这里需要实现从数据库获取租户的逻辑
        # 示例: tenant = await db.get(Tenant, current_user.tenant_id)
        # 暂时返回模拟数据

        # 处理 tenant_id，确保它是 UUID 对象
        tenant_id = current_user.tenant_id
        # 如果 tenant_id 是字符串，则转换为 UUID
        if isinstance(tenant_id, str):
            tenant_id = uuid.UUID(tenant_id)

        tenant = Tenant(
            id=tenant_id,
            name="示例租户",
            status="active"
        )

        if not tenant:
            raise HTTPException(
                status_code=404,
                detail="租户不存在"
            )

        return tenant

async def get_current_service_provider(
    current_user: User = Depends(get_current_user)
) -> ServiceProvider:
    """
    获取当前服务商
    从当前用户中获取服务商ID，并返回服务商信息
    """
    if not current_user.service_provider_id:
        raise HTTPException(
            status_code=400,
            detail="用户未关联服务商"
        )

    # 从数据库获取服务商信息
    async with AsyncSessionLocal() as db:
        # 这里需要实现从数据库获取服务商的逻辑
        # 示例: service_provider = await db.get(ServiceProvider, current_user.service_provider_id)
        # 暂时返回模拟数据
        service_provider = ServiceProvider(
            id=uuid.UUID(current_user.service_provider_id),
            name="示例服务商",
            status="active"
        )

        if not service_provider:
            raise HTTPException(
                status_code=404,
                detail="服务商不存在"
            )

        return service_provider

# 权限检查依赖
def check_permission(required_permission: str):
    """
    检查用户是否具有指定权限
    """
    async def dependency(current_user: User = Depends(get_current_user)):
        # 系统管理员拥有所有权限
        if current_user.is_system_admin:
            return True

        # 这里需要实现权限检查逻辑
        # 示例: 从数据库中获取用户角色和权限
        has_permission = False  # 默认没有权限

        if not has_permission:
            raise HTTPException(
                status_code=403,
                detail=f"没有权限执行此操作: {required_permission}"
            )

        return True

    return dependency

async def get_current_project_id(
    project_id: uuid.UUID = Path(..., description="项目ID")
) -> uuid.UUID:
    """
    作为依赖项直接返回路径参数中的 project_id，类型为 uuid.UUID
    """
    return project_id

def get_current_tenant_id(tenant_id: Optional[uuid.UUID] = Query(None, description="租户ID")) -> Optional[uuid.UUID]:
    """
    获取当前租户ID，若未提供则返回 None。
    """
    return tenant_id