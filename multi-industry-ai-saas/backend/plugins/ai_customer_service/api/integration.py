#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
AI客服系统整合 API
"""

import logging
import uuid
from typing import Dict, Any, Optional, List
from datetime import datetime
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel

from api.deps import get_db
from core.auth import get_current_user
from api.deps import get_current_project
from models.user import User
from models.project import Project
from ..services.customer_service import get_customer_service_manager

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter()

# 请求和响应模型
class IntegrationSettings(BaseModel):
    ai_model_id: Optional[str] = None
    vision_model_id: Optional[str] = None
    audio_model_id: Optional[str] = None
    embedding_model_id: Optional[str] = None
    knowledge_base_ids: List[str] = []
    temperature: float = 0.7
    vision_temperature: Optional[float] = 0.7
    max_tokens: int = 2000
    confidence_threshold: float = 0.8
    enable_context_memory: bool = True
    enable_knowledge_search: bool = True
    fallback_to_human: bool = True
    enable_mcp_tools: bool = True
    mcp_server_ids: List[str] = []
    mcp_tool_ids: List[str] = []

class IntegrationSettingsResponse(BaseModel):
    ai_model_id: Optional[str]
    vision_model_id: Optional[str] = None
    audio_model_id: Optional[str] = None
    embedding_model_id: Optional[str] = None
    knowledge_base_ids: List[str]
    temperature: float
    vision_temperature: Optional[float] = None
    max_tokens: int
    confidence_threshold: float
    enable_context_memory: bool
    enable_knowledge_search: bool
    fallback_to_human: bool
    enable_mcp_tools: bool = True
    mcp_server_ids: List[str] = []
    mcp_tool_ids: List[str] = []
    created_at: datetime
    updated_at: datetime

@router.get(
    "/settings",
    response_model=IntegrationSettingsResponse,
    summary="获取系统整合设置",
    description="获取AI客服的系统整合配置"
)
async def get_integration_settings(
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """获取系统整合设置"""
    try:
        # 获取客服管理器实例
        cs_manager = get_customer_service_manager()
        
        # 获取整合设置
        settings = await cs_manager.get_integration_settings(
            project_id=project.id,
            db=db
        )
        
        return IntegrationSettingsResponse(
            ai_model_id=settings.get("ai_model_id"),
            vision_model_id=settings.get("vision_model_id"),
            audio_model_id=settings.get("audio_model_id"),
            embedding_model_id=settings.get("embedding_model_id"),
            knowledge_base_ids=settings.get("knowledge_base_ids", []),
            temperature=settings.get("temperature", 0.7),
            vision_temperature=settings.get("vision_temperature", 0.7),
            max_tokens=settings.get("max_tokens", 2000),
            confidence_threshold=settings.get("confidence_threshold", 0.8),
            enable_context_memory=settings.get("enable_context_memory", True),
            enable_knowledge_search=settings.get("enable_knowledge_search", True),
            fallback_to_human=settings.get("fallback_to_human", True),
            enable_mcp_tools=settings.get("enable_mcp_tools", True),
            mcp_server_ids=settings.get("mcp_server_ids", []),
            mcp_tool_ids=settings.get("mcp_tool_ids", []),
            created_at=settings.get("created_at", datetime.utcnow()),
            updated_at=settings.get("updated_at", datetime.utcnow())
        )
        
    except Exception as e:
        logger.error(f"获取系统整合设置失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取系统整合设置失败: {str(e)}"
        )

@router.post(
    "/settings",
    response_model=IntegrationSettingsResponse,
    summary="创建系统整合设置",
    description="创建AI客服的系统整合配置"
)
async def create_integration_settings(
    settings_data: IntegrationSettings,
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """创建系统整合设置"""
    try:
        # 获取客服管理器实例
        cs_manager = get_customer_service_manager()
        
        # 创建整合设置
        created_settings = await cs_manager.create_integration_settings(
            project_id=project.id,
            db=db,
            settings_data=settings_data.dict()
        )
        
        return IntegrationSettingsResponse(
            ai_model_id=created_settings.get("ai_model_id"),
            vision_model_id=created_settings.get("vision_model_id"),
            audio_model_id=created_settings.get("audio_model_id"),
            embedding_model_id=created_settings.get("embedding_model_id"),
            knowledge_base_ids=created_settings.get("knowledge_base_ids", []),
            temperature=created_settings.get("temperature", 0.7),
            vision_temperature=created_settings.get("vision_temperature", 0.7),
            max_tokens=created_settings.get("max_tokens", 2000),
            confidence_threshold=created_settings.get("confidence_threshold", 0.8),
            enable_context_memory=created_settings.get("enable_context_memory", True),
            enable_knowledge_search=created_settings.get("enable_knowledge_search", True),
            fallback_to_human=created_settings.get("fallback_to_human", True),
            enable_mcp_tools=created_settings.get("enable_mcp_tools", True),
            mcp_server_ids=created_settings.get("mcp_server_ids", []),
            mcp_tool_ids=created_settings.get("mcp_tool_ids", []),
            created_at=created_settings.get("created_at", datetime.utcnow()),
            updated_at=created_settings.get("updated_at", datetime.utcnow())
        )
        
    except Exception as e:
        logger.error(f"创建系统整合设置失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建系统整合设置失败: {str(e)}"
        )

@router.put(
    "/settings",
    response_model=IntegrationSettingsResponse,
    summary="更新系统整合设置",
    description="更新AI客服的系统整合配置"
)
async def update_integration_settings(
    settings_data: IntegrationSettings,
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """更新系统整合设置"""
    try:
        # 获取客服管理器实例
        cs_manager = get_customer_service_manager()
        
        # 更新整合设置
        updated_settings = await cs_manager.update_integration_settings(
            project_id=project.id,
            db=db,
            settings_data=settings_data.dict()
        )
        
        return IntegrationSettingsResponse(
            ai_model_id=updated_settings.get("ai_model_id"),
            vision_model_id=updated_settings.get("vision_model_id"),
            audio_model_id=updated_settings.get("audio_model_id"),
            embedding_model_id=updated_settings.get("embedding_model_id"),
            knowledge_base_ids=updated_settings.get("knowledge_base_ids", []),
            temperature=updated_settings.get("temperature", 0.7),
            vision_temperature=updated_settings.get("vision_temperature", 0.7),
            max_tokens=updated_settings.get("max_tokens", 2000),
            confidence_threshold=updated_settings.get("confidence_threshold", 0.8),
            enable_context_memory=updated_settings.get("enable_context_memory", True),
            enable_knowledge_search=updated_settings.get("enable_knowledge_search", True),
            fallback_to_human=updated_settings.get("fallback_to_human", True),
            enable_mcp_tools=updated_settings.get("enable_mcp_tools", True),
            mcp_server_ids=updated_settings.get("mcp_server_ids", []),
            mcp_tool_ids=updated_settings.get("mcp_tool_ids", []),
            created_at=updated_settings.get("created_at", datetime.utcnow()),
            updated_at=updated_settings.get("updated_at", datetime.utcnow())
        )
        
    except Exception as e:
        logger.error(f"更新系统整合设置失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新系统整合设置失败: {str(e)}"
        )

@router.get(
    "/test",
    summary="测试系统整合",
    description="测试AI模型和知识库的连接状态"
)
async def test_integration(
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """测试系统整合"""
    try:
        # 获取客服管理器实例
        cs_manager = get_customer_service_manager()
        
        # 测试整合状态
        test_results = await cs_manager.test_integration(
            project_id=project.id,
            db=db
        )
        
        return {
            "success": True,
            "message": "系统整合测试完成",
            "results": test_results
        }
        
    except Exception as e:
        logger.error(f"测试系统整合失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"测试系统整合失败: {str(e)}"
        )

# AI模型配置相关API
class AIModelConfig(BaseModel):
    name: str
    model_type: str  # chat, vision, audio等
    provider: str    # openai, anthropic, local等
    api_key: Optional[str] = None
    base_url: Optional[str] = None
    model: str       # 具体模型名称，如gpt-4-turbo
    max_tokens: int = 2000
    temperature: float = 0.7
    is_default: bool = False
    enabled: bool = True

class AIModelConfigResponse(BaseModel):
    id: str
    name: str
    model_type: str
    provider: str
    model: str
    max_tokens: int
    temperature: float
    is_default: bool
    enabled: bool
    created_at: datetime
    updated_at: datetime

@router.get(
    "/ai-models",
    summary="获取AI客服插件的AI模型配置",
    description="获取AI客服插件的AI模型配置（来源与AI助理相同，但独立保存和计费）"
)
async def get_ai_models(
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """
    获取AI客服插件的AI模型配置
    注意：模型来源与AI助理相同（同一模型池），但AI客服有独立的配置和计费
    """
    try:
        # 获取客服管理器实例
        cs_manager = get_customer_service_manager()
        
        # 从插件数据库表中获取AI客服的模型配置
        try:
            configs = await cs_manager.get_ai_model_configs(
                project_id=project.id,
                db=db
            )
        except Exception as e:
            # 如果方法未实现，返回示例数据
            logger.warning(f"AI模型配置服务需要实现: {e}")
            configs = [
                {
                    "id": "cs_config_1",
                    "source_model_id": "gpt-4-turbo",
                    "name": "GPT-4 Turbo",
                    "model": "gpt-4-turbo",
                    "model_type": "chat",
                    "provider": "openai",
                    "enabled": True,
                    "temperature": 0.7,
                    "max_tokens": 2000,
                    "created_at": datetime.now(),
                    "updated_at": datetime.now(),
                    "usage_for_customer_service": True,
                    "billing_config": {
                        "separate_billing": True,
                        "rate_limit": 1000,
                        "cost_per_token": 0.00003
                    }
                },
                {
                    "id": "cs_config_2", 
                    "source_model_id": "gpt-4-vision",
                    "name": "GPT-4 Vision",
                    "model": "gpt-4-vision-preview",
                    "model_type": "vision",
                    "provider": "openai",
                    "enabled": False,
                    "temperature": 0.5,
                    "max_tokens": 1000,
                    "created_at": datetime.now(),
                    "updated_at": datetime.now(),
                    "usage_for_customer_service": False,
                    "billing_config": {
                        "separate_billing": True,
                        "rate_limit": 500,
                        "cost_per_token": 0.00005
                    }
                }
            ]
        
        return {
            "success": True,
            "data": configs,
            "note": "AI客服模型配置 - 来源与AI助理相同，统一计费到项目（显示在AI助手中）"
        }
        
    except Exception as e:
        logger.error(f"获取AI模型配置失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取AI模型配置失败: {str(e)}"
        )

@router.post(
    "/ai-models",
    summary="创建AI模型配置",
    description="为AI客服插件创建新的AI模型配置"
)
async def create_ai_model(
    config_data: AIModelConfig,
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """创建AI模型配置"""
    try:
        # 获取客服管理器实例
        cs_manager = get_customer_service_manager()
        
        # 创建AI模型配置
        created_config = await cs_manager.create_ai_model_config(
            project_id=project.id,
            db=db,
            config_data=config_data.dict()
        )
        
        return {
            "success": True,
            "data": created_config,
            "message": "AI模型配置创建成功"
        }
        
    except Exception as e:
        logger.error(f"创建AI模型配置失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建AI模型配置失败: {str(e)}"
        )

@router.put(
    "/ai-models/{config_id}",
    summary="更新AI模型配置",
    description="更新AI客服插件的AI模型配置"
)
async def update_ai_model(
    config_id: str,
    config_data: AIModelConfig,
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """更新AI模型配置"""
    try:
        # 获取客服管理器实例
        cs_manager = get_customer_service_manager()
        
        # 更新AI模型配置
        updated_config = await cs_manager.update_ai_model_config(
            config_id=config_id,
            project_id=project.id,
            db=db,
            config_data=config_data.dict()
        )
        
        return {
            "success": True,
            "data": updated_config,
            "message": "AI模型配置更新成功"
        }
        
    except Exception as e:
        logger.error(f"更新AI模型配置失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新AI模型配置失败: {str(e)}"
        )

@router.delete(
    "/ai-models/{config_id}",
    summary="删除AI模型配置",
    description="删除AI客服插件的AI模型配置"
)
async def delete_ai_model(
    config_id: str,
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """删除AI模型配置"""
    try:
        # 获取客服管理器实例
        cs_manager = get_customer_service_manager()
        
        # 删除AI模型配置
        await cs_manager.delete_ai_model_config(
            config_id=config_id,
            project_id=project.id,
            db=db
        )
        
        return {
            "success": True,
            "message": "AI模型配置删除成功"
        }
        
    except Exception as e:
        logger.error(f"删除AI模型配置失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除AI模型配置失败: {str(e)}"
        )

# MCP工具配置相关API（注意：这里应该从项目AI助理获取，不是SAPI插件）
@router.get(
    "/mcp/tools",
    summary="获取MCP工具列表",
    description="从项目AI助理获取MCP工具配置（面向内部用户）"
)
async def get_mcp_tools(
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """获取项目AI助理的MCP工具列表"""
    try:
        # 从项目AI助理系统获取真实的MCP工具
        from services.ai.tool_service import AIToolService
        
        # 获取AI助手工具列表
        ai_tools, total = await AIToolService.get_tools(
            db=db,
            tenant_id=project.tenant_id,
            project_id=project.id,
            status="active",
            skip=0,
            limit=100
        )
        
        tools = []
        for tool in ai_tools:
            tools.append({
                "id": str(tool.id),
                "name": tool.name,
                "tool_type": tool.type,
                "description": tool.description,
                "enabled": tool.status == "active",
                "source": "project_ai_assistant",
                "parameters": tool.parameters or {},
                "created_at": tool.created_at.isoformat() if tool.created_at else None
            })
        
        logger.info(f"从项目AI助理获取到 {len(tools)} 个MCP工具")
        
        return {
            "success": True,
            "data": tools,
            "total": total,
            "source": "project_ai_assistant",
            "note": "这些工具来自项目AI助理系统，面向内部用户"
        }
        
    except Exception as e:
        logger.error(f"获取MCP工具失败: {e}")
        # 如果获取失败，返回空列表而不是模拟数据
        return {
            "success": False,
            "data": [],
            "total": 0,
            "source": "project_ai_assistant",
            "error": f"获取MCP工具失败: {str(e)}"
        }

@router.get(
    "/mcp/servers",
    summary="获取MCP服务器列表",
    description="从项目AI助理获取MCP服务器配置"
)
async def get_mcp_servers(
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """获取项目AI助理的MCP服务器列表"""
    try:
        # 从项目AI助理系统获取真实的MCP服务器
        from models.ai_mcp_server import AIMCPServer
        from sqlalchemy import select
        
        # 查询项目的MCP服务器
        result = await db.execute(
            select(AIMCPServer).where(
                AIMCPServer.project_id == project.id
            ).order_by(AIMCPServer.created_at.desc())
        )
        ai_servers = result.scalars().all()
        
        servers = []
        for server in ai_servers:
            servers.append({
                "id": str(server.id),
                "name": server.name,
                "status": server.status,
                "description": server.description,
                "transport_type": server.transport_type,
                "deployment_mode": server.deployment_mode,
                "enabled": server.enabled,
                "source": "project_ai_assistant",
                "tools_count": server.tools_count,
                "last_connected_at": server.last_connected_at.isoformat() if server.last_connected_at else None,
                "available_tools": server.available_tools or [],
                "created_at": server.created_at.isoformat() if server.created_at else None
            })
        
        logger.info(f"从项目AI助理获取到 {len(servers)} 个MCP服务器")
        
        return {
            "success": True,
            "data": servers,
            "total": len(servers),
            "source": "project_ai_assistant",
            "note": "这些服务器来自项目AI助理系统"
        }
        
    except Exception as e:
        logger.error(f"获取MCP服务器失败: {e}")
        # 如果获取失败，返回空列表而不是模拟数据
        return {
            "success": False,
            "data": [],
            "total": 0,
            "source": "project_ai_assistant",
            "error": f"获取MCP服务器失败: {str(e)}"
        }
