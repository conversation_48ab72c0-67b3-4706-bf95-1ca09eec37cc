from typing import Optional, Dict, Any, List, Union
from pydantic import BaseModel, UUID4
from datetime import datetime

# 门店分类基础模型
class StoreCategoryBase(BaseModel):
    name: str
    code: str
    description: Optional[str] = None

# 创建门店分类请求模型
class StoreCategoryCreate(StoreCategoryBase):
    project_id: Optional[UUID4] = None

# 更新门店分类请求模型
class StoreCategoryUpdate(StoreCategoryBase):
    pass

# 门店分类响应模型
class StoreCategory(StoreCategoryBase):
    id: UUID4
    project_id: UUID4
    created_at: datetime
    updated_at: Optional[datetime] = None

    model_config = {"from_attributes": True}

# 门店区域基础模型
class StoreRegionBase(BaseModel):
    name: str
    code: str
    description: Optional[str] = None
    parent_id: Optional[UUID4] = None

# 创建门店区域请求模型
class StoreRegionCreate(StoreRegionBase):
    project_id: Optional[UUID4] = None

# 更新门店区域请求模型
class StoreRegionUpdate(StoreRegionBase):
    pass

# 门店区域响应模型（简化版，不包含嵌套关系）
class StoreRegionSimple(StoreRegionBase):
    id: UUID4
    project_id: UUID4
    created_at: datetime
    updated_at: Optional[datetime] = None
    full_name: Optional[str] = None

    model_config = {"from_attributes": True}
        
    @classmethod
    def from_orm(cls, obj):
        """自定义from_orm方法来处理full_name字段"""
        # 如果对象有预计算的_full_name属性，使用它
        if hasattr(obj, '_full_name'):
            full_name = obj._full_name
        else:
            # 否则计算full_name，但要安全地处理parent关系
            try:
                if hasattr(obj, 'parent') and obj.parent:
                    full_name = f"{obj.parent.name}-{obj.name}"
                else:
                    full_name = obj.name
            except:
                # 如果访问parent失败，就使用name
                full_name = obj.name
        
        # 创建字典包含所有字段
        data = {
            'id': obj.id,
            'project_id': obj.project_id,
            'name': obj.name,
            'code': obj.code,
            'description': obj.description,
            'parent_id': obj.parent_id,
            'created_at': obj.created_at,
            'updated_at': obj.updated_at,
            'full_name': full_name
        }
        
        return cls(**data)

# 门店区域响应模型（完整版，包含嵌套关系）
class StoreRegion(StoreRegionSimple):
    parent: Optional["StoreRegion"] = None
    children: Optional[List["StoreRegion"]] = []

# 门店基础模型
class StoreBase(BaseModel):
    name: str
    code: str
    address: Optional[str] = None
    phone: Optional[str] = None
    manager: Optional[str] = None
    latitude: Optional[float] = None
    longitude: Optional[float] = None
    business_hours: Optional[str] = None
    status: str = "active"

# 创建门店请求模型
class StoreCreate(StoreBase):
    project_id: UUID4
    category_id: Optional[UUID4] = None
    region_id: Optional[UUID4] = None
    settings: Optional[Dict[str, Any]] = None

# 更新门店请求模型
class StoreUpdate(StoreBase):
    category_id: Optional[UUID4] = None
    region_id: Optional[UUID4] = None
    settings: Optional[Dict[str, Any]] = None

# 门店响应模型
class Store(StoreBase):
    id: UUID4
    project_id: UUID4
    category_id: Optional[UUID4] = None
    region_id: Optional[UUID4] = None
    settings: Optional[Dict[str, Any]] = None
    created_at: datetime
    updated_at: Optional[datetime] = None
    category: Optional[StoreCategory] = None
    region: Optional[StoreRegionSimple] = None  # 使用简化版的StoreRegion，避免嵌套关系

    model_config = {"from_attributes": True}

# 门店详情响应模型（包含负责人ID）
class StoreDetail(Store):
    manager_user_id: Optional[UUID4] = None

# 门店列表响应模型
class StoreList(BaseModel):
    items: List[Store]
    total: int
    page: int
    size: int
    pages: int

    model_config = {"from_attributes": True}
