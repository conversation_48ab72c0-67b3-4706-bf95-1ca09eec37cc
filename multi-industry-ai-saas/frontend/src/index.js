// 解决process未定义问题
// 注释掉下面的代码，程序会先启动，然后再安装process依赖
// import process from 'process';
// window.process = process;

import React from 'react';
import ReactDOM from 'react-dom/client';
import { BrowserRouter } from 'react-router-dom';
import App from './App';
import './index.css';

// 上下文提供者
import { AuthProvider } from './contexts/AuthContext';
import { TenantProvider } from './contexts/TenantContext';
import { IndustryProvider } from './contexts/IndustryContext';
import { NotificationProvider } from './contexts/NotificationContext';

const root = ReactDOM.createRoot(document.getElementById('root'));
root.render(
  <BrowserRouter>
    <AuthProvider>
      <TenantProvider>
        <IndustryProvider>
          <NotificationProvider>
            <App />
          </NotificationProvider>
        </IndustryProvider>
      </TenantProvider>
    </AuthProvider>
  </BrowserRouter>
);
