#!/usr/bin/env python
# -*- coding: utf-8 -*-

import logging
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status, Query, Path
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import update, delete
from pydantic import BaseModel, Field
import uuid
from datetime import datetime

from db.database import get_db
from models.core import Industry
from models.user import User
from core.auth import get_current_system_admin, get_current_user

# 配置日志
logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter()


# 行业模型
class IndustryBase(BaseModel):
    code: str
    name: str
    description: Optional[str] = None
    icon: Optional[str] = None
    settings: Optional[Dict[str, Any]] = {}


class IndustryCreate(IndustryBase):
    pass


class IndustryUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    icon: Optional[str] = None
    is_active: Optional[bool] = None
    settings: Optional[Dict[str, Any]] = None


class IndustryResponse(IndustryBase):
    id: uuid.UUID
    is_active: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True


@router.post("/industries", response_model=IndustryResponse, status_code=status.HTTP_201_CREATED)
async def create_industry(
    industry: IndustryCreate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_system_admin),
):
    """
    创建行业（系统管理员权限）
    """
    try:
        # 检查行业代码是否已存在
        result = await db.execute(select(Industry).where(Industry.code == industry.code))
        existing_industry = result.scalars().first()
        
        if existing_industry:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="行业代码已存在",
            )
        
        # 创建行业
        db_industry = Industry(
            id=uuid.uuid4(),
            code=industry.code,
            name=industry.name,
            description=industry.description,
            icon=industry.icon,
            settings=industry.settings,
        )
        
        db.add(db_industry)
        await db.commit()
        await db.refresh(db_industry)
        
        logger.info(f"行业创建成功: {db_industry.id}")
        return db_industry
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"创建行业失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建行业失败: {str(e)}",
        )


@router.get("/industries", response_model=List[IndustryResponse])
async def get_industries(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    is_active: Optional[bool] = Query(None),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    获取行业列表
    """
    try:
        # 构建查询
        query = select(Industry)
        
        # 应用过滤条件
        if is_active is not None:
            query = query.where(Industry.is_active == is_active)
        
        # 应用分页
        query = query.offset(skip).limit(limit)
        
        # 执行查询
        result = await db.execute(query)
        industries = result.scalars().all()
        
        return industries
    except Exception as e:
        logger.error(f"获取行业列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取行业列表失败: {str(e)}",
        )


@router.get("/industries/{industry_id}", response_model=IndustryResponse)
async def get_industry(
    industry_id: uuid.UUID = Path(...),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    获取行业详情
    """
    try:
        # 查询行业
        result = await db.execute(select(Industry).where(Industry.id == industry_id))
        industry = result.scalars().first()
        
        if not industry:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="行业不存在",
            )
        
        return industry
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取行业详情失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取行业详情失败: {str(e)}",
        )


@router.get("/industries/code/{code}", response_model=IndustryResponse)
async def get_industry_by_code(
    code: str = Path(...),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    根据代码获取行业详情
    """
    try:
        # 查询行业
        result = await db.execute(select(Industry).where(Industry.code == code))
        industry = result.scalars().first()
        
        if not industry:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="行业不存在",
            )
        
        return industry
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取行业详情失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取行业详情失败: {str(e)}",
        )


@router.put("/industries/{industry_id}", response_model=IndustryResponse)
async def update_industry(
    industry_id: uuid.UUID = Path(...),
    industry_update: IndustryUpdate = None,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_system_admin),
):
    """
    更新行业信息（系统管理员权限）
    """
    try:
        # 查询行业
        result = await db.execute(select(Industry).where(Industry.id == industry_id))
        db_industry = result.scalars().first()
        
        if not db_industry:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="行业不存在",
            )
        
        # 更新行业信息
        update_data = industry_update.dict(exclude_unset=True)
        
        if update_data:
            await db.execute(
                update(Industry)
                .where(Industry.id == industry_id)
                .values(**update_data)
            )
            await db.commit()
        
        # 重新查询行业
        result = await db.execute(select(Industry).where(Industry.id == industry_id))
        updated_industry = result.scalars().first()
        
        return updated_industry
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"更新行业失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新行业失败: {str(e)}",
        )


@router.delete("/industries/{industry_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_industry(
    industry_id: uuid.UUID = Path(...),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_system_admin),
):
    """
    删除行业（系统管理员权限）
    """
    try:
        # 查询行业
        result = await db.execute(select(Industry).where(Industry.id == industry_id))
        db_industry = result.scalars().first()
        
        if not db_industry:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="行业不存在",
            )
        
        # 删除行业
        await db.execute(delete(Industry).where(Industry.id == industry_id))
        await db.commit()
        
        logger.info(f"行业删除成功: {industry_id}")
        return None
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        logger.error(f"删除行业失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除行业失败: {str(e)}",
        )
