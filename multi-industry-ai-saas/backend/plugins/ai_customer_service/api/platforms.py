#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
平台管理 API
"""

import logging
from typing import Dict, Any, Optional, List
from fastapi import APIRouter, Depends, HTTPException, status, Query, Path
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel

from api.deps import get_db
from core.auth import get_current_user
from api.deps import get_current_project
from models.user import User
from models.project import Project
from ..services.customer_service import CustomerServiceManager

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter()

# 请求模型
class PlatformConfigRequest(BaseModel):
    platform_type: str
    platform_name: str
    credentials: Dict[str, Any]
    config: Optional[Dict[str, Any]] = None
    is_active: bool = True

# 响应模型
class PlatformResponse(BaseModel):
    id: str
    platform_type: str
    platform_name: str
    is_active: bool
    config: Dict[str, Any]
    created_at: str
    last_sync_at: Optional[str]

# 获取客服管理器实例
def get_customer_service_manager() -> CustomerServiceManager:
    return CustomerServiceManager()

@router.get(
    "",
    response_model=List[PlatformResponse],
    summary="获取平台列表",
    description="获取项目配置的所有平台"
)
async def get_platforms(
    platform_type: Optional[str] = Query(None, description="平台类型过滤"),
    is_active: Optional[bool] = Query(None, description="是否激活过滤"),
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """获取平台列表"""
    try:
        # 这里需要实现实际的数据库查询
        # 暂时返回模拟数据
        
        platforms = [
            PlatformResponse(
                id="platform_1",
                platform_type="wechat",
                platform_name="个人微信",
                is_active=True,
                config={"auto_reply": True},
                created_at="2025-01-01T00:00:00Z",
                last_sync_at="2025-01-01T12:00:00Z"
            ),
            PlatformResponse(
                id="platform_2",
                platform_type="dingtalk",
                platform_name="钉钉机器人",
                is_active=True,
                config={"auto_reply": True},
                created_at="2025-01-01T00:00:00Z",
                last_sync_at="2025-01-01T12:00:00Z"
            )
        ]
        
        # 应用过滤
        if platform_type:
            platforms = [p for p in platforms if p.platform_type == platform_type]
        if is_active is not None:
            platforms = [p for p in platforms if p.is_active == is_active]
        
        return platforms
        
    except Exception as e:
        logger.error(f"获取平台列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取平台列表失败: {str(e)}"
        )

@router.post(
    "",
    response_model=PlatformResponse,
    status_code=status.HTTP_201_CREATED,
    summary="添加平台",
    description="添加新的平台配置"
)
async def add_platform(
    platform_data: PlatformConfigRequest,
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db),
    cs_manager: CustomerServiceManager = Depends(get_customer_service_manager)
):
    """添加平台"""
    try:
        # 添加平台适配器
        success = await cs_manager.add_platform_adapter(
            platform_type=platform_data.platform_type,
            config={
                "platform_name": platform_data.platform_name,
                "credentials": platform_data.credentials,
                "config": platform_data.config or {}
            }
        )
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="平台添加失败，请检查配置"
            )
        
        # 这里需要保存到数据库
        # 暂时返回模拟数据
        
        return PlatformResponse(
            id="new_platform_id",
            platform_type=platform_data.platform_type,
            platform_name=platform_data.platform_name,
            is_active=platform_data.is_active,
            config=platform_data.config or {},
            created_at="2025-01-01T00:00:00Z",
            last_sync_at=None
        )
        
    except Exception as e:
        logger.error(f"添加平台失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"添加平台失败: {str(e)}"
        )

@router.put(
    "/{platform_id}",
    response_model=PlatformResponse,
    summary="更新平台配置",
    description="更新指定平台的配置"
)
async def update_platform(
    platform_id: str = Path(..., description="平台ID"),
    platform_data: PlatformConfigRequest = ...,
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """更新平台配置"""
    try:
        # 这里需要实现实际的更新逻辑
        # 暂时返回模拟数据
        
        return PlatformResponse(
            id=platform_id,
            platform_type=platform_data.platform_type,
            platform_name=platform_data.platform_name,
            is_active=platform_data.is_active,
            config=platform_data.config or {},
            created_at="2025-01-01T00:00:00Z",
            last_sync_at="2025-01-01T12:00:00Z"
        )
        
    except Exception as e:
        logger.error(f"更新平台配置失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新平台配置失败: {str(e)}"
        )

@router.delete(
    "/{platform_id}",
    summary="删除平台",
    description="删除指定的平台配置"
)
async def delete_platform(
    platform_id: str = Path(..., description="平台ID"),
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db),
    cs_manager: CustomerServiceManager = Depends(get_customer_service_manager)
):
    """删除平台"""
    try:
        # 这里需要先获取平台类型，然后移除适配器
        platform_type = "wechat"  # 需要从数据库获取
        
        await cs_manager.remove_platform_adapter(platform_type)
        
        # 这里需要从数据库删除记录
        
        return {"success": True, "message": "平台删除成功"}
        
    except Exception as e:
        logger.error(f"删除平台失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除平台失败: {str(e)}"
        )

@router.post(
    "/{platform_id}/test",
    summary="测试平台连接",
    description="测试指定平台的连接状态"
)
async def test_platform_connection(
    platform_id: str = Path(..., description="平台ID"),
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """测试平台连接"""
    try:
        # 这里需要实现实际的连接测试逻辑
        # 暂时返回成功结果
        
        return {
            "success": True,
            "message": "平台连接正常",
            "test_time": "2025-01-01T12:00:00Z",
            "response_time": 150  # 毫秒
        }
        
    except Exception as e:
        logger.error(f"测试平台连接失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"测试平台连接失败: {str(e)}"
        )

@router.get(
    "/types",
    summary="获取支持的平台类型",
    description="获取系统支持的所有平台类型"
)
async def get_supported_platform_types():
    """获取支持的平台类型"""
    try:
        platform_types = [
            {
                "type": "wechat",
                "name": "个人微信",
                "description": "支持个人微信客服机器人",
                "required_credentials": ["token", "app_id"],
                "features": ["文本消息", "图片消息", "语音消息"]
            },
            {
                "type": "work_wechat",
                "name": "企业微信",
                "description": "支持企业微信客服应用",
                "required_credentials": ["corp_id", "agent_id", "secret"],
                "features": ["文本消息", "图片消息", "文件消息", "卡片消息"]
            },
            {
                "type": "dingtalk",
                "name": "钉钉",
                "description": "支持钉钉机器人",
                "required_credentials": ["app_key", "app_secret"],
                "features": ["文本消息", "图片消息", "链接消息", "卡片消息"]
            },
            {
                "type": "feishu",
                "name": "飞书",
                "description": "支持飞书机器人",
                "required_credentials": ["app_id", "app_secret"],
                "features": ["文本消息", "图片消息", "富文本消息"]
            }
        ]
        
        return {
            "success": True,
            "data": platform_types
        }
        
    except Exception as e:
        logger.error(f"获取平台类型失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取平台类型失败: {str(e)}"
        )
