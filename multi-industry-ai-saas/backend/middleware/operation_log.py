#!/usr/bin/env python
# -*- coding: utf-8 -*-

import logging
import json
import uuid
from typing import Dict, Any, Optional, List, Callable
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp
import re

from core.tenant_context import get_tenant_context
from models.operation_log import OperationLog
from db.database import AsyncSessionLocal
from core.auth import jwt, JWTError
from core.config import settings

# 配置日志
logger = logging.getLogger(__name__)

# 需要记录的路径模式
PATH_PATTERNS = [
    # 用户管理
    (r"/api/v1/users", "user", "用户管理"),
    # 角色管理
    (r"/api/v1/roles", "role", "角色管理"),
    # 项目管理
    (r"/api/v1/projects", "project", "项目管理"),
    # 门店管理
    (r"/api/v1/project/[^/]+/stores", "store", "门店管理"),
    # 产品管理
    (r"/api/v1/project/[^/]+/products", "product", "产品管理"),
    # 供应商管理
    (r"/api/v1/project/[^/]+/suppliers", "supplier", "供应商管理"),
    # 库存管理
    (r"/api/v1/project/[^/]+/inventory", "inventory", "库存管理"),
    # 采购管理
    (r"/api/v1/project/[^/]+/purchase", "purchase", "采购管理"),
    # 销售管理
    (r"/api/v1/project/[^/]+/sales", "sales", "销售管理"),
    # 系统设置
    (r"/api/v1/project/[^/]+/settings", "system", "系统设置"),
    # 知识库管理
    (r"/api/v1/project/[^/]+/knowledge-base", "knowledge", "知识库管理"),
]

# HTTP方法到操作的映射
METHOD_TO_ACTION = {
    "POST": "create",
    "PUT": "update",
    "PATCH": "update",
    "DELETE": "delete",
    "GET": None  # 不记录GET请求
}

# 资源类型映射
RESOURCE_TYPE_MAPPING = {
    "user": "user",
    "role": "role",
    "project": "project",
    "store": "store",
    "product": "product",
    "supplier": "supplier",
    "inventory": "inventory",
    "purchase": "purchase_order",
    "sales": "sales_report",
    "system": "system_config",
    "knowledge": "knowledge_document",
}

class OperationLogMiddleware(BaseHTTPMiddleware):
    """
    操作日志中间件
    记录用户的操作日志
    """

    def __init__(self, app: ASGIApp):
        super().__init__(app)

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # 先执行请求
        response = await call_next(request)

        # 只记录成功的请求
        if 200 <= response.status_code < 300 and request.method != "GET":
            try:
                await self._log_operation(request, response)
            except Exception as e:
                logger.error(f"记录操作日志失败: {e}")

        return response

    async def _log_operation(self, request: Request, response: Response):
        """记录操作日志"""
        # 获取请求路径和方法
        path = request.url.path
        method = request.method

        # 检查是否需要记录
        module, description = self._get_module_and_description(path, method)
        if not module or not description:
            return

        # 获取操作类型
        action = METHOD_TO_ACTION.get(method)
        if not action:
            return

        # 获取资源类型
        resource_type = RESOURCE_TYPE_MAPPING.get(module, module)

        # 获取资源ID
        resource_id = self._extract_resource_id(path)

        # 获取用户ID
        user_id = await self._get_user_id(request)
        if not user_id:
            return

        # 获取项目ID
        project_id = self._extract_project_id(path)
        if not project_id:
            # 尝试从租户上下文获取
            tenant_context = get_tenant_context()
            if tenant_context and tenant_context.project_id:
                project_id = tenant_context.project_id

        if not project_id:
            return

        # 获取租户ID
        tenant_id = None
        tenant_context = get_tenant_context()
        if tenant_context and tenant_context.tenant_id:
            tenant_id = tenant_context.tenant_id

        # 获取请求体
        request_body = await self._get_request_body(request)

        # 创建操作日志
        async with AsyncSessionLocal() as db:
            try:
                log = OperationLog(
                    id=uuid.uuid4(),
                    tenant_id=tenant_id,
                    project_id=project_id,
                    user_id=user_id,
                    module=module,
                    action=action,
                    resource_type=resource_type,
                    resource_id=resource_id,
                    description=description,
                    details=request_body,
                    ip_address=request.client.host if request.client else None,
                    user_agent=request.headers.get("user-agent")
                )

                db.add(log)
                await db.commit()
            except Exception as e:
                await db.rollback()
                logger.error(f"保存操作日志失败: {e}")
                raise

    def _get_module_and_description(self, path: str, method: str) -> tuple:
        """获取模块和描述"""
        for pattern, module, desc_prefix in PATH_PATTERNS:
            if re.match(pattern, path):
                action_desc = {
                    "POST": "创建",
                    "PUT": "更新",
                    "PATCH": "更新",
                    "DELETE": "删除"
                }.get(method, "")

                if action_desc:
                    return module, f"{action_desc}{desc_prefix}"

        return None, None

    def _extract_resource_id(self, path: str) -> Optional[uuid.UUID]:
        """从路径中提取资源ID"""
        # 尝试匹配最后一个路径段作为资源ID
        segments = path.rstrip('/').split('/')
        last_segment = segments[-1]

        try:
            # 检查是否是UUID格式
            return uuid.UUID(last_segment)
        except ValueError:
            return None

    def _extract_project_id(self, path: str) -> Optional[uuid.UUID]:
        """从路径中提取项目ID"""
        # 匹配 /project/{project_id}/ 格式
        match = re.search(r'/project/([^/]+)/', path)
        if match:
            try:
                return uuid.UUID(match.group(1))
            except ValueError:
                return None
        return None

    async def _get_user_id(self, request: Request) -> Optional[uuid.UUID]:
        """获取用户ID"""
        # 从请求头中获取token
        auth_header = request.headers.get("Authorization")
        if not auth_header:
            return None

        # 提取token
        token = None
        if auth_header.startswith("Bearer "):
            token = auth_header[7:]

        if not token:
            return None

        # 解析JWT令牌
        try:
            # 解码JWT令牌
            payload = jwt.decode(token, settings.SECRET_KEY, algorithms=[settings.ALGORITHM])
            user_id = payload.get("user_id")

            if not user_id:
                return None

            try:
                return uuid.UUID(user_id)
            except ValueError:
                logger.error(f"无效的用户ID格式: {user_id}")
                return None

        except JWTError as e:
            logger.error(f"JWT解码失败: {e}")
            return None
        except Exception as e:
            logger.error(f"获取用户信息失败: {e}")
            return None

    async def _get_request_body(self, request: Request) -> Dict[str, Any]:
        """获取请求体"""
        try:
            body = await request.body()
            if body:
                try:
                    return json.loads(body)
                except json.JSONDecodeError:
                    return {"raw": str(body)}
            return {}
        except Exception as e:
            logger.error(f"获取请求体失败: {e}")
            return {}
