import React, { useState, useEffect } from 'react';
import {
  Table, Button, Space, Modal, Form, Input, Select,
  Popconfirm, message, Row, Col, DatePicker, InputNumber,
  Divider, Card, Tag, Tooltip, Descriptions
} from 'antd';
import {
  PlusOutlined, EditOutlined, DeleteOutlined,
  CheckCircleOutlined, CloseCircleOutlined, SearchOutlined,
  ReloadOutlined, FileTextOutlined, EyeOutlined
} from '@ant-design/icons';
import dayjs from 'dayjs';
import { useLocation, useSearchParams } from 'react-router-dom';
import apiService from '../../../../services/api';

const { Option } = Select;
const { TextArea } = Input;

/**
 * 采购入库管理组件
 * 用于管理采购入库单
 */
const PurchaseInboundManagement = ({ warehouses, loading: warehousesLoading, refreshWarehouses }) => {
  const [loading, setLoading] = useState(false);
  const [inbounds, setInbounds] = useState([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });
  const [selectedWarehouse, setSelectedWarehouse] = useState(null);
  const [modalVisible, setModalVisible] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [currentInbound, setCurrentInbound] = useState(null);
  const [form] = Form.useForm();
  const [searchForm] = Form.useForm();
  const [products, setProducts] = useState([]);
  const [purchaseOrders, setPurchaseOrders] = useState([]);
  const [selectedPurchaseOrder, setSelectedPurchaseOrder] = useState(null);
  const [inboundItems, setInboundItems] = useState([]);

  // 获取URL参数
  const [searchParams] = useSearchParams();
  const purchaseOrderIdFromUrl = searchParams.get('purchase_order_id');

  // 初始化数据
  useEffect(() => {
    if (selectedWarehouse) {
      fetchInbounds();
      fetchPurchaseOrders();
    }
  }, [selectedWarehouse]);

  // 处理URL参数中的采购订单ID，自动选择仓库
  useEffect(() => {
    if (purchaseOrderIdFromUrl && warehouses.length > 0 && !selectedWarehouse) {
      // 根据采购订单获取对应的仓库ID
      fetchPurchaseOrderAndSelectWarehouse(purchaseOrderIdFromUrl);
    }
  }, [purchaseOrderIdFromUrl, warehouses]);

  // 处理URL参数中的采购订单ID
  useEffect(() => {
    if (purchaseOrderIdFromUrl && selectedWarehouse && purchaseOrders.length > 0) {
      // 检查URL中的采购订单是否存在于列表中
      const orderExists = purchaseOrders.find(order => order.id === purchaseOrderIdFromUrl);
      if (orderExists) {
        // 自动打开创建入库单模态框并预选采购订单
        showCreateModalWithOrder(purchaseOrderIdFromUrl);
      }
    }
  }, [purchaseOrderIdFromUrl, selectedWarehouse, purchaseOrders]);

  // 获取入库单列表
  const fetchInbounds = async (params = {}) => {
    if (!selectedWarehouse) return;

    setLoading(true);
    try {
      const queryParams = {
        skip: (pagination.current - 1) * pagination.pageSize,
        limit: pagination.pageSize,
        ...params
      };

      const response = await apiService.project.warehouse.getInbounds(selectedWarehouse, queryParams);

      if (response && response.success) {
        setInbounds(response.items || []);
        setPagination({
          ...pagination,
          total: response.total || 0
        });
      } else {
        message.error(response?.message || '获取入库单列表失败');
      }
    } catch (error) {
      console.error('获取入库单列表失败:', error);
      message.error('获取入库单列表失败: ' + (error.message || '未知错误'));
    } finally {
      setLoading(false);
    }
  };

  // 获取采购订单列表
  const fetchPurchaseOrders = async () => {
    try {
      const response = await apiService.project.purchaseOrder.getList({
        warehouse_id: selectedWarehouse,
        status: 'confirmed',
        limit: 100
      });

      // API直接返回包含items的数据，不需要检查success字段
      if (response) {
        setPurchaseOrders(response.items || []);
      }
    } catch (error) {
      console.error('获取采购订单列表失败:', error);
      message.error('获取采购订单列表失败: ' + (error.message || '未知错误'));
    }
  };

  // 获取商品列表
  const fetchProducts = async () => {
    try {
      const response = await apiService.project.product.getList({
        limit: 100
      });

      // API直接返回包含items的数据，不需要检查success字段
      if (response) {
        setProducts(response.items || []);
      }
    } catch (error) {
      console.error('获取商品列表失败:', error);
      message.error('获取商品列表失败: ' + (error.message || '未知错误'));
    }
  };

  // 根据采购订单ID获取详情并自动选择仓库
  const fetchPurchaseOrderAndSelectWarehouse = async (purchaseOrderId) => {
    try {
      const response = await apiService.project.purchaseOrder.getDetail(purchaseOrderId);
      
      if (response) {
        const orderData = response.data || response;
        const warehouseId = orderData.warehouse_id;
        
        if (warehouseId) {
          // 检查仓库是否在列表中
          const warehouse = warehouses.find(w => w.id === warehouseId);
          if (warehouse) {
            setSelectedWarehouse(warehouseId);
            message.info(`已自动选择仓库：${warehouse.name}`);
          } else {
            message.warning('采购订单关联的仓库不在当前仓库列表中');
          }
        }
      }
    } catch (error) {
      console.error('获取采购订单详情失败:', error);
      message.error('无法获取采购订单信息，请手动选择仓库');
    }
  };

  // 处理采购订单选择
  const handlePurchaseOrderSelect = async (orderId) => {
    if (!orderId) {
      setSelectedPurchaseOrder(null);
      setInboundItems([]);
      return;
    }

    try {
      const response = await apiService.project.purchaseOrder.getDetail(orderId);

      // 检查响应是否有效
      if (response) {
        // 根据API返回结构确定正确的数据位置
        const orderData = response.data || response;
        setSelectedPurchaseOrder(orderData);

        // 转换采购订单项为入库单项
        const items = (orderData.items || []).map(item => ({
          product_id: item.product_id,
          product_name: item.product_name,
          product_code: item.product_code,
          product_specification: item.product_specification,
          product_unit: item.product_unit || '个', // 确保有单位值
          quantity: item.quantity - (item.received_quantity || 0),
          unit_price: item.unit_price,
          total_amount: (item.quantity - (item.received_quantity || 0)) * item.unit_price,
          purchase_order_item_id: item.id
        }));

        setInboundItems(items);
      }
    } catch (error) {
      console.error('获取采购订单详情失败:', error);
      message.error('获取采购订单详情失败: ' + (error.message || '未知错误'));
    }
  };

  // 处理表单提交
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();

      if (inboundItems.length === 0) {
        message.warning('请至少添加一个入库商品');
        return;
      }

      setLoading(true);

      const inboundData = {
        ...values,
        inbound_date: values.inbound_date.format('YYYY-MM-DD'),
        items: inboundItems
      };

      try {
        let response;

        if (currentInbound) {
          // 更新入库单
          response = await apiService.project.warehouse.updateInbound(
            selectedWarehouse,
            currentInbound.id,
            inboundData
          );

          if (response && response.success) {
            message.success('入库单更新成功');
            setModalVisible(false);
            fetchInbounds();
          } else {
            message.error(response?.message || '更新入库单失败');
          }
        } else {
          // 创建入库单
          response = await apiService.project.warehouse.createInbound(
            selectedWarehouse,
            inboundData
          );

          if (response && response.success) {
            message.success('入库单创建成功');
            setModalVisible(false);
            fetchInbounds();
          } else {
            message.error(response?.message || '创建入库单失败');
          }
        }
      } catch (error) {
        console.error('保存入库单失败:', error);
        message.error('保存入库单失败: ' + (error.message || '未知错误'));
      } finally {
        setLoading(false);
      }
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  // 显示创建入库单模态框
  const showCreateModal = () => {
    setCurrentInbound(null);
    form.resetFields();
    form.setFieldsValue({
      inbound_date: dayjs(),
      status: 'pending'
    });
    setSelectedPurchaseOrder(null);
    setInboundItems([]);
    fetchProducts();
    setModalVisible(true);
  };

  // 显示创建入库单模态框并预选采购订单
  const showCreateModalWithOrder = async (purchaseOrderId) => {
    setCurrentInbound(null);
    form.resetFields();
    form.setFieldsValue({
      inbound_date: dayjs(),
      status: 'pending',
      purchase_order_id: purchaseOrderId
    });
    setInboundItems([]);
    fetchProducts();
    
    // 自动选择采购订单
    await handlePurchaseOrderSelect(purchaseOrderId);
    setModalVisible(true);
    
    message.success('已自动选择关联的采购订单，请确认入库信息');
  };

  // 显示编辑入库单模态框
  const showEditModal = (record) => {
    setCurrentInbound(record);
    form.setFieldsValue({
      inbound_number: record.inbound_number,
      purchase_order_id: record.purchase_order_id,
      inbound_date: record.inbound_date ? dayjs(record.inbound_date) : dayjs(),
      status: record.status,
      notes: record.notes
    });
    setInboundItems(record.items || []);
    fetchProducts();
    setModalVisible(true);
  };

  // 显示入库单详情模态框
  const showDetailModal = async (record) => {
    setCurrentInbound(record);
    setDetailModalVisible(true);
  };

  // 处理删除入库单
  const handleDelete = async (id) => {
    try {
      setLoading(true);
      const response = await apiService.project.warehouse.deleteInbound(selectedWarehouse, id);

      if (response && response.success) {
        message.success('入库单删除成功');
        fetchInbounds();
      } else {
        message.error(response?.message || '删除入库单失败');
      }
    } catch (error) {
      console.error('删除入库单失败:', error);
      message.error('删除入库单失败: ' + (error.message || '未知错误'));
    } finally {
      setLoading(false);
    }
  };

  // 处理确认入库
  const handleConfirm = async (id) => {
    try {
      setLoading(true);
      const response = await apiService.project.warehouse.confirmInbound(selectedWarehouse, id);

      if (response && response.success) {
        message.success('入库单确认成功');
        fetchInbounds();
      } else {
        message.error(response?.message || '确认入库单失败');
      }
    } catch (error) {
      console.error('确认入库单失败:', error);
      message.error('确认入库单失败: ' + (error.message || '未知错误'));
    } finally {
      setLoading(false);
    }
  };

  // 处理搜索
  const handleSearch = (values) => {
    fetchInbounds(values);
  };

  // 处理表格分页变化
  const handleTableChange = (pagination, filters, sorter) => {
    setPagination(pagination);
    fetchInbounds({
      skip: (pagination.current - 1) * pagination.pageSize,
      limit: pagination.pageSize
    });
  };

  // 入库单状态映射
  const statusMap = {
    pending: { text: '待入库', color: 'warning' },
    confirmed: { text: '已入库', color: 'success' },
    cancelled: { text: '已取消', color: 'error' }
  };

  // 入库单列定义
  const columns = [
    {
      title: '入库单号',
      dataIndex: 'inbound_number',
      key: 'inbound_number',
      render: (text, record) => (
        <a onClick={() => showDetailModal(record)}>{text}</a>
      )
    },
    {
      title: '关联采购单',
      dataIndex: 'purchase_order_number',
      key: 'purchase_order_number'
    },
    {
      title: '入库日期',
      dataIndex: 'inbound_date',
      key: 'inbound_date',
      render: (text) => text ? dayjs(text).format('YYYY-MM-DD') : '-'
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => {
        const { text, color } = statusMap[status] || { text: status, color: 'default' };
        return <Tag color={color}>{text}</Tag>;
      }
    },
    {
      title: '商品数量',
      dataIndex: 'item_count',
      key: 'item_count'
    },
    {
      title: '总金额',
      dataIndex: 'total_amount',
      key: 'total_amount',
      render: (text) => `¥${parseFloat(text || 0).toFixed(2)}`
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="small">
          <Button
            type="text"
            icon={<EyeOutlined />}
            onClick={() => showDetailModal(record)}
          />

          {record.status === 'pending' && (
            <>
              <Button
                type="text"
                icon={<EditOutlined />}
                onClick={() => showEditModal(record)}
              />

              <Button
                type="text"
                icon={<CheckCircleOutlined />}
                onClick={() => handleConfirm(record.id)}
              />

              <Popconfirm
                title="确定要删除这个入库单吗？"
                onConfirm={() => handleDelete(record.id)}
                okText="确定"
                cancelText="取消"
              >
                <Button
                  type="text"
                  danger
                  icon={<DeleteOutlined />}
                />
              </Popconfirm>
            </>
          )}
        </Space>
      )
    }
  ];

  return (
    <div className="purchase-inbound-management">
      <div style={{ marginBottom: 16 }}>
        <Row gutter={16}>
          <Col span={8}>
            <Select
              placeholder="请选择仓库"
              style={{ width: '100%' }}
              loading={warehousesLoading}
              onChange={(value) => setSelectedWarehouse(value)}
              value={selectedWarehouse}
            >
              {warehouses.map(warehouse => (
                <Option key={warehouse.id} value={warehouse.id}>{warehouse.name}</Option>
              ))}
            </Select>
          </Col>

          <Col span={16} style={{ textAlign: 'right' }}>
            <Space>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={showCreateModal}
                disabled={!selectedWarehouse}
              >
                新建入库单
              </Button>

              <Button
                icon={<ReloadOutlined />}
                onClick={() => fetchInbounds()}
                disabled={!selectedWarehouse}
              >
                刷新
              </Button>
            </Space>
          </Col>
        </Row>
      </div>

      <Card>
        <Form
          form={searchForm}
          layout="inline"
          onFinish={handleSearch}
          style={{ marginBottom: 16 }}
        >
          <Form.Item name="inbound_number" label="入库单号">
            <Input placeholder="请输入入库单号" />
          </Form.Item>

          <Form.Item name="purchase_order_number" label="采购单号">
            <Input placeholder="请输入采购单号" />
          </Form.Item>

          <Form.Item name="status" label="状态">
            <Select placeholder="请选择状态" style={{ width: 120 }} allowClear>
              <Option value="pending">待入库</Option>
              <Option value="confirmed">已入库</Option>
              <Option value="cancelled">已取消</Option>
            </Select>
          </Form.Item>

          <Form.Item>
            <Button type="primary" htmlType="submit" icon={<SearchOutlined />}>
              搜索
            </Button>
          </Form.Item>
        </Form>

        <Table
          columns={columns}
          dataSource={inbounds}
          rowKey="id"
          pagination={pagination}
          loading={loading}
          onChange={handleTableChange}
        />
      </Card>

      {/* 入库单表单模态框 */}
      <Modal
        title={currentInbound ? '编辑入库单' : '新建入库单'}
        open={modalVisible}
        onOk={handleSubmit}
        onCancel={() => setModalVisible(false)}
        width={800}
        confirmLoading={loading}
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="inbound_number"
                label="入库单号"
                rules={[{ required: true, message: '请输入入库单号' }]}
              >
                <Input placeholder="请输入入库单号" disabled={!!currentInbound} />
              </Form.Item>
            </Col>

            <Col span={12}>
              <Form.Item
                name="purchase_order_id"
                label="关联采购单"
              >
                <Select
                  placeholder="请选择采购单"
                  onChange={handlePurchaseOrderSelect}
                  allowClear
                >
                  {purchaseOrders.map(order => (
                    <Option key={order.id} value={order.id}>
                      {order.order_number} - {order.supplier_name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="inbound_date"
                label="入库日期"
                rules={[{ required: true, message: '请选择入库日期' }]}
              >
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>

            <Col span={12}>
              <Form.Item
                name="status"
                label="状态"
                rules={[{ required: true, message: '请选择状态' }]}
              >
                <Select placeholder="请选择状态">
                  <Option value="pending">待入库</Option>
                  <Option value="confirmed">已入库</Option>
                  <Option value="cancelled">已取消</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="notes"
            label="备注"
          >
            <TextArea rows={4} placeholder="请输入备注" />
          </Form.Item>

          <Divider>入库商品</Divider>

          {inboundItems.length > 0 ? (
            <Table
              dataSource={inboundItems}
              rowKey={(record, index) => index}
              pagination={false}
              size="small"
              columns={[
                { title: '商品名称', dataIndex: 'product_name', key: 'product_name' },
                { title: '商品编码', dataIndex: 'product_code', key: 'product_code' },
                { title: '规格', dataIndex: 'product_specification', key: 'product_specification' },
                { title: '单位', dataIndex: 'product_unit', key: 'product_unit' },
                { title: '数量', dataIndex: 'quantity', key: 'quantity' },
                { title: '单价', dataIndex: 'unit_price', key: 'unit_price', render: (text) => `¥${parseFloat(text || 0).toFixed(2)}` },
                { title: '总金额', dataIndex: 'total_amount', key: 'total_amount', render: (text) => `¥${parseFloat(text || 0).toFixed(2)}` }
              ]}
              summary={() => (
                <Table.Summary fixed>
                  <Table.Summary.Row>
                    <Table.Summary.Cell index={0} colSpan={6} align="right">总计</Table.Summary.Cell>
                    <Table.Summary.Cell index={1}>
                      ¥{inboundItems.reduce((sum, item) => sum + (item.total_amount || 0), 0).toFixed(2)}
                    </Table.Summary.Cell>
                  </Table.Summary.Row>
                </Table.Summary>
              )}
            />
          ) : (
            <div style={{ textAlign: 'center', padding: '20px 0' }}>
              {selectedPurchaseOrder ? (
                <span>该采购单没有可入库的商品</span>
              ) : (
                <span>请选择采购单以加载商品</span>
              )}
            </div>
          )}
        </Form>
      </Modal>

      {/* 入库单详情模态框 */}
      <Modal
        title="入库单详情"
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={[
          <Button key="close" onClick={() => setDetailModalVisible(false)}>
            关闭
          </Button>
        ]}
        width={800}
      >
        {currentInbound && (
          <>
            <Descriptions bordered column={2}>
              <Descriptions.Item label="入库单号" span={2}>{currentInbound.inbound_number}</Descriptions.Item>
              <Descriptions.Item label="关联采购单">{currentInbound.purchase_order_number || '-'}</Descriptions.Item>
              <Descriptions.Item label="入库日期">{currentInbound.inbound_date ? dayjs(currentInbound.inbound_date).format('YYYY-MM-DD') : '-'}</Descriptions.Item>
              <Descriptions.Item label="状态">
                <Tag color={statusMap[currentInbound.status]?.color || 'default'}>
                  {statusMap[currentInbound.status]?.text || currentInbound.status}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="总金额">¥{parseFloat(currentInbound.total_amount || 0).toFixed(2)}</Descriptions.Item>
              <Descriptions.Item label="备注" span={2}>{currentInbound.notes || '-'}</Descriptions.Item>
            </Descriptions>

            <Divider>入库商品</Divider>

            <Table
              dataSource={currentInbound.items || []}
              rowKey="id"
              pagination={false}
              size="small"
              columns={[
                { title: '商品名称', dataIndex: 'product_name', key: 'product_name' },
                { title: '商品编码', dataIndex: 'product_code', key: 'product_code' },
                { title: '规格', dataIndex: 'product_specification', key: 'product_specification' },
                { title: '单位', dataIndex: 'product_unit', key: 'product_unit' },
                { title: '数量', dataIndex: 'quantity', key: 'quantity' },
                { title: '单价', dataIndex: 'unit_price', key: 'unit_price', render: (text) => `¥${parseFloat(text || 0).toFixed(2)}` },
                { title: '总金额', dataIndex: 'total_amount', key: 'total_amount', render: (text) => `¥${parseFloat(text || 0).toFixed(2)}` }
              ]}
              summary={() => (
                <Table.Summary fixed>
                  <Table.Summary.Row>
                    <Table.Summary.Cell index={0} colSpan={6} align="right">总计</Table.Summary.Cell>
                    <Table.Summary.Cell index={1}>
                      ¥{(currentInbound.items || []).reduce((sum, item) => sum + (item.total_amount || 0), 0).toFixed(2)}
                    </Table.Summary.Cell>
                  </Table.Summary.Row>
                </Table.Summary>
              )}
            />
          </>
        )}
      </Modal>
    </div>
  );
};

export default PurchaseInboundManagement;
