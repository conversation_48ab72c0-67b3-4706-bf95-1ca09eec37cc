from typing import List, Optional, Dict, Any, Union
from pydantic import BaseModel, UUID4, Field, IPvAnyAddress
from datetime import datetime

# 操作日志模型
class OperationLogBase(BaseModel):
    """操作日志基础模型"""
    module: str
    action: str
    resource_type: str
    resource_id: Optional[UUID4] = None
    description: str
    details: Optional[Dict[str, Any]] = None
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None


class OperationLogCreate(OperationLogBase):
    """创建操作日志模型"""
    pass


class OperationLogInDB(OperationLogBase):
    """数据库操作日志模型"""
    id: UUID4
    tenant_id: Optional[UUID4] = None
    project_id: UUID4
    user_id: UUID4
    created_at: datetime
    user_name: Optional[str] = None  # 非数据库字段，用于前端展示

    class Config:
        model_config = {"from_attributes": True}

    def dict(self, *args, **kwargs):
        # 确保 IP 地址被转换为字符串
        result = super().dict(*args, **kwargs)
        if 'ip_address' in result and result['ip_address'] is not None:
            result['ip_address'] = str(result['ip_address'])
        return result


class OperationLogResponse(BaseModel):
    """操作日志响应模型"""
    success: bool
    message: Optional[str] = None
    data: Optional[OperationLogInDB] = None


class OperationLogListResponse(BaseModel):
    """操作日志列表响应模型"""
    success: bool
    message: Optional[str] = None
    items: List[OperationLogInDB]
    total: int
    page: Optional[int] = None
    size: Optional[int] = None
    pages: Optional[int] = None


# 操作日志查询参数
class OperationLogQueryParams(BaseModel):
    """操作日志查询参数"""
    module: Optional[str] = None
    action: Optional[str] = None
    resource_type: Optional[str] = None
    resource_id: Optional[UUID4] = None
    user_id: Optional[UUID4] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    search: Optional[str] = None
    page: int = 1
    size: int = 20
