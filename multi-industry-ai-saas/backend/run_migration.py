#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
运行迁移脚本
"""

import asyncio
from db.database import get_db
from db.migrations import fix_ai_providers_column, fix_ai_models_column, fix_ai_models_code_column, fix_ai_providers

async def run():
    """运行迁移脚本"""
    async for db in get_db():
        print("运行 fix_ai_providers_column 迁移脚本...")
        await fix_ai_providers_column.run_migration(db)
        print("运行 fix_ai_models_column 迁移脚本...")
        await fix_ai_models_column.run_migration(db)
        print("运行 fix_ai_models_code_column 迁移脚本...")
        await fix_ai_models_code_column.run_migration(db)
        print("运行 fix_ai_providers 迁移脚本...")
        await fix_ai_providers.run_migration(db)
        break

if __name__ == "__main__":
    asyncio.run(run())
