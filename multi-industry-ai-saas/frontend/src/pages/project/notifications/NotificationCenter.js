import React, { useState, useEffect } from 'react';
import {
  Card,
  List,
  Badge,
  Button,
  Radio,
  Space,
  Empty,
  message,
  Popconfirm,
  Tag,
  Alert,
  Tooltip
} from 'antd';
import {
  BellOutlined,
  CheckOutlined,
  DeleteOutlined,
  ClockCircleOutlined,
  InfoCircleOutlined,
  WarningOutlined,
  ExclamationCircleOutlined,
  CheckCircleOutlined,
  UserOutlined,
  FileTextOutlined,
  ShoppingCartOutlined,
  MailOutlined,
  PlusOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import dayjs from "dayjs";
import relativeTime from 'dayjs/plugin/relativeTime';
import 'dayjs/locale/zh-cn';
import { project } from '../../../services/api';

// 设置dayjs语言为中文并启用相对时间插件
dayjs.locale('zh-cn');
dayjs.extend(relativeTime);

const NotificationCenter = () => {
  const navigate = useNavigate();
  const [loading, setLoading] = useState(true);
  const [notifications, setNotifications] = useState([]);
  const [filter, setFilter] = useState('all');
  const [markingAsRead, setMarkingAsRead] = useState(false);
  const [deleting, setDeleting] = useState(false);

  // 获取通知列表
  useEffect(() => {
    fetchNotifications();
  }, []);

  // 获取通知列表
  const fetchNotifications = async () => {
    setLoading(true);
    try {
      const response = await project.notification.getList();

      if (response && response.success) {
        // 确保数据是数组
        const notificationsData = Array.isArray(response.data) ? response.data : [];
        setNotifications(notificationsData);
      } else {
        // 使用模拟数据
        const mockData = [
          {
            id: 1,
            title: '系统更新通知',
            content: '系统将于今晚22:00-23:00进行例行维护，请提前做好准备。',
            type: 'system',
            is_read: false,
            created_at: new Date().toISOString(),
            priority: 'high'
          },
          {
            id: 2,
            title: '新订单提醒',
            content: '您有一个新的订单需要处理，订单号：ORD20230501001。',
            type: 'order',
            is_read: false,
            created_at: new Date(Date.now() - 3600000).toISOString(),
            priority: 'normal'
          },
          {
            id: 3,
            title: '库存预警',
            content: '商品"苹果iPhone 14 Pro"库存不足，当前库存：5，请及时补充。',
            type: 'inventory',
            is_read: true,
            created_at: new Date(Date.now() - 86400000).toISOString(),
            priority: 'high'
          },
          {
            id: 4,
            title: '任务已完成',
            content: '您分配的任务"月度库存盘点"已被张三完成。',
            type: 'task',
            is_read: true,
            created_at: new Date(Date.now() - 172800000).toISOString(),
            priority: 'normal'
          },
          {
            id: 5,
            title: '账户安全提醒',
            content: '您的账户于今天10:30在新设备上登录，如非本人操作，请立即修改密码。',
            type: 'security',
            is_read: false,
            created_at: new Date(Date.now() - 259200000).toISOString(),
            priority: 'high'
          }
        ];
        setNotifications(mockData);
      }
    } catch (error) {
      console.error('获取通知失败:', error);
      message.error('获取通知列表失败');
      setNotifications([]);
    } finally {
      setLoading(false);
    }
  };

  // 标记通知为已读
  const markAsRead = async (notification) => {
    if (notification.is_read) return;

    setMarkingAsRead(true);
    try {
      const response = await project.notification.markAsRead(notification.id);

      if (response && response.success) {
        // 更新本地通知状态
        setNotifications(prev =>
          prev.map(item =>
            item.id === notification.id ? { ...item, is_read: true } : item
          )
        );
        message.success('已标记为已读');
      } else {
        // 模拟API调用成功
        setNotifications(prev =>
          prev.map(item =>
            item.id === notification.id ? { ...item, is_read: true } : item
          )
        );
      }
    } catch (error) {
      console.error('标记通知已读失败:', error);
      message.error('标记已读失败');
    } finally {
      setMarkingAsRead(false);
    }
  };

  // 标记所有通知为已读
  const markAllAsRead = async () => {
    setMarkingAsRead(true);
    try {
      const response = await project.notification.markAllAsRead();

      if (response && response.success) {
        // 更新本地通知状态
        setNotifications(prev => prev.map(item => ({ ...item, is_read: true })));
        message.success('已全部标记为已读');
      } else {
        // 模拟API调用成功
        setNotifications(prev => prev.map(item => ({ ...item, is_read: true })));
        message.success('已全部标记为已读');
      }
    } catch (error) {
      console.error('标记所有通知已读失败:', error);
      message.error('操作失败');
    } finally {
      setMarkingAsRead(false);
    }
  };

  // 删除通知
  const deleteNotification = async (notification) => {
    setDeleting(true);
    try {
      const response = await project.notification.delete(notification.id);

      if (response && response.success) {
        // 从本地通知列表中移除
        setNotifications(prev => prev.filter(item => item.id !== notification.id));
        message.success('删除成功');
      } else {
        // 模拟API调用成功
        setNotifications(prev => prev.filter(item => item.id !== notification.id));
        message.success('删除成功');
      }
    } catch (error) {
      console.error('删除通知失败:', error);
      message.error('删除失败');
    } finally {
      setDeleting(false);
    }
  };

  // 处理通知点击
  const handleNotificationClick = async (notification) => {
    // 标记为已读
    if (!notification.is_read) {
      await markAsRead(notification);
    }

    // 导航到目标页面
    if (notification.target_url) {
      navigate(notification.target_url);
    }
  };

  // 处理筛选变化
  const handleFilterChange = (e) => {
    setFilter(e.target.value);
  };

  // 根据筛选条件获取通知
  const getFilteredNotifications = () => {
    if (filter === 'all') {
      return notifications;
    } else if (filter === 'unread') {
      return notifications.filter(item => !item.is_read);
    } else if (filter === 'read') {
      return notifications.filter(item => item.is_read);
    }
    return notifications;
  };

  // 获取通知类型名称
  const getNotificationTypeName = (type) => {
    const typeMap = {
      system: '系统通知',
      order: '订单通知',
      inventory: '库存通知',
      task: '任务通知',
      security: '安全通知',
      payment: '支付通知',
      user: '用户通知'
    };
    return typeMap[type] || '其他通知';
  };

  // 获取通知图标
  const getNotificationIcon = (type) => {
    const iconMap = {
      system: <InfoCircleOutlined />,
      order: <ShoppingCartOutlined />,
      inventory: <WarningOutlined />,
      task: <CheckCircleOutlined />,
      security: <ExclamationCircleOutlined />,
      payment: <FileTextOutlined />,
      user: <UserOutlined />,
      message: <MailOutlined />
    };
    return iconMap[type] || <BellOutlined />;
  };

  // 获取通知优先级标签
  const getPriorityTag = (priority) => {
    if (priority === 'high') {
      return <Tag color="red">重要</Tag>;
    }
    return null;
  };

  // 获取未读通知数量
  const getUnreadCount = () => {
    return notifications.filter(item => !item.is_read).length;
  };

  return (
    <div className="notification-center">
      <Card
        title={
          <Space>
            <BellOutlined />
            <span>通知中心</span>
            {getUnreadCount() > 0 && (
              <Badge count={getUnreadCount()} style={{ backgroundColor: '#1890ff' }} />
            )}
          </Space>
        }
        extra={
          <Space>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => navigate('/project/notifications/create')}
            >
              创建通知
            </Button>
            <Radio.Group value={filter} onChange={handleFilterChange}>
              <Radio.Button value="all">全部</Radio.Button>
              <Radio.Button value="unread">未读</Radio.Button>
              <Radio.Button value="read">已读</Radio.Button>
            </Radio.Group>
            <Button
              type="primary"
              onClick={markAllAsRead}
              disabled={getUnreadCount() === 0 || markingAsRead}
              loading={markingAsRead}
            >
              全部标为已读
            </Button>
          </Space>
        }
      >
        {getUnreadCount() > 0 && (
          <div style={{ marginBottom: 16 }}>
            <Alert
              message={`您有 ${getUnreadCount()} 条未读通知`}
              type="info"
              showIcon
            />
          </div>
        )}

        <List
          loading={loading}
          itemLayout="horizontal"
          dataSource={getFilteredNotifications()}
          locale={{ emptyText: <Empty description="暂无通知" /> }}
          renderItem={item => (
            <List.Item
              key={item.id}
              actions={[
                <Button
                  type="text"
                  icon={<CheckOutlined />}
                  onClick={(e) => {
                    e.stopPropagation();
                    markAsRead(item);
                  }}
                  disabled={item.is_read || markingAsRead}
                  loading={markingAsRead}
                >
                  标为已读
                </Button>,
                <Popconfirm
                  title="确定要删除这条通知吗？"
                  onConfirm={(e) => {
                    e.stopPropagation();
                    deleteNotification(item);
                  }}
                  okText="确定"
                  cancelText="取消"
                >
                  <Button
                    type="text"
                    icon={<DeleteOutlined />}
                    danger
                    loading={deleting}
                    onClick={(e) => e.stopPropagation()}
                  >
                    删除
                  </Button>
                </Popconfirm>
              ]}
              style={{
                backgroundColor: item.is_read ? 'transparent' : '#f0f5ff',
                padding: '12px 24px',
                cursor: 'pointer',
                borderRadius: '4px',
                marginBottom: '8px'
              }}
              onClick={() => handleNotificationClick(item)}
            >
              <List.Item.Meta
                avatar={
                  <Badge dot={!item.is_read} offset={[0, 0]}>
                    <div style={{
                      width: '40px',
                      height: '40px',
                      borderRadius: '20px',
                      backgroundColor: '#e6f7ff',
                      display: 'flex',
                      justifyContent: 'center',
                      alignItems: 'center',
                      color: '#1890ff'
                    }}>
                      {getNotificationIcon(item.type)}
                    </div>
                  </Badge>
                }
                title={
                  <Space>
                    <span style={{ fontWeight: item.is_read ? 'normal' : 'bold' }}>
                      {item.title}
                    </span>
                    <Tag color="blue">{getNotificationTypeName(item.type)}</Tag>
                    {getPriorityTag(item.priority)}
                    {!item.is_read && <Badge status="processing" text="未读" />}
                  </Space>
                }
                description={
                  <div>
                    <div>{item.content}</div>
                    <div style={{ fontSize: '12px', color: '#bfbfbf', marginTop: '4px' }}>
                      <ClockCircleOutlined /> {dayjs(item.created_at).fromNow()}
                    </div>
                  </div>
                }
              />
            </List.Item>
          )}
        />
      </Card>
    </div>
  );
};

export default NotificationCenter;
