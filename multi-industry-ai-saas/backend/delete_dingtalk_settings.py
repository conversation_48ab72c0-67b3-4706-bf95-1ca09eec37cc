import asyncio
import uuid
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from sqlalchemy import select, delete
import os

# --- 配置 ---
# 从环境变量获取数据库连接字符串
DATABASE_URL = os.environ.get("DATABASE_URL")
if not DATABASE_URL:
    raise ValueError("请设置 DATABASE_URL 环境变量")

# 您提供的 Client ID
CLIENT_ID_TO_DELETE = "dingl8pb7vvolkijd89t"

# 数据库模型 (需要根据您的项目结构调整导入)
from models.plugin import Base
from plugins.dingtalk.models.models import DingTalkSettings

async def main():
    """主函数，用于连接数据库并删除指定的钉钉设置记录"""
    engine = create_async_engine(DATABASE_URL)
    async_session = sessionmaker(engine, expire_on_commit=False, class_=AsyncSession)

    print(f"准备连接数据库: {engine.url.render_as_string(hide_password=True)}")

    async with async_session() as session:
        async with session.begin():
            print(f"正在查找 Client ID 为 '{CLIENT_ID_TO_DELETE}' 的钉钉设置...")

            # 1. 查找记录
            stmt_find = select(DingTalkSettings).where(
                DingTalkSettings.dingtalk_ai_client_id == CLIENT_ID_TO_DELETE
            )
            result = await session.execute(stmt_find)
            settings_to_delete = result.scalars().all()

            if not settings_to_delete:
                print("未找到匹配的设置记录，无需删除。")
                return

            print(f"找到了 {len(settings_to_delete)} 条匹配的记录，准备删除...")

            # 2. 删除记录
            stmt_delete = delete(DingTalkSettings).where(
                DingTalkSettings.dingtalk_ai_client_id == CLIENT_ID_TO_DELETE
            )
            delete_result = await session.execute(stmt_delete)
            
            print(f"成功删除了 {delete_result.rowcount} 条记录。")

    print("数据库操作完成。")

if __name__ == "__main__":
    # 运行异步主函数
    # 在 Python 3.7+ 中，可以直接使用 asyncio.run()
    try:
        asyncio.run(main())
    except Exception as e:
        print(f"执行脚本时出错: {e}") 