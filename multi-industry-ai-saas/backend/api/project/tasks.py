#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
项目异步任务 API
"""

import logging
import uuid
from typing import Optional, List
from fastapi import APIRouter, Depends, HTTPException, Query, Path, status
from sqlalchemy.ext.asyncio import AsyncSession

from db.database import get_db
from models.project import Project
from core.auth import get_current_user
from api.deps import get_current_project
from services.async_task_service import AsyncTaskService
from models.user import User

logger = logging.getLogger(__name__)
router = APIRouter()

@router.get("/tasks")
async def get_tasks(
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
    status: Optional[str] = Query(None),
    task_type: Optional[str] = Query(None),
    limit: int = Query(50, ge=1, le=100),
    offset: int = Query(0, ge=0),
):
    """
    获取项目异步任务列表
    """
    try:
        tasks = await AsyncTaskService.get_user_tasks(
            db=db,
            user_id=current_user.id,
            project_id=project.id,
            status=status,
            task_type=task_type,
            limit=limit,
            offset=offset
        )

        # 转换为前端所需的格式
        task_data = []
        for task in tasks:
            task_dict = {
                "id": str(task.id),
                "task_type": task.task_type,
                "task_name": task.task_name,
                "description": task.description,
                "status": task.status,
                "progress": task.progress,
                "priority": task.priority,
                "error_message": task.error_message,
                "created_at": task.created_at.isoformat() if task.created_at else None,
                "updated_at": task.updated_at.isoformat() if task.updated_at else None,
                "started_at": task.started_at.isoformat() if task.started_at else None,
                "completed_at": task.completed_at.isoformat() if task.completed_at else None,
                "input_data": task.input_data,
                "result_data": task.result_data,
                "is_completed": task.status in ['success', 'failed', 'cancelled']
            }
            task_data.append(task_dict)

        return {
            "success": True,
            "message": "获取异步任务列表成功",
            "data": task_data,
            "total": len(task_data)
        }
    except Exception as e:
        logger.error(f"获取异步任务列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取异步任务列表失败: {str(e)}",
        )

@router.get("/tasks/{task_id}")
async def get_task(
    task_id: uuid.UUID = Path(...),
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    获取异步任务详情
    """
    try:
        task = await AsyncTaskService.get_task(db=db, task_id=task_id)

        if not task:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"任务 {task_id} 不存在",
            )

        # 检查权限：只能查看自己创建的任务或项目内的任务
        if task.user_id != current_user.id and task.project_id != project.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问该任务",
            )

        task_dict = {
            "id": str(task.id),
            "task_type": task.task_type,
            "task_name": task.task_name,
            "description": task.description,
            "status": task.status,
            "progress": task.progress,
            "priority": task.priority,
            "error_message": task.error_message,
            "created_at": task.created_at.isoformat() if task.created_at else None,
            "updated_at": task.updated_at.isoformat() if task.updated_at else None,
            "started_at": task.started_at.isoformat() if task.started_at else None,
            "completed_at": task.completed_at.isoformat() if task.completed_at else None,
            "input_data": task.input_data,
            "result_data": task.result_data,
            "is_completed": task.status in ['success', 'failed', 'cancelled']
        }

        return {
            "success": True,
            "message": "获取任务详情成功",
            "data": task_dict,
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取任务详情失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取任务详情失败: {str(e)}",
        )

@router.post("/tasks/{task_id}/cancel")
async def cancel_task(
    task_id: uuid.UUID = Path(...),
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db),
):
    """
    取消异步任务
    """
    try:
        task = await AsyncTaskService.get_task(db=db, task_id=task_id)

        if not task:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"任务 {task_id} 不存在",
            )

        # 检查权限：只能取消自己创建的任务
        if task.user_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权取消该任务",
            )

        success = await AsyncTaskService.cancel_task(db=db, task_id=task_id)

        if success:
            return {
                "success": True,
                "message": "任务已取消",
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="任务无法取消（可能已完成或已取消）",
            )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"取消任务失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"取消任务失败: {str(e)}",
        ) 