import React, { useState, useEffect } from 'react';
import {
  Table,
  Card,
  Button,
  Space,
  Tag,
  Input,
  DatePicker,
  Form,
  Select,
  Modal,
  message,
  Typography,
  Row,
  Col,
  Statistic,
  Tabs,
  Divider,
  Tooltip,
  Badge,
  Descriptions,
  Empty,
  Avatar,
  Spin
} from 'antd';
import {
  SearchOutlined,
  ReloadOutlined,
  AppstoreOutlined,
  ShoppingCartOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ClockCircleOutlined,
  FileTextOutlined,
  DollarOutlined
} from '@ant-design/icons';
import dayjs from 'dayjs';
import apiService from '../../../services/api';
import { useParams } from 'react-router-dom';

const { Title, Text } = Typography;
const { TabPane } = Tabs;
const { RangePicker } = DatePicker;

const PluginOrders = () => {
  const [loading, setLoading] = useState(false);
  const [orders, setOrders] = useState([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });
  const [filters, setFilters] = useState({
    status: undefined,
    plugin_id: undefined,
    date_range: undefined
  });
  const [orderDetail, setOrderDetail] = useState(null);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const { projectId } = useParams();

  // 获取订单列表
  const fetchOrders = async (page = pagination.current, pageSize = pagination.pageSize) => {
    setLoading(true);
    try {
      const params = {
        page,
        page_size: pageSize
      };

      if (filters.status) {
        params.status = filters.status;
      }

      if (filters.plugin_id) {
        params.plugin_id = filters.plugin_id;
      }

      const response = await apiService.tenant.plugins.getOrders(params);
      if (response && response.success) {
        setOrders(response.data.items || []);
        setPagination({
          current: response.data.page || 1,
          pageSize: response.data.page_size || 10,
          total: response.data.total || 0
        });
      } else {
        message.error(response?.message || '获取插件订单失败');
      }
    } catch (error) {
      console.error('获取插件订单失败:', error);
      message.error('获取插件订单失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取订单详情
  const fetchOrderDetail = async (orderId) => {
    try {
      const response = await apiService.tenant.plugins.getOrderDetail(orderId);
      if (response && response.success) {
        setOrderDetail(response.data);
        setDetailModalVisible(true);
      } else {
        message.error(response?.message || '获取订单详情失败');
      }
    } catch (error) {
      console.error('获取订单详情失败:', error);
      message.error('获取订单详情失败');
    }
  };

  // 初始化加载
  useEffect(() => {
    fetchOrders();
  }, []);

  // 处理筛选变化
  const handleFilterChange = (key, value) => {
    setFilters({
      ...filters,
      [key]: value
    });
  };

  // 处理搜索
  const handleSearch = () => {
    fetchOrders(1);
  };

  // 处理重置
  const handleReset = () => {
    setFilters({
      status: undefined,
      plugin_id: undefined,
      date_range: undefined
    });
    fetchOrders(1);
  };

  // 处理表格分页变化
  const handleTableChange = (pagination, filters, sorter) => {
    fetchOrders(pagination.current, pagination.pageSize);
  };

  // 处理查看详情
  const handleViewDetail = (orderId) => {
    fetchOrderDetail(orderId);
  };

  // 处理关闭详情模态框
  const handleCloseDetailModal = () => {
    setDetailModalVisible(false);
    setOrderDetail(null);
  };

  // 获取状态标签
  const getStatusTag = (status) => {
    switch (status) {
      case 'active':
        return <Tag color="success">有效</Tag>;
      case 'expired':
        return <Tag color="default">已过期</Tag>;
      case 'cancelled':
        return <Tag color="error">已取消</Tag>;
      default:
        return <Tag>{status}</Tag>;
    }
  };

  // 获取支付状态标签
  const getPaymentStatusTag = (status) => {
    switch (status) {
      case 'paid':
        return <Tag color="success">已支付</Tag>;
      case 'pending':
        return <Tag color="warning">待支付</Tag>;
      case 'failed':
        return <Tag color="error">支付失败</Tag>;
      case 'refunded':
        return <Tag color="default">已退款</Tag>;
      default:
        return <Tag>{status}</Tag>;
    }
  };

  // 表格列定义
  const columns = [
    {
      title: '订单号',
      dataIndex: 'order_number',
      key: 'order_number',
      render: (text) => <a onClick={() => handleViewDetail(text)}>{text}</a>
    },
    {
      title: '插件',
      dataIndex: 'plugin_name',
      key: 'plugin_name',
      render: (text, record) => (
        <Space>
          {record.plugin_icon ? (
            <Avatar src={record.plugin_icon} size="small" shape="square" />
          ) : (
            <Avatar icon={<AppstoreOutlined />} size="small" shape="square" />
          )}
          {text}
        </Space>
      )
    },
    {
      title: '金额',
      dataIndex: 'amount',
      key: 'amount',
      render: (text, record) => `${text} ${record.currency}`
    },
    {
      title: '计费周期',
      dataIndex: 'billing_cycle',
      key: 'billing_cycle',
      render: (text) => {
        switch (text) {
          case 'monthly':
            return '月付';
          case 'yearly':
            return '年付';
          case 'one_time':
            return '一次性';
          default:
            return text;
        }
      }
    },
    {
      title: '购买时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (text) => dayjs(text).format('YYYY-MM-DD HH:mm:ss')
    },
    {
      title: '到期时间',
      dataIndex: 'end_date',
      key: 'end_date',
      render: (text) => text ? dayjs(text).format('YYYY-MM-DD') : '永久有效'
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (text) => getStatusTag(text)
    },
    {
      title: '支付状态',
      dataIndex: 'payment_status',
      key: 'payment_status',
      render: (text) => getPaymentStatusTag(text)
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Button type="link" onClick={() => handleViewDetail(record.id)}>
            查看详情
          </Button>
        </Space>
      )
    }
  ];

  return (
    <div className="plugin-orders">
      <Card>
        <Title level={4}>插件订单</Title>
        <Divider />

        {/* 筛选区域 */}
        <div className="filter-section" style={{ marginBottom: 16 }}>
          <Row gutter={16} align="middle">
            <Col span={6}>
              <Space>
                <span>订单状态：</span>
                <Select
                  placeholder="选择订单状态"
                  allowClear
                  style={{ width: 180 }}
                  value={filters.status}
                  onChange={(value) => handleFilterChange('status', value)}
                >
                  <Select.Option value="active">有效</Select.Option>
                  <Select.Option value="expired">已过期</Select.Option>
                  <Select.Option value="cancelled">已取消</Select.Option>
                </Select>
              </Space>
            </Col>
            <Col span={10}>
              <Space>
                <span>日期范围：</span>
                <RangePicker
                  value={filters.date_range}
                  onChange={(dates) => handleFilterChange('date_range', dates)}
                  placeholder={['开始日期', '结束日期']}
                />
              </Space>
            </Col>
            <Col span={6}>
              <Space>
                <Button type="primary" icon={<SearchOutlined />} onClick={handleSearch}>
                  搜索
                </Button>
                <Button icon={<ReloadOutlined />} onClick={handleReset}>
                  重置
                </Button>
              </Space>
            </Col>
          </Row>
        </div>

        {/* 订单表格 */}
        <Table
          columns={columns}
          dataSource={orders}
          rowKey="id"
          pagination={pagination}
          loading={loading}
          onChange={handleTableChange}
          locale={{
            emptyText: <Empty description="暂无订单记录" />
          }}
        />

        {/* 订单详情模态框 */}
        <Modal
          title="订单详情"
          open={detailModalVisible}
          onCancel={handleCloseDetailModal}
          footer={[
            <Button key="close" onClick={handleCloseDetailModal}>
              关闭
            </Button>
          ]}
          width={800}
        >
          {orderDetail ? (
            <div>
              <Descriptions title="基本信息" bordered>
                <Descriptions.Item label="订单号" span={3}>
                  {orderDetail.order_number}
                </Descriptions.Item>
                <Descriptions.Item label="插件名称" span={3}>
                  <Space>
                    {orderDetail.plugin_icon ? (
                      <Avatar src={orderDetail.plugin_icon} size="small" shape="square" />
                    ) : (
                      <Avatar icon={<AppstoreOutlined />} size="small" shape="square" />
                    )}
                    {orderDetail.plugin_name}
                  </Space>
                </Descriptions.Item>
                <Descriptions.Item label="订单金额">
                  {orderDetail.amount} {orderDetail.currency}
                </Descriptions.Item>
                <Descriptions.Item label="计费周期">
                  {orderDetail.billing_cycle === 'monthly'
                    ? '月付'
                    : orderDetail.billing_cycle === 'yearly'
                    ? '年付'
                    : '一次性'}
                </Descriptions.Item>
                <Descriptions.Item label="购买周期">
                  {orderDetail.duration} {orderDetail.billing_cycle === 'monthly'
                    ? '个月'
                    : orderDetail.billing_cycle === 'yearly'
                    ? '年'
                    : ''}
                </Descriptions.Item>
                <Descriptions.Item label="订单状态">
                  {getStatusTag(orderDetail.status)}
                </Descriptions.Item>
                <Descriptions.Item label="支付状态">
                  {getPaymentStatusTag(orderDetail.payment_status)}
                </Descriptions.Item>
                <Descriptions.Item label="支付方式">
                  {orderDetail.payment_method === 'online' ? '在线支付' :
                   orderDetail.payment_method === 'offline' ? '线下支付' :
                   orderDetail.payment_method === 'free' ? '免费' :
                   orderDetail.payment_method}
                </Descriptions.Item>
                <Descriptions.Item label="生效时间">
                  {dayjs(orderDetail.start_date).format('YYYY-MM-DD HH:mm:ss')}
                </Descriptions.Item>
                <Descriptions.Item label="到期时间" span={2}>
                  {orderDetail.end_date
                    ? dayjs(orderDetail.end_date).format('YYYY-MM-DD HH:mm:ss')
                    : '永久有效'}
                </Descriptions.Item>
                <Descriptions.Item label="购买人">
                  {orderDetail.purchased_by?.name || '未知用户'}
                </Descriptions.Item>
                <Descriptions.Item label="购买时间" span={2}>
                  {dayjs(orderDetail.created_at).format('YYYY-MM-DD HH:mm:ss')}
                </Descriptions.Item>
              </Descriptions>

              {orderDetail.service_provider_order && (
                <div style={{ marginTop: 24 }}>
                  <Descriptions title="服务商订单信息" bordered>
                    <Descriptions.Item label="服务商订单号" span={3}>
                      {orderDetail.service_provider_order.order_no}
                    </Descriptions.Item>
                    <Descriptions.Item label="订单状态">
                      {getStatusTag(orderDetail.service_provider_order.status)}
                    </Descriptions.Item>
                    <Descriptions.Item label="支付方式">
                      {orderDetail.service_provider_order.payment_method === 'online' ? '在线支付' :
                       orderDetail.service_provider_order.payment_method === 'offline' ? '线下支付' :
                       orderDetail.service_provider_order.payment_method === 'free' ? '免费' :
                       orderDetail.service_provider_order.payment_method}
                    </Descriptions.Item>
                    <Descriptions.Item label="支付时间">
                      {orderDetail.service_provider_order.payment_time
                        ? dayjs(orderDetail.service_provider_order.payment_time).format('YYYY-MM-DD HH:mm:ss')
                        : '-'}
                    </Descriptions.Item>
                  </Descriptions>
                </div>
              )}
            </div>
          ) : (
            <div style={{ textAlign: 'center', padding: '20px 0' }}>
              <Spin />
              <p>加载中...</p>
            </div>
          )}
        </Modal>
      </Card>
    </div>
  );
};

export default PluginOrders;
