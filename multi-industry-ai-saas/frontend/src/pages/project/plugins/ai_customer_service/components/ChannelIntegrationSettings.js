import React, { useState, useEffect } from 'react';
import {
  Card, Form, Select, Switch, Button, message, 
  Typography, Divider, Space, Row, Col, Spin, Alert, Tooltip,
  Modal, Input, Table, Tag, Popconfirm, Upload
} from 'antd';
import { 
  SettingOutlined, 
  WechatOutlined, 
  GroupOutlined, 
  MessageOutlined,
  SaveOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  UploadOutlined,
  QrcodeOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;
const { TextArea } = Input;

/**
 * 渠道集成设置组件
 */
const ChannelIntegrationSettings = () => {
  const [form] = Form.useForm();
  const [channelForm] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);
  const [channels, setChannels] = useState([]);
  const [channelModalVisible, setChannelModalVisible] = useState(false);
  const [editingChannel, setEditingChannel] = useState(null);
  const [qrCodeVisible, setQrCodeVisible] = useState(false);
  const [qrCodeData, setQrCodeData] = useState(null);

  // 获取项目ID
  const getProjectId = () => {
    const projectId = localStorage.getItem('project_id');
    if (projectId && projectId !== 'null' && projectId !== 'undefined') {
      return projectId;
    }

    const userData = JSON.parse(localStorage.getItem('user_data') || '{}');
    if (userData && userData.project_id) {
      localStorage.setItem('project_id', userData.project_id);
      return userData.project_id;
    }

    return null;
  };

  // 渠道类型配置
  const channelTypes = [
    { 
      key: 'wechat_personal', 
      label: '个人微信', 
      icon: <WechatOutlined />, 
      color: '#52c41a',
      description: '个人微信群管理，支持群聊自动回复',
      requiresQr: true
    },
    { 
      key: 'wechat_work', 
      label: '企业微信', 
      icon: <WechatOutlined />, 
      color: '#1890ff',
      description: '企业微信群管理，通过API接入',
      requiresQr: false
    },
    { 
      key: 'dingtalk', 
      label: '钉钉', 
      icon: <GroupOutlined />, 
      color: '#ff6900',
      description: '钉钉群管理，机器人接入',
      requiresQr: false
    },
    { 
      key: 'feishu', 
      label: '飞书', 
      icon: <MessageOutlined />, 
      color: '#722ed1',
      description: '飞书群管理，应用接入',
      requiresQr: false
    },
  ];

  useEffect(() => {
    const projectId = getProjectId();
    if (projectId) {
      loadChannels();
    }
  }, []);

  const loadChannels = async () => {
    setLoading(true);
    try {
      const projectId = getProjectId();
      if (!projectId) return;

      const response = await fetch(`/api/v1/project/${projectId}/plugin/ai-customer-service/channels/`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setChannels(data.data || data || []);
      } else {
        // 使用示例数据作为回退
        setChannels([
          {
            id: '1',
            name: '客户群1',
            channel_type: 'wechat_personal',
            status: 'active',
            member_count: 128,
            auto_reply_enabled: true,
            created_at: '2025-01-01T00:00:00Z'
          },
          {
            id: '2',
            name: 'VIP客户群',
            channel_type: 'wechat_work',
            status: 'active',
            member_count: 67,
            auto_reply_enabled: true,
            created_at: '2025-01-01T00:00:00Z'
          }
        ]);
      }
    } catch (error) {
      console.error('加载渠道失败:', error);
      message.error('加载渠道列表失败');
    } finally {
      setLoading(false);
    }
  };

  const handleSaveChannel = async (values) => {
    try {
      setSaving(true);
      const projectId = getProjectId();
      if (!projectId) {
        message.error('无法获取项目ID');
        return;
      }
      
      const url = editingChannel 
        ? `/api/v1/project/${projectId}/plugin/ai-customer-service/channels/${editingChannel.id}`
        : `/api/v1/project/${projectId}/plugin/ai-customer-service/channels/`;
      
      const method = editingChannel ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(values)
      });

      if (response.ok) {
        message.success(editingChannel ? '渠道更新成功' : '渠道创建成功');
        setChannelModalVisible(false);
        setEditingChannel(null);
        channelForm.resetFields();
        await loadChannels();
      } else {
        message.error('保存渠道失败');
      }
    } catch (error) {
      console.error('保存渠道失败:', error);
      message.error('保存渠道失败');
    } finally {
      setSaving(false);
    }
  };

  const handleDeleteChannel = async (channelId) => {
    try {
      const projectId = getProjectId();
      if (!projectId) {
        message.error('无法获取项目ID');
        return;
      }

      const response = await fetch(`/api/v1/project/${projectId}/plugin/ai-customer-service/channels/${channelId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        message.success('渠道删除成功');
        await loadChannels();
      } else {
        message.error('删除渠道失败');
      }
    } catch (error) {
      console.error('删除渠道失败:', error);
      message.error('删除渠道失败');
    }
  };

  const handleConnectChannel = async (channelType) => {
    if (channelType === 'wechat_personal') {
      // 个人微信需要扫码登录
      try {
        const projectId = getProjectId();
        const response = await fetch(`/api/v1/project/${projectId}/plugin/ai-customer-service/channels/qr/${channelType}/`, {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          }
        });

        if (response.ok) {
          const data = await response.json();
          setQrCodeData(data);
          setQrCodeVisible(true);
        } else {
          message.error('获取二维码失败');
        }
      } catch (error) {
        console.error('获取二维码失败:', error);
        message.error('获取二维码失败');
      }
    } else {
      // 其他渠道通过配置接入
      setEditingChannel(null);
      channelForm.resetFields();
      channelForm.setFieldsValue({ channel_type: channelType });
      setChannelModalVisible(true);
    }
  };

  const getChannelTypeInfo = (type) => {
    return channelTypes.find(ct => ct.key === type) || {};
  };

  const getStatusTag = (status) => {
    const statusConfig = {
      active: { color: 'success', text: '活跃' },
      inactive: { color: 'default', text: '离线' },
      connecting: { color: 'processing', text: '连接中' },
      error: { color: 'error', text: '错误' }
    };
    const config = statusConfig[status] || statusConfig.inactive;
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  const channelColumns = [
    {
      title: '渠道名称',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => {
        const typeInfo = getChannelTypeInfo(record.channel_type);
        return (
          <Space>
            <span style={{ color: typeInfo.color }}>{typeInfo.icon}</span>
            <Text strong>{text}</Text>
          </Space>
        );
      }
    },
    {
      title: '渠道类型',
      dataIndex: 'channel_type',
      key: 'channel_type',
      render: (type) => {
        const typeInfo = getChannelTypeInfo(type);
        return <Text>{typeInfo.label}</Text>;
      }
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: getStatusTag
    },
    {
      title: '成员数',
      dataIndex: 'member_count',
      key: 'member_count',
      render: (count) => <Text>{count || 0} 人</Text>
    },
    {
      title: '自动回复',
      dataIndex: 'auto_reply_enabled',
      key: 'auto_reply_enabled',
      render: (enabled) => enabled ? 
        <CheckCircleOutlined style={{ color: '#52c41a' }} /> : 
        <ExclamationCircleOutlined style={{ color: '#faad14' }} />
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space>
          <Button
            size="small"
            icon={<EditOutlined />}
            onClick={() => {
              setEditingChannel(record);
              channelForm.setFieldsValue(record);
              setChannelModalVisible(true);
            }}
          >
            编辑
          </Button>
          <Popconfirm
            title="确定删除这个渠道吗？"
            onConfirm={() => handleDeleteChannel(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button
              size="small"
              danger
              icon={<DeleteOutlined />}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      )
    }
  ];

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>
          <Text>加载渠道配置中...</Text>
        </div>
      </div>
    );
  }

  return (
    <div style={{ padding: '0' }}>
      <Alert
        message="渠道集成设置"
        description={
          <div>
            <p>• <strong>多平台支持</strong>：接入微信、企业微信、钉钉、飞书等主流平台</p>
            <p>• <strong>群管理</strong>：自动回复、群活跃度监控、智能运营</p>
            <p>• <strong>AI集成</strong>：AI客服可以在各个渠道中提供智能服务</p>
            <p>• <strong>统一管理</strong>：所有渠道消息集中处理和分析</p>
          </div>
        }
        type="info"
        showIcon
        style={{ marginBottom: 24 }}
      />

      {/* 快速接入 */}
      <Card 
        title={
          <Space>
            <PlusOutlined />
            <Text strong>快速接入渠道</Text>
          </Space>
        } 
        style={{ marginBottom: 24 }}
      >
        <Row gutter={[16, 16]}>
          {channelTypes.map(channelType => (
            <Col span={6} key={channelType.key}>
              <Card
                size="small"
                hoverable
                style={{ textAlign: 'center', height: '120px' }}
                onClick={() => handleConnectChannel(channelType.key)}
              >
                <div style={{ fontSize: '24px', color: channelType.color, marginBottom: '8px' }}>
                  {channelType.icon}
                </div>
                <Text strong style={{ display: 'block', marginBottom: '4px' }}>
                  {channelType.label}
                </Text>
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  {channelType.description}
                </Text>
              </Card>
            </Col>
          ))}
        </Row>
      </Card>

      {/* 已接入渠道 */}
      <Card
        title={
          <Space>
            <GroupOutlined />
            <Text strong>已接入渠道</Text>
          </Space>
        }
        extra={
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => {
              setEditingChannel(null);
              channelForm.resetFields();
              setChannelModalVisible(true);
            }}
          >
            手动添加渠道
          </Button>
        }
      >
        <Table
          columns={channelColumns}
          dataSource={channels}
          rowKey="id"
          loading={loading}
          pagination={false}
        />
      </Card>

      {/* 渠道配置弹窗 */}
      <Modal
        title={editingChannel ? '编辑渠道' : '添加渠道'}
        open={channelModalVisible}
        onOk={() => channelForm.submit()}
        onCancel={() => {
          setChannelModalVisible(false);
          setEditingChannel(null);
          channelForm.resetFields();
        }}
        confirmLoading={saving}
        width={600}
      >
        <Form
          form={channelForm}
          layout="vertical"
          onFinish={handleSaveChannel}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="渠道名称"
                rules={[{ required: true, message: '请输入渠道名称' }]}
              >
                <Input placeholder="输入渠道名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="channel_type"
                label="渠道类型"
                rules={[{ required: true, message: '请选择渠道类型' }]}
              >
                <Select placeholder="选择渠道类型">
                  {channelTypes.map(type => (
                    <Option key={type.key} value={type.key}>
                      <Space>
                        <span style={{ color: type.color }}>{type.icon}</span>
                        {type.label}
                      </Space>
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="description"
            label="渠道描述"
          >
            <TextArea rows={3} placeholder="输入渠道描述" />
          </Form.Item>

          <Divider>API配置</Divider>

          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) => 
              prevValues.channel_type !== currentValues.channel_type
            }
          >
            {({ getFieldValue }) => {
              const channelType = getFieldValue('channel_type');
              
              if (channelType === 'wechat_work') {
                return (
                  <Row gutter={16}>
                    <Col span={8}>
                      <Form.Item name="corp_id" label="Corp ID">
                        <Input placeholder="企业微信Corp ID" />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item name="corp_secret" label="Corp Secret">
                        <Input.Password placeholder="企业微信Corp Secret" />
                      </Form.Item>
                    </Col>
                    <Col span={8}>
                      <Form.Item name="agent_id" label="Agent ID">
                        <Input placeholder="应用Agent ID" />
                      </Form.Item>
                    </Col>
                  </Row>
                );
              }
              
              if (channelType === 'dingtalk') {
                return (
                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item name="app_key" label="App Key">
                        <Input placeholder="钉钉App Key" />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item name="app_secret" label="App Secret">
                        <Input.Password placeholder="钉钉App Secret" />
                      </Form.Item>
                    </Col>
                  </Row>
                );
              }
              
              if (channelType === 'feishu') {
                return (
                  <Row gutter={16}>
                    <Col span={12}>
                      <Form.Item name="app_id" label="App ID">
                        <Input placeholder="飞书App ID" />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item name="app_secret" label="App Secret">
                        <Input.Password placeholder="飞书App Secret" />
                      </Form.Item>
                    </Col>
                  </Row>
                );
              }
              
              return null;
            }}
          </Form.Item>

          <Divider>功能设置</Divider>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="auto_reply_enabled"
                label="启用自动回复"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="ai_enabled"
                label="启用AI客服"
                valuePropName="checked"
              >
                <Switch defaultChecked />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="monitoring_enabled"
                label="启用监控"
                valuePropName="checked"
              >
                <Switch defaultChecked />
              </Form.Item>
            </Col>
          </Row>

          <Divider>适配器设置</Divider>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="message_adapter_enabled"
                label="消息适配器"
                valuePropName="checked"
                tooltip="启用后将自动转换不同平台的消息格式"
              >
                <Switch defaultChecked />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="webhook_adapter_enabled"
                label="Webhook适配器"
                valuePropName="checked"
                tooltip="启用后将支持通过Webhook接收消息"
              >
                <Switch defaultChecked />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="protocol_adapter_type"
                label="协议适配器"
                tooltip="选择适合的通信协议适配器"
              >
                <Select placeholder="选择协议适配器" allowClear>
                  <Option value="http">HTTP 适配器</Option>
                  <Option value="websocket">WebSocket 适配器</Option>
                  <Option value="webhook">Webhook 适配器</Option>
                  <Option value="polling">轮询适配器</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="message_format_adapter"
                label="消息格式适配器"
                tooltip="自动转换消息格式以适配不同平台"
              >
                <Select placeholder="选择格式适配器" allowClear>
                  <Option value="json">JSON 格式</Option>
                  <Option value="xml">XML 格式</Option>
                  <Option value="text">纯文本格式</Option>
                  <Option value="markdown">Markdown 格式</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="adapter_webhook_url"
            label="适配器Webhook URL"
            tooltip="用于接收平台消息的Webhook地址"
          >
            <Input placeholder="https://your-domain.com/webhook/channel-adapter" />
          </Form.Item>

          <Form.Item
            name="adapter_retry_config"
            label="适配器重试配置"
          >
            <Row gutter={8}>
              <Col span={8}>
                <Input 
                  addonBefore="最大重试次数" 
                  placeholder="3" 
                  defaultValue="3"
                />
              </Col>
              <Col span={8}>
                <Input 
                  addonBefore="重试间隔(秒)" 
                  placeholder="5" 
                  defaultValue="5"
                />
              </Col>
              <Col span={8}>
                <Input 
                  addonBefore="超时时间(秒)" 
                  placeholder="30" 
                  defaultValue="30"
                />
              </Col>
            </Row>
          </Form.Item>
        </Form>
      </Modal>

      {/* 二维码弹窗 */}
      <Modal
        title="微信登录"
        open={qrCodeVisible}
        footer={null}
        onCancel={() => setQrCodeVisible(false)}
        width={400}
      >
        <div style={{ textAlign: 'center', padding: '20px' }}>
          <QrcodeOutlined style={{ fontSize: '200px', color: '#1890ff' }} />
          <div style={{ marginTop: '16px' }}>
            <Text>请使用微信扫描二维码登录</Text>
          </div>
          <div style={{ marginTop: '8px' }}>
            <Text type="secondary">登录成功后将自动接入微信群管理</Text>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default ChannelIntegrationSettings; 