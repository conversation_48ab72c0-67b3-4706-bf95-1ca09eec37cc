import React, { useState, useEffect } from 'react';
import {
  Table,
  Button,
  Space,
  Tag,
  Input,
  DatePicker,
  Form,
  Select,
  Modal,
  message,
  Typography,
  Card,
  Divider,
  Upload,
  Tabs,
  Row,
  Col,
  Statistic,
  Badge,
  Tooltip,
  Image,
  Descriptions,
  Timeline,
  Alert,
  Calendar
} from 'antd';
import {
  PlusOutlined,
  SearchOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  ReloadOutlined,
  UploadOutlined,
  CalendarOutlined,
  ShopOutlined,
  PictureOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ClockCircleOutlined
} from '@ant-design/icons';
import dayjs from "dayjs";
import apiService from '../../../../services/api';

const { Title, Text, Paragraph } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;
const { TextArea } = Input;
const { TabPane } = Tabs;

// 活动状态标签颜色映射
const statusColors = {
  draft: 'default',
  pending: 'processing',
  active: 'success',
  completed: 'blue',
  cancelled: 'error'
};

// 活动状态文本映射
const statusTexts = {
  draft: '草稿',
  pending: '待开始',
  active: '进行中',
  completed: '已结束',
  cancelled: '已取消'
};

/**
 * 营销活动维护组件
 * 用于创建和管理门店营销活动
 */
const MarketingActivities = ({ stores }) => {
  const [activeTab, setActiveTab] = useState('1');
  const [loading, setLoading] = useState(false);
  const [activities, setActivities] = useState([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });
  const [searchParams, setSearchParams] = useState({
    status: '',
    store_id: '',
    dateRange: null,
    search: ''
  });
  const [modalVisible, setModalVisible] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [editingActivity, setEditingActivity] = useState(null);
  const [currentActivity, setCurrentActivity] = useState(null);
  const [form] = Form.useForm();
  const [searchForm] = Form.useForm();
  const [fileList, setFileList] = useState([]);
  const [activityStats, setActivityStats] = useState({
    total: 0,
    draft: 0,
    pending: 0,
    active: 0,
    completed: 0,
    cancelled: 0
  });

  // 在 fetchActivities 和 handleSearch 里，修正 store_id 传参
  const isValidUUID = (id) => /^[0-9a-fA-F-]{36}$/.test(id);

  // 获取活动列表
  const fetchActivities = async (params = {}) => {
    setLoading(true);
    try {
      const { current, pageSize } = pagination;
      const { status, store_id, dateRange, search } = searchParams;
      const queryParams = {
        skip: (current - 1) * pageSize,
        limit: pageSize,
        status,
        search,
        ...params
      };
      if (store_id && isValidUUID(store_id)) {
        queryParams.store_id = store_id;
      }
      if (dateRange && dateRange[0] && dateRange[1]) {
        queryParams.start_date = dateRange[0].format('YYYY-MM-DD');
        queryParams.end_date = dateRange[1].format('YYYY-MM-DD');
      }
      const response = await apiService.project.marketingActivity.getList(queryParams);

      if (response) {
        setActivities(response.items || []);
        setPagination({
          ...pagination,
          current: response.page || 1,
          pageSize: response.size || 10,
          total: response.total || 0
        });

        // 更新活动统计
        updateActivityStats(response.items || []);
      }
    } catch (error) {
      console.error('获取活动列表失败:', error);
      message.error('获取活动列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 更新活动统计
  const updateActivityStats = (activityList) => {
    const stats = {
      total: activityList.length,
      draft: 0,
      pending: 0,
      active: 0,
      completed: 0,
      cancelled: 0
    };

    activityList.forEach(activity => {
      if (activity.status) {
        stats[activity.status] = (stats[activity.status] || 0) + 1;
      }
    });

    setActivityStats(stats);
  };

  // 处理表格分页变化
  const handleTableChange = (pagination, filters, sorter) => {
    setPagination(pagination);
    fetchActivities({
      skip: (pagination.current - 1) * pagination.pageSize,
      limit: pagination.pageSize
    });
  };

  // 处理搜索表单提交
  const handleSearch = (values) => {
    const { status, store_id, dateRange, search } = values;
    setSearchParams({ status, store_id, dateRange, search });
    setPagination({ ...pagination, current: 1 });
    const params = {
      skip: 0,
      status,
      search,
      start_date: dateRange && dateRange[0] ? dateRange[0].format('YYYY-MM-DD') : undefined,
      end_date: dateRange && dateRange[1] ? dateRange[1].format('YYYY-MM-DD') : undefined
    };
    if (store_id && isValidUUID(store_id)) {
      params.store_id = store_id;
    }
    fetchActivities(params);
  };

  // 重置搜索表单
  const handleReset = () => {
    searchForm.resetFields();
    const defaultParams = {
      status: '',
      store_id: '',
      dateRange: null,
      search: ''
    };
    setSearchParams(defaultParams);
    setPagination({ ...pagination, current: 1 });
    fetchActivities({
      skip: 0
    });
  };

  // 打开活动表单
  const handleOpenActivityForm = (activity = null) => {
    setEditingActivity(activity);
    setFileList([]);
    form.resetFields();

    if (activity) {
      // 编辑活动
      form.setFieldsValue({
        title: activity.title,
        description: activity.description,
        store_ids: activity.store_ids || [],
        start_date: activity.start_date ? dayjs(activity.start_date) : null,
        end_date: activity.end_date ? dayjs(activity.end_date) : null,
        budget: activity.budget,
        target_audience: activity.target_audience,
        expected_results: activity.expected_results,
        status: activity.status
      });

      // 设置图片列表
      if (activity.image_url) {
        setFileList([
          {
            uid: '-1',
            name: '活动图片',
            status: 'done',
            url: activity.image_url
          }
        ]);
      }
    } else {
      // 创建活动
      form.setFieldsValue({
        start_date: dayjs(),
        end_date: dayjs().add(7, 'days'),
        status: 'draft'
      });
    }

    setModalVisible(true);
  };

  // 查看活动详情
  const handleViewActivityDetail = (activity) => {
    setCurrentActivity(activity);
    setDetailModalVisible(true);
  };

  // 提交活动表单
  const handleSubmitActivity = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      // 准备提交数据
      const activityData = {
        ...values,
        start_date: values.start_date ? values.start_date.format('YYYY-MM-DD') : null,
        end_date: values.end_date ? values.end_date.format('YYYY-MM-DD') : null,
        image_url: fileList.length > 0 ? (fileList[0].response?.url || fileList[0].url) : null
      };

      if (editingActivity) {
        // 更新活动
        await apiService.project.marketingActivity.update(editingActivity.id, activityData);
        message.success('活动更新成功');
      } else {
        // 创建活动
        await apiService.project.marketingActivity.create(activityData);
        message.success('活动创建成功');
      }

      setModalVisible(false);
      fetchActivities();
    } catch (error) {
      console.error('提交活动失败:', error);
      if (error.errorFields) {
        // 表单验证错误
        return;
      }
      message.error('提交活动失败: ' + (error.response?.data?.detail || error.message));
    } finally {
      setLoading(false);
    }
  };

  // 删除活动
  const handleDeleteActivity = async (id) => {
    setLoading(true);
    try {
      await apiService.project.marketingActivity.delete(id);
      message.success('活动删除成功');
      fetchActivities();
    } catch (error) {
      console.error('删除活动失败:', error);
      message.error('删除活动失败: ' + (error.response?.data?.detail || error.message));
    } finally {
      setLoading(false);
    }
  };

  // 更新活动状态
  const handleUpdateActivityStatus = async (id, status) => {
    setLoading(true);
    try {
      await apiService.project.marketingActivity.updateStatus(id, { status });
      message.success('活动状态更新成功');
      fetchActivities();
    } catch (error) {
      console.error('更新活动状态失败:', error);
      message.error('更新活动状态失败: ' + (error.response?.data?.detail || error.message));
    } finally {
      setLoading(false);
    }
  };

  // 处理标签页切换
  const handleTabChange = (key) => {
    setActiveTab(key);
  };

  // 获取活动状态信息
  const getActivityStatus = (activity) => {
    const now = dayjs();
    const startDate = dayjs(activity.start_date);
    const endDate = dayjs(activity.end_date);

    let status = '';
    let color = '';
    let text = '';

    if (activity.status === 'draft') {
      status = 'draft';
      color = 'default';
      text = '草稿';
    } else if (activity.status === 'cancelled') {
      status = 'cancelled';
      color = 'error';
      text = '已取消';
    } else if (now.isBefore(startDate)) {
      status = 'upcoming';
      color = 'warning';
      text = '即将开始';
    } else if (now.isAfter(endDate)) {
      status = 'ended';
      color = 'default';
      text = '已结束';
    } else {
      status = 'active';
      color = 'success';
      text = '进行中';
    }

    return { status, color, text };
  };

  // 日历单元格渲染函数
  const calendarCellRender = (value) => {
    // 获取当前日期
    const date = value.format('YYYY-MM-DD');

    // 筛选出当天的活动
    const dayActivities = activities.filter(activity => {
      // 活动开始日期
      const startDate = dayjs(activity.start_date).format('YYYY-MM-DD');
      // 活动结束日期
      const endDate = dayjs(activity.end_date).format('YYYY-MM-DD');

      // 检查当前日期是否在活动日期范围内
      return (date >= startDate && date <= endDate);
    });

    // 如果没有活动，返回 null
    if (dayActivities.length === 0) {
      return null;
    }

    // 返回活动列表
    return (
      <ul className="calendar-activity-list" style={{ margin: 0, padding: 0, listStyle: 'none' }}>
        {dayActivities.map(activity => {
          // 获取活动状态
          const { status, color, text } = getActivityStatus(activity);

          return (
            <li key={activity.id} style={{ marginBottom: '4px' }}>
              <Badge
                color={color}
                text={
                  <Tooltip title={activity.title}>
                    <span
                      style={{
                        cursor: 'pointer',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        whiteSpace: 'nowrap',
                        display: 'inline-block',
                        maxWidth: '100%'
                      }}
                      onClick={() => handleViewActivityDetail(activity)}
                    >
                      {activity.title}
                    </span>
                  </Tooltip>
                }
              />
            </li>
          );
        })}
      </ul>
    );
  };

  // 文件上传配置
  const uploadProps = {
    name: 'file',
    action: '/api/upload',
    headers: {
      authorization: 'Bearer ' + localStorage.getItem('token'),
    },
    beforeUpload: (file) => {
      const isImage = file.type.startsWith('image/');
      if (!isImage) {
        message.error('只能上传图片文件!');
      }
      return isImage || Upload.LIST_IGNORE;
    },
    onChange(info) {
      if (info.file.status === 'done') {
        message.success(`${info.file.name} 上传成功`);
      } else if (info.file.status === 'error') {
        message.error(`${info.file.name} 上传失败`);
      }
      setFileList(info.fileList.slice(-1)); // 只保留最后一个文件
    },
  };

  // 初始化
  useEffect(() => {
    fetchActivities();
  }, []);

  // 搜索表单
  const SearchForm = () => (
    <Form
      layout="inline"
      form={searchForm}
      initialValues={searchParams}
      onFinish={handleSearch}
      style={{ marginBottom: 16 }}
    >
      <Form.Item name="status" label="状态">
        <Select style={{ width: 120 }} allowClear>
          <Option value="">全部</Option>
          <Option value="draft">草稿</Option>
          <Option value="pending">待开始</Option>
          <Option value="active">进行中</Option>
          <Option value="completed">已结束</Option>
          <Option value="cancelled">已取消</Option>
        </Select>
      </Form.Item>

      <Form.Item name="store_id" label="门店">
        <Select style={{ width: 150 }} allowClear>
          <Option value="">全部</Option>
          {stores.map(store => (
            <Option key={store.id} value={store.id}>{store.name}</Option>
          ))}
        </Select>
      </Form.Item>

      <Form.Item name="dateRange" label="活动日期">
        <RangePicker style={{ width: 240 }} />
      </Form.Item>

      <Form.Item name="search" label="搜索">
        <Input
          placeholder="活动标题/描述"
          prefix={<SearchOutlined />}
          style={{ width: 200 }}
        />
      </Form.Item>

      <Form.Item>
        <Button type="primary" htmlType="submit">
          搜索
        </Button>
      </Form.Item>

      <Form.Item>
        <Button onClick={handleReset}>
          重置
        </Button>
      </Form.Item>
    </Form>
  );

  // 活动表格列定义
  const columns = [
    {
      title: '活动标题',
      dataIndex: 'title',
      key: 'title',
      render: (text, record) => (
        <a onClick={() => handleViewActivityDetail(record)}>{text}</a>
      )
    },
    {
      title: '门店',
      dataIndex: 'store_names',
      key: 'store_names',
      render: (stores) => {
        if (!stores || stores.length === 0) return '-';
        if (stores.length <= 2) return stores.join(', ');
        return (
          <Tooltip title={stores.join(', ')}>
            {`${stores[0]}, ${stores[1]} 等${stores.length}个门店`}
          </Tooltip>
        );
      }
    },
    {
      title: '开始日期',
      dataIndex: 'start_date',
      key: 'start_date',
      render: (text) => text ? dayjs(text).format('YYYY-MM-DD') : '-'
    },
    {
      title: '结束日期',
      dataIndex: 'end_date',
      key: 'end_date',
      render: (text) => text ? dayjs(text).format('YYYY-MM-DD') : '-'
    },
    {
      title: '预算',
      dataIndex: 'budget',
      key: 'budget',
      render: (text) => text ? `¥${parseFloat(text).toFixed(2)}` : '-'
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => (
        <Tag color={statusColors[status] || 'default'}>
          {statusTexts[status] || status}
        </Tag>
      )
    },
    {
      title: '创建人',
      dataIndex: 'creator_name',
      key: 'creator_name'
    },
    {
      title: '操作',
      key: 'action',
      width: 200,
      render: (_, record) => (
        <Space size="small">
          <Button
            type="link"
            size="small"
            icon={<EyeOutlined />}
            onClick={() => handleViewActivityDetail(record)}
          >
            查看
          </Button>

          {record.status === 'draft' && (
            <>
              <Button
                type="link"
                size="small"
                icon={<EditOutlined />}
                onClick={() => handleOpenActivityForm(record)}
              >
                编辑
              </Button>

              <Button
                type="link"
                danger
                size="small"
                icon={<DeleteOutlined />}
                onClick={() => {
                  Modal.confirm({
                    title: '确认删除',
                    content: `确定要删除活动 "${record.title}" 吗？此操作不可恢复。`,
                    okText: '删除',
                    okType: 'danger',
                    cancelText: '取消',
                    onOk: () => handleDeleteActivity(record.id)
                  });
                }}
              >
                删除
              </Button>
            </>
          )}

          {record.status === 'draft' && (
            <Button
              type="link"
              size="small"
              icon={<ClockCircleOutlined />}
              onClick={() => handleUpdateActivityStatus(record.id, 'pending')}
            >
              发布
            </Button>
          )}

          {record.status === 'pending' && (
            <Button
              type="link"
              size="small"
              icon={<CheckCircleOutlined />}
              onClick={() => handleUpdateActivityStatus(record.id, 'active')}
            >
              开始
            </Button>
          )}

          {record.status === 'active' && (
            <Button
              type="link"
              size="small"
              icon={<CheckCircleOutlined />}
              onClick={() => handleUpdateActivityStatus(record.id, 'completed')}
            >
              结束
            </Button>
          )}

          {(record.status === 'pending' || record.status === 'active') && (
            <Button
              type="link"
              danger
              size="small"
              icon={<CloseCircleOutlined />}
              onClick={() => handleUpdateActivityStatus(record.id, 'cancelled')}
            >
              取消
            </Button>
          )}
        </Space>
      )
    }
  ];

  // 自定义样式
  const calendarStyles = {
    '.calendar-activity-list': {
      margin: 0,
      padding: 0,
      listStyle: 'none',
      maxHeight: '80px',
      overflow: 'auto'
    },
    '.calendar-activity-list li': {
      marginBottom: '4px',
      fontSize: '12px',
      lineHeight: '1.5',
      whiteSpace: 'nowrap',
      overflow: 'hidden',
      textOverflow: 'ellipsis'
    },
    '.ant-picker-calendar-date-content': {
      height: '80px',
      overflow: 'hidden'
    }
  };

  return (
    <div className="marketing-activities" style={{ position: 'relative' }}>
      <style>
        {`
          .calendar-activity-list {
            margin: 0;
            padding: 0;
            list-style: none;
            max-height: 80px;
            overflow: auto;
          }
          .calendar-activity-list li {
            margin-bottom: 4px;
            font-size: 12px;
            line-height: 1.5;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }
          .ant-picker-calendar-date-content {
            height: 80px;
            overflow: hidden;
          }
        `}
      </style>
      <Tabs activeKey={activeTab} onChange={handleTabChange}>
        <TabPane tab="活动列表" key="1">
          <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between' }}>
            <div>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => handleOpenActivityForm()}
                style={{ marginRight: 8 }}
              >
                创建活动
              </Button>
              <Button
                icon={<ReloadOutlined />}
                onClick={() => fetchActivities()}
              >
                刷新
              </Button>
            </div>
          </div>

          <Row gutter={16} style={{ marginBottom: 16 }}>
            <Col span={4}>
              <Card>
                <Statistic
                  title="活动总数"
                  value={activityStats.total}
                  suffix="个"
                />
              </Card>
            </Col>
            <Col span={4}>
              <Card>
                <Statistic
                  title="草稿"
                  value={activityStats.draft}
                  suffix="个"
                  valueStyle={{ color: '#d9d9d9' }}
                />
              </Card>
            </Col>
            <Col span={4}>
              <Card>
                <Statistic
                  title="待开始"
                  value={activityStats.pending}
                  suffix="个"
                  valueStyle={{ color: '#1890ff' }}
                />
              </Card>
            </Col>
            <Col span={4}>
              <Card>
                <Statistic
                  title="进行中"
                  value={activityStats.active}
                  suffix="个"
                  valueStyle={{ color: '#52c41a' }}
                />
              </Card>
            </Col>
            <Col span={4}>
              <Card>
                <Statistic
                  title="已结束"
                  value={activityStats.completed}
                  suffix="个"
                  valueStyle={{ color: '#722ed1' }}
                />
              </Card>
            </Col>
            <Col span={4}>
              <Card>
                <Statistic
                  title="已取消"
                  value={activityStats.cancelled}
                  suffix="个"
                  valueStyle={{ color: '#f5222d' }}
                />
              </Card>
            </Col>
          </Row>

          <SearchForm />

          <Table
            columns={columns}
            dataSource={activities}
            rowKey="id"
            pagination={pagination}
            onChange={handleTableChange}
            loading={loading}
            scroll={{ x: 1200 }}
          />
        </TabPane>

        <TabPane tab="活动日历" key="2">
          <div style={{ padding: 16 }}>
            <Row gutter={16} style={{ marginBottom: 16 }}>
              <Col span={24}>
                <Card>
                  <Calendar
                    dateCellRender={calendarCellRender}
                    headerRender={({ value, type, onChange, onTypeChange }) => {
                      const start = 0;
                      const end = 12;
                      const monthOptions = [];

                      for (let i = start; i < end; i++) {
                        monthOptions.push(
                          <Select.Option key={i} value={i}>
                            {i + 1}月
                          </Select.Option>
                        );
                      }

                      const year = value.year();
                      const month = value.month();
                      const options = {
                        year: {
                          value: year,
                          onChange: (newYear) => {
                            const now = value.clone().year(newYear);
                            onChange(now);
                          },
                        },
                        month: {
                          value: month,
                          onChange: (newMonth) => {
                            const now = value.clone().month(newMonth);
                            onChange(now);
                          },
                        },
                      };

                      return (
                        <div style={{ padding: 8 }}>
                          <Row gutter={8} justify="end">
                            <Col>
                              <Select
                                size="small"
                                dropdownMatchSelectWidth={false}
                                value={options.month.value}
                                onChange={options.month.onChange}
                              >
                                {monthOptions}
                              </Select>
                            </Col>
                            <Col>
                              <DatePicker
                                size="small"
                                picker="year"
                                value={dayjs().year(options.year.value)}
                                onChange={(date) => {
                                  if (date) {
                                    options.year.onChange(date.year());
                                  }
                                }}
                              />
                            </Col>
                          </Row>
                        </div>
                      );
                    }}
                  />
                </Card>
              </Col>
            </Row>
          </div>
        </TabPane>
      </Tabs>

      {/* 活动表单 */}
      <Modal
        title={editingActivity ? '编辑活动' : '创建活动'}
        open={modalVisible}
        onOk={handleSubmitActivity}
        onCancel={() => setModalVisible(false)}
        width={700}
        confirmLoading={loading}
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Form.Item
            name="title"
            label="活动标题"
            rules={[{ required: true, message: '请输入活动标题' }]}
          >
            <Input placeholder="请输入活动标题" />
          </Form.Item>

          <Form.Item
            name="description"
            label="活动描述"
            rules={[{ required: true, message: '请输入活动描述' }]}
          >
            <TextArea rows={4} placeholder="请输入活动描述" />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="start_date"
                label="开始日期"
                rules={[{ required: true, message: '请选择开始日期' }]}
              >
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>

            <Col span={12}>
              <Form.Item
                name="end_date"
                label="结束日期"
                rules={[{ required: true, message: '请选择结束日期' }]}
              >
                <DatePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="store_ids"
            label="适用门店"
            rules={[{ required: true, message: '请选择至少一个门店' }]}
          >
            <Select
              mode="multiple"
              placeholder="请选择适用门店"
            >
              {stores.map(store => (
                <Option key={store.id} value={store.id}>{store.name}</Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="budget"
            label="活动预算"
          >
            <Input prefix="¥" suffix="元" type="number" step="0.01" placeholder="请输入活动预算" />
          </Form.Item>

          <Form.Item
            name="target_audience"
            label="目标受众"
          >
            <TextArea rows={2} placeholder="请描述活动的目标受众" />
          </Form.Item>

          <Form.Item
            name="expected_results"
            label="预期效果"
          >
            <TextArea rows={2} placeholder="请描述活动的预期效果" />
          </Form.Item>

          <Form.Item
            name="status"
            label="活动状态"
            rules={[{ required: true, message: '请选择活动状态' }]}
          >
            <Select placeholder="请选择活动状态">
              <Option value="draft">草稿</Option>
              <Option value="pending">待开始</Option>
              <Option value="active">进行中</Option>
              <Option value="completed">已结束</Option>
              <Option value="cancelled">已取消</Option>
            </Select>
          </Form.Item>

          <Form.Item
            label="活动图片"
          >
            <Upload
              {...uploadProps}
              fileList={fileList}
              listType="picture-card"
            >
              {fileList.length < 1 && <div>
                <PlusOutlined />
                <div style={{ marginTop: 8 }}>上传图片</div>
              </div>}
            </Upload>
          </Form.Item>
        </Form>
      </Modal>

      {/* 活动详情 */}
      <Modal
        title="活动详情"
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={[
          <Button key="back" onClick={() => setDetailModalVisible(false)}>
            关闭
          </Button>
        ]}
        width={800}
      >
        {currentActivity && (
          <div>
            <Row gutter={16}>
              <Col span={16}>
                <Title level={4}>{currentActivity.title}</Title>
                <Tag color={statusColors[currentActivity.status] || 'default'}>
                  {statusTexts[currentActivity.status] || currentActivity.status}
                </Tag>
              </Col>
              <Col span={8} style={{ textAlign: 'right' }}>
                <Text type="secondary">创建人: {currentActivity.creator_name}</Text>
              </Col>
            </Row>

            <Divider />

            <Row gutter={16}>
              <Col span={currentActivity.image_url ? 12 : 24}>
                <Descriptions column={1} bordered>
                  <Descriptions.Item label="活动描述">
                    {currentActivity.description || '-'}
                  </Descriptions.Item>
                  <Descriptions.Item label="活动时间">
                    {currentActivity.start_date ? dayjs(currentActivity.start_date).format('YYYY-MM-DD') : '-'} 至 {currentActivity.end_date ? dayjs(currentActivity.end_date).format('YYYY-MM-DD') : '-'}
                  </Descriptions.Item>
                  <Descriptions.Item label="适用门店">
                    {currentActivity.store_names && currentActivity.store_names.length > 0 ? currentActivity.store_names.join(', ') : '-'}
                  </Descriptions.Item>
                  <Descriptions.Item label="活动预算">
                    {currentActivity.budget ? `¥${parseFloat(currentActivity.budget).toFixed(2)}` : '-'}
                  </Descriptions.Item>
                  <Descriptions.Item label="目标受众">
                    {currentActivity.target_audience || '-'}
                  </Descriptions.Item>
                  <Descriptions.Item label="预期效果">
                    {currentActivity.expected_results || '-'}
                  </Descriptions.Item>
                </Descriptions>
              </Col>

              {currentActivity.image_url && (
                <Col span={12}>
                  <div style={{ textAlign: 'center' }}>
                    <Image
                      src={currentActivity.image_url}
                      alt={currentActivity.title}
                      style={{ maxWidth: '100%', maxHeight: 300 }}
                    />
                  </div>
                </Col>
              )}
            </Row>

            <Divider />

            <Title level={5}>活动时间线</Title>
            <Timeline>
              <Timeline.Item color="green">
                创建时间: {dayjs(currentActivity.created_at).format('YYYY-MM-DD HH:mm:ss')}
              </Timeline.Item>
              {currentActivity.updated_at && (
                <Timeline.Item color="blue">
                  最后更新: {dayjs(currentActivity.updated_at).format('YYYY-MM-DD HH:mm:ss')}
                </Timeline.Item>
              )}
              {currentActivity.start_date && (
                <Timeline.Item color={dayjs(currentActivity.start_date).isBefore(dayjs()) ? 'green' : 'blue'}>
                  开始时间: {dayjs(currentActivity.start_date).format('YYYY-MM-DD')}
                </Timeline.Item>
              )}
              {currentActivity.end_date && (
                <Timeline.Item color={dayjs(currentActivity.end_date).isBefore(dayjs()) ? 'green' : 'blue'}>
                  结束时间: {dayjs(currentActivity.end_date).format('YYYY-MM-DD')}
                </Timeline.Item>
              )}
            </Timeline>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default MarketingActivities;
