import React, { useState, useEffect, useCallback } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Progress,
  Tooltip,
  Modal,
  message,
  Input,
  Select,
  DatePicker,
  Row,
  Col,
  Statistic,
  Popconfirm,
  Checkbox,
  Dropdown,
  Menu,
  Badge,
  Typography,
  Empty,
  Spin
} from 'antd';
import {
  ReloadOutlined,
  DeleteOutlined,
  StopOutlined,
  SearchOutlined,
  FilterOutlined,
  DownOutlined,
  ExclamationCircleOutlined,
  EyeOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  SyncOutlined
} from '@ant-design/icons';
import { formatDistanceToNow } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import taskAPI from '../services/api/project/task';

const { Search } = Input;
const { Option } = Select;
const { RangePicker } = DatePicker;
const { Text } = Typography;

const TaskMonitor = () => {
  const [tasks, setTasks] = useState([]);
  const [loading, setLoading] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0
  });
  const [filters, setFilters] = useState({
    status: null,
    task_type: null,
    task_module: null,
    search: '',
    date_range: null
  });
  const [stats, setStats] = useState({});
  const [availableModules, setAvailableModules] = useState([]);

  // 状态配置
  const statusConfig = {
    pending: { color: 'blue', icon: <ClockCircleOutlined />, text: '等待中' },
    running: { color: 'orange', icon: <SyncOutlined spin />, text: '执行中' },
    success: { color: 'green', icon: <CheckCircleOutlined />, text: '成功' },
    failed: { color: 'red', icon: <CloseCircleOutlined />, text: '失败' },
    cancelled: { color: 'default', icon: <StopOutlined />, text: '已取消' }
  };

  // 模块配置
  const moduleConfig = {
    purchase: { text: '采购管理', color: 'blue' },
    inventory: { text: '库存管理', color: 'green' },
    sales: { text: '销售管理', color: 'orange' },
    ai: { text: 'AI处理', color: 'purple' },
    report: { text: '报表导出', color: 'cyan' },
    import: { text: '数据导入', color: 'geekblue' },
    sync: { text: '数据同步', color: 'magenta' }
  };

  // 加载任务列表
  const loadTasks = useCallback(async () => {
    setLoading(true);
    try {
      const params = {
        ...filters,
        limit: pagination.pageSize,
        offset: (pagination.current - 1) * pagination.pageSize
      };

      // 处理日期范围
      if (filters.date_range && filters.date_range.length === 2) {
        params.start_date = filters.date_range[0].format('YYYY-MM-DD');
        params.end_date = filters.date_range[1].format('YYYY-MM-DD');
      }

      const response = await taskAPI.getList(params);
      
      if (response.success) {
        setTasks(response.data.tasks || []);
        setPagination(prev => ({
          ...prev,
          total: response.data.total || 0
        }));
        setStats(response.data.status_stats || {});
        setAvailableModules(response.data.available_modules || []);
      }
    } catch (error) {
      console.error('加载任务列表失败:', error);
      message.error('加载任务列表失败');
    } finally {
      setLoading(false);
    }
  }, [filters, pagination.current, pagination.pageSize]);

  // 初始加载
  useEffect(() => {
    loadTasks();
  }, [loadTasks]);

  // 删除任务
  const handleDelete = async (taskId) => {
    try {
      const response = await taskAPI.delete(taskId);
      if (response.success) {
        message.success('删除成功');
        loadTasks();
      }
    } catch (error) {
      console.error('删除任务失败:', error);
      message.error('删除任务失败');
    }
  };

  // 批量删除
  const handleBatchDelete = async () => {
    if (selectedRowKeys.length === 0) {
      message.warning('请选择要删除的任务');
      return;
    }

    Modal.confirm({
      title: '确认删除',
      icon: <ExclamationCircleOutlined />,
      content: `确定要删除选中的 ${selectedRowKeys.length} 个任务吗？`,
      onOk: async () => {
        try {
          const response = await taskAPI.batchDelete(selectedRowKeys);
          if (response.success) {
            message.success(`批量删除完成：成功 ${response.data.deleted} 个，失败 ${response.data.failed} 个`);
            setSelectedRowKeys([]);
            loadTasks();
          }
        } catch (error) {
          console.error('批量删除失败:', error);
          message.error('批量删除失败');
        }
      }
    });
  };

  // 取消任务
  const handleCancel = async (taskId) => {
    try {
      const response = await taskAPI.cancel(taskId);
      if (response.success) {
        message.success('任务已取消');
        loadTasks();
      }
    } catch (error) {
      console.error('取消任务失败:', error);
      message.error('取消任务失败');
    }
  };

  // 查看任务详情
  const handleViewDetail = (task) => {
    Modal.info({
      title: '任务详情',
      width: 600,
      content: (
        <div>
          <p><strong>任务ID:</strong> {task.id}</p>
          <p><strong>任务名称:</strong> {task.task_name}</p>
          <p><strong>任务类型:</strong> {task.task_type}</p>
          <p><strong>状态:</strong> <Tag color={statusConfig[task.status]?.color}>{statusConfig[task.status]?.text}</Tag></p>
          <p><strong>进度:</strong> {task.progress}%</p>
          <p><strong>创建时间:</strong> {task.created_at}</p>
          <p><strong>开始时间:</strong> {task.started_at || '未开始'}</p>
          <p><strong>完成时间:</strong> {task.completed_at || '未完成'}</p>
          {task.error_message && (
            <p><strong>错误信息:</strong> <Text type="danger">{task.error_message}</Text></p>
          )}
          {task.description && (
            <p><strong>描述:</strong> {task.description}</p>
          )}
        </div>
      )
    });
  };

  // 表格列定义
  const columns = [
    {
      title: '任务名称',
      dataIndex: 'task_name',
      key: 'task_name',
      ellipsis: true,
      render: (text, record) => (
        <div>
          <div>{text}</div>
          <Text type="secondary" style={{ fontSize: '12px' }}>
            {record.task_type}
          </Text>
        </div>
      )
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status) => {
        const config = statusConfig[status] || {};
        return (
          <Tag color={config.color} icon={config.icon}>
            {config.text}
          </Tag>
        );
      }
    },
    {
      title: '进度',
      dataIndex: 'progress',
      key: 'progress',
      width: 120,
      render: (progress, record) => {
        if (record.status === 'pending') {
          return <Text type="secondary">等待中</Text>;
        }
        return (
          <Progress
            percent={progress || 0}
            size="small"
            status={record.status === 'failed' ? 'exception' : 'active'}
            showInfo={false}
          />
        );
      }
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 120,
      render: (time) => (
        <Tooltip title={time}>
          <Text style={{ fontSize: '12px' }}>
            {formatDistanceToNow(new Date(time), { addSuffix: true, locale: zhCN })}
          </Text>
        </Tooltip>
      )
    },
    {
      title: '耗时',
      key: 'duration',
      width: 80,
      render: (_, record) => {
        if (!record.started_at) return '-';
        
        const start = new Date(record.started_at);
        const end = record.completed_at ? new Date(record.completed_at) : new Date();
        const duration = Math.round((end - start) / 1000);
        
        if (duration < 60) return `${duration}s`;
        if (duration < 3600) return `${Math.floor(duration / 60)}m`;
        return `${Math.floor(duration / 3600)}h`;
      }
    },
    {
      title: '操作',
      key: 'actions',
      width: 150,
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              type="text"
              size="small"
              icon={<EyeOutlined />}
              onClick={() => handleViewDetail(record)}
            />
          </Tooltip>
          
          {record.is_running && (
            <Tooltip title="取消任务">
              <Popconfirm
                title="确定要取消这个任务吗？"
                onConfirm={() => handleCancel(record.id)}
              >
                <Button
                  type="text"
                  size="small"
                  icon={<StopOutlined />}
                  danger
                />
              </Popconfirm>
            </Tooltip>
          )}
          
          {record.is_completed && (
            <Tooltip title="删除任务">
              <Popconfirm
                title="确定要删除这个任务吗？"
                onConfirm={() => handleDelete(record.id)}
              >
                <Button
                  type="text"
                  size="small"
                  icon={<DeleteOutlined />}
                  danger
                />
              </Popconfirm>
            </Tooltip>
          )}
        </Space>
      )
    }
  ];

  // 行选择配置
  const rowSelection = {
    selectedRowKeys,
    onChange: setSelectedRowKeys,
    getCheckboxProps: (record) => ({
      disabled: record.is_running, // 运行中的任务不能选择删除
    }),
  };

  // 筛选菜单
  const filterMenu = (
    <Menu>
      <Menu.Item key="clear" onClick={() => setFilters({
        status: null,
        task_type: null,
        task_module: null,
        search: '',
        date_range: null
      })}>
        清除所有筛选
      </Menu.Item>
    </Menu>
  );

  return (
    <div style={{ padding: '24px' }}>
      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: '24px' }}>
        <Col span={6}>
          <Card>
            <Statistic
              title="总任务数"
              value={pagination.total}
              prefix={<ClockCircleOutlined />}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="运行中"
              value={stats.running || 0}
              prefix={<SyncOutlined spin />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="成功"
              value={stats.success || 0}
              prefix={<CheckCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col span={6}>
          <Card>
            <Statistic
              title="失败"
              value={stats.failed || 0}
              prefix={<CloseCircleOutlined />}
              valueStyle={{ color: '#ff4d4f' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 筛选和操作栏 */}
      <Card style={{ marginBottom: '16px' }}>
        <Row gutter={16} align="middle">
          <Col span={6}>
            <Search
              placeholder="搜索任务名称或类型"
              value={filters.search}
              onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
              onSearch={loadTasks}
              enterButton={<SearchOutlined />}
            />
          </Col>
          
          <Col span={4}>
            <Select
              placeholder="状态筛选"
              value={filters.status}
              onChange={(value) => setFilters(prev => ({ ...prev, status: value }))}
              allowClear
              style={{ width: '100%' }}
            >
              {Object.entries(statusConfig).map(([key, config]) => (
                <Option key={key} value={key}>
                  <Tag color={config.color} icon={config.icon}>
                    {config.text}
                  </Tag>
                </Option>
              ))}
            </Select>
          </Col>
          
          <Col span={4}>
            <Select
              placeholder="模块筛选"
              value={filters.task_module}
              onChange={(value) => setFilters(prev => ({ ...prev, task_module: value }))}
              allowClear
              style={{ width: '100%' }}
            >
              {availableModules.map(module => (
                <Option key={module} value={module}>
                  <Tag color={moduleConfig[module]?.color}>
                    {moduleConfig[module]?.text || module}
                  </Tag>
                </Option>
              ))}
            </Select>
          </Col>
          
          <Col span={6}>
            <RangePicker
              value={filters.date_range}
              onChange={(dates) => setFilters(prev => ({ ...prev, date_range: dates }))}
              style={{ width: '100%' }}
            />
          </Col>
          
          <Col span={4}>
            <Space>
              <Button
                icon={<ReloadOutlined />}
                onClick={loadTasks}
                loading={loading}
              >
                刷新
              </Button>
              
              <Dropdown overlay={filterMenu} trigger={['click']}>
                <Button icon={<FilterOutlined />}>
                  筛选 <DownOutlined />
                </Button>
              </Dropdown>
            </Space>
          </Col>
        </Row>
        
        {/* 批量操作 */}
        {selectedRowKeys.length > 0 && (
          <Row style={{ marginTop: '16px' }}>
            <Col span={24}>
              <Space>
                <Text>已选择 {selectedRowKeys.length} 项</Text>
                <Button
                  type="primary"
                  danger
                  icon={<DeleteOutlined />}
                  onClick={handleBatchDelete}
                >
                  批量删除
                </Button>
                <Button onClick={() => setSelectedRowKeys([])}>
                  取消选择
                </Button>
              </Space>
            </Col>
          </Row>
        )}
      </Card>

      {/* 任务列表 */}
      <Card>
        <Table
          rowSelection={rowSelection}
          columns={columns}
          dataSource={tasks}
          rowKey="id"
          loading={loading}
          pagination={{
            ...pagination,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
            onChange: (page, pageSize) => {
              setPagination(prev => ({
                ...prev,
                current: page,
                pageSize
              }));
            }
          }}
          locale={{
            emptyText: (
              <Empty
                image={Empty.PRESENTED_IMAGE_SIMPLE}
                description="暂无任务数据"
              />
            )
          }}
        />
      </Card>
    </div>
  );
};

export default TaskMonitor; 