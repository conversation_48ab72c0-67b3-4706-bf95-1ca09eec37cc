from typing import List, Optional, Dict, Any, Union
from pydantic import BaseModel, UUID4, Field
from datetime import datetime

# 库存项基础模型
class InventoryItemBase(BaseModel):
    """库存项基础模型"""
    product_id: UUID4
    warehouse_id: UUID4
    quantity: int = 0
    min_quantity: int = 0
    max_quantity: Optional[int] = None
    is_slow_moving: bool = False


class InventoryItemCreate(InventoryItemBase):
    """创建库存项模型"""
    pass


class InventoryItemUpdate(BaseModel):
    """更新库存项模型"""
    product_id: Optional[UUID4] = None
    warehouse_id: Optional[UUID4] = None
    quantity: Optional[int] = None
    min_quantity: Optional[int] = None
    max_quantity: Optional[int] = None
    is_slow_moving: Optional[bool] = None


class InventoryItemInDB(InventoryItemBase):
    """数据库库存项模型"""
    id: UUID4
    tenant_id: Optional[UUID4] = None
    project_id: UUID4
    created_at: datetime
    updated_at: datetime
    created_by: Optional[UUID4] = None
    updated_by: Optional[UUID4] = None
    last_movement_date: Optional[datetime] = None
    product_name: Optional[str] = None  # 非数据库字段，用于前端展示
    product_sku: Optional[str] = None  # 非数据库字段，用于前端展示
    product_category: Optional[str] = None  # 非数据库字段，用于前端展示
    warehouse_name: Optional[str] = None  # 非数据库字段，用于前端展示

    class Config:
        model_config = {"from_attributes": True}


class InventoryItemResponse(BaseModel):
    """库存项响应模型"""
    success: bool
    message: Optional[str] = None
    data: Optional[InventoryItemInDB] = None


class InventoryItemListResponse(BaseModel):
    """库存项列表响应模型"""
    success: bool
    message: Optional[str] = None
    items: List[InventoryItemInDB]
    total: int
    page: Optional[int] = None
    size: Optional[int] = None
    pages: Optional[int] = None


# 库存历史记录模型
class InventoryHistoryBase(BaseModel):
    """库存历史记录基础模型"""
    inventory_item_id: UUID4
    action_type: str
    previous_quantity: int
    new_quantity: int
    change_amount: int
    reference_id: Optional[UUID4] = None
    reference_type: Optional[str] = None
    notes: Optional[str] = None


class InventoryHistoryCreate(InventoryHistoryBase):
    """创建库存历史记录模型"""
    pass


class InventoryHistoryInDB(InventoryHistoryBase):
    """数据库库存历史记录模型"""
    id: UUID4
    tenant_id: Optional[UUID4] = None
    project_id: UUID4
    created_at: datetime
    created_by: Optional[UUID4] = None
    operator_name: Optional[str] = None  # 非数据库字段，用于前端展示

    class Config:
        model_config = {"from_attributes": True}


class InventoryHistoryResponse(BaseModel):
    """库存历史记录响应模型"""
    success: bool
    message: Optional[str] = None
    data: Optional[InventoryHistoryInDB] = None


class InventoryHistoryListResponse(BaseModel):
    """库存历史记录列表响应模型"""
    success: bool
    message: Optional[str] = None
    history: List[InventoryHistoryInDB]
    total: int
    page: Optional[int] = None
    size: Optional[int] = None
    pages: Optional[int] = None


# 库存调拨单模型
class InventoryTransferItemBase(BaseModel):
    """库存调拨单明细基础模型"""
    product_id: UUID4
    quantity: int
    notes: Optional[str] = None


class InventoryTransferBase(BaseModel):
    """库存调拨单基础模型"""
    source_warehouse_id: UUID4
    target_warehouse_id: UUID4
    transfer_date: datetime
    notes: Optional[str] = None
    items: List[InventoryTransferItemBase]


class InventoryTransferCreate(InventoryTransferBase):
    """创建库存调拨单模型"""
    pass


class InventoryTransferUpdate(BaseModel):
    """更新库存调拨单模型"""
    status: Optional[str] = None
    notes: Optional[str] = None


class InventoryTransferItemInDB(InventoryTransferItemBase):
    """数据库库存调拨单明细模型"""
    id: UUID4
    transfer_id: UUID4
    product_name: Optional[str] = None  # 非数据库字段，用于前端展示
    product_sku: Optional[str] = None  # 非数据库字段，用于前端展示

    class Config:
        model_config = {"from_attributes": True}


class InventoryTransferInDB(BaseModel):
    """数据库库存调拨单模型"""
    id: UUID4
    tenant_id: Optional[UUID4] = None
    project_id: UUID4
    transfer_number: str
    source_warehouse_id: UUID4
    target_warehouse_id: UUID4
    status: str
    transfer_date: datetime
    notes: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    created_by: Optional[UUID4] = None
    updated_by: Optional[UUID4] = None
    source_warehouse_name: Optional[str] = None  # 非数据库字段，用于前端展示
    target_warehouse_name: Optional[str] = None  # 非数据库字段，用于前端展示
    items: List[InventoryTransferItemInDB]

    class Config:
        model_config = {"from_attributes": True}


class InventoryTransferResponse(BaseModel):
    """库存调拨单响应模型"""
    success: bool
    message: Optional[str] = None
    data: Optional[InventoryTransferInDB] = None


class InventoryTransferListResponse(BaseModel):
    """库存调拨单列表响应模型"""
    success: bool
    message: Optional[str] = None
    items: List[InventoryTransferInDB]
    total: int
    page: Optional[int] = None
    size: Optional[int] = None
    pages: Optional[int] = None


# 库存盘点单模型
class InventoryCheckItemBase(BaseModel):
    """库存盘点单明细基础模型"""
    product_id: UUID4
    system_quantity: int
    actual_quantity: int
    difference: int
    notes: Optional[str] = None


class InventoryCheckBase(BaseModel):
    """库存盘点单基础模型"""
    warehouse_id: UUID4
    check_date: datetime
    notes: Optional[str] = None
    items: List[InventoryCheckItemBase]


class InventoryCheckCreate(InventoryCheckBase):
    """创建库存盘点单模型"""
    pass


class InventoryCheckUpdate(BaseModel):
    """更新库存盘点单模型"""
    status: Optional[str] = None
    notes: Optional[str] = None


class InventoryCheckItemInDB(InventoryCheckItemBase):
    """数据库库存盘点单明细模型"""
    id: UUID4
    check_id: UUID4
    product_name: Optional[str] = None  # 非数据库字段，用于前端展示
    product_sku: Optional[str] = None  # 非数据库字段，用于前端展示

    class Config:
        model_config = {"from_attributes": True}


class InventoryCheckInDB(BaseModel):
    """数据库库存盘点单模型"""
    id: UUID4
    tenant_id: Optional[UUID4] = None
    project_id: UUID4
    check_number: str
    warehouse_id: UUID4
    status: str
    check_date: datetime
    notes: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    created_by: Optional[UUID4] = None
    updated_by: Optional[UUID4] = None
    warehouse_name: Optional[str] = None  # 非数据库字段，用于前端展示
    items: List[InventoryCheckItemInDB]

    class Config:
        model_config = {"from_attributes": True}


class InventoryCheckResponse(BaseModel):
    """库存盘点单响应模型"""
    success: bool
    message: Optional[str] = None
    data: Optional[InventoryCheckInDB] = None


class InventoryCheckListResponse(BaseModel):
    """库存盘点单列表响应模型"""
    success: bool
    message: Optional[str] = None
    items: List[InventoryCheckInDB]
    total: int
    page: Optional[int] = None
    size: Optional[int] = None
    pages: Optional[int] = None


# 库存导入相关模型
class InventoryImportPreviewItem(BaseModel):
    """库存导入预览项"""
    name: str
    code: str
    category: Optional[str] = None
    current_stock: int
    min_stock: int
    unit: str
    status: str  # new, update, error
    error_message: Optional[str] = None


class InventoryImportPreview(BaseModel):
    """库存导入预览"""
    preview: List[InventoryImportPreviewItem]
    total: int
    new_items: int
    updated_items: int
    errors: int


class InventoryImportResponse(BaseModel):
    """库存导入响应"""
    success: bool
    message: Optional[str] = None
    data: Optional[InventoryImportPreview] = None


class InventoryImportConfirm(BaseModel):
    """库存导入确认"""
    warehouse_id: UUID4


class InventoryImportConfirmResponse(BaseModel):
    """库存导入确认响应"""
    success: bool
    message: Optional[str] = None
    imported_count: int


# 库存预警相关模型
class InventoryAlertItem(BaseModel):
    """库存预警项模型"""
    id: UUID4
    product_id: UUID4
    product_name: str
    product_sku: Optional[str] = None
    warehouse_id: UUID4
    warehouse_name: str
    quantity: int
    min_quantity: int
    max_quantity: int = 0
    alert_type: str = Field(..., description="预警类型: low(低库存), high(高库存), slow_moving(滞销)")
    created_at: datetime
    updated_at: datetime

    class Config:
        model_config = {"from_attributes": True}


class InventoryAlertResponse(BaseModel):
    """库存预警响应模型"""
    success: bool = True
    message: Optional[str] = None
    items: List[InventoryAlertItem]
    total: int
    page: Optional[int] = None
    size: Optional[int] = None
    pages: Optional[int] = None


class InventoryAlertSummary(BaseModel):
    """库存预警摘要模型"""
    low_stock_count: int
    high_stock_count: int
    slow_moving_count: int
    total_alert_count: int


class InventoryAlertSummaryResponse(BaseModel):
    """库存预警摘要响应模型"""
    success: bool = True
    message: Optional[str] = None
    data: InventoryAlertSummary
