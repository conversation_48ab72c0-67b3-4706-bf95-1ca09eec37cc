import React, { useState, useEffect, useRef } from 'react';
import {
  Card,
  Input,
  Button,
  Avatar,
  Typography,
  Space,
  Divider,
  Tag,
  Tooltip,
  List,
  message,
  Spin,
  Badge,
  FloatButton,
  Drawer,
  Alert,
  Collapse,
  Row,
  Col,
  Progress,
  Statistic,
  Modal,
  Form,
  Select,
  DatePicker,
  InputNumber,
  Switch
} from 'antd';
import {
  RobotOutlined,
  SendOutlined,
  QuestionCircleOutlined,
  BulbOutlined,
  CloseOutlined,
  MessageOutlined,
  BarChartOutlined,
  TrophyOutlined,
  UserOutlined,
  HeartOutlined,
  ThunderboltOutlined,
  StarOutlined,
  PlusOutlined,
  SettingOutlined,
  GiftOutlined
} from '@ant-design/icons';
import dayjs from 'dayjs';
import aiAssistantApi from '../service/api/aiAssistant';
import marketingGameApi from '../service/api/marketingGame';

const { Text, Paragraph } = Typography;
const { TextArea } = Input;
const { Panel } = Collapse;
const { Option } = Select;
const { RangePicker } = DatePicker;

const AIAssistant = ({ 
  gameId, 
  visible, 
  onClose, 
  position = 'right',
  style = {},
  embedded = false,
  onGameCreated = () => {},
  onGameUpdated = () => {}
}) => {
  const [messages, setMessages] = useState([]);
  const [inputValue, setInputValue] = useState('');
  const [loading, setLoading] = useState(false);
  const [typing, setTyping] = useState(false);
  const [suggestions, setSuggestions] = useState([]);
  const [gameAnalysis, setGameAnalysis] = useState(null);
  const [quickReplies, setQuickReplies] = useState([]);
  const [conversationId, setConversationId] = useState(null);
  const [aiPersonality, setAiPersonality] = useState('friendly');
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [createGameData, setCreateGameData] = useState(null);
  const [form] = Form.useForm();
  const messagesEndRef = useRef(null);

  // 预设的快速回复
  const defaultQuickReplies = [
    { text: '游戏规则是什么？', type: 'question' },
    { text: '如何提高中奖率？', type: 'question' },
    { text: '还有什么奖品？', type: 'question' },
    { text: '如何分享给朋友？', type: 'question' },
    { text: '遇到问题怎么办？', type: 'question' }
  ];

  useEffect(() => {
    if (visible && gameId) {
      initializeChat();
      fetchGameSuggestions();
      fetchGameAnalysis();
    }
  }, [visible, gameId]);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const initializeChat = async () => {
    try {
      const response = await aiAssistantApi.initializeChat(gameId);
      if (response.success) {
        const welcomeMessage = {
          id: Date.now(),
          content: response.data.welcome_message,
          sender: 'ai',
          timestamp: new Date(),
          type: 'welcome'
        };
        setMessages([welcomeMessage]);
        setConversationId(response.data.conversation_id);
        setAiPersonality(response.data.personality || 'friendly');
        setQuickReplies(response.data.quick_replies || defaultQuickReplies);
      }
    } catch (error) {
      console.error('初始化AI助手失败:', error);
      // 添加默认欢迎消息
      const defaultWelcome = {
        id: Date.now(),
        content: '🎮 您好！欢迎来到营销游戏！我是您的AI助手，有什么问题都可以问我哦！',
        sender: 'ai',
        timestamp: new Date(),
        type: 'welcome'
      };
      setMessages([defaultWelcome]);
      setQuickReplies(defaultQuickReplies);
    }
  };

  const fetchGameSuggestions = async () => {
    try {
      const response = await aiAssistantApi.getGameSuggestions(gameId);
      if (response.success) {
        setSuggestions(response.data || []);
      }
    } catch (error) {
      console.error('获取游戏建议失败:', error);
    }
  };

  const fetchGameAnalysis = async () => {
    try {
      const response = await aiAssistantApi.getGameAnalysis(gameId);
      if (response.success) {
        setGameAnalysis(response.data);
      }
    } catch (error) {
      console.error('获取游戏分析失败:', error);
    }
  };

  const sendMessage = async (content = inputValue) => {
    if (!content.trim()) return;

    const userMessage = {
      id: Date.now(),
      content: content.trim(),
      sender: 'user',
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');
    setLoading(true);
    setTyping(true);

    try {
      const response = await aiAssistantApi.sendMessage(gameId, {
        message: content.trim(),
        conversation_id: conversationId
      });

      if (response.success) {
        const aiMessage = {
          id: Date.now() + 1,
          content: response.data.reply,
          sender: 'ai',
          timestamp: new Date(),
          suggestions: response.data.suggestions || [],
          actions: response.data.actions || []
        };

        // 模拟打字效果
        setTimeout(() => {
          setMessages(prev => [...prev, aiMessage]);
          setTyping(false);
          
          // 更新建议
          if (response.data.new_suggestions) {
            setSuggestions(prev => [...prev, ...response.data.new_suggestions]);
          }
        }, 1000);
      } else {
        throw new Error(response.message || 'AI回复失败');
      }
    } catch (error) {
      console.error('发送消息失败:', error);
      
      // 添加错误回复
      const errorMessage = {
        id: Date.now() + 1,
        content: '抱歉，我暂时无法回答这个问题。请稍后再试或联系客服。',
        sender: 'ai',
        timestamp: new Date(),
        type: 'error'
      };
      
      setTimeout(() => {
        setMessages(prev => [...prev, errorMessage]);
        setTyping(false);
      }, 1000);
    } finally {
      setLoading(false);
    }
  };

  const handleQuickReply = (reply) => {
    sendMessage(reply.text);
  };

  const handleAction = async (action) => {
    try {
      switch (action.type) {
        case 'create_basic':
        case 'create_enhanced':
          setCreateGameData({
            gameType: action.data?.gameType || 'lucky_draw',
            enhanced: action.type === 'create_enhanced'
          });
          setShowCreateForm(true);
          break;
          
        case 'setup_prizes':
          // 跳转到奖品设置页面
          message.info('正在跳转到奖品设置页面...');
          break;
          
        case 'edit_prizes':
          // 打开奖品编辑界面
          message.info('正在打开奖品编辑界面...');
          break;
          
        case 'setup_sharing':
          // 打开分享设置界面
          message.info('正在打开分享设置界面...');
          break;
          
        case 'edit_rules':
          // 打开规则编辑界面
          message.info('正在打开规则编辑界面...');
          break;
          
        case 'apply_suggestions':
          await applySuggestions();
          break;
          
        case 'export_report':
          await exportReport();
          break;
          
        default:
          message.info(`执行操作：${action.text}`);
      }
    } catch (error) {
      console.error('执行操作失败:', error);
      message.error('操作执行失败，请稍后重试');
    }
  };

  const applySuggestions = async () => {
    try {
      message.info('正在应用优化建议...');
      
      // 添加确认消息
      const confirmMessage = {
        id: Date.now(),
        content: '✅ 已为您应用优化建议！建议已经生效，您可以查看游戏设置确认更改。',
        sender: 'ai',
        timestamp: new Date(),
        type: 'success'
      };
      setMessages(prev => [...prev, confirmMessage]);
      
      // 刷新建议列表
      fetchGameSuggestions();
    } catch (error) {
      console.error('应用建议失败:', error);
      message.error('应用建议失败，请稍后重试');
    }
  };

  const exportReport = async () => {
    try {
      message.info('正在生成数据报告...');
      
      // 模拟报告生成
      setTimeout(() => {
        const reportMessage = {
          id: Date.now(),
          content: '📊 数据报告已生成！\n\n报告包含：\n• 参与用户分析\n• 中奖率统计\n• 时间分布图表\n• 优化建议\n\n报告已发送到您的邮箱。',
          sender: 'ai',
          timestamp: new Date(),
          type: 'success'
        };
        setMessages(prev => [...prev, reportMessage]);
      }, 2000);
    } catch (error) {
      console.error('导出报告失败:', error);
      message.error('导出报告失败，请稍后重试');
    }
  };

  const handleCreateGame = async (values) => {
    try {
      setLoading(true);
      
      // 处理时间范围
      if (values.time_range) {
        values.start_time = values.time_range[0].toISOString();
        values.end_time = values.time_range[1].toISOString();
        delete values.time_range;
      }

      // 设置游戏类型
      values.game_type = createGameData.gameType;

      const response = await marketingGameApi.createGame(values);
      
      if (response.success) {
        message.success('游戏创建成功！');
        setShowCreateForm(false);
        form.resetFields();
        
        // 添加成功消息
        const successMessage = {
          id: Date.now(),
          content: `🎉 太棒了！游戏"${values.name}"创建成功！\n\n游戏已经可以使用了。您可以：\n• 设置奖品和中奖概率\n• 配置分享功能\n• 开始推广活动\n\n需要我帮您设置奖品吗？`,
          sender: 'ai',
          timestamp: new Date(),
          type: 'success',
          actions: [
            {
              text: '设置奖品',
              type: 'setup_prizes'
            },
            {
              text: '配置分享',
              type: 'setup_sharing'
            }
          ]
        };
        setMessages(prev => [...prev, successMessage]);
        
        // 通知父组件
        onGameCreated(response.data);
      } else {
        throw new Error(response.message || '创建失败');
      }
    } catch (error) {
      console.error('创建游戏失败:', error);
      message.error(error.message || '创建游戏失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const getMessageIcon = (message) => {
    switch (message.type) {
      case 'welcome':
        return <HeartOutlined style={{ color: '#ff4d4f' }} />;
      case 'success':
        return <StarOutlined style={{ color: '#52c41a' }} />;
      case 'error':
        return <QuestionCircleOutlined style={{ color: '#faad14' }} />;
      default:
        return <RobotOutlined style={{ color: '#1890ff' }} />;
    }
  };

  const getSuggestionIcon = (suggestion) => {
    switch (suggestion.type) {
      case 'optimization':
        return <ThunderboltOutlined style={{ color: '#faad14' }} />;
      case 'promotion':
        return <BulbOutlined style={{ color: '#1890ff' }} />;
      case 'ux':
        return <UserOutlined style={{ color: '#52c41a' }} />;
      default:
        return <BulbOutlined style={{ color: '#722ed1' }} />;
    }
  };

  const getSuggestionColor = (priority) => {
    switch (priority) {
      case 'high':
        return '#ff4d4f';
      case 'medium':
        return '#faad14';
      case 'low':
        return '#52c41a';
      default:
        return '#1890ff';
    }
  };

  const renderMessage = (message) => (
    <div
      key={message.id}
      style={{
        display: 'flex',
        justifyContent: message.sender === 'user' ? 'flex-end' : 'flex-start',
        marginBottom: 16
      }}
    >
      {message.sender === 'ai' && (
        <Avatar
          icon={getMessageIcon(message)}
          style={{ marginRight: 8, flexShrink: 0 }}
        />
      )}
      
      <div
        style={{
          maxWidth: '80%',
          padding: '12px 16px',
          borderRadius: message.sender === 'user' ? '16px 16px 4px 16px' : '16px 16px 16px 4px',
          backgroundColor: message.sender === 'user' ? '#1890ff' : '#f6f8fa',
          color: message.sender === 'user' ? 'white' : '#000',
          position: 'relative'
        }}
      >
        <Paragraph 
          style={{ 
            margin: 0, 
            color: message.sender === 'user' ? 'white' : '#000',
            whiteSpace: 'pre-wrap'
          }}
        >
          {message.content}
        </Paragraph>
        
        {message.suggestions && message.suggestions.length > 0 && (
          <div style={{ marginTop: 8 }}>
            {message.suggestions.map((suggestion, index) => (
              <Tag
                key={index}
                color="blue"
                style={{ marginBottom: 4, cursor: 'pointer' }}
                onClick={() => sendMessage(suggestion)}
              >
                {suggestion}
              </Tag>
            ))}
          </div>
        )}
        
        {message.actions && message.actions.length > 0 && (
          <div style={{ marginTop: 8 }}>
            <Space wrap>
              {message.actions.map((action, index) => (
                <Button
                  key={index}
                  size="small"
                  type="primary"
                  ghost
                  onClick={() => handleAction(action)}
                  icon={action.type.includes('create') ? <PlusOutlined /> : 
                        action.type.includes('setup') ? <SettingOutlined /> :
                        action.type.includes('edit') ? <SettingOutlined /> : undefined}
                >
                  {action.text}
                </Button>
              ))}
            </Space>
          </div>
        )}
        
        <div style={{ 
          fontSize: '11px', 
          color: message.sender === 'user' ? 'rgba(255,255,255,0.6)' : '#999',
          marginTop: 4
        }}>
          {dayjs(message.timestamp).format('HH:mm')}
        </div>
      </div>
      
      {message.sender === 'user' && (
        <Avatar
          icon={<UserOutlined />}
          style={{ 
            marginLeft: 8, 
            backgroundColor: '#1890ff',
            flexShrink: 0
          }}
        />
      )}
    </div>
  );

  const chatContent = (
    <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      {/* 头部 */}
      <div style={{ 
        padding: '16px 20px', 
        borderBottom: '1px solid #f0f0f0',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        color: 'white'
      }}>
        <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Space>
            <Avatar icon={<RobotOutlined />} style={{ backgroundColor: 'rgba(255,255,255,0.2)' }} />
            <div>
              <div style={{ fontWeight: 'bold' }}>AI营销助手</div>
              <div style={{ fontSize: '12px', opacity: 0.8 }}>
                {aiPersonality === 'friendly' ? '友好助手' : 
                 aiPersonality === 'professional' ? '专业顾问' : '活泼伙伴'}
              </div>
            </div>
          </Space>
          {!embedded && (
            <Button 
              type="text" 
              icon={<CloseOutlined />} 
              onClick={onClose}
              style={{ color: 'white' }}
            />
          )}
        </div>
      </div>

      {/* 游戏分析面板 */}
      {gameAnalysis && (
        <div style={{ padding: '12px 20px', background: '#fafafa', borderBottom: '1px solid #f0f0f0' }}>
          <Collapse size="small" ghost>
            <Panel 
              header={
                <Space>
                  <BarChartOutlined style={{ color: '#1890ff' }} />
                  <Text strong style={{ fontSize: '12px' }}>游戏数据概览</Text>
                </Space>
              } 
              key="analysis"
            >
              <Row gutter={16}>
                <Col span={6}>
                  <Statistic 
                    title="参与人数" 
                    value={gameAnalysis.participants || 0}
                    prefix={<UserOutlined />}
                    valueStyle={{ fontSize: '14px' }}
                  />
                </Col>
                <Col span={6}>
                  <Statistic 
                    title="中奖率" 
                    value={gameAnalysis.win_rate || 0}
                    suffix="%"
                    prefix={<TrophyOutlined />}
                    valueStyle={{ fontSize: '14px' }}
                  />
                </Col>
                <Col span={6}>
                  <Statistic 
                    title="分享率" 
                    value={gameAnalysis.share_rate || 0}
                    suffix="%"
                    prefix={<MessageOutlined />}
                    valueStyle={{ fontSize: '14px' }}
                  />
                </Col>
                <Col span={6}>
                  <Statistic 
                    title="总评分" 
                    value={gameAnalysis.overall_score || 0}
                    prefix={<StarOutlined />}
                    valueStyle={{ fontSize: '14px' }}
                  />
                </Col>
              </Row>
            </Panel>
          </Collapse>
        </div>
      )}

      {/* 消息列表 */}
      <div style={{ 
        flex: 1, 
        padding: '16px 20px', 
        overflowY: 'auto',
        background: '#fff'
      }}>
        {messages.map(renderMessage)}
        
        {typing && (
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: 16 }}>
            <Avatar icon={<RobotOutlined />} style={{ marginRight: 8 }} />
            <div style={{
              padding: '12px 16px',
              borderRadius: '16px 16px 16px 4px',
              backgroundColor: '#f6f8fa'
            }}>
              <Spin size="small" />
              <Text style={{ marginLeft: 8, color: '#999' }}>AI助手正在思考...</Text>
            </div>
          </div>
        )}
        
        <div ref={messagesEndRef} />
      </div>

      {/* 快速回复 */}
      {quickReplies.length > 0 && (
        <div style={{ 
          padding: '8px 20px', 
          borderTop: '1px solid #f0f0f0',
          background: '#fafafa'
        }}>
          <div style={{ marginBottom: 8 }}>
            <Text type="secondary" style={{ fontSize: '12px' }}>💡 快速提问</Text>
          </div>
          <div style={{ display: 'flex', flexWrap: 'wrap', gap: 6 }}>
            {quickReplies.slice(0, 3).map((reply, index) => (
              <Tag
                key={index}
                color="processing"
                style={{ cursor: 'pointer', margin: 0 }}
                onClick={() => handleQuickReply(reply)}
              >
                {reply.text}
              </Tag>
            ))}
          </div>
        </div>
      )}

      {/* 输入框 */}
      <div style={{ 
        padding: '16px 20px', 
        borderTop: '1px solid #f0f0f0',
        background: '#fff'
      }}>
        <Space.Compact style={{ width: '100%' }}>
          <TextArea
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            placeholder="输入您的问题..."
            autoSize={{ minRows: 1, maxRows: 3 }}
            onPressEnter={(e) => {
              if (!e.shiftKey) {
                e.preventDefault();
                sendMessage();
              }
            }}
            style={{ resize: 'none' }}
          />
          <Button
            type="primary"
            icon={<SendOutlined />}
            loading={loading}
            onClick={() => sendMessage()}
            disabled={!inputValue.trim()}
          >
            发送
          </Button>
        </Space.Compact>
      </div>

      {/* 智能建议 */}
      {suggestions.length > 0 && (
        <div style={{ 
          padding: '16px 20px', 
          borderTop: '1px solid #f0f0f0',
          background: '#fafafa'
        }}>
          <Collapse size="small" ghost>
            <Panel 
              header={
                <Space>
                  <BulbOutlined style={{ color: '#faad14' }} />
                  <Text strong style={{ fontSize: '12px' }}>
                    智能建议 
                    <Badge count={suggestions.length} size="small" style={{ marginLeft: 8 }} />
                  </Text>
                </Space>
              } 
              key="suggestions"
            >
              <List
                size="small"
                dataSource={suggestions.slice(0, 3)}
                renderItem={(suggestion) => (
                  <List.Item
                    actions={[
                      <Button 
                        size="small" 
                        type="link"
                        onClick={() => handleAction(suggestion)}
                      >
                        应用
                      </Button>
                    ]}
                  >
                    <List.Item.Meta
                      avatar={getSuggestionIcon(suggestion)}
                      title={
                        <Space>
                          <Text style={{ fontSize: '12px' }}>{suggestion.title}</Text>
                          <Tag 
                            color={getSuggestionColor(suggestion.priority)}
                            size="small"
                          >
                            {suggestion.priority}
                          </Tag>
                        </Space>
                      }
                      description={
                        <Text 
                          type="secondary" 
                          style={{ fontSize: '11px' }}
                          ellipsis={{ tooltip: suggestion.message }}
                        >
                          {suggestion.message}
                        </Text>
                      }
                    />
                  </List.Item>
                )}
              />
            </Panel>
          </Collapse>
        </div>
      )}
    </div>
  );

  if (embedded) {
    return (
      <>
        <Card 
          style={{ height: '500px', ...style }}
          bodyStyle={{ padding: 0, height: '100%' }}
        >
          {chatContent}
        </Card>

        {/* 游戏创建表单模态框 */}
        <Modal
          title={`创建${createGameData?.enhanced ? '增强版' : '基础版'}营销游戏`}
          open={showCreateForm}
          onCancel={() => setShowCreateForm(false)}
          footer={null}
          width={600}
          destroyOnClose
        >
          <Form
            form={form}
            layout="vertical"
            onFinish={handleCreateGame}
            initialValues={{
              game_type: createGameData?.gameType || 'lucky_draw',
              max_draws_per_user: 1,
              time_range: [dayjs(), dayjs().add(7, 'day')]
            }}
          >
            <Form.Item
              label="游戏名称"
              name="name"
              rules={[{ required: true, message: '请输入游戏名称' }]}
            >
              <Input placeholder="请输入游戏名称" />
            </Form.Item>

            <Form.Item
              label="游戏描述"
              name="description"
            >
              <TextArea 
                rows={3} 
                placeholder="请输入游戏描述，介绍游戏玩法和奖品"
              />
            </Form.Item>

            <Form.Item
              label="活动时间"
              name="time_range"
              rules={[{ required: true, message: '请选择活动时间' }]}
            >
              <RangePicker 
                showTime
                style={{ width: '100%' }}
                placeholder={['开始时间', '结束时间']}
              />
            </Form.Item>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  label="每人最大参与次数"
                  name="max_draws_per_user"
                  rules={[{ required: true, message: '请输入参与次数' }]}
                >
                  <InputNumber
                    min={1}
                    max={100}
                    style={{ width: '100%' }}
                    placeholder="每人最多可参与次数"
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="最大参与人数"
                  name="max_participants"
                >
                  <InputNumber
                    min={0}
                    style={{ width: '100%' }}
                    placeholder="0表示无限制"
                  />
                </Form.Item>
              </Col>
            </Row>

            {createGameData?.enhanced && (
              <>
                <Alert
                  message="增强版功能"
                  description="增强版游戏包含AI助手、高级分析、自定义主题等功能"
                  type="info"
                  showIcon
                  style={{ marginBottom: 16 }}
                />
                
                <Form.Item
                  label="启用AI助手"
                  name="ai_assistant_enabled"
                  valuePropName="checked"
                >
                  <Switch 
                    checkedChildren="启用"
                    unCheckedChildren="禁用"
                    defaultChecked
                  />
                </Form.Item>

                <Form.Item
                  label="启用高级分析"
                  name="enable_analytics"
                  valuePropName="checked"
                >
                  <Switch 
                    checkedChildren="启用"
                    unCheckedChildren="禁用"
                    defaultChecked
                  />
                </Form.Item>
              </>
            )}

            <div style={{ textAlign: 'right', marginTop: 24 }}>
              <Space>
                <Button onClick={() => setShowCreateForm(false)}>
                  取消
                </Button>
                <Button 
                  type="primary" 
                  htmlType="submit"
                  loading={loading}
                  icon={<GiftOutlined />}
                >
                  创建游戏
                </Button>
              </Space>
            </div>
          </Form>
        </Modal>
      </>
    );
  }

  return (
    <>
      <FloatButton
        icon={<MessageOutlined />}
        type="primary"
        style={{
          right: 24,
          bottom: 24
        }}
        tooltip="AI助手"
        onClick={onClose}
        badge={{ dot: suggestions.length > 0 }}
      />

      <Drawer
        title={null}
        placement={position}
        onClose={onClose}
        open={visible}
        width={400}
        styles={{
          body: { padding: 0 },
          header: { display: 'none' }
        }}
        closable={false}
      >
        {chatContent}
      </Drawer>

      {/* 游戏创建表单模态框 */}
      <Modal
        title={`创建${createGameData?.enhanced ? '增强版' : '基础版'}营销游戏`}
        open={showCreateForm}
        onCancel={() => setShowCreateForm(false)}
        footer={null}
        width={600}
        destroyOnClose
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleCreateGame}
          initialValues={{
            game_type: createGameData?.gameType || 'lucky_draw',
            max_draws_per_user: 1,
            time_range: [dayjs(), dayjs().add(7, 'day')]
          }}
        >
          <Form.Item
            label="游戏名称"
            name="name"
            rules={[{ required: true, message: '请输入游戏名称' }]}
          >
            <Input placeholder="请输入游戏名称" />
          </Form.Item>

          <Form.Item
            label="游戏描述"
            name="description"
          >
            <TextArea 
              rows={3} 
              placeholder="请输入游戏描述，介绍游戏玩法和奖品"
            />
          </Form.Item>

          <Form.Item
            label="活动时间"
            name="time_range"
            rules={[{ required: true, message: '请选择活动时间' }]}
          >
            <RangePicker 
              showTime
              style={{ width: '100%' }}
              placeholder={['开始时间', '结束时间']}
            />
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="每人最大参与次数"
                name="max_draws_per_user"
                rules={[{ required: true, message: '请输入参与次数' }]}
              >
                <InputNumber
                  min={1}
                  max={100}
                  style={{ width: '100%' }}
                  placeholder="每人最多可参与次数"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="最大参与人数"
                name="max_participants"
              >
                <InputNumber
                  min={0}
                  style={{ width: '100%' }}
                  placeholder="0表示无限制"
                />
              </Form.Item>
            </Col>
          </Row>

          {createGameData?.enhanced && (
            <>
              <Alert
                message="增强版功能"
                description="增强版游戏包含AI助手、高级分析、自定义主题等功能"
                type="info"
                showIcon
                style={{ marginBottom: 16 }}
              />
              
              <Form.Item
                label="启用AI助手"
                name="ai_assistant_enabled"
                valuePropName="checked"
              >
                <Switch 
                  checkedChildren="启用"
                  unCheckedChildren="禁用"
                  defaultChecked
                />
              </Form.Item>

              <Form.Item
                label="启用高级分析"
                name="enable_analytics"
                valuePropName="checked"
              >
                <Switch 
                  checkedChildren="启用"
                  unCheckedChildren="禁用"
                  defaultChecked
                />
              </Form.Item>
            </>
          )}

          <div style={{ textAlign: 'right', marginTop: 24 }}>
            <Space>
              <Button onClick={() => setShowCreateForm(false)}>
                取消
              </Button>
              <Button 
                type="primary" 
                htmlType="submit"
                loading={loading}
                icon={<GiftOutlined />}
              >
                创建游戏
              </Button>
            </Space>
          </div>
        </Form>
      </Modal>
    </>
  );
};

export default AIAssistant; 