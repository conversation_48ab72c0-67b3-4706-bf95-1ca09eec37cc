import React, { useState, useEffect } from 'react';
import {
  Card,
  List,
  Button,
  Space,
  Input,
  Tag,
  Typography,
  Divider,
  Row,
  Col,
  Tabs,
  message,
  Spin,
  Empty,
  Badge,
  Tooltip,
  Modal
} from 'antd';
import {
  AppstoreOutlined,
  SearchOutlined,
  DownloadOutlined,
  ShoppingCartOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  InfoCircleOutlined,
  SettingOutlined,
  RocketOutlined,
  DollarOutlined,
  StarOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import * as pluginService from '../../../services/pluginService';

const { Title, Text, Paragraph } = Typography;
const { Search } = Input;
const { TabPane } = Tabs;

/**
 * 插件市场组件
 */
const PluginMarket = () => {
  const [loading, setLoading] = useState(true);
  const [plugins, setPlugins] = useState([]);
  const [installedPlugins, setInstalledPlugins] = useState([]);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0
  });
  const [searchText, setSearchText] = useState('');
  const [activeCategory, setActiveCategory] = useState('all');
  const [categories, setCategories] = useState([]);
  const [activeTab, setActiveTab] = useState('all');
  const [purchaseModalVisible, setPurchaseModalVisible] = useState(false);
  const [selectedPlugin, setSelectedPlugin] = useState(null);
  
  const navigate = useNavigate();
  
  // 获取插件市场列表
  const fetchPlugins = async () => {
    setLoading(true);
    try {
      const params = {
        page: pagination.current,
        page_size: pagination.pageSize,
        search: searchText || undefined,
        category: activeCategory !== 'all' ? activeCategory : undefined
      };
      
      const response = await pluginService.getPluginMarket(params);
      
      if (response.success) {
        setPlugins(response.data.plugins || []);
        setPagination({
          ...pagination,
          total: response.data.pagination.total
        });
        
        if (response.data.categories) {
          setCategories(response.data.categories);
        }
      } else {
        message.error('获取插件市场列表失败');
      }
    } catch (error) {
      console.error('获取插件市场列表失败:', error);
      message.error('获取插件市场列表失败');
    } finally {
      setLoading(false);
    }
  };
  
  // 获取已安装的插件列表
  const fetchInstalledPlugins = async () => {
    try {
      const response = await pluginService.getInstalledPlugins();
      
      if (response.success) {
        setInstalledPlugins(response.data || []);
      } else {
        message.error('获取已安装的插件列表失败');
      }
    } catch (error) {
      console.error('获取已安装的插件列表失败:', error);
      message.error('获取已安装的插件列表失败');
    }
  };
  
  // 处理搜索
  const handleSearch = (value) => {
    setSearchText(value);
    setPagination({
      ...pagination,
      current: 1
    });
  };
  
  // 处理分类切换
  const handleCategoryChange = (category) => {
    setActiveCategory(category);
    setPagination({
      ...pagination,
      current: 1
    });
  };
  
  // 处理标签页切换
  const handleTabChange = (key) => {
    setActiveTab(key);
    
    if (key === 'installed') {
      fetchInstalledPlugins();
    } else {
      fetchPlugins();
    }
  };
  
  // 处理查看插件详情
  const handleViewPlugin = (plugin) => {
    navigate(`/tenant/plugin/detail/${plugin.id}`);
  };
  
  // 处理安装插件
  const handleInstallPlugin = async (plugin) => {
    try {
      // 如果插件需要付费且未购买，则显示购买对话框
      if (plugin.price > 0 && !plugin.is_purchased) {
        setSelectedPlugin(plugin);
        setPurchaseModalVisible(true);
        return;
      }
      
      const response = await pluginService.installPlugin(plugin.id);
      
      if (response.success) {
        message.success('插件安装成功');
        fetchPlugins();
      } else {
        message.error('插件安装失败');
      }
    } catch (error) {
      console.error('安装插件失败:', error);
      message.error('安装插件失败');
    }
  };
  
  // 处理卸载插件
  const handleUninstallPlugin = async (plugin) => {
    Modal.confirm({
      title: '确认卸载',
      content: `确定要卸载插件 "${plugin.name}" 吗？`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        try {
          const response = await pluginService.uninstallPlugin(plugin.id);
          
          if (response.success) {
            message.success('插件卸载成功');
            fetchInstalledPlugins();
          } else {
            message.error('插件卸载失败');
          }
        } catch (error) {
          console.error('卸载插件失败:', error);
          message.error('卸载插件失败');
        }
      }
    });
  };
  
  // 处理购买插件
  const handlePurchasePlugin = async () => {
    if (!selectedPlugin) {
      return;
    }
    
    try {
      const response = await pluginService.purchasePlugin(selectedPlugin.id, {
        billing_cycle: 'monthly',
        duration: 1,
        payment_method: 'online'
      });
      
      if (response.success) {
        message.success('插件购买成功');
        setPurchaseModalVisible(false);
        
        // 安装插件
        await pluginService.installPlugin(selectedPlugin.id);
        fetchPlugins();
      } else {
        message.error('插件购买失败');
      }
    } catch (error) {
      console.error('购买插件失败:', error);
      message.error('购买插件失败');
    }
  };
  
  // 处理配置插件
  const handleConfigPlugin = (plugin) => {
    navigate(`/tenant/plugin/config/${plugin.id}`);
  };
  
  // 初始化
  useEffect(() => {
    fetchPlugins();
  }, [pagination.current, pagination.pageSize, searchText, activeCategory]);
  
  // 渲染插件列表
  const renderPluginList = (dataSource, isInstalled = false) => {
    if (loading && !isInstalled) {
      return (
        <div style={{ textAlign: 'center', padding: '50px 0' }}>
          <Spin size="large" />
          <p style={{ marginTop: 16 }}>加载中...</p>
        </div>
      );
    }
    
    if (dataSource.length === 0) {
      return (
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description={isInstalled ? "暂无已安装的插件" : "暂无符合条件的插件"}
        />
      );
    }
    
    return (
      <List
        grid={{ gutter: 16, column: 3 }}
        dataSource={dataSource}
        pagination={
          isInstalled ? false : {
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            onChange: (page) => {
              setPagination({
                ...pagination,
                current: page
              });
            }
          }
        }
        renderItem={(item) => {
          const isInstalled = item.is_installed;
          
          return (
            <List.Item>
              <Card
                hoverable
                cover={
                  <div style={{ 
                    height: 140, 
                    display: 'flex', 
                    alignItems: 'center', 
                    justifyContent: 'center',
                    background: '#f5f5f5'
                  }}>
                    {item.icon_url ? (
                      <img 
                        src={item.icon_url} 
                        alt={item.name} 
                        style={{ maxHeight: 120, maxWidth: 120 }}
                      />
                    ) : (
                      <AppstoreOutlined style={{ fontSize: 64, color: '#1890ff' }} />
                    )}
                  </div>
                }
                actions={[
                  <Tooltip title="查看详情">
                    <Button 
                      type="link" 
                      icon={<InfoCircleOutlined />} 
                      onClick={() => handleViewPlugin(item)}
                    >
                      详情
                    </Button>
                  </Tooltip>,
                  isInstalled ? (
                    <Tooltip title="配置插件">
                      <Button 
                        type="link" 
                        icon={<SettingOutlined />} 
                        onClick={() => handleConfigPlugin(item)}
                      >
                        配置
                      </Button>
                    </Tooltip>
                  ) : (
                    <Tooltip title="安装插件">
                      <Button 
                        type="link" 
                        icon={<DownloadOutlined />} 
                        onClick={() => handleInstallPlugin(item)}
                      >
                        安装
                      </Button>
                    </Tooltip>
                  ),
                  isInstalled && (
                    <Tooltip title="卸载插件">
                      <Button 
                        type="link" 
                        danger
                        icon={<CloseCircleOutlined />} 
                        onClick={() => handleUninstallPlugin(item)}
                      >
                        卸载
                      </Button>
                    </Tooltip>
                  )
                ]}
              >
                <Card.Meta
                  title={
                    <Space>
                      {item.name}
                      {isInstalled && (
                        <Badge status="success" text="已安装" />
                      )}
                    </Space>
                  }
                  description={
                    <div>
                      <Paragraph ellipsis={{ rows: 2 }}>
                        {item.description}
                      </Paragraph>
                      <div style={{ marginTop: 8 }}>
                        <Space>
                          <Tag color="blue">{item.category}</Tag>
                          <Tag color="green">v{item.version}</Tag>
                          {item.price > 0 ? (
                            <Tag color="orange">¥{item.price}/{item.billing_cycle}</Tag>
                          ) : (
                            <Tag color="green">免费</Tag>
                          )}
                        </Space>
                      </div>
                    </div>
                  }
                />
              </Card>
            </List.Item>
          );
        }}
      />
    );
  };
  
  return (
    <div>
      <Card
        title={
          <Space>
            <AppstoreOutlined />
            <span>插件市场</span>
          </Space>
        }
        extra={
          <Space>
            <Search
              placeholder="搜索插件"
              onSearch={handleSearch}
              style={{ width: 200 }}
            />
          </Space>
        }
      >
        <Tabs activeKey={activeTab} onChange={handleTabChange}>
          <TabPane tab="全部插件" key="all">
            <div style={{ marginBottom: 16 }}>
              <Space>
                <Button
                  type={activeCategory === 'all' ? 'primary' : 'default'}
                  onClick={() => handleCategoryChange('all')}
                >
                  全部
                </Button>
                {categories.map(category => (
                  <Button
                    key={category.value}
                    type={activeCategory === category.value ? 'primary' : 'default'}
                    onClick={() => handleCategoryChange(category.value)}
                  >
                    {category.label}
                  </Button>
                ))}
              </Space>
            </div>
            {renderPluginList(plugins)}
          </TabPane>
          <TabPane tab="已安装" key="installed">
            {renderPluginList(installedPlugins, true)}
          </TabPane>
        </Tabs>
      </Card>
      
      {/* 购买插件对话框 */}
      <Modal
        title="购买插件"
        open={purchaseModalVisible}
        onCancel={() => setPurchaseModalVisible(false)}
        onOk={handlePurchasePlugin}
        okText="确认购买"
        cancelText="取消"
      >
        {selectedPlugin && (
          <div>
            <div style={{ marginBottom: 16 }}>
              <Space>
                <Title level={4}>{selectedPlugin.name}</Title>
                <Tag color="orange">¥{selectedPlugin.price}/{selectedPlugin.billing_cycle}</Tag>
              </Space>
            </div>
            <Paragraph>{selectedPlugin.description}</Paragraph>
            <Divider />
            <Row gutter={16}>
              <Col span={12}>
                <Text strong>版本：</Text>
                <Text>v{selectedPlugin.version}</Text>
              </Col>
              <Col span={12}>
                <Text strong>作者：</Text>
                <Text>{selectedPlugin.author}</Text>
              </Col>
            </Row>
            <Row gutter={16} style={{ marginTop: 8 }}>
              <Col span={12}>
                <Text strong>分类：</Text>
                <Text>{selectedPlugin.category}</Text>
              </Col>
              <Col span={12}>
                <Text strong>计费周期：</Text>
                <Text>
                  {selectedPlugin.billing_cycle === 'monthly' ? '月付' :
                   selectedPlugin.billing_cycle === 'quarterly' ? '季付' :
                   selectedPlugin.billing_cycle === 'yearly' ? '年付' : '一次性'}
                </Text>
              </Col>
            </Row>
            <Divider />
            <Paragraph>
              <InfoCircleOutlined style={{ marginRight: 8 }} />
              购买后将自动安装插件，您可以随时在已安装插件列表中查看和配置。
            </Paragraph>
          </div>
        )}
      </Modal>
    </div>
  );
};

export default PluginMarket;
