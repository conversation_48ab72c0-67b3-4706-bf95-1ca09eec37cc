#!/usr/bin/env python
# -*- coding: utf-8 -*-

import logging
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status, Query, Path
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import parse_obj_as
from datetime import datetime, date
import uuid

from db.database import get_db
from core.auth import get_current_user
from api.deps import get_current_project
from models.project import Project
from models.user import User
from schemas.sales_report import (
    SalesReportCreate,
    SalesReportUpdate,
    SalesReportStatusUpdate,
    SalesReportResponse,
    SalesReportListResponse,
    SalesReportStatisticsResponse
)
from services.sales_report import SalesReportService

# 配置日志
logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter()

@router.get("", response_model=SalesReportListResponse)
async def get_sales_reports(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    store_id: Optional[uuid.UUID] = None,
    report_status: Optional[str] = Query(None, alias="status"),
    report_type: Optional[str] = None,
    search: Optional[str] = None,
    start_date: Optional[date] = None,
    end_date: Optional[date] = None,
    sort_by: str = "report_date",
    sort_order: str = "desc",
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """获取销售上报列表"""
    try:
        start_datetime = datetime.combine(start_date, datetime.min.time()) if start_date else None
        end_datetime = datetime.combine(end_date, datetime.max.time()) if end_date else None

        sales_reports = await SalesReportService.get_reports(
            db=db,
            project_id=project.id,
            skip=skip,
            limit=limit,
            store_id=store_id,
            status=report_status,
            report_type=report_type,
            search=search,
            start_date=start_datetime,
            end_date=end_datetime,
            sort_by=sort_by,
            sort_order=sort_order
        )

        total = await SalesReportService.count_reports(
            db=db,
            project_id=project.id,
            store_id=store_id,
            status=report_status,
            report_type=report_type,
            search=search,
            start_date=start_datetime,
            end_date=end_datetime
        )
        
        # 使用 Pydantic 的 parse_obj_as 进行转换，它会自动处理嵌套模型
        report_responses = parse_obj_as(List[SalesReportResponse], sales_reports)

        return {
            "items": report_responses,
            "total": total,
            "page": skip // limit + 1 if limit else 1,
            "size": limit,
            "pages": (total + limit - 1) // limit if limit else 1
        }
    except Exception as e:
        logger.error(f"获取销售上报列表失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取销售上报列表失败"
        )

@router.post("", response_model=SalesReportResponse, status_code=status.HTTP_201_CREATED)
async def create_sales_report(
    report_in: SalesReportCreate,
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """创建新的销售上报"""
    try:
        report = await SalesReportService.create_report(
            db=db,
            project_id=project.id,
            user_id=current_user.id,
            report_data=report_in
        )
        return report
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        logger.error(f"创建销售上报失败: {e}", exc_info=True)
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="创建销售上报失败")

@router.get("/statistics", response_model=SalesReportStatisticsResponse)
async def get_sales_statistics(
    start_date: date = Query(..., description="开始日期"),
    end_date: date = Query(..., description="结束日期"),
    store_id: Optional[uuid.UUID] = Query(None, description="门店ID"),
    group_by: str = Query("month", description="分组方式: month, day"),
    report_status: Optional[str] = Query(None, alias="status", description="报告状态 (DRAFT, PUBLISHED, ARCHIVED)"),
    data_type: str = Query("sales", description="数据类型 (sales, recharge)"),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """获取销售统计数据"""
    try:
        start_datetime = datetime.combine(start_date, datetime.min.time())
        end_datetime = datetime.combine(end_date, datetime.max.time())

        statistics = await SalesReportService.get_sales_statistics(
            db=db,
            project_id=project.id,
            start_date=start_datetime,
            end_date=end_datetime,
            store_id=store_id,
            group_by=group_by,
            status=report_status,
            data_type=data_type
        )
        
        return {
            "items": statistics,
            "total": len(statistics)
        }
    except Exception as e:
        logger.error(f"获取销售统计数据失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取销售统计数据失败"
        )

@router.get("/{report_id}", response_model=SalesReportResponse)
async def get_sales_report(
    report_id: uuid.UUID = Path(...),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """获取销售上报详情"""
    try:
        report_detail = await SalesReportService.get_report_with_details(db, report_id)
        if not report_detail:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="销售上报不存在"
            )

        if report_detail.project_id != project.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问此销售上报"
            )

        return report_detail
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取销售上报详情失败: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取销售上报详情失败"
        )

@router.put("/{report_id}", response_model=SalesReportResponse)
async def update_sales_report(
    report_id: uuid.UUID,
    report_in: SalesReportUpdate,
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """更新销售上报"""
    try:
        report = await SalesReportService.update_report(
            db=db,
            report_id=report_id,
            project_id=project.id,
            user_id=current_user.id,
            report_data=report_in
        )
        if not report:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="销售上报不存在")
        return report
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        logger.error(f"更新销售上报失败: {e}", exc_info=True)
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="更新销售上报失败")

@router.put("/{report_id}/status", response_model=SalesReportResponse)
async def update_sales_report_status(
    report_id: uuid.UUID = Path(...),
    status_data: SalesReportStatusUpdate = None,
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """更新销售上报状态"""
    # First, check if the report exists and belongs to the project
    report = await SalesReportService.get_report_by_id(db, report_id)
    if not report or report.project_id != project.id:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="销售上报不存在或无权访问")

    try:
        updated_report = await SalesReportService.update_sales_report_status(
            db=db,
            report_id=report_id,
            status_data=status_data,
            user_id=current_user.id
        )
        if not updated_report:
             raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="销售上报不存在")
        return updated_report
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        logger.error(f"更新销售上报状态失败: {e}", exc_info=True)
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="更新销售上报状态失败")

@router.delete("/{report_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_sales_report(
    report_id: uuid.UUID = Path(...),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """删除销售上报"""
    try:
        success = await SalesReportService.delete_sales_report(
            db=db, 
            report_id=report_id,
            project_id=project.id
        )
        if not success:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="要删除的销售上报不存在")
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except Exception as e:
        logger.error(f"删除销售上报失败: {e}", exc_info=True)
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="删除销售上报失败")

