import React, { useState, useEffect } from 'react';
import {
  Card, Row, Col, Table, Button, Space, Tag, Progress, 
  Statistic, Alert, Tabs, Select, DatePicker, Input,
  Modal, Form, message, Tooltip, Badge, Empty, Spin,
  List, Descriptions, Rate
} from 'antd';
import {
  PartitionOutlined, ShopOutlined, GlobalOutlined,
  Bar<PERSON><PERSON>Outlined, Line<PERSON>hartOutlined, PieChartOutlined,
  RiseOutlined, DollarOutlined, SyncOutlined,
  SearchOutlined, FilterOutlined, ExportOutlined,
  RobotOutlined, EnvironmentOutlined, TeamOutlined,
  ClockCircleOutlined, ThunderboltOutlined, FireOutlined,
  EyeOutlined, HeartOutlined, StarOutlined
} from '@ant-design/icons';
import { Line, Column, Pie, Gauge } from '@ant-design/plots';
import { api } from '../../../../services/api';

const { TabPane } = Tabs;
const { Option } = Select;
const { RangePicker } = DatePicker;
const { Search } = Input;

/**
 * 渠道分析组件
 * 分析实体门店和线上渠道的综合表现
 */
const ChannelAnalysis = () => {
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');
  const [channelData, setChannelData] = useState({});
  const [storeData, setStoreData] = useState({});
  const [onlineData, setOnlineData] = useState({});
  const [performanceData, setPerformanceData] = useState([]);
  const [comparisonData, setComparisonData] = useState([]);
  const [filters, setFilters] = useState({
    channelType: 'all',
    dateRange: null,
    keyword: ''
  });
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    fetchChannelAnalysisData();
  }, [filters]);

  // 获取渠道分析数据
  const fetchChannelAnalysisData = async () => {
    try {
      setLoading(true);
      
      // 获取项目ID
      const projectId = localStorage.getItem('project_id');
      if (!projectId) {
        throw new Error('未找到项目ID');
      }
      
      const params = {
        channelType: filters.channelType,
        dateRange: filters.dateRange,
        keyword: filters.keyword
      };

      // 使用真实的API端点
      const response = await api.get(`/project/${projectId}/business-analysis/channel-analysis`, { params });
      
      if (response && response.data) {
        setChannelData(response.data.channels || {});
        setStoreData(response.data.stores || {});
        setOnlineData(response.data.online || {});
        setPerformanceData(response.data.performance || []);
        setComparisonData(response.data.comparison || []);
      }
    } catch (error) {
      console.error('获取渠道分析数据失败:', error);
      message.error('获取数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 刷新数据
  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchChannelAnalysisData();
    setRefreshing(false);
    message.success('数据已刷新');
  };

  // 门店表格列
  const storeColumns = [
    {
      title: '门店信息',
      dataIndex: 'store',
      key: 'store',
      width: 200,
      fixed: 'left',
      render: (store) => {
        if (!store) return '-';
        return (
          <div>
            <div style={{ fontWeight: 500 }}>{store.name || '-'}</div>
            <div style={{ fontSize: 12, color: '#666' }}>
              <EnvironmentOutlined style={{ marginRight: 4 }} />
              {store.address || '-'}
            </div>
            <div style={{ fontSize: 12, color: '#666' }}>
              面积: {store.area || 0}㎡ | 员工: {store.staff || 0}人
            </div>
          </div>
        );
      }
    },
    {
      title: '经营状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status) => {
        const statusConfig = {
          'excellent': { color: 'green', text: '优秀' },
          'good': { color: 'blue', text: '良好' },
          'average': { color: 'orange', text: '一般' },
          'poor': { color: 'red', text: '较差' }
        };
        const config = statusConfig[status] || statusConfig.average;
        return <Tag color={config.color}>{config.text}</Tag>;
      }
    },
    {
      title: '销售额',
      dataIndex: 'revenue',
      key: 'revenue',
      width: 120,
      render: (revenue, record) => (
        <div>
          <div style={{ fontWeight: 500 }}>¥{(revenue || 0).toLocaleString()}</div>
          <div style={{ 
            fontSize: 12, 
            color: (record.revenueGrowth || 0) > 0 ? '#52c41a' : '#ff4d4f' 
          }}>
            {(record.revenueGrowth || 0) > 0 ? '↗' : '↘'} {Math.abs(record.revenueGrowth || 0)}%
          </div>
        </div>
      )
    },
    {
      title: '客流量',
      dataIndex: 'traffic',
      key: 'traffic',
      width: 100,
      render: (traffic, record) => (
        <div>
          <div style={{ fontWeight: 500 }}>{(traffic || 0).toLocaleString()}</div>
          <div style={{ fontSize: 12, color: '#666' }}>
            转化率: {record.conversionRate || 0}%
          </div>
        </div>
      )
    },
    {
      title: '效率指数',
      dataIndex: 'efficiency',
      key: 'efficiency',
      width: 120,
      render: (efficiency) => {
        const efficiencyValue = efficiency || 0;
        return (
          <div>
            <Progress 
              percent={efficiencyValue} 
              size="small" 
              strokeColor={
                efficiencyValue >= 80 ? '#52c41a' : 
                efficiencyValue >= 60 ? '#faad14' : '#ff4d4f'
              }
              showInfo={false}
            />
            <div style={{ fontSize: 12, marginTop: 4 }}>
              {efficiencyValue >= 80 ? '高效' : efficiencyValue >= 60 ? '中等' : '低效'}
            </div>
          </div>
        );
      }
    },
    {
      title: '评分',
      dataIndex: 'rating',
      key: 'rating',
      width: 100,
      render: (rating) => {
        const ratingValue = rating || 0;
        return (
          <div style={{ textAlign: 'center' }}>
            <Rate disabled defaultValue={ratingValue} style={{ fontSize: 14 }} />
            <div style={{ fontSize: 12, color: '#666' }}>{ratingValue}/5</div>
          </div>
        );
      }
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Button size="small" type="link">详情</Button>
          <Button size="small" type="link">优化</Button>
        </Space>
      )
    }
  ];

  // 渲染渠道概览
  const renderChannelOverview = () => {
    const overviewStats = [
      {
        title: '总渠道数',
        value: 28,
        prefix: <PartitionOutlined style={{ color: '#1890ff' }} />,
        color: '#1890ff'
      },
      {
        title: '实体门店',
        value: 15,
        prefix: <ShopOutlined style={{ color: '#52c41a' }} />,
        color: '#52c41a'
      },
      {
        title: '线上渠道',
        value: 13,
        prefix: <GlobalOutlined style={{ color: '#722ed1' }} />,
        color: '#722ed1'
      },
      {
        title: '渠道效率',
        value: 78.5,
        suffix: '%',
        prefix: <RiseOutlined style={{ color: '#faad14' }} />,
        color: '#faad14'
      }
    ];

    // 渠道收入分布数据
    const revenueDistribution = [
      { channel: '实体门店', revenue: 680000, percentage: 65 },
      { channel: '电商平台', revenue: 250000, percentage: 24 },
      { channel: '社交电商', revenue: 80000, percentage: 8 },
      { channel: '其他渠道', revenue: 35000, percentage: 3 }
    ];

    const pieConfig = {
      data: revenueDistribution,
      angleField: 'revenue',
      colorField: 'channel',
      radius: 0.8,
      label: false,
      interactions: [{ type: 'element-active' }],
      legend: {
        position: 'bottom'
      }
    };

    const channelRanking = channelData.ranking || [];

    return (
      <div>
        <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
          {overviewStats.map((stat, index) => (
            <Col xs={24} sm={12} lg={6} key={index}>
              <Card>
                <Statistic
                  title={stat.title}
                  value={stat.value}
                  suffix={stat.suffix}
                  prefix={stat.prefix}
                  valueStyle={{ color: stat.color }}
                />
              </Card>
            </Col>
          ))}
        </Row>

        <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
          <Col xs={24} lg={12}>
            <Card title="渠道收入分布">
              <Pie {...pieConfig} height={300} />
            </Card>
          </Col>
          <Col xs={24} lg={12}>
            <Card title="渠道表现排行">
              <div className="channel-ranking">
                {channelRanking.map((channel, index) => (
                  <div key={index} style={{ 
                    display: 'flex', 
                    justifyContent: 'space-between', 
                    alignItems: 'center',
                    padding: '12px 0',
                    borderBottom: index < channelRanking.length - 1 ? '1px solid #f0f0f0' : 'none'
                  }}>
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                      <Badge 
                        count={index + 1} 
                        style={{ 
                          backgroundColor: index < 3 ? '#faad14' : '#1890ff',
                          marginRight: 12
                        }} 
                      />
                      <div>
                        <div style={{ fontWeight: 500 }}>{channel.name || '-'}</div>
                        <div style={{ fontSize: 12, color: '#666' }}>{channel.type || '-'}</div>
                      </div>
                    </div>
                    <div style={{ textAlign: 'right' }}>
                      <div style={{ fontWeight: 500, color: '#52c41a' }}>
                        ¥{(channel.revenue || 0).toLocaleString()}
                      </div>
                      <div style={{ fontSize: 12, color: '#666' }}>
                        效率: {channel.efficiency || 0}%
                      </div>
                    </div>
                  </div>
                ))}
                {channelRanking.length === 0 && (
                  <Empty description="暂无数据" />
                )}
              </div>
            </Card>
          </Col>
        </Row>

        <Alert
          message="AI渠道洞察"
          description="基于多维度数据分析，为您提供渠道优化建议和发展机会识别。"
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />
      </div>
    );
  };

  // 渲染实体门店分析
  const renderStoreAnalysis = () => {
    const storePerformanceData = storeData.performance || [];
    
    const performanceConfig = {
      data: storePerformanceData,
      xField: 'month',
      yField: 'value',
      seriesField: 'metric',
      smooth: true,
      animation: {
        appear: {
          animation: 'path-in',
          duration: 1000,
        },
      },
    };

    return (
      <div>
        <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
          <Col xs={24} sm={6}>
            <Card>
              <Statistic
                title="门店总数"
                value={15}
                prefix={<ShopOutlined style={{ color: '#1890ff' }} />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={6}>
            <Card>
              <Statistic
                title="平均坪效"
                value={1286}
                prefix="¥"
                suffix="/㎡"
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={6}>
            <Card>
              <Statistic
                title="平均客流"
                value={456}
                suffix="人/日"
                prefix={<TeamOutlined style={{ color: '#faad14' }} />}
                valueStyle={{ color: '#faad14' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={6}>
            <Card>
              <Statistic
                title="转化率"
                value={23.8}
                suffix="%"
                prefix={<RiseOutlined style={{ color: '#722ed1' }} />}
                valueStyle={{ color: '#722ed1' }}
              />
            </Card>
          </Col>
        </Row>

        <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
          <Col xs={24}>
            <Card title="门店经营趋势">
              <Line {...performanceConfig} height={300} />
            </Card>
          </Col>
        </Row>

        <Card title="门店详细分析">
          <Table
            columns={storeColumns}
            dataSource={storeData.stores || []}
            rowKey="id"
            scroll={{ x: 1200 }}
            pagination={{ pageSize: 10 }}
            loading={loading}
          />
        </Card>
      </div>
    );
  };

  // 渲染线上渠道分析
  const renderOnlineAnalysis = () => {
    const onlineChannels = [
      {
        name: '天猫旗舰店',
        platform: '天猫',
        revenue: 180000,
        growth: 15.6,
        traffic: 25600,
        conversion: 3.2,
        rating: 4.8
      },
      {
        name: '京东自营',
        platform: '京东',
        revenue: 120000,
        growth: 8.9,
        traffic: 18900,
        conversion: 2.8,
        rating: 4.6
      },
      {
        name: '抖音小店',
        platform: '抖音',
        revenue: 85000,
        growth: 45.2,
        traffic: 32100,
        conversion: 1.9,
        rating: 4.5
      },
      {
        name: '微信小程序',
        platform: '微信',
        revenue: 65000,
        growth: 22.3,
        traffic: 15600,
        conversion: 4.1,
        rating: 4.7
      }
    ];

    return (
      <div>
        <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
          <Col xs={24} sm={6}>
            <Card>
              <Statistic
                title="线上渠道数"
                value={13}
                prefix={<GlobalOutlined style={{ color: '#1890ff' }} />}
                valueStyle={{ color: '#1890ff' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={6}>
            <Card>
              <Statistic
                title="线上总收入"
                value={450000}
                prefix="¥"
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={6}>
            <Card>
              <Statistic
                title="平均增长率"
                value={23.0}
                suffix="%"
                prefix={<RiseOutlined style={{ color: '#faad14' }} />}
                valueStyle={{ color: '#faad14' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={6}>
            <Card>
              <Statistic
                title="平均转化率"
                value={3.0}
                suffix="%"
                valueStyle={{ color: '#722ed1' }}
              />
            </Card>
          </Col>
        </Row>

        <Row gutter={[16, 16]}>
          {onlineChannels.map((channel, index) => (
            <Col xs={24} sm={12} lg={6} key={index}>
              <Card 
                title={channel.name}
                extra={<Tag color="blue">{channel.platform}</Tag>}
              >
                <div style={{ marginBottom: 16 }}>
                  <Statistic
                    title="月收入"
                    value={channel.revenue}
                    prefix="¥"
                    valueStyle={{ fontSize: 18 }}
                  />
                </div>
                <div style={{ marginBottom: 8 }}>
                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                    <span>增长率:</span>
                    <span style={{ 
                      color: channel.growth > 0 ? '#52c41a' : '#ff4d4f',
                      fontWeight: 500 
                    }}>
                      {channel.growth > 0 ? '+' : ''}{channel.growth}%
                    </span>
                  </div>
                </div>
                <div style={{ marginBottom: 8 }}>
                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                    <span>流量:</span>
                    <span style={{ fontWeight: 500 }}>{(channel.traffic || 0).toLocaleString()}</span>
                  </div>
                </div>
                <div style={{ marginBottom: 8 }}>
                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                    <span>转化率:</span>
                    <span style={{ fontWeight: 500 }}>{channel.conversion}%</span>
                  </div>
                </div>
                <div style={{ textAlign: 'center', marginTop: 12 }}>
                  <Rate disabled defaultValue={channel.rating} style={{ fontSize: 14 }} />
                  <div style={{ fontSize: 12, color: '#666', marginTop: 4 }}>
                    评分: {channel.rating}/5
                  </div>
                </div>
              </Card>
            </Col>
          ))}
        </Row>
      </div>
    );
  };

  // 渲染渠道对比分析
  const renderChannelComparison = () => {
    const comparisonMetrics = [
      { metric: '收入贡献', offline: 65, online: 35 },
      { metric: '客户获取', offline: 45, online: 55 },
      { metric: '转化效率', offline: 78, online: 22 },
      { metric: '成本效益', offline: 60, online: 40 },
      { metric: '增长潜力', offline: 30, online: 70 }
    ];

    return (
      <div>
        <Alert
          message="渠道对比分析"
          description="对比实体门店与线上渠道的各项指标，帮助优化渠道配置和资源分配。"
          type="info"
          showIcon
          style={{ marginBottom: 24 }}
        />

        <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
          <Col xs={24} lg={12}>
            <Card title="渠道指标对比">
              <div className="comparison-metrics">
                {comparisonMetrics.map((item, index) => (
                  <div key={index} style={{ marginBottom: 20 }}>
                    <div style={{ 
                      display: 'flex', 
                      justifyContent: 'space-between', 
                      marginBottom: 8 
                    }}>
                      <span style={{ fontWeight: 500 }}>{item.metric}</span>
                    </div>
                    <div style={{ marginBottom: 4 }}>
                      <div style={{ 
                        display: 'flex', 
                        justifyContent: 'space-between', 
                        fontSize: 12, 
                        color: '#666' 
                      }}>
                        <span>实体门店</span>
                        <span>{item.offline}%</span>
                      </div>
                      <Progress 
                        percent={item.offline} 
                        size="small" 
                        strokeColor="#52c41a"
                        showInfo={false}
                      />
                    </div>
                    <div>
                      <div style={{ 
                        display: 'flex', 
                        justifyContent: 'space-between', 
                        fontSize: 12, 
                        color: '#666' 
                      }}>
                        <span>线上渠道</span>
                        <span>{item.online}%</span>
                      </div>
                      <Progress 
                        percent={item.online} 
                        size="small" 
                        strokeColor="#1890ff"
                        showInfo={false}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </Card>
          </Col>

          <Col xs={24} lg={12}>
            <Card title="AI优化建议">
              <List
                dataSource={[
                  {
                    type: 'opportunity',
                    title: '线上渠道增长机会',
                    content: '抖音小店增长率达45.2%，建议加大投入和推广力度。',
                    priority: 'high'
                  },
                  {
                    type: 'warning',
                    title: '门店效率待提升',
                    content: '3家门店效率低于60%，需要优化运营策略。',
                    priority: 'medium'
                  },
                  {
                    type: 'suggestion',
                    title: '渠道协同优化',
                    content: '建议打通线上线下会员体系，提升整体转化率。',
                    priority: 'medium'
                  },
                  {
                    type: 'opportunity',
                    title: '新渠道拓展',
                    content: '小红书、B站等新兴平台具有较大潜力。',
                    priority: 'low'
                  }
                ]}
                renderItem={(item, index) => (
                  <List.Item style={{ padding: '12px 0' }}>
                    <div style={{ width: '100%' }}>
                      <div style={{ display: 'flex', alignItems: 'center', marginBottom: 8 }}>
                        {item.type === 'opportunity' && <FireOutlined style={{ color: '#ff4d4f', marginRight: 8 }} />}
                        {item.type === 'warning' && <ThunderboltOutlined style={{ color: '#faad14', marginRight: 8 }} />}
                        {item.type === 'suggestion' && <EyeOutlined style={{ color: '#1890ff', marginRight: 8 }} />}
                        <span style={{ fontWeight: 500 }}>{item.title}</span>
                        <Tag 
                          color={
                            item.priority === 'high' ? 'red' : 
                            item.priority === 'medium' ? 'orange' : 'blue'
                          }
                          size="small"
                          style={{ marginLeft: 8 }}
                        >
                          {item.priority === 'high' ? '高' : 
                           item.priority === 'medium' ? '中' : '低'}
                        </Tag>
                      </div>
                      <div style={{ fontSize: 14, color: '#666' }}>
                        {item.content}
                      </div>
                    </div>
                  </List.Item>
                )}
              />
            </Card>
          </Col>
        </Row>
      </div>
    );
  };

  if (loading && !refreshing) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>加载渠道分析数据...</div>
      </div>
    );
  }

  return (
    <div className="channel-analysis">
      {/* 筛选工具栏 */}
      <Card style={{ marginBottom: 16 }}>
        <Row gutter={[16, 16]} align="middle">
          <Col xs={24} sm={8} md={6}>
            <Select
              placeholder="选择渠道类型"
              style={{ width: '100%' }}
              value={filters.channelType}
              onChange={(value) => setFilters({ ...filters, channelType: value })}
            >
              <Option value="all">全部渠道</Option>
              <Option value="offline">实体门店</Option>
              <Option value="online">线上渠道</Option>
              <Option value="social">社交电商</Option>
            </Select>
          </Col>
          <Col xs={24} sm={8} md={6}>
            <RangePicker
              style={{ width: '100%' }}
              onChange={(dates) => setFilters({ ...filters, dateRange: dates })}
            />
          </Col>
          <Col xs={24} sm={8} md={6}>
            <Search
              placeholder="搜索渠道"
              onSearch={(value) => setFilters({ ...filters, keyword: value })}
              style={{ width: '100%' }}
            />
          </Col>
          <Col xs={24} sm={24} md={6}>
            <Space>
              <Button 
                icon={<SyncOutlined />} 
                onClick={handleRefresh}
                loading={refreshing}
              >
                刷新
              </Button>
              <Button icon={<ExportOutlined />}>
                导出
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 分析内容Tab */}
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane 
          tab={
            <Space>
              <PartitionOutlined />
              渠道概览
            </Space>
          } 
          key="overview"
        >
          {renderChannelOverview()}
        </TabPane>

        <TabPane 
          tab={
            <Space>
              <ShopOutlined />
              实体门店
            </Space>
          } 
          key="stores"
        >
          {renderStoreAnalysis()}
        </TabPane>

        <TabPane 
          tab={
            <Space>
              <GlobalOutlined />
              线上渠道
            </Space>
          } 
          key="online"
        >
          {renderOnlineAnalysis()}
        </TabPane>

        <TabPane 
          tab={
            <Space>
              <BarChartOutlined />
              对比分析
            </Space>
          } 
          key="comparison"
        >
          {renderChannelComparison()}
        </TabPane>
      </Tabs>
    </div>
  );
};

export default ChannelAnalysis; 