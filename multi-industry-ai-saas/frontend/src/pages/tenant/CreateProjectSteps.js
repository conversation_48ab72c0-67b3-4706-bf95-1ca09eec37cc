import React, { useState, useEffect } from 'react';
import {
  Steps, Form, Input, Button, Card, Select, InputNumber, Typography,
  message, Spin, Radio, Row, Col, Divider, Modal, Progress
} from 'antd';
import {
  ArrowLeftOutlined, CheckCircleOutlined, ShopOutlined,
  AppstoreOutlined, UserOutlined, CloudOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { admin, tenant } from '../../services';
import './CreateProjectSteps.css';

const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;
const { Option } = Select;

// 模拟的套餐数据
const PLANS = [
  {
    id: 'basic',
    name: '基础版',
    price: 0,
    period: '免费',
    color: '#5B8FF9',
    features: [
      '1个项目',
      '最多5个用户',
      '基础功能',
      '1GB存储空间',
      '社区支持'
    ],
    maxProjects: 1,
    maxUsers: 5,
    maxStorage: 1
  },
  {
    id: 'standard',
    name: '标准版',
    price: 199,
    period: '月付',
    color: '#F46036',
    features: [
      '10个项目',
      '最多50个用户',
      '全部功能',
      '10GB存储空间',
      '7×24小时技术支持'
    ],
    maxProjects: 10,
    maxUsers: 50,
    maxStorage: 10
  },
  {
    id: 'premium',
    name: '高级版',
    price: 499,
    period: '月付',
    color: '#E6B333',
    features: [
      '不限项目数',
      '不限用户数',
      '全部功能+高级功能',
      '100GB存储空间',
      '专属客户经理'
    ],
    maxProjects: 999,
    maxUsers: 999,
    maxStorage: 100
  },
  {
    id: 'enterprise',
    name: '企业版',
    price: 999,
    period: '月付',
    color: '#722ED1',
    features: [
      '不限项目数',
      '不限用户数',
      '全部功能+企业级功能',
      '1TB存储空间',
      '专属解决方案'
    ],
    maxProjects: 999,
    maxUsers: 999,
    maxStorage: 1000
  }
];

const CreateProjectSteps = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [form] = Form.useForm();
  const [current, setCurrent] = useState(0);
  const [loading, setLoading] = useState(false);
  const [industries, setIndustries] = useState([]);
  const [loadingIndustries, setLoadingIndustries] = useState(true);
  const [selectedPlan, setSelectedPlan] = useState('basic');
  const [projectInfo, setProjectInfo] = useState({});
  const [paymentMethod, setPaymentMethod] = useState('free');
  const [redeemCode, setRedeemCode] = useState('');
  const [creatingProject, setCreatingProject] = useState(false);
  const [progress, setProgress] = useState(0);
  const [creationComplete, setCreationComplete] = useState(false);

  // 获取行业列表
  useEffect(() => {
    const fetchIndustries = async () => {
      try {
        setLoadingIndustries(true);
        const response = await admin.industry.getList();

        // 处理API响应，确保我们有一个行业数组
        let industriesData = [];
        if (Array.isArray(response)) {
          industriesData = response;
        } else if (response && Array.isArray(response.items)) {
          industriesData = response.items;
        } else if (response && typeof response === 'object') {
          // 如果响应是一个对象，尝试提取行业数据
          console.log('API响应格式:', response);
          industriesData = response.industries || response.data || [];
        }

        // 如果没有获取到行业数据，添加默认行业
        if (industriesData.length === 0) {
          industriesData = [
            { id: '1', code: 'retail', name: '零售业' },
            { id: '2', code: 'restaurant', name: '餐饮业' },
            { id: '3', code: 'hotel', name: '酒店业' }
          ];
        }

        setIndustries(industriesData);
      } catch (error) {
        console.error('获取行业列表失败:', error);
        message.error('获取行业列表失败');

        // 设置默认行业
        setIndustries([
          { id: '1', code: 'retail', name: '零售业' },
          { id: '2', code: 'restaurant', name: '餐饮业' },
          { id: '3', code: 'hotel', name: '酒店业' }
        ]);
      } finally {
        setLoadingIndustries(false);
      }
    };

    fetchIndustries();
  }, []);

  // 处理步骤变化
  const handleStepChange = (step) => {
    setCurrent(step);
  };

  // 处理下一步
  const handleNext = async () => {
    try {
      if (current === 0) {
        // 验证基本信息表单
        const values = await form.validateFields();
        setProjectInfo({
          ...projectInfo,
          ...values
        });
        setCurrent(current + 1);
      } else if (current === 1) {
        // 已选择套餐，直接进入下一步
        setCurrent(current + 1);
      } else if (current === 2) {
        // 处理支付或兑换码
        if (selectedPlan === 'basic' || paymentMethod === 'redeem') {
          // 基础版免费或使用兑换码，直接进入创建流程
          startProjectCreation();
        } else {
          // 模拟支付成功
          message.success('支付成功');
          startProjectCreation();
        }
      }
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  };

  // 处理上一步
  const handlePrev = () => {
    setCurrent(current - 1);
  };

  // 处理套餐选择
  const handlePlanSelect = (planId) => {
    setSelectedPlan(planId);

    // 更新项目信息中的配额
    const plan = PLANS.find(p => p.id === planId);
    if (plan) {
      setProjectInfo({
        ...projectInfo,
        max_users: plan.maxUsers,
        max_storage_gb: plan.maxStorage
      });
    }
  };

  // 处理支付方式选择
  const handlePaymentMethodChange = (e) => {
    setPaymentMethod(e.target.value);
  };

  // 处理兑换码输入
  const handleRedeemCodeChange = (e) => {
    setRedeemCode(e.target.value);
  };

  // 验证兑换码
  const validateRedeemCode = () => {
    // 模拟验证兑换码
    if (redeemCode === 'FREE2024') {
      message.success('兑换码验证成功');
      return true;
    } else {
      message.error('无效的兑换码');
      return false;
    }
  };

  // 开始项目创建流程
  const startProjectCreation = () => {
    setCurrent(3);
    setCreatingProject(true);

    // 模拟创建进度
    let percent = 0;
    const interval = setInterval(() => {
      percent += Math.floor(Math.random() * 10) + 1;
      if (percent >= 100) {
        percent = 100;
        clearInterval(interval);

        // 模拟API调用
        setTimeout(() => {
          completeProjectCreation();
        }, 500);
      }
      setProgress(percent);
    }, 300);
  };

  // 完成项目创建
  const completeProjectCreation = async () => {
    try {
      // 构建项目数据
      const projectData = {
        name: projectInfo.name,
        description: projectInfo.description,
        industry_type: projectInfo.industry_type,
        max_users: projectInfo.max_users,
        max_storage_gb: projectInfo.max_storage_gb,
        plugins: projectInfo.plugins || []
      };

      // 调用API创建项目
      const response = await tenant.project.create(projectData);

      // 设置当前项目
      if (response && response.id) {
        // 保存项目ID和名称到localStorage
        localStorage.setItem('project_id', response.id);
        localStorage.setItem('project_name', projectInfo.name);

        // 设置用户角色为项目管理员，确保能看到所有菜单
        const userData = JSON.parse(localStorage.getItem('user_data') || '{}');
        userData.role = 'project_admin';
        localStorage.setItem('user_data', JSON.stringify(userData));
      }

      setCreationComplete(true);
      setCreatingProject(false);

      // 不再自动跳转，让用户点击按钮进入项目

    } catch (error) {
      console.error('创建项目失败:', error);
      message.error('创建项目失败: ' + (error.response?.data?.detail || '未知错误'));
      setCreatingProject(false);
    }
  };

  // 返回项目选择页面
  const handleBack = () => {
    navigate('/tenant/projects');
  };

  // 立即进入项目
  const handleEnterProject = () => {
    message.success('正在进入项目: ' + projectInfo.name);

    // 使用window.location.href而不是navigate，确保页面完全刷新
    window.location.href = '/dashboard';
  };

  // 渲染步骤内容
  const renderStepContent = () => {
    switch (current) {
      case 0:
        return renderBasicInfoStep();
      case 1:
        return renderPlanSelectionStep();
      case 2:
        return renderPaymentStep();
      case 3:
        return renderCreationStep();
      default:
        return null;
    }
  };

  // 渲染基本信息步骤
  const renderBasicInfoStep = () => {
    return (
      <div className="step-content">
        <Title level={4}>填写项目基本信息</Title>
        <Paragraph type="secondary">
          请填写项目的基本信息，创建后部分信息可以修改
        </Paragraph>

        <Form
          form={form}
          layout="vertical"
          initialValues={{
            name: projectInfo.name || '',
            description: projectInfo.description || '',
            industry_type: projectInfo.industry_type || 'retail'
          }}
        >
          <Form.Item
            name="name"
            label="项目名称"
            rules={[
              { required: true, message: '请输入项目名称' },
              { max: 100, message: '项目名称不能超过100个字符' }
            ]}
          >
            <Input placeholder="请输入项目名称" />
          </Form.Item>

          <Form.Item
            name="industry_type"
            label="行业类型"
            rules={[{ required: true, message: '请选择行业类型' }]}
          >
            <Select placeholder="请选择行业类型">
              {industries.map(industry => (
                <Option key={industry.code} value={industry.code}>
                  {industry.name}
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="description"
            label="项目描述"
          >
            <TextArea
              placeholder="请输入项目描述"
              autoSize={{ minRows: 3, maxRows: 6 }}
            />
          </Form.Item>
        </Form>
      </div>
    );
  };

  // 渲染套餐选择步骤
  const renderPlanSelectionStep = () => {
    return (
      <div className="step-content">
        <Title level={4}>选择您的套餐</Title>
        <Paragraph type="secondary">
          请选择适合您需求的套餐，不同套餐提供不同的功能和配额
        </Paragraph>

        <Row gutter={[16, 16]} className="plan-cards">
          {PLANS.map(plan => (
            <Col xs={24} sm={12} md={6} key={plan.id}>
              <Card
                className={`plan-card ${selectedPlan === plan.id ? 'selected' : ''}`}
                onClick={() => handlePlanSelect(plan.id)}
                hoverable
                style={{ borderColor: selectedPlan === plan.id ? plan.color : undefined }}
              >
                <div className="plan-header" style={{ backgroundColor: plan.color }}>
                  <div className="plan-name">{plan.name}</div>
                  <div className="plan-price">
                    {plan.price > 0 ? (
                      <>
                        <span className="price-number">{plan.price}</span>
                        <span className="price-unit">元/{plan.period}</span>
                      </>
                    ) : (
                      <span className="price-free">免费</span>
                    )}
                  </div>
                </div>

                <div className="plan-features">
                  {plan.features.map((feature, index) => (
                    <div key={index} className="feature-item">
                      <CheckCircleOutlined /> {feature}
                    </div>
                  ))}
                </div>

                <Button
                  type={selectedPlan === plan.id ? "primary" : "default"}
                  block
                  style={{
                    backgroundColor: selectedPlan === plan.id ? plan.color : undefined,
                    borderColor: selectedPlan === plan.id ? plan.color : undefined
                  }}
                >
                  {selectedPlan === plan.id ? '已选择' : '选择套餐'}
                </Button>
              </Card>
            </Col>
          ))}
        </Row>
      </div>
    );
  };

  // 渲染支付步骤
  const renderPaymentStep = () => {
    const selectedPlanData = PLANS.find(p => p.id === selectedPlan);

    // 如果是免费套餐，直接显示确认信息
    if (selectedPlan === 'basic') {
      return (
        <div className="step-content">
          <Title level={4}>确认创建项目</Title>
          <Paragraph type="secondary">
            您选择的是免费套餐，无需支付，点击"立即创建"按钮开始创建项目
          </Paragraph>

          <Card className="confirmation-card">
            <div className="confirmation-item">
              <span className="label">项目名称:</span>
              <span className="value">{projectInfo.name}</span>
            </div>
            <div className="confirmation-item">
              <span className="label">行业类型:</span>
              <span className="value">
                {industries.find(i => i.code === projectInfo.industry_type)?.name || projectInfo.industry_type}
              </span>
            </div>
            <div className="confirmation-item">
              <span className="label">套餐:</span>
              <span className="value">{selectedPlanData?.name || '基础版'}</span>
            </div>
            <div className="confirmation-item">
              <span className="label">用户数上限:</span>
              <span className="value">{selectedPlanData?.maxUsers || 5}人</span>
            </div>
            <div className="confirmation-item">
              <span className="label">存储空间:</span>
              <span className="value">{selectedPlanData?.maxStorage || 1}GB</span>
            </div>
          </Card>
        </div>
      );
    }

    // 付费套餐，显示支付选项
    return (
      <div className="step-content">
        <Title level={4}>选择支付方式</Title>
        <Paragraph type="secondary">
          请选择支付方式或输入兑换码
        </Paragraph>

        <Card className="payment-card">
          <div className="selected-plan-info">
            <div className="plan-name" style={{ color: selectedPlanData?.color }}>
              {selectedPlanData?.name || '标准版'}
            </div>
            <div className="plan-price">
              {selectedPlanData?.price || 199}元/{selectedPlanData?.period || '月付'}
            </div>
          </div>

          <Divider />

          <Radio.Group
            onChange={handlePaymentMethodChange}
            value={paymentMethod}
            className="payment-methods"
          >
            <Radio value="alipay">支付宝</Radio>
            <Radio value="wechat">微信支付</Radio>
            <Radio value="redeem">使用兑换码</Radio>
          </Radio.Group>

          {paymentMethod === 'redeem' && (
            <div className="redeem-code-section">
              <Input
                placeholder="请输入兑换码"
                value={redeemCode}
                onChange={handleRedeemCodeChange}
                style={{ width: '60%' }}
              />
              <Button
                type="primary"
                onClick={validateRedeemCode}
                style={{ marginLeft: 8 }}
              >
                验证
              </Button>
              <div className="redeem-tip">
                <Text type="secondary">提示: 试试兑换码 "FREE2024"</Text>
              </div>
            </div>
          )}
        </Card>
      </div>
    );
  };

  // 渲染创建步骤
  const renderCreationStep = () => {
    if (creatingProject) {
      return (
        <div className="step-content creation-step">
          <Title level={4}>正在创建项目</Title>
          <Paragraph type="secondary">
            请稍候，系统正在为您创建项目...
          </Paragraph>

          <div className="creation-progress">
            <Progress percent={progress} status="active" />
            <div className="progress-text">
              {progress < 30 && '正在初始化项目...'}
              {progress >= 30 && progress < 60 && '正在配置项目环境...'}
              {progress >= 60 && progress < 90 && '正在设置项目权限...'}
              {progress >= 90 && '即将完成...'}
            </div>
          </div>

          <div className="creation-illustration">
            <img src="/images/creating-project.svg" alt="创建中" />
          </div>
        </div>
      );
    }

    if (creationComplete) {
      return (
        <div className="step-content creation-complete">
          <div className="success-icon">
            <CheckCircleOutlined />
          </div>
          <Title level={3}>项目创建成功！</Title>
          <Paragraph>
            您的项目"{projectInfo.name}"已成功创建，现在可以开始使用了。
          </Paragraph>

          <div className="action-buttons">
            <Button type="primary" size="large" onClick={handleEnterProject}>
              立即进入项目
            </Button>
          </div>
        </div>
      );
    }

    return null;
  };

  // 渲染步骤操作按钮
  const renderStepActions = () => {
    if (current === 3) {
      return null; // 创建步骤不显示操作按钮
    }

    return (
      <div className="step-actions">
        {current > 0 && (
          <Button onClick={handlePrev} style={{ marginRight: 8 }}>
            上一步
          </Button>
        )}
        <Button
          type="primary"
          onClick={handleNext}
        >
          {current === 2 ? '立即创建' : '下一步'}
        </Button>
      </div>
    );
  };

  return (
    <div className="create-project-steps-container">
      <Card className="create-project-steps-card">
        <div className="steps-header">
          <Button
            type="link"
            icon={<ArrowLeftOutlined />}
            onClick={handleBack}
            className="back-button"
          >
            返回项目列表
          </Button>
          <Title level={3}>创建新项目</Title>
        </div>

        <Steps
          current={current}
          onChange={handleStepChange}
          className="project-steps"
          items={[
            {
              title: '填写信息',
              icon: <UserOutlined />
            },
            {
              title: '选择套餐',
              icon: <AppstoreOutlined />
            },
            {
              title: '确认支付',
              icon: <ShopOutlined />
            },
            {
              title: '创建完成',
              icon: <CloudOutlined />
            }
          ]}
        />

        {loadingIndustries && current === 0 ? (
          <div className="loading-container">
            <Spin size="large" />
          </div>
        ) : (
          <>
            <div className="steps-content">
              {renderStepContent()}
            </div>
            {renderStepActions()}
          </>
        )}
      </Card>
    </div>
  );
};

export default CreateProjectSteps;
