import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  Input,
  Form,
  message,
  Typography,
  Space,
  Divider,
  Row,
  Col,
  Spin,
  Empty,
  Modal,
  Tag
} from 'antd';
import {
  DownloadOutlined,
  LockOutlined,
  FileImageOutlined,
  FileTextOutlined,
  FilePdfOutlined,
  FileUnknownOutlined,
  EyeOutlined,
  ClockCircleOutlined,
  UserOutlined
} from '@ant-design/icons';
import { useParams, useNavigate } from 'react-router-dom';
import projectSpaceService from '../../../services/projectSpaceService';

const { Title, Text, Paragraph } = Typography;

/**
 * 分享文件组件
 */
const SharedFile = () => {
  const [loading, setLoading] = useState(true);
  const [fileInfo, setFileInfo] = useState(null);
  const [shareInfo, setShareInfo] = useState(null);
  const [requiresPassword, setRequiresPassword] = useState(false);
  const [password, setPassword] = useState('');
  const [previewVisible, setPreviewVisible] = useState(false);
  const { shareToken } = useParams();
  const navigate = useNavigate();
  
  // 获取分享文件信息
  const fetchSharedFile = async (password = null) => {
    setLoading(true);
    try {
      const response = await projectSpaceService.getSharedFile(shareToken, password);
      
      if (response.success) {
        if (response.data.requires_password) {
          setRequiresPassword(true);
        } else {
          setRequiresPassword(false);
          setFileInfo(response.data.file_info);
          setShareInfo(response.data.share_info);
        }
      } else {
        message.error('获取分享文件失败');
      }
    } catch (error) {
      console.error('获取分享文件失败:', error);
      message.error('获取分享文件失败');
    } finally {
      setLoading(false);
    }
  };
  
  // 验证密码
  const verifyPassword = async () => {
    if (!password) {
      message.warning('请输入访问密码');
      return;
    }
    
    try {
      const response = await projectSpaceService.verifySharePassword(shareToken, password);
      
      if (response.success) {
        setRequiresPassword(false);
        setFileInfo(response.data.file_info);
        setShareInfo(response.data.share_info);
      } else {
        message.error('密码错误');
      }
    } catch (error) {
      console.error('验证密码失败:', error);
      message.error('验证密码失败');
    }
  };
  
  // 下载文件
  const handleDownload = () => {
    if (fileInfo) {
      const downloadUrl = projectSpaceService.getSharedFileDownloadUrl(shareToken, password);
      window.open(downloadUrl, '_blank');
    }
  };
  
  // 预览文件
  const handlePreview = () => {
    if (fileInfo && fileInfo.preview_available) {
      setPreviewVisible(true);
    } else {
      message.info('此文件类型不支持预览');
    }
  };
  
  // 初始化
  useEffect(() => {
    if (shareToken) {
      fetchSharedFile();
    }
  }, [shareToken]);
  
  // 文件图标映射
  const getFileIcon = (mimeType) => {
    if (mimeType.startsWith('image/')) {
      return <FileImageOutlined style={{ fontSize: 48, color: '#1890ff' }} />;
    } else if (mimeType.startsWith('text/')) {
      return <FileTextOutlined style={{ fontSize: 48, color: '#52c41a' }} />;
    } else if (mimeType === 'application/pdf') {
      return <FilePdfOutlined style={{ fontSize: 48, color: '#f5222d' }} />;
    } else {
      return <FileUnknownOutlined style={{ fontSize: 48, color: '#faad14' }} />;
    }
  };
  
  // 文件大小格式化
  const formatFileSize = (size) => {
    if (size < 1024) {
      return size + ' B';
    } else if (size < 1024 * 1024) {
      return (size / 1024).toFixed(2) + ' KB';
    } else if (size < 1024 * 1024 * 1024) {
      return (size / (1024 * 1024)).toFixed(2) + ' MB';
    } else {
      return (size / (1024 * 1024 * 1024)).toFixed(2) + ' GB';
    }
  };
  
  // 渲染密码输入表单
  const renderPasswordForm = () => {
    return (
      <Card style={{ width: 400, margin: '100px auto' }}>
        <div style={{ textAlign: 'center', marginBottom: 24 }}>
          <LockOutlined style={{ fontSize: 48, color: '#1890ff' }} />
          <Title level={4} style={{ marginTop: 16 }}>此文件需要密码访问</Title>
        </div>
        <Form>
          <Form.Item>
            <Input.Password
              prefix={<LockOutlined />}
              placeholder="请输入访问密码"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              onPressEnter={verifyPassword}
            />
          </Form.Item>
          <Form.Item>
            <Button type="primary" block onClick={verifyPassword}>
              验证密码
            </Button>
          </Form.Item>
        </Form>
      </Card>
    );
  };
  
  // 渲染文件信息
  const renderFileInfo = () => {
    if (!fileInfo) {
      return (
        <Empty
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description="文件不存在或已被删除"
        />
      );
    }
    
    return (
      <Card style={{ width: 600, margin: '50px auto' }}>
        <div style={{ textAlign: 'center', marginBottom: 24 }}>
          {getFileIcon(fileInfo.mime_type)}
          <Title level={4} style={{ marginTop: 16 }}>{fileInfo.name}</Title>
        </div>
        
        <Divider />
        
        <Row gutter={16}>
          <Col span={12}>
            <Text strong>文件类型：</Text>
            <Text>{fileInfo.mime_type}</Text>
          </Col>
          <Col span={12}>
            <Text strong>文件大小：</Text>
            <Text>{formatFileSize(fileInfo.size)}</Text>
          </Col>
        </Row>
        
        <Row gutter={16} style={{ marginTop: 16 }}>
          <Col span={12}>
            <Text strong>上传时间：</Text>
            <Text>{new Date(fileInfo.created_at).toLocaleString()}</Text>
          </Col>
          <Col span={12}>
            {shareInfo && shareInfo.expires_at && (
              <>
                <Text strong>过期时间：</Text>
                <Text>{new Date(shareInfo.expires_at).toLocaleString()}</Text>
              </>
            )}
          </Col>
        </Row>
        
        {shareInfo && (
          <div style={{ marginTop: 16 }}>
            <Space>
              <Tag icon={<UserOutlined />} color="blue">
                分享次数：{shareInfo.access_count}
              </Tag>
              {shareInfo.expires_at && (
                <Tag icon={<ClockCircleOutlined />} color="orange">
                  {new Date(shareInfo.expires_at) > new Date() ? '有效期内' : '已过期'}
                </Tag>
              )}
            </Space>
          </div>
        )}
        
        <Divider />
        
        <div style={{ textAlign: 'center' }}>
          <Space>
            <Button
              type="primary"
              icon={<DownloadOutlined />}
              onClick={handleDownload}
            >
              下载文件
            </Button>
            {fileInfo.preview_available && (
              <Button
                icon={<EyeOutlined />}
                onClick={handlePreview}
              >
                预览文件
              </Button>
            )}
          </Space>
        </div>
        
        {fileInfo.preview_available && fileInfo.mime_type.startsWith('image/') && (
          <div style={{ marginTop: 24, textAlign: 'center' }}>
            <img
              src={projectSpaceService.getSharedFileThumbnailUrl(shareToken, password)}
              alt={fileInfo.name}
              style={{ maxWidth: '100%', maxHeight: '300px' }}
            />
          </div>
        )}
      </Card>
    );
  };
  
  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '100px 0' }}>
        <Spin size="large" />
        <p style={{ marginTop: 16 }}>加载中...</p>
      </div>
    );
  }
  
  if (requiresPassword) {
    return renderPasswordForm();
  }
  
  return (
    <div>
      {renderFileInfo()}
      
      {/* 文件预览模态框 */}
      <Modal
        title={fileInfo?.name}
        open={previewVisible}
        onCancel={() => setPreviewVisible(false)}
        footer={null}
        width={800}
      >
        {fileInfo && (
          <div style={{ textAlign: 'center' }}>
            {fileInfo.mime_type.startsWith('image/') ? (
              <img
                src={projectSpaceService.getSharedFileDownloadUrl(shareToken, password)}
                alt={fileInfo.name}
                style={{ maxWidth: '100%', maxHeight: '500px' }}
              />
            ) : fileInfo.mime_type === 'application/pdf' ? (
              <iframe
                src={projectSpaceService.getSharedFileDownloadUrl(shareToken, password)}
                width="100%"
                height="500px"
                title={fileInfo.name}
              />
            ) : (
              <div>
                <p>此文件类型不支持预览</p>
                <Button
                  type="primary"
                  icon={<DownloadOutlined />}
                  onClick={handleDownload}
                >
                  下载文件
                </Button>
              </div>
            )}
          </div>
        )}
      </Modal>
    </div>
  );
};

export default SharedFile;
