import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Progress,
  Table,
  Tag,
  Space,
  Button,
  Alert,
  Spin,
  Typography,
  Divider,
  List,
  Avatar,
  Badge
} from 'antd';
import {
  ShopOutlined,
  DollarOutlined,
  RiseOutlined,
  FallOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  ClockCircleOutlined,
  SyncOutlined,
  RobotOutlined,
  EyeOutlined
} from '@ant-design/icons';
import { Line, Column, Pie } from '@ant-design/plots';
import { useParams } from 'react-router-dom';
import multiChannelService from '../services/multiChannelService';

const { Title, Text } = Typography;

const Dashboard = () => {
  const { projectId } = useParams();
  const [loading, setLoading] = useState(true);
  const [overviewData, setOverviewData] = useState(null);
  const [performanceData, setPerformanceData] = useState([]);
  const [trendsData, setTrendsData] = useState([]);
  const [aiInsights, setAiInsights] = useState([]);

  // 加载数据
  useEffect(() => {
    loadDashboardData();
  }, [projectId]);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      
      // 并行加载所有数据
      const [overview, performance, trends, insights] = await Promise.all([
        multiChannelService.getDashboardOverview(projectId),
        multiChannelService.getChannelPerformance(projectId, 7),
        multiChannelService.getOperationTrends(projectId, 30),
        multiChannelService.getAiInsights(projectId)
      ]);

      setOverviewData(overview.data);
      setPerformanceData(performance.data);
      setTrendsData(trends.data);
      setAiInsights(insights.data);
      
    } catch (error) {
      console.error('加载仪表板数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 渠道健康状态颜色
  const getHealthColor = (score) => {
    if (score >= 80) return '#52c41a';
    if (score >= 60) return '#faad14';
    return '#ff4d4f';
  };

  // 操作状态标签
  const getStatusTag = (status) => {
    const statusMap = {
      'success': { color: 'success', text: '成功' },
      'failed': { color: 'error', text: '失败' },
      'pending': { color: 'processing', text: '进行中' },
      'warning': { color: 'warning', text: '警告' }
    };
    
    const config = statusMap[status] || { color: 'default', text: status };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" tip="加载运营数据中..." />
      </div>
    );
  }

  return (
    <div>

      {/* AI洞察提醒 */}
      {aiInsights?.alerts && aiInsights.alerts.length > 0 && (
        <Alert
          message="AI智能提醒"
          description={
            <List
              size="small"
              dataSource={aiInsights.alerts.slice(0, 3)}
              renderItem={item => (
                <List.Item>
                  <Text>{item.message}</Text>
                </List.Item>
              )}
            />
          }
          type="info"
          showIcon
          style={{ marginBottom: 24 }}
          action={
            <Button size="small" type="link">
              查看全部建议
            </Button>
          }
        />
      )}

      {/* 核心指标卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="总渠道数"
              value={overviewData?.summary?.total_channels || 0}
              prefix={<ShopOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
            <div style={{ marginTop: 8 }}>
              <Text type="secondary">
                已配置: {overviewData?.summary?.configured_channels || 0}
              </Text>
            </div>
          </Card>
        </Col>
        
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="商品映射"
              value={overviewData?.summary?.total_product_mappings || 0}
              prefix={<DollarOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
            <div style={{ marginTop: 8 }}>
              <Text type="secondary">
                跨渠道商品数量
              </Text>
            </div>
          </Card>
        </Col>
        
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="今日操作"
              value={overviewData?.summary?.today_operations || 0}
              prefix={<SyncOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
            <div style={{ marginTop: 8 }}>
              <Text type="secondary">
                自动化执行次数
              </Text>
            </div>
          </Card>
        </Col>
        
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <div>
              <Text strong>渠道健康度</Text>
              <div style={{ marginTop: 8 }}>
                <Progress
                  type="circle"
                  size={80}
                  percent={overviewData?.summary?.channel_health_score || 0}
                  strokeColor={getHealthColor(overviewData?.summary?.channel_health_score || 0)}
                />
              </div>
            </div>
          </Card>
        </Col>
      </Row>

      {/* 渠道状态和性能 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} lg={12}>
          <Card title="渠道状态分布" extra={<Button type="link">管理渠道</Button>}>
            <Row gutter={16}>
              <Col span={12}>
                <Statistic
                  title="活跃渠道"
                  value={overviewData?.channel_status?.active_channels || 0}
                  valueStyle={{ color: '#52c41a' }}
                  prefix={<CheckCircleOutlined />}
                />
              </Col>
              <Col span={12}>
                <Statistic
                  title="未配置API"
                  value={overviewData?.channel_status?.api_not_configured || 0}
                  valueStyle={{ color: '#faad14' }}
                  prefix={<ExclamationCircleOutlined />}
                />
              </Col>
            </Row>
            
            <Divider />
            
            <div>
              <Text strong>API配置进度</Text>
              <Progress
                percent={
                  overviewData?.channel_status?.total_channels > 0
                    ? Math.round((overviewData?.channel_status?.api_configured / overviewData?.channel_status?.total_channels) * 100)
                    : 0
                }
                strokeColor="#1890ff"
                style={{ marginTop: 8 }}
              />
            </div>
          </Card>
        </Col>
        
        <Col xs={24} lg={12}>
          <Card title="渠道性能排行" extra={<Button type="link">查看详情</Button>}>
            {performanceData?.channels && performanceData.channels.length > 0 ? (
              <List
                size="small"
                dataSource={performanceData.channels.slice(0, 5)}
                renderItem={(item, index) => (
                  <List.Item>
                    <List.Item.Meta
                      avatar={
                        <Badge count={index + 1} style={{ backgroundColor: '#1890ff' }}>
                          <Avatar icon={<ShopOutlined />} />
                        </Badge>
                      }
                      title={item.channel_name}
                      description={`销售额: ¥${item.sales?.toLocaleString() || 0}`}
                    />
                    <div>
                      {item.growth_rate > 0 ? (
                        <Tag color="green" icon={<RiseOutlined />}>
                          +{item.growth_rate}%
                        </Tag>
                      ) : (
                        <Tag color="red" icon={<FallOutlined />}>
                          {item.growth_rate}%
                        </Tag>
                      )}
                    </div>
                  </List.Item>
                )}
              />
            ) : (
              <div style={{ textAlign: 'center', padding: '20px' }}>
                <Text type="secondary">暂无性能数据</Text>
              </div>
            )}
          </Card>
        </Col>
      </Row>

      {/* 操作趋势图表 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} lg={16}>
          <Card title="操作趋势" extra={<Button type="link">查看详情</Button>}>
            {trendsData?.trends && trendsData.trends.length > 0 ? (
              <Line
                data={trendsData.trends.flatMap(series => 
                  series.data.map(point => ({
                    date: point.date,
                    value: point.value,
                    type: series.operation_type
                  }))
                )}
                xField="date"
                yField="value"
                seriesField="type"
                height={300}
                smooth={true}
                point={{ size: 3 }}
                legend={{ position: 'top' }}
              />
            ) : (
              <div style={{ textAlign: 'center', padding: '50px' }}>
                <Text type="secondary">暂无趋势数据</Text>
              </div>
            )}
          </Card>
        </Col>
        
        <Col xs={24} lg={8}>
          <Card title="最近操作记录">
            {overviewData?.recent_operations && overviewData.recent_operations.length > 0 ? (
              <List
                size="small"
                dataSource={overviewData.recent_operations.slice(0, 8)}
                renderItem={item => (
                  <List.Item>
                    <List.Item.Meta
                      avatar={<ClockCircleOutlined style={{ color: '#1890ff' }} />}
                      title={
                        <Space>
                          <Text strong>{item.operation_type}</Text>
                          {getStatusTag(item.result_status)}
                        </Space>
                      }
                      description={
                        <div>
                          <Text type="secondary" style={{ fontSize: 12 }}>
                            {new Date(item.executed_at).toLocaleString()}
                          </Text>
                          {item.result_message && (
                            <div>
                              <Text type="secondary" style={{ fontSize: 12 }}>
                                {item.result_message}
                              </Text>
                            </div>
                          )}
                        </div>
                      }
                    />
                  </List.Item>
                )}
              />
            ) : (
              <div style={{ textAlign: 'center', padding: '20px' }}>
                <Text type="secondary">暂无操作记录</Text>
              </div>
            )}
          </Card>
        </Col>
      </Row>

      {/* 快速操作 */}
      <Card title="快速操作">
        <Space wrap>
          <Button type="primary" icon={<ShopOutlined />}>
            添加渠道
          </Button>
          <Button icon={<DollarOutlined />}>
            批量调价
          </Button>
          <Button icon={<SyncOutlined />}>
            同步商品
          </Button>
          <Button icon={<RobotOutlined />}>
            创建自动化规则
          </Button>
          <Button icon={<EyeOutlined />}>
            添加竞品监控
          </Button>
        </Space>
      </Card>
    </div>
  );
};

export default Dashboard;
