#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
用户模型
"""

import uuid
from datetime import datetime
from typing import List, Optional, Dict, Any
from sqlalchemy import Column, String, Boolean, ForeignKey, DateTime, Text, JSON, func, UniqueConstraint

from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship

from passlib.context import CryptContext

from db.database import Base

# 密码哈希上下文
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

class User(Base):
    """用户模型"""
    __tablename__ = "users"

    # 确保表名正确
    __table_args__ = {'extend_existing': True}

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=True)  # 租户ID，可为空表示系统级用户
    service_provider_id = Column(UUID(as_uuid=True), ForeignKey("service_providers.id"), nullable=True)  # 服务商ID，可为空
    username = Column(String(50), nullable=False)
    email = Column(String(100), nullable=False)
    password_hash = Column(String(255), nullable=False)
    full_name = Column(String(100), nullable=True)
    phone = Column(String(20), nullable=True)
    avatar_url = Column(String(255), nullable=True)
    status = Column(String(20), default="active", nullable=False)  # active, inactive, locked
    is_system_admin = Column(Boolean, default=False)  # 是否系统管理员(超级管理员)
    is_super_admin = Column(Boolean, default=False)  # 是否超级管理员(与is_system_admin同义，更明确的命名)
    is_service_provider_admin = Column(Boolean, default=False)  # 是否服务商管理员
    is_tenant_admin = Column(Boolean, default=False)  # 是否租户管理员
    role = Column(String(50), nullable=True)  # 用户角色
    permissions = Column(JSON, default=dict)  # 用户权限
    settings = Column(JSON, default=dict)  # 用户设置
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    last_login = Column(DateTime(timezone=True), nullable=True)

    # 第三方登录相关字段
    dingtalk_union_id = Column(String(100), nullable=True, unique=True)  # 钉钉unionId
    dingtalk_user_id = Column(String(100), nullable=True)  # 钉钉openId
    wechat_unionid = Column(String(100), nullable=True, unique=True)  # 微信unionid
    wechat_openid = Column(String(100), nullable=True, unique=True)  # 微信openid

    # 关系
    tenant = relationship("Tenant", back_populates="users")
    service_provider = relationship("ServiceProvider", back_populates="admins")
    project_users = relationship("ProjectUser", back_populates="user", cascade="all, delete-orphan")
    third_party_accounts = relationship("ThirdPartyAccount", back_populates="user", cascade="all, delete-orphan")
    ai_usages = relationship("AIUsage", back_populates="user", cascade="all, delete-orphan")
    
    # 异步任务关系
    async_tasks = relationship("AsyncTask", back_populates="user", cascade="all, delete-orphan")

    # 非持久化属性，用于存储项目相关信息
    project_role = None  # 用户在当前项目中的角色代码
    project_role_name = None  # 用户在当前项目中的角色名称
    is_project_admin = None  # 用户是否为项目管理员
    project_id = None  # 当前项目ID

    # 知识库相关关系 - 使用延迟加载
    # 使用纯表名和列名的字符串
    created_knowledge_documents = relationship(
        "KnowledgeDocument",
        primaryjoin="users.c.id == knowledge_documents.c.created_by",
        foreign_keys="knowledge_documents.c.created_by",
        backref="creator",
        lazy="dynamic",
        post_update=True  # 避免循环依赖
    )

    # 使用纯表名和列名的字符串
    updated_knowledge_documents = relationship(
        "KnowledgeDocument",
        primaryjoin="users.c.id == knowledge_documents.c.updated_by",
        foreign_keys="knowledge_documents.c.updated_by",
        backref="updater",
        lazy="dynamic",
        post_update=True  # 避免循环依赖
    )

    # 添加 is_superuser 属性，兼容旧代码
    @property
    def is_superuser(self):
        return self.is_system_admin or self.is_super_admin

    @is_superuser.setter
    def is_superuser(self, value):
        self.is_system_admin = value
        self.is_super_admin = value

    @staticmethod
    def get_password_hash(password):
        """获取密码哈希"""
        return pwd_context.hash(password)

    def verify_password(self, password):
        """验证密码"""
        return pwd_context.verify(password, self.password_hash)

    def get_auth_header(self, request: Optional[Any] = None) -> Optional[str]:
        """
        获取当前用户的认证头 (Bearer token)。
        优先从当前请求的 Authorization header 获取 (如果提供了 request 对象)。
        其次尝试从环境变量 SYSTEM_API_TOKEN 获取 (通常用于系统内部调用)。
        如果都无法获取，则尝试生成一个新的临时令牌。
        """
        import logging
        logger = logging.getLogger(__name__)

        # 尝试从当前请求获取 (如果 FastAPI Request 对象被传入)
        # Ensure 'request' and 'request.headers' are not None before accessing.
        if request and hasattr(request, 'headers') and request.headers is not None:
            auth_header_val = request.headers.get("authorization")
            if auth_header_val and auth_header_val.lower().startswith("bearer "):
                logger.info(f"从请求头获取到认证令牌: {auth_header_val[:20]}...")
                return auth_header_val
            # 如果是其他类型的 Authorization (如 Basic)，则不应使用它作为 Bearer token
            # 这里可以选择记录一个警告或直接忽略

        # 备选：尝试从环境变量获取系统级API令牌
        import os
        system_api_token = os.environ.get("SYSTEM_API_TOKEN")
        if system_api_token:
            # 确保返回的是 "Bearer " 格式
            if system_api_token.lower().startswith("bearer "):
                logger.info(f"从环境变量获取到认证令牌: {system_api_token[:20]}...")
                return system_api_token
            else:
                token_with_prefix = f"Bearer {system_api_token}"
                logger.info(f"从环境变量获取到认证令牌并添加前缀: {token_with_prefix[:20]}...")
                return token_with_prefix

        # 如果上述方法都无法获取令牌，尝试生成一个临时令牌
        try:
            from datetime import datetime, timedelta
            from jose import jwt
            from core.config import settings

            # 创建一个临时令牌，有效期为10分钟
            payload = {
                "sub": self.username,
                "user_id": str(self.id),
                "exp": datetime.utcnow() + timedelta(minutes=10)
            }

            # 如果用户有租户ID，添加到令牌中
            if self.tenant_id:
                payload["tenant_id"] = str(self.tenant_id)

            # 如果用户有服务商ID，添加到令牌中
            if self.service_provider_id:
                payload["service_provider_id"] = str(self.service_provider_id)

            # 添加用户角色和权限
            payload["role"] = self.role
            payload["permissions"] = self.permissions
            payload["is_system_admin"] = self.is_system_admin
            payload["is_super_admin"] = self.is_super_admin
            payload["is_service_provider_admin"] = self.is_service_provider_admin
            payload["is_tenant_admin"] = self.is_tenant_admin

            # 生成令牌
            token = jwt.encode(payload, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
            token_with_prefix = f"Bearer {token}"
            logger.info(f"生成临时认证令牌: {token_with_prefix[:20]}...")
            return token_with_prefix
        except Exception as e:
            logger.error(f"生成临时认证令牌失败: {str(e)}")
        return None

    def __repr__(self):
        return f"<User(id={self.id}, username='{self.username}', email='{self.email}')>"

class UserActivity(Base):
    """用户活动日志"""
    __tablename__ = "user_activities"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=True)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id"), nullable=True)
    activity_type = Column(String(50), nullable=False)  # login, logout, create, update, delete, etc.
    resource_type = Column(String(50), nullable=True)  # 资源类型
    resource_id = Column(String(50), nullable=True)  # 资源ID
    description = Column(Text, nullable=True)
    ip_address = Column(String(50), nullable=True)
    user_agent = Column(String(255), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # 关系
    user = relationship("User", backref="activities")

    def __repr__(self):
        return f"<UserActivity(id={self.id}, user_id={self.user_id}, activity_type='{self.activity_type}')>"

class ThirdPartyAccount(Base):
    """第三方账号关联"""
    __tablename__ = "third_party_accounts"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=True)
    platform = Column(String(50), nullable=False)  # wechat, dingtalk, feishu, etc.
    platform_user_id = Column(String(100), nullable=False)  # 第三方用户ID
    platform_username = Column(String(100), nullable=True)  # 第三方用户名
    avatar_url = Column(String(255), nullable=True)  # 头像URL
    access_token = Column(String(255), nullable=True)  # 访问令牌
    refresh_token = Column(String(255), nullable=True)  # 刷新令牌
    expires_at = Column(DateTime(timezone=True), nullable=True)  # 令牌过期时间
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # 关系
    user = relationship("User", back_populates="third_party_accounts")

    # 兼容性属性
    @property
    def provider(self):
        return self.platform

    @provider.setter
    def provider(self, value):
        self.platform = value

    @property
    def provider_user_id(self):
        return self.platform_user_id

    @provider_user_id.setter
    def provider_user_id(self, value):
        self.platform_user_id = value

    @property
    def provider_username(self):
        return self.platform_username

    @provider_username.setter
    def provider_username(self, value):
        self.platform_username = value

    def __repr__(self):
        return f"<ThirdPartyAccount(id={self.id}, user_id={self.user_id}, platform='{self.platform}')>"
