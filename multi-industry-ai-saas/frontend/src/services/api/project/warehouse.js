import api from '../httpClient';
import { getProjectId } from '../httpClient';

/**
 * 项目-仓库管理API
 */
const warehouse = {
  /**
   * 获取仓库列表
   * @param {Object} params - 查询参数
   * @returns {Promise} - 仓库列表
   */
  getList: async (params = {}) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.get(`/project/${projectId}/basic/warehouses`, { params });
  },

  /**
   * 获取仓库详情
   * @param {string} id - 仓库ID
   * @returns {Promise} - 仓库详情
   */
  getDetail: async (id) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.get(`/project/${projectId}/basic/warehouses/${id}`);
  },

  /**
   * 创建仓库
   * @param {Object} data - 仓库数据
   * @returns {Promise} - 创建结果
   */
  create: async (data) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.post(`/project/${projectId}/basic/warehouses`, data);
  },

  /**
   * 更新仓库
   * @param {string} id - 仓库ID
   * @param {Object} data - 仓库数据
   * @returns {Promise} - 更新结果
   */
  update: async (id, data) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.put(`/project/${projectId}/basic/warehouses/${id}`, data);
  },

  /**
   * 删除仓库
   * @param {string} id - 仓库ID
   * @returns {Promise} - 删除结果
   */
  delete: async (id) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.delete(`/project/${projectId}/basic/warehouses/${id}`);
  },

  /**
   * 获取仓库库存列表
   * @param {string} warehouseId - 仓库ID
   * @param {Object} params - 查询参数
   * @returns {Promise} - 仓库库存列表
   */
  getInventory: async (warehouseId, params = {}) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.get(`/project/${projectId}/warehouses/${warehouseId}/inventory`, { params });
  },

  /**
   * 更新仓库库存
   * @param {string} warehouseId - 仓库ID
   * @param {string} inventoryId - 库存ID
   * @param {Object} data - 库存数据
   * @returns {Promise} - 更新结果
   */
  updateInventory: async (warehouseId, inventoryId, data) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.put(`/project/${projectId}/warehouses/${warehouseId}/inventory/${inventoryId}`, data);
  },

  /**
   * 获取仓库入库单列表
   * @param {string} warehouseId - 仓库ID
   * @param {Object} params - 查询参数
   * @returns {Promise} - 仓库入库单列表
   */
  getInbounds: async (warehouseId, params = {}) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.get(`/project/${projectId}/warehouses/${warehouseId}/inbounds`, { params });
  },

  /**
   * 创建仓库入库单
   * @param {string} warehouseId - 仓库ID
   * @param {Object} data - 入库单数据
   * @returns {Promise} - 创建结果
   */
  createInbound: async (warehouseId, data) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.post(`/project/${projectId}/warehouses/${warehouseId}/inbounds`, data);
  },

  /**
   * 获取仓库出库单列表
   * @param {string} warehouseId - 仓库ID
   * @param {Object} params - 查询参数
   * @returns {Promise} - 仓库出库单列表
   */
  getOutbounds: async (warehouseId, params = {}) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.get(`/project/${projectId}/warehouses/${warehouseId}/outbounds`, { params });
  },

  /**
   * 创建仓库出库单
   * @param {string} warehouseId - 仓库ID
   * @param {Object} data - 出库单数据
   * @returns {Promise} - 创建结果
   */
  createOutbound: async (warehouseId, data) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.post(`/project/${projectId}/warehouses/${warehouseId}/outbounds`, data);
  },

  /**
   * 获取入库单详情
   * @param {string} warehouseId - 仓库ID
   * @param {string} inboundId - 入库单ID
   * @returns {Promise} - 入库单详情
   */
  getInboundDetail: async (warehouseId, inboundId) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.get(`/project/${projectId}/warehouses/${warehouseId}/inbounds/${inboundId}`);
  },

  /**
   * 更新入库单
   * @param {string} warehouseId - 仓库ID
   * @param {string} inboundId - 入库单ID
   * @param {Object} data - 入库单数据
   * @returns {Promise} - 更新结果
   */
  updateInbound: async (warehouseId, inboundId, data) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.put(`/project/${projectId}/warehouses/${warehouseId}/inbounds/${inboundId}`, data);
  },

  /**
   * 删除入库单
   * @param {string} warehouseId - 仓库ID
   * @param {string} inboundId - 入库单ID
   * @returns {Promise} - 删除结果
   */
  deleteInbound: async (warehouseId, inboundId) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.delete(`/project/${projectId}/warehouses/${warehouseId}/inbounds/${inboundId}`);
  },

  /**
   * 获取仓库管理员用户列表
   * @returns {Promise} - 仓库管理员用户列表
   */
  getManagers: async () => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.get(`/project/${projectId}/basic/warehouses/managers`);
  },

  /**
   * 确认入库单
   * @param {string} warehouseId - 仓库ID
   * @param {string} inboundId - 入库单ID
   * @returns {Promise} - 确认结果
   */
  confirmInbound: async (warehouseId, inboundId) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.post(`/project/${projectId}/warehouses/${warehouseId}/inbounds/${inboundId}/confirm`);
  }
};

export default warehouse;
