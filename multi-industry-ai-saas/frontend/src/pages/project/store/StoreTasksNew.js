import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Input,
  DatePicker,
  Form,
  Select,
  Modal,
  message,
  Typography,
  Tabs,
  Statistic,
  Row,
  Col,
  Badge,
  Avatar,
  Tooltip,
  Dropdown,
  Menu,
  Progress,
  Upload
} from 'antd';
import {
  SearchOutlined,
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  ReloadOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ClockCircleOutlined,
  CalendarOutlined,
  MoreOutlined,
  SyncOutlined,
  UploadOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import dayjs from 'dayjs';
import apiService from '../../../services/api';

const { Title, Text } = Typography;
const { RangePicker } = DatePicker;
const { Option } = Select;
const { TextArea } = Input;

// 任务状态标签颜色映射
const statusColors = {
  pending: 'default',
  in_progress: 'processing',
  completed: 'success',
  overdue: 'error',
  cancelled: 'warning'
};

// 任务状态文本映射
const statusTexts = {
  pending: '待处理',
  in_progress: '进行中',
  completed: '已完成',
  overdue: '已逾期',
  cancelled: '已取消'
};

// 任务优先级标签颜色映射
const priorityColors = {
  low: 'default',
  medium: 'blue',
  high: 'orange',
  urgent: 'red'
};

// 任务优先级文本映射
const priorityTexts = {
  low: '低',
  medium: '中',
  high: '高',
  urgent: '紧急'
};

// 任务类型映射
const taskTypeTexts = {
  daily: '每日',
  weekly: '每周',
  monthly: '每月',
  custom: '自定义'
};

// 任务类别映射
const categoryTexts = {
  store: '门店',
  operation: '运营',
  marketing: '营销',
  inventory: '库存',
  finance: '财务',
  other: '其他'
};

/**
 * 门店任务管理组件
 */
const StoreTasksNew = () => {
  // 状态
  const [activeTab, setActiveTab] = useState('1');
  const [loading, setLoading] = useState(false);
  const [loadingUsers, setLoadingUsers] = useState(false);
  const [tasks, setTasks] = useState([]);
  const [stores, setStores] = useState([]);
  const [users, setUsers] = useState([]);
  const [filteredUsers, setFilteredUsers] = useState([]);
  const [selectedStores, setSelectedStores] = useState([]);
  const [taskStats, setTaskStats] = useState({
    total: 0,
    pending: 0,
    in_progress: 0,
    completed: 0,
    overdue: 0,
    cancelled: 0
  });
  const [storeStats, setStoreStats] = useState([]);
  const [selectedTask, setSelectedTask] = useState(null);
  const [modalVisible, setModalVisible] = useState(false);
  const [fileList, setFileList] = useState([]);
  const [form] = Form.useForm();
  const [filterForm] = Form.useForm();
  const [filters, setFilters] = useState({
    status: undefined,
    priority: undefined,
    category: undefined,
    assignee: undefined,
    dateRange: null,
    search: ''
  });
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0
  });
  const navigate = useNavigate();

  // 初始化
  useEffect(() => {
    // 先获取门店和用户数据
    fetchStores();

    // 获取用户数据并确保设置过滤后的用户列表
    fetchUsers().then(() => {
      // 确保用户数据加载后设置过滤后的用户列表
      setTimeout(() => {
        if (users.length > 0 && filteredUsers.length === 0) {
          setFilteredUsers(users);
        }
      }, 500);
    });

    // 获取任务列表
    fetchTasks();
  }, []);

  // 获取门店列表
  const fetchStores = async () => {
    try {
      const response = await apiService.project.store.getList();
      if (response && response.items) {
        setStores(response.items);
      }
    } catch (error) {
      console.error('获取门店列表失败:', error);
      message.error('获取门店列表失败');
    }
  };

  // 获取用户列表
  const fetchUsers = async (storeIds = []) => {
    return new Promise(async (resolve) => {
      try {
        setLoadingUsers(true);
        const response = await apiService.project.user.getList();

        if (response && Array.isArray(response)) {
          // 处理返回的数据格式
          const allUsers = response
            .filter(item => item.success && item.data)
            .map(item => {
              const userData = item.data;
              // 确保用户数据包含必要的字段
              return {
                ...userData,
                // 如果没有name，使用username
                name: userData.name || userData.username,
                // 如果有多个门店名称，用逗号连接
                store_name: Array.isArray(userData.store_names) && userData.store_names.length > 0
                  ? userData.store_names.join(', ')
                  : ''
              };
            });

          setUsers(allUsers);

          // 如果选择了特定门店，过滤用户
          if (storeIds && storeIds.length > 0 && !storeIds.includes('all')) {
            // 过滤出选定门店的用户
            const storeUsers = allUsers.filter(user => {
              // 检查用户是否属于选定的门店
              if (Array.isArray(user.store_ids)) {
                return user.store_ids.some(storeId =>
                  storeIds.includes(storeId.toString())
                );
              }
              return false;
            });

            setFilteredUsers(storeUsers.length > 0 ? storeUsers : allUsers);
          } else {
            // 如果选择了"所有门店"或没有选择门店，显示所有用户
            setFilteredUsers(allUsers);
          }
        } else if (response && response.items) {
          // 兼容原有的数据格式
          const allUsers = response.items;
          setUsers(allUsers);
          setFilteredUsers(allUsers);
        } else {
          // 如果没有数据，设置为空数组
          setUsers([]);
          setFilteredUsers([]);
        }
        resolve(true);
      } catch (error) {
        console.error('获取用户列表失败:', error);
        message.error('获取用户列表失败');
        resolve(false);
      } finally {
        setLoadingUsers(false);
      }
    });
  };

  // 处理门店选择变化
  const handleStoreChange = (values) => {
    // 检查是否选择了"所有门店"
    if (values && values.includes('all')) {
      // 如果选择了"所有门店"，移除其他所有选项
      form.setFieldsValue({ store_id: ['all'] });
      setSelectedStores(['all']);
      fetchUsers(); // 加载所有用户
    } else if (values && values.length > 0) {
      // 如果选择了特定门店，加载这些门店的用户
      setSelectedStores(values);
      fetchUsers(values);
    } else {
      // 如果没有选择任何门店，清空用户列表
      setSelectedStores([]);
      setFilteredUsers([]);
    }
  };

  // 获取任务列表
  const fetchTasks = async () => {
    setLoading(true);
    try {
      // 构建查询参数
      const params = {
        status: filters.status,
        priority: filters.priority,
        category: filters.category,
        assignee_id: filters.assignee,
        start_date: filters.dateRange?.[0]?.format('YYYY-MM-DD'),
        end_date: filters.dateRange?.[1]?.format('YYYY-MM-DD'),
        search: filters.search || undefined,
        page: pagination.current,
        size: pagination.pageSize
      };

      try {
        // 获取当前用户信息
        const currentUser = await apiService.project.user.getCurrentUser();

        // 如果当前用户不是管理员，则只显示分配给自己的任务
        if (currentUser && !currentUser.is_admin && !currentUser.is_project_admin) {
          // 检查用户是否属于门店部门
          const isStoreUser = currentUser.roles && currentUser.roles.some(
            role => role.name.includes('门店') || role.name.includes('store')
          );

          if (isStoreUser) {
            // 门店部门用户可以看到所有门店任务
            params.category = 'store';
          } else {
            // 非门店部门用户只能看到分配给自己的任务
            params.assignee_id = currentUser.id;
          }
        }
      } catch (error) {
        console.error('获取当前用户信息失败:', error);
      }

      // 调用API获取任务列表
      try {
        const response = await apiService.project.routineTask.getList(params);
        if (response && response.items) {
          setTasks(response.items);
          setPagination({
            ...pagination,
            total: response.total || 0
          });

          // 更新任务统计
          updateTaskStats(response.items);

          // 更新门店统计
          updateStoreStats(response.items);
        } else {
          setTasks([]);
        }
      } catch (error) {
        console.error('获取任务列表失败:', error);
        message.error('获取任务列表失败: ' + (error.message || '未知错误'));
        setTasks([]);
      }
    } catch (error) {
      console.error('获取任务列表失败:', error);
      message.error('获取任务列表失败: ' + (error.message || '未知错误'));
    } finally {
      setLoading(false);
    }
  };

  // 更新任务统计
  const updateTaskStats = (taskList) => {
    const stats = {
      total: taskList.length,
      pending: 0,
      in_progress: 0,
      completed: 0,
      overdue: 0,
      cancelled: 0
    };

    taskList.forEach(task => {
      if (task.status) {
        stats[task.status] = (stats[task.status] || 0) + 1;
      }
    });

    setTaskStats(stats);
  };

  // 更新门店统计
  const updateStoreStats = (taskList) => {
    const storeMap = new Map();

    taskList.forEach(task => {
      if (task.store_id && task.store_name) {
        if (!storeMap.has(task.store_id)) {
          storeMap.set(task.store_id, {
            store_id: task.store_id,
            store_name: task.store_name,
            total: 0,
            pending: 0,
            in_progress: 0,
            completed: 0,
            overdue: 0,
            cancelled: 0,
            completion_rate: 0
          });
        }

        const storeStat = storeMap.get(task.store_id);
        storeStat.total += 1;

        if (task.status) {
          storeStat[task.status] = (storeStat[task.status] || 0) + 1;
        }
      }
    });

    // 计算完成率
    storeMap.forEach(stat => {
      stat.completion_rate = stat.total > 0 ? Math.round((stat.completed / stat.total) * 100) : 0;
    });

    setStoreStats(Array.from(storeMap.values()));
  };
