/* 日常店务页面样式 */
.daily-operations-page {
  margin-bottom: 24px;
}

/* 到货确认样式 */
.arrival-confirmation .ant-statistic-title {
  font-size: 14px;
}

.arrival-confirmation .ant-card {
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.arrival-confirmation .ant-table-wrapper {
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 到货确认模态框样式 */
.arrival-confirmation .ant-modal-body .ant-table-cell {
  padding: 8px;
}

.arrival-confirmation .ant-modal-body .ant-form-item {
  margin-bottom: 0;
}

.arrival-confirmation .ant-table-summary {
  font-weight: bold;
}

.arrival-confirmation .difference-negative {
  color: #f5222d;
}

.arrival-confirmation .difference-positive {
  color: #52c41a;
}

/* 日常费用样式 */
.daily-expenses .ant-statistic-title {
  font-size: 14px;
}

.daily-expenses .ant-card {
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.daily-expenses .ant-table-wrapper {
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 费用统计卡片样式 */
.expense-stat-card {
  height: 100%;
}

/* 上传组件样式 */
.upload-list-inline .ant-upload-list-item {
  float: left;
  width: 200px;
  margin-right: 8px;
}

.upload-list-inline [class*='-upload-list-rtl'] .ant-upload-list-item {
  float: right;
}
