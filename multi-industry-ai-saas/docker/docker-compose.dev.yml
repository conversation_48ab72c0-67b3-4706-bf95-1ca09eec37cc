version: '3.3'

services:
  # 后端服务
  retail-ai-saas-backend:
    build:
      context: ../backend
      dockerfile: ${DOCKERFILE_BACKEND:-../docker/backend/Dockerfile}
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    container_name: retail-ai-saas-backend-dev
    restart: unless-stopped
    volumes:
      - ${BACKEND_VOLUME:-../backend:/app}
      - ../uploads:/app/uploads
      - /var/run/docker.sock:/var/run/docker.sock  # 挂载 Docker socket
    environment:
      - DEBUG=${DEBUG:-true}
      - POSTGRES_SERVER=retail-ai-saas-db
      - POSTGRES_USER=${POSTGRES_USER:-postgres}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-postgres}
      - POSTGRES_DB=${POSTGRES_DB:-retail_ai_saas}
      - POSTGRES_PORT=5432
      - REDIS_HOST=retail-ai-saas-redis
      - REDIS_PORT=6379
      - REDIS_DB=0
      - SECRET_KEY=${SECRET_KEY:-dev_secret_key_change_in_production}
      - ALLOWED_HOSTS=${ALLOWED_HOSTS:-*}
      - CORS_ORIGINS=http://localhost:3001,http://retail-ai-saas-frontend:3000,https://saas.houshanai.com,http://**************:3001,http://**************
      - STORAGE_LOCAL_PATH=/app/uploads
      - LOG_LEVEL=${LOG_LEVEL:-DEBUG}
      - LOGURU_LEVEL=DEBUG
      - UVICORN_LOG_LEVEL=debug
      - MCP_CONTAINER_INSTANCES_PATH=/app/mcp_servers
      - MCP_TARGET_API_BASE_URL=http://retail-ai-saas-backend:8000
      - DOCKER_ENV=true
      - TRAEFIK_MCP_DOMAIN_SUFFIX=saas.houshanai.com
    ports:
      - "${BACKEND_PORT:-8001}:8000"
    depends_on:
      - retail-ai-saas-db
      - retail-ai-saas-redis
    networks:
      - retail-ai-saas-network
    command: uvicorn main:app --host 0.0.0.0 --port 8000 --reload --log-level debug

  # 前端服务
  retail-ai-saas-frontend:
    build:
      context: ../frontend
      dockerfile: ${DOCKERFILE_FRONTEND:-../docker/frontend/Dockerfile}
    container_name: retail-ai-saas-frontend-dev
    restart: unless-stopped
    volumes:
      - ${FRONTEND_VOLUME:-../frontend:/app}
      - /app/node_modules
    environment:
      - NODE_ENV=${NODE_ENV:-development}
      - REACT_APP_API_URL=/api
      - CHOKIDAR_USEPOLLING=true
      - WDS_SOCKET_HOST=${WDS_SOCKET_HOST:-127.0.0.1}
      - WDS_SOCKET_PORT=${WDS_SOCKET_PORT:-3001}
      - DANGEROUSLY_DISABLE_HOST_CHECK=${DANGEROUSLY_DISABLE_HOST_CHECK:-true}
    ports:
      - "${FRONTEND_PORT:-3001}:3000"
    depends_on:
      - retail-ai-saas-backend
    networks:
      - retail-ai-saas-network
    command: ${FRONTEND_COMMAND:-npm start}

  # 数据库
  retail-ai-saas-db:
    image: pgvector/pgvector:pg14
    container_name: retail-ai-saas-db-dev
    restart: unless-stopped
    volumes:
      - postgres_data:/var/lib/postgresql/data
    environment:
      - POSTGRES_USER=${POSTGRES_USER:-postgres}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD:-postgres}
      - POSTGRES_DB=${POSTGRES_DB:-retail_ai_saas}
    ports:
      - "${DB_PORT:-5433}:5432"
    networks:
      - retail-ai-saas-network

  # Redis
  retail-ai-saas-redis:
    image: redis:7-alpine
    container_name: retail-ai-saas-redis-dev
    restart: unless-stopped
    volumes:
      - redis_data:/data
    ports:
      - "${REDIS_PORT:-6380}:6379"
    networks:
      - retail-ai-saas-network

  # Nginx
  retail-ai-saas-nginx:
    image: nginx:1.23-alpine
    container_name: retail-ai-saas-nginx-dev
    restart: unless-stopped
    volumes:
      - ../docker/nginx/nginx.conf:/etc/nginx/conf.d/default.conf
      - ../uploads:/var/www/uploads
    ports:
      - "${NGINX_PORT:-8080}:80"
    depends_on:
      - retail-ai-saas-backend
      - retail-ai-saas-frontend
    networks:
      - retail-ai-saas-network

networks:
  retail-ai-saas-network:
    driver: bridge

volumes:
  postgres_data:
  redis_data:
