#!/usr/bin/env python
# -*- coding: utf-8 -*-

from typing import List, Optional, Dict, Any
from pydantic import BaseModel, UUID4, Field
from datetime import datetime

# 品牌相关模型
class ProductBrandBase(BaseModel):
    """品牌基础模型"""
    name: str
    description: Optional[str] = None
    logo_url: Optional[str] = None

class ProductBrandCreate(ProductBrandBase):
    """创建品牌模型"""
    pass

class ProductBrandUpdate(ProductBrandBase):
    """更新品牌模型"""
    name: Optional[str] = None

class ProductBrandInDB(ProductBrandBase):
    """数据库品牌模型"""
    id: UUID4
    tenant_id: Optional[UUID4] = None
    project_id: UUID4
    created_at: datetime
    updated_at: datetime
    created_by: Optional[UUID4] = None
    updated_by: Optional[UUID4] = None

    class Config:
        model_config = {"from_attributes": True}

class ProductBrandResponse(BaseModel):
    """品牌响应模型"""
    success: bool
    message: Optional[str] = None
    data: Optional[ProductBrandInDB] = None

class ProductBrandListResponse(BaseModel):
    """品牌列表响应模型"""
    success: bool
    message: Optional[str] = None
    data: List[ProductBrandInDB]
    total: int
    page: Optional[int] = None
    page_size: Optional[int] = None
    pages: Optional[int] = None

# 分类相关模型
class ProductCategoryBase(BaseModel):
    """分类基础模型"""
    name: str
    parent_id: Optional[UUID4] = None
    description: Optional[str] = None

class ProductCategoryCreate(ProductCategoryBase):
    """创建分类模型"""
    pass

class ProductCategoryUpdate(ProductCategoryBase):
    """更新分类模型"""
    name: Optional[str] = None

class ProductCategoryInDB(ProductCategoryBase):
    """数据库分类模型"""
    id: UUID4
    tenant_id: Optional[UUID4] = None
    project_id: UUID4
    created_at: datetime
    updated_at: datetime
    created_by: Optional[UUID4] = None
    updated_by: Optional[UUID4] = None
    parent_name: Optional[str] = None  # 非数据库字段，用于前端展示

    class Config:
        model_config = {"from_attributes": True}

class ProductCategoryResponse(BaseModel):
    """分类响应模型"""
    success: bool
    message: Optional[str] = None
    data: Optional[ProductCategoryInDB] = None

class ProductCategoryListResponse(BaseModel):
    """分类列表响应模型"""
    success: bool
    message: Optional[str] = None
    data: List[ProductCategoryInDB]
    total: int
    page: Optional[int] = None
    page_size: Optional[int] = None
    pages: Optional[int] = None

# 产品相关模型
class ProductBase(BaseModel):
    """产品基础模型"""
    name: str
    sku: str
    barcode: Optional[str] = None
    brand_id: Optional[UUID4] = None
    category_id: Optional[UUID4] = None
    description: Optional[str] = None
    specification: Optional[str] = None
    unit: Optional[str] = None  # 添加单位字段
    cost_price: Optional[float] = None
    retail_price: Optional[float] = None
    image_url: Optional[str] = None
    is_active: bool = True

class ProductCreate(ProductBase):
    """创建产品模型"""
    pass

class ProductUpdate(ProductBase):
    """更新产品模型"""
    name: Optional[str] = None
    sku: Optional[str] = None
    is_active: Optional[bool] = None

class ProductInDB(ProductBase):
    """数据库产品模型"""
    id: UUID4
    tenant_id: Optional[UUID4] = None
    project_id: UUID4
    # 新增的多规格字段
    product_group_id: Optional[str] = None  # 商品分组ID，用字符串类型
    is_variant: Optional[bool] = None  # 是否为变体商品
    display_name: Optional[str] = None  # 显示名称（包含规格）
    created_at: datetime
    updated_at: datetime
    created_by: Optional[UUID4] = None
    updated_by: Optional[UUID4] = None
    brand_name: Optional[str] = None  # 非数据库字段，用于前端展示
    category_name: Optional[str] = None  # 非数据库字段，用于前端展示

    class Config:
        model_config = {"from_attributes": True}

class ProductResponse(BaseModel):
    """产品响应模型"""
    success: bool
    message: Optional[str] = None
    data: Optional[ProductInDB] = None

class ProductListResponse(BaseModel):
    """产品列表响应模型"""
    success: bool
    message: Optional[str] = None
    data: List[ProductInDB]
    total: int
    page: Optional[int] = None
    page_size: Optional[int] = None
    pages: Optional[int] = None

# 库存相关模型
class InventoryItemBase(BaseModel):
    """库存项基础模型"""
    product_id: UUID4
    warehouse_id: UUID4
    quantity: int = 0
    min_quantity: int = 0
    max_quantity: Optional[int] = None

class InventoryItemCreate(InventoryItemBase):
    """创建库存项模型"""
    pass

class InventoryItemUpdate(InventoryItemBase):
    """更新库存项模型"""
    product_id: Optional[UUID4] = None
    warehouse_id: Optional[UUID4] = None
    quantity: Optional[int] = None
    min_quantity: Optional[int] = None
    max_quantity: Optional[int] = None

class InventoryItemInDB(InventoryItemBase):
    """数据库库存项模型"""
    id: UUID4
    tenant_id: Optional[UUID4] = None
    project_id: UUID4
    created_at: datetime
    updated_at: datetime
    created_by: Optional[UUID4] = None
    updated_by: Optional[UUID4] = None
    product_name: Optional[str] = None  # 非数据库字段，用于前端展示
    warehouse_name: Optional[str] = None  # 非数据库字段，用于前端展示

    class Config:
        model_config = {"from_attributes": True}

class InventoryItemResponse(BaseModel):
    """库存项响应模型"""
    success: bool
    message: Optional[str] = None
    data: Optional[InventoryItemInDB] = None

class InventoryItemListResponse(BaseModel):
    """库存项列表响应模型"""
    success: bool
    message: Optional[str] = None
    data: List[InventoryItemInDB]
    total: int
    page: Optional[int] = None
    page_size: Optional[int] = None
    pages: Optional[int] = None
