# SApi 插件 - 智能助手集成平台

## 1. 概述

SApi (Smart Assistant API) 插件是一个智能助手集成平台，允许将系统的 REST API 端点转换为 MCP (Model Context Protocol) 工具，使得各种 MCP 客户端（如 Claude、Cursor 等）可以直接调用系统功能。该插件基于 fastapi_mcp 项目，实现了 API 到 MCP 工具的自动映射和管理。

## 2. 功能特性

- **API 端点管理**：管理和配置系统的 API 端点，包括端点定义、权限控制和访问限制
- **MCP 服务器管理**：部署和管理 MCP 协议服务器，实现与各类大模型和应用的无缝连接
- **API 到 MCP 映射**：将 REST API 端点自动映射为 MCP 工具，支持参数和响应映射
- **令牌管理**：管理 API 访问令牌，控制 API 访问权限和使用限制
- **使用统计**：记录 API 使用情况，提供使用统计和分析
- **客户端配置**：提供 MCP 客户端配置指南，支持各种 MCP 客户端的连接

## 3. 架构设计

### 3.1 核心组件

1. **API 端点管理器**：管理系统 API 端点，包括端点定义、参数和响应模板
2. **MCP 服务器适配器**：管理 MCP 服务器的生命周期，包括启动、停止和状态监控
3. **API 到 MCP 映射器**：将 API 端点映射为 MCP 工具，处理参数和响应转换
4. **令牌管理器**：管理 API 访问令牌，处理令牌验证和权限控制
5. **使用记录器**：记录 API 使用情况，提供使用统计和分析

### 3.2 数据模型

1. **SAPIConfig**：插件配置，包括 API 密钥、模型名称等
2. **SAPIEndpoint**：API 端点定义，包括路径、方法、参数和响应模板
3. **MCPServer**：MCP 服务器配置，包括启动命令、环境变量等
4. **APIMCPToolMapping**：API 到 MCP 工具的映射，包括参数映射和响应映射
5. **SAPIToken**：API 访问令牌，包括权限和过期时间
6. **SAPIUsageLog**：API 使用记录，包括请求和响应数据

### 3.3 工作流程

1. **API 端点注册**：系统 API 端点注册到 SApi 插件
2. **MCP 服务器部署**：部署 MCP 服务器，配置环境变量和启动参数
3. **API 到 MCP 映射**：将 API 端点映射为 MCP 工具，配置参数和响应映射
4. **MCP 工具调用**：MCP 客户端调用 MCP 工具，SApi 插件将请求转发到对应的 API 端点
5. **响应处理**：API 端点返回响应，SApi 插件将响应转换为 MCP 工具响应格式

## 4. 实现细节

### 4.1 API 端点管理

- 支持手动添加和自动发现 API 端点
- 支持端点参数和响应模板定义
- 支持端点权限控制和访问限制

### 4.2 MCP 服务器管理

- 支持多种 MCP 服务器部署方式，包括本地部署和远程部署
- 支持 MCP 服务器生命周期管理，包括启动、停止和状态监控
- 支持 MCP 服务器配置管理，包括环境变量和启动参数

### 4.3 API 到 MCP 映射

- 支持自动映射和手动映射
- 支持参数映射和响应映射
- 支持映射验证和测试

### 4.4 令牌管理

- 支持JWT+OAuth2.1标准的令牌生成和验证
- 支持令牌权限控制和过期时间
- 支持服务器权限映射和默认权限
- 支持令牌使用统计和限制
- 支持令牌申请和审批流程

### 4.5 使用记录

- 记录 API 调用情况，包括请求和响应数据
- 提供使用统计和分析
- 支持使用限制和配额管理

## 5. 使用指南

### 5.1 安装和配置

1. 安装 SApi 插件
2. 配置 API 端点
3. 部署 MCP 服务器
4. 配置 API 到 MCP 映射

### 5.2 MCP 客户端配置

1. 配置 MCP 客户端连接参数
2. 获取 JWT 访问令牌
3. 测试 MCP 工具调用

### 5.3 OAuth2.1认证

1. 申请OAuth2.1客户端凭证
2. 使用授权码流程获取访问令牌
3. 使用刷新令牌更新访问令牌
4. 使用客户端凭证流程获取访问令牌

### 5.4 API 端点管理

1. 添加 API 端点
2. 配置端点参数和响应模板
3. 设置端点权限和访问限制

### 5.5 MCP 服务器管理

1. 部署 MCP 服务器
2. 配置服务器环境变量和启动参数
3. 管理服务器生命周期

### 5.6 API 到 MCP 映射管理

1. 创建 API 到 MCP 映射
2. 配置参数映射和响应映射
3. 测试映射效果

## 6. 开发指南

### 6.1 插件开发

1. 克隆 SApi 插件代码
2. 安装开发依赖
3. 修改插件代码
4. 测试插件功能

### 6.2 MCP 服务器开发

1. 了解 MCP 协议规范
2. 开发 MCP 服务器
3. 测试 MCP 服务器功能

### 6.3 API 端点开发

1. 了解 API 端点规范
2. 开发 API 端点
3. 测试 API 端点功能

## 7. 参考资源

- [fastapi_mcp](https://github.com/tadata-org/fastapi_mcp) - 将 FastAPI 端点转换为 MCP 工具的库
- [Model Context Protocol](https://github.com/anthropics/anthropic-cookbook/tree/main/model_context_protocol) - MCP 协议规范
- [FastAPI](https://fastapi.tiangolo.com/) - 用于构建 API 的现代 Python 框架
- [OAuth 2.1](https://oauth.net/2.1/) - OAuth 2.1 认证框架
- [JWT](https://jwt.io/) - JSON Web Token 标准
- [PyJWT](https://pyjwt.readthedocs.io/) - Python JWT 实现
