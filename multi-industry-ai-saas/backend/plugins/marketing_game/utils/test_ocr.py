#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
小票识别测试脚本
用于测试小票识别功能
"""

import os
import sys
import asyncio
import logging
from pathlib import Path

# 添加项目根目录到 Python 路径
sys.path.insert(0, str(Path(__file__).parent.parent.parent.parent))

from plugins.marketing_game.utils.receipt_recognition import recognize_receipt

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_receipt_recognition():
    """测试小票识别功能"""
    try:
        # 测试图片路径
        test_image_path = Path(__file__).parent / "test_receipt.jpg"
        
        # 如果测试图片不存在，打印错误信息
        if not test_image_path.exists():
            logger.error(f"测试图片不存在: {test_image_path}")
            logger.info("请将小票图片保存为 test_receipt.jpg 放在当前目录下")
            return
        
        # 读取测试图片
        with open(test_image_path, "rb") as f:
            image_data = f.read()
        
        # 调用小票识别功能
        logger.info("开始识别小票...")
        result = await recognize_receipt(image_data)
        
        # 打印识别结果
        logger.info(f"识别结果: {result}")
        
        # 检查识别结果
        if result["success"]:
            logger.info("小票识别成功!")
            logger.info(f"消费总金额: {result['total_amount']}")
            logger.info(f"会员ID: {result['member_id']}")
            logger.info(f"商店名称: {result['store_name']}")
            logger.info(f"交易时间: {result['transaction_time']}")
            logger.info(f"小票ID: {result['receipt_id']}")
            logger.info(f"商品列表: {result['items']}")
            logger.info(f"原始文本: {result['raw_text']}")
        else:
            logger.error(f"小票识别失败: {result['error_message']}")
    except Exception as e:
        logger.error(f"测试小票识别功能失败: {str(e)}")

if __name__ == "__main__":
    # 运行测试
    asyncio.run(test_receipt_recognition())
