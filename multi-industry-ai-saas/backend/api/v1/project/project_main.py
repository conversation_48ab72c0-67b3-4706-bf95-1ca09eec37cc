#!/usr/bin/env python
# -*- coding: utf-8 -*-

import logging
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, Path
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
import uuid
from datetime import datetime
from pydantic import BaseModel

from api.deps import get_db
from models.project import Project
from models.project_user import ProjectUser
from models.user import User
from core.auth import get_current_user
from core.tenant_context import get_tenant_context

# 配置日志
logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter()

"""
本文件仅用于全局/平台级项目查询（如获取当前项目信息等）。
涉及租户上下文（如项目创建、更新、删除等）的所有项目操作，
请统一放在 routers/tenant.py 中实现。
"""

# --- 以下模型仅供参考，实际业务可移至 schemas 层 ---
# class ProjectBase(BaseModel):
#     ...

class ProjectResponse(BaseModel):
    id: uuid.UUID
    tenant_id: uuid.UUID
    name: str
    description: Optional[str]
    status: str
    industry_type: Optional[str]
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True

class ProjectDetailResponse(BaseModel):
    id: uuid.UUID
    tenant_id: uuid.UUID
    industry_id: uuid.UUID
    created_by: uuid.UUID
    created_at: datetime
    updated_at: datetime
    industry_name: str
    creator_name: str

    model_config = {"from_attributes": True}

@router.get("", response_model=List[ProjectResponse])
async def get_projects(
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    tenant_id: Optional[uuid.UUID] = Query(None),
    status: Optional[str] = Query(None),
    industry_type: Optional[str] = Query(None),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    获取项目列表
    """
    try:
        # 获取租户ID
        if not tenant_id:
            tenant_context = get_tenant_context()
            if tenant_context and tenant_context.tenant_id:
                tenant_id = tenant_context.tenant_id
            elif current_user.tenant_id:
                tenant_id = current_user.tenant_id

        query = select(Project)
        if tenant_id:
            if not current_user.is_system_admin and not current_user.is_service_provider_admin and current_user.tenant_id != tenant_id:
                raise HTTPException(
                    status_code=403,
                    detail="没有权限查看此租户的项目",
                )
            query = query.where(Project.tenant_id == tenant_id)
        elif not current_user.is_system_admin and not current_user.is_service_provider_admin:
            if not current_user.tenant_id:
                return []
            query = query.where(Project.tenant_id == current_user.tenant_id)
        if status:
            query = query.where(Project.status == status)
        if industry_type:
            query = query.where(Project.industry_type == industry_type)
        query = query.offset(skip).limit(limit)
        result = await db.execute(query)
        projects = result.scalars().all()
        if not current_user.is_system_admin and not current_user.is_service_provider_admin and not current_user.is_tenant_admin:
            result = await db.execute(
                select(ProjectUser.project_id).where(ProjectUser.user_id == current_user.id)
            )
            allowed_project_ids = [row[0] for row in result]
            projects = [p for p in projects if p.id in allowed_project_ids]
        return projects
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取项目列表失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取项目列表失败: {str(e)}",
        )

@router.get("/", response_model=ProjectResponse)
async def get_project(
    project_id: uuid.UUID = Path(...),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    获取项目详情
    """
    try:
        result = await db.execute(select(Project).where(Project.id == project_id))
        db_project = result.scalars().first()
        if not db_project:
            raise HTTPException(
                status_code=404,
                detail="项目不存在",
            )
        if not current_user.is_system_admin and not current_user.is_service_provider_admin:
            if current_user.tenant_id != db_project.tenant_id:
                raise HTTPException(
                    status_code=403,
                    detail="没有权限访问此项目",
                )
            if not current_user.is_tenant_admin:
                result = await db.execute(
                    select(ProjectUser).where(
                        ProjectUser.project_id == project_id,
                        ProjectUser.user_id == current_user.id,
                    )
                )
                project_user = result.scalars().first()
                if not project_user:
                    raise HTTPException(
                        status_code=403,
                        detail="没有权限访问此项目",
                    )
        return db_project
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取项目详情失败: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"获取项目详情失败: {str(e)}",
        )