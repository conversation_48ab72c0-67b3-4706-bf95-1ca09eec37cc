#!/usr/bin/env python
# -*- coding: utf-8 -*-

import logging
import uuid
import json
from typing import Optional, List, Dict, Any
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.exc import SQLAlchemyError
from tenacity import retry, stop_after_attempt, wait_fixed, retry_if_exception_type

from db.database import engine, Base

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@retry(
    stop=stop_after_attempt(5),
    wait=wait_fixed(3),
    retry=retry_if_exception_type(SQLAlchemyError),
    before_sleep=lambda retry_state: logger.info(f"Retrying table creation, attempt {retry_state.attempt_number}...")
)
async def create_tables():
    """
    根据 Base 中注册的所有模型元数据创建数据库表 (异步)。
    """
    try:
        logger.info("Starting table creation process...")
        # 使用异步连接并通过 run_sync 执行同步元数据创建
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)
        logger.info("Successfully created/checked database tables.")
    except Exception as e:
        logger.error(f"An error occurred during table creation: {e}", exc_info=True)
        raise


async def create_tenant_schema(tenant_id: str) -> str:
    """
    创建租户Schema
    """
    # 生成Schema名称
    schema_name = f"tenant_{tenant_id.replace('-', '')}"
    
    try:
        async with engine.begin() as conn:
            # 创建Schema
            await conn.execute(text(f"CREATE SCHEMA IF NOT EXISTS {schema_name}"))
            
            # 设置搜索路径
            await conn.execute(text(f"SET search_path TO {schema_name}"))
            
            # 创建租户特定的表
            # 这里可以根据需要创建租户特定的表
            # 例如，可以创建一个租户配置表
            await conn.execute(text(f"""
            CREATE TABLE IF NOT EXISTS {schema_name}.tenant_settings (
                id UUID PRIMARY KEY,
                key VARCHAR(100) NOT NULL,
                value TEXT,
                created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
            )
            """))
            
            # 重置搜索路径
            await conn.execute(text("SET search_path TO public"))
        
        logger.info(f"租户Schema创建成功: {schema_name}")
        return schema_name
    except Exception as e:
        logger.error(f"创建租户Schema失败: {e}")
        raise


async def drop_tenant_schema(tenant_id: str) -> bool:
    """
    删除租户Schema
    """
    # 生成Schema名称
    schema_name = f"tenant_{tenant_id.replace('-', '')}"
    
    try:
        async with engine.begin() as conn:
            # 删除Schema
            await conn.execute(text(f"DROP SCHEMA IF EXISTS {schema_name} CASCADE"))
        
        logger.info(f"租户Schema删除成功: {schema_name}")
        return True
    except Exception as e:
        logger.error(f"删除租户Schema失败: {e}")
        raise


async def create_industry_tables(db: AsyncSession, industry_type: str, schema_name: str) -> bool:
    """
    创建行业特定的表
    """
    try:
        # 根据行业类型创建不同的表
        if industry_type == "retail":
            await create_retail_tables(db, schema_name)
        elif industry_type == "restaurant":
            await create_restaurant_tables(db, schema_name)
        elif industry_type == "hotel":
            await create_hotel_tables(db, schema_name)
        elif industry_type == "beauty":
            await create_beauty_tables(db, schema_name)
        elif industry_type == "education":
            await create_education_tables(db, schema_name)
        elif industry_type == "healthcare":
            await create_healthcare_tables(db, schema_name)
        else:
            logger.warning(f"未知的行业类型: {industry_type}")
            return False
        
        logger.info(f"行业表创建成功: {industry_type}, schema: {schema_name}")
        return True
    except Exception as e:
        logger.error(f"创建行业表失败: {industry_type}, schema: {schema_name}, error: {e}")
        raise


async def create_retail_tables(db: AsyncSession, schema_name: str) -> bool:
    """
    创建零售业特定的表
    """
    try:
        # 设置搜索路径
        await db.execute(text(f"SET search_path TO {schema_name}"))
        
        # 创建商品表
        await db.execute(text(f"""
        CREATE TABLE IF NOT EXISTS {schema_name}.products (
            id UUID PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            code VARCHAR(50) NOT NULL,
            category_id UUID,
            price DECIMAL(10, 2) NOT NULL,
            cost DECIMAL(10, 2),
            stock INTEGER DEFAULT 0,
            unit VARCHAR(20),
            description TEXT,
            status VARCHAR(20) DEFAULT 'active',
            created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
        )
        """))
        
        # 创建商品分类表
        await db.execute(text(f"""
        CREATE TABLE IF NOT EXISTS {schema_name}.product_categories (
            id UUID PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            parent_id UUID,
            level INTEGER DEFAULT 1,
            status VARCHAR(20) DEFAULT 'active',
            created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
        )
        """))
        
        # 创建门店表
        await db.execute(text(f"""
        CREATE TABLE IF NOT EXISTS {schema_name}.stores (
            id UUID PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            address TEXT,
            contact_person VARCHAR(50),
            contact_phone VARCHAR(20),
            status VARCHAR(20) DEFAULT 'active',
            created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
        )
        """))
        
        # 创建库存表
        await db.execute(text(f"""
        CREATE TABLE IF NOT EXISTS {schema_name}.inventory (
            id UUID PRIMARY KEY,
            product_id UUID NOT NULL,
            store_id UUID NOT NULL,
            quantity INTEGER DEFAULT 0,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            CONSTRAINT fk_inventory_product FOREIGN KEY (product_id) REFERENCES {schema_name}.products(id),
            CONSTRAINT fk_inventory_store FOREIGN KEY (store_id) REFERENCES {schema_name}.stores(id)
        )
        """))
        
        # 创建销售表
        await db.execute(text(f"""
        CREATE TABLE IF NOT EXISTS {schema_name}.sales (
            id UUID PRIMARY KEY,
            store_id UUID NOT NULL,
            total_amount DECIMAL(10, 2) NOT NULL,
            payment_method VARCHAR(20),
            sale_date DATE NOT NULL,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            CONSTRAINT fk_sales_store FOREIGN KEY (store_id) REFERENCES {schema_name}.stores(id)
        )
        """))
        
        # 创建销售明细表
        await db.execute(text(f"""
        CREATE TABLE IF NOT EXISTS {schema_name}.sale_items (
            id UUID PRIMARY KEY,
            sale_id UUID NOT NULL,
            product_id UUID NOT NULL,
            quantity INTEGER NOT NULL,
            price DECIMAL(10, 2) NOT NULL,
            amount DECIMAL(10, 2) NOT NULL,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            CONSTRAINT fk_sale_items_sale FOREIGN KEY (sale_id) REFERENCES {schema_name}.sales(id),
            CONSTRAINT fk_sale_items_product FOREIGN KEY (product_id) REFERENCES {schema_name}.products(id)
        )
        """))
        
        # 重置搜索路径
        await db.execute(text("SET search_path TO public"))
        await db.commit()
        
        logger.info(f"零售业表创建成功: {schema_name}")
        return True
    except Exception as e:
        await db.rollback()
        logger.error(f"创建零售业表失败: {schema_name}, error: {e}")
        raise


async def create_restaurant_tables(db: AsyncSession, schema_name: str) -> bool:
    """
    创建餐饮业特定的表
    """
    try:
        # 设置搜索路径
        await db.execute(text(f"SET search_path TO {schema_name}"))
        
        # 创建餐厅表
        await db.execute(text(f"""
        CREATE TABLE IF NOT EXISTS {schema_name}.restaurants (
            id UUID PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            address TEXT,
            contact_person VARCHAR(50),
            contact_phone VARCHAR(20),
            status VARCHAR(20) DEFAULT 'active',
            created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
        )
        """))
        
        # 创建菜品表
        await db.execute(text(f"""
        CREATE TABLE IF NOT EXISTS {schema_name}.menu_items (
            id UUID PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            category_id UUID,
            price DECIMAL(10, 2) NOT NULL,
            cost DECIMAL(10, 2),
            description TEXT,
            image_url VARCHAR(255),
            status VARCHAR(20) DEFAULT 'active',
            created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
        )
        """))
        
        # 创建菜品分类表
        await db.execute(text(f"""
        CREATE TABLE IF NOT EXISTS {schema_name}.menu_categories (
            id UUID PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            sort_order INTEGER DEFAULT 0,
            status VARCHAR(20) DEFAULT 'active',
            created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
        )
        """))
        
        # 创建订单表
        await db.execute(text(f"""
        CREATE TABLE IF NOT EXISTS {schema_name}.orders (
            id UUID PRIMARY KEY,
            restaurant_id UUID NOT NULL,
            table_number VARCHAR(20),
            customer_count INTEGER DEFAULT 1,
            total_amount DECIMAL(10, 2) NOT NULL,
            payment_method VARCHAR(20),
            status VARCHAR(20) DEFAULT 'pending',
            order_time TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            CONSTRAINT fk_orders_restaurant FOREIGN KEY (restaurant_id) REFERENCES {schema_name}.restaurants(id)
        )
        """))
        
        # 创建订单明细表
        await db.execute(text(f"""
        CREATE TABLE IF NOT EXISTS {schema_name}.order_items (
            id UUID PRIMARY KEY,
            order_id UUID NOT NULL,
            menu_item_id UUID NOT NULL,
            quantity INTEGER NOT NULL,
            price DECIMAL(10, 2) NOT NULL,
            amount DECIMAL(10, 2) NOT NULL,
            note TEXT,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            CONSTRAINT fk_order_items_order FOREIGN KEY (order_id) REFERENCES {schema_name}.orders(id),
            CONSTRAINT fk_order_items_menu_item FOREIGN KEY (menu_item_id) REFERENCES {schema_name}.menu_items(id)
        )
        """))
        
        # 重置搜索路径
        await db.execute(text("SET search_path TO public"))
        await db.commit()
        
        logger.info(f"餐饮业表创建成功: {schema_name}")
        return True
    except Exception as e:
        await db.rollback()
        logger.error(f"创建餐饮业表失败: {schema_name}, error: {e}")
        raise


async def create_hotel_tables(db: AsyncSession, schema_name: str) -> bool:
    """
    创建酒店业特定的表
    """
    try:
        # 设置搜索路径
        await db.execute(text(f"SET search_path TO {schema_name}"))
        
        # 创建酒店表
        await db.execute(text(f"""
        CREATE TABLE IF NOT EXISTS {schema_name}.hotels (
            id UUID PRIMARY KEY,
            name VARCHAR(100) NOT NULL,
            address TEXT,
            contact_person VARCHAR(50),
            contact_phone VARCHAR(20),
            star_rating INTEGER,
            status VARCHAR(20) DEFAULT 'active',
            created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
        )
        """))
        
        # 创建房间类型表
        await db.execute(text(f"""
        CREATE TABLE IF NOT EXISTS {schema_name}.room_types (
            id UUID PRIMARY KEY,
            hotel_id UUID NOT NULL,
            name VARCHAR(100) NOT NULL,
            price DECIMAL(10, 2) NOT NULL,
            capacity INTEGER DEFAULT 2,
            description TEXT,
            amenities JSONB,
            status VARCHAR(20) DEFAULT 'active',
            created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            CONSTRAINT fk_room_types_hotel FOREIGN KEY (hotel_id) REFERENCES {schema_name}.hotels(id)
        )
        """))
        
        # 创建房间表
        await db.execute(text(f"""
        CREATE TABLE IF NOT EXISTS {schema_name}.rooms (
            id UUID PRIMARY KEY,
            hotel_id UUID NOT NULL,
            room_type_id UUID NOT NULL,
            room_number VARCHAR(20) NOT NULL,
            floor VARCHAR(10),
            status VARCHAR(20) DEFAULT 'available',
            created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            CONSTRAINT fk_rooms_hotel FOREIGN KEY (hotel_id) REFERENCES {schema_name}.hotels(id),
            CONSTRAINT fk_rooms_room_type FOREIGN KEY (room_type_id) REFERENCES {schema_name}.room_types(id)
        )
        """))
        
        # 创建预订表
        await db.execute(text(f"""
        CREATE TABLE IF NOT EXISTS {schema_name}.bookings (
            id UUID PRIMARY KEY,
            hotel_id UUID NOT NULL,
            room_id UUID NOT NULL,
            guest_name VARCHAR(100) NOT NULL,
            guest_phone VARCHAR(20),
            guest_email VARCHAR(100),
            check_in_date DATE NOT NULL,
            check_out_date DATE NOT NULL,
            adults INTEGER DEFAULT 1,
            children INTEGER DEFAULT 0,
            total_amount DECIMAL(10, 2) NOT NULL,
            payment_status VARCHAR(20) DEFAULT 'pending',
            booking_status VARCHAR(20) DEFAULT 'confirmed',
            created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
            CONSTRAINT fk_bookings_hotel FOREIGN KEY (hotel_id) REFERENCES {schema_name}.hotels(id),
            CONSTRAINT fk_bookings_room FOREIGN KEY (room_id) REFERENCES {schema_name}.rooms(id)
        )
        """))
        
        # 重置搜索路径
        await db.execute(text("SET search_path TO public"))
        await db.commit()
        
        logger.info(f"酒店业表创建成功: {schema_name}")
        return True
    except Exception as e:
        await db.rollback()
        logger.error(f"创建酒店业表失败: {schema_name}, error: {e}")
        raise


async def create_beauty_tables(db: AsyncSession, schema_name: str) -> bool:
    """
    创建美容美发业特定的表
    """
    # 实现美容美发业特定的表创建逻辑
    # 为了简化，这里省略具体实现
    logger.info(f"美容美发业表创建成功: {schema_name}")
    return True


async def create_education_tables(db: AsyncSession, schema_name: str) -> bool:
    """
    创建教育培训业特定的表
    """
    # 实现教育培训业特定的表创建逻辑
    # 为了简化，这里省略具体实现
    logger.info(f"教育培训业表创建成功: {schema_name}")
    return True


async def create_healthcare_tables(db: AsyncSession, schema_name: str) -> bool:
    """
    创建医疗健康业特定的表
    """
    # 实现医疗健康业特定的表创建逻辑
    # 为了简化，这里省略具体实现
    logger.info(f"医疗健康业表创建成功: {schema_name}")
    return True


async def initialize_system_data(db: AsyncSession) -> bool:
    """
    初始化系统数据
    """
    try:
        # 初始化行业数据
        await initialize_industries(db)
        
        # 初始化系统角色
        await initialize_system_roles(db)
        
        # 初始化系统管理员
        await initialize_system_admin(db)
        
        # 初始化默认服务商
        await initialize_default_service_provider(db)
        
        # 初始化默认租户
        await initialize_default_tenant(db)
        
        logger.info("系统数据初始化成功")
        return True
    except Exception as e:
        logger.error(f"初始化系统数据失败: {e}")
        raise


async def initialize_industries(db: AsyncSession) -> bool:
    """
    初始化行业数据
    """
    try:
        # 检查是否已存在行业数据
        result = await db.execute(text("SELECT COUNT(*) FROM industries"))
        count = result.scalar()
        
        if count > 0:
            logger.info("行业数据已存在，跳过初始化")
            return True
        
        # 插入行业数据
        industries = [
            {
                "id": str(uuid.uuid4()),
                "code": "retail",
                "name": "零售业",
                "description": "包括商店、超市、便利店等零售业态",
                "icon": "shopping-cart",
                "is_active": True,
            },
            {
                "id": str(uuid.uuid4()),
                "code": "restaurant",
                "name": "餐饮业",
                "description": "包括餐厅、咖啡厅、酒吧等餐饮服务业态",
                "icon": "restaurant",
                "is_active": True,
            },
            {
                "id": str(uuid.uuid4()),
                "code": "hotel",
                "name": "酒店业",
                "description": "包括酒店、民宿等住宿服务业态",
                "icon": "hotel",
                "is_active": True,
            },
            {
                "id": str(uuid.uuid4()),
                "code": "beauty",
                "name": "美容美发业",
                "description": "包括美容院、美发店等美容美发服务业态",
                "icon": "scissors",
                "is_active": True,
            },
            {
                "id": str(uuid.uuid4()),
                "code": "education",
                "name": "教育培训业",
                "description": "包括培训机构、教育中心等教育培训业态",
                "icon": "book",
                "is_active": True,
            },
            {
                "id": str(uuid.uuid4()),
                "code": "healthcare",
                "name": "医疗健康业",
                "description": "包括诊所、健康中心等医疗健康服务业态",
                "icon": "heart",
                "is_active": True,
            },
        ]
        
        for industry in industries:
            await db.execute(
                text("""
                INSERT INTO industries (id, code, name, description, icon, is_active, created_at, updated_at)
                VALUES (:id, :code, :name, :description, :icon, :is_active, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                """),
                industry,
            )
        
        await db.commit()
        logger.info("行业数据初始化成功")
        return True
    except Exception as e:
        await db.rollback()
        logger.error(f"初始化行业数据失败: {e}")
        raise


async def initialize_system_roles(db: AsyncSession) -> bool:
    """
    初始化系统角色
    """
    try:
        # 检查是否已存在系统角色
        result = await db.execute(text("SELECT COUNT(*) FROM roles WHERE is_system_role = TRUE"))
        count = result.scalar()
        
        if count > 0:
            logger.info("系统角色已存在，跳过初始化")
            return True
        
        # 插入系统角色
        roles = [
            {
                "id": str(uuid.uuid4()),
                "name": "system_admin",
                "description": "系统管理员",
                "permissions": '{"*": true}',
                "is_system_role": True,
            },
            {
                "id": str(uuid.uuid4()),
                "name": "service_provider_admin",
                "description": "服务商管理员",
                "permissions": '{"service_provider_admin": true, "tenant_admin": true}',
                "is_system_role": True,
            },
            {
                "id": str(uuid.uuid4()),
                "name": "tenant_admin",
                "description": "租户管理员",
                "permissions": '{"tenant_admin": true}',
                "is_system_role": True,
            },
        ]
        
        for role in roles:
            await db.execute(
                text("""
                INSERT INTO roles (id, project_id, name, code, description, permissions, is_system_role, created_at, updated_at)
                VALUES (:id, NULL, :name, :name, :description, :permissions, :is_system_role, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                """),
                role,
            )
        
        await db.commit()
        logger.info("系统角色初始化成功")
        return True
    except Exception as e:
        await db.rollback()
        logger.error(f"初始化系统角色失败: {e}")
        raise


async def initialize_system_admin(db: AsyncSession) -> bool:
    """
    初始化系统管理员
    """
    try:
        # 检查是否已存在系统管理员
        result = await db.execute(text("SELECT COUNT(*) FROM users WHERE is_system_admin = TRUE"))
        count = result.scalar()
        
        if count > 0:
            logger.info("系统管理员已存在，跳过初始化")
            return True
        
        # 获取系统管理员角色ID
        result = await db.execute(text("SELECT id FROM roles WHERE name = 'system_admin'"))
        role_id = result.scalar()
        
        if not role_id:
            logger.error("系统管理员角色不存在")
            return False
        
        # 插入系统管理员
        admin = {
            "id": str(uuid.uuid4()),
            "username": "admin",
            "email": "<EMAIL>",
            "password_hash": User.get_password_hash("admin123"),
            "full_name": "系统管理员",
            "status": "active",
            "is_system_admin": True,
            "is_super_admin": True,
            "is_service_provider_admin": False,
            "is_tenant_admin": False,
            "role": "system_admin",
        }
        
        await db.execute(
            text("""
            INSERT INTO users (
                id, username, email, password_hash, full_name, status, 
                is_system_admin, is_super_admin, is_service_provider_admin, is_tenant_admin, 
                role, created_at, updated_at
            )
            VALUES (
                :id, :username, :email, :password_hash, :full_name, :status, 
                :is_system_admin, :is_super_admin, :is_service_provider_admin, :is_tenant_admin,
                :role, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
            )
            """),
            admin,
        )
        
        await db.commit()
        logger.info("系统管理员初始化成功")
        return True
    except Exception as e:
        await db.rollback()
        logger.error(f"初始化系统管理员失败: {e}")
        raise


async def initialize_default_service_provider(db: AsyncSession) -> bool:
    """
    初始化默认服务商
    """
    try:
        # 检查是否已存在默认服务商
        result = await db.execute(text("SELECT COUNT(*) FROM service_providers WHERE name = 'Default Service Provider'"))
        count = result.scalar()
        
        if count > 0:
            logger.info("默认服务商已存在，跳过初始化")
            return True
        
        # 获取系统管理员ID
        result = await db.execute(text("SELECT id FROM users WHERE is_system_admin = TRUE LIMIT 1"))
        admin_user_id = result.scalar()
        
        if not admin_user_id:
            logger.error("系统管理员不存在，无法创建默认服务商")
            return False
        
        # 插入默认服务商
        service_provider_id = str(uuid.uuid4())
        service_provider = {
            "id": service_provider_id,
            "name": "Default Service Provider",
            "company_name": "默认服务商公司",
            "contact_person": "系统管理员",
            "contact_email": "<EMAIL>",
            "contact_phone": "13800138000",
            "status": "active",
            "commission_rate": 15,
            "supported_industries": ["retail", "restaurant", "hotel", "beauty", "education", "healthcare"],
            "branding": {
                "logo": "",
                "primary_color": "#1890ff",
                "secondary_color": "#f0f2f5"
            }
        }
        
        await db.execute(
            text("""
            INSERT INTO service_providers (
                id, name, company_name, contact_person, contact_email, contact_phone,
                status, commission_rate, supported_industries, branding, created_at, updated_at
            )
            VALUES (
                :id, :name, :company_name, :contact_person, :contact_email, :contact_phone,
                :status, :commission_rate, :supported_industries, :branding, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
            )
            """),
            {
                **service_provider,
                "supported_industries": json.dumps(service_provider["supported_industries"]),
                "branding": json.dumps(service_provider["branding"])
            }
        )
        
        # 创建服务商管理员用户
        service_provider_admin_id = str(uuid.uuid4())
        service_provider_admin = {
            "id": service_provider_admin_id,
            "service_provider_id": service_provider_id,
            "username": "service_admin",
            "email": "<EMAIL>",
            "password_hash": User.get_password_hash("service123"),
            "full_name": "服务商管理员",
            "status": "active",
            "is_system_admin": False,
            "is_super_admin": False,
            "is_service_provider_admin": True,
            "is_tenant_admin": False,
            "role": "service_provider_admin",
        }
        
        await db.execute(
            text("""
            INSERT INTO users (
                id, service_provider_id, username, email, password_hash, full_name, status,
                is_system_admin, is_super_admin, is_service_provider_admin, is_tenant_admin,
                role, created_at, updated_at
            )
            VALUES (
                :id, :service_provider_id, :username, :email, :password_hash, :full_name, :status,
                :is_system_admin, :is_super_admin, :is_service_provider_admin, :is_tenant_admin,
                :role, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
            )
            """),
            service_provider_admin,
        )
        
        await db.commit()
        logger.info("默认服务商初始化成功")
        return True
    except Exception as e:
        await db.rollback()
        logger.error(f"初始化默认服务商失败: {e}")
        raise


async def initialize_default_tenant(db: AsyncSession) -> bool:
    """
    初始化默认租户
    """
    try:
        # 检查是否已存在默认租户
        result = await db.execute(text("SELECT COUNT(*) FROM tenants WHERE name = 'Default Tenant'"))
        count = result.scalar()
        
        if count > 0:
            logger.info("默认租户已存在，跳过初始化")
            return True
        
        # 获取默认服务商ID
        result = await db.execute(text("SELECT id FROM service_providers WHERE name = 'Default Service Provider' LIMIT 1"))
        service_provider_id = result.scalar()
        
        if not service_provider_id:
            logger.error("默认服务商不存在，无法创建默认租户")
            return False
        
        # 获取零售业行业ID（作为默认行业）
        result = await db.execute(text("SELECT code FROM industries WHERE code = 'retail' LIMIT 1"))
        industry_code = result.scalar()
        
        if not industry_code:
            logger.error("零售业行业不存在，无法创建默认租户")
            return False
        
        # 生成租户ID和Schema名称
        tenant_id = str(uuid.uuid4())
        schema_name = f"tenant_{tenant_id.replace('-', '')}"
        
        # 插入默认租户
        tenant = {
            "id": tenant_id,
            "service_provider_id": service_provider_id,
            "name": "Default Tenant",
            "industry_type": industry_code,
            "is_direct": False,
            "schema_name": schema_name,
            "status": "active",
            "max_projects": 5,
            "max_users": 50,
            "max_storage_gb": 10,
            "settings": {
                "timezone": "Asia/Shanghai",
                "language": "zh-CN",
                "currency": "CNY"
            }
        }
        
        await db.execute(
            text("""
            INSERT INTO tenants (
                id, service_provider_id, name, industry_type, is_direct, schema_name,
                status, max_projects, max_users, max_storage_gb, settings, created_at, updated_at
            )
            VALUES (
                :id, :service_provider_id, :name, :industry_type, :is_direct, :schema_name,
                :status, :max_projects, :max_users, :max_storage_gb, :settings, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
            )
            """),
            {
                **tenant,
                "settings": json.dumps(tenant["settings"])
            }
        )
        
        # 创建租户Schema
        await create_tenant_schema(tenant_id)
        
        # 创建行业特定的表
        await create_industry_tables(db, industry_code, schema_name)
        
        # 创建租户管理员用户
        tenant_admin_id = str(uuid.uuid4())
        tenant_admin = {
            "id": tenant_admin_id,
            "tenant_id": tenant_id,
            "service_provider_id": service_provider_id,
            "username": "tenant_admin",
            "email": "<EMAIL>",
            "password_hash": User.get_password_hash("tenant123"),
            "full_name": "租户管理员",
            "status": "active",
            "is_system_admin": False,
            "is_super_admin": False,
            "is_service_provider_admin": False,
            "is_tenant_admin": True,
            "role": "tenant_admin",
        }
        
        await db.execute(
            text("""
            INSERT INTO users (
                id, tenant_id, service_provider_id, username, email, password_hash, full_name, status,
                is_system_admin, is_super_admin, is_service_provider_admin, is_tenant_admin,
                role, created_at, updated_at
            )
            VALUES (
                :id, :tenant_id, :service_provider_id, :username, :email, :password_hash, :full_name, :status,
                :is_system_admin, :is_super_admin, :is_service_provider_admin, :is_tenant_admin,
                :role, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
            )
            """),
            tenant_admin,
        )
        
        # 创建默认项目
        project_id = str(uuid.uuid4())
        project_code = f"default_{tenant_id.replace('-', '')[:8]}"
        default_project = {
            "id": project_id,
            "tenant_id": tenant_id,
            "name": "默认项目",
            "code": project_code,
            "description": "系统自动创建的默认项目",
            "industry_type": industry_code,
            "status": "active",
            "max_users": 50,
            "max_storage_gb": 10,
            "plugins": "[]",
            "settings": "{}"
        }
        
        await db.execute(
            text("""
            INSERT INTO projects (
                id, tenant_id, name, code, description, industry_type, status,
                max_users, max_storage_gb, plugins, settings, created_at, updated_at
            )
            VALUES (
                :id, :tenant_id, :name, :code, :description, :industry_type, :status,
                :max_users, :max_storage_gb, :plugins, :settings, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
            )
            """),
            default_project,
        )
        
        # 创建项目管理员角色
        role_id = str(uuid.uuid4())
        project_admin_role = {
            "id": role_id,
            "project_id": project_id,
            "name": "项目管理员",
            "code": "project_admin",
            "description": "项目管理员，拥有项目的所有权限",
            "permissions": json.dumps({
                "project_view": True,
                "project_edit": True,
                "project_admin": True,
                "user_manage": True,
                "role_manage": True,
                "store_manage": True,
                "product_manage": True,
                "inventory_manage": True,
                "purchase_manage": True,
                "finance_manage": True,
                "report_view": True
            }),
            "is_system_role": True,
            "status": "active"
        }
        
        await db.execute(
            text("""
            INSERT INTO roles (
                id, project_id, name, code, description, permissions, is_system_role, status, created_at, updated_at
            )
            VALUES (
                :id, :project_id, :name, :code, :description, :permissions, :is_system_role, :status, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
            )
            """),
            project_admin_role,
        )
        
        # 将租户管理员关联到默认项目
        project_user_id = str(uuid.uuid4())
        project_user = {
            "id": project_user_id,
            "project_id": project_id,
            "user_id": tenant_admin_id,
            "role_id": role_id,
            "is_admin": True,
            "status": "active",
            "settings": "{}"
        }
        
        await db.execute(
            text("""
            INSERT INTO project_users (
                id, project_id, user_id, role_id, is_admin, status, settings, created_at, updated_at
            )
            VALUES (
                :id, :project_id, :user_id, :role_id, :is_admin, :status, :settings, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP
            )
            """),
            project_user,
        )
        
        await db.commit()
        logger.info("默认租户初始化成功")
        return True
    except Exception as e:
        await db.rollback()
        logger.error(f"初始化默认租户失败: {e}")
        raise


async def main():
    """
    主函数：运行完整的数据库初始化
    """
    try:
        logger.info("开始数据库初始化...")
        
        # 创建所有表
        logger.info("创建数据库表...")
        await create_tables()
        
        # 初始化系统数据
        logger.info("初始化系统数据...")
        from db.database import get_db
        async for db in get_db():
            await initialize_system_data(db)
            break
        
        logger.info("数据库初始化完成！")
        logger.info("=" * 50)
        logger.info("默认账户信息:")
        logger.info("系统管理员 - 用户名: admin, 密码: admin123")
        logger.info("服务商管理员 - 用户名: service_admin, 密码: service123")
        logger.info("租户管理员 - 用户名: tenant_admin, 密码: tenant123")
        logger.info("=" * 50)
        logger.info("⚠️  请在生产环境中立即修改默认密码！")
        
    except Exception as e:
        logger.error(f"数据库初始化失败: {e}")
        raise


if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
