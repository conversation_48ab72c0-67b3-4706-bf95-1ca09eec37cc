#!/bin/bash

# Traefik v2.10 到 v3.3 升级脚本
# 作者: AI Assistant
# 日期: $(date +%Y-%m-%d)

set -e

echo "🚀 开始 Traefik v2.10 到 v3.3 升级过程..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查是否在正确的目录
if [ ! -f "docker-compose.yml" ] || [ ! -f "traefik.yml" ]; then
    echo -e "${RED}❌ 错误: 请在traefik目录中运行此脚本${NC}"
    exit 1
fi

# 检查主项目是否运行
check_main_project() {
    echo -e "${BLUE}🔍 检查主项目状态...${NC}"
    
    # 检查主项目网络是否存在
    if ! docker network inspect retail-ai-saas-network >/dev/null 2>&1; then
        echo -e "${RED}❌ 错误: 主项目网络 retail-ai-saas-network 不存在${NC}"
        echo -e "${YELLOW}💡 请先启动主项目: cd ../docker && docker-compose up -d${NC}"
        exit 1
    fi
    
    # 检查主项目nginx是否运行
    if ! docker ps | grep -q "retail-ai-saas-nginx.*Up"; then
        echo -e "${RED}❌ 错误: 主项目nginx未运行${NC}"
        echo -e "${YELLOW}💡 请先启动主项目: cd ../docker && docker-compose up -d${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ 主项目运行正常${NC}"
}

# 备份当前配置
echo -e "${BLUE}📦 备份当前配置...${NC}"
BACKUP_DIR="backup-$(date +%Y%m%d-%H%M%S)"
mkdir -p "$BACKUP_DIR"
cp -r . "$BACKUP_DIR/"
echo -e "${GREEN}✅ 配置已备份到: $BACKUP_DIR${NC}"

# 检查主项目状态
check_main_project

# 检查当前Traefik状态
echo -e "${BLUE}🔍 检查当前Traefik状态...${NC}"
if docker-compose ps | grep -q "traefik.*Up"; then
    echo -e "${YELLOW}⚠️  检测到Traefik正在运行${NC}"
    TRAEFIK_RUNNING=true
else
    echo -e "${YELLOW}ℹ️  Traefik当前未运行${NC}"
    TRAEFIK_RUNNING=false
fi

# 验证配置文件
echo -e "${BLUE}🔧 验证新配置文件...${NC}"
if ! grep -q "defaultRuleSyntax: v2" traefik.yml; then
    echo -e "${RED}❌ 错误: traefik.yml中缺少向后兼容配置${NC}"
    exit 1
fi

if ! grep -q "traefik:v3.3" docker-compose.yml; then
    echo -e "${RED}❌ 错误: docker-compose.yml中未更新到v3.3版本${NC}"
    exit 1
fi

if ! grep -q "external: true" docker-compose.yml; then
    echo -e "${RED}❌ 错误: docker-compose.yml中未配置外部网络${NC}"
    exit 1
fi

if ! grep -q "retail-ai-saas-nginx:80" dynamic/services.yml; then
    echo -e "${RED}❌ 错误: 动态配置中未正确配置主项目nginx连接${NC}"
    exit 1
fi

echo -e "${GREEN}✅ 配置文件验证通过${NC}"

# 拉取新镜像
echo -e "${BLUE}📥 拉取Traefik v3.3镜像...${NC}"
docker pull traefik:v3.3

# 停止当前服务（如果在运行）
if [ "$TRAEFIK_RUNNING" = true ]; then
    echo -e "${BLUE}🛑 停止当前Traefik服务...${NC}"
    docker-compose down
    echo -e "${GREEN}✅ 服务已停止${NC}"
fi

# 启动新版本
echo -e "${BLUE}🚀 启动Traefik v3.3...${NC}"
docker-compose up -d

# 等待服务启动
echo -e "${BLUE}⏳ 等待服务启动...${NC}"
sleep 15

# 检查服务状态
echo -e "${BLUE}🔍 检查服务状态...${NC}"
if docker-compose ps | grep -q "traefik.*Up"; then
    echo -e "${GREEN}✅ Traefik v3.3 启动成功!${NC}"
else
    echo -e "${RED}❌ Traefik启动失败，正在回滚...${NC}"
    docker-compose down
    # 恢复备份
    cp "$BACKUP_DIR/docker-compose.yml" .
    cp "$BACKUP_DIR/traefik.yml" .
    cp -r "$BACKUP_DIR/dynamic/" .
    docker-compose up -d
    echo -e "${YELLOW}⚠️  已回滚到之前版本${NC}"
    exit 1
fi

# 健康检查
echo -e "${BLUE}🏥 执行健康检查...${NC}"
sleep 5

# 检查API端点
if curl -f -s http://localhost:8090/api/rawdata > /dev/null; then
    echo -e "${GREEN}✅ Traefik API端点响应正常${NC}"
else
    echo -e "${YELLOW}⚠️  Traefik API端点检查失败，但服务可能仍在启动中${NC}"
fi

# 检查路由配置
echo -e "${BLUE}🛣️  检查路由配置...${NC}"
if curl -f -s http://localhost:8090/api/http/routers > /dev/null; then
    echo -e "${GREEN}✅ 路由配置加载成功${NC}"
else
    echo -e "${YELLOW}⚠️  路由配置检查失败${NC}"
fi

# 检查与主项目的连接
echo -e "${BLUE}🔗 检查与主项目的连接...${NC}"
if docker exec traefik ping -c 1 retail-ai-saas-nginx >/dev/null 2>&1; then
    echo -e "${GREEN}✅ Traefik可以连接到主项目nginx${NC}"
else
    echo -e "${RED}❌ Traefik无法连接到主项目nginx${NC}"
    echo -e "${YELLOW}💡 请检查网络配置和主项目状态${NC}"
fi

# 测试主项目路由
echo -e "${BLUE}🧪 测试主项目路由...${NC}"
sleep 5
if curl -f -s http://localhost/health > /dev/null; then
    echo -e "${GREEN}✅ 主项目路由工作正常${NC}"
else
    echo -e "${YELLOW}⚠️  主项目路由测试失败，请手动检查${NC}"
fi

# 显示升级后信息
echo -e "${GREEN}🎉 升级完成!${NC}"
echo -e "${BLUE}📊 升级后信息:${NC}"
echo "- Traefik版本: v3.3"
echo "- 仪表板地址: http://localhost:8090"
echo "- 主项目访问: http://localhost"
echo "- 配置备份: $BACKUP_DIR"
echo "- 向后兼容: 已启用 (defaultRuleSyntax: v2)"

echo -e "${YELLOW}📝 后续步骤:${NC}"
echo "1. 访问 http://localhost:8090 检查仪表板"
echo "2. 访问 http://localhost 测试主项目"
echo "3. 测试所有路由是否正常工作"
echo "4. 监控日志: docker-compose logs -f traefik"
echo "5. 运行完整测试: ./test-setup.sh"

echo -e "${BLUE}🔧 可选优化:${NC}"
echo "- 启用OpenTelemetry观测性功能"
echo "- 逐步迁移到v3语法规则"
echo "- 配置HTTPS和证书"

echo -e "${GREEN}✨ 升级脚本执行完成!${NC}" 