#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
令牌申请表结构更新迁移脚本

更新令牌申请表结构，以支持JWT+OAuth2.1认证
"""

import logging
from sqlalchemy import text
from sqlalchemy.ext.asyncio import AsyncSession

# 配置日志
logger = logging.getLogger(__name__)

async def update_token_application_table(db: AsyncSession):
    """
    更新令牌申请表结构
    
    Args:
        db: 数据库会话
    """
    try:
        # 检查是否已存在新列
        check_columns_sql = """
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name = 'sapi_token_applications' 
        AND column_name IN ('server_permissions', 'default_permissions', 'token_type', 'project_id', 'expires_at');
        """
        
        result = await db.execute(text(check_columns_sql))
        existing_columns = [row[0] for row in result.fetchall()]
        
        # 需要添加的列
        columns_to_add = []
        
        # 检查server_permissions列
        if 'server_permissions' not in existing_columns:
            columns_to_add.append("ADD COLUMN server_permissions JSONB")
        
        # 检查default_permissions列
        if 'default_permissions' not in existing_columns:
            columns_to_add.append("ADD COLUMN default_permissions JSONB")
        
        # 检查token_type列
        if 'token_type' not in existing_columns:
            columns_to_add.append("ADD COLUMN token_type VARCHAR(20) DEFAULT 'mcp'")
        
        # 检查project_id列
        if 'project_id' not in existing_columns:
            columns_to_add.append("ADD COLUMN project_id UUID")
        
        # 检查expires_at列
        if 'expires_at' not in existing_columns:
            columns_to_add.append("ADD COLUMN expires_at TIMESTAMP")
        
        # 检查token列长度
        check_token_length_sql = """
        SELECT character_maximum_length 
        FROM information_schema.columns 
        WHERE table_name = 'sapi_token_applications' 
        AND column_name = 'token';
        """
        
        result = await db.execute(text(check_token_length_sql))
        token_length = result.scalar()
        
        # 如果token列长度小于1000，则修改
        if token_length and token_length < 1000:
            columns_to_add.append("ALTER COLUMN token TYPE VARCHAR(1000)")
        
        # 如果有需要添加的列，执行ALTER TABLE语句
        if columns_to_add:
            alter_table_sql = f"""
            ALTER TABLE sapi_token_applications
            {', '.join(columns_to_add)};
            """
            
            await db.execute(text(alter_table_sql))
            await db.commit()
            
            logger.info("令牌申请表结构更新成功")
        else:
            logger.info("令牌申请表结构已是最新，无需更新")
        
        return True
    except Exception as e:
        await db.rollback()
        logger.error(f"更新令牌申请表结构失败: {str(e)}")
        return False
