#!/usr/bin/env python
# -*- coding: utf-8 -*-

from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field, ConfigDict
from datetime import datetime
import uuid

class NotificationBase(BaseModel):
    """通知基础模型"""
    title: str
    content: str
    type: str = "system"  # system, task, inventory, order, security
    category: Optional[str] = None
    priority: str = "normal"  # normal, high
    content_data: Optional[Dict[str, Any]] = None
    target_url: Optional[str] = None

class NotificationCreate(NotificationBase):
    """创建通知模型"""
    user_id: uuid.UUID

class NotificationResponse(NotificationBase):
    """通知响应模型"""
    id: uuid.UUID
    user_id: uuid.UUID
    project_id: uuid.UUID
    is_read: bool
    created_at: datetime
    read_at: Optional[datetime] = None

    model_config = ConfigDict(from_attributes=True)

class NotificationList(BaseModel):
    """通知列表模型"""
    items: List[NotificationResponse]
    total: int
    page: int
    page_size: int
    pages: int

class NotificationTypeSettings(BaseModel):
    """通知类型设置模型"""
    system: bool = True
    task: bool = True
    inventory: bool = True
    order: bool = True
    security: bool = True

class NotificationSettings(BaseModel):
    """通知设置模型"""
    frequency: str = "realtime"  # realtime, hourly, daily
    email_notifications: bool = True
    push_notifications: bool = True
    notification_types: NotificationTypeSettings = Field(default_factory=NotificationTypeSettings)
