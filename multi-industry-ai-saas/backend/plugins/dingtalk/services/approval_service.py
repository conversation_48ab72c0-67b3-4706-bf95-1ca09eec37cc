"""
钉钉审批服务 - 核心审批功能和业务发现同步
基于钉钉开放平台 2025 最新 API
"""
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import and_, or_, desc, select, func
import asyncio
import uuid
from fastapi import HTTPException

from ..models import (
    DingTalkApprovalInstance,
    DingTalkApprovalTemplate,
    DingTalkApprovalOperation,
    DingTalkApprovalStatistics,
    DingTalkApprovalTask
)
from .base_service import DingTalkBaseService
from ..utils.dingtalk_api import DingTalkAPI

# 导入项目业务模型
from models.loss import Loss
from models.warehouse import WarehouseInbound

logger = logging.getLogger(__name__)

class ApprovalService(DingTalkBaseService):
    """钉钉审批服务 - 核心功能：模板管理、事件处理、业务发现同步"""
    
    def __init__(self, db: AsyncSession, tenant_id: str, project_id: str, dingtalk_api: DingTalkAPI):
        if isinstance(project_id, str):
            project_id = uuid.UUID(project_id)
        if isinstance(tenant_id, str):
            tenant_id = uuid.UUID(tenant_id)
        super().__init__(db, project_id)
        self.tenant_id = tenant_id
        self.dingtalk_api = dingtalk_api
    
    # ==================== 模板管理 ====================
    
    async def create_approval_template(self, template_data: Dict[str, Any]) -> Dict[str, Any]:
        """创建钉钉审批模板"""
        try:
            # 调用钉钉API创建模板
            response = await self.dingtalk_api.create_or_update_approval_template(template_data)
            
            if not response or not response.get('success'):
                error_msg = response.get('error', '未知错误') if response else '创建模板失败'
                raise HTTPException(status_code=400, detail=f"创建钉钉审批模板失败: {error_msg}")
                
            result_data = response.get('result', {})
            process_code = result_data.get('process_code')
            if not process_code:
                raise HTTPException(status_code=400, detail=f"未获取到审批模板编码")
            
            # 保存模板到本地数据库
            template = DingTalkApprovalTemplate(
                id=uuid.uuid4(),
                tenant_id=self.tenant_id,
                project_id=self.project_id,
                process_code=process_code,
                name=template_data['name'],
                description=template_data.get('description', ''),
                template_type=template_data.get('type', 'general'),
                form_schema=template_data,
                form_components=template_data.get('formComponents', []),
                status='ENABLE',
                created_at=datetime.utcnow()
            )
            
            self.db.add(template)
            await self.db.commit()
            await self.db.refresh(template)
            
            logger.info(f"✅ 成功创建审批模板: {template.name} (process_code: {process_code})")
            
            return {
                "success": True,
                "data": {
                    "template_id": str(template.id),
                    "process_code": process_code,
                    "template_name": template.name,
                    "template_type": template.template_type,
                    "status": template.status
                },
                "message": "审批模板创建成功"
            }
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"❌ 创建审批模板失败: {str(e)}")
            raise HTTPException(status_code=500, detail=f"创建审批模板失败: {str(e)}")
    
    async def update_approval_template(self, template_id: str, template_data: Dict[str, Any]) -> Dict[str, Any]:
        """更新钉钉审批模板"""
        try:
            # 查询现有模板
            stmt = select(DingTalkApprovalTemplate).where(
                DingTalkApprovalTemplate.id == uuid.UUID(template_id),
                DingTalkApprovalTemplate.project_id == self.project_id
            )
            result = await self.db.execute(stmt)
            template = result.scalar_one_or_none()
            
            if not template:
                raise HTTPException(status_code=404, detail="审批模板不存在")
            
            # 添加process_code到更新数据中
            template_data['process_code'] = template.process_code
            
            # 调用钉钉API更新模板
            response = await self.dingtalk_api.create_or_update_approval_template(template_data)
            
            if not response or not response.get('success'):
                error_msg = response.get('error', '未知错误') if response else '更新模板失败'
                raise HTTPException(status_code=400, detail=f"更新钉钉审批模板失败: {error_msg}")
            
            # 更新本地数据库
            template.name = template_data['name']
            template.template_type = template_data.get('type', template.template_type)
            template.description = template_data.get('description', template.description)
            template.form_schema = template_data
            template.form_components = template_data.get('formComponents', template.form_components)
            template.updated_at = datetime.utcnow()
            
            await self.db.commit()
            
            logger.info(f"✅ 成功更新审批模板: {template.name}")
            
            return {
                "success": True,
                "data": {
                    "template_id": str(template.id),
                    "process_code": template.process_code,
                    "template_name": template.name,
                    "template_type": template.template_type,
                    "status": template.status
                },
                "message": "审批模板更新成功"
            }
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"❌ 更新审批模板失败: {str(e)}")
            raise HTTPException(status_code=500, detail=f"更新审批模板失败: {str(e)}")
    
    async def get_templates(self) -> Dict[str, Any]:
        """获取审批模板列表"""
        try:
            stmt = select(DingTalkApprovalTemplate).where(
                DingTalkApprovalTemplate.project_id == self.project_id
            ).order_by(desc(DingTalkApprovalTemplate.created_at))
            
            result = await self.db.execute(stmt)
            templates = result.scalars().all()
            
            template_list = []
            for template in templates:
                # 确保form_components字段格式正确
                form_components = template.form_components or []
                
                # 如果form_components为空，尝试从form_schema获取
                if not form_components and template.form_schema:
                    form_components = template.form_schema.get('form_components', []) or template.form_schema.get('formComponents', [])
                
                # 确保每个组件都有必要的字段
                formatted_components = []
                for i, comp in enumerate(form_components):
                    if isinstance(comp, dict):
                        formatted_comp = {
                            "id": comp.get("id", f"comp_{i}"),
                            "componentType": comp.get("componentType", comp.get("type", "TextField")),
                            "type": comp.get("componentType", comp.get("type", "TextField")),
                            "props": comp.get("props", {})
                        }
                        
                        # 确保props有基本字段
                        if not formatted_comp["props"].get("componentId"):
                            formatted_comp["props"]["componentId"] = formatted_comp["id"]
                        if not formatted_comp["props"].get("label"):
                            formatted_comp["props"]["label"] = formatted_comp["componentType"]
                        
                        formatted_components.append(formatted_comp)
                
                template_list.append({
                    "id": str(template.id),
                    "process_code": template.process_code,
                    "name": template.name,
                    "description": template.description,
                    "template_type": template.template_type,
                    "status": template.status,
                    "form_components": formatted_components,  # 使用格式化后的组件
                    "created_at": template.created_at.isoformat() if template.created_at else None,
                    "updated_at": template.updated_at.isoformat() if template.updated_at else None
                })
            
            return {
                "success": True,
                "data": {
                    "templates": template_list,
                    "total": len(template_list)
                },
                "message": "获取模板列表成功"
            }
        except Exception as e:
            logger.error(f"❌ 获取模板列表失败: {str(e)}")
            raise HTTPException(status_code=500, detail=f"获取模板列表失败: {str(e)}")
    
    # ==================== 业务发现和自动同步 ====================
    
    async def discover_pending_approvals(self) -> Dict[str, Any]:
        """
        发现项目中需要审批的业务数据
        扫描各种业务模型中状态为 pending 的记录
        """
        try:
            logger.info("🔍 开始发现项目中待审批的业务数据...")
            
            discovered_items = []
            
            # 发现门店报损申请
            loss_items = await self._discover_loss_approvals()
            discovered_items.extend(loss_items)
            
            # 发现仓库入库申请
            inbound_items = await self._discover_inbound_approvals()
            discovered_items.extend(inbound_items)
            
            # 可以继续添加其他业务类型的发现...
            
            logger.info(f"🔍 发现了 {len(discovered_items)} 个待审批业务")
            
            return {
                "success": True,
                "data": {
                    "discovered_items": discovered_items,
                    "total": len(discovered_items)
                },
                "message": f"发现了 {len(discovered_items)} 个待审批业务"
            }
            
        except Exception as e:
            logger.error(f"❌ 发现待审批业务失败: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "data": {"discovered_items": [], "total": 0}
            }
    
    async def _discover_loss_approvals(self) -> List[Dict[str, Any]]:
        """发现门店报损待审批项目"""
        try:
            stmt = select(Loss).where(
                and_(
                    Loss.project_id == self.project_id,
                    Loss.status == 'pending'
                )
            ).order_by(desc(Loss.created_at))
            
            result = await self.db.execute(stmt)
            losses = result.scalars().all()
            
            discovered = []
            for loss in losses:
                discovered.append({
                    "business_type": "store_loss",
                    "business_id": str(loss.id),
                    "title": f"门店报损申请 - {loss.reason}",
                    "amount": loss.total_amount,
                    "created_at": loss.created_at.isoformat() if loss.created_at else None,
                    "details": {
                        "store_id": str(loss.store_id),
                        "product_id": str(loss.product_id),
                        "quantity": loss.quantity,
                        "unit_price": loss.unit_price,
                        "reason": loss.reason,
                        "notes": loss.notes
                    }
                })
            
            logger.info(f"🔍 发现 {len(discovered)} 个门店报损待审批")
            return discovered
            
        except Exception as e:
            logger.error(f"❌ 发现门店报损审批失败: {str(e)}")
            return []
    
    async def _discover_inbound_approvals(self) -> List[Dict[str, Any]]:
        """发现仓库入库待审批项目"""
        try:
            stmt = select(WarehouseInbound).where(
                and_(
                    WarehouseInbound.project_id == self.project_id,
                    WarehouseInbound.status == 'pending'
                )
            ).order_by(desc(WarehouseInbound.created_at))
            
            result = await self.db.execute(stmt)
            inbounds = result.scalars().all()
            
            discovered = []
            for inbound in inbounds:
                discovered.append({
                    "business_type": "warehouse_inbound",
                    "business_id": str(inbound.id),
                    "title": f"仓库入库申请",
                    "created_at": inbound.created_at.isoformat() if inbound.created_at else None,
                    "details": {
                        "warehouse_id": str(inbound.warehouse_id) if inbound.warehouse_id else None,
                        "supplier_name": getattr(inbound, 'supplier_name', None),
                        "notes": getattr(inbound, 'notes', None)
                    }
                })
            
            logger.info(f"🔍 发现 {len(discovered)} 个仓库入库待审批")
            return discovered
                
        except Exception as e:
            logger.error(f"❌ 发现仓库入库审批失败: {str(e)}")
            return []
    
    async def auto_sync_discovered_approvals(self, sync_options: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        自动同步发现的待审批业务到钉钉
        """
        try:
            logger.info("🔄 开始自动同步发现的审批到钉钉...")
            
            # 先发现待审批项目
            discovery_result = await self.discover_pending_approvals()
            if not discovery_result.get('success'):
                return discovery_result
            
            discovered_items = discovery_result['data']['discovered_items']
            
            # 批量同步到钉钉
            sync_results = []
            successful_syncs = 0
            
            for item in discovered_items:
                try:
                    sync_result = await self._sync_single_business_to_dingtalk(item, sync_options)
                    sync_results.append({
                        "business_type": item["business_type"],
                        "business_id": item["business_id"],
                        "title": item["title"],
                        "sync_success": sync_result.get("success", False),
                        "sync_message": sync_result.get("message", ""),
                        "dingtalk_instance_id": sync_result.get("dingtalk_instance_id")
                    })
                    
                    if sync_result.get("success"):
                        successful_syncs += 1
                        logger.info(f"✅ 成功同步: {item['title']}")
                    else:
                        logger.warning(f"⚠️ 同步失败: {item['title']} - {sync_result.get('message')}")
                        
                except Exception as e:
                    logger.error(f"❌ 同步单个业务失败: {item['title']} - {str(e)}")
                    sync_results.append({
                        "business_type": item["business_type"],
                        "business_id": item["business_id"],
                        "title": item["title"],
                        "sync_success": False,
                        "sync_message": f"同步异常: {str(e)}",
                        "dingtalk_instance_id": None
                    })
            
            logger.info(f"🔄 自动同步完成: {successful_syncs}/{len(discovered_items)} 成功")
            
            return {
                "success": True,
                "data": {
                    "sync_results": sync_results,
                    "total_discovered": len(discovered_items),
                    "successful_syncs": successful_syncs,
                    "failed_syncs": len(discovered_items) - successful_syncs
                },
                "message": f"自动同步完成，成功同步 {successful_syncs}/{len(discovered_items)} 个审批"
            }
                
        except Exception as e:
            logger.error(f"❌ 自动同步发现的审批失败: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "data": {
                    "sync_results": [],
                    "total_discovered": 0,
                    "successful_syncs": 0,
                    "failed_syncs": 0
                }
            }
    
    async def _sync_single_business_to_dingtalk(self, business_item: Dict[str, Any], sync_options: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """同步单个业务项目到钉钉审批"""
        try:
            business_type = business_item["business_type"]
            business_id = business_item["business_id"]
            
            # 检查是否已经同步过
            existing_instance = await self._get_existing_approval_instance(business_type, business_id)
            if existing_instance:
                return {
                    "success": False,
                    "message": "该业务已经同步过审批实例",
                    "dingtalk_instance_id": existing_instance.instance_id
                }
            
            # 获取或创建对应的审批模板
            template = await self._ensure_business_template(business_type)
            if not template:
                return {
                    "success": False,
                    "message": f"未找到或无法创建 {business_type} 类型的审批模板"
                }
            
            # 获取创建者用户ID
            creator_user_id = business_item.get("creator_user_id", uuid.uuid4())
            
            # 构建钉钉审批实例数据
            from ..utils.approval_sync import ApprovalSyncService
            sync_service = ApprovalSyncService(self.dingtalk_api, self.db)
            
            # 调用专门的同步方法
            sync_result = await sync_service.create_and_sync_approval(
                tenant_id=self.tenant_id,
                    project_id=self.project_id,
                business_type=business_type,
                business_data={**business_item["details"], "business_id": business_id},
                originator_user_id=creator_user_id,
                sync_options=sync_options
            )
            
            return sync_result
            
        except Exception as e:
            logger.error(f"❌ 同步单个业务到钉钉失败: {str(e)}")
            return {
                "success": False,
                "message": f"同步失败: {str(e)}"
            }
    
    async def _get_existing_approval_instance(self, business_type: str, business_id: str) -> Optional[DingTalkApprovalInstance]:
        """检查是否已存在审批实例"""
        try:
            stmt = select(DingTalkApprovalInstance).where(
                and_(
                    DingTalkApprovalInstance.project_id == self.project_id,
                    DingTalkApprovalInstance.business_type == business_type,
                    DingTalkApprovalInstance.business_id == business_id
                )
            )
            result = await self.db.execute(stmt)
            return result.scalar_one_or_none()
        except Exception:
            return None
    
    async def _ensure_business_template(self, business_type: str) -> Optional[DingTalkApprovalTemplate]:
        """确保业务类型有对应的审批模板"""
        try:
            # 先查找现有模板
            stmt = select(DingTalkApprovalTemplate).where(
                and_(
                    DingTalkApprovalTemplate.project_id == self.project_id,
                    DingTalkApprovalTemplate.template_type == business_type,
                    DingTalkApprovalTemplate.status == 'ENABLE'
                )
            )
            result = await self.db.execute(stmt)
            template = result.scalar_one_or_none()
            
            if template:
                return template
            
            # 如果没有，创建默认模板
            return await self._create_default_business_template(business_type)
            
        except Exception as e:
            logger.error(f"❌ 确保业务模板失败: {str(e)}")
            return None
    
    async def _create_default_business_template(self, business_type: str) -> Optional[DingTalkApprovalTemplate]:
        """为业务类型创建默认审批模板"""
        try:
            # 根据业务类型构建默认模板数据
            template_configs = {
                "store_loss": {
                    "name": "门店报损审批",
                    "description": "门店商品报损审批流程",
                    "type": "store_loss"
                },
                "warehouse_inbound": {
                    "name": "仓库入库审批",
                    "description": "仓库商品入库审批流程",
                    "type": "warehouse_inbound"
                }
            }
            
            config = template_configs.get(business_type)
            if not config:
                logger.warning(f"⚠️ 未找到业务类型 {business_type} 的默认模板配置")
                return None
            
            # 调用创建模板方法
            result = await self.create_approval_template(config)
            
            if result.get("success"):
                template_id = result["data"]["template_id"]
                stmt = select(DingTalkApprovalTemplate).where(
                    DingTalkApprovalTemplate.id == uuid.UUID(template_id)
                )
                result = await self.db.execute(stmt)
                return result.scalar_one_or_none()
            
            return None
            
        except Exception as e:
            logger.error(f"❌ 创建默认业务模板失败: {str(e)}")
            return None
    
    # ==================== 事件处理 ====================
    
    async def handle_approval_callback(self, callback_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理钉钉审批回调事件
        当钉钉侧审批状态变化时，同步回项目侧业务数据
        """
        try:
            logger.info(f"📥 收到钉钉审批回调: {callback_data}")
            
            event_type = callback_data.get('EventType')
            process_instance_id = callback_data.get('processInstanceId')
            
            if not process_instance_id:
                return {
                    "success": False,
                    "message": "回调数据中缺少 processInstanceId"
                }
            
            # 查找本地审批实例
            stmt = select(DingTalkApprovalInstance).where(
                DingTalkApprovalInstance.instance_id == process_instance_id
            )
            result = await self.db.execute(stmt)
            instance = result.scalar_one_or_none()
            
            if not instance:
                logger.warning(f"⚠️ 未找到对应的本地审批实例: {process_instance_id}")
                return {
                    "success": False,
                    "message": f"未找到审批实例: {process_instance_id}"
                }
            
            # 更新实例状态
            old_status = instance.status
            instance.status = callback_data.get('result', instance.status)
            instance.updated_at = datetime.utcnow()
            
            # 如果审批完成，更新完成时间
            if instance.status in ['COMPLETED', 'TERMINATED']:
                instance.approved_at = datetime.utcnow()
            
            await self.db.commit()
            
            # 触发业务逻辑处理
            await self._sync_approval_status_to_business(instance, callback_data)
            
            logger.info(f"✅ 审批回调处理成功: {process_instance_id} - {old_status} -> {instance.status}")
            
            return {
                "success": True,
                "data": {
                    "instance_id": str(instance.id),
                    "process_instance_id": process_instance_id,
                    "old_status": old_status,
                    "new_status": instance.status
                },
                "message": "审批回调处理成功"
            }
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"❌ 处理审批回调失败: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "message": "处理审批回调失败"
            }
    
    async def _sync_approval_status_to_business(self, instance: DingTalkApprovalInstance, callback_data: Dict[str, Any]):
        """将审批状态同步回业务数据"""
        try:
            business_type = instance.business_type
            business_id = instance.business_id
            
            if not business_type or not business_id:
                logger.warning("⚠️ 审批实例缺少业务类型或业务ID，无法同步状态")
                return
            
            logger.info(f"🔄 同步审批状态到业务数据: {business_type} - {business_id}")
            
            # 根据业务类型更新对应的业务数据
            if business_type == "store_loss":
                await self._update_loss_status(business_id, instance.status, callback_data)
            elif business_type == "warehouse_inbound":
                await self._update_inbound_status(business_id, instance.status, callback_data)
            
            logger.info(f"✅ 业务状态同步完成: {business_type} - {business_id}")
            
        except Exception as e:
            logger.error(f"❌ 同步审批状态到业务数据失败: {str(e)}")
    
    async def _update_loss_status(self, loss_id: str, approval_status: str, callback_data: Dict[str, Any]):
        """更新门店报损状态"""
        try:
            stmt = select(Loss).where(Loss.id == uuid.UUID(loss_id))
            result = await self.db.execute(stmt)
            loss = result.scalar_one_or_none()
            
            if not loss:
                logger.warning(f"⚠️ 未找到报损记录: {loss_id}")
                return
            
            # 状态映射
            status_mapping = {
                "COMPLETED": "approved",
                "TERMINATED": "rejected",
                "RUNNING": "pending",
                "NEW": "pending"
            }
            
            new_status = status_mapping.get(approval_status, "pending")
            
            if loss.status != new_status:
                loss.status = new_status
                
                if new_status in ["approved", "rejected"]:
                    loss.approved_at = datetime.utcnow()
                    # 这里可以从callback_data中获取审批人信息
                
                await self.db.commit()
                logger.info(f"✅ 更新报损状态成功: {loss_id} -> {new_status}")
            
        except Exception as e:
            logger.error(f"❌ 更新报损状态失败: {str(e)}")
    
    async def _update_inbound_status(self, inbound_id: str, approval_status: str, callback_data: Dict[str, Any]):
        """更新仓库入库状态"""
        try:
            stmt = select(WarehouseInbound).where(WarehouseInbound.id == uuid.UUID(inbound_id))
            result = await self.db.execute(stmt)
            inbound = result.scalar_one_or_none()
            
            if not inbound:
                logger.warning(f"⚠️ 未找到入库记录: {inbound_id}")
                return
            
            # 状态映射
            status_mapping = {
                "COMPLETED": "confirmed",
                "TERMINATED": "cancelled",
                "RUNNING": "pending",
                "NEW": "pending"
            }
            
            new_status = status_mapping.get(approval_status, "pending")
            
            if inbound.status != new_status:
                inbound.status = new_status
                await self.db.commit()
                logger.info(f"✅ 更新入库状态成功: {inbound_id} -> {new_status}")
            
        except Exception as e:
            logger.error(f"❌ 更新入库状态失败: {str(e)}")
    
    # ==================== 查询和统计 ====================
    
    async def get_instances(self, template_id: Optional[str] = None, status: Optional[str] = None) -> Dict[str, Any]:
        """获取审批实例列表"""
        try:
            conditions = [DingTalkApprovalInstance.project_id == self.project_id]
        
            if template_id:
                conditions.append(DingTalkApprovalInstance.template_id == uuid.UUID(template_id))
            if status:
                conditions.append(DingTalkApprovalInstance.status == status)
        
            stmt = select(DingTalkApprovalInstance).where(
                and_(*conditions)
            ).order_by(desc(DingTalkApprovalInstance.created_at))
        
            result = await self.db.execute(stmt)
            instances = result.scalars().all()
        
            return {
                "success": True,
                "data": {
                    "instances": [
                    {
                "id": str(instance.id),
                        "instance_id": instance.instance_id,
                        "process_code": instance.process_code,
                "title": instance.title,
                            "description": instance.description,
                "status": instance.status,
                            "business_type": instance.business_type,
                            "business_id": instance.business_id,
                            "created_at": instance.created_at.isoformat() if instance.created_at else None,
                            "approved_at": instance.approved_at.isoformat() if instance.approved_at else None
                    }
                    for instance in instances
                    ],
                    "total": len(instances)
                },
                "message": "获取实例列表成功"
            }
        except Exception as e:
            logger.error(f"❌ 获取实例列表失败: {str(e)}")
            raise HTTPException(status_code=500, detail=f"获取实例列表失败: {str(e)}")
    
    async def get_approval_statistics(self) -> Dict[str, Any]:
        """获取审批统计信息"""
        try:
            # 总实例数统计
            total_stmt = select(func.count(DingTalkApprovalInstance.id)).where(
                DingTalkApprovalInstance.project_id == self.project_id
            )
            total_result = await self.db.execute(total_stmt)
            total_instances = total_result.scalar() or 0
            
            # 按状态统计
            status_stmt = select(
                DingTalkApprovalInstance.status,
                func.count(DingTalkApprovalInstance.id)
            ).where(
                DingTalkApprovalInstance.project_id == self.project_id
            ).group_by(DingTalkApprovalInstance.status)
            
            status_result = await self.db.execute(status_stmt)
            status_stats = {status: count for status, count in status_result.fetchall()}
            
            # 按业务类型统计
            business_type_stmt = select(
                DingTalkApprovalInstance.business_type,
                func.count(DingTalkApprovalInstance.id)
            ).where(
                DingTalkApprovalInstance.project_id == self.project_id
            ).group_by(DingTalkApprovalInstance.business_type)
            
            business_type_result = await self.db.execute(business_type_stmt)
            business_type_stats = {btype: count for btype, count in business_type_result.fetchall()}
        
            return {
            "success": True,
            "data": {
                    "total_instances": total_instances,
                    "status_statistics": status_stats,
                    "business_type_statistics": business_type_stats,
                    "pending_instances": status_stats.get('NEW', 0) + status_stats.get('RUNNING', 0),
                    "completed_instances": status_stats.get('COMPLETED', 0),
                    "terminated_instances": status_stats.get('TERMINATED', 0)
                },
                "message": "获取统计信息成功"
            }
        except Exception as e:
            logger.error(f"❌ 获取审批统计失败: {str(e)}")
            raise HTTPException(status_code=500, detail=f"获取审批统计失败: {str(e)}")