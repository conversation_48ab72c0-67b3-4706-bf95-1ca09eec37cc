#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
租户相关的 Pydantic 模型
"""

from typing import Optional, List
from uuid import UUID
from pydantic import BaseModel, Field
from datetime import datetime


class TenantBase(BaseModel):
    """租户基础模型"""
    name: str = Field(..., description="租户名称")
    industry_type: str = Field(..., description="行业类型")
    is_direct: bool = Field(True, description="是否直接客户")
    schema_name: Optional[str] = Field(None, description="数据库 schema 名称")
    max_projects: int = Field(5, description="最大项目数")
    max_users: int = Field(10, description="最大用户数")
    max_storage_gb: int = Field(10, description="最大存储空间(GB)")
    status: str = Field("active", description="状态")


class TenantCreate(TenantBase):
    """创建租户模型"""
    service_provider_id: Optional[UUID] = Field(None, description="服务商ID")


class TenantUpdate(BaseModel):
    """更新租户模型"""
    name: Optional[str] = Field(None, description="租户名称")
    industry_type: Optional[str] = Field(None, description="行业类型")
    is_direct: Optional[bool] = Field(None, description="是否直接客户")
    max_projects: Optional[int] = Field(None, description="最大项目数")
    max_users: Optional[int] = Field(None, description="最大用户数")
    max_storage_gb: Optional[int] = Field(None, description="最大存储空间(GB)")
    status: Optional[str] = Field(None, description="状态")
    service_provider_id: Optional[UUID] = Field(None, description="服务商ID")


class TenantResponse(TenantBase):
    """租户响应模型"""
    id: UUID
    service_provider_id: Optional[UUID] = None
    created_at: datetime
    updated_at: Optional[datetime] = None
    current_users: Optional[int] = Field(0, description="当前用户数")
    current_projects: Optional[int] = Field(0, description="当前项目数")
    current_storage_gb: Optional[float] = Field(0, description="当前存储使用量(GB)")
    subscription_plan_name: Optional[str] = Field(None, description="订阅计划名称")

    class Config:
        model_config = {"from_attributes": True}


class TenantListResponse(BaseModel):
    """租户列表响应模型"""
    items: List[TenantResponse]
    total: int
