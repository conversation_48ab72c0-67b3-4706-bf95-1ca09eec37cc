#!/usr/bin/env python
# -*- coding: utf-8 -*-

import logging
from typing import Optional, Dict, Any
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp
from contextvars import ContextVar
from pydantic import BaseModel
import uuid
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

# 配置日志
logger = logging.getLogger(__name__)

class TenantContext(BaseModel):
    """
    租户上下文模型
    """
    tenant_id: Optional[uuid.UUID] = None
    service_provider_id: Optional[uuid.UUID] = None
    project_id: Optional[uuid.UUID] = None
    industry_type: Optional[str] = None
    schema_name: Optional[str] = None

# 创建租户上下文变量
tenant_context_var: ContextVar[Optional[TenantContext]] = ContextVar("tenant_context", default=None)


def get_tenant_context() -> Optional[TenantContext]:
    """
    获取当前请求的租户上下文
    """
    return tenant_context_var.get()


def set_tenant_context(context: TenantContext) -> None:
    """
    设置当前请求的租户上下文
    """
    tenant_context_var.set(context)


def clear_tenant_context() -> None:
    """
    清除当前请求的租户上下文
    """
    tenant_context_var.set(None)


async def get_tenant_info_from_db(db: AsyncSession, tenant_id: uuid.UUID) -> Optional[Dict[str, Any]]:
    """
    从数据库中获取租户信息
    """
    try:
        # 导入模型
        from models.tenant import Tenant

        # 查询租户
        result = await db.execute(select(Tenant).where(Tenant.id == tenant_id))
        tenant = result.scalars().first()

        if not tenant:
            return None

        return {
            "tenant_id": tenant.id,
            "service_provider_id": tenant.service_provider_id,
            "name": tenant.name,
            "industry_type": tenant.industry_type,
            "schema_name": tenant.schema_name,
            "status": tenant.status,
        }
    except Exception as e:
        logger.error(f"获取租户信息失败: {e}")
        return None


class TenantContextMiddleware(BaseHTTPMiddleware):
    """
    租户上下文中间件
    """
    def __init__(self, app: ASGIApp):
        super().__init__(app)

    async def dispatch(self, request: Request, call_next: callable) -> Response:
        # 清除上下文
        clear_tenant_context()

        # 从请求中提取租户信息
        tenant_id_str = self._extract_tenant_id(request)
        service_provider_id_str = self._extract_service_provider_id(request)
        project_id_str = self._extract_project_id(request)

        # 转换为UUID类型，添加错误处理
        tenant_id = None
        service_provider_id = None
        project_id = None

        if tenant_id_str:
            try:
                tenant_id = uuid.UUID(tenant_id_str)
            except ValueError:
                logger.warning(f"无效的租户ID格式: {tenant_id_str}")

        if service_provider_id_str:
            try:
                service_provider_id = uuid.UUID(service_provider_id_str)
            except ValueError:
                logger.warning(f"无效的服务商ID格式: {service_provider_id_str}")

        if project_id_str:
            try:
                project_id = uuid.UUID(project_id_str)
            except ValueError:
                logger.warning(f"无效的项目ID格式: {project_id_str}")

        # 如果有租户ID，则设置上下文
        if tenant_id:
            # 生成Schema名称
            tenant_id_str = str(tenant_id)
            schema_name = f"tenant_{tenant_id_str.replace('-', '')}"

            # 创建上下文
            context = TenantContext(
                tenant_id=tenant_id,
                service_provider_id=service_provider_id,
                project_id=project_id,
                schema_name=schema_name
            )

            # 设置上下文
            set_tenant_context(context)

            logger.debug(f"设置租户上下文: {context}")

            # 注意：在实际应用中，可以从数据库中获取租户信息，并更新上下文
            # 例如：
            # from db.database import get_db
            # db = await anext(get_db())
            # tenant_info = await get_tenant_info_from_db(db, tenant_id)
            # if tenant_info:
            #     context.industry_type = tenant_info["industry_type"]
            #     set_tenant_context(context)

        # 处理请求
        response = await call_next(request)

        # 清除上下文
        clear_tenant_context()

        return response

    def _extract_tenant_id(self, request: Request) -> Optional[str]:
        """
        从请求中提取租户ID
        """
        # 1. 从请求头中提取
        tenant_id = request.headers.get("X-Tenant-ID")
        if tenant_id:
            return tenant_id

        # 2. 从子域名中提取
        host = request.headers.get("host", "")
        if "." in host and not host.startswith("www."):
            subdomain = host.split(".")[0]
            # 排除MCP服务器子域名和其他特殊子域名
            if subdomain not in ["admin", "api"] and not subdomain.startswith("mcp-"):
                # 这里需要查询数据库，将子域名映射到租户ID
                # 为了简化，我们假设子域名就是租户ID
                return subdomain

        # 3. 从路径中提取
        path = request.url.path
        if "/api/v1/tenants/" in path:
            # 路径格式: /api/v1/tenants/{tenant_id}/...
            parts = path.split("/")
            tenant_index = parts.index("tenants")
            if len(parts) > tenant_index + 1:
                return parts[tenant_index + 1]

        # 4. 从查询参数中提取
        tenant_id = request.query_params.get("tenant_id")
        if tenant_id:
            return tenant_id

        return None

    def _extract_service_provider_id(self, request: Request) -> Optional[str]:
        """
        从请求中提取服务商ID
        """
        # 从请求头中提取
        return request.headers.get("X-Service-Provider-ID")

    def _extract_project_id(self, request: Request) -> Optional[str]:
        """
        从请求中提取项目ID
        """
        # 1. 从请求头中提取
        project_id = request.headers.get("X-Project-ID")
        if project_id:
            return project_id

        # 2. 从路径中提取
        path = request.url.path
        if "/api/v1/projects/" in path:
            # 路径格式: /api/v1/projects/{project_id}/...
            parts = path.split("/")
            project_index = parts.index("projects")
            if len(parts) > project_index + 1:
                return parts[project_index + 1]

        # 3. 从查询参数中提取
        project_id = request.query_params.get("project_id")
        if project_id:
            return project_id

        return None
