import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Statistic,
  Typography,
  Table,
  Button,
  Spin,
  Empty,
  DatePicker,
  Tabs,
  List,
  Tag,
  Space,
  Select,
  Tooltip,
  Divider
} from 'antd';
import {
  DollarOutlined,
  ShoppingCartOutlined,
  UserOutlined,
  ShopOutlined,
  RobotOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  SyncOutlined,
  <PERSON><PERSON>hartOutlined,
  Pie<PERSON>hartOutlined,
  AppstoreOutlined,
  BellOutlined,
  LikeOutlined,
  DislikeOutlined
} from '@ant-design/icons';
import { Line, Pie, Column } from '@ant-design/plots';
import dayjs from 'dayjs';
import apiService from '../../../services/api';
import { message } from 'antd';
import { useNavigate } from 'react-router-dom';

const { Title, Text, Paragraph } = Typography;
const { RangePicker } = DatePicker;
const { TabPane } = Tabs;
const { Option } = Select;

/**
 * 运营仪表盘组件
 * 展示销售、订单、客户等运营数据
 */
const OperationsDashboard = () => {
  const navigate = useNavigate();
  
  // 状态定义
  const [loading, setLoading] = useState(true);
  const [dashboardData, setDashboardData] = useState(null);
  const [dateRange, setDateRange] = useState([dayjs().subtract(6, 'days'), dayjs()]);
  const [aiLoading, setAiLoading] = useState(false);
  const [feedbackLoading, setFeedbackLoading] = useState(false);
  const [stores, setStores] = useState([]);
  const [selectedStore, setSelectedStore] = useState(null);

  // 初始化加载
  useEffect(() => {
    fetchStores();
    fetchDashboardData();
  }, [dateRange, selectedStore]);

  // 获取门店列表
  const fetchStores = async () => {
    try {
      const response = await apiService.project.store.getList();
      setStores(response.items || []);
    } catch (error) {
      console.error('获取门店列表失败:', error);
    }
  };

  // 获取仪表盘数据
  const fetchDashboardData = async () => {
    setLoading(true);
    try {
      const params = {
        start_date: dateRange[0].format('YYYY-MM-DD'),
        end_date: dateRange[1].format('YYYY-MM-DD'),
        store_id: selectedStore
      };

      const response = await apiService.project.dashboard.getOperationsDashboard(params);
      setDashboardData(response);
    } catch (error) {
      console.error('获取运营仪表盘数据失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 刷新AI推荐
  const refreshAIRecommendations = async () => {
    setAiLoading(true);
    try {
      const response = await apiService.project.dashboard.getAIRecommendations('operations');
      if (response && response.recommendations) {
        setDashboardData(prev => ({
          ...prev,
          ai_recommendations: response.recommendations
        }));
      }
    } catch (error) {
      console.error('获取AI推荐失败:', error);
    } finally {
      setAiLoading(false);
    }
  };

  // 处理AI推荐反馈
  const handleRecommendationFeedback = async (recommendationId, isHelpful) => {
    setFeedbackLoading(true);
    try {
      await apiService.project.dashboard.submitAIFeedback({
        recommendation_id: recommendationId,
        is_helpful: isHelpful,
        feedback_type: 'operations'
      });

      // 更新本地状态，标记反馈已提交
      setDashboardData(prev => ({
        ...prev,
        ai_recommendations: prev.ai_recommendations.map(item =>
          item.id === recommendationId
            ? { ...item, feedback_submitted: true, is_helpful: isHelpful }
            : item
        )
      }));

      message.success(isHelpful ? '感谢您的反馈！' : '感谢您的反馈，我们会继续改进');
    } catch (error) {
      console.error('提交AI推荐反馈失败:', error);
      message.error('提交反馈失败，请重试');
    } finally {
      setFeedbackLoading(false);
    }
  };

  // 处理日期范围变化
  const handleDateRangeChange = (dates) => {
    if (dates && dates.length === 2) {
      setDateRange(dates);
    }
  };

  // 处理门店选择变化
  const handleStoreChange = (value) => {
    setSelectedStore(value);
  };

  // 刷新数据
  const handleRefresh = () => {
    fetchDashboardData();
  };

  // 渲染销售趋势图表
  const renderSalesTrendChart = () => {
    if (!dashboardData || !dashboardData.sales_trend || dashboardData.sales_trend.length === 0) {
      return <Empty description="暂无销售趋势数据" />;
    }

    const config = {
      data: dashboardData.sales_trend,
      xField: 'date',
      yField: 'value',
      seriesField: 'type',
      smooth: true,
      animation: {
        appear: {
          animation: 'path-in',
          duration: 1000
        }
      },
      xAxis: {
        title: {
          text: '日期'
        }
      },
      yAxis: {
        title: {
          text: '销售额 (元)'
        }
      },
      tooltip: {
        formatter: (datum) => {
          return { name: '销售额', value: `¥${datum.value.toFixed(2)}` };
        }
      }
    };

    return <Line {...config} height={300} />;
  };

  // 渲染类别分布图表
  const renderCategoryDistributionChart = () => {
    if (!dashboardData || !dashboardData.category_distribution || dashboardData.category_distribution.length === 0) {
      return <Empty description="暂无类别分布数据" />;
    }

    const config = {
      data: dashboardData.category_distribution,
      angleField: 'value',
      colorField: 'name',
      radius: 0.8,
      label: {
        type: 'outer',
        content: '{name}: {percentage}%'
      },
      interactions: [{ type: 'element-active' }],
      tooltip: {
        formatter: (datum) => {
          return { name: datum.name, value: `¥${datum.value.toFixed(2)} (${datum.percentage.toFixed(1)}%)` };
        }
      }
    };

    return <Pie {...config} height={300} />;
  };

  // 渲染分时段销售图表
  const renderHourlySalesChart = () => {
    if (!dashboardData || !dashboardData.hourly_sales || Object.keys(dashboardData.hourly_sales).length === 0) {
      return <Empty description="暂无分时段销售数据" />;
    }

    const data = Object.entries(dashboardData.hourly_sales).map(([hour, value]) => ({
      hour,
      value
    }));

    const config = {
      data,
      xField: 'hour',
      yField: 'value',
      label: {
        position: 'middle',
        style: {
          fill: '#FFFFFF',
          opacity: 0.6,
        },
      },
      xAxis: {
        label: {
          autoHide: true,
          autoRotate: false,
        },
        title: {
          text: '时段'
        }
      },
      yAxis: {
        title: {
          text: '销售额 (元)'
        }
      },
      meta: {
        hour: {
          alias: '时段',
        },
        value: {
          alias: '销售额',
        },
      },
      tooltip: {
        formatter: (datum) => {
          return { name: `${datum.hour}时段`, value: `¥${datum.value.toFixed(2)}` };
        }
      }
    };

    return <Column {...config} height={300} />;
  };

  // 渲染AI推荐
  const renderAIRecommendations = () => {
    if (!dashboardData || !dashboardData.ai_recommendations || dashboardData.ai_recommendations.length === 0) {
      return <Empty description="暂无AI推荐" />;
    }

    return (
      <List
        dataSource={dashboardData.ai_recommendations}
        renderItem={(item) => (
          <List.Item>
            <div style={{ width: '100%' }}>
              <div style={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'flex-start',
                marginBottom: 12
              }}>
                <div>
                  <Text strong style={{ fontSize: 16, marginRight: 8 }}>{item.title}</Text>
                  <Tag color={
                    item.type === '业务优化' ? 'blue' :
                    item.type === '销售增长' ? 'green' :
                    item.type === '成本控制' ? 'orange' :
                    item.type === '风险预警' ? 'red' : 'default'
                  }>
                    {item.type}
                  </Tag>
                  {item.priority && (
                    <Tag color={
                      item.priority === '高' ? 'red' :
                      item.priority === '中' ? 'orange' : 'green'
                    }>
                      {item.priority}优先级
                    </Tag>
                  )}
                </div>
                <div>
                  {item.feedback_submitted ? (
                    <span>
                      {item.is_helpful ?
                        <Tag icon={<LikeOutlined />} color="success">已标记为有帮助</Tag> :
                        <Tag icon={<DislikeOutlined />} color="default">已提交反馈</Tag>
                      }
                    </span>
                  ) : (
                    <Space>
                      <Button
                        type="primary"
                        size="small"
                        icon={<LikeOutlined />}
                        onClick={() => handleRecommendationFeedback && handleRecommendationFeedback(item.id, true)}
                      >
                        有帮助
                      </Button>
                      <Button
                        size="small"
                        icon={<DislikeOutlined />}
                        onClick={() => handleRecommendationFeedback && handleRecommendationFeedback(item.id, false)}
                      >
                        没有帮助
                      </Button>
                    </Space>
                  )}
                </div>
              </div>

              <Paragraph style={{ margin: '12px 0' }}>{item.content}</Paragraph>

              {item.action_items && item.action_items.length > 0 && (
                <div style={{
                  background: '#f5f5f5',
                  padding: '12px 16px',
                  borderRadius: '4px',
                  marginBottom: 12
                }}>
                  <Text strong>建议行动:</Text>
                  <ul style={{ marginTop: 8, marginBottom: 0, paddingLeft: 20 }}>
                    {item.action_items.map((action, index) => (
                      <li key={index}>{action}</li>
                    ))}
                  </ul>
                </div>
              )}

              {item.detailed_analysis && (
                <div style={{ marginTop: 12 }}>
                  <Divider style={{ margin: '12px 0' }} />
                  <Text strong>详细分析:</Text>
                  <Paragraph style={{ margin: '8px 0 0' }}>{item.detailed_analysis}</Paragraph>
                </div>
              )}
            </div>
          </List.Item>
        )}
        style={{ background: '#fff' }}
      />
    );
  };

  // 渲染门店性能表格
  const renderStorePerformanceTable = () => {
    if (!dashboardData || !dashboardData.store_performance || dashboardData.store_performance.length === 0) {
      return <Empty description="暂无门店性能数据" />;
    }

    const columns = [
      {
        title: '门店名称',
        dataIndex: 'name',
        key: 'name',
      },
      {
        title: '销售额',
        dataIndex: 'sales',
        key: 'sales',
        render: (text) => `¥${text.toFixed(2)}`,
        sorter: (a, b) => a.sales - b.sales,
      },
      {
        title: '订单数',
        dataIndex: 'orders',
        key: 'orders',
        sorter: (a, b) => a.orders - b.orders,
      },
      {
        title: '客户数',
        dataIndex: 'customers',
        key: 'customers',
        sorter: (a, b) => a.customers - b.customers,
      }
    ];

    return (
      <Table
        columns={columns}
        dataSource={dashboardData.store_performance}
        rowKey="id"
        pagination={{ pageSize: 5 }}
      />
    );
  };

  // 渲染产品性能表格
  const renderProductPerformanceTable = () => {
    if (!dashboardData || !dashboardData.product_performance || dashboardData.product_performance.length === 0) {
      return <Empty description="暂无产品性能数据" />;
    }

    const columns = [
      {
        title: '产品名称',
        dataIndex: 'name',
        key: 'name',
      },
      {
        title: '销售额',
        dataIndex: 'sales',
        key: 'sales',
        render: (text) => `¥${text.toFixed(2)}`,
        sorter: (a, b) => a.sales - b.sales,
      },
      {
        title: '销售数量',
        dataIndex: 'quantity',
        key: 'quantity',
        sorter: (a, b) => a.quantity - b.quantity,
      }
    ];

    return (
      <Table
        columns={columns}
        dataSource={dashboardData.product_performance}
        rowKey="id"
        pagination={{ pageSize: 5 }}
      />
    );
  };

  return (
    <div className="operations-dashboard">
      <Card>
        <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Title level={4} style={{ margin: 0 }}>运营仪表盘</Title>
          <Space>
            <Select
              placeholder="选择门店"
              allowClear
              style={{ width: 200 }}
              onChange={handleStoreChange}
              value={selectedStore}
            >
              {stores.map(store => (
                <Option key={store.id} value={store.id}>{store.name}</Option>
              ))}
            </Select>
            <RangePicker
              value={dateRange}
              onChange={handleDateRangeChange}
              allowClear={false}
            />
            <Button
              icon={<SyncOutlined />}
              onClick={handleRefresh}
              loading={loading}
            >
              刷新数据
            </Button>
          </Space>
        </div>

        <Spin spinning={loading}>
          {dashboardData ? (
            <>
              {/* 统计卡片 */}
              <Row gutter={[16, 16]}>
                <Col xs={24} sm={12} md={6}>
                  <Card>
                    <Statistic
                      title="今日销售额"
                      value={dashboardData.today_sales.value}
                      precision={2}
                      prefix={<DollarOutlined />}
                      suffix="元"
                      valueStyle={{ color: '#3f8600' }}
                    />
                    <div className="statistic-footer">
                      {dashboardData.today_sales.is_increase ? (
                        <Text type="success">
                          <ArrowUpOutlined /> {Math.abs(dashboardData.today_sales.change).toFixed(1)}%
                        </Text>
                      ) : (
                        <Text type="danger">
                          <ArrowDownOutlined /> {Math.abs(dashboardData.today_sales.change).toFixed(1)}%
                        </Text>
                      )}
                      <Text type="secondary" style={{ marginLeft: 8 }}>与昨日相比</Text>
                    </div>
                  </Card>
                </Col>
                <Col xs={24} sm={12} md={6}>
                  <Card>
                    <Statistic
                      title="今日订单数"
                      value={dashboardData.today_orders.value}
                      prefix={<ShoppingCartOutlined />}
                      suffix="单"
                      valueStyle={{ color: '#1890ff' }}
                    />
                    <div className="statistic-footer">
                      {dashboardData.today_orders.is_increase ? (
                        <Text type="success">
                          <ArrowUpOutlined /> {Math.abs(dashboardData.today_orders.change).toFixed(1)}%
                        </Text>
                      ) : (
                        <Text type="danger">
                          <ArrowDownOutlined /> {Math.abs(dashboardData.today_orders.change).toFixed(1)}%
                        </Text>
                      )}
                      <Text type="secondary" style={{ marginLeft: 8 }}>与昨日相比</Text>
                    </div>
                  </Card>
                </Col>
                <Col xs={24} sm={12} md={6}>
                  <Card>
                    <Statistic
                      title="今日客户数"
                      value={dashboardData.today_customers.value}
                      prefix={<UserOutlined />}
                      suffix="人"
                      valueStyle={{ color: '#722ed1' }}
                    />
                    <div className="statistic-footer">
                      {dashboardData.today_customers.is_increase ? (
                        <Text type="success">
                          <ArrowUpOutlined /> {Math.abs(dashboardData.today_customers.change).toFixed(1)}%
                        </Text>
                      ) : (
                        <Text type="danger">
                          <ArrowDownOutlined /> {Math.abs(dashboardData.today_customers.change).toFixed(1)}%
                        </Text>
                      )}
                      <Text type="secondary" style={{ marginLeft: 8 }}>与昨日相比</Text>
                    </div>
                  </Card>
                </Col>
                <Col xs={24} sm={12} md={6}>
                  <Card>
                    <Statistic
                      title="今日客单价"
                      value={dashboardData.today_average_order.value}
                      precision={2}
                      prefix={<DollarOutlined />}
                      suffix="元"
                      valueStyle={{ color: '#fa8c16' }}
                    />
                    <div className="statistic-footer">
                      {dashboardData.today_average_order.is_increase ? (
                        <Text type="success">
                          <ArrowUpOutlined /> {Math.abs(dashboardData.today_average_order.change).toFixed(1)}%
                        </Text>
                      ) : (
                        <Text type="danger">
                          <ArrowDownOutlined /> {Math.abs(dashboardData.today_average_order.change).toFixed(1)}%
                        </Text>
                      )}
                      <Text type="secondary" style={{ marginLeft: 8 }}>与昨日相比</Text>
                    </div>
                  </Card>
                </Col>
              </Row>

              {/* 快捷入口 */}
              <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
                <Col xs={24} sm={8} md={6} lg={4}>
                  <Card
                    hoverable
                    onClick={() => navigate('/project/operations/orders')}
                    style={{ textAlign: 'center', cursor: 'pointer' }}
                  >
                    <ShoppingCartOutlined style={{ fontSize: 24, color: '#1890ff', marginBottom: 8 }} />
                    <div>订单管理</div>
                  </Card>
                </Col>
                <Col xs={24} sm={8} md={6} lg={4}>
                  <Card
                    hoverable
                    onClick={() => navigate('/project/operations/customers')}
                    style={{ textAlign: 'center', cursor: 'pointer' }}
                  >
                    <UserOutlined style={{ fontSize: 24, color: '#52c41a', marginBottom: 8 }} />
                    <div>客户管理</div>
                  </Card>
                </Col>
                <Col xs={24} sm={8} md={6} lg={4}>
                  <Card
                    hoverable
                    onClick={() => navigate('/project/operations/reports')}
                    style={{ textAlign: 'center', cursor: 'pointer' }}
                  >
                    <LineChartOutlined style={{ fontSize: 24, color: '#722ed1', marginBottom: 8 }} />
                    <div>运营报表</div>
                  </Card>
                </Col>
                <Col xs={24} sm={8} md={6} lg={4}>
                  <Card
                    hoverable
                    onClick={() => navigate('/project/ai/role-assistants?role=operation_manager')}
                    style={{ textAlign: 'center', cursor: 'pointer' }}
                  >
                    <RobotOutlined style={{ fontSize: 24, color: '#f5222d', marginBottom: 8 }} />
                    <div>AI助手</div>
                  </Card>
                </Col>
              </Row>

              {/* 图表区域 */}
              <Tabs defaultActiveKey="1" style={{ marginTop: 16 }}>
                <TabPane
                  tab={<span><LineChartOutlined /> 销售趋势</span>}
                  key="1"
                >
                  <Card>
                    {renderSalesTrendChart()}
                  </Card>
                </TabPane>
                <TabPane
                  tab={<span><PieChartOutlined /> 类别分布</span>}
                  key="2"
                >
                  <Card>
                    {renderCategoryDistributionChart()}
                  </Card>
                </TabPane>
                <TabPane
                  tab={<span><AppstoreOutlined /> 分时段销售</span>}
                  key="3"
                >
                  <Card>
                    {renderHourlySalesChart()}
                  </Card>
                </TabPane>
                <TabPane
                  tab={<span><ShopOutlined /> 门店性能</span>}
                  key="4"
                >
                  <Card>
                    {renderStorePerformanceTable()}
                  </Card>
                </TabPane>
                <TabPane
                  tab={<span><ShoppingCartOutlined /> 产品性能</span>}
                  key="5"
                >
                  <Card>
                    {renderProductPerformanceTable()}
                  </Card>
                </TabPane>
              </Tabs>

              {/* AI推荐 */}
              <Card
                title={
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <span><RobotOutlined /> AI推荐</span>
                    <Button
                      icon={<SyncOutlined />}
                      onClick={refreshAIRecommendations}
                      loading={aiLoading}
                      size="small"
                    >
                      刷新AI推荐
                    </Button>
                  </div>
                }
                style={{ marginTop: 16 }}
              >
                {renderAIRecommendations()}
              </Card>
            </>
          ) : (
            <Empty description="暂无数据" />
          )}
        </Spin>
      </Card>
    </div>
  );
};

export default OperationsDashboard;
