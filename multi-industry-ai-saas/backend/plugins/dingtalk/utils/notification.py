#!/usr/bin/env python
# -*- coding: utf-8 -*-

import logging
import json
import time
import hmac
import hashlib
import base64
import urllib.parse
import asyncio
import httpx
from datetime import datetime
from typing import Dict, List, Any, Optional
import uuid

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import and_, or_

from db.database import get_db
from ..models.models import DingTalkWebhook, DingTalkSettings, DingTalkNotificationLog, DingTalkUserMapping

# 初始化日志
logger = logging.getLogger(__name__)

async def send_notification_to_dingtalk(
    tenant_id: uuid.UUID,
    project_id: uuid.UUID,
    notification_type: str,
    title: str,
    content: str,
    target_users: List[str] = None,
    target_groups: List[str] = None,
    extra_data: Dict[str, Any] = None,
    webhook_ids: List[str] = None
) -> Dict[str, Any]:
    """
    发送通知到钉钉
    
    Args:
        tenant_id: 租户ID
        project_id: 项目ID
        notification_type: 通知类型
        title: 通知标题
        content: 通知内容
        target_users: 目标用户ID列表
        target_groups: 目标群组ID列表
        extra_data: 额外数据
        webhook_ids: 指定的Webhook ID列表
    
    Returns:
        Dict[str, Any]: 发送结果
    """
    result = {
        "success": False,
        "message": "",
        "data": {
            "sent_count": 0,
            "failed_count": 0,
            "logs": []
        }
    }
    
    if target_users is None:
        target_users = []
    
    if target_groups is None:
        target_groups = []
    
    if extra_data is None:
        extra_data = {}
    
    async for db in get_db():
        try:
            # 获取插件设置
            settings_query = select(DingTalkSettings).where(
                and_(
                    DingTalkSettings.tenant_id == tenant_id,
                    DingTalkSettings.project_id == project_id
                )
            )
            settings_result = await db.execute(settings_query)
            settings = settings_result.scalar_one_or_none()
            
            if not settings:
                result["message"] = "钉钉插件设置不存在"
                return result
            
            if not settings.enable_dingtalk:
                result["message"] = "钉钉通知已禁用"
                return result
            
            # 根据通知级别过滤
            if settings.notification_level == "high_only" and notification_type not in ["high", "urgent", "critical", "test"]:
                result["message"] = "根据通知级别设置，此通知不会发送"
                return result
            
            # 获取Webhook列表
            webhooks_query = select(DingTalkWebhook).where(
                and_(
                    DingTalkWebhook.tenant_id == tenant_id,
                    DingTalkWebhook.project_id == project_id,
                    DingTalkWebhook.enabled == True
                )
            )
            
            # 如果指定了Webhook ID列表，则只获取指定的Webhook
            if webhook_ids:
                webhook_uuids = [uuid.UUID(wid) for wid in webhook_ids if wid]
                webhooks_query = webhooks_query.where(DingTalkWebhook.id.in_(webhook_uuids))
            
            webhooks_result = await db.execute(webhooks_query)
            webhooks = webhooks_result.scalars().all()
            
            if not webhooks:
                result["message"] = "没有可用的钉钉Webhook"
                return result
            
            # 获取用户映射
            user_mappings = {}
            if target_users:
                user_ids = [uuid.UUID(uid) for uid in target_users if uid]
                if user_ids:
                    mappings_query = select(DingTalkUserMapping).where(
                        and_(
                            DingTalkUserMapping.tenant_id == tenant_id,
                            DingTalkUserMapping.project_id == project_id,
                            DingTalkUserMapping.user_id.in_(user_ids)
                        )
                    )
                    mappings_result = await db.execute(mappings_query)
                    mappings = mappings_result.scalars().all()
                    
                    for mapping in mappings:
                        user_mappings[str(mapping.user_id)] = mapping.dingtalk_user_id
            
            # 发送通知
            sent_count = 0
            failed_count = 0
            logs = []
            
            for webhook in webhooks:
                # 检查通知类型是否匹配
                if webhook.notification_types and "all" not in webhook.notification_types and notification_type not in webhook.notification_types:
                    continue
                
                # 检查目标用户是否匹配
                if webhook.target_users and not set(target_users).intersection(set(webhook.target_users)) and "all" not in webhook.target_users:
                    continue
                
                # 检查目标群组是否匹配
                if webhook.target_groups and not set(target_groups or []).intersection(set(webhook.target_groups)) and "all" not in webhook.target_groups:
                    continue
                
                # 准备消息内容
                message_template = webhook.message_template or settings.default_template or "### {{title}}\n\n{{content}}\n\n> 来自系统通知 - {{time}}"
                
                # 替换模板变量
                message = message_template.replace("{{title}}", title)
                message = message.replace("{{content}}", content)
                message = message.replace("{{time}}", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
                
                # 添加@用户
                at_mobiles = []
                at_dingtalk_ids = []
                
                for user_id in target_users:
                    dingtalk_user_id = user_mappings.get(user_id)
                    if dingtalk_user_id:
                        at_dingtalk_ids.append(dingtalk_user_id)
                
                # 构建消息
                msg = {
                    "msgtype": "markdown",
                    "markdown": {
                        "title": title,
                        "text": message
                    },
                    "at": {
                        "atMobiles": at_mobiles,
                        "atDingtalkIds": at_dingtalk_ids,
                        "isAtAll": False
                    }
                }
                
                # 发送消息
                retry_count = 0
                max_retries = settings.retry_count
                retry_interval = settings.retry_interval
                
                while retry_count <= max_retries:
                    try:
                        # 生成签名
                        timestamp = str(int(round(time.time() * 1000)))
                        secret = webhook.secret
                        
                        if secret:
                            string_to_sign = f"{timestamp}\n{secret}"
                            hmac_code = hmac.new(secret.encode(), string_to_sign.encode(), digestmod=hashlib.sha256).digest()
                            sign = urllib.parse.quote_plus(base64.b64encode(hmac_code).decode())
                            webhook_url = f"{webhook.webhook_url}&timestamp={timestamp}&sign={sign}"
                        else:
                            webhook_url = webhook.webhook_url
                        
                        # 发送请求
                        async with httpx.AsyncClient() as client:
                            async with session.post(webhook_url, json=msg) as response:
                                response_data = await response.json()
                                
                                # 创建日志
                                log = DingTalkNotificationLog(
                                    id=uuid.uuid4(),
                                    tenant_id=tenant_id,
                                    project_id=project_id,
                                    webhook_id=webhook.id,
                                    notification_type=notification_type,
                                    title=title,
                                    content=content,
                                    target_users=target_users,
                                    target_groups=target_groups,
                                    retry_count=retry_count,
                                    response_data=response_data
                                )
                                
                                if response_data.get("errcode") == 0:
                                    log.status = "success"
                                    sent_count += 1
                                    logs.append({
                                        "webhook_id": str(webhook.id),
                                        "webhook_name": webhook.name,
                                        "status": "success",
                                        "retry_count": retry_count
                                    })
                                    break
                                else:
                                    log.status = "failed"
                                    log.error_message = response_data.get("errmsg", "未知错误")
                                    
                                    if retry_count < max_retries:
                                        retry_count += 1
                                        await asyncio.sleep(retry_interval)
                                    else:
                                        failed_count += 1
                                        logs.append({
                                            "webhook_id": str(webhook.id),
                                            "webhook_name": webhook.name,
                                            "status": "failed",
                                            "error": log.error_message,
                                            "retry_count": retry_count
                                        })
                                
                                db.add(log)
                    
                    except Exception as e:
                        logger.error(f"发送钉钉通知失败: {str(e)}")
                        
                        # 创建日志
                        log = DingTalkNotificationLog(
                            id=uuid.uuid4(),
                            tenant_id=tenant_id,
                            project_id=project_id,
                            webhook_id=webhook.id,
                            notification_type=notification_type,
                            title=title,
                            content=content,
                            target_users=target_users,
                            target_groups=target_groups,
                            status="failed",
                            error_message=str(e),
                            retry_count=retry_count
                        )
                        db.add(log)
                        
                        if retry_count < max_retries:
                            retry_count += 1
                            await asyncio.sleep(retry_interval)
                        else:
                            failed_count += 1
                            logs.append({
                                "webhook_id": str(webhook.id),
                                "webhook_name": webhook.name,
                                "status": "failed",
                                "error": str(e),
                                "retry_count": retry_count
                            })
            
            # 提交事务
            await db.commit()
            
            # 设置结果
            result["success"] = sent_count > 0
            result["message"] = f"发送结果: {sent_count} 成功, {failed_count} 失败"
            result["data"]["sent_count"] = sent_count
            result["data"]["failed_count"] = failed_count
            result["data"]["logs"] = logs
            
            return result
        
        except Exception as e:
            logger.error(f"发送钉钉通知失败: {str(e)}")
            await db.rollback()
            result["message"] = f"发送钉钉通知失败: {str(e)}"
            return result
        finally:
            await db.close()
