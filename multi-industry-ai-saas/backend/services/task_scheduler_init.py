#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
任务调度初始化模块
用于在应用启动时初始化任务调度器
"""

import logging
from typing import Callable, Awaitable
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import and_

from models.routine_task import RoutineTask
from .task_scheduler import TaskScheduler

# 配置日志
logger = logging.getLogger(__name__)

async def initialize_task_scheduler(db_factory: Callable[[], Awaitable[AsyncSession]]):
    """
    初始化任务调度器
    
    Args:
        db_factory: 数据库会话工厂函数
    """
    try:
        logger.info("正在初始化任务调度器...")
        
        # 获取任务调度器实例
        scheduler = TaskScheduler.get_instance()
        
        # 启动任务调度器
        await scheduler.start_scheduler(db_factory)
        
        # 加载现有任务
        await load_existing_tasks(db_factory, scheduler)
        
        logger.info("任务调度器初始化完成")
        
    except Exception as e:
        logger.error(f"初始化任务调度器失败: {str(e)}")
        # 不抛出异常，允许应用程序继续启动
        # 但记录错误，以便后续处理

async def load_existing_tasks(
    db_factory: Callable[[], Awaitable[AsyncSession]], 
    scheduler: TaskScheduler
):
    """
    加载现有任务
    
    Args:
        db_factory: 数据库会话工厂函数
        scheduler: 任务调度器实例
    """
    try:
        logger.info("正在加载现有任务...")
        
        async with db_factory() as db:
            # 查询所有未完成的任务
            query = select(RoutineTask).where(
                and_(
                    RoutineTask.status.in_(["pending", "in_progress"]),
                )
            )
            result = await db.execute(query)
            tasks = result.scalars().all()
            
            logger.info(f"找到 {len(tasks)} 个未完成任务")
            
            # 调度所有任务
            for task in tasks:
                await scheduler.schedule_task(db, task)
                
            # 查询所有周期性任务
            query = select(RoutineTask).where(
                and_(
                    RoutineTask.is_recurring == True,
                    RoutineTask.status.in_(["pending", "in_progress", "completed"])
                )
            )
            result = await db.execute(query)
            recurring_tasks = result.scalars().all()
            
            logger.info(f"找到 {len(recurring_tasks)} 个周期性任务")
            
            # 调度所有周期性任务
            for task in recurring_tasks:
                if task.status == "completed":
                    # 对于已完成的周期性任务，创建下一个周期的任务
                    await scheduler._handle_recurring_task(db, task.id)
                else:
                    # 对于未完成的周期性任务，直接调度
                    await scheduler.schedule_task(db, task)
        
        logger.info("现有任务加载完成")
        
    except Exception as e:
        logger.error(f"加载现有任务失败: {str(e)}")
        # 不抛出异常，允许应用程序继续启动
        # 但记录错误，以便后续处理
