from fastapi import APIRouter, Depends, HTTPException, status, Form, Query, Path, Request
from fastapi.responses import FileResponse, JSONResponse
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Optional
import uuid
import logging

from db.database import get_db
from models.operation_log import OperationLog
from services.storage_service import StorageService

# 配置日志
logger = logging.getLogger(__name__)

# 不需要认证的路由 - 移除前缀，因为在__init__.py中已经添加了
router = APIRouter(tags=["space_share"])

@router.get("/{share_token}")
async def get_shared_file(
    share_token: str,
    password: Optional[str] = None,
    db: AsyncSession = Depends(get_db)
):
    """
    获取分享文件信息
    """
    result = await StorageService.get_shared_file(
        db=db,
        share_token=share_token,
        password=password
    )
    return result

@router.post("/{share_token}/verify")
async def verify_share_password(
    share_token: str,
    password: str = Form(...),
    db: AsyncSession = Depends(get_db)
):
    """
    验证分享密码
    """
    result = await StorageService.get_shared_file(
        db=db,
        share_token=share_token,
        password=password
    )
    return result

@router.get("/{share_token}/download")
async def download_shared_file(
    share_token: str,
    password: Optional[str] = Query(None),
    db: AsyncSession = Depends(get_db),
    request: Request = None
):
    """
    下载分享文件 - 公开访问，不需要认证
    """
    try:
        logger.info(f"开始处理分享文件下载请求: share_token={share_token}")

        # 先验证分享
        share_result = await StorageService.get_shared_file(
            db=db,
            share_token=share_token,
            password=password
        )

        if not share_result["success"] or share_result["data"]["requires_password"]:
            logger.warning(f"分享文件访问被拒绝: share_token={share_token}, 原因: 需要密码或验证失败")
            return JSONResponse(
                status_code=status.HTTP_403_FORBIDDEN,
                content=share_result
            )

        # 获取文件信息
        file_id = uuid.UUID(share_result["data"]["file_id"])
        file_info = share_result["data"]["file_info"]

        logger.info(f"获取分享文件信息成功: file_id={file_id}")

        # 查询文件
        file_query = await StorageService.get_file(db, file_id, None)
        if not file_query:
            logger.error(f"分享文件不存在: file_id={file_id}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="文件不存在"
            )

        # 记录操作日志 - 匿名访问
        try:
            log = OperationLog(
                id=uuid.uuid4(),
                project_id=file_query.project_id,
                user_id=file_query.uploaded_by,  # 使用文件上传者作为记录者
                module="space_share",
                action="download",
                resource_type="shared_file",
                resource_id=file_id,
                description=f"分享文件下载: {file_query.original_name}",
                details={
                    "file_name": file_query.original_name,
                    "share_token": share_token,
                    "anonymous_access": True
                },
                ip_address=request.client.host if request and request.client else None,
                user_agent=request.headers.get("user-agent") if request else None
            )
            db.add(log)
            await db.commit()
        except Exception as log_error:
            logger.error(f"记录操作日志失败: {str(log_error)}")
            await db.rollback()
            # 继续处理下载，不因日志记录失败而中断

        logger.info(f"准备返回文件: path={file_query.path}, name={file_query.original_name}")

        # 返回文件 - 不需要认证
        return FileResponse(
            path=file_query.path,
            filename=file_query.original_name,
            media_type=file_query.mime_type
        )
    except Exception as e:
        logger.error(f"下载分享文件时发生错误: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"下载文件失败: {str(e)}"
        )

@router.get("/{share_token}/thumbnail")
async def get_shared_file_thumbnail(
    share_token: str,
    password: Optional[str] = Query(None),
    db: AsyncSession = Depends(get_db),
    request: Request = None
):
    """
    获取分享文件缩略图 - 公开访问，不需要认证
    """
    try:
        logger.info(f"开始处理分享文件缩略图请求: share_token={share_token}")

        # 先验证分享
        share_result = await StorageService.get_shared_file(
            db=db,
            share_token=share_token,
            password=password
        )

        if not share_result["success"] or share_result["data"]["requires_password"]:
            logger.warning(f"分享文件缩略图访问被拒绝: share_token={share_token}, 原因: 需要密码或验证失败")
            return JSONResponse(
                status_code=status.HTTP_403_FORBIDDEN,
                content=share_result
            )

        # 获取文件信息
        file_id = uuid.UUID(share_result["data"]["file_id"])
        logger.info(f"获取分享文件信息成功: file_id={file_id}")

        # 获取文件信息（用于记录日志）
        file_query = await StorageService.get_file(db, file_id, None)
        if not file_query:
            logger.error(f"分享文件不存在: file_id={file_id}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="文件不存在"
            )

        # 获取缩略图
        thumbnail_info = await StorageService.get_thumbnail(db, file_id, None)
        if not thumbnail_info:
            logger.error(f"分享文件缩略图不存在: file_id={file_id}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="缩略图不存在"
            )

        # 记录操作日志 - 匿名访问
        try:
            log = OperationLog(
                id=uuid.uuid4(),
                project_id=file_query.project_id,
                user_id=file_query.uploaded_by,  # 使用文件上传者作为记录者
                module="space_share",
                action="view",
                resource_type="shared_file_thumbnail",
                resource_id=file_id,
                description=f"查看分享文件缩略图: {file_query.original_name}",
                details={
                    "file_name": file_query.original_name,
                    "share_token": share_token,
                    "anonymous_access": True
                },
                ip_address=request.client.host if request and request.client else None,
                user_agent=request.headers.get("user-agent") if request else None
            )
            db.add(log)
            await db.commit()
        except Exception as log_error:
            logger.error(f"记录操作日志失败: {str(log_error)}")
            await db.rollback()
            # 继续处理缩略图，不因日志记录失败而中断

        logger.info(f"准备返回缩略图: path={thumbnail_info['file_path']}, name={thumbnail_info['file_name']}")

        # 返回缩略图 - 不需要认证
        return FileResponse(
            path=thumbnail_info["file_path"],
            filename=thumbnail_info["file_name"],
            media_type=thumbnail_info["mime_type"]
        )
    except Exception as e:
        logger.error(f"获取分享文件缩略图时发生错误: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取缩略图失败: {str(e)}"
        )
