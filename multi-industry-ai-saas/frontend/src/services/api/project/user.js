import api from '../httpClient';
import { getProjectId } from '../httpClient';

/**
 * 项目-用户API（业务层，仅业务相关接口）
 */
const user = {
  /**
   * 获取用户列表
   * @param {Object} params - 查询参数
   * @returns {Promise} - 用户列表
   */
  getList: async (params = {}) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.get(`/project/${projectId}/users`, { params });
  },

  /**
   * 获取用户详情
   * @param {string} id - 用户ID
   * @returns {Promise} - 用户详情
   */
  getDetail: async (id) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.get(`/project/${projectId}/users/${id}`);
  },

  /**
   * 创建用户
   * @param {Object} data - 用户数据
   * @returns {Promise} - 创建结果
   */
  create: async (data) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.post(`/project/${projectId}/users`, data);
  },

  /**
   * 更新用户
   * @param {string} id - 用户ID
   * @param {Object} data - 用户数据
   * @returns {Promise} - 更新结果
   */
  update: async (id, data) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.put(`/project/${projectId}/users/${id}`, data);
  },

  /**
   * 删除用户
   * @param {string} id - 用户ID
   * @returns {Promise} - 删除结果
   */
  delete: async (id) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.delete(`/project/${projectId}/users/${id}`);
  },

  /**
   * 获取当前用户信息
   * @returns {Promise} - 当前用户信息
   */
  getCurrentUser: async () => {
    return api.get('/auth/me');
  },

  /**
   * 获取用户角色
   * @param {string} id - 用户ID
   * @returns {Promise} - 用户角色
   */
  getRoles: async (id) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.get(`/project/${projectId}/users/${id}/roles`);
  },

  /**
   * 更新用户角色
   * @param {string} id - 用户ID
   * @param {Object} data - 角色数据
   * @returns {Promise} - 更新结果
   */
  updateRoles: async (id, data) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.put(`/project/${projectId}/users/${id}/roles`, data);
  },

  /**
   * 获取项目管理员列表
   * @param {Object} params - 查询参数
   * @returns {Promise} - 项目管理员列表
   */
  getAdmins: async (params = {}) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.get(`/project/${projectId}/admins`, { params });
  },

  /**
   * 创建项目管理员
   * @param {Object} data - 管理员数据
   * @returns {Promise} - 创建结果
   */
  createAdmin: async (data) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.post(`/project/${projectId}/admins`, data);
  },

  /**
   * 更新项目管理员
   * @param {string} id - 管理员ID
   * @param {Object} data - 管理员数据
   * @returns {Promise} - 更新结果
   */
  updateAdmin: async (id, data) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.put(`/project/${projectId}/admins/${id}`, data);
  },

  /**
   * 删除项目管理员
   * @param {string} id - 管理员ID
   * @returns {Promise} - 删除结果
   */
  deleteAdmin: async (id) => {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    return api.delete(`/project/${projectId}/admins/${id}`);
  }
};

export default user;
