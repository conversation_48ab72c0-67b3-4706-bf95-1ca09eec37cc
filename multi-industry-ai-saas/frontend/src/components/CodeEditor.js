import React from 'react';
import { Input } from 'antd';

/**
 * 简单的代码编辑器组件
 * 未来可以替换为更高级的编辑器，如 Monaco Editor 或 CodeMirror
 */
const CodeEditor = ({ value, onChange, language = 'javascript', height = '300px', ...props }) => {
  return (
    <Input.TextArea
      value={value}
      onChange={(e) => onChange && onChange(e.target.value)}
      style={{ 
        fontFamily: 'monospace', 
        fontSize: '14px',
        height,
        lineHeight: 1.5,
      }}
      {...props}
    />
  );
};

export default CodeEditor;
