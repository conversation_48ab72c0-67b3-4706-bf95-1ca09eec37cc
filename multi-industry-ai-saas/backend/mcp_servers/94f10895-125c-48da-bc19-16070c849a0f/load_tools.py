#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
工具加载器 - 从工具映射管理加载工具
"""

import os
import json
import logging
import asyncio
import httpx
from typing import Dict, Any, List, Optional, Union

# 设置日志记录器
logger = logging.getLogger(__name__)

async def load_tools(session=None, system_token=None) -> List[Dict[str, Any]]:
    """
    从工具映射管理加载工具列表
    
    Args:
        session: MCP会话对象，可选
        system_token: 系统令牌，优先使用，可选
        
    Returns:
        List[Dict[str, Any]]: 工具列表
    """
    try:
        # 获取API基础URL
        base_url = os.environ.get("API_BASE_URL", "http://localhost:8000")
        logger.info(f"load_tools: API_BASE_URL: {base_url}")
        
        # 优先使用传入的系统令牌
        token = system_token
        
        # 如果没有系统令牌，尝试从会话获取令牌
        if not token and session:
            token = session.system_token
            logger.info("load_tools: Using token from session.")
            
        # 如果还是没有令牌，尝试从环境变量获取
        if not token:
            env_token = os.environ.get("SYSTEM_TOKEN")
            if env_token:
                token = env_token
                logger.info("load_tools: Using token from SYSTEM_TOKEN environment variable.")
            
        # 如果都没有令牌，记录警告并返回空列表
        if not token:
            logger.warning("load_tools: 缺少认证令牌，无法从API获取工具列表")
            return []
        
        logger.debug(f"load_tools: Using token: {'*' * 5}{token[-5:] if len(token) > 5 else token}") # Log last 5 chars of token for verification
            
        # 获取服务器ID和项目ID
        server_id_from_env = os.environ.get("MCP_SERVER_ID", "MCP_SERVER_ID_NOT_SET")
        project_id_from_env = os.environ.get("PROJECT_ID", "PROJECT_ID_NOT_SET")
        
        server_id = session.server_id if session and hasattr(session, 'server_id') else server_id_from_env
        project_id = session.project_id if session and hasattr(session, 'project_id') else project_id_from_env
        
        logger.info(f"load_tools: MCP_SERVER_ID (env): {server_id_from_env}, PROJECT_ID (env): {project_id_from_env}")
        logger.info(f"load_tools: Effective server_id: {server_id}, project_id: {project_id}")

        if not project_id or project_id == "PROJECT_ID_NOT_SET":
            logger.warning("load_tools: project_id is not set. Will try to load global tools or extract from token.")
            # 尝试从系统令牌中提取项目ID
            if token:
                try:
                    import base64
                    parts = token.split('.')
                    if len(parts) == 3:
                        payload_part = parts[1]
                        missing_padding = len(payload_part) % 4
                        if missing_padding:
                            payload_part += '=' * (4 - missing_padding)
                        payload_bytes = base64.urlsafe_b64decode(payload_part)
                        payload = json.loads(payload_bytes.decode('utf-8'))
                        token_project_id = payload.get('project_id')
                        if token_project_id:
                            project_id = str(token_project_id)
                            logger.info(f"load_tools: 从JWT令牌中提取到项目ID: {project_id}")
                except Exception as e:
                    logger.warning(f"load_tools: 从JWT令牌中提取项目ID失败: {e}")
            
            # 如果仍然没有项目ID，尝试加载全局工具
            if not project_id or project_id == "PROJECT_ID_NOT_SET":
                logger.info("load_tools: 将尝试加载全局工具（不依赖项目ID）")
                # 可以返回一些全局工具或者空列表
                # return []  # 暂时返回空列表，后续可以添加全局工具
        if not server_id or server_id == "MCP_SERVER_ID_NOT_SET":
            # server_id 可能是可选的，如果API设计允许获取项目下所有工具而无需指定server_id
            # 但当前URL结构包含server_id，所以这里也认为它是必须的
            logger.warning("load_tools: server_id is not set. This might be an issue depending on API.")
            # 暂时不返回，看URL构造
        
        # 构建工具映射列表URL
        # 原URL: f"{base_url}/api/v1/project/{project_id}/plugin/sapi/servers/{server_id}/tools"
        # 根据之前的讨论，MCP工具加载应该使用项目级工具API，而不是特定服务器的工具API
        # 假设正确的项目工具API是 /api/v1/projects/{project_id}/tools/mcp (需要确认主后端是否有此API)
        # tools_url = f"{base_url}/api/v1/projects/{project_id}/tools/mcp"
        # **暂时保留原URL，因为这是当前代码使用的，先确认其行为**
        tools_url = f"{base_url}/api/v1/project/{project_id}/plugin/sapi/servers/{server_id}/tools"
        logger.info(f"load_tools: Attempting to fetch tools from URL: {tools_url}")
        
        # 发送请求
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {token}"
        }
        logger.debug(f"load_tools: Request headers: {headers}")
        
        async with httpx.AsyncClient(timeout=httpx.Timeout(10.0, connect=5.0)) as client: # Added timeout
            try:
                response = await client.get(tools_url, headers=headers)
            except httpx.RequestError as exc:
                logger.error(f"load_tools: HTTP request failed: {exc.__class__.__name__} - {exc}")
                return []

            logger.info(f"load_tools: Response status code: {response.status_code}")
            
            # 检查响应状态码
            if response.status_code != 200:
                logger.warning(f"load_tools: 获取工具映射列表失败: {response.status_code}")
                # 记录详细错误信息，但不暴露给客户端
                logger.debug(f"load_tools: Response content: {response.text}")
                return []
                
            logger.debug(f"load_tools: Raw response content: {response.text}")
            # 解析响应内容
            try:
                tools_data = response.json()
            except json.JSONDecodeError as exc:
                logger.error(f"load_tools: Failed to decode JSON response: {exc}")
                logger.debug(f"load_tools: Response content that failed to parse: {response.text}")
                return []

            # 验证工具列表
            if isinstance(tools_data, dict) and "tools" in tools_data:
                # 如果返回的是包含tools列表的字典，提取tools列表
                tools_data = tools_data["tools"]
                
            if not isinstance(tools_data, list):
                logger.warning(f"load_tools: 工具映射列表格式不正确: {type(tools_data)}, content: {tools_data}")
                return []
                
            # 清理并验证每个工具
            valid_tools = []
            for tool in tools_data:
                if not isinstance(tool, dict) or "name" not in tool:
                    logger.warning(f"load_tools: 工具格式不正确，跳过: {tool}")
                    continue
                valid_tools.append(tool)
                
            logger.info(f"load_tools: 成功从工具映射管理加载 {len(valid_tools)} 个工具")
            return valid_tools
    except Exception as e:
        logger.error(f"load_tools: 加载工具列表时发生异常: {str(e)}", exc_info=True)
        logger.warning("load_tools: 没有从工具映射管理中加载到工具")
        return [] 