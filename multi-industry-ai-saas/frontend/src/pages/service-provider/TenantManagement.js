import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Typography,
  Input,
  Row,
  Col,
  Dropdown,
  Menu,
  Modal,
  Form,
  Select,
  InputNumber,
  Tooltip,
  Badge,
  Avatar,
  Progress,
  Tabs,
  Statistic,
  Divider
} from 'antd';
import {
  ShopOutlined,
  PlusOutlined,
  SearchOutlined,
  FilterOutlined,
  MoreOutlined,
  EditOutlined,
  SettingOutlined,
  DeleteOutlined,
  ExclamationCircleOutlined,
  UserOutlined,
  CloudUploadOutlined,
  AppstoreOutlined,
  CloudServerOutlined,
  TeamOutlined,
  RiseOutlined,
  DownOutlined,
  EyeOutlined,
  PauseCircleOutlined,
  PlayCircleOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import apiService from '../../services/api';
import { useAuth } from '../../contexts/AuthContext';
import './TenantManagement.css';

const { Title, Text } = Typography;
const { TabPane } = Tabs;
const { confirm } = Modal;

const TenantManagement = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [tenants, setTenants] = useState([]);
  const [searchText, setSearchText] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');
  const [filterIndustry, setFilterIndustry] = useState('all');
  const [modalVisible, setModalVisible] = useState(false);
  const [editingTenant, setEditingTenant] = useState(null);
  const [form] = Form.useForm();
  const [activeTab, setActiveTab] = useState('all');
  const [statistics, setStatistics] = useState({
    total: 0,
    active: 0,
    trial: 0,
    suspended: 0,
    inactive: 0
  });

  // 加载租户数据
  useEffect(() => {
    fetchTenants();
  }, [user.service_provider_id]);

  // 获取租户列表
  const fetchTenants = async () => {
    try {
      setLoading(true);
      const response = await apiService.getTenantsByServiceProvider(user.service_provider_id);

      // 添加一些模拟数据以便展示
      const enhancedTenants = response.map(tenant => ({
        ...tenant,
        current_users: Math.floor(Math.random() * tenant.max_users),
        current_storage_gb: Math.floor(Math.random() * tenant.max_storage_gb),
        current_projects: Math.floor(Math.random() * tenant.max_projects),
        subscription_plan_name: ['标准版', '专业版', '企业版', '尊享版'][Math.floor(Math.random() * 4)],
        last_active: new Date(Date.now() - Math.floor(Math.random() * 30) * 86400000).toLocaleDateString()
      }));

      setTenants(enhancedTenants);

      // 计算统计数据
      const stats = {
        total: enhancedTenants.length,
        active: enhancedTenants.filter(t => t.status === 'active').length,
        trial: enhancedTenants.filter(t => t.status === 'trial').length,
        suspended: enhancedTenants.filter(t => t.status === 'suspended').length,
        inactive: enhancedTenants.filter(t => t.status === 'inactive').length
      };
      setStatistics(stats);
    } catch (error) {
      console.error('获取租户列表失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 打开创建租户模态框
  const showCreateModal = () => {
    setEditingTenant(null);
    form.resetFields();
    setModalVisible(true);
  };

  // 打开编辑租户模态框
  const showEditModal = (tenant) => {
    setEditingTenant(tenant);
    form.setFieldsValue({
      name: tenant.name,
      industry_type: tenant.industry_type,
      status: tenant.status,
      max_users: tenant.max_users,
      max_projects: tenant.max_projects,
      max_storage_gb: tenant.max_storage_gb,
      subscription_plan_id: tenant.subscription_plan_id
    });
    setModalVisible(true);
  };

  // 处理表单提交
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      if (editingTenant) {
        // 更新租户
        await apiService.updateTenant(editingTenant.id, values);
      } else {
        // 创建租户
        values.service_provider_id = user.service_provider_id;
        await apiService.createTenant(values);
      }

      setModalVisible(false);
      fetchTenants();
    } catch (error) {
      console.error('提交表单失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 处理租户状态变更
  const handleStatusChange = async (tenant, newStatus) => {
    try {
      await apiService.updateTenant(tenant.id, { status: newStatus });
      fetchTenants();
    } catch (error) {
      console.error('更新租户状态失败:', error);
    }
  };

  // 处理删除租户
  const handleDelete = (tenant) => {
    confirm({
      title: '确定要删除此租户吗?',
      icon: <ExclamationCircleOutlined />,
      content: '删除后将无法恢复，租户的所有数据将被清除。',
      okText: '确定',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          await apiService.deleteTenant(tenant.id);
          fetchTenants();
        } catch (error) {
          console.error('删除租户失败:', error);
        }
      }
    });
  };

  // 过滤租户数据
  const getFilteredTenants = () => {
    return tenants.filter(tenant => {
      const matchesSearch = tenant.name.toLowerCase().includes(searchText.toLowerCase()) ||
                           tenant.industry_type.toLowerCase().includes(searchText.toLowerCase());
      const matchesStatus = filterStatus === 'all' || tenant.status === filterStatus;
      const matchesIndustry = filterIndustry === 'all' || tenant.industry_type === filterIndustry;
      const matchesTab = activeTab === 'all' || tenant.status === activeTab;

      return matchesSearch && matchesStatus && matchesIndustry && matchesTab;
    });
  };

  // 租户表格列
  const columns = [
    {
      title: '租户名称',
      dataIndex: 'name',
      key: 'name',
      render: (text, record) => (
        <Space>
          <Avatar icon={<ShopOutlined />} style={{ backgroundColor: '#1890ff' }} />
          <Text strong>{text}</Text>
        </Space>
      )
    },
    {
      title: '行业',
      dataIndex: 'industry_type',
      key: 'industry_type',
      render: (text) => {
        const industryMap = {
          'retail': { color: 'blue', name: '零售' },
          'restaurant': { color: 'orange', name: '餐饮' },
          'hotel': { color: 'purple', name: '酒店' }
        };
        const industry = industryMap[text] || { color: 'default', name: text };
        return <Tag color={industry.color}>{industry.name}</Tag>;
      }
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status) => {
        const statusMap = {
          'active': { color: 'success', text: '正常' },
          'inactive': { color: 'default', text: '未激活' },
          'suspended': { color: 'error', text: '已暂停' },
          'trial': { color: 'processing', text: '试用中' }
        };
        const statusInfo = statusMap[status] || { color: 'default', text: status };
        return <Tag color={statusInfo.color}>{statusInfo.text}</Tag>;
      }
    },
    {
      title: '用户数',
      dataIndex: 'max_users',
      key: 'max_users',
      render: (max, record) => (
        <Space direction="vertical" size={0} style={{ width: '100%' }}>
          <Text>{record.current_users || 0}/{max}</Text>
          <Progress
            percent={Math.round(((record.current_users || 0) / max) * 100)}
            size="small"
            showInfo={false}
            status={((record.current_users || 0) / max) > 0.8 ? 'exception' : 'normal'}
          />
        </Space>
      )
    },
    {
      title: '存储空间',
      dataIndex: 'max_storage_gb',
      key: 'max_storage_gb',
      render: (max, record) => (
        <Space direction="vertical" size={0} style={{ width: '100%' }}>
          <Text>{record.current_storage_gb || 0}/{max} GB</Text>
          <Progress
            percent={Math.round(((record.current_storage_gb || 0) / max) * 100)}
            size="small"
            showInfo={false}
            status={((record.current_storage_gb || 0) / max) > 0.8 ? 'exception' : 'normal'}
          />
        </Space>
      )
    },
    {
      title: '项目数',
      dataIndex: 'max_projects',
      key: 'max_projects',
      render: (max, record) => (
        <Space direction="vertical" size={0} style={{ width: '100%' }}>
          <Text>{record.current_projects || 0}/{max}</Text>
          <Progress
            percent={Math.round(((record.current_projects || 0) / max) * 100)}
            size="small"
            showInfo={false}
            status={((record.current_projects || 0) / max) > 0.8 ? 'exception' : 'normal'}
          />
        </Space>
      )
    },
    {
      title: '订阅计划',
      dataIndex: 'subscription_plan_name',
      key: 'subscription_plan_name',
      render: (text) => <Tag color="gold">{text || '标准版'}</Tag>
    },
    {
      title: '最近活跃',
      dataIndex: 'last_active',
      key: 'last_active',
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => navigate(`/service-provider/tenants/${record.id}`)}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => showEditModal(record)}
            />
          </Tooltip>
          <Dropdown
            overlay={
              <Menu>
                <Menu.Item
                  key="settings"
                  icon={<SettingOutlined />}
                  onClick={() => navigate(`/service-provider/tenants/${record.id}/settings`)}
                >
                  配置管理
                </Menu.Item>
                <Menu.Item
                  key="users"
                  icon={<TeamOutlined />}
                  onClick={() => navigate(`/service-provider/tenants/${record.id}/users`)}
                >
                  用户管理
                </Menu.Item>
                <Menu.Item
                  key="plugins"
                  icon={<AppstoreOutlined />}
                  onClick={() => navigate(`/service-provider/tenants/${record.id}/plugins`)}
                >
                  插件管理
                </Menu.Item>
                <Menu.Divider />
                {record.status === 'active' ? (
                  <Menu.Item
                    key="suspend"
                    icon={<PauseCircleOutlined />}
                    onClick={() => handleStatusChange(record, 'suspended')}
                  >
                    暂停租户
                  </Menu.Item>
                ) : (
                  <Menu.Item
                    key="activate"
                    icon={<PlayCircleOutlined />}
                    onClick={() => handleStatusChange(record, 'active')}
                  >
                    激活租户
                  </Menu.Item>
                )}
                <Menu.Item
                  key="delete"
                  danger
                  icon={<DeleteOutlined />}
                  onClick={() => handleDelete(record)}
                >
                  删除租户
                </Menu.Item>
              </Menu>
            }
            trigger={['click']}
          >
            <Button type="text" icon={<MoreOutlined />} />
          </Dropdown>
        </Space>
      ),
    },
  ];

  return (
    <div className="tenant-management">
      <div className="page-header">
        <Title level={2}><ShopOutlined /> 租户管理</Title>
        <Text type="secondary">管理您的所有租户，包括配置、用户和插件</Text>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} className="stats-row">
        <Col xs={24} sm={12} md={8} lg={4}>
          <Card bordered={false} className="stat-card">
            <Statistic
              title="租户总数"
              value={statistics.total}
              prefix={<ShopOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={8} lg={4}>
          <Card bordered={false} className="stat-card">
            <Statistic
              title="正常租户"
              value={statistics.active}
              prefix={<PlayCircleOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={8} lg={4}>
          <Card bordered={false} className="stat-card">
            <Statistic
              title="试用租户"
              value={statistics.trial}
              prefix={<RiseOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={8} lg={4}>
          <Card bordered={false} className="stat-card">
            <Statistic
              title="已暂停"
              value={statistics.suspended}
              prefix={<PauseCircleOutlined />}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={8} lg={4}>
          <Card bordered={false} className="stat-card">
            <Statistic
              title="未激活"
              value={statistics.inactive}
              prefix={<ExclamationCircleOutlined />}
              valueStyle={{ color: '#bfbfbf' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} md={8} lg={4}>
          <Card bordered={false} className="stat-card action-card">
            <Button
              type="primary"
              icon={<PlusOutlined />}
              size="large"
              onClick={showCreateModal}
              block
            >
              创建新租户
            </Button>
          </Card>
        </Col>
      </Row>

      <Card bordered={false} className="tenant-card">
        {/* 搜索和过滤 */}
        <div className="table-toolbar">
          <Input
            placeholder="搜索租户名称或行业"
            prefix={<SearchOutlined />}
            value={searchText}
            onChange={e => setSearchText(e.target.value)}
            style={{ width: 250 }}
            allowClear
          />
          <Space>
            <Select
              placeholder="状态筛选"
              style={{ width: 120 }}
              value={filterStatus}
              onChange={setFilterStatus}
              allowClear
            >
              <Select.Option value="all">全部状态</Select.Option>
              <Select.Option value="active">正常</Select.Option>
              <Select.Option value="inactive">未激活</Select.Option>
              <Select.Option value="suspended">已暂停</Select.Option>
              <Select.Option value="trial">试用中</Select.Option>
            </Select>
            <Select
              placeholder="行业筛选"
              style={{ width: 120 }}
              value={filterIndustry}
              onChange={setFilterIndustry}
              allowClear
            >
              <Select.Option value="all">全部行业</Select.Option>
              <Select.Option value="retail">零售</Select.Option>
              <Select.Option value="restaurant">餐饮</Select.Option>
              <Select.Option value="hotel">酒店</Select.Option>
            </Select>
            <Button
              icon={<ReloadOutlined />}
              onClick={fetchTenants}
            >
              刷新
            </Button>
          </Space>
        </div>

        {/* 租户列表标签页 */}
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          className="tenant-tabs"
        >
          <TabPane
            tab={
              <Badge count={statistics.total} offset={[10, 0]}>
                <span>全部</span>
              </Badge>
            }
            key="all"
          />
          <TabPane
            tab={
              <Badge count={statistics.active} offset={[10, 0]}>
                <span>正常</span>
              </Badge>
            }
            key="active"
          />
          <TabPane
            tab={
              <Badge count={statistics.trial} offset={[10, 0]}>
                <span>试用中</span>
              </Badge>
            }
            key="trial"
          />
          <TabPane
            tab={
              <Badge count={statistics.suspended} offset={[10, 0]}>
                <span>已暂停</span>
              </Badge>
            }
            key="suspended"
          />
          <TabPane
            tab={
              <Badge count={statistics.inactive} offset={[10, 0]}>
                <span>未激活</span>
              </Badge>
            }
            key="inactive"
          />
        </Tabs>

        {/* 租户表格 */}
        <Table
          columns={columns}
          dataSource={getFilteredTenants()}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showTotal: total => `共 ${total} 条记录`,
            showSizeChanger: true,
            showQuickJumper: true
          }}
        />
      </Card>

      {/* 创建/编辑租户模态框 */}
      <Modal
        title={editingTenant ? '编辑租户' : '创建租户'}
        open={modalVisible}
        onOk={handleSubmit}
        onCancel={() => setModalVisible(false)}
        confirmLoading={loading}
        width={700}
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="租户名称"
                rules={[{ required: true, message: '请输入租户名称' }]}
              >
                <Input placeholder="请输入租户名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="industry_type"
                label="行业类型"
                rules={[{ required: true, message: '请选择行业类型' }]}
              >
                <Select placeholder="请选择行业类型">
                  <Select.Option value="retail">零售</Select.Option>
                  <Select.Option value="restaurant">餐饮</Select.Option>
                  <Select.Option value="hotel">酒店</Select.Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="status"
                label="状态"
                initialValue="active"
              >
                <Select>
                  <Select.Option value="active">正常</Select.Option>
                  <Select.Option value="inactive">未激活</Select.Option>
                  <Select.Option value="suspended">已暂停</Select.Option>
                  <Select.Option value="trial">试用中</Select.Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="subscription_plan_id"
                label="订阅计划"
              >
                <Select placeholder="请选择订阅计划">
                  <Select.Option value="1">标准版</Select.Option>
                  <Select.Option value="2">专业版</Select.Option>
                  <Select.Option value="3">企业版</Select.Option>
                  <Select.Option value="4">尊享版</Select.Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="max_users"
                label="最大用户数"
                initialValue={10}
              >
                <InputNumber min={1} max={1000} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="max_projects"
                label="最大项目数"
                initialValue={5}
              >
                <InputNumber min={1} max={100} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="max_storage_gb"
                label="最大存储空间(GB)"
                initialValue={10}
              >
                <InputNumber min={1} max={1000} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    </div>
  );
};

export default TenantManagement;
