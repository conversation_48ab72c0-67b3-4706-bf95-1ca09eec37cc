#!/usr/bin/env python
# -*- coding: utf-8 -*-

import logging
from typing import List, Optional, Dict, Any, Union
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import and_, or_, desc, update, func, case
from datetime import datetime
import uuid
import os
import json

from models.loss import Loss
from models.store import Store
from models.product import Product, ProductCategory
from models.user import User
from schemas.loss import LossCreate, LossUpdate, LossStatusUpdate
from core.config import settings

# 配置日志
logger = logging.getLogger(__name__)

class LossService:
    @staticmethod
    async def get_loss_by_id(db: AsyncSession, loss_id: Union[uuid.UUID, str]) -> Optional[Loss]:
        """根据ID获取报损记录"""
        result = await db.execute(select(Loss).where(Loss.id == loss_id))
        return result.scalars().first()

    @staticmethod
    async def get_losses(
        db: AsyncSession,
        project_id: Union[uuid.UUID, str],
        skip: int = 0,
        limit: int = 100,
        store_id: Optional[Union[uuid.UUID, str]] = None,
        product_id: Optional[Union[uuid.UUID, str]] = None,
        status: Optional[str] = None,
        search: Optional[str] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        created_by: Optional[Union[uuid.UUID, str]] = None
    ) -> List[Loss]:
        """获取报损记录列表"""
        query = select(Loss).where(Loss.project_id == project_id)

        # 应用过滤条件
        if store_id:
            query = query.where(Loss.store_id == store_id)
        if product_id:
            query = query.where(Loss.product_id == product_id)
        if status:
            query = query.where(Loss.status == status)
        if created_by:
            query = query.where(Loss.created_by == created_by)
        if start_date:
            query = query.where(Loss.created_at >= start_date)
        if end_date:
            query = query.where(Loss.created_at <= end_date)
        if search:
            # 搜索门店名称、产品名称、报损原因等
            # 需要联表查询
            query = query.join(Store, Loss.store_id == Store.id)
            query = query.join(Product, Loss.product_id == Product.id)
            query = query.where(
                or_(
                    Store.name.ilike(f"%{search}%"),
                    Product.name.ilike(f"%{search}%"),
                    Loss.reason.ilike(f"%{search}%"),
                    Loss.notes.ilike(f"%{search}%")
                )
            )

        # 按创建时间降序排序
        query = query.order_by(desc(Loss.created_at))

        # 应用分页
        query = query.offset(skip).limit(limit)

        result = await db.execute(query)
        return result.scalars().all()

    @staticmethod
    async def count_losses(
        db: AsyncSession,
        project_id: Union[uuid.UUID, str],
        store_id: Optional[Union[uuid.UUID, str]] = None,
        product_id: Optional[Union[uuid.UUID, str]] = None,
        status: Optional[str] = None,
        search: Optional[str] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        created_by: Optional[Union[uuid.UUID, str]] = None
    ) -> int:
        """获取报损记录总数"""
        query = select(func.count()).select_from(Loss).where(Loss.project_id == project_id)

        # 应用过滤条件
        if store_id:
            query = query.where(Loss.store_id == store_id)
        if product_id:
            query = query.where(Loss.product_id == product_id)
        if status:
            query = query.where(Loss.status == status)
        if created_by:
            query = query.where(Loss.created_by == created_by)
        if start_date:
            query = query.where(Loss.created_at >= start_date)
        if end_date:
            query = query.where(Loss.created_at <= end_date)
        if search:
            # 搜索门店名称、产品名称、报损原因等
            # 需要联表查询
            query = query.join(Store, Loss.store_id == Store.id)
            query = query.join(Product, Loss.product_id == Product.id)
            query = query.where(
                or_(
                    Store.name.ilike(f"%{search}%"),
                    Product.name.ilike(f"%{search}%"),
                    Loss.reason.ilike(f"%{search}%"),
                    Loss.notes.ilike(f"%{search}%")
                )
            )

        result = await db.execute(query)
        return result.scalar()

    @staticmethod
    async def create_loss(
        db: AsyncSession,
        loss_data: LossCreate,
        project_id: Union[uuid.UUID, str],
        user_id: Union[uuid.UUID, str]
    ) -> Loss:
        """创建报损记录"""
        # 获取产品信息，计算总金额
        result = await db.execute(select(Product).where(Product.id == loss_data.product_id))
        product = result.scalars().first()
        if not product:
            raise ValueError("产品不存在")

        # 使用成本价计算总金额
        unit_price = product.cost_price if product.cost_price else 0
        total_amount = unit_price * loss_data.quantity

        # 创建报损记录
        db_loss = Loss(
            project_id=project_id,
            store_id=loss_data.store_id,
            product_id=loss_data.product_id,
            quantity=loss_data.quantity,
            product_unit=loss_data.product_unit if getattr(loss_data, 'product_unit', None) else getattr(product, 'unit', None),
            unit_price=unit_price,
            total_amount=total_amount,
            reason=loss_data.reason,
            notes=loss_data.notes,
            status="pending",
            created_by=user_id,
            images=[],  # 初始为空列表，后续通过文件上传服务添加
            images_size=0
        )

        db.add(db_loss)
        await db.commit()
        await db.refresh(db_loss)
        return db_loss

    @staticmethod
    async def update_loss(
        db: AsyncSession,
        loss_id: Union[uuid.UUID, str],
        loss_data: LossUpdate
    ) -> Optional[Loss]:
        """更新报损记录"""
        # 获取报损记录
        db_loss = await LossService.get_loss_by_id(db, loss_id)
        if not db_loss:
            return None

        # 只能更新待审核状态的报损记录
        if db_loss.status != "pending":
            raise ValueError(f"无法修改状态为'{db_loss.status}'的报损记录")

        # 更新字段
        update_data = loss_data.dict(exclude_unset=True)

        # 如果更新了数量，需要重新计算总金额
        if "quantity" in update_data:
            # 获取产品信息
            result = await db.execute(select(Product).where(Product.id == db_loss.product_id))
            product = result.scalars().first()
            if product and product.cost_price:
                db_loss.unit_price = product.cost_price
                db_loss.total_amount = product.cost_price * update_data["quantity"]

        # 更新其他字段
        for key, value in update_data.items():
            setattr(db_loss, key, value)

        await db.commit()
        await db.refresh(db_loss)
        return db_loss

    @staticmethod
    async def update_loss_status(
        db: AsyncSession,
        loss_id: Union[uuid.UUID, str],
        status_data: LossStatusUpdate,
        user_id: Union[uuid.UUID, str]
    ) -> Optional[Loss]:
        """更新报损记录状态"""
        # 获取报损记录
        db_loss = await LossService.get_loss_by_id(db, loss_id)
        if not db_loss:
            return None

        new_status = status_data.status

        # 状态转换验证
        if new_status == "withdrawn":
            # 只有待审核状态的报损记录可以撤回，且只能由创建者撤回
            if db_loss.status != "pending":
                raise ValueError(f"无法撤回状态为'{db_loss.status}'的报损记录")
            if str(db_loss.created_by) != str(user_id):
                raise ValueError("只有创建者可以撤回报损记录")
        elif new_status in ["approved", "rejected"]:
            # 只有待审核状态的报损记录可以审核
            if db_loss.status != "pending":
                raise ValueError(f"无法审核状态为'{db_loss.status}'的报损记录")

            # 如果是拒绝，必须提供拒绝原因
            if new_status == "rejected" and not status_data.reject_reason:
                raise ValueError("拒绝时必须提供拒绝原因")
        else:
            raise ValueError(f"无效的状态值: {new_status}")

        # 更新状态
        db_loss.status = new_status

        # 如果是审核操作，记录审核人和审核时间
        if new_status in ["approved", "rejected"]:
            db_loss.approved_by = user_id
            db_loss.approved_at = datetime.now()

            # 如果是拒绝，记录拒绝原因
            if new_status == "rejected":
                db_loss.reject_reason = status_data.reject_reason

        await db.commit()
        await db.refresh(db_loss)
        return db_loss

    @staticmethod
    async def add_loss_images(
        db: AsyncSession,
        loss_id: Union[uuid.UUID, str],
        images: List[Dict[str, Any]],
        images_size: int
    ) -> Optional[Loss]:
        """添加报损记录图片"""
        # 获取报损记录
        db_loss = await LossService.get_loss_by_id(db, loss_id)
        if not db_loss:
            return None

        # 更新图片信息
        db_loss.images = images
        db_loss.images_size = images_size

        await db.commit()
        await db.refresh(db_loss)
        return db_loss

    @staticmethod
    async def get_loss_with_details(
        db: AsyncSession,
        loss_id: Union[uuid.UUID, str]
    ) -> Optional[Dict[str, Any]]:
        """获取报损记录详情，包括关联信息"""
        # 构建查询
        query = select(
            Loss,
            Store.name.label("store_name"),
            Product.name.label("product_name"),
            Product.sku.label("product_code"),
            Loss.product_unit.label("product_unit"),
            User.username.label("creator_name")
        ).join(
            Store, Loss.store_id == Store.id
        ).join(
            Product, Loss.product_id == Product.id
        ).join(
            User, Loss.created_by == User.id
        ).where(
            Loss.id == loss_id
        )

        # 执行查询
        result = await db.execute(query)
        row = result.first()

        if not row:
            return None

        # 构建响应数据
        loss = row.Loss
        loss_dict = {
            "id": loss.id,
            "project_id": loss.project_id,
            "store_id": loss.store_id,
            "product_id": loss.product_id,
            "quantity": loss.quantity,
            "unit_price": loss.unit_price,
            "total_amount": loss.total_amount,
            "reason": loss.reason,
            "notes": loss.notes,
            "status": loss.status,
            "images": loss.images,
            "images_size": loss.images_size,
            "created_by": loss.created_by,
            "created_at": loss.created_at,
            "updated_at": loss.updated_at,
            "approved_by": loss.approved_by,
            "approved_at": loss.approved_at,
            "reject_reason": loss.reject_reason,
            "store_name": row.store_name,
            "product_name": row.product_name,
            "product_code": row.product_code,
            "product_unit": row.product_unit,
            "creator_name": row.creator_name,
            "approver_name": None
        }

        # 如果有审核人，获取审核人信息
        if loss.approved_by:
            approver_result = await db.execute(select(User.username).where(User.id == loss.approved_by))
            approver = approver_result.scalar()
            if approver:
                loss_dict["approver_name"] = approver

        return loss_dict

    @staticmethod
    async def get_loss_statistics(
        db: AsyncSession,
        project_id: Union[uuid.UUID, str],
        start_date: datetime,
        end_date: datetime,
        store_id: Optional[Union[uuid.UUID, str]] = None
    ) -> Dict[str, Any]:
        """获取报损统计数据"""
        # 基础查询条件
        base_conditions = [
            Loss.project_id == project_id,
            Loss.created_at >= start_date,
            Loss.created_at <= end_date
        ]

        if store_id:
            base_conditions.append(Loss.store_id == store_id)

        # 1. 获取总体统计数据 - 修改为只统计审核通过的记录
        total_query = select(
            # 报损总金额和总数量只统计审核通过的记录
            func.sum(case((Loss.status == 'approved', Loss.total_amount), else_=0)).label("total_amount"),
            func.count(case((Loss.status == 'approved', 1), else_=None)).label("total_count"),
            func.sum(case((Loss.status == 'pending', Loss.total_amount), else_=0)).label("pending_amount"),
            func.count(case((Loss.status == 'pending', 1), else_=None)).label("pending_count"),
            func.sum(case((Loss.status == 'approved', Loss.total_amount), else_=0)).label("approved_amount"),
            func.count(case((Loss.status == 'approved', 1), else_=None)).label("approved_count"),
            func.sum(case((Loss.status == 'rejected', Loss.total_amount), else_=0)).label("rejected_amount"),
            func.count(case((Loss.status == 'rejected', 1), else_=None)).label("rejected_count")
        ).where(and_(*base_conditions))

        total_result = await db.execute(total_query)
        total_stats = total_result.fetchone()

        # 2. 按门店统计 - 只统计审核通过的记录
        store_query = select(
            Store.id.label("store_id"),
            Store.name.label("store_name"),
            func.sum(Loss.total_amount).label("amount"),
            func.count(Loss.id).label("count")
        ).join(
            Store, Loss.store_id == Store.id
        ).where(
            and_(*base_conditions, Loss.status == 'approved')  # 只统计审核通过的记录
        ).group_by(
            Store.id, Store.name
        ).order_by(
            desc(func.sum(Loss.total_amount))
        )

        store_result = await db.execute(store_query)
        store_stats = store_result.fetchall()

        # 3. 按原因统计 - 只统计审核通过的记录
        reason_query = select(
            Loss.reason,
            func.sum(Loss.total_amount).label("amount"),
            func.count(Loss.id).label("count")
        ).where(
            and_(*base_conditions, Loss.status == 'approved')  # 只统计审核通过的记录
        ).group_by(
            Loss.reason
        ).order_by(
            desc(func.sum(Loss.total_amount))
        )

        reason_result = await db.execute(reason_query)
        reason_stats = reason_result.fetchall()

        # 4. 按月份统计 - 只统计审核通过的记录
        month_expr = func.date_trunc('month', Loss.created_at)
        month_query = select(
            month_expr.label("month"),
            func.sum(Loss.total_amount).label("amount"),
            func.count(Loss.id).label("count")
        ).where(
            and_(*base_conditions, Loss.status == 'approved')  # 只统计审核通过的记录
        ).group_by(
            month_expr
        ).order_by(
            month_expr
        )

        month_result = await db.execute(month_query)
        month_stats = month_result.fetchall()

        # 5. 按商品统计 (TOP5) - 只统计审核通过的记录
        product_query = select(
            Product.id.label("product_id"),
            Product.name.label("product_name"),
            func.sum(Loss.total_amount).label("amount"),
            func.count(Loss.id).label("count")
        ).join(
            Product, Loss.product_id == Product.id
        ).where(
            and_(*base_conditions, Loss.status == 'approved')  # 只统计审核通过的记录
        ).group_by(
            Product.id, Product.name
        ).order_by(
            desc(func.sum(Loss.total_amount))
        ).limit(5)

        product_result = await db.execute(product_query)
        product_stats = product_result.fetchall()

        # 6. 按分类统计 - 只统计审核通过的记录
        category_query = select(
            ProductCategory.id.label("category_id"),
            ProductCategory.name.label("category_name"),
            func.sum(Loss.total_amount).label("amount"),
            func.count(Loss.id).label("count")
        ).join(
            Product, Loss.product_id == Product.id
        ).join(
            ProductCategory, Product.category_id == ProductCategory.id
        ).where(
            and_(*base_conditions, Loss.status == 'approved')  # 只统计审核通过的记录
        ).group_by(
            ProductCategory.id, ProductCategory.name
        ).order_by(
            desc(func.sum(Loss.total_amount))
        )

        category_result = await db.execute(category_query)
        category_stats = category_result.fetchall()

        # 构建返回结果
        statistics = {
            "total_amount": float(total_stats.total_amount or 0),
            "total_count": total_stats.total_count or 0,
            "pending_amount": float(total_stats.pending_amount or 0),
            "pending_count": total_stats.pending_count or 0,
            "approved_amount": float(total_stats.approved_amount or 0),
            "approved_count": total_stats.approved_count or 0,
            "rejected_amount": float(total_stats.rejected_amount or 0),
            "rejected_count": total_stats.rejected_count or 0,
            "by_store": [
                {
                    "store_id": str(row.store_id),
                    "store_name": row.store_name,
                    "amount": float(row.amount or 0),
                    "count": row.count or 0
                } for row in store_stats
            ],
            "by_reason": [
                {
                    "reason": row.reason,
                    "reason_name": get_reason_name(row.reason),
                    "amount": float(row.amount or 0),
                    "count": row.count or 0
                } for row in reason_stats
            ],
            "by_month": [
                {
                    "month": row.month.strftime("%Y-%m"),
                    "amount": float(row.amount or 0),
                    "count": row.count or 0
                } for row in month_stats
            ],
            "by_product": [
                {
                    "product_id": str(row.product_id),
                    "product_name": row.product_name,
                    "amount": float(row.amount or 0),
                    "count": row.count or 0
                } for row in product_stats
            ],
            "by_category": [
                {
                    "category_id": str(row.category_id),
                    "category_name": row.category_name,
                    "amount": float(row.amount or 0),
                    "count": row.count or 0
                } for row in category_stats
            ]
        }

        return statistics

# 辅助函数：获取报损原因的中文名称
def get_reason_name(reason: str) -> str:
    """获取报损原因的中文名称"""
    reason_map = {
        "expired": "过期",
        "damaged": "损坏",
        "lost": "丢失",
        "quality": "质量问题",
        "other": "其他"
    }
    return reason_map.get(reason, reason)
