.plugin-management {
  padding: 24px;
}

.page-header {
  margin-bottom: 24px;
}

.stats-row {
  margin-bottom: 24px;
}

.stat-card {
  height: 100%;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
  transition: all 0.3s;
}

.stat-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.plugin-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.09);
}

.table-toolbar {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
}

.plugin-tabs {
  margin-bottom: 16px;
}

.plugin-list {
  margin-top: 16px;
}

.plugin-item {
  height: 100%;
  border-radius: 8px;
  transition: all 0.3s;
}

.plugin-item:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.plugin-cover {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 24px 0;
  background: linear-gradient(135deg, #f5f7fa 0%, #e4e8eb 100%);
}

.plugin-avatar {
  margin-bottom: 16px;
}

.plugin-status {
  margin-top: 8px;
}

.plugin-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.plugin-desc {
  margin-bottom: 8px;
}

.plugin-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.plugin-stats {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
}

.plugin-detail .plugin-header {
  display: flex;
  margin-bottom: 16px;
}

.plugin-detail .plugin-avatar {
  margin-right: 16px;
}

.plugin-detail .plugin-header-info {
  flex: 1;
}

.plugin-detail .plugin-tags {
  margin-bottom: 8px;
}

.plugin-detail .plugin-rating {
  display: flex;
  align-items: center;
}

.plugin-stats-row {
  margin: 16px 0;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .plugin-management {
    padding: 16px;
  }
  
  .table-toolbar {
    flex-direction: column;
    gap: 16px;
  }
  
  .table-toolbar .ant-input-affix-wrapper {
    width: 100% !important;
  }
  
  .plugin-meta {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .plugin-rating {
    margin-top: 8px;
  }
}
