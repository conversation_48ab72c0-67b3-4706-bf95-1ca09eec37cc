/**
 * 异步任务进度组件
 * 用于显示任务执行状态和进度
 */

import React, { useState } from 'react';
import { Progress, Spin, Alert, Button, Card, Typography, Space, Tag, Modal, Descriptions, Drawer, Collapse } from 'antd';
import { 
  LoadingOutlined, 
  CheckCircleOutlined, 
  CloseCircleOutlined, 
  ClockCircleOutlined,
  StopOutlined,
  ReloadOutlined,
  EyeOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import { formatDateTime, formatDate, formatTime } from '../utils/dateTimeUtils';

const { Text, Title, Paragraph } = Typography;
const { Panel } = Collapse;

const TaskStatusIcon = ({ status }) => {
  switch (status) {
    case 'pending':
      return <ClockCircleOutlined style={{ color: '#faad14' }} />;
    case 'running':
      return <LoadingOutlined style={{ color: '#1890ff' }} />;
    case 'success':
      return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
    case 'failed':
      return <CloseCircleOutlined style={{ color: '#ff4d4f' }} />;
    case 'cancelled':
      return <StopOutlined style={{ color: '#d9d9d9' }} />;
    default:
      return <ClockCircleOutlined style={{ color: '#d9d9d9' }} />;
  }
};

const TaskStatusTag = ({ status }) => {
  const statusConfig = {
    pending: { color: 'orange', text: '等待中' },
    running: { color: 'blue', text: '执行中' },
    success: { color: 'green', text: '已完成' },
    failed: { color: 'red', text: '失败' },
    cancelled: { color: 'default', text: '已取消' },
  };

  const config = statusConfig[status] || { color: 'default', text: '未知' };
  
  return (
    <Tag color={config.color} icon={<TaskStatusIcon status={status} />}>
      {config.text}
    </Tag>
  );
};

export const AsyncTaskProgress = ({
  taskInfo,
  loading = false,
  error = null,
  title = '任务执行中',
  showDetails = true,
  showActions = true,
  onCancel = null,
  onRetry = null,
  onViewResult = null,
  style = {},
  size = 'default', // small, default, large
}) => {
  const [detailVisible, setDetailVisible] = useState(false);
  
  if (!taskInfo && !loading && !error) {
    return null;
  }

  const progress = taskInfo?.progress || 0;
  const status = taskInfo?.status || 'pending';
  const isRunning = ['pending', 'running'].includes(status);
  const isCompleted = status === 'success';
  const isFailed = ['failed', 'cancelled'].includes(status);
  const hasResult = taskInfo?.result || (taskInfo && typeof taskInfo.result !== 'undefined');

  const getProgressStatus = () => {
    if (isCompleted) return 'success';
    if (isFailed) return 'exception';
    return 'active';
  };

  // 使用统一的时间格式化工具计算耗时
  const formatDuration = (startTime, endTime) => {
    if (!startTime) return '';
    
    try {
      const start = new Date(startTime);
      const end = endTime ? new Date(endTime) : new Date();
      
      if (isNaN(start.getTime()) || isNaN(end.getTime())) {
        return '';
      }
      
      const duration = Math.round((end - start) / 1000);
      
      if (duration < 60) return `${duration}秒`;
      if (duration < 3600) return `${Math.floor(duration / 60)}分${duration % 60}秒`;
      return `${Math.floor(duration / 3600)}小时${Math.floor((duration % 3600) / 60)}分`;
    } catch (error) {
      console.error('计算耗时失败:', error);
      return '';
    }
  };

  // 渲染任务结果预览
  const renderResultPreview = () => {
    if (!hasResult || !taskInfo.result) return null;
    
    const result = taskInfo.result;
    
    // 如果是采购分拨单AI处理结果 - 修复数据结构匹配
    let preview = null;
    let purchaseItems = [];
    
    // 检查多种可能的数据结构
    if (result.data && result.data.preview) {
      preview = result.data.preview;
      purchaseItems = preview.purchase_items || [];
    } else if (result.result && result.result.data && result.result.data.preview) {
      preview = result.result.data.preview;
      purchaseItems = preview.purchase_items || [];
    } else if (result.preview) {
      preview = result.preview;
      purchaseItems = preview.purchase_items || [];
    } else if (result.purchase_items) {
      purchaseItems = result.purchase_items || [];
    }
    
    // 如果是采购分拨单相关任务且有商品数据
    const isPurchaseOrderTask = taskInfo.task_type === 'purchase_order_ai_preview' || 
                               taskInfo.task_name?.includes('采购分拨单') ||
                               purchaseItems.length > 0;
    
    if (isPurchaseOrderTask && purchaseItems.length > 0) {
      return (
        <div style={{ marginTop: 12, padding: 12, background: '#f6ffed', border: '1px solid #b7eb8f', borderRadius: 6 }}>
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginBottom: 8 }}>
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <CheckCircleOutlined style={{ color: '#52c41a', marginRight: 6 }} />
              <Text strong style={{ color: '#389e0d' }}>AI识别成功</Text>
            </div>
            <Space size="small">
              <Button 
                size="small" 
                type="primary"
                onClick={() => onViewResult && onViewResult(result)}
              >
                查看详情
              </Button>
              <Button 
                size="small"
                onClick={() => {
                  // 跳转到采购管理页面
                  window.open('/project/purchase/orders', '_blank');
                }}
              >
                前往采购管理
              </Button>
              <Button 
                size="small"
                onClick={() => {
                  // 跳转到采购分拨页面
                  window.open('/project/purchase/orders?tab=2', '_blank');
                }}
              >
                继续分拨
              </Button>
            </Space>
          </div>
          
          <div>
            <Text type="secondary">识别到 {purchaseItems.length} 个商品：</Text>
            <div style={{ marginTop: 6 }}>
              {purchaseItems.slice(0, 3).map((item, index) => (
                <div key={index} style={{ fontSize: '12px', color: '#666' }}>
                  • {item.product_name} - {item.quantity}{item.product_unit} - ¥{item.total_amount}
                </div>
              ))}
              {purchaseItems.length > 3 && (
                <Text type="secondary" style={{ fontSize: '12px' }}>
                  等共 {purchaseItems.length} 个商品...
                </Text>
              )}
            </div>
            
            <div style={{ marginTop: 8, padding: 8, background: '#e6f7ff', borderRadius: 4 }}>
              <Text style={{ fontSize: '12px', color: '#1890ff' }}>
                💡 提示：您可以查看详情确认识别结果，前往采购管理页面生成订单，或继续进行分拨操作
              </Text>
            </div>
          </div>
        </div>
      );
    }
    
    // 其他类型的结果
    if (result.success) {
      return (
        <div style={{ marginTop: 12, padding: 12, background: '#f6ffed', border: '1px solid #b7eb8f', borderRadius: 6 }}>
          <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <CheckCircleOutlined style={{ color: '#52c41a', marginRight: 6 }} />
              <Text strong style={{ color: '#389e0d' }}>任务完成</Text>
            </div>
            {onViewResult && (
              <Button 
                size="small" 
                type="primary"
                onClick={() => onViewResult(result)}
              >
                查看结果
              </Button>
            )}
          </div>
          {result.message && (
            <Text type="secondary" style={{ fontSize: '12px', marginTop: 4, display: 'block' }}>
              {result.message}
            </Text>
          )}
        </div>
      );
    }
    
    return null;
  };

  // 渲染详情抽屉
  const renderDetailDrawer = () => {
    if (!taskInfo) return null;
    
    // 简化数据提取逻辑
    const result = taskInfo.result;
    let purchaseItems = [];
    let isPurchaseOrderTask = false;
    
    // 检查任务类型
    if (taskInfo.task_type === 'purchase_order_ai_preview' || 
        taskInfo.task_name?.includes('采购分拨单')) {
      isPurchaseOrderTask = true;
      
      // 提取采购商品数据
      if (result) {
        if (result.data?.preview?.purchase_items) {
          purchaseItems = result.data.preview.purchase_items;
        } else if (result.preview?.purchase_items) {
          purchaseItems = result.preview.purchase_items;
        } else if (result.purchase_items) {
          purchaseItems = result.purchase_items;
        }
      }
    }
    
    return (
      <Drawer
        title="任务详情"
        placement="right"
        onClose={() => setDetailVisible(false)}
        open={detailVisible}
        width={600}
      >
        <Descriptions bordered column={1} size="small">
          <Descriptions.Item label="任务ID">{taskInfo.id}</Descriptions.Item>
          <Descriptions.Item label="任务类型">{taskInfo.task_type}</Descriptions.Item>
          <Descriptions.Item label="任务名称">{taskInfo.task_name}</Descriptions.Item>
          <Descriptions.Item label="描述">{taskInfo.description || '无'}</Descriptions.Item>
          <Descriptions.Item label="状态">
            <TaskStatusTag status={status} />
          </Descriptions.Item>
          <Descriptions.Item label="进度">{progress}%</Descriptions.Item>
          <Descriptions.Item label="创建时间">
            {taskInfo.created_at ? formatDateTime(taskInfo.created_at) : '无'}
          </Descriptions.Item>
          <Descriptions.Item label="开始时间">
            {taskInfo.started_at ? formatDateTime(taskInfo.started_at) : '未开始'}
          </Descriptions.Item>
          <Descriptions.Item label="完成时间">
            {taskInfo.completed_at ? formatDateTime(taskInfo.completed_at) : '未完成'}
          </Descriptions.Item>
          <Descriptions.Item label="耗时">
            {formatDuration(taskInfo.started_at || taskInfo.created_at, taskInfo.completed_at || taskInfo.updated_at)}
          </Descriptions.Item>
        </Descriptions>

        {taskInfo.error_message && (
          <Alert
            message="错误信息"
            description={taskInfo.error_message}
            type="error"
            showIcon
            style={{ marginTop: 16 }}
          />
        )}

        {/* 采购分拨单任务的后续操作 */}
        {isCompleted && isPurchaseOrderTask && (
          <div style={{ marginTop: 16 }}>
            <Title level={5}>后续操作</Title>
            <div style={{ 
              background: '#f6ffed', 
              border: '1px solid #b7eb8f', 
              borderRadius: 6, 
              padding: 16 
            }}>
              <div style={{ marginBottom: 12 }}>
                <CheckCircleOutlined style={{ color: '#52c41a', marginRight: 6 }} />
                <Text strong style={{ color: '#389e0d' }}>
                  任务执行成功
                  {purchaseItems.length > 0 && ` - 识别到 ${purchaseItems.length} 个商品`}
                </Text>
              </div>
              
              <Space direction="vertical" style={{ width: '100%' }}>
                <Button 
                  type="primary"
                  block
                  onClick={() => {
                    setDetailVisible(false);
                    if (onViewResult && result) {
                      onViewResult(result);
                    }
                  }}
                >
                  查看识别结果
                </Button>
                
                <Button 
                  block
                  onClick={() => {
                    window.open('/project/purchase/orders', '_blank');
                  }}
                >
                  前往采购管理
                </Button>
                
                <Button 
                  block
                  onClick={() => {
                    window.open('/project/purchase/orders?tab=2', '_blank');
                  }}
                >
                  继续分拨操作
                </Button>
              </Space>
              
              <div style={{ marginTop: 12, padding: 8, background: '#e6f7ff', borderRadius: 4 }}>
                <Text style={{ fontSize: '12px', color: '#1890ff' }}>
                  💡 提示：您可以查看识别结果确认数据，前往采购管理页面生成订单，或继续进行分拨操作
                </Text>
              </div>
            </div>
          </div>
        )}

        {/* 其他任务类型的通用操作 */}
        {isCompleted && !isPurchaseOrderTask && hasResult && (
          <div style={{ marginTop: 16 }}>
            <Title level={5}>任务结果</Title>
            <div style={{ 
              background: '#f6ffed', 
              border: '1px solid #b7eb8f', 
              borderRadius: 6, 
              padding: 16 
            }}>
              <div style={{ marginBottom: 12 }}>
                <CheckCircleOutlined style={{ color: '#52c41a', marginRight: 6 }} />
                <Text strong style={{ color: '#389e0d' }}>任务执行成功</Text>
              </div>
              
              <Button 
                type="primary"
                block
                onClick={() => {
                  setDetailVisible(false);
                  if (onViewResult && result) {
                    onViewResult(result);
                  }
                }}
              >
                查看结果
              </Button>
            </div>
          </div>
        )}

        {/* 原始结果数据（调试用） */}
        {hasResult && (
          <div style={{ marginTop: 16 }}>
            <Title level={5}>原始数据</Title>
            <Collapse>
              <Panel header="查看完整结果" key="1">
                <pre style={{ 
                  background: '#f5f5f5', 
                  padding: 12, 
                  borderRadius: 4, 
                  fontSize: '12px',
                  maxHeight: '400px',
                  overflow: 'auto'
                }}>
                  {JSON.stringify(taskInfo.result, null, 2)}
                </pre>
              </Panel>
            </Collapse>
          </div>
        )}
      </Drawer>
    );
  };

  const cardSize = size === 'small' ? 'small' : 'default';

  return (
    <>
    <Card size={cardSize} style={{ marginBottom: 16, ...style }}>
      <Space direction="vertical" style={{ width: '100%' }}>
        {/* 标题和状态 */}
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Title level={size === 'small' ? 5 : 4} style={{ margin: 0 }}>
            {title}
          </Title>
          <TaskStatusTag status={status} />
        </div>

        {/* 进度条 */}
        {(isRunning || progress > 0) && (
          <Progress
            percent={Math.round(progress)}
            status={getProgressStatus()}
            size={size === 'small' ? 'small' : 'default'}
            showInfo={progress > 0}
          />
        )}

        {/* 加载状态 - 增强的加载提示 */}
        {loading && !taskInfo && (
          <div style={{ textAlign: 'center', padding: '20px 0' }}>
            <Spin size={size === 'small' ? 'small' : 'default'} />
            <Text style={{ marginLeft: 8 }}>正在启动任务...</Text>
          </div>
        )}

        {/* 运行中状态的加载提示 */}
        {isRunning && taskInfo && (
          <div style={{ 
            background: '#e6f7ff', 
            border: '1px solid #91d5ff',
            borderRadius: 6,
            padding: 12,
            marginTop: 8
          }}>
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <LoadingOutlined style={{ color: '#1890ff', marginRight: 8 }} />
              <Text strong style={{ color: '#1890ff' }}>
                {status === 'pending' ? '任务排队中，请稍候...' : '正在处理中，请勿刷新页面...'}
              </Text>
            </div>
            {taskInfo.description && (
              <Text type="secondary" style={{ fontSize: '12px', marginTop: 4, display: 'block' }}>
                {taskInfo.description}
              </Text>
            )}
          </div>
        )}

        {/* 错误信息 */}
        {error && (
          <Alert
            message="任务执行出错"
            description={error}
            type="error"
            showIcon
            size={size === 'small' ? 'small' : 'default'}
          />
        )}

        {/* 任务详情 */}
        {showDetails && taskInfo && (
          <div>
            {taskInfo.message && (
              <Text type="secondary">{taskInfo.message}</Text>
            )}
            
            {taskInfo.error_message && (
              <Alert
                message="执行失败"
                description={taskInfo.error_message}
                type="error"
                showIcon
                size="small"
                style={{ marginTop: 8 }}
              />
            )}

              {/* 结果预览 */}
              {renderResultPreview()}

            {/* 时间信息 - 使用统一的时间格式化 */}
            <div style={{ marginTop: 8 }}>
              <Space wrap>
                {taskInfo.created_at && (
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    创建时间: {formatDateTime(taskInfo.created_at)}
                  </Text>
                )}
                {taskInfo.started_at && (
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    开始时间: {formatDateTime(taskInfo.started_at)}
                  </Text>
                )}
                {taskInfo.completed_at && (
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    完成时间: {formatDateTime(taskInfo.completed_at)}
                  </Text>
                )}
                {taskInfo.updated_at && (
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    更新时间: {formatDateTime(taskInfo.updated_at)}
                  </Text>
                )}
                {(taskInfo.created_at || taskInfo.started_at) && (
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    耗时: {formatDuration(
                      taskInfo.started_at || taskInfo.created_at, 
                      taskInfo.completed_at || taskInfo.updated_at
                    )}
                  </Text>
                )}
              </Space>
            </div>
          </div>
        )}

        {/* 操作按钮 */}
        {showActions && (
          <div style={{ textAlign: 'right' }}>
            <Space>
                {taskInfo && (
                  <Button
                    size={size === 'small' ? 'small' : 'default'}
                    icon={<EyeOutlined />}
                    onClick={() => setDetailVisible(true)}
                  >
                    查看详情
                  </Button>
                )}
                {isCompleted && hasResult && onViewResult && (
                  <Button
                    size={size === 'small' ? 'small' : 'default'}
                    type="primary"
                    icon={<InfoCircleOutlined />}
                    onClick={() => onViewResult(taskInfo.result)}
                  >
                    查看结果
                  </Button>
                )}
              {isRunning && onCancel && (
                <Button
                  size={size === 'small' ? 'small' : 'default'}
                  onClick={onCancel}
                  icon={<StopOutlined />}
                >
                  取消
                </Button>
              )}
              {isFailed && onRetry && (
                <Button
                  size={size === 'small' ? 'small' : 'default'}
                  onClick={onRetry}
                  icon={<ReloadOutlined />}
                >
                  重试
                </Button>
              )}
            </Space>
          </div>
        )}
      </Space>
    </Card>
      
      {/* 详情抽屉 */}
      {renderDetailDrawer()}
    </>
  );
};

/**
 * 简化的任务进度条组件
 */
export const SimpleTaskProgress = ({
  progress = 0,
  status = 'running',
  message = '',
  size = 'default',
}) => {
  const getProgressStatus = () => {
    if (status === 'success') return 'success';
    if (['failed', 'cancelled'].includes(status)) return 'exception';
    return 'active';
  };

  return (
    <div style={{ padding: '8px 0' }}>
      <Progress
        percent={Math.round(progress)}
        status={getProgressStatus()}
        size={size}
        format={(percent) => (
          <span style={{ fontSize: size === 'small' ? '12px' : '14px' }}>
            {percent}%
          </span>
        )}
      />
      {message && (
        <Text type="secondary" style={{ fontSize: '12px', marginTop: 4, display: 'block' }}>
          {message}
        </Text>
      )}
    </div>
  );
};

/**
 * 任务列表组件
 */
export const TaskList = ({ 
  tasks = [], 
  onCancel, 
  onRetry, 
  onViewResult,
  size = 'small',
  showActions = true 
}) => {
  // 确保tasks是数组
  if (!Array.isArray(tasks) || tasks.length === 0) {
    return null;
  }

  return (
    <div>
      {tasks.map((task, index) => {
        // 兼容不同的数据格式
        const taskData = Array.isArray(task) ? task[1] : task;
        const taskKey = Array.isArray(task) ? task[0] : (task.id || index);
        
        return (
          <AsyncTaskProgress
            key={taskKey}
            taskInfo={taskData}
            title={taskData.task_name || `任务 ${taskKey}`}
            showDetails={true}
            showActions={showActions}
            size={size}
            onCancel={taskData.id && onCancel ? () => onCancel(taskData.id) : null}
            onRetry={taskData.status === 'failed' && onRetry ? () => onRetry(taskData.id) : null}
            onViewResult={onViewResult}
          />
        );
      })}
    </div>
  );
};

export default AsyncTaskProgress; 