#!/usr/bin/env python
# -*- coding: utf-8 -*-

import logging
from typing import List, Dict, Any, Optional
from fastapi import APIRouter, Depends, HTTPException, status, Query, Path
from sqlalchemy.ext.asyncio import AsyncSession
from pydantic import BaseModel, Field, UUID4
import uuid
from datetime import datetime

from db.database import get_db
from services.tenant_data_sharing_service import TenantDataSharingService
from core.auth import get_current_user, get_current_tenant_admin
from models.user import User
from core.tenant_context import get_tenant_context

# 配置日志
logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter()

# 请求和响应模型
class DataSharingCreate(BaseModel):
    """创建数据共享请求模型"""
    target_tenant_id: UUID4 = Field(..., description="目标租户ID")
    data_type: str = Field(..., description="数据类型")
    data_id: UUID4 = Field(..., description="数据ID")
    access_level: str = Field("read", description="访问级别: read, write, admin")
    expiry_days: Optional[int] = Field(None, description="过期天数，为空表示永不过期")
    metadata: Optional[Dict[str, Any]] = Field(None, description="元数据")

class DataSharingResponse(BaseModel):
    """数据共享响应模型"""
    id: str
    source_tenant_id: str
    target_tenant_id: str
    data_type: str
    data_id: str
    status: str
    access_level: str
    expiry_date: Optional[str] = None
    created_at: str
    updated_at: Optional[str] = None
    metadata: Dict[str, Any]

class DataSharingListResponse(BaseModel):
    """数据共享列表响应模型"""
    items: List[DataSharingResponse]
    total: int
    skip: int
    limit: int

class DataSharingRevokeResponse(BaseModel):
    """撤销数据共享响应模型"""
    success: bool
    message: str
    data: Dict[str, Any]

class DataAccessCheckResponse(BaseModel):
    """数据访问检查响应模型"""
    has_access: bool
    access_level: Optional[str] = None
    source: Optional[str] = None
    sharing_id: Optional[str] = None
    message: Optional[str] = None

# API路由
@router.post("/share", response_model=DataSharingResponse, status_code=status.HTTP_201_CREATED)
async def create_data_sharing(
    data: DataSharingCreate,
    current_user: User = Depends(get_current_tenant_admin),
    db: AsyncSession = Depends(get_db)
):
    """
    创建租户数据共享
    """
    # 获取当前租户上下文
    tenant_context = get_tenant_context()
    if not tenant_context or not tenant_context.tenant_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="无法获取当前租户信息"
        )
        
    # 创建数据共享
    result = await TenantDataSharingService.create_data_sharing(
        db=db,
        source_tenant_id=tenant_context.tenant_id,
        target_tenant_id=data.target_tenant_id,
        data_type=data.data_type,
        data_id=data.data_id,
        access_level=data.access_level,
        expiry_days=data.expiry_days,
        metadata=data.metadata,
        created_by=current_user.id
    )
    
    return result

@router.get("/shared", response_model=DataSharingListResponse)
async def list_shared_data(
    data_type: Optional[str] = None,
    status: str = "active",
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    列出当前租户共享的数据
    """
    # 获取当前租户上下文
    tenant_context = get_tenant_context()
    if not tenant_context or not tenant_context.tenant_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="无法获取当前租户信息"
        )
        
    # 查询共享数据
    result = await TenantDataSharingService.list_shared_data(
        db=db,
        tenant_id=tenant_context.tenant_id,
        data_type=data_type,
        status=status,
        as_source=True,
        skip=skip,
        limit=limit
    )
    
    return result

@router.get("/received", response_model=DataSharingListResponse)
async def list_received_data(
    data_type: Optional[str] = None,
    status: str = "active",
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    列出当前租户接收的共享数据
    """
    # 获取当前租户上下文
    tenant_context = get_tenant_context()
    if not tenant_context or not tenant_context.tenant_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="无法获取当前租户信息"
        )
        
    # 查询接收的共享数据
    result = await TenantDataSharingService.list_shared_data(
        db=db,
        tenant_id=tenant_context.tenant_id,
        data_type=data_type,
        status=status,
        as_source=False,
        skip=skip,
        limit=limit
    )
    
    return result

@router.get("/{sharing_id}", response_model=DataSharingResponse)
async def get_data_sharing(
    sharing_id: UUID4,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取数据共享详情
    """
    result = await TenantDataSharingService.get_data_sharing(
        db=db,
        sharing_id=sharing_id
    )
    
    return result

@router.post("/{sharing_id}/revoke", response_model=DataSharingRevokeResponse)
async def revoke_data_sharing(
    sharing_id: UUID4,
    current_user: User = Depends(get_current_tenant_admin),
    db: AsyncSession = Depends(get_db)
):
    """
    撤销数据共享
    """
    result = await TenantDataSharingService.revoke_data_sharing(
        db=db,
        sharing_id=sharing_id,
        user_id=current_user.id
    )
    
    return result

@router.get("/check-access/{data_type}/{data_id}", response_model=DataAccessCheckResponse)
async def check_data_access(
    data_type: str,
    data_id: UUID4,
    tenant_id: UUID4,
    access_level: str = "read",
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    检查是否有权限访问数据
    """
    result = await TenantDataSharingService.check_data_access(
        db=db,
        tenant_id=tenant_id,
        data_type=data_type,
        data_id=data_id,
        required_access_level=access_level,
        user_id=current_user.id
    )
    
    return result
