import React, { useState, useEffect } from 'react';
import { Card, Tabs, message } from 'antd';
import { ShopOutlined, DollarOutlined, BarChartOutlined } from '@ant-design/icons';
import ArrivalConfirmation from './daily-operations/ArrivalConfirmation';
import DailyExpenses from './daily-operations/DailyExpenses';
import MonthlyInventory from './daily-operations/MonthlyInventory';
import apiService from '../../../services/api';
import { getProjectId } from '../../../services/api/httpClient';
import './daily-operations/styles.css';

const { TabPane } = Tabs;

/**
 * 日常店务组件
 * 包含三个子模块：到货确认、日常费用、月度盘点
 */
const DailyOperations = () => {
  const [activeTab, setActiveTab] = useState('1');
  const [loading, setLoading] = useState(false);
  const [stores, setStores] = useState([]);
  const [currentStoreId, setCurrentStoreId] = useState(null);

  // 获取门店列表
  const fetchStores = async () => {
    setLoading(true);
    try {
      const projectId = getProjectId();
      if (!projectId) {
        message.error('未找到项目ID');
        return;
      }

      const response = await apiService.project.store.getList();
      if (response && response.items) {
        setStores(response.items);

        // 如果有门店，默认选择第一个
        if (response.items.length > 0 && !currentStoreId) {
          setCurrentStoreId(response.items[0].id);
        }
      }
    } catch (error) {
      console.error('获取门店列表失败:', error);
      message.error('获取门店列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 处理标签页切换
  const handleTabChange = (key) => {
    setActiveTab(key);
  };

  // 处理门店选择
  const handleStoreChange = (storeId) => {
    setCurrentStoreId(storeId);
  };

  // 初始化
  useEffect(() => {
    fetchStores();
  }, []);

  return (
    <div className="daily-operations-page">
      <Card title="日常店务" bordered={false}>
        <Tabs activeKey={activeTab} onChange={handleTabChange}>
          <TabPane
            tab={<span><ShopOutlined />到货确认</span>}
            key="1"
          >
            <ArrivalConfirmation
              stores={stores}
              currentStoreId={currentStoreId}
              onStoreChange={handleStoreChange}
              loading={loading}
            />
          </TabPane>

          <TabPane
            tab={<span><DollarOutlined />日常费用</span>}
            key="2"
          >
            <DailyExpenses
              stores={stores}
              currentStoreId={currentStoreId}
              onStoreChange={handleStoreChange}
              loading={loading}
            />
          </TabPane>

          <TabPane
            tab={<span><BarChartOutlined />月度盘点</span>}
            key="3"
          >
            <MonthlyInventory
              stores={stores}
              currentStoreId={currentStoreId}
              onStoreChange={handleStoreChange}
              loading={loading}
            />
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default DailyOperations;
