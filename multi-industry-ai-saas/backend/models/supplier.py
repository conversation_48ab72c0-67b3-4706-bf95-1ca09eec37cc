#!/usr/bin/env python
# -*- coding: utf-8 -*-

from sqlalchemy import Column, String, Integer, Float, Text, Boolean, ForeignKey, DateTime, UniqueConstraint
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid

from db.database import Base

class Supplier(Base):
    """供应商模型"""
    __tablename__ = "suppliers"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=True)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id"), nullable=False)
    name = Column(String(200), nullable=False)
    contact_person = Column(String(100), nullable=True)
    contact_phone = Column(String(20), nullable=True)
    email = Column(String(100), nullable=True)
    address = Column(String(255), nullable=True)
    description = Column(Text, nullable=True)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    created_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)
    updated_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)

    # 关系
    tenant = relationship("Tenant", back_populates="suppliers")
    project = relationship("Project", back_populates="suppliers")
    supplier_products = relationship("SupplierProduct", back_populates="supplier")
    purchase_order_items = relationship("PurchaseOrderItem", back_populates="supplier")
    evaluations = relationship("SupplierEvaluation", back_populates="supplier")

    __table_args__ = (
        UniqueConstraint('project_id', 'name', name='uq_supplier_name_per_project'),
    )

    def __repr__(self):
        return f"<Supplier(id={self.id}, name='{self.name}')>"


class SupplierProduct(Base):
    """供应商产品关联模型"""
    __tablename__ = "supplier_products"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=True)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id"), nullable=False)
    supplier_id = Column(UUID(as_uuid=True), ForeignKey("suppliers.id"), nullable=False)
    product_id = Column(UUID(as_uuid=True), ForeignKey("products.id"), nullable=False)
    supplier_product_code = Column(String(100), nullable=True)
    price = Column(Float, nullable=True)
    min_order_quantity = Column(Integer, nullable=True)
    lead_time_days = Column(Integer, nullable=True)
    is_preferred = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    created_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)
    updated_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)

    # 关系
    supplier = relationship("Supplier", back_populates="supplier_products")
    product = relationship("Product", back_populates="supplier_products")
    tenant = relationship("Tenant", back_populates="supplier_products")
    project = relationship("Project", back_populates="supplier_products")

    __table_args__ = (
        UniqueConstraint('project_id', 'supplier_id', 'product_id', name='uq_supplier_product_per_project'),
    )

    def __repr__(self):
        return f"<SupplierProduct(id={self.id}, supplier_id='{self.supplier_id}', product_id='{self.product_id}')>"


class SupplierEvaluation(Base):
    """供应商评估模型"""
    __tablename__ = "supplier_evaluations"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=True)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id"), nullable=False)
    supplier_id = Column(UUID(as_uuid=True), ForeignKey("suppliers.id"), nullable=False)
    evaluation_date = Column(DateTime(timezone=True), nullable=False)
    quality_score = Column(Integer, nullable=True)  # 1-5
    delivery_score = Column(Integer, nullable=True)  # 1-5
    price_score = Column(Integer, nullable=True)  # 1-5
    service_score = Column(Integer, nullable=True)  # 1-5
    overall_score = Column(Float, nullable=True)
    comments = Column(Text, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    created_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    updated_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)

    # 关系
    supplier = relationship("Supplier", back_populates="evaluations")
    tenant = relationship("Tenant", back_populates="supplier_evaluations")
    project = relationship("Project", back_populates="supplier_evaluations")
    evaluator = relationship("User", foreign_keys=[created_by])

    def __repr__(self):
        return f"<SupplierEvaluation(id={self.id}, supplier_id='{self.supplier_id}', overall_score={self.overall_score})>"
