#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
钉钉插件统一设置服务
重构后只使用 DingTalkSettings 表，消除 DingTalkConfig 表的冗余
"""

import logging
from typing import Dict, Any, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_
import uuid
import secrets

from ..models.models import DingTalkSettings

logger = logging.getLogger(__name__)

class UnifiedSettingsService:
    """钉钉插件统一设置服务"""
    
    def __init__(self, db: AsyncSession, tenant_id: str, project_id: str):
        self.db = db
        # 确保ID为UUID格式
        if isinstance(tenant_id, str):
            self.tenant_id = uuid.UUID(tenant_id)
        else:
            self.tenant_id = tenant_id
            
        if isinstance(project_id, str):
            self.project_id = uuid.UUID(project_id)
        else:
            self.project_id = project_id
    
    async def get_settings_by_corp_id(self, corp_id: str) -> Optional[DingTalkSettings]:
        """通过钉钉企业CorpID获取设置的异步方法"""
        settings_query = select(DingTalkSettings).where(DingTalkSettings.corp_id == corp_id)
        result = await self.db.execute(settings_query)
        return result.scalar_one_or_none()

    async def get_settings(self) -> Dict[str, Any]:
        """
        获取钉钉插件设置
        
        Returns:
            Dict[str, Any]: 设置数据
        """
        try:
            # 从统一的设置表获取所有配置
            settings_query = select(DingTalkSettings).where(
                and_(
                    DingTalkSettings.tenant_id == self.tenant_id,
                    DingTalkSettings.project_id == self.project_id
                )
            )
            result = await self.db.execute(settings_query)
            settings = result.scalar_one_or_none()
            
            if settings:
                return {
                    # 钉钉应用配置
                    "app_key": settings.app_key or "",
                    "app_secret": settings.app_secret or "",
                    "agent_id": settings.agent_id or "",
                    "corp_id": settings.corp_id or "",
                    "webhook_url": settings.webhook_url or "",
                    "callback_url": settings.callback_url or "",
                    "aes_key": settings.aes_key or "",
                    "token": settings.token or "",
                    
                    # 基础设置
                    "enabled": settings.enabled,
                    "enable_dingtalk": getattr(settings, 'enable_dingtalk', True),
                    "notification_level": getattr(settings, 'notification_level', 'all'),
                    "retry_count": getattr(settings, 'retry_count', 3),
                    "retry_interval": getattr(settings, 'retry_interval', 60),
                    "default_template": getattr(settings, 'default_template', ''),
                    
                    # 扩展配置
                    "config": settings.config or {},
                    "ai_integration_config": getattr(settings, 'ai_integration_config', {}),

                    # AI助理直通模式配置
                    "ai_passthrough_enabled": getattr(settings, 'ai_passthrough_enabled', False),
                    "passthrough_ai_assistant_id": getattr(settings, 'passthrough_ai_assistant_id', None),
                    "dingtalk_ai_assistant_id": getattr(settings, 'dingtalk_ai_assistant_id', None),
                    "dingtalk_ai_client_id": getattr(settings, 'dingtalk_ai_client_id', None),
                    "dingtalk_ai_client_secret": getattr(settings, 'dingtalk_ai_client_secret', None),
                    "dingtalk_ai_auth_method": getattr(settings, 'dingtalk_ai_auth_method', 'none'),
                    "dingtalk_api_key_auth_type": getattr(settings, 'dingtalk_api_key_auth_type', 'custom_header'),
                    "dingtalk_custom_header_name": getattr(settings, 'dingtalk_custom_header_name', None),
                    "dingtalk_custom_api_key": getattr(settings, 'dingtalk_custom_api_key', None),
                    "dingtalk_oauth_auth_url": getattr(settings, 'dingtalk_oauth_auth_url', None),
                    "dingtalk_oauth_token_url": getattr(settings, 'dingtalk_oauth_token_url', None),
                    "domain": getattr(settings, 'domain', None),
                }
            else:
                # 返回默认设置
                return {
                    "app_key": "",
                    "app_secret": "",
                    "agent_id": "",
                    "corp_id": "",
                    "webhook_url": "",
                    "callback_url": "",
                    "aes_key": "",
                    "token": "",
                    "enabled": True,
                    "enable_dingtalk": True,
                    "notification_level": "all",
                    "retry_count": 3,
                    "retry_interval": 60,
                    "default_template": "",
                    "config": {},
                    "ai_integration_config": {},
                    "ai_passthrough_enabled": False,
                    "passthrough_ai_assistant_id": None,
                    "dingtalk_ai_assistant_id": None,
                    "dingtalk_ai_client_id": None,
                    "dingtalk_ai_client_secret": None,
                    "dingtalk_ai_auth_method": "none",
                    "dingtalk_api_key_auth_type": "custom_header",
                    "dingtalk_custom_header_name": None,
                    "dingtalk_custom_api_key": None,
                    "dingtalk_oauth_auth_url": None,
                    "dingtalk_oauth_token_url": None,
                    "domain": None,
                }
                
        except Exception as e:
            logger.error(f"获取钉钉插件设置失败: {str(e)}")
            return {}
    
    async def update_settings(self, settings_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        更新钉钉插件设置
        
        Args:
            settings_data: 设置数据
            
        Returns:
            Dict[str, Any]: 更新结果
        """
        try:
            # 首先获取现有设置，以便进行比较
            settings_query = select(DingTalkSettings).where(
                and_(
                    DingTalkSettings.tenant_id == self.tenant_id,
                    DingTalkSettings.project_id == self.project_id
                )
            )
            result = await self.db.execute(settings_query)
            existing_settings = result.scalar_one_or_none()
            
            if not existing_settings:
                # 如果记录不存在，则创建一个新的空的实例
                settings = DingTalkSettings(
                    id=uuid.uuid4(),
                    tenant_id=self.tenant_id,
                    project_id=self.project_id
                )
                self.db.add(settings)
            else:
                settings = existing_settings
            
            # 使用 setattr 安全地逐个更新字段，无论是新建还是更新
            for field_name, field_value in settings_data.items():
                if hasattr(settings, field_name):
                    setattr(settings, field_name, field_value)
                else:
                    logger.warning(f"更新钉钉设置时忽略未知字段: '{field_name}'")
            
            await self.db.commit()
            await self.db.refresh(settings)
            
            # 返回更新后的设置
            updated_settings = await self.get_settings()
            
            return {
                "success": True,
                "message": "设置更新成功",
                "settings": updated_settings
            }
            
        except Exception as e:
            logger.error(f"更新钉钉插件设置失败: {str(e)}")
            await self.db.rollback()
            return {
                "success": False,
                "message": f"更新设置失败: {str(e)}"
            }
    
    async def get_config_for_api(self) -> Dict[str, str]:
        """
        获取API调用所需的配置
        
        Returns:
            Dict[str, str]: API配置
        """
        try:
            settings = await self.get_settings()
            return {
                "app_key": settings.get("app_key", ""),
                "app_secret": settings.get("app_secret", ""),
                "agent_id": settings.get("agent_id", ""),
                "corp_id": settings.get("corp_id", ""),
                "webhook_url": settings.get("webhook_url", ""),
                "has_config": bool(
                    settings.get("app_key") and 
                    settings.get("app_secret")
                )
            }
            
        except Exception as e:
            logger.error(f"获取API配置失败: {str(e)}")
            return {
                "app_key": "",
                "app_secret": "",
                "agent_id": "",
                "corp_id": "",
                "webhook_url": "",
                "has_config": False
            }
    
    async def is_configured(self) -> bool:
        """
        检查钉钉插件是否已正确配置
        
        Returns:
            bool: 是否已配置
        """
        try:
            settings = await self.get_settings()
            return bool(
                settings.get("app_key") and 
                settings.get("app_secret") and 
                settings.get("enabled", False)
            )
            
        except Exception as e:
            logger.error(f"检查钉钉插件配置失败: {str(e)}")
            return False

# 提供向后兼容的别名
SettingsService = UnifiedSettingsService 