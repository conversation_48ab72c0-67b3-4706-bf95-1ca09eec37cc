import api from '../../../../../services/api/httpClient';
import { getProjectId } from '../../../../../services/api/httpClient';

/**
 * 申请API令牌
 *
 * @param {Object} data 申请数据
 * @returns {Promise<Object>} 申请结果
 */
export const applyForToken = async (data) => {
  try {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    const response = await api.post(`/project/${projectId}/plugin/sapi/token-applications`, data);
    return response;
  } catch (error) {
    console.error('申请API令牌失败:', error);
    throw error;
  }
};

/**
 * 获取令牌列表
 *
 * @param {Object} params 查询参数
 * @returns {Promise<Object>} 令牌列表
 */
export const getTokenList = async (params = {}) => {
  try {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    const response = await api.get(`/project/${projectId}/plugin/sapi/tokens`, { params });
    return response;
  } catch (error) {
    console.error('获取令牌列表失败:', error);
    throw error;
  }
};

/**
 * 更新令牌状态
 *
 * @param {string} tokenId 令牌ID
 * @param {Object} data 更新数据
 * @returns {Promise<Object>} 更新结果
 */
export const updateTokenStatus = async (tokenId, data) => {
  try {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    const response = await api.put(`/project/${projectId}/plugin/sapi/tokens/${tokenId}/status`, data);
    return response;
  } catch (error) {
    console.error('更新令牌状态失败:', error);
    throw error;
  }
};

/**
 * 获取令牌详情
 *
 * @param {string} tokenId 令牌ID
 * @returns {Promise<Object>} 令牌详情
 */
export const getTokenDetail = async (tokenId) => {
  try {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    const response = await api.get(`/project/${projectId}/plugin/sapi/tokens/${tokenId}`);
    return response;
  } catch (error) {
    console.error('获取令牌详情失败:', error);
    throw error;
  }
};

/**
 * 删除令牌
 *
 * @param {string} tokenId 令牌ID
 * @returns {Promise<Object>} 删除结果
 */
export const deleteToken = async (tokenId) => {
  try {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    const response = await api.delete(`/project/${projectId}/plugin/sapi/tokens/${tokenId}`);
    return response;
  } catch (error) {
    console.error('删除令牌失败:', error);
    throw error;
  }
};

/**
 * 获取令牌申请列表
 *
 * @param {Object} params 查询参数
 * @returns {Promise<Object>} 申请列表
 */
export const getTokenApplications = async (params = {}) => {
  try {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    const response = await api.get(`/project/${projectId}/plugin/sapi/token-applications`, { params });
    return response;
  } catch (error) {
    console.error('获取令牌申请列表失败:', error);
    throw error;
  }
};

/**
 * 审批令牌申请
 *
 * @param {string} applicationId 申请ID
 * @param {Object} data 审批数据
 * @returns {Promise<Object>} 审批结果
 */
export const approveTokenApplication = async (applicationId, data) => {
  try {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    const response = await api.post(`/project/${projectId}/plugin/sapi/token-applications/${applicationId}/approve`, data);
    return response;
  } catch (error) {
    console.error('审批令牌申请失败:', error);
    throw error;
  }
};

/**
 * 拒绝令牌申请
 *
 * @param {string} applicationId 申请ID
 * @param {Object} data 拒绝数据
 * @returns {Promise<Object>} 拒绝结果
 */
export const rejectTokenApplication = async (applicationId, data) => {
  try {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    const response = await api.post(`/project/${projectId}/plugin/sapi/token-applications/${applicationId}/reject`, data);
    return response;
  } catch (error) {
    console.error('拒绝令牌申请失败:', error);
    throw error;
  }
};

/**
 * 创建API令牌
 *
 * @param {Object} data 令牌数据
 * @returns {Promise<Object>} 创建结果
 */
export const createToken = async (data) => {
  try {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    const response = await api.post(`/project/${projectId}/plugin/sapi/tokens`, data);
    return response;
  } catch (error) {
    console.error('创建API令牌失败:', error);
    throw error;
  }
};

/**
 * 获取令牌申请详情
 *
 * @param {string} applicationId 申请ID
 * @returns {Promise<Object>} 申请详情
 */
export const getTokenApplicationDetail = async (applicationId) => {
  try {
    const projectId = getProjectId();
    if (!projectId) {
      throw new Error('未指定项目ID');
    }
    const response = await api.get(`/project/${projectId}/plugin/sapi/token-applications/${applicationId}`);
    return response;
  } catch (error) {
    console.error('获取令牌申请详情失败:', error);
    throw error;
  }
};
