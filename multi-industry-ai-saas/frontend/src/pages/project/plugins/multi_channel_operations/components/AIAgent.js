import React, { useState, useEffect, useRef } from 'react';
import {
  Card,
  Input,
  Button,
  Space,
  List,
  Avatar,
  Typography,
  Tag,
  Divider,
  Row,
  Col,
  Spin,
  Empty,
  message,
  Tooltip,
  Badge,
  Modal,
  Collapse
} from 'antd';
import {
  SendOutlined,
  RobotOutlined,
  UserOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  InfoCircleOutlined,
  BulbOutlined,
  HistoryOutlined
} from '@ant-design/icons';
import { useParams } from 'react-router-dom';
import multiChannelService from '../services/multiChannelService';

const { Title, Text, Paragraph } = Typography;
const { TextArea } = Input;
const { Panel } = Collapse;

const AIAgent = () => {
  const { projectId } = useParams();
  const [loading, setLoading] = useState(false);
  const [messages, setMessages] = useState([]);
  const [inputValue, setInputValue] = useState('');
  const [conversationId, setConversationId] = useState(null);
  const [capabilities, setCapabilities] = useState(null);
  const [taskHistory, setTaskHistory] = useState([]);
  const [capabilitiesModalVisible, setCapabilitiesModalVisible] = useState(false);
  const messagesEndRef = useRef(null);

  useEffect(() => {
    loadCapabilities();
    loadTaskHistory();
    // 初始化对话
    addMessage('assistant', '👋 您好！我是全渠道运营AI助手，可以帮您处理商品同步、价格管理、图片生成等任务。请告诉我您需要什么帮助！');
  }, []);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const loadCapabilities = async () => {
    try {
      const response = await multiChannelService.getAICapabilities(projectId);
      if (response.success) {
        setCapabilities(response.data);
      }
    } catch (error) {
      console.error('获取AI能力失败:', error);
    }
  };

  const loadTaskHistory = async () => {
    try {
      const response = await multiChannelService.getAITasks(projectId, {
        limit: 10
      });
      setTaskHistory(response || []);
    } catch (error) {
      console.error('获取任务历史失败:', error);
    }
  };

  const addMessage = (role, content, actions = [], data = {}) => {
    const message = {
      id: Date.now(),
      role,
      content,
      actions,
      data,
      timestamp: new Date().toLocaleTimeString()
    };
    setMessages(prev => [...prev, message]);
  };

  const handleSendMessage = async () => {
    if (!inputValue.trim()) return;

    const userMessage = inputValue.trim();
    setInputValue('');

    // 添加用户消息
    addMessage('user', userMessage);

    try {
      setLoading(true);

      const response = await multiChannelService.chatWithAI(projectId, {
        message: userMessage,
        conversation_id: conversationId
      });

      if (response.success) {
        // 设置对话ID
        if (!conversationId && response.task_id) {
          setConversationId(response.task_id);
        }

        // 添加AI回复
        addMessage('assistant', response.response, response.actions, response.data);

        // 刷新任务历史
        loadTaskHistory();
      } else {
        addMessage('assistant', '抱歉，处理您的请求时出现了问题，请稍后重试。');
      }
    } catch (error) {
      console.error('发送消息失败:', error);
      addMessage('assistant', '网络连接异常，请检查网络后重试。');
    } finally {
      setLoading(false);
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const getActionIcon = (action) => {
    const iconMap = {
      'product_sync': <CheckCircleOutlined style={{ color: '#52c41a' }} />,
      'price_update': <InfoCircleOutlined style={{ color: '#1890ff' }} />,
      'image_generation': <BulbOutlined style={{ color: '#faad14' }} />,
      'channel_analysis': <HistoryOutlined style={{ color: '#722ed1' }} />,
      'automation_setup': <ExclamationCircleOutlined style={{ color: '#f5222d' }} />
    };
    return iconMap[action] || <InfoCircleOutlined />;
  };

  const getActionText = (action) => {
    const textMap = {
      'product_sync': '商品同步',
      'price_update': '价格更新',
      'image_generation': '图片生成',
      'channel_analysis': '渠道分析',
      'automation_setup': '自动化设置',
      'general_help': '帮助说明'
    };
    return textMap[action] || action;
  };

  const getTaskStatusTag = (status) => {
    const statusMap = {
      'pending': <Tag color="default">等待中</Tag>,
      'processing': <Tag color="processing">处理中</Tag>,
      'completed': <Tag color="success">已完成</Tag>,
      'failed': <Tag color="error">失败</Tag>
    };
    return statusMap[status] || <Tag>{status}</Tag>;
  };

  const renderMessage = (msg) => {
    const isUser = msg.role === 'user';

    return (
      <div
        key={msg.id}
        style={{
          display: 'flex',
          justifyContent: isUser ? 'flex-end' : 'flex-start',
          marginBottom: 16
        }}
      >
        <div
          style={{
            maxWidth: '70%',
            display: 'flex',
            flexDirection: isUser ? 'row-reverse' : 'row',
            alignItems: 'flex-start'
          }}
        >
          <Avatar
            icon={isUser ? <UserOutlined /> : <RobotOutlined />}
            style={{
              backgroundColor: isUser ? '#1890ff' : '#52c41a',
              margin: isUser ? '0 0 0 8px' : '0 8px 0 0'
            }}
          />
          <div
            style={{
              backgroundColor: isUser ? '#1890ff' : '#f6f6f6',
              color: isUser ? 'white' : 'black',
              padding: '12px 16px',
              borderRadius: '12px',
              position: 'relative'
            }}
          >
            <div style={{ whiteSpace: 'pre-wrap' }}>{msg.content}</div>

            {/* 显示执行的动作 */}
            {msg.actions && msg.actions.length > 0 && (
              <div style={{ marginTop: 8 }}>
                <Space wrap>
                  {msg.actions.map((action, index) => (
                    <Tag
                      key={index}
                      icon={getActionIcon(action)}
                      color={isUser ? 'blue' : 'default'}
                    >
                      {getActionText(action)}
                    </Tag>
                  ))}
                </Space>
              </div>
            )}

            <div
              style={{
                fontSize: '12px',
                opacity: 0.7,
                marginTop: 4,
                textAlign: isUser ? 'right' : 'left'
              }}
            >
              {msg.timestamp}
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div style={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
      <Row gutter={16} style={{ height: '100%' }}>
        {/* 主对话区域 */}
        <Col span={16}>
          <Card
            title={
              <Space>
                <RobotOutlined />
                <span>AI智能助手</span>
                <Badge count={messages.filter(m => m.role === 'assistant').length} />
              </Space>
            }
            extra={
              <Space>
                <Tooltip title="查看AI能力">
                  <Button
                    type="text"
                    icon={<InfoCircleOutlined />}
                    onClick={() => setCapabilitiesModalVisible(true)}
                  >
                    能力说明
                  </Button>
                </Tooltip>
              </Space>
            }
            style={{ height: '100%', display: 'flex', flexDirection: 'column' }}
            bodyStyle={{ flex: 1, display: 'flex', flexDirection: 'column', padding: 0 }}
          >
            {/* 消息列表 */}
            <div
              style={{
                flex: 1,
                overflowY: 'auto',
                padding: '16px',
                backgroundColor: '#fafafa'
              }}
            >
              {messages.map(renderMessage)}
              {loading && (
                <div style={{ textAlign: 'center', padding: '16px' }}>
                  <Spin size="small" />
                  <Text style={{ marginLeft: 8 }}>AI正在思考中...</Text>
                </div>
              )}
              <div ref={messagesEndRef} />
            </div>

            {/* 输入区域 */}
            <div style={{ padding: '16px', borderTop: '1px solid #f0f0f0' }}>
              <Space.Compact style={{ width: '100%' }}>
                <TextArea
                  value={inputValue}
                  onChange={(e) => setInputValue(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="输入您的需求，例如：同步商品A到美团、生成商品图片、分析销售数据..."
                  autoSize={{ minRows: 1, maxRows: 4 }}
                  disabled={loading}
                />
                <Button
                  type="primary"
                  icon={<SendOutlined />}
                  onClick={handleSendMessage}
                  loading={loading}
                  disabled={!inputValue.trim()}
                >
                  发送
                </Button>
              </Space.Compact>
            </div>
          </Card>
        </Col>

        {/* 侧边栏 */}
        <Col span={8}>
          <Card
            title="任务历史"
            size="small"
            style={{ height: '100%' }}
            bodyStyle={{ padding: '8px' }}
          >
            {taskHistory.length > 0 ? (
              <List
                size="small"
                dataSource={taskHistory}
                renderItem={(task) => (
                  <List.Item style={{ padding: '8px 0' }}>
                    <div style={{ width: '100%' }}>
                      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                        <Text strong style={{ fontSize: '12px' }}>{task.task_name}</Text>
                        {getTaskStatusTag(task.status)}
                      </div>
                      <div style={{ marginTop: 4 }}>
                        <Text type="secondary" style={{ fontSize: '11px' }}>
                          {task.user_input?.substring(0, 50)}...
                        </Text>
                      </div>
                      <div style={{ marginTop: 4 }}>
                        <Text type="secondary" style={{ fontSize: '10px' }}>
                          {new Date(task.created_at).toLocaleString()}
                        </Text>
                      </div>
                    </div>
                  </List.Item>
                )}
              />
            ) : (
              <Empty
                image={Empty.PRESENTED_IMAGE_SIMPLE}
                description="暂无任务历史"
                style={{ margin: '20px 0' }}
              />
            )}
          </Card>
        </Col>
      </Row>

      {/* AI能力说明模态框 */}
      <Modal
        title="AI智能助手能力说明"
        open={capabilitiesModalVisible}
        onCancel={() => setCapabilitiesModalVisible(false)}
        footer={null}
        width={800}
      >
        {capabilities && (
          <Collapse defaultActiveKey={['tasks']}>
            <Panel header="支持的任务类型" key="tasks">
              <List
                dataSource={capabilities.supported_tasks}
                renderItem={(task) => (
                  <List.Item>
                    <List.Item.Meta
                      avatar={getActionIcon(task.type)}
                      title={task.name}
                      description={
                        <div>
                          <Paragraph>{task.description}</Paragraph>
                          <div>
                            <Text strong>示例：</Text>
                            <ul style={{ marginTop: 4 }}>
                              {task.examples.map((example, index) => (
                                <li key={index}>
                                  <Text code>{example}</Text>
                                </li>
                              ))}
                            </ul>
                          </div>
                        </div>
                      }
                    />
                  </List.Item>
                )}
              />
            </Panel>

            <Panel header="支持的平台" key="platforms">
              <Row gutter={[16, 16]}>
                {capabilities.supported_platforms?.map((platform) => (
                  <Col span={8} key={platform.code}>
                    <Card size="small">
                      <div style={{ textAlign: 'center' }}>
                        <Title level={5}>{platform.name}</Title>
                        <Space wrap>
                          {platform.features.map((feature) => (
                            <Tag key={feature} size="small">{feature}</Tag>
                          ))}
                        </Space>
                      </div>
                    </Card>
                  </Col>
                ))}
              </Row>
            </Panel>
          </Collapse>
        )}
      </Modal>
    </div>
  );
};

export default AIAgent;
