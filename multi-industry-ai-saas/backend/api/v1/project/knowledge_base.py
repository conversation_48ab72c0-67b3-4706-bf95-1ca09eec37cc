#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
项目级知识库 API
"""

import logging
import uuid
from typing import Optional, List
from fastapi import APIRouter, Depends, HTTPException, Query, Path, status, UploadFile, File, Form
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func
from datetime import datetime

from db.database import get_db
from models.project import Project
from models.user import User
from models.knowledge_base import KnowledgeDocument, KnowledgeCategory
from schemas.knowledge_base import (
    KnowledgeDocumentCreate,
    KnowledgeDocumentUpdate,
    KnowledgeDocumentResponse,
    KnowledgeDocumentListResponse,
    KnowledgeCategoryResponse,
    KnowledgeCategoryListResponse
)
from api.deps import get_current_user, get_current_project
from services.knowledge_base import KnowledgeBaseService
from services.knowledge_integration import KnowledgeIntegrationService

logger = logging.getLogger(__name__)
router = APIRouter()

@router.get("", response_model=KnowledgeDocumentListResponse)
async def get_documents(
    skip: int = Query(0, ge=0),
    limit: int = Query(10, ge=1, le=100),
    category_id: Optional[uuid.UUID] = None,
    store_id: Optional[uuid.UUID] = None,
    search: Optional[str] = None,
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取知识库文档列表
    """
    try:
        documents, total = await KnowledgeBaseService.get_documents(
            db=db,
            project_id=project.id,
            skip=skip,
            limit=limit,
            category_id=category_id,
            store_id=store_id,
            search=search
        )

        return {
            "items": documents,
            "total": total
        }
    except Exception as e:
        logger.error(f"获取知识库文档列表失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取知识库文档列表失败: {str(e)}"
        )

@router.get("/categories", response_model=KnowledgeCategoryListResponse)
async def get_categories(
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取知识库分类列表
    """
    try:
        categories = await KnowledgeBaseService.get_categories(
            db=db,
            project_id=project.id
        )

        return {
            "items": categories,
            "total": len(categories)
        }
    except Exception as e:
        logger.error(f"获取知识库分类列表失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取知识库分类列表失败: {str(e)}"
        )

@router.post("/categories", response_model=KnowledgeCategoryResponse, status_code=status.HTTP_201_CREATED)
async def create_category(
    category_data: dict,
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    创建知识库分类
    """
    try:
        # 检查必填字段
        if "name" not in category_data or not category_data["name"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="分类名称不能为空"
            )

        # 创建分类
        category = await KnowledgeBaseService.create_category(
            db=db,
            project_id=project.id,
            category_data={
                "name": category_data["name"],
                "description": category_data.get("description", ""),
                "parent_id": category_data.get("parent_id")
            },
            created_by=current_user.id
        )

        return category
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"创建知识库分类失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建知识库分类失败: {str(e)}"
        )

@router.get("/ai-knowledge-bases", response_model=List[dict])
async def get_available_ai_knowledge_bases(
    document_id: Optional[uuid.UUID] = Query(None, description="文档ID，可选"),
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取项目可用的 AI 知识库列表
    如果提供了 document_id，则同时返回该文档已同步到的 AI 知识库
    """
    try:
        # 获取项目可用的 AI 知识库列表
        knowledge_bases = await KnowledgeIntegrationService.get_ai_knowledge_bases_for_project(
            db=db,
            project_id=project.id
        )

        # 如果提供了文档 ID，则获取该文档已同步到的 AI 知识库
        if document_id:
            # 检查文档是否存在
            document = await KnowledgeBaseService.get_document_by_id(
                db=db,
                project_id=project.id,
                document_id=document_id
            )

            if not document:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="文档不存在"
                )

            # 获取文档已同步到的 AI 知识库列表
            synced_knowledge_bases = await KnowledgeIntegrationService.get_document_ai_knowledge_bases(
                db=db,
                document_id=document_id
            )

            # 标记已同步的知识库
            for kb in knowledge_bases:
                kb["synced"] = any(synced_kb["id"] == kb["id"] for synced_kb in synced_knowledge_bases)

        return knowledge_bases
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"获取项目可用的 AI 知识库列表失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取项目可用的 AI 知识库列表失败: {str(e)}"
        )

@router.post("", response_model=KnowledgeDocumentResponse, status_code=status.HTTP_201_CREATED)
async def create_document(
    document_data: KnowledgeDocumentCreate,
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    创建知识库文档
    """
    try:
        document = await KnowledgeBaseService.create_document(
            db=db,
            project_id=project.id,
            document_data=document_data,
            created_by=current_user.id
        )

        return document
    except Exception as e:
        logger.error(f"创建知识库文档失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建知识库文档失败: {str(e)}"
        )

@router.get("/{document_id}", response_model=KnowledgeDocumentResponse)
async def get_document(
    document_id: uuid.UUID = Path(...),
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取知识库文档详情
    """
    try:
        document = await KnowledgeBaseService.get_document_by_id(
            db=db,
            project_id=project.id,
            document_id=document_id
        )

        if not document:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="文档不存在"
            )

        return document
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"获取知识库文档详情失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取知识库文档详情失败: {str(e)}"
        )

@router.put("/{document_id}", response_model=KnowledgeDocumentResponse)
async def update_document(
    document_data: KnowledgeDocumentUpdate,
    document_id: uuid.UUID = Path(...),
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    更新知识库文档
    """
    try:
        # 检查文档是否存在
        document = await KnowledgeBaseService.get_document_by_id(
            db=db,
            project_id=project.id,
            document_id=document_id
        )

        if not document:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="文档不存在"
            )

        # 更新文档
        updated_document = await KnowledgeBaseService.update_document(
            db=db,
            project_id=project.id,
            document_id=document_id,
            document_data=document_data,
            updated_by=current_user.id
        )

        return updated_document
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"更新知识库文档失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新知识库文档失败: {str(e)}"
        )

@router.post("/{document_id}/sync-to-ai", response_model=dict)
async def sync_document_to_ai_knowledge_base(
    document_id: uuid.UUID = Path(...),
    knowledge_base_id: uuid.UUID = Query(..., description="AI 知识库 ID"),
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    将知识库文档同步到 AI 知识库
    """
    try:
        # 检查文档是否存在
        document = await KnowledgeBaseService.get_document_by_id(
            db=db,
            project_id=project.id,
            document_id=document_id
        )

        if not document:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="文档不存在"
            )

        # 同步文档到 AI 知识库
        ai_document = await KnowledgeIntegrationService.sync_document_to_ai_knowledge_base(
            db=db,
            document_id=document_id,
            knowledge_base_id=knowledge_base_id,
            user_id=current_user.id
        )

        if not ai_document:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="同步文档到 AI 知识库失败"
            )

        return {
            "success": True,
            "message": "文档已成功同步到 AI 知识库",
            "ai_document_id": str(ai_document.id) if hasattr(ai_document, 'id') else None
        }
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"同步文档到 AI 知识库失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"同步文档到 AI 知识库失败: {str(e)}"
        )

@router.get("/{document_id}/ai-knowledge-bases", response_model=List[dict])
async def get_document_ai_knowledge_bases(
    document_id: uuid.UUID = Path(...),
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    获取文档已同步到的 AI 知识库列表
    """
    try:
        # 检查文档是否存在
        document = await KnowledgeBaseService.get_document_by_id(
            db=db,
            project_id=project.id,
            document_id=document_id
        )

        if not document:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="文档不存在"
            )

        # 获取文档已同步到的 AI 知识库列表
        knowledge_bases = await KnowledgeIntegrationService.get_document_ai_knowledge_bases(
            db=db,
            document_id=document_id
        )

        return knowledge_bases
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"获取文档已同步到的 AI 知识库列表失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取文档已同步到的 AI 知识库列表失败: {str(e)}"
        )

@router.delete("/{document_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_document(
    document_id: uuid.UUID = Path(...),
    project: Project = Depends(get_current_project),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    删除知识库文档
    """
    try:
        # 检查文档是否存在
        document = await KnowledgeBaseService.get_document_by_id(
            db=db,
            project_id=project.id,
            document_id=document_id
        )

        if not document:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="文档不存在"
            )

        # 删除文档
        await KnowledgeBaseService.delete_document(
            db=db,
            project_id=project.id,
            document_id=document_id
        )

        return None
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"删除知识库文档失败: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除知识库文档失败: {str(e)}"
        )
