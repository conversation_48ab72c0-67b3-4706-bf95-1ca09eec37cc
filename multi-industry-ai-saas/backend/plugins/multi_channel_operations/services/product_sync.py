#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
全渠道运营助手 - 商品同步服务
"""

import logging
import asyncio
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, and_

from ..models.product_mapping import MultiChannelProductMapping, MultiChannelBatchOperation
from ..models.operation_log import MultiChannelOperationLog
from .platform_adapter import PlatformAdapterManager

logger = logging.getLogger(__name__)


class ProductSyncService:
    """商品同步服务"""
    
    def __init__(self):
        self.platform_manager = PlatformAdapterManager()
    
    async def sync_product_to_channel(self, 
                                      db: AsyncSession,
                                      project_id: str,
                                      product_id: str,
                                      channel_id: str,
                                      product_data: Dict[str, Any],
                                      channel_config: Dict[str, Any],
                                      user_id: Optional[str] = None) -> Dict[str, Any]:
        """同步单个商品到指定渠道"""
        try:
            # 获取平台适配器
            platform_code = channel_config.get('platform_code')
            adapter = self.platform_manager.get_adapter(platform_code, channel_config)
            
            if not adapter:
                return {
                    'success': False,
                    'error': f'不支持的平台: {platform_code}'
                }
            
            # 查找现有映射
            stmt = select(MultiChannelProductMapping).where(
                and_(
                    MultiChannelProductMapping.project_id == project_id,
                    MultiChannelProductMapping.product_id == product_id,
                    MultiChannelProductMapping.channel_id == channel_id
                )
            )
            result = await db.execute(stmt)
            mapping = result.scalar_one_or_none()
            
            # 更新同步状态为进行中
            if mapping:
                mapping.sync_status = "syncing"
                mapping.sync_error = None
            else:
                mapping = MultiChannelProductMapping(
                    project_id=project_id,
                    product_id=product_id,
                    channel_id=channel_id,
                    sync_status="syncing"
                )
                db.add(mapping)
            
            await db.commit()
            
            # 执行同步
            if mapping.external_product_id:
                # 更新现有商品
                result = await adapter.update_product(mapping.external_product_id, product_data)
            else:
                # 创建新商品
                result = await adapter.create_product(product_data)
            
            # 更新映射记录
            if result.get('success'):
                mapping.sync_status = "success"
                mapping.last_sync_at = datetime.utcnow()
                mapping.sync_error = None
                
                if result.get('external_id'):
                    mapping.external_product_id = result['external_id']
                
                # 更新渠道特定数据
                mapping.channel_product_data = result.get('data', {})
                
                # 记录操作日志
                await self._log_operation(
                    db, project_id, "product_sync", "success",
                    f"商品同步成功: {product_data.get('name', 'Unknown')}",
                    product_id, channel_id, user_id
                )
                
            else:
                mapping.sync_status = "failed"
                mapping.sync_error = result.get('error', '未知错误')
                
                # 记录操作日志
                await self._log_operation(
                    db, project_id, "product_sync", "failed",
                    f"商品同步失败: {result.get('error', '未知错误')}",
                    product_id, channel_id, user_id
                )
            
            await db.commit()
            
            return {
                'success': result.get('success', False),
                'external_id': mapping.external_product_id,
                'error': mapping.sync_error
            }
            
        except Exception as e:
            logger.error(f"商品同步异常: {e}")
            
            # 更新失败状态
            if mapping:
                mapping.sync_status = "failed"
                mapping.sync_error = f"同步异常: {str(e)}"
                await db.commit()
            
            return {
                'success': False,
                'error': f'同步异常: {str(e)}'
            }
    
    async def batch_sync_products(self,
                                  db: AsyncSession,
                                  project_id: str,
                                  product_ids: List[str],
                                  channel_ids: List[str],
                                  user_id: Optional[str] = None) -> str:
        """批量同步商品到多个渠道"""
        try:
            # 创建批量操作记录
            batch_operation = MultiChannelBatchOperation(
                project_id=project_id,
                operation_type="sync",
                operation_name="批量商品同步",
                target_channels=channel_ids,
                target_products=product_ids,
                status="running",
                total_items=len(product_ids) * len(channel_ids),
                started_at=datetime.utcnow()
            )
            db.add(batch_operation)
            await db.commit()
            
            # 异步执行批量同步
            asyncio.create_task(self._execute_batch_sync(
                db, batch_operation.id, project_id, product_ids, channel_ids, user_id
            ))
            
            return str(batch_operation.id)
            
        except Exception as e:
            logger.error(f"批量同步启动失败: {e}")
            raise
    
    async def _execute_batch_sync(self,
                                  db: AsyncSession,
                                  batch_id: str,
                                  project_id: str,
                                  product_ids: List[str],
                                  channel_ids: List[str],
                                  user_id: Optional[str] = None):
        """执行批量同步"""
        try:
            completed_items = 0
            failed_items = 0
            results = []
            
            for product_id in product_ids:
                for channel_id in channel_ids:
                    try:
                        # 这里需要获取商品数据和渠道配置
                        # 实际实现中需要从数据库查询
                        product_data = await self._get_product_data(db, product_id)
                        channel_config = await self._get_channel_config(db, channel_id)
                        
                        result = await self.sync_product_to_channel(
                            db, project_id, product_id, channel_id,
                            product_data, channel_config, user_id
                        )
                        
                        if result['success']:
                            completed_items += 1
                        else:
                            failed_items += 1
                        
                        results.append({
                            'product_id': product_id,
                            'channel_id': channel_id,
                            'result': result
                        })
                        
                    except Exception as e:
                        failed_items += 1
                        results.append({
                            'product_id': product_id,
                            'channel_id': channel_id,
                            'result': {'success': False, 'error': str(e)}
                        })
                    
                    # 更新进度
                    progress = int((completed_items + failed_items) / (len(product_ids) * len(channel_ids)) * 100)
                    await self._update_batch_progress(db, batch_id, progress, completed_items, failed_items)
            
            # 完成批量操作
            await self._complete_batch_operation(db, batch_id, results)
            
        except Exception as e:
            logger.error(f"批量同步执行失败: {e}")
            await self._fail_batch_operation(db, batch_id, str(e))
    
    async def sync_multiple_channels(self,
                                     db: AsyncSession,
                                     project_id: str,
                                     product_id: str,
                                     channel_ids: List[str],
                                     user_id: Optional[str] = None) -> Dict[str, Any]:
        """一品多渠道同步优化"""
        try:
            # 获取商品数据
            product_data = await self._get_product_data(db, product_id)
            
            # 并发同步到多个渠道
            tasks = []
            for channel_id in channel_ids:
                channel_config = await self._get_channel_config(db, channel_id)
                task = self.sync_product_to_channel(
                    db, project_id, product_id, channel_id,
                    product_data, channel_config, user_id
                )
                tasks.append((channel_id, task))
            
            # 等待所有任务完成
            results = {}
            for channel_id, task in tasks:
                try:
                    result = await task
                    results[channel_id] = result
                except Exception as e:
                    results[channel_id] = {
                        'success': False,
                        'error': f'同步异常: {str(e)}'
                    }
            
            # 统计结果
            success_count = sum(1 for r in results.values() if r.get('success'))
            total_count = len(results)
            
            return {
                'success': success_count > 0,
                'total_channels': total_count,
                'success_channels': success_count,
                'failed_channels': total_count - success_count,
                'results': results
            }
            
        except Exception as e:
            logger.error(f"多渠道同步失败: {e}")
            return {
                'success': False,
                'error': f'多渠道同步异常: {str(e)}'
            }
    
    async def _get_product_data(self, db: AsyncSession, product_id: str) -> Dict[str, Any]:
        """获取商品数据"""
        # 这里需要实现从商品库获取商品数据的逻辑
        # 暂时返回模拟数据
        return {
            'name': '示例商品',
            'description': '商品描述',
            'price': 99.99,
            'stock': 100,
            'images': ['https://example.com/image.jpg'],
            'specifications': []
        }
    
    async def _get_channel_config(self, db: AsyncSession, channel_id: str) -> Dict[str, Any]:
        """获取渠道配置"""
        # 这里需要实现从渠道配置获取平台信息的逻辑
        # 暂时返回模拟数据
        return {
            'platform_code': 'meituan',
            'api_credentials': {
                'app_id': 'test_app_id',
                'app_secret': 'test_app_secret',
                'access_token': 'test_access_token'
            }
        }
    
    async def _log_operation(self, db: AsyncSession, project_id: str, operation_type: str,
                             status: str, description: str, product_id: str,
                             channel_id: str, user_id: Optional[str] = None):
        """记录操作日志"""
        log = MultiChannelOperationLog(
            project_id=project_id,
            operation_type=operation_type,
            operation_name="商品同步",
            operation_description=description,
            target_type="product",
            target_id=product_id,
            status=status,
            user_id=user_id,
            channel_id=channel_id,
            operation_source="manual" if user_id else "auto"
        )
        db.add(log)
    
    async def _update_batch_progress(self, db: AsyncSession, batch_id: str,
                                     progress: int, completed: int, failed: int):
        """更新批量操作进度"""
        stmt = update(MultiChannelBatchOperation).where(
            MultiChannelBatchOperation.id == batch_id
        ).values(
            progress=progress,
            completed_items=completed,
            failed_items=failed
        )
        await db.execute(stmt)
        await db.commit()
    
    async def _complete_batch_operation(self, db: AsyncSession, batch_id: str, results: List[Dict]):
        """完成批量操作"""
        stmt = update(MultiChannelBatchOperation).where(
            MultiChannelBatchOperation.id == batch_id
        ).values(
            status="completed",
            progress=100,
            completed_at=datetime.utcnow(),
            result_summary={'total_results': len(results)}
        )
        await db.execute(stmt)
        await db.commit()
    
    async def _fail_batch_operation(self, db: AsyncSession, batch_id: str, error: str):
        """标记批量操作失败"""
        stmt = update(MultiChannelBatchOperation).where(
            MultiChannelBatchOperation.id == batch_id
        ).values(
            status="failed",
            completed_at=datetime.utcnow(),
            error_details=[{'error': error}]
        )
        await db.execute(stmt)
        await db.commit()
