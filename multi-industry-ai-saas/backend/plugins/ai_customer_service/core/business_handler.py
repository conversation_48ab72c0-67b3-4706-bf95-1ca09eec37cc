#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
客服业务处理器

处理各种客服业务场景，包括产品咨询、投诉处理、工单管理等。
"""

import logging
import uuid
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_

from services.task import TaskService
from services.notification_service import NotificationService
from schemas.task import TaskCreate
from schemas.notification import NotificationCreate
from ..models.customer_service import (
    CustomerServiceTicket, 
    TicketStatus, 
    TicketPriority,
    CustomerServiceSession
)

logger = logging.getLogger(__name__)

class BusinessHandler:
    """客服业务处理器"""
    
    def __init__(self):
        self.escalation_keywords = [
            "投诉", "不满", "退款", "赔偿", "经理", "主管", 
            "人工", "转人工", "客服", "严重", "紧急"
        ]
        self.priority_keywords = {
            "urgent": ["紧急", "急", "马上", "立即", "严重"],
            "high": ["重要", "尽快", "优先", "问题"],
            "medium": ["咨询", "了解", "请问"],
            "low": ["建议", "意见", "反馈"]
        }
        
    async def handle_customer_inquiry(
        self,
        db: AsyncSession,
        session_id: str,
        message: str,
        intent: str,
        project_id: uuid.UUID,
        user_id: Optional[uuid.UUID] = None,
        platform_user_id: str = "",
        platform_type: str = ""
    ) -> Dict[str, Any]:
        """
        处理客户咨询
        
        Args:
            db: 数据库会话
            session_id: 会话ID
            message: 用户消息
            intent: 消息意图
            project_id: 项目ID
            user_id: 用户ID
            platform_user_id: 平台用户ID
            platform_type: 平台类型
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        try:
            # 分析是否需要人工介入
            needs_escalation = await self._check_escalation_needed(message, intent)
            
            # 确定优先级
            priority = await self._determine_priority(message, intent)
            
            # 如果需要人工介入，创建工单
            ticket_id = None
            if needs_escalation:
                ticket_id = await self._create_support_ticket(
                    db=db,
                    session_id=session_id,
                    message=message,
                    intent=intent,
                    priority=priority,
                    project_id=project_id,
                    platform_user_id=platform_user_id,
                    platform_type=platform_type
                )
                
            # 生成处理建议
            suggestions = await self._generate_handling_suggestions(
                message, intent, priority
            )
            
            return {
                "needs_escalation": needs_escalation,
                "priority": priority,
                "ticket_id": str(ticket_id) if ticket_id else None,
                "suggestions": suggestions,
                "auto_responses": await self._get_auto_responses(intent),
                "next_actions": await self._get_next_actions(intent, needs_escalation)
            }
            
        except Exception as e:
            logger.error(f"处理客户咨询失败: {e}")
            return {
                "needs_escalation": True,
                "priority": "medium",
                "ticket_id": None,
                "suggestions": [],
                "auto_responses": ["抱歉，系统暂时繁忙，我会尽快为您处理。"],
                "next_actions": ["escalate_to_human"]
            }
            
    async def _check_escalation_needed(self, message: str, intent: str) -> bool:
        """检查是否需要人工介入"""
        try:
            # 检查关键词
            message_lower = message.lower()
            for keyword in self.escalation_keywords:
                if keyword in message_lower:
                    return True
                    
            # 检查意图
            if intent in ["complaint", "refund", "urgent_support"]:
                return True
                
            # 检查消息长度和复杂度
            if len(message) > 200:
                return True
                
            return False
            
        except Exception as e:
            logger.error(f"检查人工介入需求失败: {e}")
            return True  # 出错时默认需要人工介入
            
    async def _determine_priority(self, message: str, intent: str) -> str:
        """确定优先级"""
        try:
            message_lower = message.lower()
            
            # 检查紧急关键词
            for priority, keywords in self.priority_keywords.items():
                for keyword in keywords:
                    if keyword in message_lower:
                        return priority
                        
            # 根据意图确定优先级
            intent_priority_map = {
                "complaint": "high",
                "urgent_support": "urgent",
                "refund": "high",
                "inquiry": "medium",
                "greeting": "low"
            }
            
            return intent_priority_map.get(intent, "medium")
            
        except Exception as e:
            logger.error(f"确定优先级失败: {e}")
            return "medium"
            
    async def _create_support_ticket(
        self,
        db: AsyncSession,
        session_id: str,
        message: str,
        intent: str,
        priority: str,
        project_id: uuid.UUID,
        platform_user_id: str,
        platform_type: str
    ) -> Optional[uuid.UUID]:
        """创建支持工单"""
        try:
            # 生成工单号
            ticket_number = f"CS{datetime.now().strftime('%Y%m%d%H%M%S')}{uuid.uuid4().hex[:6].upper()}"
            
            # 根据意图生成标题
            title_map = {
                "complaint": f"客户投诉 - {platform_type}用户",
                "refund": f"退款申请 - {platform_type}用户", 
                "urgent_support": f"紧急支持 - {platform_type}用户",
                "inquiry": f"客户咨询 - {platform_type}用户"
            }
            
            title = title_map.get(intent, f"客服工单 - {platform_type}用户")
            
            # 创建工单
            ticket = CustomerServiceTicket(
                tenant_id=project_id,  # 暂时使用project_id作为tenant_id
                project_id=project_id,
                session_id=uuid.UUID(session_id) if session_id else None,
                ticket_number=ticket_number,
                title=title,
                description=message,
                category=intent,
                status=TicketStatus.OPEN,
                priority=getattr(TicketPriority, priority.upper(), TicketPriority.MEDIUM),
                customer_platform_id=platform_user_id,
                customer_name=f"{platform_type}用户",
                meta_data={
                    "platform_type": platform_type,
                    "intent": intent,
                    "auto_created": True,
                    "escalation_reason": "AI判断需要人工介入"
                }
            )
            
            db.add(ticket)
            await db.commit()
            await db.refresh(ticket)
            
            # 创建任务分配给客服团队
            await self._create_customer_service_task(
                db=db,
                ticket=ticket,
                project_id=project_id
            )
            
            # 发送通知
            await self._send_ticket_notification(
                db=db,
                ticket=ticket,
                project_id=project_id
            )
            
            logger.info(f"创建客服工单: {ticket_number}")
            
            return ticket.id
            
        except Exception as e:
            logger.error(f"创建支持工单失败: {e}")
            await db.rollback()
            return None
            
    async def _create_customer_service_task(
        self,
        db: AsyncSession,
        ticket: CustomerServiceTicket,
        project_id: uuid.UUID
    ):
        """为工单创建客服任务"""
        try:
            # 创建任务数据
            task_data = TaskCreate(
                title=f"处理客服工单: {ticket.ticket_number}",
                description=f"""
                工单详情：
                - 工单号：{ticket.ticket_number}
                - 客户：{ticket.customer_name}
                - 优先级：{ticket.priority}
                - 分类：{ticket.category}
                - 描述：{ticket.description}
                
                请及时处理客户问题并更新工单状态。
                """,
                category="customer_service",
                priority=ticket.priority,
                status="pending",
                due_date=datetime.now(),  # 根据优先级设置截止时间
                assignee_ids=[],  # 可以分配给客服团队
                attachments=[]
            )
            
            # 创建任务
            await TaskService.create_task(
                db=db,
                project_id=project_id,
                task_data=task_data,
                created_by=uuid.uuid4()  # 系统创建
            )
            
        except Exception as e:
            logger.error(f"创建客服任务失败: {e}")
            
    async def _send_ticket_notification(
        self,
        db: AsyncSession,
        ticket: CustomerServiceTicket,
        project_id: uuid.UUID
    ):
        """发送工单通知"""
        try:
            # 创建通知数据
            notification_data = NotificationCreate(
                title=f"新的客服工单: {ticket.ticket_number}",
                content=f"""
                收到新的客服工单，请及时处理：
                
                工单号：{ticket.ticket_number}
                客户：{ticket.customer_name}
                优先级：{ticket.priority}
                分类：{ticket.category}
                
                描述：{ticket.description[:100]}...
                """,
                type="customer_service",
                priority="high" if ticket.priority in ["urgent", "high"] else "medium",
                target_url=f"/customer-service/tickets/{ticket.id}"
            )
            
            # 发送给客服团队（这里需要获取客服团队成员）
            # 暂时发送给系统管理员
            await NotificationService.create_notification(
                db=db,
                user_id=uuid.uuid4(),  # 需要获取实际的客服人员ID
                project_id=project_id,
                notification_data=notification_data
            )
            
        except Exception as e:
            logger.error(f"发送工单通知失败: {e}")
            
    async def _generate_handling_suggestions(
        self,
        message: str,
        intent: str,
        priority: str
    ) -> List[str]:
        """生成处理建议"""
        try:
            suggestions = []
            
            if intent == "complaint":
                suggestions = [
                    "立即道歉并表示理解客户的困扰",
                    "详细了解问题的具体情况",
                    "提供解决方案或补偿措施",
                    "跟进处理结果并确认客户满意度"
                ]
            elif intent == "refund":
                suggestions = [
                    "核实订单信息和退款原因",
                    "说明退款政策和流程",
                    "协助客户完成退款申请",
                    "提供退款进度查询方式"
                ]
            elif intent == "inquiry":
                suggestions = [
                    "仔细理解客户的具体需求",
                    "提供准确详细的产品信息",
                    "推荐合适的产品或服务",
                    "询问是否还有其他问题"
                ]
            else:
                suggestions = [
                    "耐心倾听客户的问题",
                    "提供专业的解答和建议",
                    "确保客户问题得到解决",
                    "记录重要信息便于后续跟进"
                ]
                
            return suggestions
            
        except Exception as e:
            logger.error(f"生成处理建议失败: {e}")
            return ["请耐心处理客户问题"]
            
    async def _get_auto_responses(self, intent: str) -> List[str]:
        """获取自动回复模板"""
        try:
            response_templates = {
                "greeting": [
                    "您好！很高兴为您服务，请问有什么可以帮助您的吗？",
                    "欢迎！我是AI客服助手，有什么问题可以随时咨询我。"
                ],
                "complaint": [
                    "非常抱歉给您带来不便，我会立即为您处理这个问题。",
                    "我理解您的困扰，让我来帮您解决这个问题。"
                ],
                "inquiry": [
                    "我来为您详细介绍一下相关信息。",
                    "关于您的问题，我为您查询一下相关资料。"
                ],
                "refund": [
                    "关于退款问题，我来为您详细说明处理流程。",
                    "我会帮您核实订单信息并协助处理退款。"
                ]
            }
            
            return response_templates.get(intent, [
                "我来为您处理这个问题。",
                "让我帮助您解决这个需求。"
            ])
            
        except Exception as e:
            logger.error(f"获取自动回复失败: {e}")
            return ["我来为您处理这个问题。"]
            
    async def _get_next_actions(self, intent: str, needs_escalation: bool) -> List[str]:
        """获取下一步操作建议"""
        try:
            if needs_escalation:
                return [
                    "escalate_to_human",
                    "create_ticket", 
                    "notify_supervisor",
                    "schedule_followup"
                ]
            else:
                action_map = {
                    "greeting": ["provide_menu", "ask_how_to_help"],
                    "inquiry": ["provide_information", "offer_assistance"],
                    "complaint": ["apologize", "investigate_issue"],
                    "refund": ["verify_order", "explain_policy"]
                }
                
                return action_map.get(intent, ["provide_assistance"])
                
        except Exception as e:
            logger.error(f"获取下一步操作失败: {e}")
            return ["provide_assistance"]
            
    async def get_ticket_status(
        self,
        db: AsyncSession,
        ticket_id: uuid.UUID
    ) -> Optional[Dict[str, Any]]:
        """获取工单状态"""
        try:
            query = select(CustomerServiceTicket).where(
                CustomerServiceTicket.id == ticket_id
            )
            
            result = await db.execute(query)
            ticket = result.scalar_one_or_none()
            
            if not ticket:
                return None
                
            return {
                "id": str(ticket.id),
                "ticket_number": ticket.ticket_number,
                "title": ticket.title,
                "status": ticket.status,
                "priority": ticket.priority,
                "created_at": ticket.created_at.isoformat(),
                "updated_at": ticket.updated_at.isoformat(),
                "resolved_at": ticket.resolved_at.isoformat() if ticket.resolved_at else None
            }
            
        except Exception as e:
            logger.error(f"获取工单状态失败: {e}")
            return None
            
    async def update_ticket_status(
        self,
        db: AsyncSession,
        ticket_id: uuid.UUID,
        status: str,
        notes: Optional[str] = None
    ) -> bool:
        """更新工单状态"""
        try:
            query = select(CustomerServiceTicket).where(
                CustomerServiceTicket.id == ticket_id
            )
            
            result = await db.execute(query)
            ticket = result.scalar_one_or_none()
            
            if not ticket:
                return False
                
            ticket.status = status
            ticket.updated_at = datetime.utcnow()
            
            if status == TicketStatus.RESOLVED:
                ticket.resolved_at = datetime.utcnow()
                
            if notes:
                if not ticket.meta_data:
                    ticket.meta_data = {}
                if "notes" not in ticket.meta_data:
                    ticket.meta_data["notes"] = []
                ticket.meta_data["notes"].append({
                    "timestamp": datetime.utcnow().isoformat(),
                    "note": notes
                })
                
            await db.commit()
            
            logger.info(f"工单 {ticket.ticket_number} 状态更新为: {status}")
            
            return True
            
        except Exception as e:
            logger.error(f"更新工单状态失败: {e}")
            await db.rollback()
            return False
