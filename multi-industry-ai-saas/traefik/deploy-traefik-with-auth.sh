#!/bin/bash

# Traefik + SAPI鉴权微服务部署脚本
# 用于启动完整的Traefik路由和鉴权优化

set -e

echo "🚀 开始部署Traefik + SAPI鉴权微服务..."

# 检查Docker和Docker Compose
if ! command -v docker &> /dev/null; then
    echo "❌ Docker未安装，请先安装Docker"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose未安装，请先安装Docker Compose"
    exit 1
fi

# 设置工作目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

echo "📁 工作目录: $SCRIPT_DIR"

# 检查必要文件
REQUIRED_FILES=(
    "docker-compose.yml"
    "traefik.yml"
    "dynamic/services.yml"
    "../backend/plugins/sapi/Dockerfile.traefik-auth"
    "../backend/plugins/sapi/services/traefik_auth_service.py"
    "../backend/plugins/sapi/requirements.txt"
)

for file in "${REQUIRED_FILES[@]}"; do
    if [[ ! -f "$file" ]]; then
        echo "❌ 缺少必要文件: $file"
        exit 1
    fi
done

echo "✅ 所有必要文件检查通过"

# 检查环境变量文件
if [[ ! -f "../.env" ]]; then
    echo "⚠️  未找到.env文件，将使用默认配置"
    echo "请确保以下环境变量已设置："
    echo "  - DATABASE_URL"
    echo "  - REDIS_URL"
    echo "  - SECRET_KEY"
    echo "  - JWT_SECRET_KEY"
    echo "  - SAPI_SECRET_KEY"
fi

# 创建网络（如果不存在）
echo "🌐 检查Docker网络..."
if ! docker network ls | grep -q "docker_retail-ai-saas-network"; then
    echo "创建Docker网络: docker_retail-ai-saas-network"
    docker network create docker_retail-ai-saas-network
else
    echo "✅ Docker网络已存在"
fi

# 创建日志目录
echo "📁 创建日志目录..."
mkdir -p ../backend/plugins/sapi/logs

# 停止现有服务（如果存在）
echo "🛑 停止现有服务..."
docker-compose down --remove-orphans || true

# 构建镜像
echo "🔨 构建SAPI鉴权微服务镜像..."
docker-compose build sapi-traefik-auth

# 启动服务
echo "🚀 启动Traefik + SAPI鉴权微服务..."
docker-compose up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 15

# 健康检查
echo "🔍 检查服务健康状态..."

# 检查鉴权微服务
MAX_RETRIES=30
RETRY_COUNT=0

echo "检查SAPI鉴权微服务..."
while [[ $RETRY_COUNT -lt $MAX_RETRIES ]]; do
    if curl -f http://localhost:8002/health > /dev/null 2>&1; then
        echo "✅ SAPI鉴权微服务启动成功！"
        break
    else
        echo "⏳ 等待鉴权微服务启动... ($((RETRY_COUNT + 1))/$MAX_RETRIES)"
        sleep 2
        ((RETRY_COUNT++))
    fi
done

if [[ $RETRY_COUNT -eq $MAX_RETRIES ]]; then
    echo "❌ 鉴权微服务启动超时，请检查日志"
    docker-compose logs sapi-traefik-auth
    exit 1
fi

# 检查Traefik
RETRY_COUNT=0
echo "检查Traefik服务..."
while [[ $RETRY_COUNT -lt $MAX_RETRIES ]]; do
    if curl -f http://localhost:8090/ping > /dev/null 2>&1; then
        echo "✅ Traefik服务启动成功！"
        break
    else
        echo "⏳ 等待Traefik启动... ($((RETRY_COUNT + 1))/$MAX_RETRIES)"
        sleep 2
        ((RETRY_COUNT++))
    fi
done

if [[ $RETRY_COUNT -eq $MAX_RETRIES ]]; then
    echo "❌ Traefik启动超时，请检查日志"
    docker-compose logs traefik
    exit 1
fi

# 显示服务状态
echo "📊 服务状态:"
docker-compose ps

# 测试MCP服务器直接路由
echo "🧪 测试MCP服务器直接路由..."
MCP_SERVER_ID="952cf866-77e2-4411-87ee-49357eeb2a62"
MCP_URL="http://mcp-${MCP_SERVER_ID}.localhost"

echo "测试MCP服务器健康检查: ${MCP_URL}/health"
if curl -f -H "Host: mcp-${MCP_SERVER_ID}.localhost" http://localhost/health > /dev/null 2>&1; then
    echo "✅ MCP服务器直接路由测试成功！"
else
    echo "⚠️  MCP服务器直接路由测试失败，可能需要等待MCP服务器完全启动"
fi

# 显示服务信息
echo ""
echo "🎉 部署完成！"
echo ""
echo "📋 服务信息:"
echo "  - Traefik仪表板: http://localhost:8090"
echo "  - 鉴权服务地址: http://localhost:8002"
echo "  - 鉴权健康检查: http://localhost:8002/health"
echo "  - 鉴权服务指标: http://localhost:8002/metrics"
echo "  - MCP直接路由示例: http://mcp-${MCP_SERVER_ID}.localhost"
echo ""
echo "🔧 管理命令:"
echo "  - 查看所有日志: docker-compose logs -f"
echo "  - 查看鉴权日志: docker-compose logs -f sapi-traefik-auth"
echo "  - 查看Traefik日志: docker-compose logs -f traefik"
echo "  - 停止服务: docker-compose down"
echo "  - 重启服务: docker-compose restart"
echo ""
echo "📖 使用说明:"
echo "  1. MCP服务器现在支持Traefik直接路由（mcp-{server_id}.localhost）"
echo "  2. 鉴权微服务自动验证SAPI令牌并转换为系统令牌"
echo "  3. 支持直接访问令牌和SAPI令牌两种鉴权模式"
echo "  4. 所有现有功能保持兼容，性能显著提升"
echo ""
echo "✨ Traefik鉴权优化已启用，MCP服务性能将显著提升！" 