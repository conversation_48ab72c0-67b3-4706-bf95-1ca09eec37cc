import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Tabs, 
  Typography, 
  message 
} from 'antd';
import { 
  TeamOutlined, 
  BookOutlined, 
  GiftOutlined, 
  CheckCircleOutlined,
  AimOutlined
} from '@ant-design/icons';
import TaskManagement from './components/TaskManagement';
import KnowledgeBase from './components/KnowledgeBase';
import MarketingActivities from './components/MarketingActivities';
import StoreTargetManagement from './components/StoreTargetManagement';
import apiService from '../../../services/api';

const { Title } = Typography;
const { TabPane } = Tabs;

/**
 * 门店运维管理组件
 * 包含多个子模块：门店任务、知识库、营销活动、门店目标等
 */
const StoreOperation = () => {
  const [activeTab, setActiveTab] = useState('1');
  const [loading, setLoading] = useState(false);
  const [stores, setStores] = useState([]);

  // 获取门店列表
  const fetchStores = async () => {
    setLoading(true);
    try {
      const response = await apiService.project.store.getList();
      if (response && response.items) {
        setStores(response.items);
      }
    } catch (error) {
      console.error('获取门店列表失败:', error);
      message.error('获取门店列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 处理标签页切换
  const handleTabChange = (key) => {
    setActiveTab(key);
  };

  // 初始化
  useEffect(() => {
    fetchStores();
  }, []);

  return (
    <div className="store-operation-page">
      <Card>
        <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 16 }}>
          <Title level={4}>门店运维管理</Title>
        </div>

        <Tabs activeKey={activeTab} onChange={handleTabChange}>
          <TabPane 
            tab={<span><CheckCircleOutlined />门店任务管理</span>} 
            key="1"
          >
            <TaskManagement stores={stores} />
          </TabPane>
          
          <TabPane 
            tab={<span><BookOutlined />知识库维护</span>} 
            key="2"
          >
            <KnowledgeBase stores={stores} />
          </TabPane>
          
          <TabPane 
            tab={<span><GiftOutlined />营销活动维护</span>} 
            key="3"
          >
            <MarketingActivities stores={stores} />
          </TabPane>
          
          <TabPane 
            tab={<span><AimOutlined />门店目标</span>} 
            key="4"
          >
            <StoreTargetManagement stores={stores} />
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default StoreOperation;
