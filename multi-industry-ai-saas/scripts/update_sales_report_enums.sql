-- 使用事务确保操作的原子性
BEGIN;

-- 1. 重建 sales_report_status 枚举类型

-- 重命名旧的枚举类型作为备份
ALTER TYPE public.sales_report_status RENAME TO sales_report_status_old;

-- 创建新的、正确的枚举类型
CREATE TYPE public.sales_report_status AS ENUM ('draft', 'published', 'archived');

-- 更新 sales_reports 表，将 status 列转换为新的枚举类型
-- 使用 a.status::text::public.sales_report_status 进行类型转换
ALTER TABLE public.sales_reports
    ALTER COLUMN status DROP DEFAULT;
ALTER TABLE public.sales_reports
    ALTER COLUMN status TYPE public.sales_report_status USING status::text::public.sales_report_status;
ALTER TABLE public.sales_reports
    ALTER COLUMN status SET DEFAULT 'draft'::public.sales_report_status;

-- 2. 重建 sales_report_type 枚举类型

-- 重命名旧的枚举类型作为备份
ALTER TYPE public.sales_report_type RENAME TO sales_report_type_old;

-- 创建新的、正确的枚举类型
CREATE TYPE public.sales_report_type AS ENUM ('shift', 'daily', 'monthly');

-- 更新 sales_reports 表，将 report_type 列转换为新的枚举类型
ALTER TABLE public.sales_reports
    ALTER COLUMN report_type TYPE public.sales_report_type USING report_type::text::public.sales_report_type;


-- 3. 清理旧的枚举类型

-- 成功更新后，删除旧的枚举类型备份
DROP TYPE public.sales_report_status_old;
DROP TYPE public.sales_report_type_old;

-- 提交事务
COMMIT;

-- 验证更新结果
\echo '验证 sales_report_status 新的枚举值:'
SELECT unnest(enum_range(NULL::sales_report_status));

\echo '验证 sales_report_type 新的枚举值:'
SELECT unnest(enum_range(NULL::sales_report_type)); 