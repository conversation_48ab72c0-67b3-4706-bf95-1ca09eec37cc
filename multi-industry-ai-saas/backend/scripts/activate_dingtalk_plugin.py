#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
激活钉钉机器人插件脚本

此脚本用于激活钉钉机器人插件，解决403错误问题。
"""

import asyncio
import sys
import os
import logging
from sqlalchemy import select, and_, update
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from db.database import get_engine
from models.plugin import Plugin, TenantPlugin
from models.tenant import Tenant
from models.project import Project
from plugins.dingtalk.models.models import DingTalkSettings
from plugins.dingtalk.db_init import initialize_tenant_plugin

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

async def activate_dingtalk_plugin():
    """激活钉钉机器人插件"""
    try:
        # 获取数据库引擎
        engine = await get_engine()
        
        # 创建会话
        async_session = sessionmaker(
            engine, class_=AsyncSession, expire_on_commit=False
        )
        
        async with async_session() as db:
            # 查询钉钉机器人插件
            plugin_query = select(Plugin).where(Plugin.code == "dingtalk_robot")
            result = await db.execute(plugin_query)
            plugin = result.scalar_one_or_none()
            
            if not plugin:
                logger.error("钉钉机器人插件未安装")
                return
            
            logger.info(f"找到钉钉机器人插件: {plugin.id}")
            
            # 查询所有租户
            tenant_query = select(Tenant)
            result = await db.execute(tenant_query)
            tenants = result.scalars().all()
            
            for tenant in tenants:
                logger.info(f"处理租户: {tenant.id} ({tenant.name})")
                
                # 检查租户是否已安装插件
                tenant_plugin_query = select(TenantPlugin).where(
                    and_(
                        TenantPlugin.tenant_id == tenant.id,
                        TenantPlugin.plugin_id == plugin.id
                    )
                )
                result = await db.execute(tenant_plugin_query)
                tenant_plugin = result.scalar_one_or_none()
                
                if not tenant_plugin:
                    # 为租户安装插件
                    logger.info(f"为租户 {tenant.id} 安装钉钉机器人插件")
                    tenant_plugin = TenantPlugin(
                        tenant_id=tenant.id,
                        plugin_id=plugin.id,
                        status="active",
                        version=plugin.version,
                        settings={}
                    )
                    db.add(tenant_plugin)
                    await db.commit()
                elif tenant_plugin.status != "active":
                    # 激活插件
                    logger.info(f"激活租户 {tenant.id} 的钉钉机器人插件")
                    tenant_plugin.status = "active"
                    await db.commit()
                else:
                    logger.info(f"租户 {tenant.id} 的钉钉机器人插件已激活")
                
                # 查询租户的所有项目
                project_query = select(Project).where(Project.tenant_id == tenant.id)
                result = await db.execute(project_query)
                projects = result.scalars().all()
                
                for project in projects:
                    logger.info(f"处理项目: {project.id} ({project.name})")
                    
                    # 检查项目是否已初始化插件设置
                    settings_query = select(DingTalkSettings).where(
                        and_(
                            DingTalkSettings.tenant_id == tenant.id,
                            DingTalkSettings.project_id == project.id
                        )
                    )
                    result = await db.execute(settings_query)
                    settings = result.scalar_one_or_none()
                    
                    if not settings:
                        # 为项目初始化插件设置
                        logger.info(f"为项目 {project.id} 初始化钉钉机器人插件设置")
                        await initialize_tenant_plugin(db, tenant.id, project.id, plugin.id)
                    else:
                        logger.info(f"项目 {project.id} 已初始化钉钉机器人插件设置")
            
            logger.info("钉钉机器人插件激活完成")
    
    except Exception as e:
        logger.error(f"激活钉钉机器人插件失败: {str(e)}")
        raise

if __name__ == "__main__":
    asyncio.run(activate_dingtalk_plugin())
