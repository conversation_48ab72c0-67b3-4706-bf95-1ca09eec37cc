"""
闯关游戏插件 - 后端实现
"""
from typing import Dict, List, Optional, Any
from fastapi import APIRouter, Depends, HTTPException, Query, Path
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func
from datetime import datetime, timedelta
import random
import json
import logging

from db.database import get_db as get_async_session
from models.project import Project
from models.tenant import Tenant
from models.user import User
from plugins.marketing_game.models import MarketingGame, GamePrize, GameRecord
from plugins.marketing_game.schemas import (
    MarketingGameResponse as GameDetailResponse,
    GamePrizeResponse,
    GameRecordResponse,
    GameRecordCreate as GameRecordCreateRequest,
    GameRecordResponse as GameRecordCreateResponse
)
# 定义辅助函数
async def verify_project_access(project_id, session):
    """验证项目访问权限"""
    # 在实际应用中，这里应该检查用户是否有权限访问该项目
    # 简化起见，我们只检查项目是否存在
    project = await session.get(Project, project_id)
    if not project:
        raise HTTPException(status_code=404, detail="项目不存在")
    return project

# 设置日志
logger = logging.getLogger(__name__)

# 创建路由
router = APIRouter(prefix="/api/v1/project/{project_id}/plugin/marketing_game/adventure_game")

@router.get("/levels", response_model=Dict[str, Any])
async def get_game_levels(
    project_id: int = Path(...),
    game_id: int = Query(..., description="游戏ID"),
    session: AsyncSession = Depends(get_async_session)
):
    """
    获取闯关游戏的关卡设置
    """
    # 验证项目访问权限
    await verify_project_access(project_id, session)

    # 获取游戏信息
    game = await session.get(MarketingGame, game_id)
    if not game:
        raise HTTPException(status_code=404, detail="游戏不存在")

    # 获取游戏设置中的关卡信息
    settings = game.settings or {}
    levels = settings.get("levels", [])

    # 如果没有设置关卡，返回默认关卡
    if not levels:
        levels = [
            {
                "id": 1,
                "name": "初级挑战",
                "description": "完成简单的问答题，获得进入下一关的资格",
                "type": "quiz",
                "questions": [
                    {
                        "id": "q1",
                        "text": "以下哪个是我们品牌的标志颜色？",
                        "options": ["红色", "蓝色", "绿色", "黄色"],
                        "answer": 0
                    },
                    {
                        "id": "q2",
                        "text": "我们的旗舰店位于哪个城市？",
                        "options": ["北京", "上海", "广州", "深圳"],
                        "answer": 1
                    }
                ],
                "required_score": 1,
                "reward": {
                    "points": 10,
                    "description": "获得10积分"
                }
            },
            {
                "id": 2,
                "name": "中级挑战",
                "description": "完成产品知识测试，展示你对我们产品的了解",
                "type": "quiz",
                "questions": [
                    {
                        "id": "q3",
                        "text": "我们的明星产品有哪些特点？",
                        "options": ["价格实惠", "质量上乘", "设计新颖", "以上都是"],
                        "answer": 3
                    },
                    {
                        "id": "q4",
                        "text": "我们的产品保修期是多久？",
                        "options": ["6个月", "1年", "2年", "3年"],
                        "answer": 2
                    },
                    {
                        "id": "q5",
                        "text": "以下哪项不是我们提供的服务？",
                        "options": ["免费安装", "上门维修", "终身质保", "7天无理由退换"],
                        "answer": 2
                    }
                ],
                "required_score": 2,
                "reward": {
                    "points": 20,
                    "description": "获得20积分"
                }
            },
            {
                "id": 3,
                "name": "高级挑战",
                "description": "完成最终挑战，赢取大奖！",
                "type": "quiz",
                "questions": [
                    {
                        "id": "q6",
                        "text": "我们的品牌创立于哪一年？",
                        "options": ["1998年", "2005年", "2010年", "2015年"],
                        "answer": 1
                    },
                    {
                        "id": "q7",
                        "text": "以下哪个不是我们的合作伙伴？",
                        "options": ["阿里巴巴", "腾讯", "百度", "字节跳动"],
                        "answer": 3
                    },
                    {
                        "id": "q8",
                        "text": "我们的企业使命是什么？",
                        "options": ["创造价值", "服务客户", "引领行业", "以上都是"],
                        "answer": 3
                    },
                    {
                        "id": "q9",
                        "text": "我们最新推出的产品系列名称是？",
                        "options": ["未来星", "智慧家", "创新者", "领航者"],
                        "answer": 1
                    }
                ],
                "required_score": 3,
                "reward": {
                    "points": 50,
                    "description": "获得50积分和抽奖机会"
                }
            }
        ]

    return {
        "success": True,
        "data": levels
    }

@router.post("/complete", response_model=GameRecordCreateResponse)
async def complete_adventure_game(
    request: GameRecordCreateRequest,
    project_id: int = Path(...),
    game_id: int = Query(..., description="游戏ID"),
    session: AsyncSession = Depends(get_async_session)
):
    """
    完成闯关游戏并抽奖
    """
    # 验证项目访问权限
    await verify_project_access(project_id, session)

    # 获取游戏信息
    game = await session.get(MarketingGame, game_id)
    if not game:
        raise HTTPException(status_code=404, detail="游戏不存在")

    # 检查游戏是否已开始
    if game.status != "active":
        raise HTTPException(status_code=400, detail="游戏尚未开始或已结束")

    # 检查游戏时间
    now = datetime.now()
    if game.start_time and game.start_time > now:
        raise HTTPException(status_code=400, detail="游戏尚未开始")
    if game.end_time and game.end_time < now:
        raise HTTPException(status_code=400, detail="游戏已结束")

    # 获取参与者ID，如果为空则生成一个
    participant_id = request.participant_id
    if not participant_id:
        participant_id = f"anonymous_{random.randint(10000, 99999)}_{int(datetime.now().timestamp())}"

    # 检查参与次数限制
    if game.max_draws_per_user > 0:
        # 查询该用户已参与次数
        stmt = select(func.count()).where(
            GameRecord.game_id == game_id,
            GameRecord.participant_id == participant_id
        )
        result = await session.execute(stmt)
        count = result.scalar_one()

        if count >= game.max_draws_per_user:
            raise HTTPException(status_code=400, detail="已达到最大参与次数")

    # 获取奖品列表
    stmt = select(GamePrize).where(
        GamePrize.game_id == game_id,
        GamePrize.remaining > 0
    )
    result = await session.execute(stmt)
    prizes = result.scalars().all()

    # 如果没有可用奖品，返回默认奖品（未中奖）
    if not prizes:
        default_prize = GamePrize(
            id=-1,
            game_id=game_id,
            name="谢谢参与",
            description="下次再来",
            probability=100,
            remaining=999999,
            is_default=True
        )
        prizes = [default_prize]

    # 根据得分和概率选择奖品
    score = request.score or 0

    # 根据得分调整中奖概率
    # 得分越高，中奖概率越大
    score_factor = min(score / 100, 1.0)  # 将得分转换为0-1之间的因子

    # 构建概率表
    probability_table = []
    total_probability = 0

    for prize in prizes:
        if prize.is_default:
            # 默认奖品（未中奖）的概率会随着得分的增加而减少
            adjusted_probability = max(0, 100 - (prize.probability * score_factor))
        else:
            # 实际奖品的概率会随着得分的增加而增加
            adjusted_probability = prize.probability * (1 + score_factor)

        if prize.remaining > 0:
            probability_table.append({
                "prize": prize,
                "probability": adjusted_probability
            })
            total_probability += adjusted_probability

    # 随机抽取奖品
    if total_probability <= 0:
        # 如果总概率为0，返回默认奖品
        selected_prize = next((p["prize"] for p in probability_table if p["prize"].is_default), probability_table[0]["prize"])
    else:
        # 根据概率随机选择
        rand = random.uniform(0, total_probability)
        cumulative = 0
        selected_prize = probability_table[-1]["prize"]  # 默认选择最后一个

        for item in probability_table:
            cumulative += item["probability"]
            if rand <= cumulative:
                selected_prize = item["prize"]
                break

    # 更新奖品剩余数量
    if selected_prize.id > 0:  # 非默认奖品
        selected_prize.remaining -= 1
        session.add(selected_prize)

    # 创建抽奖记录
    record = GameRecord(
        game_id=game_id,
        prize_id=selected_prize.id,
        participant_id=participant_id,
        participant_name=request.participant_name or "匿名用户",
        device_id=request.device_id,
        score=score,
        created_at=datetime.now()
    )
    session.add(record)
    await session.commit()
    await session.refresh(record)

    # 返回抽奖结果
    return {
        "success": True,
        "data": {
            "id": record.id,
            "game_id": record.game_id,
            "prize_id": record.prize_id,
            "prize_name": selected_prize.name,
            "prize_description": selected_prize.description,
            "prize_image": selected_prize.image_url,
            "participant_id": record.participant_id,
            "participant_name": record.participant_name,
            "score": record.score,
            "created_at": record.created_at.isoformat()
        }
    }
