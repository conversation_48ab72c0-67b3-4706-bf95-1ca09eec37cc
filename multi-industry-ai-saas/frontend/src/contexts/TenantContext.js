import React, { createContext, useContext, useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { message } from 'antd';
import { useAuth } from './AuthContext';
import { admin, tenant, serviceProvider, setTenantId, setProjectId } from '../services';

// 创建租户上下文
const TenantContext = createContext();

/**
 * 租户上下文提供者
 * 管理租户、服务商和项目相关的状态和操作
 */
export const TenantProvider = ({ children }) => {
  const { user, isAuthenticated } = useAuth();
  const navigate = useNavigate();

  // 状态
  const [currentTenant, setCurrentTenant] = useState(null);
  const [currentServiceProvider, setCurrentServiceProvider] = useState(null);
  const [currentProject, setCurrentProject] = useState(null);
  const [tenants, setTenants] = useState([]);
  const [projects, setProjects] = useState([]);
  const [loading, setLoading] = useState(false);
  const [industryType, setIndustryType] = useState('retail'); // 默认行业类型为零售

  // 初始化租户信息
  useEffect(() => {
    if (isAuthenticated && user) {
      // 如果用户已登录，获取租户信息
      if (user.tenant_id) {
        fetchTenantInfo(user.tenant_id);
      } else if (user.service_provider_id) {
        fetchServiceProviderInfo(user.service_provider_id);
      }

      // 如果是系统管理员或服务商管理员，获取所有租户
      if (user.is_system_admin || user.is_service_provider_admin) {
        fetchTenants();
      }
    }
  }, [isAuthenticated, user]);

  // 获取租户信息
  const fetchTenantInfo = async (tenantId) => {
    try {
      setLoading(true);
      setTenantId(tenantId);
      const response = await tenant.getInfo();
      setCurrentTenant(response);
      setIndustryType(response.industry_type);

      // 获取租户的项目
      fetchProjects(tenantId);
    } catch (error) {
      console.error('获取租户信息失败:', error);
      message.error('获取租户信息失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取服务商信息
  const fetchServiceProviderInfo = async (serviceProviderId) => {
    try {
      setLoading(true);
      // 设置服务商ID
      localStorage.setItem('service_provider_id', serviceProviderId);
      const response = await serviceProvider.getInfo();
      setCurrentServiceProvider(response);
    } catch (error) {
      console.error('获取服务商信息失败:', error);
      message.error('获取服务商信息失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取所有租户
  const fetchTenants = async () => {
    try {
      setLoading(true);
      let response;

      if (user.is_system_admin) {
        // 系统管理员获取所有租户
        response = await admin.tenant.getList();
      } else if (user.is_service_provider_admin && user.service_provider_id) {
        // 服务商管理员获取其服务商下的租户
        response = await serviceProvider.tenant.getList();
      }

      if (response) {
        setTenants(response);
      }
    } catch (error) {
      console.error('获取租户列表失败:', error);
      message.error('获取租户列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取租户的项目
  const fetchProjects = async (tenantId) => {
    try {
      setLoading(true);
      // 确保租户ID已设置
      if (tenantId) {
        setTenantId(tenantId);
      }
      const response = await tenant.project.getList();
      setProjects(response);

      // 如果有项目且没有选择当前项目，则选择第一个项目
      if (response.length > 0 && !currentProject) {
        setCurrentProject(response[0]);
      }
    } catch (error) {
      console.error('获取项目列表失败:', error);
      message.error('获取项目列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 切换租户
  const switchTenant = async (tenantId) => {
    try {
      setLoading(true);
      await fetchTenantInfo(tenantId);

      // 重置当前项目
      setCurrentProject(null);

      // 重定向到租户的仪表盘
      navigate('/dashboard');

      message.success('已切换租户');
    } catch (error) {
      console.error('切换租户失败:', error);
      message.error('切换租户失败');
    } finally {
      setLoading(false);
    }
  };

  // 切换项目
  const switchProject = async (projectId) => {
    try {
      setLoading(true);
      const project = projects.find(p => p.id === projectId);

      if (project) {
        setCurrentProject(project);

        // 设置项目ID
        setProjectId(projectId);

        // 如果项目的行业类型与当前不同，则更新行业类型
        if (project.industry_type !== industryType) {
          setIndustryType(project.industry_type);
        }

        // 重定向到项目的仪表盘
        navigate('/dashboard');

        message.success('已切换项目');
      } else {
        throw new Error('项目不存在');
      }
    } catch (error) {
      console.error('切换项目失败:', error);
      message.error('切换项目失败');
    } finally {
      setLoading(false);
    }
  };

  // 创建新租户
  const createTenant = async (tenantData) => {
    try {
      setLoading(true);
      let response;

      if (user.is_system_admin) {
        // 系统管理员创建租户
        response = await admin.tenant.create(tenantData);
      } else if (user.is_service_provider_admin) {
        // 服务商管理员创建租户
        response = await serviceProvider.tenant.create(tenantData);
      }

      // 刷新租户列表
      await fetchTenants();

      message.success('租户创建成功');
      return response;
    } catch (error) {
      console.error('创建租户失败:', error);
      message.error('创建租户失败');
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // 创建新项目
  const createProject = async (projectData) => {
    try {
      setLoading(true);
      const response = await tenant.project.create({
        ...projectData,
        tenant_id: currentTenant?.id
      });

      // 刷新项目列表
      await fetchProjects(currentTenant.id);

      message.success('项目创建成功');
      return response;
    } catch (error) {
      console.error('创建项目失败:', error);
      message.error('创建项目失败');
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // 上下文值
  const contextValue = {
    currentTenant,
    currentServiceProvider,
    currentProject,
    tenants,
    projects,
    loading,
    industryType,
    switchTenant,
    switchProject,
    createTenant,
    createProject,
    fetchTenants,
    fetchProjects,
    setIndustryType
  };

  return (
    <TenantContext.Provider value={contextValue}>
      {children}
    </TenantContext.Provider>
  );
};

// 自定义Hook，用于在组件中使用租户上下文
export const useTenant = () => {
  const context = useContext(TenantContext);
  if (!context) {
    throw new Error('useTenant必须在TenantProvider内部使用');
  }
  return context;
};

export default TenantContext;
