#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
JWT认证模块

提供JWT令牌的生成、验证和管理功能
支持OAuth2.1标准，符合MCP最新草案及协议
"""

import os
import json
import time
import logging
import uuid
from datetime import datetime, timedelta, timezone
from typing import Dict, Any, Optional, List, Union, Set

import jwt
from fastapi import Depends, HTTPException, status, Request, Security
from fastapi.security import OAuth2PasswordBearer, OAuth2PasswordRequestForm, HTTPBearer, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select

from db.database import get_db
from models.user import User
from core.config import settings
from core.auth import get_current_user

from .models import SAPIToken, MCPServer

# 配置日志
logger = logging.getLogger(__name__)

# JWT配置
JWT_SECRET_KEY = settings.SECRET_KEY  # 直接使用settings.SECRET_KEY，不再从环境变量获取
JWT_ALGORITHM = "HS256"
JWT_ACCESS_TOKEN_EXPIRE_MINUTES = int(os.environ.get("JWT_ACCESS_TOKEN_EXPIRE_MINUTES", "30"))
JWT_REFRESH_TOKEN_EXPIRE_DAYS = int(os.environ.get("JWT_REFRESH_TOKEN_EXPIRE_DAYS", "7"))
JWT_MCP_TOKEN_EXPIRE_HOURS = int(os.environ.get("JWT_MCP_TOKEN_EXPIRE_HOURS", "24"))

# OAuth2配置
OAUTH2_TOKEN_URL = "/api/v1/plugin/sapi/oauth/token"
oauth2_scheme = OAuth2PasswordBearer(tokenUrl=OAUTH2_TOKEN_URL)
http_bearer = HTTPBearer()

# 令牌类型
class TokenType:
    ACCESS = "access"
    REFRESH = "refresh"
    MCP = "mcp"

# 令牌权限
class TokenPermission:
    READ = "read"
    WRITE = "write"
    READ_WRITE = "read_write"

    @classmethod
    def get_all_permissions(cls) -> Set[str]:
        """获取所有权限"""
        return {cls.READ, cls.WRITE, cls.READ_WRITE}

    @classmethod
    def validate_permission(cls, permission: str) -> bool:
        """验证权限是否有效"""
        return permission in cls.get_all_permissions()

    @classmethod
    def get_effective_permissions(cls, permission: str) -> Set[str]:
        """获取有效权限集合"""
        if permission == cls.READ_WRITE:
            return {cls.READ, cls.WRITE}
        return {permission}

# 创建JWT令牌
def create_jwt_token(
    subject: str,
    token_type: str,
    expires_delta: Optional[timedelta] = None,
    scopes: List[str] = None,
    project_id: Optional[str] = None,
    server_permissions: Optional[Dict[str, List[str]]] = None,
    default_permissions: Optional[List[str]] = None,
    user_id: Optional[str] = None,
    **extra_claims
) -> str:
    """
    创建JWT令牌

    Args:
        subject: 令牌主题，通常是用户ID或令牌ID
        token_type: 令牌类型，如access、refresh、mcp等
        expires_delta: 过期时间增量，如果为None则使用默认值
        scopes: 令牌作用域列表
        project_id: 项目ID
        server_permissions: 服务器权限映射，格式为{server_id: [permissions]}
        default_permissions: 默认权限列表，用于未明确指定的服务器
        user_id: 用户ID
        **extra_claims: 额外的声明

    Returns:
        str: JWT令牌
    """
    if expires_delta is None:
        if token_type == TokenType.ACCESS:
            expires_delta = timedelta(minutes=JWT_ACCESS_TOKEN_EXPIRE_MINUTES)
        elif token_type == TokenType.REFRESH:
            expires_delta = timedelta(days=JWT_REFRESH_TOKEN_EXPIRE_DAYS)
        elif token_type == TokenType.MCP:
            # MCP令牌默认有效期为24小时
            expires_delta = timedelta(hours=JWT_MCP_TOKEN_EXPIRE_HOURS)
        else:
            # 默认30分钟
            expires_delta = timedelta(minutes=30)

    # 当前时间 (使用timezone-aware对象)
    now = datetime.now(tz=timezone.utc)

    # 基本声明
    claims = {
        "sub": subject,
        "type": token_type,
        "iat": now,
        "exp": now + expires_delta,
        "jti": str(uuid.uuid4()),  # JWT ID，用于标识令牌
    }

    # 添加作用域
    if scopes:
        claims["scope"] = " ".join(scopes)

    # 添加项目ID
    if project_id:
        claims["project_id"] = project_id

    # 添加服务器权限映射
    if server_permissions:
        claims["server_permissions"] = server_permissions

    # 添加默认权限
    if default_permissions:
        claims["default_permissions"] = default_permissions

    # 添加用户ID
    if user_id:
        claims["user_id"] = user_id

    # 添加额外的声明
    claims.update(extra_claims)

    # 编码JWT
    encoded_jwt = jwt.encode(claims, JWT_SECRET_KEY, algorithm=JWT_ALGORITHM)

    return encoded_jwt

# 验证JWT令牌
def verify_jwt_token(token: str) -> Dict[str, Any]:
    """
    验证JWT令牌

    Args:
        token: JWT令牌

    Returns:
        Dict[str, Any]: 令牌声明

    Raises:
        HTTPException: 如果令牌无效或已过期
    """
    try:
        # 解码JWT
        payload = jwt.decode(token, JWT_SECRET_KEY, algorithms=[JWT_ALGORITHM])
        return payload
    except jwt.ExpiredSignatureError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="令牌已过期",
            headers={"WWW-Authenticate": "Bearer"},
        )
    except jwt.InvalidTokenError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的令牌",
            headers={"WWW-Authenticate": "Bearer"},
        )

# 从请求中获取JWT令牌
async def get_token_from_request(
    request: Request,
    token_from_header: Optional[HTTPAuthorizationCredentials] = Security(http_bearer)
) -> str:
    """
    从请求中获取JWT令牌，符合OAuth2.1规范

    Args:
        request: 请求对象
        token_from_header: 从Authorization头部获取的令牌

    Returns:
        str: JWT令牌
    """
    # 首先尝试从Authorization头部获取令牌
    if token_from_header:
        return token_from_header.credentials

    # 然后尝试从请求体获取令牌（仅限POST请求）
    if request.method == "POST" and request.headers.get("content-type") == "application/x-www-form-urlencoded":
        try:
            form_data = await request.form()
            if "access_token" in form_data:
                return form_data["access_token"]
        except Exception as e:
            logger.warning(f"从请求体获取令牌失败: {str(e)}")

    # 最后尝试从Cookie获取令牌
    token = request.cookies.get("access_token")
    if token:
        return token

    # 如果都没有找到，抛出异常
    raise HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="未提供令牌",
        headers={"WWW-Authenticate": "Bearer"},
    )

# 获取当前令牌
async def get_current_token(token: str = Depends(oauth2_scheme)) -> Dict[str, Any]:
    """
    获取当前令牌

    Args:
        token: JWT令牌

    Returns:
        Dict[str, Any]: 令牌声明
    """
    return verify_jwt_token(token)

# 验证MCP令牌权限
def verify_mcp_token_permission(
    payload: Dict[str, Any],
    server_id: str,
    required_permission: str
) -> bool:
    """
    验证MCP令牌是否有特定服务器的特定权限

    Args:
        payload: 令牌声明
        server_id: 服务器ID
        required_permission: 所需权限

    Returns:
        bool: 是否有权限
    """
    # 检查令牌类型
    if payload.get("type") != TokenType.MCP:
        return False

    # 获取服务器权限映射
    server_permissions = payload.get("server_permissions", {})

    # 获取默认权限
    default_permissions = payload.get("default_permissions", [])

    # 检查特定服务器权限
    if server_id in server_permissions:
        permissions = server_permissions[server_id]
        # 如果有read_write权限，则同时拥有read和write权限
        if TokenPermission.READ_WRITE in permissions:
            return True
        # 否则检查是否有所需权限
        return required_permission in permissions

    # 如果没有特定服务器权限，检查默认权限
    if TokenPermission.READ_WRITE in default_permissions:
        return True
    return required_permission in default_permissions

# 获取当前MCP令牌
async def get_current_mcp_token(
    db: AsyncSession = Depends(get_db),
    token: str = Depends(get_token_from_request)
) -> Dict[str, Any]:
    """
    获取当前MCP令牌

    Args:
        db: 数据库会话
        token: JWT令牌

    Returns:
        Dict[str, Any]: 令牌声明
    """
    # 验证令牌
    payload = verify_jwt_token(token)

    # 检查令牌类型
    if payload.get("type") != TokenType.MCP:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的MCP令牌",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # 检查必要的声明
    if "project_id" not in payload:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="无效的MCP令牌，缺少项目ID",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # 更新令牌的最后使用时间
    try:
        token_id = payload.get("jti")
        if token_id:
            # 查询令牌
            result = await db.execute(select(SAPIToken).where(SAPIToken.id == token_id))
            token_obj = result.scalars().first()

            if token_obj:
                # 更新最后使用时间
                token_obj.last_used = datetime.now()
                await db.commit()
    except Exception as e:
        logger.warning(f"更新令牌最后使用时间失败: {str(e)}")

    return payload

# 验证MCP令牌对特定服务器的权限
async def verify_mcp_server_permission(
    payload: Dict[str, Any] = Depends(get_current_mcp_token),
    server_id: str = None,
    required_permission: str = TokenPermission.READ
) -> Dict[str, Any]:
    """
    验证MCP令牌对特定服务器的权限

    Args:
        payload: 令牌声明
        server_id: 服务器ID
        required_permission: 所需权限

    Returns:
        Dict[str, Any]: 令牌声明
    """
    if not server_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="未提供服务器ID",
        )

    # 验证权限
    if not verify_mcp_token_permission(payload, server_id, required_permission):
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=f"无权访问服务器 {server_id}，需要 {required_permission} 权限",
        )

    return payload
