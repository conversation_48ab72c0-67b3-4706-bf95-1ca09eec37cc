import React, { useState, useEffect } from 'react';
import {
  Card,
  Button,
  Space,
  Tag,
  Typography,
  Descriptions,
  Table,
  Divider,
  message,
  Spin,
  Empty,
  Tabs,
  Modal,
  Form,
  Input,
  DatePicker,
  Select,
  Row,
  Col,
  Statistic,
  Badge,
  Tooltip
} from 'antd';
import {
  ArrowLeftOutlined,
  EditOutlined,
  DeleteOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  RollbackOutlined,
  SendOutlined
} from '@ant-design/icons';
import { useParams, useNavigate } from 'react-router-dom';
import dayjs from "dayjs";
import apiService from '../../../services/api';
import dateTimeUtils from '../../../utils/dateTimeUtils';

const { Title, Text } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;

// 状态标签颜色映射
const statusColors = {
  draft: 'default',
  submitted: 'processing',
  approved: 'success',
  rejected: 'error'
};

// 状态文本映射
const statusTexts = {
  draft: '草稿',
  submitted: '已提交',
  approved: '已通过',
  rejected: '已拒绝'
};

// 上报类型映射
const reportTypeTexts = {
  shift: '班次',
  daily: '日报',
  weekly: '周报',
  monthly: '月报'
};

const SalesReportDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();

  // 状态管理
  const [loading, setLoading] = useState(true);
  const [report, setReport] = useState(null);
  const [activeTab, setActiveTab] = useState('sales');
  const [statusModalVisible, setStatusModalVisible] = useState(false);
  const [statusForm] = Form.useForm();
  
  // 获取销售上报详情
  const fetchReportDetail = async () => {
    setLoading(true);
    try {
      const response = await apiService.project.salesReport.getDetail(id);
      if (response) {
        setReport(response);
      } else {
        message.error('获取销售上报详情失败');
      }
    } catch (error) {
      console.error('获取销售上报详情失败:', error);
      message.error('获取销售上报详情失败: ' + (error.response?.data?.detail || error.message));
    } finally {
      setLoading(false);
    }
  };

  // 返回列表
  const handleBack = () => {
    navigate('/project/store/sales-report');
  };

  // 编辑销售上报
  const handleEdit = () => {
    navigate(`/project/store/sales-report/edit/${id}`);
  };

  // 更新状态
  const handleStatusUpdate = async () => {
    try {
      const values = await statusForm.validateFields();
      setLoading(true);
      await apiService.project.salesReport.updateStatus(id, {
        status: values.status,
        notes: values.notes,
      });
      message.success('状态更新成功');
      setStatusModalVisible(false);
      fetchReportDetail();
    } catch (error) {
      console.error('状态更新失败:', error);
      message.error('状态更新失败: ' + (error.response?.data?.detail || error.message));
    } finally {
      setLoading(false);
    }
  };

  // 初始化
  useEffect(() => {
    dateTimeUtils.initDateTimeUtils();
    if (id) {
      fetchReportDetail();
    }
  }, [id]);

  // 销售渠道详情表格列定义
  const channelDetailsColumns = [
    {
      title: '渠道名称',
      dataIndex: ['sales_channel', 'name'],
      key: 'channel_name',
    },
    {
      title: '总销售额',
      dataIndex: 'total_sales',
      key: 'total_sales',
      render: (text) => `¥${parseFloat(text || 0).toFixed(2)}`,
    },
    {
      title: '总笔数',
      dataIndex: 'total_orders',
      key: 'total_orders',
    },
  ];

  const expandedRowRender = (record) => {
    const columns = [
      {
        title: '支付方式',
        dataIndex: ['payment_method', 'name'],
        key: 'pm_name'
      },
      {
        title: '金额',
        dataIndex: 'amount',
        key: 'amount',
        render: (text) => `¥${parseFloat(text || 0).toFixed(2)}`,
      },
      {
        title: '笔数',
        dataIndex: 'orders',
        key: 'orders'
      },
    ];
    return <Table columns={columns} dataSource={record.payment_methods_details} pagination={false} rowKey="payment_method_id"/>;
  };

  // 充值/售卡详情表格列定义
  const rechargeDetailsColumns = [
    {
      title: '支付方式',
      dataIndex: ['payment_method', 'name'],
      key: 'pm_name',
    },
    {
      title: '金额',
      dataIndex: 'amount',
      key: 'amount',
      render: (text) => `¥${parseFloat(text || 0).toFixed(2)}`,
    },
    {
      title: '笔数',
      dataIndex: 'orders',
      key: 'orders',
    },
  ];
  
  if (loading) {
    return <Spin tip="正在加载详情..." style={{ display: 'block', marginTop: 50 }} />;
  }

  if (!report) {
    return (
      <Empty
        description="销售上报不存在或已被删除"
        style={{ margin: '50px 0' }}
      >
        <Button type="primary" onClick={handleBack}>返回列表</Button>
      </Empty>
    );
  }

  const grandTotalAmount = (report?.total_sales || 0) + (report?.total_recharge_amount || 0);
  const grandTotalOrders = (report?.total_orders || 0) + (report?.total_recharge_orders || 0);

  const salesSection = (
    <Table
      columns={channelDetailsColumns}
      dataSource={report?.channel_details || []}
      rowKey="sales_channel_id"
      pagination={false}
      expandable={{
        expandedRowRender,
        rowExpandable: record => record.payment_methods_details && record.payment_methods_details.length > 0,
      }}
      summary={() => (
        <Table.Summary.Row>
          <Table.Summary.Cell index={0}><strong>总计</strong></Table.Summary.Cell>
          <Table.Summary.Cell index={1}>
            <Text strong>¥{parseFloat(report?.total_sales || 0).toFixed(2)}</Text>
          </Table.Summary.Cell>
          <Table.Summary.Cell index={2}>
            <Text strong>{report?.total_orders || 0}</Text>
          </Table.Summary.Cell>
        </Table.Summary.Row>
      )}
    />
  );

  const rechargeSection = (
    <Table
      columns={rechargeDetailsColumns}
      dataSource={report?.recharge_sales_data || []}
      rowKey="payment_method_id"
      pagination={false}
      summary={() => (
        <Table.Summary.Row>
          <Table.Summary.Cell index={0}><strong>总计</strong></Table.Summary.Cell>
          <Table.Summary.Cell index={1}>
            <Text strong>¥{parseFloat(report?.total_recharge_amount || 0).toFixed(2)}</Text>
          </Table.Summary.Cell>
          <Table.Summary.Cell index={2}>
            <Text strong>{report?.total_recharge_orders || 0}</Text>
          </Table.Summary.Cell>
        </Table.Summary.Row>
      )}
    />
  );

  return (
    <div className="sales-report-detail-page">
      <Card
        title={`销售上报详情 (编号: ${report?.report_number || 'N/A'})`}
        extra={
          <Space>
            <Button icon={<ArrowLeftOutlined />} onClick={handleBack}>
              返回
            </Button>
            {report?.status === 'draft' && (
              <Button icon={<EditOutlined />} onClick={handleEdit}>
                编辑
              </Button>
            )}
             <Button 
                type="primary" 
                danger={report?.status !== 'draft'} 
                onClick={() => setStatusModalVisible(true)}
              >
                {report?.status === 'draft' ? '提交审核' : '更新状态'}
              </Button>
          </Space>
        }
      >
        <Descriptions bordered column={3}>
          <Descriptions.Item label="门店名称">{report?.store?.name}</Descriptions.Item>
          <Descriptions.Item label="上报日期">
            {report?.report_date ? dayjs(report.report_date).format('YYYY-MM-DD') : 'N/A'}
          </Descriptions.Item>
          <Descriptions.Item label="状态">
            <Tag color={statusColors[report?.status || 'default']}>
              {statusTexts[report?.status || 'N/A']}
            </Tag>
          </Descriptions.Item>
          <Descriptions.Item label="上报类型">
            {reportTypeTexts[report?.report_type || 'daily']}
          </Descriptions.Item>
          <Descriptions.Item label="上报人">{report?.reporter?.username || 'N/A'}</Descriptions.Item>
          <Descriptions.Item label="创建时间">
            {report?.created_at ? dateTimeUtils.formatDateTime(report.created_at) : 'N/A'}
          </Descriptions.Item>
           <Descriptions.Item label="审核人">{report?.auditor?.username || 'N/A'}</Descriptions.Item>
          <Descriptions.Item label="审核时间">
            {report?.audited_at ? dateTimeUtils.formatDateTime(report.audited_at) : 'N/A'}
          </Descriptions.Item>
          <Descriptions.Item label="最后更新时间">
             {report?.updated_at ? dateTimeUtils.formatDateTime(report.updated_at) : 'N/A'}
          </Descriptions.Item>
          <Descriptions.Item label="备注" span={3}>{report?.notes || '无'}</Descriptions.Item>
           <Descriptions.Item label="审核意见" span={3}>{report?.audit_notes || '无'}</Descriptions.Item>
        </Descriptions>

        <Divider />
        
        <Row gutter={24}>
          <Col xs={24} md={16}>
            <Tabs activeKey={activeTab} onChange={setActiveTab}>
              <TabPane tab="销售额详情" key="sales">
                {salesSection}
              </TabPane>
              <TabPane tab="充值/售卡详情" key="recharge">
                {rechargeSection}
              </TabPane>
            </Tabs>
          </Col>

          <Col xs={24} md={8}>
            <Card title="数据总览" bordered={false}>
              <Statistic title="销售总额" value={report?.total_sales || 0} precision={2} prefix="¥" />
              <Statistic title="销售总笔数" value={report?.total_orders || 0} />
              <Divider />
              <Statistic title="充值/售卡总额" value={report?.total_recharge_amount || 0} precision={2} prefix="¥" />
              <Statistic title="充值/售卡总笔数" value={report?.total_recharge_orders || 0} />
              <Divider style={{'border-color': 'black', 'border-width': '2px'}}/>
               <Statistic title="合计总额" value={grandTotalAmount} precision={2} prefix="¥" />
               <Statistic title="合计总笔数" value={grandTotalOrders} />
            </Card>
          </Col>
        </Row>
      </Card>

      <Modal
        title="更新上报状态"
        open={statusModalVisible}
        onCancel={() => setStatusModalVisible(false)}
        onOk={handleStatusUpdate}
        confirmLoading={loading}
      >
        <Form form={statusForm} layout="vertical">
          <Form.Item
            name="status"
            label="选择新状态"
            initialValue={report?.status === 'draft' ? 'submitted' : report?.status}
            rules={[{ required: true, message: '请选择状态' }]}
          >
            <Select>
              {report?.status === 'draft' && <Option value="submitted">提交审核</Option>}
              {report?.status === 'submitted' && <Option value="approved">通过</Option>}
              {report?.status === 'submitted' && <Option value="rejected">拒绝</Option>}
              {report?.status === 'approved' && <Option value="draft">撤回到草稿</Option>}
              {report?.status === 'rejected' && <Option value="draft">撤回到草稿</Option>}
            </Select>
          </Form.Item>
          <Form.Item
            name="notes"
            label="备注/审核意见"
          >
            <Input.TextArea rows={4} placeholder="请输入备注或审核意见" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default SalesReportDetail;
