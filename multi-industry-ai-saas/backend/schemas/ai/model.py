#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
AI 模型 Schema
"""

from typing import Optional, List, Dict, Any
from datetime import datetime
import uuid
from pydantic import BaseModel, Field, validator

class AIModelCapabilities(BaseModel):
    """AI模型能力配置"""
    # 基础能力
    supports_chat: bool = Field(True, description="支持对话")
    supports_function_calling: bool = Field(False, description="支持函数调用")
    supports_streaming: bool = Field(True, description="支持流式输出")
    supports_system_message: bool = Field(True, description="支持系统消息")
    
    # 多模态能力
    supports_vision: bool = Field(False, description="支持视觉/图像理解")
    supports_image_generation: bool = Field(False, description="支持图像生成")
    supports_audio_input: bool = Field(False, description="支持音频输入")
    supports_audio_output: bool = Field(False, description="支持音频输出")
    supports_speech_to_text: bool = Field(False, description="支持语音转文字")
    supports_text_to_speech: bool = Field(False, description="支持文字转语音")
    supports_video_understanding: bool = Field(False, description="支持视频理解")
    
    # 文档处理能力
    supports_document_analysis: bool = Field(False, description="支持文档分析")
    supports_code_generation: bool = Field(False, description="支持代码生成")
    supports_code_execution: bool = Field(False, description="支持代码执行")
    
    # 高级能力
    supports_web_search: bool = Field(False, description="支持网络搜索")
    supports_file_upload: bool = Field(False, description="支持文件上传")
    supports_memory: bool = Field(False, description="支持记忆功能")
    supports_plugins: bool = Field(False, description="支持插件")
    supports_thinking: bool = Field(False, description="支持长思考模式")
    
    # 输入格式支持
    supported_image_formats: Optional[List[str]] = Field(None, description="支持的图像格式")
    supported_audio_formats: Optional[List[str]] = Field(None, description="支持的音频格式")
    supported_video_formats: Optional[List[str]] = Field(None, description="支持的视频格式")
    supported_document_formats: Optional[List[str]] = Field(None, description="支持的文档格式")
    
    # 限制参数
    max_image_size: Optional[int] = Field(None, description="最大图像尺寸(像素)")
    max_audio_duration: Optional[int] = Field(None, description="最大音频时长(秒)")
    max_video_duration: Optional[int] = Field(None, description="最大视频时长(秒)")
    max_file_size: Optional[int] = Field(None, description="最大文件大小(MB)")

class AIModelBase(BaseModel):
    """AI 模型基础 Schema"""
    provider_id: uuid.UUID = Field(..., description="提供商 ID")
    name: str = Field(..., description="模型名称，如 gpt-4, claude-3 等")
    display_name: str = Field(..., description="显示名称")
    description: Optional[str] = Field(None, description="描述")
    model_type: str = Field(..., description="模型类型：chat, embedding, vision, audio, multimodal")
    capabilities: Optional[AIModelCapabilities] = Field(None, description="模型能力配置")
    context_window: Optional[int] = Field(None, description="上下文窗口大小")
    token_limit: Optional[int] = Field(None, description="最大 token 数")
    input_price_per_1k_tokens: Optional[float] = Field(None, description="每千 token 输入价格（美元）")
    output_price_per_1k_tokens: Optional[float] = Field(None, description="每千 token 输出价格（美元）")
    
    # 新增定价字段
    image_price_per_1k_tokens: Optional[float] = Field(None, description="每千图像token价格（美元）")
    audio_price_per_minute: Optional[float] = Field(None, description="每分钟音频价格（美元）")
    video_price_per_minute: Optional[float] = Field(None, description="每分钟视频价格（美元）")
    
    # 性能参数
    average_response_time: Optional[float] = Field(None, description="平均响应时间(秒)")
    max_concurrent_requests: Optional[int] = Field(None, description="最大并发请求数")
    rate_limit_per_minute: Optional[int] = Field(None, description="每分钟请求限制")
    
    status: str = Field("active", description="状态：active, inactive, deprecated")
    is_builtin: bool = Field(False, description="是否内置模型")
    
    @validator('model_type')
    def validate_model_type(cls, v):
        allowed_types = ['chat', 'embedding', 'vision', 'audio', 'multimodal', 'code', 'image_generation', 'reasoning']
        if v not in allowed_types:
            raise ValueError(f'model_type must be one of {allowed_types}')
        return v

class AIModelCreate(AIModelBase):
    """创建 AI 模型 Schema"""
    pass

class AIModelUpdate(BaseModel):
    """更新 AI 模型 Schema"""
    display_name: Optional[str] = Field(None, description="显示名称")
    description: Optional[str] = Field(None, description="描述")
    capabilities: Optional[AIModelCapabilities] = Field(None, description="模型能力配置")
    context_window: Optional[int] = Field(None, description="上下文窗口大小")
    token_limit: Optional[int] = Field(None, description="最大 token 数")
    input_price_per_1k_tokens: Optional[float] = Field(None, description="每千 token 输入价格（美元）")
    output_price_per_1k_tokens: Optional[float] = Field(None, description="每千 token 输出价格（美元）")
    image_price_per_1k_tokens: Optional[float] = Field(None, description="每千图像token价格（美元）")
    audio_price_per_minute: Optional[float] = Field(None, description="每分钟音频价格（美元）")
    video_price_per_minute: Optional[float] = Field(None, description="每分钟视频价格（美元）")
    average_response_time: Optional[float] = Field(None, description="平均响应时间(秒)")
    max_concurrent_requests: Optional[int] = Field(None, description="最大并发请求数")
    rate_limit_per_minute: Optional[int] = Field(None, description="每分钟请求限制")
    status: Optional[str] = Field(None, description="状态：active, inactive, deprecated")

class AIModelInDB(AIModelBase):
    """数据库中的 AI 模型 Schema"""
    id: uuid.UUID
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    # 关联数据
    provider_name: Optional[str] = None
    provider_display_name: Optional[str] = None

    class Config:
        model_config = {"from_attributes": True}

class AIModelResponse(BaseModel):
    """AI 模型响应 Schema"""
    success: bool = True
    message: Optional[str] = None
    data: Optional[AIModelInDB] = None

class AIModelListResponse(BaseModel):
    """AI 模型列表响应 Schema"""
    success: bool = True
    message: Optional[str] = None
    data: List[AIModelInDB] = []
    total: int = 0
    page: int = 1
    page_size: int = 20
