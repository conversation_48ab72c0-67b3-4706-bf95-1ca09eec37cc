import React, { useState, useEffect } from 'react';
import {
  Card, Row, Col, Table, Button, Space, Tag, Progress, 
  Statistic, Alert, Tabs, Select, DatePicker, Input,
  Modal, Form, message, Tooltip, Badge, Empty, Spin
} from 'antd';
import {
  ShoppingOutlined, RiseOutlined, DollarOutlined,
  FireOutlined, ThunderboltOutlined, SyncOutlined,
  Bar<PERSON>hartOutlined, Line<PERSON>hartOutlined, Pie<PERSON>hartOutlined,
  SearchOutlined, FilterOutlined, ExportOutlined,
  RobotOutlined, GlobalOutlined, ShopOutlined,
  ShoppingCartOutlined, ApartmentOutlined
} from '@ant-design/icons';
import { Line, Column, Pie } from '@ant-design/plots';
import { api } from '../../../../services/api';
import PurchaseAnalysis from './PurchaseAnalysis';
import SupplyChainAnalysis from './SupplyChainAnalysis';

const { TabPane } = Tabs;
const { Option } = Select;
const { RangePicker } = DatePicker;
const { Search } = Input;

/**
 * 商品分析组件
 * 分析商品竞争力、定价策略、热销预测等
 */
const ProductAnalysis = () => {
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('competitiveness');
  const [productData, setProductData] = useState([]);
  const [competitivenessData, setCompetitivenessData] = useState([]);
  const [pricingData, setPricingData] = useState([]);
  const [trendData, setTrendData] = useState([]);
  const [externalData, setExternalData] = useState([]);
  const [filters, setFilters] = useState({
    category: 'all',
    dateRange: null,
    keyword: ''
  });
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    fetchProductAnalysisData();
  }, [filters]);

  // 获取商品分析数据
  const fetchProductAnalysisData = async () => {
    try {
      setLoading(true);
      
      // 获取项目ID
      const projectId = localStorage.getItem('project_id');
      if (!projectId) {
        throw new Error('未找到项目ID');
      }
      
      const params = {
        category: filters.category,
        dateRange: filters.dateRange,
        keyword: filters.keyword
      };

      // 使用真实的API端点
      const response = await api.get(`/project/${projectId}/business-analysis/product-analysis`, { params });
      
      if (response && response.data) {
        setProductData(response.data.products || []);
        setCompetitivenessData(response.data.competitiveness || []);
        setPricingData(response.data.pricing || []);
        setTrendData(response.data.trends || []);
        setExternalData(response.data.external || []);
      }
    } catch (error) {
      console.error('获取商品分析数据失败:', error);
      message.error('获取数据失败');
    } finally {
      setLoading(false);
    }
  };

  // 刷新数据
  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchProductAnalysisData();
    setRefreshing(false);
    message.success('数据已刷新');
  };

  // 竞争力分析表格列
  const competitivenessColumns = [
    {
      title: '商品名称',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      fixed: 'left',
      render: (text, record) => (
        <Space>
          <img 
            src={record.image || '/placeholder-product.png'} 
            alt={text}
            style={{ width: 32, height: 32, borderRadius: 4 }}
          />
          <div>
            <div style={{ fontWeight: 500 }}>{text}</div>
            <div style={{ fontSize: 12, color: '#666' }}>{record.sku}</div>
          </div>
        </Space>
      )
    },
    {
      title: '竞争力指数',
      dataIndex: 'competitivenessScore',
      key: 'competitivenessScore',
      width: 120,
      render: (score) => (
        <div>
          <Progress 
            percent={score} 
            size="small" 
            strokeColor={
              score >= 80 ? '#52c41a' : 
              score >= 60 ? '#faad14' : '#ff4d4f'
            }
          />
          <div style={{ fontSize: 12, marginTop: 4 }}>
            {score >= 80 ? '优秀' : score >= 60 ? '良好' : '需改进'}
          </div>
        </div>
      )
    },
    {
      title: '市场价格',
      dataIndex: 'marketPrice',
      key: 'marketPrice',
      width: 100,
      render: (price) => `¥${price?.toFixed(2) || 0}`
    },
    {
      title: '我方价格',
      dataIndex: 'ourPrice',
      key: 'ourPrice',
      width: 100,
      render: (price, record) => {
        const diff = record.marketPrice ? ((price - record.marketPrice) / record.marketPrice * 100) : 0;
        return (
          <div>
            <div>¥{price?.toFixed(2) || 0}</div>
            <div style={{ 
              fontSize: 12, 
              color: diff > 0 ? '#ff4d4f' : diff < 0 ? '#52c41a' : '#666' 
            }}>
              {diff > 0 ? '+' : ''}{diff.toFixed(1)}%
            </div>
          </div>
        );
      }
    },
    {
      title: '销量趋势',
      dataIndex: 'salesTrend',
      key: 'salesTrend',
      width: 100,
      render: (trend) => (
        <Tag color={trend > 0 ? 'green' : trend < 0 ? 'red' : 'default'}>
          {trend > 0 ? '↗' : trend < 0 ? '↘' : '→'} {Math.abs(trend)}%
        </Tag>
      )
    },
    {
      title: 'AI建议',
      dataIndex: 'aiSuggestion',
      key: 'aiSuggestion',
      width: 200,
      render: (suggestion) => (
        <Tooltip title={suggestion}>
          <div style={{ 
            maxWidth: 180, 
            overflow: 'hidden', 
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap'
          }}>
            <RobotOutlined style={{ marginRight: 4, color: '#1890ff' }} />
            {suggestion}
          </div>
        </Tooltip>
      )
    },
    {
      title: '操作',
      key: 'action',
      width: 120,
      fixed: 'right',
      render: (_, record) => (
        <Space size="small">
          <Button size="small" type="link">详情</Button>
          <Button size="small" type="link">调价</Button>
        </Space>
      )
    }
  ];

  // 定价建议表格列
  const pricingColumns = [
    {
      title: '商品名称',
      dataIndex: 'name',
      key: 'name',
      width: 200
    },
    {
      title: '当前价格',
      dataIndex: 'currentPrice',
      key: 'currentPrice',
      width: 100,
      render: (price) => `¥${price?.toFixed(2) || 0}`
    },
    {
      title: '建议价格',
      dataIndex: 'suggestedPrice',
      key: 'suggestedPrice',
      width: 100,
      render: (price, record) => {
        const diff = record.currentPrice ? ((price - record.currentPrice) / record.currentPrice * 100) : 0;
        return (
          <div>
            <div style={{ fontWeight: 500 }}>¥{price?.toFixed(2) || 0}</div>
            <div style={{ 
              fontSize: 12, 
              color: diff > 0 ? '#ff4d4f' : diff < 0 ? '#52c41a' : '#666' 
            }}>
              {diff > 0 ? '+' : ''}{diff.toFixed(1)}%
            </div>
          </div>
        );
      }
    },
    {
      title: '预期影响',
      dataIndex: 'expectedImpact',
      key: 'expectedImpact',
      width: 150,
      render: (impact) => (
        <div>
          <div>销量: <span style={{ color: impact.sales > 0 ? '#52c41a' : '#ff4d4f' }}>
            {impact.sales > 0 ? '+' : ''}{impact.sales}%
          </span></div>
          <div>利润: <span style={{ color: impact.profit > 0 ? '#52c41a' : '#ff4d4f' }}>
            {impact.profit > 0 ? '+' : ''}{impact.profit}%
          </span></div>
        </div>
      )
    },
    {
      title: '建议理由',
      dataIndex: 'reason',
      key: 'reason',
      render: (reason) => (
        <Tooltip title={reason}>
          <div style={{ 
            maxWidth: 200, 
            overflow: 'hidden', 
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap'
          }}>
            {reason}
          </div>
        </Tooltip>
      )
    }
  ];

  // 渲染竞争力分析
  const renderCompetitivenessAnalysis = () => (
    <div>
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title="平均竞争力指数"
              value={75.6}
              suffix="分"
              prefix={<RiseOutlined style={{ color: '#1890ff' }} />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title="优势商品数量"
              value={23}
              suffix="个"
              prefix={<FireOutlined style={{ color: '#52c41a' }} />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title="需改进商品"
              value={8}
              suffix="个"
              prefix={<ThunderboltOutlined style={{ color: '#faad14' }} />}
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
      </Row>

      <Card title="商品竞争力详情">
        <Table
          columns={competitivenessColumns}
          dataSource={competitivenessData}
          rowKey="id"
          scroll={{ x: 1200 }}
          pagination={{ pageSize: 10 }}
          loading={loading}
        />
      </Card>
    </div>
  );

  // 渲染定价建议
  const renderPricingSuggestions = () => (
    <div>
      <Alert
        message="AI定价建议"
        description="基于市场数据、竞品分析和销售历史，为您提供智能定价建议。"
        type="info"
        showIcon
        style={{ marginBottom: 16 }}
      />

      <Card title="定价建议列表">
        <Table
          columns={pricingColumns}
          dataSource={pricingData}
          rowKey="id"
          pagination={{ pageSize: 10 }}
          loading={loading}
        />
      </Card>
    </div>
  );

  // 渲染热销预测
  const renderTrendPrediction = () => {
    // 确保trendData是数组，如果是对象则取其data属性
    const trendsArray = Array.isArray(trendData) ? trendData : (trendData?.data || []);
    
    const trendChartData = trendsArray.map((item, index) => ({
      date: item.date || `2024-${String(index + 1).padStart(2, '0')}`,
      predicted: item.predictedSales || item.sales || 1000 + index * 50,
      actual: item.actualSales || item.sales || 1000 + index * 50
    }));

    const trendConfig = {
      data: trendChartData,
      xField: 'date',
      yField: 'predicted',
      smooth: true,
      animation: {
        appear: {
          animation: 'path-in',
          duration: 1000,
        },
      },
    };

    // 模拟热销商品数据
    const hotProducts = [
      { name: '商品A', growthRate: 25.6 },
      { name: '商品B', growthRate: 18.3 },
      { name: '商品C', growthRate: 15.7 },
      { name: '商品D', growthRate: 12.4 },
      { name: '商品E', growthRate: 9.8 }
    ];

    return (
      <div>
        <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
          <Col xs={24} lg={16}>
            <Card title="销量趋势预测">
              <Line {...trendConfig} height={300} />
            </Card>
          </Col>
          <Col xs={24} lg={8}>
            <Card title="热销商品预测">
              <div className="hot-products">
                {hotProducts.map((item, index) => (
                  <div key={index} className="hot-product-item" style={{ 
                    padding: '12px 0', 
                    borderBottom: index < hotProducts.length - 1 ? '1px solid #f0f0f0' : 'none' 
                  }}>
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <div>
                        <Badge count={index + 1} style={{ backgroundColor: '#1890ff' }} />
                        <span style={{ marginLeft: 8, fontWeight: 500 }}>{item.name}</span>
                      </div>
                      <div style={{ textAlign: 'right' }}>
                        <div style={{ color: '#52c41a', fontWeight: 500 }}>
                          +{item.growthRate}%
                        </div>
                        <div style={{ fontSize: 12, color: '#666' }}>
                          预测增长
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </Card>
          </Col>
        </Row>
      </div>
    );
  };

  // 渲染外部数据对比
  const renderExternalDataComparison = () => (
    <div>
      <Alert
        message="外部数据源"
        description="整合电商平台、同城实体店、社交媒体等多渠道数据，提供全面的市场对比分析。"
        type="info"
        showIcon
        style={{ marginBottom: 16 }}
      />

      <Row gutter={[16, 16]}>
        <Col xs={24} lg={12}>
          <Card title={
            <Space>
              <GlobalOutlined />
              电商平台对比
            </Space>
          }>
            <div className="external-data-list">
              {externalData.ecommerce?.map((item, index) => (
                <div key={index} style={{ 
                  padding: '12px 0', 
                  borderBottom: index < externalData.ecommerce.length - 1 ? '1px solid #f0f0f0' : 'none' 
                }}>
                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                    <div>
                      <div style={{ fontWeight: 500 }}>{item.platform}</div>
                      <div style={{ fontSize: 12, color: '#666' }}>{item.productName}</div>
                    </div>
                    <div style={{ textAlign: 'right' }}>
                      <div style={{ fontWeight: 500 }}>¥{item.price}</div>
                      <div style={{ fontSize: 12, color: '#666' }}>销量: {item.sales}</div>
                    </div>
                  </div>
                </div>
              )) || <Empty description="暂无数据" />}
            </div>
          </Card>
        </Col>

        <Col xs={24} lg={12}>
          <Card title={
            <Space>
              <ShopOutlined />
              同城实体店对比
            </Space>
          }>
            <div className="external-data-list">
              {externalData.offline?.map((item, index) => (
                <div key={index} style={{ 
                  padding: '12px 0', 
                  borderBottom: index < externalData.offline.length - 1 ? '1px solid #f0f0f0' : 'none' 
                }}>
                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                    <div>
                      <div style={{ fontWeight: 500 }}>{item.storeName}</div>
                      <div style={{ fontSize: 12, color: '#666' }}>{item.location}</div>
                    </div>
                    <div style={{ textAlign: 'right' }}>
                      <div style={{ fontWeight: 500 }}>¥{item.price}</div>
                      <div style={{ fontSize: 12, color: '#666' }}>距离: {item.distance}km</div>
                    </div>
                  </div>
                </div>
              )) || <Empty description="暂无数据" />}
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  );

  if (loading && !refreshing) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>加载商品分析数据...</div>
      </div>
    );
  }

  return (
    <div className="product-analysis">
      {/* 筛选工具栏 */}
      <Card style={{ marginBottom: 16 }}>
        <Row gutter={[16, 16]} align="middle">
          <Col xs={24} sm={8} md={6}>
            <Select
              placeholder="选择商品类别"
              style={{ width: '100%' }}
              value={filters.category}
              onChange={(value) => setFilters({ ...filters, category: value })}
            >
              <Option value="all">全部类别</Option>
              <Option value="food">食品饮料</Option>
              <Option value="daily">日用百货</Option>
              <Option value="electronics">电子产品</Option>
            </Select>
          </Col>
          <Col xs={24} sm={8} md={6}>
            <RangePicker
              style={{ width: '100%' }}
              onChange={(dates) => setFilters({ ...filters, dateRange: dates })}
            />
          </Col>
          <Col xs={24} sm={8} md={6}>
            <Search
              placeholder="搜索商品"
              onSearch={(value) => setFilters({ ...filters, keyword: value })}
              style={{ width: '100%' }}
            />
          </Col>
          <Col xs={24} sm={24} md={6}>
            <Space>
              <Button 
                icon={<SyncOutlined />} 
                onClick={handleRefresh}
                loading={refreshing}
              >
                刷新
              </Button>
              <Button icon={<ExportOutlined />}>
                导出
              </Button>
            </Space>
          </Col>
        </Row>
      </Card>

      {/* 分析内容Tab */}
      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane 
          tab={
            <Space>
              <RiseOutlined />
              竞争力分析
            </Space>
          } 
          key="competitiveness"
        >
          {renderCompetitivenessAnalysis()}
        </TabPane>

        <TabPane 
          tab={
            <Space>
              <DollarOutlined />
              定价建议
            </Space>
          } 
          key="pricing"
        >
          {renderPricingSuggestions()}
        </TabPane>

        <TabPane 
          tab={
            <Space>
              <FireOutlined />
              热销预测
            </Space>
          } 
          key="trends"
        >
          {renderTrendPrediction()}
        </TabPane>

        <TabPane 
          tab={
            <Space>
              <GlobalOutlined />
              外部数据对比
            </Space>
          } 
          key="external"
        >
          {renderExternalDataComparison()}
        </TabPane>

        <TabPane 
          tab={
            <Space>
              <ShoppingCartOutlined />
              采购分析
            </Space>
          } 
          key="purchase"
        >
          <PurchaseAnalysis />
        </TabPane>

        <TabPane 
          tab={
            <Space>
              <ApartmentOutlined />
              供应链分析
            </Space>
          } 
          key="supply-chain"
        >
          <SupplyChainAnalysis />
        </TabPane>
      </Tabs>
    </div>
  );
};

export default ProductAnalysis; 