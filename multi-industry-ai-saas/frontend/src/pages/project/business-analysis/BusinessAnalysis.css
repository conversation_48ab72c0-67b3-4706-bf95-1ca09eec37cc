.business-analysis {
  padding: 24px;
  background: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 24px;
}

.page-header .ant-typography {
  margin-bottom: 8px;
}

.ai-insights {
  max-height: 400px;
  overflow-y: auto;
}

.insight-item {
  padding: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  margin-bottom: 12px;
  background: #fafafa;
  transition: all 0.3s ease;
}

.insight-item:hover {
  border-color: #1890ff;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.1);
}

.insight-item:last-child {
  margin-bottom: 0;
}

.insight-header {
  margin-bottom: 8px;
}

.insight-content {
  margin-bottom: 8px;
  line-height: 1.6;
}

.insight-suggestion {
  padding-top: 8px;
  border-top: 1px solid #f0f0f0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .business-analysis {
    padding: 16px;
  }
  
  .ai-insights {
    max-height: 300px;
  }
  
  .insight-item {
    padding: 12px;
  }
}

/* 统计卡片样式 */
.ant-statistic-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.ant-statistic-content {
  font-size: 24px;
  font-weight: 600;
}

/* Tab样式优化 */
.ant-tabs-tab {
  font-size: 16px;
  font-weight: 500;
}

.ant-tabs-tab-active {
  color: #1890ff !important;
}

/* 卡片阴影效果 */
.ant-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border-radius: 8px;
  transition: box-shadow 0.3s ease;
}

.ant-card:hover {
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
} 