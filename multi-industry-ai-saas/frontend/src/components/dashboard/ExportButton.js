import React, { useState } from 'react';
import { Button, Dropdown, Menu, message } from 'antd';
import { DownloadOutlined, FileExcelOutlined, FileTextOutlined } from '@ant-design/icons';

/**
 * 数据导出按钮组件
 * 支持导出CSV格式
 * @param {Object} props - 组件属性
 * @param {Array} props.data - 要导出的数据
 * @param {string} props.filename - 导出文件名
 */
const ExportButton = ({ data, filename = 'export' }) => {
  const [loading, setLoading] = useState(false);

  // 处理导出
  const handleExport = () => {
    setLoading(true);

    try {
      exportToCSV();
    } catch (error) {
      console.error('导出失败:', error);
      message.error('导出失败，请重试');
    } finally {
      setLoading(false);
    }
  };

  // 导出为CSV
  const exportToCSV = () => {
    if (!data || data.length === 0) {
      message.warning('没有可导出的数据');
      return;
    }

    // 获取表头
    const headers = Object.keys(data[0]);

    // 创建CSV内容
    let csvContent = headers.join(',') + '\n';

    // 添加数据行
    data.forEach(item => {
      const row = headers.map(header => {
        // 处理包含逗号、引号或换行符的字段
        const cell = String(item[header] || '');
        if (cell.includes(',') || cell.includes('"') || cell.includes('\n')) {
          return `"${cell.replace(/"/g, '""')}"`;
        }
        return cell;
      });
      csvContent += row.join(',') + '\n';
    });

    // 创建Blob对象
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });

    // 创建下载链接
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `${filename}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    message.success('CSV导出成功');
  };

  // 导出菜单
  const menu = (
    <Menu onClick={() => handleExport()}>
      <Menu.Item key="csv" icon={<FileExcelOutlined />}>
        导出为CSV
      </Menu.Item>
    </Menu>
  );

  return (
    <Dropdown overlay={menu} trigger={['click']}>
      <Button
        icon={<DownloadOutlined />}
        loading={loading}
      >
        {loading ? '正在导出...' : '导出'}
      </Button>
    </Dropdown>
  );
};

export default ExportButton;
