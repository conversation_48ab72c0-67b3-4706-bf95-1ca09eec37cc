#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
MCP服务器直接访问中间件

支持绕过代理的直接访问，同时保持与现有代理模式的兼容性
"""

import json
import logging
import time
from typing import Dict, Any, Optional
from fastapi import Request, HTTPException
from fastapi.responses import JSONResponse

logger = logging.getLogger(__name__)

class DirectAccessMiddleware:
    """MCP服务器直接访问中间件"""
    
    def __init__(self):
        self.proxy_fallback_enabled = True
        self.backend_url = "http://retail-ai-saas-backend:8000"
    
    async def __call__(self, request: Request, call_next):
        """中间件处理逻辑"""
        try:
            # 检查是否为直接访问请求
            auth_type = request.headers.get("x-mcp-auth-type")
            
            if auth_type in ["direct_access", "sapi_converted"]:
                # 直接访问模式 - 从Traefik鉴权中间件获取用户信息
                return await self._handle_direct_access(request, call_next)
            else:
                # 代理模式或未知模式 - 回退到代理处理
                if self.proxy_fallback_enabled:
                    return await self._handle_proxy_fallback(request)
                else:
                    raise HTTPException(status_code=401, detail="Authentication required")
        
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Direct access middleware error: {str(e)}", exc_info=True)
            # 发生错误时回退到代理模式
            if self.proxy_fallback_enabled:
                return await self._handle_proxy_fallback(request)
            else:
                raise HTTPException(status_code=500, detail="Middleware error")
    
    async def _handle_direct_access(self, request: Request, call_next):
        """处理直接访问请求"""
        try:
            # 从Traefik鉴权中间件设置的头部获取用户信息
            user_id = request.headers.get("x-mcp-user-id")
            project_id = request.headers.get("x-mcp-project-id")
            session_id = request.headers.get("x-mcp-session-id")
            system_token = request.headers.get("x-mcp-system-token")
            permissions_str = request.headers.get("x-mcp-permissions", "[]")
            auth_type = request.headers.get("x-mcp-auth-type")
            
            if not user_id:
                raise HTTPException(status_code=401, detail="Missing user information from auth")
            
            # 解析权限
            try:
                permissions = json.loads(permissions_str)
            except json.JSONDecodeError:
                permissions = []
            
            # 将用户信息注入到请求状态中
            request.state.user_id = user_id
            request.state.project_id = project_id
            request.state.session_id = session_id or f"direct_{int(time.time())}"
            request.state.system_token = system_token
            request.state.permissions = permissions
            request.state.auth_type = auth_type
            request.state.is_direct_access = True
            
            logger.info(f"Direct access: user={user_id}, project={project_id}, session={request.state.session_id}")
            
            # 继续处理请求
            response = await call_next(request)
            
            # 添加直接访问标识头
            response.headers["X-MCP-Access-Mode"] = "direct"
            response.headers["X-MCP-Session-ID"] = request.state.session_id
            
            return response
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error in direct access handling: {str(e)}", exc_info=True)
            raise HTTPException(status_code=500, detail="Direct access processing error")
    
    async def _handle_proxy_fallback(self, request: Request):
        """处理代理回退请求"""
        try:
            import httpx
            
            # 构建代理请求URL
            proxy_url = f"{self.backend_url}{request.url.path}"
            if request.url.query:
                proxy_url += f"?{request.url.query}"
            
            # 准备代理请求头
            proxy_headers = dict(request.headers)
            proxy_headers.pop("host", None)  # 移除host头避免冲突
            
            # 读取请求体
            body = await request.body()
            
            async with httpx.AsyncClient(timeout=30.0) as client:
                # 转发请求到后端代理
                proxy_response = await client.request(
                    method=request.method,
                    url=proxy_url,
                    headers=proxy_headers,
                    content=body
                )
                
                # 构建响应
                response_headers = dict(proxy_response.headers)
                response_headers["X-MCP-Access-Mode"] = "proxy_fallback"
                
                return JSONResponse(
                    status_code=proxy_response.status_code,
                    content=proxy_response.json() if proxy_response.headers.get("content-type", "").startswith("application/json") else proxy_response.text,
                    headers=response_headers
                )
                
        except Exception as e:
            logger.error(f"Proxy fallback error: {str(e)}", exc_info=True)
            raise HTTPException(status_code=502, detail="Proxy fallback failed")

class DirectAccessSessionManager:
    """直接访问会话管理器"""
    
    def __init__(self):
        self.sessions = {}
        self.session_ttl = 1800  # 30分钟
    
    def create_session_from_request(self, request: Request) -> Dict[str, Any]:
        """从请求创建会话对象"""
        if not hasattr(request.state, 'is_direct_access'):
            raise ValueError("Request is not a direct access request")
        
        session_data = {
            "session_id": request.state.session_id,
            "user_id": request.state.user_id,
            "project_id": request.state.project_id,
            "system_token": request.state.system_token,
            "permissions": request.state.permissions,
            "auth_type": request.state.auth_type,
            "created_at": time.time(),
            "last_activity": time.time(),
            "is_direct_access": True
        }
        
        # 缓存会话
        self.sessions[session_data["session_id"]] = session_data
        
        return session_data
    
    def get_session(self, session_id: str) -> Optional[Dict[str, Any]]:
        """获取会话"""
        session = self.sessions.get(session_id)
        if session:
            # 检查是否过期
            if time.time() - session["last_activity"] > self.session_ttl:
                self.sessions.pop(session_id, None)
                return None
            
            # 更新活动时间
            session["last_activity"] = time.time()
            return session
        
        return None
    
    def cleanup_expired_sessions(self):
        """清理过期会话"""
        now = time.time()
        expired_sessions = [
            session_id for session_id, session in self.sessions.items()
            if now - session["last_activity"] > self.session_ttl
        ]
        
        for session_id in expired_sessions:
            self.sessions.pop(session_id, None)
        
        if expired_sessions:
            logger.info(f"Cleaned up {len(expired_sessions)} expired direct access sessions")

# 全局实例
direct_access_middleware = DirectAccessMiddleware()
session_manager = DirectAccessSessionManager() 