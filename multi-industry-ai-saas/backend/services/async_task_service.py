#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
异步任务服务
"""

import asyncio
import uuid
import json
import logging
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List, Callable
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update
from sqlalchemy.orm import selectinload

from models.task import AsyncTask
from services.notification_service import NotificationService
from schemas.task_execution import TaskExecutionResult

logger = logging.getLogger(__name__)

class AsyncTaskService:
    """异步任务服务"""
    
    # 任务执行器注册表
    _task_executors: Dict[str, Callable] = {}
    
    @classmethod
    def register_executor(cls, task_type: str, executor: Callable):
        """注册任务执行器"""
        cls._task_executors[task_type] = executor
        logger.info(f"注册任务执行器: {task_type}")
    
    @classmethod
    def get_registered_executors(cls) -> Dict[str, str]:
        """获取已注册的任务执行器列表"""
        return {task_type: str(executor) for task_type, executor in cls._task_executors.items()}
    
    @staticmethod
    async def create_task(
        db: AsyncSession,
        task_type: str,
        task_name: str,
        user_id: uuid.UUID,
        input_data: Dict[str, Any],
        project_id: Optional[uuid.UUID] = None,
        tenant_id: Optional[uuid.UUID] = None,
        description: Optional[str] = None,
        priority: int = 0,
        timeout_seconds: Optional[int] = None,
        max_retries: int = 3
    ) -> AsyncTask:
        """创建异步任务"""
        try:
            task = AsyncTask(
                task_type=task_type,
                task_name=task_name,
                description=description,
                user_id=user_id,
                project_id=project_id,
                tenant_id=tenant_id,
                input_data=input_data,
                priority=priority,
                timeout_seconds=timeout_seconds,
                max_retries=max_retries,
                status="pending"
            )
            
            db.add(task)
            await db.commit()
            await db.refresh(task)
            
            logger.info(f"创建异步任务: {task.id}, 类型: {task_type}, 名称: {task_name}")
            
            # 启动任务执行
            asyncio.create_task(AsyncTaskService._execute_task(task.id))
            
            return task
            
        except Exception as e:
            logger.error(f"创建异步任务失败: {e}")
            await db.rollback()
            raise
    
    @staticmethod
    async def get_task(db: AsyncSession, task_id: uuid.UUID) -> Optional[AsyncTask]:
        """获取任务信息"""
        try:
            stmt = select(AsyncTask).where(AsyncTask.id == task_id)
            result = await db.execute(stmt)
            return result.scalar_one_or_none()
        except Exception as e:
            logger.error(f"获取任务失败: {e}")
            return None
    
    @staticmethod
    async def update_task(
        db: AsyncSession,
        task_id: uuid.UUID,
        status: Optional[str] = None,
        progress: Optional[int] = None,
        message: Optional[str] = None,
        result: Optional[Dict[str, Any]] = None,
        error_message: Optional[str] = None,
        retry_count: Optional[int] = None
    ) -> Optional[AsyncTask]:
        """更新任务状态"""
        try:
            # 获取任务
            query = select(AsyncTask).where(AsyncTask.id == task_id)
            result_obj = await db.execute(query)
            task = result_obj.scalar_one_or_none()
            
            if not task:
                logger.error(f"任务 {task_id} 不存在")
                return None
            
            # 更新字段
            update_data = {}
            if status is not None:
                update_data['status'] = status
                
                # 设置开始时间
                if status == 'running' and not task.started_at:
                    update_data['started_at'] = datetime.now()
                    
                # 设置完成时间
                elif status in ['success', 'failed', 'cancelled'] and not task.completed_at:
                    update_data['completed_at'] = datetime.now()
            
            if progress is not None:
                update_data['progress'] = max(0, min(100, progress))
            
            if message is not None:
                update_data['description'] = message
                
            if result is not None:
                update_data['result_data'] = result
                
            if error_message is not None:
                update_data['error_message'] = error_message
                
            if retry_count is not None:
                update_data['retry_count'] = retry_count
            
            update_data['updated_at'] = datetime.now()
            
            # 执行更新
            update_query = update(AsyncTask).where(AsyncTask.id == task_id).values(**update_data)
            await db.execute(update_query)
            await db.commit()
            
            # 重新获取更新后的任务
            await db.refresh(task)
            
            logger.info(f"任务 {task_id} 状态更新成功: {status}")
            
            # 发送WebSocket任务状态更新推送（在事务提交后）
            if status is not None:
                # 异步发送推送，避免阻塞主流程
                asyncio.create_task(AsyncTaskService._send_task_status_update_async(task_id, status))
            
            return task
            
        except Exception as e:
            logger.error(f"更新任务 {task_id} 失败: {e}")
            await db.rollback()
            return None
    
    @staticmethod
    async def update_task_status(
        db: AsyncSession,
        task_id: uuid.UUID,
        status: str,
        progress: Optional[int] = None,
        message: Optional[str] = None
    ) -> Optional[AsyncTask]:
        """简化的任务状态更新方法"""
        return await AsyncTaskService.update_task(
            db=db,
            task_id=task_id,
            status=status,
            progress=progress,
            message=message
        )
    
    @staticmethod
    async def _execute_task(task_id: uuid.UUID):
        """执行任务"""
        from db.database import AsyncSessionLocal
        
        logger.info(f"异步任务 {task_id} 开始执行")
        
        async with AsyncSessionLocal() as db:
            task_status_to_set_on_exception = "failed" # Default status
            error_message_on_exception = "任务执行器异常"
            
            try:
                # 获取任务信息
                task = await AsyncTaskService.get_task(db, task_id)
                if not task:
                    logger.error(f"任务不存在: {task_id}")
                    return
                
                logger.info(f"任务 {task_id} 当前状态: {task.status}, 类型: {task.task_type}")
                
                # 检查任务执行器是否存在
                executor = AsyncTaskService._task_executors.get(task.task_type)
                if not executor:
                    logger.error(f"未找到任务执行器: {task.task_type}")
                    await AsyncTaskService.update_task(
                        db, task_id, "failed", 
                        error_message=f"未找到任务执行器: {task.task_type}"
                    )
                    return
                
                logger.info(f"任务 {task_id} 开始执行，执行器: {executor}")
                
                # 更新任务状态为运行中
                await AsyncTaskService.update_task(db, task_id, "running", 0)
                logger.info(f"任务 {task_id} 状态已更新为 running")
                
                # 执行任务
                try:
                    if task.timeout_seconds and task.timeout_seconds > 0:
                        execution_result: TaskExecutionResult = await asyncio.wait_for(
                            executor(db, task), 
                            timeout=task.timeout_seconds
                        )
                    else:
                        execution_result: TaskExecutionResult = await executor(db, task)
                    
                    logger.info(f"任务 {task_id} 执行器返回: success={execution_result.success}")

                    if execution_result.success:
                        # 任务业务逻辑成功
                        await AsyncTaskService.update_task(
                            db, task_id, "success", 100, result=execution_result.data
                        )
                        logger.info(f"任务 {task_id} 标记为成功完成")
                    else:
                        # 任务业务逻辑失败
                        error_message = execution_result.data.get("error", "任务执行失败，但未提供明确错误信息。")
                        logger.error(f"任务 {task_id} 业务逻辑失败: {error_message}")
                        await AsyncTaskService.update_task(
                            db, task_id, "failed", 
                            error_message=error_message,
                            result=execution_result.data # Store full error context
                        )

                except asyncio.TimeoutError:
                    logger.error(f"任务 {task_id} 执行超时")
                    await AsyncTaskService.update_task(db, task_id, "failed", error_message="任务执行超时")

                except Exception as e:
                    # Catch unexpected errors during executor execution (e.g., programming errors in the executor)
                    logger.error(f"任务 {task_id} 执行期间发生意外错误: {e}", exc_info=True)
                    # Check for retries for unexpected errors
                    if task.retry_count < task.max_retries:
                        new_retry_count = task.retry_count + 1
                        logger.info(f"任务 {task_id} 准备重试第{new_retry_count}次 (当前重试次数: {task.retry_count}, 最大重试次数: {task.max_retries})")
                        
                        # 更新任务状态为pending，并增加重试次数
                        await AsyncTaskService.update_task(
                            db, task_id, "pending",
                            error_message=f"重试第{new_retry_count}次: {str(e)}",
                            retry_count=new_retry_count
                        )
                        
                        # 延迟一段时间后重新执行任务
                        delay = min(2 ** new_retry_count, 60)  # 指数退避，最多60秒
                        logger.info(f"任务 {task_id} 将在 {delay} 秒后重试")
                        
                        # 延迟后再重新创建任务执行，避免在当前流程中递归
                        asyncio.create_task(AsyncTaskService._delayed_retry_task(task_id, delay))
                    else:
                        logger.error(f"任务 {task_id} 已达到最大重试次数 ({task.max_retries})，标记为失败")
                        await AsyncTaskService.update_task(
                            db, task_id, "failed", 
                            error_message=f"任务因意外错误失败: {str(e)}"
                        )
            
            except asyncio.CancelledError:
                # 这个捕获块处理的是 _execute_task 本身（或其内部的 setup/teardown）被取消的情况
                # 或者是从内部 try...except asyncio.CancelledError 中重新抛出的情况
                logger.warning(f"异步任务执行器 {task_id} 被取消。")
                # 尝试将任务标记为 "cancelled"
                # 注意: 如果数据库会话此时已失效，这里可能仍然会出问题。
                # 一个更健壮的模式可能是在服务关闭信号处理程序中集中取消任务，
                # 并允许它们自行完成（不更新DB状态）。
                # 但对于显式取消API调用，更新DB状态是合理的。
                try:
                    # 重新获取最新的 task 对象，因为其状态可能已在别处被改变
                    current_task = await AsyncTaskService.get_task(db, task_id)
                    if current_task and current_task.status not in ["success", "failed", "cancelled"]:
                        logger.info(f"尝试将任务 {task_id} 状态更新为 'cancelled' (因执行器取消)")
                        await AsyncTaskService.update_task(db, task_id, "cancelled", error_message="任务被取消")
                except Exception as db_update_err:
                    logger.error(f"在取消任务 {task_id} 后更新其状态为 'cancelled' 时发生错误: {db_update_err}", exc_info=True)
                # CancelledError 不应被视为需要更新为 "failed" 的异常
                # 它应该被传播，或者这里就是任务执行的终点。
            
            except Exception as e:
                logger.error(f"任务执行器顶层异常: {task_id}, 错误: {e}", exc_info=True)
                try:
                    # 重新获取最新的 task 对象，因为其状态可能已在别处被改变
                    current_task = await AsyncTaskService.get_task(db, task_id)
                    # 只有当任务不是因为取消而失败，且尚未完成时才更新
                    if current_task and task_status_to_set_on_exception == "failed" and \
                       current_task.status not in ["success", "failed", "cancelled"]:
                        logger.info(f"尝试将任务 {task_id} 状态更新为 '{task_status_to_set_on_exception}' (因顶层异常)")
                        await AsyncTaskService.update_task(
                            db, task_id, task_status_to_set_on_exception,
                            error_message=f"{error_message_on_exception}: {str(e)}"
                        )
                except Exception as db_update_err:
                    logger.error(f"更新任务 {task_id} 失败状态时发生错误: {db_update_err}", exc_info=True)
            
            finally:
                logger.info(f"异步任务 {task_id} 执行流程结束")
    
    @staticmethod
    async def _delayed_retry_task(task_id: uuid.UUID, delay: float):
        """延迟重试任务"""
        try:
            logger.info(f"任务 {task_id} 延迟 {delay} 秒后重试")
            await asyncio.sleep(delay)
            logger.info(f"任务 {task_id} 开始重试执行")
            await AsyncTaskService._execute_task(task_id)
        except Exception as e:
            logger.error(f"延迟重试任务 {task_id} 失败: {e}", exc_info=True)
    
    @staticmethod
    async def _send_task_status_update_async(task_id: uuid.UUID, status: str):
        """异步发送任务状态更新推送（独立会话）"""
        try:
            from db.database import AsyncSessionLocal
            
            # 使用独立的数据库会话避免冲突
            async with AsyncSessionLocal() as db:
                task = await AsyncTaskService.get_task(db, task_id)
                if not task:
                    logger.warning(f"发送推送时未找到任务: {task_id}")
                    return
                
                await AsyncTaskService._send_task_status_update(db, task)
                
        except Exception as e:
            logger.error(f"异步发送任务状态更新失败: {e}")

    @staticmethod
    async def _send_task_status_update(db: AsyncSession, task: AsyncTask):
        """发送实时任务状态更新推送"""
        try:
            # 直接通过Redis发送WebSocket消息，绕过NotificationService
            from .redis_manager import RedisManager
            
            # 构建任务状态更新消息
            message = {
                "type": "task_update",
                "task_id": str(task.id),
                "status": task.status,
                "progress": task.progress or 0,
                "task_name": task.task_name,
                "task_type": task.task_type,
                "error_message": task.error_message,
                "started_at": task.started_at.isoformat() if task.started_at else None,
                "completed_at": task.completed_at.isoformat() if task.completed_at else None,
                "updated_at": task.updated_at.isoformat() if task.updated_at else None,
                "is_completed": task.status in ["success", "failed", "cancelled", "completed"]
            }
            
            # 如果任务完成，包含结果数据
            if task.status in ["success", "completed"] and task.result_data:
                message["result"] = task.result_data
            
            # 直接通过Redis发送推送
            try:
                redis_manager = await RedisManager.get_instance()
                
                # 发送到用户通道
                if task.user_id:
                    await redis_manager.publish_notification(
                        f"user:{task.user_id}:notifications",
                        message
                    )
                    logger.debug(f"发送任务状态更新到用户 {task.user_id}: {task.id} -> {task.status}")
                
                # 发送到项目通道
                if task.project_id:
                    await redis_manager.publish_notification(
                        f"project:{task.project_id}:notifications", 
                        message
                    )
                    logger.debug(f"发送任务状态更新到项目 {task.project_id}: {task.id} -> {task.status}")
                    
            except Exception as redis_error:
                logger.warning(f"Redis推送失败，这不会影响任务执行: {redis_error}")
                
        except Exception as e:
            logger.error(f"发送任务状态更新失败: {e}")

    @staticmethod
    async def _send_task_notification(db: AsyncSession, task_id: uuid.UUID, status: str):
        """发送任务完成通知（兼容旧方法）"""
        try:
            task = await AsyncTaskService.get_task(db, task_id)
            if not task:
                return
            
            await AsyncTaskService._send_task_status_update(db, task)
                
        except Exception as e:
            logger.error(f"发送任务通知失败: {e}")
    
    @staticmethod
    async def cancel_task(db: AsyncSession, task_id: uuid.UUID) -> bool:
        """取消任务"""
        try:
            task = await AsyncTaskService.get_task(db, task_id)
            if not task:
                return False
            
            if task.status in ["completed", "failed", "cancelled"]:
                return False
            
            await AsyncTaskService.update_task(db, task_id, "cancelled")
            return True
            
        except Exception as e:
            logger.error(f"取消任务失败: {e}")
            return False
    
    @staticmethod
    async def delete_task(db: AsyncSession, task_id: uuid.UUID) -> bool:
        """删除任务"""
        try:
            task = await AsyncTaskService.get_task(db, task_id)
            if not task:
                return False
            
            # 如果任务正在运行，先取消它
            if task.status in ["pending", "running"]:
                await AsyncTaskService.cancel_task(db, task_id)
            
            # 删除任务
            await db.delete(task)
            await db.commit()
            
            logger.info(f"删除任务成功: {task_id}")
            return True
            
        except Exception as e:
            logger.error(f"删除任务失败: {e}")
            await db.rollback()
            return False
    
    @staticmethod
    async def batch_delete_tasks(
        db: AsyncSession, 
        task_ids: List[uuid.UUID],
        user_id: Optional[uuid.UUID] = None,
        project_id: Optional[uuid.UUID] = None
    ) -> Dict[str, int]:
        """批量删除任务"""
        try:
            deleted_count = 0
            failed_count = 0
            
            for task_id in task_ids:
                task = await AsyncTaskService.get_task(db, task_id)
                if not task:
                    failed_count += 1
                    continue
                
                # 权限检查
                if user_id and task.user_id != user_id:
                    failed_count += 1
                    continue
                
                if project_id and task.project_id != project_id:
                    failed_count += 1
                    continue
                
                # 删除任务
                if await AsyncTaskService.delete_task(db, task_id):
                    deleted_count += 1
                else:
                    failed_count += 1
            
            return {
                "deleted": deleted_count,
                "failed": failed_count,
                "total": len(task_ids)
            }
            
        except Exception as e:
            logger.error(f"批量删除任务失败: {e}")
            return {
                "deleted": 0,
                "failed": len(task_ids),
                "total": len(task_ids)
            }
    
    @staticmethod
    async def get_user_tasks(
        db: AsyncSession,
        user_id: uuid.UUID,
        project_id: Optional[uuid.UUID] = None,
        status: Optional[str] = None,
        task_type: Optional[str] = None,
        task_module: Optional[str] = None,  # 新增：按模块筛选
        start_date: Optional[datetime] = None,  # 新增：开始日期
        end_date: Optional[datetime] = None,    # 新增：结束日期
        search: Optional[str] = None,           # 新增：搜索关键词
        limit: int = 50,
        offset: int = 0
    ) -> Dict[str, Any]:
        """获取用户任务列表（增强版）"""
        try:
            # 构建基础查询
            stmt = select(AsyncTask).where(AsyncTask.user_id == user_id)
            count_stmt = select(AsyncTask).where(AsyncTask.user_id == user_id)
            
            # 项目筛选
            if project_id:
                stmt = stmt.where(AsyncTask.project_id == project_id)
                count_stmt = count_stmt.where(AsyncTask.project_id == project_id)
            
            # 状态筛选
            if status:
                stmt = stmt.where(AsyncTask.status == status)
                count_stmt = count_stmt.where(AsyncTask.status == status)
            
            # 任务类型筛选
            if task_type:
                stmt = stmt.where(AsyncTask.task_type == task_type)
                count_stmt = count_stmt.where(AsyncTask.task_type == task_type)
            
            # 模块筛选（基于任务类型的前缀）
            module_patterns = {
                "purchase": ["purchase_order_%", "procurement_%"],
                "inventory": ["inventory_%", "stock_%"],
                "sales": ["sales_%", "order_%"],
                "ai": ["%_ai_%", "ai_%"],
                "report": ["report_%", "export_%"],
                "import": ["import_%", "upload_%"],
                "sync": ["sync_%", "synchronize_%"]
            }
            
            if task_module:
                if task_module in module_patterns:
                    from sqlalchemy import or_
                    patterns = module_patterns[task_module]
                    module_conditions = [AsyncTask.task_type.like(pattern) for pattern in patterns]
                    stmt = stmt.where(or_(*module_conditions))
                    count_stmt = count_stmt.where(or_(*module_conditions))
            
            # 日期范围筛选 - API层已经处理了时间格式转换
            if start_date:
                stmt = stmt.where(AsyncTask.created_at >= start_date)
                count_stmt = count_stmt.where(AsyncTask.created_at >= start_date)
            
            if end_date:
                stmt = stmt.where(AsyncTask.created_at <= end_date)
                count_stmt = count_stmt.where(AsyncTask.created_at <= end_date)
            
            # 搜索筛选
            if search:
                from sqlalchemy import or_
                search_pattern = f"%{search}%"
                search_conditions = [
                    AsyncTask.task_name.ilike(search_pattern),
                    AsyncTask.description.ilike(search_pattern),
                    AsyncTask.task_type.ilike(search_pattern)
                ]
                stmt = stmt.where(or_(*search_conditions))
                count_stmt = count_stmt.where(or_(*search_conditions))
            
            # 获取总数
            from sqlalchemy import func
            total_result = await db.execute(select(func.count()).select_from(count_stmt.subquery()))
            total = total_result.scalar()
            
            # 获取分页数据
            stmt = stmt.order_by(AsyncTask.created_at.desc()).offset(offset).limit(limit)
            result = await db.execute(stmt)
            tasks = result.scalars().all()
            
            # 统计信息
            stats_stmt = select(
                AsyncTask.status,
                func.count(AsyncTask.id).label('count')
            ).where(AsyncTask.user_id == user_id)
            
            if project_id:
                stats_stmt = stats_stmt.where(AsyncTask.project_id == project_id)
            
            stats_stmt = stats_stmt.group_by(AsyncTask.status)
            stats_result = await db.execute(stats_stmt)
            status_stats = {row.status: row.count for row in stats_result}
            
            return {
                "tasks": tasks,
                "total": total,
                "offset": offset,
                "limit": limit,
                "status_stats": status_stats,
                "available_modules": list(module_patterns.keys()) if task_module is None else None
            }
            
        except Exception as e:
            logger.error(f"获取用户任务列表失败: {e}")
            return {
                "tasks": [],
                "total": 0,
                "offset": offset,
                "limit": limit,
                "status_stats": {},
                "available_modules": []
            } 