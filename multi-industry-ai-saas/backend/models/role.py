from sqlalchemy import Column, <PERSON>, <PERSON><PERSON><PERSON>, DateTime, ForeignKey, JSON, Text, Integer, UniqueConstraint
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid

from db.database import Base

class Role(Base):
    """角色模型"""
    __tablename__ = "roles"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    project_id = Column(UUID(as_uuid=False), ForeignKey("projects.id"), nullable=True)  # 允许为空，支持系统级角色
    name = Column(String(50), nullable=False)
    code = Column(String(50), nullable=False)
    description = Column(Text, nullable=True)
    permissions = Column(JSON, default=dict)  # 角色权限
    status = Column(String(20), default="active", nullable=False)  # active, inactive
    is_system_role = Column(Boolean, default=False)  # 是否系统角色
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 关联关系
    project = relationship("Project", backref="roles")
    project_users = relationship("ProjectUser", back_populates="role")

    __table_args__ = (
        UniqueConstraint('project_id', 'code', name='uq_role_code_per_project'),
    )

    def __repr__(self):
        return f"<Role(id={self.id}, name='{self.name}', code='{self.code}')>"


class Permission(Base):
    """权限模型"""
    __tablename__ = "permissions"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(100), nullable=False)
    code = Column(String(100), nullable=False, unique=True)
    description = Column(Text, nullable=True)
    module = Column(String(50), nullable=False)  # 模块：system, store, operation, purchase, finance, inventory, etc.
    action = Column(String(50), nullable=False)  # 操作：view, create, update, delete, etc.
    resource = Column(String(50), nullable=False)  # 资源：user, role, store, product, etc.
    is_system_permission = Column(Boolean, default=False)  # 是否系统权限
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    def __repr__(self):
        return f"<Permission(id={self.id}, code='{self.code}')>"


class RolePermission(Base):
    """角色权限关联模型"""
    __tablename__ = "role_permissions"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    role_id = Column(UUID(as_uuid=True), ForeignKey("roles.id"), nullable=False)
    permission_id = Column(UUID(as_uuid=True), ForeignKey("permissions.id"), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())

    # 关联关系
    role = relationship("Role", backref="role_permissions")
    permission = relationship("Permission", backref="role_permissions")

    __table_args__ = (
        UniqueConstraint('role_id', 'permission_id', name='uq_role_permission'),
    )

    def __repr__(self):
        return f"<RolePermission(id={self.id}, role_id={self.role_id}, permission_id={self.permission_id})>"
