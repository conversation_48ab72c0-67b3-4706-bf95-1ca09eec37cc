from sqlalchemy import Column, String, <PERSON>olean, DateTime, ForeignKey, JSON, Text, Integer, UniqueConstraint, BigInteger
from sqlalchemy.dialects.postgresql import UUID, ARRAY
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid

from db.database import Base

class SpaceSettings(Base):
    """空间设置模型"""
    __tablename__ = "space_settings"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id"), nullable=True)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id"), nullable=False)
    max_file_size = Column(BigInteger, default=10485760)  # 默认最大文件大小为10MB
    allowed_extensions = Column(ARRAY(String), default=["jpg", "jpeg", "png", "gif", "pdf", "doc", "docx", "xls", "xlsx", "ppt", "pptx", "txt", "zip", "rar"])
    max_storage_per_user = Column(BigInteger, default=524288000)  # 默认每用户最大存储空间为500MB
    max_storage_per_project = Column(BigInteger, default=5368709120)  # 默认每项目最大存储空间为5GB
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    created_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)
    updated_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)

    # 关系
    tenant = relationship("Tenant", backref="space_settings")
    project = relationship("Project", backref="space_settings")
    creator = relationship("User", foreign_keys=[created_by])
    updater = relationship("User", foreign_keys=[updated_by])
    module_settings = relationship("ModuleSpaceSettings", back_populates="space_settings", cascade="all, delete-orphan")

    __table_args__ = (
        UniqueConstraint('project_id', name='uq_space_settings_per_project'),
    )

    def __repr__(self):
        return f"<SpaceSettings(id={self.id}, project_id='{self.project_id}')>"


class ModuleSpaceSettings(Base):
    """模块空间设置模型"""
    __tablename__ = "module_space_settings"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    space_settings_id = Column(UUID(as_uuid=True), ForeignKey("space_settings.id"), nullable=False)
    module_code = Column(String(50), nullable=False)  # 模块代码，如loss, purchase, etc.
    module_name = Column(String(100), nullable=False)  # 模块名称
    max_file_size = Column(BigInteger, nullable=True)  # 模块最大文件大小，为空则使用全局设置
    allowed_extensions = Column(ARRAY(String), nullable=True)  # 模块允许的文件扩展名，为空则使用全局设置
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 关系
    space_settings = relationship("SpaceSettings", back_populates="module_settings")

    __table_args__ = (
        UniqueConstraint('space_settings_id', 'module_code', name='uq_module_space_settings'),
    )

    def __repr__(self):
        return f"<ModuleSpaceSettings(id={self.id}, module_code='{self.module_code}')>"
