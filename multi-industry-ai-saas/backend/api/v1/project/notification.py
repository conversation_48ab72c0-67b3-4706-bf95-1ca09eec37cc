#!/usr/bin/env python
# -*- coding: utf-8 -*-

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session
from sqlalchemy import func, select
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
import uuid

from db.database import get_db
from core.auth import get_current_user
from api.deps import get_current_project_id
from models.user import User
from models.notification import Notification
from schemas.notification import NotificationCreate, NotificationResponse, NotificationList, NotificationSettings
from api.project.notification_settings import router as notification_settings_router

router = APIRouter()

# 注册通知设置路由
router.include_router(
    notification_settings_router,
    prefix="/settings",
    tags=["project-notification-settings"]
)

@router.get("", response_model=Dict[str, Any])
async def get_notifications(
    page: int = Query(1, ge=1),
    page_size: int = Query(50, ge=1, le=100),
    filter_type: Optional[str] = None,
    priority: Optional[str] = None,
    is_read: Optional[bool] = None,
    current_user: User = Depends(get_current_user),
    project_id: uuid.UUID = Depends(get_current_project_id),
    db: Session = Depends(get_db)
):
    """获取用户通知列表"""
    try:
        offset = (page - 1) * page_size
        thirty_days_ago = datetime.now() - timedelta(days=30)
        # 现在可以使用完整的查询，包括read_at字段
        stmt = select(Notification).where(
            Notification.user_id == current_user.id,
            Notification.project_id == project_id,
            Notification.created_at >= thirty_days_ago
        )
        if filter_type:
            stmt = stmt.where(Notification.type == filter_type)
        if priority:
            stmt = stmt.where(Notification.priority == priority)
        if is_read is not None:
            stmt = stmt.where(Notification.is_read == is_read)
        count_stmt = select(func.count()).select_from(stmt.subquery())
        total = (await db.execute(count_stmt)).scalar()
        stmt = stmt.order_by(Notification.created_at.desc()).offset(offset).limit(page_size)
        result = await db.execute(stmt)
        notifications = result.scalars().all()
        
        # 转换为 Pydantic schema 对象
        notification_responses = [NotificationResponse.model_validate(notification) for notification in notifications]
        
        return {
            "success": True,
            "data": notification_responses,
            "total": total,
            "page": page,
            "page_size": page_size,
            "pages": (total + page_size - 1) // page_size
        }
    except HTTPException:
        # 让 HTTPException（如认证错误）直接传播
        raise
    except Exception as e:
        # 只捕获其他非HTTP异常
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取通知列表失败: {str(e)}"
        )

@router.get("/unread-count", response_model=Dict[str, Any])
async def get_unread_count(
    priority: Optional[str] = None,
    current_user: User = Depends(get_current_user),
    project_id: uuid.UUID = Depends(get_current_project_id),
    db: Session = Depends(get_db)
):
    """获取未读通知数量"""
    try:
        stmt = select(func.count(Notification.id)).where(
            Notification.user_id == current_user.id,
            Notification.project_id == project_id,
            Notification.is_read == False
        )
        if priority:
            stmt = stmt.where(Notification.priority == priority)
        count = (await db.execute(stmt)).scalar() or 0
        return {
            "success": True,
            "count": count
        }
    except HTTPException:
        # 让 HTTPException（如认证错误）直接传播
        raise
    except Exception as e:
        # 只捕获其他非HTTP异常
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取未读通知数量失败: {str(e)}"
        )

@router.put("/{notification_id}/read", response_model=Dict[str, Any])
async def mark_notification_as_read(
    notification_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    project_id: uuid.UUID = Depends(get_current_project_id),
    db: Session = Depends(get_db)
):
    """标记通知为已读"""
    try:
        result = await db.execute(select(Notification).where(
            Notification.id == notification_id,
            Notification.user_id == current_user.id,
            Notification.project_id == project_id
        ))
        notification = result.scalars().first()
        if not notification:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="通知不存在或不属于当前用户"
            )
        notification.is_read = True
        notification.read_at = datetime.now()
        await db.commit()
        return {
            "success": True,
            "message": "通知已标记为已读"
        }
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"标记通知已读失败: {str(e)}"
        )

@router.put("/read-all", response_model=Dict[str, Any])
async def mark_all_notifications_as_read(
    current_user: User = Depends(get_current_user),
    project_id: uuid.UUID = Depends(get_current_project_id),
    db: Session = Depends(get_db)
):
    """标记所有通知为已读"""
    try:
        result = await db.execute(select(Notification).where(
            Notification.user_id == current_user.id,
            Notification.project_id == project_id,
            Notification.is_read == False
        ))
        notifications = result.scalars().all()
        for notification in notifications:
            notification.is_read = True
            notification.read_at = datetime.now()
        await db.commit()
        return {
            "success": True,
            "message": "所有通知已标记为已读",
            "count": len(notifications)
        }
    except HTTPException:
        # 让 HTTPException（如认证错误）直接传播
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"标记所有通知已读失败: {str(e)}"
        )

@router.delete("/{notification_id}", response_model=Dict[str, Any])
async def delete_notification(
    notification_id: uuid.UUID,
    current_user: User = Depends(get_current_user),
    project_id: uuid.UUID = Depends(get_current_project_id),
    db: Session = Depends(get_db)
):
    """删除指定通知"""
    try:
        result = await db.execute(select(Notification).where(
            Notification.id == notification_id,
            Notification.user_id == current_user.id,
            Notification.project_id == project_id
        ))
        notification = result.scalars().first()
        if not notification:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="通知不存在或不属于当前用户"
            )
        await db.delete(notification)
        await db.commit()
        return {
            "success": True,
            "message": "通知已删除"
        }
    except HTTPException:
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除通知失败: {str(e)}"
        )

@router.delete("/delete-all", response_model=Dict[str, Any])
async def delete_all_notifications(
    current_user: User = Depends(get_current_user),
    project_id: uuid.UUID = Depends(get_current_project_id),
    db: Session = Depends(get_db)
):
    """删除当前用户的所有通知"""
    try:
        result = await db.execute(select(Notification).where(
            Notification.user_id == current_user.id,
            Notification.project_id == project_id
        ))
        notifications = result.scalars().all()
        for notification in notifications:
            await db.delete(notification)
        await db.commit()
        return {
            "success": True,
            "message": "所有通知已删除",
            "count": len(notifications)
        }
    except HTTPException:
        # 让 HTTPException（如认证错误）直接传播
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除所有通知失败: {str(e)}"
        )

@router.post("", status_code=status.HTTP_201_CREATED, response_model=Dict[str, Any])
async def create_notification(
    notification_data: NotificationCreate,
    current_user: User = Depends(get_current_user),
    project_id: uuid.UUID = Depends(get_current_project_id),
    db: Session = Depends(get_db)
):
    """创建新通知"""
    try:
        # 创建通知
        notification = Notification(
            id=uuid.uuid4(),
            user_id=notification_data.user_id,
            project_id=project_id,
            title=notification_data.title,
            content=notification_data.content,
            type=notification_data.type,
            category=notification_data.category,
            priority=notification_data.priority,
            content_data=notification_data.content_data,
            target_url=notification_data.target_url,
            created_at=datetime.now(),
            is_read=False
        )
        db.add(notification)
        db.commit()
        db.refresh(notification)

        # 转换为 Pydantic schema 对象
        notification_response = NotificationResponse.model_validate(notification)

        return {
            "success": True,
            "message": "通知创建成功",
            "data": notification_response
        }
    except HTTPException:
        # 让 HTTPException（如认证错误）直接传播
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建通知失败: {str(e)}"
        )

@router.get("/settings", response_model=Dict[str, Any])
async def get_notification_settings(
    current_user: User = Depends(get_current_user),
    project_id: uuid.UUID = Depends(get_current_project_id),
    db: Session = Depends(get_db)
):
    """获取通知设置"""
    try:
        # 从用户设置中获取通知设置
        user_settings = current_user.settings or {}
        notification_settings = user_settings.get("notification", {})

        # 如果没有设置，返回默认设置
        if not notification_settings:
            notification_settings = {
                "frequency": "realtime",
                "email_notifications": True,
                "push_notifications": True,
                "notification_types": {
                    "system": True,
                    "task": True,
                    "inventory": True,
                    "order": True,
                    "security": True
                }
            }

        return {
            "success": True,
            "data": notification_settings
        }
    except HTTPException:
        # 让 HTTPException（如认证错误）直接传播
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取通知设置失败: {str(e)}"
        )

@router.put("/settings", response_model=Dict[str, Any])
async def update_notification_settings(
    settings: NotificationSettings,
    current_user: User = Depends(get_current_user),
    project_id: uuid.UUID = Depends(get_current_project_id),
    db: Session = Depends(get_db)
):
    """更新通知设置"""
    try:
        # 获取当前用户设置
        user_settings = current_user.settings or {}

        # 更新通知设置
        user_settings["notification"] = settings.dict()

        # 保存设置
        current_user.settings = user_settings
        db.commit()

        return {
            "success": True,
            "message": "通知设置更新成功",
            "data": settings
        }
    except HTTPException:
        # 让 HTTPException（如认证错误）直接传播
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新通知设置失败: {str(e)}"
        )
