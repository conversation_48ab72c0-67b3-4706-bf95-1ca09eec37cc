#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
AI 音频服务
"""

import logging
import uuid
import json
import time
import base64
import httpx
from typing import List, Optional, Dict, Any, Union, BinaryIO
from sqlalchemy.ext.asyncio import AsyncSession

from models.ai import AIConfig, AIModel, AIProvider
from models.project import Project
from models.user import User
from services.ai import AIConfigService, AIUsageService
from schemas.ai import AIUsageCreate

logger = logging.getLogger(__name__)

class AIAudioService:
    """AI 音频服务"""

    @staticmethod
    async def transcribe_audio(
        db: AsyncSession,
        project_id: uuid.UUID,
        user_id: uuid.UUID,
        audio_data: Union[str, bytes],
        model_id: Optional[uuid.UUID] = None,
        config_id: Optional[uuid.UUID] = None,
        language: Optional[str] = None,
        prompt: Optional[str] = None,
        response_format: str = "text",
        temperature: Optional[float] = None,
    ) -> Dict[str, Any]:
        """
        转录音频
        
        audio_data 可以是 base64 编码的字符串或二进制数据
        """
        start_time = time.time()
        
        try:
            # 获取有效的 AI 配置
            if config_id:
                config = await AIConfigService.get_config(db, config_id)
                if not config or config.status != "active":
                    raise ValueError(f"AI 配置 ID '{config_id}' 不存在或未激活")
            else:
                config = await AIConfigService.get_effective_config(
                    db=db,
                    project_id=project_id,
                    model_id=model_id,
                    model_type="audio",
                )
                
            if not config:
                raise ValueError("未找到有效的 AI 配置")
                
            # 获取模型信息
            model = await db.get(AIModel, config.model_id)
            if not model:
                raise ValueError(f"AI 模型 ID '{config.model_id}' 不存在")
                
            # 检查模型是否支持音频功能
            model_capabilities = model.capabilities or {}
            if not model_capabilities.get("audio"):
                raise ValueError(f"模型 '{model.name}' 不支持音频功能")
                
            # 获取提供商信息
            provider = await db.get(AIProvider, config.provider_id)
            if not provider:
                raise ValueError(f"AI 提供商 ID '{config.provider_id}' 不存在")
                
            # 获取项目信息
            project = await db.get(Project, project_id)
            if not project:
                raise ValueError(f"项目 ID '{project_id}' 不存在")
                
            # 处理音频数据
            if isinstance(audio_data, bytes):
                # 将二进制数据转换为 base64 编码的字符串
                audio_base64 = base64.b64encode(audio_data).decode('utf-8')
            else:
                # 假设已经是 base64 编码的字符串
                audio_base64 = audio_data
                
            # 根据提供商类型构建请求
            api_url = config.api_endpoint or provider.api_base_url
            
            if provider.name == "openai":
                # OpenAI API
                if not api_url.endswith("/audio/transcriptions"):
                    api_url = f"{api_url.rstrip('/')}/audio/transcriptions"
                    
                headers = {
                    "Authorization": f"Bearer {config.api_key}",
                }
                
                # OpenAI 使用 multipart/form-data
                files = {
                    "file": ("audio.mp3", base64.b64decode(audio_base64), "audio/mpeg")
                }
                
                data = {
                    "model": model.name,
                    "response_format": response_format,
                }
                
                if language:
                    data["language"] = language
                    
                if prompt:
                    data["prompt"] = prompt
                    
                if temperature is not None:
                    data["temperature"] = temperature
                elif config.temperature is not None:
                    data["temperature"] = config.temperature
                    
            elif provider.name == "anthropic":
                # Anthropic API 目前不支持音频转录
                raise ValueError("Anthropic 不支持音频转录功能")
                
            elif provider.name == "deepseek":
                # DeepSeek API 目前不支持音频转录
                raise ValueError("DeepSeek 不支持音频转录功能")
                
            else:
                raise ValueError(f"不支持的 AI 提供商: {provider.name}")
                
            # 添加代理
            proxy = None
            if config.proxy_url:
                proxy = config.proxy_url
                
            # 发送请求
            async with httpx.AsyncClient(proxies=proxy) as client:
                if provider.name == "openai":
                    response = await client.post(
                        api_url,
                        headers=headers,
                        files=files,
                        data=data,
                        timeout=60,
                    )
                else:
                    # 其他提供商的请求方式
                    response = await client.post(
                        api_url,
                        json={},  # 占位符
                        headers={},  # 占位符
                        timeout=60,
                    )
                
                # 检查响应状态
                response.raise_for_status()
                
                # 解析响应
                if response_format == "json":
                    result = response.json()
                else:
                    # 文本格式
                    result = {"text": response.text}
                
            # 计算耗时
            duration_ms = int((time.time() - start_time) * 1000)
            
            # 估算 token 使用情况（音频转录没有明确的 token 计数）
            # 假设每秒音频约 20-30 个单词，每个单词约 1.5 个 token
            # 这里使用一个粗略估计，实际应用中可能需要更精确的计算
            audio_duration_seconds = 60  # 假设音频长度为 60 秒
            estimated_tokens = int(audio_duration_seconds * 25 * 1.5)  # 25 words/sec * 1.5 tokens/word
            
            prompt_tokens = estimated_tokens
            completion_tokens = 0
            total_tokens = estimated_tokens
            
            # 计算成本
            cost = 0.0
            if model.input_price_per_1k_tokens:
                cost = (total_tokens / 1000) * model.input_price_per_1k_tokens
                
            # 记录使用情况
            usage_data = AIUsageCreate(
                tenant_id=config.tenant_id or project.tenant_id,
                project_id=project_id,
                user_id=user_id,
                config_id=config.id,
                model_id=model.id,
                request_type="audio",
                prompt_tokens=prompt_tokens,
                completion_tokens=completion_tokens,
                total_tokens=total_tokens,
                cost=cost,
                duration_ms=duration_ms,
                status="success",
                request_metadata={
                    "provider": provider.name,
                    "model": model.name,
                    "temperature": temperature,
                    "language": language,
                    "has_prompt": bool(prompt),
                    "response_format": response_format,
                }
            )
            
            await AIUsageService.create_usage(db, usage_data)
            
            return result
            
        except Exception as e:
            # 计算耗时
            duration_ms = int((time.time() - start_time) * 1000)
            
            # 记录错误
            logger.error(f"AI 音频转录请求失败: {e}")
            
            # 如果已经获取到配置和模型，记录使用情况
            if 'config' in locals() and 'model' in locals() and 'project' in locals():
                error_usage_data = AIUsageCreate(
                    tenant_id=config.tenant_id or project.tenant_id,
                    project_id=project_id,
                    user_id=user_id,
                    config_id=config.id if 'config' in locals() else None,
                    model_id=model.id if 'model' in locals() else None,
                    request_type="audio",
                    prompt_tokens=0,
                    completion_tokens=0,
                    total_tokens=0,
                    cost=0,
                    duration_ms=duration_ms,
                    status="failed",
                    error_message=str(e),
                    request_metadata={
                        "provider": provider.name if 'provider' in locals() else None,
                        "model": model.name if 'model' in locals() else None,
                        "language": language,
                        "has_prompt": bool(prompt),
                        "response_format": response_format,
                    }
                )
                
                await AIUsageService.create_usage(db, error_usage_data)
                
            raise ValueError(f"AI 音频转录请求失败: {str(e)}")
            
    @staticmethod
    async def text_to_speech(
        db: AsyncSession,
        project_id: uuid.UUID,
        user_id: uuid.UUID,
        text: str,
        model_id: Optional[uuid.UUID] = None,
        config_id: Optional[uuid.UUID] = None,
        voice: str = "alloy",
        response_format: str = "mp3",
        speed: float = 1.0,
    ) -> Dict[str, Any]:
        """
        文本转语音
        """
        start_time = time.time()
        
        try:
            # 获取有效的 AI 配置
            if config_id:
                config = await AIConfigService.get_config(db, config_id)
                if not config or config.status != "active":
                    raise ValueError(f"AI 配置 ID '{config_id}' 不存在或未激活")
            else:
                config = await AIConfigService.get_effective_config(
                    db=db,
                    project_id=project_id,
                    model_id=model_id,
                    model_type="audio",
                )
                
            if not config:
                raise ValueError("未找到有效的 AI 配置")
                
            # 获取模型信息
            model = await db.get(AIModel, config.model_id)
            if not model:
                raise ValueError(f"AI 模型 ID '{config.model_id}' 不存在")
                
            # 检查模型是否支持文本转语音功能
            model_capabilities = model.capabilities or {}
            if not model_capabilities.get("text_to_speech"):
                raise ValueError(f"模型 '{model.name}' 不支持文本转语音功能")
                
            # 获取提供商信息
            provider = await db.get(AIProvider, config.provider_id)
            if not provider:
                raise ValueError(f"AI 提供商 ID '{config.provider_id}' 不存在")
                
            # 获取项目信息
            project = await db.get(Project, project_id)
            if not project:
                raise ValueError(f"项目 ID '{project_id}' 不存在")
                
            # 根据提供商类型构建请求
            api_url = config.api_endpoint or provider.api_base_url
            
            if provider.name == "openai":
                # OpenAI API
                if not api_url.endswith("/audio/speech"):
                    api_url = f"{api_url.rstrip('/')}/audio/speech"
                    
                headers = {
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {config.api_key}",
                }
                
                request_params = {
                    "model": model.name,
                    "input": text,
                    "voice": voice,
                    "response_format": response_format,
                    "speed": speed,
                }
                
            elif provider.name == "anthropic":
                # Anthropic API 目前不支持文本转语音
                raise ValueError("Anthropic 不支持文本转语音功能")
                
            elif provider.name == "deepseek":
                # DeepSeek API 目前不支持文本转语音
                raise ValueError("DeepSeek 不支持文本转语音功能")
                
            else:
                raise ValueError(f"不支持的 AI 提供商: {provider.name}")
                
            # 添加代理
            proxy = None
            if config.proxy_url:
                proxy = config.proxy_url
                
            # 发送请求
            async with httpx.AsyncClient(proxies=proxy) as client:
                response = await client.post(
                    api_url,
                    json=request_params,
                    headers=headers,
                    timeout=60,
                )
                
                # 检查响应状态
                response.raise_for_status()
                
                # 获取音频数据
                audio_data = response.content
                
                # 将音频数据转换为 base64 编码的字符串
                audio_base64 = base64.b64encode(audio_data).decode('utf-8')
                
                # 构建结果
                result = {
                    "audio_base64": audio_base64,
                    "format": response_format,
                }
                
            # 计算耗时
            duration_ms = int((time.time() - start_time) * 1000)
            
            # 估算 token 使用情况
            # 文本转语音的 token 计算通常基于输入文本的长度
            # 假设每个单词约 1.5 个 token
            words = len(text.split())
            estimated_tokens = int(words * 1.5)
            
            prompt_tokens = estimated_tokens
            completion_tokens = 0
            total_tokens = estimated_tokens
            
            # 计算成本
            cost = 0.0
            if model.input_price_per_1k_tokens:
                cost = (total_tokens / 1000) * model.input_price_per_1k_tokens
                
            # 记录使用情况
            usage_data = AIUsageCreate(
                tenant_id=config.tenant_id or project.tenant_id,
                project_id=project_id,
                user_id=user_id,
                config_id=config.id,
                model_id=model.id,
                request_type="text_to_speech",
                prompt_tokens=prompt_tokens,
                completion_tokens=completion_tokens,
                total_tokens=total_tokens,
                cost=cost,
                duration_ms=duration_ms,
                status="success",
                request_metadata={
                    "provider": provider.name,
                    "model": model.name,
                    "voice": voice,
                    "response_format": response_format,
                    "speed": speed,
                    "text_length": len(text),
                    "word_count": words,
                }
            )
            
            await AIUsageService.create_usage(db, usage_data)
            
            return result
            
        except Exception as e:
            # 计算耗时
            duration_ms = int((time.time() - start_time) * 1000)
            
            # 记录错误
            logger.error(f"AI 文本转语音请求失败: {e}")
            
            # 如果已经获取到配置和模型，记录使用情况
            if 'config' in locals() and 'model' in locals() and 'project' in locals():
                error_usage_data = AIUsageCreate(
                    tenant_id=config.tenant_id or project.tenant_id,
                    project_id=project_id,
                    user_id=user_id,
                    config_id=config.id if 'config' in locals() else None,
                    model_id=model.id if 'model' in locals() else None,
                    request_type="text_to_speech",
                    prompt_tokens=0,
                    completion_tokens=0,
                    total_tokens=0,
                    cost=0,
                    duration_ms=duration_ms,
                    status="failed",
                    error_message=str(e),
                    request_metadata={
                        "provider": provider.name if 'provider' in locals() else None,
                        "model": model.name if 'model' in locals() else None,
                        "voice": voice,
                        "response_format": response_format,
                        "speed": speed,
                        "text_length": len(text) if 'text' in locals() else 0,
                    }
                )
                
                await AIUsageService.create_usage(db, error_usage_data)
                
            raise ValueError(f"AI 文本转语音请求失败: {str(e)}")
