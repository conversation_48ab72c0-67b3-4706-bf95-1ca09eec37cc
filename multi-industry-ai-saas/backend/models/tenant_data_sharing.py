#!/usr/bin/env python
# -*- coding: utf-8 -*-

from sqlalchemy import Column, String, Boolean, DateTime, ForeignKey, JSON, Text, Integer, UniqueConstraint, Table
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid

from db.database import Base

class TenantDataSharing(Base):
    """租户数据共享模型"""
    __tablename__ = "tenant_data_sharing"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    source_tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id", ondelete="CASCADE"), nullable=False)
    target_tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id", ondelete="CASCADE"), nullable=False)
    data_type = Column(String(50), nullable=False)  # 数据类型，如 'product', 'supplier', 'knowledge', 等
    data_id = Column(UUID(as_uuid=True), nullable=False)  # 数据ID
    status = Column(String(20), default="active", nullable=False)  # active, inactive, revoked
    access_level = Column(String(20), default="read", nullable=False)  # read, write, admin
    expiry_date = Column(DateTime(timezone=True), nullable=True)  # 过期时间，为空表示永不过期
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    created_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    updated_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)

    # 关系
    source_tenant = relationship("Tenant", foreign_keys=[source_tenant_id], back_populates="shared_data_as_source")
    target_tenant = relationship("Tenant", foreign_keys=[target_tenant_id], back_populates="shared_data_as_target")
    creator = relationship("User", foreign_keys=[created_by])
    updater = relationship("User", foreign_keys=[updated_by])

    # 元数据，可以包含额外的共享配置
    sharing_metadata = Column(JSON, default=dict)

    __table_args__ = (
        # 确保同一数据只能被同一源租户共享给同一目标租户一次
        UniqueConstraint('source_tenant_id', 'target_tenant_id', 'data_type', 'data_id', name='uq_tenant_data_sharing'),
    )

    def __repr__(self):
        return f"<TenantDataSharing(id={self.id}, source={self.source_tenant_id}, target={self.target_tenant_id}, type={self.data_type})>"


class TenantDataSharingLog(Base):
    """租户数据共享日志模型"""
    __tablename__ = "tenant_data_sharing_logs"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    sharing_id = Column(UUID(as_uuid=True), ForeignKey("tenant_data_sharing.id", ondelete="CASCADE"), nullable=False)
    action = Column(String(50), nullable=False)  # create, update, revoke, access
    action_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    action_at = Column(DateTime(timezone=True), server_default=func.now())
    details = Column(JSON, default=dict)  # 操作详情

    # 关系
    sharing = relationship("TenantDataSharing")
    user = relationship("User")

    def __repr__(self):
        return f"<TenantDataSharingLog(id={self.id}, action={self.action}, sharing_id={self.sharing_id})>"
