import React from 'react';
import { Card, Statistic, Typography, Tooltip } from 'antd';
import { ArrowUpOutlined, ArrowDownOutlined, InfoCircleOutlined } from '@ant-design/icons';

const { Text } = Typography;

/**
 * 统计卡片组件
 * 用于展示仪表盘中的统计数据
 * @param {Object} props - 组件属性
 * @param {string} props.title - 卡片标题
 * @param {number} props.value - 统计值
 * @param {number} props.precision - 小数位数
 * @param {React.ReactNode} props.prefix - 前缀图标
 * @param {string} props.suffix - 后缀文本
 * @param {string} props.valueStyle - 值样式
 * @param {number} props.change - 变化值（百分比）
 * @param {boolean} props.isIncrease - 是否增长
 * @param {string} props.compareText - 比较文本
 * @param {string} props.tooltip - 提示文本
 */
const StatisticCard = ({
  title,
  value,
  precision = 0,
  prefix,
  suffix,
  valueStyle,
  change,
  isIncrease,
  compareText = '与上期相比',
  tooltip
}) => {
  return (
    <Card className="statistic-card">
      <div className="statistic-card-header">
        <div className="statistic-card-title">
          {title}
          {tooltip && (
            <Tooltip title={tooltip}>
              <InfoCircleOutlined style={{ marginLeft: 8 }} />
            </Tooltip>
          )}
        </div>
      </div>
      <Statistic
        value={value}
        precision={precision}
        prefix={prefix}
        suffix={suffix}
        valueStyle={valueStyle || { color: '#1890ff' }}
      />
      {change !== undefined && (
        <div className="statistic-footer">
          {isIncrease ? (
            <Text type="success">
              <ArrowUpOutlined /> {Math.abs(change).toFixed(1)}%
            </Text>
          ) : (
            <Text type="danger">
              <ArrowDownOutlined /> {Math.abs(change).toFixed(1)}%
            </Text>
          )}
          <Text type="secondary" style={{ marginLeft: 8 }}>{compareText}</Text>
        </div>
      )}
    </Card>
  );
};

export default StatisticCard;
