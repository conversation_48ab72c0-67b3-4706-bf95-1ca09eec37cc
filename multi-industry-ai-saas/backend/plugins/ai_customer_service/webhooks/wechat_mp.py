#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
微信公众号 Webhook 处理器

处理微信公众号的消息推送，包括15秒响应机制和异步消息处理。
"""

import logging
import hashlib
import xml.etree.ElementTree as ET
from typing import Dict, Any, Optional
from datetime import datetime
from fastapi import APIRouter, Request, Response, HTTPException, status
from fastapi.responses import PlainTextResponse

from ..core.platform_adapter import PlatformMessage, PlatformResponse, WeChatMPAdapter
from ..services.customer_service import CustomerServiceManager

logger = logging.getLogger(__name__)

# 创建路由器
router = APIRouter()

class WeChatMPWebhookHandler:
    """微信公众号Webhook处理器"""
    
    def __init__(self):
        self.cs_manager = None
        self.mp_adapter = None
        
    async def initialize(self, cs_manager: CustomerServiceManager):
        """初始化处理器"""
        self.cs_manager = cs_manager
        # 获取微信公众号适配器
        self.mp_adapter = cs_manager.platform_manager.adapters.get("wechat_mp")
        
    def verify_signature(self, signature: str, timestamp: str, nonce: str, token: str) -> bool:
        """验证微信签名"""
        try:
            # 将token、timestamp、nonce三个参数进行字典序排序
            tmp_arr = [token, timestamp, nonce]
            tmp_arr.sort()
            tmp_str = "".join(tmp_arr)
            
            # sha1加密
            sha1 = hashlib.sha1()
            sha1.update(tmp_str.encode('utf-8'))
            hashcode = sha1.hexdigest()
            
            # 与signature对比
            return hashcode == signature
            
        except Exception as e:
            logger.error(f"验证微信签名失败: {e}")
            return False
            
    def parse_xml_message(self, xml_data: str) -> Dict[str, Any]:
        """解析微信XML消息"""
        try:
            root = ET.fromstring(xml_data)
            
            message = {}
            for child in root:
                message[child.tag] = child.text
                
            return message
            
        except Exception as e:
            logger.error(f"解析XML消息失败: {e}")
            return {}
            
    def build_xml_response(self, to_user: str, from_user: str, content: str, msg_type: str = "text") -> str:
        """构建XML响应"""
        try:
            timestamp = int(datetime.now().timestamp())
            
            if msg_type == "text":
                xml_template = """<xml>
<ToUserName><![CDATA[{to_user}]]></ToUserName>
<FromUserName><![CDATA[{from_user}]]></FromUserName>
<CreateTime>{timestamp}</CreateTime>
<MsgType><![CDATA[text]]></MsgType>
<Content><![CDATA[{content}]]></Content>
</xml>"""
                return xml_template.format(
                    to_user=to_user,
                    from_user=from_user,
                    timestamp=timestamp,
                    content=content
                )
            else:
                # 其他消息类型的模板可以在这里添加
                return ""
                
        except Exception as e:
            logger.error(f"构建XML响应失败: {e}")
            return ""
            
    async def process_message(self, message_data: Dict[str, Any]) -> Optional[str]:
        """处理微信消息"""
        try:
            msg_type = message_data.get("MsgType")
            from_user = message_data.get("FromUserName")
            to_user = message_data.get("ToUserName")
            
            if not all([msg_type, from_user, to_user]):
                logger.warning("微信消息缺少必要字段")
                return None
                
            # 构建平台消息
            platform_message = PlatformMessage(
                platform_type="wechat_mp",
                platform_user_id=from_user,
                user_name=None,  # 稍后获取
                message_type=msg_type,
                content=message_data.get("Content", ""),
                attachments=[],
                timestamp=float(message_data.get("CreateTime", 0)),
                session_id=f"wechat_mp_{from_user}",
                meta_data={
                    "msg_id": message_data.get("MsgId"),
                    "to_user": to_user
                }
            )
            
            # 处理不同类型的消息
            if msg_type == "text":
                # 文本消息
                platform_message.content = message_data.get("Content", "")
            elif msg_type == "image":
                # 图片消息
                platform_message.attachments = [{
                    "type": "image",
                    "media_id": message_data.get("MediaId"),
                    "pic_url": message_data.get("PicUrl")
                }]
                platform_message.content = "[图片]"
            elif msg_type == "voice":
                # 语音消息
                platform_message.attachments = [{
                    "type": "voice",
                    "media_id": message_data.get("MediaId"),
                    "recognition": message_data.get("Recognition", "")
                }]
                platform_message.content = message_data.get("Recognition", "[语音]")
            elif msg_type == "event":
                # 事件消息
                event = message_data.get("Event")
                if event == "subscribe":
                    platform_message.content = "关注公众号"
                elif event == "unsubscribe":
                    platform_message.content = "取消关注公众号"
                elif event == "CLICK":
                    platform_message.content = f"点击菜单: {message_data.get('EventKey', '')}"
                else:
                    platform_message.content = f"事件: {event}"
            else:
                logger.warning(f"不支持的消息类型: {msg_type}")
                return None
                
            # 如果有客服管理器，处理消息
            if self.cs_manager:
                # 异步处理消息（不阻塞15秒响应）
                import asyncio
                asyncio.create_task(self._async_process_message(platform_message))
                
                # 检查是否有立即响应
                if self.mp_adapter:
                    pending_response = self.mp_adapter.get_pending_response(from_user)
                    if pending_response:
                        return self.build_xml_response(
                            to_user=from_user,
                            from_user=to_user,
                            content=pending_response["content"]
                        )
                        
            # 默认响应
            if msg_type == "event" and message_data.get("Event") == "subscribe":
                welcome_msg = "欢迎关注！我是AI智能客服，有什么问题可以随时咨询我。"
                return self.build_xml_response(
                    to_user=from_user,
                    from_user=to_user,
                    content=welcome_msg
                )
                
            return None  # 不回复
            
        except Exception as e:
            logger.error(f"处理微信消息失败: {e}")
            return None
            
    async def _async_process_message(self, platform_message: PlatformMessage):
        """异步处理消息（超过15秒的处理）"""
        try:
            if self.cs_manager:
                # 这里会触发完整的AI处理流程
                await self.cs_manager.handle_platform_message(platform_message)
                
        except Exception as e:
            logger.error(f"异步处理微信消息失败: {e}")

# 全局处理器实例
webhook_handler = WeChatMPWebhookHandler()

@router.get("/wechat-mp/webhook")
async def wechat_mp_verify(
    signature: str,
    timestamp: str,
    nonce: str,
    echostr: str
):
    """微信公众号验证接口"""
    try:
        # 这里需要从配置中获取token
        # 暂时使用默认值，实际使用时需要从数据库或配置文件获取
        token = "your_wechat_token"
        
        if webhook_handler.verify_signature(signature, timestamp, nonce, token):
            return PlainTextResponse(echostr)
        else:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="签名验证失败"
            )
            
    except Exception as e:
        logger.error(f"微信公众号验证失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="验证失败"
        )

@router.post("/wechat-mp/webhook")
async def wechat_mp_message(
    request: Request,
    signature: str,
    timestamp: str,
    nonce: str
):
    """微信公众号消息接收接口"""
    try:
        # 验证签名
        token = "your_wechat_token"  # 需要从配置获取
        
        if not webhook_handler.verify_signature(signature, timestamp, nonce, token):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="签名验证失败"
            )
            
        # 获取XML数据
        xml_data = await request.body()
        xml_str = xml_data.decode('utf-8')
        
        # 解析消息
        message_data = webhook_handler.parse_xml_message(xml_str)
        
        if not message_data:
            return PlainTextResponse("success")
            
        # 处理消息
        response_xml = await webhook_handler.process_message(message_data)
        
        if response_xml:
            return Response(content=response_xml, media_type="application/xml")
        else:
            return PlainTextResponse("success")
            
    except Exception as e:
        logger.error(f"处理微信公众号消息失败: {e}")
        return PlainTextResponse("success")  # 微信要求返回success

@router.post("/wechat-mp/init")
async def init_wechat_mp_webhook():
    """初始化微信公众号Webhook处理器"""
    try:
        # 这里应该从插件实例中获取客服管理器
        # 暂时返回成功响应
        return {"success": True, "message": "微信公众号Webhook处理器初始化成功"}

    except Exception as e:
        logger.error(f"初始化微信公众号Webhook处理器失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="初始化失败"
        )
