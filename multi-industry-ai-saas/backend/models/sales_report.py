#!/usr/bin/env python
# -*- coding: utf-8 -*-

import uuid
from datetime import datetime
from typing import List, Optional, Dict, Any
from sqlalchemy import Column, String, Float, Integer, Boolean, ForeignKey, DateTime, Text, Enum, func
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship
from sqlalchemy.ext.hybrid import hybrid_property
import enum

from models.core import Base


class SalesReportType(str, enum.Enum):
    SHIFT = "shift"  # 班次报告
    DAILY = "daily"  # 每日报告
    MONTHLY = "monthly" # 每月报告

class SalesReportStatus(str, enum.Enum):
    DRAFT = "draft"
    PUBLISHED = "published"
    ARCHIVED = "archived"

class SalesReport(Base):
    """销售上报模型"""
    __tablename__ = "sales_reports"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id", ondelete="CASCADE"), nullable=False, index=True, comment="项目ID")
    store_id = Column(UUID(as_uuid=True), ForeignKey("stores.id", ondelete="SET NULL"), nullable=True, index=True, comment="门店ID")
    reporter_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)

    report_date = Column(DateTime, nullable=False, default=datetime.utcnow, comment="上报日期")
    report_type = Column(Enum(SalesReportType, name="sales_report_type", native_enum=False, values_callable=lambda obj: [e.value for e in obj]), nullable=False, comment="上报类型: shift-班次, daily-日报, monthly-月报")
    status = Column(Enum(SalesReportStatus, name="sales_report_status", native_enum=False, values_callable=lambda obj: [e.value for e in obj]), nullable=False, default=SalesReportStatus.DRAFT)

    total_sales = Column(Float, nullable=False, default=0.0, comment="销售总额")
    total_orders = Column(Integer, nullable=False, default=0, comment="订单总数")
    total_customers = Column(Integer, nullable=False, default=0, comment="顾客总数")

    online_sales = Column(Float, nullable=False, default=0.0, comment="线上销售额")
    offline_sales = Column(Float, nullable=False, default=0.0, comment="线下销售额")

    payment_methods_data = Column(JSONB, default=list, name="payment_methods_data", comment="支付方式统计")
    recharge_sales_data = Column(JSONB, default=list, name="recharge_sales_data", comment="充值/售卡支付方式统计")
    product_categories = Column(JSONB, nullable=True, comment="产品类别统计")
    hourly_sales = Column(JSONB, nullable=True, comment="分时段销售统计")

    # 充值售卡相关字段
    recharge_amount = Column(Float, nullable=False, default=0.0, comment="会员充值金额")
    card_sales_amount = Column(Float, nullable=False, default=0.0, comment="储值卡销售金额")
    recharge_count = Column(Integer, nullable=False, default=0, comment="会员充值笔数")
    card_sales_count = Column(Integer, nullable=False, default=0, comment="储值卡销售数量")

    notes = Column(Text, nullable=True, comment="备注")
    reject_reason = Column(Text, nullable=True, comment="拒绝原因")

    created_by = Column(UUID(as_uuid=True), ForeignKey("users.id", ondelete="SET NULL"), nullable=True, comment="创建人ID")
    updated_by = Column(UUID(as_uuid=True), ForeignKey("users.id", ondelete="SET NULL"), nullable=True, comment="更新人ID")
    approved_by = Column(UUID(as_uuid=True), ForeignKey("users.id", ondelete="SET NULL"), nullable=True, comment="审核人ID")
    approved_at = Column(DateTime, nullable=True, comment="审核时间")

    created_at = Column(DateTime, nullable=False, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")

    # 关系
    project = relationship("Project", back_populates="sales_reports")
    store = relationship("Store", back_populates="sales_reports")
    reporter = relationship("User", foreign_keys=[reporter_id])
    creator = relationship("User", foreign_keys=[created_by])
    updater = relationship("User", foreign_keys=[updated_by])
    approver = relationship("User", foreign_keys=[approved_by])
    items = relationship("SalesReportItem", back_populates="sales_report", cascade="all, delete-orphan")
    channel_details = relationship("SalesReportChannel", back_populates="sales_report", cascade="all, delete-orphan")

    @hybrid_property
    def store_name(self):
        return self.store.name if self.store else None

    @hybrid_property
    def creator_name(self):
        return self.creator.username if self.creator else None

    @hybrid_property
    def updater_name(self):
        return self.updater.username if self.updater else None

    @hybrid_property
    def approver_name(self):
        return self.approver.username if self.approver else None

    def __repr__(self):
        return f"<SalesReport(id={self.id}, type='{self.report_type}', date='{self.report_date}')>"


class SalesReportChannel(Base):
    """销售报告的渠道详情"""
    __tablename__ = 'sales_report_channels'

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    sales_report_id = Column(UUID(as_uuid=True), ForeignKey('sales_reports.id', ondelete="CASCADE"), nullable=False, index=True)
    sales_channel_id = Column(UUID(as_uuid=True), ForeignKey('sales_channels.id', ondelete="CASCADE"), nullable=False, index=True)

    total_sales = Column(Float, nullable=False, default=0.0, comment="该渠道销售总额")
    total_orders = Column(Integer, nullable=False, default=0, comment="该渠道订单总数")
    total_customers = Column(Integer, nullable=False, default=0, comment="该渠道顾客总数")

    payment_methods_details = Column(JSONB, nullable=True, comment="该渠道的支付方式明细")

    sales_report = relationship("SalesReport", back_populates="channel_details")
    sales_channel = relationship("SalesChannel")


class SalesReportItem(Base):
    """销售上报项模型"""
    __tablename__ = "sales_report_items"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    sales_report_id = Column(UUID(as_uuid=True), ForeignKey("sales_reports.id", ondelete="CASCADE"), nullable=False, index=True, comment="销售上报ID")
    product_id = Column(UUID(as_uuid=True), ForeignKey("products.id", ondelete="SET NULL"), nullable=True, index=True, comment="产品ID")

    product_name = Column(String(255), nullable=False, comment="产品名称")
    product_code = Column(String(50), nullable=True, comment="产品编码")
    product_category = Column(String(50), nullable=True, comment="产品类别")
    product_unit = Column(String(20), nullable=True, comment="产品单位")

    quantity = Column(Float, nullable=False, default=0.0, comment="销售数量")
    unit_price = Column(Float, nullable=False, default=0.0, comment="单价")
    discount_amount = Column(Float, nullable=False, default=0.0, comment="折扣金额")
    total_amount = Column(Float, nullable=False, default=0.0, comment="销售金额")

    notes = Column(Text, nullable=True, comment="备注")

    created_at = Column(DateTime, nullable=False, default=datetime.utcnow, comment="创建时间")
    updated_at = Column(DateTime, nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")

    # 关系
    sales_report = relationship("SalesReport", back_populates="items")
    product = relationship("Product")
