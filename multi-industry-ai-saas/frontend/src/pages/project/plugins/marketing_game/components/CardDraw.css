/* 抽卡游戏样式 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes float {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

@keyframes flip {
  0% { transform: rotateY(0); }
  100% { transform: rotateY(180deg); }
}

@keyframes shine {
  0% { box-shadow: 0 0 5px 2px rgba(255, 215, 0, 0.6); }
  50% { box-shadow: 0 0 20px 5px rgba(255, 215, 0, 0.8); }
  100% { box-shadow: 0 0 5px 2px rgba(255, 215, 0, 0.6); }
}

.card-draw-container {
  width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  padding: 20px;
  position: relative;
  overflow: hidden;
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
  animation: fadeIn 0.8s ease-in-out;
}

.card-draw-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.7);
  z-index: 0;
}

.card-draw-header {
  position: relative;
  z-index: 1;
  text-align: center;
  margin-bottom: 20px;
  width: 100%;
  max-width: 800px;
}

.game-logo {
  width: 100px;
  height: 100px;
  object-fit: contain;
  margin-bottom: 10px;
  border-radius: 50%;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  background-color: white;
  padding: 5px;
  animation: float 6s ease-in-out infinite;
}

.game-title {
  color: #333;
  margin-bottom: 10px !important;
  font-weight: bold;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.game-description {
  color: #666;
  margin-bottom: 20px !important;
  max-width: 600px;
  margin-left: auto !important;
  margin-right: auto !important;
}

.card-draw-content {
  position: relative;
  z-index: 1;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 20px;
  padding: 30px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  width: 100%;
  max-width: 1000px;
  margin-bottom: 30px;
}

.card-draw-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  width: 100%;
}

.loading-icon {
  font-size: 40px;
  color: #ff5722;
  animation: pulse 1.5s infinite ease-in-out;
}

.loading-text {
  margin-top: 20px;
  font-size: 16px;
  color: #666;
}

.settings-bar {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 20px;
}

.settings-item {
  display: flex;
  align-items: center;
  margin-left: 20px;
}

.settings-label {
  margin-right: 10px;
  color: #666;
  font-size: 14px;
}

.remaining-draws {
  text-align: center;
  margin: 10px 0 20px;
  font-size: 16px;
  color: #333;
  font-weight: bold;
}

.remaining-count {
  color: #ff5722;
  font-size: 18px;
  margin: 0 5px;
}

/* 卡片容器 */
.cards-container {
  margin: 20px 0;
  width: 100%;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

/* 卡片样式 */
.card {
  width: 100%;
  aspect-ratio: 3/4;
  perspective: 1000px;
  cursor: pointer;
  margin-bottom: 20px;
  border-radius: 10px;
  border: 2px solid #ff5722;
  box-shadow: 0 0 10px rgba(255, 87, 34, 0.3);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(255, 87, 34, 0.5);
}

.card.flipped .card-inner {
  transform: rotateY(180deg);
}

.card.winning {
  animation: shine 2s infinite;
}

.card-inner {
  position: relative;
  width: 100%;
  height: 100%;
  text-align: center;
  transition: transform 0.8s;
  transform-style: preserve-3d;
}

.card-front, .card-back {
  position: absolute;
  width: 100%;
  height: 100%;
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  border-radius: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
}

.card-front {
  background-color: #f5f5f5;
  background-size: cover;
  background-position: center;
}

.card-question {
  font-size: 40px;
  color: rgba(255, 255, 255, 0.8);
  background-color: rgba(0, 0, 0, 0.3);
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.card-back {
  background-color: white;
  transform: rotateY(180deg);
}

.card-prize, .card-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  padding: 10px;
}

.prize-image {
  width: 60px;
  height: 60px;
  object-fit: contain;
  margin-bottom: 10px;
}

.prize-name {
  font-size: 14px;
  font-weight: bold;
  color: #333;
  text-align: center;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.card-draw-actions {
  display: flex;
  justify-content: center;
  margin: 20px 0;
}

.reset-button {
  min-width: 120px;
  height: 40px;
  border-radius: 20px;
  font-weight: bold;
}

.action-buttons {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
  justify-content: center;
  margin-top: 20px;
}

.action-button {
  min-width: 110px;
  height: 40px;
  border-radius: 20px;
  transition: all 0.3s ease;
}

.action-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
}

.card-draw-footer {
  position: relative;
  z-index: 1;
  width: 100%;
  max-width: 1000px;
  text-align: center;
  padding: 15px 0;
  margin-top: 10px;
  color: #666;
  font-size: 14px;
}

.footer-links {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-top: 10px;
}

.footer-link {
  color: #666;
  text-decoration: none;
}

.footer-link:hover {
  color: #ff5722;
  text-decoration: underline;
}

/* 响应式样式 */
@media (max-width: 1200px) {
  .card-draw-content {
    max-width: 800px;
  }
}

@media (max-width: 992px) {
  .card-draw-content {
    max-width: 700px;
    padding: 25px;
  }

  .game-title {
    font-size: 28px;
  }
}

@media (max-width: 768px) {
  .card-draw-container {
    padding: 20px;
  }

  .card-draw-content {
    max-width: 100%;
    padding: 20px;
  }

  .game-title {
    font-size: 24px;
  }

  .game-description {
    font-size: 14px;
  }

  .prize-image {
    width: 40px;
    height: 40px;
  }

  .prize-name {
    font-size: 12px;
  }
}

@media (max-width: 576px) {
  .card-draw-container {
    padding: 15px;
  }

  .card-draw-content {
    padding: 15px;
  }

  .card {
    aspect-ratio: 2/3;
  }

  .prize-image {
    width: 30px;
    height: 30px;
  }

  .action-buttons {
    flex-direction: column;
    gap: 10px;
  }

  .action-button {
    width: 100%;
  }

  .game-logo {
    width: 80px;
    height: 80px;
  }

  .game-title {
    font-size: 20px;
  }

  .settings-bar {
    flex-direction: column;
    align-items: flex-end;
  }

  .settings-item {
    margin-left: 0;
    margin-bottom: 10px;
  }
}
