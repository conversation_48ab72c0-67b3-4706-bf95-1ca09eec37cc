#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
全渠道运营助手 - 商品管理API
"""

import logging
import uuid
from datetime import datetime
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query, Body
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_
from pydantic import BaseModel, Field

from db.database import get_db
from auth.dependencies import get_current_user, get_current_project
from models.user import User
from models.project import Project
from ..models.product_mapping import MultiChannelProductMapping, MultiChannelBatchOperation, ProductImageGeneration
from ..services.product_sync import ProductSyncService
from ..services.image_generator import AIImageGeneratorService

logger = logging.getLogger(__name__)
router = APIRouter(tags=["全渠道运营-商品管理"])

# Pydantic模型
class ProductMappingResponse(BaseModel):
    id: str
    product_id: str
    product_spec_id: Optional[str]
    channel_id: str
    external_product_id: Optional[str]
    external_spec_id: Optional[str]
    channel_product_data: Dict[str, Any]
    price_strategy: str
    base_price: Optional[float]
    current_price: Optional[float]
    min_price: Optional[float]
    max_price: Optional[float]
    stock_sync_enabled: bool
    current_stock: Optional[int]
    sync_status: str
    last_sync_at: Optional[str]
    sync_error: Optional[str]
    is_active: bool
    is_online: bool
    created_at: str
    updated_at: str

class ProductSyncRequest(BaseModel):
    product_id: str = Field(..., description="商品ID")
    channel_id: str = Field(..., description="渠道ID")
    product_data: Dict[str, Any] = Field(..., description="商品数据")

class BatchSyncRequest(BaseModel):
    product_ids: List[str] = Field(..., description="商品ID列表")
    channel_ids: List[str] = Field(..., description="渠道ID列表")

class MultiChannelSyncRequest(BaseModel):
    product_id: str = Field(..., description="商品ID")
    channel_ids: List[str] = Field(..., description="渠道ID列表")

class PriceUpdateRequest(BaseModel):
    mappings: List[Dict[str, Any]] = Field(..., description="价格更新列表")

class ImageGenerationRequest(BaseModel):
    product_id: str = Field(..., description="商品ID")
    prompt: str = Field(..., description="生成提示词")
    style: str = Field(default="natural", description="图片风格")
    size: str = Field(default="1024x1024", description="图片尺寸")
    model: str = Field(default="dall-e-3", description="AI模型")
    channel_id: Optional[str] = Field(None, description="目标渠道ID")

class BatchOperationResponse(BaseModel):
    id: str
    operation_type: str
    operation_name: str
    status: str
    progress: int
    total_items: int
    completed_items: int
    failed_items: int
    started_at: Optional[str]
    completed_at: Optional[str]

# 初始化服务
product_sync_service = ProductSyncService()
image_generator = AIImageGeneratorService()


@router.get("/mappings", response_model=List[ProductMappingResponse])
async def get_product_mappings(
    product_id: Optional[str] = Query(None, description="商品ID过滤"),
    channel_id: Optional[str] = Query(None, description="渠道ID过滤"),
    sync_status: Optional[str] = Query(None, description="同步状态过滤"),
    is_active: Optional[bool] = Query(None, description="是否启用过滤"),
    skip: int = Query(0, description="跳过数量"),
    limit: int = Query(50, description="限制数量"),
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """获取商品映射列表"""
    try:
        # 构建查询条件
        conditions = [MultiChannelProductMapping.project_id == project.id]

        if product_id:
            conditions.append(MultiChannelProductMapping.product_id == product_id)
        if channel_id:
            conditions.append(MultiChannelProductMapping.channel_id == channel_id)
        if sync_status:
            conditions.append(MultiChannelProductMapping.sync_status == sync_status)
        if is_active is not None:
            conditions.append(MultiChannelProductMapping.is_active == is_active)

        # 执行查询
        stmt = select(MultiChannelProductMapping).where(
            and_(*conditions)
        ).offset(skip).limit(limit).order_by(MultiChannelProductMapping.created_at.desc())

        result = await db.execute(stmt)
        mappings = result.scalars().all()

        # 转换为响应格式
        return [
            ProductMappingResponse(
                id=str(mapping.id),
                product_id=mapping.product_id,
                product_spec_id=mapping.product_spec_id,
                channel_id=mapping.channel_id,
                external_product_id=mapping.external_product_id,
                external_spec_id=mapping.external_spec_id,
                channel_product_data=mapping.channel_product_data or {},
                price_strategy=mapping.price_strategy,
                base_price=mapping.base_price,
                current_price=mapping.current_price,
                min_price=mapping.min_price,
                max_price=mapping.max_price,
                stock_sync_enabled=mapping.stock_sync_enabled,
                current_stock=mapping.current_stock,
                sync_status=mapping.sync_status,
                last_sync_at=mapping.last_sync_at.isoformat() if mapping.last_sync_at else None,
                sync_error=mapping.sync_error,
                is_active=mapping.is_active,
                is_online=mapping.is_online,
                created_at=mapping.created_at.isoformat(),
                updated_at=mapping.updated_at.isoformat()
            )
            for mapping in mappings
        ]

    except Exception as e:
        logger.error(f"获取商品映射列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取商品映射列表失败: {str(e)}")


@router.post("/sync")
async def sync_product_to_channel(
    request: ProductSyncRequest,
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """同步单个商品到指定渠道"""
    try:
        # 获取渠道配置
        channel_config = await _get_channel_config(db, request.channel_id)

        # 执行同步
        result = await product_sync_service.sync_product_to_channel(
            db=db,
            project_id=str(project.id),
            product_id=request.product_id,
            channel_id=request.channel_id,
            product_data=request.product_data,
            channel_config=channel_config,
            user_id=str(current_user.id)
        )

        return {
            "success": result["success"],
            "message": "同步成功" if result["success"] else f"同步失败: {result.get('error')}",
            "external_id": result.get("external_id"),
            "error": result.get("error")
        }

    except Exception as e:
        logger.error(f"商品同步失败: {e}")
        raise HTTPException(status_code=500, detail=f"商品同步失败: {str(e)}")


@router.post("/batch-sync")
async def batch_sync_products(
    request: BatchSyncRequest,
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """批量同步商品到多个渠道"""
    try:
        # 启动批量同步
        batch_id = await product_sync_service.batch_sync_products(
            db=db,
            project_id=str(project.id),
            product_ids=request.product_ids,
            channel_ids=request.channel_ids,
            user_id=str(current_user.id)
        )

        return {
            "success": True,
            "message": "批量同步任务已启动",
            "batch_id": batch_id
        }

    except Exception as e:
        logger.error(f"批量同步启动失败: {e}")
        raise HTTPException(status_code=500, detail=f"批量同步启动失败: {str(e)}")


@router.post("/multi-channel-sync")
async def sync_to_multiple_channels(
    request: MultiChannelSyncRequest,
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """一品多渠道同步优化"""
    try:
        result = await product_sync_service.sync_multiple_channels(
            db=db,
            project_id=str(project.id),
            product_id=request.product_id,
            channel_ids=request.channel_ids,
            user_id=str(current_user.id)
        )

        return {
            "success": result["success"],
            "message": f"已同步到{result['success_channels']}/{result['total_channels']}个渠道",
            "total_channels": result["total_channels"],
            "success_channels": result["success_channels"],
            "failed_channels": result["failed_channels"],
            "results": result["results"]
        }

    except Exception as e:
        logger.error(f"多渠道同步失败: {e}")
        raise HTTPException(status_code=500, detail=f"多渠道同步失败: {str(e)}")


@router.post("/update-prices")
async def batch_update_prices(
    request: PriceUpdateRequest,
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """批量更新商品价格"""
    try:
        success_count = 0
        total_count = len(request.mappings)
        results = []

        for mapping_update in request.mappings:
            mapping_id = mapping_update.get('mapping_id')
            new_price = mapping_update.get('price')

            if not mapping_id or new_price is None:
                results.append({
                    'mapping_id': mapping_id,
                    'success': False,
                    'error': '缺少必要参数'
                })
                continue

            try:
                # 查找映射记录
                stmt = select(MultiChannelProductMapping).where(
                    and_(
                        MultiChannelProductMapping.id == mapping_id,
                        MultiChannelProductMapping.project_id == project.id
                    )
                )
                result = await db.execute(stmt)
                mapping = result.scalar_one_or_none()

                if not mapping:
                    results.append({
                        'mapping_id': mapping_id,
                        'success': False,
                        'error': '映射记录不存在'
                    })
                    continue

                # 更新价格
                mapping.current_price = new_price
                mapping.updated_at = datetime.utcnow()

                # 如果有外部商品ID，同步到平台
                if mapping.external_product_id:
                    channel_config = await _get_channel_config(db, mapping.channel_id)
                    # 这里需要调用平台API更新价格
                    # 暂时标记为成功
                    pass

                success_count += 1
                results.append({
                    'mapping_id': mapping_id,
                    'success': True,
                    'new_price': new_price
                })

            except Exception as e:
                results.append({
                    'mapping_id': mapping_id,
                    'success': False,
                    'error': str(e)
                })

        await db.commit()

        return {
            "success": success_count > 0,
            "message": f"价格更新完成，成功{success_count}/{total_count}个",
            "total_count": total_count,
            "success_count": success_count,
            "results": results
        }

    except Exception as e:
        logger.error(f"批量更新价格失败: {e}")
        raise HTTPException(status_code=500, detail=f"批量更新价格失败: {str(e)}")


@router.post("/generate-image")
async def generate_product_image(
    request: ImageGenerationRequest,
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """生成商品AI图片"""
    try:
        result = await image_generator.generate_product_image(
            db=db,
            project_id=str(project.id),
            product_id=request.product_id,
            prompt=request.prompt,
            style=request.style,
            size=request.size,
            model=request.model,
            channel_id=request.channel_id,
            user_id=str(current_user.id)
        )

        return {
            "success": result["success"],
            "message": "图片生成成功" if result["success"] else f"图片生成失败: {result.get('error')}",
            "generation_id": result.get("generation_id"),
            "images": result.get("images", []),
            "error": result.get("error")
        }

    except Exception as e:
        logger.error(f"AI图片生成失败: {e}")
        raise HTTPException(status_code=500, detail=f"AI图片生成失败: {str(e)}")


@router.post("/generate-channel-image")
async def generate_channel_specific_image(
    product_id: str = Body(..., description="商品ID"),
    channel_id: str = Body(..., description="渠道ID"),
    base_prompt: str = Body(..., description="基础提示词"),
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """为特定渠道生成定制化商品图片"""
    try:
        result = await image_generator.generate_channel_specific_image(
            db=db,
            project_id=str(project.id),
            product_id=product_id,
            channel_id=channel_id,
            base_prompt=base_prompt,
            user_id=str(current_user.id)
        )

        return {
            "success": result["success"],
            "message": "渠道定制图片生成成功" if result["success"] else f"生成失败: {result.get('error')}",
            "generation_id": result.get("generation_id"),
            "images": result.get("images", []),
            "error": result.get("error")
        }

    except Exception as e:
        logger.error(f"渠道定制图片生成失败: {e}")
        raise HTTPException(status_code=500, detail=f"渠道定制图片生成失败: {str(e)}")


@router.get("/batch-operations", response_model=List[BatchOperationResponse])
async def get_batch_operations(
    operation_type: Optional[str] = Query(None, description="操作类型过滤"),
    status: Optional[str] = Query(None, description="状态过滤"),
    skip: int = Query(0, description="跳过数量"),
    limit: int = Query(20, description="限制数量"),
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """获取批量操作列表"""
    try:
        # 构建查询条件
        conditions = [MultiChannelBatchOperation.project_id == project.id]

        if operation_type:
            conditions.append(MultiChannelBatchOperation.operation_type == operation_type)
        if status:
            conditions.append(MultiChannelBatchOperation.status == status)

        # 执行查询
        stmt = select(MultiChannelBatchOperation).where(
            and_(*conditions)
        ).offset(skip).limit(limit).order_by(MultiChannelBatchOperation.created_at.desc())

        result = await db.execute(stmt)
        operations = result.scalars().all()

        # 转换为响应格式
        return [
            BatchOperationResponse(
                id=str(op.id),
                operation_type=op.operation_type,
                operation_name=op.operation_name,
                status=op.status,
                progress=op.progress,
                total_items=op.total_items,
                completed_items=op.completed_items,
                failed_items=op.failed_items,
                started_at=op.started_at.isoformat() if op.started_at else None,
                completed_at=op.completed_at.isoformat() if op.completed_at else None
            )
            for op in operations
        ]

    except Exception as e:
        logger.error(f"获取批量操作列表失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取批量操作列表失败: {str(e)}")


@router.get("/batch-operations/{operation_id}")
async def get_batch_operation_detail(
    operation_id: str,
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """获取批量操作详情"""
    try:
        stmt = select(MultiChannelBatchOperation).where(
            and_(
                MultiChannelBatchOperation.id == operation_id,
                MultiChannelBatchOperation.project_id == project.id
            )
        )
        result = await db.execute(stmt)
        operation = result.scalar_one_or_none()

        if not operation:
            raise HTTPException(status_code=404, detail="批量操作不存在")

        return {
            "id": str(operation.id),
            "operation_type": operation.operation_type,
            "operation_name": operation.operation_name,
            "target_channels": operation.target_channels,
            "target_products": operation.target_products,
            "operation_params": operation.operation_params,
            "status": operation.status,
            "progress": operation.progress,
            "total_items": operation.total_items,
            "completed_items": operation.completed_items,
            "failed_items": operation.failed_items,
            "result_summary": operation.result_summary,
            "error_details": operation.error_details,
            "started_at": operation.started_at.isoformat() if operation.started_at else None,
            "completed_at": operation.completed_at.isoformat() if operation.completed_at else None,
            "created_at": operation.created_at.isoformat(),
            "updated_at": operation.updated_at.isoformat()
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取批量操作详情失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取批量操作详情失败: {str(e)}")


@router.get("/image-generations")
async def get_image_generations(
    product_id: Optional[str] = Query(None, description="商品ID过滤"),
    skip: int = Query(0, description="跳过数量"),
    limit: int = Query(20, description="限制数量"),
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """获取AI图片生成历史"""
    try:
        result = await image_generator.get_generation_history(
            db=db,
            project_id=str(project.id),
            product_id=product_id,
            limit=limit
        )

        return {
            "success": True,
            "data": result
        }

    except Exception as e:
        logger.error(f"获取图片生成历史失败: {e}")
        raise HTTPException(status_code=500, detail=f"获取图片生成历史失败: {str(e)}")


@router.post("/select-image")
async def select_generated_image(
    generation_id: str = Body(..., description="生成记录ID"),
    image_url: str = Body(..., description="选中的图片URL"),
    current_user: User = Depends(get_current_user),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db)
):
    """选择AI生成的图片作为商品主图"""
    try:
        success = await image_generator.select_image_for_product(
            db=db,
            generation_id=generation_id,
            image_url=image_url,
            user_id=str(current_user.id)
        )

        return {
            "success": success,
            "message": "图片选择成功" if success else "图片选择失败"
        }

    except Exception as e:
        logger.error(f"选择图片失败: {e}")
        raise HTTPException(status_code=500, detail=f"选择图片失败: {str(e)}")


async def _get_channel_config(db: AsyncSession, channel_id: str) -> Dict[str, Any]:
    """获取渠道配置"""
    # 这里需要实现从数据库获取渠道配置的逻辑
    # 暂时返回模拟数据
    return {
        'platform_code': 'meituan',
        'api_credentials': {
            'app_id': 'test_app_id',
            'app_secret': 'test_app_secret',
            'access_token': 'test_access_token'
        }
    }
</rewritten_file> 