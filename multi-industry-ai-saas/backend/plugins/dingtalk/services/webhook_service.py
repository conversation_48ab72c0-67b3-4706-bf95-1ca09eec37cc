#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
钉钉Webhook事件处理服务
处理各种钉钉事件回调，包括审批、待办、通讯录变更等
"""

import logging
import json
import hmac
import hashlib
import base64
import uuid
from typing import Dict, Any, List, Optional
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_

from ..models.models import (
    DingTalkWebhook, 
    DingTalkEventLog,
    DingTalkApprovalInstance,
    DingTalkTodoTask,
    DingTalkUserMapping
)
from ..utils.dingtalk_api import DingTalkAPI

logger = logging.getLogger(__name__)

class WebhookService:
    """钉钉Webhook事件处理服务"""
    
    def __init__(self, db: AsyncSession, tenant_id: Optional[uuid.UUID] = None, project_id: Optional[uuid.UUID] = None):
        self.db = db
        self.tenant_id = tenant_id
        self.project_id = project_id
    
    async def create_subscription(
        self,
        tenant_id: uuid.UUID,
        project_id: uuid.UUID,
        event_types: List[str],
        callback_url: str,
        dingtalk_api: DingTalkAPI
    ) -> Dict[str, Any]:
        """
        创建事件订阅
        
        Args:
            tenant_id: 租户ID
            project_id: 项目ID
            event_types: 事件类型列表
            callback_url: 回调URL
            dingtalk_api: 钉钉API实例
            
        Returns:
            Dict[str, Any]: 创建结果
        """
        try:
            # 生成订阅密钥
            secret = self._generate_secret()
            
            # 创建Webhook记录
            webhook = DingTalkWebhook(
                id=uuid.uuid4(),
                tenant_id=tenant_id,
                project_id=project_id,
                event_types=event_types,
                callback_url=callback_url,
                secret=secret,
                enabled=True
            )
            
            self.db.add(webhook)
            await self.db.commit()
            
            logger.info(f"创建事件订阅成功: {event_types}")
            
            return {
                "webhook_id": str(webhook.id),
                "event_types": event_types,
                "callback_url": callback_url,
                "secret": secret
            }
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"创建事件订阅失败: {str(e)}")
            raise
    
    async def handle_event(
        self,
        event_data: Dict[str, Any],
        signature: str,
        timestamp: str
    ) -> Dict[str, Any]:
        """
        处理钉钉事件回调
        
        Args:
            event_data: 事件数据
            signature: 签名
            timestamp: 时间戳
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        try:
            event_type = event_data.get('EventType', '')
            
            logger.info(f"处理钉钉事件: {event_type}")
            
            # 根据事件类型分发处理
            if event_type == 'bpms_instance_change':
                return await self._handle_approval_instance_change(event_data)
            elif event_type == 'bpms_task_change':
                return await self._handle_approval_task_change(event_data)
            elif event_type == 'todo_task_change':
                return await self._handle_todo_task_change(event_data)
            elif event_type == 'todo_task_create':
                return await self._handle_todo_task_create(event_data)
            elif event_type == 'todo_task_update':
                return await self._handle_todo_task_update(event_data)
            elif event_type == 'todo_task_delete':
                return await self._handle_todo_task_delete(event_data)
            elif event_type == 'user_add_org':
                return await self._handle_user_add_org(event_data)
            elif event_type == 'user_modify_org':
                return await self._handle_user_modify_org(event_data)
            elif event_type == 'chat_update_title':
                return await self._handle_chat_update_title(event_data)
            else:
                logger.warning(f"未处理的事件类型: {event_type}")
                return {
                    "success": True,
                    "message": f"未处理的事件类型: {event_type}"
                }
                
        except Exception as e:
            logger.error(f"处理事件失败: {str(e)}")
            return {
                "success": False,
                "message": f"处理事件失败: {str(e)}"
            }
    
    async def _handle_approval_instance_change(self, event_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理审批实例状态变更事件
        
        Args:
            event_data: 事件数据
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        try:
            process_instance_id = event_data.get('processInstanceId')
            status = event_data.get('status')
            result = event_data.get('result')
            
            logger.info(f"审批实例状态变更: {process_instance_id}, 状态: {status}, 结果: {result}")
            
            # 查找本地审批实例
            query = select(DingTalkApprovalInstance).where(
                DingTalkApprovalInstance.instance_id == process_instance_id
            )
            db_result = await self.db.execute(query)
            instance = db_result.scalar_one_or_none()
            
            if instance:
                # 更新实例状态
                old_status = instance.status
                instance.status = status
                
                if status in ['COMPLETED', 'TERMINATED']:
                    instance.finish_time = datetime.utcnow()
                
                instance.last_sync_time = datetime.utcnow()
                await self.db.commit()
                
                logger.info(f"更新审批实例状态: {old_status} -> {status}")
                
                # 如果审批通过，触发业务逻辑
                if status == 'COMPLETED' and result == 'agree':
                    await self._trigger_approval_business_logic(instance, event_data)
                
                return {
                    "success": True,
                    "message": "审批实例状态更新成功",
                    "data": {
                        "instance_id": process_instance_id,
                        "old_status": old_status,
                        "new_status": status
                    }
                }
            else:
                logger.warning(f"未找到审批实例: {process_instance_id}")
                return {
                    "success": True,
                    "message": f"未找到审批实例: {process_instance_id}"
                }
                
        except Exception as e:
            logger.error(f"处理审批实例状态变更失败: {str(e)}")
            return {
                "success": False,
                "message": f"处理失败: {str(e)}"
            }
    
    async def _handle_approval_task_change(self, event_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理审批任务状态变更事件
        
        Args:
            event_data: 事件数据
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        try:
            process_instance_id = event_data.get('processInstanceId')
            task_id = event_data.get('taskId')
            status = event_data.get('status')
            
            logger.info(f"审批任务状态变更: 任务{task_id}, 实例{process_instance_id}, 状态: {status}")
            
            # 这里可以添加任务状态变更的业务逻辑
            # 例如：发送通知、更新任务状态等
            
            return {
                "success": True,
                "message": "审批任务状态变更处理成功",
                "data": {
                    "task_id": task_id,
                    "instance_id": process_instance_id,
                    "status": status
                }
            }
            
        except Exception as e:
            logger.error(f"处理审批任务状态变更失败: {str(e)}")
            return {
                "success": False,
                "message": f"处理失败: {str(e)}"
            }
    
    async def _handle_todo_task_change(self, event_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理待办任务变更事件
        
        Args:
            event_data: 事件数据
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        try:
            task_id = event_data.get('taskId')
            status = event_data.get('status')
            user_id = event_data.get('userId')
            
            logger.info(f"待办任务变更: 任务{task_id}, 用户{user_id}, 状态: {status}")
            
            # 查找本地待办任务
            query = select(DingTalkTodoTask).where(
                DingTalkTodoTask.dingtalk_record_id == task_id
            )
            db_result = await self.db.execute(query)
            todo_task = db_result.scalar_one_or_none()
            
            if todo_task:
                # 更新任务状态
                old_status = todo_task.status
                todo_task.status = 'completed' if status == 1 else 'pending'
                todo_task.dingtalk_status = status
                todo_task.last_sync_at = datetime.utcnow()
                
                await self.db.commit()
                
                logger.info(f"更新待办任务状态: {old_status} -> {todo_task.status}")
                
                return {
                    "success": True,
                    "message": "待办任务状态更新成功",
                    "data": {
                        "task_id": task_id,
                        "old_status": old_status,
                        "new_status": todo_task.status
                    }
                }
            else:
                logger.warning(f"未找到待办任务: {task_id}")
                return {
                    "success": True,
                    "message": f"未找到待办任务: {task_id}"
                }
                
        except Exception as e:
            logger.error(f"处理待办任务变更失败: {str(e)}")
            return {
                "success": False,
                "message": f"处理失败: {str(e)}"
            }
    
    async def _handle_todo_task_create(self, event_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理待办任务创建事件
        
        Args:
            event_data: 事件数据
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        try:
            task_id = event_data.get('taskId')
            subject = event_data.get('subject', '')
            creator_id = event_data.get('creatorId')
            executor_ids = event_data.get('executorIds', [])
            created_time = event_data.get('createdTime')
            due_time = event_data.get('dueTime')
            priority = event_data.get('priority', 0)
            source_id = event_data.get('sourceId', '')
            
            logger.info(f"待办任务创建: 任务{task_id}, 标题: {subject}, 创建者: {creator_id}")
            
            # 记录事件日志 - 使用实际的tenant_id和project_id
            # 从事件数据或实例属性获取实际值
            actual_tenant_id = self.tenant_id
            actual_project_id = self.project_id
            
            # 如果实例属性为空，尝试从数据库查找第一个可用的租户和项目
            if not actual_tenant_id or not actual_project_id:
                from models.tenant import Tenant
                from models.project import Project
                
                if not actual_tenant_id:
                    tenant_stmt = select(Tenant).limit(1)
                    tenant_result = await self.db.execute(tenant_stmt)
                    tenant = tenant_result.scalar_one_or_none()
                    actual_tenant_id = tenant.id if tenant else None
                
                if not actual_project_id and actual_tenant_id:
                    project_stmt = select(Project).where(Project.tenant_id == actual_tenant_id).limit(1)
                    project_result = await self.db.execute(project_stmt)
                    project = project_result.scalar_one_or_none()
                    actual_project_id = project.id if project else None
            
            if actual_tenant_id and actual_project_id:
                event_log = DingTalkEventLog(
                    id=uuid.uuid4(),
                    tenant_id=actual_tenant_id,
                    project_id=actual_project_id,
                    event_type='todo_task_create',
                    event_data=event_data,
                    status='processed'
                )
                self.db.add(event_log)
            else:
                logger.warning("无法获取有效的tenant_id或project_id，跳过事件日志记录")
            
            # 检查是否已存在该任务
            query = select(DingTalkTodoTask).where(
                DingTalkTodoTask.dingtalk_record_id == task_id
            )
            db_result = await self.db.execute(query)
            existing_task = db_result.scalar_one_or_none()
            
            if not existing_task:
                # 创建新的待办任务记录
                todo_task = DingTalkTodoTask(
                    id=uuid.uuid4(),
                    dingtalk_record_id=task_id,
                    title=subject,
                    status='pending',
                    dingtalk_status=0,  # 0表示未完成
                    creator_id=creator_id,
                    executor_ids=executor_ids,
                    priority=priority,
                    source_id=source_id,
                    created_at=datetime.fromtimestamp(created_time / 1000) if created_time else datetime.utcnow(),
                    due_time=datetime.fromtimestamp(due_time / 1000) if due_time else None,
                    last_sync_at=datetime.utcnow()
                )
                self.db.add(todo_task)
                
                await self.db.commit()
                
                logger.info(f"创建待办任务记录成功: {task_id}")
                
                return {
                    "success": True,
                    "message": "待办任务创建事件处理成功",
                    "data": {
                        "task_id": task_id,
                        "subject": subject,
                        "creator_id": creator_id,
                        "executor_ids": executor_ids
                    }
                }
            else:
                logger.info(f"待办任务已存在: {task_id}")
                return {
                    "success": True,
                    "message": f"待办任务已存在: {task_id}"
                }
                
        except Exception as e:
            await self.db.rollback()
            logger.error(f"处理待办任务创建失败: {str(e)}")
            return {
                "success": False,
                "message": f"处理失败: {str(e)}"
            }
    
    async def _handle_todo_task_update(self, event_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理待办任务更新事件
        
        Args:
            event_data: 事件数据
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        try:
            task_id = event_data.get('taskId')
            subject = event_data.get('subject', '')
            status = event_data.get('status')
            is_done = event_data.get('isDone', False)
            updated_time = event_data.get('updatedTime')
            
            logger.info(f"待办任务更新: 任务{task_id}, 标题: {subject}, 状态: {status}, 是否完成: {is_done}")
            
            # 记录事件日志 - 使用实际的tenant_id和project_id
            actual_tenant_id = self.tenant_id
            actual_project_id = self.project_id
            
            # 如果实例属性为空，尝试从数据库查找第一个可用的租户和项目
            if not actual_tenant_id or not actual_project_id:
                from models.tenant import Tenant
                from models.project import Project
                
                if not actual_tenant_id:
                    tenant_stmt = select(Tenant).limit(1)
                    tenant_result = await self.db.execute(tenant_stmt)
                    tenant = tenant_result.scalar_one_or_none()
                    actual_tenant_id = tenant.id if tenant else None
                
                if not actual_project_id and actual_tenant_id:
                    project_stmt = select(Project).where(Project.tenant_id == actual_tenant_id).limit(1)
                    project_result = await self.db.execute(project_stmt)
                    project = project_result.scalar_one_or_none()
                    actual_project_id = project.id if project else None
            
            if actual_tenant_id and actual_project_id:
                event_log = DingTalkEventLog(
                    id=uuid.uuid4(),
                    tenant_id=actual_tenant_id,
                    project_id=actual_project_id,
                    event_type='todo_task_update',
                    event_data=event_data,
                    status='processed'
                )
                self.db.add(event_log)
            else:
                logger.warning("无法获取有效的tenant_id或project_id，跳过事件日志记录")
            
            # 查找并更新现有任务
            query = select(DingTalkTodoTask).where(
                DingTalkTodoTask.dingtalk_task_id == task_id
            )
            db_result = await self.db.execute(query)
            existing_task = db_result.scalar_one_or_none()
            
            if existing_task:
                # 更新任务信息
                existing_task.subject = subject
                existing_task.title = subject
                existing_task.is_done = is_done
                existing_task.status = "completed" if is_done else "pending"
                existing_task.dingtalk_status = 1 if is_done else 0
                existing_task.last_sync_at = datetime.utcnow()
                existing_task.updated_at = datetime.utcnow()
                existing_task.raw_data = event_data
                
                await self.db.commit()
                
                logger.info(f"更新待办任务记录成功: {task_id}")
                
                return {
                    "success": True,
                    "message": "待办任务更新事件处理成功",
                    "data": {
                        "task_id": task_id,
                        "subject": subject,
                        "is_done": is_done,
                        "status": existing_task.status
                    }
                }
            else:
                logger.warning(f"未找到待办任务: {task_id}")
                return {
                    "success": True,
                    "message": f"未找到待办任务: {task_id}"
                }
                
        except Exception as e:
            await self.db.rollback()
            logger.error(f"处理待办任务更新失败: {str(e)}")
            return {
                "success": False,
                "message": f"处理失败: {str(e)}"
            }
    
    async def _handle_todo_task_delete(self, event_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理待办任务删除事件
        
        Args:
            event_data: 事件数据
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        try:
            task_id = event_data.get('taskId')
            subject = event_data.get('subject', '')
            deleted_time = event_data.get('deletedTime')
            
            logger.info(f"待办任务删除: 任务{task_id}, 标题: {subject}, 删除时间: {deleted_time}")
            
            # 记录事件日志 - 使用实际的tenant_id和project_id
            actual_tenant_id = self.tenant_id
            actual_project_id = self.project_id
            
            # 如果实例属性为空，尝试从数据库查找第一个可用的租户和项目
            if not actual_tenant_id or not actual_project_id:
                from models.tenant import Tenant
                from models.project import Project
                
                if not actual_tenant_id:
                    tenant_stmt = select(Tenant).limit(1)
                    tenant_result = await self.db.execute(tenant_stmt)
                    tenant = tenant_result.scalar_one_or_none()
                    actual_tenant_id = tenant.id if tenant else None
                
                if not actual_project_id and actual_tenant_id:
                    project_stmt = select(Project).where(Project.tenant_id == actual_tenant_id).limit(1)
                    project_result = await self.db.execute(project_stmt)
                    project = project_result.scalar_one_or_none()
                    actual_project_id = project.id if project else None
            
            if actual_tenant_id and actual_project_id:
                event_log = DingTalkEventLog(
                    id=uuid.uuid4(),
                    tenant_id=actual_tenant_id,
                    project_id=actual_project_id,
                    event_type='todo_task_delete',
                    event_data=event_data,
                    status='processed'
                )
                self.db.add(event_log)
            else:
                logger.warning("无法获取有效的tenant_id或project_id，跳过事件日志记录")
            
            # 查找并删除本地任务记录
            if task_id:
                query = select(DingTalkTodoTask).where(
                    DingTalkTodoTask.dingtalk_task_id == task_id
                )
                db_result = await self.db.execute(query)
                existing_task = db_result.scalar_one_or_none()
                
                if existing_task:
                    # 软删除：标记为已删除而不是物理删除
                    existing_task.is_deleted = True
                    existing_task.deleted_at = datetime.utcnow()
                    existing_task.updated_at = datetime.utcnow()
                    
                    logger.info(f"标记任务为已删除: {task_id}")
                else:
                    logger.warning(f"未找到要删除的任务: {task_id}")
            
            await self.db.commit()
            
            return {
                "success": True,
                "message": "待办任务删除事件处理成功",
                "data": {
                    "task_id": task_id,
                    "subject": subject,
                    "deleted_time": deleted_time
                }
            }
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"处理待办任务删除失败: {str(e)}")
            return {
                "success": False,
                "message": f"处理失败: {str(e)}"
            }
    
    async def _handle_user_add_org(self, event_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理用户加入企业事件
        
        Args:
            event_data: 事件数据
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        try:
            user_id = event_data.get('UserId')
            user_name = event_data.get('UserName')
            
            logger.info(f"用户加入企业: {user_id} ({user_name})")
            
            # 这里可以添加用户加入企业的业务逻辑
            # 例如：自动创建用户映射、发送欢迎消息等
            
            return {
                "success": True,
                "message": "用户加入企业事件处理成功",
                "data": {
                    "user_id": user_id,
                    "user_name": user_name
                }
            }
            
        except Exception as e:
            logger.error(f"处理用户加入企业事件失败: {str(e)}")
            return {
                "success": False,
                "message": f"处理失败: {str(e)}"
            }
    
    async def _handle_user_modify_org(self, event_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理用户信息变更事件
        
        Args:
            event_data: 事件数据
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        try:
            user_id = event_data.get('UserId')
            user_name = event_data.get('UserName')
            
            logger.info(f"用户信息变更: {user_id} ({user_name})")
            
            # 查找并更新用户映射
            query = select(DingTalkUserMapping).where(
                DingTalkUserMapping.dingtalk_user_id == user_id
            )
            db_result = await self.db.execute(query)
            user_mappings = db_result.scalars().all()
            
            for mapping in user_mappings:
                mapping.dingtalk_name = user_name
                mapping.updated_at = datetime.utcnow()
            
            if user_mappings:
                await self.db.commit()
                logger.info(f"更新用户映射: {len(user_mappings)} 条记录")
            
            return {
                "success": True,
                "message": "用户信息变更事件处理成功",
                "data": {
                    "user_id": user_id,
                    "user_name": user_name,
                    "updated_mappings": len(user_mappings)
                }
            }
            
        except Exception as e:
            logger.error(f"处理用户信息变更事件失败: {str(e)}")
            return {
                "success": False,
                "message": f"处理失败: {str(e)}"
            }
    
    async def _handle_chat_update_title(self, event_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        处理群聊标题变更事件
        
        Args:
            event_data: 事件数据
            
        Returns:
            Dict[str, Any]: 处理结果
        """
        try:
            chat_id = event_data.get('ChatId')
            title = event_data.get('Title')
            
            logger.info(f"群聊标题变更: {chat_id} -> {title}")
            
            # 这里可以添加群聊标题变更的业务逻辑
            
            return {
                "success": True,
                "message": "群聊标题变更事件处理成功",
                "data": {
                    "chat_id": chat_id,
                    "title": title
                }
            }
            
        except Exception as e:
            logger.error(f"处理群聊标题变更事件失败: {str(e)}")
            return {
                "success": False,
                "message": f"处理失败: {str(e)}"
            }
    
    async def _trigger_approval_business_logic(
        self,
        instance: DingTalkApprovalInstance,
        event_data: Dict[str, Any]
    ):
        """
        触发审批通过后的业务逻辑
        
        Args:
            instance: 审批实例
            event_data: 事件数据
        """
        try:
            process_code = instance.process_code
            
            logger.info(f"触发审批业务逻辑: {process_code}")
            
            # 根据审批模板类型执行不同的业务逻辑
            if 'loss' in process_code.lower():
                await self._handle_loss_approval(instance)
            elif 'expense' in process_code.lower():
                await self._handle_expense_approval(instance)
            elif 'sales' in process_code.lower():
                await self._handle_sales_approval(instance)
            else:
                logger.info(f"未配置业务逻辑的审批类型: {process_code}")
                
        except Exception as e:
            logger.error(f"触发审批业务逻辑失败: {str(e)}")
    
    async def _handle_loss_approval(self, instance: DingTalkApprovalInstance):
        """处理报损审批通过后的业务逻辑"""
        logger.info(f"处理报损审批: {instance.instance_id}")
        # 这里可以添加报损审批通过后的业务逻辑
        # 例如：更新库存、记录报损记录等
    
    async def _handle_expense_approval(self, instance: DingTalkApprovalInstance):
        """处理费用审批通过后的业务逻辑"""
        logger.info(f"处理费用审批: {instance.instance_id}")
        # 这里可以添加费用审批通过后的业务逻辑
        # 例如：记录财务账目、发起付款流程等
    
    async def _handle_sales_approval(self, instance: DingTalkApprovalInstance):
        """处理销售审批通过后的业务逻辑"""
        logger.info(f"处理销售审批: {instance.instance_id}")
        # 这里可以添加销售审批通过后的业务逻辑
        # 例如：更新销售记录、计算提成等
    
    def _generate_secret(self) -> str:
        """生成订阅密钥"""
        import secrets
        return secrets.token_urlsafe(32)
    
    def _verify_signature(self, body: str, signature: str, secret: str) -> bool:
        """验证签名"""
        try:
            expected_signature = hmac.new(
                secret.encode('utf-8'),
                body.encode('utf-8'),
                hashlib.sha256
            ).hexdigest()
            
            return hmac.compare_digest(signature, expected_signature)
        except Exception as e:
            logger.error(f"验证签名失败: {str(e)}")
            return False