#!/usr/bin/env python
# -*- coding: utf-8 -*-

from typing import List, Optional, Dict, Any
from pydantic import BaseModel, UUID4, Field, EmailStr
from datetime import datetime

# 供应商相关模型
class SupplierBase(BaseModel):
    """供应商基础模型"""
    name: str
    contact_person: Optional[str] = None
    contact_phone: Optional[str] = None
    email: Optional[EmailStr] = None
    address: Optional[str] = None
    description: Optional[str] = None
    is_active: bool = True

class SupplierCreate(SupplierBase):
    """创建供应商模型"""
    pass

class SupplierUpdate(SupplierBase):
    """更新供应商模型"""
    name: Optional[str] = None
    is_active: Optional[bool] = None

class SupplierInDB(SupplierBase):
    """数据库供应商模型"""
    id: UUID4
    tenant_id: Optional[UUID4] = None
    project_id: UUID4
    created_at: datetime
    updated_at: datetime
    created_by: Optional[UUID4] = None
    updated_by: Optional[UUID4] = None

    class Config:
        model_config = {"from_attributes": True}

class SupplierResponse(BaseModel):
    """供应商响应模型"""
    success: bool
    message: Optional[str] = None
    data: Optional[SupplierInDB] = None

class SupplierListResponse(BaseModel):
    """供应商列表响应模型"""
    success: bool
    message: Optional[str] = None
    data: List[SupplierInDB]
    total: int
    page: Optional[int] = None
    page_size: Optional[int] = None
    pages: Optional[int] = None

# 供应商产品关联相关模型
class SupplierProductBase(BaseModel):
    """供应商产品关联基础模型"""
    supplier_id: UUID4
    product_id: UUID4
    supplier_product_code: Optional[str] = None
    price: Optional[float] = None
    min_order_quantity: Optional[int] = None
    lead_time_days: Optional[int] = None
    is_preferred: bool = False

class SupplierProductCreate(SupplierProductBase):
    """创建供应商产品关联模型"""
    pass

class SupplierProductUpdate(SupplierProductBase):
    """更新供应商产品关联模型"""
    supplier_id: Optional[UUID4] = None
    product_id: Optional[UUID4] = None
    is_preferred: Optional[bool] = None

class SupplierProductInDB(SupplierProductBase):
    """数据库供应商产品关联模型"""
    id: UUID4
    tenant_id: Optional[UUID4] = None
    project_id: UUID4
    created_at: datetime
    updated_at: datetime
    created_by: Optional[UUID4] = None
    updated_by: Optional[UUID4] = None
    supplier_name: Optional[str] = None  # 非数据库字段，用于前端展示
    product_name: Optional[str] = None  # 非数据库字段，用于前端展示
    product_sku: Optional[str] = None  # 非数据库字段，用于前端展示

    class Config:
        model_config = {"from_attributes": True}

class SupplierProductResponse(BaseModel):
    """供应商产品关联响应模型"""
    success: bool
    message: Optional[str] = None
    data: Optional[SupplierProductInDB] = None

class SupplierProductListResponse(BaseModel):
    """供应商产品关联列表响应模型"""
    success: bool
    message: Optional[str] = None
    data: List[SupplierProductInDB]
    total: int
    page: Optional[int] = None
    page_size: Optional[int] = None
    pages: Optional[int] = None

# 供应商评估相关模型
class SupplierEvaluationBase(BaseModel):
    """供应商评估基础模型"""
    supplier_id: UUID4
    evaluation_date: datetime
    quality_score: Optional[int] = None
    delivery_score: Optional[int] = None
    price_score: Optional[int] = None
    service_score: Optional[int] = None
    overall_score: Optional[float] = None
    comments: Optional[str] = None

class SupplierEvaluationCreate(SupplierEvaluationBase):
    """创建供应商评估模型"""
    pass

class SupplierEvaluationUpdate(SupplierEvaluationBase):
    """更新供应商评估模型"""
    supplier_id: Optional[UUID4] = None
    evaluation_date: Optional[datetime] = None

class SupplierEvaluationInDB(SupplierEvaluationBase):
    """数据库供应商评估模型"""
    id: UUID4
    tenant_id: Optional[UUID4] = None
    project_id: UUID4
    created_at: datetime
    updated_at: datetime
    created_by: UUID4
    updated_by: Optional[UUID4] = None
    supplier_name: Optional[str] = None  # 非数据库字段，用于前端展示
    evaluator_name: Optional[str] = None  # 非数据库字段，用于前端展示

    class Config:
        model_config = {"from_attributes": True}

class SupplierEvaluationResponse(BaseModel):
    """供应商评估响应模型"""
    success: bool
    message: Optional[str] = None
    data: Optional[SupplierEvaluationInDB] = None

class SupplierEvaluationListResponse(BaseModel):
    """供应商评估列表响应模型"""
    success: bool
    message: Optional[str] = None
    data: List[SupplierEvaluationInDB]
    total: int
    page: Optional[int] = None
    page_size: Optional[int] = None
    pages: Optional[int] = None
