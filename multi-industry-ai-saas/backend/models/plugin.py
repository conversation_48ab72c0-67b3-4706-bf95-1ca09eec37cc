from sqlalchemy import Column, <PERSON>, <PERSON>olean, DateTime, ForeignKey, JSON, Text, Integer, Float, UniqueConstraint
from sqlalchemy.dialects.postgresql import UUID, ARRAY
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid
from datetime import datetime

from db.database import Base

class Plugin(Base):
    """插件模型"""
    __tablename__ = "plugins"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    code = Column(String(100), nullable=False, unique=True)
    name = Column(String(100), nullable=False)
    description = Column(Text, nullable=True)
    version = Column(String(20), nullable=False)
    author = Column(String(100), nullable=True)
    website = Column(String(255), nullable=True)
    icon_url = Column(String(255), nullable=True)
    category = Column(String(50), nullable=False)  # ai, message, marketing, financial, analysis, etc.
    price = Column(Float, nullable=False, default=0)
    billing_cycle = Column(String(20), nullable=False, default="monthly")  # monthly, quarterly, yearly, one_time
    is_system = Column(Boolean, default=False)
    is_active = Column(Boolean, default=True)
    requires_subscription = Column(Boolean, default=False)
    min_subscription_plan = Column(String(50), nullable=True)
    installation_path = Column(String(255), nullable=True)
    entry_point = Column(String(255), nullable=True)
    settings_schema = Column(JSON, default=dict)
    features = Column(ARRAY(String), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # 关系
    versions = relationship("PluginVersion", back_populates="plugin", cascade="all, delete-orphan")
    tenant_plugins = relationship("TenantPlugin", back_populates="plugin", cascade="all, delete-orphan")
    purchases = relationship("PluginPurchase", back_populates="plugin", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<Plugin(id={self.id}, name='{self.name}', version='{self.version}')>"


class PluginVersion(Base):
    """插件版本模型"""
    __tablename__ = "plugin_versions"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    plugin_id = Column(UUID(as_uuid=True), ForeignKey("plugins.id", ondelete="CASCADE"), nullable=False)
    version = Column(String(20), nullable=False)
    release_notes = Column(Text, nullable=True)
    download_url = Column(String(255), nullable=True)
    checksum = Column(String(100), nullable=True)
    min_system_version = Column(String(20), nullable=True)
    is_compatible = Column(Boolean, default=True)
    is_latest = Column(Boolean, default=False)
    released_at = Column(DateTime(timezone=True), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # 关系
    plugin = relationship("Plugin", back_populates="versions")

    __table_args__ = (
        UniqueConstraint('plugin_id', 'version', name='uq_plugin_version'),
    )

    def __repr__(self):
        return f"<PluginVersion(id={self.id}, plugin_id='{self.plugin_id}', version='{self.version}')>"


class TenantPlugin(Base):
    """租户插件模型"""
    __tablename__ = "tenant_plugins"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id", ondelete="CASCADE"), nullable=False)
    plugin_id = Column(UUID(as_uuid=True), ForeignKey("plugins.id", ondelete="CASCADE"), nullable=False)
    status = Column(String(20), nullable=False, default="active")  # active, disabled, uninstalled
    version = Column(String(20), nullable=False)
    installed_at = Column(DateTime(timezone=True), server_default=func.now())
    installed_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True)
    last_updated_at = Column(DateTime(timezone=True), nullable=True)
    settings = Column(JSON, default=dict)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # 关系
    tenant = relationship("Tenant", back_populates="plugins")
    plugin = relationship("Plugin", back_populates="tenant_plugins")
    installer = relationship("User", foreign_keys=[installed_by])

    __table_args__ = (
        UniqueConstraint('tenant_id', 'plugin_id', name='uq_tenant_plugin'),
    )

    def __repr__(self):
        return f"<TenantPlugin(id={self.id}, tenant_id='{self.tenant_id}', plugin_id='{self.plugin_id}')>"


class PluginPurchase(Base):
    """插件购买记录模型"""
    __tablename__ = "plugin_purchases"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), ForeignKey("tenants.id", ondelete="CASCADE"), nullable=False)
    plugin_id = Column(UUID(as_uuid=True), ForeignKey("plugins.id", ondelete="CASCADE"), nullable=False)
    service_provider_id = Column(UUID(as_uuid=True), ForeignKey("service_providers.id"), nullable=True)
    order_number = Column(String(50), nullable=False, unique=True)
    amount = Column(Float, nullable=False)
    currency = Column(String(10), nullable=False, default="CNY")
    billing_cycle = Column(String(20), nullable=False, default="monthly")  # monthly, quarterly, yearly, one_time
    duration = Column(Integer, nullable=False, default=1)  # 购买周期数量
    start_date = Column(DateTime(timezone=True), nullable=False)
    end_date = Column(DateTime(timezone=True), nullable=True)
    status = Column(String(20), nullable=False, default="active")  # active, expired, cancelled
    payment_method = Column(String(50), nullable=True)
    payment_status = Column(String(20), nullable=False, default="paid")  # pending, paid, failed, refunded
    purchased_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # 关系
    tenant = relationship("Tenant", back_populates="plugin_purchases")
    plugin = relationship("Plugin", back_populates="purchases")
    service_provider = relationship("ServiceProvider", back_populates="plugin_sales")
    purchaser = relationship("User", foreign_keys=[purchased_by])

    def __repr__(self):
        return f"<PluginPurchase(id={self.id}, tenant_id='{self.tenant_id}', plugin_id='{self.plugin_id}')>"
