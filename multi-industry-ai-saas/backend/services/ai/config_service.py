#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
AI 配置服务
"""

import logging
import uuid
from typing import List, Optional, Dict, Any, Tuple
from sqlalchemy import select, func, or_, and_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import joinedload

from models.ai import AIConfig, AIProvider, AIModel
from models.tenant import Tenant
from models.project import Project
from schemas.ai import AIConfigCreate, AIConfigUpdate
from services.ai.provider_service import AIProviderService
from services.ai.model_service import AIModelService
from core.security import encrypt_text, decrypt_text

logger = logging.getLogger(__name__)

class AIConfigService:
    """AI 配置服务"""

    @staticmethod
    async def get_configs(
        db: AsyncSession,
        skip: int = 0,
        limit: int = 100,
        tenant_id: Optional[uuid.UUID] = None,
        project_id: Optional[uuid.UUID] = None,
        provider_id: Optional[uuid.UUID] = None,
        model_id: Optional[uuid.UUID] = None,
        status: Optional[str] = None,
    ) -> <PERSON><PERSON>[List[AIConfig], int]:
        """
        获取 AI 配置列表
        """
        # 构建基础查询，确保预加载关联数据
        query = select(AIConfig).options(
            joinedload(AIConfig.provider, innerjoin=False),
            joinedload(AIConfig.model, innerjoin=False),
            joinedload(AIConfig.tenant, innerjoin=False),
            joinedload(AIConfig.project, innerjoin=False)
        )
        
        # 构建查询条件
        conditions = []
        
        # 如果指定了项目 ID，则查询项目级配置和系统级配置
        if project_id:
            conditions.append(
                or_(
                    AIConfig.project_id == project_id,
                    and_(AIConfig.project_id.is_(None), AIConfig.tenant_id.is_(None))
                )
            )
        # 如果指定了租户 ID，则查询租户级配置和系统级配置
        elif tenant_id:
            conditions.append(
                or_(
                    AIConfig.tenant_id == tenant_id,
                    and_(AIConfig.project_id.is_(None), AIConfig.tenant_id.is_(None))
                )
            )
            
        if provider_id:
            conditions.append(AIConfig.provider_id == provider_id)
            
        if model_id:
            conditions.append(AIConfig.model_id == model_id)
            
        if status:
            conditions.append(AIConfig.status == status)
            
        if conditions:
            query = query.where(and_(*conditions))
            
        # 获取总数
        count_query = select(func.count()).select_from(
            select(AIConfig).where(and_(*conditions)) if conditions else select(AIConfig)
        )
        total = await db.scalar(count_query)
        
        # 获取分页数据
        query = query.offset(skip).limit(limit).order_by(AIConfig.created_at.desc())
        result = await db.execute(query)
        configs = result.scalars().unique().all()  # 使用unique()避免重复数据
        
        # 手动确保关联数据完整性
        from services.ai.model_service import AIModelService
        from services.ai.provider_service import AIProviderService
        
        for config in configs:
            try:
                # 检查并补充模型信息
                if not hasattr(config, 'model') or not config.model:
                    logger.warning(f"配置 {config.name} 缺少模型关联，手动加载模型 {config.model_id}")
                    model = await AIModelService.get_model(db, config.model_id)
                    if model:
                        # 临时设置模型关联（不持久化）
                        object.__setattr__(config, 'model', model)
                    else:
                        logger.error(f"无法找到模型 {config.model_id}")
                
                # 检查并补充提供商信息
                if not hasattr(config, 'provider') or not config.provider:
                    logger.warning(f"配置 {config.name} 缺少提供商关联，手动加载提供商 {config.provider_id}")
                    provider = await AIProviderService.get_provider(db, config.provider_id)
                    if provider:
                        # 临时设置提供商关联（不持久化）
                        object.__setattr__(config, 'provider', provider)
                    else:
                        logger.error(f"无法找到提供商 {config.provider_id}")
                        
                # 验证关联数据
                logger.debug(f"配置 {config.name}: "
                           f"model_loaded={hasattr(config, 'model') and config.model is not None}, "
                           f"provider_loaded={hasattr(config, 'provider') and config.provider is not None}")
                        
            except Exception as e:
                logger.error(f"处理配置 {config.id} 的关联数据时出错: {e}")
        
        # 解密 API 密钥
        for config in configs:
            if config.api_key:
                config.api_key = decrypt_text(config.api_key)
        
        # 应用能力覆盖并计算有效能力
        for config in configs:
            if config.model and config.model.capabilities:
                # 获取基础能力
                base_capabilities = config.model.capabilities or {}
                
                # 应用配置中的能力覆盖（如果有的话）
                if config.config and config.config.get("capabilities_override"):
                    capabilities_override = config.config["capabilities_override"]
                    # 合并基础能力和覆盖能力，创建有效能力
                    effective_capabilities = {**base_capabilities, **capabilities_override}
                    
                    # 临时将有效能力设置到模型对象上，供前端使用
                    # 注意：这不会持久化到数据库，只是为了API响应
                    object.__setattr__(config.model, 'effective_capabilities', effective_capabilities)
                    
                    # 同时也更新capabilities字段，这样前端可以直接使用
                    object.__setattr__(config.model, 'capabilities', effective_capabilities)
                    
                    logger.debug(f"配置 {config.name} 应用能力覆盖: {capabilities_override}")
                else:
                    # 没有能力覆盖，有效能力就是基础能力
                    object.__setattr__(config.model, 'effective_capabilities', base_capabilities)
        
        return configs, total

    @staticmethod
    async def get_config(
        db: AsyncSession,
        config_id: uuid.UUID,
    ) -> Optional[AIConfig]:
        """
        获取 AI 配置详情
        """
        query = select(AIConfig).options(
            joinedload(AIConfig.provider),
            joinedload(AIConfig.model),
            joinedload(AIConfig.tenant),
            joinedload(AIConfig.project)
        ).where(AIConfig.id == config_id)
        
        result = await db.execute(query)
        config = result.scalars().first()
        
        # 解密 API 密钥
        if config and config.api_key:
            config.api_key = decrypt_text(config.api_key)
            
        return config

    @staticmethod
    async def create_config(
        db: AsyncSession,
        config_data: AIConfigCreate,
    ) -> AIConfig:
        """
        创建 AI 配置
        """
        # 检查提供商是否存在
        provider = await AIProviderService.get_provider(db, config_data.provider_id)
        if not provider:
            raise ValueError(f"提供商 ID '{config_data.provider_id}' 不存在")
            
        # 检查模型是否存在
        model = await AIModelService.get_model(db, config_data.model_id)
        if not model:
            raise ValueError(f"模型 ID '{config_data.model_id}' 不存在")
            
        # 检查模型是否属于该提供商
        if model.provider_id != config_data.provider_id:
            raise ValueError(f"模型 '{model.name}' 不属于提供商 '{provider.name}'")
            
        # 检查租户是否存在
        if config_data.tenant_id:
            tenant_query = select(Tenant).where(Tenant.id == config_data.tenant_id)
            tenant_result = await db.execute(tenant_query)
            tenant = tenant_result.scalars().first()
            if not tenant:
                raise ValueError(f"租户 ID '{config_data.tenant_id}' 不存在")
                
        # 检查项目是否存在
        if config_data.project_id:
            project_query = select(Project).where(Project.id == config_data.project_id)
            project_result = await db.execute(project_query)
            project = project_result.scalars().first()
            if not project:
                raise ValueError(f"项目 ID '{config_data.project_id}' 不存在")
                
            # 检查项目是否属于该租户
            if config_data.tenant_id and project.tenant_id != config_data.tenant_id:
                raise ValueError(f"项目 '{project.name}' 不属于租户 ID '{config_data.tenant_id}'")
        
        # 创建配置数据
        config_dict = config_data.dict()
        
        # 加密 API 密钥
        if config_dict.get("api_key"):
            config_dict["api_key"] = encrypt_text(config_dict["api_key"])
            
        # 创建配置
        config = AIConfig(**config_dict)
        db.add(config)
        await db.commit()
        await db.refresh(config)
        
        # 重新查询配置以获取完整的关联数据
        config_query = select(AIConfig).options(
            joinedload(AIConfig.provider),
            joinedload(AIConfig.model)
        ).where(AIConfig.id == config.id)
        
        result = await db.execute(config_query)
        config_with_relations = result.scalars().first()
        
        # 解密 API 密钥用于返回
        if config_with_relations and config_with_relations.api_key:
            config_with_relations.api_key = decrypt_text(config_with_relations.api_key)
        
        logger.info(f"创建 AI 配置成功: {config.id}")
        return config_with_relations

    @staticmethod
    async def update_config(
        db: AsyncSession,
        config_id: uuid.UUID,
        config_data: AIConfigUpdate,
    ) -> Optional[AIConfig]:
        """
        更新 AI 配置
        """
        # 获取配置
        config = await AIConfigService.get_config(db, config_id)
        if not config:
            return None
            
        # 更新配置
        update_data = config_data.dict(exclude_unset=True)
        
        # 验证provider_id和model_id的匹配关系
        if "provider_id" in update_data or "model_id" in update_data:
            new_provider_id = update_data.get("provider_id", config.provider_id)
            new_model_id = update_data.get("model_id", config.model_id)
            
            # 检查提供商是否存在
            provider = await AIProviderService.get_provider(db, new_provider_id)
            if not provider:
                raise ValueError(f"提供商 ID '{new_provider_id}' 不存在")
                
            # 检查模型是否存在
            model = await AIModelService.get_model(db, new_model_id)
            if not model:
                raise ValueError(f"模型 ID '{new_model_id}' 不存在")
                
            # 检查模型是否属于该提供商
            if model.provider_id != new_provider_id:
                raise ValueError(f"模型 '{model.name}' 不属于提供商 '{provider.name}'")
        
        # 处理 API 密钥：如果为空或None，则不更新
        if "api_key" in update_data:
            if update_data["api_key"]:
                # 加密新的 API 密钥
                update_data["api_key"] = encrypt_text(update_data["api_key"])
            else:
                # 如果API密钥为空，则不更新此字段
                del update_data["api_key"]
            
        for key, value in update_data.items():
            setattr(config, key, value)
            
        await db.commit()
        
        # 重新查询配置以获取最新的关联数据（特别是provider和model）
        updated_config_query = select(AIConfig).options(
            joinedload(AIConfig.provider),
            joinedload(AIConfig.model)
        ).where(AIConfig.id == config_id)
        
        result = await db.execute(updated_config_query)
        updated_config = result.scalars().first()
        
        # 解密 API 密钥用于返回
        if updated_config and updated_config.api_key:
            updated_config.api_key = decrypt_text(updated_config.api_key)
        
        logger.info(f"更新 AI 配置成功: {config_id}")
        return updated_config

    @staticmethod
    async def delete_config(
        db: AsyncSession,
        config_id: uuid.UUID,
    ) -> bool:
        """
        删除 AI 配置
        """
        # 获取配置
        config = await AIConfigService.get_config(db, config_id)
        if not config:
            return False
            
        # 删除配置
        await db.delete(config)
        await db.commit()
        
        logger.info(f"删除 AI 配置成功: {config.id}")
        return True

    @staticmethod
    async def get_effective_config(
        db: AsyncSession,
        project_id: uuid.UUID,
        model_id: Optional[uuid.UUID] = None,
        model_type: Optional[str] = None,
    ) -> Optional[AIConfig]:
        """
        获取有效的 AI 配置
        
        优先级：项目级配置 > 租户级配置 > 系统级配置
        """
        # 获取项目信息
        project_query = select(Project).where(Project.id == project_id)
        project_result = await db.execute(project_query)
        project = project_result.scalars().first()
        
        if not project:
            logger.error(f"项目 ID '{project_id}' 不存在")
            return None
            
        tenant_id = project.tenant_id
        
        # 构建查询条件
        conditions = []
        
        # 状态必须为 active
        conditions.append(AIConfig.status == "active")
        
        # 如果指定了模型 ID，则查询该模型的配置
        if model_id:
            conditions.append(AIConfig.model_id == model_id)
        # 如果指定了模型类型，则查询该类型的模型配置
        elif model_type:
            model_query = select(AIModel.id).where(AIModel.model_type == model_type)
            model_result = await db.execute(model_query)
            model_ids = [model_id for model_id, in model_result]
            
            if not model_ids:
                logger.error(f"没有找到类型为 '{model_type}' 的模型")
                return None
                
            conditions.append(AIConfig.model_id.in_(model_ids))
            
        # 按优先级查询配置
        # 1. 项目级配置
        project_config_query = select(AIConfig).options(
            joinedload(AIConfig.provider),
            joinedload(AIConfig.model)
        ).where(
            AIConfig.project_id == project_id,
            *conditions
        ).order_by(AIConfig.priority.desc())
        
        project_config_result = await db.execute(project_config_query)
        project_config = project_config_result.scalars().first()
        
        if project_config:
            # 解密 API 密钥
            project_config.api_key = decrypt_text(project_config.api_key)
            return project_config
            
        # 2. 租户级配置
        tenant_config_query = select(AIConfig).options(
            joinedload(AIConfig.provider),
            joinedload(AIConfig.model)
        ).where(
            AIConfig.tenant_id == tenant_id,
            AIConfig.project_id.is_(None),
            *conditions
        ).order_by(AIConfig.priority.desc())
        
        tenant_config_result = await db.execute(tenant_config_query)
        tenant_config = tenant_config_result.scalars().first()
        
        if tenant_config:
            # 解密 API 密钥
            tenant_config.api_key = decrypt_text(tenant_config.api_key)
            return tenant_config
            
        # 3. 系统级配置
        system_config_query = select(AIConfig).options(
            joinedload(AIConfig.provider),
            joinedload(AIConfig.model)
        ).where(
            AIConfig.tenant_id.is_(None),
            AIConfig.project_id.is_(None),
            *conditions
        ).order_by(AIConfig.priority.desc())
        
        system_config_result = await db.execute(system_config_query)
        system_config = system_config_result.scalars().first()
        
        if system_config:
            # 解密 API 密钥
            system_config.api_key = decrypt_text(system_config.api_key)
            return system_config
            
        logger.error(f"没有找到有效的 AI 配置")
        return None

    @staticmethod
    def get_effective_capabilities(config: AIConfig) -> Dict[str, Any]:
        """
        获取配置的有效能力（应用能力覆盖后的结果）
        
        Args:
            config: AI配置对象
            
        Returns:
            有效的能力配置字典
        """
        if not config or not config.model:
            return {}
            
        # 获取模型的基础能力
        base_capabilities = config.model.capabilities or {}
        
        # 应用配置中的能力覆盖（如果有的话）
        if config.config and config.config.get("capabilities_override"):
            capabilities_override = config.config["capabilities_override"]
            # 合并基础能力和覆盖能力
            effective_capabilities = {**base_capabilities, **capabilities_override}
        else:
            effective_capabilities = base_capabilities
            
        return effective_capabilities
