import React, { useState } from 'react';
import { Tabs, Card, Space } from 'antd';
import { ShopOutlined, InboxOutlined, AppstoreOutlined, ShoppingOutlined } from '@ant-design/icons';
import StoreManagement from './StoreManagement';
import WarehouseManagement from './WarehouseManagement';
import SalesManagement from './SalesManagement';

const { TabPane } = Tabs;

/**
 * 基础设置组件
 * 包含门店管理和仓储管理两个子模块
 */
const BasicSettings = () => {
  const [activeTab, setActiveTab] = useState('store');

  return (
    <div className="basic-settings">
      <Card
        title={
          <Space>
            <AppstoreOutlined />
            <span>基础设置</span>
          </Space>
        }
        className="settings-card"
      >
        <Tabs activeKey={activeTab} onChange={setActiveTab}>
          <TabPane
            tab={
              <span>
                <ShopOutlined />
                门店管理
              </span>
            }
            key="store"
          >
            <StoreManagement />
          </TabPane>

          <TabPane
            tab={
              <span>
                <InboxOutlined />
                仓储管理
              </span>
            }
            key="warehouse"
          >
            <WarehouseManagement />
          </TabPane>

          <TabPane
            tab={
              <span>
                <ShoppingOutlined />
                销售管理
              </span>
            }
            key="sales"
          >
            <SalesManagement />
          </TabPane>
        </Tabs>
      </Card>
    </div>
  );
};

export default BasicSettings;
