#!/bin/bash

# 后端服务监控脚本
CONTAINER_NAME="retail-ai-saas-backend-dev"
HEALTH_URL="http://localhost:8001/api/v1/health"
LOG_FILE="/tmp/backend_monitor.log"

# 日志函数
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# 检查容器状态
check_container() {
    if ! docker ps | grep -q "$CONTAINER_NAME"; then
        return 1
    fi
    return 0
}

# 检查健康状态
check_health() {
    local response=$(curl -s -w "%{http_code}" -o /dev/null "$HEALTH_URL" 2>/dev/null)
    if [ "$response" = "200" ]; then
        return 0
    else
        return 1
    fi
}

# 显示状态
show_status() {
    echo "=== 后端服务状态 ==="
    echo "容器名称: $CONTAINER_NAME"
    echo "健康检查URL: $HEALTH_URL"
    echo ""
    
    if check_container; then
        echo "容器状态: ✓ 运行中"
    else
        echo "容器状态: ✗ 未运行"
    fi
    
    if check_health; then
        echo "健康检查: ✓ 正常"
    else
        echo "健康检查: ✗ 异常"
    fi
    
    echo ""
    echo "最近的错误日志:"
    docker logs "$CONTAINER_NAME" --tail=10 2>/dev/null | grep -E "(ERROR|CRITICAL)" | tail -3
}

# 重启容器
restart_container() {
    log "正在重启容器 $CONTAINER_NAME..."
    docker restart "$CONTAINER_NAME"
    sleep 15
    log "容器重启完成"
}

# 主程序
case "$1" in
    status)
        show_status
        ;;
    restart)
        restart_container
        ;;
    *)
        echo "用法: $0 [status|restart]"
        exit 1
        ;;
esac 