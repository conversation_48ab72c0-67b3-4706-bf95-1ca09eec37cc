"""
初始化仓库数据
"""
from uuid import UUID
from sqlalchemy.orm import Session
from models.warehouse import Warehouse, WarehouseSetting

def init_warehouse_settings(db: Session, project_id: UUID):
    """初始化仓库设置"""
    # 检查是否已存在仓库设置
    setting = db.query(WarehouseSetting).filter(
        WarehouseSetting.project_id == project_id
    ).first()
    
    if not setting:
        setting = WarehouseSetting(
            project_id=project_id,
            warehouse_mode="multi",
            default_business_hours_start="09:00",
            default_business_hours_end="18:00",
            enable_front_warehouse=True,
            enable_cross_warehouse_transfer=True,
            inventory_warning_threshold=10,
            auto_replenishment=True,
            default_capacity_unit="sqm"
        )
        db.add(setting)
        db.commit()
        db.refresh(setting)
    
    return setting

def init_warehouses(db: Session, project_id: UUID):
    """初始化仓库数据"""
    # 确保仓库设置已初始化
    init_warehouse_settings(db, project_id)
    
    warehouses = []
    
    # 检查仓库是否已存在，不存在则创建
    created_warehouses = []
    for warehouse_data in warehouses:
        warehouse = db.query(Warehouse).filter(
            Warehouse.project_id == project_id,
            Warehouse.code == warehouse_data["code"]
        ).first()
        
        if not warehouse:
            warehouse = Warehouse(project_id=project_id, **warehouse_data)
            db.add(warehouse)
            created_warehouses.append(warehouse)
    
    db.commit()
    return created_warehouses
