/**
 * 用户管理页面
 * 系统管理员用于管理所有用户
 */

import React, { useState, useEffect } from 'react';
import { 
  Table, 
  Button, 
  Input, 
  Space, 
  Modal, 
  Form, 
  Select, 
  Switch, 
  message, 
  Popconfirm, 
  Tag, 
  Card, 
  Typography 
} from 'antd';
import { 
  PlusOutlined, 
  SearchOutlined, 
  EditOutlined, 
  DeleteOutlined, 
  UserOutlined, 
  LockOutlined 
} from '@ant-design/icons';
import apiService from '../../services/api';

const { Title } = Typography;
const { Option } = Select;

const UserManagement = () => {
  const [users, setUsers] = useState([]);
  const [tenants, setTenants] = useState([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [modalTitle, setModalTitle] = useState('添加用户');
  const [editingUser, setEditingUser] = useState(null);
  const [searchText, setSearchText] = useState('');
  const [form] = Form.useForm();

  // 加载用户数据
  const fetchUsers = async () => {
    try {
      setLoading(true);
      const data = await apiService.getUsers();
      setUsers(data);
    } catch (error) {
      message.error('获取用户列表失败');
      console.error('获取用户列表失败:', error);
    } finally {
      setLoading(false);
    }
  };

  // 加载租户数据
  const fetchTenants = async () => {
    try {
      const data = await apiService.getTenants();
      setTenants(data);
    } catch (error) {
      message.error('获取租户列表失败');
      console.error('获取租户列表失败:', error);
    }
  };

  // 初始化数据
  useEffect(() => {
    fetchUsers();
    fetchTenants();
  }, []);

  // 打开添加用户模态框
  const showAddModal = () => {
    setModalTitle('添加用户');
    setEditingUser(null);
    form.resetFields();
    setModalVisible(true);
  };

  // 打开编辑用户模态框
  const showEditModal = (user) => {
    setModalTitle('编辑用户');
    setEditingUser(user);
    form.setFieldsValue({
      username: user.username,
      email: user.email,
      name: user.name,
      tenant_id: user.tenant_id,
      is_active: user.is_active,
      is_system_admin: user.is_system_admin,
      is_service_provider_admin: user.is_service_provider_admin,
      is_tenant_admin: user.is_tenant_admin,
    });
    setModalVisible(true);
  };

  // 关闭模态框
  const handleCancel = () => {
    setModalVisible(false);
  };

  // 提交表单
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      
      if (editingUser) {
        // 更新用户
        await apiService.updateUser(editingUser.id, values);
        message.success('用户更新成功');
      } else {
        // 创建用户
        await apiService.createUser(values);
        message.success('用户创建成功');
      }
      
      setModalVisible(false);
      fetchUsers();
    } catch (error) {
      console.error('提交表单失败:', error);
      message.error('操作失败: ' + (error.response?.data?.message || error.message));
    }
  };

  // 删除用户
  const handleDelete = async (userId) => {
    try {
      await apiService.deleteUser(userId);
      message.success('用户删除成功');
      fetchUsers();
    } catch (error) {
      console.error('删除用户失败:', error);
      message.error('删除失败: ' + (error.response?.data?.message || error.message));
    }
  };

  // 重置密码
  const handleResetPassword = async (userId) => {
    try {
      await apiService.resetUserPassword(userId);
      message.success('密码重置成功，新密码已发送至用户邮箱');
    } catch (error) {
      console.error('重置密码失败:', error);
      message.error('重置密码失败: ' + (error.response?.data?.message || error.message));
    }
  };

  // 过滤用户数据
  const filteredUsers = users.filter(user => 
    user.username.toLowerCase().includes(searchText.toLowerCase()) ||
    user.email.toLowerCase().includes(searchText.toLowerCase()) ||
    user.name.toLowerCase().includes(searchText.toLowerCase())
  );

  // 表格列定义
  const columns = [
    {
      title: '用户名',
      dataIndex: 'username',
      key: 'username',
      sorter: (a, b) => a.username.localeCompare(b.username),
    },
    {
      title: '姓名',
      dataIndex: 'name',
      key: 'name',
      sorter: (a, b) => a.name.localeCompare(b.name),
    },
    {
      title: '邮箱',
      dataIndex: 'email',
      key: 'email',
    },
    {
      title: '租户',
      dataIndex: 'tenant_name',
      key: 'tenant_name',
      render: (_, record) => {
        const tenant = tenants.find(t => t.id === record.tenant_id);
        return tenant ? tenant.name : '无';
      },
    },
    {
      title: '角色',
      key: 'roles',
      render: (_, record) => (
        <Space>
          {record.is_system_admin && <Tag color="red">系统管理员</Tag>}
          {record.is_service_provider_admin && <Tag color="orange">服务商管理员</Tag>}
          {record.is_tenant_admin && <Tag color="green">租户管理员</Tag>}
          {!record.is_system_admin && !record.is_service_provider_admin && !record.is_tenant_admin && 
            <Tag color="blue">普通用户</Tag>}
        </Space>
      ),
    },
    {
      title: '状态',
      dataIndex: 'is_active',
      key: 'is_active',
      render: (isActive) => (
        <Tag color={isActive ? 'green' : 'red'}>
          {isActive ? '启用' : '禁用'}
        </Tag>
      ),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="middle">
          <Button 
            icon={<EditOutlined />} 
            onClick={() => showEditModal(record)}
            size="small"
          />
          <Button 
            icon={<LockOutlined />} 
            onClick={() => handleResetPassword(record.id)}
            size="small"
            title="重置密码"
          />
          <Popconfirm
            title="确定要删除此用户吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button 
              icon={<DeleteOutlined />} 
              danger 
              size="small"
            />
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <Card>
        <Title level={2}>用户管理</Title>
        
        <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between' }}>
          <Input
            placeholder="搜索用户"
            prefix={<SearchOutlined />}
            style={{ width: 300 }}
            value={searchText}
            onChange={e => setSearchText(e.target.value)}
          />
          <Button 
            type="primary" 
            icon={<PlusOutlined />}
            onClick={showAddModal}
          >
            添加用户
          </Button>
        </div>
        
        <Table
          columns={columns}
          dataSource={filteredUsers}
          rowKey="id"
          loading={loading}
          pagination={{ pageSize: 10 }}
        />
      </Card>
      
      <Modal
        title={modalTitle}
        open={modalVisible}
        onOk={handleSubmit}
        onCancel={handleCancel}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Form.Item
            name="username"
            label="用户名"
            rules={[{ required: true, message: '请输入用户名' }]}
          >
            <Input prefix={<UserOutlined />} placeholder="用户名" />
          </Form.Item>
          
          {!editingUser && (
            <Form.Item
              name="password"
              label="密码"
              rules={[{ required: true, message: '请输入密码' }]}
            >
              <Input.Password prefix={<LockOutlined />} placeholder="密码" />
            </Form.Item>
          )}
          
          <Form.Item
            name="email"
            label="邮箱"
            rules={[
              { required: true, message: '请输入邮箱' },
              { type: 'email', message: '请输入有效的邮箱地址' }
            ]}
          >
            <Input placeholder="邮箱" />
          </Form.Item>
          
          <Form.Item
            name="name"
            label="姓名"
            rules={[{ required: true, message: '请输入姓名' }]}
          >
            <Input placeholder="姓名" />
          </Form.Item>
          
          <Form.Item
            name="tenant_id"
            label="所属租户"
          >
            <Select placeholder="选择租户">
              <Option value={null}>无</Option>
              {tenants.map(tenant => (
                <Option key={tenant.id} value={tenant.id}>{tenant.name}</Option>
              ))}
            </Select>
          </Form.Item>
          
          <Form.Item
            name="is_active"
            label="启用状态"
            valuePropName="checked"
            initialValue={true}
          >
            <Switch checkedChildren="启用" unCheckedChildren="禁用" />
          </Form.Item>
          
          <Form.Item
            name="is_system_admin"
            label="系统管理员"
            valuePropName="checked"
            initialValue={false}
          >
            <Switch />
          </Form.Item>
          
          <Form.Item
            name="is_service_provider_admin"
            label="服务商管理员"
            valuePropName="checked"
            initialValue={false}
          >
            <Switch />
          </Form.Item>
          
          <Form.Item
            name="is_tenant_admin"
            label="租户管理员"
            valuePropName="checked"
            initialValue={false}
          >
            <Switch />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default UserManagement;
