import React, { useState } from 'react';
import { Card, Tabs, Typo<PERSON>, <PERSON>ert, Button, Space } from 'antd';
import {
  ApiOutlined,
  SettingOutlined,
  KeyOutlined,
  CheckOutlined,
  ExperimentOutlined,
  ClusterOutlined,
  LinkOutlined,
  ToolOutlined
} from '@ant-design/icons';
import { useAuth } from '../../../../contexts/AuthContext';

// 导入组件
import TokenApply from './components/TokenApply';
import ClientConfig from './components/ClientConfig';
import MCPTest from './components/MCPTest';
import ServerManage from './components/ServerManage';
import ApiEndpointToolMappingManage from './components/ApiEndpointToolMappingManage';
import TokenManage from './components/TokenManage';

const { Title, Paragraph } = Typography;
const { TabPane } = Tabs;

const SAPIPlugin = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('1');
  // 修改判断逻辑，适应当前的用户信息结构
  const isAdmin = user && (
    user.role === 'admin' ||
    user.is_system_admin ||
    user.is_super_admin ||
    user.is_tenant_admin ||
    user.is_service_provider_admin ||
    user.is_project_admin
  );

  return (
    <Card>
      <div style={{ marginBottom: 24 }}>
        <Title level={2}>智能助手集成平台 (SAPI)</Title>
        <Paragraph>
          智能助手集成平台(SAPI)是一个强大的API集成工具，支持Model Context Protocol(MCP)协议，允许您将系统功能与各类应用程序和服务无缝连接。
          通过SAPI，您可以将大模型能力集成到开发工具、创建自定义应用程序、聊天机器人和自动化工作流，显著提高开发效率和用户体验。
        </Paragraph>

        <Alert
          message="使用指南"
          description={
            <div>
              <p>1. 申请API令牌以获取访问权限</p>
              <p>2. 使用MCP客户端配置连接到API</p>
              <p>3. 测试API调用效果和响应结果</p>
              <p>4. 管理API令牌和审批申请（管理员）</p>
              <p>5. 管理API端点和工具映射（管理员）</p>
              <p>6. 配置和管理MCP服务器（管理员）</p>
              <p>7. 开始构建您的应用程序或集成</p>
            </div>
          }
          type="info"
          showIcon
          style={{ marginBottom: 16 }}
        />
      </div>

      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane
          tab={<span><ApiOutlined /> 概览</span>}
          key="1"
        >
          <div style={{ display: 'flex', flexDirection: 'column', gap: 24 }}>
            <Space direction="vertical" size="large">
              <div>
                <Title level={4}>1. 申请API令牌</Title>
                <Paragraph>
                  申请API令牌以获取访问权限，令牌用于认证和授权API请求。
                </Paragraph>
                <Button type="primary" onClick={() => setActiveTab('2')}>
                  <KeyOutlined /> 申请API令牌
                </Button>
              </div>

              <div>
                <Title level={4}>2. MCP客户端配置</Title>
                <Paragraph>
                  配置MCP客户端连接到API，支持各种MCP客户端，如Claude、Cursor等。
                </Paragraph>
                <Button type="primary" onClick={() => setActiveTab('3')}>
                  <SettingOutlined /> MCP客户端配置
                </Button>
              </div>

              <div>
                <Title level={4}>3. MCP测试</Title>
                <Paragraph>
                  直接测试MCP服务器，按照MCP协议标准流程进行测试，验证服务器功能和工具调用。
                </Paragraph>
                <Button type="primary" onClick={() => setActiveTab('7')}>
                  <ExperimentOutlined /> MCP测试
                </Button>
              </div>

              {isAdmin && (
                <div>
                  <Title level={4}>4. API令牌管理</Title>
                  <Paragraph>
                    管理API令牌和审批令牌申请，控制API访问权限。
                  </Paragraph>
                  <Button type="primary" onClick={() => setActiveTab('4')}>
                    <KeyOutlined /> API令牌管理
                  </Button>
                </div>
              )}

              {isAdmin && (
                <div>
                  <Title level={4}>5. API端点和工具映射管理</Title>
                  <Paragraph>
                    管理API端点和工具映射，包括端点定义、参数配置、访问限制，以及将API端点映射为MCP工具。
                  </Paragraph>
                  <Button type="primary" onClick={() => setActiveTab('5')}>
                    <ToolOutlined /> API端点和工具映射管理
                  </Button>
                </div>
              )}

              {isAdmin && (
                <div>
                  <Title level={4}>7. MCP服务端管理</Title>
                  <Paragraph>
                    部署和管理MCP协议服务器，实现与各类大模型和应用的无缝连接。
                  </Paragraph>
                  <Button type="primary" onClick={() => setActiveTab('8')}>
                    <ClusterOutlined /> MCP服务端管理
                  </Button>
                </div>
              )}
            </Space>
          </div>
        </TabPane>

        <TabPane
          tab={<span><KeyOutlined /> 申请API令牌</span>}
          key="2"
        >
          <TokenApply />
        </TabPane>

        <TabPane
          tab={<span><SettingOutlined /> MCP客户端配置</span>}
          key="3"
        >
          <ClientConfig />
        </TabPane>

        {isAdmin && (
          <TabPane
            tab={<span><KeyOutlined /> API令牌管理</span>}
            key="4"
          >
            <TokenManage />
          </TabPane>
        )}

        {isAdmin && (
          <TabPane
            tab={<span><ToolOutlined /> API端点和工具映射管理</span>}
            key="5"
          >
            <ApiEndpointToolMappingManage />
          </TabPane>
        )}

        <TabPane
          tab={<span><ExperimentOutlined /> MCP测试</span>}
          key="7"
        >
          <MCPTest />
        </TabPane>

        {isAdmin && (
          <TabPane
            tab={<span><ClusterOutlined /> MCP服务端管理</span>}
            key="8"
          >
            <ServerManage />
          </TabPane>
        )}
      </Tabs>
    </Card>
  );
};

export default SAPIPlugin;
