import pytest
from typing import Generator, Any
from httpx import Client
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session
import uuid
from sqlalchemy_utils import database_exists, create_database, drop_database

from main import app
from core.config import settings
from db.database import get_db, Base
from core.auth import create_access_token
from models.user import User
from models.tenant import Tenant
from models.project import Project

# 使用同步引擎创建测试数据库
TEST_DATABASE_URL = str(settings.DATABASE_URL).replace("postgresql+asyncpg", "postgresql") + "_test"

# 确保测试数据库存在
engine = create_engine(str(settings.DATABASE_URL).replace("postgresql+asyncpg", "postgresql"))
if not database_exists(TEST_DATABASE_URL):
    create_database(TEST_DATABASE_URL)

test_engine = create_engine(TEST_DATABASE_URL, echo=True)

# 创建同步测试会话
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=test_engine)

@pytest.fixture(scope="session", autouse=True)
def setup_database():
    """Set up the test database, creating all tables."""
    Base.metadata.create_all(bind=test_engine)
    yield
    Base.metadata.drop_all(bind=test_engine)

@pytest.fixture
def db() -> Generator[Session, Any, None]:
    """Fixture to provide a database session for each test."""
    connection = test_engine.connect()
    transaction = connection.begin()
    session = TestingSessionLocal(bind=connection)
    yield session
    session.close()
    transaction.rollback()
    connection.close()


@pytest.fixture
def client(db: Session) -> Generator[Client, Any, None]:
    """Fixture for a sync test client."""
    def override_get_db():
        yield db

    app.dependency_overrides[get_db] = override_get_db
    with Client(app=app, base_url="http://test") as c:
        yield c
    del app.dependency_overrides[get_db]


@pytest.fixture
def current_tenant(db: Session) -> Tenant:
    """Fixture to create a test tenant."""
    tenant = Tenant(id=uuid.uuid4(), name="Test Tenant")
    db.add(tenant)
    db.commit()
    db.refresh(tenant)
    return tenant

@pytest.fixture
def current_project(db: Session, current_tenant: Tenant) -> Project:
    """Fixture to create a test project."""
    project = Project(id=uuid.uuid4(), name="Test Project", tenant_id=current_tenant.id, industry="retail")
    db.add(project)
    db.commit()
    db.refresh(project)
    return project

@pytest.fixture
def current_user(db: Session, current_tenant: Tenant) -> User:
    """Fixture to create a test user."""
    user = User(
        id=uuid.uuid4(),
        username="testuser",
        email="<EMAIL>",
        hashed_password="fake_password",
        tenant_id=current_tenant.id,
    )
    db.add(user)
    db.commit()
    db.refresh(user)
    return user

@pytest.fixture
def get_auth_headers(current_user: User) -> dict:
    """Fixture to get authentication headers for a test user."""
    token = create_access_token(
        data={"user_id": str(current_user.id), "role": "user"}
    )
    return {"Authorization": f"Bearer {token}"} 