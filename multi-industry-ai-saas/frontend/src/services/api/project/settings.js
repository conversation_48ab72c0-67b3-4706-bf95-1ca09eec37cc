import request from '../../request';
import { getProjectId } from '../httpClient';

/**
 * 项目-设置API
 */
const settings = {
  /**
   * 获取项目设置
   * @returns {Promise} - 项目设置
   */
  getSettings: async () => {
    try {
      const projectId = getProjectId();
      if (!projectId) {
        console.warn('未指定项目ID，返回默认设置');
        // 返回默认设置
        return {
          theme: {
            mode: 'light',
            primaryColor: '#1890ff',
            borderRadius: 4,
            compactMode: false,
            customFont: false,
            fontFamily: 'default'
          },
          notification: {
            emailNotification: true,
            smsNotification: false,
            pushNotification: true,
            notificationFrequency: 'immediate',
            dailyDigest: true,
            soundEnabled: true
          },
          security: {
            passwordExpiration: 90,
            twoFactorAuth: false,
            loginAttempts: 5,
            sessionTimeout: 30,
            ipRestriction: false,
            allowedIps: ''
          },
          general: {
            language: 'zh_CN',
            timezone: 'Asia/Shanghai',
            dateFormat: 'YYYY-MM-DD',
            timeFormat: '24hour'
          },
          third_party_login: {
            wechat_work: {
              enabled: false,
              corp_id: '',
              agent_id: '',
              secret: '',
              redirect_uri: ''
            },
            dingtalk: {
              enabled: false,
              app_key: '',
              app_secret: '',
              redirect_uri: ''
            },
            feishu: {
              enabled: false,
              app_id: '',
              app_secret: '',
              redirect_uri: ''
            },
            wechat: {
              enabled: false,
              app_id: '',
              app_secret: '',
              redirect_uri: ''
            }
          }
        };
      }
      return await request.get(`/project/${projectId}/settings`);
    } catch (error) {
      console.error('获取项目设置失败:', error);
      // 返回默认设置
      return {
        theme: {
          mode: 'light',
          primaryColor: '#1890ff',
          borderRadius: 4,
          compactMode: false,
          customFont: false,
          fontFamily: 'default'
        },
        notification: {
          emailNotification: true,
          smsNotification: false,
          pushNotification: true,
          notificationFrequency: 'immediate',
          dailyDigest: true,
          soundEnabled: true
        },
        security: {
          passwordExpiration: 90,
          twoFactorAuth: false,
          loginAttempts: 5,
          sessionTimeout: 30,
          ipRestriction: false,
          allowedIps: ''
        },
        general: {
          language: 'zh_CN',
          timezone: 'Asia/Shanghai',
          dateFormat: 'YYYY-MM-DD',
          timeFormat: '24hour'
        },
        third_party_login: {
          wechat_work: {
            enabled: false,
            corp_id: '',
            agent_id: '',
            secret: '',
            redirect_uri: ''
          },
          dingtalk: {
            enabled: false,
            app_key: '',
            app_secret: '',
            redirect_uri: ''
          },
          feishu: {
            enabled: false,
            app_id: '',
            app_secret: '',
            redirect_uri: ''
          },
          wechat: {
            enabled: false,
            app_id: '',
            app_secret: '',
            redirect_uri: ''
          }
        }
      };
    }
  },

  /**
   * 更新项目设置
   * @param {Object} data - 设置数据
   * @returns {Promise} - 更新结果
   */
  updateSettings: async (data) => {
    try {
      const projectId = getProjectId();
      if (!projectId) {
        throw new Error('未指定项目ID');
      }
      return await request.put(`/project/${projectId}/settings`, data);
    } catch (error) {
      console.error('更新项目设置失败:', error);
      throw error;
    }
  },

  /**
   * 获取主题设置
   * @returns {Promise} - 主题设置
   */
  getTheme: async () => {
    try {
      // 获取项目设置
      const response = await settings.getSettings();
      // 从项目设置中获取主题设置
      const themeSettings = response?.theme || {};
      return {
        mode: themeSettings.mode || 'light',
        primaryColor: themeSettings.primaryColor || '#1890ff',
        borderRadius: themeSettings.borderRadius || 4,
        compactMode: themeSettings.compactMode || false,
        customFont: themeSettings.customFont || false,
        fontFamily: themeSettings.fontFamily || 'default'
      };
    } catch (error) {
      console.error('获取主题设置失败:', error);
      // 返回默认主题设置
      return {
        mode: 'light',
        primaryColor: '#1890ff',
        borderRadius: 4,
        compactMode: false,
        customFont: false,
        fontFamily: 'default'
      };
    }
  },

  /**
   * 更新主题设置
   * @param {Object} data - 主题设置数据
   * @returns {Promise} - 更新结果
   */
  updateTheme: async (data) => {
    try {
      // 先获取当前项目设置
      const currentSettings = await settings.getSettings();
      const updatedSettings = {
        ...currentSettings,
        theme: data
      };
      // 更新项目设置
      return await settings.updateSettings(updatedSettings);
    } catch (error) {
      console.error('更新主题设置失败:', error);
      throw error;
    }
  },

  /**
   * 获取通知设置
   * @returns {Promise} - 通知设置
   */
  getNotification: async () => {
    try {
      // 获取项目设置
      const response = await settings.getSettings();
      // 从项目设置中获取通知设置
      const notificationSettings = response?.notification || {};
      return {
        emailNotification: notificationSettings.emailNotification || true,
        smsNotification: notificationSettings.smsNotification || false,
        pushNotification: notificationSettings.pushNotification || true,
        notificationFrequency: notificationSettings.notificationFrequency || 'immediate',
        dailyDigest: notificationSettings.dailyDigest || true,
        soundEnabled: notificationSettings.soundEnabled || true
      };
    } catch (error) {
      console.error('获取通知设置失败:', error);
      // 返回默认通知设置
      return {
        emailNotification: true,
        smsNotification: false,
        pushNotification: true,
        notificationFrequency: 'immediate',
        dailyDigest: true,
        soundEnabled: true
      };
    }
  },

  /**
   * 更新通知设置
   * @param {Object} data - 通知设置数据
   * @returns {Promise} - 更新结果
   */
  updateNotification: async (data) => {
    try {
      // 先获取当前项目设置
      const currentSettings = await settings.getSettings();
      const updatedSettings = {
        ...currentSettings,
        notification: data
      };
      // 更新项目设置
      return await settings.updateSettings(updatedSettings);
    } catch (error) {
      console.error('更新通知设置失败:', error);
      throw error;
    }
  },

  /**
   * 获取安全设置
   * @returns {Promise} - 安全设置
   */
  getSecurity: async () => {
    try {
      // 获取项目设置
      const response = await settings.getSettings();
      // 从项目设置中获取安全设置
      const securitySettings = response?.security || {};
      return {
        passwordExpiration: securitySettings.passwordExpiration || 90,
        twoFactorAuth: securitySettings.twoFactorAuth || false,
        loginAttempts: securitySettings.loginAttempts || 5,
        sessionTimeout: securitySettings.sessionTimeout || 30,
        ipRestriction: securitySettings.ipRestriction || false,
        allowedIps: securitySettings.allowedIps || ''
      };
    } catch (error) {
      console.error('获取安全设置失败:', error);
      // 返回默认安全设置
      return {
        passwordExpiration: 90,
        twoFactorAuth: false,
        loginAttempts: 5,
        sessionTimeout: 30,
        ipRestriction: false,
        allowedIps: ''
      };
    }
  },

  /**
   * 更新安全设置
   * @param {Object} data - 安全设置数据
   * @returns {Promise} - 更新结果
   */
  updateSecurity: async (data) => {
    try {
      // 先获取当前项目设置
      const currentSettings = await settings.getSettings();

      // 创建一个新的安全设置对象，确保所有字段都有值
      const securitySettings = {
        passwordExpiration: data.passwordExpiration !== undefined ? data.passwordExpiration : 90,
        twoFactorAuth: data.twoFactorAuth !== undefined ? data.twoFactorAuth : false,
        loginAttempts: data.loginAttempts !== undefined ? data.loginAttempts : 5,
        sessionTimeout: data.sessionTimeout !== undefined ? data.sessionTimeout : 30,
        ipRestriction: data.ipRestriction !== undefined ? data.ipRestriction : false,
        allowedIps: data.allowedIps !== undefined ? data.allowedIps : ''
      };

      const updatedSettings = {
        ...currentSettings,
        security: securitySettings
      };

      // 更新项目设置
      return await settings.updateSettings(updatedSettings);
    } catch (error) {
      console.error('更新安全设置失败:', error);
      throw error;
    }
  },

  /**
   * 获取常规设置
   * @returns {Promise} - 常规设置
   */
  getGeneral: async () => {
    try {
      // 获取项目设置
      const response = await settings.getSettings();
      // 从项目设置中获取常规设置
      const generalSettings = response?.general || {};
      return {
        language: generalSettings.language || 'zh_CN',
        timezone: generalSettings.timezone || 'Asia/Shanghai',
        dateFormat: generalSettings.dateFormat || 'YYYY-MM-DD',
        timeFormat: generalSettings.timeFormat || '24hour'
      };
    } catch (error) {
      console.error('获取常规设置失败:', error);
      // 返回默认常规设置
      return {
        language: 'zh_CN',
        timezone: 'Asia/Shanghai',
        dateFormat: 'YYYY-MM-DD',
        timeFormat: '24hour'
      };
    }
  },

  /**
   * 更新常规设置
   * @param {Object} data - 常规设置数据
   * @returns {Promise} - 更新结果
   */
  updateGeneral: async (data) => {
    try {
      // 先获取当前项目设置
      const currentSettings = await settings.getSettings();
      const updatedSettings = {
        ...currentSettings,
        general: data
      };
      // 更新项目设置
      return await settings.updateSettings(updatedSettings);
    } catch (error) {
      console.error('更新常规设置失败:', error);
      throw error;
    }
  },

  /**
   * 获取第三方登录设置
   * @returns {Promise} - 第三方登录设置
   */
  getThirdPartyLogin: async () => {
    try {
      // 获取项目设置
      const response = await settings.getSettings();
      // 从项目设置中获取第三方登录设置
      const thirdPartyLoginSettings = response?.third_party_login || {};
      return {
        wechat_work: thirdPartyLoginSettings.wechat_work || {
          enabled: false,
          corp_id: '',
          agent_id: '',
          secret: '',
          redirect_uri: ''
        },
        dingtalk: thirdPartyLoginSettings.dingtalk || {
          enabled: false,
          app_key: '',
          app_secret: '',
          redirect_uri: ''
        },
        feishu: thirdPartyLoginSettings.feishu || {
          enabled: false,
          app_id: '',
          app_secret: '',
          redirect_uri: ''
        },
        wechat: thirdPartyLoginSettings.wechat || {
          enabled: false,
          app_id: '',
          app_secret: '',
          redirect_uri: ''
        }
      };
    } catch (error) {
      console.error('获取第三方登录设置失败:', error);
      // 返回默认第三方登录设置
      return {
        wechat_work: {
          enabled: false,
          corp_id: '',
          agent_id: '',
          secret: '',
          redirect_uri: ''
        },
        dingtalk: {
          enabled: false,
          app_key: '',
          app_secret: '',
          redirect_uri: ''
        },
        feishu: {
          enabled: false,
          app_id: '',
          app_secret: '',
          redirect_uri: ''
        },
        wechat: {
          enabled: false,
          app_id: '',
          app_secret: '',
          redirect_uri: ''
        }
      };
    }
  },

  /**
   * 更新第三方登录设置
   * @param {Object} data - 第三方登录设置数据
   * @returns {Promise} - 更新结果
   */
  updateThirdPartyLogin: async (data) => {
    try {
      // 先获取当前项目设置
      const currentSettings = await settings.getSettings();

      // 获取当前的第三方登录设置
      const currentThirdPartyLogin = currentSettings.third_party_login || {};

      // 处理禁用的第三方登录
      Object.keys(data).forEach(key => {
        // 如果是禁用某个第三方登录，清空其相关配置
        if (key.endsWith('_enabled') && data[key] === false) {
          const platform = key.replace('_enabled', '');
          // 清空该平台的所有配置，但保留enabled状态
          Object.keys(currentThirdPartyLogin).forEach(configKey => {
            if (configKey.startsWith(platform + '_') && configKey !== key) {
              data[configKey] = '';
            }
          });
        }
      });

      // 合并设置，确保只更新提供的部分
      const updatedThirdPartyLogin = {
        ...currentThirdPartyLogin,
        ...data
      };

      const updatedSettings = {
        ...currentSettings,
        third_party_login: updatedThirdPartyLogin
      };

      // 更新项目设置
      return await settings.updateSettings(updatedSettings);
    } catch (error) {
      console.error('更新第三方登录设置失败:', error);
      throw error;
    }
  },

  /**
   * 获取AI设置
   * @returns {Promise} - AI设置
   */
  getAI: async () => {
    try {
      const projectId = getProjectId();
      if (!projectId) {
        // 返回默认AI设置
        return {
          enabled: true,
          cache_duration: '1hour',
          insights_enabled: true,
          suggestions_enabled: true,
          auto_analysis: false,
          analysis_frequency: 'daily',
          max_requests_per_hour: 100,
          enable_data_learning: false,
          privacy_mode: true
        };
      }
      return await request.get(`/project/${projectId}/settings/ai`);
    } catch (error) {
      console.error('获取AI设置失败:', error);
      // 返回默认AI设置
      return {
        enabled: true,
        cache_duration: '1hour',
        insights_enabled: true,
        suggestions_enabled: true,
        auto_analysis: false,
        analysis_frequency: 'daily',
        max_requests_per_hour: 100,
        enable_data_learning: false,
        privacy_mode: true
      };
    }
  },

  /**
   * 更新AI设置
   * @param {Object} data - AI设置数据
   * @returns {Promise} - 更新结果
   */
  updateAI: async (data) => {
    try {
      const projectId = getProjectId();
      if (!projectId) {
        throw new Error('未指定项目ID');
      }
      return await request.post(`/project/${projectId}/settings/ai`, data);
    } catch (error) {
      console.error('更新AI设置失败:', error);
      throw error;
    }
  },

  /**
   * 获取API接口设置
   * @returns {Promise} - API接口设置
   */
  getApiSettings: async () => {
    try {
      const projectId = getProjectId();
      if (!projectId) {
        return { weather: {}, gaode: {} };
      }
      // 直接调用标准API，它返回的数据 { weather: ..., gaode: ... } 直接匹配表单结构
      return await request.get(`/project/${projectId}/settings/api`);
    } catch (error) {
      console.error('获取API接口设置失败:', error);
      return { weather: {}, gaode: {} };
    }
  },

  /**
   * 更新API接口设置
   * @param {Object} data - API接口设置数据，其结构为 { weather: ..., gaode: ... }
   * @returns {Promise} - 更新结果
   */
  updateApiSettings: async (data) => {
    try {
      const projectId = getProjectId();
      if (!projectId) {
        throw new Error('未指定项目ID');
      }
      // 直接将表单数据发送到标准API
      return await request.post(`/project/${projectId}/settings/api`, data);
    } catch (error) {
      console.error('更新API接口设置失败:', error);
      throw error;
    }
  },
};

export default settings;
