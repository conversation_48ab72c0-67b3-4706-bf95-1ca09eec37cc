#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
钉钉审批业务集成服务
处理审批通过后与业务系统的数据同步
"""

import logging
from typing import Dict, Any, Optional
from datetime import datetime
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_

logger = logging.getLogger(__name__)

async def integrate_store_loss(
    db: AsyncSession, 
    project_id: str, 
    instance: 'DingTalkApprovalInstance', 
    action: str = "sync"
) -> Dict[str, Any]:
    """
    集成门店报损审批
    
    Args:
        db: 数据库会话
        project_id: 项目ID
        instance: 审批实例
        action: 操作类型 (sync/rollback)
        
    Returns:
        Dict[str, Any]: 集成结果
    """
    try:
        # 解析表单数据
        form_data = {}
        if instance.form_component_values:
            for field in instance.form_component_values:
                form_data[field.get("name")] = field.get("value")
        
        store_name = form_data.get("store_name")
        product_name = form_data.get("product_name")
        product_code = form_data.get("product_code")
        quantity = float(form_data.get("quantity", 0))
        unit_price = float(form_data.get("unit_price", 0))
        loss_reason = form_data.get("loss_reason")
        description = form_data.get("description", "")
        
        if not all([store_name, product_name, quantity > 0]):
            return {"success": False, "message": "表单数据不完整"}
        
        # 获取门店信息 - 从主项目导入
        from models.store import Store
        store_query = select(Store).where(
            and_(Store.project_id == project_id, Store.name == store_name)
        )
        store_result = await db.execute(store_query)
        store = store_result.scalar_one_or_none()
        
        if not store:
            return {"success": False, "message": f"门店 {store_name} 不存在"}
        
        if action == "sync":
            # 创建报损记录 - 从主项目导入
            from models.store_loss import StoreLoss
            
            # 检查是否已经存在相同的报损记录
            existing_query = select(StoreLoss).where(
                and_(
                    StoreLoss.store_id == store.id,
                    StoreLoss.approval_instance_id == instance.id
                )
            )
            existing_result = await db.execute(existing_query)
            existing_loss = existing_result.scalar_one_or_none()
            
            if existing_loss:
                return {"success": False, "message": "报损记录已存在，避免重复创建"}
            
            # 创建新的报损记录
            store_loss = StoreLoss(
                store_id=store.id,
                product_name=product_name,
                product_code=product_code,
                quantity=quantity,
                unit_price=unit_price,
                total_amount=quantity * unit_price,
                loss_reason=loss_reason,
                description=description,
                approval_instance_id=instance.id,
                approved_by=instance.originator_user_name,
                approved_at=instance.approved_at or datetime.now(),
                created_at=datetime.now()
            )
            
            db.add(store_loss)
            await db.commit()
            
            logger.info(f"创建门店报损记录: 门店={store_name}, 产品={product_name}, 数量={quantity}")
            
            return {
                "success": True,
                "data": {
                    "loss_id": store_loss.id,
                    "store_name": store_name,
                    "product_name": product_name,
                    "quantity": quantity,
                    "total_amount": quantity * unit_price
                },
                "message": "报损记录创建成功"
            }
            
        elif action == "rollback":
            # 回滚报损记录
            from models.store_loss import StoreLoss
            
            rollback_query = select(StoreLoss).where(
                and_(
                    StoreLoss.store_id == store.id,
                    StoreLoss.approval_instance_id == instance.id
                )
            )
            rollback_result = await db.execute(rollback_query)
            loss_record = rollback_result.scalar_one_or_none()
            
            if loss_record:
                await db.delete(loss_record)
                await db.commit()
                logger.info(f"回滚门店报损记录: {loss_record.id}")
                
                return {
                    "success": True,
                    "message": "报损记录回滚成功"
                }
            else:
                return {"success": False, "message": "未找到需要回滚的报损记录"}
        
        else:
            return {"success": False, "message": f"不支持的操作类型: {action}"}
            
    except Exception as e:
        logger.error(f"集成门店报损失败: {str(e)}")
        return {"success": False, "message": f"集成门店报损失败: {str(e)}"}

async def integrate_sales_report(
    db: AsyncSession, 
    project_id: str, 
    instance: 'DingTalkApprovalInstance', 
    action: str = "sync"
) -> Dict[str, Any]:
    """
    集成销售业绩上报审批
    
    Args:
        db: 数据库会话
        project_id: 项目ID
        instance: 审批实例
        action: 操作类型 (sync/rollback)
        
    Returns:
        Dict[str, Any]: 集成结果
    """
    try:
        # 解析表单数据
        form_data = {}
        if instance.form_component_values:
            for field in instance.form_component_values:
                form_data[field.get("name")] = field.get("value")
        
        store_name = form_data.get("store_name")
        report_date = form_data.get("report_date")
        total_sales = float(form_data.get("total_sales", 0))
        customer_count = int(form_data.get("customer_count", 0))
        average_price = float(form_data.get("average_price", 0))
        top_products = form_data.get("top_products", "")
        performance_summary = form_data.get("performance_summary", "")
        
        if not all([store_name, report_date, total_sales > 0]):
            return {"success": False, "message": "表单数据不完整"}
        
        # 获取门店信息
        from models.store import Store
        store_query = select(Store).where(
            and_(Store.project_id == project_id, Store.name == store_name)
        )
        store_result = await db.execute(store_query)
        store = store_result.scalar_one_or_none()
        
        if not store:
            return {"success": False, "message": f"门店 {store_name} 不存在"}
        
        if action == "sync":
            # 创建销售记录
            from models.sales_report import SalesReport
            
            # 解析日期
            try:
                report_datetime = datetime.strptime(report_date, '%Y-%m-%d')
            except ValueError:
                return {"success": False, "message": "报告日期格式错误"}
            
            # 检查是否已经存在相同的销售记录
            existing_query = select(SalesReport).where(
                and_(
                    SalesReport.store_id == store.id,
                    SalesReport.approval_instance_id == instance.id
                )
            )
            existing_result = await db.execute(existing_query)
            existing_report = existing_result.scalar_one_or_none()
            
            if existing_report:
                return {"success": False, "message": "销售记录已存在，避免重复创建"}
            
            # 创建新的销售记录
            sales_report = SalesReport(
                store_id=store.id,
                report_date=report_datetime.date(),
                total_sales=total_sales,
                customer_count=customer_count,
                average_price=average_price,
                top_products=top_products,
                performance_summary=performance_summary,
                approval_instance_id=instance.id,
                approved_by=instance.originator_user_name,
                approved_at=instance.approved_at or datetime.now(),
                created_at=datetime.now()
            )
            
            db.add(sales_report)
            await db.commit()
            
            logger.info(f"创建销售业绩记录: 门店={store_name}, 日期={report_date}, 销售额={total_sales}")
            
            return {
                "success": True,
                "data": {
                    "report_id": sales_report.id,
                    "store_name": store_name,
                    "report_date": report_date,
                    "total_sales": total_sales,
                    "customer_count": customer_count
                },
                "message": "销售业绩记录创建成功"
            }
            
        elif action == "rollback":
            # 回滚销售记录
            from models.sales_report import SalesReport
            
            rollback_query = select(SalesReport).where(
                and_(
                    SalesReport.store_id == store.id,
                    SalesReport.approval_instance_id == instance.id
                )
            )
            rollback_result = await db.execute(rollback_query)
            sales_record = rollback_result.scalar_one_or_none()
            
            if sales_record:
                await db.delete(sales_record)
                await db.commit()
                logger.info(f"回滚销售业绩记录: {sales_record.id}")
                
                return {
                    "success": True,
                    "message": "销售业绩记录回滚成功"
                }
            else:
                return {"success": False, "message": "未找到需要回滚的销售记录"}
        
        else:
            return {"success": False, "message": f"不支持的操作类型: {action}"}
            
    except Exception as e:
        logger.error(f"集成销售业绩上报失败: {str(e)}")
        return {"success": False, "message": f"集成销售业绩上报失败: {str(e)}"}

async def integrate_expense_reimbursement(
    db: AsyncSession, 
    project_id: str, 
    instance: 'DingTalkApprovalInstance', 
    action: str = "sync"
) -> Dict[str, Any]:
    """
    集成费用报销审批
    
    Args:
        db: 数据库会话
        project_id: 项目ID
        instance: 审批实例
        action: 操作类型 (sync/rollback)
        
    Returns:
        Dict[str, Any]: 集成结果
    """
    try:
        # 解析表单数据
        form_data = {}
        if instance.form_component_values:
            for field in instance.form_component_values:
                form_data[field.get("name")] = field.get("value")
        
        store_name = form_data.get("store_name")
        expense_date = form_data.get("expense_date")
        expense_type = form_data.get("expense_type")
        amount = float(form_data.get("amount", 0))
        description = form_data.get("description", "")
        vendor = form_data.get("vendor", "")
        receipts = form_data.get("receipts", "")
        
        if not all([store_name, expense_date, expense_type, amount > 0]):
            return {"success": False, "message": "表单数据不完整"}
        
        # 获取门店信息
        from models.store import Store
        store_query = select(Store).where(
            and_(Store.project_id == project_id, Store.name == store_name)
        )
        store_result = await db.execute(store_query)
        store = store_result.scalar_one_or_none()
        
        if not store:
            return {"success": False, "message": f"门店 {store_name} 不存在"}
        
        if action == "sync":
            # 创建费用记录
            from models.expense import Expense
            
            # 解析日期
            try:
                expense_datetime = datetime.strptime(expense_date, '%Y-%m-%d')
            except ValueError:
                return {"success": False, "message": "费用日期格式错误"}
            
            # 检查是否已经存在相同的费用记录
            existing_query = select(Expense).where(
                and_(
                    Expense.store_id == store.id,
                    Expense.approval_instance_id == instance.id
                )
            )
            existing_result = await db.execute(existing_query)
            existing_expense = existing_result.scalar_one_or_none()
            
            if existing_expense:
                return {"success": False, "message": "费用记录已存在，避免重复创建"}
            
            # 创建新的费用记录
            expense = Expense(
                store_id=store.id,
                expense_date=expense_datetime.date(),
                expense_type=expense_type,
                amount=amount,
                description=description,
                vendor=vendor,
                receipts=receipts,
                approval_instance_id=instance.id,
                approved_by=instance.originator_user_name,
                approved_at=instance.approved_at or datetime.now(),
                created_at=datetime.now()
            )
            
            db.add(expense)
            await db.commit()
            
            logger.info(f"创建费用报销记录: 门店={store_name}, 类型={expense_type}, 金额={amount}")
            
            return {
                "success": True,
                "data": {
                    "expense_id": expense.id,
                    "store_name": store_name,
                    "expense_type": expense_type,
                    "amount": amount,
                    "expense_date": expense_date
                },
                "message": "费用报销记录创建成功"
            }
            
        elif action == "rollback":
            # 回滚费用记录
            from models.expense import Expense
            
            rollback_query = select(Expense).where(
                and_(
                    Expense.store_id == store.id,
                    Expense.approval_instance_id == instance.id
                )
            )
            rollback_result = await db.execute(rollback_query)
            expense_record = rollback_result.scalar_one_or_none()
            
            if expense_record:
                await db.delete(expense_record)
                await db.commit()
                logger.info(f"回滚费用报销记录: {expense_record.id}")
                
                return {
                    "success": True,
                    "message": "费用报销记录回滚成功"
                }
            else:
                return {"success": False, "message": "未找到需要回滚的费用记录"}
        
        else:
            return {"success": False, "message": f"不支持的操作类型: {action}"}
            
    except Exception as e:
        logger.error(f"集成费用报销失败: {str(e)}")
        return {"success": False, "message": f"集成费用报销失败: {str(e)}"}

async def get_integration_status(
    db: AsyncSession,
    project_id: str,
    instance_id: int
) -> Dict[str, Any]:
    """
    获取业务集成状态
    
    Args:
        db: 数据库会话
        project_id: 项目ID
        instance_id: 审批实例ID
        
    Returns:
        Dict[str, Any]: 集成状态信息
    """
    try:
        # 获取审批实例
        from ..models import DingTalkApprovalInstance
        query = select(DingTalkApprovalInstance).where(
            and_(
                DingTalkApprovalInstance.id == instance_id,
                DingTalkApprovalInstance.project_id == project_id
            )
        )
        result = await db.execute(query)
        instance = result.scalar_one_or_none()
        
        if not instance:
            return {"success": False, "message": "审批实例不存在"}
        
        integration_info = {
            "instance_id": instance.id,
            "template_type": instance.template_type,
            "status": instance.status,
            "is_integrated": getattr(instance, 'is_integrated', False),
            "integrated_at": getattr(instance, 'integrated_at', None),
            "business_records": []
        }
        
        # 根据类型查询关联的业务记录
        if instance.template_type == 'store_loss':
            from models.store_loss import StoreLoss
            records_query = select(StoreLoss).where(
                StoreLoss.approval_instance_id == instance.id
            )
            records_result = await db.execute(records_query)
            records = records_result.scalars().all()
            integration_info["business_records"] = [
                {
                    "id": record.id,
                    "type": "store_loss",
                    "created_at": record.created_at.isoformat() if record.created_at else None
                }
                for record in records
            ]
            
        elif instance.template_type == 'sales_report':
            from models.sales_report import SalesReport
            records_query = select(SalesReport).where(
                SalesReport.approval_instance_id == instance.id
            )
            records_result = await db.execute(records_query)
            records = records_result.scalars().all()
            integration_info["business_records"] = [
                {
                    "id": record.id,
                    "type": "sales_report",
                    "created_at": record.created_at.isoformat() if record.created_at else None
                }
                for record in records
            ]
            
        elif instance.template_type == 'expense_reimbursement':
            from models.expense import Expense
            records_query = select(Expense).where(
                Expense.approval_instance_id == instance.id
            )
            records_result = await db.execute(records_query)
            records = records_result.scalars().all()
            integration_info["business_records"] = [
                {
                    "id": record.id,
                    "type": "expense",
                    "created_at": record.created_at.isoformat() if record.created_at else None
                }
                for record in records
            ]
        
        return {
            "success": True,
            "data": integration_info
        }
        
    except Exception as e:
        logger.error(f"获取集成状态失败: {str(e)}")
        return {
            "success": False,
            "message": f"获取集成状态失败: {str(e)}"
        } 