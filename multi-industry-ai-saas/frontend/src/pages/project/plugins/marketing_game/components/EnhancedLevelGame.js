import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  Button,
  message,
  Modal,
  Typography,
  Card,
  Row,
  Col,
  Statistic,
  Badge,
  Progress,
  Space,
  Tag,
  Tooltip,
  Alert,
  Spin,
  Empty,
  Avatar,
  List,
  Divider,
  Switch,
  Select,
  Form,
  Input,
  notification,
  Steps,
  Timeline,
  Slider
} from 'antd';
import {
  TrophyOutlined,
  GiftOutlined,
  SoundOutlined,
  SoundFilled,
  PlayCircleOutlined,
  StarOutlined,
  FireOutlined,
  ThunderboltOutlined,
  HeartOutlined,
  SmileOutlined,
  CrownOutlined,
  SettingOutlined,
  HistoryOutlined,
  UserOutlined,
  ReloadOutlined,
  ClockCircleOutlined,
  RocketOutlined,
  DiamondOutlined,
  CheckCircleOutlined,
  LockOutlined,
  UnlockOutlined,
  RightOutlined,
  CaretRightOutlined,
  FlagOutlined,
  EyeOutlined,
  PauseOutlined,
  StopOutlined
} from '@ant-design/icons';
import './EnhancedLevelGame.css';
import marketingGameApi from '../service/api/marketingGame';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;
const { Step } = Steps;

// 主题配置
const THEMES = {
  classic: {
    name: '经典风格',
    primaryColor: '#1890ff',
    secondaryColor: '#52c41a',
    bgGradient: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
    cardBg: 'rgba(255, 255, 255, 0.95)'
  },
  adventure: {
    name: '冒险风格',
    primaryColor: '#ff7a45',
    secondaryColor: '#faad14',
    bgGradient: 'linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)',
    cardBg: 'rgba(255, 215, 0, 0.2)'
  },
  dark: {
    name: '暗黑风格',
    primaryColor: '#722ed1',
    secondaryColor: '#13c2c2',
    bgGradient: 'linear-gradient(135deg, #434343 0%, #000000 100%)',
    cardBg: 'rgba(0, 0, 0, 0.8)'
  },
  galaxy: {
    name: '星系风格',
    primaryColor: '#2f54eb',
    secondaryColor: '#eb2f96',
    bgGradient: 'linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%)',
    cardBg: 'rgba(30, 30, 60, 0.8)'
  }
};

// 关卡难度
const LEVEL_DIFFICULTIES = {
  easy: { name: '简单', color: '#52c41a', icon: '🌱' },
  normal: { name: '普通', color: '#faad14', icon: '⭐' },
  hard: { name: '困难', color: '#ff7a45', icon: '🔥' },
  expert: { name: '专家', color: '#f5222d', icon: '💎' },
  master: { name: '大师', color: '#722ed1', icon: '👑' }
};

// 挑战类型
const CHALLENGE_TYPES = {
  question: { name: '答题挑战', icon: '❓' },
  puzzle: { name: '拼图挑战', icon: '🧩' },
  memory: { name: '记忆挑战', icon: '🧠' },
  speed: { name: '速度挑战', icon: '⚡' },
  luck: { name: '运气挑战', icon: '🍀' }
};

const EnhancedLevelGame = ({ gameId, embedded = false }) => {
  // 基础状态
  const [loading, setLoading] = useState(true);
  const [game, setGame] = useState(null);
  const [prizes, setPrizes] = useState([]);
  const [levels, setLevels] = useState([]);
  const [currentLevel, setCurrentLevel] = useState(0);
  const [userProgress, setUserProgress] = useState(null);
  
  // 界面状态
  const [currentTheme, setCurrentTheme] = useState('classic');
  const [soundEnabled, setSoundEnabled] = useState(true);
  const [animationSpeed, setAnimationSpeed] = useState(1);
  const [showStats, setShowStats] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [showLevelMap, setShowLevelMap] = useState(false);
  
  // 游戏状态
  const [gameStatus, setGameStatus] = useState('ready'); // ready, playing, paused, completed, failed
  const [challengeActive, setChallengeActive] = useState(false);
  const [currentChallenge, setCurrentChallenge] = useState(null);
  const [timeRemaining, setTimeRemaining] = useState(0);
  const [score, setScore] = useState(0);
  const [lives, setLives] = useState(3);
  const [powerUps, setPowerUps] = useState({});
  
  // 统计状态
  const [totalGames, setTotalGames] = useState(0);
  const [highestLevel, setHighestLevel] = useState(0);
  const [totalScore, setTotalScore] = useState(0);
  const [bestTime, setBestTime] = useState(null);
  
  // 挑战状态
  const [challengeData, setChallengeData] = useState(null);
  const [userAnswer, setUserAnswer] = useState('');
  const [challengeTimer, setChallengeTimer] = useState(null);
  
  // 结果状态
  const [showResult, setShowResult] = useState(false);
  const [gameResult, setGameResult] = useState(null);
  
  // 引用
  const timerRef = useRef(null);
  const challengeTimerRef = useRef(null);
  const animationRef = useRef(null);
  
  // 音频引用
  const startSoundRef = useRef(null);
  const successSoundRef = useRef(null);
  const failSoundRef = useRef(null);
  const levelUpSoundRef = useRef(null);
  const clickSoundRef = useRef(null);

  // 初始化音频
  useEffect(() => {
    if (soundEnabled) {
      try {
        startSoundRef.current = new Audio('/sounds/game-start.mp3');
        successSoundRef.current = new Audio('/sounds/success.mp3');
        failSoundRef.current = new Audio('/sounds/fail.mp3');
        levelUpSoundRef.current = new Audio('/sounds/level-up.mp3');
        clickSoundRef.current = new Audio('/sounds/click.mp3');
        
        [startSoundRef, successSoundRef, failSoundRef, levelUpSoundRef, clickSoundRef].forEach(ref => {
          if (ref.current) {
            ref.current.volume = 0.3;
            ref.current.preload = 'auto';
          }
        });
      } catch (error) {
        console.log('音频加载失败:', error);
      }
    }
  }, [soundEnabled]);

  // 播放音效
  const playSound = useCallback((type) => {
    if (!soundEnabled) return;
    
    try {
      let audio = null;
      switch (type) {
        case 'start':
          audio = startSoundRef.current;
          break;
        case 'success':
          audio = successSoundRef.current;
          break;
        case 'fail':
          audio = failSoundRef.current;
          break;
        case 'levelup':
          audio = levelUpSoundRef.current;
          break;
        case 'click':
          audio = clickSoundRef.current;
          break;
        default:
          return;
      }
      
      if (audio) {
        audio.currentTime = 0;
        audio.play().catch(e => console.log('音频播放失败:', e));
      }
    } catch (error) {
      console.log('播放音效失败:', error);
    }
  }, [soundEnabled]);

  // 获取游戏数据
  const fetchGameData = useCallback(async () => {
    try {
      setLoading(true);
      
      const gameResponse = await marketingGameApi.getGameDetail(gameId);
      if (!gameResponse.success) {
        message.error('获取游戏详情失败');
        return;
      }
      
      const gameData = gameResponse.data;
      setGame(gameData);
      
      const prizesResponse = await marketingGameApi.getGamePrizes(gameId);
      if (prizesResponse.success) {
        setPrizes(prizesResponse.data || []);
      }
      
      // 生成关卡数据
      generateLevels(prizesResponse.data || []);
      
      await fetchUserStats();
      
    } catch (error) {
      console.error('获取游戏数据失败:', error);
      message.error('获取游戏数据失败');
    } finally {
      setLoading(false);
    }
  }, [gameId]);

  // 生成关卡数据
  const generateLevels = (prizeList) => {
    const levelData = [];
    const difficultyKeys = Object.keys(LEVEL_DIFFICULTIES);
    const challengeKeys = Object.keys(CHALLENGE_TYPES);
    
    for (let i = 0; i < 10; i++) {
      const difficultyIndex = Math.min(Math.floor(i / 2), difficultyKeys.length - 1);
      const difficulty = difficultyKeys[difficultyIndex];
      const challengeType = challengeKeys[i % challengeKeys.length];
      
      levelData.push({
        id: i + 1,
        name: `第${i + 1}关`,
        difficulty,
        challengeType,
        timeLimit: 60 - (i * 5), // 时间递减
        scoreRequired: (i + 1) * 100,
        prize: prizeList[i % prizeList.length] || null,
        unlocked: i === 0,
        completed: false,
        bestScore: 0,
        attempts: 0
      });
    }
    
    setLevels(levelData);
  };

  // 获取用户统计
  const fetchUserStats = useCallback(async () => {
    try {
      const response = await marketingGameApi.getUserStats(gameId);
      if (response.success) {
        const stats = response.data;
        setTotalGames(stats.total_games || 0);
        setHighestLevel(stats.highest_level || 0);
        setTotalScore(stats.total_score || 0);
        setBestTime(stats.best_time);
        
        // 根据用户进度解锁关卡
        const progress = stats.level_progress || {};
        setUserProgress(progress);
        
        setLevels(prev => prev.map((level, index) => ({
          ...level,
          unlocked: index <= (stats.highest_level || 0),
          completed: progress[level.id]?.completed || false,
          bestScore: progress[level.id]?.bestScore || 0,
          attempts: progress[level.id]?.attempts || 0
        })));
      }
    } catch (error) {
      console.error('获取用户统计失败:', error);
    }
  }, [gameId]);

  // 初始化
  useEffect(() => {
    fetchGameData();
  }, [fetchGameData]);

  // 游戏计时器
  useEffect(() => {
    if (gameStatus === 'playing' && timeRemaining > 0) {
      timerRef.current = setTimeout(() => {
        setTimeRemaining(prev => {
          if (prev <= 1) {
            handleTimeUp();
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }
    
    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
    };
  }, [gameStatus, timeRemaining]);

  // 开始关卡
  const startLevel = async (levelIndex) => {
    if (levelIndex >= levels.length || !levels[levelIndex].unlocked) {
      message.warning('该关卡尚未解锁');
      return;
    }
    
    playSound('start');
    
    const level = levels[levelIndex];
    setCurrentLevel(levelIndex);
    setGameStatus('playing');
    setTimeRemaining(level.timeLimit);
    setScore(0);
    setLives(3);
    setChallengeActive(false);
    
    // 生成挑战
    generateChallenge(level);
    
    message.success(`开始${level.name}！`);
  };

  // 生成挑战
  const generateChallenge = (level) => {
    let challenge = null;
    
    switch (level.challengeType) {
      case 'question':
        challenge = generateQuestionChallenge(level);
        break;
      case 'puzzle':
        challenge = generatePuzzleChallenge(level);
        break;
      case 'memory':
        challenge = generateMemoryChallenge(level);
        break;
      case 'speed':
        challenge = generateSpeedChallenge(level);
        break;
      case 'luck':
        challenge = generateLuckChallenge(level);
        break;
      default:
        challenge = generateQuestionChallenge(level);
    }
    
    setCurrentChallenge(challenge);
    setChallengeData(challenge);
    setChallengeActive(true);
    setUserAnswer('');
  };

  // 生成答题挑战
  const generateQuestionChallenge = (level) => {
    const questions = [
      {
        question: '什么是营销游戏的主要目的？',
        options: ['娱乐用户', '增加销售', '收集数据', '品牌推广'],
        correct: 1,
        explanation: '营销游戏主要目的是通过互动增加销售转化'
      },
      {
        question: '哪种颜色在营销中最能引起注意？',
        options: ['蓝色', '红色', '绿色', '黄色'],
        correct: 1,
        explanation: '红色在视觉上最具冲击力，最能引起注意'
      },
      {
        question: '用户参与度最高的营销方式是？',
        options: ['广告投放', '内容营销', '互动游戏', '邮件营销'],
        correct: 2,
        explanation: '互动游戏能最大程度提升用户参与度'
      }
    ];
    
    const randomIndex = Math.floor(Math.random() * questions.length);
    return {
      type: 'question',
      data: questions[randomIndex],
      timeLimit: 30
    };
  };

  // 生成拼图挑战
  const generatePuzzleChallenge = (level) => {
    const patterns = [
      { pattern: '🔴🔵🔴🔵?', answer: '🔴', options: ['🔴', '🔵', '🟡', '🟢'] },
      { pattern: '123?567', answer: '4', options: ['4', '5', '6', '8'] },
      { pattern: 'ABCD?FGH', answer: 'E', options: ['E', 'D', 'F', 'X'] }
    ];
    
    const randomIndex = Math.floor(Math.random() * patterns.length);
    return {
      type: 'puzzle',
      data: patterns[randomIndex],
      timeLimit: 45
    };
  };

  // 生成记忆挑战
  const generateMemoryChallenge = (level) => {
    const sequence = [];
    const length = 3 + Math.floor(level.id / 2);
    const colors = ['🔴', '🔵', '🟡', '🟢', '🟣'];
    
    for (let i = 0; i < length; i++) {
      sequence.push(colors[Math.floor(Math.random() * colors.length)]);
    }
    
    return {
      type: 'memory',
      data: {
        sequence,
        showTime: 3000,
        userSequence: []
      },
      timeLimit: 60
    };
  };

  // 生成速度挑战
  const generateSpeedChallenge = (level) => {
    const operations = [
      { a: 15, b: 7, op: '+', answer: 22 },
      { a: 25, b: 8, op: '-', answer: 17 },
      { a: 6, b: 4, op: '*', answer: 24 },
      { a: 36, b: 6, op: '/', answer: 6 }
    ];
    
    const randomIndex = Math.floor(Math.random() * operations.length);
    const operation = operations[randomIndex];
    
    return {
      type: 'speed',
      data: {
        question: `${operation.a} ${operation.op} ${operation.b} = ?`,
        answer: operation.answer.toString()
      },
      timeLimit: 15
    };
  };

  // 生成运气挑战
  const generateLuckChallenge = (level) => {
    const choices = ['A', 'B', 'C'];
    const winChoice = choices[Math.floor(Math.random() * choices.length)];
    
    return {
      type: 'luck',
      data: {
        choices,
        winChoice,
        message: '选择你的幸运选项！'
      },
      timeLimit: 30
    };
  };

  // 提交答案
  const submitAnswer = async (answer) => {
    if (!challengeActive || !currentChallenge) return;
    
    let isCorrect = false;
    let resultMessage = '';
    
    switch (currentChallenge.type) {
      case 'question':
        isCorrect = parseInt(answer) === currentChallenge.data.correct;
        resultMessage = isCorrect ? '回答正确！' : `答案错误，正确答案是：${currentChallenge.data.options[currentChallenge.data.correct]}`;
        break;
      case 'puzzle':
        isCorrect = answer === currentChallenge.data.answer;
        resultMessage = isCorrect ? '拼图完成！' : `答案错误，正确答案是：${currentChallenge.data.answer}`;
        break;
      case 'speed':
        isCorrect = answer === currentChallenge.data.answer;
        resultMessage = isCorrect ? '计算正确！' : `计算错误，正确答案是：${currentChallenge.data.answer}`;
        break;
      case 'luck':
        isCorrect = answer === currentChallenge.data.winChoice;
        resultMessage = isCorrect ? '运气不错！' : '运气欠佳，再试一次吧！';
        break;
      case 'memory':
        isCorrect = JSON.stringify(answer) === JSON.stringify(currentChallenge.data.sequence);
        resultMessage = isCorrect ? '记忆力超群！' : '记忆有误，再试一次吧！';
        break;
      default:
        isCorrect = false;
    }
    
    setChallengeActive(false);
    
    if (isCorrect) {
      playSound('success');
      const points = 100 + (timeRemaining * 2);
      setScore(prev => prev + points);
      
      notification.success({
        message: '挑战成功！',
        description: `${resultMessage} 获得 ${points} 分`,
        duration: 3
      });
      
      // 检查是否通关
      if (score + points >= levels[currentLevel].scoreRequired) {
        await completeLevel();
      } else {
        // 生成下一个挑战
        setTimeout(() => {
          generateChallenge(levels[currentLevel]);
        }, 2000);
      }
    } else {
      playSound('fail');
      setLives(prev => {
        const newLives = prev - 1;
        if (newLives <= 0) {
          gameOver();
        }
        return newLives;
      });
      
      notification.error({
        message: '挑战失败！',
        description: resultMessage,
        duration: 3
      });
      
      // 重新生成挑战
      setTimeout(() => {
        if (lives > 1) {
          generateChallenge(levels[currentLevel]);
        }
      }, 2000);
    }
  };

  // 完成关卡
  const completeLevel = async () => {
    playSound('levelup');
    setGameStatus('completed');
    
    const level = levels[currentLevel];
    const newScore = score;
    
    // 更新关卡状态
    setLevels(prev => prev.map((lvl, index) => {
      if (index === currentLevel) {
        return {
          ...lvl,
          completed: true,
          bestScore: Math.max(lvl.bestScore, newScore),
          attempts: lvl.attempts + 1
        };
      } else if (index === currentLevel + 1) {
        return { ...lvl, unlocked: true };
      }
      return lvl;
    }));
    
    // 可能获得奖品
    if (level.prize) {
      try {
        const response = await marketingGameApi.drawPrize(gameId);
        if (response.success && response.data.prize) {
          setGameResult({
            type: 'level_complete_with_prize',
            level: level.name,
            score: newScore,
            prize: response.data.prize
          });
        } else {
          setGameResult({
            type: 'level_complete',
            level: level.name,
            score: newScore
          });
        }
      } catch (error) {
        console.error('获取奖品失败:', error);
        setGameResult({
          type: 'level_complete',
          level: level.name,
          score: newScore
        });
      }
    } else {
      setGameResult({
        type: 'level_complete',
        level: level.name,
        score: newScore
      });
    }
    
    setShowResult(true);
    await fetchUserStats();
  };

  // 游戏结束
  const gameOver = () => {
    setGameStatus('failed');
    setGameResult({
      type: 'game_over',
      level: levels[currentLevel]?.name,
      score
    });
    setShowResult(true);
  };

  // 时间到
  const handleTimeUp = () => {
    message.warning('时间到！');
    gameOver();
  };

  // 暂停游戏
  const pauseGame = () => {
    if (gameStatus === 'playing') {
      setGameStatus('paused');
      playSound('click');
    }
  };

  // 继续游戏
  const resumeGame = () => {
    if (gameStatus === 'paused') {
      setGameStatus('playing');
      playSound('click');
    }
  };

  // 重新开始关卡
  const restartLevel = () => {
    if (currentLevel < levels.length) {
      startLevel(currentLevel);
    }
  };

  // 渲染挑战内容
  const renderChallenge = () => {
    if (!currentChallenge || !challengeActive) return null;
    
    const challenge = currentChallenge;
    
    switch (challenge.type) {
      case 'question':
        return (
          <div className="question-challenge">
            <Title level={4}>{challenge.data.question}</Title>
            <Row gutter={16}>
              {challenge.data.options.map((option, index) => (
                <Col span={12} key={index}>
                  <Button
                    size="large"
                    block
                    onClick={() => submitAnswer(index)}
                    style={{ marginBottom: 8, minHeight: 48 }}
                  >
                    {String.fromCharCode(65 + index)}. {option}
                  </Button>
                </Col>
              ))}
            </Row>
          </div>
        );
        
      case 'puzzle':
        return (
          <div className="puzzle-challenge">
            <Title level={4}>找出规律，填入问号处</Title>
            <div className="pattern-display">
              <Text style={{ fontSize: 24 }}>{challenge.data.pattern}</Text>
            </div>
            <Row gutter={8} style={{ marginTop: 20 }}>
              {challenge.data.options.map((option, index) => (
                <Col span={6} key={index}>
                  <Button
                    size="large"
                    block
                    onClick={() => submitAnswer(option)}
                    style={{ fontSize: 18 }}
                  >
                    {option}
                  </Button>
                </Col>
              ))}
            </Row>
          </div>
        );
        
      case 'memory':
        return (
          <div className="memory-challenge">
            <Title level={4}>记住序列并重复</Title>
            <div className="sequence-display">
              {challenge.data.showTime > 0 ? (
                <div>
                  <Text>记住这个序列：</Text>
                  <div style={{ fontSize: 32, margin: '20px 0' }}>
                    {challenge.data.sequence.join(' ')}
                  </div>
                </div>
              ) : (
                <div>
                  <Text>请重复序列：</Text>
                  <div style={{ margin: '20px 0' }}>
                    <Input
                      placeholder="输入序列（用空格分隔）"
                      value={userAnswer}
                      onChange={(e) => setUserAnswer(e.target.value)}
                      onPressEnter={() => {
                        const sequence = userAnswer.split(' ').filter(Boolean);
                        submitAnswer(sequence);
                      }}
                    />
                  </div>
                  <Button type="primary" onClick={() => {
                    const sequence = userAnswer.split(' ').filter(Boolean);
                    submitAnswer(sequence);
                  }}>
                    提交
                  </Button>
                </div>
              )}
            </div>
          </div>
        );
        
      case 'speed':
        return (
          <div className="speed-challenge">
            <Title level={4}>快速计算</Title>
            <div className="calculation-display">
              <Text style={{ fontSize: 32 }}>{challenge.data.question}</Text>
            </div>
            <div style={{ margin: '20px 0' }}>
              <Input
                placeholder="输入答案"
                value={userAnswer}
                onChange={(e) => setUserAnswer(e.target.value)}
                onPressEnter={() => submitAnswer(userAnswer)}
                style={{ textAlign: 'center', fontSize: 18 }}
              />
            </div>
            <Button type="primary" size="large" onClick={() => submitAnswer(userAnswer)}>
              提交答案
            </Button>
          </div>
        );
        
      case 'luck':
        return (
          <div className="luck-challenge">
            <Title level={4}>{challenge.data.message}</Title>
            <Row gutter={16} style={{ marginTop: 20 }}>
              {challenge.data.choices.map((choice, index) => (
                <Col span={8} key={index}>
                  <Button
                    size="large"
                    block
                    onClick={() => submitAnswer(choice)}
                    style={{ height: 80, fontSize: 24 }}
                  >
                    选项 {choice}
                  </Button>
                </Col>
              ))}
            </Row>
          </div>
        );
        
      default:
        return <div>未知挑战类型</div>;
    }
  };

  // 渲染关卡地图
  const renderLevelMap = () => (
    <div className="level-map">
      <Row gutter={16}>
        {levels.map((level, index) => (
          <Col span={8} key={level.id} style={{ marginBottom: 16 }}>
            <Card
              className={`level-card ${level.completed ? 'completed' : ''} ${!level.unlocked ? 'locked' : ''}`}
              hoverable={level.unlocked}
              onClick={() => level.unlocked && startLevel(index)}
              style={{
                cursor: level.unlocked ? 'pointer' : 'not-allowed',
                opacity: level.unlocked ? 1 : 0.5
              }}
            >
              <div className="level-header">
                <Space>
                  {level.unlocked ? (
                    level.completed ? (
                      <CheckCircleOutlined style={{ color: '#52c41a' }} />
                    ) : (
                      <UnlockOutlined style={{ color: '#faad14' }} />
                    )
                  ) : (
                    <LockOutlined style={{ color: '#d9d9d9' }} />
                  )}
                  <Text strong>{level.name}</Text>
                </Space>
              </div>
              <div className="level-info">
                <Space direction="vertical" size="small" style={{ width: '100%' }}>
                  <Tag color={LEVEL_DIFFICULTIES[level.difficulty].color}>
                    {LEVEL_DIFFICULTIES[level.difficulty].icon} {LEVEL_DIFFICULTIES[level.difficulty].name}
                  </Tag>
                  <Text type="secondary">
                    {CHALLENGE_TYPES[level.challengeType].icon} {CHALLENGE_TYPES[level.challengeType].name}
                  </Text>
                  <Text type="secondary">
                    <ClockCircleOutlined /> {level.timeLimit}秒
                  </Text>
                  {level.bestScore > 0 && (
                    <Text type="secondary">
                      <TrophyOutlined /> 最高分: {level.bestScore}
                    </Text>
                  )}
                </Space>
              </div>
            </Card>
          </Col>
        ))}
      </Row>
    </div>
  );

  // 渲染统计信息
  const renderStats = () => (
    <Row gutter={16}>
      <Col span={6}>
        <Card className="stat-card">
          <Statistic
            title="总游戏次数"
            value={totalGames}
            prefix={<PlayCircleOutlined />}
            valueStyle={{ color: THEMES[currentTheme].primaryColor }}
          />
        </Card>
      </Col>
      <Col span={6}>
        <Card className="stat-card">
          <Statistic
            title="最高关卡"
            value={highestLevel + 1}
            prefix={<FlagOutlined />}
            valueStyle={{ color: '#52c41a' }}
          />
        </Card>
      </Col>
      <Col span={6}>
        <Card className="stat-card">
          <Statistic
            title="总积分"
            value={totalScore}
            prefix={<StarOutlined />}
            valueStyle={{ color: '#faad14' }}
          />
        </Card>
      </Col>
      <Col span={6}>
        <Card className="stat-card">
          <Statistic
            title="最佳时间"
            value={bestTime || '--'}
            suffix="秒"
            prefix={<RocketOutlined />}
            valueStyle={{ color: '#f5222d' }}
          />
        </Card>
      </Col>
    </Row>
  );

  // 渲染设置面板
  const renderSettings = () => (
    <Space direction="vertical" style={{ width: '100%' }}>
      <Card title="主题设置">
        <Select
          value={currentTheme}
          onChange={setCurrentTheme}
          style={{ width: '100%' }}
        >
          {Object.entries(THEMES).map(([key, theme]) => (
            <Option key={key} value={key}>
              {theme.name}
            </Option>
          ))}
        </Select>
      </Card>
      
      <Card title="动画速度">
        <Slider
          value={animationSpeed}
          onChange={setAnimationSpeed}
          min={0.5}
          max={2}
          step={0.1}
          marks={{
            0.5: '慢',
            1: '正常',
            1.5: '快',
            2: '超快'
          }}
        />
      </Card>
      
      <Card title="音效设置">
        <Space>
          <Text>音效开关：</Text>
          <Switch
            checked={soundEnabled}
            onChange={setSoundEnabled}
            checkedChildren={<SoundFilled />}
            unCheckedChildren={<SoundOutlined />}
          />
        </Space>
      </Card>
    </Space>
  );

  if (loading) {
    return (
      <div className="level-loading">
        <Spin size="large" />
        <Title level={4} style={{ marginTop: 16 }}>
          加载游戏中...
        </Title>
      </div>
    );
  }

  if (!game || !levels.length) {
    return (
      <Empty
        description="游戏暂未配置或关卡为空"
        image={Empty.PRESENTED_IMAGE_SIMPLE}
      />
    );
  }

  const theme = THEMES[currentTheme];

  return (
    <div className={`enhanced-level-game theme-${currentTheme}`} style={{ background: theme.bgGradient }}>
      {/* 游戏头部 */}
      <div className="game-header">
        <Card className="header-card" style={{ background: theme.cardBg }}>
          <Row justify="space-between" align="middle">
            <Col>
              <Title level={3} style={{ margin: 0 }}>
                <RocketOutlined style={{ marginRight: 8, color: theme.primaryColor }} />
                {game.name}
              </Title>
              <Text>{game.description}</Text>
            </Col>
            <Col>
              <Space>
                <Tooltip title="关卡地图">
                  <Button
                    icon={<EyeOutlined />}
                    onClick={() => setShowLevelMap(!showLevelMap)}
                  />
                </Tooltip>
                <Tooltip title="游戏统计">
                  <Button
                    icon={<HistoryOutlined />}
                    onClick={() => setShowStats(!showStats)}
                  />
                </Tooltip>
                <Tooltip title="游戏设置">
                  <Button
                    icon={<SettingOutlined />}
                    onClick={() => setShowSettings(!showSettings)}
                  />
                </Tooltip>
              </Space>
            </Col>
          </Row>
        </Card>
      </div>

      {/* 统计信息 */}
      {showStats && (
        <div className="stats-section">
          {renderStats()}
        </div>
      )}

      {/* 关卡地图 */}
      {showLevelMap && (
        <div className="level-map-section">
          <Card title="关卡地图" className="map-card" style={{ background: theme.cardBg }}>
            {renderLevelMap()}
          </Card>
        </div>
      )}

      {/* 主游戏区域 */}
      <div className="main-game-area">
        <Row gutter={24}>
          <Col span={showSettings ? 16 : 24}>
            {gameStatus === 'ready' ? (
              <Card className="ready-card" style={{ background: theme.cardBg }}>
                <div className="ready-content">
                  <Title level={2}>准备开始闯关！</Title>
                  <Paragraph>选择关卡开始你的冒险之旅</Paragraph>
                  {renderLevelMap()}
                </div>
              </Card>
            ) : (
              <Card className="game-card" style={{ background: theme.cardBg }}>
                {/* 游戏状态栏 */}
                <div className="game-status-bar">
                  <Row justify="space-between" align="middle">
                    <Col>
                      <Space size="large">
                        <Statistic
                          title="关卡"
                          value={levels[currentLevel]?.name || ''}
                          valueStyle={{ fontSize: 16 }}
                        />
                        <Statistic
                          title="分数"
                          value={score}
                          valueStyle={{ color: theme.primaryColor }}
                        />
                        <Statistic
                          title="生命"
                          value={lives}
                          prefix={<HeartOutlined style={{ color: '#f5222d' }} />}
                          valueStyle={{ color: '#f5222d' }}
                        />
                      </Space>
                    </Col>
                    <Col>
                      <Space>
                        <Progress
                          type="circle"
                          percent={(timeRemaining / (levels[currentLevel]?.timeLimit || 1)) * 100}
                          format={() => `${timeRemaining}s`}
                          width={60}
                          strokeColor={timeRemaining > 10 ? theme.primaryColor : '#f5222d'}
                        />
                        {gameStatus === 'playing' && (
                          <Button icon={<PauseOutlined />} onClick={pauseGame}>
                            暂停
                          </Button>
                        )}
                        {gameStatus === 'paused' && (
                          <Button icon={<CaretRightOutlined />} onClick={resumeGame}>
                            继续
                          </Button>
                        )}
                        <Button icon={<StopOutlined />} onClick={() => setGameStatus('ready')}>
                          退出
                        </Button>
                      </Space>
                    </Col>
                  </Row>
                </div>

                {/* 挑战区域 */}
                <div className="challenge-area">
                  {gameStatus === 'paused' ? (
                    <div className="paused-overlay">
                      <Title level={3}>游戏已暂停</Title>
                      <Button type="primary" size="large" onClick={resumeGame}>
                        继续游戏
                      </Button>
                    </div>
                  ) : (
                    renderChallenge()
                  )}
                </div>
              </Card>
            )}
          </Col>

          {/* 设置面板 */}
          {showSettings && (
            <Col span={8}>
              <Card title="游戏设置" className="settings-card" style={{ background: theme.cardBg }}>
                {renderSettings()}
              </Card>
            </Col>
          )}
        </Row>
      </div>

      {/* 结果模态框 */}
      <Modal
        open={showResult}
        onCancel={() => setShowResult(false)}
        footer={null}
        centered
        className="result-modal"
        width={480}
      >
        <div className="result-content">
          {gameResult?.type === 'level_complete_with_prize' ? (
            <div className="win-result">
              <div className="celebration-icon">
                <CrownOutlined style={{ fontSize: 64, color: '#faad14' }} />
              </div>
              <Title level={2} style={{ color: '#52c41a', margin: '16px 0' }}>
                🎉 关卡完成！获得奖品！
              </Title>
              <Card className="prize-info-card">
                <Avatar
                  src={gameResult.prize.image_url}
                  icon={<GiftOutlined />}
                  size={80}
                />
                <Title level={4} style={{ margin: '16px 0 8px' }}>
                  {gameResult.prize.name}
                </Title>
                <Text type="secondary">{gameResult.prize.description}</Text>
                <div style={{ marginTop: 16 }}>
                  <Tag color="gold">关卡: {gameResult.level}</Tag>
                  <Tag color="blue">分数: {gameResult.score}</Tag>
                </div>
              </Card>
            </div>
          ) : gameResult?.type === 'level_complete' ? (
            <div className="success-result">
              <div className="success-icon">
                <CheckCircleOutlined style={{ fontSize: 64, color: '#52c41a' }} />
              </div>
              <Title level={2} style={{ color: '#52c41a', margin: '16px 0' }}>
                🎊 关卡完成！
              </Title>
              <div style={{ textAlign: 'center' }}>
                <Tag color="gold">关卡: {gameResult.level}</Tag>
                <Tag color="blue">分数: {gameResult.score}</Tag>
              </div>
            </div>
          ) : (
            <div className="fail-result">
              <div className="fail-icon">
                <SmileOutlined style={{ fontSize: 64, color: '#1890ff' }} />
              </div>
              <Title level={3} style={{ margin: '16px 0' }}>
                再接再厉！
              </Title>
              <Paragraph style={{ textAlign: 'center' }}>
                本次挑战结束，继续努力吧！
              </Paragraph>
              <div style={{ textAlign: 'center' }}>
                <Tag color="orange">关卡: {gameResult?.level}</Tag>
                <Tag color="blue">分数: {gameResult?.score || 0}</Tag>
              </div>
            </div>
          )}
          
          <div className="result-actions">
            <Space size="large">
              <Button onClick={() => setShowResult(false)}>
                关闭
              </Button>
              <Button
                type="primary"
                icon={<ReloadOutlined />}
                onClick={() => {
                  setShowResult(false);
                  setTimeout(restartLevel, 500);
                }}
              >
                再玩一次
              </Button>
            </Space>
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default EnhancedLevelGame; 