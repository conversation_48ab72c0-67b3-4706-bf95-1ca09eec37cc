import api from '../httpClient';
import { getTenantId } from '../httpClient';

/**
 * 租户-项目管理API
 */
const project = {
  /**
   * 获取项目列表
   * @param {Object} params - 查询参数
   * @returns {Promise} - 项目列表
   */
  getList: async (params = {}) => {
    const tenantId = getTenantId();
    if (!tenantId) {
      throw new Error('未指定租户ID');
    }
    return api.get('/tenant/projects', { params });
  },

  /**
   * 获取项目详情
   * @param {string} id - 项目ID
   * @returns {Promise} - 项目详情
   */
  getDetail: async (id) => {
    const tenantId = getTenantId();
    if (!tenantId) {
      throw new Error('未指定租户ID');
    }
    return api.get(`/tenant/projects/${id}`);
  },

  /**
   * 创建项目
   * @param {Object} data - 项目数据
   * @returns {Promise} - 创建结果
   */
  create: async (data) => {
    const tenantId = getTenantId();
    if (!tenantId) {
      throw new Error('未指定租户ID');
    }
    return api.post('/tenant/projects', data);
  },

  /**
   * 更新项目
   * @param {string} id - 项目ID
   * @param {Object} data - 项目数据
   * @returns {Promise} - 更新结果
   */
  update: async (id, data) => {
    const tenantId = getTenantId();
    if (!tenantId) {
      throw new Error('未指定租户ID');
    }
    return api.put(`/tenant/projects/${id}`, data);
  },

  /**
   * 删除项目
   * @param {string} id - 项目ID
   * @returns {Promise} - 删除结果
   */
  delete: async (id) => {
    const tenantId = getTenantId();
    if (!tenantId) {
      throw new Error('未指定租户ID');
    }
    return api.delete(`/tenant/projects/${id}`);
  },

  /**
   * 获取项目用户列表
   * @param {string} projectId - 项目ID
   * @param {Object} params - 查询参数
   * @returns {Promise} - 用户列表
   */
  getUsers: async (projectId, params = {}) => {
    const tenantId = getTenantId();
    if (!tenantId) {
      throw new Error('未指定租户ID');
    }
    return api.get(`/tenant/projects/${projectId}/users`, { params });
  },

  /**
   * 添加项目用户
   * @param {string} projectId - 项目ID
   * @param {Object} data - 用户数据
   * @returns {Promise} - 添加结果
   */
  addUser: async (projectId, data) => {
    const tenantId = getTenantId();
    if (!tenantId) {
      throw new Error('未指定租户ID');
    }
    return api.post(`/tenant/projects/${projectId}/users`, data);
  },

  /**
   * 移除项目用户
   * @param {string} projectId - 项目ID
   * @param {string} userId - 用户ID
   * @returns {Promise} - 移除结果
   */
  removeUser: async (projectId, userId) => {
    const tenantId = getTenantId();
    if (!tenantId) {
      throw new Error('未指定租户ID');
    }
    return api.delete(`/tenant/projects/${projectId}/users/${userId}`);
  },

  /**
   * 获取项目统计信息
   * @param {string} projectId - 项目ID
   * @returns {Promise} - 统计信息
   */
  getStats: async (projectId) => {
    const tenantId = getTenantId();
    if (!tenantId) {
      throw new Error('未指定租户ID');
    }
    return api.get(`/tenant/projects/${projectId}/stats`);
  },

  /**
   * 初始化项目
   * @param {string} projectId - 项目ID
   * @returns {Promise} - 初始化结果
   */
  initialize: async (projectId) => {
    const tenantId = getTenantId();
    if (!tenantId) {
      throw new Error('未指定租户ID');
    }
    return api.post(`/tenant/projects/${projectId}/init`);
  }
};

export default project;
