import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Modal,
  Form,
  Input,
  Select,
  InputNumber,
  Switch,
  message,
  Popconfirm,
  Tooltip,
  Typography,
  Row,
  Col,
  Divider,
  Alert,
  Slider,
  Radio
} from 'antd';
import {
  PlusOutlined,
  EditOutlined,
  DeleteOutlined,
  RobotOutlined,
  DollarOutlined,
  LineC<PERSON>Outlined,
  BulbOutlined,
  ThunderboltOutlined
} from '@ant-design/icons';
import { useParams } from 'react-router-dom';
import multiChannelService from '../services/multiChannelService';

const { Title, Text } = Typography;
const { Option } = Select;

const PricingStrategy = () => {
  const { projectId } = useParams();
  const [loading, setLoading] = useState(false);
  const [strategies, setStrategies] = useState([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [aiModalVisible, setAiModalVisible] = useState(false);
  const [editingStrategy, setEditingStrategy] = useState(null);
  const [aiSuggestions, setAiSuggestions] = useState(null);
  const [form] = Form.useForm();
  const [aiForm] = Form.useForm();

  useEffect(() => {
    loadStrategies();
  }, [projectId]);

  const loadStrategies = async () => {
    try {
      setLoading(true);
      const response = await multiChannelService.getPricingStrategies(projectId);
      setStrategies(response.data || []);
    } catch (error) {
      console.error('加载定价策略失败:', error);
      message.error('加载定价策略失败');
    } finally {
      setLoading(false);
    }
  };

  const handleAdd = () => {
    setEditingStrategy(null);
    form.resetFields();
    setModalVisible(true);
  };

  const handleEdit = (record) => {
    setEditingStrategy(record);
    form.setFieldsValue({
      ...record,
      conditions: record.conditions || {},
      actions: record.actions || {}
    });
    setModalVisible(true);
  };

  const handleDelete = async (id) => {
    try {
      await multiChannelService.deletePricingStrategy(projectId, id);
      message.success('删除成功');
      loadStrategies();
    } catch (error) {
      console.error('删除失败:', error);
      message.error('删除失败');
    }
  };

  const handleSubmit = async (values) => {
    try {
      if (editingStrategy) {
        await multiChannelService.updatePricingStrategy(projectId, editingStrategy.id, values);
        message.success('更新成功');
      } else {
        await multiChannelService.createPricingStrategy(projectId, values);
        message.success('创建成功');
      }
      setModalVisible(false);
      loadStrategies();
    } catch (error) {
      console.error('保存失败:', error);
      message.error('保存失败');
    }
  };

  const handleGetAiSuggestions = async (values) => {
    try {
      setLoading(true);
      const response = await multiChannelService.getAiPricingSuggestions(
        projectId,
        values.product_id,
        values.channel_id
      );
      setAiSuggestions(response.data);
      message.success('AI建议获取成功');
    } catch (error) {
      console.error('获取AI建议失败:', error);
      message.error('获取AI建议失败');
    } finally {
      setLoading(false);
    }
  };

  const getStrategyTypeTag = (type) => {
    const typeMap = {
      'fixed': { color: 'blue', text: '固定定价' },
      'dynamic': { color: 'green', text: '动态定价' },
      'competitor_based': { color: 'orange', text: '竞品跟随' },
      'ai_optimized': { color: 'purple', text: 'AI优化' }
    };
    
    const config = typeMap[type] || { color: 'default', text: type };
    return <Tag color={config.color}>{config.text}</Tag>;
  };

  const getStatusTag = (isActive) => {
    return isActive ? 
      <Tag color="success">启用</Tag> : 
      <Tag color="default">禁用</Tag>;
  };

  const columns = [
    {
      title: '策略名称',
      dataIndex: 'strategy_name',
      key: 'strategy_name',
      render: (text, record) => (
        <div>
          <div style={{ fontWeight: 'bold' }}>{text}</div>
          <Text type="secondary" style={{ fontSize: 12 }}>
            {record.description}
          </Text>
        </div>
      )
    },
    {
      title: '策略类型',
      dataIndex: 'strategy_type',
      key: 'strategy_type',
      render: (type) => getStrategyTypeTag(type)
    },
    {
      title: '适用范围',
      key: 'scope',
      render: (_, record) => (
        <div>
          <div>商品: {record.product_count || 0} 个</div>
          <div>渠道: {record.channel_count || 0} 个</div>
        </div>
      )
    },
    {
      title: '价格调整',
      key: 'price_adjustment',
      render: (_, record) => {
        const adjustment = record.actions?.price_adjustment;
        if (!adjustment) return '-';
        
        return (
          <div>
            <div>{adjustment.type === 'percentage' ? '百分比' : '固定金额'}</div>
            <div style={{ color: adjustment.value > 0 ? '#52c41a' : '#ff4d4f' }}>
              {adjustment.value > 0 ? '+' : ''}{adjustment.value}
              {adjustment.type === 'percentage' ? '%' : '元'}
            </div>
          </div>
        );
      }
    },
    {
      title: '执行次数',
      dataIndex: 'execution_count',
      key: 'execution_count',
      render: (count) => count || 0
    },
    {
      title: '状态',
      dataIndex: 'is_active',
      key: 'is_active',
      render: (isActive) => getStatusTag(isActive)
    },
    {
      title: '操作',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Tooltip title="编辑">
            <Button
              type="text"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            />
          </Tooltip>
          
          <Tooltip title="查看执行记录">
            <Button
              type="text"
              icon={<LineChartOutlined />}
            />
          </Tooltip>
          
          <Popconfirm
            title="确定要删除这个定价策略吗？"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除">
              <Button
                type="text"
                danger
                icon={<DeleteOutlined />}
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      )
    }
  ];

  return (
    <div>

      {/* AI智能运营助手 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={8}>
          <Card>
            <div style={{ textAlign: 'center' }}>
              <BulbOutlined style={{ fontSize: 32, color: '#1890ff', marginBottom: 8 }} />
              <div>
                <Text strong>AI智能定价</Text>
                <div style={{ marginTop: 4 }}>
                  <Text type="secondary" style={{ fontSize: 12 }}>
                    基于市场数据和竞品分析
                  </Text>
                </div>
                <Button
                  type="primary"
                  size="small"
                  style={{ marginTop: 8 }}
                  onClick={() => setAiModalVisible(true)}
                >
                  获取建议
                </Button>
              </div>
            </div>
          </Card>
        </Col>

        <Col span={8}>
          <Card>
            <div style={{ textAlign: 'center' }}>
              <RobotOutlined style={{ fontSize: 32, color: '#52c41a', marginBottom: 8 }} />
              <div>
                <Text strong>AI运营智能体</Text>
                <div style={{ marginTop: 4 }}>
                  <Text type="secondary" style={{ fontSize: 12 }}>
                    全自动化运营决策
                  </Text>
                </div>
                <Button
                  type="primary"
                  size="small"
                  style={{ marginTop: 8, backgroundColor: '#52c41a', borderColor: '#52c41a' }}
                >
                  启动智能体
                </Button>
              </div>
            </div>
          </Card>
        </Col>

        <Col span={8}>
          <Card>
            <div style={{ textAlign: 'center' }}>
              <ThunderboltOutlined style={{ fontSize: 32, color: '#fa8c16', marginBottom: 8 }} />
              <div>
                <Text strong>批量优化</Text>
                <div style={{ marginTop: 4 }}>
                  <Text type="secondary" style={{ fontSize: 12 }}>
                    一键优化所有商品价格
                  </Text>
                </div>
                <Button
                  type="primary"
                  size="small"
                  style={{ marginTop: 8, backgroundColor: '#fa8c16', borderColor: '#fa8c16' }}
                >
                  开始优化
                </Button>
              </div>
            </div>
          </Card>
        </Col>
      </Row>

      <Card>
        <div style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between' }}>
          <Space>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={handleAdd}
            >
              创建策略
            </Button>
            <Button
              icon={<ThunderboltOutlined />}
            >
              批量执行
            </Button>
          </Space>
          
          <Space>
            <Text type="secondary">
              共 {strategies.length} 个策略
            </Text>
          </Space>
        </div>

        <Table
          columns={columns}
          dataSource={strategies}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`
          }}
        />
      </Card>

      {/* 定价策略模态框 */}
      <Modal
        title={editingStrategy ? '编辑定价策略' : '创建定价策略'}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={800}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="strategy_name"
                label="策略名称"
                rules={[{ required: true, message: '请输入策略名称' }]}
              >
                <Input placeholder="如：美团外卖动态定价" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="strategy_type"
                label="策略类型"
                rules={[{ required: true, message: '请选择策略类型' }]}
              >
                <Select placeholder="选择策略类型">
                  <Option value="fixed">固定定价</Option>
                  <Option value="dynamic">动态定价</Option>
                  <Option value="competitor_based">竞品跟随</Option>
                  <Option value="ai_optimized">AI优化</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="description"
            label="策略描述"
          >
            <Input.TextArea rows={2} placeholder="描述这个定价策略的用途和规则" />
          </Form.Item>

          <Divider>触发条件</Divider>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name={['conditions', 'time_based']}
                label="时间触发"
              >
                <Select placeholder="选择时间触发条件">
                  <Option value="hourly">每小时</Option>
                  <Option value="daily">每天</Option>
                  <Option value="weekly">每周</Option>
                  <Option value="custom">自定义</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name={['conditions', 'competitor_change']}
                label="竞品价格变动"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name={['conditions', 'sales_threshold']}
                label="销量阈值"
              >
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="销量低于此值时触发"
                  min={0}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name={['conditions', 'inventory_threshold']}
                label="库存阈值"
              >
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="库存低于此值时触发"
                  min={0}
                />
              </Form.Item>
            </Col>
          </Row>

          <Divider>执行动作</Divider>

          <Form.Item
            name={['actions', 'adjustment_type']}
            label="调价方式"
            rules={[{ required: true, message: '请选择调价方式' }]}
          >
            <Radio.Group>
              <Radio value="percentage">按百分比调整</Radio>
              <Radio value="fixed">按固定金额调整</Radio>
              <Radio value="target_price">设置目标价格</Radio>
            </Radio.Group>
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name={['actions', 'adjustment_value']}
                label="调整幅度"
                rules={[{ required: true, message: '请输入调整幅度' }]}
              >
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="调整的数值"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name={['actions', 'max_adjustment']}
                label="最大调整幅度(%)"
              >
                <Slider
                  min={1}
                  max={50}
                  marks={{
                    1: '1%',
                    10: '10%',
                    25: '25%',
                    50: '50%'
                  }}
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name={['actions', 'min_price']}
                label="最低价格"
              >
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="价格不能低于此值"
                  min={0}
                  precision={2}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name={['actions', 'max_price']}
                label="最高价格"
              >
                <InputNumber
                  style={{ width: '100%' }}
                  placeholder="价格不能高于此值"
                  min={0}
                  precision={2}
                />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="is_active"
            label="启用策略"
            valuePropName="checked"
            initialValue={true}
          >
            <Switch />
          </Form.Item>

          <div style={{ textAlign: 'right', marginTop: 24 }}>
            <Space>
              <Button onClick={() => setModalVisible(false)}>
                取消
              </Button>
              <Button type="primary" htmlType="submit">
                {editingStrategy ? '更新' : '创建'}
              </Button>
            </Space>
          </div>
        </Form>
      </Modal>

      {/* AI定价建议模态框 */}
      <Modal
        title="AI智能定价建议"
        open={aiModalVisible}
        onCancel={() => setAiModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form
          form={aiForm}
          layout="vertical"
          onFinish={handleGetAiSuggestions}
        >
          <Alert
            message="AI定价助手"
            description="基于历史销售数据、竞品价格、市场趋势等因素，为您提供最优定价建议"
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="product_id"
                label="选择商品"
                rules={[{ required: true, message: '请选择商品' }]}
              >
                <Select placeholder="选择要分析的商品">
                  {/* 这里应该加载商品列表 */}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="channel_id"
                label="选择渠道"
                rules={[{ required: true, message: '请选择渠道' }]}
              >
                <Select placeholder="选择目标渠道">
                  {/* 这里应该加载渠道列表 */}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          {aiSuggestions && (
            <Card title="AI建议结果" style={{ marginTop: 16 }}>
              <Row gutter={16}>
                <Col span={8}>
                  <div style={{ textAlign: 'center' }}>
                    <div style={{ fontSize: 24, fontWeight: 'bold', color: '#1890ff' }}>
                      ¥{aiSuggestions.suggested_price}
                    </div>
                    <div>建议价格</div>
                  </div>
                </Col>
                <Col span={8}>
                  <div style={{ textAlign: 'center' }}>
                    <div style={{ fontSize: 24, fontWeight: 'bold', color: '#52c41a' }}>
                      +{aiSuggestions.expected_sales_increase}%
                    </div>
                    <div>预期销量提升</div>
                  </div>
                </Col>
                <Col span={8}>
                  <div style={{ textAlign: 'center' }}>
                    <div style={{ fontSize: 24, fontWeight: 'bold', color: '#722ed1' }}>
                      {aiSuggestions.confidence_score}%
                    </div>
                    <div>置信度</div>
                  </div>
                </Col>
              </Row>
              
              <Divider />
              
              <div>
                <Text strong>分析说明：</Text>
                <div style={{ marginTop: 8 }}>
                  {aiSuggestions.analysis_notes}
                </div>
              </div>
            </Card>
          )}

          <div style={{ textAlign: 'right', marginTop: 24 }}>
            <Space>
              <Button onClick={() => setAiModalVisible(false)}>
                关闭
              </Button>
              <Button type="primary" htmlType="submit" loading={loading}>
                获取AI建议
              </Button>
            </Space>
          </div>
        </Form>
      </Modal>
    </div>
  );
};

export default PricingStrategy;
