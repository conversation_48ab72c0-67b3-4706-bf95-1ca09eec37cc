# StoreSalesTarget 模型重构记录

## 问题描述

在代码库中发现了两个不同的 `StoreSalesTarget` 模型定义，造成了以下问题：

1. **数据库字段冲突**：两个模型都定义了 `daily_target` 字段，但结构不同
2. **导入混乱**：不同文件导入不同的 `StoreSalesTarget` 模型
3. **功能重复**：两个模型实现相似的功能但数据结构不一致

## 原有模型对比

### 模型 A：`models/store_sales_target.py`（已删除）
```python
class StoreSalesTarget(Base):
    __tablename__ = "store_sales_targets"
    
    # 按日期的目标设定
    target_date = Column(Date, nullable=False, comment="目标日期")
    daily_target = Column(Float, nullable=False, comment="当日销售目标额")
    monthly_target = Column(Float, nullable=True, comment="当月总销售目标额")
```

### 模型 B：`models/store_operations.py`（保留）
```python
class StoreSalesTarget(Base):
    __tablename__ = "store_sales_targets"
    
    # 按年月的目标设定，包含更多业务逻辑
    target_year = Column(Integer, nullable=False, comment="目标年份")
    target_month = Column(Integer, nullable=False, comment="目标月份")
    last_year_sales = Column(Float, nullable=True, comment="去年同期月销售额")
    growth_rate = Column(Float, nullable=True, comment="期望增长率(%)")
    monthly_target = Column(Float, nullable=False, comment="月度销售目标")
    daily_target = Column(Float, nullable=True, comment="日均销售目标")
```

## 重构决策

**保留模型 B**，原因：
1. ✅ **功能更完整**：包含年同比、增长率等业务逻辑
2. ✅ **使用更广泛**：在 `store_operations.py` 中有完整的 CRUD API
3. ✅ **数据结构更合理**：使用年月组合而非每日记录，减少数据冗余
4. ✅ **已有实际应用**：有实际的业务流程在使用

**删除模型 A**，原因：
1. ❌ **功能重复**：与模型 B 功能重叠
2. ❌ **使用有限**：仅在 `super_dashboard.py` 中使用
3. ❌ **结构简单**：缺少重要的业务字段

## 重构步骤

### 1. 删除重复模型
- 删除 `backend/models/store_sales_target.py` 文件

### 2. 修复导入引用
- 修改 `backend/api/v1/project/super_dashboard.py` 导入语句
- 修改 `backend/models/__init__.py` 导入语句

### 3. 调整查询逻辑
```python
# 修改前：使用具体日期查询
StoreSalesTarget.target_date == query_date

# 修改后：使用年月查询
StoreSalesTarget.target_year == current_year,
StoreSalesTarget.target_month == current_month
```

### 4. 添加错误处理
```python
try:
    # 查询销售目标
    sales_target_stmt = select(func.sum(StoreSalesTarget.daily_target))...
    total_target = await db.scalar(sales_target_stmt) or 0
except Exception as e:
    logger.warning(f"获取销售目标数据失败: {e}, 使用默认值")
    total_target = 100000  # 默认目标值
```

## 影响范围

### 修改的文件
- ✅ `backend/models/store_sales_target.py` - 已删除
- ✅ `backend/api/v1/project/super_dashboard.py` - 修改导入和查询逻辑
- ✅ `backend/models/__init__.py` - 修改导入语句

### 不受影响的功能
- ✅ 门店运营管理的销售目标 CRUD API
- ✅ 现有的销售目标数据（使用年月结构）
- ✅ 其他业务模块

### 需要测试的功能
- [ ] 门店大盘的销售目标显示
- [ ] 运营KPI数据获取
- [ ] 销售目标进度计算

## 数据迁移建议

由于保留的模型使用不同的数据结构（年月 vs 日期），建议：

1. **检查现有数据**：确认 `store_sales_targets` 表使用的是哪种结构
2. **数据转换**：如有必要，将日期格式的数据转换为年月格式
3. **添加默认数据**：为测试环境添加一些示例销售目标数据

## 验证结果

```bash
✅ StoreSalesTarget 导入成功
```

重构完成，模型冲突已解决！

## 最佳实践总结

1. **避免重复模型**：相同业务概念应使用统一的数据模型
2. **选择更完整的实现**：保留功能更全面、使用更广泛的模型
3. **渐进式重构**：先统一导入，再调整业务逻辑，最后清理冗余代码
4. **完善错误处理**：为数据库变更添加降级处理方案 