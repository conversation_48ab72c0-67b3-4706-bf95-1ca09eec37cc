-- 初始化存储配额数据
-- 为现有项目创建默认存储配额

DO $$
DECLARE
    project_record RECORD;
    quota_exists INTEGER;
BEGIN
    -- 为每个项目创建默认存储配额
    FOR project_record IN SELECT id, tenant_id FROM projects LOOP
        -- 检查项目配额是否已存在
        SELECT COUNT(*) INTO quota_exists 
        FROM storage_quotas 
        WHERE project_id = project_record.id AND user_id IS NULL;
        
        IF quota_exists = 0 THEN
            -- 插入项目级别的存储配额（默认5GB）
            INSERT INTO storage_quotas (
                id, tenant_id, project_id, user_id, 
                total_quota, used_quota, image_quota, document_quota, other_quota,
                last_updated, created_at
            ) VALUES (
                gen_random_uuid(),
                project_record.tenant_id,
                project_record.id,
                NULL,  -- 项目级配额，user_id为NULL
                5368709120,  -- 5GB
                0,
                0,
                0,
                0,
                NOW(),
                NOW()
            );
            
            RAISE NOTICE '为项目 % 创建了默认存储配额', project_record.id;
        ELSE
            RAISE NOTICE '项目 % 的存储配额已存在', project_record.id;
        END IF;
    END LOOP;
    
    -- 更新现有配额的使用情况
    UPDATE storage_quotas 
    SET 
        used_quota = COALESCE((
            SELECT SUM(size) 
            FROM storage_files 
            WHERE project_id = storage_quotas.project_id 
            AND status = 'active'
            AND (storage_quotas.user_id IS NULL OR uploaded_by = storage_quotas.user_id)
        ), 0),
        image_quota = COALESCE((
            SELECT SUM(size) 
            FROM storage_files 
            WHERE project_id = storage_quotas.project_id 
            AND status = 'active'
            AND mime_type LIKE 'image/%'
            AND (storage_quotas.user_id IS NULL OR uploaded_by = storage_quotas.user_id)
        ), 0),
        document_quota = COALESCE((
            SELECT SUM(size) 
            FROM storage_files 
            WHERE project_id = storage_quotas.project_id 
            AND status = 'active'
            AND (mime_type LIKE 'application/%' OR mime_type LIKE 'text/%')
            AND (storage_quotas.user_id IS NULL OR uploaded_by = storage_quotas.user_id)
        ), 0),
        other_quota = COALESCE((
            SELECT SUM(size) 
            FROM storage_files 
            WHERE project_id = storage_quotas.project_id 
            AND status = 'active'
            AND mime_type NOT LIKE 'image/%' 
            AND mime_type NOT LIKE 'application/%' 
            AND mime_type NOT LIKE 'text/%'
            AND (storage_quotas.user_id IS NULL OR uploaded_by = storage_quotas.user_id)
        ), 0),
        last_updated = NOW();
        
    RAISE NOTICE '已更新所有存储配额的使用情况';
END $$; 