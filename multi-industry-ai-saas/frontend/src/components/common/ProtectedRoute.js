import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import rbacService from '../../services/rbacService';

/**
 * 基于角色的路由保护组件
 * 使用 RBAC 服务进行权限控制
 *
 * @param {Object} props - 组件属性
 * @param {React.ReactNode} props.children - 子组件
 * @param {Array|string} props.allowedRoles - 允许访问的角色列表或单个角色
 * @param {string} props.redirectPath - 重定向路径，默认为首页
 */
const ProtectedRoute = ({ children, allowedRoles, redirectPath = '/' }) => {
  const location = useLocation();
  const { user } = useAuth();

  // 获取用户数据 - 优先使用 AuthContext 中的 user 对象
  let userData = user;

  // 如果 AuthContext 中的 user 对象为空或者不包含项目角色信息，尝试从 localStorage 获取
  if (!userData || !userData.project_role) {
    try {
      userData = JSON.parse(localStorage.getItem('user_data') || '{}');
      console.log('ProtectedRoute - 从 localStorage 获取用户数据');
    } catch (error) {
      console.error('ProtectedRoute - 解析 localStorage 中的用户数据失败:', error);
      userData = {};
    }
  }

  console.log('ProtectedRoute - 当前路径:', location.pathname);
  console.log('ProtectedRoute - 所需角色:', allowedRoles);
  console.log('ProtectedRoute - 用户数据:', userData);



  // 检查用户是否有权限访问
  const hasPermission = () => {
    // 如果没有指定角色要求，则所有用户都可以访问
    if (!allowedRoles) return true;

    // 使用 RBAC 服务检查用户是否有所需角色
    return rbacService.hasRole(userData, allowedRoles);
  };

  // 检查当前路径，如果已经在项目选择页面，则不进行权限检查
  if (location.pathname === '/tenant/projects') {
    console.log('ProtectedRoute - 已经在项目选择页面，不进行权限检查');
    // 重置重定向计数器
    localStorage.setItem('redirect_attempts', '0');
    return children;
  }

  // 如果用户没有权限，重定向到指定路径
  if (!hasPermission()) {
    console.warn(`用户没有权限访问 ${location.pathname}，重定向到 ${redirectPath}`);

    // 检查是否有重定向循环
    const redirectAttempts = parseInt(localStorage.getItem('redirect_attempts') || '0');
    console.log('ProtectedRoute - 重定向尝试次数:', redirectAttempts);

    // 如果重定向尝试次数过多，直接进入项目选择页面
    if (redirectAttempts > 2) {
      console.log('ProtectedRoute - 检测到多次重定向尝试，直接进入项目选择页面');
      localStorage.setItem('redirect_attempts', '0'); // 重置计数器
      return <Navigate to="/tenant/projects" state={{ from: location }} replace />;
    }

    // 增加重定向尝试计数
    localStorage.setItem('redirect_attempts', (redirectAttempts + 1).toString());

    // 使用 RBAC 服务获取用户应该跳转的路径
    let targetPath = redirectPath;

    // 如果重定向路径是默认路径，则使用 RBAC 服务获取用户的仪表盘路径
    if (redirectPath === '/' || !redirectPath) {
      targetPath = rbacService.getFallbackPath(userData);
    }

    // 如果目标路径也是当前路径，则直接进入项目选择页面，避免循环
    if (targetPath === location.pathname) {
      console.log('ProtectedRoute - 目标路径与当前路径相同，直接进入项目选择页面');
      return <Navigate to="/tenant/projects" state={{ from: location }} replace />;
    }

    console.log('ProtectedRoute - 最终重定向路径:', targetPath);

    return <Navigate to={targetPath} state={{ from: location }} replace />;
  }

  // 重置重定向计数器
  localStorage.removeItem('redirect_attempts');

  return children;
};

export default ProtectedRoute;
