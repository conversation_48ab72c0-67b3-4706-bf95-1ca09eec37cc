# 插件市场与AI模块集成方案

## 1. 概述

本文档描述了插件市场与AI模块的集成方案，旨在创建一个统一的、可扩展的插件生态系统，同时保持现有插件市场的功能和特性。

## 2. 现状分析

### 2.1 现有插件市场

- 插件市场属于项目级别，每个项目可以安装和管理自己的插件
- 插件通过API路径 `/api/v1/project/{projectId}/plugin/插件名` 提供服务
- 后端插件位于 `retail-ai-saas/backend/plugins/` 目录中
- 每个插件有自己独立的文件夹，保持插件的独立性

### 2.2 计划中的AI模块插件系统

- AI模块计划实现一个插件架构，支持动态加载和卸载插件
- 插件可以扩展AI模块的功能，如内容审核、数据分析等
- 插件需要支持多租户隔离和安全检查
- 插件可以与第三方服务集成

## 3. 集成方案

### 3.1 统一插件架构

我们将创建一个统一的插件架构，同时支持现有的插件市场和AI模块插件：

```
Plugin (插件基础模型)
├── id: UUID
├── name: String (唯一标识符)
├── display_name: String (显示名称)
├── description: String
├── version: String
├── author: String
├── type: String (marketplace, ai, system)
├── status: String (active, inactive)
├── config_schema: JSON (配置模式)
├── default_config: JSON (默认配置)
├── permissions: JSON (所需权限)
├── created_at: DateTime
└── updated_at: DateTime

ProjectPlugin (项目插件关联)
├── id: UUID
├── project_id: UUID (外键关联Project)
├── plugin_id: UUID (外键关联Plugin)
├── config: JSON (插件配置)
├── status: String (active, inactive)
├── created_at: DateTime
└── updated_at: DateTime

TenantPlugin (租户插件关联)
├── id: UUID
├── tenant_id: UUID (外键关联Tenant)
├── plugin_id: UUID (外键关联Plugin)
├── config: JSON (插件配置)
├── status: String (active, inactive)
├── created_at: DateTime
└── updated_at: DateTime
```

### 3.2 插件类型

1. **市场插件 (marketplace)**
   - 现有的插件市场插件
   - 提供业务功能扩展，如营销游戏、客户管理等
   - 项目级别安装和配置

2. **AI插件 (ai)**
   - 扩展AI模块功能的插件
   - 提供内容审核、数据分析、行业适配等功能
   - 可以是项目级别或租户级别

3. **系统插件 (system)**
   - 系统级别的核心功能插件
   - 由系统管理员管理
   - 影响整个系统的功能

### 3.3 插件注册与发现机制

1. **统一注册中心**
   - 所有插件在同一个注册中心注册
   - 支持插件版本管理和依赖检查
   - 提供插件状态监控

2. **动态发现机制**
   - 系统启动时自动发现并加载插件
   - 支持热插拔，无需重启系统
   - 提供插件冲突检测和解决

3. **权限控制**
   - 基于角色的插件访问控制
   - 插件权限细粒度管理
   - 插件操作审计日志

### 3.4 API路由集成

1. **统一路由前缀**
   - 市场插件: `/api/v1/project/{projectId}/plugin/{pluginName}`
   - AI插件: `/api/v1/project/{projectId}/ai/plugin/{pluginName}`
   - 系统插件: `/api/v1/system/plugin/{pluginName}`

2. **路由中间件**
   - 统一的插件认证和授权中间件
   - 请求日志和监控中间件
   - 错误处理和响应格式化中间件

3. **API文档集成**
   - 自动生成插件API文档
   - 集成到系统API文档中
   - 支持API测试和调试

### 3.5 前端集成

1. **统一插件市场界面**
   - 展示所有类型的插件
   - 按类型、功能、热度等分类
   - 提供搜索和筛选功能

2. **插件配置界面**
   - 统一的插件配置界面
   - 基于插件配置模式自动生成表单
   - 支持配置验证和预览

3. **插件功能入口**
   - 在相应功能区域提供插件入口
   - 支持插件功能的无缝集成
   - 提供插件状态指示

## 4. 技术实现

### 4.1 插件加载机制

```python
# 插件加载器示例
class PluginLoader:
    def __init__(self):
        self.plugins = {}
        self.plugin_routes = {}
        
    def discover_plugins(self):
        """发现并加载所有插件"""
        # 扫描插件目录
        plugin_dirs = self._scan_plugin_directories()
        
        # 加载每个插件
        for plugin_dir in plugin_dirs:
            plugin_info = self._load_plugin_info(plugin_dir)
            if plugin_info:
                self.register_plugin(plugin_info)
    
    def register_plugin(self, plugin_info):
        """注册插件"""
        plugin_id = plugin_info.get('id')
        if plugin_id in self.plugins:
            logger.warning(f"插件 {plugin_id} 已经注册，将被覆盖")
        
        self.plugins[plugin_id] = plugin_info
        
        # 注册插件路由
        if plugin_info.get('routes'):
            self.plugin_routes[plugin_id] = plugin_info.get('routes')
            
        logger.info(f"插件 {plugin_id} 注册成功")
        
    def get_plugin(self, plugin_id):
        """获取插件信息"""
        return self.plugins.get(plugin_id)
        
    def get_plugin_routes(self, plugin_id):
        """获取插件路由"""
        return self.plugin_routes.get(plugin_id)
```

### 4.2 插件接口定义

```python
# 插件基类
class BasePlugin:
    def __init__(self, config=None):
        self.config = config or {}
        
    def initialize(self):
        """初始化插件"""
        pass
        
    def shutdown(self):
        """关闭插件"""
        pass
        
    def get_routes(self):
        """获取插件路由"""
        return []
        
    def get_info(self):
        """获取插件信息"""
        return {
            'id': self.get_id(),
            'name': self.get_name(),
            'description': self.get_description(),
            'version': self.get_version(),
            'author': self.get_author(),
            'type': self.get_type()
        }
        
    # 以下方法需要子类实现
    def get_id(self):
        raise NotImplementedError
        
    def get_name(self):
        raise NotImplementedError
        
    def get_description(self):
        raise NotImplementedError
        
    def get_version(self):
        raise NotImplementedError
        
    def get_author(self):
        raise NotImplementedError
        
    def get_type(self):
        raise NotImplementedError
```

### 4.3 AI插件示例

```python
# AI内容审核插件示例
class ContentModerationPlugin(BasePlugin):
    def get_id(self):
        return "ai-content-moderation"
        
    def get_name(self):
        return "内容审核"
        
    def get_description(self):
        return "提供AI内容审核功能，检测有害内容并进行过滤"
        
    def get_version(self):
        return "1.0.0"
        
    def get_author(self):
        return "Retail AI Team"
        
    def get_type(self):
        return "ai"
        
    def moderate_content(self, content, context=None):
        """内容审核功能"""
        # 实现内容审核逻辑
        result = {
            'is_safe': True,
            'categories': {},
            'filtered_content': content
        }
        
        # 使用AI模型进行内容审核
        # ...
        
        return result
        
    def get_routes(self):
        """获取插件路由"""
        return [
            {
                'path': '/moderate',
                'method': 'POST',
                'handler': self.handle_moderate_request
            }
        ]
        
    async def handle_moderate_request(self, request):
        """处理内容审核请求"""
        data = await request.json()
        content = data.get('content')
        context = data.get('context')
        
        result = self.moderate_content(content, context)
        
        return JSONResponse(result)
```

## 5. 迁移策略

### 5.1 现有插件迁移

1. **插件元数据更新**
   - 为现有插件添加统一的元数据格式
   - 更新插件类型为 "marketplace"
   - 添加版本信息和权限声明

2. **插件接口适配**
   - 创建适配层，使现有插件符合新的插件接口
   - 保持向后兼容，不破坏现有功能
   - 逐步迁移到新的插件架构

3. **数据迁移**
   - 创建数据迁移脚本，将现有插件数据迁移到新模型
   - 保留现有插件配置和状态
   - 验证迁移后的数据完整性

### 5.2 新插件开发指南

1. **插件开发规范**
   - 定义统一的插件开发规范
   - 提供插件模板和示例
   - 创建插件开发文档

2. **插件测试框架**
   - 提供插件单元测试框架
   - 支持插件集成测试
   - 自动化插件验证流程

3. **插件发布流程**
   - 定义插件发布和更新流程
   - 提供插件版本管理指南
   - 建立插件质量评估标准

## 6. 结论

通过统一插件架构，我们可以将现有的插件市场与计划中的AI模块插件系统无缝集成，创建一个更加强大和灵活的插件生态系统。这种集成不仅保持了现有功能的完整性，还为未来的扩展提供了坚实的基础。

统一的插件系统将为用户提供更一致的体验，同时为开发者提供更清晰的插件开发路径。通过标准化的插件接口和管理机制，我们可以鼓励更多的第三方开发者参与到插件生态系统的建设中，丰富系统的功能和应用场景。