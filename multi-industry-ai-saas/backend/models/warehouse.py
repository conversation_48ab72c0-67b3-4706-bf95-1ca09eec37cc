from sqlalchemy import Column, String, Boolean, DateTime, ForeignKey, JSON, Text, Integer, UniqueConstraint, Float
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid

from db.database import Base

class Warehouse(Base):
    """仓库模型"""
    __tablename__ = "warehouses"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id"), nullable=False)
    name = Column(String(100), nullable=False)
    code = Column(String(50), nullable=False)
    type = Column(String(20), nullable=False)  # central(总仓), regional(区域仓), distribution(配送中心), front(前置仓)
    address = Column(String(200), nullable=True)
    phone = Column(String(20), nullable=True)
    manager = Column(String(50), nullable=True)
    capacity = Column(Float, nullable=True)  # 仓库容量
    capacity_unit = Column(String(10), default="sqm", nullable=True)  # sqm(平方米), cbm(立方米)
    business_hours = Column(String(100), nullable=True)  # 营业时间，如：09:00-18:00
    status = Column(String(20), default="active", nullable=False)  # active, inactive, closed
    settings = Column(JSON, default=dict)  # 仓库设置
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 关联关系
    project = relationship("Project", backref="warehouses")
    inventory_items = relationship("InventoryItem", back_populates="warehouse")
    purchase_orders = relationship("PurchaseOrder", back_populates="warehouse")

    __table_args__ = (
        UniqueConstraint('project_id', 'code', name='uq_warehouse_code_per_project'),
    )

    def __repr__(self):
        return f"<Warehouse(id={self.id}, name='{self.name}', code='{self.code}')>"


class WarehouseSetting(Base):
    """仓库设置模型"""
    __tablename__ = "warehouse_settings"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id"), nullable=False)
    warehouse_mode = Column(String(20), default="multi", nullable=False)  # central(总仓模式), multi(多仓模式), front(前置仓模式)
    default_business_hours_start = Column(String(5), default="09:00", nullable=False)
    default_business_hours_end = Column(String(5), default="18:00", nullable=False)
    enable_front_warehouse = Column(Boolean, default=True, nullable=False)
    enable_cross_warehouse_transfer = Column(Boolean, default=True, nullable=False)
    inventory_warning_threshold = Column(Integer, default=10, nullable=False)  # 库存预警阈值(%)
    auto_replenishment = Column(Boolean, default=True, nullable=False)  # 自动补货
    default_capacity_unit = Column(String(10), default="sqm", nullable=False)  # sqm(平方米), cbm(立方米)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 关联关系
    project = relationship("Project", backref="warehouse_settings")

    def __repr__(self):
        return f"<WarehouseSetting(id={self.id}, project_id={self.project_id}, mode='{self.warehouse_mode}')>"


class WarehouseInbound(Base):
    """仓库入库单模型"""
    __tablename__ = "warehouse_inbounds"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), nullable=True)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id"), nullable=False)
    warehouse_id = Column(UUID(as_uuid=True), ForeignKey("warehouses.id"), nullable=False)
    inbound_number = Column(String(50), nullable=False)
    purchase_order_id = Column(UUID(as_uuid=True), ForeignKey("purchase_orders.id"), nullable=True)
    purchase_order_number = Column(String(50), nullable=True)
    inbound_date = Column(String(10), nullable=False)
    status = Column(String(20), nullable=False, default="pending")  # pending(待入库), confirmed(已入库), cancelled(已取消)
    total_amount = Column(Float, nullable=False, default=0.0)
    item_count = Column(Integer, nullable=False, default=0)
    notes = Column(Text, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    created_by = Column(UUID(as_uuid=True), nullable=True)
    updated_by = Column(UUID(as_uuid=True), nullable=True)

    # 关联关系
    project = relationship("Project", backref="warehouse_inbounds")
    warehouse = relationship("Warehouse", backref="inbounds")
    purchase_order = relationship("PurchaseOrder", backref="inbounds")
    items = relationship("WarehouseInboundItem", back_populates="inbound", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<WarehouseInbound(id={self.id}, inbound_number='{self.inbound_number}', status='{self.status}')>"


class WarehouseInboundItem(Base):
    """仓库入库单项模型"""
    __tablename__ = "warehouse_inbound_items"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    tenant_id = Column(UUID(as_uuid=True), nullable=True)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id"), nullable=False)
    inbound_id = Column(UUID(as_uuid=True), ForeignKey("warehouse_inbounds.id"), nullable=False)
    purchase_order_item_id = Column(UUID(as_uuid=True), ForeignKey("purchase_order_items.id"), nullable=True)
    product_id = Column(UUID(as_uuid=True), ForeignKey("products.id"), nullable=True)
    product_name = Column(String(100), nullable=False)
    product_code = Column(String(50), nullable=True)
    product_specification = Column(String(100), nullable=True)
    product_unit = Column(String(20), nullable=True)
    quantity = Column(Float, nullable=False)
    unit_price = Column(Float, nullable=False)
    total_amount = Column(Float, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 关联关系
    project = relationship("Project", backref="warehouse_inbound_items")
    inbound = relationship("WarehouseInbound", back_populates="items")
    purchase_order_item = relationship("PurchaseOrderItem", backref="inbound_items")
    product = relationship("Product", backref="inbound_items")

    def __repr__(self):
        return f"<WarehouseInboundItem(id={self.id}, product_name='{self.product_name}', quantity={self.quantity})>"


class ProjectUserWarehouse(Base):
    """项目用户仓库关联模型"""
    __tablename__ = "project_user_warehouses"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    project_user_id = Column(UUID(as_uuid=True), ForeignKey("project_users.id"), nullable=False)
    warehouse_id = Column(UUID(as_uuid=True), ForeignKey("warehouses.id"), nullable=False)
    is_manager = Column(Boolean, default=False, nullable=False)  # 是否为仓库负责人
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())

    # 关联关系
    project_user = relationship("ProjectUser", backref="warehouse_associations")
    warehouse = relationship("Warehouse", backref="user_associations")

    __table_args__ = (
        UniqueConstraint('project_user_id', 'warehouse_id', name='uq_project_user_warehouse'),
    )

    def __repr__(self):
        return f"<ProjectUserWarehouse(id={self.id}, project_user_id={self.project_user_id}, warehouse_id={self.warehouse_id}, is_manager={self.is_manager})>"
