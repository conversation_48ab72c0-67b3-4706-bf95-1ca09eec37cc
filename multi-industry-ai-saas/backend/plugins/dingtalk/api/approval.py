"""
钉钉审批流程API
基于钉钉开放平台OA审批流程中心接口
"""
from fastapi import APIRouter, HTTPException, Depends, Query, Body, Request
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional, Dict, Any
import json
import logging
import uuid
from datetime import datetime, timedelta
from sqlalchemy import select, desc, and_

from api.deps import get_db, get_current_project_id, get_current_user, get_current_tenant
from models.user import User
from models.tenant import Tenant

# 使用相对导入从同级目录导入模型
from ..models import DingTalkApprovalInstance, DingTalkApprovalTemplate
from ..services.approval_service import ApprovalService
from ..utils.dingtalk_api import DingTalkAPI
from ..services.business_integration import (
    integrate_store_loss, 
    integrate_sales_report, 
    integrate_expense_reimbursement
)

logger = logging.getLogger(__name__)
router = APIRouter()

@router.post("/templates")
async def create_approval_template(
    template_data: Dict[str, Any],
    project_id: str = Depends(get_current_project_id),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: AsyncSession = Depends(get_db)
):
    """
    创建钉钉审批模板
    基于钉钉官方文档实现：后端创建模板，用户在钉钉客户端发起审批
    """
    try:
        # 获取钉钉配置
        from ..services.unified_settings_service import UnifiedSettingsService
        settings_service = UnifiedSettingsService(db, str(project.tenant_id), project_id)
        settings = await settings_service.get_settings()
        
        if not settings.get('app_key') or not settings.get('app_secret'):
            raise HTTPException(status_code=400, detail="请先配置钉钉应用密钥")
        
        # 初始化钉钉API服务
        from ..services.dingtalk_api_service import DingTalkAPIService
        dingtalk_api = DingTalkAPIService(
            app_key=settings['app_key'],
            app_secret=settings['app_secret']
        )
        
        # 初始化审批服务  
        approval_service = ApprovalService(db, str(current_tenant.id), project_id, dingtalk_api)
        
        result = await approval_service.create_approval_template(template_data)
        return result
        
    except Exception as e:
        logger.error(f"创建审批模板失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/sync-instances")
async def sync_approval_instances(
    request: Request,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """同步钉钉审批实例状态"""
    try:
        project_id = request.path_params.get("project_id")
        
        # 获取钉钉配置
        dingtalk_config = await get_dingtalk_config(db, project_id)
        if not dingtalk_config:
            return {"success": False, "message": "钉钉配置不存在"}
        
        # 创建钉钉API实例
        dingtalk_api = DingTalkAPI(
            app_key=dingtalk_config.app_key,
            app_secret=dingtalk_config.app_secret
        )
        
        # 获取需要同步的实例（状态为NEW或RUNNING的）
        query = select(DingTalkApprovalInstance).where(
            and_(
                DingTalkApprovalInstance.project_id == project_id,
                DingTalkApprovalInstance.status.in_(['NEW', 'RUNNING'])
            )
        )
        result = await db.execute(query)
        instances = result.scalars().all()
        
        synced_count = 0
        
        for instance in instances:
            try:
                # 从钉钉获取最新状态
                dingtalk_detail = await dingtalk_api.get_approval_instance_detail(
                    instance.process_instance_id
                )
                
                if dingtalk_detail:
                    # 更新本地状态
                    old_status = instance.status
                    instance.status = dingtalk_detail.get('status', instance.status)
                    
                    # 如果状态发生变化，记录审批时间
                    if old_status != instance.status and instance.status in ['COMPLETED', 'TERMINATED']:
                        instance.approved_at = datetime.now()
                    
                    # 更新表单数据（如果有变化）
                    if dingtalk_detail.get('form_component_values'):
                        instance.form_component_values = dingtalk_detail['form_component_values']
                    
                    await db.commit()
                    synced_count += 1
                    
                    logger.info(f"同步审批实例 {instance.id} 状态: {old_status} -> {instance.status}")
                    
            except Exception as e:
                logger.error(f"同步审批实例 {instance.id} 失败: {str(e)}")
                continue
        
        return {
            "success": True,
            "data": {
                "synced_count": synced_count,
                "total_count": len(instances)
            },
            "message": f"同步完成，更新了 {synced_count} 个审批实例"
        }
        
    except Exception as e:
        logger.error(f"同步审批实例状态失败: {str(e)}")
        return {
            "success": False,
            "message": f"同步审批实例状态失败: {str(e)}"
        }

@router.post("/callback")
async def handle_approval_callback(
    callback_data: Dict[str, Any],
    project_id: str = Depends(get_current_project_id),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: AsyncSession = Depends(get_db)
):
    """
    处理钉钉审批回调
    当用户在钉钉客户端完成审批操作时，钉钉会回调此接口
    """
    try:
        # 初始化服务
        approval_service = ApprovalService(db, str(current_tenant.id), project_id, None)
        
        result = await approval_service.handle_approval_callback(callback_data)
        return result
        
    except Exception as e:
        logger.error(f"处理审批回调失败: {str(e)}")
        # 回调处理失败不应该抛出异常，以免影响钉钉回调
        return {"success": False, "message": str(e)}

@router.get("/templates/list")
async def get_approval_templates_new(
    project_id: str = Depends(get_current_project_id),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: AsyncSession = Depends(get_db)
):
    """获取本地审批模板列表"""
    try:
        approval_service = ApprovalService(db, str(current_tenant.id), project_id, None)
        result = await approval_service.get_templates()
        return result
        
    except Exception as e:
        logger.error(f"获取审批模板列表失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/instances/list")
async def get_approval_instances_list(
    request: Request,
    db: AsyncSession = Depends(get_db),
    store_id: Optional[int] = Query(None, description="门店ID"),
    template_type: Optional[str] = Query(None, description="模板类型"),
    status: Optional[str] = Query(None, description="审批状态"),
    start_date: Optional[str] = Query(None, description="开始日期"),
    end_date: Optional[str] = Query(None, description="结束日期"),
    current_user: User = Depends(get_current_user)
):
    """获取审批实例列表 - 支持业务筛选"""
    try:
        project_id = request.path_params.get("project_id")
        
        # 基础查询
        query = select(DingTalkApprovalInstance).where(
            DingTalkApprovalInstance.project_id == project_id
        )
        
        # 添加筛选条件
        if template_type:
            query = query.where(DingTalkApprovalInstance.template_type == template_type)
        
        if status:
            query = query.where(DingTalkApprovalInstance.status == status)
        
        if start_date:
            start_datetime = datetime.strptime(start_date, '%Y-%m-%d')
            query = query.where(DingTalkApprovalInstance.created_at >= start_datetime)
        
        if end_date:
            end_datetime = datetime.strptime(end_date, '%Y-%m-%d') + timedelta(days=1)
            query = query.where(DingTalkApprovalInstance.created_at < end_datetime)
        
        # 门店筛选（通过表单数据）
        if store_id:
            store_result = await db.execute(select(Store).where(Store.id == store_id))
            store = store_result.scalar_one_or_none()
            if store:
                # 通过表单数据中的门店名称筛选
                query = query.where(
                    DingTalkApprovalInstance.form_component_values.contains(
                        [{"name": "store_name", "value": store.name}]
                    )
                )
        
        # 按创建时间倒序
        query = query.order_by(desc(DingTalkApprovalInstance.created_at))
        
        result = await db.execute(query)
        instances = result.scalars().all()
        
        # 转换为字典格式
        instances_data = []
        for instance in instances:
            instance_dict = {
                "id": instance.id,
                "process_instance_id": instance.process_instance_id,
                "template_type": instance.template_type,
                "title": instance.title,
                "status": instance.status,
                "form_component_values": instance.form_component_values,
                "originator_user_id": instance.originator_user_id,
                "originator_user_name": instance.originator_user_name,
                "created_at": instance.created_at.isoformat() if instance.created_at else None,
                "approved_at": instance.approved_at.isoformat() if instance.approved_at else None,
                "dingtalk_url": instance.dingtalk_url
            }
            instances_data.append(instance_dict)
        
        return {
            "success": True,
            "data": {
                "instances": instances_data,
                "total": len(instances_data)
            }
        }
        
    except Exception as e:
        logger.error(f"获取审批实例列表失败: {str(e)}")
        return {
            "success": False,
            "message": f"获取审批实例列表失败: {str(e)}"
        }

@router.get("/types")
async def get_approval_types():
    """获取支持的审批类型"""
    try:
        approval_types = [
            {
                "key": "loss_approval", 
                "name": "产品报损",
                "description": "用于门店商品报损申请审批，对接采购管理系统的报损功能",
                "fields": ["product_name", "product_code", "quantity", "unit_price", "loss_reason", "loss_date", "store_name", "total_amount", "attachments"],
                "priority": "high"
            },
            {
                "key": "procurement_request",
                "name": "采购申请",
                "description": "物资设备采购申请流程，对接采购管理系统",
                "fields": ["item_name", "quantity", "estimated_cost", "supplier", "urgency_level", "justification", "warehouse_id"],
                "priority": "high"
            },
            {
                "key": "sales_report",
                "name": "销售业绩上报",
                "description": "针对门店管理，销售人员业绩数据上报审批",
                "fields": ["report_period", "store_name", "sales_amount", "customer_count", "performance_summary", "supporting_docs"],
                "priority": "medium"
            },
            {
                "key": "expense_reimbursement",
                "name": "费用报销",
                "description": "参考日常店务，日常费用报销申请",
                "fields": ["expense_type", "store_name", "amount", "expense_date", "business_purpose", "receipts"],
                "priority": "medium"
            },
            {
                "key": "leave_request",
                "name": "请假申请",
                "description": "员工请假申请流程",
                "fields": ["leave_type", "start_date", "end_date", "leave_days", "reason", "emergency_contact"],
                "priority": "low"
            }
        ]
        
        return {
            "success": True,
            "data": {
                "types": approval_types
            }
        }
        
    except Exception as e:
        logger.error(f"获取审批类型失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取审批类型失败: {str(e)}")

@router.get("/status")
async def get_approval_status():
    """获取审批系统状态"""
    try:
        return {
            "success": True,
            "data": {
                "message": "钉钉审批系统已启动",
                "version": "2.0",
                "features": [
                    "审批模板创建",
                    "审批实例同步", 
                    "审批回调处理",
                    "业务逻辑触发"
                ]
            }
        }
        
    except Exception as e:
        logger.error(f"获取审批状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/discover-business")
async def discover_pending_business_approvals(
    project_id: str = Depends(get_current_project_id),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: AsyncSession = Depends(get_db)
):
    """发现项目中待审批的业务数据"""
    try:
        approval_service = ApprovalService(db, str(current_tenant.id), project_id, None)
        return await approval_service.discover_pending_approvals()
    except Exception as e:
        logger.error(f"发现待审批业务失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/auto-sync")
async def auto_sync_discovered_approvals(
    project_id: str = Depends(get_current_project_id),
    sync_options: Optional[Dict[str, Any]] = Body(None),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: AsyncSession = Depends(get_db)
):
    """自动同步发现的待审批业务到钉钉"""
    try:
        dingtalk_api = await get_dingtalk_api(db, project_id)
        approval_service = ApprovalService(db, str(current_tenant.id), project_id, dingtalk_api)
        return await approval_service.auto_sync_discovered_approvals(sync_options)
    except Exception as e:
        logger.error(f"自动同步审批失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.post("/sync-business")
async def sync_specific_business_approval(
    project_id: str = Depends(get_current_project_id),
    business_data: Dict[str, Any] = Body(...),
    current_tenant: Tenant = Depends(get_current_tenant),
    db: AsyncSession = Depends(get_db)
):
    """同步指定的业务数据到钉钉审批"""
    try:
        dingtalk_api = await get_dingtalk_api(db, project_id)
        
        # 调用专门的同步服务
        from ..utils.approval_sync import ApprovalSyncService
        sync_service = ApprovalSyncService(dingtalk_api, db)
        
        return await sync_service.create_and_sync_approval(
            tenant_id=current_tenant.id,
            project_id=project_id,
            business_type=business_data["business_type"],
            business_data=business_data.get("details", {}),
            originator_user_id=business_data.get("originator_user_id", uuid.uuid4())
        )
    except Exception as e:
        logger.error(f"同步业务审批失败: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@router.get("/business-stats")
async def get_business_approval_stats(
    request: Request,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """获取业务审批统计数据"""
    try:
        project_id = request.path_params.get("project_id")
        
        # 查询所有审批实例
        query = select(DingTalkApprovalInstance).where(
            DingTalkApprovalInstance.project_id == project_id
        )
        result = await db.execute(query)
        instances = result.scalars().all()
        
        # 统计数据
        stats = {
            "total": len(instances),
            "pending": len([i for i in instances if i.status in ['NEW', 'RUNNING']]),
            "approved": len([i for i in instances if i.status == 'COMPLETED']),
            "rejected": len([i for i in instances if i.status == 'TERMINATED'])
        }
        
        # 按类型统计
        type_stats = {}
        for instance in instances:
            template_type = instance.template_type
            if template_type not in type_stats:
                type_stats[template_type] = {
                    "total": 0,
                    "pending": 0,
                    "approved": 0,
                    "rejected": 0
                }
            
            type_stats[template_type]["total"] += 1
            if instance.status in ['NEW', 'RUNNING']:
                type_stats[template_type]["pending"] += 1
            elif instance.status == 'COMPLETED':
                type_stats[template_type]["approved"] += 1
            elif instance.status == 'TERMINATED':
                type_stats[template_type]["rejected"] += 1
        
        # 按门店统计
        store_stats = {}
        for instance in instances:
            store_name = None
            if instance.form_component_values:
                for field in instance.form_component_values:
                    if field.get("name") == "store_name":
                        store_name = field.get("value")
                        break
            
            if store_name:
                if store_name not in store_stats:
                    store_stats[store_name] = {
                        "total": 0,
                        "pending": 0,
                        "approved": 0,
                        "rejected": 0
                    }
                
                store_stats[store_name]["total"] += 1
                if instance.status in ['NEW', 'RUNNING']:
                    store_stats[store_name]["pending"] += 1
                elif instance.status == 'COMPLETED':
                    store_stats[store_name]["approved"] += 1
                elif instance.status == 'TERMINATED':
                    store_stats[store_name]["rejected"] += 1
        
        return {
            "success": True,
            "data": {
                **stats,
                "type_stats": type_stats,
                "store_stats": store_stats
            }
        }
        
    except Exception as e:
        logger.error(f"获取业务审批统计失败: {str(e)}")
        return {
            "success": False,
            "message": f"获取业务审批统计失败: {str(e)}"
        }

@router.post("/business-integration")
async def handle_business_integration(
    request: Request,
    data: Dict[str, Any],
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """处理业务系统集成 - 审批通过后的数据同步"""
    try:
        project_id = request.path_params.get("project_id")
        instance_id = data.get("instance_id")
        action = data.get("action", "sync")  # sync, rollback
        
        if not instance_id:
            return {"success": False, "message": "实例ID不能为空"}
        
        # 获取审批实例
        query = select(DingTalkApprovalInstance).where(
            and_(
                DingTalkApprovalInstance.id == instance_id,
                DingTalkApprovalInstance.project_id == project_id
            )
        )
        result = await db.execute(query)
        instance = result.scalar_one_or_none()
        
        if not instance:
            return {"success": False, "message": "审批实例不存在"}
        
        # 只处理已通过的审批
        if instance.status != 'COMPLETED':
            return {"success": False, "message": "只能同步已通过的审批"}
        
        # 根据审批类型进行业务集成
        integration_result = None
        
        if instance.template_type == 'store_loss':
            integration_result = await integrate_store_loss(db, project_id, instance, action)
        elif instance.template_type == 'sales_report':
            integration_result = await integrate_sales_report(db, project_id, instance, action)
        elif instance.template_type == 'expense_reimbursement':
            integration_result = await integrate_expense_reimbursement(db, project_id, instance, action)
        else:
            return {"success": False, "message": f"不支持的审批类型: {instance.template_type}"}
        
        if integration_result and integration_result.get("success"):
            # 更新集成状态
            instance.is_integrated = True
            instance.integrated_at = datetime.now()
            await db.commit()
            
            return {
                "success": True,
                "data": integration_result.get("data"),
                "message": "业务集成成功"
            }
        else:
            return {
                "success": False,
                "message": integration_result.get("message", "业务集成失败")
            }
            
    except Exception as e:
        logger.error(f"处理业务集成失败: {str(e)}")
        return {
            "success": False,
            "message": f"处理业务集成失败: {str(e)}"
        }

async def get_dingtalk_config(db: AsyncSession, project_id: str):
    """获取钉钉配置（已废弃，使用统一设置服务）"""
    # 获取tenant_id
    from models.project import Project
    project_query = select(Project).where(Project.id == project_id)
    result = await db.execute(project_query)
    project = result.scalar_one_or_none()
    
    if not project:
        return None
    
    # 使用统一设置服务
    from ..services.unified_settings_service import UnifiedSettingsService
    settings_service = UnifiedSettingsService(db, str(project.tenant_id), str(project_id))
    config = await settings_service.get_config_for_api()
    
    # 返回兼容的对象格式
    class ConfigCompat:
        def __init__(self, config_dict):
            self.app_key = config_dict.get("app_key", "")
            self.app_secret = config_dict.get("app_secret", "")
            self.corp_id = config_dict.get("corp_id", "")
            self.webhook_url = config_dict.get("webhook_url", "")
            self.has_config = config_dict.get("has_config", False)
    
    return ConfigCompat(config) if config.get("has_config") else None 