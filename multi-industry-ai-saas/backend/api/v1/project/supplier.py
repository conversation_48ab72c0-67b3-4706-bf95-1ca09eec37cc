#!/usr/bin/env python
# -*- coding: utf-8 -*-

from fastapi import APIRouter, Depends, HTTPException, status, Query, Path, UploadFile, File, Form
from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_, select
from typing import List, Optional, Dict, Any
from datetime import datetime
import uuid
import os
import shutil
import pandas as pd
import io

from db.database import get_db
from core.auth import get_current_user
from api.deps import get_current_project
from models.user import User
from models.supplier import Supplier, SupplierProduct, SupplierEvaluation
from models.product import Product
from schemas.supplier import (
    SupplierCreate, SupplierUpdate, SupplierResponse, SupplierListResponse,
    SupplierProductCreate, SupplierProductUpdate, SupplierProductResponse, SupplierProductListResponse,
    SupplierEvaluationCreate, SupplierEvaluationUpdate, SupplierEvaluationResponse, SupplierEvaluationListResponse
)

router = APIRouter()

@router.get("", response_model=SupplierListResponse)
async def get_suppliers(
    project_id: uuid.UUID = Path(...),
    page: int = Query(1, ge=1),
    page_size: int = Query(10, ge=1, le=100),
    search: Optional[str] = None,
    is_active: Optional[bool] = None,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取供应商列表"""
    try:
        offset = (page - 1) * page_size
        stmt = select(Supplier).where(Supplier.project_id == project_id)
        if search:
            stmt = stmt.where(
                or_(
                    Supplier.name.ilike(f"%{search}%"),
                    Supplier.contact_person.ilike(f"%{search}%"),
                    Supplier.contact_phone.ilike(f"%{search}%")
                )
            )
        if is_active is not None:
            stmt = stmt.where(Supplier.is_active == is_active)
        count_stmt = select(func.count()).select_from(stmt.subquery())
        total = (await db.execute(count_stmt)).scalar()
        stmt = stmt.order_by(Supplier.created_at.desc()).offset(offset).limit(page_size)
        result = await db.execute(stmt)
        suppliers = result.scalars().all()
        return {
            "success": True,
            "data": suppliers,
            "total": total,
            "page": page,
            "page_size": page_size,
            "pages": (total + page_size - 1) // page_size
        }
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取供应商列表失败: {str(e)}"
        )

@router.get("/{supplier_id}", response_model=SupplierResponse)
async def get_supplier(
    project_id: uuid.UUID = Path(...),
    supplier_id: uuid.UUID = Path(...),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取供应商详情"""
    try:
        # 查询供应商
        supplier = await db.execute(select(Supplier).where(
            Supplier.id == supplier_id,
            Supplier.project_id == project_id
        ).limit(1))
        supplier = supplier.scalars().first()

        if not supplier:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="供应商不存在"
            )

        return {
            "success": True,
            "data": supplier
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取供应商详情失败: {str(e)}"
        )

@router.post("", response_model=SupplierResponse, status_code=status.HTTP_201_CREATED)
async def create_supplier(
    supplier: SupplierCreate,
    project_id: uuid.UUID = Path(...),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """创建供应商"""
    try:
        # 检查供应商名称是否已存在
        existing_supplier = await db.execute(select(Supplier).where(
            Supplier.project_id == project_id,
            Supplier.name == supplier.name
        ).limit(1))
        existing_supplier = existing_supplier.scalars().first()

        if existing_supplier:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="供应商名称已存在"
            )

        # 创建供应商
        new_supplier = Supplier(
            id=uuid.uuid4(),
            tenant_id=current_user.tenant_id,
            project_id=project_id,
            name=supplier.name,
            contact_person=supplier.contact_person,
            contact_phone=supplier.contact_phone,
            email=supplier.email,
            address=supplier.address,
            description=supplier.description,
            is_active=supplier.is_active,
            created_by=current_user.id,
            updated_by=current_user.id
        )

        db.add(new_supplier)
        await db.commit()
        await db.refresh(new_supplier)

        return {
            "success": True,
            "message": "供应商创建成功",
            "data": new_supplier
        }
    except HTTPException:
        await db.rollback()
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建供应商失败: {str(e)}"
        )

@router.put("/{supplier_id}", response_model=SupplierResponse)
async def update_supplier(
    supplier: SupplierUpdate,
    project_id: uuid.UUID = Path(...),
    supplier_id: uuid.UUID = Path(...),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """更新供应商"""
    try:
        # 查询供应商
        db_supplier = await db.execute(select(Supplier).where(
            Supplier.id == supplier_id,
            Supplier.project_id == project_id
        ).limit(1))
        db_supplier = db_supplier.scalars().first()

        if not db_supplier:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="供应商不存在"
            )

        # 如果更新名称，检查是否与其他供应商冲突
        if supplier.name and supplier.name != db_supplier.name:
            existing_supplier = await db.execute(select(Supplier).where(
                Supplier.project_id == project_id,
                Supplier.name == supplier.name,
                Supplier.id != supplier_id
            ).limit(1))
            existing_supplier = existing_supplier.scalars().first()

            if existing_supplier:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="供应商名称已存在"
                )

        # 更新供应商
        update_data = supplier.dict(exclude_unset=True)
        update_data["updated_by"] = current_user.id
        update_data["updated_at"] = datetime.now()

        for key, value in update_data.items():
            setattr(db_supplier, key, value)

        await db.commit()
        await db.refresh(db_supplier)

        return {
            "success": True,
            "message": "供应商更新成功",
            "data": db_supplier
        }
    except HTTPException:
        await db.rollback()
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新供应商失败: {str(e)}"
        )

@router.delete("/{supplier_id}", response_model=Dict[str, Any])
async def delete_supplier(
    project_id: uuid.UUID = Path(...),
    supplier_id: uuid.UUID = Path(...),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """删除供应商"""
    try:
        # 查询供应商
        db_supplier = await db.execute(select(Supplier).where(
            Supplier.id == supplier_id,
            Supplier.project_id == project_id
        ).limit(1))
        db_supplier = db_supplier.scalars().first()

        if not db_supplier:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="供应商不存在"
            )

        # 检查供应商是否有关联的产品
        supplier_products = await db.execute(select(func.count()).select_from(select(SupplierProduct).where(
            SupplierProduct.supplier_id == supplier_id
        )))
        supplier_products = supplier_products.scalars().first()

        # 检查供应商是否有关联的采购订单
        purchase_orders = await db.execute(select(func.count()).select_from(select(func.text("purchase_orders")).where(
            select(func.text("supplier_id = :supplier_id")).params(supplier_id=supplier_id)
        )))
        purchase_orders = purchase_orders.scalars().first()

        if supplier_products > 0 or purchase_orders > 0:
            # 软删除，将供应商标记为非活动状态
            db_supplier.is_active = False
            db_supplier.updated_by = current_user.id
            db_supplier.updated_at = datetime.now()
            await db.commit()

            return {
                "success": True,
                "message": "供应商已标记为非活动状态，因为存在关联的产品或订单"
            }
        else:
            # 硬删除
            await db.delete(db_supplier)
            await db.commit()

            return {
                "success": True,
                "message": "供应商删除成功"
            }
    except HTTPException:
        await db.rollback()
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除供应商失败: {str(e)}"
        )

@router.get("/{supplier_id}/products", response_model=SupplierProductListResponse)
async def get_supplier_products(
    project_id: uuid.UUID = Path(...),
    supplier_id: uuid.UUID = Path(...),
    page: int = Query(1, ge=1),
    page_size: int = Query(10, ge=1, le=100),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """获取供应商产品列表"""
    try:
        # 检查供应商是否存在
        supplier = await db.execute(select(Supplier).where(
            Supplier.id == supplier_id,
            Supplier.project_id == project_id
        ).limit(1))
        supplier = supplier.scalars().first()

        if not supplier:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="供应商不存在"
            )

        # 计算分页偏移量
        offset = (page - 1) * page_size

        # 构建查询条件
        stmt = select(
            SupplierProduct,
            Product.name.label("product_name"),
            Product.sku.label("product_sku")
        ).join(
            Product, SupplierProduct.product_id == Product.id
        ).where(
            SupplierProduct.supplier_id == supplier_id,
            SupplierProduct.project_id == project_id
        )

        # 获取总数
        count_stmt = select(func.count()).select_from(stmt.subquery())
        total = (await db.execute(count_stmt)).scalar()

        # 获取分页数据
        stmt = stmt.order_by(SupplierProduct.created_at.desc()).offset(offset).limit(page_size)
        result = await db.execute(stmt)
        supplier_products_data = result.scalars().all()

        # 转换为响应格式
        supplier_products = []
        for sp, product_name, product_sku in supplier_products_data:
            sp_dict = {
                "id": sp.id,
                "tenant_id": sp.tenant_id,
                "project_id": sp.project_id,
                "supplier_id": sp.supplier_id,
                "product_id": sp.product_id,
                "supplier_product_code": sp.supplier_product_code,
                "price": sp.price,
                "min_order_quantity": sp.min_order_quantity,
                "lead_time_days": sp.lead_time_days,
                "is_preferred": sp.is_preferred,
                "created_at": sp.created_at,
                "updated_at": sp.updated_at,
                "created_by": sp.created_by,
                "updated_by": sp.updated_by,
                "product_name": product_name,
                "product_sku": product_sku,
                "supplier_name": supplier.name
            }
            supplier_products.append(sp_dict)

        return {
            "success": True,
            "data": supplier_products,
            "total": total,
            "page": page,
            "page_size": page_size,
            "pages": (total + page_size - 1) // page_size
        }
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取供应商产品列表失败: {str(e)}"
        )

@router.post("/{supplier_id}/products", response_model=SupplierProductResponse, status_code=status.HTTP_201_CREATED)
async def create_supplier_product(
    supplier_product: SupplierProductCreate,
    project_id: uuid.UUID = Path(...),
    supplier_id: uuid.UUID = Path(...),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """创建供应商产品关联"""
    try:
        # 检查供应商是否存在
        supplier = await db.execute(select(Supplier).where(
            Supplier.id == supplier_id,
            Supplier.project_id == project_id
        ).limit(1))
        supplier = supplier.scalars().first()

        if not supplier:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="供应商不存在"
            )

        # 检查产品是否存在
        product = await db.execute(select(Product).where(
            Product.id == supplier_product.product_id,
            Product.project_id == project_id
        ).limit(1))
        product = product.scalars().first()

        if not product:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="产品不存在"
            )

        # 检查关联是否已存在
        existing_relation = await db.execute(select(SupplierProduct).where(
            SupplierProduct.supplier_id == supplier_id,
            SupplierProduct.product_id == supplier_product.product_id,
            SupplierProduct.project_id == project_id
        ).limit(1))
        existing_relation = existing_relation.scalars().first()

        if existing_relation:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="供应商产品关联已存在"
            )

        # 创建供应商产品关联
        new_supplier_product = SupplierProduct(
            id=uuid.uuid4(),
            tenant_id=current_user.tenant_id,
            project_id=project_id,
            supplier_id=supplier_id,
            product_id=supplier_product.product_id,
            supplier_product_code=supplier_product.supplier_product_code,
            price=supplier_product.price,
            min_order_quantity=supplier_product.min_order_quantity,
            lead_time_days=supplier_product.lead_time_days,
            is_preferred=supplier_product.is_preferred,
            created_by=current_user.id,
            updated_by=current_user.id
        )

        db.add(new_supplier_product)
        await db.commit()
        await db.refresh(new_supplier_product)

        # 转换为响应格式
        supplier_product_dict = {
            "id": new_supplier_product.id,
            "tenant_id": new_supplier_product.tenant_id,
            "project_id": new_supplier_product.project_id,
            "supplier_id": new_supplier_product.supplier_id,
            "product_id": new_supplier_product.product_id,
            "supplier_product_code": new_supplier_product.supplier_product_code,
            "price": new_supplier_product.price,
            "min_order_quantity": new_supplier_product.min_order_quantity,
            "lead_time_days": new_supplier_product.lead_time_days,
            "is_preferred": new_supplier_product.is_preferred,
            "created_at": new_supplier_product.created_at,
            "updated_at": new_supplier_product.updated_at,
            "created_by": new_supplier_product.created_by,
            "updated_by": new_supplier_product.updated_by,
            "product_name": product.name,
            "product_sku": product.sku,
            "supplier_name": supplier.name
        }

        return {
            "success": True,
            "message": "供应商产品关联创建成功",
            "data": supplier_product_dict
        }
    except HTTPException:
        await db.rollback()
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建供应商产品关联失败: {str(e)}"
        )

@router.put("/{supplier_id}/products/{supplier_product_id}", response_model=SupplierProductResponse)
async def update_supplier_product(
    supplier_product: SupplierProductUpdate,
    project_id: uuid.UUID = Path(...),
    supplier_id: uuid.UUID = Path(...),
    supplier_product_id: uuid.UUID = Path(...),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """更新供应商产品关联"""
    try:
        # 查询供应商产品关联
        db_supplier_product = await db.execute(select(SupplierProduct).where(
            SupplierProduct.id == supplier_product_id,
            SupplierProduct.supplier_id == supplier_id,
            SupplierProduct.project_id == project_id
        ).limit(1))
        db_supplier_product = db_supplier_product.scalars().first()

        if not db_supplier_product:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="供应商产品关联不存在"
            )

        # 如果更新产品，检查产品是否存在
        if supplier_product.product_id and supplier_product.product_id != db_supplier_product.product_id:
            product = await db.execute(select(Product).where(
                Product.id == supplier_product.product_id,
                Product.project_id == project_id
            ).limit(1))
            product = product.scalars().first()

            if not product:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="产品不存在"
                )

            # 检查新的关联是否已存在
            existing_relation = await db.execute(select(SupplierProduct).where(
                SupplierProduct.supplier_id == supplier_id,
                SupplierProduct.product_id == supplier_product.product_id,
                SupplierProduct.project_id == project_id,
                SupplierProduct.id != supplier_product_id
            ).limit(1))
            existing_relation = existing_relation.scalars().first()

            if existing_relation:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="供应商产品关联已存在"
                )

        # 更新供应商产品关联
        update_data = supplier_product.dict(exclude_unset=True)
        update_data["updated_by"] = current_user.id
        update_data["updated_at"] = datetime.now()

        for key, value in update_data.items():
            setattr(db_supplier_product, key, value)

        await db.commit()
        await db.refresh(db_supplier_product)

        # 查询产品和供应商信息
        product = await db.execute(select(Product).where(Product.id == db_supplier_product.product_id).limit(1))
        product = product.scalars().first()
        supplier = await db.execute(select(Supplier).where(Supplier.id == db_supplier_product.supplier_id).limit(1))
        supplier = supplier.scalars().first()

        # 转换为响应格式
        supplier_product_dict = {
            "id": db_supplier_product.id,
            "tenant_id": db_supplier_product.tenant_id,
            "project_id": db_supplier_product.project_id,
            "supplier_id": db_supplier_product.supplier_id,
            "product_id": db_supplier_product.product_id,
            "supplier_product_code": db_supplier_product.supplier_product_code,
            "price": db_supplier_product.price,
            "min_order_quantity": db_supplier_product.min_order_quantity,
            "lead_time_days": db_supplier_product.lead_time_days,
            "is_preferred": db_supplier_product.is_preferred,
            "created_at": db_supplier_product.created_at,
            "updated_at": db_supplier_product.updated_at,
            "created_by": db_supplier_product.created_by,
            "updated_by": db_supplier_product.updated_by,
            "product_name": product.name if product else None,
            "product_sku": product.sku if product else None,
            "supplier_name": supplier.name if supplier else None
        }

        return {
            "success": True,
            "message": "供应商产品关联更新成功",
            "data": supplier_product_dict
        }
    except HTTPException:
        await db.rollback()
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新供应商产品关联失败: {str(e)}"
        )

@router.delete("/{supplier_id}/products/{supplier_product_id}", response_model=Dict[str, Any])
async def delete_supplier_product(
    project_id: uuid.UUID = Path(...),
    supplier_id: uuid.UUID = Path(...),
    supplier_product_id: uuid.UUID = Path(...),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """删除供应商产品关联"""
    try:
        # 查询供应商产品关联
        db_supplier_product = await db.execute(select(SupplierProduct).where(
            SupplierProduct.id == supplier_product_id,
            SupplierProduct.supplier_id == supplier_id,
            SupplierProduct.project_id == project_id
        ).limit(1))
        db_supplier_product = db_supplier_product.scalars().first()

        if not db_supplier_product:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="供应商产品关联不存在"
            )

        # 删除供应商产品关联
        await db.delete(db_supplier_product)
        await db.commit()

        return {
            "success": True,
            "message": "供应商产品关联删除成功"
        }
    except HTTPException:
        await db.rollback()
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除供应商产品关联失败: {str(e)}"
        )

@router.post("/batch-import", response_model=Dict[str, Any])
async def batch_import_suppliers(
    project_id: uuid.UUID = Path(...),
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """批量导入供应商"""
    try:
        # 检查文件类型
        if not file.filename.endswith(('.xlsx', '.xls')):
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="只支持Excel文件(.xlsx, .xls)"
            )

        # 读取Excel文件
        contents = await file.read()
        df = pd.read_excel(io.BytesIO(contents))

        # 验证必要的列
        required_columns = ['name']
        for column in required_columns:
            if column not in df.columns:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Excel文件缺少必要的列: {column}"
                )

        # 处理导入数据
        success_count = 0
        error_count = 0
        errors = []

        for index, row in df.iterrows():
            try:
                # 检查必要字段
                if pd.isna(row['name']):
                    errors.append({
                        "row": index + 2,  # Excel行号从1开始，标题行为1
                        "error": "供应商名称不能为空"
                    })
                    error_count += 1
                    continue

                # 检查供应商名称是否已存在
                existing_supplier = await db.execute(select(Supplier).where(
                    Supplier.project_id == project_id,
                    Supplier.name == row['name']
                ).limit(1))
                existing_supplier = existing_supplier.scalars().first()

                if existing_supplier:
                    errors.append({
                        "row": index + 2,
                        "error": f"供应商名称 '{row['name']}' 已存在"
                    })
                    error_count += 1
                    continue

                # 创建供应商
                new_supplier = Supplier(
                    id=uuid.uuid4(),
                    tenant_id=current_user.tenant_id,
                    project_id=project_id,
                    name=row['name'],
                    contact_person=row['contact_person'] if 'contact_person' in df.columns and not pd.isna(row['contact_person']) else None,
                    contact_phone=row['contact_phone'] if 'contact_phone' in df.columns and not pd.isna(row['contact_phone']) else None,
                    email=row['email'] if 'email' in df.columns and not pd.isna(row['email']) else None,
                    address=row['address'] if 'address' in df.columns and not pd.isna(row['address']) else None,
                    description=row['description'] if 'description' in df.columns and not pd.isna(row['description']) else None,
                    is_active=True,
                    created_by=current_user.id,
                    updated_by=current_user.id
                )

                db.add(new_supplier)
                success_count += 1

            except Exception as e:
                errors.append({
                    "row": index + 2,
                    "error": str(e)
                })
                error_count += 1

        # 提交事务
        await db.commit()

        return {
            "success": True,
            "message": f"批量导入完成，成功: {success_count}，失败: {error_count}",
            "data": {
                "success_count": success_count,
                "error_count": error_count,
                "errors": errors
            }
        }
    except HTTPException:
        await db.rollback()
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"批量导入供应商失败: {str(e)}"
        )

@router.get("/export", response_model=Dict[str, Any])
async def export_suppliers(
    project_id: uuid.UUID = Path(...),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """导出供应商列表"""
    # 注意：此处应该返回文件下载，但为了简化，我们只返回成功消息
    # 实际实现应该使用StreamingResponse返回Excel文件
    return {
        "success": True,
        "message": "供应商导出功能已触发，请在系统通知中查看导出结果"
    }
