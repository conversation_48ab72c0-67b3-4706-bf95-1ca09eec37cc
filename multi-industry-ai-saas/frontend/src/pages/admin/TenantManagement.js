import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Table, 
  Button, 
  Space, 
  Tooltip, 
  Tag, 
  Typography, 
  Modal, 
  Form, 
  Input, 
  Select, 
  Divider,
  Popconfirm,
  message,
  Spin,
  Alert,
  Row,
  Col,
  Statistic,
  Upload,
  DatePicker,
  InputNumber
} from 'antd';
import { 
  BankOutlined, 
  PlusOutlined, 
  EditOutlined, 
  DeleteOutlined, 
  EyeOutlined, 
  SettingOutlined,
  UploadOutlined,
  CloudServerOutlined,
  TeamOutlined,
  ProjectOutlined,
  CloudOutlined,
  ApartmentOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import apiService from '../../services/api';
import dayjs from "dayjs";

const { Title, Paragraph, Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;
const { RangePicker } = DatePicker;

const TenantManagement = () => {
  const navigate = useNavigate();
  
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [tenants, setTenants] = useState([]);
  const [serviceProviders, setServiceProviders] = useState([]);
  const [industries, setIndustries] = useState([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [modalTitle, setModalTitle] = useState('');
  const [editingTenant, setEditingTenant] = useState(null);
  const [form] = Form.useForm();
  const [statistics, setStatistics] = useState({
    total: 0,
    active: 0,
    inactive: 0,
    projects: 0,
    users: 0
  });
  
  // 加载租户数据
  useEffect(() => {
    fetchTenants();
    fetchServiceProviders();
    fetchIndustries();
  }, []);
  
  // 获取租户列表
  const fetchTenants = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await apiService.getTenants();
      setTenants(response);
      
      // 计算统计数据
      const stats = {
        total: response.length,
        active: response.filter(t => t.status === 'active').length,
        inactive: response.filter(t => t.status !== 'active').length,
        projects: response.reduce((sum, t) => sum + (t.project_count || 0), 0),
        users: response.reduce((sum, t) => sum + (t.user_count || 0), 0)
      };
      
      setStatistics(stats);
    } catch (error) {
      console.error('获取租户列表失败:', error);
      setError('获取租户列表失败，请稍后重试');
    } finally {
      setLoading(false);
    }
  };
  
  // 获取服务商列表
  const fetchServiceProviders = async () => {
    try {
      const response = await apiService.getServiceProviders({ status: 'active' });
      setServiceProviders(response);
    } catch (error) {
      console.error('获取服务商列表失败:', error);
      message.error('获取服务商列表失败');
    }
  };
  
  // 获取行业列表
  const fetchIndustries = async () => {
    try {
      const response = await apiService.getIndustries({ is_active: true });
      setIndustries(response);
    } catch (error) {
      console.error('获取行业列表失败:', error);
      message.error('获取行业列表失败');
    }
  };
  
  // 打开创建租户模态框
  const showCreateModal = () => {
    setModalTitle('创建租户');
    setEditingTenant(null);
    form.resetFields();
    setModalVisible(true);
  };
  
  // 打开编辑租户模态框
  const showEditModal = (tenant) => {
    setModalTitle('编辑租户');
    setEditingTenant(tenant);
    
    // 设置订阅日期范围
    let subscriptionDateRange = null;
    if (tenant.subscription_start_date && tenant.subscription_end_date) {
      subscriptionDateRange = [
        dayjs(tenant.subscription_start_date),
        dayjs(tenant.subscription_end_date)
      ];
    }
    
    form.setFieldsValue({
      name: tenant.name,
      service_provider_id: tenant.service_provider_id,
      industry_type: tenant.industry_type,
      description: tenant.description,
      status: tenant.status,
      subscription_plan_id: tenant.subscription_plan_id,
      subscription_date_range: subscriptionDateRange,
      max_projects: tenant.max_projects,
      max_users: tenant.max_users,
      max_storage_gb: tenant.max_storage_gb,
      settings: tenant.settings ? JSON.stringify(tenant.settings) : '{}'
    });
    
    setModalVisible(true);
  };
  
  // 关闭模态框
  const handleCancel = () => {
    setModalVisible(false);
  };
  
  // 提交表单
  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      
      // 处理订阅日期范围
      if (values.subscription_date_range) {
        values.subscription_start_date = values.subscription_date_range[0].format('YYYY-MM-DD');
        values.subscription_end_date = values.subscription_date_range[1].format('YYYY-MM-DD');
      }
      delete values.subscription_date_range;
      
      // 解析JSON设置
      try {
        values.settings = values.settings ? JSON.parse(values.settings) : {};
      } catch (e) {
        message.error('设置格式不正确，请输入有效的JSON');
        return;
      }
      
      setLoading(true);
      
      if (editingTenant) {
        // 更新租户
        await apiService.updateTenant(editingTenant.id, values);
        message.success('租户更新成功');
      } else {
        // 创建租户
        await apiService.createTenant(values);
        message.success('租户创建成功');
      }
      
      setModalVisible(false);
      fetchTenants();
    } catch (error) {
      console.error('提交表单失败:', error);
      message.error('操作失败: ' + (error.response?.data?.detail || '未知错误'));
    } finally {
      setLoading(false);
    }
  };
  
  // 删除租户
  const handleDelete = async (id) => {
    try {
      setLoading(true);
      await apiService.deleteTenant(id);
      message.success('租户删除成功');
      fetchTenants();
    } catch (error) {
      console.error('删除租户失败:', error);
      message.error('删除失败: ' + (error.response?.data?.detail || '未知错误'));
    } finally {
      setLoading(false);
    }
  };
  
  // 获取状态标签
  const getStatusTag = (status) => {
    switch (status) {
      case 'active':
        return <Tag color="success">活跃</Tag>;
      case 'inactive':
        return <Tag color="error">停用</Tag>;
      case 'pending':
        return <Tag color="warning">待审核</Tag>;
      case 'suspended':
        return <Tag color="orange">已暂停</Tag>;
      case 'expired':
        return <Tag color="red">已过期</Tag>;
      default:
        return <Tag>{status}</Tag>;
    }
  };
  
  // 获取行业名称
  const getIndustryName = (code) => {
    const industry = industries.find(i => i.code === code);
    return industry ? industry.name : code;
  };
  
  // 获取服务商名称
  const getServiceProviderName = (id) => {
    const provider = serviceProviders.find(p => p.id === id);
    return provider ? provider.name : '未知服务商';
  };
  
  // 表格列定义
  const columns = [
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: '服务商',
      dataIndex: 'service_provider_id',
      key: 'service_provider_id',
      render: (text) => getServiceProviderName(text),
    },
    {
      title: '行业',
      dataIndex: 'industry_type',
      key: 'industry_type',
      render: (text) => getIndustryName(text),
    },
    {
      title: '项目数',
      dataIndex: 'project_count',
      key: 'project_count',
      render: (text, record) => (
        <span>{text || 0} / {record.max_projects || '不限'}</span>
      ),
    },
    {
      title: '用户数',
      dataIndex: 'user_count',
      key: 'user_count',
      render: (text, record) => (
        <span>{text || 0} / {record.max_users || '不限'}</span>
      ),
    },
    {
      title: '存储空间',
      dataIndex: 'storage_usage',
      key: 'storage_usage',
      render: (text, record) => (
        <span>
          {text ? `${(text / 1024 / 1024 / 1024).toFixed(2)} GB` : '0 GB'} / {record.max_storage_gb ? `${record.max_storage_gb} GB` : '不限'}
        </span>
      ),
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (text) => getStatusTag(text),
    },
    {
      title: '订阅到期',
      dataIndex: 'subscription_end_date',
      key: 'subscription_end_date',
      render: (text) => text ? new Date(text).toLocaleDateString() : '永久',
    },
    {
      title: '创建时间',
      dataIndex: 'created_at',
      key: 'created_at',
      render: (text) => new Date(text).toLocaleString(),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="查看详情">
            <Button 
              type="text" 
              icon={<EyeOutlined />} 
              onClick={() => navigate(`/admin/tenants/${record.id}`)} 
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button 
              type="text" 
              icon={<EditOutlined />} 
              onClick={() => showEditModal(record)} 
            />
          </Tooltip>
          <Tooltip title="设置">
            <Button 
              type="text" 
              icon={<SettingOutlined />} 
              onClick={() => navigate(`/admin/tenants/${record.id}/settings`)} 
            />
          </Tooltip>
          <Popconfirm
            title="确定要删除此租户吗？"
            description="删除后无法恢复，且会删除其下所有项目和数据。"
            onConfirm={() => handleDelete(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Button 
              type="text" 
              danger 
              icon={<DeleteOutlined />} 
            />
          </Popconfirm>
        </Space>
      ),
    },
  ];
  
  // 错误提示
  const errorAlert = error ? (
    <Alert
      message="错误"
      description={error}
      type="error"
      showIcon
      style={{ marginBottom: 16 }}
    />
  ) : null;
  
  return (
    <div className="tenant-management">
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
        <div>
          <Title level={2}><BankOutlined /> 租户管理</Title>
          <Paragraph>管理系统中的租户，包括创建、编辑和删除租户。</Paragraph>
        </div>
        <Button 
          type="primary" 
          icon={<PlusOutlined />} 
          onClick={showCreateModal}
        >
          创建租户
        </Button>
      </div>
      
      {errorAlert}
      
      {/* 统计卡片 */}
      <Row gutter={16} style={{ marginBottom: 24 }}>
        <Col span={4}>
          <Card>
            <Statistic
              title="租户总数"
              value={statistics.total}
              prefix={<BankOutlined />}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="活跃租户"
              value={statistics.active}
              valueStyle={{ color: '#3f8600' }}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="非活跃租户"
              value={statistics.inactive}
              valueStyle={{ color: '#cf1322' }}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="项目总数"
              value={statistics.projects}
              prefix={<ProjectOutlined />}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Statistic
              title="用户总数"
              value={statistics.users}
              prefix={<TeamOutlined />}
            />
          </Card>
        </Col>
        <Col span={4}>
          <Card>
            <Button 
              type="primary" 
              icon={<CloudOutlined />} 
              onClick={() => fetchTenants()}
              loading={loading}
              block
            >
              刷新数据
            </Button>
          </Card>
        </Col>
      </Row>
      
      <Spin spinning={loading}>
        <Card>
          <Table 
            columns={columns} 
            dataSource={tenants} 
            rowKey="id" 
            pagination={{ 
              pageSize: 10,
              showSizeChanger: true,
              showTotal: (total) => `共 ${total} 条记录`
            }}
          />
        </Card>
      </Spin>
      
      {/* 创建/编辑租户模态框 */}
      <Modal
        title={modalTitle}
        open={modalVisible}
        onOk={handleSubmit}
        onCancel={handleCancel}
        width={800}
        confirmLoading={loading}
      >
        <Form
          form={form}
          layout="vertical"
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="租户名称"
                rules={[{ required: true, message: '请输入租户名称' }]}
              >
                <Input placeholder="请输入租户名称" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="service_provider_id"
                label="服务商"
                rules={[{ required: true, message: '请选择服务商' }]}
              >
                <Select placeholder="请选择服务商">
                  {serviceProviders.map(provider => (
                    <Option key={provider.id} value={provider.id}>
                      {provider.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>
          
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="industry_type"
                label="行业类型"
                rules={[{ required: true, message: '请选择行业类型' }]}
              >
                <Select placeholder="请选择行业类型">
                  {industries.map(industry => (
                    <Option key={industry.code} value={industry.code}>
                      {industry.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="status"
                label="状态"
                initialValue="active"
                rules={[{ required: true, message: '请选择状态' }]}
              >
                <Select placeholder="请选择状态">
                  <Option value="active">活跃</Option>
                  <Option value="inactive">停用</Option>
                  <Option value="pending">待审核</Option>
                  <Option value="suspended">已暂停</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          
          <Form.Item
            name="description"
            label="描述"
          >
            <TextArea rows={4} placeholder="请输入租户描述" />
          </Form.Item>
          
          <Divider>订阅信息</Divider>
          
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="subscription_plan_id"
                label="订阅计划"
              >
                <Select placeholder="请选择订阅计划" allowClear>
                  <Option value="basic">基础版</Option>
                  <Option value="standard">标准版</Option>
                  <Option value="premium">高级版</Option>
                  <Option value="enterprise">企业版</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="subscription_date_range"
                label="订阅期限"
              >
                <RangePicker style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>
          
          <Divider>资源限制</Divider>
          
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="max_projects"
                label="最大项目数"
                tooltip="设置为0表示不限制"
                initialValue={1}
              >
                <InputNumber min={0} style={{ width: '100%' }} placeholder="请输入最大项目数" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="max_users"
                label="最大用户数"
                tooltip="设置为0表示不限制"
                initialValue={10}
              >
                <InputNumber min={0} style={{ width: '100%' }} placeholder="请输入最大用户数" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="max_storage_gb"
                label="最大存储空间(GB)"
                tooltip="设置为0表示不限制"
                initialValue={5}
              >
                <InputNumber min={0} style={{ width: '100%' }} placeholder="请输入最大存储空间" />
              </Form.Item>
            </Col>
          </Row>
          
          <Form.Item
            name="settings"
            label="设置"
            tooltip="JSON格式的设置"
            initialValue="{}"
          >
            <TextArea rows={4} placeholder='{"theme": "light", "logo_url": "https://example.com/logo.png"}' />
          </Form.Item>
          
          <Divider>品牌信息</Divider>
          
          <Form.Item
            name="logo"
            label="Logo"
          >
            <Upload
              name="logo"
              listType="picture"
              maxCount={1}
              beforeUpload={() => false}
            >
              <Button icon={<UploadOutlined />}>上传Logo</Button>
            </Upload>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default TenantManagement;
