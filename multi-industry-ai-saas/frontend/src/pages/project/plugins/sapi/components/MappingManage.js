import React, { useState, useEffect } from 'react';
import { Card, Typography, Button, Table, Space, Tag, Modal, Form, Input, Select, Switch, Tabs, Spin, message, Tooltip, Popconfirm, Badge, Drawer, Descriptions, Alert } from 'antd';
import { PlusOutlined, ReloadOutlined, DeleteOutlined, EditOutlined, EyeOutlined, LinkOutlined, ApiOutlined, ToolOutlined } from '@ant-design/icons';
import ReactJson from 'react-json-view';
import { getMappingList, createMapping, updateMapping, deleteMapping } from '../services/mappingService';
import { getEndpointList } from '../services/endpointService';
import { getAvailableServers } from '../services/mcpService';

const { Title, Paragraph, Text } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;
const { TextArea } = Input;

/**
 * API到MCP工具映射管理组件
 */
const MappingManage = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [mappings, setMappings] = useState([]);
  const [endpoints, setEndpoints] = useState([]);
  const [servers, setServers] = useState([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [modalTitle, setModalTitle] = useState('创建工具映射');
  const [editingMapping, setEditingMapping] = useState(null);
  const [detailVisible, setDetailVisible] = useState(false);
  const [detailMapping, setDetailMapping] = useState(null);
  const [selectedEndpoint, setSelectedEndpoint] = useState(null);

  // 加载映射列表
  useEffect(() => {
    fetchMappings();
    fetchEndpoints();
    fetchServers();
  }, []);

  // 获取映射列表
  const fetchMappings = async () => {
    setLoading(true);
    try {
      const response = await getMappingList();
      if (response.success) {
        setMappings(response.data);
      } else {
        message.error('获取工具映射列表失败');
      }
    } catch (error) {
      console.error('获取工具映射列表失败:', error);
      message.error('获取工具映射列表失败');
    } finally {
      setLoading(false);
    }
  };

  // 获取端点列表
  const fetchEndpoints = async () => {
    try {
      const response = await getEndpointList();
      if (response.success) {
        setEndpoints(response.data);
      } else {
        message.error('获取API端点列表失败');
      }
    } catch (error) {
      console.error('获取API端点列表失败:', error);
      message.error('获取API端点列表失败');
    }
  };

  // 获取服务器列表
  const fetchServers = async () => {
    try {
      const response = await getAvailableServers();
      if (response.success) {
        setServers(response.data);
      } else {
        message.error('获取服务器列表失败');
      }
    } catch (error) {
      console.error('获取服务器列表失败:', error);
      message.error('获取服务器列表失败');
    }
  };

  // 打开创建映射模态框
  const showCreateModal = () => {
    setModalTitle('创建工具映射');
    setEditingMapping(null);
    form.resetFields();
    setSelectedEndpoint(null);
    setModalVisible(true);
  };

  // 打开编辑映射模态框
  const showEditModal = (mapping) => {
    setModalTitle('编辑工具映射');
    setEditingMapping(mapping);
    setSelectedEndpoint(mapping.endpoint_id);

    // 设置表单初始值
    form.setFieldsValue({
      endpoint_id: mapping.endpoint_id,
      server_id: mapping.server_id,
      tool_name: mapping.tool_name,
      tool_description: mapping.tool_description || '',
      parameter_mapping: mapping.parameter_mapping ? JSON.stringify(mapping.parameter_mapping, null, 2) : '{}',
      response_mapping: mapping.response_mapping ? JSON.stringify(mapping.response_mapping, null, 2) : '{}',
      supports_streaming: mapping.supports_streaming,
      supports_media: mapping.supports_media,
      enabled: mapping.enabled
    });

    setModalVisible(true);
  };

  // 处理表单提交
  const handleSubmit = async (values) => {
    try {
      // 解析参数映射和响应映射
      let parameter_mapping = {};
      let response_mapping = {};

      try {
        parameter_mapping = JSON.parse(values.parameter_mapping);
      } catch (error) {
        message.error('参数映射格式错误，请输入有效的JSON');
        return;
      }

      try {
        response_mapping = JSON.parse(values.response_mapping);
      } catch (error) {
        message.error('响应映射格式错误，请输入有效的JSON');
        return;
      }

      // 构建映射配置
      const mappingConfig = {
        ...values,
        parameter_mapping,
        response_mapping
      };

      if (editingMapping) {
        // 更新映射
        const response = await updateMapping(editingMapping.id, mappingConfig);
        if (response.success) {
          message.success('工具映射更新成功');
          setModalVisible(false);
          fetchMappings();
        } else {
          message.error('工具映射更新失败: ' + (response.message || '未知错误'));
        }
      } else {
        // 创建映射
        const response = await createMapping(mappingConfig);
        if (response.success) {
          message.success('工具映射创建成功');
          setModalVisible(false);
          fetchMappings();
        } else {
          message.error('工具映射创建失败: ' + (response.message || '未知错误'));
        }
      }
    } catch (error) {
      console.error('提交表单失败:', error);
      message.error('提交表单失败: ' + (error.message || '未知错误'));
    }
  };

  // 处理删除映射
  const handleDelete = async (mappingId) => {
    try {
      const response = await deleteMapping(mappingId);
      if (response.success) {
        message.success('工具映射删除成功');
        fetchMappings();
      } else {
        message.error('工具映射删除失败: ' + (response.message || '未知错误'));
      }
    } catch (error) {
      console.error('删除工具映射失败:', error);
      message.error('删除工具映射失败: ' + (error.message || '未知错误'));
    }
  };

  // 查看映射详情
  const showMappingDetail = (mapping) => {
    setDetailMapping(mapping);
    setDetailVisible(true);
  };

  // 处理端点选择变化
  const handleEndpointChange = (endpointId) => {
    setSelectedEndpoint(endpointId);

    // 根据选择的端点自动生成工具名称
    const endpoint = endpoints.find(e => e.id === endpointId);
    if (endpoint) {
      // 提取端点路径中的最后一部分作为工具名称
      const path_parts = endpoint.endpoint_path.trim('/').split('/');
      // 如果路径以/api/开头，则去掉api部分
      if (path_parts && path_parts[0] === 'api') {
        path_parts.shift();
      }
      const tool_name = path_parts[path_parts.length - 1] || endpoint.name.toLowerCase().replace(/\s+/g, '_');

      form.setFieldsValue({
        tool_name,
        tool_description: endpoint.description || ''
      });
    }
  };

  // 表格列定义
  const columns = [
    {
      title: '状态',
      key: 'enabled',
      render: (_, record) => (
        <Badge
          status={record.enabled ? 'success' : 'default'}
          text={record.enabled ? '启用' : '禁用'}
        />
      ),
      width: 80,
    },
    {
      title: '工具名称',
      dataIndex: 'tool_name',
      key: 'tool_name',
      render: text => <Text strong>{text}</Text>
    },
    {
      title: 'API端点',
      key: 'endpoint',
      render: (_, record) => (
        <Space>
          {record.endpoint_method && (
            <Tag color={
              record.endpoint_method === 'GET' ? 'green' :
              record.endpoint_method === 'POST' ? 'blue' :
              record.endpoint_method === 'PUT' ? 'orange' :
              record.endpoint_method === 'DELETE' ? 'red' :
              record.endpoint_method === 'PATCH' ? 'purple' : 'default'
            }>
              {record.endpoint_method}
            </Tag>
          )}
          <Text ellipsis style={{ maxWidth: 200 }}>{record.endpoint_path}</Text>
        </Space>
      ),
    },
    {
      title: '服务器',
      dataIndex: 'server_name',
      key: 'server_name',
    },
    {
      title: '特性',
      key: 'features',
      render: (_, record) => (
        <Space>
          {record.supports_streaming && <Tag color="blue">流式</Tag>}
          {record.supports_media && <Tag color="purple">媒体</Tag>}
        </Space>
      ),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <Space>
          <Tooltip title="查看详情">
            <Button
              icon={<EyeOutlined />}
              size="small"
              onClick={() => showMappingDetail(record)}
            />
          </Tooltip>
          <Tooltip title="编辑">
            <Button
              icon={<EditOutlined />}
              size="small"
              onClick={() => showEditModal(record)}
            />
          </Tooltip>
          <Tooltip title="删除">
            <Popconfirm
              title="确定要删除此工具映射吗？"
              onConfirm={() => handleDelete(record.id)}
              okText="确定"
              cancelText="取消"
            >
              <Button
                icon={<DeleteOutlined />}
                size="small"
                danger
              />
            </Popconfirm>
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <Card>
      <Title level={4}>工具映射管理</Title>
      <Paragraph>
        管理API端点到MCP工具的映射，包括创建、编辑和删除映射。通过映射，可以将系统API转换为MCP工具，供大语言模型使用。
      </Paragraph>

      <div style={{ marginBottom: 16 }}>
        <Space>
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={showCreateModal}
          >
            创建映射
          </Button>
          <Button
            icon={<ReloadOutlined />}
            onClick={() => {
              fetchMappings();
              fetchEndpoints();
              fetchServers();
            }}
          >
            刷新列表
          </Button>
        </Space>
      </div>

      <Spin spinning={loading}>
        <Table
          dataSource={mappings}
          columns={columns}
          rowKey="id"
          pagination={{ pageSize: 10 }}
        />
      </Spin>

      {/* 创建/编辑映射模态框 */}
      <Modal
        title={modalTitle}
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        footer={null}
        width={700}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            parameter_mapping: '{}',
            response_mapping: '{}',
            supports_streaming: true,
            supports_media: false,
            enabled: true
          }}
        >
          <Form.Item
            name="endpoint_id"
            label="API端点"
            rules={[{ required: true, message: '请选择API端点' }]}
          >
            <Select
              placeholder="请选择API端点"
              onChange={handleEndpointChange}
              showSearch
              optionFilterProp="children"
            >
              {endpoints.map(endpoint => (
                <Option key={endpoint.id} value={endpoint.id}>
                  <Space>
                    <Tag color={
                      endpoint.method === 'GET' ? 'green' :
                      endpoint.method === 'POST' ? 'blue' :
                      endpoint.method === 'PUT' ? 'orange' :
                      endpoint.method === 'DELETE' ? 'red' :
                      endpoint.method === 'PATCH' ? 'purple' : 'default'
                    }>
                      {endpoint.method}
                    </Tag>
                    {endpoint.name} - {endpoint.endpoint_path}
                  </Space>
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="server_id"
            label="MCP服务器"
            rules={[{ required: true, message: '请选择MCP服务器' }]}
          >
            <Select
              placeholder="请选择MCP服务器"
              showSearch
              optionFilterProp="children"
            >
              {servers.map(server => (
                <Option key={server.id} value={server.id}>
                  {server.name}
                  <Badge
                    status={server.status === 'running' ? 'success' : 'default'}
                    text={server.status === 'running' ? '运行中' : '已停止'}
                    style={{ marginLeft: 8 }}
                  />
                </Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            name="tool_name"
            label="工具名称"
            rules={[{ required: true, message: '请输入工具名称' }]}
            tooltip="MCP工具的名称，建议使用小写字母和下划线"
          >
            <Input placeholder="请输入工具名称" />
          </Form.Item>

          <Form.Item
            name="tool_description"
            label="工具描述"
            tooltip="工具的描述，如果不填写则使用API端点的描述"
          >
            <TextArea
              placeholder="请输入工具描述"
              rows={2}
            />
          </Form.Item>

          <Form.Item
            name="parameter_mapping"
            label="参数映射"
            tooltip="JSON格式的参数映射，用于将API参数映射到MCP工具参数"
          >
            <TextArea
              placeholder="请输入JSON格式的参数映射"
              rows={5}
            />
          </Form.Item>

          <Form.Item
            name="response_mapping"
            label="响应映射"
            tooltip="JSON格式的响应映射，用于将API响应映射到MCP工具响应"
          >
            <TextArea
              placeholder="请输入JSON格式的响应映射"
              rows={5}
            />
          </Form.Item>

          <Form.Item
            name="supports_streaming"
            label="支持流式响应"
            valuePropName="checked"
            tooltip="是否支持流式响应"
          >
            <Switch />
          </Form.Item>

          <Form.Item
            name="supports_media"
            label="支持媒体"
            valuePropName="checked"
            tooltip="是否支持图片等媒体类型"
          >
            <Switch />
          </Form.Item>

          <Form.Item
            name="enabled"
            label="启用状态"
            valuePropName="checked"
            tooltip="是否启用此工具映射"
          >
            <Switch />
          </Form.Item>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                {editingMapping ? '更新' : '创建'}
              </Button>
              <Button onClick={() => setModalVisible(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* 映射详情抽屉 */}
      <Drawer
        title={detailMapping?.tool_name}
        placement="right"
        width={700}
        onClose={() => setDetailVisible(false)}
        open={detailVisible}
      >
        {detailMapping && (
          <Tabs defaultActiveKey="1">
            <TabPane
              tab={<span><ToolOutlined /> 基本信息</span>}
              key="1"
            >
              <Descriptions bordered column={1}>
                <Descriptions.Item label="ID">{detailMapping.id}</Descriptions.Item>
                <Descriptions.Item label="工具名称">{detailMapping.tool_name}</Descriptions.Item>
                <Descriptions.Item label="工具描述">{detailMapping.tool_description || detailMapping.endpoint_description}</Descriptions.Item>
                <Descriptions.Item label="API端点">
                  <Space>
                    {detailMapping.endpoint_method && (
                      <Tag color={
                        detailMapping.endpoint_method === 'GET' ? 'green' :
                        detailMapping.endpoint_method === 'POST' ? 'blue' :
                        detailMapping.endpoint_method === 'PUT' ? 'orange' :
                        detailMapping.endpoint_method === 'DELETE' ? 'red' :
                        detailMapping.endpoint_method === 'PATCH' ? 'purple' : 'default'
                      }>
                        {detailMapping.endpoint_method}
                      </Tag>
                    )}
                    {detailMapping.endpoint_path}
                  </Space>
                </Descriptions.Item>
                <Descriptions.Item label="服务器">{detailMapping.server_name}</Descriptions.Item>
                <Descriptions.Item label="特性">
                  <Space>
                    {detailMapping.supports_streaming && <Tag color="blue">流式</Tag>}
                    {detailMapping.supports_media && <Tag color="purple">媒体</Tag>}
                  </Space>
                </Descriptions.Item>
                <Descriptions.Item label="状态">
                  <Badge
                    status={detailMapping.enabled ? 'success' : 'default'}
                    text={detailMapping.enabled ? '启用' : '禁用'}
                  />
                </Descriptions.Item>
                <Descriptions.Item label="创建时间">{new Date(detailMapping.created_at).toLocaleString()}</Descriptions.Item>
                <Descriptions.Item label="更新时间">{new Date(detailMapping.updated_at).toLocaleString()}</Descriptions.Item>
              </Descriptions>
            </TabPane>

            <TabPane
              tab={<span><ApiOutlined /> 映射配置</span>}
              key="2"
            >
              <Card size="small" title="参数映射" style={{ marginBottom: 16 }}>
                {detailMapping.parameter_mapping && Object.keys(detailMapping.parameter_mapping).length > 0 ? (
                  <ReactJson
                    src={detailMapping.parameter_mapping}
                    name={false}
                    collapsed={1}
                    displayDataTypes={false}
                  />
                ) : (
                  <Alert
                    message="无参数映射"
                    description="未配置参数映射，将使用原始参数名称"
                    type="info"
                    showIcon
                  />
                )}
              </Card>

              <Card size="small" title="响应映射">
                {detailMapping.response_mapping && Object.keys(detailMapping.response_mapping).length > 0 ? (
                  <ReactJson
                    src={detailMapping.response_mapping}
                    name={false}
                    collapsed={1}
                    displayDataTypes={false}
                  />
                ) : (
                  <Alert
                    message="无响应映射"
                    description="未配置响应映射，将使用原始响应格式"
                    type="info"
                    showIcon
                  />
                )}
              </Card>
            </TabPane>
          </Tabs>
        )}
      </Drawer>
    </Card>
  );
};

export default MappingManage;
