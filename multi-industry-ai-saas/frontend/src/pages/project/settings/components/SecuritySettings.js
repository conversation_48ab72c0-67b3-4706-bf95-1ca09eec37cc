import React, { useState, useEffect } from 'react';
import {
  Form, Input, Switch, Button, Space, Divider,
  InputNumber, Row, Col, Alert, Select, message
} from 'antd';
import {
  SaveOutlined, ReloadOutlined, LockOutlined,
  SafetyOutlined, ClockCircleOutlined
} from '@ant-design/icons';
import { project } from '../../../../services';

/**
 * 安全设置组件
 * 用于配置密码策略、登录限制、IP限制等安全相关设置
 */
const SecuritySettings = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [ipRestrictionEnabled, setIpRestrictionEnabled] = useState(false);

  // 初始化表单数据
  useEffect(() => {
    fetchSettings();
  }, []);

  // 获取设置数据
  const fetchSettings = async () => {
    try {
      setLoading(true);
      const settings = await project.settings.getSecurity();
      form.setFieldsValue(settings);
      setIpRestrictionEnabled(settings.ipRestriction);
      setLoading(false);
    } catch (error) {
      console.error('获取安全设置失败:', error);
      setLoading(false);
    }
  };

  // 保存设置
  const saveSettings = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);
      await project.settings.updateSecurity(values);
      message.success('安全设置保存成功');
      setLoading(false);
    } catch (error) {
      console.error('保存安全设置失败:', error);
      message.error('保存安全设置失败');
      setLoading(false);
    }
  };

  // 重置设置
  const resetSettings = () => {
    form.resetFields();
    message.info('安全设置已重置');
  };

  return (
    <Form
      form={form}
      layout="vertical"
      className="settings-form"
      initialValues={{
        passwordExpiration: 90,
        twoFactorAuth: false,
        loginAttempts: 5,
        sessionTimeout: 30,
        ipRestriction: false,
        allowedIps: ''
      }}
    >
      <Alert
        message="安全设置"
        description="配置系统安全相关设置，包括密码策略、登录限制、会话超时等。"
        type="info"
        showIcon
        style={{ marginBottom: 24 }}
      />

      <Row gutter={24}>
        <Col span={12}>
          <Form.Item
            name="passwordExpiration"
            label="密码过期时间(天)"
            rules={[{ required: true, message: '请输入密码过期时间' }]}
            tooltip="设置密码过期时间，超过该时间用户需要重新设置密码。设置为0表示密码永不过期。"
          >
            <InputNumber min={0} style={{ width: '100%' }} />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            name="loginAttempts"
            label="最大登录尝试次数"
            rules={[{ required: true, message: '请输入最大登录尝试次数' }]}
            tooltip="设置用户连续登录失败的最大次数，超过该次数账户将被锁定。"
          >
            <InputNumber min={1} style={{ width: '100%' }} />
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={24}>
        <Col span={12}>
          <Form.Item
            name="sessionTimeout"
            label="会话超时时间(分钟)"
            rules={[{ required: true, message: '请输入会话超时时间' }]}
            tooltip="设置用户无操作情况下的会话超时时间，超过该时间用户需要重新登录。"
          >
            <InputNumber min={5} style={{ width: '100%' }} />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            name="twoFactorAuth"
            label="启用双因素认证"
            valuePropName="checked"
            tooltip="启用后，用户登录时需要进行双因素认证。"
          >
            <Switch />
          </Form.Item>
        </Col>
      </Row>

      <Form.Item
        name="ipRestriction"
        label="启用IP限制"
        valuePropName="checked"
        tooltip="启用后，只有指定IP地址的用户才能登录系统。"
      >
        <Switch onChange={setIpRestrictionEnabled} />
      </Form.Item>

      {ipRestrictionEnabled && (
        <Form.Item
          name="allowedIps"
          label="允许的IP地址"
          tooltip="输入允许登录的IP地址，多个IP地址请用逗号分隔。"
        >
          <Input.TextArea
            placeholder="请输入允许的IP地址，多个IP地址请用逗号分隔，例如：***********,***********"
            rows={3}
          />
        </Form.Item>
      )}

      <Divider />

      <Form.Item>
        <Space>
          <Button
            type="primary"
            icon={<SaveOutlined />}
            onClick={saveSettings}
            loading={loading}
          >
            保存设置
          </Button>
          <Button
            icon={<ReloadOutlined />}
            onClick={resetSettings}
          >
            重置
          </Button>
        </Space>
      </Form.Item>
    </Form>
  );
};

export default SecuritySettings;
