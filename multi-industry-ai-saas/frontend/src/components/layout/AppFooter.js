import React from 'react';
import { Layout, Typography } from 'antd';
import { useTenant } from '../../contexts/TenantContext';

const { Footer } = Layout;
const { Text } = Typography;

const AppFooter = () => {
  const { currentTenant, currentServiceProvider } = useTenant();
  
  // 获取版权信息
  const getCopyrightInfo = () => {
    if (currentTenant?.settings?.footer_text) {
      return currentTenant.settings.footer_text;
    } else if (currentServiceProvider?.settings?.footer_text) {
      return currentServiceProvider.settings.footer_text;
    } else {
      return `多行业AI SaaS系统 ©${new Date().getFullYear()} 版权所有`;
    }
  };
  
  return (
    <Footer style={{ textAlign: 'center', padding: '12px 50px' }}>
      <Text type="secondary">{getCopyrightInfo()}</Text>
    </Footer>
  );
};

export default AppFooter;
