#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
全渠道运营助手 - 竞品监控模型
"""

import uuid
from sqlalchemy import Column, String, Boolean, ForeignKey, Text, DateTime, Float, Integer, Index
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship
from datetime import datetime

from db.database import Base


class CompetitorProduct(Base):
    """竞品商品模型"""
    __tablename__ = "multi_channel_competitor_products"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id", ondelete="CASCADE"), nullable=False)
    channel_id = Column(UUID(as_uuid=True), ForeignKey("sales_channels.id", ondelete="CASCADE"), nullable=False)
    
    # 竞品信息
    competitor_name = Column(String(200), nullable=False, comment="竞争对手名称")
    competitor_store_id = Column(String(255), nullable=True, comment="竞争对手店铺ID")
    
    # 商品信息
    product_name = Column(String(500), nullable=False, comment="商品名称")
    external_product_id = Column(String(255), nullable=False, comment="外部平台商品ID")
    product_url = Column(String(1000), nullable=True, comment="商品链接")
    
    # 当前价格信息
    current_price = Column(Float, nullable=True, comment="当前价格")
    original_price = Column(Float, nullable=True, comment="原价")
    discount_info = Column(String(200), nullable=True, comment="优惠信息")
    
    # 商品详情
    description = Column(Text, nullable=True, comment="商品描述")
    images = Column(JSONB, default=list, comment="商品图片URL列表")
    specifications = Column(JSONB, default=dict, comment="商品规格信息")
    
    # 销售数据
    sales_volume = Column(Integer, nullable=True, comment="销量")
    rating = Column(Float, nullable=True, comment="评分")
    review_count = Column(Integer, nullable=True, comment="评价数量")
    
    # 监控设置
    is_active = Column(Boolean, default=True, comment="是否启用监控")
    monitor_frequency = Column(Integer, default=60, comment="监控频率（分钟）")
    price_alert_threshold = Column(Float, nullable=True, comment="价格预警阈值")
    
    # 爬虫配置
    crawler_config = Column(JSONB, default=dict, comment="爬虫配置")
    last_crawl_at = Column(DateTime, nullable=True, comment="最后爬取时间")
    crawl_status = Column(String(50), default="pending", comment="爬取状态")
    crawl_error = Column(Text, nullable=True, comment="爬取错误信息")
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # 索引
    __table_args__ = (
        Index('idx_competitor_channel', 'project_id', 'channel_id'),
        Index('idx_external_product', 'external_product_id'),
        Index('idx_monitor_active', 'is_active', 'last_crawl_at'),
    )


class CompetitorPriceHistory(Base):
    """竞品价格历史记录"""
    __tablename__ = "multi_channel_competitor_price_history"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    competitor_product_id = Column(UUID(as_uuid=True), ForeignKey("multi_channel_competitor_products.id", ondelete="CASCADE"), nullable=False)
    
    # 价格信息
    price = Column(Float, nullable=False, comment="价格")
    original_price = Column(Float, nullable=True, comment="原价")
    discount_info = Column(String(200), nullable=True, comment="优惠信息")
    
    # 销售数据
    sales_volume = Column(Integer, nullable=True, comment="销量")
    rating = Column(Float, nullable=True, comment="评分")
    review_count = Column(Integer, nullable=True, comment="评价数量")
    
    # 库存状态
    stock_status = Column(String(50), nullable=True, comment="库存状态")
    
    # 记录时间
    recorded_at = Column(DateTime, default=datetime.utcnow, comment="记录时间")
    
    # 索引
    __table_args__ = (
        Index('idx_competitor_price_time', 'competitor_product_id', 'recorded_at'),
    )


class CompetitorAlert(Base):
    """竞品预警记录"""
    __tablename__ = "multi_channel_competitor_alerts"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id", ondelete="CASCADE"), nullable=False)
    competitor_product_id = Column(UUID(as_uuid=True), ForeignKey("multi_channel_competitor_products.id", ondelete="CASCADE"), nullable=False)
    
    # 预警信息
    alert_type = Column(String(50), nullable=False, comment="预警类型: price_drop, price_rise, stock_out, new_product")
    alert_title = Column(String(200), nullable=False, comment="预警标题")
    alert_message = Column(Text, nullable=False, comment="预警消息")
    
    # 触发数据
    trigger_data = Column(JSONB, default=dict, comment="触发预警的数据")
    
    # 处理状态
    status = Column(String(50), default="pending", comment="处理状态: pending, acknowledged, resolved, ignored")
    handled_by = Column(UUID(as_uuid=True), nullable=True, comment="处理人ID")
    handled_at = Column(DateTime, nullable=True, comment="处理时间")
    handle_note = Column(Text, nullable=True, comment="处理备注")
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)


class CompetitorAnalysis(Base):
    """竞品分析报告"""
    __tablename__ = "multi_channel_competitor_analysis"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id", ondelete="CASCADE"), nullable=False)
    
    # 分析范围
    analysis_type = Column(String(50), nullable=False, comment="分析类型: daily, weekly, monthly, custom")
    start_date = Column(DateTime, nullable=False, comment="分析开始时间")
    end_date = Column(DateTime, nullable=False, comment="分析结束时间")
    
    # 分析结果
    analysis_data = Column(JSONB, default=dict, comment="分析数据")
    insights = Column(JSONB, default=list, comment="分析洞察")
    recommendations = Column(JSONB, default=list, comment="AI推荐建议")
    
    # 生成信息
    generated_by = Column(String(50), default="system", comment="生成方式: system, user, ai")
    ai_model = Column(String(100), nullable=True, comment="使用的AI模型")
    
    # 时间戳
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
