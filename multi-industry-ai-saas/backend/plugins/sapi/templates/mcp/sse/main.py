#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
MCP服务器 - SSE Протокол (V2 - использует обновленный mcp.base)
"""

import os
import json
import logging
import asyncio
import sys
import uuid # Нужен для генерации ID сессий SSE, если не используется ID из base.py напрямую
from typing import Dict, Any, List, Optional, Union

# Импорт из mcp.base
from mcp.base import (
    MCPServer,
    SERVER_NAME,
    SERVER_VERSION,
    ErrorCode, # Import ErrorCode
    get_mcp_server_id,
    start_heartbeat as start_mcp_base_heartbeat_func,
    MCPSession,
    authenticate_request # Explicitly import authenticate_request
)
# 从load_tools.py导入load_tools函数
from load_tools import load_tools
# Утилиты для регистрации в Redis
from utils import register_server, heartbeat as redis_registration_heartbeat, parsed_env_sdk

# Настройка логирования
logger = logging.getLogger(__name__)

# FastAPI, Pydantic и SSE утилиты
from fastapi import FastAPI, Request as FastAPIRequest, Response as FastAPIResponse, HTTPException
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
from sse_starlette.sse import EventSourceResponse
from pydantic import BaseModel, Field

# --- Глобальный экземпляр MCPServer ---
SERVER_ID = get_mcp_server_id()
mcp_server = MCPServer(server_id=SERVER_ID)

# Создание FastAPI приложения
app = FastAPI(title=f"MCP Server (SSE) - {SERVER_ID}", version=SERVER_VERSION)

# CORS Middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"], allow_credentials=True, allow_methods=["*"], allow_headers=["*"],
)

# Модель для POST-запросов (команды)
class MCPRequestModelSSE(BaseModel):
    id: Optional[Union[str, int]] = None
    method: str = Field(...)
    params: Dict[str, Any] = Field(default_factory=dict)

# Хранилище очередей для активных SSE сессий (ключ - session_id из base.py)
sse_session_queues: Dict[str, asyncio.Queue] = {}

# Событие Startup
@app.on_event("startup")
async def startup_event_sse():
    logger.info(f"--- [SSE {SERVER_ID}] Событие Startup ---")
    host_for_registration = parsed_env_sdk.get("MCP_SERVER_HOST") or parsed_env_sdk.get("MCP_SERVICE_NAME") or f"mcp-server-{SERVER_ID}"
    port_str = os.environ.get("PORT")
    if not port_str:
        logger.critical(f"[SSE {SERVER_ID}] Переменная окружения PORT не установлена.")
        return
    try:
        current_port = int(port_str)
    except ValueError:
        logger.critical(f"[SSE {SERVER_ID}] Неверное значение PORT: '{port_str}'.")
        return

    logger.info(f"[SSE {SERVER_ID}] Регистрация в Redis: хост={host_for_registration}, порт={current_port}...")
    try:
        # 添加元数据以支持Traefik路由
        traefik_mcp_domain_suffix = os.environ.get("TRAEFIK_MCP_DOMAIN_SUFFIX", "localhost")
        mcp_instance_hostname = f"mcp-{SERVER_ID}.{traefik_mcp_domain_suffix}"

        registration_data = {
            "server_id": SERVER_ID,
            "host": host_for_registration,
            "port": current_port,
            "port_sse": current_port,
            "metadata": {
                "traefik_hostname": mcp_instance_hostname,
                "protocols": ["sse"],
                "version": SERVER_VERSION
            }
        }

        if register_server(**registration_data):
            logger.info(f"[SSE {SERVER_ID}] Сервер успешно зарегистрирован/обновлен в Redis.")
            asyncio.create_task(redis_registration_heartbeat(**registration_data))
            logger.info(f"[SSE {SERVER_ID}] Запущена задача Redis heartbeat.")
        else:
            logger.error(f"[SSE {SERVER_ID}] Ошибка регистрации в Redis.")
    except Exception as e:
        logger.error(f"[SSE {SERVER_ID}] Исключение при регистрации или запуске heartbeat: {e}", exc_info=True)

    # 先初始化服务器（包括预加载工具）
    await mcp_server.initialize_server()

    logger.info(f"[SSE {SERVER_ID}] Загрузка инструментов...")
    # 获取系统令牌
    system_token = os.environ.get("SYSTEM_TOKEN")
    if system_token:
        logger.info(f"[SSE {SERVER_ID}] 使用系统令牌获取工具列表")
    else:
        logger.warning(f"[SSE {SERVER_ID}] 未找到系统令牌，工具获取可能失败")

    # 不使用MCPSession，直接传递None并让load_tools内部处理系统令牌
    tools = await load_tools(None)  # load_tools内部会处理None并查找环境变量中的系统令牌

    # 注册工具到mcp_server
    for tool in tools:
        tool_name = tool.get("name")
        if not tool_name:
            logger.warning(f"工具缺少name字段: {tool}")
            continue

        # 创建工具函数
        async def tool_func(session, params, tool_metadata=tool, stream_mode=False):
            """
            工具函数模板

            Args:
                session: MCP会话
                params: 工具参数
                tool_metadata: 工具元数据
                stream_mode: 是否使用流模式

            Returns:
                Dict[str, Any]: 工具执行结果
            """
            logger.info(f"执行工具: {tool_metadata.get('name')}")
            logger.info(f"工具参数: {params}")
            logger.info(f"流模式: {stream_mode}")

            # 更新会话的最后活动时间
            session.last_activity_time = asyncio.get_event_loop().time()

            # 这里应该实现工具的具体逻辑
            # 例如，调用API、执行计算等

            # 返回工具执行结果
            return {
                "result": f"工具 {tool_metadata.get('name')} 执行成功",
                "metadata": tool_metadata,
                "stream_mode": stream_mode
            }

        # 注册工具
        mcp_server.register_tool(tool_name, tool_func)
        logger.info(f"工具 {tool_name} 注册成功")

    logger.info(f"[SSE {SERVER_ID}] 成功注册 {len(tools)} 个工具")
    logger.info(f"[SSE {SERVER_ID}] Запуск фоновой задачи очистки сессий mcp.base...")
    asyncio.create_task(start_mcp_base_heartbeat_func(mcp_server))
    logger.info(f"[SSE {SERVER_ID}] Сервер '{SERVER_NAME}' полностью запущен и инициализирован на порту {current_port}.")

# GET /mcp - установка SSE соединения и аутентификация
@app.get("/mcp")
@app.get("/message")  # 添加标准MCP路径
async def mcp_sse_connect_and_authenticate(http_request: FastAPIRequest): # Renamed function for clarity
    logger.info(f"[SSE GET {SERVER_ID}] Новое SSE соединение от {http_request.client.host}")
    headers = dict(http_request.headers)

    try:
        # --- Шаг 1: Только Аутентификация ---
        session = await authenticate_request(SERVER_ID, headers)
        # Сохраняем сессию в хранилище сервера
        mcp_server.session_store[session.session_id] = session
        # Отмечаем, что сессия еще не прошла полную инициализацию через MCP
        mcp_server.is_initialized_for_session[session.session_id] = False
        logger.info(f"[SSE GET {SERVER_ID}] Аутентификация успешна, создана сессия: {session.session_id}")

    except HTTPException as he:
        # --- Аутентификация не удалась ---
        logger.warning(f"[SSE GET {SERVER_ID}] Ошибка аутентификации: {he.detail}")
        # Возвращаем HTTP ошибку, соответствующую ошибке аутентификации
        return FastAPIResponse(content=json.dumps({"jsonrpc": "2.0", "error": {"code": he.status_code, "message": he.detail}}),
                               media_type="application/json",
                               status_code=he.status_code)
    except Exception as e_auth:
        # --- Другая ошибка во время аутентификации ---
        logger.error(f"[SSE GET {SERVER_ID}] Неожиданная ошибка при аутентификации: {e_auth}", exc_info=True)
        err_resp = {"jsonrpc": "2.0", "error": {"code": 500, "message": f"Ошибка сервера при аутентификации: {str(e_auth)}"}}
        return FastAPIResponse(content=json.dumps(err_resp), media_type="application/json", status_code=500)

    # --- Шаг 2: Создание очереди SSE ---
    # Используем session_id из успешно созданной сессии
    base_session_id = session.session_id
    session_queue = asyncio.Queue()
    sse_session_queues[base_session_id] = session_queue
    logger.info(f"[SSE GET {SERVER_ID}] Сессия {base_session_id} аутентифицирована, SSE очередь создана.")

    # --- Шаг 3: Запуск генератора событий SSE ---
    async def sse_event_generator(current_session: MCPSession): # Pass the created session
        logger.debug(f"[SSE Gen {current_session.session_id}] 事件生成器启动。")
        try:
            # --- Отправка минимального события initialize с sessionId ---
            # (Клиенту все равно нужен будет полный initialize через POST)
            min_initialize_response = {
                "jsonrpc": "2.0",
                "result": {
                    "sessionId": current_session.session_id,
                    "message": "SSE connection established. Send full initialize request via POST /mcp."
                    # Можно добавить базовую информацию о сервере, если нужно
                    # "serverInfo": { "name": SERVER_NAME, "version": SERVER_VERSION, "id": SERVER_ID }
                }
                # Нет capabilities здесь, они будут в ответе на POST initialize
            }
            event_id = str(uuid.uuid4())
            yield {"event": "connection_established", "id": event_id, "data": json.dumps(min_initialize_response)} # Use a custom event name? Or stick to 'initialize'?
            logger.info(f"[SSE Gen {current_session.session_id}] Отправлено событие 'connection_established' с sessionId: {event_id}")

            # --- Основной цикл ожидания сообщений из очереди или отправки ping ---
            _SENTINEL_CLOSE_QUEUE_ = object()
            while True:
                 # (Rest of the existing main loop logic: check disconnect, wait for queue, send ping)
                try:
                    # 检查客户端是否已断开连接
                    if await http_request.is_disconnected():
                        logger.info(f"[SSE Gen {current_session.session_id}] 客户端已断开连接。")
                        break

                    # 等待来自队列的消息，最多等待1秒
                    try:
                        message_to_send = await asyncio.wait_for(session_queue.get(), timeout=1.0)
                        if message_to_send is _SENTINEL_CLOSE_QUEUE_:
                            logger.info(f"[SSE Gen {current_session.session_id}] 收到关闭队列信号。")
                        break

                        # 确保每个消息都有唯一ID
                        if isinstance(message_to_send, dict) and "id" not in message_to_send:
                            message_to_send["id"] = str(uuid.uuid4())

                        yield message_to_send
                        session_queue.task_done()
                except asyncio.TimeoutError:
                        # 发送ping事件以保持连接活跃
                        ping_id = str(uuid.uuid4())
                        ping_data = json.dumps({"timestamp": asyncio.get_event_loop().time()})
                        yield {"event": "ping", "id": ping_id, "data": ping_data}
                        continue
                    except asyncio.CancelledError:
                        logger.warning(f"[SSE Gen {current_session.session_id}] asyncio.CancelledErrorв главном цикле. Повторный выброс.")
                        raise
                    except Exception as e_loop:
                        logger.error(f"[SSE Gen {current_session.session_id}] Исключение в главном цикле: {str(e_loop)}", exc_info=True)
                        break
                except Exception as e_outer:
                    logger.error(f"[SSE Gen {current_session.session_id}] Исключение во внешнем цикле: {str(e_outer)}", exc_info=True)
                    break

        except asyncio.CancelledError:
            logger.warning(f"[SSE Gen DEBUG {current_session.session_id}] 事件生成器任务被取消（外部try-except）。")
            # No need to raise here, finally block will handle cleanup
        except Exception as e:
            logger.error(f"[SSE Gen {current_session.session_id}] 事件生成器中的异常: {str(e)}", exc_info=True)
        finally:
            # --- Очистка ---
            logger.info(f"[SSE Gen DEBUG {current_session.session_id}] Очистка: удаление очереди сессии.")
            if current_session.session_id in sse_session_queues:
                del sse_session_queues[current_session.session_id]
                logger.info(f"[SSE Gen DEBUG {current_session.session_id}] Очередь сессии удалена из sse_session_queues.")
            # Дополнительно: Удалить сессию из mcp_server? Или пусть heartbeat mcp_server этим занимается?
            # Решаем оставить управление mcp_server.session_store через его собственный heartbeat.
            logger.info(f"[SSE Gen DEBUG {current_session.session_id}] Генератор событий завершен.")

    # --- Шаг 4: Возвращаем EventSourceResponse для установления соединения ---
    # Передаем успешно созданную сессию в генератор
    return EventSourceResponse(
        sse_event_generator(session), # Pass the authenticated session
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "X-Accel-Buffering": "no"
        }
    )

# POST /mcp - отправка команд для существующей SSE-сессии
@app.post("/mcp")
async def mcp_sse_command(request_data: MCPRequestModelSSE, http_request: FastAPIRequest):
    logger.debug(f"[SSE POST {SERVER_ID}] id={request_data.id}, method={request_data.method}")
    headers = dict(http_request.headers)

    # --- ВАЖНО: Теперь initialize обрабатывается здесь, через POST ---
    if request_data.method == "initialize":
        logger.info(f"[SSE POST {SERVER_ID}] Обработка POST initialize запроса: {request_data.id}")
        # Нужен sessionId для ассоциации с существующим SSE соединением (аутентифицированным через GET)
        # Клиент *должен* был получить sessionId из первого 'connection_established' события
        session_id = request_data.params.get("sessionId")
        if not session_id:
            logger.warning(f"[SSE POST {SERVER_ID}] initialize запрос через POST не содержит sessionId.")
            return {"jsonrpc": "2.0", "id": request_data.id, "error": {"code": -32602, "message": "POST initialize requires 'sessionId' obtained from initial connection."}}

        # Проверка, что сессия существует в mcp_server (была создана при GET)
        if session_id not in mcp_server.session_store:
            logger.warning(f"[SSE POST {SERVER_ID}] initialize запрос для несуществующей сессии: {session_id}")
            return {"jsonrpc": "2.0", "id": request_data.id, "error": {"code": -32000, "message": "Session not found or expired."}}

        # Вызов handle_initialize теперь выполнит полную логику (версия, capabilities и т.д.)
        # Используем существующую сессию
        session = mcp_server.session_store[session_id]

        # Обновляем параметры сессии, если нужно (например, client_info из initialize params)
        client_capabilities = request_data.params.get("capabilities", {})
        session.client_info = {"capabilities": client_capabilities} # Пример
        session.last_activity_time = asyncio.get_event_loop().time() # Обновляем активность

        # --- Вызов полной логики handle_initialize из base.py ---
        # Мы не можем вызвать handle_initialize напрямую, так как он заново аутентифицирует.
        # Нам нужно выполнить только ту часть, что после аутентификации.
        # Рефакторинг handle_initialize в base.py может быть нужен,
        # либо здесь дублируем логику построения ответа.

        # --- Вариант: Дублируем логику построения ответа initialize ---
        try:
             # Проверка версии протокола клиента
            protocol_version = request_data.params.get("protocolVersion", PROTOCOL_VERSION)

             # Выполнение колбэков on_initialize
            await mcp_server._execute_callbacks(mcp_server.on_initialize_callbacks, session, request_data.params)
            mcp_server.is_initialized_for_session[session_id] = True

            # Построение ответа
            server_info = { "name": SERVER_NAME, "version": SERVER_VERSION, "id": SERVER_ID }
            server_capabilities = { "tools": { "streaming": True, "listChanged": True } }

            response_data = {
                "jsonrpc": "2.0",
                "id": request_data.id,
                "result": {
                    "protocolVersion": protocol_version,
                    "serverInfo": server_info,
                    "capabilities": server_capabilities,
                    "sessionId": session_id # Подтверждаем sessionId
                }
            }
            logger.info(f"[SSE POST {SERVER_ID}] Полный initialize для сессии {session_id} завершен.")

            # Ответ отправляется через SSE очередь
            if session_id in sse_session_queues:
                 event_id = str(uuid.uuid4())
                 await sse_session_queues[session_id].put({"event": "initialize_response", "id": event_id, "data": json.dumps(response_data)})
                 logger.info(f"[SSE POST {SERVER_ID}] Ответ на POST initialize (event_id: {event_id}) поставлен в очередь для сессии {session_id}.")
                 # Возвращаем простое подтверждение на POST запрос
                 return {"jsonrpc": "2.0", "id": request_data.id, "result": {"status": "initialize_queued_via_sse", "sse_event_id": event_id}}
            else:
                 logger.error(f"[SSE POST {SERVER_ID}] Очередь для сессии {session_id} не найдена после initialize.")
                 # Возвращаем ошибку на POST запрос
                 return {"jsonrpc": "2.0", "id": request_data.id, "error": {"code": -32000, "message": "SSE queue not found after initialize."}}

        except Exception as e_init_post:
             logger.error(f"[SSE POST {SERVER_ID}] Ошибка при обработке POST initialize: {e_init_post}", exc_info=True)
             return {"jsonrpc": "2.0", "id": request_data.id, "error": {"code": -32000, "message": f"Server error during POST initialize: {str(e_init_post)}"}}

    # --- Обработка других методов через POST ---
    elif request_data.method == "tools/refresh":
        # ... (existing logic for tools/refresh, needs sessionId) ...
        session_id = request_data.params.get("sessionId")
        if not session_id:
            logger.warning(f"[SSE POST {SERVER_ID}] tools/refresh缺少sessionId")
            return {"jsonrpc": "2.0", "id": request_data.id, "error": {"code": -32602, "message": "Missing required parameter 'sessionId'"}}
        # ... (rest of tools/refresh logic)
        # The response should likely be queued, not returned directly
        response_data = await mcp_server.handle_request(
            method="tools/refresh",
            params=request_data.params,
            request_id=request_data.id,
            headers=headers,
            session_id_from_transport=session_id
        )
        # Queue the response instead of returning it in the POST response
        if session_id in sse_session_queues:
            event_id = str(uuid.uuid4())
            await sse_session_queues[session_id].put({"event": "message", "id": event_id, "data": json.dumps(response_data)})
            logger.info(f"[SSE POST {SERVER_ID}] Ответ на tools/refresh (event_id: {event_id}) поставлен в очередь для сессии {session_id}.")
            return {"jsonrpc": "2.0", "id": request_data.id, "result": {"status": "request_queued_via_sse", "sse_event_id": event_id}}
        else:
             logger.error(f"[SSE POST {SERVER_ID}] Очередь для сессии {session_id} не найдена при tools/refresh.")
             return {"jsonrpc": "2.0", "id": request_data.id, "error": {"code": -32000, "message": "SSE queue not found for tools/refresh."}}

    # ... (existing logic for stream/start, stream/data, stream/end - they already expect sessionId) ...
    elif request_data.method == "stream/start":
        logger.info(f"[SSE POST {SERVER_ID}] 处理流式请求开始: {request_data.id}")
        session_id = request_data.params.get("sessionId")
        if not session_id:
            logger.warning(f"[SSE POST {SERVER_ID}] 流式请求缺少sessionId")
            return {"jsonrpc": "2.0", "id": request_data.id, "error": {"code": -32602, "message": "Missing required parameter 'sessionId'"}}

        # Call handle_stream_start which returns the stream_id confirmation
        response_data = await mcp_server.handle_stream_start(
            method=request_data.params.get("method", "unknown"),
            params=request_data.params.get("params", {}),
            request_id=request_data.id,
            headers=headers,
            session_id_from_transport=session_id
        )

        # Queue the confirmation response
        if session_id in sse_session_queues:
        if "result" in response_data and "stream_id" in response_data["result"]:
                event_id = str(uuid.uuid4())
                await sse_session_queues[session_id].put({"event": "stream_start", "id": event_id, "data": json.dumps(response_data)})
                 logger.info(f"[SSE POST {SERVER_ID}] 流开始响应 (stream_id: {response_data['result']['stream_id']}, event_id: {event_id}) 已放入会话 {session_id} 的队列")
                 # Return confirmation for the POST request
                 return {"jsonrpc": "2.0", "id": request_data.id, "result": {"status": "stream_start_queued_via_sse", "stream_id": response_data['result']['stream_id'], "sse_event_id": event_id}}
             else: # Handle error case from handle_stream_start
                  event_id = str(uuid.uuid4())
                  await sse_session_queues[session_id].put({"event": "error", "id": event_id, "data": json.dumps(response_data)})
                  logger.warning(f"[SSE POST {SERVER_ID}] 流开始错误响应 (event_id: {event_id}) 已放入会话 {session_id} 的队列")
                  return response_data # Return the error directly in POST response
        else:
             logger.error(f"[SSE POST {SERVER_ID}] Очередь для сессии {session_id} не найдена при stream/start.")
             return {"jsonrpc": "2.0", "id": request_data.id, "error": {"code": -32000, "message": "SSE queue not found for stream/start."}}

    elif request_data.method == "stream/data":
        logger.info(f"[SSE POST {SERVER_ID}] 处理流式数据: {request_data.id}")
        session_id = request_data.params.get("sessionId")
        stream_id = request_data.params.get("stream_id")
        data = request_data.params.get("data")
        if not session_id or not stream_id:
            logger.warning(f"[SSE POST {SERVER_ID}] 流式数据缺少sessionId或stream_id")
            return {"jsonrpc": "2.0", "id": request_data.id, "error": {"code": -32602, "message": "Missing required parameters 'sessionId' or 'stream_id'"}}

        success = await mcp_server.handle_stream_data(stream_id, data)
        # Queue the data event
        if success and session_id in sse_session_queues:
            event_id = str(uuid.uuid4())
            stream_data_event = { "stream_id": stream_id, "data": data, "timestamp": asyncio.get_event_loop().time() }
            await sse_session_queues[session_id].put({"event": "stream_data", "id": event_id, "data": json.dumps(stream_data_event)})
            # Return simple success for POST
            return {"jsonrpc": "2.0", "id": request_data.id, "result": {"success": True}}
        elif not success:
             logger.warning(f"[SSE POST {SERVER_ID}] handle_stream_data вернул False для stream_id {stream_id}")
             return {"jsonrpc": "2.0", "id": request_data.id, "result": {"success": False, "message": "Failed to handle stream data server-side."}}
        else: # Queue not found
             logger.error(f"[SSE POST {SERVER_ID}] Очередь для сессии {session_id} не найдена при stream/data.")
             return {"jsonrpc": "2.0", "id": request_data.id, "error": {"code": -32000, "message": "SSE queue not found for stream/data."}}


    elif request_data.method == "stream/end":
        logger.info(f"[SSE POST {SERVER_ID}] 处理流式请求结束: {request_data.id}")
        session_id = request_data.params.get("sessionId")
        stream_id = request_data.params.get("stream_id")
        if not session_id or not stream_id:
            logger.warning(f"[SSE POST {SERVER_ID}] 流式请求结束缺少sessionId或stream_id")
            return {"jsonrpc": "2.0", "id": request_data.id, "error": {"code": -32602, "message": "Missing required parameters 'sessionId' or 'stream_id'"}}

        success = await mcp_server.handle_stream_end(stream_id)
        # Queue the end event
        if success and session_id in sse_session_queues:
            event_id = str(uuid.uuid4())
            stream_end_event = { "stream_id": stream_id, "timestamp": asyncio.get_event_loop().time() }
            await sse_session_queues[session_id].put({"event": "stream_end", "id": event_id, "data": json.dumps(stream_end_event)})
            # Return simple success for POST
            return {"jsonrpc": "2.0", "id": request_data.id, "result": {"success": True}}
        elif not success:
             logger.warning(f"[SSE POST {SERVER_ID}] handle_stream_end вернул False для stream_id {stream_id}")
             return {"jsonrpc": "2.0", "id": request_data.id, "result": {"success": False, "message": "Failed to handle stream end server-side."}}
        else: # Queue not found
             logger.error(f"[SSE POST {SERVER_ID}] Очередь для сессии {session_id} не найдена при stream/end.")
             return {"jsonrpc": "2.0", "id": request_data.id, "error": {"code": -32000, "message": "SSE queue not found for stream/end."}}

    # --- Обработка остальных методов (например, tools/call) ---
    else:
        session_id = request_data.params.get("sessionId")
    if not session_id:
            logger.warning(f"[SSE POST {SERVER_ID}] Отсутствует sessionId в params для метода {request_data.method}.")
        return {"jsonrpc": "2.0", "id": request_data.id, "error": {"code": -32602, "message": "Отсутствует sessionId в параметрах запроса"}}

    if session_id not in sse_session_queues:
        logger.warning(f"[SSE POST {SERVER_ID}] Очередь для сессии {session_id} не найдена.")
        return {"jsonrpc": "2.0", "id": request_data.id, "error": {"code": -32000, "message": "SSE сессия не найдена или не активна"}}

        if session_id not in mcp_server.session_store:
        logger.warning(f"[SSE POST {SERVER_ID}] Сессия {session_id} не найдена в mcp_server.session_store.")
        if session_id in sse_session_queues: del sse_session_queues[session_id]
        return {"jsonrpc": "2.0", "id": request_data.id, "error": {"code": -32000, "message": "Сессия MCP истекла или была удалена"}}

        # Вызываем mcp_server.handle_request для обработки метода
    response_from_base = await mcp_server.handle_request(
        method=request_data.method,
            params=request_data.params,
        request_id=request_data.id,
        headers=headers,
            session_id_from_transport=session_id
    )

        # Отправляем ответ через SSE очередь
    event_id = str(uuid.uuid4())
    await sse_session_queues[session_id].put({"event": "message", "id": event_id, "data": json.dumps(response_from_base)})
        logger.info(f"[SSE POST {SERVER_ID}] Ответ на запрос {request_data.id} (метод: {request_data.method}, event_id: {event_id}) поставлен в очередь для сессии {session_id}.")

        # HTTP ответ на POST подтверждает получение команды
    return {"jsonrpc": "2.0", "id": request_data.id, "result": {"status": "request_queued_via_sse", "sse_event_id": event_id}}

# POST /mcp/close - запрос от клиента на закрытие SSE сессии
@app.post("/mcp/close")
async def client_initiated_sse_close(request_data: MCPRequestModelSSE, http_request: FastAPIRequest):
    session_id = request_data.params.get("sessionId") # Это base_session_id
    logger.info(f"[SSE Close {SERVER_ID}] Запрос от клиента на закрытие сессии {session_id}.")

    if not session_id or session_id not in sse_session_queues:
        logger.warning(f"[SSE Close {SERVER_ID}] Сессия {session_id} не найдена для закрытия.")
        return {"jsonrpc": "2.0", "id": request_data.id, "error": {"code": -32602, "message": "Сессия не найдена"}}

    headers = dict(http_request.headers) # Заголовки из запроса на закрытие

    # 1. Уведомить mcp_server.handle_request о shutdown для этой сессии
    await mcp_server.handle_request(
        method="shutdown",
        params={"sessionId": session_id}, # mcp.base ожидает sessionId в params для shutdown
        request_id=request_data.id, # ID запроса на закрытие
        headers=headers, # Передаем заголовки
        session_id_from_transport=session_id
    )
    logger.info(f"[SSE Close {SERVER_ID}] mcp_server.handle_request('shutdown') вызван для сессии {session_id}.")

    # 2. Отправить специальное событие в очередь, чтобы event_generator корректно завершился
    if session_id in sse_session_queues: # Проверка, что очередь еще существует
        await sse_session_queues[session_id].put({"event": "shutdown_confirmation", "id": str(uuid.uuid4()), "data": json.dumps({"reason": "client_request"})})
        logger.info(f"[SSE Close {SERVER_ID}] shutdown_confirmation поставлен в очередь для {session_id}.")
        # Очередь будет удалена в finally блоке event_generator

    return {"jsonrpc": "2.0", "id": request_data.id, "result": {"status": "close_initiated_for_sse"}}

# Эндпоинт для проверки состояния
@app.get("/health")
async def health_check_sse():
    redis_ok = False
    try:
        from utils import get_redis_client
        redis = await get_redis_client()
        await redis.ping()
        redis_ok = True
    except Exception as e:
        logger.warning(f"[SSE {SERVER_ID} Health] Ошибка проверки Redis: {e}")

    return {
        "status": "ok", "protocol": "sse", "server_id": SERVER_ID,
        "server_name": SERVER_NAME, "version": SERVER_VERSION,
        "redis_connected": redis_ok, "active_sse_queues": len(sse_session_queues)
    }

# Главная функция для запуска
if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    logger.info(f"--- [SSE MCP Server V2 ({SERVER_ID})] Запуск через __main__ ---")
    port_str = os.environ.get("PORT")
    if not port_str:
        logger.critical("Переменная окружения PORT не установлена. Невозможно запустить сервер.")
        sys.exit(1)
    try:
        port = int(port_str)
    except ValueError:
        logger.critical(f"Неверное значение PORT: '{port_str}'. Должно быть числом.")
        sys.exit(1)

    logger.info(f"Запуск Uvicorn для SSE MCP сервера ({SERVER_ID}) на хосте 0.0.0.0, порт {port}")
    uvicorn.run(app, host="0.0.0.0", port=port, log_config=None)
