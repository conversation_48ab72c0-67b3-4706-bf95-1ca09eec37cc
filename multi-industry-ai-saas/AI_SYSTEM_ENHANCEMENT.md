# AI系统增强功能说明

本次更新为多行业AI SaaS系统增加了三个重要功能模块，大幅提升了系统的AI处理能力和用户体验。

## 功能概述

### 1. AI助手管理模块的系统AI整合设置

#### 功能描述
在AI助手管理模块中新增系统AI整合设置功能，允许用户配置系统默认的AI模型，包括：
- 默认聊天模型（用于文本对话、内容生成等）
- 默认视觉模型（用于图像识别、表格分析等）
- 默认语音模型（用于语音识别、文本转语音等）
- 默认嵌入模型（用于向量化、语义搜索等）

#### 技术实现
- **后端模型**: `SystemAIIntegration` - 存储系统AI整合配置
- **后端服务**: `SystemAIIntegrationService` - 提供配置管理逻辑
- **后端API**: `/api/v1/project/{project_id}/ai/system-integration` - RESTful API接口
- **前端界面**: `SystemIntegrationSettings.js` - 配置管理界面

#### 配置层级
支持三级配置优先级：
1. **项目级配置**（最高优先级）
2. **租户级配置**（中等优先级）
3. **全局配置**（最低优先级）

#### 关键特性
- 模型类型验证确保配置正确性
- 支持模型回退机制
- 可配置请求超时和重试策略
- 实时配置状态展示

### 2. 通用AI+规则混合表格处理模块

#### 功能描述
实现了一个通用的表格数据处理模块，采用"AI为主 + 规则兜底"的混合处理方案：

1. **模板智能分流** - 自动识别表格类型并匹配预定义模板
2. **已知模板规则引擎提取** - 对已知模板使用规则引擎快速处理
3. **未知模板AI通用提取** - 对未知模板使用AI视觉模型进行通用数据提取
4. **数据校验与修正** - 自动验证和修正提取的数据

#### 技术实现
- **后端服务**: `TableProcessingService` - 核心处理逻辑
- **后端API**: `/api/v1/project/{project_id}/table-processing/` - 处理接口
- **Schema定义**: `table_processing.py` - 数据结构定义
- **模板系统**: 支持预定义模板和自定义模板注册

#### 处理流程
```
文件输入 → 模板匹配 → 处理策略选择 → 数据提取 → 校验修正 → 结果输出
                ↓
        [已知模板] → 规则引擎
        [未知模板] → AI视觉识别
        [混合模式] → 规则+AI结合
```

#### 预定义模板
- **采购分拨单模板** - 支持商品信息和门店分拨数据提取
- **库存表模板** - 支持库存管理数据提取
- 支持扩展更多业务模板

### 3. 采购分拨管理智能处理功能

#### 功能描述
在现有采购分拨管理模块基础上，集成AI智能识别功能，实现采购分拨单的自动化处理：

- **智能表格识别** - 自动识别采购分拨单的表格结构
- **商品信息提取** - 准确提取商品名称、单位、规格、单价等信息
- **门店分拨数据提取** - 自动识别和提取各门店的分拨数量
- **数据验证与修正** - 自动验证数据完整性并进行智能修正

#### 技术实现
- **增强API**: `preview_upload_with_ai` - 新增AI处理预览接口
- **集成服务**: 调用通用表格处理服务
- **智能提示词**: 专门针对采购分拨单优化的AI提示词
- **回退机制**: AI处理失败时自动回退到传统Excel解析

#### 使用流程
1. 上传采购分拨单文件（Excel/CSV/图片）
2. 系统自动识别为采购分拨单模板
3. AI智能提取商品信息和分拨数据
4. 实时预览提取结果和验证信息
5. 确认后自动创建采购订单和分拨单据

## 接口说明

### 系统AI整合设置接口

#### 获取配置
```http
GET /api/v1/project/{project_id}/ai/system-integration
```

#### 创建配置
```http
POST /api/v1/project/{project_id}/ai/system-integration
Content-Type: application/json

{
  "default_chat_model_id": "uuid",
  "default_vision_model_id": "uuid",
  "default_audio_model_id": "uuid",
  "default_embedding_model_id": "uuid",
  "chat_temperature": 0.7,
  "vision_temperature": 0.7,
  "chat_enabled": true,
  "vision_enabled": true,
  "audio_enabled": true,
  "embedding_enabled": true,
  "enable_fallback": true,
  "request_timeout": 30,
  "max_retries": 3
}
```

#### 更新配置
```http
PUT /api/v1/project/{project_id}/ai/system-integration/{config_id}
```

#### 获取有效模型
```http
GET /api/v1/project/{project_id}/ai/effective-model/{model_type}
```

### 通用表格处理接口

#### 处理表格
```http
POST /api/v1/project/{project_id}/table-processing/process
Content-Type: application/json

{
  "file_id": "uuid",
  "processing_mode": "auto",
  "template_hint": "purchase_distribution",
  "use_default_vision_model": true,
  "enable_validation": true,
  "enable_correction": true
}
```

#### 预览处理结果
```http
POST /api/v1/project/{project_id}/table-processing/preview
```

#### 获取可用模板
```http
GET /api/v1/project/{project_id}/table-processing/templates
```

### 采购分拨智能处理接口

#### AI智能预览
```http
POST /api/v1/project/{project_id}/purchase-orders/preview-upload-ai
Content-Type: application/json

{
  "file_id": "uuid",
  "upload_type": "both",
  "use_ai": true,
  "processing_mode": "auto"
}
```

## 数据库变更

### 新增表：system_ai_integrations

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | UUID | 主键 |
| tenant_id | UUID | 租户ID（可空，全局配置） |
| project_id | UUID | 项目ID（可空，租户级配置） |
| default_chat_model_id | UUID | 默认聊天模型ID |
| default_vision_model_id | UUID | 默认视觉模型ID |
| default_audio_model_id | UUID | 默认语音模型ID |
| default_embedding_model_id | UUID | 默认嵌入模型ID |
| chat_temperature | FLOAT | 聊天模型温度参数 |
| chat_max_tokens | INTEGER | 聊天模型最大token数 |
| vision_temperature | FLOAT | 视觉模型温度参数 |
| vision_max_tokens | INTEGER | 视觉模型最大token数 |
| chat_enabled | BOOLEAN | 启用聊天功能 |
| vision_enabled | BOOLEAN | 启用视觉功能 |
| audio_enabled | BOOLEAN | 启用语音功能 |
| embedding_enabled | BOOLEAN | 启用嵌入功能 |
| enable_fallback | BOOLEAN | 启用模型回退机制 |
| request_timeout | INTEGER | 请求超时时间 |
| max_retries | INTEGER | 最大重试次数 |
| created_at | DATETIME | 创建时间 |
| updated_at | DATETIME | 更新时间 |

## 部署说明

### 1. 数据库迁移
```bash
# 运行迁移脚本
python run_migration.py

# 或使用alembic直接运行
alembic upgrade head
```

### 2. 依赖更新
确保以下Python包已安装：
```
pandas>=1.5.0
numpy>=1.21.0
pillow>=9.0.0
```

### 3. 前端依赖
无新增依赖，使用现有的Ant Design组件。

### 4. 配置文件
无需修改现有配置文件，新功能兼容现有配置。

## 使用指南

### 1. 配置系统AI整合设置

1. 登录系统，进入AI助手管理页面
2. 点击"系统AI整合设置"菜单
3. 为每种模型类型选择默认模型
4. 配置模型参数（温度、最大token等）
5. 设置功能开关和通用参数
6. 保存配置

### 2. 使用采购分拨单智能处理

1. 进入采购分拨管理页面
2. 选择"采购分拨管理"标签页
3. 上传采购分拨单文件（支持Excel、CSV、图片）
4. 系统自动使用AI识别并提取数据
5. 检查预览结果和验证信息
6. 确认无误后提交处理

### 3. 自定义表格模板

1. 通过API注册自定义模板：
```javascript
await apiService.project.ai.tableProcessing.registerTemplate(projectId, {
  template_id: "custom_template",
  template_name: "自定义模板",
  description: "模板描述",
  columns: [
    {
      name: "列名",
      field_key: "field_key",
      data_type: "string",
      required: true
    }
  ],
  identification_rules: ["识别规则1", "识别规则2"]
});
```

## 技术优势

### 1. 智能化程度高
- AI自动识别表格结构和内容
- 智能数据验证和修正
- 自适应不同表格格式

### 2. 处理准确性强
- 多模型协同工作
- 规则引擎确保数据准确性
- 实时验证和错误提示

### 3. 扩展性好
- 模块化设计，易于扩展
- 支持自定义模板
- 标准化API接口

### 4. 用户体验佳
- 一键智能处理
- 实时预览结果
- 友好的错误提示

## 注意事项

1. **模型配置**：确保已配置有效的AI模型才能使用AI功能
2. **文件格式**：支持Excel（.xlsx, .xls）、CSV和常见图片格式
3. **权限管理**：需要相应的项目权限才能访问配置功能
4. **数据安全**：上传的文件仅用于数据提取，不会永久存储
5. **性能考虑**：大文件处理可能需要较长时间，建议合理控制文件大小

## 后续计划

1. **增加更多预定义模板**：财务报表、库存盘点、销售报告等
2. **优化AI识别准确性**：基于用户反馈持续优化模型
3. **增加批量处理功能**：支持多文件同时处理
4. **添加处理历史记录**：便于追踪和审计
5. **集成更多AI能力**：文档理解、智能摘要等

通过这三个功能模块的增加，系统的AI处理能力得到了显著提升，为用户提供了更加智能、便捷的表格数据处理体验。 