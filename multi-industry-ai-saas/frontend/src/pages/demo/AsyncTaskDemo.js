/**
 * 异步任务架构演示页面
 * 展示如何使用新的异步处理架构
 */

import React, { useState } from 'react';
import { 
  Card, 
  Button, 
  Space, 
  Typography, 
  Alert, 
  Divider, 
  Upload,
  Form,
  Select,
  message,
  Row,
  Col,
  Input,
  Switch,
  Slider,
  Progress,
  Spin
} from 'antd';
import { 
  UploadOutlined, 
  PlayCircleOutlined,
  InfoCircleOutlined,
  FileTextOutlined,
  CloudServerOutlined,
  ExperimentOutlined,
  SettingOutlined,
  StopOutlined
} from '@ant-design/icons';

import apiService from '../../services/api';

const { Title, Paragraph, Text } = Typography;
const { Option } = Select;

const AsyncTaskDemo = () => {
  const [uploadedFile, setUploadedFile] = useState(null);
  const [form] = Form.useForm();

  // 演示任务状态管理
  const [demoTasks, setDemoTasks] = useState(new Map());

  // 启动演示任务
  const startDemoTask = (taskType) => {
    const taskId = `demo-${taskType}-${Date.now()}`;
    
    // 创建任务对象
    const task = {
      id: taskId,
      type: taskType,
      name: `${taskType}_demo`,
      status: 'running',
      progress: 0,
      startTime: Date.now(),
      totalDuration: Math.floor(Math.random() * 10000) + 5000 // 5-15秒
    };

    // 添加到任务列表
    setDemoTasks(prev => new Map(prev.set(taskId, task)));

    // 开始进度模拟
    const interval = 200; // 每200ms更新一次
    const totalSteps = Math.floor(task.totalDuration / interval);
    let currentStep = 0;

    const timer = setInterval(() => {
      currentStep++;
      const progress = Math.min(Math.floor((currentStep / totalSteps) * 100), 100);
      
      setDemoTasks(prev => {
        const newTasks = new Map(prev);
        const updatedTask = { ...newTasks.get(taskId), progress };
        newTasks.set(taskId, updatedTask);
        return newTasks;
      });

      if (progress >= 100) {
        clearInterval(timer);
        
        // 模拟5%失败率
        const isSuccess = Math.random() > 0.05;
        
        setDemoTasks(prev => {
          const newTasks = new Map(prev);
          const completedTask = {
            ...newTasks.get(taskId),
            status: isSuccess ? 'completed' : 'failed',
            progress: 100,
            result: isSuccess ? `${taskType} 演示任务完成` : `${taskType} 演示任务失败`,
            endTime: Date.now()
          };
          newTasks.set(taskId, completedTask);
          return newTasks;
        });

        if (isSuccess) {
          message.success(`任务 ${taskType} 执行成功！`);
        } else {
          message.error(`任务 ${taskType} 执行失败！`);
        }
      }
    }, interval);

    // 存储定时器ID用于取消
    task.timerId = timer;
    setDemoTasks(prev => new Map(prev.set(taskId, task)));
  };

  // 取消演示任务
  const cancelDemoTask = (taskId) => {
    const task = demoTasks.get(taskId);
    if (task && task.timerId) {
      clearInterval(task.timerId);
      setDemoTasks(prev => {
        const newTasks = new Map(prev);
        const cancelledTask = {
          ...task,
          status: 'cancelled',
          endTime: Date.now()
        };
        newTasks.set(taskId, cancelledTask);
        return newTasks;
      });
      message.info(`任务 ${task.type} 已取消`);
    }
  };

  // 清理完成的任务
  const clearCompletedTasks = () => {
    setDemoTasks(prev => {
      const newTasks = new Map();
      prev.forEach((task, id) => {
        if (task.status === 'running') {
          newTasks.set(id, task);
        } else if (task.timerId) {
          clearInterval(task.timerId);
        }
      });
      return newTasks;
    });
  };

  // 文件上传处理
  const handleFileUpload = (info) => {
    if (info.file.status === 'done') {
      setUploadedFile(info.file.response);
      message.success('文件上传成功');
    } else if (info.file.status === 'error') {
      message.error('文件上传失败');
    }
  };

  // 启动AI预览任务
  const startAIPreviewTask = async () => {
    if (!uploadedFile) {
      message.warning('请先上传文件');
      return;
    }

    const values = form.getFieldsValue();
    startDemoTask('ai_preview');
  };

  // 渲染任务状态
  const renderTaskStatus = (task) => {
    const getStatusColor = (status) => {
      switch (status) {
        case 'running': return '#1890ff';
        case 'completed': return '#52c41a';
        case 'failed': return '#ff4d4f';
        case 'cancelled': return '#d9d9d9';
        default: return '#d9d9d9';
      }
    };

    const getStatusText = (status) => {
      switch (status) {
        case 'running': return '运行中';
        case 'completed': return '已完成';
        case 'failed': return '失败';
        case 'cancelled': return '已取消';
        default: return '未知';
      }
    };

    return (
      <Card 
        size="small" 
        style={{ marginBottom: 8 }}
        title={`${task.name} (${task.id.slice(-8)})`}
        extra={
          task.status === 'running' ? (
            <Button 
              size="small" 
              type="text" 
              icon={<StopOutlined />} 
              onClick={() => cancelDemoTask(task.id)}
            >
              取消
            </Button>
          ) : null
        }
      >
        <div style={{ display: 'flex', alignItems: 'center', gap: 12 }}>
          <div style={{ flex: 1 }}>
            <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: 4 }}>
              <Text strong>{getStatusText(task.status)}</Text>
              <Text type="secondary">{task.progress}%</Text>
            </div>
            <Progress 
              percent={task.progress} 
              size="small" 
              strokeColor={getStatusColor(task.status)}
              status={task.status === 'failed' ? 'exception' : 'normal'}
            />
            {task.result && (
              <Text type="secondary" style={{ fontSize: '12px' }}>
                {task.result}
              </Text>
            )}
            {task.endTime && (
              <div style={{ fontSize: '12px', color: '#999', marginTop: 4 }}>
                耗时: {((task.endTime - task.startTime) / 1000).toFixed(1)}s
              </div>
            )}
          </div>
        </div>
      </Card>
    );
  };

  return (
    <div style={{ padding: '24px' }}>
      <Title level={2}>异步任务架构演示</Title>
      
      <Alert
        message="异步处理架构说明"
        description={
          <div>
            <Paragraph>
              这个新的异步架构解决了长时间AI处理的超时问题：
            </Paragraph>
            <ul>
              <li><strong>立即响应</strong>: API立即返回任务ID，不会因为AI处理耗时而超时</li>
              <li><strong>实时进度</strong>: 通过轮询获取任务执行状态和进度</li>
              <li><strong>用户体验</strong>: 用户可以看到处理进度，可以取消任务</li>
              <li><strong>系统稳定</strong>: 避免长时间HTTP连接占用资源</li>
              <li><strong>错误处理</strong>: 更好的错误信息和重试机制</li>
            </ul>
          </div>
        }
        type="info"
        showIcon
        icon={<InfoCircleOutlined />}
        style={{ marginBottom: 24 }}
      />

      <Row gutter={24}>
        {/* 左侧：任务控制面板 */}
        <Col span={12}>
          <Card title="任务控制面板" style={{ marginBottom: 24 }}>
            <Space direction="vertical" style={{ width: '100%' }}>
              {/* AI文档处理演示 */}
              <Card size="small" title="AI文档处理" type="inner">
                <Form form={form} layout="vertical" size="small">
                  <Form.Item label="上传文件">
                    <Upload
                      name="file"
                      action="/api/v1/storage/upload"
                      headers={{
                        'Authorization': `Bearer ${localStorage.getItem('token')}`
                      }}
                      onChange={handleFileUpload}
                      maxCount={1}
                    >
                      <Button icon={<UploadOutlined />} size="small">
                        选择文件
                      </Button>
                    </Upload>
                    {uploadedFile && (
                      <Text type="success" style={{ fontSize: '12px' }}>
                        已上传: {uploadedFile.filename}
                      </Text>
                    )}
                  </Form.Item>
                  
                  <Form.Item label="处理模式" name="processing_mode">
                    <Select placeholder="选择处理模式" size="small">
                      <Option value="auto">自动选择</Option>
                      <Option value="template_only">仅模板匹配</Option>
                      <Option value="ai_only">仅AI识别</Option>
                    </Select>
                  </Form.Item>
                  
                  <Button 
                    type="primary" 
                    icon={<PlayCircleOutlined />}
                    onClick={startAIPreviewTask}
                    disabled={!uploadedFile}
                    size="small"
                  >
                    启动AI处理
                  </Button>
                </Form>
              </Card>

              <Divider />

              {/* 模拟任务演示 */}
              <Card size="small" title="模拟任务" type="inner">
                <Space wrap>
                  <Button 
                    onClick={() => startDemoTask('data_analysis')}
                    size="small"
                  >
                    数据分析任务
                  </Button>
                  <Button 
                    onClick={() => startDemoTask('report_generation')}
                    size="small"
                  >
                    报告生成任务
                  </Button>
                  <Button 
                    onClick={() => startDemoTask('batch_processing')}
                    size="small"
                  >
                    批量处理任务
                  </Button>
                </Space>
                <Divider style={{ margin: '12px 0' }} />
                <Button 
                  onClick={clearCompletedTasks}
                  size="small"
                  type="text"
                >
                  清理已完成任务
                </Button>
              </Card>
            </Space>
          </Card>

          {/* 架构优势说明 */}
          <Card title="架构优势" size="small">
            <Space direction="vertical" style={{ width: '100%' }}>
              <div>
                <Text strong>🚀 性能优势</Text>
                <ul style={{ fontSize: '12px', marginTop: 4 }}>
                  <li>HTTP连接不再长时间占用</li>
                  <li>支持高并发任务处理</li>
                  <li>服务器资源使用更合理</li>
                </ul>
              </div>
              
              <div>
                <Text strong>👤 用户体验</Text>
                <ul style={{ fontSize: '12px', marginTop: 4 }}>
                  <li>实时查看处理进度</li>
                  <li>可以取消长时间任务</li>
                  <li>页面刷新后任务状态保持</li>
                </ul>
              </div>
              
              <div>
                <Text strong>🔧 开发友好</Text>
                <ul style={{ fontSize: '12px', marginTop: 4 }}>
                  <li>统一的任务管理接口</li>
                  <li>简化的前端Hook</li>
                  <li>可复用的进度组件</li>
                </ul>
              </div>
            </Space>
          </Card>
        </Col>

        {/* 右侧：任务状态面板 */}
        <Col span={12}>
          <Card title="任务执行状态" style={{ marginBottom: 24 }}>
            {demoTasks.size === 0 ? (
              <div style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}>
                暂无执行中的任务
              </div>
            ) : (
              <div style={{ maxHeight: '400px', overflowY: 'auto' }}>
                {Array.from(demoTasks.values())
                  .sort((a, b) => b.startTime - a.startTime)
                  .map(task => (
                    <div key={task.id}>
                      {renderTaskStatus(task)}
                    </div>
                  ))}
              </div>
            )}
          </Card>

          {/* 技术说明 */}
          <Card title="技术实现" size="small">
            <Space direction="vertical" style={{ width: '100%' }}>
              <div>
                <Text strong>后端架构</Text>
                <ul style={{ fontSize: '12px', marginTop: 4 }}>
                  <li>AsyncTask模型存储任务状态</li>
                  <li>AsyncTaskService管理任务生命周期</li>
                  <li>任务执行器分离具体业务逻辑</li>
                </ul>
              </div>
              
              <div>
                <Text strong>前端架构</Text>
                <ul style={{ fontSize: '12px', marginTop: 4 }}>
                  <li>AsyncTaskService轮询管理</li>
                  <li>useAsyncTask Hook状态管理</li>
                  <li>AsyncTaskProgress组件显示</li>
                </ul>
              </div>
            </Space>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default AsyncTaskDemo; 