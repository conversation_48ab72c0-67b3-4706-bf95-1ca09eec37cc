#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
例行任务模型
"""

import uuid
from datetime import datetime
from typing import List, Optional
from sqlalchemy import Column, String, Text, ForeignKey, DateTime, Enum, Boolean, Integer
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship
from sqlalchemy.ext.declarative import declared_attr

from db.database import Base

class RoutineTask(Base):
    """
    例行任务模型
    """
    __tablename__ = "routine_tasks"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    title = Column(String(100), nullable=False, comment="任务标题")
    description = Column(Text, nullable=True, comment="任务描述")

    # 任务类型：store(门店任务), inventory(库存任务), marketing(营销任务), etc.
    category = Column(String(50), nullable=False, comment="任务类型")

    # 任务优先级：low, medium, high
    priority = Column(String(20), nullable=False, default="medium", comment="任务优先级")

    # 任务状态：pending(待处理), in_progress(进行中), completed(已完成), cancelled(已取消)
    status = Column(String(20), nullable=False, default="pending", comment="任务状态")

    # 任务开始时间
    start_date = Column(DateTime, nullable=True, comment="任务开始时间")

    # 任务截止时间
    due_date = Column(DateTime, nullable=True, comment="任务截止时间")

    # 任务完成时间
    completed_date = Column(DateTime, nullable=True, comment="任务完成时间")

    # 任务创建时间
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")

    # 任务更新时间
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")

    # 任务创建者
    created_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True, comment="创建者ID")

    # 任务更新者
    updated_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=True, comment="更新者ID")

    # 任务所属项目
    project_id = Column(UUID(as_uuid=True), ForeignKey("projects.id"), nullable=False, comment="项目ID")

    # 任务所属门店
    store_id = Column(UUID(as_uuid=True), ForeignKey("stores.id"), nullable=True, comment="门店ID（单个门店，向后兼容）")

    # 任务所属多个门店
    store_ids = Column(JSONB, default=list, comment="门店ID列表（多个门店）")

    # 是否适用于所有门店
    is_all_stores = Column(Boolean, default=False, comment="是否适用于所有门店")

    # 任务负责人
    assignee_ids = Column(JSONB, default=list, comment="负责人ID列表")

    # 任务标签
    tags = Column(JSONB, default=list, comment="标签列表")

    # 任务附件
    attachments = Column(JSONB, default=list, comment="附件列表")

    # 任务是否重复
    is_recurring = Column(Boolean, default=False, comment="是否重复任务")

    # 重复频率：daily(每天), weekly(每周), monthly(每月), etc.
    recurrence_pattern = Column(String(20), nullable=True, comment="重复频率")

    # 重复间隔
    recurrence_interval = Column(Integer, nullable=True, comment="重复间隔")

    # 重复结束日期
    recurrence_end_date = Column(DateTime, nullable=True, comment="重复结束日期")

    # 重复次数
    recurrence_count = Column(Integer, nullable=True, comment="重复次数")

    # 自定义字段
    custom_fields = Column(JSONB, default=dict, comment="自定义字段")

    # 关系
    # 使用字符串引用类名，并明确指定外键列
    project = relationship("Project", foreign_keys=[project_id], back_populates="routine_tasks")
    store = relationship("Store", foreign_keys=[store_id], back_populates="routine_tasks")
    creator = relationship("User", foreign_keys=[created_by])
    updater = relationship("User", foreign_keys=[updated_by])
    comments = relationship("TaskComment", back_populates="task", cascade="all, delete-orphan")

    def __repr__(self):
        return f"<RoutineTask {self.title}>"

class TaskComment(Base):
    """
    任务评论模型
    """
    __tablename__ = "task_comments"

    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    content = Column(Text, nullable=False, comment="评论内容")

    # 评论创建时间
    created_at = Column(DateTime, default=datetime.utcnow, comment="创建时间")

    # 评论更新时间
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, comment="更新时间")

    # 评论创建者
    created_by = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False, comment="创建者ID")

    # 评论所属任务
    task_id = Column(UUID(as_uuid=True), ForeignKey("routine_tasks.id"), nullable=False, comment="任务ID")

    # 评论附件
    attachments = Column(JSONB, default=list, comment="附件列表")

    # 关系
    task = relationship("RoutineTask", back_populates="comments")
    creator = relationship("User", foreign_keys=[created_by])

    def __repr__(self):
        return f"<TaskComment {self.id}>"
