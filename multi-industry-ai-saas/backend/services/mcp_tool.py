#!/usr/bin/env python
# -*- coding: utf-8 -*-

import asyncio
import json
import time
from typing import List, Optional, Dict, Any, <PERSON><PERSON>
from uuid import UUID
import httpx
import websockets
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, and_, or_, func
from sqlalchemy.orm import selectinload

from models.mcp_tool import MCPTool, MCPToolUsage
from schemas.mcp_tool import MCPToolCreate, MCPToolUpdate, MCPToolExecuteRequest


class MCPToolService:
    """MCP工具服务"""

    @staticmethod
    async def get_tools(
        db: AsyncSession,
        project_id: UUID,
        skip: int = 0,
        limit: int = 20,
        name: Optional[str] = None,
        server_type: Optional[str] = None,
        is_enabled: Optional[bool] = None,
        tags: Optional[List[str]] = None
    ) -> Tuple[List[MCPTool], int]:
        """获取MCP工具列表"""
        
        # 构建查询条件
        conditions = [
            or_(
                MCPTool.project_id == project_id,
                MCPTool.project_id.is_(None)  # 系统级工具
            )
        ]
        
        if name:
            conditions.append(MCPTool.name.ilike(f"%{name}%"))
        
        if server_type:
            conditions.append(MCPTool.server_type == server_type)
        
        if is_enabled is not None:
            conditions.append(MCPTool.is_enabled == is_enabled)
        
        if tags:
            # 检查tags字段包含任一指定标签
            tag_conditions = []
            for tag in tags:
                tag_conditions.append(MCPTool.tags.contains([tag]))
            conditions.append(or_(*tag_conditions))
        
        # 查询总数
        count_query = select(func.count()).select_from(
            select(MCPTool).filter(and_(*conditions)).subquery()
        )
        total_result = await db.execute(count_query)
        total = total_result.scalar()
        
        # 查询数据
        query = select(MCPTool).filter(and_(*conditions)).order_by(
            MCPTool.is_builtin.desc(),
            MCPTool.created_at.desc()
        ).offset(skip).limit(limit)
        
        result = await db.execute(query)
        tools = result.scalars().all()
        
        return tools, total

    @staticmethod
    async def get_tool(db: AsyncSession, tool_id: UUID) -> Optional[MCPTool]:
        """获取单个MCP工具"""
        query = select(MCPTool).filter(MCPTool.id == tool_id)
        result = await db.execute(query)
        return result.scalars().first()

    @staticmethod
    async def create_tool(
        db: AsyncSession,
        tool_data: MCPToolCreate,
        user_id: UUID
    ) -> MCPTool:
        """创建MCP工具"""
        
        # 检查工具名称是否已存在
        existing_query = select(MCPTool).filter(
            and_(
                MCPTool.name == tool_data.name,
                or_(
                    MCPTool.project_id == tool_data.project_id,
                    MCPTool.project_id.is_(None)
                )
            )
        )
        existing_result = await db.execute(existing_query)
        existing_tool = existing_result.scalars().first()
        
        if existing_tool:
            raise ValueError(f"工具名称 '{tool_data.name}' 已存在")
        
        # 创建工具
        tool = MCPTool(
            **tool_data.dict(exclude={'project_id', 'tenant_id'}),
            project_id=tool_data.project_id,
            tenant_id=tool_data.tenant_id,
            created_by=user_id
        )
        
        db.add(tool)
        await db.commit()
        await db.refresh(tool)
        
        return tool

    @staticmethod
    async def update_tool(
        db: AsyncSession,
        tool_id: UUID,
        tool_data: MCPToolUpdate,
        user_id: UUID
    ) -> Optional[MCPTool]:
        """更新MCP工具"""
        
        tool = await MCPToolService.get_tool(db, tool_id)
        if not tool:
            return None
        
        # 更新字段
        update_data = tool_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(tool, field, value)
        
        tool.updated_by = user_id
        
        await db.commit()
        await db.refresh(tool)
        
        return tool

    @staticmethod
    async def delete_tool(db: AsyncSession, tool_id: UUID) -> bool:
        """删除MCP工具"""
        
        tool = await MCPToolService.get_tool(db, tool_id)
        if not tool:
            return False
        
        # 检查是否为内置工具
        if tool.is_builtin:
            raise ValueError("不能删除内置工具")
        
        await db.delete(tool)
        await db.commit()
        
        return True

    @staticmethod
    async def test_tool_connection(
        server_url: str,
        server_type: str = "http",
        auth_type: str = "none",
        auth_config: Optional[Dict[str, Any]] = None,
        timeout: int = 30
    ) -> Dict[str, Any]:
        """测试MCP工具连接"""
        
        start_time = time.time()
        
        try:
            if server_type == "http":
                return await MCPToolService._test_http_connection(
                    server_url, auth_type, auth_config, timeout
                )
            elif server_type == "websocket":
                return await MCPToolService._test_websocket_connection(
                    server_url, auth_type, auth_config, timeout
                )
            else:
                return {
                    "success": False,
                    "message": f"不支持的服务器类型: {server_type}",
                    "response_time": int((time.time() - start_time) * 1000)
                }
        
        except Exception as e:
            return {
                "success": False,
                "message": f"连接测试失败: {str(e)}",
                "response_time": int((time.time() - start_time) * 1000)
            }

    @staticmethod
    async def _test_http_connection(
        server_url: str,
        auth_type: str,
        auth_config: Optional[Dict[str, Any]],
        timeout: int
    ) -> Dict[str, Any]:
        """测试HTTP连接"""
        
        headers = {"Content-Type": "application/json"}
        
        # 添加认证头
        if auth_type == "api_key" and auth_config:
            api_key = auth_config.get("api_key")
            key_header = auth_config.get("header", "Authorization")
            if api_key:
                headers[key_header] = f"Bearer {api_key}"
        
        async with httpx.AsyncClient(timeout=timeout) as client:
            # 尝试获取工具列表
            response = await client.post(
                f"{server_url}/tools/list",
                headers=headers,
                json={}
            )
            
            if response.status_code == 200:
                data = response.json()
                return {
                    "success": True,
                    "message": "连接成功",
                    "available_tools": data.get("tools", []),
                    "server_info": data.get("server_info", {}),
                    "response_time": int(response.elapsed.total_seconds() * 1000)
                }
            else:
                return {
                    "success": False,
                    "message": f"HTTP错误: {response.status_code}",
                    "response_time": int(response.elapsed.total_seconds() * 1000)
                }

    @staticmethod
    async def _test_websocket_connection(
        server_url: str,
        auth_type: str,
        auth_config: Optional[Dict[str, Any]],
        timeout: int
    ) -> Dict[str, Any]:
        """测试WebSocket连接"""
        
        try:
            # 构建WebSocket URL
            ws_url = server_url.replace("http://", "ws://").replace("https://", "wss://")
            
            async with websockets.connect(ws_url, timeout=timeout) as websocket:
                # 发送工具列表请求
                request = {
                    "jsonrpc": "2.0",
                    "id": 1,
                    "method": "tools/list",
                    "params": {}
                }
                
                await websocket.send(json.dumps(request))
                response = await websocket.recv()
                data = json.loads(response)
                
                if "result" in data:
                    return {
                        "success": True,
                        "message": "连接成功",
                        "available_tools": data["result"].get("tools", []),
                        "server_info": data["result"].get("server_info", {})
                    }
                else:
                    return {
                        "success": False,
                        "message": f"WebSocket错误: {data.get('error', '未知错误')}"
                    }
        
        except Exception as e:
            return {
                "success": False,
                "message": f"WebSocket连接失败: {str(e)}"
            }

    @staticmethod
    async def execute_tool(
        db: AsyncSession,
        project_id: UUID,
        request: MCPToolExecuteRequest,
        user_id: UUID
    ) -> Dict[str, Any]:
        """执行MCP工具"""
        
        # 获取工具
        tool = await MCPToolService.get_tool(db, request.tool_id)
        if not tool:
            raise ValueError(f"工具 ID '{request.tool_id}' 不存在")
        
        if not tool.is_enabled:
            raise ValueError("工具已禁用")
        
        # 创建使用记录
        usage = MCPToolUsage(
            project_id=project_id,
            tenant_id=tool.tenant_id,
            tool_id=request.tool_id,
            session_id=request.session_id,
            request_data=request.parameters,
            status="pending",
            created_by=user_id
        )
        
        db.add(usage)
        await db.commit()
        await db.refresh(usage)
        
        start_time = time.time()
        
        try:
            # 执行工具
            if tool.server_type == "http":
                result = await MCPToolService._execute_http_tool(
                    tool, request.parameters, request.timeout or tool.timeout
                )
            elif tool.server_type == "websocket":
                result = await MCPToolService._execute_websocket_tool(
                    tool, request.parameters, request.timeout or tool.timeout
                )
            else:
                raise ValueError(f"不支持的服务器类型: {tool.server_type}")
            
            execution_time = int((time.time() - start_time) * 1000)
            
            # 更新使用记录
            usage.response_data = result
            usage.status = "success"
            usage.execution_time = execution_time
            
            await db.commit()
            
            return {
                "success": True,
                "message": "工具执行成功",
                "data": result,
                "execution_time": execution_time,
                "usage_id": usage.id
            }
        
        except Exception as e:
            execution_time = int((time.time() - start_time) * 1000)
            
            # 更新使用记录
            usage.status = "failed"
            usage.error_message = str(e)
            usage.execution_time = execution_time
            
            await db.commit()
            
            return {
                "success": False,
                "message": f"工具执行失败: {str(e)}",
                "execution_time": execution_time,
                "usage_id": usage.id
            }

    @staticmethod
    async def _execute_http_tool(
        tool: MCPTool,
        parameters: Dict[str, Any],
        timeout: int
    ) -> Dict[str, Any]:
        """执行HTTP工具"""
        
        headers = {"Content-Type": "application/json"}
        
        # 添加认证头
        if tool.auth_type == "api_key" and tool.auth_config:
            api_key = tool.auth_config.get("api_key")
            key_header = tool.auth_config.get("header", "Authorization")
            if api_key:
                headers[key_header] = f"Bearer {api_key}"
        
        async with httpx.AsyncClient(timeout=timeout) as client:
            response = await client.post(
                f"{tool.server_url}/tools/call",
                headers=headers,
                json={
                    "name": tool.name,
                    "arguments": parameters
                }
            )
            
            if response.status_code == 200:
                return response.json()
            else:
                raise Exception(f"HTTP错误: {response.status_code} - {response.text}")

    @staticmethod
    async def _execute_websocket_tool(
        tool: MCPTool,
        parameters: Dict[str, Any],
        timeout: int
    ) -> Dict[str, Any]:
        """执行WebSocket工具"""
        
        ws_url = tool.server_url.replace("http://", "ws://").replace("https://", "wss://")
        
        async with websockets.connect(ws_url, timeout=timeout) as websocket:
            request = {
                "jsonrpc": "2.0",
                "id": 1,
                "method": "tools/call",
                "params": {
                    "name": tool.name,
                    "arguments": parameters
                }
            }
            
            await websocket.send(json.dumps(request))
            response = await websocket.recv()
            data = json.loads(response)
            
            if "result" in data:
                return data["result"]
            else:
                raise Exception(f"WebSocket错误: {data.get('error', '未知错误')}")

    @staticmethod
    async def get_tool_usages(
        db: AsyncSession,
        project_id: UUID,
        skip: int = 0,
        limit: int = 20,
        tool_id: Optional[UUID] = None,
        status: Optional[str] = None,
        session_id: Optional[str] = None
    ) -> Tuple[List[MCPToolUsage], int]:
        """获取工具使用记录"""
        
        conditions = [MCPToolUsage.project_id == project_id]
        
        if tool_id:
            conditions.append(MCPToolUsage.tool_id == tool_id)
        
        if status:
            conditions.append(MCPToolUsage.status == status)
        
        if session_id:
            conditions.append(MCPToolUsage.session_id == session_id)
        
        # 查询总数
        count_query = select(func.count()).select_from(
            select(MCPToolUsage).filter(and_(*conditions)).subquery()
        )
        total_result = await db.execute(count_query)
        total = total_result.scalar()
        
        # 查询数据
        query = select(MCPToolUsage).options(
            selectinload(MCPToolUsage.tool)
        ).filter(and_(*conditions)).order_by(
            MCPToolUsage.created_at.desc()
        ).offset(skip).limit(limit)
        
        result = await db.execute(query)
        usages = result.scalars().all()
        
        return usages, total 