import marketingGameApi from './marketingGame';
import api from '../../../../../../services/api/httpClient';

/**
 * AI助手API服务
 */
const aiAssistantApi = {
  /**
   * 初始化聊天
   * @param {string} gameId - 游戏ID
   * @returns {Promise} - 初始化结果
   */
  initializeChat: async (gameId) => {
    try {
      const projectId = localStorage.getItem('project_id');
      if (!projectId) {
        return {
          success: false,
          message: '获取项目ID失败'
        };
      }

      // 获取游戏详情以提供个性化欢迎消息
      let gameInfo = null;
      if (gameId) {
        try {
          const gameResponse = await marketingGameApi.getGameDetail(gameId);
          if (gameResponse.success) {
            gameInfo = gameResponse.data;
          }
        } catch (error) {
          console.warn('获取游戏详情失败:', error);
        }
      }

      const welcomeMessage = gameInfo 
        ? `🎮 您好！欢迎来到"${gameInfo.name}"营销游戏！我是您的AI助手，可以帮您分析游戏数据、优化设置，或者创建新的营销游戏。有什么问题都可以问我！`
        : '🎮 您好！欢迎使用营销游戏AI助手！我可以帮您：\n\n📊 分析游戏数据和效果\n🎯 提供营销策略建议\n🎮 创建和设置新游戏\n💡 优化现有游戏配置\n\n请告诉我您需要什么帮助？';

      return {
        success: true,
        data: {
          conversation_id: `conv_${Date.now()}`,
          welcome_message: welcomeMessage,
          personality: 'friendly',
          quick_replies: gameId ? [
            { text: '分析这个游戏的数据', type: 'analysis' },
            { text: '如何提高参与率？', type: 'optimization' },
            { text: '给我一些改进建议', type: 'suggestion' },
            { text: '创建一个新游戏', type: 'create' }
          ] : [
            { text: '我想创建一个抽奖游戏', type: 'create' },
            { text: '如何设计有效的营销活动？', type: 'strategy' },
            { text: '分析我的所有游戏', type: 'analysis' },
            { text: '给我一些营销建议', type: 'suggestion' }
          ]
        }
      };
    } catch (error) {
      console.error('初始化AI助手失败:', error);
      return {
        success: false,
        message: error.message || '初始化失败'
      };
    }
  },

  /**
   * 发送消息
   * @param {string} gameId - 游戏ID
   * @param {Object} data - 消息数据
   * @returns {Promise} - 回复结果
   */
  sendMessage: async (gameId, data) => {
    try {
      const projectId = localStorage.getItem('project_id');
      if (!projectId) {
        return {
          success: false,
          message: '获取项目ID失败'
        };
      }

      const message = data.message.toLowerCase();
      
      // 分析用户意图并提供相应回复
      const intent = aiAssistantApi.analyzeIntent(message);
      const response = await aiAssistantApi.generateResponse(intent, message, gameId);

      return {
        success: true,
        data: response
      };
    } catch (error) {
      console.error('发送消息失败:', error);
      return {
        success: false,
        message: error.message || '发送失败'
      };
    }
  },

  /**
   * 分析用户意图
   * @param {string} message - 用户消息
   * @returns {string} - 意图类型
   */
  analyzeIntent: (message) => {
    const keywords = {
      create: ['创建', '新建', '制作', '设计', '开始'],
      analysis: ['分析', '数据', '统计', '效果', '报告'],
      optimization: ['优化', '改进', '提高', '增加', '提升'],
      strategy: ['策略', '方案', '建议', '怎么', '如何'],
      prize: ['奖品', '奖励', '礼品', '中奖'],
      share: ['分享', '传播', '推广', '邀请'],
      rules: ['规则', '玩法', '流程', '步骤'],
      help: ['帮助', '不懂', '不会', '教我']
    };

    for (const [intent, words] of Object.entries(keywords)) {
      if (words.some(word => message.includes(word))) {
        return intent;
      }
    }

    return 'general';
  },

  /**
   * 生成AI回复
   * @param {string} intent - 用户意图
   * @param {string} message - 原始消息
   * @param {string} gameId - 游戏ID
   * @returns {Promise} - 回复内容
   */
  generateResponse: async (intent, message, gameId) => {
    switch (intent) {
      case 'create':
        return await aiAssistantApi.handleCreateGame(message);
      case 'analysis':
        return await aiAssistantApi.handleAnalysis(gameId);
      case 'optimization':
        return await aiAssistantApi.handleOptimization(gameId);
      case 'strategy':
        return await aiAssistantApi.handleStrategy(message);
      case 'prize':
        return await aiAssistantApi.handlePrizeQuestions(gameId);
      case 'share':
        return await aiAssistantApi.handleShareQuestions(gameId);
      case 'rules':
        return await aiAssistantApi.handleRulesQuestions(gameId);
      default:
        return await aiAssistantApi.handleGeneralQuestions(message);
    }
  },

  /**
   * 处理游戏创建对话
   */
  handleCreateGame: async (message) => {
    const gameTypes = {
      '抽奖': 'lucky_draw',
      '转盘': 'wheel_spin', 
      '刮刮卡': 'scratch_card',
      '砸金蛋': 'golden_egg'
    };

    let detectedType = null;
    for (const [name, type] of Object.entries(gameTypes)) {
      if (message.includes(name)) {
        detectedType = { name, type };
        break;
      }
    }

    if (detectedType) {
      return {
        reply: `好的！我来帮您创建一个${detectedType.name}游戏。\n\n为了给您设计最合适的游戏，请告诉我：\n\n1️⃣ 这个活动的主要目的是什么？（如：吸引新客户、促进销售、提高品牌知名度）\n2️⃣ 预计参与人数大概多少？\n3️⃣ 活动预算范围是多少？\n4️⃣ 希望活动持续多长时间？\n\n您可以一次回答，也可以一个一个来！`,
        suggestions: ['吸引新客户', '促进销售', '提高品牌知名度'],
        actions: [
          {
            text: '直接创建基础版',
            type: 'create_basic',
            data: { gameType: detectedType.type }
          },
          {
            text: '创建增强版',
            type: 'create_enhanced', 
            data: { gameType: detectedType.type }
          }
        ]
      };
    } else {
      return {
        reply: `我来帮您创建营销游戏！首先，请选择您想要的游戏类型：\n\n🎲 **抽奖游戏** - 经典的抽奖模式，简单易懂\n🎡 **转盘游戏** - 视觉效果好，互动性强\n🎫 **刮刮卡** - 趣味性高，参与感强\n🥚 **砸金蛋** - 新颖有趣，吸引力大\n\n请告诉我您想要哪种类型？`,
        suggestions: ['抽奖游戏', '转盘游戏', '刮刮卡', '砸金蛋'],
        actions: []
      };
    }
  },

  /**
   * 处理数据分析请求
   */
  handleAnalysis: async (gameId) => {
    try {
      if (!gameId) {
        // 获取所有游戏的概览数据
        const projectId = localStorage.getItem('project_id');
        const response = await api.get(`/project/${projectId}/plugin/marketing-game/ai-overview`);
        
        if (response.data) {
          const data = response.data;
          return {
            reply: `📊 **您的营销游戏总览**\n\n🎮 总游戏数：${data.total_games}\n✅ 活跃游戏：${data.active_games}\n👥 总参与人数：${data.total_participants}\n⭐ 综合评分：${data.overall_score}\n\n需要我分析特定游戏的详细数据吗？`,
            suggestions: ['查看游戏列表', '创建新游戏', '优化建议'],
            actions: [
              {
                text: '查看详细报告',
                type: 'detailed_report'
              }
            ]
          };
        } else {
          return {
            reply: `看起来您还没有创建任何营销游戏。\n\n我可以帮您：\n• 创建第一个营销游戏\n• 了解不同游戏类型的特点\n• 制定营销策略\n\n您想从哪里开始？`,
            suggestions: ['创建第一个游戏', '了解游戏类型', '制定营销策略'],
            actions: []
          };
        }
      } else {
        // 分析特定游戏
        const projectId = localStorage.getItem('project_id');
        const [gameResponse, analysisResponse] = await Promise.all([
          marketingGameApi.getGameDetail(gameId),
          api.get(`/project/${projectId}/plugin/marketing-game/games/${gameId}/ai-analysis`)
        ]);

        if (gameResponse.success && analysisResponse.data) {
          const game = gameResponse.data;
          const analysis = analysisResponse.data;
          
          return {
            reply: `📊 **"${game.name}"游戏数据分析**\n\n👥 总参与人数：${analysis.participants}\n🏆 中奖率：${analysis.win_rate}%\n📤 分享率：${analysis.share_rate}%\n⭐ 综合评分：${analysis.overall_score}\n\n**分析建议：**\n${aiAssistantApi.generateAnalysisInsights(analysis.participants, analysis.win_rate, 0)}`,
            suggestions: ['优化中奖率', '增加参与度', '查看详细记录'],
            actions: [
              {
                text: '导出数据报告',
                type: 'export_report'
              }
            ]
          };
        } else {
          return {
            reply: '抱歉，无法获取游戏数据。请检查游戏是否存在或稍后重试。',
            suggestions: ['重新尝试', '查看其他游戏'],
            actions: []
          };
        }
      }
    } catch (error) {
      console.error('分析数据失败:', error);
      return {
        reply: '数据分析时出现错误，请稍后重试。',
        suggestions: ['重新分析', '联系技术支持'],
        actions: []
      };
    }
  },

  /**
   * 生成分析洞察
   */
  generateAnalysisInsights: (participants, winRate, todayParticipants) => {
    const insights = [];
    
    if (participants < 50) {
      insights.push('• 参与人数较少，建议加强推广');
    } else if (participants > 500) {
      insights.push('• 参与度很高，效果不错！');
    }
    
    if (winRate < 10) {
      insights.push('• 中奖率偏低，可能影响用户积极性');
    } else if (winRate > 30) {
      insights.push('• 中奖率较高，注意控制成本');
    }
    
    if (todayParticipants === 0) {
      insights.push('• 今日无新参与，建议检查推广渠道');
    }
    
    return insights.length > 0 ? insights.join('\n') : '• 游戏数据表现正常';
  },

  /**
   * 处理优化建议
   */
  handleOptimization: async (gameId) => {
    if (!gameId) {
      return {
        reply: `我可以为您提供营销游戏优化建议！\n\n**常见优化方向：**\n\n🎯 **提高参与率**\n• 优化游戏界面设计\n• 简化参与流程\n• 增加分享激励\n\n🏆 **提升转化效果**\n• 调整奖品设置\n• 优化中奖概率\n• 添加引导文案\n\n📱 **增强用户体验**\n• 优化移动端适配\n• 加快加载速度\n• 完善反馈机制\n\n请告诉我您想优化哪个方面？`,
        suggestions: ['提高参与率', '提升转化效果', '增强用户体验'],
        actions: []
      };
    }

    try {
      const projectId = localStorage.getItem('project_id');
      const [gameResponse, suggestionsResponse] = await Promise.all([
        marketingGameApi.getGameDetail(gameId),
        api.get(`/project/${projectId}/plugin/marketing-game/games/${gameId}/ai-suggestions`)
      ]);
      
      if (gameResponse.success && suggestionsResponse.data) {
        const game = gameResponse.data;
        const suggestions = suggestionsResponse.data;
        
        if (suggestions.length === 0) {
          return {
            reply: `🎉 **"${game.name}"表现优秀！**\n\n当前游戏配置良好，暂无需要优化的地方。\n\n您可以：\n• 继续监控游戏数据\n• 考虑创建新的营销活动\n• 分享成功经验`,
            suggestions: ['监控数据', '创建新游戏', '查看报告'],
            actions: []
          };
        }
        
        const suggestionText = suggestions.map((s, i) => `${i + 1}. **${s.title}**\n   ${s.message}`).join('\n\n');
        
        return {
          reply: `🔧 **"${game.name}"优化建议**\n\n${suggestionText}\n\n需要我详细解释某个建议吗？`,
          suggestions: ['详细解释', '立即应用', '查看更多建议'],
          actions: [
            {
              text: '应用建议',
              type: 'apply_suggestions'
            }
          ]
        };
      }
    } catch (error) {
      console.error('获取优化建议失败:', error);
    }

    return {
      reply: '无法获取游戏信息，请稍后重试。',
      suggestions: ['重新尝试'],
      actions: []
    };
  },

  /**
   * 处理策略咨询
   */
  handleStrategy: async (message) => {
    return {
      reply: `💡 **营销策略建议**\n\n基于您的问题，我推荐以下策略：\n\n**1. 目标设定**\n• 明确活动目标（拉新/促活/转化）\n• 设定可量化的KPI指标\n\n**2. 用户画像**\n• 分析目标用户群体\n• 了解用户偏好和行为\n\n**3. 奖品策略**\n• 设置有吸引力的主奖\n• 增加中等价值奖品数量\n• 控制成本与效果平衡\n\n**4. 推广渠道**\n• 多渠道同步推广\n• 利用社交媒体传播\n• 线上线下结合\n\n需要我详细解释某个策略吗？`,
      suggestions: ['目标设定方法', '用户画像分析', '奖品策略制定', '推广渠道选择'],
      actions: []
    };
  },

  /**
   * 处理奖品相关问题
   */
  handlePrizeQuestions: async (gameId) => {
    if (!gameId) {
      return {
        reply: `🎁 **奖品设置建议**\n\n**奖品类型选择：**\n• 实物奖品：商品、礼品卡、优惠券\n• 虚拟奖品：积分、会员权益、服务\n• 体验奖品：免费试用、专属服务\n\n**奖品配置原则：**\n• 主奖吸引眼球（1-2个高价值）\n• 普奖保证参与感（多个中低价值）\n• 安慰奖维持热情（优惠券、积分）\n\n需要帮您设计具体的奖品方案吗？`,
        suggestions: ['设计奖品方案', '计算奖品成本', '优化中奖率'],
        actions: []
      };
    }

    try {
      const prizesResponse = await marketingGameApi.getGamePrizes(gameId);
      if (prizesResponse.success) {
        const prizes = prizesResponse.data || [];
        
        if (prizes.length === 0) {
          return {
            reply: `这个游戏还没有设置奖品。\n\n我建议您添加以下奖品：\n• 1个主奖（高价值，低概率）\n• 2-3个普通奖品（中等价值）\n• 1个安慰奖（优惠券或积分）\n\n需要我帮您设置奖品吗？`,
            suggestions: ['设置奖品', '查看奖品模板', '计算成本'],
            actions: [
              {
                text: '立即设置奖品',
                type: 'setup_prizes'
              }
            ]
          };
        } else {
          const prizeList = prizes.map(p => `• ${p.name}：${p.probability}% 中奖率`).join('\n');
          return {
            reply: `🎁 **当前奖品设置：**\n\n${prizeList}\n\n**分析：**\n${this.analyzePrizeSetup(prizes)}\n\n需要调整奖品设置吗？`,
            suggestions: ['调整中奖率', '添加新奖品', '删除奖品'],
            actions: [
              {
                text: '编辑奖品',
                type: 'edit_prizes'
              }
            ]
          };
        }
      }
    } catch (error) {
      console.error('获取奖品信息失败:', error);
    }

    return {
      reply: '无法获取奖品信息，请稍后重试。',
      suggestions: ['重新尝试'],
      actions: []
    };
  },

  /**
   * 分析奖品设置
   */
  analyzePrizeSetup: (prizes) => {
    const totalProbability = prizes.reduce((sum, p) => sum + p.probability, 0);
    const analysis = [];
    
    if (totalProbability > 100) {
      analysis.push('⚠️ 总中奖率超过100%，请调整');
    } else if (totalProbability < 50) {
      analysis.push('📈 中奖率较低，可能影响参与积极性');
    } else {
      analysis.push('✅ 中奖率设置合理');
    }
    
    if (prizes.length < 3) {
      analysis.push('💡 建议增加奖品种类，提高吸引力');
    }
    
    return analysis.join('\n');
  },

  /**
   * 处理分享相关问题
   */
  handleShareQuestions: async (gameId) => {
    return {
      reply: `📤 **分享功能优化建议**\n\n**分享激励机制：**\n• 分享获得额外抽奖机会\n• 好友参与获得奖励\n• 分享达到一定数量解锁特殊奖品\n\n**分享内容优化：**\n• 设计吸引人的分享图片\n• 编写有趣的分享文案\n• 突出奖品和中奖机会\n\n**分享渠道：**\n• 微信朋友圈\n• 微信群\n• QQ空间\n• 微博\n\n需要我帮您设置分享功能吗？`,
      suggestions: ['设置分享奖励', '优化分享内容', '增加分享渠道'],
      actions: [
        {
          text: '配置分享功能',
          type: 'setup_sharing'
        }
      ]
    };
  },

  /**
   * 处理规则相关问题
   */
  handleRulesQuestions: async (gameId) => {
    if (!gameId) {
      return {
        reply: `📋 **游戏规则设计建议**\n\n**基本规则要素：**\n• 参与条件（年龄、地区、会员等）\n• 参与次数限制\n• 活动时间\n• 奖品说明\n• 中奖规则\n\n**规则设计原则：**\n• 简单易懂\n• 公平公正\n• 合法合规\n• 可操作性强\n\n需要我帮您制定具体规则吗？`,
        suggestions: ['制定参与规则', '设置时间限制', '编写规则说明'],
        actions: []
      };
    }

    try {
      const gameResponse = await marketingGameApi.getGameDetail(gameId);
      if (gameResponse.success) {
        const game = gameResponse.data;
        
        return {
          reply: `📋 **"${game.name}"游戏规则**\n\n• 游戏类型：${this.getGameTypeName(game.game_type)}\n• 每人最多参与：${game.max_draws_per_user || 1}次\n• 活动时间：${new Date(game.start_time).toLocaleDateString()} - ${new Date(game.end_time).toLocaleDateString()}\n• 参与条件：${game.description || '无特殊限制'}\n\n需要修改规则吗？`,
          suggestions: ['修改参与次数', '调整活动时间', '更新参与条件'],
          actions: [
            {
              text: '编辑游戏规则',
              type: 'edit_rules'
            }
          ]
        };
      }
    } catch (error) {
      console.error('获取游戏规则失败:', error);
    }

    return {
      reply: '无法获取游戏规则，请稍后重试。',
      suggestions: ['重新尝试'],
      actions: []
    };
  },

  /**
   * 获取游戏类型名称
   */
  getGameTypeName: (type) => {
    const typeNames = {
      'lucky_draw': '抽奖游戏',
      'wheel_spin': '转盘游戏',
      'scratch_card': '刮刮卡',
      'golden_egg': '砸金蛋'
    };
    return typeNames[type] || type;
  },

  /**
   * 处理一般问题
   */
  handleGeneralQuestions: async (message) => {
    return {
      reply: `我理解您的问题。作为营销游戏AI助手，我可以帮您：\n\n🎮 **游戏管理**\n• 创建和设置营销游戏\n• 分析游戏数据和效果\n• 优化游戏配置\n\n📊 **数据分析**\n• 查看参与数据\n• 分析用户行为\n• 生成效果报告\n\n💡 **策略建议**\n• 营销策略制定\n• 奖品配置建议\n• 推广方案优化\n\n请告诉我您具体需要什么帮助？`,
      suggestions: ['创建新游戏', '分析现有游戏', '获取营销建议', '查看使用教程'],
      actions: []
    };
  },

  /**
   * 获取游戏建议（真实数据）
   * @param {string} gameId - 游戏ID
   * @returns {Promise} - 建议列表
   */
  getGameSuggestions: async (gameId) => {
    try {
      if (!gameId) {
        return {
          success: true,
          data: [
            {
              id: 1,
              title: '创建第一个营销游戏',
              message: '开始您的营销之旅，创建一个吸引用户的游戏',
              type: 'create',
              priority: 'high'
            }
          ]
        };
      }

      // 调用真实的后端API获取建议
      const projectId = localStorage.getItem('project_id');
      const response = await api.get(`/project/${projectId}/plugin/marketing-game/games/${gameId}/ai-suggestions`);
      
      return {
        success: true,
        data: response.data || []
      };
    } catch (error) {
      console.error('获取游戏建议失败:', error);
      return {
        success: false,
        data: [],
        message: error.message || '获取失败'
      };
    }
  },

  /**
   * 获取游戏分析（真实数据）
   * @param {string} gameId - 游戏ID
   * @returns {Promise} - 分析结果
   */
  getGameAnalysis: async (gameId) => {
    try {
      const projectId = localStorage.getItem('project_id');
      
      if (!gameId) {
        // 获取所有游戏的汇总数据
        const response = await api.get(`/project/${projectId}/plugin/marketing-game/ai-overview`);
        return {
          success: true,
          data: response.data
        };
      }

      // 获取特定游戏的分析数据
      const response = await api.get(`/project/${projectId}/plugin/marketing-game/games/${gameId}/ai-analysis`);
      
      return {
        success: true,
        data: response.data
      };
    } catch (error) {
      console.error('获取游戏分析失败:', error);
      return {
        success: false,
        data: null,
        message: error.message || '获取失败'
      };
    }
  }
};

export default aiAssistantApi; 