#!/bin/sh

# 停止MCP服务器
echo "Stopping MCP server..."

if [ -f "pid.txt" ]; then
    pid=$(cat pid.txt)
    if ps -p $pid > /dev/null; then
        echo "Killing process $pid"
        kill $pid
        rm pid.txt
    else
        echo "Process $pid is not running"
        rm pid.txt
    fi
else
    echo "No pid.txt file found"
fi

# 查找并杀死所有Python进程
echo "Looking for Python processes..."
pkill -f "python.*main.py" || echo "No Python processes found"

echo "MCP server stopped"
