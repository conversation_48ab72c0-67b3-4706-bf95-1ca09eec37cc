#!/usr/bin/env python
# -*- coding: utf-8 -*-

from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field, UUID4
from datetime import datetime

# 知识库分类
class KnowledgeCategoryBase(BaseModel):
    name: str
    description: Optional[str] = None
    parent_id: Optional[UUID4] = None

class KnowledgeCategoryCreate(KnowledgeCategoryBase):
    pass

class KnowledgeCategoryUpdate(KnowledgeCategoryBase):
    name: Optional[str] = None

class KnowledgeCategoryResponse(KnowledgeCategoryBase):
    id: UUID4
    project_id: UUID4
    created_at: datetime
    updated_at: datetime
    
    class Config:
        model_config = {"from_attributes": True}

class KnowledgeCategoryListResponse(BaseModel):
    items: List[KnowledgeCategoryResponse]
    total: int

# 知识库文档
class KnowledgeDocumentBase(BaseModel):
    title: str
    description: Optional[str] = None
    content: Optional[str] = None
    file_url: Optional[str] = None
    file_name: Optional[str] = None
    file_type: Optional[str] = None
    is_public: bool = True
    store_ids: Optional[List[UUID4]] = None
    metadata: Optional[Dict[str, Any]] = None

class KnowledgeDocumentCreate(KnowledgeDocumentBase):
    category_id: UUID4

class KnowledgeDocumentUpdate(KnowledgeDocumentBase):
    title: Optional[str] = None
    category_id: Optional[UUID4] = None

class KnowledgeDocumentResponse(KnowledgeDocumentBase):
    id: UUID4
    project_id: UUID4
    category_id: Optional[UUID4] = None
    category_name: Optional[str] = None
    store_names: Optional[List[str]] = None
    creator_name: Optional[str] = None
    updater_name: Optional[str] = None
    created_by: Optional[UUID4] = None
    updated_by: Optional[UUID4] = None
    created_at: datetime
    updated_at: datetime
    
    class Config:
        model_config = {"from_attributes": True}

class KnowledgeDocumentListResponse(BaseModel):
    items: List[KnowledgeDocumentResponse]
    total: int
