from typing import List, Dict, Any, Optional, Union
from sqlalchemy.orm import Session, selectinload
from sqlalchemy import and_, select, func, update
from uuid import UUID

from models.warehouse import Warehouse, WarehouseSetting, ProjectUserWarehouse
from schemas.warehouse import WarehouseCreate, WarehouseUpdate, WarehouseSettingCreate, WarehouseSettingUpdate
from models.project_user import ProjectUser
from models.user import User

class WarehouseService:
    @staticmethod
    async def get_warehouse_by_id(db, warehouse_id: Union[UUID, str]) -> Optional[Warehouse]:
        """根据ID获取仓库"""
        result = await db.execute(select(Warehouse).where(Warehouse.id == warehouse_id))
        return result.scalars().first()

    @staticmethod
    async def get_warehouse_by_code(db, project_id: Union[UUID, str], code: str) -> Optional[Warehouse]:
        """根据编码获取仓库"""
        result = await db.execute(select(Warehouse).where(and_(Warehouse.project_id == project_id, Warehouse.code == code)))
        return result.scalars().first()

    @staticmethod
    async def get_warehouses(
        db,
        project_id: Union[UUID, str],
        skip: int = 0,
        limit: int = 100,
        warehouse_type: Optional[str] = None,
        status: Optional[str] = None
    ) -> List[Warehouse]:
        """获取仓库列表"""
        stmt = select(Warehouse).where(Warehouse.project_id == project_id)
        if warehouse_type:
            stmt = stmt.where(Warehouse.type == warehouse_type)
        if status:
            stmt = stmt.where(Warehouse.status == status)
        stmt = stmt.offset(skip).limit(limit)
        result = await db.execute(stmt)
        return result.scalars().all()

    @staticmethod
    async def count_warehouses(
        db,
        project_id: Union[UUID, str],
        warehouse_type: Optional[str] = None,
        status: Optional[str] = None
    ) -> int:
        """统计仓库数量"""
        stmt = select(func.count()).select_from(Warehouse).where(Warehouse.project_id == project_id)
        if warehouse_type:
            stmt = stmt.where(Warehouse.type == warehouse_type)
        if status:
            stmt = stmt.where(Warehouse.status == status)
        result = await db.execute(stmt)
        return result.scalar_one()

    @staticmethod
    async def create_warehouse(db, warehouse: WarehouseCreate) -> Warehouse:
        """创建仓库"""
        db_warehouse = Warehouse(
            project_id=warehouse.project_id,
            name=warehouse.name,
            code=warehouse.code,
            type=warehouse.type,
            address=warehouse.address,
            phone=warehouse.phone,
            manager=warehouse.manager,
            capacity=warehouse.capacity,
            capacity_unit=warehouse.capacity_unit,
            business_hours=warehouse.business_hours,
            status=warehouse.status,
            settings=warehouse.settings
        )
        db.add(db_warehouse)
        await db.commit()
        await db.refresh(db_warehouse)
        return db_warehouse

    @staticmethod
    async def update_warehouse(db, warehouse_id: Union[UUID, str], warehouse_update: WarehouseUpdate) -> Optional[Warehouse]:
        """更新仓库"""
        db_warehouse = await WarehouseService.get_warehouse_by_id(db, warehouse_id)
        if db_warehouse:
            update_data = warehouse_update.dict(exclude_unset=True)
            for key, value in update_data.items():
                setattr(db_warehouse, key, value)
            await db.commit()
            await db.refresh(db_warehouse)
        return db_warehouse

    @staticmethod
    async def delete_warehouse(db, warehouse_id: Union[UUID, str]) -> bool:
        """删除仓库"""
        db_warehouse = await WarehouseService.get_warehouse_by_id(db, warehouse_id)
        if db_warehouse:
            await db.delete(db_warehouse)
            await db.commit()
            return True
        return False


class WarehouseSettingService:
    @staticmethod
    async def get_setting_by_project_id(db, project_id: Union[UUID, str]) -> Optional[WarehouseSetting]:
        """根据项目ID获取仓库设置"""
        result = await db.execute(select(WarehouseSetting).where(WarehouseSetting.project_id == project_id))
        return result.scalars().first()

    @staticmethod
    async def create_setting(db, setting: WarehouseSettingCreate) -> WarehouseSetting:
        """创建仓库设置"""
        db_setting = WarehouseSetting(
            project_id=setting.project_id,
            warehouse_mode=setting.warehouse_mode,
            default_business_hours_start=setting.default_business_hours_start,
            default_business_hours_end=setting.default_business_hours_end,
            enable_front_warehouse=setting.enable_front_warehouse,
            enable_cross_warehouse_transfer=setting.enable_cross_warehouse_transfer,
            inventory_warning_threshold=setting.inventory_warning_threshold,
            auto_replenishment=setting.auto_replenishment,
            default_capacity_unit=setting.default_capacity_unit
        )
        db.add(db_setting)
        await db.commit()
        await db.refresh(db_setting)
        return db_setting

    @staticmethod
    async def update_setting(db, project_id: Union[UUID, str], setting_update: WarehouseSettingUpdate) -> Optional[WarehouseSetting]:
        """更新仓库设置"""
        db_setting = await WarehouseSettingService.get_setting_by_project_id(db, project_id)
        if db_setting:
            update_data = setting_update.dict(exclude_unset=True)
            for key, value in update_data.items():
                setattr(db_setting, key, value)
            await db.commit()
            await db.refresh(db_setting)
            return db_setting
        else:
            # 如果设置不存在，则创建新设置
            setting_create = WarehouseSettingCreate(
                project_id=project_id,
                **setting_update.dict()
            )
            return await WarehouseSettingService.create_setting(db, setting_create)

    @staticmethod
    async def get_or_create_setting(db, project_id: Union[UUID, str]) -> WarehouseSetting:
        """获取或创建仓库设置"""
        db_setting = await WarehouseSettingService.get_setting_by_project_id(db, project_id)
        if db_setting:
            return db_setting
        else:
            # 创建默认设置
            setting_create = WarehouseSettingCreate(
                project_id=project_id,
                warehouse_mode="multi",
                default_business_hours_start="09:00",
                default_business_hours_end="18:00",
                enable_front_warehouse=True,
                enable_cross_warehouse_transfer=True,
                inventory_warning_threshold=10,
                auto_replenishment=True,
                default_capacity_unit="sqm"
            )
            return await WarehouseSettingService.create_setting(db, setting_create)


class ProjectUserWarehouseService:
    @staticmethod
    async def set_manager(db, warehouse_id, project_user_id):
        """设置仓库管理员"""
        # 先将该仓库下所有负责人标记为 False
        await db.execute(
            update(ProjectUserWarehouse)
            .where(ProjectUserWarehouse.warehouse_id == warehouse_id)
            .values(is_manager=False)
        )
        
        # 检查是否已存在关联记录
        result = await db.execute(
            select(ProjectUserWarehouse).where(
                ProjectUserWarehouse.warehouse_id == warehouse_id,
                ProjectUserWarehouse.project_user_id == project_user_id
            )
        )
        existing_association = result.scalars().first()
        
        if existing_association:
            # 如果已存在，则更新为管理员
            await db.execute(
                update(ProjectUserWarehouse)
                .where(
                    ProjectUserWarehouse.warehouse_id == warehouse_id,
                    ProjectUserWarehouse.project_user_id == project_user_id
                )
                .values(is_manager=True)
            )
        else:
            # 如果不存在，则创建新的关联记录
            new_association = ProjectUserWarehouse(
                warehouse_id=warehouse_id,
                project_user_id=project_user_id,
                is_manager=True
            )
            db.add(new_association)
        
        await db.commit()

    @staticmethod
    async def get_manager_user(db, warehouse_id):
        """获取仓库管理员用户"""
        result = await db.execute(
            select(ProjectUserWarehouse)
            .options(selectinload(ProjectUserWarehouse.project_user).selectinload(ProjectUser.user))
            .where(ProjectUserWarehouse.warehouse_id == warehouse_id, ProjectUserWarehouse.is_manager == True)
        )
        puw = result.scalars().first()
        if puw and puw.project_user and puw.project_user.user:
            return puw.project_user.user
        return None

    @staticmethod
    async def get_manager_project_user_id(db, warehouse_id) -> Optional[UUID]:
        """获取仓库管理员的project_user_id"""
        result = await db.execute(
            select(ProjectUserWarehouse.project_user_id)
            .where(ProjectUserWarehouse.warehouse_id == warehouse_id, ProjectUserWarehouse.is_manager == True)
        )
        return result.scalars().first()
