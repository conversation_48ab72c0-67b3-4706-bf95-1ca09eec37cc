#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
项目级 AI 工具 API
"""

import logging
import uuid
from typing import Optional, List
from fastapi import APIRouter, Depends, HTTPException, Query, Path, status
from sqlalchemy.ext.asyncio import AsyncSession

from db.database import get_db
from models.project import Project
from models.user import User
from core.auth import get_current_user
from api.deps import get_current_project
from services.ai import AIAssistantService, AIToolService
from schemas.ai import (
    AIAssistantToolCreate,
    AIAssistantToolUpdate,
    AIAssistantToolResponse,
    AIAssistantToolListResponse,
)

logger = logging.getLogger(__name__)
router = APIRouter()

@router.get("/ai/tools", response_model=AIAssistantToolListResponse)
async def list_tools(
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db),
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    type: Optional[str] = Query(None),
    status: Optional[str] = Query(None),
):
    """
    获取 AI 工具列表
    """
    try:
        tools, total = await AIAssistantService.get_tools(
            db=db,
            skip=skip,
            limit=limit,
            project_id=project.id,
            type=type,
            status=status,
        )
        
        return {
            "success": True,
            "message": "获取 AI 工具列表成功",
            "data": tools,
            "total": total,
            "page": skip // limit + 1,
            "page_size": limit,
        }
    except Exception as e:
        logger.error(f"获取 AI 工具列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取 AI 工具列表失败: {str(e)}",
        )

@router.get("/ai/tools/{tool_id}", response_model=AIAssistantToolResponse)
async def get_tool(
    tool_id: uuid.UUID = Path(...),
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db),
):
    """
    获取 AI 工具详情
    """
    try:
        tool = await AIAssistantService.get_tool(db=db, tool_id=tool_id)
        
        if not tool:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"AI 工具 ID '{tool_id}' 不存在",
            )
            
        # 检查是否有权限访问该工具
        if tool.project_id and tool.project_id != project.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问该 AI 工具",
            )
            
        return {
            "success": True,
            "message": "获取 AI 工具详情成功",
            "data": tool,
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取 AI 工具详情失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"获取 AI 工具详情失败: {str(e)}",
        )

@router.post("/ai/tools", response_model=AIAssistantToolResponse)
async def create_tool(
    tool_data: AIAssistantToolCreate,
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    创建 AI 工具
    """
    try:
        # 检查权限
        if not current_user.is_system_admin and not current_user.is_tenant_admin:
            # 检查是否为项目管理员
            if not any(pu.is_admin for pu in project.project_users if pu.user_id == current_user.id):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="只有项目管理员才能创建 AI 工具",
                )
                
        # 设置项目 ID
        tool_data.project_id = project.id
        tool_data.tenant_id = project.tenant_id
        
        # 创建工具
        tool = await AIAssistantService.create_tool(
            db=db,
            tool_data=tool_data,
            user_id=current_user.id,
        )
        
        return {
            "success": True,
            "message": "创建 AI 工具成功",
            "data": tool,
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建 AI 工具失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建 AI 工具失败: {str(e)}",
        )

@router.put("/ai/tools/{tool_id}", response_model=AIAssistantToolResponse)
async def update_tool(
    tool_id: uuid.UUID,
    tool_data: AIAssistantToolUpdate,
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    更新 AI 工具
    """
    try:
        # 获取工具
        tool = await AIAssistantService.get_tool(db=db, tool_id=tool_id)
        
        if not tool:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"AI 工具 ID '{tool_id}' 不存在",
            )
            
        # 检查是否有权限访问该工具
        if tool.project_id and tool.project_id != project.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问该 AI 工具",
            )
            
        # 检查权限
        if not current_user.is_system_admin and not current_user.is_tenant_admin:
            # 检查是否为项目管理员或工具创建者
            is_project_admin = any(pu.is_admin for pu in project.project_users if pu.user_id == current_user.id)
            is_creator = tool.created_by == current_user.id
            
            if not is_project_admin and not is_creator:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="只有项目管理员或工具创建者才能更新 AI 工具",
                )
                
        # 更新工具
        updated_tool = await AIAssistantService.update_tool(
            db=db,
            tool_id=tool_id,
            tool_data=tool_data,
        )
        
        return {
            "success": True,
            "message": "更新 AI 工具成功",
            "data": updated_tool,
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新 AI 工具失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"更新 AI 工具失败: {str(e)}",
        )

@router.delete("/ai/tools/{tool_id}", response_model=AIAssistantToolResponse)
async def delete_tool(
    tool_id: uuid.UUID,
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    删除 AI 工具
    """
    try:
        # 获取工具
        tool = await AIAssistantService.get_tool(db=db, tool_id=tool_id)
        
        if not tool:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"AI 工具 ID '{tool_id}' 不存在",
            )
            
        # 检查是否有权限访问该工具
        if tool.project_id and tool.project_id != project.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="无权访问该 AI 工具",
            )
            
        # 检查权限
        if not current_user.is_system_admin and not current_user.is_tenant_admin:
            # 检查是否为项目管理员或工具创建者
            is_project_admin = any(pu.is_admin for pu in project.project_users if pu.user_id == current_user.id)
            is_creator = tool.created_by == current_user.id
            
            if not is_project_admin and not is_creator:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="只有项目管理员或工具创建者才能删除 AI 工具",
                )
                
        # 删除工具
        success = await AIAssistantService.delete_tool(db=db, tool_id=tool_id)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="删除 AI 工具失败",
            )
            
        return {
            "success": True,
            "message": "删除 AI 工具成功",
            "data": tool,
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除 AI 工具失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"删除 AI 工具失败: {str(e)}",
        )

@router.post("/ai/tools/builtin", response_model=AIAssistantToolListResponse)
async def create_builtin_tools(
    project: Project = Depends(get_current_project),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    创建内置工具
    """
    try:
        # 检查权限
        if not current_user.is_system_admin and not current_user.is_tenant_admin:
            # 检查是否为项目管理员
            if not any(pu.is_admin for pu in project.project_users if pu.user_id == current_user.id):
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="只有项目管理员才能创建内置工具",
                )
                
        # 创建内置工具
        tools = await AIToolService.create_builtin_tools(
            db=db,
            tenant_id=project.tenant_id,
            project_id=project.id,
            user_id=current_user.id,
        )
        
        return {
            "success": True,
            "message": "创建内置工具成功",
            "data": tools,
            "total": len(tools),
            "page": 1,
            "page_size": len(tools),
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"创建内置工具失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"创建内置工具失败: {str(e)}",
        )
